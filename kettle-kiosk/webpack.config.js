var prod = process.env.NODE_ENV == "production";
var dev = process.env.NODE_ENV == "dev";
var local = process.env.NODE_ENV == "local";
var prodBasePath = "/home/<USER>/kiosk";
var devBasePath = "/home/<USER>/kiosk";
var localTemplate = 'ejs!src/index.local.ejs';
var devTemplate = 'ejs!src/index.dev.ejs';
var prodTemplate = 'ejs!src/index.prod.ejs';
var localBasePath = "C:/projects/chaayos-kettle/kettle-kiosk/";
var webpack = require('webpack');
var path = require('path');
var HtmlWebpackPlugin = require('html-webpack-plugin');
var CleanWebpackPlugin = require('clean-webpack-plugin');
var WebpackMd5Hash = require('webpack-md5-hash');
var polyfill = require("babel-polyfill");

var commonPlugins = [
    new WebpackMd5Hash(),
    new CleanWebpackPlugin(['dist'], {
        root: prod?prodBasePath:(dev?devBasePath:localBasePath),
        verbose: true,
        dry: false,
        exclude: []
    }),
    new webpack.optimize.CommonsChunkPlugin({
        name: ["vendor"],
        minChunks:"2"
    }),
    new HtmlWebpackPlugin({
        template: prod ? prodTemplate: (dev ? devTemplate: localTemplate),
        filename: 'index.html',
        excludeChunks: ['vendors']
    })
    /*new webpack.ProvidePlugin({
        'Promise': 'es6-promise', // Thanks Aaron (https://gist.github.com/Couto/b29676dd1ab8714a818f#gistcomment-1584602)
        'fetch': 'imports?this=>global!exports?global.fetch!whatwg-fetch'
    })*/
];

var prodExplicitPlugins = [
    new webpack.DefinePlugin({
        'process.env': {
            NODE_ENV: JSON.stringify('production')
        }
    }),
    new webpack.optimize.UglifyJsPlugin({compress:true,warning:false, sourcemap:false}),
    new webpack.optimize.DedupePlugin(),
    new webpack.optimize.OccurenceOrderPlugin(),
    new webpack.optimize.AggressiveMergingPlugin(),
];

var prodVendors = ['babel-polyfill', "jquery", "react", 'react-router', 'react-redux', 'react-slick', 'react-select', 'redux',
    'lodash', 'redux-thunk', 'redux-promise-middleware'];
var devVendors = ['babel-polyfill', "jquery", "react", 'react-router', 'react-redux', 'react-slick', 'react-select', 'redux',
    'lodash', 'redux-logger', 'redux-thunk', 'redux-promise-middleware'];
var localVendors = ['babel-polyfill', "jquery", "react", 'react-router', 'react-redux', 'react-slick', 'react-select', 'redux',
    'lodash', 'redux-logger', 'redux-thunk', 'redux-promise-middleware'];

var prodPlugins = commonPlugins.concat(prodExplicitPlugins);
var devPlugins = commonPlugins.concat(prodExplicitPlugins);

module.exports = {
    context: path.join(__dirname, "src"),
    devtool: !prod ? "inline-sourcemap" : null,
    entry: {
        vendor: prod? prodVendors:(dev?devVendors: localVendors),
        home: ["./js/home.js"],
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                enforce: 'pre',
                loader: 'eslint-loader',
                options: {
                    emitWarning: true
                },
            },
        ],
        noParse: ["jquery"].map(function(name) {
            return path.join(__dirname, "node_modules", name);
        }),
        loaders: [
            {
                test: /\.js?$/,
                exclude: /(node_modules|bower_components)/,
                loader: 'babel-loader',
                query: {
                    presets: ['react', 'es2015', 'stage-0'],
                    plugins: ['react-html-attrs', 'transform-class-properties', 'transform-decorators-legacy']
                }
            },
            { test: /\.hbs$/, loader: 'handlebars-loader' },
            {
                test: /\.json$/,
                loader: 'json-loader'
            }
        ]
    },
    output: {
        path: path.join(__dirname, "dist"),
        filename: prod?"[name].[chunkhash].js":(dev?"[name].[chunkhash].js":"[name].min.js"),
        crossOriginLoading: "anonymous"
    },
    plugins: prod ? prodPlugins: (dev? devPlugins:commonPlugins),
};