import appUtil from "../AppUtil";

class PaymentService {

    constructor() {
        this.orderType = ["DELIVERY","TAKE_AWAY","DINE_IN"];
        this.billType = ["NET_PRICE","MRP","ZERO_TAX"];
        this.taxTypes = {
            DELIVERY: ["netPriceVat","mrpVat","surchargeOnTax"],
            TAKE_AWAY: ["netPriceVat","mrpVat","surchargeOnTax"],
            DINE_IN: ["serviceTax","netPriceVat","mrpVat","surchargeOnTax","sbCess","kkCess"],
        };
        this.taxAmountType = {
            serviceTax:"TOTAL",
            netPriceVat : "NET_PRICE",
            mrpVat : "MRP",
            sbCess : "TOTAL",
            kkCess : "TOTAL"
        }
        this.billLevelValues = {};
        this.itemLevelValues = {};
        this.totalTax = 0;
        this.initBillLevelValues = this.initBillLevelValues.bind(this);
        this.initItemLevelValues = this.initItemLevelValues.bind(this);
        this.calculateBillLevelValues = this.calculateBillLevelValues.bind(this);
        this.calculateItemLevelValues = this.calculateItemLevelValues.bind(this);
        this.calculateTaxes = this.calculateTaxes.bind(this);
        this.calculateSavings = this.calculateSavings.bind(this);
    }

    initCalculations(orderType, orderDetail){
        this.initBillLevelValues();
        this.initItemLevelValues();
        this.calculateBillLevelValues(orderDetail.transactionDetail.discountDetail);
        this.calculateItemLevelValues(orderDetail);
        this.calculateTaxes(orderType, orderDetail.transactionDetail);
        this.calculateSavings(orderType, orderDetail.transactionDetail);
        return orderDetail;
    }

    initBillLevelValues(){
        this.billLevelValues.discount = 0;
        this.billLevelValues.promotionalOffer = 0;
    }

    initItemLevelValues(){
        this.billType.map((type)=>{
            this.itemLevelValues[type] = {};
            this.itemLevelValues[type].amount = 0;
            this.itemLevelValues[type].discount = 0;
            this.itemLevelValues[type].promotionalOffer = 0;
        });
    }

    calculateBillLevelValues(discountDetail){
        //cart.orderDetail.transactionDetail.discountDetail
        if(discountDetail!=null){
            if(discountDetail.discount.value!=null && discountDetail.discount.value>0){
                this.billLevelValues.discount = parseFloat(discountDetail.discount.value);
            }
            if(discountDetail.promotionalOffer!=null && discountDetail.promotionalOffer>0){
                this.billLevelValues.promotionalOffer = parseFloat(discountDetail.promotionalOffer);
            }
        }
    }

    calculateItemLevelValues(orderDetail){
        orderDetail.orders.map((item) => {
            item.amount = item.price * item.quantity;
            this.itemLevelValues[item.billType].amount += item.amount;
            if(!appUtil.checkEmpty(item.discountDetail) && !appUtil.checkEmpty(item.discountDetail.discount)){
                if(item.discountDetail.discount.percentage!=null){
                    item.discountDetail.discount.value = parseFloat((item.discountDetail.discount.percentage / 100) * item.amount);
                    this.itemLevelValues[item.billType].discount += item.discountDetail.discount.value;
                }
                if(item.discountDetail.promotionalOffer!=null){
                    this.itemLevelValues[item.billType].promotionalOffer +=  parseFloat(item.discountDetail.promotionalOffer);
                }
            }
        });
    }

    calculateTaxes(orderType, transactionDetail){
        var totalAmountForTax = this.getTotalAmountForTaxByDiscount(true, transactionDetail);
        this.taxTypes[orderType].map((taxType)=>{
            if(transactionDetail[taxType].percentage!=null && transactionDetail[taxType].percentage>0){
                if(taxType!="surchargeOnTax"){
                    if(this.taxAmountType[taxType]=="TOTAL"){
                        //calculate tax on total amount
                        transactionDetail[taxType].value = parseFloat(totalAmountForTax * parseFloat(transactionDetail[taxType].percentage/100)).toFixed(4);
                    }else{
                        //calculate tax on specific type amount e.g netPriceAmount, mrpAmount
                        transactionDetail[taxType].value = parseFloat(this.getBillTypeAmountByDiscount(taxType, true, transactionDetail)
                            * parseFloat(transactionDetail[taxType].percentage/100)).toFixed(4);
                    }
                }
            }else{
                transactionDetail[taxType].value = 0;
            }
        });
        transactionDetail.surchargeOnTax.value = parseFloat((parseFloat(transactionDetail.netPriceVat.value)+parseFloat(transactionDetail.mrpVat.value)) *
            parseFloat(transactionDetail.surchargeOnTax.percentage/100)).toFixed(4);
        this.setTotalTax(orderType, transactionDetail);
        this.fillOtherTransactionDetails(transactionDetail);
    }

    calculateSavings(orderType, transactionDetail){
        var taxObj = {};
        this.taxTypes[orderType].map((taxType)=>{
            taxObj[taxType] = {};
            taxObj[taxType].value = 0;
        });
        var totalAmountForTax = this.getTotalAmountForTaxByDiscount(false);
        this.taxTypes[orderType].map((taxType)=>{
            if(transactionDetail[taxType].percentage!=null && transactionDetail[taxType].percentage>0){
                if(taxType!="surchargeOnTax"){
                    if(this.taxAmountType[taxType]=="TOTAL"){
                        //calculate tax on total amount
                        taxObj[taxType].value = parseFloat(totalAmountForTax * parseFloat(transactionDetail[taxType].percentage/100));
                    }else{
                        //calculate tax on specific type amount e.g netPriceAmount, mrpAmount
                        taxObj[taxType].value = parseFloat(this.getBillTypeAmountByDiscount(taxType, false)
                            * parseFloat(transactionDetail[taxType].percentage/100));
                    }
                }
            }else{
                taxObj[taxType].value = 0;
            }
        });
        taxObj.surchargeOnTax.value = parseFloat((taxObj.netPriceVat.value+taxObj.mrpVat.value) *
            parseFloat(transactionDetail.surchargeOnTax.percentage/100));
        var totalTax = 0;
        this.taxTypes[orderType].map((taxType)=>{
            totalTax += taxObj[taxType].value;
        });
        var saving = Math.round(parseFloat(totalAmountForTax + totalTax) - transactionDetail.paidAmount);
        transactionDetail.savings = saving>0?saving:0;
    }

    getTotalAmountForTaxByDiscount(removeDiscount, transactionDetail){
        var amt = 0;
        this.billType.map((billType)=>{
            if(billType!="ZERO_TAX"){
                amt += this.itemLevelValues[billType].amount;
                if(removeDiscount){
                    amt -= (this.itemLevelValues[billType].discount + this.itemLevelValues[billType].promotionalOffer);
                }
            }
        });
        //deduct bill level discount an promo
        if(removeDiscount){
            amt -= (transactionDetail.discountDetail.discount.value + transactionDetail.discountDetail.promotionalOffer);
        }
        return amt;
    }

    getBillTypeAmountByDiscount(taxType, removeDiscount, transactionDetail){
        var ret = this.itemLevelValues[this.taxAmountType[taxType]].amount;
        if(removeDiscount){
            ret = ret - (this.itemLevelValues[this.taxAmountType[taxType]].discount + this.itemLevelValues[this.taxAmountType[taxType]].promotionalOffer);
            if(transactionDetail.discountDetail.discount.percentage!=null && transactionDetail.discountDetail.discount.percentage>0){
                ret -= parseFloat(ret * parseFloat(transactionDetail.discountDetail.discount.percentage/100));
            }
            if(transactionDetail.discountDetail.promotionalOffer!=null && transactionDetail.discountDetail.promotionalOffer>0){
                ret -= parseFloat(transactionDetail.discountDetail.promotionalOffer);
            }
        }
        return ret;
    }

    setTotalTax(orderType, transactionDetail){
        var total = 0;
        this.taxTypes[orderType].map((taxType)=>{
           total += parseFloat(transactionDetail[taxType].value);
        });
        this.totalTax = total;
    }

    fillOtherTransactionDetails(transactionDetail){
        transactionDetail.totalAmount = parseFloat(this.getTotalAmountByDiscount(false, transactionDetail)).toFixed(4);
        transactionDetail.taxableAmount = parseFloat(this.getTotalAmountByDiscount(true, transactionDetail));
        var paidAmount = parseFloat(transactionDetail.taxableAmount + this.totalTax);
        transactionDetail.paidAmount = Math.round(paidAmount);
        transactionDetail.roundOffValue = parseFloat(transactionDetail.paidAmount - paidAmount).toFixed(4);
        transactionDetail.discountDetail.totalDiscount = parseFloat(this.getTotalDiscount(transactionDetail)).toFixed(4);
    }

    getTotalAmountByDiscount(removeDiscount, transactionDetail){
        var total = 0;
        this.billType.map((billType)=>{
            total += this.itemLevelValues[billType].amount;
            if(removeDiscount){
                total = total - (this.itemLevelValues[billType].discount + this.itemLevelValues[billType].promotionalOffer);
            }
        });
        if(removeDiscount){
            total -= (transactionDetail.discountDetail.discount.value + transactionDetail.discountDetail.promotionalOffer);
        }
        return total;
    }

    getTotalDiscount(transactionDetail){
        var total = 0;
        this.billType.map((billType)=>{
            total += (this.itemLevelValues[billType].discount + this.itemLevelValues[billType].promotionalOffer);
        });
        total += (transactionDetail.discountDetail.discount.value + transactionDetail.discountDetail.promotionalOffer);
        return total;
    }
}

const paymentService = new PaymentService();
export default paymentService;