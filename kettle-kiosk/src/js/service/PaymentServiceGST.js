/**
 * Created by Chaayos on 22-06-2017.
 */
import appUtil from "../AppUtil";

class PaymentServiceGST {

    constructor() {
        this.orderType = ["DELIVERY","TAKE_AWAY","DINE_IN"];
        this.gstTaxTypes = ['igst','cgst','sgst'];
        this.gstTaxCodeMap = {igst:"IGST",cgst:"CGST",sgst:"SGST/UTGST"};
        this.taxApplicabilityMap = [];
        this.calculateTaxes = this.calculateTaxes.bind(this);
        this.calculateSavings = this.calculateSavings.bind(this);
    }

    initCalculations(orderType, orderDetail, taxes, isInterState){
        if(orderDetail.orders.length>0){
            this.initTaxes(orderDetail, taxes, isInterState);
            this.calculateTaxes(orderType, orderDetail, isInterState);
            //this.calculateSavings(orderType, orderDetail.transactionDetail);
        }
        return orderDetail;
    }

    initTaxes(orderDetail, taxes, isInterState){
        orderDetail.orders.map(orderItem => {
            if(orderItem.billType!="ZERO_TAX"){
                taxes.map( tax => {
                    if(tax.taxCode==orderItem.code){
                        orderItem.taxes = [];
                        if(isInterState){
                            orderItem.taxes.push({type: "GST",code: this.gstTaxCodeMap['igst'], percentage: tax.state['igst'],
                                value: 0, total: 0, taxable: 0
                            });
                        }else{
                            this.gstTaxTypes.map(type =>{
                                if(type!="igst" && tax.state[type]>0){ //TODO fix IGST scenario
                                    orderItem.taxes.push({
                                        type: "GST", code: this.gstTaxCodeMap[type], percentage: tax.state[type],
                                        value: 0, total: 0, taxable: 0
                                    });
                                }
                            });
                        }
                        this.taxApplicabilityMap = {};
                        tax.others.map(otherTax => {
                            this.taxApplicabilityMap[otherTax.type] = otherTax.applicabilty;
                            orderItem.taxes.push({
                                type: otherTax.type, code: otherTax.type, percentage: otherTax.tax,
                                value: 0, total: 0, taxable: 0
                            });
                        });
                    }
                });
            }
        });
    }

    calculateTaxes(orderType, orderDetail){
        orderDetail.orders.map(orderItem => {
            var taxable = this.calculateProductAmount(orderItem, true);
            orderItem.amount = taxable;
            var amount = parseFloat(parseFloat(orderItem.price) * parseFloat(orderItem.quantity));
            orderItem.totalAmount = amount;
            var tax = 0;
            var originalTax = 0;
            orderItem.taxes.map(taxItem => {
                taxItem.total = amount;
                taxItem.taxable = taxable;
                if(taxItem.type=="GST"){
                    taxItem.value = parseFloat(parseFloat(taxItem.percentage/100) * parseFloat(taxItem.taxable)).toFixed(4);
                    tax = parseFloat(parseFloat(tax) + parseFloat(taxItem.value)).toFixed(4);
                    originalTax = parseFloat(parseFloat(originalTax) + parseFloat((taxItem.percentage/100) * taxItem.total)).toFixed(4);
                }
            });
            orderItem.tax = tax;
            orderItem.originalTax = originalTax;
            var gstTaxValue = orderItem.tax;
            var originalGstTaxValue = orderItem.originalTax;
            orderItem.taxes.map(taxItem => {
                var originalTaxValue = 0;
                if(taxItem.type!="GST"){
                    if(this.taxApplicabilityMap[taxItem.code]!=null && this.taxApplicabilityMap[taxItem.code]=="ON_TAX"){
                        taxItem.value = parseFloat((taxItem.percentage/100) * parseFloat(gstTaxValue)).toFixed(4);
                        originalTaxValue = parseFloat((taxItem.percentage/100) * parseFloat(originalGstTaxValue)).toFixed(4);
                    }else{
                        taxItem.value = parseFloat((taxItem.percentage/100) * parseFloat(taxItem.taxable)).toFixed(4);
                        originalTaxValue = parseFloat((taxItem.percentage/100) * parseFloat(taxItem.total)).toFixed(4);
                    }
                    orderItem.tax = parseFloat(parseFloat(orderItem.tax) + parseFloat(taxItem.value)).toFixed(4);
                    orderItem.originalTax = parseFloat(parseFloat(orderItem.originalTax) + parseFloat(originalTaxValue)).toFixed(4);
                }
            });
        });
        this.fillTransactionDetails(orderDetail);
        this.calculateSavings(orderType, orderDetail);
    }

    calculateProductAmount(orderItem, withDiscount){
        var amount = (orderItem.price * orderItem.quantity);
        if(withDiscount){
            orderItem.discountDetail.totalDiscount = (orderItem.discountDetail.promotionalOffer==null?0:orderItem.discountDetail.promotionalOffer +
                orderItem.discountDetail.discount.value);
            amount = amount - orderItem.discountDetail.totalDiscount;
        }
        return amount;
    }

    fillTransactionDetails(orderDetail){
        var discountDetail = orderDetail.transactionDetail.discountDetail;
        orderDetail.transactionDetail = appUtil.getNewTransactionObject();
        orderDetail.transactionDetail.discountDetail = discountDetail;
        orderDetail.transactionDetail.discountDetail.totalDiscount = 0;
        orderDetail.orders.map(orderItem => {
            orderDetail.transactionDetail.totalAmount = parseFloat(parseFloat(orderDetail.transactionDetail.totalAmount) + parseFloat(orderItem.totalAmount)).toFixed(4);
            orderDetail.transactionDetail.taxableAmount = parseFloat(parseFloat(orderDetail.transactionDetail.taxableAmount) + parseFloat(orderItem.amount)).toFixed(4);
            orderDetail.transactionDetail.tax = parseFloat(parseFloat(orderDetail.transactionDetail.tax) + parseFloat(orderItem.tax)).toFixed(4);
            orderDetail.transactionDetail.discountDetail.totalDiscount = parseFloat(parseFloat(orderDetail.transactionDetail.discountDetail.totalDiscount) +
                parseFloat(orderItem.discountDetail.totalDiscount)).toFixed(4);
            orderItem.taxes.map(tax => {
                if(this.transactionDetailHasType(orderDetail.transactionDetail.taxes, tax)){
                    orderDetail.transactionDetail.taxes.map(t => {
                        if(t.code == tax.code && t.percentage == tax.percentage){
                            t.value = parseFloat(parseFloat(t.value)+parseFloat(tax.value)).toFixed(4);
                            t.total = parseFloat(t.total) + parseFloat(tax.total);
                            t.taxable = parseFloat(t.taxable) + parseFloat(tax.taxable);
                        }
                    });
                }else{
                    orderDetail.transactionDetail.taxes.push({type:tax.type,code:tax.code,percentage:tax.percentage,value:tax.value,total:tax.total,taxable:tax.taxable});
                }
            });
        });
        var paidAmount = parseFloat(parseFloat(orderDetail.transactionDetail.taxableAmount) + parseFloat(orderDetail.transactionDetail.tax)).toFixed(4);
        orderDetail.transactionDetail.paidAmount = Math.round(paidAmount);
        orderDetail.transactionDetail.roundOffValue = parseFloat(orderDetail.transactionDetail.paidAmount - paidAmount).toFixed(4);
    }

    transactionDetailHasType(taxes, tax){
        var ret = false;
        taxes.map(t => {
            if(!ret){
                ret = (t.code == tax.code && t.percentage == tax.percentage);
            }
        });
        return ret;
    }

    calculateSavings(orderType, orderDetail){
        var nonDiscountedPayableAmount = 0;
        var c = this;
        orderDetail.orders.map(orderItem => {
            nonDiscountedPayableAmount = parseFloat(parseFloat(nonDiscountedPayableAmount) + parseFloat(orderItem.totalAmount) + parseFloat(orderItem.originalTax)).toFixed(4);
        });
        var saving = Math.round(parseFloat(nonDiscountedPayableAmount) - parseFloat(orderDetail.transactionDetail.paidAmount));
        orderDetail.transactionDetail.savings = saving>0?saving:0;
    }
}

const paymentServiceGST = new PaymentServiceGST();
export default paymentServiceGST;