/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileTermsLayout from "./mobile/MobileTermsLayout";
import DesktopTermsLayout from "./desktop/DesktopTermsLayout";

export default class TermsLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileTermsLayout props={this.props} />
            )
        } else {
            return (
                <DesktopTermsLayout props={this.props} />
            )
        }
    }
}