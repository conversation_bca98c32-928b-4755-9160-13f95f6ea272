/**
 * Created by Chaayos on 10-12-2016.
 */
import React from "react";
import { connect } from "react-redux";
import browserHistory from "react-router/lib/browserHistory";
import appUtil from "../../AppUtil";
import MobileHeader from "./MobileHeader";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        orderDetail: store.orderManagementReducer.orderDetail,
        currentOrderId:store.orderManagementReducer.currentOrderId,
        status: store.orderManagementReducer.status,
        error: store.orderManagementReducer.error,
        sessionKey:store.customerReducer.sessionKey,
        orderStatusTimeouts:store.orderManagementReducer.orderStatusTimeouts
    };
})
export default class MobileOrderSummaryLayout extends React.Component {

    constructor(){
        super();
        this.state = {
        };
        this.reOrder = this.reOrder.bind(this);
        this.backToMenu = this.backToMenu.bind(this);
        this.refreshStatus = this.refreshStatus.bind(this);
    }

    backToMenu(){
        trackUtils.trackGoToMenuClicked("orderDetail");
        browserHistory.push("/menu");
    }

    reOrder(){
        this.props.dispatch(OrderManagementActions.reOrder("orderDetail",this.props.orderDetail.generateOrderId));
    }

    refreshStatus(){
        //this.props.dispatch(OrderManagementActions.getOrderStatus(this.props.orderDetail.generateOrderId, this.props.orderDetail.source, this.props.orderStatusTimeouts));
    }

    componentWillMount(){
        window.scrollTo(0, 0);
        if(appUtil.checkEmpty(this.props.sessionKey)){
            browserHistory.push("/login");
        }else{
            if(!appUtil.checkEmpty(this.props.props.location.query)){
                this.props.dispatch(OrderManagementActions.setCurrentOrderId({id: this.props.props.location.query.orderId, type: "GEN"}));
            }
            // this.props.dispatch(OrderManagementActions.getOrderDetail(this.props.orderDetail, this.props.currentOrderId, null, this.props.orderStatusTimeouts));
        }
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"orderDetail",device:"mobile",custom:true});
    }

    componentWillUnmount(){
        this.props.dispatch(OrderManagementActions.removeStatusTimeouts(this.props.orderStatusTimeouts));
    }

    render (){

        var orderItems = [];
        var orderLength = 0;
        var stats = [];
        if(this.props.orderDetail!=null && this.props.orderDetail.orders.length>0){
            this.props.orderDetail.orders.map((item) => {
                if(item.productId!=1044 && item.productId!=1043){
                    orderLength++;
                    var style = {backgroundImage:"url(../../../img/products/"+item.productId+".jpg)"};
                    var variants = "";
                    var products = "";
                    var addons = "";
                    var menuProducts = "";
                    if (item.composition.variants.length > 0) {
                        item.composition.variants.map((variant) => {
                            variants = variants + variant.alias + ", "
                        })
                        if (item.composition.products.length == 0) {
                            variants = variants.substr(0, variants.length - 2);
                        }
                    }
                    if (item.composition.products.length > 0) {
                        item.composition.products.map((product) => {
                            products = products + product.product.name + ", "
                        })
                        products = products.substr(0, products.length - 2);
                    }
                    if (item.composition.addons.length > 0) {
                        addons = "Add On : ";
                        item.composition.addons.map((addon) => {
                            addons = addons + addon.product.name + ", "
                        });
                        addons = addons.substr(0, addons.length - 2);
                    }
                    if (item.composition.menuProducts.length > 0) {
                        item.composition.menuProducts.map((product) => {
                            menuProducts = menuProducts + product.productName + ", "
                        })
                        menuProducts = menuProducts.substr(0, menuProducts.length - 2);
                    }

                    orderItems.push(
                        <div key={item.itemId} class="orderItemContainer">
                            <div class="pic" style={style}></div>
                            <div class="productTitle">{item.productName}</div>
                            <div class="productPrice"><img class="rupeeIcon" src="../../../img/rupee.png" />{item.totalAmount}</div>
                            <div class="clear"></div>
                            <div class="customizationDetail">
                                <div>{menuProducts}</div>
                                <div>
                                    {item.dimension != "None" ? (
                                        <span>{item.dimension}{(variants.length>0 || products.length>0 || addons.length>0)?",":null} </span>
                                    ) : null}
                                    {variants}
                                    {products}
                                </div>
                                <div>{addons}</div>
                            </div>
                            <div class="productQty">Qty {item.quantity}</div>
                        </div>
                    )
                }
            });
            var statuses = appUtil.getOrderStatusArray(this.props.orderDetail.status,this.props.orderDetail.source);
            statuses.map((status,index) => {
                stats.push(
                    <span key={status.status}>
                        <div class="orderStatus">
                            {status.active?(
                                <span class="active"><img src={"../../../img/orderStatus/active/"+status.image+".svg"} /><br />{status.status}</span>
                            ):(
                                <span><img src={"../../../img/orderStatus/"+status.image+".svg"} /><br />{status.status}</span>
                            )}
                        </div>
                        {index<(statuses.length-1)?(
                            <div class="separator"><img src="../../../img/rightChevron.png" /></div>
                        ):null}
                    </span>
                )
            });
        }

        return(
            <div>
                <div class="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                {(this.props.status==null || this.props.status=="PENDING")?(
                    <div class="mobilePageContainer">
                        <div class="ptop50">
                            <div class="loader load8"></div>
                        </div>
                        <div class="text-center">Fetching order summary.</div>
                    </div>
                ):(null)}
                {(this.props.status=="FULFILLED" && !appUtil.checkEmpty(this.props.orderDetail))?(
                    <div class="mobilePageContainer">
                        <div class="mobilePageHead">Current Order</div>
                        <div class="orderDetailSubHead">
                            Status
                            <span style={{color:"#5e7e47", textDecoration:"underline", textTransform:"capitalize"}} class="right" onClick={this.reOrder.bind(this)}>Reorder</span>
                        </div>
                        <div class="orderStatusContainer">
                            {stats}
                        </div>
                        <div class="orderDetailSubHead">
                            Summary
                            {!appUtil.isOrderCompleted(this.props.orderDetail.status, this.props.orderDetail.source)?(
                                <span class="right" style={{color:"#5e7e47", textTransform:"capitalize", textDecoration:"underline",cursor:"pointer"}}
                                      onClick={this.refreshStatus.bind(this)}>Refresh Status</span>
                            ):null}
                        </div>
                        <div class="orderItemContainer">
                            <div class="address" style={{display:"inline-block"}}><b>Order No.:</b> {this.props.orderDetail.generateOrderId}</div>
                            {this.props.orderDetail.tokenNumber!=null && this.props.orderDetail.tokenNumber>0 ?(
                                <div class="address" style={{float:"right"}}><b>Token No.:</b> {this.props.orderDetail.tokenNumber}</div>
                            ):null}
                        </div>
                        {orderItems}
                        {!appUtil.checkEmpty(this.props.orderDetail.orderRemark)?(
                            <div class="orderItemContainer">
                                <div class="remark">Instructions: {this.props.orderDetail.orderRemark}</div>
                            </div>
                        ):(null)}
                        <div class="orderItemContainer">
                            {this.props.orderDetail.source=="COD"?(
                                <div class="address"><b>Delivery:</b> {this.props.orderDetail.address.line1}, {this.props.orderDetail.address.locality},
                                    {this.props.orderDetail.address.city}</div>
                            ):(
                                <div class="address">
                                    <b>{this.props.orderDetail.source=="TAKE_AWAY"?"Take away":"Dine in"} :</b> {this.props.orderDetail.unitName}
                                </div>
                            )}
                        </div>
                        <div class="orderItemContainer" style={{marginTop:"20px"}}>
                            <span class="total">Total</span>
                            <div class="right">
                                <div class="qty">{orderLength>1?orderLength+" items":orderLength+" item"}</div>
                                <div class="totalPrice">Rs. {this.props.orderDetail.transactionDetail.paidAmount}</div>
                            </div>
                            <div class="clear"></div>
                        </div>
                        <div class="btn btn-primary" style={{width:'80%',margin:'20px auto'}} onClick={this.backToMenu.bind(this)}>Back to Menu</div>
                    </div>
                ):(null)}
                {(this.props.status=="REJECTED")?(
                    <div class="mobilePageContainer">
                        <div class="alert error">{this.props.error.errorMessage}</div>
                    </div>
                ):(null)}
            </div>
        )
    }
}