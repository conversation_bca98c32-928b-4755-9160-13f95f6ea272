import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux";
import Select from 'react-select';
import * as LocalityActions from "../../actions/LocalityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as UtilityActions from "../../actions/UtilityActions";

@connect((store) => {
    return {
        showLocationWrapper: store.localityReducer.showLocationWrapper,
        criteria: store.localityReducer.criteria,
        selectedCity: store.localityReducer.selectedCity,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedOutlet: store.localityReducer.selectedOutlet,
        localities: store.localityReducer.localities,
        outlets: store.localityReducer.outlets,
        showLocality:store.localityReducer.showLocality,
        showOutlet:store.localityReducer.showOutlet,
        showError:store.localityReducer.showError,
        showLoader:store.localityReducer.showLoader,
    };
})
export default class MobileLocationLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            localitiesList: [],
            outletsList:[],
        };
        this.init = this.init.bind(this);
        this.loadLocalities = this.loadLocalities.bind(this);
        this.loadOutlets = this.loadOutlets.bind(this);
        this.formatLocalities = this.formatLocalities.bind(this);
        this.formatOutlets = this.formatOutlets.bind(this);
        this.selectLocality = this.selectLocality.bind(this);
        this.selectOutlet = this.selectOutlet.bind(this);
        this.backToHome = this.backToHome.bind(this);
    }

    init() {
        if(this.props.selectedCity!=null){
            if(this.props.criteria=="DELIVERY") {
                this.loadLocalities(this.props.selectedCity.city);
            }else{
                this.loadOutlets(this.props.selectedCity.city);
            }
        }else{
            this.props.dispatch(UtilityActions.showPopup("Please select city!"));
            browserHistory.push("/");
        }
    }

    formatLocalities(){
        this.state.localitiesList = [];
        this.props.localities.map((item, index) =>
        {
            this.state.localitiesList.push({value:index, label:item});
        });
    }

    formatOutlets(){
        if(this.props.outlets.length>0){
            this.state.outletsList = [];
            this.props.outlets.map((item) =>
            {
                //do not show galleria market in dine in
                if(this.props.criteria=="DINE_IN" && item.id==10005){}else{
                    this.state.outletsList.push({value:item.id, label:item.name});
                }
            });
        }
    }

    loadLocalities(val){
        this.props.dispatch(LocalityActions.loadLocalitiesList(val));
    }

    loadOutlets(val){
        this.props.dispatch(LocalityActions.loadOutletsList(val));
    }

    selectLocality(val){
        this.props.dispatch(LocalityActions.selectLocality(val));
        this.props.dispatch(OutletMenuActions.getUnitProducts(this.props.criteria,this.props.selectedCity,val, null));
        this.props.dispatch(OutletMenuActions.getTags());
        browserHistory.push("/menu");
    }

    selectOutlet(val){
        if(val!=null){
            this.props.dispatch(LocalityActions.selectOutlet(val));
            this.props.dispatch(OutletMenuActions.getUnitProducts(this.props.criteria,this.props.selectedCity,null,val));
            this.props.dispatch(OutletMenuActions.getTags());
            browserHistory.push("/menu");
        }
    }

    backToHome(){
        browserHistory.push("/");
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.init();
    }

    componentDidMount(){
        if(document.getElementsByClassName("locationWrapper")!=null){
            document.getElementsByClassName("locationWrapper")[0].className = "locationWrapper active";
        }
    }

    render() {

        if(this.props.localities.length>0){
            this.formatLocalities();
        }
        if(this.props.outlets.length>0){
            this.formatOutlets();
        }

        return (
            <div class="locationWrapper">
                <div class="expandHead">
                    <div class="backArrow" onClick={this.backToHome.bind(this)}><img src="../../../img/backGrey.svg" /></div>
                    <div class="headLine">Select {this.props.showLocality?"Locality":this.props.showOutlet?"Outlet":""}</div>
                </div>
                {this.props.showLocality?(
                    <div class="localitySelector">
                        <Select name="localitiesList" value={this.props.selectedLocality} options={this.state.localitiesList}
                                autofocus={true} openOnFocus={true} backspaceRemoves={false} onChange={this.selectLocality.bind(this)} clearable={false} placeholder="Type location" autoBlur={true} />
                    </div>
                ):null}
                {this.props.showOutlet?(
                    <div class="outletSelector">
                        <Select name="outletsList" value={this.props.selectedOutlet} options={this.state.outletsList}
                                autofocus={true} openOnFocus={true} backspaceRemoves={false} onChange={this.selectOutlet.bind(this)} clearable={false} placeholder="Type outlet name" autoBlur={true} />
                    </div>
                ):null}
            </div>
        )
    }
}