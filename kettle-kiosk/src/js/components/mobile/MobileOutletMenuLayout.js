import React from "react";
import {connect} from "react-redux";
import {browserHistory} from "react-router";
import $ from "jquery";
import _ from "lodash";
import MobileHeader from "./MobileHeader";
import SidebarLayout from "../SidebarLayout";
import MobileCustomizationModal from "./MobileCustomizationModal";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as CustomizationModalActions from "../../actions/CustomizationModalActions";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as LocalityActions from "../../actions/LocalityActions";
import {showPopup} from "../../actions/UtilityActions";
import * as CampaignManagementActions from "../../actions/CampaignManagementActions";
import appUtil from "../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import productService from "../../service/ProductService";

@connect((store) => {
    return {
        criteria:store.localityReducer.criteria,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedCity: store.localityReducer.selectedCity,
        selectedOutlet: store.localityReducer.selectedOutlet,
        unit: store.outletMenuReducer.unit,
        skipDeliveryPackaging:store.outletMenuReducer.skipDeliveryPackaging,
        showError: store.outletMenuReducer.showError,
        fetchingUnitData: store.outletMenuReducer.fetchingUnitData,
        sidebarOpen: store.sidebarReducer.sidebarOpen,
        cart: store.cartManagementReducer.cart,
        specialMenu: store.outletMenuReducer.specialMenu,
        recipeLoadingProductId: store.outletMenuReducer.recipeLoadingProductId,
        productTags: store.outletMenuReducer.productTags,
        boughtByYou: store.outletMenuReducer.boughtByYou,
        initMenu: store.outletMenuReducer.initMenu,
        deviceKey:store.customerReducer.deviceKey,
        cityStateMap: store.localityReducer.cityStateMap,
        campaignInitialized:store.campaignReducer.campaignInitialized,
    };
})
export default class MobileOutletMenuLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            products: {},
            mount: true,
            sizeDescription:productService.getSizeDescriptions()
        };
        this.init = this.init.bind(this);
        this.postInit = this.postInit.bind(this);
        this.stickyRelocate = this.stickyRelocate.bind(this);
        this.navAction = this.navAction.bind(this);
        this.loadProductRecipes = this.loadProductRecipes.bind(this);
        this.updateProductQuantityDirectly = this.updateProductQuantityDirectly.bind(this);
        this.updateDimensionProductQty = this.updateDimensionProductQty.bind(this);
        this.updateRecipeProductQty = this.updateRecipeProductQty.bind(this);
        this.openCustomizationPanel = this.openCustomizationPanel.bind(this);
        this.toggleCustomisation = this.toggleCustomisation.bind(this);
        this.tappedCategory = this.tappedCategory.bind(this);
    }

    init() {
        if(this.props.campaignInitialized==null && appUtil.checkEmpty(this.props.deviceKey)){
            window.location.href = "https://cafes.chaayos.com";
        }else{
            var selectInfo = false;
            if(!this.props.fetchingUnitData && !this.props.showError && this.props.campaignInitialized==null){
                if (!appUtil.checkEmpty(this.props.criteria)) {
                    if (!appUtil.checkEmpty(this.props.selectedCity) && this.props.selectedCity.state!=null){
                        if(this.props.criteria=="DELIVERY" && !appUtil.checkEmpty(this.props.selectedLocality)){
                            this.props.dispatch(OutletMenuActions.getUnitProducts(this.props.criteria,this.props.selectedCity,
                                this.props.selectedLocality,null));
                        }else if(this.props.criteria!="DELIVERY" && !appUtil.checkEmpty(this.props.selectedOutlet)){
                            this.props.dispatch(OutletMenuActions.getUnitProducts(this.props.criteria,this.props.selectedCity,
                                null, this.props.selectedOutlet));
                        }else{
                            selectInfo = true;
                        }
                    }else{
                        selectInfo = true;
                    }
                }else{
                    this.props.dispatch(LocalityActions.setCriteria("DELIVERY"));
                    selectInfo = true;
                }
            }
            if(selectInfo){
                this.setState({mount:false});
                this.props.dispatch(showPopup("Please select locality or outlet.","info", 2000));
                browserHistory.push("/");
            }
        }
    }

    postInit() {
        if(this.state.mount) {
            document.getElementsByTagName("body")[0].addEventListener("scroll", _.debounce(this.stickyRelocate.bind(this)));
            this.navAction(0);
        }
    }

    stickyRelocate() {
        if (window.location.pathname == "/menu") {
            var menus = document.getElementsByClassName("menu");
            var tabs = document.getElementsByClassName("navTab");
            for (var i = 0; i < menus.length; i++) {
                if(i==0){
                    if(menus[i+1].getBoundingClientRect().top > 100 && !tabs[i].className.match(/\bactive\b/)){
                        this.navAction(i);
                        try{trackUtils.trackCategoryChanged(menus[i].getAttribute("id"));}catch(e){}
                        break;
                    }
                }else if(i+1==menus.length){
                    if(menus[i].getBoundingClientRect().top <= 100 && !tabs[i].className.match(/\bactive\b/)){
                        this.navAction(i);
                        try{trackUtils.trackCategoryChanged(menus[i].getAttribute("id"));
                        trackUtils.trackFullMenuExplored();}catch(e){}
                        break;
                    }
                }else{
                    if (menus[i].getBoundingClientRect().top <= 100 &&
                        menus[i+1].getBoundingClientRect().top > 100 && !tabs[i].className.match(/\bactive\b/)) {
                        this.navAction(i);
                        try{trackUtils.trackCategoryChanged(menus[i].getAttribute("id"));}catch(e){}
                        break;
                    }
                }
            }
        }
    }

    tappedCategory(cat){
        trackUtils.trackCategoryClicked(cat);
    }

    navAction(index) {
        var tabs = document.getElementsByClassName("navTab");
        for (var i = 0; i < tabs.length; i++) {
            tabs[i].className = tabs[i].className.replace(/\bactive\b/, '');
        }
        tabs[index].className += " active";
        var nav = tabs[index];
        if(nav){
            var navOffset = nav.getBoundingClientRect().left - tabs[0].getBoundingClientRect().left
                , sliderWidth = nav.offsetWidth
                , transformAmount = document.getElementById("navxSlider").style.transform ? parseInt(document.getElementById("navxSlider").style.transform.split("(")[1]) : 0;
            document.getElementById("navxSlider").style.width = sliderWidth + "px";
            document.getElementById("navxSlider").style.transform = "translateX(" + navOffset + "px)";
            if(navOffset>transformAmount){
                window.requestAnimationFrame(function () {
                    $('.navTabContainer').animate({
                        scrollLeft: navOffset
                    }, 600);
                });
            }else{
                window.requestAnimationFrame(function () {
                    $('.navTabContainer').animate({
                        scrollLeft: navOffset - window.innerWidth + sliderWidth
                    }, 600);
                });
            }
        }
    }

    loadProductRecipes(product) {
        if(product.strategy==4 || product.strategy==2){
            this.toggleCustomisation(product);
        }
        if (!product.recipesLoaded) {
            var prod = {...product};
            this.props.dispatch(OutletMenuActions.loadProductRecipe(this.props.unit, prod));
        }
    }

    openCustomizationPanel(product) {
        if(productService.stockAvailable(product)){
            var productObj = {...product};
            if(product.id==11){
                productObj.id = 10;
                productObj.name = "Desi Chai";
            }
            this.props.dispatch(CustomizationModalActions.showCustomizationModal(productObj, "MENU_ITEM", null));
            this.loadProductRecipes(product);
        }
    }

    toggleCustomisation(product){
        if(!product.showCustomization){
            this.props.dispatch(OutletMenuActions.toggleCustomization(product));
        }
    }

    updateProductQuantityDirectly(prod, type, event) {
        var webCategory = appUtil.findParentBySelector(event.target,".menu").getAttribute("id");
        var product = {...prod};
        if (type == "DECR") {
            if (product.cartQuantity == 1) {
                this.props.dispatch(CartManagementActions.removeItem(this.props.cart, product.cartItems[0], "menu",
                    this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
            } else {
                this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, -1, product.cartItems[0].itemId,
                    this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
            }
        } else {
            if(productService.stockAvailable(prod)){
                if (product.cartQuantity == 0) {
                    //var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                    var cartItem = product;
                    cartItem.selectedDimension = product.prices[0];
                    cartItem.selectedDimension.recipe = {ingredient:{variants:[],products:[],compositeProduct:null}};
                    cartItem.prices = [];
                    this.props.dispatch(CartManagementActions.addItemToCart({
                        cartItem: cartItem,
                        addons: [],
                        quantity: 1,
                        customizationStrategy: product.strategy,
                        webCategory: webCategory,
                        tags:this.props.productTags[prod.id]
                    }));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, 1, product.cartItems[0].itemId,
                        this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
                }
                if(product.cartQuantity+1 >= product.stock){
                    this.props.dispatch(OutletMenuActions.trackSelectionStockout(product.name));
                }
            }
        }
    }

    updateDimensionProductQty(product, dimension, type, event) {
        var webCategory = appUtil.findParentBySelector(event.target,".menu").getAttribute("id");
        var cartItem = null;
        product.cartItems.map((ci) => {
            if (ci.dimension == dimension) {
                cartItem = ci;
            }
        });
        if (type == "DECR") {
            if(cartItem != null){
                if (cartItem.quantity == 1) {
                    this.props.dispatch(CartManagementActions.removeItem(this.props.cart, cartItem, "menu",
                        this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, -1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
                }
            }
        } else {
            if(productService.stockAvailable(product)) {
                if (cartItem == null) {
                    var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                    cartItem = retObj.product;
                    //cartItem.selectedDimension = retObj.product.prices[0];
                    product.prices.map((price) => {
                        if (price.dimension == dimension) {
                            cartItem.selectedDimension = price;
                        }
                    });
                    cartItem.prices = [];
                    this.props.dispatch(CartManagementActions.addItemToCart({
                        cartItem: cartItem,
                        addons: [],
                        quantity: 1,
                        customizationStrategy: product.strategy,
                        webCategory: webCategory,
                        tags:this.props.productTags[product.id]
                    }));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, 1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
                }
                if(product.cartQuantity+1 >= product.stock){
                    this.props.dispatch(OutletMenuActions.trackSelectionStockout(product.name));
                }
            }
        }
    }

    updateRecipeProductQty(product, ingredientDetail, type, customizationType, event) {
        var webCategory = appUtil.findParentBySelector(event.target,".menu").getAttribute("id");
        var cartItem = null;
        product.cartItems.map((ci) => {
            if(customizationType=="PRODUCT"){
                ci.composition.products.map((compProduct) => {
                    if (compProduct.product.productId == ingredientDetail.product.productId) {
                        cartItem = ci;
                    }
                });
            }
            if(customizationType=="VARIANT"){
                ci.composition.variants.map((compVariant) => {
                    if (compVariant.alias == ingredientDetail.alias) {
                        cartItem = ci;
                    }
                });
            }
        });
        if (type == "DECR") {
            if (cartItem != null) {
                if (cartItem.quantity == 1) {
                    this.props.dispatch(CartManagementActions.removeItem(this.props.cart, cartItem,"menu",
                        this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, -1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
                }
            }
        } else {
            if(productService.stockAvailable(product)) {
                if (cartItem == null) {
                    var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                    cartItem = retObj.product;
                    cartItem.selectedDimension = retObj.product.prices[0];
                    cartItem.prices = [];
                    if (customizationType == "PRODUCT") {
                        cartItem.selectedDimension.recipe.ingredient.products.map((ingrProduct) => {
                            ingrProduct.details.map((ingProductDetail) => {
                                ingProductDetail.active = (ingProductDetail.product.productId == ingredientDetail.product.productId);
                            })
                        });
                    }
                    if (customizationType == "VARIANT") {
                        cartItem.selectedDimension.recipe.ingredient.variants.map((ingrVariant) => {
                            ingrVariant.details.map((ingVariantDetail) => {
                                ingVariantDetail.active = (ingVariantDetail.alias == ingredientDetail.alias);
                            })
                        });
                    }
                    this.props.dispatch(CartManagementActions.addItemToCart({
                        cartItem: cartItem,
                        addons: [],
                        quantity: 1,
                        customizationStrategy: product.strategy,
                        webCategory: webCategory,
                        tags:this.props.productTags[product.id]
                    }));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, 1, cartItem.itemId, this.props.skipDeliveryPackaging));
                }
                if(product.cartQuantity+1 >= product.stock){
                    this.props.dispatch(OutletMenuActions.trackSelectionStockout(product.name));
                }
            }
        }
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.init();
    }

    componentDidMount() {
        trackUtils.trackPageView({page:"menu",device:"mobile",custom:true});
        if(this.state.mount){
            this.postInit();
        }
    }

    componentDidUpdate(){
        if(this.props.initMenu){
            document.getElementById("navxSlider").style.width = document.getElementsByClassName("navTab")[0].offsetWidth + "px";
            this.props.dispatch(OutletMenuActions.setInitMenu(false));
        }
    }

    componentWillUnmount() {
        if(this.state.mount){
            document.getElementsByTagName("body")[0].removeEventListener("scroll", this.stickyRelocate.bind(this));
        }
    }

    render() {
        //var locationMetadata = appUtil.getLocalityMetadata();
        if (this.state.mount) {
            var desiChaiArr = productService.getDesiChaiArr();
            var baarishWaliChaiArr = productService.getBaarishWaliChaiArr();
            var productsMap = {};
            let specialProductsMap = {};
            if (this.props.unit != null && this.props.unit.products!=null && this.props.unit.products.length > 0) {
                this.props.unit.products.map((product) => {
                    var zeroPriceFound = false;
                    if(product.prices != null && product.prices.length > 0){
                        product.prices.map(function (price) {
                            if(!zeroPriceFound && price.price < 1){
                                zeroPriceFound = true;
                            }
                        })
                    }
                    if(!zeroPriceFound){
                        if([1065,30,20].indexOf(product.id)>-1 &&  this.props.criteria!="DINE_IN"){}else{
                            if (desiChaiArr.indexOf(product.id) < 0 && baarishWaliChaiArr.indexOf(product.id) < 0 && product.webType != 0 && product.prices.length>0) {
                                product.cartItems = [];
                                product.cartQuantity = 0;
                                product.recipeCustomizationCount = 0;
                                product.sizeCustomizations = [];
                                product.recipeCustomizations = [];
                                productService.setCartQuantityToProduct(this.props.cart,product);
                                productService.setRecipeCustomizationCount(product);
                                var tags = productService.createProductTagsLayout(this.props.productTags[product.id]);
                                if (product.customize == false) {
                                    if (product.prices.length > 1) {
                                        product.prices.map((price) => {
                                            var qty = 0;
                                            product.cartItems.map((cartItem) => {
                                                if (cartItem.dimension == price.dimension) {
                                                    qty = cartItem.quantity;
                                                }
                                            })
                                            product.sizeCustomizations.push(
                                                <div key={product.id + price.dimension} class="sizeCustomizationWrapper">
                                                    <span>{price.dimension=="None"?"Regular":price.dimension}</span>
                                                    {product.webType==3623 || product.webType==3624 || product.webType==3625?(
                                                        <span class="sizeDescription">
                                                    ({product.webType==3625?this.state.sizeDescription[price.dimension+"3625"]:this.state.sizeDescription[price.dimension]})
                                                </span>
                                                    ):null}
                                                    <span style={{marginLeft:"10px"}}><img class="rupeeIcon" src="../../img/rupee.png" /> {Math.round(price.priceInclusiveTax)}</span>
                                                    {qty == 0 ? (
                                                        <div class="addProductBtn"
                                                             onClick={this.updateDimensionProductQty.bind(this, product, price.dimension, "INCR")}>&#43;</div>
                                                    ) : (
                                                        <div class="qtyWrapper">
                                                            <div class="incr"
                                                                 onClick={this.updateDimensionProductQty.bind(this, product, price.dimension, "INCR")}>
                                                                +
                                                            </div>
                                                            <div class="qty">{qty}</div>
                                                            <div class="dcr"
                                                                 onClick={this.updateDimensionProductQty.bind(this, product, price.dimension, "DECR")}>
                                                                -
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            )
                                        })
                                    }
                                } else {
                                    if ((product.prices.length == 1) && (product.recipeCustomizationCount == 1) && product.recipesLoaded && product.showCustomization) {
                                        var price = product.prices[0];
                                        if (price.recipe.ingredient.products.length > 0) {
                                            var ingrDetail = [];
                                            price.recipe.ingredient.products.map((ingrProduct) => {
                                                ingrProduct.details.map((detail) => {
                                                    var qty = 0;
                                                    product.cartItems.map((cartItem) => {
                                                        cartItem.composition.products.map((prod) => {
                                                            if (prod.product.productId == detail.product.productId) {
                                                                qty += cartItem.quantity;
                                                            }
                                                        })
                                                    });
                                                    ingrDetail.push(
                                                        <div key={product.id + detail.product.productId}
                                                             class="sizeCustomizationWrapper">
                                                            <span>{detail.product.name}</span>
                                                            {qty == 0 ? (
                                                                <div class="addProductBtn"
                                                                     onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "PRODUCT")}>&#43;</div>
                                                            ) : (
                                                                <div class="qtyWrapper">
                                                                    <div class="incr"
                                                                         onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "PRODUCT")}>
                                                                        +
                                                                    </div>
                                                                    <div class="qty">{qty}</div>
                                                                    <div class="dcr"
                                                                         onClick={this.updateRecipeProductQty.bind(this, product, detail, "DECR", "PRODUCT")}>
                                                                        -
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    )
                                                });
                                                product.recipeCustomizations = (
                                                    <div>
                                                        {ingrDetail}
                                                    </div>
                                                );
                                            })
                                        }
                                        if (price.recipe.ingredient.products.length == 0 && product.prices[0].recipe.ingredient.variants.length > 0) {
                                            product.prices[0].recipe.ingredient.variants.map((variant) => {
                                                var variantDetail = [];
                                                variant.details.map((detail) => {
                                                    var qty = 0;
                                                    product.cartItems.map((cartItem) => {
                                                        cartItem.composition.variants.map((variant) => {
                                                            if (variant.alias == detail.alias) {
                                                                qty += cartItem.quantity;
                                                            }
                                                        })
                                                    });
                                                    variantDetail.push(
                                                        <div key={product.id + detail.alias} class="sizeCustomizationWrapper">
                                                            <span>{detail.alias}</span>
                                                            {qty == 0 ? (
                                                                <div class="addProductBtn"
                                                                     onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "VARIANT")}>&#43;</div>
                                                            ) : (
                                                                <div class="qtyWrapper">
                                                                    <div class="incr"
                                                                         onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "VARIANT")}>
                                                                        +
                                                                    </div>
                                                                    <div class="qty">{qty}</div>
                                                                    <div class="dcr"
                                                                         onClick={this.updateRecipeProductQty.bind(this, product, detail, "DECR", "VARIANT")}>
                                                                        -
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    )
                                                })
                                                product.recipeCustomizations = (
                                                    <div>
                                                        {variantDetail}
                                                    </div>
                                                );
                                            })
                                        }
                                    }
                                }
                                var data = productsMap[product.webType];
                                if (data == null) {
                                    data = [];
                                }
                                var productObj = (
                                    <div key={product.id+":"+product.prices[0].priceInclusiveTax} class="productContainer">
                                        <div class="productImage"
                                             style={{backgroundImage:"url(../../../img/products/" + product.id + ".jpg)"
                                             +", url(../../../img/defImageLoading.png )"}}>
                                            <div class="stockOutWrapper" style={product.inventoryLoaded && product.cartQuantity>=product.stock?{display:"block"}:{display:"none"}}>
                                                <div class="stockOut">out of stock</div>
                                            </div>
                                            {tags.length>0?(<div class="tagWrapper">{tags}</div>):null}
                                        </div>
                                        <div class="productDetail">
                                            <div class="productTitleWrapper">
                                                <div class="productTitle">
                                                    {product.attribute == 'VEG' ? (
                                                        <img class="vegNonVegIcon" src="../../img/veg.png"/>
                                                    ) : (null)}
                                                    {product.attribute == 'NON_VEG' ? (
                                                        <img class="vegNonVegIcon" src="../../img/non-veg.png"/>
                                                    ) : (null)}
                                                    {product.id==11?"Desi Chai":product.name}
                                                    {(product.webType==3623 || product.webType==3624 || product.webType==3625)?(
                                                        <span class="sizeDescription">
                                                    ({product.webType==3625?this.state.sizeDescription[product.prices[0].dimension+"3625"]:this.state.sizeDescription[product.prices[0].dimension]})
                                                </span>
                                                    ):null}
                                                </div>
                                                <div class="productPrice">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                    {Math.round(product.prices[0].priceInclusiveTax)}
                                                </div>
                                            </div>
                                            <div class="productDescriptionWrapper">
                                                <div class="productDescription">{product.description}</div>
                                                {product.strategy == 5 ? (
                                                    (product.cartQuantity > 0) ? (
                                                        <div class="qtyWrapper">
                                                            <div class="incr"
                                                                 onClick={this.updateProductQuantityDirectly.bind(this, product, "INCR")}>
                                                                +
                                                            </div>
                                                            <div class="qty">{product.cartQuantity}</div>
                                                            <div class="dcr"
                                                                 onClick={this.updateProductQuantityDirectly.bind(this, product, "DECR")}>
                                                                -
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div class="addProductBtn"
                                                             onClick={this.updateProductQuantityDirectly.bind(this, product, "INCR")}>
                                                            &#43;
                                                        </div>
                                                    )
                                                ) : null}
                                                {(!product.recipesLoaded && (product.strategy == 4 || product.strategy == 2)) ||
                                                (product.recipesLoaded && (!product.showCustomization || (product.showCustomization && product.cartQuantity > 0))) ? (
                                                    <div class="addProductBtn" onClick={!product.recipesLoaded ?
                                                        this.loadProductRecipes.bind(this, product):this.toggleCustomisation.bind(this, product)}>
                                                        {product.cartQuantity > 0 ? (
                                                            <div>{product.cartQuantity}</div>) : (<span>&#43;</span>)}
                                                    </div>
                                                ) : null}
                                                {product.strategy == 3 || product.strategy == 1 ? (
                                                    <div class="addProductBtn" onClick={this.openCustomizationPanel.bind(this, product)}>
                                                        {product.cartQuantity > 0 ? (
                                                            <div>{product.cartQuantity}</div>) : (<span>&#43;</span>)}
                                                    </div>
                                                ):null}
                                            </div>
                                            {product.showCustomization?(
                                                this.props.recipeLoadingProductId != null && product.id == this.props.recipeLoadingProductId ? (
                                                    <div class="loader load8"></div>
                                                ):(
                                                    <div>
                                                        {product.strategy == 4 ? (
                                                            <div class="customizationWrapper">
                                                                {product.sizeCustomizations}
                                                            </div>
                                                        ) : null}
                                                        {product.strategy == 2 ? (
                                                            <div class="customizationWrapper">
                                                                {product.recipeCustomizations}
                                                            </div>
                                                        ) : null}
                                                    </div>
                                                )
                                            ):null}
                                        </div>
                                    </div>
                                );
                                data.push(productObj);
                                productsMap[product.webType] = data;
                                //productService.setTopSellers(this.props.topSellersLoaded,this.props.tagDetail.TOP_SELLERS, productsMap, product.id, productObj);
                                productService.setSpecialProductsMap(specialProductsMap,this.props.specialMenu,product.id,productObj);
                                productService.setBoughtByYou(this.props.boughtByYou, productsMap, product, productObj);
                            }
                        }
                    }
                });
            }

            productService.sortSpecialProductsInMap(specialProductsMap,this.props.specialMenu);

            let specialMenuData = [];
            if(Object.keys(specialProductsMap).length>0){
                Object.keys(specialProductsMap).map((key,index) => {
                    specialMenuData.push(<div key={key+":"+index} id={key} class="menu">
                        <div class="categoryHeader">{appUtil.formatTag(key)}</div>
                        <div class="productsWrapper">{specialProductsMap[key]}</div>
                    </div>)
                });
            }
            let specialMenuList = [];
            if(Object.keys(specialProductsMap).length>0){
                Object.keys(specialProductsMap).map(key => {
                    specialMenuList.push(<a key={key} href={"#"+key} class="navTab" onClick={this.tappedCategory.bind(this, key)}>{appUtil.formatTag(key)}</a>)
                });
            }

            productService.sortProductsByPrice(productsMap);
            var dummyProduct = productService.getDummyProductLayout();

            return (
                <div id="appRoot">
                    <SidebarLayout />
                    <div class="staticHead">
                        <MobileHeader menu={true} showLocationMetadata={true} showCartBtn={true} props={this.props.props} />
                        <div class={(!this.props.showError && !this.props.fetchingUnitData)?"navTabContainer active":"navTabContainer"}>
                            {this.props.boughtByYou.length>0?<a href="#BoughtByYou" class="navTab" onClick={this.tappedCategory.bind(this, "BoughtByYou")}>Bought By You</a>:null}
                            {specialMenuList}
                            <a href="#IndianChai" class="navTab" onClick={this.tappedCategory.bind(this, "IndianChai")}>Indian Chai</a>
                            <a href="#Breakfast" class="navTab" onClick={this.tappedCategory.bind(this, "Breakfast")}>Breakfast</a>
                            <a href="#Meals" class="navTab" onClick={this.tappedCategory.bind(this, "Meals")}>Meals</a>
                            <a href="#Nashta" class="navTab" onClick={this.tappedCategory.bind(this, "Nashta")}>Nashta</a>
                            <a href="#Bakery" class="navTab" onClick={this.tappedCategory.bind(this, "Bakery")}>Bakery</a>
                            <a href="#ChaiUnChai" class="navTab" onClick={this.tappedCategory.bind(this, "ChaiUnChai")}>Chai - unChai</a>
                            <a href="#Cold" class="navTab" onClick={this.tappedCategory.bind(this, "Cold")}>Cold</a>
                            <a href="#Merchandise" class="navTab" onClick={this.tappedCategory.bind(this, "Merchandise")}>Merchandise</a>
                            <div id="navxSlider" class="slider"></div>
                        </div>
                    </div>
                    {(this.props.criteria=="DELIVERY" && this.props.selectedLocality!=null) ||
                    (this.props.selectedOutlet!=null)?(
                        (this.props.showError && !this.props.fetchingUnitData)? (
                            <div class="unitLoadingError">
                                <img src="../../../img/sadCup.svg" /><br />
                                Something went wrong.<br />Please select location/outlet again!
                            </div>
                        ) : (
                            <div class="rel">
                                <div class={this.props.fetchingUnitData ? "fetchingData active" : "fetchingData"}>
                                    <img src="../../../img/iconHappyCup.svg"/>
                                    {this.props.criteria == "DELIVERY" ? (
                                        <p class="loadingMessage">Fetching menu of our nearest outlet {this.props.selectedLocality!=null?"in "+this.props.selectedLocality.label:""} ...</p>
                                    ) : (
                                        <p class="loadingMessage">Fetching menu of our outlet {this.props.selectedOutlet!=null?this.props.selectedOutlet.label:""}</p>
                                    )}
                                </div>

                                <div class={(!this.props.fetchingUnitData && !this.props.showError) ? "menuContainer active" : "menuContainer"}>
                                    {this.props.boughtByYou.length>0?(
                                        <div id="BoughtByYou" class="menu">
                                            <div class="categoryHeader">Bought By You</div>
                                            <div class="productsWrapper">{productsMap[2]}</div>
                                        </div>
                                    ):null}
                                    {specialMenuData}
                                    <div id="IndianChai" class="menu">
                                        <div class="categoryHeader">Indian Chai</div>
                                        {productsMap[3623] != null ? (
                                            <div class="productsWrapper">{productsMap[3623]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="Breakfast" class="menu">
                                        <div class="categoryHeader">Breakfast</div>
                                        {productsMap[3626] != null ? (
                                            <div class="productsWrapper">{productsMap[3626]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="Meals" class="menu">
                                        <div class="categoryHeader">Meals</div>
                                        {productsMap[3627] != null ? (
                                            <div class="productsWrapper">{productsMap[3627]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="Nashta" class="menu">
                                        <div class="categoryHeader">Nashta</div>
                                        {productsMap[3628] != null ? (
                                            <div class="productsWrapper">{productsMap[3628]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="Bakery" class="menu">
                                        <div class="categoryHeader">Bakery</div>
                                        {productsMap[3629] != null ? (
                                            <div class="productsWrapper">{productsMap[3629]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="ChaiUnChai" class="menu">
                                        <div class="categoryHeader">Chai - unChai</div>
                                        {productsMap[3624] != null ? (
                                            <div class="productsWrapper">{productsMap[3624]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="Cold" class="menu">
                                        <div class="categoryHeader">Cold</div>
                                        {productsMap[3625] != null ? (
                                            <div class="productsWrapper">{productsMap[3625]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                    <div id="Merchandise" class="menu">
                                        <div class="categoryHeader">Merchandise</div>
                                        {productsMap[3631] != null ? (
                                            <div class="productsWrapper">{productsMap[3631]}</div>
                                        ) : (dummyProduct)}
                                    </div>
                                </div>
                                <MobileCustomizationModal />
                            </div>
                        )
                    ):null}
                </div>
            )
        }else{
            return null;
        }
    }

}