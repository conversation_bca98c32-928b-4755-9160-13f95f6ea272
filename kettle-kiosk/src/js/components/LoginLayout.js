import React from "react";
import appUtil from "../AppUtil";
import MobileLoginLayout from "./mobile/MobileLoginLayout";
import DesktopLoginLayout from "./desktop/DesktopLoginLayout";

export default class LoginLayout extends React.Component {
	render (){
		if(appUtil.isMobile()){
			return(
				<MobileLoginLayout props={this.props} />
			)
		}else{
			return(
				<DesktopLoginLayout props={this.props} />
			)
		}
	}
}