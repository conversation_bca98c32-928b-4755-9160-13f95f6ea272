/**
 * Created by Chaayos on 12-12-2016.
 */
import React from "react";
import { Link, browserHistory } from "react-router";
import { connect } from "react-redux";
import * as SidebarActions from "../actions/SidebarActions";
import StorageUtils from "../utils/StorageUtils";

@connect((store) => {
    return {
        loginStatus:store.sidebarReducer.loginStatus,
        sidebarOpen:store.sidebarReducer.sidebarOpen
    };
})
export default class SidebarLayout extends React.Component {

    constructor() {
        super();
        this.state = {
        };
        this.toggleSidebar = this.toggleSidebar.bind(this);
        this.home = this.home.bind(this);
        this.terms = this.terms.bind(this);
        this.logout = this.logout.bind(this);
        this.login = this.login.bind(this);
        this.orders = this.orders.bind(this);
        this.cart = this.cart.bind(this);
        this.profile = this.profile.bind(this);
        this.about = this.about.bind(this);
        this.contact = this.contact.bind(this);
    }

    toggleSidebar(){
        this.props.dispatch(SidebarActions.toggleSidebar());
    }

    home(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        browserHistory.push("/menu");
    }

    profile(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        var auth = StorageUtils.getAuthDetail();
        if(auth!=null && auth.sessionKey!=null){
            browserHistory.push("/account");
        }else{
            browserHistory.push("/login");
        }
    }

    about(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        //browserHistory.push("/about");
    }

    contact(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        //browserHistory.push("/contact");
    }

    terms(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        //browserHistory.push("/terms");
    }

    logout(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        this.props.dispatch(SidebarActions.logout());
    }

    login(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        browserHistory.push("/login");
    }

    orders(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        var auth = StorageUtils.getAuthDetail();
        if(auth!=null && auth.sessionKey!=null){
            browserHistory.push("/orders");
        }else{
            browserHistory.push("/login");
        }
    }

    cart(){
        this.props.dispatch(SidebarActions.toggleSidebar());
        browserHistory.push("/cart");
    }

    componentWillMount(){
        this.props.dispatch(SidebarActions.setLoginStatus());
    }

    render() {
        var authData = StorageUtils.getAuthDetail();
        return (
            <div class="sidebarWrapper" style={this.props.sidebarOpen?{pointerEvents:"auto"}:{pointerEvents:"none"}}>
                <div class="sidebarShadow" style={this.props.sidebarOpen?{opacity:"1"}:{opacity:"0"}}
                     onClick={this.toggleSidebar.bind(this)}></div>
                <div class="sidebarContentWrapper" style={this.props.sidebarOpen?{transform:"translate3d(0,0,0)"}:
                {transform:"translate3d(-102%,0,0)"}}>
                    <div class="sideBarMenu head" onClick={this.home.bind(this)}>
                        <img class="sidebarLogo" src="../../img/chaayosLogoGreen.svg"/>
                    </div>
                    <div class="sideBarMenu" onClick={this.home.bind(this)}>
                        <div class="listIcon"><img src="../../img/homeIcon.svg" /></div>
                        Home
                    </div>
                    <div class="sideBarMenu" onClick={this.profile.bind(this)}>
                        <div class="listIcon"><img src="../../img/profile.svg" /></div>
                        Profile
                    </div>
                    <div class="sideBarMenu" onClick={this.cart.bind(this)}>
                        <div class="listIcon"><img src="../../img/iconCart.svg" /></div>
                        My Cart
                    </div>
                    <div class="sideBarMenu" onClick={this.orders.bind(this)}>
                        <div class="listIcon"><img src="../../img/iconMyorders.svg" /></div>
                        My Orders
                    </div>
                    {(authData!=null && authData.sessionKey!=null)?(
                        <div class="sideBarMenu" onClick={this.logout.bind(this)}>
                            <div class="listIcon"><img src="data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTguMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDMzMCAzMzAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDMzMCAzMzA7IiB4bWw6c3BhY2U9InByZXNlcnZlIiB3aWR0aD0iMzJweCIgaGVpZ2h0PSIzMnB4Ij4KPGc+Cgk8cGF0aCBkPSJNMjQ1LjYwOCw4NC4zOTJjLTUuODU2LTUuODU3LTE1LjM1NS01Ljg1OC0yMS4yMTMtMC4wMDFjLTUuODU3LDUuODU4LTUuODU4LDE1LjM1NSwwLDIxLjIxM0wyNjguNzg5LDE1MEg4NS4wMDIgICBjLTguMjg0LDAtMTUsNi43MTYtMTUsMTVzNi43MTYsMTUsMTUsMTVoMTgzLjc4NWwtNDQuMzkyLDQ0LjM5MmMtNS44NTgsNS44NTgtNS44NTgsMTUuMzU1LDAsMjEuMjEzICAgYzIuOTI5LDIuOTI5LDYuNzY4LDQuMzkzLDEwLjYwNyw0LjM5M2MzLjgzOSwwLDcuNjc4LTEuNDY0LDEwLjYwNi00LjM5M2w2OS45OTgtNjkuOTk4YzUuODU4LTUuODU3LDUuODU4LTE1LjM1NSwwLTIxLjIxMyAgIEwyNDUuNjA4LDg0LjM5MnoiIGZpbGw9IiM4MjZiNWMiLz4KCTxwYXRoIGQ9Ik0xNTUsMzMwYzguMjg0LDAsMTUtNi43MTYsMTUtMTVzLTYuNzE2LTE1LTE1LTE1SDQwVjMwaDExNWM4LjI4NCwwLDE1LTYuNzE2LDE1LTE1cy02LjcxNi0xNS0xNS0xNUgyNSAgIGMtOC4yODQsMC0xNSw2LjcxNi0xNSwxNXYzMDBjMCw4LjI4NCw2LjcxNiwxNSwxNSwxNUgxNTV6IiBmaWxsPSIjODI2YjVjIi8+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+Cg==" /></div>
                            Logout
                        </div>
                    ):(
                        <div>
                            <div class="sideBarMenu" onClick={this.login.bind(this)}>
                                <div class="listIcon"><img class="listIcon" src="../../img/iconLogin.svg"/></div>
                                Login
                            </div>
                        </div>
                    )}
                    <div class="sidebarSeparator"></div>
                    <div class="static">
                        <Link to={"/about"} alt="About Chaayos" class="sideBarMenu" onClick={this.about.bind(this)}>About Us</Link>
                        <Link to={"/contact"} alt="Contact Us" class="sideBarMenu" onClick={this.contact.bind(this)}>Contact Us</Link>
                        <Link to={"/terms"} alt="Chaayos Terms and Conditions" class="sideBarMenu" onClick={this.terms.bind(this)}>Terms & Conditions</Link>
                        <Link to={"/privacy"} alt="Chaayos Privacy Policy" class="sideBarMenu" onClick={this.terms.bind(this)}>Privacy Policy</Link>
                    </div>
                </div>
            </div>
        )
    }
}