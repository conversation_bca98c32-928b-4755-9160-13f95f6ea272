/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileAboutLayout from "./mobile/MobileAboutLayout";
import DesktopAboutLayout from "./desktop/DesktopAboutLayout";

export default class AboutLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileAboutLayout props={this.props} />
            )
        } else {
            return (
                <DesktopAboutLayout props={this.props} />
            )
        }
    }
}