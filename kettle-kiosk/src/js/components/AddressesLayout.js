/**
 * Created by Chaayos on 06-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileAddressesLayout from "./mobile/MobileAddressesLayout";
import DesktopAddressesLayout from "./desktop/DesktopAddressesLayout";

export default class AddressesLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileAddressesLayout props={this.props} />
            )
        } else {
            return (
                <DesktopAddressesLayout props={this.props} />
            )
        }
    }
}