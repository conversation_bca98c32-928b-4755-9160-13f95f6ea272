/**
 * Created by Chaayos on 10-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileOrdersLayout from "./mobile/MobileOrdersLayout";
import DesktopOrdersLayout from "./desktop/DesktopOrdersLayout";

export default class OrderSummaryLayout extends React.Component {

    render() {
        if (appUtil.isMobile()) {
            return (
                <MobileOrdersLayout props={this.props} />
            )
        } else {
            return (
                <DesktopOrdersLayout props={this.props} />
            )
        }
    }
}