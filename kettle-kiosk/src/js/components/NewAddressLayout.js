/**
 * Created by Chaayos on 07-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileNewAddressLayout from "./mobile/MobileNewAddressLayout";
import DesktopNewAddressLayout from "./desktop/DesktopNewAddressLayout";

export default class NewAddressLayout extends React.Component {

    render() {
        if (appUtil.isMobile()) {
            return (
                <MobileNewAddressLayout props={this.props} />
            )
        } else {
            return (
                <DesktopNewAddressLayout props={this.props} />
            )
        }
    }
}