/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobilePrivacyLayout from "./mobile/MobilePrivacyLayout";
import DesktopPrivacyLayout from "./desktop/DesktopPrivacyLayout";

export default class PrivacyLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobilePrivacyLayout props={this.props} />
            )
        } else {
            return (
                <DesktopPrivacyLayout props={this.props} />
            )
        }
    }
}