/**
 * Created by Chaayos on 10-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileOrderSummaryLayout from "./mobile/MobileOrderSummaryLayout";
import DesktopOrderSummaryLayout from "./desktop/DesktopOrderSummaryLayout";

export default class OrderSummaryLayout extends React.Component {

    render() {
        if (appUtil.isMobile()) {
            return (
                <MobileOrderSummaryLayout props={this.props} />
            )
        } else {
            return (
                <DesktopOrderSummaryLayout props={this.props} />
            )
        }
    }
}