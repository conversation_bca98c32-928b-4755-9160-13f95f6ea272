import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux";
import appUtil from "../../AppUtil";
import StorageUtils from "../../utils/StorageUtils";
import * as SidebarActions from "../../actions/SidebarActions";
import * as LocalityActions from "../../actions/LocalityActions";
import * as UtilityActions from "../../actions/UtilityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        cart: store.cartManagementReducer.cart,
        selectedOutlet: store.localityReducer.selectedOutlet,
    };
})
export default class DesktopHeader extends React.Component {

    constructor() {
        super();
        this.state = {};
        this.goToCart = this.goToCart.bind(this);
        this.goBack = this.goBack.bind(this);
    }

    goToCart() {
        browserHistory.push("/cart");
    }

    goBack() {
        if(window.location.pathname == "/paymentModes"){
            trackUtils.trackReturnedFromPayment();
        } else {
            browserHistory.push("/menu");
        }
    }

    componentWillMount(){
    }

    render() {

        var cartItems = 0;
        var cart = this.props.cart;
        if(this.props.showCartBtn && !appUtil.checkEmpty(cart) && cart.orderDetail.orders.length>0){
            cartItems = cart.orderDetail.orders.length;
            cart.orderDetail.orders.map((orderItem) => {
                if(orderItem.productId==1043 || orderItem.productId==1044){
                    cartItems--;
                }
            });
        }

        return (
            <div class='headerWrapper'>
                {this.props.showBack ? (
                    <div class='headerBtn left' onClick={this.goBack.bind(this)}>
                        <img class="menuIcon" src="../../../img/back.svg"/>
                    </div>
                ):null}
                <div>
                    <div class="headerLogo">
                        <img src="../../../img/logo.svg"/>
                    </div>
                    <div className="outletWrapper ellipsis right">
                        {this.props.selectedOutlet != null ? this.props.selectedOutlet.label : ""}
                    </div>
                </div>
                <span class="right">
                    {this.props.showCartBtn ? (
                        <span class="headerBtn rel" onClick={this.goToCart.bind(this)}>
                            {(cartItems > 0) ? (
                                <span class="cartSizeLabel">{cartItems}</span>
                            ) : (null)}
                            <img src="../../../img/cart.svg"/>
                        </span>
                    ) : (null)}
                </span>
            </div>
        )
    }
}