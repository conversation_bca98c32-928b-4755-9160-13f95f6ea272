import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux";
import Slider from 'react-slick';
import DesktopHeader from "./DesktopHeader";
import DesktopLocationLayout from "./DesktopLocationLayout";
import SidebarLayout from "../SidebarLayout";
import DesktopFooterLayout from "./DesktopFooterLayout";
import appUtil from "../../AppUtil";
import * as LocalityActions from "../../actions/LocalityActions";
import * as UtilityActions from "../../actions/UtilityActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        criteria: store.localityReducer.criteria,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedOutlet: store.localityReducer.selectedOutlet,
        localities: store.localityReducer.localities,
        cities: store.localityReducer.cities,
        outlets: store.localityReducer.outlets,
        showLocality:store.localityReducer.showLocality,
        showOutlet:store.localityReducer.showOutlet,
        showError:store.localityReducer.showError,
        showLoader:store.localityReducer.showLoader,
        sidebarOpen: store.sidebarReducer.sidebarOpen,
        showLocationWrapper: store.localityReducer.showLocationWrapper,
    };
})
export default class DesktopHomeLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            citiesList:[],
            localitiesList: [],
            outletsList:[],
            lastScrollTop: 0
        };
        this.init = this.init.bind(this);
    }

    init() {
        //if(!this.props.localityDataSelected){
            /*var data = StorageUtils.getLocalityMetadata();
            if(data!=null){
                if(data.criteria!=null){
                    this.props.dispatch(LocalityActions.setCriteria(data.criteria));
                }else{
                    this.props.dispatch(LocalityActions.setCriteria("DELIVERY"));
                }
            }else{
                this.props.dispatch(LocalityActions.setCriteria("DELIVERY"));
            }*/
            var criteria = this.props.criteria;
            if(appUtil.checkEmpty(criteria)){
                criteria = "DELIVERY";
                this.props.dispatch(LocalityActions.setCriteria(criteria));
            }
            this.props.dispatch(LocalityActions.loadCitiesList(criteria));
        //}
    }

    goToMenu(){
        if(this.props.criteria==null){
            this.props.dispatch(UtilityActions.showPopup("Please select delivery or pickup.", "info"));
        }else if(this.props.criteria=="DELIVERY" && this.props.selectedLocality==null){
            this.props.dispatch(UtilityActions.showPopup("Please select delivery location.", "info"));
        }else if(this.props.criteria=="TAKE_AWAY" && this.props.selectedOutlet==null){
            this.props.dispatch(UtilityActions.showPopup("Please select outlet for pickup.", "info"));
        }else{
            browserHistory.push("/menu");
        }
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.init();
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"home",device:"desktop",custom:true});
    }

    render (){
        var settings = {
            dots: true,
            infinite: true,
            speed: 500,
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 7000
        };

        return(
            <div id="appRoot">
                <DesktopLocationLayout/>
                <div class={this.props.showLocationWrapper?"mainContent active":"mainContent"}>
                    <div class={this.props.showLocationWrapper?"locationWrapperOverlay active":"locationWrapperOverlay"}></div>
                    <SidebarLayout/>
                    <div class="headerWrapperContainer" style={{boxShadow:"#000 0 0 2px 0"}}>
                        <DesktopHeader menu={true} showLocationMetadata={true} showCartBtn={true} props={this.props.props} />
                    </div>
                    <div style={{paddingTop:"60px"}}>
                        <Slider {...settings}>
                            {/*<div class="imgSlide" onClick={this.redirect.bind(this, "https://pages.razorpay.com/giveback-chaayos")}
                                 style={{backgroundImage: 'url(../../../img/desktop/banner/giveback.jpg)'}}></div>*/}
                            <div class="imgSlide" style={{backgroundImage: 'url(../../../img/desktop/banner/contactlessChaayosApp.jpg)'}}></div>
                            <div class="imgSlide" style={{backgroundImage: 'url(../../../img/desktop/banner/meriWaliChai.jpg)'}}></div>
                            <div class="imgSlide" style={{backgroundImage: 'url(../../../img/desktop/banner/loyalty.jpg)'}}></div>
                            <div class="imgSlide" style={{backgroundImage: 'url(../../../img/desktop/banner/baarishAurChai.jpg)'}}></div>
                            <div class="imgSlide" style={{backgroundImage: 'url(../../../img/desktop/banner/merchansise.jpg)'}}></div>
                        </Slider>
                    </div>
                    <div style={{background:"#fff", padding:"30px 0 50px 0", textAlign:"center", marginTop:"20px",borderTop:"#f1f1f1 1px solid"}}>
                        <div style={{display:"inline-block",borderBottom:"#5e7e47 2px solid",fontSize:"23px",color:"#5e7e47",textTransform:"uppercase",cursor:"pointer"}}
                             onClick={this.goToMenu.bind(this)}>
                            <img width={"50px"} src="../../../img/desktop/kettle.svg" /><br />
                            Order Now
                        </div>
                        <div style={{color:"#686868", textAlign:"center", padding:"10px 0"}}>
                            Get your favourite food delivered to your home, or pick from nearest Chaayos cafe.
                        </div>
                    </div>
                    <DesktopFooterLayout />
                </div>
            </div>
        )
    }
}



/*
<div style={{background:"#f4f4f4", padding:"30px 0 50px 0"}}>
    <div style={{width:"1024px", margin:"0 auto"}}>
        <div class="headLine text-center">How it works</div>
    </div>
</div>*/
