import React from "react";
import {connect} from "react-redux";
import * as PaymentActions from "../../actions/PaymentActions";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import appUtil from "../../AppUtil";

@connect((store) => {
    return {
        showAGSModal: store.paymentReducer.showAGSModal,
        showAGSLoader: store.paymentReducer.showAGSLoader,
        agsInterval: store.paymentReducer.agsInterval,
        agsStatusCode: store.paymentReducer.agsStatusCode,
        agsStatusTitle: store.paymentReducer.agsStatusTitle,
        agsStatusMessage: store.paymentReducer.agsStatusMessage,
        orderFailed: store.orderManagementReducer.orderFailed,
        woId: store.orderManagementReducer.woId,
        timerDataMin: store.paymentReducer.timerDataMin,
        timerDataSec: store.paymentReducer.timerDataSec,
        timerInterval: store.paymentReducer.timerInterval,
        agsAmountToPay: store.paymentReducer.agsAmountToPay
    };
})
export default class AGSPaymentModal extends React.Component {

    constructor() {
        super();
        this.state = {
            couponCode: "",
            contactMsg: "",
            nameMsg: "",
            otpMsg: "",
            emailMsg: ""
        };
        this.closeModal = this.closeModal.bind(this);
        //this.placeOrder = this.placeOrder.bind(this);
        this.prependZero = this.prependZero.bind(this);
    }

    closeModal() {
        clearInterval(this.props.timerInterval);
        this.props.dispatch(PaymentActions.setShowAGSPaymentModal(false, null, this.props.agsInterval));
    }

    /*placeOrder() {
        this.props.dispatch(OrderManagementActions.orderCheckoutByWOId(this.props.woId, null));
    }*/

    componentWillMount() {

    }

    prependZero(number) {
        if (number < 9)
            return "0" + number;
        else
            return number;
    }

    render() {
        if (this.props.showAGSModal) {
            return (
                <div class="modal">
                    <div style={{
                        marginTop: '230px',
                        backgroundColor: '#fff',
                        overflow: 'auto',
                        top: '350px',
                        borderRadius: '5px',
                    }}>
                        {/*<div className="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                            &times;
                        </div>*/}
                        <p class="modalTitle">
                            Processing Payment
                        </p>
                        <div class="customizationSection productHead">
                            <div style={{textAlign: "center"}}>
                                {this.props.timerDataMin!=null && this.props.timerDataSec!=null? (
                                    <h1 style={{ fontSize: 70}}>{this.prependZero(this.props.timerDataMin)}:{this.prependZero(this.props.timerDataSec)}</h1>
                                ):null}
                                <div style={{
                                    width: '421px',
                                    fontSize: '32px',
                                    padding: '15px 60px',
                                    color: '#fff',
                                    backgroundColor: '#8BC34A',
                                    display: 'inline-block',
                                    textAlign: 'center',
                                    borderRadius: '22px',
                                    alignItems: 'center',
                                    margin: "20px"
                                }}>
                                    <p>Collecting Payment</p><br/>
                                    <img style={{width: "51px"}}
                                         src="../../../img/indian-ruppee-icon-white.png"/>
                                    <span style={{fontSize: "60px", margin: "0 0 0 15px", fontWeight: "bold"}}>{this.props.agsAmountToPay}</span>
                                </div>
                            </div>

                            {/*{this.props.showAGSLoader ? (
                                <div class="loaderWrapper">
                                    <div class="load8 loader"></div>
                                </div>
                            ) : null}*/}
                            {!appUtil.checkEmpty(this.props.agsStatusCode)?(
                                <div>
                                    <div style={{minHeight:"150px"}}>
                                        <div style={{textAlign:"center"}}>
                                            {/*Title Body*/}
                                            {(this.props.agsStatusTitle !=null) ? (
                                                <p style={{fontSize:"35px", fontWeight:"bold", color:"#3b3b3b", textAlign:"center", padding:"30px 0"}}>
                                                    {this.props.agsStatusTitle}
                                                </p>
                                            ) : null}
                                            {/*0 Insert card and enter pin or Tap*/}
                                            {(this.props.agsStatusCode === 0) ? (
                                                <div style={{textAlign: "center"}}>
                                                <span>
                                                    <img src="../../../img/payment/insert-card-animation.gif"
                                                         style={{maxWidth: "800px", marginBottom: "30px"}}/>
                                                </span>
                                                    <div style={{position: "fixed", bottom: "0", right: "0"}}>
                                                        <h2 style={{fontSize: "30px", fontWeight: "bold", color: "#fff"}}>Insert Card & Enter PIN</h2>
                                                        <h2 style={{fontSize: "30px", fontWeight: "bold", color: "#fff"}}>Or</h2>
                                                        <h2 style={{fontSize: "30px", fontWeight: "bold", color: "#fff"}}>Tap Card</h2>

                                                        <img style={{width: '50px', height: '100px'}} src="../../../img/payment/swipe-arrow.gif"/>
                                                    </div>
                                                </div>
                                            ) : null}
                                            {/*1 Order details missing or 2 amount missing or 5 black response*/}
                                            {(this.props.agsStatusCode === 1
                                                || this.props.agsStatusCode === 2
                                                || this.props.agsStatusCode === 5) ? (
                                                <div style={{textAlign: "center"}}>
                                                    <img src="../../../img/payment/missing.gif"
                                                         style={{width:"300px", height:"300px" ,marginBottom:"30px"}} />
                                                </div>
                                            ) : null}
                                            {/*Gateway Down*/}
                                            {(this.props.agsStatusCode === 3) ? (
                                                <div style={{textAlign: "center"}}>
                                                    <img src="../../../img/payment/ags_gateway_down.gif"
                                                         style={{width:"300px", height:"300px" ,marginBottom:"30px"}} />
                                                </div>
                                            ) : null}
                                            {/*Not Connected*/}
                                            {(this.props.agsStatusCode === 4) ? (
                                                <div style={{textAlign: "center"}}>
                                                    <img src="../../../img/payment/not_connected.gif"
                                                         style={{width:"300px", height:"300px" ,marginBottom:"30px"}} />
                                                </div>
                                            ) : null}
                                            {/*Payment Successful*/}
                                            {(this.props.agsStatusCode === 6 && this.props.orderFailed != true) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/payment-successful.gif" />
                                                </div>
                                            ) : null}
                                            {/*Payment Failed*/}
                                            {(this.props.agsStatusCode === 7) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/failed.png" />
                                                </div>
                                            ) : null}
                                            {/*Payment Cancelled.*/}
                                            {(this.props.agsStatusCode === 8) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/cancelled2.png" />
                                                </div>
                                            ) : null}
                                            {/*Payment Void.*/}
                                            {(this.props.agsStatusCode === 9) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/trans_void.jpg" />
                                                </div>
                                            ) : null}
                                            {/*Transaction Failed.*/}
                                            {(this.props.agsStatusCode === 10) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/failed.png" />
                                                </div>
                                            ) : null}
                                            {/*Card Error.*/}
                                            {(this.props.agsStatusCode === 11) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/card_invalid.png" />
                                                </div>
                                            ) : null}
                                            {/*Already Refunded*/}
                                            {(this.props.agsStatusCode === 12) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/success.png" />
                                                </div>
                                            ) : null}
                                            {/*Already Refunded*/}
                                            {(this.props.agsStatusCode === 13 || this.props.agsStatusCode === 14) ? (
                                                <div style={{textAlign:"center"}}>
                                                    <img src="../../../img/payment/missing.png" />
                                                </div>
                                            ) : null}
                                            {/*Some Error Occurred*/}
                                            {(this.props.agsStatusCode === 15) ? (
                                                <div style={{textAlign: "center"}}>
                                                    <img src="../../../img/payment/cancelled2.png"
                                                         style={{width:"300px", height:"300px" ,marginBottom:"30px"}} />
                                                </div>
                                            ) : null}
                                            {/*Message Body*/}
                                            {(!appUtil.checkEmpty(this.props.agsStatusMessage)) ? (
                                                <p style={{fontSize:"35px", fontWeight:"bold",
                                                    color:"#3b3b3b", textAlign:"center", padding:"30px 0"}}>
                                                    {this.props.agsStatusMessage}
                                                </p>
                                            ) : null}
                                        </div>
                                    </div>
                                    {/*Payment Not Successful*/}
                                    {(this.props.agsStatusCode !== 6 && this.props.agsStatusCode !== 0) ? (
                                        <div style={{textAlign: "right"}}>
                                            <div className="btn btn-default couponBtn" onClick={this.closeModal.bind(this)}
                                                 style={{display: "inline-block"}}>
                                                Cancel
                                            </div>
                                        </div>
                                    ):null}
                                </div>
                            ):null}
                        </div>
                    </div>
                </div>
            )
        } else {
            return null;
        }
    }
}