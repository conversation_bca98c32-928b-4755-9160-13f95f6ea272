import React from "react";
import { connect } from "react-redux";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";

@connect((store) => {
    return {
        cart:store.cartManagementReducer.cart,
        showInstructionsModal: store.outletMenuReducer.showInstructionsModal,
    };
})
export default class DesktopInstructionsModal extends React.Component {

    constructor() {
        super();
        this.state = {
            remark:""
        };
        this.closeModal = this.closeModal.bind(this);
        this.addInstructions = this.addInstructions.bind(this);
        this.handleChange = this.handleChange.bind(this);
    }


    closeModal() {
        this.props.dispatch(OutletMenuActions.setShowInstructionModal(false));
    }

    addInstructions(){
        var remark = document.getElementById("remark").value;
        this.props.dispatch(CartManagementActions.setOrderRemark(this.props.cart, remark));
        this.closeModal();
    }

    handleChange(event) {
        this.setState({remark: event.target.value});
    }

    componentWillMount() {
        var remark = this.props.cart != null ? this.props.cart.orderDetail.orderRemark : "";
        this.setState({remark:remark});
    }

    render() {
        if(this.props.showInstructionsModal) {
            return (
                <div className="modal">
                    <div className="modalBody">
                        <div className="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                            &times;
                        </div>
                        <p className="modalTitle">
                            Add Instructions
                        </p>
                        <div className="customizationSection productHead">
                            <div className="contactContainer">
                            <textarea rows="3" id="remark" type="text" placeholder="e.g make it kadak" autoComplete="off"
                                      value={this.state.remark} onChange={this.handleChange.bind(this)} />
                            </div>
                            <div className="btn btn-primary" style={{marginTop: '16px'}}
                                 onClick={this.addInstructions.bind(this)}>
                                Add Instructions
                            </div>
                        </div>
                    </div>
                </div>
            )
        } else {
            return null;
        }
    }
}