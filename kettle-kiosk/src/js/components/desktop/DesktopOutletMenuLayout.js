import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux";
import $ from "jquery";
import _ from "lodash";
import IdleTimer from 'react-idle-timer';
// import DesktopLocationLayout from "./DesktopLocationLayout";
// import DesktopFooterLayout from "./DesktopFooterLayout";
// import SidebarLayout from "../SidebarLayout";
import appUtil from "../../AppUtil";
import DesktopHeader from "./DesktopHeader";
import DesktopBannerHeader from "./DesktopBannerHeader";
import DesktopCartItemLayout from "./DesktopCartItemLayout";
import DesktopInstructionsModal from "./DesktopInstructionsModal";
import DesktopCouponModal from "./DesktopCouponModal";
import MobileCustomizationModal from "../mobile/MobileCustomizationModal";
import * as LocalityActions from "../../actions/LocalityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as CustomizationModalActions from "../../actions/CustomizationModalActions";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as CampaignManagementActions from "../../actions/CampaignManagementActions";
import trackUtils from "../../utils/TrackUtils";
import productService from "../../service/ProductService";
import StorageUtils from "../../utils/StorageUtils";
import * as UtilityActions from "../../actions/UtilityActions";
import * as OrderManagementActions from "../../actions/OrderManagementActions";

@connect((store) => {
    return {
        showLocationWrapper: store.localityReducer.showLocationWrapper,
        criteria: store.localityReducer.criteria,
        selectedCity: store.localityReducer.selectedCity,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedOutlet: store.localityReducer.selectedOutlet,
        unit: store.outletMenuReducer.unit,
        skipDeliveryPackaging:store.outletMenuReducer.skipDeliveryPackaging,
        outletShowError: store.outletMenuReducer.showError,
        fetchingUnitData: store.outletMenuReducer.fetchingUnitData,
        cart: store.cartManagementReducer.cart,
        specialMenu: store.outletMenuReducer.specialMenu,
        recipeLoadingProductId: store.outletMenuReducer.recipeLoadingProductId,
        productTags: store.outletMenuReducer.productTags,
        boughtByYou: store.outletMenuReducer.boughtByYou,
        initMenu: store.outletMenuReducer.initMenu,
        cityStateMap: store.localityReducer.cityStateMap,
        campaignInitialized: store.campaignReducer.campaignInitialized,
        kettleAuthDetails: store.outletLoginReducer.kettleAuthDetails,
    };
})
export default class DesktopOutletMenuLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            sizeDescription:productService.getSizeDescriptions(),
            products: {},
            citiesList:[],
            localitiesList: [],
            outletsList:[],
            taxDetail: "",
            timeout:1000 * 240,
            userLoggedIn: false,
            isTimedOut: false
        };
        this.init = this.init.bind(this);
        this.loadUnitData = this.loadUnitData.bind(this);
        this.tappedCategory = this.tappedCategory.bind(this);

        //unit load calls
        this.postInit = this.postInit.bind(this);
        this.stickyRelocate = this.stickyRelocate.bind(this);
        this.navAction = this.navAction.bind(this);
        this.loadProductRecipes = this.loadProductRecipes.bind(this);
        this.updateProductQuantityDirectly = this.updateProductQuantityDirectly.bind(this);
        this.updateDimensionProductQty = this.updateDimensionProductQty.bind(this);
        this.updateRecipeProductQty = this.updateRecipeProductQty.bind(this);
        this.openCustomizationPanel = this.openCustomizationPanel.bind(this);
        this.toggleCustomisation = this.toggleCustomisation.bind(this);
        this.setOrderRemark = this.setOrderRemark.bind(this);
        this.showTaxDetail = this.showTaxDetail.bind(this);
        this.showInstructionModal = this.showInstructionModal.bind(this);
        this.showCouponModal = this.showCouponModal.bind(this);
        this.clearCart = this.clearCart.bind(this);

        this.idleTimer = null;
        this.onAction = this._onAction.bind(this);
        this.onActive = this._onActive.bind(this);
        this.onIdle = this._onIdle.bind(this);
    }



    _onAction(e) {
        console.log('user did something', e)
        this.setState({isTimedOut: false})
    }

    _onActive(e) {
        console.log('user is active', e)
        this.setState({isTimedOut: false})
    }

    _onIdle(e) {
        console.log('user is idle from cart menu', e)
        const isTimedOut = this.state.isTimedOut
        if (isTimedOut) {
            console.log('timed out idle from cart menu');
            // navigate to cart menu after clearing cart and contact details
            // browserHistory.push("/menu");
            this.props.dispatch(OrderManagementActions.startNewOrder());

        } else {
            this.setState({showModal: true})
            this.idleTimer.reset();
            this.setState({isTimedOut: true})
        }

    }

    init() {
        var criteria = "DINE_IN";
        this.props.dispatch(LocalityActions.setCriteria(criteria));
        if(this.props.kettleAuthDetails != null && this.props.kettleAuthDetails.authToken != null){
            if(this.props.unit == null) {
                var selectedOutlet = {label:this.props.kettleAuthDetails.unitDetail.name, value:this.props.kettleAuthDetails.unitDetail.id};
                this.props.dispatch(LocalityActions.selectOutlet(selectedOutlet));
                this.loadUnitData(criteria, null, null, selectedOutlet);
            } else {
                this.props.dispatch(OutletMenuActions.loadUnitInventory(null));
            }
            this.props.dispatch(LocalityActions.setShowOutlet(true));
            this.props.dispatch(CartManagementActions.initCart(this.props.unit, this.props.skipDeliveryPackaging, false));
            this.props.dispatch(CartManagementActions.setCartStock(this.props.unitInventory));
        } else {
            browserHistory.push("/outletLogin");
        }
        this.props.dispatch(OutletMenuActions.setShowInstructionModal(false));
        StorageUtils.setCustomerDetail(null);
    }

    loadUnitData(criteria, city, locality, outlet) {
        this.props.dispatch(OutletMenuActions.getUnitProducts(criteria, city, locality, outlet));
        this.props.dispatch(OutletMenuActions.getTags());
        if(window.location.pathname=="/"){
            browserHistory.push("/menu");
        }
    }

    tappedCategory(cat){
        var tabs = document.getElementsByClassName("navTab");
        var hash = "#" + cat;
        for (var i = 0; i < tabs.length; i++) {
            if(hash != tabs[i].hash){
                tabs[i].className = tabs[i].className.replace(/\bactive\b/, '');
            }else{
                tabs[i].className += " active";
                this.navAction(i);
            }
        }
        $('html, body').animate({scrollTop: $(hash).offset().top - 200 }, 'slow');
        trackUtils.trackCategoryClicked(cat);
    }

    postInit(){
        document.getElementsByTagName("body")[0].addEventListener("scroll", _.debounce(this.stickyRelocate.bind(this)));
        this.navAction(0);
    }

    stickyRelocate() {
        if (window.location.pathname == "/menu") {
            var menus = document.getElementsByClassName("menu");
            var tabs = document.getElementsByClassName("navTab");
            for (var i = 0; i < menus.length; i++) {
                if(i==0){
                    if(menus[i+1].getBoundingClientRect().top > 100 && !tabs[i].className.match(/\bactive\b/)){
                        this.navAction(i);
                        try{trackUtils.trackCategoryChanged(menus[i].getAttribute("id"));}catch(e){}
                        break;
                    }
                }else if(i+1==menus.length){
                    if(menus[i].getBoundingClientRect().top <= 100 && !tabs[i].className.match(/\bactive\b/)){
                        this.navAction(i);
                        try{trackUtils.trackCategoryChanged(menus[i].getAttribute("id"));
                        trackUtils.trackFullMenuExplored();}catch(e){}
                        break;
                    }
                }else{
                    if (menus[i].getBoundingClientRect().top <= 100 &&
                        menus[i+1].getBoundingClientRect().top > 100 && !tabs[i].className.match(/\bactive\b/)) {
                        this.navAction(i);
                        try{trackUtils.trackCategoryChanged(menus[i].getAttribute("id"));}catch(e){}
                        break;
                    }
                }
            }
        }
    }

    navAction(index) {
        var tabs = document.getElementsByClassName("navTab");
        for (var i = 0; i < tabs.length; i++) {
            tabs[i].className =
                tabs[i].className.replace(/\bactive\b/, '');
        }
        tabs[index].className += " active";
        var nav = tabs[index];
        if(nav){
            var navOffset = nav.getBoundingClientRect().left - tabs[0].getBoundingClientRect().left
                , sliderWidth = nav.offsetWidth
                , transformAmount = document.getElementById("navxSlider").style.transform ? parseInt(document.getElementById("navxSlider").style.transform.split("(")[1]) : 0;
            document.getElementById("navxSlider").style.width = sliderWidth + "px";
            document.getElementById("navxSlider").style.transform = "translateX(" + navOffset + "px)";
            if(navOffset>transformAmount){
                window.requestAnimationFrame(function () {
                    $('.navTabContainer').animate({
                        scrollLeft: navOffset
                    }, 600);
                });
            }else{
                window.requestAnimationFrame(function () {
                    $('.navTabContainer').animate({
                        scrollLeft: navOffset - window.innerWidth + sliderWidth
                    }, 600);
                });
            }
        }
    }

    loadProductRecipes(product) {
        if (!product.recipesLoaded) {
            this.props.dispatch(OutletMenuActions.loadProductRecipe(this.props.unit, product));
        }
    }

    openCustomizationPanel(product) {
        if(productService.stockAvailable(product)){
            var productObj = {...product};
            if(product.id==11){
                productObj.id = 10;
                productObj.name = "Desi Chai";
            }
            this.props.dispatch(CustomizationModalActions.showCustomizationModal(productObj, "MENU_ITEM", null));
            this.loadProductRecipes(product);
        }
    }

    toggleCustomisation(product){
        if(!product.showCustomization){
            this.props.dispatch(OutletMenuActions.toggleCustomization(product));
        }
    }

    updateProductQuantityDirectly(prod, type, event) {
        var webCategory = null;
        if(event != null) {
            webCategory = appUtil.findParentBySelector(event.target,".menu").getAttribute("id");
        }
        var product = {...prod};
        product.prices = prod.prices.slice();
        if (type == "DECR") {
            if (product.cartQuantity == 1) {
                this.props.dispatch(CartManagementActions.removeItem(this.props.cart, product.cartItems[0], "menu",
                    this.props.skipDeliveryPackaging, false));
            } else {
                this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, -1, product.cartItems[0].itemId,
                    this.props.skipDeliveryPackaging, false));
            }
        } else {
            if(productService.stockAvailable(prod)){
                if (product.cartQuantity == 0) {
                    //var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                    var cartItem = product;
                    cartItem.selectedDimension = product.prices[0];
                    cartItem.selectedDimension.recipe = {ingredient:{variants:[],products:[],compositeProduct:null}};
                    cartItem.prices = [];
                    this.props.dispatch(CartManagementActions.addItemToCart({
                        cartItem: cartItem,
                        addons: [],
                        quantity: 1,
                        customizationStrategy: product.strategy,
                        webCategory: webCategory,
                        tags:this.props.productTags[prod.id]
                    }));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, 1, product.cartItems[0].itemId,
                        this.props.skipDeliveryPackaging, false));
                }
                if(product.cartQuantity+1 >= product.stock){
                    this.props.dispatch(OutletMenuActions.trackSelectionStockout(product.name));
                }
            }
        }
    }

    updateDimensionProductQty(product, dimension, type, event) {
        var webCategory = null;
        if(event != null) {
            webCategory = appUtil.findParentBySelector(event.target,".menu").getAttribute("id");
        }
        var cartItem = null;
        product.cartItems.map((ci) => {
            if (ci.dimension == dimension) {
                cartItem = ci;
            }
        });
        if (type == "DECR") {
            if(cartItem != null){
                if (cartItem.quantity == 1) {
                    this.props.dispatch(CartManagementActions.removeItem(this.props.cart, cartItem, "menu",
                        this.props.skipDeliveryPackaging, false));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, -1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, false));
                }
            }
        } else {
            if(productService.stockAvailable(product)) {
                if (cartItem == null) {
                    var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                    cartItem = retObj.product;
                    //cartItem.selectedDimension = retObj.product.prices[0];
                    product.prices.map((price) => {
                        if (price.dimension == dimension) {
                            cartItem.selectedDimension = price;
                        }
                    });
                    cartItem.prices = [];
                    this.props.dispatch(CartManagementActions.addItemToCart({
                        cartItem: cartItem,
                        addons: [],
                        quantity: 1,
                        customizationStrategy: product.strategy,
                        webCategory: webCategory,
                        tags:this.props.productTags[product.id]
                    }));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, 1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, false));
                }
                if(product.cartQuantity+1 >= product.stock){
                    this.props.dispatch(OutletMenuActions.trackSelectionStockout(product.name));
                }
            }
        }
    }

    updateRecipeProductQty(product, ingredientDetail, type, customizationType, event) {
        var webCategory = null;
        if(event != null) {
            webCategory = appUtil.findParentBySelector(event.target,".menu").getAttribute("id");
        }
        var cartItem = null;
        product.cartItems.map((ci) => {
            if(customizationType=="PRODUCT"){
                ci.composition.products.map((compProduct) => {
                    if (compProduct.product.productId == ingredientDetail.product.productId) {
                        cartItem = ci;
                    }
                });
            }
            if(customizationType=="VARIANT"){
                ci.composition.variants.map((compVariant) => {
                    if (compVariant.alias == ingredientDetail.alias) {
                        cartItem = ci;
                    }
                });
            }
        });
        if (type == "DECR") {
            if (cartItem != null) {
                if (cartItem.quantity == 1) {
                    this.props.dispatch(CartManagementActions.removeItem(this.props.cart, cartItem, "menu",
                        this.props.skipDeliveryPackaging, false));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, -1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, false));
                }
            }
        } else {
            if(productService.stockAvailable(product)) {
                if (cartItem == null) {
                    var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                    cartItem = retObj.product;
                    cartItem.selectedDimension = retObj.product.prices[0];
                    cartItem.prices = [];
                    if (customizationType == "PRODUCT") {
                        cartItem.selectedDimension.recipe.ingredient.products.map((ingrProduct) => {
                            ingrProduct.details.map((ingProductDetail) => {
                                ingProductDetail.active = (ingProductDetail.product.productId == ingredientDetail.product.productId);
                            })
                        });
                    }
                    if (customizationType == "VARIANT") {
                        cartItem.selectedDimension.recipe.ingredient.variants.map((ingrVariant) => {
                            ingrVariant.details.map((ingVariantDetail) => {
                                ingVariantDetail.active = (ingVariantDetail.alias == ingredientDetail.alias);
                            })
                        });
                    }
                    this.props.dispatch(CartManagementActions.addItemToCart({
                        cartItem: cartItem,
                        addons: [],
                        quantity: 1,
                        customizationStrategy: product.strategy,
                        webCategory: webCategory,
                        tags:this.props.productTags[product.id]
                    }));
                } else {
                    this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, 1, cartItem.itemId,
                        this.props.skipDeliveryPackaging, false));
                }
                if(product.cartQuantity+1 >= product.stock){
                    this.props.dispatch(OutletMenuActions.trackSelectionStockout(product.name));
                }
            }
        }
    }

    handleProductClick(product) {
        if(product.strategy == 5) {
            this.updateProductQuantityDirectly(product, "INCR");
        } else {
            this.openCustomizationPanel(product);
        }
    }

    setOrderRemark() {
        var remark = document.getElementById("orderRemarkInput").value;
        this.props.dispatch(CartManagementActions.setOrderRemark(this.props.cart, remark));
    }

    applyCoupon() {
        var couponCode = document.getElementById("couponInput").value;
        if (!appUtil.checkEmpty(couponCode)) {
            this.props.dispatch(CartManagementActions.applyCoupon(couponCode.toUpperCase()
                , appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
        } else {
            this.props.dispatch(UtilityActions.showPopup("Please enter a coupon code."));
        }
    }

    setCouponAndApply(code){
        if(code!=null){
            document.getElementById("couponInput").value = code.toUpperCase();
        }
        this.applyCoupon();
    }

    removeCoupon() {
        document.getElementById("couponInput").value = "";
        try{
            if(!appUtil.checkOrderDetailsEmpty(this.props.cart)){
                trackUtils.trackCouponRemoved(this.props.cart.orderDetail.offerCode);
            }
        }catch(e){}
        this.props.dispatch(CartManagementActions.removeCoupon(appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
    }

    placeOrder() {
        this.props.dispatch(CartManagementActions.placeOrder(this.props.cart,this.props.unit,this.props.selectedCity));
        //this.props.dispatch(CartManagementActions.syncCart());
    }

    syncAndLogin(){
        this.props.dispatch(CartManagementActions.syncCartAndLogin());
    }

    showTaxDetail() {
        /*if (this.state.taxDetail == "open") {
            this.setState({
                taxDetail: ""
            })
        } else {
            try{trackUtils.trackTaxDetailViewed(this.props.cart.cartId);}catch(e){}
            this.setState({
                taxDetail: "open"
            })
        }*/
    }

    showInstructionModal() {
        this.props.dispatch(OutletMenuActions.setShowInstructionModal(true));
    }

    showCouponModal() {
        this.props.dispatch({type:"SET_COUPON_LOGIN", payload: false});
        this.props.dispatch(OutletMenuActions.setShowCouponModal(true));
    }

    clearCart() {
        this.props.dispatch(CartManagementActions.clearCart());
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        //this.props.dispatch(CampaignManagementActions.initCampaign(this.props.props, this.props.campaignInitialized));
        this.init();
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"menu",device:"desktop",custom:true});
        this.postInit();
    }

    componentDidUpdate(){
        if(this.props.initMenu){
            document.getElementById("navxSlider").style.width = document.getElementsByClassName("navTab")[0].offsetWidth + "px";
            this.props.dispatch(OutletMenuActions.setInitMenu(false));
        }
    }

    componentWillUnmount() {
        document.getElementsByTagName("body")[0].removeEventListener("scroll", this.stickyRelocate.bind(this));
    }

    render (){
        //menu layout code
        var desiChaiArr = productService.getDesiChaiArr();
        var baarishWaliChaiArr = productService.getBaarishWaliChaiArr();
        var productsMap = {};
        let specialProductsMap = {};
        if (this.props.unit != null && this.props.unit.products!=null && this.props.unit.products.length > 0) {
            this.props.unit.products.map((product) => {
                var zeroPriceFound = false;
                if(product.prices != null && product.prices.length > 0){
                    product.prices.map(function (price) {
                        if(!zeroPriceFound && price.price < 1){
                            zeroPriceFound = true;
                        }
                    })
                }
                if(!zeroPriceFound){
                    if([1065,30,20].indexOf(product.id)>-1 &&  this.props.criteria!="DINE_IN"){

                    } else{
                        if (desiChaiArr.indexOf(product.id) < 0 && baarishWaliChaiArr.indexOf(product.id) < 0 && product.webType != 0 && product.prices.length>0) {
                            product.cartItems = [];
                            product.cartQuantity = 0;
                            product.recipeCustomizationCount = 0;
                            product.sizeCustomizations = [];
                            product.recipeCustomizations = [];
                            productService.setCartQuantityToProduct(this.props.cart,product);
                            productService.setRecipeCustomizationCount(product);
                            var tags = productService.createProductTagsLayout(this.props.productTags[product.id]);
                            if (product.customize == false) {
                                if (product.prices.length > 1) {
                                    product.prices.map((price) => {
                                        var qty = 0;
                                        product.cartItems.map((cartItem) => {
                                            if (cartItem.dimension == price.dimension) {
                                                qty = cartItem.quantity;
                                            }
                                        });
                                        product.sizeCustomizations.push(
                                            <div key={product.id + price.dimension} class="sizeCustomizationWrapper">
                                                <span>{price.dimension=="None"?"Regular":price.dimension}</span>
                                                {product.webType==3623 || product.webType==3624 || product.webType==3625?(
                                                    <span class="sizeDescription">
                                                    ({product.webType==3625?this.state.sizeDescription[price.dimension+"3625"]:this.state.sizeDescription[price.dimension]})
                                                </span>
                                                ):null}
                                                <span style={{marginLeft:"10px"}}><img class="rupeeIcon" src="../../img/rupee.png" /> {Math.round(price.priceInclusiveTax)}</span>
                                                {qty == 0 ? (
                                                    <div class="addProductBtn"
                                                         onClick={this.updateDimensionProductQty.bind(this, product, price.dimension, "INCR")}>Add</div>
                                                ) : (
                                                    <div class="qtyWrapper">
                                                        <div class="incr"
                                                             onClick={this.updateDimensionProductQty.bind(this, product, price.dimension, "INCR")}>
                                                            +
                                                        </div>
                                                        <div class="qty">{qty}</div>
                                                        <div class="dcr"
                                                             onClick={this.updateDimensionProductQty.bind(this, product, price.dimension, "DECR")}>
                                                            -
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        )
                                    })
                                }
                            } else {
                                if ((product.prices.length == 1) && (product.recipeCustomizationCount == 1) && product.recipesLoaded && product.showCustomization) {
                                    var price = product.prices[0];
                                    if (price.recipe.ingredient.products.length > 0) {
                                        var ingrDetail = [];
                                        price.recipe.ingredient.products.map((ingrProduct) => {
                                            ingrProduct.details.map((detail) => {
                                                var qty = 0;
                                                product.cartItems.map((cartItem) => {
                                                    cartItem.composition.products.map((prod) => {
                                                        if (prod.product.productId == detail.product.productId) {
                                                            qty += cartItem.quantity;
                                                        }
                                                    })
                                                });
                                                ingrDetail.push(
                                                    <div key={product.id + detail.product.productId}
                                                         class="sizeCustomizationWrapper">
                                                        <span>{detail.product.name}</span>
                                                        {qty == 0 ? (
                                                            <div class="addProductBtn"
                                                                 onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "PRODUCT")}>Add</div>
                                                        ) : (
                                                            <div class="qtyWrapper">
                                                                <div class="incr"
                                                                     onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "PRODUCT")}>
                                                                    +
                                                                </div>
                                                                <div class="qty">{qty}</div>
                                                                <div class="dcr"
                                                                     onClick={this.updateRecipeProductQty.bind(this, product, detail, "DECR", "PRODUCT")}>
                                                                    -
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                )
                                            });
                                            product.recipeCustomizations = (
                                                <div>
                                                    {ingrDetail}
                                                </div>
                                            );
                                        })
                                    }
                                    if (price.recipe.ingredient.products.length == 0 && product.prices[0].recipe.ingredient.variants.length > 0) {
                                        product.prices[0].recipe.ingredient.variants.map((variant) => {
                                            var variantDetail = [];
                                            variant.details.map((detail) => {
                                                var qty = 0;
                                                product.cartItems.map((cartItem) => {
                                                    cartItem.composition.variants.map((variant) => {
                                                        if (variant.alias == detail.alias) {
                                                            qty += cartItem.quantity;
                                                        }
                                                    })
                                                });
                                                variantDetail.push(
                                                    <div key={product.id + detail.alias} class="sizeCustomizationWrapper">
                                                        <span>{detail.alias}</span>
                                                        {qty == 0 ? (
                                                            <div class="addProductBtn"
                                                                 onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "VARIANT")}>Add</div>
                                                        ) : (
                                                            <div class="qtyWrapper">
                                                                <div class="incr"
                                                                     onClick={this.updateRecipeProductQty.bind(this, product, detail, "INCR", "VARIANT")}>
                                                                    +
                                                                </div>
                                                                <div class="qty">{qty}</div>
                                                                <div class="dcr"
                                                                     onClick={this.updateRecipeProductQty.bind(this, product, detail, "DECR", "VARIANT")}>
                                                                    -
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                )
                                            });
                                            product.recipeCustomizations = (
                                                <div>
                                                    {variantDetail}
                                                </div>
                                            );
                                        })
                                    }
                                }
                            }
                            var data = productsMap[product.webType];
                            if (data == null) {
                                data = [];
                            }
                            var productObj = (
                                <div key={product.id+":"+product.prices[0].priceInclusiveTax} class="productContainer">
                                    <div class="productImage"
                                         style={{backgroundImage:"url(../../../img/products/" + product.id + ".jpg)"
                                         +", url(../../../img/defImageLoading.png )"}}>
                                        <div class="stockOutWrapper" style={product.inventoryLoaded && product.cartQuantity>=product.stock?{display:"block"}:{display:"none"}}>
                                            <div class="stockOut">out of stock</div>
                                        </div>
                                        {tags.length>0?(<div class="tagWrapper">{tags}</div>):null}
                                    </div>
                                    <div class="productDetail">
                                        <div class="productTitleWrapper">
                                            <div class="productTitle">
                                                {product.attribute == 'VEG' ? (
                                                    <img class="vegNonVegIcon" src="../../img/veg.png"/>
                                                ) : (null)}
                                                {product.attribute == 'NON_VEG' ? (
                                                    <img class="vegNonVegIcon" src="../../img/non-veg.png"/>
                                                ) : (null)}
                                                {product.id==11?"Desi Chai":product.name}
                                                {(product.webType==3623 || product.webType==3624 || product.webType==3625)?(
                                                    <span class="sizeDescription">
                                                    ({product.webType==3625?this.state.sizeDescription[product.prices[0].dimension+"3625"]:this.state.sizeDescription[product.prices[0].dimension]})
                                                    </span>
                                                ):null}
                                            </div>

                                        </div>
                                        <div class="productDescriptionWrapper">
                                            <div class="productDescription">
                                                <div className="productPrice">
                                                    <img className="rupeeIcon" src="../../img/rupee.png"/>
                                                    {Math.round(product.prices[0].priceInclusiveTax)}
                                                </div>
                                            </div>
                                            {product.strategy == 5 ? (
                                                (product.cartQuantity > 0) ? (
                                                    <div class="qtyWrapper" style={{marginRight:"10px"}}>
                                                        <div class="incr"
                                                             onClick={this.updateProductQuantityDirectly.bind(this, product, "INCR")}>
                                                            +
                                                        </div>
                                                        <div class="qty">{product.cartQuantity}</div>
                                                        <div class="dcr"
                                                             onClick={this.updateProductQuantityDirectly.bind(this, product, "DECR")}>
                                                            -
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <div class={product.inventoryLoaded && product.cartQuantity>=product.stock
                                                            ? "addProductBtn disabled" : "addProductBtn"}
                                                         onClick={this.updateProductQuantityDirectly.bind(this, product, "INCR")}>
                                                        {product.inventoryLoaded && product.cartQuantity>=product.stock ? (
                                                            <span style={{fontSize:"18px"}}>Out of Stock</span>
                                                        ) : "Add"}
                                                    </div>
                                                )
                                            ) : null}
                                            {(product.strategy == 4 || product.strategy == 2) ? (
                                                <div class={product.inventoryLoaded && product.cartQuantity>=product.stock
                                                    ? "addProductBtn disabled" : "addProductBtn"}
                                                    onClick={!product.recipesLoaded ?
                                                    this.loadProductRecipes.bind(this, product):this.openCustomizationPanel.bind(this, product)}>
                                                    {product.cartQuantity > 0 ? (
                                                        <div>
                                                            {product.cartQuantity}
                                                        </div>
                                                    ) : (
                                                        <span>
                                                            {product.inventoryLoaded && product.cartQuantity>=product.stock ? (
                                                                <span style={{fontSize:"18px"}}>Out of Stock</span>
                                                            ) : "Add"}
                                                        </span>
                                                    )}
                                                </div>
                                            ) : null}
                                            {(product.strategy == 3 || product.strategy == 1) ? (
                                                <div class={product.inventoryLoaded && product.cartQuantity>=product.stock
                                                    ? "addProductBtn disabled" : "addProductBtn"}

                                                     onClick={this.openCustomizationPanel.bind(this, product)}>
                                                    {product.cartQuantity > 0 ? (
                                                        <div>
                                                            {product.cartQuantity}
                                                        </div>
                                                    ) : (
                                                        <span>
                                                            {product.inventoryLoaded && product.cartQuantity>=product.stock ? (
                                                                <span style={{fontSize:"18px"}}>Out of Stock</span>
                                                            ) : "Add"}
                                                        </span>
                                                    )}
                                                </div>
                                            ): null}
                                        </div>
                                        {/*{product.showCustomization?(
                                            this.props.recipeLoadingProductId != null && product.id == this.props.recipeLoadingProductId ? (
                                                <div class="loader load8"></div>
                                            ):(
                                                <div>
                                                    {product.strategy == 4 ? (
                                                        <div class="customizationWrapper">
                                                            {product.sizeCustomizations}
                                                        </div>
                                                    ) : null}
                                                    {product.strategy == 2 ? (
                                                        <div class="customizationWrapper">
                                                            {product.recipeCustomizations}
                                                        </div>
                                                    ) : null}
                                                </div>
                                            )
                                        ):null}*/}
                                    </div>
                                </div>
                            );
                            data.push(productObj);
                            productsMap[product.webType] = data;
                            //productService.setTopSellers(this.props.topSellersLoaded,this.props.tagDetail.TOP_SELLERS, productsMap, product.id, productObj);
                            productService.setSpecialProductsMap(specialProductsMap,this.props.specialMenu,product.id,productObj);
                            productService.setBoughtByYou(this.props.boughtByYou, productsMap, product, productObj);
                        }
                    }
                }
            });
        }

        productService.sortSpecialProductsInMap(specialProductsMap,this.props.specialMenu);

        let specialMenuData = [];
        if(Object.keys(specialProductsMap).length>0){
            Object.keys(specialProductsMap).map((key,index) => {
                specialMenuData.push(<div key={key+":"+index} id={key} class="menu">
                    <div class="categoryHeader">{appUtil.formatTag(key)}</div>
                    <div class="productsWrapper">{specialProductsMap[key]}</div>
                </div>)
            });
        }
        let specialMenuList = [];
        if(Object.keys(specialProductsMap).length>0){
            Object.keys(specialProductsMap).map(key => {
                specialMenuList.push(<a key={key} href={"#"+key} class="navTab" onClick={this.tappedCategory.bind(this, key)}>{appUtil.formatTag(key)}</a>)
            });
        }

        productService.sortProductsByPrice(productsMap);

        var dummyProduct = productService.getDummyProductLayout();

        var cartItems = 0;
        var cart = this.props.cart;
        if(!appUtil.checkEmpty(cart) && cart.orderDetail.orders.length>0){
            cartItems = cart.orderDetail.orders.length;
            cart.orderDetail.orders.map((orderItem) => {
                if(orderItem.productId==1043 || orderItem.productId==1044){
                    cartItems--;
                }
            });
        }

        var items = [];
        if(cartItems > 0) {
            var delivery = 0, packaging = 0;
            this.props.cart.orderDetail.orders.map((orderItem, index) => {
                if (orderItem.productId != 1043 && orderItem.productId != 1044) {
                    items.push(<DesktopCartItemLayout key={index} item={orderItem}/>);
                } else {
                    if (orderItem.productId == 1043) {
                        packaging = orderItem.totalAmount;
                    }
                    if (orderItem.productId == 1044) {
                        delivery = orderItem.totalAmount;
                    }
                }
            });
            var taxDetails = [];
            if(this.props.cart.orderDetail.transactionDetail != null) {
                this.props.cart.orderDetail.transactionDetail.taxes.map(tax => {
                    taxDetails.push(
                        <div key={tax.code+tax.percentage} class="txDetailItem">
                            {tax.code} ({tax.percentage}%)
                            <span class="right">
                                <img class="rupeeIcon" src="../../img/rupee.png"/>
                                {tax.value}
                            </span>
                        </div>
                    )
                });
            }
        }

        var remark = (this.props.cart != null && this.props.cart.orderDetail!=null) ? (this.props.cart.orderDetail.orderRemark) : "";
        if(remark == null) {
            remark = "";
        }
        if(remark.length > 28) {
            remark = remark.substring(0,28) + "...";
        }

        return(
            <div id="appRoot">

                <IdleTimer
                    ref={ref => { this.idleTimer = ref }}
                    element={document}
                    onActive={this.onActive}
                    onIdle={this.onIdle}
                    onAction={this.onAction}
                    debounce={250}
                    timeout={this.state.timeout} />
                {/*<DesktopLocationLayout />*/}
                <div class={this.props.showLocationWrapper?"mainContent active":"mainContent"}>
                    {/*<div class={this.props.showLocationWrapper?"locationWrapperOverlay active":"locationWrapperOverlay"}></div>*/}
                    {/*<SidebarLayout />*/}
                    <div class="headerWrapperContainer">
                        {/*<DesktopHeader showBack={false} showCartBtn={false} props={this.props.props} />*/}
                        <DesktopBannerHeader/>
                        <div class="navTabContainerWrapper">
                            <div class="leftMenuContainer">
                                <a href="#MilkChai" className="navTab"onClick={this.tappedCategory.bind(this, "MilkChai")}>Milk Chai</a>
                                <a href="#Non-MilkChai" className="navTab"onClick={this.tappedCategory.bind(this, "Non-MilkChai")}>Non-Milk Chai</a>
                                <a href="#Shakes" className="navTab" onClick={this.tappedCategory.bind(this, "Shakes")}>Shakes</a>
                                <a href="#IcedTeas" className="navTab" onClick={this.tappedCategory.bind(this, "IcedTeas")}>Iced Teas</a>
                                <a href="#Lemonades" className="navTab"onClick={this.tappedCategory.bind(this, "Lemonades")}>Lemonades</a>
                                <a href="#Meals" className="navTab"onClick={this.tappedCategory.bind(this, "Meals")}>Meals</a>
                                <a href="#QuickBites" className="navTab"onClick={this.tappedCategory.bind(this, "QuickBites")}>Quick Bites</a>
                                <a href="#Chaats" className="navTab"onClick={this.tappedCategory.bind(this, "Chaats")}>Chaats</a>
                                <a href="#Sandwiches" className="navTab"onClick={this.tappedCategory.bind(this, "Sandwiches")}>Sandwiches</a>
                                <a href="#Bakery" className="navTab"onClick={this.tappedCategory.bind(this, "Bakery")}>Bakery</a>
                                <a href="#PackagedProducts" className="navTab longText"onClick={this.tappedCategory.bind(this, "PackagedProducts")}>Packaged Products</a>
                                <div id="navxSlider" className="slider"></div>
                            </div>
                        </div>
                    </div>

                    {(this.props.outletShowError && !this.props.fetchingUnitData)? (
                        <div class="unitLoadingError" style={{marginTop:"100px"}}>
                            <img src="../../../img/sadCup.svg" /><br />
                            Something went wrong.<br />Please call cafe operator!
                        </div>
                    ) : (
                        <div class={cartItems > 0 ? "outletMenuWrapper cart":"outletMenuWrapper"}>
                            <div class={this.props.fetchingUnitData ? "fetchingData active" : "fetchingData"}>
                                <img src="../../../img/iconHappyCup.svg"/>
                                {this.props.criteria == "DELIVERY" ? (
                                    <p class="loadingMessage">Fetching menu of our nearest outlet
                                        {this.props.selectedLocality!=null?"in "+this.props.selectedLocality.label:""} ...</p>
                                ) : (
                                    <p class="loadingMessage">Fetching menu of our outlet {this.props.selectedOutlet!=null?this.props.selectedOutlet.label:""}</p>
                                )}
                            </div>

                            <div class={(!this.props.fetchingUnitData && !this.props.outletShowError) ? "menuContainer active" : "menuContainer"}>
                                {(this.props.criteria=="DELIVERY" && this.props.selectedLocality!=null) ||
                                ((this.props.criteria=="TAKE_AWAY" || this.props.criteria=="DINE_IN") && this.props.selectedOutlet!=null)?(
                                    <div>
                                        {/*{specialMenuData}*/}
                                        <div id="MilkChai" class="menu">
                                            <div class="categoryHeader">Milk Chai</div>
                                            {productsMap[3623] != null ? (
                                                <div class="productsWrapper">{productsMap[3623]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Non-MilkChai" class="menu">
                                            <div class="categoryHeader">Non-Milk Chai</div>
                                            {productsMap[3624] != null ? (
                                                <div class="productsWrapper">{productsMap[3624]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Shakes" class="menu">
                                            <div class="categoryHeader">Shakes</div>
                                            {productsMap[3625] != null ? (
                                                <div class="productsWrapper">{productsMap[3625]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="IcedTeas" class="menu">
                                            <div class="categoryHeader">Iced Teas</div>
                                            {productsMap[3663] != null ? (
                                                <div class="productsWrapper">{productsMap[3663]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Lemonades" class="menu">
                                            <div class="categoryHeader">Lemonades</div>
                                            {productsMap[3664] != null ? (
                                                <div class="productsWrapper">{productsMap[3664]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Meals" class="menu">
                                            <div class="categoryHeader">Meals</div>
                                            {productsMap[3626] != null ? (
                                                <div class="productsWrapper">{productsMap[3626]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="QuickBites" class="menu">
                                            <div class="categoryHeader">Quick Bites</div>
                                            {productsMap[3627] != null ? (
                                                <div class="productsWrapper">{productsMap[3627]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Chaats" class="menu">
                                            <div class="categoryHeader">Chaats</div>
                                            {productsMap[3628] != null ? (
                                                <div class="productsWrapper">{productsMap[3628]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Sandwiches" class="menu">
                                            <div class="categoryHeader">Sandwiches</div>
                                            {productsMap[3665] != null ? (
                                                <div class="productsWrapper">{productsMap[3665]}</div>
                                            ) : (dummyProduct)}
                                        </div>
                                        <div id="Bakery" class="menu">
                                            <div class="categoryHeader">Bakery</div>
                                            {productsMap[3629] != null ? (
                                                    <div class="productsWrapper">{productsMap[3629]}</div>
                                                ) : (dummyProduct)}
                                        </div>
                                        <div id="PackagedProducts" class="menu">
                                            <div class="categoryHeader">Packaged Products</div>
                                            {productsMap[3631] != null ? (
                                                    <div class="productsWrapper">{productsMap[3631]}</div>
                                                ) : (dummyProduct)}
                                        </div>
                                    </div>
                                ):null}
                            </div>
                            <MobileCustomizationModal />
                        </div>
                    )}
                    {cartItems > 0 ? (
                        <div style={{position:"absolute", bottom:"0px", height:"410px", width:"100%", background:"#f3f3f3", borderTop:"#ddd 1px solid",
                            overflow:"auto", zIndex:"2"}}>
                            <div style={{background:"#5e7e47", color:"#FFF", padding:"10px"}}>
                                <span className="clearCartBox" onClick={this.clearCart.bind(this)}>
                                        Clear Cart
                                    </span>
                                <div style={{width:"504px", margin:"0px 0 0px 520px", fontSize:"21px"}}>
                                    My Cart
                                    <span style={{float:"right", background:"#d3dc03", borderRadius:"3px", color:"#000", padding:"3px 11px",
                                        fontWeight:"bold", marginTop:"-3px"}}>
                                        TOTAL :
                                        <img class="rupeeIcon" src="../../../img/rupee.png" style={{height:"19px", margin:"0 3px -2px 10px"}} />
                                        {this.props.cart.orderDetail.transactionDetail != null ?
                                            Math.round(this.props.cart.orderDetail.transactionDetail.paidAmount) : 0}
                                    </span>
                                </div>
                            </div>
                            <div class="desktopPageContainer" style={{boxShadow: "none", paddingBottom:"0", background:"transparent", minHeight:"auto", height:"350px"}}>
                                <div class="cartMovingContainer">
                                    <div class="cartHead"
                                         style={{marginRight: "1%", boxShadow: "0 1px 2px 0 #e4e4df"}}>
                                        {cartItems} {cartItems > 1 ? "Items" : "Item"}
                                        {/*<div class="btn btn-primary clearCart"
                                             onClick={this.clearCart.bind(this)}>Clear Cart</div>*/}
                                    </div>
                                    <div class="cartItems">
                                        {items.reverse()}
                                    </div>
                                </div>

                                <div class="fixedContainer">
                                    {/*<div class="cartBox">
                                        <div class="cartHead" onClick={this.showInstructionModal.bind(this)} style={{color:"#5e7e47", cursor:"pointer", fontWeight:"bold", fontSize:"16px"}}>
                                            {remark.length > 0 ? remark : "Add Instructions"}
                                            <img src="../../../img/chevronRight.svg" style={{float:"right", margin:"2px 15px 0 0"}} />
                                        </div>
                                    </div>*/}
                                    <div class="cartBox">
                                        <div class="cartHead" onClick={this.showCouponModal.bind(this)} style={{color:"#5e7e47", cursor:"pointer", fontWeight:"bold", fontSize:"16px"}}>
                                            Apply Coupon
                                            <img src="../../../img/chevronRight.svg" style={{float:"right", margin:"2px 15px 0 0"}} />
                                        </div>
                                    </div>
                                    <div class="cartBox">
                                        <div class="cartHead">Price details</div>
                                        <div class="cartItemContainer">
                                            <div class="transactionDetails">
                                                <div class="txDetailItem">
                                                    Coupon Discount
                                                    <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {this.props.cart.orderDetail.transactionDetail != null ?
                                                            Math.round(this.props.cart.orderDetail.transactionDetail.discountDetail.totalDiscount) : 0}
                                                </span>
                                                </div>
                                                <div class="txDetailItem" onClick={this.showTaxDetail.bind(this)}>
                                                    Tax {/*<img class="taxInfo" src="../../img/info-icon.png"/>*/}
                                                    <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {this.props.cart.orderDetail.transactionDetail != null ?
                                                            Math.round(this.props.cart.orderDetail.transactionDetail.tax) : 0}
                                                </span>
                                                </div>
                                                <div class={"taxDetailWrapper " + this.state.taxDetail}>
                                                    {taxDetails}
                                                </div>
                                                <div class="txDetailItem totalAmount"
                                                     style={{borderTop: "#ddd 1px dashed"}}>
                                                    Amount Payable
                                                    <span class="right">
                                                        <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                            {this.props.cart.orderDetail.transactionDetail != null ?
                                                                Math.round(this.props.cart.orderDetail.transactionDetail.paidAmount) : 0}
                                                    </span>
                                                </div>
                                                <div class="txDetailItem">
                                                    Savings
                                                    <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {this.props.cart.orderDetail.transactionDetail != null ?
                                                            Math.round(this.props.cart.orderDetail.transactionDetail.savings) : 0}
                                                </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="btn btn-primary" style={{margin: "10px 0"}}
                                         onClick={this.placeOrder.bind(this)}>
                                        Place Order
                                    </div>
                                </div>
                            </div>

                        </div>
                    ):null}
                    {/*<DesktopInstructionsModal />*/}
                    <DesktopCouponModal />
                </div>
            </div>
        )
    }
}