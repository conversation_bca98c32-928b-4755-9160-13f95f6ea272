import React from "react";
import {connect} from "react-redux";
import * as PaymentActions from "../../actions/PaymentActions";
import DesktopPaytmQRCodeModal from "./DesktopPaytmQRCodeModal";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import * as CustomerActions from "../../actions/CustomerActions";
import appUtil from "../../AppUtil";
import * as UtilityActions from "../../actions/UtilityActions";
import storageUtils from "../../utils/StorageUtils";
import {payByPaytmQRForGiftCard} from "../../actions/PaymentActions";
import {setPayTMQRCodeId} from "../../actions/PaymentActions";

@connect((store) => {
    return {
        showGiftCardPurchaseModal: store.paymentReducer.showGiftCardPurchaseModal,
        customer: store.customerReducer.customer,
        giftCardCart: store.cartManagementReducer.giftCardCart,
        cart: store.cartManagementReducer.cart,
        unit: store.outletMenuReducer.unit,
        allGiftCards: store.paymentReducer.allGiftCards,
        showLoader: store.paymentReducer.showLoader,
        loadingMessage: store.paymentReducer.loadingMessage,
        sessionKey: store.customerReducer.sessionKey,
        deviceKey: store.customerReducer.deviceKey,
        paytmQRStatus: store.paymentReducer.paytmQRStatus,
        paytmQRInterval: store.paymentReducer.paytmQRInterval,
        cardPaymentMode: store.paymentReducer.cardPaymentMode,

        timerDataMin: store.paymentReducer.timerDataMin,
        timerDataSec: store.paymentReducer.timerDataSec,
        secRemaining: store.paymentReducer.secRemaining,
        timerInterval: store.paymentReducer.timerInterval,
        stopTimer: store.paymentReducer.stopTimer

    };
})
export default class GiftCardPurchaseModal extends React.Component {

    constructor() {
        super();
        // this.props.dispatch(PaymentActions.createGiftCardCart(this.props));
        this.payByCardMachine = this.payByCardMachine.bind(this);

        this.intervalHandle;
        this.initializeTimer = this.initializeTimer.bind(this);
        this.handleChange = this.handleChange.bind(this);
        this.startCountDown = this.startCountDown.bind(this);
        this.tick = this.tick.bind(this);
    }

    initializeTimer(){
        this.props.dispatch(PaymentActions.initializeTimer());
    }

    handleChange(event) {
        this.props.dispatch({type: "SET_TIMER_MIN", payload: event.target.value});
    }

    startCountDown() {
        this.initializeTimer();
        this.intervalHandle = setInterval(this.tick, 1000);
        this.props.dispatch({type: "SET_TIMER_INTERVAL", payload: this.intervalHandle});
        this.props.dispatch({type: "SET_STOP_TIMER", payload: false});
    }

    tick() {
        console.log("this.props.stopTimer:: "+ this.props.stopTimer);
        if(this.props.stopTimer){
            console.log("this.props.stopTimer:: "+ this.intervalHandle);
            clearInterval(this.intervalHandle);
        }
        console.log("secs remaining:: "+ this.props.secRemaining);
        console.log("mins remaining:: "+ this.props.timerDataMin);
        console.log("sec remaining:: "+ this.props.timerDataSec);
        this.props.dispatch({type: "SET_TIMER_MIN", payload: Math.floor(this.props.secRemaining / 60)});
        this.props.dispatch({type: "SET_TIMER_SEC", payload: this.props.secRemaining - (this.props.timerDataMin * 60)});
        console.log("timerDataMin " + this.props.timerDataMin);
        console.log("timerDataSec " + this.props.timerDataSec);
        if (this.props.timerDataMin === 0 && this.props.timerDataSec === 0) {
            console.log("Closing the pop up!!");
            clearInterval(this.props.timerInterval);
            this.props.dispatch({type: "SET_SHOW_AGS_MODAL", payload: false});
            this.initializeTimer();
        }
        let secRemain = this.props.secRemaining;
        secRemain = secRemain -1;
        console.log("secRemain minus 1:: "+ secRemain);
        this.props.dispatch({type: "SET_SEC_REMAINING", payload: secRemain});
    }

    payByPaytmQR() {
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        this.props.dispatch(PaymentActions.payByPaytmQRForGiftCard(this.props));
    }

    payByCardMachine() {
        console.log("Gift Card: Card Payment type is :: ", this.props.cardPaymentMode);
        if(this.props.cardPaymentMode === 'EZETAP'){
            this.payByEzetap();
        }
        if(this.props.cardPaymentMode === 'AGS'){
            this.payByAGS();
        }
    }

    payByEzetap() {
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        this.props.dispatch(PaymentActions.payWithEzetapForGiftCard(this.props));
    }

    payByAGS() {
        this.startCountDown();
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        this.props.dispatch(PaymentActions.payWithAGSForGiftCard(this.props));
    }

    closeModal() {
        clearInterval(this.props.paytmQRInterval);
        this.props.dispatch(PaymentActions.setPayTMQRCodeId(null));
        this.props.dispatch(PaymentActions.syncOldCartForGiftCard(this.props));
    }

    render() {
        if (this.props.showGiftCardPurchaseModal) {
            return (
                <div className="modal" style={{background: "rgba(0, 0, 0, 0.3)"}}>
                    <div className="modalBody"
                         style={{
                             top: "20%",
                             padding: "20px",
                             width: "95%",
                             height: "100%",
                             left: "28px",
                             borderRadius: "35px"
                         }}>
                        <span></span>
                        <div className="modalCloseBtn"
                             style={{right: "50px", top: "53px", fontSize: "50px", color: "white"}}
                             onClick={() => this.closeModal()}>
                            &times;
                        </div>
                        <div className="modalTitle" style={{
                            fontSize: "40px",
                            marginTop: "10px",
                            padding: "30px",
                            color: "white",
                            background: "#5e7e47",
                            borderRadius: "35px",
                            height: "auto"
                        }}>
                            Wallet Payment
                        </div>
                        <div>
                            <div style={{
                                fontSize: "45px",
                                textAlign: "center",
                                color: "#5e7e47",
                                marginTop: "30px",
                                fontWeight: "600"
                            }}>
                                Total Amount
                                <img className="rupeeIcon" style={{margin: "0px 5px 0 20px"}}
                                     src="../../../img/rupee.png"/>
                                <span>{this.props.customer.giftCardAmount}</span>
                            </div>
                            {/*<div style={{textAlign: "center", height: "325px", marginTop: "35px"}}>*/}
                            {/*<canvas id="paytmQrForGiftCard"></canvas>*/}
                            {/*</div>*/}

                            {/*{this.props.showLoader ?*/}
                            {/*<div>*/}
                            {/*<span style={{*/}
                            {/*fontSize: "25px",*/}
                            {/*textAlign: "center"*/}
                            {/*}}>{this.props.loadingMessage}</span>*/}
                            {/*</div> :*/}
                            <div style={{marginTop: "10%", display: "flex"}}>
                                <div className="loginBlockLeft"
                                     style={{
                                         background: "#fff",
                                         boxShadow: "#333 0 0 2px 0",
                                         width: "40%",
                                         margin: "30px",
                                         height: "600px"
                                     }}>
                                    <div style={{
                                        fontSize: "30px",
                                        margin: "20px",
                                        textAlign: "center", fontWeight: "bold"
                                    }}><span>Scan QR Code</span>
                                        <div> & Pay</div>
                                    </div>

                                    <div style={{textAlign: "center", height: "325px", marginTop: "35px"}}>
                                            <span style={{
                                                fontSize: "25px",
                                                textAlign: "center"
                                            }}>{this.props.loadingMessage}</span>
                                        <div style={{textAlign: "center", height: "325px"}}>
                                            <canvas id="paytmQrForGiftCard"></canvas>
                                        </div>

                                    </div>
                                    <div style={{
                                        fontSize: "30px",
                                        textAlign: "center", fontWeight: "bold"
                                    }}>powered by
                                    </div>
                                    <img src="../../../img/paytm-logo.png"
                                         style={{
                                             height: "80px",
                                             width: "250px",
                                             margin: "auto",
                                             marginLeft: "65px"
                                         }}/>
                                    {/*<img src="../../../img/tap-icon.png"*/}
                                    {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                    {/*<div style={{*/}
                                    {/*padding: "10px 0",*/}
                                    {/*color: "#5e7e47",*/}
                                    {/*textAlign: "center",*/}
                                    {/*fontSize: "21px",*/}
                                    {/*fontWeight: "bold"*/}
                                    {/*}}>Tap to pay*/}
                                    {/*</div>*/}
                                </div>
                                {/*<div>*/}
                                <div style={{
                                    background: "lightgray",
                                    width: "5px",
                                    margin: "30px"
                                }}>
                                </div>
                                <div style={{
                                    fontSize: "20px",
                                    color: "white", background: "#5e7e47",
                                    padding: "13px", marginTop: "30%",
                                    marginLeft: "-7%",
                                    height: "75px",
                                    borderRadius: "50%",
                                    border: "10px solid white"
                                }}>OR
                                </div>
                                {/*</div>*/}
                                <div className="loginBlockRight"
                                     style={{
                                         background: "#fff",
                                         boxShadow: "#333 0 0 2px 0",
                                         margin: "30px",
                                         width: "40%",
                                         height: "600px"
                                     }}
                                     onClick={this.payByCardMachine.bind(this)}>
                                    <div style={{
                                        fontSize: "30px",
                                        fontWeight: "bold",
                                        margin: "20px",
                                        textAlign: "center"
                                    }}>
                                        <span>Credit Card</span>
                                        <div>/Debit Card</div>
                                    </div>
                                    <img src="../../../img/card-type.png"
                                         style={{margin: "10px", height: "43%", marginBottom: "33px"}}/>

                                    <div style={{
                                        padding: "10px",
                                        textAlign: "center",
                                        fontSize: "21px",
                                        fontWeight: "bold",
                                        background: "rgb(94, 126, 71)",
                                        color: "white",
                                        width: "83%",
                                        margin: "auto",
                                        borderRadius: "10px"
                                    }}>Tap to pay
                                    </div>
                                    {/*<img src="../../../img/ezetap-logo.png"
                                         style={{
                                             margin: "15px",
                                             height: "24%",
                                             marginLeft: "39px"
                                         }}/>*/}
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            );
        } else {
            return null;
        }
    }
}