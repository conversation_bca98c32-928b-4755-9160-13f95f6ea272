import React from "react";
import {connect} from "react-redux";
import * as PaymentActions from "../../actions/PaymentActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import * as CustomerActions from "../../actions/CustomerActions";
import appUtil from "../../AppUtil";
import * as UtilityActions from "../../actions/UtilityActions";
import {setCashPaymentOTPMessage} from "../../actions/PaymentActions";

@connect((store) => {
    return {
        showCashModal: store.paymentReducer.showCashModal,
        customer: store.customerReducer.customer,
        orderFailed: store.orderManagementReducer.orderFailed,
        isCashAmountExceeded: store.paymentReducer.isCashAmountExceeded,
        isCashAmountOTPNeeded: store.paymentReducer.isCashAmountOTPNeeded,
        cashPaymentOTPErrorMessage: store.paymentReducer.cashPaymentOTPErrorMessage,
        cashPaymentOTPSuccessMessage: store.paymentReducer.cashPaymentOTPSuccessMessage,
        cashOrderLoader: store.paymentReducer.cashOrderLoader
    };
})
export default class CashPaymentModal extends React.Component {

    constructor() {
        super();
        this.closeModal = this.closeModal.bind(this);
        this.cancelPayment = this.cancelPayment.bind(this);
        this.placeOrder = this.placeOrder.bind(this);
        this.resendVerification = this.resendVerification.bind(this);
        this.verifyOTPAndPlaceOrder = this.verifyOTPAndPlaceOrder.bind(this);
        this.verifyOTPAutoPlaceOrder = this.verifyOTPAutoPlaceOrder.bind(this);
    }

    closeModal() {
        this.props.dispatch(PaymentActions.setShowCashPaymentModal(false));
    }

    cancelPayment() {
        this.closeModal();
    }

    placeOrder() {
        if(this.props.customer.otpVerified || !this.props.isCashAmountOTPNeeded) {
            this.props.dispatch(OrderManagementActions.orderCheckout(1, null));
        }
    }

    resendVerification() {
        this.props.dispatch(PaymentActions.resendVerification(this.props.customer.contact));
    }

    verifyOTPAndPlaceOrder() {
        var otp = document.getElementById("otpInput").value;
        this.props.dispatch(PaymentActions.verifyOTP(this.props.customer.contact, otp))
    }

    verifyOTPAutoPlaceOrder() {
        let otp = document.getElementById("otpInput").value;
        if (otp.match(/^[0-9]\d{3}$/)) {
            this.props.dispatch(PaymentActions.verifyOTP(this.props.customer.contact, otp))
        }
    }

    componentWillMount() {
        // if (this.props.showCashModal && this.props.customer.otpVerified) {
        //     this.props.dispatch(setCashPaymentOTPMessage("OTP verified, Placing Order...", false));
        //     this.props.dispatch({type: "SET_CASH_ORDER_LOADER", payload: true});
        //     this.props.dispatch(OrderManagementActions.orderCheckout(1, null));
        // }
        this.props.dispatch(setCashPaymentOTPMessage(null, false));
    }

    render() {
        if (this.props.showCashModal) {
            return (
                <div class="modal">
                    <div class="modalBody">
                        <div className="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                            &times;
                        </div>
                        <p class="modalTitle">
                            Pay Cash
                        </p>
                        <div class="customizationSection productHead">


                            {!this.props.isCashAmountExceeded && this.props.isCashAmountOTPNeeded ? (
                                <div>
                                    <div className="payableAmount1Modal">
                                        <p>Cash To Pay</p><br/>
                                        <img style={{width: "51px", marginBottom: "-6px"}}
                                             src="../../../img/indian-ruppee-icon-white.png"/>
                                        <span style={{
                                            fontSize: "60px",
                                            margin: "0 0 0 15px",
                                            fontWeight: "bold"
                                        }}>{this.props.amountToPay}</span>
                                    </div>

                                    {this.props.cashOrderLoader ? (
                                            <div style={{textAlign: "center"}}>
                                                <p style={{fontSize: "21px"}}>Placing your order now.</p>
                                                <img src="../../../img/payment/payment-successful.gif"
                                                     style={{margin: "20px"}}/>
                                            </div>
                                        ) :
                                        (
                                            <div className="cashPaymentOTPSection">
                                                {this.props.cashPaymentOTPErrorMessage ? (
                                                    <div className="otpMessageError">
                                                        {this.props.cashPaymentOTPErrorMessage}
                                                    </div>
                                                ) : null}
                                                {this.props.cashPaymentOTPSuccessMessage ? (
                                                    <div className="otpMessageSuccess">
                                                        {this.props.cashPaymentOTPSuccessMessage}
                                                    </div>
                                                ) : null}

                                                {/*<div className="resendOTPButton" onClick={this.resendVerification.bind(this)}>
                                                Resend OTP
                                            </div>*/}

                                                {!this.props.customer.otpVerified &&
                                                <div>
                                                    <div className="contactContainer fieldContainer">
                                                        <input id="otpInput" type="number"
                                                               placeholder="Enter One Time Password"
                                                               maxLength="4"
                                                               autoComplete="off"
                                                               onKeyUp={this.verifyOTPAutoPlaceOrder.bind(this)}/>
                                                    </div>

                                                    <div className="verifyOTPButton"
                                                         onClick={this.verifyOTPAndPlaceOrder.bind(this)}>
                                                        Verify
                                                    </div>
                                                </div>
                                                }
                                            </div>)
                                    }

                                </div>
                            ) : null}


                            {this.props.isCashAmountExceeded ? (
                                <div>
                                    <div className="payableAmount1Modal">
                                        <p>Amount to Pay</p><br/>
                                        <img style={{width: "51px", marginBottom: "-6px"}}
                                             src="../../../img/indian-ruppee-icon-white.png"/>
                                        <span style={{
                                            fontSize: "60px",
                                            margin: "0 0 0 15px",
                                            fontWeight: "bold"
                                        }}>{this.props.amountToPay}</span>
                                    </div>

                                    <div style={{textAlign: "center"}}>
                                        <p style={{fontSize: "21px"}}>Order Amount is greater than 1000 Rs <br/>
                                            Kindly use Paytm to pay</p>
                                    </div>

                                    <div className="btn btn-default" onClick={this.closeModal.bind(this)}
                                         style={{margin: "75px"}}>
                                        Ok
                                    </div>
                                </div>
                            ) : null}

                            {!this.props.isCashAmountExceeded && !this.props.isCashAmountOTPNeeded ? (
                                <div>
                                    <div className="payableAmount1Modal">
                                        <p>Pay at the counter</p><br/>
                                        <img style={{width: "51px", marginBottom: "-6px"}}
                                             src="../../../img/indian-ruppee-icon-white.png"/>
                                        <span style={{
                                            fontSize: "60px",
                                            margin: "0 0 0 15px",
                                            fontWeight: "bold"
                                        }}>{this.props.amountToPay}</span>
                                    </div>


                                    {this.props.cashOrderLoader ? (
                                            <div style={{textAlign: "center"}}>
                                                <p style={{fontSize: "21px"}}>Placing your order now.</p>
                                                <img src="../../../img/payment/payment-successful.gif"
                                                     style={{margin: "20px"}}/>
                                            </div>
                                        ) :
                                        (
                                            <div className="cashPaymentOTPSection">
                                                <div style={{textAlign: "center"}}>
                                                    <p style={{fontSize: "21px"}}>To place order, Press Ok</p>
                                                </div>
                                                <div className="btn btn-default" onClick={this.placeOrder.bind(this)}
                                                     style={{margin: "75px"}}>
                                                    OK
                                                </div>
                                            </div>)
                                    }
                                </div>
                            ) : null}
                        </div>
                    </div>
                </div>
            )
        } else {
            return null;
        }
    }
}