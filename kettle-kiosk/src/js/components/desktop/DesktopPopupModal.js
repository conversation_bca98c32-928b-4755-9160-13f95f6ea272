import React from "react";
import {connect} from "react-redux";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import appUtil from "../../AppUtil";
import * as CustomerActions from "../../actions/CustomerActions";
import * as UtilityActions from "../../actions/UtilityActions";
import StorageUtils from "../../utils/StorageUtils";
import * as PaymentActions from "../../actions/PaymentActions";

@connect((store) => {
    return {
        showRedeemChaayosCashModal: store.outletMenuReducer.showRedeemChaayosCashModal,
        customer: store.customerReducer.customer,
        otpMessage: store.outletMenuReducer.otpMessage,
        showOtp: store.outletMenuReducer.showOtp,
        showOk: store.outletMenuReducer.showOk,
        popUpTitle: store.outletMenuReducer.popUpTitle,
        cart: store.cartManagementReducer.cart,
    };
})
export default class DesktopPopupModal extends React.Component {
    constructor(props) {
        super(props);
    }

    componentWillReceiveProps(nextProps) {
        this.state = {
            isChaayosCash: true
        };
        if (nextProps && nextProps.popUpTitle == "REDEEM CHAI") {
            this.setState({
                isChaayosCash: false
            });
        }
    }

    closeModal() {
        this.props.dispatch({type: "SET_OTP_MESSAGE", payload: null});
        this.props.dispatch({type: "SHOW_OTP", payload: false});
        this.props.dispatch(OutletMenuActions.setShowRedeemChaayosCashModal(false));
    }

    renderButtons = () => {
        let redeemChai = [];
        for (let i = 1; i <= this.getMinRedeemChai(this.props); i++) {
            redeemChai.push(
                <div key={i} className="loginBackButton" style={{width: "unset", padding: "10px", margin: "5px"}}
                     onClick={() => this.onSelectFreeChai(i)}>{i} Free Chai</div>)
        }
        return redeemChai;
    }

    checkRedeemChaiInCart(props) {
        let count = 0;
        props.cart.orderDetail.orders.forEach((orderItem) => {
            if(StorageUtils.getRedemptionChaiProductIds().indexOf(orderItem.productId) != -1) {
                count = count + orderItem.quantity;
            }
        });
        return count;
    }

    getMinRedeemChai(props) {
        return this.findMin(5, this.findMin(this.checkRedeemChaiInCart(props), parseInt(props.customer.loyalty/60, 10)));
    }

    findMin(input1, input2) {
        return input1 > input2 ? input2 : input1;

    }

    verifyOtp() {
        const otp = document.getElementById("otpEntered").value;
        if (otp.length == 4) {
            this.props.customer.otpEntered = otp;
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
            this.props.dispatch(PaymentActions.verifyOtpToRedeemChai(this.props))
        }
    }

    generateOtp() {
        this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
    }

    onSelectFreeChai(chaiCount) {
        this.props.dispatch({type: "SHOW_OK", payload: true});
        this.props.customer.chaiRedeemed = chaiCount;
        this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
        StorageUtils.setCustomerDetail(this.props.customer);
    }

    getChaiRedeemed() {
        return StorageUtils.getCustomerDetail().redeemChai;
    }

    render() {
        if (this.props.showRedeemChaayosCashModal) {
            return (
                <div className="modal gcPaymentModal">
                    <div className="modalBody" style={{top: "40%", padding: "20px"}}>
                        <div className="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                            &times;
                        </div>
                        <div className="modalTitle" style={{fontSize: "25px"}}>
                            {this.props.popUpTitle}
                            <span> {this.state.isChaayosCash ? " - " : ""} {this.getChaiRedeemed()}</span>
                        </div>
                        {!this.state.isChaayosCash ?
                            <div style={{display: "inline-block", textAlign: "center"}}>
                                {this.renderButtons()}
                            </div>
                            :
                            <div class="strip" style={{marginBottom:"75px"}}>Available Chaayos Cash <span>{this.props.customer.chaayosCash}</span></div>
                        }
                        {this.props.showOtp &&
                        <div style={{textAlign: "center", margin: "20px"}}>
                            <input id="otpEntered" type="tel" placeholder="Enter OTP here" maxLength="4"
                                   autoComplete="off" autoFocus="true" onKeyUp={() => this.verifyOtp()}/>
                        </div>
                        }
                        {this.props.otpMessage}
                        <div style={{textAlign: "center"}}>
                            {(this.props.showOk || this.state.isChaayosCash) &&
                            <div className="loginBackButton" style={{width: "unset", margin: "10px"}}
                                 onClick={() => this.generateOtp()}>Redeem
                            </div>
                            }
                            <div className="loginBackButton" style={{width: "unset", margin: "10px"}}
                                 onClick={() => this.closeModal()}>Cancel
                            </div>
                        </div>
                    </div>
                </div>
            )
        } else {
            return null
        }
    }
}