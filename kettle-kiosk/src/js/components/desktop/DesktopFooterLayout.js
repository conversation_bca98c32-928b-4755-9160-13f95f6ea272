/**
 * Created by Chaayos on 06-12-2016.
 */
import React from "react";
import { Link, browserHistory } from "react-router";

export default class DesktopAboutLayout extends React.Component {

    constructor(){
        super();
    }

    render (){
        return(
            <div>
                {/*<div style={{paddingTop:"50px"}}>
                    <div class="footerContainer">
                        <div class="footerContent">
                            <ul>
                                <li class="listItem"><Link to={"/about"} alt="About Chaayos">About Us</Link></li>
                                <li class="listItem"><Link to={"/contact"} alt="Contact Page">Contact Us</Link></li>
                                <li class="listItem"><Link to={"/terms"} alt="Terms and Conditions">Terms & Conditions</Link></li>
                            </ul>
                            <div class="socialLinkWrapper">
                                <a href="https://www.facebook.com/chaayos" target="_blank" alt="Chaayos at Facebook">
                                    <img src="../../../img/desktop/facebook.png" />
                                </a>
                                <a href="https://twitter.com/chaayos" target="_blank" alt="Chaayos Twitter page">
                                    <img src="../../../img/desktop/twitter.png" />
                                </a>
                                <a href="https://instagram.com/chaayos" target="_blank" alt="Chaayos Instagram Page">
                                    <img src="../../../img/desktop/instagram.png" />
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="footerStrip">
                        <div class="footerStripContent">
                            &copy; Chaayos, Sunshine Teahouse Pvt. Ltd. All rights Reserved. CIN: U55204DL2012PTC304447
                        </div>
                    </div>
                </div>*/}
            </div>
        )
    }
}