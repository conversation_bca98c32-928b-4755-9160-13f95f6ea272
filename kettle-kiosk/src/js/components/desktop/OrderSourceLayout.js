import React from "react";
import {connect} from "react-redux";
import * as CartManagementActions from "../../actions/CartManagementActions";
import StorageUtils from "../../utils/StorageUtils";
import * as CustomerActions from "../../actions/CustomerActions";
import {browserHistory} from "react-router";
import * as PaymentActions from "../../actions/PaymentActions";
import DesktopBannerHeader from "./DesktopBannerHeader";
import GiftCardPaymentModal from "./GiftCardPaymentModal";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import IdleTimer from 'react-idle-timer';
import appUtil from "../../AppUtil";


@connect((store) => {
    return {
        showOrderSourceModal: store.cartManagementReducer.showOrderSourceModal,
        customer: store.customerReducer.customer,
        otpMessage: store.outletMenuReducer.otpMessage,
        showOtp: store.outletMenuReducer.showOtp,
        showOk: store.outletMenuReducer.showOk,
        cart: store.cartManagementReducer.cart,
        setRedemptionDone: store.customerReducer.setRedemptionDone,
        selectedOrderSource: store.outletMenuReducer.selectedOrderSource,
        orderSourceMessage: store.outletMenuReducer.orderSourceMessage,
        beforeRedemptionCart: store.cartManagementReducer.beforeRedemptionCart
    };
})
export default class OrderSourceLayout extends React.Component {

    constructor(props) {
        super(props);
        this.submit = this.submit.bind(this);
        this.goBack = this.goBack.bind(this);
        this.state = {
            timeout: 1000 * 120,
            userLoggedIn: false,
            isTimedOut: false
        };

        this.idleTimer = null;
        this.onAction = this._onAction.bind(this);
        this.onActive = this._onActive.bind(this);
        this.onIdle = this._onIdle.bind(this);
    }

    _onAction(e) {
        console.log('user did something', e);
        this.setState({isTimedOut: false})
    }

    _onActive(e) {
        console.log('user is active', e);
        this.setState({isTimedOut: false})
    }

    _onIdle(e) {
        console.log('user is idle in order source', e);
        const isTimedOut = this.state.isTimedOut;
        if (isTimedOut) {
            console.log('timed out idle from order source');
            // navigate to cart menu after clearing cart and contact details
            this.props.dispatch(OrderManagementActions.startNewOrder());

        } else {
            this.setState({showModal: true});
            this.idleTimer.reset();
            this.setState({isTimedOut: true})
        }

    }

    submit(source) {
        this.props.dispatch({type: "SET_ORDER_SOURCE_MESSAGE", payload: null});
        this.props.dispatch(CartManagementActions.setOrderSource(source, this.props));
    }

    goBack() {
        StorageUtils.removeCustomerDetail();
        this.props.dispatch(CustomerActions.setCustomerDetail());
        this.props.dispatch(CartManagementActions.removeCoupon(false));
        this.props.dispatch({type: "SET_REDEMPTION_DONE", payload: false});
        browserHistory.push("/menu");
    }

    componentWillMount() {
        let chai = [];
        for (let i = 1; i <= this.getMinRedeemChai(this.props); i++) {
            chai.push({
                id: i,
                active: false
            });
        }
        this.state = {
            redeemChais: chai
        };
        if (this.props.customer) {
            this.props.customer.chaiRedeemed = 0;
        }
        this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
        this.props.dispatch({type: "SET_SELECTED_ORDER_SOURCE", payload: null});
        if (this.props.customer!=null
            && this.props.customer.eligibleForSignupOffer
                && this.props.dispatch(CartManagementActions.checkRedeemChaiInCart(this.props)) > 0
                    && !appUtil.checkOrderDetailsEmpty(this.props.cart)
                        && !this.props.cart.orderDetail.offerCode) {
            this.props.customer.chaiRedeemed = 1;
            this.props.customer.otpSource = "RedeemChai";
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
            this.props.dispatch({type: "SET_REDEMPTION_DONE", payload: true});
            if (this.props.customer.otpVerified) {
                this.props.dispatch(PaymentActions.addRedemptionToOrder(this.props, true));
            } else {
                this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
                this.props.dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: true});
                console.log("customer detail storage ", StorageUtils.getCustomerDetail());
            }
        }
    }

    renderButtons = () => {
        let redeemChai = [];
        for (let i = 0; i < this.state.redeemChais.length; i++) {
            if (this.state.redeemChais[i].active) {
                redeemChai.push(
                    <div key={i} className="redeemButton" style={{background: "#597e3a", color: "white"}}
                         onClick={() => this.onSelectFreeChai(i + 1)}>{i + 1}</div>)
            } else {
                redeemChai.push(
                    <div key={i} className="redeemButton"
                         onClick={() => this.onSelectFreeChai(i + 1)}>{i + 1}</div>)
            }
        }
        return redeemChai;
    }

    getMinRedeemChai(props) {
        return this.findMin(5, this.minInCart(props));
    }

    minInCart(props) {
        if (props.customer) {
            return this.findMin(this.props.dispatch(CartManagementActions.checkRedeemChaiInCart(this.props)), parseInt(props.customer.loyalty / 60, 10));
        }
    }

    checkRedeemChaiInCart() {
        return this.props.dispatch(CartManagementActions.checkRedeemChaiInCart(this.props));
    }

    findMin(input1, input2) {
        return input1 > input2 ? input2 : input1;
    }

    getPoints() {
        if(this.props.customer!=null && this.props.customer.loyalty!=null){
            return this.findMin(5, parseInt(this.props.customer.loyalty / 60, 10));
        }
    }

    verifyOtp() {
        const otp = document.getElementById("giftCardOtpEntered").value;
        if (otp.length == 4) {
            this.props.customer.otpEntered = otp;
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
            this.props.dispatch(PaymentActions.verifyOtpToRedeemChai(this.props))
        }
    }

    generateOtp() {
        this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
    }

    onSelectFreeChai(chaiCount) {
        this.state.redeemChais.map((chai) => {
            return chai.active = false;
        });
        this.state.redeemChais[this.state.redeemChais.findIndex((obj => obj.id == chaiCount))].active = true;
        this.props.dispatch({type: "SHOW_OK", payload: true});
        this.props.customer.chaiRedeemed = chaiCount;
        this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
        StorageUtils.setCustomerDetail(this.props.customer);
        if (!this.props.customer.otpVerified) {
            this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
            this.props.dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: true});
            this.props.customer.otpSource = "RedeemChai";
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
        } else {
            this.props.dispatch(PaymentActions.addRedemptionToOrder(this.props));
        }
    }

    getChaiRedeemed() {
        return StorageUtils.getCustomerDetail().redeemChai;
    }

    checkRedeemPoints() {
        return (this.props.customer!=null && this.props.customer.loyalty >= 60 && this.checkRedeemChaiInCart() == 0);
    }

    redeemLater() {
        this.props.dispatch({type: "SET_ORDER_SOURCE_MESSAGE", payload: null});
        this.props.dispatch({type: "SHOW_OK", payload: false});
        this.props.customer.redemptionDone = true;
        this.props.customer.chaiRedeemed = 0;
        this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
        StorageUtils.setCustomerDetail(this.props.customer);
        this.props.dispatch(PaymentActions.removeRedeemChai(this.props, true));
    }

    goBack(clearCart) {
        StorageUtils.removeCustomerDetail();
        this.props.dispatch(CustomerActions.removeCustomerDetail());
        this.props.dispatch(CartManagementActions.removeCoupon(false));
        if (clearCart == true) {
            this.props.dispatch(CartManagementActions.clearCart());
        }
        browserHistory.push("/menu");
    }

    skipSecondFreeChai() {
        if (this.props.selectedOrderSource != null) {
            this.props.dispatch({type: "SET_REDEMPTION_DONE", payload: true});
            browserHistory.push("/paymentModes");
        } else {
            this.props.dispatch({type: "SET_ORDER_SOURCE_MESSAGE", payload: "Please select your eating preference"});
        }
    }

    render() {
        let points = this.getPoints();
        return (
            <div>
                <IdleTimer
                    ref={ref => {
                        this.idleTimer = ref
                    }}
                    element={document}
                    onActive={this.onActive}
                    onIdle={this.onIdle}
                    onAction={this.onAction}
                    debounce={250}
                    timeout={this.state.timeout}/>
                <DesktopBannerHeader/>
                <div style={{
                    fontSize: "28px", textTransform: "capitalize", margin: "0 auto", padding: "20px", color: "#fff",
                    background: "#5e7e47", border: "#4a6238 1px solid"
                }}>
                    Hi {this.props.customer.name}
                    {/*<span style={{float: "right"}}>******{this.props.customer.contact.substr(6)}</span>*/}
                </div>
                <div class="customizationSection productHead"
                     style={{height: "1609px", textAlign: "center", background: "rgb(244, 244, 244)"}}>

                    <div style={{fontSize: "35px", margintop: "25px", color: "red"}}>
                        {this.props.orderSourceMessage}
                    </div>
                    <div>
                        <div
                            className={this.props.selectedOrderSource == "CAFE" ? "orderSourceBtn selected" : "orderSourceBtn"}
                            onClick={this.submit.bind(this, "CAFE")}>
                            {this.props.selectedOrderSource == "CAFE" ? (
                                <img src="/img/selectedEatHere.png"/>
                            ) : (
                                <img src="/img/unselectedEatHere.png"/>
                            )}
                            <br/>
                            Dine In
                        </div>
                        <div
                            className={this.props.selectedOrderSource == "TAKE_AWAY" ? "orderSourceBtn selected" : "orderSourceBtn"}
                            onClick={this.submit.bind(this, "TAKE_AWAY")}>
                            {this.props.selectedOrderSource == "TAKE_AWAY" ? (
                                <img src="/img/selectedTakeAway.png"/>
                            ) : (
                                <img src="/img/unselectedTakeAway.png"/>
                            )}
                            <br/>
                            Take Away
                        </div>
                    </div>

                    {!appUtil.checkOrderDetailsEmpty(this.props.cart) && !this.props.cart.orderDetail.offerCode && this.props.customer.eligibleForSignupOffer && this.checkRedeemChaiInCart() == 0 &&
                    <div style={{fontSize: "35px", width: "80%", margin: "auto"}}>
                        <span style={{display: "block", marginBottom: "30px"}}>You are eligible for second Free Chai. Please add Regular Desi chai to cart and enjoy</span>
                        <div className="loginBackButton"
                             style={{width: "40%", margin: "10px", background: "#597e3a", fontSize: "35px"}}
                             onClick={() => this.goBack()}>Edit Cart
                        </div>
                        {/*<div className="loginBackButton"*/}
                        {/*style={{width: "40%", margin: "10px", background: "#597e3a", fontSize: "35px"}}*/}
                        {/*onClick={() => this.skipSecondFreeChai()}>Skip*/}
                        {/*</div>*/}
                    </div>
                    }

                    {!appUtil.checkOrderDetailsEmpty(this.props.cart) && !this.props.cart.orderDetail.offerCode && this.props.customer.eligibleForSignupOffer && this.checkRedeemChaiInCart() > 0 &&
                    <div style={{fontSize: "35px", width: "80%", margin: "auto"}}>
                        <span style={{display: "block", marginBottom: "30px"}}>You were eligible for second Free Chai. We have redeemed 1 desi chai for you.</span>
                        {/*<div className="loginBackButton"*/}
                        {/*style={{width: "40%", margin: "10px", background: "#597e3a", fontSize: "35px"}}*/}
                        {/*onClick={() => this.goBack()}>Back To Menu*/}
                        {/*</div>*/}
                        {/*<div className="loginBackButton"*/}
                        {/*style={{width: "40%", margin: "10px", background: "#597e3a", fontSize: "35px"}}*/}
                        {/*onClick={() => this.skipSecondFreeChai()}>Skip*/}
                        {/*</div>*/}
                    </div>
                    }

                    {!appUtil.checkOrderDetailsEmpty(this.props.cart) && !this.props.cart.orderDetail.offerCode && !this.props.customer.eligibleForSignupOffer && this.minInCart(this.props) > 0 &&
                    <div>
                        {this.props.otpMessage}
                        <div style={{
                            fontSize: "35px",
                            background: "#597e3a",
                            padding: "20px",
                            marginBottom: "80px",
                            color: "white"
                        }}><span style={{marginRight: "35px"}}>Redeem your LoyalTea</span>
                            <span>Earned Points - {this.props.customer.loyalty}</span></div>

                        <div className="redeemContainer">
                            <div style={{
                                display: "inline-block",
                                textAlign: "center",
                                margin: "0px auto",
                                background: "#c4e0a0",
                                padding: "30px 20px",
                                borderRadius: "25px"
                            }}>
                                {this.renderButtons()}
                            </div>
                        </div>
                        <div style={{fontSize: "40px", margin: "40px"}}>You are
                            redeeming {this.props.customer.chaiRedeemed} desi
                            {(this.props.customer.chaiRedeemed > 1) && <span> chais!</span>}
                            {this.props.customer.chaiRedeemed == 1 && <span> chai!</span>}
                        </div>
                        {/*{this.props.showOtp &&*/}
                        {/*<div style={{*/}
                        {/*display: "flex",*/}
                        {/*margin: "0px auto",*/}
                        {/*fontSize: "35px",*/}
                        {/*width: "60%"*/}
                        {/*}}>*/}
                        {/*<input style={{*/}
                        {/*fontSize: "35px", borderRadius: "10px",*/}
                        {/*border: "1px solid",*/}
                        {/*marginRight: "30px",padding: "15px"*/}
                        {/*}} id="giftCardOtpEntered" type="tel"*/}
                        {/*placeholder="Enter OTP here" maxLength="4"*/}
                        {/*autoComplete="off" autoFocus="true"*/}
                        {/*onKeyUp={() => this.verifyOtp()}/>*/}
                        {/*<div style={{*/}
                        {/*background: "rgb(94, 126, 71)", padding: "20px",*/}
                        {/*borderRadius: "15px", color: "white"*/}
                        {/*}} onClick={() => this.generateOtp()}>Resend*/}
                        {/*</div>*/}
                        {/*</div>*/}
                        {/*}*/}
                        {/*{this.props.otpMessage}*/}
                        <div style={{
                            background: "lightgray",
                            height: "4px",
                            margin: "40px 0"
                        }}>
                            <span style={{
                                fontSize: "20px",
                                color: "black", background: "#f5f6f0",
                                padding: "10px"
                            }}>OR</span>
                        </div>
                        <div className="loginBackButton"
                             style={{width: "unset", margin: "10px", background: "#597e3a", fontSize: "35px"}}
                             onClick={() => this.redeemLater()}>Have It Later
                        </div>
                    </div>
                    }

                    {!appUtil.checkOrderDetailsEmpty(this.props.cart)
                        && !this.props.cart.orderDetail.offerCode
                            && !this.props.customer.eligibleForSignupOffer && this.checkRedeemPoints() ?
                        (<div>
                            <div style={{
                                fontSize: "35px",
                                background: "#597e3a",
                                padding: "20px",
                                marginBottom: "80px",
                                color: "white"
                            }}><span style={{marginRight: "35px"}}>Redeem your LoyalTea</span>
                                <span>Earned Points - {this.props.customer.loyalty}</span></div>

                            <div style={{fontSize: "35px", marginBottom: "30px"}}>
                                You can redeem max {points} Regular Desi chai. Please go back and add Desi chai.
                            </div>

                            <div className="loginBackButton"
                                 style={{width: "40%", margin: "10px", background: "#597e3a", fontSize: "35px"}}
                                 onClick={() => this.goBack()}>Edit Cart
                            </div>
                        </div>):null}

                </div>
                <GiftCardPaymentModal/>
            </div>

        )
    }
}