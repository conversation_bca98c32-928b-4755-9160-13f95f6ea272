import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux"
import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import { setAddressViewType, getLoyalteaScore, setCustomerDetail } from "../../actions/CustomerActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        loyalteaPending: store.customerReducer.loyalteaPending,
        customerLoyalteaScore: store.customerReducer.customerLoyalteaScore,
        customer: store.customerReducer.customer,
    };
})
export default class MobileProfileLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            customer:null
        };
        this.goToAddresses =this.goToAddresses.bind(this);
    }

    goToAddresses(){
        this.props.dispatch(setAddressViewType("PROFILE"));
        browserHistory.push("/addresses");
    }

    componentWillMount() {
        this.props.dispatch(getLoyalteaScore());
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"account",device:"desktop",custom:true});
        this.props.dispatch(setCustomerDetail());
    }

    render() {

        return (
            <div>
                <div class="colouredHead">
                    <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                <div class="desktopPageContainer">
                    <div class="mobileProfileHead">
                        {this.props.customer!=null?(
                            <div>
                                <img class="profilePic" src="../../../img/profilePic.png" />
                                <div class="userName">{this.props.customer.name}</div>
                                <div class="userContact">{this.props.customer.contact}</div>
                                <div class="userEmail">{this.props.customer.email}</div>
                            </div>
                        ):(
                            <div>
                                <img class="profilePic" src="../../../img/profilePic.png" />
                                <div class="userName" style={{background:"#ddd", padding:"5px", margin:"auto", width:"100px", marginTop:"15px", marginBottom:"10px"}}></div>
                                <div class="userContact" style={{background:"#ddd", padding:"3px", margin:"auto", width:"70px", marginBottom:"5px"}}></div>
                                <div class="userEmail" style={{background:"#ddd", padding:"2px", margin:"auto", width:"90px", marginBottom:"5px"}}></div>
                            </div>
                        )}

                    </div>
                    <div class="profileCard">
                        {this.props.loyalteaPending?(
                            <div class="loader load8"></div>
                        ):(
                            this.props.customerLoyalteaScore!=null?(
                                <div class="loyalteaScore">{this.props.customerLoyalteaScore}</div>
                            ):(
                                <div style={{color:"red",fontSize:"14px", textAlign:"center"}}>Error in getting LoyalTea score.</div>
                            )
                        )}
                        <div class="cardTitle text-center">LoyalTea Points</div>
                    </div>
                    <div class="profileCard">
                        <div class="cardTitle">My Addresses</div>
                        <div class="cardFooter" onClick={this.goToAddresses.bind(this)}>
                            <img src="../../../img/rightArrow.svg" /> View your addresses
                        </div>
                    </div>
                </div>
                <DesktopFooterLayout />
            </div>
        )
    }
}
