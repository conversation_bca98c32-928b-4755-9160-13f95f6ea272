import React from "react";
import {connect} from "react-redux";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import appUtil from "../../AppUtil";
import * as CustomerActions from "../../actions/CustomerActions";
import * as UtilityActions from "../../actions/UtilityActions";
import StorageUtils from "../../utils/StorageUtils";

@connect((store) => {
    return {
        cart: store.cartManagementReducer.cart,
        offerApplied: store.cartManagementReducer.offerApplied,
        offerErrorCode: store.cartManagementReducer.offerErrorCode,
        offerErrorMessage: store.cartManagementReducer.offerErrorMessage,
        showCouponModal: store.outletMenuReducer.showCouponModal,
        couponLogin: store.outletMenuReducer.couponLogin,
        otpResendSeconds: store.customerReducer.otpResendSeconds,
        showContactSection: store.customerReducer.showContactSection,
        isLogin: store.customerReducer.isLogin,
        getName: store.customerReducer.getName,
        getEmail: store.customerReducer.getEmail,
        loginCustomerPending: store.customerReducer.loginCustomerPending,
        signupCustomerPending: store.customerReducer.signupCustomerPending,
        lookupCustomerPending: store.customerReducer.lookupCustomerPending,
        customer: store.customerReducer.customer,
        couponApplying: store.cartManagementReducer.couponApplying,
        unit:store.outletMenuReducer.unit,
        giftCardOffer: store.paymentReducer.giftCardOffer
    };
})
export default class DesktopCouponModal extends React.Component {

    constructor() {
        super();
        this.state = {
            couponCode: "",
            contactMsg: "",
            nameMsg: "",
            otpMsg: "",
            emailMsg: ""
        };
        this.closeModal = this.closeModal.bind(this);
        this.handleChange = this.handleChange.bind(this);
        this.applyCoupon = this.applyCoupon.bind(this);
        this.removeCoupon = this.removeCoupon.bind(this);
        this.removeCustomer = this.removeCustomer.bind(this);
    }

    applyCoupon() {
        this.props.dispatch(CartManagementActions.applyCoupon(this.state.couponCode, false));
    }

    removeCoupon() {
        this.props.dispatch(CartManagementActions.removeCoupon(false));
    }

    removeCustomer() {
        StorageUtils.removeCustomerDetail();
        this.props.dispatch(CustomerActions.removeCustomerDetail());
        var cart = {...this.props.cart};
        cart.customerId = null;
        if (cart.orderDetail != null) {
            cart.orderDetail.webCustomerId = null;
        }
        this.props.dispatch({type: "SET_COUPON_LOGIN", payload: false});
        this.props.dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
        this.props.dispatch({type: "SET_LOGIN_REJECTED"});
        this.props.dispatch({type: "SET_SIGNUP_REJECTED"});
    }

    closeModal() {
        this.props.dispatch(OutletMenuActions.setShowCouponModal(false));
    }

    handleChange(event) {
        this.setState({couponCode: event.target.value.toUpperCase()});
    }

    loginUser(e) {
        e.preventDefault();
        var contact = document.getElementById("userContactInput").value;
        if (!appUtil.checkEmpty(contact) && appUtil.validContact(contact)) {
            this.props.dispatch(CustomerActions.loadGiftCards(this.props));
            this.props.dispatch(CustomerActions.lookupCustomer(contact, this.props.couponLogin, this.state.couponCode));
        } else {
            this.setState({contactMsg: "Please enter valid contact number!"});
            //this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "info"));
        }
    }

    verifyUser(e) {
        e.preventDefault();
        var otp = document.getElementById("otpInput").value;
        var name = null;
        var email = null;
        if (this.props.getName) {
            name = document.getElementById("nameInput").value;
        }
        if (this.props.getEmail) {
            email = document.getElementById("emailInput").value;
        }
        if (appUtil.checkEmpty(otp)) {
            this.setState({otpMsg: "Please enter one time password received on your mobile!"});
            //this.props.dispatch(UtilityActions.showPopup("Please enter one time password received on your mobile!", "info"));
        } else if (this.props.getName && appUtil.checkEmpty(name)) {
            this.setState({nameMsg: "Please enter name!"});
            //this.props.dispatch(UtilityActions.showPopup("Please enter name!", "info"));
        } else if (this.props.getEmail && !appUtil.validEmail(email)) {
            this.setState({emailMsg: "Please enter valid email address!"});
            //this.props.dispatch(UtilityActions.showPopup("Please enter valid email address!", "info"));
        } else {
            if (this.props.isLogin) {
                this.props.dispatch(CustomerActions.loginCustomer(name, email, otp, this.props.getName, this.props.getEmail, this.props.customer.contact, this.props.redirectTo,
                    true, this.state.couponCode));
            } else {
                this.props.dispatch(CustomerActions.signUpCustomer(name, email, otp, this.props.customer.contact,
                    this.props.redirectTo, false, true, this.state.couponCode));
            }
        }
    }

    resendVerification() {
        this.props.dispatch(CustomerActions.resendVerification(this.props.customer.contact));
    }

    editContact() {
        this.props.dispatch(CustomerActions.resetLogin());
    }

    resetCouponModal() {
        var couponCode = !appUtil.checkOrderDetailsEmpty(this.props.cart) ? this.props.cart.orderDetail.offerCode : "";
        this.setState({couponCode: (couponCode != null ? couponCode : "")});
        this.removeCustomer();
        this.props.dispatch({
            type: "OFFER_ERROR",
            payload: {code: 0, message: null}
        });
    }

    componentWillMount() {
        var couponCode = !appUtil.checkOrderDetailsEmpty(this.props.cart) ? this.props.cart.orderDetail.offerCode : "";
        this.setState({couponCode: couponCode});
    }

    componentWillUpdate(nextProps, nextState) {
        if (this.props.showCouponModal == false && nextProps.showCouponModal == true) {
            this.resetCouponModal();
        }
    }

    render() {
        var resendString = "";
        var min = 0, sec = 0;
        if (this.props.otpResendSeconds > 0) {
            min = parseInt(this.props.otpResendSeconds / 60);
            sec = this.props.otpResendSeconds % 60;
            min > 0 ? resendString += min + " min " : " ";
            sec > 0 ? resendString += sec + " sec" : "";
        }

        var offerCode = (!appUtil.checkOrderDetailsEmpty(this.props.cart)) ? this.props.cart.orderDetail.offerCode : null;

        if (this.props.showCouponModal) {
            return (
                <div class="modal">
                    <div class="modalBody">
                        <div class="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                            &times;
                        </div>
                        <p class="modalTitle">
                            Add Coupon
                        </p>
                        <div class="customizationSection productHead">
                            {!this.props.couponLogin ? (
                                <div>
                                    {offerCode != null ? (
                                        <div class="contactContainer" style={{padding: "15px", fontSize: "21px"}}>
                                            {this.props.cart.orderDetail.offerCode}
                                            <div class="removeCouponBtn" onClick={this.removeCoupon.bind(this)}>Remove
                                            </div>
                                        </div>
                                    ) : (
                                        <div>
                                            <div class="contactContainer">
                                                <input id="couponCode" type="text" placeholder="coupon code here"
                                                       autoComplete="off" style={{textTransform: "uppercase"}}
                                                       autoFocus="true"
                                                       value={this.state.couponCode != null ? this.state.couponCode : ""}
                                                       onKeyUp={this.handleChange.bind(this)}/>
                                            </div>

                                            {!this.props.couponApplying ? (
                                                <div>
                                                    <div class="btn btn-default couponBtn"
                                                         onClick={this.closeModal.bind(this)}>
                                                        Cancel
                                                    </div>
                                                    <div class="btn btn-primary couponBtn"
                                                         onClick={this.applyCoupon.bind(this)}>
                                                        Apply
                                                    </div>
                                                </div>
                                            ) : (
                                                <div class="loaderWrapper">
                                                    <div class="load8 loader"></div>
                                                    <p class="loaderMessage">Applying offer please wait.</p>
                                                </div>
                                            )
                                            }

                                            {(this.props.offerErrorMessage != null && this.props.offerErrorCode != 101) ? (
                                                <div style={{
                                                    textAlign: "center",
                                                    padding: "5px",
                                                    color: "red"
                                                }}>{this.props.offerErrorMessage}</div>
                                            ) : null}
                                            {/*{this.props.customer != null ? (
                                                <div style={{textAlign:"center", padding:"5px", cursor:"pointer", textDecoration:"underline", color:"#5e7e47"}}
                                                     onClick={this.removeCustomer.bind(this)}>
                                                    Try another contact number.
                                                </div>
                                            ):null}*/}
                                        </div>
                                    )}
                                </div>
                            ) : null}
                            {this.props.couponLogin == true ? (
                                <div>
                                    <div class="alert alert-info">Coupon is applicable for registered customers only.
                                    </div>
                                    {this.props.showContactSection ? (
                                        <div class="loginContainer">
                                            <form name="lookupForm" action="#" onSubmit={this.loginUser.bind(this)}>
                                                {/*<div class="headLine">Login</div>*/}
                                                <div class="loginSectionTagline">Login with your Mobile No.</div>
                                                <div class="contactContainer">
                                                    <input id="userContactInput" type="tel"
                                                           placeholder="Enter Mobile No." maxLength="10"
                                                           autoComplete="off"/>
                                                </div>
                                                {this.state.contactMsg.length > 0 ? (
                                                    <div>{this.state.contactMsg}</div>
                                                ) : null}
                                                {this.props.lookupCustomerPending ? (
                                                    <div class="loaderWrapper">
                                                        <div class="load8 loader"></div>
                                                        <p class="loaderMessage">Verifying contact number</p>
                                                    </div>
                                                ) : (
                                                    <div class="btn btn-primary" style={{marginTop: '16px'}}
                                                         onClick={this.loginUser.bind(this)}>
                                                        Submit
                                                    </div>
                                                )}
                                                <div>
                                                    <span class="resendLink"
                                                          onClick={this.resetCouponModal.bind(this)}>Back</span>
                                                </div>
                                            </form>
                                        </div>
                                    ) : (
                                        <div class="loginContainer">
                                            <form name="verificationForm" action="#"
                                                  onSubmit={this.verifyUser.bind(this)}>
                                                <div class="headLine">Verification</div>
                                                <div class="loginSectionTagline">An OTP was sent
                                                    to {this.props.customer && this.props.customer.contact}
                                                    <span classe="resendLink" style={{marginLeft: "20px"}}
                                                          onClick={this.editContact.bind(this)}>Edit</span>
                                                </div>
                                                {this.props.getName ? (
                                                    <div>
                                                        <div class="contactContainer">
                                                            <input id="nameInput" style={{textTransform: "capitalize"}}
                                                                   type="text"
                                                                   placeholder="Enter your name" maxLength="70"
                                                                   autoComplete="off"/>
                                                        </div>
                                                        {this.state.nameMsg.length > 0 ? (
                                                            <div style={{
                                                                color: "red",
                                                                padding: "5px"
                                                            }}>{this.state.nameMsg}</div>
                                                        ) : null}
                                                    </div>
                                                ) : null}
                                                {this.props.getEmail ? (
                                                    <div>
                                                        <div class="contactContainer">
                                                            <input id="emailInput" type="email"
                                                                   placeholder="Enter your email" maxLength="100"
                                                                   autoComplete="off"/>
                                                        </div>
                                                        {this.state.emailMsg.length > 0 ? (
                                                            <div style={{
                                                                color: "red",
                                                                padding: "5px"
                                                            }}>{this.state.emailMsg}</div>
                                                        ) : null}
                                                    </div>
                                                ) : null}
                                                <div class="contactContainer">
                                                    <input id="otpInput" type="number"
                                                           placeholder="Enter One Time Password" maxLength="10"
                                                           autoComplete="off"/>
                                                </div>
                                                {this.state.otpMsg.length > 0 ? (
                                                    <div style={{color: "green"}}>{this.state.otpMsg}</div>
                                                ) : null}
                                                {(this.props.signupCustomerPending || this.props.loginCustomerPending) ? (
                                                    <div class="loaderWrapper">
                                                        <div class="load8 loader"></div>
                                                        <p class="loaderMessage">Please wait while we sign you in.</p>
                                                    </div>
                                                ) : (
                                                    <div>
                                                        <div class="btn btn-primary" style={{marginTop: '16px'}}
                                                             onClick={this.verifyUser.bind(this)}>
                                                            Verify
                                                        </div>
                                                        <div class="resendText">
                                                            {this.props.otpResendSeconds <= 0 ? (
                                                                <div>
                                                                    Didn't receive?
                                                                    <span class="resendLink"
                                                                          onClick={this.resendVerification.bind(this)}>Resend</span>
                                                                </div>
                                                            ) : (
                                                                <div>
                                                                    Try again in {resendString}.
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </form>
                                        </div>
                                    )}
                                    {(this.props.offerErrorMessage != null && this.props.offerErrorCode == 101) ? (
                                        <div style={{
                                            textAlign: "center",
                                            padding: "5px",
                                            color: "red"
                                        }}>{this.props.offerErrorMessage}</div>
                                    ) : null}
                                </div>
                            ) : null}
                        </div>
                    </div>
                </div>
            )
        } else {
            return null;
        }
    }
}