/**
 * Created by Chaayos on 09-12-2016.
 */
import React from "react";
import {connect} from "react-redux";
import {browserHistory} from "react-router";
import appUtil from "../../AppUtil";
import * as UtilityActions from "../../actions/UtilityActions";
import * as PaymentActions from "../../actions/PaymentActions";
import * as CustomerActions from "../../actions/CustomerActions";
import * as CartManagementActions from "../../actions/CartManagementActions";
import trackUtils from "../../utils/TrackUtils";
import StorageUtils from "../../utils/StorageUtils";
import DesktopPaytmQRCodeModal from "./DesktopPaytmQRCodeModal";
import DesktopBannerHeader from "./DesktopBannerHeader";
import EzetapPaymentModal from "./EzetapPaymentModal";
import AGSPaymentModal from "./AGSPaymentModal";
import CashPaymentModal from "./CashPaymentModal";
import GiftCardPurchaseModal from "./GiftCardPurchaseModal";
import GiftCardPaymentModal from "./GiftCardPaymentModal";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import DesktopPopupModal from "./DesktopPopupModal";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import IdleTimer from 'react-idle-timer';


@connect((store) => {
    return {
        cart: store.cartManagementReducer.cart,
        giftCardCart: store.cartManagementReducer.giftCardCart,
        allGiftCards: store.paymentReducer.allGiftCards,
        unit: store.outletMenuReducer.unit,
        sessionKey: store.customerReducer.sessionKey,
        deviceKey: store.customerReducer.deviceKey,
        criteria: store.localityReducer.criteria,
        orderStatusTimeouts: store.orderManagementReducer.orderStatusTimeouts,
        customer: store.customerReducer.customer,
        paymentLoader: store.paymentReducer.paymentLoader,
        ezetapInterval: store.paymentReducer.ezetapInterval,
        agsInterval: store.paymentReducer.agsInterval,
        selectivePaymentMode: store.paymentReducer.selectivePaymentMode,
        cardPaymentMode: store.paymentReducer.cardPaymentMode,

        showPopupMessage: store.utilityReducer.showPopupMessage,
        showFullPageLoader: store.utilityReducer.showFullPageLoader,
        messageText: store.utilityReducer.messageText,
        loaderMessage: store.utilityReducer.loaderMessage,
        messageType: store.utilityReducer.messageType,
        showPrompt: store.utilityReducer.showPrompt,
        promptMessage: store.utilityReducer.promptMessage,
        promptSuccess: store.utilityReducer.promptSuccess,
        promptDismiss: store.utilityReducer.promptDismiss,
        showPaytmGIFLoader: store.utilityReducer.showPaytmGIFLoader,
        hasAvailRedeemChaayos: store.paymentReducer.hasAvailRedeemChaayos,
        showOtp: store.paymentReducer.showOtp,
        setRedemptionDone: store.customerReducer.setRedemptionDone,
        beforeRedemptionCart: store.cartManagementReducer.beforeRedemptionCart,
        showGiftCardPaymentModal: store.paymentReducer.showGiftCardPaymentModal,
        giftCardOffer: store.paymentReducer.giftCardOffer,
        otpMessage: store.outletMenuReducer.otpMessage,
        couponCode: store.customerReducer.couponCode,
        paytmQRTimeout: store.paymentReducer.paytmQRTimeout,
        paytmQRInterval: store.paymentReducer.paytmQRInterval,
        timerDataMin: store.paymentReducer.timerDataMin,
        timerDataSec: store.paymentReducer.timerDataSec,
        secRemaining: store.paymentReducer.secRemaining,
        timerInterval: store.paymentReducer.timerInterval,
        stopTimer: store.paymentReducer.stopTimer,
        showPayModesForGiftCard: store.paymentReducer.showPayModesForGiftCard

    };
})
export default class DesktopPaymentModesLayout extends React.Component {
    constructor() {
        super();
        this.state = {
            timeout: 1000 * 420,
            userLoggedIn: false,
            isTimedOut: false
        };
        this.payByRazorPay = this.payByRazorPay.bind(this);
        this.payByCash = this.payByCash.bind(this);
        this.payByPaytm = this.payByPaytm.bind(this);
        this.payByEzetap = this.payByEzetap.bind(this);
        this.payByAGSCardMachine = this.payByAGSCardMachine.bind(this);
        this.payByCardMachine = this.payByCardMachine.bind(this);
        this.goBack = this.goBack.bind(this);
        this.idleTimer = null;
        this.onAction = this._onAction.bind(this);
        this.onActive = this._onActive.bind(this);
        this.onIdle = this._onIdle.bind(this);

        this.intervalHandle;
        this.initializeTimer = this.initializeTimer.bind(this);
        this.handleChange = this.handleChange.bind(this);
        this.startCountDown = this.startCountDown.bind(this);
        this.tick = this.tick.bind(this);
    }

    initializeTimer(){
        this.props.dispatch(PaymentActions.initializeTimer());
    }

    _onAction(e) {
        console.log('user did something', e);
        this.setState({isTimedOut: false})
    }

    _onActive(e) {
        console.log('user is active', e);
        this.setState({isTimedOut: false})
    }

    _onIdle(e) {
        console.log('user is idle from payment modes', e);
        const isTimedOut = this.state.isTimedOut;
        if (isTimedOut) {
            console.log('timed out idle from payment modes');
            // navigate to cart menu after clearing cart and contact details
            clearInterval(this.props.paytmQRInterval);
            clearTimeout(this.props.paytmQRTimeout);
            this.props.dispatch(OrderManagementActions.startNewOrder());

        } else {
            this.setState({showModal: true});
            this.idleTimer.reset();
            this.setState({isTimedOut: true})
        }

    }

    handleChange(event) {
        this.props.dispatch({type: "SET_TIMER_MIN", payload: event.target.value});
    }

    tick() {
        console.log("this.props.stopTimer:: "+ this.props.stopTimer);
        if(this.props.stopTimer){
            console.log("this.props.stopTimer:: "+ this.intervalHandle);
            clearInterval(this.intervalHandle);
        }
        console.log("secs remaining:: "+ this.props.secRemaining);
        console.log("mins remaining:: "+ this.props.timerDataMin);
        console.log("sec remaining:: "+ this.props.timerDataSec);
        this.props.dispatch({type: "SET_TIMER_MIN", payload: Math.floor(this.props.secRemaining / 60)});
        this.props.dispatch({type: "SET_TIMER_SEC", payload: this.props.secRemaining - (this.props.timerDataMin * 60)});
        console.log("timerDataMin " + this.props.timerDataMin);
        console.log("timerDataSec " + this.props.timerDataSec);
        if (this.props.timerDataMin === 0 && this.props.timerDataSec === 0) {
            console.log("Closing the pop up!!");
            clearInterval(this.props.timerInterval);
            this.props.dispatch({type: "SET_SHOW_AGS_MODAL", payload: false});
            this.initializeTimer();
        }
        let secRemain = this.props.secRemaining;
        secRemain = secRemain -1;
        console.log("secRemain minus 1:: "+ secRemain);
        this.props.dispatch({type: "SET_SEC_REMAINING", payload: secRemain});
    }

    startCountDown() {
        this.initializeTimer();
        this.intervalHandle = setInterval(this.tick, 1000);
        this.props.dispatch({type: "SET_TIMER_INTERVAL", payload: this.intervalHandle});
        this.props.dispatch({type: "SET_STOP_TIMER", payload: false});
    }

    payByRazorPay(type) {
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        browserHistory.push("/payProcess");
        this.props.dispatch(PaymentActions.payByRazorPay(this.props.cart, type, this.props.orderStatusTimeouts));
    }

    payByPaytm() {
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        browserHistory.push("/payProcess");
        this.props.dispatch(PaymentActions.payByPaytm(this.props.cart));
    }

    payByPaytmQR() {
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        //browserHistory.push("/payProcess");
        this.props.dispatch(PaymentActions.payByPaytmQR());
    }

    payByCash() {
        console.log('Enter pay By cash');
        let amount = this.props.cart.orderDetail.transactionDetail.paidAmount;
        if (amount > 1000) {
            console.log('this is amount greater than 1000');
            this.props.dispatch(PaymentActions.setShowCashPaymentModal(true, true, false));
        } else {
            console.log('this is less greater than 1000');
            if (amount > 200 && !this.props.customer.otpVerified) {
                console.log('OTP needed');
                this.props.dispatch(PaymentActions.setShowCashPaymentModal(true, false, true));
                this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
            } else {
                console.log('OTP not needed');
                this.props.dispatch(PaymentActions.setShowCashPaymentModal(true, false, false));
            }
        }
    }

    payByGiftCard() {
        if (this.canPayViaGiftCard()) {
            if (!this.props.customer.otpVerified) {
                this.props.customer.otpSource = "GiftCard";
                this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
                StorageUtils.setCustomerDetail(this.props.customer);
                this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
                this.props.dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: true});
            } else {
                this.props.dispatch(PaymentActions.addGiftCardToSettle(this.props, true));
            }
        } else {
            this.props.dispatch(PaymentActions.calculateLowBalance(this.props));
        }
    }

    purchaseGiftCard(id, amount) {
        this.props.dispatch(UtilityActions.showFullPageLoader("Purchasing gift card..."));
        let newCart = {...this.props.cart};
        newCart.orderDetail = {...this.props.cart.orderDetail};
        newCart.orderDetail.orders = [];
        newCart.orderDetail.transactionDetail = {...this.props.cart.orderDetail.transactionDetail};
        newCart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
        newCart.orderDetail.settlements = {...this.props.cart.orderDetail.settlements};
        this.props.customer.giftCardAmount = amount;

        let newItem = {
            addons: [],
            amount: 0,
            billType: "ZERO_TAX",
            cardType: "ECARD",
            code: "GIFT_CARD",
            complimentaryDetail: {
                isComplimentary: false,
                reason: null,
                reasonCode: null
            },
            dimension: "None",
            discountDetail: appUtil.getEmptyDiscountObj(),
            hasBeenRedeemed: false,
            isCardValid: true,
            isCombo: false,
            itemId: 1,
            originalTax: 0,
            price: 0,
            productId: 0,
            productName: "",
            productType: 9,
            quantity: 1,
            recipeId: 301,
            tax: 0,
            taxes: [],
            totalAmount: 0
        };
        this.props.allGiftCards.map((card) => {
            if (card.id == id) {
                // card.amount += amount;
                card.quantity = 1;
                newItem.productId = card.id;
                newItem.recipeId = card.recipeId;
                newItem.amount = card.amount;
                newItem.price = card.amount;
                newItem.productName = card.name;
                newItem.recipeId = card.recipeId;
                newItem.totalAmount = card.amount;
            }
        });
        newCart.orderDetail.transactionDetail.paidAmount = amount;
        newCart.orderDetail.transactionDetail.taxableAmount = amount;
        newCart.orderDetail.transactionDetail.totalAmount = amount;
        newCart.orderDetail.source = this.props.cart.orderDetail.source;
        newCart.orderDetail.settlements = [];
        newCart.orderDetail.brandId = 1; //for chaayos

        newCart.orderDetail.orders.push(newItem);
        this.props.dispatch({type: "GIFT_CARD_CART", payload: newCart});
        StorageUtils.setGiftCardCartDetails(newCart);
        this.props.dispatch(PaymentActions.createGiftCardCart(this.props, newCart));
    }

    renderGiftCards() {
        let view = [];
        // if(this.props.allGiftCards.length > 0) {
        let i = 0;
        this.props.allGiftCards.forEach((u) => {
            // if ((u.amount + this.props.customer.giftCardBalance) > this.props.cart.orderDetail.transactionDetail.paidAmount) {
            i += 1;
            if (u.offer > 0) {
                view.push(
                    <div key={i} className="giftCardOffer" onClick={() => {
                        this.purchaseGiftCard(u.id, u.amount);
                    }}>

                        <div style={{fontSize: "40px"}}>Add</div>
                        <div style={{fontSize: "50px"}}>{u.amount}</div>
                        <div style={{fontSize: "40px"}}>get</div>
                        <div style={{
                            background: "#597e3a",
                            padding: "20px",
                            borderRadius: "20px",
                            color: "white",
                            marginTop: "20px",
                            fontSize: "40px"
                        }}>
                            <span>extra</span>
                            <div>{u.offer}</div>
                        </div>
                    </div>
                );
            } else {
                view.push(
                    <div style={{width: "35%"}} key={i} className="giftCardOffer" onClick={() => {
                        this.purchaseGiftCard(u.id, u.amount);
                    }}>
                        <div style={{fontSize: "40px", marginTop: "80px"}}>Add</div>
                        <div style={{fontSize: "50px"}}>{u.amount}</div>
                    </div>
                );
            }

            // }
        });
        // }
        return view;
    }

    payByCardMachine() {
        console.log("Card Payment type is :: ", this.props.cardPaymentMode);
        if(this.props.cardPaymentMode === 'EZETAP'){
            this.payByEzetap();
        }
        if(this.props.cardPaymentMode === 'AGS'){
            this.payByAGSCardMachine();
        }
    }

    payByEzetap() {
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        this.props.dispatch(PaymentActions.payWithEzetap());
    }

    payByAGSCardMachine() {
        this.startCountDown();
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        this.props.dispatch(PaymentActions.payWithAGSCardMachine());
    }

    openDialog() {
        this.props.dispatch(OutletMenuActions.setShowCouponModal(true));
    }

    openRedeemChaayosCashModal(isChaayosCash) {
        this.props.dispatch(OutletMenuActions.setShowRedeemChaayosCashModal(true));
        if (isChaayosCash) {
            this.props.dispatch({type: "POP_UP_TITLE", payload: "CHAAYOS CASH"});
        } else {
            this.props.dispatch({type: "POP_UP_TITLE", payload: "REDEEM CHAI"});
        }
    }

    checkRedeemAvailable(props) {
        return (!props.customer.availChaayosCash && props.customer.chaiRedeemed == 0 && props.customer.loyalty > 60 && this.checkRedeemChaiInCart(props) > 0);
    }

    checkRedeemChaiInCart(props) {
        let count = 0;
        props.cart.orderDetail.orders.forEach((orderItem) => {
            if (StorageUtils.getRedemptionChaiProductIds().indexOf(orderItem.productId) != -1) {
                count = count + orderItem.quantity;
            }
        });
        return count;
    }

    getMinRedeemChai(props) {
        return this.findMin(5, this.findMin(this.checkRedeemChaiInCart(props), parseInt(props.customer.loyalty / 60), 10));
    }

    findMin(input1, input2) {
        return input1 > input2 ? input2 : input1;

    }

    goBack(clearCart) {
        StorageUtils.removeCustomerDetail();
        this.props.dispatch(CustomerActions.setCustomerDetail());
        this.props.dispatch(CartManagementActions.removeCoupon(false));
        this.props.dispatch({type: "SET_REDEMPTION_DONE", payload: false});
        this.props.dispatch({type: "SET_COUPON", payload: null});
        if (clearCart == true) {
            this.props.dispatch(CartManagementActions.clearCart());
        }
        browserHistory.push("/menu");
    }

    componentWillMount() {
        this.props.dispatch({type: "SET_OTP_MESSAGE", payload: null});
        window.scrollTo(0, 0);
        if (appUtil.checkEmpty(this.props.cart)) {
            browserHistory.push("/menu");
        } else if (appUtil.checkEmpty(this.props.cart.sessionId)) {
            browserHistory.push("/login");
        } else {
            var len = 0;
            this.props.cart.orderDetail.orders.map((orderItem) => {
                if (orderItem.productId != 1043 && orderItem.productId != 1044) {
                    len++;
                }
            });
            if (len == 0) {
                browserHistory.push("/menu");
            }
            if (this.props.cart.orderDetail.transactionDetail == null || this.props.cart.orderDetail.transactionDetail.paidAmount < 1) {
                if (this.props.customer.chaiRedeemed > 0) {
                    // checkout order by cash and then navigate to order success page
                    this.props.dispatch(UtilityActions.showFullPageLoader("We are placing your order. Please Wait..."));
                    this.props.dispatch(OrderManagementActions.orderCheckout(1, null));
                } else {
                    browserHistory.push("/menu");

                }
            }
        }
        this.props.dispatch(PaymentActions.setShowEzetapPaymentModal(false, null, this.props.ezetapInterval));
        this.props.dispatch(PaymentActions.setShowAGSPaymentModal(false, null, this.props.agsInterval));
        this.props.dispatch(PaymentActions.showPaytmQRModal(false));
        this.props.dispatch(PaymentActions.setShowCashPaymentModal(false, false, false));
        this.props.dispatch(CartManagementActions.setShowOrderSourceModal(false));

    }

    componentDidMount() {
        if (!appUtil.checkEmpty(this.props.cart) && this.props.cart.orderDetail != null && (this.props.cart.orderDetail.source == "COD" && this.props.cart.orderDetail.deliveryAddress == null)) {
            this.props.dispatch(UtilityActions.showPopup("Please select add delivery address."));
            this.props.dispatch(CustomerActions.setAddressViewType("DELIVERY"));
            browserHistory.push("/addresses");
        }
        trackUtils.trackPageView({page: "paymentModes", device: "desktop", custom: true});
        this.props.dispatch(UtilityActions.hideFullPageLoader());
        this.props.dispatch(CartManagementActions.setShowOrderSourceModal(true));
    }

    removeRedeemChai() {
        this.props.dispatch(PaymentActions.removeRedeemChai(this.props));
        this.props.customer.chaiRedeemed = 0;
        this.props.customer.redemptionDone = false;
        this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
        StorageUtils.setCustomerDetail(this.props.customer);
    }

    removeChaayosCash() {
        this.props.dispatch(PaymentActions.removeChaayosCash(this.props));
    }

    canPayViaGiftCard() {
        return this.props.customer.giftCardBalance >= this.props.cart.orderDetail.transactionDetail.paidAmount;
    }

    useChaayosCash(event) {
        if (event.target.checked) {
            if (!this.props.customer.otpVerified) {
                this.props.customer.otpSource = "ChaayosCash";
                this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
                StorageUtils.setCustomerDetail(this.props.customer);
                this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
                this.props.dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: true});
            } else {
                this.props.dispatch(PaymentActions.applyChaayosCash(this.props));
            }
        } else {
            if (this.props.customer.availChaayosCash) {
                this.props.dispatch(PaymentActions.removeChaayosCash(this.props));
            }
        }
    }

    verifyOtp() {
        const otp = document.getElementById("otpEntered").value;
        if (otp.length == 4) {
            this.props.customer.otpEntered = otp;
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
            this.props.dispatch(PaymentActions.verifyOtpToRedeemChai(this.props, "CHAAYOS_CASH"))
        }
    }

    verifyOtpForGiftCard() {
        const otp = document.getElementById("giftCardOtpEntered").value;
        if (otp.length == 4) {
            this.props.customer.otpEntered = otp;
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
            this.props.dispatch(PaymentActions.verifyOtpForGiftCard(this.props));
        }
    }

    resendOtp() {
        this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
    }

    closeGCPMModal() {
        this.props.dispatch({type: "SET_SHOW_PAYMODES_FOR_GIFT_CARD", payload: false});
    }

    renderOrderSummary = () => {
        let orders = [];
        let i = 0;
        let flag = false;

        this.props.cart.orderDetail.orders.forEach((u) => {
            i += 1;
            if (u.hasBeenRedeemed && !flag && this.props.customer.redemptionDone) {
                flag = true;
                // if(StorageUtils.getRedemptionChaiProductIds().indexOf(u.productId) != -1) {
                //
                // }
                orders.push(
                    <div key={i} style={{fontSize: "35px", display: "flex"}}>
                        <div style={{width: "55%"}}>{u.productName}<span>(Loyalty)</span></div>
                        <div style={{width: "20%"}}>{u.quantity}</div>
                        <div style={{width: "15%"}}>{Math.round(u.totalAmount + parseFloat(u.originalTax))}</div>
                        <div style={{
                            background: "#5e7e47",
                            color: "#fff",
                            textAlign: "center",
                            borderRadius: "32px",
                            width: "30px",
                            height: "30px",
                            fontSize: "20px",
                            padding: "3px"
                        }} onClick={() => {
                            this.removeRedeemChai();
                        }}>X
                        </div>
                    </div>
                );
            } else {
                orders.push(
                    <div key={i} style={{fontSize: "35px", display: "flex"}}>
                        <div style={{width: "55%"}}>{u.productName}</div>
                        <div style={{width: "20%"}}>{u.quantity}</div>
                        <div style={{width: "15%"}}>{Math.round(u.totalAmount + parseFloat(u.originalTax))}</div>
                    </div>
                );
            }
        });
        return orders;
    };


    render() {
        let cartDetail = this.props.cart;
        if (cartDetail != null && cartDetail.orderDetail.transactionDetail != null && cartDetail.orderDetail.transactionDetail.paidAmount >= 1) {
            const payableAmount = Math.round(cartDetail.orderDetail.transactionDetail.paidAmount);
            return (
                <div style={{backgroundColor: "rgb(244, 244, 244)", height: "1920px", overflow: "hidden"}}>
                    <IdleTimer
                        ref={ref => {
                            this.idleTimer = ref
                        }}
                        element={document}
                        onActive={this.onActive}
                        onIdle={this.onIdle}
                        onAction={this.onAction}
                        debounce={250}
                        timeout={this.state.timeout}/>
                    <DesktopBannerHeader/>
                    <div style={{
                        fontSize: "28px", textTransform: "capitalize", margin: "0 auto", padding: "20px", color: "#fff",
                        background: "#5e7e47", border: "#4a6238 1px solid"
                    }}>
                        Hi {this.props.customer.name}
                        {/*<span style={{float: "right"}}>******{this.props.customer.contact.substr(6)}</span>*/}
                    </div>

                    <div style={{width: "80%", margin: "0 auto"}}>
                        <div className="loginBackButton" onClick={this.goBack.bind(this)}
                             style={{margin: "40px 0"}}>Edit Cart
                        </div>
                        <div className="loginBackButton" onClick={this.goBack.bind(this, true)}
                             style={{margin: "40px 0", float: "right"}}>Start New Order
                        </div>
                    </div>

                    <div className="OrderSummary">
                        {(this.props.cart.orderDetail.orders.length > 0) &&
                        <div style={{fontSize: "35px", display: "flex", fontWeight: "bold", marginBottom: "15px"}}>
                            <div style={{width: "54%"}}>Item</div>
                            <div style={{width: "20%"}}>Qty</div>
                            <div style={{width: "15%"}}>Rate</div>
                        </div>
                        }
                        <div style={{maxHeight: "175px", overflow: "auto"}}>
                            {this.renderOrderSummary()}
                        </div>
                        <div
                            style={{textAlign: "center", color: "red", marginTop: "10px"}}>{this.props.otpMessage}</div>
                        {(!this.props.cart.orderDetail.offerCode && this.props.customer.chaiRedeemed == 0 && this.props.customer.chaayosCash > 0) &&
                        <div className="chaayosCash" style={this.props.customer.availChaayosCash ? {opacity: 1} : {}}>
                            <input style={{
                                opacity: "2",
                                height: "35px",
                                width: "40px",
                                fontSize: "25px",
                                margin: "0 10px"
                            }}
                                   type="checkbox"
                                   defaultChecked={false} value="text" onChange={(e) => {
                                this.useChaayosCash(e)
                            }}
                            />Use your <img style={{
                            width: "25px",
                            height: "35px", margin: "0 10px -6px 10px"
                        }}
                                            src="../../../img/rupee.png"/>
                            100 Chaayos Cash
                        </div>
                        }
                        {/*{this.props.showOtp &&*/}
                        {/*<input style={{fontSize: "30px"}} id="otpEntered" type="tel" placeholder="Enter OTP here"*/}
                        {/*maxLength="4"*/}
                        {/*autoComplete="off" autoFocus="true" onKeyUp={() => this.verifyOtp()}/>*/}
                        {/*}*/}
                    </div>
                    <div style={{
                        fontSize: "35px",
                        background: "rgba(89,126,58,0.21)",
                        padding: "3% 0",
                        marginTop: "30px",
                        fontWeight: "bolder",
                        color: "#4c4b4a"
                    }}>
                        <div style={{marginLeft: "10%", width: "100%"}}>
                            {cartDetail != null && cartDetail.orderDetail.transactionDetail != null && cartDetail.orderDetail.transactionDetail.savings > 0 &&
                            <div>
                                {
                                    <div style={{display: "flex"}}>
                                        <span style={{width: "60%"}}>Total </span>
                                        <span>{cartDetail.orderDetail.transactionDetail.savings + cartDetail.orderDetail.transactionDetail.paidAmount}</span>
                                    </div>
                                }
                                {this.props.couponCode &&
                                <div style={{display: "flex"}}>
                                    <span style={{width: "60%"}}>Coupon Code Applied </span>
                                    <span>{this.props.couponCode}</span>
                                </div>
                                }
                                <div style={{display: "flex"}}>
                                    <span style={{width: "60%"}}>Savings </span>
                                    <span>{cartDetail.orderDetail.transactionDetail.savings}
                                </span>
                                </div>
                            </div>
                            }
                            <div style={{display: "flex", fontSize: "45px"}}>
                                <span style={{width: "60%"}}>Amount Payable</span>
                                <span style={{width: "20%"}}>{payableAmount}</span>
                            </div>
                        </div>

                    </div>
                    <div class="loyalitySection">
                        {/*{(this.props.customer.chaayosCash > 0 && !this.props.customer.availChaayosCash && this.props.customer.chaiRedeemed == 0) &&*/}
                        {/*<div className="loginBackButton" onClick={() => {*/}
                        {/*this.openRedeemChaayosCashModal(true)*/}
                        {/*}}*/}
                        {/*style={{margin: "100px 0", float: "left"}}>Chaayos Cash*/}
                        {/*</div>*/}
                        {/*}*/}
                        {/*{this.props.customer.availChaayosCash &&*/}
                        {/*<div className="loginBackButton" onClick={() => {*/}
                        {/*this.removeChaayosCash()*/}
                        {/*}}*/}
                        {/*style={{margin: "100px 0", float: "left"}}>Remove Chaayos Cash*/}
                        {/*</div>}*/}

                        {/*{this.checkRedeemAvailable(this.props) &&*/}
                        {/*<div className="loginBackButton" style={{float:"right"}} onClick={() => {*/}
                        {/*this.openRedeemChaayosCashModal(false)*/}
                        {/*}}*/}
                        {/*style={{margin: "100px 0", float: "right"}}>Redeem Chai*/}
                        {/*</div>*/}
                        {/*}*/}
                        {/*{this.props.customer.chaiRedeemed > 0 &&*/}
                        {/*<div className="loginBackButton" onClick={() => {*/}
                        {/*this.removeRedeemChai();*/}
                        {/*}}*/}
                        {/*style={{margin: "0", float: "right"}}>Remove Redeem Chai*/}
                        {/*</div>}*/}
                    </div>
                    {/*<div class="paymentModeSection"*/}
                    {/*style={{background: "none", textAlign: "center", minHeight: "auto", marginTop: "85px"}}>*/}
                    {/*<div class="payableAmount1">*/}
                    {/*<img style={{width: "51px", marginBottom: "-6px"}}*/}
                    {/*src="../../../img/indian-ruppee-icon-white.png"/>*/}
                    {/*<span style={{*/}
                    {/*fontSize: "60px",*/}
                    {/*margin: "0 0 0 15px",*/}
                    {/*fontWeight: "bold"*/}
                    {/*}}>{payableAmount}</span>*/}
                    {/*</div>*/}
                    {/*</div>*/}
                    {/*<div class="desktopPageHead"*/}
                    {/*style={{margin: "50px 0 0 0", textAlign: "center", fontSize: "43px"}}>How would you like to*/}
                    {/*pay?*/}
                    {/*</div>*/}
                    {(this.props.customer.giftCardBalance > 0 || (this.props.allGiftCards!=null && this.props.allGiftCards.length > 0)) &&
                    <div style={{marginTop: "30px"}}>
                        {this.props.giftCardOffer!=null && this.props.giftCardOffer.length > 0 &&
                        <div style={{
                            fontSize: "35px",
                            textAlign: "center",
                            background: "#73a141",
                            padding: "20px"
                        }}><span
                            style={{color: "white", padding: "10px", borderRadius: "10px"}}>Offer Ends Today</span>
                        </div>
                        }

                        {this.props.giftCardOffer!=null && this.props.giftCardOffer.length == 0 &&
                        <div style={{
                            fontSize: "35px",
                            textAlign: "center",
                            background: "#73a141",
                            padding: "20px"
                        }}><span
                            style={{
                                color: "white",
                                padding: "10px",
                                borderRadius: "10px"
                            }}>Add money to your wallet</span>
                        </div>
                        }
                        <div class="giftCardSection">
                            <div style={{display: "flex", margin: "20px 0"}}>
                                {/*{this.canPayViaGiftCard() &&*/}
                                <div style={{
                                    border: "1px solid", padding: "25px", width: "32%",
                                    borderRadius: "25px",
                                    background: "#c47f1a",
                                    color: "white"
                                }} onClick={() => {
                                    this.payByGiftCard()
                                }}>
                                    <div style={{
                                        textAlign: "left", paddingLeft: "15px",
                                        fontSize: "34px", marginTop: "30px", marginBottom: "20px"
                                    }}>Pay Via Chaayos Wallet
                                    </div>
                                    <div style={{
                                        color: "#c47f1a",
                                        background: "white",
                                        borderRadius: "20px",
                                        padding: "10px"
                                    }}>
                                        <div style={{display: "flex"}}>
                                            <img src="../../../img/chaayosWallet.png"/>
                                            <span style={{marginTop: "30px"}}>Balance</span>
                                        </div>
                                        <div style={{
                                            fontSize: "68px",
                                            fontWeight: "bolder",
                                            textAlign: "left"
                                        }}>{this.props.customer.giftCardBalance}</div>
                                    </div>
                                </div>
                                {/*}*/}
                                {this.props.allGiftCards!=null && this.props.allGiftCards.length > 0 &&
                                <div className="giftCardContainer">{this.renderGiftCards()}
                                </div>
                                }
                            </div>
                            {/*{this.props.showGiftCardPaymentModal &&*/}
                            {/*<div style={{*/}
                            {/*display: "flex",*/}
                            {/*marginTop: "30px"*/}
                            {/*}}>*/}
                            {/*<input style={{*/}
                            {/*fontSize: "25px", borderRadius: "10px",*/}
                            {/*border: "1px solid",*/}
                            {/*marginRight: "30px"*/}
                            {/*}} id="giftCardOtpEntered" type="tel"*/}
                            {/*placeholder="Enter OTP here" maxLength="4"*/}
                            {/*autoComplete="off" autoFocus="true"*/}
                            {/*onKeyUp={() => this.verifyOtpForGiftCard()}/>*/}
                            {/*<div style={{*/}
                            {/*background: "rgb(94, 126, 71)", padding: "10px",*/}
                            {/*width: "20%",*/}
                            {/*borderRadius: "15px", color: "white"*/}
                            {/*}} onClick={() => this.resendOtp()}>Resend*/}
                            {/*</div>*/}
                            {/*</div>*/}
                            {/*}*/}
                            {this.props.customer.gitCardMessage}
                        </div>
                    </div>
                    }

                    <div style={{
                        fontSize: "35px",
                        textAlign: "center",
                        background: "#73a141",
                        padding: "20px",
                        marginTop: "20px"
                    }}><span
                        style={{color: "white", padding: "10px", borderRadius: "10px"}}>Other Payment Options</span>
                    </div>
                    <div class="paymentModeSection"
                         style={{backgroundColor: "rgb(244, 244, 244)", textAlign: "center"}}>
                        {/*<img src="../../../img/backgrounds/payment_modes_screen.png" />*/}

                        <div style={{maxWidth: "1024px", margin: "0 auto", marginTop: "20px", textAlign: "center"}}>
                            {this.props.selectivePaymentMode == "CASH" ? (
                                <div>
                                    <div class="loginBlockLeft"
                                         style={{
                                             background: "#fff",
                                             boxShadow: "#333 0 0 2px 0",
                                             paddingTop: "70px",
                                             height: "267px",
                                             elevation: 10
                                         }}
                                         onClick={this.payByPaytmQR.bind(this)}>
                                        <img src="../../../img/paytm-logo.png"
                                             style={{width: "80%"}}/>
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold",
                                            marginTop: "60px"
                                        }}>Tap to pay
                                        </div>
                                    </div>
                                    {/*<div class="loginDividerBlock">*/}
                                    {/*<div class="loginDivider">OR</div>*/}
                                    {/*</div>*/}
                                    <div class="loginBlockRight"
                                         style={{
                                             background: "#fff",
                                             boxShadow: "#333 0 0 2px 0",
                                             height: "260px",
                                             elevation: 10}}
                                         onClick={this.payByCash.bind(this)}>
                                        <div style={{fontSize: "21px", fontWeight: "bold", margin: "20px"}}>Pay By
                                            Cash
                                        </div>
                                        <img src="../../../img/payment/indian_Rupee_Currency.png"
                                             style={{width: "61%", height: "142px"}}/>
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold"
                                        }}>Tap to pay
                                        </div>
                                    </div>
                                </div>
                            ) : null}
                            {this.props.selectivePaymentMode == "CARDS" ? (
                                <div>
                                    <div class="loginBlockLeft"
                                         style={{
                                             background: "#fff",
                                             boxShadow: "#333 0 0 2px 0",
                                             paddingTop: "70px",
                                             height: "267px"
                                         }}
                                         onClick={this.payByPaytmQR.bind(this)}>
                                        <img src="../../../img/paytm-logo.png"
                                             style={{width: "80%"}}/>
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold",
                                            marginTop: "60px"
                                        }}>Tap to pay
                                        </div>
                                    </div>
                                    {/*<div class="loginDividerBlock">*/}
                                    {/*<div class="loginDivider">OR</div>*/}
                                    {/*</div>*/}
                                    <div class="loginBlockRight"
                                         style={{background: "#fff", boxShadow: "#333 0 0 2px 0", height: "260px"}}
                                         onClick={this.payByCardMachine.bind(this)}>
                                        <div style={{fontSize: "21px", fontWeight: "bold", margin: "20px"}}>Credit/Debit
                                            Cards
                                        </div>
                                        <img src="../../../img/card-type.png" style={{margin: "10px", width: "61%"}}/>
                                        {/*<img src="../../../img/ezetap-logo.png"*/}
                                        {/*style={{width: "180px", margin: "10px"}}/>*/}
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold"
                                        }}>Tap to pay
                                        </div>
                                    </div>
                                </div>
                            ) : null}

                            {this.props.selectivePaymentMode == "ALL" ? (
                                <div>
                                    <div class="loginBlockLeft"
                                         style={{
                                             background: "#fff",
                                             boxShadow: "#333 0 0 2px 0",
                                             paddingTop: "70px",
                                             height: "267px"
                                         }}
                                         onClick={this.payByPaytmQR.bind(this)}>
                                        <img src="../../../img/paytm-logo.png"
                                             style={{width: "80%"}}/>
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold",
                                            marginTop: "60px"
                                        }}>Tap to pay
                                        </div>
                                    </div>

                                    <div className="loginBlockRight"
                                         style={{background: "#fff", boxShadow: "#333 0 0 2px 0", height: "unset"}}
                                         onClick={this.payByCardMachine.bind(this)}>
                                        <div style={{fontSize: "21px", fontWeight: "bold", margin: "20px"}}>Credit/Debit
                                            Cards
                                        </div>
                                        <img src="../../../img/card-type.png" style={{margin: "10px", width: "61%"}}/>
                                        {/*<img src="../../../img/ezetap-logo.png"*/}
                                        {/*style={{width: "180px", margin: "10px"}}/>*/}
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold"
                                        }}>Tap to pay
                                        </div>
                                    </div>

                                    <div class="loginBlockRight"
                                         style={{background: "#fff", boxShadow: "#333 0 0 2px 0", height: "unset"}}
                                         onClick={this.payByCash.bind(this)}>
                                        <div style={{fontSize: "21px", fontWeight: "bold", margin: "20px"}}>Pay By
                                            Cash
                                        </div>
                                        <img src="../../../img/payment/indian_Rupee_Currency.png"
                                             style={{width: "61%", height: "142px"}}/>
                                        {/*<img src="../../../img/tap-icon.png"*/}
                                        {/*style={{margin: "10px 55px", width: "60px"}}/>*/}
                                        <div style={{
                                            padding: "10px 0",
                                            color: "#5e7e47",
                                            textAlign: "center",
                                            fontSize: "21px",
                                            fontWeight: "bold"
                                        }}>Tap to pay
                                        </div>
                                    </div>
                                </div>
                            ) : null}

                        </div>

                    </div>



                    {this.props.showPayModesForGiftCard? (
                        <div className="modal" style={{background: "rgba(0, 0, 0, 0.3)"}}>
                            <div className="modalBody"
                                 style={{
                                     top: "20%",
                                     padding: "20px",
                                     width: "95%",
                                     height: "100%",
                                     left: "28px",
                                     borderRadius: "35px"
                                 }}>
                                <span></span>
                                <div className="modalCloseBtn"
                                     style={{right: "50px", top: "53px", fontSize: "50px", color: "white"}}
                                     onClick={() => this.closeGCPMModal()}>
                                    &times;
                                </div>
                                <div className="modalTitle" style={{
                                    fontSize: "40px",
                                    marginTop: "10px",
                                    padding: "30px",
                                    color: "white",
                                    background: "#5e7e47",
                                    borderRadius: "35px",
                                    height: "auto"
                                }}>
                                    Select Wallet Payment Mode
                                </div>
                                <div>
                                    <div style={{
                                        fontSize: "45px",
                                        textAlign: "center",
                                        color: "#5e7e47",
                                        marginTop: "30px",
                                        fontWeight: "600"
                                    }}>
                                        Total Amount
                                        <img className="rupeeIcon" style={{margin: "0px 5px 0 20px"}}
                                             src="../../../img/rupee.png"/>
                                        <span>{this.props.customer.giftCardAmount}</span>
                                    </div>

                                    <div style={{marginTop: "10%", display: "flex"}}>
                                        <div className="loginBlockLeft"
                                             style={{
                                                 background: "#fff",
                                                 boxShadow: "#333 0 0 2px 0",
                                                 margin: "30px",
                                                 width: "40%",
                                                 height: "600px"
                                             }}
                                             /*onClick={this.payByCardMachine.bind(this)}*//>
                                            <div style={{
                                                fontSize: "30px",
                                                fontWeight: "bold",
                                                margin: "20px",
                                                textAlign: "center"
                                            }}>
                                                <span>Paytm</span>
                                            </div>
                                        </div>

                                        {/*<div>*/}
                                        <div style={{
                                            background: "lightgray",
                                            width: "5px",
                                            margin: "30px"
                                        }}>
                                        </div>
                                        <div style={{
                                            fontSize: "20px",
                                            color: "white", background: "#5e7e47",
                                            padding: "13px", marginTop: "30%",
                                            marginLeft: "-7%",
                                            height: "75px",
                                            borderRadius: "50%",
                                            border: "10px solid white"
                                        }}>OR
                                        </div>
                                        {/*</div>*/}

                                    </div>

                                </div>

                            </div>
                    ):null}


                    <DesktopPaytmQRCodeModal amountToPay={payableAmount}/>
                    <EzetapPaymentModal amountToPay={payableAmount}/>
                    <AGSPaymentModal />
                    <CashPaymentModal amountToPay={payableAmount}/>
                    <GiftCardPurchaseModal/>
                    <GiftCardPaymentModal/>
                    {/*<OrderSourceModal/>*/}
                    <DesktopPopupModal/>
                </div>
            )
        } else {
            return null;
        }
    }
}
