import React from "react";
import {connect} from "react-redux";
import {Link, browserHistory} from "react-router";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as CustomizationModalActions from "../../actions/CustomizationModalActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as UtilityActions from "../../actions/UtilityActions";
import appUtil from "../../AppUtil";

@connect((store) => {
	return {
		cart: store.cartManagementReducer.cart,
		unit: store.outletMenuReducer.unit,
		skipDeliveryPackaging:store.outletMenuReducer.skipDeliveryPackaging,
		cartInventory:store.cartManagementReducer.cartInventory,
	};
})
export default class DesktopCartItemLayout extends React.Component {

	constructor() {
		super();
		this.state = {};
		this.init = this.init.bind(this);
		this.updateQty = this.updateQty.bind(this);
		this.removeItem = this.removeItem.bind(this);
		this.customizeItem = this.customizeItem.bind(this);
	}

	init() {

	}

	updateQty(qty, item) {
		if(this.props.cart!=null && this.props.cart.orderDetail.offerCode!=null){
			this.props.dispatch(CartManagementActions.removeCoupon());
			if(document.getElementById("couponCode") != null) {
                document.getElementById("couponCode").value = "";
			}
			this.props.dispatch(UtilityActions.showPopup("We have removed coupon from your cart as it is being updated. Please reapply coupon once you have made all the changes.", "info", 6000));
		}
		if ((this.props.item.quantity + qty) > 0) {
			var stock = this.props.cartInventory[this.props.item.productId];
			if(stock==null || (stock-qty)>=0){
				this.props.dispatch(CartManagementActions.updateItemQty(this.props.cart, qty, item.itemId, this.props.skipDeliveryPackaging));
				if(stock!=null){
					var stockInv = Object.assign(this.props.cartInventory);
					stockInv[this.props.item.productId] -= qty;
					this.props.dispatch(CartManagementActions.updateCartStock(stockInv));
				}
			}
		}else if ((this.props.item.quantity + qty) == 0) {
			this.removeItem(item);
		}
	}

	removeItem(item) {
		var props = this.props;
        if(props.cart!=null && props.cart.orderDetail.offerCode!=null){
            props.dispatch(CartManagementActions.removeCoupon());
            document.getElementById("couponInput").value = "";
            props.dispatch(UtilityActions.showPopup("We have removed coupon from your cart as it is being updated. Please reapply coupon once you have made all the changes.", "info", 6000));
        }
        props.dispatch(CartManagementActions.removeItem(props.cart, item, "cart", props.skipDeliveryPackaging));
	}

	customizeItem(item) {
		const dcIds = [10,11,12,50];
		var pid = dcIds.indexOf(item.productId)>=0?11:item.productId;
		this.props.unit.products.map((product) => {
			if (product.id == pid) {
				var prod = Object.assign({}, product);
				this.props.dispatch(CartManagementActions.setEditItemId(item.itemId));
				this.props.dispatch(CustomizationModalActions.showCustomizationModal(prod, "CART_ITEM", item));
				this.props.dispatch(OutletMenuActions.loadProductRecipe(this.props.unit, prod, item));
			}
		});
	}

	componentWillMount() {
		this.init();
	}

	render() {

		var variants = [];
		var products = [];
		var addons = [];
		var menuProducts = [];
		var dcIds = [10,11,12,50];
		var stock = null;
		if(this.props.item!=null){
            if(this.props.cartInventory != null) {
                stock = this.props.cartInventory[this.props.item.productId];
            }
            var dcNames = {10:"Regular",11:"Full Doodh",12:"Doodh Kum",50:"Pani Kum"};
            if (this.props.item.composition!=null
				&& !appUtil.checkEmpty(this.props.item.composition.variants)) {
                this.props.item.composition.variants.map((variant) => {
                    variants.push(variant.alias);
                });
            }
            if (this.props.item.composition!=null
				&& !appUtil.checkEmpty(this.props.item.composition.products)) {
                this.props.item.composition.products.map((product) => {
                    products.push(product.product.name);
                });
            }
            if (this.props.item.composition!=null
				&& !appUtil.checkEmpty(this.props.item.composition.addons)) {
                this.props.item.composition.addons.map((addon) => {
                    addons.push(addon.product.name);
                });
            }
            if (this.props.item.composition!=null
				&& !appUtil.checkEmpty(this.props.item.composition.menuProducts)) {
                this.props.item.composition.menuProducts.map((product) => {
                    menuProducts.push(product.productName);
                });
            }
		}

		return (
			<div class="cartBox cartItemContainer">
				{stock!=null && stock>=0 && stock<6?(
					<div class={stock>0?"stockTag":"stockTag red"}>{stock} In Stock</div>
				):null}
				<div class="rel">
					<div class="actionContainer">
						<div class="actionDiv">
							{(this.props.item.customizationStrategy==1 || this.props.item.customizationStrategy==3)?(
									<div style={{display:"inline-block",textDecoration:"underline", color:"#5e7e47", margin:"10px",
										fontSize:"15px", fontWeight:"bold", textTransform:"uppercase"}}
										 onClick={this.customizeItem.bind(this, this.props.item)}>
										{/*<img class= "editImg" src="../../../img/edit.png" />*/}
										Edit Item
									</div>
								):(null)}
							<div class="removeItem" onClick={this.removeItem.bind(this, this.props.item)}>&times;</div>
						</div>
					</div>
				</div>
				<div class="rel">
					<div class="pic" style={{backgroundImage:"url(../../../img/products/"+this.props.item.productId+".jpg)"}}></div>
					<div class="itemTitle ellipsis">{this.props.item.productName}</div>
					<div class="itemPrice">
						<img class="rupeeIcon" src="../../img/rupee.png" style={{height:"20px"}} />
						{Math.round(parseFloat(this.props.item.totalAmount) + parseFloat(this.props.item.tax))}
					</div>
					<div class="rel">
						<div class="itemDetail">
							<div>{menuProducts}</div>
							<div>
								{this.props.item.customizationStrategy==4?(
									<span>{this.props.item.dimension=="None"?"Regular":this.props.item.dimension}</span>
								):null}
								{this.props.item.customizationStrategy==3 || this.props.item.customizationStrategy==1?(
									<span>
                                        <span>{this.props.item.dimension== "None"?"Regular":this.props.item.dimension}</span>
										{dcIds.indexOf(this.props.item.productId) >= 0 ? ", Milk - " + dcNames[this.props.item.productId] : ""}
										{", " + variants.join(", ")}
										{products.join(", ")}
                                    </span>
								):null}
								{this.props.item.customizationStrategy==2?(
									<span>{variants.length > 0?variants.join(", "):products.length>0?products.join(", "):""}</span>
								):null}
							</div>
							{(this.props.item.customizationStrategy==3 || this.props.item.customizationStrategy==1) && addons.length>0?(
								<div>{"Add On : "+addons.join(", ")}</div>
							):null}

						</div>
						<div class="qtyWrapper">
							<div class="incr" onClick={this.updateQty.bind(this, 1, this.props.item)}>&#43;</div>
							<div class="qty">{this.props.item.quantity}</div>
							<div class="dcr" onClick={this.updateQty.bind(this, -1, this.props.item)}>&#45;</div>
						</div>
					</div>
				</div>
			</div>
		)
	}
}