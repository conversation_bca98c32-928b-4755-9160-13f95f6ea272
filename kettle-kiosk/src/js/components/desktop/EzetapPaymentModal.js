import React from "react";
import {connect} from "react-redux";
import * as PaymentActions from "../../actions/PaymentActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as OrderManagementActions from "../../actions/OrderManagementActions";

@connect((store) => {
    return {
        showEzetapModal: store.paymentReducer.showEzetapModal,
        ezetapStatusMessage: store.paymentReducer.ezetapStatusMessage,
        showEzetapLoader: store.paymentReducer.showEzetapLoader,
        ezetapInterval: store.paymentReducer.ezetapInterval,
        ezetapStatusCode: store.paymentReducer.ezetapStatusCode,
        orderFailed: store.orderManagementReducer.orderFailed,
        woId: store.orderManagementReducer.woId,
    };
})
export default class EzetapPaymentModal extends React.Component {

    constructor() {
        super();
        this.state = {
            couponCode: "",
            contactMsg: "",
            nameMsg: "",
            otpMsg: "",
            emailMsg: ""
        };
        this.closeModal = this.closeModal.bind(this);
        this.cancelPayment = this.cancelPayment.bind(this);
        this.placeOrder = this.placeOrder.bind(this);
    }

    closeModal() {
        this.props.dispatch(PaymentActions.setShowEzetapPaymentModal(false, null, this.props.ezetapInterval));
    }

    cancelPayment() {
        //this.props.dispatch(PaymentActions.cancelEzetapPayment());
        this.props.dispatch(PaymentActions.cancelEzetapPayment());
        this.closeModal();
    }

    placeOrder() {
        this.props.dispatch(OrderManagementActions.orderCheckoutByWOId(this.props.woId, null));
    }

    componentWillMount() {

    }

    render() {
        if (this.props.showEzetapModal) {
            return (
                <div class="modal">
                    <div class="modalBody">
                        <div className="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                            &times;
                        </div>
                        <p class="modalTitle">
                            Processing Payment
                        </p>
                        <div class="customizationSection productHead">

                            <div className="payableAmount1Modal">
                                <p>Collecting Payment</p><br/>
                                <img style={{width: "51px", marginBottom: "-6px"}}
                                     src="../../../img/indian-ruppee-icon-white.png"/>
                                <span style={{fontSize: "60px", margin: "0 0 0 15px", fontWeight: "bold"}}>{this.props.amountToPay}</span>
                            </div>

                            {this.props.showEzetapLoader ? (
                                <div class="loaderWrapper">
                                    <div class="load8 loader"></div>
                                </div>
                            ) : null}
                            <div style={{minHeight:"150px"}}>
                                <div style={{textAlign:"center"}}>
                                    {(this.props.ezetapStatusCode.code <= 30) ? (
                                        <div>
                                            {(this.props.ezetapStatusCode.code <= 12 && this.props.ezetapStatusCode.code <= 21 && this.props.ezetapStatusCode.code != 23) ? (
                                                <p style={{fontSize:"24px", textAlign:"center", color:"red"}}>{this.props.ezetapStatusCode.message}</p>
                                            ):(
                                                <p style={{fontSize:"24px", textAlign:"center"}}>{this.props.ezetapStatusCode.message}</p>
                                            )}
                                        </div>
                                    ):null}
                                    {(this.props.ezetapStatusCode.code > 30 && this.props.orderFailed != true) ? (
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>
                                            Payment successful. Placing your order. Please remove your card.
                                        </p>
                                    ) : null}
                                    {(this.props.ezetapStatusCode.code > 30 && this.props.orderFailed == true) ? (
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>
                                            Payment was successful but order failed.<br/> Please try again or contact our customer support at 1800-120-2424.
                                        </p>
                                    ) : null}

                                    {(this.props.ezetapStatusCode.code > -1 && this.props.ezetapStatusCode.code < 5) ? (
                                        <div style={{textAlign:"center"}}>
                                            <img src="../../../img/payment/clock_rotation_gif.gif" style={{width:"300px", height:"300px" ,marginBottom:"30px"}} />
                                            <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                                <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please Insert Your Card</h1>
                                                <img src="../../../img/payment/swipe-arrow.gif" />
                                            </div>
                                        </div>
                                    ) : null}
                                    {(this.props.ezetapStatusCode.code == 5) ? (
                                        <div style={{textAlign: "center"}}>
                                            <img src="../../../img/payment/insert-card-animation.gif" style={{maxWidth: "400px", marginBottom: "30px"}}/>
                                            <div style={{position: "fixed", bottom: "0", right: "0"}}>
                                                <h1 style={{fontSize: "35px", fontWeight: "bold", color: "#fff"}}>Please Insert Your Card</h1>
                                                <img src="../../../img/payment/swipe-arrow.gif"/>
                                            </div>
                                        </div>
                                    ) : null}
                                    {(this.props.ezetapStatusCode.code == 8) ? (
                                        <div style={{textAlign:"center"}}>
                                            <img src="../../../img/payment/enter-pin.gif" style={{maxWidth:"400px", marginBottom:"30px"}} />
                                            <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                                <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please Enter PIN Here</h1>
                                                <img src="../../../img/payment/swipe-arrow.gif" />
                                            </div>
                                        </div>
                                    ) : null}
                                    {(this.props.ezetapStatusCode.code >= 9 && this.props.ezetapStatusCode.code <= 11) ? (
                                        <div style={{textAlign:"center"}}>
                                            <img src="../../../img/payment/money-transfer.gif" style={{marginBottom:"30px"}} />
                                        </div>
                                    ) : null}
                                    {(this.props.ezetapStatusCode.code > 30 && this.props.orderFailed != true) ? (
                                        <div style={{textAlign:"center"}}>
                                            <img src="../../../img/payment/payment-successful.gif" />
                                            <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                                <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please remove your card.</h1>
                                                <img src="../../../img/payment/swipe-arrow.gif" />
                                            </div>
                                        </div>
                                    ) : null}
                                    {(this.props.ezetapStatusCode.code > 30 && this.props.orderFailed == true) ? (
                                        <div style={{textAlign:"center"}}>
                                            <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>
                                                Payment was successful but order failed.<br/> Please try again or contact our customer support at 1800-120-2424.
                                            </p>
                                            <img src="../../../img/payment/payment-successful.gif" />
                                        </div>
                                    ) : null}
                                </div>



                                {/*{(this.props.ezetapStatusCode < 0) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center"}}>Error in processing payment.<br/> Please try again.</p>
                                    </div>
                                ) : null}
                                {(this.props.ezetapStatusCode > -1 && this.props.ezetapStatusCode < 5) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center"}}>Preparing for the transaction</p>
                                        <img src="../../../img/payment/clock_rotation_gif.gif"
                                             style={{width:"300px", height:"300px" ,marginBottom:"30px"}} />
                                        <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                            <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please Insert Your Card</h1>
                                            <img src="../../../img/payment/swipe-arrow.gif" />
                                        </div>
                                    </div>
                                ) : null}
                                {(this.props.ezetapStatusCode > 4 && this.props.ezetapStatusCode < 8) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>Please insert card in the device.</p>
                                        <img src="../../../img/payment/insert-card-animation.gif" style={{maxWidth:"400px", marginBottom:"30px"}} />

                                        <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                            <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please Insert Your Card</h1>
                                            <img src="../../../img/payment/swipe-arrow.gif" />
                                        </div>

                                    </div>
                                ) : null}
                                {(this.props.ezetapStatusCode > 7 && this.props.ezetapStatusCode < 10) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>Please enter secret PIN on the device.</p>
                                        <img src="../../../img/payment/enter-pin.gif" style={{maxWidth:"400px", marginBottom:"30px"}} />

                                        <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                            <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please Enter PIN Here</h1>
                                            <img src="../../../img/payment/swipe-arrow.gif" />
                                        </div>
                                    </div>
                                ) : null}
                                {(this.props.ezetapStatusCode > 9 && this.props.ezetapStatusCode < 20) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>Processing payment.</p>
                                        <img src="../../../img/payment/money-transfer.gif" style={{marginBottom:"30px"}} />
                                    </div>
                                ) : null}
                                {(this.props.ezetapStatusCode > 20 && this.props.orderFailed != true) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>
                                            Payment successful. Placing your order. Please remove your card.
                                        </p>
                                        <img src="../../../img/payment/payment-successful.gif" />

                                        <div style={{position:"fixed", bottom:"0", right:"0"}}>
                                            <h1 style={{fontSize:"35px", fontWeight:"bold", color: "#fff"}}>Please remove your card.</h1>
                                            <img src="../../../img/payment/swipe-arrow.gif" />
                                        </div>
                                    </div>
                                ) : null}
                                {(this.props.ezetapStatusCode > 20 && this.props.orderFailed == true) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"24px", textAlign:"center", padding:"30px 0"}}>
                                            Payment was successful but order failed.<br/> Please try again or contact our customer support at 1800-120-2424.
                                        </p>
                                        <img src="../../../img/payment/payment-successful.gif" />
                                    </div>
                                ) : null}*/}
                            </div>
                            <div style={{textAlign: "right"}}>
                                <div className="btn btn-default couponBtn" onClick={this.cancelPayment.bind(this)}
                                     style={{display: "inline-block"}}>
                                    Cancel
                                </div>
                            </div>
                            {(this.props.ezetapStatusCode.code > 20 && this.props.orderFailed == true) ? (
                                <div style={{textAlign: "right"}}>
                                    <div class="btn btn-default couponBtn" onClick={this.placeOrder.bind(this)} style={{display: "inline-block"}}>
                                        Try Again
                                    </div>
                                </div>
                            ) : null}
                        </div>
                    </div>
                </div>
            )
        } else {
            return null;
        }
    }
}