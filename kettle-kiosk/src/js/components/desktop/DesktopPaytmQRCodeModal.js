import React from "react";
import { connect } from "react-redux";
import * as PaymentActions from "../../actions/PaymentActions";
import StorageUtils from "../../utils/StorageUtils";
import qrcodel from "qr-code-with-logo";
import * as OrderManagementActions from "../../actions/OrderManagementActions";

@connect((store) => {
    return {
        cart:store.cartManagementReducer.cart,
        showPayTmQRSection: store.paymentReducer.showPayTmQRSection,
        payTMQRCodeId: store.paymentReducer.payTMQRCodeId,
        paytmPaymentSuccess: store.paymentReducer.paytmPaymentSuccess,
        showPaytmQRModal:store.paymentReducer.showPaytmQRModal,
        paytmQRInterval:store.paymentReducer.paytmQRInterval,
        paytmQRStatus:store.paymentReducer.paytmQRStatus,
        woId: store.orderManagementReducer.woId,
        orderFailed: store.orderManagementReducer.orderFailed,

        showPopupMessage: store.utilityReducer.showPopupMessage,
        showFullPageLoader: store.utilityReducer.showFullPageLoader,
        messageText: store.utilityReducer.messageText,
        loaderMessage: store.utilityReducer.loaderMessage,
        messageType: store.utilityReducer.messageType,
        showPrompt: store.utilityReducer.showPrompt,
        promptMessage:store.utilityReducer.promptMessage,
        promptSuccess:store.utilityReducer.promptSuccess,
        promptDismiss:store.utilityReducer.promptDismiss,
        showPaytmGIFLoader:store.utilityReducer.showPaytmGIFLoader

    };
})
export default class DesktopPaytmQRCodeModal extends React.Component {

    constructor() {
        super();
        this.closeModal = this.closeModal.bind(this);
        this.handleChange = this.handleChange.bind(this);
        this.loadNewQR = this.loadNewQR.bind(this);
        this.placeOrder = this.placeOrder.bind(this);
    }

    closeModal() {
        this.props.dispatch(PaymentActions.setPayTMQRCodeId(null));
        this.props.dispatch((PaymentActions.showPaytmQRModal(false, null)));
        clearInterval(this.props.paytmQRInterval);
    }

    loadNewQR() {
        this.props.dispatch(PaymentActions.payByPaytmQR());
    }

    placeOrder() {
        this.props.dispatch(OrderManagementActions.orderCheckoutByWOId(this.props.woId, null));
    }

    handleChange(event) {
    }

    componentDidUpdate(prevProps) {
        /*if(this.props.payTMQRCodeId!==null && prevProps.payTMQRCodeId !== this.props.payTMQRCodeId && !this.props.paytmPaymentSuccess) {
            console.log("Inside condition");
            var canvas = document.getElementById('paytmQr');
            var qrOptions = StorageUtils.getPaytmQrConfigOptions(this.props.payTMQRCodeId);
            qrOptions.canvas = canvas;
            qrcodel.toCanvas(qrOptions);
            this.props.dispatch(PaymentActions.setPaytmPaymentSuccess(false));
            this.props.dispatch(PaymentActions.checkPaytmQRPaymentStatus(this.props.payTMQRCodeId));
        }*/
    }

    componentWillMount() {
        this.props.dispatch(PaymentActions.setPaytmPaymentSuccess(false));
        this.props.dispatch({type: "SET_PAYTM_QR_STATUS", payload: 1});
    }

    render() {
        if (this.props.showPaytmQRModal == true) {
            return (
                <div>
                    <div class="modal">
                        <div class="modalBody">
                            {(this.props.paytmQRStatus != -2 &&  this.props.paytmQRStatus < 2 )? (
                                <div class="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                                    &times;
                                </div>
                            ) : null}
                            <p class="modalTitle">
                                Scan QR Code for payment
                            </p>
                            <div class="customizationSection productHead">
                                <div className="payableAmount1Modal">
                                    <p>Collecting Payment</p><br/>
                                    <img style={{width: "51px", marginBottom: "-6px"}}
                                         src="../../../img/indian-ruppee-icon-white.png"/>
                                    <span style={{
                                        fontSize: "60px",
                                        margin: "0 0 0 15px",
                                        fontWeight: "bold"
                                    }}>{this.props.amountToPay}</span>
                                </div>
                                {this.props.showPaytmGIFLoader ? (
                                    <div class="loaderWrapper">
                                        <div class="load8 loader"></div>
                                        <p class="loaderMessage">Loading Paytm QR code for payment</p>
                                    </div>
                                ) : null}
                                <div style={{textAlign: "center"}}>
                                    <canvas id="paytmQr"></canvas>
                                </div>
                                {this.props.paytmQRStatus == 0 ? (
                                    <div style={{textAlign:"center", marginTop:"20px"}}>
                                        <p style={{fontSize:"21px"}}>Please scan above QR using your Paytm app. <br/> It will expire in 2 minutes.</p>
                                    </div>
                                ) : null}
                                {/*{this.props.paytmQRStatus == 1 ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"21px"}}>Waiting for the payment.</p>
                                        <img src="../../../img/payment/money-transfer.gif" style={{margin:"20px"}} />
                                    </div>
                                ) : null}*/}
                                {this.props.paytmQRStatus == 2 ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"21px"}}>Payment successful. Placing your order now.</p>
                                        <img src="../../../img/payment/payment-successful.gif" style={{margin:"20px"}} />
                                    </div>
                                ) : null}
                                {this.props.paytmQRStatus == -1 ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"21px"}}>Payment failed. Please try again.</p>
                                        <img src="../../../img/payment/order-failed.gif" style={{margin:"20px"}} />
                                    </div>
                                ) : null}
                                {(this.props.paytmQRStatus == 2 && this.props.orderFailed == true) ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"21px"}}>Payment was successful but order failed.<br/> Please try again or contact our customer support at 1800-120-2424.</p>
                                        <div style={{background:"#5e7e47", padding:"10px 30px", color:"#FFF", fontSize:"18px", borderRadius:"3px"}}
                                             onClick={this.placeOrder.bind(this)}>
                                            Try again.
                                        </div>
                                        <img src="../../../img/payment/order-failed.gif" style={{margin:"20px"}} />
                                    </div>
                                ) : null}
                                {this.props.paytmQRStatus == -3 ? (
                                    <div style={{textAlign:"center"}}>
                                        <p style={{fontSize:"21px"}}>Error loading QR code from Paytm. Please try again.</p>
                                        <div style={{background:"#5e7e47", padding:"10px 30px", color:"#FFF", fontSize:"18px", borderRadius:"3px"}}
                                             onClick={this.loadNewQR.bind(this)}>
                                            Try again.
                                        </div>
                                        <img src="../../../img/payment/order-failed.gif" style={{margin:"20px"}} />
                                    </div>
                                ) : null}
                            </div>
                        </div>
                    </div>
                </div>
            )
        } else {
            return null;
        }
    }
}