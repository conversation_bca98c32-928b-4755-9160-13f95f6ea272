import React from "react";
import {connect} from "react-redux";
import DesktopFooterLayout from "./DesktopFooterLayout";
import * as OutletLoginActions from "./../../actions/OutletLoginActions";
import * as UtilityActions from "./../../actions/UtilityActions";
import * as PaymentActions from "./../../actions/PaymentActions";
import appUtil from "./../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import StorageUtils from "../../utils/StorageUtils";

@connect((store) => {
    return {
        autoConfig: store.outletLoginReducer.autoConfig,
        kettleAuthDetails: store.outletLoginReducer.kettleAuthDetails,
        unitList: store.outletLoginReducer.unitList,
        selectedUnit: store.outletLoginReducer.selectedUnit,
        terminalList: store.outletLoginReducer.terminalList,
        selectedTerminal: store.outletLoginReducer.selectedTerminal,
        userId: store.outletLoginReducer.userId,
        password: store.outletLoginReducer.password,
        selectivePaymentMode: store.paymentReducer.selectivePaymentMode,
        cardPaymentMode: store.paymentReducer.cardPaymentMode
    };
})
export default class DesktopOutletLoginLayout extends React.Component {

    constructor() {
        super();
        this.state = {unit:{}, terminal:{}};
        this.loginUser = this.loginUser.bind(this);
        this.selectUnit = this.selectUnit.bind(this);
        this.selectTerminal = this.selectTerminal.bind(this);
    }

    selectUnit(event) {
        this.setState({unit: event.target.value});
        this.props.dispatch(OutletLoginActions.selectUnit(JSON.parse(event.target.value)));
    }

    selectTerminal(event) {
        this.setState({terminal: event.target.value});
        this.props.dispatch(OutletLoginActions.selectTerminal(event.target.value));
    }

    loginUser() {
        var userId = document.getElementById("userId").value;
        var password = document.getElementById("password").value;
        var selectedUnit = null, selectedTerminal = null;
        if(this.props.kettleAuthDetails != null && this.props.kettleAuthDetails.unitDetail != null) {
            selectedUnit = this.props.kettleAuthDetails.unitDetail;
        } else {
            selectedUnit = this.props.selectedUnit;
        }
        if(this.props.kettleAuthDetails != null && this.props.kettleAuthDetails.terminal != null) {
            selectedTerminal = this.props.kettleAuthDetails.terminal;
        } else {
            selectedTerminal = this.props.selectedTerminal;
        }
        if(userId == null || userId.trim().length == 0) {
            UtilityActions.showPopup("Please enter user id");
        } else if(password == null || password.trim().length == 0) {
            UtilityActions.showPopup("Please enter password");
        } else if(selectedUnit == null) {
            UtilityActions.showPopup("Please select unit");
        } else if(selectedTerminal == null) {
            UtilityActions.showPopup("Please select terminal");
        } else {
            this.props.dispatch(OutletLoginActions.loginOutlet(this.props, userId, password))
        }
    }

    setSelectivePaymentMode(mode) {
        this.props.dispatch(PaymentActions.setSelectivePaymentMode(mode));
    }

    setCardPaymentMode(mode) {
        this.props.dispatch(PaymentActions.setCardPaymentMode(mode));
    }

    componentWillMount() {
        this.props.dispatch(OutletLoginActions.initOutletLogin(this.props));
        if(this.props.selectivePaymentMode == null) {
            this.props.dispatch(PaymentActions.setSelectivePaymentMode("CASH"));
        }
        if(this.props.cardPaymentMode == null) {
            this.props.dispatch(PaymentActions.setCardPaymentMode("AGS"));
        }
    }

    render() {

        var unitListOptions = [], terminalListOptions = [];
        if(this.props.unitList != null && this.props.unitList.length > 0) {
            this.props.unitList.map(function (unit) {
                unitListOptions.push(
                    <option key={unit.id} value={JSON.stringify(unit)}>{unit.name}</option>
                );
            });
        }
        if(this.props.terminalList != null && this.props.terminalList.length > 0) {
            this.props.terminalList.map(function (terminal) {
                terminalListOptions.push(
                    <option key={terminal} value={terminal}>T{terminal}</option>
                );
            });
        }

        return (
            <div>
                <div style={{width:"1024px",margin:"auto"}}>
                    <div class="loginContainer">
                        {this.props.autoConfig ? (
                            <div class="alert alert-info">
                                {this.props.kettleAuthDetails.unitDetail.name} - T{this.props.kettleAuthDetails.terminal}
                            </div>
                        ):null}
                        <div>
                            <p>Select payment modes:</p>
                            {this.props.selectivePaymentMode == "CASH"  ? (
                                <div>
                                    <button onClick={this.setSelectivePaymentMode.bind(this, "CASH")} class="btn btn-primary">Cash</button>
                                    <button className="btn btn-default" onClick={this.setSelectivePaymentMode.bind(this, "CARDS")}>Cards</button>
                                    <button className="btn btn-default" onClick={this.setSelectivePaymentMode.bind(this, "ALL")}>All</button>
                                </div>
                            ):null}
                            {this.props.selectivePaymentMode == "CARDS" ? (
                                <div>
                                    <button className="btn btn-default" onClick={this.setSelectivePaymentMode.bind(this, "CASH")}>Cash</button>
                                    <button onClick={this.setSelectivePaymentMode.bind(this, "CARDS")}className="btn btn-primary">Cards</button>
                                    <button className="btn btn-default" onClick={this.setSelectivePaymentMode.bind(this, "ALL")}>All</button>
                                </div>
                            ):null}
                            {this.props.selectivePaymentMode == "ALL" ? (
                                <div>
                                    <button className="btn btn-default" onClick={this.setSelectivePaymentMode.bind(this, "CASH")}>Cash</button>
                                    <button className="btn btn-default" onClick={this.setSelectivePaymentMode.bind(this, "CARDS")}>Cards</button>
                                    <button onClick={this.setSelectivePaymentMode.bind(this, "ALL")}className="btn btn-primary">All</button>
                                </div>
                            ):null}
                        </div>

                        {this.props.selectivePaymentMode == "ALL" || this.props.selectivePaymentMode == "CARDS" ? (
                            <div>
                                <p>Select Card Payment mode:</p>
                                {this.props.cardPaymentMode == "AGS"  ? (
                                    <div>
                                        <button class="btn btn-primary" onClick={this.setCardPaymentMode.bind(this, "AGS")} >AGS</button>
                                        <button className="btn btn-default" onClick={this.setCardPaymentMode.bind(this, "EZETAP")}>Ezetap</button>
                                    </div>
                                ):null}
                                {this.props.cardPaymentMode == "EZETAP" ? (
                                    <div>
                                        <button className="btn btn-default" onClick={this.setCardPaymentMode.bind(this, "AGS")}>AGS </button>
                                        <button className="btn btn-primary" onClick={this.setCardPaymentMode.bind(this, "EZETAP")}>Ezetap </button>
                                    </div>
                                ):null}
                            </div>
                        ) : null}


                        <form name="lookupForm" action="#" onSubmit={this.loginUser.bind(this)}>
                            <div class="headLine">Login</div>
                            <div class="loginSectionTagline">Fill following details to login.</div>
                            <div class="contactContainer">
                                <input id="userId" type="tel" placeholder="User Id" maxLength="10" autoComplete="off"/>
                            </div>
                            <div class="contactContainer">
                                <input id="password" type="password" placeholder="Password" maxLength="30" autoComplete="off"/>
                            </div>
                            {(!this.props.autoConfig && !this.props.unitListLoading) ? (
                                <div>
                                    <div style={{padding:"5px 5px 10px 2px"}}>Select Unit</div>
                                    <div class="contactContainer">
                                        <select id="unitList" value={this.state.unit} onChange={this.selectUnit.bind(this)}>
                                            {unitListOptions}
                                        </select>
                                    </div>
                                    <div style={{padding:"5px 5px 10px 2px"}}>Select Terminal</div>
                                    <div class="contactContainer">
                                        <select id="terminalList" value={this.state.terminal} onChange={this.selectTerminal.bind(this)}>
                                            {terminalListOptions}
                                        </select>
                                    </div>
                                </div>
                            ): null}
                            {this.props.unitListLoading ? (
                                <div>
                                   Show Loader
                                </div>
                            ): null}
                            {!this.props.unitListLoading ? (
                                <div class="btn btn-primary" style={{marginTop: '16px'}}
                                     onClick={this.loginUser.bind(this)}>
                                    Login
                                </div>
                            ): null}
                        </form>
                    </div>
                </div>
                <DesktopFooterLayout/>
            </div>
        )
    }
}