import React from "react";
import {connect} from "react-redux";
import * as PaymentActions from "../../actions/PaymentActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import * as CustomerActions from "../../actions/CustomerActions";
import appUtil from "../../AppUtil";
import * as UtilityActions from "../../actions/UtilityActions";
import StorageUtils from "../../utils/StorageUtils";

@connect((store) => {
    return {
        showGiftCardPaymentModal: store.paymentReducer.showGiftCardPaymentModal,
        customer: store.customerReducer.customer,
        cart: store.cartManagementReducer.cart,
        otpMessage: store.outletMenuReducer.otpMessage,
        selectedOrderSource: store.outletMenuReducer.selectedOrderSource
    }
})
export default class GiftCardPaymentModal extends React.Component {

    constructor(props) {
        super(props);
    }

    componentWillMount() {

    }

    closeModal() {
        this.props.dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
        this.props.dispatch({type: "SET_OTP_MESSAGE", payload: null});
        this.props.dispatch({type: "SET_ORDER_SOURCE_MESSAGE", payload: null});
    }

    verifyOtp() {
        const otp = document.getElementById("giftCardOtpEntered").value;
        if (otp.length == 4) {
            let customer = StorageUtils.getCustomerDetail();
            this.props.customer.otpEntered = otp;
            this.props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: this.props.customer});
            StorageUtils.setCustomerDetail(this.props.customer);
            this.props.dispatch(PaymentActions.verifyOtpForGiftCard(this.props))
        }
    }

    getOtpSource() {
        return StorageUtils.getCustomerDetail().otpSource;
    }

    resendOtp() {
        this.props.dispatch(PaymentActions.generateOTPForRedeemChai(this.props.customer.contact));
    }

    render() {
        if (this.props.showGiftCardPaymentModal) {
            return (
                <div className="modal" style={{background: "rgba(0, 0, 0, 0.3)"}}>
                    <div className="modalBody" style={{
                        top: "30%",
                        padding: "20px",
                        width: "95%",
                        height: "100%",
                        left: "28px",
                        borderRadius: "50px",
                        border: "3px solid rgb(94, 126, 1)",
                        boxShadow: "0 0 3px 3px black"
                    }}>
                        <span></span>
                        <div className="modalCloseBtn" style=
                        {{right: "50px", top: "53px", fontSize: "50px", color: "white"}}
                         onClick={() => this.closeModal()}>
                            &times;
                        </div>

                        <div className="modalTitle" style={{
                            fontSize: "40px",
                            marginTop: "10px",
                            padding: "30px",
                            color: "white",
                            background: "#5e7e47",
                            borderRadius: "35px",
                            height: "auto"
                        }}>
                            {this.getOtpSource() == "GiftCard" &&
                            <span>OTP To Redeem Gift Card</span>
                            }
                            {this.getOtpSource() == "RedeemChai" &&
                            <span>OTP To Redeem {this.props.customer.chaiRedeemed} Chai</span>
                            }
                            {this.getOtpSource() == "ChaayosCash" &&
                            <span>OTP To Redeem Chaayos Cash</span>
                            }

                        </div>
                        <div style={{textAlign: "center", marginTop: "20%"}}>
                            <input style={{
                                fontSize: "45px",
                                borderRadius: "15px",
                                border: "7px solid rgb(94, 126, 71)",
                                padding: "20px",
                                textAlign: "center", color: "rgb(94, 126, 1)"
                            }} id="giftCardOtpEntered" type="tel"
                                   placeholder="Enter OTP here" maxLength="4"
                                   autoComplete="off" autoFocus="true"
                                   onKeyUp={() => this.verifyOtp()}/>
                            <div style={{
                                background: "rgb(94, 126, 71)",
                                padding: "25px",
                                width: "60%",
                                borderRadius: "15px",
                                color: "white",
                                margin: "auto",
                                fontSize: "40px",
                                marginTop: "30px",
                            }} onClick={() => this.resendOtp()}>Resend
                            </div>
                        </div>
                        <div style={{textAlign: "center", fontSize: "35px", marginTop: "30px"}}>
                            {this.props.otpMessage}
                        </div>

                        {/*<div className="modalTitle" style={{fontSize: "25px"}}>*/}
                        {/*Pay By Gift Card*/}
                        {/*</div>*/}
                        {/*<div class="strip">*/}
                        {/*Amount To Pay: <span>{this.props.amountToPay}</span>*/}
                        {/*</div>*/}
                        {/*<div>*/}
                        {/*Gift Card balance: {this.props.customer.giftCardBalance}*/}
                        {/*</div>*/}
                        {/*<div style={{textAlign: "center", margin: "20px"}}>*/}
                        {/*<input style={{fontSize: "25px"}} id="otpEntered" type="tel" placeholder="Enter OTP here" maxLength="4"*/}
                        {/*autoComplete="off" autoFocus="true" onKeyUp={() => this.verifyOtp()}/>*/}
                        {/*</div>*/}
                    </div>
                </div>
            );
        } else {
            return null;
        }
    }
}