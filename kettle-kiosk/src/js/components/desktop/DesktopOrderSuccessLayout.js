/**
 * Created by Chaayos on 10-12-2016.
 */
import React from "react";
import { connect } from "react-redux";
import browserHistory from "react-router/lib/browserHistory";
import appUtil from "../../AppUtil";
import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        currentOrderId:store.orderManagementReducer.currentOrderId,
        customer: store.customerReducer.customer,
        setStartNewOrderTimeout: store.cartManagementReducer.setStartNewOrderTimeout
    };
})
export default class DesktopOrderSuccessLayout extends React.Component {

    constructor(props){
        super(props);
        this.state = {
        };
        this.startNewOrder = this.startNewOrder.bind(this);
    }

    startNewOrder(){
        this.props.dispatch(OrderManagementActions.startNewOrder(this.props));
    }

    componentWillMount(){
        window.scrollTo(0, 0);

    }

    componentDidMount(){
        trackUtils.trackPageView({page:"orderDetail",device:"desktop",custom:true});
        this.props.dispatch(OrderManagementActions.autoMoveStartNewOrder());
    }

    componentWillUnmount(){
        //this.props.dispatch(OrderManagementActions.removeStatusTimeouts(this.props.orderStatusTimeouts))
    }

    render (){

        return(
            <div style={{backgroundImage:"url(../../../img/backgrounds/order-success.png"}}>
                <div class="colouredHead">
                    <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                <div class="desktopPageContainer" style={{background:"none", boxShadow:"none"}}>
                    <div class="text-center">
                        <img src="../../../img/cutting-chai.png" style={{margin:"220px 0 0 0"}} />
                        <div style={{fontSize:"83px", textTransform:"uppercase", fontWeight:"bold", marginBottom:"-21px"}}>
                            Thank You
                        </div>
                        <div style={{fontSize:"52px"}}>
                            Your Order is placed
                        </div>
                        <div style={{fontSize:"32px", margin:"100px auto", border:"#333 2px solid", borderRadius:"20px", width:"450px", padding:"15px"}}>
                            Order ID <br />
                            <span style={{color:"#5e7e47", fontSize:"31px", fontWeight:"bold"}}>{this.props.currentOrderId != null ? this.props.currentOrderId.id:""}</span>
                        </div>
                        <div style={{fontSize:"21px", margin:"10px auto", width:"715px", textAlign:"left"}}>
                            You will get your order receipt via SMS on your Chaayos registered mobile number and via email on your Chaayos registered email address.
                            Additionally, you can ask for hard copy of receipt from cafe staff.
                        </div>
                        <div class="btn btn-primary" style={{fontSize: "33px", margin: "100px 0", fontWeight: "bold", borderRadius: "10px", display: "inline-block"}}
                             onClick={this.startNewOrder.bind(this)}>Start New Order
                        </div>
                    </div>
                </div>
                <DesktopFooterLayout />
            </div>
        )
    }
}