/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileContactLayout from "./mobile/MobileContactLayout";
import DesktopContactLayout from "./desktop/DesktopContactLayout";

export default class ContactLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileContactLayout props={this.props} />
            )
        } else {
            return (
                <DesktopContactLayout props={this.props} />
            )
        }
    }
}