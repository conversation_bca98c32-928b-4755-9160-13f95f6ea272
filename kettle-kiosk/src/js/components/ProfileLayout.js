import React from "react";
import appUtil from "../AppUtil";
import MobileProfileLayout from "./mobile/MobileProfileLayout";
import DesktopProfileLayout from "./desktop/DesktopProfileLayout";

export default class ProfileLayout extends React.Component {
	render (){
		if(appUtil.isMobile()){
			return(
				<MobileProfileLayout props={this.props} />
			)
		}else{
			return(
				<DesktopProfileLayout props={this.props} />
			)
		}
	}
}