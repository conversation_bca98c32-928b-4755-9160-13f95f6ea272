import React from "react";
import ReactDOM from "react-dom";
import { Router, Route, browserHistory } from "react-router";
// import IndexRoute from "react-router/lib/IndexRoute";
import { Provider } from "react-redux";
// import * as firebase from "firebase";
//import Promise from 'promise-polyfill';
import customPolyFills from "./CustomPolyFills";
import store from "./store";
import MainRoutingLayout from "./components/MainRoutingLayout";
// import HomeLayout from "./components/HomeLayout";
// import CartLayout from "./components/CartLayout";
// import AddressesLayout from "./components/AddressesLayout";
// import NewAddressLayout from "./components/NewAddressLayout";
// import OrdersLayout from "./components/OrdersLayout";
// import OrderSummaryLayout from "./components/OrderSummaryLayout";
// import TermsLayout from "./components/TermsLayout";
// import PrivacyLayout from "./components/PrivacyLayout";
// import ProfileLayout from "./components/ProfileLayout";
// import AboutLayout from "./components/AboutLayout";
// import ContactLayout from "./components/ContactLayout";
// import PaymentProcessLayout from "./components/PaymentProcessLayout";
// import MobileLocationLayout from "./components/mobile/MobileLocationLayout";
import DesktopOutletLoginLayout from "./components/desktop/DesktopOutletLoginLayout";
import DesktopPaymentModesLayout from "./components/desktop/DesktopPaymentModesLayout";
import DesktopLoginLayout from "./components/desktop/DesktopLoginLayout";
import DesktopOutletMenuLayout from "./components/desktop/DesktopOutletMenuLayout";
import DesktopOrderSuccessLayout from "./components/desktop/DesktopOrderSuccessLayout";
import OrderSourceLayout from "./components/desktop/OrderSourceLayout";

const app = document.getElementById('app');

(function () {
    // promise polyfill
    if (!window.Promise) {
        window.Promise = Promise;
    }
    customPolyFills.initiatePolyFills();
    document.getElementsByClassName("splash")[0]!=null?document.getElementsByClassName("splash")[0].className += " inactive":null;
    setTimeout(function () {
        if(document.getElementsByClassName("splash")[0]!=null){
            document.getElementsByClassName("splash")[0].parentNode.removeChild(document.getElementsByClassName("splash")[0]);
        }
    }, 2500);

    //dev config
    /*var config = {
        apiKey: "AIzaSyDaeMgQZr_HDfYY1SdMXUOptBGshrh-12E",
        authDomain: "chaayos-webapp.firebaseapp.com",
        databaseURL: "https://chaayos-webapp.firebaseio.com",
        storageBucket: "chaayos-webapp.appspot.com",
        messagingSenderId: "1022795285764"
    };
    firebase.initializeApp(config);*/
    //prod config
    /*var config = {
        apiKey: "AIzaSyAUuPwz1fjjdFzVgkDrfb14zKm33w_8LuY",
        authDomain: "chaayos-prod-web-app.firebaseapp.com",
        databaseURL: "https://chaayos-prod-web-app.firebaseio.com",
        projectId: "chaayos-prod-web-app",
        storageBucket: "chaayos-prod-web-app.appspot.com",
        messagingSenderId: "************"
    };
    firebase.initializeApp(config);*/
})();

ReactDOM.render(
    <Provider store={store}>
        <Router history={browserHistory}>
            <Route path="/" component={MainRoutingLayout}>
                {/*<IndexRoute component={HomeLayout}/>*/}
                <Route path="menu" component={DesktopOutletMenuLayout}/>
                <Route path="outletLogin" component={DesktopOutletLoginLayout} />
                <Route path="paymentModes" component={DesktopPaymentModesLayout}/>
                <Route path="login" component={DesktopLoginLayout}/>
                <Route path="orderSuccess" component={DesktopOrderSuccessLayout}/>
                <Route path="orderSource" component={OrderSourceLayout}/>
                {/*<Route path="search" component={MobileLocationLayout} />*/}
                {/*<Route path="cart" component={CartLayout}/>
                <Route path="addresses" component={AddressesLayout}/>
                <Route path="newAddress" component={NewAddressLayout}/>*/}
                {/*<Route path="orders" component={OrdersLayout}/>
                <Route path="orderDetail" component={OrderSummaryLayout} />*/}
                {/*<Route path="terms" component={TermsLayout}/>
                <Route path="privacy" component={PrivacyLayout}/>
                <Route path="account" component={ProfileLayout}/>
                <Route path="about" component={AboutLayout}/>
                <Route path="contact" component={ContactLayout}/>*/}
                {/*<Route path="payProcess" component={PaymentProcessLayout}/>*/}
            </Route>
        </Router>
    </Provider>,
    app
);