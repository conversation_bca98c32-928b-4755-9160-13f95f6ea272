/**
 * Created by Chaayos on 01-12-2016.
 */
import axios from "axios";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import { browserHistory } from "react-router";
import * as UtilityActions from "./UtilityActions";
import * as CartManagementActions from "./CartManagementActions";
import trackUtils from "../utils/TrackUtils";
// import * as PubNubActions from "./PubNubActions";


export function setKettleAuthDetails(auth) {
    return dispatch => {
        dispatch({type:"SET_KETTLE_AUTH_DETAILS",payload:auth});
    }
}

export function initOutletLogin(props) {
    return dispatch => {
        dispatch(resetScreen());
        if(props.kettleAuthDetails == null || props.kettleAuthDetails.unitDetail == null) {
            dispatch({type:"SET_AUTO_CONFIG", payload: false});
            dispatch(loadOutletList());
        } else {
            dispatch({type:"SET_AUTO_CONFIG", payload: true});
        }
    }
}

export function resetScreen() {
    return dispatch => {
        dispatch({type:"SET_USER_ID",payload:null});
        dispatch({type:"SET_PASSWORD",payload:null});
        dispatch({type:"SET_KETTLE_AUTH_DETAILS",payload:StorageUtils.getKettleAuthDetails()});
    }
}

export function loadOutletList() {
    return dispatch => {
        dispatch({type: "SET_UNIT_LIST_LOADING", payload: true});
        axios({
            method: "GET",
            url: apis.getUrls().unitMetadata.allUnits+ '?category=CAFE',
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(response.data != null) {
                dispatch({type:"SET_UNIT_LIST", payload:response.data});
                //dispatch({type:"SET_SELECTED_UNIT", payload:response.data[0]});
                dispatch(selectUnit(response.data[0]));
            } else {
                throw new Error("Unit list could not be loaded.");
            }
            dispatch({type: "SET_UNIT_LIST_LOADING", payload: false});
        }).catch(function (error) {
            console.log(error);
            dispatch({type: "SET_UNIT_LIST_LOADING", payload: false});
            UtilityActions.showPopup("Error loading outlet list.", "ERROR", 4000)
        });
    }
}

export function selectUnit(unitDetail) {
    return dispatch => {
        dispatch({type:"SET_SELECTED_UNIT",payload:unitDetail});
        var terminalList = [];
        for(var i=1; i<=unitDetail.noOfTakeawayTerminals; i++) {
            terminalList.push(i);
        }
        dispatch({type:"SET_TERMINAL_LIST",payload:terminalList});
        dispatch(selectTerminal(terminalList[0]));
    }
}

export function selectTerminal(terminal) {
    return dispatch => {
        dispatch({type:"SET_SELECTED_TERMINAL",payload:terminal});
    }
}

export function loginOutlet(props, userId, password) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader("Please wait while we log you in."));
        var request = {userId:userId, unitId:props.selectedUnit.id,password:password,terminalId:props.selectedTerminal,screenType:"CUSTOMER",application:"KETTLE_CRM"};
        axios({
            method: "POST",
            url: apis.getUrls().users.login,
            data: JSON.stringify(request),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(response.data != null && response.data.jwtToken != null){
                var kettleAuthDetails = {unitDetail:props.selectedUnit, terminal:props.selectedTerminal, authToken:response.data.jwtToken};
                StorageUtils.setKettleAuthDetails(kettleAuthDetails);
                StorageUtils.setTerminalId(props.selectedTerminal);
                dispatch({type:"SET_KETTLE_AUTH_DETAILS", payload:kettleAuthDetails});
                browserHistory.push("/menu");
                // dispatch(PubNubActions.setupPubNub());
                /*axios({
                    method: "POST",
                    url: apis.getUrls().order.getCashCardOffer,
                    data: JSON.stringify(props.selectedUnit.id),
                    headers: {'Content-Type': 'application/json'}
                }).then(function (offerResponse) {
                    dispatch({type: "SET_GIFT_CARD_OFFER", payload: offerResponse.data});
                    browserHistory.push("/menu");
                    dispatch(PubNubActions.setupPubNub());
                }).catch(function (error) {
                    dispatch(UtilityActions.hideFullPageLoader());
                    UtilityActions.showPopup("Error in login. Please try again.", 4000);
                    browserHistory.push("/menu");
                    dispatch(PubNubActions.setupPubNub());
                });*/
            } else {
                UtilityActions.showPopup("Error in login. Please try again.", 4000);
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch(UtilityActions.hideFullPageLoader());
            UtilityActions.showPopup("Error in login. Please try again.", 4000);
        });
    }
}



export function setOTPResendSeconds(sec) {
    return dispatch => {
        dispatch({type:"SET_OTP_RESEND_SECONDS",payload:sec});
    }
}