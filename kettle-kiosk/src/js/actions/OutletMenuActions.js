import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import _ from "lodash";
import {browserHistory} from "react-router";
import * as CartManagementActions from "./CartManagementActions";
import * as CustomizationModalActions from "./CustomizationModalActions";
import * as LocalityActions from "./LocalityActions";
import * as UtilityActions from "./UtilityActions";
import trackUtils from "../utils/TrackUtils";

// import * as PubNubActions from "./PubNubActions";

export function getUnitProducts(criteria, city, locality, outlet) {
    return dispatch => {
        if ((criteria == "DELIVERY" && !appUtil.checkEmpty(locality)) ||
            ((criteria == "DINE_IN" || criteria == "TAKE_AWAY") && !appUtil.checkEmpty(outlet))) {
            let url;
            let method;
            let reqData;
            if (criteria == "DELIVERY") {
                url = apis.getUrls().neoCache.getDeliveryUnit;
                method = "POST";
                reqData = JSON.stringify({city: city.city, locality: locality.label});
            } else {
                url = apis.getUrls().unitCache.getUnit + "?unitId=" + outlet.value;
                method = "GET";
                reqData = {};
            }
            dispatch({type: "LOAD_UNIT_PRODUCTS_PENDING"});
            axios({
                method: method,
                url: url,
                data: reqData,
                headers: {"Content-Type": "application/json"}
            }).then(function (response) {
                if (!appUtil.checkEmpty(response.data) && response.status == 200) {
                    let unitData = response.data.unit;
                    appUtil.setTaxes(unitData.taxes);
                    var skipDeliveryPackaging = {
                        delivery: response.data.skipDeliveryCharge,
                        packaging: response.data.skipPackagingCharge
                    };
                    dispatch({type: "SET_SKIP_DELIVERY_PACKAGING", payload: skipDeliveryPackaging});

                    StorageUtils.setUnitDetails({id: unitData.id, name: unitData.name});
                    appUtil.setInclusiveTaxPrices(unitData, appUtil.isInterstate(unitData, city));
                    dispatch({type: "LOAD_UNIT_PRODUCTS_FULFILLED", payload: unitData});
                    CartManagementActions.setDeliveryAndPackagingItem(unitData);
                    dispatch(CartManagementActions.initCart(unitData, skipDeliveryPackaging, appUtil.isInterstate(unitData, city)));
                    dispatch(initAutoRecipeLoad(unitData));
                    dispatch(loadSpecialMenu());
                    dispatch(loadUnitInventory(unitData)); //TODO add this for inventory call
                    if (response.data.error && response.data.error > 0) {
                        dispatch(UtilityActions.showPopup(response.data.message, "info", 4000));
                        try {
                            trackUtils.trackUnitClosed({
                                city: city.city,
                                locality: !appUtil.checkEmpty(locality) ? locality.label : "",
                                outlet: unitData.name,
                                type: type,
                                msg: response.data.message
                            });
                        } catch (e) {
                        }
                    }
                    //dispatch(loadBoughtByYou());
                    dispatch(loadProductTags());
                    if (!appUtil.checkEmpty(StorageUtils.getCartDetail())) {
                        let crt = StorageUtils.getCartDetail();
                        crt.orderDetail.unitId = unitData.id;
                        crt.orderDetail.unitName = unitData.name;
                        dispatch({type: "UPDATE_CART", payload: crt});
                    }
                    dispatch(fetchGiftCardOffers(unitData.id));
                } else {
                    dispatch(reselectLocalityMeta(city));
                }
            }).catch(function (error) {
                console.log(error);
                dispatch(reselectLocalityMeta(city));
            });
        } else {
            browserHistory.push("/");
            dispatch(UtilityActions.showPopup("Please select locality or outlet."));
        }
    }
}

const trackDeliveryAnalytics = (city, locality, outlet, type) => {
    try {
        trackUtils.trackLocalityMetadata({
            city: city,
            locality: !appUtil.checkEmpty(locality) ? locality.label : "",
            outlet: outlet,
            type: type
        });
    } catch (e) {
    }

};

const trackTakeawayAnalytics = (city, outlet, type) => {
    try {
        trackUtils.trackLocalityMetadata({city: city, locality: "", outlet: outlet, type: type});
    } catch (e) {
    }

};

const reselectLocalityMeta = (cityObj) => {
    return dispatch => {
        dispatch(LocalityActions.selectCity(cityObj.city, cityObj.state));
        dispatch(UtilityActions.showPopup("Please select locality/outlet again!"));
        if (appUtil.isMobile()) {
            browserHistory.push("/");
        } else {
            browserHistory.push("/menu");
            dispatch(LocalityActions.toggleLocationWrapper(true));
        }
    }
};

const initAutoRecipeLoad = (unit) => {
    return dispatch => {
        var products = [];
        var filtered = _.filter(unit.products, (o) => {
            return o.strategy != 5 && [10, 12, 50].indexOf(o.id) < 0
        });
        _.map(filtered, (prod) => {
            var product = {productId: prod.id, recipes: {}};
            _.map(prod.prices, (price) => {
                product.recipes[price.dimension] = null;
            });
            products.push(product);
        });
        dispatch({type: "SET_RECIPE_PRODUCTS", payload: products});
        dispatch(autoRecipeLoad(products, 0));
    }
};

const autoRecipeLoad = (products, index) => {
    return dispatch => {
        if (products.length > index) {
            axios({
                method: "POST",
                url: apis.getUrls().neoCache.productRecipes,
                data: JSON.stringify([products[index]]),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                var productRecipe = {productId: response.data.productId, recipes: {}};
                response.data.recipes.map(recipe => {
                    productRecipe.recipes[recipe.dimension.code] = recipe;
                });
                dispatch({type: "SILENT_LOAD_PRODUCT_RECIPE", payload: productRecipe});
                dispatch(autoRecipeLoad(products, index + 1));
            }).catch((error) => {
                dispatch(autoRecipeLoad(products, index + 1));
            });
        }
    }
};

const loadSpecialMenu = () => {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().neoCache.specialMenu,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "SPECIAL_MENU_LOADED", payload: response.data});
        }).catch(function (error) {
            dispatch({type: "SPECIAL_MENU_REJECTED", payload: error});
        });
    }
};

const loadProductTags = () => {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().neoCache.productTags,
            data: {},
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "PRODUCT_TAGS_LOADED", payload: response.data});
        }).catch(function (error) {
        });
    }
};

const loadBoughtByYou = () => {
    return dispatch => {
        if (StorageUtils.getSessionId() != null) {
            axios({
                method: "POST",
                url: apis.getUrls().customer.boughtByYou,
                data: JSON.stringify(StorageUtils.getCustomerId()),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response && response.data) {
                    dispatch({type: "BOUGHT_BY_YOU_LOADED", payload: response.data});
                }
            }).catch(function (error) {

            });
        }
    }
};

export function loadUnitInventory(unit) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().webInventory.unit,
            data: JSON.stringify(StorageUtils.getUnitDetails().id),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data) {
                dispatch({type: "INVENTORY_FULFILLED", payload: response.data});
                dispatch(CartManagementActions.updateStockInCart(StorageUtils.getCartDetail(), response.data));
                dispatch(CartManagementActions.setCartStock(response.data));
                trackInitialStockout(unit, response.data);
            }
        }).catch(function (error) {
            dispatch({type: "INVENTORY_REJECTED", payload: error});
        });
    }
}

export function trackInitialStockout(unit, stock) {
    if (unit && unit.products) {
        let productNames = [];
        unit.products.map((product) => {
            if (stock[product.id] == 0) {
                productNames.push(product.name);
            }
        });
        productNames.length > 0 ? trackUtils.trackInitialStockout(productNames.join(",")) : null;
    }
}

export function getTags() {
    return dispatch => {
        dispatch({type: "LOAD_TAGS_PENDING"});
        axios({
            method: "GET",
            url: apis.getUrls().neoCache.tags,
            data: {},
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "LOAD_TAGS_FULFILLED", payload: response.data});
        }).catch(function (error) {
            dispatch({type: "LOAD_TAGS_REJECTED", payload: error});
        });
    }
}

export function trackSelectionStockout(productName) {
    return dispatch => {
        !appUtil.checkEmpty(productName) ? trackUtils.trackSelectionStockout(productName) : null;
    }
}

export function loadProductRecipe(unit, product, cartItem) {
    return dispatch => {
        if (!product.recipesLoaded) {
            let requestData = [{productId: [10, 11, 12, 50].indexOf(product.id) >= 0 ? 11 : product.id, recipes: {}}];
            _.forEach(product.prices, (price) => {
                requestData[0].recipes[price.dimension] = null;
            });
            dispatch({type: "LOAD_RECIPE_TO_PRODUCT_PENDING", payload: product.id});
            axios({
                method: "POST",
                url: apis.getUrls().neoCache.productRecipes,
                data: JSON.stringify(requestData),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                let recipesList = response.data;
                /*var constituentProductIds = []; // composite product case
                if (product.type == 8) {
                    recipesList.map((productRecipe) => {
                        productRecipe.recipes["None"].ingredient.compositeProduct.details.map((detail) => {
                            detail.menuProducts.map((product) => {
                                unit.products.map((unitProduct) => {
                                    if (unitProduct.id == product.product.productId && !unitProduct.recipesLoaded) {
                                        constituentProductIds.push(product.product.productId);
                                    }
                                });
                            });
                        });
                    });
                }
                if(constituentProductIds.length>0) {
                    //load constituent product recipes which have not been loaded
                    axios({
                        method: "POST",
                        url: apis.getUrls().neoCache.productRecipes,
                        data: JSON.stringify(constituentProductIds),
                        headers: {'Content-Type': 'application/json'}
                    }).then(function (response) {
                        recipesList = recipesList.concat(response.data);
                        console.log(recipesList);
                        product.recipesLoaded = true;
                        dispatch(CustomizationModalActions.updateCustomizationProduct(product));
                        dispatch({type: "LOAD_RECIPE_TO_PRODUCT_FULFILLED", payload: recipesList});
                        dispatch(setCustomizationActions(product, recipesList, cartItem));
                    }).catch(function (error) {
                        dispatch({type: "LOAD_RECIPE_TO_PRODUCT_REJECTED"});
                    });
                }else {*/
                // product recipe List object returned
                product.recipesLoaded = true;
                dispatch(CustomizationModalActions.updateCustomizationProduct(product));
                dispatch({type: "LOAD_RECIPE_TO_PRODUCT_FULFILLED", payload: response.data});
                dispatch(setCustomizationActions(product, recipesList, cartItem));
                /*}*/
            }).catch(function (error) {
                dispatch({type: "LOAD_RECIPE_TO_PRODUCT_REJECTED"});
            });
        }
    }
}

const setCustomizationActions = (product, recipesList, ci) => {
    return dispatch => {
        //set customisation code
        var customizationType = ci == null ? "MENU_ITEM" : "CART_ITEM";
        if (product.customize) {
            if (product.prices.length > 1) {
                //open customization modal
                dispatch(CustomizationModalActions.showCustomizationModal(product, customizationType, ci));
                if (product.id == 11 && ci == null) {
                    product.id = 10;
                    product.name = "Desi Chai";
                }
                dispatch(CustomizationModalActions.updateCustomizationProduct(product));
            } else {
                if (product.type != 8) {
                    var count = 0;
                    for (var key in recipesList[0].recipes) {
                        if (product.prices[0].dimension == key) {
                            count += recipesList[0].recipes[key].customizationCount;
                        }
                    }
                    if (count > 1) {
                        dispatch(CustomizationModalActions.showCustomizationModal(product, customizationType, ci));
                    } else {
                        //show customization below product
                        dispatch({type: "SET_SHOW_CUSTOMIZATION", payload: product});
                    }
                } else {
                    dispatch(CustomizationModalActions.showCustomizationModal(product, customizationType, ci));
                }
            }
        } else {
            if (product.prices.length > 1) {
                //show dimensions below product
                dispatch({type: "SET_SHOW_CUSTOMIZATION", payload: product});
            } else {
                //add item to cart
                var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                var cartItem = retObj.product;
                cartItem.selectedDimension = retObj.product.prices[0];
                cartItem.prices = [];
                dispatch(CartManagementActions.addItemToCart({cartItem: cartItem, addons: [], quantity: 1}));
            }
        }
    }
};

export function discardUnit() {
    return dispatch => {
        dispatch({type: "DISCARD_UNIT"});
    }
}

export function toggleCustomization(product) {
    return dispatch => {
        product.showCustomization = !product.showCustomization;
        dispatch({type: "SET_SHOW_CUSTOMIZATION", payload: product});
    }
}

export function setInitMenu(val) {
    return dispatch => {
        dispatch({type: "SET_INIT_MENU", payload: val});
    }
}

export function setShowInstructionModal(val) {
    return dispatch => {
        if (val === true) {
            document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
        } else {
            document.getElementsByTagName("BODY")[0].style.overflow = "auto";
        }
        dispatch({type: "SET_SHOW_INSTRUCTION_MODAL", payload: val});
    }
}

export function setShowCouponModal(val) {
    return dispatch => {
        if (val === true) {
            document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
        } else {
            document.getElementsByTagName("BODY")[0].style.overflow = "auto";
        }
        dispatch({type: "SET_COUPON_LOGIN", payload: false});
        dispatch({type: "SET_SHOW_COUPON_MODAL", payload: val});
        dispatch({type: "SHOW_CONTACT_SECTION", payload: true});
    }
}

export function setShowRedeemChaayosCashModal(val) {
    return dispatch => {
        if (val === true) {
            document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
        } else {
            document.getElementsByTagName("BODY")[0].style.overflow = "auto";
        }
        dispatch({type: "SET_SHOW_REDEEM_CHAAYOS_CASH_MODAL", payload: val});
    }
}

export function fetchGiftCardOffers(unitId) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().order.getCashCardOffer,
            data: JSON.stringify(unitId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (offerResponse) {
            dispatch({type: "SET_GIFT_CARD_OFFER", payload: offerResponse.data});
            browserHistory.push("/menu");
            // dispatch(PubNubActions.setupPubNub());
        }).catch(function (error) {
            dispatch(UtilityActions.hideFullPageLoader());
            UtilityActions.showPopup("Error in login. Please try again.", 4000);
            browserHistory.push("/menu");
            // dispatch(PubNubActions.setupPubNub());
        });
    }
}