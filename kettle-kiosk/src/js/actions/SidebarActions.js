import { browserHistory } from "react-router";
import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import * as UtilityActions from "./UtilityActions";
import * as CartManagementActions from "./CartManagementActions";
import trackUtils from "../utils/TrackUtils";

export function toggleSidebar() {
    return dispatch => {
        dispatch({type: "TOGGLE_SIDEBAR"});
    }
}
export function logout() {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader("Logging out. Please wait..."));
        var authData = StorageUtils.getAuthDetail();
        axios({
            method: "POST",
            url: apis.getUrls().customer.logout,
            data: JSON.stringify({deviceKey:authData.deviceKey, sessionKey:authData.sessionKey}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            const cartDetail = response.data.cartDetail;
            response.data.cartDetail = null;
            StorageUtils.setAuthDetail(response.data);
            trackUtils.logout();
            dispatch(CartManagementActions.updateCart(cartDetail));
            dispatch({type: "SET_LOGIN_STATUS", payload:false});
            dispatch({type: "RESET_LOGIN"});
            dispatch({type: "HIDE_FULLPAGE_LOADER"});
            browserHistory.push("/menu");
            dispatch(UtilityActions.showPopup("Logout Successful.","info"));
        }).catch(function (error) {
            dispatch(UtilityActions.showPopup("Logout failed. Try again!","error"));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function setLoginStatus(){
    return dispatch => {
        var status = StorageUtils.getAuthDetail()!=null?(StorageUtils.getAuthDetail().sessionKey != null):false;
        dispatch({type: "SET_LOGIN_STATUS", payload: status});
    }
}