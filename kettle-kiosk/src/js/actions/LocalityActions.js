import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import trackUtils from "../utils/TrackUtils";

export function loadCitiesList(criteria){
    return dispatch => {
        dispatch({type: "LOAD_CITIES_PENDING"});
        axios({
            method:"GET",
            url:apis.getUrls().neoCache.getCities,
            data: {},
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            var cities = [];
            Object.keys(response.data).map(key => {
                cities.push(key);
            });
            var localityData = StorageUtils.getLocalityMetadata();
            localityData = localityData==null?{}:localityData;
            if(appUtil.checkEmpty(localityData.city)){
                localityData = {...localityData, city:cities[0], state:response.data[cities[0]]};
            }else if(appUtil.checkEmpty(localityData.state)){
                localityData = {...localityData, state:response.data[localityData.city]};
            }
            localityData = {...localityData, criteria:criteria};
            StorageUtils.setLocalityMetadata(localityData);
            dispatch({type:"LOAD_CITIES_FULFILLED", payload:cities});
            dispatch({type:"SET_CITY_STATE_MAP", payload:response.data});
            dispatch({type:"SELECT_CITY", payload:{city:localityData.city, state:localityData.state}});
            if(criteria=="DELIVERY"){
                dispatch(setShowLocality(true));
            }else{
                dispatch(setShowOutlet(true));
            }
            if(!appUtil.isMobile()){
                if(criteria=="DELIVERY"){
                    dispatch(loadLocalitiesList(localityData.city));
                }else{
                    dispatch(loadOutletsList(localityData.city));
                }
            }
        }).catch(function (error) {
            dispatch({type:"LOAD_OUTLETS_REJECTED", payload:error});
        });
    }
}

export function loadLocalitiesList(city){
    return dispatch => {
        dispatch({type: "LOAD_LOCALITIES", 
            payload: axios({
                    method:"POST",
                    url:apis.getUrls().neoCache.getLocalities,
                    data:JSON.stringify(city),
                    headers: {'Content-Type': 'application/json'}
                })
        });
    }
}

export function loadOutletsList(city){
    return dispatch => {
        dispatch({type: "LOAD_OUTLETS",
            payload: axios({
                    method:"POST",
                    url:apis.getUrls().neoCache.getTakeawayUnits,
                    data:JSON.stringify(city),
                    headers: {'Content-Type': 'application/json'}
                })
        });
    }
}

export function selectCity(city, state){
    return dispatch => {
        dispatch({type: "SELECT_CITY", payload: {city:city,state:state}});
        var data = StorageUtils.getLocalityMetadata();
        if(data==null){
            data = {};
        }
        data.city = city;
        data.locality = null;
        data.outlet = null;
        data.state = state;
        StorageUtils.setLocalityMetadata(data);
        trackUtils.initWebEngageParams(null,city,null,null);
    }
}

export function setCity(city, state){
    return dispatch => {
        dispatch({type: "SET_CITY", payload: {city:city,state:state}});
        trackUtils.initWebEngageParams(null,city,null,null);
    }
}

export function selectLocality(locality){
    return dispatch => {
        dispatch({type: "SELECT_LOCALITY", payload: locality});
        var data = StorageUtils.getLocalityMetadata();
        data.locality = locality;
        StorageUtils.setLocalityMetadata(data);
        try{trackUtils.initWebEngageParams(null,null,locality.label,null)}catch(e){};
    }
}

export function selectOutlet(outlet){
    return dispatch => {
        dispatch({type: "SELECT_OUTLET", payload: outlet});
        var data = StorageUtils.getLocalityMetadata();
        data.outlet = outlet;
        StorageUtils.setLocalityMetadata(data);
        try{
            trackUtils.initWebEngageParams(null,null,null,outlet.label);
            trackUtils.trackLocalityMetadata({city:data.city,locality:!appUtil.checkEmpty(data.locality)?data.locality.label:null,outlet:!appUtil.checkEmpty(data.outlet)?data.outlet.label:null,type:1});
        }catch(e){}
    }
}

export function hideError(){
    return dispatch => {
        dispatch({type: "HIDE_ERROR"});
    }
}

export function setCriteria(criteria){
    return dispatch => {
        dispatch({type: "SET_CRITERIA", payload:criteria});
        //var hasParcel = criteria=="DELIVERY";
        var data = StorageUtils.getLocalityMetadata();
        if(data==null) data={};
        data.criteria = criteria;
        StorageUtils.setLocalityMetadata(data);
        trackUtils.trackOrderModeChange(criteria);
        trackUtils.initWebEngageParams(criteria, null, null,null);
    }
}

export function toggleLocationWrapper(value) {
    return dispatch => {
        dispatch({type: "SET_LOCATION_WRAPPER", payload:value});
    }
}

export function setShowLocality(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_LOCALITY", payload:value});
    }
}

export function setShowOutlet(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_OUTLET", payload:value});
    }
}