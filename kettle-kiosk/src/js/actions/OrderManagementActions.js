import {browserHistory} from "react-router";
import axios from "axios";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import * as UtilityActions from "./UtilityActions";
import * as PaymentActions from "./PaymentActions";
import * as CartManagementActions from "./CartManagementActions";
import * as CampaignManagementActions from "./CampaignManagementActions";
import * as PromoActions from "./PromoActions";
import trackUtils from "../utils/TrackUtils";
import * as CustomerActions from "./CustomerActions";

export function getOrderDetail(orderDetail, currentOrderId, trackSuccess, timeoutsArray) {
    return dispatch => {
        if (currentOrderId != null) {
            if (!appUtil.checkEmpty(orderDetail) && ((currentOrderId.type == "GEN" && orderDetail.generateOrderId == currentOrderId.id) ||
                (currentOrderId.type == "WEB" && orderDetail.externalOrderId == currentOrderId.id))) {
                dispatch({type: "GET_ORDER_SUMMARY_FULFILLED", payload: {data: orderDetail}});
            } else {
                var url;
                if (currentOrderId.type == "WEB") {
                    url = apis.getUrls().order.searchByWebId;
                } else {
                    url = apis.getUrls().order.searchByOrderId;
                }
                dispatch({type: "GET_ORDER_SUMMARY_PENDING"});
                axios({
                    method: "POST",
                    url: url,
                    data: JSON.stringify(currentOrderId.id),
                    headers: {'Content-Type': 'application/json'}
                }).then((response) => {
                    dispatch({type: "GET_ORDER_SUMMARY_FULFILLED", payload: response});
                    if (!appUtil.isOrderCompleted(response.data.status, response.data.source)) {
                        const x = setTimeout(() => {
                            dispatch(getOrderStatus(currentOrderId.id, response.data.source, timeoutsArray));
                        }, 300000);
                        timeoutsArray.push(x);
                    }
                    if (trackSuccess == true) {
                        var d = StorageUtils.getLocalityMetadata();
                        try {
                            trackUtils.trackSuccessOrder({
                                criteria: d.criteria,
                                city: d.city,
                                locality: !appUtil.checkEmpty(d.locality) ? d.locality.label : "",
                                outlet: !appUtil.checkEmpty(d.outlet) ? d.outlet.label : "",
                                cart: response.data
                            });
                        } catch (e) {
                        }
                        browserHistory.push("/orderDetail");
                    }
                }).catch((error) => {
                    dispatch({type: "GET_ORDER_SUMMARY_REJECTED", payload: error});
                })
            }
        } else {
            browserHistory.push("/orders");
        }

    }
}

export function getOrderStatus(orderId, source, timeoutsArray) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().order.searchStatusByOrderId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {
            if (response.data) {
                dispatch({type: "ORDER_STATUS_UPDATE", payload: response.data});
                if (!appUtil.isOrderCompleted(response.data, source)) {
                    const x = setTimeout(() => {
                        dispatch(getOrderStatus(orderId, source, timeoutsArray));
                    }, 300000);
                    timeoutsArray.push(x);
                }
            } else {
                dispatch(UtilityActions.showPopup("Error in updating order status. Please reload the page."));
            }
        }).catch(e => {
            //dispatch(UtilityActions.showPopup("Error in getting order status."));
        })
    }
}

export function removeStatusTimeouts(timeoutsArray) {
    return dispatch => {
        timeoutsArray.map(timeout => {
            clearTimeout(timeout);
        });
        dispatch(setStatusTimeoutsArray([]));
    }
}

export function setStatusTimeoutsArray(data) {
    return dispatch => {
        dispatch({type: "SET_ORDER_STATUS_TIMEOUTS", payload: data});
    }
}

export function getCustomerOrders(page) {
    return dispatch => {
        var customerId = StorageUtils.getCustomerId();
        if (customerId != null) {
            dispatch({type: "GET_CUSTOMER_ORDERS_PENDING"});
            axios({
                method: "POST",
                url: apis.getUrls().order.searchCustomerOrders,
                data: JSON.stringify({customerId: StorageUtils.getCustomerId(), page: page, limit: 5}),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                dispatch({type: "GET_CUSTOMER_ORDERS_FULFILLED", payload: response});
            }).catch(function (error) {
                dispatch({type: "GET_CUSTOMER_ORDERS_REJECTED", payload: error});
            })
        } else {
            //send to login page and show message session expired
            browserHistory.push("/login");
            dispatch(UtilityActions.showPopup("Session expired. Please login again!", 'info'));
        }
    }
}

export function resetPage() {
    return dispatch => {
        dispatch({type: "RESET_PAGE"});
    }
}

export function orderCheckout(mode, timeoutsArray) {
    return dispatch => {
        dispatch({type: "SET_CASH_ORDER_LOADER", payload: true});
        dispatch({type: "SET_ORDER_FAILED", payload: false});
        var cart = StorageUtils.getCartDetail();
        cart.orderDetail.settlements = [];
        cart.orderDetail.settlements.push({
            mode: mode,
            amount: cart.orderDetail.transactionDetail.paidAmount
        });
        //console.log('external Order Id is :: '+cart.orderDetail.externalOrderId);
        if (cart.orderDetail.externalOrderId == null) {
            cart.orderDetail.externalOrderId = "KO433665635";
        }
        cart.orderType = "KIOSK_CASH";
        //console.log('After adding external Order Id is :: '+cart.orderDetail.externalOrderId);
        dispatch(PaymentActions.setPaymentData({
            paymentMessage: "Please wait while we place your order.",
            paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
        }));
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkoutCash,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data != null) {
                if (response.data.errorCode != null) {
                    dispatch(handleOrderCreationException(response.data));
                } else {
                    var currentOrderId = {id: response.data, type: "GEN"};
                    dispatch(setCurrentOrderId(currentOrderId));
                    dispatch(getOrderDetail(null, currentOrderId, true, timeoutsArray));
                    dispatch(PaymentActions.setPaymentInitiated(false));
                    dispatch(stampDevice(function (response) {
                        const cartDetail = response.data.cartDetail;
                        response.data.cartDetail = null;

                        StorageUtils.setAuthDetail(response.data);
                        StorageUtils.setDeviceKey(response.data.deviceKey);
                        dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));

                        dispatch(CartManagementActions.updateCart(cartDetail));
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
                        //dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                        dispatch(UtilityActions.hideFullPageLoader());
                        browserHistory.push("/orderSuccess");
                    }, function (error) {
                        //browserHistory.push("/orderDetail");
                        browserHistory.push("/orderSuccess");
                        dispatch(UtilityActions.hideFullPageLoader());
                        dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                    }));
                    StorageUtils.removeCampaignDetails();
                    dispatch(CampaignManagementActions.setCampaignDetail(null));
                    //dispatch(PromoActions.customerAccepted(false));
                }
                dispatch({type: "SET_CASH_ORDER_LOADER", payload: false});
            } else {
                dispatch({type: "SET_CASH_ORDER_LOADER", payload: false});
                slackCartId(cart.cartId);
                dispatch({type: "SET_ORDER_FAILED", payload: true});
                dispatch(PaymentActions.setPaymentData({
                    paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.",
                    paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
                }));
            }
        }).catch(function (error) {
            dispatch({type: "SET_CASH_ORDER_LOADER", payload: false});
            dispatch({type: "SET_ORDER_FAILED", payload: true});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(handleOrderCreationException(error.response.data, cart.cartId, null));
        });
    }
}

export function checkOutOrderByGiftCard() {
    return dispatch => {
        dispatch({type: "SET_ORDER_FAILED", payload: false});
        var cart = StorageUtils.getCartDetail();

        if (cart.orderDetail.externalOrderId == null) {
            cart.orderDetail.externalOrderId = "KOGC433665635";
        }
        cart.orderDetail.brandId = 1;
        cart.orderType = "KIOSK_GIFT_CARD";
        dispatch(PaymentActions.setPaymentData({
            paymentMessage: "Please wait while we place your order.",
            paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
        }));
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkoutCash,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log('checkOutOrderByGiftCard ', response);
            if (response.data != null) {
                if (response.data.errorCode != null) {
                    dispatch(handleOrderCreationException(response.data));
                } else {
                    var currentOrderId = {id: response.data, type: "GEN"};
                    dispatch(setCurrentOrderId(currentOrderId));
                    dispatch(getOrderDetail(null, currentOrderId, true));
                    dispatch(PaymentActions.setPaymentInitiated(false));
                    dispatch(stampDevice(function (response) {
                        if(!appUtil.checkEmpty(response)){
                            const cartDetail = response.data.cartDetail;
                            response.data.cartDetail = null;
                            dispatch({type: "UPDATE_CART", payload: cartDetail});
                            StorageUtils.setCartDetail(cartDetail);
                            StorageUtils.setAuthDetail(response.data);
                            StorageUtils.setDeviceKey(response.data.deviceKey);
                            dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));
                            dispatch(CartManagementActions.updateCart(cartDetail));
                        }
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
                        dispatch({type: "SET_OTP_MESSAGE", payload: null});
                        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
                        dispatch(UtilityActions.hideFullPageLoader());
                        browserHistory.push("/orderSuccess");
                    }, function (error) {
                        dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                    }));
                    StorageUtils.removeCampaignDetails();
                    dispatch(CampaignManagementActions.setCampaignDetail(null));

                }
                dispatch({type: "SET_CASH_ORDER_LOADER", payload: false});
            } else {
                dispatch({type: "SET_CASH_ORDER_LOADER", payload: false});
                dispatch({type: "SET_ORDER_FAILED", payload: true});
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(PaymentActions.setPaymentData({
                    paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.",
                    paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
                }));
            }
        }).catch(function (error) {
            dispatch({type: "SET_CASH_ORDER_LOADER", payload: false});
            dispatch({type: "SET_ORDER_FAILED", payload: true});
            dispatch(handleOrderCreationException(error.response.data, cart.cartId, null));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function orderCheckoutOfGiftCard(orderId) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkoutWOId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data != null) {
                console.log('Payment of gift card has been done.');
            }
        }).catch(function (error) {
            dispatch({type: "SET_ORDER_FAILED", payload: true});
            dispatch(handleOrderCreationException(error.response.data, null, orderId));
        });
    }
}

export function orderCheckoutByWOId(orderId, timeoutsArray) {
    return dispatch => {
        dispatch({type: "SET_ORDER_FAILED", payload: false});
        dispatch(PaymentActions.setPaymentData({
            paymentMessage: "Payment success. Please wait while we place your order.",
            paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
        }));
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkoutWOId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data != null) {
                var currentOrderId = {id: response.data, type: "GEN"};
                dispatch(setCurrentOrderId(currentOrderId));
                //dispatch(getOrderDetail(null, currentOrderId, true, timeoutsArray));
                dispatch(PaymentActions.setPaymentInitiated(false));
                dispatch(stampDevice(function (response) {
                    const cartDetail = response.data.cartDetail;
                    response.data.cartDetail = null;
                    StorageUtils.setAuthDetail(response.data);

                    StorageUtils.setAuthDetail(response.data);
                    StorageUtils.setDeviceKey(response.data.deviceKey);
                    dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));

                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
                    //dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                    browserHistory.push("/orderSuccess");
                }, function (error) {
                    console.log(error);
                    /*browserHistory.push("/orderDetail");
                    dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));*/
                }));
                StorageUtils.removeCampaignDetails();
                dispatch(CampaignManagementActions.setCampaignDetail(null));
                //dispatch(PromoActions.customerAccepted(false));
            } else {
                StorageUtils.removeCurrentOrderId();
                slackWOId(orderId);
                dispatch({type: "SET_ORDER_FAILED", payload: true});
                dispatch(PaymentActions.setPaymentData({
                    paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.",
                    paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
                }));
            }
        }).catch(function (error) {
            StorageUtils.removeCurrentOrderId();
            dispatch({type: "SET_ORDER_FAILED", payload: true});
            dispatch(handleOrderCreationException(error.response.data, null, orderId));
        });
    }
}

export function slackCartId(cartId) {
    axios({
        method: "POST",
        url: apis.getUrls().order.slackCartId,
        data: JSON.stringify(cartId),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {

    });
}

export function slackWOId(WOId) {
    axios({
        method: "POST",
        url: apis.getUrls().order.slackWOId,
        data: JSON.stringify(WOId),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {

    });
}

export function reOrder(screen, orderId, isInterState) {
    return dispatch => {
        trackUtils.trackReorderClicked(screen);
        //var order = StorageUtils.getCurrentOrderId();
        var unit = StorageUtils.getUnitDetails();
        axios({
            method: "POST",
            url: apis.getUrls().cart.createCartFromOrderId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                var cart = response.data;
                cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
                cart = appUtil.calculateTaxes(cart, isInterState);
                dispatch(CartManagementActions.updateCart(response.data));
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup("Your cart is ready." + (unit == null ? " Please select your location." : ""), "info", 3000));
                unit != null ? browserHistory.push("/cart") : browserHistory.push("/");
            } else {
                dispatch(UtilityActions.showPopup("Error in creating the order. Please try again!", "error"));
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch((error) => {
            dispatch(UtilityActions.showPopup("Error in creating the order. Please try again!", "error"));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function stampDevice(success, errorCall) {
    return dispatch => {
        var authDetail = StorageUtils.getAuthDetail();
        authDetail.cartDetail = null;
        if (!appUtil.checkEmpty(authDetail) && authDetail.deviceKey != null) {
            axios({
                method: "POST",
                url: apis.getUrls().stamping.stampDevice,
                data: JSON.stringify(authDetail),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (typeof success == 'function') {
                    success(response);
                } else {
                    window.location.reload();
                }
            }).catch(function (error) {
                if (typeof errorCall == 'function') {
                    errorCall(error);
                } else {
                    window.location.reload();
                }
            });
        } else {
            dispatch(UtilityActions.createDevice());
        }
    }
}

export function setCurrentOrderId(order) {
    return dispatch => {
        dispatch({type: "SET_CURRENT_ORDER_ID", payload: order});
        StorageUtils.setCurrentOrderId(order);
    }
}

export function handleOrderCreationException(response, cartId, woId) {
    return dispatch => {
        dispatch(UtilityActions.hideFullPageLoader());
        if (response.errorCode == 711) {
            stampDevice(function (response) {
                const cartDetail = response.data.cartDetail;
                response.data.cartDetail = null;

                StorageUtils.setAuthDetail(response.data);
                StorageUtils.setDeviceKey(response.data.deviceKey);
                dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));

                dispatch(CartManagementActions.updateCart(cartDetail));
                dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
            }, function (error) {
                //window.location.reload();
            });
            dispatch(UtilityActions.showPopup("Order already placed. Check order history!", 'info'));
            browserHistory.push("/orders");
        } else if ([701, 702, 711, 715, 708].indexOf(response.errorCode) >= 0) {
            dispatch(UtilityActions.showPrompt("Error Placing order. Please call at 1800-120-2424 for support.", function () {
                window.location.reload();
            }, function () {
                window.location.reload();
            }));
        } else {
            if (cartId != null) {
                slackCartId(cartId);
            }
            if (woId != null) {
                slackWOId(woId);
            }
            dispatch(PaymentActions.setPaymentData({
                paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.",
                paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
            }));
        }
    }
}

export function autoMoveStartNewOrder() {
    return dispatch => {
        StorageUtils.removeCustomerDetail();
        dispatch(CustomerActions.removeCustomerDetail());
        dispatch(CartManagementActions.removeCoupon(false));
        dispatch(CartManagementActions.clearCart());
        let timeout = setTimeout(function () {
            dispatch(startNewOrder());
        }, 10 * 1000)
        dispatch({type: "SET_START_NEW_ORDER_TIMEOUT", payload: timeout});
    }
}

export function startNewOrder(props) {
    return dispatch => {
        StorageUtils.removeCustomerDetail();
        dispatch(CustomerActions.removeCustomerDetail());
        dispatch(CartManagementActions.removeCoupon(false));
        dispatch(CartManagementActions.clearCart());
        dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
        dispatch({type: "SET_REDEMPTION_DONE", payload: false});
        dispatch({type: "BEFORE_REDEMPTION_CART", payload: null});
        dispatch({type: "SET_OTP_MESSAGE", payload: null});
        dispatch({type: "SET_COUPON", payload: null});
        if(props != undefined) {
            clearTimeout(props.setStartNewOrderTimeout);
        }
        browserHistory.push("/menu");
    }
}