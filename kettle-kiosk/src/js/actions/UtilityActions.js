import appUtil from "../AppUtil";
import apis from "../APIs";
import axios from "axios";
import trackUtils from "../utils/TrackUtils";
import StorageUtils from "../utils/StorageUtils";
import * as CartManagementActions from "./CartManagementActions";
import * as LocalityActions from "./LocalityActions";
import * as OrderManagementActions from "./OrderManagementActions";
import * as CampaignManagementActions from "./CampaignManagementActions";
import * as CustomerActions from "./CustomerActions";

export function createDevice() {
    return dispatch =>{
        axios({
            method: "POST",
            url: apis.getUrls().stamping.registerDevice,
            data: JSON.stringify({userAgent: navigator.userAgent, platform: navigator.platform}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            let cartDetail = response.data.cartDetail;
            response.data.cartDetail = null;
            StorageUtils.setAuthDetail(response.data);
            dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));
            cartDetail.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria || "TAKE_AWAY");
            //API will never return null cart from backend(vivek)
            dispatch(CartManagementActions.updateCart(cartDetail));
            const o = getDefParams();
            trackUtils.initWebEngageParams(o.orderMode, o.city, o.locality, o.outlet);
            /*if (!appUtil.checkEmpty(cartDetail)) {
                storageUtils.setCartDetail(cartDetail);
            }else{
                storageUtils.removeCartDetail();
            }*/
        }).catch(function (error) {
            dispatch({type:"SET_INTERNET_ERROR", payload:true});
        });
    }
}

export function stampDevice(authDetail) {
    return dispatch =>{
        axios({
            method: "POST",
            url: apis.getUrls().stamping.stampDevice,
            data: JSON.stringify(authDetail),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (appUtil.checkEmpty(response.data)) {
                dispatch(createDevice());
            } else {
                let cartDetail = response.data.cartDetail;
                response.data.cartDetail = null;
                StorageUtils.setAuthDetail(response.data);
                dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));
                dispatch(CustomerActions.setSessionKey(response.data.sessionKey));
                cartDetail.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria || "TAKE_AWAY");
                //API will never return null cart from backend(vivek)
                dispatch(CartManagementActions.updateCart(cartDetail));
                var contact = StorageUtils.getCustomerContact();
                contact!=null?trackUtils.trackUser(contact):null;
                const o = getDefParams();
                trackUtils.initWebEngageParams(o.orderMode, o.city, o.locality, o.outlet);
                /*if (!appUtil.checkEmpty(cartDetail)) {
                    appUtil.setCartDetail(cartDetail);
                }else{
                    appUtil.removeCartDetail();
                }*/
            }
        }).catch(function (error) {
            dispatch({type:"SET_INTERNET_ERROR", payload:true});
        });
    }
}

const getDefParams = function (){
    var orderMode = null, city=null, locality=null, outlet=null;
    var data = StorageUtils.getLocalityMetadata();
    !appUtil.checkEmpty(data)?(
        orderMode=data.criteria,
        city=data.city,
        locality=(data.locality!=null?data.locality.label:""),
        outlet=(data.outlet!=null?data.outlet.label:"")/*,
        device_id=(StorageUtils.getAuthDetail()!=null?StorageUtils.getDeviceId():"")*/
    ):null;
    return {orderMode:orderMode, city:city, locality:locality, outlet:outlet};
};

export function setDefaultData() {
    return dispatch => {
        //setting locality data from cookie if available
        var authDetail = StorageUtils.getAuthDetail();
        if(!appUtil.checkEmpty(authDetail)){
            dispatch(CustomerActions.setDeviceKey(authDetail.deviceKey));
            dispatch(CustomerActions.setSessionKey(authDetail.sessionKey));
        }
        //dispatch(CampaignManagementActions.setCampaignCookie(StorageUtils.getCampaignDetails()));
    }
};

export function showPopup(message, type, timeout) {
    return dispatch => {
        if (type != null) {
            dispatch({type: "SHOW_POPUP", payload: {message: message, type: type}});
        } else {
            dispatch({type: "SHOW_POPUP", payload: {message: message, type: "info"}});
        }
        var milliSec = 2000;
        if(timeout!=null){
            milliSec = timeout;
        }
        setTimeout(function () {
            dispatch({type: "HIDE_POPUP"});
        }, milliSec);
    }
}

export function showFullPageLoader(message) {
    return dispatch => {
        dispatch({type: "SHOW_FULLPAGE_LOADER", payload: message});
    }
}

export function hideFullPageLoader() {
    return dispatch => {
        dispatch({type: "HIDE_FULLPAGE_LOADER"});
    }
}

export function handlePrompt(){
    return dispatch => {
        dispatch({type: "HANDLE_PROMPT"});
    }
}

export function showPrompt(message, success, dismiss){
    return dispatch => {
        dispatch({type: "SHOW_PROMPT", payload:{message:message,success:success,dismiss:dismiss}});
    }
}

export function handleRotate(isLandscape){
    return dispatch => {
        dispatch({type: "SET_ORIENTATION", payload:isLandscape});
    }
}

export function setPaytmGIFLoader(status) {
    return dispatch => {
        dispatch({type: "SET_PAYTM_GIF_LOADER", payload: status});
    }
}