/**
 * Created by Chaayos on 01-12-2016.
 */
import PubNubUtils from "./../utils/PubNubUtils";
import * as CustomerActions from "./CustomerActions";

export function setupPubNub() {
    return dispatch => {
        PubNubUtils.initPubNub();
        PubNubUtils.receiveMessage(function (message) {
            //console.log("Received message :::: ", message);
            dispatch(processSocketMessage(message));
        });
    }
}

export function processSocketMessage(message) {
    return dispatch => {
        console.log("Socket message received:::::::::::::::::::::::::::::::", message);
        var data = message;
        // var key = Object.keys(data)[0];
        var key = Object.keys(message)[0];

        switch (key) {
            case "TRUE_CALLER_PROFILE":
                var contact = data[key].contact.substring(2); //removing first 2 digits as it is country code
                data[key].contact = contact;
                dispatch(CustomerActions.lookupCustomer(contact, false, null,  data[key]));
                break;
        }

    }
}