/**
 * Created by Chaayos on 01-12-2016.
 */
import axios from "axios";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import {browserHistory} from "react-router";
import * as UtilityActions from "./UtilityActions";
import * as CartManagementActions from "./CartManagementActions";
import * as OutletMenuActions from "./OutletMenuActions";
import trackUtils from "../utils/TrackUtils";
import {updateCart} from "./CartManagementActions";
import * as LocalityActions from "./LocalityActions";

export function lookupCustomer(contact, couponLogin, couponCode, truecallerProfile) {
    return dispatch => {
        if (!couponLogin) {
            dispatch(UtilityActions.showFullPageLoader("Loading..."));
        }
        dispatch({type: "LOOKUP_CUSTOMER_PENDING", payload: contact});
        dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: true});
        var customer = StorageUtils.getCustomerDetail();
        customer = customer == null ? {} : customer;
        StorageUtils.setCustomerDetail({...customer, contact: contact});
        axios({
            method: "POST",
            url: apis.getUrls().customer.lookupOnly,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data == -2) {
                dispatch(UtilityActions.showPopup("You are an internal customer and hence not authorised to place order here."));
                dispatch({type: "LOOKUP_CUSTOMER_FULFILLED", payload: response.data});
                dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
            }
            if (response.data == 1 || response.data == 3) {
                dispatch(getCustomerDetailsAndGoForPayment(contact, couponLogin, couponCode));
            } else if (truecallerProfile != null) {
                dispatch(saveVerifiedCustomer(truecallerProfile, couponLogin, couponCode));
            } else {
                dispatch({type: "LOOKUP_CUSTOMER_FULFILLED", payload: response.data});
                dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
                dispatch(UtilityActions.hideFullPageLoader());
            }
            trackUtils.trackCustomerLookup({contact: contact, status: "SUCCESS", isRegistered: response.data != -1});
        }).catch(function (error) {
            dispatch({type: "LOOKUP_CUSTOMER_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
            trackUtils.trackCustomerLookup({contact: contact, status: "FAILED", isRegistered: false});
        });
    }
}

export function getCustomerDetailsAndGoForPayment(contact, couponLogin, couponCode) {
    return dispatch => {
        if (!couponLogin) {
            dispatch(UtilityActions.showFullPageLoader("Verifying contact number. Please wait."));
        }
        var reqObj = {contact: contact, deviceKey: StorageUtils.getAuthDetail().deviceKey, update: false};
        axios({
            method: "POST",
            url: apis.getUrls().customer.getCustomerInfo,
            data: JSON.stringify(reqObj),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data != null && response.data.contact == contact) {
                var cart = response.data.device.cartDetail;
                var cartDetail = StorageUtils.getCartDetail();
                //cartDetail.orderDetail.orders = cart.orderDetail.orders;
                cartDetail.orderDetail.webCustomerId = cart.customerId;
                //cartDetail.cartId = cart.cartId;
                cartDetail.deviceId = cart.deviceId;
                cartDetail.customerId = cart.customerId;
                cartDetail.orderDetail.customerName = response.data.name;
                cartDetail.sessionId = cart.sessionId;
                var customer = StorageUtils.getCustomerDetail();
                if (customer == null) {
                    customer = {}
                }

                axios({
                    method: "POST",
                    url: apis.getUrls().customer.getLoyalty,
                    data: JSON.stringify({contact: contact}),
                    headers: {'Content-Type': 'application/json'}
                }).then(function (response1) {
                    customer = {
                        ...customer,
                        contact: response.data.contact,
                        name: response.data.name,
                        email: response.data.email,
                        loyalty: response1.data.loyalityPoints,
                        chaayosCash: response1.data.chaayosCash,
                        customerId: response.data.customerId,
                        availChaayosCash: false,
                        chaiRedeemed: 0,
                        redemptionDone: false,
                        eligibleForSignupOffer: response1.data.eligibleForSignupOffer,
                        otpVerified: false
                    };

                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
                    StorageUtils.setCustomerDetail(customer);
                    var criteria = "TAKE_AWAY";
                    dispatch(LocalityActions.setCriteria(criteria));
                    cartDetail.orderDetail.source = criteria;
                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch({type: "LOGIN_CUSTOMER_FULFILLED", payload: response.data.device.sessionKey});
                    if (couponLogin == true) {
                        dispatch(CartManagementActions.applyCoupon(couponCode, false, true));
                        dispatch(getGiftCardsForCustomer(customer));
                    } else {
                        dispatch(createAdhocCart(cartDetail, customer, couponLogin));
                        dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
                        dispatch({type: "SET_LOGIN_REJECTED"});
                        dispatch({type: "SET_SIGNUP_REJECTED"});
                    }
                }).catch(function (error) {
                    dispatch({type: "LOOKUP_CUSTOMER_REJECTED", payload: error});
                    dispatch(UtilityActions.hideFullPageLoader());
                    dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
                    dispatch({type: "SET_LOGIN_REJECTED"});
                    dispatch({type: "SET_SIGNUP_REJECTED"});
                });
            }
            else {
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
                dispatch({type: "SET_LOGIN_REJECTED"});
                dispatch({type: "SET_SIGNUP_REJECTED"});
                dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
            }
        }).catch(function (error) {
            dispatch({type: "LOOKUP_CUSTOMER_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
            dispatch({type: "SET_LOGIN_REJECTED"});
            dispatch({type: "SET_SIGNUP_REJECTED"});
        });
    }
}

// check for giftCard
export function getGiftCardsForCustomer(customer) {

    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().customer.getGiftCard,
            data: customer.customerId,
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log('get gift card ', StorageUtils.getCustomerDetail());
            let customer = StorageUtils.getCustomerDetail();
            console.log('customer get gc ', customer);
           
            customer.selfGiftCards = response.data.self;
            customer.otherGiftCards = response.data.gift;
            customer.giftCardBalance = calculateGiftCardBalance(customer.selfGiftCards);
            dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
            StorageUtils.setCustomerDetail(customer);
            // browserHistory.push("/orderSource");
        }).catch(function (error) {
            UtilityActions.showPopup("Error in getting gift card of customer");
            // browserHistory.push("/orderSource");
        });
    }
}

export function calculateGiftCardBalance(cards) {
    let balance = 0;
    cards.forEach((card) => {
        balance = balance + card.cashPendingAmount;
    });
    return balance;
}

export function saveVerifiedCustomer(truecallerProfile, couponLogin, couponCode) {
    return dispatch => {
        if (!couponLogin) {
            dispatch(UtilityActions.showFullPageLoader("Verifying contact number. Please wait."));
        }
        //var reqObj = {contact: contact, deviceKey: StorageUtils.getAuthDetail().deviceKey, update: false};
        dispatch(signUpCustomer(truecallerProfile.name, truecallerProfile.email, null, truecallerProfile.contact, "/orderSource", false, false, null, -1));
    }
}

export function createAdhocCart(cart, customer) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().cart.createCart,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            var cart = StorageUtils.getCartDetail();
            cart.cartId = response.data.cartId;
            dispatch(updateCart(cart));
            browserHistory.push("/orderSource");
            dispatch(getGiftCardsForCustomer(customer));
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            UtilityActions.showPopup("Error in verifying contact number")
        });
    }
}

export function loginCustomer(name, email, otp, getName, getEmail, contact, url, couponLogin, couponCode) {
    return dispatch => {
        var data = StorageUtils.getAuthDetail();
        if (appUtil.checkEmpty(data)) {
            browserHistory.push("/");
            dispatch(OutletMenuActions.setShowCouponModal(false));
            dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
        } else {
            if (!couponLogin) {
                dispatch(UtilityActions.showFullPageLoader(""));
            }
            dispatch({type: "LOGIN_CUSTOMER_PENDING", payload: true});
            url = (url != null) ? url : "/menu";
            var reqObj = {otp: otp, contact: contact, deviceKey: data.deviceKey, update: false};
            if (getName) {
                reqObj.name = name;
                reqObj.update = true;
            }
            if (getEmail && email != null) {
                reqObj.email = email;
                reqObj.update = true;
            }
            axios({
                method: "POST",
                url: apis.getUrls().customer.login,
                data: JSON.stringify(reqObj),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response.data != null) {
                    var cart = response.data.device.cartDetail;
                    response.data.device.cartDetail = null;
                    StorageUtils.setAuthDetail(response.data.device);
                    StorageUtils.getCustomerContact() != null ? trackUtils.trackUser(StorageUtils.getCustomerContact()) : null;
                    var cartDetail = StorageUtils.getCartDetail();
                    if (appUtil.checkEmpty(cartDetail)) {
                        cartDetail = cart;
                    } else {
                        cartDetail.orderDetail.orders = cart.orderDetail.orders;
                        cartDetail.orderDetail.webCustomerId = cart.customerId;
                        cartDetail.cartId = cart.cartId;
                        cartDetail.deviceId = cart.deviceId;
                        cartDetail.customerId = cart.customerId;
                        cartDetail.orderDetail.customerName = response.data.name;
                        cartDetail.sessionId = cart.sessionId;
                    }
                    //if(!appUtil.checkEmpty(cart))
                    //cartDetail = appUtil.calculateTaxes(cartDetail);
                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch({type: "SET_DEVICE_KEY", payload: response.data.device.deviceKey});
                    dispatch({type: "LOGIN_CUSTOMER_FULFILLED", payload: response.data.device.sessionKey});
                    dispatch(UtilityActions.hideFullPageLoader());
                    var customer = StorageUtils.getCustomerDetail();
                    if (customer == null) {
                        customer = {}
                    }
                    customer = {
                        ...customer,
                        customerId: response.data.customerId,
                        contact: response.data.contact,
                        name: response.data.name,
                        email: response.data.email,
                        loyalty: response.data.loyalty
                    };
                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
                    StorageUtils.setCustomerDetail(customer);
                    StorageUtils.getCustomerDetail() != null ? trackUtils.trackUserData(StorageUtils.getCustomerDetail()) : null;
                    trackUtils.trackLogin({
                        contact: contact,
                        name: response.data.name,
                        email: response.data.email,
                        otp: otp,
                        targetUrl: url,
                        status: "SUCCESS"
                    });
                    if (couponLogin == true) {
                        dispatch(CartManagementActions.applyCoupon(couponCode, false));
                    } else {
                        browserHistory.push(url);
                    }
                } else {
                    //dispatch(UtilityActions.showPopup("Error verifying OTP. Please enter correct OTP", "error", 2000));
                    dispatch(setErrorSignUp("Error verifying OTP. Please enter correct OTP"));
                    trackUtils.trackLogin({
                        contact: contact,
                        name: name,
                        email: email,
                        otp: otp,
                        targetUrl: url,
                        status: "FAILED"
                    });
                }
            }).catch(function (error) {
                dispatch({type: "LOGIN_CUSTOMER_REJECTED", payload: error});
                dispatch(UtilityActions.hideFullPageLoader());
                //dispatch(UtilityActions.showPopup("Error in OTP verification. Please try again!", "error", 2000));
                dispatch(setErrorSignUp("Error verifying OTP. Please enter correct OTP"));
                trackUtils.trackLogin({
                    contact: contact,
                    name: name,
                    email: email,
                    otp: otp,
                    targetUrl: url,
                    status: "FAILED"
                });
            });
        }
    }
}

export function signUpCustomer(name, email, otp, contact, url, isInterState, couponLogin, couponCode, tcId) {
    return dispatch => {
        var data = StorageUtils.getAuthDetail();
        if (appUtil.checkEmpty(data)) {
            browserHistory.push("/");
            dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
        } else {
            if (!couponLogin) {
                dispatch(UtilityActions.showFullPageLoader(""));
            }
            var apiUrl = apis.getUrls().customer.signUp;
            if (tcId == -1) {
                apiUrl = apis.getUrls().customer.signUpVerified;
            }
            dispatch({type: "SIGNUP_CUSTOMER_PENDING", payload: true});
            axios({
                method: "POST",
                url: apiUrl,
                data: JSON.stringify({
                    name: name,
                    contact: contact,
                    otp: otp,
                    email: email,
                    deviceKey: data.deviceKey,
                    tcId: tcId,
                    registrationUnitId: StorageUtils.getKettleAuthDetails().unitDetail.id,
                    acquisitionSource: "CAFE",
                    acquisitionBrandId: 1,
                    acquisitionToken: StorageUtils.getKettleAuthDetails().unitDetail.id

                }),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response.data != null) {
                    var cart = response.data.device.cartDetail;
                    response.data.device.cartDetail = null;
                    StorageUtils.setAuthDetail(response.data.device);
                    StorageUtils.getCustomerContact() != null ? trackUtils.trackUser(StorageUtils.getCustomerContact()) : null;
                    var cartDetail = StorageUtils.getCartDetail();
                    if (appUtil.checkEmpty(cartDetail)) {
                        cartDetail = cart;
                    } else {
                        cartDetail.orderDetail.orders = cart.orderDetail.orders;
                        cartDetail.orderDetail.webCustomerId = cart.customerId;
                        cartDetail.cartId = cart.cartId;
                        cartDetail.deviceId = cart.deviceId;
                        cartDetail.customerId = cart.customerId;
                        cartDetail.orderDetail.customerName = response.data.name;
                        cartDetail.sessionId = cart.sessionId;
                    }
                    cartDetail.orderDetail.transactionDetail == null ? cartDetail.orderDetail.transactionDetail = appUtil.getNewTransactionObject() : null;
                    cartDetail = appUtil.calculateTaxes(cartDetail, isInterState);
                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch({type: "SIGNUP_CUSTOMER_FULFILLED", payload: response.data.device.sessionKey});
                    dispatch(UtilityActions.hideFullPageLoader());
                    var customer = StorageUtils.getCustomerDetail();
                    if (customer == null) {
                        customer = {}
                    }
                    customer = {
                        ...customer,
                        customerId: response.data.customerId,
                        contact: response.data.contact,
                        name: response.data.name,
                        email: response.data.email,
                        loyalty: response.data.loyalityPoints,
                        selfGiftCards: [],
                        otherGiftCards: [],
                        giftCardBalance: 0,
                        otpVerified: true
                    };
                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
                    StorageUtils.setCustomerDetail(customer);
                    StorageUtils.getCustomerDetail() != null ? trackUtils.trackUserData(StorageUtils.getCustomerDetail()) : null;
                    trackUtils.trackSignup({
                        contact: contact,
                        name: name,
                        email: email,
                        otp: otp,
                        targetUrl: url,
                        status: "SUCCESS"
                    });
                    url = (url != null) ? url : "/menu";
                    if (couponLogin) {
                        dispatch(CartManagementActions.applyCoupon(couponCode, false));
                    } else {
                        browserHistory.push(url);
                    }
                } else {
                    //dispatch(UtilityActions.showPopup("Error verifying OTP. Please enter correct OTP", "error", 2000));
                    dispatch(setErrorSignUp("Error verifying OTP. Please enter correct OTP"));
                    let customer = StorageUtils.getCustomerDetail();
                    customer.otpVerified = false;
                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
                    StorageUtils.setCustomerDetail(customer);

                    trackUtils.trackSignup({
                        contact: contact,
                        name: name,
                        email: email,
                        otp: otp,
                        targetUrl: url,
                        status: "FAILED"
                    });
                }
            }).catch(function (error) {
                dispatch({type: "SIGNUP_CUSTOMER_REJECTED", payload: error});
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(setErrorSignUp("Error verifying OTP. Please enter correct OTP"));
                /*dispatch(UtilityActions.showPopup("Error in OTP verification. Please try again!", "error", 2000));*/
                trackUtils.trackSignup({
                    contact: contact,
                    name: name,
                    email: email,
                    otp: otp,
                    targetUrl: url,
                    status: "FAILED"
                });
            });
        }
    }
}

export function getCustomerAddresses(customerId) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        dispatch({type: "CUSTOMER_ADDRESSES_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.addresses,
            data: JSON.stringify(customerId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "CUSTOMER_ADDRESSES_FULFILLED", payload: response.data});
            trackUtils.trackAddressCount(response.data.length);
            if (appUtil.checkEmpty(response.data)) {
                browserHistory.push("/newAddress");
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch({type: "CUSTOMER_ADDRESSES_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function selectAddress(id) {
    return dispatch => {
        dispatch({type: "SELECT_ADDRESS", payload: id});
        trackUtils.trackAddressSelected("EXISTING");
    }
}

export function removeSelectedAddress() {
    return dispatch => {
        dispatch({type: "REMOVE_SELECTED_ADDRESS"});
    }
}

export function addAddress(landmark, line1, locality, city, type, redirectTo) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().customer.addAddress,
            data: JSON.stringify({
                id: StorageUtils.getCustomerId(),
                address: {
                    line1: line1,
                    locality: locality,
                    city: city.city,
                    landmark: landmark,
                    state: "Haryana",
                    country: "India",
                    addressType: type
                },
                cartId: StorageUtils.getCartDetail() != null ? StorageUtils.getCartDetail().cartId : null
            }),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "ADD_ADDRESS", payload: response.data});
            var cart = StorageUtils.getCartDetail();
            cart.orderDetail.deliveryAddress = response.data.id;
            dispatch(CartManagementActions.updateCart(cart));
            trackUtils.trackAddressAdd({status: "SUCCESS"});
            if (redirectTo != null) {
                trackUtils.trackAddressSelected("NEW");
                browserHistory.push("/orderSource");
            } else {
                browserHistory.push("/addresses");
            }
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Address added successfully!", "info"));
        }).catch(function (error) {
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error adding address. Try again!", "info"));
            trackUtils.trackAddressAdd({status: "FAILED"});
        })
    }
}

export function resetLogin() {
    return dispatch => {
        dispatch({type: "RESET_LOGIN"});
    }
}

export function resendVerification(contact) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().customer.resendVerification,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data) {
                if (response.data.success == true) {
                    dispatch(UtilityActions.showPopup("OTP resent successfully. Please check your phone.", "info"));
                    document.getElementById("otpInput").focus();
                    trackUtils.trackOTPResent({contact: contact, status: "SUCCESS"});
                } else {
                    dispatch(UtilityActions.showPopup("You have tried too many OTP attempts. Please try again in " + response.data.sec + "seconds.", "info"));
                    dispatch(initOTPResendClock(response.data.sec));
                }
            } else {
                dispatch(UtilityActions.showPopup("Error sending OTP. Please try later.", "info"));
                //trackUtils.trackOTPResent({contact:contact,status:"FAILED"});
            }
        }).catch(function (error) {
            dispatch(UtilityActions.showPopup("Error sending OTP. Please try later.", "info"));
            trackUtils.trackOTPResent({contact: contact, status: "FAILED"});
        })
    }
}

export function setAddressViewType(type) {
    return dispatch => {
        dispatch({type: "SET_ADDRESS_VIEW_TYPE", payload: type});
    }
}

export function getLoyalteaScore() {
    return dispatch => {
        dispatch({type: "GET_LOYALTEA_SCORE_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.loyaltea,
            data: JSON.stringify(StorageUtils.getCustomerId()),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                var customer = {
                    ...StorageUtils.getCustomerDetail(),
                    name: response.data.name,
                    email: response.data.email
                };
                StorageUtils.setCustomerDetail(customer);
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
            }
            dispatch({type: "GET_LOYALTEA_SCORE_FULFILLED", payload: response.data.loyalty});
        }).catch(function (error) {
            dispatch({type: "GET_LOYALTEA_SCORE_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error getting Loyaltea score!", 'error'));
        })
    }
}

export function setCustomerDetail() {
    return dispatch => {
        dispatch({type: "SET_CUSTOMER_DETAIL", payload: StorageUtils.getCustomerDetail()});
    }
}

export function removeCustomerDetail() {
    return dispatch => {
        dispatch({type: "SET_CUSTOMER_DETAIL", payload: null});
    }
}

export function setDeviceKey(key) {
    return dispatch => {
        dispatch({type: "SET_DEVICE_KEY", payload: key});
    }
}

export function setSessionKey(key) {
    return dispatch => {
        dispatch({type: "SET_SESSION_KEY", payload: key});
    }
}

export function initOTPResendClock(seconds) {
    return dispatch => {
        var sec = seconds;
        var clock = setInterval(() => {
            if (sec == 0) {
                clearInterval(clock);
            }
            dispatch(setOTPResendSeconds(sec--));
        }, 1000)
    }
}

export function setOTPResendSeconds(sec) {
    return dispatch => {
        dispatch({type: "SET_OTP_RESEND_SECONDS", payload: sec});
    }
}

export function setErrorSignUp(errorMessage) {
    return dispatch => {
        dispatch({type: "SET_ERROR_SIGN_UP_MESSAGE", payload: errorMessage});
    }
}

export function loadGiftCards(props) {
    return dispatch => {
        let allCards = [];
        if (props.unit != null) {
            props.unit.products.forEach((u) => {
                if (u.taxCode == "GIFT_CARD") {
                    var gco = props.giftCardOffer.filter(function (off) {
                        return off.denomination == u.prices[0].price;
                    });
                    let card = {
                        id: u.id,
                        name: u.name,
                        amount: u.prices[0].price,
                        quantity: 0,
                        recipeId: u.prices[0].recipeId,
                        productType: 9,
                        offer: 0
                    };

                    if (gco.length > 0) {
                        card.offer = gco[0].denomination * (gco[0].offer / 100);
                    }
                    allCards.push(card);
                }
            });
            dispatch({type: "LOAD_GIFT_CARDS", payload: allCards});
        }
    }
}

/*
export function generateTrucallerQRRequest() {
    return dispatch => {
        var requestId = encodeURI(StorageUtils.getKettleAuthDetails().unitDetail.id+"#"+StorageUtils.getKettleAuthDetails().terminal+"*"+new Date().getTime());
        var trueCallerQRString = StorageUtils.getTrueCallerAppId()+"|"+requestId+"|appName:"+StorageUtils.getTrueCallerAppName();
        dispatch({type: "SET_TRUE_CALLER_QR_STRING", payload: trueCallerQRString});
    }
}*/
