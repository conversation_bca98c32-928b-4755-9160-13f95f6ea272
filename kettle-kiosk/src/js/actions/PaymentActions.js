import axios from "axios";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import {browserHistory} from "react-router";
import * as OrderManagementActions from "./OrderManagementActions";
import * as UtilityActions from "./UtilityActions";
import * as CustomerActions from "./CustomerActions";
import trackUtils from "../utils/TrackUtils";
import qrcodel from "qr-code-with-logo";
import {initOTPResendClock} from "./CustomerActions";
import * as AGSUtility from "./AGSUtilityActions";
import _ from "lodash";
import { w3cwebsocket as W3CWebSocket } from "websocket";
import {handleOrderCreationException} from "./OrderManagementActions";


export function payByRazorPay(cart, paymentMethod, timeoutsArray) {
    return dispatch => {
        if (StorageUtils.getCustomerDetail() != null) {
            trackUtils.trackPaymentModeSelected({
                paymentPartner: "RAZORPAY",
                mode: paymentMethod,
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: cart.orderDetail.transactionDetail.paidAmount
            });
            axios({
                method: "GET",
                url: apis.getUrls().payment.paymentKey,
                data: {},
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                if (response.data) {
                    var pk = response.data;
                    axios({
                        method: "POST",
                        url: apis.getUrls().payment.createRazorPay,
                        data: JSON.stringify({
                            cartId: StorageUtils.getCartDetail().cartId,
                            paymentPartner: "RAZOR_PAY"
                        }),
                        headers: {'Content-Type': 'application/json'}
                    }).then(function (response) {
                        if (!appUtil.checkEmpty(response.data) && response.data.receipt != null) {
                            var paymentRequest = response.data;
                            // doing payment by Razorpay
                            var options = {
                                key: pk,
                                amount: paymentRequest.amount,
                                name: "Chaayos",
                                description: "Payment For Bill " + paymentRequest.receipt,
                                image: "/img/favicon-96x96.png",
                                prefill: {
                                    name: StorageUtils.getCustomerDetail().name,
                                    email: StorageUtils.getCustomerDetail().email,
                                    contact: StorageUtils.getCustomerDetail().contact,
                                    method: paymentMethod
                                },
                                handler: function (response) {
                                    dispatch(setPaymentData({
                                        paymentMessage: "Processing payment. Please wait and do not refresh this page.",
                                        paymentStatus: "SUCCESS", failureMessage: null, showLoader: true
                                    }));
                                    response.externalOrderId = paymentRequest.receipt;
                                    dispatch(validateRzp(response));
                                    trackUtils.trackPaymentSuccess({
                                        paymentPartner: "RAZORPAY",
                                        mode: paymentMethod,
                                        orderMode: StorageUtils.getLocalityMetadata().criteria,
                                        amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                                        externalOrderId: paymentRequest.receipt
                                    });
                                    dispatch(OrderManagementActions.orderCheckoutByWOId(paymentRequest.receipt, timeoutsArray));
                                },
                                modal: {
                                    ondismiss: function () {
                                        dispatch(cancelPayment(paymentRequest.receipt, "CANCELLED", "CUSTOMER", "Cancelled after opening.", ""));
                                        trackUtils.trackPaymentFailed({
                                            paymentPartner: "RAZORPAY",
                                            mode: paymentMethod,
                                            orderMode: StorageUtils.getLocalityMetadata().criteria,
                                            amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                                            externalOrderId: paymentRequest.receipt,
                                            reason: "Cancelled after opening."
                                        });
                                        dispatch(setPaymentData({
                                            paymentMessage: null, paymentStatus: "FAILED",
                                            failureMessage: "Payment failed. Please try again.",
                                            showLoader: false
                                        }));
                                    }
                                },
                                theme: {
                                    "color": "#5e7e47"
                                },
                                order_id: paymentRequest.id,
                                callback_url: apis.getUrls().paymentExternal.razorPayCheckoutAPI
                            };
                            var rzp1 = new Razorpay(options);
                            rzp1.open();
                        } else {
                            OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                            trackUtils.trackPaymentFailed({
                                paymentPartner: "RAZORPAY",
                                mode: paymentMethod,
                                orderMode: StorageUtils.getLocalityMetadata().criteria,
                                amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                                externalOrderId: paymentRequest.receipt,
                                reason: "Payment creation request returned wrong data"
                            });
                            dispatch(setPaymentData({
                                paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                                failureMessage: "Error in initiating payment request. Please try again."
                            }));
                        }
                    }).catch(function (error) {
                        OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                        trackUtils.trackPaymentFailed({
                            paymentPartner: "RAZORPAY",
                            mode: paymentMethod,
                            orderMode: StorageUtils.getLocalityMetadata().criteria,
                            amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                            externalOrderId: paymentRequest.receipt,
                            reason: "Payment creation request failed"
                        });
                        dispatch(setPaymentData({
                            paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                            failureMessage: "Error in initiating payment request. Please try again."
                        }));
                    })
                } else {
                    OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                    trackUtils.trackPaymentFailed({
                        paymentPartner: "RAZORPAY",
                        mode: paymentMethod,
                        orderMode: StorageUtils.getLocalityMetadata().criteria,
                        amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                        externalOrderId: "NA",
                        reason: "No payment key returned"
                    });
                    dispatch(setPaymentData({
                        paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                        failureMessage: "Something went wrong. Please try again."
                    }));
                }
            }).catch((error) => {
                console.log(error);
                OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                trackUtils.trackPaymentFailed({
                    paymentPartner: "RAZORPAY",
                    mode: paymentMethod,
                    orderMode: StorageUtils.getLocalityMetadata().criteria,
                    amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                    externalOrderId: "NA",
                    reason: "Payment key request failed"
                });
                dispatch(setPaymentData({
                    paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                    failureMessage: "Something went wrong. Please try again."
                }));
            });
        } else {
            dispatch(UtilityActions.showPopup("Please login to continue."));
            browserHistory.push("/login");
        }
    }
}

export function payByPaytm(cart) {
    return dispatch => {
        if (cart != null) {
            trackUtils.trackPaymentModeSelected({
                paymentPartner: "PAYTM",
                mode: "wallet",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: cart.orderDetail.transactionDetail.paidAmount
            });
            axios({
                method: "POST",
                url: apis.getUrls().payment.createPaytm,
                data: JSON.stringify({cartId: cart.cartId, paymentPartner: "PAYTM"}),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                var paymentRequest = response.data;
                // doing payment by paytm
                if (!appUtil.checkEmpty(paymentRequest) && paymentRequest != null) {
                    Object.keys(paymentRequest).forEach(function (k) {
                        if (paymentRequest[k] == null || paymentRequest[k].toString().trim() == "") {
                            delete paymentRequest[k];
                        }
                        if (typeof paymentRequest[k] == "number") {
                            paymentRequest[k] = paymentRequest[k].toFixed(2).toString();
                        }
                    });
                    if (paymentRequest != undefined && paymentRequest != null) {
                        var host = location.host.toLowerCase();
                        var paymentHost = (host == "chaayos.com" || host == "m.chaayos.com" || host == "www.chaayos.com") ? "secure.paytm.in" : "pguat.paytm.com";
                        var form = '<form id="payForm" action="https://' + paymentHost + '/oltp-web/processTransaction" method="post">';
                        for (var key in paymentRequest) {
                            form += '<input type="hidden" name="' + key + '" value="' + paymentRequest[key] + '"/>';
                        }
                        form += '</form>';
                        var div = document.createElement('div');
                        div.setAttribute("id", "payFormWrapper");
                        div.innerHTML = form;
                        document.getElementsByTagName("body")[0].appendChild(div);
                        document.getElementById("payForm").submit();
                    }
                } else {
                    OrderManagementActions.slackCartId(cart.cartId);
                    dispatch(setPaymentData({
                        paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                        failureMessage: "Error in initiating payment request. Please try again"
                    }));
                }
            }).catch(function (error) {
                console.log(error);
                if (StorageUtils.getCartDetail() != null) {
                    OrderManagementActions.slackCartId(cart.cartId);
                }
                dispatch(setPaymentData({
                    paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                    failureMessage: "Error in initiating payment request. Please try again"
                }));
            })
        } else {
            dispatch(UtilityActions.showPopup("Please login to continue."));
            browserHistory.push("/login");
        }
    }
}

export function payByPaytmQR() {
    return dispatch => {
        /*dispatch(UtilityActions.showFullPageLoader("Fetching Paytm QR Code... "));*/
        dispatch(UtilityActions.setPaytmGIFLoader(true));
        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 1});
        dispatch(showPaytmQRModal(true, null));
        var cart = StorageUtils.getCartDetail();
        if (cart != null) {
            let posId = cart.orderDetail.unitId + '#' + cart.orderDetail.terminalId;
            console.log(posId.toString());
            trackUtils.trackPaymentModeSelected({
                paymentPartner: "PAYTMQR",
                mode: "wallet",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: cart.orderDetail.transactionDetail.paidAmount
            });
            axios({
                method: "POST",
                url: apis.getUrls().payment.createPaytmQR,
                /*url: "http://172.16.16.140:8080/neo-service/rest/v1/wp/cpqr",*/
                data: JSON.stringify({cartId: cart.cartId, paymentPartner: "PAYTMQR", posId: posId.toString()}),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                console.log("Create PAYTM UPI Code response:: " + response);
                if (response != null && !appUtil.checkEmpty(response.data) && response.data.qrCodeId !== undefined) {
                    console.log("The Qr code is : ", response.data.qrCodeId);
                    dispatch({type: "SET_PAYTM_QR_CODE_ID", payload: response.data.qrCodeId});
                    dispatch({type: "SET_PAYTM_QR_STATUS", payload: 0});
                    dispatch(createPaytmQRCanvas(response.data.qrCodeId));
                    /*dispatch(UtilityActions.hideFullPageLoader());*/
                } else {
                    console.log("Null Response received");
                    dispatch({type: "SET_PAYTM_QR_STATUS", payload: -3});
                }
                dispatch(UtilityActions.setPaytmGIFLoader(false));
            }).catch(function (error) {
                console.log("Error Occurred!!", error);
                /*dispatch(UtilityActions.hideFullPageLoader());*/
                dispatch(UtilityActions.setPaytmGIFLoader(false));
                dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
            });
        } else {
            /*dispatch(UtilityActions.hideFullPageLoader());*/
            dispatch(UtilityActions.setPaytmGIFLoader(false));
            dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
            browserHistory.push("/menu")
        }
    }
}

export function createGiftCardCart(props, giftCardCart) {
    return dispatch => {
        const request = {
            deviceKey: props.deviceKey,
            sessionKey: props.sessionKey,
            cartDetail: giftCardCart
        };
        axios({
            method: "POST",
            url: apis.getUrls().cart.createGiftCardCart,
            data: JSON.stringify(request),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log(' createGiftCardCart response ', response);
            let giftCard = response.data.cartDetail;
            StorageUtils.setDeviceKey(response.data.deviceKey);
            StorageUtils.setAuthDetailNoCookieUpdate(response.data);
            //StorageUtils.setCartDetailsInAuthDetail(response.data);
            dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));
            const cartDetail = StorageUtils.getCartDetail();
            giftCard.orderDetail.unitId = cartDetail.orderDetail.unitId;
            cartDetail.orderDetail.terminalId = StorageUtils.getKettleAuthDetails().terminal;
            giftCard.orderDetail.terminalId = cartDetail.orderDetail.terminalId;
            giftCard.orderDetail.brandId = cartDetail.orderDetail.brandId;
            dispatch({type: "GIFT_CARD_CART", payload: giftCard});
            StorageUtils.setGiftCardCartDetails(giftCard);
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(payByPaytmQRForGiftCard(props));
            dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: true});
            //dispatch({type: "SET_SHOW_PAYMODES_FOR_GIFT_CARD", payload: true});
            //dispatch(payWithAGSForGiftCard(props));
        }).catch(function (error) {
            console.log("Error Occurred!!", error);
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function payByPaytmQRForGiftCard(props) {
    return dispatch => {
        dispatch(UtilityActions.setPaytmGIFLoader(true));
        dispatch({
            type: "SET_SHOW_LOADER",
            payload: {showLoader: true, loadingMessage: "Generating QR..."}
        });
        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 1});
        // dispatch(showPaytmQRModalForGiftCard(true, null));
        var cart = StorageUtils.getGiftCardCartDetails();
        if (cart != null) {
            let posId = cart.orderDetail.unitId + '#' + cart.orderDetail.terminalId;
            console.log(posId.toString());
            trackUtils.trackPaymentModeSelected({
                paymentPartner: "PAYTMQR",
                mode: "wallet",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: cart.orderDetail.transactionDetail.paidAmount
            });
            axios({
                method: "POST",
                url: apis.getUrls().payment.createPaytmQR,
                /*url: "http://172.16.16.140:8080/neo-service/rest/v1/wp/cpqr",*/
                data: JSON.stringify({cartId: cart.cartId, paymentPartner: "PAYTMQR", posId: posId.toString()}),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response != null && !appUtil.checkEmpty(response.data) && response.data.qrCodeId !== undefined) {
                    console.log("The Qr code is : ", response.data.qrCodeId);
                    dispatch({type: "SET_PAYTM_QR_CODE_ID", payload: response.data.qrCodeId});
                    dispatch({type: "SET_PAYTM_QR_STATUS", payload: 0});
                    dispatch(createPaytmQRCanvasForGiftCard(response.data.qrCodeId, props));
                    /*dispatch(UtilityActions.hideFullPageLoader());*/
                } else {
                    console.log("Null Response received");
                    dispatch({type: "SET_PAYTM_QR_STATUS", payload: -3});
                }
                dispatch({
                    type: "SET_SHOW_LOADER",
                    payload: {showLoader: false, loadingMessage: null}
                });
                dispatch(UtilityActions.setPaytmGIFLoader(false));
            }).catch(function (error) {
                dispatch({
                    type: "SET_SHOW_LOADER",
                    payload: {showLoader: false, loadingMessage: "Unable to generate QR."}
                });
                console.log("Error Occurred!!", error);
                /*dispatch(UtilityActions.hideFullPageLoader());*/
                dispatch(UtilityActions.setPaytmGIFLoader(false));
                dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
            });
        } else {
            /*dispatch(UtilityActions.hideFullPageLoader());*/
            dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: false, loadingMessage: null}
            });
            dispatch(UtilityActions.setPaytmGIFLoader(false));
            dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
            browserHistory.push("/menu")
        }
    }
}


export function createPaytmQRCanvas(qrCode) {
    return dispatch => {
        var canvas = document.getElementById('paytmQr');
        var qrOptions = StorageUtils.getPaytmQrConfigOptions(qrCode);
        qrOptions.canvas = canvas;
        qrcodel.toCanvas(qrOptions);
        dispatch(checkPaytmQRPaymentStatus(qrCode));
    }
}

export function createPaytmQRCanvasForGiftCard(qrCode, props) {
    return dispatch => {
        var canvas = document.getElementById('paytmQrForGiftCard');
        var qrOptions = StorageUtils.getPaytmQrConfigOptions(qrCode);
        qrOptions.canvas = canvas;
        qrcodel.toCanvas(qrOptions);
        dispatch({
            type: "SET_SHOW_LOADER",
            payload: {showLoader: false, loadingMessage: "Waiting for Payment..."}
        });
        dispatch(checkPaytmQRPaymentStatusForGiftCard(props));
    }
}


export function removePaytmQRCanvasOfgiftCard() {
    return dispatch => {
        var canvas = document.getElementById('paytmQrForGiftCard');
        if (canvas != null) {
            canvas.remove();
        }
    }
}

export function removePaytmQRCanvas() {
    return dispatch => {
        var canvas = document.getElementById('paytmQr');
        if (canvas != null) {
            canvas.remove();
        }
    }
}


export function showPaytmQRModal(showModal, interval) {
    return dispatch => {
        if (showModal != true) {
            clearInterval(interval);
        }
        dispatch({type: "SET_SHOW_PAYTM_QR_MODAL", payload: showModal});
    }
}

export function showPaytmQRModalForGiftCard(showModal, interval) {
    return dispatch => {
        if (showModal != true) {
            clearInterval(interval);
        }
        dispatch({type: "SET_SHOW_PAYTM_QR_MODAL_FOR_GIFT_CARD", payload: showModal});
    }
}


export function checkPaytmQRPaymentStatusForGiftCard(props) {
    return dispatch => {
        var interval;
        clearInterval(interval);
        var timeout = setTimeout(function () {
            clearInterval(interval);
            console.log("Timed out!!!");
            dispatch(setPayTMQRCodeId(null));
            // dispatch(showPaytmQRModalForGiftCard(false, null));
            dispatch(removePaytmQRCanvasOfgiftCard());
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(syncOldCartForGiftCard(props));
        }, 240 * 1000); // 4 min

        dispatch({type: "SET_PAYTM_QR_TIMEOUT", payload: timeout});

        var cart = StorageUtils.getGiftCardCartDetails();
        /*dispatch(UtilityActions.showFullPageLoader("Please wait while we check your PayTM payment status.."));*/

        interval = setInterval(function () {

            console.log("checking paytm payment status for gift card ::::::::::::::::::");
            axios({
                method: "POST",
                url: apis.getUrls().payment.checkPaytmQRPaymentStatus,
                /*url: "http://172.16.16.140:8080/neo-service/rest/v1/wp/cpps",*/
                data: JSON.stringify({cartId: cart.cartId, paymentPartner: "PAYTMQR"}),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                //console.log("Payment response is ::::::::::", JSON.stringify(response));
                if (response != null && response.data != null && response.data.paymentStatus !== undefined) {
                    let payTMPaymentStaus = response.data.paymentStatus;
                    //payTMPaymentStaus = "SUCCESS";
                    console.log("payTMPaymentStaus... ", payTMPaymentStaus);
                    console.log("payTMPayment OrderId... ", response.data.orderId);
                    if (payTMPaymentStaus === "TXN_SUCCESS") {
                        clearInterval(interval);
                        clearTimeout(timeout);
                        dispatch(removePaytmQRCanvasOfgiftCard());
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 2});
                        dispatch({type: "SET_WO_ID", payload: response.data.orderId});
                        dispatch(setPayTMQRCodeId(null));
                        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
                        dispatch(orderCheckoutOfGiftCard(response.data.orderId, props));
                        dispatch({
                            type: "SET_SHOW_LOADER",
                            payload: {showLoader: false, loadingMessage: "Payment Successfull..."}
                        });
                        //dispatch(setShowPaymentQRCodeModal(false));
                        //dispatch(showPaytmQRModal(false, null));
                    }
                    else if (payTMPaymentStaus === "PENDING") {
                        console.log("Payment pending....");
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 0});
                    }
                    else if (payTMPaymentStaus === "TXN_FAILURE") {
                        console.log("Payment failed....");
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: -1});
                        dispatch({
                            type: "SET_SHOW_LOADER",
                            payload: {showLoader: false, loadingMessage: "Payment Failed..."}
                        });
                        clearInterval(interval);
                        clearTimeout(timeout);
                        dispatch(UtilityActions.hideFullPageLoader());
                        dispatch(setPayTMQRCodeId(null));
                        dispatch(removePaytmQRCanvasOfgiftCard());
                        dispatch(syncOldCartForGiftCard(props));
                    }
                    else {
                        console.log("Invalid status received ", JSON.stringify(response));
                        clearInterval(interval);
                        clearTimeout(timeout);
                        dispatch(UtilityActions.hideFullPageLoader());
                        dispatch(setPayTMQRCodeId(null));
                        dispatch(removePaytmQRCanvasOfgiftCard());
                        dispatch(syncOldCartForGiftCard(props));
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 1});
                    }
                } else {
                    clearInterval(interval);
                    clearTimeout(timeout);
                    dispatch(UtilityActions.hideFullPageLoader());
                    dispatch(setPayTMQRCodeId(null));
                    dispatch(removePaytmQRCanvasOfgiftCard());
                    dispatch(syncOldCartForGiftCard(props));
                }
            }).catch((error) => {
                clearInterval(interval);
                clearTimeout(timeout);
                dispatch(setPayTMQRCodeId(null));
                dispatch(removePaytmQRCanvasOfgiftCard());
                dispatch(syncOldCartForGiftCard(props));
                dispatch(UtilityActions.hideFullPageLoader());
                console.log("Payment failed response::::::::::::", JSON.stringify(error));
            });
        }, 5 * 1000); // 5 sec

        dispatch({type: "SET_PAYTM_QR_INTERVAL", payload: interval});


    }
}

export function calculateLowBalance(props) {
    return dispatch => {
        let lowBalance = props.cart.orderDetail.transactionDetail.paidAmount - props.customer.giftCardBalance;
        let addBalance;
        if (lowBalance <= 500) {
            addBalance = 500;
        } else if (lowBalance > 500 && lowBalance <= 1000) {
            addBalance = 1000;
        } else {
            addBalance = 2000;
        }
        props.customer.gitCardMessage = "You have low balance to place this order via Gift Card. Please add " + addBalance + " amount in gift card.";
        dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
    }
}

export function orderCheckoutOfGiftCard(orderId, props) {
    return dispatch => {
        let customer = StorageUtils.getCustomerDetail();
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkoutWOId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data != null) {
                console.log('Payment of gift card has been done.');
                dispatch(UtilityActions.showFullPageLoader("Adding Money to your wallet..."));
                axios({
                    method: "POST",
                    url: apis.getUrls().customer.getGiftCard1,
                    data: StorageUtils.getCustomerDetail().customerId,
                    headers: {'Content-Type': 'application/json'}
                }).then(function (response1) {
                    if (response1.data != null || response1.data != "") {
                        customer.selfGiftCards = response1.data.self;
                        customer.otherGiftCards = response1.data.gift;
                        customer.giftCardBalance = CustomerActions.calculateGiftCardBalance(customer.selfGiftCards);
                        dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
                        StorageUtils.setCustomerDetail(customer);
                        dispatch(addGiftCardToSettle(props, false));
                        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
                    } else {
                        UtilityActions.showPopup("Error in settling cart");
                        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
                    }
                }).catch(function (error) {
                    UtilityActions.showPopup("Error in getting gift card");
                    dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
                    dispatch(UtilityActions.hideFullPageLoader());
                });
            }
        }).catch(function (error) {
            dispatch({type: "SET_ORDER_FAILED", payload: true});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(handleOrderCreationException(error.response.data, null, orderId));
        });
    }
}

export function syncOldCart(props) {
    return dispatch => {
        let cartDetail = StorageUtils.getCartDetail();
        let request = {
            deviceKey: StorageUtils.getDeviceKey(),
            sessionKey: props.sessionKey,
            cartDetail: cartDetail
        }
        axios({
            method: "POST",
            url: apis.getUrls().cart.syncOldCart,
            data: JSON.stringify(request),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {

            // condition for check balance of cart and gift card
            let customer = StorageUtils.getCustomerDetail();
            if (customer.giftCardBalance < cartDetail.orderDetail.transactionDetail.paidAmount) {
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(calculateLowBalance(props));
                dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
            } else {
                dispatch(OrderManagementActions.checkOutOrderByGiftCard());
            }
        }).catch(function (error) {
            dispatch(UtilityActions.hideFullPageLoader());
            console.log("Syncing old cart failed");
        });
    }
}

export function syncOldCartForGiftCard(props) {
    return dispatch => {
        let request = {
            deviceKey: props.deviceKey,
            sessionKey: props.sessionKey,
            cartDetail: StorageUtils.getCartDetail()
        };
        axios({
            method: "POST",
            url: apis.getUrls().cart.syncOldCart,
            data: JSON.stringify(request),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log("syncing old cart");
            dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
        }).catch(function (error) {
            console.log("Syncing old cart failed");
        });
    }
}


export function checkPaytmQRPaymentStatus(payTMQRCodeId) {
    return dispatch => {
        var interval;
        clearInterval(interval);
        var cart = StorageUtils.getCartDetail();
        /*dispatch(UtilityActions.showFullPageLoader("Please wait while we check your PayTM payment status.."));*/

        interval = setInterval(function () {

            console.log("checking paytm payment status ::::::::::::::::::");
            axios({
                method: "POST",
                url: apis.getUrls().payment.checkPaytmQRPaymentStatus,
                /*url: "http://172.16.16.140:8080/neo-service/rest/v1/wp/cpps",*/
                data: JSON.stringify({cartId: cart.cartId, paymentPartner: "PAYTMQR"}),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                //console.log("Payment response is ::::::::::", JSON.stringify(response));
                if (response != null && response.data != null && response.data.paymentStatus !== undefined) {
                    let payTMPaymentStaus = response.data.paymentStatus;
                    //payTMPaymentStaus = "SUCCESS";
                    console.log("payTMPaymentStaus... ", payTMPaymentStaus);
                    console.log("payTMPayment OrderId... ", response.data.orderId);
                    if (payTMPaymentStaus === "TXN_SUCCESS") {
                        clearInterval(interval);
                        dispatch(removePaytmQRCanvas());
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 2});
                        dispatch({type: "SET_WO_ID", payload: response.data.orderId});
                        dispatch(OrderManagementActions.orderCheckoutByWOId(response.data.orderId, null));
                        dispatch(setPayTMQRCodeId(null));
                        //dispatch(setShowPaymentQRCodeModal(false));
                        //dispatch(showPaytmQRModal(false, null));
                    }
                    else if (payTMPaymentStaus === "PENDING") {
                        console.log("Payment pending....");
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 0});
                    }
                    else if (payTMPaymentStaus === "TXN_FAILURE") {
                        console.log("Payment failed....");
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: -1});
                    }
                    else {
                        console.log("Invalid status received ", JSON.stringify(response));
                        clearInterval(interval);
                        dispatch({type: "SET_PAYTM_QR_STATUS", payload: 1});
                    }
                } else {
                    clearInterval(interval);
                }
            }).catch((error) => {
                clearInterval(interval);
                dispatch(setPayTMQRCodeId(null));
                console.log("Payment failed response::::::::::::", JSON.stringify(error));
            });
        }, 5 * 1000); // 5 sec

        dispatch({type: "SET_PAYTM_QR_INTERVAL", payload: interval});

        setTimeout(function () {
            clearInterval(interval);
            console.log("Timed out!!!");
            dispatch(setPayTMQRCodeId(null));
            dispatch(showPaytmQRModal(false, null));
        }, 240 * 1000); // 1 min
    }
}

export function payWithEzetap() {
    return dispatch => {
        dispatch(setShowEzetapLoader(true));
        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Starting your payment process. Please wait."});
        dispatch({type: "SET_SHOW_EZETAP_MODAL", payload: true});
        dispatch({
            type: "SET_EZETAP_STATUS_CODE",
            payload: {code: 0, message: "Starting your payment process. Please wait."}
        });
        axios({
            method: "POST",
            url: apis.getUrls().payment.createEzetap,
            data: JSON.stringify({cartId: StorageUtils.getCartDetail().cartId, paymentPartner: "EZETAP"}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                var paymentRequest = response.data;
                var cart = StorageUtils.getCartDetail();
                //dispatch(UtilityActions.showFullPageLoader("Please insert your card in the card slot at bottom right corner of the device."));
                console.log(cart);
                var data = {
                    "username": StorageUtils.getCustomerDetail().contact,
                    "amount": cart.orderDetail.transactionDetail.paidAmount,
                    "orderId": paymentRequest.receipt,
                    "externalRef2": StorageUtils.getCustomerDetail().name,
                    "externalRef3": "Mobile",
                    "customerMobile": StorageUtils.getCustomerDetail().contact,
                    //"cardType":null,
                    "serviceFee": -1
                };
                console.log("card request data", data);
                axios({
                    method: "POST",
                    url: apis.getUrls().ezetap.cardPayment,
                    data: JSON.stringify(data),
                    headers: {'Content-Type': 'application/json'}
                }).then((response) => {
                    if (response.data.responseObj.IsOperationComplete == true && response.data.responseObj.success == true) {
                        console.log("Payment success response::::::::::::", response);
                        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Payment success. Placing your order."});
                        dispatch({
                            type: "SET_EZETAP_STATUS_CODE",
                            payload: {
                                code: 31,
                                message: "Payment success. Placing your order. Please remove your card."
                            }
                        });
                        dispatch(validateEzetapPayment(response.data));
                        //UtilityActions.showFullPageLoader("Payment success. Placing your order.");
                        dispatch({type: "SET_WO_ID", payload: paymentRequest.receipt});
                        dispatch(OrderManagementActions.orderCheckoutByWOId(paymentRequest.receipt, null));
                    } else {
                        UtilityActions.hideFullPageLoader();
                        //UtilityActions.showPopup("Payment failed. Please try again.");
                        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Payment failed. Please try again."});
                        dispatch({
                            type: "SET_EZETAP_STATUS_CODE",
                            payload: {code: -1, message: "Payment failed. Please try again."}
                        });
                    }
                }).catch((error) => {
                    dispatch(UtilityActions.hideFullPageLoader());
                    //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:error.message});
                    dispatch({
                        type: "SET_EZETAP_STATUS_CODE",
                        payload: {code: -1, message: "Payment failed. Please try again."}
                    });
                    //UtilityActions.showPopup(JSON.stringify(error), "INFO", 4000);
                    console.log("Payment failure response::::::::::::", error);
                });
                dispatch(getEzetapPaymentStatus());
            }
        }).catch(function (error) {
            OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
            trackUtils.trackPaymentFailed({
                paymentPartner: "EZETAP",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                externalOrderId: null,
                reason: "Payment creation request failed"
            });
            dispatch(setPaymentData({
                paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                failureMessage: "Error in initiating payment request. Please try again."
            }));
            UtilityActions.hideFullPageLoader();
            dispatch({type: "SET_EZETAP_STATUS_MESSAGE", payload: "Payment failed. Please try again."});
            //UtilityActions.showPopup("Payment failed. Please try again.");
        });
    }
}

export function payWithEzetapForGiftCard(props) {
    return dispatch => {
        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
        dispatch(setShowEzetapLoader(true));
        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Starting your payment process. Please wait."});
        dispatch({type: "SET_SHOW_EZETAP_MODAL", payload: true});
        dispatch({
            type: "SET_EZETAP_STATUS_CODE",
            payload: {code: 0, message: "Starting your payment process. Please wait."}
        });
        axios({
            method: "POST",
            url: apis.getUrls().payment.createEzetap,
            data: JSON.stringify({cartId: StorageUtils.getGiftCardCartDetails().cartId, paymentPartner: "EZETAP"}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                var paymentRequest = response.data;
                var cart = StorageUtils.getGiftCardCartDetails();
                //dispatch(UtilityActions.showFullPageLoader("Please insert your card in the card slot at bottom right corner of the device."));
                console.log(cart);
                var data = {
                    "username": StorageUtils.getCustomerDetail().contact,
                    "amount": cart.orderDetail.transactionDetail.paidAmount,
                    "orderId": paymentRequest.receipt,
                    "externalRef2": StorageUtils.getCustomerDetail().name,
                    "externalRef3": "Mobile",
                    "customerMobile": StorageUtils.getCustomerDetail().contact,
                    //"cardType":null,
                    "serviceFee": -1
                };
                console.log("card request data", data);
                axios({
                    method: "POST",
                    url: apis.getUrls().ezetap.cardPayment,
                    data: JSON.stringify(data),
                    headers: {'Content-Type': 'application/json'}
                }).then((response) => {
                    if (response.data.responseObj.IsOperationComplete == true && response.data.responseObj.success == true) {
                        console.log("Payment success response::::::::::::", response);
                        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Payment success. Placing your order."});
                        dispatch({
                            type: "SET_EZETAP_STATUS_CODE",
                            payload: {
                                code: 31,
                                message: "Payment success. Placing your order. Please remove your card."
                            }
                        });
                        dispatch(validateEzetapPayment(response.data));
                        //UtilityActions.showFullPageLoader("Payment success. Placing your order.");
                        dispatch({type: "SET_WO_ID", payload: paymentRequest.receipt});
                        //dispatch(OrderManagementActions.orderCheckoutByWOId(paymentRequest.receipt, null));
                        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
                        dispatch(orderCheckoutOfGiftCard(paymentRequest.receipt, props));
                        dispatch({
                            type: "SET_SHOW_LOADER",
                            payload: {showLoader: false, loadingMessage: "Payment Successful..."}
                        });
                    } else {
                        UtilityActions.hideFullPageLoader();
                        //UtilityActions.showPopup("Payment failed. Please try again.");
                        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Payment failed. Please try again."});
                        dispatch({
                            type: "SET_EZETAP_STATUS_CODE",
                            payload: {code: -1, message: "Payment failed. Please try again."}
                        });
                        dispatch(syncOldCartForGiftCard(props));
                    }
                }).catch((error) => {
                    dispatch(UtilityActions.hideFullPageLoader());
                    //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:error.message});
                    dispatch({
                        type: "SET_EZETAP_STATUS_CODE",
                        payload: {code: -1, message: "Payment failed. Please try again."}
                    });
                    //UtilityActions.showPopup(JSON.stringify(error), "INFO", 4000);
                    console.log("Payment failure response::::::::::::", error);
                    dispatch(syncOldCartForGiftCard(props));
                });
                dispatch(getEzetapPaymentStatus());
            }
        }).catch(function (error) {
            OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
            trackUtils.trackPaymentFailed({
                paymentPartner: "EZETAP",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: StorageUtils.getGiftCardCartDetails().orderDetail.transactionDetail.paidAmount,
                externalOrderId: null,
                reason: "Payment creation request failed"
            });
            dispatch(setPaymentData({
                paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                failureMessage: "Error in initiating payment request. Please try again."
            }));
            UtilityActions.hideFullPageLoader();
            dispatch({type: "SET_EZETAP_STATUS_MESSAGE", payload: "Payment failed. Please try again."});
            //UtilityActions.showPopup("Payment failed. Please try again.");
        });
    }
}

export function getEzetapPaymentStatus() {
    return dispatch => {
        var interval = setInterval(function () {
            console.log("calling get payment api ::::::::::::::::::");
            axios({
                method: "GET",
                url: apis.getUrls().ezetap.cardPayment,
                data: JSON.stringify({}),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                dispatch(setShowEzetapLoader(false));
                console.log("Payment get success response::::::::::::", JSON.stringify(response));
                if (response != null && response.data != null) {
                    if (response.data.IsOperationComplete == true) {
                        clearInterval(interval);
                        if (response.data.success != true) {
                            //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:"Error in payment. Please try again."});
                            //dispatch({type:"SET_EZETAP_STATUS_CODE", payload:-1});
                            //UtilityActions.showPopup("Error in payment. Please try again.");
                        }
                        dispatch(UtilityActions.hideFullPageLoader());
                    } else {
                        //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:response.data.messageResponse.msg});
                        dispatch({
                            type: "SET_EZETAP_STATUS_CODE",
                            payload: {
                                code: response.data.messageResponse.status,
                                message: response.data.messageResponse.msg
                            }
                        });
                        //dispatch(UtilityActions.showFullPageLoader(response.data.messageResponse.msg));
                    }
                } else {
                    clearInterval(interval);
                }
            }).catch((error) => {
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(setShowEzetapLoader(false));
                //dispatch({type:"SET_EZETAP_STATUS_MESSAGE", payload:error.message});
                //UtilityActions.showPopup("Error in payment. Please try again.");
                //console.log("Payment get failure response::::::::::::", JSON.stringify(error));
            });
        }, 1 * 1000);
        dispatch({type: "SET_EZETAP_INTERVAL", payload: interval});
    }
}

export function validateEzetapPayment(obj) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().payment.ezetapValidate,
            data: JSON.stringify(obj.responseObj.transactionResponse),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}

export function cancelEzetapPayment() {
    return dispatch => {
        axios({
            method: "GET",
            url: apis.getUrls().ezetap.cancelPayment,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {
            console.log("cancel response", response);
        }).catch((error) => {
            console.log("cancel error", error);
        });
    }
}

export function setShowEzetapPaymentModal(showModal, message, ezetapInterval) {
    return dispatch => {
        if (showModal == false) {
            clearInterval(ezetapInterval);
            dispatch({type: "SET_EZETAP_INTERVAL", payload: null});
        }
        dispatch({type: "SET_SHOW_EZETAP_MODAL", payload: showModal});
        dispatch({type: "SET_EZETAP_STATUS_MESSAGE", payload: message});
    }
}

export function setShowCashPaymentModal(showModal, amountExceeded, otpNeeded) {
    return dispatch => {
        dispatch({type: "SET_SHOW_CASH_MODAL", payload: showModal});
        dispatch({type: "SET_CASH_AMOUNT_EXCEEDED", payload: amountExceeded});
        dispatch({type: "SET_CASH_AMOUNT_OTP_NEEDED", payload: otpNeeded});
    }
}

export function setShowEzetapLoader(showLoader) {
    return dispatch => {
        dispatch({type: "SET_SHOW_EZETAP_LOADER", payload: showLoader});
    }
}

export function payByCash(cart, timeoutsArray) {
    return dispatch => {
        console.log('cart is::: ' + cart.toString());
        trackUtils.trackPaymentModeSelected({
            paymentPartner: "CHAAYOS",
            mode: "CASH",
            orderMode: StorageUtils.getLocalityMetadata().criteria,
            amount: cart.orderDetail.transactionDetail.paidAmount
        });
        dispatch(OrderManagementActions.orderCheckout(1, timeoutsArray));
    }
}

export function setPaymentData(data) {
    return dispatch => {
        dispatch({type: "SET_PAYMENT_DATA", payload: data});
    }
}

export function setPaymentMinuteTimer(min) {
    return dispatch => {
        dispatch({type: "SET_AGS_INTERVAL", payload: min});
    }
}

export function setPaymentInitiated(data) {
    return dispatch => {
        dispatch({type: "SET_PAYMENT_INITIATED", payload: data});
        dispatch(setPaymentData({
            paymentMessage: "Initiating payment request. Please wait and do not refresh this page.",
            paymentStatus: "INITIATED",
            failureMessage: null,
            showLoader: true
        }));
    }
}

export function cancelPayment(receipt, status, cancelledBy, cancellationReason, failureReason) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().payment.cancel,
            data: JSON.stringify({
                receiptId: receipt,
                status: status,
                cancelledBy: cancelledBy,
                cancellationReason: cancellationReason,
                failureReason: failureReason
            }),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}

export function failurePayment(receipt, status, cancelledBy, cancellationReason, failureReason) {
    return dispatch => {
        OrderManagementActions.slackWOId(receipt);
        axios({
            method: "POST",
            url: apis.getUrls().payment.failure,
            data: JSON.stringify({
                receiptId: receipt,
                status: status,
                cancelledBy: cancelledBy,
                cancellationReason: cancellationReason,
                failureReason: failureReason
            }),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}

export function cancelPaymentBackend(error, reason) {
    return dispatch => {
        if (!appUtil.checkEmpty(error)) {
            dispatch(failurePayment(error, "FAILED", "CUSTOMER", "", reason));
        }
        dispatch(setPaymentData({
            paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
            failureMessage: "Payment failed. Please try again or call at 1800-120-2424 for support."
        }));
    }
}

export function validateRzp(obj) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().payment.validate,
            data: JSON.stringify(obj),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}

export function sendKeepAliveToEzetap() {
    return dispatch => {
        setInterval(function () {
            axios({
                method: "GET",
                url: "http://localhost:9001/ezeapi/keepalive",
                data: JSON.stringify({}),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                console.log("Keep alive success response::::::::::::", JSON.stringify(response));
            }).catch((error) => {
                console.log("Keep alive failure response::::::::::::", JSON.stringify(error));
            });
        }, 60 * 1000);
    }
}

function togglePaymentLoader(value) {
    return function (dispatch) {
        dispatch({type: "SET_PAYMENT_LOADER", payload: value});
    };
}

export function setShowPaymentQRCodeModal(val) {
    return dispatch => {
        /*if(val === true) {
            document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
        } else {
            document.getElementsByTagName("BODY")[0].style.overflow = "auto";
        }*/
        dispatch({type: "SET_PAYTM_QR_CODE_ID", payload: null});
        /*dispatch({type: "SET_SHOW_PAYTM_QR_SECTION", payload:val});*/
    }
}

export function setPayTMQRCodeId(val) {
    return dispatch => {
        dispatch({type: "SET_PAYTM_QR_CODE_ID", payload: val});
    }
}

export function setPaytmPaymentSuccess(value) {
    return function (dispatch) {
        dispatch({type: "SET_PAYTM_PAYMENT_SUCCESS", payload: value});
    };
}

export function generateOTP(contact) {
    return dispatch => {
        dispatch(setCashPaymentOTPMessage(null, false));
        dispatch(setCashPaymentOTPMessage(null, true));
        console.log('generating OTP');
        axios({
            method: "POST",
            url: apis.getUrls().customer.lookup,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data == -2) {
                dispatch(setCashPaymentOTPMessage("You are an internal customer and hence not authorised to place order here.", true));
            }
            if (response.data == 1) {
                dispatch(setCashPaymentOTPMessage("An OTP has been sent to your contact.", false));
            } else {
                dispatch(setCashPaymentOTPMessage("Failed to generate OTP.", true));
            }
        }).catch(function (error) {
            dispatch(setCashPaymentOTPMessage("Failed to generate OTP.", true));
        });
    }
}

export function generateOTPForRedeemChai(contact) {
    return dispatch => {
        console.log('generating OTP');
        axios({
            method: "POST",
            url: apis.getUrls().customer.generateOtp,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response) {
                console.log('generated OTP');
                dispatch(setCashPaymentOTPMessage("An OTP has been sent to your contact.", false));
                dispatch({type: "SET_OTP_MESSAGE", payload: null});
                dispatch({type: "SHOW_OTP", payload: true});
                dispatch({type: "SHOW_OK", payload: false});
            } else {
                dispatch(setCashPaymentOTPMessage("Failed to generate OTP.", true));
            }
        }).catch(function (error) {
            dispatch({
                type: "SET_OTP_MESSAGE",
                payload: "Something went wrong while generating OTP. Please try later."
            });
            dispatch(setCashPaymentOTPMessage("Something went wrong while generating OTP. Please try later.", true));
        });
    }
}

export function setCashPaymentOTPMessage(val, isError) {
    return dispatch => {
        if (isError) {
            dispatch({type: "SET_CASH_OTP_ERROR_RESPONSE_MESSAGE", payload: val});
        } else {
            dispatch({type: "SET_CASH_OTP_SUCCESS_RESPONSE_MESSAGE", payload: val});
        }

    }
}

export function resendVerification(contact) {
    return dispatch => {
        dispatch(setCashPaymentOTPMessage(null, false));
        dispatch(setCashPaymentOTPMessage(null, true));
        axios({
            method: "POST",
            url: apis.getUrls().customer.resendVerification,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data) {
                if (response.data.success === true) {
                    dispatch(setCashPaymentOTPMessage("OTP resent successfully. Please check your phone.", false));
                    document.getElementById("otpInput").focus();
                    trackUtils.trackOTPResent({contact: contact, status: "SUCCESS"});
                } else {
                    dispatch(setCashPaymentOTPMessage("You have tried too many OTP attempts. Please try again in "
                        + response.data.sec + "seconds.", true));
                    dispatch(initOTPResendClock(response.data.sec));
                }
            } else {
                dispatch(setCashPaymentOTPMessage("Error sending OTP. Please try later.", true));
            }
        }).catch(function (error) {
            dispatch(setCashPaymentOTPMessage("Error sending OTP. Please try later.", true));
            trackUtils.trackOTPResent({contact: contact, status: "FAILED"});
        })
    }
}

export function verifyOTP(contact, otp) {
    return dispatch => {
        dispatch(setCashPaymentOTPMessage(null, false));
        dispatch(setCashPaymentOTPMessage(null, true));
        let customer = {contact: contact, otp: otp};
        axios({
            method: "POST",
            url: apis.getUrls().customer.verifyOTP,
            data: JSON.stringify(customer),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log(response);
            if (response.data != null) {
                if (response.data == true) {
                    dispatch(setCashPaymentOTPMessage("OTP verified, Placing Order...", false));
                    dispatch({type: "SET_CASH_ORDER_LOADER", payload: true});
                    dispatch(OrderManagementActions.orderCheckout(1, null));
                } else {
                    dispatch(setCashPaymentOTPMessage("Incorrect OTP! Please enter correct OTP", true));
                }
            } else {
                dispatch(setCashPaymentOTPMessage("Error verifying OTP. Please try later.", true));
            }
        }).catch(function (error) {
            dispatch(setCashPaymentOTPMessage("Error verifying OTP. Please try later.", true));
        })
    }
}

export function verifyOtpToRedeemChai(props, source) {
    return dispatch => {
        const customer = {
            contact: props.customer.contact,
            otp: props.customer.otpEntered
        }
        dispatch({type: "SET_OTP_MESSAGE", payload: null});
        axios({
            method: "POST",
            url: apis.getUrls().customer.verifyOTP,
            data: JSON.stringify(customer),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log('otp verified');
            if (response.data != null) {
                if (response.data == true) {
                    dispatch({type: "SET_OTP_MESSAGE", payload: "OTP verified!!!"});
                    dispatch({type: "SET_SHOW_CC_OTP", payload: false});
                    dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
                    props.customer.otpVerified = true;
                    props.dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                    StorageUtils.setCustomerDetail(props.customer);
                    // apply chaayos cash
                    if (source == "CHAAYOS_CASH") {
                        dispatch(applyChaayosCash(props));
                    } else { // apply loyal tea
                        dispatch(addRedemptionToOrder(props));
                        dispatch({type: "SHOW_OTP", payload: false});
                        dispatch({type: "AVAIL_REDEEM_CHAAYOS", payload: true});
                        dispatch({type: "SET_OTP_MESSAGE", payload: null});

                        // dispatch(OutletMenuActions.setShowRedeemChaayosCashModal(false));
                    }
                } else {
                    dispatch({type: "SET_OTP_MESSAGE", payload: "Incorrect OTP! Please enter correct OTP"});

                }
            } else {
                dispatch({type: "SET_OTP_MESSAGE", payload: "Error verifying OTP. Please try later."});

            }
        }).catch(function (error) {
            dispatch({type: "SET_OTP_MESSAGE", payload: "Something went wrong. Please try later."});
        })
    }
}

export function verifyOtpForGiftCard(props) {
    return dispatch => {
        const customer = {
            contact: props.customer.contact,
            otp: props.customer.otpEntered
        };
        dispatch(UtilityActions.showFullPageLoader("Verifying OTP..."));
        dispatch({type: "SET_OTP_MESSAGE", payload: null});
        axios({
            method: "POST",
            url: apis.getUrls().customer.verifyOTP,
            data: JSON.stringify(customer),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log('otp verified');
            if (response.data != null) {
                if (response.data == true) {
                    dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
                    // let customer = StorageUtils.getCustomerDetail();
                    props.customer.otpVerified = true;
                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                    StorageUtils.setCustomerDetail(props.customer);
                    dispatch({type: "SET_OTP_MESSAGE", payload: "OTP verified!!!"});
                    let otpSource = props.customer.otpSource;
                    console.log('verifyOtpForGiftCard props ', props);
                    console.log('verifyOtpForGiftCard storage utils ', props.customer);

                    if (otpSource == "GiftCard") {
                        dispatch(addGiftCardToSettle(props))
                    } else if (otpSource == "RedeemChai") {
                        dispatch(addRedemptionToOrder(props));
                    } else if (otpSource == "ChaayosCash") {
                        dispatch(applyChaayosCash(props));
                    }
                } else {
                    dispatch({type: "SET_OTP_MESSAGE", payload: "Incorrect OTP! Please enter correct OTP"});
                    dispatch(UtilityActions.hideFullPageLoader());
                }
            } else {
                dispatch({type: "SET_OTP_MESSAGE", payload: "Error verifying OTP. Please try later."});
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            dispatch({type: "SET_OTP_MESSAGE", payload: "Something went wrong. Please try later."});
            dispatch(UtilityActions.hideFullPageLoader());
        })
    }
}

export function addGiftCardToSettle(props, doSyncOldCart) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader("Placing your order..."));
        dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
        let cart = StorageUtils.getCartDetail();
        let customer = StorageUtils.getCustomerDetail();
        let balanceAmount = cart.orderDetail.transactionDetail.paidAmount;
        let paidAmount = 0;
        cart.orderDetail.settlements = [];
        cart.orderDetail.settlements.push({
            mode: 10,
            amount: balanceAmount,
            externalSettlements: []
        });

        // axios({
        //     method: "POST",
        //     url: apis.getUrls().customer.getGiftCard1,
        //     data: customer.customerId,
        //     headers: {'Content-Type': 'application/json'}
        // }).then(function (response1) {
        //     if (response1.data != null || response1.data != "") {
        //         customer.selfGiftCards = response1.data.self;
        //         customer.otherGiftCards = response1.data.gift;
        //         customer.giftCardBalance = CustomerActions.calculateGiftCardBalance(customer.selfGiftCards);
        //         dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
        //         StorageUtils.setCustomerDetail(customer);
                customer.selfGiftCards.forEach((card) => {
                    if (balanceAmount > 0) {
                        let paidAmount = balanceAmount >= card.cashPendingAmount ? card.cashPendingAmount : balanceAmount;
                        cart.orderDetail.settlements[0].externalSettlements.push({
                            amount: paidAmount,
                            externalTransactionId: card.cashCardId
                        });
                        balanceAmount = balanceAmount - paidAmount;
                    }
                });
                dispatch({type: "UPDATE_CART", payload: cart});
                StorageUtils.setCartDetail(cart);

                if (!doSyncOldCart) {
                    dispatch(syncOldCart(props));
                } else {
                    dispatch(OrderManagementActions.checkOutOrderByGiftCard());
                }
        //     } else {
        //         UtilityActions.showPopup("Error in settling cart");
        //         dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
        //     }
        // }).catch(function (error) {
        //     UtilityActions.showPopup("Error in getting gift card");
        //     dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
        //     dispatch(UtilityActions.hideFullPageLoader());
        // });
    }
}

export function applyChaayosCash(props) {
    return dispatch => {

        let reqObj = {
            order: props.cart.orderDetail,
            couponCode: "CHAAYOS_CASH",
        };
        dispatch(UtilityActions.showFullPageLoader("Applying Chaayos cash..."));

        reqObj.order.customerId = props.customer.customerId;
        axios({
            method: "POST",
            url: apis.getUrls().offer.applyChaayosCash,
            data: JSON.stringify(reqObj),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log('ápplied chaayos cash ', response.data);
            if (!response.data.error) {
                dispatch({type: "SET_OTP_MESSAGE", payload: "Chaayos cash applied!!!"});
                dispatch({type: "AVAIL_REDEEM_CHAAYOS", payload: true});
                dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});

                _.map(props.cart.orderDetail.orders, (orderItem) => {
                    _.map(response.data.order.orders, (oi) => {
                        if (orderItem.itemId == oi.itemId) {
                            orderItem.discountDetail = oi.discountDetail;
                        }
                    });
                });
                props.cart.orderDetail.offerCode = response.data.couponCode;
                props.cart.orderDetail.cashRedeemed = response.data.order.cashRedeemed;
                props.cart.orderDetail.transactionDetail = response.data.order.transactionDetail;
                let cart = appUtil.calculateTaxes(props.cart, false);

                dispatch({type: "SYNC_CART_PENDING"});
                axios({
                    method: "POST",
                    url: apis.getUrls().cart.sync,
                    data: JSON.stringify(cart),
                    headers: {'Content-Type': 'application/json'}
                }).then(function (response) {
                    if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                        dispatch({type: "UPDATE_CART", payload: cart});
                        StorageUtils.setCartDetail(cart);
                        props.customer.availChaayosCash = true;
                        dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                        StorageUtils.setCustomerDetail(props.customer);
                        // dispatch({type: "SET_OTP_MESSAGE", payload: null});
                        dispatch({type: "SET_SHOW_CC_OTP", payload: false});
                        dispatch(UtilityActions.hideFullPageLoader());

                        if (cart.orderDetail.transactionDetail == null || cart.orderDetail.transactionDetail.paidAmount < 1) {
                            if (props.customer.availChaayosCash) {
                                // checkout order by cash and then navigate to order success page
                                dispatch(UtilityActions.showFullPageLoader("We are placing your order. Please Wait..."));
                                dispatch(OrderManagementActions.orderCheckout(1, null));
                            }
                        }
                    }
                }).catch(function (error) {
                    dispatch({type: "SYNC_CART_REJECTED", payload: error});
                    dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
                    dispatch(UtilityActions.hideFullPageLoader());
                });
                // dispatch(OutletMenuActions.setShowRedeemChaayosCashModal(false));
            } else {
                dispatch({type: "SET_OTP_MESSAGE", payload: response.data.errorMessage});
                props.customer.availChaayosCash = false;
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                StorageUtils.setCustomerDetail(props.customer);
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            dispatch({type: "SET_OTP_MESSAGE", payload: "Something went wrong. Please try again later."});
            props.customer.availChaayosCash = false;
            dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
            StorageUtils.setCustomerDetail(props.customer);
            dispatch(UtilityActions.hideFullPageLoader());
        })
    };
}

export function addRedemptionToOrder(props, isSecondFree) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader("Redeeming your chai. Please Wait..."));
        dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});
        let count = 0;
        let count1 = 0;
        let subTotal = 0;
        let originalAmount = 0;
        let paidAmount = 0;
        let promotionalOffer = 0;
        let offerDiscount = 0;
        let tax = 0.0;
        let totalAmount = 0.0;
        let transactionalObject = appUtil.getNewTransactionObject();
        let orderItem1 = {};
        let found = false;

        console.log('cart before ', props.cart);
        const newCart = JSON.parse(JSON.stringify(props.cart));

        dispatch({type: "BEFORE_REDEMPTION_CART", payload: newCart});

        props.cart.orderDetail.transactionDetail.taxes = [];
        props.cart.orderDetail.orders.forEach((orderItem) => {
            orderItem.discountDetail = appUtil.getEmptyDiscountObj();
            if (count < props.customer.chaiRedeemed) {
                if (orderItem.hasBeenRedeemed != true
                    && StorageUtils.getRedemptionChaiProductIds().indexOf(orderItem.productId) != -1 &&
                    orderItem.dimension == "Regular") {
                    // && orderItem.quantity <= (props.customer.chaiRedeemed - count)) {
                    if (orderItem.quantity > props.customer.chaiRedeemed) {
                        found = true;
                        orderItem1 = {...orderItem};
                        orderItem1.taxes = JSON.parse(JSON.stringify(orderItem.taxes));
                        orderItem1.hasBeenRedeemed = false;
                        orderItem1.taxes[0].value = 0.0;
                        orderItem1.taxes[0].total = 0.0;
                        orderItem1.taxes[0].taxable = 0.0;
                        orderItem1.taxes[1].value = 0.0;
                        orderItem1.taxes[1].taxable = 0.0;
                        orderItem1.taxes[1].total = 0.0;
                        orderItem1.discountDetail = appUtil.getEmptyDiscountObj();
                        orderItem1.complimentaryDetail = null;
                    }
                    count1 += (orderItem.quantity > props.customer.chaiRedeemed ? props.customer.chaiRedeemed - count : orderItem.quantity);
                    if (orderItem.quantity > props.customer.chaiRedeemed) {
                        let quantity = orderItem.quantity;
                        orderItem.quantity = (props.customer.chaiRedeemed - count);
                        orderItem1.quantity = quantity - orderItem.quantity;
                        orderItem.amount = orderItem.quantity * orderItem.price;
                        orderItem.totalAmount = orderItem.quantity * orderItem.price;
                        orderItem.originalTax = ((orderItem.taxes[0].percentage * (orderItem.quantity * orderItem.price)) / 100) + (orderItem.taxes[1].percentage * (orderItem.quantity * orderItem.price)) / 100;
                    }
                    count = count1;
                    orderItem.hasBeenRedeemed = true;
                    orderItem.complimentaryDetail = {
                        isComplimentary: true,
                        reasonCode: 2101,
                        reason: null
                    };
                    orderItem.discountDetail.promotionalOffer = orderItem.amount;
                    orderItem.discountDetail.totalDiscount = orderItem.discountDetail.promotionalOffer + orderItem.discountDetail.discount.value;
                    orderItem.tax = 0;
                    orderItem.amount = 0;
                    orderItem.taxes[0].value = 0.0;
                    orderItem.taxes[0].total = orderItem.totalAmount;
                    orderItem.taxes[0].taxable = 0.0;
                    orderItem.taxes[1].value = 0.0;
                    orderItem.taxes[1].total = orderItem.totalAmount;
                    orderItem.taxes[1].taxable = 0.0;
                }
            }

            if (found) {
                orderItem1.amount = orderItem1.quantity * orderItem1.price;
                orderItem1.totalAmount = orderItem1.quantity * orderItem1.price;
                orderItem1.taxes[0].taxable = orderItem1.quantity * orderItem1.price;
                orderItem1.taxes[0].total = orderItem1.quantity * orderItem1.price;
                orderItem1.taxes[0].value = (orderItem1.taxes[0].percentage * (orderItem1.quantity * orderItem1.price)) / 100;
                orderItem1.taxes[1].taxable = orderItem1.quantity * orderItem1.price;
                orderItem1.taxes[1].total = orderItem1.quantity * orderItem1.price;
                orderItem1.taxes[1].value = (orderItem1.taxes[1].percentage * (orderItem1.quantity * orderItem1.price)) / 100;
                orderItem1.tax = orderItem1.taxes[0].value + orderItem1.taxes[1].value;
                orderItem1.originalTax = orderItem1.taxes[0].value + orderItem1.taxes[1].value;
            }
        });

        if (found) {
            props.cart.orderDetail.orders.push(orderItem1);
        }

        console.log('before tax ', props.cart.orderDetail);
        props.cart.orderDetail.orders.forEach((orderItem, index) => {
            if (props.cart.orderDetail.transactionDetail.taxes.length == 0) {
                props.cart.orderDetail.transactionDetail.taxes = JSON.parse(JSON.stringify(orderItem.taxes));
            } else {
                orderItem.taxes.forEach((atax) => {
                    // let found = false;
                    props.cart.orderDetail.transactionDetail.taxes.forEach((tdx) => {
                        if (tdx.code == atax.code && tdx.percentage == atax.percentage) {
                            tdx.taxable += atax.taxable;
                            tdx.total += atax.total;
                            tdx.value = parseFloat(tdx.value) + parseFloat(atax.value);
                            // found = true;
                        }
                    });
                });
            }


            offerDiscount = offerDiscount + orderItem.discountDetail.discount.value;
            promotionalOffer = promotionalOffer + parseFloat(orderItem.discountDetail.promotionalOffer);
            subTotal = subTotal + parseFloat(orderItem.amount);
            originalAmount = originalAmount + orderItem.totalAmount;
            originalAmount = originalAmount + parseFloat(orderItem.originalTax);
            paidAmount = paidAmount + orderItem.amount;
            paidAmount = paidAmount + parseFloat(orderItem.tax);
            tax += parseFloat(orderItem.tax);
            totalAmount = totalAmount + (orderItem.quantity * orderItem.price)
        });

        props.cart.orderDetail.transactionDetail.taxableAmount = subTotal;
        props.cart.orderDetail.transactionDetail.totalAmount = totalAmount;
        let finalPaid = Math.round(parseFloat(paidAmount));
        props.cart.orderDetail.transactionDetail.paidAmount = finalPaid;
        props.cart.orderDetail.transactionDetail.roundOffValue = finalPaid - paidAmount;
        props.cart.orderDetail.transactionDetail.savings = Math.round(parseFloat(originalAmount) - finalPaid);
        props.cart.orderDetail.transactionDetail.discountDetail.promotionalOffer = promotionalOffer;
        props.cart.orderDetail.transactionDetail.discountDetail.discount.value = offerDiscount;
        props.cart.orderDetail.transactionDetail.discountDetail.totalDiscount = promotionalOffer + offerDiscount;
        props.cart.orderDetail.transactionDetail.tax = tax;

        if (isSecondFree || props.customer.eligibleForSignupOffer) {
            props.cart.orderDetail.pointsRedeemed = 0;
            props.cart.orderDetail.containsSignupOffer = true;
        } else {
            props.cart.orderDetail.pointsRedeemed = -(props.customer.chaiRedeemed * 60);
        }

        console.log('cart after ', props.cart);
        dispatch({type: "UPDATE_CART", payload: props.cart});
        StorageUtils.setCartDetail(props.cart);
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(props.cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                props.customer.redemptionDone = true;
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                StorageUtils.setCustomerDetail(props.customer);
                dispatch(UtilityActions.hideFullPageLoader());
                if (props.selectedOrderSource != null) {
                    dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false});

                    if (props.cart.orderDetail.transactionDetail == null || props.cart.orderDetail.transactionDetail.paidAmount < 1) {
                        if (props.customer.chaiRedeemed > 0) {
                            // checkout order by cash and then navigate to order success page
                            dispatch(UtilityActions.showFullPageLoader("We are placing your order. Please Wait..."));
                            dispatch(OrderManagementActions.orderCheckout(1, null));
                        }
                    }

                    browserHistory.push("/paymentModes");
                } else {
                    dispatch({type: "SET_SHOW_GIFT_CARD_PAYMENT_MODAL", payload: false})
                    dispatch({type: "SET_ORDER_SOURCE_MESSAGE", payload: "Please select your eating preference"});
                }
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch({type: "SYNC_CART_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
        });
    }
}

export function removeRedeemChai(props, haveItLater) {
    return dispatch => {
        let cart = props.beforeRedemptionCart;
        if (props.beforeRedemptionCart == null) {
            cart = StorageUtils.getCartDetail();
        }
        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                props.customer.redemptionDone = false;
                props.customer.chaiRedeemed = 0;
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                StorageUtils.setCustomerDetail(props.customer);
                if (haveItLater) {
                    props.customer.redemptionDone = true;
                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                    StorageUtils.setCustomerDetail(props.customer);
                    if (props.selectedOrderSource != null) {

                        if (props.cart.orderDetail.transactionDetail == null || props.cart.orderDetail.transactionDetail.paidAmount < 1) {
                            if (props.customer.chaiRedeemed > 0) {
                                // checkout order by cash and then navigate to order success page
                                dispatch(UtilityActions.showFullPageLoader("We are placing your order. Please Wait..."));
                                dispatch(OrderManagementActions.orderCheckout(1, null));
                            }
                        }

                        browserHistory.push("/paymentModes");
                    } else {
                        dispatch({type: "SET_ORDER_SOURCE_MESSAGE", payload: "Please select your eating preference"});
                    }
                } else {
                    dispatch({type: "UPDATE_CART", payload: props.beforeRedemptionCart});
                    StorageUtils.setCartDetail(props.beforeRedemptionCart);
                }
            }
        }).catch(function (error) {
            dispatch({type: "SYNC_CART_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
        });
    }
}


export function removeChaayosCash(props) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader("Removing Chaayos Cash..."));

        props.customer.availChaayosCash = false;
        _.map(props.cart.orderDetail.orders, (orderItem) => {
            orderItem.discountDetail = appUtil.getEmptyDiscountObj();
        });
        props.cart.orderDetail.offerCode = "";
        // props.cart.orderDetail.transactionDetail = response.data.order.transactionDetail;
        let cart = appUtil.calculateTaxes(props.cart, false);

        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                dispatch({type: "UPDATE_CART", payload: cart});
                StorageUtils.setCartDetail(cart);
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: props.customer});
                StorageUtils.setCustomerDetail(props.customer);
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            dispatch({type: "SYNC_CART_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function setSelectivePaymentMode(mode) {
    return dispatch => {
        StorageUtils.setSelectivePaymentMode(mode);
        dispatch({type: "SET_SELECTIVE_PAYMENT_MODE", payload: mode});
    }
}

export function setCardPaymentMode(mode) {
    return dispatch => {
        StorageUtils.setCardPaymentMode(mode);
        dispatch({type: "SET_CARD_PAYMENT_MODE", payload: mode});
    }
}

export function updateSelectivePaymentMode() {
    return dispatch => {
        dispatch({type: "SET_SELECTIVE_PAYMENT_MODE", payload: StorageUtils.getSelectivePaymentMode()});
    }
}

export function updateCardPaymentMode() {
    return dispatch => {
        dispatch({type: "SET_CARD_PAYMENT_MODE", payload: StorageUtils.getCardPaymentMode()});
    }
}

export function synCart(props) {
    return dispatch => {
        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(props.giftCardCart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                dispatch(setPaymentInitiated(true));
                dispatch(payByPaytmQRForGiftCard(props));
            }
        }).catch(function (error) {
            dispatch({type: "SYNC_CART_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
        });
    }
}

function executeAGSResponse(dispatch, responseString, generatedOrderId) {
    if(!appUtil.checkEmpty(responseString)){
        console.log('response :: ' + responseString);
        let response = JSON.parse(responseString);
        dispatch({
            type: "SET_AGS_STATUS",
            payload: {code: response.code,
                      title: response.title,
                       msg: response.msg}
        });
        if(response.code === 6){
            console.log("Success!!!!");
            if(!appUtil.checkEmpty(response.trnData)){
                dispatch(validateAGSPayment(response.trnData));
            }
            dispatch({type: "SET_WO_ID", payload: generatedOrderId});
            dispatch(OrderManagementActions.orderCheckoutByWOId(generatedOrderId, null));
            dispatch({type: "SET_STOP_TIMER", payload: true});
        }
        dispatch(setShowAGSLoader(false));
        UtilityActions.hideFullPageLoader();
    }
}

function executeFailure(dispatch, errorString) {
    if(!appUtil.checkEmpty(errorString)){
        let response = JSON.parse(errorString);
        UtilityActions.hideFullPageLoader();
        dispatch({
            type: "SET_AGS_STATUS",
            payload: {code: response.code,
                title: response.title,
                msg: response.msg}
        });
        dispatch(setShowAGSLoader(false));
    }
}

export function payWithAGSCardMachine() {
    return dispatch => {
        let cart = StorageUtils.getCartDetail();
        let amount = cart.orderDetail.transactionDetail.paidAmount;
        dispatch(setShowAGSLoader(true));
        dispatch({type: "SET_AGS_AMOUNT_TO_PAY", payload: amount});
        dispatch({type: "SET_SHOW_AGS_MODAL", payload: true});
        dispatch({
            type: "SET_AGS_STATUS",
            payload: {code: 0,
                title: "Enter Card or Tap Card",
                msg: "Enter card and enter pin Or tap the card "}
        });
        axios({
            method: "POST",
            url: apis.getUrls().payment.createAGS,
            data: JSON.stringify({cartId: StorageUtils.getCartDetail().cartId, paymentPartner: "AGS"}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (res) {
            console.log(JSON.stringify(res));
            if (!appUtil.checkEmpty(res.data)) {
                let resData = JSON.stringify(res.data);
                let resJSON = JSON.parse(resData);
                let generatedOrderId = resJSON.receipt;
                let printFlag = 0;
                AGSUtility.makeSaleRequest(generatedOrderId, amount, printFlag)
                    .then(machineResponse => {
                        console.log("Received response from card machine");
                        executeAGSResponse(dispatch, machineResponse, generatedOrderId);
                    }).catch(machineError => {
                        executeFailure(dispatch, machineError);
                    });
            }
        }).catch(function (error) {
            OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
            trackUtils.trackPaymentFailed({
                paymentPartner: "AGS",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,
                externalOrderId: null,
                reason: "Payment creation request failed"
            });
            dispatch(setPaymentData({
                paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                failureMessage: "Error in initiating payment request. Please try again."
            }));
            UtilityActions.hideFullPageLoader();
            dispatch({type: "SET_AGS_STATUS_MESSAGE", payload: "Payment failed. Please try again."});
        });
    }
}

export function setShowAGSPaymentModal(showModal, message, agsInterval) {
    return dispatch => {
        if (showModal == false) {
            clearInterval(agsInterval);
            dispatch({type: "SET_AGS_INTERVAL", payload: null});
        }
        dispatch({type: "SET_SHOW_AGS_MODAL", payload: showModal});
    }
}

export function setShowAGSLoader(showLoader) {
    return dispatch => {
        dispatch(initializeTimer());
        dispatch({type: "SET_SHOW_AGS_LOADER", payload: showLoader});
    }
}

export function validateAGSPayment(obj) {
    let request = JSON.stringify(obj);
    console.log("validateAGSPayment request ! ", request);
    return dispatch => {

        axios({
            method: "POST",
            url: apis.getUrls().payment.agsValidate,
            data: request,
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {
            console.log('validate order response', response);
        }).catch((error) => {
            console.log('validate order error', error);
        });
    }
}

export function payWithAGSForGiftCard(props) {
    return dispatch => {
        dispatch(showPaytmQRModalForGiftCard(false, null));
        dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
        let giftCardCart = StorageUtils.getGiftCardCartDetails();
        let amount = giftCardCart.orderDetail.transactionDetail.paidAmount;
        dispatch(setShowAGSLoader(true));
        dispatch({type: "SET_AGS_AMOUNT_TO_PAY", payload: amount});
        dispatch({type: "SET_SHOW_AGS_MODAL", payload: true});
        dispatch({
            type: "SET_AGS_STATUS",
            payload: {code: 0,
                title: "Card Payment",
                msg: "Enter card and enter pin Or tap the card "}
        });
        axios({
            method: "POST",
            url: apis.getUrls().payment.createAGS,
            data: JSON.stringify({cartId: StorageUtils.getGiftCardCartDetails().cartId, paymentPartner: "AGS"}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (res) {
            console.log(JSON.stringify(res));
            if (!appUtil.checkEmpty(res.data)) {
                let resData = JSON.stringify(res.data);
                let resJSON = JSON.parse(resData);
                let generatedOrderId = resJSON.receipt;
                let printFlag = 0;
                AGSUtility.makeSaleRequest(generatedOrderId, amount, printFlag)
                    .then(responseString => {
                        executeGiftCardAGSResponse(dispatch, responseString, generatedOrderId, props)
                    }).catch(errorString => {
                        executeGiftCardFailure(dispatch, errorString, props);
                });
            }
        }).catch(function (error) {
            OrderManagementActions.slackCartId(StorageUtils.getGiftCardCartDetails().cartId);
            trackUtils.trackPaymentFailed({
                paymentPartner: "AGS",
                orderMode: StorageUtils.getLocalityMetadata().criteria,
                amount: StorageUtils.getGiftCardCartDetails().orderDetail.transactionDetail.paidAmount,
                externalOrderId: null,
                reason: "Payment creation request failed"
            });
            dispatch(setPaymentData({
                paymentMessage: null, paymentStatus: "FAILED", showLoader: false,
                failureMessage: "Error in initiating payment request. Please try again."
            }));
            UtilityActions.hideFullPageLoader();
            dispatch({type: "SET_AGS_STATUS_MESSAGE", payload: "Payment failed. Please try again."});
        });
    }
}

function executeGiftCardAGSResponse(dispatch, responseString, generatedOrderId, props) {
    if(!appUtil.checkEmpty(responseString)){
        console.log('response :: ' + responseString);
        let response = JSON.parse(responseString);
        dispatch({
            type: "SET_AGS_STATUS",
            payload: {code: response.code,
                title: response.title,
                msg: response.msg}
        });
        if(response.code === 6){
            console.log("Success!!!!");
            if(!appUtil.checkEmpty(response.trnData)){
                dispatch(validateAGSPayment(response.trnData));
            }
            dispatch({type: "SET_WO_ID", payload: generatedOrderId});
            dispatch({type: "SET_SHOW_GIFT_CARD_PURCHASE_MODAL", payload: false});
            dispatch(orderCheckoutOfGiftCard(generatedOrderId, props));
            dispatch({type: "SET_STOP_TIMER", payload: true});
        }else{
            dispatch(syncOldCartForGiftCard(props));
        }
        dispatch(setShowAGSLoader(false));
        UtilityActions.hideFullPageLoader();
    }
}

function executeGiftCardFailure(dispatch, errorString, props) {
    if(!appUtil.checkEmpty(errorString)){
        let response = JSON.parse(errorString);
        UtilityActions.hideFullPageLoader();
        dispatch({
            type: "SET_AGS_STATUS",
            payload: {code: response.code,
                title: response.title,
                msg: response.msg}
        });
        dispatch(syncOldCartForGiftCard(props));
        dispatch(setShowAGSLoader(false));
    }
}

export function initializeTimer(){
    return dispatch => {
        console.log("Initializing timer");
        dispatch({type: "SET_TIMER_MIN", payload: 1});
        dispatch({type: "SET_TIMER_SEC", payload: 0});
        dispatch({type: "SET_SEC_REMAINING", payload: 60});
    }
}