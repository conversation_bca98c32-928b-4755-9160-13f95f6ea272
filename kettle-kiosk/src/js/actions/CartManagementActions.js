import {browserHistory} from "react-router";
import axios from "axios";
import _ from "lodash";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import * as UtilityActions from "./UtilityActions";
import * as OutletMenuActions from "./OutletMenuActions";
import trackUtils from "../utils/TrackUtils";
import * as OrderManagementActions from "./OrderManagementActions";

export function initCart(unit, skipDeliveryPackaging, isInterstate) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var itemRemoved = false;
        var removedItems = [];
        if (!appUtil.checkEmpty(cart) && unit != null) {
            if (cart.orderDetail.orders.length > 0) {
                var orderItems = [];
                var index = 1;
                var cartChanged = false;
                cart.orderDetail.orders.map((item) => {
                    var found = false;
                    if (item.code != null) { //for backward compatibility issues
                        item.discountDetail = appUtil.getEmptyDiscountObj();
                        unit.products.map((product) => {
                            if (product.id == item.productId) {
                                product.prices.map((price) => {
                                    if (price.dimension == item.dimension) {
                                        if (item.price != price.price) {
                                            item.price = price.price;
                                            cartChanged = true;
                                        }
                                        item.itemId = index;
                                        index++;
                                        orderItems.push(item);
                                        found = true;
                                    }
                                });
                                item.customizationStrategy = product.strategy;
                            }
                        });
                        if (!found) {
                            if (item.productId != 1044 && item.productId != 1043) {
                                itemRemoved = true;
                                removedItems.push(item.productName + (item.dimension == "None" ? "-Regular" : "-" + item.dimension));
                            }
                            sendRemoveItemFromCart(cart, item);
                        }
                    }
                });
                cart.orderDetail.orders = orderItems;
                cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();//cart.orderDetail.transactionDetail==null?{}:cart.orderDetail.transactionDetail;
                //setTaxProfile(unit, cart);
                //cart.orderDetail.offerCode = null;
                cart.orderDetail.unitId = unit.id;
                cart.orderDetail.unitName = unit.name;
                cart.orderDetail.stateId = unit.location.state.id;
                cart.orderDetail.brandId = 1;
                cart.orderDetail.terminalId = 786;
                if (StorageUtils.getCampaignDetails() != null && StorageUtils.getCampaignDetails().cid != null) {
                    cart.orderDetail.campaignId = StorageUtils.getCampaignDetails().cid;
                }
                if(!appUtil.checkEmpty(StorageUtils.getLocalityMetadata())
                    && !appUtil.checkEmpty(StorageUtils.getLocalityMetadata().criteria)){
                    console.log("The order type is::: " + StorageUtils.getLocalityMetadata().criteria);
                    cart.orderDetail.hasParcel = (StorageUtils.getLocalityMetadata().criteria == "TAKE_AWAY");
                }
                cart.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria);
                var auth = StorageUtils.getAuthDetail();
                if (!appUtil.checkEmpty(auth)) {
                    cart.deviceId = StorageUtils.getDeviceId();
                    cart.customerId = null;
                    if (!appUtil.checkEmpty(cart.orderDetail)) {
                        cart.orderDetail.webCustomerId = null;
                    }
                }
            }
            dispatch(updateCart(cart));
            dispatch(removeDeliveryAndPackagingFromCart(cart, skipDeliveryPackaging));
            if (cart.orderDetail.source == "COD") {
                dispatch(addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
            }
            if (!appUtil.checkEmpty(cart.orderDetail)) {
                cart.orderDetail.transactionDetail == null ? cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject() : null;
                cart = appUtil.calculateTaxes(cart, isInterstate);
            }
            dispatch(updateCart(cart));
            dispatch(removeCoupon(false));
        }
        if (itemRemoved) {
            cartChanged = true;
            var msg;
            if (cart.orderDetail.source == "COD") {
                msg = "We have removed " + removedItems.join(", ") + " from your cart because " + (removedItems.length == 1 ? "this item is" : "these items are") + " not available in delivery menu for selected location."
            } else {
                msg = "We have removed " + removedItems.join(", ") + " from your cart because " + (removedItems.length == 1 ? "this item is" : "these items are") + " not available in menu of selected outlet."
            }
            dispatch(UtilityActions.showPopup(msg, 'info', 7000));
        }
        if (cartChanged) {
            if (cart.orderDetail.offerCode != null) {
                dispatch(removeOfferInitCart(true));
            }
        }
    }
}

const addDeliveryAndPackagingToCart = (cart, skipDeliveryPackaging) => {
    return dispatch => {
        var deliveryAvailable = false, packagingAvailable = false;
        var cartAmount = 0;
        cart.orderDetail.orders.map((orderItem) => {
            if (orderItem.productId != 1043 && orderItem.productId != 1044) {
                cartAmount += (orderItem.price * orderItem.quantity);
            }
            if (orderItem.productId == 1043) {
                packagingAvailable = true;
            }
            if (orderItem.productId == 1044) {
                deliveryAvailable = true;
            }
        });
        var item = StorageUtils.getDeliveryPackagingCartItems();
        if (!packagingAvailable && !skipDeliveryPackaging.packaging && item.packaging != null) {
            item.packaging.itemId = cart.orderDetail.orders.length + 1;
            cart.orderDetail.orders.push(item.packaging);
            dispatch(updateCart(cart));
            //sendAddItemToCart(cart, item.packaging, "", []);
        }
        if (cartAmount < 200) {
            if (!deliveryAvailable && !skipDeliveryPackaging.delivery && item.delivery != null) {
                item.delivery.itemId = cart.orderDetail.orders.length + 1;
                cart.orderDetail.orders.push(item.delivery);
                dispatch(updateCart(cart));
                //sendAddItemToCart(cart, item.delivery, "", []);
            }
        } else {
            if (deliveryAvailable) {
                cart.orderDetail.orders.map((orderItem) => {
                    if (orderItem.productId == 1044) {
                        dispatch(removeItem(cart, orderItem));
                    }
                });
            }
        }
    }
};

const removeDeliveryAndPackagingFromCart = (cart, skipDeliveryPackaging) => {
    return dispatch => {
        var packaging = null, delivery = null;
        cart.orderDetail.orders.map((orderItem) => {
            if (orderItem.productId == 1044) {
                delivery = orderItem;
            }
            if (orderItem.productId == 1043) {
                packaging = orderItem;
            }
        });
        if (delivery != null) {
            dispatch(removeItem(cart, delivery, "", skipDeliveryPackaging, false));
        }
        if (packaging != null) {
            dispatch(removeItem(cart, packaging, "", skipDeliveryPackaging, false));
        }
    }
};

export function setDeliveryAndPackagingItem(unit) {
    var packaging = null, delivery = null, deliveryItem = null, packagingItem = null;
    unit.products.map((product) => {
        if (product.id == 1044) {
            delivery = product;
        }
        if (product.id == 1043) {
            packaging = product;
        }
    });
    if (delivery != null) {
        deliveryItem = {
            id: delivery.id,
            name: delivery.name,
            price: delivery.prices[0].price,
            dimension: delivery.prices[0].dimension,
            billType: delivery.billType,
            type: delivery.type,
            taxCode: delivery.taxCode
        };
        //appUtil.getDeliveryPackagingCartItem(delivery);
    }
    if (packaging != null) {
        packagingItem = {
            id: packaging.id,
            name: packaging.name,
            price: packaging.prices[0].price,
            dimension: packaging.prices[0].dimension,
            billType: packaging.billType,
            type: packaging.type,
            taxCode: delivery.taxCode
        };
        //appUtil.getDeliveryPackagingCartItem(packaging);
    }
    StorageUtils.setDeliveryPackagingCartItems({delivery: deliveryItem, packaging: packagingItem});
}

export function addItemToCart(cartObj) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var action = "ADD_ITEM";
        if (appUtil.checkEmpty(cart)) {
            cart = appUtil.getNewCartObj();
            action = "CREATE_CART";
        }
        var variants = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.variants.map((variant) => {
            variant.details.map((detail) => {
                if (detail.active) {
                    variants.push(detail);
                }
            })
        });
        var ingredientProducts = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.products.map((product) => {
            product.details.map((detail) => {
                if (detail.active) {
                    ingredientProducts.push(detail);
                }
            })
        });
        var menuProducts = [];
        if (cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct != null) {
            cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.cartProduct != null) {
                    menuProducts.push(detail.cartProduct);
                }
            })
        }
        var cartItem = {
            itemId: cart.orderDetail.orders.length + 1,
            productId: cartObj.cartItem.id,
            customizationStrategy: cartObj.customizationStrategy,
            productName: appUtil.getProductName(cartObj.cartItem.id, cartObj.cartItem.name),
            productCategory: {id: cartObj.cartItem.webType},
            quantity: cartObj.quantity,
            price: cartObj.cartItem.selectedDimension.price,
            totalAmount: null,
            amount: cartObj.quantity * cartObj.cartItem.selectedDimension.price,
            discountDetail: appUtil.getEmptyDiscountObj(),
            addons: [],
            dimension: cartObj.cartItem.selectedDimension.dimension,
            billType: cartObj.cartItem.billType,
            isCombo: false, // TODO fix this by webtype cartObj.cartItem.type == 8,
            composition: {
                variants: variants,
                products: ingredientProducts,
                menuProducts: menuProducts,
                addons: cartObj.addons
            },
            recipeId: cartObj.cartItem.selectedDimension.recipeId,
            code: cartObj.cartItem.taxCode,
            taxes: [],
            tax: 0,
            originalTax: 0
        };
        /*if (!appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
         cart = appUtil.calculateTaxes(cart);
         }*/
        //code to add cart to backend
        if (action == "CREATE_CART") {
            cart.orderDetail.orders.push(cartItem);
            StorageUtils.setCartDetail(cart);
            dispatch({type: "CREATE_CART", payload: cart});
            dispatch(sendCreateCart(cart));
        } else if (action == "ADD_ITEM") {
            /*dispatch({type: "ADD_ITEM_TO_CART", payload: cartItem});
             sendAddItemToCart(cart, cartItem, cartObj.webCategory, cartObj.tags);*/
            //dispatch(addItem(cart, cartItem, cartObj.webCategory, cartObj.tags));
            dispatch({type: "ADD_ITEM_TO_CART"});
            console.log("item being added::::::::::::::::::::::::::::", cartItem);
            cart.orderDetail.orders.push(cartItem);
            cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
            var unitDetail = StorageUtils.getKettleAuthDetails().unitDetail;
            if (unitDetail != null) {
                cart.orderDetail.unitId = unitDetail.id;
                cart.orderDetail.unitName = unitDetail.name;
            }
            cart = appUtil.calculateTaxes(cart, false);
            dispatch(updateCart(cart));
            sendAddItemToCart(cart, cartItem, cartObj.webCategory, cartObj.tags);
        }
    }
}

export function sendCreateCart(cart) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().cart.createCart,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            var cart = StorageUtils.getCartDetail();
            cart.cartId = response.data.cartId;
            dispatch(updateCart(cart));
        }).catch(function (error) {
            //UtilityActions.showPopup("Error in verifying contact number")
        });
    }
}

const sendAddItemToCart = (cart, cartItem, webCategory, tags) => {
    if (cart == null || cart.cartId == null) {
        window.location.reload();
    }
    try {
        trackUtils.trackAddCartItem({
            cartItem: cartItem, cartId: cart.cartId, unitId: cart.orderDetail.unitId,
            unitName: cart.orderDetail.unitName, webCategory: webCategory, tags: tags
        });
    } catch (e) {
    }
    if(cart != null && cart.cartId != null){
        axios({
            method: "POST",
            url: apis.getUrls().cart.addItem,
            data: JSON.stringify({cartId: cart.cartId, orderItem: cartItem}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log("Product added in cart...................", response);
        }).catch(function (error) {
            console.log("Error while adding in cart...................", error);
        });
    }
};

const sendRemoveItemFromCart = (cart, item, screen) => {
    try {
        trackUtils.trackRemoveCartItem({
            cartItem: item,
            cartId: cart.cartId,
            unitId: cart.orderDetail.unitId,
            unitName: cart.orderDetail.unitName,
            screen: screen
        });
    } catch (e) {
    }
    axios({
        method: "POST",
        url: apis.getUrls().cart.removeItem,
        data: JSON.stringify({cartId: cart.cartId, orderItem: item}),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {

    });
};

/*export function addItem(cart, cartItem, webCategory, tags) {
 return dispatch => {
 cart.orderDetail.orders.push(cartItem);
 dispatch(updateCart(cart));
 sendAddItemToCart(cart, cartItem, webCategory, tags);
 }
 }*/

export function updateItemQty(cart, qty, itemId, skipDeliveryPackaging, isInterState) {
    return dispatch => {
        var cartx = Object.assign({}, cart);
        cartx.orderDetail.orders.map((item) => {
            if (item.itemId == itemId) {
                item.quantity = item.quantity + qty;
                item.amount = item.quantity * item.price;
            }
        });
        if (cartx.orderDetail.source == "COD") {
            dispatch(addDeliveryAndPackagingToCart(cartx, skipDeliveryPackaging));
        } else {
            dispatch(removeDeliveryAndPackagingFromCart(cartx));
        }
        if (!appUtil.checkEmpty(cartx.orderDetail) && !appUtil.checkEmpty(cartx.orderDetail.transactionDetail)) {
            cartx = appUtil.calculateTaxes(cartx, isInterState);
        }
        dispatch(updateCart(cartx));
    }
}

export function removeItem(cart, item, screen, skipDeliveryPackaging, isInterState) {
    return dispatch => {
        var items = [];
        var index = 1;
        cart.orderDetail.orders.map((itemx) => {
            if (itemx.itemId != item.itemId) {
                itemx.itemId = index++;
                items.push(itemx);
            }
        });
        cart.orderDetail.orders = items;
        if (!appUtil.checkEmpty(cart.orderDetail) && !appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
            cart = appUtil.calculateTaxes(cart, isInterState);
        }
        dispatch(updateCart(cart));
        if (item.productId != 1043 && item.productId != 1044) {
            sendRemoveItemFromCart(cart, item, screen);
        }
        if (cart.orderDetail.source == "COD") {
            dispatch(addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
        } else {
            dispatch(removeDeliveryAndPackagingFromCart(cart));
        }
    }
}

export function setOrderRemark(cart, remark) {
    return dispatch => {
        if (!appUtil.checkEmpty(remark)) {
            trackUtils.trackOrderRemark(remark);
            var cartx = Object.assign({}, cart);
            cartx.orderDetail.orderRemark = remark;
            dispatch(updateCart(cartx));
        }
    }
}

export function setSyncCart(value) {
    return dispatch => {
        dispatch({type: "SET_SYNC_CART", payload: value});
    }
}

export function placeOrder(cart, unit, selectedCity) {
    return dispatch => {
        var isInterstate = false; //appUtil.isInterstate(unit, selectedCity);
        var cart = StorageUtils.getCartDetail();
        //var unit = StorageUtils.getUnitDetails();
        var productList = [];
        _.map(cart.orderDetail.orders, (oi) => {
            if (productList.indexOf(oi.productId) < 0) {
                productList.push(oi.productId);
            }
        });
        dispatch(UtilityActions.showFullPageLoader("Redirecting you to customer login page"));
        axios({
            method: "POST",
            url: apis.getUrls().webInventory.unitProducts,
            data: JSON.stringify({id: unit.id, name: unit.name, list: productList}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                dispatch({type: "INVENTORY_UPDATE", payload: response.data});
                dispatch(updateStockInCart(cart, response.data, true, isInterstate, selectedCity));
            } else {
                cart.orderDetail.orders.length > 0 ? dispatch(syncCart(isInterstate, selectedCity)) : null;
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            console.log(error);
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function updateStockInCart(cart, stock, isRejected, isInterstate, selectedCity) {
    return dispatch => {
        if (cart.orderDetail && cart.orderDetail.orders && cart.orderDetail.orders.length > 0) {
            var inventoryShortage = false;
            var shortItems = [];
            var shortStock = [];
            var remove = [];
            _.map(cart.orderDetail.orders, (oi) => {
                if (stock[oi.productId] <= 0) {
                    remove.push(oi);
                } else if (stock[oi.productId] != null) {
                    if (oi.quantity > stock[oi.productId]) {
                        oi.quantity = stock[oi.productId];
                        inventoryShortage = true;
                        shortItems.push(oi.productName);
                        shortStock.push({
                            product: oi.productName,
                            cartQuantity: oi.quantity,
                            shortQuantity: oi.quantity - stock[oi.productId]
                        });
                    }
                }
            });
            _.map(remove, (item) => {
                dispatch(removeItem(cart, item));
            });
            dispatch(updateCart(cart));
            if (remove.length > 0) {
                dispatch(UtilityActions.showPopup("Few items have been removed from your cart as they are not available in stock.", "info", 2000));
            }
            if (inventoryShortage) {
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup(shortItems.join(", ") + " in your cart are not available in requested quantity. So, we have updated their quantity.", "error", 2000));
            } else {
                (cart.orderDetail.orders.length > 0 && isRejected == true) ? dispatch(syncCart(isInterstate, selectedCity)) : null;
            }
            if (shortStock.length > 0 && isRejected == true) {
                trackUtils.trackRejectedStock(shortItems);
            }
        }
    }
}

export function syncCart(isInterState, selectedCity, handleError) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        cart.orderDetail.transactionDetail == null ? cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject() : null;
        if (cart.orderDetail.source == null || typeof cart.orderDetail.source == 'undefined' || cart.orderDetail.source.length == 0) {
            var crit = "TAKE_AWAY";//StorageUtils.getLocalityMetadata().criteria;
            if (crit == null || crit.length == 0) {
                browserHistory.push("/menu");
                dispatch(UtilityActions.showPopup("Something went wrong. Please check cart!", "info"));
            } else {
                cart.orderDetail.source = appUtil.getSourceFromCriteria(crit);
            }
        }
        if (cart.orderDetail.unitId == null || appUtil.checkEmpty(cart.orderDetail.unitName)) {
            cart.orderDetail.unitId = StorageUtils.getKettleAuthDetails().unitDetail.id;
            cart.orderDetail.unitName = StorageUtils.getKettleAuthDetails().unitDetail.name;
        }
        cart.currentStateId = StorageUtils.getKettleAuthDetails().unitDetail.stateCode;
        /*if (selectedCity.state) {
            cart.currentStateId = selectedCity.state;
        }*/
        cart = appUtil.calculateTaxes(cart, isInterState);
        cart.orderDetail.brandId = 1;
        dispatch(updateCart(cart));
        var d = StorageUtils.getLocalityMetadata();
        trackUtils.trackInitiateCheckout({
            criteria: d.criteria,
            city: d.city,
            locality: !appUtil.checkEmpty(d.locality) ? d.locality.label : "",
            outlet: !appUtil.checkEmpty(d.outlet) ? d.outlet.label : "",
            cart: cart
        });
        dispatch(UtilityActions.showFullPageLoader("Syncing your cart. Please wait!"));
        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                dispatch({type: "SYNC_CART_FULFILLED"});
                const criteria = StorageUtils.getLocalityMetadata().criteria;
                dispatch({type: "SET_ADDRESS_VIEW_TYPE", payload: "DELIVERY"});
                /*if (!appUtil.checkEmpty(StorageUtils.getSessionId())) {
                    if (criteria == "DELIVERY") {
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: "/addresses"});
                        browserHistory.push("/addresses");
                    } else {
                        browserHistory.push("/paymentModes");
                    }
                    dispatch(UtilityActions.hideFullPageLoader());
                } else {*/
                /*if (criteria == "DELIVERY") {
                    dispatch({type: "SET_LOGIN_REDIRECT", payload: "/addresses"});
                } else {*/
                dispatch({type: "SET_LOGIN_REDIRECT", payload: "/orderSource"});
                if (StorageUtils.getCustomerDetail() != null) {
                    browserHistory.push("/orderSource");
                    dispatch(UtilityActions.hideFullPageLoader());
                } else {
                    browserHistory.push("/login");
                    dispatch(UtilityActions.hideFullPageLoader());
                }
                /*}*/

                /*}*/
            } else {
                dispatch({type: "SYNC_CART_REJECTED", payload: null});
                dispatch(UtilityActions.showPopup(response.data.msg, "error", 2000));
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            if (handleError != false && error.response != null) {
                dispatch(handleSyncError(error.response.data, isInterState, selectedCity));
            }
        });
    }
}

export function handleSyncError(response, isInterState, selectedCity) {
    return dispatch => {
        if (response.errorCode == 716) { //INVALID_SOURCE
            var cart = StorageUtils.getCartDetail();
            cart.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria);
            dispatch(updateCart(cart));
            dispatch(syncCart(isInterState, selectedCity, false));
        } else if (response.errorCode == 703) { //INVALID_UNIT
            var cart = StorageUtils.getCartDetail();
            cart.orderDetail.unitId = StorageUtils.getKettleAuthDetails().unitDetail.id;
            cart.orderDetail.unitName = StorageUtils.getKettleAuthDetails().unitDetail.name;
            dispatch(updateCart(cart));
            dispatch(syncCart(isInterState, selectedCity, false));
        } else if ([701, 702, 715, 708].indexOf(response.errorCode) >= 0) {
            dispatch(UtilityActions.showPrompt("Error in validating cart. Please try again!", function () {
                window.location.reload();
            }, function () {
                window.location.reload();
            }));
        } else {
            dispatch({type: "SYNC_CART_REJECTED", payload: null});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later or call 1800-120-2424 for support.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        }
    }
}

export function updateDeliveryAddressToCart(addressId) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        cart.orderDetail.deliveryAddress = addressId;
        dispatch(UtilityActions.showFullPageLoader(""));
        dispatch({type: "UPDATE_DELIVERY_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.addAddressToCart,
            data: JSON.stringify({id: addressId, name: cart.cartId}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data) {
                dispatch(updateCart(cart));
                dispatch({type: "UPDATE_DELIVERY_CART_FULFILLED"});
                browserHistory.push("/orderSource");
            } else {
                dispatch({type: "UPDATE_DELIVERY_CART_REJECTED", payload: error});
                dispatch(UtilityActions.showPopup("Error setting address to cart. Try again.", 'error'));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch({type: "UPDATE_DELIVERY_CART_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error setting address to cart. Try again.", 'error'));
        });
    }
}

export function applyCoupon(couponCode, isInterState, canRoute) {
    return dispatch => {
        dispatch({type: "SET_COUPON", payload: couponCode});
        trackUtils.trackCouponTried(couponCode);
        var cartValid = validCartItems();
        if (cartValid.status == "success") {
            var cart = StorageUtils.getCartDetail();
            var unitDetail = StorageUtils.getKettleAuthDetails().unitDetail;
            if (unitDetail != null) {
                cart.orderDetail.unitId = unitDetail.id;
                cart.orderDetail.unitName = unitDetail.name;
            }
            var data;
            if (!appUtil.checkEmpty(StorageUtils.getCustomerDetail())) {
                data = {
                    couponCode: couponCode,
                    order: cart.orderDetail,
                    contact: StorageUtils.getCustomerDetail().contact
                };
            } else {
                data = {couponCode: couponCode, order: cart.orderDetail};
            }
            if (data.order.externalOrderId == null) {
                data.order.externalOrderId = "KO433665635";
            }
            dispatch({type: "SET_COUPON_APPLYING", payload: true});
            //dispatch(UtilityActions.showFullPageLoader(""));
            axios({
                method: "POST",
                url: apis.getUrls().offer.apply,
                data: JSON.stringify(data),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response.status == 200 && !appUtil.checkEmpty(response.data)) {
                    if (response.data.error == false) {
                        _.map(cart.orderDetail.orders, (orderItem) => {
                            _.map(response.data.order.orders, (oi) => {
                                if (orderItem.itemId == oi.itemId) {
                                    orderItem.discountDetail = oi.discountDetail;
                                }
                            });
                        });
                        cart.orderDetail.offerCode = response.data.couponCode;
                        cart.orderDetail.transactionDetail = response.data.order.transactionDetail;
                        cart = appUtil.calculateTaxes(cart, isInterState);
                        dispatch({type: "OFFER_APPLIED", payload: cart});
                        dispatch(UtilityActions.showPopup("Offer applied.", "info"));
                        dispatch(OutletMenuActions.setShowCouponModal(false));
                        dispatch({type: "SYNC_CART_PENDING"});
                        axios({
                            method: "POST",
                            url: apis.getUrls().cart.sync,
                            data: JSON.stringify(cart),
                            headers: {'Content-Type': 'application/json'}
                        }).then(function (response) {
                            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                                dispatch({type: "UPDATE_CART", payload: cart});
                                StorageUtils.setCartDetail(cart);
                                if(canRoute) {
                                    browserHistory.push("/orderSource");
                                }
                            }
                        }).catch(function (error) {
                            dispatch({type: "SYNC_CART_REJECTED", payload: error});
                            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
                            dispatch(UtilityActions.hideFullPageLoader());
                        });

                        try {
                            trackUtils.trackOfferSuccess({
                                coupon: cart.orderDetail.offerCode,
                                discount: cart.orderDetail.transactionDetail.totalDiscount
                            });
                        } catch (e) {
                        }
                    } else {
                        if (response.data.errorCode == 101) {
                            dispatch({type: "SET_COUPON_LOGIN", payload: true});
                            dispatch({type: "SET_LOOKUP_CUSTOMER_PENDING", payload: false});
                            dispatch({type: "SET_LOGIN_REJECTED"});
                            dispatch({type: "SET_SIGNUP_REJECTED"});
                            dispatch({
                                type: "OFFER_ERROR",
                                payload: {code: response.data.errorCode, message: response.data.errorMessage}
                            });
                        } else {
                            dispatch({
                                type: "OFFER_ERROR",
                                payload: {code: response.data.errorCode, message: response.data.errorMessage}
                            });
                            dispatch({type: "SET_COUPON_LOGIN", payload: false});
                            //dispatch(UtilityActions.showPopup("Offer not applicable see error message.", "info"));
                            try {
                                trackUtils.trackOfferFailed({
                                    coupon: couponCode,
                                    errorCode: response.data.errorCode,
                                    reason: response.data.errorMessage
                                });
                            } catch (e) {
                            }
                        }
                    }
                }
                dispatch({type: "SET_COUPON_APPLYING", payload: false});
                //dispatch(UtilityActions.hideFullPageLoader());
            }).catch(function (error) {
                //dispatch(UtilityActions.hideFullPageLoader());
                console.log(error);
                dispatch({
                    type: "OFFER_ERROR",
                    payload: {code: 0, message: "Unable to apply offer."}
                });
                dispatch({type: "SET_COUPON_LOGIN", payload: false});
                //dispatch(UtilityActions.showPopup("Offer not applicable.", 'error'));
                dispatch({type: "SET_COUPON_APPLYING", payload: false});
            });
        } else {
            dispatch(UtilityActions.showPopup(cartValid.msg, 'error'));
        }
    }
}

const validCartItems = () => {
    const cart = StorageUtils.getCartDetail();
    var ret = null, set = false;
    if (cart != null && cart.orderDetail != null && cart.orderDetail.orders.length > 0) {
        cart.orderDetail.orders.map((oi) => {
            if (oi.billType == "ZERO_TAX") {
                ret = {status: "fail", msg: "Offer not application on following item: " + oi.productName};
                set = true;
            }
        });
        if (!set) {
            ret = {status: "success", msg: ""}
        }
    } else {
        if (!set) {
            ret = {status: "fail", msg: "Cart is not available. Please reload page."}
        }
    }
    return ret;
};

export function removeCoupon(isInterState) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var discountObj = appUtil.getEmptyDiscountObj();
        if(!appUtil.checkOrderDetailsEmpty(cart) && cart.orderDetail.orders!=null){
            cart.orderDetail.orders.map((item) => {
                item.discountDetail = discountObj;
            });
            if (appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
                cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
            }
            cart.orderDetail.transactionDetail.discountDetail = discountObj;
            cart.orderDetail.transactionDetail.discountDetail.wasValueSet = false;
            cart.orderDetail.offerCode = null;
            cart = appUtil.calculateTaxes(cart, isInterState);
            dispatch(updateCart(cart));
            dispatch(OutletMenuActions.setShowCouponModal(false));
        }
    }
}

export function clearCart() {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        /*if(cart!=null){
            cart = null;
            dispatch(updateCart(cart));
        }*/
        if(cart!=null){
            var discountObj = appUtil.getEmptyDiscountObj();
            cart.orderDetail.orders = [];
            if (appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
                cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
            }
            cart.orderDetail.transactionDetail.discountDetail = discountObj;
            cart.orderDetail.transactionDetail.discountDetail.wasValueSet = false;
            cart.orderDetail.offerCode = null;
            cart = appUtil.calculateTaxes(cart, false);
            dispatch(updateCart(cart));
        }
    }
}

export function setEditItemId(id) {
    return dispatch => {
        dispatch({type: "SET_EDIT_ITEM_ID", payload: id});
    }
}

export function editCartItem(cartObj, itemId, skipDeliveryPackaging, isInterState) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var variants = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.variants.map((variant) => {
            variant.details.map((detail) => {
                if (detail.active) {
                    variants.push(detail);
                }
            })
        });
        var ingredientProducts = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.products.map((product) => {
            product.details.map((detail) => {
                if (detail.active) {
                    ingredientProducts.push(detail);
                }
            })
        });
        var menuProducts = [];
        if (cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct != null) {
            cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.cartProduct != null) {
                    menuProducts.push(detail.cartProduct);
                }
            })
        }
        var cartItem = {
            itemId: itemId,
            productId: cartObj.cartItem.id,
            productName: appUtil.getProductName(cartObj.cartItem.id, cartObj.cartItem.name),
            customizationStrategy: cartObj.customizationStrategy,
            quantity: cartObj.quantity,
            price: cartObj.cartItem.selectedDimension.price,
            totalAmount: null,
            amount: cartObj.quantity * cartObj.cartItem.selectedDimension.price,
            discountDetail: {
                discountCode: null,
                discountReason: null,
                promotionalOffer: 0,
                discount: {
                    percentage: 0,
                    value: 0
                },
                totalDiscount: 0
            },
            addons: [],
            dimension: cartObj.cartItem.selectedDimension.dimension,
            billType: cartObj.cartItem.billType,
            isCombo: cartObj.cartItem.type == 8,
            composition: {
                variants: variants,
                products: ingredientProducts,
                menuProducts: menuProducts,
                addons: cartObj.addons
            },
            recipeId: cartObj.cartItem.selectedDimension.recipe.recipeId,
            code: cartObj.cartItem.taxCode,
            taxes: [],
            tax: 0,
            originalTax: 0
        };
        var orders = [];
        cart.orderDetail.orders.map((orderItem) => {
            if (orderItem.itemId == itemId) {
                orders.push(cartItem);
            } else {
                orders.push(orderItem);
            }
        });
        cart.orderDetail.orders = orders;
        if (!cart.orderDetail.source == "COD") {
            dispatch(removeDeliveryAndPackagingFromCart(cart));
        } else {
            dispatch(addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
        }
        if (!appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
            cart = appUtil.calculateTaxes(cart, isInterState);
        }
        dispatch(updateCart(cart));
    }
}

export function removeOfferInitCart(val) {
    return dispatch => {
        dispatch({type: "OFFER_REMOVED_IN_INIT_CART", payload: val});
    }
}

export function setCartStock(unitInventory) {
    return dispatch => {
        if (unitInventory != null) {
            var cartInventory = [];
            var order = !appUtil.checkEmpty(StorageUtils.getCartDetail()) ? StorageUtils.getCartDetail().orderDetail : null;
            if (order != null) {
                _.map(order.orders, (oi) => {
                    if (unitInventory[oi.productId] != null) {
                        cartInventory[oi.productId] = (cartInventory[oi.productId] != null ? cartInventory[oi.productId] - oi.quantity : unitInventory[oi.productId] - oi.quantity);
                    }
                })
            }
            dispatch({type: "SET_CART_INVENTORY", payload: cartInventory});
        }
    }
}

export function updateCartStock(stock) {
    return dispatch => {
        dispatch({type: "SET_CART_INVENTORY", payload: stock});
    }
}

export function syncCartAndLogin() {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        if (cart.orderDetail.source == null || typeof cart.orderDetail.source == 'undefined' || cart.orderDetail.source.length == 0) {
            var crit = "TAKE_AWAY";//StorageUtils.getLocalityMetadata().criteria;
            if (crit == null || crit.length == 0) {
                browserHistory.push("/menu");
                dispatch(UtilityActions.showPopup("Something went wrong. Please check cart!", "info"));
            } else {
                cart.orderDetail.source = appUtil.getSourceFromCriteria(crit);
            }
        }
        if (cart.orderDetail.unitId == null || appUtil.checkEmpty(cart.orderDetail.unitName)) {
            cart.orderDetail.unitId = StorageUtils.getUnitDetails().id;
            cart.orderDetail.unitName = StorageUtils.getUnitDetails().name;
        }
        dispatch(updateCart(cart));
        trackUtils.trackInitiateCheckout(cart);
        dispatch(UtilityActions.showFullPageLoader("Syncing your cart. Please wait!"));
        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                dispatch({type: "SYNC_CART_FULFILLED"});
                const criteria = StorageUtils.getLocalityMetadata().criteria;
                dispatch({type: "SET_ADDRESS_VIEW_TYPE", payload: "DELIVERY"});
                browserHistory.push("/login");
                dispatch(UtilityActions.hideFullPageLoader());
            } else {
                dispatch({type: "SYNC_CART_REJECTED", payload: null});
                dispatch(UtilityActions.showPopup(response.data.msg, "error", 2000));
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            dispatch({type: "SYNC_CART_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function updateCart(cart) {
    return dispatch => {
        dispatch({type: "UPDATE_CART", payload: {...cart}});
        StorageUtils.setCartDetail(cart);
    }
}

export function setShowOrderSourceModal(show) {
    return dispatch => {
        dispatch({type: "SET_SHOW_ORDER_SOURCE_MODAL", payload: show});
    }
}


export function checkRedeemChaiInCart(props) {
    return dispatch => {
        let count = 0;
        if (props.cart && props.cart.orderDetail && props.cart.orderDetail.orders.length > 0) {
            props.cart.orderDetail.orders.forEach((orderItem) => {
                if (StorageUtils.getRedemptionChaiProductIds().indexOf(orderItem.productId) != -1 && orderItem.dimension == "Regular") {
                    count = count + orderItem.quantity;
                }
            });
            return count;
        }
    }
}

export function setOrderSource(source, props) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        cart.orderDetail.source = source;
        dispatch(updateCart(cart));
        props.dispatch({type: "SET_SELECTED_ORDER_SOURCE", payload: source});
        if(source == "CAFE") {
            dispatch(UtilityActions.showFullPageLoader("Placing your dine in order."));
        } else {
            dispatch(UtilityActions.showFullPageLoader("Placing your take away order."));
        }
        axios({
            method: "POST",
            url: apis.getUrls().cart.setOrderSource,
            data: JSON.stringify({cartId: cart.cartId, orderDetail: {source: source}}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch(UtilityActions.hideFullPageLoader());
            if (response.data == true) {
                console.log('cart ', props.cart);
                if (props.cart.orderDetail.transactionDetail == null || props.cart.orderDetail.transactionDetail.paidAmount < 1) {
                    if (props.customer.chaiRedeemed > 0 || props.cart.orderDetail.offerCode) {
                        // checkout order by cash and then navigate to order success page
                        dispatch(UtilityActions.showFullPageLoader("We are placing your order. Please Wait..."));
                        dispatch(OrderManagementActions.orderCheckout(1, null));
                    }
                } else {
                    if (props.cart.orderDetail.offerCode || props.customer.redemptionDone || (props.customer.loyalty < 60 || dispatch(checkRedeemChaiInCart(props)) == 0)) {
                        // dispatch(setShowOrderSourceModal(false));
                        browserHistory.push("/paymentModes");
                    }
                }
            } else {
                dispatch(UtilityActions.showPopup("Error in setting eating location preference. Please try again.", "error", 2000))
            }
        }).catch(function (error) {
            console.log(error);
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error in setting eating location preference. Please try again.", "error", 2000));
        });
    }
}