import appUtil from "../AppUtil";
import apis from "../APIs";
import axios from "axios";
import { browserHistory } from "react-router";
import trackUtils from "../utils/TrackUtils";
import StorageUtils from "../utils/StorageUtils";
import * as OutletMenuActions from "./OutletMenuActions";
import * as LocalityActions from "./LocalityActions";

export function initCampaign(props, campaignInitialized) {
    return dispatch =>{
        if(campaignInitialized!=true){
            if(props.location.query.ct!=null){
                var campaignDetail = {};
                campaignDetail.cid=props.location.query.cid; //campaign id
                campaignDetail.cp=props.location.query.cp;   //coupon
                campaignDetail.ct=props.location.query.ct;   //city
                campaignDetail.pid=props.location.query.pid; //product id
                campaignDetail.ac=props.location.query.ac;   //action
                if(campaignDetail.cid!=null){
                    StorageUtils.setCampaignDetails(campaignDetail);
                }
                dispatch(LocalityActions.setCriteria("DELIVERY"));
                dispatch(LocalityActions.setCity(campaignDetail.ct,null));
                var data = StorageUtils.getLocalityMetadata();
                if(data==null){
                    data = {};
                }
                data.city = campaignDetail.ct;
                StorageUtils.setLocalityMetadata(data);
                dispatch(setCampaignDetail(campaignDetail));
                dispatch(setCampaignCookie(StorageUtils.getCampaignDetails()));
                axios({
                    method: "POST",
                    url: apis.getUrls().neoCache.getLocalityData,
                    data: JSON.stringify(campaignDetail.ct),
                    headers: {'Content-Type': 'application/json'}
                }).then(function (response) {
                    if(response.data!=null){
                        var locality = {value:0,label:response.data};
                        dispatch(LocalityActions.selectLocality(locality));
                        dispatch(OutletMenuActions.getUnitProducts("DELIVERY", {city:campaignDetail.ct}, locality, null));
                    }else{
                        StorageUtils.removeCampaignDetails();
                        window.location.href = window.location.origin+"/menu";
                    }
                }).catch(function (error) {
                    console.log(error);
                    StorageUtils.removeCampaignDetails();
                    window.location.href = window.location.origin+"/menu";
                });
            }else{
                browserHistory.push("/menu");
            }
        }
    }
}

export function setCampaignDetail(payload) {
    return dispatch => {
        dispatch({type:"SET_CAMPAIGN_DETAIL", payload:payload});
    }
}

export function setCampaignCookie(payload) {
    return dispatch => {
        dispatch({type:"SET_CAMPAIGN_COOKIE", payload:payload});
    }
}