/**
 * Created by Chaayos on 01-12-2016.
 */
import StorageUtils from "../utils/StorageUtils";

export default function reducer(state = {
    selectedUnit:null,
    userId:null,
    password:null,
    unitList: null,
    unitListLoading: false,
    kettleAuthDetails:null,
    autoConfig: false,
    terminalList: null,
    selectedTerminal:null
}, action) {

    switch (action.type) {
        case "SET_USER_ID":
        {
            return {...state, userId:action.payload};
            break;
        }
        case "SET_SELECTED_UNIT":
        {
            return {...state, selectedUnit: action.payload};
            break;
        }
        case "SET_PASSWORD":
        {
            return {...state, password: action.payload};
            break;
        }
        case "SET_UNIT_LIST":
        {
            return {...state, unitList: action.payload};
            break;
        }
        case "SET_UNIT_LIST_LOADING":
        {
            return {...state, unitListLoading: action.payload};
            break;
        }
        case "SET_KETTLE_AUTH_DETAILS":
        {
            return {...state, kettleAuthDetails: action.payload};
            break;
        }
        case "SET_AUTO_CONFIG":
        {
            return {...state, autoConfig: action.payload};
            break;
        }
        case "SET_TERMINAL_LIST":
        {
            return {...state, terminalList: action.payload};
            break;
        }
        case "SET_SELECTED_TERMINAL":
        {
            return {...state, selectedTerminal: action.payload};
            break;
        }
    }

    return state;

}