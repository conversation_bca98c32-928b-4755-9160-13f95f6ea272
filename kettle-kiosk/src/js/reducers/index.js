import { combineReducers } from "redux";
import localityReducer from "./LocalityReducer";
import outletMenuReducer from "./OutletMenuReducer";
import customizationModalReducer from "./CustomizationModalReducer";
import utilityReducer from "./UtilityReducer";
import cartManagementReducer from "./CartManagementReducer";
import customerReducer from "./CustomerReducer";
import orderManagementReducer from "./OrderManagementReducer";
import sidebarReducer from "./SidebarReducer";
import paymentReducer from "./PaymentReducer";
import campaignReducer from "./CampaignReducer";
import promoOfferReducer from "./PromoOfferReducer";
import outletLoginReducer from "./OutletLoginReducer";

export default combineReducers({
    localityReducer,
    outletMenuReducer,
    customizationModalReducer,
    utilityReducer,
    cartManagementReducer,
    customerReducer,
    orderManagementReducer,
    sidebarReducer,
    paymentReducer,
    campaignReducer,
    promoOfferReducer,
    outletLoginReducer
})