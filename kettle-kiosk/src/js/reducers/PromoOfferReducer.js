export default function reducer(state = {
    showPromoOffer:false,
    promoOfferCode:"WELCOME",
    customerAccepted:false,
    isLoginPromo:false,
    promoOfferImage:"promo.jpg",
    promoOfferHeadline:"Use WELCOME to get 10% off",
    promoOfferText:'Welcome to Chai Lovers club, begin your relationship with "Meri Wali Chai" & Great Food.' +
    'Get 10% off on your first order.',
}, action) {

    switch (action.type) {
        case "SET_CUSTOMER_ACCEPTED_PROMO":
        {
            return {...state, customerAccepted: action.payload};
            break;
        }
        case "SET_SHOW_PROMO":
        {
            return {...state, showPromoOffer: action.payload};
            break;
        }
    }

    return state;

}