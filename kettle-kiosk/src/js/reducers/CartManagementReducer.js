import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";

export default function reducer(state = {
    cart: null,
    syncCart: true,
    offerErrorMap: {
        101: "Customer Login Required",
        102: "Missing Required Product",
        103: "Not Available at Unit",
        104: "Not Available in Region",
        105: "Not Available for current order source",
        106: "Not Available",
        107: "Coupon Not Found",
        108: "Expired",
        109: "Insufficient Data"
    },
    offerErrorCode:null,
    offerErrorMessage:null,
    offerApplied:false,
    editItemId: null,
    offerRemovedInitCart:false,
    cartInventory:null,
    couponApplying:false,
    showOrderSourceModal:false,
    giftCardCart: null,
    beforeRedemptionCart: null,
    setStartNewOrderTimeout: null
}, action) {

    switch (action.type) {
        case "UPDATE_CART":
        {
            if (action.payload != null) {
                var cart = Object.assign({}, action.payload);
                return {...state, cart: cart, offerApplied:false, offerErrorMessage:null, offerErrorCode:null};
            } else {
                return {...state, cart: null, offerApplied:false, offerErrorMessage:null, offerErrorCode:null};
            }
            break;
        }
        case "BEFORE_REDEMPTION_CART":
        {
            if (action.payload != null) {
                var cart = Object.assign({}, action.payload);
                return {...state, beforeRedemptionCart: cart, offerApplied:false, offerErrorMessage:null, offerErrorCode:null};
            } else {
                return {...state, beforeRedemptionCart: null, offerApplied:false, offerErrorMessage:null, offerErrorCode:null};
            }
            break;
        }
        case "CREATE_CART":
        {
            return {...state, cart: action.payload};
            break;
        }
        case "GIFT_CARD_CART":
        {
            return {...state, giftCardCart: action.payload};
            break;
        }
        case "ADD_ITEM_TO_CART":
        {
            /*var cart = Object.assign({}, state.cart);
            cart.orderDetail.orders.push(action.payload);
            StorageUtils.setCartDetail(cart);
            return {...state, cart: cart};*/
            break;
        }
        case "REMOVE_ITEM_FROM_CART":
        {
            /*var cart = Object.assign({}, state.cart);
            var items = [];
            var index = 1;
            cart.orderDetail.orders.map((item) => {
                if (item.itemId != action.payload) {
                    item.itemId = index++;
                    items.push(item);
                }
            });
            cart.orderDetail.orders = items;
            if (!appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
                cart = appUtil.calculateTaxes(cart);
            }
            StorageUtils.setCartDetail(cart);
            return {...state, cart: cart};*/
            break;
        }
        case "CHECKOUT_CART_PENDING":
        {
            //return {...state, state};
            break;
        }
        case "CHECKOUT_CART_FULFILLED":
        {
            //return {...state, state};
            break;
        }
        case "CHECKOUT_CART_REJECTED":
        {
            //return {...state, state};
            break;
        }
        case "SET_SYNC_CART":
        {
            return {...state, syncCart: action.payload};
            break;
        }
        case "SYNC_CART_PENDING":
        {
            break;
        }
        case "SYNC_CART_FULFILLED":
        {
            return {...state, syncCart: false};
            break;
        }
        case "SYNC_CART_REJECTED":
        {
            break;
        }
        case "OFFER_APPLIED":
        {
            var cart  = Object.assign({}, action.payload);
            return {...state, offerApplied: true, cart:cart, offerErrorCode:null};
            break;
        }
        case "OFFER_ERROR":
        {
            return {...state, offerApplied: false, offerErrorCode:action.payload.code, offerErrorMessage:action.payload.message};
            break;
        }
        case "SET_EDIT_ITEM_ID":
        {
            return {...state, editItemId:action.payload};
            break;
        }
        case "OFFER_REMOVED_IN_INIT_CART":
        {
            return {...state, offerRemovedInitCart:action.payload};
            break;
        }
        case "SET_CART_INVENTORY":
        {
            return {...state, cartInventory:action.payload}
        }
        case "SET_COUPON_APPLYING":
        {
            return {...state, couponApplying:action.payload}
        }
        case "SET_SHOW_ORDER_SOURCE_MODAL":
        {
            return {...state, showOrderSourceModal:action.payload}
        }
        case "SET_START_NEW_ORDER_TIMEOUT":
        {
            return {...state, setStartNewOrderTimeout:action.payload}
        }
    }

    return state;

}