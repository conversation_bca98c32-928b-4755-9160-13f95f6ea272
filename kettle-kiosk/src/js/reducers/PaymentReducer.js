/**
 * Created by Chaayos on 18-02-2017.
 */
export default function reducer(state = {
    paymentInitiated:false,
    paymentMessage: "",
    paymentStatus:"CREATED",
    showLoader: false,
    failureMessage:null,
    showPayTmQRSection: false,
    payTMQRCodeId: null,
    paytmPaymentSuccess: false,
    showEzetapModal: false,
    showEzetapLoader:false,
    ezetapStatusMessage:null,
    ezetapStatusCode:null,
    ezetapInterval:null,
    paymentLoader: false,
    showPaytmQRModal:false,
    paytmQRInterval:null,
    paytmQRStatus:null,
    showCashModal: false,
    isCashAmountExceeded: false,
    cashPaymentOTPErrorMessage: null,
    cashPaymentOTPSuccessMessage: null,
    cashPaymentOTPMessage: null,
    cashOrderLoader: false,
    selectivePaymentMode:"CASH",
    hasAvailRedeemChaayos: false,
    showGiftCardPurchaseModal: false,
    showGiftCardPaymentModal: false,
    allGiftCards: null,
    showPaytmQRModalForGiftCard: false,
    showEzetapModalForGiftCard: false,
    loadingMessage: null,
    showOtp: false,
    giftCardOffer: null,
    paytmQRTimeout: null,
    showAGSModal: false,
    showAGSLoader:false,
    agsStatusCode:null,
    agsStatusTitle:null,
    agsStatusMessage:null,
    agsInterval:null,
    cardPaymentMode:"AGS",
    timerDataMin: 1,
    timerDataSec: 0,
    secRemaining: 0,
    timerInterval: null,
    stopTimer: true,
    showPayModesForGiftCard: false,
    giftCardCartDetails: null,
    agsAmountToPay: null
}, action) {

    switch (action.type) {
        case "SET_PAYMENT_DATA":
        {
            return {...state, paymentMessage: action.payload.paymentMessage,
                paymentStatus: action.payload.paymentStatus,
                showLoader: action.payload.showLoader,
                failureMessage: action.payload.failureMessage,
            };
            break;
        }
        case "SET_PAYMENT_INITIATED":
        {
            return {...state, paymentInitiated: action.payload};
            break;
        }
        case "SET_SHOW_PAYTM_QR_SECTION":
        {
            return {...state, showPayTmQRSection: action.payload};
            break;
        }
        case "SET_PAYTM_QR_CODE_ID":
        {
            return {...state, payTMQRCodeId: action.payload};
            break;
        }
        case "SET_PAYTM_PAYMENT_SUCCESS":
        {
            return {...state, paytmPaymentSuccess: action.payload};
        }
        case "SET_PAYMENT_LOADER":
        {
            return {...state, paymentLoader: action.payload};
        }
        case "SET_SHOW_EZETAP_MODAL":
        {
            return {...state, showEzetapModal: action.payload};
        }
        case "SET_SHOW_EZETAP_LOADER":
        {
            return {...state, showEzetapLoader: action.payload};
        }
        case "SET_EZETAP_STATUS_MESSAGE":
        {
            return {...state, ezetapStatusMessage: action.payload};
        }
        case "SET_EZETAP_STATUS_CODE":
        {
            return {...state, ezetapStatusCode: action.payload};
        }
        case "SET_EZETAP_INTERVAL":
        {
            return {...state, ezetapInterval: action.payload};
        }
        case "SET_SHOW_PAYTM_QR_MODAL":
        {
            return {...state, showPaytmQRModal: action.payload};
        }
        case "SET_SHOW_GIFT_CARD_PURCHASE_MODAL":
        {
            return {...state, showGiftCardPurchaseModal: action.payload};
        }
        case "SET_SHOW_GIFT_CARD_PAYMENT_MODAL":
        {
            return {...state, showGiftCardPaymentModal: action.payload};
        }
        case "SET_PAYTM_QR_INTERVAL":
        {
            return {...state, paytmQRInterval: action.payload};
        }
        case "SET_PAYTM_QR_TIMEOUT":
        {
            return {...state, paytmQRTimeout: action.payload};
        }
        case "SET_PAYTM_QR_STATUS":
        {
            return {...state, paytmQRStatus: action.payload};
        }
        case "SET_SHOW_CASH_MODAL":
        {
            return {...state, showCashModal: action.payload};
        }
        case "SET_CASH_AMOUNT_EXCEEDED":
        {
            return {...state, isCashAmountExceeded: action.payload};
        }
        case "SET_CASH_AMOUNT_OTP_NEEDED":
        {
            return {...state, isCashAmountOTPNeeded: action.payload};
        }
        case "SET_CASH_OTP_SUCCESS_RESPONSE_MESSAGE":
        {
            return {...state, cashPaymentOTPSuccessMessage: action.payload};
        }
        case "SET_CASH_OTP_ERROR_RESPONSE_MESSAGE":
        {
            return {...state, cashPaymentOTPErrorMessage: action.payload};
        }
        case "SET_CASH_ORDER_LOADER": {
            return {...state, cashOrderLoader: action.payload};
        }
        case "SET_SELECTIVE_PAYMENT_MODE":
        {
            return {...state, selectivePaymentMode: action.payload};
        }
        case "SET_CARD_PAYMENT_MODE":
        {
            return {...state, cardPaymentMode: action.payload};
        }
        case "AVAIL_REDEEM_CHAAYOS":
        {
            return {...state, hasAvailRedeemChaayos: action.payload};
        }
        case "LOAD_GIFT_CARDS":
        {
            return {...state, allGiftCards: action.payload};
        }
        case "SET_SHOW_PAYTM_QR_MODAL_FOR_GIFT_CARD":
        {
            return {...state, showPaytmQRModalForGiftCard: action.payload};
        }
        case "SET_SHOW_EZETAP_MODAL_FOR_GIFT_CARD":
        {
            return {...state, showEzetapModalForGiftCard: action.payload};
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
        case "SET_SHOW_CC_OTP": {
            return {...state, showOtp: action.payload};
            break;
        }
        case "SET_GIFT_CARD_OFFER": {
            return {...state, giftCardOffer: action.payload};
            break;
        }
        case "SET_SHOW_AGS_MODAL":
        {
            return {...state, showAGSModal: action.payload};
        }
        case "SET_SHOW_AGS_LOADER":
        {
            return {...state, showAGSLoader: action.payload};
        }
        case "SET_AGS_STATUS":
        {
            return {...state,
                agsStatusCode: action.payload.code,
                agsStatusTitle: action.payload.title,
                agsStatusMessage: action.payload.msg};
        }
        case "SET_AGS_INTERVAL":
        {
            return {...state, agsInterval: action.payload};
        }
        case "SET_TIMER_MIN":{
            return {...state, timerDataMin: action.payload};}
        case "SET_TIMER_SEC":{
            return {...state,timerDataSec: action.payload};
        }
        case "SET_SEC_REMAINING":{
            return {...state, secRemaining: action.payload};
        }
        case "SET_TIMER_INTERVAL":{
            return {...state, timerInterval: action.payload};
        }
        case "SET_STOP_TIMER":{
            return {...state, stopTimer: action.payload};
        }
        case "SET_SHOW_PAYMODES_FOR_GIFT_CARD":{
            return {...state, showPayModesForGiftCard: action.payload};
        }
        case "SET_GIFT_CARD_CART_DETAILS":{
            return {...state, giftCardCartDetails: action.payload};
        }
        case "SET_AGS_AMOUNT_TO_PAY":{
            return {...state, agsAmountToPay: action.payload};
        }


    }

    return state;

}