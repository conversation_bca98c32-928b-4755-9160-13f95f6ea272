export default function reducer(state = {
    campaignDetail:null,
    campaignCookie:null,
    campaignInitialized:null
}, action) {

    switch (action.type) {
        case "SET_CAMPAIGN_DETAIL":
        {
            return {...state, campaignDetail: action.payload, campaignInitialized:true};
            break;
        }
        case "SET_CAMPAIGN_COOKIE":
        {
            return {...state, campaignCookie: action.payload};
            break;
        }
    }

    return state;

}