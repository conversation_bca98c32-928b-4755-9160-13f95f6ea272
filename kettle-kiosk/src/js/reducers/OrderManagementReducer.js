export default function reducer(state = {
    status:null,
    currentOrderId:null,
    orderDetail:null,
    orderList:null,
    error:null,
    page:1,
    showMore: true,
    orderStatusTimeouts:[],
    orderFailed: false,
    woId:null
}, action) {

    switch (action.type) {
        case "RESET_PAGE":
        {
            return{...state, page:1, orderList:null, showMore:true, error:null, status:null}
            break;
        }
        case "HIDE_SHOW_MORE":
        {
            return{...state, showMore:false}
            break;
        }
        case "GET_ORDER_SUMMARY_PENDING":
        {
            return{...state, status:"PENDING"}
            break;
        }
        case "GET_ORDER_SUMMARY_FULFILLED":
        {
            return {...state, orderDetail: action.payload.data, status: "FULFILLED"};
            break;
        }
        case "GET_ORDER_SUMMARY_REJECTED":
        {
            return {...state, orderDetail: null, status: "REJECTED", error:action.payload};
            break;
        }
        case "GET_CUSTOMER_ORDERS_PENDING":
        {
            return{...state, status:"PENDING"}
            break;
        }
        case "GET_CUSTOMER_ORDERS_FULFILLED":
        {
            var showMore = true;
            var orderList;
            if(action.payload.data.orders.length<5){
                showMore = false;
            }
            if(state.orderList==null){
                orderList = action.payload.data;
            }else{
                orderList = Object.assign({}, state.orderList);
                orderList.orders = orderList.orders.concat(action.payload.data.orders);
            }
            return {...state, orderList: orderList, status: "FULFILLED", page:state.page+1, showMore:showMore};
            break;
        }
        case "GET_CUSTOMER_ORDERS_REJECTED":
        {
            return {...state, status: "REJECTED", error:action.payload};
            break;
        }
        case "SET_CURRENT_ORDER_ID":
        {
            return {...state, currentOrderId:action.payload};
            break;
        }
        case "ORDER_STATUS_UPDATE":
        {
            var order = Object.assign({}, state.orderDetail);
            order.status = action.payload;
            return {...state, orderDetail:order};
            break;
        }
        case "SET_ORDER_STATUS_TIMEOUTS":
        {
            return {...state, orderStatusTimeouts:action.payload};
            break;
        }
        case "SET_ORDER_FAILED":
        {
            return {...state, orderFailed:action.payload};
            break;
        }
        case "SET_WO_ID":
        {
            return {...state, woId:action.payload};
            break;
        }
    }

    return state;

}