/**
 * Created by Chaayos on 01-12-2016.
 */
import StorageUtils from "../utils/StorageUtils";

export default function reducer(state = {
    customer: null,
    showContactSection: true,
    isLogin:true,
    getName: false,
    getEmail:false,
    redirectTo: null,
    sessionKey: null,
    deviceKey:null,
    addresses: [],
    selectedAddress: null,
    status:null,
    showCaptcha:false,
    addressViewType:"PROFILE",
    customerLoyalteaScore:null,
    loyalteaPending:true,
    otpResendSeconds:0,
    signupCustomerPending:false,
    loginCustomerPending:false,
    lookupCustomerPending:false,
    errorSignUpMessage:null,
    setRedemptionDone: false,
    couponCode: null
    //trueCallerQRString:null
}, action) {

    switch (action.type) {
        case "RESET_LOGIN":
        {
            return {...state, showContactSection:true, isLogin:true, getName: false, getEmail:false, errorSignUpMessage:null};
            break;
        }
        case "SET_LOGIN_REDIRECT":
        {
            return {...state, redirectTo: action.payload};
            break;
        }
        case "LOOKUP_CUSTOMER_PENDING":
        {
            var customer = Object.assign({}, state.customer);
            customer.contact = action.payload;
            return {...state, customer: customer};
            break;
        }
        case "LOOKUP_CUSTOMER_FULFILLED":
        {
            //as they say, there is always a better way to do this
            if(action.payload==-2){
                return {...state, getName: false, getEmail:false, isLogin:false, showContactSection: true}
            }
            if(action.payload==-1){
                return {...state, getName: true, getEmail:true, isLogin:false, showContactSection: false}
            }
            if(action.payload==1){
                return {...state, getName: false, getEmail:false, isLogin:true, showContactSection: false}
            }
            if(action.payload==2){
                return {...state, getName: true, getEmail:false, isLogin:true, showContactSection: false}
            }
            if(action.payload==3){
                return {...state, getName: false, getEmail:true, isLogin:true, showContactSection: false}
            }
            if(action.payload==4){
                return {...state, getName: true, getEmail:true, isLogin:true, showContactSection: false}
            }
            if(action.payload==5){
                return {...state, getName: false, getEmail:false, isLogin:false, showContactSection: false}
            }
            if(action.payload==6){
                return {...state, getName: true, getEmail:false, isLogin:false, showContactSection: false}
            }
            if(action.payload==7){
                return {...state, getName: false, getEmail:true, isLogin:false, showContactSection: false}
            }
            if(action.payload==8){
                return {...state, getName: true, getEmail:true, isLogin:false, showContactSection: false}
            }
            break;
        }
        case "LOOKUP_CUSTOMER_REJECTED":
        {
            break;
        }
        case "SET_LOOKUP_CUSTOMER_PENDING":
        {
            return {...state, lookupCustomerPending: action.payload};
            break;
        }
        case "LOGIN_CUSTOMER_PENDING":
        {
            return {...state, loginCustomerPending: true};
            break;
        }
        case "LOGIN_CUSTOMER_FULFILLED":
        {
            return {...state, sessionKey: action.payload, loginCustomerPending: false};
            break;
        }
        case "LOGIN_CUSTOMER_REJECTED":
        {
            return {...state, loginCustomerPending: false};
            break;
        }
        case "SIGNUP_CUSTOMER_PENDING":
        {
            return {...state, signupCustomerPending: true};
            break;
        }
        case "SIGNUP_CUSTOMER_FULFILLED":
        {
            return {...state, sessionKey: action.payload, signupCustomerPending:false};
            break;
        }
        case "SIGNUP_CUSTOMER_REJECTED":
        {
            return {...state, signupCustomerPending:false};
            break;
        }
        case "CUSTOMER_ADDRESSES_PENDING":
        {
            return {...state, status:"PENDING"};
            break;
        }
        case "CUSTOMER_ADDRESSES_FULFILLED":
        {
            return {...state, addresses: action.payload, status:"FULFILLED"};
            break;
        }
        case "CUSTOMER_ADDRESSES_REJECTED":
        {
            return {...state, status:"REJECTED"};
            break;
        }
        case "SELECT_ADDRESS":
        {
            var selectedAddress;
            state.addresses.map((address) => {
                if (address.id == action.payload) {
                    selectedAddress = address;
                }
            });
            return {...state, selectedAddress: selectedAddress}
            break;
        }
        case "ADD_ADDRESS":
        {
            var selectedAddress = action.payload;
            var addresses = Object.assign([], state.addresses);
            addresses.push(action.payload);

            return {...state, addresses:addresses, selectedAddress: selectedAddress}
            break;
        }
        case "REMOVE_SELECTED_ADDRESS":
        {
            return {...state, selectedAddress: null}
            break;
        }
        case "SET_CUSTOMER":
        {
            return {...state, customer: action.payload}
            break;
        }
        case "SHOW_CAPTCHA":
        {
            return {...state, showCaptcha:true}
            break;
        }
        case "SET_ADDRESS_VIEW_TYPE":
        {
            return {...state, addressViewType:action.payload}
            break;
        }
        case "GET_LOYALTEA_SCORE_PENDING":
        {
            return {...state, loyalteaPending:true}
            break;
        }
        case "GET_LOYALTEA_SCORE_FULFILLED":
        {
            return {...state, loyalteaPending:false, customerLoyalteaScore:action.payload}
            break;
        }
        case "GET_LOYALTEA_SCORE_REJECTED":
        {
            return {...state, loyalteaPending:false}
            break;
        }
        case "SET_CUSTOMER_DETAIL":
        {
            return {...state, customer: {...action.payload}};
            break;
        }
        case "SET_DEVICE_KEY":
        {
            return {...state, deviceKey: action.payload};
            break;
        }
        case "SET_SESSION_KEY":
        {
            return {...state, sessionKey: action.payload};
            break;
        }
        case "SET_OTP_RESEND_SECONDS":
        {
            return {...state, otpResendSeconds: action.payload};
            break;
        }
        /*case "SET_TRUE_CALLER_QR_STRING":
        {
            return {...state, trueCallerQRString: action.payload};
            break;
        }*/
        case "SET_ERROR_SIGN_UP_MESSAGE":
        {
            return {...state, errorSignUpMessage: action.payload};
            break;
        }
        case "SET_REDEMPTION_DONE":
        {
            return {...state, setRedemptionDone: action.payload};
            break;
        }
        case "SET_COUPON":
        {
            return {...state, couponCode: action.payload};
            break;
        }
        case "SHOW_CONTACT_SECTION": {
            return {...state, showContactSection: action.payload};
            break;
        }
        default:
            return state;
    }

    return state;

}