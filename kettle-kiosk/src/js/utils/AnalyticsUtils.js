/**
 * Created by Chaayos on 04-03-2017.
 */

class AnalyticsUtils{

    constructor(){
        this.category = {
            3623:"IndianChai",
            3624:"ChaiUn<PERSON>hai",
            3625:"Cold",
            3626:"Breakfast",
            3627:"Feasts",
            3628:"<PERSON><PERSON>",
            3629:"<PERSON><PERSON>",
            3631:"Merchandise"
        };
        this.getCategory = this.getCategory.bind(this);
        this.sendOrderForTracking = this.sendOrderForTracking.bind(this);
        this.trackInitiateCheckout = this.trackInitiateCheckout.bind(this);
        this.trackPaymentModeSelected = this.trackPaymentModeSelected.bind(this);
        this.trackAddCartItem = this.trackAddCartItem.bind(this);
    }

    getCategory(id){
        return this.category[id];
    }

    sendOrderForTracking(order){
        try{
            //var cart = order;
            var products = [];
            var packaging = 0;

            var deliveryCharge = 0;
            order.orders.map(orderItem => {
                if(orderItem.productId!=1044 && orderItem.productId!=1043){
                    products.push({
                        name:orderItem.productName,
                        id:orderItem.productId,
                        brand:"Chaayos",
                        category:this.getCategory(orderItem.productCategory.id),
                        price:orderItem.price,
                        quantity:orderItem.quantity,
                        variant:orderItem.dimension
                    });
                }else{
                    if(orderItem.productId==1044){
                        deliveryCharge = orderItem.price;
                    }
                    if(orderItem.productId==1043){
                        packaging = orderItem.price;
                    }
                }
            });
            var tax = order.transactionDetail.tax;
            window.dataLayer = window.dataLayer || [];
            var ecommerce = {
                purchase: {
                    actionField: {
                        id:order.generateOrderId,
                        revenue:order.transactionDetail.paidAmount,
                        tax:tax.toFixed(2),
                        shipping: deliveryCharge,
                        coupon:order.offerCode
                    },
                    products:products
                }
            };
            dataLayer.push({
                ecommerce: ecommerce,
                event:"ORDER_SUCCESS"
            });
            var criteria = order.source;
            var source = "";
            (criteria == "COD")?source="Delivery":null;
            (criteria == "TAKE_AWAY")?source="Takeaway":null;
            (criteria == "CAFE")?source="DineIn":null;
            dataLayer.push({
                transactionAmount: order.transactionDetail.paidAmount,
                transactionId:order.generateOrderId,
                transactionSource:source,
                transactionUnit:order.unitName,
                event:"FetchOrderDetail"
            });
            return {purchase:ecommerce.purchase}
        }catch (e){
            return null;
        }
    }

    trackInitiateCheckout(cart){
        dataLayer.push({
            transactionAmount: cart.orderDetail.transactionDetail.paidAmount,
            event:"InitiateCheckout"
        });
    }

    trackPaymentModeSelected(data){
        dataLayer.push({
            transactionAmount: data.amount,
            event:"AddPaymentInfo"
        });
    }

    trackAddCartItem(data){
        dataLayer.push({
            itemPrice:data.cartItem.price,
            cartId:data.cartId,
            productId:data.cartItem.productId,
            event:"AddToCart"
        });
    }
}

const analyticsUtils = new AnalyticsUtils();
export default analyticsUtils;