/**
 * Created by Chaayos on 04-03-2017.
 */

class FacebookPixelUtils {

    constructor() {
        this.handlePixelTrack = this.handlePixelTrack.bind(this);
        this.getPageViewObject = this.getPageViewObject.bind(this);
        this.getCheckoutObject = this.getCheckoutObject.bind(this);
        this.getOrderSuccessObject = this.getOrderSuccessObject.bind(this);
    }

    handlePixelTrack(eventName, data, isCustom){
        try{
            if(isCustom){
                fbq('trackCustom', eventName, data);
            }else{
                fbq('track', eventName, data);
            }
        }catch(e){
            //console.log(e.name,e.message);
        }
    }

    getPageViewObject(page, deviceType) {
        return {
            viewName: page,
            deviceType: deviceType
        };
    }

    getCheckoutObject(value, currency, num_items, content_name, content_categories, content_ids){
        return {
            value: value,
            currency: currency,
            num_items:num_items,
            content_name: content_name,
            content_categories: content_categories,
            content_ids: content_ids
        }
    }

    getOrderSuccessObject(purchase){
        return {purchase:purchase}
    }
}


const facebookPixelUtils = new FacebookPixelUtils();
export default facebookPixelUtils;