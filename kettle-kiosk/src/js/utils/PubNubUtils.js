/**
 * Created by Chaayos on 07-04-2017.
 */

//import PubNub from "pubnub";
import StorageUtils from "./StorageUtils";

class PubNubUtils {

    constructor(){
        this.pubnub = null;
        this.initPubNub = this.initPubNub.bind(this);
        this.getPubNub = this.getPubNub.bind(this);
        this.getPubNubPublishKey = this.getPubNubPublishKey.bind(this);
        this.getPubNubSubscribeKey = this.getPubNubSubscribeKey.bind(this);
        this.getChannelName = this.getChannelName.bind(this);
    }

    initPubNub(){
        this.pubnub = new PubNub({
            publishKey : this.getPubNubPublishKey(),
            subscribeKey : this.getPubNubSubscribeKey()
        });
    }

    getPubNub() {
        return this.pubnub;
    }

    getPubNubPublishKey() {
        if(StorageUtils.getEnvType() === "PROD") {
            return '******************************************';
        } else {
            return '******************************************';
        }
    }

    getPubNubSubscribeKey() {
        if(StorageUtils.getEnvType() === "PROD") {
            return '******************************************';
        } else {
            return '******************************************';
        }
    }

    getChannelName() {
        var envType = (StorageUtils.getEnvType()=="PROD") ? "SPROD" : StorageUtils.getEnvType();
        var channel = envType + '_' + 'KioskTCChannel_' + StorageUtils.getKettleAuthDetails().unitDetail.id + '_' + StorageUtils.getKettleAuthDetails().terminal;
        console.log("channel name:::::::::::::::::::::::::::::::::::::::::: ", channel);
        return channel;
    }

    receiveMessage(callback) {
        this.pubnub.addListener({
            status: function (statusEvent) {
            },
            message: function (m) {
                if (callback) {
                    callback(m.message);
                }
            },
            presence: function (m) {
                console.log(m)
            },
        });

        this.pubnub.subscribe({
            channels: [this.getChannelName()],
            withPresence: true
        });
    }

}

const pubNubUtils = new PubNubUtils();
export default pubNubUtils;
