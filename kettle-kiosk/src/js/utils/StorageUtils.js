/**
 * Created by Chaayos on 07-04-2017.
 */

import AppUtils from "./../AppUtil";

class StorageUtils {

    constructor(){
        this.checkEmpty = this.checkEmpty.bind(this);
        this.setLocalStoreItem = this.setLocalStoreItem.bind(this);
        this.getLocalStoreItem = this.getLocalStoreItem.bind(this);
        this.removeLocalStoreItem = this.removeLocalStoreItem.bind(this);
        this.authDetail = {};
        this.setAuthDetail = this.setAuthDetail.bind(this);
        this.getAuthDetail = this.getAuthDetail.bind(this);
        this.removeAuthDetail = this.removeAuthDetail.bind(this);
        this.unitDetail = {};
        this.getUnitDetails = this.getUnitDetails.bind(this);
        this.setUnitDetails = this.setUnitDetails.bind(this);
        this.removeUnitDetails = this.removeUnitDetails.bind(this);
        this.localityMetadata = null;
        this.getLocalityMetadata = this.getLocalityMetadata.bind(this);
        this.setLocalityMetadata = this.setLocalityMetadata.bind(this);
        this.getDeviceId = this.getDeviceId.bind(this);
        this.cartDetail = {};
        this.getCartDetail = this.getCartDetail.bind(this);
        this.setCartDetail = this.setCartDetail.bind(this);
        this.removeCartDetail = this.removeCartDetail.bind(this);
        this.customerDetail = {};
        this.setCustomerDetail = this.setCustomerDetail.bind(this);
        this.getCustomerDetail = this.getCustomerDetail.bind(this);
        this.getCustomerId = this.getCustomerId.bind(this);
        this.getCustomerContact = this.getCustomerContact.bind(this);
        this.getSessionId = this.getSessionId.bind(this);
        this.getDeliveryPackagingCartItem = this.getDeliveryPackagingCartItem.bind(this);
        this.setDeliveryPackagingCartItems = this.setDeliveryPackagingCartItems.bind(this);
        this.getDeliveryPackagingCartItems = this.getDeliveryPackagingCartItems.bind(this);
        this.setCurrentOrderId = this.setCurrentOrderId.bind(this);
        this.getCurrentOrderId = this.getCurrentOrderId.bind(this);
        this.campaignDetails = {};
        this.getCampaignDetails = this.getCampaignDetails.bind(this);
        this.setCampaignDetails = this.setCampaignDetails.bind(this);
        this.removeCampaignDetails = this.removeCampaignDetails.bind(this);
        this.promoData = {};
        this.getPromoData = this.getPromoData.bind(this);
        this.setPromoData = this.setPromoData.bind(this);
        this.removePromoData = this.removePromoData.bind(this);

        this.kettleAuthDetails = {};
        this.getKettleAuthDetails = this.getKettleAuthDetails.bind(this);
        this.setKettleAuthDetails = this.setKettleAuthDetails.bind(this);
        this.getTrueCallerAppId = this.getTrueCallerAppId.bind(this);
        this.getTrueCallerAppName = this.getTrueCallerAppName.bind(this);
        this.getQrConfigOptions = this.getQrConfigOptions.bind(this);
        this.getEnvType = this.getEnvType.bind(this);
        this.getPaytmConfig = this.getPaytmConfig.bind(this);
        this.getPaytmQrApi = this.getPaytmQrApi.bind(this);

        this.selectivePaymentMode = null;
        this.setSelectivePaymentMode = this.setSelectivePaymentMode.bind(this);
        this.getSelectivePaymentMode = this.getSelectivePaymentMode.bind(this);
        this.cardPaymentMode = null;
        this.setCardPaymentMode = this.setCardPaymentMode.bind(this);
        this.getCardPaymentMode = this.getCardPaymentMode.bind(this);
        this.desiChaiProductIds = [10,11,12,50,14,15,170,1205];
        this.setUnitProducts = this.setUnitProducts.bind(this);
        this.products = [];
        this.giftCardCart = null;
        this.deviceKey = null;
    }

    checkEmpty(obj) {
        if (obj === undefined || obj === null || obj === {}) {
            return true;
        }
        if (typeof obj === "string" || typeof obj === "number") {
            return obj.toString().trim().length === 0;
        }
        for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
        }
        return true;
    }

    setLocalStoreItem(key, data){
        if(window.localStorage){
            try{
                localStorage.setItem(key, data);
            }catch(e){}
        }
    }

    getLocalStoreItem(key){
        if (window.localStorage) {
            try{
                return localStorage.getItem(key);
            }catch(e){}
        }
    }

    removeLocalStoreItem(key){
        if (window.localStorage && this.getItem(key)!=null) {
            try{
                localStorage.removeItem(key);
            }catch(e){}
        }
    }

    setUnitProducts(products) {
        this.products = products;
    }

    getUnitProducts () {
        return this.products;
    }

    setGiftCardCartDetails(giftCardCart) {
        this.giftCardCart = giftCardCart;
    }

    getGiftCardCartDetails () {
        return this.giftCardCart;
    }

    setDeviceKey(deviceKey) {
        this.deviceKey = deviceKey;
    }

    getDeviceKey () {
        return this.deviceKey;
    }

    setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days*24*60*60*1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + value + expires + "; path=/";
    }

    getCookie(cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return null;
    }

    eraseCookie(name) {
        this.setCookie(name, "", -1);
    }

    setAuthDetail(data) {
        this.authDetail = data;
        this.setCookie("cad", JSON.stringify(data),30);
    }

    setAuthDetailNoCookieUpdate(data) {
        this.authDetail = data;
    }

    setCartDetailsInAuthDetail(data) {
        let prevAuthDetail = this.getAuthDetail();
        console.log("Previous auth details:: "+JSON.stringify(prevAuthDetail));
        console.log("New auth details:: "+JSON.stringify(data));

        prevAuthDetail.cartDetail = data.cartDetail;
        console.log("Updated cart details:: "+JSON.stringify(prevAuthDetail));
        this.setCookie("cad", JSON.stringify(prevAuthDetail),30);

    }

    getAuthDetail() {
        if (this.checkEmpty(this.authDetail)) {
            var data = this.getCookie("cad");
            this.authDetail = data!=null?JSON.parse(data):null;
        }
        return this.authDetail;
    }

    removeAuthDetail() {
        this.authDetail = null;
        this.eraseCookie("cad");
    }

    setUnitDetails(data) {
        this.unitDetail = data;
        this.setCookie("cud", JSON.stringify(data), 30);
    }

    getUnitDetails() {
        if (this.checkEmpty(this.unitDetail)) {
            var data = this.getCookie("cud");
            this.unitDetail = data!=null?JSON.parse(data):null;
        }
        return this.unitDetail;
    }

    removeUnitDetails() {
        this.unitDetail = null;
        this.eraseCookie("cud");
    }

    setLocalityMetadata(data) {
        this.localityMetadata = data;
        this.setCookie("clmd", JSON.stringify(data), 30);
    }

    getLocalityMetadata() {
        if (this.checkEmpty(this.localityMetadata)) {
            var data = this.getCookie("clmd");
            this.localityMetadata = data!=null?JSON.parse(data):{};
        }
        return this.localityMetadata;
    }

    getDeviceId() {
        var data = this.getAuthDetail();
        const deviceKey = data!=null?data.deviceKey:null;
        if (deviceKey != null) {
            return deviceKey.substring(0, deviceKey.indexOf(":"));
        } else {
            return null;
        }
    }

    setCartDetail(data) {
        this.cartDetail = data;
        //this.setItem("ccd", JSON.stringify(data));
    }

    getCartDetail() {
        /*if (this.checkEmpty(this.cartDetail)) {
            this.cartDetail = this.getItem("ccd")!=null?JSON.parse(this.getItem("ccd")):null;
        }*/
        return this.cartDetail;
    }

    removeCartDetail() {
        this.cartDetail = {};
        //this.removeItem("ccd");
    }

    setCustomerDetail(data){
        this.customerDetail = data;
        this.setCookie("ccsd", JSON.stringify(data), 30);
    }

    getCustomerDetail(){
        if (this.checkEmpty(this.customerDetail)) {
            var data = this.getCookie("ccsd");
            this.customerDetail = data!=null?JSON.parse(data):null;
        }
        return this.customerDetail;
    }

    removeCustomerDetail() {
        this.eraseCookie("ccsd");
    }

    getCustomerId() {
        const sessionKey = !this.checkEmpty(this.getAuthDetail())?this.getAuthDetail().sessionKey:null;
        if (sessionKey != null) {
            return sessionKey.split("#")[3];
        } else {
            return null;
        }
    }

    getCustomerContact() {
        const sessionKey = !this.checkEmpty(this.getAuthDetail())?this.getAuthDetail().sessionKey:null;
        if (sessionKey != null) {
            return sessionKey.split("#")[4];
        } else {
            return null;
        }
    }

    setTerminalId(terminalId) {
        this.terminalId = terminalId;
    }

    getTerminalId() {
        return this.terminalId;
    }

    getSessionId() {
        const sessionKey = !this.checkEmpty(this.getAuthDetail())?this.getAuthDetail().sessionKey:null;
        if (sessionKey != null) {
            return sessionKey.substring(0, sessionKey.indexOf("#"));
        } else {
            return null;
        }
    }

    getRedemptionChaiProductIds() {
        return this.desiChaiProductIds;
    }

    getDeliveryPackagingCartItem(product){
        if(product!=null){
            return {
                itemId: 0,
                productId: product.id,
                customizationStrategy: 0,
                productName: product.name,
                quantity: 1,
                price: product.price,
                totalAmount: null,
                amount: product.price,
                discountDetail: {
                    discountCode: null,
                    discountReason: null,
                    promotionalOffer: 0,
                    discount: {
                        percentage: 0,
                        value: 0
                    },
                    totalDiscount: 0
                },
                addons: [],
                dimension: product.dimension,
                billType: product.billType,
                isCombo: product.type == 8,
                composition: {
                    variants: [],
                    products: [],
                    menuProducts: [],
                    addons: []
                },
                recipeId: null,
                code:product.taxCode
            }
        }else{
            return null;
        }
    }

    setDeliveryPackagingCartItems(data){
        this.deliveryPackagingCartItems = data!=null?{delivery:this.getDeliveryPackagingCartItem(data.delivery), packaging:this.getDeliveryPackagingCartItem(data.packaging)}:null;
        this.setCookie("dpci", JSON.stringify(data),30);
    }

    getDeliveryPackagingCartItems(){
        var data = null;
        if (this.checkEmpty(this.deliveryPackagingCartItems)) {
            data = this.getCookie("dpci");
            data = data!=null?JSON.parse(data):null;
            this.deliveryPackagingCartItems = data!=null?{delivery:this.getDeliveryPackagingCartItem(data.delivery), packaging:this.getDeliveryPackagingCartItem(data.packaging)}:null;
        }
        return this.deliveryPackagingCartItems;
    }

    setCurrentOrderId(data){
        this.currentOrderId = data;
        this.setCookie("cco", JSON.stringify(data), 30);
    }

    getCurrentOrderId(){
        if (this.checkEmpty(this.currentOrderId)) {
            var data = this.getCookie("cco");
            this.currentOrderId = data!=null?JSON.parse(data):null;
        }
        return this.currentOrderId;
    }

    getCampaignDetails(){
        var data = this.getCookie("cd");
        return data!=null?JSON.parse(data):null;
    }

    setCampaignDetails(data, days){
        this.campaignDetails = data;
        if(days== null || typeof days == 'undefined'){
            days = 7;
        }
        this.setCookie("cd", JSON.stringify(data), days);
    }

    removeCurrentOrderId() {
        this.eraseCookie("cco");
    }

    removeCampaignDetails() {
        this.eraseCookie("cd");
    }

    getPromoData(){
        var data = this.getCookie("pd");
        return data!=null?JSON.parse(data):null;
    }

    setPromoData(data, days){
        this.promoData = data;
        if(days== null || typeof days == 'undefined'){
            days = 7;
        }
        this.setCookie("pd", JSON.stringify(data), days);
    }

    removePromoData() {
        this.eraseCookie("pd");
    }

    getKettleAuthDetails(){
        var data = this.getCookie("kad");
        return data!=null?JSON.parse(data):null;
    }

    setKettleAuthDetails(data, days){
        this.setCookie("kad", JSON.stringify(data), days);
    }

    getTrueCallerAppId() {
        if(this.getEnvType() === "PROD") {
            return "si8c37f64038ab40d986df7431167c46c3";
        } else {
            return "sia490401ea785451a9b0f744c7133dc86";
        }
    }

    getTrueCallerAppName() {
        if(this.getEnvType() === "PROD") {
            return "CHAAYOS";
        } else {
            return "KETTLE-CUSTOMER-DEV";
        }
    }

    getQrConfigOptions() {
        var requestId = encodeURI(this.getKettleAuthDetails().unitDetail.id+"#"+this.getKettleAuthDetails().terminal+"*"+new Date().getTime());
        var trueCallerQRString = this.getTrueCallerAppId()+"|"+requestId+"|appName:"+this.getTrueCallerAppName();
        return {
            canvas: null,
            content: trueCallerQRString,
            width: 250,
            logo: {
                src: "/img/favicon-96x96.png",
                // src: 'https://cdn.blog.cloudself.cn/images/avatar.png',
                radius: 8
            }
        };
    }

    getPaytmQrConfigOptions(qrCodeId) {
        return {
            canvas: null,
            content: qrCodeId,
            width: 300,
            borderRadius: 5,
            border: 2,
            logo: {
                src: "/img/favicon-96x96.png",
                radius: 8
            }
        };
    }

    getEnvType(){
        if(window.location.href.indexOf("dev-staging.chaayos.com") >= 0) {
            return "DEV";
        } else if(window.location.href.indexOf("chaayos.com") >= 0) {
            return "PROD";
        } else {
            return "DEV";
        }
    }

    getPaytmConfig() {
        if(this.getEnvType() == "PROD") {
            return null;
        } else {
            var data = {
                "mid": "Chaayo33428196367406",
                "orderId": null,
                "amount": 1,
                "businessType": "QR_ORDER",
                "displayName": "Test Txn",
                "imageRequired": false,
                "contactPhoneNo": this.getCustomerDetail().contact,
                "expiryDate": AppUtils.formatDate(AppUtils.addTime(null, 2), 'yyyy-MM-dd hh:mm:ss'),
                "posId": this.getKettleAuthDetails().unitDetail.id+"_"+this.getKettleAuthDetails().terminal,
                "orderDetails": "true",
                "invoiceDetails": "true"
            };
            return data;
        }
    }

    getPaytmQrApi(){
        if(this.getEnvType() == "PROD") {
            return "https://securegw.paytm.in/qr/create";
        } else {
            return "http://securegw-stage.paytm.in/qr/create";
        }
    }

    setSelectivePaymentMode(mode) {
        this.selectivePaymentMode = mode;
        this.setLocalStoreItem("spm", mode);
    }

    getSelectivePaymentMode() {
        if(this.selectivePaymentMode == null) {
            this.selectivePaymentMode = this.getLocalStoreItem("spm");
        }
        return this.selectivePaymentMode;
    }

    setCardPaymentMode(mode) {
        this.cardPaymentMode = mode;
        this.setLocalStoreItem("cpm", mode);
    }

    getCardPaymentMode() {
        if(this.cardPaymentMode == null) {
            this.cardPaymentMode = this.getLocalStoreItem("cpm");
        }
        return this.cardPaymentMode;
    }

}

const storageUtils = new StorageUtils();
export default storageUtils;
