/**
 * Created by Chaayos on 07-03-2017.
 */
import WebEngageUtils from "./WebEngageUtils";
import StorageUtils from "./StorageUtils";

class WebEngageDataConverter {

    constructor() {
        this.getPaymentModeNameFromId = this.getPaymentModeNameFromId.bind(this);
        this.getPaymentMode = this.getPaymentMode.bind(this);
        this.getSettlements = this.getSettlements.bind(this);
        this.convertPageView = this.convertPageView.bind(this);
        this.convertOrderInitiated = this.convertOrderInitiated.bind(this);
        this.convertOrderSuccess = this.convertOrderSuccess.bind(this);
        this.convertOrderMode = this.convertOrderMode.bind(this);
        this.convertLocalityOutlet = this.convertLocalityOutlet.bind(this);
        this.convertUnitClosed = this.convertUnitClosed.bind(this);
        this.convertCategory = this.convertCategory.bind(this);
        this.convertLocalityChangeCLicked = this.convertLocalityChangeCLicked.bind(this);
        this.convertCoupon = this.convertCoupon.bind(this);
        this.convertOfferSuccess = this.convertOfferSuccess.bind(this);
        this.convertOfferFailed = this.convertOfferFailed.bind(this);
        this.convertTaxDetail = this.convertTaxDetail.bind(this);
        this.convertOrderInstruction = this.convertOrderInstruction.bind(this);
        this.convertGoToMenuClicked = this.convertGoToMenuClicked.bind(this);
        this.convertOTPResend = this.convertOTPResend.bind(this);
        this.convertUserLogin = this.convertUserLogin.bind(this);
        this.convertAddressCount = this.convertAddressCount.bind(this);
        this.convertAddressSelected = this.convertAddressSelected.bind(this);
        this.convertAddAddressClicked = this.convertAddAddressClicked.bind(this);
        this.convertAddressAdd = this.convertAddressAdd.bind(this);
        this.convertPaymentModeSelected = this.convertPaymentModeSelected.bind(this);
        this.convertCashDenied = this.convertCashDenied.bind(this);
        this.convertProductAdded = this.convertProductAdded.bind(this);
        this.convertProductRemoved = this.convertProductRemoved.bind(this);
        this.convertReorderClicked = this.convertReorderClicked.bind(this);
        this.convertUserData = this.convertUserData.bind(this);
        this.convertInitialStockout = this.convertInitialStockout.bind(this);
        this.convertSelectionStockout = this.convertSelectionStockout.bind(this);
    }

    getPaymentModeNameFromId(id) {
        var mode = {1: "CASH", 12: "RAZORPAYONLINE", 13: "PAYTMONLINE"};
        return mode[id];
    }

    getPaymentMode(settlements) {
        var mode = [];
        var x = this;
        settlements.map(function (settlement) {
            mode.push(x.getPaymentModeNameFromId(settlement.mode));
        });
        return mode.join(",");
    }

    getSettlements(settlements) {
        var data = Object.assign([], settlements);
        var ret = [];
        var x = this;
        data.map(function (settlement) {
            ret.push(x.getPaymentModeNameFromId(settlement.mode) + "x" + settlement.amount);
            //settlement.mode = this.getPaymentMode(settlement.mode);
        });
        return ret.join(",");
    }

    getConvertedObject(event, data) {
        switch (event) {
            case "PAGE_VISIT": {
                return this.convertPageView(data);
            }
            case "ORDER_INITIATED": {
                return this.convertOrderInitiated(data);
            }
            case "ORDER_SUCCESS_NEW": {
                return this.convertOrderSuccess(data);
            }
            case "ORDER_MODE_SELECTED": {
                return this.convertOrderMode(data);
            }
            case "LOCATION_OUTLET_SELECTED": {
                return this.convertLocalityOutlet(data);
            }
            case "OUTLET_CLOSED": {
                return this.convertUnitClosed(data);
            }
            case "CATEGORY_CLICKED": {
                return this.convertCategory(data);
            }
            case "CATEGORY_CHANGED": {
                return this.convertCategory(data);
            }
            case "LOCALITY_CHANGE_CLICKED": {
                return this.convertLocalityChangeCLicked(data);
            }
            case "FULL_MENU_EXPLORED": {
                return Object.assign({}, WebEngageUtils.getDefaultAttributes());
            }
            case "COUPON_TRIED": {
                return this.convertCoupon(data);
            }
            case "COUPON_REMOVED": {
                return this.convertCoupon(data);
            }
            case "OFFER_SUCCESS": {
                return this.convertOfferSuccess(data);
            }
            case "OFFER_FAILED": {
                return this.convertOfferFailed(data);
            }
            case "TAX_DETAIL_VIEWED": {
                return this.convertTaxDetail(data);
            }
            case "ORDER_INSTRUCTION_ADDED": {
                return this.convertOrderInstruction(data);
            }
            case "GO_TO_MENU_CLICKED": {
                return this.convertGoToMenuClicked(data);
            }
            case "OTP_RESENT": {
                return this.convertOTPResend(data);
            }
            case "CUSTOMER_LOOKUP": {
                return this.convertCustomerLookup(data);
            }
            case "USER_LOGIN": {
                return this.convertUserLogin(data);
            }
            case "USER_SIGNUP": {
                return this.convertUserLogin(data);
            }
            case "EXISTING_ADDRESS_COUNT": {
                return this.convertAddressCount(data);
            }
            case "ADDRESS_SELECTED": {
                return this.convertAddressSelected(data);
            }
            case "ADD_ADDRESS_CLICKED": {
                return this.convertAddAddressClicked(data);
            }
            case "ADDRESS_ADD": {
                return this.convertAddressAdd(data);
            }
            case "PAYMENT_MODE_SELECTED": {
                return this.convertPaymentModeSelected(data);
            }
            case "CASH_ON_DELIVERY_DENIED": {
                return this.convertCashDenied(data);
            }
            case "RETURNED_FROM_PAYMENT": {
                return Object.assign({}, WebEngageUtils.getDefaultAttributes());
            }
            case "PRODUCT_ADDED": {
                return this.convertProductAdded(data);
            }
            case "PRODUCT_REMOVED": {
                return this.convertProductAdded(data);
            }
            case "REORDER_CLICKED": {
                return this.convertReorderClicked(data);
            }
            case "INITIAL_STOCKOUT": {
                return this.convertInitialStockout(data);
            }
            case "SELECTION_STOCKOUT": {
                return this.convertSelectionStockout(data);
            }
            case "REJECTED_STOCK": {
                return this.convertInitialStockout(data);
            }
            case "FIRST_TIME_OFFER_MODAL_SHOWN": {
                return this.convertPromoModal(data);
            }
            case "FIRST_TIME_OFFER_ACCEPTED": {
                return this.convertPromoModal(data);
            }
            case "FIRST_TIME_OFFER_REJECTED": {
                return this.convertPromoModal(data);
            }
            case "PAYMENT_SUCCESS": {
                return this.convertPaymentSuccess(data);
            }
            case "PAYMENT_FAILED": {
                return this.convertPaymentFailed(data);
            }
            default : {
                var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes(), data);
                //defaultAttributes[event] = data;
                return defaultAttributes;
            }
        }
    }

    convertPageView(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.viewName = data.page;
        defaultAttributes.deviceType = data.device;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertOrderInitiated(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        var orderItems = [];
        var giftCardOnly = true;
        data.cart.orderDetail.orders.map(orderItem => {
            orderItems.push(orderItem.productName + "x" + orderItem.quantity);
            if (giftCardOnly) {
                giftCardOnly = [1026, 1027, 1048, 1056].indexOf(orderItem.productId) >= 0;
            }
        });
        orderItems = orderItems.join(",");
        defaultAttributes.cartId = data.cart.cartId;
        defaultAttributes.deviceId = data.cart.deviceId;
        defaultAttributes.customerId = data.cart.customerId != null ? data.cart.customerId : "";
        defaultAttributes.sessionId = data.cart.sessionId != null ? data.cart.sessionId : "";
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        defaultAttributes.criteria = data.criteria;
        //defaultAttributes.city = data.city;
        //defaultAttributes.locality = data.locality;
        //defaultAttributes.outlet = data.outlet;
        defaultAttributes.orderSource = data.cart.orderDetail.source;
        defaultAttributes.totalAmount = data.cart.orderDetail.transactionDetail.totalAmount;
        defaultAttributes.taxableAmount = data.cart.orderDetail.transactionDetail.taxableAmount;
        defaultAttributes.paidAmount = data.cart.orderDetail.transactionDetail.paidAmount;
        defaultAttributes.totalDiscount = data.cart.orderDetail.transactionDetail.discountDetail.totalDiscount;
        defaultAttributes.unitId = data.cart.orderDetail.unitId; //StorageUtils.getLocalityMetadata().outlet != null ? StorageUtils.getLocalityMetadata().outlet.value : 0;
        defaultAttributes.unitName = data.cart.orderDetail.unitName;
        defaultAttributes.offerCode = data.cart.orderDetail.offerCode == null ? "" : data.cart.orderDetail.offerCode;
        defaultAttributes.containsSignupOffer = data.cart.orderDetail.containsSignupOffer === true;
        defaultAttributes.timeToInitiate = (new Date().getTime() - data.cart.creationTime) / 1000;
        defaultAttributes.cartStatus = data.cart.cartStatus;
        defaultAttributes.orderItems = orderItems;
        defaultAttributes.giftCardOnly = giftCardOnly === true;
        return defaultAttributes;
    }

    convertOrderSuccess(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        var orderItems = [];
        var giftCardOnly = true;
        data.cart.orders.map(orderItem => {
            orderItems.push(orderItem.productName + "x" + orderItem.quantity);
            if (giftCardOnly) {
                giftCardOnly = [1026, 1027, 1048, 1056].indexOf(orderItem.productId) >= 0;
            }
        });
        orderItems = orderItems.join(",");
        //defaultAttributes.city = data.city;
        //defaultAttributes.locality = data.locality;
        //defaultAttributes.outlet = data.outlet;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        defaultAttributes.orderSource = data.cart.source;
        defaultAttributes.totalAmount = data.cart.transactionDetail.totalAmount;
        defaultAttributes.taxableAmount = data.cart.transactionDetail.taxableAmount;
        defaultAttributes.paidAmount = data.cart.transactionDetail.paidAmount;
        defaultAttributes.totalDiscount = data.cart.transactionDetail.discountDetail.totalDiscount;
        defaultAttributes.unitId = StorageUtils.getLocalityMetadata().outlet != null ? StorageUtils.getLocalityMetadata().outlet.value : 0;
        defaultAttributes.unitName = data.cart.unitName;
        defaultAttributes.offerCode = data.cart.offerCode == null ? "" : data.cart.offerCode;
        defaultAttributes.containsSignupOffer = (data.cart.containsSignupOffer != null && data.cart.containsSignupOffer == true);
        defaultAttributes.orderItems = orderItems;
        defaultAttributes.giftCardOnly = giftCardOnly === true;
        defaultAttributes.generatedOrderId = data.cart.generateOrderId;
        defaultAttributes.externalOrderId = data.cart.externalOrderId;
        defaultAttributes.billingServerTime = new Date(data.cart.billingServerTime);
        defaultAttributes.paymentMode = this.getPaymentMode(data.cart.settlements);
        defaultAttributes.pointsRedeemed = 0;
        defaultAttributes.settlements = this.getSettlements(data.cart.settlements);
        defaultAttributes.channelPartner = "CHAAYOS_WEBAPP";
        return defaultAttributes;
    }

    convertOrderMode(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.mode = data.mode;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertLocalityOutlet(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        var type = {0: "PRIMARY", 1: "PRIMARY", 2: "SECONDARY", 3: "TERTIARY"};
        defaultAttributes.city = data.city;
        defaultAttributes.locality = data.locality != null ? data.locality : "";
        defaultAttributes.outlet = data.outlet != null ? data.outlet : "";
        defaultAttributes.type = type[data.type];
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertUnitClosed(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.city = data.city;
        defaultAttributes.locality = data.locality;
        defaultAttributes.outlet = data.outlet;
        defaultAttributes.message = data.msg;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertCategory(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.category = data.cat;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertLocalityChangeCLicked(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.page = data.page;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertCoupon(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.coupon = data.coupon;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertOfferSuccess(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.coupon = data.coupon;
        defaultAttributes.discount = data.discount;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertOfferFailed(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.coupon = data.coupon;
        defaultAttributes.errorCode = data.errorCode;
        defaultAttributes.reason = data.reason;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertTaxDetail(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.cartId = data.cartId;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertOrderInstruction(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.orderInstruction = data.remark;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertGoToMenuClicked(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.source = data.source;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertCustomerLookup(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.contact = data.contact;
        defaultAttributes.status = data.status;
        defaultAttributes.isRegistered = data.isRegistered;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertOTPResend(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.contact = data.contact;
        defaultAttributes.status = data.status;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertUserLogin(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.contact = data.contact;
        defaultAttributes.name = data.name != null ? data.name : "";
        defaultAttributes.email = data.email != null ? data.email : "";
        defaultAttributes.otp = data.otp;
        defaultAttributes.targetUrl = data.targetUrl;
        defaultAttributes.status = data.status;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertAddressCount(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.count = data.count;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertAddressSelected(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.type = data.type;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertAddAddressClicked(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.linkPosition = data.pos;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertAddressAdd(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.status = data.status;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertPaymentModeSelected(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.paymentPartner = data.paymentPartner;
        defaultAttributes.mode = data.mode;
        defaultAttributes.orderMode = data.orderMode;
        defaultAttributes.amount = data.amount;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertCashDenied(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.amount = data.amount;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertPaymentSuccess(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.paymentPartner = data.paymentPartner;
        defaultAttributes.mode = data.mode;
        defaultAttributes.orderMode = data.orderMode;
        defaultAttributes.amount = data.amount;
        defaultAttributes.externalOrderId = data.externalOrderId;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertPaymentFailed(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.paymentPartner = data.paymentPartner;
        defaultAttributes.mode = data.mode;
        defaultAttributes.orderMode = data.orderMode;
        defaultAttributes.amount = data.amount;
        defaultAttributes.externalOrderId = data.externalOrderId;
        defaultAttributes.reason = data.reason;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertProductAdded(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        var sugar = "", patti = "", butter = "", addons = "", bread = "", milk = "";
        data.cartItem.composition.variants.map((variant) => {
            if (variant.productId == 100330) {
                sugar = variant.alias;
            }
            if ([100123, 100834].indexOf(variant.productId) > -1) {
                patti = variant.alias;
            }
            if (variant.productId == 100055) {
                butter = variant.alias;
            }
        });
        data.cartItem.composition.addons.map((addon) => {
            addons += ((addons.length == 0 ? "" : ",") + addon.product.name);
        });
        data.cartItem.composition.products.map((product) => {
            bread = product.product.name;
        });
        milk = WebEngageUtils.getMilk(data.cartItem.productId);
        milk = (milk == null ? "" : milk);
        if (["TopSellers", "BoughtByYou"].indexOf(data.webCategory) >= 0) {
            data.tags.push(data.webCategory);
        }
        defaultAttributes.id = data.cartItem.productId;
        defaultAttributes.name = data.cartItem.productName;
        defaultAttributes.price = data.cartItem.price;
        defaultAttributes.quantity = data.cartItem.quantity;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        defaultAttributes.webCategory = WebEngageUtils.getCategory(data.cartItem.productCategory.id) != null ?
            WebEngageUtils.getCategory(data.cartItem.productCategory.id) : "";
        defaultAttributes.type = 0;
        defaultAttributes.subType = 0;
        defaultAttributes.dimension = data.cartItem.dimension;
        defaultAttributes.milk = milk;
        defaultAttributes.sugar = sugar;
        defaultAttributes.patti = patti;
        defaultAttributes.addons = addons;
        defaultAttributes.bread = bread;
        defaultAttributes.butter = butter;
        defaultAttributes.discount = data.cartItem.discountDetail.totalDiscount;
        defaultAttributes.new = false;
        defaultAttributes.recommended = false;
        defaultAttributes.boughtByYou = false;
        defaultAttributes.recommendationCode = "";
        defaultAttributes.stock = 100;
        defaultAttributes.unitId = data.unitId;
        defaultAttributes.unitName = data.unitName;
        defaultAttributes.cartId = data.cartId;
        defaultAttributes.tags = data.tags != null ? data.tags.join(",") : "";
        return defaultAttributes;
    }

    convertProductRemoved(data) {
        var data = this.convertProductAdded(data);
        data.screen = data.screen;
        return data;
    }

    convertReorderClicked(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.screen = data.screen;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertInitialStockout(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.product = data.products;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertSelectionStockout(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.product = data.products;
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertPromoModal(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        defaultAttributes.campaignId = (data.campaign != null && data.campaign.cid != null) ? data.campaign.cid : "";
        return defaultAttributes;
    }

    convertUserData(data) {
        var defaultAttributes = Object.assign({}, WebEngageUtils.getDefaultAttributes());
        data.name != null ? defaultAttributes.we_first_name = data.name : null;
        defaultAttributes.we_last_name = "";
        data.email != null ? defaultAttributes.we_email = data.email : null;
        data.contact != null ? defaultAttributes.we_phone = "+91" + data.contact : null;
        data.loyaltea != null ? defaultAttributes.loyaltea = data.loyaltea : null;
        return defaultAttributes;
    }
}

const webEngageDataConverter = new WebEngageDataConverter();
export default webEngageDataConverter;