class APIs {

    constructor() {
        this.neoBaseUrl = "http://18.141.79.33:8080/neo-service/rest/v1/";
        this.masterBaseUrl = "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/";
        /*this.neoBaseUrl = "https://internal.chaayos.com/neo-service/rest/v1/";
        this.masterBaseUrl = "https://internal.chaayos.com/master-service/rest/v1/";*/

        this.NEO_CACHE_ROOT_CONTEXT = this.neoBaseUrl + "nc/";
        this.STAMPING_ROOT_CONTEXT = this.neoBaseUrl + "st/";
        this.UNIT_CACHE_ROOT_CONTEXT = this.neoBaseUrl + "uc/";
        this.WEB_CART_ROOT_CONTEXT = this.neoBaseUrl + "wcrt/";
        this.WEB_ORDER_ROOT_CONTEXT = this.neoBaseUrl + "word/";
        this.WEB_CUSTOMER_ROOT_CONTEXT = this.neoBaseUrl + "c/";
        this.WEB_PAYMENT_ROOT_CONTEXT = this.neoBaseUrl + "wp/";
        this.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT = this.neoBaseUrl + "wpe/";
        this.WEB_OFFER_ROOT_CONTEXT = this.neoBaseUrl + "woff/";
        this.WEB_INVENTORY_ROOT_CONTEXT = this.neoBaseUrl + "win/";

        this.UNIT_METADATA_ROOT_CONTEXT = this.masterBaseUrl + "unit-metadata/";
        this.USER_SERVICES_ROOT_CONTEXT = this.masterBaseUrl + "users/";

        this.urls = {
            neoCache: {
                getLocalities: this.NEO_CACHE_ROOT_CONTEXT + "lcts",
                getCities: this.NEO_CACHE_ROOT_CONTEXT + "cts",
                getDeliveryUnit: this.NEO_CACHE_ROOT_CONTEXT + "lct/d/u2",
                getLocalityData: this.NEO_CACHE_ROOT_CONTEXT + "ct/d/u1",
                getTakeawayUnits: this.NEO_CACHE_ROOT_CONTEXT + "lct/t/us",
                productRecipes: this.NEO_CACHE_ROOT_CONTEXT + "p/r",
                tags: this.NEO_CACHE_ROOT_CONTEXT + "ts",
                tag: this.NEO_CACHE_ROOT_CONTEXT + "t",
                specialMenu: this.NEO_CACHE_ROOT_CONTEXT + "mts",
                productTags: this.NEO_CACHE_ROOT_CONTEXT + "pts"
            },
            unitCache: {
                getUnit: this.UNIT_CACHE_ROOT_CONTEXT + "u"
            },
            stamping: {
                registerDevice: this.STAMPING_ROOT_CONTEXT + "rd",
                stampDevice: this.STAMPING_ROOT_CONTEXT + "sd"
            },
            cart: {
                createCart: this.WEB_CART_ROOT_CONTEXT + "c",
                createCartFromOrderId: this.WEB_CART_ROOT_CONTEXT + "c2",
                addItem: this.WEB_CART_ROOT_CONTEXT + "i/a",
                updateItem: this.WEB_CART_ROOT_CONTEXT + "i/u",
                removeItem: this.WEB_CART_ROOT_CONTEXT + "i/r",
                clearCart: this.WEB_CART_ROOT_CONTEXT + "clr",
                checkout: this.WEB_CART_ROOT_CONTEXT + "ckt",
                checkoutCash: this.WEB_CART_ROOT_CONTEXT + "ckt/kiosk",
                checkoutId: this.WEB_CART_ROOT_CONTEXT + "ckt/id",
                checkoutWOId: this.WEB_CART_ROOT_CONTEXT + "ckt/wo",
                sync: this.WEB_CART_ROOT_CONTEXT + "sync",
                setOrderSource: this.WEB_CART_ROOT_CONTEXT + "sos",
                createGiftCardCart: this.WEB_CART_ROOT_CONTEXT + "cgc",
                syncOldCart: this.WEB_CART_ROOT_CONTEXT + "soc"
            },
            customer: {
                lookup: this.WEB_CUSTOMER_ROOT_CONTEXT + "lkp",
                lookupOnly: this.WEB_CUSTOMER_ROOT_CONTEXT + "lkpo",
                getCustomerInfo: this.WEB_CUSTOMER_ROOT_CONTEXT + "gci",
                login: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgn",
                signUp: this.WEB_CUSTOMER_ROOT_CONTEXT + "su",
                signUpVerified: this.WEB_CUSTOMER_ROOT_CONTEXT + "suv",
                addresses: this.WEB_CUSTOMER_ROOT_CONTEXT + "as",
                addAddress: this.WEB_CUSTOMER_ROOT_CONTEXT + "add/as",
                addAddressToCart: this.WEB_CUSTOMER_ROOT_CONTEXT + "up/a/c",
                logout: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgt",
                resendVerification: this.WEB_CUSTOMER_ROOT_CONTEXT + "v/r2",
                loyaltea: this.WEB_CUSTOMER_ROOT_CONTEXT + "lyt",
                boughtByYou: this.WEB_CUSTOMER_ROOT_CONTEXT + "bbu",
                verifyOTP: this.WEB_CUSTOMER_ROOT_CONTEXT + "votp",
                getGiftCard: this.WEB_CUSTOMER_ROOT_CONTEXT + "gc",
                getGiftCard1: this.WEB_CUSTOMER_ROOT_CONTEXT + "gc",
                getLoyalty: this.WEB_CUSTOMER_ROOT_CONTEXT + "glcc",
                generateOtp: this.WEB_CUSTOMER_ROOT_CONTEXT + "gotp"
            },
            payment: {
                create: this.WEB_PAYMENT_ROOT_CONTEXT + "c",
                createRazorPay: this.WEB_PAYMENT_ROOT_CONTEXT + "rc",
                createEzetap: this.WEB_PAYMENT_ROOT_CONTEXT + "ezc",
                createPaytm: this.WEB_PAYMENT_ROOT_CONTEXT + "pc",
                paymentKey: this.WEB_PAYMENT_ROOT_CONTEXT + "pk",
                validate: this.WEB_PAYMENT_ROOT_CONTEXT + "u",
                ezetapValidate: this.WEB_PAYMENT_ROOT_CONTEXT + "eu",
                cancel: this.WEB_PAYMENT_ROOT_CONTEXT + "cc",
                failure: this.WEB_PAYMENT_ROOT_CONTEXT + "fc",
                checkPaytmQRPaymentStatus: this.WEB_PAYMENT_ROOT_CONTEXT + "cpps",
                createPaytmQR: this.WEB_PAYMENT_ROOT_CONTEXT + "cpqr",
                createAGS: this.WEB_PAYMENT_ROOT_CONTEXT + "agsc",
                agsValidate: this.WEB_PAYMENT_ROOT_CONTEXT + "agsu",
                agsCheckStatus: this.WEB_PAYMENT_ROOT_CONTEXT + "cagsps",
                checkPaytmUPIPaymentStatus: this.WEB_PAYMENT_ROOT_CONTEXT + "cpupips",
                createPaytmUPI: this.WEB_PAYMENT_ROOT_CONTEXT + "cpupi",
            },
            paymentExternal: {
                razorPayCheckoutAPI: this.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT + "rco"
            },
            order: {
                searchByWebId: this.WEB_ORDER_ROOT_CONTEXT + "s/w",
                searchByOrderId: this.WEB_ORDER_ROOT_CONTEXT + "s/id",
                searchStatusByOrderId: this.WEB_ORDER_ROOT_CONTEXT + "s/id/s",
                searchCustomerOrders: this.WEB_ORDER_ROOT_CONTEXT + "s/c",
                slackCartId: this.WEB_ORDER_ROOT_CONTEXT + "slk/cid",
                slackWOId: this.WEB_ORDER_ROOT_CONTEXT + "slk/oid",
                isFirstOrder: this.WEB_ORDER_ROOT_CONTEXT + "ifo",
                getCashCardOffer: this.WEB_ORDER_ROOT_CONTEXT + "gco"
            },
            offer: {
                apply: this.WEB_OFFER_ROOT_CONTEXT + "a",
                applyChaayosCash: this.WEB_OFFER_ROOT_CONTEXT + "cc"
            },
            webInventory: {
                unit: this.WEB_INVENTORY_ROOT_CONTEXT + "u",
                unitProducts: this.WEB_INVENTORY_ROOT_CONTEXT + "u/p"
            },
            unitMetadata: {
                allUnits:this.UNIT_METADATA_ROOT_CONTEXT+"all-units",
                takeawayUnits:this.UNIT_METADATA_ROOT_CONTEXT+"takeaway-units"
            },
            users:{
                login:this.USER_SERVICES_ROOT_CONTEXT+"login",
                changePassCode:this.USER_SERVICES_ROOT_CONTEXT+"changePasscode",
                logout:this.USER_SERVICES_ROOT_CONTEXT+"logout"
            },
            ezetap:{
                cardPayment: "http://localhost:9001/ezeapi/cardpayment",
                cancelPayment: "http://localhost:9001/ezeapi/cancelOperation"
            }
        };
        this.getUrls = this.getUrls.bind(this);
    }

    getUrls() {
        return this.urls;
    }
}

const apis = new APIs();
export default apis;