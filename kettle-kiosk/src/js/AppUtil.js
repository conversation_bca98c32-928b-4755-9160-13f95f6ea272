import _ from "lodash";
import paymentService from "./service/PaymentService";
import paymentServiceGST from "./service/PaymentServiceGST";
import StorageUtils from "./utils/StorageUtils";

class AppUtil {

    constructor() {
        this.checkEmpty = this.checkEmpty.bind(this);
        this.isMobile = this.isMobile.bind(this);
        this.calculateTaxes = this.calculateTaxes.bind(this);
        this.validContact = this.validContact.bind(this);
        this.validEmail = this.validEmail.bind(this);
        this.getNewTransactionObject = this.getNewTransactionObject.bind(this);
        this.getEmptyDiscountObj = this.getEmptyDiscountObj.bind(this);
        this.setCustomizationModal = this.setCustomizationModal.bind(this);
        this.getProductName = this.getProductName.bind(this);
        this.getNewCartObj = this.getNewCartObj.bind(this);
        this.sendOrderForTracking = this.sendOrderForTracking.bind(this);
        this.category = {
            3623:"IndianChai",
            3624:"ChaiUnChai",
            3625:"Cold",
            3626:"Breakfast",
            3627:"Feasts",
            3628:"Nashta",
            3629:"Bakery",
            3631:"Merchandise"
        };
        this.getCategory = this.getCategory.bind(this);
        this.getParameterByName = this.getParameterByName.bind(this);
        this.formatTag = this.formatTag.bind(this);
        this.collectionHas = this.collectionHas.bind(this);
        this.findParentBySelector = this.findParentBySelector.bind(this);
        this.getSourceFromCriteria = this.getSourceFromCriteria.bind(this);
        this.getOrderStatusArray = this.getOrderStatusArray.bind(this);
        this.getOrderDisplayStatus = this.getOrderDisplayStatus.bind(this);
        this.isOrderCompleted = this.isOrderCompleted.bind(this);
        this.taxes = null;
        this.setTaxes = this.setTaxes.bind(this);
        this.getTaxes = this.getTaxes.bind(this);
        this.isInterstate = this.isInterstate.bind(this);
        this.setInclusiveTaxPrices = this.setInclusiveTaxPrices.bind(this);
        this.formatDate = this.formatDate.bind(this);
        this.addTime = this.addTime.bind(this);
    }

    checkEmpty(obj) {
        if (obj === undefined || obj === null || obj === {}) {
            return true;
        }
        if (typeof obj === "string" || typeof obj === "number") {
            return obj.toString().trim().length === 0;
        }
        for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
        }
        return true;
    }

    checkOrderDetailsEmpty(cart) {
        return (cart == null || cart.orderDetail == null);
    }

    isMobile(){
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            return true;
        }else{
            return false;
        }
    }

    validContact(contact) {
        if(contact.length>10){
            return false;
        }else{
            if (/^[6,7,8,9]\d{9}/.test(contact)) {
                return true;
            } else {
                return false;
            }
        }
    }

    validEmail(email){
        if(this.checkEmpty(email)){
            return false;
        }else{
            var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            if (re.test(email)) {
                return true;
            } else {
                return false;
            }
        }
    }

    isInterstate(unit, selectedCity){
        var ret = false;
        if(selectedCity!=null && selectedCity.state!=null && unit!=null && unit.location!=null){
            ret =  unit.location.state.id != selectedCity.state;
        }else{
            return ret;
        }
    }

    //Please update both sides (NeoOrderServiceImpl.java) if any changes are done
    calculateTaxes(cart, isInterState) {
        //cart.orderDetail = paymentService.initCalculations(StorageUtils.getLocalityMetadata().criteria, cart.orderDetail);
        cart.orderDetail = paymentServiceGST.initCalculations(StorageUtils.getLocalityMetadata().criteria, cart.orderDetail, this.getTaxes(), isInterState);
        return cart;
        /*var netPriceAmount = 0;
        var mrpAmount = 0;
        var totalAmount = 0;
        var totalDiscount = 0;
        var zeroTaxAmount = 0;
        var promotionalOffer = 0;
        if(cart.orderDetail.transactionDetail!=null){
            var netPriceVatPercentage = cart.orderDetail.transactionDetail.netPriceVat.percentage;
            var mrpVatPercentage = cart.orderDetail.transactionDetail.mrpVat.percentage;
            var surchargePercentage = cart.orderDetail.transactionDetail.surchargeOnTax.percentage;
            if(cart.orderDetail.transactionDetail.discountDetail!=null){
                if(cart.orderDetail.transactionDetail.discountDetail.discount.value!=null){
                    totalDiscount = parseFloat(totalDiscount) + parseFloat(cart.orderDetail.transactionDetail.discountDetail.discount.value);
                }
                if(cart.orderDetail.transactionDetail.discountDetail.promotionalOffer!=null){
                    totalDiscount = parseFloat(totalDiscount) + parseFloat(cart.orderDetail.transactionDetail.discountDetail.promotionalOffer);
                }
            }
            cart.orderDetail.orders.map((item) => {
                var amount = parseFloat(item.price * item.quantity);
                var discount = (this.checkEmpty(item.discountDetail) || item.discountDetail.discount == null)?0:
                    parseFloat(item.discountDetail.discount.percentage / 100);
                var promo = (this.checkEmpty(item.discountDetail) || item.discountDetail.promotionalOffer == null)?0:
                    parseFloat(item.discountDetail.promotionalOffer);
                if (item.billType == "NET_PRICE") {
                    netPriceAmount += parseFloat(amount - discount - promo);
                }else if (item.billType == "MRP") {
                    mrpAmount += parseFloat(amount - discount - promo);
                }else{
                    zeroTaxAmount += parseFloat(amount - discount - promo);
                }
                totalDiscount = parseFloat(totalDiscount) + parseFloat(discount) + parseFloat(promotionalOffer) + parseFloat(promo);
                //promotionalOffer = ;
                totalAmount += amount;
            });
            var netPerDiscount = netPriceAmount;
            var mrpPerDiscount = mrpAmount;
            if(cart.orderDetail.transactionDetail.discountDetail.discount.percentage!=null){
                netPerDiscount = netPriceAmount - (netPriceAmount * (cart.orderDetail.transactionDetail.discountDetail.discount.percentage/100));
                mrpPerDiscount = mrpAmount - (mrpAmount * (cart.orderDetail.transactionDetail.discountDetail.discount.percentage/100));
            }
            var netPriceVat = parseFloat(netPerDiscount * parseFloat(netPriceVatPercentage / 100));
            var mrpVat = parseFloat(mrpPerDiscount * parseFloat(mrpVatPercentage / 100));
            var surcharge = parseFloat((netPriceVat + mrpVat) * parseFloat(surchargePercentage / 100));
            cart.orderDetail.transactionDetail.netPriceVat.value = netPriceVat.toFixed(2);
            cart.orderDetail.transactionDetail.mrpVat.value = mrpVat.toFixed(2);
            cart.orderDetail.transactionDetail.surchargeOnTax.value = surcharge.toFixed(2);
            cart.orderDetail.transactionDetail.totalAmount = totalAmount.toFixed(2);
            cart.orderDetail.transactionDetail.taxableAmount = parseFloat(netPriceAmount + mrpAmount + zeroTaxAmount - totalDiscount);
            var paidAmount = parseFloat(cart.orderDetail.transactionDetail.taxableAmount + netPriceVat + mrpVat + surcharge);
            cart.orderDetail.transactionDetail.paidAmount = Math.round(paidAmount).toFixed(2);
            cart.orderDetail.transactionDetail.roundOffValue = parseFloat(cart.orderDetail.transactionDetail.paidAmount - paidAmount).toFixed(2);
            var netPriceVatWD = parseFloat(netPriceAmount * parseFloat(netPriceVatPercentage / 100));
            var mrpVatWD = parseFloat(mrpAmount * parseFloat(mrpVatPercentage / 100));
            var surchargeWD = parseFloat((netPriceVatWD + mrpVatWD) * parseFloat(surchargePercentage / 100));
            var taxWD = parseFloat(netPriceVatWD + mrpVatWD + surchargeWD);
            var saving = parseFloat(netPriceAmount + mrpAmount + zeroTaxAmount + taxWD) - paidAmount;
            cart.orderDetail.transactionDetail.savings = saving > 0 ? parseFloat(saving).toFixed(2) : 0;
            if(cart.orderDetail.transactionDetail.discountDetail.promotionalOffer!=null && parseFloat(cart.orderDetail.transactionDetail.discountDetail.promotionalOffer)>0){
                cart.orderDetail.transactionDetail.discountDetail.promotionalOffer = parseFloat(cart.orderDetail.transactionDetail.discountDetail.promotionalOffer) + parseFloat(promotionalOffer).toFixed(2);
            }else{
                cart.orderDetail.transactionDetail.discountDetail.promotionalOffer = parseFloat(promotionalOffer).toFixed(2);
            }
            cart.orderDetail.transactionDetail.discountDetail.totalDiscount = parseFloat(totalDiscount).toFixed(2);
            //cart.orderDetail.transactionDetail.promotionalOffer = parseFloat(promotionalOffer).toFixed(2);
        }
        return cart;*/
    }

    getNewTransactionObject() {
        return {
            totalAmount: 0,
            taxableAmount: 0,
            savings: 0,
            discountDetail: {
                discount: {
                    percentage: 0,
                    value: 0,
                    wasValueSet: false
                },
                discountReason: null,
                discountCode: null,
                totalDiscount: 0,
                promotionalOffer: 0
            },
            tax:0,
            taxes:[],
            paidAmount: 0,
            roundOffValue: 0
        };
    }

    getEmptyDiscountObj(){
        return {
            discountCode: null,
            discountReason: null,
            promotionalOffer: 0,
            discount: {
                percentage: 0,
                value: 0
            },
            totalDiscount: 0
        };
    }

    setCustomizationModal(product, cartItem){
        product = Object.assign([], product);
        var selectedDimension = product.prices[0];
        var selectedAddons = [];
        var cartEditItemId = null;
        var quantity = 1;
        var setProduct = false;
        var dcNames = {10:"Regular",11:"Full Doodh",12:"Doodh Kum",50:"Pani Kum"};
        if(product.recipesLoaded && cartItem!=null) {
            cartItem = Object.assign({}, cartItem);
            var prices = [];
            var dcProd = [10,11,12,50].indexOf(cartItem.productId)>=0?11:cartItem.productId;
            if(product.id==dcProd){
                product.prices.map((price) => {
                    if(cartItem.dimension==price.dimension){
                        if (price.recipe.ingredient.compositeProduct != null) {
                            price.recipe.ingredient.compositeProduct.details.map((detail) => {
                                detail.selectedProduct = null;
                                detail.cartProduct = null;
                                cartItem.composition.menuProducts.map((mp) => {
                                    if(mp.itemName==detail.name){
                                        detail.menuProducts.map((product) => {
                                            if(product.product.productId==mp.productId){
                                                detail.selectedProduct = product;
                                                detail.cartProduct = mp;
                                            }
                                        });
                                    }
                                });
                            });
                        }
                        price.recipe.ingredient.products.map((product) => {
                            cartItem.composition.products.map((cproduct) => {
                                product.details.map((detail) => {
                                    if(detail.product.productId==cproduct.product.productId){
                                        detail.active = true;
                                    }else{
                                        detail.active = false;
                                    }
                                })
                            });
                        });
                        price.recipe.ingredient.variants.map((variant) => {
                            cartItem.composition.variants.map((cvariant) => {
                                if(variant.product.productId==cvariant.productId){
                                    variant.details.map((detail) => {
                                        if(detail.alias==cvariant.alias){
                                            detail.active = true;
                                        }else{
                                            detail.active = false;
                                        }
                                    })
                                }
                            })
                        });
                        selectedDimension = price;
                        cartItem.composition.addons.map((addon) => {
                            selectedAddons.push(addon.product.productId);
                        });
                    }
                    prices.push(price);
                    product.prices = prices;
                    product.id = cartItem.productId;
                    if([10,11,12,50].indexOf(cartItem.productId)>=0){
                        product.name = dcNames[cartItem.productId];
                    }
                });
            }else{
                setProduct = true;
            }

        }
        if (product.recipesLoaded && (cartItem==null || setProduct)) {
            selectedDimension = product.prices[0];
            selectedAddons = [];
            quantity = 1;
            product.prices.map((price) => {
                if (price.recipe.ingredient.compositeProduct != null) {
                    price.recipe.ingredient.compositeProduct.details.map((detail) => {
                        detail.selectedProduct = null;
                        detail.cartProduct = null;
                    });
                }
                price.recipe.ingredient.products.map((product) => {
                    product.details.map((detail) => {
                        detail.active = detail.defaultSetting;
                    })
                });
                price.recipe.ingredient.variants.map((variant) => {
                    variant.details.map((detail) => {
                        detail.active = detail.defaultSetting;
                    })
                });
            });
        }
        if(cartItem!=null){
            quantity = cartItem.quantity;
            cartEditItemId = cartItem.itemId;
        }
        return {product:product, selectedDimension:selectedDimension, selectedAddons:selectedAddons, quantity:quantity, cartEditItemId:cartEditItemId};
    }

    getProductName(productId, productName){
        var dcIds = [10,11,12,50];
        var dcNames = {10:"",11:" - Full Doodh",12:" - Doodh Kum",50:" - Pani Kum"};
        return dcIds.indexOf(productId)>=0?"Desi Chai"+dcNames[productId]:productName;
    }

    getNewCartObj(){
        let isParcel = false;
        if(!appUtil.checkEmpty(StorageUtils.getLocalityMetadata())
            && !appUtil.checkEmpty(StorageUtils.getLocalityMetadata().criteria)){
            console.log("The order type is::: " + StorageUtils.getLocalityMetadata().criteria);
            isParcel = (StorageUtils.getLocalityMetadata().criteria == "DELIVERY");
        }
        return {
            cartId: null,
            deviceId: StorageUtils.getDeviceId(),
            customerId: StorageUtils.getCustomerId(),
            sessionId: StorageUtils.getSessionId(),
            orderDetail: {
                customerId: 1,
                hasParcel: isParcel,
                /*source : this.getSourceFromCriteria("TAKE_AWAY"), //done for kiosk only*/
                source : appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria),
                orderStatus: "INITIATED",
                orders: [],
                enquiryItems: [],
                transactionDetail: this.getNewTransactionObject(),
                settlements: [],
                settlementType: "DEBIT",
                unitId: null,
                unitName: null,
                terminalId: null,
                offerCode: null,
                orderRemark: null,
                deliveryAddress: null,
                customerName:StorageUtils.getCustomerDetail()!=null?StorageUtils.getCustomerDetail().name:null,
                containsSignupOffer: null,
                tempCode: null,
                metadataList: [],
                webCustomerId:StorageUtils.getCustomerId()
            },
            currentStateId:null,
            cartStatus: "CREATED"
        }
    }

    sendOrderForTracking(orderId){
        var cart = StorageUtils.getCartDetail();
        var products = [];
        var packaging = 0;

        var deliveryCharge = 0;
        _.map(cart.orderDetail.orders, (orderItem)=>{
            if(orderItem.productId!=1044 && orderItem.productId!=1043){
                products.push({
                    name:orderItem.productName,
                    id:orderItem.productId,
                    brand:"Chaayos",
                    category:this.category[orderItem.productCategory.id],
                    price:orderItem.price,
                    quantity:orderItem.quantity,
                    dimension1:orderItem.dimension
                });
            }else{
                if(orderItem.productId==1044){
                    deliveryCharge = orderItem.price;
                }
                if(orderItem.productId==1043){
                    packaging = orderItem.price;
                }
            }
        });
        var tax = parseFloat(cart.orderDetail.transactionDetail.netPriceVat.value)+parseFloat(cart.orderDetail.transactionDetail.mrpVat.value)+
            parseFloat(cart.orderDetail.transactionDetail.surchargeOnTax.value);
        window.dataLayer = window.dataLayer || [];
        var ecommerce = {
            purchase: {
                actionField: {
                    id:orderId,
                    revenue:cart.orderDetail.transactionDetail.paidAmount,
                    tax:tax.toFixed(2),
                    shipping: deliveryCharge,
                    coupon:cart.orderDetail.offerCode
                },
                products:products
            }
        }
        dataLayer.push({
            ecommerce: ecommerce,
            event:"SuccessOrder"
        });
        var criteria = StorageUtils.getLocalityMetadata().criteria;
        var source = "";
        (criteria == "DELIVERY")?source="Delivery":null;
        (criteria == "TAKE_AWAY")?source="Takeaway":null;
        (criteria == "DINE_IN")?source="DineIn":null;
        var orderDetail = {
            odt: source,
            uid: cart.orderDetail.unitId,
            uin: cart.orderDetail.unitName,
            cid: cart.customerId,
            odid: orderId,
            pmt : cart.orderDetail.transactionDetail.paidAmount,
            dl : deliveryCharge,
            pkg : packaging
        }
        dataLayer.push({
            orderDetail: orderDetail,
            event:"FetchOrderDetail"
        });
        return {purchase:ecommerce.purchase,orderDetail:orderDetail}
    }

    getCategory(id){
        return this.category[id];
    }

    getParameterByName(name, url) {
        if (!url) {
            url = window.location.href;
        }
        name = name.replace(/[\[\]]/g, "\\$&");
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    }

    formatTag(tag){
        return tag.replace("_"," ").toLowerCase();
    }

    collectionHas(a, b) { //helper function (see below)
        for (var i = 0, len = a.length; i < len; i++) {
            if (a[i] == b) return true;
        }
        return false;
    }

    findParentBySelector(elm, selector) {
        var all = document.querySelectorAll(selector);
        var cur = elm.parentNode;
        while (cur && !this.collectionHas(all, cur)) { //keep going up until you find a match
            cur = cur.parentNode; //go up
        }
        return cur; //will return null if not found
    }

    getSourceFromCriteria(criteria){
        var source = "";
        (criteria == "DELIVERY")?source="COD":null;
        (criteria == "TAKE_AWAY")?source="TAKE_AWAY":null;
        (criteria == "DINE_IN")?source="CAFE":null;
        return source;
    }

    getOrderStatusArray(status, orderSource){
        var sourceMidStatusMap = {
            COD:["Accepted","Prepared","In Transit"],
            TAKE_AWAY:["Accepted","Preparing","Prepared"],
            CAFE:["Accepted","Preparing","Prepared"]
        };
        var sourceFinalStatusMap = {
            COD:{DELIVERED:"Delivered",CANCELLED_REQUESTED:"Cancelled",CANCELLED:"Cancelled"},
            TAKE_AWAY:{SETTLED:"Picked",CANCELLED_REQUESTED:"Cancelled",CANCELLED:"Cancelled"},
            CAFE:{SETTLED:"Picked",CANCELLED_REQUESTED:"Cancelled",CANCELLED:"Cancelled"}
        };
        var statusIndexMap = {
            COD:{"CREATED":0,"PROCESSING":0,"READY_TO_DISPATCH":1,"SETTLED":2,"DELIVERED":3,"CANCELLED_REQUESTED":3,"CANCELLED":3},
            TAKE_AWAY:{"CREATED":0,"PROCESSING":1,"READY_TO_DISPATCH":2,"SETTLED":3,"CANCELLED_REQUESTED":3,"CANCELLED":3},
            CAFE:{"CREATED":0,"PROCESSING":1,"READY_TO_DISPATCH":2,"SETTLED":3,"CANCELLED_REQUESTED":3,"CANCELLED":3}
        };
        var statusImageMap = {
            "Accepted":"accepted",
            "Prepared":"delivered",
            "In Transit":"intransit",
            "Delivered":"delivered",
            "Picked":"delivered",
            "Preparing":"prepared",
            "Cancelled":"cancelled"
        };
        var returnMap = [];
        sourceMidStatusMap[orderSource].map((stat, index) => {
            returnMap.push({status:stat,active:statusIndexMap[orderSource][status]>=index, image:statusImageMap[stat]});
        });
        if(["CANCELLED_REQUESTED","CANCELLED"].indexOf(status)>=0){
            returnMap.push({status:sourceFinalStatusMap[orderSource][status],active:true,
                image:statusImageMap[sourceFinalStatusMap[orderSource][status]]});
        }else{
            if(orderSource=="COD"){
                returnMap.push({status:sourceFinalStatusMap[orderSource]["DELIVERED"],active:status=="DELIVERED",
                    image:statusImageMap[sourceFinalStatusMap[orderSource]["DELIVERED"]]});
            }else{
                returnMap.push({status:sourceFinalStatusMap[orderSource]["SETTLED"],active:status=="SETTLED",
                    image:statusImageMap[sourceFinalStatusMap[orderSource]["SETTLED"]]});
            }
        }
        return returnMap;
    }

    getOrderDisplayStatus(status, orderSource){
        var statusDisplayMap = {
            COD:{CREATED:"Accepted",PROCESSING:"Accepted",READY_TO_DISPATCH:"Prepared",SETTLED:"In transit",DELIVERED:"Delivered",CANCELLED_REQUESTED:"Cancelled",CANCELLED:"Cancelled"},
            TAKE_AWAY:{CREATED:"Accepted",PROCESSING:"Preparing",READY_TO_DISPATCH:"Prepared",SETTLED:"Picked",CANCELLED_REQUESTED:"Cancelled",CANCELLED:"Cancelled"},
            CAFE:{CREATED:"Accepted",PROCESSING:"Preparing",READY_TO_DISPATCH:"Prepared",SETTLED:"Picked",CANCELLED_REQUESTED:"Cancelled",CANCELLED:"Cancelled"}
        };
        return statusDisplayMap[orderSource][status];
    }

    isOrderCompleted(status, source){
        var ret = true;
        if((source=="COD" && (status!="DELIVERED" && status!="CANCELLED" && status!="CANCELLED_REQUESTED")) ||
            ((source=="CAFE" || source=="TAKE_AWAY") && (status!="SETTLED" && status!="CANCELLED" && status!="CANCELLED_REQUESTED"))){
            ret = false;
        }
        return ret;
    }

    setTaxes(taxes){
        this.taxes = taxes;
    }

    getTaxes(){
        return this.taxes;
    }

    setInclusiveTaxPrices(unitData, isInterState) {
        var taxMap = [];
        unitData.taxes.map(function (tax) {
            taxMap[tax.taxCode] = tax;
        });
        unitData.products.map(function (product) {
            product.prices.map(function (price) {
                if(product.billType!="ZERO_TAX" && product.taxCode != "COMBO"){
                    var totalTax = 0;
                    var taxObj = taxMap[product.taxCode];
                    var cgst = parseFloat(taxObj.state.cgst/100)*price.price;
                    var sgst = parseFloat(taxObj.state.sgst/100)*price.price;
                    var igst = parseFloat(taxObj.state.igst/100)*price.price;
                    if(isInterState){
                        totalTax = cgst + igst;
                    }else{
                        totalTax = cgst + sgst;
                    }
                    var otherTax = 0;
                    if(taxObj.others!=null && taxObj.others.length>0){
                        taxObj.others.map(function (oTax) {
                            if(oTax.applicability == "ON_SALE"){
                                otherTax = parseFloat(otherTax) + parseFloat(parseFloat(oTax.tax/100)*price.price);
                            }
                            if(oTax.applicability == "ON_TAX"){
                                otherTax = parseFloat(otherTax) + parseFloat(parseFloat(oTax.tax/100)*totalTax);
                            }
                        });
                    }
                    totalTax = parseFloat(parseFloat(totalTax) + parseFloat(otherTax)).toFixed(4);
                    price.priceInclusiveTax = price.price + parseFloat(totalTax);
                }else{
                    price.priceInclusiveTax = price.price;
                }
            });
        });
    }

    addTime(time, minutes){
        var date = new Date();
        if(time != null) {
            date = new Date(time);
        }
        if(minutes != null && minutes > 0) {
            date.setMinutes(date.getMinutes() + minutes + 1);
        }
        return date;
    }

    formatDate(date, format) {
        var time = new Date(date);
        var yyyy = time.getFullYear();
        var M = time.getMonth() + 1;
        var d = time.getDate();
        var MM = M;
        var dd = d;
        var hh = time.getHours();
        var mm = time.getMinutes();
        var ss = time.getSeconds();
        if (M < 10) {
            MM = "0" + M;
        }
        if (d < 10) {
            dd = "0" + d;
        }
        format = format.replace("yyyy", yyyy);
        format = format.replace("MM", MM);
        format = format.replace("dd", dd);
        format = format.replace("hh", hh);
        format = format.replace("mm", mm);
        format = format.replace("ss", ss);
        return format;
    }


}


const appUtil = new AppUtil();
export default appUtil;