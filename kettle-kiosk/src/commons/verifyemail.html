<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chaayos - Loyaltea</title>
    <meta property="description" content="Verify your email with <PERSON>ay<PERSON> and earn 10 loyaltea points." />
    <link href="https://fonts.googleapis.com/css?family=Varela+Round" rel="stylesheet" />
    <style type="text/css">
        body{
            margin: 0;
            padding: 0;
            text-align: center;
            background: url("../img/bgDoodle.png") repeat;
        }
        .headerWrapper {
            background: #5e7e47;
            color: #fff;
            height: 50px;
            text-align: center;
        }
        .headerWrapper img{
            max-height: 40px;
            margin: 5px 10px;
        }
        #general img.banner, #couponWrapper img.banner{
            width: 100%;
            max-width: 1000px;
        }
        #main-wrapper{
            margin: 4%;
            border: 1px solid #ddd;
            padding: 5%;
            background-color: #fff;
            font-size: 20px;
            font-family: "Varela Round";
            line-height: 35px;
            box-shadow: 0 0 2px 0 #ddd;
        }
    </style>
</head>
<body>
<div class="headerWrapper">
    <img src="/chaayos/logo/chaayosLogo.png"/>
</div>
<div id="main-wrapper">
    <div id="welcome" style="display: block;">
        <div>
            <img src="/img/verify.gif" height="160px;">
        </div>
        <span>Please wait while we verify your number...</span>
    </div>
    <div id="thanks" style="display: none;">
        <div>
            <img src="/img/verify_thanks.png" height="160px;">
        </div>
        <span>
            Thank you for verifying your email.<br>
            You just earned yourself 60 points, which will get you a free desi chai on your next visit.
        </span>
    </div>
    <div id="sorry" style="display: none;">
        <div>
            <img src="/img/verify_sorry.png" height="160px;">
        </div>
        <span>
            Your email has already been authenticated.<br>
            If you want to report an issue, write to us at <a href="mailto:<EMAIL>"><EMAIL></a>
        </span>
    </div>
    <div id="error" style="display: none;">
        Invalid link! Please check the link provided to you.<br>
        If you want to report an issue, write to us at <a href="mailto:<EMAIL>"><EMAIL></a>
    </div>
</div>
</body>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
    $(function () {
        try{
            var token = window.location.href.slice(window.location.href.indexOf('?') + 1).split("=")[1];
            $.ajax({
                type: "GET",
                url: "/validate?token="+token,
                success: function(response) {
                    response = $.parseJSON(response);
                    $("#welcome").hide();
                    if (response){
                        $("#thanks").show();
                        $("#sorry").hide();
                    }else{
                        $("#thanks").hide();
                        $("#sorry").show();
                    }
                },
                error:function (err) {
                    console.log("Error while submitting :::: "+ err);
                }
            });
        }catch {
            $("#welcome, #thanks, #sorry").hide();
            $("#error").show();
        }
    });
</script>
</html>