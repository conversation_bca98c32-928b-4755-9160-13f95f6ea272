/************************************************************************/
/*                              common styles                           */
/************************************************************************/

/**************** generic styles *****************/
* {
    -webkit-text-size-adjust: none;
    margin: 0;
    padding: 0;
    outline: none;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent
}

:not(input):not(textarea):not(button) {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background-color: #dcdcdc;
}

::-webkit-scrollbar-thumb {
    background-color: #a9a9a9;
}

a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, canvas, caption, center, cite, code, dd, del,
details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header,
html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section,
small, span, strike, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, tt, u, ul, var, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    box-sizing: border-box;
}

a {
    text-decoration: none;
    color: #333
}

a:focus {
    outline: 0;
}

a:active {
    background: transparent
}

html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    -webkit-transition: .2s linear;
    transition: .2s linear;
    font-family: 'Varela Round', sans-serif;
    /* background: url("../img/bgDoodle.png") top left repeat;*/
    /*background: #fbf8eb;*/
    font-size: 12px;
    -webkit-overflow-scrolling: touch;
    overflow: hidden;
    font-smoothing: antialiased;
}

input {
    outline: none;
}

.align-right {
    text-align: right;
}

.right {
    float: right;
}

.left {
    float: left;
}

.text-center {
    text-align: center;
}

.ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.clear {
    clear: both;
}

.rel {
    position: relative;
}

.text-right {
    text-align: right;
}

.btn {
    text-align: center;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    padding: 0 25px;
    height: 50px;
    line-height: 50px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 26px;
    cursor: pointer;
    margin-right: 10px;
}

.btn.small {
    height: 45px;
    line-height: 45px;
    font-size: 14px;
}

.btn-default {
    border: #5e7e47 1px solid;
    background: #fff;
    color: #5e7e47;
}

.btn-primary {
    background: #5e7e47;
    color: #fff;
    border: #5e7e47 1px solid;
    margin: 10px 0;
    min-width: 90px;
}

.alert {
    margin: 10px;
    padding: 10px;
    background: #ddd;
    border: #ccc 1px solid;
    border-radius: 5px;
    font-size: 18px;
}

.alert.error {
    background: rgba(218, 105, 92, 0.79);
    border: rgba(218, 65, 50, 0.79) 1px solid;
}

.alert.info {
    background: rgba(113, 215, 218, 0.79);
    border: rgba(77, 214, 218, 0.79) 1px solid;
}

.ptop50 {
    padding-top: 50px;
}

.pairBtnWrapper {
    text-align: center;
    position: relative;
    padding: 0;
    margin: 50px 0px 10px 0px;
}

.pairBtnWrapper .leftBtn {
    position: absolute;
    top: 0;
    left: 10px;
    width: 200px;
}

.pairBtnWrapper .mainBtn {
    margin-left: 240px;
    margin-right: 10px;
}

.desktopPageContainer {
    width: 1024px;
    margin: auto;
    margin-top: 10px;
    position: relative;
    background: #fff;
    min-height: 400px;
    padding: 0 15px 100px 15px;
    -webkit-box-shadow: 0 0 2px 0 #d3cbb8;
    -moz-box-shadow: 0 0 2px 0 #d3cbb8;
    box-shadow: 0 0 2px 0 #d3cbb8;
}

.desktopPageContainer .desktopPageHead {
    font-size: 24px;
    text-align: center;
    color: #292826;
    padding: 15px 0;
    margin: 180px 0 0 350px;
    position: absolute;
}

.noscroll {
    overflow: hidden !important;
}

/************** radio checkbox styling *****************/
input[type=checkbox]:not(old),
input[type=radio   ]:not(old) {
    width: 15px;
    margin: 0;
    padding: 0;
    font-size: 12px;
    opacity: 0;
}

input[type=checkbox]:not(old) + label,
input[type=radio   ]:not(old) + label {
    display: inline-block;
    margin-left: -25px;
    line-height: 27px;
    vertical-align: top;
}

input[type=checkbox]:not(old) + label > span,
input[type=radio   ]:not(old) + label > span {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin: 4px 10px;
    border: 2px solid rgb(192, 192, 192);
    vertical-align: bottom;
    -webkit-transition: all 0.25s linear;
}

input[type=radio   ]:not(old) + label > span {
    border-radius: 50%;
}

input[type=checkbox]:not(old):checked + label > span,
input[type=radio   ]:not(old):checked + label > span {
    border: #5e7e47 2px solid;
    -webkit-transition: all 0.25s linear;
}

input[type=checkbox]:not(old):checked + label > span:before {
    display: block;
    width: 1em;
    color: #5e7e47;
    font-size: 21px;
    line-height: 10px;
    text-shadow: 0 0 0.0714em #5e7e47;
    font-weight: bold;
    -webkit-transition: all 0.25s linear;
}

input[type=radio]:not(old):checked + label > span > span {
    display: block;
    width: 10px;
    height: 10px;
    margin: 3px;
    border: 1px solid rgb(115, 153, 77);
    border-radius: 50%;
    background: #5e7e47;
}

#appRoot {
    min-width: 1024px;
}

#appRoot > div > div {
    box-shadow: none !important;
}

.headLine {
    font-weight: bold;
    font-size: 24px;
    text-transform: uppercase;
    margin: 30px 0;
}

/************************ css loader ********************************/
.load8.loader, .load8.loader:after {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    text-align: center;
}

.load8.loader {
    margin: 35px auto;
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 2px solid rgba(189, 189, 189, 0.2);
    border-right: 2px solid rgba(189, 189, 189, 0.2);
    border-bottom: 2px solid rgba(189, 189, 189, 0.2);
    border-left: 2px solid #065904;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 .6s infinite linear;
    animation: load8 .6s infinite linear;
}

@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/************** main routing layout styles ******************/
.internetErrorContainer {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center;
    background: #fff;
    vertical-align: middle;
    -webkit-transition: all 0.25s linear;
    transition: all 0.25s linear;
}

.internetErrorContainer > img {
    height: 250px;
    margin: 50px 0 20px 0;
}

.internetErrorContainer .msg {
    font-size: 30px;
    padding: 0 0 20px 0;
}

/******************************sidebar styles****************************/
.sidebarWrapper {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 9999;
    pointer-events: auto;
}

.sidebarShadow {
    position: absolute;
    height: 100%;
    width: 100%;
    opacity: 0;
    cursor: pointer;
    will-change: opacity;
    background: rgba(0, 0, 0, 0.6);
}

.sidebarContentWrapper {
    background: #fff;
    height: 100%;
    width: 270px;
    will-change: transform;
    box-shadow: 3px 0 8px 1px rgba(0, 0, 0, .4);
    -webkit-transform: translate3d(-102%, 0, 0);
    transform: translate3d(-102%, 0, 0);
    transition: .2s linear;
    -webkit-transition: .2s linear;
}

.sidebarContentWrapper .sideBarMenu {
    padding: 12px 20px;
    font-size: 18px;
    cursor: pointer;
}

.sidebarContentWrapper .sideBarMenu.head {
    padding: 17px 10px;
}

.sidebarContentWrapper .sideBarMenu.head .sidebarLogo {
    width: 150px;
}

.sidebarContentWrapper .sideBarMenu.head .listIcon {
    margin: -5px 13px 0 20px;
    height: 35px;
    display: inline-block;
    width: 35px;
    text-align: center;
    float: left;
    padding: 6px 0 0 0;
}

.sidebarContentWrapper .sideBarMenu .listIcon {
    margin: 0 13px -6px 0;
    height: 35px;
    display: inline-block;
    width: 35px;
    text-align: center;
}

.sidebarContentWrapper .sideBarMenu .listIcon img {
    margin: 1px 0 -6px 0;
    height: 27px;
}

.sidebarContentWrapper .sideBarMenu {
    color: #62635e;
}

.sidebarContentWrapper .static .sideBarMenu {
    display: block;
    color: #a6a6a0;
    background: #fff;
}

.sidebarContentWrapper .sideBarMenu a {
    text-decoration: none;
    display: block;
    color: #b3b3b3;
}

.sidebarContentWrapper .sidebarSeparator {
    height: 1px;
    background: #e4e4df;
    margin: 20px 0 20px 20px;
}

/**************** utility layout styles ***************/
.popupWrapper {
    position: fixed;
    top: -2px;
    left: 0;
    right: 0;
    height: 0;
    -webkit-transition: .2s linear;
    transition: .2s linear;
    font-size: 16px;
}

.popupWrapper.active {
    top: 0;
    padding: 15px 10px;
    height: auto;
    z-index: 9;
}

.popupWrapper.error {
    background: #b55a5a;
}

.popupWrapper.info {
    background: rgba(244, 179, 64, 1);
}

.fullPageLoader {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-align: center;
    z-index: 99;
}

.fullPageLoader .loaderWrapper {
    background: #fff;
    color: #000;
    border-radius: 3px;
    max-width: 600px;
    margin: auto;
    margin-top: 350px;
    padding: 15px;
}

.fullPageLoader .loaderWrapper .promptMessage {
    min-height: 50px;
    font-size: 18px;
}

.fullPageLoader .loaderWrapper .loaderMessage {
    margin: 0 30px 10px 30px;
    font-size: 21px;
}

.loaderMessage {
    text-align: center;
}

.lsMsgWrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #5e7e47;
    display: none;
    color: #fff;
    text-align: center;
    z-index: 9999999;
}

.lsMsgWrapper.active {
    display: flex;
    pointer-events: auto;
}

.lsMsgContainer {
    width: 80%;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    margin: auto;
}

.lsImgContainer {
    background-image: url(../img/rotate.png);
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    width: 30%;
    padding-bottom: 25.5%;
}

.lsMsg {
    font-size: 20px;
    margin-bottom: 10px;
    margin-top: 30px;
}

.lsDesc {
    font-size: 14px;
}

/******************************header styles*****************************/
.headerWrapperContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1;
    will-change: transform;
    transition: .2s linear;
    -webkit-transition: .2s linear;
}

.headerWrapperContainer.small {
    transform: translateY(-60px);
}

.headerWrapper {
    background: #5e7e47;
    color: #fff;
    height: 70px;
    /*text-align: center;*/
}

.headerBtn {
    color: #fff;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    font-size: 27px;
    height: 60px;
    line-height: 60px;
    width: 60px;
}

.headerBtn .ion-navicon {
    color: #fff;
}

.headerLogo {
    font-size: 24px;
    display: inline-block;
    margin-top: 5px;
}

.headerLogo img {
    height: 35px;
    margin: 10px 0 0 50px;
}

.headerWrapper .localityWrapper, .headerWrapper .outletWrapper {
    font-size: 23px;
    padding: 10px;
    border-radius: 3px;
    position: relative;
    background: rgba(0, 0, 0, 0.2);
    margin: 13px 50px 0 0;
}

.mainContent.active .headerWrapper .localityWrapper, .mainContent.active .headerWrapper .outletWrapper {
    display: block;
    z-index: 1;
    background: #7a9f5f;
}

.headerWrapper .localityWrapper .tagLine, .headerWrapper .outletWrapper .tagLine {
    text-transform: uppercase;
    font-size: 9px;
    margin: 0px 0 3px 0;
}

.headerWrapper .localityWrapper .downIcon, .headerWrapper .outletWrapper .downIcon {
    width: 17px;
    margin: 0 0 -2px 4px;
}

.headerWrapper .cartSizeLabel {
    position: absolute;
    background: #f0af3b;
    color: #5e7e47;
    line-height: normal;
    border: solid 1px #f9d99e;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
    top: 8px;
    left: 8px;
    font-size: 12px;
    padding: 3px;
    border-radius: 20px;
    min-width: 22px;
}

.pageContainer {
    margin-top: 70px;
    position: relative;
}

.pageContainer .pageHead {
    font-size: 24px;
    text-align: center;
    color: #292826;
    padding: 20px 0;
}

/******************* banner layout styles ***************************/
.imgSlide {
    height: 325px !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    color: #fff;
}

.bannerAreaWrapper {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    color: #fff;
}

.siteTitle {
    text-align: center;
}

.slick-slider {
    overflow: hidden;
}

/*******************  home layout styles *************************/
.locationSelectionWrapper {
    height: 359px;
    -webkit-transition: all .2s ease-out;
    -o-transition: all .2s ease-out;
    transition: all .2s ease-out;
    -webkit-transform: translate3d(0, -359px, 0);
    transform: translate3d(0, -359px, 0);
    font-size: 12px;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: #f4f4f4;
    background-size: cover;
    position: absolute;
    z-index: 9;
}

.locationSelectionWrapper.active {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    box-shadow: #000 0 0 5px 0;
}

.locationSelectionWrapper.active .chevronDown {
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 15px solid #f4f4f4;
    display: none;
    position: absolute;
    bottom: -9px;
    left: 205px;
}

.locationSelectionWrapper.active .chevronDown {
    display: block;
}

.mainContent {
    -webkit-transition: all .2s ease-out;
    -o-transition: all .2s ease-out;
    transition: all .2s ease-out;
    /*-webkit-transform: translate3d(0,0,0);
    transform: translate3d(0,0,0);*/
    /*position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;*/
}

.mainContent.active {
    -webkit-transform: translateY(359px);
    -moz-transform: translateY(359px);
    -ms-transform: translateY(359px);
    -o-transform: translateY(359px);
    transform: translateY(359px);
}

.locationWrapperOverlay {
    display: none;
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
}

.locationWrapperOverlay.active {
    display: block;
}

.closeLocationWrapper {
    position: absolute;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    font-size: 24px;
    top: 0;
    right: 0;
    cursor: pointer;
}

.actionAreaContainer {
    opacity: 0;
    margin-top: 20px;
    transition: .2s ease-in;
    -webkit-transition: .2s ease-in;
}

.actionAreaContainer.active {
    opacity: 1;
}

.actionAreaHeader {
    text-align: center;
    text-transform: uppercase;
}

.actionAreaContainer .actionInputWrapper {
    max-width: 400px;
    margin: 0 auto;
}

.actionAreaContainer .headline {
    text-align: center;
    font-size: 18px;
    margin-bottom: 20px;
}

.actionAreaContainer .actionInputWrapper .radioLabels {
    /*width: 50%;*/
    width: 33.3%;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    color: #62635e;
}

.actionAreaContainer .citySelector {
    text-align: center;
    max-width: 80%;
    margin: 25px auto;
}

.actionAreaContainer .cityBtn {
    color: #aaa;
    display: inline-block;
    padding: 9px 12px;
    border-radius: 20px;
    margin: 0 10px 8px 0;
    font-size: 14px;
    min-width: 85px;
    background: #fff;
    cursor: pointer;
}

.actionAreaContainer .cityBtn.active {
    border: #5e7e47 1px solid;
    color: #5e7e47;
    background: #fff;
}

.actionAreaContainer .localitySelector, .actionAreaContainer .outletSelector {
    background: #fff;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
    opacity: 0;
    transition: .2s linear;
    -webkit-transition: .2s linear;
    max-width: 500px;
    margin: 0 auto;
    border-radius: 4px;
    height: 0;
    line-height: 0;
}

.actionAreaContainer .localitySelector.active, .actionAreaContainer .outletSelector.active {
    opacity: 1;
    height: 50px;
    line-height: 50px;
}

.actionAreaContainer .Select-control {
    height: 50px;
    line-height: 50px;
    border: none;
}

.actionAreaContainer .Select-input {
    height: 50px;
    vertical-align: top;
}

.actionAreaContainer .Select-menu-outer {
    border: none;
}

.actionAreaContainer .Select-menu {
    overflow-y: auto;
    /*min-height: 700px;
    margin: -20px;
    margin-top: 20px;*/
}

.actionAreaContainer .Select--single > .Select-control .Select-value, .actionAreaContainer .Select-placeholder {
    line-height: 50px;
}

.actionAreaContainer .Select-input > input {
    line-height: 25px;
    padding: 14px 0 12px;
}

.actionAreaContainer .outletListItem {
    border-bottom: #ddd 1px solid;
    background: #fff;
    color: #000 !important;
    height: 50px;
    line-height: 50px;

}

.actionAreaContainer .errorMessage {
    border: red 1px solid;
    display: none;
}

/********************** outlet menu layout ***********************/
.navTabContainerWrapper {
    /*background: #587542;*/
    background: #f0af3b;
    box-shadow: 0 2px 3px -1px #bcbcb8;
    margin-top: -3px;
    width: 500px;
}

.navTabContainer {
    max-width: 1024px;
    margin: 0 auto;
    /*background: #587542;*/
    background: #f0af3b;
    padding: 14px 0;
    opacity: 0;
    position: relative;
    white-space: nowrap;
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
    -webkit-user-select: none;
    -webkit-overflow-scrolling: touch;
    transition: .5s linear;
    -webkit-transition: .5s linear;
}

.navTabContainer.active {
    opacity: 1;
}

.navTabContainer .slider {
    height: 4px;
    position: absolute;
    bottom: 0;
    left: 0;
    /*background: #f0af3b;*/
    background: #587542;
    border-radius: 2px;
    -webkit-transition: .6s;
    transition: .6s;
    -webkit-transform: translateX(4px);
    -ms-transform: translateX(4px);
    transform: translateX(4px);
    will-change: transform;
}

.navTabContainer::-webkit-scrollbar {
    display: none;
}

.navTabContainer .navTab {
    padding: 9px 20px;
    font-size: 16px;
    line-height: 18px;
    margin: 0;
    color: #fff;
    cursor: pointer;
    text-transform: uppercase;
    text-align: center;
    -webkit-transition: .2s linear;
    transition: .2s linear;
    width: 149px;
    display: block;
}

.navTabContainer .navTab:first-child {
    margin-left: 0;
}

.menuWrapper {
    opacity: 0;
    -webkit-transition: .2s linear;
    transition: .2s linear;
}

.menuWrapper.active {
    opacity: 1;
}

.unitLoadingError {
    margin-top: 150px;
    text-align: center;
    font-size: 18px;
}

.unitLoadingError img {
    height: 100px;
    margin-bottom: 20px;
}

.fetchingData {
    position: absolute;
    width: 100%;
    top: 150px;
    text-align: center;
    opacity: 0;
    margin-top: 150px;
}

.fetchingData.active {
    opacity: 1;
    height: auto;
    margin-top: 150px;
    z-index: 111;
}

.fetchingData .loadingMessage {
    padding: 30px 20px 0 20px;
    font-size: 18px;
}

.outletMenuWrapper {
    position: absolute;
    width: 100%;
    top: 116px;
    bottom: 0;
    overflow: auto;
    margin-left: 150px;
    padding-top: 140px;
    background: rgb(243, 243, 243);
}

.outletMenuWrapper.cart {
    /*position: absolute;
    width: 100%;
    top: 116px;
    overflow: auto;
    padding-top: 140px;
    background: rgb(243, 243, 243);*/
    bottom: 410px;
}

.menuContainer {
    margin: 0 auto;
    width: 1024px;
    z-index: 99;
    opacity: 0;
    transition: .5s linear;
    -webkit-transition: .2s linear;
    background: rgb(243, 243, 243);
    padding: 0 10px;
}

.menuContainer.active {
    opacity: 1;
}

.menuContainer .menu {
    margin-top: -150px;
    padding-top: 157px;
    padding-bottom: 10px;
}

.menuContainer .menu .categoryHeader {
    color: #577c3a;
    text-transform: capitalize;
    font-size: 39px;
    font-weight: 700;
    padding: 15px 15px 15px 30px;
    margin-right: 10px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 10px;
    /*box-shadow: 3px 3px #e0d7d7;*/
    line-height: 45px;
}

.staticHead {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 999;
    background: #5e7e47;
    will-change: transform;
}

.productsWrapper {
}

.productsWrapper .productContainer {
    /*box-shadow: 3px 3px #e0d7d7;*/
    vertical-align: top;
    margin: 10px 10px 20px 10px;
    width: 278px;
    display: inline-block;
    border-radius: 10px;
    cursor: pointer;
}

.productsWrapper .productContainer .productImage {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border: #fff 5px solid;
}

.productsWrapper .productContainer .productImage .stockOutWrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    line-height: 200px;
    font-size: 24px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.productsWrapper .productContainer .productImage .stockOutWrapper .stockOut {
    display: inline-block;
    background: #b55a5a;
    color: #fff;
    text-transform: uppercase;
    line-height: normal;
    position: absolute;
    padding: 2px 10px;
    border-radius: 10px;
    font-size: 12px;
    bottom: 10px;
    left: 10px;
}

.productsWrapper .productContainer .productImage .tagWrapper {
    position: absolute;
    top: 10px;
    left: 10px;
    color: #fff;
    font-size: 9px;
    text-transform: uppercase;
}

.productsWrapper .productContainer .productImage .tagWrapper .tagName {
    background: #5e7e47;
    border-top: solid 1px rgba(255, 255, 255, 1);
    border-right: solid 2px rgba(255, 255, 255, 1);
    border-bottom: solid 1px rgba(255, 255, 255, 1);
    border-left: solid 2px rgba(255, 255, 255, 1);
    padding: 3px 6px 1px 6px;
    border-radius: 20px;
    margin: 0 5px 5px 0;
}

.productsWrapper .productContainer .productTitleWrapper, .productsWrapper .productContainer .productDescriptionWrapper {
    position: relative;
}

.productsWrapper .productContainer .productTitle {
    /*margin-right: 76px;*/
    padding: 5px 10px;
    font-size: 18px;
    line-height: 20px;
    height: 40px;
    text-transform: capitalize;
    color: #292825;
    font-weight: bold;
    margin-bottom: 5px;
}

.productsWrapper .productContainer .productPrice {
    /*top: 0;
    right: 10px;*/
    /*width: 56px;*/
    padding: 5px 0;
    font-size: 28px;
    line-height: 18px;
    /*position: absolute;*/
    text-transform: capitalize;
    /*text-align: center;*/
    color: #5e7e47;
    font-weight: bold;
}

.productsWrapper .productContainer .productDetail {
    background: #fff;
    clear: both;
    padding: 7px 0 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.productsWrapper .productContainer .productDetail .productDescription {
    /*margin-right: 122px;*/
    width: 150px;
    padding: 5px 10px;
    min-height: 58px;
    font-size: 12px;
    line-height: 16px;
    color: #62635e;
}

.productsWrapper .productContainer .productDetail .recipeLoader {
    position: absolute;
    top: 5px;
    right: 10px;
    width: 56px;
    height: 32px;
    line-height: 32px;
}

.productsWrapper .productContainer .productDetail .addProductBtn.disabled {
    background-color: #ddd;
    color: #848484;
    border: none;
    line-height: 43px;
}

.productsWrapper .productContainer .productDetail .addProductBtn {
    top: 5px;
    right: 10px;
    width: 150px;
    height: 39px;
    line-height: 36px;
    border-radius: 6px;
    font-size: 22px;
    font-weight: 700;
    position: absolute;
    background: #5e7e47;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    border: solid 2px #5e7e47;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
    cursor: pointer;
}

.productsWrapper .vegNonVegIcon {
    height: 15px;
    margin: 0 5px -2px 0px;
}

.productsWrapper .productContainer .rupeeIcon {
    height: 21px;
    margin: 0 3px -1px 0;
}

.productsWrapper .sizeDescription {
    padding: 0 3px;
}

.productsWrapper .productContainer .customizationWrapper {
    position: relative;
    padding: 0 10px 10px;
}

.productsWrapper .productContainer .customizationWrapper .sizeCustomizationWrapper {
    padding: 0 5px;
    font-size: 14px;
    height: 55px;
    line-height: 55px;
    border-top: #ddd 1px solid;
    position: relative;
}

.productsWrapper .productContainer .productDetail .sizeCustomizationWrapper .addProductBtn {
    right: 0;
    top: 8px;
}

.productsWrapper .productContainer .productDetail .addProductBtn div {
    font-size: 22px;
    font-weight: 700;
}

.qtyWrapper {
    top: 8px;
    right: 0;
    width: 150px;
}

.qtyWrapper .incr, .qtyWrapper .dcr {
    width: 48px;
    height: 35px;
    line-height: 35px;
    font-size: 27px;
    cursor: pointer;
}

.qtyWrapper .qty {
    width: 50px;
    line-height: 35px;
}

.qtyWrapper .incr {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.qtyWrapper .dcr {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

/************** modal layout *********************/
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
}

.modal .modalBody {
    background: #fff;
    position: absolute;
    overflow: auto;
    top: 350px;
    /*bottom: 15px;*/
    width: 500px;
    border-radius: 5px;
    left: calc(50% - 250px);
}

.modal .modalBody .modalCloseBtn {
    position: absolute;
    right: 0;
    top: 0;
    color: #5e7e47;
    text-align: center;
    width: 50px;
    height: 44px;
    line-height: 44px;
    font-size: 30px;
    cursor: pointer;
}

.modalTitle {
    text-align: center;
    color: #292826;
    margin: 0;
    font-size: 18px;
    height: 45px;
    line-height: 45px;
}

/************** customization modal layout *********************/
.customizationSection {
    padding: 10px;
    border-bottom: #ddd 1px solid;
}

.customizationSection.productHead {
    background: #f5f6f0;
    border-bottom: none;
}

.customizationSection .customizationHead {
    color: #62635e;
    text-transform: uppercase;
    margin: 5px;
    font-size: 16px;
}

.customizationSection .constituent .productName {
    color: #292826;
    margin-right: 100px;
    font-size: 18px;
}

.customizationSection .constituent .productDesc {
    color: #62635e;
    font-size: 12px;
}

.customizationSection .constituent .qtyWrapper {
    position: absolute;
    top: 0px;
    right: 0px;
    text-align: center;
    text-transform: uppercase;
    width: 90px;
}

.customizationSection .productName {
    color: #292826;
    margin: 0 140px 10px 0;
    font-size: 24px;
    line-height: 24px;
}

.customizationSection .productPrice {
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
    width: 112px;
    font-size: 24px;
}

.customizationSection .rupeeIcon {
    height: 18px;
    margin: 0 3px -1px 0;
}

.customizationSection .productDesc {
    color: #62635e;
    margin-right: 135px;
    line-height: 16px;
    min-height: 45px;
    font-size: 14px;
}

.qtyWrapper {
    position: absolute;
    border-radius: 6px;
    background: #5e7e47;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    border: solid 2px #5e7e47;
    line-height: normal;
}

.customizationSection .constituent .qtyWrapper {
    background: transparent;
    box-shadow: none;
    border: none;
}

.qtyWrapper .incr, .qtyWrapper .dcr {
    text-align: center;
    display: inline-block;
    color: #5e7e47;
    background-color: #ffffff;
}

.qtyWrapper .qty {
    display: inline-block;
    text-align: center;
    color: #fff;
    background: #5e7e47;
}

.qtyWrapper .incr {
    float: right;
}

.qtyWrapper .dcr {
    float: left;
}

.customizationSection .qtyWrapper {
    top: 1px;
    right: 5px;
    width: 112px;
}

.customizationSection .qtyWrapper .incr, .customizationSection .qtyWrapper .dcr {
    width: 35px;
    height: 35px;
    line-height: 35px;
    font-size: 27px;
}

.customizationSection .qtyWrapper .qty {
    width: 38px;
    line-height: 35px;
}

.customizationSection .qtyWrapper .incr {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.customizationSection .qtyWrapper .dcr {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.customizationSection .sizeDescription {
    display: block;
    text-align: center;
    padding: 0 3px;
}

.customizationSection .dimensionBtn {
    display: inline-block;
    width: 50%;
    cursor: pointer;
    font-size: 16px;
}

.customizationSection .dimensionBtn div {
    background: #f5f6f0;
    border: solid 1px #dedede;
    color: #000;
    text-align: center;
    margin: 5px;
    height: 45px;
    line-height: 45px;
    border-radius: 5px;
}

.customizationSection .dimensionBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px -2px 0;
}

.customizationSection .dimensionBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .menuProductBtn {
    display: inline-block;
    max-width: 100%;
    cursor: pointer;
}

.customizationSection .menuProductBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
    margin: 5px;
    height: 40px;
    line-height: 40px;
    border-radius: 5px;
    padding: 0 10px;
}

.customizationSection .menuProductBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .addonBtn {
    display: inline-block;
    float: left;
    width: 33%;
    cursor: pointer;
    font-size: 16px;
}

.customizationSection .addonBtn.subKuch {
    width: 100%;
    margin-top: 10px;
}

.customizationSection .addonBtn.subKuch img {
    display: none;
}

.customizationSection .addonBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
    margin: 3px;
    height: 40px;
    line-height: 40px;
    border-radius: 5px;
}

.customizationSection .addonBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px 0 0;
}

.customizationSection .addonBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .ingredientProductBtn {
    display: inline-block;
    float: left;
    width: 50%;
    cursor: pointer;
    font-size: 16px;
}

.customizationSection .ingredientProductBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
    margin: 5px;
    height: 40px;
    line-height: 40px;
    border-radius: 5px;
}

.customizationSection .ingredientProductBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px 0 0;
}

.customizationSection .ingredientProductBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .ingredientVariantBtn {
    display: inline-block;
    float: left;
    width: 50%;
    cursor: pointer;
    font-size: 16px;
}

.customizationSection .ingredientVariantBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
    margin: 5px;
    height: 45px;
    line-height: 45px;
    border-radius: 5px;
}

.customizationSection .ingredientVariantBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px 0 0;
}

.customizationSection .ingredientVariantBtn div.active {
    background: #5e7e47;
    color: #fff;
}

/**************** cart layout styles ***********************/
.cartHead {
    color: #292826;
    text-transform: uppercase;
    padding: 15px 0 15px 10px;
    font-size: 20px;
    font-weight: bold;
    line-height: 18px;
    background: #fff;
    border-bottom: #f4f4f4 1px solid;
    position: relative;
}

.btn.btn-primary.clearCart {
    display: inline-block;
    position: absolute;
    top: 0;
    right: 5px;
    width: 135px;
    font-size: 17px;
    height: 29px;
    padding: 0;
    line-height: 29px;
    text-transform: capitalize;
}

.itemCountHead {
    color: #62635e;
    text-transform: uppercase;
    font-size: 12px;
    padding: 0 0 6px 13px;
    line-height: 18px;
}

.cartBox {
    background: #fff;
    box-shadow: 0 1px 2px 0 #e4e4df;
    margin-bottom: 10px;
    vertical-align: top;
}

.cartItemContainer {
    padding: 10px;
}

.cartItemContainer .stockTag {
}

.cartItemContainer .stockTag {
    display: inline-block;
    text-transform: uppercase;
    font-size: 12px;
    color: rgb(240, 175, 59);
    border: rgb(240, 175, 59) 1px solid;
    padding: 1px 10px;
    border-radius: 10px;
    margin-bottom: 5px;
}

.cartItemContainer .stockTag.red {
    color: #b55a5a;
    border: #b55a5a 1px solid;
}

.cartItemContainer .pic {
    display: inline-block;
    background-size: cover;
    background-position: center;
    float: left;
    width: 90px;
    height: 90px;
    margin: 0 15px 8px 0;
    border-radius: 15px;
}

.cartItemContainer .itemTitle {
    color: #292826;
    margin: 0px 80px 0px 0px;
    font-size: 24px;
    line-height: 24px;
    padding: 9px 0px;
}

.cartItemContainer .itemPrice {
    position: absolute;
    top: 8px;
    right: 5px;
    text-align: right;
    width: 90px;
    font-size: 24px;
    line-height: 24px;
}

.cartItemContainer .rupeeIcon {
    height: 12px;
    margin: 0 3px -1px 0;
}

.cartItemContainer .itemDetail {
    color: #62635e;
    margin: 5px 115px 5px 0px;
    font-size: 12px;
    min-height: 40px;
    line-height: 16px;
    white-space: normal;
}

.cartItemContainer .qtyWrapper {
    position: absolute;
    top: 0px;
    right: 0px;
    border-radius: 6px;
    background: #5e7e47;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    border: solid 2px #5e7e47;
    width: 110px;
}

.cartItemContainer .qtyWrapper .incr, .cartItemContainer .qtyWrapper .dcr {
    text-align: center;
    display: inline-block;
    color: #5e7e47;
    background-color: #ffffff;
    width: 35px;
    height: 35px;
    line-height: 37px;
    font-size: 27px;
}

.cartItemContainer .qtyWrapper .incr {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    float: right;
}

.cartItemContainer .qtyWrapper .dcr {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    float: left;
}

.cartItemContainer .qtyWrapper .qty {
    display: inline-block;
    text-align: center;
    color: #fff;
    background: #5e7e47;
    width: 36px;
    line-height: 35px;
}

.cartItemContainer .actionContainer {
    height: 33px;
    width: 100%;
    display: block;
}

.cartItemContainer .actionContainer .actionDiv {
    float: right;
}

/*.cartItemContainer .editItem {
    color: #368101;
    text-decoration: underline;
    display: inline-block;
    font-size: 14px;
    line-height: 18px;
    padding: 10px 0 5px 0;
    cursor: pointer;
}*/

.cartItemContainer .editItem, .cartItemContainer .removeItem {
    color: #fff;
    display: inline-block;
    font-size: 20px;
    padding: 0 5px;
    cursor: pointer;
    background-color: #5e7e47;
    border-radius: 13px;
    width: 26px;
    height: 26px;
    text-align: center;
    margin-right: 3px;
}

.cartItemContainer .editItem .editImg {
    width: 13px;
    height: 13px;
    padding-top: 1px;
}

.cartContainer .emptyCartIcon {
    width: 13%;
    margin-top: 30px;
    margin-bottom: 20px;
}

.cartContainer .emptyCartHead {
    color: #292826;
    font-size: 21px;
    margin: 10px 0;
}

.cartContainer .emptyCartTag {
    color: #62635e;
    font-size: 16px;
    width: 80%;
    margin: 0 auto;
}

.cartItemContainer .orderRemark {
    border: none;
    color: #62635e;
    width: 100%;
    margin: 0;
    font-size: 14px;
    line-height: 18px;
    padding: 6px 0;
}

.cartItemContainer .orderCoupon {
    border: none;
    text-transform: uppercase;
    color: #62635e;
    width: calc(100% - 100px);
    height: 37px;
    font-size: 14px;
    line-height: 18px;
}

.cartItemContainer .couponLink {
    position: absolute;
    top: 0;
    right: 0;
    color: #5e7e47;
    text-align: center;
    width: 90px;
    line-height: 18px;
    font-size: 14px;
    padding: 9px 0;
    cursor: pointer;
}

.offerError {
    color: #b55a5a;
    padding: 0 10px 10px 10px;
}

.couponLogin {
    color: #5e7e47;
    padding: 0 5px;
    cursor: pointer;
    text-decoration: underline;
}

.campaignCoupon {
    margin: 5px;
    padding: 10px;
    background: aquamarine;
    border-radius: 3px;
    cursor: pointer;
}

.cartItemContainer .transactionDetails {
    color: #62635e;
    font-size: 12px;
    line-height: 18px;
}

.cartItemContainer .totalAmount {
    color: #292826;
    font-size: 14px;
    line-height: 18px;
}

.cartItemContainer .rupeeIcon {
    margin-right: 5px;
}

.cartItemContainer .transactionDetails .txDetailItem {
    padding: 10px 5px;
}

.cartItemContainer .taxDetailWrapper {
    -webkit-transition: .2s linear;
    transition: .2s linear;
    height: 0px;
    display: none;
    background: #f0f0f0;
    padding: 10px;
}

.cartItemContainer .taxDetailWrapper.open {
    height: auto;
    display: block;
    margin-bottom: 6px;
}

.cartItemContainer .taxInfo {
    width: 17px;
    margin: 0 0 -3px 5px;
    cursor: pointer;
}

.cartMovingContainer {
    margin-right: 10px;
    display: inline-block;
    width: 633px;
}

.cartMovingContainer .cartItems {
    max-height: 300px;
    overflow: auto;
    width: 99%;
}

.cartMovingContainer .cartItems .cartBox {
    margin-bottom: 0;
}

.movingContainer {
    margin-right: 10px;
    display: inline-block;
    width: 633px;
    max-height: 348px;
    overflow: auto;
}

.fixedContainer {
    width: 350px;
    display: inline-block;
    vertical-align: top;
}

.movingContainer .cartItemContainer {
    margin-right: 1%;
    margin-bottom: 0;
    border-bottom: #f4f4f4 1px solid;
    padding: 10px;
}

/************************ login layout ********************************/

.loginContainer {
    /*width: 400px;*/
    padding: 20px;
    margin: 0 auto;
    /*margin-top: 50px;*/
    margin-bottom: 180px;
    background: #f4f4f4;
    -webkit-box-shadow: 0 0 2px 0 #d3cbb8;
    -moz-box-shadow: 0 0 2px 0 #d3cbb8;
    box-shadow: 0 0 2px 0 #d3cbb8;
}

.loginContainer .headLine {
    text-align: center;
    font-size: 21px;
    margin: 10px 0 20px 0;
    font-weight: normal;
}

.loginSectionTagline {
    text-align: center;
    padding: 10px 0;
    font-size: 18px;
}

.contactContainer {
    background: #fff;
    margin-top: 5px;
    margin-bottom: 10px;
    -webkit-box-shadow: 0 0 2px 0 #d3cbb8;
    -moz-box-shadow: 0 0 2px 0 #d3cbb8;
    box-shadow: 0 0 2px 0 #d3cbb8;
    border-radius: 2px;
}

.contactContainer input, select, textarea {
    font-size: 14px;
    line-height: 18px;
    padding: 10px;
    border: none;
    width: 100%;
    border-radius: 2px;
}

.resendText {
    text-align: right;
    font-size: 18px;
}

.resendLink {
    color: #5e7e47;
    text-decoration: underline;
    cursor: pointer;
    margin-left: 5px;
}

/************* addresses style **************/
.addressesSubHead {
    text-transform: uppercase;
    padding: 15px 10px 10px 10px;
}

.addressContainer {
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
    padding: 16px;
    width: 49%;
    display: inline-block;
    margin-right: 1%;
    vertical-align: top;
    min-height: 100px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.addressContainer:last-child {
}

.addressContainer.disabled .addressBtn, .addressContainer.disabled .addressDetail {
    opacity: .5;
}

.addressContainer.disabled {
    box-shadow: none;
}

.addressContainer .addressTypeWrapper {
    position: absolute;
    right: 0;
    top: 0;
    padding: 10px;
    font-size: 14px;
    color: #f0af3b;
}

.addressContainer .addressBtn {
    display: inline-block;
    vertical-align: top;
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 15px;
    border: #62635e 2px solid;
}

.addressContainer .radioWrapper {
    vertical-align: top;
    position: absolute;
    top: 11px;
}

.addressContainer .addressDetail {
    margin: 0 0 0 30px;
}

.addressContainer .addressDetail .addressName {
    color: #292826;
    font-size: 14px;
    line-height: 18px;
    padding: 0 0 8px 0;
}

.addressContainer .addressDetail .address {
    color: #62635e;
    font-size: 14px;
    line-height: 18px;
}

.addNewAddressBtn {
    text-align: center;
    color: #5e7e47;
    text-decoration: underline;
    padding: 20px 0 10px 0;
    font-size: 14px;
    line-height: 18px;
    cursor: pointer;
}

/**************** new address style ****************/
.newAddressSubHead {
    text-align: center;
    text-transform: uppercase;
    color: #62635e;
    font-size: 12px;
    line-height: 18px;
    padding: 15px 0;
}

.newAddressInputContainer {
    background: #fff;
    position: relative;
    padding: 10px;
    border-bottom: #efefef 1px solid;
    box-shadow: 0 1px 2px 0 #828282;
}

.newAddressInputContainer:last-child {
    border-bottom: none;
}

.newAddressInputContainer input[type=text] {
    border: none;
    width: 100%;
    color: #bcbcb8;
    font-size: 14px;
    line-height: 18px;
    padding: 10px 0px;
}

.newAddressInputContainer .selectedLocWrapper {
    color: #292826;
    text-decoration: underline;
    margin-right: 110px;
    padding: 10px 0;
    font-size: 14px;
    line-height: 18px;
}

.newAddressInputContainer .changeLocBtn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #5e7e47;
    text-align: center;
    font-size: 14px;
    width: 80px;
    line-height: 18px;
    padding: 10px 0;
    cursor: pointer;
}

.newAddressInputContainer .radioLabels {
    display: inline-block;
    padding: 10px;
}

/********** payment modes styles ********/
.payModeTypeContainer {
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
    border-bottom: #efefef 1px solid;
    background: #fff;
    position: relative;
    padding: 10px;
    font-size: 18px;
    cursor: pointer;
}

.payModeTypeContainer:last-child {
    border-bottom: none;
}

.payModeTypeContainer .payableAmount {
    color: #292826;
    padding: 15px;
    font-size: 14px;
    line-height: 18px;
    cursor: auto;
}

.payModeTypeContainer .payableAmount .rupeeIcon {
    height: 14px;
    margin: 0 5px -2px 0;
}

.payModeTypeContainer .mode {
    color: #292826;
    padding: 15px;
    font-size: 14px;
    line-height: 18px;
}

.payModeTypeContainer .mode span {
    color: #5e7e47;
    font-size: 24px;
    line-height: 15px;
}

.payModeTypeContainer1 {
    position: relative;
    padding: 0px 0px;
    font-size: 18px;
    cursor: pointer;
    margin: 90px;
    display: inline-block;
    /* border: #5e7e47 15px solid; */
    border-radius: 5px;
    vertical-align: top;
}

.payableAmount1 {
    font-size: 32px;
    padding: 15px 70px;
    text-transform: capitalize;
    color: #fff;
    background: #5e7e47;
    /*height: 122px;*/
    display: inline-block;
    text-align: center;
    border-radius: 22px;
}

.payModeTypeContainer1 .mode {
    width: 160px;
    height: 160px;
    text-align: center;
}

.payModeTypeContainer1 .mode span {
    text-transform: uppercase;
    color: #5e7e47;
    font-size: 25px;
}

/************** order list page ************/
.orderListSubHead {
    text-transform: uppercase;
    color: #62635e;
    font-size: 12px;
    padding: 10px;
    border-bottom: #ddd 1px dashed;
    margin-bottom: 10px;
}

.orderContainer {
    box-shadow: 0 1px 2px 0 #bcbcb8;
    background: #fff;
    position: relative;
    padding: 10px;
    cursor: pointer;
    display: inline-block;
    width: 49%;
    margin-right: 1%;
    margin-bottom: 10px;
    vertical-align: top;
    min-height: 125px;
    border-radius: 3px;
}

.orderContainer .orderStatus {
    position: absolute;
    right: 10px;
    top: 10px;
}

.orderContainer .reOrder {
    position: absolute;
    right: 10px;
    bottom: 10px;
}

.orderContainer .orderStatus {
    color: #f0af3b;
}

.orderContainer .reOrder {
    color: #5e7e47;
}

.orderContainer .orderStatus, .orderContainer .reOrder {
    font-size: 14px;
    line-height: 18px;
    cursor: pointer;
}

.orderContainer .orderPics {
    float: left;
    width: 50px;
    margin-right: 8px;
}

.orderContainer .orderPics .pic {
    background-position: center;
    background-size: cover;
    width: 50px;
    height: 50px;
    margin: 1px 1px 0 0;
}

.orderContainer .orderContent {
    margin-left: 58px;
}

.orderContainer .orderContent .orderId {
    color: #5e7e47;
    text-transform: uppercase;
    font-size: 10px;
    line-height: 18px;
}

.orderContainer .orderContent .orderTime {
    color: #62635e;
    text-transform: uppercase;
    font-size: 10px;
    line-height: 18px;
}

.orderContainer .orderContent .productName {
    color: #292826;
    font-size: 12px;
    margin: 5px 0;
    line-height: 18px;
}

.orderContainer .orderContent .address {
    color: #bcbcb8;
    text-transform: uppercase;
    margin-top: 2px;
    font-size: 12px;
    line-height: 18px;
}

.orderContainer .orderContent .address b {
    color: #000;
}

/************** order detail page ************/
.orderDetailSubHead {
    text-transform: uppercase;
    color: #62635e;
    font-size: 12px;
    line-height: 18px;
    padding: 10px;
}

.orderStatusContainer {
    box-shadow: 0 1px 2px 0 #bcbcb8;
    background: #fff;
    padding: 9px 0;
}

.orderStatusContainer .orderStatus {
    display: inline-block;
    text-align: center;
    color: #a6a6a0;
    width: 20%;
    font-size: 12px;
    line-height: 18px;
}

.orderStatusContainer .orderStatus .active {
    color: #5e7e47;
}

.orderStatusContainer .orderStatus img {
    display: inline-block;
    height: 27px;
    margin: 5px 0;
}

.orderStatusContainer .separator {
    display: inline-block;
    vertical-align: top;
    text-align: center;
    width: 6%;
    margin-top: 16px;
}

.orderStatusContainer .separator img {
    width: 20%;
}

.orderItemContainer {
    background: #fff;
    position: relative;
    border-bottom: #ddd 1px solid;
    padding: 15px;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
}

.orderItemContainer:last-child {
    box-shadow: 0 1px 2px 0 #bcbcb8;
}

.orderItemContainer .pic {
    display: inline-block;
    background-size: cover;
    background-position: center;
    float: left;
    width: 32px;
    height: 32px;
    margin: 0 8px 8px 0;
}

.orderItemContainer .productTitle {
    color: #292826;
    margin: 10px 90px 0 0;
    font-size: 14px;
    line-height: 18px;
}

.orderItemContainer .productPrice {
    color: #292826;
    position: absolute;
    text-align: right;
    font-size: 14px;
    top: 20px;
    line-height: 18px;
    right: 15px;
    width: 80px;
}

.orderItemContainer .productPrice .rupeeIcon {
    height: 14px;
    margin: 0 3px -1px 0;
}

.orderItemContainer .customizationDetail {
    color: #62635e;
    display: inline-block;
    font-size: 12px;
    line-height: 16px;
    width: calc(100% - 90px);
}

.orderItemContainer .productQty {
    color: #292826;
    text-align: right;
    float: right;
    margin-right: 5px;
    font-size: 14px;
    line-height: 18px;
    width: 80px;
}

.orderItemContainer .remark {
    background-color: #fbf8eb;
    border-left: solid 1px #f0af3b;
    color: #826b5c;
    word-break: break-all;
    padding: 10px;
    font-size: 12px;
    line-height: 16px;
}

.orderItemContainer .total {
    font-size: 14px;
    line-height: 18px;
    padding: 6px;
    display: inline-block;
}

.orderItemContainer .right .qty {
    color: #a6a6a0;
    text-align: right;
    font-size: 12px;
    line-height: 18px;
    margin: -4px 0 0 0;
}

.orderItemContainer .right .price {
    color: #292826;
}

.orderItemContainer .right .totalPrice {
    font-size: 14px;
    line-height: 18px;
}

/************** profile page *************************/
.mobileProfileHead {
    /*background: #5e7e47;
    border-top: #759261 1px solid;
    color: #fff;
    text-align: center;*/
    padding: 20px 15px;
    margin-bottom: 30px;
}

.mobileProfileHead .profilePic {
    border: #fff 3px solid;
    border-radius: 50%;
    background: #e3e0e0;
    float: left;
    margin-right: 20px;
}

.mobileProfileHead .userName {
    font-size: 21px;
    padding: 8px 10px;
    text-transform: capitalize;
}

.mobileProfileHead .userContact {
    font-size: 12px;
    margin: 3px 0;
}

.mobileProfileHead .userEmail {
    font-size: 12px;
    margin: 3px 0;
}

.profileCard {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 1px 0 rgba(0, 0, 0, .1);
    padding: 15px;
    margin: 10px 0;
    border: #eee 1px solid;
    width: 49%;
    margin-right: 1%;
    display: inline-block;
    vertical-align: top;
}

.profileCard .cardTitle {
    font-size: 18px;
    margin-bottom: 10px;
    word-break: break-all;
}

.profileCard .loyalteaScore {
    text-align: center;
    font-size: 30px;
    color: #5e7e47;
}

.profileCard .cardDesc {
    min-height: 28px;
}

.profileCard .cardFooter {
    border-top: 1px solid #ebebeb;
    padding: 5px 0 0;
    margin-top: 10px;
    color: #5e7e47;
    line-height: 30px;
    cursor: pointer;
}

.profileCard .cardFooter img {
    width: 20px;
    margin: 5px 10px 0 0;
    float: left;
}

/********************contact page ******************/
.addressBlock {
    width: calc(100% - 30px);
    line-height: normal;
    font-size: 14px;
    float: left;
}

/****************** footer style *******************/
.footerContainer {
    background: #fff;
    padding: 30px 0;
}

.footerContent {
    width: 1024px;
    margin: auto;
    margin-top: 20px;
    padding: 0 20px;
}

.footerContent ul {
    display: inline-block;
    width: 200px;
    vertical-align: top;
}

.footerContent li {
    list-style: none;
}

.footerContent .listHead {
    text-transform: uppercase;
    font-size: 18px;
    padding: 0 0 10px 0;
}

.footerContent .listItem {
    font-size: 14px;
    padding: 7px 0;
    cursor: pointer;
}

.footerContent .socialLinkWrapper {
    float: right;
    margin-top: 68px;
}

.footerContent .socialLinkWrapper a {
    margin: 10px;
}

.footerContent .socialLinkWrapper a img {
    width: 30px;
}

.footerStrip {
    background: #f4f4f4;
    padding: 18px 0;
}

.footerStripContent {
    width: 1024px;
    margin: auto;
    padding: 0 20px;
}

/************** promo modal styles ***********/

.promoContainer {
    position: absolute;
    width: 100%;
}

.promoWrapper {
    max-width: 500px;
    margin: auto;
    margin-top: 200px;
    background: #fff;
    border: #ddd 1px solid;
    text-align: left;
    z-index: 99;
    position: relative;
    border-radius: 3px;
}

.promoWrapper .promoClose {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 30px;
    height: 30px;
    line-height: 28px;
    color: #000;
    text-align: center;
    font-size: 21px;
    background: #fff;
    border-radius: 50%;
    cursor: pointer;
}

.promoWrapper .promoImage {
    float: left;
    width: 150px;
    height: 150px;
    margin-right: 15px;
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    background-repeat: no-repeat;
    margin-top: -10px;
    margin-left: -10px;
    border: #fff 4px solid;
}

.promoWrapper .promoBody {
    padding: 15px;
}

.promoWrapper .promoHeadline {
    font-weight: normal;
    font-size: 20px;
    margin-bottom: 10px;
    margin-right: 18px;
    color: #000;
    margin-top: 10px;
}

.promoWrapper .promoText {
    margin-bottom: 10px;
    color: #000;
    margin-right: 5px;
    line-height: 20px;
    font-size: 14px;
}

.promoWrapper .bottomNote {
    margin: 10px 0 10px 140px;
    color: #868686;
    font-size: 12px;
}

.promoWrapper .applyBtn {
    background: #5e7e47;
    display: inline-block;
    padding: 10px 40px;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

/******************** coupon modal styles *******************************/
.couponBtn {
    margin: 4px;
    width: 48%;
    display: inline-block;
}

.removeCouponBtn {
    padding: 15px;
    background: #a05757;
    float: right;
    margin-top: -15px;
    margin-right: -15px;
    color: #fff;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    cursor: pointer;
}

.leftMenuContainer {
    height: 1920px;
    width: 175px;
    position: fixed;
    z-index: 1;
    top: 235px;
    left: 0;
    background: rgb(243, 243, 243);
    overflow-x: hidden;
    padding-top: 24px;
    margin: 0;
    word-wrap: break-word;
}

.leftMenuContainer .navTab.active {
    background: #577c3a;
    font-weight: 700;
    border-right: 7px #577c3a solid;
    /* box-shadow: 0px 0px 3px 3px #bab8b8; */
    color: #fff;
}

.leftMenuContainer .navTab.longText {
    padding: 22px 10px;
}

.leftMenuContainer .navTab {
    padding: 35px 10px;
    font-size: 20px;
    font-weight: bold;
    text-transform: capitalize;
    line-height: 23px;
    margin: 5px 0;
    cursor: pointer;
    text-align: left;
    -webkit-transition: .2s linear;
    transition: .2s linear;
    width: 164px;
    height: 90px;
    display: block;
    border: #5e7e47 1px solid;
    color: #5e7e47;
    background: #FFF;
    border-right-width: 5px;
}

.bottomCartWrapper {
    position: absolute;
    bottom: 0;
    height: 470px;
    width: 100%;
    background-color: #587542;
    border-top: 1px solid rgb(221, 221, 221);
    border-top: 1px solid rgb(221, 221, 221);
    overflow: auto;
    z-index: 2;
}

/************** Header banner ************/

.headerBannerWrapper {
    height: 235px;
    overflow: hidden;
    background: #f3f3f3;
    text-align: center;
}

.headerBannerImage {
    /*height: 67px;*/
    line-height: 70px;
    font-size: 24px;
}

.truecallerSignInHead {
    text-transform: capitalize;
    font-weight: bold;
    padding: 10px;
    /* border: 1px solid rgb(94, 126, 71); */
    border-radius: 5px;
    width: 227px;
    margin: 0 auto;
    margin-top: -56px;
    margin-bottom: 50px;
    /* height: 100px; */
    text-align: center;
    background-color: #f4f4f4;
    font-size: 22px;
    line-height: 30px;
}

.truecallerSignInBlock {
    padding: 24px;
    /* border: 1px solid rgb(94, 126, 71); */
    border-radius: 5px;
    width: 300px;
    margin: 0 auto;
    /*height: 297px;*/
}

.loginBycontactHead {
    text-transform: capitalize;
    font-weight: bold;
    padding: 10px;
    /* border: 1px solid rgb(94, 126, 71); */
    border-radius: 5px;
    width: 200px;
    margin: 0 auto;
    margin-top: -40px;
    height: 100px;
    text-align: center;
    background-color: #f4f4f4;
    font-size: 22px;
    line-height: 30px;
    margin-bottom: 10px;
}

.loginByContactForm {
    padding: 6px;
    /*border: 1px solid rgb(94, 126, 71);*/
    width: 300px;
    height: 360px;
    margin: 0 auto;
}

.loginBlockLeft {
    display: inline-block;
    width: 30%;
    height: 500px;
    margin-right: 10px;
    border: #5e7e47 4px solid;
    border-radius: 30px;
    vertical-align: top;
    margin: 10px;
}

.loginDividerBlock {
    display: inline-block;
    width: 1px;
    height: 500px;
    background: rgb(0, 0, 0);
    margin: 0 50px;
    vertical-align: top;
}

.loginDivider {
    color: rgb(255, 255, 255);
    background: rgb(94, 126, 71);
    font-size: 27px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-left: -40px;
    margin-top: 220px;
    text-align: center;
    line-height: 66px;
    border: #f4f4f4 9px solid;
}

.loginBlockRight {
    position: relative;
    display: inline-block;
    width: 30%;
    height: 500px;
    border: #5e7e47 4px solid;
    border-radius: 30px;
    vertical-align: top;
    margin: 10px;
}

.loginBlockFormContainer {
    width: 286px;
    margin: auto;
    padding: 5px;
}

.loginButton {
    width: 248px;
    background: #5e7e47;
    color: #fff;
    border: #5e7e47 1px solid;
    margin: 30px 10px 0 14px;
    min-width: 90px;
    text-align: center;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    padding: 0 25px;
    height: 38px;
    line-height: 32px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    position: relative;
}

.fieldContainer {
    margin-top: 11px;
    border-radius: 3px;
    border: 1px solid rgb(94, 126, 71);
}

.inputFont {
    font-size: 20px;
    font-weight: bold;
}

.loginBackButton {
    background: #5e7e47;
    color: #fff;
    min-width: 90px;
    text-align: center;
    border-radius: 17px;
    width: 300px;
    cursor: pointer;
    font-size: 26px;
    display: inline-block;
    padding: 20px 30px;
}

.redeemButton {
    cursor: pointer;
    font-size: 45px;
    font-weight: bold;
    display: inline-block;
    min-width: 130px;
    text-align: center;
    border-radius: 17px;
    padding: 80px 20px 20px 20px;
    background: white;
    color: black;
    height: 220px;
    margin: 10px;
}

.chaayosCash {
    text-align: center;
    margin-top: 15px;
    background: #73a141;
    padding: 15px;
    margin-bottom: -50px;
    border-radius: 10px;
    opacity: 0.8
}

.colouredHead {
    /*background-color: #607D8B;*/
}

.signUpSection {
    margin-top: 35px;
    position: relative;
}

.signUpSectionOverlay {
    display: block;
    background: rgba(244, 244, 244, 0.5);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
}

.paymentModesTabContainer {
    width: 1080px;
    /*background-color: #CDDC39;*/
    margin-top: -672px;
}

.paymentModeSection {
    width: 1024px;
    margin: auto;
    margin-top: 10px;
    background: #fff;
    min-height: 300px;
    overflow: hidden;
}

.loyalitySection {
    width: 80%;
    margin: 0 auto;
}

.giftCardSection {
    text-align: center;
    font-size: 30px;
    margin: 0px auto;
    width: 80%;
}

.giftCardContainer {
    display: flex;
    font-size: 30px;
    padding: 10px 20px;
    width: 65%;
    margin: 40px 0px;
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
    background: #c4e0a0;
}

.giftCardOffer {
    padding: 15px;
    border-radius: 20px;
    margin: 20px 5px;
    background: white;
}

.modalBodyPaytm {
    background: rgba(244, 244, 244, 0.5);
    position: absolute;
    overflow: auto;
    top: 825px;
    width: 250px;
    border-radius: 5px;
    left: calc(50% - -163px);
}

.redeemContainer {
    display: flex;
}

.paytmModalCloseBtn {
    position: absolute;
    right: 0;
    top: 0;
    color: #5e7e47;
    text-align: center;
    width: 50px;
    height: 44px;
    line-height: 44px;
    font-size: 30px;
    cursor: pointer;
}

.payModeTypeContainer1 .paymentMode {
    width: 345px;
    height: 500px;
    text-align: center;
}

.fullPageLoaderPaytmQR {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-align: center;
    z-index: 99;
}

.fullPageLoaderPaytmQR .loaderWrapper {
    background: #fff;
    color: #000;
    border-radius: 3px;
    max-width: 600px;
    margin: auto;
    margin-top: 50px;
    padding: 15px;
}

.fullPageLoaderPaytmQR .loaderWrapper .promptMessage {
    min-height: 50px;
    font-size: 18px;
}

.fullPageLoaderPaytmQR .loaderWrapper .loaderMessage {
    margin: 0 30px 10px 30px;
}

.errorSignUpMessage {
    padding: 2px !important;
    position: relative;
    display: inline-block !important;
    margin-top: 5px;
    color: #c51244;
    font-size: 15px;
}

.clearCartBox {
    float: left;
    background: #fff;
    border-radius: 3px;
    color: #5e7e47;
    padding: 3px 11px;
    font-size: 20px;
    font-weight: bold;
    margin: -2px 10px 0 35px;
    height: 28px;
    line-height: 24px;
    width: 120px;
}

.payableAmount1Modal {
    margin: 30px;
    width: 421px;
    font-size: 32px;
    padding: 15px 60px;
    text-transform: capitalize;
    color: #fff;
    background: #8BC34A;
    display: inline-block;
    text-align: center;
    border-radius: 22px;
}

.cashPaymentOTPSection {
    padding: 6px;
    /* border: 1px solid rgb(94, 126, 71); */
    width: 300px;
    height: 260px;
    margin: 0 auto;
}

.resendOTPButton {
    width: 91px;
    height: 32px;
    background-color: #ddd;
    padding: 10px;
    color: #5e7e47;
    margin: 0 auto;
}

.verifyOTPButton {
    width: 248px;
    background: #5e7e47;
    color: #fff;
    border: #5e7e47 1px solid;
    margin: 30px 10px 0 14px;
    min-width: 90px;
    text-align: center;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    padding: 0 25px;
    height: 38px;
    line-height: 32px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    position: relative;
}

.otpMessageError {
    width: 100%;
    /* height: 61px; */
    /*background-color: #ddd;*/
    margin: 0 0 10px 0;
    color: #c51244;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
}

.otpMessageSuccess {
    width: 100%;
    /* height: 61px; */
    /*background-color: #ddd;*/
    margin: 0 0 10px 0;
    color: #5e7e47;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
}

.orderSourceBtn {
    display: inline-block;
    width: 30%;
    text-align: center;
    background: #fff;
    margin: 100px 40px;
    color: #9b9797;
    border: 1px solid #9b9797;
    border-radius: 20px;
    font-size: 26px;
    text-transform: uppercase;
    padding: 20px 0;

}

.orderSourceBtn.selected {
    color: #5e7e47;
    border: #5e7e47 3px solid;
    box-shadow: 0 0 8px #000;
}

.orderSourceBtn img {
    /*height: 46px;*/
    display: inline-block;
    margin: 10px;
}

.gcPaymentModal .strip {
    padding: 15px;
    background: #efefef;
    margin-bottom: 5px;
    font-size: 21px;
    margin-top: 50px;
}

.gcPaymentModal .strip span {
    float: right;
}

.gcPaymentModal .otpInput {
    padding: 10px;
    border-radius: 3px;
    border: #ccc 1px solid;
    display: block;
    width: 100%;
    font-size: 21px;
}

.OrderSummary {
    font-size: 35px;
    width: 80%;
    margin: 0 auto;
}