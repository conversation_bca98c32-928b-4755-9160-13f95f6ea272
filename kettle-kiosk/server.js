// server.js
var express = require('express');
var path = require('path');
var compression = require('compression');
var request = require('request');
var helmet = require('helmet');

var app = express();

// compress all responses
app.use(compression());
//for security
app.use(helmet());
app.disable('x-powered-by');

// serve our static stuff like index.css
app.use(express.static(path.join(__dirname, 'dist/')));

// send all requests to index.html so browserHistory in React Router works
app.get('/verifyemail', function (req, res) {
    //console.log(req);
    res.sendFile(path.join(__dirname, 'dist/verifyemail.html'));
});

// send all requests to index.html so browserHistory in React Router works
app.get('/validate', function (req, res) {
    var query = req.query;
    var token = query["token"];
    console.log(token);

    var basePath = (process.env.NODE_ENV == "dev" || process.env.NODE_ENV == "local")
                    ? "http://dev.kettle.chaayos.com:9595"
                    : "http://internal.chaayos.com";

    if(process.env.NODE_ENV == "local"){
        basePath = "http://localhost:8080"
    }
    console.log("basePath is :::: " + basePath);

    var url = basePath + "/kettle-service/rest/v1/authorization/validate?token="+encodeURIComponent(token);
    request(url, function (error, response, body) {
        console.log('error:', error); // Print the error if one occurred
        console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received
        console.log('body:', body); // Print the HTML for the Google homepage.
        res.send(body!=undefined ? body : false);
    });
});

app.get('*', function (req, res) {
    //console.log(req);
    res.sendFile(path.join(__dirname, 'dist/index.html'));
});

//var PORT = process.env.PORT || 8080
var PORT = 8686;
app.listen(PORT, function () {
    process.env.NODE_ENV = process.argv[process.argv.length-1];
    console.log('Production Express server running at localhost:' + PORT + " and ENV :: " + process.env.NODE_ENV)
});