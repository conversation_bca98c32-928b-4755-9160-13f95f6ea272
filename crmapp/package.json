{"name": "crmapp", "version": "0.0.1", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest", "postinstall": "node ./android-release-gradle-fix.js", "android": "react-native run-android"}, "dependencies": {"@react-native-community/async-storage": "^1.5.0", "@react-native-community/netinfo": "^5.7.1", "@react-native-firebase/app": "^12.1.0", "@react-native-firebase/crashlytics": "^12.1.0", "axios": "^0.19.0", "fetch-intercept": "^2.3.1", "pubnub": "^4.24.3", "pubnub-react": "^1.3.1", "react": "^17.0.1", "react-gesture-handler": "0.0.5", "react-native": "^0.63.3", "react-native-audio": "^4.3.0", "react-native-camera": "^3.44.3", "react-native-cli": "^2.0.1", "react-native-communications": "^2.2.1", "react-native-device-info": "^2.3.2", "react-native-face-tracker-library": "file:FaceTrackerLibrary", "react-native-fs": "^2.14.1", "react-native-gesture-handler": "^1.6.1", "react-native-paper": "^4.9.2", "react-native-popup-dialog": "^0.18.2", "react-native-qrcode-svg": "^5.2.0", "react-native-splash-screen": "^3.2.0", "react-native-svg": "^9.9.4", "react-native-svg-transformer": "^0.14.3", "react-native-vector-icons": "^6.5.0", "react-native-webview": "^7.5.2", "react-navigation": "^3.11.0", "react-navigation-backhandler": "^1.3.2", "react-redux": "^7.1.0", "redux": "^4.0.1", "redux-logger": "^3.0.6", "redux-promise-middleware": "^6.1.1", "redux-thunk": "^2.3.0"}, "devDependencies": {"@babel/core": "7.6.2", "@babel/runtime": "7.6.2", "babel-jest": "24.8.0", "jest": "24.8.0", "metro-react-native-babel-preset": "0.54.1", "react-test-renderer": "16.8.3"}, "jest": {"preset": "react-native"}}