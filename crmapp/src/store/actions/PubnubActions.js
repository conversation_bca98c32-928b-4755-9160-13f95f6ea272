import UtilityService from "../services/UtilityService";
import NavigationService from "../services/NavigationService";
import * as CustomerActions from "./CustomerActions";
import * as PaymentActions from "./PaymentActions";
import * as UtilityActions from "./UtilityActions";
import {ToastAndroid} from "react-native";
import StorageService from "../services/StorageService";

export function setupPubNub(data) {
    return dispatch => {
        dispatch({type: "SET_PUB_NUB_OBJECT", payload: data});
    }
}

export function subscribe(props) {
    return dispatch => {
        //const pubnub = UtilityService.getPubNub();
        props.pubnub.subscribe({
            channels: [UtilityService.getPubNubChannel(),
                UtilityService.getTrueCallerChannel(),
                UtilityService.getIngenicoStatusChannel()],
            withPresence: true
        });
        // props.pubnub.getStatus(st => {
        //     console.log("status:: ", st);
        dispatch(handlePubnubMessage(props.pubnub));
        dispatch(handleTrueCallerMessage(props.pubnub));
        dispatch(handleIngenicoPayStatusMessage(props.pubnub));
        // });
    }
}

export function publish(data) {
    return dispatch => {
        //const pubnub = UtilityService.getPubNub();
        // UtilityService.getPubNub().getStatus(st => {
        try {
            UtilityService.getPubNub().publish({
                message: data,
                channel: UtilityService.getPubNubChannel()
            });
        } catch (e) {
            console.log("Pubnub exception::::", e)
        }
        // });
    }
}

export function handleIngenicoPayStatusMessage(pubnub) {
    return dispatch => {
        const ingenicoPayStatusChannel = UtilityService.getIngenicoStatusChannel();

        pubnub.getMessage(ingenicoPayStatusChannel, (pubnubMessage) => {
            console.log('pubnubMessage message ingenicoPayStatusChannel ', pubnubMessage.message);
            let data = pubnubMessage.message;
            let key = Object.keys(pubnubMessage.message)[0];
            switch (key) {
                case 'INGENICO_PAY_STATUS':
                    console.log('INGENICO_PAY_STATUS');
                    dispatch(PaymentActions.paymentStatus(data[key], UtilityService.getCustomerDetails()));
                    break;
            }
        });
    }
}

export function handleTrueCallerMessage(pubnub) {
    return dispatch => {
        const trueCallerChannel = UtilityService.getTrueCallerChannel();
        pubnub.getMessage(trueCallerChannel, (pubnubMessage) => {
            console.log('pubnubMessage.message true calller ', pubnubMessage.message);
            let data = pubnubMessage.message;
            let key = Object.keys(pubnubMessage.message)[0];
            switch (key) {
                case 'TRUE_CALLER_PROFILE':
                    console.log('TRUE_CALLER_PROFILE');
                    const sessionId = UtilityService.getSessionId();
                    var contact = data[key].contact.substring(2); //removing first 2 digits as it is country code
                    data[key].contact = contact;
                    dispatch(CustomerActions.lookupCustomer(contact, false, null, data[key], sessionId));
                    break;
            }
        });
    }
}


export function handlePubnubMessage(pubnub) {
    return dispatch => {
        const channel = UtilityService.getPubNubChannel();
        pubnub.getMessage(channel, (pubnubMessage) => {
            console.log('pubnubMessage.message ', pubnubMessage.message);
            let data = pubnubMessage.message;
            var key = Object.keys(pubnubMessage.message);
            switch (key[0]) {
                case 'ORDER_START':
                    dispatch({type: "IS_MODAL_VISIBLE", payload: false});
                    const orderSessionId = data[key[0]].sessionId;
                    if(UtilityService.getOrderSessionId() != orderSessionId) {
                        UtilityService.setOrderSessionId(orderSessionId);
                        UtilityService.setCustomerDetails(data[key[0]]);
                        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                        dispatch(handleLoginFlow());
                        //NavigationService.navigate("CustomerData");
                        dispatch(publish({"SCREEN_OPENED": {}}));
                        const sessionId = UtilityService.generateSessionId();
                        dispatch({type:"SET_SESSION_ID", payload:sessionId});
                        dispatch({type:"SET_FACE_STRING", payload:null});
                        UtilityService.setFaceString(null);
                    } else {
                        console.log("call ignored");
                    }
                    break;
                case 'ORDER_PLACED':
                    ToastAndroid.show('Order placed successfully. Thanks for visiting Chaayos!', ToastAndroid.LONG);
                    dispatch({type: "SET_PAYMENT_QR_MAP", payload: UtilityService.getPaymentMap()});
                    NavigationService.navigate("Home");
                    dispatch({type: "IS_MODAL_VISIBLE", payload: false});
                    break;
                case 'ORDER_IN_PROGRESS':
                    dispatch({type: "IS_MODAL_VISIBLE", payload: false});
                    dispatch({type: 'OTP_SOURCE', payload: null});
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    if(data[key[0]].contactVerified == true) {
                        NavigationService.navigate("Redemption");
                    } else {
                        NavigationService.navigate("Home");
                    }
                    break;
                case "SWITCH_OTP":
                    //$scope.switchOTP();
                    break;
                case 'CUSTOMER_NOT_INTERESTED':
                    const customerDetails = UtilityService.getCustomerDetails();
                    customerDetails.customerRejected = true;
                    UtilityService.setCustomerDetails(customerDetails);
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: customerDetails});
                    dispatch({type: "IS_MODAL_VISIBLE", payload: false});
                    dispatch({type: 'OTP_SOURCE', payload: null});
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    NavigationService.navigate("Home");
                    break;
                case 'ORDER_CANCELLED':
                    dispatch({type: "IS_MODAL_VISIBLE", payload: false});
                    dispatch({type: 'OTP_SOURCE', payload: null});
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    dispatch({type: "SET_PAYMENT_REMAINING_SECONDS", payload: 0});
                    dispatch({type: "SET_SECONDS_INTERVAL", payload: 0});
                    NavigationService.navigate("Home");
                    break;
                case "ATTENDANCE_START":
                    NavigationService.navigate("AttendanceScreen");
                case "GIFT_CARD_ADDED":
                    /*if(emittedObj!=null){
                        $scope.giftCardList[emittedObj.itemId] = emittedObj;
                    }
                    $scope.loadGiftCardView();*/
                    break;
                case "GIFT_CARD_REMOVED":
                    /*if(emittedObj!=null){
                        //$scope.removeGiftCard(emittedObj.itemId);
                        //$scope.giftCardList.delete(emittedObj.itemId);
                        delete $scope.giftCardList[emittedObj.itemId];
                        if(Object.keys($scope.giftCardList).length<=0 || !$scope.isPendingGiftCard()){
                            $scope.loadThankYouView();
                        }
                    }*/
                    break;
                case "GIFT_CARD_REMOVED_ALL":
                    /*if(emittedObj!=null){
                        if($scope.giftCardList!=null && Object.keys($scope.giftCardList).length>0){
                            $scope.giftCardList = {};
                            $scope.loadThankYouView();
                        }
                    }*/
                    break;
                case "GIFT_CARDS_REVALIDATE":
                    /*if(emittedObj!=null){
                        emittedObj.map(function (card) {
                            $scope.giftCardList[card.itemId] = card;
                        });
                    }
                    $scope.loadGiftCardView();*/
                    break;
                case 'GIFT_CARDS_OTP_VERIFY':
                    dispatch(openOtpPopup('giftCard', 'to approve your gift card payment'));
                    break;
                case 'GIFT_CARDS_OTP_CHECK':
                    if (UtilityService.getCustomerDetails() && UtilityService.getCustomerDetails().otpVerified) {
                        dispatch(publish({OTP_VERIFIED: UtilityService.getCustomerDetails()}));
                    }
                    break;
                case "GIFT_CARDS_OTP_VERIFY_CANCEL":
                    dispatch({type: 'IS_MODAL_VISIBLE', payload: false});
                    dispatch({type: 'OTP_SOURCE', payload: null});
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    if (UtilityService.getCustomerDetails() && (UtilityService.getCustomerDetails().customerRejected || UtilityService.getCustomerDetails().contactVerified)) {
                        NavigationService.navigate("Home");
                    } else if (UtilityService.getCustomerDetails() && !UtilityService.getCustomerDetails().customerRejected && UtilityService.getCustomerDetails().contactVerified) {
                        NavigationService.navigate("Redemption");
                    } else if (UtilityService.getCustomerDetails() && !UtilityService.getCustomerDetails().contactVerified && !UtilityService.getCustomerDetails().customerRejected) {
                        dispatch(handleLoginFlow());
                    }
                    break;
                case 'EMAIL_INPUT_SKIPPED_BY_CRE':
                    NavigationService.navigate("Redemption");
                    break;
                case 'EMAIL_FORM_SKIPPED_BY_CRE':
                    break;
                case "TRUE_CALLER_PROFILE":
                    break;
                case "TC_GIFT_CARD_VERIFY":
                    break;
                case "TC_REDEMPTION_VERIFY":
                    break;
                case "TABLE_ORDER":
                    break;
                case 'OFFER_SIGNUP_REQUIRED':
                    dispatch(openOtpPopup('fromCoupon', 'to avail your coupon'));
                    break;
                case 'CUSTOMER_FACE_SKIPPED':
                    NavigationService.navigate("CustomerData");
                    dispatch({type: "LOGIN_BY_FACE", payload: false});
                    UtilityService.setLoginByFace(false);
                    break;
                case 'PAY_AMOUNT':
                    dispatch(PaymentActions.updateOrderDetails(data[key[0]]));
                    dispatch({type: 'SET_PAYMENT_START', payload: true});
                    NavigationService.navigate("MakePayment");
                    break;
                case 'PAYMENT_FROM_POS':
                    NavigationService.navigate("Home");
                    break;
                case 'PAYMENT_MODAL_CLOSE':
                    dispatch({type: 'IS_MODAL_VISIBLE', payload: false});
                    dispatch({type: 'OTP_SOURCE', payload: null});
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    dispatch({type: 'SET_PAYMENT_START', payload: false});
                    dispatch({type: "SET_QR_CODE", payload: null});
                    if (UtilityService.getCustomerDetails() && (UtilityService.getCustomerDetails().customerRejected || UtilityService.getCustomerDetails().contactVerified)) {
                        NavigationService.navigate("Home");
                    } else if (UtilityService.getCustomerDetails() && !UtilityService.getCustomerDetails().customerRejected && UtilityService.getCustomerDetails().contactVerified) {
                        NavigationService.navigate("Redemption");
                    } else if (UtilityService.getCustomerDetails() && !UtilityService.getCustomerDetails().contactVerified && !UtilityService.getCustomerDetails().customerRejected) {
                        dispatch(handleLoginFlow());
                    }
                    break;
                case 'PAYMENT_STATUS_CHECK':
                    console.log('PAYMENT_STATUS_CHECK');
                    if (UtilityService.getCustomerDetails() && UtilityService.getCustomerDetails().paymentStatus == "SUCCESS") {
                        dispatch(publish({PAYMENT_VERIFIED: UtilityService.getCustomerDetails()}));
                    }
                    break;
                case 'COUPON_MODAL_CLOSED':
                    dispatch({type: "IS_MODAL_VISIBLE", payload: false});
                    dispatch({type: 'OTP_SOURCE', payload: null});
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    if (UtilityService.getCustomerDetails() && UtilityService.getCustomerDetails().customerRejected) {
                        NavigationService.navigate("Home");
                    } else if (UtilityService.getCustomerDetails() && UtilityService.getCustomerDetails().contactVerified) {
                        if(UtilityService.getCustomerDetails().chaiRedeemed >= 0) {
                            NavigationService.navigate("Home");
                        } else {
                            NavigationService.navigate("Redemption");
                        }
                    } else  {
                        dispatch(handleLoginFlow());
                    }
                    break;
                case 'CHAAYOS_WALLET_BALANCE':
                    dispatch({type: "SET_CHAAYOS_WALLET_BALANCE", payload: data[key[0]]});
                    break;
                case 'APP_DATA_PUSH':
                    dispatch(UtilityActions.sendAppData());
                    break;
                case 'APP_UPDATE':
                    dispatch(UtilityActions.updateApp());
                    break;
                case 'FORCE_APP_UPDATE':
                    dispatch(UtilityActions.forceUpdateApp());
                    break;
                default:
                    break;
            }
        });
    }
}

function openOtpPopup(otpSource, otpHeadline) {
    return dispatch => {
        if(UtilityService.getLoginByFace() == true) {
            dispatch(handleOTPSkipFlow(otpSource));
        } else {
            dispatch({
                type: "OTP_SOURCE",
                payload: otpSource
            });
            dispatch({
                type: "SET_OTP_HEADLINE",
                payload: otpHeadline
            });
            NavigationService.navigate("EnterOtp");
        }
    }
}

function handleOTPSkipFlow(otpSource) {
    return dispatch => {
        let customerDetails = UtilityService.getCustomerDetails();
        customerDetails.otpVerified = true;
        //ToastAndroid.show("OTP verified!", ToastAndroid.LONG);
        dispatch({type: 'SET_CUSTOMER_DETAILS', payload: customerDetails});
        UtilityService.setCustomerDetails(customerDetails);

        dispatch({type: 'IS_MODAL_VISIBLE', payload: false});
        if (otpSource == 'fromCoupon' || otpSource == 'giftCard') {
            dispatch(publish({OTP_VERIFIED: customerDetails}));
        } else if (otpSource == 'fromSecondChai') {
            customerDetails.chaiRedeemed = 1;
            customerDetails.productId = 10;
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: customerDetails});
            UtilityService.setCustomerDetails(customerDetails);
            dispatch(publish({REDEMPTION: customerDetails}));
            ToastAndroid.show('You have redeemed your free loyality chai!', ToastAndroid.LONG);
        } else {
            dispatch(publish({REDEMPTION: customerDetails}));
            customerDetails.chaiRedeemed > 1 ? ToastAndroid.show('You have redeemed ' + customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG)
                : ToastAndroid.show('You have redeemed ' + customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG);
        }
        NavigationService.navigate('Home');
    }
}

function handleLoginFlow() {
    return dispatch => {
        StorageService.getFaceDetectionMode().then((mode) => {
            if (mode == true || mode == "true") {
                NavigationService.navigate("FaceRecognition");
            } else {
                NavigationService.navigate("CustomerData");
            }
        });
    }
}