import FaceTracker from './../../FaceTracker';
import * as PubnubActions from "./PubnubActions";
import RestService from "../services/RestService";
import UtilityService from "../services/UtilityService";
import ConfigService from "../services/ConfigService";
import apis from "../services/APIs";
import {startFaceSyncInBackground} from "./FaceRecognitionActions";
import DeviceInfo from 'react-native-device-info';

export function setNetInfoSubscriber(data) {
    return dispatch => {
        dispatch({type: "SET_NET_INFO_SUBSCRIBER", payload: data});
    }
}

export function sendAppData() {
    return dispatch => {
        try {
            const reqObj = {
                version: DeviceInfo.getVersion(),
                unitId: UtilityService.getUnitId(),
                terminal: UtilityService.getTerminalId(),
                appType: "CUSTOMER_SCREEN",
                deviceUniqueId:DeviceInfo.getUniqueID(),
                manufacturer:DeviceInfo.getManufacturer(),
                //macAddress:DeviceInfo.getMACAddress(),
                deviceModel:DeviceInfo.getModel(),
                deviceId:DeviceInfo.getDeviceId(),
                systemName:DeviceInfo.getSystemName(),
                deviceVersion: DeviceInfo.getSystemVersion(),
                bundleId:DeviceInfo.getBundleId(),
                buildNumber:DeviceInfo.getBuildNumber(),
                //deviceName:DeviceInfo.getDeviceName(),
                userAgent:DeviceInfo.getUserAgent(),
                deviceLocale:DeviceInfo.getDeviceLocale(),
                deviceCountry:DeviceInfo.getDeviceCountry()
            };
            RestService.postJSON(apis.getUrls().appManagement.appVersionUpdate, reqObj).then((response) => {
                if (response == true) {
                    console.log("app data sent successfully");
                } else {
                    console.log("error sending app data");
                }
            }).catch((error) => {
                console.log(error);
            });
        } catch (error) {
            console.log(error);
        }
    }
}

export function updateApp() {
    return dispatch => {
        dispatch(checkAppVersion(false));
    }
}

export function forceUpdateApp() {
    return dispatch => {
        dispatch(checkAppVersion(true));
    }
}

export function checkAppVersion(forceUpdate) {
    return dispatch => {
        try {
            //TODO call api to get latest app version
            //TODO if current app version != latest app version call app update
            const reqObj = [{
                appType: "CUSTOMER_SCREEN"
            }];
            RestService.getJSON(apis.getUrls().appManagement.getCurrentVersion, reqObj).then((response) => {
                if (response != null) {
                    console.log("current app version is ", response);
                    if (forceUpdate == true) {
                        dispatch(startUpdate(response));
                    } else {
                        if (response != DeviceInfo.getVersion()) {
                            dispatch(startUpdate(response));
                        }
                    }
                } else {
                    console.log("error getting app version");
                }
            }).catch((error) => {
                console.log(error);
            });
        } catch (error) {
            console.log(error);
        }
    }
}

export function startUpdate(version) {
    return dispatch => {
        try {
            dispatch(PubnubActions.publish({"CUSTOMER_SCREEN_UPDATE": {}}));
            /*let url = "https://s3-eu-west-1.amazonaws.com/chaayos.app.release/CRM/";
            url += ConfigService.getEnvironment()+"/";
            url += version+"/crm.apk";
            FaceTracker.updateApp("https://s3-eu-west-1.amazonaws.com/chaayos.app.release/CRM/DEV/V2/crm.apk");*/
            const reqObj = [
                {version: version},
                {appType: "CUSTOMER_SCREEN"}
            ];
            RestService.getJSON(apis.getUrls().appManagement.appDownload, reqObj).then((response) => {
                if (response != null) {
                    console.log("app data for download ", response);
                    if (response != null && response.filepath != null) {
                        FaceTracker.updateApp(response.filepath);
                    }
                } else {
                    console.log("error getting app version");
                }
            }).catch((error) => {
                console.log(error);
            });
        } catch
            (error) {
            console.log(error);
        }
    }
}