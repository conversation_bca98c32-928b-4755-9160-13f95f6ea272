import RestService from "../services/RestService";
import apis from "../services/APIs";
import * as PubnubActions from "./PubnubActions";
import {ToastAndroid} from "react-native";
import NavigationService from "../services/NavigationService";
import UtilityService from "../services/UtilityService";
import {faceSignup} from "./CustomerActions";

export function resendOtp(props) {
    return dispatch => {
        RestService.postJSON(apis.getUrls().customer.generateOTP, props.customerDetails).then((response) => {
            dispatch(PubnubActions.publish({OTP_RESENT: props.customerDetails}));
            ToastAndroid.show('Otp has been sent to registered mobile number!', ToastAndroid.LONG);
        });
    }
}

export function verifyOtp(otp, props, inputRef) {
    return dispatch => {
        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: "Verifying OTP"}});
        RestService.postJSON(apis.getUrls().customer.verifyOTP, props.customerDetails).then((response) => {
            if (response) {
                props.customerDetails.otpVerified = true;
                ToastAndroid.show("OTP verified!", ToastAndroid.LONG);
                dispatch({type: 'SET_CUSTOMER_DETAILS', payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                dispatch({type: 'IS_MODAL_VISIBLE', payload: false});

                switch (props.otpSource) {
                    case 'fromCoupon':
                        dispatch(PubnubActions.publish({OTP_VERIFIED: props.customerDetails}));
                        NavigationService.navigate('Home');
                        break;
                    case 'giftCard':
                        dispatch(PubnubActions.publish({OTP_VERIFIED: props.customerDetails}));
                        NavigationService.navigate('Home');
                        break;
                    case 'fromSecondChai':
                        props.customerDetails.chaiRedeemed = 1;
                        props.customerDetails.productId = 10;
                        dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                        UtilityService.setCustomerDetails(props.customerDetails);
                        dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                        ToastAndroid.show('You have redeemed your free loyality chai!', ToastAndroid.LONG);
                        NavigationService.navigate('Home');
                        break;
                    case 'fromFaceLogin':
                        dispatch({type: 'OTP_SOURCE', payload: null});
                        dispatch(faceSignup(props.customerDetails.name, props.customerDetails.contact, UtilityService.getFaceString(), UtilityService.getSessionId()));
                        if(props.customerDetails.eligibleForSignupOffer) {
                            props.customerDetails.chaiRedeemed = 1;
                            props.customerDetails.productId = 10;
                            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                            UtilityService.setCustomerDetails(props.customerDetails);
                            dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                            ToastAndroid.show('You have redeemed your free loyality chai!', ToastAndroid.LONG);
                            NavigationService.navigate('Home');
                        } else {
                            NavigationService.navigate('Redemption');
                        }
                        break;
                    default:
                        dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                        props.customerDetails.chaiRedeemed > 1 ? ToastAndroid.show('You have redeemed ' + props.customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG)
                            : ToastAndroid.show('You have redeemed ' + props.customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG);
                        //NavigationService.navigate('Home');
                }
                /*if (props.otpSource == 'fromCoupon' || props.otpSource == 'giftCard') {
                    dispatch(PubnubActions.publish({OTP_VERIFIED: props.customerDetails}));
                    NavigationService.navigate('Home');
                } else if (props.otpSource == 'fromSecondChai') {
                    props.customerDetails.chaiRedeemed = 1;
                    props.customerDetails.productId = 10;
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                    UtilityService.setCustomerDetails(props.customerDetails);
                    dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                    ToastAndroid.show('You have redeemed your free loyality chai!', ToastAndroid.LONG);
                    NavigationService.navigate('Home');
                } else if (props.otpSource == 'fromFaceLogin') {
                    dispatch(faceSignup(props.customerDetails.name, props.customerDetails.contact, UtilityService.getFaceString(), UtilityService.getSessionId()));
                    NavigationService.navigate('Redemption');
                } else {
                    dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                    props.customerDetails.chaiRedeemed > 1 ? ToastAndroid.show('You have redeemed ' + props.customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG)
                        : ToastAndroid.show('You have redeemed ' + props.customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG);
                    NavigationService.navigate('Home');
                }*/
            } else {
                props.customerDetails.otp = null;
                dispatch({type:"SET_CUSTOMER_DETAILS", payload:props.customerDetails});
                inputRef.clear();
                ToastAndroid.show('Incorrect One Time Password. Please enter again!', ToastAndroid.LONG);
            }
        }).catch((error) => {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

export function generateOtp(props) {
    return dispatch => {
        RestService.postJSON(apis.getUrls().customer.generateOTP, props.customerDetails).then((response) => {
            dispatch(PubnubActions.publish({OTP_SENT: props.customerDetails}));
            dispatch({
                type: "IS_MODAL_VISIBLE",
                payload: true
            });
            NavigationService.navigate("EnterOtp");
        }).catch((error) => {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

export function cancelOtp(props) {
    return dispatch => {
        dispatch({type: 'IS_MODAL_VISIBLE', payload: false});
        switch (props.otpSource) {
            case 'fromSecondChai':
                NavigationService.navigate("Redemption");
                break;
            default:
                props.customerDetails.chaiRedeemed = 0;
                dispatch({type: 'SET_CUSTOMER_DETAILS', payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                NavigationService.navigate("Redemption");
        }
    }
}

export function openOTPModal(source, headline) {
    return dispatch => {
        dispatch({type: "OTP_SOURCE", payload: source});
        dispatch({type: "IS_MODAL_VISIBLE", payload: true});
        dispatch({type: "SET_OTP_HEADLINE", payload: headline});
        NavigationService.navigate("EnterOtp");
    }
}