import RestService from "./../services/RestService";
import UtilityService from "./../services/UtilityService";
import ConfigService from "./../services/ConfigService";
import NavigationService from "./../services/NavigationService";
import StorageService from "./../services/StorageService";
import apis from "./../services/APIs";
import {Alert, ToastAndroid} from "react-native";
import FaceTracker from './../../FaceTracker';
import RNFS from "react-native-fs";
import * as PubnubActions from "./PubnubActions";
import * as OtpActions from "./OtpActions";

export function startFaceTracking(props) {
    return dispatch => {
        const saveImage = ConfigService.getSaveFaceImage();
        try {
            dispatch(PubnubActions.publish({"FACE_PROCESS_STARTED": {}}));
            FaceTracker.trackFaces(props.sessionId, saveImage).then(function (data) {
                if (data == "cancel") {
                    ToastAndroid.show('Error capturing face. Try again!', ToastAndroid.LONG);
                    dispatch(reset());
                } else if (data == "no_face") {
                    ToastAndroid.show('No faces captured in camera. Try again!', ToastAndroid.LONG);
                    dispatch(reset());
                } else {
                    dispatch(setFaceScanning(true));
                    dispatch(setFaceCaptured(true));
                    dispatch(setFaceString(data));
                    dispatch(faceLookup(data, props));
                    /*UtilityService.getFileData(RNFS.ExternalStorageDirectoryPath + "/Android/data/FaceTracker/scaledb64.txt").then((data) => {
                        console.log(data);
                    }).catch((err) => {
                        console.log(err.message);
                    });*/
                }
            }, function (error) {
                console.log(error);
                ToastAndroid.show('Error capturing face. Try again!', ToastAndroid.LONG);
                dispatch(reset());
            });
        } catch (error) {
            console.log(error);
        }

    }
}

export function setFaceCaptured(faceCaptured) {
    return dispatch => {
        dispatch({type: "SET_FACE_CAPTURED", payload: faceCaptured});
    }
}

export function setFaceString(faceString) {
    return dispatch => {
        dispatch({type: "SET_FACE_STRING", payload: faceString});
        UtilityService.setFaceString(faceString);
    }
}

export function setFaceScanning(faceScanning) {
    return dispatch => {
        dispatch({type: "SET_FACE_SCANNING", payload: faceScanning});
    }
}

export function faceLookup(face, props) {
    return dispatch => {
        const reqObj = {
            contact: "",
            faceImage: face,
            batchId: ConfigService.getBatchId(),
            unitId: UtilityService.getUnitId(),
            terminalId: UtilityService.getTerminalId(),
            sessionId: props.sessionId
        };
        var time = new Date().getTime();
        UtilityService.setFaceLookupCallAttemp(parseInt(UtilityService.getFaceLookupCallAttemp())+1);
        RestService.postJSON(apis.getUrls().rekog.lookup, reqObj).then((response) => {
            console.log(new Date().getTime() - time);
            console.log("face lookup response is: ", response);
            if (response != null && response.contact != null) {
                dispatch(setCustomer(response, props));
            } else {
                ToastAndroid.show('Face not found in database. Please login manually!', ToastAndroid.LONG);
                NavigationService.navigate("CustomerData");
            }
        }).catch((error) => {
            console.log(error);
            if(UtilityService.getFaceLookupCallAttemp() > 1) {
                NavigationService.navigate("CustomerData");
            } else {
                ToastAndroid.show('Face lookup failed. Trying again!', ToastAndroid.LONG);
                dispatch(faceLookup(face, props));
            }

        });
    }
}

export function setCustomer(res, props) {
    return dispatch => {
        console.log('sign in response ', response);
        let response = {...res};
        if (response != null && response.contact != null) {
            dispatch({type: "LOGIN_BY_FACE", payload: true});
            response.otpVerified = true;
            UtilityService.setLoginByFace(true);
            dispatch(PubnubActions.publish({PROCESS_STARTED: response}));
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: response});
            UtilityService.setCustomerDetails(response);
            dispatch(PubnubActions.publish({DETAILS_ENTERED: response}));

            if (response.newCustomer && !response.eligibleForSignupOffer) {
                dispatch(PubnubActions.publish({NEW_CUSTOMER: response}));

                dispatch({type: "SET_OTP_DISABLED", payload: false});
                dispatch({type: "SET_USER_NAME_DISABLED", payload: false});
                dispatch({type: "SET_OTP_SENT_COUNT", payload: props.otpSentCount + 1});
                // dispatch({type: "SET_SHOW_EMAIL_INPUT", payload: true});
            } else if (response.name == "" || response.name == null) {
                dispatch({type: "SET_SHOW_UPDATE_USER_NAME", payload: true});
            } 
           /* else if (response.contactVerified && (response.email == null || response.email == "")) {
                dispatch({type: "SET_SHOW_EMAIL_INPUT", payload: true});
                dispatch(PubnubActions.publish({SKIP_EMAIL_FORM: response}));
                NavigationService.navigate("EnterEmail");
                /!*if(faceString != null) {
                    dispatch(faceSignup(response.name, response.contact, faceString));
                }*!/
            }*/
            if (response.eligibleForSignupOffer) {
                console.log('eligibleForSignupOffer');
                if (!response.otpVerified && UtilityService.getLoginByFace() != true) {
                    dispatch(OtpActions.openOTPModal('fromSecondChai', "One time password to redeem your loyality chai"))
                    /*dispatch({
                        type: "OTP_SOURCE",
                        payload: 'fromSecondChai'
                    });
                    dispatch({
                        type: "IS_MODAL_VISIBLE",
                        payload: true
                    });
                    dispatch({
                        type: "SET_OTP_HEADLINE",
                        payload: "One time password to redeem your loyality chai"
                    });
                    NavigationService.navigate("EnterOtp");*/
                } else {
                    let customerDetails = response;
                    customerDetails.chaiRedeemed = 1;
                    customerDetails.productId = 10;
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: customerDetails});
                    UtilityService.setCustomerDetails(customerDetails);
                    dispatch(PubnubActions.publish({REDEMPTION: customerDetails}));
                    ToastAndroid.show('You have redeemed your free loyality chai!', ToastAndroid.LONG);
                    NavigationService.navigate('Home');
                }
            } else if (response.contactVerified && response.name != null && response.name != "") {
                NavigationService.navigate("Redemption");
            }
        } else {
            dispatch({type: "SET_CONTACT_NUMBER", payload: null});
            if (response.errorMessage != null && response.errorMessage.indexOf('Customer') >= 0) {
                ToastAndroid.show(response.data.errorMessage, ToastAndroid.LONG);
            } else {
                ToastAndroid.show('Please enter contact number again!', ToastAndroid.LONG);
            }
            NavigationService.navigate("CustomerData");
        }
        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
    }
}

export function reset() {
    return dispatch => {
        dispatch(setFaceCaptured(false));
        dispatch(setFaceScanning(false));
        //dispatch(setCustomer(null));
        dispatch(setFaceString(null));
    }
}

export function setFaceSync(data) {
    return dispatch => {
        UtilityService.setFaceSync(data);
        if (data == true) {
            dispatch(startFaceSyncInBackground());
        }
    }
}

export function startFaceSyncInBackground() {
    return dispatch => {
        UtilityService.setFaceSync(true);
        // get a list of files and directories in the main bundle
        RNFS.readDir(RNFS.ExternalStorageDirectoryPath + "/Android/data/FaceTracker")
            .then((dirList) => {
                //console.log('GOT RESULT', dirList);
                if (dirList.length > 0) {
                    if (dirList[0].isDirectory()) {
                        RNFS.readDir(dirList[0].path).then((dir) => {
                            //console.log('GOT RESULT', dir);
                            // stat the first file
                            dir.map(function (file) {
                                if (file.name == "originalb64.txt") {
                                    RNFS.readFile(file.path, 'utf8').then((data) => {
                                        //console.log(data);
                                        //TODO call sync API
                                        //TODO on success delete folder
                                        const reqObj = {
                                            sessionId: UtilityService.getSessionIdFromFileName(dirList[0].name),
                                            faceImage: data
                                        };
                                        RestService.postJSON(apis.getUrls().sync.syncOriginalImage, reqObj).then((response) => {
                                            if (response == true) {
                                                RNFS.unlink(dirList[0].path).then((result) => {
                                                    //console.log('FILE DELETED', result);
                                                    if (UtilityService.getFaceSync() == true) {
                                                        dispatch(startFaceSyncInBackground())
                                                    }
                                                }).catch((err) => {
                                                    console.log(err.message);
                                                });
                                            }
                                        }).catch((error) => {
                                            console.log(error);
                                        });
                                    }).catch((err) => {
                                        console.log(err);
                                    })
                                }
                            })
                        })
                    }
                }
            })
            .catch((err) => {
                console.log(err.message);
            });
    }
}

export function skipFaceLogin(props) {
    return dispatch => {
        dispatch(PubnubActions.publish({"FACE_LOGIN_SKIPPED_CUSTOMER": {}}));
        NavigationService.navigate("CustomerData");
    }
}