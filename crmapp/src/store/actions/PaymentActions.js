import RestService from "../services/RestService";
import UtilityService from "../services/UtilityService";
import apis from "./../services/APIs";
import {ToastAndroid} from "react-native";
import * as PubnubActions from "./PubnubActions";
import NavigationService from "../services/NavigationService";
import configService from "../services/ConfigService";

export function createPaytmQr(props, paymentId, paymentMode) {
    return dispatch => {

        const request = {
            generateOrderId: paymentId,
            paymentModeId: 13,
            redirectUrl: '',
            paidAmount: props.orderDetails.amount,
            paymentSource: 'KETTLE_SERVICE',
            contactNumber: props.customerDetails.contact,
            customerName: props.customerDetails.name,
            cartId: props.orderDetails.cartId,
            customerId: props.customerDetails.id,
            posId: UtilityService.getUnitId() + "#" + UtilityService.getTerminalId()
        }

        props.customerDetails.paytmOrderId = paymentId;
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
        UtilityService.setCustomerDetails(props.customerDetails);

        RestService.postJSON(apis.getUrls().orderManagement.generatePaytmQr, request).then((response) => {
            console.log('create patym qr ', response);
            if (response == null) {
                console.log('payment already successed');
                dispatch({type: "SET_QR_CODE", payload: null});
                dispatch({
                    type: "SET_SHOW_LOADER",
                    payload: {showLoader: false, loadingMessage: "Payment verified..."}
                });
                dispatch({
                    type: "SET_QR_LOADER",
                    payload: {qrLoader: false, qrLoadingMessage: null}
                });
                props.customerDetails.paymentMode = 'PAYTM';
                props.customerDetails.paymentStatus = 'SUCCESS';
                props.customerDetails.paidAmount = props.orderDetails.amount;
                props.customerDetails.externalOrderId = response.orderId;
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                dispatch(PubnubActions.publish({PAYMENT_VERIFIED: props.customerDetails}));
            } else if (response != null && !UtilityService.checkEmpty(response) && response.qrCodeId !== null) {
                console.log("The Paytm Qr code is : ", response.qrCodeId);
                dispatch({
                    type: "SET_QR_LOADER",
                    payload: {qrLoader: false, qrLoadingMessage: null}
                });
                dispatch({type: "SET_QR_CODE", payload: response.qrCodeId})
                dispatch(setQrCodeString(props, 'PAYTM', response.qrCodeId));
                console.log('checking status for ', response.ORDER_ID);
                dispatch({type: "SET_ORDER_ID", payload: response.ORDER_ID})
                NavigationService.navigate("QrScanner");
            } else {
                ToastAndroid.show("Not able to generate QR, Please try agin later... ", ToastAndroid.LONG);
                NavigationService.navigate("MakePayment");
                dispatch({type: "SET_INTERVAL", payload: 0});
                dispatch({type: "SET_SECONDS_INTERVAL", payload: 0});
                dispatch({type: "SET_PAYMENT_REMAINING_SECONDS", payload: 0});
                dispatch({type: "SET_TIMEOUT", payload: 0});
            }
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});

        }).catch(function (error) {
            console.log("Error Occurred!!", error);
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

export function createGPayQr(props, paymentId, paymentMode) {
    console.log("inside create gPay")
    console.log(props);
    return dispatch => {
        const request = {
            generateOrderId: paymentId,
            paymentModeId: 27,
            redirectUrl: '',
            paidAmount: props.orderDetails.amount,
            paymentSource: 'KETTLE_CRM',
            contactNumber: props.customerDetails.contact,
            customerName: props.customerDetails.name,
            cartId: props.orderDetails.cartId,
            customerId: props.customerDetails.id,
            // posId: UtilityService.getUnitId() + "#" + UtilityService.getTerminalId()
            posId: UtilityService.getSelectedOutlet().googleMerchantId

        }
        if (configService.getEnvironment() === "DEV") {
            request.paidAmount = 1;
        }
        props.customerDetails.gPayOrderId = paymentId;
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
        UtilityService.setCustomerDetails(props.customerDetails);
        RestService.postJSON(apis.getUrls().orderManagement.generateGpayQr, request).then((response) => {
            console.log('create gpay qr ', response);
            if (response == null) {
                console.log('payment already successed');
                dispatch({type: "SET_QR_CODE", payload: null});
                dispatch({
                    type: "SET_SHOW_LOADER",
                    payload: {showLoader: false, loadingMessage: "Payment verified..."}
                });
                dispatch({
                    type: "SET_QR_LOADER",
                    payload: {qrLoader: false, qrLoadingMessage: null}
                });
                props.customerDetails.paymentMode = 'GOOGLE PAY';
                props.customerDetails.paymentStatus = 'SUCCESS';
                props.customerDetails.paidAmount = props.orderDetails.amount;
                props.customerDetails.externalOrderId = response.orderId;
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                dispatch(PubnubActions.publish({PAYMENT_VERIFIED: props.customerDetails}));
            } else if (response != null && !UtilityService.checkEmpty(response) && response.qrCodeId !== null) {
                console.log("The GPay Qr code is : ", response.qrCodeId);
                dispatch({
                    type: "SET_QR_LOADER",
                    payload: {qrLoader: false, qrLoadingMessage: null}
                });
                dispatch({type: "SET_QR_CODE", payload: response.qrCodeId})
                dispatch(setQrCodeString(props, 'GOOGLE PAY', response.qrCodeId));
                console.log('checking status for ', response.orderId);
                UtilityService.setGPayAccessToken(response.accessToken);
                props.customerDetails.accessToken = response.accessToken;
                dispatch({type: "SET_ORDER_ID", payload: response.orderId})
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                dispatch(PubnubActions.publish({PAYMENT_MODE_BY_CUSTOMER: props.customerDetails}));
                console.log('props is ',props.customerDetails)
                UtilityService.setCustomerDetails(props.customerDetails);
                NavigationService.navigate("QrScanner");
            } else {
                ToastAndroid.show("Not able to generate QR, Please try agin later... ", ToastAndroid.LONG);
                NavigationService.navigate("MakePayment");
                dispatch({type: "SET_INTERVAL", payload: 0});
                dispatch({type: "SET_SECONDS_INTERVAL", payload: 0});
                dispatch({type: "SET_PAYMENT_REMAINING_SECONDS", payload: 0});
                dispatch({type: "SET_TIMEOUT", payload: 0});
            }
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});

        }).catch(function (error) {
            console.log("Error Occurred!!", error);
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

function createPaymentRequest(props, paymentMode) {
    return dispatch => {
        let paymentId = 'PI' + UtilityService.getUnitId() + UtilityService.getTerminalId() + Math.random().toString(36).slice(-2);

        props.customerDetails.paymentMode = paymentMode;
        paymentMode == 'BHARAT_QR' ? props.customerDetails.bharatOrderId = paymentId : props.customerDetails.upiOrderId = paymentId;
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
        UtilityService.setCustomerDetails(props.customerDetails);
        dispatch(PubnubActions.publish({PAYMENT_MODE_BY_CUSTOMER: props.customerDetails}));
        return request = {
            generateOrderId: paymentId,
            paidAmount: props.orderDetails.amount,
            paymentSource: 'KETTLE_SERVICE',
            contactNumber: props.customerDetails.contact,
            customerName: props.customerDetails.name,
            cartId: props.orderDetails.cartId,
            customerId: props.customerDetails.id,
            posId: UtilityService.getUnitId() + "#" + UtilityService.getTerminalId(),
            paymentModeName: paymentMode,
            ingenicoRequestType: 'TQR'
        }
    }
}

export function createIngenicoQr(props, paymentMode) {
    return dispatch => {
        let request = dispatch(createPaymentRequest(props, paymentMode));
        dispatch({
            type: "SET_QR_LOADER",
            payload: {qrLoader: true, qrLoadingMessage: "Please wait while generating QR..."}
        });

        RestService.postJSON(apis.getUrls().orderManagement.ingenicoGenerateQr, request).then((response) => {
            console.log('create ingenico qr ', response);
            if (response != null && response.merchantAdditionalDetails != null && response.merchantAdditionalDetails != "" && response.merchantAdditionalDetails != 'NA') {
                const qrCode = response.merchantAdditionalDetails;
                console.log("The ingenico Qr code is : ", qrCode);
                dispatch({type: "SET_QR_CODE", payload: qrCode});
                props.customerDetails.paymentStatus = "Initiated";
                dispatch(setQrCodeString(props, paymentMode == 'BHARAT_QR' ? paymentMode : 'UPI', qrCode));
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                NavigationService.navigate("QrScanner");
            } else {
                ToastAndroid.show("Not able to generate QR, Please try agin later... ", ToastAndroid.LONG);
                NavigationService.navigate("MakePayment");
                dispatch({type: "SET_INTERVAL", payload: 0});
                dispatch({type: "SET_SECONDS_INTERVAL", payload: 0});
                dispatch({type: "SET_PAYMENT_REMAINING_SECONDS", payload: 0});
                dispatch({type: "SET_TIMEOUT", payload: 0});
            }
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            dispatch({type: "SET_QR_LOADER", payload: {qrLoader: false, qrLoadingMessage: null}});

        }).catch(function (error) {
            console.log("Error Occurred!!", error);
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            dispatch({type: "SET_QR_LOADER", payload: {qrLoader: false, qrLoadingMessage: null}});
        });
    }
}

export function checkIngenicoStatus(props, paymentMode) {
    return dispatch => {
        let request = dispatch(createPaymentRequest(props, paymentMode));
        request.ingenicoRequestType = 'O';
        request.generateOrderId = paymentMode == 'BHARAT_QR' ? props.customerDetails.bharatOrderId : props.customerDetails.upiOrderId;
        dispatch({
            type: "SET_SHOW_LOADER",
            payload: {showLoader: true, loadingMessage: "Please wait while checking payment status..."}
        });

        RestService.postJSON(apis.getUrls().orderManagement.ingenicoPaymentStatusCheck, request).then((response) => {
            console.log('payment status of ingenico ', response);
            dispatch(paymentStatus(response, props.customerDetails));
        }).catch(function (error) {
            console.log("Error Occurred while checking payment status!! ", error);
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }

}

export function paymentStatus(response, data) {
    return dispatch => {
        if (response != null && response.paymentMethod && response.paymentMethod.paymentTransaction && response.paymentMethod.paymentTransaction.statusCode == "0300") {
            dispatch({type: "SET_QR_CODE", payload: null});
            dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: false, loadingMessage: "Payment verified..."}
            });
            data.paymentStatus = 'SUCCESS';
            data.paymentMode = 'INGENICO';
            data.externalOrderId = response.orderId;
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: data});
            UtilityService.setCustomerDetails(data);
            dispatch(PubnubActions.publish({PAYMENT_VERIFIED: data}));
        } else {
            dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: false, loadingMessage: "Payment awaited..."}
            });
            data.paymentStatus = 'AWAITED';
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: data});
            UtilityService.setCustomerDetails(data);
        }
    }
}

export function setPaymentRemainingTime() {
    return dispatch => {
        let durationSeconds = 600;
        dispatch({type: "SET_PAYMENT_REMAINING_SECONDS", payload: durationSeconds});
        var seconds = setInterval(function () {
            if (durationSeconds > 0) {
                durationSeconds = durationSeconds - 1;
            }
            dispatch({type: "SET_PAYMENT_REMAINING_SECONDS", payload: durationSeconds});
        }, 1000);
        dispatch({type: "SET_SECONDS_INTERVAL", payload: seconds});
    }
}

export function checkPaytmQRPaymentStatus(props, paymentId) {
    return dispatch => {
        console.log('checkPaytmQRPaymentStatus ', paymentId);

        // dispatch(setPaymentRemainingTime());

        var interval = setInterval(function () {
            console.log("checking paytm payment status ::::::::::::::::::");
            const request = {generateOrderId: paymentId, paymentPartner: "PAYTMQR"}
            RestService.postJSON(apis.getUrls().orderManagement.checkPaymentStatus, request)
                .then((response) => {
                    console.log("Payment response is ::::::::::", response);
                    if (response != null && response.paymentStatus !== undefined) {
                        let payTMPaymentStaus = response.paymentStatus;
                        console.log("payTMPaymentStaus... ", payTMPaymentStaus);
                        if (payTMPaymentStaus === "TXN_SUCCESS") {
                            console.log("Payment TXN_SUCCESS is ::::::::::", response);

                            clearInterval(interval);
                            dispatch({type: "SET_QR_CODE", payload: null});
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: false, loadingMessage: "Payment verified..."}
                            });
                            props.customerDetails.paymentMode = 'PAYTM';
                            props.customerDetails.paymentStatus = 'SUCCESS';
                            props.customerDetails.paidAmount = props.orderDetails.amount;
                            props.customerDetails.externalOrderId = response.orderId;
                            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                            UtilityService.setCustomerDetails(props.customerDetails);
                            dispatch(PubnubActions.publish({PAYMENT_VERIFIED: props.customerDetails}));
                        }
                        else if (payTMPaymentStaus === "PENDING") {
                            console.log("Payment pending.... ", response);
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: true, loadingMessage: "Waiting for payment..."}
                            });
                        }
                        else if (payTMPaymentStaus === "TXN_FAILURE") {
                            clearInterval(interval);
                            console.log("Payment failed.... ", response);
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: false, loadingMessage: "Payment failed..."}
                            });
                            dispatch({type: "SET_QR_CODE", payload: null});
                            ToastAndroid.show("Transaction failure.Please try again later!", ToastAndroid.LONG);
                            props.customerDetails.paymentMode = 'PAYTM';
                            props.customerDetails.paymentStatus = 'FAILURE';
                            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                            UtilityService.setCustomerDetails(props.customerDetails);
                            // setTimeout(function () {
                            NavigationService.navigate("Home");
                            // }, 3 * 1000);
                            dispatch(PubnubActions.publish({TRANSACTION_FAILED_BY_CUSTOMER: props.customerDetails}));
                        }
                        else {
                            console.log("Invalid status received ", JSON.stringify(response));
                            clearInterval(interval);
                            dispatch({type: "SET_QR_CODE", payload: null});
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: false, loadingMessage: "Payment failed..."}
                            });
                            ToastAndroid.show("Something went wrong.Please try again later!", ToastAndroid.LONG);

                            props.customerDetails.paymentMode = 'PAYTM';
                            props.customerDetails.paymentStatus = 'FAILURE';
                            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                            UtilityService.setCustomerDetails(props.customerDetails);
                            // setTimeout(function () {
                            NavigationService.navigate("Home");
                            // }, 3 * 1000);
                            dispatch(PubnubActions.publish({TRANSACTION_FAILED_BY_CUSTOMER: props.customerDetails}));
                        }
                    } else {
                        clearInterval(interval);
                    }
                }).catch((error) => {
                clearInterval(interval);
                dispatch({type: "SET_QR_CODE", payload: null});
                dispatch({
                    type: "SET_SHOW_LOADER",
                    payload: {showLoader: false, loadingMessage: null}
                });
                props.customerDetails.paymentMode = 'PAYTM';
                props.customerDetails.paymentStatus = 'FAILURE';
                if (props.contactEntered) {
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                    UtilityService.setCustomerDetails(props.customerDetails);
                }
                dispatch(PubnubActions.publish({TRANSACTION_FAILED_BY_CUSTOMER: props.customerDetails}));
                setTimeout(function () {
                    NavigationService.navigate("Redemption");
                }, 3 * 1000);
                console.log("Payment failed response::::::::::::", JSON.stringify(error));
            });
        }, 5 * 1000); // 5 sec

        dispatch({type: "SET_INTERVAL", payload: interval});
    }
}


export function checkGPayQRPaymentStatus(props, paymentId) {
    return dispatch => {
        console.log('checkGPayQRPaymentStatus ', paymentId);

        // dispatch(setPaymentRemainingTime());

        var interval = setInterval(function () {
            console.log("checking google pay payment status ::::::::::::::::::");
            const request = {
                posId: UtilityService.getSelectedOutlet().googleMerchantId,
                generateOrderId: paymentId,
                accessToken: UtilityService.getGPayAccessToken()
            }
            RestService.postJSON(apis.getUrls().orderManagement.generateGpayStatus, request)
                .then((response) => {
                    console.log("Payment response is ::::::::::", response);
                    if (response != null && response.paymentStatus !== undefined) {
                        let gPayPaymentStaus = response.paymentStatus;
                        console.log("gPayPaymentStaus... ", gPayPaymentStaus);
                        if (gPayPaymentStaus === "SUCCESSFUL") {
                            console.log("Payment SUCCESSFUL is ::::::::::", response);

                            clearInterval(interval);
                            dispatch({type: "SET_QR_CODE", payload: null});
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: false, loadingMessage: "Payment verified..."}
                            });
                            props.customerDetails.paymentMode = 'GOOGLE PAY';
                            props.customerDetails.paymentStatus = 'SUCCESS';
                            props.customerDetails.paidAmount = props.orderDetails.amount;
                            props.customerDetails.externalOrderId = response.orderId;
                            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                            UtilityService.setCustomerDetails(props.customerDetails);
                            dispatch(PubnubActions.publish({PAYMENT_VERIFIED: props.customerDetails}));
                        }
                        else if (gPayPaymentStaus === "INITIATED") {
                            console.log("Payment pending.... ", response);
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: true, loadingMessage: "Waiting for payment..."}
                            });
                        }
                        // else if (gPayPaymentStaus === "TXN_FAILURE") {
                        //     clearInterval(interval);
                        //     console.log("Payment failed.... ", response);
                        //     dispatch({
                        //         type: "SET_SHOW_LOADER",
                        //         payload: {showLoader: false, loadingMessage: "Payment failed..."}
                        //     });
                        //     dispatch({type: "SET_QR_CODE", payload: null});
                        //     ToastAndroid.show("Transaction failure.Please try again later!", ToastAndroid.LONG);
                        //     props.customerDetails.paymentMode = 'GPAY';
                        //     props.customerDetails.paymentStatus = 'FAILURE';
                        //     dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                        //     UtilityService.setCustomerDetails(props.customerDetails);
                        //     // setTimeout(function () {
                        //     NavigationService.navigate("Home");
                        //     // }, 3 * 1000);
                        //     dispatch(PubnubActions.publish({TRANSACTION_FAILED_BY_CUSTOMER: props.customerDetails}));
                        // }
                        else {
                            console.log("Invalid status received ", JSON.stringify(response));
                            clearInterval(interval);
                            dispatch({type: "SET_QR_CODE", payload: null});
                            dispatch({
                                type: "SET_SHOW_LOADER",
                                payload: {showLoader: false, loadingMessage: "Payment failed..."}
                            });
                            ToastAndroid.show("Something went wrong.Please try again later!", ToastAndroid.LONG);

                            props.customerDetails.paymentMode = 'GOOGLE PAY';
                            props.customerDetails.paymentStatus = 'FAILURE';
                            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                            UtilityService.setCustomerDetails(props.customerDetails);
                            // setTimeout(function () {
                            NavigationService.navigate("Home");
                            // }, 3 * 1000);
                            dispatch(PubnubActions.publish({TRANSACTION_FAILED_BY_CUSTOMER: props.customerDetails}));
                        }
                    } else {
                        clearInterval(interval);
                    }
                }).catch((error) => {
                clearInterval(interval);
                dispatch({type: "SET_QR_CODE", payload: null});
                dispatch({
                    type: "SET_SHOW_LOADER",
                    payload: {showLoader: false, loadingMessage: null}
                });
                props.customerDetails.paymentMode = 'GOOGLE PAY';
                props.customerDetails.paymentStatus = 'FAILURE';
                if (props.contactEntered) {
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                    UtilityService.setCustomerDetails(props.customerDetails);
                }
                dispatch(PubnubActions.publish({TRANSACTION_FAILED_BY_CUSTOMER: props.customerDetails}));
                setTimeout(function () {
                    NavigationService.navigate("Redemption");
                }, 3 * 1000);
                console.log("Payment failed response::::::::::::", JSON.stringify(error));
            });
        }, 5 * 1000); // 5 sec

        dispatch({type: "SET_INTERVAL", payload: interval});
    }
}


export function transactionTimedout(props) {
    return dispatch => {
        dispatch({type: "SET_QR_CODE", payload: null});
        if (props.contactEntered) {
            props.customerDetails.paymentMode = 'PAYTM';
            props.customerDetails.paymentStatus = 'TIMED_OUT';
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
            UtilityService.setCustomerDetails(props.customerDetails);
        }
        ToastAndroid.show("Transaction timed out.Please try again later!", ToastAndroid.LONG);
        dispatch(PubnubActions.publish({TRANSACTION_TIMED_OUT_BY_CUSTOMER: props.customerDetails}));
        NavigationService.navigate("Home");
    }
}

function getPaytmId(paymentModes) {
    let arr = paymentModes.filter((mode) => {
        return mode.name == 'Paytm'
    });
    return arr;
}

export function checkOrGenerateQr(props, modeId, paymentMode) {
    return dispatch => {

        const keyValue = getValueFromMap(props, paymentMode);
        // const keyValue = null;
        dispatch({
            type: "SET_QR_LOADER",
            payload: {qrLoader: true, qrLoadingMessage: "Please wait while generating QR..."}
        });
        if (modeId == 13) {
            if (keyValue == null || keyValue == undefined) {
                let paymentId = 'POS_PAYTM_U' + UtilityService.getUnitId() + 'T' + UtilityService.getTerminalId() + new Date().getTime();
                props.customerDetails.paytmTmId = paymentId;
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                dispatch(createPaytmQr(props, paymentId, paymentMode));
                dispatch(checkPaytmQRPaymentStatus(props, paymentId));
            } else {
                dispatch({
                    type: "SET_QR_LOADER",
                    payload: {qrLoader: false, qrLoadingMessage: null}
                });
                dispatch({type: "SET_QR_CODE", payload: keyValue});
                dispatch(checkPaytmQRPaymentStatus(props, props.customerDetails.paytmTmId ));
            }
        } else if (modeId == 25 && paymentMode != 'BHARAT_QR') {
            const keyValue = getValueFromMap(props, 'UPI');
            if (keyValue == null || keyValue == undefined) {
                dispatch(createIngenicoQr(props, paymentMode));
            } else {
                const request = dispatch(createPaymentRequest(props, paymentMode));
                RestService.postJSON(apis.getUrls().orderManagement.ingenicoPaymentModeUpdate, request).then((response) => {
                    console.log('updated igenico payment mode name ');
                    dispatch({type: "SET_QR_LOADER", payload: {qrLoader: false, qrLoadingMessage: null}});
                    dispatch({type: "SET_QR_CODE", payload: keyValue});
                }).catch(function (error) {
                    console.log("Error Occurred while updating payment status!! ", error);
                    dispatch({type: "SET_QR_LOADER", payload: {qrLoader: false, qrLoadingMessage: null}});
                });
            }
        } else if (modeId == 27) {
            console.log("inside 27")
            console.log(UtilityService.getSelectedOutlet())
            if (keyValue == null || keyValue == undefined) {
                let paymentId = 'GPAY' + UtilityService.getUnitId() + 'T' + UtilityService.getTerminalId() + new Date().getTime();
                props.customerDetails.gPayId = paymentId;
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                UtilityService.setCustomerDetails(props.customerDetails);
                dispatch(createGPayQr(props, paymentId, paymentMode));
                dispatch(checkGPayQRPaymentStatus(props, paymentId));
            } else {
                dispatch({
                    type: "SET_QR_LOADER",
                    payload: {qrLoader: false, qrLoadingMessage: null}
                });
                dispatch({type: "SET_QR_CODE", payload: keyValue});
                dispatch(checkGPayQRPaymentStatus(props, props.customerDetails.gPayId));
            }
        } else { // in case of BHARAT_QR
            if (keyValue == null || keyValue == undefined) {
                dispatch(createIngenicoQr(props, paymentMode));
            } else {
                dispatch({type: "SET_QR_LOADER", payload: {qrLoader: false, qrLoadingMessage: null}});
                dispatch({type: "SET_QR_CODE", payload: keyValue});
            }

        }
        props.customerDetails.paymentMode = paymentMode;
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
        UtilityService.setCustomerDetails(props.customerDetails);
        dispatch(PubnubActions.publish({PAYMENT_MODE_BY_CUSTOMER: props.customerDetails}));
    }
}

function getValueFromMap(props, sourceKey) {
    let qrString = null;

        Array.from(props.paymentQr, ([key, value]) => {
            if (key == sourceKey) {
                qrString = value;
            }
        });

    return qrString;
}

function setQrCodeString(props, item, qrCodeString) {
    return dispatch => {
        Array.from(props.paymentQr, ([key, value]) => {
            if (key == item) {
                props.paymentQr.set(key, qrCodeString);
                dispatch({type: "SET_PAYMENT_QR_MAP", payload: props.paymentQr});
            }
        });
    }
}

export function updateOrderDetails(orderDetails) {
    return dispatch => {
        dispatch({type: "ORDER_DETAILS", payload: orderDetails});
    }
}