import RestService from "./../services/RestService";
import UtilityService from "./../services/UtilityService";
import apis from "./../services/APIs";
import {ToastAndroid} from "react-native";
import * as PubnubActions from "./PubnubActions";
import NavigationService from "../services/NavigationService";
import {verifyNumber} from "./CustomerActions";

export function redeemChai(number, props) {
    return dispatch => {
        props.customerDetails.chaiRedeemed = number;
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
        UtilityService.setCustomerDetails(props.customerDetails);
        if (!props.customerDetails.otpVerified && !props.loginByFace) {
            if (number > 0) {
                RestService.postJSON(apis.getUrls().customer.generateOTP, props.customerDetails).then((response) => {
                    dispatch(PubnubActions.publish({OTP_SENT: props.customerDetails}));
                    dispatch({
                        type: "IS_MODAL_VISIBLE",
                        payload: true
                    });
                    dispatch({
                        type: "SET_OTP_HEADLINE",
                        payload: "to redeem your free chai"
                    });
                    NavigationService.navigate("EnterOtp");
                }).catch((error) => {
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                });
            } else {
                dispatch(PubnubActions.publish({REDEEM_LATER: props.customerDetails}));
                //NavigationService.navigate('Home');
            }
        } else {
            /*dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
            UtilityService.setCustomerDetails(props.customerDetails);*/
            if (number > 0) {
                dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                props.customerDetails.chaiRedeemed > 1 ? ToastAndroid.show('You have redeemed ' + props.customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG)
                    : ToastAndroid.show('You have redeemed ' + props.customerDetails.chaiRedeemed + ' chais!', ToastAndroid.LONG);
            } else {
                dispatch(PubnubActions.publish({REDEEM_LATER: props.customerDetails}));
            }
            //NavigationService.navigate('Home');
        }
    }
}

export function sendMessage(msg) {
    return dispatch => {
        dispatch(PubnubActions.publish({REDEMPTION: msg}));
    }
}

