import {applyMiddleware, createStore} from "redux";
import {createLogger} from "redux-logger";
import thunk from "redux-thunk";
import promise from "redux-promise-middleware";
import reducer from "./reducers";
import ConfigService from "./services/ConfigService";

let middleware = applyMiddleware(promise, thunk); // remove createLogger if not debugging in chrome otherwise app will render very slow
if (ConfigService.getEnvironment() == 'SPROD') {
    middleware = applyMiddleware(promise, thunk);
}

export default createStore(reducer, middleware);
