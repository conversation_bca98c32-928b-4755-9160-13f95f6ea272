class ConfigService {

    env = "DEV"; //DEV, STAGE, SPROD
    batchId = "000001";
    saveFaceImage = false;

    getPubnubEnv() {
        switch (this.env) {
            case "DEV":
                return "DEV";
            case "STAGE":
                return "DEV";
            case "SPROD":
                return "SPROD";
        }
    }

    getEnvironment() {
        return this.env;
    }

    getBaseUrl() {
        switch (this.env) {
            case "DEV":
                return "http://dev.kettle.chaayos.com:9595";
            case "STAGE":
                return "http://stage.kettle.chaayos.com:9191";
            case "SPROD":
                return "https://internal.chaayos.com";
        }
    }

    getRekognitionBaseUrl() {
        return this.getBaseUrl() + "/rekognition-service/rest/v1/"
    }

    getPubnubPublishKey() {
        switch (this.env) {
            case "DEV":
                return '******************************************';
            case "STAGE":
                return '******************************************';
            case "SPROD":
                return '******************************************';
        }
    }

    getPubnubSubscribeKey() {
        switch (this.env) {
            case "DEV":
                return '******************************************';
            case "STAGE":
                return '******************************************';
            case "SPROD":
                return '******************************************';
        }
    }

    getBatchId() {
        return this.batchId;
    }

    getSaveFaceImage() {
        return this.saveFaceImage;
    }
}

const configService = new ConfigService();

export default configService;
