import ConfigService from "./ConfigService";

class APIs {

    constructor() {
        this.getUrls = this.getUrls.bind(this);
    }

    constructURLs() {
        this.baseUrl = ConfigService.getBaseUrl();
        this.rekognitionBaseUrl = ConfigService.getRekognitionBaseUrl();
        /*if (env == 'SPROD') {
            this.baseUrl = "https://internal.chaayos.com";
        } else {
            // this.baseUrl = "http://172.16.16.101:8080";
            this.baseUrl = "http://dev.kettle.chaayos.com:9595";
            //this.baseUrl = "http://uat.tech.chaayos.com:9191";
        }

        //this.neoBaseUrl = "http://172.16.16.89:8080/neo-service/rest/v1/";

        this.rekognitionBaseUrl = "http://dev.kettle.chaayos.com:9595/rekognition-service/rest/v1/";
        //this.rekognitionBaseUrl = "http://uat.tech.chaayos.com:9191/rekognition-service/rest/v1/";
        //this.rekognitionBaseUrl = "https://internal.chaayos.com/rekognition-service/rest/v1/";*/

        var KETTLE_SERVICE = this.baseUrl + "/kettle-service/rest/v1/";
        var PAYMENT_SERVICE = this.baseUrl + "/kettle-service/rest/v2/";
        var MASTER_SERVICE = this.baseUrl + "/master-service/rest/v1/";
        var CUSTOMER_SERVICE = this.baseUrl + "/kettle-crm/rest/v1/";

        var POS_METADATA_ROOT_CONTEXT = "pos-metadata" + "/";
        var USER_SERVICES_ROOT_CONTEXT = "users/";
        var UNIT_METADATA_ROOT_CONTEXT = "unit-metadata/";
        var ORDER_MANAGEMENT_ROOT_CONTEXT = "order-management/";
        var CRM_SERVICES_ROOT_CONTEXT = "crm" + "/";
        var BRAND_METADATA_ROOT_CONTEXT = "brand-metadata/"
        var USER_MANAGEMENT_ROOT_CONTENT = "user-management/";

        var USER_CONTENT = "user/";

        var REKOGNITION_SERVICES_ROOT_CONTEXT = this.rekognitionBaseUrl + "rekog/";
        var IMAGE_SYNC_ROOT_CONTEXT = this.rekognitionBaseUrl + "sync/";
        var APP_MANAGEMENT_ROOT_CONTEXT = this.rekognitionBaseUrl + "app-manager/";

        this.urls = {
            posMetaData: {
                allUnits: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "all-units",
                userUnits: MASTER_SERVICE + USER_MANAGEMENT_ROOT_CONTENT + USER_CONTENT + "units",
                takeawayUnits: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "takeaway-units",
                crmIdleScreenUrl: KETTLE_SERVICE + POS_METADATA_ROOT_CONTEXT + "get-unit-screen-url"
            },
            users: {
                login: MASTER_SERVICE + USER_SERVICES_ROOT_CONTEXT + "login",
                changePassCode: MASTER_SERVICE + USER_SERVICES_ROOT_CONTEXT + "changePasscode",
                logout: MASTER_SERVICE + USER_SERVICES_ROOT_CONTEXT + "logout"
            },
            customer: {
                signin: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "signin",
                trueCallerSignin: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "trueCallerSignin",
                signup: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "signup",
                trueCallerSignup: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "trueCallerSignup",
                generateOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "generate/otp",
                verifySignup: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "verify/signup",
                verifyOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "verify/otp",
                updateName: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "update-name",
                resendAuthorizationOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "resendCustomerAuthorizationOTP/otp",
                resendRedemptionOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "resendRedemptionOTP/otp",
                overrideContactVerification: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "overrideContactVerification",
                submitFeedback: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "feedback/submit",
                verifyTrueCaller: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "send-truecaller-request",
                getTrueCallerProfile: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "get-truecaller-profile",
                updateTrueCallerProfile: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "update-truecaller-profile",
                saveCustomerSourceAcknowledge: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "save-customer-source-acknowledgement",
                sendAppDownloadLink: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "send-app-download-link",

            },
            unitMetaData: {
                unit: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "unit-data"
            },
            orderManagement: {
                validateGiftCardsInOrder: KETTLE_SERVICE + ORDER_MANAGEMENT_ROOT_CONTEXT + "order/validateGiftCards",
                generatePaytmQr: PAYMENT_SERVICE + "payment-management/payment/paytm/kiosk/qr/create",
                checkPaymentStatus: PAYMENT_SERVICE + "payment-management/payment/paytm/kiosk/qr/status",
                ingenicoGenerateQr: PAYMENT_SERVICE + "payment-management/payment/ingenico/create",
                ingenicoPaymentStatusCheck: PAYMENT_SERVICE + "payment-management/payment/ingenico/paymentStatus",
                ingenicoPaymentModeUpdate: PAYMENT_SERVICE + "payment-management/payment/ingenico/paymentMode/update",
                generateGpayQr: PAYMENT_SERVICE + "payment-management/payment/gpay/qr/create",
                generateGpayStatus: PAYMENT_SERVICE + "payment-management/payment/gpay/qr/status"

            },
            rekog: {
                lookup: REKOGNITION_SERVICES_ROOT_CONTEXT + "look-up",
                signup: REKOGNITION_SERVICES_ROOT_CONTEXT + "sign-up",
            },
            sync: {
                syncOriginalImage: IMAGE_SYNC_ROOT_CONTEXT + "original-image"
            },
            appManagement: {
                getCurrentVersion: APP_MANAGEMENT_ROOT_CONTEXT + "version/current",
                appDownload: APP_MANAGEMENT_ROOT_CONTEXT + "download",
                appVersionUpdate: APP_MANAGEMENT_ROOT_CONTEXT + "version/update",
                appUpload: APP_MANAGEMENT_ROOT_CONTEXT + "upload",
            },
            brandMetaData: {
                getUnitPartnerBrandMetadata: MASTER_SERVICE + BRAND_METADATA_ROOT_CONTEXT + "unit-partner-brand-metadata/get",

            }
        };
    }

    getUrls() {
        this.constructURLs();
        return this.urls;
    }
}

const apis = new APIs();
export default apis;
