import StorageService from "./StorageService";
import ConfigService from "./ConfigService";
import RNFS from "react-native-fs";

class UtilityService {

    constructor() {
        this.authDetail = null;
        this.connected = true;
        this.pubnubChannel = null;
        this.trueCallerChannel = null;
        this.ingenicoPaymentStatusChannel = null;
        this.pubnub = null;
        this.unitId = null;
        this.terminalId = null;
        this.customerDetails = null;
        this.environment = null;
        this.sessionId = null;
        this.faceString = null;
        this.loginByFace = false;
        this.faceSync = false;
        this.orderSessionId = null;
        this.faceLookupCallAttemp = 0;
        this.selectedOutlet = null;
        this.accessToken = null;
        this.unitPartnerBrandMetaData=null;
        this.homeScreenUrl=null;
    }

    setCustomerDetails(details) {
        this.customerDetails = details;
    }

    getCustomerDetails() {
        return this.customerDetails;
    }

    getAuthDetail() {
        return this.authDetail;
    }

    setAuthDetail(data) {
        this.authDetail = data;
    }

    getConnected() {
        return this.connected;
    }

    setConnected(connected) {
        this.connected = connected;
    }

    checkEmpty(obj) {
        return StorageService.checkEmpty(obj);
    }

    formatDate(date, format) {
        let time = new Date(date);
        let yyyy = time.getFullYear();
        let M = time.getMonth() + 1;
        let d = time.getDate();
        let MM = M;
        const monthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        let dd = d;
        let hh = time.getHours();
        let mm = time.getMinutes();
        let ss = time.getSeconds();
        const A = hh < 12 ? "AM" : "PM";
        if (format.indexOf("A") > -1) {
            hh = (hh > 12) ? hh - 12 : hh;
        }
        MM = (M < 10) ? "0" + M : M;
        dd = (d < 10) ? "0" + d : d;
        hh = (hh < 10) ? "0" + hh : hh;
        mm = (mm < 10) ? "0" + mm : mm;
        format = format.replace("yyyy", yyyy);
        format = format.replace("MMM", monthList[M - 1]);
        format = format.replace("MM", MM);
        format = format.replace("dd", dd);
        format = format.replace("hh", hh);
        format = format.replace("mm", mm);
        format = format.replace("ss", ss);
        format = format.replace("A", A);
        return format;
    }

    addDate(days) {
        let time = new Date();
        let d = time.getDate();
        d += days;
        time.setDate(d);
        return new Date(time);
    }

    addDateToDate(date, days) {
        let time = (date != null) ? new Date(date) : new Date(null);
        let d = time.getDate();
        d += days;
        time.setDate(d);
        return new Date(time);
    }

    setUnitId(unitId) {
        this.unitId = unitId;
    }

    getUnitId() {
        return this.unitId;
    }

    setSelectedOutlet(outlet) {
        console.log("setting outlettttttt")
        console.log(outlet)
        this.selectedOutlet = outlet;
    }

    getSelectedOutlet() {

        return this.selectedOutlet;
    }

    setTerminalId(terminalId) {
        this.terminalId = terminalId;
    }

    getTerminalId() {
        return this.terminalId;
    }

    setGPayAccessToken(accessToken) {
        this.accessToken = accessToken;
    }

    getGPayAccessToken() {
        return this.accessToken;
    }

    setEnvironment(env) {
        this.environment = env;
    }

    getEnvironment() {
        return this.environment;
    }


    timeDiff(date, date2) {
        return date.getTime() - new Date(date2).getTime();
    }

    getHandlerSummaryObj() {
        return {
            name: null,
            contact: null,
            retailerCount: 0,
            orderCount: 0,
            target: 0,
            deliveredOrderCount: 0,
            quantity: 0
        }
    }

    getRetailerSummaryObj() {
        return {
            name: null,
            contact: null,
            orderCount: 0,
            target: 0,
            deliveredOrderCount: 0,
            quantity: 0
        }
    }

    setPubNub(data) {
        this.pubnub = data;
    }

    getPubNub() {
        return this.pubnub;
    }

    setPubNubChannel(data) {
        this.pubnubChannel = data;
    }

    getPubNubChannel() {
        return this.pubnubChannel;
    }

    setTrueCallerChannel(data) {
        this.trueCallerChannel = data;
    }

    getTrueCallerChannel() {
        return this.trueCallerChannel;
    }

    setIngenicoStatusChannel(data) {
        this.ingenicoPaymentStatusChannel = data;
    }

    getIngenicoStatusChannel() {
        return this.ingenicoPaymentStatusChannel;
    }


    validName(name) {
        var re = /^[0-9]*$/;
        return !re.test(name);
    }

    validEmail(email) {
        var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email);
    }

    toTitleCase(str) {
        return str.replace(/\w\S*/g, function (txt) {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        });
    }

    getTrueCallerAppId() {
        if (ConfigService.getEnvironment() == 'SPROD') {
            return "si339f683ad63e4d418aca912881c0b927";
        } else {
            return "siefa8829bd81f4713ba5e22101be4ad83";
        }
    }

    getTrueCallerAppName() {
        if (ConfigService.getEnvironment() == 'SPROD') {
            return "CHAAYOS";
        } else {
            return "KETTLE-CUSTOMER-DEV";
        }
    }

    getCurrentTime() {
        var d = new Date();
        var date = d.getDate(); //Current Date
        if (date < 10) {
            date = "0" + date
        }
        var month = d.getMonth() + 1; //Current Month
        if (month < 10) {
            month = "0" + month
        }
        var year = d.getFullYear(); //Current Year
        var hours = d.getHours(); //Current Hours
        var min = d.getMinutes(); //Current Minutes
        var sec = d.getSeconds();
        return "" + year + month + date + hours + min + sec;
    }

    getFileData(path) {
        console.log(path);
        return RNFS.readFile(path, 'utf8');
    }

    getTruecallerQrString() {
        const requestId = encodeURI(this.getUnitId() + "#" + this.getTerminalId() + "*" + new Date().getTime());
        const str = this.getTrueCallerAppId() + "|" + requestId + "|appName:" + this.getTrueCallerAppName();
        console.log("qrcode::::::::::::::" + str);
        return str;
    }

    getPaymentModes(faceDetection) {
        let modes = [
            {
                id: 27,
                name: 'GOOGLE PAY',
                image: require('../../assets/img/gPayLogo.png'),
                sImage: require('../../assets/img/sGPayLogo.png'),
                height: 79,
                width: 233,
                sHeight: 57,
                sWidth: 168,
                selected: false
            }, {
                id: 13,
                name: 'PAYTM',
                image: require('../../assets/img/paytmLogo.png'),
                sImage: require('../../assets/img/sPaytmLogo.png'),
                height: 79,
                width: 233,
                sHeight: 57,
                sWidth: 168,
                selected: false
            }
        ];
        let ingenicoModes = [
            {
                id: 25,
                name: 'PHONE_PE',
                image: require('../../assets/img/phonePe.png'),
                sImage: require('../../assets/img/sPhonePe.png'),
                height: 120,
                width: 119,
                sHeight: 85,
                sWidth: 86,
                selected: false
            },
            {
                id: 25,
                name: 'UPI_PAY',
                image: require('../../assets/img/UPI.png'),
                sImage: require('../../assets/img/sUpi.png'),
                height: 112,
                width: 245,
                sHeight: 81,
                sWidth: 176,
                selected: false
            },
            {
                id: 25,
                name: 'GOOGLE_PAY',
                image: require('../../assets/img/googlePay.png'),
                sImage: require('../../assets/img/sGooglePay.png'),
                height: 147,
                width: 147,
                sHeight: 105,
                sWidth: 106,
                selected: false
            },
            {
                id: 25,
                name: 'BHARAT_QR',
                image: require('../../assets/img/bharatQr.png'),
                sImage: require('../../assets/img/sBharatQr.png'),
                height: 154,
                width: 189,
                sHeight: 111,
                sWidth: 136,
                selected: false
            }
        ];
        /*if(faceDetection != true && faceDetection != "true") {
            modes = modes.concat(ingenicoModes);
        }*/
        //modes = modes.concat(ingenicoModes);
        return modes;
    }

    getPaymentMap() {
        let map = new Map();
        map.set('PAYTM', null);
        map.set('UPI', null);
        map.set('BHARAT_QR', null);
        map.set('GOOGLE PAY', null);
        return map;
    }

    getImageFromRegion(region) {
        switch (region) {
            case "NCR":
                return "delhi.jpg";
            case "NCR_EDU":
                return "delhi.jpg";
            case "MUMBAI":
                return "mumbai.jpg";
            case "CHANDIGARH":
                return "chandigarh.jpg";
            case "BANGALORE":
                return "bangalore.jpg";
        }
    }

    generateSessionId() {
        this.sessionId = new Date().getTime() + "_" + this.getUnitId() + "_" + this.getTerminalId();
        this.setSessionId(this.sessionId);
        return this.sessionId;
    }

    getSessionId() {
        return this.sessionId;
    }

    setSessionId(data) {
        return this.sessionId = data;
    }

    setFaceString(data) {
        this.faceString = data;
    }

    getFaceString() {
        return this.faceString;
    }

    setLoginByFace(data) {
        this.loginByFace = data;
    }

    getLoginByFace() {
        return this.loginByFace;
    }

    setFaceSync(data) {
        this.faceSync = data;
    }

    getFaceSync() {
        return this.faceSync;
    }

    getSessionIdFromFileName(name) {
        return name.substr(0, name.indexOf("_"));
    }

    setOrderSessionId(id) {
        this.orderSessionId = id;
    }

    getOrderSessionId() {
        return this.orderSessionId;
    }

    getEmptyCustomerObj() {
        return {
            id: null,
            name: null,
            contact: null,
            email: null,
            loyalityPoints: 0,
            contactVerified: false,
            emailVerified: false,
            unitId: this.getUnitId(),
            newCustomer: false,
            otp: null,
            chaiRedeemed: 0,
            productId: 10,
            otpVerified: false
        };
    }
    ;

    capitalize(a) {
        return a.replace(/(?:^|\s)\S/g, function (a) {
            return a.toUpperCase();
        });
    }
    ;

    getFaceLookupCallAttemp() {
        return this.faceLookupCallAttemp;
    }

    setFaceLookupCallAttemp(data) {
        this.faceLookupCallAttemp = data;
    }
    getCustomerDetailType(){
        return  types={
            SOURCE_ACKNOWLEDGE:'SOURCE_ACKNOWLEDGEMENT'
        }
    }
    setUnitPartnerBrandMetaData(data){
        this.unitPartnerBrandMetaData=data;
    }
    getUnitPartnerBrandMetaData(){
        return this.unitPartnerBrandMetaData;
    }


    getHomeScreenUrl() {
        return this.homeScreenUrl;
    }

    setHomeScreenUrl(value) {
        this.homeScreenUrl = value;
    }
}

const
    utilityService = new UtilityService();

export default utilityService;
