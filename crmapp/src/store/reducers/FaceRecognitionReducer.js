export default function reducer(state = {
    faceCaptured:false,
    faceString:null,
    faceData:null,
    faceScanning:false,
    sessionId: null
}, action) {
    switch (action.type) {
        case "SET_FACE_CAPTURED": {
            return {...state, faceCaptured: action.payload};
        }
        case "SET_FACE_STRING": {
            return {...state, faceString: action.payload};
        }
        case "SET_FACE_DATA": {
            return {...state, faceData: action.payload};
        }
        case "SET_FACE_SCANNING": {
            return {...state, faceScanning: action.payload};
        }
        case "SET_SESSION_ID": {
            return {...state, sessionId: action.payload};
        }
        default:
            return state;
    }
}
