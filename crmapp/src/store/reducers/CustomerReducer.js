export default function reducer(state = {
    showLoader: false,
    loadingMessage: null,
    contactNumber: null,
    contactEntered: false,
    customerDetails: null,
    showEmailInput: false,
    otpDisabled: false,
    userNameDisabled: false,
    otpSentCount: 0,
    showOtp: true,
    showUpdateUsername: false,
    resetCustomerDetails: true,
    loginByFace: false
}, action) {
    switch (action.type) {
        case "SET_CUSTOMER_DETAILS": {
            return {...state, customerDetails: action.payload};
            break;
        }
        case "SET_CONTACT_NUMBER": {
            return {...state, contactNumber: action.payload};
            break;
        }
        case "SET_CONTACT_ENTERED": {
            return {...state, contactEntered: action.payload};
            break;
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
        case "SET_SHOW_EMAIL_INPUT": {
            return {...state, showEmailInput: action.payload};
            break;
        }
        case "SET_OTP_DISABLED": {
            return {...state, otpDisabled: action.payload};
            break;
        }
        case "SET_USER_NAME_DISABLED": {
            return {...state, userNameDisabled: action.payload};
            break;
        }
        case "SET_OTP_SENT_COUNT": {
            return {...state, otpSentCount: action.payload};
            break;
        }
        case "SET_SHOW_OTP": {
            return {...state, showOtp: action.payload};
            break;
        }
        case "SET_SHOW_UPDATE_USER_NAME": {
            return {...state, showUpdateUsername: action.payload};
            break;
        }
        case "SET_REDEEM_CHAI": {
            return {...state, setRedeemChai: action.payload};
            break;
        }
        case "RESET_CUSTOMER_DETAILS": {
            return {...state, resetCustomerDetails: action.payload};
            break;
        }
        case "LOGIN_BY_FACE": {
            return {...state, loginByFace: action.payload};
            break;
        }
        default:
            return state;
    }
    return state;
}
