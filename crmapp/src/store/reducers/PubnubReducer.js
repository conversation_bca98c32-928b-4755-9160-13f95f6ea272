export default function reducer(state = {
    pubnubObject: null,
    channelName: null
}, action) {

    switch (action.type) {
        case "SET_PUB_NUB_OBJECT": {
            return {...state, pubnubObject: action.payload};
            break;
        }
        case "SET_CHANNEL_NAME": {
            return {...state, channelName: action.payload};
            break;
        }
        default:
          return state;
    }

    return state;

}
