export default function reducer(state = {
    userId: null,
    password:null,
    internetConnected: true,
    prevRoute: null,
    currRoute: null,
    autoConfigData: null,
    loginError: false,
    loginErrorMsg:null,
    outletList:null,
    selectedOutlet: null,
    selectedTerminal: null,
    showChangePasswordModal: false,
    changePasswordObj: null,
    pubnubObject: null,
    channelName: null,
    trueCallerChannelName: null,
    unitId: null,
    faceDetectionMode:true
}, action) {

    switch (action.type) {
        case "SET_AUTO_CONFIG_DATA":
        {
            return {...state, autoConfigData:action.payload};
            break;
        }
        case "SET_USER_ID":
        {
            return {...state, userId:action.payload};
            break;
        }
        case "SET_PASSWORD":
        {
            return {...state, password:action.payload};
            break;
        }
        case "SET_OUTLET_LIST":
        {
            return {...state, outletList:action.payload};
            break;
        }
        case "SET_SELECTED_OUTLET":
        {
            return {...state, selectedOutlet:action.payload};
            break;
        }
        case "SET_SELECTED_TERMINAL":
        {
            return {...state, selectedTerminal:action.payload};
            break;
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
        case "SET_LOGIN_ERROR": {
            return {...state, loginError: action.payload.loginError, loginErrorMsg: action.payload.loginErrorMsg};
            break;
        }
        case "SET_AUTH_DETAIL": {
            return {...state, authDetail: action.payload};
            break;
        }
        case "SET_INTERNET_CONNECTED": {
            return {...state, internetConnected: action.payload};
            break;
        }
        case "SET_PREV_ROUTE": {
            return {...state, prevRoute: action.payload};
            break;
        }
        case "SET_CURR_ROUTE": {
            return {...state, currRoute: action.payload};
            break;
        }
        case "SET_SHOW_CHANGE_PASSWORD_MODAL": {
            return {...state, showChangePasswordModal: action.payload};
            break;
        }
        case "SET_CHANGE_PASSWORD_DATA": {
            return {...state, changePasswordObj: action.payload};
            break;
        }
        case 'SET_SELECTED_UNIT_ID': {
            return {...state, unitId: action.payload};
            break;

        }
        case "SET_PUB_NUB_OBJECT": {
            return {...state, pubnubObject: action.payload};
            break;
        }
        case "SET_CHANNEL_NAME": {
            return {...state, channelName: action.payload};
            break;
        }
        case 'SET_TRUE_CALLER_CHANNEL_NAME':{
            return {...state, trueCallerChannelName: action.payload};
            break;
        }
        case 'SET_FACE_DETECTION_MODE':{
            return {...state, faceDetectionMode: action.payload};
            break;
        }
        default:
          return state;
    }

    return state;

}
