export default function reducer(state = {
    orderDetails: null,
    qrCode: null,
    orderId: null,
    interval: null,
    timeout: null,
    secondsInterval: 0,
    paymentRemainingSeconds: 0,
    paymentStart: false,
    paymentModeId: 0,
    qrLoader: false,
    qrLoadingMessage: null,
    paymentQr: null,
    paymentModes: null
}, action) {

    switch (action.type) {
        case "ORDER_DETAILS": {
            return {...state, orderDetails: action.payload};
            break;
        }
        case "SET_QR_CODE": {
            return {...state, qrCode: action.payload};
            break;
        }
        case "SET_ORDER_ID": {
            return {...state, orderId: action.payload};
            break;
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
        case "SET_INTERVAL": {
            return {...state, interval: action.payload};
            break;
        }
        case "SET_TIMEOUT": {
            return {...state, timeout: action.payload};
            break;
        }
        case "SET_PAYMENT_REMAINING_SECONDS": {
            return {...state, paymentRemainingSeconds: action.payload};
            break;
        }
        case "SET_SECONDS_INTERVAL": {
            return {...state, secondsInterval: action.payload};
            break;
        }
        case "SET_PAYMENT_START": {
            return {...state, paymentStart: action.payload};
            break;
        }
        case "SET_PAYMENT_MODE_ID": {
            return {...state, paymentModeId: action.payload};
            break;
        }
        case "SET_QR_LOADER": {
            return {...state, qrLoader: action.payload.qrLoader, qrLoadingMessage: action.payload.qrLoadingMessage};
            break;
        }
        case "SET_PAYMENT_QR_MAP": {
            return {...state, paymentQr: action.payload};
            break;
        }
        case "SET_PAYMENT_MODES": {
            return {...state, paymentModes: action.payload};
            break;
        }
        case "SET_CHAAYOS_WALLET_BALANCE": {
            return {...state, chaayosWalletBalance: action.payload};
            break;
        }

        default:
            return state;
    }
    return state;
}
