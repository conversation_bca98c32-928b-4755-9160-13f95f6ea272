export default function reducer(state = {
    showLoader: false,
    email: null
}, action) {
    switch (action.type) {
        case "SET_EMAIL": {
            return {...state, email: action.payload};
            break;
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
    }
    return state;
}