export default function reducer(state = {
    customerDetails:null,

}, action) {
    switch (action.type) {
        case "SET_CUSTOMER_DETAILS": {
            return {...state, customerDetails: action.payload};
            break;
        }
        case "SET_OTP_HEADLINE": {
            return {...state, otpHeadline: action.payload};
            break;
        }
        default:
            return state;
        }

    return state;
}