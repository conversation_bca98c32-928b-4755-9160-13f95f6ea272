import React, {Component} from 'react';
import {connect} from "react-redux";
import {
    Image,
    StyleSheet,
    TouchableOpacity,
    View,
    ActivityIndicator,
    ImageBackground
} from 'react-native';
import * as FaceRecognitionActions from "./../store/actions/FaceRecognitionActions";
import CustomText from "./CustomText";
import * as CustomerActions from "../store/actions/CustomerActions";
import UtilityService from "../store/services/UtilityService";

class FaceRecognitionScreen extends Component {

    constructor() {
        super();
        this.state = {};
    }

    componentWillMount() {
        clearInterval(this.props.interval);
        clearInterval(this.props.secondsInterval);
        clearTimeout(this.props.timeout);
        this.props.resetCustomerData(this.props.interval);
    }

    componentDidMount() {
        this.props.setFaceCaptured(false);
        this.props.setFaceScanning(false);
    }

    render() {
        return (
            <ImageBackground source={{uri:'https://internal.chaayos.com/kettle-crm/img/faceItBg.jpg'}} style={{width: '100%', height: '100%'}}>
                <View style={styles.container}>
                    {!this.props.faceCaptured ? (
                        <View style={{
                            flex: 1,
                            flexDirection: 'column',
                            justifyContent: 'flex-end',
                            alignItems: "center",
                        }}>
                            <TouchableOpacity style={styles.startBtn} onPress={() => {
                                this.props.startFaceTracking(this.props)
                            }}>
                                <CustomText style={{fontSize: 23, color: "#FFF"}}>Accept & Continue</CustomText>
                            </TouchableOpacity>
                            <CustomText style={{fontSize: 23, marginTop:15}}>or</CustomText>
                            <TouchableOpacity style={styles.skipLink} onPress={() => {
                                this.props.skipFaceLogin(this.props)
                            }}>
                                <CustomText style={{fontSize: 23, color: '#FFF'}}>Login Using Contact</CustomText>
                            </TouchableOpacity>
                        </View>
                    ) : null}
                    {this.props.faceCaptured ? (
                        <ImageBackground source={require('../assets/img/noOTP.jpg')} style={{width: '100%', height: '100%'}}>
                            <View style={styles.scanContainer}>
                                {/*<Image resizeMode='contain'
                                       source={require('../assets/img/readingFace.gif')}/>*/}
                                {/*<Image style={styles.preview} resizeMode='contain'
                               source={{uri: 'data:image/jpeg;base64,' + this.props.faceString}}/>*/}
                                {/*{this.props.faceScanning ? (
                                    <View style={styles.loaderContainer}>
                                        <CustomText style={styles.loaderMessage}>Identifying face...</CustomText>
                                    </View>
                                ) : null}*/}
                            </View>
                        </ImageBackground>
                    ) : null}
                </View>
            </ImageBackground>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: "center",
    },
    scanContainer: {
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: "center",
        //backgroundColor:"white"
    },
    faceIcon: {
        width: 350,
        height: 350
    },
    preview: {
        width: 350,
        height: 350,
        borderRadius: 175
    },
    btnGreen: {
        flex: 0,
        backgroundColor: 'green',
        borderRadius: 5,
        padding: 15,
        paddingHorizontal: 20,
        alignSelf: 'center',
        margin: 20,
        color: "#FFF"
    },
    startBtn: {
        flex: 0,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: "center",
        backgroundColor: 'green',
        borderRadius: 10,
        width: 450,
        height: 70,
        alignSelf: 'center',
        /*marginBottom: 180,*/
        color: "#FFF",
        elevation: 2,
    },
    skipLink: {
        flex: 0,
        flexDirection: 'column',
        justifyContent: 'center',
        backgroundColor: 'green',
        alignItems: "center",
        width: 450,
        height: 70,
        borderRadius: 10,
        alignSelf: 'center',
        color: "#FFF",
        marginBottom: 150,
        elevation: 2,
    },
    loaderContainer: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'center',
        marginBottom: 20,
    },
    loaderMessage: {
        color: '#5e7e47',
        fontSize: 21,
        textAlign: 'center',
        paddingLeft: 30,
        paddingRight: 30
    },
});

const mapStateToProps = state => {
    return {
        faceCaptured: state.faceRecognitionReducer.faceCaptured,
        faceString: state.faceRecognitionReducer.faceString,
        faceScanning: state.faceRecognitionReducer.faceScanning,
        customerDetails: state.customerReducer.customerDetails,
        interval: state.paymentReducer.interval,
        timeout: state.paymentReducer.timeout,
        secondsInterval: state.paymentReducer.secondsInterval,
        sessionId: state.faceRecognitionReducer.sessionId,
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setFaceCaptured: (faceCaptured) => dispatch(FaceRecognitionActions.setFaceCaptured(faceCaptured)),
        setFaceString: (faceString) => dispatch(FaceRecognitionActions.setFaceString(faceString)),
        startFaceTracking: (props) => dispatch(FaceRecognitionActions.startFaceTracking(props)),
        skipFaceLogin: (props) => dispatch(FaceRecognitionActions.skipFaceLogin(props)),
        setFaceScanning: (faceScanning) => dispatch(FaceRecognitionActions.setFaceScanning(faceScanning)),
        resetCustomerData: () => dispatch(CustomerActions.resetCustomerData(true)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(FaceRecognitionScreen);