import React, { Component } from 'react';
import { connect } from 'react-redux';
import CustomText from "./CustomText";
import { IconButton, Colors } from 'react-native-paper';
import {
    View,
    StyleSheet,
    Platform,
    TouchableOpacity,
    Image,
    StatusBar,
    ActivityIndicator,
    PermissionsAndroid,
} from 'react-native';
import { WebView } from 'react-native-webview';
import NavigationService from "./../store/services/NavigationService";

class AttendanceScreen extends Component {

    constructor(props) {
        super(props);
        this.attendanceRef = React.createRef();
        this.state = {
            visible: true,
            webViewURL: `https://face.peoplestrong.com/facerecognition/?location=${this.props.autoConfigData.unitId}_${String(this.props.autoConfigData.unitName).toUpperCase()}`,
            userAgent: 'Mozilla/5.0 (Linux; An33qdroid 10; Android SDK built for x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.185 Mobile Safari/537.36'
        }
    }

    permissions = async () => {
        let granted = await PermissionsAndroid.requestMultiple([
            PermissionsAndroid.PERMISSIONS.CAMERA,
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        ]);
    }

    componentDidMount() {
        this.permissions().catch(err => console.log(err));
    }

    handleBackButtonClick() {
        NavigationService.navigate('Home');
        return true;
    }

    showSpinner() {
        this.setState({ visible: true });
    }

    hideSpinner() {
        this.setState({ visible: false });
    }

    render() {

        return (
            <View style={{
                flex: 1,
                flexDirection: 'column',
                borderColor: "#444",
                shadowColor: "#333",
                backgroundColor: "#fff"
            }}>
                <View style={{ height: Platform.OS === "android" ? StatusBar.currentHeight : 0, backgroundColor: "#5e7e47" }}></View>
                <View style={styles.headerStyle}>
                    <TouchableOpacity onPress={() => {
                        this.showSpinner();
                        this.attendanceRef.current.reload();
                    }}>
                        <IconButton
                            icon="refresh"
                            color={Colors.black}
                            size={40}
                        />
                    </TouchableOpacity>
                    <Image style={styles.logo}
                        source={require('../assets/img/loginLogo.jpg')} />
                    <TouchableOpacity onPress={() => {
                        this.handleBackButtonClick();
                    }}>
                        <IconButton
                            icon="window-close"
                            color={Colors.black}
                            size={40}
                        />
                    </TouchableOpacity>
                </View>

                <View style={styles.MainContainer}>
                {this.state.visible && (
                        <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color='#5e7e47'/>
                        <CustomText style={styles.loaderMessage}>{"Loading"}</CustomText>
                    </View>
                    )}
                    <WebView
                    style={styles.pageView}
                        onLoadEnd={() => {this.hideSpinner() }}
                        ref={this.attendanceRef}
                        userAgent={this.state.userAgent}
                        originWhitelist={['*']}
                        allowsInlineMediaPlayback
                        allowInlineMediaPlayback={true}
                        useWebkit
                        bounces={true}
                        startInLoadingState
                        scalesPageToFit
                        mediaPlaybackRequiresUserAction={false}
                        javaScriptEnabled={true}
                        geolocationEnabled={true}
                        source={{ uri: this.state.webViewURL }} />
                       

                </View>
               
            </View>
        );
    }
}

const styles = StyleSheet.create({

    headerStyle: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderColor: "#444",
        shadowColor: "#333",
        elevation: 2,
        borderWidth: 1,
        borderTopWidth: 0,
        borderBottomWidth: 0,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.5,
        height: 60,
        shadowRadius: 2,
        backgroundColor: '#fff'
    },

    logo: {
        height: 40,
        width: 150,
    },

    MainContainer: {
        backgroundColor: "transparent",
        flex: 1,
        width: '100%',
    },
    loaderContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        height:"100%"
    },
    loaderMessage: {
        color: '#5e7e47',
        fontSize: 30,
        fontWeight: 'bold',
        textAlign: 'center',
        paddingLeft: 30,
        paddingRight: 30
    },
    pageView:{
        height:"100%",
        flex:1,
    },

});

const mapStateToProps = state => {
    return {
        autoConfigData: state.loginReducer.autoConfigData,
    }
};

export default connect(mapStateToProps)(AttendanceScreen);