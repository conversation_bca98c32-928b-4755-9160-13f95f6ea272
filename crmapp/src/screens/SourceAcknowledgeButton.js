import React from 'react';
import {Image, StyleSheet, TouchableOpacity} from 'react-native';
import CustomText from "./CustomText";


const app = (props) => {

    return (
        <TouchableOpacity
            style={styles.mode}
            onPress={() =>
                props.onPress()}>
            <Image source={props.image}
                   style={styles.smallImage}/>
            <CustomText style={styles.text}>{props.name}</CustomText>
        </TouchableOpacity>
    )
}

const styles = StyleSheet.create({

    mode: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        // justifyContent:'flex-start',
        justifyContent:'flex-start',
        // flexWrap:'wrap',
        // backgroundColor: '#f7f5f5',
        borderRadius: 15,
        borderWidth:1,
        // borderColor:'#F7F7F7',

        width: 230,
        height: 60,
        // padding: 10,
        marginTop:70,


    },
    smallImage: {

        height: 80,
        width: 50,
        resizeMode:'contain'
    },
    text:{
        fontSize:18,
        flexWrap:'wrap',
        paddingLeft:20,
        // width:100,
        // justifyContent: 'center',
        color: '#118945',
        // alignItems: 'center',
        // fontWeight:'bold',
        // marginTop:50,
        // marginLeft:-80


    }
});

export default app;
