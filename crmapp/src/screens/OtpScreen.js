import React, {Component} from 'react';
import {connect} from 'react-redux';
import {Dimensions, ImageBackground, StyleSheet, TextInput, Text, Modal, TouchableOpacity, View} from 'react-native';
import * as OtpActions from "./../store/actions/OtpActions";
import CustomText from "./CustomText";
import DownloadLinkButton from "./DownloadAppButton";
import * as CustomerActions from "../store/actions/CustomerActions";
import UtilityService from "../store/services/UtilityService";


var {deviceWidth} = Dimensions.get('window');

class OtpScreen extends Component {

    constructor(props) {
        super(props);
        this.width = 0.5;
        this.height = 0.25;
        this.bordered = false;
        if (this.props.otpSource != null) {
            this.props.generateOtp(this.props);
        }

    }


    verifyOtp(otp) {
        if (otp.length == 4) {
            this.props.customerDetails.otp = otp;
            this.props.verifyOtp(otp, this.props, this.otpInput);
        }
    }

    encodeContact() {
        return '******' + this.props.customerDetails.contact.substring(6);
    }

    render() {
        return (
            <View>
                <ImageBackground width='100%' height='100%'
                                 source={require("../assets/img/otpScreen.jpg")}
                                 style={{
                                     width: '100%',
                                     height: '100%'
                                 }}>

                    <View style={styles.containerNew}>
                        <View style={styles.userInfo}>
                            <View style={{flex: 50, marginLeft: 15}}>
                                <CustomText style={{
                                    color: '#5e7e47',
                                    fontSize: 23,
                                    fontFamily: 'Nunito-Regular',
                                    textAlign: 'left'
                                }}>Hi {this.props.customerDetails.name}
                                </CustomText>
                            </View>
                            <View style={{flex: 50, marginRight: 15}}>
                                <CustomText style={{
                                    color: '#5e7e47',
                                    fontSize: 20,
                                    textAlign: 'right',
                                    fontFamily: 'Nunito-Regular'
                                }}> {this.props.customerDetails && this.encodeContact()}
                                </CustomText>
                            </View>
                        </View>
                        <View style={{marginTop: 50, alignItems: 'center'}}>
                            <CustomText style={{
                                color: '#5e7e47',
                                fontSize: 30,
                                // flexDirection: 'column',
                                // marginLeft: 100,
                                // width: 500,
                                justifyContent: 'center',
                                alignItems: 'center',
                                fontFamily: 'Nunito-Regular',
                                fontWeight:'bold',
                                flexWrap:'wrap',
                                paddingLeft:10
                            }}> OTP Verification
                            </CustomText>
                            <CustomText style={{
                                color: '#5e7e47',
                                fontSize: 30,
                                // flexDirection: 'column',
                                // marginLeft: 100,
                                // width: 500,
                                justifyContent: 'center',
                                alignItems: 'center',
                                fontFamily: 'Nunito-Regular',
                                fontWeight:'bold',
                            }}> {this.props.otpHeadline}
                            </CustomText>
                        </View>

                        <View style={{marginTop: 50}}>
                            <CustomText style={{
                                // color: '#5e7e47',
                                fontSize: 25,
                                marginLeft: 200,
                                width: 600,
                                alignItems: 'center',
                                alignItems: 'center',
                                fontFamily: 'Nunito-Regular'
                            }}> We have sent the OTP to your</CustomText>
                            <CustomText style={{
                                // color: '#5e7e47',
                                fontSize: 25,
                                marginLeft: 285,
                                width: 600,
                                alignItems: 'center',
                                alignItems: 'center',
                                fontFamily: 'Nunito-Regular'
                            }}>mobile number
                            </CustomText>
                        </View>
                        <View style={styles.inputContainerNew}>
                            <TextInput
                                ref={input => {
                                    this.otpInput = input
                                }}
                                style={styles.inputBoxNew}
                                placeholder="Enter your OTP here"
                                placeholderTextColor="#5e7e47"
                                keyboardType="number-pad"
                                underlineColorAndroid='transparent'
                                value={this.props.otp}
                                onChangeText={(otp) => this.verifyOtp(otp)}
                            />
                        </View>

                        <View style={styles.modalFooterNew}>
                            <TouchableOpacity style={styles.buttonStyleNew} onPress={() =>
                                this.props.resendOtp(this.props)}>
                                <CustomText style={styles.buttonTitleStyleNew}>
                                    <CustomText style={{color: 'gray', marginLeft: 100}}>Didn't receive
                                        OTP? </CustomText>
                                    <CustomText style={{color: '#5e7e47', fontWeight: 'bold'}}>RESEND
                                        OTP</CustomText>
                                </CustomText>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <DownloadLinkButton description="Send Link"
                                        onPress={() => this.props.sendAppDownloadLink(this.props, 'OTP_SCREEN')}/>
                </ImageBackground>
            </View>


            /*<View>
                    {this.props.isModalVisible ? (
                        <View style={styles.backDrop}>

                        </View>
                    ) : null}


                    {/!*<Dialog visible={this.props.isModalVisible} dialogStyle={styles.dialogBox}
                            width={this.width} height={this.height}
                            dialogTitle={
                                <DialogTitle title={this.props.otpHeadline}
                                             style={{fontSize: 25, fontFamily: "AmericanTypewriter", marginBottom: 20}}/>
                            }
                            footer={
                                <DialogFooter bordered={this.bordered}>
                                    <DialogButton text="Resend OTP" textStyle={styles.dialogBoxButton}
                                                  onPress={() => {
                                                      this.props.resendOtp(this.props)
                                                  }}
                                    />
                                </DialogFooter>
                            }
                            dialogAnimation={new SlideAnimation({slideFrom: 'bottom',})}
                    >
                        <DialogContent>
                            <View style={{marginTop: 20}}>
                                <CustomText style={styles.inputLabel}>OTP </CustomText>
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        ref={input => {
                                            this.otpInput = input
                                        }}
                                        style={styles.inputBox}
                                        placeholder="Enter your OTP here"
                                        keyboardType="number-pad"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#ddd'
                                        value={this.props.otp}
                                        onChangeText={(otp) => this.verifyOtp(otp)}
                                    />
                                </View>
                            </View>
                        </DialogContent>
                    </Dialog>*!/}
                </View>*/
        );
    }

}


const styles = StyleSheet.create({
    backDrop: {
        backgroundColor: 'rgba(0,0,0,0.7)',
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1
    },
    modalBox: {
        width: 500,
        flex: 0,
        backgroundColor: "#FFFFFF",
        borderRadius: 5
    },
    modalTitle: {
        padding: 10,
        backgroundColor: "#EFEFEF",
        borderBottomWidth: 1,
        borderBottomColor: "#EEEEEE",
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5
    },
    modalBody: {
        padding: 10
    },
    modalFooterNew: {
        padding: 10,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    buttonStyleNew: {
        flex: 0,
        height: 50,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        // backgroundColor: '#5e7e47',
        // borderRadius: 4,
        padding: 10,
    },
    buttonTitleStyleNew: {
        textAlign: 'center',
        padding: 10,
        color: '#5e7e47',
        fontSize: 25,
        marginTop: 70,
    },
    container: {
        flex: 0,
        alignItems: "stretch",
        justifyContent: "flex-start",
        margin: 50,
        marginTop: 200,
        borderWidth: 1,
        borderColor: "#DDD",
        borderRadius: 10,
        padding: 20,
        backgroundColor: '#FFF'
    },
    inputLabel: {
        color: "#5e7e47",
        marginBottom: 5,
        fontWeight: "bold",
        fontSize: 20,
        fontFamily: 'Nunito-Regular'
    },
    inputContainerNew: {
        flex: 0,
        borderBottomColor: '#5e7e47',
        borderBottomWidth: 1,
        // borderWidth:10,
        height: 75,
        fontFamily: 'Nunito-Regular',
        marginTop: 150,
        marginLeft: 150,
        width: 500,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
    },
    inputBoxNew: {
        height: 75,
        flex: 1,
        color: '#5e7e47',
        fontSize: 21,
        marginBottom: 5,
        width: 500,
        fontFamily: 'Nunito-Regular'
    },
    dialogBox: {
        fontSize: 25,
    },
    dialogBoxButton: {
        fontSize: 22,
        color: '#5e7e47',
        fontFamily: 'Nunito-Regular'
    },
    containerNew: {
        flex: 1,
        fontSize: 30,
        marginTop: 25
    },
    textStyle: {
        fontSize: 22,
        textAlign: 'center',
        color: 'rgba(0,0,0,0.87)'
    }, userInfo: {
        // backgroundColor: '#577C39',
        flexDirection: 'row',
        height: 45,
        alignItems: 'center',

    }, modalFooter: {
        padding: 10,
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center"
    },
    buttonStyle: {
        flex: 0,
        height: 50,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#5e7e47',
        borderRadius: 4,
        padding: 10,
    },
    buttonTitleStyle: {
        textDecorationColor: '#5e7e47',
        textAlign: 'center',
        padding: 10,
        color: '#fff',
        fontSize: 25
    },
    inputContainer: {
        flex: 0,
        borderBottomColor: '#5e7e47',
        borderBottomWidth: 1,
        height: 45,
        fontFamily: 'Nunito-Regular',
        marginBottom: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start'
    },
    inputBox: {
        height: 45,
        flex: 1,
        color: '#5e7e47',
        fontSize: 21,
        marginBottom: 5,
        fontFamily: "Nunito-Regular"
    }
});


const mapStateToProps = state => {
    return {
        customerDetails: state.customerReducer.customerDetails,
        pubnub: state.loginReducer.pubnubObject,
        otpHeadline: state.redemptionReducer.otpHeadline,
        isModalVisible: state.otpReducer.isModalVisible,
        showLoader: state.customerReducer.showLoader,
        loadingMessage: state.customerReducer.loadingMessage,
        otpSource: state.otpReducer.otpSource

    }
};

const mapDispatchToProps = dispatch => {
    return {
        verifyOtp: (otp, props, inputRef) => dispatch(OtpActions.verifyOtp(otp, props, inputRef)),
        cancelOtp: (props) => dispatch(OtpActions.cancelOtp(props)),
        resendOtp: (props) => dispatch(OtpActions.resendOtp(props)),
        generateOtp: (props) => dispatch(OtpActions.generateOtp(props)),
        sendAppDownloadLink: (props, msg) => dispatch(CustomerActions.sendAppDownloadLink(props, msg))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(OtpScreen);