import React, {Component} from 'react';
import {connect} from "react-redux";
import {StyleSheet, View, ImageBackground, Text, Image, TouchableOpacity, ActivityIndicator} from 'react-native';
import * as PaymentActions from "../store/actions/PaymentActions";
import UtilityService from "../store/services/UtilityService";
import CustomText from "./CustomText";
import DownloadLinkButton from "./DownloadAppButton";
import * as CustomerActions from "../store/actions/CustomerActions";

class PaymentModeScreen extends Component {

    constructor(props) {
        super(props);
        this.props.hideLoader();
        this.props.createPaymentMap(UtilityService.getPaymentMap());
        clearInterval(this.props.interval);
        clearTimeout(this.props.timeout);
        clearInterval(this.props.secondsInterval);
        this.state = {
            paymentModes: UtilityService.getPaymentModes(this.props.faceDetectionMode),
            sec: 0,
            min: 0
        }
    }

    componentWillMount() {
        clearInterval(this.props.interval);
        clearTimeout(this.props.timeout);
        clearInterval(this.props.secondsInterval);
        this.props.hideLoader();
    }

    componentDidMount() {
        var _this = this;
        this.props.navigation.addListener("didFocus", () => {
            clearInterval(_this.props.interval);
            clearTimeout(this.props.timeout);
            clearInterval(this.props.secondsInterval);
            this.props.hideLoader();
        });
    }

    componentWillUnMount() {
        clearInterval(this.props.interval);
        clearTimeout(this.props.timeout);
        clearInterval(this.props.secondsInterval);
    }


    renderPaymentModes = () => {
        const views = [];
        this.state.paymentModes.map((mode, index) => {
            views.push(
                <View key={index} style={styles.modeContainer}>
                    <TouchableOpacity
                        onPress={() => this.createQr(mode.id, mode.name)}>
                        <Image source={mode.image}
                               style={{width: mode.width, height: mode.height}}></Image>
                    </TouchableOpacity>
                </View>
            );
            if (mode.id == 13) {
                views.push(
                    <View style={{
                        flex: 1,
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center",
                        borderBottomWidth: 1
                    }}>
                        <CustomText>Powered by</CustomText>
                        <Image source={require("../assets/img/sPaytmLogo.png")}
                               style={{resizeMode: "contain", width: 100}}/>
                    </View>
                )
            }
        });
        views.push(
            <View style={{flex: 1, flexDirection: "row", justifyContent: "center", alignItems: "center"}}>
                <CustomText>Powered by</CustomText>
                <Image source={require("../assets/img/sPaytmLogo.png")} style={{resizeMode: "contain", width: 100}}/>
            </View>
        );
        return views;
    };

    showTimer() {
        const views = [];
        const min = Math.floor(this.props.paymentRemainingSeconds / 60);
        const sec = Math.floor(this.props.paymentRemainingSeconds % 60);
        views.push(
            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                <View style={{
                    width: 50,
                    height: 70,
                    borderColor: 'green',
                    borderWidth: 2,
                    borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <CustomText style={{fontSize: 25, textAlign: 'center'}}>{min}</CustomText>
                </View>
                <CustomText> Min : </CustomText>
                <View style={{
                    width: 50,
                    height: 70,
                    borderColor: 'green',
                    borderWidth: 2,
                    borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <CustomText style={{fontSize: 25, textAlign: 'center'}}>{sec}</CustomText>
                </View>
                <CustomText> Sec</CustomText>
            </View>
        );
        return views;
    }

    encodeContact() {
        return '******' + this.props.customerDetails.contact.substring(6);
    }

    createQr(modeId, paymentMode) {
        console.log("mode id is :" + modeId + " " + "mode is :" + paymentMode);

        this.state.paymentModes.map((mode) => {
            return mode.selected = false;
        });
        console.log("state is:");
        console.log(this.state);
        this.state.paymentModes[this.state.paymentModes.findIndex((obj => obj.name == paymentMode))].selected = true;
        this.props.setPaymentModes(this.state.paymentModes);
        this.props.setPaymentModeId(modeId);
        this.props.showNavigationLoader();
        this.props.checkOrGenerateQr(this.props, modeId, paymentMode);
    }

    render() {
        return (
            <ImageBackground width='100%' height='100%' source={require("../assets/img/paymentScreen.jpg")}
                             style={{width: '100%', height: '100%'}}>
                <View style={{flex: 1, marginTop: 70}}>
                    <View style={styles.userInfo}>
                        <View style={{flex: 50, marginLeft: 15}}>
                            <CustomText
                                style={{
                                    textAlign: 'left',
                                    color: '#577C39',
                                    fontSize: 25
                                }}>Hi {this.props.customerDetails && this.props.customerDetails.name}</CustomText>
                        </View>
                        <View style={{flex: 50, marginRight: 15}}>
                            <CustomText
                                style={{
                                    textAlign: 'right',
                                    fontSize: 25,
                                    color: '#577C39'
                                }}>{this.props.customerDetails && this.encodeContact()}</CustomText>
                        </View>
                    </View>
                    <View
                        style={{flex: 1, flexDirection: "column", justifyContent: "flex-start", alignItems: 'center'}}>
                        {this.props.showLoader === true ? (
                            <View style={styles.loaderContainer}>
                                <Image resizeMode="contain" style={styles.logo}
                                       source={require('../assets/img/loginLogo.jpg')}/>
                                <ActivityIndicator size="large" color='#5e7e47'/>
                                <CustomText style={styles.loaderMessage}>{this.props.loadingMessage}</CustomText>
                            </View>
                        ) : (
                            <View style={{
                                flex: 1,
                                flexDirection: "column",
                                marginTop: 70,
                                justifyContent: 'flex-start',
                                alignItems: 'stretch'
                            }}>
                                <View style={{flex: 0, justifyContent: "center", alignItems: "center", height: 150}}>
                                    <View style={styles.amountContainer}>
                                        <CustomText style={{color: '#577C39', fontSize: 40, fontWeight: 'bold'}}>
                                            {'\u20B9 ' + this.props.orderDetails.amount}
                                        </CustomText>
                                    </View>
                                </View>
                                {/*{this.renderPaymentModes()}*/}
                                <View style={{
                                    flex: 0,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center"
                                }}>
                                    {/*{UtilityService.getSelectedOutlet().googleMerchantId != null ? (*/}
                                        {/*<View>*/}
                                            {/*<TouchableOpacity style={styles.modeContainer}*/}
                                                              {/*onPress={() => this.createQr(27, 'GOOGLE PAY')}>*/}
                                                {/*<Image source={require('../assets/img/gPayLogo.png')}*/}
                                                       {/*style={{height: 79, resizeMode: "contain"}}/>*/}
                                            {/*</TouchableOpacity>*/}
                                            {/*<CustomText style={styles.qrDescription}>All-in-one UPI*/}
                                                {/*QR</CustomText>*/}
                                        {/*</View>*/}
                                    {/*) : (<Text></Text>)}*/}

                                    <View>
                                        <TouchableOpacity style={styles.modeContainer}
                                                          onPress={() => this.createQr(13, 'PAYTM')}>
                                            <Image source={require('../assets/img/paytmLogo.png')}
                                                   style={{height: 79, resizeMode: "contain"}}/>
                                        </TouchableOpacity>
                                        <CustomText style={styles.qrDescription}>All-in-one UPI QR</CustomText>
                                    </View>
                                </View>

                            </View>
                        )}
                    </View>
                </View>
                <DownloadLinkButton description="Send Link"
                                    onPress={() => this.props.sendAppDownloadLink(this.props, 'PAYMENT_MODE')}/>
            </ImageBackground>
        );
    }
}

const
    styles = StyleSheet.create({
        userInfo: {
            // backgroundColor: '#577C39',
            flexDirection: 'row',
            height: 45,
            alignItems: 'center',

        },
        amountContainer: {
            flex: 0,
            // backgroundColor: '#577C39',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            borderRadius: 10,
            // borderWidth:10,
            // paddingTop: 10,
            paddingBottom: 10,
            width: 220,
            height: 47,
            // marginLeft: 100,
            marginTop: -5,
        },
        modeContainer: {
            justifyContent: 'center',
            alignItems: 'center',
            // borderWidth: 4,
            /*borderColor: 'rgba(217, 217, 217, 217)',*/
            // borderColor: '#577C39',
            borderRadius: 10,
            width: 220,
            height: 150,
            marginTop: 95,
            marginLeft: 80,
            /*backgroundColor: 'rgba(217,217,217,0.6)',*/
            backgroundColor: '#FFF',
            margin: 30,
            elevation: 13,
            shadowRadius: 10,
            shadowColor: '#000',
            shadowOpacity: 0.39,

        },
        logo: {
            height: 70,
            marginBottom: 30
        },
        loaderContainer: {
            flex: 0,
            alignItems: 'stretch',
            justifyContent: 'center',
            marginBottom: 50,
            marginTop: 350
        },
        loaderMessage: {
            color: '#5e7e47',
            fontSize: 21,
            textAlign: 'center',
            paddingLeft: 30,
            paddingRight: 30
        },
        qrDescription: {
            marginLeft: 90,
            marginTop:-10,
            color: 'grey',
            fontSize:22,
            fontWeight:'bold'
        }
    });

const
    mapStateToProps = state => {
        return {
            customerDetails: state.customerReducer.customerDetails,
            orderDetails: state.paymentReducer.orderDetails,
            qrCode: state.paymentReducer.qrCode,
            showLoader: state.customerReducer.showLoader,
            loadingMessage: state.customerReducer.loadingMessage,
            interval: state.paymentReducer.interval,
            timeout: state.paymentReducer.timeout,
            paymentRemainingSeconds: state.paymentReducer.paymentRemainingSeconds,
            secondsInterval: state.paymentReducer.secondsInterval,
            paymentQr: state.paymentReducer.paymentQr,
            faceDetectionMode: state.loginReducer.faceDetectionMode,
        }
    };

const
    mapDispatchToProps = dispatch => {
        return {
            createPaytmQr: (props, paymentMode) => dispatch(PaymentActions.createPaytmQr(props, paymentMode)),
            hideLoader: () => dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}}),
            transactionTimedout: (props) => dispatch(PaymentActions.transactionTimedout(props)),
            createIngenicoQr: (props, paymentMode) => dispatch(PaymentActions.createIngenicoQr(props, paymentMode)),
            setPaymentModeId: (modeId) => dispatch({type: "SET_PAYMENT_MODE_ID", payload: modeId}),
            checkOrGenerateQr: (props, modeId, paymentMode) => dispatch(PaymentActions.checkOrGenerateQr(props, modeId, paymentMode)),
            createPaymentMap: (map) => dispatch({type: "SET_PAYMENT_QR_MAP", payload: map}),
            setPaymentModes: (paymentModes) => dispatch({type: "SET_PAYMENT_MODES", payload: paymentModes}),
            showNavigationLoader: () => dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: true, loadingMessage: "Taking you to payment screen.Please wait!"}
            }),
            sendAppDownloadLink: (props, msg) => dispatch(CustomerActions.sendAppDownloadLink(props, msg))
        }
    };

export default connect(mapStateToProps, mapDispatchToProps)(PaymentModeScreen);