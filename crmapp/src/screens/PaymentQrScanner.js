import React, {Component} from 'react';
import {connect} from "react-redux";
import {StyleSheet, View, ImageBackground, Text, Image, TouchableOpacity, ActivityIndicator} from 'react-native';
import * as PaymentActions from "../store/actions/PaymentActions";
import QRCode from 'react-native-qrcode-svg';
import CustomText from "./CustomText";
import UtilityService from "../store/services/UtilityService";

class PaymentQrScanner extends Component {

    constructor(props) {
        super(props);
        this.props.hideLoader();
        clearInterval(this.props.interval);
        clearTimeout(this.props.timeout);
        clearInterval(this.props.secondsInterval);

        this.state = {
            ingenicoTimeout: 0,
            imageUrl: this.props.paymentModes[this.props.paymentModes.findIndex((obj => obj.name == props.customerDetails.paymentMode))].sImage
        }
    }

    componentWillMount() {
        clearInterval(this.props.interval);
        clearTimeout(this.props.timeout);
        clearInterval(this.props.secondsInterval);
        clearTimeout(this.state.ingenicoTimeout);


        if (this.props.paymentModeId == 13) {
            this.props.checkPaytmQRPaymentStatus(this.props, this.props.customerDetails.paytmTmId);
            // clearTimeout(this.state.ingenicoTimeout);
        }
        else if (this.props.paymentModeId == 27) {
            this.props.checkGPayQRPaymentStatus(this.props, this.props.customerDetails.gPayId);
            // clearTimeout(this.state.ingenicoTimeout);
        }
        // else {
        //     var _this = this;
            this.props.setPaymentRemainingTime();

        // }
    }

    componentWillUnMount() {
        this.props.hideLoader();
        clearInterval(this.props.interval);
        clearInterval(this.props.secondsInterval);
        clearTimeout(this.props.timeout);
        clearTimeout(this.state.ingenicoTimeout);
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.paymentRemainingSeconds == 0 && nextProps.paymentStart) {
            clearInterval(nextProps.interval);
            clearTimeout(nextProps.timeout);
            clearInterval(nextProps.secondsInterval);
            this.props.transactionTimedout(nextProps);
        }
        if (!nextProps.paymentStart) {
            clearInterval(nextProps.interval);
            clearTimeout(nextProps.timeout);
            clearInterval(nextProps.secondsInterval);
            clearTimeout(this.state.ingenicoTimeout);
        }
    }


    showTimer() {
        const views = [];
        const min = Math.floor(this.props.paymentRemainingSeconds / 60);
        const sec = Math.floor(this.props.paymentRemainingSeconds % 60);
        views.push(
            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                <View style={{
                    width: 50,
                    height: 70,
                    borderColor: 'green',
                    borderWidth: 2,
                    borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <CustomText style={{fontSize: 25, textAlign: 'center'}}>{min}</CustomText>
                </View>
                <CustomText> Min : </CustomText>
                <View style={{
                    width: 50,
                    height: 70,
                    borderColor: 'green',
                    borderWidth: 2,
                    borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <CustomText style={{fontSize: 25, textAlign: 'center'}}>{sec}</CustomText>
                </View>
                <CustomText> Sec</CustomText>
            </View>
        );
        return views;
    }

    renderPaymentModes = () => {
        const views = [];
        this.props.paymentModes.map((mode, index) => {
            views.push(
                <View key={index} style={mode.selected ? [styles.modeContainer, {
                    borderColor: '#577C39'
                }] : styles.modeContainer}>
                    <View style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                        <TouchableOpacity
                            onPress={() => this.createQr(mode.id, mode.name)}>
                            <Image source={mode.sImage}
                                   style={{width: mode.sWidth, height: mode.sHeight}} />
                        </TouchableOpacity>
                    </View>
                </View>
            );
        });
        return views;
    }

    createQr(modeId, paymentMode) {
        console.log("props is "+" mode is "+modeId);
        console.log(this.props);
        this.props.paymentModes.map((mode) => {
            return mode.selected = false;
        });
        clearInterval(this.props.interval);
        // this.props.paymentQr(UtilityService.getPaymentMap());
        // console.log("creating null maps");
        this.props.paymentModes[this.props.paymentModes.findIndex((obj => obj.name == paymentMode))].selected = true;
        this.props.setPaymentModeId(modeId);
        this.props.setPaymentModes(this.props.paymentModes);
        this.state.imageUrl = this.props.paymentModes[this.props.paymentModes.findIndex((obj => obj.name == paymentMode))].image;
        this.props.checkOrGenerateQr(this.props, modeId, paymentMode);
    }

    encodeContact() {
        return '******' + this.props.customerDetails.contact.substring(6);
    }

    render() {
        return (
            <ImageBackground width='100%' height='100%' source={require("../assets/img/chaayosBanner.jpg")}
                             style={{width: '100%', height: '100%'}}>
                <View style={{
                    marginTop: 100
                }}>
                    <View style={styles.userInfo}>
                        <View style={{flex: 50, marginLeft: 15}}>
                            <Text style={{
                                textAlign: 'left',
                                color: 'white',
                                fontSize: 25
                            }}>Hi, {this.props.customerDetails && this.props.customerDetails.name}</Text>
                        </View>
                        <View style={{
                            flex: 50, marginRight: 15
                        }}>
                            <Text
                                style={{
                                    textAlign: 'right',
                                    color: 'white',
                                    fontSize: 25
                                }}>{this.props.customerDetails && this.encodeContact()}</Text>
                        </View>
                    </View>
                    <View style={styles.QrContainer}>
                        <View style={styles.leftContainer}>
                            {this.renderPaymentModes()}
                        </View>
                        <View style={[styles.container, {flex:55}]}>
                            <View style={{justifyCOntent:"center", alignItems:"center"}}>
                                {this.props.loadingMessage == "Payment verified..." ?
                                    <CustomText style={{color: '#577C39', fontSize: 30, fontWeight: 'bold'}}>Collected...</CustomText>
                                    : <CustomText style={{color: '#577C39', fontSize: 30, fontWeight: 'bold'}}>Collecting...</CustomText>
                                }
                                <View style={{backgroundColor: '#577C39', justifyContent: 'center', alignItems: 'center',
                                    flexDirection: 'row', width: 160, borderRadius: 10, paddingTop: 10, paddingBottom: 10}}>
                                    <View style={{justifyContent: 'center', alignItems: 'center', marginLeft: 15}}>
                                        <CustomText style={{color: 'white', fontSize: 35, fontWeight: 'bold'}}>
                                            {'\u20B9 ' + this.props.orderDetails.amount}
                                        </CustomText>
                                    </View>
                                </View>
                            </View>


                            <View style={styles.rightContainer}>
                                {this.props.qrCode &&
                                <View style={{justifyContent: 'center', alignItems: 'center',}}>
                                    <Image source={this.state.imageUrl} style={{resizeMode: 'cover'}} />
                                </View>
                                }
                                {this.props.qrCode &&
                                    <View style={styles.qrCodeContainer}>
                                        {!this.props.qrLoader ?
                                            <QRCode value={this.props.qrCode} size={300} color='black' backgroundColor='white' />
                                            :
                                            <View style={styles.loaderContainer}>
                                                <ActivityIndicator size="large" color='#5e7e47' />
                                                <CustomText style={styles.loaderMessage}>{this.props.qrLoadingMessage}</CustomText>
                                            </View>
                                        }
                                    </View>
                                }
                                {this.props.loadingMessage == "Payment verified..." &&
                                    <View style={{alignItems: 'center', justifyContent: 'center', marginTop: 50,
                                        textAlign: 'center', height: 300}}>
                                        <Image width={150} height={150} source={require("../assets/img/verifiedThanks.png")}
                                               style={{width: 150, height: 150}} />
                                        <CustomText style={styles.loaderMessage}>{this.props.loadingMessage}</CustomText>
                                        <CustomText style={{color: '#577C39', fontSize: 20, fontWeight: 'bold'}}>Please wait
                                            while we place your order.</CustomText>
                                    </View>
                                }
                                {this.props.qrCode &&
                                    <View style={{justifyContent: 'center', alignItems: 'center', marginTop: 20, marginBottom: 20}}>
                                        <CustomText style={{fontSize: 20, color: 'black'}}>
                                            <CustomText style={{fontSize: 20, color: 'black'}}>Scan and Pay using </CustomText>
                                            {this.props.customerDetails.paymentMode}
                                        </CustomText>
                                    </View>
                                }
                                {this.props.qrCode &&
                                <View style={{justifyContent: 'center', alignItems: 'center', flexDirection: 'row'}}>
                                    <View style={{justifyContent: 'center', alignItems: 'center',}}>
                                        <Image source={require("../assets/img/qrReady.png")} style={{width: 40, height: 40}} />
                                    </View>
                                    <CustomText style={{fontSize: 30, color: 'black', fontWeight: 'bold', marginLeft: 10}}>
                                        QR code is ready to scan
                                    </CustomText>
                                </View>
                                }
                                {this.props.showLoader === true && (
                                    <View style={styles.loaderContainer}>
                                        <ActivityIndicator size="large" color='#5e7e47'/>
                                        <CustomText style={styles.loaderMessage}>{this.props.loadingMessage}</CustomText>
                                    </View>
                                )}
                            </View>
                            {this.props.qrCode &&
                                <View style={{flexDirection: 'row', marginTop: 20, justifyContent: 'center', alignItems: 'center'}}>
                                    <CustomText style={{fontSize: 25, color: 'black'}}>Time Left: </CustomText>
                                    {this.showTimer()}
                                </View>
                            }
                        </View>
                    </View>
                </View>
            </ImageBackground>
        );
    }
}

const styles = StyleSheet.create({
    QrContainer: {
        flexDirection: 'row',
        marginTop: 30,
        height: '100%',
    },
    container: {
        flex: 40,
        paddingLeft: 20,
        paddingRight: 30
    },
    leftContainer: {
        flex: 25,
        marginLeft: 20,
        marginTop: 70
    },
    rightContainer: {
        marginLeft: 10,
        marginRight: 10,
        marginTop: 20,
        paddingLeft: 20,
        paddingRight: 20,
        paddingTop: 10,
        paddingBottom: 10,
        backgroundColor: 'rgba(231,231,230,0.4)',
        justifyContent: 'center',
        textAlign: 'center',
    },
    userInfo: {
        backgroundColor: '#577C39',
        flexDirection: 'row',
        height: 45,
        alignItems: 'center',
    },
    modeContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 4,
        borderColor: '#b4b4b4',
        borderRadius: 10,
        width: 200,
        height: 150,
        marginBottom: 30,
        backgroundColor: '#FFF'
    },
    qrCodeContainer: {
        flex:0,
        marginTop: 20,
        borderColor: '#577C39',
        borderStyle: 'dashed',
        borderWidth: 3,
        borderRadius: 7,
        padding: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white'
    },
    loaderContainer: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'center',
        marginTop: 50,
        marginBottom: 50,
    },
    loaderMessage: {
        color: '#5e7e47',
        fontSize: 30,
        fontWeight: 'bold',
        textAlign: 'center',
        paddingLeft: 30,
        paddingRight: 30
    }
});

const mapStateToProps = state => {
    return {
        showLoader: state.customerReducer.showLoader,
        loadingMessage: state.customerReducer.loadingMessage,
        qrLoader: state.paymentReducer.qrLoader,
        qrLoadingMessage: state.paymentReducer.qrLoadingMessage,
        customerDetails: state.customerReducer.customerDetails,
        orderDetails: state.paymentReducer.orderDetails,
        qrCode: state.paymentReducer.qrCode,
        orderId: state.paymentReducer.orderId,
        contactEntered: state.customerReducer.contactEntered,
        interval: state.paymentReducer.interval,
        timeout: state.paymentReducer.timeout,
        paymentRemainingSeconds: state.paymentReducer.paymentRemainingSeconds,
        secondsInterval: state.paymentReducer.secondsInterval,
        paymentStart: state.paymentReducer.paymentStart,
        paymentQr: state.paymentReducer.paymentQr,
        paymentModes: state.paymentReducer.paymentModes,
        paymentModeId: state.paymentReducer.paymentModeId
    }
};

const mapDispatchToProps = dispatch => {
    return {
        checkPaytmQRPaymentStatus: (props, paymentId) => dispatch(PaymentActions.checkPaytmQRPaymentStatus(props, paymentId)),
        createPaytmQr: (props, paymentMode) => dispatch(PaymentActions.createPaytmQr(props, paymentMode)),
        hideLoader: () => dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}}),
        transactionTimedout: (props) => dispatch(PaymentActions.transactionTimedout(props)),
        createIngenicoQr: (props, paymentMode) => dispatch(PaymentActions.createIngenicoQr(props, paymentMode)),
        setPaymentModeId: (modeId) => dispatch({type: "SET_PAYMENT_MODE_ID", payload: modeId}),
        checkOrGenerateQr: (props, modeId, paymentMode) => dispatch(PaymentActions.checkOrGenerateQr(props, modeId, paymentMode)),
        checkIngenicoQrPayStatus: (props, paymentMode) => dispatch(PaymentActions.checkIngenicoStatus(props, paymentMode)),
        setPaymentRemainingTime: () => dispatch(PaymentActions.setPaymentRemainingTime()),
        setPaymentModes: (paymentModes) => dispatch({type: "SET_PAYMENT_MODES", payload: paymentModes}),
        checkGPayQRPaymentStatus: (props, paymentId) => dispatch(PaymentActions.checkGPayQRPaymentStatus(props, paymentId))

    }
};

export default connect(mapStateToProps, mapDispatchToProps)(PaymentQrScanner);