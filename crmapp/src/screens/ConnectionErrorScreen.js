import React, {Component} from 'react';
import {connect} from "react-redux";
import {
    StyleSheet,
    View,
    Image,
    TouchableOpacity,
    Text,
    ToastAndroid
} from 'react-native';
import CustomText from "./CustomText";

class ConnectionErrorScreen extends Component {

    retry() {
        if(this.props.internetConnected == true){
            this.props.navigation.navigate(this.props.prevRoute);
        }else{
            ToastAndroid.show('You are still offline !', ToastAndroid.SHORT);
        }
    }

    render() {
        return (
            <View style={styles.container}>
                <CustomText style={{fontSize:62, color:'#000', marginTop: 30, marginLeft: 30, marginBottom:20}}>Oops!</CustomText>
                <CustomText style={{fontSize:21, marginLeft: 30}}>Looks like</CustomText>
                <CustomText style={{fontSize:21, marginLeft: 30}}>you are offline</CustomText>
                <View style={{flex:0, alignItems:'flex-end', justifyContent:'flex-end', marginTop: -10, marginRight:30}}>
                    <Image source={require("./../assets/img/connectionError.png")} style={styles.drawerCover} />
                </View>

                <TouchableOpacity style={styles.btn} onPress={() => this.retry()}>
                    <Text style={styles.btnText}>Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'stretch',
        justifyContent:'flex-start',
        backgroundColor: '#edeff2',
    },
    drawerCover: {
        width: 250,
        resizeMode: "contain",
        alignItems: 'flex-end',
        justifyContent: 'flex-start',
    },
    btn:{
        padding: 20,
    },
    btnText:{
        textAlign: 'center',
        fontSize: 21,
        fontWeight: 'bold'
    }
});

const mapStateToProps = state => {
    return {
        internetConnected: state.loginReducer.internetConnected,
        prevRoute: state.loginReducer.prevRoute
    }
};

const mapDispatchToProps = dispatch => {
    return {}
};

export default connect(mapStateToProps, mapDispatchToProps)(ConnectionErrorScreen);