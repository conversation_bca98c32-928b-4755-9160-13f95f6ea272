import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    KeyboardAvoidingView,
    Modal,
    Picker,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    TouchableHighlight,
    ImageBackground,
    View
} from 'react-native';
import * as EmailActions from "../store/actions/EmailActions";
import CustomText from "./CustomText";


var {deviceWidth} = Dimensions.get('window');

class EmailScreen extends Component {
    constructor() {
        super();
        this.state = {
            email:null
        }
    }

    render() {
        return (
            <View style={styles.container}>
                <Text style={styles.header}>Register your email today to get your next Desi Chai FREE!</Text>
                <View style={styles.inputContainer}>
                    <TextInput
                        style={styles.inputBox}
                        autoFocus={true}
                        placeholder="Email"
                        keyboardType="email-address"
                        underlineColorAndroid='transparent'
                        placeholderTextColor='#ddd'
                        value={this.state.email}
                        onChangeText={(email) => this.props.setEmail(email)}
                    />
                </View>

                <TouchableOpacity style={styles.buttonStyle} onPress={() =>
                    this.props.submitEmail(this.props)}>
                    <CustomText style={styles.buttonTitleStyle}>Submit</CustomText>
                </TouchableOpacity>

                <View style={{
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Image source={require("../assets/img/gogreen.png")} style={{width: 200, height: 328}}
                           resizeMode="contain"/>
                    <CustomText style={{fontSize: 22, color: 'rgba(0,0,0,0.87)'}}>Register for e-bill &</CustomText>
                    <CustomText style={{color: '#426c24', fontSize: 25}}>SAVE PAPER</CustomText>
                    {/*<TouchableOpacity activeOpacity={.5} onPress={this.props.skipEmail(this.props)}>*/}
                    <TouchableHighlight underlayColor='transparent' onPress={() => this.props.skipEmail(this.props)}>
                        <Image source={require("../assets/img/skip.png")}
                               style={{width: 142, height: 51, marginTop: 50}} resizeMode="contain"/>
                        {/*</TouchableOpacity>*/}
                    </TouchableHighlight>
                </View>

            </View>
        );
    }

}


const styles = StyleSheet.create({
    container: {
        flex: 0,
        alignItems: "stretch",
        justifyContent: "flex-start",
        margin: 50,
        marginTop: 100,
        padding: 20,
        backgroundColor: '#FFF'
    },
    inputContainer: {
        flex: 0,
        borderBottomColor: '#5e7e47',
        borderBottomWidth: 1,
        height: 45,
        marginBottom: 20,
        marginLeft: 10,
        marginRight: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
    },
    inputBox: {
        height: 45,
        flex: 1,
        color: '#5e7e47',
        fontSize: 23,
        fontFamily: 'Nunito-Regular'
    },
    buttonStyle: {
        flex: 0,
        height: 40,
        width: 100,
        justifyContent: 'flex-end',
        alignSelf: 'flex-end',
        backgroundColor: '#5e7e47',
        borderRadius: 4,
        marginRight: 10
    },
    buttonTitleStyle: {
        textDecorationColor: '#5e7e47',
        textAlign: 'center',
        padding: 10,
        color: '#fff',
        fontSize: 18
    },
    header: {
        fontSize: 25,
        marginBottom: 25,
        color: 'rgba(0,0,0,0.87)'
    }
});


const mapStateToProps = state => {
    return {
        customerDetails: state.customerReducer.customerDetails,
        email: state.emailReducer.email,
        showLoader: state.customerReducer.showLoader,
        loadingMessage: state.customerReducer.loadingMessage
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setEmail: (email) => dispatch({type: "SET_EMAIL", payload: email.toLowerCase()}),
        submitEmail: (props) => dispatch(EmailActions.setEmail(props)),
        skipEmail: (props) => dispatch(EmailActions.skipEmail(props))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(EmailScreen);