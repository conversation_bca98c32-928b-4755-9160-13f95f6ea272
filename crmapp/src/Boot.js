import React, {Component} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, StatusBar} from 'react-native';
import {connect} from 'react-redux';
import AppContainer from "./navigation/Navigator";
import NavigationService from "./store/services/NavigationService";
import * as LoginActions from "./store/actions/LoginActions";
import * as PubnubAction from './store/actions/PubnubActions';
import * as UtilityAction from './store/actions/UtilityActions';
import PubNubReact from "pubnub-react";
import UtilityService from "./store/services/UtilityService";
import ConfigService from "./store/services/ConfigService";
import NetInfo from "@react-native-community/netinfo";

class Boot extends Component {

    constructor(props) {
        super(props);
        console.disableYellowBox = true;
        const pkey = ConfigService.getPubnubPublishKey();
        const skey = ConfigService.getPubnubSubscribeKey();
        this.pubnub = new PubNubReact({
            publishKey: pkey,
            subscribeKey: skey,
            ssl: true
        });
        this.state = {};
        this.pubnub.init(this);
        this.props.setPubnub(this.pubnub);
        UtilityService.setPubNub(this.pubnub);
    }

    componentWillMount() {
        StatusBar.setHidden(true, 'none');
        BackHandler.addEventListener('hardwareBackPress', () => {
            return true
        });
    }

    async componentDidMount() {
        this.props.hideOtpView();
        StatusBar.setHidden(true, 'none');
        BackHandler.addEventListener('hardwareBackPress', () => {
            return true
        });

        //setInterval (() => {
            NetInfo.fetch().then(state => {
                console.log("Connection type", state.type);
                console.log("Is connected?", state.isConnected);
                this.handleConnectionChange(state.isConnected);
            });
            const unsubscribe = NetInfo.addEventListener(state => {
                console.log("Connection type", state.type);
                console.log("Is connected?", state.isConnected);
                this.handleConnectionChange(state.isConnected);
            });
            this.props.setNetInfoSubscriber(unsubscribe);
        //}, 3000);

        //unsubscribe();
        //NetInfo.isConnected.addEventListener('connectionChange', this.handleConnectionChange);
    }

    componentWillUnmount() {
        if (this.props.netInfoSubscriber != null && typeof this.props.netInfoSubscriber == "function") {
            this.props.netInfoSubscriber();
        }
        BackHandler.removeEventListener('hardwareBackPress');
        //NetInfo.isConnected.removeEventListener('connectionChange', this.handleConnectionChange);
    }

    handleConnectionChange = (isConnected) => {
        this.props.setIsConnected(isConnected, this.props);
    };

    getActiveRouteName(navigationState) {
        if (!navigationState) {
            return null;
        }
        const route = navigationState.routes[navigationState.index];
        if (route.routes) {
            return this.getActiveRouteName(route);
        }
        return route.routeName;
    }

    handleNavigationStateChange(prevState, currentState) {
        const currentScreen = this.getActiveRouteName(currentState);
        const prevScreen = this.getActiveRouteName(prevState);
        if (currentScreen != prevScreen) {
            this.props.setPrevScreen(prevScreen);
            this.props.setCurrScreen(currentScreen);
        }
    }


    render() {
        return (
            <SafeAreaView style={{flex: 1, backgroundColor: '#d4272a'}}>
                <AppContainer
                    onNavigationStateChange={(prevState, currentState) => this.handleNavigationStateChange(prevState, currentState)}
                    ref={navigatorRef => {
                        NavigationService.setTopLevelNavigator(navigatorRef);
                    }}/>
            </SafeAreaView>
        );
    }
}


const mapStateToProps = state => {
    return {
        internetConnected: state.loginReducer.internetConnected,
        fcmToken: state.loginReducer.fcmToken,
        pubnub: state.loginReducer.pubnubObject,
        channelName: state.loginReducer.channelName,
        netInfoSubscriber: state.utilityReducer.netInfoSubscriber
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setPrevScreen: (prevScreen) => dispatch({type: "SET_PREV_ROUTE", payload: prevScreen}),
        setCurrScreen: (currScreen) => dispatch({type: "SET_CURR_ROUTE", payload: currScreen}),
        setIsConnected: (connected) => dispatch(LoginActions.setIsConnected(connected)),
        setPubnub: (pubnub) => dispatch(PubnubAction.setupPubNub(pubnub)),
        setNetInfoSubscriber: (obj) => dispatch(UtilityAction.setNetInfoSubscriber(obj)),
        hideOtpView: () => dispatch(dispatch({type: "IS_MODAL_VISIBLE", payload: false})),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(Boot);