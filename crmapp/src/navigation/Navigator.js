import React from 'react';
import {
    createStackNavigator,
    createSwitchNavigator,
    createAppContainer
} from "react-navigation";
import LoginScreen from "./../screens/LoginScreen";
import HomeScreen from "../screens/HomeScreen";
import AttendanceScreen from "../screens/AttendanceScreen";
import CustomerDataScreen from "../screens/CustomerDataScreen";
import MainRoutingScreen from "./../screens/MainRoutingScreen";
import ConnectionErrorScreen from "./../screens/ConnectionErrorScreen";
import FaceRecognitionScreen from "./../screens/FaceRecognitionScreen";
import OtpScreen from "./../screens/OtpScreen";
import RedemptionScreen from "../screens/RedemptionScreen";
import EmailScreen from "../screens/EmailScreen";
import PaymentModeScreen from "../screens/PaymentModeScreen";
import PaymentQrScanner from "../screens/PaymentQrScanner";
import CustomerAcknowledgeScreen from "../screens/CustomerAcknowledgeScreen";

const OrderFlowNavigator = createStackNavigator({
    Home: {screen: HomeScreen, navigationOptions: {header: null}},
    CustomerData: {screen: CustomerDataScreen, navigationOptions: {header: null}},
    FaceRecognition: {screen: FaceRecognitionScreen, navigationOptions: {header: null}},
    EnterOtp: {screen: OtpScreen, navigationOptions: {header: null}},
    EnterEmail: {screen: EmailScreen, navigationOptions: {header: null}},
    Redemption: {screen: RedemptionScreen, navigationOptions: {header: null}},
    MakePayment: {screen: PaymentModeScreen, navigationOptions: {header: null}},
    QrScanner: {screen: PaymentQrScanner, navigationOptions: {header: null}},
    AcknowledgeScreen: {screen: CustomerAcknowledgeScreen, navigationOptions: {header: null}}
}, {
    navigationOptions: {
        header: null
    },
    initialRoute: "Home",
    // initialRoute: "AcknowledgeScreen",
    contentOptions: {
        activeTintColor: '#5e7e47',
        inactiveTintColor: 'grey'
    }
});

const AppMainNavigator = createSwitchNavigator({
    MainRouting: {
        screen: MainRoutingScreen,
        navigationOptions: {header: null}
    },
    Login: {
        screen: LoginScreen,
        navigationOptions: {
            header: null
        }
    },
    AttendanceScreen : {
        screen: AttendanceScreen,
        navigationOptions: {
            header: null
        }
    },
    ConnectionError: {
        screen: ConnectionErrorScreen,
        navigationOptions: {
            header: null
        }
    },
    OrderFlow: {
        // screen: CustomerAcknowledgeScreen,
        screen: OrderFlowNavigator,
        navigationOptions: {}
    }
}, {
    initialRoute: "MainRouting",
    contentOptions: {
        activeTintColor: '#5e7e47',
        inactiveTintColor: 'grey'
    }
});

export default createAppContainer(AppMainNavigator);