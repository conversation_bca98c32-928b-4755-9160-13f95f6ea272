<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/rlParent"
                android:background="@android:color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
    <LinearLayout
            android:id="@+id/topLayout"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:keepScreenOn="true">

        <com.crmapp.camera.CameraSourcePreview
                android:id="@+id/preview"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <com.crmapp.camera.GraphicOverlay
                    android:id="@+id/faceOverlay"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>

        </com.crmapp.camera.CameraSourcePreview>

    </LinearLayout>
    <LinearLayout
            android:id="@+id/scanView"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:background="@android:color/white"
            android:visibility="gone">

        <TextView
                android:text="Reading Face..."
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="32dp"
                android:gravity="center"
                android:layout_gravity="center"
                android:textColor="@android:color/black"
                android:id="@+id/scanText" />
    </LinearLayout>

</RelativeLayout>