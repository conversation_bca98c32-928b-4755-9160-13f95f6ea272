<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.crmapp">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.MICROPHONE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>

    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR"/>
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <application
            android:name=".MainApplication"
            android:label="@string/app_name"
            android:icon="@mipmap/ic_launcher"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:allowBackup="true"
            android:hardwareAccelerated="true"
            android:usesCleartextTraffic="true"
            android:theme="@style/AppTheme">

        <service
                android:name=".BackgroundService"
                android:exported="true" />

        <activity
                android:name=".MainActivity"
                android:label="@string/app_name"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
                android:screenOrientation="portrait"
                android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
                android:name=".FaceTrackerActivity"
                android:label="Face Tracker"
                android:theme="@style/Theme.AppCompat.NoActionBar"
                android:hardwareAccelerated="true"
                android:screenOrientation="portrait">
        </activity>
        <activity
                android:name=".AppUpdateActivity"
                android:label="App Update"
                android:theme="@style/Theme.AppCompat.NoActionBar"
                android:screenOrientation="portrait">
        </activity>
        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"/>

        <receiver android:name=".BootReceiver"
                  android:enabled="true"
                  android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

<!--        <provider-->
<!--                android:name="android.support.v4.-->
<!--                android:authorities="${applicationId}.provider"-->
<!--                android:exported="false"-->
<!--                android:grantUriPermissions="true">-->
<!--            <meta-data-->
<!--                    android:name="android.support.FILE_PROVIDER_PATHS"-->
<!--                    android:resource="@xml/provider_paths"/>-->
<!--        </provider>-->
    </application>

</manifest>
