package com.crmapp;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Environment;
//import android.support.design.widget.Snackbar;
//import android.support.v4.app.ActivityCompat;
//import android.support.v4.content.ContextCompat;
//import android.support.v7.app.AppCompatActivity;
import android.util.Base64;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.crmapp.camera.CameraSourcePreview;
import com.crmapp.camera.GraphicOverlay;
import com.crmapp.util.FaceUtils;
import com.google.android.gms.vision.CameraSource;
import com.google.android.gms.vision.Frame;
import com.google.android.gms.vision.MultiProcessor;
import com.google.android.gms.vision.Tracker;
import com.google.android.gms.vision.face.Face;
import com.google.android.gms.vision.face.FaceDetector;
import com.google.gson.Gson;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public final class FaceTrackerActivity extends AppCompatActivity {
    private static final String TAG = "FaceTracker";

    private CameraSource mCameraSource = null;

    private CameraSourcePreview mPreview;
    private GraphicOverlay mGraphicOverlay;

    private static final int RC_HANDLE_GMS = 9001;
    // permission request codes need to be < 256
    private static final int RC_HANDLE_CAMERA_PERM = 2;

    private FaceUtils faceUtils = new FaceUtils();

    public String FILE_SAVED_PATH = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Android/data/FaceTracker/";

    public static boolean safeToTakePicture = true;

    private static boolean saveImage = true;

    @Override
    public void onCreate(Bundle icicle) {
        super.onCreate(icicle);
        Intent intent = getIntent();
        String sessionId = intent.getStringExtra("sessionId");
        saveImage = intent.getStringExtra("saveImage") == "true";
        //Toast.makeText(getApplicationContext(), sessionId, 1).show();
        FILE_SAVED_PATH = FILE_SAVED_PATH + sessionId;
        setContentView(R.layout.tracker);
        LinearLayout view = (LinearLayout)findViewById(R.id.scanView);
        view.setVisibility(View.INVISIBLE);
        LinearLayout linearLayout = (LinearLayout)findViewById(R.id.topLayout);
        linearLayout.setVisibility(View.VISIBLE);
        safeToTakePicture = true;
        mPreview = (CameraSourcePreview) findViewById(R.id.preview);
        mGraphicOverlay = (GraphicOverlay) findViewById(R.id.faceOverlay);
        // Check for the camera permission before accessing the camera.  If the
        // permission is not granted yet, request permission.
        int rc = ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA);
        if (rc == PackageManager.PERMISSION_GRANTED) {
            createCameraSource();
        } else {
            requestCameraPermission();
        }
    }

    /**
     * Handles the requesting of the camera permission.  This includes
     * showing a "Snackbar" message of why the permission is needed then
     * sending the request.
     */
    private void requestCameraPermission() {
        Log.w(TAG, "Camera permission is not granted. Requesting permission");
        final String[] permissions = new String[]{Manifest.permission.CAMERA};
        if (!ActivityCompat.shouldShowRequestPermissionRationale(this,
                Manifest.permission.CAMERA)) {
            ActivityCompat.requestPermissions(this, permissions, RC_HANDLE_CAMERA_PERM);
            return;
        }
        final Activity thisActivity = this;
        View.OnClickListener listener = new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ActivityCompat.requestPermissions(thisActivity, permissions,
                        RC_HANDLE_CAMERA_PERM);
            }
        };

        /*Snackbar.make(mGraphicOverlay, R.string.permission_camera_rationale,
                Snackbar.LENGTH_INDEFINITE)
                .setAction(R.string.ok, listener)
                .show();*/
    }

    /**
     * Creates and starts the camera.  Note that this uses a higher resolution in comparison
     * to other detection examples to enable the barcode detector to detect small barcodes
     * at long distances.
     */
    private void createCameraSource() {

        Context context = getApplicationContext();
        FaceDetector detector = new FaceDetector.Builder(context)
                .setMode(FaceDetector.FAST_MODE)
                .build();

        detector.setProcessor(
                new MultiProcessor.Builder<>(new GraphicFaceTrackerFactory())
                        .build());

        if (!detector.isOperational()) {
            // Note: The first time that an app using face API is installed on a device, GMS will
            // download a native library to the device in order to do detection.  Usually this
            // completes before the app is run for the first time.  But if that download has not yet
            // completed, then the above call will not detect any faces.
            //
            // isOperational() can be used to check if the required native library is currently
            // available.  The detector will automatically become operational once the library
            // download completes on device.
            Log.w(TAG, "Face detector dependencies are not yet available.");
        }

        mCameraSource = new CameraSource.Builder(context, detector)
                .setRequestedPreviewSize(640, 480)
                .setFacing(CameraSource.CAMERA_FACING_FRONT)
                .setRequestedFps(60.0f)
                .build();
    }

    /**
     * Restarts the camera.
     */
    @Override
    protected void onResume() {
        super.onResume();
        startCameraSource();
    }

    /**
     * Stops the camera.
     */
    @Override
    protected void onPause() {
        super.onPause();
        mPreview.stop();
    }

    /**
     * Releases the resources associated with the camera source, the associated detector, and the
     * rest of the processing pipeline.
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mCameraSource != null) {
            mCameraSource.release();
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode != RC_HANDLE_CAMERA_PERM) {
            Log.d(TAG, "Got unexpected permission result: " + requestCode);
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
            return;
        }

        if (grantResults.length != 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            Log.d(TAG, "Camera permission granted - initialize the camera source");
            // we have permission, so create the camerasource
            createCameraSource();
            return;
        }

        Log.e(TAG, "Permission not granted: results len = " + grantResults.length +
                " Result code = " + (grantResults.length > 0 ? grantResults[0] : "(empty)"));

        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
                finish();
            }
        };

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Face Tracker sample")
                .setMessage(R.string.no_camera_permission)
                .setPositiveButton(R.string.ok, listener)
                .show();
    }

    //==============================================================================================
    // Camera Source Preview
    //==============================================================================================

    /**
     * Starts or restarts the camera source, if it exists.  If the camera source doesn't exist yet
     * (e.g., because onResume was called before the camera source was created), this will be called
     * again when the camera source is created.
     */
    private void startCameraSource() {

        // check that the device has play services available.
        int code = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(
                getApplicationContext());
        if (code != ConnectionResult.SUCCESS) {
            Dialog dlg =
                    GoogleApiAvailability.getInstance().getErrorDialog(this, code, RC_HANDLE_GMS);
            dlg.show();
        }

        if (mCameraSource != null) {
            try {
                mPreview.start(mCameraSource, mGraphicOverlay);
            } catch (IOException e) {
                Log.e(TAG, "Unable to start camera source.", e);
                mCameraSource.release();
                mCameraSource = null;
            }
        }
    }

    //==============================================================================================
    // Graphic Face Tracker
    //==============================================================================================

    /**
     * Factory for creating a face tracker to be associated with a new face.  The multiprocessor
     * uses this factory to create face trackers as needed -- one for each individual.
     */
    private class GraphicFaceTrackerFactory implements MultiProcessor.Factory<Face> {
        @Override
        public Tracker<Face> create(Face face) {
            return new GraphicFaceTracker(mGraphicOverlay);
        }
    }

    /**
     * Face tracker for each detected individual. This maintains a face graphic within the app's
     * associated face overlay.
     */
    private class GraphicFaceTracker extends Tracker<Face> {
        private GraphicOverlay mOverlay;
        private FaceGraphic mFaceGraphic;

        GraphicFaceTracker(GraphicOverlay overlay) {
            mOverlay = overlay;
            mFaceGraphic = new FaceGraphic(overlay);
        }

        /**
         * Start tracking the detected face instance within the face overlay.
         */
        @Override
        public void onNewItem(int faceId, Face item) {
            mFaceGraphic.setId(faceId);
            if (item.getEulerY() >= -5 && item.getEulerY() <= 5) {
                try {
                    if (safeToTakePicture) {
                        safeToTakePicture = false;
                        mCameraSource.takePicture(new CameraSource.ShutterCallback() {
                            @Override
                            public void onShutter() {
                                View view = (View)findViewById(R.id.scanView);
                                view.setVisibility(View.VISIBLE);
                                LinearLayout linearLayout = (LinearLayout)findViewById(R.id.topLayout);
                                linearLayout.setVisibility(View.GONE);
                            }
                        }, new PictureCallback());
                    }
                } catch (Exception e) {
                    Intent mIntent = new Intent();
                    mIntent.putExtra("result", "cancel");
                    setResult(Activity.RESULT_OK, mIntent);
                    finish();
                }
            }
        }

        /**
         * Update the position/characteristics of the face within the overlay.
         */
        @Override
        public void onUpdate(FaceDetector.Detections<Face> detectionResults, Face face) {
            mOverlay.add(mFaceGraphic);
            //mFaceGraphic.updateFace(face);
            if (face.getEulerY() >= -5 && face.getEulerY() <= 5) {
                try {
                    if (safeToTakePicture){
                        safeToTakePicture = false;
                        mCameraSource.takePicture(new CameraSource.ShutterCallback() {
                            @Override
                            public void onShutter() {
                                View view = (View)findViewById(R.id.scanView);
                                view.setVisibility(View.VISIBLE);
                                LinearLayout linearLayout = (LinearLayout)findViewById(R.id.topLayout);
                                linearLayout.setVisibility(View.GONE);
                            }
                        }, new PictureCallback());
                    }
                } catch (Exception e) {
                    Intent mIntent = new Intent();
                    mIntent.putExtra("result", "cancel");
                    setResult(Activity.RESULT_OK, mIntent);
                    finish();
                }
            }
        }

        /**
         * Hide the graphic when the corresponding face was not detected.  This can happen for
         * intermediate frames temporarily (e.g., if the face was momentarily blocked from
         * view).
         */
        @Override
        public void onMissing(FaceDetector.Detections<Face> detectionResults) {
            mOverlay.remove(mFaceGraphic);
        }

        /**
         * Called when the face is assumed to be gone for good. Remove the graphic annotation from
         * the overlay.
         */
        @Override
        public void onDone() {
            mOverlay.remove(mFaceGraphic);
        }
    }

    private class PictureCallback implements CameraSource.PictureCallback {

        @Override
        public void onPictureTaken(final byte[] bytes) {
            try {
                /*if (mPreview != null) {
                    mPreview.stop();
                    //mPreview.release();
                }*/
                Thread t = new Thread() {
                    @Override
                    public void run() {
                        super.run();
                        BitmapFactory.Options options = new BitmapFactory.Options();
                        options.inJustDecodeBounds = false;
                        options.inPurgeable = true;
                        options.inInputShareable = true;
                        options.inTempStorage = new byte[16 * 1024];
                        final Bitmap bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
                        int IMG_WIDTH = 900;
                        float IMG_HEIGHT = 0;
                        float scale = (float) bitmap.getWidth() / IMG_WIDTH;
                        IMG_HEIGHT = bitmap.getHeight() / scale;
                        final Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, IMG_WIDTH, (int) IMG_HEIGHT, false);
                        final Matrix matrix = new Matrix();
                        String deviceModel = android.os.Build.MODEL;
                        if(deviceModel.equalsIgnoreCase("Lenovo TB-X304L")) {
                            /*matrix.preScale(-1, 1);
                            Bitmap mirroredBitmap = Bitmap.createBitmap(scaledBitmap, 0, 0, scaledBitmap.getWidth(), scaledBitmap.getHeight(), matrix, false);
                            cropImage(mirroredBitmap);*/
                            cropImage(scaledBitmap);
                        } else {
                            matrix.postRotate(-90);
                            Bitmap rotatedBitmap = Bitmap.createBitmap(scaledBitmap, 0, 0, scaledBitmap.getWidth(), scaledBitmap.getHeight(), matrix, false);
                            cropImage(rotatedBitmap);
                        }
                    }
                };
                t.start();
            } catch (Exception e) {
                Intent mIntent = new Intent();
                mIntent.putExtra("result", "cancel");
                setResult(Activity.RESULT_OK, mIntent);
                finish();
            }
            //safeToTakePicture = true;
        }
    }

    public void cropImage(Bitmap input) {
        try {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this,
                        new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},
                        200);
            }
            FaceDetector faceDetector = new FaceDetector.Builder(getApplicationContext())
                    //.setProminentFaceOnly(true)
                    .setMode(FaceDetector.FAST_MODE)
                    //.setTrackingEnabled(false)
                    .build();
            if (!faceDetector.isOperational()) {
                //new AlertDialog.Builder(this.getContext()).setMessage("Could not set up the face detector!").show();
                return;
            }
            Frame frame = new Frame.Builder().setBitmap(input).build();
            SparseArray<Face> faces = faceDetector.detect(frame);
            Bitmap output = null;
            boolean faceFound = false;
            if(faces.size() > 0) {
                for (int i = 0; i < faces.size(); i++) {
                    Face thisFace = faces.valueAt(i);
                    if(thisFace.getEulerY() >= -8 && thisFace.getEulerY() <= 8) {
                        float x1 = thisFace.getPosition().x;
                        float y1 = thisFace.getPosition().y;
                        /*float x2 = x1 + thisFace.getWidth();
                        float y2 = y1 + thisFace.getHeight();*/
                        /*output = Bitmap.createBitmap((int) thisFace.getWidth(), (int) thisFace.getHeight() + 200, Bitmap.Config.RGB_565);
                        output = Bitmap.createBitmap(input, (int) x1, (int) y1 - 100, output.getWidth(), output.getHeight() + 100);*/
                        try {
                            output = Bitmap.createBitmap((int) thisFace.getWidth(), (int) thisFace.getHeight() + 200, Bitmap.Config.RGB_565);
                            output = Bitmap.createBitmap(input, (int) x1, (int) y1 - 100, output.getWidth(), output.getHeight() + 100);
                        } catch (Exception e) {
                            output = Bitmap.createBitmap((int) thisFace.getWidth(), (int) thisFace.getHeight(), Bitmap.Config.RGB_565);
                            output = Bitmap.createBitmap(input, (int) x1, (int) y1, output.getWidth(), output.getHeight());
                        }
                        if(saveImage) {
                            Thread t = new Thread() {
                                @Override
                                public void run() {
                                    super.run();
                                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                                    input.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
                                    byte[] byteArray = byteArrayOutputStream.toByteArray();
                                    String inputb64 = Base64.encodeToString(byteArray, Base64.DEFAULT);
                                    try {
                                        createDirectoryIfNotExist(FILE_SAVED_PATH, "originalb64.txt");
                                        File storageFile = new File(FILE_SAVED_PATH, "originalb64.txt");
                                        FileOutputStream out = new FileOutputStream(storageFile.getAbsolutePath());
                                        out.write(inputb64.getBytes());
                                        out.close();
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
                                }
                            };
                            t.start();
                        }
                        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                        byte[] byteArray;
                        int IMG_WIDTH = 200;
                        float IMG_HEIGHT = 0;
                        float scale = (float) output.getWidth() / IMG_WIDTH;
                        IMG_HEIGHT = output.getHeight() / scale;
                        Bitmap scaledBitmap = Bitmap.createScaledBitmap(output, IMG_WIDTH, (int) IMG_HEIGHT, false);
                        scaledBitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
                        byteArray = byteArrayOutputStream.toByteArray();
                        String scaledBase64 = Base64.encodeToString(byteArray, Base64.DEFAULT);
                        /*try {
                            createDirectoryIfNotExist(FILE_SAVED_PATH, "scaledb64.txt");
                            File storageFile = new File(FILE_SAVED_PATH, "scaledb64.txt");
                            FileOutputStream out = new FileOutputStream(storageFile.getAbsolutePath());
                            out.write(scaledBase64.getBytes());
                            out.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }*/
                        mCameraSource = null;
                        Intent mIntent = new Intent();
                        mIntent.putExtra("result", "success");
                        mIntent.putExtra("face", scaledBase64);
                        setResult(Activity.RESULT_OK, mIntent);
                        if(!faceFound) {
                            faceFound = true;
                        }
                        finish();
                    }
                }
                /*if(!faceFound) {
                    mCameraSource = null;
                    Intent mIntent = new Intent();
                    mIntent.putExtra("result", "no_face");
                    setResult(Activity.RESULT_OK, mIntent);
                    finish();
                }*/
                View view = (View)findViewById(R.id.scanView);
                view.setVisibility(View.GONE);
                LinearLayout linearLayout = (LinearLayout)findViewById(R.id.topLayout);
                linearLayout.setVisibility(View.VISIBLE);
                safeToTakePicture = true;
            } else {
                mCameraSource = null;
                Intent mIntent = new Intent();
                mIntent.putExtra("result", "no_face");
                setResult(Activity.RESULT_OK, mIntent);
                finish();
            }
        } catch (Exception e) {
            mCameraSource = null;
            Intent mIntent = new Intent();
            mIntent.putExtra("result", "cancel");
            setResult(Activity.RESULT_OK, mIntent);
            finish();
        }
    }

    public void createDirectoryIfNotExist(String path, String fileName) {
        File logDir = new File(path);
        if (!logDir.exists()) {
            final boolean dir = logDir.mkdirs();
            System.out.println(dir);
        }
        File file = new File(logDir, fileName);
        if (!file.exists()) {
            try {
                final boolean newFile = file.createNewFile();
            } catch (IOException ioe) {
                ioe.printStackTrace();
            }
        }
    }
}
