package com.crmapp;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.PowerManager;
import android.util.Log;

public class AppContext extends Application {

    private PowerManager.WakeLock mWakeLock;
    private OnScreenOffReceiver onScreenOffReceiver;
    private String TAG="CustomerScreen";
    private String TestFairyApptoken=null;
    @Override
    public void onCreate() {
        super.onCreate();
        fillMetadata();
        registerKioskModeScreenOffReceiver();
    }


    public void fillMetadata()
    {
        try {
            ApplicationInfo ai = getPackageManager().getApplicationInfo(this.getPackageName(), PackageManager.GET_META_DATA);
            Bundle bundle = ai.metaData;
            TestFairyApptoken = bundle.getString("testfairy_AppToken");
        } catch (PackageManager.NameNotFoundException e) {
           Log.e(TAG, "Failed to load meta-data, NameNotFound: " + e.getMessage());
        } catch (NullPointerException e) {
            Log.e(TAG, "Failed to load meta-data, NullPointer: " + e.getMessage());
        }
    }
    private void registerKioskModeScreenOffReceiver() {
        final IntentFilter filter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
        onScreenOffReceiver = new OnScreenOffReceiver();
        registerReceiver(onScreenOffReceiver, filter);
        startKioskService();
    }

    public PowerManager.WakeLock getWakeLock() {
        if (mWakeLock == null) {
            PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
            mWakeLock = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "CustomerScreen:wakeup");
        }
        return mWakeLock;
    }

    private void startKioskService() {
        startService(new Intent(this, BackgroundService.class));
    }
}
