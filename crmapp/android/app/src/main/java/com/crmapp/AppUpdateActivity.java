package com.crmapp;

import android.Manifest;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;

import java.io.File;

import static io.invertase.firebase.app.ReactNativeFirebaseApp.getApplicationContext;


public final class AppUpdateActivity extends AppCompatActivity {

    @Override
    public void onCreate(Bundle icicle) {
        super.onCreate(icicle);
        setContentView(R.layout.activity_app_update);
        int rc = ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
        int rci = ActivityCompat.checkSelfPermission(this, Manifest.permission.REQUEST_INSTALL_PACKAGES);
        Intent intent = getIntent();
        String url = intent.getStringExtra("url");
        if (rc == PackageManager.PERMISSION_GRANTED && rci == PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(getApplicationContext(), url, Toast.LENGTH_LONG).show();
            autoUpdate(url);
        } else {
            requestPermission(url);
        }
    }

    private void autoUpdate(String url) {
        //Toast.makeText(getApplicationContext(), "url", Toast.LENGTH_LONG).show();
        final String destination = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Android/data/crmapp/crm.apk";
        //final String destination = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/crm.apk";
        Uri uri = Uri.parse("file://" + destination);
        //Delete update file if exists
        File file = new File(destination);
        if (file.exists())
            file.delete();
        //set downloadmanager
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
        request.setDescription("Customer screen new release.");
        request.setTitle("Customer Screen");
        //set destination
        request.setDestinationUri(uri);
        // get download service and enqueue file
        final DownloadManager manager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);
        final long downloadId = manager.enqueue(request);
        TextView view = (TextView) findViewById(R.id.textView);
        view.setText("Downloading new updates...");
        BroadcastReceiver onComplete = new BroadcastReceiver() {
            public void onReceive(Context ctxt, Intent intent) {
                TextView view = (TextView) findViewById(R.id.textView);
                view.setText("Installing updates...");
                final Handler handler = new Handler();
                final BroadcastReceiver receiver = this;
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Uri apkURI = FileProvider.getUriForFile(getApplicationContext(),
                                getApplicationContext().getPackageName() + ".provider", new File(destination));
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        /*intent.setDataAndType(manager.getUriForDownloadedFile(downloadId),
                                "application/vnd.android.package-archive");*/
                        intent.setDataAndType(apkURI, "application/vnd.android.package-archive");
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK); // without this flag android returned a intent error!
                        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                        System.out.println("Installing package!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");

                        ctxt.startActivity(intent);
                        unregisterReceiver(receiver);
                        finish();
                    }
                }, 5000);
            }
        };
        //register receiver for when .apk download is compete
        registerReceiver(onComplete, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
    }

    private void requestPermission() {
        final String[] permissions = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE};
        if (!ActivityCompat.shouldShowRequestPermissionRationale(this,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            ActivityCompat.requestPermissions(this, permissions, 646);
            return;
        }
        final Activity thisActivity = this;
        View.OnClickListener listener = new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ActivityCompat.requestPermissions(thisActivity, permissions,
                        646);
            }
        };
    }

    private void requestPermission(final String url) {
        final String[] permissions = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.REQUEST_INSTALL_PACKAGES};
        ActivityCompat.requestPermissions(this, permissions, 646);
        autoUpdate(url);
    }
}