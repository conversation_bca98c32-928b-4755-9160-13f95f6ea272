package com.crmapp;

import android.app.ActivityManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.IBinder;
import android.preference.PreferenceManager;
import android.util.Log;

import java.util.List;
import java.util.concurrent.TimeUnit;

public class BackgroundService extends Service {

    private static final long CHECK_INTERVAL = TimeUnit.SECONDS.toMillis(2);
    private static final String TAG = BackgroundService.class.getSimpleName();
    private static final String PREF_KIOSK_MODE = "pref_kiosk_mode";

    private Thread mCheckThread = null;
    private Context mContext = null;
    private boolean mRunning = false;

    @Override
    public void onDestroy() {
        mRunning = false;
        super.onDestroy();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        mRunning = true;
        mContext = this;
        mCheckThread = new Thread(new Runnable() {
            @Override
            public void run() {
                do {
                    handleKioskMode();
                    try {
                        Thread.sleep(CHECK_INTERVAL);
                    } catch (InterruptedException e) {
                        Log.i(TAG, "Thread interrupted: 'BackgroundService'");
                    }
                } while (mRunning);
                stopSelf();
            }
        });

        mCheckThread.start();
        return Service.START_NOT_STICKY;
    }

    private void handleKioskMode() {
        if (isKioskModeActive()) {
            if (isInBackground()) {
                restoreApp(); // restore!
            }
        }
    }

    private boolean isInBackground() {
        ActivityManager am = (ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);
        assert am != null;
        List<ActivityManager.RunningTaskInfo> taskInfo = am.getRunningTasks(1);
        ComponentName componentInfo = taskInfo.get(0).topActivity;
        return (!mContext.getApplicationContext().getPackageName().equals(componentInfo.getPackageName()));
    }

    private void restoreApp() {
        Intent i = new Intent(mContext, MainActivity.class);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(i);
    }

    public boolean isKioskModeActive() {
        SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(mContext);
        return sp.getBoolean(PREF_KIOSK_MODE, true);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}