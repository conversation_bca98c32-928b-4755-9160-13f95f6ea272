package com.crmapp;

import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.BaseActivityEventListener;
import com.facebook.react.ReactInstanceManager;
import android.widget.Toast;
import android.app.AlertDialog;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Environment;
import android.os.Bundle;
import android.util.Base64;
import android.util.SparseArray;
import android.content.Intent;
import com.google.android.gms.vision.Frame;
import com.google.android.gms.vision.face.Face;
import com.google.android.gms.vision.face.FaceDetector;


import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import com.crmapp.model.FaceImageData;

public class FaceTrackerModule extends ReactContextBaseJavaModule {

    //public static final String FILE_SAVED_PATH = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Android/data/FaceTracker";

    private static final int REQUEST_CODE = 681;
    private static final int UPDATE_REQUEST_CODE = 682;
    private static final String E_ACTIVITY_DOES_NOT_EXIST = "E_ACTIVITY_DOES_NOT_EXIST";
    private static final String E_PICKER_CANCELLED = "E_PICKER_CANCELLED";
    private static final String E_FAILED_TO_START_ACTIVITY = "E_FAILED_TO_START_ACTIVITY";
    private static final String E_NO_FACE_DATA_FOUND = "E_NO_IMAGE_DATA_FOUND";

    private ReactInstanceManager mReactInstanceManager;

    private Promise mPromise;

    private final ActivityEventListener mActivityEventListener = new BaseActivityEventListener() {

        @Override
        public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent intent) {
            super.onActivityResult(requestCode, resultCode, intent);
            if (requestCode == REQUEST_CODE) {
                if (mPromise != null) {
                    if (resultCode == Activity.RESULT_CANCELED) {
                        mPromise.reject(E_PICKER_CANCELLED, "Face Recognition activity was cancelled.");
                    } else if (resultCode == Activity.RESULT_OK) {
                        //Toast.makeText(getReactApplicationContext(), "result success" + resultCode, 1).show();
                        //Bundle bundle = intent.getExtras();
                        String result = intent.getStringExtra("result");
                        if (result.equalsIgnoreCase("success")) {
                            mPromise.resolve(intent.getStringExtra("face"));
                        } else {
                            mPromise.resolve(result);
                        }
                    } else {
                        mPromise.resolve("Resolved with no result ok");
                    }
                    mPromise = null;
                }
            }
        }
    };

    public FaceTrackerModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(mActivityEventListener);
    }

    @Override
    public String getName() {
        return "FaceTracker";
    }

    @ReactMethod
    public void trackFaces(final String sessionId, final boolean saveImage, final Promise promise) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) {
            promise.reject(E_ACTIVITY_DOES_NOT_EXIST, "Activity doesn't exist");
            return;
        }
        mPromise = promise;
        try {
            Intent intent = new Intent(getReactApplicationContext(), FaceTrackerActivity.class);
            intent.putExtra("sessionId", sessionId);
            intent.putExtra("saveImage", saveImage);
            currentActivity.startActivityForResult(intent, REQUEST_CODE);
        } catch (Exception e) {
            mPromise.reject(E_FAILED_TO_START_ACTIVITY, e);
            mPromise = null;
        }
    }

    @ReactMethod
    public void updateApp(String url) {
        //Toast.makeText(getReactApplicationContext(), "step 1", 1).show();
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) {
            return;
        }
        try {
            Intent intent = new Intent(getReactApplicationContext(), AppUpdateActivity.class);
            intent.putExtra("url", url);
            currentActivity.startActivityForResult(intent, UPDATE_REQUEST_CODE);
        } catch (Exception e) {
            mPromise.reject(E_FAILED_TO_START_ACTIVITY, e);
            mPromise = null;
        }
    }

}