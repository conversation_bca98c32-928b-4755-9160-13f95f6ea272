package com.crmapp.util;

import android.graphics.Bitmap;
import android.graphics.Matrix;

import com.crmapp.camera.GraphicOverlay;
import com.google.android.gms.vision.face.Face;
import com.google.android.gms.vision.face.Landmark;

public class FaceUtils {

    private Face face;
    private Landmark leftEye = null;
    private Landmark rightEye = null;

    public Face getFace() {
        return face;
    }

    public void setFace(Face face) {
        this.face = face;
        locateEyes();
    }

    private void locateEyes() {
        for (Landmark landmark : face.getLandmarks()) {
            switch (landmark.getType()) {
                case Landmark.LEFT_EYE:
                    leftEye = landmark;
                    break;
                case Landmark.RIGHT_EYE:
                    rightEye = landmark;
                    break;
                default: break;
            }
        }
    }

    public boolean doesFaceHaveTwoEyes() {
        if (leftEye != null && rightEye != null) {
            return true;
        } else {
            return false;
        }
    }

    public boolean isFrontFacing() {
        if (face.getEulerY() >= -5 && face.getEulerY() <= 5) { // && face.getEulerZ() >= -5 && face.getEulerZ() <= 5
            return true;
        } else {
            return false;
        }
    }

    public int getEyesDistance() {
        float eyeDistance = rightEye.getPosition().x - leftEye.getPosition().x;
        return Math.abs((int)eyeDistance);
    }

    private Bitmap rotateAndMirrorBitmap(Bitmap input) {
        Matrix matrix = new Matrix();
        matrix.preScale(1.0f, -1.0f);
        matrix.postRotate(-90);
        return Bitmap.createBitmap(input, 0, 0, input.getWidth(), input.getHeight(), matrix, true);
    }
}
