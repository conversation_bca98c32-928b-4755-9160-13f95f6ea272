using ReactNative.Bridge;
using System;
using System.Collections.Generic;
using Windows.ApplicationModel.Core;
using Windows.UI.Core;

namespace Face.Tracker.Library.RNFaceTrackerLibrary
{
    /// <summary>
    /// A module that allows JS to share data.
    /// </summary>
    class RNFaceTrackerLibraryModule : NativeModuleBase
    {
        /// <summary>
        /// Instantiates the <see cref="RNFaceTrackerLibraryModule"/>.
        /// </summary>
        internal RNFaceTrackerLibraryModule()
        {

        }

        /// <summary>
        /// The name of the native module.
        /// </summary>
        public override string Name
        {
            get
            {
                return "RNFaceTrackerLibrary";
            }
        }
    }
}
