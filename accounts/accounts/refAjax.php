<?php
session_start();
if ($_REQUEST['ajaxaction'] == "addInfoAjax") {
     
    if (!empty($_REQUEST['captcha'])) {
        if (empty($_SESSION['captcha']) || trim(strtolower($_REQUEST['captcha'])) != $_SESSION['captcha']) {
            echo '0';
        } else {
            echo '1';
        }
    }
} 
else if ($_REQUEST['ajaxaction'] == "verifyCustAjax") {

$sessionData = $_REQUEST['sessionData'];
echo $json_a = json_encode($sessionData);
 
} else {
    echo "No Data Available";
}
?>