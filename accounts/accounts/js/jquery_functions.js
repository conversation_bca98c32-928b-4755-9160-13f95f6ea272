var httpUrl = "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/customer-profile/";

// var httpUrl =
// "http://************:9595/kettle-service/rest/v1/customer-profile/";

var vservUrl = "http://c.vserv.mobi/delivery/ti.php?trackerid=2682&app=1";

var sessionData;

var vserv = "";

function addInfo() {

	var myPhone = $("#myNumber").val();

	var captcha = $("#captcha-form").val();

	var token = $("#tokens").val();

	var source = $("#sources").val();

	var ipadd = $("#ip-add").val();

	var vserv = $("#vserv").val();

	if (myPhone == "") {

		alert("Please Enter My Phone");

		$("#myNumber").focus();

		return false;

	}

	if (!(myPhone.length == 10)) {

		alert("\nPlease enter 10 digit mobile no.");

		$("#myNumber").focus();

		return false;

	}

	if (captcha == "") {

		alert("Please Input tha above Text");

		$("#captcha-form").focus();

		return false;

	}

	var ajaxaction = "addInfoAjax";

	$.post("refAjax.php", {

		ajaxaction : ajaxaction,

		captcha : captcha

	}, function(data) {

		if (data.trim() == '0') {

			$("#campMsgDV").hide();

			$("#campMsgDV").html('Invalid code');

			return false;

		}

		else if (data == '1') {

			$("#btns").hide();

			$("#loaderID").show();

			sessionData = lookupCustomer(myPhone, source, token);

		}

	});

	return false;

}

$.postJSON = function(url, data, callback) {

	return jQuery.ajax({

		'type' : 'POST',

		'url' : url,

		'contentType' : 'application/json',

		'data' : JSON.stringify(data),

		'dataType' : 'json',

		'success' : callback

	});

};

function lookupCustomer(myPhone, source, token) {

	var cust = createCustomer(myPhone, source, token);

	$

	.postJSON(

	httpUrl + "lookup",

	cust,

	function(data) {

		if (!data.otpSent) {

			$("#campMsgDV").html('Failed to send the OTP');

		} else {

			$("#dvOTP").show();

			$("#campMsgDV")

			.html(

			'<img src="images/loading.gif" width="35px" height="35px">');

			$("#campMsgDV").hide();

			$("#dvRegistraion").hide();

			$("#otp").val("");

		}

		sessionData = data;

	});

}

function createCustomer(contactNumber, source, token) {

	var customer = {

		contactNumber : contactNumber,

		countryCode : "+91",

		acquisitionSource : source,

		acquisitionToken : token

	};

	var request = {

		customer : customer

	};

	return request;

}

function verifyCustomer(cust) {

	$.postJSON(httpUrl + "verify/signup", cust, function(data) {

		sessionData = data;

		console.log(data.otpVerificationStatus);

		if (data.otpVerificationStatus) {

			$("#dvOTP").hide();

			$("#campMsgDV").hide();

			$("#dvReg").show();

			setValues(sessionData.customer);

			var chkNew = data.newCustomer;

			if (chkNew == true) {

				$.getJSON(vservUrl + "&vserv=" + vserv);

			}

			// $.postJSON(httpUrl)

			// function (data) {

			// });

			// });

		} else {

			$("#DVSOTPResponse").hide();

			$("#DVSOTPResponse").html("OTP did not match. Please retry");

			$("#DVSOTPResponse").show();

			$("#veriFybtn").show();

			$("#verifyLoaderID").hide();

		}

	});

}

function setValues(customer) {

	// alert(customer.firstName+'ss');

	if (customer.firstName != null && customer.firstName != "") {

		$("#nameExt").val(customer.firstName);

		$("#nameExt").prop('disabled', true);

	} else {

		$("#nameExt").prop('disabled', false);

		// $("#updateProfile").show();

	}

	if (customer.emailId != null && customer.emailId != "") {

		$("#emailExt").val(customer.emailId);

		$("#emailExt").prop("disabled", true);

	} else {

		// $("#updateProfile").show();

		$("#emailExt").prop('disabled', false);

	}

	if (customer.firstName == "" || customer.firstName == null

	|| customer.emailId == "" || customer.emailId == null) {

		$("#updateProfile").show();

	} else {

		$("#updateProfile").hide();
	}

	$('#loyaltyPoints').text(customer.loyaltyPoints);

}

function sendOTP() {

	var otp = $("#otp").val();

	console.log(sessionData);

	sessionData.otpText = otp;

	verifyCustomer(sessionData);

	$("#veriFybtn").hide();

	$("#verifyLoaderID").show();

}

function updateCustomerInfo() {

	var regex = /^([0-9a-zA-Z]([-_\\.]*[0-9a-zA-Z]+)*)@([0-9a-zA-Z]([-_\\.]*[0-9a-zA-Z]+)*)[\\.]([a-zA-Z]{2,9})$/;

	sessionData.customer.firstName = $("#nameExt").val();

	sessionData.customer.emailId = $("#emailExt").val();

	if (sessionData.customer.emailId != "") {
		if (!regex.test(sessionData.customer.emailId)) {

			alert("Enter a valid email");

			$("#emailExt").focus();

			return false;

		}

	}

	$.postJSON(httpUrl + "update", sessionData, function(data) {

		sessionData = data;

		document.getElementById('updateProfile').style.display = 'none';

	});

}