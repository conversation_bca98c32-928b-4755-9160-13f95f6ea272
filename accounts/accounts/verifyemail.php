<?php
$tokens=  urlencode($_REQUEST["token"]);

function httpGet($url)
{
    $ch = curl_init();                  
    curl_setopt($ch,CURLOPT_URL,$url);
    curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
//  curl_setopt($ch,CURLOPT_HEADER, false); 
    $output=curl_exec($ch);
    curl_close($ch);
    return $output;
}
 
$veryfyurl=httpGet("http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/authorization/validate?token=$tokens");

if($veryfyurl == "true")
{
header("location:thankyou.php");
}
 else
{
 header("location:sorry.php");    
}

