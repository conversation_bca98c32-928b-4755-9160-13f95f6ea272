<!DOCTYPE HTML>
<html>
<head>
<title> Experiment with Chai : Chaayos </title>
<!-- Custom Theme files -->
<link href="css/style.css" rel="stylesheet" type="text/css" media="all"/>
<!-- Custom Theme files -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<meta name="keywords" content="Get a Free Chai Break for Chaayos" />
<link rel="icon" href="images/icon.jpg">
<script src="js/jquery11.js"> </script>
<script src="js/jquery_functions.js"> </script>
<!--Google Fonts-->
<link href='http://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800' rel='stylesheet' type='text/css'>
<!--Google Fonts-->
</head>
<?php
error_reporting(0);


$token          =    $_REQUEST['token'];           // UNDEFINED
$source         =    $_REQUEST['source'];          // CHAAYOS
$conversionId   =    $_REQUEST['conversionId'];    // CAN BE NULL   
$vserv          =    $_REQUEST['vserv'];           // CAN BE NULL

$iphone = strpos($_SERVER['HTTP_USER_AGENT'],"iPhone");
$android = strpos($_SERVER['HTTP_USER_AGENT'],"Android");
$palmpre = strpos($_SERVER['HTTP_USER_AGENT'],"webOS");
$berry = strpos($_SERVER['HTTP_USER_AGENT'],"BlackBerry");
$ipod = strpos($_SERVER['HTTP_USER_AGENT'],"iPod");


if($token==null || $token=="")
{
  $token        =  "UNDEFINED";
}
if($source == null || $source=="")
{
  $source       =  "CHAAYOS";
}

?>
  
<body background="images/3.png">
<div class="login" id="dvlogin">
    <div align="center">
    <a href="https://geo.itunes.apple.com/in/app/chaayos/id979812544?mt=8" style="display:inline-block;overflow:hidden;background:url(http://linkmaker.itunes.apple.com/images/badges/en-us/badge_appstore-lrg.svg) no-repeat;width:165px;height:40px;">
    </a>
    
    <a href="https://play.google.com/store/apps/details?id=com.snaplion.chaayos&hl=en&utm_source=global_co&utm_medium=prtnr&utm_content=Mar2515&utm_campaign=PartBadge&pcampaignid=MKT-AC-global-none-all-co-pr-py-PartBadges-Oct1515-1">
    <img alt="Get it on Google Play" src="https://play.google.com/intl/en_us/badges/images/apps/en-play-badge.png" width="165px" height="40px"/>
    </a>
    </div>

<?php
if ($iphone || $android || $palmpre || $ipod || $berry == true)
{
?>
    
    <div style="margin-bottom: 10px" align="center"><img src="images/chaiBanner.jpg" width="350" height="130"></img></div>
     
<?php
}
 else {
  ?> 
   
  <div style="margin-bottom: 10px" align="center"><img src="images/chaiBanner.jpg" width="800" height="280"></img></div>  
    
<?php
 }
?> 
    
<div id="dvRegistraion">
<div style="color: #fff; font-weight: 600"> Register your mobile number and get your first chai FREE at any Chaayos Cafe.</div>
 
     <!--  <div style="margin-bottom: 10px" align="center"><img src="images/images1.jpg"></img></div> --->
      <div style="margin-bottom: 10px">
      <input type="text" id="myNumber" name="myNumber" placeholder="Mobile no"  maxlength="10" tabindex="1" /></div>
      <div ><img src="captcha.php" id="captcha"></div>

      <div style="margin-bottom: 10px"><a href="#" onclick="
      document.getElementById('captcha').src='captcha.php?'+Math.random();
      document.getElementById('captcha-form').focus();" id="change-image" style="color:#fff; font-weight: 600"><b>Not readable? Change text.</b></a></div>

   <input type="text" name="captcha" id="captcha-form" autocomplete="off" tabindex="2" />
   <input type="hidden" name="ip-add" id="ip-add" value="<?php echo $_SERVER['REMOTE_ADDR']?>" />
   <input type="hidden" name="tokens" id="tokens" value="<?php echo $token?>" />
   <input type="hidden" name="sources" id="sources" value="<?php echo $source?>" />
   <input type="hidden" name="conversionId" id="conversionId" value="<?php echo $conversionId?>"/>
   <input type="hidden" name="vserv" id="vserv" value="<?php echo $vserv?>" />
       
   <br><br>

  <div id="campMsgDV" style="color: red; font-size: 10px; margin-left: 10px; margin-top: 10px; display: none"> &nbsp; </div>
  <div id="btns"> <a class="acc" onclick="return addInfo()" style="cursor:pointer" tabindex="3">Register / Login</a></div>
  <div id="loaderID" align="center" style="display: none"><img src="images/loading.gif" height="40" width="40"> </div>
  
</div>
    
<div class="login" id="dvOTP" style="display: none">
     
     <div style="color: #fff;"><b> OTP has been successfully sent to your mobile </b></div>
     <div style="margin-bottom: 20px; font-size: 20px">
   
     <input type="text" id="otp" name="otp" placeholder="OTP"  maxlength="4"/>
     </div>
     <div id="DVSOTPResponse" style="color:red; font-size: 15px; display:none"></div><br>
     
     <div id="veriFybtn"> <a class="acc" onclick="return sendOTP()" style="cursor:pointer">Verify</a>&nbsp;</div>
     <div id="verifyLoaderID" align="center" style="display: none"><img src="images/loading.gif" height="40" width="40"> </div>
</div>
    
    <div class="login" id="dvReg" style="display:none">
  
        <div> <p style="font-weight: 600; font-size: 20px"> Welcome </p></div>
       
        <div id="customerDiv" >
        <div>  <input type="text" id="nameExt" name="nameExt" placeholder="Name" class="user active" /></div>
        <div>  <input type="text" id="emailExt" name="emailExt" placeholder="Email" class="user active" /></div>
        <div>  <p style="font-weight: 600;font-size: 30px">Loyalty Points :  <label id="loyaltyPoints" name="loyaltyPoints" /></p></div> <br><br>
         <div>
            <span id="updateProfile"> <a class="acc" onclick="return updateCustomerInfo()" style="cursor:pointer">Update </a> </span>
            <span style="margin-left: 20px"><a class="acc" href="index.php?token=<?php echo $token?>&source=<?php echo $source?>" style="cursor:pointer" tabindex="3">Home</a></span></div>
        
      
        
    </div>

</div>
  <a href="https://geo.itunes.apple.com/in/app/chaayos/id979812544?mt=8" style="display:inline-block;overflow:hidden;background:url
(http://linkmaker.itunes.apple.com/images/badges/en-us/badge_appstore-lrg.svg) no-repeat;width:165px;height:40px;"></a>
</div>

    
 <script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-69106493-1', 'auto');
  ga('send', 'pageview');

</script>
</body>
</html>
