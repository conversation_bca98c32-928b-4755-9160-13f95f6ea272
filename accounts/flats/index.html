
<!DOCTYPE HTML>
<html>
<head>
<title>Chai Break for Chaayos</title>
<!-- Custom Theme files -->
<link href="css/style.css" rel="stylesheet" type="text/css" media="all"/>
<!-- Custom Theme files -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<meta name="keywords" content="Get a Free Chai Break for Chaayos" />
<script src="js/jquery11.js"> </script>
<script src="js/jquery_functions.js"> </script>
<!--Google Fonts-->
<link href='http://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800' rel='stylesheet' type='text/css'>
<!--Google Fonts-->
</head>


<body background="/images/Hoarding_3x4.jpg">
    <div style="margin-top: 20px; margin-bottom: 20px" align="center"><img src="images/logo.jpg"></img></div>
<div class="login">
		<h2>Gift a #Chai Break</h2>
		
		
		
		<span class="line"> </span>
		<p>Have a friend who loves chai as much as you do? </p>
                <p>Gift a #ChaiBreak to any 3 of your friends and let them sit back and enjoy a Chaayos Chai Kettle with 2 vada pavs for free.</p>
                
                <p style="font-weight: 600">Mera Naam</p>
                <input type="text" id="myName" name="myName" placeholder="My Name" class="user active" value="" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
		<p style="font-weight: 600">Mera Number</p>
                <input type="text" id="myNumber" name="myNumber" placeholder="My Phone" class="phone" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                
                <p style="font-weight: 600">Pehla Dost</p>
                <input type="text" id="firstRefName" name="firstRefName" placeholder="Name" class="user active" value="" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
		<input type="text" id="firstRefPhone" name="firstRefPhone" placeholder="Phone" class="phone" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                <input type="text" id="firstRefEmail" name="firstRefEmail" placeholder="Email" class="email" value=""/ onfocus="this.value = '';" onblur="if (this.value == '') {this.value = 'Email';}"/>
		
                
                <p style="font-weight: 600">Dosra Dost</p>
                <input type="text"  id="secondRefName" name="secondRefName" placeholder="Name" class="user active" value="" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
		<input type="text" id="secondRefPhone" name="secondRefPhone" placeholder="Phone" class="phone" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                <input type="text" id="secondRefEmail" name="secondRefEmail" placeholder="Email" class="email" value="Email.."/ onfocus="this.value = '';" onblur="if (this.value == '') {this.value = 'Email';}"/> 
                <p style="font-weight: 600">Teesra Dost</p>
                <input type="text" id="thirdRefName" name="thirdRefName" placeholder="Name" class="user active" value="" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                <input type="text" id="thirdRefPhone" name="thirdRefPhone" placeholder="Phone" class="phone" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                <input type="text" id="thirdRefEmail" name="thirdRefEmail" placeholder="Email" class="email" value=""/ onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/><br><br>
                <a class="acc"  onclick="return addInfo()" style="cursor:pointer">Submit<span class="arrow"> </span></a><span id="campMsgDV" style="color: #50773e; font-size: 11px"> &nbsp;</span>
</div>	

</body>
</html>