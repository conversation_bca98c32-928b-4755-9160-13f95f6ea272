function addInfo()
{
   
var myName                      =	$("#myName").val();
var myPhone                     =	$("#myNumber").val();    
    
var firstRefName		=	$("#firstRefName").val();
var firstRefPhone		=	$("#firstRefPhone").val();

var firstRefEmail		=	$("#firstRefEmail").val();

var secondRefName		=	$("#secondRefName").val();
var secondRefPhone		=	$("#secondRefPhone").val();

var secondRefEmail		=	$("#secondRefEmail").val();

var thirdRefName		=	$("#thirdRefName").val();
var thirdRefPhone		=	$("#thirdRefPhone").val();

var thirdRefEmail		=	$("#thirdRefEmail").val();


if(myName == "")
	{
		alert ("Please Enter My Name");
		$("#myName").focus();
		return false;
	}	

if(myPhone== "")
	{
		alert ("Please Enter My Phone");
		$("#myPhone").focus();
		return false;
	}

  var regex = /^([0-9a-zA-Z]([-_\\.]*[0-9a-zA-Z]+)*)@([0-9a-zA-Z]([-_\\.]*[0-9a-zA-Z]+)*)[\\.]([a-zA-Z]{2,9})$/;
if(firstRefName)
{
    if(firstRefPhone=="")
    {
            alert ("Please Enter Pehla Dost Mobile No");
            $("#firstRefPhone").focus();
            return false;
    }

if(isNaN($("#firstRefPhone").val()))
   {
		alert("Please Enter The Correct Mobile No.");
		$("#firstRefPhone").focus();
		return false;
   }
if(firstRefEmail)
{
     if(!regex.test(firstRefEmail)){
       alert("Please Enter Pehla Dost Valid Email");
       $("#firstRefEmail").focus();
       return false;
     }
} 
  }
    
    
if(secondRefName)
{
 if(secondRefPhone)
	{
		alert ("Please Enter Dosra Dost Mobile No");
		$("#secondRefPhone").focus();
		return false;
	}

 if(secondRefEmail)
{
            if(!regex.test(secondRefEmail)){
            $("#secondRefEmail").focus();        
            alert("Please Enter Dosra Dost valid email");
                return false;
    }
     }
 }
     
 if(thirdRefName)
{
		
if(thirdRefPhone)
	{
		alert ("Please Enter third Dost Mobile No");
		$("#thirdRefPhone").focus();
		return false;
	}

if(isNaN($("#thirdRefPhone").val()))
   {
		alert("Please insert the correct third Dost mobile no.");
		$("#thirdRefPhone").focus();
		return false;
    }
    
    
  if(thirdRefEmail)
  {   if(!regex.test(thirdRefEmail)){
                alert("Please Enter Third Dost Valid Email");
                $("#thirdRefEmail").focus();
                return false;
     }    
     
     
 }     } 
     

var ajaxaction			=	"addInfoAjax";

$.post("refAjax.php", {ajaxaction:ajaxaction,myName:myName,myPhone:myPhone,firstRefName:firstRefName,firstRefPhone:firstRefPhone,firstRefEmail:firstRefEmail,secondRefName:secondRefName,secondRefPhone:secondRefPhone,secondRefEmail:secondRefEmail,thirdRefName:thirdRefName,thirdRefPhone:thirdRefPhone,thirdRefEmail:thirdRefEmail},
function(data){

$("#campMsgDV").html(data);

});
return false;	
}


