<?php
include("database-class.php");

class myConfig extends database
{
	public	$websiteID						=	"1";   	// enterthedraw
	public	$headerChange					=	"No"; 	// do you require header change for all gifts
	public	$headerSplits					=	"1"; 	// number of splits in header
	
	private $configVarsPrize				=	array();
	private $configSelectedPrizeVars		=	array();
	// ------------- returns the website details -----------------------------------------------------
	
	function loginUser($user,$pass)
		{
			$this -> tableName 			=	"mmiusers";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"empCode = '$user' and password ='$pass'";
			$result						=	$this -> select("notFree");	
			//$result 					=	 mysql_fetch_array($query);
			return $result;
		}	
		
	function userDetails($empCode)
		{
			$this -> tableName 			=	"mmiusers";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"empCode='$empCode'";
			$result						=	$this -> select("notFree");	
			//$result 					=	 mysql_fetch_array($query);
			return $result;
		}	
		
		function getUserDetails($userName,$password)
		{
			$this -> tableName 			=	"mmiusers";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"empCode=$userName and password=$password";
			$query						=	$this -> select("notFree");	
			return $query;
		}	
		
		function getBLDetails($empID)
		{
			$this -> tableName 			=	"mt_customer";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"";
			$query						=	$this -> select("notFree");	
			return $query;
		}
		
		
		
		
		function viewProductionDetails($empID)
		{
			$this -> tableName 			=	"mt_customer";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"bl_approval='Accepted' and fin_approval='Accepted'";
			$query						=	$this -> select("notFree");	
			return $query;
		}
		
		
		
		
		function getCustomerDetails($empID)
		{
			$this -> tableName 			=	"mt_customer";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"empCode=$empID";
			$query						=	$this -> select("notFree");	
			return $query;
		}
		
		
		
		function findCustomerDetails($cid)
		{
			$this -> tableName 			=	"mt_customer";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"cust_id=$cid";
			$query						=	$this -> select("notFree");	
			return $query;
		}
		
		
		function findCustHardwareDetails($cid)
		{
			$this -> tableName 			=	"mt_order_hardware";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"hard_order_id=$cid";
			$query						=	$this -> select("notFree");	
			return $query;
	}
		
	function findCustSubscriptionDetails($cid)
		{
			$this -> tableName 			=	"mt_order_subscription";
			$this -> selectFields		=	"";
			$this -> limit 				=	"";
			$this -> condition			=	"subs_order_id=$cid";
			$query						=	$this -> select("notFree");	
			return $query;
		}	
		
	}
?>