<?php ob_start();
//session_start();
//include_once("connection.php");
PUTENV("TZ=GMT");
/* Active A
Inactive N
Denied/Deleted D
Blocked B

*/

class config  // class for admin login validation 
{
	public $phpAlert	=	array();
	
	function __construct()
	{
		parent::__construct();
		
		$this->phpAlert['agentIsInsideAdmin']	=	"You are not authorised to Enter";		// Authorization Failed ( Agent is Inside )		
		//$phpAlert['101']				=	"101";									// 101		
		
	}	
	
	function updateOperation($tableName, $primaryKey, $status, $comment,$userType,$userID)
		{
		
		 
		 /*
			$userType=$_SESSION['userType'];
			if($userType=='U') {
			$userID=$_SESSION['agentID'];
			
			}
			
			else if($userType=='S' || $userType=='A') {
			$userID=$_SESSION['userID'];
			
			}
			*/
			$this -> tableName 						= 	"aa_harg_operations";
			$this -> fieldValues[tableName]		=	$tableName;
			$this -> fieldValues[primaryKey]		=	$primaryKey;
			$this -> fieldValues[status]			=	$status;
			$this -> fieldValues[comments]			=	$comment;
			$this -> fieldValues[addDateTime]			=	$this->getCurrentDateTime();
			$this -> fieldValues[addIpAddress]		=	$_SERVER['REMOTE_ADDR'];
			$this -> fieldValues[browser]			=	$_SERVER['HTTP_USER_AGENT'];
			$this -> fieldValues[userID]			=	$userID;
			$this -> fieldValues[userType]			=	$userType;
			$res 									= 	$this -> insert('notFree');
			//echo $this->query;
			//exit();
		}

	
function getCurrentDateTime()
	{
		//$time_now=gmmktime(date('h'),date('i'),date('s'));
		$current_time = date('Y-m-d H:i:s');
		return $current_time;
	}
	
function getCurrentDate()
	{		
		$current_date = date('Y-m-d');
		return $current_date;
	}


function getCurrentYearMonth()
	{		
		$current_date = date('Y-m');
		return $current_date;
	}


function getCurrentDateTimeZoneOneHourLess()
	{
		$time_now=gmmktime(date('H')-1,date('i'),date('s'));
		$current_time = gmdate('Y-m-d H:i:s',$time_now);
		return $current_time;
	}
	

function getCurrentDateTimeZone()
	{
		$time_now=gmmktime(date('h'),date('i'),date('s'));
		$current_time = gmdate('Y-m-d H:i:s',$time_now);
		return $current_time;
	}
	


	
	
function getMyPermissions()
{
	if($_SESSION['pubID']=='')
	{
	header("location:fivelead.html");
	exit();
	}
}


function replaceBlankValue($value)
	{
		if($value == "")
			$value	=	"&nbsp;";
		
		return $value;
				
	}	
	
	function ipCountry($ip)
{

    $ip					=		explode(".",$ip);
	
	
	$ip_new				=		$ip[0]*256*256*256	+$ip[1]*256*256 + $ip[2]*256  +  $ip[3];
	
	
	$this->tableName		=		"aa_harg_country_ip_new ";	
	$this -> selectFields 		= "";	
	$this->condition		=		" $ip_new >= IpFrom AND $ip_new <= IpTo";
	$res				=		$this->select('notFree');
	$lineip=mysql_fetch_array($res);
	
	return $lineip['Country'];


}
function showStatus($status,$type='')
{
	if($status=='A')
	 return 'Approved';
		 
	elseif($status=='N')
	 return 'Inactive';
	 
	elseif($status=='P')
	 return 'Pending';

	elseif($status=='D')
	 return 'Denied';
	
	elseif($status=='B')
	 return 'Blocked';  
	else
	return ""; 
}
function array_remove($arr,$value) {

   return array_values(array_diff($arr,array($value)));

}


}
?>