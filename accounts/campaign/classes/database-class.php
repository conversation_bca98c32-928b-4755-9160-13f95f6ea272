<?php 
include_once("config-class.php");

class database extends config
{
		public 	$tableName		=	"";	
		
		public 	$fieldValues			=	array();	// normal values
		public 	$fieldValuesNotQuotes	=	array();	// values that do not require quotes around them
		public 	$fieldValuesSpecial		=	array();	// values that require addslashes or striptags
		
		public	$selectFields	=	"";
		public 	$query 			= 	"";
		public 	$condition		=	"";
		
		private $host 			= 	"localhost";
		private $user 			= 	"";
		private $password 		= 	"";
		private $database 		= 	"";
		private $conn			=	"";


	function __construct()
		{
			//$this -> conn 	= mysql_connect( $this -> host , $this -> user , $this -> password );
			//mysql_select_db($this -> database,$this -> conn) or die(mysql_error());	
		}
		

		/*###########################################################################################
							Start Insert Part
		*/###########################################################################################

		function insert($freeFlag='')
		{
			$arrStr =	$this -> createStringInsertUpdate("Insert");
			
			$tableName	=	$this->tableName;
			
			$this -> query = "insert into ".$tableName." ".$arrStr['fields']." values ".$arrStr['values'];
			
			$q	= mysql_query($this -> query);
			
			
			if($freeFlag != "notFree")
				$this -> setObjectValuesFree();
				
			return $q;
		}

/*#######################################################################################################################
			Start Update Part		
*/#######################################################################################################################

		function update($freeFlag='')
		{
			$str 	= "";
			
			$str =	$this -> createStringInsertUpdate("Update");	
			
			$tableName	=	$this->tableName;
			
			$this -> query = "update ".$tableName." "." set ".$str." where ".$this->condition;
			
			$result_up 	= mysql_query($this -> query);
			
			if($freeFlag != "notFree")
				$this -> setObjectValuesFree();
			
			return $result_up;
		}

/*#########################################################################################################################
			Start Delete Part		
*/#########################################################################################################################
	
	function delete($freeFlag='')
	{
		$tableName	=	$this->tableName;
		
		$str 	=	"delete from " . $tableName . " where " . $this->condition;
		
		if($this -> limit != "")
			$str = $str." limit ".$this->limit;	
		
		$result		=	mysql_query($str);
		
		if($freeFlag != "notFree")
			$this -> setObjectValuesFree();
				
		
		return $result;
	}

	function delete_all($freeFlag='')
	{
		$tableName	=	$this->tableName;
		
		$str = "delete from ".$tableName;
		
		$result = mysql_query($str);
		
		if($freeFlag != "notFree")
			$this -> setObjectValuesFree();
		
		return $result;
	}

	function select($freeFlag='')
	{
		$tableName	=	$this->tableName;
		
		$str = "select *  from ".$tableName;
		
		if($this -> selectFields != "")
	 	$str	=	"select ".$this->selectFields." from ".$tableName;
		
		if($this->condition != "")
			$str = $str." where ".$this->condition;

		if($this->limit != "")
			$str = $str."  limit ".$this->limit;	

		 $this-> query = $str;

		$query = mysql_query($str);
		
		if($freeFlag != "notFree")
			$this -> setObjectValuesFree();
					
		 return $query;
	}
	
		
	function postDataConvert($data, $filter="")
	{
		//$data = htmlentities($data);
		$data = strip_tags($data);		
		//$data = addslashes($data);
		return $data;
	}
	
	
	function createStringInsertUpdate($operationType)
		{
			$strFields = "(";
			$strValues = "("; 
			$i = 0; 				
				
			if(count($this->fieldValues) > 0)
			{
				foreach($this->fieldValues as $key => $val)
				{
					if($i >= 1)
						$addComma	=	",";
					else
						$addComma	=	"";	
					
					if($operationType == "Insert")
					{
						$strFields .= $addComma."`".$key."`";
						$strValues .= $addComma."'".$val."'";
					}
					else	// update
					{
						$str .= $addComma.$key." = '".$val."'";
					}
									
					$i++;
				}  // end foreach 
			}
		
			
			// Wtihout quotes
			
			if(count($this->fieldValuesNotQuotes)>0)
			{
			
			foreach($this->fieldValuesNotQuotes as $key => $val)
				{
					if($i >= 1)
						$addComma	=	",";
					else
						$addComma	=	"";	
						
					
					if($operationType == "Insert")
					{
						$strFields .= $addComma."`".$key."`";
						$strValues .= $addComma.$val;
					}
					else
					{
						$str .= $addComma.$key." = ".$val;
					}	
					
				$i++;
				}  	
			 }	
			 
			 
			 // Special
			
			if(count($this->fieldValuesSpecial)>0)
			{
			foreach($this->fieldValuesSpecial as $key => $val)
				{
					if($i >= 1)
						$addComma	=	",";
					else
						$addComma	=	"";	
					
					if($operationType == "Insert")
					{
						$strFields .= $addComma."`".$key."`";
						$strValues .= $addComma."'".$this -> postDataConvert($val)."'";
					}
					else
					{
						$str .= $addComma."`".$key."` = '".$this -> postDataConvert($val)."'";
					}
						
				$i++;
				}  	
			 }	
		
		if($operationType == "Insert")
			{
					$strFields = $strFields.")";
					$strValues = $strValues.")";
					
					$arrStr['fields']	=	$strFields;
					$arrStr['values']	=	$strValues;
					
					return $arrStr;	
			}
		else
			{
					return $str;
			}	
		
		}
	
	
	function mySqlError($type = '')		// use $type= die for die operation
	{
		$arrRet['errorDesc']	=	mysql_error();
		
		if(mysql_errno($this->conn)) 
			{
				$arrRet['errorFlag']	=	true;
				echo $arrRet['errorDesc'];
				die("ERROR HERE");
			}	
		else 
			$arrRet['errorFlag']	=	false;
	
		return $arrRet;
	}
	
	
	function lastInsertID()
	{
		global $conn;
		return mysql_insert_id();
	}
	
	function affectedRows()
	{
		return mysql_affected_rows();
	}
			
	function closeConnection($conn) // close conection
	{
		mysql_close($conn);
	} 
	
	
	
	
	
	function setObjectValuesFree()
		{
			$this -> tableName		=	"";	
		
			$this -> fieldValues			=	array();	// normal values
			$this -> fieldValuesNotQuotes	=	array();	// values that do not require quotes around them
			$this -> fieldValuesSpecial		=	array();	// values that require addslashes or striptags
		
			$this -> selectFields	=	"";
			$this -> query 			= 	"";
			$this -> condition		=	"";
		}	
	
}


/*$obj = new database;

$obj -> setObjectValuesFree();

$obj -> tableName						=	"";
$obj -> fieldValuesSpecial['fname']		=	"<br> rohti<br><br>rohit";
$obj -> fieldValuesNotQuotes['amount']	=	'`amount`+1';
$obj -> fieldValues['id']				=	"2";
$obj -> fieldValues['name']				=	"rohit";

$obj -> condition						=	"id=9";
$obj -> update();


echo "<br> HELLO=".$obj -> mySqlError("die");

echo "<br> HELLO=".$obj -> LastInsertID();

echo "<br> HELLO=".$obj -> affectedRows();

	
*/
?>