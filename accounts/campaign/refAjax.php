<?php
session_start();
include("classes/connection.php");
include("classes/mywebsite-config-class.php");
$objmyConfig 	= new myConfig;

if($_REQUEST['ajaxaction']=="addInfoAjax")
{
   // referMobile,refererEmail
  // $sqlCheck="select * from customer where referMobile and  refererEmail or " ". "

    $sqlCheck1       =   "SELECT * FROM  CAMPAIGN_REFERER where REFERER_PHONE='".trim($_REQUEST['myPhone'])."' AND HAS_REFERED='N'";
    $sqlCheckQuery1  =   mysql_query($sqlCheck1);
    $countfirst      =   mysql_num_rows($sqlCheckQuery1);
    
    
    
    $sqlCheckStatus         =   "SELECT * FROM  CAMPAIGN_REFERER where REFERER_PHONE='".trim($_REQUEST['myPhone'])."' AND HAS_REFERED='Y'";
    $sqlCheckQueryStatus    =   mysql_query($sqlCheckStatus);
    $countOnlyCheck         =   mysql_num_rows($sqlCheckQueryStatus);
  
    
    
    
 if(trim($_REQUEST['firstRefName'])==""  && trim($_REQUEST['secondRefName'])=="" && trim($_REQUEST['thirdRefName'])=="" )
 {
   echo "&nbsp;No Action, Please Add any atleast One Dost Details";
   exit();  
     
 }
    
    if($countfirst==1)                                                                  // check availbale phone
    {
    
        if(trim($_REQUEST['firstRefName']))
        {
        $objmyConfig 	-> 	tableName 				=	"CAMPAIGN_DETAILS";
	$objmyConfig	->	fieldValues['REFERER_NAME']		=	trim($_REQUEST['firstRefName']);
	$objmyConfig	->	fieldValues['REFERER_PHONE']		=	trim($_REQUEST['firstRefPhone']);
	
	
	$objmyConfig	->	fieldValues['REFERRED_BY_NAME']		=	trim($_REQUEST['myName']);
	$objmyConfig	->	fieldValues['REFERRED_BY_PHONE']	=	trim($_REQUEST['myPhone']);
	$objmyConfig	->	fieldValues['ADD_TIME']                 =	$objmyConfig->getCurrentDateTime();
	$objmyConfig	->	fieldValues['REFERRAL_CODE']		=	"CHAI-BREAK";
        $objmyConfig	->	fieldValues['REFERRAL_STATUS']		=	"Y";
        $result         						= 	$objmyConfig-> insert("notFree");
        $aff=  mysql_affected_rows();
 
	 $res                                                            =	$objmyConfig->query;
        }
        
        if(trim($_REQUEST['secondRefName']))
        {
        $objmyConfig 	-> 	tableName 				=	"CAMPAIGN_DETAILS";
        $objmyConfig	->	fieldValues['REFERER_NAME']		=	trim($_REQUEST['secondRefName']);
	$objmyConfig	->	fieldValues['REFERER_PHONE']		=	trim($_REQUEST['secondRefPhone']);
	

	$objmyConfig	->	fieldValues['REFERRED_BY_NAME']		=	trim($_REQUEST['myName']);
	$objmyConfig	->	fieldValues['REFERRED_BY_PHONE']	=	trim($_REQUEST['myPhone']);
	$objmyConfig	->	fieldValues['ADD_TIME']                 =	$objmyConfig->getCurrentDateTime();
	$objmyConfig	->	fieldValues['REFERRAL_STATUS']		=	"Y";
        $result1         						= 	$objmyConfig-> insert("notFree");
        $aff=  mysql_affected_rows();      
// $res                                                            =	$objmyConfig->query;
        }
        
        if(trim($_REQUEST['secondRefName']))
        {
        $objmyConfig 	-> 	tableName 				=	"CAMPAIGN_DETAILS";
        $objmyConfig	->	fieldValues['REFERER_NAME']		=	trim($_REQUEST['thirdRefName']);
	$objmyConfig	->	fieldValues['REFERER_PHONE']		=	trim($_REQUEST['thirdRefPhone']);
		
	$objmyConfig	->	fieldValues['REFERRED_BY_NAME']		=	trim($_REQUEST['myName']);
	$objmyConfig	->	fieldValues['REFERRED_BY_PHONE']	=	trim($_REQUEST['myPhone']);
	$objmyConfig	->	fieldValues['ADD_TIME']                 =	$objmyConfig->getCurrentDateTime();
	$objmyConfig	->	fieldValues['REFERRAL_STATUS']		=	"Y";
        $result2         						= 	$objmyConfig-> insert("notFree");
        $aff=  mysql_affected_rows();
        }
       
        $updateCheck             =   "update CAMPAIGN_REFERER set HAS_REFERED='Y' where REFERER_PHONE='".trim($_REQUEST['myPhone'])."'";
        $updateRes              =   mysql_query($updateCheck ); 
        
        
        if($aff)
        {
            echo "&nbsp;Successfully Added";
            
        }
        else
        {
            echo "&nbsp;Records not Added";
        }
        
    }  
    
    else if($countOnlyCheck==1)
    {
        
       echo "&nbsp; This offer is valid for one time use only"; 
    }
    
 else {
     
    echo "&nbsp; This Offers is Applicablefor a selected few ";
        
    }
       
        
    }
    
    else if($_REQUEST['ajaxaction']=="checkPehlaDostNo")
    {
     
        if($_REQUEST['firstRefPhone'])
    {
    $sqlPehlaDost            =   "SELECT * FROM CAMPAIGN_EXIST_DATA WHERE CUSTOMER_PHONE='".$_REQUEST['firstRefPhone']."'";
    $sqlPehlaDostQuery       =   mysql_query($sqlPehlaDost);
    $cntPehlaDost            =   mysql_num_rows($sqlPehlaDostQuery); 
    if($cntPehlaDost==1)
    {
        echo "This number belongs to an existing Chaayos customer";
    }
 else {
        echo "Available";
     }
    }
    }
    
     else if($_REQUEST['ajaxaction']=="checkDoosraDostNo")
    {
    if($_REQUEST['secondRefPhone'])
    {
        $sqlDoosraDost        =   "SELECT * FROM CAMPAIGN_EXIST_DATA WHERE CUSTOMER_PHONE='".$_REQUEST['secondRefPhone']."'";
    
    $sqlDoosraDostQuery     =   mysql_query($sqlDoosraDost);
    $cntDoosraDost            =   mysql_num_rows($sqlDoosraDostQuery); 
    if($cntDoosraDost==0)
    {
        echo "Available";
    }
    else
    {
        echo "This number belongs to an existing Chaayos customer";
    }
    }  
    }
    
    else if($_REQUEST['ajaxaction']=="checkTeesraDostNo")
    {
    
     if($_REQUEST['thirdRefPhone'])
    {   
        $sqlTeesraDost        =   "SELECT * FROM CAMPAIGN_EXIST_DATA WHERE CUSTOMER_PHONE='".$_REQUEST['thirdRefPhone']."'";
    $sqlTeesraDostQuery   =   mysql_query($sqlTeesraDost);
    $cntTeesraDost        =   mysql_num_rows($sqlTeesraDostQuery); 
    if($cntTeesraDost==0)
    {
        echo "Available";
    }
    else
    {
        echo "This number belongs to an existing Chaayos customer";
    }
    }
    }
    
    
    
 else {
        echo "No Data Available";
}