<!DOCTYPE HTML>
<html>
<head>
<title>Chai Break for Chaayos</title>
<!-- Custom Theme files -->
<link href="css/style.css" rel="stylesheet" type="text/css" media="all"/>
<!-- Custom Theme files -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<meta name="keywords" content="Get a Free Chai Break for Chaayos" />
<script src="js/jquery11.js"> </script>
<script src="js/jquery_functions.js"> </script>
<!--Google Fonts-->
<link href='http://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800' rel='stylesheet' type='text/css'>
<!--Google Fonts-->
</head>


<body>
    <div style="margin-top: 20px; margin-bottom: 20px" align="center"><img src="images/logo.jpg"></img></div>
<div class="login">
		<h2>Gift a #Chai Break</h2>
		
		
		
		<span class="line"> </span>
		<p>Have a friend who loves chai as much as you do, and hasn't tried Chaayos Chalti Phirti Chai yet? </p>
                <p>Gift a #ChaiBreak to any 3 of your friends and let them sit back and enjoy a Chaayos Chai Kettle with 2 vada pavs for free.</p>
                
                <p style="font-weight: 600">Mera Naam</p>
                <input type="text" id="myName" name="myName" placeholder="My Name" class="user active" value="" />
		<p style="font-weight: 600">Mera Number</p>
                
                <input type="text" id="myNumber" name="myNumber" placeholder="Mobile No must not start with +91,0" class="phone" maxlength="10" />
                
                <p style="font-weight: 600">Pehla Dost</p>
                <input type="text" id="firstRefName" name="firstRefName" placeholder="Name" class="user active" value="" />
                
                <input type="text" id="firstRefPhone" name="firstRefPhone" placeholder="Mobile No must not start with +91,0" class="phone" value="" maxlength="10" onblur="checkPehlaNo()" />
                <div id="pehlaDostDVR" style="font-size: 10px; color: red; display: none"></div>
                 <div id="pehlaDostDVG" style="font-size: 10px; color: green;display: none"></div>
		
                
                <p style="font-weight: 600">Dosra Dost</p>
                <input type="text"  id="secondRefName" name="secondRefName" placeholder="Name" class="user active" value="" />
		
                <input type="text" id="secondRefPhone" name="secondRefPhone" placeholder="Mobile No must not start with +91,0" class="phone"  maxlength="10" onblur="checkDoosraNo()" />
                <div id="doosraDostDVR" style="font-size: 10px; color: red; display: none"></div>
                 <div id="doosraDostDVG" style="font-size: 10px; color: green;display: none"></div>
                
                <p style="font-weight: 600">Teesra Dost</p>
                <input type="text" id="thirdRefName" name="thirdRefName" placeholder="Name" class="user active" value="" />
             
                <input type="text" id="thirdRefPhone" name="thirdRefPhone" placeholder="Mobile No must not start with +91,0" class="phone" maxlength="10" onblur="checkTeesraNo()" />
                
                <div id="teesraDostDVR" style="font-size: 10px; color: red; display: none"></div>
                 <div id="teesraDostDVG" style="font-size: 10px; color: green;display: none"></div>
                <br><br>
                <div id="btn"> <a class="acc" onclick="return addInfo()" style="cursor:pointer">Submit</a><span id="campMsgDV" style="color: #50773e; font-size: 11px"> &nbsp;</span></div>
</div>	

</body>
</html>