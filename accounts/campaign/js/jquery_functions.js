function addInfo()
{
   
var myName                      =	$("#myName").val();
var myPhone                     =	$("#myNumber").val();    
    
var firstRefName		=	$("#firstRefName").val();
var firstRefPhone		=	$("#firstRefPhone").val();



var secondRefName		=	$("#secondRefName").val();
var secondRefPhone		=	$("#secondRefPhone").val();



var thirdRefName		=	$("#thirdRefName").val();
var thirdRefPhone		=	$("#thirdRefPhone").val();




if(myName == "")
	{
		alert ("Please Enter My Name");
		$("#myName").focus();
		return false;
	}	

if(myPhone== "")
	{
		alert ("Please Enter My Phone");
		$("#myNumber").focus();
		return false;
	}

 

if (!(myPhone.length == 10)) {
  alert("\nPlease enter 10 digit mobile no.");
   $("#myNumber").focus();
   return false;
}
 
 
if(firstRefName)
{
    if(firstRefPhone=="")
    {
            alert ("Please Enter Pehla Dost Mobile No");
            $("#firstRefPhone").focus();
            return false;
    }

if(isNaN($("#firstRefPhone").val()))
   {
		alert("Please Enter The Correct Mobile No.");
		$("#firstRefPhone").focus();
		return false;
   }
   
   if (!(firstRefPhone.length == 10)) {
  alert("\nPlease enter 10 digit mobile no.");
   $("#firstRefPhone").focus();
   return false;
}

  }
    
    
if(secondRefName)
{
 if(secondRefPhone=="")
	{
		alert ("Please Enter Dosra Dost Mobile No");
		$("#secondRefPhone").focus();
		return false;
	}

 if(isNaN($("#secondRefPhone").val()))
   {
		alert("Please Enter The Correct Mobile No.");
		$("#secondRefPhone").focus();
		return false;
   }
   
      if (!(secondRefPhone.length == 10)) {
  alert("\nPlease enter 10 digit mobile no.");
   $("#secondRefPhone").focus();
   return false;
}

 }
     
 if(thirdRefName)
{
		
if(thirdRefPhone=="")
	{
		alert ("Please Enter third Dost Mobile No");
		$("#thirdRefPhone").focus();
		return false;
	}

if(isNaN($("#thirdRefPhone").val()))
   {
		alert("Please insert the correct third Dost mobile no.");
		$("#thirdRefPhone").focus();
		return false;
    }
    
    
    if (!(thirdRefPhone.length == 10)) {
  alert("The phone number is the wrong length. \nPlease enter 10 digit mobile no.");
   $("#thirdRefPhone").focus();
   return false;
}
    
     } 
     

var ajaxaction			=	"addInfoAjax";

$.post("refAjax.php", {ajaxaction:ajaxaction,myName:myName,myPhone:myPhone,firstRefName:firstRefName,firstRefPhone:firstRefPhone,secondRefName:secondRefName,secondRefPhone:secondRefPhone,thirdRefName:thirdRefName,thirdRefPhone:thirdRefPhone},
function(data){

$("#campMsgDV").html(data);

});
return false;	
}


function checkPehlaNo()
{
var firstRefPhone	=	$("#firstRefPhone").val();
var ajaxaction		=	"checkPehlaDostNo";    

$.post("refAjax.php", {ajaxaction:ajaxaction,firstRefPhone:firstRefPhone},
function(data){

if(data=="Available")
{
$("#pehlaDostDVG").html(data);
$("#pehlaDostDVG").show();
$("#btn").show();
$("#pehlaDostDVR").hide();

}
else
{
$("#pehlaDostDVR").html(data);
$("#pehlaDostDVG").hide();
$("#btn").show();
$("#pehlaDostDVR").show();
}
});
return false;  
  
    
}


function checkDoosraNo()
{
var secondRefPhone	=	$("#secondRefPhone").val();
   
var ajaxaction		=	"checkDoosraDostNo";    
$.post("refAjax.php", {ajaxaction:ajaxaction,secondRefPhone:secondRefPhone},
function(data){



if(data=="Available")
{
$("#doosraDostDVG").html(data);
$("#doosraDostDVG").show();
$("#btn").show();
$("#doosraDostDVR").hide();

}
else
{
$("#doosraDostDVR").html(data);
$("#doosraDostDVG").hide();
$("#btn").hide();
$("#doosraDostDVR").show();
}
});
return false;  
  



 
  
    
}

function checkTeesraNo()
{
 var thirdRefPhone	=	$("#thirdRefPhone").val();
   
var ajaxaction	=	"checkTeesraDostNo";    
$.post("refAjax.php", {ajaxaction:ajaxaction,thirdRefPhone:thirdRefPhone},
function(data){
$("#teesraDostDV").html(data);

if(data=="Available")
{
$("#teesraDostDVG").html(data);
$("#teesraDostDVG").show();
$("#btn").show();
$("#teesraDostDVR").hide();

}
else
{
$("#teesraDostDVR").html(data);
$("#teesraDostDVG").hide();
$("#btn").hide();
$("#teesraDostDVR").show();
}
});
return false;  
  
    
}