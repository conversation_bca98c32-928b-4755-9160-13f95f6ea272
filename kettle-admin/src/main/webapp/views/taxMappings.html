<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.grid {
	height: 500px;
	padding: 0 !important;
}
</style>
<div
	class="row"
	data-ng-init="init()">
	<div class="col-lg-12">
		<br>
		<h1 class="page-header">State to Tax Mapping</h1>
	</div>
</div>
<div class="row">
	<div class="col-xs-3">Country</div>
	<div class="col-xs-3">
		<select
			ui-select2
			class="form-control"
			style="width: 100% !important"
			data-ng-model="selectCountry"
			data-placeholder="Select a Country"
			data-ng-options="country as country.name for country in countryList track by country.id">
		</select>
	</div>
</div>
<div
	class="row"
	style="margin-top: 20px;">
	<div class="col-xs-3">Tax Category</div>
	<div class="col-xs-9">
		<select
			ui-select2
			class="form-control"
			style="width: 100% !important"
			data-ng-model="selectTaxCategoy"
			data-placeholder="Select a Tax Category"
			data-ng-options="category as category.code + ' - ' + category.name for category in taxCategories | orderBy : 'code' track by category.id"
			data-ng-change="getStateWiseTaxData()">
			<!-- <option value=""></option>
			<option
				data-ng-repeat="category in taxCategories"
				value="{{category.id}}">{{category.name}}</option> -->
		</select>
	</div>
</div>
<div
	class="row"
	style="margin-top: 20px;">
	<div class="col-xs-3">Tax Type</div>
	<div class="col-xs-3">
		<select
			ui-select2
			class="form-control"
			style="width: 100% !important"
			data-ng-model="selectTaxType"
			data-placeholder="Select a Tax Type"
			data-ng-options="type as type.name for type in taxTypes track by type.id"
			data-ng-change="getStateWiseTaxData()">
			<!-- <option value=""></option>
			<option
				data-ng-repeat="type in taxTypes"
				value="{{type.id}}">{{type.name}}</option> -->
		</select>
	</div>
</div>
<div class="row">
	<button
		class="btn btn-primary pull-right"
		style="margin-top: 20px; margin-right: 15px;"
		data-toggle="modal"
		id="getStateWiseTaxDataBtn"
		data-ng-click="getStateWiseTaxData()">
		<i class="fa fa-search fw"></i> Search
	</button>
</div>
<div
	class="row"
	style="margin-top: 20px;"
	id="gridViewId">
	<div
		class="col-lg-12"
		data-ng-if="!hideStateTaxGrid">
		<div
			id="stateTaxGrid"
			data-ui-grid="stateTaxGridOptions"
			ui-grid-edit
			ui-grid-exporter
			class="grid col-lg-12"></div>
	</div>
</div>
<div
	class="row"
	style="margin-top: 20px; margin-bottom: 20px;"
	data-ng-if="!hideStateTaxGrid">
	<button
		class="btn btn-primary pull-right"
		style="margin-top: 20px; margin-right: 15px;"
		data-toggle="modal"
		id="getStateWiseTaxDataBtn"
		data-ng-click="resetStateWiseTaxData()">
		<i class="fa fa-times fw"></i> Reset
	</button>
	<button
		class="btn btn-primary pull-right"
		style="margin-top: 20px; margin-right: 15px;"
		data-toggle="modal"
		id="getStateWiseTaxDataBtn"
		data-ng-click="updateStateWiseTaxData()">
		<i class="fa fa-check fw"></i> Update Taxes
	</button>
</div>
<div
	class="modal fade"
	id="editTaxValueModal"
	tabindex="-1"
	role="dialog"
	aria-labelledby="editTaxValueModal">
	<div
		class="modal-dialog"
		role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button
					type="button"
					class="close"
					data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title">Edit Tax</h4>
			</div>
			<div class="modal-body">
				<form
					name="editStateTaxForm"
					novalidate>
					<div class="form-group">
						<label>New Tax Value</label>
						<input
							type="number"
							max = "100"
							class="form-control"
							data-ng-model="newTaxValue" />
					</div>
					<div> Value can be between 100 and 0 only.</div>
					<div class="form-group clearfix">
						<button
							class="btn btn-primary pull-right"
							data-ng-click="updateTaxValue()">Apply to all</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>