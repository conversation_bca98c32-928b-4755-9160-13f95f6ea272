<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
	<div class="col-lg-12">
         <h1 class="page-header">
             Transferred Settlements
	     </h1>
    </div>
</div>

<div class="fullScreenLoader" data-ng-show="showFullScreenLoader">
	<img src="img/ring.gif">
</div>

<div class="row" data-ng-hide="showPullView">
	<div class="col-xs-4" data-ng-if="searchByUnitId">
		<select class="form-control"
            style="line-height:2.8em; font-size: 18px; margin-right:10px;"
            data-ng-options="outlet as outlet.name for outlet in outletList track by outlet.id"
			data-ng-change="changeUnit(unit)"
			data-ng-model="unit">
        </select>
	</div>
	<div class="col-xs-4" data-ng-if="!searchByUnitId">
		<select class="form-control"
				style="line-height:2.8em; font-size: 18px; margin-right:10px;"
				data-ng-options="mode as mode.name for mode in paymentModes track by mode.id"
				data-ng-change="changeType(paymentMode)"
				data-ng-model="paymentMode">
		</select>
	</div>
	<div class="col-xs-3">
		<div class="datepicker" data-date-format="yyyy-MM-dd hh:mm:ss" data-date-max-limit="{{today}}">
			<input type="text" data-ng-model="startDate" class="form-control" placeholder="yyyy-mm-dd hh:mm:ss" />
		</div>
	</div>
	<div class="col-xs-3">
		<div class="datepicker" data-date-format="yyyy-MM-dd hh:mm:ss" data-date-max-limit="{{today}}">
			<input type="text" data-ng-model="endDate" class="form-control" placeholder="yyyy-mm-dd hh:mm:ss" />
		</div>
	</div>
	<div class="col-xs-2">
		<button data-ng-click="getPullSettlements()" class="btn btn-warning">Get Settlements</button>
	</div>
	<div class="col-xs-3" style="margin-top: 20px" >
		Results per page: <select
			ng-model="batchSize"
			class="form-control"
			data-ng-change="getPullSettlements()"
		>
		<option value="5">5</option>
		<option value="10">10</option>
		<option value="20">20</option>
		<option value="50">50</option>
		<option value="100">100</option>
		<option value="200">200</option>
		<option value="500">500</option>
		<option value="1000">1000</option>
		<option value="2000">2000</option>
	</select>
	</div>
	</div>
</div>

<div class="row" style="margin-top:20px;" data-ng-hide="showPullView">
	<div class="col-xs-12">
		<table class="table table-bordered table-striped" data-ng-if="pullSettlements.length>0">
			<tr>
				<th class="adaptive-width">Settlement Id</th>
				<th class="adaptive-width">Unit</th>
				<th class="adaptive-width">Payment Mode</th>
				<th class="adaptive-width">Settlement Slip Number</th>
				<th class="adaptive-width">Total Amount</th>
				<th class="adaptive-width">Settled Amount</th>
				<th class="adaptive-width">Unsettled Amount</th>
				<th class="adaptive-width">Closing Amount</th>
				<th class="adaptive-width">Creation Time</th>
				<th class="adaptive-width">Settlement Status</th>
				<th class="adaptive-width">Actions</th>
			</tr>
			<tr data-ng-repeat="settlement in pullSettlements">
				<td>{{settlement.id}}</td>
				<td>{{settlement.settlementUnit.name}}</td>
				<td>{{settlement.pullDetails[0].paymentMode.description}}</td>
				<td>{{settlement.settlementServiceProvider}}</td>
				<td>{{settlement.totalAmount}}</td>
				<td>{{settlement.settlementAmount==null?'NA':settlement.settlementAmount}}</td>
				<td>{{settlement.unsettledAmount==null?'NA':settlement.unsettledAmount}}</td>
				<td>{{settlement.closingAmount==null?'NA':settlement.closingAmount}}</td>
				<td>{{settlement.settlementTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
				<td>{{settlement.settlementStatus}}</td>
				<td>
					<button class="btn btn-danger btn-xs" style="padding:5px 6px;margin:3px;border-radius:5px"  data-ng-click="viewPull(settlement)">View Components</button>
					<button class="btn btn-danger btn-xs" style="padding:5px 6px;margin:3px;border-radius:5px" data-ng-click="viewDenominations(settlement.settlementDenominations)">View Denominations</button>
				</td>
			</tr>
		</table>
		<div class="alert alert-info" data-ng-if="pullSettlements.length==0">No settlements found.</div>
	</div>
	<div class="pagination" data-ng-if="pullSettlements.length!=0 && !showPullView" style="margin-left:40%;margin-right:40%">
		<button class="page-item btn btn-primary" ng-if="startPosition>1" ng-click='prevBatch();'>&laquo;</button>
		<p class="page-item btn btn-outline" >PAGE {{startPosition}}</p>
		<button class="page-item btn btn-primary" ng-if="(startPosition*batchSize) <= pullSettlementsTotalCount" ng-click='nextBatch();'>&raquo;</button>
	</div>
</div>
<div class="row" data-ng-show="showPullView">
	<div class="col-xs-12">
		<div class="row" style="margin-top:20px;">
			<div class="col-xs-2">
				<button class="btn btn-warning pull-left" data-ng-click="hidePullView()">Back</button>
			</div>
			<div class="col-xs-10">
				<h2 class="text-center" style="font-family:'typewriter';">Component Details</h2>
			</div>
		</div>
		<div class="row" style="margin-top:20px;">
			<div class="col-xs-12">
				<table class="table table-bordered table-striped" data-ng-if="selectedSettlement.pullDetails.length>0">
					<tr>
						<th>Component Id</th>
						<th>Payment Mode</th>
						<th>Amount</th>
						<th>Business Date</th>
						<th>Status</th>
						<th>Created By</th>
						<th>Witnessed By</th>
						<th>Comment</th>
						<th>Settlement Id</th>
						<th>Actions</th>
					</tr>
					<tr data-ng-repeat="pull in selectedSettlement.pullDetails">
						<td>{{pull.pullPacketId}}</td>
						<td>{{pull.paymentMode.description}}</td>
						<td>{{pull.pullAmount}}</td>
						<td>{{pull.pullDate | date:'yyyy-MM-dd'}}</td>
						<td>{{pull.pullPacketStatus}}</td>
						<td>{{pull.createdByName}}</td>
						<td>{{pull.witnessedBy}}</td>
						<td>{{pull.comment}}</td>
						<td>{{pull.pullSettlementDetail==null?'NA':pull.pullSettlementDetail.id}}</td>
						<td>
							<button class="btn btn-danger btn-xs" style="padding:5px 6px;margin:3px;border-radius:5px" data-ng-if="pull.pullPacketStatus!='INITIATED'" 
							 data-ng-click="viewDenominations(pull.pullDenominations)">View Component Denominations</button>
							<button class="btn btn-danger btn-xs" style="padding:5px 6px;margin:3px;border-radius:5px" 
							 data-ng-click="viewClosure(pull.closurePaymentDetail)">View Closure Details</button>
						</td>
					</tr>
				</table>
				<div class="alert alert-info" data-ng-if="selectedSettlement.pullDetails.length==0">No components found.</div>
			</div>
		</div>
	</div>
</div>


<script type="text/ng-template" id="viewDenominationModal.html">
	<div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">Denomination Details</h3>
    </div>
	<div class="modal-body">
		<div class="row" style="margin-bottom:20px;">
			<div class="col-xs-12">
				<table class="table table-bordered table-striped" data-ng-if="denomEntity.length>0">
					<tr>
						<th>Denomination</th>
						<th>Denomination value</th>
						<th>Bundle Size</th>
						<th>No. Of Packets</th>
						<th>Loose Currency</th>
						<th>Total Amount</th>
					</tr>
					<tr data-ng-repeat="denom in denomEntity | orderBy : 'denominationDetail.displayOrder'">
						<td>{{denom.denominationDetail.denominationText}}</td>
						<td>{{denom.denominationDetail.denominationValue}}</td>
						<td>{{denom.denominationDetail.bundleSize}}</td>
						<td>{{denom.packetCount}}</td>
						<td>{{denom.looseCurrencyCount}}</td>
						<td>{{denom.totalAmount}}</td>
					</tr>
				</table>
				<div class="alert alert-info" data-ng-if="denomEntity.length==0">No denominations available.</div>
				<h3 class="text-right" style="color:green;" data-ng-if="denomEntity.length>0">Total Amount : {{denomTotalAmount}}</h3>
			</div>
		</div>
	</div>
    <div class="modal-footer">
		<div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
  			<button class="btn btn-danger" type="button" ng-click="cancel()">Close</button>
         	<button class="btn btn-default" type="button" ng-click="ok()">OK</button>
		</div>
    </div>
</script>

<script type="text/ng-template" id="viewClosureModal.html">
	<div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">Closure Details</h3>
    </div>
	<div class="modal-body">
		<div class="row" style="margin-bottom:20px;">
			<div class="col-xs-12">
				<table class="table table-bordered table-striped">
					<tr>
						<th>Id</th>
						<th>GMV</th>
						<th>Discount</th>
						<th>Net Sales Amount</th>
						<th>Total Tax</th>
						<th>Round Off</th>
						<th>Total Amount</th>
						<th>No. Of Bills</th>
					</tr>
					<tr>
						<td>{{closureEntity.id}}</td>
						<td>{{closureEntity.gmv}}</td>
						<td>{{closureEntity.discount}}</td>
						<td>{{closureEntity.netSalesAmount}}</td>
						<td>{{closureEntity.tax}}</td>
						<td>{{closureEntity.roundOff}}</td>
						<td>{{closureEntity.totalAmount}}</td>
						<td>{{closureEntity.billCount}}</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
    <div class="modal-footer">
		<div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
  			<button class="btn btn-danger" type="button" ng-click="cancel()">Close</button>
         	<button class="btn btn-default" type="button" ng-click="ok()">OK</button>
		</div>
    </div>
</script>