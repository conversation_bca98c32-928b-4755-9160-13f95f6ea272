<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.grid {
	height: 600px;
	padding: 0 !important;
}
</style>
<div
	class="row"
	data-ng-init="init()">
	<div class="col-lg-12">
		<br>
		<h1 class="page-header">
			Tax Category ( H.S.N.)
			<button
				class="btn btn-primary pull-right"
				data-toggle="modal"
				id="addTaxCategoryDiv"
				data-ng-click="openAddTaxCategoryModal()">
				<i class="fa fa-plus fw"></i> Add Tax Category
			</button>
		</h1>
	</div>
</div>
<div
	class="row"
	id="gridViewId">
	<div
		class="col-lg-12"
		data-ng-if="!hideCategoryGrid">
		<div
			id="categoryGrid"
			data-ui-grid="categoryGridOptions"
			ui-grid-pagination
			ui-grid-resize-columns
			ui-grid-move-columns
			class="grid col-lg-12"></div>
	</div>
</div>
<script
	type="text/ng-template"
	id="statusChangeButton.html">
      <div class="ui-grid-cell-contents">
		<button ng-if="row.entity.status=='IN_ACTIVE'"
			class="btn btn-primary btn-xs" 
			data-ng-click="grid.appScope.changeStatus(row.entity)"
			>
			<span ng-if="row.entity.status=='IN_ACTIVE'">Activate</span>
			<!--<span ng-if="row.entity.status=='ACTIVE'">Deactivate</span>-->
		</button>
      </div>
</script>
<script
	type="text/ng-template"
	id="editButton.html">
      <div class="ui-grid-cell-contents">
		<button 
			class="btn btn-primary btn-xs" 
			data-ng-click="grid.appScope.editData(row.entity)">
			<span>Edit</span>
		</button>
      </div>
</script>
<div
	class="modal fade"
	id="addTaxCategoryModal"
	tabindex="-1"
	role="dialog"
	aria-labelledby="addTaxCategoryModal">
	<div
		class="modal-dialog"
		role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button
					type="button"
					class="close"
					data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4
					class="modal-title"
					id="taxCategoryLabel">Tax Category</h4>
			</div>
			<div class="modal-body">
				<form
					name="addTaxCategoryForm"
					novalidate>
					<div class="form-group">
						<label>HSN Code</label>
						<input
							type="text"
							class="form-control"
							data-ng-model="code" />
					</div>
					<div class="form-group">
						<label>Description</label>
						<input
							type="text"
							class="form-control"
							data-ng-model="description" />
					</div>
					<div class="form-group">
						<label>Internal Description</label>
						<input
							type="text"
							class="form-control"
							data-ng-model="internalDescription" />
					</div>
					<div class="form-group">
						<label>Exempted</label>
						<div class="row">
							<div class="col-sm-2"></div>
							<div class="col-sm-4">
								<label class="btn btn-default">
									<input
										type="radio"
										data-ng-model="exempted"
										value="true"> Yes
								</label>
							</div>
							<div class="col-sm-4">
								<label class="btn btn-default">
									<input
										type="radio"
										data-ng-model="exempted"
										value="false"> No
								</label>
							</div>
							<div class="col-sm-2"></div>
						</div>
					</div>
					<div class="form-group clearfix">
						<button
							class="btn btn-primary pull-right"
							data-ng-click="addTaxCategory()">Add</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>