
<div class="row" data-ng-init="init()">
    <h1 class="page-header">Budget Manager: Regenerate PNL for Unit</h1>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Regenerate PNL</div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">
                <label>Start Date</label>
                <div class="datepicker" date-format="yyyy-MM-dd">
                    <input class="form-control" ng-model="startDate"
                           placeholder="click here" type="text" required/>
                </div>
            </div>
            <div class="col-xs-6">
                <label>End Date</label>
                <div class="datepicker" date-format="yyyy-MM-dd">
                    <input class="form-control" ng-model="endDate"
                           placeholder="click here" type="text" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <label>Unit</label> <select ui-select2 class="form-control"
                                            style="width: 100% !important" data-ng-model="selectedUnit"
                                            data-placeholder="Select a Unit"
                                            data-ng-change="selectUnit(selectedUnit)"
                                            data-ng-options="u as u.name for u in units">
            </select>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-primary" style="margin-top: 20px;"
                        data-ng-click="regeneratePnlForUnit()"
                        data-ng-disabled="endDate == null || startDate == null || selectedUnit == null">Regenerate
                    For Unit
                </button>
            </div>
        </div>
    </div>
</div>