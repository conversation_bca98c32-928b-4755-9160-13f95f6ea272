<style>
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        animation: blink 1s infinite ease-in-out;
        font-size: 1em;
    }

    @keyframes blink {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0;
        }
    }
</style>

<div class="modal-lg" data-ng-init="init()">
    <div class="modal-header">
        <button type="button" class="close" ng-click="cancel()">
            <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" ng-hide="editMode || isCloning" id="modal-title">Offer Creation Process</h4>
        <h4 class="modal-title" ng-hide="!editMode || isCloning" id="modal-title2">Offer Editing Process</h4>
        <h4 class="modal-title" data-ng-if="isCloning && editMode" id="modal-title3">Offer Cloning Process</h4>

    </div>
    <div class="modal-body" id="modal-body">
        <uib-tabset active="tabIndex">
            <uib-tab index="0" heading="Offer" disable="tabIndex!=0">
                <div class="form-group">
                    <label>Accounts Type *</label>
                    <select ng-model="offerDetail.accountsCategory" class="form-control"
                            ng-options="c as c.name for c in offerAccountsCategories track by c.id"/>
                </div>
                <div class="form-group">
                    <label>Offer Type *</label>
                    <select class="form-control" ng-model="offerDetail.category"
                            ng-options="offerCategories as offerCategories for offerCategories in offerCategories"
                            ng-change="getSubTypes(offerDetail.category)">

                    </select>
                </div>
                <div class="form-group">
                    <label>Offer Sub Type *</label>
                    <select ng-model="offerDetail.type"
                            ng-options="offerType as offerType for offerType in offerCategorySubType"
                            data-ng-change="setOfferValueHeading(offerDetail.type)"
                            class="form-control">
                    </select>
                </div>
                <div class="form-group">
                    <label>Brand Id *</label>
                    <select ng-model="offerDetail.brandId"
                            ng-options="brand.brandId as (brand.brandId + ' - ' + brand.brandName) for brand in brands"
                            class="form-control"
                            data-ng-change="getProductsByBrandId(offerDetail.brandId)"
                            data-ng-disabled="isBrandIdEditable()"
                    >
                    </select>
                </div>
                <div class="form-group">
                    <label>Frequency Applicable</label>
                    <select class="form-control"
                            ng-model="offerDetail.frequencyApplicable"
                            ng-options="frequencyApplicable.code as frequencyApplicable.name for frequencyApplicable in yesNoOption"
                            ng-change="checkFrequencyApplicable()"></select>
                </div>
                <div class="form-group">
                    <label>Frequency Count</label>
                    <input class="form-control"
                           type="number"
                           ng-model="offerDetail.frequencyCount"
                           min="0"
                           max="31"
                           ng-disabled="!offerDetail.frequencyApplicable"/>
                </div>

                <div class="form-group">
                    <label>Applicable Hour</label>
                    <input class="form-control"
                           type="number"
                           ng-model="offerDetail.applicableHour"
                           min="1"
                           max="24"
                           ng-disabled="!offerDetail.frequencyApplicable"/>
                </div>

                <div class="form-group">
                    <label>Daily Frequency Count</label>
                    <input class="form-control"
                           type="number"
                           ng-model="offerDetail.dailyFrequencyCount"
                           min="1"
                           max="24"
                           ng-disabled="!offerDetail.frequencyApplicable"/>
                </div>
                <div class="form-group">
                    <label>Max Quantity</label>
                    <input class="form-control"
                           type="number"
                           ng-model="offerDetail.maxQuantity"
                           min="0"
                           max="31"
                           ng-disabled="!offerDetail.frequencyApplicable"/>
                </div>
                <div class="form-group">
                    <label>Frequency Strategy</label>
                    <select class="form-control"
                            ng-model="offerDetail.frequencyStrategy"
                            ng-disabled="!offerDetail.frequencyApplicable"
                            ng-options="list as list for list in frequencyStrategyList"></select>
                </div>
                <div class="form-group">
                    <label>Auto Applicable At Unit</label>
                    <select class="form-control"
                            ng-model="offerDetail.autoApplicableforUnit"
                            ng-options="autoApplicableforUnit.code as autoApplicableforUnit.name for autoApplicableforUnit in yesNoOption"></select>
                </div>

                <div class="form-group">
                    <label>Offer Reason *</label>
                    <textarea class="form-control" rows="3" ng-model="offerDetail.text"
                              style="min-width: 100%; max-width: 100%; min-height: 80px" required></textarea>
                </div>
                <div class="form-group">
                    <label>Offer Description *</label>
                    <textarea class="form-control" rows="3"
                              ng-model="offerDetail.description" 
                              style="min-width: 100%; max-width: 100%; min-height: 80px" required></textarea>
                </div>
                <div class="form-group">
                    <div style="display: inline-block; width: 100%">
                        <div style="float:left">
                            <label>Start Date *</label>
                            <div class="datepicker" date-format="yyyy-MM-dd">
                                <input class="form-control" ng-model="offerDetail.startDate"
                                       placeholder="click here" type="text" required/>
                            </div>
                        </div>
                        <div style="float:right">
                            <label>End Date *</label>
                            <div class="datepicker" date-format="yyyy-MM-dd"  data-date-format="yyyy-MM-dd" data-date-min-limit="{{offerDetail.startDate==null?getDate1():getBusinessDateForDate(offerDetail.startDate)}}">
                                <input class="form-control" ng-model="offerDetail.endDate" ng-change ="compareTwoDates(offerDetail.startDate, offerDetail.endDate)"
                                       placeholder="click here" type="text" required/>
                            </div>
                        </div>
<!--                        <div ng-show="compareTwoDates(offerDetail.startDate, offerDetail.endDate)">-->
<!--                            <script type="text/javascript">-->
<!--                                alert("Please ensure endDate is greater than startDate");-->
<!--                            </script>-->
<!--                        </div>-->
                    </div>
                </div>

                <div class="form-group">
                    <label>Offer Status * </label> <select class="form-control"
                                                           ng-model="offerDetail.status"
                                                           ng-options="offerStatuses as offerStatuses for offerStatuses in offerStatuses"></select>
                </div>
                <div class="form-group">
                    <label>Minimum Bill Value </label> <input type="number"
                                                              class="form-control" min="0" string-to-number
                                                              ng-model="offerDetail.minValue"/>
                </div>
                <div class="form-group">
                    <label>Validate Customer * (customer login Required) </label> <select
                        class="form-control" ng-model="offerDetail.validateCustomer" data-ng-change="setOtpRequired(offerDetail.validateCustomer)"
                        ng-options="option.code as option.name for option in yesNoOption"></select>
                </div>
                <div class="form-group">
                    <label>Remove Loyalty </label> <select
                        class="form-control" ng-model="offerDetail.removeLoyalty"
                        ng-options="option.code as option.name for option in yesNoOption"></select>
                </div>
                <div class="form-group">
                    <label>Offer Scope *</label> <select class="form-control"
                                                         ng-model="offerDetail.offerScope"
                                                         ng-options="offerScopes as offerScopes for offerScopes in offerScopes"></select>
                </div>
                <div class="form-group"
                     data-ng-show="offerDetail.offerScope != null && offerDetail.offerScope == 'CORPORATE'">
                    <label>Email Domain *</label> <input type="text"
                                                         class="form-control" ng-model="offerDetail.emailDomain"/>
                </div>
                <div class="form-group">
                    <label>Minimum Item Count *</label> <input type="number"
                                                               class="form-control" min="0" string-to-number
                                                               ng-model="offerDetail.minItemCount"/>
                </div>
                <div class="form-group">
                    <label>Quantity Limit *</label> <input type="number"
                                                           class="form-control" min="0" string-to-number
                                                           ng-model="offerDetail.minQuantity"/>
                </div>
                <div class="form-group" data-ng-if="offerDetail.type=='FREEBIE_STRATEGY'">
                    <label>Offer Value Type*</label> <select class="form-control"
                                                               ng-model="offerDetail.offerValueType"
                                                               ng-options="type as type for type in offerValueTypes"></select>
                </div>
                <div class="form-group">
                    <label> {{displayOnlyVal}} Offer Value *</label> <input
                        type="number" class="form-control" min="0" string-to-number
                        ng-model="offerDetail.offerValue"/>
                </div>
                <div class="form-group" data-ng-if="offerDetail.type=='PERCENTAGE_BILL_MAX_CAP_STRATEGY' || offerDetail.type=='PERCENTAGE_ITEM_MAX_CAP_STRATEGY'">
                    <label>Max Capped Discount</label>
                    <input type="number" class="form-control" min="0" string-to-number
                           ng-model="offerDetail.maxDiscountAmount"/>
                </div>
                <div class="form-group" data-ng-if="offerDetail.accountsCategory.name=='LOYALTY OFFER'">
                    <label>Loyalty Burn Points </label> <input class="form-control" type="number" min="0" string-to-number
                                                             ng-model="offerDetail.loyaltyBurnPoints"/>
                </div>
                <div class="form-group" data-ng-if="offerDetail.accountsCategory.name=='LOYALTY OFFER'">
                    <label>Signup Offer Applicable </label> 
                    <select class="form-control"
                    ng-model="offerDetail.signupOfferApplicable"
                    ng-options="signupOfferApplicable.code as signupOfferApplicable.name for signupOfferApplicable in yesNoOption"></select>
                </div>
                <div class="form-group">
                    <label>OTP Required *</label>
                    <select
                            class="form-control" ng-model="offerDetail.otpRequired"
                            ng-options="option.code as option.name for option in yesNoOption"
                            data-ng-disabled="!isOtpEditable"></select>
                </div>
                <div class="form-group">
                    <label>Terms and Conditions</label>
                    <div ng-repeat="tnc in tncInputs track by $index">
                        <div style="display: inline-block; width: 100%; margin: 3px">
                            <input class="form-control" type="text" style="width: 90%; float: left; margin-right: 3px" ng-model="tncInputs[$index]"/>
                            <button class="btn btn-danger" ng-if="tncInputs.length != 1" ng-click='deleteTNC($index)'>-</button>
                            <button class="btn btn-primary" ng-if="$index == tncInputs.length-1" ng-click='addTNC()'>+</button>
                        </div>
                    </div>
                </div>
                <button ng-click="validateOfferTab()" class="btn btn-primary">Next</button>
            </uib-tab>

            <uib-tab index="1" ng-hide="editMode && !isCloning" heading="Offer Mapping" disable="tabIndex!=1">
                <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab" id="headingOne"
                             ng-click="getUnitData('category')">
                            <h4 class="panel-title">
                                <button data-toggle="collapse" data-parent="#accordion"
                                        data-target="#{{unit}}" aria-expanded="false"
                                        ng-class="{'btn disabled': selectedProductsLength > 0}"
                                        data-ng-disabled="selectedProductsLength > 0"
                                        aria-controls="collapseOne">
                                    Category ({{selectedCategoriesLength}})<i
                                        class="pull-right glyphicon"
                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                </button>
                            </h4>
                        </div>
                        <div id="category" class="panel-collapse collapse"
                             role="tabpanel" aria-labelledby="headingOne">
                            <div class="panel-body">
                                <div data-ng-if="productCategoryBrandWise.length == 0">
                                    <h4 class="loading">Loading...</h4>
                                </div>
                                <table class="table table-bordered" data-ng-if="productCategoryBrandWise.length > 0">
                                    <thead style="background-color: #50773e; color: #ffffff">
                                    <th>
                                        <input type="checkbox" ng-model="checkAllCategories"
                                               ng-click="allCategories(checkAllCategories)"/>&nbsp;Check
                                    </th>
                                    <th>Category Name&nbsp;</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat='detDatas in productCategoryBrandWise | orderBy : "detail.name"'>
                                        <td ng-class="{'success': sel[detDatas.detail.id] == true}">
                                            <input type="checkbox"
                                                   ng-model="sel[detDatas.detail.id]"
                                                   ng-click="manageCategories(detDatas,sel[detDatas.detail.id])">
                                        </td>
                                        <td ng-class="{'success': sel[detDatas.detail.id] == true}">
                                            {{detDatas.detail.name}}
                                        </td>
                                    </tr>

                                    <tr style="display:none">
                                        <td colspan="2" align="right">
                                            <div>
                                                <button class="btn btn-primary" type="button"
                                                        ng-click="selectedSubmitProductCategory()">
                                                    Submit
                                                    Category
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab" id="headingOne"
                             ng-click="getUnitData('subCategory')">
                            <h4 class="panel-title">
                                <button data-toggle="collapse" data-parent="#accordion"
                                        data-target="#{{unit}}" aria-expanded="false"
                                        ng-class="{'btn disabled': selectedProductsLength > 0}"
                                        aria-controls="collapseOne"
                                        data-ng-disabled="selectedProductsLength > 0">
                                    Sub Category ({{selectedSubCategoriesLength}})<i class="pull-right glyphicon"
                                                                                     ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                </button>
                            </h4>
                        </div>
                        <div id="subCategory" class="panel-collapse collapse"
                             role="tabpanel" aria-labelledby="headingOne">
                            <div class="panel-body">
                                <div data-ng-if="productSubCategoryBrandWise.length == 0">
                                    <h4 class="loading">Loading...</h4>
                                </div>
                                <table class="table table-bordered" data-ng-if="productSubCategoryBrandWise.length > 0">
                                    <thead style="background-color: #50773e; color: #ffffff">
                                    <th><input type="checkbox" ng-model="checkAllSubCategories"
                                               ng-click="allSubCategories(checkAllSubCategories)"/>&nbsp;Check &nbsp;
                                    </th>
                                    <th>Sub Category Name&nbsp;</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat='detDatas1 in productSubCategoryBrandWise | orderBy: "name"'>
                                        <td ng-class="{'success': subCatObj[detDatas1.id] == true}"><input
                                                type="checkbox"
                                                ng-model="subCatObj[detDatas1.id]"
                                                ng-click="manageSubCategories(detDatas1,subCatObj[detDatas1.id])">
                                        </td>
                                        <td ng-class="{'success': subCatObj[detDatas1.id] == true}">{{detDatas1.name}}
                                        </td>
                                        <!--   <pre>{{ fullSubCatObj | json}}</pre>-->
                                    </tr>
                                    <tr style="display:none">
                                        <td colspan="2" align="right">
                                            <div>
                                                <button class="btn btn-primary" type="button"
                                                        ng-click="selectedSubmitSubtypeCategory()">
                                                    Submit
                                                    Sub Category
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab" id="headingOne"
                             ng-click="getUnitData('product')">
                            <h4 class="panel-title">
                                <button data-toggle="collapse" data-parent="#accordion"
                                        data-target="#{{unit}}" aria-expanded="false"
                                        ng-class="{'btn disabled': selectedCategoriesLength > 0 || selectedSubCategoriesLength > 0}"
                                        data-ng-disabled="selectedCategoriesLength > 0 || selectedSubCategoriesLength > 0"
                                        aria-controls="collapseOne">
                                    Product ({{selectedProductsLength}})<i class="pull-right glyphicon"
                                                                           ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                </button>
                            </h4>
                        </div>
                        <div id="product" class="panel-collapse collapse"
                             role="tabpanel" aria-labelledby="headingOne">
                             <div data-ng-if="productDetailsList.length == 0">
                                <h4 class="loading">Loading...</h4>
                            </div>
                            <div class="panel-body" data-ng-if="productDetailsList.length > 0">
                                <button class="btn btn-primary" type="button"
                                         data-ng-click="selected()">Apply Filter
                                </button>
                                <div data-ng-show="isSelected == true && hideFilter == false" >
                                <div class="form-group" >
                                    <div style="display: inline-block; width: 100%">
                                        <div>
                                            <label>Category</label>
                                            <select
                                                    id="filterCategory_id"
                                                    class="form-control" ng-model="offerDetail.filterCategory"
                                                    data-ng-click="categoryChanged()"
                                                    ng-options="filter.detail.name as filter.detail.name for filter in productCategoryBrandWise">
                                            </select>
                                        </div>
                                        <div>
                                            <label>Sub Category</label>
                                            <select
                                                    id="filterSubCategory_id"
                                                    class="form-control" data-ng-model="offerDetail.filterSubCategory"
                                                    data-ng-click="getSubCategory()"
                                                    data-ng-options="filterSub.name as filterSub.name for filterSub in productSubCategoryBrandWise">
                                            </select>
                                        </div><br/>
                                    </div><br/>
                                    <div style="float:right">
                                        <button class="btn btn-primary" type="button"
                                                ng-click="applyFilter()">Apply
                                        </button>
                                        <button class="btn btn-primary" type="button"
                                                ng-click="removeFilter()">Remove Filter
                                        </button>
                                    </div>
                                </div>
                                </div>
                                <table class="table table-bordered">
                                    <thead style="background-color: #50773e; color: #ffffff">
                                    <th><input type="checkbox" ng-model="checkAllProducts"
                                               ng-click="allProducts(checkAllProducts)"/>&nbsp;Check &nbsp;
                                    </th>
                                    <th>Product Name&nbsp;</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat='detDatas2 in productDetailsList | orderBy: "name"'>
                                        <td ng-class="{'success': prodObj[detDatas2.id] == true}"><input type="checkbox"
                                                                                                         ng-model='prodObj[detDatas2.id]'
                                                                                                         ng-click="manageProducts(detDatas2,prodObj[detDatas2.id])">
                                        </td>
                                        <td ng-class="{'success': prodObj[detDatas2.id] == true}">{{detDatas2.name}}
                                        </td>
                                    </tr>
                                    <tr style="display:none">
                                        <td align="right" colspan="2">
                                            <div>
                                                <button class="btn btn-primary" type="button"
                                                        ng-click="selectedSubmitProducts()">Submit
                                                    Product
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <button ng-click="prev()" class="btn btn-primary">Prev</button>
                <button ng-click="validateOfferMappingTab()" class="btn btn-primary">Next</button>
            </uib-tab>

            <uib-tab index="2" data-ng-if="offerDetail.type == 'FREEBIE_STRATEGY'"  heading="Free Product Mapping" disable="tabIndex!=2">
                <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab" id="headingOne"
                             ng-click="getUnitData('product')">
                            <h4 class="panel-title">
                                <button data-toggle="collapse" data-parent="#accordion"
                                        data-target="#freeProduct" aria-expanded="false"
                                        aria-controls="collapseOne">
                                    Free Product ({{selectedFreeProductLength}})<i class="pull-right glyphicon"
                                                                           ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                </button>
                            </h4>
                        </div>
                        <div id="freeProduct" class="panel-collapse collapse"
                             role="tabpanel" aria-labelledby="headingOne">
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <button class="btn btn-primary" type="button"
                                            data-ng-click="selected()">Apply Filter
                                    </button>
                                    <div data-ng-show="isSelected == true && hideFilter == false" >
                                        <div class="form-group" >
                                            <div style="display: inline-block; width: 100%">
                                                <div>
                                                    <label>Category</label>
                                                    <select
                                                            id="filterCategory_id2"
                                                            class="form-control" ng-model="offerDetail.filterCategory"
                                                            data-ng-click="categoryChanged()"
                                                            ng-options="filter.detail.name as filter.detail.name for filter in productCategoryBrandWise">
                                                    </select>
                                                </div>
                                                <div>
                                                    <label>Sub Category</label>
                                                    <select
                                                            id="filterSubCategory_id3"
                                                            class="form-control" data-ng-model="offerDetail.filterSubCategory"
                                                            data-ng-click="getSubCategory()"
                                                            data-ng-options="filterSub.name as filterSub.name for filterSub in productSubCategoryBrandWise">
                                                    </select>
                                                </div><br/>
                                            </div><br/>
                                            <div style="float:right">
                                                <button class="btn btn-primary" type="button"
                                                        ng-click="applyFilter()">Apply
                                                </button>
                                                <button class="btn btn-primary" type="button"
                                                        ng-click="removeFilter()">Remove Filter
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <thead style="background-color: #50773e; color: #ffffff">
                                    <th>&nbsp;Check &nbsp;
                                    </th>
                                    <th>Product Name&nbsp;</th>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat='detDatas2 in productDetailsList | orderBy: "name"'>
                                        <td ng-class="{'success': freeProdObj[detDatas2.id]==true}"><input type="checkbox"
                                                                                                         ng-model='freeProdObj[detDatas2.id]'
                                                                                                         ng-click="manageFreeProduct(detDatas2,freeProdObj[detDatas2.id])">
                                        </td>
                                        <td ng-class="{'success': freeProdObj[detDatas2.id] == true}">{{detDatas2.name}}
                                        </td>
                                    </tr>
                                    <tr style="display:none">
                                        <td align="right" colspan="2">
                                            <div>
                                                <button class="btn btn-primary" type="button"
                                                        ng-click="selectedSubmitProducts()">Submit
                                                    Product
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <button ng-click="prev()" class="btn btn-primary">Prev</button>
                <button ng-click="validateFreeProductMappingTab()" class="btn btn-primary">Next</button>
            </uib-tab>

            <uib-tab index="3" heading="Coupons Mapping" disable="tabIndex!=3">
                <h4>Coupons Mapping</h4>
                <uib-tabset active="subIndex">
                    <uib-tab index="0" heading="Unit" disable="subIndex!=0">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getRegionsData('regionUnitList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{regionUnitList}}" aria-expanded="false"
                                                        data-ng-disabled="selectedUnitsLength > 0 || selectedCitiesLength > 0"
                                                        ng-class="{'btn disabled': selectedUnitsLength > 0 || selectedCitiesLength > 0}"
                                                        aria-controls="collapseOne">
                                                    Select Regions ({{selectedRegionsLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="regionUnitList" class="panel-collapse collapse"
                                             role="tabpanel" aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllRegions"
                                                               ng-click="allRegions(checkAllRegions)"/>&nbsp;Check
                                                        &nbsp;
                                                    </th>
                                                    <th>Name&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='UnitRegionData in regionDetailList | orderBy'>
                                                        <td ng-class="{'success': unitObjs[UnitRegionData.name] == true}">
                                                            <input type="checkbox"
                                                                   ng-model="unitObjs[UnitRegionData.name]"
                                                                   ng-click="selectRegions(UnitRegionData,unitObjs[UnitRegionData.name])">
                                                            <!--ng-click="checkRegionDetails(UnitRegionData,unitObjs[UnitRegionData])"-->
                                                        </td>
                                                        <td ng-class="{'success': unitObjs[UnitRegionData.name] == true}">
                                                            {{UnitRegionData.name}}
                                                        </td>
                                                    </tr>
                                                    <tr style="display:none">
                                                        <td colspan="2" align="right">
                                                            <div>
                                                                <button class="btn btn-primary" type="button"
                                                                        ng-click="selectedSubmitRegions(regions)">
                                                                    Submit
                                                                    Regions
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingUnitCategory"
                                             ng-click="getUnitSubCategoryData('unitSubCategoryList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{unitSubCategoryList}}" aria-expanded="false"
                                                        aria-controls="collapseOne">
                                                    Select Unit Sub Categories ({{selectedUnitSubCategoryLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="unitSubCategoryList" class="panel-collapse collapse"
                                             role="tabpanel" aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllUnitSubCategories"
                                                               ng-click="allUnitSubCategories(checkAllUnitSubCategories)"/>&nbsp;Check
                                                        &nbsp;
                                                    </th>
                                                    <th>Name&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='category in unitSubCategoryDetailList | orderBy'>
                                                        <td ng-class="{'success': unitObjs[category.name] == true}">
                                                            <input type="checkbox"
                                                                   ng-model="unitObjs[category.name]"
                                                                   ng-click="selectUnitSubCategories(category,unitObjs[category.name])">
                                                        </td>
                                                        <td ng-class="{'success': unitObjs[category.name] == true}">
                                                            {{category.name}}
                                                        </td>
                                                    </tr>
                                                    <tr style="display:none">
                                                        <td colspan="2" align="right">
                                                            <div>
                                                                <button class="btn btn-primary" type="button"
                                                                        ng-click="selectedSubmitRegions(regions)">
                                                                    Submit
                                                                    Unit Sub Categories
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getUnitsData('UnitList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{UnitList}}" aria-expanded="false"
                                                        data-ng-disabled="((selectedRegionsLength>0) || (selectedUnitSubCategoryLength > 0) || (selectedCitiesLength > 0) ) ? true : false"
                                                        ng-class="{'btn btn-disabled':((selectedRegionsLength>0) || (selectedUnitSubCategoryLength > 0) || (selectedCitiesLength > 0)) ? true : false}"
                                                        aria-controls="collapseOne">
                                                    Select Units ({{selectedUnitsLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="UnitList" class="panel-collapse collapse"
                                             role="tabpanel" aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <h4 data-ng-if="filteredCafeListBrandWise.length == 0" 
                                                    class="loading">Loading...
                                                </h4>
                                                <table class="table table-bordered" data-ng-if="filteredCafeListBrandWise.length > 0">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllUnits"
                                                               ng-click="allUnits(checkAllUnits)"/>&nbsp;Check &nbsp;
                                                    </th>
                                                    <th>Name&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='UnitCafeData in filteredCafeListBrandWise | orderBy : "name"'>
                                                        <td ng-class="{'success': unitAllObj[UnitCafeData.id] == true}">
                                                            <input type="checkbox"
                                                                   ng-model="unitAllObj[UnitCafeData.id]"
                                                                   ng-click="selectUnits(UnitCafeData,unitAllObj[UnitCafeData.id])">
                                                            <!--ng-click="checkUnitDetails(UnitCafeData.region,UnitCafeData.id)"-->
                                                        </td>
                                                        <td ng-class="{'success': unitAllObj[UnitCafeData.id] == true}">
                                                            {{UnitCafeData.name}}
                                                        </td>
                                                    </tr>
                                                    <tr style="display:none">
                                                        <td colspan="2" align="right">
                                                            <div>
                                                                <button class="btn btn-primary" type="button"
                                                                        ng-click="selectedSubmitUnits(cafelist)">
                                                                    Submit
                                                                    Units
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="cityHeading">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#cityList" aria-expanded="true"
                                                        data-ng-disabled="((selectedRegionsLength>0)  || (selectedUnitsLength > 0)) ? true : false"
                                                        ng-class="{'btn btn-disabled':((selectedRegionsLength>0) || (selectedUnitsLength > 0)) ? true : false}"
                                                        aria-controls="cityList">
                                                    Select Cities ({{selectedCitiesLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>

                                        <div id="cityList" class="panel-collapse collapse" role="tabpanel" aria-labelledby="cityHeading">
                                            <div class="panel-body">
                                                <h4 data-ng-if="allCitiesList.length == 0"
                                                    class="loading">Loading...
                                                </h4>
                                                <table class="table table-bordered" data-ng-if="allCitiesList.length > 0">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllCities"
                                                               ng-click="allCities(checkAllCities)"/>&nbsp;Check &nbsp;
                                                    </th>
                                                    <th>Name&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='cityCafeData in allCitiesList | orderBy : "name"'>
                                                        <td ng-class="{'success': cityAllObj[cityCafeData.id] == true}">
                                                            <input type="checkbox"
                                                                   ng-model="cityAllObj[cityCafeData.id]"
                                                                   ng-click="selectCities(cityCafeData,cityAllObj[cityCafeData.id])">
                                                            <!--ng-click="checkUnitDetails(cityCafeData.region,cityCafeData.id)"-->
                                                        </td>
                                                        <td ng-class="{'success': cityAllObj[cityCafeData.id] == true}">
                                                            {{cityCafeData.name}}
                                                        </td>
                                                    </tr>
                                                    <tr style="display:none">
                                                        <td colspan="2" align="right">
                                                            <div>
                                                                <button class="btn btn-primary" type="button"
                                                                        ng-click="selectedSubmitUnits(cafelist)">
                                                                    Submit
                                                                    Units
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12" id="fullSelectedObjRegionListDiv"
                                     style="display: none">
                                    <table style="display: none" class="table table-bordered">
                                        <thead style="background-color: #50773e; color: #ffffff">
                                        <th>Region List&nbsp;</th>
                                        </thead>
                                        <tbody>
                                        <tr ng-repeat='fulSelObjRegion in selectedRegions'>
                                            <td>{{fulSelObjRegion.name}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-xs-12" id="fullSelectedObjUnitListDiv"
                                     style="display: none">
                                    <table style="display: none" class="table table-bordered">
                                        <thead style="background-color: #50773e; color: #ffffff">
                                        <th>Unit &nbsp;</th>
                                        </thead>
                                        <tbody>
                                        <tr ng-repeat='fulSelObjUnits in selectedUnits'>
                                            <td>{{fulSelObjUnits.name}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prev()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateUnitSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>
                    <uib-tab index="1" heading="Customer" ng-show="false" disable="subIndex!=1">
                        <div>
                            <input type="radio" ng-model="csvs.name" value="phoneNo"
                                   ng-click="showCustomerClk('phoneNo')"> <label>
                            upload CSV with Customer Phone No </label> <span style="display: none"
                                                                             id="dvPhoneNoCust"><input
                                type="file"
                                file-reader="fileContent"/></span>
                        </div>
                        <br>
                        <br>
                        <div align="right">
                            <a href="img/customer.csv" download> <b>CSV format for
                                phone no download </b>
                            </a>
                        </div>
                        <div align="center">OR</div>
                        <div>
                            <input type="radio" ng-model="csvs.name" value="customerID"
                                   ng-click="showCustomerClk('custID')"> <label>upload
                            CSV with Customer ID</label> <span style="display: none"
                                                               id="dvCustIDCust"><input type="file"
                                                                                        file-reader="fileContent"/></span>
                        </div>
                        <div align="right">
                            <a href="img/customerID.csv" download> <b>CSV format
                                for customer id download </b>
                            </a>
                        </div>
                        <br>
                        <table class="table table-bordered">
                            <thead style="background-color: #50773e; color: #ffffff">
                            <th>Customer &nbsp;</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat='fulSelObjUnits in fileContent'>
                                <td ng-if="fulSelObjUnits.value != ''">{{fulSelObjUnits.value}}</td>
                            </tr>
                            </tbody>
                        </table>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateCustomerSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>
                    <uib-tab index="2" heading="Product" disable="subIndex!=2">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default"
                                         ng-repeat="fulSelObjProd in selectedProducts | orderBy : 'productName'">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getProductDataMap(fulSelObjProd.id,fulSelObjProd.dimensionID)">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{fulSelObjProd.code}}"
                                                        aria-expanded="false"
                                                        aria-controls="collapseOne">
                                                    {{fulSelObjProd.productName}}<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="{{fulSelObjProd.code}}"
                                             class="panel-collapse collapse" role="tabpanel"
                                             aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th>Check &nbsp;</th>
                                                    <th>Name&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr
                                                            ng-repeat='dimensionNameList in fulSelObjProd.dimensionSelectionMap'>
                                                        <td ng-class="{'success': dimensionNameList.checked == true}">
                                                            <input type="checkbox"
                                                                   ng-click="manageProductDimensions(fulSelObjProd,dimensionNameList,dimensionNameList.checked)"
                                                                   ng-model="dimensionNameList.checked"></td>
                                                        <td ng-class="{'success': dimensionNameList.checked == true}">
                                                            {{dimensionNameList.dimension.name}}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateProductSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>

                    <uib-tab index="3" data-ng-if="offerDetail.type == 'FREEBIE_STRATEGY'" heading="Free Product" disable="subIndex!=3">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default"
                                         ng-repeat="fulSelFreeObjProd in selectedFreeProduct">
                                        <div class="panel-heading" role="tab" id="headingTwo"
                                             ng-click="getProductDataMap(fulSelFreeObjProd.id,fulSelFreeObjProd.dimensionID)">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{fulSelFreeObjProd.code}}"
                                                        aria-expanded="false"
                                                        aria-controls="collapseOne">
                                                    {{fulSelFreeObjProd.productName}}<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="{{fulSelFreeObjProd.code}}"
                                             class="panel-collapse collapse" role="tabpanel"
                                             aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th>Check &nbsp;</th>
                                                    <th>Name&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr
                                                            ng-repeat='dimensionNameList in fulSelFreeObjProd.dimensionSelectionMap'>
                                                        <td ng-class="{'success': dimensionNameList.checked == true}">
                                                            <input type="checkbox"
                                                                   ng-click="manageFreeProductDimension(fulSelFreeObjProd,dimensionNameList,dimensionNameList.checked)"
                                                                   ng-model="dimensionNameList.checked"></td>
                                                        <td ng-class="{'success': dimensionNameList.checked == true}">
                                                            {{dimensionNameList.dimension.name}}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateFreeProductSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>

                    <uib-tab index="4" heading="Order Source" disable="subIndex!=4">
                        <h3>Select Order Source</h3>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getOrderSourceData('orderSourceList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{orderSourceDatas}}"
                                                        aria-expanded="false"
                                                        aria-controls="collapseOne">
                                                    Order Source ({{selectedOrderSourcesLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="orderSourceList" class="panel-collapse collapse"
                                             role="tabpanel" aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllOrdSrc"
                                                               ng-click="allOrderSources(checkAllOrdSrc)"/>&nbsp;Check
                                                        &nbsp;
                                                    </th>
                                                    <th>Order Source&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='orderSourceListsData in families | orderBy'>
                                                        <td ng-class="{'success': orderSourceObj[orderSourceListsData] == true}">
                                                            <input type="checkbox"
                                                                   ng-model="orderSourceObj[orderSourceListsData]"
                                                                   ng-click="selectOrderSource(orderSourceListsData,orderSourceObj[orderSourceListsData])">
                                                        </td>
                                                        <td ng-class="{'success': orderSourceObj[orderSourceListsData] == true}">
                                                            {{orderSourceListsData}}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateOrderSourceSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>
                    <uib-tab index="5" heading="Channel Partners" disable="subIndex!=5">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getChannelPartnerData('channelPartnerList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{partnerChannel}}" aria-expanded="false"
                                                        aria-controls="collapseOne">
                                                    Channel Partner ({{selectedChannelPartnersLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="channelPartnerList" class="panel-collapse collapse"
                                             role="tabpanel" aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllChannelPartners"
                                                               ng-click="allChannelPartners(checkAllChannelPartners)"/>&nbsp;
                                                        Check &nbsp;
                                                    </th>
                                                    <th>Channel Partner&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr
                                                            ng-repeat='channelPartnerListsData in channelPartner | orderBy : "name"'>
                                                        <td ng-class="{'success':channelPartObj[channelPartnerListsData.id] == true}">
                                                            <input type="checkbox"
                                                                   ng-click="selectChannelPartner(channelPartnerListsData,channelPartObj[channelPartnerListsData.id])"
                                                                   ng-model="channelPartObj[channelPartnerListsData.id]">
                                                        </td>
                                                        <td ng-class="{'success':channelPartObj[channelPartnerListsData.id] == true}">
                                                            {{channelPartnerListsData.name}}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateChannelPartnersSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>
                    <uib-tab index="6" heading="Payment Mode" disable="subIndex!=6">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getPaymentModeData('paymentModeList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{paymentModeLists}}"
                                                        aria-expanded="false"
                                                        aria-controls="collapseOne">
                                                    Payment Mode ({{selectedPaymentModesLength}})<i
                                                        class="pull-right glyphicon"
                                                        ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="paymentModeList" class="panel-collapse collapse"
                                             role="tabpanel" aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                <table class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th><input type="checkbox" ng-model="checkAllPaymentModes"
                                                               ng-click="allPaymentModes(checkAllPaymentModes)">&nbsp;
                                                        Check &nbsp;
                                                    </th>
                                                    <th>Payment Mode&nbsp;</th>
                                                    <th>Type&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='paymentModeListsData in paymentMode | orderBy : "name"'>
                                                        <td ng-class="{'success':paymentModeObj[paymentModeListsData.id] == true}">
                                                            <input type="checkbox"
                                                                   ng-click="selectPaymentModes(paymentModeListsData,paymentModeObj[paymentModeListsData.id])"
                                                                   ng-model="paymentModeObj[paymentModeListsData.id]">
                                                        </td>
                                                        <td ng-class="{'success':paymentModeObj[paymentModeListsData.id] == true}">
                                                            {{paymentModeListsData.name}}
                                                        </td>
                                                        <td ng-class="{'success':paymentModeObj[paymentModeListsData.id] == true}">
                                                            {{paymentModeListsData.settlementType}}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validatePaymentModeSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>
                    <uib-tab index="7" heading="New Customer" disable="subIndex!=7">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="panel-group" id="accordion" role="tablist"
                                     aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" id="headingOne"
                                             ng-click="getNewCustomerModeData('newCustomerModeList')">
                                            <h4 class="panel-title">
                                                <button data-toggle="collapse" data-parent="#accordion"
                                                        data-target="#{{newCustomerModeLists}}"
                                                        aria-expanded="false" aria-controls="collapseOne">
                                                    New Customer<i class="pull-right glyphicon"
                                                                   ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                </button>
                                            </h4>
                                        </div>
                                        <div id="newCustomerModeList"
                                             class="panel-collapse collapse" role="tabpanel"
                                             aria-labelledby="headingOne">
                                            <div class="panel-body">
                                                    <table class="table table-bordered">
                                                        <thead style="background-color: #50773e; color: #ffffff">
                                                        <th>Check &nbsp;</th>
                                                        <th>New Customer&nbsp;</th>
                                                        </thead>
                                                        <tbody>
                                                        <tr>
                                                            <td ng-class="{'success':newCustomerCheck == true}">
                                                                <input type="checkbox"
                                                                       ng-click="manageNewCustomer(newCustomerCheck)"
                                                                       ng-model="newCustomerCheck"></td>
                                                            <td ng-class="{'success':newCustomerCheck == true}">Yes</td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                    <table data-ng-if="newCustomerCheck == true" class="table table-bordered">
                                                    <thead style="background-color: #50773e; color: #ffffff">
                                                    <th>
                                                        <input type="checkbox"
                                                               ng-model="source"
                                                               ng-click="allAcquisitionSources(source)"
                                                        />&nbsp;Check
                                                    </th>
                                                    <th>Acquisition Source&nbsp;</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat='src in acquisitionTypes'>
                                                        <td ng-class="{'success': isSelectedSource(src) == true}">
                                                            <input type="checkbox"
                                                                   id="{{src}}"
                                                                   ng-model="source[src]"
                                                                   ng-click="manageAcquisitionSource(src,source[src])">
                                                        </td>
                                                        <td ng-class="{'success': isSelectedSource(src) == true}">
                                                            {{src}}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                                <div class="form-group" data-ng-if="newCustomerCheck == true">
                                                    <label>Add New Source</label>
                                                    <div ng-repeat="acq in acqSources track by $index">
                                                        <div style="display: inline-block; width: 100%; margin: 3px">
                                                            <input class="form-control" type="text" style="width: 90%; float: left; margin-right: 3px" ng-model="acqSources[$index]"/>
                                                            <button class="btn btn-danger" ng-if="acqSources.length != 1" ng-click='deleteAcq($index)'>-</button>
                                                            <button class="btn btn-primary" ng-if="$index == acqSources.length-1" ng-click='addAcq()'>+</button>
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-primary" data-ng-click='addAcqToAcqList()'>Add</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button ng-click="prevSubIndex()" class="btn btn-primary">Prev</button>
                        <button ng-click="validateNewCustomerSubTab()" class="btn btn-primary">Next</button>
                    </uib-tab>
                </uib-tabset>
            </uib-tab>

            <uib-tab index="4" heading="Partners" disable="tabIndex!=4">
                <h3>Select Partners</h3>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading" role="tab" id="headingOne"
                                     ng-click="getPartnerData('partnersList')">
                                    <h4 class="panel-title">
                                        <button data-toggle="collapse" data-parent="#accordion"
                                                data-target="#{{partnerUnit}}" aria-expanded="false"
                                                aria-controls="collapseOne">
                                            Partners List ({{offerDetail.partners.length}}) <i
                                                class="pull-right glyphicon"
                                                ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                        </button>
                                    </h4>
                                </div>
                                <div id="partnersList" class="panel-collapse collapse"
                                     role="tabpanel" aria-labelledby="headingOne">
                                    <div class="panel-body">
                                        <table class="table table-bordered">
                                            <thead style="background-color: #50773e; color: #ffffff">
                                            <th><input type="checkbox" ng-model="checkAllPartners"
                                                       ng-click="allPartners(checkAllPartners)">&nbsp; Check &nbsp;
                                            </th>
                                            <th>Partners Name&nbsp;</th>
                                            </thead>
                                            <tbody>
                                            <tr ng-repeat='partnerListsData in partnerDetailsList | orderBy : "name"'>
                                                <td ng-class="{'success': partObj[partnerListsData.id] == true}">
                                                    <input type="checkbox"
                                                           ng-model="partObj[partnerListsData.id]"
                                                           ng-click="managePartners(partnerListsData,partObj[partnerListsData.id])"
                                                           checked="checked"></td>
                                                <td ng-class="{'success': partObj[partnerListsData.id] == true}">
                                                    {{partnerListsData.name}}
                                                </td>
                                                <!-- ng-click="checkPartnerDetail(partnerListsData.id,partObj[partnerListsData.id],partnerListsData.name)"  <pre>{{ fullSubCatObj | json}}</pre>-->
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12" id="fullSelectedObjPartnerListDiv"
                             style="display: none">
                            <table class="table table-bordered">
                                <thead style="background-color: #50773e; color: #ffffff">
                                <th>Partner List &nbsp;</th>
                                <th>Status &nbsp;</th>
                                </thead>
                                <tbody>
                                <tr ng-repeat='fulSelObjParnter in fullSelectedObjPartner'>
                                    <td>{{fulSelObjParnter.name}}</td>
                                    <td>{{fulSelObjParnter.status}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <button ng-click="prev()" class="btn btn-primary">Prev</button>
                <button ng-click="validatePartnersTab()" class="btn btn-primary">Next</button>
            </uib-tab>

            <uib-tab index="5" heading="Coupons" ng-hide="editMode && !isCloning" disable="tabIndex!=5">
                <div class="col-xs-12">
                    <div class="col-xs-4">
                        <label>Prefix *{{actionOffer}}</label>
                        <input type="text"
                               class="form-control text-uppercase"
                               ng-model="couponDetail.prefix"/>
                    </div>
                    <div class="col-xs-4">
                        <label>Code *</label>
                        <input type="input"
                               class="form-control text-uppercase"
                               ng-model="couponDetail.code"
                               ng-disabled="couponDetail.prefix === null || couponDetail.prefix === ''"
                               ng-blur="checkCouponsAvail(couponDetail.code,couponDetail.prefix)"/>
                    </div>
                    <div class="col-xs-4">
                        <loading style="align:center"></loading>
                        <label>{{prefixCouponsDetail}}</label>
                        <div style="color:{{color}}">{{couponsAvail}}</div>
                    </div>
                </div>
                <div class="col-xs-12" ng-if="actionOffer=='edit'">
                    <br>
                    <div class="col-xs-4">
                        <label>Code : {{prefixCouponsDetail}}</label>
                        <div style="color: {{color">{{couponsAvail}}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label> Status * </label>
                    <select class="form-control"
                            ng-model="couponDetail.status"
                            ng-options="offerStatuses as offerStatuses for offerStatuses in offerStatuses"></select>
                </div>
                <div class="form-group">
                    <label>Start Date *</label>
                    <div class="datepicker" date-format="yyyy-MM-dd">
                        <input class="form-control" ng-model="couponDetail.startDate"
                               placeholder="click here" type="text" required/>
                    </div>
                </div>
                <div class="form-group">
                    <label>End Date *</label>
                    <div class="datepicker" date-format="yyyy-MM-dd">
                        <input class="form-control" ng-model="couponDetail.endDate"
                               placeholder="click here" type="text" required/>
                    </div>
                </div>
                <div class="form-group">
                    <label> Reusable Coupons* </label>
                    <select class="form-control"
                            ng-model="couponDetail.reusable"
                            ng-options="option.code as option.name for option in yesNoOption"></select>
                </div>
                <div class="form-group" >
                    <label>Reusable Applicability (In Days)</label>
                    <input type="number" class="form-control" min="0" string-to-number
                           ng-model="couponDetail.couponApplicability"/>
                </div>
                <div class="form-group">
                    <label> Reusable Customer* </label>
                    <select class="form-control"
                            ng-model="couponDetail.reusableByCustomer"
                            ng-options="option.code as option.name for option in yesNoOption"></select>
                </div>
                <div class="form-group">
                    <label>Max Usage*</label> <input type="number"
                                                     class="form-control" string-to-number
                                                     ng-model="couponDetail.maxUsage"/>
                </div>
                <button ng-click="prev()" class="btn btn-primary">Prev</button>
                <button data-ng-disabled="disableCouponNextButton" ng-click="validateCouponsTab()"
                        class="btn btn-primary">Next
                </button>
            </uib-tab>


            <uib-tab index="6" heading="Summary Coupons" disable="tabIndex!=6">
                <table class="table table-bordered">
                    <br>
                    <tr style="background-color: #50773e; color: #ffffff">
                        <th colspan="4" align="center">Offer Summary&nbsp;</th>
                    </tr>
                    <tr>
                        <td><b>Brand ID</b> : {{couponDetail.offer.brandId}}</td>
                        <td><b>Category</b> : {{couponDetail.offer.category}}</td>
                        <td><b>Offer Reason</b> : {{couponDetail.offer.text}}</td>
                        <td><b>Description</b> : {{couponDetail.offer.description}}</td>
                    </tr>
                    <tr>
                        <td><b>Type</b> : {{couponDetail.offer.type}}</td>
                        <td><b>Start Date </b> : <span>{{couponDetail.offer.startDate
											| date:'yyyy-MM-dd'}}</span></td>
                        <td><b>End Date </b> :
                            {{couponDetail.offer.endDate | date:'yyyy-MM-dd'}}
                        </td>
                    </tr>
                    <tr>
                        <td><b> Minimum Value </b> : {{couponDetail.offer.minValue}}</td>
                        <td><b> Quantity Limit </b> : {{couponDetail.offer.minQuantity}}</td>
                        <td><b> Minimum Item Count </b> : {{couponDetail.offer.minItemCount}}</td>
                    </tr>
                    <tr>
                        <td><b> Offer Value </b> :
                            {{couponDetail.offer.offerValue}}
                        </td>
                        <td><b>OfferScope </b> :
                            {{couponDetail.offer.offerScope}}
                        </td>
                        <td><b> Status </b> :
                            {{couponDetail.offer.status}}
                        </td>
                    </tr>
                    <tr data-ng-if="couponDetail.offer.accountsCategory.name=='LOYALTY OFFER'">
                        <td><b> Signup Offer Applicable </b> :
                            {{couponDetail.offer.signupOfferApplicable}}
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr style="background-color: #50773e; color: #ffffff">
                        <th colspan="4" align="center">Offer Meta Data
                            Description&nbsp;
                        </th>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Status</th>
                    </tr>
                    <tr
                            ng-repeat='fullMetaDataMapping in couponDetail.offer.metaDataMappings'>
                        <td>{{fullMetaDataMapping.value}}</td>
                        <td>{{fullMetaDataMapping.name}}</td>
                        <td>{{fullMetaDataMapping.status}}</td>
                    </tr>
                    <tr style="background-color: #50773e; color: #ffffff">
                        <th colspan="4" align="center">Partners&nbsp;</th>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Status</th>
                    </tr>
                    <tr
                            ng-repeat='fullMetaDataPartnersMap in couponDetail.offer.partners'>
                        <td>{{fullMetaDataPartnersMap.name}}</td>
                        <td>{{fullMetaDataPartnersMap.type}}</td>
                        <td>{{fullMetaDataPartnersMap.status}}</td>
                    </tr>
                    <tr ng-hide="editMode" style="background-color: #50773e; color: #ffffff">
                        <th colspan="4" align="center">Coupons&nbsp;</th>
                    </tr>
                    <tr ng-hide="editMode">
                        <td class="text-uppercase"><b>Code : </b>{{couponDetail.code}}</td>
                        <td class="text-uppercase"><b>Prefix : </b>{{couponDetail.prefix}}</td>
                        <td><b>Max Usage :</b>{{couponDetail.maxUsage}}</td>
                    </tr>
                    <tr ng-hide="editMode">
                        <td><b>Start Date : </b>{{couponDetail.startDate}}</td>
                        <td><b>End Date :</b>{{couponDetail.endDate}}</td>
                        <td><b>Reusable :</b>{{couponDetail.reusable}}</td>
                    </tr>
                    <tr ng-hide="editMode">
                        <td><b>Reusable By Customer :</b>{{couponDetail.reusableByCustomer}}</td>
                    </tr>
                    <tr ng-hide="editMode" style="background-color: #50773e; color: #ffffff">
                        <th colspan="4" align="center">Coupons Mapping&nbsp;</th>
                    </tr>
                    <tr ng-hide="editMode">
                        <th>ID</th>
                        <th>Type</th>
                        <th>Dimension</th>
                    </tr>
                    <tr ng-hide="editMode"
                        ng-repeat='fullCouponsMapping in couponDetail.couponMappingList'>
                        <td>{{fullCouponsMapping.name}}</td>
                        <td>{{fullCouponsMapping.type}}</td>
                        <td>{{fullCouponsMapping.dimension}}</td>
                    </tr>
                    <tr style="background-color: #50773e; color: #ffffff">
                        <th colspan="4" align="center">Offers Mapping&nbsp;</th>
                    </tr>
                    <tr>
                        <th>ID</th>
                        <th>Type</th>
                        <th>Dimension</th>
                    </tr>
                    <tr
                            ng-repeat='fullCouponsMapping in couponDetail.offer.couponMappingList'>
                        <td>{{fullCouponsMapping.name}}</td>
                        <td>{{fullCouponsMapping.type}}</td>
                        <td>{{fullCouponsMapping.dimension}}</td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <button class="btn btn-primary"
                                    type="button" ng-click="prev()">Prev
                            </button>
                            <button class="btn btn-primary" type="button"
                                    ng-hide="editMode"
                                    ng-click="addOffer()">ADD COUPONS
                                MAPPING
                            </button>
                            <button class="btn btn-primary" type="button"
                                    ng-show="editMode && !isCloning"
                                    ng-click="editOffer()">UPDATE
                                COUPONS MAPPING
                            </button>
                            <button class="btn btn-primary" type="button"
                                    ng-show="isCloning"
                                    ng-click="addOffer()">CLONE
                                COUPONS MAPPING
                            </button>
                        </td>
                    </tr>
                </table>
            </uib-tab>
<!--            -->
            <uib-tab index="7" heading="Replicate Coupons" ng-hide="editMode && !isCloning"  disable="tabIndex!=7">
                <div style="font-size: 15px; color: #50773e" align="center"
                     ng-if="resultMessage=='success'">
                    <b>Coupons mapping added successfully!! </b>
                </div>
                <br>
                <div style="font-size: 15px; color: red" align="center"
                     ng-if="resultMessage=='failed'">
                    <b> Coupons mapping failed!! </b>
                </div>
                <div align="right">
                    <button class="btn btn-primary" type="button" data-ng-disabled="isLoyaltyPointsBurnOffer()"
                            ng-click="displayReplicateCoupons()">Need Replicate
                        Coupons
                    </button>
                </div>
                <br>
                <div align="right">
                    <button class="btn btn-primary" type="button"
                            ng-click="submit()">Finish Mapping
                    </button>
                </div>
                <br>
                <div id="replicateCouponsDisplayDiv" style="display: none">
                    <div class="form-group">
                        <label><b> Prefix</b>-</label> {{couponDetail.prefix}} <label><b>
                        Coupons</b> -</label>{{couponDetail.code}} <br>
                        <br>
                        <label> Coupons Count </label>
                        <input type="number"
                               class="form-control" string-to-number
                               ng-model="replicateCount"/>
                    </div>
                    <div align="right">
                        <button class="btn btn-primary" type="button"
                                data-ng-disabled="disableCouponGeneration"
                                ng-click="showRepCoupMode(couponDetail.prefix,couponDetail.code,replicateCount)">Submit
                            Replicate Coupons
                        </button>
                    </div>
                    <div>
                        <button ng-click="exportData()">Export</button>
                        <div id="exportable" style="margin:5px; padding:5px; width: 100%;
                                                    height: 110px; overflow: auto; text-align:justify; ">
                            <table width="100%">
                                <thead>
                                <tr>
                                    <th>Name</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="item in couponsList">
                                    <td>{{item}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </uib-tab>
        </uib-tabset>
    </div>
</div>