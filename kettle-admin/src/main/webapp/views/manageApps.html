<style>
    .panel-group .panel-heading{
        padding:10px 15px;
        cursor:pointer;
    }
    .custom-modal{
        width:90% !important;
    }

</style>
<div data-ng-init="init()" class="form-group">
    <div class="row">
        <div class="col-xs-12">
            <h2>
                Apps Management Console
                <button class="btn pull-right" data-ng-click="getActiveVersion()">Refresh</button>
                <a type="button" class="btn btn-danger pull-right" ui-sref="dashboard.reports">Back</a>
            </h2>
        </div>
    </div>
    <hr>

    <!-- Available Builds view starts here-->
    <div class="row">
        <div class="col-xs-12">
            <h4>
                <span>Active Build</span>
                <button class="btn btn-success pull-right"
                         data-ng-click="pushBuild(activeVersion)"> Push Builds </button>
            </h4>
            <span><strong>Name:</strong> {{activeVersion.name}}</span>
            <br/>
            <span><strong>Upload Time:</strong> {{activeVersion.uploadDate | date : 'dd/MM/yyyy h:mma'}}</span>
            <br/>
            <ul class="list-group" style="margin-top:2rem;">
                <li class="list-group-item" data-ng-repeat="activeApp in activeVersion.apps">
                    <div class="row">
                        <div class="col-xs-4">
                            <span style="line-height: 3rem;">{{activeApp.appName}}</span>
                        </div>
                        <div class="col-xs-8">
                            <div class="btn-group pull-right">
                                <button class="btn"
                                        data-ng-click="addToSelected(activeApp)"
                                        data-ng-class="{
                                    'btn-primary': checkInSelected(activeApp),
                                    'btn-secondary': !checkInSelected(activeApp)
                                }"><i class="fa fa-check"></i></button>
                                <button class="btn"
                                        data-ng-click="removeFromSelected(activeApp)"
                                        data-ng-class="{
                                    'btn-secondary': checkInSelected(activeApp),
                                    'btn-primary': !checkInSelected(activeApp)
                                }"><i class="fa fa-close"></i></button>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <!-- Available Builds view ends here-->

    <!-- Units Assignment View starts here -->
    <div class="row">
        <div class="col-xs-12">
            <h4>
                <input type="checkbox" data-ng-model="all" data-ng-change="checkAll(all)"/> Select Cafes
            </h4>
                <div class="col-xs-12">
                    <ul class="list-group">
                    <li data-ng-repeat="unit in unitList | orderBy :'name'" class="list-group-item">
                        <input type="checkbox" data-ng-model="selectedUnits[unit.id]" />
                        <span>{{unit.name}}</span>
                        <span data-ng-repeat="updatedApp in unit.updated" style="margin-left:2px;" class="label pull-right"
                              data-ng-class="{'label-warning':!updatedApp.current,'label-default':updatedApp.current}">
                            {{updatedApp.name}}
                        </span>
                    </li>
                    </ul>
                </div>
            </div>
        </div>
    <!-- Units Assignment View ends here -->
</div>

<!-- Preview Modal for Activating  Build -->
<script type="text/ng-template" id="buildPreview.html">
    <div class="row" style="margin-top: -3.5rem;">
        <h3>Preview and Submit</h3>
    </div>
    <div class="row" data-ng-init="initPreview()">
        <div class="panel-group">
            <div class="panel panel-default" data-ng-if="app.units.length>0"
                    data-ng-repeat="app in version.apps track by $index">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <div data-toggle="collapse" data-target="#{{app.appName}}">{{app.appName}}</div>
                    </h4>
                </div>
                <div id="{{app.appName}}" class="panel-collapse collapse"
                      data-ng-class="{'in': app.units.length > 0 }">
                    <div class="panel-body" style="max-height:24rem; overflow-y: auto;">
                        <ul class="row list-group">
                            <li class="col-xs-3 list-group-item" data-ng-repeat="unit in app.units">
                                {{unit.name}}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <button class="btn btn-primary pull-right" data-ng-click="submit()">Submit</button>
        <button class="btn btn-secondary pull-right" data-ng-click="cancel()">Cancel</button>
    </div>
</script>