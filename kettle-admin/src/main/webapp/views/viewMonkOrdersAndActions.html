<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">B2B Monk Orders and Actions</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                    data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                    data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'VIEW ORDERS'">
        <div class="row alert alert-info">
            <div class="col-xs-12">
                <h3>B2B Monk Order Details</h3>
                <p>Use this panel to download User and Machine wise B2B Monk Orders</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Customer Name *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <input type="text" ng-model="order.selectedCustomer" placeholder="Customer name"
                            uib-typeahead="customer as customer.customerName for customer in customerDetailList"
                            class="form-control" data-ng-change="getCustomers(order.selectedCustomer)"
                            typeahead-on-select="getUserDetailList(order.selectedCustomer)"
                            typeahead-focus-on-select="false">
                    </div>
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="userDetailList.length > 0">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Location & Machine No *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <select class="form-control" ui-select2="order.selectedUser" id="selectedUser"
                            name="selectedUser" data-placeholder="Select Machine" data-ng-model="order.selectedUser"
                            data-ng-options="user as user.machineLocation.line2 + ', ' + user.machineLocation.city + ' - ' + user.machineSerialNo for user in userDetailList track by user"
                            data-ng-change="selectUserAndMachineForOrders()"></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="userDetailList.length > 0">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Date Range *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <div class="datepicker" data-date-format="yyyy-MM-dd" style="width: 49%; margin-right: 2%;">
                            <input type="text" class="form-control" ng-model="order.startDate"
                                placeholder="yyyy-MM-dd" />
                        </div>
                        <div class="datepicker" data-date-format="yyyy-MM-dd" style="width: 49%;">
                            <input type="text" class="form-control" ng-model="order.endDate" placeholder="yyyy-MM-dd" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-6 form-group">
                        <button class="btn btn-primary" data-toggle="modal" ng-click="downloadOrderDetails()"
                            data-ng-disabled="!checkOrderDetailObj()">
                            <i class="fa fa-download fw"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'VIEW ACTIONS'">
        <div class="row alert alert-info">
            <div class="col-xs-12">
                <h3>B2B Monk Action Details</h3>
                <p>Use this panel to download User and Machine wise B2B Monk Actions</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Customer Name *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <input type="text" ng-model="action.selectedCustomer" placeholder="Customer name"
                            uib-typeahead="customer as customer.customerName for customer in customerDetailList"
                            class="form-control" data-ng-change="getCustomers(action.selectedCustomer)"
                            typeahead-on-select="getUserDetailList(action.selectedCustomer)"
                            typeahead-focus-on-select="false">
                    </div>
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="userDetailList.length > 0">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Location & Machine No *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <select class="form-control" ui-select2="action.selectedUser" id="selectedUser"
                            name="selectedUser" data-placeholder="Select Machine" data-ng-model="action.selectedUser"
                            data-ng-options="user as user.machineLocation.line2 + ', ' + user.machineLocation.city + ' - ' + user.machineSerialNo for user in userDetailList track by user"
                            data-ng-change="selectUserAndMachineForActions()"></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="userDetailList.length > 0">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Date Range*</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <div class="datepicker" data-date-format="yyyy-MM-dd" style="width: 49%; margin-right: 2%;">
                            <input type="text" class="form-control" ng-model="action.startDate"
                                placeholder="yyyy-MM-dd" />
                        </div>
                        <div class="datepicker" data-date-format="yyyy-MM-dd" style="width: 49%;">
                            <input type="text" class="form-control" ng-model="action.endDate"
                                placeholder="yyyy-MM-dd" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-6 form-group">
                        <button class="btn btn-primary" data-toggle="modal" ng-click="downloadActionDetails()"
                            data-ng-disabled="!checkActionDetailObj()">
                            <i class="fa fa-download fw"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row" data-ng-if="selectedAction == 'VIEW ERRORS'">
        <div class="row alert alert-info">
            <div class="col-xs-12">
                <h3>B2B Monk Order Details</h3>
                <p>Use this panel to download User and Machine wise B2B Monk Error Report</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Customer Name *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <input type="text" ng-model="error.selectedCustomer" placeholder="Customer name"
                            uib-typeahead="customer as customer.customerName for customer in customerDetailList"
                            class="form-control" data-ng-change="getCustomers(error.selectedCustomer)"
                            typeahead-on-select="getUserDetailList(error.selectedCustomer)"
                            typeahead-focus-on-select="false">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12" data-ng-if="userDetailList.length > 0">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Location & Machine No *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <select class="form-control" ui-select2="error.selectedUser" id="selectedUser"
                            name="selectedUser" data-placeholder="Select Machine" data-ng-model="error.selectedUser"
                            data-ng-options="user as user.machineLocation.line2 + ', ' + user.machineLocation.city + ' - ' + user.machineSerialNo for user in userDetailList track by user"
                            data-ng-change="selectUserAndMachineForErrors()"></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12" data-ng-if="userDetailList.length > 0">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Date Range *</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <div class="datepicker" data-date-format="yyyy-MM-dd" style="width: 49%; margin-right: 2%;">
                            <input type="text" class="form-control" ng-model="error.startDate"
                                placeholder="yyyy-MM-dd" />
                        </div>
                        <div class="datepicker" data-date-format="yyyy-MM-dd" style="width: 49%;">
                            <input type="text" class="form-control" ng-model="error.endDate" placeholder="yyyy-MM-dd" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-6 form-group">
                        <button class="btn btn-primary" data-toggle="modal" ng-click="downloadErrorDetails()"
                            data-ng-disabled="!checkErrorDetailObj()">
                            <i class="fa fa-download fw"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>