<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Payment Mode Method </h1>
    </div>
    <div class="form-group">
        <label>Mode Name*</label>
        <input type="text"
               pattern=".*\S+.*"
               class="form-control"
               ng-model="modeName"
        required>
    </div>
    <div class="form-group">
        <label>Mode Type*</label>
        <select class="form-control"
                ng-model="modeType"
                required>
            <option ng-repeat="modeType in modeTypeList | orderBy" value="{{modeType}}">
                {{modeType}}
            </option>
        </select>
    </div>
    <div ng-show="modeType == 'OTHERS'" class="form-group">
        <label>Specify Mode Type*</label>
        <input type="text" class="form-control" ng-model="otherModeType" />
    </div>
    <div class="form-group">
        <label>Mode Description*</label>
        <input type="text"
               pattern=".*\S+.*"
               class="form-control"
               ng-model="modeDescription"
        />
    </div>
    <div class="form-group">
        <label>Settlement Type*</label>
        <select class="form-control"
                ng-model="settlementType"
                required>
            <option ng-repeat="settlementType in settlementTypeList | orderBy" value="{{settlementType}}">
                {{settlementType}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <label>Generate Pull*</label>
        <select class="form-control"
                ng-model="generatePull"
        required>
            <option ng-repeat="generatePull in generatePullList | orderBy" value="{{generatePull}}">
                {{generatePull}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <label>Commission Rate*</label>
        <input type="number"
               step="0.01"
               pattern=".*\S+.*"
               min = "0.00"
               class="form-control"
               ng-model="commissionRate"
               ng-change=""
        required>
    </div>
    <div class="form-group">
        <label>Mode Status*</label>
        <select class="form-control"
                ng-model="modeStatus"
        required>
            <option ng-repeat="modeStatus in modeStatusList | orderBy" value="{{modeStatus}}">
                {{modeStatus}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <label>Mode Category*</label>
        <select class="form-control"
                ng-model="modeCategory"
        required>
            <option ng-repeat="modeCategory in modeCategoryList | orderBy" value="{{modeCategory}}">
                {{modeCategory}}
            </option>
        </select>
    </div>
    <div style="width:100%;">
        <div style ="width:50%; float:left" class="form-group">
            <label>Automatic Pull Validate*</label>
            <select class="form-control"
                    ng-model="automaticPullValidate"
            required>
                <option ng-repeat="automaticPullValidate in automaticPullValidateList | orderBy" value="{{automaticPullValidate}}">
                    {{automaticPullValidate}}
                </option>
            </select>
        </div>
        <div style="margin-left: 50%" class="form-group">
            <label>Automatic Transfer*</label>
            <select class="form-control"
                    ng-model="automaticTransfer"
            required>
                <option ng-repeat="automaticTransfer in automaticTransferList | orderBy" value="{{automaticTransfer}}">
                    {{automaticTransfer}}
                </option>
            </select>
        </div>
    </div>
    <div style="width:100%;">
        <div style ="width:50%; float:left" class="form-group">
                    <label>Automatic Close Transfer*</label>
                        <select
                             class="form-control"
                             ng-model="automaticCloseTransfer"
                        required>
            <option ng-repeat="automaticCloseTransfer in automaticCloseTransferList | orderBy" value="{{automaticCloseTransfer}}">
                {{automaticCloseTransfer}}
            </option>
        </select>
    </div>
    <div style="margin-left: 50%" class="form-group">
        <label>Editable*</label>
        <select
                class="form-control"
                ng-model="editable"
                required>
            <option ng-repeat="editable in editableList | orderBy" value="{{editable}}">
                {{editable}}
            </option>
        </select>
        </div>
    </div>
    <div style="width:100%;">
        <div style ="width:50%; float:left" class="form-group">
            <label>Applicable On Discounted Orders*</label>
            <select
                    class="form-control"
                    ng-model="applicableOnDiscountedOrders"
                    required>
                <option ng-repeat="applicableOnDiscountedOrders in applicableOnDiscountedOrdersList | orderBy" value="{{applicableOnDiscountedOrders}}">
                    {{applicableOnDiscountedOrders}}
                </option>
            </select>
        </div>
        <div style="margin-left: 50%" class="form-group">
            <label>Needs Settlement Slip*</label>
            <select class="form-control"
                    ng-model="needsSettlementSlip"
                    required>
                <option ng-repeat="needsSettlementSlip in needsSettlementSlipList | orderBy" value="{{needsSettlementSlip}}">
                    {{needsSettlementSlip}}
                </option>
            </select>
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label>Needs Settlement Slip*</label>-->
<!--        <select class="form-control"-->
<!--                ng-model="needsSettlementSlip"-->
<!--                required>-->
<!--            <option ng-repeat="needsSettlementSlip in needsSettlementSlipList | orderBy" value="{{needsSettlementSlip}}">-->
<!--                {{needsSettlementSlip}}-->
<!--            </option>-->
<!--        </select>-->
<!--    </div>-->
    <div class="form-group">
        <label>Validation Source*</label>
        <input type="text"
               class="form-control"
               ng-model="validationSource"
        required>

    </div>


    <div class="form-group">
        <label>Ledger Name*</label>
        <input type="text"
               pattern=".*\S+.*"
               class="form-control"
               ng-model="ledgerName"
               required>
    </div>

    <div class="form-group">
        <button style="margin: 10px" class="btn btn-primary pull-right"
                ng-click="savePaymentMethod()">Add Payment Method
        </button>
    </div>
</div>
