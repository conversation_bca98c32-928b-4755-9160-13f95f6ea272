<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
-->
<style>
    .created {
        background-color: blue;
        padding: 5px 10px;
        font-weight: bold;
        text-align: center;
        border-radius: 5px;
        color: white;
    }
    .processing {
        background-color: darkorange;
        padding: 5px 10px;
        font-weight: bold;
        text-align: center;
        border-radius: 5px;
        color: white;
    }
    .processed {
        background-color: green;
        padding: 5px 10px;
        font-weight: bold;
        text-align: center;
        border-radius: 5px;
        color: white;
    }
    .failed {
        background-color: darkred;
        padding: 5px 10px;
        font-weight: bold;
        text-align: center;
        border-radius: 5px;
        color: white;
    }
    .disable {
        cursor: not-allowed !important;
    }
    .checklist-actions {
        position: fixed;
        z-index: 10;
        bottom: 0;
        right: 0; 
        margin-right: 40px;
        margin-bottom: 20px;
        padding: 10px 15px;
        background-color: white;
        border-radius: 10px;
    }
</style>

<div ng-init="init()" style="margin: 0px 20px;">
    <div class="row">
        <h2 class="page-header" style="display: flex; justify-content: space-between;">
            <p>Product Checklist</p>
            <div class="row checklist-actions">
                <button class="btn btn-primary" style="cursor: pointer; height: 70%;"
                    id="clearCheckList" ng-click="clearCheckList()">
                    <i class="fa fa-eraser"></i>Clear Checklist
                </button>
                <button class="btn btn-success" ng-class="{'disable': checkListEvent.status === 'PROCESSING'}" style="cursor: pointer; height: 70%;"
                    id="postCheckListBtn" ng-click="postCheckList()" ng-disabled="checkListEvent.status === 'PROCESSING'">
                    <i class="fa fa-location-arrow"></i>Post Checklist
                </button>
            </div>
        </h2>
    </div>

    <div class="row" style="display: flex;">
        <div style="width: 60%; display: flex; justify-content: space-between; background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin-bottom: 20px; margin-right: 40px;">
            <button class="btn btn-danger" style="cursor: pointer; height: 35px; margin-right: 20px;" 
                    id="clearFilter" ng-click="clearFilter()">
                    <i class="fa fa-remove"></i>Remove Filter
            </button>
            <p style="margin-right: 20px; width: 20%">Filter Unit(s):</p>
            <div style="width: 70%;">
                <div class="col-lg-4 form-group" style="width: 50%;">
                    <p>Region</p>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForRegion"
                        options="trimmedRegions" selected-model="selectedRegions"
                        events="{ onItemSelect: onRegionChange, onItemDeselect: onRegionChange }">
                    </div>
                </div>
                <div class="col-lg-4 form-group" style="width: 50%;">
                    <p>City</p>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForCities"
                        options="filteredCities" selected-model="selectedCities"
                        events="{ onItemSelect: onCityChange, onItemDeselect: onCityChange }">
                    </div>
                </div>
            </div>
        </div>
        <div class="row" style="width: 40%; display: flex; justify-content: space-between; background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin-bottom: 20px;"
            ng-hide="!checkListEvent">
            <button class="btn btn-primary" style="cursor: pointer; height: 35px; margin-right: 20px;"
                id="refreshCheckList" ng-click="getCheckList()">
                <i class="fa fa-refresh"></i>Refresh
            </button>
            <div class="col-lg-4 form-group">
                <p>Checklist Status</p>
                <p ng-class="{
                        'created': checkListEvent.status === 'CREATED',
                        'processing': checkListEvent.status === 'PROCESSING',
                        'processed': checkListEvent.status === 'PROCESSED',
                        'failed': checkListEvent.status === 'FAILED'
                    }">{{checkListEvent.status}}
                </p>
            </div>
            <div class="col-lg-4 form-group">
                <button class="btn btn-secondary" style="cursor: pointer; height: 70%;"
                    id="downloadCheckList" ng-click="downloadCheckList()" ng-if="checkListEvent.status === 'PROCESSED'">
                    <i class="fa fa-download"></i>Download
                </button>
                <p ng-if="checkListEvent.status === 'PROCESSING'" style="font-size: 10px;">
                    *Please wait while we generate the sheet. Refresh to get updated status.
                </p>
            </div>
        </div>
    </div>

    <div class="row alert alert-info" style="margin-bottom: 20px;">
        <h4><b>Add Product Here</b></h4>
        <div class="row" style="display: flex; justify-content: space-between;">
            <div class="col-lg-4" style="width: 20%;">
                <p>Brand ID</p>
                <select class="form-control" ng-model="newItem.brandId"
                        ng-options="brand.brandId as (brand.brandId + ' - ' + brand.brandName) for brand in brands"
                        ng-change="onChangeBrandId()" style="cursor: pointer;">
                    <option value="">Select Brand</option>
                </select>
            </div>
            <div class="col-lg-4">
                <p>Product</p>
                <select data-ui-select2 class="form-control" data-ng-model="newItem.product"
                        data-ng-options="product as (product.id + ' - ' + product.name + ' - ' + product.status) for product in filteredProductListBrandWise" 
                        data-ng-change="onChangeProductName(newItem.product)"
                        data-placeholder="Select Product"
                        style="width: 100%;">
                </select>
            </div>
            <!-- <div class="col-lg-4" style="width: 20%;">
                <p>Dimension</p>
                <select class="form-control" ng-model="newItem.dimension" 
                        ng-options="dimension as dimension.name for dimension in dimensionsForProduct"
                        ng-change="onChangeDimension()" style="cursor: pointer;">
                    <option value="">Select Dimension</option>
                </select>
            </div> -->
            <div class="col-lg-4 form-group" style="width: 20%; cursor: pointer;">
                <p>Dimension(s)</p>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForUnits"
                     options="dimensionsForProduct" selected-model="newItem.dimensions">
                </div>
            </div>
            <div class="col-lg-4 form-group" style="width: 20%; cursor: pointer;">
                <p>Unit(s)</p>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForUnits"
                     options="filteredUnits" selected-model="newItem.units">
                </div>
            </div>
            <div class="col-lg-4 form-group" style="width: 20%;">
                <p>Live Date</p>
                <input
                    class="form-control"
                    type="date"
                    ng-model="newItem.liveDate" min="{{currentDate}}"
                    style="cursor: pointer;" />
            </div>
            <div class="col-lg-4 form-group" style="width: 15%;">
                <button class="btn btn-primary" style="width: 100%; cursor: pointer;" 
                        id="addCheckListBtn" ng-click="addCheckListItem(newItem)">
                    <i class="fa fa-plus fw"></i>&nbsp;Add Item
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <table class="table table-striped table-bordered">
            <thead valign="top">
                <th style="width: 5%;">ID</th>
                <th style="width: 25%;">Product</th>
                <th style="width: 15%;">Dimension(s)</th>
                <th style="width: 20%;">Unit(s)</th>
                <th style="width: 10%;">Live Date</th>
                <th style="width: 20%;">Action</th>
            </thead>
            <tbody>
                <tr ng-repeat="item in checklistItems">
                    <td style="width: 5%;">{{item.product.id}}</td>
                    <td style="width: 25%;">{{item.product.name}}</td>
                    <!-- <td style="width: 15%;">{{item.dimension.name}}</td> -->
                    <td style="width: 20%;">
                        <button class="btn btn-primary" ng-click="showDimensionsModal(item)">{{item.dimensions.length}}</button>
                    </td>
                    <td style="width: 20%;">
                        <button class="btn btn-primary" ng-click="showUnitsModal(item)">{{item.units.length}}</button>
                    </td>
                    <td style="width: 10%;">{{item.liveDate | date:'dd-MM-yyyy'}}</td>
                    <td style="width: 20%;">
                        <button class="btn btn-danger" ng-click="removeCheckListItem($index)">Remove</button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<div class="modal fade" id="unitsModal" tabindex="-1" role="dialog" aria-labelledby="unitsModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="unitsModalLabel">Unit(s) for {{selectedItem.product.name}}</h4>
            </div>
            <div class="modal-body">
                <ul class="list-group">
                    <li class="list-group-item" ng-repeat="unit in selectedItem.units">{{unit.id}} - {{unit.name}}</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="dimensionsModal" tabindex="-1" role="dialog" aria-labelledby="dimensionsModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="dimensionsModalLabel">Dimension(s) for {{selectedItem.product.name}}</h4>
            </div>
            <div class="modal-body">
                <ul class="list-group">
                    <li class="list-group-item" ng-repeat="dimension in selectedItem.dimensions">{{dimension.id}} - {{dimension.code}}</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>