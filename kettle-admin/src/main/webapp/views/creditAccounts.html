<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	
        <h1 class="page-header">
        	{{action}} Credit Account
            <button class="btn btn-primary pull-right" data-toggle="modal" ng-click="addCreditAccounts()" ><i class="fa fa-plus fw"></i> Add Credit Accounts</button>
        </h1>
        
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
    </div>
</div>

<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ creditAccounts.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered" style="font-size:10px">
                    <thead>
                         <th>ID &nbsp;<a ng-click="sort_by('id');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th align="center">Legal Name&nbsp;<a ng-click="sort_by('legalName');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th align="center">Display Name&nbsp;<a ng-click="sort_by('displayName');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th align="center">Pan Number &nbsp;</th>
                         <th align="center">Tan Number&nbsp;</th>
                         <th>Account Contact Person&nbsp;</th>
                         <th>Account Person Email&nbsp;</th>
                          <th>Account Person Phone&nbsp;</th>
                        
                         <th align="center">Address&nbsp;</th>
                         <th align="center">Bank Detail&nbsp;</th>
                         <th align="center">Certificate of Incorporation&nbsp;</th>
                         <th align="center">Company Contact&nbsp;</th>
                         <th align="center">Contact Person&nbsp;</th>
                         <th align="center">Contact Person Email&nbsp;</th>
                         <th align="center">Credit Days&nbsp;</th>
                          <th>Account Status&nbsp;</th>
                          <th colspan="2" align="center">Action</th>
                    </thead>
                <tbody>
                    <tr ng-repeat="creditAccountsDetails in filtered = (creditAccounts | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                        <td>{{creditAccountsDetails.creditAccountDetailId}}</td>
                        <td>{{creditAccountsDetails.legalName}}</td>
                        <td>{{creditAccountsDetails.displayName}}</td>
                        <td>{{creditAccountsDetails.panNumber}}</td>
                        <td>{{creditAccountsDetails.tanNumber}}</td>
                        <td>{{creditAccountsDetails.accountContactPerson}}</td>
                        <td>{{creditAccountsDetails.accountContactPersonEmail}}</td>
                        <td>{{creditAccountsDetails.accountContactPersonNumber}}</td>
                        <td>{{creditAccountsDetails.address}}</td>
                        <td>{{creditAccountsDetails.bankDetail}}</td>
                        <td>{{creditAccountsDetails.certificateOfIncorporation}}</td>
                        <td>{{creditAccountsDetails.companyContact}}</td>
                        <td>{{creditAccountsDetails.contactPerson}}</td>
                        <td>{{creditAccountsDetails.contactPersonEmail}}</td>
                        <td>{{creditAccountsDetails.creditDays}}</td>
                        <td>{{creditAccountsDetails.accountStatus}}</td>
                        
                         <td> 
		                   <img ng-click="changeStatusAccountList(creditAccounts,'IN_ACTIVE',creditAccountsDetails.creditAccountDetailId)" ng-if="creditAccountsDetails.accountStatus!='IN_ACTIVE'" style="margin-bottom:8px; cursor:pointer" title="In_Active" ng-src="img/activeCat.png" height="25px" width="25px">&nbsp;&nbsp;
		          			<img ng-click="changeStatusAccountList(creditAccounts,'ACTIVE',creditAccountsDetails.creditAccountDetailId)" ng-if="creditAccountsDetails.accountStatus!='ACTIVE'" style="margin-bottom:8px; cursor:pointer" title="Active" ng-src="img/inactiveCat.png" height="25px" width="25px">&nbsp;&nbsp;
          				 </td>
                        
                        <td><button class="btn btn-primary pull-right" data-toggle="modal" ng-click="updateCreditAccounts(creditAccounts,creditAccountsDetails.creditAccountDetailId)"> UPDATE</button></td>
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>



<!-- Modal -->
<div class="modal fade" id="creditAccountsModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"> Credit Accounts</h4>
          </div>
          <div class="modal-body">
              <form name="addCreditAccountForm" novalidate>
              <div class="form-group">
                  <label>Legal Name *</label>
                  <input type="text" class="form-control" ng-model="legalName" required />
              </div>
              
              
               <div class="form-group">
                  <label>Display  Name </label>
                  <input type="text" class="form-control" ng-model="displayName" required />
              </div>
              
              
              <div class="form-group">
                  <label>Address </label>
                  <input type="text" class="form-control" ng-model="address" required />
              </div>
              
               <div class="form-group">
                  <label>TAN No </label>
                  <input type="text" class="form-control" ng-model="tanNo" required />
              </div>
              
              
              <div class="form-group">
                  <label>PAN No </label>
                  <input type="text" class="form-control" ng-model="panNo" required />
              </div>
              
               
              <div class="form-group">
                  <label>COI </label>
                  <input type="text" class="form-control" ng-model="coi" required />
              </div>
              
              <div class="form-group">
                  <label>Bank Detail </label>
                  <input type="text" class="form-control" ng-model="bankDetail" required />
              </div>
              
              <div class="form-group">
                  <label>Name of Contact Person * </label>
                  <input type="text" class="form-control" ng-model="contactPerson" required />
              </div>
              
                 <div class="form-group">
                  <label>Phone No of Contact Person *</label>
                  <input type="number" class="form-control" my-maxlength="10" string-to-number ng-model="contactPersonPhone" required />
              </div>
              
              <div class="form-group">
                  <label>Contact Person Email</label>
                  <input type="text" class="form-control" ng-model="contactPersonEmail" required />
              </div>
              
               <div class="form-group">
                  <label>Account Person * </label>
                  <input type="text" class="form-control" ng-model="accountPerson" required />
              </div>
              
              
              <div class="form-group">
                  <label>Account Person Phone No * </label>
                  <input type="number" class="form-control" my-maxlength="10" string-to-number ng-model="accountPersonPhone" required />
              </div>
              
              <div class="form-group">
                  <label>Account Person Email</label>
                  <input type="text" class="form-control" ng-model="accountPersonEmail" required />
              </div>
              
              
              <div class="form-group">
                  <label>Credit Days * </label>
                  <input type="number" class="form-control" my-maxlength="11" string-to-number ng-model="creditDays">
              </div>
              
              <div class="form-group">
                  <label>Concern Person in Chaayos *</label>
                  <input type="text" class="form-control" ng-model="concernPersonChaayos">
              </div>
              
               <div class="form-group clearfix">
                  <button class="btn btn-primary pull-right"  ng-if="action=='Add'"  ng-click="addCreditAccountsData()">Add Credit Accounts</button>
              </div>
            
               <div class="form-group clearfix" ng-if="action=='Update'">
                  <button class="btn btn-primary pull-right" ng-click="submitUpdateCreditAccounts(creditAccountDetailId)">Update Credit Accounts</button>
              </div>
              </form>
          </div>
     </div>
  </div>
</div>


    