<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    table th, td {
        border: #ccc 1px solid;
    }

    .list.selected {
        background: #1ca62c;
        color: #fff;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Channel Partner History</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-6">
            <label>Unit</label>
            <select ui-select2 class="form-control"
                    style="width: 100% !important" data-ng-model="selectedUnit"
                    data-placeholder="Select a Unit"
                    data-ng-options="u as u.name for u in units">
            </select>
        </div>
        <div class="col-xs-4">
            <label>Partner Name</label>
            <select class="form-control"
                    data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                    data-ng-model="selectedPartner">
            </select>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-4">
            <label>Brand Name</label>
            <select class="form-control"
                    data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                    data-ng-model="selectedBrand">
            </select>
        </div>

        <div class="col-xs-4">
            <button class="btn btn-primary" style="margin-top: 20px;"
                    data-ng-click="getPartnerHistory()"
                    data-ng-disabled="selectedPartner == null || selectedBrand == null || selectedUnit == null">
                Get Partner History
            </button>
        </div>
    </div>
    <div style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
    </div>
    <div class="row">
        <div class="col-xs-7">
            <table class="table table-bordered"
                   style="background: #fff;"
                   data-ng-if="partnerHistory != null && partnerHistory.length > 0">
                <thead>
                <tr>
                    <th bgcolor="#A6F7E1">Last Updated By</th>
                    <th bgcolor="#A6F7E1">For Unit</th>
                    <th bgcolor="#A6F7E1">Status</th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="group in partnerHistory">
                    <td>{{group.lastUpdatedTime}}</td>
                    <td>{{group.name}}</td>
                    <td ng-if="group.partnerStatus==false">IN_ACTIVE</td>
                    <td ng-if="group.partnerStatus==true">ACTIVE</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>