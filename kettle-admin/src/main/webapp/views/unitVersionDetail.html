<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Tea house Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Tea house Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Tea house Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div ng-init="init()">
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">
                Unit Version Details
            </h1>
        </div>
    </div>

    <div>
        <div class="row">
            <div class="col-xs-1" style="font-size: medium; margin-top: 4px">
                <label class="control-label">Regions</label>
            </div>
            <div class="col-xs-4 region-card" ng-dropdown-multiselect=""
                 options="regionList"
                 selected-model="selectedRegionList" extra-settings="multiSelectSettings">
            </div>
            <button data-ng-click="getUnitDetails()" class="btn btn-primary">Get Units</button>
        </div>
        <br>
        <div class="row">
            <div class="col-xs-1" style="font-size: medium; margin-top: 4px">
                <label class="control-label">Unit Type</label>
            </div>
            <div class="col-xs-4 region-card" ng-dropdown-multiselect=""
                 options="unitCategoryList"
                 selected-model="selectedUnitCategory" extra-settings="multiSelectSettings">
            </div>
        </div><br/>
        <div class="row">
            <div class="col-xs-1" style="font-size: medium; margin-top: 4px">
                <label class="control-label">Application Name</label>
            </div>
            <div class="col-xs-4 region-card" ng-dropdown-multiselect=""
                 options="appNameList"
                 selected-model="selectedAppNameList" extra-settings="multiSelectSettings">
            </div>
        </div><br/>
        <div class="row">
            <div class="col-xs-1" style="font-size: medium; margin-top: 4px">
                <label class="control-label">Filter</label>
            </div>
            <div class="col-xs-4">
                <input type="text" ng-model="unitFilter" ng-change="filter() "
                       placeholder="Filter" class="form-control"/>
            </div>
            <div class="col-xs-15" data-ng-if="unitDetailList.length > 0">
                <h4>Total Units : {{unitDetailList.length}}</h4>
            </div>
        </div>
        <br>

        <div class="row container"
             data-ng-if="unitDetailList != null && unitDetailList.length > 0">
            <div class="col-xs-12 text-container">
                <table class="table table-bordered">
                    <thead style="background-color: #50773e; color: #ffffff">
                    <tr>
                        <th>Unit Id&nbsp;</th>
                        <th>Unit Name&nbsp;</th>
                        <th>Application Version Details&nbsp;<br/>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="detail in unitDetailList| filter : unitFilter | orderBy : 'id'"
                        data-ng-class="{'row-selected': unitDetailList[detail.id].checked}">
                        <td>{{detail.id}}</td>
                        <td>{{detail.name}}</td>
                        <td>
                            <table class="table table-bordered" >
                                <thead>
                                <th>App Name</th>
                                <th>Active Unit Version</th>
<!--                                <th>Action</th>-->
                                </thead>
                                <tbody data-ng-repeat="version in unitVersionList[detail.id]" >
                                <tr>
                                    <td>{{version.applicationName}}</td>
                                    <td>{{version.applicationVersion}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table><br/><br/><br/><br/>
            </div>
        </div>

    </div>


</div>