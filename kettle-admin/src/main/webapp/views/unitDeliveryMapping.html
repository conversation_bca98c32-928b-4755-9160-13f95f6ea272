
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	 <h1 class="page-header"> Unit Delivery Mapping    </h1>
    </div>
<div class="col-xs-12">
	<div class="form-group">                                          
		<label>Type *</label>
		<select class="form-control" ng-model="newUnitFamily" ng-options="familyListData as familyListData for familyListData in families track by familyListData" ng-change="showUnitFamilyDetails(newUnitFamily)" > </select>
	</div>
	
	<div class="form-group">                                          
		<label>Unit *</label>
		<select class="form-control" ng-model="selectedUnitData" ng-options="unitDetailList.name as unitDetailList.name for unitDetailList in unitlist track by unitDetailList.id"> </select>
	</div>
	
	<div class="col-lg-12"><br>
		<div style align="right"><button class="btn btn-primary pull-right" ng-click="showUnitDeliveryDetails()">SUBMIT</button></div>
	</div>


<div id="viewDeliveryPartnerListDiv" style="display:none">
<style>
.pending-delete { background-color: #CCC }
</style>
<table class="table table-bordered" ng-style="{color: myColor}">
                    <thead style="background-color:#50773e; color:#ffffff">
                       <th>Check &nbsp;   <input type="checkbox" ng-model='allCheckedDelivery' style="width:20px; height:20px" ng-true-value="'YES'" ng-false-value="'NO'"  ng-click="checkAllDelivery()">  </th> 
                        <th>Delivery Name&nbsp;</th>
                        <th>Status&nbsp;</th>
                        <th>Priority&nbsp;</th>
        			</thead>
               <tbody>
             <tr ng-repeat='deliveryDetailUnit in unitDeliveryDetail' >
             		 <td width="10%">
             		 <input type="checkbox"  ng-model='delivery[deliveryDetailUnit.detail.id].checked' ng-click="checkDelivery(deliveryDetailUnit.detail.id,deliveryDetailUnit.mappingId,deliveryDetailUnit.priority,deliveryDetailUnit.detail)"></td>
             		 <td width="10%">{{deliveryDetailUnit.detail.name}}	   </td>
                     <td width="10%">{{deliveryDetailUnit.detail.status}}	</td>
                     <td width="40%">
                     	<span>{{deliveryDetailUnit.priority}}</span> 
                   		<span><input class="form-control" width="50px" string-to-number type="number" ng-model="priority[deliveryDetailUnit.mappingId]" ng-change="changePriorityValue(deliveryDetailUnit.detail.id,deliveryDetailUnit.mappingId,deliveryDetailUnit.priority,deliveryDetailUnit.detail,deliveryDetailUnit)"> </span>
                     </td>
              </tr>
              <tr><td colspan="14" align="right"><div class="form-group clearfix">
                  <button class="btn btn-primary pull-right" ng-click="submitDeliveryDetails()">Submit</button>
              </div></td></tr>
                </tbody>
 </table>
 </div>
 
 
 
 
 </div>
</div>
       
<div class="modal fade" id="deliveryUpdatedModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content" style="font-size:10px;width: 820px;">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"> Summary: Unit- Delivery Detail  </h4>
          </div>
          <div class="modal-body">
          <div  ng-if="deliveryModifyList.length > 0">
              <table class="table table-striped table-bordered">
                    <thead>
                    <th>S.No</th>
                        <th>Delivery Name&nbsp;</th>
                        <th>Old Priority</th>
                        <th>Change Priority</th>
                        <th>Status&nbsp;</th>
                        
                    </thead>
                <tbody>
                
                    <tr ng-repeat='viewDeliveryDetailsList in deliveryModifyList'> 
                   		 <th>{{$index+1}}</th>
                        <th>{{viewDeliveryDetailsList.detail.name}}</th>
                        <td>{{viewDeliveryDetailsList.priority}}</td>
                        <td>{{viewDeliveryDetailsList.priority}}</td>
                        <td>{{viewDeliveryDetailsList.detail.status}}</td>
                    </tr> 
                    <tr><td colspan="14" align="right">
                    <div class="form-group clearfix">
                  <button class="btn btn-primary pull-right" ng-click="SubmitUnitDelivery()">Submit</button></div></td></tr>
             
                </tbody>
                </table>
          </div>
          
          <div class="col-lg-10" style="margin-top:-22px"ng-if="deliveryModifyList.length == 0">
                <h4>No results found</h4>
                <br></br>
            </div></div>
     </div>
  </div>
</div> 

