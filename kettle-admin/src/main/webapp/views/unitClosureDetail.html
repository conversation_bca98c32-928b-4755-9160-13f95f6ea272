<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Tea house Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Tea house Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Tea house Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

  <style>
    .statusloader {
  border: 10px solid #f3f3f3;
  border-radius: 50%;
  border-top: 10px solid #3498db;
  width: 20px;
  height: 20px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
  </style>

<div ng-init="init()">
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">
                Unit Closure Details
            </h1>
        </div>
    </div>

    <div>
        <div class="row">

            <div class="col-lg-3 ">
                <label>Select Closure Status</label>
            </div>
            <div class="col-lg-4">
                <select ng-model="selectedClosureStatus" class='form-control'
                        data-ng-placeholder="select unit which you want to clone"
                        ng-options="status for status in statusList">
                </select>
            </div>
            <button data-ng-click="getUnitDetails()" class="btn btn-primary">Get Units</button>
        </div>
        <br>

        <div class="row container"
             data-ng-if="unitDetailList != null && unitDetailList.length > 0">
            <div class="col-xs-12 text-container">
                <table class="table table-bordered">
                    <thead style="background-color: #50773e; color: #ffffff">
                    <tr>
                        <th>Unit Id&nbsp;</th>
                        <th>Unit Name&nbsp;</th>
                        <th>Closure Comment&nbsp;<br/>
                        <th>Closure Status<br/>
                        <th>Operation Stop Date</th>    
                        <th>Started By</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat-start="detail in unitDetailList| filter : unitFilter | orderBy : 'unitId'"
                        data-ng-click="setSelectedRow(detail)" style="cursor: pointer;"
                        data-ng-class="{'rowSelected':detail.unitId===idSelected}"
                    >
                        <td>{{detail.unitId}}</td>
                        <td>{{detail.unitName}}</td>
                        <td>{{detail.message}}</td>
                        <td>{{detail.closureStatus}}</td>
                        <td>{{detail.operationStopDate | date : 'yyyy-MM-dd' }}</td>
                        <td>{{detail.createdBy}}</td>
                    </tr>
                    <tr ng-repeat-end=""
                        ng-show="detail.unitId==idSelected && groupSelected">
                        <td colspan="6">
                           
                            <div data-ng-if="!showUnitClosureForm">
                                <table class="table table-bordered">
                                <thead style="background-color: #50773e; color: #ffffff">
                                <tr>
                                    <th>Task Id&nbsp;</th>
                                    <th>Task Name&nbsp;</th>
                                    <th>Task Description&nbsp;<br/>
                                    <th>Task Owner<br/>
                                    <th>Valid Status</th>
                                </tr>
                                </thead>
                                <tr ng-repeat="state in stateList"
                                    data-ng-class="{'rowSelected':true}">
                                    <td>{{state.stateId}}</td>
                                    <td>{{state.stateName}}</td>
                                    <td>{{state.stateDescription}}</td>
                                    <td>{{state.stateOwner}}</td>
                                    <td>
                                       
                                        <div data-ng-if="unitStateStatusLoaderFlag[detail.unitId][state.stateId] === true" class="statusloader"></div>
                                        <span data-ng-if="validation[detail.unitId][state.stateId]">
                                            &#x2705;
                                        </span>
                                        <span data-ng-if="unitStateStatusLoaderFlag[detail.unitId][state.stateId] === false && validation !==null && validation[detail.unitId][state.stateId]!=null && !validation[detail.unitId][state.stateId]">
                                            &#x274c;
                                        </span>
                                    </td>
                                </tr>
                                </table>

                                <div data-ng-if="!checkNextStatus(detail)">
                                    <h4 style="color: red; font-weight: bold;">Task Failure Output : </h4>
                                    <div
                                    class="grid"
                                    id="grid"
                                    data-ui-grid="gridOptions"
                                    ui-grid-save-state
                                      ui-grid-edit
                                      ui-grid-exporter
                                      ui-grid-resize-columns
                                       ui-grid-move-columns
                                      ui-grid-auto-resize>
                                    
            
                            </div>
                                </div>
                            
                            <button data-ng-if="checkNextStatus(detail)"   style="float : right;" class="btn  btn-primary" data-ng-click="proceedUnitClosure(detail)"> Next </button>
                        </div>
                       
                            <div data-ng-if="showUnitClosureForm" style="margin-bottom: 100px;">
                               <h4>All Tasks : Completed {{completedCount}} out of {{unitClosureFormDataResponse.length}} </h4> 
                               <h4> Mandatory Tasks : Completed {{requiredTaskCount}} out of {{totalRequiredTask}}  </h4>    
                               <button class="btn btn-primary" style="margin: 10px;" data-ng-click="sendPendingFormDataEmail(detail)">Send Pending Task Notification</button>
                                <div class="row" ng-repeat="u in unitClosureFormDataResponse track by $index" style="padding: 20px;">
                                   
                                    <div class="row" style="padding: 20px;" ng-style="isCompleted(u) ? {'background-color':'rgb(221, 221, 221)'} : ''  " >
                                            <div class="row" style="border-top: 1px solid rgb(221, 221, 221); padding-top: 10px;"> 
                                              <div class="col-xs-4" ><label>S.no : {{$index+1}} </label></div>
                                              <div class="col-xs-4" >  <label class="col-form-label"> {{u.unitClosureMetaData.taskDescription}}  </label>  </div>
                                              <div class="col-xs-4" ><label for="recipient-name" class="col-form-label">  Department : {{u.unitClosureMetaData.department}}  </label> </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-xs-4">
                                                    <label class="col-form-label" >Date : </label>
                                                    <span ng-style="u.unitClosureMetaData.dateRequired==='Y' ? {'color':'blue'} : '' ">{{u.unitClosureMetaData.dateRequired==="Y" ? "*Required" : "(Optional)" }}</span> 
                                                    <input type="date" data-ng-model="u.date"  class="form-control">
                                                </div>
                                               
                                                <div class="col-xs-4">
                                                    <label class="col-form-label">Comment :</label>
                                                    <span ng-style="u.unitClosureMetaData.commentRequired==='Y' ? {'color':'blue'} : '' " > {{u.unitClosureMetaData.commentRequired==="Y" ? "*Required" : "(Optional)" }} </span> 
                                                    <textarea  class="form-control" data-ng-model="u.comment"  placeholder="Write Comment ..."></textarea>
                                                </div>

                                                <div class="col-xs-4"> 
                                                    <div data-ng-if="u.attachmentId !=null">
                                                        <h5>Attachment Uploaded Successfully</h5>
                                                   </div>
                                                   <div data-ng-if="u.attachmentId ===  null"> 
                                                   <label class="col-form-label" >Attachment : </label>
                                                   <span ng-style="u.unitClosureMetaData.attachmentRequired==='Y' ? {'color':'blue'} : '' ">{{u.unitClosureMetaData.attachmentRequired==="Y" ? "*Required" : "(Optional)" }}</span>
                                                   <input class="btn btn-primary" style="margin-top: 5px" type="file" file-model="fileToUpload" accept="">
                                                   <button class="btn btn-primary" style="margin-top: 5px"
                                                   data-ng-click="uploadUnitClosureAttachment($index)"
                                                   data-ng-if="fileToUpload != null">Upload</button>
                                                </div>
                                                </div>
                                           </div>
                                    </div>                             
                                </div>
                                <div class="row">
                                    <div class="col-xs-3"></div>
                                    <div class="col-xs-3"></div>
                                    <div class="col-xs-3"> </div>
                                    <div class="col-xs-3">
                                        <button class="btn btn-success btn-lg" type="button" style="margin-right: 10px;"  ng-click="submitUnitClosure(true,detail)">Save</button>
                                        <button class="btn btn-primary btn-lg " type="button"  ng-click="submitUnitClosure(false,detail)"> Submit </button></div>
                                </div>
                                </div>
                            </td>
                    </tr>
                    </tbody>
                </table>
                
                <br/><br/><br/><br/>
            </div>
           
        </div>

    </div>


</div>
