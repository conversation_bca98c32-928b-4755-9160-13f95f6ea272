<div id="notificationDiv" ng-init="init()" xmlns:70vh xmlns:70vh xmlns:70vh
     xmlns:max-height="http://www.w3.org/1999/xhtml">
    <div id="notificationDivDetail">
        <div class="row">
            <div class="col s8">
                <br>
                <h1 class="page-header">
                    App Notification Management
                </h1>
            </div>
            <div class="col-lg-2">
                <button class="btn btn-primary" data-toggle="modal"
                        data-ng-click="addNewAppNotificationDialog()"
                        id="addNotification">
                    <i class="fa fa-plus fw"></i> Add Notification
                </button>
            </div>
        </div>
        <br>

        <div class="form-group ">
            <label>Company Id</label>
            <!--<input class="form-control" ng-model="selectedCompanyId"-->
            <!--type="text"/>-->
            <div class="form-group " style="width: 75%">
                <select class="form-control" ng-model="optedCompanyId"
                        ng-options=" company.id for company in companiesList">
                </select>
            </div>
        </div>

        <div class="form-group" style="width: 75%">
            <label>Start Date</label>
            <div class="datepicker" data-date-format="yyyy-MM-dd">
                <input class="form-control" data-ng-model="startDate" type="text"
                       placeholder="yyyy-MM-dd"/>
            </div>
        </div>

        <div class="form-group" style="width: 75%">
            <label>End Date</label>
            <div class="datepicker" data-date-format="yyyy-MM-dd">
                <input class="form-control" data-ng-model="endDate" type="text"
                       placeholder="yyyy-MM-dd"/>
            </div>
        </div>
        <br><br>

        <div class="form-group ">
            <button class="btn btn-primary"
                    data-ng-click="findAll(startDate,endDate)">
                <i class="fa fa-search fw"></i> Find All
            </button>

        </div>
    </div>

    <br>
    <div ng-show="showTable">
        <div class="row ">
            <div class="col-xs-12">
                <div class="row ">
                    <div class="col-xs-12 overflow">

                        <table class="table table-striped table-bordered">
                            <thead style="background-color: #e7e7e7">
                            <th>Notification Category</th>
                            <th>Notification Type</th>
                            <th>Company Id</th>
                            <th>Campaign Name</th>
                            <th>Campaign Description</th>
                            <th>Notification Title</th>
                            <th>Notification Body</th>
                            <th>Image Path</th>
                            <th>Action Type</th>
                            <th>Topic</th>
                            <th style="text-align:center">Action</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat=" notification in notificationList|orderBy:'offerIndex'|orderBy : predicate :reverse|filter:searchFields ">
                                <td>{{notification.categoryName}}</td>
                                <td>{{notification.notificationDesc}}</td>
                                <td>{{notification.companyId}}</td>
                                <td>{{notification.campaignName}}</td>
                                <td width="12%">{{notification.campaignDescription}}</td>
                                <td>{{notification.title}}</td>
                                <td>{{notification.body}}</td>
                                <td>{{notification.image}}</td>
                                <td>{{notification.actionName}}</td>
                                <td>{{notification.topicSuffix}}</td>
                                <td>
                                    <div ng-show="notification.status=='INACTIVE'">
                                        <button
                                                class="btn btn-primary"
                                                data-toggle="modal"
                                                ng-click="openTestModal(notification)">Test Application
                                        </button>
                                    </div>
                                    <div ng-show="notification.status=='ACTIVE'">
                                        <button
                                                class="btn btn-primary"
                                                data-toggle="modal"
                                                ng-click="sendNotification(notification)">Send Notification
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!--add new notification-->
<div aria-labelledby="AddNewNotificationModalLabel" class="modal fade" id="AddNewAppNotificationModalData" role="dialog"
     data-keyboard="false" data-backdrop="static"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" ng-click="reset()" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_add"> Add New App Notification</h4>
            </div>
            <div class="modal-body">
                <form name="addNewReportForm" novalidate>
                    <div class="form-group">
                        <label>Notification Category</label>
                        <div class="form-group">
                            <select class=form-control ng-model="selectedNotificationCategory"
                                    ng-options="category.name  for category in notificationCategory"
                                    ng-change="onSelectCategory(selectedNotificationCategory)">
                            </select>
                        </div>
                    </div>
                    <div class="form-group ">
                        <label>Notification Type</label>
                        <div class="form-group ">
                            <select class="form-control" ng-model="selectedNotificationType"
                                    ng-change="onSelectType(selectedNotificationType)"
                                    ng-options=" type.name for type in notificationType">
                            </select>
                        </div>
                    </div>
                    <div ng-show="selectedNotificationType.code=='APP_BLOCKER'">

                        <div class="form-group ">
                            <label>Action Button Name</label>
                            <input class="form-control" ng-model="appBlockerActionButton"
                                   maxlength="500" required type="text"/>
                        </div>
                        <div class="form-group ">
                            <label>App Blocker Action</label>
                            <input class="form-control"  ng-model="appBlockerActionType"
                                   maxlength="500" required type="text"/>
                        </div>
                        <div class="form-group ">
                            <label>App Blocker Banner</label>
                            <input class="form-control" ng-model="appBlockerBanner"
                                   maxlength="500" required type="text"/>
                        </div>
                        <div class="form-group ">
                            <label>App Blocker Title</label>
                            <input class="form-control" ng-model="appBlockerTitle"
                                   maxlength="500" required type="text"/>
                        </div>
                        <div class="form-group ">
                            <label>App Blocker SubTitle</label>
                            <input class="form-control" ng-model="appBlockerSubTitle"
                                   maxlength="500" required type="text"/>
                        </div>
                    </div>
                    <div class="form-group ">
                        <label>Company Id</label>
                        <!--<input class="form-control" ng-model="companyId"-->
                        <!--required type="text"/>-->
                        <div class="form-group ">
                            <select class="form-control" ng-model="selectedCompanyId"
                                    ng-options=" company.id for company in companiesList">
                            </select>
                        </div>
                    </div>
                    <div class="form-group ">
                        <label>Campaign Name</label>
                        <input class="form-control" ng-model="campaignName"
                               maxlength="100"
                               required type="text"/>
                    </div>
                    <div class="form-group ">
                        <label>Campaign Description</label>
                        <input class="form-control" ng-model="description"
                               maxlength="500" required type="text"/>
                    </div>
                    <div ng-show="!inputExcel">
                        <div class="form-group ">
                            <label>Notification Title</label>
                            <input class="form-control" ng-model="title"
                                   maxlength="50" required type="text"/>
                        </div>
                    </div>
                    <div ng-show="!inputExcel">
                        <div class="form-group ">
                            <label>Notification Body</label>
                            <input class="form-control" ng-model="message"
                                   maxlength="150" required type="text"/>
                        </div>
                    </div>
                    <!--<div ng-show="isSingleCustomer">-->
                    <!--<div class="form-group ">-->
                    <!--<label>Contact Number</label>-->
                    <!--<input class="form-control" ng-model="singleCustomerNumber" placeholder="Please do not enter country code"-->
                    <!--maxlength="10" required type="tel"/>-->
                    <!--</div>-->
                    <!--</div>-->
                    <div ng-show="!inputExcel">
                        <div class="form-group">
                            <label>Upload Image</label>
                            <input class="btn btn-default" file-model="fileToUpload" style="width: 100%;"
                                   type="file">

                            <button class="btn btn-primary "
                                    ng-click="uploadImage()">Upload
                            </button>
                            <span ng-if="imagePath!=null" style="color: #1ca62c">Uploaded!</span>

                        </div>
                    </div>
                    <div ng-show="inputExcel">
                        <div class="form-group">
                            <label>Upload Excel file</label>
                            <input class="btn btn-default" file-model="fileToUpload" style="width: 100%;"
                                   type="file">
                            <button class="btn btn-primary" style="margin-top: 5px"
                                    data-ng-click="uploadExcelFile(campaignName)"
                                    data-ng-disabled="fileToUpload==null">Upload File
                            </button>
                        </div>
                    </div>
                    <div class="form-group ">
                        <label>Action Type</label>
                        <div class="form-group ">
                            <div ng-show="isSilent">
                                <select class="form-control" ng-model="selectedActionType"
                                        ng-options="action.name for action in notificationActionType| filter:{silent:true}">
                                </select>
                            </div>
                            <div ng-show="!isSilent">
                                <select class="form-control" ng-model="selectedActionType"
                                        ng-options="action.name for action in notificationActionType| filter:{silent:false}">
                                </select>
                            </div>

                        </div>
                    </div>
                    <div class="form-group">
                        <label>Topic</label>
                        <div class="form-group ">
                            <div ng-show="isCustomer">
                                <select class="form-control" ng-model="selectedTopic"
                                        ng-options="topic.name for topic in notificationTopic |filter:{customerStatus:true}">
                                </select>
                            </div>
                            <div ng-show="!isCustomer">
                                <select class="form-control" ng-model="selectedTopic"
                                        ng-options="topic.name for topic in notificationTopic |filter:{customerStatus:false}">
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row ">
                        <div class="form-group" align="center">
                            <button class="btn btn-primary"
                                    ng-click="submitNotification(selectedNotificationCategory,selectedNotificationType,
                                selectedCompanyId,campaignName,description,title,message,selectedActionType,selectedTopic,
                                appBlockerActionButton,appBlockerActionType,appBlockerBanner,appBlockerTitle,appBlockerSubTitle)">
                                Submit
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div aria-labelledby="UnitListViewModalLabel" class="modal fade" id="TestApplicationModal" role="dialog"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_unitList"> Testing Notification</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group ">
                            <label>Test Accounts</label>
                            <textarea class="form-control" data-ng-disabled="true">{{arrayToString(alreadyPresentAccounts)}}</textarea>
                        </div>
                        <div class="form-group ">
                            <label>Additional Accounts</label>
                            <input class="form-control" ng-model="additionalAccounts"
                                   required type="text" multiple/>
                        </div>
                        <div class="form-group">
                            <span class="checkbox-inline" style="color:#4f964f;">
                                  <b>Use Additional Accounts</b></span>
                            <input class="checkbox-inline" type="checkbox"
                                   ng-model="useAdditional"/>
                        </div>
                    </div>
                </div>
                <div class="row ">
                    <div class="form-group" align="center">
                        <button class="btn btn-primary"
                                ng-click="testApplication(additionalAccounts,useAdditional)">
                            Test Application
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>