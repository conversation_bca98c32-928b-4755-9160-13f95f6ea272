<div><pre>{{ Items | json }}</pre></div>


 <div ng-init="init()">
 		<div class="col-xs-12" >
        <div class="row">
         <div class="form-group" >
               <label> No Of Shifts </label>
                  <select class="form-control" ng-model="selectNoOfShift" ng-options="noofShifts.name for noofShifts in noofShift track by noofShifts.name" ng-click="changeShift(selectNoOfShift.name)"></select>
         </div>
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered" style="border:2px solid">
                    <thead style="background-color:#E0E0E0">
                       <th style="vertical-align:top">
                        <label class="checkbox-inline"><input type="checkbox"  ng-model="checkedDays" ng-click="checkAllDays()"/><b>Days </b></th>
                        <th>
	                        <label class="checkbox-inline">
		                        <input type="checkbox" ng-model="checkedDine"  ng-click="checkAllDineIn()"/> <b>Dine in</b>
		                        <timepicker ng-model="tickDineStartTime"  ng-change="changedDineStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
		                        <timepicker ng-model="tickDineCloseTime"  ng-change="changedDineStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
                        	</label>
                       </th>
                       
                        <th>
                        <label class="checkbox-inline">
	                        <input type="checkbox" ng-model="checkedCod" ng-click="checkAllCodIn()"/> <b> COD</b>
	                        <timepicker ng-model="tickCODStartTime" ng-change="changedCodStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
	                        <timepicker ng-model="tickCODCloseTime" ng-change="changedCodStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
                        </label>
                        </th>
                        <th>
                        <label class="checkbox-inline">
	                        <input type="checkbox" ng-model="checkedTakeAway" ng-click="checkTakeAwayIn()"/><b>Take Away </b>
	                        <timepicker ng-model="tickTakeAwayStartTime" ng-change="changedTakeAwayStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
	                        <timepicker ng-model="tickTakeAwayCloseTime" ng-change="changedTakeAwayStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
                        </label>
                        </th>
                       <th style="vertical-align:top">
	                        <label class="checkbox-inline">
	                        <input type="checkbox" ng-model="checkedHandOver"  ng-click="checkAllHandOverIn()"/><b> Hand Over </b>
	                        <timepicker ng-model="tickHandOverTime"  ng-change="changedHandOverStartCheck()" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ></timepicker>
                        </label>
                        </th>
                    </thead>
                <tbody>
                <tr ng-repeat="item in Items">
		            <td>
		            	<input type="checkbox" ng-model="item.daysTick" ng-change="checkDays(item.dayOfTheWeek,item.daysTick)"/>&nbsp;{{item.dayOfTheWeek}}
		            </td>
		            <td>
			            <input type="checkbox" ng-model="item.dineTick" ng-change="checkDineDetails(item.dayOfTheWeek,item.dineTick,$index)"/>
			            <timepicker ng-model="singleDineInStartTime[$index]" ng-change="changedDineStartDate(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>
	                    <timepicker ng-model="singleDineInCloseTime[$index]" ng-change="changedDineCloseDate(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>       
		             </td>
		              <td>
			              <input type="checkbox" ng-model="item.codeTick" ng-change="checkDeliveryDetails(item.dayOfTheWeek,item.codeTick)"/>
			              <timepicker ng-model="singleDeliveryStartTime[$index]"  ng-change="changedDeliveryStartDate(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>
	                      <timepicker ng-model="singleDeliveryCloseTime[$index]" ng-change="changedDeliveryCloseDate(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>
		              </td>
		               <td>
			               <input type="checkbox" ng-model="item.takeawayTick" ng-change="checkTakeAwayDetails(item.dayOfTheWeek,item.takeawayTick)"/>
			               <timepicker ng-model="singleTakeAwayStartTime[$index]" ng-change="changedTakeAwayStartDate(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>
	                       <timepicker ng-model="singleTakeAwayCloseTime[$index]" ng-change="changedTakeAwayCloseDate(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>
		               </td>
		              	<td>
			              	<input type="checkbox" ng-model="item.handoverTick" ng-change="checkHandOverDetails(item.dayOfTheWeek,item.handoverTick)" >
			              	<timepicker ng-model="singleHandOverTime[$index]"  ng-change="changedHandOverTime(item.dayOfTheWeek,$index)" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></timepicker>
                      	</td>
       		   	</tr>
                </tbody>
                </table>
            </div>
            
        </div>
        
    </div>
</div> 
</div>

