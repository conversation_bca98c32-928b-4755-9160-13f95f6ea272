<style type="text/css">
    th, td {
        text-align: center;
    }

    .greenBackground {
        background-color: limegreen;
    }
</style>

<div
        class="row"
        ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Build Campaign
        </h1>
    </div>

    <div style="margin: 10px">
        <form name="campaignBuilderForm">
            <div class="form-group">
                <label>Primary URL *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('primaryUrl')"/>
                <input type="text"
                       class="form-control"
                       ng-model="campaignDetail.primaryUrl"
                />
            </div>
            <div class="form-group">
                <label>Campaign Strategy *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignStrategy')"/>
                <select class="form-control"
                        ng-model="campaignDetail.campaignStrategy"
                        ng-options="campaignStrategy for campaignStrategy in campaignStrategyList"
                        ng-change="updateCampaignMapping()"
                        ng-disabled="isCloneOrEdit"
                />
            </div>
            <div class="form-group" ng-if="checkCampaignStrategyForCafeLaunch()">
                <label>Launch Cafe</label>
                <select class="form-control"
                        ng-model="campaignDetail.launchUnitId"
                        ng-options="unit.id as unit.name + ' - ' + unit.id for unit in allUnitList"
                />
            </div>
            <div class="form-group" ng-if="checkCampaignStrategyForCafeLaunch()">
                <label>Cafe Launch Date</label>
                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                    <input type="text"
                           class="form-control"
                           ng-model="campaignDetail.cafeLaunchDate"
                           placeholder="yyyy-MM-dd"
                    />
                </div>
            </div>
            <div class="form-group" ng-show="checkCampaignStrategyForGeneral()">
                <label>Parent Campaign Strategy</label>
                <em class="fa fa-info-circle" ng-click="showInfo('parentCampaignStrategy')"/>
                <select class="form-control"
                        ng-model="campaignDetail.parentCampaignStrategy"
                        ng-options="parentCampaignStrategy for parentCampaignStrategy in parentCampaignStrategyList"
                />
            </div>
            <div class="form-group" ng-show="checkCampaignStrategyForGeneral()">
                <label>New Customer Only *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('newCustomerOnly')"/>
                <select class="form-control"
                        ng-model="campaignDetail.newCustomerOnly"
                        ng-options="newCustomerOnly for newCustomerOnly in newCustomerOnlyOptions"
                />
            </div>
            <div class="form-group" ng-show="checkCampaignStrategyForDelivery()">
                <label>Linked Campaign</label>
                <em class="fa fa-info-circle" ng-click="showInfo('linkedCampaignId')"/>
                <input title="Fetch campaigns" type="checkbox" ng-model="fetchCampaignsTag" class="btn btn-primary"
                       ng-click="fetchCampaignListForLinking()"
                       style="margin-left: 3px; margin-bottom: 3px">
                </input>
                <select class="form-control"
                        ng-model="campaignDetail.linkedCampaignId"
                        ng-options="linkedCampaign.campaignId as linkedCampaign.campaignId + ' - ' + linkedCampaign.campaignName for linkedCampaign in linkedCampaignOptions"
                />
            </div>
            <div class="form-group">
                <label>Campaign Source *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignSource')"/>
                <select class="form-control"
                        ng-model="campaignDetail.campaignSource"
                        ng-options="campaignSource for campaignSource in campaignSourceList"
                />
            </div>
            <div class="form-group">
                <label>Campaign Medium *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignMedium')"/>
                <select class="form-control"
                        ng-model="campaignDetail.campaignMedium"
                        ng-options="campaignMedium for campaignMedium in campaignMediumList"
                />
            </div>
            <div class="form-group">
                <label>Campaign Name *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignName')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.campaignName"
                />
            </div>
            <div class="form-group">
                <label>Campaign Category *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignCategory')"/>
                <select class="form-control"
                        ng-model="campaignDetail.campaignCategory"
                        ng-options="campaignCategory for campaignCategory in campaignCategoryList"
                />
            </div>
            <div class="form-group">
                <label>Brand *</label>
                <select class="form-control"
                        ng-model="campaignDetail.brandId"
                        ng-options="brand.brandId as brand.brandName for brand in brandList"
                />
            </div>
            <div class="form-group">
                <label>Campaign Description *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignDesc')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.campaignDesc"
                />
            </div>
            <div class="form-group">
                <label>Applicable for Orders *</label>
                <select class="form-control"
                        ng-model="campaignDetail.applicableForOrder"
                        ng-options="applicableForOrder.code as applicableForOrder.name for applicableForOrder in yesNoOption"/>
            </div>
            <div class="form-group">
                <label>Clone Coupon *</label>
                <select class="form-control"
                        ng-model="campaignDetail.couponClone"
                        ng-change="resetCouponData()"
                        ng-options="couponClone for couponClone in cloneCouponOptions"
                />
            </div>
            <div class="form-group" ng-show="!checkCampaignStrategyForDelivery()">
                <label>Coupon Prefix *</label>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.couponPrefix"
                       maxlength="3"
                       ng-disabled="campaignDetail.couponClone == null || campaignDetail.couponClone == 'No'"
                />
            </div>
            <div class="form-group" ng-show="checkCampaignStrategyForBasicGeneral()">
                <label>Coupon Applicable after(in days)</label>
                <em class="fa fa-info-circle" ng-click="showInfo('couponApplicableAfter')"/>
                <input type="number"
                       placeholder="dd"
                       class="form-control"
                       ng-model="campaignDetail.couponApplicableAfter"
                />
            </div>
            <br>
            <div class="form-group">
                <label>Test Contact Number</label>
                <input type="number"
                       ng-model="testContactNumber"
                />
                &emsp;&emsp;
                <label>Test Customer Name</label>
                <input type="text"
                       pattern=".*\S+.*"
                       ng-model="testCustomerName"
                />
            </div>
            <br>
            <div class="form-group">
                <table class="table table-bordered">
                    <thead style="background-color: #e7e7e7">
                    <th>Customer Type</th>
                    <th>Journey</th>
                    <th>Validity<br>(in days)</th>
                    <th>Reminder<br>(in days)</th>
                    <th>Source Coupon code</th>
                    <th>Source Coupon description
                        <div style="font-size: 10px; color: #454545;">
                            (Visible to Customer)
                        </div>
                    </th>
                    <th ng-show="!checkCampaignStrategyForDelivery()">Start Date</th>
                    <th ng-show="!checkCampaignStrategyForDelivery()">End Date</th>
                    <th ng-show="checkCampaignStrategyForDelivery()">Coupon Count</th>
                    <th>Validate Coupon</th>
                    <th>Require</th>
                    <th>Test Notification</th>
                    </thead>

                    <tbody>
                    <tr ng-repeat="campaignMapping in campaignMappings track by $index"
                        ng-class="{'greenBackground': campaignMapping.isRequired == true}">
                        <td>{{campaignMapping.customerType}}</td>
                        <td>{{campaignMapping.journey}}</td>
                        <td><input type="number"
                                   min="0"
                                   placeholder="dd"
                                   class="form-control"
                                   ng-model="campaignMappings[$index].validityInDays"></td>
                        <td><input type="number"
                                   min="0"
                                   placeholder="dd"
                                   class="form-control"
                                   ng-model="campaignMappings[$index].reminderDays"></td>
                        <td><input type="text"
                                   pattern=".*\S+.*"
                                   class="form-control"
                                   ng-model="campaignMappings[$index].code"
                                   ng-change="invalidateCoupon($index)"></td>
                        <td><input type="text"
                                   pattern=".*\S+.*"
                                   class="form-control"
                                   maxlength="65"
                                   ng-model="campaignMappings[$index].desc"></td>
                        <td style="white-space: nowrap; overflow:hidden;" ng-show="!checkCampaignStrategyForDelivery()">
                            {{campaignMapping.startDate}}
                        </td>
                        <td style="white-space: nowrap; overflow:hidden;" ng-show="!checkCampaignStrategyForDelivery()">
                            {{campaignMapping.endDate}}
                        </td>
                        <td ng-show="checkCampaignStrategyForDelivery()">{{campaignMapping.deliveryCouponCount}}</td>
                        <td>
                            <button ng-click="validateCampaignMappedCoupon($index)"
                                    ng-disabled="checkNewAndLoyalTea($index)">{{campaignMapping.isValid}}
                                <!--<button
                                    title="" style="border: none; background: none"><img
                                    src="img/"
                                    alt="Change Status"></button>--></button>
                        </td>
                        <td>
                            <input ng-disabled="isEditCampaignPage == true" title="require" type="checkbox"
                                   ng-model="campaignMapping.isRequired"
                                   class="btn btn-primary ng-pristine ng-valid ng-empty ng-touched"
                                   ng-change="setPreviousJourneysRequired($index)">
                        </td>
                        <td>
                            <button ng-click="testCampaignNotification($index)"
                                    ng-disabled="!checkIfValidCoupon($index)">Test
                            </button>
                        </td>
                    </tr>
                    </tbody>

                </table>
            </div>
            <div class="form-group">
                <label>Campaign Reach *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('campaignReach')"/>
                <label class="btn btn-default" style="margin: 20px"> <input
                        type="radio"
                        data-ng-model="campaignDetail.campaignReach"
                        data-ng-value="1"
                        data-ng-change="clearCampaignReach()"/>System Specific
                </label>
                <label class="btn btn-default" style="margin: 20px" ng-show="!checkCampaignStrategyForSlotGame()"> <input
                        type="radio"
                        data-ng-model="campaignDetail.campaignReach"
                        data-ng-value="2"
                        data-ng-change="clearCampaignReach()"/>Region Specific
                </label>
                <label class="btn btn-default" style="margin: 20px" ng-show="!checkCampaignStrategyForSlotGame()"> <input
                        type="radio"
                        data-ng-model="campaignDetail.campaignReach"
                        data-ng-value="3"
                        data-ng-change="clearCampaignReach()"/>Unit Specific
                </label>
            </div>
            <div class="form-group" ng-show="campaignDetail.campaignReach == 2  || campaignDetail.campaignReach == 3">
                <label>Region</label>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                     options="regionList" selected-model="selectedRegionList" class="region-card"
                     events="{onSelectionChanged: clearSelectedCityAndUnit}">
                </div>
                <!---->
                <!--<input type="text"-->
                <!--pattern=".*\S+.*"-->
                <!--class="form-control"-->
                <!--ng-model="campaignDetail.region"-->
                <!--/>-->
            </div>
            <div class="form-group" ng-show="campaignDetail.campaignReach == 3">
                <label>City</label>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                     options="cityList" selected-model="selectedCityList" class="region-card"
                     data-ng-click="applyCityFilter()"
                     events="{onSelectionChanged: clearSelectedUnit}">
                </div>
            </div>
            <div class="form-group" ng-show="campaignDetail.campaignReach == 3">
                <label>Unit name and Id</label>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                     options="unitList" selected-model="selectedUnitList" class="region-card"
                     data-ng-click="applyUnitFilter()">
                </div>
            </div>
            <div class="form-group">
                <label>Usage Limit *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('usageLimit')"/>
                <input type="number"
                       min="0"
                       placeholder="dd"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.usageLimit"
                />
            </div>
            <div class="form-group">
                <label>Campaign Start date *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('startDate')"/>
                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                    <input type="text"
                           class="form-control"
                           ng-model="campaignDetail.startDate"
                           placeholder="yyyy-MM-dd"
                    />
                </div>
            </div>
            <br>
            <br>
            <!--<div class="form-group">-->
            <!--<label>Validity</label>-->
            <!--<input type="number"-->
            <!--min="0"-->
            <!--class="form-control"-->
            <!--ng-model="campaignDetail.validity"-->
            <!--placeholder="dd"-->
            <!--/>-->
            <!--</div>-->
            <div class="form-group">
                <label>Campaign End date *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('endDate')"/>
                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                    <input type="text"
                           class="form-control"
                           ng-model="campaignDetail.endDate"
                           placeholder="yyyy-MM-dd"
                    />
                </div>
            </div>
            <br>
            <br>
            <!--<div class="form-group">-->
            <!--<label>Landing Page Hero Banner for Mobile and Desktop *</label>-->
            <!--<input type="text"-->
            <!--pattern=".*\S+.*"-->
            <!--class="form-control"-->
            <!--ng-model="campaignDetail.heroBannerMobile"-->
            <!--/>-->
            <!--</div>-->
            <div class="form-group">
                <label>Landing Page Description *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('landingPageDesc')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.landingPageDesc"
                />
            </div>
            <div class="form-group">
                <label>SMS Template</label>
                <em class="fa fa-info-circle" ng-click="showInfo('smsTemplate')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.smsTemplate"/>
            </div>
            <div class="form-group">
                <label>Whatsapp Template</label>
                <em class="fa fa-info-circle" ng-click="showInfo('whatsappTemplate')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.whatsappTemplate"/>
            </div>
            <div class="form-group">
                <label>Reminder SMS</label>
                <em class="fa fa-info-circle" ng-click="showInfo('smsReminder')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.smsReminder"/>
            </div>
            <div class="form-group">
                <label>Reminder Whatsapp</label>
                <em class="fa fa-info-circle" ng-click="showInfo('whatsappReminder')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.whatsappReminder" \/>
            </div>
            <div class="form-group">
                <label>Reminder Day Gap</label>
                <em class="fa fa-info-circle" ng-click="showInfo('reminderDayGap')"/>
                <input type="number"
                       class="form-control"
                       placeholder="dd"
                       ng-model="campaignDetail.reminderDayGap"/>
            </div>
            <div class="form-group">
                <label>Preview URL Heading *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('utmHeading')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.utmHeading"
                />
            </div>
            <div class="form-group">
                <label>Preview URL Description *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('utmDesc')"/>
                <input type="text"
                       pattern=".*\S+.*"
                       class="form-control"
                       ng-model="campaignDetail.utmDesc"
                />
            </div>
            <div class="form-group">
                <label>Preview URL Image *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('utmImageUrl')"/>
                <div class="form-group form-inline">
                    <input class="form-control btn btn-default"
                           file-model="previewUrlImageFile"
                           style="width: 40%;"
                           type="file"/>
                    <button class="btn btn-primary "
                            ng-click="uploadCampaignImage('Preview URL Image')">
                        Upload
                    </button>
                    <img ng-show="campaignDetail.utmImageUrl" width="50px" height="50px"
                         data-ng-click="openImagePreviewModal(campaignDetail.utmImageUrl)"
                         data-ng-src="{{campaignDetail.utmImageUrl}}"/>
                    <div style="font-size: 12px; color: #303030;">
                        Image resolution: 1:1 or 16:9 and max size: 200KB
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Redirection URL *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('redirectionUrl')"/>
                <input type="text"
                       class="form-control"
                       ng-model="campaignDetail.redirectionUrl"
                />
            </div>
            <div class="form-group">
                <label>Campaign Image 1 *</label>
                <em class="fa fa-info-circle" ng-click="showInfo('image1')"/>
                <div class="form-group form-inline">
                    <input class="form-control btn btn-default"
                           file-model="image1File"
                           style="width: 40%;"
                           type="file"/>
                    <button class="btn btn-primary "
                            ng-click="uploadCampaignImage('Image1')">Upload
                    </button>
                    <img ng-show="campaignDetail.image1" width="50px" height="50px"
                         data-ng-click="openImagePreviewModal(campaignDetail.image1)"
                         data-ng-src="{{campaignDetail.image1}}"/>
                    <div style="font-size: 12px; color: #303030;">
                        Image resolution: 1:1 and max size: 200KB
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Campaign Image 2</label>
                <em class="fa fa-info-circle" ng-click="showInfo('image2')"/>
                <div class="form-group form-inline">
                    <input class="form-control btn btn-default"
                           file-model="image2File"
                           style="width: 40%;"
                           type="file"/>
                    <button class="btn btn-primary "
                            ng-click="uploadCampaignImage('Image2')">Upload
                    </button>
                    <img ng-show="campaignDetail.image2" width="50px" height="50px"
                         data-ng-click="openImagePreviewModal(campaignDetail.image2)"
                         data-ng-src="{{campaignDetail.image2}}"/>
                    <div style="font-size: 12px; color: #303030;">
                        Image resolution: 1:1 and max size: 200KB
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Campaign Image 3</label>
                <em class="fa fa-info-circle" ng-click="showInfo('image3')"/>
                <div class="form-group form-inline">
                    <input class="form-control btn btn-default"
                           file-model="image3File"
                           style="width: 40%;"
                           type="file"/>
                    <button class="btn btn-primary "
                            ng-click="uploadCampaignImage('Image3')">Upload
                    </button>
                    <img ng-show="campaignDetail.image3" width="50px" height="50px"
                         data-ng-click="openImagePreviewModal(campaignDetail.image3)"
                         data-ng-src="{{campaignDetail.image3}}"/>
                    <div style="font-size: 12px; color: #303030;">
                        Image resolution: 1:1 and max size: 200KB
                    </div>
                </div>
            </div>


            <div class="form-group">
                <label>CRM App Banner</label>
                <em class="fa fa-info-circle" ng-click="showInfo('crmAppBannerUrl')"/>
                <div class="form-group form-inline">
                    <input class="form-control btn btn-default"
                           file-model="crmAppBannerFile"
                           style="width: 40%;"
                           type="file"/>
                    <button class="btn btn-primary "
                            ng-click="uploadCampaignImage('CrmAppBanner')">Upload
                    </button>
                    <img ng-show="campaignDetail.crmAppBannerUrl" width="50px" height="50px"
                         data-ng-click="openImagePreviewModal(campaignDetail.crmAppBannerUrl)"
                         data-ng-src="{{campaignDetail.crmAppBannerUrl}}"/>
                    <div style="font-size: 12px; color: #303030;">
                        Image resolution: 1:1 and max size: 200KB
                    </div>
                </div>
            </div>

            <div ng-show="isEditCampaignPage" style="color: red">
                Note: Campaign Short URL will get changed on edit.
            </div>

            <div class="form-group">
                <button style="margin: 10px" class="btn btn-primary pull-right"
                        ng-click="submitCampaignForm()">Save Campaign
                </button>
            </div>
        </form>
    </div>

    <br><br>
    <hr/>

</div>

<div id="addShortUrl" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Short URL</h4>
            </div>

            <div class="modal-body">
                <div class="form-group">
                    <div class="form-group">
                        <label>Long URL</label>
                        <div class="form-group form-inline">
                            <input class="form-control" data-ng-model="selectedCampaignLongUrl"
                                   placeholder="Long URL" required type="text" style="width: 76%" disabled/>
                            <button ng-click="copyLongUrlToClipboard()" title="copy long url"
                                    style="border: none; background: none;"><em class="fa fa-copy"
                                                                                style="font-size: 20px; "></em>
                            </button>
                            <button ng-click="createShortUrl()"
                                    class="btn btn-warning" style="padding: 3px">Shorten Link
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Short URL</label>
                        <div class="form-group form-inline">
                            <input class="form-control" data-ng-model="selectedCampaignShortUrl"
                                   placeholder="Short URL" required type="text" style="width: 92%"/>
                            <button ng-click="copyShortUrlToClipboard()" title="copy short url"
                                    style="border: none; background: none;"><em class="fa fa-copy"
                                                                                style="font-size: 20px; "></em>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right"
                            ng-click="saveShortUrl()">
                        Save
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="imagePreviewModal" class="modal fade" role="dialog" aria-labelledby="imagePreviewModal" tabindex="-1"
     style="z-index: 9999;">
    <div role="document" style="margin: 30px auto; max-width: 400px; max-height: 400px">
        <div class="modal-content">
            <div class="frame" style="margin: auto;">
                <img style="display: block; margin: 0 auto; max-width:400px; max-height: 400px" ;
                     data-ng-src="{{previewImageSource}}"/>
            </div>
        </div>
    </div>
</div>