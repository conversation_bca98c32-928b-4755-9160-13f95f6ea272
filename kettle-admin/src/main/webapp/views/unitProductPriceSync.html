<div data-ng-init="init()" class="container">

    <h1 class="title">Unit Product Price Sync</h1>

    <div class="form-container">
        <label class="form-label">Category</label>
        <select class="form-select" data-ng-model="selectedCategory"
                data-ng-options="item for item in unitCategories track by item"
                data-ng-change="onChangeCategory()">
        </select>

        <button class="btn-primary" value="Get Units" data-ng-click="getUnits()">Get Units</button>

        <div data-ng-hide="hideUnits" class="units-container">
            <div class="unit-section">
                <h2>Sync from Unit</h2>
                <select class="unit-select" data-ng-model="selectedUnits.syncFromUnit" data-ui-select2="unitSelectOptions"
                        data-ng-options="unit as (unit.name + ' [' + unit.id + ']') for unit in allUnits | filter:excludeToUnitSelected">
                </select>
            </div>

            <div class="unit-section">
                <h2>Sync To Unit</h2>
                <select class="unit-select" data-ng-model="selectedUnits.syncToUnit" data-ui-select2="unitSelectOptions"
                        data-ng-options="unit as (unit.name + ' [' + unit.id + ']') for unit in allUnits | filter:excludeFromUnitSelected">
                </select>
            </div>
        </div>
        <button class="btn-primary" value="SYNC" data-ng-if="showSyncBtn()" data-ng-click="syncUnitProductPrice()">SYNC</button>
    </div>

</div>


<style>
    .container {
        max-width: 700px;
        margin: 20px auto;
        padding: 16px;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        font-family: 'Arial', sans-serif;
    }

    .title {
        text-align: center;
        font-size: 2rem;
        color: #2c3e50;
        margin-bottom: 20px;
    }

    .form-container {
        display: flex;
        flex-direction: column;
        gap: 12px; /* Reduced gap */
    }

    .form-label {
        font-size: 1rem;
        font-weight: 600;
        color: #34495e;
    }

    .form-select, .unit-select {
        padding: 8px 12px; /* Reduced padding */
        font-size: 0.9rem; /* Slightly smaller text */
        border: 1px solid #bdc3c7;
        border-radius: 6px;
        outline: none;
        transition: border 0.2s ease;
    }

    .form-select:focus, .unit-select:focus {
        border-color: #3498db;
    }

    .btn-primary {
        padding: 8px 16px; /* Reduced padding */
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 0.9rem;
        cursor: pointer;
        transition: background 0.2s ease;
        align-self: flex-start;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #45a049, #2c6c2c);
    }

    .units-container {
        display: flex;
        justify-content: space-between;
        margin-top: 16px; /* Reduced margin */
    }

    .unit-section {
        display: flex;
        flex-direction: column;
        width: 48%;
    }

    .unit-section h2 {
        font-size: 1.2rem; /* Smaller heading */
        color: #2c3e50;
        margin-bottom: 8px; /* Reduced margin */
    }

    @media (max-width: 600px) {
        .units-container {
            flex-direction: column;
            gap: 12px;
        }
        .unit-section {
            width: 100%;
        }
    }
</style>
