<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    .partner-page table table-bordered table-striped.selected {
        background: green;
        color: #fff;
    }

    .partner-page table table-bordered table-striped.selectedRemove{
        background: red;
        color: #fff;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Management Dashboard</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'PARTNER VIEW'">
        <div class="col-xs-12">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th>Partner Id</th>
                    <th>Partner Name</th>
                    <th>Partner Code</th>
                    <th>Kettle Partner Id</th>
                    <th>Partner Key</th>
                    <th>Partner Status</th>
                    <th style="display: none;">Actions</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="partner in channelPartnerList track by partner.partnerId">
                    <td>{{partner.partnerId}}</td>
                    <td>{{partner.partnerName}}</td>
                    <td>{{partner.partnerCode}}</td>
                    <td>{{partner.kettlePartnerId}}</td>
                    <td><span style="word-break: break-all">{{partner.partnerAuthKey}}</span></td>
                    <td>{{partner.partnerStatus}}</td>
                    <td style="display: none;">
                        <input type="button" class="btn btn-success" value="Activate"
                               data-ng-if="partner.partnerStatus == 'IN_ACTIVE'"
                               data-ng-click="togglePartnerStatus(partner)"/>
                        <input type="button" class="btn btn-danger" value="De-activate"
                               data-ng-if="partner.partnerStatus == 'ACTIVE'"
                               data-ng-click="togglePartnerStatus(partner)"/>
                    </td>
                </tr>
                </tbody>
            </table>
            <div style="display: none;">
                <input type="button" class="btn btn-primary" value="Reload unit channel partner mapping"
                       data-ng-click="reloadUnitChannelPartnerMapping()"/>
                <input type="button" class="btn btn-primary" value="Reload channel partner cache"
                       data-ng-click="reloadChannelPartnerCache()"/>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'UNIT STATUS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to update unit status or refresh stock.</h3>
                    <p>Select all the outlets where you want to take the action and then select the partners where you
                        want to take action
                        and press the corresponding button.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Outlet Updates</h3>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="pull-right">
                                <label>Select All</label>
                                <input type="checkbox" data-ng-model='selectAllUnits'
                                       style="width: 20px; height: 20px" data-ng-click="setSelectAllUnits()">
                            </div>
                            <label>Select Units</label>
                            <ul style="max-height: 400px;overflow: auto; list-style: none;">
                                <li data-ng-repeat="unit in unitList | orderBy:'name' track by unit.id"
                                    data-ng-class="{'selected':unit.selected}"
                                    data-ng-click="unit.selected == true? unit.selected = false:unit.selected=true">
                                    {{unit.name}}
                                </li>
                            </ul>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partners</label>
                                <ul style="max-height: 400px;overflow: auto; list-style: none;">
                                    <li data-ng-repeat="partner in channelPartners | orderBy:'name' track by partner.id"
                                        data-ng-class="{'selected':partner.selected}"
                                        data-ng-click="partner.selected == true? partner.selected = false:partner.selected=true">
                                        {{partner.name}}
                                    </li>
                                </ul>
                            </div>
                            <div class="form-group">
                                <label>Start Date</label>
                                <div class="datepicker" data-date-format="yyyy-MM-dd hh:MM:ss"
                                     data-date-min-limit="{{today}}">
                                    <input class="form-control" data-ng-model="startDate" type="text"
                                           placeholder="yyyy-mm-dd hh:MM:ss" required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>End Date</label>
                                <div class="datepicker" data-date-format="yyyy-MM-dd hh:MM:ss"
                                     data-date-min-limit="{{today}}">
                                    <input class="form-control" data-ng-model="endDate" type="text"
                                           placeholder="yyyy-mm-dd hh:MM:ss" required/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12" style="text-align: right;">
                            <button type="button" class="btn btn-primary" data-ng-click="setUnitStock()">
                                Refresh Stock
                            </button>
                            <button type="button" class="btn btn-success" data-ng-click="setUnitTakeawayStatus(true)">
                                Takeaway On
                            </button>
                            <button type="button" class="btn btn-danger" data-ng-click="setUnitTakeawayStatus(false)">
                                Takeaway Off
                            </button>
                            <button type="button" class="btn btn-success"
                                    data-ng-click="setUnitStatus(true, startDate, endDate)">
                                Turn On
                            </button>
                            <button type="button" class="btn btn-danger"
                                    data-ng-click="setUnitStatus(false, startDate, endDate)">
                                Turn Off
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'PRODUCT STATUS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to update product status on partner side.</h3>
                    <p>Select all the products whose status you want to update and then select the partners where you
                        want to take action
                        and press the corresponding button.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Product stock on/off</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Products"
                                       data-ng-click="getUnitProducts()"/>
                            </div>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-6">
                            <ul style="max-height: 400px;overflow: auto; list-style: none; width: 300px;">
                                <li data-ng-repeat="product in unitProductList 
                                | orderBy:'detail.name' track by product.detail.id"
                                    data-ng-class="{'selected':product.selected}"
                                    data-ng-click="product.selected == true? product.selected = false:product.selected=true">
                                    {{product.detail.name}}
                                </li>
                            </ul>
                        </div>
                        <div class="col-xs-6">
                            <!--<div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>-->
                            <label>Select Partners</label>
                            <ul style="max-height: 400px;overflow: auto; list-style: none;">
                                <li data-ng-repeat="partner in channelPartners | orderBy:'name' track by partner.id"
                                    data-ng-class="{'selected':partner.selected}"
                                    data-ng-click="partner.selected == true? partner.selected = false:partner.selected=true">
                                    {{partner.name}}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-12" style="text-align: right;">
                            <button type="button" class="btn btn-success"
                                    data-ng-click="setProductStatus(true)">Stock In
                            </button>
                            <button type="button" class="btn btn-danger"
                                    data-ng-click="setProductStatus(false)">Stock out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'PRODUCT FILTER'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to filter products which you don't want on partner side.</h3>
                    <p>Select all the products you want to filter out and then select the partners where you want to
                        take action
                        and press the corresponding button. After this you need to update menu again.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Product filter</h3>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Products"
                                       data-ng-click="getUnitProducts()"/>
                            </div>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-6">
                            <ul style="max-height: 400px;overflow: auto; list-style: none; width: 300px;">
                                <li data-ng-repeat="product in unitProductList | filter:{classification:'MENU', type:'!12'}
                                | orderBy:'detail.name' track by product.detail.id"
                                    data-ng-class="{'selected':product.selected}"
                                    data-ng-click="product.selected == true? product.selected = false:product.selected=true">
                                    {{product.detail.name}}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-12" style="text-align: right;">
                            <button type="button" class="btn btn-success"
                                    data-ng-click="setFilteredProducts(true)">Filter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'PRODUCT TAGS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage partner products tags and map them to products.</h3>
                    <p>
                        <strong>To add manage tags:</strong> type the tag you want to add and submit, once its get
                        populated in the list you can remove it by clicking remove button.<br/>
                        <strong>To map tag to products:</strong> Locate the product in the list and select tag you want
                        to map it to and submit the list by clicking submit button.
                    </p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Product Tags Management</h3>
                    <div class="form-group">
                        <label>Select Partner</label>
                        <select class="form-control"
                                data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                data-ng-model="selectedPartner"
                                data-ng-change="setSelectedPartner(selectedPartner)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)">
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                            <h4>Tag Management</h4>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Tags"
                                               data-ng-click="getPartnerTags()"/>
                                    </div>
                                </div>
                            </div>
                            <div data-ng-if="showTagsView">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <label>New Tag Id</label>
                                            <input class="form-control" data-ng-model="newTag.id"/>
                                        </div>
                                        <div class="form-group">
                                            <label>New Tag Name</label>
                                            <input class="form-control" data-ng-model="newTag.name"/>
                                        </div>
                                        <div class="form-group">
                                            <label>New Tag Group</label>
                                            <select class="form-control"
                                                    data-ng-options="group for group in tagGroupList "
                                                    data-ng-model="newTag.group"
                                            >
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Add new tag"
                                                   data-ng-click="addPartnerTags()"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info" data-ng-if="partnerTags.length == 0">No tags found. Please
                                    add some.
                                </div>
                                <table data-ng-if="partnerTags.length > 0" class="table table-bordered table-striped">
                                    <tr>
                                        <td>Tag Id</td>
                                        <td>Tag Name</td>
                                        <td>Group</td>
                                        <td>Action</td>
                                    </tr>
                                    <tr data-ng-repeat="tag in partnerTags | orderBy: 'id' track by $index">
                                        <td>{{tag.id}}</td>
                                        <td>{{tag.name}}</td>
                                        <td>{{tag.group}}</td>
                                        <td>
                                            <input type="button" class="btn btn-danger" value="Remove"
                                                   data-ng-click="removePartnerTags(tag.id)"/>
                                        </td>
                                    </tr>
                                </table>
                                <input type="button" class="btn btn-primary" value="Save tags"
                                       data-ng-click="setPartnerTags()"/>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <h4>Product Tag Mapping</h4>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Products"
                                               data-ng-click="getActiveProducts()"/>
                                        <input type="button" class="btn btn-primary pull-right" value="Submit"
                                               data-ng-if="productsTagsMappings.length > 0"
                                               data-ng-click="submitProductTagMappings()"/>
                                    </div>
                                </div>
                                <div data-ng-if="showProductTagMappingView">
                                    <!--<div class="alert alert-info" data-ng-if="productsTagsMappings.length == 0">No tags found. Please add some.</div>-->
                                    <div class="form-group">
                                        <label>Search Product</label>
                                        <input type="text" class="form-control" data-ng-model="search"/>
                                    </div>
                                    <table data-ng-if="productsTagsMappings.length > 0"
                                           class="table table-bordered table-striped">
                                        <tr>
                                            <td>Product Id</td>
                                            <td>Product Name</td>
                                            <td>Product Tags</td>
                                            <td>Action</td>
                                        </tr>
                                        <tr data-ng-repeat="mapping in filtered = (productsTagsMappings | filter:search ) track by $index">
                                            <td>{{mapping.id}}</td>
                                            <td>{{mapping.name}}</td>
                                            <td><span class="badge"
                                                      data-ng-repeat="tag in mapping.tags track by tag.id">{{tag.name}}</span>
                                            </td>
                                            <td>
                                                <input type="button" class="btn btn-primary" value="Set tags"
                                                       data-ng-click="updateTagMappings(mapping)"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'SWIGGY'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Swiggy actions</h3>
                    <div class="row" style="background: #efefef; padding-bottom: 15px;margin-bottom: 15px;">
                    <div class="col-xs-12" style="margin-top: 16px;">
                    	<div class="form-group col-xs-6">
                   			 <span style="font-size: 24px;margin-bottom: -20px;">Swiggy Stock Code Status: </span>
                   		 </div>
                                <div class="form-group col-xs-2" style="margin-top: 8px;  margin-left: -128px;">
                                    <input type="checkbox" ng-model="stockCheck" class="ng-pristine ng-untouched ng-valid ng-empty">
                                    <span>New</span>
                                </div>
                            <div class="form-group text-right col-xs-4 pull-right">
                                <input type="button" class="btn btn-primary" value="Update" data-ng-click="updateStockStatus(stockCheck)">
                            </div>
                      </div>
					</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'ZOMATO'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Zomato actions</h3>
                    <div class="row" style="background: #efefef; padding-bottom: 15px;margin-bottom: 15px;">
                        <div class="col-xs-12">
                            <h3>Set Zomato Treats</h3>
                            <p>Please note that once you set the treats item, it will be applicable across all units on
                                Zomato.</p>
                            <p style="color: red;font-size: 18px; text-decoration: underline;">
                                Please select item which has no customizations and is vegetarian.
                            </p>
                            <div class="row">
                                <div class="col-xs-12">
                                    <h3>Current treats item: {{zomatoTreatsItem.name}}</h3>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label>Select Unit</label>
                                        <select class="form-control"
                                                data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                                data-ng-model="selectedUnit"
                                                data-ng-change="setSelectedUnit(selectedUnit)">
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Products"
                                               data-ng-click="getUnitProducts()"/>
                                    </div>
                                </div>
                            </div>
                            <div class="row" data-ng-if="showUnitProducts == true">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>Select Product</label>
                                        <select class="form-control"
                                                data-ng-options="product as product.detail.name for product in treatProductList track by product.detail.id"
                                                data-ng-model="selectedProduct"
                                                data-ng-change="setSelectedProduct(selectedProduct)">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12 right-align">
                                    <input type="button" class="btn btn-danger" value="Remove Treats Item"
                                           data-ng-if="zomatoTreatsItem != null"
                                           data-ng-click="removeZomatoTreatsItem()"/>
                                    <input type="button" class="btn btn-success" value="Set Treats Item"
                                           data-ng-if="unitProductList != null"
                                           data-ng-click="setZomatoTreatsItem()"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="background: #efefef; padding-bottom: 15px; margin-bottom: 15px;">
                        <div class="col-xs-12">
                            <h3>Get unit logistics status</h3>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label>Select Unit</label>
                                        <select class="form-control"
                                                data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                                data-ng-model="selectedUnit"
                                                data-ng-change="setSelectedUnit(selectedUnit)">
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Logistics Status"
                                               data-ng-click="getLogisticsStatus()"/>
                                        <input type="button" class="btn btn-primary" value="Update Logistics Status"
                                               data-ng-click="showUpdateLogisticsStatus = true"/>
                                    </div>
                                    <div data-ng-if="zomatoUnitLogisticsStatus != null">
                                        <table class="table table-bordered"
                                               data-ng-if="zomatoUnitLogisticsStatus.code==200">
                                            <tr>
                                                <th>Self Delivery Enabled</th>
                                                <th>Self Delivery Serviceability Status</th>
                                                <th>Zomato Logistics Enabled</th>
                                                <th>Zomato Logisitics Serviceability Status</th>
                                            </tr>
                                            <tr>
                                                <td>{{zomatoUnitLogisticsStatus.data.self_delivery_enabled}}</td>
                                                <td>
                                                    {{zomatoUnitLogisticsStatus.data.self_delivery_serviceability_status}}
                                                </td>
                                                <td>{{zomatoUnitLogisticsStatus.data.zomato_logistics_enabled}}</td>
                                                <td>
                                                    {{zomatoUnitLogisticsStatus.data.zomato_logistics_serviceability_status}}
                                                </td>
                                            </tr>
                                        </table>
                                        <div data-ng-if="zomatoUnitLogisticsStatus.code!=200">
                                            {{zomatoUnitLogisticsStatus.errors}}
                                        </div>
                                    </div>
                                    <div data-ng-if="showUpdateLogisticsStatus == true">
                                        <div class="form-group">
                                            <label>Self Delivery Serviceability Status</label>
                                            <select class="form-control"
                                                    data-ng-change="setSelfDeliveryStatus(self_delivery_serviceability_status)"
                                                    data-ng-model="self_delivery_serviceability_status">
                                                <option value="null"></option>
                                                <option value="true">True</option>
                                                <option value="false">False</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Reason</label>
                                            <input type="text" class="form-control"
                                                   data-ng-model="logisticsChangeRequest.reason"/>
                                        </div>
                                        <div class="form-group">
                                            <label>Revert Seconds</label>
                                            <input type="number" class="form-control"
                                                   data-ng-model="logisticsChangeRequest.revert_seconds"/>
                                        </div>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Submit"
                                                   data-ng-click="updateLogisticsStatus()"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="background: #efefef; padding-bottom: 15px; margin-bottom: 15px;">
                        <div class="col-xs-12">
                            <h3>Get unit delivery status</h3>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label>Select Unit</label>
                                        <select class="form-control"
                                                data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                                data-ng-model="selectedUnit"
                                                data-ng-change="setSelectedUnit(selectedUnit)">
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Delivery Status"
                                               data-ng-click="getDeliveryStatus()"/>
                                        <input type="button" class="btn btn-primary" value="Update Delivery Status"
                                               data-ng-click="showUpdateDeliveryStatus = true"/>
                                    </div>
                                    <div data-ng-if="zomatoUnitDeliveryStatus != null">
                                        <table class="table table-bordered"
                                               data-ng-if="zomatoUnitDeliveryStatus.code==200">
                                            <tr>
                                                <th>Outlet Delivery Status</th>
                                                <th>Zomato Online Order Status</th>
                                            </tr>
                                            <tr>
                                                <td>{{zomatoUnitDeliveryStatus.data.outlet_delivery_status}}</td>
                                                <td>{{zomatoUnitDeliveryStatus.data.zomato_online_order_status}}</td>
                                            </tr>
                                        </table>
                                        <div data-ng-if="zomatoUnitDeliveryStatus.code!=200">
                                            {{zomatoUnitDeliveryStatus.errors}}
                                        </div>
                                    </div>
                                    <div data-ng-if="showUpdateDeliveryStatus == true">
                                        <div class="form-group">
                                            <label>Outlet Delivery Status</label>
                                            <select class="form-control"
                                                    data-ng-change="setOutletDeliveryStatus(outlet_delivery_status)"
                                                    data-ng-model="outlet_delivery_status">
                                                <option value="true">True</option>
                                                <option value="false">False</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Reason</label>
                                            <input type="text" class="form-control"
                                                   data-ng-model="outlet_delivery_status_update_reason"/>
                                        </div>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Submit"
                                                   data-ng-click="updateDeliveryStatus()"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="background: #efefef; padding-bottom: 15px; margin-bottom: 15px;">
                        <div class="col-xs-12">
                            <h3>Get unit takeaway status</h3>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label>Select Unit</label>
                                        <select class="form-control"
                                                data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                                data-ng-model="selectedUnit"
                                                data-ng-change="setSelectedUnit(selectedUnit)">
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Takeaway Status"
                                               data-ng-click="getTakeawayStatus()"/>
                                    </div>
                                    <div data-ng-if="zomatoUnitTakeawayStatus != null">
                                        <table class="table table-bordered"
                                               data-ng-if="zomatoUnitTakeawayStatus.code==200">
                                            <tr>
                                                <th>Outlet Takeaway Status</th>
                                                <th>Zomato Takeaway Status</th>
                                            </tr>
                                            <tr>
                                                <td>{{zomatoUnitTakeawayStatus.data.outlet_takeaway_status}}</td>
                                                <td>{{zomatoUnitTakeawayStatus.data.zomato_takeaway_status}}</td>
                                            </tr>
                                        </table>
                                        <div data-ng-if="zomatoUnitTakeawayStatus.code!=200">
                                            {{zomatoUnitTakeawayStatus.errors}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'MEAT TAGS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage partner meat tags and map them to products.</h3>
                    <p>
                        <strong>To add manage tags:</strong> type the tag you want to add and submit, once its get
                        populated in the list you can remove it by clicking remove button.<br/>
                        <strong>To map tag to products:</strong> Locate the product in the list and select tag you want
                        to map it to and submit the list by clicking submit button.
                    </p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Meat Tags Management</h3>
                    <div class="form-group">
                        <label>Select Partner</label>
                        <select class="form-control"
                                data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                data-ng-model="selectedPartner"
                                data-ng-change="setSelectedPartner(selectedPartner)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)">
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                            <h4>Tag Management</h4>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Tags"
                                               data-ng-click="getMeatTags()"/>
                                    </div>
                                </div>
                            </div>
                            <div data-ng-if="showMeatTagsView">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <label>New Tag Id</label>
                                            <input class="form-control" data-ng-model="newMeatTag.id"/>
                                        </div>
                                        <div class="form-group">
                                            <label>New Tag Name</label>
                                            <input class="form-control" data-ng-model="newMeatTag.name"/>
                                        </div>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Add new tag"
                                                   data-ng-click="addMeatTags()"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info" data-ng-if="meatTags.length == 0">No tags found. Please
                                    add some.
                                </div>
                                <table data-ng-if="meatTags.length > 0" class="table table-bordered table-striped">
                                    <tr>
                                        <td>Tag Id</td>
                                        <td>Tag Name</td>
                                        <td>Action</td>
                                    </tr>
                                    <tr data-ng-repeat="tag in meatTags | orderBy: 'id' track by $index">
                                        <td>{{tag.id}}</td>
                                        <td>{{tag.name}}</td>
                                        <td>
                                            <input type="button" class="btn btn-danger" value="Remove"
                                                   data-ng-click="removeMeatTags(tag.id)"/>
                                        </td>
                                    </tr>
                                </table>
                                <input type="button" class="btn btn-primary" value="Save tags"
                                       data-ng-click="setMeatTags()"/>
                            </div>



                            <div data-ng-if="showAllergenTagsView">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <label>New Allergen Id</label>
                                            <input class="form-control" data-ng-model="newAllergenTag.id"/>
                                        </div>
                                        <div class="form-group">
                                            <label>New Allergen Name</label>
                                            <input class="form-control" data-ng-model="newAllergenTag.name"/>
                                        </div>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Add new tag"
                                                   data-ng-click="addAllergenTags()"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info" data-ng-if="allergenTags.length == 0">No tags found. Please
                                    add some.
                                </div>
                                <table data-ng-if="allergenTags.length > 0" class="table table-bordered table-striped">
                                    <tr>
                                        <td>Allergen Id</td>
                                        <td>Allergen Name</td>
                                        <td>Action</td>
                                    </tr>
                                    <tr data-ng-repeat="tag in allergenTags | orderBy: 'id' track by $index">
                                        <td>{{tag.id}}</td>
                                        <td>{{tag.name}}</td>
                                        <td>
                                            <input type="button" class="btn btn-danger" value="Remove"
                                                   data-ng-click="removeAllergenTags(tag.id)"/>
                                        </td>
                                    </tr>
                                </table>
                                <input type="button" class="btn btn-primary" value="Save tags"
                                       data-ng-click="setAllergenTags()"/>
                            </div>





                        </div>
                        <div class="col-xs-6">
                            <h4>Product Meat Tag Mapping</h4>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Products"
                                               data-ng-click="getActiveProductsMeat()"/>
                                        <input type="button" class="btn btn-primary pull-right" value="Submit"
                                               data-ng-if="meatTagsMappings.length > 0"
                                               data-ng-click="submitMeatTagMappings()"/>
                                    </div>
                                </div>
                                <div data-ng-if="showMeatTagMappingView">
                                    <!--<div class="alert alert-info" data-ng-if="productsTagsMappings.length == 0">No tags found. Please add some.</div>-->
                                    <div class="form-group">
                                        <label>Search Product</label>
                                        <input type="text" class="form-control" data-ng-model="search"/>
                                    </div>
                                    <table data-ng-if="meatTagsMappings.length > 0"
                                           class="table table-bordered table-striped">
                                        <tr>
                                            <td>Product Id</td>
                                            <td>Product Name</td>
                                            <td>Product Tags</td>
                                            <td>Action</td>
                                        </tr>
                                        <tr data-ng-repeat="mapping in filtered = (meatTagsMappings | filter:search ) track by $index">
                                            <td>{{mapping.id}}</td>
                                            <td>{{mapping.name}}</td>
                                            <td><span class="badge"
                                                      data-ng-repeat="tag in mapping.tags track by tag.id">{{tag.name}}</span>
                                            </td>
                                            <td>
                                                <input type="button" class="btn btn-primary" value="Set tags"
                                                       data-ng-click="updateMeatTagMappings(mapping)"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'ALLERGEN TYPES'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage partner allergen types and map them to products.</h3>
                    <p>
                        <strong>To add manage tags:</strong> type the tag you want to add and submit, once its get
                        populated in the list you can remove it by clicking remove button.<br/>
                        <strong>To map tag to products:</strong> Locate the product in the list and select tag you want
                        to map it to and submit the list by clicking submit button.
                    </p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Allergen Types Management</h3>
                    <div class="form-group">
                        <label>Select Partner</label>
                        <select class="form-control"
                                data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                data-ng-model="selectedPartner"
                                data-ng-change="setSelectedPartner(selectedPartner)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)">
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                            <h4>Tag Management</h4>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Tags"
                                               data-ng-click="getAllergenTags()"/>
                                    </div>
                                </div>
                            </div>

                            <div data-ng-if="showAllergenTagsView">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <label>New Allergen Id</label>
                                            <input class="form-control" data-ng-model="newAllergenTag.id"/>
                                        </div>
                                        <div class="form-group">
                                            <label>New Allergen Name</label>
                                            <input class="form-control" data-ng-model="newAllergenTag.name"/>
                                        </div>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Add new tag"
                                                   data-ng-click="addAllergenTags()"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info" data-ng-if="allergenTags.length == 0">No tags found. Please
                                    add some.
                                </div>
                                <table data-ng-if="allergenTags.length > 0" class="table table-bordered table-striped">
                                    <tr>
                                        <td>Allergen Id</td>
                                        <td>Allergen Name</td>
                                        <td>Action</td>
                                    </tr>
                                    <tr data-ng-repeat="tag in allergenTags | orderBy: 'id' track by $index">
                                        <td>{{tag.id}}</td>
                                        <td>{{tag.name}}</td>
                                        <td>
                                            <input type="button" class="btn btn-danger" value="Remove"
                                                   data-ng-click="removeAllergenTags(tag.id)"/>
                                        </td>
                                    </tr>
                                </table>
                                <input type="button" class="btn btn-primary" value="Save tags"
                                       data-ng-click="setAllergenTags()"/>
                            </div>






                        </div>
                        <div class="col-xs-6">
                            <h4>Product Allergen Tag Mapping</h4>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Get Products"
                                               data-ng-click="getActiveProductsAllergen()"/>
                                        <input type="button" class="btn btn-primary pull-right" value="Submit"
                                               data-ng-if="allergenTagsMappings.length > 0"
                                               data-ng-click="submitAllergenTagMappings()"/>
                                    </div>
                                </div>
                                <div data-ng-if="showAllergenTagMappingView">
                                    <!--<div class="alert alert-info" data-ng-if="productsTagsMappings.length == 0">No tags found. Please add some.</div>-->
                                    <div class="form-group">
                                        <label>Search Product</label>
                                        <input type="text" class="form-control" data-ng-model="search"/>
                                    </div>
                                    <table data-ng-if="allergenTagsMappings.length > 0"
                                           class="table table-bordered table-striped">
                                        <tr>
                                            <td>Product Id</td>
                                            <td>Product Name</td>
                                            <td>Product Tags</td>
                                            <td>Action</td>
                                        </tr>
                                        <tr data-ng-repeat="mapping in filtered = (allergenTagsMappings | filter:search ) track by $index">
                                            <td>{{mapping.id}}</td>
                                            <td>{{mapping.name}}</td>
                                            <td><span class="badge"
                                                      data-ng-repeat="tag in mapping.tags track by tag.id">{{tag.name}}</span>
                                            </td>
                                            <td>
                                                <input type="button" class="btn btn-primary" value="Set tags"
                                                       data-ng-click="updateAllergenTagMappings(mapping)"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'DEAL OF THE DAY'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to update Deal of the Day Exclusion Products on partner side.</h3>
                    <p>Select all the products whose status you want to update and then select the partners where you
                        want to take action
                        and press the corresponding button.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Deal Of The Day Exclusion Products Selection</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Select Product</label>

                                <select data-ui-select2 class="form-control" style="width: 100% !important"
                                        data-ng-model="productsDTDO.id" data-placeholder="Select a product"
                                        data-ng-change="changeProduct(productsDTDO.id)">
                                    <option value=""></option>
                                    <option data-ng-repeat="product in productsDTDO" value="{{product.id}}">
                                        {{product.name + " : " + product.id + " - " + product.status}}
                                    </option>
                                </select>

                            </div>


                            <div class="form-group" data-ng-if="productDimension.content.length > 0">
                                <label>Select Dimension</label>


                                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                     options="productDimension.content" selected-model="selectedDimensions"
                                     class="control-label">
                                </div>


                            </div>

                            <div class="col-xs-12" style="text-align: right;">
                                <button type="button" class="btn btn-success" data-ng-show="selectedProduct"
                                        data-ng-click="addProductsDOTD()">Add as DOTD Product
                                </button>
                            </div>


                            <div class="row" data-ng-if="showUnitProducts">

                                <div>
                                <div class="col-xs-12" style="text-align: right;">
                                    <button type="button" class="btn btn-success" data-ng-if="selectedProductsDTDO.length > 0"
                                            data-ng-click="getDOTDList()"> GET DOTD LIST
                                    </button>
                                </div>
                                </div>
                                <div data-ng-if="selectedProductsDTDO.length > 0">
                                    <label>Search Product</label>
                                    <input type="text" class="form-control" data-ng-model="search"/>

                                    <table data-ng-if="selectedProductsDTDO.length > 0"
                                           class="table table-bordered table-striped ">
                                        <tr>
                                            <td>Product Id</td>
                                            <td>Product Name</td>
                                            <td>Dimensions</td>
                                            <td>Remove</td>
                                        </tr>

                                        <tr data-ng-repeat="mapping in filtered = (selectedProductsDTDO | filter:search ) track by $index">

                                            <td>{{mapping.productId}}</td>
                                            <td>{{mapping.productName}}</td>
                                            <td>
                                                <ul data-ng-repeat="dim in mapping.dotdProductDimensionsList">
                                                    <li>{{dim.dimensionName}}</li>
                                                </ul>
                                            </td>

                                            <td>
                                                <button type="button" class="btn btn-danger"
                                                        data-ng-click="removeProductsDOTD(mapping)"> Remove Product
                                                </button>
                                            </td>

                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!--<div class="col-xs-12" style="text-align: right;">-->
                                <!--<button type="button" class="btn btn-success"-->
                                        <!--data-ng-click="addProductsDOTD()">Add as DOTD Product-->
                                <!--</button>-->
                            <!--</div>-->
                        </div>

                    </div>


                </div>
                <div class="row" data-ng-if="showUnitProducts">
                    <div class="col-xs-12" style="text-align: right;">
                        <button type="button" class="btn btn-success"
                                data-ng-click="getDOTDList()"> GET DOTD LIST
                        </button>
                    <!--</div>-->
                    <!--<div class="col-xs-12" style="text-align: right;">-->
                        <button type="button" class="btn btn-primary"
                                data-ng-click="submitDOTDList()"> Submit DOTD List
                        </button>
                    </div>
                </div>



            </div>
        </div>
    </div>

</div>



<div class="row" data-ng-if="selectedAction == 'SERVING INFO'">
    <div class="col-xs-12">
        <div class="row alert alert-info">
            <div class="col-xs-12">
                <h3>Use this panel to manage partner serving info and map them to products.</h3>
                <p>
                    <strong>To add manage tags:</strong> type the tag you want to add and submit, once its get
                    populated in the list you can remove it by clicking remove button.<br/>
                    <strong>To map tag to products:</strong> Locate the product in the list and select tag you want
                    to map it to and submit the list by clicking submit button.
                </p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <h3>Serving Info Management</h3>
                <div class="form-group">
                    <label>Select Partner</label>
                    <select class="form-control"
                            data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                            data-ng-model="selectedPartner"
                            data-ng-change="setSelectedPartner(selectedPartner)">
                    </select>
                </div>
                <div class="form-group">
                    <label>Select Brand</label>
                    <select class="form-control"
                            data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                            data-ng-model="selectedBrand"
                            data-ng-change="setSelectedBrand(selectedBrand)">
                    </select>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <h4>Tag Management</h4>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" value="Get Tags"
                                           data-ng-click="getServingInfo()"/>
                                </div>
                            </div>
                        </div>

                        <div data-ng-if="showServingInfoView">
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label>New Serving Info Id</label>
                                        <input class="form-control" data-ng-model="newServingInfo.id"/>
                                    </div>
                                    <div class="form-group">
                                        <label>New Serving Info Name</label>
                                        <input class="form-control" data-ng-model="newServingInfo.name"/>
                                    </div>
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Add new tag"
                                               data-ng-click="addServingInfo()"/>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info" data-ng-if="servingInfo.length == 0">No tags found. Please
                                add some.
                            </div>
                            <table data-ng-if="servingInfo.length > 0" class="table table-bordered table-striped">
                                <tr>
                                    <td>Serving Info Id</td>
                                    <td>Serving Info Name</td>
                                    <td>Action</td>
                                </tr>
                                <tr data-ng-repeat="tag in servingInfo | orderBy: 'id' track by $index">
                                    <td>{{tag.id}}</td>
                                    <td>{{tag.name}}</td>
                                    <td>
                                        <input type="button" class="btn btn-danger" value="Remove"
                                               data-ng-click="removeServingInfo(tag.id)"/>
                                    </td>
                                </tr>
                            </table>
                            <input type="button" class="btn btn-primary" value="Save tags"
                                   data-ng-click="setServingInfo()"/>
                        </div>






                    </div>
                    <div class="col-xs-6">
                        <h4>Product Serving Info Tag Mapping</h4>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" value="Get Products"
                                           data-ng-click="getActiveProductsServingInfo()"/>
                                    <input type="button" class="btn btn-primary pull-right" value="Submit"
                                           data-ng-if="servingInfoTagsMappings.length > 0"
                                           data-ng-click="submitServingInfoMappings()"/>
                                </div>
                            </div>
                            <div data-ng-if="showServingInfoMappingView">
                                <!--<div class="alert alert-info" data-ng-if="productsTagsMappings.length == 0">No tags found. Please add some.</div>-->
                                <div class="form-group">
                                    <label>Search Product</label>
                                    <input type="text" class="form-control" data-ng-model="search"/>
                                </div>
                                <table data-ng-if="servingInfoTagsMappings.length > 0"
                                       class="table table-bordered table-striped">
                                    <tr>
                                        <td>Product Id</td>
                                        <td>Product Name</td>
                                        <td>Product Tags</td>
                                        <td>Action</td>
                                    </tr>
                                    <tr data-ng-repeat="mapping in filtered = (servingInfoTagsMappings | filter:search ) track by $index">
                                        <td>{{mapping.id}}</td>
                                        <td>{{mapping.name}}</td>
                                        <td><span class="badge"
                                                  data-ng-repeat="tag in mapping.tags track by tag.id">{{tag.name}}</span>
                                        </td>
                                        <td>
                                            <input type="button" class="btn btn-primary" value="Set tags"
                                                   data-ng-click="updateServingInfoMapping(mapping)"/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row" data-ng-if="selectedAction == 'SERVING SIZE'">
    <div class="col-xs-12">
        <div class="row alert alert-info">
            <div class="col-xs-12">
                <h3>Use this panel to manage partner Serving size and map them to products.</h3>
                <p>
                    <strong>To add manage tags:</strong> type the tag you want to add and submit, once its get
                    populated in the list you can remove it by clicking remove button.<br/>
                    <strong>To map tag to products:</strong> Locate the product in the list and select tag you want
                    to map it to and submit the list by clicking submit button.
                </p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <h3>Service Size Management</h3>
                <div class="form-group">
                    <label>Select Partner</label>
                    <select class="form-control"
                            data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                            data-ng-model="selectedPartner"
                            data-ng-change="setSelectedPartner(selectedPartner)">
                    </select>
                </div>
                <div class="form-group">
                    <label>Select Brand</label>
                    <select class="form-control"
                            data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                            data-ng-model="selectedBrand"
                            data-ng-change="setSelectedBrand(selectedBrand)">
                    </select>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <h4>Tag Management</h4>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" value="Get Tags"
                                           data-ng-click="getServingSizes()"/>
                                </div>
                            </div>
                        </div>
                        <!--<div data-ng-if="showServingSizeView">-->
                            <!--<div class="row">-->
                                <!--<div class="col-xs-12">-->
                                    <!--<div class="form-group">-->
                                        <!--<label>New Tag Id</label>-->
                                        <!--<input class="form-control" data-ng-model="newServingSize.id"/>-->
                                    <!--</div>-->
                                    <!--<div class="form-group">-->
                                        <!--<label>New Tag Name</label>-->
                                        <!--<input class="form-control" data-ng-model="newServingSize.name"/>-->
                                    <!--</div>-->
                                    <!--<div class="form-group">-->
                                        <!--<input type="button" class="btn btn-primary" value="Add new tag"-->
                                               <!--data-ng-click="addServingSizes()"/>-->
                                    <!--</div>-->
                                <!--</div>-->
                            <!--</div>-->
                            <!--<div class="alert alert-info" data-ng-if="servingSize.length == 0">No tags found. Please-->
                                <!--add some.-->
                            <!--</div>-->
                            <!--<table data-ng-if="servingSize.length > 0" class="table table-bordered table-striped">-->
                                <!--<tr>-->
                                    <!--<td>Tag Id</td>-->
                                    <!--<td>Serving Value</td>-->
                                    <!--<td>Serving Unit</td>-->
                                    <!--<td>Action</td>-->
                                <!--</tr>-->
                                <!--<tr data-ng-repeat="tag in servingSize | orderBy: 'id' track by $index">-->
                                    <!--<td>{{tag.id}}</td>-->
                                    <!--<td>{{tag.value}}</td>-->
                                    <!--<td>{{tag.unit}}</td>-->

                                    <!--<td>-->
                                        <!--<input type="button" class="btn btn-danger" value="Remove"-->
                                               <!--data-ng-click="removeServingSizes(tag.id)"/>-->
                                    <!--</td>-->
                                <!--</tr>-->
                            <!--</table>-->
                            <!--<input type="button" class="btn btn-primary" value="Save tags"-->
                                   <!--data-ng-click="setServingSizes()"/>-->
                        <!--</div>-->



                        <div data-ng-if="showServingSizeView">
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label>New Tag Id</label>
                                        <input class="form-control" data-ng-model="newServingSize.id"/>
                                    </div>
                                    <div class="form-group">
                                        <label>New Value</label>
                                        <input class="form-control" data-ng-model="newServingSize.value"/>
                                    </div>
                                    <!--<select class="form-control"-->
                                            <!--data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"-->
                                            <!--data-ng-model="selectedBrand"-->
                                            <!--data-ng-change="setSelectedBrand(selectedBrand)">-->
                                    <!--</select>-->
                                    <div class="form-group">
                                        <label>New Unit</label>
                                        <!--<input class="form-control" data-ng-model="newServingSize.unit"/>-->
                                        <select class="form-control"
                                                data-ng-options="unit for unit in servingSizeUnits"
                                                data-ng-model="newServingSize.unit">
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="button" class="btn btn-primary" value="Add new tag"
                                               data-ng-click="addServingSizes()"/>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info" data-ng-if="servingSize.length == 0">No tags found. Please
                                add some.
                            </div>
                            <table data-ng-if="servingSize.length > 0" class="table table-bordered table-striped">
                                <tr>
                                    <td>Tag Id</td>
                                    <td>Serving Value</td>
                                    <td>Serving Unit</td>
                                    <td>Action</td>
                                </tr>
                                <tr data-ng-repeat="tag in servingSize | orderBy: 'id' track by $index">
                                    <!--<td>{{tag}}</td>-->
                                    <td>{{tag.id}}</td>
                                    <td>{{tag.value}}</td>
                                    <td>{{tag.unit}}</td>
                                    <td>
                                        <input type="button" class="btn btn-danger" value="Remove"
                                               data-ng-click="removeServingSizes(tag.id)"/>
                                    </td>
                                </tr>
                            </table>
                            <input type="button" class="btn btn-primary" value="Save tags"
                                   data-ng-click="setServingSizes()"/>
                        </div>





                    </div>
                    <div class="col-xs-6">
                        <h4>Product Serving Size Mapping</h4>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" value="Get Products"
                                           data-ng-click="getActiveProductsServingSize()"/>
                                    <input type="button" class="btn btn-primary pull-right" value="Submit"
                                           data-ng-if="servingSizeMappings.length > 0"
                                           data-ng-click="submitServingSizeMappings()"/>
                                </div>
                            </div>
                            <div data-ng-if="showServingSizeMappingView">
                                <!--<div class="alert alert-info" data-ng-if="productsTagsMappings.length == 0">No tags found. Please add some.</div>-->
                                <div class="form-group">
                                    <label>Search Product</label>
                                    <input type="text" class="form-control" data-ng-model="search"/>
                                </div>
                                <table data-ng-if="servingSizeMappings.length > 0"
                                       class="table table-bordered table-striped">
                                    <tr>
                                        <td>Product Id</td>
                                        <td>Product Name</td>
                                        <td>Product Tags</td>
                                        <td>Action</td>
                                    </tr>
                                    <!--<td>{{servingSizeMappings}}</td>-->
                                    <tr data-ng-repeat="mapping in filtered = (servingSizeMappings | filter:search ) track by $index">
                                        <td>{{mapping.id}}</td>
                                        <td>{{mapping.name}}</td>
                                        <td><span class="badge" data-ng-if="mapping.tags.id">{{mapping.tags.value}} {{mapping.tags.unit}}</span>
                                        </td>
                                        <td>
                                            <input type="button" class="btn btn-primary" value="Set tags"
                                                   data-ng-click="updateServingSizeMappings(mapping)"/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>






<div class="modal fade" id="productTagMappingModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Set Tags for {{updateTagObject.product.name}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Update mappings"
                               data-ng-click="setProductTagMapping()"/>
                    </div>
                </div>
                <table class="table table-bordered table-striped">
                    <tr>
                        <td>Tag</td>
                        <td>Action</td>
                    </tr>
                    <tr data-ng-repeat="tag in updateTagObject.tags track by $index" data-ng-click="selectTag(tag)"
                        style="cursor: pointer">
                        <td>{{tag.name}}</td>
                        <td>
                            <span data-ng-if="tag.selected" class="fa fa-check"
                                  style="color: green; font-size: 32px;"></span>
                            <span data-ng-if="!tag.selected" class="fa fa-check"
                                  style="color: #ddd; font-size: 32px;"></span>
                        </td>
                    </tr>
                </table>
                <input type="button" class="btn btn-primary" value="Update mappings"
                       data-ng-click="setProductTagMapping()"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="productMeatTagMappingModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModal">Set Tags for {{updateMeatTagObject.product.name}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Update mappings"
                               data-ng-click="setProductMeatTagMapping()"/>
                    </div>
                </div>
                <table class="table table-bordered table-striped">
                    <tr>
                        <td>Tag</td>
                        <td>Action</td>
                    </tr>
                    <tr data-ng-repeat="tag in updateMeatTagObject.tags track by $index" data-ng-click="selectTag(tag)"
                        style="cursor: pointer">
                        <td>{{tag.name}}</td>
                        <td>
                            <span data-ng-if="tag.selected" class="fa fa-check"
                                  style="color: green; font-size: 32px;"></span>
                            <span data-ng-if="!tag.selected" class="fa fa-check"
                                  style="color: #ddd; font-size: 32px;"></span>
                        </td>
                    </tr>
                </table>
                <input type="button" class="btn btn-primary" value="Update mappings"
                       data-ng-click="setProductMeatTagMapping()"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="productAllergenTagMappingModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myyModal">Set Tags for {{updateAllergenTagObject.product.name}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Update mappings"
                               data-ng-click="setProductAllergenTagMapping()"/>
                    </div>
                </div>
                <table class="table table-bordered table-striped">
                    <tr>
                        <td>Tag</td>
                        <td>Action</td>
                    </tr>
                    <tr data-ng-repeat="tag in updateAllergenTagObject.tags track by $index" data-ng-click="selectTag(tag)"
                        style="cursor: pointer">
                        <td>{{tag.name}}</td>
                        <td>
                            <span data-ng-if="tag.selected" class="fa fa-check"
                                  style="color: green; font-size: 32px;"></span>
                            <span data-ng-if="!tag.selected" class="fa fa-check"
                                  style="color: #ddd; font-size: 32px;"></span>
                        </td>
                    </tr>
                </table>
                <input type="button" class="btn btn-primary" value="Update mappings"
                       data-ng-click="setProductAllergenTagMapping()"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="productServingInfoMappingModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="mModal">Set Tags for {{updateServingInfoObject.product.name}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Update mappings"
                               data-ng-click="setProductServingInfoMapping()"/>
                    </div>
                </div>
                <table class="table table-bordered table-striped">
                    <tr>
                        <td>Tag</td>
                        <td>Action</td>
                    </tr>
                    <tr data-ng-repeat="tag in updateServingInfoObject.tags track by $index" data-ng-click="selectTag(tag)"
                        style="cursor: pointer">
                        <td>{{tag.name}}</td>
                        <td>
                            <span data-ng-if="tag.selected" class="fa fa-check"
                                  style="color: green; font-size: 32px;"></span>
                            <span data-ng-if="!tag.selected" class="fa fa-check"
                                  style="color: #ddd; font-size: 32px;"></span>
                        </td>
                    </tr>
                </table>
                <input type="button" class="btn btn-primary" value="Update mappings"
                       data-ng-click="setProductServingInfoMapping()"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="productServingSizeMappingModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="mmModal">Set Tags for {{updateServingSizeObject.product.name}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Update mappings"
                               data-ng-click="setProductServingSizeMapping()"/>
                    </div>
                </div>
                <table class="table table-bordered table-striped">
                    <tr>
                        <td>Tag</td>
                        <td>Action</td>
                    </tr>
                    <tr data-ng-repeat="tag in updateServingSizeObject.tags track by $index" data-ng-click="selectTag(tag)"
                        style="cursor: pointer">
                        <td>{{tag.value}}</td>
                        <td>{{tag.unit}}</td>

                        <td>
                            <span data-ng-if="tag.selected" class="fa fa-check"
                                  style="color: green; font-size: 32px;"></span>
                            <span data-ng-if="!tag.selected" class="fa fa-check"
                                  style="color: #ddd; font-size: 32px;"></span>
                        </td>
                    </tr>
                </table>
                <input type="button" class="btn btn-primary" value="Update mappings"
                       data-ng-click="setProductServingSizeMapping()"/>
            </div>
        </div>
    </div>
</div>

