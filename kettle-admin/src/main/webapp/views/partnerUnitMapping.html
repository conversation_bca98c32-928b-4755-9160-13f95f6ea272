<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .multiselect-parent .dropdown-menu {
        width: 570px;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Unit Mapping</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'UNIT MAPPING'" data-ng-init="getAllRegionsDetails()" >
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage unit channel partner mapping.</h3>
                    <p>Whenever a new unit is mapped to channel partner then the mapping needs to be updated here.
                        Select unit, select partner and press corresponding buttons</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Unit Channel Partner Mapping</h3>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="form-group">
                                    <label>Select Region</label>
                                    <select data-ng-model="crmObject.city" class='form-control'
                                            data-ng-options="city for city in regions"
                                            data-ng-change="getUserDetail(crmObject.city)"
                                            ng-disabled="isUpdateScreen"
                                            required="required">
                                    </select>
                                </div>
                                <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Add mapping"
                                       data-ng-click="addUnitChannelPartnerMapping()"/>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>
                            </div>
                            <table class="table table-bordered table-striped">
                                <tr>
                                    <th>Id</th>
                                    <th>Unit</th>
                                    <th>Partner</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                                <tr data-ng-repeat="mapping in filtered = (unitChannelPartnerMappings | filter:search | orderBy : predicate :reverse) track by mapping.id">
                                    <td>{{mapping.id}}</td>
                                    <td>{{mapping.unit.name}}</td>
                                    <td>{{mapping.channelPartner.name}}</td>
                                    <td>{{mapping.status}}</td>
                                    <td>
                                        <button class="btn btn-success" data-ng-if="mapping.status == 'IN_ACTIVE'"
                                                data-ng-click="updateUnitChannelPartnerMapping(mapping, true)">Activate
                                        </button>
                                        <button class="btn btn-danger" data-ng-if="mapping.status == 'ACTIVE'"
                                                data-ng-click="updateUnitChannelPartnerMapping(mapping, false)">
                                            De-activate
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'UNIT PRICING'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage unit channel partner pricing mapping.</h3>
                    <p>Whenever a new unit is mapped to channel partner then the mapping needs to be updated here.
                        Select unit, select partner and press corresponding buttons</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Unit Partner Pricing Mapping</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get mappings"
                                       data-ng-click="getUnitPartnerBrandMapping(this)"/>
                                <input type="button" class="btn btn-primary" value="Add mappings"
                                       data-ng-click="setShowCodUnitSelection(true)"/>
                                <input type="button" class="btn btn-success pull-right" value="Refresh mappings cache"
                                       data-ng-click="refreshUnitPartnerBrandMappingCache()"/>
                            </div>
                            <div class="row" data-ng-show="showCodUnitSelection">
                                <div class="col-xs-12">
                                    <label>Select Unit</label>
                                    <select style ="margin-bottom:10px" class="form-control"
                                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                            data-ng-model="selectedUnit"
                                            data-ng-change="setSelectedUnit(selectedUnit)">
                                    </select>
                                    <label>Select Pricing Unit</label>
                                    <select style ="margin-bottom:10px" class="form-control"
                                            data-ng-options="unit as unit.name for unit in codUnitList track by unit.id"
                                            data-ng-model="selectedCodUnit"
                                            data-ng-change="setSelectedCodUnit(selectedCodUnit)">
                                    </select>
                                    <label>Enter Restaurant_Id:</label>
                                    <input style="margin-bottom: 10px" type="text" class="form-control" data-ng-model="restaurantId" data-ng-change="saveRestaurantId(restaurantId)" />
                                    <label>Select Live Date</label>
                                    
                                    <input class="form-control" type="date" data-ng-model="selectLiveDate" data-ng-change="setSelectLiveDate(selectLiveDate)" >
                                    <label class="checkbox-inline" style="margin-bottom: 15px; margin-top: 10px;">
                                       <input type="checkbox" data-ng-model="swiggyCloudKitchen" data-ng-change="setSwiggyCloudKitchen(swiggyCloudKitchen)">Swiggy cloud kitchen
                                   </label>
                                </div>
                                <div class="form-group">
                                    <input style="padding-left: 5px" type="button" class="btn btn-primary" value="Submit mapping"
                                           data-ng-click="addUnitPartnerBrandMapping()"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>
                            </div>
                            <table class="table table-bordered table-striped"
                                   data-ng-if="unitPartnerBrandMappings.length>0">
                                <tr>
                                    <th><input type="checkbox" ng-click="selectAllMapping()" /></th>
                                    <th>Unit</th>
                                    <th>Restaurant Id</th>
                                    <th>Partner</th>
                                    <th>Brand</th>
                                    <th>Pricing Unit</th>
                                    <th>Status</th>
                                    <th>Swiggy Cloud kitchen</th>
                                    <th>Live Date</th>
                                    <th>Actions
                                    <br>
                                    <select data-ng-model="allActions" data-ng-options="actions.value as actions.name for actions in mappingActions" data-ng-change="setMappingActions(allActions)">
                                    </select>
                                    </th>
                                </tr>
                                <tr data-ng-repeat="mapping in filtered=(unitPartnerBrandMappings | filter:search | orderBy : predicate :reverse)  track by $index">
                                    <td><input type="checkbox" ng-model="mapping.isChecked" checked="mapping.isChecked" /></td>
                                    <td>{{mapping.unit.name}}</td>
                                    <td>{{mapping.restaurantId}}</td>
                                    <td>{{mapping.partner.name}}</td>
                                    <td>{{mapping.brand.brandName}}</td>
                                    <td>{{mapping.pricingUnit.name}}</td>
                                    <td>{{mapping.status}}</td>
                                    <td>{{mapping.swiggyCloudKitchen}}</td>
                                    <td><div data-ng-if="mapping.liveDate != null">
                                        {{ mapping.liveDate | date :'dd/MM/yyyy'}}
                                        <div><input style="padding: 5px; margin-top: 10px; margin-left: 10px; " type="button" class="btn btn-primary"  value="Edit" data-ng-show="hasFinanceHeadPermissions" data-ng-click="setEditEnable(mapping.mappingId)" data-ng-if="selectUpdateLiveDateFlags[mapping.mappingId]==null" /> </div>
                                        <div data-ng-if="selectUpdateLiveDateFlags[mapping.mappingId]!=null">
                                        <input class="form-control" type="date" data-ng-model="selectEditLiveDate[mapping.mappingId]" data-ng-change="setSelectEditLiveDate(selectEditLiveDate,mapping.mappingId)" />
                                        <div data-ng-if="selectEditLiveDateFlags[mapping.mappingId]==null"><input style="padding: 5px; margin-top: 10px; margin-left: 10px; " type="button" class="btn btn-primary"  value="Update" data-ng-click="addUnitPartnerBrandMapping(mapping)"/> </div>
                                        <div data-ng-if="selectEditLiveDateFlags[mapping.mappingId]==true"><input style="padding: 5px; margin-top: 10px; margin-left: 10px; " type="button" class="btn btn-primary"  value="Update" disabled /> </div>
                                        <div data-ng-if="selectUpdateLiveDateFlags[mapping.mappingId]!=null"><input style="padding: 5px; margin-top: 10px; margin-left: 10px; " type="button" class="btn btn-danger"  value="Cancel" data-ng-click="setEditEnable(mapping.mappingId,'cancel')" /> </div>
                                       </div>
                                        
                                    </div>
                                    <div data-ng-if="mapping.liveDate == null">
                                        <input class="form-control" type="date" data-ng-model="selectEditLiveDate[mapping.mappingId]" data-ng-change="setSelectEditLiveDate(selectEditLiveDate,mapping.mappingId)" />
                                        <div data-ng-if="selectEditLiveDateFlags[mapping.mappingId]==null"><input style="padding: 5px; margin-top: 10px; margin-left: 10px; " type="button" class="btn btn-primary"  value="Add" data-ng-click="addUnitPartnerBrandMapping(mapping)"/> </div>
                                        <div data-ng-if="selectEditLiveDateFlags[mapping.mappingId]==true"><input style="padding: 5px; margin-top: 10px; margin-left: 10px; " type="button" class="btn btn-primary"  value="Add" disabled /> </div>
                                    </div>
                                </td>
                                    <td>
                                        <button class="btn btn-success"
                                                data-ng-if="mapping.status == 'IN_ACTIVE'"
                                                data-ng-click="updateUnitPartnerBrandMapping(mapping, true)">
                                            Activate
                                        </button>
                                        <button class="btn btn-danger"
                                                data-ng-if="mapping.status == 'ACTIVE'"
                                                data-ng-click="updateUnitPartnerBrandMapping(mapping, false)">
                                            De-activate
                                        </button>
                                    </td>
                                </tr>
                            </table>
                            <input type="button" ng-show="unitPartnerBrandMappings.length>0"  class="btn btn-primary" ng-click="bulkUpdateMappingStatus()" value="Submit">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'UNIT MENU MAPPING'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to map menu to partner unit.</h3>
                    <p>Menus are product group clusters combined together in proper sequence.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label>Select Application</label>
                        <select class="form-control"
                                data-ng-options="app as app.name for app in appList track by app.id"
                                data-ng-model="selectedApp"
                                data-ng-change="setSelectedApp(selectedApp)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Partner</label>
                        <select class="form-control"
                                data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                data-ng-model="selectedPartner"
                                data-ng-change="setSelectedPartner(selectedPartner)">
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Menu Type</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="optedMenuType"
                                    data-ng-change="onSelectingMenuType(optedMenuType)"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="type in menuType" value={{type}}>
                                    {{type}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Menu Sequence</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedMenu"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="menu in filteredMenu" value={{menu}}>
                                    {{menu.menuSequenceName}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <input type="button" class="btn btn-primary" value="Get Mappings"
                               data-ng-click="getUnitMenuMapping(optedMenuType,selectedMenu,this)">
                        <input type="button" class="btn btn-success" value="Add Mappings"
                               data-ng-click="addMenuMappings()">
                        <!--<input type="button" class="btn btn-success pull-right" value="Refresh Mapping Cache"-->
                               <!--data-ng-click="refreshUnitMenuMappingCache()">-->
                    </div>



                    <div class="row" data-ng-if="regions.length > 0">

                        <div class="card" data-ng-repeat="r in regions  | orderBy" >
                            <div>

                                <div class="col-xs-6 region-card" data-ng-if="$index % 2 == 0"
                                >
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"
                                            data-ng-click="getMenuDetails(r)"
                                            data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-xs-6 region-card" data-ng-if="$index % 2 != 0"
                                >
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"
                                            data-ng-click="getMenuDetails(r)"
                                            data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group" data-ng-show="unitMenuMappings != null && unitMenuMappings.length > 0 && menuMapping">
                        <label>Search</label>
                        <input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>
                    </div>
                    <table class="table table-striped table-bordered"
                           data-ng-if="unitMenuMappings != null && unitMenuMappings.length > 0">
                        <tr>
                            <th><input type="checkbox" ng-click="selectAllMenuMapping()" /></th>
                            <th>Unit ID</th>
                            <th>Unit</th>
                            <th>Partner</th>
                            <th>Brand</th>
                            <th>Menu Sequence</th>
                            <th>Menu Type</th>
                            <th>Status</th>
                            <th>Updated By</th>
                            <th>Updated At</th>
                            <th>Actions
                                <br>
                                <select data-ng-model="allActions" data-ng-options="actions.value as actions.name for actions in mappingActions" data-ng-change="setMappingActions(allActions)">
                                </select>
                            </th>
                        </tr>
                        <tr data-ng-repeat="mapping in filtered = (unitMenuMappings | filter:search|filter:setMenuTypeFilter | orderBy : predicate :reverse) track by mapping.id">
                            <td><input type="checkbox" ng-model="mapping.isChecked" checked="mapping.isChecked" /></td>
                            <td>{{mapping.unit.id}}</td>
                            <td>{{mapping.unit.name}}</td>
                            <td>{{mapping.channelPartner.name}}</td>
                            <td>{{mapping.brand.name}}</td>
                            <td>{{mapping.menuSequence.name}}</td>
                            <td>{{mapping.menuType}}</td>
                            <td>{{mapping.status}}</td>
                            <td>{{mapping.updatedBy.name}}</td>
                            <td>{{mapping.updatedAt}}</td>
                            <td>
                                <input type="button" class="btn btn-success" value="Activate"
                                       data-ng-click="activateMenuMapping(mapping, 'ACTIVE')"
                                       data-ng-if="mapping.status == 'IN_ACTIVE'"/>
                                <input type="button" class="btn btn-danger" value="De-activate"
                                       data-ng-click="activateMenuMapping(mapping, 'IN_ACTIVE')"
                                       data-ng-if="mapping.status == 'ACTIVE'"/>
                            </td>
                        </tr>
                    </table>
                    <input type="button" ng-show="unitMenuMappings.length>0"  class="btn btn-primary pull-right" ng-click="bulkUpdateMenuMappingStatus()" value="Submit">
                </div>
            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'UNIT RECOMMENDATION MENU MAPPING'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to map recommendation to partner unit.</h3>

                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label>Select Application</label>
                        <select class="form-control"
                                data-ng-options="app as app.name for app in appList track by app.id"
                                data-ng-model="selectedApp"
                                data-ng-change="setSelectedApp(selectedApp)"
                        >
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)"

                        >
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Partner</label>
                        <select class="form-control"
                                data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                data-ng-model="selectedPartner"
                                data-ng-change="setSelectedPartner(selectedPartner)"
                        >

                        </select>
                    </div>
                    <div class="form-group">
                        <label>Menu Type</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedMenuType"
                                    data-ng-change="onSelectingMenuType(selectedMenuType)"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="type in menuType" value={{type}}>
                                    {{type}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Menu Sequence</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedMenuSequence"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="menu in filteredMenu" value={{menu}}>
                                    {{menu.menuSequenceName}}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Recommendation</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedRecommendation"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="recommendation in recommendations" value={{recommendation}}>
                                    {{recommendation.menuRecommendationName}}
                                </option>
                            </select>
                        </div>
                    </div>



                    <div class="form-group">
                        <input type="button" class="btn btn-primary" value="Get Mappings"
                               data-ng-click="onSelectingDetalis(selectedMenuType,selectedMenuSequence,selectedRecommendation)">
                        <!--<input type="button" class="btn btn-success" value="Add Mappings"-->
                               <!--data-ng-click="addRecommendationMenuMappings()">-->
                        <!--<input type="button" class="btn btn-success pull-right" value="Refresh Mapping Cache"-->
                               <!--data-ng-click="refreshUnitMenuMappingCache()">-->
                    </div>

                    <div class="row" data-ng-if="regions.length > 0">

                        <div class="card" data-ng-repeat="r in regions  | orderBy" data-ng-model="selectedArea">
                            <div>

                                <div class="col-xs-6 region-card" data-ng-if="$index % 2 == 0"
                                >
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"
                                            data-ng-click="getDetails(r)"
                                            data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-xs-6 region-card" data-ng-if="$index % 2 != 0"
                                >
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"
                                            data-ng-click="getDetails(r)"
                                            data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row"
                     data-ng-if="menuRecommendationMappings != null && menuRecommendationMappings.length > 0 && recommendationMapping && selectedRegion !=null">
                    <div class="col-xs-12">
                        <table class="table table-bordered">
                            <thead style="background-color: #50773e; color: #ffffff">
                            <tr>
                                <th>Check &nbsp;
                                    <!--<input type="checkbox" data-ng-model='checkBoxModal.checkAll'-->
                                           <!--style="width: 20px; height: 20px" data-ng-click="updateAll()">-->
                                    <input type="checkbox" style="width: 33px; height: 20px" data-ng-model="checkBoxModal.checkAllRecommendation"
                                           data-ng-click="changeAllRecommendation(checkBoxModal.updatedRecommendation)">
                                </th>
                                <th>Unit Name&nbsp;</th>
                                <th>Menu Sequence&nbsp;</th>
                                <th>Menu Type&nbsp;</th>
                                <th>
                                    <table style="width: 100%;">
                                        <tr>
                                            <td colspan="3" align="center">Recommendation</td>
                                        </tr>
                                        <tr>
                                            <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                            <td>
                                                <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                                        data-ng-model="checkBoxModal.updatedRecommendation">
                                                    <option data-ng-repeat="recommendation in recommendations|filter: filterRecommendationStatus"
                                                            value="{{recommendation.menuRecommendationId}}">
                                                        {{recommendation.menuRecommendationName}}
                                                    </option>
                                                    <option value="Cancel Recommendation"> Cancel Recommendation</option>
                                                    <option value=""> </option>
                                                </select>
                                            </td>
                                            <!--<td>-->
                                                <!--<input type="checkbox" style="width: 33px; height: 20px" data-ng-model="checkBoxModal.checkAllRecommendation"-->
                                                       <!--data-ng-click="changeAllRecommendation(checkBoxModal.updatedRecommendation)">-->
                                            <!--</td>-->
                                        </tr>
                                    </table>
                                </th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat='detail in menuRecommendationMappings|filter:setMenuTypeFilter'
                                data-ng-class="{'row-selected': unitDetails[detail.id].checked}">

                                <td><input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model='unitDetails[detail.id].checked'
                                           >
                                </td>
                                <td>{{detail.unit.name}}</td>
                                <td style="width: 125px; height: 20px">{{detail.menuSequence.name}}</td>
                                <td style="width: 125px; height: 20px">{{detail.menuType}}</td>
                                <td>
                                    <table>
                                        <tr>
                                            <td style="width: 125px; height: 20px"  data-ng-if="detail.recommendation==null ">Recommendation not mapped</td>
                                            <td style="width: 125px; height: 20px"  data-ng-if="detail.recommendation!=null ">{{detail.recommendation}}</td>

                                            <td style="width: 100px;">
                                                <select class="form-control" data-ng-model="unitDetails[detail.id].recommendation">
                                                    <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                                    <option data-ng-repeat="recommendation in recommendations|filter:filterRecommendationStatus | orderBy"
                                                            value="{{recommendation.menuRecommendationId}}">{{recommendation.menuRecommendationName}}
                                                    </option>
                                                    <option value="Cancel Recommendation"> Cancel Recommendation</option>
                                                    <option value=""> </option>
                                                </select>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="right" colspan="8">
                                    <button class="btn btn-primary pull-right" data-ng-click="submitRecommendationMapping()">
                                        SUBMIT
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'UNIT PRICE PROFILE MAPPING'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to map price profile to partner unit.</h3>

                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label>Select Application</label>
                        <select class="form-control"
                                data-ng-options="app as app.name for app in appList track by app.id"
                                data-ng-model="selectedApp"
                                data-ng-change="setSelectedApp(selectedApp)"
                        >
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)"

                        >
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Partner</label>
                        <select class="form-control"
                                data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                data-ng-model="selectedPartner"
                                data-ng-change="setSelectedPartner(selectedPartner)"
                        >

                        </select>
                    </div>
                    <div class="form-group">
                        <label>Menu Type</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedMenuType"
                                    data-ng-change="onSelectingMenuType(selectedMenuType)"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="type in menuType" value={{type}}>
                                    {{type}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Menu Sequence</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedMenuSequence"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="menu in filteredMenu" value={{menu}}>
                                    {{menu.menuSequenceName}}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Price Profile</label>
                        <div class="form-group">
                            <select class="form-control"
                                    data-ng-model="selectedPriceProfile"
                            >
                                <option value="ALL">ALL</option>
                                <option ng-repeat="profile in priceProfile" value={{profile}}>
                                    {{profile.profileDescription}}
                                </option>
                            </select>
                        </div>
                    </div>



                    <div class="form-group">
                        <input type="button" class="btn btn-primary" value="Get Mappings"
                               data-ng-click="onSelectingPriceProfileDetails(selectedMenuType,selectedMenuSequence,selectedPriceProfile)">
                        <!--<input type="button" class="btn btn-success" value="Add Mappings"-->
                               <!--data-ng-click="addRecommendationMenuMappings()">-->
                        <!--<input type="button" class="btn btn-success pull-right" value="Refresh Mapping Cache"-->
                               <!--data-ng-click="refreshUnitMenuMappingCache()">-->
                    </div>


                    <div class="row" data-ng-if="regions.length > 0">

                        <div class="card" data-ng-repeat="r in regions  | orderBy" data-ng-model="selectedArea">
                            <div>

                                <div class="col-xs-6 region-card" data-ng-if="$index % 2 == 0"
                                >
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"
                                            data-ng-click="getPriceProfileDetails(r)"
                                            data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-xs-6 region-card" data-ng-if="$index % 2 != 0"
                                >
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"
                                            data-ng-click="getPriceProfileDetails(r)"
                                            data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row"
                     data-ng-if="menuRecommendationMappings != null && menuRecommendationMappings.length > 0 && recommendationMapping && selectedRegion !=null">
                    <div class="col-xs-12">
                        <table class="table table-bordered">
                            <thead style="background-color: #50773e; color: #ffffff">
                            <tr>
                                <th>Check &nbsp;
                                    <input type="checkbox" data-ng-model='checkBoxModal.checkAll'
                                           style="width: 20px; height: 20px" data-ng-click="updateAll()">
                                </th>
                                <th>Unit Name&nbsp;</th>
                                <th>Menu Sequence&nbsp;</th>
                                <th>Menu Type&nbsp;</th>
                                <th>
                                    <table style="width: 100%;">
                                        <tr>
                                            <td colspan="3" align="center">Price Profile</td>
                                        </tr>
                                        <tr>
                                            <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                            <td>
                                                <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                                        data-ng-model="checkBoxModal.updatedRecommendation">
                                                    <option data-ng-repeat="profile in priceProfile|filter: filterPriceProfileStatus"
                                                            value="{{profile.priceProfileId}}">
                                                        {{profile.profileDescription}}
                                                    </option>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="checkbox" style="width: 33px; height: 20px" data-ng-model="checkBoxModal.checkAllRecommendation"
                                                       data-ng-click="changeAllPriceProfile(checkBoxModal.updatedRecommendation)">
                                            </td>
                                        </tr>
                                    </table>
                                </th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat='detail in menuRecommendationMappings|filter:setMenuTypeFilter'
                                data-ng-class="{'row-selected': unitDetails[detail.id].checked}">

                                <td><input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model='unitDetails[detail.id].checked'
                                           >
                                </td>
                                <td>{{detail.unit.name}}</td>
                                <td style="width: 125px; height: 20px">{{detail.menuSequence.name}}</td>
                                <td style="width: 125px; height: 20px">{{detail.menuType}}</td>
                                <td>
                                    <table>
                                        <tr>
                                            <td style="width: 125px; height: 20px" >{{detail.profileDescription}}</td>
                                            <td style="width: 100px;">
                                                <select class="form-control" data-ng-model="unitDetails[detail.id].priceProfile">
                                                    <option data-ng-repeat="profile in priceProfile|filter: filterPriceProfileStatus"
                                                            value="{{profile.priceProfileId}}">
                                                        {{profile.profileDescription}}
                                                    </option>
                                                </select>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="right" colspan="8">
                                    <button class="btn btn-primary pull-right" data-ng-click="submitPriceProfileMapping()">
                                        SUBMIT
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>
        </div>
    </div>


</div>

<div class="modal fade" id="addMenuMappingModal" tabindex="-1" role="dialog" aria-labelledby="addMenuMappingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addMenuMappingModalLabel">Add menu mapping for {{selectedPartner.name}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label>Region</label>
                            <select data-ng-model="selectedRegion" class='form-control'
                                    data-ng-options="region for region in unitMenuMappingRegions"
                                    data-ng-change="getUserDetails(selectedRegion)"
                                    ng-disabled="isUpdateScreen"
                                    required="required">
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Select Unit</label>
                            <div class="form-group region-card">
                                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                     options="unitPartnerMappings"
                                     selected-model="storeSelectedUnits" class="region-card">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Menu Type</label>
                            <div ng-show="selectedPartner.name=='ZOMATO'
                                || selectedPartner.name=='MAGICPIN'
                            || selectedPartner.id==1 || selectedPartner.name=='DINE IN APP' || selectedPartner.name=='SWIGGY'">
                                <select class="form-control"
                                        data-ng-options="type for type in menuType "
                                        data-ng-model="newMenuMapping.menuType"
                                        data-ng-change="onSelectingMenuType(newMenuMapping.menuType)"
                                >
                                </select>
                            </div>
                            <div ng-show="selectedPartner.name!='ZOMATO' && selectedPartner.id!=1 && selectedPartner.name!='DINE IN APP' && selectedPartner.name!='SWIGGY'">
                                <input class="form-control"
                                       data-ng-model="newMenuMapping.menuType"
                                       data-ng-disabled="true"
                                />
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Menu Sequence</label>
                            <select class="form-control"
                                    data-ng-options="menu as menu.menuSequenceName for menu in filteredMenu"
                                    data-ng-model="selectedMenu"
                                    data-ng-change="setSelectedMenu(selectedMenu)">
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 text-right" style="margin-top: 10px;">
                                <input type="button" class="btn btn-primary" value="Set Menu"
                                       data-ng-click="addNewMenuMapping()"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
