<style>
    .brand-item {
        display: flex;
        align-items: center;
        padding: 5px;
    }

    .brand-item label {
        cursor: pointer;
    }

    .brand-item input[type="checkbox"] {
        margin-right: 8px;
        transform: scale(1.2);
    }
</style>

<div class="modal-lg" ng-init=ab.init()>
    <div class="modal-header">
        <button type="button" class="close" ng-click="ab.cancel()">
            <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="modal-title">Manage Unit - {{ab.completeUnitObj.name}}</h4>
    </div>
    <div class="modal-body" id="modal-body">
        <uib-tabset active="ab.index">
            <uib-tab index="0" heading="Basic Detail" disable="ab.index!=0">
                <h3>Unit Info</h3>
                <div class="form-group">
                    <label>Family *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.family"
                            ng-options="item for item in ab.families track by item"
                            data-ng-change="ab.getKioskCompanyList(ab.completeUnitObj.family)">
                    </select>
                </div>
                <div id="idChaiMonkDiv" ng-if="ab.completeUnitObj.family==='CHAI_MONK'">
                    <div class="form-group">
                        <label>Company Name *</label>
                        <select class="form-control" data-ng-model="ab.selectedCompany"
                                ng-options="companyViewData as companyViewData.companyName for companyViewData in ab.kioskCompanyList track by companyViewData.companyId"
                                data-ng-change="ab.getKioskOfficeList(ab.selectedCompany)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Office Name *</label>
                        <select class="form-control" data-ng-model="ab.selectedOffice"
                                ng-options="officeData as officeData.officeName for officeData in ab.kioskOfficeList track by officeData.officeId"
                                data-ng-change="ab.getLocationList(ab.selectedOffice)"></select>
                    </div>
                    <div class="form-group">
                        <label>Location Name *</label>
                        <select class="form-control" data-ng-model="ab.selectedLocation"
                                data-ng-change="ab.setBasicChaiMonkInfo(ab.selectedLocation)"
                                ng-options="locationData as locationData.locationName for locationData in ab.kioskLocationList track by locationData.locationId"></select>
                    </div>
                    <div class="form-group">
                        <label>Location*</label>
                        <select class="form-control" data-ng-model="ab.completeUnitObj.region"
                                ng-options="item for item in ab.regions track by item">
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label>Company *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.company"
                            ng-options="company.name for company in ab.newUnitCompanyList track by company.id"
                            data-ng-change="onCompanyChange(ab.completeUnitObj.company.id)">
                    </select>
                </div>
                <div class="form-group">
                    <label>Brand(s) *</label>
                    <div ng-repeat="brand in companyBrandMap" class="brand-item">
                        <label>
                            <input type="checkbox"
                                   ng-model="brand.checked"
                                   ng-change="toggleBrandSelection(brand)">
                            {{ brand.brandName }}
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label>Unit Name *</label>
                    <input ng-model="ab.completeUnitObj.name" class="form-control"
                           style="text-transform: capitalize"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           type="text" name="unitName"/>
                </div>
                <div class="form-group">
                    <label>Reference Name *</label>
                    <input ng-model="ab.completeUnitObj.referenceName" class="form-control"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           style="text-transform: capitalize"
                           type="text"/>
                </div>
                <div class="form-group">
                    <label>Short Name *</label>
                    <input ng-model="ab.completeUnitObj.shortName" class="form-control"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           style="text-transform: capitalize"
                           type="text" maxlength="10"/>
                </div>
                <div class="form-group">
                    <label>Short Code *</label>
                    <input ng-model="ab.completeUnitObj.shortCode" class="form-control"
                           style="text-transform: uppercase"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           type="text" name="shortCode" maxlength="8" />
                </div>
                <div class="form-group">
                    <label>Select Region *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.region"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item for item in ab.regions track by item">
                    </select>
                </div>
                <div class="form-group">
                    <label>Zone *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.unitZone"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item for item in ab.zoneList track by item">
                    </select>
                </div>
                <div class="form-group">
                    <label>Sub Category *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.subCategory"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item for item in ab.subCategories track by item">
                    </select>
                </div>
                <div class="form-group">
                    <label>Business Type *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.unitBusinessType"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item for item in ab.businessTypeList track by item">
                    </select>
                </div>

                <div class="form-group">
                    <label>Cafe Service Type *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.cafeType"
                            ng-options="item for item in ab.cafeTypes track by item"
                    >
                    </select>
                </div>

                <div class="form-group">
                    <label>Enable Feedback Form for POS *</label>
                    <select class="form-control"
                            data-ng-model="ab.completeUnitObj.feedbackFormEnabledForPos"
                            data-ng-options="value as (value ? 'Yes' : 'No') for value in [true, false]">
                    </select>
                </div>

                <div class="form-group">
                    <label>Dreamfolks Outlet ID *</label>
                    <input type="text"
                           class="form-control"
                           data-ng-model="ab.completeUnitObj.dreamfolksOutletId"
                           placeholder="Enter Dreamfolks Outlet ID" />
                </div>

                <div class="form-group">
                    <form name="myform">
                        <label>Unit Email *</label>
                        <input class="form-control" type="email" ng-pattern="emailRegEx"
                               ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                               name="input" ng-model="ab.completeUnitObj.unitEmail"/>
                        <span ng-show="myform.input.$error.pattern" class="error">Input is not valid.</span>
                    </form>
                </div>
                <div class="form-group" ng-hide="true">
                    <label>Communication Channel *</label>
                    <input class="form-control" type="text" style="text-transform: capitalize;"
                           ng-model="ab.completeUnitObj.channel"/>
                </div>
                <div class="form-group">
                    <label>GSTIN No *</label>
                    <input class="form-control" type="text"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           ng-model="ab.completeUnitObj.tin"/>
                </div>
                <div class="form-group">
                    <label> FSSAI  </label>
                    <input class="form-control" type="text" ng-model="ab.completeUnitObj.fssai"  />
                </div>

                <div class="form-group">
                    <label>No of Table *</label>
                    <input class="form-control" placeholder="No of Table" string-to-number
                           type="number" maxlength="6" min="1"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model=" ab.completeUnitObj.noOfTables"/>
                </div>
                <div class="form-group">
                    <label>No of Terminals *</label>
                    <input class="form-control" placeholder="No of Terminals" string-to-number type="number"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           maxlength="6" min="1" data-ng-model="ab.completeUnitObj.noOfTerminals"/>
                </div>
                <div class="form-group">
                    <label>No of Take Away Terminals </label>
                    <input class="form-control" string-to-number type="number" maxlength="2" min="1"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.noOfTakeawayTerminals"/>
                </div>
                <div class="form-group">
                    <label>Workstation Enabled *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.workstationEnabled"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Free Internet Access *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.freeInternetAccess"
                            data-ng-value="item.code" ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Table Service Enabled *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.tableService"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Table Service Type *</label>
                    <select class="form-control"
                            ng-model="ab.completeUnitObj.tableServiceType"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.zeroOneOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Token Service Enabled *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.tokenService"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Max token Number Limit *</label>
                    <input class="form-control" string-to-number type="number" maxlength="3" min="1"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.tokenLimit"/>
                </div>
                <div class="form-group">
                    <label>Number of Electricity Meters *</label>
                    <input class="form-control" string-to-number type="number" maxlength="3" min="1"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.noOfMeter"/>
                </div>
                <div class="form-group">
                    <label>DG Meter Available *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.dGAvailable"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Live Inventory Enabled *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.liveInventoryEnabled"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Packaging Type</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.packagingType" ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="packagingType for packagingType in ab.packagingTypeList">
                    </select>
                </div>
                <div class="form-group">
                    <label>Packaging Value</label>
                    <input class="form-control"
                           type="text"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.packagingValue"
                           data-ng-change="ab.validateNumberInput('packagingValue')"
                    />
                </div>
                <div class="form-group">
                    <label>Service Charge</label>
                    <input class="form-control"
                           type="text"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.serviceCharge"
                           data-ng-change="ab.validateNumberInput('serviceCharge')"
                    />
                </div>


                <div class="form-group">
                    <label>Service Charge Pos Enabled</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.serviceChargePosEnabled" ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            data-ng-value="item.code" ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                    <div class="form-group">
                    <label>Service Charge App Enabled</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.serviceChargeAppEnabled" ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            data-ng-value="item.code" ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>


                <div class="form-group">
                    <label>Select Probable Date of Unit Opening*</label>
                    <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                        <input class="form-control" data-ng-model="ab.completeUnitObj.probableOpeningDate"
                               type="text" placeholder="yyyy-MM-dd"/>
                    </div>
                </div>
                <br><br>
                <div class="form-group">
                    <label>Google Merchant Id</label>
                    <input class="form-control"  type="text" ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.googleMerchantId" />
                </div>
                <div class="form-group">
                    <label>Pricing Profile</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.pricingProfile"
                            ng-options="pricingProfile.id as pricingProfile.name for pricingProfile in ab.pricingProfileList">
                    </select>
                </div>
                <div class="form-group">
                    <label>HotSpot Enable Status</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.isHotspotEnabled"
                            ng-options="mqdtList as mqdtList for mqdtList in ab.hotspotStatusList"
                            data-ng-change="setHotSpotStatus(ab.completeUnitObj.isHotspotEnabled)">
                    </select>
                </div>
                <div class="form-group">
                    <label>Is Otp Via Email Allowed</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.isOtpAllowedViaEmail"
                            ng-options="emailAllowed as emailAllowed for emailAllowed in ab.otpAllowedViaEmail"
                            data-ng-change="setEmailAllowedStatus(ab.completeUnitObj.isOtpAllowedViaEmail)">

                    </select>
                </div>
                <div class="form-group">
                    <label>POS Version</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.posVersion"
                            ng-options="posVersion for posVersion in ab.posVersionsList">
                    </select>
                </div>
                <div class="form-group">
                    <label>Monk Recipe Profile</label>
                    <select class="form-control" ng-model="ab.completeUnitObj.monkRecipeProfile"
                            ng-options="profile.code as profile.code for profile in monkRecipeProfiles">
                    </select>
                </div>
                <div class="form-group">
                    <label>Enable Milk Tracking</label>
                    <select class="form-control"
                            data-ng-model="ab.completeUnitObj.milkTrackingEnabled"
                            data-ng-value="item.code"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>No of Monks Needed </label>
                    <input class="form-control"  type="number" 
                           data-ng-model="ab.completeUnitObj.noOfMonksNeeded" />
                </div>
                <div class="form-group">
                    <label>Assembly Strict Mode</label>
                    <select class="form-control" 
                    data-ng-model="ab.completeUnitObj.assemblyStrictMode"
                            data-ng-value="item.code"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>
                <div class="form-group">
                    <label>Assembly Otp Mode</label>
                    <select class="form-control" 
                    data-ng-model="ab.completeUnitObj.assemblyOtpMode"
                            data-ng-value="item.code"
                            ng-options="item as item.name for item in ab.yesNoOption track by item.code">
                    </select>
                </div>

                <div class="form-group"
                     ng-show="(ab.completeUnitObj.id == undefined || ab.completeUnitObj.id == null) && testingUnitCategory == 'TESTING_UNIT'">
                    <label>Is Testing Unit</label>
                    <select class="form-control"
                            data-ng-model="ab.completeUnitObj.isTestingUnit"
                            ng-options="item as item.name for item in (ab.yesNoOption | filter:{name: 'Yes'}) track by item.code">
                    </select>
                </div>

            </uib-tab>
            <uib-tab index="1" heading="Handover Details" disable="ab.index!=1">
                <div class="form-group">
                    <label>Handover Date</label>
                    <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}" >
                        <input class="form-control" data-ng-model="ab.completeUnitObj.handoverDate"
                               type="text" ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK' || !ab.handoverDateEditable"
                               placeholder="yyyy-MM-dd"
                        />
                    </div>
                    <div data-ng-if="!ab.handoverDateEditable && !ab.handoverReadMore" class="error">
                        "Can't Change handover Date " <a data-ng-click="ab.handoverEditableToggle()">read More</a>
                    </div>
                    <div data-ng-if="!ab.handoverDateEditable && ab.handoverReadMore" class="error">
                        {{ab.handoverDateMsg}} <br>
                        <a data-ng-click="ab.handoverEditableToggle()">less</a>
                    </div>
                </div>
                <p>Event Status : {{ab.handoverEventStatus}}</p>
                <div class="row" style="margin-left:5px">
                    <a data-ng-if="ab.showAssetBtn" class="btn btn-primary" data-ng-click="ab.showAssetsGrid()">Asset List</a>
                    <a data-ng-if="ab.showAssetBtn" class="btn btn-primary" data-ng-click="ab.refreshAssetsGrid()">Refresh List</a>

                </div>
                <div data-ng-if = "ab.showGrid">
                    <div
                            class="col s12 grid"
                            id="grid"
                            ui-grid="ab.gridOptions"
                            ui-grid-save-state
                            ui-grid-resize-columns
                            ui-grid-move-columns
                            ui-grid-pinning
                            ui-grid-expandable>

                    </div>
                </div>
                <p> </p>
            </uib-tab>
            <uib-tab index="2" heading="Division" disable="ab.index!=2">
                <h3>Division *</h3>
                <div class="form-group">
                    <select class="form-control" data-ng-model="ab.completeUnitObj.division"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="division as division.name for division in ab.divisions track by division.id">
                    </select>
                </div>
            </uib-tab>
            <uib-tab index="3" heading="Address" disable="ab.index!=3">
                <h3>Address</h3>
                <div class="form-group">
                    <label>Line1 *</label>
                    <input type="text" data-ng-model="ab.completeUnitObj.address.line1"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'" class="form-control">
                </div>
                <div class="form-group">
                    <label>Line2 </label>
                    <input type="text" data-ng-model="ab.completeUnitObj.address.line2"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'" class="form-control">
                </div>
                <div class="form-group">
                    <label>Line 3</label>
                    <input type="text" data-ng-model="ab.completeUnitObj.address.line3"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'" class="form-control">
                </div>
                <div class="form-group">
                    <label>Country *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.address.country"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="selectCountryListData as selectCountryListData.name for selectCountryListData in ab.countriesList track by selectCountryListData.id"></select>
                </div>
                <div class="form-group">
                    <label>State *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.address.state"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="selectStateListData as selectStateListData.name for selectStateListData in ab.stateList track by selectStateListData.id"
                            data-ng-change="ab.getCitiesList(ab.completeUnitObj.address.state)"></select>
                </div>
                <div class="form-group">
                    <label>City *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.location"
                            ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                            ng-options="selectCityListData as selectCityListData.name for selectCityListData in ab.locationList track by selectCityListData.id"
                            data-ng-change="ab.setUnitAddressCity(ab.completeUnitObj.location.name)"></select>
                </div>
                <div class="form-group">
                    <label>Zip Code *</label>
                    <form name="myPincodeform">
                        <input type="text" class="form-control"
                               ng-pattern="pincodeRegEx" name="inputPincode"
                               ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                               data-ng-model="ab.completeUnitObj.address.zipCode">
                     <span ng-show="myPincodeform.inputPincode.$error.pattern" class="error">Input is not valid.</span>
                    </form>
                </div>
                <div class="form-group" ng-if="ab.completeUnitObj.id">
                    <label>Contact 1 *</label>
                    <input type="text" class="form-control"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.address.contact1">
                </div>
                <div class="form-group" ng-if="ab.completeUnitObj.id">
                    <label>Contact 2</label>
                    <input type="text" class="form-control"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.address.contact2">
                </div>
                <div class="form-group">
                    <label>Latitude *</label>
                    <input class="form-control"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.address.latitude" type="text">
                </div>
                <div class="form-group">
                    <label>Longitude *</label>
                    <input class="form-control"
                           ng-disabled="ab.completeUnitObj.family === 'CHAI_MONK'"
                           data-ng-model="ab.completeUnitObj.address.longitude" type="text">
                </div>
            </uib-tab>
            <uib-tab index="4" heading="Product Pricing" disable="ab.index!=4">
                <h3>Select product cloning unit</h3>
                <hr/>
                <div class="form-group">
                    <label>Select unit from which you want to clone *</label>
                    <div class="col-lg-2 pull-right" style="margin-right: 20px">
                        <input title="Show all" type="checkbox" ng-model="ab.showAllPriceUnitsFlag" class="btn btn-primary"
                               style="transform: scale(1.5); margin-right: 5px;" ng-change="ab.showAllUnits('PRICE_CLONE')">
                        Show All
                        </input>
                    </div>
                    <select class="form-control" data-ng-model="ab.selectedPriceCloningUnit"
                            ng-options="unit.name for unit in ab.priceCloneUnitList track by unit.id"
                            data-ng-change="ab.setPriceCloningUnitId(ab.selectedPriceCloningUnit)"></select>
                </div>
            </uib-tab>
            <uib-tab index="5" heading="Inventory Clone" disable="ab.index!=5">
                <h3>Select Inventory Cloning Unit</h3>
                <hr/>
                <div class="form-group">
                    <label>Select unit from which you want to clone *</label>
                    <div class="col-lg-2 pull-right" style="margin-right: 10px">
                        <input title="Show all" type="checkbox" ng-model="ab.showAllInventoryUnitsFlag" class="btn btn-primary"
                               style="transform: scale(1.5); margin-right: 5px;" ng-change="ab.showAllUnits('INVENTORY_CLONE')">
                        Show All
                        </input>
                    </div>
                    <select class="form-control" data-ng-model="ab.selectedInventoryCloneUnit"
                            ng-options="unit.name for unit in ab.inventoryCloneUnitList track by unit.id"
                            data-ng-change="ab.setInventoryCloneUnitId(ab.selectedInventoryCloneUnit)"></select>
                </div>
            </uib-tab>
            <uib-tab index="6" heading="Menu Sequence Clone" disable="ab.index!=6">
                <h3>Select Menu Sequence Cloning Unit </h3>
                <hr/>
                <div class="form-group">
                    <label>Select unit from which you want to clone *</label>
                    <div class="col-lg-2 pull-right" style="margin-right: 10px">
                        <input title="Show all" type="checkbox" ng-model="ab.showAllMenuUnitsFlag" class="btn btn-primary"
                               style="transform: scale(1.5); margin-right: 5px;" ng-change="ab.showAllUnits('MENU_CLONE')">
                        Show All
                        </input>
                    </div>
                    <select class="form-control" data-ng-model="ab.selectedMenuSequenceCloneUnit"
                            ng-options="unit.name for unit in ab.menuCloneUnitList track by unit.id"
                            data-ng-change="ab.setMenuSequenceCloneUnitId(ab.selectedMenuSequenceCloneUnit)"></select>
                </div>
            </uib-tab>
            <!--changes-->
            <uib-tab index="7" heading="Unit Sales Clone" disable="ab.index!=7">
                <h3>Select Sales Cloning Unit </h3>
                <hr/>
                <div class="form-group">
                    <label>Select unit from which you want to clone *</label>
                    <div class="col-lg-2 pull-right" style="margin-right: 10px">
                        <input title="Show all" type="checkbox" ng-model="ab.showAllSalesUnitsFlag" class="btn btn-primary"
                               style="transform: scale(1.5); margin-right: 5px;" ng-change="ab.showAllUnits('SALES_CLONE')">
                        Show All
                        </input>
                    </div>
                    <select class="form-control" data-ng-model="ab.selectedSalesClonedFromUnit"
                            ng-options="unit.name for unit in ab.salesCloneUnitList track by unit.id"
                            data-ng-change="ab.setSalesClonedFromUnitId(ab.selectedSalesClonedFromUnit)"></select>
                </div>
                <br>
            </uib-tab>
            <!---->
            <uib-tab index="8" heading="Business Hour" disable="ab.index!=8">
                <h3>Business Hours</h3>
                <hr/>
                <div class="form-group">
                    <label>No Of Shifts</label>
                    <select class="form-control" data-ng-model="ab.selectedNoOfShifts"
                            ng-options="noOfShifts.name for noOfShifts in ab.noOfShifts track by noOfShifts.name"></select>
                </div>
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered" style="border: 3px solid">
                        <thead style="background-color: #E0E0E0">
                        <th style="vertical-align: top"><label class="checkbox-inline">
                            <input type="checkbox" ng-model="ab.checkedDays"
                                   ng-click="ab.checkAllDays()"/><b>Days </b></label></th>
                        <th><label class="checkbox-inline">
                            <input type="checkbox" ng-model="ab.checkedDine" ng-click="ab.checkAllDineIn()"/>
                            <b>Dine in</b><br>
                            <br> Start Time
                            <uib-timepicker ng-model="ab.headerDineInStartTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                            Close Time
                            <uib-timepicker ng-model="ab.headerDineInCloseTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                        </label></th>
                        <th><label class="checkbox-inline">
                            <input type="checkbox" ng-model="ab.checkedCod" ng-click="ab.checkAllCOD()"/> <b>
                            COD</b> <br>
                            <br> Start Time
                            <uib-timepicker ng-model="ab.headerCodStartTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                            Close Time
                            <uib-timepicker ng-model="ab.headerCodCloseTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                        </label></th>
                        <th><label class="checkbox-inline">
                            <input type="checkbox" ng-model="ab.checkedTakeAway"
                                   ng-click="ab.checkAllTakeAway()"/><b>Take
                            Away </b> <br>
                            <br> Start Time
                            <uib-timepicker ng-model="ab.headerTakeAwayStartTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                            Close Time
                            <uib-timepicker ng-model="ab.headerTakeAwayCloseTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                        </label></th>
                        <th style="vertical-align: top"><label class="checkbox-inline">
                            <input type="checkbox" ng-model="ab.checkedHandOver"
                                   ng-click="ab.checkAllHandOverIn()"/><b>
                            Hand Over</b> <br>
                            <br> Time
                            <uib-timepicker ng-model="ab.headerHandoverTime" hour-step="hstep"
                                            minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
                        </label></th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in ab.completeUnitObj.operationalHours track by item.dayOfTheWeekNumber ">
                            <td><input type="checkbox" ng-model="item.isOperational"
                                       ng-click="ab.headerDaysCheckBoxToggle(item.isOperational)"/>&nbsp;{{item.dayOfTheWeek}}
                            </td>

                            <td ng-if="completeUnitObj.family!='DELIVERY'">
                                <input type="checkbox" ng-model="item.hasDineIn"
                                       ng-change="ab.headerDineInCheckBoxToggle(item.hasDineIn)"/>
                                <uib-timepicker
                                        data-ng-model="item.dineInOpeningTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.hasDineIn"
                                        show-meridian="ismeridian"></uib-timepicker>
                                <uib-timepicker
                                        data-ng-model="item.dineInClosingTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.hasDineIn"
                                        show-meridian="ismeridian"></uib-timepicker>
                            </td>
                            <td><input type="checkbox" ng-model="item.hasDelivery"
                                       ng-change="ab.headerCodCheckBoxToggle(item.hasDelivery)"/>
                                <uib-timepicker
                                        ng-model="item.deliveryOpeningTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.hasDelivery"
                                        show-meridian="ismeridian"></uib-timepicker>
                                <uib-timepicker
                                        ng-model="item.deliveryClosingTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.hasDelivery"
                                        show-meridian="ismeridian"></uib-timepicker>
                            </td>
                            <td ng-if="completeUnitObj.family!='DELIVERY'">
                                <input type="checkbox" ng-model="item.hasTakeAway"
                                       ng-change="ab.headerTakeAwayCheckBoxToggle(item.hasTakeAway)"/>
                                <uib-timepicker
                                        ng-model="item.takeAwayOpeningTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.hasTakeAway"
                                        show-meridian="ismeridian"></uib-timepicker>
                                <uib-timepicker
                                        ng-model="item.takeAwayClosingTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.hasTakeAway"
                                        show-meridian="ismeridian"></uib-timepicker>
                            </td>
                            <td><input type="checkbox" ng-model="item.handoverTick"
                                       ng-change="ab.headerHandoverCheckBoxToggle(item.handoverTick)">
                                <uib-timepicker
                                        ng-model="item.shiftOneHandoverTime"
                                        hour-step="hstep" minute-step="mstep"
                                        data-ng-disabled="!item.handoverTick"
                                        show-meridian="ismeridian"></uib-timepicker>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </uib-tab>
            <uib-tab index="9" heading="Location" disable="ab.index!=9">
                <h3>Location</h3>
                <label>
                    <pre>Country *  :  {{ab.completeUnitObj.address.country.name}}</pre>
                </label><br>
                <label>
                    <pre>State *    :  {{ab.completeUnitObj.address.state.name}}</pre>
                </label><br>
                <label>
                    <pre>City *     :  {{ab.completeUnitObj.address.city}}</pre>
                </label><br>
            </uib-tab>
            <uib-tab index="10" heading="Area manager" disable="ab.index!=10">
                <h3>Area manager</h3>
                <div class="form-group">
                    <label>Select Area manager *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.unitManager"
                            ng-options="reportingManager as reportingManager.name for reportingManager in ab.reportingManagers | orderBy:'name' track by reportingManager.id"></select>
                </div>
                <h3>Select Deputy Area Manager</h3>
                <div class="form-group">
                    <label>DAM *</label>
                    <select class="form-control" data-ng-model="ab.completeUnitObj.cafeManager"
                            ng-options="cafeManager as cafeManager.name for cafeManager in ab.cafeManagers | orderBy:'name' track by cafeManager.id"></select>
                </div>
            </uib-tab>
            <uib-tab index="11" heading="Delivery Partner" disable="ab.index!=11">
                <h3>Select Delivery Partners</h3>
                <div class="row">
                    <div class="col-xs-10">
                        <select class="form-control" data-ng-model="ab.selectedDeliveryPartner"
                                ng-options="partnerDeliveryList as partnerDeliveryList.name for partnerDeliveryList in ab.refPartnerLists track by partnerDeliveryList.id"></select>
                    </div>
                    <div class="col-xs-2">
                        <button class="btn btn-success" ng-click="ab.addDeliveryPartner(ab.selectedDeliveryPartner)">
                            <i class="fa fa-plus fw"></i>
                        </button>
                    </div>
                </div>
                <div class="row" style="margin-top:10px">
                    <div class="col-xs-12">
                        <ul>
                            <li ng-repeat="partnerAddedList in ab.completeUnitObj.deliveryPartners track by partnerAddedList.detail.id">
                                {{partnerAddedList.detail.name}}
                                <label data-ng-repeat="priority in ab.priorityList"
                                       style="padding-left: 8px; padding-right: 8px">
                                    {{priority}}
                                    <input type="radio" data-ng-model="partnerAddedList.priority"
                                           ng-value="priority">
                                </label>
                                <button class="btn btn-danger btn-xs"
                                        ng-click="ab.removeDeliveryPartner(partnerAddedList.detail.id)">
                                    <i class="fa fa-close fw"></i>
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </uib-tab>
            <uib-tab index="12" heading="Revenue Certificate Details" disable="ab.index!=12">
                <h3>Revenue Certificate Details</h3>
                <div style="margin-top: 50px">
                    <div class="form-group">
                        <label>Please select if revenue certificate to be generated *</label>
                        <select class="form-control" data-ng-model="ab.completeUnitObj.revenueCertificateGenerationEnable"
                                ng-options="opt as opt.name for opt in ab.yesNoOption track by opt.code"></select>
                    </div>
                    <div class="form-group">
                        <form name="myform">
                            <label>Mail for revenue certificate generation *</label>
                            <input class="form-control" ng-pattern="multipleEmailRegEx"
                                   ng-disabled="!ab.completeUnitObj.revenueCertificateGenerationEnable.code"
                                   name="input" ng-model="ab.completeUnitObj.revenueCertificateEmail"
                            />
                            <span ng-show="myform.input.$error.pattern" class="error">Input is not valid.</span>
                        </form>
                    </div>
                </div>
            </uib-tab>

            <uib-tab index="13" heading="Summary Details" disable="ab.index!=13">
                <h3>Please verify details</h3>
                <hr/>
                <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 28px">Unit Info</h4>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Unit Name:</strong> {{ab.completeUnitObj.name}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Unit Email:</strong> {{ab.completeUnitObj.unitEmail}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Unit Family:</strong> {{ab.completeUnitObj.family}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Unit Region:</strong> {{ab.completeUnitObj.region}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>No of Take Away Terminal Count:</strong> {{ab.completeUnitObj.noOfTakeAway}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Work Station Enabled:</strong> {{ab.completeUnitObj.workstationEnabled.name}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Free Internet Access:</strong> {{ab.completeUnitObj.freeInternetAccess.name}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Table Service Enabled:</strong> {{ab.completeUnitObj.tableService.name}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Table Service Type:</strong> {{ab.completeUnitObj.tableServiceType.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Unit Terminals count:</strong> {{ab.completeUnitObj.noOfTerminals}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Unit GSTIN number:</strong> {{ab.completeUnitObj.tin}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>FSSAI:</strong> {{ab.completeUnitObj.fssai}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Unit Reference Name:</strong> {{ab.completeUnitObj.referenceName}}
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                            <strong>Communication Channel:</strong> {{ab.completeUnitObj.channel}}
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>No of Electricity Meter:</strong> {{ab.completeUnitObj.noOfMeter}}
                    </div>
                    <div class="col-xs-6">
                        <strong>DG Meter Available</strong> {{ab.completeUnitObj.dGAvailable.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Business Type:</strong> {{ab.completeUnitObj.unitBusinessType}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Company:</strong> {{ab.completeUnitObj.company.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Revenue Certificate Generation Enable:</strong>
                        {{ab.completeUnitObj.revenueCertificateGenerationEnable.code}}
                    </div>
                    <div class="col-xs-6" style="overflow-wrap: break-word">
                        <strong>Revenue Certificate Emails:</strong> {{ab.completeUnitObj.revenueCertificateEmail}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Assembly Strict Mode:</strong>
                        {{ab.completeUnitObj.assemblyStrictMode.code}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Assembly Otp Mode:</strong>
                        {{ab.completeUnitObj.assemblyOtpMode.code}}
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-6">
                        <strong>Service Charge:</strong>
                        {{ab.completeUnitObj.serviceCharge}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Service Charge Pos:</strong>
                        {{ab.completeUnitObj.serviceChargePosEnabled.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Service Charge App:</strong>
                        {{ab.completeUnitObj.serviceChargeAppEnabled.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Feedback form POS:</strong>
                        {{ab.completeUnitObj.feedbackFormEnabledForPos}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Dreamfolks Outlet ID:</strong>
                        {{ab.completeUnitObj.dreamfolksOutletId}}
                    </div>
                </div>


                <div class="row" ng-show="(ab.completeUnitObj.id==undefined || ab.completeUnitObj.id==null) && testingUnitCategory == 'TESTING_UNIT'">
                    <div class="col-xs-6">
                        <strong>Is Testing Unit:</strong>
                        {{ab.completeUnitObj.isTestingUnit.name}}
                    </div>
                </div>

                <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Unit Address
                    Details</h4>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Address Type:</strong> {{ab.completeUnitObj.address.addressType}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Line 1:</strong> {{ab.completeUnitObj.address.line1}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Line 2:</strong> {{ab.completeUnitObj.address.line2}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Line 3:</strong> {{ab.completeUnitObj.address.line3}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>City:</strong> {{ab.completeUnitObj.address.city}}
                    </div>
                    <div class="col-xs-6">
                        <strong>State:</strong> {{ab.completeUnitObj.address.state.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>ZipCode:</strong> {{ab.completeUnitObj.address.zipCode}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Country:</strong> {{ab.completeUnitObj.address.country.name}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Contact 1:</strong> {{ab.completeUnitObj.address.contact1}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Contact 2:</strong> {{ab.completeUnitObj.address.contact2}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Latitude:</strong> {{ab.completeUnitObj.address.latitude}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Longitude:</strong> {{ab.completeUnitObj.address.longitude}}
                    </div>
                </div>
                <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Delivery
                    Partners</h4>
                <div class="row">
                    <div class="col-xs-6" ng-repeat="addedPartnersData in ab.completeUnitObj.deliveryPartners">
                        <strong>{{addedPartnersData.detail.name}} </strong> : priority <strong>
                        {{addedPartnersData.priority}}</strong>
                    </div>
                </div>
                <div ng-show="angular.isUndefined(ab.completeUnitObj.id)">
                    <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Product
                        Pricing
                        cloning unit</h4>
                    <div class="row">
                        <div class="col-xs-6">
                            <strong>Unit name:</strong> {{ab.selectedPriceCloningUnit.name}}
                        </div>
                    </div>
                </div>
                <div>
                    <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Manager</h4>
                    <div class="row">
                        <div class="col-xs-6">
                            <strong>Manager name:</strong> {{ab.completeUnitObj.unitManager.name}}
                        </div>
                    </div>
                </div>
                <div>
                    <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">
                        Location</h4>
                    <div class="row">
                        <div class="col-xs-3">
                            <strong>Country:</strong> {{ab.completeUnitObj.address.country.name}}
                        </div>
                        <div class="col-xs-3">
                            <strong>State:</strong> {{ab.completeUnitObj.address.state.name}}
                        </div>
                        <div class="col-xs-3">
                            <strong>Location:</strong> {{ab.completeUnitObj.location.name}}
                        </div>
                        <div class="col-xs-3">
                            <strong>Code:</strong> {{ab.completeUnitObj.location.code}}
                        </div>
                    </div>
                </div>
                <div>
                    <h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">
                        Brands
                    </h4>
                    <table class="table table-striped table-bordered">
                        <tr>
                            <td style="font-weight: 700;">Brand ID</td>
                            <td style="font-weight: 700;">Brand Name</td>
                        </tr>
                        <tr data-ng-repeat="brand in selectedBrands">
                            <td>{{brand.brandId}}</td>
                            <td>{{brand.brandName}}</td>
                        </tr>
                    </table>
                </div>
                <div class="row">
                    <div class="col-xs-10" style="font-size: 11px">
                        <button type="button" class="btn btn-primary" data-toggle="collapse" data-target="#demo"
                                style="margin-top: 10px; margin-bottom: 10px;">Business
                            Hours Detail
                        </button>
                        <div id="demo" class="collapse">
                            <table class="table table-striped table-bordered">
                                <tr>
                                    <td>Shift</td>
                                    <td>Days</td>
                                    <td>Dine In</td>
                                    <td>Cod</td>
                                    <td>Take Away</td>
                                    <td>Hand Over</td>
                                </tr>
                                <tr ng-repeat="operationHour in ab.completeUnitObj.operationalHours">
                                    <td>{{operationHour.noOfShifts}}</td>
                                    <td>{{operationHour.dayOfTheWeek}}</td>
                                    <td><b>Start Date </b> :
                                        {{operationHour.dineInOpeningTime.toTimeString().slice(0,8)}} <br/><b>End Date
                                            :</b>
                                        {{operationHour.dineInClosingTime.toTimeString().slice(0,8)}}
                                    </td>
                                    <td><b>Start Date :</b>
                                        {{operationHour.deliveryOpeningTime.toTimeString().slice(0,8)}} <br/><b>End Date
                                            : </b>
                                        {{operationHour.deliveryClosingTime.toTimeString().slice(0,8)}}
                                    </td>
                                    <td><b>Start Date :</b>
                                        {{operationHour.takeAwayOpeningTime.toTimeString().slice(0,8)}} <br/><b>End Date
                                            : </b>
                                        {{operationHour.takeAwayClosingTime.toTimeString().slice(0,8)}}
                                    </td>
                                    <td>{{operationHour.shiftOneHandoverTime.toTimeString().slice(0,8)}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </uib-tab>

        </uib-tabset>
    </div>
    <div class="modal-footer">
        <button class="btn btn-primary pull-left" type="button" ng-click="ab.prev()" ng-hide="ab.index==0">Prev</button>
        <button class="btn btn-primary pull-left" type="button" ng-click="ab.next()" ng-hide="ab.index==13">Next</button>
        <button class="btn btn-primary pull-right" type="button" ng-show="ab.index==13 && ab.completeUnitObj.id==null"
                ng-click="ab.addNewUnit()">Add new Unit
        </button>
        <button class="btn btn-primary pull-right" type="button" ng-show="ab.index==13 && ab.completeUnitObj.id!=null"
                ng-click="ab.editUnit()">Edit Unit
        </button>
    </div>
</div>

<script
    type="text/ng-template"
    id="expandableRowTemplate.html">
        <div ui-grid="row.entity.subGridOptions" style="height:{{(row.entity.subGridOptions.data.length * row.height) + row.height + row.grid.headerHeight}}px" ui-grid-auto-resize></div>
</script>
<script
        type="text/ng-template"
        id="colColor.html">
    <div class="ui-grid-cell-contents"
         data-ng-style="(row.entity.scanned) ? {'color':'yellow','background':'green'} : {'color':'yellow','background':'red'}">
        {{row.entity.assetStatus}}
    </div>
</script>