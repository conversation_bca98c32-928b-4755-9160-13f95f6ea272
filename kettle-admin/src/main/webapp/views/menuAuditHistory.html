<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    table th, td {
        border: #ccc 1px solid;
    }
</style>
<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner <PERSON>u <PERSON>t History</h2>
    </div>
    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>
    <div class="row" data-ng-if="selectedAction == 'UPDATE UNIT MENU'">
        <div class="col-xs-12">

            <div class="row">
                <div class="col-xs-12">

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Menu Type</label>
                                <select class="form-control"
                                        data-ng-options="type for type in menuType "
                                        data-ng-model="selectedMenuType"
                                        data-ng-change="setSelectedMenuType(selectedMenuType)">
                                </select>
                            </div>
                            <br><br>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Menu Audit Historys"
                                       data-ng-click="getUnitMenuToAdd()"/>
                            </div>
                        </div>
                        <table class="table table-bordered table-striped"
                               data-ng-if="historyData!=null">
                            <thead>
                            <tr>
                                <th>SNo</th>
                                <th>UnitId</th>
                                <th>employeeId</th>
                                <th>employeeName</th>
                                <th>PartnerName</th>
                                <th>BrandName</th>
                                <th>MenuType</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in historyData">
                                <td>{{$index+1}}</td>
                                <td>{{item.unitId}}</td>
                                <td>{{item.employeeId}}</td>
                                <td>{{item.employeeName}}</td>
                                <td>{{item.partnerName}}</td>
                                <td>{{item.brandId}}</td>
                                <td>{{item.menuType}}</td>
                                <td>{{item.status}}</td>
                                <td>{{item.addTimeIST}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



















