<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Kettle Locality Mapping Dashboard</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="row">
                <div class=" col-xs-6 form-group">
                    <label>Filter</label>
                    <input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>
                </div>
                <div class=" col-xs-6 form-group">
                    <label>Actions</label><br/>
                    <!--<input type="button" class="btn btn-primary" value="Remove Localities" data-ng-click="removeLocalities()" />-->
                    <input type="button" class="btn btn-success" value="Add Localities"
                           data-ng-click="showAddLocalities()"/>
                    <input type="button" class="btn btn-success" value="Add Bulk Localities"
                           data-ng-click="showAddBulkLocalities()"/>
                    <input type="button" class="btn btn-success" value="Download File"
                           data-ng-click="downloadLocalities()"/>
                </div>
            </div>
            <table class="table table-bordered table-striped">
                <tr>
                    <!--<td><input type="checkbox" data-ng-model="selectAllMappings" /></td>-->
                    <td>Locality</td>
                    <td>City</td>
                    <td>State</td>
                    <td>Country</td>
                    <td>Primary Unit</td>
                    <!--<td>Skip primary delivery charge</td>-->
                    <!--<td>Skip primary packaging charge</td>-->
                    <td>Secondary Unit</td>
                    <!--<td>Skip secondary delivery charge</td>-->
                    <!--<td>Skip secondary packaging charge</td>-->
                    <td>Action</td>
                </tr>
                <tr data-ng-repeat="mapping in filtered = (localityMappings | filter:search | orderBy : predicate :reverse)">
                    <!--<td><input type="checkbox" data-ng-model="mapping.selected" /></td>-->
                    <td>{{mapping.locality}}</td>
                    <td>{{mapping.city}}</td>
                    <td>{{mapping.state}}</td>
                    <td>{{mapping.country}}</td>
                    <td>{{mapping.primaryCOD}}</td>
                    <!--<td>{{mapping.skipPrimaryDeliveryCharge}}</td>-->
                    <!--<td>{{mapping.skipPrimaryPackagingCharge}}</td>-->
                    <td>{{mapping.secondaryCOD}}</td>
                    <!--<td>{{mapping.skipSecondaryDeliveryCharge}}</td>-->
                    <!--<td>{{mapping.skipSecondaryPackagingCharge}}</td>-->
                    <td>
                        <input type="button" value="Edit" class="btn btn-primary"
                               data-ng-click="changeLocalityMapping(mapping)"/>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="editLocalityMappingModal" tabindex="-1" role="dialog"
     aria-labelledby="editLocalityMappingModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="webCategoryModalLabel">Update Locality Mapping</h4>
            </div>
            <div class="col-xs-12">
                <div class="row form-group">
                    <div class="col-lg-3"><label>Locality</label><br/>{{updateMapping.locality}}</div>
                    <div class="col-lg-3"><label>City</label><br/>{{updateMapping.city}}</div>
                    <div class="col-lg-3"><label>State</label><br/>{{updateMapping.state}}</div>
                </div>
                <div class="row form-group">
                    <div class="col-lg-6"><label>Primary Unit</label><br/>{{updateMapping.primaryCOD}}</div>
                    <div class="col-lg-6"><label>Secondary Unit</label><br/>{{updateMapping.secondaryCOD}}</div>
                </div>
                <div class="form-group">
                    <label>Primary Unit</label>
                    <select data-ng-model="editMappingPrimaryUnit" class="form-control"
                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>
                <div class="form-group">
                    <label>Secondary Unit</label>
                    <select data-ng-model="editMappingSecondaryUnit" class="form-control"
                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>

            </div>
            <div class="modal-footer">
                <input type="button" value="Update Mapping" class="btn btn-primary"
                       data-ng-click="updateLocalityMapping()"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addLocalityMappingModal" tabindex="-1" role="dialog"
     aria-labelledby="addLocalityMappingModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Add Locality Mapping</h4>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label>Locality</label>
                    <input type="text" data-ng-model="newMapping.locality" class="form-control"/>
                </div>
                <div class="form-group">
                    <label>City</label>
                    <select data-ng-model="city" class="form-control"
                            data-ng-options="map.city for map in cityMap"
                    data-ng-change="onCityChange(city)">
                    </select>
                    <!--<input type="text" data-ng-model="newMapping.city" class="form-control"/>-->
                </div>
                <div class="form-group">
                    <label>State</label>
                    <lable  class="form-control">{{newMapping.state}}</lable>
                </div>
                <div class="form-group">
                    <label>Country</label>
                    <input type="text" data-ng-model="newMapping.country" disabled class="form-control"/>
                </div>
                <div class="form-group">
                    <label>Primary Unit</label>
                    <select data-ng-model="newMappingPrimaryUnit" class="form-control"
                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>

                <div class="form-group">
                    <label>Secondary Unit</label>
                    <select data-ng-model="newMappingSecondaryUnit" class="form-control"
                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>
            </div>
            <div class="modal-footer">
                <input type="button" value="Add Mapping" class="btn btn-primary" data-ng-click="addLocalityMapping()"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addBulkLocalityMappingModal" tabindex="-1" role="dialog"
     aria-labelledby="addBulkLocalityMappingModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Add Bulk Locality Mapping</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <form name="x">
                            <div class="row row-spacing">
                                <div class="col-xs-12 form-group">
                                    <label class="control-label">Upload File : </label>
                                    <input type="file" file-model="bulkCSVFile"/>
                                </div>
                            </div>
                        </form>
                        <button type="button" class="btn btn-primary" data-ng-click="addBulkLocalityMappings()">Add Bulk
                            Mappings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>