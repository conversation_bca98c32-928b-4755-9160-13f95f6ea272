<style type="text/css">
    .rowSelected {
        background: #bdf3bf;
        /* border: 2px solid rgb(5, 106, 50); */
    }

    .innerTable {
        max-width: 95%;
        margin-left: auto;
        margin-right: auto;
    }

    th,
    td {
        text-align: center;
    }
</style>


<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">B2B Monk Customer Management Dashboard</h2>
    </div>

    <!-- <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                    data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                    data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div> -->


    <div class="row">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-lg-12">
                    <!-- <h3>Manage B2B Monk Customers, Users</h3> -->
                    <p>Use this panel to view, add and edit B2B Monk Customers, Users</p>
                    <p>Search for Customers and click on them for viewing Users</p>
                </div>
            </div>
            <div class="row">
                <button class="btn btn-primary pull-right" data-toggle="modal" id="addB2BCustomerBtn" style="margin: 5;"
                    data-ng-click="openB2BCustomerModal(customerDetail, 'Add', $event)">
                    <i class="fa fa-plus fw"></i> Add Customer
                </button>
                <div style="margin: 10px" class="row">
                    <div class="col-lg-4" style="width: 40%;">
                        <input type="text" ng-model="cust.SearchBarInput" class="form-control"
                            ng-change="getCustomers()" placeholder="Customer Name" />
                    </div>
                </div>
                <div style="margin: 10px;" class="row">
                    <div class="col-xs-12" ng-if="customerDetailList.length > 0">
                        <div class="row">
                            <div class="col-xs-12">
                                <table id="b2bCustomersTable" class="table table-bordered">
                                    <thead style="background-color: #e7e7e7;">
                                        <tr>
                                            <th>S No.</th>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Address</th>
                                            <th colspan="2" data-ng-if="selectedCustomerId">Actions</th>
                                            <th colspan="1" data-ng-if="!selectedCustomerId">Actions</th>
                                        </tr>
                                    </thead>

                                    <tbody align="center">
                                        <tr ng-repeat-start="customer in customerDetailList | filter : searchBarInput track by $index"
                                            data-ng-click="fetchB2BUsers(customer)" style="cursor: pointer;"
                                            data-ng-class="{'rowSelected':customer.customerId===selectedCustomerId}">
                                            <td>{{$index+1}}</td>
                                            <td>{{customer.customerName}}</td>
                                            <td>{{customer.customerType}}</td>
                                            <td>{{customer.address.line2 + " " + customer.address.city}}</td>
                                            <td colspan="1">
                                                <button ng-click="openB2BCustomerModal(customer, 'Preview', $event)"
                                                    title="preview"
                                                    style="border: none; background: none; margin-top: 6px"><em
                                                        class="fa fa-eye" style="font-size: 24px;"></em>
                                                </button>
                                            </td>
                                            <td colspan="1" data-ng-if="selectedCustomerId">
                                                <button ng-if="customer.customerId===selectedCustomerId" class="btn"
                                                    data-toggle="modal" id="addB2BUserBtn" style="margin: 5;"
                                                    data-ng-click="openB2BUserModal(userDetail, 'Add', $event)">
                                                    <i class="fa fa-plus fw"></i> Add User
                                                </button>
                                            </td>
                                        </tr>
                                        <tr ng-repeat-end="" ng-show="customer.customerId == selectedCustomerId">
                                            <td colspan="6">
                                                <table class="table table-bordered innerTable">
                                                    <thead style="background-color: #e7e7e7;">
                                                        <th>User Name</th>
                                                        <th>Role</th>
                                                        <th>Status</th>
                                                        <th>Location</th>
                                                        <th>Machine No.</th>
                                                        <th>Actions</th>
                                                    </thead>
                                                    <tbody>
                                                        <tr ng-repeat="user in userDetailList"
                                                            data-ng-class="{'rowSelected':true}">
                                                            <td>{{user.userName}}</td>
                                                            <td>{{user.userRole}}</td>
                                                            <td>{{user.status}}</td>
                                                            <td>{{user.machineLocation.line2 + " " +
                                                                user.machineLocation.city}}</td>
                                                            <td>{{user.machineSerialNo}}</td>
                                                            <td colspan="1">
                                                                <button
                                                                    ng-click="openB2BUserModal(user, 'Preview', $event)"
                                                                    title="preview"
                                                                    style="border: none; background: none; margin-top: 6px"><em
                                                                        class="fa fa-eye" style="font-size: 24px;"></em>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>


<div class="modal fade" id="b2bCustomerModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="modalLabel">{{customerViewMode}} Customer</h4>
            </div>
            <div style="display: flex;justify-content: flex-end; padding-right: 5%;">
                <button ng-if="customerViewMode == 'Preview'" ng-click="changeCustomerViewMode('Edit')"
                    class="btn btn-primary" style="margin: 10px; margin-right: 0px;">Edit
                </button>
                <button ng-if="customerViewMode == 'Edit'" ng-click="changeCustomerViewMode('Preview')"
                    class="btn btn-primary" style="margin: 10px; margin-right: 0px;">Preview
                </button>
            </div>
            <div class="modal-body">
                <form name="addB2BCustomerForm">
                    <div class="form-group">
                        <label>Customer Id *</label>
                        <input type="text" class="form-control" ng-model="customerDetail.customerId"
                            data-ng-disabled="customerPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Customer Name *</label>
                        <input type="text" class="form-control" ng-model="customerDetail.customerName"
                            data-ng-disabled="customerPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Customer Type *</label>
                        <select class="form-control" ng-model="customerDetail.customerType"
                            ng-options="customerType for customerType in customerTypeList"
                            data-ng-disabled="customerPreviewEnabled" />
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <b>Customer Address </b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div>
                                    <label>Line 1 *</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.line1"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Line 2 *</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.line2"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Line 3</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.line3"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>City *</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.city"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>State *</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.state"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Zip Code *</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.zipCode"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Country *</label>
                                    <input type="text" class="form-control" ng-model="customerDetail.address.country"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <b>Primary Contact </b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div>
                                    <label>Name *</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.primaryContact.name"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Email *</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.primaryContact.email"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Contact *</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.primaryContact.contact"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Alternate Contact</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.primaryContact.alternateContact"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <b>Secondary Contact </b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div>
                                    <label>Name *</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.secondaryContact.name"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Email *</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.secondaryContact.email"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Contact *</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.secondaryContact.contact"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Alternate Contact</label>
                                    <input type="text" class="form-control"
                                        ng-model="customerDetail.secondaryContact.alternateContact"
                                        data-ng-disabled="customerPreviewEnabled" />
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" ng-if="customerViewMode == 'Add'">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right" ng-click="addB2BCustomer()">
                        {{customerViewMode}} Customer
                    </button>
                </div>
            </div>
            <div class="modal-footer" ng-if="customerViewMode == 'Edit'">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right" ng-click="updateB2BCustomer()">
                        Update Customer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="b2bUserModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="modalLabel">{{userViewMode}} User</h4>
            </div>
            <div style="display: flex;justify-content: flex-end; padding-right: 5%;">
                <button ng-if="userViewMode == 'Preview'" ng-click="changeUserViewMode('Edit')"
                    class="btn btn-primary pull-right" style="margin: 10px; margin-right: 0px;">Edit
                </button>
                <button ng-if="userViewMode == 'Edit'" ng-click="changeUserViewMode('Preview')"
                    class="btn btn-primary pull-right" style="margin: 10px; margin-right: 0px;">Preview
                </button>
            </div>
            <div class="modal-body">
                <form name="addB2BUserForm">
                    <div class="form-group">
                        <label>User Name *</label>
                        <input type="text" class="form-control" ng-model="userDetail.userName"
                            data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>User Role *</label>
                        <select class="form-control" ng-model="userDetail.userRole"
                            ng-options="userRole for userRole in userRoleList" data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>App Config *</label>
                        <input type="text" class="form-control" ng-model="userDetail.appConfig"
                            data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Machine Id *</label>
                        <input type="text" class="form-control" ng-model="userDetail.machineId"
                            data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Status *</label>
                        <select class="form-control" ng-model="userDetail.status"
                            ng-options="status for status in statusList" data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Customer Id *</label>
                        <input type="text" class="form-control" ng-model="userDetail.customerId"
                            data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Machine Serial No. *</label>
                        <input type="text" class="form-control" ng-model="userDetail.machineSerialNo"
                            data-ng-disabled="userPreviewEnabled" />
                    </div>
                    <div class="form-group">
                        <label>Menu Version *</label>
                        <input type="text" class="form-control" ng-model="userDetail.menuVersion"
                            data-ng-disabled="userPreviewEnabled" />
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <b>Machine Location </b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div>
                                    <label>Line 1 *</label>
                                    <input type="text" class="form-control" ng-model="userDetail.machineLocation.line1"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Line 2 *</label>
                                    <input type="text" class="form-control" ng-model="userDetail.machineLocation.line2"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Line 3</label>
                                    <input type="text" class="form-control" ng-model="userDetail.machineLocation.line3"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                                <div>
                                    <label>City *</label>
                                    <input type="text" class="form-control" ng-model="userDetail.machineLocation.city"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                                <div>
                                    <label>State *</label>
                                    <input type="text" class="form-control" ng-model="userDetail.machineLocation.state"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Zip Code *</label>
                                    <input type="text" class="form-control"
                                        ng-model="userDetail.machineLocation.zipCode"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                                <div>
                                    <label>Country *</label>
                                    <input type="text" class="form-control"
                                        ng-model="userDetail.machineLocation.country"
                                        data-ng-disabled="userPreviewEnabled" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default" data-ng-if="!userPreviewEnabled">
                        <div class="panel-heading">
                            <b>Password </b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div>
                                    <label>Enter Password *</label>
                                    <input type="password" class="form-control" ng-model="userPassword" />
                                </div>
                                <div>
                                    <label>Re enter password *</label>
                                    <input type="password" class="form-control" ng-model="userReEnterPassword" />
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" ng-if="userViewMode == 'Add'">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right" ng-click="addB2BUser()">
                        {{userViewMode}} User
                    </button>
                </div>
            </div>
            <div class="modal-footer" ng-if="userViewMode == 'Edit'">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right" ng-click="updateB2BUser()">
                        Update User
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>