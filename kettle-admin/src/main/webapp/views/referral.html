<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Insert title here</title>
</head>
<style>
pre {
	outline: #ccc;
	padding: 5px;
	margin: 5px;
}

.string {
	color: green;
}

.number {
	color: darkorange;
}

.boolean {
	color: blue;
}

.null {
	color: magenta;
}

.key {
	color: red;
}
</style>
<body>
	<div data-ng-init="init()">
		<div class="row">
			<div class="col-lg-12">
				<h1 class="page-header">Referral Test</h1>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-6">
				<div class="row">
					<div class="form-group">
						<label>Referral Code</label> <input class="form-control"
							data-ng-model="refCode" />
					</div>
					<div class="form-group">
						<label>Contact Number</label> <input class="form-control"
							data-ng-model="contact" />
					</div>
					<div class="form-group">
						<label>Name</label> <input class="form-control"
							data-ng-model="name" />
					</div>
					<div class="form-group">
						<label>email</label> <input class="form-control"
							data-ng-model="email" />
					</div>
					<div class="form-group">
						<label>OTP</label> <input class="form-control"
							data-ng-model="otp" />
					</div>
					<div class="form-group">
						<button class="btn btn-primary" type="button"
							data-ng-click="addToken()">Create Token</button>
						<button class="btn btn-primary" type="button"
							data-ng-click="validate()">Validate</button>
						<button class="btn btn-primary" type="button"
							data-ng-click="submit()">Submit</button>
					</div>
				</div>
			</div>
			<div class="col-lg-6">
				<pre id="myDiv" style="min-height: 320px"></pre>
			</div>
		</div>
	</div>
</body>
</html>