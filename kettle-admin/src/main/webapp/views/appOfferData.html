<div id="fullOfferDiv" ng-init="init()" xmlns:70vh xmlns:70vh xmlns:70vh
     xmlns:max-height="http://www.w3.org/1999/xhtml">
    <div id="offerListDivDetail">
        <div class="row">
            <div class="col s8">
                <br>
                <h1 class="page-header">
                    App Banner Management
                </h1>
            </div>

            <div class="col-lg-2">
                <button class="btn btn-primary" data-toggle="modal"
                        data-ng-click="addNewAppOfferDialog()"
                        id="addOffer">
                    <i class="fa fa-plus fw"></i> Add Offers
                </button>
            </div>
            <!--uncomment this part whenever there isuse of push to app feature-->
            <!--<div class="col-lg-2">-->
            <!--<button class="btn btn-primary " data-toggle="modal"-->
            <!--id="pushToApp">-->
            <!--Push to App-->
            <!--</button>-->
            <!--</div>-->


        </div>
        <br>
        <div class="row">
            <div class="col-lg-3 ">
                <label>Select Partner Name</label>
            </div>
            <div class="col-lg-4">
                    <select class="form-control" ng-model="selectedPartnerId"
                            ng-options=" company.name for company in partnerId">
                    <option value="" hidden selected/>
                </select>
            </div>
            <div class="col-lg-3">
                <button class="btn btn-primary" data-ng-click="getAllOffers()"> search</button>
            </div>
        </div>

        <div class="row" ng-if="appOffers != null">
            <div class="row">
                <div class="col-lg-2">
                    <button class="btn btn-primary" data-toggle="modal"
                            data-ng-click="setOrdering()"
                            id="setOrdering">
                        Set Ordering
                    </button>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-lg-4 ">
                    <label>Search By</label>
                    <select ng-model="searchKeyword" data-ng-change="onSearchByChange()">
                        <option value="offerType">Offer Type</option>
                        <option value="description">Description</option>
                        <option value="couponCode">Coupon Code</option>
                        <option value="offerId">Offer Id</option>
                        <option value="brandId">Brand Id</option>
                    </select>
                </div>
                <div id="search" class="col-lg-8 ">
                    <input type="text" style="width: 75%;" placeholder="Search {{searchKeyword}} "
                           ng-model="searchFields[searchKeyword]"/>
                </div>
            </div>
            <br>
            <div class="row ">
                <div class="col-xs-12">
                    <div class="row ">
                        <div class="col-xs-12 overflow">

                            <table class="table table-striped table-bordered">
                                <thead style="background-color: #e7e7e7">
                                <th>Offer Type<a ng-click="sort_by('offerType');"><i
                                        class="glyphicon glyphicon-sort"></i></a></th>
                                <th>Brand ID</th>
                                <th>Index<a ng-click="sort_by('offerIndex');"><i
                                        class="glyphicon glyphicon-sort"></i></a></th>
                                <th>Description</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Coupon Code</th>
                                <th>Offer Id</th>
                                <th>List Image</th>
                                <th>Grid Image</th>
                                <th>Action Type</th>
                                <th>Status</th>
                                <th>Units</th>
                                <th colspan="3" style="text-align:center">Action</th>
                                </thead>
                                <tbody>
                                <tr ng-repeat=" offer in appOffers|orderBy:'offerIndex'|orderBy : predicate :reverse|filter:searchFields ">
                                    <td>{{offer.offerType}}</td>
                                    <td>{{offer.brandId}}</td>
                                    <td>{{offer.offerIndex || 0}}</td>
                                    <td width="12%">{{offer.description}}</td>
                                    <td>{{offer.startDate | date:'yyyy-MM-dd'}}</td>
                                    <td>{{offer.endDate | date:'yyyy-MM-dd'}}</td>
                                    <td>{{offer.couponCode}}</td>
                                    <td>{{offer.offerId}}</td>
                                    <td style="width: 25%">
                                        <div align="center">
                                            <img height="50px" width="50px"
                                                 data-ng-click="openProductImageModal(offer.listImage)"
                                                 data-ng-src="{{imageSuffix}}{{offer.listImage}}"/>
                                        </div>
                                    </td>
                                    <td style="width: 25%">
                                        <div align="center">
                                            <img height="50px" width="50px"
                                                 data-ng-click="openProductImageModal(offer.gridImage)"
                                                 data-ng-src="{{imageSuffix}}{{offer.gridImage}}"/>
                                        </div>
                                    </td>
                                    <td>{{offer.actionType}}</td>
                                    <td>
                                        <h4>
                                        <span
                                                data-ng-if="offer.status == 'ACTIVE'"
                                                class="label label-success">{{offer.status}}</span>
                                            <span
                                                    data-ng-if="offer.status != 'ACTIVE'"
                                                    class="label label-danger">{{offer.status}}</span>
                                        </h4>
                                    </td>
                                    <td>
                                        <a
                                                data-toggle="modal"
                                                id="viewUnitLists"
                                                class="btn btn-info"
                                                data-ng-click="viewUnitLists(offer.unitDetailList)">{{offer.unitDetailList.length
                                        || 'ALL'}}
                                        </a>
                                    </td>
                                    <td>
                                        <button
                                                class="btn btn-primary"
                                                data-toggle="modal"
                                                id="edit"
                                                ng-click="onEdit(offer)">Edit
                                        </button>
                                    </td>
                                    <td>
                                        <button
                                                class="btn btn-primary"
                                                data-toggle="modal"
                                                id="view"
                                                ng-click="onView(offer)">View
                                        </button>
                                    </td>
                                    <td>
                                        <button
                                                data-ng-class="{'btn btn-success':offer.status=='IN_ACTIVE', 'btn btn-danger':offer.status=='ACTIVE'}"
                                                data-ng-click="updateStatus(offer)">
                                            <span data-ng-if="offer.status == 'ACTIVE'">DEACTIVATE</span>
                                            <span data-ng-if="offer.status != 'ACTIVE'">ACTIVATE</span>
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add New App Offer Modal -->
<div aria-labelledby="AddNewAppOfferModalLabel" class="modal fade" id="AddNewOfferAppModalData" role="dialog"
     data-keyboard="false" data-backdrop="static"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" ng-click="reset()" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <div ng-show="isAddNewOffer">
                    <h4 class="modal-title" id="myModalLabel_add"> Add New App Offer</h4>
                </div>
                <div ng-show="isEditOffer">
                    <h4 class="modal-title" id="myModalLabel_update"> Update App Offer</h4>
                </div>
                <div ng-show="isViewOffer">
                    <h4 class="modal-title" id="myModalLabel_view"> View Offer</h4>
                </div>
            </div>

            <div class="modal-body">
                <form name="addNewReportForm" novalidate>
                    <fieldset ng-disabled="isViewOffer">
                        <div class="form-group">
                            <label>Banner Type</label>
                            <div class="form-group">
                                <select class="form-control" data-ng-model="offerObject.selectedOfferType"
                                        data-ng-options=" appOfferType.name for appOfferType in offerTypes">
                                    <option value="" selected hidden/>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" data-ng-if="offerObject.selectedOfferType.name == 'CUSTOMER_BANNER'">
                            <label>App Offer Applicability</label>
                            <div class="form-group" ng-repeat="labels in offerObject.appOfferApplicabilityList">
                                <label class="control-label">{{labels.appOfferApplicabilityFlag}}</label>
                                <select
                                    class="form-control"
                                    data-ng-model="labels.isApplicable"
                                    data-ng-options="applicabilityOption for applicabilityOption in applicabilityOptions"
                                    required>
                            </select>

                            </div>
                        </div>

                        <div class="form-group">
                            <label>Partner Name</label>
                            <div class="form-group">
                                <select class="form-control" data-ng-model="offerObject.selectedPartnerId"
                                        data-ng-options=" partner.name for partner in partnerId">
                                    <option value="" selected hidden/>
                                </select>
                            </div>

                        </div>

                        <div class="form-group">
                            <label>Action Category</label>
                            <select
                                    class="form-control"
                                    data-ng-model="offerObject.actionCategory"
                                    placeholder="select options"
                                    data-ng-options="actionType.name for actionType in actionCategories"
                            >
                                <option value="" selected hidden/>
                            </select>
                        </div>

                        <div data-ng-if="(offerObject.actionCategory.value == 'TAP_TO_COPY_CODE')">
                            <div style="margin-bottom: 50px">

                                <div class="form-group">
                                    <label> Coupon Code *</label>
                                    <input class="form-control" ng-model="offerObject.couponCOde"
                                           placeholder="Please Enter Coupon Code"
                                           ng-change="onEditCoupon()"
                                           required type="text"/>
                                </div>
                                <div class="col-lg-pull-1">
                                    <input type="button" class="btn btn-primary pull-right" value="Verify Coupon"
                                           data-ng-click="verifyCoupon(offerObject.couponCOde)"/>
                                </div>
                            </div>

                            <div class="form-group">
                                <label> Coupon Id </label>
                                <input class="form-control" ng-model="offerObject.couponId"
                                       ng-disabled="couponalreadypresent" required
                                       type="text"/>
                            </div>


                            <div class="form-group">
                                <label> Offer Id </label>
                                <input class="form-control" ng-model="offerObject.offerId" required
                                       ng-disabled="couponalreadypresent"
                                       type="text"/>
                            </div>
                        </div>
                        <div data-ng-if="(offerObject.actionCategory.value == 'MOVE_TO_SCREEN' )">
                            <div class="form-group">
                                <label>Action Type</label>
                                <select
                                        class="form-control"
                                        data-ng-model="offerObject.offerActionType"
                                        data-ng-options=" actionList.description for actionList in actionLists"
                                        required>
                                    <option value="" selected hidden/>
                                </select>
                            </div>
                        </div>
                        <div data-ng-if="(offerObject.actionCategory.value == 'MOVE_TO_SECTION' )">
                            <div class="form-group">
                                <label>Action Type</label>
                                <select
                                        class="form-control"
                                        data-ng-model="offerObject.offerActionType"
                                        data-ng-options="actionList.description for actionList in actionLists"
                                        required>
                                    <option value="" selected hidden/>
                                </select>
                            </div>
                        </div>
                        <div data-ng-if="(offerObject.actionCategory.value == 'CHANGE_CART_SOURCE' )">
                            <div class="form-group">
                                <label>Action Type</label>
                                <select
                                        class="form-control"
                                        data-ng-model="offerObject.offerActionType"
                                        required>
                                    <option value="take_away">Take Away</option>
                                    <option value="dine_in">Dine In</option>
                                </select>
                            </div>
                        </div>
                        <div data-ng-if="(offerObject.actionCategory.value == 'MOVE_TO_CATEGORY' )">
                            <div class="form-group">
                                <label>Menu Category</label>
                                <select
                                        class="form-control"
                                        data-ng-model="offerObject.menuCategoryId"
                                        data-ng-options="category.groupId as category.groupName +'-'+ category.groupTag for category in categoryMenuList"
                                        required>
                                    <option value="" selected hidden/>
                                </select>
                            </div>
                        </div>
                        <div data-ng-if="(offerObject.actionCategory.value == 'NO_ACTION' )">
                        </div>
                        <div data-ng-if="(offerObject.actionCategory.value == 'LINK_REDIRECTION' )">
                            <div class="form-group">
                                <label> Banner RedirectionLink </label>
                                <input class="form-control" data-ng-model="offerObject.redirectionLink"
                                       placeholder="Please provide desc. " required type="text"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Brand Id </label>
                            <select class="form-control" ng-model="offerObject.brandId" required
                                    ng-disabled="couponalreadypresent || (isEditOffer && brandIdNotEditable)"
                                    data-ng-options="brand.id as (brand.id + ' - ' + brand.name) for brand in brands"
                                    data-ng-change="brandIdChanged()">
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Start Date</label>
                            <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                                <input class="form-control" data-ng-model="offerObject.startDate" type="text"
                                       placeholder="yyyy-MM-dd" ng-disabled="couponalreadypresent" required/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>End Date</label>
                            <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                                <input class="form-control" data-ng-model="offerObject.endDate" type="text"
                                       placeholder="yyyy-MM-dd"
                                       required ng-disabled="couponalreadypresent"/>
                            </div>
                        </div>


                        <div class="form-group">
                            <label>Status</label>
                            <select
                                    class="form-control"
                                    data-ng-model="offerObject.selectedOfferStatus"
                                    data-ng-options="OfferStatus as OfferStatus.name for OfferStatus in OfferStatus"
                                    required>
                            </select>
                        </div>


                        <div class="form-group">
                            <label>App Offer Type</label>
                            <select
                                    class="form-control"
                                    data-ng-model="offerObject.appOfferType"
                                    data-ng-options="appOfferType for appOfferType in appOfferTypeList"
                                    required>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="control-label">Unit List</label>
                                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                     options="Categories" selected-model="offerObject.CategoriesSelected">
                                </div>

                            </div>
                        </div>


                        <div class="form-group">
                            <label> Title </label>
                            <input class="form-control" data-ng-model="offerObject.title"
                                   placeholder="Please provide title. " required type="text"/>
                        </div>

                        <div class="form-group">
                            <label> Description </label>
                            <input class="form-control" data-ng-model="offerObject.description"
                                   placeholder="Please provide desc. " required type="text"/>
                        </div>


                        <div class="form-group">
                            <label> List Image </label>
                            <div class="row">
                                <div class="col-lg-8">
                                    <input class="btn btn-default"
                                           file-model="offerObject.listImageToUpload"
                                           style="width: 100%;"
                                           type="file">
                                </div>
                                <div class="col-lg-4">
                                    <button class="btn btn-primary "
                                            ng-click="uploadListImage(offerObject.couponCOde)">Upload
                                    </button>
                                    <div class="pull-right">
                                        <div ng-show="offerObject.listImage.name">
                                            <img height="50px" width="50px"
                                                 data-ng-click="openProductImageModal(offerObject.listImage.name)"
                                                 data-ng-src="{{imageSuffix}}{{offerObject.listImage.name}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <span ng-if="listImageUploaded">{{offerObject.listImage.name }} uploaded! </span>

                        </div>

                        <div class="form-group">
                            <label> Grid Image </label>
                            <div class="row">
                                <div class="col-lg-8">
                                    <input class="btn btn-default" file-model="offerObject.gridImageToUpload"
                                           style="width: 100%;"
                                           type="file">
                                </div>
                                <div class="col-lg-4">
                                    <button class="btn btn-primary"
                                            ng-click="uploadGridImage(offerObject.couponCOde)">
                                        Upload
                                    </button>
                                    <div class="pull-right">
                                        <div ng-show="offerObject.gridImage.name">
                                            <img height="50px" width="50px"
                                                 data-ng-click="openProductImageModal(offerObject.gridImage.name)"
                                                 data-ng-src="{{imageSuffix}}{{offerObject.gridImage.name}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <span ng-if="gridImageUploaded">{{offerObject.gridImage.name }} uploaded! </span>
                        </div>

                        <div ng-show="isAddNewOffer">
                            <div class="form-group clearfix">
                                <button class="btn btn-primary pull-left"
                                        ng-click="reset()">
                                    Reset
                                </button>
                                <button class="btn btn-primary pull-right"
                                        ng-click="addNewAppOfferData(offerObject.selectedOfferType,offerObject.couponCOde,offerObject.startDate,
                                        offerObject.endDate,offerObject.selectedOfferStatus,offerObject.CategoriesSelected,offerObject.offerActionType,
                                        offerObject.description,null,offerObject.couponId,offerObject.offerId,offerObject.selectedPartnerId.partnerId, 
                                        offerObject.appOfferType,offerObject.appOfferApplicabilityList,offerObject.brandId)">
                                    Add Offer
                                </button>
                            </div>
                        </div>

                        <div ng-show="isEditOffer">
                            <div class="form-group clearfix">
                                <button class="btn btn-primary pull-right"
                                        ng-click="addNewAppOfferData(offerObject.selectedOfferType,offerObject.couponCOde,offerObject.startDate,
                                        offerObject.endDate,offerObject.selectedOfferStatus,offerObject.CategoriesSelected,offerObject.offerActionType,
                                        offerObject.description,null,offerObject.couponId,offerObject.offerId,offerObject.selectedPartnerId.partnerId, 
                                        offerObject.appOfferType,offerObject.appOfferApplicabilityList,offerObject.brandId)">
                                    Update
                                    Offer
                                </button>
                            </div>
                        </div>
                        <!--</div>-->

                        <!--<div data-ng-if="(offerObject.selectedOfferType.validateCoupon == false) && showToAdmin">-->
                        <!--<div style="margin-bottom: 50px">-->
                        <!--</div>-->

                        <!--<div class="form-group">-->
                        <!--<label>Start Date</label>-->
                        <!--<div class="datepicker" data-date-format="yyyy-MM-dd"-->
                        <!--data-date-min-limit="{{today}}">-->
                        <!--<input class="form-control" data-ng-model="offerObject.startDate" type="text"-->
                        <!--placeholder="yyyy-MM-dd" required/>-->
                        <!--</div>-->
                        <!--</div>-->

                        <!--<div class="form-group">-->
                        <!--<label>End Date</label>-->
                        <!--<div class="datepicker" data-date-format="yyyy-MM-dd"-->
                        <!--data-date-min-limit="{{today}}">-->
                        <!--<input class="form-control" data-ng-model="offerObject.endDate" type="text"-->
                        <!--placeholder="yyyy-MM-dd"-->
                        <!--required/>-->
                        <!--</div>-->
                        <!--</div>-->


                        <!--<div class="form-group">-->
                        <!--<label>Status</label>-->
                        <!--<select-->
                        <!--class="form-control"-->
                        <!--data-ng-model="offerObject.selectedOfferStatus"-->
                        <!--data-ng-options="OfferStatus as OfferStatus.name for OfferStatus in OfferStatus"-->
                        <!--required>-->
                        <!--</select>-->
                        <!--</div>-->


                        <!--<div class="form-group">-->
                        <!--<div class="form-group">-->
                        <!--<label class="control-label">Unit List</label>-->
                        <!--<div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"-->
                        <!--options="Categories" selected-model="offerObject.CategoriesSelected">-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--</div>-->


                        <!--<div class="form-group" ng-show="offerObject.selectedOfferType.offerType=='BANNER_ACTION'">-->
                        <!--<label>Action Type</label>-->
                        <!--<select-->
                        <!--class="form-control"-->
                        <!--data-ng-model="offerObject.offerActionType"-->
                        <!--data-ng-options="ActionType as ActionType.name for ActionType in ActionTypes"-->
                        <!--required>-->
                        <!--</select>-->
                        <!--</div>-->

                        <!--<div class="form-group">-->
                        <!--<label> Title </label>-->
                        <!--<input class="form-control" data-ng-model="offerObject.title"-->
                        <!--placeholder="Please provide title. " required type="text"/>-->
                        <!--</div>-->

                        <!--<div class="form-group">-->
                        <!--<label> Description </label>-->
                        <!--<input class="form-control" ng-model="offerObject.description"-->
                        <!--placeholder="Please provide desc. " required type="text"/>-->
                        <!--</div>-->


                        <!--<div class="form-group">-->
                        <!--<label> List Image </label>-->
                        <!--<div class="row">-->
                        <!--<div class="col-lg-8">-->
                        <!--<input class="btn btn-default" file-model="offerObject.listImageToUpload"-->
                        <!--style="width: 100%;"-->
                        <!--type="file">-->
                        <!--</div>-->
                        <!--<div class="col-lg-4">-->
                        <!--<button class="btn btn-primary"-->
                        <!--ng-click="uploadListImage(offerObject.couponCOde)">-->
                        <!--Upload-->
                        <!--</button>-->
                        <!--<div class="pull-right">-->
                        <!--<div ng-show="offerObject.listImage.name">-->
                        <!--<img height="50px" width="50px"-->
                        <!--data-ng-click="openProductImageModal(offerObject.listImage.name)"-->
                        <!--data-ng-src="{{imageSuffix}}{{offerObject.listImage.name}}"/>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--<span ng-if="listImageUploaded">{{offerObject.listImage.name }} uploaded! </span>-->

                        <!--</div>-->

                        <!--<div class="form-group">-->
                        <!--<label> Grid Image </label>-->
                        <!--<div class="row">-->
                        <!--<div class="col-lg-8">-->
                        <!--<input class="btn btn-default" file-model="offerObject.gridImageToUpload"-->
                        <!--style="width: 100%;"-->
                        <!--type="file">-->
                        <!--</div>-->
                        <!--<div class="col-lg-4">-->
                        <!--<button class="btn btn-primary"-->
                        <!--ng-click="uploadGridImage(offerObject.couponCOde)">-->
                        <!--Upload-->
                        <!--</button>-->
                        <!--<div class="pull-right">-->
                        <!--<div ng-show="offerObject.gridImage.name">-->
                        <!--<img height="50px" width="50px"-->
                        <!--data-ng-click="openProductImageModal(offerObject.gridImage.name)"-->
                        <!--data-ng-src="{{imageSuffix}}{{offerObject.gridImage.name}}"/>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--<span ng-if="gridImageUploaded">{{offerObject.gridImage.name }} uploaded! </span>-->

                        <!--</div>-->

                        <!--<div ng-show="isAddNewOffer">-->
                        <!--<div class="form-group clearfix">-->
                        <!--<button class="btn btn-primary pull-left"-->
                        <!--ng-click="reset()">-->
                        <!--Reset-->
                        <!--</button>-->
                        <!--<button class="btn btn-primary pull-right"-->
                        <!--ng-click="addNewAppOfferData(offerObject.selectedOfferType,null,offerObject.startDate,offerObject.endDate,-->
                        <!--offerObject.selectedOfferStatus,offerObject.CategoriesSelected,offerObject.offerActionType,offerObject.description,offerObject.redirectionLink,-->
                        <!--null,null)">Add Offer-->
                        <!--</button>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--<div ng-show="isEditOffer">-->
                        <!--<div class="form-group clearfix">-->
                        <!--<button class="btn btn-primary pull-right"-->
                        <!--ng-click="addNewAppOfferData(offerObject.selectedOfferType,null,offerObject.startDate,-->
                        <!--offerObject.endDate,offerObject.selectedOfferStatus,offerObject.CategoriesSelected,-->
                        <!--offerObject.offerActionType,offerObject.description,offerObject.redirectionLink,null,null)">-->
                        <!--Update Offer-->
                        <!--</button>-->
                        <!--</div>-->
                        <!--</div>-->
                        <!--</div>-->
                    </fieldset>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- set ordering modal-->
<div aria-labelledby="SetOrderingModalLabel" class="modal fade" id="SetOrderingModal" role="dialog"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_setOrdering"> Set Ordering </h4>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="alert alert-info"> Please drag and drop elements to set ordering.
                        </div>


                        <div class="alert alert-success">OFFERS
                        </div>
                        <div class="groupListing" dnd-list="appOffersTypeOffer" dnd-allowed-types="['OFFER']">
                            <div class="list" data-ng-repeat="  offer in appOffersTypeOffer " draggable="true"
                                 dnd-type="'OFFER'"
                                 dnd-draggable="offer" dnd-effect-allowed="copyMove"
                                 dnd-moved="appOffersTypeOffer.splice($index, 1)">
                                {{offer.appOfferId}} : {{offer.description}}
                            </div>
                        </div>

                        <div class="alert alert-success">PROMOTIONS
                        </div>
                        <div class="groupListing" dnd-list="appOffersTypePromotion" dnd-allowed-types="['PROMOTION']">
                            <div class="list" data-ng-repeat="  promotion in appOffersTypePromotion " draggable="true"
                                 dnd-type="'PROMOTION'"

                                 dnd-draggable="promotion" dnd-effect-allowed="copyMove"
                                 dnd-moved="appOffersTypePromotion.splice($index, 1)"
                            >
                                {{promotion.appOfferId}} : {{promotion.description}}
                            </div>
                        </div>


                        <div class="alert alert-success">ALLIANCE
                        </div>
                        <div class="groupListing" dnd-list="appOffersTypeAlliance" dnd-allowed-types="['ALLIANCE']">
                            <div class="list" data-ng-repeat="  alliance in appOffersTypeAlliance " draggable="true"
                                 dnd-type="'ALLIANCE'"
                                 dnd-draggable="alliance" dnd-effect-allowed="copyMove"
                                 dnd-moved="appOffersTypeAlliance.splice($index, 1)"
                            >
                                {{alliance.appOfferId}} : {{alliance.description}}
                            </div>
                        </div>


                        <div class="alert alert-success">WALLET_BANNER
                        </div>
                        <div class="groupListing" dnd-list="appOffersTypeWalletBanner"
                             dnd-allowed-types="['WALLET_BANNER']">
                            <div class="list" data-ng-repeat="  wallet in appOffersTypeWalletBanner " draggable="true"
                                 dnd-type="'WALLET_BANNER'"

                                 dnd-draggable="wallet" dnd-effect-allowed="copyMove"
                                 dnd-moved="appOffersTypeWalletBanner.splice($index, 1)"
                            >
                                {{wallet.appOfferId}} : {{wallet.description}}
                            </div>
                        </div>


                        <div class="alert alert-success">CHAAYOS_GIFTING
                        </div>
                        <div class="groupListing" dnd-list="appOffersTypeWalletChaayosGifting"
                             dnd-allowed-types="['CHAAYOS_GIFTING']">
                            <div class="list" data-ng-repeat="  gifting in appOffersTypeWalletChaayosGifting "
                                 draggable="true"
                                 dnd-type="'CHAAYOS_GIFTING'"
                                 dnd-draggable="gifting" dnd-effect-allowed="copyMove"
                                 dnd-moved="appOffersTypeWalletChaayosGifting.splice($index, 1)"
                            >
                                {{gifting.appOfferId}} : {{gifting.description}}
                            </div>
                        </div>
                        <br>
                        <div class="row">
                            <div class="col-xs-12 text-right">
                                <button class="btn btn-primary pull-right"
                                        ng-click="submitOrdering()">Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Unit list view modal-->
<div aria-labelledby="UnitListViewModalLabel" class="modal fade" id="UnitListViewModal" role="dialog"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_unitList"> Applicable Unit List </h4>
            </div>
            <div class="modal-body">
                <label>Unit Lists</label>
                <div class="row">
                    <div class="col-xs-12">
                        <div ng-if="unitDetailsList.length>0">
                            <table class="table table-striped table-bordered">
                                <thead style="background-color: #e7e7e7">
                                <th>Unit Id</th>
                                <th>Unit Name</th>
                                </thead>
                                <tbody>
                                <tr ng-repeat="unitList in unitDetailsList">
                                    <td>{{unitList.unitId}}</td>
                                    <td>{{unitList.unitName}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div ng-if="unitDetailsList.length<=0">
                            <div class="alert alert-info"> Applicable for all Units
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--image view modal-->
<div class="modal fade" id="displayImageModal" style="z-index: 9999;" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document" style="width: 700px;">
        <div class="modal-content">
            <div class="frame" style="margin-top: 40px;margin: auto;">
                <img style="    max-height: 70vh;max-width: 70vw;"
                     data-ng-src="{{imageSuffix}}{{imageSrc}}"/>
            </div>
        </div>
    </div>
</div>