<style xmlns="http://www.w3.org/1999/html">
    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }

    * {
        box-sizing: border-box;
    }

    .container {
        position: relative;
        display: flex;
        flexDirection: column;
        height: 85vh;
        width: 100%;
        margin-right: 16px;
        padding: 4px;
    }

    .text-container {
        flex: 1;
        overflow: auto;
        height: 100%;
        width: 100%;
    }

    .fab {
        position: absolute;
        right: 20px;
        bottom: 20px;
    }
</style>

<div data-ng-init="init()">
    <div class="row" style="margin-bottom: 20px;">
        <div class="col-xs-12">
            <p class="title">Unit Drool Version Mapping</p>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Select Drool File Type *</label>
                </div>
                <div class="col-xs-8 form-group">
                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                        data-ng-model="selectedDroolFileType" data-placeholder="Select a Drool File"
                        data-ng-change="changeProduct(prodDetails.productId)">
                        <option value=""></option>
                        <option data-ng-repeat="file in droolFileTypes" value="{{file}}">
                            {{file}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="col-xs-4 region-card">MultiRegion Select</label>
                </div>
                <div class="col-xs-8 form-group">
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings" options="trimmedRegions"
                        selected-model="selectedRegion" class="col-xs-8 region-card">
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="row">
        <button class="btn btn-primary pull-right" data-ng-click="getUnitDroolMappingDetails()">Get Details</button>
    </div>


</div>
<div class="row container" data-ng-if="unitDroolFileMappingList != null && unitDroolFileMappingList.length > 0">
    <div class="col-xs-12 text-container">
        <table class="table table-bordered">
            <thead style="background-color: #50773e; color: #ffffff">
                <tr>
                    <th>Check &nbsp;
                        <input type="checkbox" data-ng-model='checkBoxModal.checkAll' style="width: 20px; height: 20px"
                            data-ng-click="updateAll(checkBoxModal.checkAll)">
                    </th>
                    <th>Unit Name&nbsp;
                        <input type="text" data-ng-model="unitName" placeholder="enter Unit Name to filter row"
                            class="form-control" />
                    </th>
                    <th>
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Version</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                            data-ng-model="checkBoxModal.updatedFileVersion" data-ng-options="version for version in fileVersionList" >
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                        data-ng-model="checkBoxModal.checkAllVersion"
                                        data-ng-click="changeAllUnitsVersion(checkBoxModal.updatedFileVersion,checkBoxModal.checkAllVersion)">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <th>
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Status</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                        data-ng-model="checkBoxModal.updatedStatus">
                                        <option>ACTIVE</option>
                                        <option>IN_ACTIVE</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                        data-ng-model="checkBoxModal.checkAllStatus"
                                        data-ng-click="changeAllStatus(checkBoxModal.updatedStatus,checkBoxModal.checkAllStatus)">
                                </td>
                            </tr>
                        </table>
                    </th>
                </tr>
            </thead>


            <tbody>
                <tr data-ng-repeat="detail in unitDroolFileMappingList|orderBy : 'unit.name' | filter : unitName"
                    data-ng-class="{'row-selected': detail.checked}">

                    <td><input type="checkbox" style="width: 33px; height: 20px" data-ng-model='detail.checked'
                            data-ng-click="changeRow(detail.checked, detail)"></td>
                    <td>{{detail.unitName}}</td>
                    <td>
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.currentVersion}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-options="version for version in fileVersionList" a data-ng-model="detail.updatedVersion">
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td>

                    <!--                    for Status -->
                    <td>
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.currentStatus}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-model="detail.updatedStatus">
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option>ACTIVE</option>
                                        <option>IN_ACTIVE</option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

</div>

<div data-ng-if="unitDroolFileMappingList != null && unitDroolFileMappingList.length > 0" class="row container">
    <button class="btn btn-primary pull-right fab" data-ng-click="submitDetails()">
        SUBMIT
    </button>
</div>


<!------  Update summary Modal ------->
<div class="modal fade" id="updatedUnitVersionModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="font-size: 10px; width: 860px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Summary: Drool Versioning Update
                </h4>
            </div>

            <div class="modal-body">
                <div>
                    <button class="btn btn-primary pull-right" data-ng-click="submitUpdatedMappingList()">Update
                    </button>
                </div>
                <br>
                <br>
                <br>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Unit Name&nbsp;</th>
                            <th>Drool File&nbsp;</th>
                            <th>Current Version</th>
                            <th>Updated Version</th>
                            <th>Current Status</th>
                            <th>Updated Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat='detail in unitDroolFileMappingList'>
                            <td data-ng-if="detail.checked">{{$index}}</td>
                            <td data-ng-if="detail.checked">{{detail.unitId}}</td>
                            <td data-ng-if="detail.checked">{{selectedDroolFileType}}</td>
                            <td data-ng-if="detail.checked">{{detail.currentVersion}}</td>
                            <td data-ng-if="detail.checked">{{detail.updatedVersion}}</td>
                            <td data-ng-if="detail.checked">{{detail.currentStatus}}</td>
                            <td data-ng-if="detail.checked">{{detail.updatedStatus}}</td>
                        </tr>
                        <tr>
                            <td colspan="14" align="right">
                                <div class="form-group clearfix">
                                    <button class="btn btn-primary pull-right"
                                        data-ng-click="submitUpdatedMappingList()">Update
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</div>