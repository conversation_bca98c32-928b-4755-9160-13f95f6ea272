<div class="row" data-ng-init="init()">
	<div class="col-lg-12"><br>
	
        <h1 class="page-header">
        	Employee Permission Mapping 
        </h1>
    </div>
    
    <div class="form-group">
      <label>Employee *</label>
        <select class="form-control" ng-model="selectedEmployee" ng-options="employeeListData as (employeeListData.name + '-' + employeeListData.departmentName+'(' + employeeListData.designation+')') for employeeListData in employeeList | orderBy:'name' track by employeeListData.id" ng-change="viewEmployeePermission(selectedEmployee.id)" required ></select>  
    </div>
</div>