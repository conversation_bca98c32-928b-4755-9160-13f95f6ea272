<div id="fullOfferDiv" ng-init="init()" xmlns:70vh xmlns:70vh xmlns:70vh
     xmlns:max-height="http://www.w3.org/1999/xhtml">
    <div id="offerListDivDetail">
        <div class="row">
            <div class="col s8">
                <br>
                <h1 class="page-header">
                    App Section Management
                </h1>
                <span style="color: #1ca62c">**For updating category data please update<strong style="color: #c9302c"> SET SHUFFLING</strong> <br></span>
            </div>
            <div class="form-group ">
                <label>Company Id</label>
                <div class="form-group " style="width: 75%">
                    <select class="form-control" ng-model="optedCompanyId"
                            ng-options=" company.name for company in companiesList">
                        <option value="" hidden selected/>
                    </select>
                    <br>
                </div>
                <label>Partner Name</label>
                <div class="form-group " style="width: 75%">
                    <select class="form-control" ng-model="selectedPartnerId"
                            ng-options=" company.name for company in partnerId">
                        <option value="" hidden selected/>
                    </select>
                    <br>
                    <button class="btn btn-primary" data-ng-click="getSequenceVal()"> search</button>
                </div>
            </div>
            <br>

            <div class="col-lg-2">
                <button class="btn btn-primary"
                        data-toggle="modal"
                        data-target="#addSection">
                    <i class="fa fa-plus fw"></i> Add Sequence
                </button>
            </div>
            <div class="col-lg-2">
                <button class="btn btn-primary " data-ng-click="setList()"
                        data-toggle="modal"
                        data-target="#setShuffling">
                    Set Shuffling
                </button>
            </div>
        </div>
        <br>
        <div class="row " ng-if="resultFound != undefined && resultFound">
            <div class="col-xs-12">
                <div class="row ">
                    <div class="col-xs-12 overflow">

                        <table class="table table-striped table-bordered">
                            <thead style="background-color: #e7e7e7">
                            <th>Name</th>
                            <th>Code</th>
                            <th>CompanyID</th>
                            <th>Title</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Display Type</th>
                            <th colspan="3" style="text-align:center">Action</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat=" get in getSection">
                                <td>{{get.name}}</td>
                                <td>{{get.code}}</td>
                                <td>{{get.companyId}}</td>
                                <td>{{get.title}}</td>
                                <td width="12%">{{get.desc}}</td>
                                <td>{{get.active}}</td>
                                <td>{{get.displayType}}</td>
                                <td>{{get.bannerType}}</td>
                                <td>
                                    <button type="button" class="btn btn-info" data-toggle="modal"
                                            data-target="#updateSection" ng-click="onUpdate(get)">Update
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add New App Offer Modal -->
<div id="addSection" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Section</h4>
            </div>
            <div class="modal-body">
                <div class="form-group ">
                    <label>Company Id</label>
                    <div class="form-group " style="width: 75%">
                        <select class="form-control" ng-model="addSequence.companyId"
                                ng-options="  company.name for company in companiesList">
                        </select>
                    </div>
                </div>

                <div class="form-group ">
                    <label>Partner Name</label>
                    <div class="form-group " style="width: 75%">
                        <select class="form-control" ng-model="addSequence.partnerName"
                                ng-options="  partner.name for partner in partnerId">
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Name</label>
                    <div class="form-group">
                        <input class="form-control" data-ng-model="addSequence.name"
                               placeholder="Please provide title. " required type="text"/>
                    </div>

                </div>

                <div class="form-group">
                    <label> Title </label>
                    <input class="form-control" data-ng-model="addSequence.title"
                           placeholder="Please provide title. " required type="text"/>
                </div>

                <div class="form-group">
                    <label> Description </label>
                    <input class="form-control" data-ng-model="addSequence.desc"
                           placeholder="Please provide desc. " required type="text"/>
                </div>


                <div class="form-group">
                    <label>Code</label>
                    <div class="form-group">
                        <input class="form-control" data-ng-model="addSequence.type"
                               ng-disabled="isEdit"
                               placeholder="Please provide desc. " required type="text"/>
                    </div>

                </div>

                <div class="form-group">
                    <label>Status</label>
                    <div class="form-group">
                        <select class="form-control" data-ng-model="addSequence.status"
                                data-ng-options=" status for status in statusVal" val=addSequence.status>
                            <option hidden selected value=""/>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Display Type</label>
                    <div class="form-group">
                        <select class="form-control" data-ng-model="addSequence.displayType"
                                data-ng-options=" type for type in displayTypes" val=addSequence.displayType>
                            <option hidden selected value=""/>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Banner Type</label>
                    <div class="form-group">
                        <select class="form-control" data-ng-model="addSequence.bannerType"
                                data-ng-options=" type for type in bannerTypes" val=addSequence.bannerType>
                            <option hidden selected value=""/>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-left"
                            ng-click="resetAppSection()">
                        Reset
                    </button>
                    <button class="btn btn-primary pull-right" data-dismiss="modal"
                            ng-click="addSection()">
                        Add Sequence
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>


<div id="updateSection" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Update Section</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Code</label>
                    <div class="form-group">
                        <input class="form-control" data-ng-model="code"
                               required type="text" disabled>
                    </div>
                </div>

                <div class="form-group ">
                    <label>Company Id</label>
                    <div class="form-group " style="width: 75%">
                        <select class="form-control" data-ng-model="company"
                                ng-options="  company.name for company in companiesList">
                            <option value="" hidden selected/>
                        </select>
                    </div>
                </div>
                <div class="form-group ">
                    <label>Partner Name</label>
                    <div class="form-group " style="width: 75%">
                        <select class="form-control" ng-model="selectedPartnerId"
                                ng-options=" company.name for company in partnerId">
                            <option value="" hidden selected/>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Name</label>
                    <div class="form-group">
                        <input class="form-control" data-ng-model="name"
                               placeholder="Please provide title. " required type="text"/>
                    </div>

                </div>

                <div class="form-group">
                    <label> Title </label>
                    <input class="form-control" data-ng-model="title"
                           placeholder="Please provide title. " required type="text"/>
                </div>

                <div class="form-group">
                    <label> Description </label>
                    <input class="form-control" data-ng-model="description"
                           placeholder="Please provide desc. " required type="text"/>
                </div>

                <div class="form-group">
                    <label>Status</label>
                    <div class="form-group">
                        <select class="form-control" data-ng-model="status"
                                data-ng-options="active for active in statusVal">
                            <option value="" hidden selected/>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Display Type</label>
                    <div class="form-group">
                        <select class="form-control" data-ng-model="displayType"
                                ng-options="type for type in displayTypes">
                            <option value="" hidden selected/>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Banner Type</label>
                    <div class="form-group">
                        <select class="form-control" data-ng-model="bannerType"
                                ng-options="type for type in bannerTypes">
                            <option value="" hidden selected/>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right"
                            ng-click="updateSection()" data-dismiss="modal">
                        Update
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="setShuffling" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_setOrdering"> Set Ordering </h4>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">

                        <div class="form-group">
                            <label> Description </label>
                            <input class="form-control" data-ng-model="shuffledata.desc"
                                   placeholder="Please provide title. " required type="text"/>
                        </div>
                        <div class="form-group ">
                            <label>Company Id</label>
                            <div class="form-group " style="width: 75%">
                                <select class="form-control" ng-model="optedCompanyId"
                                        ng-options="  company.name for company in companiesList">
                                    <option value="" hidden selected/>
                                </select>
                            </div>
                        </div>

                        <div class="form-group ">
                            <label>Partner Name</label>
                            <div class="form-group " style="width: 75%">
                                <select class="form-control" ng-model="selectedShufflePartnerId"
                                        ng-options=" company.name for company in partnerId">
                                    <option value="" hidden selected/>
                                </select>
                            </div>
                        </div>

                        <button class="btn btn-primary" data-ng-click="setList()"> search</button>

                        <div ng-if="result">
                            <div class="alert alert-info"> Please Drag and Drop to Shuffle.
                            </div>
                            <div class="groupListing" dnd-list="lastUpdatedSequences">
                                <div class="list"
                                     data-ng-repeat="offer in lastUpdatedSequences "
                                     draggable="true"
                                     dnd-draggable="offer"
                                     dnd-effect-allowed="move"
                                     dnd-moved="lastUpdatedSequences.splice($index, 1)"
                                >
                                    {{offer.code}}
                                </div>
                            </div>
                            <br>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 text-right">
                                <button class="btn btn-primary pull-right"
                                        ng-click="submitSequence()" data-dismiss="modal">Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
