<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Delivery Mapping </h1>
    </div>

    <div class="col-xs-12">
        <div class="form-group">
            <label>Type *</label>
            <select class="form-control" ng-model="newUnitFamily">
                <option ng-repeat="family in families | orderBy" value="{{family}}">
                    {{family}}
                </option>
            </select>
        </div>

        <div class="form-group">
            <label>Selects Region *</label>
            <select class="form-control" ng-model="newUnitRegion">
                <option ng-repeat="region in regions | orderBy" value="{{region}}">{{region}}</option>
            </select>
        </div>
        <div class="form-group">
            <label>Delivery Partner*</label>
            <select class="form-control" ng-model="selectDeliveryMode"
                    ng-options="selectedDeliveryModeData as selectedDeliveryModeData.name for selectedDeliveryModeData in refPartnerLists track by selectedDeliveryModeData.code"
                    ng-change="showDeliveryDetailsResult(selectDeliveryMode.code)">
            </select>
        </div>


        <div class="col-lg-12"><br>
            <div style align="right">
                <button class="btn btn-primary pull-right" ng-click="ViewUnitDeliveryDataList()">SUBMIT</button>
            </div>

        </div>

        <div id="viewDeliveryMappingUnitWise" style="display:none">

        </div>
    </div>
</div>
