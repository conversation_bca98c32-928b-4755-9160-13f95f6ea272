<br>
<div class="row" ng-init="init()">
  <div class="col-xs-2" style="font-size: medium; margin-top: 4px">
      <label class="control-label">Regions</label>
  </div>
  <div class="col-xs-4 region-card" ng-dropdown-multiselect=""
       options="regionList"
       selected-model="selectedRegionList" extra-settings="multiSelectSettings">
  </div>
  <button data-ng-click="fetchUnits()" class="btn btn-primary">Get Units</button>
</div>
<br>
<br>
<div class="row">
  <div class="col-xs-2" style="font-size: medium; margin-top: 4px">
      <label class="control-label">Search Unit</label>
  </div>
  <div class="col-xs-4">
      <input type="text" ng-model="unitFilter" ng-change="filter() "
             placeholder="Filter" class="form-control"/>
  </div>
</div>
<br>
<div class="col-xs-12"  data-ng-show="unitList!=null && unitList.length > 0" >
  <table class="table table-striped table-bordered" id="tableDataStructure" style="margin-padding:0px;">
      <thead>
      <th>Unit Id&nbsp;</th>
      <th>Unit Name&nbsp;</th>
      <th>Region&nbsp;</th>
      <th colspan="3" align="center">Contact Details &nbsp;</th>

      </thead>
      <tbody>
      <tr  ng-repeat="unit in unitList | filter:unitFilter | orderBy :'name' " >
          <td>{{unit.id}}</td>
          <td>{{unit.name}}</td>
          <td>{{unit.region}}</td>
          <td colspan="3" >

            <div ng-show="unit.contactDetailsExist" ng-repeat="contact in unit.contactDetails">
                {{$index+1}}. {{contact.name}} -&nbsp;&nbsp;&nbsp;{{contact.code}}
              </div>
            </divng-repeat=>
            <div data-ng-show="!unit.contactDetailsExist">
              No Contact Details Available
            </div>
          </td>
          
          <td>
              <button
                  class="btn btn-primary"
                  ng-click = "openUpdateContactsModal(unit)"
                  data-ng-show="contactDetailsExist"
              >Update
              </button>
              <button 
                data-ng-show="!contactDetailsExist"
                class="btn btn-primary"
                ng-click = "openUpdateContactsModal(unit)">Add Contact Details
              </button>

          </td>
      </tr>
      </tbody>
  </table>
</div>

<div class="modal fade" id="contactDetailsUpdateModal" tabindex="-1" role="dialog" aria-labelledby="contactDetailsUpdateModalTitle">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h3 class="modal-title" id="contactDetailsUpdateModalTitle">{{unitContactsToBeUpdated.name}}</h3>
      </div>
      <div class="modal-body">
        <div>
          <h4>
            First Person Details
          </h4>
          <div  style="display:flex" >           
            <div  class="col-xs-4">
              <label>Name</label>
              <input type="text"
                    class="form-control"
                    ng-model="unitContactsToBeUpdated.contactDetails[0].name"
                    placeholder="Enter Name"
                    />
            </div>
            <div class="col-xs-4" >
              <label>Contact Number </label>
              <input type="text"
                    class="form-control"
                    ng-model="unitContactsToBeUpdated.contactDetails[0].code"
                    placeholder="Enter Number"
                    />
            </div>
          </div>
        </div>
        <br>
        <div>
          <h4>
            Second Person Details
          </h4>
          <div  style="display:flex" >        
            <div  class="col-xs-4">
              <label>Name </label>
              <input type="text"
                    class="form-control"
                    placeholder="Enter Name"
                    ng-model="unitContactsToBeUpdated.contactDetails[1].name"
                    />
            </div>
            <div class="col-xs-4 p-10" >
              <label>Contact Number</label>
              <input type="text"
                    class="form-control"
                    ng-model="unitContactsToBeUpdated.contactDetails[1].code"
                    placeholder="Enter Number"
                    />
            </div>
          </div>
        </div>
        <br>
        <div>
          <h4>
            Third Person Details
          </h4>
          <div  style="display:flex" >        
            <div  class="col-xs-4">
              <label>Name </label>
              <input type="text"
                    class="form-control"
                    placeholder="Enter Name"
                    ng-model="unitContactsToBeUpdated.contactDetails[2].name"
                    />
            </div>
            <div class="col-xs-4" >
              <label>Contact Number</label>
              <input type="text"
                    class="form-control"
                    ng-model="unitContactsToBeUpdated.contactDetails[2].code"
                    placeholder="Enter Number"
                    />
            </div>
          </div>
          <br>
      </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" ng-click="updateContactDetail(unitContactsToBeUpdated)">Save changes</button>
      </div>
    </div>
  </div>
</div>