<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">Winback Coupons Lookup
            <button class="btn btn-primary pull-right" data-toggle="modal" ng-click="openDownloadModal()"><i class="fa fa-plus fw"></i> Download sheet</button>
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        Filter:
        <input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
    </div>
</div>

<div class="row">
    <div class="col-xs-12" ng-if="filteredItems > 0">
        <p>Filtered {{ filtered.length }} of {{ totalItems}} total results</p>
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-striped table-bordered">
                    <thead>
                    <th> Id&nbsp;<a ng-click="sort_by('id');"><i class="glyphicon glyphicon-sort"></i></a></th>
                    <th> Contact Number&nbsp;</th>
                    <th> Customer Name&nbsp;</th>
                    <th> Source </th>
                    <th> Coupon Code&nbsp;</th>
                    <th> Offer Description&nbsp;</th>
                    <th> Compensation Reason</th>
                    <th> End Date </th>
                    <th> Notified </th>
                    </thead>
                    <tbody>
                    <tr ng-repeat="result in filtered = (resultList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                        <td>{{result.id}}</td>
                        <td>{{result.contactNumber}}</td>
                        <td>{{result.customerName}}</td>
                        <td data-ng-if="result.orderSource=='DELIVERY'">{{result.orderSource}} ({{result.channelPartner}})</td>
                        <td data-ng-if="result.orderSource!='DELIVERY'"> {{result.orderSource}} </td>
                        <td>{{result.couponCode}}</td>
                        <td>{{result.offerDescription}}</td>
                        <td>{{result.compansationReason}}</td>
                        <td>{{result.endDate}}</td>
                        <td data-ng-if="result.isNotified=='Y'"> Notified</td>
                        <td data-ng-if="result.isNotified=='N'">
                            <button class="btn btn-primary" data-ng-click="markNotify(result.id)">Notify</button>
                        </td>

                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
    </div>
</div>

<div align="center"><b>
    <uib-pagination total-items="totalItems" ng-model="currentPage" max-size="5"
                    boundary-link-numbers="true" ng-disabled="false" rotate="true"
                    items-per-page="entryLimit" class="pagination-sm"></uib-pagination>
</b></div>



<div
        class="modal fade"
        id="downloadSheet"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        data-ng-click="init()"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">Download Winback Coupon Data</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-sm-4 control-label">Start Date</label>
                    <div class="col-sm-8">
                        <div class="datepicker" data-date-format="yyyy-MM-dd">
                            <input class="form-control" data-ng-model="startDate" type="text" placeholder="yyyy-MM-dd" required />
                        </div>
                    </div>
                </div>
                <div class="form-group" style="margin-top:50px">
                    <label class="col-sm-4 control-label">End Date</label>
                    <div class="col-sm-8">
                        <div class="datepicker" data-date-format="yyyy-MM-dd">
                            <input class="form-control" data-ng-model="endDate" type="text" placeholder="yyyy-MM-dd" required />
                        </div>
                    </div>
                </div>
                <div>
                <div class="form-group" style="margin-top:100px;">
                    <div style="justify-content:center; display:flex">
                        <button class="btn btn-primary"
                                data-ng-click="downloadSheetBetweenDates()">Download
                        </button>
                        <button class="btn btn-primary" style="margin-left:5px;"
                                data-ng-click="downloadSheet()">Download All
                        </button>
                    </div>
                </div>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>