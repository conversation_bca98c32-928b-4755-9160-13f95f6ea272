<style type="text/css">
    form.tab-form-demo {
        margin: 20px 20px;
    }

    .tabDiv {
        margin-top: 20px;
        height: 350px;
        max-height: 350px;
    }

    .divInnerRow {
        margin-top: 20px;
        height: 50px;
        max-height: 50px;
        width: 100%;
    }

    .tabPane {
        border: 1px solid #ddd;
        border-radius: 0px 0px 5px 5px;
        padding: 10px;
    }
</style>

<div data-ng-init="init()">
    <div class="panel panel-info">
        <div class="panel-heading">{{recipeDetail.name.length > 0 ?
            recipeDetail.name : 'Please select recipe'}}
            <span data-ng-if="recipeDetail.name && recipeDetail.profile" style="color:red;"> For Profile {{recipeDetail.profile}}</span>
        </div>
        <div class="panel-body">
            <uib-tabset
                    class="tabPane"
                    active="activeTab">
                <uib-tab index="0" heading="Product Selection">
                    <div class="row tabDiv">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Select Product</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2
                                            class="form-control" style="width: 100% !important"
                                            id="productSelected"
                                            data-ng-model="product.selectedProductId"
                                            data-ng-change="onSelectProduct(product.selectedProductId)">
                                        <option value=""></option>
                                        <option data-ng-repeat="product in productsInfo " value="{{product.id}}">
                                            {{product.name}} - {{product.status}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2 class="form-control" style="width: 100% !important"
                                            data-ng-model="selectedDimensionId"
                                            data-placeholder="Select a dimension"
                                            data-ng-change="selectDimension(selectedDimensionId)">
                                        <option data-ng-repeat="dimension in selectedDimensionProfile.content"
                                                value="{{dimension.id}}">{{dimension.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12" data-ng-if="existingRecipes != null && existingRecipes.length > 0">
                            <div class="row divInnerRow">
                                <div class="col-xs-12">
                                    <button class="btn btn-primary pull-right" data-ng-show="true"
                                            data-ng-click="cloneRecipeByProfile(selectedRecipeId)"
                                            style="margin-bottom: 10px;">
                                        <i class="fa fa-plus"></i> Clone Recipe Steps
                                    </button>
                                </div>
                                <div class="col-xs-12">
                                    <table class="table table-striped table-bordered">
                                        <tr>
                                            <th>Select</th>
                                            <th>Recipe Id</th>
                                            <th>Recipe Name</th>
                                            <th>Profile</th>
                                            <th>Start date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                        <tbody>
                                        <tr data-ng-repeat="recipe in existingRecipes | orderBy: 'profile' track by recipe.recipeId">
                                            <td>
                                                <input type="radio" name="recipeGroup" data-ng-model="selectedRecipeId"
                                                       data-ng-value="recipe.recipeId"
                                                       data-ng-click="changeRecipeId(selectedRecipeId)"/>
                                            </td>
                                            <td>{{recipe.recipeId}}</td>
                                            <td>{{recipe.name}}</td>
                                            <td>{{recipe.profile}}</td>
                                            <td>{{recipe.startDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                                            <td>{{recipe.status}}</td>
                                            <td align="center">
                                                <span data-ng-if="recipe.status == 'ACTIVE' && recipe.isEditable"
                                                      data-ng-click="editActiveRecipe(recipe)"
                                                      style="cursor: pointer" title="Edit Recipe">
                                                    <i class="fa fa-edit"
                                                       style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                                <span data-ng-if="recipe.status == 'IN_PROGRESS'"
                                                      data-ng-click="selectRecipeForEdit(recipe)"
                                                      style="cursor: pointer" title="Edit Recipe">
                                                    <i class="fa fa-edit"
                                                       style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12" style="text-align: center;">
                            <button class="btn btn-primary" data-ng-click="addNewRecipe()"
                                    data-ng-show="product != null && product != undefined && product.selectedProductId != null && selectedDimensionId != null && ( existingRecipes == null || existingRecipes.length < 1)">
                                <i class="fa fa-plus"></i> Add Recipe
                            </button>
                            <button class="btn btn-primary" data-ng-click="cloneFromRecipe()"
                                    data-ng-show="product != null && product != undefined && product.selectedProductId != null && selectedDimensionId != null && ( existingRecipes == null || existingRecipes.length < 1)">
                                <i class="fa fa-plus"></i> Clone Recipe
                            </button>
                        </div>
                    </div>
                </uib-tab>
                <uib-tab index="1" heading="Recipe Detail">
                    <div class="row tabDiv">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Recipe Name</label>
                                </div>
                                <div class="col-xs-8">
                                    <input class="form-control" data-ng-model="recipeDetail.name" disabled/>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    {{recipeDetail.dimension.name}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Profile</label>
                                </div>
                                <div class="col-xs-8">
                                    {{recipeDetail.profile}}
                                </div>
                            </div>
                        </div>
                    </div>
                </uib-tab>

                <uib-tab index="2" heading="Recipe Steps">

                    <div>

                        <div class="row divInnerRow">

                            <div class="col-xs-12" style="margin-top: 20px;">

                                <div class="col-lg-2">
                                    <button class="btn btn-primary"
                                            ng-click="addRecipeStepMediaDetail()"
                                            data-toggle="modal"
                                            data-target="#addRecipeMedia">
                                        <i class="fa fa-plus fw"></i> Add Step
                                    </button>
                                </div>
                                <br>
                                <div style="margin-top: 30px" ;>
                                    Instructions:<br>

                                    <div style="font-size: 12px; color: #303030;">
                                        Image resolution: 1:1(max 200px:200px) and max size: 200KB<br>

                                        Video resolution: 1:1(max 200px:200px) and max: 5MB <br>

                                        Image must be in jpg/jpeg and video in mp4
                                    </div>
                                </div>
                                <br>

                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Drag</th>
                                        <th>Step no.</th>
                                        <th>Media Type</th>
                                        <th>Recipe Step Name</th>
                                        <th>Preview</th>
                                        <th colspan="2" style="text-align: center">Action</th>
                                    </tr>
                                    <tbody dnd-list="recipeMediaDetail">
                                    <tr ng-repeat="get in recipeMediaDetail" dnd-draggable="get" draggable="true"
                                        dnd-effect-allowed="move" dnd-moved="recipeMediaDetail.splice($index, 1)">
                                        <td align="center">
                                            <img src="img/drag-and-drop.png" width="30" height="30"/>
                                        </td>
                                        <td>{{$index+1}}</td>
                                        <td>{{get.mediaType}}</td>
                                        <td>{{get.recipeStepName}}</td>
                                        <td style="align-content: center">
                                            <button
                                                    class="btn btn-primary"
                                                    id="download"
                                                    data-toggle="modal"
                                                    ng-click="downloadRecipeMedia(get)">Download
                                            </button>
                                        </td>
                                        <td style="align-content: center">
                                            <button
                                                    class="btn btn-primary"
                                                    id="edit"
                                                    data-toggle="modal"
                                                    ng-click="editRecipeStepMediaDetail($index)"
                                                    data-target="#editRecipeStepMedia">Edit
                                            </button>
                                        </td>
                                        <td style="align-content: center">
                                            <button
                                                    class="btn btn-danger"
                                                    ng-click="deleteRecipeStepMedia($index)">DELETE
                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </uib-tab>
            </uib-tabset>

        </div>
        <div class="row pager" style="margin-top: 10px;">
            <nav>
                <ul class="pager">
                    <li data-ng-if="activeTab!=0" data-ng-click="setPrevTab(activeTab)" class="previous"
                        style="cursor: pointer">
                        <a><span aria-hidden="true">&larr;</span>Previous</a>
                    </li>
                    <li data-ng-if="activeTab==0" data-ng-click="clearAll(true)" style="cursor: pointer"><a>Reset </a>
                    </li>
                    <li data-ng-if="activeTab==2" data-ng-click="submitRecipeMediaDetail()" style="cursor: pointer"><a>Submit </a>
                    </li>
                    <li data-ng-if="activeTab > 0 && activeTab < 2" data-ng-click="setNextTab(activeTab)" class="next"
                        style="cursor: pointer">
                        <a>Next <span aria-hidden="true">&rarr;</span></a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<div id="addRecipeMedia" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Recipe Step</h4>
            </div>
            <div class="modal-body">

                <div class="form-group">
                    <label>Recipe Step Name</label>
                    <div class="form-group">
                        <input class="form-control" data-ng-model="newRecipeStepName"
                               placeholder="Please provide recipe step name. " required type="text"/>
                    </div>
                </div>
                <div class="form-group">
                    <label> Recipe Media </label>
                    <div class="row">
                        <div class="col-lg-8">
                            <input class="btn btn-default"
                                   file-model="getRecipeMediaToUpload"
                                   style="width: 100%;"
                                   type="file"/>
                        </div>
                        <div class="col-lg-4">
                            <button class="btn btn-primary "
                                    ng-click="uploadRecipeMedia()">Upload
                            </button>
                        </div>

                        <div class="col-lg-10">

                            Instructions:<br>

                            <div style="font-size: 12px; color: #303030;">
                                Image resolution: 1:1(max 200px:200px) and max size: 200KB<br>

                                Video resolution: 1:1(max 200px:200px) and max: 5MB <br>

                                Image must be in jpg/jpeg and video in mp4
                            </div>
                        </div>

                    </div>


                </div>

                <div class="modal-footer">
                    <div class="form-group clearfix">
                        <button class="btn btn-primary pull-left"
                                ng-click="resetRecipeStepMediaDetail()">
                            Reset
                        </button>
                        <button class="btn btn-primary pull-right"
                                ng-click="addRecipeMediaDetailVal()">
                            Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="editRecipeStepMedia" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Recipe Step</h4>
            </div>
            <div class="modal-body">


                <div class="form-group">
                    <label>Recipe Step Name</label>
                    <div class="form-group">
                        <input class="form-control" data-ng-model="newRecipeStepName"
                               placeholder="Please provide recipe step name. " required type="text"/>
                    </div>
                </div>

                <div class="form-group">
                    <label> Recipe Media </label>
                    <div class="row">
                        <div class="col-lg-8">
                            <input class="btn btn-default"
                                   file-model="getRecipeMediaToUpload"
                                   style="width: 100%;"
                                   type="file">
                        </div>

                        <div class="col-lg-4">
                            <button class="btn btn-primary "
                                    ng-click="uploadRecipeMedia()">Upload
                            </button>
                        </div>
                        <div class="col-lg-10">

                            Instructions:<br>

                            <div style="font-size: 12px; color: #303030;">
                                Image resolution: 1:1(max 200px:200px) and max size: 200KB<br>

                                Video resolution: 1:1(max 200px:200px) and max: 5MB <br>

                                Image must be in jpg/jpeg and video in mp4
                            </div>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <div class="form-group clearfix">
                        <button class="btn btn-primary pull-left"
                                ng-click="resetRecipeStepMediaDetail()">
                            Reset
                        </button>
                        <button class="btn btn-primary pull-right"
                                ng-click="editRecipeMediaDetailVal()">
                            Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- -- Clone Recipe Media Modal -->
<div class="modal fade" id="cloneRecipeModal" role="dialog" aria-labelledby="cloneRecipeModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="cloneRecipeModalLabel">Select Clone Product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div data-ng-if="!cloneRecipeProfile">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Product</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2 class="form-control" style="width: 100% !important"
                                            data-ng-model="selectedCloneProductId"
                                            data-ng-change="onChangeSelectedCloneProduct(selectedCloneProductId);"
                                            data-placeholder="Search Product...">
                                        <option value=""></option>
                                        <option data-ng-repeat="product in productsInfo" value="{{product.id}}">
                                            {{product.name}} - {{product.status}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2 style="width: 100% !important" class="form-control"
                                            data-ng-model="selectedCloneProductDimensionId"
                                            data-ng-change="selectCloneProductDimension(selectedCloneProductDimensionId)"
                                            data-placeholder="Select Dimension">
                                        <option data-ng-repeat="dimension in selectedCloneProductDimensionProfile.content"
                                                value="{{dimension.id}}">
                                            {{dimension.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-ng-if="cloneRecipeProfile">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Product</label>
                                </div>
                                <div class="col-xs-8">
                                    {{cloneRecipes[0].product.name}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    {{cloneRecipes[0].dimension.name}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label> Current Profile</label>
                                </div>
                                <div class="col-xs-8">
                                    {{cloneRecipes[0].profile}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Clone from Profile</label>
                                </div>
                                <div class="col-xs-8">
                                    <select class="form-control" data-ng-model="selectedRecipeDetail"
                                            data-ng-change="selectCloneRecipeDetail(selectedRecipeDetail)">
                                        <option data-ng-repeat="recipe in recipesAvailableForClone"
                                                value="{{recipe}}">
                                            {{recipe.profile + " - " + recipe.status}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12" data-ng-if="cloneRecipes != null && cloneRecipes.length > 0">
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Recipe Id</th>
                                        <th>Recipe Name</th>
                                        <th>Profile</th>
                                        <th>Actions</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="recipe in cloneRecipes  | filter: filterFunction track by recipe.recipeId">
                                        <td>{{recipe.recipeId}}</td>
                                        <td>{{recipe.name}}</td>
                                        <td>{{recipe.profile}}</td>
                                        <td align="left">
                                            <span data-ng-click="selectRecipeForClone(recipe)" style="cursor: pointer"
                                                  title="Clone">
                                                <i class="fa fa-copy" style="font-size: 24px; margin-right: 5px"></i>
                                            </span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
