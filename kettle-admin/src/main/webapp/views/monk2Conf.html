<style>
    body {
        background-color: #f7f9fc;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-top: 40px;
    }

    h2 {
        color: #2c3e50;
        border-bottom: 2px solid #4CAF50;
        padding-bottom: 10px;
    }

    .form-group label {
        font-weight: 600;
        color: #34495e;
    }

    .form-control {
        border: 1px solid #ced4da;
        border-radius: 8px;
    }

    .btn-primary {
        background-color: #4CAF50;
        border-color: #4CAF50;
    }

    .btn-primary:hover {
        background-color: #45a049;
        border-color: #45a049;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-warning {
        background-color: #f39c12;
        border-color: #f39c12;
        color: white;
    }

    .btn-warning:hover {
        background-color: #e67e22;
        border-color: #e67e22;
    }

    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: white;
    }

    .text-danger small {
        font-size: 0.85rem;
    }

    dl dt {
        color: #2c3e50;
        font-weight: bold;
    }

    dl dd {
        color: #555;
    }

    ul.list-unstyled li {
        padding: 4px 0;
        color: #2c3e50;
    }
</style>

<div ng-controller="monk2ConfCtrl" ng-init="init()" class="container">
    <h2 class="mb-4">Monk2 Configuration</h2>

    <form name="configForm" novalidate ng-show="!viewMode">
        <!-- No. of X-Twos -->
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">No. of X-Twos</label>
            <div class="col-sm-8">
                <input type="number" class="form-control"
                       ng-model="configData.noOfXTwos"
                       name="noOfXTwos" min="0" required>
                <div ng-show="configForm.noOfXTwos.$dirty && configForm.noOfXTwos.$invalid" class="text-danger">
                    <small ng-show="configForm.noOfXTwos.$error.required">Required</small>
                    <small ng-show="configForm.noOfXTwos.$error.min">Must be 0 or more</small>
                </div>
            </div>
        </div>

        <!-- Vessel Sense Delay -->
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Vessel Sense Delay</label>
            <div class="col-sm-8">
                <input type="number" class="form-control"
                       ng-model="configData.vesselSenseDelay"
                       name="vesselSenseDelay" required>
                <div ng-show="configForm.vesselSenseDelay.$dirty && configForm.vesselSenseDelay.$invalid" class="text-danger">
                    <small ng-show="configForm.vesselSenseDelay.$error.required">Required</small>
                </div>
            </div>
        </div>

        <!-- Spice Sense Delay -->
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Spice Sense Delay</label>
            <div class="col-sm-8">
                <input type="number" class="form-control"
                       ng-model="configData.spiceSenseDelay"
                       name="spiceSenseDelay" required>
                <div ng-show="configForm.spiceSenseDelay.$dirty && configForm.spiceSenseDelay.$invalid" class="text-danger">
                    <small ng-show="configForm.spiceSenseDelay.$error.required">Required</small>
                </div>
            </div>
        </div>

        <!-- Monk URLs -->
        <div class="form-group row" ng-repeat="urlObj in configData.monkUrls track by $index">
            <label class="col-sm-4 col-form-label">Monk URL {{$index + 1}}</label>
            <div class="col-sm-8">
                <input type="text" class="form-control"
                       ng-model="urlObj.url"
                       name="monkUrl{{$index}}" required>
                <div ng-show="configForm['monkUrl' + $index].$dirty && configForm['monkUrl' + $index].$invalid" class="text-danger">
                    <small ng-show="configForm['monkUrl' + $index].$error.required">Required</small>
                </div>
            </div>
        </div>


        <!-- Action Buttons -->
        <div class="form-group row mt-4">
            <div class="col-sm-12 text-right">
                <button type="button" class="btn btn-primary" ng-click="saveConfiguration()" ng-disabled="configForm.$invalid">
                    Save Configuration
                </button>
                <button type="button" class="btn btn-secondary ml-2" ng-click="toggleViewMode()">Cancel</button>
            </div>
        </div>
    </form>

    <!-- View Mode -->
    <div ng-show="viewMode">
        <dl class="row">
            <dt class="col-sm-4">No. of X-Twos</dt>
            <dd class="col-sm-8">{{viewConfigData.noOfXTwos}}</dd>

            <dt class="col-sm-4">Vessel Sense Delay</dt>
            <dd class="col-sm-8">{{viewConfigData.vesselSenseDelay}}</dd>

            <dt class="col-sm-4">Spice Sense Delay</dt>
            <dd class="col-sm-8">{{viewConfigData.spiceSenseDelay}}</dd>

            <dt class="col-sm-4">Monk URLs</dt>
            <dd class="col-sm-8">
                <ul class="mb-0" style="list-style-type: disc; padding-left: 1.5rem;">
                    <li ng-repeat="urlObj in viewConfigData.monkUrls track by $index">
                        {{urlObj.url}}
                    </li>
                </ul>
            </dd>

        </dl>

        <!-- Edit Button -->
        <div class="text-right mt-3">
            <button type="button" class="btn btn-warning" ng-click="toggleViewMode()">Edit Configuration</button>
        </div>
    </div>

    <!-- Back Button -->
    <div class="text-left mt-4">
        <button class="btn btn-outline-secondary" ng-click="goBack()">Back to Dashboard</button>
    </div>
</div>
