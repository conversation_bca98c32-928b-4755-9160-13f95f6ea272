<style xmlns="http://www.w3.org/1999/html">
    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }
</style>

<div ng-init="init()">
    <div class="row" style="margin-bottom: 20px;">
        <div class="col-xs-12">
            <p class="title">Bulk Update Unit Product Pricing</p>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Unit Type</label>
                </div>
                <div class="col-xs-6 form-group">
                    <select class="form-control" data-ng-model="prodDetails.unitCategory">
                        <option data-ng-repeat="unit in unitType | orderBy" value="{{unit}}">
                            {{unit}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Brand</label>
                </div>
                <div class="col-xs-6 form-group">
                    <select class="form-control" data-ng-model="prodDetails.brandId">
                        <option data-ng-repeat="brand in brandList | orderBy" value="{{brand.brandId}}">
                            {{brand.brandName}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Pricing Profiles</label>
                </div>
                <div class="col-xs-6 form-group">
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                         options="pricingProfileNames" selected-model="selectedPricingProfiles"
                         class="col-xs-6 region-card">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Regions</label>
                </div>
                <div class="col-xs-6 form-group">
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                         options="regionList" selected-model="selectedRegions" class="col-xs-6">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Select Product</label>
                </div>
                <div class="col-xs-8 form-group">
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForProducts"
                         options="productsInfo" selected-model="selectedProducts" class="col-xs-6">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-6 form-group">
                    <button class="btn btn-primary" data-toggle="modal" ng-click="downloadBulkProductPriceSheet()"
                            data-ng-disabled="!checkProdPriceObj()">
                        <i class="fa fa-download fw"></i> Download Unit Product Pricing Sheet
                    </button>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-2">
                    <label>Upload Bulk Product Price Sheet</label>
                </div>
                <div class="col-xs-4 row">
                    <input class="btn btn-default" style="width: 100%;" type="file"
                           file-model="productPriceFile" accept="">
                    <button class="btn btn-primary" style="margin-top: 5px"
                            data-ng-click="uploadBulkProductPriceSheet()"
                            data-ng-disabled="isUndefinedOrNull(productPriceFile)">Upload and update prices
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>