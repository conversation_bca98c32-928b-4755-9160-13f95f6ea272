<style type="text/css">
    th, td {
        text-align: center;
    }
</style>

<div
        class="row"
        ng-init="init()">

    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Search Campaigns
        </h1>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <input type="text" ng-model="searchDescInput" ng-change="filter()"
                   placeholder="Search by Name or Description" class="form-control"/>
        </div>
        <div class="col-lg-1">
            <button class="btn btn-primary" data-ng-disabled="disableSearchButton()"
                    ng-click="fetchCampaignsByDesc()">
                Search
            </button>
        </div>
        <div class="col-lg-4" style="margin-top: 7px">
            <input title="Fetch all" type="checkbox" ng-model="fetchAllTag" class="btn btn-primary pull-left"
                   style="transform: scale(1.5); margin-right: 10px; ">
            Fetch All
            </input>
        </div>
        <div class="col-lg-1">
            <button class="btn btn-primary pull-right"
                    ng-click="clearCampaignCache()">
                Launch Campaigns
            </button>
        </div>
    </div>
    <br>
    <br>
    <div class="row">
        <div class="col-xs-12" ng-if="campaignDetailsList.length > 0">
            <div class="row">
                <div class="col-xs-12">
                    <table id="campaignDetailsTable" class="table table-striped table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th>ID</th>
                        <th>Source<br>Medium</th>
                        <th>Name</th>
                        <th>Strategy</th>
                        <th>Start Date<br>(YYYY-MM-DD)</th>
                        <th>End Date<br>(YYYY-MM-DD)</th>
                        <th>Image1</th>
                        <th colspan="5">Actions</th>
                        </thead>

                        <tbody>
                        <tr ng-repeat="campaign in campaignDetailsList | filter : searchDescInput track by $index">
                            <td>{{campaign.campaignId}}</td>
                            <td>{{campaign.campaignSource}}<br>{{campaign.campaignMedium}}</td>
                            <td style="white-space: nowrap">{{campaign.campaignName}}</td>
                            <td>{{campaign.campaignStrategy}}</td>
                            <td style="white-space: nowrap">{{campaign.startDate}}</td>
                            <td style="white-space: nowrap">{{campaign.endDate}}</td>
                            <td style="min-width: 50px">
                                <img ng-show="campaign.image1"
                                     style="display: block; margin: 0 auto; max-width:50px; max-height: 50px" ;
                                     data-ng-src="{{campaign.image1}}"
                                     alt="Image not found"
                                     data-ng-click="openImagePreviewModal(campaign.image1)"/>
                            </td>
                            <td colspan="1">
                                <button ng-click="showPreview(campaign.campaignId)" title="preview"
                                        style="border: none; background: none; margin-top: 6px"><em class="fa fa-eye"
                                                                                                    style="font-size: 24px;"></em>
                                </button>
                            </td>
                            <td colspan="1">
                                <button ng-if="campaign.campaignStatus == 'ACTIVE'" ng-click="changeStatus(campaign.campaignId)"
                                        title="Active" style="border: none; background: none"><img
                                        src="img/activeCat.png"
                                        alt="Change Status"></button>
                                <button ng-if="campaign.campaignStatus != 'ACTIVE'" ng-click="changeStatus(campaign.campaignId)"
                                        title="Inactive" style="border: none; background: none"><img
                                        src="img/inactiveCat.png"
                                        alt="Change Status"></button>
                            </td>
                            <td colspan="1">
                                <button ng-click="cloneCampaign(campaign.campaignId)" title="clone"
                                        style="border: none; background: none; margin-top: 6px"><em class="fa fa-copy"
                                                                                                    style="font-size: 24px;"></em>
                                </button>
                            </td>
                            <td colspan="1">
                                <button ng-click="editCampaign(campaign.campaignId)" title="edit"
                                        style="border: none; background: none; margin-top: 6px"><em class="fa fa-edit"
                                                                                                    style="font-size: 24px;"></em>
                                </button>
                            </td>
                            <td colspan="1">
                                <button class="btn btn-primary"
                                        data-toggle="modal"
                                        data-ng-click="setSelectedCampaign(campaign.campaignId)"
                                        data-target="#addShortUrl">URL<br>Details
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="addShortUrl" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Short URL</h4>
            </div>

            <div class="modal-body">
                <div class="form-group">
                    <div class="form-group">
                        <label>Long URL</label>
                        <div class="form-group form-inline">
                            <input class="form-control" data-ng-model="selectedCampaignLongUrl"
                                   placeholder="Long URL" required type="text" style="width: 76%" disabled/>
                            <button ng-click="copyLongUrlToClipboard()" title="copy long url"
                                    style="border: none; background: none;"><em class="fa fa-copy"
                                                                                style="font-size: 20px; "></em>
                            </button>
                            <button ng-click="createShortUrl()"
                                    class="btn btn-warning" style="padding: 3px">Shorten Link
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Short URL</label>
                        <div class="form-group form-inline">
                            <input class="form-control" data-ng-model="selectedCampaignShortUrl"
                                   placeholder="Short URL" required type="text" style="width: 92%"/>
                            <button ng-click="copyShortUrlToClipboard()" title="copy short url"
                                    style="border: none; background: none;"><em class="fa fa-copy"
                                                                                style="font-size: 20px; "></em>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="form-group clearfix">
                    <button class="btn btn-primary pull-right"
                            ng-click="saveShortUrl()">
                        Save
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="imagePreviewModal" class="modal fade" role="dialog" aria-labelledby="imagePreviewModal" tabindex="-1"
     style="z-index: 9999;">
    <div role="document" style="margin: 30px auto; max-width: 400px; max-height: 400px">
        <div class="modal-content">
            <div class="frame" style="margin: auto;">
                <img style="display: block; margin: 0 auto; max-width:400px; max-height: 400px" ;
                     data-ng-src="{{previewImageSource}}" alt="Image not found"/>
            </div>
        </div>
    </div>
</div>

<div id="previewCampaignModal" class="modal fade previewModal" role="dialog" aria-labelledby="previewCampaignModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    {{previewCampaign.campaignName}}
                </h4>
            </div>
            <div class="modal-body">
                <div>
                    <button class="btn btn-primary center-block" data-dismiss="modal">Close</button>
                </div>
                <div>
                    <label>Primary URL: </label>
                    <label>{{previewCampaign.primaryUrl}}</label>
                </div>
                <div>
                    <table class="table table-striped table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th>Strategy</th>
                        <th>New Customer<br>Only</th>
                        <th>Linked Campaign<br>Id</th>
                        <th>Source</th>
                        <th>Medium</th>
                        <th>Category</th>
                        <th>Start Date<br>(YYYY-MM-DD)</th>
                        <th>End Date<br>(YYYY-MM-DD)</th>
                        <th>Is Applicable<br>for Order</th>
                        <th>Usage Limit</th>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                {{previewCampaign.campaignStrategy}}
                            </td>
                            <td>
                                {{previewNewCustomerOnly}}
                            </td>
                            <td>
                                {{previewCampaign.linkedCampaignId}}
                            </td>
                            <td>
                                {{previewCampaign.campaignSource}}
                            </td>
                            <td>
                                {{previewCampaign.campaignMedium}}
                            </td>
                            <td>
                                {{previewCampaign.campaignCategory}}
                            </td>
                            <td>
                                {{previewCampaign.startDate}}
                            </td>
                            <td>
                                {{previewCampaign.endDate}}
                            </td>
                            <td>
                                {{previewApplicableForOrder}}
                            </td>
                            <td>
                                {{previewCampaign.usageLimit}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="form-group">
                    <label>Brand: </label>
                    {{previewBrandName}}
                </div>
                <div class="form-group">
                    <label>Campaign Description: </label>
                    {{previewCampaign.campaignDesc}}
                </div>
                <div>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Is Coupon Clone?</th>
                        <th>Coupon Prefix</th>
                        <th>Coupon Applicable After<br>(in days)</th>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{previewCouponClone}}</td>
                            <td>{{previewCampaign.couponPrefix}}</td>
                            <td>{{previewCampaign.couponApplicableAfter}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div>
                    <table class="table table-bordered" style="background-color: #5cb85c">
                        <thead style="background-color: #e7e7e7">
                        <th>Customer Type</th>
                        <th>Journey</th>
                        <th>Validity<br>(in days)</th>
                        <th>Reminder<br>(in days)</th>
                        <th>Source Coupon code</th>
                        <th>Source Coupon description</th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="campaignMapping in campaignMappings track by $index"
                            ng-class="{'greenBackground': campaignMapping.isRequired == true}">
                            <td>{{campaignMapping.customerType}}</td>
                            <td>{{campaignMapping.journey}}</td>
                            <td>{{campaignMapping.validityInDays}}</td>
                            <td>{{campaignMapping.reminderDays}}</td>
                            <td>{{campaignMapping.code}}</td>
                            <td>{{campaignMapping.desc}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div>
                    <table class="table table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th width="20%">Campaign Reach</th>
                        <th width="20%">Regions</th>
                        <th width="30%">Cities</th>
                        <th width="30%">Units</th>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{previewCampaign.campaignReach}}</td>
                            <td>{{previewRegionNames}}</td>
                            <td>{{previewCityNames}}</td>
                            <td>{{previewUnitNames}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="form-group">
                    <label>Landing Page Description: </label>
                    {{previewCampaign.landingPageDesc}}
                </div>
                <div>
                    <table class="table table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th>Preview URL Heading</th>
                        <th>Preview URL Description</th>
                        <th>Preview URL Image</th>
                        <th>Redirection URL</th>
                        <th colspan="3">Campaign Images</th>
                        </thead>
                        <tbody>
                        <tr>
                            <td width="20%">{{previewCampaign.utmHeading}}</td>
                            <td width="25%">{{previewCampaign.utmDesc}}</td>
                            <td><img ng-show="previewCampaign.utmImageUrl"
                                     style="display: block; margin: 0 auto; max-width:50px; max-height: 50px" ;
                                     data-ng-src="{{previewCampaign.utmImageUrl}}"
                                     alt="Image not found"
                                     data-ng-click="openImagePreviewModal(previewCampaign.utmImageUrl)"/></td>
                            <td>{{previewCampaign.redirectionUrl}}</td>
                            <td colspan="1"><img ng-show="previewCampaign.image1"
                                                 style="display: block; margin: 0 auto; max-width:50px; max-height: 50px"
                                                 ;
                                                 data-ng-src="{{previewCampaign.image1}}"
                                                 alt="Image not found"
                                                 data-ng-click="openImagePreviewModal(previewCampaign.image1)"/></td>
                            <td colspan="1"><img ng-show="previewCampaign.image2"
                                                 style="display: block; margin: 0 auto; max-width:50px; max-height: 50px"
                                                 ;
                                                 data-ng-src="{{previewCampaign.image2}}"
                                                 alt="Image not found"
                                                 data-ng-click="openImagePreviewModal(previewCampaign.image2)"/></td>
                            <td colspan="1"><img ng-show="previewCampaign.image3"
                                                 style="display: block; margin: 0 auto; max-width:50px; max-height: 50px"
                                                 ;
                                                 data-ng-src="{{previewCampaign.image3}}"
                                                 alt="Image not found"
                                                 data-ng-click="openImagePreviewModal(previewCampaign.image3)"/></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>