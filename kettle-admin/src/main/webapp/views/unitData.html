<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div>
    <div class="row">
        <div class="col-xs-1" style="font-size: medium; margin-top: 4px">
            <label class="control-label">Regions</label>
        </div>
        <div class="col-xs-4 region-card" ng-dropdown-multiselect=""
             options="regionList"
             selected-model="selectedRegionList" extra-settings="multiSelectSettings">
        </div>
        <button data-ng-click="fetchUnits()" class="btn btn-primary">Get Units</button>
    </div>
    <br>
    <div class="row">
        <div class="col-xs-1" style="font-size: medium; margin-top: 4px">
            <label class="control-label">Filter</label>
        </div>
        <div class="col-xs-4">
            <input type="text" ng-model="unitFilter" ng-change="filter() "
                   placeholder="Filter" class="form-control"/>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-xs-12">
            <p class="title">{{unitCat}} List</p>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true" ng-init="init()">
                <div class="panel panel-default" ng-repeat="unit in unitlist | filter : unitFilter | orderBy : 'name'">
                    <div class="panel-heading" role="tab" id="headingOne" ng-click="getUnitData(unit.id)"
                         ng-class="{redBg:unit.status=='IN_ACTIVE', orangeBg:!unit.live && unit.status=='ACTIVE'}">
                        <h4 class="panel-title">
                            <button data-toggle="collapse" data-parent="#accordion" data-target="#{{unit.id}}"
                                    aria-expanded="false"
                                    aria-controls="collapseOne">
                                {{unit.name}}: {{unit.id}}
                                <i class="pull-right glyphicon"
                                   ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                            </button>
                        </h4>
                    </div>
                    <div id="{{unit.id}}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingOne">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-12 text-right">
                                    <a  class="btn btn-danger" acl-action="UCIB"
                                       data-toggle="modal"  data-ng-click="setUnitClosureId(unit)" ng-disabled="unit.closure !== null" > <p data-ng-if="unit.closure == null" > Initiate Unit Closure </p>
                                        <p data-ng-if="unit.closure !=null">(Closure Status : {{unit.closure}}) </p> </a>
                                    <a href="javascript:;" class="btn btn-primary" ng-if="unit.status=='ACTIVE'"
                                       ng-click="resetMeterReadingForUnit(unit.id)">Reset Meter Reading</a>
                                    <a href="javascript:;" class="btn btn-primary" ng-if="unit.status=='ACTIVE'"
                                       ng-click="deactivateUnit(unit.id)">Deactivate Unit</a>
                                    <a href="javascript:;" class="btn btn-primary" style="display: none"
                                       data-ng-if="unit.closed != true"
                                       ng-click="activateScmUnit(unit.id)">Activate SCM Unit</a>
                                    <a href="javascript:;" class="btn btn-primary"
                                       ng-if="unit.status=='IN_ACTIVE' && unit.closed != true"
                                       ng-click="activateUnit(unit.id)">Activate Unit</a>
                                    <a href="javascript:;" class="btn btn-primary" ng-if="unit.live"
                                       ng-click="changeUnitLiveStatus(unit.id,false,false,false)">Disable Unit</a>
                                    <a href="javascript:;" class="btn btn-primary" ng-if="!unit.live && unit.closed != true"
                                       ng-click="openDineInOrChaayosOptionStatus(unit,true)">Enable Unit</a>
<!--                                    <a href="javascript:;" class="btn btn-primary"-->
<!--                                        ng-click="editUnit(unit.unitDetail)">Edit Unit</a>-->
                                    <a href="javascript:;" class="btn btn-primary"
                                       ng-click="editUnitNew(unit.unitDetail)">Edit Unit</a>
                                    <br><br>
                                    <button class="btn btn-primary"
                                            ng-click="refreshCache(unit.id, false)">Refresh Cache
                                    </button>
                                    <button class="btn btn-primary"
                                            ng-click="refreshCache(unit.id, true)">Refresh Cache And Inventory
                                    </button>
                                    <!--ss-->
                                    <a href="javascript:;" class="btn btn-primary"
                                       ng-click="cloneCRMBanner(unit)">Clone CRM Banner</a>
                                    <a href="javascript:;" class="btn btn-primary" style="display: none"
                                       ng-click="activateScmUnit(unit.id)">Activate SCM Unit</a>
                                    <button class="btn btn-primary"
                                            data-ng-click="goToMonkConfiguration()">Add Monk Configuration
                                    </button>
                                    <br><br>
                                    <button class="btn btn-primary"
                                            data-ng-click="clearKettle2CacheWithSource('refreshUnitMenuSequence',unit.id)">Refresh Unit Menu Sequence
                                    </button>
                                    <button class="btn btn-warning"
                                            data-ng-click="restrictedApplication(unit)"> Applications
                                    </button>
                                    <br><br>
                                    <button class="btn btn-primary"
                                            data-ng-click="goToMonk2Configuration()">Add Monk 2.0 Configuration
                                    </button>
                                </div>
                            </div>
                            <p>
                                <strong>{{unitCat}}</strong>
                            </p>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">ID</th>
                                    <th scope="col">Name</th>
                                    <th scope="col">Reference Name</th>
                                    <th scope="col">Region</th>
                                    <th scope="col">Zone</th>
                                    <th scope="col">Family</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Email</th>
                                    <th scope="col">TIN</th>
                                    <th scope="col">Terminals</th>
                                    <th scope="col">TakeAway</th>
                                    <th scope="col">WorkStation</th>
                                    <th scope="col">Free Internet</th>
                                    <th scope="col">Table Service</th>
                                    <!--<th scope="col">Communication Channel</th>-->
                                    <th scope="col">Company</th>
                                </tr>
                                <tr>
                                    <td>{{unit.unitDetail.id}}</td>
                                    <td>{{unit.unitDetail.name}}</td>
                                    <td>{{unit.unitDetail.referenceName}}</td>
                                    <td>{{unit.unitDetail.region}}</td>
                                    <td>{{unit.unitDetail.unitZone}}</td>
                                    <td>{{unit.unitDetail.family}}</td>
                                    <td>{{unit.unitDetail.status}}</td>
                                    <td>{{unit.unitDetail.unitEmail}}</td>
                                    <td>{{unit.unitDetail.tin}}</td>
                                    <td>{{unit.unitDetail.noOfTerminals}}</td>
                                    <td>{{unit.unitDetail.noOfTakeawayTerminals}}</td>
                                    <td>{{unit.unitDetail.workstationEnabled}}</td>
                                    <td>{{unit.unitDetail.freeInternetAccess}}</td>
                                    <td>{{unit.unitDetail.tableService}}</td>
                                    <td>{{unit.unitDetail.tableServiceType}}</td>
                                    <!--<td>{{unit.unitDetail.channel}}</td>-->
                                    <td>{{unit.unitDetail.company.name}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Assembly Modes</strong>
                            </p>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">Name</th>
                                    <th scope="col">Status</th>
                                </tr>
                                <tr>
                                    <td>Strict Mode</td>

                                    <td>{{unit.unitDetail.assemblyStrictMode}}</td>
                                </tr>
                                <tr>
                                    <td>Cod Orders Otp Mode</td>
                                    <td>{{unit.unitDetail.assemblyOtpMode}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Division</strong>
                            </p>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">Name</th>
                                    <th scope="col">Description</th>
                                    <th scope="col">Category</th>
                                    <th scope="col">Sub Category</th>
                                </tr>
                                <tr>
                                    <td>{{unit.unitDetail.division.name}}</td>
                                    <td>{{unit.unitDetail.division.description}}</td>
                                    <td>{{unit.unitDetail.division.category}}</td>
                                    <td>{{unit.unitDetail.subCategory}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Address</strong>
                            </p>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">Line 1</th>
                                    <th scope="col">Line 2</th>
                                    <th scope="col">Line 3</th>
                                    <th scope="col">City</th>
                                    <th scope="col">State</th>
                                    <th scope="col">Country</th>
                                    <th scope="col">Zip code</th>
                                    <th scope="col">Contact 1</th>
                                    <th scope="col">Contact 2</th>
                                    <th scope="col">Latitude</th>
                                    <th scope="col">Longitude</th>
                                    <th scope="col">Address Type</th>
                                </tr>
                                <tr>
                                    <td>{{unit.unitDetail.address.line1}}</td>
                                    <td>{{unit.unitDetail.address.line2}}</td>
                                    <td>{{unit.unitDetail.address.line3}}</td>
                                    <td>{{unit.unitDetail.address.city}}</td>
                                    <td>{{unit.unitDetail.address.state}}</td>
                                    <td>{{unit.unitDetail.address.country}}</td>
                                    <td>{{unit.unitDetail.address.zipCode}}</td>
                                    <td>{{unit.unitDetail.address.contact1}}</td>
                                    <td>{{unit.unitDetail.address.contact2}}</td>
                                    <td>{{unit.unitDetail.address.latitude}}</td>
                                    <td>{{unit.unitDetail.address.longitude}}</td>
                                    <td>{{unit.unitDetail.address.addressType}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Delivery Partners</strong>
                            </p>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">Name</th>
                                    <th scope="col">Priority</th>
                                </tr>
                                <tr ng-repeat="dt in unitDeliveryData">
                                    <td>{{dt.detail.name}}</td>
                                    <td>{{dt.priority}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Location</strong>
                            </p>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">Country</th>
                                    <th scope="col">State</th>
                                    <th scope="col">City</th>
                                    <th scope="col">Code</th>
                                    <th scope="col">Name</th>
                                </tr>
                                <tr>
                                    <td>{{unit.unitDetail.location.state.country.name}}</td>
                                    <td>{{unit.unitDetail.location.state.name}}</td>
                                    <td>{{unit.unitDetail.location.locations.name}}</td>
                                    <td>{{unit.unitDetail.location.state.code}}</td>
                                    <td>{{unit.unitDetail.location.name}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Business Hours </strong>
                            </p>
                            <table class="table table-striped table-bordered" style="font-size: 11px">
                                <tr>
                                    <td>Shift</td>
                                    <td>Days</td>
                                    <td>Is Operational</td>
                                    <td ng-if="unit.unitDetail.family=='CAFE' || unit.unitDetail.family=='EMPLOYEE_MEAL'">Dine In</td>
                                    <td ng-if="unit.unitDetail.family=='CAFE' || unit.unitDetail.family=='EMPLOYEE_MEAL' || unit.unitDetail.family=='DELIVERY'">
                                        Cod
                                    </td>
                                    <td ng-if="unit.unitDetail.family=='CAFE' || unit.unitDetail.family=='EMPLOYEE_MEAL' || unit.unitDetail.family=='COD'">Take
                                        Away
                                    </td>
                                    <td>Hand Over</td>
                                </tr>
                                <!-- <tr><td>{{completeUnitObj.operationalHours}}</td></tr> -->
                                <tr ng-repeat="operationHour in unit.unitDetail.operationalHours">
                                    <td>{{operationHour.noOfShifts}}</td>
                                    <td>{{operationHour.dayOfTheWeek}}</td>
                                    <td>{{operationHour.isOperational}}</td>
                                    <td ng-if="operationHour.isOperational && (unit.unitDetail.family=='CAFE' || unit.unitDetail.family=='EMPLOYEE_MEAL')">
                                        <b>Start Date </b> : {{operationHour.dineInOpeningTime}}
                                        <br/>
                                        <b>End Date :</b> {{operationHour.dineInClosingTime}}
                                    </td>
                                    <td ng-if="operationHour.isOperational && (unit.unitDetail.family=='CAFE' || unit.unitDetail.family=='EMPLOYEE_MEAL' || unit.unitDetail.family=='DELIVERY')">
                                        <b>Start Date :</b> {{operationHour.deliveryOpeningTime}}
                                        <br/>
                                        <b>End Date : </b> {{operationHour.deliveryClosingTime}}
                                    </td>
                                    <td ng-if="operationHour.isOperational && (unit.unitDetail.family=='CAFE' || unit.unitDetail.family=='EMPLOYEE_MEAL' || unit.unitDetail.family=='COD')">
                                        <b>Start Date :</b> {{operationHour.takeAwayOpeningTime}}
                                        <br/>
                                        <b>End Date : </b> {{operationHour.takeAwayClosingTime}}
                                    </td>
                                    <td>{{operationHour.shiftOneHandoverTime}}</td>
                                </tr>
                            </table>
                            <p>
                                <strong>Products</strong>
                            </p>
                            <a href="javascript:;" class="btn btn-primary"
                               data-ng-if="unit.unitDetail.products == undefined || unit.unitDetail.products == null || unit.unitDetail.products.length == 0"
                               data-ng-click="fetchProducts(unit.unitDetail)">Get Products</a> <br/>
                            <table class="table table-bordered table-striped" style="font-size: 11px">
                                <tr>
                                    <th scope="col">ID</th>
                                    <th scope="col">Name</th>
                                    <th scope="col">Profile Size</th>
                                    <th scope="col">Addons</th>
                                    <th scope="col">Attribute</th>
                                    <th scope="col">SKU Code</th>
                                    <th scope="col">Bill Type</th>
                                    <th scope="col">Addon Profile</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Prices</th>
                                    <th scope="col">Action</th>
                                </tr>
                                <!-- {{unit.unitDetail.products}} -->
                                <tr ng-repeat="product in unit.unitDetail.products">
                                    <td>{{product.id}}</td>
                                    <td>{{product.name}}</td>
                                    <td>{{product.hasSizeProfile}}</td>
                                    <td>{{product.hasAddons}}</td>
                                    <td>{{product.attribute}}</td>
                                    <td>{{product.skuCode}}</td>
                                    <td>{{product.billType}}</td>
                                    <td>{{product.addOnProfile}}</td>
                                    <td>{{product.status}}</td>
                                    <td><span ng-repeat="price in product.prices" ng-if="!$last">{{price.price}}-</span>
                                        <span
                                                ng-repeat="price in product.prices" ng-if="$last">{{price.price}}</span>
                                    </td>
                                    <td><img
                                            ng-click="editProductDetailModal(unit.unitDetail.id,product.id,product.name,product.prices)"
                                            style="margin-bottom: 8px; cursor: pointer" title="Edit Product Unit Price"
                                            ng-src="img/change.png"
                                            height="20px" width="20px">&nbsp;&nbsp; <img
                                            ng-click="inActiveProduct(unit.unitDetail.id,product.id)"
                                            ng-if="product.status=='ACTIVE'" style="margin-bottom: 8px; cursor: pointer"
                                            title="Activate Product"
                                            ng-src="img/activeProduct.png" height="25px" width="25px">&nbsp;&nbsp; <img
                                            ng-click="activeProduct(unit.unitDetail.id,product.id)"
                                            style="margin-bottom: 8px; cursor: pointer"
                                            title="InActivate Product" ng-if="product.status=='IN_ACTIVE'"
                                            ng-src="img/inactiveProduct.png" height="22px"
                                            width="22px"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal fade" id="productUnitDetailModal" tabindex="-1" role="dialog"
                     aria-labelledby="myModalLabel">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true" style="font-size: 40px;color:red;">&times;</span>
                                </button>
                                <h3 class="modal-title" id="myModalLabel" style="text-align: center;">
                                    {{productName}}</h3>
                            </div>
                            <div class="modal-body">
                                <div data-ng-if="filteredProducts > 0">
                                    <table class="table table-bordered table-striped">
                                        <!-- <tr>
                                            <th scope="col">Existing Price</th>
                                            <th scope="col"></th>
                                            <th scope="col">New Price</th>
                                        </tr> -->
                                        <tr>
                                            <th scope="col">Size</th>
                                            <th scope="col">Price (Rs.)</th>
                                            <th scope="col">New Price</th>
                                            <th scope="col">Existing Profile</th>
                                            <th scope="col">New Profile</th>
                                        </tr>
                                        <tr data-ng-repeat="productsPricesUnit in productPrices">
                                            <td>{{productsPricesUnit.dimension}}</td>
                                            <td>{{productsPricesUnit.price}}</td>
                                            <td>{{productsPriceUnits[productsPricesUnit.price]}}<input type="text"
                                                                                                       value="productsPriceUnits[productsPricesUnit.price]"
                                                                                                       class="form-control"
                                                                                                       ng-model="productsPricesUnit.newprice"
                                                                                                       name="productsPricesUnit.dimension"
                                                                                                       required/></td>
                                            <td>{{productsPricesUnit.profile}}</td>
                                            <td><select class="form-control"
                                                        data-ng-model="productsPricesUnit.newProfile">
                                                <!-- data-ng-change="selectCloneProductProfile(selectedProfileName)"> -->
                                                <option data-ng-repeat="profile in recipeProfiles[productsPricesUnit.dimension] | orderBy"
                                                        value="{{profile}}">{{profile}}
                                                </option>
                                            </select></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3">
                                                <button type="button" class="btn btn-primary " data-dismiss="modal"
                                                        aria-label="Close">
                                                    <span aria-hidden="true">cancel</span>
                                                </button>
                                            </td>
                                            <td colspan="3">
                                                <button class="btn btn-primary pull-right"
                                                        ng-click="updateProductPriceUnit(unitIDD,productIDD,productPrices,producttNamee)">
                                                    Update
                                                </button>
                                            </td>
                                        </tr>
                                    </table>
                                    <div class="form-group clearfix" ng-if="action=='Add'">
                                        <button class="btn btn-primary pull-right" ng-click="submitAddEmployee()">Add
                                            employee
                                        </button>
                                    </div>
                                    <div class="form-group clearfix" ng-if="action=='UpdateEmp'">
                                        <button class="btn btn-primary pull-right"
                                                ng-click="submitUpdateEmployee(updatedEmployee)">Update
                                            employee
                                        </button>
                                    </div>
                                </div>
                                <div ng-if="filteredProducts == 0">
                                    <h4>No results found</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div aria-labelledby="SelectCafeLabel" class="modal fade" id="chooseDineInOrChaayosModal" role="dialog"
     tabindex="-1">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabelForType"> Changes to be reflected on </h4>
            </div>
            <div class="modal-body">
                <span style='font-size: 12px; color:#1ca62c'>*Checked  means status is Active</span><br>
                <!--<span style='font-size: 12px; color:#ff0e0b'>**To disable uncheck it</span>-->
                <div class="row" >
                    <div class="col-xs-12">
                        <div class="form-group col-xs-12">
                            <input type="checkbox" ng-model="chaayos" ng-disabled="(unitCategory!='CAFE')" class=" checkbox-inline" style="height: 2em; width: 2em;"/>
                            <span style='font-weight: bold;font-size: 21px; color:#1ca62c' class=" checkbox-inline">Chaayos.com</span>
                        </div>
                        <div class="form-group col-xs-12">
                            <input type="checkbox" ng-model="dineIn" ng-disabled="(unitCategory!='CAFE')" class=" checkbox-inline" style="height: 2em; width: 2em;"/>
                            <span style='font-weight: bold;font-size: 21px; color:#1ca62c' class=" checkbox-inline" >DineIn App</span>
                        </div>
                        <div class="form-group" align="center">
                        <button class="btn btn-primary"
                                ng-click="selectedOptionForAppAndNeoCafe(dineIn,chaayos)">Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--ss-->
<div class="modal fade" id="cloneCRMBanner" role="dialog"
     tabindex="-1">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabelForCRMclone" style="font-weight: bold"> Clone CRM Banner </h4>
            </div>
            <div class="modal-body">
                <div class="row" >
                    <div class="col-xs-12">
                        <div>
                            <h4>Select Unit which you want to clone</h4>
                            <hr/>
                            <select
                                    class="form-control"
                                    data-ng-model="cloneCRMUnit"
                                    data-ng-change="setCloneCRM(cloneCRMUnit)"
                                    data-ng-placeholder="select unit which you want to clone"
                                    data-ng-options="unit as unit.name for unit in unitlist track by unit.id">
                            </select>
                            <hr/>
                        </div>

                        <div class="form-group" align="center">
                            <button class="btn btn-primary"
                                    data-ng-click="cloneApi()">Clone</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<div class="modal fade" id="unitClosure" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="exampleModalLongTitle">Unit Closure Initiation</h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label for="recipient-name" class="col-form-label">Operation Stop Date:</label>
                        <input type="date" class="form-control" id="recipient-name" data-ng-model="unitClosure.operationStopDate">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Message:</label>
                        <textarea class="form-control" id="message-text" data-ng-model="unitClosure.message"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" data-ng-click="initiateCafeClosure()" data-dismiss="modal">Save changes</button>
            </div>
        </div>
    </div>
</div>
