<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Add Location </h1>
    </div>
    <div class="form-group">
        <label>City Name*</label>
        <input type="text"
               pattern=".*\S+.*"
               class="form-control"
               ng-model="cityName"
        />
    </div>
    <div class="form-group">
        <label>City Code*</label>
        <input type="text"
               pattern=".*\S+.*"
               class="form-control"
               ng-model="cityName"
               disabled
        />
    </div>
    <div class="form-group">
        <label>State *</label>
        <select class="form-control" ng-model="selectedState">
            <option ng-repeat="state in states" value="{{state}}">
                {{state.name}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <label>Country Code *</label>
        <select class="form-control" ng-model="countryCode">
            <option ng-repeat="country in countryCodes | orderBy" value="{{country}}">
                {{country.name}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <label>Status *</label>
        <select class="form-control" ng-model="locationStatus">
            <option ng-repeat="status in statusList | orderBy" value="{{status}}">
                {{status}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <label>Functional *</label>
        <select class="form-control" ng-model="isFunctional">
            <option ng-repeat="functional in functionalList | orderBy" value="{{functional}}">
                {{functional}}
            </option>
        </select>
    </div>
    <div class="form-group">
        <button style="margin: 10px" class="btn btn-primary pull-right"
                ng-click="saveLocation()">Add Location
        </button>
    </div>
</div>
