<!DOCTYPE html>
<html>
<head>
  <title>Profile Management</title>

<!-- Bootstrap JS -->

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f9f9f9;
    }

    .container {
      max-width: 900px;
      margin: auto;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }

    .header {
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
    }

    .form-group label {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .form-group input,
    .form-group select {
      padding: 10px;
      font-size: 14px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .table th,
    .table td {
      border: 1px solid #ddd;
      padding: 10px;
      text-align: left;
    }

    .table th {
      background-color: #4caf50;
      color: white;
    }

    .actions button {
      margin: 0 5px;
      padding: 5px 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .btn-primary {
      background-color: #4caf50;
      color: #fff;
    }

    .btn-secondary {
      background-color: #ccc;
      color: #333;
    }

    .btn-danger {
      background-color: #f44336;
      color: white;
    }

    .modal-body,.modal-header,.modal-footer{
        background-color: white;
    }
    .modal{
        background-color: white;
        margin: 70px;
    }

    .close {
      float: right;
      font-size: 20px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
    }
 

.close:hover,
.close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

  </style>
</head>
<body ng-controller="ProfileController"  data-ng-init="init()">

  <div class="container" data-ng-init="init()">
    <!-- Header -->
    <div class="header">Profile Management</div>

    <!-- Toggle Buttons -->
    <div>
      <button
        class="btn btn-primary"
        ng-click="switchView('create')">
        Create Profile
      </button>
      <button
        class="btn btn-primary"
        ng-click="switchView('cloneVersion')">
        Clone Version
      </button>
      <button
        class="btn btn-primary"
        ng-click="switchView('search')">
        Search Profiles
      </button>
      <button
      class="btn btn-primary"
      ng-click="switchView('searchVersion')">
      Search Version
    </button>
    </div>

    <!-- Create Profile Section -->
    <div ng-show="activeView === 'create'">
      <div class="form-group">
        <label for="profileName">Profile Name</label>
        <input id="profileName" type="text" ng-model="newProfile.priceProfileName" placeholder="Enter Profile Name" />
      </div>
<!-- 1 -->

      <div class="form-group">
        <label for="cloneProfile">Clone From Existing Profile (Optional)</label>
      
  
      
        <!-- Dropdown -->
        <select 
        data-ui-select2 
          id="cloneProfile" 
          ng-model="newProfile.cloneFrom" 
          class="form-control"
          data-ng-change="selectProfileForClone(newProfile.cloneFrom)">
          <option value="">-- No Clone --</option>
          <option 
            ng-repeat="profile in profiles" 
            value="{{profile}}">
            {{profile.priceProfileName}}
          </option>
        </select>
      </div>
      
      <div class="form-group" ng-show="newProfile.cloneFrom && cloneVersions.length > 0">
        <label for="cloneProfileVersion">Select Version</label>
        <select
           data-ui-select2
          id="cloneProfileVersion"
          class="form-control"
          ng-model="newProfile.cloneVersion"
          ng-options="version.versionNo as version.versionNo for version in cloneVersions">
          <option value="">-- Select Version --</option>
        </select>
      </div>
      


      <button class="btn btn-primary" ng-click="createProfile(newProfile)">Create Profile</button>
    </div>

    <div ng-show="activeView === 'cloneVersion'">
      <div class="form-group">
        <div class="form-group">
          <label for="cloneProfileVersion">Select Version</label>
          <select
             data-ui-select2
            id="bulkCloneVersion"
            class="form-control"
            ng-model="bulkCloneVersion"
            ng-options="version as version for version in allVersionNumbers">
            <option value="">-- Select Version --</option>
          </select>
        </div>
      </div>
      


      <button class="btn btn-primary" ng-click="createNewVersions(bulkCloneVersion)">Clone Version</button>
    </div>

    <!-- Search Profiles Section -->
    <div ng-show="activeView === 'search'">
      <!-- <div class="form-group">
        <label for="searchPartner">Partner</label>
        <select id="searchPartner" ng-model="search.partner">
          <option value="">-- Select Partner --</option>
          <option ng-repeat="partner in partners" value="{{partner}}">{{partner}}</option>
        </select>
      </div>

      <div class="form-group">
        <label for="searchBrand">Brand</label>
        <select id="searchBrand" ng-model="search.brand">
          <option value="">-- Select Brand --</option>
          <option ng-repeat="brand in brands" value="{{brand}}">{{brand}}</option>
        </select>
      </div> -->

      <div class="form-group">
        <label for="searchStatus">Status</label>
        <select id="searchStatus" ng-model="search.status">
          <option value="">-- Select Status --</option>
          <option value="ACTIVE">Active</option>
          <option value="IN_ACTIVE">Inactive</option>
        </select>
      </div>

      <button class="btn btn-primary" ng-click="searchProfiles()">Search</button>

      <!-- Search Results -->
      <div>
        <h3>Profiles</h3>
        <div>
          <label for="searchFilter">Search Profiles:</label>
          <input
            type="text"
            id="searchFilter"
            class="form-control"
            placeholder="Enter profile name"
            ng-model="search.priceProfileName"
          />
        </div>
        <table class="table">
          <thead>
            <tr>
              <th>Profile Name</th>
              <th>Created By</th>
              <th>Creation Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="profile in filteredProfiles | filter:search">
              <td>{{profile.priceProfileName}}</td>
              <td>{{profile.createdBy}}</td>
              <td>{{profile.creationTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
              <td>{{profile.status}}</td>
              <td>
                <button class="btn btn-primary" ng-click="openVersionModal(profile)">View Versions</button>
                <button class="btn btn-danger" ng-click="toggleProfileStatus(profile)">
                  {{profile.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div ng-show="activeView === 'searchVersion'">
      <div>
        <h3>Versions</h3>
        <!-- <div>
          <label for="searchFilter">Search Version:</label>
          <input
            type="text"
            id="searchFilter"
            class="form-control"
            placeholder="Enter Version"
            ng-model="search.version"
          />
        </div> -->
        <table class="table">
          <thead>
            <tr>
              <th>Version No</th>
              <th> Profiles </th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="version in clubbedVersions | filter:search">
              <td>{{version.versionNo}}</td>
              <td>{{version.profiles.length}}</td>
              <td>{{version.status}}</td>
              <td>
                <button class="btn btn-primary" ng-click="openProfileModal(version)">View Profiles</button>
                <button class="btn btn-danger" ng-click="toggleBulkVersionStatus(version.versionNo,version.status)">
                  {{version.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>
  </div>

  <div class="modal" id="versionModal"  role="dialog">
    <div class="modal-content">
      <span class="close" ng-click="closeVersionModal()">&times;</span>
      <div class="modal-header">
        Profile Versions: {{selectedProfile.name}}
      </div>
      <table class="table">
        <thead>
          <tr>
            <th>Version</th>
            <th>Created By</th>
            <th>Creation Date</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="version in selectedProfile.priceProfileVersions">
            <td>{{version.versionNo}}</td>
            <td>{{version.createdBy}}</td>
            <td>{{version.creationTime  | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            <td>{{version.status}}</td>
            <td>
              <button class="btn btn-danger" ng-click="toggleVersionStatus(version)">
                {{version.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}}
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary" ng-click="showCloneOptions = !showCloneOptions">Create New Version</button>
        </div>
        <!-- Section for cloning options -->
      <div ng-show="showCloneOptions" style="margin-top: 20px;">
        <div class="form-group">
          <label for="cloneProfile">Clone From Existing Profile</label>
          <select
            id="cloneProfile"
            ng-model="cloneOptions.profile"
            ng-options="profile.priceProfileName for profile in profiles">
            <option value="">-- Select Profile --</option>
          </select>
        </div>
  
        <div class="form-group" ng-show="cloneOptions.profile">
          <label for="cloneVersion">Select Version</label>
          <select
            id="cloneVersion"
            ng-model="cloneOptions.version"
            ng-options="version.versionNo for version in cloneOptions.profile.priceProfileVersions">
            <option value="">-- Select Version --</option>
          </select>
        </div>
  
        <button
          class="btn btn-primary"
          ng-disabled="!cloneOptions.version"
          ng-click="createNewVersion(cloneOptions.profile, cloneOptions.version)">
          Confirm and Create Version
        </button>
      </div>
      </div>

      <div class="modal" id="profileModal"  role="dialog">
        <div class="modal-content">
          <span class="close" ng-click="closeProfileModal()">&times;</span>
          <div class="modal-header">
            Version No: {{selectedVersion.versionNo}}
          </div>
          <table class="table">
            <thead>
              <tr>
                <th>Profile</th>
                <th>Created By</th>
                <th>Creation Date</th>
                <th>Status</th>
                <!-- <th>Actions</th> -->
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="profile in selectedVersion.profiles">
                <td>{{profile.priceProfileName}}</td>
                <td>{{profile.createdBy}}</td>
                <td>{{profile.creationTime  | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{profile.status}}</td>
                <!-- <td>
                  <button class="btn btn-danger" ng-click="toggleVersionStatus(version)">
                    {{version.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}}
                  </button>
                </td> -->
              </tr>
            </tbody>
          </table>
        </div>
            <!-- <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" ng-click="showCloneOptions = !showCloneOptions">Create New Version</button>
            </div>
            
          <div ng-show="showCloneOptions" style="margin-top: 20px;">
            <div class="form-group">
              <label for="cloneProfile">Clone From Existing Profile</label>
              <select
                id="cloneProfile"
                ng-model="cloneOptions.profile"
                ng-options="profile.priceProfileName for profile in profiles">
                <option value="">-- Select Profile --</option>
              </select>
            </div>
      
            <div class="form-group" ng-show="cloneOptions.profile">
              <label for="cloneVersion">Select Version</label>
              <select
                id="cloneVersion"
                ng-model="cloneOptions.version"
                ng-options="version.versionNo for version in cloneOptions.profile.priceProfileVersions">
                <option value="">-- Select Version --</option>
              </select>
            </div>
      
            <button
              class="btn btn-primary"
              ng-disabled="!cloneOptions.version"
              ng-click="createNewVersion(cloneOptions.profile, cloneOptions.version)">
              Confirm and Create Version
            </button>
          </div> -->
          </div>
  </div>
  
  
</body>
</html>
 