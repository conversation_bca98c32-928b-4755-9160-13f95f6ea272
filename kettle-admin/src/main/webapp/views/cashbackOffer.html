<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Cash Back Offer </h1>
    </div>
    <div class="form-group">
        <button style="margin: 10px; display:block" class="btn btn-primary pull-right"
                ng-click="refreshCache()">Refresh Cache
        </button>
    </div>
    <div class="form-group">
        <button style="margin: 10px; display:block" class="btn btn-primary pull-right"
                ng-click="addNewOffer()">+ Add New offer
        </button>
    </div>
    <label>Region</label>
    <div style="width:300px" ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
         options="regionList" selected-model="selectedRegionList" class="region-card"
         events="{onSelectionChanged: updateUnitList}">
    </div>
    <label>Unit name</label>
    <div style="width:300px" ng-dropdown-multiselect="" extra-settings="multiSelectSettingsUnits"
         options="unitList" selected-model="selectedUnitList" class="region-card"
         data-ng-click="applyUnitFilter()">
    </div>
    <div class="form-group">
        <button style="margin: 10px, display:'block'" class="btn btn-primary"
                ng-click="loadOffers()">Load Offers
        </button>
    </div>

    <table style="width:90%; border:1px black solid">
        <thead>
            <tr style="height:50px; background-color:grey; color:white; text-align:center">
                <th>Unit Id</th>
                <th>Unit Name</th>
                <th>Lag Days</th>
                <th>Validity in Days</th>
                <th>Offer Dates</th>
                <th>Percentage %</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
        <tr style="height:50px; border:1px black solid" data-ng-repeat="offer in offerList | filter : searchDescInput track by $index">
            <td >{{offer.unitId}}</td>
            <td>{{unitMap[offer.unitId].name}}</td>
            <td>{{offer.lagDays}}</td>
            <td>{{offer.validityInDays}}</td>
            <td>{{offer.offerStartDate}} - {{offer.offerEndDate}}</td>
            <td>{{offer.cashbackPercentage}} %</td>
            <td colspan="1">
                <button ng-if="offer.offerStatus == 'ACTIVE'" ng-click="changeStatus($index)"
                        title="Active" style="border: none; background: none"><img
                        src="img/activeCat.png"
                        alt="Change Status"></button>
                <button ng-if="offer.offerStatus != 'ACTIVE'" ng-click="changeStatus($index)"
                        title="Inactive" style="border: none; background: none"><img
                        src="img/inactiveCat.png"
                        alt="Change Status"></button>
            </td>
            <td colspan="1">
                <button ng-click="editOffer($index)" title="edit"
                        style="border: none; background: none; margin-top: 6px"><em class="fa fa-edit"
                                                                                    style="font-size: 24px;"></em>
                </button>
            </td>
        <tr/>
        </tbody>
    </table>

    <div  class="modal fade" id="updationModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div style="height:700px; padding:20px" class="modal-content">
                <h3>Adding new Offer</h3>
                <div data-ng-if="actionType==='ADD'">
                    <label>Region</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                         options="regionList" selected-model="selectedRegionListForOfferAddition" class="region-card"
                         events="{onSelectionChanged: updateUnitListForOfferAddition}">
                    </div>
                    <label>Unit name and Id</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsUnits"
                         options="unitListForOfferAddition" selected-model="selectedUnitListForOfferAddition" class="region-card">
                    </div>
                </div>
                <h5 style="margin-top:10px">Offer Start Date</h5>
                <div class="datepicker" data-date-format="yyyy-MM-dd" >
                    <input type="text"
                           class="form-control"
                           ng-model="offerForUpdation.offerStartDate"
                           placeholder="yyyy-MM-dd"
                    />
                </div>
                <h5 style="margin-top:10px">Offer End Date</h5>
                <div class="datepicker" data-date-format="yyyy-MM-dd" >
                    <input type="text"
                           class="form-control"
                           ng-model="offerForUpdation.offerEndDate"
                           placeholder="yyyy-MM-dd"
                    />
                </div>
                <h5 style="margin-top:10px">Lag Days</h5>
                <input type="number"
                       class="form-control"
                       ng-model="offerForUpdation.lagDays"
                />
                <h5 style="margin-top:10px">Validity in Days</h5>
                <input type="number"
                       class="form-control"
                       ng-model="offerForUpdation.validityInDays"
                />
                <h5 style="margin-top:10px">Cashback Percentage</h5>
                <input type="number"
                       class="form-control"
                       ng-model="offerForUpdation.cashbackPercentage"
                />
                <h5 style="margin-top:10px">Max number of Order</h5>
                <input type="number"
                       class="form-control"
                       ng-model="offerForUpdation.maxNumberOfOrder"
                />
                <h5 style="margin-top:10px">Offer Status</h5>
                <select class="form-control"
                        ng-model="offerForUpdation.offerStatus"
                        ng-options="status for status in statusList"
                />
                <div class="form-group" data-ng-if="actionType==='EDIT'">
                    <button style="margin: 10px; display:block" class="btn btn-primary pull-right"
                            ng-click="updateOffer(offerForUpdation.index)">Update offer
                    </button>
                </div>
                <div class="form-group" data-ng-if="actionType==='ADD'">
                    <button style="margin: 10px; display:block" class="btn btn-primary pull-right"
                            ng-click="addOffer(offerForUpdation.index)">Add offer
                    </button>
                </div>
                <!-- End form -->
            </div>
        </div>
    </div>
</div>
