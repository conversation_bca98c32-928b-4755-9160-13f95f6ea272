<style xmlns="http://www.w3.org/1999/html">
    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }

    * {
        box-sizing: border-box;
    }

    .container {
        position: relative;
        display: flex;
        flexDirection: column;
        height: 85vh;
        width: 100%;
        margin-right: 16px;
        padding: 4px;
    }

    .text-container {
        flex: 1;
        overflow: auto;
        height: 100%;
        width: 100%;
    }

    .fab {
        position: absolute;
        right: 20px;
        bottom: 20px;
    }

    .switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 4px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(24px);
}

</style>

    <div data-ng-init="init()">
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-xs-12">
                <p class="title">Unit Price Profile Mapping</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Unit Type</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select class="form-control" data-ng-model="prodDetails.unitType">
                            <option data-ng-repeat="unit in unitType | orderBy" value="{{unit}}">
                                {{unit}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Brand</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select class="form-control" data-ng-model="selectedBrand" data-ng-change="filterProductsBrandWise()">
                            <option data-ng-repeat="brand in brandList | orderBy" value="{{brand.brandId}}">
                                {{brand.brandName}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Channel Partner</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select class="form-control" data-ng-model="selectedPartner" data-ng-change="filterProductsBrandWise()">
                            <option data-ng-repeat="partner in channelPartners | orderBy" value="{{partner.id}}">
                                {{partner.name}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div> -->

        <label class="switch">
            <input type="checkbox" ng-model="fetchAll"">
            <span class="slider round"></span>
        </label>
        <span>{{ fetchAll ? 'Fetch All Units' : 'Fetch Only Active Units' }}</span>


        <div class="row row-spacing" data-ng-if="trimmedRegions.length > 0">
            <div class="col-xs-8 form-group">
                <div class="form-group region-card">
                    <label class="col-xs-6 region-card">MultiRegion Select</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                         options="trimmedRegions" selected-model="storeSelectedRegion" class="col-xs-4 region-card">
                    </div>
                </div>

            </div>
        </div>
            <div class="row">
                <div class="col-xs-4 region-card pull-left">
                    <label>Upload Bulk Product Price Sheet</label>
                    <div class="d-flex">
                        <input class="btn btn-default" style="width: 100%;" type="file" file-model="unitPriceProfileFile" accept="">
                        <button class="btn btn-primary ms-2" data-ng-click="uploadBulkUnitPriceSheet()">Upload and update Mappings</button>
                    </div>
                    </div>
                <button data-ng-click="getAllUnitPriceProfileMapping()" class="btn btn-primary  pull-right">Get Details</button>
            </div>
            <div class="row">
                <button data-ng-click="saveMappings()" class="btn btn-primary pull-right">SUBMIT</button>
                <button data-ng-click="getUnitPriceProfileSheet()" class="btn btn-primary pull-right">download Excel</button>

            </div>
    </div>
        </div>
    </div>
    <div class="row container"
         data-ng-if="unitPriceProfileMappings != null && unitPriceProfileMappings.length > 0">
        <div class="col-xs-12 text-container">
            <table class="table table-bordered">
                <thead style="background-color: #50773e; color: #ffffff">
                <tr>
                    <th>Check &nbsp;
                        <input type="checkbox" data-ng-model='checkBoxModal.checkAll'
                               style="width: 20px; height: 20px" data-ng-click="updateAll()">
                    </th>
                    <th>Unit Name&nbsp;
                        <input
                                type="text"
                                data-ng-model="filters.unitName"
                                placeholder="enter Unit Name to filter row"
                                data-ng-change="applyFilters()"
                                class="form-control"/>
                    </th>
                    <th>Unit Status&nbsp;
                            <select data-ui-select2 class="form-control  select2-element" data-ng-model="filters.unitStatus" 
                            data-ng-change="applyFilters()">

                            <option data-ng-repeat="unitStatus in unitStatusList "
                                    value="{{unitStatus}}">{{unitStatus}}
                            </option>
                        </select>
                    </th>
                    <th>
                        <table style="  width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Price Profile</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select data-ui-select2 class="form-control  select2-element" data-ng-model="selectAll.profile" 
                                    data-ng-change="selectProfileForAll(selectAll.profile)">

                                    <option data-ng-repeat="profile in profiles | orderBy"
                                            value="{{profile}}">{{profile.priceProfileName}}
                                    </option>
                                </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllProfile" 
                                           data-ng-click="selectProfileForRows(selectAll.profile,checkBoxModal.checkAllProfile)">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <th>
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Price Profile Version</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select data-ui-select2 class="form-control  select2-element" data-ng-model="selectAll.version">
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option data-ng-repeat="version in allProfileVersions | orderBy"
                                                value="{{version}}">{{version.versionNo}}
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllVersion"
                                           data-ng-click="selectProfileVersionForRows(selectAll.version,checkBoxModal.checkAllVersion)">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <!-- <th>
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Status</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                            data-ng-model="checkBoxModal.updatedStatus">
                                        <option data-ng-repeat="status in pricingStatusList"
                                                value="{{status}}">
                                            {{status}}
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllStatus"
                                           data-ng-click="changeAllStatus(checkBoxModal.updatedStatus)">
                                </td>
                            </tr>
                        </table>
                    </th> -->
                    <!--For product alias-->
                
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="detail in filteredData | orderBy : 'unit.name'"
                    data-ng-class="{'row-selected': detail.checked}">

                    <td><input type="checkbox" style="width: 33px; height: 20px"
                               data-ng-model='detail.checked'>
                    </td>
                    <td>{{detail.unit.name}}</td>
                    <td>{{detail.unit.status}}</td>
                    <td>
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.priceProfile.name}}</td>
                                <td style="width: 100px;">
                                    <select data-ui-select2 class="form-control  select2-element" data-ng-model="detail.profile" 
                                    data-ng-change="selectProfile(detail.profile,detail)">
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option data-ng-repeat="profile in profiles | orderBy"
                                                value="{{profile}}">{{profile.priceProfileName}}
                                        </option>
                                    </select>
                                </td>
                                <!-- data-ng-change="changePrice(prodDetails[detail.unit.id].price, detail)" -->
                            </tr>
                        </table>
                    </td>
                    <td>
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.priceProfileVersion}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-model="detail.priceProfileVersion" >
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option data-ng-repeat="version in detail.versions | orderBy"
                                                value="{{version.versionNo}}">{{version.versionNo}}
                                        </option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td>
<!--                    for Status -->
                    <!-- <td>
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.mappingStatus}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-model="detail.mappingStatus">
                                      data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)"
                                        <option data-ng-repeat="status in pricingStatusList | orderBy"
                                                value="{{status}}">{{status}}
                                        </option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td> -->
                </tr>
                <tr>
                    <td align="right" colspan="8">

                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <button class="btn btn-primary pull-right fab" data-ng-click="submitDetails()">
            SUBMIT
        </button>
    </div>

    <div class="modal fade" id="productDetailsModal" tabindex="-1"
         role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="font-size: 10px; width: 860px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="myModalLabel">Summary: Product
                        List For Region {{selectedRegion}}</h4>
                </div>

                <div class="modal-body">
                    <div>
                        <button acl-action-checker="ADMN_PPU_MGT" class="btn btn-primary pull-right"
                                data-ng-click="submitUpdatePriceList()">Update
                        </button>
                    </div>
                    <br>
                    <br>
                    <br>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Unit Name&nbsp;</th>
                            <th>Product&nbsp;</th>
                            <th>Dimension&nbsp;</th>
                            <th>Current Price</th>
                            <th>New Price</th>
                            <th>Current Profile</th>
                            <th>New Profile</th>
                            <th>Current Status</th>
                            <th>New Status</th>
                            <th>Current Product Name</th>
                            <th>New Product Alias</th>
                            <th>Current Dimesnsion Description</th>
                            <th>New Dimension Description</th>
                            <th>Current IsDeliveryOnlyProduct</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat='detail in requestObject'>
                            <td>{{$index+1}}</td>
                            <td>{{detail.unit.name}}</td>
                            <td>{{detail.product.name}}</td>
                            <td>{{detail.price.dimension}}</td>
                            <td>{{detail.price.currentPrice}}</td>
                            <td>{{detail.price.price}}</td>
                            <td>{{detail.price.currentProfile}}</td>
                            <td>{{detail.price.profile}}</td>
                            <td>{{detail.price.currentStatus}}</td>
                            <td>{{detail.price.status}}</td>
                            <td>{{detail.price.currentAliasProductName}}</td>
                            <td>{{detail.price.aliasProductName}}</td>
                            <td>{{detail.price.currentDimensionDescriptor}}</td>
                            <td>{{detail.price.dimensionDescriptor}}</td>
                            <td>{{detail.price.isDeliveryOnlyProduct}}</td>
                        </tr>
                        <tr>
                            <td colspan="14" align="right">
                                <div
                                        class="form-group clearfix">
                                    <button acl-action-checker="ADMN_PPU_MGT" class="btn btn-primary pull-right"
                                            data-ng-click="submitUpdatePriceList()">Update
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
