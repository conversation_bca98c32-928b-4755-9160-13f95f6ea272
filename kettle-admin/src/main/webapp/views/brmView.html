<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
	<div class="col-lg-12">
		<h1 class="page-header">BRM Report Generation</h1>
	</div>
</div>

<div class="row" data-ng-hide="showPullView">
	<table class="table table-bordered table-hover table-condensed"
		style="width: 50%">
		<tr class="info">
			<th>Report Type</th>
			<th>Action</th>
		</tr>
		<tr>
			<td>Month on Month</td>
			<td>
				<div class="col-xs-2">
					<button data-ng-click="runMOMReport()" class="btn btn-warning">Generate</button>
				</div>
			</td>
		</tr>
		<tr>
			<td>Week on Week</td>
			<td>
				<div class="col-xs-2">
					<button data-ng-click="runWOWReport()" class="btn btn-warning">Generate</button>
				</div>
			</td>
		</tr>
	</table>
</div>
<div>
<td>
	<div>
	<form>
		<label for="segregated">Segregated:</label>
		<input type="radio" id="segregated" name="reportType" ng-model="selectedOption" ng-value="'segregated'">
         <br>
		<label for="consolidated">Consolidated:</label>
		<input type="radio" id="consolidated" name="reportType" ng-model="selectedOption" ng-value="'consolidated'">
	</form>
	</div>
</td></div>

<div class="row">
	<table class="table table-bordered table-hover table-condensed"
		style="width: 50%">
		<tr class="info">
			<th>Report Type</th>
			<th>Date</th>
			<th>Action</th>
		</tr>
		<tr>
			<td>Revenue Certificate</td>
			<td><div class="datepicker" data-date-format="yyyy-MM">
					<input class="form-control" data-ng-model="seletedDate" type="text" placeholder="yyyy-MM" required />
				</div></td>
			<td>
				<div>
					<button data-ng-click="generateRevenueCertificate(seletedDate)" class="btn btn-warning">Generate</button>
				</div>
			</td>
		</tr>
	</table>
	<div style="text-align: center" >
		<div>
			<span style="font-weight:bold">Select all certificates to upload </span>
			<input type="file" class="btn" id="multi-tds-pdf" data-ng-model="filesSelected" file-input="filesInput"
				   name="files" multiple accept=".pdf"><br><br>
			<input style="text-align: center" type="button" data-ng-click="sendEmails()"
				   value="Upload Revenue Certificate"/>
		</div>
	</div>
	<div>
		<table class="table table-bordered table-hover table-condensed"
			   style="width: 75%; margin-top: 20px;">
			<tr class="info">
				<th>File Name</th>
				<th>Sent</th>
				<th>Status</th>
			</tr>
			<tr data-ng-repeat="file in filesSelected">
				<td>{{file.fileName}}</td>
				<td>{{file.status}}</td>
				<td>{{file.message}}</td>
			</tr>
		</table>
	</div>
</div>

<!-- Modal -->
<div class="modal fade" id="alertModal" role="dialog">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">BRM Report</h4>
			</div>
			<div class="modal-body">
				<p><span class="glyphicon glyphicon-info-sign pull-left" style="font-size:24px; color:#3366cc; margin-right:5px"> </span> <span style="line-height: 28px;"> {{reportStatus}}</span></p>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">OK</button>
			</div>
		</div>
	</div>
</div>
