<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">Feedback Questions</h1>
    </div>
</div>
<button
        class="btn btn-primary pull-right"
        data-toggle="modal"
        ng-click="addNewQuestion()" style="margin-bottom: 20px">
    <i class="fa fa-plus fw"></i> Add New Question
</button>
<div class="row">
    <div class="col-xs-2" style="font-size: medium; margin-top: 4px">
        <label class="control-label">Select Question type</label>
    </div>
    <select
            style="margin-bottom:20px; width:30%"
            class="form-control"
            ng-model="selectQuestionType"
            ng-options="type for type in questionType"
    />
</div>
<button class="btn btn-primary " style="margin-top: 5px; margin-bottom:20px"
        data-ng-click="getallActiveQuestions()">Get Questions
</button>

<div class="form-group" data-ng-if="showTable">
    <table class="table table-bordered">
        <thead style="background-color: #e7e7e7">
        <th>Question</th>
        <th>Question Reason</th>
        <th>Question Type</th>
        <th>Action</th>
        <th>Mappings</th>
        </thead>
        <tbody>
        <tr ng-repeat="res in result track by $index">
            <td data-ng-if="finalQuestionType == 'MCQ'"
                style="max-width: 100px; word-wrap: break-word; overflow-wrap: break-word;">
                <div data-ng-if="$index == 0" data-ng-repeat="val in res.question.split('@')"> {{val}}<br></div>
                <div data-ng-if="$index !== 0" data-ng-repeat="val in res.question.split('@')">{{$index}}) {{val}}<br>
                </div>
            </td>
            <td data-ng-if="finalQuestionType !== 'MCQ'"
                style="max-width: 100px; word-wrap: break-word; overflow-wrap: break-word;">{{res.question}}<br></td>
            <td>{{res.questionReason}}</td>
            <td>{{res.questionType}}</td>
            <td>
                <button ng-if="res.questionStatus == 'ACTIVE'" ng-click="changeQuestionStatus(res)"
                        title="Active" style="border: none; background: none"><i class="fa fa-lightbulb-o"
                                                                                 style="font-size:48px;color:green"></i>
                </button>
                <button ng-if="res.questionStatus == 'IN_ACTIVE'" ng-click="changeQuestionStatus(res)"
                        title="Inactive" style="border: none; background: none"><i class="fa fa-lightbulb-o"
                                                                                   style="font-size:48px;color:red"></i>
                </button>
                <button title="Edit" ng-click="updateQuestionModal(res)" style="border: none; background: none"><i
                        class="fa fa-pencil-square-o" style="font-size:40px;"></i></button>
            </td>
            <td>
                <button class="btn btn-primary"
                        data-toggle="modal" ng-click="showMappings(res)">Mappings
                </button>
            </td>
        </tr>
        </tbody>

    </table>
</div>


<div
        class="modal fade"
        id="addNewQuestion"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close"
                        data-ng-click="init()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">Adding New Question</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-6">Select Question Type</div>
                    <div class="col-xs-6">
                        <select
                                style="margin-bottom:20px; width:30%"
                                class="form-control"
                                ng-model="selectQuestionType"
                                ng-options="type for type in questionType"
                                data-ng-change="getSelectedQuestionType()"
                        />
                    </div>
                    <div>
                        <div class="col-xs-6">Enter Question</div>
                        <input style="margin-bottom:20px; width:30%;" type="text" ng-model="questionToAdd"
                               data-ng-change="onQuestionInput(questionToAdd)" placeholder="Enter your Question"/>
                    </div>
                    <div data-ng-if="showOption">
                        <div class="col-xs-6">Enter Options</div>
                        <div data-ng-repeat="opt in optionsArray track by $index">
                            <div style="margin-left:50%;width:100%">
                                <div data-ng-if="optionsArray[$index].length >=50">One option should contain 50
                                    characters only
                                </div>
                                <input type="text" maxlength="50" style="width: 35%;height:30px; margin-bottom:10px;"
                                       ng-model="optionsArray[$index]"/>
                                <button class="btn btn-danger" ng-if="optionsArray.length != 1"
                                        ng-click='deleteOptions($index)'>-
                                </button>
                                <button class="btn btn-primary" ng-if="$index == optionsArray.length-1"
                                        ng-click='addOptions()'>+
                                </button>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="col-xs-6">Enter Reason</div>
                        <input style="margin-bottom:20px; width:30%;" type="text" ng-model="questionReason"
                               data-ng-change="onReasonInput(questionReason)" placeholder="Enter Question's reason"/>
                    </div>
                    <div style="justify-content:center; display:flex">
                        <button class="btn btn-primary" style="margin-bottom:0px; width:30%"
                                data-ng-click="addQuestion()">Add Question
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div
        class="modal fade"
        id="showMappings"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel2">Unit Mapping For Question</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <button class="btn btn-primary pull-right"
                            data-toggle="modal" ng-click="updateMappings()"
                            style="margin-right: 20px">Add New Mappings
                    </button>
                </div>
                <div class="row">
                    <div style="margin-left:10px; margin-right:10px;"><h3>Current Mapping for Question</h3></div>
                    <div class="row">
                        <button class="btn btn-primary pull-right"
                                data-toggle="modal" ng-click="bulkStatusModal()"
                                style="margin-right: 20px; margin-top:10px; margin-bottom:10px">Bulk Status Change
                        </button>
                    </div>
                    <div class="form-group" data-ng-if="showTable" style="margin-left:10px; margin-right:10px;">
                        <table class="table table-bordered">
                            <thead style="background-color: #e7e7e7">
                            <th>QuestionId</th>
                            <th>QuestionType</th>
                            <th>UnitName</th>
                            <th>Status</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="res in questionMappingList track by $index">
                                <td>{{res.questionId}}</td>
                                <td>{{res.questionType}}</td>
                                <td>{{res.unitName}}</td>
                                <td>
                                    <button ng-if="res.mappingStatus == 'ACTIVE'" ng-click="changeStatusOfMapping(res)"
                                            title="Active" style="border: none; background: none"><i
                                            class="fa fa-lightbulb-o" style="font-size:48px;color:green"></i></button>
                                    <button ng-if="res.mappingStatus == 'IN_ACTIVE'"
                                            ng-click="changeStatusOfMapping(res)"
                                            title="Inactive" style="border: none; background: none"><i
                                            class="fa fa-lightbulb-o" style="font-size:48px;color:red"></i></button>
                                </td>
                            </tr>
                            </tbody>

                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div
        class="modal fade"
        id="updateMappings"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel3">Update Unit Mapping For Question</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-4" style="font-size: medium; margin-top: 4px">
                        <label class="control-label">Select Regions</label>
                    </div>
                    <div class="col-xs-4 region-card" ng-dropdown-multiselect=""
                         options="regionlist"
                         selected-model="selectedRegions" extra-settings="multiSelectSettings">
                    </div>
                    <button data-ng-click="getUnitDetails()" class="btn btn-primary">Get Units</button>
                    <div data-ng-if="unitNameList.length !== 0" class="col-xs-4"
                         style="font-size: medium; margin-top: 6px">
                        <label class="control-label">Select Units</label>
                    </div>
                    <div data-ng-if="unitNameList.length !== 0" class="col-xs-4 region-card"
                         style="height : settings.scrollable ? settings.scrollableHeight :'auto',overflow :'auto', margin-top:10px;"
                         ng-dropdown-multiselect=""
                         options="unitNameList"
                         selected-model="selectedUnitNames" extra-settings="multiSelectSettings">
                    </div>
                </div>
                <div class="row2">
                    <div data-ng-if="unitNameList.length !== 0" style="justify-content:center; display:flex">
                        <button class="btn btn-primary" style="margin-top:10px; margin-bottom:0px; width:30%"
                                data-ng-click="updateQuestionMapping()">Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>


<div
        class="modal fade"
        id="updateQuestion"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel4">Update Question</h4>
            </div>
            <div class="modal-body">
                <div>
                    <div class="col-xs-6">Question</div>
                    <input style="margin-bottom:20px; width:30%;" type="text" ng-model="questionToUpdate"
                           data-ng-change="onUpdateQuestionInput(questionToUpdate)"/>
                </div>
                <div data-ng-if="showOption">
                    <div class="col-xs-6">Options</div>
                    <div data-ng-repeat="opt in optionsArray track by $index">
                        <div style="margin-left:50%;width:100%">
                            <div data-ng-if="optionsArray[$index].length >=50">One option can contain 50 characters
                                only
                            </div>
                            <input type="text" maxlength="50" style="width: 35%;height:30px; margin-bottom:10px;"
                                   ng-model="optionsArray[$index]"/>
                            <button class="btn btn-danger" ng-if="optionsArray.length != 1"
                                    ng-click='deleteOptions($index)'>-
                            </button>
                            <button class="btn btn-primary" ng-if="$index == optionsArray.length-1"
                                    ng-click='addOptions()'>+
                            </button>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="col-xs-6">Reason</div>
                    <input style="margin-bottom:20px; width:30%;" type="text" ng-model="questionReasonToUpdate"
                           data-ng-change="onUpdateQuestionReason(questionReasonToUpdate)"/>
                </div>
                <div style="justify-content:center; display:flex">
                    <button class="btn btn-primary" style="margin-bottom:0px; width:30%"
                            data-ng-click="updateFeedbackQuestion()">Update
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<div
        class="modal fade"
        id="bulkStatusChange"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel5">Update Unit Mapping For Question</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="display:flex">
                    <div class="col-xs-4" style="font-size: medium; margin-top: 4px">
                        <label class="control-label">Select Status</label>
                    </div>
                    <select
                            style="margin-bottom:20px; width:30%"
                            class="form-control"
                            ng-model="selectedStatus"
                            ng-options="type for type in unitStatuses"
                    />
                    <button data-ng-click="getUnitsByStatus()" class="btn btn-primary"
                            style="margin-left:10px; height: 20%;">Get Units
                    </button>
                </div>
                <div class="row" data-ng-if="unitListByStatus.length !== 0">
                    <div class="col-xs-4"
                         style="font-size: medium; margin-top: 6px">
                        <label class="control-label">Select Units</label>
                    </div>
                    <div class="col-xs-4 region-card"
                         style="height : settings.scrollable ? settings.scrollableHeight :'auto',overflow :'auto', margin-top:10px;"
                         ng-dropdown-multiselect=""
                         options="unitListByStatus"
                         selected-model="selectedUnitListByStatus" extra-settings="multiSelectSettingsForStatusChange">
                    </div>
                </div>
                <div class="row" style="margin-top:10px" data-ng-if="unitListByStatus.length !== 0">
                    <div style="justify-content:center; display:flex">
                        <button class="btn btn-primary" data-ng-if="selectedStatus == 'ACTIVE'"
                                data-ng-click="changeBulkStatus()">Mark Inactive
                        </button>
                        <button class="btn btn-primary" data-ng-if="selectedStatus == 'IN_ACTIVE'"
                                data-ng-click="changeBulkStatus()">Mark Active
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>