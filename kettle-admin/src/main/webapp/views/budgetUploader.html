<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .row {
        padding: 5px;
    }
</style>
<div class="row" data-ng-init="init()">
    <h1 class="page-header">Budget Manager</h1>
</div>
<div class="panel panel-info">
    <div class="panel-heading">Update Budget</div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM">
                    <input class="form-control" data-ng-model="budgetDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Budget Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(budgetDate,'BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Budget Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Budget Doc
                </button>

            </div>
        </div>
    </div>
</div>
<div class="panel panel-info">
    <div class="panel-heading">Update Manpower Budget </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM" max="{{maxDate}}">
                    <input class="form-control" data-ng-model="manpowerDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Manpower Budget Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(manpowerDate,'MANPOWER_BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Budget Manpower Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('MANPOWER_BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Manpower Budget Doc
                </button>

            </div>
        </div>
    </div>
</div>
<div class="panel panel-info">
    <div class="panel-heading">Update Channel Partner Budget </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM" max="{{maxDate}}">
                    <input class="form-control" data-ng-model="channelPartnerDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Channel Partner  Budget Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(channelPartnerDate,'CHANNEL_PARTNER_BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Channel Partner  Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('CHANNEL_PARTNER_BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Channel Partner Budget Doc
                </button>

            </div>
        </div>
    </div>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Update Bank Charges Budget </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM" max="{{maxDate}}">
                    <input class="form-control" data-ng-model="bankChargesDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Bank Charges Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(bankChargesDate,'BANK_CHARGES_BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Bank Charges Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('BANK_CHARGES_BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Bank Charges Budget Doc
                </button>

            </div>
        </div>
    </div>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Update Facility Charges </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM" max="{{maxDate}}">
                    <input class="form-control" data-ng-model="facilityDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Facility Charges Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(facilityDate,'FACILITY_CHARGES_BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Facility Charges Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('FACILITY_CHARGES_BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Facility Charges Doc
                </button>

            </div>
        </div>
    </div>
</div>


<div class="panel panel-info">
    <div class="panel-heading">Update Service Charges </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM" max="{{maxDate}}">
                    <input class="form-control" data-ng-model="serviceDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Service Charges Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(serviceDate,'SERVICE_CHARGES_BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Service Charges Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('SERVICE_CHARGES_BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Service Charges Doc
                </button>

            </div>
        </div>
    </div>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Regenerate Daily PNL Entry</div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">
                <label>Start Date</label>
                <div class="datepicker" date-format="yyyy-MM-dd">
                    <input class="form-control" ng-model="startDate"
                           placeholder="click here" type="text" required/>
                </div>
            </div>
            <div class="col-xs-6">
                <label>End Date</label>
                <div class="datepicker" date-format="yyyy-MM-dd">
                    <input class="form-control" ng-model="endDate"
                           placeholder="click here" type="text" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <label>Unit</label> <select ui-select2 class="form-control"
                                            style="width: 100% !important" data-ng-model="selectedUnit"
                                            data-placeholder="Select a Unit"
                                            data-ng-change="selectUnit(selectedUnit)"
                                            data-ng-options="u as u.name for u in units">
            </select>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-primary" style="margin-top: 20px;"
                        data-ng-click="regeneratePnlForUnit()"
                        data-ng-disabled="endDate == null || startDate == null || selectedUnit == null">Regenerate
                    For Unit
                </button>
            </div>
        </div>
        <div class="row" style="margin-top: 10px;">
            <div class="col-xs-6">
                <label>All Units</label>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-danger" data-ng-click="regeneratePnlForAll()"
                        data-ng-disabled="endDate == null || startDate == null ">Regenerate
                    For All
                </button>
            </div>
        </div>
    </div>
</div>
<div class="panel panel-info">
    <div class="panel-heading">Regenerate Finalized</div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-12">
                to generate finalized PNL for a month select date of the month after the required month, as finalized is
                generated via last day Monthly closing entries of a month.
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <label>Start Date</label>
                <div class="datepicker" date-format="yyyy-MM-dd">
                    <input class="form-control" ng-model="finalizedDate"
                           placeholder="click here" type="text" required/>
                </div>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-danger" data-ng-click="regenerateFinalizedForAll()"
                        data-ng-disabled="finalizedDate == null">Regenerate Finalized
                </button>
            </div>
        </div>
    </div>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Upload Penetration Targets</div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">
                <button class="btn btn-danger" data-ng-click="uploadMonthlyPenetrationData()">Upload Monthly Data
                </button>
            </div>
        </div>
    </div>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Download PNL Data</div>
    <div class="panel-body"></div>
</div>