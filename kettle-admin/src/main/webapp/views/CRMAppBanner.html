<style type="text/css">
    input.checkbox {
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: inline;
    }

    input[type = text] {
        width: 100px;
        display: inline-block;
        margin-left: 10px;
    }
</style>

<div id="fullOfferDiv" ng-init="init()" xmlns:70vh xmlns:70vh xmlns:70vh
     xmlns:max-height="http://www.w3.org/1999/xhtml">
    <div id="offerListDivDetail">
        <div class="row">
            <div class="col s8">
                <br>
                <h1 class="page-header">
                    CRM App Banner Management
                </h1>
            </div>
            <div class="col-lg-2">
                <button class="btn btn-primary pull-right" data-toggle="modal"
                        data-target="#addTable" ng-click="addCRMAppOfferDialog()">
                    <i class="fa fa-plus fw"></i> Add Screen
                </button>
            </div>
        </div>
        <br>
        <div class="row">
            <div class="col-lg-3 ">
                <label>Select Region</label>
            </div>
            <div class="col-lg-4">
                <select ng-model="selectedRegion" class='form-control'
                        ng-options="city for city in regions" data-ng-change="getUserDetails(selectedRegion)">
                </select>
            </div>
            <div class="col-lg-3">
                <button class="btn btn-primary" data-ng-click="getScreenDetails()"> search</button>
            </div>
        </div>
        <br><br>
        <div class="row" ng-if="screenDetails != null">
            <div class="row ">
                <div class="col-xs-12">
                    <div class="row ">
                        <div class="col-xs-12 overflow">


                            <div data-ng-show="screenDetails != null && screenDetails.length > 0">

                                <div class="form-group col-lg-2">
                                    Filter:
                                    <input type="text" ng-model="search" ng-change="filter()" placeholder="Filter"
                                           class="form-control"/>
                                </div>

                                <table class="table table-striped table-bordered">
                                    <thead style="background-color: #e7e7e7">
                                    <th>
                                        <input type="checkbox" class="checkbox" data-ng-model='globalSelect'
                                               data-ng-change="selectAll()">
                                        <span style="display: inline-block; vertical-align: super">Select All</span>
                                    </th>
                                    <th>Current Status</th>
                                    <th>
                                        <div>Status</div>
                                        <select class="form-control" data-ng-model="globalStatus"
                                                style="display: inline-block; width: 150px;"
                                                data-ng-options="status as status.name for status in mappingStatusList track by status.id"
                                                data-ng-change="updateGlobalStatus(globalStatus)"/>
                                    </th>
                                    <th>Screen Type</th>
                                    <th>Unit</th>
                                    <th>Screen Image</th>
                                    <th>Action</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="unit in screenDetails | filter:search"
                                        data-ng-style="unit.selected === true ? {'background':'#b9eab9','cursor':'pointer'}:{}">
                                        <td>
                                            <input type="checkbox" class="checkbox" ng-checked="unit.selected"
                                                   data-ng-click="toggleSelectMapping(unit, true)">
                                        </td>
                                        <td>{{unit.status}}</td>
                                        <td>
                                            <select class="form-control" ng-click="setNewStatus(unit)" data-ng-model="unit.newStatus"
                                                    style="display: inline-block; width: 150px;"
                                                    data-ng-options="status as status.name for status in mappingStatusList track by status.id"/>
                                        </td>
                                        </td>
                                        <td>{{unit.screenType}}</td>
                                        <td>{{unit.unitName}}</td>
                                        <td style="width: 25%">
                                            <div align="center">
                                                <img height="50px" width="50px"
                                                     data-ng-click="openProductImageModal(unit.imagePath)"
                                                     data-ng-src="{{unit.imagePath}}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <button
                                                    class="btn btn-primary"

                                                    data-toggle="modal"
                                                    data-target="#addTable"
                                                    ng-click="onEditScreenDetail(unit)">Edit
                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div class="form-group">
                                    <input class="btn btn-primary"
                                           data-ng-click="submitAddMetadata()" type="button"
                                           value="Update Status"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add New Screen Modal -->
<div aria-labelledby="AddNewAppOfferModalLabel" class="modal fade" id="addTable" role="dialog"
     data-keyboard="false" data-backdrop="static"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" ng-click="reset()" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <div ng-show="isAddNewOffer">
                    <h4 class="modal-title" id="myModalLabel_add">{{isUpdateScreen === true ? "Update Screen" : "Add New Screen"}}</h4>
                </div>
            </div>

            <div class="modal-body">
                <form name="addNewReportForm" novalidate>
                    <fieldset ng-disabled="isViewOffer">
                        <div class="form-group">
                            <label>Screen Type</label>
                            <div class="form-group">
                                <select class="form-control screen-select" data-ng-model="crmObject.screenType"
                                        ng-disabled="isUpdateScreen">
                                    <option ng-repeat="screen in screenType">{{screen}}</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Region</label>
                            <!--<select-->
                            <!--class="form-control"-->
                            <!--placeholder="select options"-->
                            <!--&gt;-->
                            <!--<option data-ng-model="" ng-repeat="" >{{city}}</option>-->
                            <!--</select>-->

                            <select data-ng-model="crmObject.city" class='form-control'
                                    data-ng-options="city for city in regions"
                                    data-ng-change="getUserDetails(crmObject.city)"
                                    ng-disabled="isUpdateScreen"
                                    required="required">
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Cafe <span style="font-weight: lighter; color: #ff0000; font-style: italic">{{isUpdateScreen ? "(" + unitToBeUpdated.unitName + ")" : ""}}</span></label>
                            <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="units"
                                 selected-model="unitMultiSelect"
                                 extra-settings="multiSelectSettings" disabled="isUpdateScreen ? true : false"></div>
                        </div>

                        <div class="form-group">
                            <label>Content Type</label>
                            <select
                                    class="form-control"
                                    data-ng-model="crmObject.contentType"
                                    ng-disabled="isUpdateScreen"
                                    placeholder="select options"
                            >
                                <option ng-repeat="type in contentType">{{type}}</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label> Screen Image </label>
                            <div class="row">
                                <div class="col-lg-8">
                                    <input class="btn btn-default"
                                           file-model="crmObject.listImageToUpload"
                                           style="width: 100%;"
                                           type="file">
                                </div>
                                <div class="col-lg-4">
                                    <button class="btn btn-primary "
                                            ng-click="uploadScreenImage(crmObject.screenCode)">Upload
                                    </button>
                                    <div class="pull-right">
                                        <div ng-show="crmObject.listImage.name">
                                            <img height="50px" width="50px"
                                                 data-ng-click="openProductImageModal(crmObject.listImage)"
                                                 data-ng-src="{{crmObject.listImage.url}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <span ng-if="listImageUploaded">{{crmObject.listImage.name }} uploaded! </span>
                        </div>

                        <div ng-show="isAddNewOffer">
                            <div class="form-group clearfix">
                                <button class="btn btn-primary pull-left"
                                        ng-click="reset()" ng-disabled="isUpdateScreen">
                                    Reset
                                </button>
                                <button class="btn btn-primary pull-right"
                                        ng-click="isUpdateScreen ? updateCrmScreenDetail(crmObject) : addCrmScreenDetail(crmObject)"
                                        data-dismiss="modal">
                                    {{isUpdateScreen === true ? "Update Screen" : "Add New Screen"}}
                                </button>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
        </div>
    </div>
</div>

<!--image view modal-->
<div class="modal fade" id="displayImageModal" style="z-index: 9999;" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document" style="width: 700px;">
        <div class="modal-content">
            <div class="frame" style="margin-top: 40px;margin: auto;">
                <img style="    max-height: 70vh;max-width: 70vw;"
                     data-ng-src="{{imageSrc}}"/>
            </div>
        </div>
    </div>
</div>
