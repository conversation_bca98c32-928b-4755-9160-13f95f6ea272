<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style type="text/css">
    .resultGrid {
        width: auto;
        height: 410px;
        background: #fff;
        margin: 10px auto;
    }

    .ui-grid-pager-row-count-picker select {
        width: 100px;
    }
</style>
<div data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h1 class="page-header">
                Report Management Dashboard
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <label class="col-xs-2 pull-right">Select data source : </label>
            </div>
            <div class="row">
                <div class="col-xs-10">
                    <input class="btn btn-primary" data-ng-click="addNewReportCategoryDialog()"
                           data-ng-if="(environment == 'PROD' || environment == 'SPROD') && checkAccess"
                           type="button" value="Add New"/>
                    <input class="btn btn-primary" data-ng-click="addNewReportCategoryDialog()"
                           data-ng-if="(environment != 'PROD' && environment != 'SPROD')"
                           type="button" value="Add New"/>
                    <input class="btn btn-primary" data-ng-click="downloadReportTemplate()" type="button"
                           value="Download Template"/>
                </div>
                <div class="col-xs-2">
                    <select class="form-control" data-ng-model="environment" data-ng-change="getReportCategoryListByEnv(environment)"
                            data-ng-options="environment as environment for environment in environments"></select>
                </div>
            </div>
            <br/>

        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <label>Select report category</label>
            <div class="row">
                <div class="col-xs-6" data-ng-if="totalReport > 0">
                    <select class="form-control" data-ng-change="getReportVersionDetailsById(report)"
                            data-ng-model="report"
                            data-ng-options="report as report.reportName for report in reportList"></select>
                </div>
                <div class="col-xs-6">
                    <button class="btn btn-primary col-lg-pull-1" ng-click="getReportVersionDetailsById(report)">GET REPORT DETAILS</button>
                </div>
            </div>

            <br/>

            <div class="row">
                <div class="col-xs-12">
                    <p>Total Results {{totalItems}} </p>
                    <table class="table table-striped table-bordered" id="tableDataStructure"
                           style="margin-padding:0px;">
                        <thead>
                        <th>Report ID</th>
                        <th>Exec Env</th>
                        <th>Report Version</th>
                        <th>Report Name</th>
                        <th>Status</th>
                        <th>department Name</th>
                        <th>Report Type</th>
                        <th>Action</th>
                        </thead>
                        <tbody>
                        <tr  ng-if="totalReport > 0" ng-repeat="reportVersionDetail in reportVersionList | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                            <td>{{reportVersionDetail.reportId}}</td>
                            <td>{{reportVersionDetail.executionEnvironment}}</td>
                            <td>{{reportVersionDetail.reportVersion}}</td>
                            <td>{{reportVersionDetail.reportName}}</td>
                            <td>{{reportVersionDetail.status}}</td>
                            <td>{{reportVersionDetail.departmentName}}</td>
                            <td>{{reportVersionDetail.reportType}}</td>
                            <td><span style="margin:5px 5px"><button class="btn btn-primary pull-right"
                                                                     data-ng-click="downloadNewReportVersion(reportVersionDetail.versionId)"
                                                                     id="downloadVersion">Download</button></span><br>
                                <span style="margin:5px 5px"><button class="btn btn-primary pull-right" data-ng-if="reportVersionDetail.defaultV == 'N'"
                                                                     data-ng-click="markAsDefaultVersion(reportVersionDetail.versionId)"
                                                                     id="markDefault">Mark Default</button></span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <br/>
                    <div class="row">
                        <div class="col-xs-12 text-right">
                            <input class="btn btn-primary"
                                   data-ng-if="(environment == 'PROD' || environment == 'SPROD') && checkAccess"
                                   data-ng-click="uploadNewVersionDialog()" type="button"
                                   value="Upload New Version"/>
                            <input class="btn btn-primary"
                                   data-ng-if="(environment != 'PROD' && environment != 'SPROD')"
                                   data-ng-click="uploadNewVersionDialog()" type="button"
                                   value="Upload New Version"/>
                            <input class="btn btn-success" data-ng-click="updateReportStatus()" data-ng-if="report.reportStatus=='IN_ACTIVE'"
                                   type="button" value="Activate">
                            <input class="btn btn-danger" data-ng-click="updateReportStatus()" data-ng-if="report.reportStatus=='ACTIVE'"
                                   type="button" value="Deactivate">
                        </div>
                    </div>
                    <br/>
                </div>
            </div>


        </div>
    </div>
</div>


<!-- Modal -->
<div aria-labelledby="myModalLabel" class="modal fade" id="queryDisplayModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Query Content</h4>
            </div>
            <div class="modal-body">
			  <pre>
                  	{{reportType.content}}
                </pre>

            </div>
        </div>
    </div>
</div>
<!-- Add New Report Modal -->
<div aria-labelledby="AddNewReportModalLabel" class="modal fade" id="AddNewReportModalData" role="dialog" tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_add"> {{msg}} Add New Report Version</h4>
            </div>

            <div class="modal-body">
                <form name="addNewReportForm" novalidate>
                    <div class="form-group">
                        <label>Exec. Environment *</label>
                        <!-- <select class="form-control" ng-model="selectedCatDataList" ng-options="categoryName for categoryName as categoryName in categoryArrList" ng-change="addCatDetailData(selectedCatDataList.categoryName)">
                        </select>  -->
                        <div class="form-group">
                            <select class="form-control" data-ng-model="environment"
                                    data-ng-options="environment as environment for environment in environments"></select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Report Type</label>
                        <div class="form-group">
                            <select class="form-control" data-ng-model="selectedReportType"
                                    data-ng-options="reportType as reportType for reportType in reportTypes"></select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Department Name*</label>
                        <select
                                class="form-control"
                                data-ng-model="selectedDepartment"
                                data-ng-options="department as department.name for department in departmentlist track by department.id"
                                required>
                        </select>
                    </div>

                    <div class="form-group">
                        <label> Report Name *</label>
                        <input class="form-control" ng-model="reportName" required type="text"/>
                    </div>

                    <div class="form-group clearfix">
                        <button class="btn btn-primary pull-right" ng-click="submitAddNewReportData()">Add Report
                        </button>
                    </div>

                    <div class="form-group clearfix" ng-if="action=='UpdateCatAction'">
                        <button class="btn btn-primary pull-right"
                                ng-click="submitUpdateCategory(updateIDS,catStatus,types)">Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Upload New Report Version Modal -->
<div aria-labelledby="UploadNewReportVersionModalLabel" class="modal fade" id="UploadNewReportVersionModalData"
     role="dialog" tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_upload_new"> {{msg}} Upload Report Version</h4>
            </div>

            <div class="modal-body">
                <form name="addNewReportVersionForm" novalidate>

                    <div class="form-group">
                        <label> Comments *</label>
                        <input class="form-control" ng-model="comments" required type="text"/>
                    </div>

                    <div class="form-group">
                        <input class="btn btn-default" file-model="fileToUpload" style="width: 100%;"
                               type="file">
                    </div>

                    <div class="form-group clearfix">
                        <button class="btn btn-primary pull-right" data-ng-disabled="fileToUpload==null"
                                ng-click="updateNewReportVersionData()">Add Version
                        </button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
