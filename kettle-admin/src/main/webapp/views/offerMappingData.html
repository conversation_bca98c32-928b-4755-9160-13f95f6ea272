<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	<loading style align="center"></loading>
        <h1 class="page-header">
        	Coupons Mapping
        </h1>
        
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
    	Filter: 
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
    </div>
</div>


<div class="row">
	<div class="col-xs-12">
    	<p>Filtered {{ couponsList.length }} of  total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                         <th>ID &nbsp;</th>
                        <th>Code&nbsp;<a ng-click="sort_by('code');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Start Date&nbsp;</th>
                        <th>End Date&nbsp;</th>
                        <th>Mannual Override&nbsp;</th>
                        <th>MaxUsage&nbsp;</th>
                        <th>Reusable&nbsp;</th>
                        <th>Reusable By Customer&nbsp;</th>
                         <th>Status&nbsp;</th>
                         <th>Usager&nbsp;</th>
                         <th>Action&nbsp;</th>
                        
                        <th align="center">Action&nbsp;</th>
                    </thead>
                <tbody>
                <tr><td>{{couponsList}}</td></tr>
                    <tr ng-repeat="couponsData in filtered = (couponsList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                        <td>{{couponsData.id}}</td>
                        <td>{{couponsData.code}}</td>
                        <td>{{couponsData.startDate}}</td>
                        <td>{{couponsData.endDate}}</td>
                        <td>{{couponsData.manualOverride}}</td>
                        <td>{{couponsData.masUsage}}</td>
                        <td>{{couponsData.reusable}}</td>
                        <td>{{couponsData.reusableByCustomer}}</td>
                         <td>{{couponsData.status}}</td>
                         <td>{{couponsData.usage}}</td>
                        <td align="left">
                            <img ng-click="viewEmployee(employee)" style="margin-bottom:8px;cursor:pointer" title="View Employee" ng-src="img/viewEye.png" height="25px" width="25px">&nbsp;&nbsp;
                            
                        </td> 
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>





</div>

