<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
textarea {
	overflow: scroll;
	height: 100px;
}

.row .col {
	margin: 5px 10px;
}

.popup {
	visibility: hidden;
	min-width: 250px;
	margin-left: -125px;
	background-color: #A91B0D;
	color: #fff;
	text-align: center;
	border-radius: 2px;
	padding: 16px;
	position: fixed;
	z-index: 1051;
	right: 20px;
	bottom: 30px;
	font-size: 17px;
}

.popup.show {
	visibility: visible;
	-webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
	animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@
-webkit-keyframes fadein {from { bottom:0;
	opacity: 0;
}

to {
	bottom: 30px;
	opacity: 1;
}

}
@
keyframes fadein {from { bottom:0;
	opacity: 0;
}

to {
	bottom: 30px;
	opacity: 1;
}

}
@
-webkit-keyframes fadeout {from { bottom:30px;
	opacity: 1;
}

to {
	bottom: 0;
	opacity: 0;
}

}
@
keyframes fadeout {from { bottom:30px;
	opacity: 1;
}

to {
	bottom: 0;
	opacity: 0;
}
}
</style>
<div ng-init="init()">
	<div class="row" style="padding: 10px">
		<div class="col-lg-12">
			<br>
			<h1 class="page-header">Data Encryption</h1>
		</div>
	</div>
	<div class="row" style="padding: 10px">
		<br>
		<div class="form-group">
			<label>Plain Text</label> <br>
			<textarea id="plainText" ng-model="plainText" name="PlainText"
				rows="4" cols="50">
				</textarea>
		</div>
	</div>
	<div class="row" ng-show="encryptedText" ng-show="encryptedText"
		style="padding: 10px">
		<br>
		<div class="form-group">
			<label>Encrypted Text</label> <br>
			<textarea id="encryptedText" ng-model="encryptedText"
				name="EncryptedText" rows="4" cols="50">
			</textarea>
		</div>
	</div>
	<div class="row" style="padding: 10px">
		<button class="btn btn-primary" ng-click="encryptData()">Encrypt
		</button>
	</div>
</div>
