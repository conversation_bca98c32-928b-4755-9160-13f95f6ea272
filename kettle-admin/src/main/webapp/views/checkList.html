<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

  <div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	 <h1 class="page-header">
        	Check List 
           <button class="btn btn-primary pull-right" data-toggle="modal" id="myBtnCheckList" ng-click="addCheckList()"><i class="fa fa-plus fw"></i> Add Check List</button>
        </h1>
        
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
            <option value="500">500</option>
        </select>
    </div>
</div><br>

<div class="row">
	<div class="col-xs-12" >
    	 <p>Filtered {{ checkListView.length }} of {{ totalItems}} total results</p> 
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                    	<th>S.No</th>
                         <th> Name &nbsp;<a ng-click="sort_by('name');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th> Category &nbsp;<a ng-click="sort_by('category');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th> Description &nbsp;<a ng-click="sort_by('description');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th> Type &nbsp;<a ng-click="sort_by('type');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th> Status &nbsp;<a ng-click="sort_by('status');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th> Station &nbsp;<a ng-click="sort_by('station');"><i class="glyphicon glyphicon-sort"></i></a></th>
                         <th> Action &nbsp;</th>
                  </thead>
                <tbody>
                    <tr ng-repeat="checkListDetails in checkListView | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit | filter:search | orderBy : predicate :reverse">
                    <!-- <tr ng-repeat="products in filtered = (productDetailsList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                     -->
                    
                      <td>{{$index+1}}</td>
                      <td>{{checkListDetails.name}}</td>
                      <td>{{checkListDetails.category}}</td>
                      <td>{{checkListDetails.description}}</td> 
                      <td>{{checkListDetails.type}}</td>
                      <td>{{checkListDetails.status}}</td>
                      <td>{{checkListDetails.station}}</td>
                      
                      
                      <td align="left">
                      <span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600" ng-click="viewItemsData(checkListDetails.items)" title="View Items">Items</span>
                       | &nbsp;&nbsp;
                      <span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600" ng-click="viewScheduleData(checkListDetails.schedule)" title="View Schedule">Schedule</span>
                      | &nbsp;&nbsp;<span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600" ng-click="editCheckListDetails(checkListDetails)" title="View Schedule">Edit</span>
                      <!-- <span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600" ng-click="updateCheckListStatus('IN_ACTIVE',checkListDetails.id)" ng-show="checkListDetails.status=='ACTIVE'">INACTIVE</span> -->
  					   | &nbsp;&nbsp;
  					  <span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600"  ng-click="updateCheckListStatus('ACTIVE',checkListDetails._id)" ng-show="checkListDetails.status=='IN_ACTIVE'">Active</span>
  					   <span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600" ng-click="updateCheckListStatus('IN_ACTIVE',checkListDetails._id)" ng-show="checkListDetails.status=='ACTIVE'">Inactive</span>
  					  
  					 <!--  <span style="margin-bottom:8px;cursor:pointer; color:blue; font-weight:600" ng-click="updateCheckListStatus('ACTIVE',checkListDetails.id)" ng-show="checkListDetails.status=='INACTIVE'">ACTIVE</span>   -->
   					 

                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>
        
    <div class="modal fade" id="addCheckListModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" >
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">Check List </h4>
          </div>
          <div class="modal-body">
                <div>
                  <!-- Nav tabs -->
                  <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" ng-class="{active: !tab1, disabled: tab1}">
                    	<a data-target="#cafe" aria-controls="cafe" role="tab" data-toggle="tab">Basic Info</a>
                    </li>
                    <li role="presentation" ng-class="{active: !tab2, disabled: tab2}">
                    	<a data-target="#cod" aria-controls="cod" role="tab" data-toggle="tab">Check List Items</a>
                    </li>
                    <li role="presentation" ng-class="{active: !tab3, disabled: tab3}">
                    	<a data-target="#callcenter" aria-controls="callcenter" role="tab" data-toggle="tab">Schedule</a>
                    </li>
                    <li role="presentation" ng-class="{active: !tab4, disabled: tab4}">
                    	<a data-target="#summary" aria-controls="summary" role="tab" data-toggle="tab">View Summary</a>
                    </li>
                  </ul>
                
                  <!-- Tab panes -->
                  <div class="tab-content">
                    <div role="tabpanel" class="tab-pane" ng-class="{active: !tab1}" id="cafe">
                    	<h3>Basic Info</h3>
                      <div class="form-group">
                              <label style="font-size:11px">Name *</label>
                              <input class="form-control" ng-model="basicInfoName"/>
                       </div>
                       <div class="form-group">
                              <label style="font-size:11px">Description *</label>
                              <input class="form-control" ng-model="basicInfoDescription"/>
                       </div>
                       
                        <div class="form-group">
                  			<label style="font-size:11px">Station * </label>
                			<select class="form-control" ng-model="selectStationType" ng-options="stationTypesResult.name for stationTypesResult in stationResultData track by stationTypesResult.name"></select>
			  			</div>
                       
                       <div class="form-group">
                  			<label style="font-size:11px">Category * </label>
                			<select class="form-control" ng-model="selectCategoryType" ng-options="categoryType.name for categoryType in categoryCheckList track by categoryType.name"></select>
			  			</div>
                       
                       
                       <div class="form-group">
                  			<div><label style="font-size:11px">Location * </label>
                  			<label style="font-size:11px">&nbsp;CAFE &nbsp;</label>
                  			<input type="checkbox" checked="checked" ng-model="cafeLocation" value="CAFE" style="width:20px; height:20px"  ng-click="displayList(cafeLocation,'CAFE')">
                  			 <label style="font-size:11px">&nbsp;CHAI ON DEMAND &nbsp;</label><input type="checkbox" checked="checked"  ng-model="codLocation"  value="DELIVERY"  style="width:20px; height:20px"  ng-click="displayList(codLocation,'DELIVERY')"></div>
			  			</div> 
                       
                       
                       <div class="form-group">
                  			<label style="font-size:11px">Type * </label>
                			<div align="right" style="font-size:11px">* Support only Periodic</div>
                			<select class="form-control" ng-model="selectTypeCheckList" ng-options="selectTypeCheckListData.name for selectTypeCheckListData in typeCheckList track by selectTypeCheckListData.name"></select>
			  			</div>
                       
                          
                          
                        <div class="row">
                            <div class="col-xs-12">
                            	<ul><li ng-repeat="addedCafe in addedCafes track by addedCafe.id">
                            		{{addedCafe.name}} <button class="btn btn-danger btn-xs" ng-click="removeUnit('cafe',addedCafe.id)"><i class="fa fa-close fw"></i></button>
                                </li></ul>
                            </div>
                        </div>
                        <button class="btn btn-primary" type="button" ng-click="addBasicInfoDetails()">Next</button>
                    </div>
                    
                    <div role="tabpanel" class="tab-pane" ng-class="{active: !tab2}" id="cod">
                       <div class="form-group">
                              <label style="font-size:11px">Task *</label>
                              <input class="form-control" ng-model="stepItems"/>
                       </div>
                       <div class="form-group">
                              <label style="font-size:11px">Resource * </label>
                              <input class="form-control" ng-model="actionItems"/>
                       </div>
                       
                       <div class="form-group">
                              <label style="font-size:11px">Observation </label>
                              <input class="form-control" ng-model="descriptionItems"/>
                       </div>
                      
               <div class="form-group">
                  <label style="font-size:11px">Mandatory * </label>
               <select class="form-control" ng-model="selectMandatoryTypeItems" ng-options="mandatoryTypes.name for mandatoryTypes in mandatoryTypeItems track by mandatoryTypes.name"></select>
			  </div>
                          
               <div class="form-group">
                  <label >Response Type * </label>
                <select class="form-control" ng-model="selectResponseTypeItems" ng-options="responseTypesData.name for responseTypesData in responseType track by responseTypesData.name"></select>
			  </div>
			  
			  
			 <!--  <div class="form-group" ng-show="probablyValuesAction=='Activate'">
                              <label>Probably Value *</label>
                              <input class="form-control" type="text" ng-model="probablyValuesData"/>
              </div> -->
			  
			  <!--  <div class="form-group">
                  <label>Status * </label>
                <select class="form-control" ng-model="selectItemsStatus" ng-options="itemStatusData.name for itemStatusData in ItemsStatus track by itemStatusData.name"></select>
			  </div>
			   -->
			  
			  <div class="form-group">
			  	<div class="col-xs-12"><button class="btn btn-primary btn-lg btn-block" type="button" ng-click="addItemsInfoDetails()">Submit</button>
			  </div>
			  </div><br></br>
			             
			             
			    <div style="margin-top:20px" ng-if="completeCheckListObj.items.length > 0">
			    <table class="table table-striped table-bordered">
                    <thead>
                    	 <th> S.No</th>
                         <th> Task &nbsp;</th>
                         <th> Resource &nbsp;</th>
                         <th> Observation &nbsp;</th>
                         <th> Mandatory &nbsp;</th>
                         <th> Response Type &nbsp;</th>
                         <th> Action&nbsp;</th>
                        
                  </thead>
                <tbody>
                   <tr ng-repeat="checkListDetails in completeCheckListObj.items">
                      <td>{{$index+1}}</td>
                      <td>{{checkListDetails.step}}</td>
                      <td>{{checkListDetails.action}}</td>
                      <td>{{checkListDetails.description}}</td> 
                      <td>{{checkListDetails.mandatory}}</td>
                      <td>{{checkListDetails.responseType}}</td>
                      <td ng-show="ActionData=='add'"> <button class="btn btn-danger btn-xs" ng-click="removeItemsData($index)"><i class="fa fa-close fw"></i></button></td>
                      <td ng-show="ActionData=='edit'"> 
                      <button class="btn btn-danger btn-xs" ng-click="editSpecificItemsData(checkListDetails)">Edit</button></td> 
                    </tr>
                </tbody>
                </table>
                <br></br>
               </div>  
			             
			    <div ng-if="completeCheckListObj.items.length > 0">      
                <button class="btn btn-primary" type="button" ng-click="selectTab('tab1')">Prev</button>
                <button class="btn btn-primary" type="button" ng-click="addItemsInfoDetailsNext()">Next</button></div> 
              </div>
                    
                    <div role="tabpanel" class="tab-pane" ng-class="{active: !tab3}" id="scheduleDetailsDiv">
                    	<h3>Schedule</h3>
                        <div class="row">
                            <div class="col-xs-12">
                            	  <label>Do you need notification ?</label><input type="checkbox" checked="checked" ng-model='notificationRequired' style="width:20px; height:20px"  ng-click="showNotify(notificationRequired)">
                            </div>
                        </div>
                        <div class="form-group" ng-show="notificationRequiredData!='Activate'" >
                              <label>Do you need notification before minutes</label>
                              <input class="form-control" type="number" ng-model="notificationBeforeRequired"/>
                       </div>
                       
                       <div class="form-group">
                              <label>Editable before minutes</label>
                              <input class="form-control" type="number" ng-model="editableBeforeMinutes"/>
                       </div>
					       <div class="form-group">
					       <label>Frequency</label>
					        <select class="form-control" ng-model="selectFrequency" ng-options="frequencyDatas.name for frequencyDatas in frequencyType track by frequencyDatas.name" ng-change="showFrequecnyData(selectFrequency)"></select>
					      </div>
                       <div class="form-group" ng-show="frequencyCodeAction=='OPENING' || frequencyCodeAction=='CLOSING'">
                       
                       	 	 <label> After</label>
                       	 	 <input type="radio" ng-model="after" ng-value="true" ng-change="showFreq()"> 
  							<label>Before</label>
  							<input type="radio" ng-model="after" ng-value="false" ng-change="showFreq()"><br>
                       	 	<label>Time difference {{frequencyCodeAction}}</label>
                            <input class="form-control" type="number" ng-model="frequencyValue" />
                       </div>
                       
                      	<div class="col-xs-8" ng-show="frequencyCodeAction=='INTRA_DAY'">
                        <label>Hours of the days</label>
					            <select class="form-control" ng-model="selecthoursIntraDayType" ng-options="hoursTypes.name for hoursTypes in hoursType track by hoursTypes.name" ng-change="showIntraDayHours(selecthoursIntraDayType)"></select>
					    </div>
					    
							<div class="col-xs-2" style="padding:25px" ng-if="frequencyCodeAction=='INTRA_DAY'">
                            	<button class="btn btn-success" ng-click="addIntraDayHours()"><i class="fa fa-plus fw"></i></button>
                            </div> 
						<div class="row" ng-show="frequencyCodeAction=='INTRA_DAY'">
                            <div class="col-xs-12">
                            	<ul>
                            	<li ng-repeat="hoursOftheDayIntraListView in hoursOftheIntraDayListData">
                               	{{hoursOftheDayIntraListView.name}} 
                                	<button class="btn btn-danger btn-xs" ng-click="removeIntraDayHours(hoursOftheDayIntraListView)"><i class="fa fa-close fw"></i></button>
                                </li>
                                </ul>
                            </div>
                        </div>
					    
                      
                      
                       <div class="form-group" ng-show="frequencyCodeAction=='SHIFT_HANDOVER'">
                              <label>After hand over</label>
                              
                              <input type="radio" ng-model="HandOverRadio" ng-value="true" name="radioShiftOver" id="radioShiftOver1"/>
                                                           
                        	  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label> Before Hand Over </label> <input type="radio" ng-model="HandOverRadio"  ng-value="false" name="radioShiftOver" id="radioShiftOver2"/><br>
                        	  
                        	  
                        	  <label> Minutes difference </label>
                        	 <input class="form-control" type="number" ng-model="handOverMinutesDiffernce"/>
                        </div>
                        
                        <div class="col-xs-12"  ng-show="frequencyCodeAction=='WEEKLY'">
                           <div class="col-xs-4">
						       	<label>Weekly</label>
						        <select class="form-control" ng-model="selectWeeklyDay" ng-options="weeklyDayDatas.name for weeklyDayDatas in weeklyDayType track by weeklyDayDatas.name" ng-change="showWeeklyDayRecords(selectWeeklyDay)"></select>
					       </div>
					       
					      <div class="col-xs-4">
                        <label>Hours of the day </label>
					            <select class="form-control" ng-model="selectWeeklyHoursTime" ng-options="hoursTypes.name for hoursTypes in hoursType track by hoursTypes.name" ng-change="showWeeklyTime(selectWeeklyHoursTime)"></select>
					    </div>
					    
					    <div class="col-xs-2" style="padding:25px">
                            	<button class="btn btn-success" ng-click="addWeeklyHoursRecords()"><i class="fa fa-plus fw"></i></button>
                         </div>
                            
                       <div class="col-xs-12">
                           <table class="table table-striped table-bordered">
                    <tr style="background-color:"grey">
                        <th>S.No &nbsp;</th>
                        <th>Weekly Name&nbsp;</th>
                        <th>Time&nbsp;</th>
                        <th>Remove&nbsp;</th>
                      
                    </thead>
                <tbody>
                    <tr ng-repeat="weeklyNameShow in weekList">
                      <!--  <td>{{weekList}}</td> -->
                       <td>{{$index+1}}</td>
                       <td>{{weeklyNameShow.weeklyName}}</td>
                       <td>
                       	<div ng-repeat="innerItem in weeklyNameShow"> 
                       	<div ng-repeat="codsesList in innerItem">{{codsesList.code}} </div>
                       </div>
                        </td>
                       <td><button class="btn btn-danger btn-xs" ng-click="removeDaysHours(weeklyNameShow.weeklyName)"><i class="fa fa-close fw"></i></button></td>
                    </tr>
                </tbody></table>
                           </div> 
                            
                        </div>
                        
                        
                         <div class="col-xs-12"  ng-show="frequencyCodeAction=='MONTHLY'">
                           
                           <div class="col-xs-2">
                           <label> Month end</label>
                           <input type="checkbox" ng-model='monthlyEnd' style="width:20px; height:20px"  ng-click="checkMonthEnd(monthlyEnd)"></div>
                           <div class="col-xs-4" ng-show="monthEndCheck!='Activate'">
						       	<label>Monthly</label>
						       <select class="form-control" ng-model="selectMonthDate" ng-options="monthDayType as monthDayType for monthDayType in MonthDayData track by monthDayType" ng-change="showC(selectMonthDate)" ></select>
					       </div>
					       
					       
					      <div class="col-xs-4">
                        <label>Hours of the day </label>
					            <select class="form-control" ng-model="selectMonthlyHoursTime" ng-options="hoursTypes.name for hoursTypes in hoursType track by hoursTypes.name" ng-change="showMonthlyTime(selectMonthlyHoursTime)"></select>
					    </div>
					    
					    <div class="col-xs-2" style="padding:25px">
                            	<button class="btn btn-success" ng-click="addMonthlyHoursRecords()"><i class="fa fa-plus fw"></i></button>
                         </div>
                            
                       <div class="col-xs-12">
                           <table class="table table-striped table-bordered">
                    <tr style="background-color:"grey">
                        <th>S.No &nbsp;</th>
                        <th>Monthly Name&nbsp;</th>
                        <th>Time&nbsp;</th>
                        <th>Remove&nbsp;</th>
                      
                    </thead>
                <tbody>
                    <tr ng-repeat="monthlyNameShow in monthList">
                       <td>{{$index+1}}</td>
                       <td>{{monthlyNameShow.monthlyCode}}</td>
                       <td>
                       	<div ng-repeat="innerItem in monthlyNameShow"> 
                       	<div ng-repeat="codsesList in innerItem">{{codsesList.code}} </div>
                       </div>
                        </td>
                       <td><button class="btn btn-danger btn-xs" ng-click="removeMonthHours(monthlyNameShow.monthlyCode)"><i class="fa fa-close fw"></i></button></td>
                    </tr>
                </tbody></table>
                           </div> 
                            
                            
                            
					      <!--  <div class="col-xs-2" style="padding:25px">
                            	<button class="btn btn-success" ng-click="addWeeklyDays()"><i class="fa fa-plus fw"></i></button>
                            </div>
                            
                            <div class="row">
                            <div class="col-xs-12">
                            	<ul><li ng-repeat="weeklyDaysList in weeklyDaysListDetails">
                               	{{weeklyDaysList.name}} 
                                	<button class="btn btn-danger btn-xs" ng-click="f(weeklyDaysList)"><i class="fa fa-close fw"></i></button>
                                </li></ul>
                            </div>
                        </div> -->
					       
                        </div>
                        
                        
                        
                        
                      <!-- <div class="row" ng-show="frequencyCodeAction=='MONTHLY'">
     			  		<div >
     			  		<label> Month end</label>
     			  		 <input type="checkbox" ng-model='monthlyEnd' style="width:20px; height:20px"  ng-click="checkMonthEnd(monthlyEnd)">
     			  		 
     			  	<div ng-show="monthEndCheck!='Activate'" >		 
 			  			<select class="form-control" ng-model="myselectDate" ng-options="monthDayType as monthDayType for monthDayType in MonthDayData track by monthDayType" ng-change="showC(myselectDate)" >
 			  		</select>
			  		</div>
						select ng-model="myselectDate" class="form-control">
				        <option ng-repeat="item in MonthDayData" value="{{item}}">{{item}}</option>
				      </select>
				  		
				  		</div>
    		  			
    		  			<div class="col-xs-2" style="padding:25px" ng-if="monthEndCheck!='Activate'">
                            	<button class="btn btn-success" ng-click="addDate()"><i class="fa fa-plus fw"></i></button>
                            </div>
                            
                            <div class="row">
                            <div class="col-xs-12">
                            	<ul><li ng-repeat="monthDaysList in MonthDayDatas">
                                	{{monthDaysList}} 
                                	<button class="btn btn-danger btn-xs" ng-click="removeDate(monthDaysList)"><i class="fa fa-close fw"></i></button>
                                </li></ul>
                            </div>
                        </div>
                            
                       <div class="col-xs-10">
                        <label>Hours of the day </label>
					            <select class="form-control" ng-model="selecthoursType" ng-options="hoursTypes.name for hoursTypes in hoursType track by hoursTypes.name" ng-change="showHours(selecthoursType.code)"></select>
					    </div>
					    
							<div class="col-xs-2" style="padding:25px">
                            	<button class="btn btn-success" ng-click="addHours()"><i class="fa fa-plus fw"></i></button>
                            </div>
						<div class="row">
                            <div class="col-xs-12">
                            	<ul><li ng-repeat="hoursOftheDayList in hoursOftheDay">
                               	{{hoursOftheDayList}} 
                                	<button class="btn btn-danger btn-xs" ng-click="removeHours(hoursOftheDayList)"><i class="fa fa-close fw"></i></button>
                                </li></ul>
                            </div>
                        </div>
					    
                        </div> -->
                        <button class="btn btn-primary" type="button" ng-click="selectTab('tab2')">Prev</button>
                        <button class="btn btn-primary" type="button" ng-click="viewAllSummary()">Next</button>
                    </div>
                    <div role="tabpanel" class="tab-pane" ng-class="{active: !tab4}" id="summary">
                    	
                    	
                    	<div class="row">
                        <br><br>
                        <div class="col-xs-12">
                   <table class="table table-striped table-bordered">
                    <tr><th colspan="6" align="center" style="background-color:"grey">Basic Info Details</th></tr>
                    <tr>
                   		<th>	Name&nbsp;			</th>
                        <th>	Description&nbsp;	</th>
                        <th>	Type&nbsp;			</th>
                        <th>	Category&nbsp;		</th>
                        <th>	Station&nbsp;		</th>
                        <th>	Status&nbsp;		</th>
                    </tr>
                    
                     <tr>
                   		<td>{{fullCheckListObj.name}}</td>
                        <td>{{fullCheckListObj.description}}&nbsp;</td>
                        <td>{{fullCheckListObj.type}}&nbsp;</td>
                        <td>{{fullCheckListObj.category}}&nbsp;</td>
                        <td>{{fullCheckListObj.station}}&nbsp;</td>
                        <td>{{fullCheckListObj.status}}&nbsp;</td>
                    </tr>
                    
               </table>
                <table class="table table-striped table-bordered">
		                <tr>
		                        <th>Task</th>
		                        <th>Resource&nbsp;</th>
		                        <th>Observation&nbsp;</th>
		                        <th>Mandatory&nbsp;</th>
		                        <th>Response Type&nbsp;</th>
		                        <th>Ordering&nbsp;</th>
		                        <th>Multi valued&nbsp;</th>
		                        <th>Status&nbsp;</th>
		               </tr>
               
                  <tr ng-repeat="itemsDataShow in fullCheckListObj.items">
                       <td>{{itemsDataShow.step}}				</td>
                       <td>{{itemsDataShow.action}}				</td>
                       <td>{{itemsDataShow.description}}		</td>
                       <td>{{itemsDataShow.mandatory}}			</td>
                       <td>{{itemsDataShow.responseType}}		</td>
                       <td>{{itemsDataShow.ordering}}			</td>
                       <td>{{itemsDataShow.multiValued}}		</td>
                       <td>{{itemsDataShow.status}}				</td>
                    </tr>
                    
                </tbody>
                </table>
                
                
              
                <table class="table table-striped table-bordered">
                    <tr><th colspan="8" align="center" style="background-color:"grey">Schedule List</th></tr>
                    <tr>
                        <th>Do you Need Notification</th> 						<td>	{{fullCheckListObj.schedule.notificationRequired}}	</td>
                        <th>Editable Before Minutes&nbsp;</th> 					<td>	{{fullCheckListObj.schedule.editableBeforeMinutes}}	</td>
                        <th>Do you need Notification Before Minutes&nbsp;</th> 	<td>	{{fullCheckListObj.schedule.notifyBeforeMinutes}}	</td>
                        <th>Frequency&nbsp;</th>								<td>  	{{fullCheckListObj.schedule.frequency}}				</td>
                    </tr>
                       <!--  <td>Opening	</td> <td>Closing	</td><td>Intraday	</td> <td>Weekly	</td><td>Monthly	</td>  -->
					 </tr>
					 
					  <tr ng-show="fullCheckListObj.schedule.frequency=='OPENING'">
                        <td><b>Before Opening         :     &nbsp;</b>{{fullCheckListObj.schedule.opening.beforeOpening }}</td>
	                     <td><b>After Opening		  :     &nbsp;</b>{{fullCheckListObj.schedule.opening.afterOpening }}</td>
	                     <td><b>Minutes Difference	  :     &nbsp;</b>{{fullCheckListObj.schedule.opening.minutesDifference }}</td>
                    </tr>
                    
                     <tr ng-show="fullCheckListObj.schedule.frequency=='CLOSING'">
                        <td><b>Before Closing         :     &nbsp;</b>{{fullCheckListObj.schedule.closing.beforeClosing }}</td>
	                     <td><b>After Closing		  :     &nbsp;</b>{{fullCheckListObj.schedule.closing.afterClosing }}</td>
	                     <td><b>Minutes Difference	  :     &nbsp;</b>{{fullCheckListObj.schedule.closing.minutesDifference }}</td>
                    </tr>
                    
                    <tr ng-show="fullCheckListObj.schedule.frequency=='SHIFT_HANDOVER'">
                        <td><b>Before HandOver        :     &nbsp;</b>{{fullCheckListObj.schedule.shiftHandover.beforeHandover }}</td>
	                     <td><b>After HandOver		  :     &nbsp;</b>{{fullCheckListObj.schedule.shiftHandover.afterHandover }}</td>
	                     <td><b>Minutes Difference	  :     &nbsp;</b>{{fullCheckListObj.schedule.shiftHandover.minutesDifference }}</td>
                    </tr>
                    
                    <tr ng-show="fullCheckListObj.schedule.frequency=='INTRA_DAY'">
                        <td ng-repeat="hoursOftheDayIntraListView in hoursOftheIntraDayListData">{{hoursOftheDayIntraListView.name}} </td>
                    </tr>
                    
                      <tr ng-show="fullCheckListObj.schedule.frequency=='Weekly'">
                        <td><b>Intra Day Hours        :     &nbsp;</b>{{fullCheckListObj.schedule.intraDay.hoursOfTheDay }}</td>
                    </tr>
					 
                </table>
                
                
                 </div>
                 </div>
                      <div class="col-xs-4" align="right" ng-if="ActionData=='add'"><button class="btn btn-primary" type="button" ng-click="addCompleteChecklist(fullCheckListObj)">Final Submit </button></div>
                      <div class="col-xs-4" align="right" ng-if="ActionData=='edit'"><button class="btn btn-primary" type="button" ng-click="editCompleteChecklist(fullCheckListObj)">Update</button></div>
                        <div class="row">
                        	<div class="col-xs-12" ng-if="action=='Activate'"><button class="btn btn-primary pull-right" ng-click="activateEmployeeSubmit()">Activate employee</button></div>
                        	<div class="col-xs-12" ng-if="action=='Edit Mapping'"><button class="btn btn-primary pull-right" ng-click="editEmployeeMappingSubmit()">Update employee units</button></div>
                        </div>
                        <button class="btn btn-primary" type="button" ng-click="selectTab('tab3')">Prev</button>
                    </div>
                  </div>
                </div>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade" id="itemsListModal" tabindex="-1" role="dialog" aria-labelledby="itemsListModalLable">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="itemsListModalLable">Check List Items</h4>
          </div>
          <form name="myForm">
          <div class="modal-body">
            	<table class="table table-striped table-bordered">
                    <thead>
                        <th>S.No &nbsp;</th>
                        <th>Task&nbsp;</th>
                        <th>Resource&nbsp;</th>
                        <th>Observation&nbsp;</th>
                         <th>Mandatory&nbsp;</th>
                        <th>Response Type&nbsp;</th>
                       
                        <th align="center">Status&nbsp;</th>
                    </thead>
                <tbody>
                
                    <tr ng-repeat="itemsDataShow in itemsListDetails">
                       <td>{{$index+1}}</td>
                       <td>{{itemsDataShow.step}}				</td>
                       <td>{{itemsDataShow.action}}				</td>
                       <td>{{itemsDataShow.description}}		</td>
                       <td>{{itemsDataShow.mandatory}}			</td>
                       <td>{{itemsDataShow.responseType}}	</td>
                       <td>
                       <button class="btn btn-primary pull-right" data-toggle="modal" ng-click="updateCheckListItemsStatus('ACTIVE',itemsDataShow._id)" ng-show="itemsDataShow.status=='IN_ACTIVE'">ACTIVE		</button>
                       <button class="btn btn-primary pull-right" data-toggle="modal" ng-click="updateCheckListItemsStatus('IN_ACTIVE',itemsDataShow._id)" ng-show="itemsDataShow.status=='ACTIVE'">DEACTIVATE	</button>
                       </td>
                    </tr>
                </tbody>
                </table>
           
              
          </div>
          </form>
          
          
     </div>
  </div>
</div>


<div class="modal fade" id="scheduleListModal" tabindex="-1" role="dialog" aria-labelledby="scheduleListModalLable">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="scheduleListModalLable">Check Schedule List</h4>
          </div>
         <form name="myForm">
          <div class="modal-body">
            	<table class="table table-striped table-bordered">
                   <thead>
	                   <tr style="margin-bottom: 20px">
	                        <td><b>Notification Required : 	   &nbsp;</b>{{scheduleListDetails.notificationRequired }}</td>
	                        <td><b>NotifyBeforeMinutes   :     &nbsp;</b>{{scheduleListDetails.notifyBeforeMinutes }}</td>
	                        <td><b>EditableBeforeMinutes :     &nbsp;</b>{{scheduleListDetails.editableBeforeMinutes }}</td>
	                        <td><b>Frequency             :     &nbsp;</b>{{scheduleListDetails.frequency }}</td>
	                   </tr>
                   </thead>
                  </table>
                <table class="table table-striped table-bordered">
                    <tr ng-show="scheduleListDetails.frequency=='OPENING'">
                        <td><b>Before Opening         :     &nbsp;</b>{{scheduleListDetails.opening.beforeOpening }}</td>
	                     <td><b>After Closing		  :     &nbsp;</b>{{scheduleListDetails.opening.afterOpening }}</td>
	                     <td><b>Minutes Difference	  :     &nbsp;</b>{{scheduleListDetails.opening.minutesDifference }}</td>
                    </tr>
                    
                    <tr ng-show="scheduleListDetails.frequency=='CLOSING'">
                        <td><b>Before Closing         :     &nbsp;</b>{{scheduleListDetails.closing.afterClosing }}</td>
	                     <td><b>After Closing		  :     &nbsp;</b>{{scheduleListDetails.closing.beforeClosing }}</td>
	                     <td><b>Minutes Difference	  :     &nbsp;</b>{{scheduleListDetails.closing.minutesDifference }}</td>
                    </tr>
                    
                    <tr ng-show="scheduleListDetails.frequency=='INTRA_DAY'">
                        <td><b>	Intra day        	  :     &nbsp;</b>{{scheduleListDetails.notificationRequired }}</td>
	                     <td><b>Hours of the day      :     &nbsp;</b>{{scheduleListDetails.notifyBeforeMinutes }}</td>
	                     
                    </tr>
                    
                    <tr ng-show="scheduleListDetails.frequency=='SHIFT_HANDOVER'">
                        <td><b>	Shift Handover        	 </td>
	                    <td><b> Before Handover       	:     &nbsp;</b>{{scheduleListDetails.shiftHandover.beforeHandover }}</td>
	                    <td><b> After Handover       	:     &nbsp;</b>{{scheduleListDetails.shiftHandover.afterHandover }}</td>
	                     <td><b>Minutes Difference      :     &nbsp;</b>{{scheduleListDetails.shiftHandover.minutesDifference }}</td>
                    </tr>
                    
                    <tr ng-show="scheduleListDetails.frequency=='WEEKLY'">
                        <td><b>	Weekly        	 		</td>
	                    <td><b> Days of Week       		:     &nbsp;</b>{{scheduleListDetails.notifyBeforeMinutes }}</td>
	                    <td><b> Hours of the Day      	:     &nbsp;</b>{{scheduleListDetails.notifyBeforeMinutes }}</td>
                    </tr>
                    
                     <tr ng-show="scheduleListDetails.frequency=='MONTHLY'">
                        <td><b>	Monthly        	 		</td>
	                    <td><b> Days of Month       	:     &nbsp;</b>{{scheduleListDetails.monthly }}</td>
	                    <td><b> Hours of the Day      	:     &nbsp;</b>{{scheduleListDetails.notifyBeforeMinutes }}</td>
                    </tr>
                </table>
          	</div>
          </form>
          
          
     </div>
  </div>
</div>


