<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	<h1 class="page-header">
     Category List            
    </h1>
    </div>
     <div class="form-group">
                <label>Category List *</label>
                <select class="form-control" ng-model="selectedCatList" ng-options="categoryName for (categoryName,categoryList) in categoryLists" ></select> 
     </div>
      <div class="form-group">
                <label>Detail Name *</label>
          <select class="form-control"  ng-model="selectedDetailName" ng-options="selectedCat as selectedCat.detail.name for selectedCat in selectedCatList track by selectedCat.detail.id" ng-change="showDetailsData(selectedDetailName.detail.name)" required >
          </select> 
     </div>
    <div style="text-align:right"> 
      <img ng-click="addCategoryDetails(detLists.detail)" style="margin-bottom:8px; cursor:pointer" title="Add Category" ng-src="img/addCat.png" height="20px" width="20px">Add&nbsp;&nbsp;
      <img ng-click="openAddPropertyModal(detLists)" style="margin-bottom:8px;cursor:pointer;margin-left: 10px;" title="Add Category Properties" ng-src="img/property.png" height="28px" width="28px">&nbsp;&nbsp;
      <img ng-click="openUploadImageModal(detLists.content)" style="margin-bottom:8px;cursor:pointer;margin-left: 10px;" title="Upload Category Image" ng-src="img/uploadImage.png" height="28px" width="28px">&nbsp;&nbsp;
    </div>
     <div>
     <table cellpadding="0" border="1" cellspacing="0"  class="table table-hover table-striped">
        <tr id="heading">
         <th> S.No</th>
          <th> Name</th>
          <th>Code</th>
          <th>Short Code</th>
          <th>Type</th>
          <th>Status</th>
           <th>Action</th>
        </tr>
        <tr ng-repeat="detDatas in detLists.content">
         <td>{{$index +1}} </td>
          <td>{{detDatas.name}} </td>
          <td>{{detDatas.code}}</td>
          <td>{{detDatas.shortCode}}</td>
          <td>{{detDatas.type}}</td>
          <td>{{detDatas.status}} </td>
          <td> <img ng-click="activeNinactive(detDatas.id,'IN_ACTIVE')" ng-if="detDatas.status!='IN_ACTIVE'" style="margin-bottom:8px; cursor:pointer" title="Active" ng-src="img/activeCat.png" height="25px" width="25px">&nbsp;&nbsp;
          		<img ng-click="activeNinactive(detDatas.id,'ACTIVE')" ng-if="detDatas.status!='ACTIVE'" style="margin-bottom:8px; cursor:pointer" title="Active" ng-src="img/inactiveCat.png" height="25px" width="25px">&nbsp;&nbsp;
          		<img ng-click="updateCategoryDetails(detDatas.id)" style="margin-bottom:8px;cursor:pointer;margin-right:5px" title="Edit Category" ng-src="img/updateCat.png" height="20px" width="20px">&nbsp;&nbsp;
           </td>
        </tr>
      </table>
      </div>
     
     <form name="myForms" novalidate>
     <div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"> {{msg}}  Category Details</h4>
          </div>
          <div class="modal-body">
              <form name="addCatForm" novalidate>
              <div class="form-group">
                  <label> Name *</label>
                  <input type="text" class="form-control" ng-model="catName" required />
              </div>
               <div class="form-group">
                  <label> Code *</label>
                  <input type="text" class="form-control"  ng-model="catCode" required />
              </div>
              <div class="form-group">
                  <label> Short Code *</label>
                  <input type="text" class="form-control" maxlength="4" ng-model="catShortCode" required />
              </div>
               <div class="form-group clearfix" ng-if="action=='AddCatDet'">
                  <button class="btn btn-primary pull-right" ng-click="submitAddCategory()">Add</button>
              </div>
             <div class="form-group clearfix" ng-if="action=='UpdateCatAction'">
                  <button class="btn btn-primary pull-right" ng-click="submitUpdateCategory(updateIDS)">Update</button>
              </div>
              </form>
          </div>
     </div>
  </div>
</div>
</form>
</div>

<div class="modal fade" id="categoryPropertiesModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel"> {{msg}} Update Category Properties</h4>
      </div>
      <div class="modal-body">
        <form name="addCatPropertieForm" novalidate>
          <div class="form-group">
            <label style="font-size: 18px;"> Add Default Visibility </label><br/>
            <table cellpadding="0" border="1" cellspacing="0" class="table table-hover table-striped">
              <tr id="heading">
                <th>Category Name</th>
                <th>Visibility</th>
                <th>Image</th>
              </tr>
              <tr ng-repeat="detDatas in detLists.content">
                <td style="width: 50%;">{{detDatas.name}} </td>
                <td style="width: 20%;">
                  <input type="checkbox" class="checkbox" ng-model="detDatas.defaultVisibility" data-ng-click="selectVisibility(detDatas,detDatas.defaultVisibility)">
                </td>
                <td style="align-content: center;width: 48%;">
                  <!-- <div style="display:grid; grid-template-columns: 2fr 1fr;box-sizing: border-box;"> -->
                  <!-- <input class="btn btn-default" file-model="fileUploaded[detDatas.id].tagImageUrl" type="file"> -->
                    <img src="img/viewEye.png" data-ng-click="showImageModal(detDatas.id)" style="padding-left: 35px;">
                    </div>
                </td>
              </tr>
            </table>
          </div>
          <div style="border-bottom: 1px solid #6e6b6b;margin: 20px 0;"></div>
          <br/>
          <div data-ng-if="!removeSequencing" class="form-group">
                <div style="display: flex;">
                  <label style="font-size: 18px;"> Add Sequence </label><br />
                  <button class="btn btn-danger pull-right" style="height: 30px; margin-left: 60%;display: block;" data-ng-disabled="removeSequencing"
                    data-ng-click="removeCategorySequencing(detLists.content)">Remove</button>
                </div>
                <button class="btn btn-primary" ng-repeat="detDatas in detLists.content" style="margin: 5px;" data-ng-disabled="detDatas.clicked"
                      data-ng-click="addPropertySequence(detDatas,detLists.detail.id,true)">{{detDatas.name}}</button>
          </div>
          <div data-ng-if="catSequence[detLists.detail.id] != null" class="form-group">
            <br/>
            <button class="btn btn-danger pull-right" style="height: 30px;" data-ng-click="clearSequenceData(detLists.content)">Clear</button>
            <label style="font-size: 15px;"> Sequencing </label><br/>
              <div ng-repeat="sequenceData in catSequence[detLists.detail.id]" style="display: flex;text-align: center">
                <text data-ng-if="sequenceData.number > 0 " style="margin: 5px;text-align: center;display: block;">{{sequenceData.number}}.  {{sequenceData.name}}</text>
                <i data-ng-if="sequenceData.number > 0" class="fa fa-times" aria-hidden="true" style="color: red;margin-top: 8px;" data-ng-click="removeCategoryFromSequence(sequenceData,detLists.detail.id)"></i>
              </div>  
          </div>
        </form>
      </div>
      <div class="modal-footer">
          <button class="btn btn-primary pull-right" data-ng-click="updateCategoryProperties()">Update Properties</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="uploadImageModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel"> Upload Category Images</h4>
      </div>
      <br/>
      <div class="modal-body">
        <form name="addCatPropertieForm" novalidate>
          <div class="form-group">
            <table cellpadding="0" border="1" cellspacing="0" class="table table-hover table-striped">
              <tr id="heading">
                <th>Category Name</th>
                <th>Action</th>
              </tr>
              <tr ng-repeat="detDatas in detLists.content">
                <td style="width: 50%;">{{detDatas.name}} </td>
                <td style="align-content: center;width: 50%;">
                    <input class="btn btn-default" file-model="fileUploaded" det-data="detDatas.id"  type="file">
                </td>
              </tr>
            </table>
          </div>
          <br />
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary pull-right" data-ng-click="uploadImages()">Upload</button>
      </div>
    </div>
  </div>
</div>

   