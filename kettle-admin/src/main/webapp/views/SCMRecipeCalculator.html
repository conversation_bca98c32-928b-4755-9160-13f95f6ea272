<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->


<div data-ng-show="!iterationPageDetails.isShowDetails">
	<div class="row">
		<div class="col-lg-12">
			<br>
			<h1 class="page-header">Recipe Calculator</h1>
		</div>
	</div>


	<div class="form-group">
		<ul class="nav nav-tabs">
			<li data-ng-class="{active : activeIteration =='existingConstruct'}"
				data-ng-click="changeIterationType('existingConstruct')"><a
				href=""><label>Existing Construct</label></a></li>
			<li data-ng-class="{active : activeIteration =='existingProduct'}"
				data-ng-click="changeIterationType('existingProduct')"><a
				href=""> <label>Existing Product</label></a></li>
		</ul>
	</div>
	<div data-ng-if="activeIteration == 'existingProduct'">
		<div class="row top-buffer-row">
			<div class="col-lg-6">
				<select ui-select2 class="form-control"
					data-ng-model="selectedProductId"
					data-placeholder="Select a product"
					data-ng-change="changeProduct(selectedProductId)">
					<option value=""></option>
					<option
						data-ng-repeat="product in scmProductsInfo  | filter : filterByCategory"
						value="{{product.productId}}">{{product.productName}} ({{product.category.code}})  </option>
				</select>
			</div>
			<div class="col-lg-2">
				<div class="row divInnerRow">
					<div class="col-xs-8">
						<label>Fetch Decomissoned</label>
					</div>
					<div class="col-xs-4">
						<input type="checkbox" data-ng-model="isDecomissoned" ng-click="changeStatus(isDecomissoned)"
							   class="center-block" style="height: 2em; width: 2em;"/>
					</div>
				</div>
			</div>
			<div class="col-lg-2">
				<Button class="btn btn-primary"
					data-ng-click="getArchivedSCMIterationsForProduct(selectedProductId)">Get
					Archived Iteration</Button>
			</div>
			<div class="col-lg-2">
				<Button class="btn btn-primary"
						acl-action="CSCMRM"
					data-ng-click="createIterationFromProduct(selectedProductId,'')">Create
					Iteration</Button>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-6">
				<select ui-select2 class="form-control"
						data-ng-model="iterationByProfile"
						data-placeholder="Select a profile"
						data-ng-change="changeSelectedProfile(iterationByProfile)">
					<option value=""></option>
					<option
							data-ng-repeat="profile in availableProfiles"
							value="{{profile}}">{{profile}}</option>
				</select>
			</div>
			<div class="col-lg-2">
				<!--<input type="checkbox" id="Decomissioned" ng-model="isDecomissoned" ng-click="filterDecomissioned(selectedProductId)"> Fetch Decomissoned</input>-->
				<Button class="btn btn-primary"
						acl-action="CSCMRM"
						data-ng-click="getIterationForProduct(selectedProductId)">Get
					Iteration</Button>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<table class="table table-bordered table-striped">
					<tr data-ng-if="iterationList.length>0">
						<td>Iteration Name</td>
						<td>Created By</td>
						<td>Creation Date</td>
						<td>Last Modified By</td>
						<td>Last Modified Date</td>
						<td>Status</td>
						<td>Profile</td>
						<td>Action</td>
						<td>Reviews</td>
					</tr>
					<tr data-ng-if="iterationList.length==0 && showEmptyMsg">
						<td class="col-lg-12 alert alert-danger"><label>This
								list is empty.</label></td>
					</tr>
					<tr data-ng-repeat="iteration in iterationList| filter : filterByProfile">
						<td class="recipe-name" data-ng-click="viewIterationDetails(iteration)">{{iteration.iterationName}}</td>
						<td>{{iteration.createdByName}}</td>
						<td>{{iteration.creationDate | date : "dd-MM-yyyy"}}</td>
						<td>{{iteration.lastUpdatedByName}}</td>
						<td>{{iteration.modificationDate | date : "dd-MM-yyyy"}}</td>
						<td>{{iteration.status}}</td>
						<td>{{iteration.profile}}</td>
						<td><Button class="btn btn-warning btn-sm"
								data-ng-click="iterationActions(iteration,'APPROVED','existingProduct')"
                                    acl-action="ADRAR"
								data-ng-if="iteration.status=='CREATED'">Approve</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="iterationActions(iteration,'IMPROVED','existingProduct')"
								data-ng-if="iteration.status=='CREATED'">Improve</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="iterationActions(iteration,'ARCHIVED','existingProduct')"
								data-ng-if="iteration.status=='CREATED' || iteration.status=='IMPROVED' ||  iteration.status=='APPROVED' || iteration.status=='INITIATED'">Archive</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="createIterationFromProduct(selectedProductId,iteration)"
								data-ng-if="iteration.status=='INITIATED'">Edit</Button>
							<!--<Button class="btn btn-warning btn-sm"-->
								<!--data-ng-click="cloneAction(iteration,'existingProduct')"-->
									<!--acl-action="CSCMRM"-->
								<!--data-ng-if="iteration.status=='CREATED'  || iteration.status=='ARCHIVED' ||  iteration.status=='IMPROVED' || iteration.status=='APPROVED'">Clone</Button>-->
							<Button class="btn btn-warning btn-sm"
									data-target="#improveOrCreateModal"
									acl-action="CSCMRM"
									data-toggle="modal"
									data-ng-click="cloningOptions(iteration,'existingProduct')"
									data-ng-if="iteration.status=='CREATED'  || iteration.status=='ARCHIVED' ||  iteration.status=='IMPROVED' || iteration.status=='DECOMISSONED' || iteration.status=='APPROVED'">Clone</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="openRecipeIterationCostModal(iteration)"
									acl-action="CSCMRM"
								data-ng-if="iteration.status=='CREATED' ||  iteration.status=='IMPROVED' ||  iteration.status=='APPROVED'">Cost</Button></td>
						<td>{{iteration.review.reviewContent}}
						<Button class="btn btn-info btn-sm"
								data-ng-click="openReviewDetailsModal(iteration,iteration.review)"
								data-ng-if="iteration.review.reviewContent.length>0">Details</Button>
							<Button class="btn btn-warning btn-sm"
									data-target="#updateCommentModal"
									acl-action="CSCMRM"
									data-toggle="modal"
									data-ng-click="openCommentUpdateModal(iteration)"
									data-ng-if="iteration.status=='APPROVED'">Update Comments</Button>
							<Button class="btn btn-warning btn-sm"
									data-target="#uploadImageModal"
									acl-action="CSCMRM"
									data-toggle="modal"
									data-ng-click="openUploadImageModal(iteration)"
									data-ng-if="iteration.status=='APPROVED'">Upload image</Button>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
	<div data-ng-if="activeIteration == 'existingConstruct'">
		<div class="row top-buffer-row">
			<div class="col-lg-4">
				<select ui-select2 class="form-control"
					data-ng-model="selectedConstructName"
					data-placeholder="Select a construct">
					<option value=""></option>
					<option
						data-ng-repeat="construct in scmConstructsInfo"
						value="{{construct.linkedConstructName}}">{{construct.linkedConstructName}}</option>
				</select>
			</div>
			<div class="col-lg-2">
				<Button class="btn btn-primary"
						acl-action="CSCMRM"
					data-ng-click="getIterationForConstruct(selectedConstructName)">Get
					Iteration</Button>
			</div>
			<div class="col-lg-2">
				<Button class="btn btn-primary"
					data-ng-click="archivedIterationsForConstruct(selectedConstructName)">Get
					Archived Iteration</Button>
			</div>
			<div class="col-lg-2">
				<Button class="btn btn-primary" data-ng-click="createNewConstruct()">Create
					Construct</Button>
			</div>
			<div class="col-lg-2">
				<Button class="btn btn-primary"
						acl-action="CSCMRM"
					data-ng-click="createIterationFromConstruct(selectedConstructName)">Create
					Iteration</Button>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<table class="table table-bordered table-striped">
					<tr data-ng-if="iterationList.length>0">
						<td>Iteration Name</td>
						<td>Created By</td>
						<td>Creation Date</td>
						<td>Last Modified By</td>
						<td>Last Modified Date</td>
						<td>Status</td>
						<td>Action</td>
						<td>Reviews</td>
					</tr>
					<tr data-ng-if="iterationList.length==0 && showEmptyMsg">
						<td class="col-lg-10 alert alert-danger"><label>This
								list is empty.</label></td>
					</tr>
					<tr data-ng-repeat="iteration in iterationList">
						<td data-ng-class="{'recipe-name': iteration.status!='INITIATED'}" data-ng-click="viewIterationDetails(iteration)">{{iteration.iterationName}}</td>
						<td>{{iteration.createdByName}}</td>
						<td>{{iteration.creationDate | date : "dd-MM-yyyy"}}</td>
						<td>{{iteration.lastUpdatedByName}}</td>
						<td>{{iteration.modificationDate | date : "dd-MM-yyyy"}}</td>
						<td>{{iteration.status}}</td>
						<td><Button class="btn btn-warning btn-sm"
                                    acl-action="ASCMRM"
								data-ng-click="iterationActions(iteration,'APPROVED','existingConstruct')"
								data-ng-if="iteration.status=='CREATED' && currentUserId !=iteration.createdById && currentUserId !=iteration.lastUpdatedById">Approve</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="iterationActions(iteration,'IMPROVED','existingConstruct')"
								data-ng-if="iteration.status=='CREATED'">Improve</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="iterationActions(iteration,'ARCHIVED','existingConstruct')"
								data-ng-if="iteration.status=='CREATED' ||  iteration.status=='IMPROVED' ||  iteration.status=='APPROVED' || iteration.status=='INITIATED' ">Archive</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="createIterationFromConstruct(selectedConstructName,iteration)"
								data-ng-if="iteration.status=='INITIATED'">Edit</Button>
							<!--<Button class="btn btn-warning btn-sm"-->
								<!--data-ng-click="cloneAction(iteration,'existingConstruct')"-->
									<!--acl-action="CSCMRM"-->
								<!--data-ng-if="iteration.status=='CREATED'  || iteration.status=='ARCHIVED' ||  iteration.status=='IMPROVED' ||  iteration.status=='APPROVED'">Clone</Button>-->
							<Button class="btn btn-warning btn-sm"
									data-target="#improveOrCreateModal"
									acl-action="CSCMRM"
									data-toggle="modal"
									data-ng-click="cloningOptions(iteration,'existingConstruct')"
									data-ng-if="iteration.status=='CREATED'  || iteration.status=='ARCHIVED' ||  iteration.status=='IMPROVED' ||iteration.status=='DECOMISSONED' || iteration.status=='APPROVED'">Clone</Button>
							<Button class="btn btn-warning btn-sm"
								data-ng-click="openRecipeIterationCostModal(iteration)"
									acl-action="CSCMRM"
								data-ng-if="iteration.status=='CREATED' ||  iteration.status=='IMPROVED' ||  iteration.status=='APPROVED'">Cost</Button></td>
						<td>{{iteration.review.reviewContent}}
						<Button class="btn btn-info btn-sm"
								data-ng-click="openReviewDetailsModal(iteration,iteration.review)"
								data-ng-if="iteration.review.reviewContent.length>0">Details</Button>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>
<div ui-view=""></div>


<div class="modal fade" id="improveOrCreateModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">Clone Iteration</h4>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-xs-12">
						<h4>Cloning product iteration</h4>
						<div class="row" style="padding:10px 0">
							<div class="col-xs-4">{{iteration.iterationName}}</div>
							<div class="col-xs-4">{{iteration.profile}}</div>
							<div class="col-xs-4">{{iteration.status}}</div>
						</div>
						<div class="row">
							<div class="col-xs-12">
								<select ng-model="selectedProfile" class="form-control"
										data-placeholder="Select Profile" ng-change="changeProfile(selectedProfile)">
									<option ng-repeat="profile in recipeProfiles">{{profile.name}}</option>
								</select>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-12">
								<button class="btn btn-warning btn-sm" data-dismiss="modal"
										data-ng-click="cloneAction(iteration,type)"
										acl-action="CSCMRM">Create Iteration
								</button>
							</div>
						</div>
						<div class="modal-footer">
							<button class="modal-action modal-close waves-effect waves-green btn-flat" data-dismiss="modal">Close</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="updateCommentModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title"> Update Iteration Comment</h4>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-xs-12">
						<!--<h4>Update Recipe comment</h4>-->

						<div class="row top-buffer-row">
							<label for="comment">Add and Update Comment:</label>
							<textarea data-ng-model="iteration.notes" class="form-control"
									  rows="5" id="comment"></textarea>
						</div>
						<div class="d-flex justify-content-center">
								<button class="btn btn-warning btn-sm" data-dismiss="modal"
										data-ng-click="updateIterationComment(iteration)"
										acl-action="CSCMRM">Update Comment
								</button>
								<button class="btn btn-warning btn-sm"
										data-ng-click="openHindiEditor()"
										acl-action="CSCMRM">Hindi Editor
								</button>
						</div>
						<div class="modal-footer">
							<button class="modal-action modal-close waves-effect waves-green btn-flat" data-dismiss="modal">Close</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!--Upload Image Modal-->

<div aria-labelledby="AddImageSCMRecipe" class="modal fade" id="uploadImageModal" role="dialog"
	 data-keyboard="false" data-backdrop="static"
	 tabindex="-1">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button aria-label="Close" class="close" data-dismiss="modal" ng-click="reset()" type="button"><span
						aria-hidden="true">&times;</span></button>
				<div>
					<h4 class="modal-title" id="myModalLabel_add">Upload Image</h4>
				</div>
			</div>
			<div class="modal-body">
				<form name="addNewImage" novalidate>
					<fieldset>
						<div class="form-group">
							<label>Content Type</label>
							<select
									class="form-control"
									data-ng-model="selectedContentType"
									placeholder="select options">
								<option ng-repeat="type in contentType">{{type}}</option>
							</select>
						</div>
						<div class="form-group">
							<label> Image </label>
							<div class="row">
								<div class="col-lg-8">
									<input class="btn btn-default"
										   id ="scmRecipeImages"
										   style="width: 100%;"
										   type="file"
										   name="files"
										   multiple>
								</div>
							</div>
						</div>
						<div>
							<div class="form-group clearfix">
								<button class="btn btn-primary pull-left"
										ng-click="reset()">
									Reset
								</button>
								<button class="btn btn-primary pull-right"
										ng-click="uploadSCMImage(iteration)"
										data-dismiss="modal">
									Upload
								</button>
							</div>
						</div>
					</fieldset>
				</form>
			</div>
		</div>
	</div>
</div>

<script type="text/ng-template" id="newConstruct.html">
<style>
.popeye-modal-container .popeye-modal{
    width: 60%;
}
.alert-danger {
   margin-left: 15px!important;
}
.alert-success {
   margin-left: 15px!important;
}
</style>
<div>

	<form name="iterationCreateForm" novalidate>
		<div class="row">
			<div class="row top-buffer-row">
				<div class="col-lg-3">
					<label>Construct Name</label>
				</div>
				<div class="col-lg-6">
					<input class="form-control" type="text"
						data-ng-model="iterationDetails.linkedConstructName"
						name="iterationName" required
						data-ng-change="updateCosntructName()" />
				</div>
				<div class="col-lg-3">
					<span class="alert alert-danger"
						data-ng-if="iterationCreateForm.iterationName.$error.required">Required</span>
				</div>
				<div class="col-lg-3">
					<span class="btn btn-primary"
						data-ng-if="!iterationCreateForm.iterationName.$error.required"
						data-ng-click="validateConstructName(iterationDetails.linkedConstructName)">Verify</span>
				</div>
			</div>
			<div class="row top-buffer-row"
				data-ng-if="!iterationCreateForm.iterationName.$error.required && isNameVerified">
				<div class="col-lg-8 alert alert-danger"
					data-ng-if="!isNameAvailable">
					<label>This name is already used.</label>
				</div>
				<div class="col-lg-8 alert alert-success"
					data-ng-if="isNameAvailable">
					<label>This name is ready to be used by you.</label>
				</div>
			</div>
			<div class="row top-buffer-row">
				<div class="col-lg-3">
					<label>Iteration Output Uom</label>
				</div>
				<div class="col-lg-6">
					<select class="form-control"
						data-ng-change="changeOutputUom(outputUom)"
						data-ng-model="outputUom"
						data-ng-options="outputUom as outputUom.name for outputUom in subUomNameList"></select>
				</div>
			</div>
			<div class="row top-buffer-row">
				<div class="col-lg-3">
					<label>Iteration Product Uom</label>
				</div>
				<div class="col-lg-6">
					<select class="form-control"
						data-ng-change="changeProductUom(productUom)"
						data-ng-model="productUom" data-ng-disabled="outputUom.name=='PC'"
						data-ng-options="productUom as productUom.name for productUom in subUomNameList"></select>
				</div>
			</div>
			<div class="row top-buffer-row">
				<div class="col-lg-3">
					<label>Construct Output Uom</label>
				</div>
				<div class="col-lg-3">
					<input class="form-control" type="number"
						data-ng-model="conversionUom" name="conversionUom" required
						data-ng-change="updateOutputUom(conversionUom)" />
				</div>
				<div class="col-lg-3">
					<label>Construct Product Uom</label>
				</div>
				<div class="col-lg-3">
					<label>{{iterationDetails.productConversion}}
						{{iterationDetails.productUom}}</label>
				</div>
			</div>
			<div class="row top-buffer-row"
				data-ng-if="iterationDetails.productUom.name=='PC' && iterationDetails.outputUom.name=='GM'">
				<span class="alert alert-danger"
					data-ng-show="iterationCreateForm.conversionUom.$error.required">
					Conversion Ratio is Required</span>
			</div>
			<div class="row top-buffer-row">
				<div class="col-lg-6">
					<input type="submit" class="btn btn-primary"
						value="Create Construct"
						data-ng-disabled="iterationCreateForm.$invalid" data-ng-click="createConstruct(iterationCreateForm.$valid)">
				</div>
				<div class="col-lg-6">
					<span class="btn btn-danger pull-right"
						data-ng-click="closeModal()">Close</span>
				</div>
			</div>
		</div>
	</form>
</div>
</script>


<script type="text/ng-template" id="reviewModal.html">
<style>
.popeye-modal-container .popeye-modal{
  width: 70%;
}
</style>
<div class="modal-content">
	<div class="modal-header">
		<div class="row">
			<div class="col-lg-12">
				<h1 class="page-header">Review Details</h1>
			</div>
		</div>
	</div>
	<div class="modal-body">
		<div class="row top-buffer-row">
			<div class="col-lg-3">
				 <label>Iteration Parent Name </label>
			</div>
			<div class="col-lg-3" data-ng-if="iterationDetails.linkedProductName && iterationDetails.linkedConstructName == null">
					<label>{{iterationDetails.linkedProductName}}</label>
			</div>
			<div class="col-lg-3" data-ng-if="iterationDetails.linkedConstructName">
					<label>{{iterationDetails.linkedConstructName}}</label>
			</div>
			<div class="col-lg-3">
				 <label>Iteration Name </label>
			</div>
			<div class="col-lg-3">
					<label>{{iterationDetails.iterationName}}</label>
			</div>
		</div>

		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<table class="table table-bordered table-striped">
					<tr>
						<td>Reviewed By</td>
						<td>Date</td>
						<td>Remarks</td>
						<td data-ng-if="iterationDetails.status == 'APPROVED'">Linked Product Id</td>
						<td data-ng-if="iterationDetails.status == 'APPROVED'">Linked Product Name</td>
					</tr>
					<tr>
						<td>{{reviewDetails.reviewedByName}}</td>
						<td>{{reviewDetails.reviewDate | date : "dd-MM-yyyy"}}</td>
						<td>{{reviewDetails.reviewContent}}</td>
						<td data-ng-if="iterationDetails.status == 'APPROVED'">{{iterationDetails.linkedProductId}}</td>
						<td data-ng-if="iterationDetails.status == 'APPROVED'">{{iterationDetails.linkedProductName}}</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-danger" data-ng-click="closeModal()">Close</button>
	</div>
</div>


</script>
