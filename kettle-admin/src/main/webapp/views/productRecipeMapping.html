<!-- Product Recipe Mapping and Product MetaData Mapping -->

<link rel="stylesheet" href="css/product_recipe_mapping_styles.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.2/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<div class="app-container" data-ng-init="init()">
    <!-- Header Section -->
    <header class="header">
        <div class="logo">
            <i class="fa fa-cubes"></i>
            <h1>{{isProductRecipeMapping ? "Product Recipe Management" : "Product Management"}}</h1>
        </div>
        <div class="actions">
            <div class="file-upload">
                <label for="fileUpload" class="bttn btn-upload-excel">
                    <i class="fa fa-upload"></i> Upload Excel
                </label>
                <input id="fileUpload" type="file" file-model="bulkFile" accept=".xlsx, .xls" data-ng-click="removePreviousExcel()">
            </div>
            <button class="bttn btn-success" data-ng-click="uploadBulkSheet()">
                <i class="fa fa-cloud-upload-alt"></i> Preview Sheet
            </button>
        </div>
    </header>

    <!-- after bulk upload verify changes and submit data -->
    <section>
        <custom-modal is-open="showBulkUploadModal" on-submit="submitBulkUploadModal()"
            on-close="closeBulkUploadModal()" title="Verify data and submit" width="100%" height="100%" show-submit="true">
            <div>
                <table class="scrollable-table">
                    <thead>
                        <tr>
                            <th>Unit Name</th>
                            <th>Product Name</th>
                            <th>Dimension</th>
                            <th data-ng-if="isProductRecipeMapping">Old Profile</th>
                            <th data-ng-if="isProductRecipeMapping">New Profile</th>
                            <th data-ng-if="isProductRecipeMapping">Old Status</th>
                            <th data-ng-if="isProductRecipeMapping">New Status</th>
                            <th data-ng-if="isProductRecipeMapping">Old Delivery Only</th>
                            <th data-ng-if="isProductRecipeMapping">New Delivery Only</th>
                            <th data-ng-if="isProductRecipeMapping">Old Pick DineIn Consumables</th>
                            <th data-ng-if="isProductRecipeMapping">New Pick DineIn Consumables</th>
                            <th data-ng-if="!isProductRecipeMapping">Old Dimension Desc</th>
                            <th data-ng-if="!isProductRecipeMapping">New Dimension Desc</th>
                            <th data-ng-if="!isProductRecipeMapping">Old Product Alias</th>
                            <th data-ng-if="!isProductRecipeMapping">New Product Alias</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- data-ng-class="{'row-changed': (true)}" -->
                        <tr data-ng-repeat="item in bulkUploadedData track by $index">
                            <td>{{ item.unitName }} [{{item.unitId}}]</td>
                            <td>{{ item.productName }} [{{item.productId}}]</td>
                            <td>{{ item.dimensionCode }} [{{item.dimensionId}}]</td>
                            <td data-ng-if="isProductRecipeMapping">{{ item.oldRecipeProfile }}</td>
                            <td data-ng-if="isProductRecipeMapping" 
                                data-ng-class="{'field-changed': (isFieldsChanged(item.oldRecipeProfile, item.newRecipeProfile))}">{{ item.newRecipeProfile }}</td>
                            <td data-ng-if="isProductRecipeMapping">{{ item.oldStatus }}</td>
                            <td data-ng-if="isProductRecipeMapping"
                                data-ng-class="{'field-changed': (isFieldsChanged(item.oldStatus, item.newStatus))}">{{ item.newStatus }}</td>
                            <td data-ng-if="isProductRecipeMapping">{{ item.oldDeliveryOnly }}</td>
                            <td data-ng-if="isProductRecipeMapping"
                                data-ng-class="{'field-changed': (isFieldsChanged(item.oldDeliveryOnly, item.newDeliveryOnly))}">{{ item.newDeliveryOnly }}</td>
                            <td data-ng-if="isProductRecipeMapping">{{ item.oldPickDineInConsumables }}</td>
                            <td data-ng-if="isProductRecipeMapping"
                                data-ng-class="{'field-changed': (isFieldsChanged(item.oldPickDineInConsumables, item.newPickDineInConsumables))}">{{ item.newPickDineInConsumables }}</td>
                            <td data-ng-if="!isProductRecipeMapping">{{ item.oldDimensionDesc }}</td>
                            <td data-ng-if="!isProductRecipeMapping"
                                data-ng-class="{'field-changed': (isFieldsChanged(item.oldDimensionDesc, item.newDimensionDesc))}">{{ item.newDimensionDesc }}</td>
                            <td data-ng-if="!isProductRecipeMapping">{{ item.oldProductAlias }}</td>
                            <td data-ng-if="!isProductRecipeMapping"
                                data-ng-class="{'field-changed': (isFieldsChanged(item.oldProductAlias, item.newProductAlias))}">{{ item.newProductAlias }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </custom-modal>
    </section>

    <!-- show export modal -->
    <section>
        <custom-modal is-open="showMsgsModal" on-submit="closeShowMsgModal()" on-close="closeShowMsgModal()"
            title="information" width="60%" height="60%" show-submit="false">
            <table class="scrollable-table">
                <div data-ng-if="successMsgs.length > 0" class="msg-container success-msg">
                    <h4>{{successMsgs.length}} Records successfully updated</h4>
                </div>
                <div data-ng-if="failureMsgs.length > 0" class="msg-container failure-msg">
                    <h4>{{failureMsgs.length}} fields failed to updated</br> Error Messages:</h4>
                    <ul class="msg-list failureMsg-list">
                        <li data-ng-repeat="msg in failureMsgsTrimmed" class="msg-item">
                            <i class="fa fa-times-circle"></i> {{msg}}
                        </li>
                    </ul>
                    <a data-ng-if="failureMsgs.length != failureMsgsTrimmed" data-ng-click="showMoreErrorMsgs()">more...</a>
                </div>
                <div data-ng-if="defaultMsgs.length > 0" class="msg-container default-msg">
                    <h4>Notifications:</h4>
                    <ul class="msg-list defaultMsg-list">
                        <li data-ng-repeat="msg in defaultMsgs" class="msg-item">
                            <i class="fa fa-info-circle"></i> {{msg}}
                        </li>
                    </ul>
                </div>
            </table>
        </custom-modal>
    </section>

    <!-- show msg after submittion -->
    <section>
        <custom-modal is-open="showExportModal" on-submit="closeExportModal()" on-close="closeExportModal()"
            title="Export Modal" width="30%" height="50%" show-submit="false">
            <div>
                <button class="export-all" data-ng-if="selectedRecordsCount > 0"
                    data-ng-click="exportSelectedToExcel()">Export only Selected</button>
                <button class="export-all" data-ng-click="exportFilteredToExcel()">Export only Filtered</button>
                <button class="export-selected" data-ng-click="exportAllToExcel()">Export All Entries</button>
            </div>
        </custom-modal>
    </section>

    <!-- Filters Section -->
    <section class="filters-section">
        <h2><i class="fa fa-filter"></i> Filters</h2>
        <div class="filters-container">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="unitType">Unit Type</label>
                    <select id="unitType" class="form-control" data-ng-model="SelectedUnitType">
                        <option data-ng-repeat="unit in unitType | orderBy" value="{{unit}}">{{unit}}</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="brand">Brand</label>
                    <select id="brand" class="form-control" data-ng-model="selectedBrand"
                        data-ng-change="changeCategoryOnBrand()">
                        <option data-ng-repeat="brand in brandList | orderBy" value="{{brand.id}}">{{brand.name}}
                        </option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <label>Product Category</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForCatDim"
                        options="allCategories" selected-model="selectedCategories" class="multi-select"></div>
                </div>

                <div class="filter-group">
                    <label style="display: inline-flex; align-items: center; gap: 10px;"
                        data-ng-style="{'color': selectedProducts.length === 0 ? 'red' : ''}">
                        Products*
                        
                        <label class="r-toggle-switch" title="SELECT IN_ACTIVE PRODUCTS" style="margin-left: 10px;">
                            <input type="checkbox"
                                data-ng-model="showInactiveProducts"
                                data-ng-click="changeProductOnCategory()" /> Include Inactive Products
                            <span class="r-slider"></span>
                        </label>
                    </label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForProduct"
                        options="productsInfo" selected-model="selectedProducts" class="multi-select"></div>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <label><strong>Dimension</strong> <span style="color: red">(Please select this if you want to add new mappings)</span></label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForCatDim"
                        options="productDimensions" selected-model="selectedDimensions" class="multi-select"></div>
                </div>

                <div class="filter-group" data-ng-if="unitsByBrand.length > 0">
                    <label>Select Unit</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForUnit" options="unitsByBrand"
                        selected-model="selectedUnits" class="multi-select"></div>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group" data-ng-if="regions.length > 0">
                    <label>Select Region</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings" options="regions"
                        selected-model="selectedRegions" class="multi-select"></div>
                </div>

                <div class="filter-group" data-ng-if="regions.length > 0">
                    <label>Select City</label>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForCities"
                        options="filteredCities" selected-model="selectedCities" class="multi-select"></div>
                </div>
            </div>

            <div class="filter-actions" data-ng-if="selectedProducts.length > 0">
                <button class="bttn btn-pri animate__animated animate__pulse animate__infinite animate__slower"
                    data-ng-click="getDataForSelectedFilters(true)">
                    <i class="fa fa-search"></i> Get Data
                </button>
                <button class="bttn btn-pri animate__animated animate__pulse animate__infinite animate__slower"
                    data-ng-click="getDataForSelectedFilters(false)">
                    <i class="fa fa-search"></i> Get Excel To Email
                </button>
                <button class="bttn btn-pri" data-ng-click="changeShowExportModal()"
                    data-ng-if="resultProductListDetails.length > 0">
                    <i class="fa fa-file-excel"></i> Export to Excel
                </button>
                <button class="bttn btn-success" data-ng-click="viewChangesInModal()"
                    data-ng-if="selectedRecordsCount > 0">
                    <i class="fa fa-save"></i> View Changes
                </button>

            </div>
        </div>
    </section>

    <!-- Results Section -->
    <section class="results-section" data-ng-if="resultProductListDetails.length > 0">
        <h2><i class="fa fa-table"></i> Results</h2>
        <button class="bttn btn-danger" data-ng-click="clearFilters()" data-ng-if="filterApplied()">
            <i class="fa fa-question-circle"></i> Clear filters
        </button>
        <!-- Table records count -->
        <div class="record-summary">
            <span class="record-item">📊 Total Records: <strong>{{resultProductListDetails.length}}</strong></span>
            <span class="record-item">🔍 Filtered Records: <strong>{{filteredRecordsCount}}</strong></span>
            <span class="record-item">✅ Selected Records: <strong>{{selectedRecordsCount}}</strong></span>
        </div>
        <!-- table content -->
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr class="header-row">
                        <th width="50">
                            <div class="header-content">
                                <label for="selectAll">Select All</label>
                                <input class="header-filter" type="checkbox" id="selectAll"
                                    data-ng-model='filter.selectAll' data-ng-click="onChangeSelectAll()" />

                            </div>
                        </th>
                        <th>
                            <div class="header-content">
                                <span class="column-title">Unit
                                    Name</span>
                                <div class="header-filter">
                                    <input type="text" data-ng-model="filter.unitName"
                                        data-ng-change="applyFilterOnAll()" placeholder="Search..." />
                                    <i class="fa fa-search"></i>
                                </div>
                            </div>
                        </th>
                        <th>
                            <div class="header-content">
                                <span class="column-title">Product
                                    Name</span>
                                <div class="header-filter">
                                    <div ng-dropdown-multiselect=""
                                        extra-settings="multiSelectSettingsForProductInTableFilter"
                                        options="filter.allFilteredProducts" selected-model="filter.selectedProducts"
                                        class="multi-select"></div>
                                </div>
                            </div>
                        </th>
                        <th>
                            <div class="header-content">
                                <span class="column-title">Dimension</span>
                                <div class="header-filter">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForCatDim"
                                        options="filter.allFilteredDimensions"
                                        selected-model="filter.selectedDimensions" class="multi-select"></div>
                                </div>
                            </div>
                        </th>
                        <th data-ng-if="isProductRecipeMapping">
                            <div class="header-content">
                                <span class="column-title">Profile</span>
                                <div class="header-filter dropdown">
                                    <select data-ng-model="filter.profile">
                                        <option value="unmapped">show UnMapped Profiles</option>
                                        <option value="mapped">show Mapped Profiles</option>
                                        <option data-ng-repeat="profile in recipeProfiles | orderBy"
                                            value="{{profile}}">{{profile}}</option>
                                    </select>
                                    <div class="filter-actions">
                                        <!-- <button class="action-btn" data-ng-click="applyFilterOnAll()" title="Filter">
                                            <i class="fas fa-filter"></i>
                                        </button> -->
                                        <label class="r-toggle-switch">
                                            <input type="checkbox" data-ng-model="filter.profileFilter" data-ng-click="toggleSliderChecked()" 
                                                data-ng-disabled="filter.profile == null" title="Filter">
                                            <span class="r-slider">
                                        </label>
                                        <button class="action-btn apply" data-ng-click="applyProfilesFilter()"
                                            title="Apply" data-ng-if="showApplyBtnForProfile()">
                                            <i class="fa fa-check"></i>
                                        </button>
                                        <button class="action-btn info"
                                            title="Select 1 product and 1 dimension to apply profile in bulk"
                                            data-ng-click="showInfoForProfile()" data-ng-if="!showApplyBtnForProfile()">
                                            <i class="fa fa-question-circle"></i>
                                        </button>

                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-ng-if="isProductRecipeMapping">
                            <div class="header-content">
                                <span class="column-title">Status</span>
                                <div class="header-filter dropdown">
                                    <select data-ng-model="filter.updatedStatus">
                                        <option data-ng-repeat="status in defaultValues" value="{{status.name}}">
                                            {{status.name}}</option>
                                    </select>
                                    <div class="filter-actions">
                                        <!-- <button class="action-btn" data-ng-click="applyFilterOnAll()" title="Filter">
                                            <i class="fas fa-filter"></i>
                                        </button> -->
                                        <label class="r-toggle-switch">
                                            <input type="checkbox" data-ng-model="filter.statusFilter" data-ng-click="toggleSliderChecked()"
                                                data-ng-disabled="filter.updatedStatus == null" title="Filter">
                                            <span class="r-slider">
                                        </label>
                                        <button class="action-btn apply" data-ng-click="applyProfilesStatus()"
                                            title="Apply">
                                            <i class="fa fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-ng-if="isProductRecipeMapping">
                            <div class="header-content">
                                <span class="column-title">Delivery Only</span>
                                <div class="header-filter dropdown">
                                    <select data-ng-model="filter.updatedDOP">
                                        <option data-ng-repeat="flag in defaultValues" value="{{flag.flagName}}">
                                            {{flag.flagName}}</option>
                                    </select>
                                    <div class="filter-actions">
                                        <!-- <button class="action-btn" data-ng-click="applyFilterOnAll()" title="Filter">
                                            <i class="fas fa-filter"></i>
                                        </button> -->
                                        <label class="r-toggle-switch">
                                            <input type="checkbox" data-ng-model="filter.dopFilter" data-ng-click="toggleSliderChecked()"
                                                data-ng-disabled="filter.updatedDOP == null" title="Filter">
                                            <span class="r-slider">
                                        </label>
                                        <button class="action-btn apply" data-ng-click="applyDeliveryOnlyFilter()"
                                            title="Apply">
                                            <i class="fa fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-ng-if="isProductRecipeMapping">
                            <div class="header-content">
                                <span class="column-title">Pick DineIn Consumables</span>
                                <div class="header-filter dropdown">
                                    <select data-ng-model="filter.updatedDIC">
                                        <option data-ng-repeat="flag in defaultValues" value="{{flag.flagName}}">
                                            {{flag.flagName}}</option>
                                    </select>
                                    <div class="filter-actions">
                                        <!-- <button class="action-btn" data-ng-click="applyFilterOnAll()" title="Filter">
                                            <i class="fas fa-filter"></i>
                                        </button> -->
                                        <label class="r-toggle-switch">
                                            <input type="checkbox" data-ng-model="filter.dicFilter" data-ng-click="toggleSliderChecked()"
                                                data-ng-disabled="filter.updatedDIC == null" title="Filter">
                                            <span class="r-slider">
                                        </label>
                                        <button class="action-btn apply" data-ng-click="applyPickDineInConsumablesFilter()"
                                            title="Apply">
                                            <i class="fa fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-ng-if="!isProductRecipeMapping">
                            <div class="header-content">
                                <span class="column-title">Alias</span>
                                <div class="header-filter">
                                    <input type="text" data-ng-model="filter.aliasSearch" placeholder="Search..." />
                                    <div class="filter-actions">
                                        <!-- <button class="action-btn" data-ng-click="applyFilterOnAll()" title="Filter">
                                            <i class="fas fa-filter"></i>
                                        </button> -->
                                        <label class="r-toggle-switch">
                                            <input type="checkbox" data-ng-model="filter.aliasFilter" data-ng-click="toggleSliderChecked()"
                                                data-ng-disabled="filter.aliasSearch == null" title="Filter">
                                            <span class="r-slider">
                                        </label>
                                        <button class="action-btn apply" data-ng-click="applyAlias()" title="Apply">
                                            <i class="fa fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-ng-if="!isProductRecipeMapping">
                            <div class="header-content">
                                <span class="column-title">Description</span>
                                <div class="header-filter">
                                    <input type="text" data-ng-model="filter.DDSearch" placeholder="Search..." />
                                    <div class="filter-actions">
                                        <!-- <button class="action-btn" data-ng-click="applyFilterOnAll()" title="Filter">
                                            <i class="fas fa-filter"></i>
                                        </button> -->
                                        <label class="r-toggle-switch">
                                            <input type="checkbox" data-ng-model="filter.DDFilter" data-ng-click="toggleSliderChecked()"
                                                data-ng-disabled="filter.DDSearch == null" title="Filter">
                                            <span class="r-slider">
                                        </label>
                                        <button class="action-btn apply" data-ng-click="applyDimensionDesc()"
                                            title="Apply">
                                            <i class="fa fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-ng-repeat="detail in productsInThisPage" data-ng-if="detail.visible"
                        class="animate__animated animate__fadeIn"
                        data-ng-class="{'row-selected': detail.selected, 
                                'row-profile-dd-null': (detail.price.currentProfile == null),
                                'row-profile-null-selected' : (detail.price.currentProfile == null && detail.selected)}">
                        <td>
                            <div>
                                <input type="checkbox" data-ng-model='detail.selected'
                                    data-ng-click="onClickSelect(detail)" id="select">
                            </div>
                        </td>
                        <td>{{detail.unit.name}}</td>
                        <td>{{detail.product.name}} [{{detail.product.id}}]</td>
                        <td>{{detail.dimension.code}}</td>
                        <td data-ng-if="isProductRecipeMapping">
                            <span>{{detail.price.currentProfile}}</span>
                            <select class="form-control" data-ng-model="detail.price.profile"
                                data-ng-change="onChangeMarkSelect(detail, 'profile')">
                                <option data-ng-repeat="profile in detail.recipeProfiles | orderBy" value="{{profile}}">
                                    {{profile}}</option>
                            </select>
                        </td>
                        <td data-ng-if="isProductRecipeMapping">
                            <span>{{detail.price.currentStatus}}</span>
                            <select class="form-control" data-ng-model="detail.price.status"
                                data-ng-change="onChangeMarkSelect(detail, 'status')">
                                <option data-ng-repeat="status in defaultValues | orderBy" value="{{status.value}}">
                                    {{status.name}}</option>
                            </select>
                        </td>
                        <td data-ng-if="isProductRecipeMapping">
                            <span>{{detail.price.isDeliveryOnlyProductOldValue}}</span>
                            <select class="form-control" data-ng-model="detail.price.isDeliveryOnlyProduct"
                                data-ng-change="onChangeMarkSelect(detail, 'isDeliveryOnlyProduct')">
                                <option data-ng-repeat="flag in defaultValues | orderBy" value="{{flag.flagName}}">
                                    {{flag.flagName}}</option>
                            </select>
                        </td>
                        <td data-ng-if="isProductRecipeMapping">
                            <span>{{detail.price.pickDineInConsumablesOldValue}}</span>
                            <select class="form-control" data-ng-model="detail.price.pickDineInConsumables"
                                data-ng-change="onChangeMarkSelect(detail, 'pickDineInConsumables')">
                                <option data-ng-repeat="flag in defaultValues | orderBy" value="{{flag.flagName}}">
                                    {{flag.flagName}}</option>
                            </select>
                        </td>
                        <td data-ng-if="!isProductRecipeMapping">
                            <span>{{detail.price.currentAliasProductName}}</span>
                            <input class="form-control" data-ng-change="onChangeMarkSelect(detail, 'aliasProductName')"
                                data-ng-model="detail.price.aliasProductName" />
                        </td>
                        <td data-ng-if="!isProductRecipeMapping">
                            <span>{{detail.price.currentDimensionDescriptor}}</span>
                            <input class="form-control"
                                data-ng-change="onChangeMarkSelect(detail, 'dimensionDescriptor')"
                                data-ng-model="detail.price.dimensionDescriptor" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div>
            <button data-ng-disabled="currentPage == 0" data-ng-click="prevPage()">Prev</button>
            <button data-ng-click="goToPage(0)" data-ng-hide="currentPage <= 1">1</button>
            <span data-ng-if="currentPage > 2">...</span>
            <span>Page {{ currentPage + 1 }} of {{ totalPages }}</span>
            <span data-ng-if="currentPage < totalPages - 3">...</span>
            <button data-ng-click="goToPage(totalPages - 1)" data-ng-hide="currentPage >= totalPages - 2">
                {{ totalPages }}
            </button>
            <button data-ng-disabled="currentPage >= totalPages - 1" data-ng-click="nextPage()">Next</button>
        </div>
    </section>

    <!-- View After making changes in table -->
    <section>
        <custom-modal is-open="isModalOpen" on-submit="submitModal()" on-close="closeModal()" title="Product List"
            width="100%" height="100%" show-submit="true">
            <div class="record-summary">
                <span class="record-item">📊 Total Records: <strong>{{resultProductListDetails.length}}</strong></span>
                <span class="record-item">✅ Selected Records: <strong>{{selectedRecordsCount}}</strong></span>
                <span class="record-item">Updated Records: <strong>{{updatedRecordsCount}}</strong></span>
                <span data-ng-if="profileEmptyCount > 0" class="record-item">Profile Empty Records:
                    <strong>{{profileEmptyCount}}</strong></span>
            </div>
            <table class="scrollable-table">
                <thead>
                    <tr>
                        <th>Unit Name</th>
                        <th>Product Name</th>
                        <th>Dimension</th>
                        <th data-ng-if="isProductRecipeMapping">Old Profile</th>
                        <th data-ng-if="isProductRecipeMapping">New Profile</th>
                        <th data-ng-if="isProductRecipeMapping">Old Status</th>
                        <th data-ng-if="isProductRecipeMapping">New Status</th>
                        <th data-ng-if="isProductRecipeMapping">Old Delivery Only</th>
                        <th data-ng-if="isProductRecipeMapping">New Delivery Only</th>
                        <th data-ng-if="isProductRecipeMapping">Old Pick DineIn Consumables</th>
                        <th data-ng-if="isProductRecipeMapping">New Pick DineIn Consumables</th>
                        <th data-ng-if="!isProductRecipeMapping">Old Dimension Desc</th>
                        <th data-ng-if="!isProductRecipeMapping">New Dimension Desc</th>
                        <th data-ng-if="!isProductRecipeMapping">Old Product Alias</th>
                        <th data-ng-if="!isProductRecipeMapping">New Product Alias</th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-ng-repeat="item in modalData track by $index">
                        <td>{{ item.unit.name }}</td>
                        <td>{{ item.product.name }} [{{item.product.id}}]</td>
                        <td>{{ item.dimension.code }}</td>
                        <td data-ng-if="isProductRecipeMapping">{{ item.price.currentProfile }}</td>
                        <td data-ng-if="isProductRecipeMapping"
                            data-ng-class="{'field-changed': (item.price.profile != item.price.currentProfile)}">{{
                            item.price.profile }}</td>
                        <td data-ng-if="isProductRecipeMapping">{{ item.price.currentStatus }}</td>
                        <td data-ng-if="isProductRecipeMapping"
                            data-ng-class="{'field-changed': (item.price.status != undefined && item.price.status != item.price.currentStatus)}">
                            {{ item.price.status }}</td>
                        <td data-ng-if="isProductRecipeMapping">{{ item.price.isDeliveryOnlyProductOldValue }}</td>
                        <td data-ng-if="isProductRecipeMapping"
                            data-ng-class="{'field-changed': (item.price.isDeliveryOnlyProduct != item.price.isDeliveryOnlyProductOldValue)}">
                            {{ item.price.isDeliveryOnlyProduct }}</td>
                        <td data-ng-if="isProductRecipeMapping">{{ item.price.pickDineInConsumablesOldValue }}</td>
                        <td data-ng-if="isProductRecipeMapping"
                            data-ng-class="{'field-changed': (item.price.pickDineInConsumables != item.price.pickDineInConsumablesOldValue)}">
                            {{ item.price.pickDineInConsumables }}</td>
                        <td data-ng-if="!isProductRecipeMapping">{{ item.price.currentDimensionDescriptor }}</td>
                        <td data-ng-if="!isProductRecipeMapping"
                            data-ng-class="{'field-changed': (item.price.dimensionDescriptor != item.price.currentDimensionDescriptor)}">
                            {{ item.price.dimensionDescriptor }}</td>
                        <td data-ng-if="!isProductRecipeMapping">{{ item.price.currentAliasProductName }}</td>
                        <td data-ng-if="!isProductRecipeMapping"
                            data-ng-class="{'field-changed': (item.price.aliasProductName != item.price.currentAliasProductName)}">
                            {{ item.price.aliasProductName }}</td>
                    </tr>
                </tbody>
            </table>
        </custom-modal>
    </section>

</div>