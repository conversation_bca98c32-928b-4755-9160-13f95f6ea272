<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div id="fullOfferDiv">
    <div id="offerListDivDetail">
        <div class="row" ng-init="init()">
            <div class="col-lg-12">
                <br>
                <h1 class="page-header">
                    Offer Management
<!--                    <button class="btn btn-primary pull-right" data-toggle="modal"-->
<!--                            id="myBtn2" ng-click="addOffer()">-->
<!--                        <i class="fa fa-plus fw"></i> Add Offer-->
<!--                    </button>-->
                    <button class="btn btn-primary pull-right" data-toggle="modal"
                            id="myBtn3" ng-click="addOfferNew()">
                        <i class="fa fa-plus fw"></i> Add Offer
                    </button>
                </h1>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4">
                <input type="text" ng-model="search" ng-change="filter()"
                       placeholder="Search Offer" class="form-control"/>
                <p ng-show="offers.length > 0">Search {{ filteredItems }} of {{ totalItemsOffers}} total
                    results</p>
            </div>
            <div class="col-lg-1">
                <button class="btn btn-primary" title="Please enter any keyword to search." data-ng-disabled="search == '' || search == null" ng-click="searchLike()">
                    Search
                </button>
            </div>
            <div class="col-lg-4">
                <button ng-click="searchOffers()" class="btn btn-primary pull-left">
                    Display all offers
                </button>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12" data-ng-show="offers.length > 0">
                <label for="brandId">Filter By Brand ID</label>
                <select name="brandId" id="brandId" class="form-control" style="width: 200px; margin-bottom: 20px;"
                        ng-options="brand.brandId as (brand.brandId + ' - ' + brand.brandName) for brand in brands"
                        data-ng-model="selectedBrandId">
                        <option value="">ALL</option>
                </select>
            </div>
        </div>
        <div class="row">
            <div
                    class="col-xs-12"
                    ng-show="offers.length > 0">
                <div class="row">
                    <div class="col-xs-12">
                        <table class="table table-striped table-bordered">
                            <thead style="background-color: #e7e7e7">
                            <th>Id<a ng-click="sort_by('id');"><i
                                    class="glyphicon glyphicon-sort"></i></a></th>
                            <th>Category</th>
                            <th>Type<a ng-click="sort_by('status');"><i
                                    class="glyphicon glyphicon-sort"></i></a></th>
                            <th>Brand ID</th>
                            <th>Description</th>
                            <th>Text</th>
                            <th>Start Date<a ng-click="sort_by('startDate');"><i
                                    class="glyphicon glyphicon-sort"></i></a></th>
                            <th>End Date<a ng-click="sort_by('endDate');"><i
                                    class="glyphicon glyphicon-sort"></i></a></th>
                            <th>Remove Loyalty</th>
                            <th>Min Value&nbsp;<a ng-click="sort_by('minValue');"><i
                                    class="glyphicon glyphicon-sort"></i></a></th>
                            <th>Offer Value</th>
                            <th>Status</th>
                            <th colspan="3" style="text-align:center">Action</th>
                            </thead>
                            <tbody>
                            <tr     
                                    data-ng-repeat="offer in offers | filter: {brandId: selectedBrandId || undefined} | filter :search | limitTo:numPerPage:(currentPage - 1) * numPerPage | orderBy : predicate :reverse  |  limitTo:entryLimit">
                                <td>{{offer.id}}</td>
                                <td><a
                                        data-toggle="modal"
                                        id="viewOfferCouponsDiv"
                                        class="btn btn-info"
                                        data-ng-click="viewFullDetailOffer(offers,offer.id)">{{offer.category}}</a>
                                </td>
                                <td style="font-weight: bold; font-size:11px">{{offer.type.replace('_','
                                    ').replace('_STRATEGY','')}}
                                </td>
                                <td>{{offer.brandId}}</td>
                                <td width="12%">{{offer.description}}</td>
                                <td width="12%">{{offer.text}}</td>
                                <td>{{offer.startDate | date:'yyyy-MM-dd'}}</td>
                                <td>{{offer.endDate | date:'yyyy-MM-dd'}}</td>
                                <td>{{offer.removeLoyalty ? 'YES': 'NO'}}</td>
                                <td>{{offer.minValue}}</td>
                                <td>{{offer.offerValue}}</td>
                                <td><h4>
											<span
                                                    data-ng-if="offer.status == 'ACTIVE'"
                                                    class="label label-success">{{offer.status}}</span> <span
                                        data-ng-if="offer.status != 'ACTIVE'"
                                        class="label label-danger">{{offer.status}}</span>
                                </h4></td>
                                <td>
                                    <button
                                            class="btn btn-primary"
                                            data-toggle="modal"
                                            id="myBtn2"
                                            ng-click="editOfferNew(offer,offer.id)">Edit Offer
                                    </button>
                                </td>
                                <td><a
                                        data-toggle="modal"
                                        style="cursor: pointer"
                                        class="btn btn-info"
                                        ng-click="viewOfferCouponsList(offer.id)"> Coupons </a></td>
                                <!-- <td>	<a data-toggle="modal" style="cursor:pointer" ng-click="editOffer(offers,offer.id)"> 	    Offer		</a>	</td> -->
                                <td align="left">
                                            <span style="cursor: pointer"
                                                  title="Clone">
                                                <i class="fa fa-copy" ng-click="editOfferNew(offer,offer.id,true)" style="font-size: 24px; margin-right: 5px"></i>
                                            </span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <center>
                            <div align="center">
                               <b>
                                        <uib-pagination
                                                total-items="filteredItems"
                                                ng-model="currentPage"
                                                max-size="5"
                                                boundary-link-numbers="true"
                                                ng-disabled="false"
                                                rotate="true"
                                                items-per-page="numPerPage"
                                                class="pagination-sm"></uib-pagination>
                                    </b>
                            </div>
                        </center>
                    </div>
                    <div
                            class="col-lg-10"
                            ng-if="filteredItems == 0">
                        <h4>No results found</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="couponModal" tabindex="-1" role="dialog"
     aria-labelledby="couponModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="partnerLabel">Marketing Partner
                    List</h4>
            </div>
            <table class="table table-striped table-bordered"
                   style="font-size: 11px">
                <thead>
                <th>ID&nbsp;</th>
                <th>Name&nbsp;</th>
                <th>Status&nbsp;</th>
                </thead>
                <tbody>
                <tr ng-repeat="offer in viewPartnerList">
                    <td>{{offer.id}}</td>
                    <td>{{offer.name}}</td>
                    <td>{{offer.status}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="offerModal" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" ng-click="clearForm()"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Offer Creation
                    Process</h4>
            </div>
            <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                <div>
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li role="presentation" ng-class="{active: !tab1, disabled: tab1}" ng-hide="editMode">
                            <a data-target="#offer" aria-controls="offer" role="tab"
                               data-toggle="tab">Offer</a>
                        </li>
                        <li role="presentation" ng-class="{active: !tab2, disabled: tab2}" ng-hide="editMode">
                            <a data-target="#offerMapping" aria-controls="offerMapping"
                               role="tab" data-toggle="tab">Offer Mapping</a>
                        </li>
                        <li role="presentation" ng-class="{active: !tab3, disabled: tab3}" ng-hide="editMode">
                            <a data-target="#partnerlist" aria-controls="partnerlist"
                               role="tab" data-toggle="tab">Partner List</a>
                        </li>
                        <li role="presentation" ng-class="{active: !tab4, disabled: tab4}"
                            ng-if="actionOffer!='edit'"><a data-target="#coupons"
                                                           aria-controls="oupons" role="tab"
                                                           data-toggle="tab">Coupons</a></li>
                        <li role="presentation" ng-class="{active: !tab5, disabled: tab5}"
                            ng-if="actionOffer!='edit'"><a
                                data-target="#couponsmapping" aria-controls="couponsmapping"
                                role="tab" data-toggle="tab">Coupons Mapping</a></li>
                        <li role="presentation" ng-class="{active: !tab6, disabled: tab6}"
                            ng-if="actionOffer!='edit'"><a
                                data-target="#replicateCoupons" aria-controls="couponsmapping"
                                role="tab" data-toggle="tab"> Summary Coupons</a></li>
                        <li role="presentation" ng-class="{active: !tab7, disabled: tab7}"
                            ng-if="actionOffer!='edit'"><a
                                data-target="#replicateCoupons" aria-controls="replicateCoupons"
                                role="tab" data-toggle="tab">Replicate Coupons</a></li>
                    </ul>
                    <!-- Tab panes -->
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane" ng-class="{active: !tab1}"
                             id="cafe">
                            <label>Accounts Type *</label>
                            <div class="form-group">
                                <select class="form-control" ng-model="offerAccountCategory"
                                        ng-options="c as c.name for c in offerAccountsCategories track by c.id"
                                        ng-change="updateAccountsCategory(offerAccountCategory)"
                                        ng-disabled="offerForEdit != null && offerForEdit.accountsCategory != null && actionOffer == 'edit'"></select>
                            </div>
                            <label>Offer Type *</label>
                            <div class="form-group">
                                <select class="form-control" ng-model="offerCategory"
                                        ng-options="selectOfferCat as selectOfferCat.code for selectOfferCat in offerCat track by selectOfferCat.code"
                                        ng-change="showOfferCatDet(offerCategory.code)"></select>
                            </div>
                            <div class="form-group">
                                <label>Offer Sub Type *</label> <select ng-model="offerType"
                                                                        data-ng-change="setOfferValueHeading(offerType)"
                                                                        class="form-control">
                                <option ng-repeat="offerCategoryLists in offerCategoryLists"
                                        value="{{offerCategoryLists}}">{{offerCategoryLists}}
                                </option>
                            </select>
                            </div>
                            <div class="form-group">
                                <label>Offer Reason *</label>
                                <textarea class="form-control" rows="3" ng-model="offerText"></textarea>
                            </div>
                            <div class="form-group">
                                <label>Offer Description *</label>
                                <textarea class="form-control" rows="3"
                                          ng-model="offerDescription"></textarea>
                            </div>
                            <div class="form-group">
                                <div style="display: inline-block; width: 100%">
                                    <div style="float:left">
                                        <label>Start Date *</label>
                                        <div class="datepicker" date-format="yyyy-MM-dd">
                                            <input class="form-control" ng-model="startDate"
                                                   placeholder="click here" type="text" required/>
                                        </div>
                                    </div>
                                    <div style="float:right">
                                        <label>End Date *</label>
                                        <div class="datepicker" date-format="yyyy-MM-dd">
                                            <input class="form-control" ng-model="endDate"
                                                   placeholder="click here" type="text" required/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>Offer Status * </label> <select class="form-control"
                                                                       ng-model="offerStatus"
                                                                       ng-options="offerStatusType.name for offerStatusType in basicInfoStatus track by offerStatusType.name"></select>
                            </div>
                            <div class="form-group">
                                <label>Minimum Bill Value </label> <input type="number"
                                                                          class="form-control" min="0" string-to-number
                                                                          ng-model="minValue"/>
                            </div>
                            <div class="form-group">
                                <label>Validate Customer * (customer login Required) </label> <select
                                    class="form-control" ng-model="validateCustomer"
                                    ng-options="validateCustomerType.name for validateCustomerType in validateCustomerDetails track by validateCustomerType.code"></select>
                            </div>
                            <div class="form-group">
                                <label>Remove Loyalty </label> <select
                                    class="form-control" ng-model="removeLoyalty"
                                    ng-options="removeLoyaltyType.name for removeLoyaltyType in removeLoyaltyDetails track by removeLoyaltyType.code"></select>
                            </div>
                            <div class="form-group">
                                <label>Offer Scope *</label> <select class="form-control"
                                                                     ng-model="offerScope"
                                                                     ng-options="offerScopeType.name for offerScopeType in offerScopeDetails track by offerScopeType.name"></select>
                            </div>
                            <div class="form-group"
                                 data-ng-show="offerScope != null && offerScope.name == 'CORPORATE'">
                                <label>Email Domain *</label> <input type="text"
                                                                     class="form-control" ng-model="emailDomain"/>
                            </div>
                            <div class="form-group">
                                <label>Minimum Item Count *</label> <input type="number"
                                                                           class="form-control" min="0" string-to-number
                                                                           ng-model="minItemCount"/>
                            </div>
                            <div class="form-group">
                                <label>Quantity Limit *</label> <input type="number"
                                                                       class="form-control" min="0" string-to-number
                                                                       ng-model="quantityLimit"/>
                            </div>
                            <div class="form-group">
                                <label> {{displayOnlyVal}} Offer Value *</label> <input
                                    type="number" class="form-control" min="0" string-to-number
                                    ng-model="offerValue"/>
                            </div>
                            <div class="form-group" data-ng-if="offerType=='PERCENTAGE_BILL_MAX_CAP_STRATEGY'">
                                <label>Max Capped Discount</label>
                                <input type="number" class="form-control" min="0" string-to-number
                                       data-ng-change="updateMaxDiscount(maxDiscount)"
                                       ng-model="maxDiscount"/>
                            </div>
                            <div class="form-group">
                                <label>OTP Required *</label>
                                <select
                                        class="form-control" ng-model="otpRequired"
                                        ng-options="validateCustomerType.name for validateCustomerType in validateCustomerDetails track by validateCustomerType.code"></select>
                            </div>
                            <button class="btn btn-primary" type="button"
                                    ng-click="addOfferDetails()">Next
                            </button>
                        </div>
                        <div role="tabpanel" class="tab-pane" ng-class="{active: !tab2}"
                             id="cod">
                            <br>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="panel-group" id="accordion" role="tablist"
                                         aria-multiselectable="true" ng-init="init()">
                                        <div class="panel panel-default">
                                            <div class="panel-heading" role="tab" id="headingOne"
                                                 ng-click="getUnitData('category')">
                                                <h4 class="panel-title">
                                                    <button data-toggle="collapse" data-parent="#accordion"
                                                            data-target="#{{unit}}" aria-expanded="false"
                                                            ng-class="{'btn disabled': selectedSubCategoriesLength > 0 || selectedProductsLength > 0}"
                                                            data-ng-disabled="selectedSubCategoriesLength > 0 || selectedProductsLength"
                                                            aria-controls="collapseOne">
                                                        Category ({{selectedCategoriesLength}})<i
                                                            class="pull-right glyphicon"
                                                            ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                    </button>
                                                </h4>
                                            </div>
                                            <div id="category" class="panel-collapse collapse"
                                                 role="tabpanel" aria-labelledby="headingOne">
                                                <div class="panel-body">
                                                    <table class="table table-bordered">
                                                        <thead style="background-color: #50773e; color: #ffffff">
                                                        <th>Check &nbsp;</th>
                                                        <th>Category Name&nbsp;</th>
                                                        </thead>
                                                        <tbody>
                                                        <tr ng-repeat='detDatas in productCategory | orderBy : "detail.name"'>
                                                            <td ng-class="{'success': sel[detDatas.detail.id] == true}">
                                                                <input type="checkbox"
                                                                       ng-model="sel[detDatas.detail.id]"
                                                                       ng-click="manageCategories(detDatas,sel[detDatas.detail.id])">
                                                            </td>
                                                            <td ng-class="{'success': sel[detDatas.detail.id] == true}">
                                                                {{detDatas.detail.name}}
                                                            </td>
                                                            <!--  <pre>{{ productCategory | json}}</pre> -->
                                                        </tr>

                                                        <tr style="display:none">
                                                            <td colspan="2" align="right">
                                                                <div>
                                                                    <button class="btn btn-primary" type="button"
                                                                            ng-click="selectedSubmitProductCategory()">
                                                                        Submit
                                                                        Category
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel panel-default">
                                            <div class="panel-heading" role="tab" id="headingOne"
                                                 ng-click="getUnitData('subCategory')">
                                                <h4 class="panel-title">
                                                    <button data-toggle="collapse" data-parent="#accordion"
                                                            data-target="#{{unit}}" aria-expanded="false"
                                                            ng-class="{'btn disabled': selectedCategoriesLength > 0 || selectedProductsLength > 0}"
                                                            aria-controls="collapseOne"
                                                            data-ng-disabled="selecedCategoriesLength > 0 || selectedProductsLength > 0">
                                                        Sub Category ({{selectedSubCategoriesLength}})<i class="pull-right glyphicon"
                                                                       ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                    </button>
                                                </h4>
                                            </div>
                                            <div id="subCategory" class="panel-collapse collapse"
                                                 role="tabpanel" aria-labelledby="headingOne">
                                                <div class="panel-body">
                                                    <table class="table table-bordered">
                                                        <thead style="background-color: #50773e; color: #ffffff">
                                                        <th>Check &nbsp;</th>
                                                        <th>Sub Category Name&nbsp;</th>
                                                        </thead>
                                                        <tbody>
                                                        <tr ng-repeat='detDatas1 in finalSubtype | orderBy: "name"'>
                                                            <td ng-class="{'success': subCatObj[detDatas1.id] == true}"><input type="checkbox"
                                                                       ng-model="subCatObj[detDatas1.id]"
                                                                       ng-click="manageSubCategories(detDatas1,subCatObj[detDatas1.id])">
                                                            </td>
                                                            <td ng-class="{'success': subCatObj[detDatas1.id] == true}">{{detDatas1.name}}</td>
                                                            <!--   <pre>{{ fullSubCatObj | json}}</pre>-->
                                                        </tr>
                                                        <tr style="display:none">
                                                            <td colspan="2" align="right">
                                                                <div>
                                                                    <button class="btn btn-primary" type="button"
                                                                            ng-click="selectedSubmitSubtypeCategory()">
                                                                        Submit
                                                                        Sub Category
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel panel-default">
                                            <div class="panel-heading" role="tab" id="headingOne"
                                                 ng-click="getUnitData('product')">
                                                <h4 class="panel-title">
                                                    <button data-toggle="collapse" data-parent="#accordion"
                                                            data-target="#{{unit}}" aria-expanded="false"
                                                            ng-class="{'btn disabled': selectedCategoriesLength > 0 || selectedSubCategoriesLength > 0}"
                                                            data-ng-disabled="selectedCategoriesLength > 0 || selectedSubCategoriesLength > 0"
                                                            aria-controls="collapseOne">
                                                        Product ({{selectedProductsLength}})<i class="pull-right glyphicon"
                                                                  ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                    </button>
                                                </h4>
                                            </div>
                                            <div id="product" class="panel-collapse collapse"
                                                 role="tabpanel" aria-labelledby="headingOne">
                                                <div class="panel-body">
                                                    <table class="table table-bordered">
                                                        <thead style="background-color: #50773e; color: #ffffff">
                                                        <th>Check &nbsp;</th>
                                                        <th>Product Name&nbsp;</th>
                                                        </thead>
                                                        <tbody>
                                                        <tr ng-repeat='detDatas2 in productDetailsList | orderBy: "name"'>
                                                            <td ng-class="{'success': prodObj[detDatas2.id] == true}"><input type="checkbox"
                                                                       ng-model='prodObj[detDatas2.id]'
                                                                       ng-click="manageProducts(detDatas2,prodObj[detDatas2.id])">
                                                            </td>
                                                            <td ng-class="{'success': prodObj[detDatas2.id] == true}">{{detDatas2.name}}</td>
                                                        </tr>
                                                        <tr style="display:none">
                                                            <td align="right" colspan="2">
                                                                <div>
                                                                    <button class="btn btn-primary" type="button"
                                                                            ng-click="selectedSubmitProducts()">Submit
                                                                        Product
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12" id="fullSelectedObjCategoryDiv"
                                         style="display: none">
                                        <table class="table table-bordered">
                                            <thead style="background-color: #50773e; color: #ffffff">
                                            <th>Product Category &nbsp;</th>
                                            </thead>
                                            <tbody>
                                            <tr ng-repeat='fulSelObjCat in fullSelectedObjCategory'>
                                                <td>{{fulSelObjCat.categoryName}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-xs-12" id="fullSelectedObjSubCategoryDiv"
                                         style="display: none">
                                        <table class="table table-bordered">
                                            <thead style="background-color: #50773e; color: #ffffff">
                                            <th>Product Sub Category &nbsp;</th>
                                            </thead>
                                            <tbody>
                                            <tr
                                                    ng-repeat='fulSelObjSubCat in fullSelectedObjSubCategory'>
                                                <td>{{fulSelObjSubCat.subCategoryName}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-xs-12" id="fullSelectedObjProductDiv"
                                         style="display: none">
                                        <table class="table table-bordered">
                                            <thead style="background-color: #50773e; color: #ffffff">
                                            <th>Product &nbsp;</th>
                                            </thead>
                                            <tbody>
                                            <tr ng-repeat='fulSelObjProd in fullSelectedObjProduct | orderBy: "productName"'>
                                                <td>{{fulSelObjProd.productName}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <br>
                                    <br>
                                    <!-- <div id="btnAllDiv" style="display:none"> -->
                                    <button class="btn btn-primary" type="button"
                                            ng-click="selectTab('tab1')">Prev
                                    </button>
                                    <button class="btn btn-primary" type="button"
                                            ng-click="offerMapping('tab3')">Next
                                    </button>
                                    <!--  </div> -->
                                </div>
                            </div>
                        </div>
                        <div role="tabpanel" class="tab-pane" ng-class="{active: !tab3}"
                             id="partners">
                            <h3>Select Partners</h3>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="panel-group" id="accordion" role="tablist"
                                         aria-multiselectable="true">
                                        <div class="panel panel-default">
                                            <div class="panel-heading" role="tab" id="headingOne"
                                                 ng-click="getPartnerData('partnersList')">
                                                <h4 class="panel-title">
                                                    <button data-toggle="collapse" data-parent="#accordion"
                                                            data-target="#{{partnerUnit}}" aria-expanded="true"
                                                            aria-controls="collapseOne">
                                                        Partners List ({{fullSelectedObjPartner.length}}) <i
                                                            class="pull-right glyphicon"
                                                            ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                    </button>
                                                </h4>
                                            </div>
                                            <div id="partnersList"
                                                 role="tabpanel" aria-labelledby="headingOne">
                                                <div class="panel-body">
                                                    <table class="table table-bordered">
                                                        <thead style="background-color: #50773e; color: #ffffff">
                                                        <th>Check &nbsp;</th>
                                                        <th>Partners Name&nbsp;</th>
                                                        </thead>
                                                        <tbody>
                                                        <tr ng-repeat='partnerListsData in partnerDetailsList | orderBy : "name"'>
                                                            <td ng-class="{'success': partObj[partnerListsData.id] == true}">
                                                                <input type="checkbox"
                                                                       ng-model="partObj[partnerListsData.id]"
                                                                       ng-click="managePartners(partnerListsData,partObj[partnerListsData.id])"
                                                                       checked="checked"></td>
                                                            <td ng-class="{'success': partObj[partnerListsData.id] == true}">
                                                                {{partnerListsData.name}}
                                                            </td>
                                                            <!-- ng-click="checkPartnerDetail(partnerListsData.id,partObj[partnerListsData.id],partnerListsData.name)"  <pre>{{ fullSubCatObj | json}}</pre>-->
                                                        </tr>
                                                        <tr style="display:none">
                                                            <td colspan="2" align="right">
                                                                <div>
                                                                    <button class="btn btn-primary" type="button"
                                                                            ng-click="selectedSubmitPartners()">Submit
                                                                        Partners
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12" id="fullSelectedObjPartnerListDiv"
                                         style="display: none">
                                        <table class="table table-bordered">
                                            <thead style="background-color: #50773e; color: #ffffff">
                                            <th>Partner List &nbsp;</th>
                                            <th>Status &nbsp;</th>
                                            </thead>
                                            <tbody>
                                            <tr ng-repeat='fulSelObjParnter in fullSelectedObjPartner'>
                                                <td>{{fulSelObjParnter.name}}</td>
                                                <td>{{fulSelObjParnter.status}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div id="btnAllPartnerDiv">
                                <button class="btn btn-primary" type="button"
                                        ng-if="offerCategory.code=='BILL'" ng-click="selectTab('tab1')">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-if="offerCategory.code=='ITEM'" ng-click="selectTab('tab2')">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="addPartnersData('tab4')">Next
                                </button>
                            </div>
                        </div>
                        <div role="tabpanel" class="tab-pane" ng-class="{active: !tab4}"
                             id="summary">
                            <div class="col-xs-12" ng-if="actionOffer=='add'">
                                <div class="col-xs-4">
                                    <label>Prefix {{actionOffer}}</label> <input type="text"
                                                                                 class="form-control text-uppercase"
                                                                                 ng-model="prefixCoupons"/>
                                </div>
                                <div class="col-xs-4">
                                    <label>Code *</label> <input type="input"
                                                                 class="form-control text-uppercase"
                                                                 ng-model="CouponCode"
                                                                 ng-blur="checkCouponsAvail(CouponCode,prefixCoupons)"/>
                                </div>
                                <div class="col-xs-4">
                                    <loading style align="center"></loading>
                                    <label>{{prefixCouponsDetail}}</label>
                                    <div style="color: {{color">{{couponsAvail}}</div>
                                </div>
                            </div>
                            <div class="col-xs-12" ng-if="actionOffer=='edit'">
                                <br>
                                <div class="col-xs-4">
                                    <label>Code : {{prefixCouponsDetail}}</label>
                                    <div style="color: {{color">{{couponsAvail}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label> Status * </label> <select class="form-control"
                                                                  ng-model="couponsStatus"
                                                                  ng-options="couponStatusType.name for couponStatusType in basicCouponStatus track by couponStatusType.name"></select>
                            </div>
                            <div class="form-group">
                                <label>Start Date *</label>
                                <div class="datepicker" date-format="yyyy-MM-dd">
                                    <input class="form-control" ng-model="startDateCoupons"
                                           placeholder="click here" type="text" required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>End Date *</label>
                                <div class="datepicker" date-format="yyyy-MM-dd">
                                    <input class="form-control" ng-model="endDateCoupons"
                                           placeholder="click here" type="text" required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label> Reusable Coupons* </label> <select class="form-control"
                                                                           ng-model="reusableCoupons"
                                                                           ng-options="reusableCouponsDatas.name for reusableCouponsDatas in basicReusableCoupons track by reusableCouponsDatas.name"></select>
                            </div>
                            <div class="form-group">
                                <label> Reusable Customer* </label> <select class="form-control"
                                                                            ng-model="reusableCustomerCoupons"
                                                                            ng-options="reusableCustomerData.name for reusableCustomerData in basicReusableCustomer track by reusableCustomerData.name"></select>
                            </div>
                            <div class="form-group" >
                                <label>Reusable Applicability (In Days)</label>
                                <input type="number" class="form-control" min="0" string-to-number
                                       ng-model="couponApplicability"/>
                            </div>
                            <div class="form-group">
                                <label>Max Usage*</label> <input type="number"
                                                                 class="form-control" string-to-number
                                                                 ng-model="maxUsageCoupons"/>
                            </div>
                            <div class="form-group" >
                                <label>Customer Visibility</label>
                                <select type="text" class="form-control" ng-model="customerVisibility">
                                    <option value="Y">Yes</option>
                                    <option value="N">No</option>
                                </select>
                            </div>
                              <button class="btn btn-primary" type="button" ng-click="selectTab('tab3')">Prev</button>
                            <button class="btn btn-primary" type="button"
                                    ng-click="addCoupons()">Next
                            </button>
                        </div>
                        <div role="tabpanel" class="tab-pane" ng-class="{active: !tab5}"
                             id="couponsMapp">
                            <div class="row">
                                <div class="col-xs-12">
                                    <h3 style="padding: 10px">
                                        <strong>Coupons Mappings</strong>
                                    </h3>
                                    <hr/>
                                </div>
                                <ul class="nav nav-tabs" role="tablist">
                                    <li role="presentation"
                                        ng-class="{active: !tab20, disabled: tab20}"><a
                                            data-target="#Units" aria-controls="Units" role="tab"
                                            data-toggle="tab">Unit</a></li>
                                    <li role="presentation"
                                        ng-class="{active: !tab21, disabled: tab21}"><a
                                            data-target="#Customers" aria-controls="Customers" role="tab"
                                            data-toggle="tab">Customer</a></li>
                                    <li role="presentation"
                                        ng-class="{active: !tab22, disabled: tab22}"><a
                                            data-target="#products" aria-controls="products" role="tab"
                                            data-toggle="tab">Product</a></li>
                                    <li role="presentation"
                                        ng-class="{active: !tab23, disabled: tab23}"><a
                                            data-target="#orderSources" aria-controls="orderSources"
                                            role="tab" data-toggle="tab">Order Source</a></li>
                                    <li role="presentation"
                                        ng-class="{active: !tab24, disabled: tab24}"><a
                                            data-target="#channelPartners" aria-controls="channelPartners"
                                            role="tab" data-toggle="tab">Channel Partners</a></li>
                                    <li role="presentation"
                                        ng-class="{active: !tab25, disabled: tab25}"><a
                                            data-target="#paymentMode" aria-controls="paymentMode"
                                            role="tab" data-toggle="tab">Payment Mode</a></li>
                                    <li role="presentation"
                                        ng-class="{active: !tab26, disabled: tab26}"><a
                                            data-target="#newCustomers" aria-controls="newCustomers"
                                            role="tab" data-toggle="tab">New Customer</a></li>
                                </ul>
                            </div>
                            <br>
                            <br>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab20}"
                                 id="UnitsTab" style="display: none">
                                <!-- -- here is -->
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="panel-group" id="accordion" role="tablist"
                                             aria-multiselectable="true" ng-init="init()">
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getRegionsData('regionUnitList')">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{regionUnitList}}" aria-expanded="false"
                                                                data-ng-disabled="selectedUnits.length > 0"
                                                                ng-class="{'btn disabled': selectedUnits.length > 0}"
                                                                aria-controls="collapseOne">
                                                            Select Regions ({{selectedRegions.length}})<i class="pull-right glyphicon"
                                                                             ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="regionUnitList" class="panel-collapse collapse"
                                                     role="tabpanel" aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Name&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat='UnitRegionData in regions | orderBy'>
                                                                <td ng-class="{'success': unitObjs[UnitRegionData] == true}"><input type="checkbox"
                                                                           ng-model="unitObjs[UnitRegionData]"
                                                                           ng-click="selectRegions(UnitRegionData,unitObjs[UnitRegionData])">
                                                                    <!--ng-click="checkRegionDetails(UnitRegionData,unitObjs[UnitRegionData])"-->
                                                                </td>
                                                                <td ng-class="{'success': unitObjs[UnitRegionData] == true}" >{{UnitRegionData}}</td>
                                                            </tr>
                                                            <tr style="display:none">
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitRegions(regions)">
                                                                            Submit
                                                                            Regions
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getUnitsData('UnitList')">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{UnitList}}" aria-expanded="false"
                                                                data-ng-disabled="selectedRegions.length > 0"
                                                                ng-class="{'btn btn-disabled':selectedRegions.length > 0}"
                                                                aria-controls="collapseOne">
                                                            Select Units ({{selectedUnits.length}})<i class="pull-right glyphicon"
                                                                           ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="UnitList" class="panel-collapse collapse"
                                                     role="tabpanel" aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Name&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat='UnitCafeData in filteredCafeListBrandWise | orderBy : "name"'>
                                                                <td ng-class="{'success': unitAllObj[UnitCafeData.id] == true}"><input type="checkbox"
                                                                           ng-model="unitAllObj[UnitCafeData.id]"
                                                                           ng-click="selectUnits(UnitCafeData,unitAllObj[UnitCafeData.id])">
                                                                    <!--ng-click="checkUnitDetails(UnitCafeData.region,UnitCafeData.id)"-->
                                                                </td>
                                                                <td ng-class="{'success': unitAllObj[UnitCafeData.id] == true}">{{UnitCafeData.name}}</td>
                                                            </tr>
                                                            <tr style="display:none">
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitUnits(filteredCafeListBrandWise)">
                                                                            Submit
                                                                            Units
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjRegionListDiv"
                                             style="display: none">
                                            <table style="display: none" class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th>Region List&nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat='fulSelObjRegion in selectedRegions'>
                                                    <td>{{fulSelObjRegion.name}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjUnitListDiv"
                                             style="display: none">
                                            <table style="display: none" class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th>Unit &nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat='fulSelObjUnits in selectedUnits'>
                                                    <td>{{fulSelObjUnits.name}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showPrevTab()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showUnits()">Next
                                </button>
                            </div>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab22}"
                                 id="CustomerTab" style="display: none">
                                <div>
                                    <input type="radio" ng-model="csvs.name" value="phoneNo"
                                           ng-click="showCustomerClk('phoneNo')"> <label>
                                    upload CSV with Customer Phone No </label> <span style="display: none"
                                                                                     id="dvPhoneNoCust"><input
                                        type="file"
                                        file-reader="fileContent"/></span>
                                </div>
                                <br>
                                <br>
                                <div align="right">
                                    <a href="img/customer.csv" download> <b>CSV format for
                                        phone no download </b>
                                    </a>
                                </div>
                                <div align="center">OR</div>
                                <div>
                                    <input type="radio" ng-model="csvs.name" value="customerID"
                                           ng-click="showCustomerClk('custID')"> <label>upload
                                    CSV with Customer ID</label> <span style="display: none"
                                                                       id="dvCustIDCust"><input type="file"
                                                                                                file-reader="fileContent"/></span>
                                </div>
                                <div align="right">
                                    <a href="img/customerID.csv" download> <b>CSV format
                                        for customer id download </b>
                                    </a>
                                </div>
                                <br>
                                <table class="table table-bordered">
                                    <thead style="background-color: #50773e; color: #ffffff">
                                    <th>Customer &nbsp;</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat='fulSelObjUnits in fileContent'>
                                        <td>{{fulSelObjUnits.value}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <button class="btn btn-primary" type="button"
                                        ng-click="prevCustomer()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showCustomers()">Next
                                </button>
                            </div>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab22}"
                                 id="ProductsTab" style="display: none">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="panel-group" id="accordion" role="tablist"
                                             aria-multiselectable="true" ng-init="init()">
                                            <div class="panel panel-default"
                                                 ng-repeat="fulSelObjCat in fullSelectedObjCategory">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getCategoryDataMap(fulSelObjCat.value)">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{categoryListView}}"
                                                                aria-expanded="false"
                                                                aria-controls="collapseOne">
                                                            {{fulSelObjCat.value}}<i class="pull-right glyphicon"
                                                                                     ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="{{fulSelObjCat.value}}"
                                                     class="panel-collapse collapse" role="tabpanel"
                                                     aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Name&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat='allDmsCat in uniqueDimensionNames'>
                                                                <td><input type="checkbox"
                                                                           ng-model="allDmsCat.checked"></td>
                                                                <td>{{allDmsCat.name}}</td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitCategoryDimension(uniqueDimensionNames,fulSelObjCat.code,fulSelObjCat.categoryName)">
                                                                            Submit
                                                                            Dimension
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default"
                                                 ng-repeat="fulSelObjSubCat in fullSelectedObjSubCategory">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getSubCategoryDataMap(fulSelObjSubCat.id)">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{fulSelObjSubCat.id}}"
                                                                aria-expanded="false" aria-controls="collapseOne">
                                                            {{fulSelObjSubCat.subCategoryName}}<i
                                                                class="pull-right glyphicon"
                                                                ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="{{fulSelObjSubCat.id}}"
                                                     class="panel-collapse collapse" role="tabpanel"
                                                     aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Name&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat='subCatDimName in uniqueDimensionNames'>
                                                                <td><input type="checkbox"
                                                                           ng-model="subCatDimName.checked"></td>
                                                                <td>{{subCatDimName.name}}</td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitSubCategoryDimension(uniqueDimensionNames,fulSelObjSubCat.id,fulSelObjSubCat.subCategoryName)">
                                                                            Submit
                                                                            Sub Category
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default"
                                                 ng-repeat="fulSelObjProd in fullSelectedObjProduct | orderBy : 'productName'">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getProductDataMap(fulSelObjProd.id,fulSelObjProd.dimensionID)">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{fulSelObjProd.id}}"
                                                                aria-expanded="false"
                                                                aria-controls="collapseOne">
                                                            {{fulSelObjProd.productName}}<i
                                                                class="pull-right glyphicon"
                                                                ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="{{fulSelObjProd.id}}"
                                                     class="panel-collapse collapse" role="tabpanel"
                                                     aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Name&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr
                                                                    ng-repeat='dimensionNameList in dimensionNameListShow'>
                                                                <td ng-class="{'success': dimensionNameList.checked == true}"><input type="checkbox"
                                                                           ng-model="dimensionNameList.checked"></td>
                                                                <td ng-class="{'success': dimensionNameList.checked == true}">{{dimensionNameList.name}}</td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitProductDimension(dimensionNameListShow,fulSelObjProd.id,fulSelObjProd.productName)">
                                                                            Submit
                                                                            Product
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjCatDimDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th colspan="3">Category &nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjCatDimen in fullSelectedObjCateDimension'>
                                                    <td>{{fulSelObjCatDimen.dimension}}</td>
                                                    <td>{{fulSelObjCatDimen.name}}</td>
                                                    <td>
                                                        <div
                                                                ng-click="delCatDimension(fulSelObjCatDimen.dimension,fulSelObjCatDimen.value)"
                                                                style="color: red">X
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjSubCatDimDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th colspan="3">Sub Category&nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjSubCatDimen in fullSelectedObjSubCateDimension'>
                                                    <td>{{fulSelObjSubCatDimen.dimension}}</td>
                                                    <td>{{fulSelObjSubCatDimen.name}}</td>
                                                    <td>
                                                        <div
                                                                ng-click="delSubCatDimension(fulSelObjSubCatDimen.dimension,fulSelObjSubCatDimen.value)"
                                                                style="color: red">X
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjProductDimDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th colspan="3">Product&nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjProdDim in fullSelectedObjProductDimension'>
                                                    <td>{{fulSelObjProdDim.dimension}}</td>
                                                    <td>{{fulSelObjProdDim.name}}</td>
                                                    <td>
                                                        <div
                                                                ng-click="delProductDim(fulSelObjProdDim.dimension,fulSelObjProdDim.value)"
                                                                style="color: red">X
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="button"
                                        ng-click="prevProduct()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showProduct()">Next
                                </button>
                            </div>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab22}"
                                 id="OrderSourceTab" style="display: none">
                                <h3>Select Order Source</h3>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="panel-group" id="accordion" role="tablist"
                                             aria-multiselectable="true" ng-init="init()">
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getOrderSourceData('orderSourceList')">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{orderSourceDatas}}"
                                                                aria-expanded="false"
                                                                aria-controls="collapseOne">
                                                            Order Source ({{fullSelectedObjOrderSource.length}})<i class="pull-right glyphicon"
                                                                           ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="orderSourceList" class="panel-collapse collapse"
                                                     role="tabpanel" aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Order Source&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat='orderSourceListsData in families | orderBy'>
                                                                <td ng-class="{'success': orderSourceObj[orderSourceListsData] == true}">
                                                                    <input type="checkbox"
                                                                           ng-model="orderSourceObj[orderSourceListsData]"
                                                                           ng-click="selectOrderSource(orderSourceListsData,orderSourceObj[orderSourceListsData])">
                                                                </td>
                                                                <td ng-class="{'success': orderSourceObj[orderSourceListsData] == true}">{{orderSourceListsData}}</td>
                                                            </tr>
                                                            <tr style="display:none">
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitOrderSource()">
                                                                            Submit
                                                                            Order Source
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjOrderSourceDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th>Order Source List &nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjOrderSource in fullSelectedObjOrderSource'>
                                                    <td>{{fulSelObjOrderSource.value}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="button"
                                        ng-click="prevOrdSour()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showOrderSource()">Next
                                </button>
                            </div>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab22}"
                                 id="ChannelPartnerTab" style="display: none">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="panel-group" id="accordion" role="tablist"
                                             aria-multiselectable="true" ng-init="init()">
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getChannelPartnerData('channelPartnerList')">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{partnerChannel}}" aria-expanded="false"
                                                                aria-controls="collapseOne">
                                                            Channel Partner ({{fullSelectedObjChannelPartner.length}})<i class="pull-right glyphicon"
                                                                              ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="channelPartnerList" class="panel-collapse collapse"
                                                     role="tabpanel" aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Channel Partner&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr
                                                                    ng-repeat='channelPartnerListsData in channelPartner | orderBy : "name"'>
                                                                <td ng-class="{'success':channelPartObj[channelPartnerListsData.id] == true}">
                                                                    <input type="checkbox"
                                                                            ng-click="selectChannelPartner(channelPartnerListsData,channelPartObj[channelPartnerListsData.id])"
                                                                           ng-model="channelPartObj[channelPartnerListsData.id]">
                                                                </td>
                                                                <td ng-class="{'success':channelPartObj[channelPartnerListsData.id] == true}">{{channelPartnerListsData.name}}</td>
                                                            </tr>
                                                            <tr style="display:none">
                                                                <td colspan="2" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitChannelPartner(channelPartner)">
                                                                            Submit
                                                                            Channel Partner
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjChannelPartnerDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th>Channel Partner List &nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjChannelPartner in fullSelectedObjChannelPartner | orderBy : "name"'>
                                                    <td>{{fulSelObjChannelPartner.name}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="button"
                                        ng-click="prevChaPar()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showChannelPartner()">Next
                                </button>
                            </div>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab22}"
                                 id="PaymentModeTab" style="display: none">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="panel-group" id="accordion" role="tablist"
                                             aria-multiselectable="true" ng-init="init()">
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getPaymentModeData('paymentModeList')">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{paymentModeLists}}"
                                                                aria-expanded="false"
                                                                aria-controls="collapseOne">
                                                            Payment Mode ({{fullSelectedObjPaymentMode.length}})<i class="pull-right glyphicon"
                                                                           ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="paymentModeList" class="panel-collapse collapse"
                                                     role="tabpanel" aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>Payment Mode&nbsp;</th>
                                                            <th>Type&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat='paymentModeListsData in paymentMode | orderBy : "name"'>
                                                                <td ng-class="{'success':paymentModeObj[paymentModeListsData.id] == true}">
                                                                    <input type="checkbox"
                                                                           ng-click="selectPaymentModes(paymentModeListsData,paymentModeObj[paymentModeListsData.id])"
                                                                           ng-model="paymentModeObj[paymentModeListsData.id]">
                                                                </td>
                                                                <td ng-class="{'success':paymentModeObj[paymentModeListsData.id] == true}">{{paymentModeListsData.name}}</td>
                                                                <td ng-class="{'success':paymentModeObj[paymentModeListsData.id] == true}">{{paymentModeListsData.settlementType}}</td>
                                                            </tr>
                                                            <tr style="display: none;">
                                                                <td colspan="3" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitPaymentMode(paymentMode)">
                                                                            Submit
                                                                            Payment
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjPaymentModeDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th>Payment Mode List &nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjPayMode in fullSelectedObjPaymentMode'>
                                                    <td>{{fulSelObjPayMode.name}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="button"
                                        ng-click="prevPayMode()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showPaymentMode()">Next
                                </button>
                            </div>
                            <div role="tabpanel" class="tab-pane" ng-class="{active: !tab23}"
                                 id="newCustomerTab" style="display: none">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="panel-group" id="accordion" role="tablist"
                                             aria-multiselectable="true" ng-init="init()">
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="headingOne"
                                                     ng-click="getNewCustomerModeData('newCustomerModeList')">
                                                    <h4 class="panel-title">
                                                        <button data-toggle="collapse" data-parent="#accordion"
                                                                data-target="#{{newCustomerModeLists}}"
                                                                aria-expanded="false" aria-controls="collapseOne">
                                                            New Customer<i class="pull-right glyphicon"
                                                                           ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i>
                                                        </button>
                                                    </h4>
                                                </div>
                                                <div id="newCustomerModeList"
                                                     class="panel-collapse collapse" role="tabpanel"
                                                     aria-labelledby="headingOne">
                                                    <div class="panel-body">
                                                        <table class="table table-bordered">
                                                            <thead style="background-color: #50773e; color: #ffffff">
                                                            <th>Check &nbsp;</th>
                                                            <th>New Customer&nbsp;</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr
                                                                    ng-repeat='newCustomerListModeListsData in newCustomerArray'>
                                                                <td ng-class="{'success':newCustomerArray.value1 == true}">
                                                                    <input type="checkbox"
                                                                           ng-click="selectNewCustomer(newCustomerArray.value1)"
                                                                           ng-model="newCustomerArray.value1"></td>
                                                                <td ng-class="{'success':newCustomerArray.value1 == true}">Yes</td>
                                                            </tr>
                                                            <tr style="display:none">
                                                                <td colspan="3" align="right">
                                                                    <div>
                                                                        <button class="btn btn-primary" type="button"
                                                                                ng-click="selectedSubmitNewCustomer()">
                                                                            Submit
                                                                            New Customer
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" id="fullSelectedObjNewCustomerModeDiv"
                                             style="display: none">
                                            <table class="table table-bordered">
                                                <thead style="background-color: #50773e; color: #ffffff">
                                                <th>New Customer &nbsp;</th>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat='fulSelObjNewCustomer in fullSelectedObjNewCustomer'>
                                                    <td>{{fulSelObjNewCustomer.name}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="button"
                                        ng-click="prevNewCustomer()">Prev
                                </button>
                                <button class="btn btn-primary" type="button"
                                        ng-click="showNewCustomer()">Next
                                </button>
                            </div>
                        </div>
                        <div role="tabpanel" class="tab-pane" ng-class="{active: !tab6}"
                             id="SummaryCoupons">
                            <table class="table table-bordered">
                                <br>
                                <tr style="background-color: #50773e; color: #ffffff">
                                    <th colspan="4" align="center">Offer Summary&nbsp;</th>
                                </tr>
                                <tr>
                                    <td><b>Category</b> :
                                        {{fullCompleteCouponsObj.offer.category}}
                                    </td>
                                    <td><b>Offer Reason</b> :
                                        {{fullCompleteCouponsObj.offer.text}}
                                    </td>
                                    <td><b>Description</b> :
                                        {{fullCompleteCouponsObj.offer.description}}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Type</b> : {{fullCompleteCouponsObj.offer.type}}</td>
                                    <td><b>Start Date </b> : <span>{{fullCompleteCouponsObj.offer.startDate
											| date:'yyyy-MM-dd'}}</span></td>
                                    <td><b>End Date </b> :
                                        {{fullCompleteCouponsObj.offer.endDate | date:'yyyy-MM-dd'}}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b> Minimum Value </b> :
                                        {{fullCompleteCouponsObj.offer.minValue}}
                                    </td>
                                    <td><b> Quantity <Limit></Limit> </b> :
                                        {{fullCompleteCouponsObj.offer.minQuantity}}
                                    </td>
                                    <td><b> Minimum Item Count </b> :
                                        {{fullCompleteCouponsObj.offer.minItemCount}}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b> Offer Value </b> :
                                        {{fullCompleteCouponsObj.offer.offerValue}}
                                    </td>
                                    <td><b>OfferScope </b> :
                                        {{fullCompleteCouponsObj.offer.offerScope}}
                                    </td>
                                    <td><b> Status </b> :
                                        {{fullCompleteCouponsObj.offer.status}}
                                    </td>
                                </tr>
                                <tr style="background-color: #50773e; color: #ffffff">
                                    <th colspan="4" align="center">Offer Meta Data
                                        Description&nbsp;
                                    </th>
                                </tr>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                </tr>
                                <tr
                                        ng-repeat='fullMetaDataMapping in fullCompleteCouponsObj.offer.metaDataMappings'>
                                    <td>{{fullMetaDataMapping.value}}</td>
                                    <td>{{fullMetaDataMapping.name}}</td>
                                    <td>{{fullMetaDataMapping.status}}</td>
                                </tr>
                                <tr style="background-color: #50773e; color: #ffffff">
                                    <th colspan="4" align="center">Partners&nbsp;</th>
                                </tr>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                </tr>
                                <tr
                                        ng-repeat='fullMetaDataPartnersMap in fullCompleteCouponsObj.offer.partners'>
                                    <td>{{fullMetaDataPartnersMap.name}}</td>
                                    <td>{{fullMetaDataPartnersMap.type}}</td>
                                    <td>{{fullMetaDataPartnersMap.status}}</td>
                                </tr>
                                <tr style="background-color: #50773e; color: #ffffff">
                                    <th colspan="4" align="center">Coupons&nbsp;</th>
                                </tr>
                                <tr>
                                    <td class="text-uppercase"><b>Code : </b>{{fullCompleteCouponsObj.code}}</td>
                                    <td class="text-uppercase"><b>Prefix : </b>{{fullCompleteCouponsObj.prefix}}</td>
                                    <td><b>Max Usage :</b>{{fullCompleteCouponsObj.maxUsage}}</td>
                                </tr>
                                <tr>
                                    <td><b>Start Date : </b>{{fullCompleteCouponsObj.startDate}}</td>
                                    <td><b>End Date :</b>{{fullCompleteCouponsObj.endDate}}</td>
                                    <td><b>Reusable :</b>{{fullCompleteCouponsObj.reusable}}</td>
                                </tr>
                                <tr>
                                    <td><b>Reusable By Customer :</b>{{fullCompleteCouponsObj.reusableByCustomer}}</td>
                                    <td><b> Customer Visibility </b> : {{fullCompleteCouponsObj.customerVisibility}}</td>
                                </tr>
                                <tr style="background-color: #50773e; color: #ffffff">
                                    <th colspan="4" align="center">Coupons Mapping&nbsp;</th>
                                </tr>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Dimension</th>
                                </tr>
                                <tr
                                        ng-repeat='fullCouponsMapping in fullCompleteCouponsObj.couponMappingList'>
                                    <td>{{fullCouponsMapping.name}}</td>
                                    <td>{{fullCouponsMapping.type}}</td>
                                    <td>{{fullCouponsMapping.dimension}}</td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <button class="btn btn-primary"
                                                type="button" ng-click="prevCouponsMapping()">Prev
                                        </button>
                                        <button class="btn btn-primary" type="button"
                                                ng-if="fullCompleteCouponsObj.id==null"
                                                ng-click="submitFinalCouponsMapp()">ADD COUPONS
                                            MAPPING
                                        </button>
                                        <button class="btn btn-primary" type="button"
                                                ng-if="fullCompleteCouponsObj.id!=null"
                                                ng-click="submitFinalCouponsMappUpdate(fullCompleteCouponsObj)">UPDATE
                                            COUPONS MAPPING
                                        </button>
                                        <button class="btn btn-primary" type="button"
                                                ng-if="isCloning == true"
                                                ng-click="submitFinalCouponsMappUpdate()">Clone Offer
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div role="tabpanel" class="tab-pane" ng-hide="editMode" ng-class="{active: !tab7}"
                             id="ReplicateCouponsTab" style="display: none">
                            <div style="font-size: 15px; color: #50773e" align="center"
                                 ng-if="resultMessage=='success'">
                                <b> Successfully !! coupons mapping added </b>
                            </div>
                            <br>
                            <div style="font-size: 15px; color: red" align="center"
                                 ng-if="resultMessage=='failed'">
                                <b> Failed !! coupons mapping not added </b>
                            </div>
                            <div align="right" ng-hide="editMode">
                                <button class="btn btn-primary" type="button"
                                        ng-click="displayReplicateCoupons()">Need Replicate
                                    Coupons
                                </button>
                            </div>
                            <br>
                            <div align="right">
                                <button class="btn btn-primary" type="button"
                                        ng-click="closedModalOnly()">Finish Mapping
                                </button>
                            </div>
                            <br>
                            <div id="replicateCouponsDisplayDiv" style="display: none">
                                <div class="form-group">
                                    <label><b> Prefix</b>-</label> {{prefixCoupons}} <label><b>
                                    Coupons</b> -</label>{{fullCompleteCouponsObj.code}} <br>
                                    <br> <label> Coupons Count </label> <input type="number"
                                                                               class="form-control" string-to-number
                                                                               ng-model="replicateCount"/>
                                </div>
                                <div align="right">
                                    <button class="btn btn-primary" type="button"
                                            ng-click="showRepCoupMode(prefixCoupons,fullCompleteCouponsObj.code)">Submit
                                        Replicate Coupons
                                    </button>
                                </div>
                                <div>
                                    <button ng-click="exportData()">Export</button>
                                    <div id="exportable">
                                        <table width="100%">
                                            <thead>
                                            <tr>
                                                <th>Name</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr ng-repeat="item in couponsList">
                                                <td>{{item}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div id="allcouponsMappingListDetailsDiv" style="display: none">
    <div align="left" style="padding-top: 8px">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="backOfferCoupons()">
            <i class="fa fa-arrow-left   fw"></i> Back
        </button>
    </div>
    <div align="left" style="padding-top: 40px">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="allCouponsMappingView()">
            <i class="fa fa-map-marker"></i> Mapping View
        </button>
    </div>
    <div align="left" style="padding-top: 40px">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="openBulkCouponUpdateModal()">
                Bulk Update Coupon
        </button>
    </div>
    <div align="left" style="padding-top: 40px">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="openOfferDescriptionMetadataModal()">
            Update Description Metadata
        </button>
    </div>
    <div align="left" style="padding-top: 40px">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="refreshCouponsDetails()">
            Refresh
        </button>
    </div><br/>
    <div class="row">
        <div class="col-lg-4">
            Search: <input type="text" ng-model="search" ng-change="filter()"
                           placeholder="Search Offer" class="form-control"/>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12" ng-if="filteredItemsCouponsList > 0">
            <p>Filtered {{ couponsListData.length }} of {{
                totalItemsCoupons}} total results</p>
            <div class="row">
                <div class="col-xs-12" style="overflow: scroll; height: 1000px">
                    <table class="table table-striped table-bordered"
                           style="font-size: 9px">
                        <thead style="background-color: #286090; color: #ffffff">
<!--                        <th>Check &nbsp; <input type="checkbox"-->
<!--                                                ng-model='allCheckedDetails' style="width: 20px; height: 20px"-->
<!--                                                ng-true-value="'YES'" ng-false-value="'NO'"-->
<!--                                                ng-change="checkAllCoupons(allCheckedDetails)"></th>-->
                        <th>Coupons&nbsp;</th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Status <select
                                            class="form-control" style="width: 125px; height: 37px"
                                            ng-model="selectOfferStatus"
                                            ng-options="offerStatusType.name for offerStatusType in basicInfoStatus track by offerStatusType.name"
                                            ng-change="showCouponsStatus(selectOfferStatus)"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="checkboxModel.value"
                                               ng-click="checkedAllCouponStatusApply(checkboxModel.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Start Date</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="datepicker" date-format="yyyy-MM-dd">
                                            <input class="form-control" ng-model="startDate"
                                                   placeholder="click here" type="text"
                                                   ng-change="showStartDate(startDate)"/>
                                        </div>
                                        <input type="checkbox" style="width: 33px; height: 20px"
                                               ng-model="checkboxStartDateModel.value"
                                               ng-click="checkedStartDateAllApply(checkboxStartDateModel.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'">
                                    </td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">End Date</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="datepicker" date-format="yyyy-MM-dd">
                                            <input class="form-control" ng-model="endDate"
                                                   placeholder="click here" type="text"
                                                   ng-change="showEndDate(endDate)"/>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="checkboxEndDateModel.value"
                                               ng-click="checkedEndDateAllApply(checkboxEndDateModel.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Reusable</td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="center"><select
                                            class="form-control" style="width: 90px; height: 37px"
                                            ng-model="reusableStatusData"
                                            ng-options="reusableStatusType.name for reusableStatusType in reusableStatus track by reusableStatusType.name"
                                            ng-change="showReusableCoupons(reusableStatusData)"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="left"><input type="checkbox"
                                                                        style="width: 33px; height: 20px"
                                                                        ng-model="checkboxReusableModel.value"
                                                                        ng-click="checkedReusableAllApply(checkboxReusableModel.value)"
                                                                        ng-true-value="'YES'" ng-false-value="'NO'">
                                    </td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Customer Reusable <select
                                            class="form-control" style="width: 90px; height: 37px"
                                            ng-model="reusableCustomerStatusData"
                                            ng-options="reusableCustomerStatusType.name for reusableCustomerStatusType in reusableCustomerStatus track by reusableCustomerStatusType.code"
                                            ng-change="showReusableCustomerCoupons(reusableCustomerStatusData)"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="checkboxReusableCustomerModel.value"
                                               ng-click="checkedReusableCustomerAllApply(checkboxReusableCustomerModel.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="center">Max Usage</td>
                                </tr>
                                <tr>
                                    <td><input type="text"
                                               style="width: 50px; height: 37px; color: black"
                                               ng-model="updateMaxUsage"
                                               ng-change="showMaxUsage(updateMaxUsage)"/> <input
                                            type="checkbox" style="width: 33px; height: 20px"
                                            ng-model="checkboxMaxUsageModel.value"
                                            ng-click="checkedMaxUsageAllApply(checkboxMaxUsageModel.value)"
                                            ng-click="checkedMaxUsageAllApply(checkboxReusableCustomerModel.value)"
                                            ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="center">Manual Overiding <select
                                            class="form-control" style="width: 90px; height: 37px"
                                            ng-model="manualOverridingData"
                                            ng-options="manualOverridinType.name for manualOverridinType in manualOverrideStatus track by manualOverridinType.name"
                                            ng-change="showManualOveridingCoupons(manualOverridingData)"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="checkboxManualOverideModel.value"
                                               ng-click="checkedManualOverideAllApply(checkboxManualOverideModel.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>Usage&nbsp;</th>
                        <th>Customer Visibility</th>
                        <th>Action</th>
                        </thead>
                        <tbody>
                        <tr
                                ng-repeat="couponsListView in filtered = (couponsListData | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                            <!-- 	                        <td>{{couponsListView.id}}<input type="checkbox" ng-model='allCheckedDetails1' style="width:20px; height:20px" ng-true-value="'YES'" ng-false-value="'NO'"  ng-click="checkSingleCoupons(couponsListView.id,couponsListView.code,couponsListView.couponMappingList,couponsListView.offer,couponsListView.mapping,couponsListView.status,couponsListView.startDate,couponsListView.endDate,couponsListView.reusable,couponsListView.reusableByCustomer,couponsListView.maxUsage,couponsListView.manualOverride,couponsListView.usage)"> </td>
-->
<!--                            <td><input type="checkbox"-->
<!--                                       ng-model='coup[couponsListView.id].checked'-->
<!--                                       ng-click="checkSingleCoupons(couponsListView.id,couponsListView.code,couponsListView.couponMappingList,couponsListView.offer,couponsListView.mapping,couponsListView.status,couponsListView.startDate,couponsListView.endDate,couponsListView.reusable,couponsListView.reusableByCustomer,couponsListView.maxUsage,couponsListView.manualOverride,couponsListView.usage)">-->
<!--                            </td>-->
                            <td
                                    ng-click="showAllCouponsMap(couponsListView.couponMappingList)"><a
                                    href="">{{couponsListView.code}}</a><span style="color: red">-({{couponsListView.couponMappingList.length}})</span>
                            </td>
                            <td>{{couponsListView.status}}</td>
                            <td>{{couponsListView.startDate | date:'yyyy-MM-dd'}}</td>
                            <td>{{couponsListView.endDate | date:'yyyy-MM-dd'}}</td>
                            <td>{{couponsListView.reusable}}</td>
                            <td>{{couponsListView.reusableByCustomer}}</td>
                            <td>{{couponsListView.maxUsage}}</td>
                            <td>{{couponsListView.manualOverride}}</td>
                            <td>{{couponsListView.usage}}</td>
                            <td>{{couponsListView.customerVisibility}}</td>
                            <!-- <td align="left" ng-click="addMoreMappingCoupons(couponsListData,couponsListView.id)"> <a href="">Add Mapping </a> </td> -->
                            <td align="left"
                                ng-click="updateCouponsDetails(couponsListView.code)"><a
                                    href=""> Edit </a></td>
                        </tr>
                        <tr>
                            <td colspan="12">
                                <div align="right">
                                    <button class="btn btn-primary pull-right" data-toggle=""
                                            ng-click="updateBasicCouponsDetails()">
                                        <i class="fa fa-pencil-square"></i> update
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-lg-10" ng-if="filteredItems == 0">
                    <h4>No results found</h4>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="couponsListMapModals" tabindex="-1"
         role="dialog" aria-labelledby="couponsListMapModals">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="partnerLabel">Coupons Mapping List</h4>
                </div>
                <table class="table table-striped table-bordered"
                       style="font-size: 11px">
                    <thead>
                    <th>ID&nbsp;</th>
                    <th>Type&nbsp;</th>
                    <th>Dimension&nbsp;</th>
                    <th>Group&nbsp;</th>
                    <th>Min Value&nbsp;</th>
                    <th>Status&nbsp;</th>
                    <th>Value&nbsp;</th>
                    </thead>
                    <tbody>
                    <tr ng-repeat="CouponMap in allCouponsMap">
                        <td>{{CouponMap.id}}</td>
                        <td>{{CouponMap.type}}</td>
                        <td>{{CouponMap.dimension}}</td>
                        <td>{{CouponMap.group}}</td>
                        <td>{{CouponMap.minValue}}</td>
                        <td>{{CouponMap.status}}</td>
                        <td>{{CouponMap.value}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="CouponsMappingsViewDiv" style="display: none">
    <div align="left" style="padding-top: 8px">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="backBasicCouponsDetails()">
            <i class="fa fa-plus fw"></i> Back
        </button>
    </div>
    <div class="row">
        <div class="col-xs-12" ng-if="filteredItemsCouponsList > 0">
            <div class="row">
                <div class="col-xs-12" style="overflow: scroll; height: 1000px">
                    <table class="table table-striped table-bordered"
                           style="font-size: 9px">
                        <thead style="background-color: #286090; color: #ffffff">
                        <!--                         <th>Check &nbsp;   <input type="checkbox" ng-model='allMappingCouponsCheck' style="width:20px; height:20px" ng-true-value="'YES'" ng-false-value="'NO'"  ng-change="checkAllCouponsMapping(allMappingCouponsCheck)"></th>
-->
                        <th>Coupons&nbsp;</th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Region
                                        <!-- <select class="form-control" style="width:125px; height:37px" ng-model="selectOfferStatus" ng-options="offerStatusType.name for offerStatusType in basicInfoStatus track by offerStatusType.name" ng-change="showCouponsStatus(selectOfferStatus)"></select> -->
                                        <div isteven-multi-select input-model="regionDetailList"
                                             output-model="anyOutput" button-label="icon name"
                                             item-label="icon name maker" tick-property="ticked"
                                             ng-click="showRegionCheckBox()"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="chkboxRegion.value"
                                               ng-click="checkedAllCouponMappingRegionApply(chkboxRegion.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Order Source</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div isteven-multi-select input-model="familyDetailList"
                                             output-model="anyOutput" button-label="icon name"
                                             item-label="icon name maker" tick-property="ticked"
                                             ng-click="showFamilyCheckBox()"></div>
                                        <input
                                                type="checkbox" style="width: 33px; height: 20px"
                                                ng-model="chkboxFamily.value"
                                                ng-click="checkedAllCouponMappingOrderSourceApply(chkboxFamily.value)"
                                                ng-true-value="'YES'" ng-false-value="'NO'">
                                    </td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Channel Partner</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div isteven-multi-select input-model="channelDetailList"
                                             output-model="anyOutput" button-label="icon name"
                                             item-label="icon name maker" tick-property="ticked"
                                             ng-click="showChannelPartnerCheckBox()"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="chkboxChannel.value"
                                               ng-click="checkedAllCouponMappingChannelApply(chkboxChannel.value)"
                                               ng-true-value="'YES'" ng-false-value="'NO'"></td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">Payment Mode</td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="center">
                                        <div isteven-multi-select input-model="paymentDetailList"
                                             output-model="anyOutput" button-label="icon name"
                                             item-label="icon name maker" tick-property="ticked"
                                             ng-click="showPaymentCheckBox()"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="left"><input type="checkbox"
                                                                        style="width: 33px; height: 20px"
                                                                        ng-model="chkboxPayment.value"
                                                                        ng-click="checkedAllCouponMappingPaymentApply(chkboxPayment.value)"
                                                                        ng-true-value="'YES'" ng-false-value="'NO'">
                                    </td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table width="100%" style="font-size: 9px">
                                <tr>
                                    <td colspan="2" align="left">New Customer</td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="center">
                                        <div isteven-multi-select input-model="newCustomerArrayData"
                                             output-model="anyOutput" button-label="icon name"
                                             item-label="icon name maker" tick-property="ticked"
                                             ng-click="showNewCustomers()"></div>
                                    </td>
                                    <!-- <select class="form-control" style="width:90px; height:37px"  ng-model="customerN" ng-options="customerNewData.name for customerNewData in newCustomerArrayData track by customerNewData.code" ng-change="showNewCustomer()"></select>
                       </td>  -->
                                </tr>
                                <tr>
                                    <td><input type="checkbox"
                                               style="width: 33px; height: 20px"
                                               ng-model="chkboxNewCustomer.value"
                                               ng-click="checkedAllCouponMappingNewCustomerApply(chkboxNewCustomer.value)"
                                               ng-true-value="'Y'" ng-false-value="'N'"></td>
                                </tr>
                            </table>
                        </th>
                        </thead>
                        <tbody>
                        <!--  <tr ng-repeat="couponsListView in unitRegionWiseMap"> -->
                        <tr ng-repeat="(key, value) in unitRegionWiseMap">
                            <!-- 	                        <td><input type="checkbox"  ng-model='coupMap[couponsListView.id].checked' ng-click="checkSingleCouponsMapping(couponsListView.id,couponsListView.code,couponsListView.couponMappingList,couponsListView.offer,couponsListView.mapping,couponsListView.status,couponsListView.startDate,couponsListView.endDate,couponsListView.reusable,couponsListView.reusableByCustomer,couponsListView.maxUsage,couponsListView.manualOverride,couponsListView.usage)"></td>
-->
                            <td>{{key}}</td>
                            <td>
                                <table>
                                    <tr ng-repeat="(keys, values) in value.UNIT_REGION">
                                        <td ng-if="values.status!='IN_ACTIVE'">{{values.value}}</td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tr ng-repeat="(keys, values) in value.ORDER_SOURCE">
                                        <td ng-if="values.status!='IN_ACTIVE'">{{values.value}}</td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tr ng-repeat="(keys, values) in value.CHANNEL_PARTNER">
                                        <td ng-if="values.status!='IN_ACTIVE'">{{values.value}}</td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tr ng-repeat="(keys, values) in value.PAYMENT_MODE">
                                        <td ng-if="values.status!='IN_ACTIVE'">{{values.value}}</td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tr ng-repeat="(keys, values) in value.NEW_CUSTOMER">
                                        <td ng-if="values.status!='IN_ACTIVE'">{{values.value}}</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="12">
                                <div align="right">
                                    <button class="btn btn-primary pull-right" data-toggle=""
                                            ng-click="updatefullMappingCouponsDetails()">
                                        <i class="fa fa-plus fw"></i> update
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-lg-10" ng-if="filteredItems == 0">
                    <h4>No results found</h4>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="couponsInCoupons" style="display: none">
    <div align="left">
        <button class="btn btn-primary pull-right" data-toggle=""
                ng-click="backCouponsDetail()">
            <i class="fa fa-plus fw"></i> Back
        </button>
    </div>
    <div class="row">
        <div class="col-lg-4">
            Search: <input type="text" ng-model="search" ng-change="filter()"
                           placeholder="Search" class="form-control"/>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12" ng-if="filteredItems > 0">
            <p>Filtered {{ allCouponsMap.length }} of {{ totalItems}} total
                results</p>
            <div class="row">
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>ID &nbsp; <a ng-click="sort_by('id');"><i
                                class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Type&nbsp; <a ng-click="sort_by('code');"><i
                                class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Value&nbsp;</th>
                        <th>Dimension&nbsp;</th>
                        <th>Group&nbsp;</th>
                        <th>Status&nbsp;</th>
                        <th align="center">Status Change&nbsp;</th>
                        </thead>
                        <tbody>
                        <tr
                                ng-repeat="allCoupMap in filtered = (allCouponsMap | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                            <td>{{allCoupMap.id}}</td>
                            <td>{{allCoupMap.type}}</td>
                            <td>{{allCoupMap.value}}</td>
                            <td>{{allCoupMap.dimension}}</td>
                            <td>{{allCoupMap.group}}</td>
                            <td>{{allCoupMap.status}}</td>
                            <td><img
                                    ng-click="changeStatusMappingList(allCouponsMap,allCoupMap.status,allCoupMap.id)"
                                    ng-if="allCoupMap.status!='IN_ACTIVE'"
                                    style="margin-bottom: 8px; cursor: pointer" title="In_Active"
                                    ng-src="img/activeCat.png" height="25px" width="25px">&nbsp;&nbsp;
                                <img
                                        ng-click="changeStatusMappingList(allCouponsMap,allCoupMap.status,allCoupMap.id)"
                                        ng-if="allCoupMap.status!='ACTIVE'"
                                        style="margin-bottom: 8px; cursor: pointer" title="Active"
                                        ng-src="img/inactiveCat.png" height="25px" width="25px">&nbsp;&nbsp;
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-lg-10" ng-if="filteredItems == 0">
                    <h4>No results found</h4>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="offerCouponsDetailsModal" tabindex="-1"
     role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 950px">
            <div class="modal-header"
                 style="background-color: #337ab7; color: white">
                <button type="button" class="close" data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Offer and Coupon
                    Summary</h4>
            </div>
            <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                <table class="table table-striped table-bordered"
                       style="font-size: 11px">
                    <thead style="background-color: #e7e7e7">
                    <tr>
                        <td colspan="14" style="background-color: #e7e7e7"><b><h3>Offer</h3></b>
                        </td>
                    </tr>
                    <tr>
                        <th>Cat&nbsp;</th>
                        <th>Desc&nbsp;</th>
                        <th>Offer Reason&nbsp;</th>
                        <th>Offer Type&nbsp;</th>
                        <th>Start Date&nbsp;</th>
                        <th>End Date&nbsp;</th>
                        <th>Inc Taxes&nbsp;</th>
                        <th>Min Item Count&nbsp;</th>
                        <th>Min Qty&nbsp;</th>
                        <th>Min Value&nbsp;</th>
                        <th>Offer Scope&nbsp;</th>
                        <th>Offer Value&nbsp;</th>
                        <th>Validate Customer&nbsp;</th>
                        <th>Status&nbsp;</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="CouponMap in fullOfferObj">
                        <td>{{CouponMap.category}}</td>
                        <td>{{CouponMap.description}}</td>
                        <td>{{CouponMap.text}}</td>
                        <td>{{CouponMap.type}}</td>
                        <td>{{CouponMap.startDate | date:'yyyy-MM-dd'}}</td>
                        <td>{{CouponMap.endDate | date:'yyyy-MM-dd'}}</td>
                        <td>{{CouponMap.includeTaxes}}</td>
                        <td>{{CouponMap.minItemCount}}</td>
                        <td>{{CouponMap.minQuantity}}</td>
                        <td>{{CouponMap.minValue}}</td>
                        <td>{{CouponMap.offerScope}}</td>
                        <td>{{CouponMap.offerValue}}</td>
                        <td>{{CouponMap.validateCustomer}}</td>
                        <td>{{CouponMap.status}}</td>
                    </tr>
                    </tbody>
                </table>
                <div class="col-xs-12" ng-if="mappingLength > 0">
                    <table class="table table-striped table-bordered"
                           style="font-size: 11px">
                        <thead style="background-color: #e7e7e7">
                        <tr>
                            <td colspan="14" style="background-color: #e7e7e7"><b><h3>Coupons
                                Mapping</h3></b></td>
                        </tr>
                        <tr>
                            <th>Type&nbsp;</th>
                            <th>Value&nbsp;</th>
                            <th>Status&nbsp;</th>
                        </tr>
                        </thead>
                        <tr
                                ng-repeat="couponDetailsMapping in offerCouponsList[0].couponMappingList">
                            <td>{{couponDetailsMapping.type}}</td>
                            <td>{{couponDetailsMapping.value}}</td>
                            <td>{{couponDetailsMapping.status}}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-xs-12" ng-if="mappingLength == 0">
                    <table class="table table-striped table-bordered"
                           style="font-size: 11px">
                        <thead style="background-color: #e7e7e7">
                        <tr>
                            <th colspan="14"><h4>Coupons Mapping</h4></th>
                        </tr>
                        <tr>
                            <td>Couopons mapping not found</td>
                        </tr>
                        </thead>
                    </table>
                </div>
                <table class="table table-striped table-bordered"
                       style="font-size: 11px">
                    <thead style="background-color: #e7e7e7">
                    <tr>
                        <td colspan="14" style="background-color: #e7e7e7"><b><h3>Coupons</h3></b>
                        </td>
                    </tr>
                    <tr>
                        <th>Code&nbsp;</th>
                        <th>Start Date&nbsp;</th>
                        <th>End Date&nbsp;</th>
                        <th>Manual Overide&nbsp;</th>
                        <th>Max Usage&nbsp;</th>
                        <th>Reusable&nbsp;</th>
                        <th>Reusable By Customer&nbsp;</th>
                        <th>Status&nbsp;</th>
                    </tr>
                    </thead>
                    <tr ng-repeat="couponDetails in offerCouponsList">
                        <td>{{couponDetails.code}}</td>
                        <td>{{couponDetails.startDate | date:'yyyy-MM-dd'}}</td>
                        <td>{{couponDetails.endDate | date:'yyyy-MM-dd'}}</td>
                        <td>{{couponDetails.manualOverride}}</td>
                        <td>{{couponDetails.maxUsage}}</td>
                        <td>{{couponDetails.reusable}}</td>
                        <td>{{couponDetails.reusableByCustomer}}</td>
                        <td>{{couponDetails.status}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="bulkCouponUpdateModal" tabindex="-1"
     role="dialog" aria-labelledby="myModalLabel" >
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 650px">
            <div class="modal-header"
                 style="background-color: #337ab7; color: white">
                <button type="button" class="close" data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModal">Update Bulk Coupons</h4>
            </div>
            <div class="modal-body" id="modal-body">
                <div class="form-group" >
                    <label>Reusable Applicability (In Days)</label>
                    <input type="number" class="form-control" min="0" string-to-number
                           ng-model="couponApplicability"/>
                </div>
                <div class="form-group" >
                    <label>Customer Visibility</label>
                    <select type="text" class="form-control" ng-model="customerVisibility">
                        <option value="Y">Yes</option>
                        <option value="N">No</option>
                    </select>
                </div>

                <!-- Toggle Button for Day Schedule Settings -->
                <div class="form-group">
                    <label>
                        <input type="checkbox" ng-model="enableDaySchedule" ng-change="toggleScheduleDaySettings()">
                        Enable Day Schedule Settings
                    </label>
                </div>

                <!-- Schedule Settings (shown when toggle is enabled) -->
                <div ng-show="enableDaySchedule">
                    <div class="form-group">
                        <label>Select Days</label>
                        <div ng-dropdown-multiselect
                             options="daysOfWeekOptions"
                             selected-model="selectedDays"
                             extra-settings="multiSelectSettingsForDays"
                             class="multi-select">
                        </div>
                    </div>
                </div>

                <!-- Toggle Button for Time Schedule Settings -->
                <div class="form-group">
                    <label>
                        <input type="checkbox" ng-model="enableTimeSchedule" ng-change="toggleScheduleTimeSettings()">
                        Enable Time Schedule Settings
                    </label>
                </div>
                <div ng-show="enableTimeSchedule">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Start Time</label>
                                <input type="time" class="form-control" ng-model="startTime"
                                       ng-change="validateTimeRange()"/>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>End Time</label>
                                <input type="time" class="form-control" ng-model="endTime"
                                       ng-change="validateTimeRange()"/>
                            </div>
                        </div>
                    </div>

                    <!-- Time validation messages -->
                    <div ng-show="timeValidationError" class="alert alert-danger">
                        {{timeValidationMessage}}
                    </div>
                </div>

                <div align="right">
                    <button class="btn btn-primary" type="button"
                            ng-click="updateBulkCoupons()">Update
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="offerDescriptionMetadataModal" tabindex="-1"
     role="dialog" aria-labelledby="myModalLabel" >
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 850px">
            <div class="modal-header"
                 style="background-color: #337ab7; color: white">
                <button type="button" class="close" data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModal">Update Description Metadata</h4>
            </div>
            <div class="modal-body" id="modal-body">
                <div class="form-group">
                    <label>Add Description & Sequence</label>
                    <div ng-repeat="tnc in offerDescriptionMetadata track by $index">
                        <div style="display: inline-block; width: 100%; margin: 3px">
                                <select style="width: 20%; float: left; margin-left: 3px; margin-right: 3px" class="form-control"
                                        ng-model="offerDescriptionMetadata[$index].descriptionType">
                                    <option value="" disabled selected>Metadata Type</option>
                                    <option value="TEXT">TEXT</option>
                                    <option value="IMAGE">IMAGE</option>
                                    <option value="VIDEO">VIDEO</option>
                                    <option value="GIF">GIF</option>
                                </select>
                                <input class="form-control" placeholder="Metadata Value" type="text" style="width: 29%; float: left; margin-left: 3px margin-right: 3px" ng-model="offerDescriptionMetadata[$index].descriptionValue"/>
                                <input class="form-control" placeholder="Color" type="text" style="width: 15%; float: left;margin-left: 3px; margin-right: 3px" ng-model="offerDescriptionMetadata[$index].color"/>
                                <input class="form-control" placeholder="Font Size" type="text" style="width: 15%; float: left;margin-left: 3px; margin-right: 3px" ng-model="offerDescriptionMetadata[$index].fontSize"/>
                            <select style="width: 11%; float: left;margin-left: 3px; margin-right: 3px" class="form-control" ng-model="offerDescriptionMetadata[$index].isBold">
                                <option value="" disabled selected>Is Bold</option>
                                <option value="Y">YES</option>
                                <option value="N">NO</option>
                            </select>

                            <button class="btn btn-danger" ng-if="offerDescriptionMetadata.length != 1" ng-click='deleteDescriptionMetadata($index)'>-</button>
                            <button class="btn btn-primary" ng-if="$index == offerDescriptionMetadata.length-1" ng-click='addDescriptionMetadata($index)'>+</button>
                        </div>
                    </div>
                </div>
                <div align="right">
                    <button class="btn btn-primary" type="button"
                            ng-click="updateOfferDescriptionMetadata()">Update
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    $(document).ready(function () {
        $('pre code').each(function (i, block) {
            hljs.highlightBlock(block);
        });
    });
</script>