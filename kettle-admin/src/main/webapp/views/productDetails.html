<style xmlns="http://www.w3.org/1999/html">
    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }

    * {
        box-sizing: border-box;
    }

    .container {
        position: relative;
        display: flex;
        flexDirection: column;
        height: 85vh;
        width: 100%;
        margin-right: 16px;
        padding: 4px;
    }

    .text-container {
        flex: 1;
        overflow: auto;
        height: 100%;
        width: 100%;
    }

    .fab {
        position: absolute;
        right: 20px;
        bottom: 20px;
    }
</style>

    <div data-ng-init="init()">
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-xs-12">
                <p data-ng-if="!isProductRecipeMapping" class="title">Product Details</p>
                <p data-ng-if="isProductRecipeMapping" class="title">Product Recipe Details</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Unit Type</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select class="form-control" data-ng-model="prodDetails.unitType">
                            <option data-ng-repeat="unit in unitType | orderBy" value="{{unit}}">
                                {{unit}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Brand</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select class="form-control" data-ng-model="selectedBrand" data-ng-change="filterProductsBrandWise()">
                            <option data-ng-repeat="brand in brandList | orderBy" value="{{brand.brandId}}">
                                {{brand.brandName}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Select Product</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select data-ui-select2 class="form-control" style="width: 100% !important"
                                data-ng-model="prodDetails.productId" data-placeholder="Select a product"
                                data-ng-change="changeProduct(prodDetails.productId)">
                            <option value=""></option>
                            <option data-ng-repeat="product in productsInfo" value="{{product.id}}">
                                {{product.name + " : " + product.id + " - " + product.status}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Dimension</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <select class="form-control" data-ng-model="prodDetails.dimensionId"
                                data-ng-change="changeDimension()">
                            <option data-ng-repeat="dimension in productDimension.content" value="{{dimension.id}}">
                                {{dimension.name}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Pricing Profile</label>
                    </div>
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                         options="pricingProfileNames" selected-model="selectedPricingProfiles" class="col-xs-4 region-card">
                    </div>
                </div>
            </div>
        </div>

        <div data-ng-if="trimmedRegions.length > 0">
            <div class="row" style="margin-top: 10px; margin-bottom: 10px;">
                <div class="col-xs-4">
                    <label class="control-label">MultiRegion Select</label>
                </div>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                        options="trimmedRegions" selected-model="storeSelectedRegion" class="col-xs-4 region-card">
                </div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Select City</label>
                </div>
                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForCities"
                        options="filteredCities" selected-model="selectedCities" class="col-xs-4 region-card">
                </div>
            </div>
            <div class="col-xs-4 region-card">
                <button data-ng-click="getAllUnitProductMapping()" class="btn btn-primary">Get Details</button>
                <button data-ng-click="submitDetails()" class="btn btn-primary pull-right">SUBMIT</button>
            </div>
        </div>
    </div>
    <div class="row container"
         data-ng-if="productListDetails != null && productListDetails.length > 0">
        <div class="col-xs-12 text-container">
            <table class="table table-bordered">
                <thead style="background-color: #50773e; color: #ffffff">
                <tr>
                    <th>Check &nbsp;
                        <input type="checkbox" data-ng-model='checkBoxModal.checkAll'
                               style="width: 20px; height: 20px" data-ng-click="updateAll()">
                    </th>
                    <th>Unit Name&nbsp;
                        <input
                                type="text"
                                data-ng-model="unitName"
                                placeholder="enter Unit Name to filter row"
                                class="form-control"/>
                    </th>
                    <th data-ng-if="isProductRecipeMapping">
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Profile</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                            data-ng-model="checkBoxModal.updatedProfile">
                                        <option data-ng-repeat="profile in recipeProfiles | orderBy"
                                                value="{{profile}}">
                                            {{profile}}
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllProfile"
                                           data-ng-click="changeAllProfile(checkBoxModal.updatedProfile)">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <th data-ng-if="isProductRecipeMapping">
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Status</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                            data-ng-model="checkBoxModal.updatedStatus">
                                        <option data-ng-repeat="status in pricingStatusList"
                                                value="{{status.value}}">
                                            {{status.name}}
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllStatus"
                                           data-ng-click="changeAllStatus(checkBoxModal.updatedStatus)">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <!--For product alias-->
                    <th data-ng-if="!isProductRecipeMapping">
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Product Alias</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <input type="text" class="form-control" style="width: 100px; margin-left: 65px;"
                                           data-ng-model="checkBoxModal.updatedProductAlias"/>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllAlias" data-ng-click="changeAllAlias()">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <!--for dimension description-->
                    <th data-ng-if="!isProductRecipeMapping">
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Dimension Description</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <input type="text" class="form-control" style="width: 100px; margin-left: 65px;"
                                           data-ng-model="checkBoxModal.updatedDimensionDescription"/>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllDescription"
                                           data-ng-click="changeAllDescription()">
                                </td>
                            </tr>
                        </table>
                    </th>
                    <!--for delivery only products -->
                    <th data-ng-if="isProductRecipeMapping">
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="3" align="center">Delivery Only Product</td>
                            </tr>
                            <tr>
                                <td style="width: 10px; height: 10px">&nbsp;Current&nbsp;</td>
                                <td>
                                    <select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                            data-ng-model="checkBoxModal.updatedDeliveryOnlyProducts">
                                        <option data-ng-repeat="status in isDeliveryOnlyProductMap"
                                                value="{{status.value}}">
                                            {{status.name}}
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <input type="checkbox" style="width: 33px; height: 20px"
                                           data-ng-model="checkBoxModal.checkAllDeliveryOnlyProducts"
                                           data-ng-click="changeAllDeliveryOnlyProducts(checkBoxModal.updatedDeliveryOnlyProducts)">
                                </td>
                            </tr>
                        </table>
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="detail in productListDetails|orderBy : 'unit.name' | filter : unitName"
                    data-ng-class="{'row-selected': prodDetails[detail.unit.id].checked}">

                    <td><input type="checkbox" style="width: 33px; height: 20px"
                               data-ng-model='prodDetails[detail.unit.id].checked'
                               data-ng-click="changeRow(prodDetails[detail.unit.id].checked, prodDetails[detail.unit.id], detail.price)">
                    </td>
                    <td>{{detail.unit.name}}</td>
                    <td data-ng-if="isProductRecipeMapping">
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.price.profile}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-model="prodDetails[detail.unit.id].profile">
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option data-ng-repeat="profile in recipeProfiles | orderBy"
                                                value="{{profile}}">{{profile}}
                                        </option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td>
<!--                    for Status -->
                    <td data-ng-if="isProductRecipeMapping">
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.price.status}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-model="prodDetails[detail.unit.id].status">
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option data-ng-repeat="status in pricingStatusList | orderBy"
                                                value="{{status.value}}">{{status.name}}
                                        </option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td data-ng-if="!isProductRecipeMapping">
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.price.aliasProductName}}</td>
                                <td style="width: 100px;">
                                    <input class="form-control" data-ng-model="prodDetails[detail.unit.id].alias"/>
                                </td>
                                <!-- data-ng-change="changePrice(prodDetails[detail.unit.id].price, detail)" -->
                            </tr>
                        </table>
                    </td>
                    <td data-ng-if="!isProductRecipeMapping">
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.price.dimensionDescriptor}}</td>
                                <td style="width: 100px;">
                                    <input class="form-control"
                                           data-ng-model="prodDetails[detail.unit.id].description"/>
                                </td>
                                <!-- data-ng-change="changePrice(prodDetails[detail.unit.id].price, detail)" -->
                            </tr>
                        </table>
                    </td>
                    <td data-ng-if="isProductRecipeMapping">
                        <table>
                            <tr>
                                <td style="width: 125px; height: 20px">{{detail.price.isDeliveryOnlyProduct}}</td>
                                <td style="width: 100px;">
                                    <select class="form-control" data-ng-model="prodDetails[detail.unit.id].isDeliveryOnlyProduct">
                                        <!-- data-ng-change="changeProfile(prodDetails[detail.unit.id].profile, detail)" -->
                                        <option data-ng-repeat="status in isDeliveryOnlyProductMap | orderBy"
                                                value="{{status.value}}">{{status.name}}
                                        </option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right" colspan="8">

                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <button class="btn btn-primary pull-right fab" data-ng-click="submitDetails()">
            SUBMIT
        </button>
    </div>

    <div class="modal fade" id="productDetailsModal" tabindex="-1"
         role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="font-size: 10px; width: 860px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="myModalLabel">Summary: Product
                        List For Region {{selectedRegion}}</h4>
                </div>

                <div class="modal-body">
                    <div>
                        <button acl-action-checker="ADMN_PPU_MGT" class="btn btn-primary pull-right"
                                data-ng-click="submitUpdatePriceList()">Update
                        </button>
                    </div>
                    <br>
                    <br>
                    <br>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Unit Name&nbsp;</th>
                            <th>Product&nbsp;</th>
                            <th>Dimension&nbsp;</th>
                            <th>Current Profile</th>
                            <th>New Profile</th>
                            <th>Current Status</th>
                            <th>New Status</th>
                            <th>Current Product Name</th>
                            <th>New Product Alias</th>
                            <th>Current Dimesnsion Description</th>
                            <th>New Dimension Description</th>
                            <th>Current IsDeliveryOnlyProduct</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat='detail in requestObject'>
                            <td>{{$index+1}}</td>
                            <td>{{detail.unit.name}}</td>
                            <td>{{detail.product.name}}</td>
                            <td>{{detail.price.dimension}}</td>
                            <td>{{detail.price.currentProfile}}</td>
                            <td>{{detail.price.profile}}</td>
                            <td>{{detail.price.currentStatus}}</td>
                            <td>{{detail.price.status}}</td>
                            <td>{{detail.price.currentAliasProductName}}</td>
                            <td>{{detail.price.aliasProductName}}</td>
                            <td>{{detail.price.currentDimensionDescriptor}}</td>
                            <td>{{detail.price.dimensionDescriptor}}</td>
                            <td>{{detail.price.isDeliveryOnlyProduct}}</td>
                        </tr>
                        <tr>
                            <td colspan="14" align="right">
                                <div
                                        class="form-group clearfix">
                                    <button acl-action-checker="ADMN_PPU_MGT" class="btn btn-primary pull-right"
                                            data-ng-click="submitUpdatePriceList()">Update
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
