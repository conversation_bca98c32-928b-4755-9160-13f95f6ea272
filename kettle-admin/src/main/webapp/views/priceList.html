
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	 <h1 class="page-header"> Product Price   </h1>
    </div>

<div class="col-xs-12">
<div class="form-group">                                          
<label>Type *</label>  
<select class="form-control" ng-model="newUnitFamily">
<option ng-repeat="productPriceDataList in families | orderBy" value="{{productPriceDataList}}">{{productPriceDataList}}</option> 
</select>
</div>

<div class="form-group">                                          
<label>Selects Region *</label>  
<select class="form-control" ng-model="newUnitRegion">
<option ng-repeat="region in regions | orderBy" value="{{region}}">{{region}}</option> 
</select>
</div>	
	<div class="form-group">
	    <label>Category*</label>
	    <select class="form-control" ng-model="selectProductType" ng-options="selectedCat as selectedCat.detail.name for selectedCat in productCategory track by selectedCat.detail.id" ng-change="showCategoryDetails(selectProductType.content)" >
	    </select> 
	</div>
	
	<div class="form-group">
	 <label>Sub Category *</label>
	 <select class="form-control" ng-model="selectProductSubtype" ng-options="selectedSubCat as selectedSubCat.name for selectedSubCat in selectedProductSubTypeData track by selectedSubCat.id" ng-change="showSubCategoryDetails(selectProductSubtype.id)">
	 </select> 
	</div> 


	<div class="form-group">
	 <label>Product *</label>
	<select class="form-control" ng-model="selectProductDetail" ng-options="productDetailsList as productDetailsList.name for productDetailsList in allProductList track by productDetailsList.id" ng-change="showDimensionDetails(selectProductDetail.dimensionProfileId)">
	
	</select>  
	</div>

<div class="form-group">
 <label>Dimension *</label>
<select class="form-control" ng-model="selectProductDimension1" ng-options="dimensionData as dimensionData.name for dimensionData in selectProductDimensionData1 track by dimensionData.id">
</select>  
</div>

<div class="col-lg-12"><br>
	<div style align="right"><button class="btn btn-primary pull-right" ng-click="showPriceDataList()">SUBMIT</button></div>
	
</div><br><br><br>

<loading style align="center"></loading><br>
<div id="viewProductPriceDiv" style="display:none">
<style>
.pending-delete { background-color: #CCC }
</style>
<table class="table table-bordered" ng-style="{color: myColor}">
                    <thead style="background-color:#50773e; color:#ffffff">
                        <th>Check &nbsp;   <input type="checkbox" ng-model='allChecked' style="width:20px; height:20px" ng-true-value="'YES'" ng-false-value="'NO'"  ng-click="checkAll()">  </th> 
                        <th>Unit Name&nbsp;</th>
                        <th>
                        <table  width="100%" >
                        <tr>
                        		<td colspan="2" align="center">Price</td>
                        </tr>
                        <tr>
	                        <td style="width:10px; height:10px">&nbsp;Current&nbsp;</td>
	                        <td>
		                        <input type="text"  style="width: 50px; color:#000000"  ng-model="updatePriceInput"/>
		                        <input type="checkbox" style="width:33px; height:20px" ng-model="checkboxModel" ng-click="checkedAllApply(checkboxModel)" ng-true-value="'YES'" ng-false-value="'NO'">
		                      </td>
                        </tr>
                        </table>
                        
                       <!--  Price&nbsp;<br>Current<input type="text" style="color:#000000" class="col-sm" ng-model="updatePriceInput"/>&nbsp;&nbsp;&nbsp;
                         <input type="checkbox" style="width:20px; height:20px" ng-model="checkboxModel.value" ng-click="checkedAllApply(checkboxModel.value)" ng-true-value="'YES'" ng-false-value="'NO'"> -->
           </th>
           <th>
           <table  width="100%">
                        <tr>
                        	<td colspan="2" align="center">Cafe Cost</td>
                        </tr>
                        <tr><td style="width:10px; height:10px">&nbsp;Current&nbsp; </td>
                        	<td>
                        		<input type="text"  style="width: 55px; color:#000000"  ng-model="updateCafeCost"/>
                        		<input type="checkbox" style="width:33px; height:20px" ng-model="checkboxCafeModel" ng-click="checkedCafeAllApply(checkboxCafeModel)" ng-true-value="'YES'" ng-false-value="'NO'">
                        	</td>
                        </tr>
                        </table>
           </th>
           <th>
           <table  width="100%">
                        <tr>
                        	<td colspan="2" align="center">Cod Cost</td>
                        </tr>
                        <tr><td style="width:10px; height:10px">&nbsp;Current&nbsp;</td>
                        	<td>
                        		<input type="text"  style="width: 55px; color:#000000"  ng-model="updateCodCost"/>
                        		<input type="checkbox" style="width:33px; height:20px" ng-model="checkboxCodModel" ng-click="checkedCodAllApply(checkboxCodModel)" ng-true-value="'YES'" ng-false-value="'NO'">
                        	</td>
                        </tr>
           </table>
           </th>
           <th>
           <table  width="100%">
                        <tr><td colspan="2" align="center">Buffer</td> </tr>
                        <tr><td style="width:10px; height:10px">&nbsp;Current&nbsp;</td>
                        	<td>
                        		<input type="text"  style="width: 55px; color:#000000"  ng-model="updateBufferCost"/>
                        		<input type="checkbox" style="width:33px; height:20px" ng-model="checkboxBufferModel" ng-click="checkedBufferAllApply(checkboxBufferModel)" ng-true-value="'YES'" ng-false-value="'NO'">
                        	</td>
                        </tr>
           </table>
           </th>
           <th>
           <table  width="100%">
                <tr>
                	<td colspan="2" align="center">Threshold</td>
                </tr>
	               <tr><td style="width:10px; height:10px">&nbsp;Current&nbsp;</td>
	                   	 <td>
	                    	<input type="text"  style="width: 50px; color:#000000"  ng-model="updateThreshold"/>
	                    		<input type="checkbox" style="width:33px; height:20px" ng-model="checkboxThresholdModel" ng-click="checkedThresholdAllApply(checkboxThresholdModel)" ng-true-value="'YES'" ng-false-value="'NO'">
	                     </td>
	               </tr>
           </table>
           </th>
        	</thead>
               <tbody>
             		<tr ng-repeat='detDatas in productListDetails'  ng-class="{'pending-delete': sel[detDatas.unit.id]}"> 
             		
             			<td><input type="checkbox"  ng-model='sel[detDatas.unit.id].checked' ng-click="checkDataOnly(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.unit.id,detDatas.product.id,detDatas.price.id,detDatas.price.cost,detDatas.price.codCost,detDatas.price.buffer,detDatas.price.threshold)"></td>
                        <td>{{detDatas.unit.name}}</td>
                        <td>
                        <table>
                        <tr><td style="width:125px; height:20px">{{detDatas.price.price}}</td>
                        <td style="width: 100px;"><input class="form-control" string-to-number type="number"  min="0" ng-model="productUpdatedPrice[detDatas.unit.id]"  ng-change="changeUpdatePrice(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.price.dimension,detDatas.product.name,detDatas.unit.id,detDatas.product.id,detDatas.price.id,detDatas.price.cost,detDatas.price.codCost,detDatas.price.buffer,detDatas.price.threshold)" /> </td></tr>
                        </table>
                        </td> 
                        <td>
                        <table>
                        <tr><td style="width:125px; height:20px">{{detDatas.price.cost}}</td>
                        <td style="width: 100px;"><input class="form-control" string-to-number type="number" min="0"  ng-model="cafeCost[detDatas.unit.id]" ng-change="changeCafePrice(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.price.dimension,detDatas.product.name,detDatas.unit.id,detDatas.product.id,detDatas.price.id,detDatas.price.cost,detDatas.price.codCost,detDatas.price.buffer,detDatas.price.threshold)" /> </td></tr>
                        </table>
                        </td>
                    	<td>
                    	<table>
                        <tr><td style="width:125px; height:20px">{{detDatas.price.codCost}}</td>
                        <td style="width: 100px;">
                        <input class="form-control" string-to-number type="number" min="0" ng-model="codCost[detDatas.unit.id]" ng-change="changeCodPrice(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.price.dimension,detDatas.product.name,detDatas.unit.id,detDatas.product.id,detDatas.price.id,detDatas.price.cost,detDatas.price.codCost,detDatas.price.buffer,detDatas.price.threshold)">
                        </table>
                        </td>
                        
                       <td>
                    	<table>
                        <tr><td style="width:125px; height:20px">{{detDatas.price.buffer}}</td>
                        <td style="width: 100px;">
                        <input class="form-control" string-to-number type="number" min="0" ng-model="bufferValue[detDatas.unit.id]" ng-change="changeBufferValue(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.price.dimension,detDatas.product.name,detDatas.unit.id,detDatas.product.id,detDatas.price.id,detDatas.price.cost,detDatas.price.codCost,detDatas.price.buffer,detDatas.price.threshold)">
                        </table>
                        </td>
                        
                        <td>
                    	<table>
                        <tr><td style="width:125px; height:20px">{{detDatas.price.threshold}}</td>
                        <td style="width: 100px;">
                        <input class="form-control" string-to-number type="number" min="0" ng-model="threshValue[detDatas.unit.id]" ng-change="changeThresholdValue(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.price.dimension,detDatas.product.name,detDatas.unit.id,detDatas.product.id,detDatas.price.id,detDatas.price.cost,detDatas.price.codCost,detDatas.price.buffer,detDatas.price.threshold)">
                        </table>
                        </td>
<!--                     	<input class="form-control" string-to-number type="number" ng-model="cafeCost[detDatas.unit.id]" ng-change="changeUpdatePrice(detDatas.id,detDatas.price.id,detDatas.unit.name,detDatas.price.price,sel[detDatas.unit.id].checked,detDatas.price.dimension,detDatas.product.name,detDatas.unit.id,detDatas.product.id,detDatas.price.id)" /> </td> 
 -->                    </tr>
                    <tr><td align="right" colspan="8"><button class="btn btn-primary pull-right" ng-click="viewProductPriceList()">SUBMIT</button></div></td></tr>
                </tbody>
 </table>
 </div>
 </div>
 
 <div class="modal fade" id="productPriceModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content" style="font-size:10px;width: 820px;">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"> Summary: Product Price List </h4>
          </div>
         
          <div class="modal-body">
              <table class="table table-striped table-bordered">
                    <thead>
                    <th>S.No</th>
                        <th>Unit Name&nbsp;</th>
                        <th>Dimension&nbsp;</th>
                        <th>Product&nbsp;</th>
                        <th>Current Price</th>
                        <th>Update Price</th>
                        <th>Old Cafe Price</th>
                        <th>Update Cafe Price</th>
                        <th>Current Cod Price</th>
                        <th>Update Cod Price</th>
                        <th>old Buffer</th>
                        <th>change Buffer</th>
                        <th>old Threshold</th>
                        <th>change Threshold </th>
                    </thead>
                <tbody>
                    <tr ng-repeat='productPriceDisplay in productFinalList'> 
                    <td>{{$index+1}}</td>
                        <td>{{productPriceDisplay.unintNamee}}</td>
                        <td>{{productPriceDisplay.dimension}}</td>
                        <td>{{productPriceDisplay.product}}</td>
                        <td>{{productPriceDisplay.currentPrices}}</td>
                         <td>{{productPriceDisplay.price}}</td>
                         <td>{{productPriceDisplay.cafeCosts}}</td>
                         <td>{{productPriceDisplay.changeCafecosts}}</td>
                         <td>{{productPriceDisplay.codCosts}}</td>
                         <td>{{productPriceDisplay.changeCodCosts}}</td>
                         <td>{{productPriceDisplay.buffer}}</td>
                         <td>{{productPriceDisplay.changeBuffer}}</td>
                         <td>{{productPriceDisplay.threshold}}</td>
                         <td>{{productPriceDisplay.changeThreshold}}</td>
                    </tr>
                    <tr><td colspan="14" align="right"><div class="form-group clearfix">
                  <button class="btn btn-primary pull-right"  ng-click="submitUpdatePriceList(productFinalList)">Update Price {{buttonNotDisplay}}</button>
              </div></td></tr>
                </tbody>
                </table>
          </div>
     </div>
  </div>
</div>
</div>
        

