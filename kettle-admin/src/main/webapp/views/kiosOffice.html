<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	
        <h1 class="page-header">
        Office 
            <button class="btn btn-primary pull-right" data-toggle="modal" id="addOfficeIdDiv" ng-click="addKiosOffice()"><i class="fa fa-plus fw"></i> Add Office</button>
        </h1>
        
    </div>
</div>


<div class="form-group">
 <label>Company Name *</label>
 <select class="form-control" ng-model="selectedOfficeCompanyNameList" ng-options="selectedCompanyNameData as selectedCompanyNameData.companyName for selectedCompanyNameData in kiosCompanyList track by selectedCompanyNameData.companyName" ng-change="showCompanyOfficeList(selectedOfficeCompanyNameList)" >
 </select> 
 </div>
 
<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ companyAllOfficeList.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                         <th>ID &nbsp;<a ng-click="sort_by('id');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Office Name&nbsp;<a ng-click="sort_by('name');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Office Short Code&nbsp;</th>
                        <th>Payment Mode&nbsp;</th>
                        <th>Region&nbsp;</th>
                        <th>Tin&nbsp;</th>
                         <th align="center" >Status&nbsp;</th>
                        <th align="center" colspan="2">Action&nbsp;</th>
                        
                    </thead>
                <tbody>
                    <tr ng-repeat="officeDatas in filtered = (companyAllOfficeList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                        <td>{{officeDatas.officeId}}</td>
                        <td>{{officeDatas.officeName}}</td>
                        <td>{{officeDatas.officeShortCode}}</td>
                        <td>{{officeDatas.paymentMode}}</td>
                        <td>{{officeDatas.region}}</td>
                        <td>{{officeDatas.tin}}</td>
                         <td>{{officeDatas.officeStatus}}</td>
                         <td><span style="display:inline-block; margin:10px,0"><button class="btn btn-primary pull-right"   ng-if="officeDatas.officeStatus=='IN_ACTIVE'" ng-click="OfficeStatusChange('ACTIVE',officeDatas.officeId)">Activate</button></span>
	                        <span style="display:inline-block; margin:10px,0"><button class="btn btn-primary pull-right"   ng-if="officeDatas.officeStatus=='ACTIVE'" ng-click="OfficeStatusChange('IN_ACTIVE',officeDatas.officeId)">Deactivate</button></span>
	                    
	                       <span> <button class="btn btn-primary pull-right"   ng-click="editOffice(officeDatas.officeId)">Edit</button></span>
						</td> 
                   
                    </tr>
                </tbody>
                </table>
            </div>
            
        </div>
        
    </div>
    
    <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4 align="center">No results found</h4>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="officeModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header" style="background-color:#337ab7;color:white">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"  id="myModalLabel">{{action}} Office</h4>
          </div>
          <div class="modal-body">
              <form name="addOfficeForm" style="line-height:30px" novalidate>
              <div class="row">
              <div class="col-xs-8">
                  <label style="font-size:11px">Office Name *</label>
                  <input type="text" class="form-control" placeholder="Office Name" ng-model="officeName" required />
              </div>
              
              <div class="col-xs-4">
                  <label style="font-size:11px"> Short Code *</label>
                  <input type="text" class="form-control" placeholder="Office ShortCode" ng-model="officeShortCode" required />
              </div>
              
              <div class="col-xs-4">
                 <label style="font-size:11px">Payment Mode </label>
                 <select class="form-control" ng-model="selectedPaymentMode" ng-options="paymentModeData as paymentModeData.name for paymentModeData in paymentMode track by paymentModeData.name"></select>
              </div>
              
              <div class="col-xs-4">
                 <label style="font-size:11px">Region *</label>
                    <select class="form-control" ng-model="newUnitRegion">
                        <option ng-repeat="region in regions" value="{{region}}">{{region}}</option>
                    </select>
             	</div>
             
              <div class="col-xs-4" >
                  <label style="font-size:11px">Tin *</label>
                  <input type="text" class="form-control" placeholder="Tin" ng-model="officeTin" required />
              </div>
              
              
              <!-- <div class="col-xs-12" style="margin-bottom:20px">
                 <label style="font-size:11px">Company Name </label>
                 <select class="form-control" ng-model="selectedOfficeCompanyName" ng-options="officeCompanyNameData as officeCompanyNameData.companyName for officeCompanyNameData in kiosCompanyList track by selectedOfficeCompanyName"></select>
              </div>
              </div> -->
              
              
			 <div class="col-xs-12" style="margin-bottom:20px">
				 <label>Company Name *</label>
				 <select class="form-control" ng-model="selectedOfficeCompanyName" ng-options="selectedCompanyViewData as selectedCompanyViewData.companyName for selectedCompanyViewData in kiosCompanyList track by selectedCompanyViewData.companyName" ng-change="showCompanyViewList(selectedOfficeCompanyName)" >
				 </select> 
			</div>
			</div>
			          
              <div class="panel panel-default">
                  <div class="panel-heading">
                      <b>Contact Detail </b>
                  </div>
	                  <div class="panel-body">
		                      <div class="table-responsive">
		                      		<div class="col-xs-4">
		                              <input class="form-control" ng-model="officeContactName" placeholder="Contact Name" required />
		                          	</div>
		                          
		                          	<div class="col-xs-4">
		                              <input class="form-control" ng-model="officeContactEmail" placeholder="Contact email" required />
		                         	 </div>
		                          <div class="col-xs-4">
		                              <input class="form-control" ng-model="officeContactNumber" placeholder="Contact number" required />
		                          </div>
		                  	</div>
	              	 </div>
              </div>
              
              <div class="panel panel-default">
                  <div class="panel-heading">
                      <b>Office Address </b>
                  </div>
                  <div class="panel-body">
                      <div class="table-responsive">
                          <div class="col-xs-12">
                              <label style="font-size:11px">line 1 *</label>
                              <input class="form-control" ng-model="officeline1" placeholder="address1" required />
                          </div>
                          <div class="col-xs-12">
                              <label style="font-size:11px">line 2</label>
                              <input class="form-control" ng-model="officeline2" placeholder="address2"/>
                          </div>
                          <div class="col-xs-4">
                              <label style="font-size:11px">line 3</label>
                              <input class="form-control" ng-model="officeline3" placeholder="address1"/>
                          </div>
                          
                          <div class="col-xs-4">
                              <label style="font-size:11px">Locality </label>
                              <input class="form-control" ng-model="officeLocality" placeholder="locality"/>
                          </div>
                          
                          <div class="col-xs-4">
                			  <label style="font-size:11px">City *</label>
                              <input class="form-control" ng-model="officeCity" placeholder="city"/>
                          </div>
                          
                          <div class="col-xs-4">
                              <label style="font-size:11px">Zip code *</label>
                              <input class="form-control" placeholder="Zip" type="number" my-maxlength="6" string-to-number ng-model="officeZipCode" />
                          </div>
                          <div class="col-xs-4">
           					  <label style="font-size:11px">Contact 1 *</label>
                			  <input class="form-control" placeholder="contact Number" type="number" my-maxlength="10" string-to-number ng-model="officeContact1" />
        				  </div>
                          <div class="col-xs-4">
                             <label style="font-size:11px">Contact 2</label>
                              <input class="form-control" type="number" my-maxlength="10" string-to-number ng-model="officeContact2"/>
                          </div>
                          <div class="col-xs-4">
                               <label style="font-size:11px">Latitude *</label>
                                  <input class="form-control" placeholder="Lattitue" string-to-number type="number" maxlength="18" min="0" ng-model="officeLatitude"/>
                           </div>
                            <div class="col-xs-4">
                              <label style="font-size:11px">Longitude *</label>
                              <input class="form-control" placeholder="Longitude" string-to-number type="number" maxlength="18" min="0" ng-model="officeLongitude"/>           </div>
                      		</div>
                      		
                      		<div class="col-xs-12">
                              <label style="font-size:11px">State *</label>  
                              <select class="form-control" ng-model="officeState" required >
                                 <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                  <option value="Andhra Pradesh">Andhra Pradesh</option>
                                  <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                  <option value="Assam">Assam</option>
                                  <option value="Bihar">Bihar</option>
                                  <option value="Chandigarh">Chandigarh</option>
                                  <option value="Chhattisgarh">Chhattisgarh</option>
                                  <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                  <option value="Daman and Diu">Daman and Diu</option>
                                  <option value="Delhi">Delhi</option>
                                  <option value="Goa">Goa</option>
                                  <option value="Gujarat">Gujarat</option>
                                  <option value="Haryana">Haryana</option>
                                  <option value="Himachal Pradesh">Himachal Pradesh</option>
                                  <option value="Jammu and Kashmir">Jammu and Kashmir</option>
                                  <option value="Jharkhand">Jharkhand</option>
                                  <option value="Karnataka">Karnataka</option>
                                  <option value="Kerala">Kerala</option>
                                  <option value="Lakshadweep">Lakshadweep</option>
                                  <option value="Madhya Pradesh">Madhya Pradesh</option>
                                  <option value="Maharashtra">Maharashtra</option>
                                  <option value="Manipur">Manipur</option>
                                  <option value="Meghalaya">Meghalaya</option>
                                  <option value="Mizoram">Mizoram</option>
                                  <option value="Nagaland">Nagaland</option>
                                  <option value="Odisha">Odisha</option>
                                  <option value="Puducherry">Puducherry</option>
                                  <option value="Punjab">Punjab</option>
                                  <option value="Rajasthan">Rajasthan</option>
                                  <option value="Sikkim">Sikkim</option>
                                  <option value="Tamil Nadu">Tamil Nadu</option>
                                  <option value="Telangana">Telangana</option>
                                  <option value="Tripura">Tripura</option>
                                  <option value="Uttar Pradesh">Uttar Pradesh</option>
                                  <option value="Uttarakhand">Uttarakhand</option>
                                  <option value="West Bengal">West Bengal</option>
                              </select>
                          </div>
                      <div class="form-group">&nbsp;</div>
                          
               <div class="form-group clearfix" ng-if="action=='Add'" ng-hide="showEmpBtn">
                  <button class="btn btn-primary btn-lg btn-block" ng-click="submitAddOffice()">Add</button>
          	 </div>
                  </div>
              </div>
              </form>
          </div>
     </div>
  </div>
</div>

