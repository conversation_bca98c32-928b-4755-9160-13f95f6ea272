<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
	<div class="col-lg-12"><br>
	
        <h1 class="page-header">
        	Coupons
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
    <form data-ng-submit="searchCouponCode()">
    	Coupon Search:
    	<input type="text" data-ng-model="search" placeholder="Enter coupon code here" class="form-control" />
    </form>
    </div>
</div>
</br>
<div class="row">
	<div class="col-xs-12" ng-if="coupon.code">
		<table class="table table-striped table-bordered" style="font-size:11px">
			<caption>Coupon Details</caption>
			<thead>
				<th>Id</th>
				<th>Code</th>
				<th>Offer Id</th>
				<th>Offer Text</th>
				<th>Offer Description</th>
				<th>Start Date</th>
				<th>End Date</th>
				<th>Reusable</th>
				<th>Reusable By Customer</th>
				<th>Maximum Usage</th>
				<th>Current Usage</th>
				<th>Manual Override</th>
				<th>Status</th>
			</thead>
			<tbody>
				<tr>
					<td>{{coupon.id}}</td>
					<td>{{coupon.code}}</td>
					<td>{{coupon.offer.id}}</td>
					<td>{{coupon.offer.text}}</td>
					<td>{{coupon.offer.description}}</td>
					<td>{{coupon.startDate | date:'yyyy-MM-dd'}}</td>
					<td>{{coupon.endDate | date:'yyyy-MM-dd'}}</td>
					<td>{{coupon.reusable}}</td>
					<td>{{coupon.reusableByCustomer}}</td>
					<td>{{coupon.maxUsage}}</td>
					<td>{{coupon.usage}}</td>
					<td>{{coupon.manualOverride}}</td>
					<td>{{coupon.status}}</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="col-lg-10" ng-if="!coupon">
		<h4>No results found</h4>
	</div>
</div>
</br>
<div class="row">
	<div class="col-xs-12" ng-if="coupon.code">
		<table class="table table-striped table-bordered" style="font-size:11px">
			<caption>Offer Details</caption>
			<thead >
  				<th> ID</th>
				<th> Category</th>
				<th> Type</th>
				<th> Desc</th>
				<th> Start Date</th>
				<th> End Date</th>
				<th> Min Value</th>
				<th> Inclusive Of Tax</th>
				<th> Status</th>
				<th> Minimum Quantity</th>
				<th> Min Loyalty</th>
				<th> Offer Value</th>
				<th> Min Item Count</th>
			</thead>
			<tbody>
				<tr>
                <td>{{coupon.offer.id}}</td>
                <td>{{coupon.offer.category}}</td>
                <td>{{coupon.offer.type}}</td>
                <td>{{coupon.offer.description}}</td>
                <td>{{coupon.offer.startDate | date:'yyyy-MM-dd'}}</td>
                <td>{{coupon.offer.endDate | date:'yyyy-MM-dd'}}</td>
                <td>{{coupon.offer.minValue}}</td>
                <td>{{coupon.offer.includeTaxes}}</td>
                <td>{{coupon.offer.status}}</td>
                <td>{{coupon.offer.minQuantity}}</td>
                <td>{{coupon.offer.minLoyalty}}</td>
                <td>{{coupon.offer.offerValue}}</td>
                <td>{{coupon.offer.minItemCount}}</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>
