<style xmlns="http://www.w3.org/1999/html">
    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .row-selected {
        background-color: darkgray;
    }

    .row-unSelected {
        background-color: white;
    }

    .card-selected {
        background-color: #f0ad4e;
    }

    * {
        box-sizing: border-box;
    }

    .container {
        position: relative;
        display: flex;
        flexDirection: column;
        height: 85vh;
        width: 100%;
        margin-right: 16px;
        padding: 4px;
    }

    .text-container {
        flex: 1;
        overflow: auto;
        height: 100%;
        width: 100%;
    }

    .fab {
        position: absolute;
        right: 20px;
        bottom: 20px;
    }
</style>

<div class="row"
     ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Employee Benefits </h1>
    </div>
    <div class="col-xs-12">
        <table class="table table-bordered" id="tableDataStructure" style="margin-padding:0px;">
            <thead>
            <th>Check &nbsp;
                <input type="checkbox" data-ng-model='checkBoxModal.checkAll'
                       style="width: 20px; height: 20px" data-ng-click="updateAll()">
            </th>
            <th>Customer Id&nbsp;</th>
            <th>Name&nbsp;</th>
            <th>Email Id&nbsp;</th>
            <th>Contact Number&nbsp;</th>
            <th> Employability Status&nbsp;</th>
            </thead>
            <tbody>
            <tr ng-repeat="employee in employeeBenefits track by $index"
                data-ng-class="{'row-selected': employee.isChecked}">
                <td><input type="checkbox" data-ng-model='employee.isChecked' style="width: 20px; height: 20px" data-ng-click="checkBoxIndex($index)"></td>
                <td>{{employee.id}}</td>
                <td>{{employee.name}}</td>
                <td>{{employee.email}}</td>
                <td>{{employee.contact}}</td>
                <td>
                    <button
                            data-ng-disabled="isBulkDeactivate >0"
                            class="btn btn-danger"
                            ng-click="deactivateEmployeeBenefits(employee)">DEACTIVATE
                    </button>
                </td>
            </tr>
            </tbody>
        </table>
        <div style="position: fixed; bottom: 30px; right: 20px; z-index: 1">
            <button style="margin: 10px" class="btn btn-danger pull-right"
                    data-ng-hide="isBulkDeactivate === 0"
                    ng-click="deactivateEmployeeBenefitsinBulk(employeeBenefits)">BULK DEACTIVATE
            </button>
        </div>
    </div>
</div>