<div data-ng-if="iterationPageDetails.isShowDetails">
	<div class="row">
		<div class="col-lg-12">
			<h1 class="page-header">
			<span data-ng-if="iterationDetails.linkedProductName && iterationDetails.linkedProductName != null">{{iterationDetails.linkedConstructName}} Details</span>
			<span data-ng-if="iterationDetails.linkedProductName && iterationDetails.linkedProductName == null">{{iterationDetails.linkedProductName}} Details</span>
				 <input
					type="button" class="btn btn-info  pull-right"
					value="Bact To Recipe Calculator" data-ng-click="goToParent()"
					data-ng-if="true" />
			</h1>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-2 pull-left">
			<button data-ng-if="activeState.currentIndex>0"
				data-ng-click="previous()" class="btn btn-primary">Previous</button>
		</div>
		<div class="col-lg-8" style="text-align: center;">
			<h2>{{activeState.title}}</h2>
		</div>
		<div class="col-lg-2 pull-right">
			<button
				data-ng-if="activeState.currentIndex< 3 && iterationPageDetails.isViewMode"
				data-ng-click="viewNext(activeState.currentIndex)" class="btn btn-primary">Next</button>
		</div>
	</div>
	<fieldset data-ng-disabled="iterationPageDetails.isViewMode"> 
	<div data-ng-show="activeState.name=='iterationDetail'" >
		<form name="iterationCreateForm" novalidate>
			<div class="row">
				<div class="row top-buffer-row">
					<div class="col-lg-3">
						<label>Iteration Name</label>
					</div>
					<div class="col-lg-5">
						<div
							data-ng-if="!isNameAvailable && iterationDetails.linkedProductName">
							{{iterationDetails.linkedProductName}}: <input id="iterationName"
								type="text" data-ng-model="iterationDetails.iterationName"
								name="iterationName" required
								data-ng-change="updateIterationName()" />
						</div>
						<div
							data-ng-if="!isNameAvailable && iterationDetails.linkedConstructName">
							{{iterationDetails.linkedConstructName}}: <input
								id="iterationName" type="text"
								data-ng-model="iterationDetails.iterationName"
								name="iterationName" required
								data-ng-change="updateIterationName()" />
						</div>
						<div data-ng-if="isNameAvailable">
							<label>{{iterationDetails.iterationName}}</label>
						</div>
					</div>
					<div class="col-lg-3">
						<span class="alert alert-danger"
							data-ng-if="iterationCreateForm.iterationName.$error.required">Required</span>
					</div>
					<div class="col-lg-3">
						<span class="btn btn-primary"
							data-ng-if="!iterationCreateForm.iterationName.$error.required && !isNameAvailable"
							data-ng-click="validateIterationName(iterationDetails.iterationName)">Verify</span>
					</div>
				</div>
				<div class="row top-buffer-row"
					data-ng-if="!iterationCreateForm.iterationName.$error.required && isNameVerified">
					<div class="col-lg-10 alert alert-danger"
						data-ng-if="!isNameAvailable">
						<label>This name is already used.</label>
					</div>
					<div class="col-lg-10 alert alert-success"
						data-ng-if="isNameAvailable">
						<label>This name is ready to be used by you.</label>
					</div>
				</div>
				<div class="row top-buffer-row">
					<div class="col-lg-3">
						<label>Iteration Output Uom</label>
					</div>
					<div class="col-lg-6"
						data-ng-if="isOutputUOMEdiatble">
						<select class="form-control"
							data-ng-change="changeOutputUom(outputUom)"
							data-ng-model="outputUom"
							data-ng-options="outputUom as outputUom.name for outputUom in subUomNameList"></select>
					</div>
					<div class="col-lg-6" data-ng-if="!isOutputUOMEdiatble">{{iterationDetails.outputUom}}</div>
				</div>
				<div class="row top-buffer-row">
					<div class="col-lg-3">
						<label>Iteration Product Uom</label>
					</div>
					<div class="col-lg-6">{{iterationDetails.productUom}}</div>
				</div>
				<div class="row top-buffer-row">
					<div class="col-lg-3">
						<label>Iteration Output Uom</label>
					</div>
					<div class="col-lg-3">
						<label>
							<!--data-ng-if="isOutputUOMEdiatble"-->
							<input type="number"
							data-ng-model="iterationDetails.outputConversion"
							name="outputConversion" required data-ng-disabled="iterationDetails.outputUom=='PC'"/> <!-- data-ng-change="updateIterationName()" -->
							{{iterationDetails.outputUom}}
						</label>
						<!--<label data-ng-if="!isOutputUOMEdiatble">{{iterationDetails.outputConversion}}-->
							<!--{{iterationDetails.outputUom}}</label>-->
					</div>
					<div class="col-lg-3">
						<label>Iteration Product Uom</label>
					</div>
					<div class="col-lg-3">
						<label>{{iterationDetails.productConversion}}
							{{iterationDetails.productUom}}</label>
					</div>
				</div>
				<div class="top-buffer-row">
					<div class="col-lg-3" style="margin-left: -10px;">
						<label>Profile Name</label>
					</div>
					<div class="col-lg-3">
						<label>N/A</label>
					</div>
				</div>
				<div class="row top-buffer-row"
					data-ng-if="iterationDetails.productUom.name=='PC' && iterationDetails.outputUom.name=='GM'">
					<span class="alert alert-danger"
						data-ng-show="iterationCreateForm.conversionUom.$error.required">
						Conversion Ratio is Required</span>
				</div>
			</div>
		</form>
	</div>

		<div data-ng-show="activeState.name=='ingredientList'">
		<div class="row top-buffer-row"  data-ng-show="!iterationPageDetails.isViewMode">
			<div class="col-lg-3">
				<label>Set Ordering</label>
			</div>
			<div class="col-lg-3">
				<label>Select SCM Product</label>
			</div>
			<div class="col-lg-5" >
				<select ui-select2="selectOptions" class="form-control"
					data-ng-model="selectedProductId"
					data-placeholder="Select a product"
					data-ng-change="selectSCMMainProduct(selectedProductId)">
					<option value=""></option>
					<option
						data-ng-repeat="product in scmProductsInfoForRecipe"
						value="{{product.productId}}">{{product.productName}} ({{product.category.code}}) </option>
				</select>
			</div>
			<div class="col-lg-3">
				<button class="btn btn-primary " data-toggle="modal"
						data-ng-click="setOrdering()"
						id="setOrdering">
					Set Ordering
				</button>
			</div>
			<div class="col-lg-3">
				<button class="btn btn-primary pull-left"
					data-ng-click="addNewProduct()">
					<i class="fa fa-plus"></i> Add New Product
				</button>
			</div>
		</div>
		<div class="row top-buffer-row" data-ng-if="ingredientList.length>0">
			<div class="col-lg-12">
				<table class="table table-bordered table-striped">
					<thead>

					<tr>
						<td>Product name</td>
						<td>Uom</td>
						<td>Quantity</td>
						<td>Special Instructions</td>
						<td>Show Recipe </td>
						<td>Action</td>
					</tr>
					</thead>
					<tbody dnd-list="ingredientList">
					<tr data-ng-repeat="product in ingredientList track by $index">
						<td>{{product.productName}}</td>
						<td ng-style="product.uom == 'ML' && {'color':'red'}" >{{product.uom}}</td>
						<td><input class="form-control" type="number"
							data-ng-model="product.quantity"
							data-ng-change="updateProdQuantity(product.productId,product.quantity,$index)"
							required></td>
						<td>
							<span data-ng-if="product.instructions!=null">
								<span data-ng-repeat="item in product.instructions track by $index" class="specialInstrLabel">
									{{item.instruction}}
									<span data-ng-click="removeInstruction(product.instructions, $index)" style="cursor: pointer;">&times;</span>
								</span>
							</span>
							<input type="button" class="btn btn-success" value="Add"
								   data-ng-click="openInstructionsModal(product)">
						</td>
						<td><input class="form-control" type="checkbox"
								   data-ng-model="product.showRecipe"
								   ng-disabled="((product.type!=4) || (product.type==4 &&  product.autoProduction==false))"
								   data-ng-change="showRecipeOrNot(product.productId,product.showRecipe,$index)"
								   ></td>
						<td><input type="button" class="btn btn-danger"
							value="Delete" data-ng-click="ingredientList.splice($index,1)"></td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div data-ng-show="activeState.name=='productDetails'">
		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<table class="table table-bordered table-striped">
					<tr>
						<td>Product name</td>
						<td>Uom</td>
						<td>Quantity</td>
						<td>Yield %</td>
						<td>Yield Reason</td>
					</tr>
					<tr data-ng-repeat="product in ingredientList">
						<td>{{product.productName}}</td>
						<td ng-style="product.uom == 'ML' && {'color':'red'}">{{product.uom}}</td>
						<td>{{product.quantity}}</td>
						<td><input class="form-control" type="number"
							data-ng-model="product.yieldPercentage" value="{{product.yieldPercentage}}" required></td>
						<td><input class="form-control" type="text"
							data-ng-model="product.yieldReason"></td>
					</tr>
				</table>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-3">
				<label for="outputQuantity">Recipe Output Quantity</label>
			</div>
			<div class="col-lg-3">
				<input class="form-control" type="number"
					data-ng-model="iterationDetails.outputQuantity"
					name="outputQuantity" required id="outputQuantity" />
			</div>
			<div class="col-lg-3">
				<label>{{iterationDetails.outputUom}}</label>
			</div>
			<div class="col-lg-3">
				<span class="alert alert-danger"
					data-ng-show="!iterationDetails.outputQuantity">Output
					Quantity is Required</span>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-4">
				<label>Recipe Output Uom</label>
			</div>
			<div class="col-lg-2">
				<label>{{iterationDetails.outputConversion}}
					{{iterationDetails.outputUom}}</label>
			</div>
			<div class="col-lg-4">
				<label>Recipe Product Uom</label>
			</div>
			<div class="col-lg-2">
				<label>{{iterationDetails.productConversion}}
					{{iterationDetails.productUom}}</label>
			</div>
		</div>
	</div>
	<div data-ng-show="activeState.name=='detailsPreview'">
		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<table class="table table-bordered table-striped">
					<tr>
						<td>Product name</td>
						<td>Uom</td>
						<td>Quantity({{iterationDetails.outputQuantity}}{{iterationDetails.outputUom}})</td>
						<td>Quantity(1{{iterationDetails.productUom}})</td>
						<td>Quantity(Yield)</td>
						<td>Yield %</td>
						<td>Yield Reason</td>
						<td>Instructions</td>
					</tr>
					<tr data-ng-repeat="product in ingredientList">
						<td>{{product.productName}}</td>
						<td ng-style="product.uom == 'ML' && {'color':'red'}">{{product.uom}}</td>
						<td>{{product.quantity}}</td>
						<td>{{product.quantityPerSubUom}}</td>
						<td>{{product.yieldQuantity}}</td>
						<td>{{product.yieldPercentage}}</td>
						<td>{{product.yieldReason}}</td>
						<td><span class="specialInstrLabel" data-ng-repeat="item in product.instructions">{{item.instruction}}</span></td>
					</tr>
				</table>
			</div>
		</div>
	</div>
	<div
		data-ng-if="ingredientList.length>0 || activeState.name == 'iterationDetail'">
		<div class="row top-buffer-row">
			<label for="comment">Comment:</label>
			<textarea data-ng-model="iterationDetails.notes" class="form-control"
				rows="5" id="comment"
				data-ng-disabled="activeState.name=='detailsPreview'"></textarea>
		</div>
		<div class="row top-buffer-row" data-ng-show="!iterationPageDetails.isViewMode">
			<div class="col-lg-4">
				<Button class="btn btn-success btn-lg"
					data-ng-click="sumbitIngredientDetails(activeState.name)">{{activeState.buttonText}}</Button>
			</div>
		</div>
	</div>
</fieldset>
</div>

<!-- set ordering modal-->
<div aria-labelledby="SetOrderingModalLabel" class="modal fade" id="SetOrderingModal" role="dialog"
	 tabindex="-1">
	<div class="modal-dialog" role="document">
		<div class="modal-content">

			<div class="modal-header">
				<button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
						aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel_setOrdering"> Set Ordering </h4>
			</div>

			<div class="modal-body">
				<div class="row">
					<div class="col-xs-12">
						<div class="alert alert-info"> Please drag and drop elements to set ordering.
						</div>
						<div class="groupListing" dnd-list="ingredientList">
							<div class="list" data-ng-repeat="  product in ingredientList " draggable="true" dnd-draggable="product" dnd-effect-allowed="copyMove" dnd-moved="ingredientList.splice($index, 1)">
								 {{product.productName}} : {{product.quantity}}
							</div>
						</div>
						<br>
						<!--<div class="row">-->
						<!--<div class="col-xs-12 text-right">-->
						<!--<button class="btn btn-primary pull-right"-->
						<!--ng-click="submitOrdering()">Submit-->
						<!--</button>-->
						<!--</div>-->
						<!--</div>-->
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<script type="text/ng-template" id="iterationIngredientInstructionModal.html">
	<style>
		.popeye-modal-container .popeye-modal{
			width: 70%;
		}
	</style>
	<div class="modal-content" data-ng-init="init()">
		<div class="modal-header">
			<div class="row">
				<div class="col-lg-12">
					<h1 class="page-header">Instructions</h1>
				</div>
			</div>
		</div>
		<div class="modal-body">
			<div class="row top-buffer-row">
				<div class="col-lg-12">
					<label>Product Name : {{product.productName}}</label>
				</div>
			</div>
			<div class="row top-buffer-row">
				<div class="col-lg-12">
					<p data-ng-repeat="item in instructions" data-ng-click="item.checked = !item.checked"
					   style="background:#ddd; padding: 10px; margin: 1px; cursor: pointer; width: 45%;float: left;">
						{{item.instruction}}
						<span data-ng-if="item.checked==true" style="float: right;">&#10004;</span>
					</p>
				</div>
			</div>
			<div class="row top-buffer-row">
				<div class="col-lg-12">
					<table class="table">
						<tr>
							<td><input type="text" class="form-control" data-ng-model="instructionText" /></td>
							<td>
								<input type="button" class="btn btn-success" value="Add"
									   data-ng-click="addNewInstruction()">
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-danger" data-ng-click="closeModal()">Submit</button>
		</div>
	</div>

</script>