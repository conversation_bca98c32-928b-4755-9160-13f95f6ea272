<style xmlns="http://www.w3.org/1999/html">
    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }

    * {
        box-sizing: border-box;
    }

    .container {
        position: relative;
        display: flex;
        flexDirection: column;
        height: 85vh;
        width: 100%;
        margin-right: 16px;
        padding: 4px;
    }

    .text-container {
        flex: 1;
        overflow: auto;
        height: 100%;
        width: 100%;
    }

    .fab {
        position: absolute;
        right: 20px;
        bottom: 20px;
    }
    .toggle-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20px 0;
    }

    .toggle-label {
      font-family: Arial, sans-serif;
      font-size: 16px;
      margin-right: 10px;
    }

    .toggle-options {
      display: flex;
      gap: 20px;
    }

    .toggle-option {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .toggle-option input[type="radio"] {
      display: none;
    }

    .toggle-option label {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
      padding: 8px 12px;
      border: 2px solid #ccc;
      border-radius: 20px;
      transition: all 0.3s ease;
    }

    .toggle-option input[type="radio"]:checked + label {
      background-color: #4caf50;
      color: #fff;
      border-color: #4caf50;
      font-weight: bold;
    }

    .toggle-option label:hover {
      border-color: #4caf50;
    }



    .editable-cell input {
        width: 100%;
        height: 100%;
        border: none;
        padding: 4px;
        box-sizing: border-box;
    }

    .editable-cell input:focus {
        outline: 2px solid #4caf50;
    }

    .add-mapping {
        margin-top: 20px;
    }

    .modal-body,.modal-header,.modal-footer{
        background-color: white;
    }
    .modal{
        background-color: white;
        margin: 70px;
    }

    .bulk-edit-actions {
        margin: 10px 0;
    }



.checked-row {
    background-color: #add8e6; /* Light blue for checked rows */
}

.unmapped-row {
    background-color: #FFA07A; /* Light blue for checked rows */
}

.new-row {
    background-color: #d4edda; /* Light green for new rows */
}





/* Wrapper for the price cell content */
.cell-content {
    display: flex; /* Align content horizontally */
    justify-content: space-between; /* Add space between original and new price */
    align-items: center; /* Vertically align both items */
    gap: 8px; /* Add some space between original price and input */
    width: 100%; /* Ensure it uses full width */
}

/* Style for original value */
.original-value {
    font-size: 14px; /* Adjust the font size */
    color: #6c757d; /* Muted gray color */
    font-weight: 600; /* Slightly bold for better emphasis */
}

/* Style for changed-row */
.changed-row {
    background-color: #f8d7da; /* Light red background for changed rows */
    border: 1px solid #f5c6cb; /* Border for distinction */
    padding: 2px 4px; /* Add padding */
    border-radius: 4px; /* Rounded corners */
}

/* Style for normal-row */
.normal-row {
    background-color: #e9ecef; /* Light gray background for unchanged rows */
    border: 1px solid #dee2e6; /* Light border */
    padding: 2px 4px; /* Add padding */
    border-radius: 4px; /* Rounded corners */
}

/* Style for input box */
.price-input {
    width: 120px; /* Fixed width to align better with text */
    padding: 5px; /* Spacing inside input box */
    border: 1px solid #ced4da; /* Light border */
    border-radius: 4px; /* Rounded corners */
    font-size: 14px; /* Consistent font size */
    box-sizing: border-box; /* Include padding in the width */
}

.table-container {
    max-height: 500px;  /* Adjust height as needed */
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid #ddd; /* Add border to define container */
}

.table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    white-space: nowrap; /* Prevent text wrapping */
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

thead {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    z-index: 10;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 5px; /* Space between elements */
    flex-wrap: wrap; /* Allow wrapping if needed */
}

.input-group input,
.input-group select {
    flex: 1; /* Make input and select take available space */
    min-width: 80px; /* Prevent too small elements */
}

.input-group button {
    white-space: nowrap; /* Prevent text from wrapping */
    padding: 5px 10px;
    font-size: 12px;
    min-width: 60px; /* Ensure button width consistency */
}

table th {
    white-space: nowrap;
    text-align: center;
    padding: 5px;
}


/* General Flexbox fixes for aligning content in rows */
.row2 {
    display: flex; /* Ensure it's a flex container */
    flex-wrap: nowrap; /* Prevent wrapping */
    justify-content: space-evenly; /* Align content to the right */
    align-items: center; /* Vertically center content */
}

.rowleft {
    display: flex; 
    flex-wrap: nowrap; 
    justify-content: flex-end; 
    align-items: center; 
}

/* Styling for .row-spacing and container spacing */
.row-spacing {
    margin-top: 10px;
}

/* Container adjustments */
.container {
    position: relative;
    display: flex;
    flex-direction: column; /* Keep content stacked vertically */
    height: 85vh;
    width: 100%;
    margin-right: 16px;
    padding: 4px;
    align-items: flex-start; /* Ensure container items are aligned from left */
}

/* Right-aligned flex elements in row */
.row > .col-auto {
    display: flex;
    justify-content: flex-end; /* Ensure columns inside the row are aligned to the right */
}

/* Buttons and toggle options alignment */
.toggle-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    width: 100%;
}

.toggle-options {
    display: flex;
    gap: 20px;
    justify-content: flex-end; /* Align toggle options to the right */
}

/* Enhanced button styling with spacing */
.toggle-option label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    padding: 8px 12px;
    border: 2px solid #ccc;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.toggle-option input[type="radio"]:checked + label {
    background-color: #4caf50;
    color: #fff;
    border-color: #4caf50;
    font-weight: bold;
}

.toggle-option label:hover {
    border-color: #4caf50;
}

/* Flexbox improvements for cell content and input box alignment */
.cell-content {
    display: flex; /* Align content horizontally */
    justify-content: space-between; /* Space between original and new price */
    align-items: center; /* Vertically align both items */
    gap: 8px; /* Space between original price and input */
    width: 100%; /* Full width for the container */
}

.price-input {
    width: 120px; /* Set width to control input box size */
    padding: 5px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

/* Table container adjustments */
.table-container {
    max-height: 500px; 
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid #ddd;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    white-space: nowrap;
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

/* Make sure table header stays at the top */
thead {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    z-index: 10;
}

/* Consistent input group styling */
.input-group {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: wrap; /* Allow wrapping if necessary */
}

.input-group input,
.input-group select {
    flex: 1;
    min-width: 80px;
}

.input-group button {
    padding: 5px 10px;
    font-size: 12px;
    min-width: 60px;
    white-space: nowrap;
}

/* Ensure tables with sticky headers stay responsive */
table th {
    white-space: nowrap;
    text-align: center;
    padding: 5px;
}

.col-auto {
    display: flex;
    gap: 10px; /* Adjust the value as needed for the gap */
}

.applied-filters-container {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-box {
    background: white;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.filter-label {
    font-weight: bold;
    margin-right: 5px;
}

.badge-filter {
    background-color: #ff7b00;
    color: white;
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 13px;
    white-space: nowrap;
    cursor: pointer;
    margin-right: 5px;
}

.badge-filter:hover {
    background-color: #007bff;
}

.show-more {
    color: #007bff;
    cursor: pointer;
    font-weight: bold;
    margin-left: 5px;
}

.show-more:hover {
    text-decoration: underline;
}

/* Overlay to cover entire screen */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensures modal appears on top */
}

/* Modal container with max height and scroll */
.modal-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 900px; /* Prevents it from stretching too much */
    max-height: 80vh; /* Prevents overflow */
    overflow-y: auto; /* Enables scroll if content is too large */
    text-align: center;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    position: relative;
}

/* Close button for better control */
.modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
}

/* Table Styling */
.preview-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.preview-table th, .preview-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    white-space: nowrap; /* Keeps text in a single line */
}

.preview-table th {
    background: #f4f4f4;
    font-weight: bold;
}

.highlight-change {
    background-color: #ffeb3b; /* Highlight changes */
    font-weight: bold;
}

/* Buttons Container */
.modal-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.modal-actions .btn {
    margin: 5px;
}


</style>




<div  data-ng-init="init()">
    <!-- Mapping Selection -->
    <div class="toggle-container">
        <span class="toggle-label">Choose Mapping:</span>
        <div class="toggle-options">
            <!-- <div class="toggle-option">
                <input type="radio" id="profileToProduct" name="mappingType" value="ProfileToProduct" ng-model="mappingType"
                data-ng-change="toggleMappingType()" />
                <label for="profileToProduct">Profile to Product</label>
            </div> -->
            <div class="toggle-option">
                <input type="radio" id="productToProfile" name="mappingType" value="ProductToProfile" ng-model="mappingType" 
                data-ng-change="toggleMappingType()"/>
                <label for="productToProfile">Product to Profile</label>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div data-ng-if="mappingType == 'ProfileToProduct'">
        <div class="row-spacing">
            <label>Price Profile:</label>
            <select class="form-control" data-ng-model="model.priceProfile" data-ng-change="selectProfile(model.priceProfile)">
                <option data-ng-repeat="profile in profiles | orderBy" value="{{profile}}">{{profile.priceProfileName}}</option>
            </select>
        </div>
        <div class="row-spacing">
            <label>Version:</label>
            <select class="form-control" data-ng-model="model.selectedVersion" data-ng-change="changeVersion(model.selectedVersion)">
                <option data-ng-repeat="version in versions | orderBy" value="{{version.versionNo}}">{{version.versionNo}}</option>
            </select>
        </div>
    </div>

    <div data-ng-if="mappingType == 'ProductToProfile'">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Brand</label>
                    </div>
                    <div class="col-xs-6 form-group">
                        <select class="form-control" data-ng-model="model.brandId" data-ng-change = "changeSelectedBrand(model.brandId)">
                            <option data-ng-repeat="brand in brandList | orderBy" value="{{brand.brandId}}"   
                            >
                                {{brand.brandName}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Select Product Type</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForProductType" 
                             options="preoductTypeList" selected-model="selectedProductTypes" class="col-xs-6">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Select Product</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForProducts" 
                             options="filteredProductsInfo" selected-model="selectedProducts" class="col-xs-6">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="row-spacing">
            <label>Select Product:</label>
            <select data-ui-select2 class="form-control" data-ng-model="model.productId" data-placeholder="Select a product" data-ng-change="changeProduct(model.productId)">
                <option data-ng-repeat="product in productsInfo" value="{{product}}">{{product.name}}</option>
            </select>
        </div> -->
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Dimension:</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForDimensions" 
                             options="productDimensions" selected-model="selectedDimensions" class="col-xs-6">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Price Profile:</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForProfiles" 
                             options="profiles" selected-model="selectedProfiles" class="col-xs-6">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <label class="control-label">Price Profile Versions:</label>
                    </div>
                    <div class="col-xs-8 form-group">
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForVersions" 
                             options="versions" selected-model="selectedVersons" class="col-xs-6">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="row-spacing">
            <label>Dimension:</label>
            <select class="form-control" data-ng-model="model.dimensionId"
                                data-ng-change="changeDimension(model.dimensionId)">
                            <option data-ng-repeat="dimension in selectedProduct.productDimension.content" value="{{dimension}}">
                                {{dimension.name}}
                            </option>
                        </select>
        </div> -->
        <!-- <div class="row-spacing">
            <label>Price Profile:</label>
            <select class="form-control" data-ng-model="model.priceProfile" data-ng-change="selectProfile(model.priceProfile)">
                <option data-ng-repeat="profile in profiles | orderBy" value="{{profile}}">{{profile.priceProfileName}}</option>
            </select>
        </div> -->
        <!-- <div class="row-spacing">
            <label>Version:</label>
            <select class="form-control" data-ng-model="model.selectedVersion" data-ng-change="changeVersion(model.selectedVersion)">
                <option data-ng-repeat="version in versions | orderBy" value="{{version.versionNo}}">{{version.versionNo}}</option>
            </select>
        </div> -->
    </div>

    <div class="bulk-edit-actions rowleft ">
        <button class="btn btn-primary" data-ng-click="getProfileProductMappings()">Get Data</button>
    </div>

    <div class="applied-filters-container">
        <h4>Applied Filters</h4>
        <div class="filters-wrapper">
            <div class="filter-box" data-ng-repeat="filter in filters">
                <span class="filter-label">
                    <i class="{{ filter.icon }}"></i> {{ filter.label }}:
                </span>
                <span class="filter-values">
                    <span class="badge badge-filter" 
                          title="{{ value }}" 
                          data-ng-repeat="value in filter.displayValues track by $index">
                        {{ value }}
                    </span>
                    <span class="show-more" 
                          data-ng-if="filter.hiddenCount > 0"
                          data-ng-click="filter.expand()">
                        {{ filter.isExpanded ? "Show Less" : "+ " + filter.hiddenCount + " more" }}
                    </span>
                </span>
            </div>
        </div>
    </div>
    
    
    
    
    <button class="btn btn-primary" data-ng-click="showPreviewMappings()">Preview Mappings</button>


    <div class="row2 mt-2 d-flex justify-content-end flex-nowrap align-items-center">
        <!-- Sorting Section -->
        <div>
            <strong>Sort By:</strong>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="sortOrder" value="productName" data-ng-model="sortOption"
                    data-ng-change="applySorting()">
                <label class="form-check-label">Product Name</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="sortOrder" value="profileVersion" data-ng-model="sortOption"
                    data-ng-change="applySorting()">
                <label class="form-check-label">Price Profile & Version</label>
            </div>
        </div>
        

        <!-- File Upload Section -->
        <div class="ms-3">
            <label>Upload Bulk Product Price Sheet</label>
            <div class="d-flex">
                <input class="btn btn-default" style="width: 100%;" type="file" file-model="productPriceFile" accept="">
                <button class="btn btn-primary ms-2" data-ng-click="uploadBulkProductPriceSheet()">Upload and update prices</button>
            </div>
        </div>
    
        <!-- Buttons Section -->
        <div class="ms-2">
            <button class="btn btn-primary ms-2" data-ng-click="getProductPriceProfileSheet()">Download Selected</button>
            <button class="btn btn-primary" data-ng-click="submitMappings()">Submit Mappings</button>
        </div>
    
    
    </div>
        <strong>Filtered Rows Count:</strong> {{ filteredData.length }}
        </div>
        <div class="table-container" >
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <!-- Bulk Selection Checkbox -->
                        <th>
                            <input type="checkbox" data-ng-model="selectAll" data-ng-click="toggleAllRows(selectAll)" />
                        </th>
                        
                        <!-- Price Profile Filter -->
                        <th>
                            <input 
                                type="text" 
                                class="form-control" 
                                placeholder="Filter by Price Profile" 
                                data-ng-model="filters.priceProfile" 
                                data-ng-change="applyFilters()" />
                        </th>
                        
                        <!-- Product Filter -->
                        <th>
                            <input 
                                type="text" 
                                class="form-control" 
                                placeholder="Filter by Product Id" 
                                data-ng-model="filters.productId" 
                                data-ng-change="applyFilters()" />
                        </th>

                        <th>
                            <input 
                                type="text" 
                                class="form-control" 
                                placeholder="Filter by Product Name" 
                                data-ng-model="filters.product" 
                                data-ng-change="applyFilters()" />
                        </th>
                        
                        <!-- Dimension Filter -->
                        <th>
                            <input 
                                type="text" 
                                class="form-control" 
                                placeholder="Filter by Dimension" 
                                data-ng-model="filters.dimension" 
                                data-ng-change="applyFilters()" />
                        </th>
                        
                        <!-- Profile Version Filter -->
                        <th>
                            <input 
                                type="text" 
                                class="form-control" 
                                placeholder="Filter by Version" 
                                data-ng-model="filters.version" 
                                data-ng-change="applyFilters()" />
                        </th>
                        
                        <!-- Price Filter with Bulk Update -->
                        <th>
                            <div class="input-group">
                                <input 
                                    type="number" 
                                    class="form-control" 
                                    placeholder="Filter by Price" 
                                    data-ng-model="header.price" 
                                    data-ng-change="applyFilters()" />
                                <button 
                                    class="btn btn-primary" 
                                    data-ng-click="updatePriceForFilteredRows(header.price)">Apply</button>
                                    <button data-ng-if="filters.newPrice == null"
                                    class="btn btn-primary" 
                                    data-ng-click="filterPriceRows(header.price)">Filter</button>
                                    <button data-ng-if="filters.newPrice != null"
                                    class="btn btn-primary" 
                                    data-ng-click="unFilterPriceRows(header.price)">Un-Filter</button>
                            </div>
                        </th>
                        
                        <!-- Status Filter with Bulk Update -->
                        <th>
                            <div class="input-group">
                                <select 
                                    class="form-control" 
                                    data-ng-model="header.status" 
                                    data-ng-change="applyFilters()">
                                    <option value="ACTIVE">ACTIVE</option>
                                    <option value="IN_ACTIVE">IN_ACTIVE</option>
                                </select>
                                <button 
                                    class="btn btn-primary" 
                                    data-ng-click="updateStatusForFilteredRows(header.status)">Apply</button>
                                    <button data-ng-if="filters.newStatus == null"
                                    class="btn btn-primary" 
                                    data-ng-click="filterStatusRows(header.status)">Filter</button>
                                    <button data-ng-if="filters.newStatus != null"
                                    class="btn btn-primary" 
                                    data-ng-click="unFilterStatusRows(header.status)">Un-Filter</button>
                            </div>
                        </th>
                        <th>
                            <button 
                                class="btn btn-success" 
                                title="Add New Mapping" 
                                data-ng-click="addNewRow()">
                                +
                            </button>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <div >
                        <tr data-ng-repeat="row in (filteredData | orderBy) | limitTo: pageSize : (currentPage - 1) * pageSize"

                    data-ng-class="{
                        'unmapped-row' : row.price  == null && row.newPrice == null,
                        'checked-row': row.selected === true,
                        'new-row': row.isNew === true,

                    }">
                        <td><input type="checkbox" data-ng-model="row.selected" /></td>
                        <td data-ng-if="row.isNew != true && row.priceProfileId != null">{{ row.priceProfileId.name}}</td>
                        <td data-ng-if="row.isNew == true || row.priceProfileId == null ">
                                <select data-ui-select2 class="form-control" data-ng-model="row.priceProfile" 
                                data-ng-change="selectProfileForRow(row.priceProfile,row)">
                                    <option data-ng-repeat="profile in profiles | orderBy" value="{{profile}}">{{profile.priceProfileName}}</option>
                                </select>
                        </td>
                        <td data-ng-if="row.isNew != true">{{ row.productId.id }}</td>
                        <td data-ng-if="row.isNew != true">{{ row.productId.name }}</td>
                        <td data-ng-if="row.isNew == true" >
                                <select  data-ui-select2
                                class="form-control" 
                                data-ng-model="row.productId" 
                                data-ng-options="product.name for product in filteredProductsInfo"
                                data-ng-change="selectProductForRow(row.productId,row)">
                                <option value="">Select Product</option>
                            </select>
                            
                        </td>
                        <td data-ng-if="row.isNew == true || row.dimensionCode == null ">
                                <select  data-ui-select2
                                class="form-control" 
                                data-ng-model="row.dimensionCode" 
                                data-ng-init="row.dimensionCode = row.dimensionCode || (row.productDimensionOptions.content.length > 0 ? row.productDimensionOptions.content[0] : null)"
                                data-ng-options="dimension.name for dimension in row.productDimensionOptions.content">
                                <option value="">Select Dimension</option>
                            </select>
                        </td>
                        <td data-ng-if="row.isNew != true && row.dimensionCode != null">{{ row.dimensionCode.name }}</td>
                    <td data-ng-if="row.isNew != true && row.version != null ">{{ row.version }}</td>
                        <td data-ng-if="row.isNew == true || row.version == null">
                                <select class="form-control" data-ng-model="row.version">
                                    <option data-ng-repeat="version in row.versions | orderBy" value="{{version.versionNo}}">{{version.versionNo}}</option>
                                </select>
                        </td>
                        <td class="editable-cell">
                            <div class="cell-content">
                                <span class="original-value" 
                                      data-ng-class="{'changed-row': row.originalPrice != row.newPrice}">
                                    {{row.originalPrice}}
                                </span>
                                <input type="text" class="price-input" data-ng-model="row.newPrice" placeholder="Enter price" 
                                       data-ng-class="{
                                           'changed-row': row.originalPrice != row.newPrice,
                                           'normal-row': row.originalPrice == row.newPrice
                                       }" />
                            </div>
                        </td>
                        <td class="editable-cell">
                            <div class="original-value">
                                {{row.originalStatus}}
                            </div>
                            <div   data-ng-class="{
                                'changed-row': row.originalStatus != row.newStatus,
                                'normal-row': row.originalStatus == row.newStatus
                            }">
                            <select class="form-control" data-ng-model="row.newStatus">
                                <option data-ng-repeat="status in statusList" value="{{ status }}">{{ status }}</option>
                            </select>
                            </div>
                        
                        </td>
                        <td data-ng-if="row.isNew == true">
                            <button 
                                class="btn btn-danger" 
                                title="Remove Row" 
                                data-ng-click="removeRow($index)">
                                ×
                            </button>
                        </td>
                    </tr>
                    </div>
                
                </tbody>
            </table>

        </div>
        <uib-pagination 
    total-items="filteredData.length" 
    items-per-page="pageSize" 
    ng-model="currentPage">
</uib-pagination>
    </div>

    <!-- Add New Mapping Button
    <div class="add-mapping">
        <button class="btn btn-success" data-ng-click="openAddMappingModal()">Add New Mapping</button>
    </div> -->

    <!-- Modal for Adding New Mapping -->
    <div class="modal" id="versionModal"  role="dialog" >
        <div class="modal-content">
            <div class="modal-header">
                <h4>Add New Mapping</h4>
                <button class="btn btn-danger btn-sm" data-ng-click="closeAddMappingModal()">X</button>
            </div>
            <div class="modal-body">
                <label>Price Profile:</label>
                <select class="form-control" data-ng-model="newMapping.priceProfile">
                    <option data-ng-repeat="profile in profiles" value="{{profile.priceProfileName}}">{{profile.priceProfileName}}</option>
                </select>
                <label>Product:</label>
                <input type="text" class="form-control" data-ng-model="newMapping.product" placeholder="Enter Product Name" />
                <label>Dimension:</label>
                <input type="text" class="form-control" data-ng-model="newMapping.dimension" placeholder="Enter Dimension" />
                <label>Version:</label>
                <input type="text" class="form-control" data-ng-model="newMapping.version" placeholder="Enter Version" />
                <label>Price:</label>
                <input type="text" class="form-control" data-ng-model="newMapping.price" placeholder="Enter Price" />
                <label>Status:</label>
                <select class="form-control" data-ng-model="newMapping.status">
                    <option data-ng-repeat="status in statusList" value="{{ status.value }}">{{ status.name }}</option>
                </select>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" data-ng-click="addMapping()">Add Mapping</button>
                <button class="btn btn-secondary" data-ng-click="closeAddMappingModal()">Cancel</button>
            </div>
        </div>
    </div>

<!-- Modal for Preview -->
<div class="modal-overlay" data-ng-if="showPreviewModal">
    <div class="modal-content">
        <span class="modal-close" data-ng-click="closePreview()">✖</span>
        <h3>Preview Mappings</h3>
        <table class="preview-table">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Price Profile</th>
                    <th>Version</th>
                    <th>Dimension</th>
                    <th>Original Price</th>
                    <th>New Price</th>
                    <th>Original Status</th>
                    <th>New Status</th>
                </tr>
            </thead>
            <tbody>
                <tr data-ng-repeat="mapping in previewMappings">
                    <td>{{ mapping.productName }}</td>
                    <td>{{ mapping.priceProfile }}</td>
                    <td>{{ mapping.version }}</td>
                    <td>{{ mapping.dimension }}</td>
                    <td>{{ mapping.originalPrice }}</td>
                    <td ng-class="{'highlight-change': mapping.originalPrice != mapping.newPrice}">
                        {{ mapping.newPrice }}
                    </td>
                    <td>{{ mapping.originalStatus }}</td>
                    <td ng-class="{'highlight-change': mapping.originalStatus != mapping.newStatus}">
                        {{ mapping.newStatus }}
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="modal-actions">
            <button class="btn btn-success" data-ng-click="saveMappings(mappingsPayload)">Confirm & Submit</button>
            <button class="btn btn-danger" data-ng-click="closePreview()">Cancel</button>
        </div>
    </div>
</div>
</div>