<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">Generate Winback Coupon For Customer</h1>
    </div>
</div>
<div class="row" style="display:flex;margin-left:100px">
    <label class="control-label" style="margin-right:40px;margin-top:5px;font-size:18px">Select Order Source</label>
    <select
            style="margin-bottom:20px;margin-right:80px; width:30%"
            class="form-control"
            ng-model="selectedSource"
            data-ng-change="selectSourceValue(selectedSource)"
            ng-options="type for type in Source"
    />
    <button
            class="btn btn-primary pull-right"
            data-toggle="modal"
            ng-click="getCoupons()" style="margin-bottom: 20px">Get Coupons
    </button>
</div>
<div data-ng-if="showFurther" class="row" style="display:flex">
    <table style="width:90%;margin-left:20px">
        <tbody>
            <tr>
                <td><label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;width:100%">Select Offer</label></td>
                <td>
                    <div data-ng-if="selectedSource=='DINE_IN'">
                        <select
                                style="margin-bottom:20px;margin-left:50px; width:70%"
                                class="form-control"
                                ng-model="selectedOffer",
                                data-ng-change="selectOfferValue(selectedOffer)"
                                ng-options="type for type in dineInCouponsList"
                        />
                    </div>
                    <div data-ng-if="selectedSource=='DELIVERY'">
                        <select
                                style="margin-bottom:20px;margin-left:50px; width:70%"
                                class="form-control"
                                ng-model="selectedOffer"
                                data-ng-change="selectOfferValue(selectedOffer)"
                                ng-options="type for type in deliveryCouponsList"
                        />
                    </div>
                </td>
            </tr>
            <tr data-ng-if="showFurther && showPartner">
                <td>
                    <label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;width:100%">Select Partner</label>
                </td>
                <td>
<!--                    <div  class="row" style="display:flex">-->

                        <select
                                style="margin-bottom:20px;margin-left:50px; width:70%"
                                class="form-control"
                                ng-model="selectedPartner"
                                data-ng-change="selectChannelPartnerValue(selectedPartner)"
                                ng-options="type for type in channelPartner"
                        />
<!--                    </div>-->
                </td>
            </tr>
            <tr>
                <td><label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;;width:100%">Enter Customer Contact Number</label></td>
                <td>
<!--                    <div data-ng-if="showFurther" class="row" style="display:flex">-->
                        <input style="margin-bottom:20px;margin-left:50px; width:70%;padding:5px" type="text" ng-model="contactNumber"
                               data-ng-change="onConatctNumberInput(contactNumber)" placeholder="Enter contact Number"/>
<!--                    </div>-->
                </td>
            </tr>
        <tr>
            <td>
                <label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;width:100%">Enter Customer Name</label>
            </td>
            <td>
<!--                <div class="row" style="display:flex">-->
                    <input style="margin-bottom:20px;margin-left:50px; width:70%;padding:5px" type="text" ng-model="customerName"
                           data-ng-change="onCustomerNameInput(customerName)" placeholder="Enter Customer Name"/>
<!--                </div>-->
            </td>
        </tr>
        <tr>
            <td><label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;width:100%">Enter Validity in Days</label></td>
            <td>
<!--                <div data-ng-if="showFurther" class="row" style="display:flex">-->
                    <input style="margin-bottom:20px;margin-left:50px; width:70%;padding:5px" type="number" ng-model="validityInDays"
                           data-ng-change="onValidityInDaysInput(validityInDays)" placeholder="Enter Validity in Days"/>
<!--                </div>-->
            </td>
        </tr>
        <tr>
            <td>
                <label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;padding:5px;width:100%">Select Compensation Reason</label>
            </td>
            <td>
<!--                <div data-ng-if="showFurther" class="row" style="display:flex">-->
                    <select
                            style="margin-bottom:20px;margin-left:50px; width:70%"
                            class="form-control"
                            ng-model="selectedCompensationReason"
                            data-ng-change="selectCompensationReasonValue(selectedCompensationReason)"
                            ng-options="type for type in compensationReason"
                    />
<!--                </div>-->
            </td>
        </tr>
            <tr>
                <td>
                    <label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;padding:5px;width:100%">Select Compensation Source</label>
                </td>
                <td>
                    <!--                <div data-ng-if="showFurther" class="row" style="display:flex">-->
                    <select
                            style="margin-bottom:20px;margin-left:50px; width:70%"
                            class="form-control"
                            ng-model="selectedComplainSource"
                            data-ng-change="selectComplainSourceValue(selectedComplainSource)"
                            ng-options="type for type in complainSourceOption"
                    />
                    <!--                </div>-->
                </td>
            </tr>
        <tr>
            <td>
                <label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;padding:5px;width:100%">Enter Comment</label>
            </td>
            <td>
<!--                <div data-ng-if="showFurther" class="row" style="display:flex">-->
                    <input style="margin-bottom:20px;margin-left:50px; width:70%" type="text" ng-model="comment"
                           data-ng-change="onCommentInput(comment)" placeholder="Enter Comment"/>
<!--                </div>-->
            </td>
        </tr>
        <tr>
            <td>
                <label class="control-label" style="margin-left:25%;margin-top:5px;font-size:18px;padding:5px;width:100%">Enter Generated OrderId</label>
            </td>
            <td>
<!--                <div data-ng-if="showFurther" class="row" style="display:flex">-->
                    <input style="margin-bottom:20px;margin-left:50px; width:70%" type="text" ng-model="orderId"
                           data-ng-change="onOrderIdInput(orderId)" placeholder="Enter Order Id"/>
<!--                </div>-->
            </td>
        </tr>
        </tbody>
    </table>



</div>

<div data-ng-if="showFurther" class="row" style="display:flex;width:100%;justify-content:center">
    <button
            class="btn btn-primary pull-right"
            data-toggle="modal"
            ng-click="addMappings()" style="margin-bottom: 20px">Add Mappings
    </button>
</div>

<div
        class="modal fade"
        id="couponDetail"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        data-ng-click="init()"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">Notify Customer About Coupons Detail</h4>
            </div>
            <div class="modal-body">
                <div style="margin-left: 20%">
                    <div class="col-xs-6">Contact Number</div>
                    <div>{{notifyResponse.contactNumber}}</div>
                </div>
                <div style="margin-left: 20%">
                    <div class="col-xs-6">Customer Name</div>
                    <div>{{notifyResponse.customerName}}</div>
                </div>
                <div style="margin-left: 20%">
                    <div class="col-xs-6">Coupon code</div>
                    <div>{{notifyResponse.couponCode}}</div>
                </div>
                <div style="margin-left: 20%">
                    <div class="col-xs-6">Offer Description</div>
                    <div>{{notifyResponse.offerDescription}}</div>
                </div>
                <div style="margin-left: 20%">
                    <div class="col-xs-6">Start Date</div>
                    <div>{{notifyResponse.startDate}}</div>
                </div>
                <div style="margin-left: 20%">
                    <div class="col-xs-6">End Date</div>
                    <div>{{notifyResponse.endDate}}</div>
                </div>
                <div style="justify-content:center; display:flex">
                    <button class="btn btn-primary" style="margin-top:20px;margin-bottom:0px; width:30%"
                            data-ng-click="notNow()">Not Now
                    </button>
                    <button class="btn btn-primary" style="margin-left:5px;margin-top:20px;margin-bottom:0px; width:30%"
                            data-ng-click="markNotify()">Notify
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
