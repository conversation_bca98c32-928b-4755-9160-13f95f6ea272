<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    table th, td {
        border: #ccc 1px solid;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Menu Management Dashboard V1</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'UPDATE UNIT MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to update menu changes for individual unit per region.</h3>
                    <p>In order to push menu, select each region and their corresponding first outlet, then select partner and press get menu. Once menu is loaded, press add menu.
                        Do this activity for all the regions where you want to update menu.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Set Menu</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Region</label>
                                <select class="form-control"
                                        data-ng-options="region as region.name for region in regions track by region.id"
                                        data-ng-model="selectedRegion"
                                        data-ng-change="setSelectedRegion(selectedRegion)">
                                </select>
                            </div>
                            <!--<div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in filteredUnits track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>-->
                            <!--<div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Menu"
                                       data-ng-click="getUnitMenuToAdd()"/>
                            </div>-->
                            <div data-ng-if="filteredUnits != null && filteredUnits.length > 0">
                                <div class="row" style="padding:10px; background:#ddd; border:#ccc 1px solid; margin: 5px">
                                    <div class="col-xs-4">Unit Name</div>
                                    <div class="col-xs-4">Last Menu Time</div>
                                    <div class="col-xs-4">Menu Updated By</div>
                                </div>
                                <div data-ng-repeat="unit in filteredUnits track by $index"
                                     style="border:#ccc 1px solid; margin: 10px 5px; background:#ddd;">
                                    <div class="row {{checkToday(unit.activeMenu)?'yellowBg':''}}" data-ng-click="expandUnit(unit)"
                                         style="padding:10px; border:#ccc 1px solid; margin: 0; cursor: pointer;">
                                        <div class="col-xs-4">{{unit.name}}</div>
                                        <div class="col-xs-4">
                                            {{(unit.activeMenu != null) ? (unit.activeMenu.addTime | date:'yyyy-MM-dd hh:mm:ss a') : 'NA'}}
                                        </div>
                                        <div class="col-xs-4">
                                            {{(unit.activeMenu != null) ? (unit.activeMenu.employeeName) : 'NA'}}
                                        </div>
                                    </div>
                                    <div data-ng-if="unit.expand == true" style="background: #fff; padding:10px">
                                        <div class="row">
                                            <div class="col-xs-12 text-right">
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="unit.newMenu" data-ng-change="setNewMenu(unit, unit.newMenu)"> New Menu
                                                    </label>
                                                </span>
                                                <input type="button" class="btn btn-primary" value="Get Current Partner Menu" data-ng-click="getCurrentMenu()" />
                                                <input type="button" class="btn btn-primary" value="Refresh Current Partner Menu" data-ng-click="refreshMenu()" />
                                                <input type="button" class="btn btn-primary" value="Get Kettle Menu for Update" data-ng-click="getUnitMenuToAdd()" />
                                                <input type="button" data-ng-if="addMenuObj!=null" class="btn btn-primary" value="Send Menu to Partner"
                                                       data-ng-click="validateMenu()" />
                                            </div>
                                        </div>
                                        <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                                            <div data-ng-if="addMenuObj != null">
                                                <h3>Menu Items</h3>
                                                <div data-ng-repeat="cat in addMenuObj.menu.categories | orderBy: 'category_order' track by cat.category_id">
                                                    <div style="background: #69ac6a; padding:15px;margin: 10px 0 0 0;">
                                                        <h4 style="display: inline;">Category: {{cat.category_name == 'Others' ? 'Unmapped products':cat.category_name}}</h4>
                                                        <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                        line-height: 10px;padding: 8px;float: right;">
                                                            {{cat.category_order}}
                                                        </span>
                                                    </div>

                                                    <table class="table bordered" data-ng-if="cat.items.length > 0" style="background: #ea8080;">
                                                        <tr>
                                                            <th>Product Name</th>
                                                            <th>Product taxes</th>
                                                            <th>Bogo active</th>
                                                            <th>Treats active</th>
                                                            <th>Order</th>
                                                            <th>Filtered</th>
                                                            <th>Customizations</th>
                                                        </tr>
                                                        <tr data-ng-repeat="item in cat.items | orderBy: 'item_order' track by item.item_id">
                                                            <td>{{item.item_name}}</td>
                                                            <td>{{item.item_taxes[0].taxes.join()}}</td>
                                                            <td>{{item.item_is_bogo_active}}</td>
                                                            <td>{{item.item_is_treats_active}}</td>
                                                            <td>{{item.item_order}}</td>
                                                            <td>{{filteredProducts.indexOf(item.item_id) >= 0}}</td>
                                                            <td>
                                                                <span data-ng-repeat="group in item.groups">
                                                                    <strong>{{group.group_name}}:</strong>
                                                                    <span data-ng-repeat="gitem in group.items">{{gitem.item_name}}</span>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div data-ng-if="cat.subcategories.length > 0" style="border: #ccc 1px solid;padding: 10px;">
                                                        <div data-ng-repeat="subCat in cat.subcategories | orderBy: 'subcategory_order' track by subCat.subcategory_id">
                                                            <div style="background: #e2af2e; padding:15px;margin: 10px 0 0 0;">
                                                                <h4 style="display: inline;">Sub-category: {{subCat.subcategory_name}}</h4>
                                                                <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                                line-height: 10px;padding: 8px;float: right;">
                                                                    {{subCat.subcategory_order}}
                                                                </span>
                                                            </div>
                                                            <table class="table bordered" data-ng-if="subCat.items.length > 0">
                                                                <tr>
                                                                    <th>Product Name</th>
                                                                    <th>Product taxes</th>
                                                                    <th>Bogo active</th>
                                                                    <th>Treats active</th>
                                                                    <th>Order</th>
                                                                    <th>Filtered</th>
                                                                    <th>Customizations</th>
                                                                </tr>
                                                                <tr data-ng-repeat="item in subCat.items | orderBy: 'item_order' track by item.item_id">
                                                                    <td>{{item.item_name}}</td>
                                                                    <td>{{item.item_taxes[0].taxes.join()}}</td>
                                                                    <td>{{item.item_is_bogo_active}}</td>
                                                                    <td>{{item.item_is_treats_active}}</td>
                                                                    <td>{{item.item_order}}</td>
                                                                    <td>{{filteredProducts.indexOf(item.item_id) >= 0}}</td>
                                                                    <td>
                                                                        <span data-ng-repeat="group in item.groups">
                                                                            <strong>{{group.group_name}}:</strong>
                                                                            <span data-ng-repeat="gitem in group.items">{{gitem.item_name}} </span>
                                                                        </span>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div data-ng-if="addMenuObj.menu.charges.length>0">
                                                    <h3>Charges</h3>
                                                    <table class="table bordered">
                                                        <tr>
                                                            <th>Charge name</th>
                                                            <th>Charge value</th>
                                                            <th>Always applicable</th>
                                                            <th>Applicable below</th>
                                                            <th>Charge type</th>
                                                            <th>Charge taxes</th>
                                                        </tr>
                                                        <tr data-ng-repeat="charge in addMenuObj.menu.charges">
                                                            <td>{{charge.charge_name}}</td>
                                                            <td>{{charge.charge_value}}</td>
                                                            <td>{{charge.charge_always_applicable}}</td>
                                                            <td>{{charge.charge_applicable_below_order_amount}}</td>
                                                            <td>{{charge.charge_type}}</td>
                                                            <td>{{charge.charge_taxes[0].taxes.join()}}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div data-ng-if="addMenuObj.menu.taxes.length>0">
                                                    <h3>Taxes</h3>
                                                    <table class="table bordered">
                                                        <tr>
                                                            <th>Tax id</th>
                                                            <th>Tax name</th>
                                                            <th>Tax type</th>
                                                            <th>Tax value</th>
                                                        </tr>
                                                        <tr data-ng-repeat="tax in addMenuObj.menu.taxes">
                                                            <td>{{tax.tax_id}}</td>
                                                            <td>{{tax.tax_name}}</td>
                                                            <td>{{tax.tax_type}}</td>
                                                            <td>{{tax.tax_value}}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div data-ng-if="addMenuObj.restaurant_offers.length>0">
                                                    <h3>Offers</h3>
                                                    <table class="table bordered">
                                                        <tr>
                                                            <th>Offer id</th>
                                                            <th>Start Date</th>
                                                            <th>End Date</th>
                                                            <th>Offer Type</th>
                                                            <th>Discount Type</th>
                                                            <th>Min Order Amount</th>
                                                            <th>Discount Value</th>
                                                        </tr>
                                                        <tr data-ng-repeat="offer in addMenuObj.restaurant_offers">
                                                            <td>{{offer.offer_id}}</td>
                                                            <td>{{offer.start_date}}</td>
                                                            <td>{{offer.end_date}}</td>
                                                            <td>{{offer.offer_type}}</td>
                                                            <td>{{offer.discount_type}}</td>
                                                            <td>{{offer.min_order_amount}}</td>
                                                            <td>{{offer.discount_value}}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <div data-ng-if="selectedPartner.partnerName == 'SWIGGY'">
                                            <div data-ng-if="addMenuObj != null">
                                                <h3>Menu Items</h3>
                                                <div data-ng-repeat="cat in addMenuObj.entity.main_categories | orderBy: 'order' track by cat.id">
                                                    <div style="background: #69ac6a; padding:15px;margin: 10px 0 0 0;">
                                                        <h4 style="display: inline;">Category: {{cat.name == 'Others' ? 'Unmapped products':cat.name}}</h4>
                                                        <h5 style="display: inline;"> ( id: {{cat.id}} ) </h5>
                                                        <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                        line-height: 10px;padding: 8px;float: right;">
                                                            {{cat.order}}
                                                        </span>
                                                    </div>

                                                    <div data-ng-if="cat.sub_categories.length > 0" style="border: #ccc 1px solid;padding: 10px;">
                                                        <div data-ng-repeat="subCat in cat.sub_categories | orderBy: 'order' track by subCat.id">
                                                            <div style="background: #e2af2e; padding:15px;margin: 10px 0 0 0;">
                                                                <h4 style="display: inline;">Sub-category: {{subCat.name}}</h4>
                                                                <h5 style="display: inline;"> ( id: {{subCat.id}} ) </h5>
                                                                <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                                line-height: 10px;padding: 8px;float: right;">
                                                                    {{subCat.order}}
                                                                </span>
                                                            </div>
                                                            <table class="table bordered" data-ng-if="addMenuObj.entity.items.length > 0">
                                                                <tr>
                                                                    <th>Id</th>
                                                                    <th>Product Name</th>
                                                                    <th>Image</th>
                                                                    <th>Description</th>
                                                                    <th>Is Veg</th>
                                                                    <th>Price</th>
                                                                    <th>Tax Details</th>
                                                                    <th>Packing Charges</th>
                                                                    <th>Variants</th>
                                                                    <th>Add Ons</th>
                                                                </tr>
                                                                <tr data-ng-repeat="item in addMenuObj.entity.items" data-ng-if="item.category_id == cat.id && item.sub_category_id == subCat.id">
                                                                    <td>{{item.id}}</td>
                                                                    <td>{{item.name}}</td>
                                                                    <td><img src = "{{item.image_url}}" width="60" height="60"></td>
                                                                    <td>{{item.description}}</td>
                                                                    <td>{{item.is_veg}}</td>
                                                                    <td>{{item.price | number:2}}</td>
                                                                    <th>
                                                                        <table class="table bordered" data-ng-if="item.gst_details != null">
                                                                            <tr>
                                                                                <th>IGST</th>
                                                                                <th>SGST</th>
                                                                                <th>CGST</th>
                                                                                <th>Inclusive</th>
                                                                            </tr>
                                                                            <tr data-ng-if="item.gst_details != null">
                                                                                <td>{{item.gst_details.igst}}</td>
                                                                                <td>{{item.gst_details.sgst}}</td>
                                                                                <td>{{item.gst_details.cgst}}</td>
                                                                                <td>{{item.gst_details.inclusive}}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </th>
                                                                    <td>{{item.packing_charges | number:2}}</td>
                                                                    <!--Variants-->
                                                                    <td>
                                                                        <div data-ng-if="item.variant_groups.length > 0">
                                                                            <div data-ng-repeat="variantGroup in item.variant_groups | orderBy: 'order' track by variantGroup.id">
                                                                                <div style="background: #9BD7D5; padding:15px;margin: 10px 0 0 0;">
                                                                                    <h4 style="display: inline;">Variant Group: {{variantGroup.name}}</h4>
                                                                                    <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;line-height: 10px;padding: 8px;float: right;">
                                                                                            {{variantGroup.order}}
                                                                                    </span>
                                                                                </div>
                                                                                <table class="table bordered" data-ng-if="variantGroup.variants.length > 0">
                                                                                    <tr>
                                                                                        <th>Id</th>
                                                                                        <th>Name</th>
                                                                                        <th>Price</th>
                                                                                        <th>Taxes</th>
                                                                                    </tr>
                                                                                    <tr data-ng-repeat="variant in variantGroup.variants | orderBy: 'order' track by variant.id">
                                                                                        <td>{{variant.id}}</td>
                                                                                        <td>{{variant.name}}</td>
                                                                                        <td>{{variant.price | number:2}}</td>
                                                                                        <td>
                                                                                            <table class="table bordered" data-ng-if="variant.gst_details != null">
                                                                                                <tr>
                                                                                                    <th>IGST</th>
                                                                                                    <th>SGST</th>
                                                                                                    <th>CGST</th>
                                                                                                    <th>Inclusive</th>
                                                                                                </tr>
                                                                                                <tr data-ng-if="variant.gst_details != null">
                                                                                                    <td>{{variant.gst_details.igst}}</td>
                                                                                                    <td>{{variant.gst_details.sgst}}</td>
                                                                                                    <td>{{variant.gst_details.cgst}}</td>
                                                                                                    <td>{{variant.gst_details.inclusive}}</td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <!--Add Ons-->
                                                                    <td>
                                                                        <div data-ng-if="item.variant_groups.length >= 0">
                                                                            <div data-ng-repeat="addOnGroup in item.addon_groups | orderBy: 'order' track by addOnGroup.id">
                                                                                <div style="background: #FFF5C3; padding:15px;margin: 10px 0 0 0;">
                                                                                    <h4 style="display: inline;">Add On Group: {{addOnGroup.name}}</h4>
                                                                                    <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;line-height: 10px;padding: 8px;float: right;">
                                                                                            {{addOnGroup.order}}
                                                                                    </span>
                                                                                </div>
                                                                                <table class="table bordered" data-ng-if="addOnGroup.addons.length > 0">
                                                                                    <tr>
                                                                                        <th>AddOn Id</th>
                                                                                        <th>AddOn Name</th>
                                                                                    </tr>
                                                                                    <tr data-ng-repeat="addOn in addOnGroup.addons | orderBy: 'order' track by addOn.id">
                                                                                        <td>{{addOn.id}}</td>
                                                                                        <td>{{addOn.name}}</td>
                                                                                    </tr>
                                                                                </table>
                                                                            </div>
                                                                        </div>
                                                                    </td>

                                                                </tr>
                                                            </table>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div data-ng-if="addMenuObj.unmappedProducts.length > 0">
                                                <h3>Unmapped Products</h3>
                                                <table class="table bordered" style="background: #ea8080;">
                                                    <tr>
                                                        <td>Id</td>
                                                        <td>Name</td>
                                                    </tr>
                                                    <tr data-ng-repeat="item in addMenuObj.unmappedProducts track by item.id">
                                                        <td>{{item.id}}</td>
                                                        <td>{{item.name}}</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div data-ng-if="selectedAction == 'MANAGE OFFER'">
        <div class="row">
            <div class="col-xs-12">
                <h3>Use this panel to set run offer on partner menu.</h3>
                <p>TO run any offer, first create the offer from this panel and then update menu using update menu panel.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <div class="form-group">
                    <label>Select Units</label>
                    <ul style="max-height: 400px;overflow: auto; list-style: none;">
                        <li data-ng-repeat="unit in unitList | orderBy:'name' track by unit.id"
                            data-ng-class="{'selected':unit.selected}" data-ng-click="selectUnit(unit)">
                            {{unit.name}}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label>Select Partner</label>
                    <select class="form-control"
                            data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                            data-ng-model="selectedPartner"
                            data-ng-change="setSelectedPartner(selectedPartner)">
                    </select>
                </div>

                <div class="form-group">
                    <input type="button" class="btn btn-primary" value="Add Offers" data-ng-click="showAddOffer()"/>
                    <input type="button" class="btn btn-primary" value="Get Active Offers" data-ng-click="getOffers()"/>
                </div>

                <div class="row" data-ng-show="showAddNewOffer">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label>Offer Type</label>
                            <select class="form-control" data-ng-model="offerType">
                                <option value=""></option>
                                <option value="DISCOUNT">DISCOUNT</option>
                                <option value="BOGO">BOGO</option>
                            </select>
                        </div>
                        <div data-ng-show="offerType=='DISCOUNT'">
                            <div class="form-group">
                                <label>Coupon Code</label>
                                <input type="text" id="couponCode" class="form-control" style="text-transform: uppercase;"
                                       data-ng-model="couponCode" data-ng-change="setCouponCode(couponCode)" />
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Find Offer" data-ng-click="couponSearch()" />
                            </div>
                        </div>
                        <div data-ng-show="offerType=='BOGO'">
                            <div class="form-group">
                                <label>Start Date</label>
                                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                                    <input class="form-control" data-ng-model="startDate" type="text" placeholder="yyyy-MM-dd" required />
                                </div>
                            </div>
                            <div class="form-group">
                                <label>End Date</label>
                                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                                    <input class="form-control" data-ng-model="endDate" type="text" placeholder="yyyy-MM-dd" required />
                                </div>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Submit" data-ng-click="setBogo(startDate, endDate)" />
                            </div>
                        </div>

                        <h4 data-ng-if="offer != null">Offer Detail</h4>
                        <table class="table table-bordered" data-ng-if="offer != null">
                            <tr>
                                <th>Discount Type</th>
                                <th>Discount Value</th>
                                <th>Min Order Amount</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Is Active</th>
                            </tr>
                            <tr>
                                <td>{{offer.offerData.discount_type}}</td>
                                <td>{{offer.offerData.discount_value}}</td>
                                <td>{{offer.offerData.min_order_amount}}</td>
                                <td>{{offer.offerData.start_date}}</td>
                                <td>{{offer.offerData.end_date}}</td>
                                <td>{{offer.offerData.is_active}}</td>
                            </tr>
                        </table>
                        <div class="form-group">
                            <input type="button" class="btn btn-primary" value="Add Offer"
                                   data-ng-if="offer != null" data-ng-click="addNewOffer()" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                    <h4 data-ng-if="offers != null && offers.length > 0">Offers</h4>
                    <table class="table table-bordered" data-ng-if="offers != null && offers.length > 0">
                        <tr>
                            <th>Unit Name</th>
                            <th>Coupon Code</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Discount Type</th>
                            <th>Discount Value</th>
                            <th>Min Order Amount</th>
                            <th>Is Active</th>
                            <th>Actions</th>
                        </tr>
                        <tr data-ng-repeat="offer in offers track by offer.id">
                            <td>{{unitMap[offer.unitId]}}</td>
                            <td>{{offer.couponCode}}</td>
                            <td>{{offer.offerData.start_date}}</td>
                            <td>{{offer.offerData.end_date}}</td>
                            <td>{{offer.offerData.discount_type}}</td>
                            <td>{{offer.offerData.discount_value}}</td>
                            <td>{{offer.offerData.min_order_amount}}</td>
                            <td>{{offer.active}}</td>
                            <td>
                                <input type="button" class="btn btn-danger" value="Deactivate"
                                       data-ng-if="offer.active" data-ng-click="updateOfferStatus(offer, false)" />
                                <input type="button" class="btn btn-success" value="Activate"
                                       data-ng-if="offer.active == false" data-ng-click="updateOfferStatus(offer, true)" />
                            </td>
                        </tr>
                    </table>

                </div>

            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'BOGO PRODUCTS'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to set BOGO products for partner menu.</h3>
                    <p>To run BOGO offer, first set BOGO products from this panel. Then, add BOGO offer from manage offer panel and
                        then finally update the menu from update menu panel</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>BOGO Products</h3>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Products"
                                       data-ng-click="getUnitProducts()"/>
                            </div>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-6">
                            <ul style="max-height: 400px;overflow: auto; list-style: none; width: 300px;">
                                <li data-ng-repeat="product in unitProductList | filter:{classification:'MENU', type:'!12'}
                                | orderBy:'detail.name' track by product.detail.id"
                                    data-ng-class="{'selected':product.selected}"
                                    data-ng-click="product.selected == true? product.selected = false:product.selected=true">
                                    {{product.detail.name}}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-12" style="text-align: right;">
                            <button type="button" class="btn btn-success"
                                    data-ng-click="setBogoProducts(true)">Set BOGO
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--<div class="row" data-ng-if="selectedAction == 'ADD MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to push partner menu for newly mapped outlets or refresh menu for individual outlet.</h3>
                    <p>In order to push update, select outlet then select partner and press get menu. After menu is loaded press sync to partner button.</p>
                </div>
            </div>
            <div class="form-group">
                <label>Select Unit</label>
                <select class="form-control"
                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                        data-ng-model="selectedUnit"
                        data-ng-change="setSelectedUnit(selectedUnit)">
                </select>
            </div>
            <div class="form-group">
                <label>Select Partner</label>
                <select class="form-control"
                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                        data-ng-model="selectedPartner"
                        data-ng-change="setSelectedPartner(selectedPartner)">
                </select>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Get Menu" data-ng-click="getCurrentMenu()" />
            </div>
            <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                <div data-ng-if="currentMenu != null">
                    <h3>Menu Items</h3>
                    <div data-ng-repeat="cat in currentMenu.menuRequest.menu.categories">
                        <h4>Category: {{cat.category_name}}</h4>
                        <table class="table bordered">
                            <tr>
                                <th>Product Name</th>
                                <th>Product taxes</th>
                                <th>Bogo active</th>
                                <th>Treats active</th>
                                <th>Customizations</th>
                            </tr>
                            <tr data-ng-repeat="item in cat.items">
                                <td>{{item.item_name}}</td>
                                <td>{{item.item_taxes[0].taxes.join()}}</td>
                                <td>{{item.item_is_bogo_active}}</td>
                                <td>{{item.item_is_treats_active}}</td>
                                <td>
                                    <span data-ng-repeat="group in item.groups">
                                        <strong>{{group.group_name}}:</strong>
                                        <span data-ng-repeat="gitem in group.items">{{gitem.item_name}} </span>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div data-ng-if="currentMenu.menuRequest.menu.charges.length>0">
                        <h3>Charges</h3>
                        <table class="table bordered">
                            <tr>
                                <th>Charge name</th>
                                <th>Charge value</th>
                                <th>Always applicable</th>
                                <th>Applicable below</th>
                                <th>Charge type</th>
                                <th>Charge taxes</th>
                            </tr>
                            <tr data-ng-repeat="charge in currentMenu.menuRequest.menu.charges">
                                <td>{{charge.charge_name}}</td>
                                <td>{{charge.charge_value}}</td>
                                <td>{{charge.charge_always_applicable}}</td>
                                <td>{{charge.charge_applicable_below_order_amount}}</td>
                                <td>{{charge.charge_type}}</td>
                                <td>{{charge.charge_taxes[0].taxes.join()}}</td>
                            </tr>
                        </table>
                    </div>
                    <div data-ng-if="currentMenu.menuRequest.menu.taxes.length > 0">
                        <h3>Taxes</h3>
                        <table class="table bordered">
                            <tr>
                                <th>Tax id</th>
                                <th>Tax name</th>
                                <th>Tax type</th>
                                <th>Tax value</th>
                            </tr>
                            <tr data-ng-repeat="tax in currentMenu.menuRequest.menu.taxes">
                                <td>{{tax.tax_id}}</td>
                                <td>{{tax.tax_name}}</td>
                                <td>{{tax.tax_type}}</td>
                                <td>{{tax.tax_value}}</td>
                            </tr>
                        </table>
                    </div>
                    <div data-ng-if="currentMenu.menuRequest.restaurant_offers.length>0">
                        <h3>Offers</h3>
                        <table class="table bordered">
                            <tr>
                                <th>Offer id</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Offer Type</th>
                                <th>Discount Type</th>
                                <th>Min Order Amount</th>
                                <th>Discount Value</th>
                            </tr>
                            <tr data-ng-repeat="offer in currentMenu.menuRequest.restaurant_offers">
                                <td>{{offer.offer_id}}</td>
                                <td>{{offer.start_date}}</td>
                                <td>{{offer.end_date}}</td>
                                <td>{{offer.offer_type}}</td>
                                <td>{{offer.discount_type}}</td>
                                <td>{{offer.min_order_amount}}</td>
                                <td>{{offer.discount_value}}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="form-group">
                    <input type="button" class="btn btn-primary" value="Sync to Partner" data-ng-click="refreshMenu()" />
                </div>
            </div>
        </div>
    </div>-->

    <!--<div class="row" data-ng-if="selectedAction == 'UPDATE MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to update menu changes across system.</h3>
                    <p>In order to push menu, select each region and their corresponding first outlet, then select partner and press get menu. Once menu is loaded, press add menu.
                        Do this activity for all the regions where you want to update menu.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Set Menu</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Region</label>
                                <select class="form-control"
                                        data-ng-options="region as region.name for region in regions track by region.id"
                                        data-ng-model="selectedRegion"
                                        data-ng-change="setSelectedRegion(selectedRegion)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in filteredUnits track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Menu"
                                       data-ng-click="getMenuToAdd()"/>
                            </div>
                            <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                                <div data-ng-if="addMenuObj != null">
                                    <h3>Menu Items</h3>
                                    <div data-ng-repeat="cat in addMenuObj.menu.categories">
                                        <h4>Category: {{cat.category_name}}</h4>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Product Name</th>
                                                <th>Product taxes</th>
                                                <th>Bogo active</th>
                                                <th>Treats active</th>
                                                <th>Customizations</th>
                                            </tr>
                                            <tr data-ng-repeat="item in cat.items">
                                                <td>{{item.item_name}}</td>
                                                <td>{{item.item_taxes[0].taxes.join()}}</td>
                                                <td>{{item.item_is_bogo_active}}</td>
                                                <td>{{item.item_is_treats_active}}</td>
                                                <td>
                                                <span data-ng-repeat="group in item.groups">
                                                    <strong>{{group.group_name}}:</strong>
                                                    <span data-ng-repeat="gitem in group.items">{{gitem.item_name}} </span>
                                                </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div data-ng-if="addMenuObj.menu.charges.length>0">
                                        <h3>Charges</h3>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Charge name</th>
                                                <th>Charge value</th>
                                                <th>Always applicable</th>
                                                <th>Applicable below</th>
                                                <th>Charge type</th>
                                                <th>Charge taxes</th>
                                            </tr>
                                            <tr data-ng-repeat="charge in addMenuObj.menu.charges">
                                                <td>{{charge.charge_name}}</td>
                                                <td>{{charge.charge_value}}</td>
                                                <td>{{charge.charge_always_applicable}}</td>
                                                <td>{{charge.charge_applicable_below_order_amount}}</td>
                                                <td>{{charge.charge_type}}</td>
                                                <td>{{charge.charge_taxes[0].taxes.join()}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div data-ng-if="addMenuObj.menu.taxes.length>0">
                                        <h3>Taxes</h3>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Tax id</th>
                                                <th>Tax name</th>
                                                <th>Tax type</th>
                                                <th>Tax value</th>
                                            </tr>
                                            <tr data-ng-repeat="tax in addMenuObj.menu.taxes">
                                                <td>{{tax.tax_id}}</td>
                                                <td>{{tax.tax_name}}</td>
                                                <td>{{tax.tax_type}}</td>
                                                <td>{{tax.tax_value}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div data-ng-if="addMenuObj.restaurant_offers.length>0">
                                        <h3>Offers</h3>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Offer id</th>
                                                <th>Start Date</th>
                                                <th>End Date</th>
                                                <th>Offer Type</th>
                                                <th>Discount Type</th>
                                                <th>Min Order Amount</th>
                                                <th>Discount Value</th>
                                            </tr>
                                            <tr data-ng-repeat="offer in addMenuObj.restaurant_offers">
                                                <td>{{offer.offer_id}}</td>
                                                <td>{{offer.start_date}}</td>
                                                <td>{{offer.end_date}}</td>
                                                <td>{{offer.offer_type}}</td>
                                                <td>{{offer.discount_type}}</td>
                                                <td>{{offer.min_order_amount}}</td>
                                                <td>{{offer.discount_value}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group" data-ng-if="addMenuObj!=null">
                                <input type="button" class="btn btn-primary" value="Send Menu to Partner"
                                       data-ng-click="addPartnerMenu()"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>-->


</div>



<div class="modal fade" id="nonMatchedProductsModal" tabindex="-1" role="dialog" aria-labelledby="nonMatchedProductsModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="nonMatchedProductsModalLabel">Non available products</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <label style="color: red;">Following products were not found in cafe menu and hence they will not be sent in the partner menu please look carefully.</label>
                        <table class="table table-bordered table-striped">
                            <tr>
                                <td>Product Id</td>
                                <td>Product Name</td>
                                <td>Dimension</td>
                            </tr>
                            <tr data-ng-repeat="item in nonMatchedProducts track by $index">
                                <td>{{item.id}}</td>
                                <td>{{item.name}}</td>
                                <td>{{item.dimension}}</td>
                            </tr>
                        </table>
                        <div class="form-group text-right">
                            <input type="button" class="btn btn-danger" value="Cancel" data-dismiss="modal" aria-label="Close">
                            <input type="button" class="btn btn-primary" value="Update Menu" data-ng-click="addPartnerMenuForUnit()">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>