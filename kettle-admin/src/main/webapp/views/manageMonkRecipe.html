<style type="text/css">
.table-striped>tbody>tr:nth-of-type(even) {
	background-color: #FFF;
}
</style>
<!-- <div data-ng-init="init()">
	<div class="row">
		<div class="col-xs-6" style="margin: 10px; text-align: right">
			<button data-ng-click="downloadAllRecipe()">
				Download Active Monk Recipes <i class="fa fa-download"
					style="font-size: 24px; margin-right: 5px;"></i>
			</button>
		</div>
	</div>
</div> -->
<div class="row" style="margin-bottom: 10px;">
	<div class="col-xs-4">
		<button class="btn  btn-info" style="margin-left: 5px;"
			data-ng-click="recipeByStatus('ACTIVATED')">Active Recipes</button>
	</div>
	<div class="col-xs-4">
		<button class="btn  btn-info" style="margin-left: 5px;"
			data-ng-click="recipeByStatus('CREATED')">Created Recipes</button>
	</div>
	<div class="col-xs-4">
		<button class="btn  btn-info" style="margin-left: 5px;"
			data-ng-click="createNewVersion()">Create New Version</button>
	</div>
</div>
<div class="row" data-ng-if="selectedRecipe != null">
	<div class="col-xs-12">
		<div class="row" data-ng-if="selectedRecipe.status == 'CREATED'">
			<div class="col-xs-12">
					<button class="btn  btn-info" style="margin-left: 5px;"
			data-ng-click="activateSelected()">Activate Version</button>			</div>
		</div>
		<div class="row">
			<div class="col-xs-3">
				<label>Version : {{selectedRecipe.version}}</label>
			</div>
			<div class="col-xs-3">
				<label>Status : {{selectedRecipe.status}}</label>
			</div>
			<div class="col-xs-4" data-ng-if="selectedRecipe.status == 'CREATED'">
				<label>Created By : {{selectedRecipe.updatedByName}}({{selectedRecipe.updatedById}})</label>
			</div>
			<div class="col-xs-4" data-ng-if="selectedRecipe.status == 'ACTIVATED'">
				<label>Activated By : {{selectedRecipe.activatedByName}}({{selectedRecipe.activatedById}})</label>
			</div>
			<div class="col-xs-2" data-ng-if="selectedRecipe.status == 'CREATED'">
				<label>Creation Time : {{selectedRecipe.genetrationTime}}</label>
			</div>
			<div class="col-xs-2" data-ng-if="selectedRecipe.status == 'ACTIVATED'">
				<label>Activation Time: {{selectedRecipe.activationTime}}</label>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				<label>Total Additions : {{selectedRecipe.changeLog.added}}</label>
			</div>
			<div class="col-xs-12">
				<label>Total Updations : {{selectedRecipe.changeLog.updated}}</label>
			</div>
			<div class="col-xs-12">
				<label>Total Deletions : {{selectedRecipe.changeLog.removed}}</label>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-4">
				<table
					class="table table-striped table-bordered table-hover table-condensed table-responsive">
					<tr class="info">
						<th>Additions</th>
					</tr>
					<tbody>
						<tr
							data-ng-repeat="data in selectedRecipe.changeLog.additions track by $index">
							<td>{{data}}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="col-xs-4">
				<table
					class="table table-striped table-bordered table-hover table-condensed table-responsive">
					<tr class="info">
						<th>Updations</th>
					</tr>
					<tbody>
						<tr
							data-ng-repeat="data in selectedRecipe.changeLog.updations track by $index">
							<td>{{data}}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="col-xs-4">
				<table
					class="table table-striped table-bordered table-hover table-condensed table-responsive">
					<tr class="info">
						<th>Deletions</th>
					</tr>
					<tbody>
						<tr
							data-ng-repeat="data in selectedRecipe.changeLog.deletions track by $index">
							<td>{{data}}</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				<table
					class="table table-striped table-bordered table-hover table-condensed table-responsive">
					<tr class="info">
						<th>Product Name</th>
						<th>Dimension</th>
						<th>Quantity</th>
						<th>Preparation</th>
						<th>Water(ml)</th>
						<th>Milk(ml)</th>
						<th>Boil Settle(s)</th>
						<th>No Of Boils</th>
						<th>Heating Time Mins</th>
						<th>Heating Time Secs</th>
						<th>Recipe Key</th>
						<th>Recipe Content</th>
					</tr>
					<tbody>
						<tr
							data-ng-repeat="recipe in selectedRecipe.content track by $index">
							<td>{{recipe.productName}}</td>
							<td>{{recipe.dimension}}</td>
							<td>{{recipe.quantity}}</td>
							<td>{{recipe.prep}}</td>
							<td>{{recipe.water}}</td>
							<td>{{recipe.milk}}</td>
							<td>{{recipe.boilSettle}}</td>
							<td>{{recipe.noOfBoils}}</td>
							<td>{{recipe.heatingTimeMins}}</td>
							<td>{{recipe.heatingTimeSecs}}</td>
							<td>{{recipe.key}}</td>
							<td>{{recipe.content}}</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
