<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<link href="css/image_upload.css" rel="stylesheet" type="text/css"/>
<style>
    .frame {
        width: fit-content !important;
        height: fit-content !important;
    }

    .modal-backdrop {
        background-color: black;
        opacity: 0.1 !important;
    }

    .modalImage .image-wrapper {
        text-align: center;
    }

    .modalImage .image-wrapper img {
        max-width: 560px;
        max-height: 560px;
    }

    .modalImage .image-description {
        text-align: center;
        margin-top: 10px;
    }
</style>
<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header">Product Detail
            <button class="btn btn-primary pull-right" id="addProductIdDiv" data-toggle="modal" ng-click="addProduct()">
                <i class="fa fa-plus fw"></i> Add Product
            </button>
            <button class="btn btn-primary pull-right" id="cloneProductIdDiv" data-toggle="modal" ng-click="openCloneProductModal()" style="margin-right: 10px;">
                <i class="fa fa-plus fw"></i> Clone Product
            </button>
        </h1>
    </div>

</div>

<div class="modal fade" id="cloneProductModal" role="dialog" aria-labelledby="cloneProductModal">
    <div class="modal-dialog" role="document">

<div class="modal-content">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="cloneProductModalLabel">Select Clone Product</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div>
                <div class="col-xs-12">
                    <div class="row divInnerRow">
                        <div class="col-xs-4">
                            <label>Product</label>
                        </div>
                        <div class="col-xs-8">
                            <select ui-select2 class="form-control" style="width: 100% !important"
                                    data-ng-model="selectedCloneProduct"
                                    data-ng-change="onChangeSelectedCloneProduct(selectedCloneProduct);"
                                    data-placeholder="Search Product...">
                                <option value=""></option>
                                <option data-ng-repeat="product in productDetailsList" value="{{product.id}}">
                                    {{product.name}} - {{product.status}}
                                </option>
                            </select>
                        </div>
                        <button class="btn btn-primary pull-right" data-ng-show="true"
                                data-ng-click="cloneProduct(selectedCloneProduct)"
                                style="margin-bottom: 10px;">
                            <i class="fa fa-plus"></i> Clone
                        </button>
                    </div>
                </div>
            </div>
            <!--<div data-ng-if="cloneProduct">
                <div class="col-xs-12">
                    <div class="row divInnerRow">
                        <div class="col-xs-4">
                            <label>Product</label>
                        </div>
                        <div class="col-xs-8">
                            {{cloneProduct.name}}
                        </div>
                    </div>
                </div>
            </div>-->
        </div>
    </div>
</div>
    </div>
</div>


<div class="row">
    <div class="col-lg-4">
        Filter:
        <input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control"/>
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
            <option value="300">300</option>
        </select>
    </div>
</div>

<div class="row">
    <div class="col-xs-12" ng-if="filteredItems > 0">
        <p>Filtered {{ filteredList.length }} of {{ totalItems}} total results</p>        <div class="row">
            <div class="col-xs-12">
                <table class="table table-striped table-bordered" style="font-size:12px; padding:3px">
                    <thead valign="top">
                    <th>ID &nbsp;<a ng-click="sort_by('id');"><i class="glyphicon glyphicon-sort"></i></a></th>
                    <th> Product Name&nbsp;<a ng-click="sort_by('name');"><i class="glyphicon glyphicon-sort"></i></a>
                    </th>
                    <th>Short Code&nbsp;<a ng-click="sort_by('shortCode');"><i class="glyphicon glyphicon-sort"></i></a>
                    </th>
                    <th>Description&nbsp;<a ng-click="sort_by('description');"><i class="glyphicon glyphicon-sort"></i></a>
                    </th>
                    <th align="center">Class&nbsp;</th>
                    <th align="center">Product Type&nbsp;</th>
                    <th align="center" style="font-size:9px">Product Sub type&nbsp;</th>
                    <th align="center">Start Date&nbsp;</th>
                    <th>Status&nbsp;<a ng-click="sort_by('status');"><i class="glyphicon glyphicon-sort"></i></a></th>
                    <th align="center">Action&nbsp;</th>
                    </thead>
                    <tbody>
                    <tr ng-repeat="products in filteredList = (productDetailsList | filter : search | orderBy : predicate :reverse ) | startFrom:(currentPage-1)*entryLimit | limitTo: entryLimit ">
                        <td width="5%">{{products.id}}</td>
                        <td width="15%">{{products.name}}</td>
                        <td width="8%">{{products.shortCode}}</td>
                        <td width="15%" title="{{products.description}}" >{{ products.description.length > 30 ? (products.description | limitTo:30) + '...' : products.description }}</td>
                        <td width="7%">{{products.classification}}</td>
                        <td width="5%">{{products.type}}</td>
                        <td width="5%">{{products.subType}}</td>
                        <td width="8%">{{products.startDate}}</td>
                        <td width="5%">{{products.status}}</td>
                        <td valign="top">
                            <table>
                                <tr>
                                    <td>&nbsp;
                                        <!-- <img ng-click="viewProductDetailsData('View',products.id,products.status)" style="margin-bottom:8px;cursor:pointer" title="View Product" ng-src="img/viewEye.png" height="25px" width="25px">&nbsp;&nbsp;!-->
                                        <img ng-click="viewProductDetailsData('Update',products.id,products.status)"
                                             style="margin-bottom:8px; cursor:pointer" title="View and Update Product"
                                             ng-src="img/updateProduct.png" height="20px" width="20px">&nbsp;&nbsp;
                                    </td>
                                    <td>&nbsp;<img ng-click="inActiveProduct(products.id,'IN_ACTIVE')"
                                                   ng-if="products.status=='ACTIVE'"
                                                   style="margin-bottom:8px; cursor:pointer" title="Inactive Product"
                                                   ng-src="img/activeProduct.png" height="25px" width="25px">&nbsp;&nbsp;
                                    </td>
                                    <td>&nbsp;<img ng-click="inActiveProduct(products.id,'ACTIVE')"
                                                   style="margin-bottom:8px;cursor:pointer" title="Activate Product"
                                                   ng-if="products.status=='IN_ACTIVE'" ng-src="img/inactiveProduct.png"
                                                   height="22px" width="22px"></td>
                                    <td data-toggle="modal" id="viewProductImageDiv">&nbsp;<img
                                            ng-click="viewProductImage(products.id, products.name)"
                                            style="margin-bottom:8px; cursor:pointer"
                                            title="View and upload Product picture" ng-src="img/products.png"
                                            height="25px" width="25px">&nbsp;&nbsp;
                                    </td>

                                    <td data-toggle="modal" id="viewProductDimensionImageDiv">&nbsp;<img
                                            ng-click="viewProductDimensionImage(products.id, products.name , products.dimensionProfileId)"
                                            style="margin-bottom:8px; cursor:pointer; margin-right:2px"
                                            title="View and upload Product Dimension picture" ng-src="img/ProductDimension.png"
                                            height="20px" width="20px">&nbsp;&nbsp;
                                    </td>

                                    <td>&nbsp;
                                        <img ng-click="getProductAliasMapping(products.id)"
                                             style="margin-bottom:8px; cursor:pointer" title="Add or update Alias"
                                             ng-src="img/alias.png" height="20px" width="20px">&nbsp; &nbsp;
                                    </td>

                                    <td>
                                        <img ng-click="getProductDescription(products.id)"
                                             style="margin-bottom:8px; cursor:pointer" title="Add description"
                                             ng-src="img/updateCat.png" height="20px" width="20px">
                                    </td>

                                    <td>
                                        <img ng-click="viewCityProductImage(products.id, products.name)"
                                             style="margin-bottom:8px; cursor:pointer" title="Add City Image Mapping"
                                             ng-src="img/products.png" height="20px" width="20px">
                                    </td>
                                    <td>
                                        <img ng-click="openNutritionDetailModal(products)"
                                             style="margin-bottom:8px; cursor:pointer" title="Add nutrition count"
                                             ng-src="img/nutrition.png" height="34px" width="34px">
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    </tbody>
                </table>
                <!-- <pagination total-items="totalItems" ng-model="currentPage" max-size="5" boundary-links="true"  items-per-page="numPerPage" class="pagination"> </pagination> -->

                <!-- <uib-pagination total-items="totalItems" ng-model="currentPage" max-size="5" class="pagination-sm"  boundary-link-numbers="true" ng-disabled="false" rotate="true"  num-pages="numPages"></uib-pagination> -->
                <div align="center"><b>
                    <uib-pagination total-items="filteredList.length" ng-model="currentPage" max-size="5"
                                    boundary-link-numbers="true"  rotate="true"
                                    items-per-page="entryLimit" class="pagination-sm"></uib-pagination>
                </b></div>
            </div>


        </div>
        <div class="col-lg-10" ng-if="filteredItems == 0">
            <h4>No results found</h4>
        </div>
    </div>

</div>

<div class="modal fade" id="productModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">{{action}}&nbsp; Product</h4>
            </div>
            <form name="addProductForm" novalidate>
                <div class="modal-body">                                        <!-- start modal -->
                    <div class="form-group">
                        <label>Product Name *</label>
                        <input type="text" class="form-control" ng-model="productName" name="productName" required/>
                        <p style="color:red">{{validation}}</p>
                    </div>
                    <div class="form-group">
                        <label>Product Description *</label>
                        <textarea class="form-control" rows="1" ng-model="productDescription"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Short Code *</label>
                        <input type="text" class="form-control" maxlength="6" ng-model="productShortCode"
                               name="productShortCode" required/>
                    </div>
                    <!--  <div class="form-group">
                            <label>Vendor *</label>
                            <select class="form-control" ng-model="selectProductVender" ng-options="vendorListData as vendorListData.vendorName for vendorListData in vendorList track by vendorListData.vendorId">
                            </select>
                     </div> -->
                    <div class="form-group">
                        <label>Product Classification *</label>
                        <select class="form-control" ng-model="selectedProductClassification">
                            <option ng-repeat="selectedClass in productClassifications" value="{{selectedClass}}">
                                {{selectedClass}}
                            </option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Product Type *</label>
                        <select class="form-control" ng-model="selectedProductType"
                                ng-options="selectedCat as selectedCat.detail.code for selectedCat in productCategory track by selectedCat.detail.id"
                                ng-change="showCategoryDetails(selectedProductType.content)">
                        </select>
                    </div>
                    <div class="form-group"
                         data-ng-if="selectedProductType != null && selectedProductType.detail.id == 5">
                        <label>Preparation Mode *</label>
                        <select class="form-control" ng-model="selectedPreparationMode"
                                ng-options="prepTypes.name for prepTypes in preparationModes track by prepTypes.code">
                            <option></option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Product Subtype *</label>
                        <select class="form-control" ng-model="selectetProductSubtype"
                                ng-options="selectedSubCat as selectedSubCat.name for selectedSubCat in selectedProductSubTypeData track by selectedSubCat.id">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Station Category (Work Station Category) *</label>
                        <select class="form-control" ng-model="selectedStationCategory"
                                ng-options="selectedStationCat as selectedStationCat.name for selectedStationCat in workStationsStationCategory track by selectedStationCat.id"
                                ng-change="setSelectedStationCategory(selectedStationCategory)">
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Select Regular Tags</label><br/>
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="regularTagList" selected-model="selectedRegularTags" >
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Select Nutrition Tags</label><br/>
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="nutritionTagList" selected-model="selectedNutritionTags" >
                        </div>
                    </div>

                    <div class="form-group">
                        <label> Tax Code * </label>
                        <select class="form-control" ng-model="selectTaxCode"
                                ng-options="taxCodeData as (taxCodeData.code + '-' + taxCodeData.name)  for taxCodeData in taxCategory | orderBy: 'code' track by taxCodeData.code"> </select>
                    </div>

                    <div class="form-group">
                        <label>Web Type </label>
                        <select class="form-control" ng-model="selectedWebType"
                                ng-options="selectedWebTypeData as selectedWebTypeData.name for selectedWebTypeData in webAppType track by selectedWebTypeData.id">
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Attribute *</label>
                        <select class="form-control" ng-model="selectProductAttribute"
                                ng-options="attribute.name for attribute in attribute track by attribute.code"></select>
                    </div>
                    <div class="form-group">
                        <label>Product Start Date *</label>
                        <div class="datepicker" date-format="yyyy-MM-dd">
                            <input class="form-control" ng-model="productStartDate" placeholder="Select Date"
                                   type="text"
                                   required/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Product End Date *</label>
                        <div class="datepicker" date-format="yyyy-MM-dd">
                            <input class="form-control" ng-model="productEndDate" type="text" placeholder="Select Date"
                                   required/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Product SKU CODE *</label>
                        <input type="text" class="form-control" ng-model="productSKU" required/>
                    </div>
                    <div class="form-group">
                    <label>Dimension * </label>
                    <select class="form-control" ng-model="selectetProductDimension"
                            ng-options="selectedProductDimension as selectedProductDimension.detail.name for selectedProductDimension in productDimension track by selectedProductDimension.detail.id">
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Price Type * </label>
                        <select class="form-control" ng-model="selectPriceType"
                                ng-options="priceTypes.name for priceTypes in priceType track by priceTypes.code"></select>
                    </div>
                    <div class="form-group">
                        <label>Addition Item Types * </label>
                        <select class="form-control" ng-model="selectProductAdons"
                                ng-options="ProductAdonsOpt as ProductAdonsOpt.detail.code for ProductAdonsOpt in productAdons track by ProductAdonsOpt.detail.id">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Inventory Tracked *</label>
                        <select class="form-control" ng-model="selectInventoryTracked"
                                ng-options="InventoryData as InventoryData.name for InventoryData in inventoryTrackedData track by InventoryData.code">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Inventory Tracked Level*</label>
                        <select class="form-control" ng-model="selectedInventoryTrackedLevel"
                                ng-options="trackLevel.code as trackLevel.name for trackLevel in inventoryTrackedLevel">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Employee Meal Component *</label>
                        <select class="form-control" ng-model="selectEmployeeMealComponent"
                                ng-options="InventoryData as InventoryData.name for InventoryData in inventoryTrackedData track by InventoryData.code">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Is taxable COGS *</label>
                        <select class="form-control" ng-model="selectTaxableCogs"
                                ng-options="InventoryData as InventoryData.name for InventoryData in inventoryTrackedData track by InventoryData.code">
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Support Variant level ordering *</label>
                        <select class="form-control" ng-model="selectVariantOrderLevel"
                                ng-options="VariantLevelData as VariantLevelData.name for VariantLevelData in variantOrderLevel track by VariantLevelData.code">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Preparation time(in minutes) *</label>
                        <input type="text" class="form-control" ng-model="productPrepTime" required/>
                    </div>

                    <div class="form-group">
                        <label>Brand Name</label>
                        <select class="form-control"
                            data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                            data-ng-model="selectedBrand">
                        </select>
                    </div>

                    <div class="form-group clearfix" ng-if="action=='Add'">
                        <loading style align="center"></loading>
                        <button class="btn btn-primary pull-right" ng-click="submitAddProduct()" ng-hide="addBtnShow">
                            Add
                            Product
                        </button>
                    </div>

                    <div class="form-group clearfix" ng-if="action=='Update'">
                        <loading style align="center"></loading>
                        <button class="btn btn-primary pull-right" ng-click="submitUpdateProduct(prodID)"
                                ng-hide="updateBtnHide">Update Product
                        </button>
                    </div>
                </div><!-- End Modal -->
            </form>   <!-- End form -->
        </div>
    </div>
</div>


<div class="modal fade" id="productImageUploadModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="ImageUploadLabel">&nbsp; Product Image Upload - {{productNameDesc + "
                    ("}}{{productID + ")"}} </h4>
            </div>
            <form name="addProductForm" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-xs-12">
                            <form name="myForm">
                                <div class="col-xs-12">
                                    <label>Image Type</label>
                                    <div class="form-group">
                                        <select class="form-control" data-ng-model="imageCategoryType"
                                                data-ng-options="imageCategoryType as imageCategoryType.name + ' (' + imageCategoryType.value + ')'
                                                     for imageCategoryType in imageCategoryList" data-ng-change="updateIndex(1)">
                                        </select>
                                    </div>
                                </div>
                                <div class="col-xs-12">
                                    <div class="col-xs-12">
                                        <label>Index :</label>
                                        <input type="number" data-ng-model="indexOfImage" min="1" max="10"
                                               data-ng-change="updateIndex(indexOfImage)"/>
                                    </div>
                                    <div class="col-xs-12" data-ng-if="imageCategoryType.name != 'SHOWCASE_VIDEO'">
                                        <lable>Photo:</lable>
                                        <input class="btn btn-default" file-model="fileToUpload" type="file">
                                        <i ng-show="myForm.file.$error.required">*required</i><br>
                                        <i ng-show="myForm.file.$error.maxSize">File too large {{picFile.size /
                                            1000000|number:1}}MB: max {{picFile.$errorParam}}</i>
                                    </div>
                                    <div class="col-xs-12" data-ng-if="imageCategoryType.name === 'SHOWCASE_VIDEO'">
                                        <input class="btn btn-default" data-ng-model="showCaseVideo" type="text">
                                        <button class="btn btn-primary"
                                                ng-click="uploadProductVideo(showCaseVideo)">upload
                                        </button>
                                        <br><br>
                                    </div>

                                    <div class="col-xs-12" data-ng-if="imageCategoryType.name != 'SHOWCASE_VIDEO'">
                                        <button class="btn btn-primary"
                                                ng-click="checkFileDimensionsAndUpload()">Upload
                                        </button>
                                        <span class="btn btn-primary"
                                              data-ng-if="productImageDetails.showcaseVideo!=null" style="float: right"><a
                                                style="color: snow" data-ng-href="{{productImageDetails.showcaseVideo}}"
                                                target="_blank">playVideo</a></span>
                                    </div>

                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row" style="margin:8px; overflow-y:scroll; height:420px; display:block;">

                        <table ng-if="productImageDetails != null" class="table table-striped table-bordered "
                               style="font-size:11px">
                            <thead>
                            <th style="text-align: center;">S.NO</th>
                            <th style="text-align: center;">Product Image Type</th>
                            <th style="text-align: center;">Index</th>
                            <th style="text-align: center;">Product Image</th>
                            </thead>
                            <tbody style="align-content: center">
                            <!--<tr data-ng-if="productImageDetails.trendLow != null">
                                <th align="center" style="text-align: center; width: 50%">Trending Low</th>
                                <td style="width: 50%">
                                    <div data-ng-if="productImageDetails.trendLow != null" align="center">
                                        <img height="100px" width="100px"
                                             data-ng-click="openProductImageModal(productImageDetails.trendLow)"
                                             data-ng-src="{{imageSuffix}}{{productImageDetails.trendLow}}"/>
                                    </div>
                                </td>
                            </tr>-->
                            <!--<tr data-ng-if="productImageDetails.trendHigh != null">
                                <th align="center" style="text-align: center;">Trending High</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.trendHigh != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.trendHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.trendHigh}}"/>
                                </td>
                            </tr>-->
                            <!--<tr data-ng-if="productImageDetails.specialLow != null">
                                <th align="center" style="text-align: center;">Special Low</th>
                                <td>
                                    <div data-ng-if="productImageDetails.specialLow != null" align="center">
                                        <img height="100px" width="100px"
                                             data-ng-click="openProductImageModal(productImageDetails.specialLow)"
                                             data-ng-src="{{imageSuffix}}{{productImageDetails.specialLow}}"/>
                                    </div>
                                </td>
                            </tr>
                            <tr data-ng-if="productImageDetails.specialHigh != null">
                                <th align="center" style="text-align: center;">Special High</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.specialHigh != null"
                                                        ng-click="openProductImageModal(productImageDetails.specialHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.specialHigh}}"/>
                                </td>
                            </tr>-->
                            <tr data-ng-if="productImageDetails.gridLows != null" data-ng-repeat="image in productImageDetails.gridLows track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grid Lows</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                         data-ng-if="image != null"
                                         data-ng-click="openProductImageModal(image.url)"
                                         data-ng-src="{{imageSuffix}}{{image.url}}"/>
                                </td>
                            </tr>
                            <tr data-ng-if="productImageDetails.gridLowsWebp != null" data-ng-repeat="image in productImageDetails.gridLowsWebp track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grid Lows Webp</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                         data-ng-if="image != null"
                                         data-ng-click="openProductImageModal(image.url)"
                                         data-ng-src="{{imageSuffix}}{{image.url}}"/>
                                </td>
                            </tr>
                            <tr data-ng-if="productImageDetails.grids100X100!= null" data-ng-repeat="image in productImageDetails.grids100X100 track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grids 100 X 100</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffix}}{{image.url}}"/>
                                </td>
                            </tr>
                            </tr>
                            <tr data-ng-if="productImageDetails.grids400X400!= null" data-ng-repeat="image in productImageDetails.grids400X400 track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grids 400 X 400</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffix}}{{image.url}}"/>
                                </td>
                            </tr>

                            <tr data-ng-if="productImageDetails.gridHigh != null">
                                <td align="center">1</td>
                                <th align="center" style="text-align: center;">Grid High</th>
                                <td align="center">1</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.gridHigh != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.gridHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.gridHigh}}"/>
                                </td>
                            </tr>
                            <!--<tr data-ng-if="productImageDetails.listLow != null">
                                <th align="center" style="text-align: center;">List Low</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.listLow != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.listLow)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.listLow}}"/>
                                </td>
                            </tr>-->
                            <tr data-ng-if="productImageDetails.listHigh != null">
                                <td align="center">1</td>
                                <th align="center" style="text-align: center;">List High</th>
                                <td align="center">1</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.listHigh != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.listHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.listHigh}}"/>
                                </td>
                            </tr>
                            <!--<tr data-ng-if="productImageDetails.comboLow != null">
                                <th align="center" style="text-align: center;">Combo Low</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.comboLow != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.comboLow)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.comboLow}}"/>
                                </td>
                            </tr>
                            <tr data-ng-if="productImageDetails.comboHigh != null">
                                <th align="center" style="text-align: center;">Combo High</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.comboHigh != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.comboHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.comboHigh}}"/>
                                </td>
                            </tr>-->
                            <tr data-ng-if="productImageDetails.recommendationImages1200X1200!= null" data-ng-repeat="image in productImageDetails.recommendationImages1200X1200 track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Recommendation Image 1200X1200</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffix}}{{image.url}}"/>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>        <!-- End Modal -->
            </form>   <!-- End form -->
        </div>
    </div>
</div>

<div class="modal fade" id="productDimensionImageUploadModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="ImageDimensionUploadLabel">&nbsp; Product Image Upload - {{productNameDesc + "
                    ("}}{{productID + ")"}} </h4>
            </div>
            <form name="addProductForm" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-xs-12">
                            <form name="myForm">
                                <div class="col-xs-12">
                                    <label>Image Type</label>
                                    <div class="form-group">
                                        <select class="form-control" data-ng-model="imageCategoryType"
                                                data-ng-options="imageCategoryType as imageCategoryType.name + ' (' + imageCategoryType.value + ')'
                                                     for imageCategoryType in imageCategoryList" data-ng-change="updateIndex(1)">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Dimension * </label>
                                    <select class="form-control" ng-model="selectedDimension"
                                            ng-options="dimension as dimension.name for dimension in dimensionOptions track by dimension.id">
                                    </select>
                                </div>
                                <div class="col-xs-12">
                                    <div class=col-xs-12">
                                        <label>Index :</label>
                                        <input type="number" data-ng-model="indexOfImage" min="1" max="10"
                                               data-ng-change="updateIndex(indexOfImage)"/>
                                    </div>
                                    <div class="col-xs-12" data-ng-if="imageCategoryType.name != 'SHOWCASE_VIDEO'">
                                        <lable>Photo:</lable>
                                        <input class="btn btn-default" file-model="fileToUpload" type="file">
                                        <i ng-show="myForm.file.$error.required">*required</i><br>
                                        <i ng-show="myForm.file.$error.maxSize">File too large {{picFile.size /
                                            1000000|number:1}}MB: max {{picFile.$errorParam}}</i>
                                    </div>
                                    <div class="col-xs-12" data-ng-if="imageCategoryType.name === 'SHOWCASE_VIDEO'">
                                        <input class="btn btn-default" data-ng-model="showCaseVideo" type="text">
                                        <button class="btn btn-primary"
                                                ng-click="uploadProductVideo(showCaseVideo)">upload
                                        </button>
                                        <br><br>
                                    </div>

                                    <div class="col-xs-12" data-ng-if="imageCategoryType.name != 'SHOWCASE_VIDEO'">
                                        <button class="btn btn-primary"
                                                ng-click="checkFileDimensionsAndUploadNew()">Upload
                                        </button>
                                        <span class="btn btn-primary"
                                              data-ng-if="productImageDetails.showcaseVideo!=null" style="float: right"><a
                                                style="color: snow" data-ng-href="{{productImageDetails.showcaseVideo}}"
                                                target="_blank">playVideo</a></span>
                                    </div>

                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row" style="margin:8px; overflow-y:scroll; height:420px; display:block;">

                        <table ng-if="productDimensionImageDetails != null" class="table table-striped table-bordered "
                               style="font-size:11px">
                            <thead>
                            <th style="text-align: center;">S.NO</th>
                            <th style="text-align: center;">Product Image Type</th>
                            <th style="text-align: center;">Dimension</th>
                            <th style="text-align: center;">Index</th>
                            <th style="text-align: center;">Product Image</th>
                            </thead>
                            <tbody style="align-content: center">
                            <!--<tr data-ng-if="productImageDetails.trendLow != null">
                                <th align="center" style="text-align: center; width: 50%">Trending Low</th>
                                <td style="width: 50%">
                                    <div data-ng-if="productImageDetails.trendLow != null" align="center">
                                        <img height="100px" width="100px"
                                             data-ng-click="openProductImageModal(productImageDetails.trendLow)"
                                             data-ng-src="{{imageSuffix}}{{productImageDetails.trendLow}}"/>
                                    </div>
                                </td>
                            </tr>-->
                            <!--<tr data-ng-if="productImageDetails.trendHigh != null">
                                <th align="center" style="text-align: center;">Trending High</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.trendHigh != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.trendHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.trendHigh}}"/>
                                </td>
                            </tr>-->
                            <!--<tr data-ng-if="productImageDetails.specialLow != null">
                                <th align="center" style="text-align: center;">Special Low</th>
                                <td>
                                    <div data-ng-if="productImageDetails.specialLow != null" align="center">
                                        <img height="100px" width="100px"
                                             data-ng-click="openProductImageModal(productImageDetails.specialLow)"
                                             data-ng-src="{{imageSuffix}}{{productImageDetails.specialLow}}"/>
                                    </div>
                                </td>
                            </tr>
                            <tr data-ng-if="productImageDetails.specialHigh != null">
                                <th align="center" style="text-align: center;">Special High</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.specialHigh != null"
                                                        ng-click="openProductImageModal(productImageDetails.specialHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.specialHigh}}"/>
                                </td>
                            </tr>-->
                            <tr data-ng-if="productDimensionImageDetails.gridLows != null" data-ng-repeat="image in productDimensionImageDetails.gridLows track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grid Lows</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffixNew}}{{image.url}}"/>
                                </td>
                            </tr>
                            <tr data-ng-if="productDimensionImageDetails.gridLowsWebp != null" data-ng-repeat="image in productDimensionImageDetails.gridLowsWebp track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grid Lows Webp</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffixNew}}{{image.url}}"/>
                                </td>
                            </tr>
                            <tr data-ng-if="productDimensionImageDetails.grids100X100!= null" data-ng-repeat="image in productDimensionImageDetails.grids100X100 track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grids 100 X 100</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffixNew}}{{image.url}}"/>
                                </td>
                            </tr>
                            </tr>
                            <tr data-ng-if="productDimensionImageDetails.grids400X400!= null" data-ng-repeat="image in productDimensionImageDetails.grids400X400 track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">Grids 400 X 400</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffixNew}}{{image.url}}"/>
                                </td>
                            </tr>

                            <tr data-ng-if="productDimensionImageDetails.gridHigh != null">
                                <td align="center">1</td>
                                <th align="center" style="text-align: center;">Grid High</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <td align="center">1</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productDimensionImageDetails.gridHigh != null"
                                                        data-ng-click="openProductImageModal(productDimensionImageDetails.gridHigh)"
                                                        data-ng-src="{{imageSuffixNew}}{{productDimensionImageDetails.gridHigh}}"/>
                                </td>
                            </tr>
                            <!--<tr data-ng-if="productImageDetails.listLow != null">
                                <th align="center" style="text-align: center;">List Low</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.listLow != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.listLow)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.listLow}}"/>
                                </td>
                            </tr>-->
                            <tr data-ng-if="productDimensionImageDetails.listHigh != null">
                                <td align="center">1</td>
                                <th align="center" style="text-align: center;">List High</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <td align="center">1</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productDimensionImageDetails.listHigh != null"
                                                        data-ng-click="openProductImageModal(productDimensionImageDetails.listHigh)"
                                                        data-ng-src="{{imageSuffixNew}}{{productDimensionImageDetails.listHigh}}"/>
                                </td>
                            </tr>
                            <!--<tr data-ng-if="productImageDetails.comboLow != null">
                                <th align="center" style="text-align: center;">Combo Low</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.comboLow != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.comboLow)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.comboLow}}"/>
                                </td>
                            </tr>
                            <tr data-ng-if="productImageDetails.comboHigh != null">
                                <th align="center" style="text-align: center;">Combo High</th>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="productImageDetails.comboHigh != null"
                                                        data-ng-click="openProductImageModal(productImageDetails.comboHigh)"
                                                        data-ng-src="{{imageSuffix}}{{productImageDetails.comboHigh}}"/>
                                </td>
                            </tr>-->
                            <tr data-ng-if="productDimensionImageDetails.recommendationImages1200X1200!= null" data-ng-repeat="image in productDimensionImageDetails.recommendationImages1200X1200 track by $index">
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <th align="center" align="center" style="text-align: center;">{{productDimensionImageDetails.dimensionCode}}</th>
                                <th align="center" align="center" style="text-align: center;">Recommendation Image 1200X1200</th>
                                <td align="center">{{image.index}}</td>
                                <td align="center"><img height="100px" width="100px"
                                                        data-ng-if="image != null"
                                                        data-ng-click="openProductImageModal(image.url)"
                                                        data-ng-src="{{imageSuffixNew}}{{image.url}}"/>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>        <!-- End Modal -->
            </form>   <!-- End form -->
        </div>
    </div>
</div>

<div class="modal fade" id="productCityImageUploadModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-header" >
                <h4 class="modal-title" id="CityImageUploadLabel">&nbsp; Product Image Upload - {{productNameDesc + "
                    ("}}{{productID + ")"}} </h4>
            </div>
            <form name="addProductCityImageForm" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <form name="myForm">
                            <div class="col-xs-12">
                                <label>Select City</label>
                                <div class="form-group">
                                    <select  class="form-control" data-ng-model="model.selectedCity"
                                             data-ng-change="updateCity(model.selectedCity)"
                                        data-ng-options="city as city.name  for city in cityList track by city.name" >

<!--                                        data-ng-options="imageCategoryType as imageCategoryType.name + ' (' + imageCategoryType.value + ')'-->
<!--                                        for imageCategoryType in imageCategoryList"  >-->

                                        <!--                                             data-ng-options="cityName as city.name-->
                                        <!--                                                     for city in cityList track by city.name" >-->
                                    </select>
                                </div>
                            </div>
                            <div class="col-xs-12">
                                <label>Select Day Slot</label>
                                <div class="form-group">
                                    <select  class="form-control" data-ng-model="model.selectedDaySlot"
                                             data-ng-change="updateDaySlot(model.selectedDaySlot)"
                                             data-ng-options="city as city for city in dayslots" >

                                        <!--                                        data-ng-options="imageCategoryType as imageCategoryType.name + ' (' + imageCategoryType.value + ')'-->
                                        <!--                                        for imageCategoryType in imageCategoryList"  >-->

                                        <!--                                             data-ng-options="cityName as city.name-->
                                        <!--                                                     for city in cityList track by city.name" >-->
                                    </select>
                                </div>
                            </div>
                        </form>

                        <div>
                            Selected City : {{selectedCity.name}}
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label>City name</label>-->
<!--                            <select class="form-control"-->
<!--                                    data-ng-options="cityName as city.name for city  in cityList track by city.name"-->
<!--                                    data-ng-model="selectedCit"-->
<!--                                    data-ng-click="updateCity(selectedCit)">-->
<!--                            </select>-->
<!--                        </div>-->
                    </div>
                    <div class="row" >
                        <div class="col-xs-12">
                            <form name="myForm">
                                <div class="col-xs-12">
                                    <lable>Photo:</lable>
                                    <input class="btn btn-default" file-model="fileToUpload" type="file">
                                    <i ng-show="myForm.file.$error.required">*required</i><br>
                                    <i ng-show="myForm.file.$error.maxSize">File too large {{picFile.size /
                                        1000000|number:1}}MB: max {{picFile.$errorParam}}</i>
                                </div>
                                <div class="col-xs-12">
                                    <div class="col-xs-12" >
                                        <button class="btn btn-primary"
                                                ng-click="checkFileDimensionsAndUploadForCityLevel(selectedCity)">Upload
                                        </button>
                                    </div>

                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row" style="margin:8px; overflow-y:scroll; height:420px; display:block;">

                        <table ng-if="productCityImageDetails != null" class="table table-striped table-bordered "
                               style="font-size:11px">
                            <thead>
                            <th style="text-align: center;">S.NO</th>
                            <th style="text-align: center;">Product Image</th>
                            </thead>
                            <tbody style="align-content: center">
                            <tr>
                                <th align="center" align="center" style="text-align: center;">{{$index+1}}</th>
                                <td align="center"><img height="200px" width="200px"
                                                        data-ng-if="productCityImageDetails!= null && productCityImageDetails.imageUrl != null"
                                                        data-ng-click="openProductImageModal(productCityImageDetails.imageUrl)"
                                                        data-ng-src="{{imageSuffix}}{{productCityImageDetails.imageUrl}}"/>
                                </td>
                                <td><button class="btn btn-primary"
                                            ng-click="deactivateImageForCityAndSlot(selectedCity,selectedDaySlot)">Remove Image
                                </button></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>        <!-- End Modal -->
            </form>   <!-- End form -->
        </div>
    </div>
</div>

<div class="modal fade" id="aliasModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel2">&nbsp; Brand Alias mapping</h4>
            </div>
            <form name="addProductForm" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <table class="table table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>Brand Name</th>
                                <th>Alias</th>
                                <th>Description</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="mapping in assocatedAliasMappings track by mapping.entityAliasMappingId">
                                <td style="width: 20%">{{getBrandName(mapping.brandId)}}</td>
                                <td style="width: 30%">{{mapping.alias}}</td>
                                <td style="width: 50%">{{mapping.description}}</td>
                            </tr>
                            </tbody>
                        </table>


                    </div>
                    <hr>
                    <div class="row">

                        <div class="col-xs-12">
                            <form name="myForm">
                                <div class="row">
                                    <div class="col-xs-6">
                                        <lable>Select Brand</lable>
                                        <select class="form-control" ng-model="selectedBrandAliasMapping.brandId"
                                                data-ng-change="setAlias(selectedBrandAliasMapping.brandId)"
                                                ng-options="brand.brandId as brand.brandName for brand in brandList track by brand.brandId">
                                        </select>
                                    </div>

                                    <div class="col-xs-6" style="width: min-content;">
                                        <lable for="alias">Alias</lable>
                                        <input id="alias" name="alias" type="text"
                                               ng-model="selectedBrandAliasMapping.alias">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <lable for="description">Description</lable>
                                        <textarea style="width: inherit;" name="description" id="description"
                                                  type="text" ng-model="selectedBrandAliasMapping.description"/>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-xs-4">
                                        <span><button class="btn btn-primary"
                                                      ng-click="addAliasMapping($event)"
                                                      data-ng-if="selectedBrandAliasMapping.entityAliasMappingId == null">ADD</button></span>
                                        <span><button class="btn btn-primary"
                                                      ng-click="updateAliasMapping($event)"
                                                      data-ng-if="selectedBrandAliasMapping.entityAliasMappingId != null">UPDATE</button></span>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>        <!-- End Modal -->
            </form>   <!-- End form -->


        </div>
    </div>
</div>

<!-- Display Complete Product Pic Modal -->
<!--<div class="modal fade bd-example-modal-xl" style="background-color: black" id="displayCompleProductImageModal"-->
<!--tabindex="-1" role="dialog" aria-labelledby="myModalLabel3">-->
<!--<div class="modal-dialog modal-xl" role="document">-->
<!--<div class="modalImage">-->
<!--<div class="modal-body">-->
<!--<div class="image-wrapper">-->
<!--<a ng-href="{{imageSuffix}}{{ImageSrc}}" target="_blank">-->
<!--<img ng-src={{imageSuffix}}{{ImageSrc}}>-->
<!--</a>-->
<!--</div>-->
<!--<div class="text-muted image-description">-->
<!--{{ImageSrc}}-->
<!--</div>-->
<!--</div>-->
<!--</div>-->
<!--</div>-->
<!--</div>-->
<!--<div class="modal" id="displayCompleProductImageModal"-->
<!--style="width: fit-content;height: fit-content; overflow:visible;top: 16%;">-->
<!---->
<!--</div>-->
<div class="modal fade" id="displayCompleProductImageModal" style="z-index: 9999;" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document" style="width: 700px;">
        <div class="modal-content">
            <div class="frame" style="margin-top: 40px; margin: auto;">
                <img style="    max-height: 70vh;max-width: 70vw;"
                     data-ng-src="{{imageSuffix}}{{ImageSrc}}"/>
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="productDescriptionModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="productDescriptionModalLabel">Product description</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div id="ckeditor"></div>
                        <button class="btn btn-primary pull-right" ng-click="updateProductDescription()">UPDATE</button>
                    </div>
                </div>
            </div>        <!-- End Modal -->
        </div>
    </div>
</div>


<div class="modal fade" id="nutritionFactorModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" backdrop = 'static'>
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" data-ng-click="closeNutritionModal()" aria-label="Close"><span
                        aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="nutritionDetail">Product Nutrition Detail</h4>
            </div>
            <form name="addProductForm" novalidate>
                <div class="modal-body"> <!-- start modal -->
                    <div class="form-group">
                        <label>Select Source Type *</label>
                        <select class="form-control" ng-model="selectedSourceType" data-ng-change="setCountData()">
                            <option>CAFE</option>
                            <option>COD</option>
                        </select>
                    </div>
                        <div class="form-group" >
                            <label>CALORIE COUNT</label>
                            <input type="text" class="form-control" data-ng-change="setNutritionData('CALORIE_COUNT',calorieCount)" data-ng-model="calorieCount" />
                        </div>
                        <div class="form-group">
                            <label>PROTEIN COUNT</label>
                            <input type="text" class="form-control" data-ng-change="setNutritionData('PROTEIN_COUNT',proteinCount)" data-ng-model="proteinCount" />
                        </div>
                        <div class="form-group">
                            <label>FAT COUNT</label>
                            <input type="text" class="form-control" data-ng-change="setNutritionData('FAT_COUNT',fatCount)" data-ng-model="fatCount" />
                        </div>
                        <div class="form-group">
                            <label>CARBOHYDRATE COUNT</label>
                            <input type="text" class="form-control" data-ng-change="setNutritionData('CARBOHYDRATE_COUNT',carbohydrateCount)" data-ng-model="carbohydrateCount" />
                        </div>
                        <div class="form-group">
                            <label>FIBRE COUNT</label>
                            <input type="text" class="form-control" data-ng-change="setNutritionData('FIBRE_COUNT',fibreCount)" data-ng-model="fibreCount" />
                        </div>


                    <div class="form-group clearfix">
                        <button class="btn btn-primary pull-right" ng-click="submitNutritionData()"
                            ng-hide="updateBtnHide">Update Nutrition Data
                        </button>
                    </div>
                </div><!-- End Modal -->
            </form> <!-- End form -->
        </div>
    </div>
</div>
