<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h3>Access Control List</h3>
    </div>
</div>
<div class="row">
    <div class="col-xs-4">
        <select data-ng-model="selectedApp" data-ng-options="app.name as app for app in appList track by app.id"
                class="form-control" />
    </div>
    <div class="col-xs-4 form-group">
        <input type="button" class="btn btn-primary" value="Submit" data-ng-click="getAccessControls()"/>
    </div>
    <div class="col-xs-4 form-group text-right">
        <input type="button" class="btn btn-primary" value="Add Access" data-ng-click="openAddNewAccessModal()"/>
    </div>
</div>
<div class="row">
    <div class="col-xs-12">
        <table class="table table-bordered table-striped" data-ng-if="accessList.length>0">
            <tr>
                <td>Access Control Id</td>
                <td>Module</td>
                <td>Description</td>
                <td>Status</td>
                <td>Action</td>
            </tr>
            <tr data-ng-repeat="item in accessList track by item.id">
                <td>{{item.id}}</td>
                <td>{{item.module}}</td>
                <td>{{item.description}}</td>
                <td>{{item.status}}</td>
                <td>
                    <input type="button" class="btn btn-success" value="Activate" data-ng-if="item.status=='IN_ACTIVE'" data-ng-click="activateAccess(item.id)">
                    <input type="button" class="btn btn-danger" value="Deactivate" data-ng-if="item.status=='ACTIVE'" data-ng-click="deactivateAccess(item.id)">
                </td>
            </tr>
        </table>
        <div class="alert alert-info" data-ng-show="noResultMsg">No results found!</div>
    </div>
</div>

<div class="modal fade" id="addAccessModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Add Access</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12 form-group">
                        <label>Api Module Name</label>
                        <input type="text" class="form-control" data-ng-model="moduleName" />
                    </div>
                    <div class="col-xs-12 form-group text-right">
                        <input type="button" class="btn btn-primary" value="Add" data-ng-click="addAccess()" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>