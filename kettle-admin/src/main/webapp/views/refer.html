<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row">
	<div class="loginBox" style="margin-top:30px; width:250px;">
		<form>
			<div class="input-group">
				<input placeholder="Your Contact"
					type="text" class="form-control" data-ng-model="contactNumber" />
				<p class="error" data-ng-show="contactNumberError">Your contact cannot be empty</p>
			</div>
			<br />
			<div class="input-group">
				<input placeholder="Friend's Name"
					type="text" class="form-control" data-ng-model="referralName" />
				<p class="error" data-ng-show="referralNameError">Friend's name cannot be empty</p>
			</div>
			<br />
			<div class="input-group">
				<input placeholder="Friend's Contact"
					type="text" class="form-control" data-ng-model="referralNumber" />
				<p class="error" data-ng-show="referralNumberError">Friend's contact cannot be empty</p>
			</div>
			<br />
			<div class="text-center">
				<button data-ng-class="{loginDisabled:loginDisabled}" data-ng-click="refer()"
					class="btn btn-primary">Refer</button>
				<button data-ng-click="clear()" class="btn btn-default">Clear</button>
			</div>
			<div class="clearfix"></div>
		</form>
	</div>
</div>