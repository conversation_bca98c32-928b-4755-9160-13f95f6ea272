<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    input.checkbox {
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: inline;
    }

    input[type = text] {
        width: 100px;
        display: inline-block;
        margin-left: 10px;
    }
</style>
<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header text-center">Unit Partner Brand Metadata Dashboard
            <!--<button class="btn btn-primary pull-right" id="addProductIdDiv" data-toggle="modal" ng-click="addProduct()">
                <i class="fa fa-plus fw" /> Manage Metadata
            </button>-->
        </h1>
    </div>
</div>

<div class="alert alert-info">
    <h4>Use this panel to manage unit partner brand related metadata.</h4>
    <p>This contains data like loyalty points for unit partner brand</p>
</div>

<div class="row">
    <div class="col-xs-12">
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <label>Select Key *</label>
                    <select data-ng-options="key as key.name for key in unitPartnerBrandMetadataKeys track by key.id"
                            data-ng-model="selectedKey" data-ng-change="setSelectedKey(selectedKey)"
                            class="form-control"/>
                    <p style="color:red">{{validation}}</p>
                </div>
                <div class="form-group">
                    <label>Select Partner *</label>
                    <select data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                            data-ng-model="selectedPartner" data-ng-change="setSelectedPartner(selectedPartner)"
                            class="form-control"/>
                </div>
                <div class="form-group">
                    <label>Select Brand *</label>
                    <select data-ng-options="brand as brand.brandName for brand in brandList track by brand.brandId"
                            data-ng-model="selectedBrand" data-ng-change="setSelectedBrand(selectedBrand)"
                            class="form-control" disabled/>
                </div>
                <div class="form-group">
                    <input class="btn btn-primary"
                           data-ng-click="getMetadata()" type="button"
                           value="Get Metadata"/>
                </div>

                <div data-ng-show="unitPartnerBrandMetadata != null && unitPartnerBrandMetadata.length > 0">
                    <div class="form-group">
                        Filter:
                        <input type="text" ng-model="search" ng-change="filter()" placeholder="Filter"
                               class="form-control"/>
                    </div>

                    <table class="table table-striped table-bordered">
                        <thead valign="top">
                        <th>
                            <input type="checkbox" class="checkbox" data-ng-model='globalSelect'
                                   data-ng-change="selectAll()">
                            <span style="display: inline-block; vertical-align: super">Select All</span>
                        </th>
                        <th>Unit</th>
                        <th>Partner</th>
                        <th>Brand</th>
                        <th>Current Value</th>
                        <th>Current Status</th>
                        <th>
                            <div ng-if="globalMetaStatus">
                                <div>Value</div>
                                <input type="text" class="form-control"
                                       data-ng-change="setGlobalValue(globalValue)" data-ng-model="globalValue">
                            </div>
                            <div ng-if="!globalMetaStatus">
                                <div>Value</div>
                                <select class="form-control" data-ng-change="setGlobalValue(globalValue)" data-ng-model="globalValue"
                                        style="display: inline-block; width: 150px;"
                                        data-ng-options="status for status in mappingMetaStatusList "/>
                            </div>
                        </th>
                        <th>
                            <div>Status</div>
                            <select class="form-control" data-ng-model="globalStatus"
                                    style="display: inline-block; width: 150px;"
                                    data-ng-options="status as status.name for status in mappingStatusList track by status.id"/>
                        </th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="data in unitPartnerBrandMetadata | filter :search"
                            data-ng-style="data.selected === true ? {'background':'#b9eab9','cursor':'pointer'}:{}">
                            <td data-ng-click="toggleSelectMapping(data, true)">
                                <input type="checkbox" class="checkbox" ng-checked="data.selected"
                                       data-ng-click="toggleSelectMapping(data)">
                            </td>
                            <td data-ng-click="toggleSelectMapping(data, true)">{{data.unit.name}}</td>
                            <td data-ng-click="toggleSelectMapping(data, true)">{{data.partner.name}}</td>
                            <td data-ng-click="toggleSelectMapping(data, true)">{{data.brand.brandName}}</td>
                            <td data-ng-click="toggleSelectMapping(data, true)">{{data.value}}</td>
                            <td data-ng-click="toggleSelectMapping(data, true)">{{data.status}}</td>
                            <td>
                                <div ng-if="globalMetaStatus">
                                    <input type="text" class="form-control" ng-click="setNewValue(data)" data-ng-model="data.newValue">
                                </div>
                                <div ng-if="!globalMetaStatus">
                                    <select class="form-control" ng-click="setNewValue(data)" data-ng-model="data.newValue"
                                            style="display: inline-block; width: 150px;"
                                            data-ng-options="status for status in mappingMetaStatusList "/>
                                </div>
                            </td>
                            <td>
                                <select class="form-control" ng-click="setNewStatus(data)" data-ng-model="data.newStatus"
                                        style="display: inline-block; width: 150px;"
                                        data-ng-options="status as status.name for status in mappingStatusList track by status.id"/>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <div class="form-group">
                        <input class="btn btn-primary"
                               data-ng-click="submitAddMetadata()" type="button"
                               value="Add Metadata"/>
                    </div>
                </div>

            </div>
        </div>
    </div>

</div>