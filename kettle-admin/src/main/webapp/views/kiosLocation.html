<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div
	class="row"
	ng-init="init()">
	<div class="col-lg-12">
		<br>
		<h1 class="page-header">
			Location
			<button
				class="btn btn-primary pull-right"
				data-toggle="modal"
				id="addLocationDiv"
				ng-click="addKiosLocation()">
				<i class="fa fa-plus fw"></i> Add Location
			</button>
		</h1>
	</div>
</div>
<div class="row">
	<div class="col-xs-12">
		<label>Company Name *</label>
		<select
			class="form-control"
			ng-model="selectedLocationCompanyView"
			ng-options="selectedCompanyViewData as selectedCompanyViewData.companyName for selectedCompanyViewData in kiosCompanyList track by selectedCompanyViewData.companyName"
			ng-change="showCompanyViewList(selectedLocationCompanyView)">
		</select>
	</div>
	<div class="col-xs-12">
		<label>Office Name *</label>
		<select
			class="form-control"
			ng-model="selectedOfficeNameView"
			ng-options="selectedOfficeNameViewData as selectedOfficeNameViewData.officeName for selectedOfficeNameViewData in companyAllOfficeView track by selectedOfficeNameViewData.officeId"></select>
	</div>
	<div>&nbsp;</div>
	<div class="form-group clearfix">
		<button
			class="btn btn-primary pull-right"
			ng-click="submitViewLocation()">Submit</button>
	</div>
</div>
<div class="row">
	<div
		class="col-xs-12"
		ng-if="filteredItems > 0">
		<div class="row">
			<div class="col-xs-12">
				<table class="table table-striped table-bordered">
					<thead>
						<th>ID &nbsp;<a ng-click="sort_by('id');"><i
								class="glyphicon glyphicon-sort"></i></a></th>
						<th>Location Name&nbsp;<a ng-click="sort_by('name');"><i
								class="glyphicon glyphicon-sort"></i></a></th>
						<th>Location Short Code&nbsp;</th>
						<th>Location Address&nbsp;</th>
						<th>Assigned Unit&nbsp;</th>
						<th align="center">Status&nbsp;</th>
						<th colspan="2">Action&nbsp;</th>
					</thead>
					<tbody>
						<tr
							ng-repeat="locationDatas in filtered = (officeLocationListView | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
							<td>{{locationDatas.locationId}}</td>
							<td>{{locationDatas.locationName}}</td>
							<td>{{locationDatas.locationShortCode}}</td>
							<td>{{locationDatas.locationAddress}}</td>
							<td
								ng-if="locationDatas.assigned==true"
								style="color: green">{{locationDatas.assignedUnit.name}}</td>
							<td
								ng-if="locationDatas.assigned==false"
								style="color: red">Not Assigned</td>
							<td>{{locationDatas.locationStatus}}</td>
							<td><span style="display: inline-block; margin: 10px, 0">
									<button
										class="btn btn-primary pull-right"
										ng-if="locationDatas.locationStatus=='IN_ACTIVE'"
										ng-click="LocationStatusChange('ACTIVE',locationDatas.locationId)">Activate</button>
								</span> <span style="display: inline-block; margin: 10px, 0">
									<button
										class="btn btn-primary pull-right"
										ng-if="locationDatas.locationStatus=='ACTIVE'"
										ng-click="LocationStatusChange('IN_ACTIVE',locationDatas.locationId)">Deactivate</button>
								</span> <span>
									<button
										class="btn btn-primary pull-right"
										ng-click="editLocation(locationDatas.locationId)">Edit</button>
								</span></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div
		class="col-lg-10"
		ng-if="filteredItems == 0">
		<h4 align="center">No results found</h4>
	</div>
</div>
<!-- Modal -->
<div
	class="modal fade"
	id="locationModal"
	tabindex="-1"
	role="dialog"
	aria-labelledby="myModalLabel">
	<div
		class="modal-dialog"
		role="document">
		<div class="modal-content">
			<div
				class="modal-header"
				style="background-color: #337ab7; color: white">
				<button
					type="button"
					class="close"
					data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4
					class="modal-title"
					id="myModalLabel">{{action}} Location</h4>
			</div>
			<div class="modal-body">
				<form
					name="addOfficeForm"
					style="line-height: 30px"
					novalidate>
					<div class="row">
						<div class="col-xs-12">
							<label>Company Name *</label>
							<select
								class="form-control"
								ng-model="selectedCompanyNameList"
								ng-options="selectedCompanyNameData as selectedCompanyNameData.companyName for selectedCompanyNameData in kiosCompanyList track by selectedCompanyNameData.companyName"
								ng-change="showCompanyLocationList(selectedCompanyNameList)">
							</select>
						</div>
						<div class="col-xs-12">
							<label>Office Name *</label>
							<select
								class="form-control"
								ng-model="selectedOfficeNameList"
								ng-options="selectedOfficeData as selectedOfficeData.officeName for selectedOfficeData in companyAllOfficeList track by selectedOfficeData">
							</select>
						</div>
						<div class="col-xs-8">
							<label style="font-size: 11px">Location Name *</label>
							<input
								type="text"
								class="form-control"
								placeholder="Location Name"
								ng-model="locationName"
								required />
						</div>
						<div class="col-xs-4">
							<label style="font-size: 11px"> Short Code *</label>
							<input
								type="text"
								class="form-control"
								placeholder="Location ShortCode"
								ng-model="locationShortCode"
								required />
						</div>
						<div class="col-xs-12">
							<label style="font-size: 11px">Location Address </label>
							<input
								type="text"
								class="form-control"
								placeholder="Location Address"
								ng-model="locationAddress"
								required />
						</div>
						<div>&nbsp;</div>
						<div
							class="form-group clearfix"
							style="padding-right: 20px"
							ng-if="action=='Add'"
							ng-hide="showEmpBtn">
							<button
								class="btn btn-primary pull-right"
								ng-click="submitAddLocation()">Add</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
