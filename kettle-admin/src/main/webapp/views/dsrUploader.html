<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div
	class="row"
	data-ng-init="init()">
	<div class="col-xs-12">
		<h1 class="page-header">DSR Manager</h1>
	</div>
</div>
<div class="container-fluid">
	<div class="row ">
		<div class="col-xs-12">
			<div class="row margin-bottom10">
				<div class="col-xs-6">Unit</div>
				<div class="col-xs-6">
					<select
						ui-select2
						class="form-control"
						style="width: 100% !important"
						data-ng-model="selectedUnit"
						data-placeholder="Select a Unit"
						data-ng-change= "selectUnit(selectedUnit)"
						data-ng-options="u as u.name for u in units">
					</select>
				</div>
			</div>
			<div class="row margin-bottom10">
				<div class="col-xs-6">Start Date</div>
				<div class="col-xs-6">
					<div
						class="datepicker"
						data-date-format="yyyy-MM-dd">
						<input
							class="form-control"
							data-ng-model="startDate"
							type="date"
							placeholder="yyyy-MM-dd"
							required />
					</div>
				</div>
			</div>
			<div class="row margin-bottom10">
				<div class="col-xs-6">End Date</div>
				<div class="col-xs-6">
					<div
						class="datepicker"
						data-date-format="yyyy-MM-dd">
						<input
							class="form-control"
							data-ng-model="endDate"
							type="date"
							placeholder="yyyy-MM-dd"
							required />
					</div>
				</div>
			</div>
			<div class="row margin-bottom10">
				<div class="col-xs-12">
					<input
						type="button"
						class="btn btn-primary"
						value="Start Upload"
						data-ng-click="startUpload()" />
				</div>
			</div>
		</div>
	</div>
</div>
<div data-ng-if="completedList.length > 0" class="container-fluid" style="margin-top:20px">
	<div class="row ">
		<div class="col-xs-12">
			<div class="row margin-bottom10">
				<div class="col-xs-3">BusinessDate</div>
				<div class="col-xs-3">Status</div>
			</div>
			<div data-ng-repeat="d in completedList">
				<div class="row margin-bottom10">
					<div class="col-xs-3">{{d.businessDate| date : 'dd-MM-yyyy'}}</div>
					<div class="col-xs-3">{{d.status==true ? 'COMPLETED':'FAILED'}} {{d.status.errorMessage}}</div>
				</div>
			</div>
		</div>
	</div>
</div>