<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">Add Exception Days</h1>
    </div>
</div>

<div class="container-fluid">
    <div class="row ">
        <div class="col-xs-12">
            <div class="row margin-bottom10">
                <div class="col-xs-3">Day Close Date</div>
                <div class="col-xs-7">
                    <div class="datepicker" data-date-format="yyyy-MM-dd">
                        <input class="form-control" data-ng-model="exceptionDate" type="text"
                               placeholder="yyyy-MM-dd"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--<div data-ng-if="prodDetails.unitType!= undefined && !prodDetails.unitType">-->

<marquee behavior="scroll" direction="left" style="color: red"><b> Please Select Date first and then click on the desired region!!!</b></marquee>

    <div style="text-align: center;font-weight: bold">
        REGION
    </div>
    <div class="row row-spacing" data-ng-if="regions.length > 0">

        <div class="card" data-ng-repeat="r in regions  | orderBy">
            <div>
                <!-- data-ng-if=" r!= 'NCR_EDU'" -->
                <div class="col-xs-6 region-card " data-ng-if="$index % 2 == 0"
                     data-ng-click="getUnitList(r)">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item"
                            data-ng-class="{'card-selected':  r == selectedRegion}" style="font-weight: bold;border-color: #1b6d85;background-color:#999999">{{r}}
                        </li>
                    </ul>
                </div>
                <div class="col-xs-6 region-card" data-ng-if="$index % 2 != 0"
                     data-ng-click="getUnitList(r)">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item"
                            data-ng-class="{'card-selected':  r == selectedRegion}" style="font-weight: bold;border-color: #1b6d85;background-color:#999999">{{r}}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<!--</div>-->

<div ng-if="resultFound!=null && resultFound">
<div class="row"
     data-ng-if="unitListDetails != null && unitListDetails.length > 0">
    <div class="col-xs-12">
        <table class="table table-bordered">
            <thead style="background-color: #50773e; color: #ffffff">
            <tr>
                <th>Check &nbsp;
                    <input type="checkbox" data-ng-model='checkBoxModal.checkAll'
                           style="width: 20px; height: 20px" data-ng-click="updateAll()">
                </th>
                <th>Unit Id&nbsp;</th>
                <th>Unit Name</th>
                <th>Region</th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat='detail in unitListDetails'
                data-ng-class="{'row-selected': prodDetails[detail.id].checked}" >
                <td> <input type="checkbox" style="width: 33px; height: 20px"
                            data-ng-model='prodDetails[detail.id].checked'
                            data-ng-click="changeRow(prodDetails[detail.id].checked, prodDetails[detail.id], detail.price)"></td>
                <td>{{detail.id}}</td>
                <td>{{detail.name}}</td>
                <td>{{detail.code}}</td>
            </tr>
            <tr>
                <td align="right" colspan="8">
                    <button class="btn btn-primary pull-right" data-ng-click="submitDetails()">
                        SUBMIT
                    </button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
</div>