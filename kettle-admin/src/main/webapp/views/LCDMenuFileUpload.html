<!DOCTYPE html>
<html lang="en" data-ng-app="adminapp">
    <head>
        <title>LCD Menu Image Upload Management</title>
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.5/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.6.10/angular.min.js"></script>
        <!-- Include your controller -->
        <style>
          body {
            background-color: #f5f5f5;
            font-family: 'Roboto', sans-serif;
          }
          .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .loading-spinner {
            color: white;
            font-size: 24px;
          }
          .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
          }
          .floating-alert {
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            opacity: 1;
            transition: opacity 0.3s ease-in-out;
          }
          .floating-alert.ng-hide {
            opacity: 0;
          }
          .floating-alert .close {
            margin-left: 10px;
          }
          .breadcrumb-container {
            position: sticky;
            top: 60px;
            left: 0;
            right: 0;
            background: white;
            padding: 10px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 99;
            margin-bottom: 20px;
          }
          .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
          }
          .breadcrumb-item {
            display: flex;
            align-items: center;
            color: #5f6368;
            font-size: 14px;
            cursor: pointer;
            transition: color 0.2s ease;
          }
          .breadcrumb-item:hover {
            color: #1a73e8;
          }
          .breadcrumb-item.active {
            color: #1a73e8;
            font-weight: 500;
          }
          .breadcrumb-item i {
            margin-right: 8px;
            font-size: 16px;
          }
          .breadcrumb-item .separator {
            margin: 0 8px;
            color: #dadce0;
          }
          .history-stack {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding: 8px 0;
            margin-top: 10px;
            -webkit-overflow-scrolling: touch;
            background: white;
          }
          .history-btn {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            border: 1px solid #dadce0;
            border-radius: 16px;
            background: white;
            color: #5f6368;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
          }
          .history-btn:hover {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
          }
          .history-btn.active {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
          }
          .history-btn i {
            margin-right: 6px;
            font-size: 14px;
          }
          .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            width: 256px;
            background: white;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            padding: 20px 0;
            z-index: 100;
            overflow-y: auto;
          }
          .main-content {
            margin-left: 256px;
            padding: 20px;
            margin-top: 120px;
            min-height: calc(100vh - 120px);
            position: relative;
            z-index: 1;
          }
          .nav-item {
            padding: 12px 24px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            color: #5f6368;
          }
          .nav-item:hover {
            background-color: #f1f3f4;
          }
          .nav-item.active {
            background-color: #e8f0fe;
            color: #1a73e8;
          }
          .nav-item i {
            margin-right: 12px;
            font-size: 20px;
          }
          .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
          }
          .grid-item {
            position: relative;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
          }
          .grid-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
          }
          .grid-item.selected {
            border: 2px solid #ffa500;
          }
          .grid-item i {
            font-size: 32px;
            color: #1a73e8;
            margin-bottom: 10px;
          }
          .grid-item.file-item i {
            color: #666;
          }
          .grid-item.file-item:hover i {
            color: #1a73e8;
          }
          .grid-item span {
            display: block;
            color: #5f6368;
            font-size: 14px;
          }
          .folder-content {
            padding: 20px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
          }
          .folder-content i {
            font-size: 2em;
            color: #ffa500;
          }
          .folder-content span {
            text-align: center;
            word-break: break-word;
          }
          .file-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            cursor: pointer;
          }
          .file-preview {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px 8px 0 0;
          }
          .file-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          .file-item:hover .file-actions {
            opacity: 1;
          }
          .file-action-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 4px;
            padding: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .file-action-btn:hover {
            background: #fff;
            transform: scale(1.1);
          }
          .file-action-btn.delete-btn:hover {
            color: #ff0000;
          }
          .file-info {
            padding: 10px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }
          .file-name {
            font-weight: 500;
            margin-bottom: 5px;
            word-break: break-word;
          }
          .file-meta {
            font-size: 0.8em;
            color: #666;
          }
          .delete-folder-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            padding: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
          .grid-item:hover .delete-folder-btn {
            opacity: 1;
          }
          .delete-folder-btn:hover {
            color: #c82333;
          }
          .upload-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-top: 20px;
            border: 2px dashed #dadce0;
            transition: all 0.2s;
          }
          .upload-section:hover {
            border-color: #1a73e8;
            background: #f8f9fa;
          }
          .selected-files {
            margin-top: 15px;
            text-align: left;
          }
          .selected-file {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            display: flex;
            align-items: center;
            font-size: 14px;
          }
          .selected-file i {
            margin-right: 8px;
            color: #5f6368;
          }
          .action-buttons {
            position: fixed;
            bottom: 20px;
            left: 20px; /* Changed from right to left */
            display: flex;
            gap: 10px;
            z-index: 10001; /* Match upload button z-index */
          }
          .action-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #1a73e8;
            color: white;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .action-button:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
          }
          .action-button i {
            font-size: 20px;
          }
          .variant-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
          }
          .variant-group {
            margin-bottom: 15px;
          }
          .variant-group label {
            display: block;
            margin-bottom: 8px;
            color: #5f6368;
            font-size: 14px;
          }
          .variant-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 14px;
          }
          /* Reset any conflicting styles */
          .lcd-menu-container {
            margin-top: 80px;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
          }

          /* Make breadcrumb styles specific to this component */
          .lcd-menu-container .breadcrumb-container {
            position: relative;
            background: #fff;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
            z-index: 100;
          }

          .lcd-menu-container .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
          }

          .lcd-menu-container .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 14px;
          }

          .lcd-menu-container .breadcrumb-item.active {
            color: #1a73e8;
            font-weight: 500;
          }

          .lcd-menu-container .breadcrumb-separator {
            color: #999;
            margin: 0 4px;
          }

          .lcd-menu-container .history-stack {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding: 8px 0;
            margin-top: 10px;
            -webkit-overflow-scrolling: touch;
          }

          .lcd-menu-container .history-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 16px;
            color: #666;
            font-size: 13px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .lcd-menu-container .history-item:hover {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
          }

          .lcd-menu-container .history-item.active {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
          }

          /* Make grid styles specific to this component */
          .lcd-menu-container .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 16px;
            padding: 20px;
          }

          .lcd-menu-container .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .lcd-menu-container .grid-item:hover {
            border-color: #1a73e8;
            box-shadow: 0 2px 8px rgba(26,115,232,0.1);
          }

          .lcd-menu-container .grid-item.selected {
            border-color: #1a73e8;
            background: #e8f0fe;
          }

          .lcd-menu-container .grid-item i {
            font-size: 24px;
            color: #666;
            margin-bottom: 8px;
          }

          .lcd-menu-container .grid-item.selected i {
            color: #1a73e8;
          }

          .lcd-menu-container .grid-item span {
            font-size: 14px;
            color: #333;
            text-align: center;
          }

          .lcd-menu-container .grid-item.selected span {
            color: #1a73e8;
            font-weight: 500;
          }

          /* Make file upload styles specific to this component */
          .lcd-menu-container .file-upload-section {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            text-align: center;
          }

          .lcd-menu-container .file-upload-section.dragover {
            border-color: #1a73e8;
            background: #e8f0fe;
          }

          .lcd-menu-container .file-upload-section i {
            font-size: 48px;
            color: #666;
            margin-bottom: 16px;
          }

          .lcd-menu-container .file-upload-section p {
            color: #666;
            margin-bottom: 16px;
          }

          .lcd-menu-container .file-upload-section .upload-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #1a73e8;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s ease;
          }

          .lcd-menu-container .file-upload-section .upload-btn:hover {
            background: #1557b0;
          }

          .lcd-menu-container .file-upload-section input[type="file"] {
            display: none;
          }

          .lcd-menu-container .selected-files {
            margin-top: 16px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }

          .lcd-menu-container .selected-file {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 13px;
          }

          .lcd-menu-container .selected-file i {
            font-size: 16px;
            color: #666;
          }

          .lcd-menu-container .selected-file .remove-file {
            color: #dc3545;
            cursor: pointer;
          }

          /* Make action buttons specific to this component */
          .lcd-menu-container .action-buttons {
            position: fixed;
            bottom: 24px;
            right: 24px;
            display: flex;
            gap: 16px;
            z-index: 1000;
          }

          .lcd-menu-container .action-button {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #1a73e8;
            color: #fff;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
            z-index: 1000;
          }

          .lcd-menu-container .action-button:hover {
            background: #1557b0;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          }

          .lcd-menu-container .action-button i {
            font-size: 24px;
          }

          /* Make loading overlay specific to this component */
          .lcd-menu-container .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
          }

          .lcd-menu-container .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          /* Make notifications specific to this component */
          .lcd-menu-container .notification {
            position: fixed;
            bottom: 24px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            animation: slideUp 0.3s ease;
          }

          .lcd-menu-container .notification.error {
            background: #dc3545;
          }

          .lcd-menu-container .notification.success {
            background: #28a745;
          }

          @keyframes slideUp {
            from {
              transform: translate(-50%, 100%);
              opacity: 0;
            }
            to {
              transform: translate(-50%, 0);
              opacity: 1;
            }
          }

          .current-path, .search-path {
            margin-top: 10px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .current-path {
            border-left: 3px solid #1a73e8;
          }

          .search-path {
            border-left: 3px solid #28a745;
          }

          .current-path i, .search-path i {
            font-size: 14px;
          }

          .current-path i {
            color: #1a73e8;
          }

          .search-path i {
            color: #28a745;
          }

          .path-actions {
            margin-left: auto;
            display: flex;
            gap: 8px;
          }

          .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            background: transparent;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .action-btn:hover {
            background: #e8f0fe;
            color: #1a73e8;
          }

          .reset-btn:hover {
            color: #dc3545;
          }

          .download-btn:hover {
            color: #28a745;
          }

          .current-path {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .mode-toggle {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
          }

          .mode-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #dadce0;
            border-radius: 20px;
            background: white;
            color: #5f6368;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .mode-btn:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
            color: #1a73e8;
          }

          .mode-btn.active {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
          }

          .mode-btn i {
            font-size: 14px;
          }

          .search-results {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
          }

          .image-item {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .preview-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
          }

          .image-info {
            padding: 10px;
          }

          .image-name {
            display: block;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .image-path {
            display: block;
            font-size: 12px;
            color: #666;
            word-break: break-all;
          }

          /* Search Results Styles */
          .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
          }

          .view-toggle {
            display: flex;
            gap: 8px;
          }

          .view-btn {
            padding: 8px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: white;
            color: #5f6368;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .view-btn.active {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
          }

          .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
          }

          .image-preview {
            position: relative;
            overflow: hidden;
            border-radius: 8px 8px 0 0;
          }

          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          .image-preview:hover .image-overlay {
            opacity: 1;
          }

          /* List View Styles */
          .image-list {
            border: 1px solid #dadce0;
            border-radius: 8px;
            overflow: hidden;
          }

          .list-header {
            display: grid;
            grid-template-columns: 2fr 3fr 1fr;
            padding: 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #dadce0;
            font-weight: 500;
          }

          .list-item {
            display: grid;
            grid-template-columns: 2fr 3fr 1fr;
            padding: 12px;
            border-bottom: 1px solid #dadce0;
            align-items: center;
            transition: background-color 0.2s ease;
          }

          .list-item:hover {
            background-color: #f8f9fa;
          }

          .list-thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 12px;
          }

          .list-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            align-items: center;
            padding: 0 8px;
          }

          .list-action-btn {
            background: none;
            border: none;
            color: #5f6368;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
          }

          .list-action-btn:hover {
            background: #e8f0fe;
            color: #1a73e8;
          }

          .list-action-btn.delete-btn:hover {
            background: #fce8e6;
            color: #d93025;
          }

          .list-action-btn i {
            font-size: 16px;
          }

          .col-actions {
            text-align: right;
            padding-right: 16px;
          }

          /* Variants Screen Styles */
          .variants-screen {
            padding: 20px;
          }

          .variants-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
          }

          .variants-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
          }

          .variants-selection {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .upload-area {
            border: 2px dashed #dadce0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .upload-area:hover {
            border-color: #1a73e8;
            background: #f8f9fa;
          }

          .upload-area i {
            font-size: 48px;
            color: #5f6368;
            margin-bottom: 16px;
          }

          .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
          }

          .image-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .image-details {
            padding: 12px;
          }

          .image-variants {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 8px;
          }

          .variant-tag {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }

          .remove-file {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            padding: 4px;
          }

          .remove-file:hover {
            color: #c82333;
          }

          /* Add new styles for floating upload button and modal */
          .floating-upload-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #1a73e8;
            color: white;
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            cursor: pointer;
            z-index: 10001; /* Increased z-index to be above search results modal */
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
          }

          .floating-upload-btn:hover {
            transform: scale(1.1);
            background: #1557b0;
          }

          .floating-upload-btn i {
            font-size: 24px;
          }

          .upload-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .upload-modal-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .upload-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
          }

          .upload-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #5f6368;
          }

          .upload-modal-close:hover {
            color: #1a73e8;
          }

          .current-path-display {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 3px solid #1a73e8;
          }

          .upload-area-modal {
            border: 2px dashed #dadce0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 20px;
          }

          .upload-area-modal:hover {
            border-color: #1a73e8;
            background: #f8f9fa;
          }

          /* Add styles for variant info */
          .variant-info {
            margin-left: 15px;
            padding: 4px 8px;
            background: #e8f0fe;
            border-radius: 4px;
            font-size: 0.9em;
            color: #1a73e8;
          }

          .variant-info i {
            margin-right: 5px;
          }

          .variant-info.no-variants {
            background: #fce8e6;
            color: #d93025;
          }

          .current-path {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 20px;
          }

          .path-info {
            display: flex;
            align-items: center;
            flex: 1;
          }

          .path-info i {
            margin-right: 10px;
            color: #5f6368;
          }

          .path-actions {
            margin-left: auto;
          }

          /* Add styles for search results modal */
          .search-results-modal {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 12px rgba(0,0,0,0.15);
            z-index: 10000; /* Set z-index below the upload button */
            transform: translateY(100%);
            transition: transform 0.3s ease;
            max-height: 80vh;
            overflow-y: auto;
          }

          .search-results-modal.show {
            transform: translateY(0);
          }

          .modal-drag-handle {
            width: 40px;
            height: 4px;
            background: #dadce0;
            border-radius: 2px;
            margin: 12px auto;
            cursor: pointer;
          }

          /* Add styles for floating variant button */
          .floating-variant-btn {
            position: fixed;
            bottom: 30px;
            right: 100px; /* Position next to upload button */
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #34a853;
            color: white;
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
          }

          .floating-variant-btn:hover {
            transform: scale(1.1);
            background: #2d8745;
          }

          .floating-variant-btn i {
            font-size: 24px;
          }

          /* Add styles for variant selection modal */
          .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .modal-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
          }

          .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #5f6368;
          }

          .close-btn:hover {
            color: #1a73e8;
          }

          .modal-body {
            padding: 20px 0;
          }

          .form-group {
            margin-bottom: 20px;
          }

          .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #5f6368;
            font-size: 14px;
          }

          .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 14px;
          }

          .variant-options {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
          }

          .variant-list {
            max-height: 300px;
            overflow-y: auto;
          }

          .variant-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
          }

          .variant-item:last-child {
            border-bottom: none;
          }

          .variant-item label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
          }

          .variant-item input[type="checkbox"] {
            margin: 0;
          }

          /* Variant Selection Styles */
          .variant-selection-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
          }

          .variant-group-selection {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
          }

          .selected-variant-groups {
            margin-top: 20px;
          }

          .variant-group-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
          }

          .group-header h5 {
            margin: 0;
            color: #1a73e8;
          }

          .variants-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
          }

          .variant-item {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 6px 12px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
          }

          .variant-item button {
            background: none;
            border: none;
            color: #dc3545;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 12px;
          }

          .add-variant {
            display: flex;
            gap: 10px;
            margin-top: 10px;
          }

          .add-variant select {
            flex: 1;
          }

          .selected-variants-preview {
            background: #e8f0fe;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 14px;
            color: #1a73e8;
          }

          .selected-variants-preview i {
            margin-right: 5px;
          }

          /* Add styles for new folder modal */
          .new-folder-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
          }

          .new-folder-modal.show {
            opacity: 1;
            visibility: visible;
          }

          .new-folder-modal .modal-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            position: relative;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(0);
            transition: transform 0.3s ease;
          }

          .new-folder-modal.show .modal-content {
            transform: translateY(0);
          }

          .new-folder-modal .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
          }

          .new-folder-modal .modal-header h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
          }

          .new-folder-modal .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #5f6368;
            padding: 0;
            line-height: 1;
          }

          .new-folder-modal .close-btn:hover {
            color: #1a73e8;
          }

          .new-folder-modal .modal-body {
            padding: 20px 0;
          }

          .new-folder-modal .form-group {
            margin-bottom: 20px;
          }

          .new-folder-modal .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #5f6368;
            font-size: 14px;
          }

          .new-folder-modal .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
          }

          .new-folder-modal .form-control:focus {
            border-color: #1a73e8;
            outline: none;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
          }

          .new-folder-modal .text-center {
            text-align: center;
          }

          .new-folder-modal .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0 8px;
          }

          .new-folder-modal .btn-default {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            color: #5f6368;
          }

          .new-folder-modal .btn-default:hover {
            background: #e8eaed;
          }

          .new-folder-modal .btn-primary {
            background: #1a73e8;
            border: none;
            color: white;
          }

          .new-folder-modal .btn-primary:hover {
            background: #1557b0;
          }

          .new-folder-modal .btn-primary:disabled {
            background: #dadce0;
            cursor: not-allowed;
          }

          .new-folder-modal .btn i {
            margin-right: 8px;
          }

          /* Add styles for the dashboard */
          .dashboard-screen {
            padding: 20px;
          }

          .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
          }

          .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
          }

          .dashboard-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .dashboard-card h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
          }

          .path-summary {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }

          .path-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
          }

          .step-label {
            color: #5f6368;
            font-weight: 500;
          }

          .step-value {
            color: #1a73e8;
          }

          .variants-summary {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }

          .variant-group {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 12px;
          }

          .group-header h5 {
            margin: 0 0 8px 0;
            color: #1a73e8;
          }

          .variants-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }

          .variant-tag {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
          }

          .recent-images {
            max-height: 400px;
            overflow-y: auto;
          }

          .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 12px;
          }

          .image-item {
            background: #f8f9fa;
            border-radius: 4px;
            overflow: hidden;
          }

          .image-preview {
            position: relative;
            padding-top: 100%;
          }

          .image-preview img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          .image-preview:hover .image-overlay {
            opacity: 1;
          }

          .image-info {
            padding: 8px;
          }

          .image-name {
            display: block;
            font-size: 12px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .image-date {
            display: block;
            font-size: 10px;
            color: #5f6368;
          }

          .action-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            font-size: 16px;
          }

          .action-btn:hover {
            color: #1a73e8;
          }

          /* Update floating buttons container position */
          .floating-buttons-container {
            position: fixed;
            bottom: 24px;
            left: 296px; /* 256px (sidebar width) + 20px (padding) + 20px (additional right offset) */
            display: flex;
            align-items: center;
            gap: 16px;
            z-index: 10001;
          }

          .action-buttons {
            display: flex;
            gap: 16px;
          }

          .action-button {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #1a73e8;
            color: #fff;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
            z-index: 10001;
          }

          .action-button:hover {
            background: #1557b0;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          }

          .action-button i {
            font-size: 24px;
          }

          .floating-upload-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #1a73e8;
            color: white;
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            cursor: pointer;
            z-index: 10002; /* Higher than search results modal */
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
          }

          .floating-upload-btn:hover {
            transform: scale(1.1);
            background: #1557b0;
          }

          .floating-upload-btn i {
            font-size: 24px;
          }

          /* Add styles for edit mode overlay */
          .edit-mode-overlay {
            position: fixed;
            top: 0;
            left: 256px; /* Start after sidebar */
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 10002; /* Higher than search results modal */
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .edit-mode-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          /* Add styles for clear edit mode button */
          .clear-edit-btn {
            position: fixed;
            bottom: 24px;
            right: 90px; /* Position it to the left of the upload button */
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 10001;
          }

          .clear-edit-btn:hover {
            background: #c82333;
            transform: scale(1.1);
          }

          .clear-edit-btn i {
            font-size: 20px;
          }

          /* Update edit mode overlay styles */
          .edit-mode-overlay {
            position: fixed;
            top: 0;
            left: 256px;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 10002;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .edit-mode-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
          }

          .modal-header h3 {
            margin: 0;
            color: #1a73e8;
          }

          .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #5f6368;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
          }

          .close-btn:hover {
            background: #f1f3f4;
            color: #1a73e8;
          }

          /* New Step Modal Styles */
          .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
          }

          .modal-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }

          .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
          }

          .modal-header h3 {
            margin: 0;
            color: #333;
          }

          .modal-header .close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
          }

          .modal-header .close:hover {
            color: #333;
          }

          .modal-body {
            margin-bottom: 20px;
          }

          .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
          }

          .form-group {
            margin-bottom: 15px;
          }

          .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
          }

          .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
          }

          .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          }

          .btn-primary {
            background: #1a73e8;
            color: white;
          }

          .btn-primary:hover {
            background: #1557b0;
          }

          .btn-secondary {
            background: #f1f3f4;
            color: #333;
          }

          .btn-secondary:hover {
            background: #e8eaed;
          }

          .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          /* Add these styles to your existing CSS */
          .media-preview {
            position: relative;
            width: 100%;
            height: 150px;
            overflow: hidden;
            border-radius: 8px 8px 0 0;
            background: #f8f9fa;
          }

          .media-preview img,
          .media-preview video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .media-preview i {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: #666;
          }

          .file-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 0.8em;
            color: #666;
          }

          .file-meta span {
            display: inline-block;
          }

          /* Add these styles to your existing CSS */
          .preview-modal-content {
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
          }

          .preview-body {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
          }

          .media-preview-container {
            width: 100%;
            max-height: 60vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
          }

          .preview-media {
            max-width: 100%;
            max-height: 60vh;
            object-fit: contain;
          }

          .preview-icon {
            font-size: 64px;
            color: #666;
          }

          .file-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
          }

          .detail-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
          }

          .detail-label {
            font-weight: 500;
            color: #666;
            font-size: 0.9em;
          }

          .detail-value {
            color: #333;
            word-break: break-word;
          }

          .preview-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 15px;
            border-top: 1px solid #eee;
          }

          /* Variant Management Styles */
          .variant-management-modal {
            max-width: 1000px;
            width: 90%;
            max-height: 90vh;
          }

          .variant-management-body {
            display: flex;
            gap: 20px;
            padding: 20px;
            height: calc(90vh - 100px);
          }

          .groups-section, .items-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
          }

          .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
          }

          .groups-list, .items-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
          }

          .group-item, .item-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }

          .group-item.selected {
            border: 2px solid #007bff;
          }

          .group-info, .item-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
          }

          .group-name, .item-name {
            font-weight: 500;
          }

          .group-status, .item-status {
            font-size: 0.8em;
            padding: 2px 6px;
            border-radius: 3px;
          }

          .status.active {
            background: #d4edda;
            color: #155724;
          }

          .status.inactive {
            background: #f8d7da;
            color: #721c24;
          }

          .group-actions, .item-actions {
            display: flex;
            gap: 5px;
          }

          .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
          }

          /* Variant Management Styles */
          .variant-management {
            display: flex;
            gap: 20px;
            height: 500px;
          }

          .groups-section, .items-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
          }

          .section-header {
            padding: 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .section-header h3 {
            margin: 0;
            font-size: 16px;
          }

          .add-btn {
            padding: 5px 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          }

          .add-btn:hover {
            background: #45a049;
          }

          .groups-list, .items-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
          }

          .group-item, .item-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
          }

          .group-item.selected, .item-item.selected {
            background: #e3f2fd;
            border-color: #2196F3;
          }

          .group-info, .item-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
          }

          .group-name, .item-name {
            font-weight: bold;
          }

          .group-status, .item-status {
            font-size: 12px;
            padding: 2px 5px;
            border-radius: 3px;
            background: #f44336;
            color: white;
          }

          .group-status.active, .item-status.active {
            background: #4CAF50;
          }

          .group-actions, .item-actions {
            display: flex;
            gap: 5px;
          }

          .edit-btn, .delete-btn {
            padding: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
          }

          .edit-btn {
            background: #2196F3;
            color: white;
          }

          .delete-btn {
            background: #f44336;
            color: white;
          }

          .edit-btn:hover {
            background: #1976D2;
          }

          .delete-btn:hover {
            background: #d32f2f;
          }

          /* Form Styles */
          .form-group {
            margin-bottom: 15px;
          }

          .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
          }

          .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
          }

          .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
          }

          .cancel-btn, .save-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          }

          .cancel-btn {
            background: #f44336;
            color: white;
          }

          .save-btn {
            background: #4CAF50;
            color: white;
          }

          .cancel-btn:hover {
            background: #d32f2f;
          }

          .save-btn:hover {
            background: #45a049;
          }
        </style>
      </head>
      <body ng-controller="LCDMenuFileUploadController">
        <!-- Loading Overlay -->
        <div class="loading-overlay" ng-show="isLoading">
          <div class="loading-spinner">
                <i class="fa fa-spinner fa-spin"></i> Loading...
          </div>
        </div>
      
        <!-- Floating Notifications -->
        <div class="notification-container">
            <div class="floating-alert alert alert-danger" ng-show="errorMessage">
            <button type="button" class="close" ng-click="errorMessage = ''">&times;</button>
            <strong>Error:</strong> {{errorMessage}}
          </div>
            <div class="floating-alert alert alert-success" ng-show="successMessage">
            <button type="button" class="close" ng-click="successMessage = ''">&times;</button>
            <strong>Success:</strong> {{successMessage}}
            </div>
          </div>
          
        <!-- Breadcrumb and History -->
        <div class="breadcrumb-container">
            <!-- Add mode toggle -->
            <div class="mode-toggle">
              <div class="nav-item" ng-click="openVariantManagementModal()">
                <i class="fa fa-list"></i>
                <span>Manage Variants</span>
              </div>
                <button class="mode-btn" ng-click="openNewStepModal()">
                    <i class="fa fa-plus"></i> Add New Step
                </button>
                <button class="mode-btn" ng-class="{'active': uploadMode}" ng-click="toggleUploadMode()">
                    <i class="fa fa-upload"></i>
                    <span>Upload Mode</span>
                </button>
                <button class="mode-btn" ng-class="{'active': !uploadMode}" ng-click="toggleUploadMode()">
                    <i class="fa fa-sitemap"></i>
                    <span>Normal Mode</span>
                </button>
                </div>
            <!-- <div class="breadcrumb">
                <div class="breadcrumb-item" 
                     ng-repeat="(index, item) in navigationHistory" 
                     ng-class="{'active': index === navigationHistory.length - 1}"
                     ng-click="goToHistoryItem(index)">
                    <i class="fa" ng-class="getStepIcon(item.step)"></i>
                    <span>{{item.label}}</span>
                    <span ng-if="index < navigationHistory.length - 1" class="separator">/</span>
              </div>
                  </div> -->
            <div class="history-stack">
                <button class="history-btn" 
                        ng-repeat="(index, item) in navigationHistory" 
                        ng-click="goToHistoryItem(index)" 
                        ng-class="{'active': index === navigationHistory.length - 1}">
                    <i class="fa" ng-class="getStepIcon(item.step)"></i>
                    <span>{{item.label}}</span>
                </button>
                </div>
            <!-- Add current path display with actions -->
            <div class="current-path">
                <div class="path-info">
                    <i class="fa fa-folder-open"></i>
                    <span>{{currentPath}}</span>
                    <span class="variant-info" ng-if="formData.variants.length > 0">
                        <i class="fa fa-tags"></i>
                        Variants: {{formData.variants.join(', ')}}
                    </span>
                    <span class="variant-info no-variants" ng-if="!formData.variants.length">
                        <i class="fa fa-exclamation-circle"></i>
                        No variants selected
                    </span>
                    <div class="path-actions">
                        <button class="action-btn reset-btn" ng-click="resetPath()">
                            <i class="fa fa-refresh"></i> Reset
                        </button>
                        <button class="action-btn download-btn" ng-click="downloadPathImages()">
                            <i class="fa fa-download"></i> Download
                        </button>
                  </div>
                </div>
              </div>
            </div>
          
        <!-- Add search path display -->
        <div class="search-path" ng-if="currentSearchPath">
            <i class="fa fa-search"></i>
            <span>Searching: {{currentSearchPath}}</span>
          </div>
          
      
        <!-- <div class="search-results" ng-if="uploadedImages && uploadedImages.length > 0">
            <div class="results-header">
                <h3>Search Results</h3>
                <div class="view-toggle">
                    <button class="view-btn" ng-class="{'active': viewMode === 'grid'}" ng-click="viewMode = 'grid'">
                        <i class="fa fa-th-large"></i>
                    </button>
                    <button class="view-btn" ng-class="{'active': viewMode === 'list'}" ng-click="viewMode = 'list'">
                        <i class="fa fa-list"></i>
                    </button>
                </div>
              </div>
            
          
            <div class="image-grid" ng-if="viewMode === 'grid'">
                <div class="image-item" ng-repeat="image in uploadedImages">
                    <div class="image-preview">
                        <img ng-src="{{image.url}}" alt="{{image.name}}" class="preview-image">
                        <div class="image-overlay">
                            <button class="action-btn edit-btn" ng-click="editImage(image)" title="Edit">
                                <i class="fa fa-pencil"></i>
                            </button>
                            <button class="action-btn delete-btn" ng-click="removeImage(image.id)" title="Delete">
                                <i class="fa fa-trash"></i>
                            </button>
                </div>
              </div>
                    <div class="image-info">
                        <span class="image-name">{{image.name}}</span>
                        <span class="image-path">{{image.version}}/{{image.region}}/{{image.priceProfile}}/{{image.orientation}}/{{image.slot}}/{{image.lcdType}}</span>
                </div>
              </div>
            </div>
            
           
            <div class="image-list" ng-if="viewMode === 'list'">
                <div class="list-header">
                    <div class="col-name">Name</div>
                    <div class="col-path">Path</div>
                    <div class="col-actions">Actions</div>
                </div>
                <div class="list-item" ng-repeat="image in uploadedImages">
                    <div class="col-name">
                        <img ng-src="{{image.url}}" alt="{{image.name}}" class="list-thumbnail">
                        <span>{{image.name}}</span>
              </div>
                    <div class="col-path">{{image.version}}/{{image.region}}/{{image.priceProfile}}/{{image.orientation}}/{{image.slot}}/{{image.lcdType}}</div>
                    <div class="col-actions">
                        <button class="action-btn edit-btn" ng-click="editImage(image)" title="Edit">
                            <i class="fa fa-pencil"></i>
                        </button>
                        <button class="action-btn delete-btn" ng-click="removeImage(image.id)" title="Delete">
                            <i class="fa fa-trash"></i>
                        </button>
                </div>
              </div>
            </div>
            </div> -->
            
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="nav-item" ng-click="goToStep('version')" ng-class="{'active': currentStep === 'version'}">
              <i class="fa fa-folder"></i>
              <span>Version</span>
            </div>
            <div class="nav-item" ng-click="goToStep('region')" ng-class="{'active': currentStep === 'region'}">
              <i class="fa fa-globe"></i>
              <span>Region</span>
            </div>
            <div class="nav-item" ng-click="goToStep('priceProfile')" ng-class="{'active': currentStep === 'priceProfile'}">
              <i class="fa fa-tag"></i>
              <span>Price Profile</span>
            </div>
            <div class="nav-item" ng-click="goToStep('orientation')" ng-class="{'active': currentStep === 'orientation'}">
              <i class="fa fa-arrows-alt"></i>
              <span>Orientation</span>
            </div>
            <div class="nav-item" ng-click="goToStep('slot')" ng-class="{'active': currentStep === 'slot'}">
              <i class="fa fa-square"></i>
              <span>Slot</span>
            </div>
            <div class="nav-item" ng-click="goToStep('lcdType')" ng-class="{'active': currentStep === 'lcdType'}">
              <i class="fa fa-desktop"></i>
              <span>LCD Type</span>
            </div>
            <div class="nav-item" ng-click="openVariantManagementModal()">
              <i class="fa fa-list"></i>
              <span>Manage Variants</span>
            </div>
            <div class="nav-item" ng-click="openNewStepModal()">
              <i class="fa fa-plus"></i>
              <span>Add New Step</span>
            </div>
          </div>
            
        <!-- Main Content -->
        <div class="main-content">
            <h1 class="page-header">LCD Menu Image Upload</h1>

            <!-- Version Selection -->
            <div ng-if="currentStep === 'version'" class="grid-container">
                <!-- Folders -->
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData.version === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate('version', folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
                
                <!-- Files -->
                <!-- <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                    <div class="file-content" ng-click="previewFile(file)">
                        <div class="media-preview">
                            <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                            <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                                <source ng-src="{{file.url}}" type="video/mp4">
                            </video>
                            <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">{{file.name}}</div>
                            <div class="file-meta">
                                <span>{{file.size | bytes}}</span>
                                <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                                <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                            <i class="fa fa-pencil"></i>
                        </button>
                        <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div> -->
                
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
            </div>
            
            <!-- Region Selection -->
            <div ng-if="currentStep === 'region'" class="grid-container">
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData.region === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate('region', folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
                <!-- Files -->
                <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                  <div class="file-content" ng-click="previewFile(file)">
                      <div class="media-preview">
                          <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                          <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                              <source ng-src="{{file.url}}" type="video/mp4">
                          </video>
                          <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                      </div>
                      <div class="file-info">
                          <div class="file-name">{{file.name}}</div>
                          <div class="file-meta">
                              <span>{{file.size | bytes}}</span>
                              <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                              <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                          </div>
                      </div>
                  </div>
                  <div class="file-actions">
                      <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                          <i class="fa fa-pencil"></i>
                      </button>
                      <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                          <i class="fa fa-trash"></i>
                      </button>
                  </div>
              </div>
            </div>

            <!-- Price Profile Selection -->
            <div ng-if="currentStep === 'priceProfile'" class="grid-container">
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData.priceProfile === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate('priceProfile', folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
                <!-- Files -->
                <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                  <div class="file-content" ng-click="previewFile(file)">
                      <div class="media-preview">
                          <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                          <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                              <source ng-src="{{file.url}}" type="video/mp4">
                          </video>
                          <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                      </div>
                      <div class="file-info">
                          <div class="file-name">{{file.name}}</div>
                          <div class="file-meta">
                              <span>{{file.size | bytes}}</span>
                              <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                              <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                          </div>
                      </div>
                  </div>
                  <div class="file-actions">
                      <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                          <i class="fa fa-pencil"></i>
                      </button>
                      <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                          <i class="fa fa-trash"></i>
                      </button>
                  </div>
              </div>
            </div>

            <!-- Orientation Selection -->
            <div ng-if="currentStep === 'orientation'" class="grid-container">
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData.orientation === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate('orientation', folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
                <!-- Files -->
                <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                  <div class="file-content" ng-click="previewFile(file)">
                      <div class="media-preview">
                          <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                          <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                              <source ng-src="{{file.url}}" type="video/mp4">
                          </video>
                          <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                      </div>
                      <div class="file-info">
                          <div class="file-name">{{file.name}}</div>
                          <div class="file-meta">
                              <span>{{file.size | bytes}}</span>
                              <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                              <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                          </div>
                      </div>
                  </div>
                  <div class="file-actions">
                      <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                          <i class="fa fa-pencil"></i>
                      </button>
                      <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                          <i class="fa fa-trash"></i>
                      </button>
                  </div>
              </div>
            </div>

            <!-- Slot Selection -->
            <div ng-if="currentStep === 'slot'" class="grid-container">
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData.slot === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate('slot', folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
                <!-- Files -->
                <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                  <div class="file-content" ng-click="previewFile(file)">
                      <div class="media-preview">
                          <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                          <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                              <source ng-src="{{file.url}}" type="video/mp4">
                          </video>
                          <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                      </div>
                      <div class="file-info">
                          <div class="file-name">{{file.name}}</div>
                          <div class="file-meta">
                              <span>{{file.size | bytes}}</span>
                              <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                              <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                          </div>
                      </div>
                  </div>
                  <div class="file-actions">
                      <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                          <i class="fa fa-pencil"></i>
                      </button>
                      <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                          <i class="fa fa-trash"></i>
                      </button>
                  </div>
              </div>
            </div>

            <!-- LCD Type Selection -->
            <div ng-if="currentStep === 'lcdType'" class="grid-container">
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData.lcdType === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate('lcdType', folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
                <!-- Files -->
                <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                  <div class="file-content" ng-click="previewFile(file)">
                      <div class="media-preview">
                          <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                          <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                              <source ng-src="{{file.url}}" type="video/mp4">
                          </video>
                          <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                      </div>
                      <div class="file-info">
                          <div class="file-name">{{file.name}}</div>
                          <div class="file-meta">
                              <span>{{file.size | bytes}}</span>
                              <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                              <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                          </div>
                      </div>
                  </div>
                  <div class="file-actions">
                      <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                          <i class="fa fa-pencil"></i>
                      </button>
                      <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                          <i class="fa fa-trash"></i>
                      </button>
                  </div>
              </div>
            </div>

            <!-- Generic Step Selection for Custom Steps -->
            <div ng-if="!['version', 'region', 'priceProfile', 'orientation', 'slot', 'lcdType', 'variants'].includes(currentStep)" class="grid-container">
                <!-- Folders -->
                <div class="grid-item" ng-repeat="folder in currentStepFolders" 
                     ng-class="{'selected': formData[currentStep] === folder.name}">
                    <div class="folder-content" ng-click="quickNavigate(currentStep, folder.name)">
                        <i class="fa fa-folder"></i>
                        <span>{{folder.name}}</span>
                    </div>
                    <button class="delete-folder-btn" ng-click="deleteFolder(folder)">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>

                
                <!-- Files -->
                <div class="grid-item file-item" ng-repeat="file in currentStepFiles">
                    <div class="file-content" ng-click="previewFile(file)">
                        <div class="media-preview">
                            <img ng-if="file.type.startsWith('image/')" ng-src="{{file.url}}" alt="{{file.name}}" class="file-preview">
                            <video ng-if="file.type === 'video/mp4'" class="file-preview" controls>
                                <source ng-src="{{file.url}}" type="video/mp4">
                            </video>
                            <i ng-if="!file.type.startsWith('image/') && file.type !== 'video/mp4'" class="fa fa-file"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">{{file.name}}</div>
                            <div class="file-meta">
                                <span>{{file.size | bytes}}</span>
                                <span ng-if="file.uploadTime">• Uploaded: {{file.uploadTime | date:'short'}}</span>
                                <span ng-if="!file.uploadTime">• Modified: {{file.modified | date:'short'}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="file-action-btn" ng-click="editFile(file)" title="Edit">
                            <i class="fa fa-pencil"></i>
                        </button>
                        <button class="file-action-btn delete-btn" ng-click="deleteFile(file)" title="Delete">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="grid-item create-folder" ng-click="openNewFolderModal()">
                    <i class="fa fa-plus"></i>
                    <span>Create New {{currentStep | capitalize}}</span>
                </div>
            </div>

            <!-- Variant Selection -->
            <div ng-if="currentStep === 'variants'" class="dashboard-screen">
                <div class="dashboard-header">
                    <h3>Path Summary</h3>
                    <div class="dashboard-actions">
                        <button class="btn btn-primary" ng-click="searchImages()">
                            <i class="fa fa-search"></i> Search Images
                        </button>
                        <button class="btn btn-success" ng-click="downloadPathImages()">
                            <i class="fa fa-download"></i> Download All
                        </button>
                    </div>
                </div>

                <div class="dashboard-content">
                    <!-- Path Summary Card -->
                    <div class="dashboard-card">
                        <h4>Current Path</h4>
                        <div class="path-summary">
                            <div class="path-item" ng-repeat="(step, value) in formData">
                                <span class="step-label">{{step | capitalize}}:</span>
                                <span class="step-value">{{value || 'Not Selected'}}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Variants Card -->
                    <div class="dashboard-card" ng-if="variantGroupSelections.length > 0">
                        <h4>Selected Variants</h4>
                        <div class="variants-summary">
                            <div class="variant-group" ng-repeat="group in variantGroupSelections">
                                <div class="group-header">
                                    <h5>{{group.group}}</h5>
                                </div>
                                <div class="variants-list">
                                    <span class="variant-tag" ng-repeat="variant in group.variants">
                                        {{variant}}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Images Card -->
                    <div class="dashboard-card">
                        <h4>Recent Images</h4>
                        <div class="recent-images">
                            <div class="image-grid">
                                <div class="image-item" ng-repeat="image in recentImages">
                                    <div class="image-preview">
                                        <img ng-src="{{image.url}}" alt="{{image.name}}">
                                        <div class="image-overlay">
<!--                                            <button class="action-btn" ng-click="editImage(image)" title="Edit">-->
<!--                                                <i class="fa fa-pencil"></i>-->
<!--                                            </button>-->
                                            <button class="action-btn" ng-click="removeImage(image.id)" title="Delete">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="image-info">
                                        <span class="image-name">{{image.name}}</span>
                                        <span class="image-date">{{image.uploadDate | date:'short'}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="action-button" ng-click="searchImages()">
                    <i class="fa fa-search"></i>
                    </button>
                <button class="btn btn-primary" ng-click="downloadExcel()" ng-if="uploadedImages.length > 0">
                    <i class="fa fa-file-excel-o"></i> Download Excel
                </button>
            </div>
        </div>

        <!-- Upload Modal -->
        <div class="modal" ng-show="showUploadModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Upload Images</h3>
                    <button class="close-btn" ng-click="closeUploadModal()">&times;</button>
                </div>
                
                <div class="current-path-display" ng-if="currentPath">
                    <i class="fa fa-folder-open"></i>
                    <span>Current Path: {{currentPath}}</span>
                </div>

                <!-- Variant Selection Section -->
                <div class="variant-selection-section">
                    <h4>Select Variants</h4>
                    <div class="variant-group-selection">
                        <select class="form-control" ng-model="selectedVariantGroup">
                            <option value="">Select Variant Group</option>
                            <option ng-repeat="group in variantGroups" value="{{group.groupName}}">
                                {{group.groupName}}
                            </option>
                        </select>
                        <button class="btn btn-primary" ng-click="addVariantGroup()">
                            <i class="fa fa-plus"></i> Add Group
                        </button>
                    </div>

                    <!-- Selected Variant Groups -->
                    <div class="selected-variant-groups" ng-if="variantGroupSelections.length > 0">
                        <div class="variant-group-item" ng-repeat="(groupIndex, group) in variantGroupSelections">
                            <div class="group-header">
                                <h5>{{group.group}}</h5>
                                <button class="btn btn-danger btn-sm" ng-click="removeVariantGroup(groupIndex)">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                            <div class="variants-list">
                                <div class="variant-item" ng-repeat="(variantIndex, variant) in group.variants">
                                    <span>{{variant}}</span>
                                    <button class="btn btn-danger btn-sm" ng-click="removeVariantFromGroup(groupIndex, variantIndex)">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="add-variant">
                                <select class="form-control" ng-model="group.selectedVariant">
                                    <option value="">Select Variant</option>
                                    <option ng-repeat="item in group.items" value="{{item.itemName}}">
                                        {{item.itemName}}
                                    </option>
                                </select>
                                <button class="btn btn-success btn-sm" ng-click="addVariantToGroup(groupIndex, group.selectedVariant)">
                                    <i class="fa fa-plus"></i> Add
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Variants Preview -->
                    <div class="selected-variants-preview" ng-if="selectedVariantsPreview">
                        <i class="fa fa-info-circle"></i>
                        <span>Selected variants will be appended to filename: {{selectedVariantsPreview}}</span>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div class="upload-area-modal" ng-click="openFileInput()">
                    <input type="file" id="file-input" lcd-file-model="selectedFiles" multiple accept="image/*,video/mp4" style="display: none;">
                    <i class="fa fa-cloud-upload"></i>
                    <p>Click to select images or videos for upload</p>
                </div>

                <div class="selected-files" ng-if="selectedFiles.length > 0">
                    <div ng-repeat="file in selectedFiles" class="selected-file">
                        <i class="fa" ng-class="file.type.startsWith('image/') ? 'fa-file-image-o' : 'fa-file-video-o'"></i>
                        <span>{{file.name}}</span>
                        <button class="remove-file" ng-click="removeSelectedFile($index)">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>

                <div class="text-center" style="margin-top: 20px;">
                    <button class="btn btn-success" ng-click="uploadImages()" ng-disabled="!selectedFiles.length">
                        <i class="fa fa-upload"></i> Upload Images
                    </button>
                </div>
            </div>
        </div>
          
        <!-- Search Results Modal -->
        <div class="search-results-modal" ng-class="{'show': showSearchResults}" ng-if="uploadedImages && uploadedImages.length > 0">
            <div class="modal-header">
                <div class="modal-drag-handle" ng-click="toggleSearchResults()"></div>
                <div class="header-content">
                    <h3>Search Results</h3>
                    <button class="close-btn" ng-click="closeSearchResults()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="view-toggle">
                    <button class="view-btn" ng-class="{'active': viewMode === 'grid'}" ng-click="viewMode = 'grid'">
                        <i class="fa fa-th-large"></i>
                    </button>
                    <button class="view-btn" ng-class="{'active': viewMode === 'list'}" ng-click="viewMode = 'list'">
                        <i class="fa fa-list"></i>
                    </button>
                </div>
            </div>
            <div class="container">
                <!-- Grid View -->
                <div class="image-grid" ng-if="viewMode === 'grid'">
                    <div class="image-item" ng-repeat="image in uploadedImages">
                        <div class="image-preview">
                            <img ng-src="{{image.url}}" alt="{{image.name}}" class="preview-image">
                            <div class="image-overlay">
<!--                                <button class="action-btn edit-btn" ng-click="editImage(image)" title="Edit">-->
<!--                                    <i class="fa fa-pencil"></i>-->
<!--                                </button>-->
                                <button class="action-btn delete-btn" ng-click="removeImage(image.id)" title="Delete">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="image-info">
                            <span class="image-name">{{image.name}}</span>
                            <span class="image-path">{{image.path}}</span>
                        </div>
                    </div>
                </div>
            
                <!-- List View -->
                <div class="image-list" ng-if="viewMode === 'list'">
                    <div class="list-header">
                        <div class="col-name">Name</div>
                        <div class="col-path">Path</div>
                        <div class="col-actions">Actions</div>
                    </div>
                    <div class="list-item" ng-repeat="image in uploadedImages">
                        <div class="col-name">
                            <img ng-src="{{image.url}}" alt="{{image.name}}" class="list-thumbnail">
                            <span>{{image.name}}</span>
                        </div>
                        <div class="col-path">{{image.path}}</div>
                        <div class="col-actions">
<!--                            <button class="list-action-btn" ng-click="editImage(image)" title="Edit">-->
<!--                                <i class="fa fa-pencil"></i>-->
<!--                            </button>-->
                            <button class="list-action-btn delete-btn" ng-click="removeImage(image.id)" title="Delete">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Mode Actions -->
        <div class="edit-mode-actions" ng-if="isEditMode">
            <button class="btn btn-danger" ng-click="cancelEdit()">
                <i class="fa fa-times"></i> Cancel Edit
            </button>
        </div>

        <!-- Variant Selection Modal -->
        <div class="modal" ng-show="showVariantGroupModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Select Variant Group</h3>
                    <button class="close-btn" ng-click="closeVariantGroupModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="variantGroupSelect">Variant Group:</label>
                        <select id="variantGroupSelect" ng-model="selectedVariantGroup" class="form-control">
                            <option value="">Select a variant group</option>
                            <option ng-repeat="group in variantGroups" value="{{group.groupName}}">
                                {{group.groupName}}
                            </option>
                        </select>
                    </div>
                    
                    <div class="modal-footer">
                        <button class="btn btn-secondary" ng-click="closeVariantGroupModal()">Cancel</button>
                        <button class="btn btn-primary" ng-click="addVariantGroup()" ng-disabled="!selectedVariantGroup">
                            Add Group
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Selected Variant Groups -->
        <div class="selected-variant-groups" ng-if="variantGroupSelections.length > 0">
            <div class="variant-group-item" ng-repeat="(groupIndex, group) in variantGroupSelections">
                <div class="group-header">
                    <h5>{{group.group}}</h5>
                    <button class="btn btn-danger btn-sm" ng-click="removeVariantGroup(groupIndex)">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="variants-list">
                    <div class="variant-item" ng-repeat="(variantIndex, variant) in group.variants">
                        <span>{{variant}}</span>
                        <button class="btn btn-danger btn-sm" ng-click="removeVariantFromGroup(groupIndex, variantIndex)">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="add-variant">
                    <select class="form-control" ng-model="group.selectedVariant">
                        <option value="">Select Variant</option>
                        <option ng-repeat="item in group.items" value="{{item.itemName}}">
                            {{item.itemName}}
                        </option>
                    </select>
                    <button class="btn btn-success btn-sm" ng-click="addVariantToGroup(groupIndex, group.selectedVariant)">
                        <i class="fa fa-plus"></i> Add
                    </button>
                </div>
            </div>
        </div>

        <!-- Add Floating Buttons -->
        <div class="floating-buttons-container">
            <div class="action-buttons">
                <button class="action-button" ng-click="searchImages()">
                    <i class="fa fa-search"></i>
                </button>
                <button class="action-button" ng-click="downloadExcel()" ng-if="uploadedImages.length > 0">
                    <i class="fa fa-file-excel-o"></i>
                </button>
            </div>
        </div>
        <button class="floating-upload-btn" ng-click="openUploadModal()" ng-if="currentStep !== 'version' && formData.version">
            <i class="fa fa-upload"></i>
        </button>
       
      
      
        <!-- New Folder Modal -->
        <div class="new-folder-modal" ng-class="{'show': showNewFolderModal}">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Create New {{currentStep | capitalize}}</h3>
                    <button class="close-btn" ng-click="closeNewFolderModal()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>{{currentStep | capitalize}} Name</label>
                        <input type="text" class="form-control" ng-model="newFolderName" 
                               placeholder="Enter {{currentStep | capitalize}} name"
                               ng-keyup="$event.keyCode === 13 && createNewFolder()">
                    </div>
                    <div class="text-center">
                        <button class="btn btn-default" ng-click="closeNewFolderModal()">
                            Cancel
                        </button>
                        <button class="btn btn-primary" ng-click="createNewFolder()" 
                                ng-disabled="!newFolderName">
                            <i class="fa fa-plus"></i> Create
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Mode Overlay -->
        <div class="edit-mode-overlay" ng-if="isEditMode">
            <div class="edit-mode-content">
                <div class="modal-header">
                    <h3>Edit Image</h3>
                    <button class="close-btn" ng-click="cancelEdit()">&times;</button>
                </div>
                
                <div class="current-path-display" ng-if="currentPath">
                    <i class="fa fa-folder-open"></i>
                    <span>Current Path: {{currentPath}}</span>
                </div>

                <!-- Variant Selection Section -->
                <div class="variant-selection-section">
                    <h4>Select Variants</h4>
                    <div class="variant-group-selection">
                        <select class="form-control" ng-model="selectedVariantGroup">
                            <option value="">Select Variant Group</option>
                            <option ng-repeat="group in variantGroups" value="{{group.groupName}}">
                                {{group.groupName}}
                            </option>
                        </select>
                        <button class="btn btn-primary" ng-click="addVariantGroup()">
                            <i class="fa fa-plus"></i> Add Group
                        </button>
                    </div>

                    <!-- Selected Variant Groups -->
                    <div class="selected-variant-groups" ng-if="variantGroupSelections.length > 0">
                        <div class="variant-group-item" ng-repeat="(groupIndex, group) in variantGroupSelections">
                            <div class="group-header">
                                <h5>{{group.group}}</h5>
                                <button class="btn btn-danger btn-sm" ng-click="removeVariantGroup(groupIndex)">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                            <div class="variants-list">
                                <div class="variant-item" ng-repeat="(variantIndex, variant) in group.variants">
                                    <span>{{variant}}</span>
                                    <button class="btn btn-danger btn-sm" ng-click="removeVariantFromGroup(groupIndex, variantIndex)">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="add-variant">
                                <select class="form-control" ng-model="group.selectedVariant">
                                    <option value="">Select Variant</option>
                                    <option ng-repeat="item in group.items" value="{{item.itemName}}">
                                        {{item.itemName}}
                                    </option>
                                </select>
                                <button class="btn btn-success btn-sm" ng-click="addVariantToGroup(groupIndex, group.selectedVariant)">
                                    <i class="fa fa-plus"></i> Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div class="upload-area-modal" ng-click="openFileInput()">
                    <input type="file" id="file-input" lcd-file-model="selectedFiles" multiple accept="image/*,video/mp4" style="display: none;">
                    <i class="fa fa-cloud-upload"></i>
                    <p>Click to select new image or video</p>
                </div>

                <div class="selected-files" ng-if="selectedFiles.length > 0">
                    <div ng-repeat="file in selectedFiles" class="selected-file">
                        <i class="fa" ng-class="file.type.startsWith('image/') ? 'fa-file-image-o' : 'fa-file-video-o'"></i>
                        <span>{{file.name}}</span>
                        <button class="remove-file" ng-click="removeSelectedFile($index)">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>

                <div class="text-center" style="margin-top: 20px;">
                    <button class="btn btn-success" ng-click="updateImage()" ng-disabled="!selectedFiles.length">
                        <i class="fa fa-save"></i> Update Image
                    </button>
                    <button class="btn btn-default" ng-click="cancelEdit()" style="margin-left: 10px;">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                </div>
            </div>
        </div>

        <!-- Add clear edit mode button -->
        <button class="clear-edit-btn" ng-if="isEditMode" ng-click="cancelEdit()" title="Clear Edit Mode">
            <i class="fa fa-times"></i>
        </button>

        <!-- New Step Modal -->
        <div class="modal" ng-show="showNewStepModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add New Step</h3>
                    <button class="close" ng-click="closeNewStepModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newStepName">Step Name</label>
                        <input type="text" id="newStepName" ng-model="newStepName" class="form-control" placeholder="Enter step name">
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" ng-click="closeNewStepModal()">Cancel</button>
                    <button class="btn btn-primary" ng-click="createNewStep()" ng-disabled="!newStepName">Create</button>
                </div>
            </div>
        </div>

        <!-- Preview Modal -->
        <div class="modal" ng-show="showPreviewModal">
            <div class="modal-content preview-modal-content">
                <div class="modal-header">
                    <h3>{{selectedFile.name}}</h3>
                    <button class="close-btn" ng-click="closePreviewModal()">&times;</button>
                </div>
                <div class="preview-body">
                    <div class="media-preview-container">
                        <img ng-if="selectedFile.type.startsWith('image/')" ng-src="{{selectedFile.url}}" alt="{{selectedFile.name}}" class="preview-media">
                        <video ng-if="selectedFile.type === 'video/mp4'" class="preview-media" controls>
                            <source ng-src="{{selectedFile.url}}" type="video/mp4">
                        </video>
                        <i ng-if="!selectedFile.type.startsWith('image/') && selectedFile.type !== 'video/mp4'" class="fa fa-file preview-icon"></i>
                    </div>
                    <div class="file-details">
                        <div class="detail-item">
                            <span class="detail-label">Name:</span>
                            <span class="detail-value">{{selectedFile.name}}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Size:</span>
                            <span class="detail-value">{{selectedFile.size | bytes}}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">{{selectedFile.type}}</span>
                        </div>
                        <div class="detail-item" ng-if="selectedFile.uploadTime">
                            <span class="detail-label">Uploaded:</span>
                            <span class="detail-value">{{selectedFile.uploadTime | date:'medium'}}</span>
                        </div>
                        <div class="detail-item" ng-if="!selectedFile.uploadTime && selectedFile.modified">
                            <span class="detail-label">Modified:</span>
                            <span class="detail-value">{{selectedFile.modified | date:'medium'}}</span>
                        </div>
                    </div>
                </div>
                <div class="preview-actions">
                    <button class="btn btn-primary" ng-click="editFile(selectedFile)">
                        <i class="fa fa-pencil"></i> Edit
                    </button>
                    <button class="btn btn-danger" ng-click="deleteFile(selectedFile)">
                        <i class="fa fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>

        <!-- Variant Management Modal -->
        <div class="modal" ng-show="showVariantManagementModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Manage Variant Groups</h2>
                    <button class="close-btn" ng-click="closeVariantManagementModal()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="variant-management">
                        <!-- Groups Section -->
                        <div class="groups-section">
                            <div class="section-header">
                                <h3>Variant Groups</h3>
                                <button class="add-btn" ng-click="openNewGroupModal()">
                                    <i class="fa fa-plus"></i> Add Group
                                </button>
                            </div>
                            <div class="groups-list">
                                <div class="group-item" ng-repeat="group in variantGroups" 
                                     ng-class="{'selected': selectedGroup && selectedGroup.id === group.id}"
                                     ng-click="selectGroup(group)">
                                    <div class="group-info">
                                        <span class="group-name">{{group.groupName}}</span>
                                        <span class="group-status" ng-class="{'active': group.status === 'ACTIVE'}">
                                            {{group.status}}
                                        </span>
                                    </div>
                                    <div class="group-actions">
                                        <button class="edit-btn" ng-click="editGroup(group)">
                                            <i class="fa fa-pencil"></i>
                                        </button>
                                        <button class="delete-btn" ng-click="deleteGroup(group)">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Items Section -->
                        <div class="items-section" ng-if="selectedGroup">
                            <div class="section-header">
                                <h3>Items in {{selectedGroup.groupName}}</h3>
                                <button class="add-btn" ng-click="openNewItemModal()">
                                    <i class="fa fa-plus"></i> Add Item
                                </button>
                            </div>
                            <div class="items-list">
                                <div class="item-item" ng-repeat="item in variantItems">
                                    <div class="item-info">
                                        <span class="item-name">{{item.itemName}}</span>
                                        <span class="item-status" ng-class="{'active': item.status === 'ACTIVE'}">
                                            {{item.status}}
                                        </span>
                                    </div>
                                    <div class="item-actions">
                                        <button class="edit-btn" ng-click="editItem(item)">
                                            <i class="fa fa-pencil"></i>
                                        </button>
                                        <button class="delete-btn" ng-click="deleteItem(item)">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Group Modal -->
        <div class="modal" ng-show="showGroupModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>{{editingGroup ? 'Edit' : 'New'}} Variant Group</h2>
                    <button class="close-btn" ng-click="closeGroupModal()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form ng-submit="saveGroup()">
                        <div class="form-group">
                            <label>Group Name</label>
                            <input type="text" ng-model="newGroup.groupName" required>
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select ng-model="newGroup.status">
                                <option value="ACTIVE">Active</option>
                                <option value="INACTIVE">Inactive</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="cancel-btn" ng-click="closeGroupModal()">Cancel</button>
                            <button type="submit" class="save-btn">Save</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- New Item Modal -->
        <div class="modal" ng-show="showItemModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>{{editingItem ? 'Edit' : 'New'}} Variant Item</h2>
                    <button class="close-btn" ng-click="closeItemModal()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form ng-submit="saveItem()">
                        <div class="form-group">
                            <label>Item Name</label>
                            <input type="text" ng-model="newItem.itemName" required>
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select ng-model="newItem.status">
                                <option value="ACTIVE">Active</option>
                                <option value="INACTIVE">Inactive</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="cancel-btn" ng-click="closeItemModal()">Cancel</button>
                            <button type="submit" class="save-btn">Save</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        </div>
      
      </body>
</html>