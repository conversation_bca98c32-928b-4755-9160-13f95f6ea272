<!--Custom toast-->
<div class="toast-container" data-ng-if="toasts.length > 0">
    <div class="toast" data-ng-repeat="(index, toast) in toasts" data-ng-class="'toast-' + toast.type">
        <span class="toast-message">{{ toast.message }}</span>
        <button class="toast-close-btn" data-ng-click="toasts.splice(index, 1)">✖</button>
    </div>
</div>

<style>
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .toast {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.5s ease forwards;
        max-width: 350px;
        min-width: 250px;
        opacity: 0.95;
        position: relative;
        overflow: hidden;
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    .toast.fade-out {
        opacity: 0;
        transform: translateX(50%);
    }

    .toast-message {
        flex: 1;
    }

    .toast-close-btn {
        background: transparent;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        margin-left: 10px;
        transition: transform 0.3s;
    }

    .toast-close-btn:hover {
        transform: scale(1.2);
    }

    .toast-create {
        background: linear-gradient(135deg, #2196F3, #6EC6FF);
    }

    .toast-success {
        background: linear-gradient(135deg, #4CAF50, #8BC34A);
    }

    .toast-error {
        background: linear-gradient(135deg, #F44336, #E57373);
    }

    .toast-warning {
        background: linear-gradient(135deg, #FF9800, #FFB74D);
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }

        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes fadeOut {
        to {
            opacity: 0;
            transform: translateX(50%);
        }
    }

    @media (max-width: 480px) {
        .toast-container {
            right: 10px;
            left: 10px;
        }

        .toast {
            max-width: 100%;
            padding: 12px 16px;
        }
    }
</style>