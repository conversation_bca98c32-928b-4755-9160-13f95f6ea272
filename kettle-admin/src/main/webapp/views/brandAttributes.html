<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header">
            Brand Attribute Mapping
        </h1>
    </div>
    <div class="form-group">
        <label>Brand *</label>
        <select class="form-control" ng-model="selectedBrand" data-ng-change="getSelectedBrandId(selectedBrand)"
            data-ng-options="brand as brand for brand in brands">
        </select>
    </div>
    <br /><br />
    <div data-ng-if="selectedBrand !== null">
        <button class="btn btn-primary pull-right" style="margin:5px"
            data-ng-click="editBrandMappingModalOpen()">Edit</button>
        <!--        <button class="btn btn-primary pull-right" style="margin:5px" >Add Attribute</button>-->
    </div><br />
    <div data-ng-repeat="data in brandList">
        <table data-ng-if="selectedBrand == data.brandName " cellpadding="0" border="2" cellspacing="0"
            class="table table-hover table-striped">
            <tr id="heading" style="background-color:#bcf7cc">
                <th style="font-size:18px" >ATTRIBUTE NAME</th>
                <th style="font-size:18px" >ATTRIBUTE VALUE</th>
            </tr>
            <tr data-ng-repeat="mapData in brandAttributeMap[data.brandId]">
                <td>{{mapData.attribute}}</td>
                <td>{{mapData.value}}</td>
            </tr>
        </table>
    </div>


</div>


<!--Edit form-->
<form name="myForms" novalidate>
    <div class="modal fade" id="editBrandMappingModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Edit Brand Attribute Mapping</h4>
                </div>
                <div class="modal-body">
                    <form name="editAttributeForm" novalidate>
                        <div class="form-group" data-ng-repeat="data in brandAttributeMap[selectedBrandId]">
                            <label> {{data.attribute}}</label>
                            <input type="text" class="form-control" ng-model="data.value" />
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary pull-right"
                        data-ng-click="updateBrandAttributeMapping()">Update</button>
                </div>
            </div>
        </div>
    </div>
</form>