<div class="row" data-ng-init="init()">
	<div class="col-lg-12">
		<h1 class="page-header">Manual Bill Report</h1>
	</div>
</div>

<div class="row">
	<div class="col-xs-8">
    	<select class="form-control" ng-model="selectedCafe" ng-options="cafe as cafe.name for cafe in cafelist | orderBy : 'name'" data-ng-change="getManualBillBooks()"></select>
    </div>
	
	<div class="row">
	    <div class="col-xs-4"><button class="btn btn-primary pull-left" ng-click="getManualBillBooks()">Submit</button></div>
    </div> 
     <div class="row" data-ng-if="selectedCafe != null">
   	 	<h4 align="center">{{selectedCafe.name}} Bill Books</h4>
    </div> 
     <div class="row" data-ng-if="msgError != null">
   	 	<h4 align="center">{{msgError}}</h4>
    </div> 
    <br></br>
    <div class="rTable">
    <div class="rTableRow">
   	 	<div class="rTableHead"><p>Book Id</p></div>
   	 	<div class="rTableHead"><p>Start No.</p></div>
   	 	<div class="rTableHead"><p>End No.</p></div>
   	 	<div class="rTableHead"><p>Bill Used</p></div>
   	 	<div class="rTableHead"><p>Book Status</p></div>
   	 	<div class="rTableHead"><p>Activation Time</p></div>
   	 	<div class="rTableHead"><p>View Orders</p></div>
   	 	<div class="rTableHead"><p>Download Orders</p></div>
    </div>    
   	<div class="rTableRow" data-ng-repeat="book in allManualBillBooks track by book.id">
   	 	<div class="rTableCell"><p>{{book.id}}</p></div>
   	 	<div class="rTableCell"><p>{{book.startNo}}</p></div>
   	 	<div class="rTableCell"><p>{{book.endNo}}</p></div>
   	 	<div class="rTableCell"><p>{{book.usedBillCount}}</p></div>
   	 	<div class="rTableCell"><p>{{book.status}}</p></div>
   	 	<div class="rTableCell"><p>{{book.activationTime | date:'yyyy-MM-dd HH:mm:ss'}}</p></div>
   	 	<div class="rTableCell"><p><button class="btn btn-primary pull-left" ng-click="getManualBills(book.id)" data-ng-if="book.status != 'CREATED'">Orders</button></p></div>
   	 	<div class="rTableCell"><p><button class="btn btn-primary pull-left" ng-click="downloadtManualBills(book.id)" data-ng-if="book.status != 'CREATED'">Download</button></p></div>
    </div>       
    </div>
    <br></br>
    <div class="rTable" data-ng-if="allManualBills != null && allManualBills.length > 0">
    <div class="rTableRow">
   	 	<div class="rTableHead"><p>Book No</p></div>
   	 	<div class="rTableHead"><p>Order Id</p></div>
   	 	<div class="rTableHead"><p>Total</p></div>
   	 	<div class="rTableHead"><p>Taxable</p></div>
   	 	<div class="rTableHead"><p>Taxes</p></div>
   	 	<div class="rTableHead"><p>Paid</p></div>
   	 	<div class="rTableHead"><p>Settlement</p></div>
   	 	<div class="rTableHead"><p>Status</p></div>
   	 	<div class="rTableHead"><p>Order Time</p></div>
   	 	<div class="rTableHead"><p>Employee</p></div>
    </div>    
   	<div class="rTableRow" data-ng-repeat="book in allManualBills track by book.id">
   	 	<div class="rTableCell"><p>{{book.bookNo}}</p></div>
   	 	<div class="rTableCell"><p>{{book.id}}</p></div>
   	 	<div class="rTableCell"><p>{{book.total}}</p></div>
   	 	<div class="rTableCell"><p>{{book.taxable}}</p></div>
   	 	<div class="rTableCell"><p>{{book.taxes}}</p></div>
   	 	<div class="rTableCell"><p>{{book.paid}}</p></div>
   	 	<div class="rTableCell"><p>{{book.settlement}}</p></div>
   	 	<div class="rTableCell"><p>{{book.status}}</p></div>
   	 	<div class="rTableCell"><p>{{book.time | date:'yyyy-MM-dd HH:mm:ss'}}</p></div>
   	 	<div class="rTableCell"><p>{{book.employee}}</p></div>
    </div>       
    </div>
</div>