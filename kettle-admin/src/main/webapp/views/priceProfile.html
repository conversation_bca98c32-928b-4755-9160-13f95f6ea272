<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .row .col {
        margin: 5px 10px;
    }

    .popup {
        visibility: hidden;
        min-width: 250px;
        margin-left: -125px;
        background-color: #A91B0D;
        color: #fff;
        text-align: center;
        border-radius: 2px;
        padding: 16px;
        position: fixed;
        z-index: 1051;
        right: 20px;
        bottom: 30px;
        font-size: 17px;
    }

    .popup.show {
        visibility: visible;
        -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
        animation: fadein 0.5s, fadeout 0.5s 2.5s;
    }

    @-webkit-keyframes fadein {
        from {
            bottom: 0;
            opacity: 0;
        }
        to {
            bottom: 30px;
            opacity: 1;
        }
    }

    @keyframes fadein {
        from {
            bottom: 0;
            opacity: 0;
        }
        to {
            bottom: 30px;
            opacity: 1;
        }
    }

    @-webkit-keyframes fadeout {
        from {
            bottom: 30px;
            opacity: 1;
        }
        to {
            bottom: 0;
            opacity: 0;
        }
    }

    @keyframes fadeout {
        from {
            bottom: 30px;
            opacity: 1;
        }
        to {
            bottom: 0;
            opacity: 0;
        }
    }
</style>

<div class="row" ng-init="init()" style="padding:10px">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Price Profile

            <button class="btn btn-primary pull-right" ng-if="!showAddForm"
                    id="myBtn3" ng-click="addPriceProfileNew()">
                <i class="fa fa-plus fw"></i> Add new price profile
            </button>
            <button class="btn btn-primary pull-right" ng-if="showAddForm"
                    id="myBtn4" ng-click="addPriceProfileNew()">
                <i class="fa fa-search fw"></i> Find price profile
            </button>
        </h1>
    </div>
    <div class="form-group" ng-show="showAddForm">
        <div class="row" style="padding:10px">
            <div class="col">
                <label>Enter Profile Description</label>
                <input type="text" class="form-control" data-ng-model="description"/>
            </div>
        </div>
        <div class="row" style="padding:10px">
            <div class="col">
                <label>Select Profile Type</label>
                <select class="form-control" data-ng-model="profileType"
                        data-ng-options="profileType as profileType for profileType in profileTypes"></select>
            </div>
        </div>
        <div class="row" style="padding:10px">
            <div class="col">
                <label>Select Profile Status</label>
                <select class="form-control" data-ng-model="activeprofileStatus"
                        data-ng-options="activeprofileStatus as activeprofileStatus for activeprofileStatus in profileStatus"></select>
            </div>
        </div>
        <div class="row" style="padding:10px">
            <div class="col">
                <label>Enter Threshold</label>
                <input type="number" ng-change="checkThreshold()" class="form-control" data-ng-model="threshold"/>
            </div>
        </div>

        <div class="col-xs-12"
             ng-if="profileRangeValueDetails.length > 0">
            <div class="row">
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>S. No</th>
                        <th>Start Price</th>
                        <th>End Price</th>
                        <th>Delta Price</th>
                        <th>Range Value Status</th>
                        <th>Actions</th>
                        </thead>
                        <tbody>
                        <tr
                                ng-repeat="prValue in profileRangeValueDetails">
                            <td>{{$index + 1}}</td>
                            <td>{{prValue.startPrice}}</td>
                            <td>{{prValue.endPrice}}</td>
                            <td>{{prValue.deltaPrice}}</td>
                            <td>{{prValue.rangeValuesStatus}}</td>
                            <td><input type="button" class="btn btn-danger" ng-if="$index==profileRangeValueDetails.length-1"
                                       data-ng-click="deleteRangeValue($index)" value="Delete"/></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 text-left">
                <input type="button" class="btn btn-primary"
                       data-ng-click="showPriceRangeModal()" value="Add New Price Range"/>
            </div>
            <div class="col-xs-12 text-right">
                <input type="button" class="btn btn-primary"
                       data-ng-click="addPriceProfile()" value="Submit"/>
            </div>
        </div>
    </div>

    <div ng-show="!showAddForm">
        <div class="row" style="padding:10px">
            <div class="col">
                <label>Select Profile Type</label>
                <select class="form-control" data-ng-model="searchprofileType"
                        data-ng-options="searchprofileType as searchprofileType for searchprofileType in profileTypes"></select>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 text-left">
                <input type="button" class="btn btn-primary" data-ng-click="getPriceProfileData()"
                       value="Fetch Data"/>
            </div>
        </div>
    </div>

    <div class="col-xs-12"
         ng-if="priceProfileData.length > 0" ng-show="!showAddForm">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-striped table-bordered">
                    <thead>
                    <th>S. No</th>
                    <th>Description</th>
                    <th>Profile Type</th>
                    <th>Profile Status</th>
                    <th>Threshold</th>
                    <th>Last Updated By</th>
                    <th>Profile Creation Time</th>
                    <th>Last Update time</th>
                    <th>Profile Range Values</th>
                    <th>Actions</th>
                    </thead>
                    <tbody>
                    <tr
                            ng-repeat="prValue in priceProfileData">
                        <td>{{$index + 1}}</td>
                        <td>{{prValue.profileDescription}}</td>
                        <td>{{prValue.profileType}}</td>
                        <td>{{prValue.profileStatus}}</td>
                        <td>{{prValue.thresholdPercentage}}</td>
                        <td>{{prValue.lastUpdatedBy}}</td>
                        <td>{{prValue.profileCreationTime | date:'short'}}</td>
                        <td>{{prValue.lastUpdateTime | date:'short'}}</td>
                        <td>
                            <button class="btn btn-secondary"
                                    data-ng-click="showPriceRangeDetails(prValue.profileRangeValueDetails)">Show details
                            </button>
                        </td>
                        <td><input type="button" class="btn btn-primary"
                                   data-ng-click="showEditPriceProfileModal(prValue)"
                                   value="Edit"/></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="popup" id="snackbar">{{errorMessage}}</div>
    <div class="popup" id="toast" style="background-color: black">{{generalMessage}}</div>

    <div class="modal fade" id="priceRangeModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="myModalLabel">Add new price range</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <label>Enter Start Price</label>
                            <input type="number" min="0" class="form-control" data-ng-model="currentStartPrice"  disabled="true" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <label>Enter End Price</label>
                            <input type="number" ng-show="fixedValues" min="0" class="form-control" data-ng-model="currentEndPrice" disabled="true" />
                            <input type="number" ng-show="!fixedValues" class="form-control" data-ng-model="currentEndPrice"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <label>Enter Delta Price</label>
                            <input type="number" class="form-control" data-ng-model="currentDeltaPrice"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <label>Select Range Value Status</label>
                            <select class="form-control" data-ng-model="activerangeValueStatus"
                                    data-ng-options="activerangeValueStatus as activerangeValueStatus for activerangeValueStatus in rangeValueStatus"></select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 text-right">
                            <input type="button" class="btn btn-primary"
                                   data-ng-click="addNewRangeValue()" value="Submit"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="editpriceProfileModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="mypriceModalLabel">Edit Price Profile</h4>
                </div>
                <div class="modal-body" >
                    <div class="row">
                        <div class="col">
                            <label>Enter new Description</label>
                            <input type="text" min="0" class="form-control" data-ng-model="newDescription"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <label>Enter new threshold</label>
                            <input type="number" min="" ng-change="checkNewThreshold()" class="form-control" data-ng-model="newThreshold"/>
                        </div>
                    </div>
                    <div class="row">
                    <div class="col">
                        <label>Select new profile status</label>
                        <select class="form-control" data-ng-model="newProfileStatus"
                                data-ng-options="newProfileStatus as newProfileStatus for newProfileStatus in profileStatus"></select>
                    </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 text-right">
                            <input type="button" class="btn btn-primary"
                                   data-ng-click="updatePriceProfileData()" value="Submit"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="PriceRangeDetailsModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="mypriceDetailLabel">Price Range Details</h4>
                </div>
                <div class="modal-body" style="overflow-y: auto">
                    <div class="col-xs-12"
                         ng-if="priceRangeDetailsData">
                        <div class="row">
                            <div class="col-xs-12">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <th>S. No</th>
                                    <th>Start Price</th>
                                    <th>End Price</th>
                                    <th>Delta Price</th>
                                    <th>Range Value Status</th>
                                    </thead>
                                    <tbody>
                                    <tr
                                            ng-repeat="prValue in priceRangeDetailsData">
                                        <td>{{$index + 1}}</td>
                                        <td>{{prValue.startPrice}}</td>
                                        <td>{{prValue.endPrice}}</td>
                                        <td>{{prValue.deltaPrice}}</td>
                                        <td>{{prValue.rangeValuesStatus}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>