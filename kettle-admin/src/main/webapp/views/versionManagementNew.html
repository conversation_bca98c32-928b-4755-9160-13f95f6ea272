<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Tea house Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Tea house Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Tea house Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<link rel="stylesheet" href="css/version_management_styles.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<div class="app-container" data-ng-init="init()">
    <!-- Header Section -->
    <header class="header">
        <div class="logo">
            <i class="fa fa-code-branch"></i>
            <h1>Unit Version Management</h1>
        </div>
    </header>

    <!-- Filters Section -->
    <section class="filters-section animate__animated animate__fadeIn">
        <div class="filters-header">
            <h2><i class="fa fa-filter"></i> Filter Options</h2>
            <div class="actions">
                <button class="bttn btn-pri" data-ng-click="getUnitDetails()">
                    <i class="fa fa-search"></i> Get Units
                </button>
            </div>
        </div>
        <div class="filters-container">
            <div class="filter-row">
                <div class="filter-group">
                    <label>Regions</label>
                    <div class="multi-select" ng-dropdown-multiselect=""
                         options="regionList"
                         selected-model="selectedRegionList" extra-settings="multiSelectSettings">
                    </div>
                </div>

                <div class="filter-group">
                    <label>Unit Type</label>
                    <div class="multi-select" ng-dropdown-multiselect=""
                         options="unitCategoryList"
                         selected-model="selectedUnitCategory" extra-settings="multiSelectSettings">
                    </div>
                </div>

                <div class="filter-group">
                    <label>Application Name</label>
                    <div class="multi-select" ng-dropdown-multiselect=""
                         options="appNameList"
                         selected-model="selectedAppNameList" extra-settings="multiSelectSettings">
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- App Selection Bar -->
    <section class="app-selection-bar animate__animated animate__fadeIn">
        <div class="app-selection-item">
            <label>POS</label>
            <div style="flex: 1;">
                <select class="form-control"
                        data-ng-model="selectedAllPosVersion" data-ng-change="findCompatibleCrmVersions('POS',selectedAllPosVersion)">
                    <option value=""></option>
                    <option data-ng-repeat="list in allActiveVersionsAvailable['POS'] "
                            value="{{list}}" >
                        {{list}}
                    </option>
                </select>
            </div>
        </div>

        <div class="app-selection-item">
            <label>CAFE APP</label>
            <div style="flex: 1;">
                <select class="form-control"
                        data-ng-model="selectedAllKettleCrmVersion" data-ng-change="findCompatibleCrmVersions('CAFE_APP',selectedAllKettleCrmVersion)">
                    <option value=""></option>
                    <option data-ng-repeat="list in allActiveVersionsAvailable['CAFE_APP'] | orderBy"
                            value="{{list}}">
                        {{list}}
                    </option>
                </select>
            </div>
        </div>

        <div class="app-selection-item">
            <label>TABLE SERVICE</label>
            <div style="flex: 1;">
                <select class="form-control large-dropdown"
                        data-ng-model="selectedAllTableServiceVersion" data-ng-change="findCompatibleCrmVersions('TABLE_SERVICE',selectedAllTableServiceVersion)">
                    <option value=""></option>
                    <option data-ng-repeat="list in allActiveVersionsAvailable['TABLE_SERVICE'] | orderBy"
                            value="{{list}}">
                        {{list}}
                    </option>
                </select>
            </div>
        </div>

        <div class="app-selection-item">
            <label>MONK APP</label>
            <div style="flex: 1;">
                <select class="form-control large-dropdown"
                        data-ng-model="selectedAllMonkAppVersion" data-ng-change="findCompatibleCrmVersions('MONK_APP',selectedAllMonkAppVersion)">
                    <option value=""></option>
                    <option data-ng-repeat="list in allActiveVersionsAvailable['MONK_APP'] | orderBy"
                            value="{{list}}">
                        {{list}}
                    </option>
                </select>
            </div>
        </div>
        <div class="app-selection-item">
            <label>KETTLE ATTENDANCE</label>
            <div style="flex: 1;">
                <select class="form-control large-dropdown"
                        data-ng-model="selectedAllKettleAttendanceAppVersion" data-ng-change="findCompatibleCrmVersions('KETTLE_ATTENDANCE',selectedAllKettleAttendanceAppVersion)">
                    <option value=""></option>
                    <option data-ng-repeat="list in allActiveVersionsAvailable['KETTLE_ATTENDANCE'] | orderBy"
                            value="{{list}}">
                        {{list}}
                    </option>
                </select>
            </div>
        </div>
    </section>

    <!-- Results Section -->
    <section class="results-section animate__animated animate__fadeIn" data-ng-if="unitDetailList != null && unitDetailList.length > 0">
        <div class="results-header">
            <h2><i class="fa fa-list"></i> Unit Version List</h2>
            <div class="units-stats">
                <div class="units-count">
                    <i class="fa fa-database"></i> Total Units: {{unitDetailList.length}}
                </div>
                <div class="units-count">
                    <i class="fa fa-filter"></i> Filtered Units: {{(unitDetailList | filter: customFilter).length}}
                </div>
                <div class="units-count">
                    <i class="fa fa-check-square"></i> Selected Units: {{getSelectedCount()}}
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr class="header-row">
                        <th>
                            <div class="checkbox-header">
                                <input type="checkbox" data-ng-model='checkAll' data-ng-click="updateAll(checkAll)">
                                <span>Select All</span>
                            </div>
                        </th>
                        <th>
                            <div class="header-content">
                                <span class="column-title">Unit Name</span>
                                <div class="header-filter">
                                    <input type="text" ng-model="$parent.searchText" placeholder="🔍 Search..." />
                                </div>
                            </div>
                        </th>
                        <th>Application Version Details</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        data-ng-repeat="detail in getPaginatedUnits()"
                        data-ng-class="{'row-selected': unitDetailList[detail.id].checked}" class="animate__animated animate__fadeIn">

                        <td class="checkbox-cell">
                            <input type="checkbox" data-ng-model='detail.checked' data-ng-click="changeRow(detail.checked, detail)">
                        </td>
                        <td>{{detail.name}} ({{detail.id}})</td>
                        <td>
                            <table class="data-table">
                                <thead>
                                    <tr class="header-row">
                                        <th>App Name</th>
                                        <th>Initiated Version</th>
                                        <th>New Version</th>
                                    </tr>
                                </thead>
                                <tbody data-ng-repeat="version in unitAllActiveEventList[detail.id]" data-ng-if="toShow(version)">
                                    <tr>
                                        <td>{{version.applicationName}}</td>
                                        <td>{{version.applicationVersion}}</td>
                                        <td>
                                            <select class="form-control" data-ng-model="version.changedVersion"
                                                    data-ng-change="findCompatibleCrmVersionsForUnit(version)">
                                                <option value=""></option>
                                                <option data-ng-repeat="list in allActiveVersionsAvailable[version.applicationName] | orderBy"
                                                        value="{{list}}">{{list}}
                                                </option>
                                            </select>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <button data-ng-click="updateSelectedUnitVersion(detail)" class="bttn btn-success">
                                <i class="fa fa-sync-alt"></i> Update
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination Controls -->
        <div class="pagination-container" data-ng-if="unitDetailList != null && unitDetailList.length > 0">
            <div class="pagination-controls">
                <button class="pagination-btn" data-ng-click="setPage(currentPage - 1)" data-ng-disabled="currentPage === 1">
                    <i class="fa fa-chevron-left"></i>
                </button>
                <button class="pagination-btn"
                        data-ng-repeat="page in getPageNumbers()"
                        data-ng-click="page !== '...' && setPage(page)"
                        data-ng-class="{'active': page === currentPage, 'disabled': page === '...'}">
                    {{page}}
                </button>
                <button class="pagination-btn" data-ng-click="setPage(currentPage + 1)" data-ng-disabled="currentPage === totalPages">
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>
            <div class="pagination-info">
                <span data-ng-if="getFilteredUnits().length > 0">Page {{currentPage}} of {{totalPages}} | Showing {{(currentPage-1)*itemsPerPage + 1}}-{{Math.min(currentPage*itemsPerPage, getFilteredUnits().length)}} of {{getFilteredUnits().length}} units</span>
                <span data-ng-if="getFilteredUnits().length === 0">No units to display</span>
            </div>
            <div data-ng-if="unitDetailList != null && unitDetailList.length > 0">
                <button class="bttn btn-pri" data-ng-click="updateAllVersionsSelected()">
                    <i class="fa fa-sync-alt"></i> Update All Selected Versions
                </button>
            </div>
        </div>

    </section>

</div>