<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Web Category Tags
            <span>
                <button class="btn btn-primary pull-right" style="margin: 6px" data-toggle="modal"
                             id="addWebTagsDiv" ng-click="addWebCategory()">
                    <i class="fa fa-plus fw"></i>Add
                </button>
                <button class="btn btn-primary pull-right" style="margin: 6px" data-toggle="modal"
                        id="refreshCache" ng-click="refreshWebappCache()">
                    <i class="fa fa-plus fw"></i>Refresh Webapp Cache
                </button>
            </span>
        </h1>
    </div>
</div>
<div class="row" id="exportable">
    <div class="col-xs-12">
        <p>&nbsp;</p>
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-striped table-bordered">
                    <thead>
                    <th>S.No&nbsp;</th>
                    <th>Category&nbsp;</th>
                    <th>Name&nbsp;</th>
                    <th>Product&nbsp;</th>
                    <th>Status&nbsp;</th>
                    <th>No of Product&nbsp;</th>
                    <th>Max Product&nbsp;</th>
                    <th align="center">Action&nbsp;</th>
                    </thead>
                    <tbody>
                    <tr ng-repeat="webTagsList in webTags">
                        <td>{{$index+1}}</td>
                        <td>{{webTagsList.category}}</td>
                        <td>{{webTagsList.name}}</td>
                        <td>
                            <table>
                                <tr ng-repeat="valProduct in webTagsList.products">
                                    <td style="color: brown"><strong>{{valProduct.name}}</strong></td>
                                </tr>
                            </table>
                        <td>{{webTagsList.status}}</td>
                        <td>{{webTagsList.products.length}}</td>
                        <td>{{webTagsList.maxProducts}}</td>
                        <td>
                            <button class="btn btn-primary pull-right" style="margin: 6px" data-toggle="modal"
                                    id="orderingCategoryDiv" ng-click="managingOrderingList(webTagsList)"><i
                                    class="fa  fa-retweet"></i> Ordering
                            </button>
                            <img ng-click="editWebCategoryTags(webTagsList)" style="margin-bottom: 8px; cursor: pointer"
                                 title="Edit Tags" ng-src="img/change.png" height="20px" width="20px">
                            <img ng-if="webTagsList.status == 'IN_ACTIVE'"
                                 ng-click="activateCategoryTags(webTagsList,webTagsList.maxProducts)"
                                 style="margin-bottom: 8px; cursor: pointer" title="Activate"
                                 ng-src="img/inactiveCat.png" height="20px" width="20px">
                            <img ng-if="webTagsList.status == 'ACTIVE'"
                                 ng-click="deactivateCategoryTags(webTagsList,webTagsList.maxProducts)"
                                 style="margin-bottom: 8px; cursor: pointer" title="Deactivate"
                                 ng-src="img/activeCat.png" height="20px" width="20px"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="orderingModal" tabindex="-1" role="dialog" aria-labelledby="orderingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="resetPasswordLabel">Product Ordering</h4>
            </div>
            <div class="row">
                <div ng-repeat="(listName, list) in models.products" class="col-md-12">
                    <div class="panel panel-info">
                        <div class="panel-heading"><h3 class="panel-title">Ordering Change</h3></div>
                        <div class="panel-body">
                            <table class="table table-striped table-bordered">
                                <tr>
                                    <td>
                                        <ul dnd-list="list">
                                            <li ng-repeat="item in list" dnd-draggable="item"
                                                dnd-moved="list.splice($index, 1)" dnd-effect-allowed="move"
                                                dnd-selected="models.selected = item"
                                                ng-class="{'selected': models.selected === item}"
                                                class="ng-binding ng-scope" draggable="true">
                                                {{item.id}}-{{item.name}}
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                            </table>
                            <div>
                                <button class="btn btn-primary pull-right"
                                        ng-click="reorderTag(models,list)">Re Order
                                </button>
                                <loading style align="center"></loading>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="webCategoryModalModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Web Product</h4>
            </div>
            <div class="modal-body">
                <form name="addEmployeeForm" novalidate>
                    <div class="form-group">
                        <label>Type *</label>
                        <select class="form-control" ng-model="selectedTypeCategory"
                                ng-options="categoryList as categoryList for categoryList in selectedCategoryTypeList track by categoryList"></select>
                    </div>
                    <div class="form-group">
                        <label>Name *</label> <input type="text" class="form-control" ng-model="nameProduct"/>
                    </div>
                    <div class="form-group">
                        <label>Max Product *</label> <input type="text" class="form-control" ng-model="maxProduct"/>
                    </div>
                    <div class="form-group">
                        <label>Color</label>
                        <input type="text"  class="form-control" ng-model="selectedTagColor[nameProduct]" ng-change="validateHex(selectedTagColor[nameProduct])" >
                    </div>
                    <div ng-show="!isValidHex" style="color: red;">
                        Invalid hex code!
                    </div>
                    <div class="form-group clearfix">
                        <button class="btn btn-primary pull-right" ng-click="submitWebProduct()">Add</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="webCategoryModalModal1" tabindex="-1" role="dialog" aria-labelledby="webCategoryModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="webCategoryModalLabel">Web Category Tags</h4>
            </div>
            <form name="myForm">
                <div class="modal-body">
                    <div class="form-group">


                        <label>Type *</label>
                        <div class="col-xs-12">
                            <select class="form-control" ng-model="selectedTypeCategory"
                                    ng-options="categoryList as categoryList for categoryList in selectedCategoryTypeList track by categoryList"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Name *</label>
                        <div class="col-xs-12">
                            <input type="text" class="form-control" ng-model="name"/>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<style>
    .pickListButtons {
        padding: 10px;
        text-align: center;
    }

    .pickListButtons button {
        margin-bottom: 5px;
    }

    .pickListSelect {
        height: 200px !important;
    }
</style>
<div class="modal fade" id="editWebCategoryModal" tabindex="-1" role="dialog" aria-labelledby="editWebCategoryModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="webCategoryModalLabel">Web Category Tags</h4>
            </div>

            <div class="col-xs-12">
                <lable>Max Product</lable>
                <input type="text" class="form-control" ng-model="maxProductDetails"/>
            </div>
            <div class="col-xs-12">
                <label>Color</label>
                <input type="text"  class="form-control" ng-model="selectedColor" ng-change="validateHex(selectedColor)" >
            </div>
            <div ng-show="!isValidHex" style="color: red;">
                Invalid hex code!
            </div>
            <div class="container">
                <div class="panel panel-default col-xs-6">
                    <div id="pickListContainer" class="panel-body"></div>
                </div>
            </div>
        </div>
    </div>
</div>

