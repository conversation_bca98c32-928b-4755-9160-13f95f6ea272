<div class="row" data-ng-init="init()">
	<div class="col-lg-12"><br>
	
        <h1 class="page-header">
        	Customer 
        </h1>
    </div>
</div>

<div class="row">
   
   <div class="col-xs-4">
        <input class="form-control" type="number" placeholder="Enter customer phone number" my-maxlength="10" string-to-number ng-model="search" />
   </div>
    
    <div class="col-xs-4" >
      <img ng-click="viewCustomerDetail()" style="cursor:pointer" title="View Customer" ng-src="img/search.png" height="40px" width="40px">
   </div>
    
</div>
</br>
<div class="row">
	<div class="col-xs-12" ng-if="customerDetailsList.id">
		<table class="table table-striped table-bordered" style="font-size:11px">
			<caption>Customer Details</caption>
			<thead>
				<th>S.No</th>
				<th>Name</th>
				<th>Mobile No</th>
				<th>Email </th>
				<th>Email Verified</th>
				<th>SMS Reachable</th>
				<th>Whatsapp Reachable</th>
				<th>Block list Status</th>
				<th>Action</th>
				
			</thead>
			<tbody>
				<tr>
					<td>{{$index+1}}</td>
					<td>{{customerDetailsList.firstName}}</td>
					<td>{{customerDetailsList.contactNumber}}</td>
					<td>{{customerDetailsList.emailId}}</td>
					<td>{{customerDetailsList.contactNumberVerified}}</td>
					<td>{{customerDetailsList.smsSubscriber}}</td>
					<td>{{customerDetailsList.optWhatsapp}}</td>
					<td>{{customerDetailsList.blacklisted}}</td>
					<td>
						<div class="row">
							<div class="col-xs-3">
								<button class="btn btn-primary " style="font-size:small;" type="button" ng-if="customerDetailsList.blacklisted==false" ng-click="addBlackList(customerDetailsList.id)">Add Black List</button>
							    <button class="btn btn-primary " style="font-size:small;" type="button" ng-if="customerDetailsList.blacklisted==true" ng-click="removeBlackList(customerDetailsList.id)">Remove Black List</button>
							</div>
							<div class="col-xs-4" ng-if="customerDetailsList.blacklisted==false">
								<button class="btn btn-danger " style="font-size:small;" type="button" ng-if="customerDetailsList.smsSubscriber==false" ng-click="subscribeSMS(customerDetailsList.id)">SMS Subscribe</button>
						    	<button class="btn btn-primary " style="font-size:small;" type="button" ng-if="customerDetailsList.smsSubscriber==true" ng-click="unubscribeSMS(customerDetailsList.id)">SMS Unsubscribe</button>
							</div>
							<div class="col-xs-4" ng-if="customerDetailsList.blacklisted==false">
								<button class="btn btn-danger " style="font-size:small;" type="button" ng-if="customerDetailsList.optWhatsapp=='N'" ng-click="subscribeWhatsapp(customerDetailsList.id)">Whatsapp Subscribe</button>
						    	<button class="btn btn-primary " style="font-size:small;" type="button" ng-if="customerDetailsList.optWhatsapp=='Y'" ng-click="unubscribeWhatsapp(customerDetailsList.id)">Whatsapp Unsubscribe</button>
							</div>
						</div>
					</td>
					</tr>
			</tbody>
		</table>
	</div>
	<div class="col-lg-10" ng-if="!customerDetailsList.id">
		<h4>No results found</h4>
	</div> 
	
	 
</div>
</br>