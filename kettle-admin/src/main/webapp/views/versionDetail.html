<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">Version Details</h1>
    </div>
</div>
<button
        class="btn btn-primary pull-right"
        data-toggle="modal"
        ng-click="addNewVersion()" style="margin-bottom: 20px">
    <i class="fa fa-plus fw"></i> Add New Version
</button>
<div class="row">
    <div class="col-xs-2" style="font-size: medium; margin-top: 4px">
        <label class="control-label">Select Application</label>
    </div>
<select
        style="margin-bottom:20px; width:30%"
        class="form-control"
        ng-model="selectApplicationName"
        ng-options="fileType for fileType in applicationName"
/>
</div>
<button class="btn btn-primary " style="margin-top: 5px; margin-bottom:20px"
        data-ng-click="getallActiveVersion()">Get Details
</button>
<div class="form-group" data-ng-if="showTable">
    <table class="table table-bordered">
        <thead style="background-color: #e7e7e7">
        <th>Application Name</th>
        <th>Application version</th>
        <th>Action</th>
        <th data-ng-if="showDownloader">Update</th>
        <th data-ng-if="showDownloader">Download</th>
        </thead>
        <tbody>
        <tr ng-repeat="version in activeVersionList track by $index">
            <td>{{finalApplication}}</td>
            <td>{{version}}</td>
            <td>
                <button class="btn btn-danger" data-ng-click="updateVersion(finalApplication,version)">INACTIVE</button>
            </td>
            <td data-ng-if="showDownloader">
                <button class="btn btn-primary" data-ng-click="showCompatibilityModal(finalApplication,version)">Update</button>
            </td>
            <td data-ng-if="showDownloader">
                <button data-ng-click="downloadBuild(finalApplication,version)"><i class="fa fa-download"
                           style="font-size: 24px; margin-right: 5px;"></i></button>
            </td>
        </tr>
        </tbody>

    </table>
</div>

<div
        class="modal fade"
        id="addNewVersion"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close"
                        data-ng-click="init()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">Adding Version For {{selectApplicationName}} Application</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-6">Select Your Application</div>
                    <div class="col-xs-6">
                        <select
                                style="margin-bottom:20px; width:30%"
                                class="form-control"
                                ng-model="selectApplicationName"
                                ng-options="fileType for fileType in applicationName"
                                data-ng-change="getVersionToAdd()"
                        />
                    </div>
                    <div>
                        <div class="col-xs-6">Select Release Type</div>
                        <select
                                style="margin-bottom:20px; width:30%"
                                class="form-control"
                                ng-model="selectedReleasedType"
                                ng-options="fileType for fileType in releaseType"
                                data-ng-change="getVersionToAdd()"
                        />
<!--                        <input style="margin-bottom:20px; width:30%;" type="text" data-ng-model="selectedReleasedType" ng-change="filter()" value="selectedReleasedType">-->
<!--                        <button class="btn btn-primary" style="margin-top: 5px">-->
<!--                        </button>-->
                    </div>
                    <div data-ng-if="!enterVersion" style="display:flex">
                        <div class="col-xs-6">Select Application Version</div>
                        <select
                                style="margin-bottom:20px; width:20%"
                                class="form-control"
                                ng-model="versionToAdd"
                                ng-options="fileType for fileType in versionToShow"
                                data-ng-change="onVersionInput(versionToAdd)"
                        />
<!--                        <div style="justify-content:center; display:flex">-->
                            <button class="btn btn-primary" style="margin-bottom:10px;margin-left:2%; width:20%;height:5%"
                                    data-ng-click="toggleVersionButton()">Enter Manually
                            </button>
<!--                        </div>-->
                    </div>
                    <div data-ng-if="enterVersion">
                        <div class="col-xs-6">Enter Application Version</div>
                        <input style="margin-bottom:20px; width:30%;" type="text" ng-model="versionToAdd" data-ng-change="onVersionInput(versionToAdd)" placeholder="Enter your version"/>
                    </div>
                    <div data-ng-if="uploaderButton">
                            <div class="col-xs-6">Choose Compatible POS Version</div>
                        <select
                                style="margin-bottom:20px; width:20%"
                                class="form-control"
                                ng-model="selectedCompatiblePosVersion"
                                ng-options="fileType for fileType in compatiblePosVersion"
                                data-ng-change="onCompatibileVersionInput(selectedCompatiblePosVersion)"
                        />
                        <div class="col-xs-6">Upload App Build</div>
                                <input class="btn btn-default" style="margin-bottom:20px; width:30%" type="file"
                                       file-model="fileToUpload" accept="">
                    </div>
                    <div>
                        <div class="col-xs-6">Enter Deployment Description</div>
                        <div data-ng-repeat="desc in descriptionArray track by $index">
                            <div style="margin-left:50%;width:100%">
                                <div data-ng-if="descriptionArray[$index].length >=100">One point should contain 100 characters only</div>
                                <input type="text" maxlength="100"  style="width: 35%;height:30px; margin-bottom:10px;" ng-model="descriptionArray[$index]"/>
                                <button class="btn btn-danger" ng-if="descriptionArray.length != 1" ng-click='deleteDescription($index)'>-</button>
                                <button class="btn btn-primary" ng-if="$index == descriptionArray.length-1" ng-click='addDescription()'>+</button>
                            </div>
                        </div>
                    </div>
                    <div style="justify-content:center; display:flex">
                        <button class="btn btn-primary" style="margin-bottom:0px; width:30%"
                                data-ng-click="addVersion()">Add New Version
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div
    class="modal fade"
    id="updateCompatibility"
    tabindex="-1"
    role="dialog"
    aria-labelledby="myModalLabel">
    <div
        class="modal-dialog"
        role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close"
                        data-ng-click="closeCompatibilityModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel2">Update Compatibility</h4>
            </div>
            <div class="modal-body">
                <div style="flex-direction:column; display:flex" >
                    <div>
                        <div class="col-xs-6">Existed Compatible POS Version : </div>
                        <div>{{existedCompatiblePosVersion}}</div>
                    </div>
                    <div>
                        <div class="col-xs-6">Choose Compatible POS Version</div>
                        <select
                                style="margin-bottom:20px; width:20%"
                                class="form-control"
                                ng-model="selectedCompatiblePosVersion"
                                ng-options="fileType for fileType in compatiblePosVersion"
                                data-ng-change="onCompatibileVersionInput(selectedCompatiblePosVersion)"
                        />
                    </div>
                    <div>
                    <div class="col-xs-6">Upload App Build</div>
                    <input class="btn btn-default" style="margin-bottom:20px; width:30%" type="file"
                           file-model="fileToUpload" accept="">
                    </div>
                    <div style="justify-content:center; display:flex">
                        <button class="btn btn-primary" style="margin-bottom:0px; width:30%"
                                data-ng-click="updateCompatibility()">Update
                        </button>
                    </div>
                </div>
            </div>
        </div>

        </div>
</div>

