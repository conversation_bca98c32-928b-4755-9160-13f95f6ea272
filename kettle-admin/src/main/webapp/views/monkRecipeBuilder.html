<style type="text/css">
form.tab-form-demo .tab-pane {
	margin: 20px 20px;
}

.tabDiv {
	margin-top: 20px;
	height: 350px;
	max-height: 350px;
}

.divInnerRow {
	margin-top: 20px;
	height: 50px;
	max-height: 50px;
	width: 100%;
}

.tabPane {
	border: 1px solid #ddd;
	border-radius: 0px 0px 5px 5px;
	padding: 10px;
}
</style>

<div data-ng-init="init()">
	<div class="panel panel-info">
		<div class="panel-heading">{{recipeDetail.name.length > 0 ?
			recipeDetail.name : 'Please select recipe'}}</div>
		<div class="panel-body">
			<uib-tabset class="tabPane" active="activeTab"> <uib-tab
				index="0" heading="Product Selection">
			<div class="row tabDiv">
				<div class="col-xs-12">
					<div class="row divInnerRow">
						<div class="col-xs-4">
							<label class="pull-right">Select Product</label>
						</div>
						<div class="col-xs-8">
							<select ui-select2 class="form-control"
								style="width: 100% !important" data-ng-model="selectedProductId"
								data-placeholder="Select a product"
								data-ng-change="selectMonkProduct(selectedProductId)">
								<option value=""></option>
								<option
									data-ng-repeat="product in productsInfo | filter: filterByHotBeverages"
									value="{{product.id}}">{{product.name}}</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-xs-12">
					<div class="row divInnerRow">
						<div class="col-xs-4">
							<label class="pull-right">Dimension</label>
						</div>
						<div class="col-xs-8">
							<select ui-select2 class="form-control"
								style="width: 100% !important"
								data-ng-model="selectedDimensionId"
								data-placeholder="Select a dimension"
								data-ng-change="selectDimension(selectedDimensionId)">
								<option
									data-ng-repeat="dimension in selectedDimensionProfile.content"
									value="{{dimension.id}}">{{dimension.name}}</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-xs-12"
					data-ng-if="existingRecipes != null && existingRecipes.length > 0">
					<div class="row divInnerRow">
						<div class="col-xs-12">
							<table class="table table-striped table-bordered">
								<thead>
									<th>Recipe Id</th>
									<th>Recipe Name</th>
									<th>Actions</th>
								</thead>
								<tbody>
									<tr
										data-ng-repeat="recipe in existingRecipes track by recipe.recipeId">
										<td>{{recipe.recipeId}}</td>
										<td>{{recipe.name}}</td>
										<td align="left"><span
											data-ng-click="selectRecipeForEdit(recipe)"
											style="cursor: pointer" title="Add/Edit Details"><i
												class="fa fa-edit"
												style="font-size: 24px; margin-right: 5px"></i></span> <span
											data-ng-click="removeRecipe(recipe)" style="cursor: pointer"
											title="Delete Recipe"><i class="fa fa-remove"
												style="font-size: 24px; margin-right: 5px; color: red"></i></span></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div class="col-xs-12" style="text-align: center;">
					<button class="btn btn-primary"
						data-ng-show="selectedProductId != null && selectedDimensionId != null && ( existingRecipes == null || existingRecipes.length < 1)"
						data-ng-click="addNewRecipe()">
						<i class="fa fa-plus"></i> Add Recipe
					</button>
				</div>
			</div>
			</uib-tab> <uib-tab index="1" heading="Basic Info">
			<div class="row tabDiv">
				<div class="col-xs-12">
					<div class="row divInnerRow">
						<div class="col-xs-4">
							<label class="pull-right">Recipe Name</label>
						</div>
						<div class="col-xs-8">
							<input class="form-control" data-ng-model="recipeDetail.name"
								disabled />
						</div>
					</div>
				</div>
				<div class="col-xs-12">
					<div class="row divInnerRow">
						<div class="col-xs-4">
							<label class="pull-right">Start Date</label>
						</div>
						<div class="col-xs-8">
							<div class="datepicker" data-date-min-limit="{{todaysDate}}"
								data-date-format="yyyy-MM-dd">
								<input class="form-control"
									data-ng-model="recipeDetail.startDate"
									placeholder="Select a start date..." type="text" required />
							</div>
						</div>
					</div>
				</div>
			</div>

			</uib-tab> <uib-tab index="2" heading="Details">
			<div class="row tabDiv">
				<div class="col-xs-12" style="margin-left: 20px; margin-right: 20px">
					<div class="row divInnerRow">
						<div class="col-xs-12">
							<label>{{recipeDetail.preparation}} Chai</label>
						</div>
						<div class="col-xs-12" style="margin-top: 20px;">
							<div class="row divInnerRow">
								<div class="col-xs-1">
									<label style="text-align: center">Quantity</label>
								</div>
								<div class="col-xs-2">
									<label style="text-align: center">Water (ml)</label>
								</div>
								<div class="col-xs-2">
									<label style="text-align: center">Milk (ml)</label>
								</div>
								<div class="col-xs-2">
									<label style="text-align: center">Boil Settle (secs)</label>
								</div>
								<div class="col-xs-1">
									<label style="text-align: center">No Of Boils</label>
								</div>
								<div class="col-xs-2">
									<label style="text-align: center">Heating Time (mins)</label>
								</div>
								<div class="col-xs-2">
									<label style="text-align: center">Heating Time (secs)</label>
								</div>
							</div>
							<div class="row divInnerRow"
								data-ng-repeat="data in recipeDetail.datas">
								<div class="col-xs-1">
									<label>{{data.quantity}}</label>
								</div>
								<div class="col-xs-2">
									<input class="form-control" type="number"
										data-ng-model="data.water" min="0" max="9999" />
								</div>
								<div class="col-xs-2">
									<input class="form-control" type="number"
										data-ng-model="data.milk" min="0" max="9999" />
								</div>
								<div class="col-xs-2">
									<input class="form-control" type="number"
										data-ng-model="data.boilSettle" min="0" max="20" />
								</div>
								<div class="col-xs-1">
									<input class="form-control" type="number"
										data-ng-model="data.noOfBoils" min="0" max="9" />
								</div>
								<div class="col-xs-2">
									<input class="form-control" type="number"
										data-ng-model="data.heatingTimeMins" min="0" max="60" />
								</div>
								<div class="col-xs-2">
									<input class="form-control" type="number"
										data-ng-model="data.heatingTimeSecs" min="0" max="60" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			</uib-tab> </uib-tabset>
		</div>

		<div class="row pager" style="margin-top: 10px;">
			<nav>
				<ul class="pager">
					<li data-ng-if="activeTab!=0" data-ng-click="setPrevTab(activeTab)"
						class="previous" style="cursor: pointer"><a><span
							aria-hidden="true">&larr;</span> Previous</a></li>
					<li data-ng-if="activeTab==0" data-ng-click="clearAll(true)"
						style="cursor: pointer"><a>Reset </a></li>
					<li data-ng-if="activeTab==2" data-ng-click="showPreview()"
						style="cursor: pointer"><a>Preview </a></li>
					<li data-ng-if="activeTab > 0 && activeTab < 2"
						data-ng-click="setNextTab(activeTab)" class="next"
						style="cursor: pointer"><a>Next <span aria-hidden="true">&rarr;</span></a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<div class="modal fade previewModal" id="showPreviewModal" role="dialog"
	aria-labelledby="showPreviewModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="showPreviewModalLabel">{{recipeDetail.name}}
					Preview</h4>
			</div>
			<div class="modal-body">
				<div class="row tabDiv">
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-6">
								<button class="btn btn-primary pull-right" data-dismiss="modal">Cancel</button>
							</div>
							<div class="col-xs-6">
								<button class="btn btn-primary pull-left"
									data-ng-click="saveRecipe()">Save Recipe</button>
							</div>
						</div>
					</div>

					<div class="col-xs-12">
						<h3>Recipe Details</h3>
						<div class="row divInnerRow">
							<div class="col-xs-12" style="margin-top: 10px;"
								data-ng-if="recipeDetail.datas != null">
								<table class="table table-striped table-bordered"
									style="margin-top: 10px;">
									<thead>
										<th>Quantity</th>
										<th>Water(ml)</th>
										<th>Milk(ml)</th>
										<th>Boil Settle(s)</th>
										<th>No Of Boils</th>
										<th>Heating Time Mins</th>
										<th>Heating Time Secs</th>
									</thead>
									<tbody>

										<tr
											data-ng-repeat="detail in recipeDetail.datas track by $index">
											<td><b>{{detail.quantity}}</b></td>
											<td>{{detail.water}}</td>
											<td>{{detail.milk}}</td>
											<td>{{detail.boilSettle}}</td>
											<td>{{detail.noOfBoils}}</td>
											<td>{{detail.heatingTimeMins}}</td>
											<td>{{detail.heatingTimeSecs}}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>