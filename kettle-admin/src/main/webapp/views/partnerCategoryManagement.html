<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    table th, td {
        border: #ccc 1px solid;
    }

    .list.selected {
        background: #1ca62c;
        color: #fff;
    }
    .multiselect-container {
        display: flex;
    }
    .multiselect {
        flex: 1;
        margin-right: 10px;
    }
    .padding-right {
        padding-right: 50px;
    }
    .menu-active {
        background-color: rgb(205, 255, 205);
    }
    .inactive {
        background-color: rgb(255, 215, 215);
    }
    .archived {
        background-color: rgb(255, 255, 193);
    }
    .activate {
        background-color: lightgreen;
        &:hover {
            background-color: rgb(114, 238, 114);
        }
    }
    .deactivate {
        background-color: lightcoral;
        &:hover {
            background-color: rgb(234, 89, 89);
        }
    }
    .archive {
        background-color: rgb(255, 255, 105);
        &:hover {
            background-color: rgb(242, 242, 75);
        }
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Category Management Dashboard</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'MANAGE GROUPS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage product groups.</h3>
                    <p>Product groups are product clusters that can be used to create a menu category or sub-category.
                        Group also contains the sequence of each product and
                        product can't be repeated in a group.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 alert alert-warning">
                    <input type="button" class="btn btn-success btn-lg" value="Add New Group"
                           data-ng-click="showCreateGroups = !showCreateGroups"/>
                    <div data-ng-show="showCreateGroups">
                        <h3>Add new group</h3>
                        <div class="form-group">
                            <label>Group name</label>
                            <input type="text" id="groupName" class="form-control" data-ng-model="newGroup.groupName"/>
                        </div>
                        <div class="form-group">
                            <label>Group Type</label>
                            <select class="form-control"
                                    data-ng-options="type as type.name for type in groupTypes track by type.id"
                                    data-ng-model="selectedType"
                                    data-ng-change="setSelectedType(selectedType)">
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Group Tag</label>
                            <input type="text" id="groupTag" class="form-control" data-ng-model="newGroup.groupTag"/>
                        </div>
                        <div class="form-group">
                            <label>Group description</label>
                            <input type="text" id="groupDescription" class="form-control"
                                   data-ng-model="newGroup.groupDescription"/>
                        </div>


                        <div class="form-group">
                            <label>Menu App</label>
                            <select
                                    class="form-control"
                                    data-ng-options=" type for type in  menuAppList"
                                    data-ng-model="newGroup.menuApp"
                            >

                            </select>
                        </div>
                        <div ng-show="selectedType.id==2">

                            <div class="form-group">
                                <label>Category Icon</label>
                                <select
                                        ui-select2
                                        class="form-control"
                                        id="iconId"
                                        data-ng-options=" icon.iconName for icon in  icons"
                                        data-ng-model="newGroup.icon"
                                >

                                </select>
                            </div>

                            <!--<div ng-show="newGroup.menuApp=='DINE_IN_APP'">-->
                            <div class="form-group">
                                <label>Category Tags </label>
                                <select
                                        class="form-control"
                                        data-ng-options=" tag.id as tag.name for tag in categoryTags "
                                        data-ng-model="newGroup.categoryTag"
                                >
                                </select>
                            </div>

                            <div class="form-group">
                                <label> Category Details </label>
                                <input class="form-control" data-ng-model="newGroup.menuCategoryDetails"
                                       placeholder="Please provide desc. " required type="text"/>
                            </div>
                            <div class="form-group">
                                <label>Menu Category Image </label>
                                <div class="row">
                                    <div class="col-lg-8">
                                        <input class="btn btn-default"
                                               file-model="newGroup.categoryImageUpload"
                                               style="width: 100%;"
                                               type="file">
                                    </div>
                                    <div class="col-lg-4">
                                        <button class="btn btn-primary "
                                                ng-click="uploadCategoryImage('CREATE')">Upload
                                        </button>
                                        <div class="pull-right">
                                            <div ng-show="newGroup.menuCategoryImage">
                                                <img height="50px" width="50px"
                                                     data-ng-click="openProductImageModal(newGroup.menuCategoryImage)"
                                                     data-ng-src="{{newGroup.menuCategoryImage}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <span ng-if="categoryImageUploaded"> uploaded!
                                <button class="btn btn-primary pull-right form-group"
                                        ng-click="clearImage('CATEGORY_IMAGE','CREATE')">
                                    Clear Image
                                </button>
                                </span>
                                <!--</div>-->

                            </div>
                            <!--</div>-->

                        </div>
                        <div class="form-group">
                            <label>Menu Type</label>
                            <select
                                    class="form-control"
                                    data-ng-options="type for type in menuType "
                                    data-ng-model="newGroup.menuType"
                            >
                            </select>
                        </div>


                        <div class="col-xs-12">
                            <div ng-show="selectedType.id==2">
                                <div class="form-group col-xs-4">
                                    <input
                                            type="checkbox"
                                            id="groupCheckBox"
                                            data-ng-model="cloneBox"
                                            ng-checked="clone"
                                            data-ng-click="cloneStatus(cloneBox)"
                                            style="height: 2em; width: 2em;"/>
                                    <span>Tick for cloning</span>
                                </div>
                                <div ng-if="cloneBox" class="form-group col-xs-4">
                                    <label>Select Sub Category For Cloning</label>
                                    <select
                                            ui-select2
                                            id="selectedGroupCloneId"
                                            class="form-control"
                                            data-ng-change="setSelectedCloningSubCategory(cloningGroup)"
                                            data-ng-options="group as group.groupName+'-'+group.groupTag+'-'+group.groupDescription for group in productGroups|filter:{groupType: 'SUB_CATEGORY'} "
                                            data-ng-model="cloningGroup"
                                    >
                                    </select>
                                </div>
                            </div>
                            <div class="form-group text-right col-xs-4 pull-right">
                                <input type="button" class="btn btn-primary" value="Add Group"
                                       data-ng-click="addNewGroup()"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group col-xs-6">
                <label>Menu App</label>
                <select class="form-control"
                        data-ng-options=" type for type in  menuAppList"
                        data-ng-model="selectedMenuApp"
                        data-ng-change="setSelectedMenuAppForSearch(selectedMenuApp)">
                </select>
            </div>
            <div class="form-group col-xs-6">
                <label>Group Type</label>
                <select class="form-control"
                        data-ng-options="type as type.name for type in groupTypes track by type.id"
                        data-ng-model="selectedTypeForSearch"
                        data-ng-change="setSelectedTypeForSearch(selectedTypeForSearch)">
                </select>
            </div>
            <div class="form-group col-xs-6">
                <button type="button" class="btn btn-success btn-lg"
                        data-ng-click="getAllProductGroups()">Search</button>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group col-xs-6">
                        <input type="text" class="form-control" data-ng-model="searchProductGroup"
                               placeholder="enter group tag here"/>
                    </div>

                    <div class="form-group col-xs-4">
                        <button class="btn btn-success " data-ng-click="showGroups= !showGroups"
                                ng-disabled="productGroups.length<=0">Display all group
                        </button>
                    </div>

                    <div ng-show="showGroups">
                        <table class="table table-bordered " data-ng-if="productGroups.length>0">

                            <tr>
                                <th>Group Type</th>
                                <th>Group Name</th>
                                <th>Group Tag</th>
                                <th>Menu Type</th>
                                <th>Description</th>
                                <th>Creation Time</th>
                                <th>Created By</th>
                                <th>Manage Product Mapping</th>
                                <th>Action</th>
                            </tr>
                            <tbody>
                            <tr ng-repeat-start="group in filtered = (productGroups | filter:searchProductGroup | orderBy : predicate :reverse) track by group.groupId"
                                data-ng-click="setSelectedRow(group)" style="cursor: pointer;"
                                data-ng-class="{'rowSelected':group.groupId===idSelected}">

                                <td>{{group.groupType}}</td>
                                <td>{{group.groupName}}</td>
                                <td>{{group.groupTag}}</td>
                                <td>{{group.menuType}}</td>
                                <td>{{group.groupDescription}}</td>
                                <td>{{group.creationTime|date:'yyyy-MM-dd'}}</td>
                                <td>{{group.createdBy.name}} [{{group.createdBy.id}}]</td>
                                <td>
                                    <input type="button" class="btn btn-sm btn-primary" value="Mapping"
                                           data-ng-click="openProductSequenceModal(group)"
                                           data-ng-if="group.groupType == 'SUB_CATEGORY'"/>
                                </td>
                                <td data-ng-click="openEditGroup(group)"><i class="fa fa-edit"
                                                                            style="font-size: 24px; margin-right: 5px"></i>
                                </td>
                            </tr>
                            <tr ng-repeat-end=""
                                ng-show="group.groupId===idSelected && groupSelected">
                                <td colspan="8">
                                    <table class="table table-bordered">
                                        <tr ng-repeat="subGroups in subCategoryInCategory  "
                                            data-ng-class="{'rowSelected':true}">
                                            <td>{{subGroups.groupType}}</td>
                                            <td>{{subGroups.groupName}}</td>
                                            <td>{{subGroups.groupTag}}</td>
                                            <td>{{subGroups.menuType}}</td>
                                            <td>{{subGroups.groupDescription}}</td>
                                            <td>{{subGroups.creationTime|date:'yyyy-MM-dd'}}</td>
                                            <td>{{subGroups.createdBy.name}} [{{subGroups.createdBy.id}}]</td>
                                            <td>
                                                <input type="button" class="btn btn-sm btn-primary" value="Mapping"
                                                       data-ng-click="openProductSequenceModal(subGroups)"
                                                />
                                                <input type="button" class="btn btn-sm btn-primary" value="Recommended"
                                                       data-ng-click="openProductRecommendationModal(subGroups)"
                                                />
                                            </td>
                                            <td data-ng-click="openEditGroup(subGroups)"><i class="fa fa-edit"
                                                                                            style="font-size: 24px;cursor: pointer; margin-right: 5px"></i>
                                            </td>
                                        </tr>
                                    </table>

                                </td>


                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'MANAGE MENU'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage menus.</h3>
                    <p>Menus are product group clusters combined together in proper sequence.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 alert alert-warning">
                    <input type="button" class="btn btn-success btn-lg" value="Add New Menu"
                           data-ng-click="showCreateMenu = !showCreateMenu"/>
                    <div data-ng-show="showCreateMenu">
                        <h3>Add new menu</h3>
                        <div class="form-group">
                            <label>Menu name</label>
                            <input type="text" id="menuName" class="form-control"
                                   data-ng-model="newMenu.menuSequenceName"/>
                        </div>
                        <div class="form-group">
                            <label>Menu description</label>
                            <input type="text" id="menuDescription" class="form-control"
                                   data-ng-model="newMenu.menuSequenceDescription"/>
                        </div>
                        <div class="form-group">
                            <label>Menu App</label>
                            <select
                                    class="form-control"
                                    data-ng-options=" type for type in  menuAppList"
                                    data-ng-model="newMenu.menuApp"
                            >

                            </select>
                        </div>
                        <div class="form-group">
                            <label>Menu Type</label>
                            <select class="form-control"
                                    data-ng-options="type for type in menuType "
                                    data-ng-model="newMenu.menuType"
                            >
                            </select>
                        </div>
                        <div class="col-xs-12">
                            <div class="form-group col-xs-4">
                                <input
                                        type="checkbox"
                                        class="center-block"
                                        ng-model="menuClone"
                                        data-ng-click="cloneStatus(menuClone)"
                                        ng-checked="clone"
                                        style="height: 2em; width: 2em;"/>
                                <span>Tick for cloning</span>
                            </div>
                            <div ng-if="menuClone" class="form-group col-xs-4">
                                <label>Select Menu For Cloning</label>
                                <select
                                        ui-select2
                                        id="selectedCloningMenuId"
                                        class="form-control"
                                        data-ng-change="setSelectedCloningMenu(cloningMenu)"
                                        data-ng-options="menu as menu.menuSequenceName+'-'+menu.menuSequenceDescription for menu in menus "
                                        data-ng-model="cloningMenu"
                                >
                                </select>
                            </div>
                        </div>
                        <div class="form-group text-right col-xs-4 pull-right">
                            <input type="button" class="btn btn-primary " value="Add Menu"
                                   data-ng-click="addNewMenu()"/>
                        </div>

                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <!--<div class="form-group" data-ng-if="menus.length>0">-->
                        <!--<label>Search</label>-->
                        <!--<input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>-->
                    <!--</div>-->
                    <table class="table table-bordered table-striped" data-ng-if="menus.length>0">
                        <tr>
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" class="form-control" data-ng-model="search" data-ng-change="" placeholder="Enter Menu Name to search"/>
                            </div>
                            <div class="form-group" style="display: flex;">
                                <label class="padding-right">Filter By</label>
                                <div class="padding-right" style="width: 25%;">
                                    <label>Menu App</label>
                                    <select
                                        class="form-control"
                                        data-ng-options="type for type in menuAppList"
                                        data-ng-model="newGroup.menuApp"
                                    >
                                        <!-- <option value="ALL">ALL</option> -->
                                    </select>
                                </div>
                                <div class="padding-right" style="width: 25%;">
                                    <label>Menu Status</label>
                                    <select
                                        class="form-control"
                                        data-ng-options="menuStatus for menuStatus in menuStatusList"
                                        data-ng-model="newGroup.menuStatus"
                                    >
                                        <!-- <option value="ALL">ALL</option> -->
                                    </select>
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <th>Menu ID</th>
                            <th>Menu Name</th>
                            <th>Description</th>
                            <th>Creation Time</th>
                            <th>Menu Type</th>
                            <th>Menu App</th>
                            <th>Menu Status</th>
                            <th>Created By</th>
                            <th>Change Menu Status</th>
                            <th>Manage Mappings</th>
                        </tr>
                        <tr data-ng-repeat="menu in filtered = (menus | filter:search | filter:customSearch | orderBy : predicate :reverse) track by menu.menuSequenceId">
                            <td>{{menu.menuSequenceId}}</td>
                            <td>{{menu.menuSequenceName}}</td>
                            <td>{{menu.menuSequenceDescription}}</td>
                            <td>{{menu.creationTime|date:'yyyy-MM-dd'}}</td>
                            <td>{{menu.menuType}}</td>
                            <td>{{menu.menuApp}}</td>
                            <td data-ng-class="{
                                    'menu-active': menu.menuStatus === 'ACTIVE',
                                    'inactive': menu.menuStatus === 'IN_ACTIVE',
                                    'archived': menu.menuStatus === 'ARCHIVED'
                                }">
                                {{menu.menuStatus}}
                            </td>
                            <td>{{menu.createdBy.name}} [{{menu.createdBy.id}}]</td>
                            <td>
                                <div>
                                    <input type="button" data-ng-if="menu.menuStatus === 'ACTIVE'" 
                                    value="De-Activate" class="btn btn-sm deactivate"
                                    style="margin-bottom: 10px;"
                                    data-ng-click="setChangeMenuStatusParams(menu.menuSequenceId, 'IN_ACTIVE')">
                                    <input type="button" data-ng-if="menu.menuStatus === 'ACTIVE'" 
                                    value="Archive" class="btn btn-sm archive" 
                                    style="color: black; margin-bottom: 10px;"
                                    data-ng-click="setChangeMenuStatusParams(menu.menuSequenceId, 'ARCHIVED')">

                                    <input type="button" data-ng-if="menu.menuStatus === 'IN_ACTIVE'" 
                                    value="Activate" class="btn btn-sm activate"
                                    style="margin-bottom: 10px; color: black;"
                                    data-ng-click="setChangeMenuStatusParams(menu.menuSequenceId, 'ACTIVE')">
                                    <input type="button" data-ng-if="menu.menuStatus === 'IN_ACTIVE'" 
                                    value="Archive" class="btn btn-sm archive" 
                                    style="color: black; margin-bottom: 10px;"
                                    data-ng-click="setChangeMenuStatusParams(menu.menuSequenceId, 'ARCHIVED')">
                                </div>
                            </td>
                            <td>
                                <input type="button" class="btn btn-sm btn-primary" value="Mapping"
                                       data-ng-click="openMenuSequenceModal(menu)"/>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'MANAGE RECOMMENDATIONS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage recommendation.</h3>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 alert alert-warning">
                    <input type="button" class="btn btn-success btn-lg" value="Add New Recommendation"
                           data-ng-click="showCreateRecommendation = !showCreateRecommendation"/>
                    <div data-ng-show="showCreateRecommendation">
                        <h3>Add new Recommendation</h3>
                        <div class="form-group">
                            <label>Recommendation name</label>
                            <input type="text" id="recommendationName" class="form-control"
                                   data-ng-change="chnage(newRecommendation.menuRecommendationName)"
                                   data-ng-model="newRecommendation.menuRecommendationName"/>
                        </div>
                        <div class="form-group">
                            <label>Recommendation description</label>
                            <input type="text" id="recommendationDescription" class="form-control"
                                   data-ng-model="newRecommendation.menuRecommendationDescription"/>
                        </div>
                        <div class="form-group">
                            <label>Recommendation Type</label>
                            <select class="form-control"
                                    data-ng-options="type for type in recommendationTypes "
                                    data-ng-model="newRecommendation.recommendationType"
                            >
                            </select>
                        </div>
                        <div class="col-xs-12">
                            <div class="form-group col-xs-4">
                                <input
                                        type="checkbox"
                                        class="center-block"
                                        ng-model="newRecommendation.recommendationClone"
                                        style="height: 2em; width: 2em;"/>
                                <span>Tick for cloning</span>
                            </div>
                            <div ng-if="newRecommendation.recommendationClone" class="form-group col-xs-4">
                                <label>Select Menu For Cloning</label>
                                <select
                                        ui-select2
                                        id="selectedCloningRecommendationId"
                                        class="form-control"
                                        data-ng-change="setSelectedCloningRecommendation(cloningRecommendation)"
                                        data-ng-options="recommendation as recommendation.menuRecommendationName+'-'+recommendation.menuRecommendationDescription for recommendation in recommendations "
                                        data-ng-model="cloningRecommendation"
                                >
                                </select>
                            </div>
                        </div>
                        <div class="form-group text-right col-xs-4 pull-right">
                            <input type="button" class="btn btn-primary " value="Add Recommendation"
                                   data-ng-click="addNewRecommendation()"/>
                        </div>

                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <!--<div class="col-xs-6">-->
                            <!--<div class="form-group" data-ng-if="recommendations.length>0">-->
                                <!--<label>Search</label>-->
                                <!--<input type="text" class="form-control" data-ng-model="searchRecommendation" placeholder="Enter Recommendation Name to search"/>-->
                            <!--</div>-->
                        <!--</div>-->
                        <div class="col-xs-12">
                            <div class="form-group">
                                <input type="button" class="btn btn-success pull-right" value="Refresh Mapping Cache"
                                       data-ng-click="refreshUnitMenuMappingCache()">
                            </div>
                        </div>
                    </div>
                    <table class="table table-bordered table-striped" data-ng-if="recommendations.length>0">
                        <tr>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" class="form-control" data-ng-model="searchRecommendation" placeholder="Enter Recommendation Name to search" data-ng-change=""/>
                        </div>
                        </tr>
                        <tr>
                            <th>Recommendation Name</th>
                            <th>Description</th>
                            <th>Creation Time</th>
                            <th>Recommendation Type</th>
                            <th>Created By</th>
                            <th>Manage Mappings</th>
                            <th>Action</th>
                        </tr>
                        <tr data-ng-repeat="recommendation in filtered = (recommendations | filter: searchRecommendation | orderBy : predicate :reverse) track by recommendation.menuRecommendationId">
                            <td>{{recommendation.menuRecommendationName}}</td>
                            <td>{{recommendation.menuRecommendationDescription}}</td>
                            <td>{{recommendation.creationTime|date:'yyyy-MM-dd'}}</td>
                            <td>{{recommendation.recommendationType}}</td>
                            <td> {{recommendation.createdBy}}</td>
                            <td>
                                <input type="button" class="btn btn-sm btn-primary" value="Mapping"
                                       data-ng-click="openRecommendationModal(recommendation)"/>
                            </td>
                            <td>
                                <input type="button" class="btn btn-success" value="Activate"
                                       data-ng-click="changeRecommendationStatus(recommendation, 'ACTIVE')"
                                       data-ng-if="recommendation.status == 'IN_ACTIVE'"/>
                                <input type="button" class="btn btn-danger" value="De-activate"
                                       data-ng-click="changeRecommendationStatus(recommendation, 'IN_ACTIVE')"
                                       data-ng-if="recommendation.status == 'ACTIVE'"/>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'MANAGE ICONS'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to manage icons.</h3>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 alert alert-warning">
                    <input type="button" class="btn btn-success btn-lg" value="Add New Icon"
                           data-ng-click="showCreateIcon = !showCreateIcon"/>
                    <div data-ng-show="showCreateIcon">
                        <h3>Add new Icon</h3>
                        <div class="form-group">
                            <label>Icon name</label>
                            <input type="text" class="form-control"
                                   data-ng-model="newIcon.iconName"/>
                        </div>
                        <div class="form-group">
                            <label>Icon description</label>
                            <input type="text" class="form-control"
                                   data-ng-model="newIcon.iconDescription"/>
                        </div>
                        <div class="form-group">
                            <label>Upload Icon</label>
                            <input class="btn btn-default" file-model="fileToUpload" style="width: 100%;"
                                   type="file">
                        </div>

                        <div class="form-group text-right col-xs-4 pull-right">
                            <input type="button" class="btn btn-primary " value="Add Icon"
                                   data-ng-click="addNewIcon()"/>
                        </div>
                        <!--<img height="100px" width="100px"-->
                        <!--data-ng-src="{{imageSuffix}}{{iconDetails.iconUrl}}"/>-->
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <!--<div class="form-group" data-ng-if="icons.length>0">-->
                        <!--<label>Search</label>-->
                        <!--<input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>-->
                    <!--</div>-->
                    <table class="table table-bordered table-striped" data-ng-if="icons.length>0">
                        <tr>
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" class="form-control" data-ng-model="search" data-ng-change=""/>
                            </div>
                        </tr>
                        <tr>
                            <th>Icon Name</th>
                            <th>Description</th>
                            <th>Icon</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Creation Time</th>
                        </tr>
                        <tr data-ng-repeat="icon in icons | filter:search">
                            <td>{{icon.iconName}}</td>
                            <td>{{icon.iconDescription}}</td>
                            <td><img height="100px" width="100px" data-ng-src="{{icon.iconUrl}}"/></td>
                            <td>{{icon.status}}</td>
                            <td>{{icon.createdBy}}</td>
                            <td>{{icon.creationTime|date:'yyyy-MM-dd'}}</td>
                            <!--<td>-->
                            <!--<input type="button" class="btn btn-sm btn-primary" value="Mapping"-->
                            <!--data-ng-click="openMenuSequenceModal(menu)"/>-->
                            <!--</td>-->
                        </tr>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>


<div class="modal fade previewModal" id="productSequenceModal" tabindex="-1" role="dialog"
     aria-labelledby="productSequenceModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Products for {{selectedGroup.groupName}} -
                    ({{selectedGroup.groupTag}})</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-3 col-xs-offset-1">
                        <div class="form-group">
                            <label>Product Group List</label>
                            <input type="text" class="form-control" data-ng-model="searchProductGroupMapping"
                                   placeholder="Search"/>
                        </div>
                        <div style="height: 500px; overflow: auto;">
                            <table class="table table-bordered table-striped" data-ng-if="productGroupMap.length>0">
                                <tr>
                                    <th>Product Name</th>
                                    <th>Groups</th>
                                </tr>
                                <tr data-ng-repeat="item in filtered = (productGroupMap | filter:searchProductGroupMapping | orderBy : predicate :reverse) track by item.id">
                                    <td>{{item.name}}</td>
                                    <td>{{item.groups.join(", ")}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-xs-7">
                        <!--<uib-tabset active="active" justified="true">-->
                        <!--<uib-tab index="0" heading="Product Mapping">-->
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="row">
                                    <div class="col-xs-6">
                                        <div class="groupListing" dnd-list="groupAvialableProductSequence"
                                             dnd-allowed-types="['PRODUCT']"
                                             style="height:575px;overflow:auto;margin-bottom:10px;">
                                            <div class="list"
                                                 data-ng-repeat="product in groupAvialableProductSequence"
                                                 draggable="true" dnd-draggable="product" dnd-type="'PRODUCT'"
                                                 dnd-effect-allowed="copyMove"
                                                 dnd-moved="groupAvialableProductSequence.splice($index, 1)">
                                                {{product.name}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6">
                                        <div class="groupListing" dnd-list="selectedProductSequence"
                                             dnd-allowed-types="['PRODUCT']"
                                             style="height:575px;overflow:auto;margin-bottom:10px;">
                                            <div class="list" data-ng-repeat="product in selectedProductSequence"
                                                 draggable="true" dnd-draggable="product" dnd-type="'PRODUCT'"
                                                 dnd-effect-allowed="copyMove"
                                                 dnd-moved="selectedProductSequence.splice($index, 1)">
                                                {{product.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 20px">
                            <div class="col-xs-12 text-right">
                                <input type="button" class="btn btn-primary" value="Submit"
                                       data-ng-click="setGroupProductSequence()"/>
                            </div>
                        </div>
                        <!--<div class="row">
                            <div class="col-xs-6">
                                <input type="button" class="btn btn-primary" value="Add mappings" data-ng-click="addProductTagMapping()" />
                            </div>
                            <div class="col-xs-6 text-right">
                                <input type="button" class="btn btn-primary" value="Remove mappings" data-ng-click="removeProductTagMapping()" />
                            </div>
                        </div>-->
                        <!--<div class="row" style="margin-top: 20px">
                            <div class="col-xs-12 text-right">
                                <input type="button" class="btn btn-primary" value="Next" data-ng-click="setProductTagSequencing()" />
                            </div>
                        </div>-->
                        <!--</uib-tab>-->
                        <!--<uib-tab index="1" heading="Product Sequencing">
                            <div class="row" style="margin-top: 10px;">
                                <div class="col-xs-9">
                                    <ul class="partnerCategoryPicker">
                                        <li data-ng-repeat="(key, value) in productSequenceMap track by $index"
                                            data-ng-click="selectSequencingProduct(value)" data-ng-class="{'selected':value.selected == true}">
                                            {{value.name}}:{{key}}
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-xs-3">
                                    <input type="button" class="btn btn-primary" value="Up" data-ng-click="setPreviousIndex()" /><br />
                                    <input type="button" class="btn btn-primary" value="Down" data-ng-click="setNextIndex()" />
                                </div>
                            </div>
                            <div class="row" style="margin-top: 20px">
                                <div class="col-xs-6">
                                    <input type="button" class="btn btn-primary" value="Previous" data-ng-click="setProductTagMapping()" />
                                </div>
                                <div class="col-xs-6 text-right">
                                    <input type="button" class="btn btn-primary" value="Submit" data-ng-click="setGroupProductSequence()" />
                                </div>
                            </div>
                        </uib-tab>-->
                        <!--</uib-tabset>-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="productRecommendationModal" tabindex="-1" role="dialog"
     aria-labelledby="productRecommendationModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="productRecommendationModalLabel">
                    Products for {{selectedGroup.groupName}} - ({{selectedGroup.groupTag}})
                </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="groupListing" style="height:575px;overflow:auto;margin-bottom:10px;">
                            <div class="list" data-ng-repeat="sequence in selectedGroup.productSequenceList"
                                 style="cursor: pointer"
                                 data-ng-class="{'selected':sequence.recommended}"
                                 data-ng-click="toggleProductRecommended(sequence)">
                                {{sequence.product.name}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top: 20px">
                    <div class="col-xs-12 text-right">
                        <input type="button" class="btn btn-primary" value="Submit"
                               data-ng-click="setGroupProductRecommendation()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade previewModal" id="menuSequenceModal" tabindex="-1" role="dialog"
     aria-labelledby="menuSequenceModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-ng-click="refreshAllVariablesOnModalClose()" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="menuSequenceModalLabel">Menu Sequence for
                    {{selectedMenu.menuSequenceName}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="alert alert-info"> Please drag and drop elements from left to right to create
                            sequence.
                        </div>
                        <div class="col-xs-12">
                            <div class="col-xs-6">
                                <select
                                        class="form-control"
                                        data-ng-options="type for type in searchtype "
                                        data-ng-model="selectedSearchType"
                                        data-placeholder="Search group on"
                                        data-ng-change="setSelectedSearchtype(selectedSearchType)"
                                >
                                </select>
                            </div>
                            <div class="col-xs-6">
                                <select
                                        class="form-control"
                                        data-ng-options="type for type in setSelection "
                                        data-ng-model="selection"
                                >
                                </select>
                            </div>
                        </div>
                        <div class="alert alert-danger" data-ng-if="menuSequenceError != null">{{menuSequenceError}}
                        </div>
                        <div class="row">
                            <div class="col-xs-3">
                                <h4 style="margin-top: 20px;">Categories</h4>
                                <div class="groupListing" dnd-list="categoryGroups" dnd-allowed-types="['CATEGORY']"
                                     dnd-drop="categoryBackDrop(index, item, external, type)">
                                    <div class="list"
                                         data-ng-repeat="group in categoryGroups|filter:{groupTag:selection}"
                                         draggable="true"
                                         dnd-draggable="group" dnd-type="'CATEGORY'" dnd-effect-allowed="copyMove"
                                         dnd-moved="categoryGroups.splice($index, 1)">
                                        {{group.groupName}}: {{group.groupTag}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-3">
                                <h4 style="margin-top: 20px;">Sub Categories</h4>
                                <div class="groupListing" dnd-list="subCategoryGroups"
                                     dnd-allowed-types="['SUB_CATEGORY']">
                                    <div class="list"
                                         data-ng-repeat="group in subCategoryGroups|filter:{groupTag:selection}"
                                         draggable="true"
                                         dnd-draggable="group" dnd-type="'SUB_CATEGORY'"
                                         dnd-effect-allowed="copyMove"
                                         dnd-moved="subCategoryGroups.splice($index, 1)">
                                        {{group.groupName}}: {{group.groupTag}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <h3>Menu Sequence</h3>
                                <div class="groupListing" style="border-bottom: #ccc 1px solid;"
                                     dnd-list="menuSequence"
                                     dnd-allowed-types="['CATEGORY']"
                                     dnd-drop="categoryDrop(index, item, external, type)">
                                    <div class="catList" data-ng-repeat="group in menuSequence" draggable="true"
                                         dnd-draggable="group" dnd-type="'CATEGORY'" dnd-effect-allowed="copyMove"
                                         dnd-moved="menuSequence.splice($index, 1)">
                                        <div class="catHead">{{group.groupName}}
                                            <div>
<!--                                            <input type="button" class="btn btn-primary pull-right"  value="View timings"-->
<!--                                                   data-ng-click="viewTimings(group.groupName)" data-toggle="modal" data-target="#viewTimingsModal"/>-->
                                            <input type="button" class="btn btn-primary" style="margin-left width:20%"  value="Add Timings"
                                                   data-toggle="modal" data-ng-click="setGroupName(group)" data-target="#addTimingsModal"/>
                                            </div>
                                        </div>
                                        <div class="catBody" dnd-list="group.subGroups"
                                             dnd-allowed-types="['SUB_CATEGORY']">
                                            <div class="list" data-ng-repeat="subGroup in group.subGroups"
                                                 draggable="true"
                                                 dnd-draggable="subGroup" dnd-type="'SUB_CATEGORY'"
                                                 dnd-effect-allowed="copyMove"
                                                 dnd-moved="group.subGroups.splice($index, 1)">
                                                {{subGroup.groupName}}: {{subGroup.groupTag}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 text-right" style="margin-top: 10px;">
                                <input type="button" class="btn btn-primary" value="Set Menu"
                                       data-ng-click="setMenu()"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div aria-labelledby="editGroupModal" class="modal fade" id="editGroupModal" role="dialog"
     data-keyboard="false" data-backdrop="static"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="editGroupModalLabel"> Edit Group Data</h4>
            </div>
            <div class="modal-body">
                <div class="form-group ">
                    <label>Group Type</label>
                    <input class="form-control" ng-model="editGroup.groupType"
                           disabled type="text"/>
                </div>
                <div class="form-group ">
                    <label>Group Name</label>
                    <input class="form-control" ng-model="editGroup.groupName"
                           type="text"/>
                </div>
                <div class="form-group">
                    <label>Group Tag</label>
                    <input type="text" id="editGroup.groupTag" class="form-control" data-ng-model="editGroup.groupTag"/>
                </div>
                <div class="form-group">
                    <label>Group description</label>
                    <input type="text" id="editGroup.groupDescription" class="form-control"
                           data-ng-model="editGroup.groupDescription"/>
                </div>
                <div class="form-group">
                    <label>Menu App</label>

                    <select
                            class="form-control"
                            data-ng-options=" type for type in  menuAppList"
                            data-ng-model="editGroup.menuApp"
                    >

                    </select>
                </div>
                <div ng-show="editGroup.groupType=='CATEGORY'">

                    <div class="form-group">
                        <label>Category Icon</label>
                        <select
                                class="form-control"
                                id="editGroupIconId"
                                data-ng-model="editGroup.icon"
                                data-ng-options="icon.iconName for icon in  icons track by icon.productGroupImageId"

                        >

                        </select>
                    </div>


                    <!--<div ng-show="editGroup.menuApp=='DINE_IN_APP'">-->
                    <div class="form-group">
                        <label>Category Tags </label>
                        <select
                                class="form-control"
                                data-ng-options=" tag.id as tag.name for tag in categoryTags "
                                data-ng-model="editGroup.categoryTag"
                        >
                        </select>
                    </div>

                    <div class="form-group">
                        <label> Category Details </label>
                        <input class="form-control" data-ng-model="editGroup.menuCategoryDetails"
                               placeholder="Please provide desc. " required type="text"/>
                    </div>
                    <div class="form-group">
                        <label>Menu Category Image </label>
                        <div class="row">
                            <div class="col-lg-8">
                                <input class="btn btn-default"
                                       file-model="editGroup.categoryImageUpload"
                                       style="width: 100%;"
                                       type="file">
                            </div>
                            <div class="col-lg-4">
                                <button class="btn btn-primary "
                                        ng-click="uploadCategoryImage('EDIT')">Upload
                                </button>
                                <div class="pull-right">
                                    <div ng-show="editGroup.menuCategoryImage">
                                        <img height="50px" width="50px"
                                             data-ng-click="openProductImageModal(editGroup.menuCategoryImage)"
                                             data-ng-src="{{editGroup.menuCategoryImage}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span ng-if="categoryImageUploaded"> uploaded!
                                <button class="btn btn-primary pull-right form-group"
                                        ng-click="clearImage('CATEGORY_IMAGE','EDIT')">
                                    Clear Image
                                </button>
                                </span>
                        <!--</div>-->

                    </div>

                </div>
                <div class="form-group">
                    <label>Menu Type</label>
                    <select
                            class="form-control"
                            data-ng-options="type for type in menuType "
                            data-ng-model="editGroup.menuType"
                    >
                    </select>
                </div>
                <div class="row ">
                    <div class="form-group" align="center">
                        <button class="btn btn-primary"
                                ng-click="updateGroup()">
                            Update
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade previewModal" id="menuRecommendationModal" tabindex="-1" role="dialog"
     aria-labelledby="menuRecommendationModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true" data-ng-click="closeRecommendationModal()">&times;</span>
                </button>
                <h4 class="modal-title" id="menuRecommendationModalLabel">Menu Sequence for
                    {{selectedRecommendation.menuRecommendationName}}</h4>
            </div>
            <div class="modal-body">

                <div class="row">
                    <div class="col-xs-12">
                        <div class="alert alert-info"> Please drag and drop elements from left to right to create
                            recommendation
                            sequence.
                        </div>
                        <div class="col-xs-12">
                            <div class="col-xs-6">
                                <select
                                        class="form-control"
                                        data-ng-model="selectedProductType"
                                        ng-init="selectedProductType='ALL'"
                                        data-placeholder="Search product on"
                                >
                                    <option value="ALL">ALL</option>
                                    <option ng-repeat="type in productClassifications" value={{type}}>
                                        {{type}}
                                    </option>
                                </select>
                            </div>
                            <div class="row pull-right">
                                <div class="col-xs-6 text-right" style="margin-top: 10px;">
                                    <input type="button" class="btn btn-primary" value="Set Menu"
                                           data-ng-click="setRecommendation()"/>
                                </div>
                            </div>
                            <!--<div class="col-xs-6">-->
                            <!--<select-->
                            <!--class="form-control"-->
                            <!--data-ng-options="type for type in setSelection "-->
                            <!--data-ng-model="selection"-->
                            <!--&gt;-->
                            <!--</select>-->
                            <!--</div>-->
                        </div>
                        <div class="row">
                            <div class="col-xs-4">
                                <h4>Product</h4>
                                <div class="form-group">
                                    <input type="text" class="form-control"
                                        placeholder="Search by Product ID or Name"
                                        data-ng-model="productSearch">
                                </div>
                                <div class="groupListing" dnd-list="productList"
                                     dnd-drop="recommendationBackDrop(index, item, external, type)"
                                     style="height:600px;overflow:auto;margin-bottom:10px;">
                                    <div class="list"
                                         data-ng-repeat="group in productList|filter:filterProduct|filter:productSearchFilter"
                                         draggable="true"
                                         dnd-draggable="group" dnd-type="'description'" dnd-effect-allowed="copy" dnd-disable-if="$parent.dragging"
                                    >
                                        {{group.name}} : {{group.description}}
                                    </div>
                                </div>
                            </div>
                            <!--<div class="col-xs-3">-->
                            <!--<h4 style="margin-top: 20px;">Products to be Recommended</h4>-->
                            <!--<div class="groupListing" dnd-list="productsInfo"-->
                            <!--&gt;-->
                            <!--<div class="list"-->
                            <!--data-ng-repeat="group in productsInfo|filter:{classification:'MENU'}"-->
                            <!--draggable="true"-->
                            <!--dnd-draggable="product" dnd-type="'description'"-->
                            <!--dnd-effect-allowed="copyMove"-->
                            <!--dnd-moved="productsInfo.splice($index, 1)">-->
                            <!--{{group.name}}: {{group.description}}-->
                            <!--</div>-->
                            <!--</div>-->
                            <!--</div>-->
                            <div class="col-xs-8">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <h4 style="margin-right: 20px;">
                                        Recommendation Sequence
                                    </h4>
                                    <div style="display: flex; align-items: center;">
                                            <input type="checkbox" id="openAll" data-ng-model="openAll" style="margin: 0; margin-right: 10px; transform: scale(1.25);">
                                            <label for="openAll" style="margin: 0">Open All Sequences</label>
                                    </div>
                                </div>
                                <div class="form-group" style="margin-bottom: 20px;">
                                    <input type="text" class="form-control"
                                        placeholder="Search by Sequence Name or ID"
                                        data-ng-model="recommendationSearch">
                                </div>
                                <div class="groupListing" style="border-bottom: #ccc 1px solid; height:600px; overflow:auto;"
                                     dnd-list="recommendationSequence"
                                     dnd-drop="recommendationDrop(index, item, external, type)">
                                    <div class="catList" data-ng-repeat="group in recommendationSequence | filter:recommendationSearchFilter track by (group.id + '_' + group.recommendationTitle)"
                                         draggable="true"
                                         dnd-draggable="group" dnd-type="'description'" dnd-effect-allowed="move"
                                         dnd-moved="recommendationSequence.splice($index, 1)">
                                        <div class="catHead" ng-click="toggleAccordion($index)" style="cursor: pointer">
                                            {{group.name}}
                                            <input style="color: black"
                                                data-ng-model="group.recommendationTitle"
                                                placeholder="Enter recommendation title here"
                                                type="text"
                                                data-ng-click="$event.stopPropagation()"
                                            >
                                            <span class="pull-right">
                                                <i class="fa" ng-class="{'fa-chevron-down': !openAll && openIndex !== $index, 'fa-chevron-up': openAll || openIndex === $index}"></i>
                                            </span>
                                        </div>
                                        <div class="catBody" dnd-list="group.recommendedProduct"
                                             dnd-allowed-types="['description']"
                                             data-ng-show="openAll || openIndex === $index">
                                            <div class="list row"
                                                 data-ng-repeat="recommended in group.recommendedProduct track by recommenderProductTracker(group,recommended)"
                                                 draggable="true"
                                                 dnd-draggable="recommended" dnd-type="'description'"
                                                 dnd-effect-allowed="move"
                                                 dnd-moved="group.recommendedProduct.splice($index, 1)">
                                                <div>
                                                    <div class="col-xs-6">{{recommended.name}} : {{recommended.description}} {{group.id}}:{{recommended.id}}</div>
                                                    <div class="col-xs-6">
                                                        <input style="color: black" class="form-control"
                                                               data-ng-model="recommended.discountAmount"
                                                               placeholder="Enter fixed discount value"
                                                               type="text">
                                                    </div>

                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="row">
                            <div class="col-xs-12 text-right" style="margin-top: 10px;">
                                <input type="button" class="btn btn-primary" value="Set Menu"
                                       data-ng-click="setRecommendation()"/>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div
        class="modal fade"
        id="addTimingsModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            style="width:80%;"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

                <h4
                        class="modal-title"
                        id="myModalLabel1">Add Timings</h4>
            </div>
            <div class="modal-body">
                <div data-ng-hide="mapOfDaysAndSlots == null && viewData == null" data-ng-if="checkMapOfDayAndSlots()" >
                <table class="table table-borderes ">
                    <thead>
                    <tr>
                        <th>Days</th>
                        <th>Slots</th>
                        <th>Action</th>
                    </tr>
                    <tr data-ng-repeat="data in viewData">
                        <td>{{data.days}}</td>
                        <td>{{data.daySlots}}</td>
                        <td>
                            <button class="btn btn-primary" style="margin-top: 5px"
                                    data-toggle="modal" data-ng-click="removeFromViewData(data.days,data.slots)">Remove
                            </button>
                        </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="data in mapOfDaysAndSlots">
                        <td>
                            {{ data.days.join(', ') }}
                        </td>
                        <td>
                            {{ data.slots.join(', ') }}
                        </td>
                        <td>
                            <button class="btn btn-primary" style="margin-top: 5px"
                                    data-toggle="modal" data-ng-click="removeFromMapOfDayAndSlots(data.days,data.slots)">Remove
                            </button>
                        </td>
                    </tr>
                    </tbody>
                </table>
                </div>
<!--                <div data-ng-hide="mapOfDaysAndSlots == null" data-ng-if="checkMapOfDayAndSlots()" > {{mapOfDaysAndSlots}} </div>-->
                        <div ng-app="myApp" ng-controller="partnerCategoryManagementCtrl">
                           <div class="multiselect-container">
                                <div class="multiselect">Select Day
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"  options="days" selected-model="selectedDays"  class="region-card"></div>
                                </div>
                                <div class="multiselect">Select Slot
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"  options="slots" selected-model="selectedSlots" class="region-card"></div>
                                </div>
                            </div>
                        </div>
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-toggle="modal" data-ng-click="addToMapSlots()">ADD
                </button>
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-toggle="modal" data-ng-click="refreshAllVariables()">Save Timings
                </button>
            </div>

        </div>
    </div>
</div>

<!--<div-->
<!--        class="modal fade"-->
<!--        id="viewTimingsModal"-->
<!--        tabindex="-1"-->
<!--        role="dialog"-->
<!--        aria-labelledby="myModalLabel">-->
<!--    <div-->
<!--            class="modal-dialog"-->
<!--            role="document">-->
<!--        <div class="modal-content">-->
<!--            <div class="modal-header">-->
<!--                <button-->
<!--                        type="button"-->
<!--                        class="close"-->
<!--                        data-dismiss="modal"-->
<!--                        aria-label="Close">-->
<!--                    <span aria-hidden="true">&times;</span>-->
<!--                </button>-->

<!--                <h4-->
<!--                        class="modal-title"-->
<!--                        id="myModalLabel2">View Timings</h4>-->
<!--            </div>-->
<!--            <div class="modal-body">-->
<!--                <table class="table table-bordered ">-->
<!--                    <tr>-->
<!--                        <th>Days</th>-->
<!--                        <th>Slots</th>-->
<!--                    </tr>-->
<!--                    <tr data-ng-repeat="(viewDays,viewSlots) in viewData">-->
<!--                        <td>{{viewDays}}</td>-->
<!--                        <td>{{viewSlots}}</td>-->
<!--                    </tr><tr data-ng-repeat="(viewDays,viewSlots) in viewData">-->
<!--                        <td>{{viewDays}}</td>-->
<!--                        <td>{{viewSlots}}</td>-->
<!--                    </tr>-->
<!--                </table>-->

<!--            </div>-->

<!--        </div>-->
<!--    </div>-->
<!--</div>-->

<!--image view modal-->
<div class="modal fade" id="displayImageModal" style="z-index: 9999;" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document" style="width: 700px;">
        <div class="modal-content">
            <div class="frame" style="margin-top: 40px;margin: auto;">
                <img style="    max-height: 70vh;max-width: 70vw;"
                     data-ng-src="{{imageSrc}}"/>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="confirmationModal" style="z-index: 9999;" tabindex="-1" role="dialog"
    aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document" style="width: 80%;">
        <div class="modal-content"
             style="padding: 2%;">
            <h4 style="text-align: center; margin-bottom: 10px;">Are you sure you want to change the Menu Status?</h4>
            <div style="display: flex; justify-content: center;">
                <input type="button" class="btn btn-sm btn-primary" value="Yes"
                style="margin-right: 10px;"
                data-ng-click="changeMenuStatus()">
                <input type="button" class="btn btn-sm btn-primary" value="No" 
                style="background-color: white; color: #337ab7"
                data-ng-click="closeConfirmationModal()">
            </div>
        </div>
    </div>
</div>
