<!--
  ~ Created By Shanmukh
  -->

<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div
        class="row"
        ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            User Policy Management
        </h1>
    </div>
</div>
<div class="row">
    <div class="col-lg-8">
        <input
                type="text"
                data-ng-model="searchPolicy"
                data-ng-change="setSearchPolicy(searchPolicy)"
                placeholder="Enter Policy Name to Search"
                class="form-control"/>
    </div>
    <div class="col-lg-4">
        <button
                class="btn btn-primary pull-right"
                ng-click="openPolicyRoles(null, 'ADD')">
            <i class="fa fa-plus fw"></i> Add User Policy
        </button>
    </div>
</div>
<div class="row">
    <br>
    <div
            class="col-xs-12"
            ng-if="userPolicies.length > 0">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-striped table-bordered">
                    <thead>
                    <th>Policy Id</th>
                    <th>Policy Name</th>
                    <th>Policy Desc</th>
                    <th>Created By</th>
                    <th>Created At</th>
                    <th>Actions</th>
                    </thead>
                    <tbody>
                    <tr ng-repeat="policy in userPolicies | filter:searchPolicy">
                        <td>{{policy.userPolicyId}}</td>
                        <td>{{policy.policyName}}</td>
                        <td>{{policy.policyDescription}}</td>
                        <td>{{policy.createdBy}}</td>
                        <td>{{policy.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                        <td align="left"><img
                                ng-click="openPolicyRoles(policy, 'VIEW')"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="View Policy"
                                ng-src="img/viewEye.png"
                                height="25px"
                                width="25px"> &nbsp;&nbsp;
                            <img
                                    ng-click="openPolicyRoles(policy, 'EDIT')"
                                    style="margin-bottom: 8px; cursor: pointer"
                                    title="Edit Policy"
                                    ng-src="img/change.png"
                                    height="20px"
                                    width="20px"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div data-ng-if="userPolicies.length == 0" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No Policies found..!</div>
</div>
</div>

<div
        class="modal previewModal fade"
        id="editPolicyRolesModal"
        role="dialog"
        aria-labelledby="editPolicyRolesModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="col-lg-8">
                    <h4 class="modal-title" id="editPolicyRolesModalLabel">{{currentAction}} Policy Roles -
                        {{currentSelectedPolicy.policyName}}</h4>
                </div>
                <div class="col-lg-3" data-ng-if="currentAction != 'ADD'">
                    <button class="btn btn-small" data-ng-if="currentViewType != 'ALL_ROLES_SCREEN'"
                            ng-click="setCurrentViewType('previous')">Previous
                    </button>
                    <button class="btn btn-small" data-ng-if="currentViewType != 'PREVIEW_ROLES_SCREEN'"
                            ng-click="setCurrentViewType('next')">Next
                    </button>
                </div>
                <div class="col-lg-3" data-ng-if="currentAction == 'ADD'">
                    <button class="btn btn-small" data-ng-if="currentViewType != 'ADD_POLICY'"
                            ng-click="setCurrentViewType('previous')">Previous
                    </button>
                    <button class="btn btn-small" data-ng-if="currentViewType != 'PREVIEW_ROLES_SCREEN'"
                            ng-click="setCurrentViewType('next')">Next
                    </button>
                </div>
                <div class="col-lg-1 pull-right">
                    <button type="button"
                            class="close"
                            data-dismiss="modal"
                            data-ng-click="closeModal()"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
            <form name="myForm">
                <div class="modal-body" data-ng-if="currentViewType == 'ADD_POLICY'">
                    <br>
                    <div class="form-group">
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="col-xs-4">
                                    <label class="control-label">Select Department : </label>
                                </div>
                                <div class="col-xs-8 form-group">
                                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                                            id="selectedDepartment"
                                            data-ng-model="selectedDepartment" data-placeholder="Select a Department"
                                            data-ng-change="setSelectedDepartment(selectedDepartment)">
                                        <option value=""></option>
                                        <option data-ng-repeat="dept in departmentList" value="{{dept}}">
                                            {{dept.name + " - " + dept.id}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="col-xs-4">
                                    <label class="control-label">Select Designation : </label>
                                </div>
                                <div class="col-xs-8 form-group">
                                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                                            id="selectedDesignation"
                                            data-ng-model="selectedDesignation" data-placeholder="Select a Designation"
                                            data-ng-change="setSelectedDesignation(selectedDesignation)">
                                        <option value=""></option>
                                        <option data-ng-repeat="desg in designationList" value="{{desg}}">
                                            {{desg.name + " - " + desg.id}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                            <div class="col-xs-12">
                                <div class="row">
                                    <div class="col-xs-4">
                                        <label class="control-label">Policy Name*(DepartmentName_DesignationName) : </label>
                                    </div>
                                    <div class="col-xs-8 form-group">
                                        <input type="text" class="form-control" data-ng-model="currentSelectedPolicy.policyName"
                                               placeholder="Select Department and Designation"
                                               data-ng-model-options="{ allowInvalid: true }"
                                               data-ng-disabled="true"
                                               data-ng-change="setPolicyName(currentSelectedPolicy.policyName)" required>
                                    </div>
                                </div>
                            </div>
                    </div>
                    <div class="form-group">
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="col-xs-4">
                                    <label for="policyDescription" class="control-label">Policy Description* : </label>
                                </div>
                                <div class="col-xs-8 form-group">
                                    <textarea id="policyDescription" class="form-control"
                                              placeholder="Enter Policy Description"
                                              data-ng-model="currentSelectedPolicy.policyDescription"
                                              data-ng-change="setPolicyDescription(currentSelectedPolicy.policyDescription)"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-body" data-ng-if="currentViewType != 'ADD_POLICY'">
                    <div class=form-group">
                        <input type="text" id="filterBySearch" data-ng-model="filterBySearch" data-ng-change="setFilterRole(filterBySearch)" placeholder="Filter Roles " class="form-control"/>
                    </div>
                    <div class="form-group">
                        <br>
                        <table class="table table-striped table-bordered" data-ng-if="finalRows.length > 0">
                            <thead>
                            <tr>
                                <th>Check</th>
                                <th>Role name</th>
                                <th>Role Description</th>
                                <th>Role Application</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-style="role.status == 'ACTIVE' ?
                                                {'background-color':'green' , 'color':'white'} : ''"
                                style="cursor: pointer;"
                                data-ng-disabled="currentAction == 'VIEW' || currentViewType == 'PREVIEW_ROLES_SCREEN'"
                                ng-repeat-start="role in finalRows | filter:filterBySearch">
                                <td data-ng-disabled="currentAction == 'VIEW'">
                                    <input type="checkbox" data-ng-checked="role.status == 'ACTIVE'"
                                           data-ng-disabled="currentAction == 'VIEW' || currentViewType == 'PREVIEW_ROLES_SCREEN'"
                                           data-ng-click="changeRoleStatus(role)"
                                           ng-true-value="'ACTIVE'" ng-false-value="'IN_ACTIVE'"/>
                                </td>
                                <td data-ng-disabled="currentAction == 'VIEW'">{{role.code}}</td>
                                <td data-ng-disabled="currentAction == 'VIEW'">{{role.name}}</td>
                                <td data-ng-disabled="currentAction == 'VIEW'">{{role.applicationName}}</td>
                                <td data-ng-show="role.roleActions != null && role.roleActions.length > 0">
                                    <button class="btn btn-medium yellowBg" style="color: black"
                                            data-ng-click="showActionsForRole(role)">
                                        {{role.expanded ? "Hide Actions" : "Show Actions"}}
                                    </button>
                                </td>
                                <td data-ng-show="role.roleActions == null || role.roleActions.length == 0">
                                    No Actions
                                </td>
                            </tr>
                            <tr data-ng-if="role.expanded && role.roleActions.length > 0 && currentSelectedRole == role.roleId"
                                ng-repeat-end>
                                <td colspan="10">
                                    <div
                                            class="row"
                                            id="gridViewId">
                                        <div
                                                class="col-lg-12">
                                            <div
                                                    id="categoryGrid"
                                                    data-ui-grid="categoryGridOptions"
                                                    ui-grid-save-state=""
                                                    ui-grid-resize-columns
                                                    ui-grid-move-columns
                                                    class="grid col-lg-12"
                                                    data-ng-if="role.expanded && role.roleActions.length > 0 && currentSelectedRole == role.roleId"
                                            ></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="row"  data-ng-if="currentAction != 'VIEW' && currentViewType == 'PREVIEW_ROLES_SCREEN' && isDocRequired == true">
                        <div class="col-lg-3">
                            <input class="btn btn-primary" style="margin-top: 5px" type="file" file-model="fileToUpload" accept="">
                        </div>
                        <div class="col-lg-3">
                            <button class="btn btn-primary" style="margin-top: 5px"
                                    data-ng-click="uploadPolicyEditProof()"
                                    data-ng-if="fileToUpload != null">Upload Policy Proof
                            </button>
                        </div>
                        <div class="col-lg-3 pull-right" data-ng-if="uploadedDoc != null">
                            <button class="btn btn-primary pull-right" ng-click="submitUpdatePolicyRoles()">
                                {{currentAction == 'ADD' ? "Add Policy" : "Update Policy"}}
                            </button>
                        </div>
                    </div>
                    <div class="row"  data-ng-if="currentAction != 'VIEW' && currentViewType == 'PREVIEW_ROLES_SCREEN' && isDocRequired == false">
                        <div class="col-lg-3 pull-right">
                            <button class="btn btn-primary pull-right" ng-click="submitUpdatePolicyRoles()">
                                {{currentAction == 'ADD' ? "Add Policy" : "Update Policy"}}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
