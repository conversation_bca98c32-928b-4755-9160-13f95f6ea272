<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    :root {
        --added: #d4edda;
        --modified: #fff3cd;
        --dropped: #f8d7da;
        --cloned: #cce5ff; /* Light blue for cloned items */
    }

    .tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 80px;
        max-width: 150px;
        text-align: center;
        padding: 5px 10px;
        border-radius: 999px;
        color: black;
        margin-right: 10px;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .cloned-unit {
        background-color: var(--cloned);
        border-radius: 4px;
        padding: 2px 5px;
        margin-bottom: 2px;
    }

</style>
<div
        class="row"
        ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Employee Dashboard
            <button acl-action="ADMN_EMP_ADD"
                    class="btn btn-primary pull-right"
                    data-toggle="modal"
                    id="addEmployeeIdDiv"
                    ng-click="addEmployee()">
                <i class="fa fa-plus fw"></i> Add Employee
            </button>
            <button acl-action="ADMN_EMP_ADD"
                    class="btn btn-primary pull-right"
                    data-toggle="modal"
                    id="addEmployeeBulki"
                    ng-click="addBulkEmployee()"
                    style="margin-right: 10px">
                <i class="fa fa-plus fw"></i> Add Bulk Employee
            </button>
            <button data-ng-if="hasSyncEmpAccess"
                    class="btn btn-primary pull-right"
                    data-toggle="modal"
                    id="syncEmployee"
                    ng-click="syncEmployee()"
                    style="margin-right: 5px;margin-left: 5px;">
                <i class="fa fa-refresh" style="color: #white;" aria-hidden="true"></i> Sync Employee
            </button>
        </h1>
    </div>
</div>
<div class="row">
    <div class="col-lg-4">
        Filter: <input
            type="text"
            ng-model="search"
            ng-change="filter()"
            placeholder="Filter"
            class="form-control"/>
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page: <select
            ng-model="entryLimit"
            class="form-control">
        <option value="5">5</option>
        <option value="10">10</option>
        <option value="20">20</option>
        <option value="50">50</option>
        <option value="100">100</option>
        <option value="200">200</option>
        <option value="500">500</option>
        <option value="1000">1000</option>
        <option value="2000">2000</option>
    </select>
    </div>
</div>
<div class="row">
    <button
            class="btn btn-primary pull-right"
            style="margin-top: 10px"
            ng-click='downloadCSV({ filename: "employee-data.csv" });'>Download
        CSV
    </button>
    <div
            class="col-xs-12"
            ng-if="filteredItems > 0">
        <p>Filtered {{ filtered.length }} of {{ totalItems}} total
            results</p>
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-striped table-bordered">
                    <thead>
                    <th>ID &nbsp;<a ng-click="sort_by('id');"><i
                            class="glyphicon glyphicon-sort"></i></a></th>
                    <th>Employee Name&nbsp;<a ng-click="sort_by('name');"><i
                            class="glyphicon glyphicon-sort"></i></a></th>
                    <th>Status&nbsp;<a ng-click="sort_by('status');"><i
                            class="glyphicon glyphicon-sort"></i></a></th>
                    <th>Department&nbsp;<a ng-click="sort_by('departmentName');"><i
                            class="glyphicon glyphicon-sort"></i></a></th>
                    <th>Designation&nbsp;<a ng-click="sort_by('designation');"><i
                            class="glyphicon glyphicon-sort"></i></a></th>
                    <th>Employee Code&nbsp;<a ng-click="sort_by('employeeCode');"><i
                            class="glyphicon glyphicon-sort"></i></a></th>
                    <th align="center">Action&nbsp;</th>
                    </thead>
                    <tbody>
                    <tr
                            ng-repeat="employee in filtered = (employeeList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                        <td>{{employee.id}}</td>
                        <td>{{employee.name}}</td>
                        <td>{{employee.status}}</td>
                        <td>{{employee.departmentName}}</td>
                        <td>{{employee.designation}}</td>
                        <td>{{employee.employeeCode}}</td>
                        <td align="left"><img
                                ng-click="viewEmployee(employee)"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="View Employee"
                                ng-src="img/viewEye.png"
                                height="25px"
                                width="25px"> &nbsp;&nbsp; <img
                                ng-if="isHR()"
                                ng-click="updateEmployee(employee)"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="Update Employee"
                                ng-src="img/updateUser.png"
                                height="25px"
                                width="25px">&nbsp;&nbsp; <img
                                ng-click="editEmpMapping(employee)"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="Edit Mapping"
                                ng-src="img/change.png"
                                height="20px"
                                width="20px">&nbsp;&nbsp; <img
                                ng-click="activateEmployee(employee)"
                                ng-if="employee.status!='ACTIVE' && empActivateAccess()"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="Activate Employee"
                                ng-src="img/activeEmp.png"
                                height="25px"
                                width="25px">&nbsp;&nbsp; <img
                                ng-click="resetPassword(employee.id)"
                                ng-if="employee.status!='IN_ACTIVE'"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="Reset Password"
                                ng-src="img/resetPass.png"
                                height="25px"
                                width="25px"><img acl-action="ADMN_EMP_ROLE"
                                ng-click="updateRoles(employee)"
                                ng-if="employee.status!='IN_ACTIVE' && isHR()"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="Update Roles"
                                ng-src="img/roleChange.png"
                                height="25px"
                                width="25px"> <img
                                ng-click="inActiveEmployee(employee.id)"
                                style="margin-bottom: 8px; cursor: pointer"
                                title="DeActivate Employee"
                                ng-if="employee.status=='ACTIVE' && isHR()"
                                ng-src="img/inactiveEmployee.png"
                                height="25px"
                                width="25px"></td>
                    </tr>
                    </tbody>
                </table>
                <div align="center"><b>
                    <uib-pagination total-items="filtered.length" ng-model="currentPage" max-size="5"
                                    boundary-link-numbers="true" ng-disabled="false" rotate="true"
                                    items-per-page="entryLimit" class="pagination-sm"></uib-pagination>
                </b></div>
            </div>
        </div>
            <div
                    class="col-lg-10"
                    ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div
        class="modal fade"
        id="employeeModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myModalLabel">{{action}} Employee</h4>
            </div>
            <div class="modal-body">
                <form
                        name="addEmployeeForm"
                        novalidate>
                    <div class="form-group">
                        <label>Employee Name *</label>
                        <input
                            type="text"
                            class="form-control"
                            ng-model="empName"
                            required/>
                    </div>
                    <div class="form-group">
                        <label>Gender *</label> <select
                            class="form-control"
                            ng-model="empGender"
                            required>
                        <option value="M">Male</option>
                        <option value="F">Female</option>
                    </select>
                    </div>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <b>Employee Address </b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div class="form-group">
                                    <label>line 1 *</label> <input
                                        class="form-control"
                                        ng-model="currentAddress.line1"
                                        required/>
                                </div>
                                <div class="form-group">
                                    <label>line 2</label> <input
                                        class="form-control"
                                        ng-model="currentAddress.line2"/>
                                </div>
                                <div class="form-group">
                                    <label>line 3</label> <input
                                        class="form-control"
                                        ng-model="currentAddress.line3"/>
                                </div>
                                <div class="form-group">
                                    <label>City *</label> <input
                                        class="form-control"
                                        ng-model="currentAddress.city"/>
                                </div>
                                <div class="form-group">
                                    <label>State *</label> <select
                                        class="form-control"
                                        ng-model="currentAddress.state"
                                        required>
                                    <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                                    <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                    <option value="Assam">Assam</option>
                                    <option value="Bihar">Bihar</option>
                                    <option value="Chandigarh">Chandigarh</option>
                                    <option value="Chhattisgarh">Chhattisgarh</option>
                                    <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                    <option value="Daman and Diu">Daman and Diu</option>
                                    <option value="Delhi">Delhi</option>
                                    <option value="Goa">Goa</option>
                                    <option value="Gujarat">Gujarat</option>
                                    <option value="Haryana">Haryana</option>
                                    <option value="Himachal Pradesh">Himachal Pradesh</option>
                                    <option value="Jammu and Kashmir">Jammu and Kashmir</option>
                                    <option value="Jharkhand">Jharkhand</option>
                                    <option value="Karnataka">Karnataka</option>
                                    <option value="Kerala">Kerala</option>
                                    <option value="Lakshadweep">Lakshadweep</option>
                                    <option value="Madhya Pradesh">Madhya Pradesh</option>
                                    <option value="Maharashtra">Maharashtra</option>
                                    <option value="Manipur">Manipur</option>
                                    <option value="Meghalaya">Meghalaya</option>
                                    <option value="Mizoram">Mizoram</option>
                                    <option value="Nagaland">Nagaland</option>
                                    <option value="Odisha">Odisha</option>
                                    <option value="Puducherry">Puducherry</option>
                                    <option value="Punjab">Punjab</option>
                                    <option value="Rajasthan">Rajasthan</option>
                                    <option value="Sikkim">Sikkim</option>
                                    <option value="Tamil Nadu">Tamil Nadu</option>
                                    <option value="Telangana">Telangana</option>
                                    <option value="Tripura">Tripura</option>
                                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                                    <option value="Uttarakhand">Uttarakhand</option>
                                    <option value="West Bengal">West Bengal</option>
                                </select>
                                </div>
                                <div class="form-group">
                                    <label>Zip code *</label> <input
                                        class="form-control"
                                        type="number"
                                        my-maxlength="6"
                                        string-to-number
                                        ng-model="currentAddress.zipCode"/>
                                </div>
                                <div class="form-group">
                                    <label>Contact 1 *</label> <input
                                        class="form-control"
                                        type="number"
                                        my-maxlength="10"
                                        string-to-number
                                        ng-model="currentAddress.contact1"/>
                                </div>
                                <div class="form-group">
                                    <label>Contact 2</label> <input
                                        class="form-control"
                                        type="number"
                                        my-maxlength="10"
                                        string-to-number
                                        ng-model="currentAddress.contact2"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div
                        class="form-group"
                        ng-if="showPermanentAddress">
                        <label>Permanent Address</label>
                        <div>As Above</div>
                        <div class="radio">
                            <label><input
                                type="radio"
                                ng-model="permanentAddressSame"
                                ng-change="updatePerm('true')"
                                ng-value="true">Yes</label>
                        </div>
                        <div class="radio">
                            <label><input
                                type="radio"
                                ng-model="permanentAddressSame"
                                ng-change="updatePerm('false')"
                                ng-value="false">No</label>
                        </div>
                    </div> -->
                    <!-- <div
                        class="panel panel-default"
                        ng-if="!permanentAddressSame && showPermanentAddress">
                        <div class="panel-heading">
                            <b>Permanent Address</b>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <div class="form-group">
                                    <label>line 1 *</label> <input
                                        class="form-control"
                                        ng-model="permanentAddress.line1"
                                        required />
                                </div>
                                <div class="form-group">
                                    <label>line 2</label> <input
                                        class="form-control"
                                        ng-model="permanentAddress.line2" />
                                </div>
                                <div class="form-group">
                                    <label>line 3</label> <input
                                        class="form-control"
                                        ng-model="permanentAddress.line3" />
                                </div>
                                <div class="form-group">
                                    <label>City *</label> <input
                                        class="form-control"
                                        ng-model="permanentAddress.city" />
                                </div>
                                <div class="form-group">
                                    <label>State *</label> <select
                                        class="form-control"
                                        ng-model="permanentAddress.state"
                                        required>
                                        <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                        <option value="Andhra Pradesh">Andhra Pradesh</option>
                                        <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                        <option value="Assam">Assam</option>
                                        <option value="Bihar">Bihar</option>
                                        <option value="Chandigarh">Chandigarh</option>
                                        <option value="Chhattisgarh">Chhattisgarh</option>
                                        <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                        <option value="Daman and Diu">Daman and Diu</option>
                                        <option value="Delhi">Delhi</option>
                                        <option value="Goa">Goa</option>
                                        <option value="Gujarat">Gujarat</option>
                                        <option value="Haryana">Haryana</option>
                                        <option value="Himachal Pradesh">Himachal Pradesh</option>
                                        <option value="Jammu and Kashmir">Jammu and Kashmir</option>
                                        <option value="Jharkhand">Jharkhand</option>
                                        <option value="Karnataka">Karnataka</option>
                                        <option value="Kerala">Kerala</option>
                                        <option value="Lakshadweep">Lakshadweep</option>
                                        <option value="Madhya Pradesh">Madhya Pradesh</option>
                                        <option value="Maharashtra">Maharashtra</option>
                                        <option value="Manipur">Manipur</option>
                                        <option value="Meghalaya">Meghalaya</option>
                                        <option value="Mizoram">Mizoram</option>
                                        <option value="Nagaland">Nagaland</option>
                                        <option value="Odisha">Odisha</option>
                                        <option value="Puducherry">Puducherry</option>
                                        <option value="Punjab">Punjab</option>
                                        <option value="Rajasthan">Rajasthan</option>
                                        <option value="Sikkim">Sikkim</option>
                                        <option value="Tamil Nadu">Tamil Nadu</option>
                                        <option value="Telangana">Telangana</option>
                                        <option value="Tripura">Tripura</option>
                                        <option value="Uttar Pradesh">Uttar Pradesh</option>
                                        <option value="Uttarakhand">Uttarakhand</option>
                                        <option value="West Bengal">West Bengal</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Zip code *</label> <input
                                        class="form-control"
                                        type="number"
                                        my-maxlength="6"
                                        string-to-number
                                        ng-model="permanentAddress.zipCode" />
                                </div>
                                <div class="form-group">
                                    <label>Contact 1 *</label> <input
                                        class="form-control"
                                        type="number"
                                        my-maxlength="10"
                                        string-to-number
                                        ng-model="permanentAddress.contact1" />
                                </div>
                                <div class="form-group">
                                    <label>Contact 2</label> <input
                                        class="form-control"
                                        type="number"
                                        my-maxlength="10"
                                        string-to-number
                                        ng-model="permanentAddress.contact2" />
                                </div>
                            </div>
                        </div>
                    </div> -->
                    <div class="form-group">
                        <label>Department *</label> <select
                            class="form-control"
                            data-ng-model="selectedDepartment"
                            data-ng-change="changeEligibility(false)"
                            data-ng-options="department as department.name for department in departmentlist track by department.id"
                            required>
                    </select>
                    </div>
                    <div
                            class="form-group panel panel-default"
                            data-ng-if="selectedDepartment.id == 101 || selectedDepartment.id == 114 || selectedDepartment.id == 126 ||
                            selectedDepartment.id == 112 || selectedDepartment.id == 143  || selectedDepartment.id == 106 || selectedDepartment.id == 136 ">
                        <div class="panel-heading">
                            <b>Eligible for Employee Meal</b>
                        </div>
                        <div class="row panel-body">
                            <div class="col-xs-6 text-center">
                                <label class="btn btn-default"> <input
                                        type="radio"
                                        data-ng-model="eligible"
                                        data-ng-change="changeEligibility(eligible)"
                                        data-ng-value="true"/>Yes
                                </label>
                            </div>
                            <div class="col-xs-6 text-center">
                                <label class="btn btn-default"> <input
                                        type="radio"
                                        data-ng-model="eligible"
                                        data-ng-change="changeEligibility(eligible)"
                                        data-ng-value="false"/>No
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Company *</label> <select
                            class="form-control"
                            data-ng-model="selectedCompany"
                            data-ng-options="company as company.name for company in companiesList track by company.id"
                            required>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Email </label> <input
                            class="form-control"
                            type="text"
                            ng-model="employeeEmail"/>
                    </div>
                    <div class="form-group">
                        <label>Designation *</label> <select
                            class="form-control"
                            ng-model="selectedDesignation"
                            ng-options="designation as designation.name for designation in selectedDepartment.designations track by designation.id"
                            required>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Employment Type *</label> <select
                            class="form-control"
                            ng-model="employmentType"
                            required>
                        <option value="FULL_TIME">FULL TIME</option>
                        <option value="PART_TIME">PART TIME</option>
                    </select>
                    </div>
                    <div
                            class="form-group"
                            ng-if="action=='View'">
                        <label>Employment Status *</label> <select
                            class="form-control"
                            ng-model="employmentStatus"
                            required>
                        <option value="ACTIVE">ACTIVE</option>
                        <option value="IN_ACTIVE">IN-ACTIVE</option>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Biometrics Identifier </label> <input
                            type="text"
                            class="form-control"
                            ng-model="biometricId"/>
                    </div>
                    <div class="form-group">
                        <label>Joining Date *</label>
                        <div
                                class="datepicker"
                                data-date-format="yyyy-MM-dd"
                                data-date-min-limit="{{subscriptionMinDate}}">
                            <input
                                    class="form-control"
                                    ng-model="joiningDate"
                                    type="text"
                                    ng-readonly="true"
                                    placeholder="yyyy-mm-dd"
                                    required/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Reporting Manager *</label> <select
                            class="form-control"
                            ng-model="selectedReportingManager"
                            ng-options="reportingManager as reportingManager.name + ' - ' + reportingManager.employeeCode for reportingManager in ListManager | orderBy:'name' track by reportingManager.id"
                            required>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Employee Code *</label> <input
                            type="text"
                            class="form-control"
                            ng-model="employeeCode"/>
                    </div>
                    <div class="form-group">
                        <label>Slack ID </label> <input
                            type="text"
                            class="form-control"
                            ng-model="slackId"/>
                    </div>

                    <div class="form-group">
                        <label>Date of Birth</label>
                        <div class="datepicker" data-date-format="yyyy-MM-dd" >
                            <input class="form-control" data-ng-model="dateOfBirth"
                                   type="text" placeholder="yyyy-MM-dd"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Reason for Termination</label> <input
                            type="text"
                            class="form-control"
                            ng-model="terminationReason"/>
                    </div>

                    <div class="form-group">
                        <label>HR Executive</label> <input
                            type="text"
                            class="form-control"
                            ng-model="hrExecutive"/>
                    </div>

                    <div class="form-group">
                        <label>Leave Approval Authority</label> <input
                            type="text"
                            class="form-control"
                            ng-model="leaveApprovalAuthority"/>
                    </div>

                    <div class="form-group">
                        <label>Location Code</label> <input
                            type="text"
                            class="form-control"
                            ng-model="locationCode"
                            required/>
                    </div>

                    <div class="form-group">
                        <label>Application Mapping Type </label> <select
                            class="form-control"
                            ng-model="selectedMappingType"
                            >
                        <option value="OVER_ALL">OVER ALL</option>
                        <option value="REGION">REGION</option>
                        <option value="CITY">CITY</option>
                    </select>
                    </div>

                    <div class="form-group" ng-if="selectedMappingType =='REGION'">
                        <label> Region *
                            <span>
                           </span>
                        </label>
                        <div>
                            <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="regionlist"
                                 selected-model="selectedRegion"
                                 extra-settings="multiSelectSettings"></div>
                        </div>
                    </div>

                    <div class="form-group" ng-if="selectedMappingType=='CITY' ">
                        <label> City *
                            <span>
                         </span>
                        </label>
<!--                        <div>-->
                            <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="citylist"
                                 selected-model="selectedCity"
                                 extra-settings="multiSelectSettings"></div>

                           </div>

                    <div
                            class="form-group clearfix"
                            ng-if="action=='Add'"
                            ng-hide="showEmpBtn">
                        <button acl-action="ADMN_EMP_ADD"
                                class="btn btn-primary pull-right"
                                ng-click="submitAddEmployee()">Add employee
                        </button>
                        <loading align="center" />
                    </div>
                    <div
                            class="form-group clearfix"
                            ng-if="action=='UpdateEmp'">
                        <button
                                class="btn btn-primary pull-right"
                                ng-click="submitUpdateEmployee(updatedEmployee)">Update
                            employee
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div
        class="modal fade"
        id="employeeBulkModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">{{action}} Employee</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="display: flex;justify-content: center;margin-bottom: 10px;">
                    <button
                            class="btn btn-primary pull-right"
                            data-toggle="modal"
                            id="downloadOnboardingSheet"
                            ng-click="downloadOnboardingSheet()">
                        <i class="fa fa-download fw"></i> Download Onboarding Sheet
                    </button>
                </div>
                <div class="row">
                    <div class="col-xs-6">Upload Employee Onboarding Sheet</div>
                    <div class="col-xs-6">
                        <input class="btn btn-default" style="width: 100%;" type="file"
                               file-model="fileToUpload" accept="">
                        <button class="btn btn-primary" style="margin-top: 5px"
                                data-ng-click="uploadEmployeeSheet()"
                                data-ng-disabled="fileToUpload==null">Upload Onboarding Sheet
                        </button>

                    </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- -- Reset Password Modal -->
<div
        class="modal fade"
        id="resetPasswordModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="resetPasswordLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="resetPasswordLabel">{{action}} Password</h4>
            </div>
            <form name="myForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label>New Password *</label> <input
                            type="password"
                            class="form-control"
                            id="pw1"
                            name="pw1"
                            ng-model="pw1"
                            ng-required/>
                    </div>
                    <div class="form-group">
                        <label>Confirm Password *</label> <input
                            type="password"
                            class="form-control"
                            id="pw2"
                            name="pw2"
                            ng-model="pw2"/>
                    </div>
                    <div
                            class="msg-block"
                            ng-if="actionPwd==false">
                        <span class="msg-error">Passwords don't match.</span>
                    </div>
                    <div
                            class="form-group clearfix"
                            ng-if="action=='Reset'">
                        <button
                                class="btn btn-primary pull-right"
                                ng-click="submitResetPassword(idss)">Update
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div
    class="modal previewModal fade"
    id="editRolesModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="editRolesModalLabel">
    <div
        class="modal-dialog"
        role="document">
        <div class="modal-content" data-ng-show="modalTabIndex === 0">
            <div class="modal-header" style="height: 200px;">
                <button
                    type="button"
                    class="close"
                    aria-label="Close"
                    data-ng-click="onCloseModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="editRolesModalLabel">
                    Add / Edit Roles
                    <span data-ng-if="autoSelectedPolicy != null">Applied Policy - {{autoSelectedPolicy.policyName}}</span>
                    <span data-ng-if="appliedPolicy != null">Applied Policy - {{appliedPolicy.policyName}}</span>
                </h4>
            </div>
            <form name="myForm">
                <div class="modal-body">
                    <div class="form-group" style="display: flex; justify-content: space-between;">
                        <div style="display: flex; width: 60%;">
                            <p>Filter:&nbsp;</p>
                            <input type="text" ng-model="role" placeholder="Filter" class="form-control"/>
                        </div>
                        <div class="input-group" style="width: 30%; margin-bottom: 20px;">
                            <span class="input-group-addon">Filter Available Roles by Brand&nbsp;</span>
                            <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsForBrands"
                                 options="brands" selected-model="selectedBrandsForFiltering" style="cursor: pointer;"
                                 events="{ onSelectionChanged: filterRolesByBrand }">
                            </div>
                        </div>
                    </div>
                    <div class="form-inline" style="display: flex; align-items: center; margin-top: 10px;">
                        <label style="font-weight: normal; margin-right: 5px;">Clone other employee mapping:</label>

                        <!-- Searchable Dropdown -->
                        <select ui-select2="{allowClear:true, placeholder:'Search employee...', width:'300px'}"
                                class="form-control input-sm"
                                id="cloneEmployeeRoleDropdown"
                                ng-model="selectedEmployeeId"
                                ng-options="employee.id as (employee.name + ' (' + employee.id + ')') for employee in employeeList | filter:excludeCurrentEmployee">
                            <option value=""></option>
                        </select>

                        <!-- Clone Button -->
                        <button type="button" class="btn btn-primary btn-sm" style="margin-left: 10px;" ng-click="handleCloneRoleClick()" id="cloneRoleButton">
                            Clone
                        </button>
                    </div>
                    <div class="form-group" data-ng-if="activeEmployeeRoles.length > 0">
                        <label>Current Roles For Employee {{editRolesUserId}}</label>
                        <table class="table table-striped table-bordered">
                            <tr>
                                <th>Check</th>
                                <th>Role name</th>
                                <th>Role Description</th>
                                <th>Role Application</th>
                                <th>Actions</th>
                            </tr>
                            <tr style="cursor: pointer;" data-ng-style="role.status == 'ACTIVE' ?
                                                {'background-color':'green' , 'color':'white'} : ''"
                                ng-repeat-start="role in filteredRoles|filter:role | limitTo:numberPerPage:(curtPage - 1) * numberPerPage track by $index"
                                ng-class="{'cloned-unit': role.isCloned}">
                                <td>
                                    <input type="checkbox" data-ng-checked="role.status == 'ACTIVE'" data-ng-click="changeRoleStatus(role)"
                                           ng-true-value="'ACTIVE'" ng-false-value="'IN_ACTIVE'"/>
                                </td>
                                <td>
                                    {{role.code}}
                                    <div style="display: flex; margin-top: 10px;">
                                        <div data-ng-repeat="(brandId, brandName) in brandIdNameMap"
                                             data-ng-show="hasRoleType(role, brandId)"
                                             data-ng-click="role.status != 'IN_ACTIVE' && toggleBrandMapping(role, brandId)"
                                             data-ng-style="role.status == 'IN_ACTIVE' && {'cursor': 'not-allowed'}">

                                            <p data-ng-if="employeeRoleBrandMapping[role.id][brandId] === true"
                                               data-ng-style="{'background-color': brandColorMap[brandId], 'border': '2px solid ' + brandColorMap[brandId]}"
                                               class="tag">
                                                <i class="fa fa-check"></i>
                                                {{brandName}}
                                            </p>

                                            <p data-ng-if="employeeRoleBrandMapping[role.id][brandId] === false"
                                               data-ng-style="{'background-color': 'white', 'border': '2px solid ' + brandColorMap[brandId]}"
                                               class="tag">
                                                {{brandName}}
                                            </p>
                                        </div>
                                    </div>
                                </td>
                                <td>{{role.name}}</td>
                                <td>{{role.applicationName}}</td>
                                <td data-ng-show="role.roleActions != null && role.roleActions.length > 0">
                                    <button class="btn btn-medium yellowBg" style="color: black"
                                            data-ng-click="showActionsForRole(role)">
                                        {{role.expanded ? "Hide Actions" : "Show Actions"}}
                                    </button>
                                </td>
                                <td data-ng-show="role.roleActions == null || role.roleActions.length == 0">
                                    No Actions
                                </td>
                            </tr>
                            <tr data-ng-if="role.expanded && role.roleActions.length > 0 && currentSelectedRole == role.roleId"
                                ng-repeat-end>
                                <td colspan="10">
                                    <div
                                            class="row"
                                            id="gridViewId">
                                        <div
                                                class="col-lg-12">
                                            <div
                                                    id="categoryGrid"
                                                    data-ui-grid="categoryGridOptions"
                                                    ui-grid-save-state=""
                                                    ui-grid-resize-columns
                                                    ui-grid-move-columns
                                                    class="grid col-lg-12"
                                                    data-ng-if="role.expanded && role.roleActions.length > 0 && currentSelectedRole == role.roleId"
                                            ></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="form-group clearfix">
                        <div class="col-lg-3 pull-right" style="padding: 0px;">
                            <button class="btn btn-secondary pull-right" data-ng-click="moveForward()">
                                Next
                            </button>
                        </div>
                    </div>
                    <div align="center"><b>
                        <uib-pagination total-items="total" ng-model="curtPage" max-size="5"
                                        boundary-link-numbers="true" ng-disabled="false" rotate="true"
                                        items-per-page="numberPerPage" class="pagination-sm"></uib-pagination>
                    </b></div>
                </div>
            </form>
        </div>
        <div class="modal-content" data-ng-show="modalTabIndex === 1">
            <div class="modal-header" style="height: 200px;">
                <button
                    type="button"
                    class="close"
                    aria-label="Close"
                    data-ng-click="onCloseModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="editRolesModalLabel">
                    Review <b>Employee - Role - Brand Mapping</b> Changes
                </h4>
            </div>
            <div class="modal-body">
                <table class="table table-striped table-bordered">
                    <label>Reviewing Changes for Employee {{editRolesUserId}}</label>
                    <tr style="font-weight: 700;">
                        <td>Role ID</td>
                        <td>Role Name</td>
                        <td>Role Mapping Status</td>
                        <td>Mapped Brands</td>
                    </tr>
                    <tr data-ng-repeat="rbm in updatedEmployeeRoleMappings.roleBrandMappings | orderBy: 'roleId'"
                        data-ng-style="{
                            'background-color': rbm.status === 'IN_ACTIVE' ? 'var(--dropped)' :
                                                (rbm.status === 'ACTIVE' && userActiveRoles.includes(rbm.roleId)) ? 'var(--modified)' :
                                                'var(--added)'
                        }">
                        <td>{{rbm.roleId}}</td>
                        <td>{{rbm.roleName}}</td>
                        <td>{{rbm.status}}</td>
                        <td>{{convertSetToArray(rbm.brands).length > 0 ?
                            formatBrands(convertSetToArray(rbm.brands)) :
                            "-"}}
                        </td>
                    </tr>
                </table>
                <div>
                    <div style="display: flex; gap: 20px; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 20px; background-color: var(--added); border: 1px solid black; margin-right: 5px;"></div>
                            <span>Newly Added Role</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 20px; background-color: var(--modified); border: 1px solid black; margin-right: 5px;"></div>
                            <span>Updated Role</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 20px; background-color: var(--dropped); border: 1px solid black; margin-right: 5px;"></div>
                            <span>Dropped Role</span>
                        </div>
                    </div>
                </div>
                <div style="width: 100%; margin-top: 20px; display: flex; justify-content: space-between;">
                    <div style="width: 30%;">
                        <button class="btn btn-secondary" data-ng-click="moveBack()">
                            Previous
                        </button>
                    </div>
                    <div data-ng-if="isDocRequired" style="width: 60%; display: flex; justify-content: right;">
                        <input class="btn btn-primary" type="file" file-model="fileToUpload" accept="">
                        <button class="btn btn-primary"
                                data-ng-click="uploadRoleEditProof()"
                                data-ng-if="fileToUpload != null"
                                style="margin-left: 10px;">
                            Upload Role Edit Proof
                        </button>
                        <button data-ng-if="uploadedDoc != null"
                                class="btn btn-primary" ng-click="submitUpdateRolesV2()"
                                style="margin-left: 10px; background-color: rgb(0, 134, 0);">
                            Update Roles
                        </button>
                    </div>
                    <div data-ng-if="isDocRequired == false">
                        <button class="btn btn-primary" ng-click="submitUpdateRolesV2()" style="background-color: rgb(0, 134, 0)">
                            Update Roles
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- End edit role Modal -->
<!-- Modal -->
<div
        class="modal fade"
        id="employeeActivationModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog modal-lg"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myModalLabel">Select units where employee is to be
                    activated</h4>
                <div class="form-inline" style="display: flex; align-items: center; margin-top: 10px;">
                    <label style="font-weight: normal; margin-right: 5px;">Clone other employee mapping:</label>

                    <!-- Searchable Dropdown -->
                    <select ui-select2="select2Options"
                            class="form-control"
                            id="employeeDropdown"
                            ng-model="selectedEmployeeId"
                            ng-options="employee.id as (employee.name + ' (' + employee.id + ')') for employee in employeeList | filter:excludeCurrentEmployee">
                        <option value=""></option>
                    </select>

                    <button type="button" class="btn btn-primary btn-sm" style="margin-left: 10px;" ng-click="handleCloneClick()" id="cloneButton">
                        Clone
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div>
                    <!-- Nav tabs -->
                    <ul
                            class="nav nav-tabs"
                            role="tablist">
                        <li
                                role="presentation"
                                ng-class="{active: !tab1, disabled: tab1}"><a
                                data-target="#cafe"
                                aria-controls="cafe"
                                role="tab"
                                data-toggle="tab">Cafe</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab2, disabled: tab2}"><a
                                data-target="#cod"
                                aria-controls="cod"
                                role="tab"
                                data-toggle="tab">COD</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab3, disabled: tab3}"><a
                                data-target="#callcenter"
                                aria-controls="callcenter"
                                role="tab"
                                data-toggle="tab">Call Center</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab4, disabled: tab4}"><a
                                data-target="#callcenter"
                                aria-controls="callcenter"
                                role="tab"
                                data-toggle="tab">KITCHEN</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab5, disabled: tab5}"><a
                                data-target="#callcenter"
                                aria-controls="callcenter"
                                role="tab"
                                data-toggle="tab">WAREHOUSE</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab6, disabled: tab6}"><a
                                data-target="#callcenter"
                                aria-controls="callcenter"
                                role="tab"
                                data-toggle="tab">OFFICE</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab7, disabled: tab7}"><a
                                data-target="#callcenter"
                                aria-controls="callcenter"
                                role="tab"
                                data-toggle="tab">CHAI_MONK</a></li>
                        <li
                                role="presentation"
                                ng-class="{active: !tab8, disabled: tab8}"><a
                                data-target="#summary"
                                aria-controls="summary"
                                role="tab"
                                data-toggle="tab">View Summary</a></li>
                    </ul>
                    <!-- Tab panes -->
                    <div class="tab-content" style="padding: 15px;">
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab1}"
                                id="cafe">
                            <h3>Select Cafes</h3>
                            <div
                                    class="row col-xs-8 alert alert-danger"
                                    id="cafeValidID"
                                    style="display: none; margin-left: 15px">Unit Mapped add
                                Limit Exceeded.
                            </div>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allCafesCheck" ng-change="selectAllCafe()" /> Add all
                            </div>
                        </div> -->
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsCafes"
                                         options="cafelist"
                                         selected-model="storeSelectedCafes" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('cafe')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li ng-repeat="addedCafe in addedCafes track by addedCafe.id" ng-class="{'cloned-unit': addedCafe.isCloned}">
                                            {{addedCafe.name}} <span ng-if="addedCafe.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('cafe',addedCafe.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab2',addedCafes)">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab2}"
                                id="cod">
                            <h3>Select CODs</h3>
                            <div
                                    class="row col-xs-8 alert alert-danger"
                                    id="codValidID"
                                    style="display: none; margin-left: 15px">Mapping Employee
                                Unit Exceeded
                            </div>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allCODsCheck" ng-change="selectAllCOD()" /> Add all
                            </div>
                        </div> -->
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsCOD"
                                         options="codlist"
                                         selected-model="storeSelectedCOD" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('cod')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li ng-repeat="addedCOD in addedCODs track by addedCOD.id" ng-class="{'cloned-unit': addedCOD.isCloned}">
                                            {{addedCOD.name}} <span ng-if="addedCOD.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('cod',addedCOD.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab1')">Prev
                            </button>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab3')">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab3}"
                                id="callcenter">
                            <h3>Select Call Center</h3>
                            <div
                                    class="row col-xs-8 alert alert-danger"
                                    id="callCenterValidID"
                                    style="display: none; margin-left: 15px">Mapping Employee
                                Unit Exceeded
                            </div>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allCallCentersCheck" ng-change="selectAllCallCenter()" /> Add all
                            </div>
                        </div> -->
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsCallCenter"
                                         options="callcenterlist"
                                         selected-model="storeSelectedCallCenter" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('callCenter')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li
                                                ng-repeat="addedCallCenter in addedCallCenters track by addedCallCenter.id" ng-class="{'cloned-unit': addedCallCenter.isCloned}">
                                            {{addedCallCenter.name}} <span ng-if="addedCallCenter.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('callCenter',addedCallCenter.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab2')">Prev
                            </button>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab4')">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab4}"
                                id="kitchen">
                            <h3>Select Kitchen</h3>
                            <div
                                    class="row col-xs-8 alert alert-danger"
                                    id="kitchenValidID"
                                    style="display: none; margin-left: 15px">Unit Mapped add
                                Limit Exceeded.
                            </div>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allKitchensCheck" ng-change="selectAllKitchen()" /> Add all
                            </div>
                        </div> -->
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsKitchen"
                                         options="kitchenlist"
                                         selected-model="storeSelectedKitchen" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('kitchen')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li
                                                ng-repeat="addedKitchen in addedKitchens track by addedKitchen.id" ng-class="{'cloned-unit': addedKitchen.isCloned}">
                                            {{addedKitchen.name}} <span ng-if="addedKitchen.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('kitchen',addedKitchen.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab3')">Prev
                            </button>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab5')">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab5}"
                                id="warehouse">
                            <h3>Select Ware House</h3>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allWareHouseCheck" ng-change="selectAllWareHouse()" /> Add all
                            </div>
                        </div> -->
                            <div
                                    class="row col-xs-8 alert alert-danger"
                                    id="wareHouseValidID"
                                    style="display: none; margin-left: 15px">Unit Mapped add
                                Limit Exceeded.
                            </div>
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsWarehouse"
                                         options="warehouselist"
                                         selected-model="storeSelectedWarehouse" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('wareHouse')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li
                                                ng-repeat="addedWareHouses in addedWareHouse track by addedWareHouses.id" ng-class="{'cloned-unit': addedWareHouses.isCloned}">
                                            {{addedWareHouses.name}} <span ng-if="addedWareHouses.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('wareHouse',addedWareHouses.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab4')">Prev
                            </button>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab6')">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab6}"
                                id="office">
                            <h3>Select Office</h3>
                            <div
                                    class="row col-xs-8 alert alert-danger"
                                    id="officeValidID"
                                    style="display: none; margin-left: 15px">Unit Mapped add
                                Limit Exceeded.
                            </div>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allOfficeCheck" ng-change="selectAllOffice()" /> Add all
                            </div>
                        </div> -->
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsOffice"
                                         options="officelist"
                                         selected-model="storeSelectedOffice" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('office')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li
                                                ng-repeat="addedOfficeDataShow in addedOffices track by addedOfficeDataShow.id" ng-class="{'cloned-unit': addedOfficeDataShow.isCloned}">
                                            {{addedOfficeDataShow.name}} <span ng-if="addedOfficeDataShow.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('office',addedOfficeDataShow.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab5')">Prev
                            </button>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab7')">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab7}"
                                id="chaimonk">
                            <h3>Select Chai Monk</h3>
                            <div
                                    class="row"
                                    id="monkValidID"
                                    style="display: none">
                                <div
                                        class="col-xs-8 alert alert-danger"
                                        style="margin-left: 15px">You can not add more than 2
                                    Monk
                                </div>
                            </div>
                            <!-- <div class="row">
                            <div class="col-xs-12">
                                <input type="checkbox" ng-model="allchaiMonkCheck" ng-change="selectAllChaiMonk()" /> Add all
                            </div>
                        </div> -->
                            <div class="row">
                                <div class="col-xs-10">
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsChaiMonk"
                                         options="chaiMonkList"
                                         selected-model="storeSelectedChaiMonk" class="region-card">
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    <button
                                            class="btn btn-success"
                                            ng-click="addUnit('chaimonk')">
                                        <i class="fa fa-plus fw"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <ul>
                                        <li
                                                ng-repeat="addedChaiMonks in addedChaiMonk track by addedChaiMonks.id" ng-class="{'cloned-unit': addedChaiMonks.isCloned}">
                                            {{addedChaiMonks.name}} <span ng-if="addedChaiMonks.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                            <button
                                                    class="btn btn-danger btn-xs"
                                                    ng-click="removeUnit('chaimonk',addedChaiMonks.id)">
                                                <i class="fa fa-close fw"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab6')">Prev
                            </button>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab8')">Next
                            </button>
                        </div>
                        <div
                                role="tabpanel"
                                class="tab-pane"
                                ng-class="{active: !tab8}"
                                id="summary">
                            <div class="row">
                                <div class="col-xs-12">
                                    <h4 style="padding: 10px">
                                        <strong>Employee Name:</strong> {{emplyeeSelected.name}}
                                    </h4>
                                    <hr/>
                                    <div class="row">
                                        <div class="col-xs-4">
                                            <h4>Activated on Cafes:</h4>
                                            <ul>
                                                <li
                                                        ng-repeat="addedCafe in addedCafes track by addedCafe.id" ng-class="{'cloned-unit': addedCafe.isCloned}">
                                                    {{addedCafe.name}} <span ng-if="addedCafe.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-xs-4">
                                            <h4>Activated on CODs:</h4>
                                            <ul>
                                                <li ng-repeat="addedCOD in addedCODs track by addedCOD.id" ng-class="{'cloned-unit': addedCOD.isCloned}">
                                                    {{addedCOD.name}} <span ng-if="addedCOD.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-xs-4">
                                            <h4>Activated on Call Centers:</h4>
                                            <ul>
                                                <li
                                                        ng-repeat="addedCallCenter in addedCallCenters track by addedCallCenter.id" ng-class="{'cloned-unit': addedCallCenter.isCloned}">
                                                    {{addedCallCenter.name}} <span ng-if="addedCallCenter.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-xs-4">
                                            <h4>Activated on Kitchen</h4>
                                            <ul>
                                                <li
                                                        ng-repeat="addedKitchen in addedKitchens track by addedKitchen.id" ng-class="{'cloned-unit': addedKitchen.isCloned}">
                                                    {{addedKitchen.name}} <span ng-if="addedKitchen.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-xs-4">
                                            <h4>Activated on Warehouse</h4>
                                            <ul>
                                                <li
                                                        ng-repeat="addedWareHouseDatas in addedWareHouse track by addedWareHouseDatas.id" ng-class="{'cloned-unit': addedWareHouseDatas.isCloned}">
                                                    {{addedWareHouseDatas.name}} <span ng-if="addedWareHouseDatas.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-xs-4">
                                            <h4>Activated on Office</h4>
                                            <ul>
                                                <li
                                                        ng-repeat="addedOfficeDatas in addedOffices track by addedOfficeDatas.id" ng-class="{'cloned-unit': addedOfficeDatas.isCloned}">
                                                    {{addedOfficeDatas.name}} <span ng-if="addedOfficeDatas.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-xs-4">
                                            <h4>Activated on Chai Monk</h4>
                                            <ul>
                                                <li
                                                        ng-repeat="addedChaiMonkDatas in addedChaiMonk track by addedChaiMonkDatas.id" ng-class="{'cloned-unit': addedChaiMonkDatas.isCloned}">
                                                    {{addedChaiMonkDatas.name}} <span ng-if="addedChaiMonkDatas.isCloned" style="font-size: 11px; color: #0056b3;">(cloned)</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div
                                        class="col-xs-12"
                                        ng-if="action=='Activate'">
                                    <button
                                            class="btn btn-primary pull-right"
                                            ng-click="activateEmployeeSubmit()">Activate employee
                                    </button>
                                </div>
                                <div
                                        class="col-xs-12"
                                        ng-if="action=='Edit Mapping'">
                                    <button
                                            class="btn btn-primary pull-right"
                                            ng-click="editEmployeeMappingSubmit()">Update
                                        employee units
                                    </button>
                                </div>
                            </div>
                            <button
                                    class="btn btn-primary"
                                    type="button"
                                    ng-click="selectTab('tab7')">Prev
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
