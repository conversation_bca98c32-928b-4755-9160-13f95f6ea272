<style>
.popeye-modal-container .popeye-modal{
    width: 60%;
}
.alert-danger {
   margin-left: 15px!important;
}
</style>
<div class="row">
		<div class="col-lg-10">
			<h3>Iteration Action Modal </h3>
		</div>
		<div class="col-lg-2">
			<button type="button" class="btn btn-primary" data-ng-click="seeCost()">See COST</button>
		</div>
	</div>

<form name="actionForm" novalidate>
	<div class="row">
		<div class="row top-buffer-row">
			<div class="col-lg-3">
				<label>Iteration Name</label>
			</div>
			<div class="col-lg-3">{{iterationDetails.iterationName}}</div>
			<div class="col-lg-3">
				<label>Status</label>

			</div>
			<div class="col-lg-3">{{iterationDetails.status}}</div>
		</div>
		<div class="row top-buffer-row" data-ng-if="iterationDetails.linkedConstructName && action == 'APPROVED'">
			<div class="col-lg-6">
				<select  class="form-control"
					data-ng-model="selectedProduct"
					 data-ng-options="product as product.productName for product in scmProductsInfo | filter : filterByCategory" >
				</select>
			</div>
			<div class="col-lg-3">
				<Button class="btn btn-primary"
					data-ng-click="changeProduct(selectedProduct)">Link
					Product</Button>
			</div>
		</div>
		<div class="row top-buffer-row" data-ng-if="iterationDetails.linkedProductId">
		<div class="col-lg-3">
				<label>Product Name</label>
			</div>
			<div class="col-lg-3">{{iterationDetails.linkedProductId}}</div>
			<div class="col-lg-3">
				<label>Product Id</label>

			</div>
			<div class="col-lg-3">{{iterationDetails.linkedProductName}}</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-3">
				<label>Profile Name</label>
			</div>
			<div class="col-lg-3" ng-if="action=='APPROVED'">
				<label></label>
				<select style="margin-top: -30px" ng-model="selectedProfile" class="form-control" data-placeholder="Select Profile" ng-change="changeProfile(selectedProfile)">
					<option ng-repeat="profile  in recipeProfiles">{{profile.name}}</option>
				</select>
			</div>
			<div class="col-lg-3" ng-if="action=='IMPROVED' || action=='CREATED' || action=='ARCHIVED' ">{{iterationDetails.profile}}</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<label>Review Remarks:</label>
				<textarea data-ng-model="review.reviewContent" class="form-control"
				 name="reviewContent" required></textarea>
			</div>
		</div>
		<div class="row top-buffer-row"
			data-ng-if="actionForm.reviewContent.$error.required">
			<div class="col-lg-8 alert alert-danger">
				<label>Remarks is required</label>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-6">
				<button type="submit" class="btn btn-primary"
					data-ng-disabled="actionForm.$invalid"
					data-ng-click="submit(actionForm.$valid)">{{actionText}}</button>
			</div>
			<div class="col-lg-6">
				<span class="btn btn-danger pull-right" data-ng-click="closeModal()">Close</span>
			</div>
		</div>
	</div>
</form>