<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .modal-body,
    .modal-header,
    .modal-footer {
        background-color: white;
    }

    .modal {
        background-color: white;
        margin: 70px;
    }
</style>

<div class="row" ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Order Payment Management
        </h1>
    </div>
    <div class="row">
        <div class="col-lg-4" style="margin:8px">
            <input type="text" ng-model="searchValue" placeholder="Search order payment events" class="form-control" />
        </div>
        <div class="col-lg-1">
            <div class="row" style="display:flex;">
                <button class="btn btn-primary" style="margin:8px" title="Please enter CustomerId Id to search."
                    data-ng-model="searchByCustomerId" data-ng-disabled="searchValue == '' || searchValue == null"
                    ng-click="getOrderPaymentDetails('customerId')">
                    By CustomerId
                </button>
                <button class="btn btn-primary" style="margin:8px" title="Please enter Order Id to search."
                    data-ng-model="searchByOrderId" data-ng-disabled="searchValue == '' || searchValue == null"
                    ng-click="getOrderPaymentDetails('orderId')">
                    By OrderId
                </button>
                <button class="btn btn-primary" style="margin:8px" title="Please enter Contact Number to search."
                    data-ng-model="searchByContactNumber" data-ng-disabled="searchValue == '' || searchValue == null"
                    ng-click="getOrderPaymentDetails('contactNumber')">
                    By ContactNumber
                </button>
            </div>
        </div>
    </div>
    <div style="display: flex; margin-top: 2.5%; width: 100%; ">
        <div style="display: flex; align-items: baseline;">
            <div class="col-xs-4">
                <label class="control-label">Payment Source</label>
            </div>
            <div class="col-xs-5 form-group">
                <select class="form-control" data-ng-model="paymentSource">
                    <option data-ng-repeat="unit in ['DINE_IN', 'NEO_SERVICE'] | orderBy" value="{{unit}}">
                        {{unit}}
                    </option>
                </select>
            </div>
            <div class="col-xs-4">
                <label class="control-label">Payment Status</label>
            </div>
            <div class="col-xs-6 form-group">
                <select class="form-control" data-ng-model="paymentStatus">
                    <option data-ng-repeat="unit in ['SUCCESSFUL', 'REFUND_INITIATED', 'REFUND_PROCESSED'] | orderBy"
                        value="{{unit}}">
                        {{unit}}
                    </option>
                </select>
            </div>
        </div>
    </div>
    <div style="display: flex; width: 50%; align-items: baseline; ">
        <div class="col-xs-5">
            <label class="control-label">Pick Start Date</label>
        </div>
        <div class="datepicker" data-date-format="yyyy-MM-dd 00:mm:ss">
            <input class="form-control" data-ng-model="startDate" type="text" placeholder="yyyy-mm-dd" required />
        </div>
        <button class="btn btn-primary" style="margin:8px" title="Find All Payment Details"
                data-ng-model="searchByCustomerId" data-ng-disabled="!(paymentSource != null && paymentStatus != null)"
                ng-click="getOrderPaymentDetailsBySourceAndStatus()">Find All Payments</button>
    </div>
    <div class="row" style="margin-top: 30px">
        <div class="col-xs-12" ng-if="orderPaymentData.length > 0">
            <div class="row">
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered">
                        <thead style="background-color: #E5F3C7">
                            <th style="text-align:center">GENERATED ORDER ID</th>
                            <th style="text-align:center">ORDER ID</th>
                            <th style="text-align:center">CUSTOMER ID</th>
                            <th style="text-align:center">CUSTOMER NAME</th>
                            <th style="text-align:center">CONTACT NUMBER</th>
                            <th style="text-align:center">REQUEST TIME</th>
                            <th style="text-align:center">PAYMENT SOURCE</th>
                            <!--                        <th style="text-align:center">PAYMENT MODE</th>-->
                            <th style="text-align:center">REQUEST STATUS</th>
                            <th style="text-align:center">PAYMENT STATUS&nbsp;</th>
                            <th style="text-align:center">PARTNER TRANSACTION ID</th>
                            <th style="text-align:center">AMOUNT</th>
                            <th style="text-align:center">INITIATE REFUND</th>
                        </thead>
                        <tbody>
                            <tr data-ng-repeat="detail in orderPaymentData">
                                <td style="text-align:center">{{detail.generatedOrderId}}</td>
                                <td style="text-align:center">{{detail.orderId}}</td>
                                <td style="text-align:center">{{detail.customerId}}</td>
                                <td style="text-align:center">{{detail.customerName}}</td>
                                <td style="text-align:center">{{detail.contactNumber}}</td>
                                <td style="text-align:center">{{detail.requestTime}}</td>
                                <td style="text-align:center">{{detail.paymentSource}}</td>
                                <!--                            <td style="text-align:center">{{detail.paymentMode}}</td>-->
                                <td style="text-align:center">{{detail.requestStatus}}</td>
                                <td style="text-align:center">{{detail.paymentStatus}}</td>
                                <td style="text-align:center">{{detail.partnerTransactionId}}</td>
                                <td style="text-align:center">&#x20B9; {{detail.transactionAmount}}</td>
                                <td style="text-align:center">
                                    <button class="btn btn-primary pull-right" style="margin:8px" title="Refund"
                                        data-ng-disabled="detail.paymentStatus != 'REFUND_INITIATED'"
                                        ng-click="initiateRefund(detail.partnerTransactionId)">
                                        Refund
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-lg-10" ng-if="orderPaymentData.length == 0">
                    <h4>No results found</h4>
                </div>

                <button class="btn btn-primary pull-right" style="margin:8px" title="More Data"
                    data-ng-disabled="searchValue == '' || searchValue == null" data-ng-hide="startPosition == 2"
                    ng-click="getMoreData()">
                    More
                </button>
            </div>
        </div>
    </div>
</div>
</div>
</div>