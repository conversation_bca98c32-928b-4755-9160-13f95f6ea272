<div class="row" data-ng-init="init()">
  <div class="col-lg-12">
    <h1 class="page-header">Assembly Recipe Notification </h1>
  </div>
</div>

<div>
<div style="width:100%">
  <div style ="width:46%; float:left" class="form-group" >
    <label>Select Unit</label>
    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsCafes" data-ng-if="cafelist.length > 0"
         options="cafelist" selected-model="selectedUnit" class="region-card">
    </div>
  </div>
  <div style="margin-left: 54%" class="form-group">
    <label>Select Notification Type</label>
    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
         options="typeList" selected-model="selectedType">
  </div>
</div>

  <div>
  <button data-ng-click="sendNotification()" class="btn btn-primary pull-right">Send Notification</button>
  </div>
</div>
</div>
