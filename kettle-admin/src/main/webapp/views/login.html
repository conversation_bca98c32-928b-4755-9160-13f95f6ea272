<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="loginBack" ng-init="init()">
	<div class="responseBox">
    	<div class="alert" ng-class="{'alert-danger':!authSuccess, 'alert-success':authSuccess}" ng-show="showMessage">
    		<button type="button" class="close"><span aria-hidden="true" ng-click="removeAlert()">&times;</span></button>
        	{{authMessage}}
        </div>
    </div>
    <div class="loginBox" style="width: 600px;">
    	<img src="img/logo.png" style="width:50px; height:50px" />
        <h4 style="text-transform: uppercase">Kettle Admin</h4>
        <form>
        	<div class="input-group">
            	<span class="input-group-addon" style="width: 120px;">User ID *</span>
                <input type="text" class="form-control" ng-model="userId" style="width: 450px"/>
            </div><br />
        	<div class="input-group">
            	<span class="input-group-addon" style="width: 120px">Passcode *</span>
                <input type="password" class="form-control" ng-model="passcode" style="width: 450px"/>
            </div><br />
            <div class="input-group">
            	<span class="input-group-addon" style="width: 120px">Company *</span>
                <select ng-model="companyId"
                        ng-options="company.id as (company.id + ' - ' + company.name) for company in companies"
                        class="form-control"
                        data-ng-change="onCompanyChange()"
                        style="width: 450px">
                </select>
            </div><br />
            <div class="input-group">
            	<span class="input-group-addon" style="width: 120px">Brand</span>
                <select ng-model="brandId"
                        ng-options="brand.brandId as (brand.brandId + ' - ' + brand.brandName) for brand in filteredBrands"
                        class="form-control"
                        data-ng-change=""
                        style="width: 450px">
                </select>
            </div><br />
            <button ng-class="{loginDisabled:loginDisabled}" ng-click="login()" style="background-color:#28630d;border-color:#50773e" class="btn btn-primary btn-lg btn-block">{{loginText}}</button>
            <div class="clearfix"></div>
        </form>
    </div>
</div>