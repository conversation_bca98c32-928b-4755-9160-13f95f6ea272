<div class="row partnerEdcMappingContainer" ng-init="init()">
    <div class="col-lg-12"><br>
        <div class="row">
            <h1 class="page-header"> Partner Dqr Mapping </h1>
        </div>
        <div class="row">
            <button style="margin: 10px" class="btn btn-primary pull-right" data-toggle="modal"
                    data-target="#offerModal" data-ng-click="toggelEditMode()">{{editMode ? "Hide": "View "}} Dqr Mappers List
            </button>
        </div>
    </div>




    <div  data-ng-show="!editMode">
        <div class="form-group">
            <label>Select Cafe *</label>
            <select class="form-control" ng-model="selectedUnit">
                <option ng-repeat="cafe in cafelist | orderBy" value="{{cafe}}">
                    {{cafe.name}}
                </option>
            </select>
        </div>
        <div class="form-group">
            <label>Partner Name *</label>
            <input type="text"
                   pattern=".*\S+.*"
                   class="form-control"
                   ng-model="partnerName"
                   required>
        </div>
        <div class="form-group">
            <label>Merchant Id *</label>
            <input type="text"
                   pattern=".*\S+.*"
                   class="form-control"
                   ng-model="merchantId"
                   required>
        </div>
        <div class="form-group">
            <label>Merchant Key *</label>
            <input type="text"
                   pattern=".*\S+.*"
                   class="form-control"
                   ng-model="merchantKey"
                   required>
        </div>
        <div class="form-group">
            <label>Status *</label>
            <select class="form-control" ng-model="dqrMappingStatus">
                <option ng-repeat="status in statusList | orderBy" value="{{status}}">
                    {{status}}
                </option>
            </select>
        </div>
        <div class="form-group">
            <button style="margin: 10px" class="btn btn-primary pull-right"
                    ng-click="savePartnerDqrMapping()">Submit
            </button>
        </div>
    </div>

    <div data-ng-show="editMode">
        <div>
            <label class="col region-card">Select Status * </label>
            <select class="form-control" ng-model="dqrMappingStatus">
                <option ng-repeat="status in statusList | orderBy" value="{{status}}">
                    {{status}}
                </option>
            </select>
        </div>
        <div style="margin-bottom: 30px;">
      <span class="btn btn-primary" data-ng-click="getPartnerDqrMapperList()" style="margin-top: 20px">
        View List
      </span>
            <span class="btn btn-warning" data-ng-click="updatePartnerDqrMapperList()" data-ng-if="newPartnerDqrMapperList != null && newPartnerDqrMapperList.length > 0" style="margin-top: 20px">
        Update Changes
      </span>
        </div>


        <table class="table table-striped table-bordered"
               style="font-size: 15px" data-ng-if="partnerDqrMapperList != null && partnerDqrMapperList.length > 0">
            <thead>
            <th>Unit Id</th>
            <th>Partner Name</th>
            <th>Merchant Id</th>
            <th>Merchant Key</th>
            <th>Status</th>
            <th>Change Status</th>
            </thead>
            <tbody>
            <tr data-ng-repeat="partnerDqrMapper in partnerDqrMapperList track by partnerDqrMapper.id" data-ng-model="selectOffer">
                <td>{{partnerDqrMapper.unitId}}</td>
                <td>{{partnerDqrMapper.partnerName}}</td>
                <td>{{partnerDqrMapper.merchantId}}</td>
                <td>{{partnerDqrMapper.merchantKey}}</td>

                <td>{{partnerDqrMapper.status}}</td>
                <td>
                    <button class="btn btn-danger" data-ng-if="partnerDqrMapper.status =='ACTIVE' " data-ng-click="changeStatus(partnerDqrMapper)" style="width: 100px; margin-left: 40px"> IN_ACTIVE
                    </button>
                    <button class="btn btn-success" data-ng-if="partnerDqrMapper.status =='IN_ACTIVE' " data-ng-click="changeStatus(partnerDqrMapper)" style="width: 100px; margin-left: 10px" > ACTIVE
                    </button>
                </td>
            </tr>
            </tbody>
        </table>

    </div>
</div>

