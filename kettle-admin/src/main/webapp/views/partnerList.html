<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	 <h1 class="page-header">Partner List
       <button class="btn btn-primary pull-right" data-toggle="modal" ng-click="addPartner()"><i class="fa fa-plus fw"></i> Add Partner</button>
     </h1>
    </div>
</div>


<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
    </div>
</div>

<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ filtered.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                        <th>ID &nbsp;<a ng-click="sort_by('id');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th> Partner Name&nbsp;<a ng-click="sort_by('name');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Type&nbsp;<a ng-click="sort_by('type');"><i class="glyphicon glyphicon-sort"></i></a></th>
                       
                    </thead>
                <tbody>
                    <tr ng-repeat="partners in filtered = (partnerDetailsList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
                        <td>{{partners.id}}</td>
                        <td>{{partners.name}}</td>
                        <td>{{partners.type}}</td>
                        
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="partnerModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">{{action}}&nbsp; Partner</h4>
          </div>
          <form name="addProductForm" novalidate>
          <div class="modal-body"> 										<!-- start modal -->
              <div class="form-group">
                  <label>Partner Name *</label>
                  <input type="text" class="form-control" ng-model="partnerName" name="partnerName" required />
                  
          	 </div>
          <div class="form-group">
                  <label>Partner Type * </label>
                <select class="form-control" ng-model="selectPartnerType" ng-options="partnerTypes.name for partnerTypes in partnerType track by partnerTypes.name"></select>
			  </div>
          
              
             
               <div class="form-group clearfix">
                <loading style align="center"></loading>
                  <button class="btn btn-primary pull-right" ng-click="submitAddPartner()">Add Partner</button>
              </div>
          </div>		<!-- End Modal -->
          </form>   <!-- End form -->
     </div>
  </div>
</div>

<div align="center"><b>
    <uib-pagination total-items="totalItems" ng-model="currentPage" max-size="5"
                    boundary-link-numbers="true" ng-disabled="false" rotate="true"
                    items-per-page="entryLimit" class="pagination-sm"></uib-pagination>
</b></div>