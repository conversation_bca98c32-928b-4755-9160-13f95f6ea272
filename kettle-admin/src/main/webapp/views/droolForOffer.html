<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">Drool For Decision</h1>
    </div>
</div>
<button
        class="btn btn-primary pull-right"
        data-toggle="modal"
        ng-click="uploadFile()" style="margin-bottom: 20px">
    <i class="fa fa-plus fw"></i> Add New Version
</button>
<select
        style="margin-bottom:20px; width:30%"
        class="form-control"
        ng-model="selectedDroolFileType"
        ng-options="fileType for fileType in droolFileTypes"
/>
<button class="btn btn-primary " style="margin-top: 5px; margin-bottom:20px"
        data-ng-click="getAllFilesDetail()"
        data-ng-disabled="allFileDetail==null">Fetch Files
</button>
<div class="form-group">
    <table class="table table-bordered">
        <thead style="background-color: #e7e7e7">
        <th></th>
        <th>Version</th>
        <th>File Name</th>
        <th>File Type</th>
        <th>Creation Time</th>
        <th>Status</th>
        <th>Download</th>
        <th>Action</th>
        </thead>
        <tbody>
        <tr ng-repeat="fileData in allFileDetail track by $index">
            <td><img data-ng-if="fileData.isDefault != null && fileData.isDefault == 'Y'" title="Default Drool"
                     ng-src="img/defaultImg.png" height="30px" width="30px"></td>
           <td>{{fileData.version}}</td>
           <td>{{fileData.fileName}}</td>
           <td>{{fileData.type}}</td>
           <td>{{fileData.creationTime}}</td>
           <td>
               <button ng-if="fileData.status == 'ACTIVE'"
                       title="Active" style="border: none; background: none"><i class="fa fa-lightbulb-o" style="font-size:48px;color:green"></i></button>
               <button ng-if="fileData.status == 'IN_ACTIVE'" ng-click="activateFile($index)"
                       title="Inactive" style="border: none; background: none"><i class="fa fa-lightbulb-o" style="font-size:48px;color:red"></i></button>
               <button ng-if="fileData.status == 'PROCESSING'" ng-click="activateFile($index)"
                       title="Processing" style="border: none; background: none"><i class="fa fa-lightbulb-o" style="font-size:48px;color:yellow"></i></button>
           </td>
            <td><button ng-click="downloadFile($index)" class="btn btn-secondary">Download</button></td>
            <td style="display:flex">
                <button class="btn btn-primary"  ng-click="setAsDefault($index)">Set Default</button>
                <img title="Delete Drool Sheet permanently" ng-src="img/delete.png" ng-click="inActivateVersion($index)" height="30px" width="30px">
            </td>
        </tr>
        </tbody>

    </table>
</div>

<div
        class="modal fade"
        id="addDroolDecisionFile"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">Adding Drool File for {{selectedDroolFileType}}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-6">Upload Drool File</div>
                    <div class="col-xs-6">
                        <input class="btn btn-default" style="width: 100%;" type="file"
                               file-model="fileToUpload" accept="">
                        <button class="btn btn-primary" style="margin-top: 5px"
                                data-ng-click="addNewVersion()"
                                data-ng-disabled="fileToUpload==null">Add New Version
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
