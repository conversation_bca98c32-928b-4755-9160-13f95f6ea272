<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .modal-body,.modal-header,.modal-footer{
        background-color: white;
    }
    .modal{
        background-color: white;
        margin: 70px;
    }
</style>

<div class="row" ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Order Management
        </h1>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <input type="text" ng-model="orderId"
                   placeholder="Search Order Id" class="form-control"/>
        </div>
        <div class="col-lg-1">
            <button class="btn btn-primary" title="Please enter Order Id to search."
                    data-ng-disabled="orderId == '' || orderId == null" ng-click="getOrderByGeneratedOrderId()">
                Search
            </button>
        </div>
    </div>
    <div class="row" style="margin-top: 30px">
        <div
                class="col-xs-12"
                ng-if="orderDetail.length > 0">
            <div class="row">
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th>GENERATED ORDER ID</th>
                        <th>CUSTOMER ID</th>
                        <th>UNIT ID</th>
                        <th>ORDER STATUS</th>
                        <th>SETTLEMENT TYPE</th>
                        <th>BILL GENERATION TIME</th>
                        <th>ORDER SOURCE&nbsp;</th>
                        <th colspan="3" style="text-align:center">UPDATE STATUS</th>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="order in orderDetail">
                            <td>{{order.generateOrderId}}</td>
                            <td>{{order.customerId}}</td>
                            <td>{{order.unitId}}</td>
                            <td>{{order.status}}</td>
                            <td>{{order.settlementType}}</td>
                            <td>{{order.billCreationTime}}</td>
                            <td>{{order.source}}</td>
                            <td>
                                <button class="btn btn-primary" title="Update the order status"
                                        ng-click="openCancelOrderModal()">
                                    Cancel order
                                </button>
                            </td>
                            <td>
                                <button class="btn btn-primary" title="Update the order status"
                                        ng-click="openSettleOrderModal()">
                                    Settle Order
                                </button>
                            </td>
                            <td data-ng-if="order.printCount != 0">
                                <button class="btn btn-primary" title="Reset Bill Print"
                                        ng-click="resetPrintCount(order)">
                                    Reset Print Count
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div
                        class="col-lg-10"
                        ng-if="orderDetail.length == 0">
                    <h4>No results found</h4>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>


<!--Modal for cancel order-->
<div class="modal"  id = "adminOrderCancelUpdateModal" ng-init = "initCancellation()" role="dialog">
    <div class="modal-header" >
        <div flash-message="5000"></div>
        <h3 class="text-center">Order Cancel Details</h3>
    </div>

    <div class="modal-body" >
        <form novalidate role="form" name="orderCancelForm" autocomplete="off">
            <div class="row" style="margin: 5px;">
                <div class="col-xs-3">
                    <label for="username">User Id</label>
                </div>
                <div class="col-xs-9">
                    <input type="number" name="username" id="username" ng-minlength="6"
                           ng-maxlength="6" ng-model="userId" autocomplete="off" required />
                </div>
            </div>
            <div class="row" style="margin: 5px;" >
                <div class="col-xs-3">
                    <label for="Password">Passcode</label>

                </div>
                <div class="col-xs-9">
                    <input type="password" name="password" id="Password" ng-model="pwd"
                           autocomplete="off" required />
                </div>
            </div>

            <div class="row" style="margin: 15px;" >
                <div class="col-xs-3" >
                    <label>Cancellation Reason?</label>
                </div>
                <div class="col-xs-9">
                    <select style="height: 30px;" id="cancellationReasonId" name="cancellationReasonId"
                            data-ng-model="selectedReason"
                            data-ng-options="data as data.desc for data in transactionalReasonMetaData.cancellationReasons">
                    </select>
                    <label style="margin: 15px; color: red;"
                           data-ng-if="selectedReason != null && selectedReason.noWastage && !selectedReason.completeWastage">NO
                        WASTAGE</label>
                    <label style="margin: 15px; color: red;"
                           data-ng-if="selectedReason != null && selectedReason.completeWastage">COMPLETE
                        WASTAGE</label>
                </div>
            </div>
            <div class="row" data-ng-if="isOnlineOrder" style="margin: 15px;">
                <div class="col-xs-3">
                    <label>Initiate Refund?</label>
                </div>
                <div class="col-xs-9">
                    <div>
                        <input type="radio" name="refund" data-ng-model="refund"
                               value="true" data-ng-change="setRefund(refund)" />&nbsp;<span>Yes</span>
                    </div>

                    <div>
                        <input type="radio" name="refund" data-ng-model="refund"
                               value="false" data-ng-change="setRefund(refund)" />&nbsp;<span>No</span>
                    </div>

                    <div class="alert alert-warning" data-ng-if="showRefundMessage">
                        Note: The amount would be refunded within 3-5 working days.</div>
                </div>
            </div>
            <div class="row" style="margin: 15px;" >
                <div class="col-xs-3">
                    <label for="comment">Reason:</label>
                </div>
                <div class="col-xs-9">
				<textarea style="width: 100%;" rows="5" name="comment" id="comment"
                          ng-model="cancelOrderReason" required>
                </textarea>
                </div>
            </div>
        </form>
    </div>

    <div class="modal-footer">
        <div class="btn-group">
            <a  class="btn btn-danger" ng-click="cancelOrderCancel()">Cancel</a>
            <a  class="btn btn-default" ng-click="checkUserForCancellation()"
                ng-disabled="!orderCancelForm.$valid || dataLoading">Submit</a>
        </div>
    </div>
</div>

<!--Modal for settle the order-->
<div class="modal"  id = "adminOrderSettleModal" role="dialog">
    <div class="modal-header" >
        <div flash-message="5000"></div>
        <h3 class="text-center">Order Settle Details</h3>
    </div>
    <div class="modal-body" >
        <form novalidate role="form" name="orderCancelForm" autocomplete="off">
            <div class="row" style="margin: 5px;">
                <div class="col-xs-3">
                    <label for="username">User Id</label>
                </div>
                <div class="col-xs-9">
                    <input type="number" name="username" id="username" ng-minlength="6"
                           ng-maxlength="6" ng-model="userId" autocomplete="off" required />
                </div>
            </div>
            <div class="row" style="margin: 5px;" >
                <div class="col-xs-3">
                    <label for="Password">Passcode</label>
                </div>
                <div class="col-xs-9">
                    <input type="password" name="password" id="Password" ng-model="pwd"
                           autocomplete="off" required />
                </div>
            </div>
        </form>
    </div>

    <div class="modal-footer">
        <div class="btn-group">
            <a class="btn btn-danger" ng-click="cancelOrderSettleCancel()">Cancel</a>
            <a class="btn btn-default" ng-click="checkUserForCancellation()"
               ng-disabled="!orderCancelForm.$valid || dataLoading">Submit</a>
        </div>
    </div>

</div>