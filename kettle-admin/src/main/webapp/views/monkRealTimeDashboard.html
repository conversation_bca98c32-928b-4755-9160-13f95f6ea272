<style>
.modal-dialog  {
width:70%;
height:70%;
}

</style>

<div class="container-fluid partner-page" data-ng-init="init()">
	<div class="row">
		<h2 class="text-center" style="color: #737370;text-align: center;"><PERSON><PERSON> Real Time Dashboard</h2>
	</div>

	<div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
		<div class="col-xs-12">
			<div class="form-group">
				<label>Order Id</label> <br>
				<input id="orderId" ng-model="orderId" name="orderId">
				</input>
				<button class="btn btn-primary" ng-click="submitOrderId()">Get Data
				</button>
			</div>
		</div>
	</div>

	<div class="row"  ng-show="dashboardData">
		<div class="row alert alert-info">
			<div class="col-xs-12">
				<h3><PERSON><PERSON> Order Details</h3>
			</div>
		</div>
		<div class="row">
			<div class="table-responsive">
			<table class="table table-bordered table-hover">
				<thead>
				<tr>
					<th>Remake</th>
					<th>Remake Reason</th>
					<th>Order Id</th>
					<th>Item Id</th>
					<th>Unit Id</th>
					<th>Unit Name</th>
					<th>Product Id</th>
					<th>Product Name</th>
					<th>Product Dimension</th>
					<th>Product Quantity</th>
					<th>Billing Server Time</th>
					<th>Assembly Order Creation Time</th>
					<th>Estimated Recipe Preperation Time</th>
					<th>Task Start Time</th>
					<th>Task Completion Time</th>
					<th>Task Completed In</th>
					<th>Monk Name</th>
					<th>Monk Calibration Coeff.</th>
					<th>Monk Recipe Version</th>
					<th>Monk Recipe String</th>
					<th>Steep Time</th>
					<th>Steew Time</th>
					<th>Number Of Boils</th>
					<th>Expected Number Of Boils</th>
					<th>Expected Product Final Quantity</th>
					<th>Product Final Quantity</th>
					<th>Product Expected Water Quantity</th>
					<th>Product Actual Water Quantity</th>
					<th>Product Expected Milk Quantity</th>
					<th>Product Actual Milk Quantity</th>
					<th>Further Details</th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="row in dashboardData">
					<td class="alert alert-danger" data-ng-if="row.remakeOrder">{{row.remakeOrder}}</td>
					<td data-ng-if="!row.remakeOrder" >{{row.remakeOrder}}</td>
					<td>{{row.remakeReason}}</td>
					<td>{{row.orderId}}</td>
					<td>{{row.itemId}}</td>
					<td>{{row.unitId}}</td>
					<td>{{row.unitName}}</td>
					<td>{{row.productId}}</td>
					<td>{{row.productName}}</td>
					<td>{{row.productDimension}}</td>
					<td>{{row.productQuantity}}</td>
					<td>{{row.billingServerTime}}</td>
					<td>{{row.assemblyOrderCreationTime}}</td>
					<td>{{row.preperationTime}}</td>
					<td>{{row.taskStartTime}}</td>
					<td>{{row.taskCompletionTime}}</td>
					<td>{{row.taskCompletedIn}}</td>
					<td>{{row.monkName}}</td>
					<td>{{row.calibrationCoef}}</td>
					<td>{{row.recipeVersion}}</td>
					<td>{{row.recipeString}}</td>
					<td>{{row.steepingTime}}</td>
					<td>{{row.steewingTime}}</td>
					<td>{{row.noOfBoils}}</td>
					<td>{{row.expectedNoOfBoils}}</td>
					<td>{{row.expectedFinalQuantity}}</td>
					<td>{{row.finalQuantity}}</td>
					<td>{{row.expectedWaterQuatity}}</td>
					<td>{{row.actualWaterQuatity}}</td>
					<td>{{row.expectedMilkQuatity}}</td>
					<td>{{row.actualMilkQuatity}}</td>
					<td>
						<div class="row">
							<div class="col-sm">
								<button class="btn btn-sm btn-primary" ng-click="openModal(row)">Show Addons</button>
							</div>
							<div class="col-sm">
								<button class="btn btn-sm btn-primary" ng-click="openStatusCodes(row)">Show Status Codes</button>
							</div>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
			</div>
		</div>
	</div>

</div>

<script type="text/ng-template" id="monkRealTimeDashboardExtendedDetails.html">
	<div class="modal-header">
		<h3 class="modal-title">Further Details</h3>
		<button type="button" class="close" ng-click="close()">&times;</button>
	</div>
	<div class="modal-body">
		<table class="table table-bordered">
			<thead>
			<tr>
				<th>Order Item Addon Id</th>
				<th>Product Id</th>
				<th>Dimension</th>
				<th>Source</th>
				<th>Name</th>
				<th>UOM</th>
				<th>Quantity</th>
				<th>Type</th>
				<th>Default Setting</th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="nestedRow in nestedData">
				<td>{{nestedRow.orderItemAddonId}}</td>
				<td>{{nestedRow.productId}}</td>
				<td>{{nestedRow.dimension}}</td>
				<td>{{nestedRow.source}}</td>
				<td>{{nestedRow.name}}</td>
				<td>{{nestedRow.uom}}</td>
				<td>{{nestedRow.quantity}}</td>
				<td>{{nestedRow.type}}</td>
				<td>{{nestedRow.defaultSetting}}</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div class="modal-footer">
		<button class="btn btn-secondary" ng-click="close()">Close</button>
	</div>
</script>

<script type="text/ng-template" id="monkRealTimeDashboardStatusCodes.html">
	<div class="modal-header">
		<h3 class="modal-title">Further Details</h3>
		<button type="button" class="close" ng-click="close()">&times;</button>
	</div>
	<div class="modal-body">
		<table class="table table-bordered">
			<thead>
			<tr>
				<th>Monk Payload</th>
				<th>Time</th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="nestedRow in nestedData">
				<td>{{nestedRow.payLoad}}</td>
				<td>{{nestedRow.time}}</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div class="modal-footer">
		<button class="btn btn-secondary" ng-click="close()">Close</button>
	</div>
</script>
