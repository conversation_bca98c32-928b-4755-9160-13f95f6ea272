<div class="row" ng-init="init()">
  <div class="col-lg-12"><br>
<h3>Revenue Certificate Details</h3>
<div style="margin-top: 50px">
  <div class="form-group">
    <label>Select Unit*</label>
      <select class="form-control" ng-model="selectedCafe" ng-options="cafe as cafe.name for cafe in cafelist | orderBy : 'name'" data-ng-change="getmappings(selectedCafe)"></select>
  </div>
  <div class="form-group">
    <label>Please select if revenue certificate to be generated *</label>
    <select class="form-control" ng-model="isFunctional">
      <option ng-repeat="istrue in isrevenueCertificate | orderBy" value="{{istrue}}">
        {{istrue}}
      </option>
    </select>
  </div>
  <div class="form-group">
    <form name="myform">
      <label>Mail for revenue certificate generation *</label>
      <input class="form-control" ng-pattern="multipleEmailRegEx" name="input" ng-model="revenueCertificateEmail"
             ng-disabled="isFunctional == null || isFunctional === 'NO'">
<!--             name="input" ng-model="revenueCertificateEmail"-->
<!--      />-->
      <span ng-show="myform.input.$error.pattern" class="error">Input is not valid.</span>
    </form>
  </div>
  <div>
    <button type="button" class="btn btn-primary" data-ng-click="submitCertificateUnitMapping()">Submit</button>
  </div>
</div>