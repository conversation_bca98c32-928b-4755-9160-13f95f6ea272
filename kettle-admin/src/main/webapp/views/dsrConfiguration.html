<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">DSR Configuration</h1>
    </div>
</div>
<div class="container-fluid">
    <div class="row ">
        <div class="col-xs-12">
            <div class="row margin-bottom10">
                <div class="col-xs-3">Cafe Name</div>
                <div class="col-xs-7">
                    <select
                            ui-select2
                            class="form-control"
                            style="width: 100% !important"
                            data-ng-model="selectedpartnerID"
                            data-placeholder="Select PartnerID"
                            data-ng-change= "selectPartnerId(selectedpartnerID)"
                            data-ng-options="u as u.name for u in units">
                    </select>
                </div>
                <div class="col-xs-1" style="align:right" ng-if="resultFound != undefined && !resultFound"><button class="btn btn-primary pull-right" data-toggle="modal"
                                                                      data-target="#addTable" ng-click="addData()"> Add </button></div>
                <div class="col-xs-1" style="align:right" ng-if="resultFound != undefined && resultFound"><button class="btn btn-primary pull-right" data-toggle="modal"
                                                                  data-target="#updateTable" ng-click="addUpdateData()">Update</button></div>
            </div>
        </div>
    </div>
    <div id="addTable" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" >&times;</button>
                    <h4 class="modal-title">Add Details {{selectedpartnerID.name}}</h4>
                </div>
                <div class="modal-body" style="text-align: center">

                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">TENANT_ID<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="tenant" placeholder="Enter the TenantID" value="{{tenant}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">FTP_SERVER<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                         border-color: gray;"><input type="text" ng-model="ftpserver" placeholder="FTP_SERVER" value="{{ftpserver}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">FTP_PORT<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="number" ng-model="ftpport" placeholder="FTP_PORT" value="{{ftpport}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">USERNAME<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="username" placeholder="UserName" value="{{username}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">PASSWORD<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="password" placeholder="Password" value="{{password}}"></div>
                    </div>

                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">STATUS<sup style="color: red;">*</sup></div>
                       <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;">
                            <select ng-model="status" ng-options="isactive for isactive in statusreport" style="padding-left: 93px;"></select>
                        </div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">Report Type<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><select ng-model="reporttype" ng-options="report for report in reportList" style="padding-left: 12px;"></select></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">DIRECTORY</div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="directory" placeholder="Directory" value="{{directory}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">PROTOCOL<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="protocol" placeholder="PROTOCOL" value="{{protocol}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">REPORT_EMAIL<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="email" ng-model="email" placeholder="Enter the Email" value="{{email}}" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary"  data-ng-click="addDsrConfig()">Add</button>
                </div>
            </div>
        </div>
    </div>
    <div id="updateTable" class="modal fade" role="dialog" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" data-ng-click="showTableData()">&times;</button>
                    <h4 class="modal-title">Update Details {{selectedpartnerID.name}}</h4>
                </div>
                <div class="modal-body" style="text-align: center">
                    <div class="row" style="padding: 5px;">
                        <div class="col-xs-6">TENANT_ID<sup style="color: red;">*</sup> </div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="tenant" placeholder="Enter the TenantID" value="{{tenant}}"></div>
                    </div>
                    <div class="row" style="padding: 5px;">
                        <div class="col-xs-6">FTP_SERVER<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="ftpserver" placeholder="FTP_SERVER" value="{{ftpserver}}"></div>
                    </div>
                    <div class="row" style="padding: 5px;">
                        <div class="col-xs-6">FTP_PORT<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="number" ng-model="ftpport" placeholder="FTP_PORT" value="{{ftpport}}"></div>
                    </div>
                    <div class="row" style="padding: 5px;">
                        <div class="col-xs-6">USERNAME<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="username" placeholder="UserName" value="{{username}}"></div>
                    </div>
                    <div class="row" style="padding: 5px;">
                        <div class="col-xs-6">PASSWORD<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="password" placeholder="Password" value="{{password}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">STATUS<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;">
                            <select ng-model="status" ng-options="isactive for isactive in statusreport" style="padding-left: 93px" ></select>
                        </div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">Report Type<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><select ng-model="reporttype" ng-options="report for report in reportList" style="padding-left: 12px;"></select></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">DIRECTORY</div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="directory" placeholder="Directory" value="{{directory}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">PROTOCOL<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="text" ng-model="protocol" placeholder="PROTOCOL" value="{{protocol}}"></div>
                    </div>
                    <div class="row" style="padding: 5px">
                        <div class="col-xs-6">REPORT_EMAIL<sup style="color: red;">*</sup></div>
                        <div class="col-xs-6" style="padding-left: 83px;border-radius: 3px;
                        border-color: gray;"><input type="email" ng-model="email" placeholder="Enter the Email" value="{{email}}" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-ng-click="updateDsrConfig()">Update</button>
                </div>
            </div>
        </div>
    </div>
    <table class="table table-bordered table-striped" ng-if="resultFound != undefined && resultFound">
        <thead>
            <tr>
                <td scope="col">TENANT_ID</td>
                <td scope="col">FTP_SERVER</td>
                <td scope="col">FTP_PORT</td>
                <td scope="col">USERNAME</td>
                <td scope="col">PASSWORD</td>
                <td scope="col">STATUS</td>
                <td scope="col">REPORT_TYPE</td>
                <td scope="col">DIRECTORY</td>
                <td scope="col">PROTOCOL</td>
                <td scope="col">REPORT_EMAIL</td>
            </tr>
        </thead>
        <tr>
            <td scope="col">{{tenant}}</td>
            <td scope="col">{{ftpserver}}</td>
            <td scope="col">{{ftpport}}</td>
            <td scope="col">{{username}}</td>
            <td scope="col">{{password}}</td>
            <td scope="col">{{status}}</td>
            <td scope="col">{{reporttype}}</td>
            <td scope="col">{{directory}}</td>
            <td scope="col">{{protocol}}</td>
            <td scope="col">{{email}}</td>
        </tr>
    </table>
    <div ng-if="resultFound != undefined && !resultFound">
        <b>No Data Available</b>
    </div>
</div>

