<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    table th, td {
        border: #ccc 1px solid;
    }

     body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h1 {
        color: #333;
    }

    .form-group {
        margin-bottom: 15px;
    }

    label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    select, input[type="file"] {
        padding: 8px;
        margin: 5px 0;
        width: 100%;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    textarea {
        width: 100%;
        padding: 10px;
        margin-top: 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #fefefe;
    }

    .error-section {
        background: #f0f0f0;
        color: #333;
        padding: 15px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin-top: 20px;
    }

    .upload-panel {
        border: 1px solid #e1e1e1;
        padding: 20px;
        border-radius: 8px;
        background-color: #ffffff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
    }

    .upload-panel h3 {
        color: #444;
        margin-bottom: 15px;
    }

    .btn-custom {
        background-color: #0066cc;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
    }

    .btn-custom:hover {
        background-color: #004d99;
    }




.log-section {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f7f7f7;
}

.log-section h4, .log-category h5, .log-type h6 {
    margin: 5px 0;
}

ul {
    margin-left: 20px;
    padding: 0;
    list-style: disc;
}

ul li {
    margin: 3px 0;
}

.log-type-header {
    color: #333;
    font-size: 18px;
    margin-bottom: 10px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

/* Second-level category styling */
.log-category-header {
    color: #555;
    font-size: 16px;
    margin: 10px 0;
    font-weight: bold;
    background-color: #f1f1f1;
    padding: 5px 10px;
    border-radius: 4px;
}

/* Third-level subcategory styling */
.log-type-subheader {
    color: #666;
    font-size: 14px;
    margin-top: 10px;
    font-style: italic;
}

/* Styling for the change list */
.change-list {
    list-style-type: disc;
    padding-left: 20px;
    margin-top: 5px;
}

.change-item {
    margin-bottom: 5px;
    color: #444;
    line-height: 1.6;
}

/* Optional hover effect for items */
.change-item:hover {
    background-color: #eef;
    border-radius: 4px;
    padding: 2px 5px;
}

</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Menu Management Dashboard</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'UPDATE UNIT MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to update menu changes for individual unit per region.</h3>
                    <p>In order to push menu, select each region and their corresponding first outlet, then select
                        partner and press get menu. Once menu is loaded, press add menu.
                        Do this activity for all the regions where you want to update menu.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Set Menu</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Menu Type</label>
                                <select class="form-control"
                                        data-ng-options="type for type in menuType "
                                        data-ng-model="selectedMenuType"
                                        data-ng-change="setSelectedMenuType(selectedMenuType)"
                                >
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Show Units"
                                       data-ng-click="setFilteredUnits()">
                            </div>
                            <!--<div class="form-group">
                                <label>Select Region</label>
                                <select class="form-control"
                                        data-ng-options="region as region.name for region in regions track by region.id"
                                        data-ng-model="selectedRegion"
                                        data-ng-change="setSelectedRegion(selectedRegion)">
                                </select>
                            </div>-->
                            <div data-ng-if="filteredUnits != null && filteredUnits.length > 0">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <input type="text" data-ng-model="singleUnitSearch" class="form-control"
                                               placeholder="search unit">
                                    </div>
                                </div>
                                <div class="row"
                                     style="padding:10px; background:#ddd; border:#ccc 1px solid; margin: 5px">
                                    <div class="col-xs-12">Unit Name</div>
                                    <!--<div class="col-xs-4">Last Menu Time</div>
                                    <div class="col-xs-4">Menu Updated By</div>-->
                                </div>
                                <div data-ng-repeat="unit in filteredUnits | filter: singleUnitSearch | orderBy:'name' track by unit.id"
                                     style="border:#ccc 1px solid; margin: 10px 5px; background:#ddd;">
                                    <div class="row {{checkToday(unit.activeMenu)?'yellowBg':''}}"
                                         data-ng-click="expandUnit(unit)"
                                         style="padding:10px; border:#ccc 1px solid; margin: 0; cursor: pointer;">
                                        <div class="col-xs-12">{{unit.name}}</div>
                                    </div>
                                    <div data-ng-if="unit.expand == true" style="background: #fff; padding:10px">

                                        <div class="row">
                                            <div class="col-xs-12 text-right">
                                                <!--<span class="checkbox" data-ng-if="selectedPartner.partnerName == 'SWIGGY'">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="unit.newMenu"
                                                               data-ng-change="setNewMenu(unit, unit.newMenu)"> New Menu
                                                    </label>
                                                </span>-->
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="unit.addPackaging"
                                                               data-ng-change="setAddPackaging(unit, unit.addPackaging)"> Add packaging
                                                    </label>
                                                </span>
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox"
                                                               data-ng-model="unit.splitAllDesiChaiDimensions"
                                                               data-ng-change="setSplitAllDesiChaiDimensions(unit, unit.splitAllDesiChaiDimensions)"> Split All Desi Chai Dimensions
                                                    </label>
                                                </span>
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="unit.miniKetliDefault"
                                                               data-ng-change="setMiniKetliDefault(unit, unit.miniKetliDefault)"> Mini Ketli default
                                                    </label>
                                                </span>
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="unit.addSeparateDimension"
                                                            data-ng-change="toggleSeparateDimension(unit,unit.addSeparateDimension)"> Add Separate Dimension Item
                                                    </label>
                                                </span>
                                                
                                                <!-- Dropdown shown conditionally -->
                                                <div class="form-group" data-ng-show="unit.addSeparateDimension">
                                                    <label for="itemDimension">Item Dimension</label>
                                                    <select id="itemDimension" class="form-control" data-ng-model="unit.selectedDimension"
                                                        data-ng-change="onDimensionChange(unit,unit.selectedDimension)">
                                                        <option value="ChotiKetli">Choti Ketli</option>
                                                        <option value="BadiKetli">Badi Ketli</option>
                                                        <option value="MiniKetli">Mini Ketli</option>
                                                        <option value="Regular">Regular</option>
                                                    </select>
                                                </div>
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="bulkDealOfTheDay"
                                                               data-ng-change="setBulkDealOfTheDay(bulkDealOfTheDay)"> Deal Of The Day
                                                    </label>
                                                </span>
                                                <span class="checkbox">
                                                    <label>
                                                        <input type="checkbox" data-ng-model="unit.clubAllDesiChaiDimensions"
                                                               data-ng-change="setClubAllDesiChaiDimensions(unit, unit.clubAllDesiChaiDimensions)"> Club All Desi Chai Dimensions
                                                    </label>
                                                </span>
                                                <span class="checkbox">
                                                    <label>
                                                        <input id="addTimingId" type="checkbox" data-ng-model="addTimings" data-ng-change ="setAddTimings(e)"> Add Timings To Menu
                                                    </label>
                                                </span>
                                            </div>
                                        </div>


                                        <div class="row">
                                            <div class="col-xs-12"
                                                 data-ng-if="unit.newMenu == true && selectedMenuType != 'SINGLE_SERVE'">
                                                <table class="table table-striped table-bordered"
                                                       id="tableDataStructure"
                                                       style="margin-padding:0px;">
                                                    <thead>
                                                    <th>Menu Version</th>
                                                    <th>Menu Type</th>
                                                    <th>Partner</th>
                                                    <th>Outlet Id</th>
                                                    <th>Menu Name</th>
                                                    <th>Creation Time</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr data-ng-repeat="item in unitVersionList | orderBy: '-version'">
                                                        <td>{{item.version}}</td>
                                                        <td>{{item.menuType}}</td>
                                                        <td>
                                                            {{getChannelPartnerById(item.kettlePartnerId).partnerName}}
                                                        </td>
                                                        <td>{{item.unitId}}</td>
                                                        <td>{{item.menuSequenceName}}</td>
                                                        <td>{{item.addTime | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
                                                        <td>{{item.status}}</td>
                                                        <td>
							                            <span><button class="btn btn-primary pull-right"
                                                                      data-ng-if="item.status == 'IN_PROGRESS'"
                                                                      data-ng-click="markAsDefaultVersion(item.version, item.unitId, item.kettlePartnerId, item.brandId)"
                                                                      id="markDefault">Mark Active</button></span>
                                                            <span><button class="btn btn-primary pull-right"
                                                                          data-ng-if="item.status == 'ACTIVE' || item.status == 'IN_ACTIVE'"
                                                                          data-ng-click="viewVersionMenu(item.version, item.unitId, item.kettlePartnerId, item.brandId)"
                                                                          id="downloadVersion">View Menu</button></span><br>

                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                                <br/>
                                                <div class="row">
                                                    <div class="col-xs-12 text-right">
                                                        <input class="btn btn-primary"
                                                               data-ng-click="getUnitMenuToAdd()" type="button"
                                                               value="Upload New Version"/>
                                                        <input type="button" data-ng-if="addMenuObj!=null && !viewMenu"
                                                               class="btn btn-primary" value="Save Menu"
                                                               data-ng-click="uploadNewMenu()"/>
                                                    </div>
                                                </div>
                                                <br/>
                                            </div>
                                        </div>
                                        <div class="row"
                                             data-ng-if="(selectedPartner.partnerName == 'ZOMATO' && selectedMenuType == 'SINGLE_SERVE') || (unit.newMenu == false)">
                                            <div class="col-xs-12 text-right">
                                                <input type="button" class="btn btn-primary"
                                                       value="Get Current Partner Menu"
                                                       data-ng-click="getCurrentMenu()"/>
                                                <input type="button" class="btn btn-primary"
                                                       value="Refresh Current Partner Menu"
                                                       data-ng-click="refreshMenu()"/>
                                                <input type="button" class="btn btn-primary"
                                                       value="Get Kettle Menu for Update"
                                                       data-ng-click="getUnitMenuToAdd()"/>
                                                <input type="button" data-ng-if="addMenuObj!=null"
                                                       class="btn btn-primary" value="Send Menu to Partner"
                                                       data-ng-click="validateMenu()"/>
                                            </div>
                                        </div>
                                        <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                                            <div data-ng-if="unit.newMenu != true">
                                                <div data-ng-if="addMenuObj != null">
                                                    <h3>Menu Items</h3>
                                                    <div data-ng-repeat="cat in addMenuObj.menu.categories | orderBy: 'category_order' track by cat.category_id">
                                                        <div style="background: #69ac6a; padding:15px;margin: 10px 0 0 0;"
                                                             data-ng-if="cat.items.length > 0">
                                                            <h4 style="display: inline;">Category: {{cat.category_name
                                                                == 'Others' ? 'Unmapped products' :
                                                                cat.category_name}}</h4>
                                                            <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                        line-height: 10px;padding: 8px;float: right;">
                                                            {{cat.category_order}}
                                                        </span>
                                                        </div>

                                                        <table class="table bordered" data-ng-if="cat.items.length > 0"
                                                               style="background: #ea8080;">
                                                            <tr>
                                                                <th>Product Name</th>
                                                                <th>Product taxes</th>
                                                                <th>Bogo active</th>
                                                                <th>Treats active</th>
                                                                <th>Order</th>
                                                                <th>Filtered</th>
                                                                <th>Customizations</th>
                                                            </tr>
                                                            <tr data-ng-repeat="item in cat.items | orderBy: 'item_order' track by item.item_id">
                                                                <td>{{item.item_name}}</td>
                                                                <td>{{item.item_taxes[0].taxes.join()}}</td>
                                                                <td>{{item.item_is_bogo_active}}</td>
                                                                <td>{{item.item_is_treats_active}}</td>
                                                                <td>{{item.item_order}}</td>
                                                                <td>{{filteredProducts.indexOf(item.item_id) >= 0}}</td>
                                                                <td>
                                                                <span data-ng-repeat="group in item.groups">
                                                                    <strong>{{group.group_name}}:</strong>
                                                                    <span data-ng-repeat="gitem in group.items">{{gitem.item_name}}</span>
                                                                </span>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <div data-ng-if="cat.subcategories.length > 0"
                                                             style="border: #ccc 1px solid;padding: 10px;">
                                                            <div data-ng-repeat="subCat in cat.subcategories | orderBy: 'subcategory_order' track by subCat.subcategory_id">
                                                                <div style="background: #e2af2e; padding:15px;margin: 10px 0 0 0;">
                                                                    <h4 style="display: inline;">Sub-category:
                                                                        {{subCat.subcategory_name}}</h4>
                                                                    <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                                line-height: 10px;padding: 8px;float: right;">
                                                                    {{subCat.subcategory_order}}
                                                                </span>
                                                                </div>
                                                                <table class="table bordered"
                                                                       data-ng-if="subCat.items.length > 0">
                                                                    <tr>
                                                                        <th>Product Name</th>
                                                                        <th>Product taxes</th>
                                                                        <th>Bogo active</th>
                                                                        <th>Treats active</th>
                                                                        <th>Order</th>
                                                                        <th>Filtered</th>
                                                                        <th>Customizations</th>
                                                                    </tr>
                                                                    <tr data-ng-repeat="item in subCat.items | orderBy: 'item_order' track by item.item_id">
                                                                        <td>{{item.item_name}}</td>
                                                                        <td>{{item.item_taxes[0].taxes.join()}}</td>
                                                                        <td>{{item.item_is_bogo_active}}</td>
                                                                        <td>{{item.item_is_treats_active}}</td>
                                                                        <td>{{item.item_order}}</td>
                                                                        <td>{{filteredProducts.indexOf(item.item_id) >=
                                                                            0}}
                                                                        </td>
                                                                        <td>
                                                                        <span data-ng-repeat="group in item.groups">
                                                                            <strong>{{group.group_name}}:</strong>
                                                                            <span data-ng-repeat="gitem in group.items">{{gitem.item_name}} </span>
                                                                        </span>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div data-ng-if="addMenuObj.menu.charges.length>0">
                                                        <h3>Charges</h3>
                                                        <table class="table bordered">
                                                            <tr>
                                                                <th>Charge name</th>
                                                                <th>Charge value</th>
                                                                <th>Always applicable</th>
                                                                <th>Applicable below</th>
                                                                <th>Charge type</th>
                                                                <th>Charge taxes</th>
                                                            </tr>
                                                            <tr data-ng-repeat="charge in addMenuObj.menu.charges">
                                                                <td>{{charge.charge_name}}</td>
                                                                <td>{{charge.charge_value}}</td>
                                                                <td>{{charge.charge_always_applicable}}</td>
                                                                <td>{{charge.charge_applicable_below_order_amount}}</td>
                                                                <td>{{charge.charge_type}}</td>
                                                                <td>{{charge.charge_taxes[0].taxes.join()}}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div data-ng-if="addMenuObj.menu.taxes.length>0">
                                                        <h3>Taxes</h3>
                                                        <table class="table bordered">
                                                            <tr>
                                                                <th>Tax id</th>
                                                                <th>Tax name</th>
                                                                <th>Tax type</th>
                                                                <th>Tax value</th>
                                                            </tr>
                                                            <tr data-ng-repeat="tax in addMenuObj.menu.taxes">
                                                                <td>{{tax.tax_id}}</td>
                                                                <td>{{tax.tax_name}}</td>
                                                                <td>{{tax.tax_type}}</td>
                                                                <td>{{tax.tax_value}}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div data-ng-if="addMenuObj.restaurant_offers.length>0">
                                                        <h3>Offers</h3>
                                                        <table class="table bordered">
                                                            <tr>
                                                                <th>Offer id</th>
                                                                <th>Start Date</th>
                                                                <th>End Date</th>
                                                                <th>Offer Type</th>
                                                                <th>Discount Type</th>
                                                                <th>Min Order Amount</th>
                                                                <th>Discount Value</th>
                                                            </tr>
                                                            <tr data-ng-repeat="offer in addMenuObj.restaurant_offers">
                                                                <td>{{offer.offer_id}}</td>
                                                                <td>{{offer.start_date}}</td>
                                                                <td>{{offer.end_date}}</td>
                                                                <td>{{offer.offer_type}}</td>
                                                                <td>{{offer.discount_type}}</td>
                                                                <td>{{offer.min_order_amount}}</td>
                                                                <td>{{offer.discount_value}}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div data-ng-if="unit.newMenu == true">
                                                <div data-ng-if="showMenuObj != null && selectedMenuType != 'SINGLE_SERVE'">
                                                    <h3>Menu Items</h3>
                                                    <div data-ng-repeat="cat in showMenuObj.menu.categories | orderBy: 'order' track by $index">
                                                        <div style="background: #69ac6a; padding:15px;margin: 10px 0 0 0;">
                                                            <h4 style="display: inline;">
                                                                Category:
                                                                {{cat.name == 'Others' ? 'Unmapped products' :
                                                                cat.name}}
                                                            </h4>
                                                            <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                        line-height: 10px;padding: 8px;float: right;">
                                                                {{cat.order}}
                                                            </span>
                                                        </div>
                                                        <div data-ng-if="cat.subCategories.length > 0"
                                                             style="border: #ccc 1px solid;padding: 10px;">
                                                            <div data-ng-repeat="subCat in cat.subCategories | orderBy: 'order' track by $index">
                                                                <div style="background: #e2af2e; padding:15px;margin: 10px 0 0 0;">
                                                                    <h4 style="display: inline;">Sub-category:
                                                                        {{subCat.name}}</h4>
                                                                    <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                                line-height: 10px;padding: 8px;float: right;">
                                                                    {{subCat.order}}
                                                                </span>
                                                                </div>
                                                                <table class="table bordered"
                                                                       data-ng-if="subCat.entities.length > 0">
                                                                    <tr>
                                                                        <th>Product Name</th>
                                                                        <th>Product Tags</th>
                                                                        <th>Order</th>
                                                                        <th>Filtered</th>
                                                                        <th>Customizations</th>
                                                                    </tr>
                                                                    <tr data-ng-repeat="item in subCat.entities | orderBy: 'order' track by $index">
                                                                        <td>{{item.item.name}}</td>
                                                                        <td>
                                                                            <span data-ng-repeat="tag in item.item.tags">
                                                                                {{tag}}
                                                                                <font ng-show="!$last">,</font>
                                                                            </span>
                                                                        </td>
                                                                        <td>{{item.order}}</td>
                                                                        <td>
                                                                            {{filteredProducts.indexOf(item.item.vendorEntityId)
                                                                            >= 0}}
                                                                        </td>
                                                                        <td>
                                                                            <span data-ng-repeat="group in item.item.properties">
                                                                                <strong>{{group.name}}:</strong>
                                                                                <span data-ng-repeat="gitem in group.propertyValues">{{gitem.value}} </span>
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div data-ng-if="showMenuObj.menu.charges.length>0">
                                                        <h3>Charges</h3>
                                                        <table class="table bordered">
                                                            <tr>
                                                                <th>Charge name</th>
                                                                <th>Charge value</th>
                                                                <th>Charge type</th>
                                                                <th>Charge taxes</th>
                                                            </tr>
                                                            <tr data-ng-repeat="charge in showMenuObj.charges">
                                                                <td>PACKAGING</td>
                                                                <td>{{charge.chargeValue}}</td>
                                                                <td>
                                                                    {{charge.vendorEntityId == "PC_D_P" ? 'PERCENTAGE' :
                                                                    'FIXED'}}
                                                                </td>
                                                                <td>{{charge.taxes.join()}}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div data-ng-if="showMenuObj.restaurant_offers.length>0">
                                                        <h3>Offers</h3>
                                                        <table class="table bordered">
                                                            <tr>
                                                                <th>Offer id</th>
                                                                <th>Start Date</th>
                                                                <th>End Date</th>
                                                                <th>Offer Type</th>
                                                                <th>Discount Type</th>
                                                                <th>Min Order Amount</th>
                                                                <th>Discount Value</th>
                                                            </tr>
                                                            <tr data-ng-repeat="offer in addMenuObj.restaurant_offers">
                                                                <td>{{offer.offer_id}}</td>
                                                                <td>{{offer.start_date}}</td>
                                                                <td>{{offer.end_date}}</td>
                                                                <td>{{offer.offer_type}}</td>
                                                                <td>{{offer.discount_type}}</td>
                                                                <td>{{offer.min_order_amount}}</td>
                                                                <td>{{offer.discount_value}}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div data-ng-if="showMenuObj != null && selectedMenuType == 'SINGLE_SERVE'">
                                                    <div style="background: #69ac6a; padding:15px;margin: 10px 0 0 0;">
                                                        <h4 style="display: inline;">Single-Serve Menu</h4>
                                                        </span>
                                                    </div>
                                                    <table class="table bordered">
                                                        <tr>
                                                            <th>Product Name</th>
                                                            <th>Product taxes</th>
                                                            <th>Description</th>
                                                        </tr>
                                                        <tr data-ng-repeat="item in showMenuObj.menu.catalogues track by $index">
                                                            <td>{{item.name}}</td>
                                                            <td>{{item.taxGroups[0].slug}}</td>
                                                            <td>{{item.description}}</td>
                                                            <td>
                                                                <span data-ng-repeat="group in item.properties">
                                                                    <strong>{{group.name}}:</strong>
                                                                    <span data-ng-repeat="gitem in group.propertyValues">{{gitem.value}} </span>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <div data-ng-if="selectedPartner.partnerName == 'SWIGGY'">

                                            <div data-ng-if="unit.newMenu == true">
                                                <div data-ng-if="addMenuObj != null">
                                                    <h3>Menu Items</h3>
                                                    <div data-ng-repeat="cat in addMenuObj.entity.main_categories | orderBy: '-order' track by $index">
                                                        <div style="background: #69ac6a; padding:15px;margin: 10px 0 0 0;"
                                                             data-ng-if="cat.items.length > 0">
                                                            <h4 style="display: inline;">Category: {{cat.name ==
                                                                'Others' ? 'Unmapped products' : cat.name}}</h4>
                                                            <h5 style="display: inline;"> ( id: {{cat.id}} ) </h5>
                                                            <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                        line-height: 10px;padding: 8px;float: right;">
                                                            {{cat.order}}
                                                        </span>
                                                        </div>

                                                        <div data-ng-if="cat.sub_categories.length > 0"
                                                             style="border: #ccc 1px solid;padding: 10px;">
                                                            <div data-ng-repeat="subCat in cat.sub_categories | orderBy: '-order' track by $index">
                                                                <div style="background: #e2af2e; padding:15px;margin: 10px 0 0 0;">
                                                                    <h4 style="display: inline;">Sub-category:
                                                                        {{subCat.name}}</h4>
                                                                    <h5 style="display: inline;"> ( id: {{subCat.id}}
                                                                        ) </h5>
                                                                    <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;
                                                                line-height: 10px;padding: 8px;float: right;">
                                                                    {{subCat.order}}
                                                                </span>
                                                                </div>
                                                                <table class="table bordered"
                                                                       data-ng-if="addMenuObj.entity.items.length > 0">
                                                                    <tr>
                                                                        <th>Id</th>
                                                                        <th>Product Name</th>
                                                                        <th>Image</th>
                                                                        <th>Description</th>
                                                                        <th>Is Veg</th>
                                                                        <th>Price</th>
                                                                        <th>Tax Details</th>
                                                                        <th>Packing Charges</th>
                                                                        <th>Variants</th>
                                                                        <th>Add Ons</th>
                                                                    </tr>
                                                                    <tr data-ng-repeat="item in addMenuObj.entity.items | orderBy: '-order' track by item.id"
                                                                        data-ng-if="item.category_id == cat.id && item.sub_category_id == subCat.id">
                                                                        <td>{{item.id}}</td>
                                                                        <td>{{item.name}}</td>
                                                                        <td><img src="{{item.image_url}}" width="60"
                                                                                 height="60"></td>
                                                                        <td>{{item.description}}</td>
                                                                        <td>{{item.is_veg}}</td>
                                                                        <td>{{item.price | number:2}}</td>
                                                                        <th>
                                                                            <table class="table bordered"
                                                                                   data-ng-if="item.gst_details != null">
                                                                                <tr>
                                                                                    <th>IGST</th>
                                                                                    <th>SGST</th>
                                                                                    <th>CGST</th>
                                                                                    <th>Inclusive</th>
                                                                                </tr>
                                                                                <tr data-ng-if="item.gst_details != null">
                                                                                    <td>{{item.gst_details.igst}}</td>
                                                                                    <td>{{item.gst_details.sgst}}</td>
                                                                                    <td>{{item.gst_details.cgst}}</td>
                                                                                    <td>{{item.gst_details.inclusive}}
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </th>
                                                                        <td>{{item.packing_charges | number:2}}</td>
                                                                        <!--Variants-->
                                                                        <td>
                                                                            <div data-ng-if="item.variant_groups.length > 0">
                                                                                <div data-ng-repeat="variantGroup in item.variant_groups | orderBy: 'order' track by variantGroup.id">
                                                                                    <div style="background: #9BD7D5; padding:15px;margin: 10px 0 0 0;">
                                                                                        <h4 style="display: inline;">
                                                                                            Variant Group:
                                                                                            {{variantGroup.name}}</h4>
                                                                                        <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;line-height: 10px;padding: 8px;float: right;">
                                                                                            {{variantGroup.order}}
                                                                                    </span>
                                                                                    </div>
                                                                                    <table class="table bordered"
                                                                                           data-ng-if="variantGroup.variants.length > 0">
                                                                                        <tr>
                                                                                            <th>Id</th>
                                                                                            <th>Name</th>
                                                                                            <th>Price</th>
                                                                                            <th>Taxes</th>
                                                                                        </tr>
                                                                                        <tr data-ng-repeat="variant in variantGroup.variants | orderBy: 'order' track by variant.id">
                                                                                            <td>{{variant.id}}</td>
                                                                                            <td>{{variant.name}}</td>
                                                                                            <td>{{variant.price |
                                                                                                number:2}}
                                                                                            </td>
                                                                                            <td>
                                                                                                <table class="table bordered"
                                                                                                       data-ng-if="variant.gst_details != null">
                                                                                                    <tr>
                                                                                                        <th>IGST</th>
                                                                                                        <th>SGST</th>
                                                                                                        <th>CGST</th>
                                                                                                        <th>Inclusive
                                                                                                        </th>
                                                                                                    </tr>
                                                                                                    <tr data-ng-if="variant.gst_details != null">
                                                                                                        <td>
                                                                                                            {{variant.gst_details.igst}}
                                                                                                        </td>
                                                                                                        <td>
                                                                                                            {{variant.gst_details.sgst}}
                                                                                                        </td>
                                                                                                        <td>
                                                                                                            {{variant.gst_details.cgst}}
                                                                                                        </td>
                                                                                                        <td>
                                                                                                            {{variant.gst_details.inclusive}}
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <!--Add Ons-->
                                                                        <td>
                                                                            <div data-ng-if="item.variant_groups.length >= 0">
                                                                                <div data-ng-repeat="addOnGroup in item.addon_groups | orderBy: 'order' track by addOnGroup.id">
                                                                                    <div style="background: #FFF5C3; padding:15px;margin: 10px 0 0 0;">
                                                                                        <h4 style="display: inline;">Add
                                                                                            On Group:
                                                                                            {{addOnGroup.name}}</h4>
                                                                                        <span style="background: #fff;font-size: 18px;border-radius: 50%;display: inline-block;line-height: 10px;padding: 8px;float: right;">
                                                                                            {{addOnGroup.order}}
                                                                                    </span>
                                                                                    </div>
                                                                                    <table class="table bordered"
                                                                                           data-ng-if="addOnGroup.addons.length > 0">
                                                                                        <tr>
                                                                                            <th>AddOn Id</th>
                                                                                            <th>AddOn Name</th>
                                                                                        </tr>
                                                                                        <tr data-ng-repeat="addOn in addOnGroup.addons | orderBy: 'order' track by addOn.id">
                                                                                            <td>{{addOn.id}}</td>
                                                                                            <td>{{addOn.name}}</td>
                                                                                            <!--<td>-->
                                                                                                <!--<div data-ng-show="addOnGroup.name=='PaidAddons'">-->
                                                                                                    <!--{{addOn.price}}-->
                                                                                                <!--</div>-->
                                                                                            <!--</td>-->

                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                        </td>

                                                                    </tr>
                                                                </table>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div data-ng-if="unit.newMenu != true">
                                                <div data-ng-if="addMenuObj != null">
                                                    <h3>Menu Items</h3>
                                                    <table class="table bordered table-striped">
                                                        <tr>
                                                            <td>restaurant Id</td>
                                                            <td>Category Id</td>
                                                            <td>Category Name</td>
                                                            <td>Category Order</td>
                                                            <td>sub Category Id</td>
                                                            <td>sub Category Name</td>
                                                            <td>sub Category Order</td>
                                                            <td>Name</td>
                                                            <td>description</td>
                                                            <td>Parent</td>
                                                            <td>Variant Group Id</td>
                                                            <td>Variant Group Name</td>
                                                            <td>Variant Group Order</td>
                                                            <td>Variant Default Value</td>
                                                            <td>Order</td>
                                                            <td>price</td>
                                                            <td>veg egg non</td>
                                                            <td>packing Charges</td>
                                                            <td>SGST</td>
                                                            <td>CGST</td>
                                                            <td>external id</td>
                                                        </tr>
                                                        <tr data-ng-repeat="item in addMenuObj.items track by $index">
                                                            <td>{{item.restaurantId}}</td>
                                                            <td>{{item.mainCategoryId}}</td>
                                                            <td>{{item.mainCategoryName}}</td>
                                                            <td>{{item.mainCategoryOrder}}</td>
                                                            <td>{{item.subCategoryId}}</td>
                                                            <td>{{item.subCategoryName}}</td>
                                                            <td>{{item.subCategoryOrder}}</td>
                                                            <td>{{item.Name}}</td>
                                                            <td>{{item.description}}</td>
                                                            <td>{{item.Parent}}</td>
                                                            <td>{{item['Variant Group Id']}}</td>
                                                            <td>{{item['Variant Group Name']}}</td>
                                                            <td>{{item['Variant Group Order']}}</td>
                                                            <td>{{item['Variant Default Value']}}</td>
                                                            <td>{{item.Order}}</td>
                                                            <td>{{item.price}}</td>
                                                            <td>{{item.veg_egg_non}}</td>
                                                            <td>{{item.packingCharges}}</td>
                                                            <td>{{item.Item_SGST}}</td>
                                                            <td>{{item.Item_CGST}}</td>
                                                            <td>{{item.external_id}}</td>
                                                        </tr>
                                                    </table>
                                                    <h3>Menu Addons</h3>
                                                    <table class="table bordered table-striped">
                                                        <tr>
                                                            <td>Rest Id</td>
                                                            <td>Items Id</td>
                                                            <td>Addon Name</td>
                                                            <td>Addon Order</td>
                                                            <td>Addon Price</td>
                                                            <td>Addon IsVeg</td>
                                                            <td>Addon Instock</td>
                                                            <td>AddonGroup Name</td>
                                                            <td>external_addon_id</td>
                                                            <td>Addon_SGST</td>
                                                            <td>Addon_CGST</td>
                                                        </tr>
                                                        <tr data-ng-repeat="item in addMenuObj.addons track by $index">
                                                            <td>{{item['Rest Id']}}</td>
                                                            <td>{{item['Items Id']}}</td>
                                                            <td>{{item['Addon Name']}}</td>
                                                            <td>{{item['Addon Order']}}</td>
                                                            <td>{{item['Addon Price']}}</td>
                                                            <td>{{item['Addon IsVeg']}}</td>
                                                            <td>{{item['Addon Instock']}}</td>
                                                            <td>{{item['AddonGroup Nam']}}</td>
                                                            <td>{{item.external_addon_id}}</td>
                                                            <td>{{item.Addon_SGST}}</td>
                                                            <td>{{item.Addon_CGST}}</td>
                                                        </tr>
                                                    </table>
                                                    <h3>Unmapped Products</h3>
                                                    <table class="table bordered" style="background: #ea8080;">
                                                        <tr>
                                                            <td>Id</td>
                                                            <td>Name</td>
                                                        </tr>
                                                        <tr data-ng-repeat="item in addMenuObj.unmappedProducts track by item.id">
                                                            <td>{{item.id}}</td>
                                                            <td>{{item.name}}</td>
                                                        </tr>
                                                    </table>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'UPDATE BULK MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to update menu changes for multiple units in bulk.</h3>
                    <p>In order to push menu, select partner, brand, menu type and region. Then select all units. click
                        on bulk upload</p>
                </div>
            </div>
            <div>
                <h3 class="pull-left">Set Menu</h3>
                <input type="button" class="pull-right btn btn-success btn-lg" value="Back"
                       data-ng-if="bulkUploadStarted == true" data-ng-click="backToBulkUpload()"/>
            </div>

            <div class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-12" data-ng-show="bulkUploadStarted == false">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Menu Type</label>
                                <select class="form-control"
                                        data-ng-options="type for type in menuType "
                                        data-ng-model="selectedMenuType"
                                        data-ng-change="setSelectedMenuType(selectedMenuType)"
                                >
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Show Units"
                                       data-ng-click="setFilteredUnits()">
                            </div>
                            <!--<div class="form-group">
                                <label>Select Region</label>
                                <select class="form-control"
                                        data-ng-options="region as region.name for region in regions track by region.id"
                                        data-ng-model="selectedRegion"
                                        data-ng-change="setSelectedRegion(selectedRegion)">
                                </select>
                            </div>-->

                            <div class="row">
                                <div class="col-xs-4">
                                    <span class="checkbox">
                                        <label>
                                            <input type="checkbox" data-ng-model="bulkAddPackaging"
                                                   data-ng-change="setBulkAddPackaging(bulkAddPackaging)"> Add packaging
                                        </label>
                                    </span>
                                </div>
                                <div class="col-xs-4">
                                    <span class="checkbox">
                                        <label>
                                            <input type="checkbox"
                                                   data-ng-model="bulkSplitAllDesiChaiDimensions"
                                                   data-ng-change="setBulkSplitAllDesiChaiDimensions(bulkSplitAllDesiChaiDimensions)">
                                            Split All Desi Chai Dimensions
                                        </label>
                                    </span>
                                </div>
                                <div class="col-xs-2">
                                    <span class="checkbox">
                                        <label>
                                            <input type="checkbox" data-ng-model="bulkMiniKetliDefault"
                                                   data-ng-change="setBulkMiniKetliDefault(bulkMiniKetliDefault)"> Mini Ketli default
                                        </label>
                                    </span>
                                </div>
                                <div class="col-xs-2">
                                    <span class="checkbox">
                                        <label>
                                            <input type="checkbox" data-ng-model="bulkAddSeparateDimension"
                                                data-ng-change="toggleBulkSeparateDimension(bulkAddSeparateDimension)"> Add Separate Dimension Item
                                        </label>
                                    </span>
                                
                                    <!-- Dropdown shown conditionally -->
                                    <div class="form-group" data-ng-show="bulkAddSeparateDimension">
                                        <label for="bulkItemDimension">Item Dimension</label>
                                        <select id="bulkItemDimension" class="form-control" data-ng-model="bulkSelectedDimension"
                                            data-ng-change="onDimensionChangeBulk(bulkSelectedDimension)">
                                            <option value="ChotiKetli">Choti Ketli</option>
                                            <option value="BadiKetli">Badi Ketli</option>
                                            <option value="MiniKetli">Mini Ketli</option>
                                            <option value="Regular">Regular</option>
                                        </select>
                                        </div>
                                </div>

                                <div class="col-xs-4">
                                    <span class="checkbox">
                                        <label>
                                            <input type="checkbox" data-ng-model="bulkDealOfTheDay"
                                                   data-ng-change="setBulkDealOfTheDay(bulkDealOfTheDay)"> Deal Of The Day
                                        </label>
                                    </span>
                                </div>
                                <div class="col-xs-4">
                                    <span class="checkbox">
                                        <label>
                                            <input type="checkbox" data-ng-model="bulkClubAllDesiChaiDimensions"
                                                   data-ng-change="setBulkClubAllDesiChaiDimensions(bulkClubAllDesiChaiDimensions)"> Club All Desi Chai Dimensions
                                        </label>
                                    </span>
                                </div>
                            </div>

                            <div data-ng-if="filteredUnits != null && filteredUnits.length > 0">
                                <div class="form-group">
                                    <label>Select Units</label>
                                    <div class="row">
                                        <div class="col-xs-6 form-group">
                                            <input class="form-control" data-ng-model="unitSearch"/>
                                        </div>
                                        <div class="col-xs-6 pull-right">
                                            <label>Select All</label>
                                            <input type="checkbox" data-ng-model='selectAllUnits'
                                                   style="width: 20px; height: 20px"
                                                   data-ng-click="setSelectAllUnits()">
                                        </div>
                                    </div>

                                    <ul style="max-height: 400px;overflow: auto; list-style: none;">
                                        <li data-ng-repeat="unit in filteredUnits | filter:unitSearch | orderBy:'name' track by unit.id"
                                            data-ng-class="{'selected':unit.selected}" data-ng-click="selectUnit(unit)">
                                            {{unit.name}}
                                        </li>
                                    </ul>
                                </div>
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" value="Proceed for bulk upload"
                                           data-ng-click="proceedBulkMenuUpload()"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12" data-ng-show="bulkUploadStarted == true">
                            <div class="row" style="background: #efefef; padding: 10px;">
                                <div class="col-xs-3"><label>Partner:</label> {{selectedPartner.partnerName}}</div>
                                <div class="col-xs-3"><label>Brand:</label> {{selectedBrand.brandName}}</div>
                                <div class="col-xs-3"><label>Menu type:</label> {{selectedMenuType}}</div>
                                <div class="col-xs-3"><label>Partner:</label> {{selectedRegion.name}}</div>
                            </div>
                            <div class="row" style="margin-bottom: 10px; background: #efefef; padding: 10px;">
                                <div class="col-xs-3"><label>Region:</label> {{bulkAddPackaging}}</div>
                                <div class="col-xs-3"><label>Split dimension:</label> {{bulkSplitAllDesiChaiDimensions}}
                                </div>
                                <div class="col-xs-3"><label>MiniKetli default:</label> {{bulkMiniKetliDefault}}</div>
                            </div>
                            <div class="row" data-ng-repeat="unit in unitsForBulkUpload track by unit.id"
                                 style="padding: 15px; margin: 5px; border: #efefef 1px solid; font-weight: bold;">
                                <div class="col-xs-4">
                                    <div data-ng-if="unit.uploadStatus == 'WAITING'">{{unit.name}}</div>
                                    <div data-ng-if="unit.uploadStatus == 'GENERATING'" style="color: #ffa506;">
                                        {{unit.name}}
                                    </div>
                                    <div data-ng-if="unit.uploadStatus == 'COMPLETED'" style="color: #0cd21a;">
                                        {{unit.name}}
                                    </div>
                                    <div data-ng-if="unit.uploadStatus == 'SKIPPED'" style="color: #d2282d;">
                                        {{unit.name}}
                                    </div>
                                </div>
                                <div class="col-xs-2 text-center">
                                    <div data-ng-if="unit.uploadStatus == 'WAITING'">{{unit.uploadStatus}}...</div>
                                    <div style="margin: -10px" data-ng-if="unit.uploadStatus == 'GENERATING'">
                                        <img src="img/loader.gif" style="height: 40px;">
                                    </div>
                                    <div data-ng-if="unit.uploadStatus == 'COMPLETED'">
                                        <span class="fa fa-check" style="color: green; font-size: 32px;"/>
                                    </div>
                                    <div data-ng-if="unit.uploadStatus == 'SKIPPED'">
                                        <span class="fa fa-times" style="color: green; font-size: 32px;"/>
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    {{unit.uploadStatus}}
                                </div>
                                <div class="col-xs-4">
                                    <div data-ng-if="unit.id === selectedUnit.id">
                                        {{detailLoaderMessage}}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12 pull-right">
                                    <input type="button" class="btn btn-primary" value="Start Upload"
                                           data-ng-click="startBulkUpload()">
                                    <input type="button" class="btn btn-primary" value="Copy Skipped Units"
                                           data-ng-click="copyUnitsWithStatus('SKIPPED')">
                                    <input type="button" class="btn btn-primary" value="Copy Completed Units"
                                           data-ng-click="copyUnitsWithStatus('COMPLETED')">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div data-ng-if="selectedAction == 'PUSH UNIT MENU'">
        <div class="row">
            <div class="col-xs-12">
                <h3>Use this panel to push menu to partner units.</h3>
                <p>In order to push menu, first select units from Select Units and then select Partner and brand to push
                    menu on specific partner using
                    panel.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <div class="pull-right">
                    <label>Select All</label>
                    <input type="checkbox" data-ng-model='selectAllUnits'
                           style="width: 20px; height: 20px" data-ng-click="setSelectAllUnits()">
                </div>
                <div class="form-group">
                    <label>Select Units</label>
                    <ul style="max-height: 400px;overflow: auto; list-style: none;">
                        <li data-ng-repeat="unit in unitList | orderBy:'name' track by unit.id"
                            data-ng-class="{'selected':unit.selected}" data-ng-click="selectUnit(unit)">
                            {{unit.name}}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label>Select Partner</label>
                    <select class="form-control"
                            data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                            data-ng-model="selectedPartner"
                            data-ng-change="setSelectedPartner(selectedPartner)">
                    </select>
                </div>
                <div class="form-group">
                    <label>Select Brand</label>
                    <select class="form-control"
                            data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                            data-ng-model="selectedBrand"
                            data-ng-change="setSelectedBrand(selectedBrand)">
                    </select>
                </div>
                <div class="form-group">
                    <label>Menu Type</label>
                    <select class="form-control"
                            data-ng-options="type for type in menuType "
                            data-ng-model="selectedMenuType"
                            data-ng-change="setSelectedMenuType(selectedMenuType)"
                    >
                    </select>
                </div>

                <div class="form-group">
                    <input type="button" class="btn btn-primary" value="Push Menu" data-ng-click="pushMenuToUnit()"/>
                    <input type="button" class="btn btn-primary pull-right" value="Track Menu"
                           data-ng-click="trackSwiggyMenu()" data-ng-if="selectedPartner.partnerName == 'SWIGGY'"/>
                </div>

                <div data-ng-if="trackSwiggyMenuStatus != null">
                    <div>Swiggy menu status responses:</div>
                    <div>{{trackSwiggyMenuStatus.name}} : {{trackSwiggyMenuStatus.response}}</div>
                </div>

            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                    <h4 data-ng-if="offers != null && offers.length > 0">Offers</h4>
                    <table class="table table-bordered" data-ng-if="offers != null && offers.length > 0">
                        <tr>
                            <th>Unit Name</th>
                            <th>Coupon Code</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Discount Type</th>
                            <th>Discount Value</th>
                            <th>Min Order Amount</th>
                            <th>Is Active</th>
                            <th>Actions</th>
                        </tr>
                        <tr data-ng-repeat="offer in offers track by offer.id">
                            <td>{{unitMap[offer.unitId]}}</td>
                            <td>{{offer.couponCode}}</td>
                            <td>{{offer.offerData.start_date}}</td>
                            <td>{{offer.offerData.end_date}}</td>
                            <td>{{offer.offerData.discount_type == "" || offer.offerData.discount_type == null ?
                                offer.offerData.offer_type : offer.offerData.discount_type}}
                            </td>
                            <td>{{offer.offerData.discount_value}}</td>
                            <td>{{offer.offerData.min_order_amount}}</td>
                            <td>{{offer.active}}</td>
                            <td>
                                <input type="button" class="btn btn-danger" value="Deactivate"
                                       data-ng-if="offer.active" data-ng-click="updateOfferStatus(offer, false)"/>
                                <input type="button" class="btn btn-success" value="Activate"
                                       data-ng-if="offer.active == false"
                                       data-ng-click="updateOfferStatus(offer, true)"/>
                            </td>
                        </tr>
                    </table>

                </div>

            </div>
        </div>
    </div>


    <div data-ng-if="selectedAction == 'MANAGE OFFER'">
        <div class="row">
            <div class="col-xs-12">
                <h3>Use this panel to set run offer on partner menu.</h3>
                <p>TO run any offer, first create the offer from this panel and then update menu using update menu
                    panel.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <div class="form-group">
                    <label>Select Units</label>
                    <ul style="max-height: 400px;overflow: auto; list-style: none;">
                        <li data-ng-repeat="unit in unitList | orderBy:'name' track by unit.id"
                            data-ng-class="{'selected':unit.selected}" data-ng-click="selectUnit(unit)">
                            {{unit.name}}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label>Select Partner</label>
                    <select class="form-control"
                            data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                            data-ng-model="selectedPartner"
                            data-ng-change="setSelectedPartner(selectedPartner)">
                    </select>
                </div>

                <div class="form-group">
                    <input type="button" class="btn btn-primary" value="Add Offers" data-ng-click="showAddOffer()"/>
                    <input type="button" class="btn btn-primary" value="Get Active Offers" data-ng-click="getOffers()"/>
                </div>

                <div class="row" data-ng-show="showAddNewOffer">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label>Offer Type</label>
                            <select class="form-control" data-ng-model="offerType">
                                <option value=""></option>
                                <option value="DISCOUNT">DISCOUNT</option>
                                <option value="BOGO">BOGO</option>
                            </select>
                        </div>
                        <div data-ng-show="offerType=='DISCOUNT'">
                            <div class="form-group">
                                <label>Coupon Code</label>
                                <input type="text" id="couponCode" class="form-control"
                                       style="text-transform: uppercase;"
                                       data-ng-model="couponCode" data-ng-change="setCouponCode(couponCode)"/>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Find Offer"
                                       data-ng-click="couponSearch()"/>
                            </div>
                        </div>
                        <div data-ng-show="offerType=='BOGO'">
                            <div class="form-group">
                                <label>Start Date</label>
                                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                                    <input class="form-control" data-ng-model="startDate" type="text"
                                           placeholder="yyyy-MM-dd" required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>End Date</label>
                                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                                    <input class="form-control" data-ng-model="endDate" type="text"
                                           placeholder="yyyy-MM-dd" required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Submit"
                                       data-ng-click="setBogo(startDate, endDate)"/>
                            </div>
                        </div>

                        <h4 data-ng-if="offer != null">Offer Detail</h4>
                        <table class="table table-bordered" data-ng-if="offer != null">
                            <tr>
                                <th>Discount Type</th>
                                <th>Discount Value</th>
                                <th>Min Order Amount</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Is Active</th>
                            </tr>
                            <tr>
                                <td>{{offer.offerData.discount_type}}</td>
                                <td>{{offer.offerData.discount_value}}</td>
                                <td>{{offer.offerData.min_order_amount}}</td>
                                <td>{{offer.offerData.start_date}}</td>
                                <td>{{offer.offerData.end_date}}</td>
                                <td>{{offer.offerData.is_active}}</td>
                            </tr>
                        </table>
                        <div class="form-group">
                            <input type="button" class="btn btn-primary" value="Add Offer"
                                   data-ng-if="offer != null" data-ng-click="addNewOffer()"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                    <h4 data-ng-if="offers != null && offers.length > 0">Offers</h4>
                    <table class="table table-bordered" data-ng-if="offers != null && offers.length > 0">
                        <tr>
                            <th>Unit Name</th>
                            <th>Coupon Code</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Discount Type</th>
                            <th>Discount Value</th>
                            <th>Min Order Amount</th>
                            <th>Is Active</th>
                            <th>Actions</th>
                        </tr>
                        <tr data-ng-repeat="offer in offers track by offer.id">
                            <td>{{unitMap[offer.unitId]}}</td>
                            <td>{{offer.couponCode}}</td>
                            <td>{{offer.offerData.start_date}}</td>
                            <td>{{offer.offerData.end_date}}</td>
                            <td>{{offer.offerData.discount_type == "" || offer.offerData.discount_type == null ?
                                offer.offerData.offer_type : offer.offerData.discount_type}}
                            </td>
                            <td>{{offer.offerData.discount_value}}</td>
                            <td>{{offer.offerData.min_order_amount}}</td>
                            <td>{{offer.active}}</td>
                            <td>
                                <input type="button" class="btn btn-danger" value="Deactivate"
                                       data-ng-if="offer.active" data-ng-click="updateOfferStatus(offer, false)"/>
                                <input type="button" class="btn btn-success" value="Activate"
                                       data-ng-if="offer.active == false"
                                       data-ng-click="updateOfferStatus(offer, true)"/>
                            </td>
                        </tr>
                    </table>

                </div>

            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'BOGO PRODUCTS'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to set BOGO products for partner menu.</h3>
                    <p>To run BOGO offer, first set BOGO products from this panel. Then, add BOGO offer from manage
                        offer panel and
                        then finally update the menu from update menu panel</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>BOGO Products</h3>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Products"
                                       data-ng-click="getUnitProducts()"/>
                            </div>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-6">
                            <ul style="max-height: 400px;overflow: auto; list-style: none; width: 300px;">
                                <li data-ng-repeat="product in unitProductList | filter:{classification:'MENU', type:'!12'}
                                | orderBy:'detail.name' track by product.detail.id"
                                    data-ng-class="{'selected':product.selected}"
                                    data-ng-click="product.selected == true? product.selected = false:product.selected=true">
                                    {{product.detail.name}}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="row" data-ng-if="showUnitProducts">
                        <div class="col-xs-12" style="text-align: right;">
                            <button type="button" class="btn btn-success"
                                    data-ng-click="setBogoProducts(true)">Set BOGO
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'CAFE MENU AUTO PUSH'">
        <div class="row">
            <div class="col-xs-12">
                <h3> CAFE MENU AUTO PUSH </h3>
                <p> TO SELECT THE CAFE AND PARTNER TO PUSH MENU</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-4">
                <div class="form-group">
                    <label>Select Brand</label>
                    <select class="form-control"
                            data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                            data-ng-model="selectedBrand"
                            data-ng-change="setSelectedBrand(selectedBrand)">
                    </select>
                </div>
                <div class="form-group">
                    <input type="button" class="btn btn-primary" value="SELECT"
                           data-ng-click="getCafeUnitList()"/>
                </div>
            </div>
            <div class="col-xs-12">
                <table class="table table-bordered" data-ng-if="partnerUnitList.length >0">
                    <thead style="background-color: #50773e; color: #ffffff">
                    <tr>
                        <th>CHECK <input type="checkbox" data-ng-model='checkBoxModal.checkAll'
                                         style="width: 33px; height: 20px" data-ng-click="updateCheck()"></th>
                        <th>UNIT ID</th>
                        <th>UNIT NAME</th>
                        <th> ZOMATO <input type="checkbox" data-ng-model='checkBoxModal.checkAllZomato'
                                           data-ng-click="updateZomato()" style="width: 33px; height: 20px"></th>
                        <th> SWIGGY <input type="checkbox" data-ng-model='checkBoxModal.checkAllSwiggy'
                                           data-ng-click="updateSwiggy()" style="width: 33px; height: 20px"></th>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="unit in partnerUnitList  track by $index" style="cursor: pointer">
                        <td><input type="checkbox" data-ng-model='unit.checked'
                                   data-ng-disabled="unit.unitPartnerZomato==false &&  unit.unitPartnerSwiggy==false"
                                   style="width: 33px; height: 20px">
                        </td>
                        <td>{{unit.unitId}}</td>
                        <td>{{unit.unitName}}</td>
                        <td data-ng-click="singleClickZomato(unit.unitId,true)">
                            <input type="checkbox" data-ng-model='unit.zomato'
                                   data-ng-click="singleClickZomato(unit.unitId,true)"
                                   data-ng-disabled="unit.unitPartnerZomato==false" data-ng-checked="unit.zomato"
                                   style="width: 33px; height: 20px">
                        </td>
                        <td data-ng-click="singleClickSwiggy(unit.unitId,true)">
                            <input type="checkbox" data-ng-model='unit.swiggy'
                                   data-ng-click="singleClickSwiggy(unit.unitId,true)"
                                   data-ng-disabled="unit.unitPartnerSwiggy==false" data-ng-checked="unit.swiggy"
                                   style="width: 33px; height: 20px">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" colspan="8">
                            <button class="btn btn-primary pull-right" data-ng-click="sendUpdatedList()">
                                SUBMIT
                            </button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'PRODUCT PACKAGING CHARGES'">
        <div class="row">
            <div class="col-xs-12">
                <h3>Use this panel to Map Packaging Charges With Products</h3>
            </div>
        </div>
        <div class="form-group">
            <label>Select Brand</label>
            <select class="form-control"
                    data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                    data-ng-model="selectedBrand"
                    data-ng-change="setSelectedBrand(selectedBrand)">
            </select>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <div class="pull-right">
                    <label>Select All</label>
                    <input type="checkbox" data-ng-model='selectAllPricingUnits'
                           style="width: 20px; height: 20px" data-ng-click="setSelectAllPricingUnits()">
                </div>
                <div class="form-group">
                    <label>Select Units</label>
                    <ul style="max-height: 400px;overflow: auto; list-style: none;">
                        <li data-ng-repeat="unit in pricingUnits | orderBy:'name' track by unit.id"
                            data-ng-class="{'selected':unit.selected}" data-ng-click="selectUnit(unit)">
                            {{unit.name}}
                        </li>
                    </ul>
                </div>
            </div>
            <input type="button" class="btn btn-primary" value="SELECT"
                   data-ng-click="getPartnerProducts()"/>
            <div class="col-xs-6">
                <div class="pull-right">
                    <label>Select All</label>
                    <input type="checkbox" data-ng-model='selectAllPartnerProducts'
                           style="width: 20px; height: 20px" data-ng-click="setSelectAllPartnerProducts()">
                </div>
                <div class="form-group">
                    <label>Select Products</label>
                    <ul style="max-height: 400px;overflow: auto; list-style: none;">
                        <li data-ng-repeat="product in productList | orderBy:'product.detail.name' track by product.detail.id"
                            data-ng-class="{'selected':product.selected}" data-ng-click="selectProduct(product)">
                            {{product.detail.name}}
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row">
            <input type="button" class="btn btn-primary" value="DOWNLOAD"
                   data-ng-click="downloadProductPackagingTemplate()"/>
        </div>

        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-2">
                        <label>Upload Unit Product Packaging  Sheet</label>
                    </div>
                    <div class="col-xs-4 row">
                        <input class="btn btn-default" style="width: 100%;" type="file"
                               file-model="productPackagingFile" accept="">
                        <button class="btn btn-primary" style="margin-top: 5px"
                                data-ng-click="uploadProductPackagingSheet()"
                                data-ng-disabled="isUndefinedOrNull(productPackagingFile)">Upload and update Packagings
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="upload-panel" data-ng-if="selectedAction == 'UPLOAD MENU MAPPINGS'">
        <div class="row">
            <div class="col-xs-12">
                <h3>Use this panel to Upload Menu Sequence And Mappings</h3>
            </div>
        </div>
        <div class="form-group">
            <label for="menuApp">Select Menu App:</label>
            <select id="menuApp" ng-model="selectedMenuApp" ng-change="onMenuAppChange(selectedMenuApp)">
                <option value="" disabled selected>Select a menu app</option>
                <option ng-repeat="app in menuApps" value="{{ app }}">{{ app }}</option>
            </select>
        </div>

        <div ng-if="selectedMenuApp != null" class="form-group">
            <button class="btn-custom" ng-click="downloadMappingExcel(selectedMenuApp, true)">Download Template</button>
        </div>

        <div ng-if="showMenuMappingsExcelDownload == true" class="form-group">
            <p><strong>Last Upload Time:</strong> {{ lastUploadTime || 'Not available' }}</p>
            <p><strong>Last Uploaded By:</strong> {{ lastUploadedBy || 'Not available' }}</p>
            <button class="btn-custom" ng-click="downloadMappingExcel(selectedMenuApp, false)">Download Last Uploaded File</button>
        </div>

        <form ng-submit="uploadAndParse()">
            <div class="form-group">
                <label>Select Sheets:</label>
                <div ng-repeat="sheet in sheetNames" style="margin-left: 10px;">
                    <input type="checkbox" ng-click="toggleSheetSelection(sheet)" /> {{ sheet }}
                </div>
            </div>

            <div class="form-group">
                <label for="file">Upload Excel File:</label>
                <input type="file" id="file" onchange="angular.element(this).scope().onFileChange(event)" />
                <p ng-if="file"><strong>Selected File:</strong> {{ file.name }}</p>
            </div>

            <button type="submit" class="btn-custom">Upload and Parse</button>
        </form>

        <div ng-if="errorsList.length > 0" class="error-section">
            <h3>Errors List:</h3>
            <textarea readonly rows="10">{{ errorsList.join('\n') }}</textarea>
            <button ng-click="copyErrorsToClipboard()" class="btn-custom"><i class="fas fa-copy"></i> Copy Errors</button>
        </div>
        <!--<pre>{{ model.changeLogs | json }}</pre>-->
        <div class="form-group">
            <h3>Change Logs</h3>

            <div ng-repeat="(logType, logDetails) in model.changeLogs" class="log-section">
                <!-- Top-level log type -->
                <h4 class="log-type-header">{{ logType }}</h4>

                <!-- Second level: UPDATE_CHANGES_LOG, NEW_CHANGES_LOG -->
                <div ng-repeat="(changeCategory, changes) in logDetails" class="log-category">
                    <h5 class="log-category-header">{{ changeCategory }}</h5>

                    <!-- Third level: MENU_SEQUENCE_SUB_CAT_MAPPING, MENU_EXCEL_SEQUENCE -->
                    <div ng-repeat="(changeType, changeList) in changes" class="log-type">
                        <h6 class="log-type-subheader">{{ changeType }}</h6>

                        <!-- Display list of changes -->
                        <ul class="change-list">
                            <li class="change-item" ng-repeat="item in changeList">{{ item }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </body>
    </html>

    <!--<div class="row" data-ng-if="selectedAction == 'ADD MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to push partner menu for newly mapped outlets or refresh menu for individual outlet.</h3>
                    <p>In order to push update, select outlet then select partner and press get menu. After menu is loaded press sync to partner button.</p>
                </div>
            </div>
            <div class="form-group">
                <label>Select Unit</label>
                <select class="form-control"
                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                        data-ng-model="selectedUnit"
                        data-ng-change="setSelectedUnit(selectedUnit)">
                </select>
            </div>
            <div class="form-group">
                <label>Select Partner</label>
                <select class="form-control"
                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                        data-ng-model="selectedPartner"
                        data-ng-change="setSelectedPartner(selectedPartner)">
                </select>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Get Menu" data-ng-click="getCurrentMenu()" />
            </div>
            <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                <div data-ng-if="currentMenu != null">
                    <h3>Menu Items</h3>
                    <div data-ng-repeat="cat in currentMenu.menuRequest.menu.categories">
                        <h4>Category: {{cat.category_name}}</h4>
                        <table class="table bordered">
                            <tr>
                                <th>Product Name</th>
                                <th>Product taxes</th>
                                <th>Bogo active</th>
                                <th>Treats active</th>
                                <th>Customizations</th>
                            </tr>
                            <tr data-ng-repeat="item in cat.items">
                                <td>{{item.item_name}}</td>
                                <td>{{item.item_taxes[0].taxes.join()}}</td>
                                <td>{{item.item_is_bogo_active}}</td>
                                <td>{{item.item_is_treats_active}}</td>
                                <td>
                                    <span data-ng-repeat="group in item.groups">
                                        <strong>{{group.group_name}}:</strong>
                                        <span data-ng-repeat="gitem in group.items">{{gitem.item_name}} </span>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div data-ng-if="currentMenu.menuRequest.menu.charges.length>0">
                        <h3>Charges</h3>
                        <table class="table bordered">
                            <tr>
                                <th>Charge name</th>
                                <th>Charge value</th>
                                <th>Always applicable</th>
                                <th>Applicable below</th>
                                <th>Charge type</th>
                                <th>Charge taxes</th>
                            </tr>
                            <tr data-ng-repeat="charge in currentMenu.menuRequest.menu.charges">
                                <td>{{charge.charge_name}}</td>
                                <td>{{charge.charge_value}}</td>
                                <td>{{charge.charge_always_applicable}}</td>
                                <td>{{charge.charge_applicable_below_order_amount}}</td>
                                <td>{{charge.charge_type}}</td>
                                <td>{{charge.charge_taxes[0].taxes.join()}}</td>
                            </tr>
                        </table>
                    </div>
                    <div data-ng-if="currentMenu.menuRequest.menu.taxes.length > 0">
                        <h3>Taxes</h3>
                        <table class="table bordered">
                            <tr>
                                <th>Tax id</th>
                                <th>Tax name</th>
                                <th>Tax type</th>
                                <th>Tax value</th>
                            </tr>
                            <tr data-ng-repeat="tax in currentMenu.menuRequest.menu.taxes">
                                <td>{{tax.tax_id}}</td>
                                <td>{{tax.tax_name}}</td>
                                <td>{{tax.tax_type}}</td>
                                <td>{{tax.tax_value}}</td>
                            </tr>
                        </table>
                    </div>
                    <div data-ng-if="currentMenu.menuRequest.restaurant_offers.length>0">
                        <h3>Offers</h3>
                        <table class="table bordered">
                            <tr>
                                <th>Offer id</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Offer Type</th>
                                <th>Discount Type</th>
                                <th>Min Order Amount</th>
                                <th>Discount Value</th>
                            </tr>
                            <tr data-ng-repeat="offer in currentMenu.menuRequest.restaurant_offers">
                                <td>{{offer.offer_id}}</td>
                                <td>{{offer.start_date}}</td>
                                <td>{{offer.end_date}}</td>
                                <td>{{offer.offer_type}}</td>
                                <td>{{offer.discount_type}}</td>
                                <td>{{offer.min_order_amount}}</td>
                                <td>{{offer.discount_value}}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="form-group">
                    <input type="button" class="btn btn-primary" value="Sync to Partner" data-ng-click="refreshMenu()" />
                </div>
            </div>
        </div>
    </div>-->

    <!--<div class="row" data-ng-if="selectedAction == 'UPDATE MENU'">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Use this panel to update menu changes across system.</h3>
                    <p>In order to push menu, select each region and their corresponding first outlet, then select partner and press get menu. Once menu is loaded, press add menu.
                        Do this activity for all the regions where you want to update menu.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Set Menu</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Region</label>
                                <select class="form-control"
                                        data-ng-options="region as region.name for region in regions track by region.id"
                                        data-ng-model="selectedRegion"
                                        data-ng-change="setSelectedRegion(selectedRegion)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in filteredUnits track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.partnerName for partner in channelPartners track by partner.partnerId"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Menu"
                                       data-ng-click="getMenuToAdd()"/>
                            </div>
                            <div data-ng-if="selectedPartner.partnerName == 'ZOMATO'">
                                <div data-ng-if="addMenuObj != null">
                                    <h3>Menu Items</h3>
                                    <div data-ng-repeat="cat in addMenuObj.menu.categories">
                                        <h4>Category: {{cat.category_name}}</h4>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Product Name</th>
                                                <th>Product taxes</th>
                                                <th>Bogo active</th>
                                                <th>Treats active</th>
                                                <th>Customizations</th>
                                            </tr>
                                            <tr data-ng-repeat="item in cat.items">
                                                <td>{{item.item_name}}</td>
                                                <td>{{item.item_taxes[0].taxes.join()}}</td>
                                                <td>{{item.item_is_bogo_active}}</td>
                                                <td>{{item.item_is_treats_active}}</td>
                                                <td>
                                                <span data-ng-repeat="group in item.groups">
                                                    <strong>{{group.group_name}}:</strong>
                                                    <span data-ng-repeat="gitem in group.items">{{gitem.item_name}} </span>
                                                </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div data-ng-if="addMenuObj.menu.charges.length>0">
                                        <h3>Charges</h3>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Charge name</th>
                                                <th>Charge value</th>
                                                <th>Always applicable</th>
                                                <th>Applicable below</th>
                                                <th>Charge type</th>
                                                <th>Charge taxes</th>
                                            </tr>
                                            <tr data-ng-repeat="charge in addMenuObj.menu.charges">
                                                <td>{{charge.charge_name}}</td>
                                                <td>{{charge.charge_value}}</td>
                                                <td>{{charge.charge_always_applicable}}</td>
                                                <td>{{charge.charge_applicable_below_order_amount}}</td>
                                                <td>{{charge.charge_type}}</td>
                                                <td>{{charge.charge_taxes[0].taxes.join()}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div data-ng-if="addMenuObj.menu.taxes.length>0">
                                        <h3>Taxes</h3>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Tax id</th>
                                                <th>Tax name</th>
                                                <th>Tax type</th>
                                                <th>Tax value</th>
                                            </tr>
                                            <tr data-ng-repeat="tax in addMenuObj.menu.taxes">
                                                <td>{{tax.tax_id}}</td>
                                                <td>{{tax.tax_name}}</td>
                                                <td>{{tax.tax_type}}</td>
                                                <td>{{tax.tax_value}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div data-ng-if="addMenuObj.restaurant_offers.length>0">
                                        <h3>Offers</h3>
                                        <table class="table bordered">
                                            <tr>
                                                <th>Offer id</th>
                                                <th>Start Date</th>
                                                <th>End Date</th>
                                                <th>Offer Type</th>
                                                <th>Discount Type</th>
                                                <th>Min Order Amount</th>
                                                <th>Discount Value</th>
                                            </tr>
                                            <tr data-ng-repeat="offer in addMenuObj.restaurant_offers">
                                                <td>{{offer.offer_id}}</td>
                                                <td>{{offer.start_date}}</td>
                                                <td>{{offer.end_date}}</td>
                                                <td>{{offer.offer_type}}</td>
                                                <td>{{offer.discount_type}}</td>
                                                <td>{{offer.min_order_amount}}</td>
                                                <td>{{offer.discount_value}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group" data-ng-if="addMenuObj!=null">
                                <input type="button" class="btn btn-primary" value="Send Menu to Partner"
                                       data-ng-click="addPartnerMenu()"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>-->


</div>


<div class="modal fade" id="nonMatchedProductsModal" tabindex="-1" role="dialog"
     aria-labelledby="nonMatchedProductsModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="nonMatchedProductsModalLabel">Non available products</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <label style="color: red;">Following products were not found in cafe menu and hence they will
                            not be sent in the partner menu please look carefully.</label>
                        <table class="table table-bordered table-striped">
                            <tr>
                                <td>Product Id</td>
                                <td>Product Name</td>
                                <td>Dimension</td>
                            </tr>
                            <tr data-ng-repeat="item in nonMatchedProducts track by $index">
                                <td>{{item.id}}</td>
                                <td>{{item.name}}</td>
                                <td>{{item.dimension}}</td>
                            </tr>
                        </table>
                        <div class="form-group text-right">
                            <input type="button" class="btn btn-danger" value="Cancel" data-dismiss="modal"
                                   aria-label="Close">
                            <input type="button" class="btn btn-primary" value="Ok"
                                   data-ng-click="addPartnerMenuForUnit()">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="bulkUploadUnitSkipModal" tabindex="-1" role="dialog"
     aria-labelledby="bulkUploadUnitSkipModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="bulkUploadUnitSkipModalLabel">Skip Current Unit Upload</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <label style="color: red;">Upload for unit {{selectedUnit.name}} could not finished due to
                            errors.
                            Do you want to skip this unit for now and continue to next unit menu upload.</label>
                        <div class="form-group text-right">
                            <input type="button" class="btn btn-danger" value="Cancel" data-dismiss="modal"
                                   aria-label="Close">
                            <input type="button" class="btn btn-primary" value="Ok"
                                   data-ng-click="skipCurrentUnitBulkUpload()">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
