<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .multiselect-parent .dropdown-menu {
        width: 570px;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }
</style>

<div class="container-fluid partner-page" >

    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Server Restart Dashboard</h2>
    </div>
    <br>




    <div class="row" >
        <br>
        <div class="col-xs-12">
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Restart App Cache Server"
                       data-ng-click="restart('appCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Restart App CRM Server"
                       data-ng-click="restart('appCrm')"/>
            </div>

        </div>
       
    </div>
</div>
