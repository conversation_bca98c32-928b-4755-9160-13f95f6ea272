<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
	<div style align="right"><button class="btn btn-primary pull-right" ng-click="addCategoryData()">Add</button></div>
	<h1 class="page-header">
     Category List            
    </h1>
    </div>
	<div class="form-group">
                <label>Category *</label>
               	 <select class="form-control" ng-model="selectedCatData"  ng-options="categoryName for (categoryName,categoryList) in categoryDatas" ng-change="showCategoryData(selectedCatData)">
                </select> 
     </div>
     <div>
     <table cellpadding="0" border="1" cellspacing="0"  class="table table-hover table-striped">
        <tr id="heading">
         <th> S.No</th>
          <th>Name</th>
          <th>Code</th>
          <th>Group</th>
          <th>Status</th>          
           <th>Action</th>
        </tr>
        <tr ng-repeat='detDatas in showCatData'>
           <td>{{$index +1}}</td>
          <td>{{detDatas.detail.name}} </td>
          <td>{{detDatas.detail.code}}</td>
          <td>{{detDatas.detail.type}}</td>
          <td>{{detDatas.detail.status}} </td>
          <td>
          <img ng-click="activeNinactive(detDatas.detail.id,'IN_ACTIVE')" ng-if="detDatas.detail.status!='IN_ACTIVE'" style="margin-bottom:8px; cursor:pointer" title="Active" ng-src="img/activeCat.png" height="25px" width="25px">&nbsp;&nbsp;
          <img ng-click="activeNinactive(detDatas.detail.id,'ACTIVE',$index+1)" ng-if="detDatas.detail.status!='ACTIVE'" style="margin-bottom:8px; cursor:pointer" title="Active" ng-src="img/inactiveCat.png" height="25px" width="25px">&nbsp;&nbsp;
          <img ng-click="editCategoryDetails(detDatas.detail.id,detDatas.detail.type)" style="margin-bottom:8px;cursor:pointer" title="Edit Category" ng-src="img/updateCat.png" height="20px" width="20px">&nbsp;&nbsp;       
           </td> 
          </tr>
      </table>
      </div> 
      
      <form name="myForms" novalidate>
     <div class="modal fade" id="categoryModalData" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"> {{msg}}  Category Details</h4>
          </div>
          <div class="modal-body">
              <form name="addCatForm" novalidate>
               <div class="form-group">
                <label>Category *</label>
                <!-- <select class="form-control" ng-model="selectedCatDataList" ng-options="categoryName for categoryName as categoryName in categoryArrList" ng-change="addCatDetailData(selectedCatDataList.categoryName)">
                </select>  -->
                <select ng-model="selectedItem" class="form-control" ng-init="selectedItem=categoryArrList[0]" ng-change="addCatDetailData(selectedItem)">
        			<option ng-repeat="item in categoryArrList" value="{{item}}" >{{item}}</option>
      			</select>
            </div>
              <div class="form-group">
                  <label> Name *</label>
                  <input type="text" class="form-control" ng-model="catName" required />
              </div>
               <div class="form-group">
                  <label> Code *</label>
                  <input type="text" class="form-control"  ng-model="catCode" required />
              </div>
               <div class="form-group clearfix" ng-if="action=='AddCatData'">
                  <button class="btn btn-primary pull-right" ng-click="submitAddCategoryData()">Add</button>
              </div>
             <div class="form-group clearfix" ng-if="action=='UpdateCatAction'">
                  <button class="btn btn-primary pull-right" ng-click="submitUpdateCategory(updateIDS,catStatus,types)">Update</button>
              </div>
              </form>
          </div>
     </div>
  </div>
</div>
</form>
</div>