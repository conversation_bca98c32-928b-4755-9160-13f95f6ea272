
<div class="row" data-ng-init="init()">
    <h1 class="page-header">Bulk PnL Adjustment </h1>
</div>


<div class="panel panel-info">
    <div class="panel-heading">Update</div>

    <div class="panel-body">
        <div class="row">
            <div class="col-xs-6">Select Year and Month</div>
            <div class="col-xs-6">
                <div class="datepicker" data-date-format="yyyy-MM" max="{{maxDate}}">
                    <input class="form-control" data-ng-model="serviceDate" type="text" placeholder="yyyy-MM" required/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Download Service Charges Template</div>
            <div class="col-xs-6">
                <input type="button" class="btn btn-primary" value="Download"
                       data-ng-click="downloadTemplate(serviceDate,'SERVICE_CHARGES_BUDGET')"/>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">Upload Service Charges Document</div>
            <div class="col-xs-6">
                <input class="btn btn-default" style="width: 100%;" type="file"
                       file-model="fileToUpload">
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-ng-click="uploadSheet('SERVICE_CHARGES_BUDGET')"
                        data-ng-disabled="fileToUpload==null">Upload Service Charges Doc
                </button>

            </div>
        </div>
    </div>
</div>