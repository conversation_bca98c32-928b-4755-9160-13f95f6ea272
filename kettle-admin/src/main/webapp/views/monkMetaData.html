<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .row .col {
        margin: 5px 10px;
    }

    .popup {
        visibility: hidden;
        min-width: 250px;
        margin-left: -125px;
        background-color: #A91B0D;
        color: #fff;
        text-align: center;
        border-radius: 2px;
        padding: 16px;
        position: fixed;
        z-index: 1051;
        right: 20px;
        bottom: 30px;
        font-size: 17px;
    }

    .popup.show {
        visibility: visible;
        -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
        animation: fadein 0.5s, fadeout 0.5s 2.5s;
    }

    @-webkit-keyframes fadein {
        from {
            bottom: 0;
            opacity: 0;
        }
        to {
            bottom: 30px;
            opacity: 1;
        }
    }

    @keyframes fadein {
        from {
            bottom: 0;
            opacity: 0;
        }
        to {
            bottom: 30px;
            opacity: 1;
        }
    }

    @-webkit-keyframes fadeout {
        from {
            bottom: 30px;
            opacity: 1;
        }
        to {
            bottom: 0;
            opacity: 0;
        }
    }

    @keyframes fadeout {
        from {
            bottom: 30px;
            opacity: 1;
        }
        to {
            bottom: 0;
            opacity: 0;
        }
    }
</style>

<div class="row" ng-init="init()" style="padding:10px">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Monk  Meta Data

        </h1>

        <div class="form-group">
            <label>Image Type</label>
            <select class="form-control" data-ng-model="metaData"
            data-ng-options="metaData as metaData.name
            for metaData in metaDataList">
            </select>
        </div>


        <div class="col-xs-12">

            <div class="col-xs-12" data-ng-if="metaData.value != 'video'">
                <lable>Photo: </lable>
                <input class="btn btn-default" file-model="fileToUpload" type="file">
                <i ng-show="myForm.file.$error.required">*required</i><br>
                <i ng-show="myForm.file.$error.maxSize">File too large {{picFile.size /
                1000000|number:1}}MB: max {{picFile.$errorParam}}</i>
            </div>
            <div class="col-xs-12" data-ng-if="metaData.value === 'video'">
                <lable>Video Link: </lable>
                <input class="btn btn-default" data-ng-model="showCaseVideo" type="text">
                <button class="btn btn-primary"
                        ng-click="uploadProductVideo(showCaseVideo)">upload
                </button>
                <br><br>
            </div>

            <div class="col-xs-12" data-ng-if="metaData.value != 'video'">
                <button class="btn btn-primary"
                        ng-click="uploadProductPic()">Upload
                </button>
                <span class="btn btn-primary"
                      data-ng-if="productImageDetails.showcaseVideo!=null" style="float: right"><a
                        style="color: snow" data-ng-href="{{productImageDetails.showcaseVideo}}"
                        target="_blank">playVideo</a></span>
            </div>

        </div>
    </div>




</div>