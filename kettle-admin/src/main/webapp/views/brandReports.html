<style type="text/css">
.resultGrid{
	width:auto;
	height:410px;
	background:#fff;
	margin:10px auto;
}
.ui-grid-pager-row-count-picker select {
	width:100px;
}
</style>
<div data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h1 class="page-header">
                Reporting Dashboard Brand Wise
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-4">
            <label>Select Brand</label>
            <select class="form-control" data-ng-model="selectedBrand" data-ng-change="brandSelected(selectedBrand)" data-ng-options="brand as brand.brandName for brand in allBrands"></select>
        </div>
        <div class="col-xs-2 col-xs-offset-10">
            <label>Select data source:</label>
            <select class="form-control" data-ng-change="getReportEnv(environment)" data-ng-model="environment" data-ng-options="environment as environment for environment in environments"></select>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <label>Select report category</label>
            <div class="row">
                <div class="col-xs-12">
                    <select class="form-control" data-ng-change="getReports()" data-ng-model="report" data-ng-options="report as report.fileName for report in reportList"></select>
                </div>
                <!-- <div class="col-xs-2">
                    <button class="btn btn-primary pull-right" data-ng-click="getReports()">Get Report types</button>
                </div> -->
            </div>
            <br />
            <div class="row">
                <div class="col-xs-6">
                    <label>Select report type</label>
                    <select class="form-control" data-ng-model="category" data-ng-change="getReportType()" data-ng-options="category as category.name for category in categories"></select>
                </div>
                <div class="col-xs-6">
                    <label>Select report</label>
                    <select class="form-control" data-ng-model="reportType" data-ng-options="reportType as reportType.name for reportType in reportTypes"></select>
                </div>
            </div>
            <br />

            <label data-ng-if="reportType.param.length>0">Fill the below parameters:</label>
            <div class="row form-horizontal" data-ng-if="reportType.param.length>0">
                <div class="col-xs-4">
                    <div class="form-group" data-ng-repeat="paramSingle in reportType.param">
                        <label class="col-sm-4 control-label">{{paramSingle.displayName}}</label>
                        <div class="col-sm-8">
                            <input class="form-control" data-ng-if="paramSingle.dataType=='STRING'" type="text" data-ng-model="paramSingle.value" />
                            <input class="form-control" data-ng-if="paramSingle.dataType=='INTEGER' || paramSingle.dataType=='LONG' || paramSingle.dataType=='DOUBLE'" type="number" data-ng-model="paramSingle.value" />
                            <div class="datepicker" data-ng-if="paramSingle.dataType=='DATE'" data-date-format="yyyy-MM-dd">
                                <input class="form-control" data-ng-model="paramSingle.value" type="text" placeholder="yyyy-MM-dd" required />
                            </div>
                            <select class="form-control" data-ng-if="paramSingle.dataType=='BOOLEAN'" data-ng-model="paramSingle.value">
                                <option value="true">TRUE</option>
                                <option value="false">FALSE</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-xs-12 text-right">
                    <input type="button" class="btn btn-primary"
                           data-ng-click="showQuery()" value="Show query" />
                    <input type="button" class="btn btn-primary"
                           data-ng-if="(environment == 'PROD' || environment == 'SPROD')"
                           data-ng-click="executeQuery()" value="Execute query" />
                    <input type="button" class="btn btn-primary"
                           data-ng-if="(environment != 'PROD' && environment != 'SPROD')"
                           data-ng-click="executeQuery()" value="Execute query" />
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-xs-12" data-ng-if="showResultMetadata">
                    <label>{{reportType.name}}</label> <label style="margin-left:30px;">Total results: {{resultData.length}}</label>
                    <button data-ng-click="exportReport()" class="pull-right custom-csv-link-location">Export to CSV</button>
                </div>
            </div>
            <div class="row">
                <div data-ng-show="loadingData" align="center"><img data-ng-src="img/loading.gif" /><div>Loading Data</div></div>
            </div>
            <div data-ng-show="errorMessage == null" ui-grid="reportData" ui-grid-pagination ui-grid-exporter ui-grid-move-columns class="resultGrid"></div>
            <div data-ng-show="errorMessage != null"><label style="color: red">{{errorMessage}}</label></div>
        </div>
    </div>
</div>




<!-- Modal -->
<div class="modal fade" id="queryDisplayModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Query Content</h4>
            </div>
            <div class="modal-body">
          		<pre>
                  	{{reportType.content}}
                </pre>

            </div>
        </div>
    </div>
</div>