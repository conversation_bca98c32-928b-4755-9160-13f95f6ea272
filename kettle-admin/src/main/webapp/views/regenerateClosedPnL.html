
<div class="row" data-ng-init="init()">
    <h1 class="page-header">Budget Manager :  Regenerate Finalized</h1>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Regenerate Closed</div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-12">
                to generate Closed PNL for a month select date of the month after the required month, as closed is
                generated via last day Monthly closing entries of a month.
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <label>Start Date</label>
                <div class="datepicker" date-format="yyyy-MM-dd">
                    <input class="form-control" ng-model="closedDate"
                           placeholder="click here" type="text" required/>
                </div>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-danger" data-ng-click="regenerateClosedForAll()"
                        data-ng-disabled="closedDate == null">Regenerate Finalized
                </button>
            </div>
        </div>
    </div>
</div>