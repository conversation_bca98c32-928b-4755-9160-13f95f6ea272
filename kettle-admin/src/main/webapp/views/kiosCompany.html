<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
        <h1 class="page-header">
        	Company
            <button class="btn btn-primary pull-right" data-toggle="modal" id="addCompanyIdDiv" ng-click="addCompany()"><i class="fa fa-plus fw"></i> Add Company</button>
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
</div>

<div class="row">
	<div class="col-xs-12">
    	<p>Filtered {{ kiosCompanyList.length }} of {{ totalItems}} total results</p>
    	<div class="row">
        	<div class="col-xs-12" ng-if="filteredItems > 0">
            	<table class="table table-striped table-bordered" id="tableDataStructure" style="margin-padding:0px;">
                    <thead>
                         <th>ID &nbsp;<a ng-click="sort_by('id');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Company Name&nbsp;<a ng-click="sort_by('companyName');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th>Company Domain&nbsp;</th>
                        <th>Company Email&nbsp;</th>
                        <th>Payment Mode&nbsp;</th>
                        <th>Status &nbsp;<a ng-click="sort_by('companyStatus');"><i class="glyphicon glyphicon-sort"></i></a></th>
                        <th  colspan="2" align="center">Action&nbsp;</th>
                    </thead>
                <tbody>
         <tr ng-repeat="companyDetailsList in filtered = (kiosCompanyList | filter:search | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit">
              <td>{{companyDetailsList.companyId}}</td>
                        <td>{{companyDetailsList.companyName}}</td>
                        <td><span ng-repeat="job in companyDetailsList.companyDomains"> {{job.name}}<br> </span></td>
                        <td>{{companyDetailsList.companyEmail}}</td>
                         <td>{{companyDetailsList.paymentMode}}</td>
                         <td>{{companyDetailsList.companyStatus}}</td>
                        <td><span style="padding-left:10px"><button class="btn btn-primary pull-right"  id="companyStatusIdDiv" ng-if="companyDetailsList.companyStatus=='IN_ACTIVE'" ng-click="CompanyStatusChange('ACTIVE',companyDetailsList.companyId)">Activate</button></span>
	                    <span style="display:inline-block;margin:10px 0"><button class="btn btn-primary pull-right"  id="companyStatusIdDiv" ng-if="companyDetailsList.companyStatus=='ACTIVE'" ng-click="CompanyStatusChange('IN_ACTIVE',companyDetailsList.companyId)">Deactivate</button></span>
	                    <span style="display:inline-block;margin:10px 0"> <button class="btn btn-primary pull-right"  id="companyStatusIdDiv"  ng-click="editCompany(companyDetailsList.companyId)">Edit</button></span>
						</td> 
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="companyModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header" style="background-color:#337ab7;color:white">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">{{action}} Company</h4>
          </div>
          <div class="modal-body" style="line-height:34px">
             <form name="addEmployeeForm" novalidate>
              <div class="col-xs-6">
                  <label>Company Name *</label>
                  <input type="text" class="form-control" ng-model="kiosCompanyName" required />
              </div>
              <div class="col-xs-6">
                  <label> Company Email *</label>
                  <input class="form-control" type="text"   ng-model="kiosCompanyEmail" />
              </div> 
              <div class="col-xs-10">
                  <label> Domains *</label>
                  <input class="form-control" type="text" placeholder="Please Input Domain Name"  ng-model="kiosCompanyDomains" />
              </div>
              
              <div class="col-xs-2" style="margin-top:-35px; margin-left:475px">
                  <button class="btn btn-primary pull-right" ng-click="addMoreDomain()">+</button>
              </div>
              
               <div ng-repeat="domainData in domainList" style="margin-top:10px" class="col-xs-12">
				     <div class="col-xs-10"> <input type="text" class="form-control" disabled="disabled" ng-model="domainData.name" name="" placeholder="Enter domain Name"></div>
				     <div class="col-xs-2">  <button class="btn btn-primary pull-right" ng-show="$last" ng-click="removeChoice()">-</button></div>
			</div>
                           
              <div class="col-xs-6"><br>
                  <label>Contact Name *</label>
                  <input class="form-control" type="text"   ng-model="kiosContactDetailName" />
              </div>
              
              <div class="col-xs-6"><br>
                  <label>Contact Email *</label>
                  <input class="form-control" type="text"   ng-model="kiosContactDetailEmail" />
              </div>
              
              <div class="col-xs-6">
                  <label>Contact Phone </label>
                  <input class="form-control"  string-to-number type="number" my-maxlength="11"  ng-model="kiosContactDetailShortCode" />
              </div>
              
              <div class="col-xs-6">
                  <label>Payment Mode *</label>
                 <select class="form-control" ng-model="selectedPaymentMode" ng-options="paymentModeData as paymentModeData.name for paymentModeData in paymentMode track by paymentModeData.name"></select>
              </div>
              
              <div class="col-xs-12">
                  <label>Company Sub Domain  *</label>
                 <input class="form-control" ng-model="selectedCompanyUrl"/>
              </div>
              
              <div class="col-xs-12">&nbsp;</div>
              
              <div class="form-group clearfix" ng-if="action=='Add'" ng-hide="showEmpBtn" >
                  <button class="btn btn-primary pull-right"  ng-click="submitAddCompany()">Add</button>
          	 </div>
          	 
          	 <div class="form-group clearfix" ng-if="action=='Edit'" ng-hide="showEmpBtn" >
                  <button class="btn btn-primary pull-right" ng-click="submitUpdateCompany()">update</button>
          	 </div>
				
              </form>
              </div>
          
     </div>
     
  </div>
</div>
