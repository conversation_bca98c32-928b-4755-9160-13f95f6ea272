<style type="text/css" xmlns:ui="http://www.w3.org/1999/xhtml">
    form.tab-form-demo {
        margin: 20px 20px;
    }

    .tabDiv {
        margin-top: 20px;
        height: 350px;
        max-height: 350px;
    }

    .divInnerRow {
        margin-top: 20px;
        height: 50px;
        max-height: 50px;
        width: 100%;
    }

    .tabPane {
        border: 1px solid #ddd;
        border-radius: 0px 0px 5px 5px;
        padding: 10px;
    }

    .row-selected {
        background-color: rgb(156, 231, 155);
    }

    .row-non-editable {
        background-color: rgb(231, 155, 155);
    }

</style>
<div data-ng-init="init()">
    <div class="panel panel-info">
        <div class="panel-heading">{{recipeDetail.name.length > 0 ?
            recipeDetail.name : 'Please select recipe'}}
            <span data-ng-if="recipeDetail.name && recipeDetail.profile" style="color:red;"> For Profile {{recipeDetail.profile}}</span>
        </div>
        <div class="panel-body">
            <uib-tabset
                    class="tabPane"
                    active="activeTab">
                <uib-tab index="0" heading="Product Selection">
                    <div class="row tabDiv">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Select Product</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2
                                            class="form-control" style="width: 100% !important"
                                            id="productSelected"
                                            data-ng-model="product.selectedProductId"
                                            data-ng-change="onSelectProduct(product.selectedProductId)">
                                        <option value=""></option>
                                        <option data-ng-repeat="product in productsInfo " value="{{product.id}}">
                                            {{product.name}} - {{product.status}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2 class="form-control" style="width: 100% !important"
                                            data-ng-model="selectedDimensionId"
                                            data-placeholder="Select a dimension"
                                            data-ng-change="selectDimension(selectedDimensionId)">
                                        <option data-ng-repeat="dimension in selectedDimensionProfile.content"
                                                value="{{dimension.id}}">{{dimension.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12" data-ng-if="existingRecipes != null && existingRecipes.length > 0">
                            <div class="row divInnerRow">
                                <div class="col-xs-12">
                                    <button class="btn btn-primary pull-right" data-ng-show="true"
                                            data-ng-click="cloneRecipeByProfile(selectedRecipeId)"
                                            style="margin-bottom: 10px;">
                                        <i class="fa fa-plus"></i> Clone Recipe Profile
                                    </button>
                                </div>
                                <div class="col-xs-12">
                                    <table class="table table-striped table-bordered">
                                        <tr>
                                            <th>Select</th>
                                            <th>Recipe Id</th>
                                            <th>Recipe Name</th>
                                            <th>Profile</th>
                                            <th>Start date</th>
                                            <th>Status</th>
                                            <th>Approvals</th>
                                            <th>Actions</th>
                                        </tr>
                                        <tbody>
                                        <tr data-ng-repeat="recipe in existingRecipes | orderBy: 'profile' track by recipe.recipeId">
                                            <td>
                                                <input type="radio" name="recipeGroup" data-ng-model="selectedRecipeId"
                                                       data-ng-value="recipe.recipeId"
                                                       data-ng-click="changeRecipeId(selectedRecipeId)"/>
                                            </td>
                                            <td>{{recipe.recipeId}}</td>
                                            <td>{{recipe.name}}</td>
                                            <td>{{recipe.profile}}</td>
                                            <td>{{recipe.startDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                                            <td>{{recipe.status}}</td>
                                            <td>
                                                <button class="btn btn-primary"
                                                        acl-action-checker="ADRFAC"
                                                        data-ng-click="sendRecipeForApproval(recipe, true)"
                                                        data-ng-if="recipe.status =='IN_PROGRESS'"
                                                        style="margin-bottom: 10px;">
                                                    Send For Approval
                                                </button>
                                                <br>
                                                <span data-ng-if="recipe.status =='IN_PROGRESS' && recipe.approvedBy != null"
                                                      style="margin-bottom: 10px;">
                                                    Approved By {{recipe.approvedByName}}
                                                </span>
                                                <button class="btn btn-success"
                                                        acl-action-checker="ADRAR"
                                                        data-ng-click="previewRecipe(recipe, true)"
                                                        data-ng-if="recipe.status =='PENDING_APPROVAL'"
                                                        style="margin-bottom: 10px;">
                                                    Approve/Reject Recipe
                                                </button>
                                                <button class="btn btn-success"
                                                        acl-action-checker="ADRAR"
                                                        data-ng-click="rejectRecipeForApproval(recipe, false)"
                                                        data-ng-if="recipe.status =='PENDING_APPROVAL'"
                                                        style="margin-bottom: 10px;">
                                                    Cancel Approval Request
                                                </button>
                                            </td>
                                            <td align="left">
                                                <span data-ng-if="recipe.status == 'ACTIVE' && recipe.isEditable"
                                                      data-ng-click="editActiveRecipe(recipe)"
                                                      style="cursor: pointer" title="Edit Recipe">
                                                    <i class="fa fa-edit"
                                                       style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                                <span data-ng-if="recipe.status == 'IN_PROGRESS'"
                                                      data-ng-click="selectRecipeForEdit(recipe)"
                                                      style="cursor: pointer" title="Edit Recipe">
                                                    <i class="fa fa-edit"
                                                       style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                                <span data-ng-click="previewRecipe(recipe)" style="cursor: pointer"
                                                      title="View Recipe">
                                                    <i class="fa fa-eye" style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                                <!--<span data-ng-click="removeRecipe(recipe)" style="cursor: pointer" title="Delete Recipe">
                                                    <i class="fa fa-remove" style="font-size: 24px; margin-right: 5px; color: red"></i>
                                                </span>-->
                                                <span data-ng-click="getRecipeLogs(recipe.recipeId)"
                                                      style="cursor: pointer" title="View Logs">
                                                    <i class="fa fa-list"
                                                       style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                                <span data-ng-click="getDispenserDetail(recipe)"
                                                      style="cursor: pointer" title="Dispenser">
                                                    <i class="fa fa-filter"
                                                       style="font-size: 24px; margin-right: 5px"></i>
                                                </span>
                                                <span data-ng-click="inactivateRecipeProfile(recipe)"
                                                  style="cursor: pointer" title="Mark Inactive" data-ng-if="recipe.status !='IN_PROGRESS' && recipe.status !='PENDING_APPROVAL'">
                                                  <i class="fa fa-remove"
                                                     style="font-size: 24px; margin-right: 5px; color: red"></i>
                                                </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12" style="text-align: center;">
                            <button class="btn btn-primary" data-ng-click="addNewRecipe()"
                                    data-ng-show="product != null && product != undefined && product.selectedProductId != null && selectedDimensionId != null && ( existingRecipes == null || existingRecipes.length < 1)">
                                <i class="fa fa-plus"></i> Add Recipe
                            </button>
                            <button class="btn btn-primary" data-ng-click="cloneFromRecipe()"
                                    data-ng-show="product != null && product != undefined && product.selectedProductId != null && selectedDimensionId != null && ( existingRecipes == null || existingRecipes.length < 1)">
                                <i class="fa fa-plus"></i> Clone Recipe
                            </button>
                        </div>
                    </div>
                </uib-tab>
                <uib-tab index="1" heading="Recipe Detail">
                    <div class="row tabDiv">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Recipe Name</label>
                                </div>
                                <div class="col-xs-8">
                                    <input class="form-control" data-ng-model="recipeDetail.name" disabled/>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    {{recipeDetail.dimension.name}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Dimension Descriptor</label>
                                </div>
                                <div class="col-xs-8">
                                    <input class="form-control" data-ng-model="recipeDetail.dimension.desc"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Dispensed</label>
                                </div>
                                <div class="col-xs-8">
                                    <input type="checkbox" data-ng-model="recipeDetail.dispensed" style="zoom: 1.5"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Profile</label>
                                </div>
                                <div class="col-xs-8">
                                    {{recipeDetail.profile}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Start Date</label>
                                </div>
                                <div class="col-xs-8">
                                    <div class="datepicker" data-date-min-limit="{{todaysDate}}"
                                         data-date-format="yyyy-MM-dd">
                                        <input class="form-control" data-ng-model="recipeDetail.startDate"
                                               placeholder="Select a start date..." type="text" required/>
                                    </div>
                                    <p data-ng-show="showStartDateMandatory == true">Please select start date.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label class="pull-right">Will be delivered?</label>
                                </div>
                                <div class="col-xs-8">
                                    <input type="checkbox" data-ng-model="recipeDetail.deliverable" style="zoom: 1.5"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </uib-tab>
                <uib-tab index="2" heading="Ingredients">
                    <div class="row tabDiv">
                        <div class="col-xs-12" style="margin-left: 20px; margin-right: 20px">
                            <uib-tabset active="activeIngredientTab">
                                <uib-tab index="0" heading="Variant Product Selection">
                                    <div class="row divInnerRow">
                                        <div data-ng-if="isComboProduct" class="alert alert-info">
                                            <strong>Click Next!</strong> Nothing to add as its a combo product.
                                        </div>
                                        <div class="col-xs-12" >
                                            <button data-ng-if="!isComboProduct" class="btn btn-primary pull-left"
                                                    data-ng-click="addNewVarient()">
                                                <i class="fa fa-plus"></i> Add Variant
                                            </button>
                                        </div>
                                        <div class="col-xs-12" style="margin-top: 20px;">
                                            <table class="table table-striped table-bordered"
                                                   data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.variants != null && recipeDetail.ingredient.variants.length > 0">
                                                <tr>
                                                    <th>Product Id</th>
                                                    <th>Product Name</th>
                                                    <th>Unit Of Measure</th>
                                                    <th>Is Critical</th>
                                                    <th>Is Customizable</th>
                                                    <th>Details</th>
                                                    <th>Actions</th>
                                                </tr>
                                                <tbody>
                                                <tr data-ng-repeat="variant in recipeDetail.ingredient.variants">
                                                    <td>{{variant.product.productId}}</td>
                                                    <td>{{variant.product.name}}({{variant.product.displayName}})</td>
                                                    <td>{{variant.uom}}</td>
                                                    <td>{{variant.critical}}</td>
                                                    <td>{{variant.customize}}</td>
                                                    <td>{{printVariantDetails(variant.details)}}</td>
                                                    <td align="left">
                                                          <span >
                                                          <span data-ng-click="addVariantAliases(variant)"
                                                                style="cursor: pointer"
                                                                title="Add/Edit Details">
                                                              <i class="fa fa-edit"
                                                                 style="font-size: 24px; margin-right: 5px"></i>
                                                          </span>
                                                              <span data-ng-click="recipeDetail.ingredient.variants.splice($index, 1);"
                                                                    style="cursor: pointer" title="Remove Variant">
                                                                  <i class="fa fa-remove"
                                                                     style="font-size: 24px; margin-right: 5px; color: red"></i>
                                                              </span>
                                                          </span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </uib-tab>
                                <uib-tab index="1" heading="SCM Product Selection">
                                    <div class="row divInnerRow">
                                        <div data-ng-if="isComboProduct" class="alert alert-info">
                                            <strong>Click Next!</strong> Nothing to add as its a combo
                                            product.
                                        </div>
                                        <div class="col-xs-12" data-ng-show="!cloneRecipeProfile">
                                            <button data-ng-if="!isComboProduct" class="btn btn-primary pull-left"
                                                    data-ng-click="addNewSCMProduct()">
                                                <i class="fa fa-plus"></i> Add SCM Product
                                            </button>
                                        </div>
                                        <div class="col-xs-12" style="margin-top: 20px;">
                                            <table class="table table-striped table-bordered"
                                                   data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.products != null && recipeDetail.ingredient.products.length > 0">
                                                <tr>
                                                    <th>Product Category</th>
                                                    <th>Display</th>
                                                    <th>Is Critical</th>
                                                    <th>Is Customizable</th>
                                                    <th>Details</th>
                                                    <th>Actions</th>
                                                </tr>
                                                <tbody>
                                                <tr data-ng-repeat="variant in recipeDetail.ingredient.products track by $index">
                                                    <td>{{variant.category.name}}</td>
                                                    <td>{{variant.display}}</td>
                                                    <td>{{variant.critical}}</td>
                                                    <td>{{variant.customize}}</td>
                                                    <td>{{printSCMProductDetails(variant.details)}}</td>
                                                    <td align="left">
                                                        <span >
                                                            <span data-ng-click="editVariantSCMProduct(variant)"
                                                                  style="cursor: pointer" title="Add/Edit Details">
                                                                <i class="fa fa-edit"
                                                                   style="font-size: 24px; margin-right: 5px"></i>
                                                            </span>
                                                            <span data-ng-click="recipeDetail.ingredient.products.splice($index, 1);"
                                                                  style="cursor: pointer" title="Remove Variant">
                                                                <i class="fa fa-remove"
                                                                   style="font-size: 24px; margin-right: 5px; color: red"></i>
                                                            </span>
                                                        </span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </uib-tab>
                                <uib-tab index="2" heading="Menu Product Selection">
                                    <div class="row divInnerRow">
                                        <div data-ng-if="(!isComboProduct && !isQVM)" class="alert alert-info">
                                            <strong>Click Next!</strong> Nothing to add as its Not a combo
                                            product.
                                        </div>
                                        <div class="col-xs-12"
                                             data-ng-if="(isComboProduct || isQVM) ">
                                            <div class="row divInnerRow">
                                                <div class="col-xs-6">
                                                    <select ui-select="selectOptions" style="width: 100% !important"
                                                            data-ng-model="selectedItemCount"
                                                            data-placeholder="No Of Items..."
                                                            class="form-control pull-right">
                                                        <option value="1">1</option>
                                                        <option value="2">2</option>
                                                        <option value="3">3</option>
                                                        <option value="4">4</option>
                                                        <option value="5">5</option>
                                                        <option value="6">6</option>
                                                        <option value="7">7</option>
                                                        <option value="8">8</option>
                                                        <option value="9">9</option>
                                                        <option value="10">10</option>
                                                        <option value="11">11</option>
                                                        <option value="12">12</option>
                                                        <option value="13">13</option>
                                                        <option value="14">14</option>
                                                        <option value="15">15</option>

                                                    </select>
                                                </div>
                                                <div class="col-xs-6">
                                                    <button data-ng-if="isComboProduct || isQVM"
                                                            class="btn btn-primary pull-left"
                                                            data-ng-click="createComboProductItems(selectedItemCount)">
                                                        Select Size
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" data-ng-show="!cloneRecipeProfile && (isSuperCombo || isFixedMealProduct)" style="margin-left: 20px;">
                                            <input type="checkbox"
                                                data-ng-model="recipeDetail.ingredient.compositeProduct.isAllowMultiselection"
                                                id="isAllowMultiselection"
                                                style="transform: scale(1.5);">
                                            <label for="isAllowMultiselection" style="margin-left: 10px;">Allow Multi-Selection?</label>
                                        </div>
                                        <div class="row" data-ng-show="!cloneRecipeProfile && isSuperCombo" style="margin-left: 20px;">
                                            <input type="checkbox"
                                                data-ng-model="recipeDetail.ingredient.compositeProduct.isAllowMultiselection"
                                                id="isAllowMultiselection"
                                                style="transform: scale(1.5);">
                                            <label for="isAllowMultiselection" style="margin-left: 10px;">Allow Multi-Selection?</label>
                                        </div>
                                        <div class="col-xs-12" style="margin-top: 20px;"
                                             data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.compositeProduct != null && recipeDetail.ingredient.compositeProduct.details!= null && recipeDetail.ingredient.compositeProduct.details.length > 0"
                                             data-ng-repeat="detail in recipeDetail.ingredient.compositeProduct.details track by $index">
                                            <div class="row">
                                                <div class="col-xs-12">
                                                    <label>Options for {{detail.name}}</label>
                                                </div>
                                                <div class="row">
                                                    <div class="col-xs-4" >
                                                        <button class="btn btn-primary pull-left"
                                                                style="margin-top: 10px;"
                                                                data-ng-click="openMenuProduct(detail)">
                                                            <i class="fa fa-plus"></i> Add New Product
                                                        </button>
                                                    </div>
                                                    <div class="col-xs-8" ng-if="(isComboProduct && (isHeroCombo || isSuperCombo || isBoxCombo))">
                                                        <div class="row">
                                                            <div class="col-xs-4">
                                                                <label>Discount
                                                                    Percentage</label>
                                                                <label ng-if="isSuperCombo">Discount Value</label>
                                                                <input
                                                                       type="number" class="form-control" required ng-model="detail.discount"/>
                                                            </div>
                                                            <div class="col-xs-4">
                                                                <input class="btn btn-primary" type="button" style="margin-top: 23px" value="Remove discount" data-ng-click="removeDiscount(detail)"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row" ng-show="(isComboProduct && (isHeroCombo || isSuperCombo || isBoxCombo || isFixedMealProduct))">
                                                    <div class="col-lg-3" data-ng-show="!isFixedMealProduct">
                                                        <label>Internal Discount Type</label>
                                                        <select class="form-control" data-ng-model="detail.internalDiscountType">
                                                            <option value="FIXED">FIXED</option>
                                                            <option value="PERCENTAGE">PERCENTAGE</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-lg-3" data-ng-show="!isFixedMealProduct">
                                                        <label>Internal Discount Value</label>
                                                        <input class="form-control" type="number" required ng-model="detail.internalDiscount"/>
                                                    </div>
                                                    <div class="col-lg-3" data-ng-show="!isFixedMealProduct">
                                                        <input class="btn btn-primary" type="button" style="margin-top: 23px" value="Remove internal discount" data-ng-click="removeInternalDiscount(detail)"/>
                                                    </div>
                                                    <div class="col-lg-3" data-ng-if="isHeroCombo && detail.name == 'Item 1'" style="display:inline-block">
                                                        <label class="checkbox" style="">
                                                            Customizable
                                                            <input class="checkbox" type="checkbox" value="Customizable" data-ng-model="detail.customizable"/>
                                                        </label>
                                                    </div>
                                                    <div class="col-lg-3" data-ng-if="isHeroCombo" style="display:inline-block">
                                                        <label class="checkbox">
                                                             Discount Applicable
                                                            <input class="checkbox" type="checkbox" value="discountApplicable" data-ng-model="detail.discountApplicable"/>
                                                        </label>
                                                    </div>
                                                    <div class="col-lg-3" data-ng-if="isHeroCombo || (recipeDetail.ingredient.compositeProduct.isAllowMultiselection && (isSuperCombo || isFixedMealProduct))">
                                                        <label>Min Selection</label>
                                                        <!-- <select class="form-control" data-ng-model="detail.minSelection">
                                                            <option data-ng-repeat="menuProduct in detail.menuProducts track by $index" data-ng-if="$index != 0" type="number">{{$index}}</option>
                                                        </select> -->
                                                        <input class="form-control" type="number" data-ng-model="detail.minSelection">
                                                    </div>
                                                    <div class="col-lg-3" data-ng-if="isHeroCombo || (recipeDetail.ingredient.compositeProduct.isAllowMultiselection && (isSuperCombo || isFixedMealProduct))">
                                                        <label>Max Selection</label>
                                                        <!-- <select class="form-control" data-ng-model="detail.maxSelection">
                                                            <option data-ng-repeat="menuProduct in detail.menuProducts track by $index" data-ng-if="$index != 0" type="number">{{$index}}</option>
                                                        </select> -->
                                                        <input class="form-control" type="number" data-ng-model="detail.maxSelection">
                                                    </div>
                                                </div>
                                                <div class="col-xs-12">
                                                    <table class="table table-striped table-bordered"
                                                           style="margin-top: 10px;">
                                                        <tr>
                                                            <th>Product Name</th>
                                                            <th>Dimension</th>
                                                            <th>Quantity</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                        <tbody>
                                                        <tr ng-repeat="variant in detail.menuProducts track by $index">
                                                            <td>{{variant.product.name}}</td>
                                                            <td>{{variant.dimension.name}}</td>
                                                            <td>{{variant.quantity}}</td>
                                                            <td align="left">
                                                                <span >
                                                                    <span data-ng-click="detail.menuProducts.splice($index, 1);"
                                                                          style="cursor: pointer"
                                                                          title="Remove Variant">
                                                                        <i class="fa fa-remove"
                                                                           style="font-size: 24px; margin-right: 5px; color: red;"></i>
                                                                    </span>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </uib-tab>
                                <uib-tab index="3" heading="Fixed Meal Product Selection">
                                    <div class="row divInnerRow">
                                        <div data-ng-if="!isFixedMealProduct" class="alert alert-info">
                                            <strong>Click Next!</strong> Nothing to add as its Not a Fixed Meal
                                            product.
                                        </div>
                                        <div class="col-xs-12"
                                             data-ng-if="(isFixedMealProduct) && (recipeDetail.ingredient == null || recipeDetail.ingredient.compositeProduct == null || recipeDetail.ingredient.compositeProduct.details == null || recipeDetail.ingredient.compositeProduct.details.length == 0)">
                                            <div class="row divInnerRow">
                                                <div class="col-xs-6">
                                                    <select ui-select="selectOptions" style="width: 100% !important"
                                                            data-ng-model="selectedItemCount"
                                                            data-placeholder="No Of Items..."
                                                            class="form-control pull-right">
                                                        <option value="1">1</option>
                                                        <option value="2">2</option>
                                                        <option value="3">3</option>
                                                        <option value="4">4</option>
                                                        <option value="5">5</option>
                                                        <option value="6">6</option>
                                                        <option value="7">7</option>
                                                        <option value="8">8</option>
                                                        <option value="9">9</option>
                                                        <option value="10">10</option>
                                                        <option value="11">11</option>
                                                        <option value="12">12</option>
                                                        <option value="13">13</option>
                                                        <option value="14">14</option>
                                                        <option value="15">15</option>
                                                    </select>
                                                </div>
                                                <div class="col-xs-6">
                                                    <button data-ng-if="isComboProduct || isQVM"
                                                            class="btn btn-primary pull-left"
                                                            data-ng-click="createComboProductItems(selectedItemCount)">
                                                        Select Size
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12" style="margin-top: 20px;"
                                             data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.compositeProduct != null && recipeDetail.ingredient.compositeProduct.details!= null && recipeDetail.ingredient.compositeProduct.details.length > 0"
                                             data-ng-repeat="detail in recipeDetail.ingredient.compositeProduct.details track by $index">
                                            <div class="row">
                                                <div class="col-xs-6">
                                                    <label>Options for {{detail.name}}</label>
                                                </div>
                                                <div class="col-xs-4">
                                                    <label>Want to Add Fixed Meal Product</label>
                                                </div>
                                                <div class="col-xs-2">
                                                    <input type="checkbox" data-ng-model="detail.isFixedMealApplicable"  style="zoom: 1.5"/>
                                                </div>
                                                <div class="row">
                                                    <div class="col-xs-4" data-ng-show="!cloneRecipeProfile">
                                                        <button class="btn btn-primary pull-left"
                                                                style="margin-top: 10px;"
                                                                data-ng-disabled="detail.menuProducts.length>0"
                                                                data-ng-click="openMenuProduct(detail)">
                                                            <i class="fa fa-plus"></i> Add New Product
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="col-xs-12">
                                                    <table class="table table-striped table-bordered"
                                                           style="margin-top: 10px;">
                                                        <tr>
                                                            <th>Product Name</th>
                                                            <th>Dimension</th>
                                                            <th>Quantity</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                        <tbody>
                                                        <tr ng-repeat="variant in detail.menuProducts track by $index">
                                                            <td>{{variant.product.name}}</td>
                                                            <td>{{variant.dimension.name}}</td>
                                                            <td>{{variant.quantity}}</td>
                                                            <td align="left">
                                                                <span data-ng-show="!cloneRecipeProfile">
                                                                    <span data-ng-click="detail.menuProducts.splice($index, 1);"
                                                                          style="cursor: pointer"
                                                                          title="Remove Variant">
                                                                        <i class="fa fa-remove"
                                                                           style="font-size: 24px; margin-right: 5px; color: red;"></i>
                                                                    </span>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </uib-tab>


                                <uib-tab index="4" heading="QVM Product Customization">
                                    <div class="row divInnerRow">
                                        <div data-ng-if="!isQVM" class="alert alert-info">
                                            <strong>Click Next!</strong> Nothing to add as its Not a Quick Value Meal
                                            product.
                                        </div>

                                        <div class="col-xs-12" style="margin-top: 20px;"
                                             data-ng-if="isQVM && recipeDetail.ingredient != null && recipeDetail.ingredient.compositeProduct != null && recipeDetail.ingredient.compositeProduct.details!= null && recipeDetail.ingredient.compositeProduct.details.length > 0"
                                             data-ng-repeat="detail in recipeDetail.ingredient.compositeProduct.details track by $index">
                                            <div class="row">
                                                <div class="col-xs-12">
                                                    <label>Options for {{detail.name}}</label>
                                                </div>
                                                <div class="col-xs-12"
                                                     data-ng-repeat="mp in detail.menuProducts track by $index">
                                                    <label>{{mp.product.name}} - {{mp.dimension.name}}
                                                        ({{mp.quantity}})</label>
                                                    <div class="col-xs-12" style="margin-top: 20px;">
                                                        <table class="table table-striped table-bordered"
                                                               data-ng-if="mp.ingredient != null && mp.ingredient.variants != null && mp.ingredient.variants.length > 0">
                                                            <tr>
                                                                <th>Product Id</th>
                                                                <th>Product Name</th>
                                                                <th>Unit Of Measure</th>
                                                                <th>Is Critical</th>
                                                                <th>Is Customizable</th>
                                                                <th>Details</th>
                                                            </tr>
                                                            <tbody>
                                                            <tr data-ng-repeat="variant in mp.ingredient.variants">
                                                                <td>{{variant.product.productId}}</td>
                                                                <td>
                                                                    {{variant.product.name}}({{variant.product.displayName}})
                                                                </td>
                                                                <td>{{variant.uom}}</td>
                                                                <td>{{variant.critical}}</td>
                                                                <td>{{variant.customize}}</td>
                                                                <td>
                                                                    <div class="form-check"
                                                                         data-ng-repeat="detail in variant.details track by $index"
                                                                         data-ng-click="setDefaultQVMVariant(variant.details, detail)">
                                                                        <input class="form-check-input" type="radio"
                                                                               name={{variant.product.productId}}
                                                                               id={{variant.product.productId}}
                                                                               value={{detail.alias}}
                                                                               data-ng-checked="detail.defaultSetting==true">
                                                                        <label class="form-check-label"
                                                                               for={{variant.product.productId}}>
                                                                            {{detail.defaultSetting?"**":""}}
                                                                            {{detail.alias}}({{detail.yield}},
                                                                            {{detail.quantity}})
                                                                        </label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                    <div class="col-xs-12" style="margin-top: 20px;">
                                                        <table class="table table-striped table-bordered"
                                                               data-ng-if="mp.ingredient != null && mp.ingredient.products != null && mp.ingredient.products.length > 0">
                                                            <tr>
                                                                <th>Product Category</th>
                                                                <th>Display</th>
                                                                <th>Is Critical</th>
                                                                <th>Is Customizable</th>
                                                                <th>Details</th>
                                                            </tr>
                                                            <tr data-ng-repeat="product in mp.ingredient.products track by $index">
                                                                <td>{{product.category.name}}</td>
                                                                <td>{{product.display}}</td>
                                                                <td>{{product.critical}}</td>
                                                                <td>{{product.customize}}</td>
                                                                <td>
                                                                    <div class="form-check"
                                                                         data-ng-repeat="detail in product.details track by $index"
                                                                         data-ng-click="setDefaultQVMVariant(product.details, detail)">
                                                                        <input class="form-check-input" type="radio"
                                                                               name={{product.category.name}}
                                                                               id={{detail.product.productId}}
                                                                               value={{detail.product.name}}
                                                                               data-ng-checked="detail.defaultSetting==true">
                                                                        <label class="form-check-label"
                                                                               for={{variant.product.productId}}>
                                                                            {{detail.defaultSetting?"**":""}}
                                                                            {{detail.product.name}}({{detail.yield}},
                                                                            {{detail.quantity}})
                                                                        </label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>

                                                    <div class="col-xs-12" style="margin-top: 20px;">
                                                        <table class="table table-striped table-bordered">
                                                            <tr>
                                                                <th>Product Name</th>
                                                                <th>Quantity</th>
                                                                <th>Yield</th>
                                                                <th>Customizable</th>
                                                                <th>Select</th>
                                                            </tr>
                                                            <tr ng-repeat="addon in mp.addons track by $index">
                                                                <td>{{addon.product.name}}</td>
                                                                <td>{{addon.quantity}}</td>
                                                                <td>{{addon.yield}}</td>
                                                                <td>{{addon.customize}}</td>
                                                                <td align="left">
                                                                    <span data-ng-if="addon.defaultSetting == true"
                                                                          data-ng-click="selectQVMAddon(addon)"
                                                                          style="cursor: pointer"
                                                                          title="Remove Variant">
                                                                        <i class="fa fa-check"
                                                                           style="font-size: 25px; color: green"></i>
                                                                    </span>
                                                                    <span data-ng-if="addon.defaultSetting != true"
                                                                          data-ng-click="selectQVMAddon(addon)"
                                                                          style="cursor: pointer"
                                                                          title="Remove Variant">
                                                                        <i class="fa fa-check"
                                                                           style="font-size: 25px; color: grey"></i>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </uib-tab>


                                <uib-tab index="5" heading="Others">
                                    <div class="row divInnerRow">
                                        <div data-ng-if="isComboProduct" class="alert alert-info">
                                            <strong>Click Next!</strong> Nothing to add as its a combo
                                            product.
                                        </div>
                                        <div class="col-xs-12">
                                            <button data-ng-if="!isComboProduct" class="btn btn-primary pull-left"
                                                    data-ng-click="openOtherSCMProduct()">
                                                <i class="fa fa-plus"></i> Add New Product
                                            </button>
                                        </div>
                                        <div class="col-xs-12" style="margin-top: 20px;">
                                            <table class="table table-striped table-bordered"
                                                   data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.components != null && recipeDetail.ingredient.components.length > 0">
                                                <tr>
                                                    <th>Product Name</th>
                                                    <th>Unit Of Measure</th>
                                                    <th>Quantity</th>
                                                    <th>Yield</th>
                                                    <th>Is Critical</th>
                                                    <th>Actions</th>
                                                </tr>
                                                <tbody>
                                                <tr data-ng-repeat="variant in recipeDetail.ingredient.components track by $index">
                                                    <td>{{variant.product.name}}</td>
                                                    <td>{{variant.uom}}</td>
                                                    <td>{{variant.quantity}}</td>
                                                    <td>{{variant.yield}}</td>
                                                    <td>{{variant.critical}}</td>
                                                    <td align="left">
                                                        <span data-ng-click="editOtherSCMProduct(variant)"
                                                              style="cursor: pointer" title="Add/Edit Details">
                                                            <i class="fa fa-edit"
                                                               style="font-size: 24px; margin-right: 5px"></i>
                                                        </span>&nbsp;&nbsp;
                                                        <span data-ng-click="recipeDetail.ingredient.components.splice($index, 1);"
                                                              style="cursor: pointer" title="Remove Variant">
                                                            <i class="fa fa-remove"
                                                               style="font-size: 24px; margin-right: 5px; color: red;"></i>
                                                        </span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </uib-tab>
                            </uib-tabset>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 40px;">
                        <div class="col-xs-6">
                            <button type="button" class="btn btn-default btn-md pull-right"
                                    data-ng-if="activeIngredientTab!=0"
                                    data-ng-click="setPrevIngredientTab(activeIngredientTab)">
                                <i class="fa fa-play fa-rotate-180"> </i> Prev Ingredient
                            </button>
                        </div>
                        <div class="col-xs-6">
                            <button type="button" class="btn btn-default btn-md pull-left"
                                    data-ng-if="activeIngredientTab!=5"
                                    data-ng-click="setNextIngredientTab(activeIngredientTab)">
                                Next Ingredient <i class="fa fa-play"></i>
                            </button>
                        </div>
                    </div>
                </uib-tab>
                <uib-tab index="3" heading="Addons">
                    <addon-menu-product-directive addon-list="recipeDetail.addons"
                                                  mandatory-addon-list="recipeDetail.mandatoryAddons"
                                                  open-addon-menu-product="openAddonMenuProduct(type)"
                                                  edit-addon-menu-product="editAddonMenuProduct(variant,type)"
                                                  clone-recipe-profile="cloneRecipeProfile">
                    </addon-menu-product-directive>
                </uib-tab>
                <uib-tab index="4" heading="Options">
                    <options-product-directive option-list="recipeDetail.options"
                                               open-option-product="openOptionProduct()"
                                               open-option-addon-product-modal="openOptionAddonProductModal()"
                                               clone-recipe-profile="cloneRecipeProfile">
                    </options-product-directive>
                </uib-tab>
<!--                <uib-tab index="5" heading="Recommendations">-->
<!--                    <recommendation-directive recommendation-list="recipeDetail.recommendations"-->
<!--                                              open-recommendation="openRecommendation()"-->
<!--                                              edit-recommendation="editRecommendation(variant)"-->
<!--                                              clone-recipe-profile="cloneRecipeProfile">-->
<!--                    </recommendation-directive>-->
<!--                </uib-tab>-->
                <uib-tab index="5" heading="Dine In Consumables">
                    <addon-directive addon-list="recipeDetail.dineInConsumables" open-addon-product="openAddonProduct()"
                                     edit-addon-product="editAddonProduct(variant)">
                    </addon-directive>
                </uib-tab>
                <uib-tab index="6" heading="Delivery Consumables">
                    <addon-directive addon-list="recipeDetail.deliveryConsumables"
                                     open-addon-product="openAddonProduct()"
                                     edit-addon-product="editAddonProduct(variant)"></addon-directive>
                </uib-tab>
                <uib-tab index="7" heading="Takeaway Consumables">
                    <addon-directive addon-list="recipeDetail.takeawayConsumables"
                                     open-addon-product="openAddonProduct()"
                                     edit-addon-product="editAddonProduct(variant)"></addon-directive>
                </uib-tab>
                <uib-tab index="8" heading="Condiments">
                    <condiment-directive></condiment-directive>
                </uib-tab>
            </uib-tabset>
        </div>
        <div class="row pager" style="margin-top: 10px;">
            <nav>
                <ul class="pager">
                    <li data-ng-if="activeTab!=0" data-ng-click="setPrevTab(activeTab)" class="previous"
                        style="cursor: pointer">
                        <a><span aria-hidden="true">&larr;</span>Previous</a>
                    </li>
                    <li data-ng-if="activeTab==0" data-ng-click="clearAll(true)" style="cursor: pointer"><a>Reset </a>
                    </li>
                    <li data-ng-if="activeTab==8" data-ng-click="showPreview()" style="cursor: pointer"><a>Preview </a>
                    </li>
                    <li data-ng-if="activeTab > 0 && activeTab < 8" data-ng-click="setNextTab(activeTab)" class="next"
                        style="cursor: pointer">
                        <a>Next <span aria-hidden="true">&rarr;</span></a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
<!-- -- Preview Modal -->
<div class="modal fade previewModal" id="showPreviewModal" role="dialog" aria-labelledby="showPreviewModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="col-xs-6">
                    <h4 class="modal-title" id="showPreviewModalLabel">
                        {{recipeDetail.name}} Preview for Profile {{recipeDetail.profile}}
                        <span data-ng-if="currentDisplayRecipe == 'OLD'"> (ACTIVE Recipe)</span>
                        <span data-ng-if="currentDisplayRecipe == 'NEW' && displayDifference == null"> (ACTIVE Recipe)</span>
                    </h4>
                </div>
                <div class="col-xs-5">
                    <div class="btn-group" role="group">
                        <button type="button" data-ng-repeat="display in displayTypes track by $index"
                                data-ng-model="selectedDisplay"
                                data-ng-class="{'btn btn-default':selectedDisplay!=display,'btn btn-primary':selectedDisplay==display}"
                                data-ng-click="setSelectedDisplay(display)">{{display}}
                        </button>
                    </div>
                </div>
                <div class="col-xs-5" data-ng-if="!(displayDifference != null || isForApproval)">
                </div>
                <div class="col-xs-1 right">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                            data-ng-click="closeEditModal()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
            <div class="modal-body" data-ng-if="selectedDisplay != 'COMPARE'">
                <div class="row tabDiv" data-ng-if="recipeCostDetail != null">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-6" data-ng-if="isPreview==false && currentDisplayRecipe == 'NEW'">
                                <button class="btn btn-primary pull-left" data-ng-if="isPreview==false && currentDisplayRecipe == 'NEW'"
                                        data-ng-click="saveRecipe()">Save Recipe
                                </button>
                            </div>
                            <div class="col-xs-6" data-ng-if="isForApproval && currentDisplayRecipe == 'NEW'">
                                <button class="btn btn-danger"
                                        acl-action-checker="ADRAR"
                                        data-ng-click="rejectRecipeForApproval(recipeDetail, true)"
                                        data-ng-if="recipeDetail.status =='PENDING_APPROVAL'"
                                        style="margin-bottom: 10px;">
                                    Reject Recipe
                                </button>
                                <button class="btn btn-success"
                                        acl-action-checker="ADRAR"
                                        data-ng-click="sendRecipeForApproval(recipeDetail, false)"
                                        data-ng-if="recipeDetail.status =='PENDING_APPROVAL'"
                                        style="margin-bottom: 10px; margin-left: 10px;">
                                    Approve Recipe
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label>Select Region</label>
                            <select class="form-control" ng-model="selectedRegion" ng-options=" region for region in allRegions" data-ng-change="getCostOfRecipes(selectedRegion, true, currentDisplayRecipe)"> </select>
                        </div>
                    </div>
                    <div class="col-xs-12" data-ng-if=" recipeCostDetail != null">
                        <h3>Recipe Cost Details</h3>
                        <div class="row divInnerRow"
                             data-ng-if="recipeCostDetail.erroCodes != null && recipeCostDetail.erroCodes.length > 0">
                            <div class="col-xs-12" style="margin-top: 10px;">
                                <h4>Recipe Common Errors</h4>
                                <div class="row" data-ng-repeat="error in recipeCostDetail.erroCodes track by $index">
                                    <div class="col-xs-12">
                                        <label>{{error}}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row divInnerRow"
                             data-ng-repeat="category in recipeCostDetail.categoryCost track by $index"
                             data-ng-if="category.erroCodes != null && category.erroCodes.length > 0">
                            <div class="col-xs-12" style="margin-top: 10px;">
                                <h4>{{category.costType}} Errors</h4>
                                <div class="row" data-ng-repeat="error in category.erroCodes track by $index">
                                    <div class="col-xs-12">
                                        <label>{{error}}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row divInnerRow"
                             data-ng-if="recipeCostDetail.categoryCost != null && recipeCostDetail.categoryCost.length > 0 && (recipeDetail.status == 'ACTIVE' ||
                             isPreview==true && recipeDetail.status != 'ACTIVE' || currentDisplayRecipe == 'OLD')">
                            <div class="col-xs-12" style="margin-top: 10px;" data-ng-if="recipeDetail.status == 'ACTIVE'">
                                <h4>{{recipeCostDetail.recipeName}} Cost Summary</h4>
                                <div class="row"
                                     data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
                                    <div class="col-xs-12">
                                        <label>{{category.costType}} - {{category.cost}}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" data-ng-if="recipeDetail.status != 'ACTIVE' && displayDifference != null && currentDisplayRecipe == 'NEW'">
                            <h4 style="margin-left: 10px;">{{recipeCostDetail.recipeName}} Cost Summary</h4>
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th colspan="2"></th>
                                        <th>Proposed Recipe Cost</th>
                                        <th>Active Recipe Cost</th>
                                        <th title="(current - previous)/100">Difference Cost</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="2" style="font-weight: bold">CAFE</td>
                                        <td>{{displayDifference.currentCAFE}}</td>
                                        <td >{{displayDifference.previousCAFE != undefined ? displayDifference.previousCAFE : '-'}}</td>
                                        <td>{{displayDifference.diffCAFE != undefined ? (displayDifference.diffCAFE | number : 2) + '%' : '-'}}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" style="font-weight: bold">DELIVERY</td>
                                        <td>{{displayDifference.currentDELIVERY}}</td>
                                        <td>{{displayDifference.previousDELIVERY != undefined ? displayDifference.previousDELIVERY : '-'}}</td>
                                        <td>{{displayDifference.diffTAKE_AWAY != undefined ? (displayDifference.diffTAKE_AWAY | number : 2) + '%': '-'}}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" style="font-weight: bold" >TAKE_AWAY</td>
                                        <td>{{displayDifference.currentTAKE_AWAY}}</td>
                                        <td>{{displayDifference.previousTAKE_AWAY != undefined ? displayDifference.previousTAKE_AWAY : '-'}}</td>
                                        <td>{{displayDifference.diffTAKE_AWAY != undefined ? (displayDifference.diffTAKE_AWAY | number : 2) + '%' : '-'}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="row divInnerRow"
                             data-ng-if="recipeCostDetail.categoryCost != null && recipeCostDetail.categoryCost.length > 0">
                            <div class="col-xs-12" style="margin-top: 10px;">
                                <h4>Detailed Cost Calculation</h4>
                                <div class="row"
                                     data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
                                    <div class="col-xs-12">
                                        <div class="row">
                                            <div class="col-xs-12">
                                                <h5>{{recipeCostDetail.recipeName}} -
                                                    {{category.costType}} Cost Details ({{category.cost}})
                                                </h5>
                                            </div>
                                            <div class="col-xs-12">
                                                <table class="table table-striped table-bordered"
                                                       style="margin-top: 10px;">
                                                    <tr>
                                                        <th>Component Type</th>
                                                        <th>Type</th>
                                                        <th>Product Id</th>
                                                        <th>Product Name</th>
                                                        <th>UoM</th>
                                                        <th>Quantity</th>
                                                        <th>Price</th>
                                                        <th>Cost</th>
                                                    </tr>
                                                    <tbody>
                                                    <tr data-ng-repeat="ingredient in recipeCostDetail.commonIngredient track by $index"
                                                        data-ng-style="(ingredient.calculatedFromNegotiatedPrice == 'Y') ? (ingredient.cost == 0 ? {'background':'red'} : {'background':'orange'}) : {}">
                                                        <td>Common</td>
                                                        <td>{{ingredient.type}}</td>
                                                        <td>{{ingredient.productId}}</td>
                                                        <td>{{ingredient.productName}}</td>
                                                        <td>{{ingredient.uom}}</td>
                                                        <td>{{ingredient.quantity}}</td>
                                                        <td>{{ingredient.price}}</td>
                                                        <td>{{ingredient.cost}}</td>
                                                    </tr>
                                                    <tr data-ng-repeat="ingredient in category.ingredients track by $index">
                                                        <td>{{category.costType}}</td>
                                                        <td>{{ingredient.type}}</td>
                                                        <td>{{ingredient.productId}}</td>
                                                        <td>{{ingredient.productName}}</td>
                                                        <td>{{ingredient.uom}}</td>
                                                        <td>{{ingredient.quantity}}</td>
                                                        <td>{{ingredient.price}}</td>
                                                        <td>{{ingredient.cost}}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <h3>Recipe Constituent Details</h3>
                        <div class="row divInnerRow">
                            <div class="row" style="margin-left: 20px;">
                                <p>
                                    Allow Multi-Selection?
                                    <strong ng-class="{'text-success': recipeDetail.ingredient.compositeProduct.isAllowMultiselection, 'text-danger': !recipeDetail.ingredient.compositeProduct.isAllowMultiselection}">
                                        {{recipeDetail.ingredient.compositeProduct.isAllowMultiselection ? 'YES' : 'NO'}}
                                    </strong>
                                </p>
                            </div>
                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="!isQVM && recipeDetail.ingredient != null && recipeDetail.ingredient.compositeProduct != null && recipeDetail.ingredient.compositeProduct.details!= null && recipeDetail.ingredient.compositeProduct.details.length > 0"
                                 data-ng-repeat="detail in recipeDetail.ingredient.compositeProduct.details track by $index">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <h4>Options for {{detail.name}}</h4>
                                            </div>
                                            <div class="col-xs-3">
                                                <h4>Discount: {{detail.discount}}</h4>
                                            </div>
                                            <div class="col-xs-3">
                                                <h4>Internal Discount: {{detail.internalDiscount}}</h4>
                                            </div>
                                            <div class="col-xs-3">
                                                <h4>Internal Discount Type: {{detail.internalDiscountType}}</h4>
                                            </div>
                                            <div data-ng-if="detail.name == 'Item 1' && detail.customizable != null" class="col-xs-3">
                                                <h4>Customizable: {{detail.customizable}}</h4>
                                            </div>
                                            <div data-ng-if="detail.name !== 'Item 1' && detail.maxSelection != null" class="col-xs-3">
                                                <h4>Max Selection: {{detail.maxSelection}}</h4>
                                            </div>
                                            <div data-ng-if="detail.name !== 'Item 1' && detail.minSelection != null" class="col-xs-3">
                                                <h4>Min Selection: {{detail.minSelection}}</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12">
                                        <table class="table table-striped table-bordered" style="margin-top: 10px;">
                                            <tr>
                                                <th>Product Name</th>
                                                <th>Dimension</th>
                                                <th>Quantity</th>
                                            </tr>
                                            <tbody>
                                            <tr ng-repeat="variant in detail.menuProducts track by $index">
                                                <td>{{variant.product.name}} ({{variant.product.displayName}})</td>
                                                <td>{{variant.dimension.name}}</td>
                                                <td>{{variant.quantity}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="isQVM && recipeDetail.ingredient != null && recipeDetail.ingredient.compositeProduct != null && recipeDetail.ingredient.compositeProduct.details!= null && recipeDetail.ingredient.compositeProduct.details.length > 0"
                                 data-ng-repeat="detail in recipeDetail.ingredient.compositeProduct.details track by $index">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <h4>Options for {{detail.name}}</h4>
                                    </div>
                                    <div class="col-xs-12" ng-repeat="mp in detail.menuProducts track by $index">
                                        <p>{{mp.product.name}} - {{mp.dimension.name}} ({{mp.quantity}})</p>
                                        <div class="row">
                                            <div class="col-xs-12" style="margin-top: 10px;"
                                                 data-ng-if="mp.ingredient != null && mp.ingredient.variants != null && mp.ingredient.variants.length > 0">
                                                <h4>Ingredient Variant Details</h4>
                                                <table class="table table-striped table-bordered">
                                                    <tr>
                                                        <th>Product Id</th>
                                                        <th>Product Name</th>
                                                        <th>Unit Of Measure</th>
                                                        <th>Is Critical</th>
                                                        <th>Is Customizable</th>
                                                        <th>Details</th>
                                                    </tr>
                                                    <tr data-ng-repeat="variant in mp.ingredient.variants">
                                                        <td>{{variant.product.productId}}</td>
                                                        <td>{{variant.product.name}}</td>
                                                        <td>{{variant.uom}}</td>
                                                        <td>{{variant.critical}}</td>
                                                        <td>{{variant.customize}}</td>
                                                        <td>{{printVariantDetails(variant.details)}}</td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div class="col-xs-12" style="margin-top: 20px;">
                                                <h4>Addons Details</h4>
                                                <table class="table table-striped table-bordered">
                                                    <tr>
                                                        <th>Product Name</th>
                                                        <th>Quantity</th>
                                                        <th>Yield</th>
                                                        <th>Customizable</th>
                                                    </tr>
                                                    <tr ng-repeat="addon in mp.addons track by $index"
                                                        data-ng-if="addon.defaultSetting == true">
                                                        <td>{{addon.product.name}}</td>
                                                        <td>{{addon.quantity}}</td>
                                                        <td>{{addon.yield}}</td>
                                                        <td>{{addon.customize}}</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.variants != null && recipeDetail.ingredient.variants.length > 0">
                                <h4>Ingredient Variant Details</h4>
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Product Id</th>
                                        <th>Product Name</th>
                                        <th>Unit Of Measure</th>
                                        <th>Is Critical</th>
                                        <th>Is Customizable</th>
                                        <th>Tag</th>
                                        <th>Description</th>
                                        <th>Details</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="variant in recipeDetail.ingredient.variants">
                                        <td>{{variant.product.productId}}</td>
                                        <td>{{variant.product.name}}</td>
                                        <td>{{variant.uom}}</td>
                                        <td>{{variant.critical}}</td>
                                        <td>{{variant.customize}}</td>
                                        <td>{{printTag(variant.details)}}</td>
                                        <td>{{printDescription(variant.details)}}</td>
                                        <td>{{printVariantDetails(variant.details)}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.products != null && recipeDetail.ingredient.products.length > 0">
                                <h4>Ingredient SCM Details</h4>
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Product Category</th>
                                        <th>Display</th>
                                        <th>Is Critical</th>
                                        <th>Tag</th>
                                        <th>Instruction</th>
                                        <th>Description</th>
                                        <th>Is Customizable</th>
                                        <th>Details</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="variant in recipeDetail.ingredient.products track by $index">
                                        <td>{{variant.category.name}}</td>
                                        <td>{{variant.display}}</td>
                                        <td>{{variant.critical}}</td>
                                        <td>{{printTag(variant.details)}}</td>
                                        <td>{{printInstruction(variant.details)}}</td>
                                        <td>{{printDescription(variant.details)}}</td>
                                        <td>{{variant.customize}}</td>
                                        <td>{{printSCMProductDetails(variant.details)}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.addons != null  && recipeDetail.addons.length > 0">
                                <h4>Addon Details</h4>
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Product Name</th>
                                        <th>Quantity</th>
                                        <th>Tag</th>
                                        <th>Yield</th>
                                        <th>Is Critical</th>
                                        <th>Customizable</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="addon in recipeDetail.addons track by $index">
                                        <td>{{addon.product.name}}</td>
                                        <td>{{addon.quantity}}</td>
                                        <td>{{addon.tag}}</td>
                                        <td>{{addon.yield}}</td>
                                        <td>{{addon.critical}}</td>
                                        <td>{{addon.customize}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.mandatoryAddons != null  && recipeDetail.mandatoryAddons.length > 0">
                                <h4>Mandatory Addon Details</h4>
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Product Name</th>
                                        <th>Quantity</th>
                                        <th>Tag</th>
                                        <th>Yield</th>
                                        <th>Customizable</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="mandatoryAddon in recipeDetail.mandatoryAddons track by $index">
                                        <td>{{mandatoryAddon.product.name}}</td>
                                        <td>{{mandatoryAddon.quantity}}</td>
                                        <td>{{mandatoryAddon.tag}}</td>
                                        <td>{{mandatoryAddon.yield}}</td>
                                        <td>{{mandatoryAddon.customize}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>


                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.options != null  && recipeDetail.options.length > 0">
                                <h4>Paid Addon Details</h4>
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Product Name</th>
                                        <th>Type</th>
                                        <th>Short Code</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="paidAddOn in recipeDetail.options track by $index">
                                        <td title="{{paidAddOn.productId}}">{{paidAddOn.name}}</td>
                                        <td>{{paidAddOn.type}}</td>
                                        <td>{{paidAddOn.shortCode}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.recommendations != null  && recipeDetail.recommendations.length > 0">
                                <h4>Recommendation Details</h4>
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Product Name</th>
                                        <th>Quantity</th>
                                        <th>Dimension</th>
                                        <th>Yield</th>
                                        <th>Customizable</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="recommendation in recipeDetail.recommendations track by $index">
                                        <td>{{recommendation.product.name}}</td>
                                        <td>{{recommendation.quantity}}</td>
                                        <td>{{recommendation.dimension.name}}</td>
                                        <td>{{recommendation.yield}}</td>
                                        <td>{{recommendation.customize}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-xs-12" style="margin-top: 10px;"
                                 data-ng-if="recipeDetail.condiments != null">
                                <h4>Condiment Details</h4>
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <th>Source </th>
                                    <th>Group Id</th>
                                    <th>Group Name</th>
                                    <th>Quantity</th>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>CAFE</td>
                                        <td data-ng-if="dineCondiment!=null">{{dineCondiment.groupId}}</td>
                                        <td data-ng-if="dineCondiment!=null">{{dineCondiment.groupName}}</td>
                                        <td data-ng-if="dineQuantity!=null">{{dineQuantity}}</td>
                                    </tr>
                                    <tr>
                                        <td>COD</td>
                                        <td data-ng-if="codCondiment!=null">{{codCondiment.groupId}}</td>
                                        <td data-ng-if="codCondiment!=null">{{codCondiment.groupName}}</td>
                                        <td data-ng-if="codQuantity!=null">{{codQuantity}}</td>
                                    </tr>
                                    <tr>
                                        <td>TAKE AWAY</td>
                                        <td data-ng-if="takeawayCondiment!=null">{{takeawayCondiment.groupId}}</td>
                                        <td data-ng-if="takeawayCondiment!=null">{{takeawayCondiment.groupName}}</td>
                                        <td data-ng-if="takeawayQuantity!=null">{{takeawayQuantity}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <product-view list-label="'Other Products'"
                                          ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.components != null && recipeDetail.ingredient.components.length > 0"
                                          product-list="recipeDetail.ingredient.components"></product-view>
                            <!--<product-view list-label="'Addon Products'"-->
                            <!--ng-if="recipeDetail.addons != null && recipeDetail.addons.length > 0"-->
                            <!--product-list="recipeDetail.addons"></product-view>-->
                            <product-view list-label="'Dine In Cosumables'"
                                          ng-if="recipeDetail.dineInConsumables != null && recipeDetail.dineInConsumables.length > 0"
                                          product-list="recipeDetail.dineInConsumables"></product-view>
                            <product-view list-label="'Delivery Consumables'"
                                          ng-if="recipeDetail.deliveryConsumables != null && recipeDetail.deliveryConsumables.length > 0"
                                          product-list="recipeDetail.deliveryConsumables"></product-view>
                            <product-view list-label="'Take Away Consumbales'"
                                          ng-if="recipeDetail.takeawayConsumables != null && recipeDetail.takeawayConsumables.length > 0"
                                          product-list="recipeDetail.takeawayConsumables"></product-view>

                        </div>
                        </div>
                    </div>
                <div class="row tabDiv" data-ng-if="recipeCostDetail == null">
                    <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No Active Recipe Found To Compare ..!</div>
                </div>
            </div>
            <div class="modal-body" data-ng-if="selectedDisplay == 'COMPARE'">
                <div data-ng-if="compareRecipeDetail != null && compareRecipeDetail.displayOnUi">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label>Select Region</label>
                        <select class="form-control" ng-model="selectedRegion" ng-options=" region for region in allRegions" data-ng-change="getCostOfRecipes(selectedRegion, true, currentDisplayRecipe)"> </select>
                    </div>
                </div>
                <h4>Recipe Cost Comparison</h4>
                <div class="col-xs-12">
                    <h5>Total Orders Of Active Recipe : {{monthlyOrdersOfRecipe['kettleOrdersQuantity']}}</h5>
                    <h5>Total Sale Amount Of Active Recipe : Rs. {{monthlyOrdersOfRecipe['kettleOrdersSale']}}</h5>
                </div>
                <div class="row" data-ng-if="recipeDetail.status != 'ACTIVE' && displayDifference != null">
                    <h4 style="margin-left: 10px;">{{recipeCostDetail.recipeName}} Cost Summary</h4>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th colspan="2"></th>
                            <th>Proposed Recipe Cost</th>
                            <th>Active Recipe Cost</th>
                            <th title="(current - previous)/100">Difference Cost</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="2" style="font-weight: bold">CAFE</td>
                            <td>{{displayDifference.currentCAFE}}</td>
                            <td >{{displayDifference.previousCAFE != undefined ? displayDifference.previousCAFE : '-'}}</td>
                            <td>{{displayDifference.diffCAFE != undefined ? (displayDifference.diffCAFE | number : 2) + '%' : '-'}}</td>
                        </tr>
                        <tr>
                            <td colspan="2" style="font-weight: bold">DELIVERY</td>
                            <td>{{displayDifference.currentDELIVERY}}</td>
                            <td>{{displayDifference.previousDELIVERY != undefined ? displayDifference.previousDELIVERY : '-'}}</td>
                            <td>{{displayDifference.diffTAKE_AWAY != undefined ? (displayDifference.diffTAKE_AWAY | number : 2) + '%': '-'}}</td>
                        </tr>
                        <tr>
                            <td colspan="2" style="font-weight: bold" >TAKE_AWAY</td>
                            <td>{{displayDifference.currentTAKE_AWAY}}</td>
                            <td>{{displayDifference.previousTAKE_AWAY != undefined ? displayDifference.previousTAKE_AWAY : '-'}}</td>
                            <td>{{displayDifference.diffTAKE_AWAY != undefined ? (displayDifference.diffTAKE_AWAY | number : 2) + '%' : '-'}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <h4>Cost Calculation</h4>
                <div class="col-xs-12 container" data-ng-repeat="costType in ['CAFE', 'DELIVERY', 'TAKE_AWAY']">
                    <h4>{{costType}} (A - ACTIVE, P - PROPOSED)</h4>
                    <table class="table table-striped table-bordered"
                           style="margin-top: 10px;">
                        <tr>
                            <th>Component Type</th>
                            <th>Type</th>
                            <th>Product Id</th>
                            <th>Product Name</th>
                            <th>UoM</th>
                            <th>ACTIVE</th>
                            <th>PROPOSED</th>
                            <th>Quantity(A)</th>
                            <th>Quantity(P)</th>
                            <th>Price(A)</th>
                            <th>Price(P)</th>
                            <th>Cost(A)</th>
                            <th>Cost(P)</th>
                        </tr>
                        <tbody>
                        <tr data-ng-repeat="ingredient in compareRecipeDetail.commonIngredient track by $index"
                            data-ng-style="(ingredient.calculatedFromNegotiatedPrice == 'Y') ? (ingredient.cost == 0 ? {'background':'red'} : {'background':'orange'}) : {}">
                            <td>Common</td>
                            <td>{{ingredient.type}}</td>
                            <td>{{ingredient.productId}}</td>
                            <td>{{ingredient.productName}}</td>
                            <td>{{ingredient.uom}}</td>
                            <td data-ng-if="ingredient.isInActive">&check;</td>
                            <td data-ng-if="!ingredient.isInActive">&cross;</td>
                            <td data-ng-if="ingredient.isInProposed">&check;</td>
                            <td data-ng-if="!ingredient.isInProposed">&cross;</td>
                            <td>{{ingredient.activeQuantity}}</td>
                            <td>{{ingredient.proposedQuantity}}</td>
                            <td>{{ingredient.activePrice}}</td>
                            <td>{{ingredient.proposedPrice}}</td>
                            <td>{{ingredient.activeCost}}</td>
                            <td>{{ingredient.proposedCost}}</td>
                        </tr>
                        <tr data-ng-repeat="ingredient in getCostTypeProducts(costType, compareRecipeDetail) track by $index">
                            <td>{{costType}}</td>
                            <td>{{ingredient.type}}</td>
                            <td>{{ingredient.productId}}</td>
                            <td>{{ingredient.productName}}</td>
                            <td>{{ingredient.uom}}</td>
                            <td data-ng-if="ingredient.isInActive">&check;</td>
                            <td data-ng-if="!ingredient.isInActive">&cross;</td>
                            <td data-ng-if="ingredient.isInProposed">&check;</td>
                            <td data-ng-if="!ingredient.isInProposed">&cross;</td>
                            <td>{{ingredient.activeQuantity}}</td>
                            <td>{{ingredient.proposedQuantity}}</td>
                            <td>{{ingredient.activePrice}}</td>
                            <td>{{ingredient.proposedPrice}}</td>
                            <td>{{ingredient.activeCost}}</td>
                            <td>{{ingredient.proposedCost}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-12" style="margin-top: 10px;"
                     data-ng-if="compareRecipeDetail.ingredientVariants.length > 0">
                    <h4>Ingredient Variant Details (A - ACTIVE, P - PROPOSED)</h4>
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>Product Id</th>
                            <th>Product Name</th>
                            <th>Unit Of Measure</th>
                            <th>ACTIVE</th>
                            <th>PROPOSED</th>
                            <th>Is Critical(A)</th>
                            <th>Is Critical(P)</th>
                            <th>Is Customizable(A)</th>
                            <th>Is Customizable(P)</th>
                            <th>Tag(A)</th>
                            <th>Tag(P)</th>
                            <th>Description(A)</th>
                            <th>Description(P)</th>
                            <th>Details(A)</th>
                            <th>Details(P)</th>
                        </tr>
                        <tbody>
                        <tr data-ng-repeat="variant in compareRecipeDetail.ingredientVariants">
                            <td>{{variant.product.productId}}</td>
                            <td>{{variant.product.name}}</td>
                            <td>{{variant.uom}}</td>
                            <td data-ng-if="variant.isInActive">&check;</td>
                            <td data-ng-if="!variant.isInActive">&cross;</td>
                            <td data-ng-if="variant.isInProposed">&check;</td>
                            <td data-ng-if="!variant.isInProposed">&cross;</td>
                            <td>{{variant.activeCritical}}</td>
                            <td>{{variant.proposedCritical}}</td>
                            <td>{{variant.activeCustomizable}}</td>
                            <td>{{variant.proposedCustomizable}}</td>
                            <td>{{printTag(variant.activeDetails)}}</td>
                            <td>{{printTag(variant.proposedDetails)}}</td>
                            <td>{{printDescription(variant.activeDetails)}}</td>
                            <td>{{printDescription(variant.proposedDetails)}}</td>
                            <td>{{printVariantDetails(variant.activeDetails)}}</td>
                            <td>{{printVariantDetails(variant.proposedDetails)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-xs-12" style="margin-top: 10px;" data-ng-repeat="recipeType in [currentActiveRecipe,cloneOfRecipeDetail]"
                     data-ng-if="recipeType.ingredient != null && recipeType.ingredient.products != null && recipeType.ingredient.products.length > 0">
                    <h4>Ingredient SCM Details{{recipeType.status ==='ACTIVE' ? "(Active Recipe)" : "(Proposed Recipe)"}}</h4>
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>Product Category</th>
                            <th>Display</th>
                            <th>Is Critical</th>
                            <th>Tag</th>
                            <th>Instruction</th>
                            <th>Description</th>
                            <th>Is Customizable</th>
                            <th>Details</th>
                        </tr>
                        <tbody>
                        <tr data-ng-repeat="variant in recipeType.ingredient.products track by $index">
                            <td>{{variant.category.name}}</td>
                            <td>{{variant.display}}</td>
                            <td>{{variant.critical}}</td>
                            <td>{{printTag(variant.details)}}</td>
                            <td>{{printInstruction(variant.details)}}</td>
                            <td>{{printDescription(variant.details)}}</td>
                            <td>{{variant.customize}}</td>
                            <td>{{printSCMProductDetails(variant.details)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-xs-12" style="margin-top: 10px;"
                     data-ng-if="compareRecipeDetail.addonProducts != null  && compareRecipeDetail.addonProducts.length > 0">
                    <h4>Addon Details (A - ACTIVE, P - PROPOSED)</h4>
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>Product Name</th>
                            <th>ACTIVE</th>
                            <th>PROPOSED</th>
                            <th>Quantity(A)</th>
                            <th>Quantity(P)</th>
                            <th>Tag(A)</th>
                            <th>Tag(P)</th>
                            <th>Yield(A)</th>
                            <th>Yield(P)</th>
                            <th>Is Critical(A)</th>
                            <th>Is Critical(P)</th>
                            <th>Customizable(A)</th>
                            <th>Customizable(P)</th>
                        </tr>
                        <tbody>
                        <tr data-ng-repeat="addon in compareRecipeDetail.addonProducts track by $index">
                            <td>{{addon.product.name}}</td>
                            <td data-ng-if="addon.isInActive">&check;</td>
                            <td data-ng-if="!addon.isInActive">&cross;</td>
                            <td data-ng-if="addon.isInProposed">&check;</td>
                            <td data-ng-if="!addon.isInProposed">&cross;</td>
                            <td>{{addon.activeQuantity}}</td>
                            <td>{{addon.proposedQuantity}}</td>
                            <td>{{addon.activeTag}}</td>
                            <td>{{addon.proposedTag}}</td>
                            <td>{{addon.activeYield}}</td>
                            <td>{{addon.proposedYield}}</td>
                            <td>{{addon.activeCritical}}</td>
                            <td>{{addon.proposedCritical}}</td>
                            <td>{{addon.activeCustomize}}</td>
                            <td>{{addon.proposedCustomize}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-xs-12" style="margin-top: 10px;"
                     data-ng-if="compareRecipeDetail.mandatoryAddonProducts != null  && compareRecipeDetail.mandatoryAddonProducts.length > 0">
                    <h4>Mandatory Addon Details (A - ACTIVE, P - PROPOSED)</h4>
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>Product Name</th>
                            <th>ACTIVE</th>
                            <th>PROPOSED</th>
                            <th>Quantity(A)</th>
                            <th>Quantity(P)</th>
                            <th>Tag(A)</th>
                            <th>Tag(P)</th>
                            <th>Yield(A)</th>
                            <th>Yield(P)</th>
                            <th>Customizable(A)</th>
                            <th>Customizable(P)</th>
                        </tr>
                        <tbody>
                        <tr data-ng-repeat="addon in compareRecipeDetail.mandatoryAddonProducts track by $index">
                            <td>{{addon.product.name}}</td>
                            <td data-ng-if="addon.isInActive">&check;</td>
                            <td data-ng-if="!addon.isInActive">&cross;</td>
                            <td data-ng-if="addon.isInProposed">&check;</td>
                            <td data-ng-if="!addon.isInProposed">&cross;</td>
                            <td>{{addon.activeQuantity}}</td>
                            <td>{{addon.proposedQuantity}}</td>
                            <td>{{addon.activeTag}}</td>
                            <td>{{addon.proposedTag}}</td>
                            <td>{{addon.activeYield}}</td>
                            <td>{{addon.proposedYield}}</td>
                            <td>{{addon.activeCustomize}}</td>
                            <td>{{addon.proposedCustomize}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-xs-12" style="margin-top: 10px;"
                     data-ng-if="compareRecipeDetail.paidAddonProducts != null  && compareRecipeDetail.paidAddonProducts.length > 0">
                    <h4>Paid Addon Details</h4>
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>Product Name</th>
                            <th>Type</th>
                            <th>Short Code</th>
                            <th>ACTIVE</th>
                            <th>PROPOSED</th>
                        </tr>
                        <tbody>
                        <tr data-ng-repeat="paidAddOn in compareRecipeDetail.paidAddonProducts track by $index">
                            <td title="{{paidAddOn.productId}}">{{paidAddOn.name}}</td>
                            <td>{{paidAddOn.type}}</td>
                            <td>{{paidAddOn.shortCode}}</td>
                            <td data-ng-if="paidAddOn.isInActive">&check;</td>
                            <td data-ng-if="!paidAddOn.isInActive">&cross;</td>
                            <td data-ng-if="paidAddOn.isInProposed">&check;</td>
                            <td data-ng-if="!paidAddOn.isInProposed">&cross;</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-xs-12" style="margin-top: 10px;"
                     data-ng-if="compareRecipeDetail.otherProducts != null  && compareRecipeDetail.otherProducts.length > 0">
                <h4>Other Products</h4>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Product Name</th>
                        <th>Unit Of Measure</th>
                        <th>ACTIVE</th>
                        <th>PROPOSED</th>
                        <th>Quantity(A)</th>
                        <th>Quantity(P)</th>
                        <th>Is Critical(A)</th>
                        <th>Is Critical(P)</th>
                        <th>Is Customizable(A)</th>
                        <th>Is Customizable(P)</th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="variant in compareRecipeDetail.otherProducts track by $index">
                            <td>{{variant.product.name}}</td>
                            <td>{{variant.uom}}</td>
                            <td data-ng-if="variant.isInActive">&check;</td>
                            <td data-ng-if="!variant.isInActive">&cross;</td>
                            <td data-ng-if="variant.isInProposed">&check;</td>
                            <td data-ng-if="!variant.isInProposed">&cross;</td>
                            <td>{{variant.activeQuantity}}</td>
                            <td>{{variant.proposedQuantity}}</td>
                            <td>{{variant.activeCritical}}</td>
                            <td>{{variant.proposedCritical}}</td>
                            <td>{{variant.activeCustomize}}</td>
                            <td>{{variant.proposedCustomize}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
                    <div class="row tabDiv" data-ng-if="compareRecipeDetail == null || !compareRecipeDetail.displayOnUi">
                        <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;" data-ng-if="currentActiveRecipe != null">Loading Data To Compare.Please Wait...!</div>
                        <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;" data-ng-if="currentActiveRecipe == null">No Active Recipe To Compare..!</div>
                    </div>
            </div>
        </div>
        </div>
    </div>

<!-- -- Add Menu Products Modal -->
<div class="modal fade" id="showLogModal" role="dialog" aria-labelledby="showLogModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="showLogModalHead">
                    Recipe Logs
                </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <table class="table table-striped table-bordered">
                            <tr>
                                <th>Updated By</th>
                                <th>Update Time</th>
                                <th>Comment</th>
                            </tr>
                            <tr data-ng-repeat="log in recipeLogs">
                                <td>{{log.recipeId}}</td>
                                <td>{{log.updateTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                                <td>{{log.updatedBy.name}} [{{log.updatedBy.id}}]</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-xs-12">
                        <button type="button" class="btn btn-primary" data-dismiss="modal" aria-label="Close">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- profile mapping modal -->
<div class="modal fade previewModal" id="showRecipeProfileInactiveModal" role="dialog" aria-labelledby="showRecipeProfileInactiveModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="showRecipeProfileInactiveModalHead">
                   Inactivate {{selectedRecipeForInactivation.name}} - {{selectedRecipeForInactivation.profile}}
                </h4>
            </div>
            <div class="modal-body">
                <div class="row" data-ng-if="recipeInactiveUnits.length > 0">
                    <h4 style="margin-left: 20px;font-weight: bold;">Un map the below listed units First ..!</h4>
                    <div style="margin-left: 20px;" class="row s2">
                        <div class="col">
                            <label>Select all</label>
                            <input type="checkbox" id="selectAllId" ng-model="selectedAllUnits" data-ng-click="selectedAllUnitCheck(selectedAllUnits)"/>
                        </div>
                        <div class="col" data-ng-if="selectedAllUnits">
                            <label>Select profile for all units</label>
                            <select ui-select2 style="width: 20% !important" data-ng-model="selectedProfileForAllUnits" data-placeholder="Select profile..." data-ng-change="onChangeSelectedProfileForAllUnits(selectedProfileForAllUnits)">
                                <option data-ng-repeat="profile in allRecipesForProduct | filter: '!'+selectedRecipeForInactivation.profile track by $index">{{profile}}</option>
                            </select>
                        </div>
                        <br>
                    </div>
                    <div class="col-xs-12">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>S.No</th>
                                    <th>Unit Id</th>
                                    <th><input type="text" placeholder="Search unit name" data-ng-model="searchText" data-ng-change="applyFilters(searchText, model.unitPPStatus, model.unitStatus)"/></th>
                                    <th>Select Profile</th>
                                    <th>Unit product price status
                                        <select style="width: 60% !important" data-ng-model="model.unitPPStatus" data-placeholder="Filter unit product price status" data-ng-change="applyFilters(searchText, model.unitPPStatus, model.unitStatus)">
                                            <option data-ng-repeat="status in statusOptions">{{status}}</option>
                                        </select>
                                        <button class="btn-xs-small btn-primary" data-ng-if="model.unitPPStatus != null" data-ng-click="unfilter('status')">unFilter</button>
                                    </th>
                                    <th>Unit Status
                                        <select style="width: 60% !important" data-ng-model="model.unitStatus" data-placeholder="Filter unit status" data-ng-change="applyFilters(searchText, model.unitPPStatus, model.unitStatus)">
                                            <option data-ng-repeat="status in statusOptions">{{status}}</option>
                                        </select>
                                        <button class="btn-xs-small btn-primary" data-ng-if="model.unitStatus != null" data-ng-click="unfilter('code')">unFilter</button>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr data-ng-repeat="unit in recipeInactiveUnits" data-ng-if="unit.isVisible" data-ng-class="{'row-selected': (unit.selected && unit.profile != null), 'row-non-editable': !unit.canEdit}">
                                    <td><input type="checkbox" id="selectId" ng-model="unit.selected" class="center-block" data-ng-disabled="!unit.canEdit"/></td>
                                    <td>{{$index+1}}</td>
                                    <td>{{unit.id}}</td>
                                    <td>{{unit.name}}</td>
                                    <td>
                                        <select style="width: 100% !important" data-ng-model="unit.profile" data-placeholder="Select profile..." data-ng-if="unit.canEdit">
                                            <option data-ng-repeat="profile in allRecipesForProduct | filter: '!'+selectedRecipeForInactivation.profile track by $index">{{profile}}</option>
                                        </select>
                                        <p data-ng-hide="unit.canEdit" style="color: red;" title="UNIT status and UNIT PRODUCT PRICE status is ACTIVE, So you cannot change RECIPE PROFILE">Selection not available</p>
                                    </td>
                                    <td>{{unit.status}}</td>
                                    <td>{{unit.code}}</td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>
                <div class="row" data-ng-if="recipeInactiveUnits.length == 0">
                    <h4 style="margin-left: 20px;font-weight: bold;">No Units Mapped with the Selected Recipe Profile</h4>
                    <br>
                </div>
                <div class="row">
                    <div class="col-xs-6">
                        <button type="button" class="btn btn-primary" data-dismiss="modal" aria-label="Close">
                            Close
                        </button>
                    </div>
                    <div class="col-xs-6 pull-right" data-ng-if="recipeInactiveUnits.length == 0">
                        <button type="button" class="btn btn-danger" data-ng-click="changeRecipeStatus()">
                            In Activate
                        </button>
                    </div>
                    <div class="col-xs-6 pull-right" data-ng-if="recipeInactiveUnits.length > 0">
                        <button type="button" class="btn btn-success" data-ng-click="changeRecipeProfiles()">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- -- Add Variant Modal Primary-->
<div class="modal fade" id="addVariantModal" role="dialog" aria-labelledby="addVariantModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addVariantModalLabel">Select Variant Product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Select Product</label>
                            </div>
                            <div class="col-xs-8">
                                <select ui-select2 class="form-control" style="width: 100% !important"
                                        data-ng-model="selectedProductVariantId"
                                        data-ng-change="selectVariantProduct(selectedProductVariantId)"
                                        data-placeholder="Search Product...">
                                    <option data-ng-repeat="product in scmProductsInfo" value="{{product.productId}}">
                                        {{product.productName}}  ({{product.category.code}})
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div ng-show="recipeDetail.dispensed">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dispensed</label>
                                </div>
                                <div class="col-xs-8">
                                    <input type="checkbox" data-ng-model="selectedVariantProduct.dispensed"
                                           class="center-block" style="height: 2em; width: 2em;"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div ng-show="recipeDetail.dispensed && selectedVariantProduct.dispensed">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dispense Tag</label>
                                </div>
                                <div class="col-xs-8">
                                    <select class="form-control" style="width: 100% !important"
                                            data-ng-model="selectedVariantProduct.dispenseTag"
                                            ng-options="tags for tags in dispenseTagList.dispenseTags">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Display Name:</label>
                            </div>
                            <div class="col-xs-8">
                                <input type="text" data-ng-model="newVariantProductDisplayName" class="form-control"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Unit Of Measure</label>
                            </div>
                            <div class="col-xs-8">
                                <label class="form-control">{{selectedVariantProduct.unitOfMeasure}}</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Critical Product</label>
                            </div>
                            <div class="col-xs-8">
                                <input type="checkbox" data-ng-model="selectedVariantProduct.critical"
                                       class="center-block" style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Customizable</label>
                            </div>
                            <div class="col-xs-8">
                                <input type="checkbox" data-ng-model="selectedVariantProduct.customize"
                                       class="center-block" style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-6">
                                <button class="btn btn-primary pull-right" data-dismiss="modal" style="width: 90px">
                                    <i class="fa fa-remove"></i> Cancel
                                </button>
                            </div>
                            <div class="col-xs-6">
                                <button class="btn btn-primary pull-left" data-ng-click="addVariantProduct()"
                                        style="width: 90px">
                                    <i class="fa fa-plus"></i> Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Add Variant Modal Secondary (Aliases Modal) -->
<div class="modal fade" id="addVariantAliasModal" role="dialog" aria-labelledby="addVariantAliasModal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="addVariantAliasModalLabel">Select Variant Alias</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin: 0px;">
                    <div class="col-xs-12"
                         style="background-color: #f7f7f9; border: 1px solid #e1e1e8; border-radius: 4px;">
                        <div class="row divInnerRow">
                            <div class="col-xs-2">
                                <label>Product:</label>
                            </div>
                            <div class="col-xs-5">
                                {{selectedVariantProductForEdit.product.name}}
                            </div>
                            <div class="col-xs-2">
                                <label>UoM:</label>
                            </div>
                            <div class="col-xs-3">
                                {{selectedVariantProductForEdit.uom}}
                            </div>
                        </div>
                        <div class="row divInnerRow">
                            <div class="col-xs-3">
                                <label>Display Name</label>
                            </div>
                            <div class="col-xs-3">
                                <input type="text" data-ng-model="selectedVariantProductForEdit.product.displayName"
                                       class="form-control"/>
                            </div>
                        </div>
                        <div class="row divInnerRow">
                            <div class="col-xs-3">
                                <label>Is Critical</label>
                            </div>
                            <div class="col-xs-3">
                                <input type="checkbox" data-ng-model="selectedVariantProductForEdit.critical"
                                       class="center-block" style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div ng-show="recipeDetail.dispensed">
                                <div class="row divInnerRow">
                                    <div class="col-xs-3">
                                        <label>Dispensed</label>
                                    </div>
                                    <div class="col-xs-8">
                                        <input type="checkbox" data-ng-model="selectedVariantProductForEdit.dispensed"
                                               class="center-block" style="height: 2em; width: 2em;"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div ng-show="recipeDetail.dispensed && selectedVariantProductForEdit.dispensed">
                                <div class="row divInnerRow">
                                    <div class="col-xs-3">
                                        <label>Dispense Tag</label>
                                    </div>
                                    <div class="col-xs-8">
                                        <select class="form-control" style="width: 100% !important"
                                                data-ng-model="selectedVariantProductForEdit.dispenseTag"
                                                ng-options="tags for tags in dispenseTagList.dispenseTags">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row divInnerRow">
                            <div class="col-xs-3">
                                <label>Customizable</label>
                            </div>
                            <div class="col-xs-3">
                                <input type="checkbox" data-ng-model="selectedVariantProductForEdit.customize"
                                       class="center-block" style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <button class="btn btn-primary pull-left"
                                        data-ng-click="addAnotherVariantAlias(selectedVariantProductForEdit)">
                                    <i class="fa fa-plus" style="margin-right: 5px;"></i>Add Another Alias
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row">
                            <table class="table table-striped table-bordered">
                                <tr>
                                    <th>Default</th>
                                    <th>Alias</th>
                                    <th>Description</th>
                                    <th>Tag</th>
                                    <th>Quantity</th>
                                    <th>Yield</th>
                                    <th>Actions</th>
                                </tr>
                                <tbody>
                                <tr data-ng-repeat="aliasDetail in selectedVariantProductForEdit.details">
                                    <td><input type="checkbox" data-ng-model="aliasDetail.defaultSetting"
                                               data-ng-click="markDefault(selectedVariantProductForEdit.details, $index)"
                                               class="center-block" style="height: 2em; width: 2em;"/></td>
                                    <td style="width:20%;"><input class="form-control"
                                                                  data-ng-model="aliasDetail.alias"/></td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.desc"/></td>
                                    <td style="width:20%;"><select ng-options="tag for tag in  tagList"
                                                                   class="form-control"
                                                                   data-ng-model="aliasDetail.tag"/></td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.quantity"/></td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.yield"/></td>
                                    <td align="left">
                                        <span data-ng-click="selectedVariantProductForEdit.details.splice($index, 1);"
                                              style="cursor: pointer" title="Remove Variant ALias">
                                            <i class="fa fa-remove" style="font-size: 25px; color: red"></i>
                                        </span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <button class="btn btn-primary center-block" data-ng-click="validateVariantAliases()">
                                    Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Add and Edit SCM Modal Primary -->
<div class="modal fade" id="addSCMProductModal" role="dialog" aria-labelledby="addSCMProductModal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addSCMProductModalLabel">Select SCM Product</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin: 0px">
                    <div class="col-xs-12"
                         disable-all="selectedSCMProductForAdd != null && selectedSCMProductForAdd.details != null && selectedSCMProductForAdd.details.length > 0">
                        <div class="row divInnerRow">
                            <div class="col-xs-2">
                                <label>Category</label>
                            </div>
                            <div class="col-xs-10">
                                <select ui-select2 class="form-control" style="width: 100% !important"
                                        data-ng-model="selectedSCMCategoryId"
                                        data-placeholder="Search Category..."
                                        data-ng-change="selectedSCMProductId = null">
                                    <option value=""></option>
                                    <option data-ng-repeat="category in scmMetadata.categoryDefinitions"
                                            value="{{category.categoryId}}">{{category.categoryName}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-2">
                                <label>Display</label>
                            </div>
                            <div class="col-xs-10">
                                <input type="text" data-ng-model="selectedSCMProductForAdd.display"
                                       class="form-control"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-2">
                                <label>Is Critical</label>
                            </div>
                            <div class="col-xs-10">
                                <input type="checkbox" data-ng-model="selectedSCMProductForAdd.critical"
                                       class="center-block" style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-2">
                                <label>Customizable</label>
                            </div>
                            <div class="col-xs-10">
                                <input type="checkbox" data-ng-model="selectedSCMProductForAdd.customize"
                                       class="center-block" style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-2">
                                <label>Product</label>
                            </div>
                            <div class="col-xs-8">
                                <select ui-select2 class="form-control" style="width: 100% !important"
                                        data-ng-model="selectedSCMProduct"
                                        data-placeholder="Search Product...">
                                    <option value=""></option>
                                    <option data-ng-repeat="product in scmProductsInfo | filter: filterByCategoryId"
                                            value="{{product}}">{{product.productName}}  ({{product.category.code}})
                                    </option>
                                </select>
                            </div>
                            <div class="col-xs-2">
                                <button class="btn btn-primary pull-left"
                                        data-ng-click="addSelectedSCMProduct(selectedSCMProduct)">
                                    <i class="fa fa-plus"></i> Add
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row">
                            <table class="table table-striped table-bordered">
                                <tr>
                                    <th>Default</th>
                                    <th>Product Name</th>
                                    <th>Tag</th>
                                    <th>Instruction</th>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Yield in %</th>
                                    <th>Actions</th>
                                </tr>
                                <tbody>
                                <tr data-ng-repeat="aliasDetail in selectedSCMProductForAdd.details">
                                    <td>
                                        <input type="checkbox"
                                               data-ng-click="markDefault(selectedSCMProductForAdd.details, $index)"
                                               data-ng-model="aliasDetail.defaultSetting" class="center-block"
                                               style="height: 2em; width: 2em;"/>
                                    </td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.product.name"/></td>
                                    <td style="width: 20%"><select class="form-control"
                                                                   ng-options="tag for tag in tagList"
                                                                   data-ng-model="aliasDetail.tag"></select></td>
                                    <td style="width:20%;"><select
                                            ng-options="instruction as instruction.name for instruction in instructionList track by instruction.name"
                                            class="form-control"
                                            data-ng-model="aliasDetail.instruction"/></td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.desc"/></td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.quantity"/></td>
                                    <td><input class="form-control" data-ng-model="aliasDetail.yield"/></td>
                                    <td align="left">
                                        <span data-ng-click="selectedSCMProductForAdd.details.splice($index,1);"
                                              style="cursor: pointer" title="Remove Variant ALias">
                                            <i class="fa fa-remove" style="font-size: 25px; color: red"></i>
                                        </span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <button class="btn btn-primary center-block"
                                        data-ng-click="validateVariantSCMProduct()">Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Add Other SCM Products Modal -->
<div class="modal fade" id="addOtherSCMProductModal" role="dialog" aria-labelledby="addOtherSCMProductModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addOtherSCMProductModalLabel">Select SCM Product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Product</label>
                            </div>
                            <div class="col-xs-8" data-ng-if="isAdd">
                                <select ui-select2 class="form-control" style="width: 100% !important"
                                        data-ng-model="otherSCMProduct"
                                        data-ng-change="setSelectedOtherProduct(otherSCMProduct)">
                                    <option data-ng-repeat="product in scmProductsInfo" value="{{product}}">
                                        {{product.productName}}  ({{product.category.code}})
                                    </option>
                                </select>
                            </div>
                            <div class="col-xs-8" data-ng-if="!isAdd">
                                <label class="form-control">{{selectedOtherSCMProduct.productName}}</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Unit Of Measure</label>
                            </div>
                            <div class="col-xs-8">
                                <label class="form-control">{{selectedOtherSCMProduct.unitOfMeasure}}</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Quantity</label>
                            </div>
                            <div class="col-xs-8">
                                <input class="form-control" data-ng-model="selectedOtherProductQuantity"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Yield</label>
                            </div>
                            <div class="col-xs-8">
                                <input class="form-control" data-ng-model="selectedOtherProductYield"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Is Critical</label>
                            </div>
                            <div class="col-xs-8">
                                <input type="checkbox" data-ng-model="selectedOtherProductCritical" class="center-block"
                                       style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Display</label>
                            </div>
                            <div class="col-xs-8">
                                <input type="checkbox" data-ng-model="selectedOtherProductDisplay" class="center-block"
                                       style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                        <div data-ng-show="selectedOtherProductDisplay" class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Display Short Code: </label>
                            </div>
                            <div  class="col-xs-8">
                                <input type="text" data-ng-model="selectedOtherProductDisplayShortCode" class="form-control"
                                       data-ng-change="setSelOtherProductDisplayShortCode(selectedOtherProductDisplayShortCode)"  required/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <button class="btn btn-primary center-block"
                                data-ng-click="addOtherSCMProduct(selectedOtherSCMProduct, selectedOtherProductQuantity, selectedOtherProductCritical, selectedOtherProductYield,selectedOtherProductDisplay,selectedOtherProductDisplayShortCode)">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Clone Recipe Modal -->
<div class="modal fade" id="cloneRecipeModal" role="dialog" aria-labelledby="cloneRecipeModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="cloneRecipeModalLabel">Select Clone Product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div data-ng-if="!cloneRecipeProfile">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Product</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2 class="form-control" style="width: 100% !important"
                                            data-ng-model="selectedCloneProductId"
                                            data-ng-change="onChangeSelectedCloneProduct(selectedCloneProductId);"
                                            data-placeholder="Search Product...">
                                        <option value=""></option>
                                        <option data-ng-repeat="product in productsInfo" value="{{product.id}}">
                                            {{product.name}} - {{product.status}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    <select ui-select2 style="width: 100% !important" class="form-control"
                                            data-ng-model="selectedCloneProductDimensionId"
                                            data-ng-change="selectCloneProductDimension(selectedCloneProductDimensionId)"
                                            data-placeholder="Select Dimension">
                                        <option data-ng-repeat="dimension in selectedCloneProductDimensionProfile.content"
                                                value="{{dimension.id}}">
                                            {{dimension.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-ng-if="cloneRecipeProfile">
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Product</label>
                                </div>
                                <div class="col-xs-8">
                                    {{cloneRecipes[0].product.name}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dimension</label>
                                </div>
                                <div class="col-xs-8">
                                    {{cloneRecipes[0].dimension.name}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label> Current Profile</label>
                                </div>
                                <div class="col-xs-8">
                                    {{cloneRecipes[0].profile}}
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Profile</label>
                                </div>
                                <div class="col-xs-8">
                                    <select class="form-control" data-ng-model="selectedProfileName"
                                            data-ng-change="selectCloneProductProfile(selectedProfileName)">
                                        <option data-ng-repeat="profile in recipeProfileAvailable"
                                                value="{{profile.name}}">
                                            {{profile.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12" data-ng-if="cloneRecipes != null && cloneRecipes.length > 0">
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Recipe Id</th>
                                        <th>Recipe Name</th>
                                        <th>Profile</th>
                                        <th>Actions</th>
                                    </tr>
                                    <tbody>
                                    <tr data-ng-repeat="recipe in cloneRecipes track by recipe.recipeId">
                                        <td>{{recipe.recipeId}}</td>
                                        <td>{{recipe.name}}</td>
                                        <td>{{recipe.profile}}</td>
                                        <td align="left">
                                            <span data-ng-click="selectRecipeForClone(recipe)" style="cursor: pointer"
                                                  title="Clone">
                                                <i class="fa fa-copy" style="font-size: 24px; margin-right: 5px"></i>
                                            </span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Add Menu Products Modal -->
<div class="modal fade" id="addMenuProductModal" role="dialog" aria-labelledby="addMenuProductModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addMenuProductModalLabel">
                    Select Menu Product
                </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Product</label>
                            </div>
                            <div class="col-xs-8">
                                <select ui-select2 style="width: 100% !important" data-ng-model="selectedMenuProductId"
                                        data-ng-change="onChangeSelectedMenuProduct(selectedMenuProductId);"
                                        data-placeholder="Search Product..." class="form-control">
                                    <option value=""></option>
                                    <option data-ng-repeat="product in productsInfo |filter:menuProductfilter"
                                            value="{{product.id}}">
                                        {{product.name}} - {{product.status}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Dimension</label>
                            </div>
                            <div class="col-xs-8">
                                <select ui-select2 style="width: 100% !important"
                                        data-ng-model="selectedMenuProductDimensionId"
                                        data-ng-change="selectMenuProductDimension(selectedMenuProductDimensionId)"
                                        data-placeholder="Select Dimension" class="form-control">
                                    <option data-ng-repeat="dimension in selectedMenuProductDimensionProfile.content"
                                            value="{{dimension.id}}">
                                        {{dimension.name}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Quantity</label>
                            </div>
                            <div class="col-xs-8">
                                <input class="form-control" data-ng-model="selectedMenuProductQuantity"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <button class="btn btn-primary center-block"
                                data-ng-click="addMenuProduct(selectedMenuProductQuantity)">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Add Add-on Products Modal -->
<div class="modal fade" id="addAddonsProductModal" role="dialog" aria-labelledby="addAddonsProductModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addAddonsProductModalh">Select SCM Product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Product</label>
                            </div>
                            <div class="col-xs-8" data-ng-if="isAdd">
                                <select ui-select2 class="form-control" style="width: 100% !important"
                                        data-ng-model="addonProduct"
                                        data-ng-change="setSelectedAddonProduct(addonProduct)">
                                    <option data-ng-repeat="product in scmProductsInfo" value="{{product}}">
                                        {{product.productName}}  ({{product.category.code}})
                                    </option>
                                </select>
                            </div>
                            <div class="col-xs-8" data-ng-if="!isAdd">
                                <label class="form-control">{{selectedAddonProduct.productName}}</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Unit Of Measure</label>
                            </div>
                            <div class="col-xs-8">
                                <label class="form-control">{{selectedAddonProduct.unitOfMeasure}}</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Quantity</label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        class="form-control"
                                        data-ng-model="selectedAddonQuantity"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Yield in %</label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        class="form-control"
                                        data-ng-model="selectedAddonYield"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Customizable *<span style="color: red;">Only If You Wish To Show It On Web</span></label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        type="checkbox"
                                        data-ng-model="selectedAddonCustomize"
                                        class="center-block"
                                        style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Display</label>
                            </div>
                            <div class="col-xs-8">
                                <input type="checkbox" data-ng-model="selectedAddonDisplay" class="center-block"
                                       style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                        <div data-ng-show="selectedAddonDisplay" class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Enter Display Short Code :</label>
                            </div>
                            <div  class="col-xs-8">
                                <input type="text" data-ng-model="selectedAddonDisplayShortCode" class="form-control"
                                       data-ng-change="setSelAddonDisplayShortCode(selectedAddonDisplayShortCode)"  />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <button
                                class="btn btn-primary center-block"
                                data-ng-click="addAddonProduct(activeTab,selectedAddonProduct, selectedAddonQuantity, selectedAddonYield, selectedAddonCustomize, selectedAddonDisplay, selectedAddonDisplayShortCode)">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- -- Add Add-on Products Modal -->
<div
        class="modal fade"
        id="addAddonsMenuProductModal"
        role="dialog"
        aria-labelledby="addAddonsMenuProductModal">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="addAddonsMenuProductModal1">Select {{mandatoryOroptional}} Addon Product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Product</label>
                            </div>
                            <div
                                    class="col-xs-8"
                                    data-ng-if="isAdd">
                                <select
                                        ui-select2
                                        name="selectedAddonMenuProductSelect"
                                        id="selectedAddonMenuProductSelectId"
                                        class="form-control"
                                        style="width: 100% !important"
                                        data-ng-model="selectedAddonMenuProduct"
                                        data-ng-change="setSelectedAddonMenuProduct(selectedAddonMenuProduct)">
                                    <option
                                            data-ng-repeat="product in productsInfo"
                                            value="{{product}}">{{product.name}} - {{product.status}}
                                    </option>
                                </select>
                            </div>
                            <div
                                    class="col-xs-8"
                                    data-ng-if="!isAdd">
                                <label class="form-control">{{selectedAddonMenuProduct.name}}</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div ng-show="recipeDetail.dispensed">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dispensed</label>
                                </div>
                                <div class="col-xs-8">
                                    <input type="checkbox" data-ng-model="addonDispensedFlag"
                                           class="center-block" style="height: 2em; width: 2em;"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div ng-show="recipeDetail.dispensed && addonDispensedFlag">
                            <div class="row divInnerRow">
                                <div class="col-xs-4">
                                    <label>Dispense Tag</label>
                                </div>
                                <div class="col-xs-8">
                                    <select class="form-control" style="width: 100% !important"
                                            data-ng-model="selectedAddonDispenseTag"
                                            ng-options="tags for tags in dispenseTagList.dispenseTags">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Quantity</label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        class="form-control"
                                        data-ng-model="selectedAddonMenuProductQuantity"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Tag</label>
                            </div>
                            <div class="col-xs-8">
                                <select class="form-control" ng-options="tag for tag in tagList"
                                        data-ng-model="addOnTag"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Yield in %</label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        class="form-control"
                                        data-ng-model="selectedAddonMenuProductYield"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Customizable *<span style="color: red;">Only If You Wish To Show It On Web</span></label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        type="checkbox"
                                        data-ng-model="selectedAddonMenuCustomize"
                                        class="center-block"
                                        style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <button
                                class="btn btn-primary center-block"
                                data-ng-click="addAddonMenuProduct(activeTab,selectedAddonMenuProduct,addonDispensedFlag,selectedAddonDispenseTag, selectedAddonMenuProductQuantity, selectedAddonMenuProductYield,
                                selectedAddonMenuCustomize,addOnTag)">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- option products -->
<div
        class="modal fade"
        id="addOptionProductModal"
        role="dialog"
        aria-labelledby="addOptionProductModal">
    <div1
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Select Options</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Option Type</label>
                            </div>
                            <div class="col-xs-8">
                                <select

                                        class="form-control"
                                        style="width: 100% !important"
                                        data-ng-model="selectedOptionType"
                                        data-ng-change="changeSelectedOptionType(selectedOptionType)"
                                        data-ng-options="optionType as optionType.detail.code for optionType in optionsList">
                                </select>
                            </div>
                        </div>
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Option</label>
                            </div>
                            <div class="col-xs-8">
                                <select

                                        class="form-control"
                                        style="width: 100% !important"
                                        data-ng-model="selectedOption"
                                        data-ng-change="changeSelectedOption(selectedOption)"
                                        data-ng-options="option as option.name for option in selectedOptionType.content">
                                </select>
                            </div>
                        </div>
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <button
                                        class="btn btn-primary center-block"
                                        data-ng-click="addOptionMenuProduct(activeTab,selectedOptionType,selectedOption)">
                                    Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div1>
</div>

<!--Recommendations-->
<div
        class="modal fade"
        id="recomendationsModal"
        role="dialog"
        aria-labelledby="recomendationsModal">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Select product</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Product</label>
                            </div>
                            <div ng-show="isRecommendation">
                                <div class="col-xs-8">
                                    <select
                                            ui-select2
                                            id="selectedRecommendationProductId"
                                            class="form-control"
                                            style="width: 100% !important"
                                            data-ng-model="selectedRecommendationProduct"
                                            data-ng-change="onChangeProduct(selectedRecommendationProduct)"
                                            data-ng-options="recommendations as recommendations.name+' - '+recommendations.status for recommendations in productsInfo | filter : {classification:'MENU'}">
                                    </select>
                                </div>
                            </div>
                            <div ng-show="!isRecommendation">
                                <div class="col-xs-8">
                                    <label class="form-control">{{selectedRecommendationProduct.name}}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Dimension</label>
                            </div>
                            <div ng-show="isRecommendation">
                                <div class="col-xs-8">
                                    <select
                                            id="selectedDimensionId"
                                            class="form-control"
                                            style="width: 100% !important"
                                            data-ng-model="selectedDimension"
                                            data-ng-options=" dimension.name for dimension in getRecommendationProductDimension.content">
                                    </select>
                                </div>
                            </div>
                            <div ng-show="!isRecommendation">
                                <div class="col-xs-8">
                                    <label class="form-control">{{selectedDimension.name}}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Quantity</label>
                            </div>
                            <div class="col-xs-8">
                                <input ng-disabled="true"
                                       class="form-control"
                                       data-ng-model="selectedRecommendationQuantity"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Yield in %</label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        class="form-control"
                                        data-ng-model="selectedRecommendationYield"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Customizable *<span
                                        style="color: red;">Only If You Wish To Show It On Web</span></label>
                            </div>
                            <div class="col-xs-8">
                                <input
                                        type="checkbox"
                                        data-ng-model="selectedRecommendationCustomize"
                                        class="center-block"
                                        style="height: 2em; width: 2em;"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <button
                                class="btn btn-primary center-block"
                                data-ng-click="addRecommendation(activeTab,selectedRecommendationProduct, selectedRecommendationQuantity, selectedRecommendationYield,
                                selectedRecommendationCustomize)">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Option Paid Addons -->
<div
        class="modal fade"
        id="addOptionAddonProductModal"
        role="dialog"
        aria-labelledby="addOptionAddonProductModal">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Select Addon Options</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Addon Product</label>
                            </div>
                            <div class="col-xs-8">
                                <select
                                        ui-select2
                                        class="form-control"
                                        style="width: 100% !important"
                                        data-ng-model="selectedAddonOptionType"
                                        data-ng-change="changeSelectedOptionType(selectedAddonOptionType)"
                                        data-ng-options="addon as addon.name+' - '+addon.status for addon in productsInfo | filter : {subType:1201}">
                                </select>
                            </div>
                        </div>
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <button
                                        class="btn btn-primary center-block"
                                        data-ng-click="addOptionAddonProduct(activeTab,selectedAddonOptionType)">
                                    Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Option Paid Addons -->
<div class="modal fade" id="editActiveRecipeModal" role="dialog" aria-labelledby="editActiveRecipeModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Select Start Date</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row divInnerRow">
                            <div class="col-xs-4">
                                <label>Effective Date</label>
                            </div>
                            <div class="col-xs-8">
                                <div class="datepicker" data-date-format="yyyy-MM-dd"
                                     data-date-min-limit="{{tomorrow}}">
                                    <input class="form-control" data-ng-model="startDate" type="text"
                                           placeholder="yyyy-MM-dd" required/>
                                </div>
                            </div>
                        </div>
                        <div class="row divInnerRow">
                            <div class="col-xs-12">
                                <button class="btn btn-primary center-block" data-ng-click="addInProgressRecipe()">
                                    Submit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div
        class="modal fade"
        id="dispenserModal"
        role="dialog"
        aria-labelledby="dispenserModal">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        ng-click="clearDispenserDetail()"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Dispenser detail</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="alert alert-info"> Please click on add after inserting or changing value.
                    </div>
                    <ul class="nav nav-tabs">
                        <li class="active" data-toggle="tab" data-ng-click="changeOption(1)"><a>Variants</a></li>
                        <li data-toggle="tab" data-ng-click=" changeOption(2)"><a>Addons</a></li>
                        <li data-toggle="tab" data-ng-click=" changeOption(3)"><a>Mandatory Addons</a></li>
                    </ul>
                    <div class="tab-content">
                        <div ng-if="selectedVariantTab">
                            <div class="col-xs-12" style="margin-top: 20px;">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <th>Variant</th>
                                    <th>Detail</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="variant in variantDispenserDetail">
                                        <td style="width: 25%">{{variant.product.name}}</td>
                                        <td>
                                            <div style="text-align: center">
                                                <span><b>{{variant.dispenseTag}}</b></span>
                                            </div>
                                            <br>
                                            <div ng-repeat="detail in variant.aliasAndRevolution">
                                                <div class="row">
                                                    <div class="col-xs-2">
                                                        <span>{{detail.alias.alias}}</span>
                                                    </div>
                                                    <div class="col-xs-2">
                                                        <input type="number" size="50" placeholder="enter revolution"
                                                               data-ng-change="saveRevolutionDetails(detail.alias,variant.dispenseTag,detail.revolution,'variants')"
                                                               ng-model="detail.revolution"/>
                                                    </div>
                                                    <div class="col-xs-offset-9">
                                                        <!--<button ng-click="saveRevolutionDetails(detail.alias,variant.dispenseTag,detail.revolution,'variants')">-->
                                                        <!--Add-->
                                                        <!--</button>-->
                                                        <span ng-if="detail.revolution>=0"
                                                              data-ng-click="removeDispenserDetail(detail.alias.alias,'variants');variant.aliasAndRevolution.splice($index,1);"
                                                              style="cursor: pointer"
                                                              title="Remove ">
   								                            	<i class="fa fa-remove"
                                                                   style="font-size: 25px; color: red"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <br>
                                            </div>

                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div ng-if="selectedAddonTab">
                            <div class="col-xs-12" style="margin-top: 20px;">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <th>Addon</th>
                                    <th>Detail</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="addon in addonDispenserDetail">
                                        <td>{{addon.product.name}}</td>
                                        <td>
                                            <div style="text-align: center">
                                                <span><b>{{addon.dispenseTag}}</b></span>
                                            </div>
                                            <br>
                                            <div class="col-xs-2">
                                                <input type="number" placeholder="enter revolution"
                                                       data-ng-change="saveRevolutionDetails(addon.product,addon.dispenseTag,addon.revolution,'addOns')"
                                                       ng-model="addon.revolution"/>
                                            </div>
                                            <div class="col-xs-offset-7">
                                                <!--<button ng-click="saveRevolutionDetails(addon.product,addon.dispenseTag,addon.revolution,'addOns')">-->
                                                <!--Add-->
                                                <!--</button>-->

                                                <span ng-if="addon.revolution>=0"
                                                      data-ng-click="removeDispenserDetail(addon.product.name,'addOns');addonDispenserDetail.splice($index,1);"
                                                      style="cursor: pointer"
                                                      title="Remove ">
   									                 <i class="fa fa-remove" style="font-size: 25px; color: red"></i>
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div ng-if="selectedMandatory">
                            <div class="col-xs-12" style="margin-top: 20px;">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <th>Addon</th>
                                    <th>Detail</th>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="addon in mandatoryAddonDispenserDetail">
                                        <td>{{addon.product.name}}</td>
                                        <td>
                                            <div style="text-align: center">
                                                <span><b>{{addon.dispenseTag}}</b></span>
                                            </div>
                                            <br>
                                            <div class="col-xs-2">
                                                <input type="number" placeholder="enter revolution"
                                                       data-ng-change="saveRevolutionDetails(addon.product,addon.dispenseTag,addon.revolution,'mandatoryAddons')"
                                                       ng-model="addon.revolution"/>
                                            </div>
                                            <div class="col-xs-offset-7">
                                                <!--<button ng-click="saveRevolutionDetails(addon.product,addon.dispenseTag,addon.revolution,'mandatoryAddons')">-->
                                                <!--Add-->
                                                <!--</button>-->

                                                <span ng-if="addon.revolution>=0"
                                                      data-ng-click="removeDispenserDetail(addon.product.name,'mandatoryAddons');mandatoryAddonDispenserDetail.splice($index,1);"
                                                      style="cursor: pointer"
                                                      title="Remove ">
   									               <i class="fa fa-remove" style="font-size: 25px; color: red"></i>
   								                 </span>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!--<div class="row">-->
                <div style="text-align: center">
                    <div class="btn">
                        <button class="btn btn-primary" ng-click="submitDispenser()">Submit</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script
        type="text/ng-template"
        id="addonDirectiveTemplate.html">
    <div id="addonDiv" class="row tabDiv">
        <div class="col-xs-12">
            <div class="row divInnerRow">
                <div class="col-xs-12">
                    <button class="btn btn-primary pull-left"
                            ng-click="openAddonProduct()"><i class="fa fa-plus"></i> Add New Product
                    </button>
                </div>
                <div class="col-xs-12" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Product Name</th>
                        <th>Unit Of Measure</th>
                        <th>Quantity</th>
                        <th>Yield</th>
                        <th>Actions</th>
                        </thead>
                        <tbody>
                        <tr
                                ng-repeat="variant in addonList track by $index">
                            <td>{{variant.product.name}}</td>
                            <td>{{variant.uom}}</td>
                            <td>{{variant.quantity}}</td>
                            <td>{{variant.yield}}</td>
                            <td align="left">
   								<span data-ng-click="editAddonProduct({variant : variant})"
                                      style="cursor: pointer"
                                      title="Add/Edit Details">
   									<i class="fa fa-edit" style="font-size: 25px;"></i>
   								</span>&nbsp;&nbsp;
                                <span data-ng-click="addonList.splice($index, 1);"
                                      style="cursor: pointer"
                                      title="Remove Variant">
   									<i class="fa fa-remove" style="font-size: 25px; color: red"></i>
   								</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</script>
<script
        type="text/ng-template"
        id="addonMenuProductDirectiveTemplate.html">
    <div id="menuProductDiv" class="row tabDiv">
        <div class="col-xs-12">
            <div class="row divInnerRow">
                <div class="col-xs-12" data-ng-show="!cloneRecipeProfile">
                    <button class="btn btn-primary pull-left"
                            data-ng-click="openAddonMenuProduct({type:'OPTIONAL'})"><i class="fa fa-plus"></i> Add New
                        Product
                    </button>
                    <button class="btn btn-primary pull-left" style="margin-left: 10px;"
                            data-ng-click="openAddonMenuProduct({type:'MANDATORY'})"><i class="fa fa-plus"></i> Add New
                        Mandatory Product
                    </button>
                </div>
                <div class="col-xs-12" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Product Name</th>
                        <th>Quantity</th>
                        <th>Tag</th>
                        <th>Yield</th>
                        <th>Customizable</th>
                        <th>Actions</th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="variant in addonList track by $index">
                            <td>{{variant.product.name}}</td>
                            <td>{{variant.quantity}}</td>
                            <td>{{variant.tag}}</td>
                            <td>{{variant.yield}}</td>
                            <td>{{variant.customize}}</td>
                            <td align="left">
							<span >
   								<span data-ng-click="editAddonMenuProduct({variant : variant,type:'OPTIONAL'})"
                                      style="cursor: pointer"
                                      title="Add/Edit Details">
   									<i class="fa fa-edit" style="font-size: 25px;"></i>
   								</span>&nbsp;&nbsp;
   								<span data-ng-click="addonList.splice($index, 1);"
                                      style="cursor: pointer"
                                      title="Remove Variant">
   									<i class="fa fa-remove" style="font-size: 25px; color: red"></i>
   								</span>
							</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-xs-12" style="margin-top: 20px;">
                    <span>Mandatory Addon</span>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Product Name</th>
                        <th>Quantity</th>
                        <th>Tag</th>
                        <th>Yield</th>
                        <th>Customizable</th>
                        <th>Actions</th>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="variant in  mandatoryAddonList track by $index">
                            <td>{{variant.product.name}}</td>
                            <td>{{variant.quantity}}</td>
                            <td>{{variant.tag}}</td>
                            <td>{{variant.yield}}</td>
                            <td>{{variant.customize}}</td>
                            <td align="left">
							<span data-ng-show="!cloneRecipeProfile">
   								<span data-ng-click="editAddonMenuProduct({variant : variant,type:'MANDATORY'})"
                                      style="cursor: pointer"
                                      title="Add/Edit Details">
   									<i class="fa fa-edit" style="font-size: 25px;"></i>
   								</span>&nbsp;&nbsp;
   								<span data-ng-click="mandatoryAddonList.splice($index, 1);"
                                      style="cursor: pointer"
                                      title="Remove Variant">
   									<i class="fa fa-remove" style="font-size: 25px; color: red"></i>
   								</span>
							</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/ng-template"
        id="optionProductDirectiveTemplate.html">
    <div id="optionProductDiv" class="row tabDiv">
        <div class="col-xs-12">
            <div class="row divInnerRow">
                <div class="col-xs-12" data-ng-show="!cloneRecipeProfile">
                    <button class="btn btn-primary pull-left"
                            ng-click="openOptionProduct()"><i class="fa fa-plus"></i> Add New Option
                    </button>
                    <button class="btn btn-primary pull-left" style="margin-left: 10px;"
                            ng-click="openOptionAddonProductModal()"><i class="fa fa-plus"></i> Add New Addon
                        Product
                    </button>
                </div>
                <div class="col-xs-12" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Option Name</th>
                        <th>Type</th>
                        <th>Code</th>
                        <th>Actions</th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="variant in optionList track by $index">
                            <td>{{variant.name}}</td>
                            <td>{{variant.type}}</td>
                            <td>{{variant.code}}</td>
                            <td align="left">
   								<span data-ng-click="optionList.splice($index, 1);"
                                      style="cursor: pointer"
                                      title="Remove Variant">
   									<i class="fa fa-remove" style="font-size: 25px; color: red"></i>
   								</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</script>

<!--script for recommendation-->
<script
        type="text/ng-template"
        id="recommendationDirectiveTemplate.html">
    <div id="menuRecommendationDiv" class="row tabDiv">
        <div class="col-xs-12">
            <div class="row divInnerRow">
                <div class="col-xs-12" data-ng-show="!cloneRecipeProfile">
                    <button class="btn btn-primary pull-left"
                            ng-click="openRecommendation()"><i class="fa fa-plus"></i> Add New Product
                    </button>
                </div>
                <div class="col-xs-12" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Product Name</th>
                        <th>Quantity</th>
                        <th>Yield</th>
                        <th>Customizable</th>
                        <th>Actions</th>
                        </thead>
                        <tbody dnd-list="recommendationList">
                        <tr ng-repeat="variant in recommendationList " draggable="true"
                            dnd-effect-allowed="copyMove"
                            dnd-moved="recommendationList.splice($index, 1)"

                            dnd-draggable="variant">
                            <td>{{variant.product.name}}</td>
                            <td>{{variant.quantity}}</td>
                            <td>{{variant.yield}}</td>
                            <td>{{variant.customize}}</td>
                            <td align="left">
							<span data-ng-show="!cloneRecipeProfile">
   								<span data-ng-click="editRecommendation({variant : variant})"
                                      style="cursor: pointer"
                                      title="Add/Edit Details">
   									<i class="fa fa-edit" style="font-size: 25px;"></i>
   								</span>&nbsp;&nbsp;
   								<span data-ng-click="recommendationList.splice($index, 1);"
                                      style="cursor: pointer"
                                      title="Remove Variant">
   									<i class="fa fa-remove" style="font-size: 25px; color: red"></i>
   								</span>
							</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</script>



<script
        type="text/ng-template"
        id="condimentDirectiveTemplate.html">
    <div id="condimentDiv" class="row tabDiv">
        <div class="col-xs-12">
            <div class="row divInnerRow">
                <div class="col-xs-12" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <th>Source </th>
                        <th>Initial Condiment Group</th>
                        <th>Update/Set Condiment Group</th>
                        <th>Quantity</th>
                        </thead>
                        <tbody>
                        <tr>
                            <td>CAFE</td>
                            <td data-ng-if="dineCondiment!=null">{{dineCondiment.groupName}}</td>
                            <td>
                                <div class="col-xs-8">
                                    <select ui-select2 style="width: 100% !important" data-ng-model="dineCondiment"
                                            data-ng-change="onChangeSelectedCondimentSource(dineCondiment,'CAFE');"
                                            class="form-control">
                                        <option value=""></option>
                                        <option data-ng-repeat="condiment in condimentCafeList"
                                                value="{{condiment}}">{{condiment.groupName}}
                                        </option>
                                    </select>
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control" required data-ng-model="dineQuantity"
                                       data-ng-change="changeDineCondimentQuantity(dineQuantity,'CAFE');"/>
                            </td>
                        </tr>
                        <tr>
                            <td>COD</td>
                            <td data-ng-if="codCondiment!=null">{{codCondiment.groupName}}</td>
                            <td>
                                <div class="col-xs-8">
                                    <select ui-select2 style="width: 100% !important" data-ng-model="codCondiment"
                                            data-ng-change="onChangeSelectedCondimentSource(codCondiment,'COD');"
                                            class="form-control">
                                        <option value=""></option>
                                        <option data-ng-repeat="condiment in condimentCODList"
                                                value="{{condiment}}">{{condiment.groupName}}
                                        </option>
                                    </select>
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control" required data-ng-model="codQuantity"
                                       data-ng-change="changeCODCondimentQuantity(codQuantity,'COD');"/>
                            </td>
                        </tr>
                        <tr>
                            <td>TAKE AWAY</td>
                            <td data-ng-if="takeawayCondiment!=null">{{takeawayCondiment.groupName}}</td>
                            <td>
                                <div class="col-xs-8">
                                    <select ui-select2 style="width: 100% !important" data-ng-model="takeawayCondiment"
                                            data-ng-change="onChangeSelectedCondimentSource(takeawayCondiment,'TAKE_AWAY');"
                                            class="form-control">
                                        <option value=""></option>
                                        <option data-ng-repeat="condiment in condimentTakeAwayList"
                                                value="{{condiment}}">{{condiment.groupName}}
                                        </option>
                                    </select>
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control" required ng-model="takeawayQuantity"
                                       data-ng-change="changeTAKECondimentQuantity(takeawayQuantity,'TAKE_AWAY');"/>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</script>
