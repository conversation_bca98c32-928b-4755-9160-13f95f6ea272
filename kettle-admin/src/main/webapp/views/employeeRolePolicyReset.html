<!--
  ~ Created By Shanmukh
  -->
<div
        class="row"
        ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Employee Role Policy Reset
        </h1>
    </div>
</div>
<div class="row">
<br>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Select Department : </label>
                </div>
                <div class="col-xs-8 form-group">
                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                            data-ng-model="selectedDepartment" data-placeholder="Select a Department"
                            data-ng-change="setSelectedDepartment(selectedDepartment)">
                        <option value=""></option>
                        <option data-ng-repeat="dept in departmentList" value="{{dept}}">
                            {{dept.name + " - " + dept.id}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Select Designation : </label>
                </div>
                <div class="col-xs-8 form-group">
                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                            data-ng-model="selectedDesignation" data-placeholder="Select a Designation"
                            data-ng-change="setSelectedDesignation(selectedDesignation)">
                        <option value=""></option>
                        <option data-ng-repeat="desg in designationList" value="{{desg}}">
                            {{desg.name + " - " + desg.id}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedDepartment != null && selectedDesignation != null">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4">
                    <label class="control-label">Select Employee : </label>
                </div>
                <div class="col-xs-8 form-group">
                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                            id="selectedEmployee"
                            data-ng-model="selectedEmployee" data-placeholder="Select an Employee"
                            data-ng-change="setSelectedEmployee(selectedEmployee)">
                        <option value=""></option>
                        <option data-ng-repeat="emp in employeesList" value="{{emp}}">
                            {{emp.name + " - " + emp.id}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedDepartment != null && selectedDesignation != null && selectedEmployeeJson != null">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-4 center-block" data-ng-if="selectedEmployeeJson.userPolicy != null">
                    <button class="btn btn-primary center-block"
                            ng-click="openPolicyRoles(selectedEmployeeJson.userPolicy, 'VIEW')">
                        View User Policy
                    </button>
                </div>
                <div class="col-xs-4 center-block">
                    <button class="btn btn-primary center-block"
                            ng-click="resetPolicy()">
                        Set User Policy
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div
        class="modal previewModal fade"
        id="editPolicyRolesModal"
        role="dialog"
        aria-labelledby="editPolicyRolesModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="col-lg-8">
                    <h4 class="modal-title" id="editPolicyRolesModalLabel">{{currentAction}} Policy Roles -
                        <span data-ng-if="selectedPolicyJson.policyName != null">{{selectedPolicyJson.policyName}}</span>
                        <span data-ng-if="selectedEmployeeJson != null"> for {{selectedEmployeeJson.name}} ({{selectedEmployeeJson.id}})</span>
                    </h4>
                </div>
                <div class="col-lg-3">
                    <b><p class="modal-title">Current Policy -
                        <span data-ng-if="currentSelectedPolicy.policyName != null">{{currentSelectedPolicy.policyName}}</span>
                        <span data-ng-if="currentSelectedPolicy.policyName == null">NA</span>
                    </p>
                    </b>
                </div>
                <div class="col-lg-1 pull-right">
                    <button type="button"
                            class="close"
                            data-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
            <form name="myForm">
                <div class="modal-body" data-ng-if="currentViewType != 'ADD_POLICY'">
                    <div class="row" data-ng-if="currentAction != 'VIEW'">
                        <br>
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="col-xs-4">
                                    <label class="control-label">Select Policy : </label>
                                </div>
                                <div class="col-xs-8 form-group">
                                    <select data-ui-select2 class="form-control" style="width: 100% !important"
                                            id="selectedPolicy"
                                            data-ng-model="selectedPolicy" data-placeholder="Select a Policy"
                                            data-ng-change="setSelectedPolicy(selectedPolicy)">
                                        <option value=""></option>
                                        <option data-ng-repeat="policy in userPolicies" value="{{policy}}">
                                            {{policy.policyName + " ( " + policy.policyDescription + " )" }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=form-group">
                        <input type="text" ng-model="filterBySearch" placeholder="Filter Roles " class="form-control"/>
                    </div>
                    <div class="form-group">
                        <br>
                        <table class="table table-striped table-bordered" data-ng-if="finalRows.length > 0">
                            <thead>
                            <tr>
                                <th>Check</th>
                                <th>Role name</th>
                                <th>Role Description</th>
                                <th>Role Application</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-style="role.status == 'ACTIVE' ?
                                                {'background-color':'green' , 'color':'white'} : ''"
                                style="cursor: pointer;"
                                data-ng-disabled="true"
                                ng-repeat-start="role in finalRows | filter:filterBySearch">
                                <td data-ng-disabled="true">
                                    <input type="checkbox" data-ng-checked="role.status == 'ACTIVE'"
                                           data-ng-disabled="true"
                                           data-ng-click="changeRoleStatus(role)"
                                           ng-true-value="'ACTIVE'" ng-false-value="'IN_ACTIVE'"/>
                                </td>
                                <td>{{role.roleName}}</td>
                                <td>{{role.roleDescription}}</td>
                                <td>{{role.applicationName}}</td>
                                <td data-ng-show="role.roleActions != null && role.roleActions.length > 0">
                                    <button class="btn btn-medium yellowBg" style="color: black"
                                            data-ng-click="showActionsForRole(role)">
                                        {{role.expanded ? "Hide Actions" : "Show Actions"}}
                                    </button>
                                </td>
                                <td data-ng-show="role.roleActions == null || role.roleActions.length == 0">
                                    No Actions
                                </td>
                            </tr>
                            <tr data-ng-if="role.expanded && role.roleActions.length > 0 && currentSelectedRole == role.roleId"
                                ng-repeat-end>
                                <td colspan="10">
                                    <div
                                            class="row"
                                            id="gridViewId">
                                        <div
                                                class="col-lg-12">
                                            <div
                                                    id="categoryGrid"
                                                    data-ui-grid="categoryGridOptions"
                                                    ui-grid-save-state=""
                                                    ui-grid-resize-columns
                                                    ui-grid-move-columns
                                                    class="grid col-lg-12"
                                                    data-ng-if="role.expanded && role.roleActions.length > 0 && currentSelectedRole == role.roleId"
                                            ></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div data-ng-if="finalRows.length == 0" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">Please Select a Policy to Apply..!</div>
                    <div class="row" data-ng-if="currentAction == 'RESET' && selectedPolicyJson != null">
                        <div class="col-lg-3">
                            <input class="btn btn-primary" style="margin-top: 5px" type="file" file-model="fileToUpload" accept="">
                        </div>
                        <div class="col-lg-3">
                            <button class="btn btn-primary" style="margin-top: 5px"
                                    data-ng-click="uploadPolicyChangeProof()"
                                    data-ng-if="fileToUpload != null">Upload Policy Change Proof
                            </button>
                        </div>
                        <div class="col-lg-3 center-block" data-ng-if="uploadedDoc != null">
                            <button class="btn btn-primary pull-right" ng-click="resetEmployeeUserPolicy()">
                                Reset Policy
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
