<style type="text/css">
.table-striped>tbody>tr:nth-of-type(even) {
    background-color: #FFF;
}
</style>
<div data-ng-init="init()">
	<div class="row">
	<div class="col-xs-6" style="margin: 10px;text-align: right">
		<button data-ng-click="downloadAllRecipe()" >Download All Recipes <i class="fa fa-download"
					style="font-size: 24px; margin-right: 5px;"></i>
					</button>
	</div>
	<div class="col-xs-6" style="margin: 10px;text-align: right">
		<button data-ng-click="downloadAllRecipeInstruction()" >Download All Recipes Instruction<i class="fa fa-download"
					style="font-size: 24px; margin-right: 5px;"></i>
					</button>
	</div>
	<!-- <div class="col-xs-6" style="margin: 10px;text-align: right">
		<button data-ng-click="downloadAllRecipeCost()" >Download All Recipes Cost <i class="fa fa-download"
					style="font-size: 24px; margin-right: 5px;"></i>
					</button>
	</div><div class="col-xs-6" style="margin: 10px;text-align: right">
		<button data-ng-click="downloadAllRecipeDetailWithCost()" >Download All Recipes Details With Cost <i class="fa fa-download"
					style="font-size: 24px; margin-right: 5px;"></i>
					</button>
	</div> --></div>
	<div class="row" style="margin-bottom: 10px;">
		<div class="col-xs-4">
		<input type="text"  class="input-sm" data-ng-model="recipeName">
		<button class="btn  btn-info" style="margin-left: 5px;" data-ng-click="findRecipeContainsName(recipeName)"> Find By Name </button>
		</div>
		<div class="col-xs-4">
			<input type="text"  class="input-sm" data-ng-model="recipeId">
			<button class="btn  btn-info" style="margin-left: 5px;" data-ng-click="findRecipeByRecipeId(recipeId)"> Find By Recipe Id </button>
		</div>
		<div class="col-xs-4">
		<button class="btn btn-info" data-ng-click="findAllRecipe()"> Find All Recipes</button>
		</div>
	</div>
	<div class="row">
	<div class="col-xs-12">
		<table class="table table-striped table-bordered table-hover table-condensed table-responsive">
			<tr class="info">
				<th>Recipe Id</th>
				<th>Recipe Name</th>
				<th>Product Name</th>
				<th>Dimension</th>
				<th>Profile</th>
				<th>Start Date</th>
				<th>Action</th>
			</tr>
			<tbody>
			<tr data-ng-repeat="recipe in recipes track by recipe._id">
			<td>{{recipe.recipeId}}</td>
			<td>{{recipe.name}}</td>
			<td>{{recipe.product.name}}</td>
			<td>{{recipe.dimension.name}}</td>
			<td>{{recipe.profile}}</td>
			<td>{{recipe.startDate | date : 'yyyy-MM-dd'}}</td>
			<td align="left"><span
				data-ng-click="viewRecipe(recipe)" style="cursor: pointer"
				title="View Details"><i class="fa fa-files-o"
					style="font-size: 24px; margin-right: 5px"></i></span>
				<span
				data-ng-click="downloadRecipe(recipe)" style="cursor: pointer"
				title="Download Recipe"><i class="fa fa-download"
					style="font-size: 24px; margin-right: 5px;"></i></span>
				<span data-ng-if="recipe.status === 'IN_ACTIVE'"
				data-ng-click="reActivateRecipe(recipe)" style="cursor: pointer"
				title="Re Activate Recipe"><i class="fa fa-refresh"
					style="font-size: 24px; margin-right: 5px;"></i></span>
				<!--<span
				data-ng-click="removeRecipe(recipe)" style="cursor: pointer"
				title="Delete Recipe"><i class="fa fa-remove"
					style="font-size: 24px; margin-right: 5px; color: red"></i></span> -->
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	</div>
</div>
<div class="modal fade previewModal" id="showPreviewModal" 
	role="dialog" aria-labelledby="showPreviewModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="showPreviewModalLabel">{{recipeDetail.name + "(" + recipeDetail.profile + ")"}}
					Preview</h4>
			</div>
			<div class="modal-body">
				<div class="row tabDiv">
					<div class="col-xs-12">
						<div class="form-group">
							<label>Select Region</label>
							<select class="form-control" ng-model="selectedRegion" ng-options=" region for region in allRegions" data-ng-change="viewRecipe(recipeDetail)"> </select>
						</div>
					</div>
				<div class="col-xs-12" data-ng-if=" recipeCostDetail != null">
						<h3>Recipe Cost Details</h3>
						<div class="row divInnerRow"
							data-ng-if="recipeCostDetail.erroCodes != null && recipeCostDetail.erroCodes.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>Recipe Common Errors</h4>
								<div class="row"
									data-ng-repeat="error in recipeCostDetail.erroCodes track by $index">
									<div class="col-xs-12">
										<label>{{error}}</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row divInnerRow"
							data-ng-repeat="category in recipeCostDetail.categoryCost track by $index"
							data-ng-if="category.erroCodes != null && category.erroCodes.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>{{category.costType}} Errors</h4>
								<div class="row"
									data-ng-repeat="error in category.erroCodes track by $index">
									<div class="col-xs-12">
										<label>{{error}}</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row divInnerRow"
							data-ng-if="recipeCostDetail.categoryCost != null && recipeCostDetail.categoryCost.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>{{recipeCostDetail.recipeName}} Cost Summary</h4>
								<div class="row" data-ng-show="!onlySCM"
									data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
									<div class="col-xs-12">
										<label>{{category.costType}} - {{category.cost}}</label>
									</div>
								</div>
								<div class="row" data-ng-show="onlySCM && $index == 0"
									data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
									<div class="col-xs-12">
										<label>Total Cost - {{category.cost}}</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row divInnerRow"
							data-ng-if="recipeCostDetail.categoryCost != null && recipeCostDetail.categoryCost.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>Detailed Cost Calculation</h4>
								<div class="row" 
									data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
									<div class="col-xs-12" data-ng-show="!onlySCM || (onlySCM && $index == 0)">
										<div class="row">
											<div class="col-xs-12" data-ng-show="!onlySCM">
												<h5>{{recipeCostDetail.recipeName}} -
													{{category.costType}} Cost Details ({{category.cost}})</h5>
											</div>
											<div class="col-xs-12" data-ng-show="onlySCM">
												<h5>{{recipeCostDetail.recipeName}} -
													Cost Details ({{category.cost}})</h5>
											</div>
											<div class="col-xs-12">
												<table class="table table-striped table-bordered"
													style="margin-top: 10px;">
													<thead>
														<th>Component Type</th>
														<th>Type</th>
														<th>Product Id</th>
														<th>Product Name</th>
														<th>UoM</th>
														<th>Quantity</th>
														<th>Yield in %</th>
														<th>Price</th>
														<th>Cost</th>
													</thead>
													<tbody>
														<tr
															data-ng-repeat="ingredient in recipeCostDetail.commonIngredient track by $index"
															data-ng-style="(ingredient.calculatedFromNegotiatedPrice == 'Y') ? (ingredient.cost == 0 ? {'background':'red'} : {'background':'orange'}) : {}">
															<td>Common</td>
															<td>{{ingredient.type}}</td>
															<td>{{ingredient.productId}}</td>
															<td>{{ingredient.productName}}</td>
															<td>{{ingredient.uom}}</td>
															<td>{{ingredient.quantity}}</td>
															<td>{{ingredient.yield}}</td>
															<td>{{ingredient.price}}</td>
															<td>{{ingredient.cost}}</td>
														</tr>
														<tr
															data-ng-repeat="ingredient in category.ingredients track by $index">
															<td>{{category.costType}}</td>
															<td>{{ingredient.type}}</td>
															<td>{{ingredient.productId}}</td>
															<td>{{ingredient.productName}}</td>
															<td>{{ingredient.uom}}</td>
															<td>{{ingredient.quantity}}</td>
															<td>{{ingredient.yield}}</td>
															<td>{{ingredient.price}}</td>
															<td>{{ingredient.cost}}</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<h3>Recipe Constituent Details</h3>
						<div class="row divInnerRow">
							<div class="col-xs-12" style="margin-top: 10px;"
								data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.compositeProduct != null && recipeDetail.ingredient.compositeProduct.details!= null && recipeDetail.ingredient.compositeProduct.details.length > 0"
								data-ng-repeat="detail in recipeDetail.ingredient.compositeProduct.details track by $index">
								<div class="row">
									<div class="col-xs-12">
										<label>Options for {{detail.name}}</label>
									</div>
									<div class="col-xs-12">
										<table class="table table-striped table-bordered"
											style="margin-top: 10px;">
											<thead>
												<th>Product Name</th>
												<th>Dimension</th>
												<th>Quantity</th>
											</thead>
											<tbody>
												<tr
													data-ng-repeat="variant in detail.menuProducts track by $index">
													<td>{{variant.product.name}}</td>
													<td>{{variant.dimension.name}}</td>
													<td>{{variant.quantity}}</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>

							<div class="col-xs-12" style="margin-top: 10px;"
								data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.variants != null && recipeDetail.ingredient.variants.length > 0">
								<h4>Ingredient Variant Details</h4>
								<table class="table table-striped table-bordered">
									<thead>
										<th>Product Id</th>
										<th>Product Name</th>
										<th>Unit Of Measure</th>
										<th>Details</th>
									</thead>
									<tbody>
										<tr data-ng-repeat="variant in recipeDetail.ingredient.variants">
											<td>{{variant.product.productId}}</td>
											<td>{{variant.product.name}}</td>
											<td>{{variant.uom}}</td>
											<td>{{printVariantDetails(variant.details)}}</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="col-xs-12" style="margin-top: 10px;"
								data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.products != null && recipeDetail.ingredient.products.length > 0">
								<h4>Ingredient SCM Details</h4>
								<table class="table table-striped table-bordered">
									<thead>
										<th>Product Category</th>
										<th>Details</th>
									</thead>
									<tbody>
										<tr
											data-ng-repeat="variant in recipeDetail.ingredient.products track by $index">
											<td>{{variant.category.name}}</td>
											<td>{{printSCMProductDetails(variant.details)}}</td>
										</tr>
									</tbody>
								</table>
							</div>
							<product-view list-label="'Other Products'"
								ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.components != null && recipeDetail.ingredient.components.length > 0"
								product-list="recipeDetail.ingredient.components"></product-view>
							<product-view list-label="'Addon Products'"
								ng-if="recipeDetail.addons != null && recipeDetail.addons.length > 0"
								product-list="recipeDetail.addons"></product-view>
							<product-view list-label="'Dine In Cosumables'"
								ng-if="recipeDetail.dineInConsumables != null && recipeDetail.dineInConsumables.length > 0"
								product-list="recipeDetail.dineInConsumables"></product-view>
							<product-view list-label="'Delivery Consumables'"
								ng-if="recipeDetail.deliveryConsumables != null && recipeDetail.deliveryConsumables.length > 0"
								product-list="recipeDetail.deliveryConsumables"></product-view>
							<product-view list-label="'Take Away Consumbales'"
								ng-if="recipeDetail.takeawayConsumables != null && recipeDetail.takeawayConsumables.length > 0"
								product-list="recipeDetail.takeawayConsumables"></product-view>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/ng-template" id="scmProductViewDirective.html">
				<div class="col-xs-12" style="margin-top: 10px;">
					<h4>{{listLabel}}</h4>
					<table class="table table-striped table-bordered">
						<thead>
							<th>Product Name</th>
							<th>Unit Of Measure</th>
							<th>Quantity</th>
							<th>Yield in %</th>
						</thead>
						<tbody>
							<tr
								ng-repeat="variant in productList track by $index">
								<td>{{variant.product.name}}</td>
								<td>{{variant.uom}}</td>
								<td>{{variant.quantity}}</td>
								<td>{{variant.yield}}</td>
							</tr>
						</tbody>
					</table>
				</div>
</script>
