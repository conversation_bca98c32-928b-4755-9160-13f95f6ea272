<div class="row"
     ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Payment Information </h1>
    </div>
    <div class="col-xs-12">
        <table class="table table-striped table-bordered" id="tableDataStructure" style="margin-padding:0px;">
            <thead>
            <th>Mode Name&nbsp;</th>
            <th>Mode Type&nbsp;</th>
            <th>Mode Description&nbsp;</th>
            <th>Settlement Type&nbsp;</th>
            <th>Generate Pull&nbsp;</th>
            <th>Commission Rate&nbsp;</th>
            <th>Mode Status&nbsp;</th>
            <th>Mode Category&nbsp;</th>
            <th>Validation Source&nbsp;</th>
            <th>Ledger Name&nbsp;</th>

            <th colspan="2" align="center"></th>
            </thead>
            <tbody>
            <tr ng-repeat="payments in paymentModeList">
                <td>{{payments.name}}</td>
                <td>{{payments.type}}</td>
<!--                <td>{{payments.otherModeType}}</td>-->
                <td>{{payments.description}}</td>
                <td>{{payments.settlementType}}</td>
                <td>{{payments.generatePull ? 1 : 0}}</td>
                <td>{{payments.commissionRate}}</td>
                <td>{{payments.status}}</td>
                <td>{{payments.category}}</td>
                <td>{{payments.validationSource}}</td>
                <td>{{payments.ledgerName}}</td>
                <td>
                    <button
                            class="btn btn-primary"
                            ng-click="getPaymentMappingByIndex($index)">Update
                    </button>

                </td>
                <td>
                    <button
                        class="btn btn-primary"
                        ng-click="editPymtMapping(payments)">Update Mapping
                    </button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--modal 1  for update payment method-->

    <div
            class="modal fade"
            id="paymentMethodModal"
            tabindex="-1"
            role="dialog"
            aria-labelledby="myModalLabel">
        <div
                class="modal-dialog"
                role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button
                            type="button"
                            class="close"
                            data-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>

                    <h4
                            class="modal-title"
                            id="myModalLabel">{{action}} Update Payment Method</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Mode Name*</label>
                        <input type="text"
                               class="form-control"
                               ng-model="name"
                               required/>
                    </div>
<!--                    <div class="form-group">-->
<!--                        <label>Mode Type*</label><select-->
<!--                            class="form-control"-->
<!--                            ng-model="type"-->
<!--                            required>-->
<!--                        <option value="CARD">CARD</option>-->
<!--                        <option value="CASH">CASH</option>-->
<!--                        <option value="AMEX Card">AMEX Card</option>-->
<!--                        <option value="COUPON">COUPON</option>-->
<!--                        <option value="CREDIT">CREDIT</option>-->
<!--                        <option value="PREPAID">PREPAID</option>-->
<!--                        <option value="GYFTR">GYFTR</option>-->
<!--                        <option value="OTHERS">OTHERS</option>-->
<!--                    </select>-->
                        <div class="form-group">
                            <label>Mode Type*</label>
                            <select class="form-control"
                                    ng-model="type"
                                    required>
                                <option ng-repeat="type in typeList | orderBy" value="{{type}}">
                                    {{type}}
                                </option>
                            </select>
                        </div>
                        <div ng-show="type == 'OTHERS'" class="form-group">
                            <label>Specify Mode Type*</label>
                            <input type="text" class="form-control" ng-model="otherModeType" />
                        </div>
                    <div class="form-group">
                        <label>Mode Description*</label>
                        <input type="text"
                               class="form-control"
                               ng-model="description"
                               required/>
                    </div>
                    <div class="form-group">
                        <label>Settlement Type*</label><select
                            class="form-control"
                            ng-model="settlementType"
                            required>
                        <option value="CREDIT">CREDIT</option>
                        <option value="DEBIT">DEBIT</option>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Commission Rate*</label>
                        <input type="number"
                               step="0.01"
                               pattern=".*\S+.*"
                               min = "0.00"
                               class="form-control"
                               ng-model="commissionRate"
                               ng-change=""
                        />
                    </div>
                    <div class="form-group">
                        <label>Generate Pull*</label>
                        <select class="form-control"
                                ng-model="generatePull"
                                required>
                            <option ng-repeat="generatePull in generatePullList | orderBy" value="{{generatePull}}">
                                {{generatePull}}
                            </option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Mode Status*</label><select
                            class="form-control"
                            ng-model="status"
                            required>
                        <option value="ACTIVE">ACTIVE</option>
                        <option value="IN_ACTIVE">INACTIVE</option>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>Mode Category*</label><select
                            class="form-control"
                            ng-model="category"
                            required>
                        <option value="ONLINE">ONLINE</option>
                        <option value="OFFLINE">OFFLINE</option>
                    </select>
                    </div>
                    <div style="width:100%;">
                        <div style ="width:50%; float:left" class="form-group">
                            <label>Automatic Pull Validate*</label>
                            <select class="form-control" ng-model="autoPullValidate">
                                <option ng-repeat="autoPullValidate in autoPullValidateList | orderBy" value="{{autoPullValidate}}">
                                    {{autoPullValidate}}
                                </option>
                            </select>
                        </div>
                        <div style="margin-left: 50%" class="form-group">
                            <label>Automatic Transfer*</label>
                            <select
                                    class="form-control"
                                    ng-model="autoTransfer" required>
                                <option ng-repeat="autoTransfer in autoTransferList | orderBy" value="{{autoTransfer}}">
                                    {{autoTransfer}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div style="width:100%;">
                    <div style ="width:50%; float:left" class="form-group">
                        <label>Automatic Close Transfer*</label>
                        <select class="form-control"
                                ng-model="autoCloseTransfer"required>
                            <option ng-repeat="autoCloseTransfer in autoCloseTransferList | orderBy" value="{{autoCloseTransfer}}">
                                {{autoCloseTransfer}}
                            </option>
                        </select>
                    </div>
                    <div style="margin-left: 50%" class="form-group">
                        <label>Editable*</label>
                        <select class="form-control" ng-model="editable"
                                required>
                            <option ng-repeat="editable in editableList | orderBy" value="{{editable}}">
                                {{editable}}
                            </option>
                        </select>
                    </div>
                </div>

                    <div style="width:100%;">
                        <div style ="width:50%; float:left" class="form-group">
                            <label>Applicable On Discounted Orders*</label>
                            <select class="form-control"
                                    ng-model="applicableOnDiscountedOrders"required>
                                <option ng-repeat="applicableOnDiscountedOrders in applicableOnDiscountedOrdersList | orderBy" value="{{applicableOnDiscountedOrders}}">
                                    {{applicableOnDiscountedOrders}}
                                </option>
                            </select>
                        </div>
                        <div style="margin-left: 50%" class="form-group">
                            <label>Needs Settlement Slip*</label>
                            <select class="form-control"
                                    ng-model="needsSettlementSlip"
                                    required>
                                <option ng-repeat="needsSettlementSlip in needsSettlementSlipList | orderBy" value="{{needsSettlementSlip}}">
                                    {{needsSettlementSlip}}
                                </option>
                            </select>
                        </div>
                    </div>
<!--                    <div class="form-group">-->
<!--                        <label>Needs Settlement Slip*</label>-->
<!--                        <select class="form-control"-->
<!--                                ng-model="needsSettlementSlip"-->
<!--                                required>-->
<!--                            <option ng-repeat="needsSettlementSlip in needsSettlementSlipList | orderBy" value="{{needsSettlementSlip}}">-->
<!--                                {{needsSettlementSlip}}-->
<!--                            </option>-->
<!--                        </select>-->
<!--                    </div>-->

                    <div class="form-group">
                        <label>Validation Source*</label>
                        <input type="text"
                               class="form-control"
                               ng-model="validationSource"
                               required/>
                    </div>
                    <div class="form-group">
                        <label>Ledger Name*</label>
                        <input type="text"
                               class="form-control"
                               ng-model="ledgerName"
                               required/>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary"
                                type="button"
                                ng-click="submitUpdatePaymentMethod(updatePaymentMethod)">UPDATE</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!--    modal 2 for unit payment mapping-->
    <div
            class="modal fade"
            id="unitpaymentModal"
            tabindex="-1"
            role="dialog"
            aria-labelledby="myModalLabel">
        <div
                class="modal-dialog modal-lg"
                role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button
                            type="button"
                            class="close"
                            data-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>

                    <h4 class="modal-title"
                        id="myModalLabel">{{action}} Payment Method Present In Different Units
                    </h4>
                </div>
                    <h3 style="position:relative;left:20px"> Select Cafes</h3>
                    <div class="row">
                        <div
                                style="position:relative;left:20px"
                                class="col-xs-10">
                            <div ng-dropdown-multiselect="" extra-settings="multiSelectSettingsCafes"
                                 options="cafelist"
                                 selected-model="storeSelectedCafes" class="region-card">
                            </div>
                        </div>
                        <div class="col-xs-2">
                            <button
                                    class="btn btn-success"
                                    ng-click="addUnit('cafe')">
                                <i class="fa fa-plus fw"></i>
                            </button>
                        </div>
                </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <ul>
                                <li ng-if="mapping.mappingStatus === 'ACTIVE'" ng-repeat="mapping in selectedUnitPaymentMappings track by mapping.unitDetail.id">
                                    {{mapping.unitDetail.name}}
                                    <button
                                            class="btn btn-danger btn-xs"
                                            ng-click="removeUnit('cafe',mapping.unitDetail.id)">
                                        <i class="fa fa-close fw"></i>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                <div
                        style="position:relative;left:20px"
                        class="form-group">
                    <button class="btn btn-primary"
                            type="button"
                            ng-click="saveUpdatePaymentMethodMapping(unitPaymentMappings)">SAVE</button>
                </div>

            </div>
        </div>
    </div>
