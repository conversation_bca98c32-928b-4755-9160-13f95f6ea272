<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.popeye-modal-container .popeye-modal{
    width: 70%;
}
.red {
	background-color: red;
}
</style>
<div class="modal-content">
	<div class="modal-header">
		<div class="row">
			<div class="col-lg-12">
				<h1 class="page-header">Recipe Cost Calculator</h1>
			</div>
		</div>
	</div>
	<div class="modal-body">
		<div class="row top-buffer-row">
			<div class="col-lg-3">
				 <label>Iteration Parent Name </label>
			</div>
			<div class="col-lg-3" data-ng-if="iterationDetails.linkedProductName">
					<label>{{iterationDetails.linkedProductName}}</label> 
			</div>
			<div class="col-lg-3" data-ng-if="iterationDetails.linkedConstructName && iterationDetails.linkedProductName == null">
					<label>{{iterationDetails.linkedConstructName}}</label>
			</div>
			<div class="col-lg-3">
				 <label>Iteration Name </label>
			</div>
			<div class="col-lg-3">
					<label>{{iterationDetails.iterationName}}</label>
			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-3">
				<label>Select Region </label>
			</div>
			<div class="col-lg-6">
				<select class="form-control" ng-model="selectedRegion"
						ng-options=" region for region in allRegions"
						data-ng-change="setSelectedRegion(selectedRegion)"> </select>

			</div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-12">
				<table class="table table-bordered">
					<tr>
						<td>Product name</td>
						<td>Uom</td>
						<td>Quantity({{iterationDetails.outputQuantity}}{{iterationDetails.outputUom}})</td>
						<td>Quantity(1{{iterationDetails.productUom}})</td>
						<td>Quantity(Yield)</td>
						<td>Yield %</td>
						<td>Yield Reason</td>
						<td>Cost</td>
						<td>Unit Price</td>
					</tr>
					<tr data-ng-repeat="product in iterationDetails.components"
						data-ng-style="(priceMap[product.productId].calculatedFromNegotiatedPrice == 'Y') ? (priceMap[product.productId].cost == 0 ? {'background':'red'} : {'background':'orange'}) : {}">
						<td title="{{product.productId}}" data-ng-style="(priceMap[product.productId].isRecipeProduct == 'Y') ? {'font-weight' : 'bold'} : {}">
							{{product.productName}}</td>
						<td>{{product.uom}}</td>
						<td>{{product.quantity}}</td>
						<td>{{product.quantityPerSubUom}}</td>
						<td>{{product.yieldQuantity}}</td>
						<td>{{product.yieldPercentage}}</td>
						<td>{{product.yieldReason}}</td>
						<td data-ng-if="priceDetailsMap[product.productId] != '-'">{{priceDetailsMap[product.productId]}}</td>
						<td style="text-align: center" data-ng-if="priceDetailsMap[product.productId] == '-'">-</td>
						<td>{{priceMap[product.productId].cost}}</td>
					</tr>
				</table>
			</div>
		</div>
		<div class="row top-buffer-row" data-ng-if="recipeCost>0" style="font-size: 26px;color: darkblue;">
			<div class="col-lg-12"><label>Recipe Cost of 1{{iterationDetails.productUom}} is Rs.{{recipeCost}} (approx) for Region {{selectedRegion}}</label></div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-danger" data-ng-click="closeModal()">Close</button>
	</div>
</div>

