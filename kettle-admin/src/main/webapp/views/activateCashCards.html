<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <br>
        <h1 class="page-header">Cash Card Manager</h1>
    </div>
</div>
<div class="panel panel-info">
    <div class="panel-heading">Cash Card Activation</div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-12" style="padding: 15px;">
                <input type="radio" data-ng-model="byCard" data-ng-value="true">
                Card Number <input type="radio" data-ng-model="byCard"
                                   data-ng-value="false"> Serial Number
            </div>
        </div>
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-xs-12">
                <label>Code</label> <input type="text" data-ng-model="code"
                                           class="form-control" style="margin-bottom: 10px;"> <input
                    type="button" class="btn btn-primary" value="Submit"
                    data-ng-click="getCardDetail()"/>
            </div>
        </div>
        <div class="row" data-ng-if="cardDetail!=null">
            <div class="col-xs-12">
                <div class="row" style="margin-bottom: 10px;">
                    <div class="col-xs-4">
                        <label>Card Id:</label><br/> {{cardDetail.cashCardId}}
                    </div>
                    <div class="col-xs-4">
                        <label>Serial:</label><br/> {{cardDetail.cardSerial}}
                    </div>
                    <div class="col-xs-4">
                        <label>Code:</label><br/> {{cardDetail.cardNumber}}
                    </div>
                </div>
                <div class="row" style="margin-bottom: 10px;">
                    <div class="col-xs-4">
                        <label>Created At:</label><br/> {{cardDetail.creationTime |
                        date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                    </div>
                    <div class="col-xs-4">
                        <label>Purchased At:</label><br/> {{cardDetail.purchaseTime |
                        date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                    </div>
                    <div class="col-xs-4">
                        <label>Activated At:</label><br/> {{cardDetail.activationTime |
                        date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                    </div>
                </div>
                <div class="row" style="margin-bottom: 10px;">
                    <div class="col-xs-4">
                        <label>Buyer Id:</label><br/> {{cardDetail.buyerId}}
                    </div>
                    <div class="col-xs-4">
                        <label>Customer Id:</label><br/> {{cardDetail.customerId}}
                    </div>
                    <div class="col-xs-4">
                        <label>Start Date:</label><br/> {{cardDetail.startDate}}
                    </div>
                </div>
                <div class="row" style="margin-bottom: 10px;">
                    <div class="col-xs-4">
                        <label>Initial Amount:</label><br/>
                        {{cardDetail.cashInitialAmount}}
                    </div>
                    <div class="col-xs-4">
                        <label>Balance:</label><br/> {{cardDetail.cashPendingAmount}}
                    </div>
                    <div class="col-xs-4">
                        <label>Last Modified:</label><br/> {{cardDetail.lastModified |
                        date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <label>Status:</label><br/> {{cardDetail.cardStatus}}
                    </div>
                </div>
                <div class="row" data-ng-if="cardDetail.cardStatus=='INITIATED'">
                    <div class="col-xs-12">
                        <label>Status:</label><br/>
                        <textarea data-ng-model="activationReason" class="form-control"
                                  data-ng-trim="true" />
                        <input type="button" class="btn btn-primary" value="Activate"
                               data-ng-click="activateCard(activationReason)"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info" data-ng-show="noResultMsg">No
            results found!
        </div>
    </div>
</div>

<div class="panel panel-info">
    <div class="panel-heading">Cash Card Offers</div>
    <div class="panel-body">


        <div class="row" style="margin-bottom: 10px">
            <div class="col-xs-12">
				<span class="btn btn-primary" data-toggle="modal"
                      data-target="#offerModal" data-ng-click="toggelEditMode()">
					{{editMode ? "Cancel": "Add Offer"}}</span>
            </div>
        </div>

        <div class="row" data-ng-show="!editMode" style="margin-bottom: 20px;">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>Start date</label>
                            <div class="datepicker" data-date-format="yyyy-MM-dd">
                                <input data-ng-model="startDate" type="text" class="form-control"
                                       placeholder="yyyy-mm-dd" required/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>End date</label>
                            <div class="datepicker" data-date-format="yyyy-MM-dd">
                                <input data-ng-model="endDate" type="text" class="form-control"
                                       placeholder="yyyy-mm-dd" required/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>Select Partner *</label>
                            <div  ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                 options="storeSelectedPartner" selected-model="Partnerdata"  >
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-bottom: 30px;">
					<span class="btn btn-primary" data-ng-click="getOffersForDate()" style="margin-top: 20px">
						View Offers
					</span>
                </div>
                <div class="col-xs-12 form-group">
                    <div class="form-group region-card">
                        <label class="col-xs-2 region-card">Select Denomination </label>
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="denominationList" selected-model="denomination" class="col-xs-2 region-card"
                             data-ng-click="applyFilter()">
                        </div>
                        <label class="col-xs-2 region-card">Select Status </label>
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="status" selected-model="storeStatus" class="col-xs-2 region-card"
                             data-ng-click="applyFilterStatus()">
                        </div>
                        <button class="btn btn-danger" data-ng-click="deactivateAll()" style="width: 100px; margin-left: 40px"> Deactivate
                        </button>
                        <button class="btn btn-success" style="width: 100px; margin-left: 10px" data-ng-click="ActivateAll()"> Activate
                        </button>
                    </div>
                </div>
                <div class="col-xs-12 form-group">
                    <div class="form-group region-card">
                        <label class="col-xs-2 region-card">Direct Purchase Of Gift Card </label>
                        <button class="btn btn-danger" data-ng-click="giftCardWalletPurchase('N')" style="width: 100px; margin-left: 40px"> Deactivate
                        </button>
                        <button class="btn btn-success" style="width: 100px; margin-left: 10px" data-ng-click="giftCardWalletPurchase('Y')"> Activate
                        </button>
                    </div>
                </div>
               <div>
                <div class="col-xs-12" style="padding: 15px;" data-ng-if="offerStatus">
                              <span><h4>Activate/Deactivate Offers</h4><button class="btn btn-primary" data-ng-click="changeSelectedOfferStatus()">Submit</button></span>
                </div>
                <table class="table table-striped table-bordered"
                       style="font-size: 10px" data-ng-if="offers != null && offers.length > 0">
                    <thead>
                    <th>Offer Id</th>
                    <th>Description</th>
                    <th>Suggest Wallet <b>Description</b></th>
                    <th>Unit Id</th>
                    <th>Unit Name</th>
                    <th>Denomination</th>
                    <th>Percentage</th>
                    <th>Suggest Wallet <b>Percentage</b></th>
                    <th>Wallet Type</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Offer Status</th>
                    <th>PartnerId</th>
                    <th><select class="form-control" style="width: 100px !important; margin-left: 65px;"
                                data-ng-model="offerStatus"
                                data-ng-click="selectStatus(offerStatus)">
                        <option data-ng-repeat="offerStatus in status"
                                data-ng-click="selectStatus(offerStatus)"
                                value="{{offerStatus}}">
                            {{offerStatus}}
                        </option>
                    </select>
                        <input type="checkbox" data-ng-click="checkAllOffers()" class="ng-pristine ng-valid ng-touched ng-not-empty" >
                    </th>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="offer in newOffer track by $index" data-ng-model="selectOffer">
                        <td>{{offer.cashCardOfferId}}</td>
                        <td>{{offer.description}}</td>
                        <td>{{offer.suggestWalletDescription}}</td>
                        <td>{{offer.unitId}}</td>
                        <td>{{unitMap[offer.unitId].name}}</td>
                        <td>{{offer.denomination}}</td>
                        <td>{{offer.percentage}}</td>
                        <td>{{offer.suggestWalletPercentage}}</td>
                        <td>{{offer.walletType}}</td>
                        <td>{{offer.startDate | date:'yyyy-MM-dd'}}</td>
                        <td>{{offer.endDate | date:'yyyy-MM-dd'}}</td>
                        <td>
                            <h4>
									<span
                                            data-ng-class="offer.offerStatus == 'ACTIVE' ? 'label label-success' : 'label label-danger'"
                                            class="">{{offer.offerStatus}} </span>
                            </h4>
                        </td>
                        <td>{{offer.partnerId == null ? "" : findPartnerById(offer.partnerId).name}}</td>
                            <td>
                                <input type="checkbox" data-ng-click="testCheckbox($index, offer)"  data-ng-model="selectOffer[offer.cashCardOfferId]" class="ng-pristine ng-valid ng-touched ng-not-empty">
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <div data-ng-show="editMode" style="padding: 10px">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>Start date</label>
                            <div class="datepicker" data-date-format="yyyy-MM-dd">
                                <input data-ng-model="startDate" type="text" class="form-control"
                                       placeholder="yyyy-mm-dd" required/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>End date</label>
                            <div class="datepicker" data-date-format="yyyy-MM-dd">
                                <input data-ng-model="endDate" type="text" class="form-control"
                                       placeholder="yyyy-mm-dd" required/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>Select Partner *</label>
                            <div  ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                 options="storeSelectedPartner" selected-model="Partnerdata"  >
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-4">
                        <div class="form-group">
                            <label>Wallet Type * </label>
                            <select ng-model="walletType" class="form-control">
                                <option value="GIFT_CARD_WALLET">E-Gift Card</option>
                                <option value="SUGGEST_WALLET">Suggest wallet</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label>Offer Percentage</label>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="0" id="0percent">
                                <label class="form-check-label" for="0percent">
                                    0%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="1" id="1percent">
                                <label class="form-check-label" for="1percent">
                                    1%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="2" id="2percent">
                                <label class="form-check-label" for="2percent">
                                    2%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="3" id="3percent">
                                <label class="form-check-label" for="3percent">
                                    3%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="4" id="4percent">
                                <label class="form-check-label" for="4percent">
                                    4%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="5" id="5percent">
                                <label class="form-check-label" for="5percent">
                                    5%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="6" id="6percent">
                                <label class="form-check-label" for="6percent">
                                    6%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="8" id="8percent">
                                <label class="form-check-label" for="8percent">
                                    8%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="10" id="10percent">
                                <label class="form-check-label" for="10percent">
                                    10%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="15" id="15percent">
                                <label class="form-check-label" for="15percent">
                                    15%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="20" id="20percent">
                                <label class="form-check-label" for="20percent">
                                    20%
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" data-ng-model="percentage" value="25" id="25percent">
                                <label class="form-check-label" for="25percent">
                                    25%
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label>Cash Card Values</label>
                            <div class="form-check">
                                <input type="checkbox" data-ng-model="cashcard50" id="cashcard50">
                                <label class="form-check-label" for="cashcard50">
                                    50
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" data-ng-model="cashcard100" id="cashcard100">
                                <label class="form-check-label" for="cashcard100">
                                    100
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" data-ng-model="cashcard500" id="cashcard500">
                                <label class="form-check-label" for="cashcard500">
                                    500
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" data-ng-model="cashcard1000" id="cashcard1000">
                                <label class="form-check-label" for="cashcard1000">
                                    1000
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" data-ng-model="cashcard2000" id="cashcard2000">
                                <label class="form-check-label" for="cashcard2000">
                                    2000
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" data-ng-model="cashcard5000" id="cashcard5000">
                                <label class="form-check-label" for="cashcard5000">
                                    5000
                                </label>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row row-spacing" data-ng-if="regions.length > 0">

                    <div class="card" data-ng-repeat="r in regions  | orderBy">
                        <div>
                            <!-- data-ng-if=" r!= 'NCR_EDU'" -->
                            <div class="col-xs-4 region-card" data-ng-click="getUnitForRegion(r)" >
                                <ul class="list-group list-group-flush" data-ng-click="getUnitForRegion(r)" >
                                    <li class="list-group-item"
                                        data-ng-class="{'card-selected':  r == selectedRegion}">{{r}}
                                    </li>

                                </ul>

                            </div >
                        </div >

                        </div>
                    </div>
                </div>



            <div class="row row-spacing" data-ng-if="regions.length > 0">
                <div class="col-xs-12 form-group">
                    <div class="form-group region-card">
                        <label class="col-xs-3 region-card">MultiRegion Select</label>
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="trimmedRegions" selected-model="storeSelectedRegion" class="col-xs-4 region-card" >
                        </div>
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="unitSubCategory" selected-model="storeSubCategory" class="col-xs-4 region-card">
                        </div>
                        <div class="col-xs-4 region-card">
                            <button data-ng-click=" getAllUnitProductMapping()" class="btn btn-primary">Get Details</button>
                        </div>
                    </div>
                </div>
            </div>



                <div class="form-group">
                    <label>Search</label>
                    <input type="text" class="form-control" data-ng-model="search" data-ng-change="" />
                </div>
                <table class="table table-bordered">
                    <tr>
                        <th >Unit Selection <input type="checkbox" data-ng-click="checkAll()">  </th>
                        <th>Unit Id</th>
                        <th>Unit Name</th>
                        <th>Unit Region</th>
                        <th>Unit Sub Category</th>
                    </tr>
                    <tr data-ng-repeat="unit in filtered = (regionWiseList | filter:search | orderBy : predicate :reverse) track by unit.id" data-ng-click="unit.selected = !unit.selected" style="cursor: pointer;" data-ng-class="{'rowSelected':unit.selected}">
                        <td>
                            <input type="checkbox" data-ng-model="unit.selected" id="{{unit.id}}" onClick="this.checked=!this.checked;">
                        </td>
                        <td>{{unit.id}}</td>
                        <td>{{unit.name}}</td>
                        <td>{{unit.region}}</td>
                        <td>{{unit.subCategory}}</td>
                    </tr>
                </table>

                <button type="button" class="btn btn-primary" data-ng-click="addCashCardOffers()">Add offers</button>
            </div>
        </div>

    </div>
</div>
