<style>
.popeye-modal-container .popeye-modal {
	width: 70%;
}
</style>
<div class="modal-content" data-ng-init="init()">
	<div class="modal-header">
		<div class="row">
			<div class="col-lg-12">
				<h1 class="page-header">{{unit.name}} Applications</h1>
			</div>
		</div>
	</div>
	<div class="modal-body">
		<div class="row top-buffer-row">
			<div class="col-lg-12"><h3 style="color: red;">Restrict Installation</h3></div>
		</div>
		<div class="row top-buffer-row">
			<div class="col-lg-12" data-ng-repeat="app in applicationList">
				<input type="checkbox" data-ng-checked="app.status == 'ACTIVE'"
					data-ng-click="updateStatus(app)"> <span>{{app.applicationName}}</span>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-danger pull-left"
			data-ng-click="updateRestriction()">Submit</button>
		<button type="button" class="btn btn-warning pull-right"
			data-ng-click="closeModal()">Cancel</button>
	</div>
</div>