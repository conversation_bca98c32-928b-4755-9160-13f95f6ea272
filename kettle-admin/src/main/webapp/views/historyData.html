<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Cafe Auto Push Log Data</h2>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12">
                    <h3>Cafe Menu Auto Push Log Data</h3>
                    <p>Select brand and unit to get the log data </p>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label>Select Brand</label>
                        <select class="form-control"
                                data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                data-ng-model="selectedBrand"
                                data-ng-change="setSelectedBrand(selectedBrand)">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Select Unit</label>
                        <select class="form-control"
                                data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                data-ng-model="selectedUnit"
                                data-ng-change="setSelectedUnit(selectedUnit)">
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="button" class="btn btn-primary" value="SELECT"
                               data-ng-click="logData()"/>
                    </div>
                </div>
                <div class="col-xs-12">
                    <table class="table table-bordered">
                        <thead style="background-color: #50773e; color: #ffffff">
                        <tr>
                            <th>Unit ID</th>
                            <th>Unit Name</th>
                            <th>Employ Id</th>
                            <th>Last Updated Time</th>
                            <th>Zomato Menu Status</th>
                            <th>Swiggy Menu Status</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="unit in CafeMenuHistoryList  track by $index">
                            <td>{{unit.unitId}}</td>
                            <td>{{unit.unitName}}</td>
                            <td>{{unit.employeeId}}</td>
                            <td>{{unit.lastUpdatedTimeString}}</td>
                            <td>{{unit.zomatoMenuFlag}}</td>
                            <td>{{unit.swiggyMenuFlag}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>