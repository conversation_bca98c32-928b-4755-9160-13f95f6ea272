<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Stock Dashboard</h2>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to update product status on partner side.</h3>
                    <p>Select all the products whose status you want to update and then select the partners where you
                        want to take action
                        and press the corresponding button.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Product Stock View</h3>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <select class="form-control"
                                        data-ng-options="unit as unit.name for unit in unitList track by unit.id"
                                        data-ng-model="selectedUnit"
                                        data-ng-change="setSelectedUnit(selectedUnit)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Products"
                                       data-ng-click="getUnitProducts()"/>
                            </div>
                            <div class="form-group" data-ng-if="showUnitProducts">
                                <label>Select Product</label>
                                <select class="form-control"
                                        data-ng-options="product as product.detail.name for product in unitProductList
                                        | filter:{classification:'MENU', type:'!12', inventoryTracked:'true'}
                                        | orderBy:'detail.name' track by product.detail.id"
                                        data-ng-model="selectedProduct"
                                        data-ng-change="setSelectedProduct(selectedProduct)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Stock Data"
                                       data-ng-click="getProductStockSnapshot()"/>
                            </div>
                        </div>
                    </div>
                    <div class="row" data-ng-show="unitProductStock !=null">
                        <div class="col-xs-12" style="text-align: right;">
                            <canvas id="myChart" width="800" height="400"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
