<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .modal-body,
    .modal-header,
    .modal-footer {
        background-color: white;
    }

    .modal {
        background-color: white;
        margin: 70px;
    }
</style>

<div class="row" ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Loyalty Management
        </h1>
    </div>
    <div class="row">
        <div class="col-lg-4" style="margin:8px">
            <input type="text" ng-model="searchValue" placeholder="Search loyalty events" class="form-control" />
        </div>
        <div class="col-lg-1">
            <div class="row" style="display:flex;">
                <button class="btn btn-primary" style="margin:8px" title="Please enter Generated Order Id to search."
                    data-ng-model="searchByGeneratedOrderId" data-ng-disabled="searchValue == '' || searchValue == null"
                    ng-click="getLoyaltyEvents('GENERATED_ORDER_ID',10)">
                    By GeneratedOrderId
                </button>
                <button class="btn btn-primary" style="margin:8px" title="Please enter CustomerId Id to search."
                    data-ng-model="searchByCustomerId" data-ng-disabled="searchValue == '' || searchValue == null"
                    ng-click="getLoyaltyEvents('CUSTOMER_ID',10)">
                    By CustomerId
                </button>
                <button class="btn btn-primary" style="margin:8px" title="Please enter Order Id to search."
                    data-ng-model="searchByOrderId" data-ng-disabled="searchValue == '' || searchValue == null"
                    ng-click="getLoyaltyEvents('ORDER_ID',10)">
                    By OrderId
                </button>
            </div>
        </div>
    </div>
    <div class="row" style="margin-top: 30px">
        <div class="col-xs-12" ng-if="loyaltyEventsData.length > 0">
            <div class="row">
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered">
                        <thead style="background-color: #E5F3C7">
                            <th style="text-align:center">ORDER ID</th>
                            <th style="text-align:center">CUSTOMER ID</th>
                            <th style="text-align:center">TRANSACTION_POINTS</th>
                            <th style="text-align:center">TRANSACTION_CODE_TYPE</th>
                            <th style="text-align:center">TRANSACTION_TIME</th>
                            <th style="text-align:center">TRANSACTION_STATUS</th>
                            <th style="text-align:center">REASON&nbsp;</th>
                        </thead>
                        <tbody>
                            <tr data-ng-repeat="event in loyaltyEventsData">
                                <td style="text-align:center">{{event.orderId}}</td>
                                <td style="text-align:center">{{event.customerId}}</td>
                                <td style="text-align:center">{{event.transactionPoints}}</td>
                                <td style="text-align:center">{{event.transactionCodeType}}</td>
                                <td style="text-align:center">{{event.transactionTime}}</td>
                                <td style="text-align:center">{{event.transactionStatus}}</td>
                                <td style="text-align:center">{{event.reason}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-lg-10" ng-if="loyaltyEventsData.length == 0">
                    <h4>No results found</h4>
                </div>

                <button class="btn btn-primary pull-right" style="margin:8px" title="More Data"
                    data-ng-disabled="searchValue == '' || searchValue == null" ng-click="getMoreData()">
                    More
                </button>
            </div>
        </div>
    </div>
</div>
</div>
</div>