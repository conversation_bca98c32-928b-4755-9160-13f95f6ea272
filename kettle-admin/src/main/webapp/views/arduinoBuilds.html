<div data-ng-init="init()" class="form-group">
    <div class="row">
        <div class="col-xs-12">
            <h2>
                Arduino Builds Console

                <div class = "btn-group pull-right">
                    <button class="btn" data-ng-click="getActiveVersion()">Refresh</button>
                    <button data-ng-if="initiatedVersion == undefined || initiatedVersion == null"
                            class="btn btn-primary" data-ng-click="initiateBuild()">Initiate New Build</button>
                    <a class="btn btn-danger" ui-sref="dashboard.reports">Back</a>
                </div>
            </h2>
        </div>
    </div>
    <hr>
    <!-- Available Builds view starts here-->
    <div class="row">
        <div class="col-xs-12" data-ng-if = "activeVersion != undefined">
            <h4>
                <span>Active Build</span>
            </h4>
            <span><strong>Name:</strong> {{activeVersion.version}}</span>
            <br/>
            <span><strong>Upload Time:</strong> {{activeVersion.initiationTime | date : 'dd/MM/yyyy h:mma'}}</span>
            <br/>
            <span><strong>Activation Time:</strong> {{activeVersion.activationTime | date : 'dd/MM/yyyy h:mma'}}</span>
            <br/>
        </div>

        <div class="col-xs-12" data-ng-if = "initiatedVersion != undefined">
            <h4>
                <span>Initiated Build: <strong>{{initiatedVersion.version}}</strong></span>
                <button class="btn btn-primary pull-right" data-ng-click="activateBuild(initiatedVersion)">Activate</button>
            </h4>
        </div>
        <div class="col-xs-12" data-ng-if = "initiatedVersion != undefined">
            <div class="row form-inline">
                <div class="form-group">
                    <label>Select Unit:</label>
                    <select class="form-control" data-ng-model="selectedUnit"
                            data-ng-options="unit as unit.name for unit in unitList | orderBy:'name'"></select>
                </div>
                <div class="form-group">
                    <label>No of Monks</label>
                    <input type="number" step="1" min="1" max="6" data-ng-model="monksInUnit">
                </div>
                <button class="btn btn-default" data-ng-click="addUnit(selectedUnit, monksInUnit)">Add</button>
            </div>
            <div class="row" data-ng-if="buildUnits!=null" data-ng-repeat="unit in buildUnits">
                <div class="form-group">
                    <label>Unit:{{unit.name}}</label>
                    <div class="form-group" data-ng-repeat="monk in unit.monks">
                        <label>{{monk.name}}</label>
                        <input type="file" data-ng-model="monk.file">
                        <button class="btn btn-primary" data-ng-if="monk.file!=null"
                                data-ng-click="uploadBuild(unit, monk)">Upload</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>