<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Tea house Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Tea house Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Tea house Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div ng-init="init()">
	<div class="row">
		<div class="col-lg-12">
			<!-- &nbsp; <img src="img/printer.png" width="20" height="20" ng-click="printSample()" align="right" title="Sample Print" style="margin-top:20px; cursor:pointer"> -->
			<!-- <loading style align="center"></loading>---->
			<h1 class="page-header">
				Unit Dash board
                <button type="button" class="btn btn-primary pull-right" style="margin-left: 10px" id="btnAddNewUnitsIDnew" ng-click="open()">
                    <i class="fa fa-plus"></i> Add new unit
                </button>
<!--				<button type="button" class="btn btn-primary pull-right" id="btnAddNewUnitsID" ng-click="addNewUnitModal()">-->
<!--					<i class="fa fa-plus"></i> Add new unit-->
<!--				</button>-->
			</h1>
		</div>
	</div>
	<div ui-view></div>
	<!-- Modal -->
	<div class="modal fade" id="unitManagemmentModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
					<h4 class="modal-title" id="myModalLabel">Manage Unit - {{newUnitName}}</h4>
				</div>
				<div class="modal-body">
					<div>
						<!-- Nav tabs -->
						<ul class="nav nav-tabs" role="tablist">
							<li role="presentation" ng-class="{active: !tab1, disabled: tab1}"><a data-target="#basic"
								aria-controls="basic" role="tab" data-toggle="tab">Basic Detail</a></li>
							<li role="presentation" ng-class="{active: !tab2, disabled: tab2}"><a data-target="#division"
								aria-controls="division" role="tab" data-toggle="tab">Division</a></li>
							<li role="presentation" ng-class="{active: !tab3, disabled: tab3}"><a data-target="#address"
								aria-controls="address" role="tab" data-toggle="tab">Address</a></li>
							<li role="presentation" ng-class="{active: !tab5, disabled: tab5}"><a data-target="#pricing"
								aria-controls="pricing" role="tab" data-toggle="tab">Product Pricing</a></li>
							<li role="presentation" ng-class="{active: !tab6, disabled: tab6}"><a data-target="#inventory"
								aria-controls="inventory" role="tab" data-toggle="tab">Inventory Clone</a></li>
							<li role="presentation" ng-class="{active: !tab7, disabled: tab7}"><a data-target="#partner"
								aria-controls="partner" role="tab" data-toggle="tab">Delivery Partner</a></li>
							<li role="presentation" ng-class="{active: !tab8, disabled: tab8}"><a data-target="#businessHour"
								aria-controls="businessHour" role="tab" data-toggle="tab">Business Hour</a></li>
							<li role="presentation" ng-class="{active: !tab9, disabled: tab9}"><a data-target="#reportingManagers"
								aria-controls="reportingManagers" role="tab" data-toggle="tab">Reporting Manager</a></li>
							<li role="presentation" ng-class="{active: !tab10, disabled: tab10}"><a data-target="#locationUnit"
								aria-controls="locationUnit" role="tab" data-toggle="tab">Location</a></li>
							<li role="presentation" ng-class="{active: !tab11, disabled: tab11}"><a data-target="#summary"
								aria-controls="summary" role="tab" data-toggle="tab">Summary Details</a></li>
						</ul>
						<!-- Tab panes -->
						<div class="tab-content">
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab1}" id="basic">
								<h3>Unit Info</h3>
								<!-- <div class="form-group">
                                    <label>Family *</label>
                                    <select class="form-control" ng-model="newUnitFamily">
                                        <option ng-repeat="family in families" value="{{family}}">{{family}}</option>
                                    </select>
                                </div> -->
								<div class="form-group">
								<label>Family *</label>
								<select class="form-control" ng-model="newUnitFamily"
										ng-options="familiesData as familiesData for familiesData in families track by familiesData"
										ng-change="showFamily(newUnitFamily)">
								</select>
							</div>
								<div id="idChaiMonkDiv" style="display: none">
									<div class="form-group">
										<label>Company Name *</label>
										<select class="form-control" ng-model="selectedCompany"
											ng-options="companyViewData as companyViewData.companyName for companyViewData in kiosCompanyList track by companyViewData.companyId"
											ng-change="selectOfficeList(selectedCompany)">
										</select>
									</div>
									<div class="form-group">
										<label>Office Name *</label>
										<select class="form-control" ng-model="selectedOffice"
											ng-options="officeData as officeData.officeName for officeData in kioskOfficeList track by officeData.officeId"
											ng-change="selectLocationList(selectedOffice)"></select>
									</div>
									<div class="form-group">
										<label>Location Name *</label>
										<select class="form-control" ng-model="selectedLocation"
											ng-options="locationData as locationData.locationName for locationData in kioskLocationList track by locationData.locationId"
											ng-change="makeKioskLocation(selectedLocation)"></select>
									</div>
									<div class="form-group">
										<label>Location*</label>
										<select class="form-control" ng-model="newUnitRegion">
											<option ng-repeat="region in regions" value="{{region}}">{{region}}</option>
										</select>
									</div>
								</div>
								<div class="form-group">
									<label>Company *</label>
									<select class="form-control" ng-model="newUnitCompany"
											ng-options="companyViewData as companyViewData.name for companyViewData in newUnitCompanyList track by companyViewData.id"
											ng-change="setCompanyData(newUnitCompany)">
										</select>
								</div>
								<div class="form-group">
									<label>Unit Name *</label>
									<input class="form-control" style="text-transform: capitalize" ng-model="newUnitName"
										ng-disabled="monkDisabled()" />
								</div>
								<div class="form-group">
									<label>Reference Name *</label>
									<input class="form-control" style="text-transform: capitalize" ng-model="referenceUnitName"
										ng-disabled="monkDisabled()" />
								</div>
								<div class="form-group">
									<label>Selects Region *</label>
									<select class="form-control" ng-model="newUnitRegion" ng-disabled="monkDisabled()">
										<option ng-repeat="region in regions" value="{{region}}">{{region}}</option>
									</select>
								</div>
								<div class="form-group">
									<label>Sub Category *</label>
									<select class="form-control" ng-model="selectedSubCategory" ng-disabled="monkDisabled()"
										ng-options="subCategory as subCategory for subCategory in subCategories">
									</select>
								</div>
								<div class="form-group">
									<label>Business Type *</label>
									<select class="form-control" ng-model="selectedBusinessType" ng-disabled="monkDisabled()"
										ng-options="businessType as businessType for businessType in businessTypeList">
									</select>
								</div>
								<div class="form-group">
									<label>Unit Email *</label>
									<input class="form-control" type="email" ng-model="newUnitEmail" />
								</div>
								<div class="form-group">
									<label>Communication Channel *</label>
									<input class="form-control" type="text" ng-model="newChannel" />
								</div>
								<div class="form-group">
									<label>GSTIN No *</label>
									<input class="form-control" type="text" ng-model="newUnitTin" ng-disabled="monkDisabled()" />
								</div>
								<div class="form-group">
									<label>No of Table *</label>
									<input class="form-control" placeholder="No of Table" ng-disabled="monkDisabled()" string-to-number
										type="number" maxlength="6" min="1" data-ng-model="noOfTables" />
								</div>
								<div class="form-group">
									<label>No of Terminals *</label>
									<input class="form-control" placeholder="No of Terminals" string-to-number type="number"
										ng-disabled="monkDisabled()" maxlength="6" min="1" data-ng-model="newUnitTerminals" />
								</div>
								<div class="form-group">
									<label>No of Take Away Terminals * (Minimum 1 is required.)</label>
									<input class="form-control" string-to-number type="number" maxlength="2" min="1" ng-disabled="monkDisabled()"
										data-ng-model="noOfTakeAway" />
								</div>
								<div class="form-group">
									<label>Workstation Enabled *</label>
									<select class="form-control" ng-model="workstationEnabled" ng-disabled="monkDisabled()"
										ng-options="workstationEnabledData as workstationEnabledData.name for workstationEnabledData in workstationUnit track by workstationEnabledData.code">
									</select>
								</div>
								<div class="form-group">
									<label>Free Internet Access *</label>
									<select class="form-control" ng-model="freeInternetAccess" ng-disabled="monkDisabled()"
										ng-options="freeInternetAccessData as freeInternetAccessData.name for freeInternetAccessData in freeInternetAccessUnit track by freeInternetAccessData.code">
									</select>
								</div>
								<div class="form-group">
									<label>Table Service Enabled *</label>
									<select class="form-control" ng-model="tableService" ng-disabled="monkDisabled()"
										ng-options="tableServiceData as tableServiceData.name for tableServiceData in tableServiceUnit track by tableServiceData.code">
									</select>
								</div>
								<div class="form-group">
									<label>Table Service Type *</label>
									<select class="form-control" ng-model="tableServiceType" ng-disabled="monkDisabled()"
											ng-options="item as item.name for item in ab.zeroOneOption track by item.code">
									</select>
								</div>
								<div class="form-group">
									<label>Token Service Enabled *</label>
									<select class="form-control" ng-model="tokenService" ng-disabled="monkDisabled()"
										ng-options="tokenServiceData as tokenServiceData.name for tokenServiceData in tokenServiceUnit track by tokenServiceData.code">
									</select>
								</div>
								<div class="form-group">
									<label>Max token Number Limit *</label>
									<input class="form-control" string-to-number type="number" maxlength="3" min="1" ng-disabled="monkDisabled()"
										data-ng-model="maxTokenLimit" />
								</div>
								<div class="form-group">
									<label>Number of Electricity Meters *</label>
									<input class="form-control" string-to-number type="number" maxlength="3" min="1" ng-disabled="monkDisabled()"
										data-ng-model="noOfMeter" />
								</div>
								<div class="form-group">
									<label>DG Meter Available *</label>
									<select class="form-control" ng-model="dGAvailable" ng-disabled="monkDisabled()"
										ng-options="dgOption as dgOption.name for dgOption in dgOptionList">
									</select>
								</div>
								<div class="form-group">
									<label>Live Inventory Enabled *</label>
									<select class="form-control" ng-model="liveInventoryEnabled" ng-disabled="monkDisabled()"
										ng-options="dgOption as dgOption.name for dgOption in dgOptionList">
									</select>
								</div>
								<div class="form-group">
									<label>Packaging Type</label>
									<select class="form-control" ng-model="packagingType" ng-disabled="monkDisabled()"
											ng-options="packagingType for packagingType in packagingTypeList">
									</select>
								</div>
								<div class="form-group">
									<label>Packaging Value</label>
									<input class="form-control"  type="number" ng-disabled="monkDisabled()"
										   data-ng-model="packagingValue" />
								</div>
								<div class="form-group">
									<label>Google Merchant Id</label>
									<input class="form-control"  type="text" ng-disabled="monkDisabled()"
										   data-ng-model="googleMerchantId" />
								</div>

								<!-- - <div class="form-group">
                                      <label>Area Manager *</label>
                                      <select class="form-control" ng-model="selectedAreaManager"  ng-options="reportingManager as reportingManager.name for reportingManager in reportingManagers | orderBy:'name' track by reportingManager.id" required > </select>
                                  </div> --->
								<button class="btn btn-primary" type="button" ng-click="validateBasicDetail()">Next</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab2}" id="division">
								<div class="form-group">
									<h3>Division *</h3>
									<select class="form-control" ng-model="newUnitDivision" ng-disabled="monkDisabled()"
										ng-options="division as division.name for division in divisions track by division.id"></select>
								</div>
								<button class="btn btn-primary" type="button" ng-click="selectTab('tab1')">Prev</button>
								<button class="btn btn-primary" type="button" ng-click="gotoAddressTab('tab3')">Next</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab3}" id="address">
								<div class="form-group">
									<h3>Address</h3>
									<div class="form-group">
										<label>Line 1 *</label>
										<input class="form-control" ng-model="address.line1" ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Line 2 *</label>
										<input class="form-control" ng-model="address.line2" ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Line 3</label>
										<input class="form-control" ng-model="address.line3" ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Country *</label>
										<select class="form-control" ng-disabled="monkDisabled()" ng-model="selectedAddressCountry"
											ng-options="selectCountryListData as selectCountryListData.name for selectCountryListData in countriesList track by selectCountryListData.id"
											ng-change="showCountryView(selectedAddressCountry)">
										</select>
									</div>
									<div class="form-group">
										<label>State *</label>
										<select class="form-control" ng-disabled="monkDisabled()" ng-model="selectedAddressState"
											ng-options="selectStateListData as selectStateListData.name for selectStateListData in stateList track by selectStateListData.id"
											ng-change="selectStateUnit(selectedAddressState)">
										</select>
									</div>
									<div class="form-group">
										<label>City *</label>
										<select class="form-control" ng-disabled="monkDisabled()" ng-model="selectedAddressCity"
											ng-options="selectCityListData as selectCityListData.name for selectCityListData in locationList track by selectCityListData.id">
										</select>
									</div>
									<div class="form-group">
										<label>Zipcode *</label>
										<input class="form-control" string-to-number type="number" my-maxlength="6" min="0" ng-model="address.zipCode"
											ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Contact 1 *</label>
										<input class="form-control" string-to-number type="number" my-maxlength="10" min="10"
											ng-model="address.contact1" ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Contact 2</label>
										<input class="form-control" string-to-number type="number" my-maxlength="10" min="10"
											ng-model="address.contact2" ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Latitude *</label>
										<input class="form-control" string-to-number type="number" maxlength="18" min="0" ng-model="address.latitude"
											ng-disabled="monkDisabled()" />
									</div>
									<div class="form-group">
										<label>Longitude *</label>
										<input class="form-control" string-to-number type="number" maxlength="18" min="0" ng-model="address.longitude"
											ng-disabled="monkDisabled()" />
									</div>
								</div>
								<button class="btn btn-primary" type="button" ng-click="selectTab('tab2')">Prev</button>
								<button class="btn btn-primary" type="button" ng-click="validateAddress()">Next</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab4}" id="tax">
								<div class="form-group">
									<h3>Tax Details</h3>
									<div class="form-group" ng-repeat="taxProfile in taxProfiles">
										<label>{{taxProfile.name}}({{taxProfile.type}}) *</label>
										<input class="form-control" type="number" string-to-number max="100" min="0" ng-model="taxProfile.percentage" />
									</div>
								</div>
								<button class="btn btn-primary" type="button" ng-click="selectTab('tab3')">Prev</button>
								<button class="btn btn-primary" type="button" ng-click="validateTaxProfiles()">Next</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab5}" id="pricing">
								<div class="form-group">
									<h3>Select product cloning unit</h3>
									<hr />
									<div class="form-group">
										<label>Select unit from which you want to clone *</label>
										<select class="form-control" ng-model="newUnitProductPricing"
											ng-options="unit as unit.name for unit in unitlist track by unit.id"></select>
									</div>
								</div>
								<button class="btn btn-primary" type="button" ng-click="selectTab('tab3')">Prev</button>
								<button class="btn btn-primary" type="button" ng-click="validateUnitProductPricing()">Next</button>
								<button class="btn btn-primary" type="button" ng-if="familyData=='MONK'" ng-click="skipProductCloning()">
									Skip</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab6}" id="inventory">
								<div class="form-group">
									<h3>Select Inventory Cloning Unit</h3>
									<hr />
									<div class="form-group">
										<label>Select unit from which you want to clone *</label>
										<select class="form-control" ng-model="newUnitInventoryClone"
											ng-options="unit as unit.name for unit in unitlist track by unit.id"></select>
									</div>
								</div>
								<button class="btn btn-primary" type="button" ng-click="selectTab('tab5')">Prev</button>
								<button class="btn btn-primary" type="button" ng-click="validateUnitInventoryCloning()">Next</button>
								<button class="btn btn-primary" type="button" ng-if="familyData=='MONK'" ng-click="skipInventoryCloning()">
									Skip</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab7}" id="partner">
								<h3>Select Delivery Partners</h3>
								<div class="row">
									<div class="col-xs-10">
										<select class="form-control" ng-model="deliveryPartners"
											ng-options="partnerDeliveryList as partnerDeliveryList.name for partnerDeliveryList in refPartnerLists track by partnerDeliveryList.id">
											{{partnerDeliveryList.status}}
										</select>
									</div>
									<div class="col-xs-2">
										<button class="btn btn-success" ng-click="addPriorityPartners()">
											<i class="fa fa-plus fw"></i>
										</button>
									</div>
									<div class="row">
										<div class="col-xs-12">
											<ul>
												<li ng-repeat="partnerAddedList in addedPartners track by partnerAddedList.detail.id">
													{{partnerAddedList.detail.name}} <label ng-repeat="priority in priorityList">
														&nbsp;&nbsp; {{priority}}&nbsp;&nbsp; <input type="radio" ng-model="partnerAddedList.priority"
															ng-value="priority" name="{{partnerAddedList.id}}">
													</label>
													<button class="btn btn-danger btn-xs" ng-click="removePartner(partnerAddedList)">
														<i class="fa fa-close fw"></i>
													</button>
												</li>
											</ul>
										</div>
									</div>
								</div>
								<br>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='add'" ng-click="selectTab('tab6')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='edit'" ng-click="selectTab('tab4')">Prev</button>
								<button class="btn btn-primary" type="button" ng-click="validateUnitPartnerDetails()">Next</button>
								<button class="btn btn-primary" type="button" ng-if="familyData=='MONK'" ng-click="skipPartnerDetail()">
									Skip</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab8}" id="businessHour">
								<div class="form-group">
									<h3>Business Hours</h3>
									<hr />
								</div>
								<!-- <div><pre>{{ Items | json }}</pre></div>   -->
								<div class="row" style="font-size: 12px">
									<div class="col-xs-12" style="margin-bottom: 20px">
										<label> No Of Shifts </label>
										<select class="form-control" ng-model="selectNoOfShift"
											ng-options="noofShifts.name for noofShifts in noofShift track by noofShifts.name"></select>
									</div>
									<br>
									<div class="col-xs-12">
										<table class="table table-striped table-bordered" style="border: 3px solid">
											<thead style="background-color: #E0E0E0">
												<th style="vertical-align: top"><label class="checkbox-inline">
													<input type="checkbox" ng-model="checkedDays" ng-click="checkAllDays()" /><b>Days </b></label></th>
												<th scope="col"><label class="checkbox-inline">
														<input type="checkbox" ng-model="checkedDine" checked="checked" ng-click="checkAllDineIn(checkedDine)" />
														<b>Dine in</b><br>
														<br> Start Time
														<uib-timepicker ng-model="tickDineStartTime" ng-change="changedDineStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
														Close Time
														<uib-timepicker ng-model="tickDineCloseTime" ng-change="changedDineStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
													</label></th>
												<th scope="col"><label class="checkbox-inline">
														<input type="checkbox" ng-model="checkedCod" ng-click="checkAllCodIn(checkedCod)" /> <b> COD</b> <br>
														<br> Start Time
														<uib-timepicker ng-model="tickCODStartTime" ng-change="changedCodStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
														Close Time
														<uib-timepicker ng-model="tickCODCloseTime" ng-change="changedCodStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
													</label></th>
												<th scope="col"><label class="checkbox-inline">
														<input type="checkbox" ng-model="checkedTakeAway" ng-click="checkTakeAwayIn(checkedTakeAway)" /><b>Take
															Away </b> <br>
														<br> Start Time
														<uib-timepicker ng-model="tickTakeAwayStartTime" ng-change="changedTakeAwayStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
														Close Time
														<uib-timepicker ng-model="tickTakeAwayCloseTime" ng-change="changedTakeAwayStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
													</label></th>
												<th style="vertical-align: top"><label class="checkbox-inline">
														<input type="checkbox" ng-model="checkedHandOver" ng-click="checkAllHandOverIn(checkedHandOver)" /><b>
															Hand Over</b> <br>
														<br> Time
														<uib-timepicker ng-model="tickHandOverTime" ng-change="changedHandOverStartCheck()" hour-step="hstep"
															minute-step="mstep" show-meridian="ismeridian"></uib-timepicker>
													</label></th>
											</thead>
											<tbody>
												<tr ng-repeat="item in Items">
													<td><input type="checkbox" ng-model="item.daysTick"
														ng-change="checkDays(item.dayOfTheWeek,item.daysTick)" />&nbsp;{{item.dayOfTheWeek}}</td>
													<td ng-if="completeUnitObj.family!='DELIVERY'"><input type="checkbox" ng-model="item.dineTick"
														ng-change="checkDineDetails(item.dayOfTheWeek,item.dineTick,$index)" /> <uib-timepicker
															ng-model="singleDineInStartTime[$index]" ng-change="changedDineStartDate(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker> <uib-timepicker
															ng-model="singleDineInCloseTime[$index]" ng-change="changedDineCloseDate(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker></td>
													<td><input type="checkbox" ng-model="item.codeTick"
														ng-change="checkDeliveryDetails(item.dayOfTheWeek,item.codeTick)" /> <uib-timepicker
															ng-model="singleDeliveryStartTime[$index]" ng-change="changedDeliveryStartDate(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker> <uib-timepicker
															ng-model="singleDeliveryCloseTime[$index]" ng-change="changedDeliveryCloseDate(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker></td>
													<td ng-if="completeUnitObj.family!='DELIVERY'"><input type="checkbox" ng-model="item.takeawayTick"
														ng-change="checkTakeAwayDetails(item.dayOfTheWeek,item.takeawayTick)" /> <uib-timepicker
															ng-model="singleTakeAwayStartTime[$index]" ng-change="changedTakeAwayStartDate(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker> <uib-timepicker
															ng-model="singleTakeAwayCloseTime[$index]" ng-change="changedTakeAwayCloseDate(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker></td>
													<td><input type="checkbox" ng-model="item.handoverTick"
														ng-change="checkHandOverDetails(item.dayOfTheWeek,item.handoverTick)"> <uib-timepicker
															ng-model="singleHandOverTime[$index]" ng-change="changedHandOverTime(item.dayOfTheWeek,$index)"
															hour-step="hstep" minute-step="mstep" show-meridian="ismeridian"></uib-timepicker></td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='add'" ng-click="selectTab('tab7')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='edit'" ng-click="selectTab('tab7')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-click="validateBusinessHours()">Next</button>
							</div>
							<!--   <div>{{completeUnitObj}}</div> -->
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab9}" id="reportingManagers">
								<h3>Select Area Manager</h3>
								<div class="form-group">
									<label>Area Manager *</label>
									<select class="form-control" ng-model="selectedReportingManager"
										ng-options="reportingManager as reportingManager.name for reportingManager in reportingManagers | orderBy:'name' track by reportingManager.id"
										required>
									</select>
								</div>
								<h3>Select Cafe Manager</h3>
								<div class="form-group">
									<label>Cafe Manager *</label>
									<select class="form-control" ng-model="selectedCafeManager"
											ng-options="cafeManager as cafeManager.name for cafeManager in cafeManagers | orderBy:'name' track by cafeManager.id"
											required>
									</select>
								</div>
								<br>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='add'" ng-click="selectTab('tab8')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='edit'" ng-click="selectTab('tab8')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-click="showReportingManager()">Next</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab10}" id="locationUnit">
								<h3>Location</h3>
								<div class="form-group">
									<label>Country *</label>
									<label>{{selectedAddressCountry.name}}</label>
								</div>
								<div class="form-group">
									<label>State *</label>
									<label>{{selectedAddressState.name}}</label>
								</div>
								<div class="form-group">
									<label>City *</label>
									<label>{{selectedAddressCity.name}}</label>
								</div>
								<br>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='add'" ng-click="selectTab('tab9')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-if="unitAction=='edit'" ng-click="selectTab('tab9')">Prev
								</button>
								<button class="btn btn-primary" type="button" ng-click="showLocationUnit()">Next</button>
							</div>
							<div role="tabpanel" class="tab-pane" ng-class="{active: !tab11}" id="summary">
								<div class="form-group" style="line-height: 27px">
									<h3>Please verify details</h3>
									<hr />
									<h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 28px">Unit Info</h4>
									<div class="row">
										<div class="col-xs-6">
											<strong>Unit Name:</strong> {{completeUnitObj.name}}
										</div>
										<div class="col-xs-6">
											<strong>Unit Email:</strong> {{completeUnitObj.unitEmail}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>Unit Family:</strong> {{completeUnitObj.family}}
										</div>
										<div class="col-xs-6">
											<strong>Unit Region:</strong> {{completeUnitObj.region}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>No of Take Away Terminal Count:</strong> {{completeUnitObj.noOfTakeawayTerminals}}
										</div>
										<div class="col-xs-6">
											<strong>Work Station Enabled:</strong> {{completeUnitObj.workstationEnabled}}
										</div>
										<div class="col-xs-6">
											<strong>Free Internet Access:</strong> {{completeUnitObj.freeInternetAccess}}
										</div>
										<div class="col-xs-6">
											<strong>Table Service Enabled:</strong> {{completeUnitObj.tableService}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>Unit Terminals count:</strong> {{completeUnitObj.noOfTerminals}}
										</div>
										<div class="col-xs-6">
											<strong>Unit GSTIN number:</strong> {{completeUnitObj.tin}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>Unit Reference Name:</strong> {{completeUnitObj.referenceName}}
										</div>
										<div class="row">
											<div class="col-xs-6">
												<strong>Communication Channel:</strong> {{completeUnitObj.channel}}
											</div>
										</div>

									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>No of Electricity Meter:</strong> {{completeUnitObj.noOfMeter}}
										</div>
										<div class="col-xs-6">
											<strong>DG  Meter Available</strong> {{completeUnitObj.dGAvailable}}
										</div>
									</div>
									<div class="row">
									<div class="col-xs-12">
											<strong>Business Type:</strong> {{completeUnitObj.unitBusinessType}}
									</div>
									</div>
									<h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Unit Address
										Details</h4>
									<div class="row">
										<div class="col-xs-6">
											<strong>Address Type:</strong> {{completeUnitObj.address.addressType}}
										</div>
										<div class="col-xs-6">
											<strong>Line 1:</strong> {{completeUnitObj.address.line1}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>Line 2:</strong> {{completeUnitObj.address.line2}}
										</div>
										<div class="col-xs-6">
											<strong>Line 3:</strong> {{completeUnitObj.address.line3}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>City:</strong> {{completeUnitObj.address.city}}
										</div>
										<div class="col-xs-6">
											<strong>State:</strong> {{completeUnitObj.address.state}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>ZipCode:</strong> {{completeUnitObj.address.zipCode}}
										</div>
										<div class="col-xs-6">
											<strong>Country:</strong> {{completeUnitObj.address.country}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>Contact 1:</strong> {{completeUnitObj.address.contact1}}
										</div>
										<div class="col-xs-6">
											<strong>Contact 2:</strong> {{completeUnitObj.address.contact2}}
										</div>
									</div>
									<div class="row">
										<div class="col-xs-6">
											<strong>Latitude:</strong> {{completeUnitObj.address.latitude}}
										</div>
										<div class="col-xs-6">
											<strong>Longitude:</strong> {{completeUnitObj.address.longitude}}
										</div>
									</div>
									<h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Delivery Partners</h4>
									<div class="row">
										<div class="col-xs-6" ng-repeat="addedPartnersDatas in addedPartners">
											<strong>{{addedPartnersDatas.detail.name}} </strong> : priority <strong>
												{{addedPartnersDatas.priority}}</strong>
										</div>
									</div>
								</div>
								<div ng-if="unitAction=='add'">
									<h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Product Pricing
										cloning unit</h4>
									<div class="row">
										<div class="col-xs-6">
											<strong>Unit name:</strong> {{newUnitProductPricing.name}}
										</div>
									</div>
								</div>
								<div>
									<h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Manager</h4>
									<div class="row">
										<div class="col-xs-6">
											<strong>Manager name:</strong> {{completeUnitObj.unitManager.name}}
										</div>
									</div>
								</div>
								<div>
									<h4 style="background-color: #C0C0C0; padding: 17px 7px 12px 11px; border-radius: 20px">Location</h4>
									<div class="row">
										<div class="col-xs-3">
											<strong>Country:</strong> {{selectedAddressCountry.name}}
										</div>
										<div class="col-xs-3">
											<strong>State:</strong> {{selectedAddressState.name}}
										</div>
										<div class="col-xs-3">
											<strong>Location:</strong> {{completeUnitObj.location.name}}
										</div>
										<div class="col-xs-3">
											<strong>Code:</strong> {{completeUnitObj.location.code}}
										</div>
									</div>
								</div>
								<br>
								<div class="row">
									<div class="col-xs-10" style="font-size: 11px">
										<button type="button" class="btn btn-primary" data-toggle="collapse" data-target="#demo">Business
											Hours Detail</button>
										<div id="demo" class="collapse">
											<table class="table table-striped table-bordered">
												<tr>
													<td>Shift</td>
													<td>Days</td>
													<td>Dine In</td>
													<td>Cod</td>
													<td>Take Away</td>
													<td>Hand Over</td>
												</tr>
												<!-- <tr><td>{{completeUnitObj.operationalHours}}</td></tr> -->
												<tr ng-repeat="operationHour in completeUnitObj.operationalHours">
													<td>{{operationHour.noOfShifts}}</td>
													<td>{{operationHour.dayOfTheWeek}}</td>
													<td><b>Start Date </b> : {{operationHour.dineInOpeningTime}} <b>End Date :</b>
														{{operationHour.dineInClosingTime}}</td>
													<td><b>Start Date :</b> {{operationHour.deliveryOpeningTime}} <b>End Date : </b>
														{{operationHour.deliveryClosingTime}}</td>
													<td><b>Start Date :</b> {{operationHour.takeAwayOpeningTime}} <b>End Date : </b>
														{{operationHour.takeAwayClosingTime}}</td>
													<td>{{operationHour.shiftOneHandoverTime}}</td>
												</tr>
											</table>
										</div>
									</div>
								</div>
								<div class="row" style="margin-left: 10px">
									<div class="row" style="margin-right: 10px">
										<div class="col-xs-12" ng-hide="showfarebut2">
											<a href="javascript:;" ng-if="addUnitBtn" class="btn btn-primary pull-right" ng-click="addNewUnit()">Add
												Unit</a> <a href="javascript:;" ng-if="editUnitBtn" class="btn btn-primary pull-right"
												ng-click="editExistingUnit()">Update Unit</a>
										</div>
										<loading style align="center" />
									</div>
									<button class="btn btn-primary" type="button" ng-if="unitAction=='add'" ng-click="selectTab('tab10')">Prev
									</button>
									<button class="btn btn-primary" type="button" ng-if="unitAction=='edit'" ng-click="selectTab('tab10')">Prev
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
