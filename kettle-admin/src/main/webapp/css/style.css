/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

@charset "utf-8";
/* CSS Document */

* {
    outline: none;
}

.nav, .pagination, .carousel, .panel-title a {
    cursor: pointer;
    outline: none;
}

button.close {
    padding: 10px 15px;
}

.accordion-toggle {
    display: block;
    padding: 10px 15px;
}

.panel-group .panel-heading {
    padding: 0;
}

.panel-group .panel-title button {
    background: none;
    padding: 10px 15px;
    display: block;
    border: none;
    width: 100%;
    text-align: left;
}

#employeeModal .ng-invalid.ng-dirty {
    border-color: #F00;
}

#employeeModal .ng-valid.ng-dirty {
    border-color: #060;
}

/***************** login page style *********************/
.loginBack {
    /*background-color:#50773e;*/
    background-size: cover;
    position: fixed;
    width: 100%;
    height: 100%;
}

.responseBox {
    width: 400px;
    margin: auto;
    margin-top: 100px;
    height: 50px;
}

.alert-success {
    color: #FFFFFF;
    background-color: #237900;
    border-color: #1E4200;
}

.loginBox {
    width: 400px;
    margin: auto;
    border-radius: 2px;
    border: #DDD 1px solid;
    box-shadow: #CCC 0 0 8px 0;
    padding: 20px;
    text-align: center;
    background: rgba(246, 246, 246, 0.78);
}

.loginDisabled {
    cursor: no-drop;
    pointer-events: none;
}

.responseBox .alert {
    padding: 10px;
}

/***************** unit page styles *********************/
.title {
    font-size: 24px;
}

.nav-tabs li a {
    pointer-events: none;
}

.nav-tabs li.disabled {
    cursor: no-drop;
}

.redBg {
    background: #d47070 !important;
}

.orangeBg {
    background: #f0ad4e !important;
}

.greenBg {
    background: #73c173 !important;
}

.error {
    color: #FF0000;
}

.fullScreenLoader {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background: rgba(255, 255, 255, .8);
    z-index: 99999999999999;
}

.fullScreenLoader img {
    margin-top: 10%;
}

.ng-cloak {
    display: none !important;
}

.previewModal .modal-dialog {
    position: fixed;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
}

.previewModal .modal-content {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0;
}

.previewModal .modal-body {
    position: absolute;
    left: 0;
    right: 0;
    top: 56px;
    bottom: 0;
    overflow: auto;
}

.rTable {
    display: table;
    width: 100%;
}

.rTableRow {
    display: table-row;
}

.rTableHeading {
    display: table-header-group;
    background-color: #ddd;
}

.rTableCell {
    display: table-cell;
    padding: 3px 10px;
    border: 1px solid #999999;
}

.rTableHead {
    display: table-cell;
    padding: 3px 10px;
    background-color: #DDD;
    border: 1px solid #999999;
}

.rTableHeading {
    display: table-header-group;
    background-color: #ddd;
    font-weight: bold;
}

.rTableFoot {
    display: table-footer-group;
    font-weight: bold;
    background-color: #ddd;
}

.rTableBody {
    display: table-row-group;
}

.top-buffer-row {
    margin-top: 20px;
}

.recipe-name {
    cursor: pointer;
    font-weight: 800;
    color: indigo;
    text-decoration: underline;
}

.specialInstrLabel {
    padding: 2px 5px;
    border: #ccc 1px solid;
    border-radius: 4px;
    background: #ddd;
    margin-right: 5px;
}

.margin-bottom10 {
    margin-bottom: 10px;
}

.yellowBg {
    background: yellow;
}

.partnerCategoryPicker {
    height: 250px;
    border: #000 1px solid;
    border-radius: 3px;
    overflow: auto;
    list-style: none;
}

.partnerCategoryPicker li {
    margin-left: -40px;
    padding: 8px;
    cursor: pointer;
}

.partnerCategoryPicker li:hover {
    background: #ddd;
}

.partnerCategoryPicker li.selected {
    background: green;
    color: #fff;
}

.groupListing, .catBody {
    border: #ccc 1px solid;
    border-radius: 3px;
    min-height: 50px;
}

.groupListing .list {
    border-bottom: #ccc 1px solid;
    padding: 10px;
}

.groupListing .catList {
    margin: 5px;
    border: #ccc 1px solid;
    border-radius: 3px;
}

.groupListing .catList .catHead {
    padding: 10px;
    color: #fff;
    background: #1E4200;
}

.groupListing .catList .catBody {
    margin: 10px;
    min-height: 20px;
}

.rowSelected {
    background: #aef1b0;
}

.overflow {
    overflow-y: auto;
    max-height: 700px;
}
.partnerEdcMappingContainer{
    width: 60%;
    position: sticky;
    left: 30%;
}