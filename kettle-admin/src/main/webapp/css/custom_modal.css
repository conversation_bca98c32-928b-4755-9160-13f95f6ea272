/* Modal Styles */
.c-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.c-modal-overlay.show {
  opacity: 1;
  pointer-events: auto; /* Re-enable pointer events when shown */
}

/*.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}*/

.c-modal-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-width: 95vw;
  max-height: 95vh;
  position: relative;
  overflow: hidden;
}

/* Animation for modal appearance */
.c-animated-pop {
  transform: scale(0.9);
  opacity: 0;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
              opacity 0.3s ease;
}

.c-modal-overlay.show .c-animated-pop {
  transform: scale(1);
  opacity: 1;
}

.c-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  position: relative;
}

.c-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
  /* No need for flex positioning as the parent will center it */
}

/*.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}*/

.c-modal-close {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #777;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s;
}

.c-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.c-modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.c-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f9fa;
}

/* Button styles */
.c-btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s, transform 0.1s;
}

.c-btn:active {
  transform: translateY(1px);
}

.c-btn-primary {
  background-color: #007bff;
  color: white;
}

.c-btn-primary:hover {
  background-color: #0069d9;
}

.c-btn-secondary {
  background-color: #f53c2f;
  color: white;
}

.c-btn-secondary:hover {
  background-color: #f72a1b;
}

/* Scrollable table styles */
.scrollable-table {
  width: 100%;
  border-collapse: collapse;
}

.scrollable-table th,
.scrollable-table td {
  padding: 10px 15px;
  text-align: left;
  border-bottom: 1px solid #e5e5e5;
}

.scrollable-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.scrollable-table input[type="number"],
.scrollable-table input[type="text"],
.form-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out;
}

.scrollable-table input[type="number"]:focus,
.scrollable-table input[type="text"]:focus,
.form-input:focus {
  border-color: #80bdff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-container {
    width: 95% !important;
    height: 80% !important;
  }

  .scrollable-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}