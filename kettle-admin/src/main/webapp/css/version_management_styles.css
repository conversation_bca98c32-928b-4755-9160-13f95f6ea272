/* Version Management UI Variables */
:root {
  --primary-color: #57ab64;
  --secondary-color: #34c3ff;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --text-color: #333;
  --light-text: #777;
  --background-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e1e5eb;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
  --header-bg: #f9fafc;
  --filter-input-height: 28px;
}

/* General Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

.app-container {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 1rem;
  flex: 1;
}

/* Header Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: fadeInDown 0.8s;
  background-color: var(--primary-color);
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.logo i {
  font-size: 2.5rem;
  color: white;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.actions {
  display: flex;
  gap: 1rem;
  animation: fadeInRight 0.8s;
}

/* Button Styles */
.bttn {
  padding: 0.7rem 1.2rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.bttn i {
  font-size: 1rem;
}

.btn-pri {
  background-color: var(--primary-color);
  color: white;
}

.btn-pri:hover {
  background-color: #57ab64;
  transform: translateY(-2px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #27ae60;
  transform: translateY(-2px);
}

/* Filters Section */
.filters-section {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 1.5rem;
  margin-bottom: 2rem;
  animation: fadeIn 0.8s;
}

/* Filters Header */
.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.2rem;
}

.filters-section h2 {
  font-size: 1.5rem;
  margin-bottom: 0;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-section h2 i {
  color: var(--secondary-color);


}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.filter-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 250px;
  margin-right: 20px;
  margin-bottom: 5px;
}

.filter-group:last-child {
  margin-right: 0;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--light-text);
}

.form-control {
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color var(--transition-speed);
  gap:10px;
}

.form-control:focus {
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 195, 255, 0.2);
}

.filter-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

/* Results Section */
.results-section {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 1.5rem;
  animation: fadeIn 0.8s;
}

/* Results Header */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.2rem;
  flex-wrap: wrap;
}

.results-section h2 {
  font-size: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.results-section h2 i {
  color: var(--secondary-color);
}

.units-stats {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.units-count {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  background-color: white;
  color:red;
  border-radius: 20px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--primary-color);
}

.units-count i {
  margin-right: 5px;
}

/* Table Container */
.table-responsive {
  overflow-x: auto;
  margin-bottom: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  animation: fadeIn 0.8s;
}

/* Data Table Styling */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

/* Header Row */
.header-row {
  background-color: var(--background-color);
}

.header-row th {
  padding: 12px 15px;
  border-bottom: 2px solid var(--border-color);
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  vertical-align: top;
}

/* Header Content and Filters */
.header-content {
  display: flex;
  flex-direction: column;
  padding: 0;
}

.column-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
  display: block;
  font-size: 1.3rem;
}

.header-filter {
  position: relative;
  margin-top: 5px;
}

.header-filter input[type="text"] {
  width: 100%;
  padding: 6px 25px 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.85rem;
  background-color: white;
  transition: all var(--transition-speed);
}

.header-filter input[type="text"]:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 107, 253, 0.2);
}

.header-filter i.fas.fa-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--light-text);
}

/* Table Body Rows */
.data-table tbody tr td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  font-size: 1.2rem;
  text-align: left;
}

/* Row States */
.row-selected {
  background-color: #e8f0fe !important;
}

.row-changed {
  background-color: #fff8e6 !important;
}

.field-changed {
  background-color: #e6ffe6;
  font-weight: 600;
}

/* App Selection Bar */
.app-selection-bar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  width: 100%;
  background-color: var(--card-bg);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.app-selection-item {
  display: flex;
  align-items: center;
  background-color: var(--background-color);
  padding: 8px 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px var(--shadow-color);
  margin: 5px;
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.app-selection-item label {
  font-weight: 600;
  margin-right: 10px;
  margin-bottom: 0;
  min-width: 100px;
}

.app-selection-item input[type="checkbox"] {
  margin-left: 15px;
}

/* Animation */
.animate__animated {
  animation-duration: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate__fadeIn {
  animation-name: fadeIn;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group {
    min-width: auto;
  }
}

/* Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Footer Bar */
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 15px 30px;
  background-color:white;
  box-shadow: 0 -2px 10px var(--shadow-color);
  display: flex;
  justify-content: flex-end;
  z-index: 100;
}

.footer-bar .bttn {
  margin: auto;
}

.footer-bar .btn-update-all {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 10px 20px;
  font-size: 1rem;
}

.footer-bar .bttn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Add padding to the bottom of the results section to prevent content from being hidden behind the footer */
.results-section {
  margin-bottom: 70px;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 10px 0;
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  font-size: 0.9rem;
  color: var(--light-text);
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-btn {
  min-width: 35px;
  height: 35px;
  border: 1px solid var(--border-color);
  background-color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.pagination-btn:hover:not(.disabled):not(.active) {
  background-color: var(--background-color);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn i {
  font-size: 0.8rem;
}

.update-all-btn {
  margin-left: 20px;
}

/* Multi-select dropdown */
.multi-select .dropdown-menu {
  max-width: 300px !important;
  overflow-x: auto;
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  max-height: 300px;
  z-index: 9999;
  position: absolute;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Fix for dropdown positioning */
.multi-select {
  position: relative;
  width: 100%;
}

/* Dropdown button styling */
.multi-select button {
  width: 100%;
  text-align: left;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 0.95rem;
  color: var(--text-color);
  transition: all var(--transition-speed);
}

.multi-select button:hover,
.multi-select button:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 107, 253, 0.1);
}

.multi-select .dropdown-menu li {
  padding: 4px 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.multi-select .dropdown-menu li:hover {
  background-color: #f5f7fa;
}

/* Ensure dropdowns appear above other elements */
.filters-section {
  position: relative;
  z-index: 200;
}

.app-selection-bar {
  position: relative;
  z-index: 150;
}

.results-section {
  position: relative;
  z-index: 100;
}

/* Units count badge */
.units-count {
  display: inline-block;
  padding: 5px 10px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

/* Checkbox Styling */
.checkbox-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
}

.checkbox-cell input[type="checkbox"]{
    height:18px;
    width:18px;
}



.checkbox-header input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.checkbox-header span {
  font-weight: 600;
  cursor: pointer;
}
