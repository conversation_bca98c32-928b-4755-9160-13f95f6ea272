/* Product Management UI Variables */
:root {
  --primary-color: #4a6bfd;
  --secondary-color: #34c3ff;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --text-color: #333;
  --light-text: #777;
  --background-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e1e5eb;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
  --header-bg: #f9fafc;
  --filter-input-height: 28px;
}

/* General Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-container {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 1rem;
  flex: 1;
}

/* Header Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: fadeInDown 0.8s;
}

.logo i {
  font-size: 2.5rem;
  color: var(--primary-color);
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-color);
}

.actions {
  display: flex;
  gap: 1rem;
  animation: fadeInRight 0.8s;
}

/* Button Styles */
.bttn {
  padding: 0.7rem 1.2rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.bttn i {
  font-size: 1rem;
}

.btn-pri {
  background-color: var(--primary-color);
  color: white;
}

.btn-pri:hover {
  background-color: #3555e0;
  transform: translateY(-2px);
}

.btn-upload-excel {
  background-color: #074f8a;
  color: #fff;
  border: 1px solid var(--border-color);
}

.btn-upload-excel:hover {
  background-color: #f5f5f5;
  transform: translateY(-2px);
  color: #000
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #27ae60;
  transform: translateY(-2px);
}

/* File Upload Styling */
.file-upload {
  position: relative;
}

.file-upload input[type="file"] {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

/* Filters Section */
.filters-section {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 1.5rem;
  margin-bottom: 2rem;
  animation: fadeIn 0.8s;
}

.filters-section h2 {
  font-size: 1.3rem;
  margin-bottom: 1.2rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-section h2 i {
  color: var(--secondary-color);
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.filter-row {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 250px;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--light-text);
}

.form-control {
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color var(--transition-speed);
}

.form-control:focus {
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 195, 255, 0.2);
}

.filter-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

/* Results Section */
.results-section {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 1.5rem;
  animation: fadeIn 0.8s;
}

.results-section h2 {
  font-size: 1.3rem;
  margin-bottom: 1.2rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.results-section h2 i {
  color: var(--secondary-color);
}

/* Table Container */
.table-responsive {
  overflow-x: visible;
  margin-bottom: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  animation: fadeIn 0.8s;
}

/* Data Table Styling */
.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: visible;
}

/* Header Row */
.header-row {
  background-color: var(--background-color);
}

.header-row th {
  padding: 0;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid var(--border-color);
  text-align: left;
  vertical-align: top;
}

.header-content {
  display: flex;
  flex-direction: column;
  padding: 10px 8px;
}

.column-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
  display: block;
  font-size: 1rem;
  color: var(--light-text);
}

.header-filter {
  position: relative;
  margin-top: 5px;
}

.header-filter input[type="text"] {
  width: 100%;
  padding: 6px 25px 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.85rem;
  background-color: white;
  transition: all var(--transition-speed);
}

.header-filter input[type="text"]:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 107, 253, 0.2);
}

.header-filter i.fas.fa-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--light-text);
}

/* Header Dropdown Filters */
.header-filter.dropdown select {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: all var(--transition-speed);
}

.header-filter.dropdown select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 107, 253, 0.2);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
  margin-top: 5px;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: var(--text-color);
  cursor: pointer;
  transition: all var(--transition-speed);
}

.action-btn:hover {
  background-color: #e0e0e0;
}

.action-btn.apply {
  background-color: var(--success-color);
  color: white;
}

.action-btn.apply:hover {
  background-color: #27ae60;
}

.action-btn.info {
  background-color: var(--danger-color);
  color: white;
}

.action-btn.info:hover {
  background-color: var(--warning-color);
}

/* Table Body Rows */
.data-table tbody tr td {
  padding: 12px 8px;
  border-bottom: 1px solid var(--border-color);
  font-size: 1.3rem;
  text-align: left;
}

/* Row States */
.row-selected {
  background-color: #e8f0fe;
}

.row-changed {
  background-color: #c8f4fe;
}

.field-changed {
  background-color: #2596be;
}

.row-profile-dd-null {
  background-color: #fff8e1;
}

.row-not-modified-but-selected {
  background-color: #f0fff0;
}

.row-profile-null-selected {
    background-color: #e28743;
}

.filter-applied {
    border-color: var(--warning-color);
    border-width: 1.5px;
    background-color: var(--warning-color);
    font-size: 14px;
    border-radius: 5px;
}

/* Checkbox Styling */
.checkbox-header {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px;
}

.checkbox-header input[type="checkbox"] {
  /*display: none;*/
}

.checkbox-header label {
  cursor: pointer;
}

.checkbox-container {
  position: relative;
  display: inline-block;
}

.checkbox-container input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-container label {
  position: relative;
  display: inline-block;
  padding-left: 25px;
  margin-bottom: 0;
  cursor: pointer;
}

.checkbox-container label:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--transition-speed);
}

.checkbox-container input[type="checkbox"]:checked+label {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked+label:after {
  content: '';
  position: absolute;
  top: 3px;
  left: 7px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Form controls within table */
.form-control {
  width: 100%;
  padding: 0.5rem 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: all var(--transition-speed);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 107, 253, 0.2);
}

/* Animation */
.animate__animated {
  animation-duration: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.animate__fadeIn {
  animation-name: fadeIn;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .header-row th {
    position: relative;
  }

  .header-content {
    padding: 8px 4px;
  }

  .column-title {
    font-size: 1rem;
  }

  .data-table tbody tr td {
    padding: 8px 4px;
    font-size: 0.85rem;
  }

  .filter-row {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group {
    min-width: auto;
  }
}

/* Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Additional Styles */
.file-upload {
  position: relative;
  overflow: hidden;
}

.file-upload input[type="file"] {
  position: absolute;
  font-size: 100px;
  opacity: 0;
  right: 0;
  top: 0;
}

.multi-select .dropdown-menu {
    max-width: 300px !important; /* Ensure max width */
    overflow-x: auto; /* Enable horizontal scroll if needed */
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    max-height: 300px;
    z-index: 9999;
    position: absolute;
}

.record-summary {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 3px;
    border-radius: 7px;
    margin-bottom: 2px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    color: green;
    font-family: 'Arial', sans-serif;
}

.record-item {
    font-size: 14px;
    display: flex;
    align-items: center;
}

.record-item strong {
    font-weight: bold;
}

button.export-selected {
    background-color: #2196F3;
    color: white;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin: 5px;
}

button.export-selected:hover {
    background-color: #1976D2;
}

button.export-selected:active {
    background-color: #1565C0;
}

button.export-all {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin: 5px;
}

button.export-all:hover {
    background-color: #45A049;
}

button.export-all:active {
    background-color: #388E3C;
}

button.export-all:disabled,
button.export-selected:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.msg-container {
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
    border: 1px solid transparent;
}

.success-msg {
    background-color: #dff0d8;
    border-color: #d0e9c6;
    color: #3c763d;
}

.success-msg h4 {
    color: #3c763d;
    margin-top: 0;
}

.failure-msg {
    background-color: #f2dede;
    border-color: #ebcccc;
    color: #a94442;
}

.failure-msg h4 {
    color: #a94442;
    margin-top: 0;
}

.default-msg {
    background-color: #d9edf7;
    border-color: #bcdff1;
    color: #31708f;
}

.default-msg h4 {
    color: #31708f;
    margin-top: 0;
}

.msg-list {
    padding-left: 20px;
    margin: 10px 0 0 0;
}

.msg-item {
    margin: 5px 0;
    padding: 3px 0;
}

/* Toggle slider */
.r-toggle-switch {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

.r-toggle-switch input {
  display: none;
}

.r-slider {
  width: 34px;
  height: 18px;
  background-color: #ccc;
  border-radius: 9px;
  position: relative;
  transition: 0.2s;
  display: inline-block;
  margin-right: 8px;
}

.r-slider:before {
  content: "";
  position: absolute;
  width: 14px;
  height: 14px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: 0.2s;
}

input:checked + .r-slider {
  background-color: #4CAF50;
}

input:checked + .r-slider:before {
  transform: translateX(16px);
}