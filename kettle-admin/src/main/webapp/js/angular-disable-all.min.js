!function(){"use strict";function e(){return{restrict:"A"}}function t(){return{restrict:"A",link:function(e,t,n){var i=n.disableElementId?document.getElementById(n.disableElementId):t[0];e.$watch(n.disableAll,function(e){e?a(i):l(i)}),e.$on("$destroy",function(){l(i)})}}}angular.module("disableAll",[]),angular.module("disableAll").directive("skipDisable",e),angular.module("disableAll").directive("disableAll",t);var a=function(e){angular.element(e).addClass("disable-all"),e.style.color="gray",i(e.getElementsByTagName("input")),i(e.getElementsByTagName("button")),i(e.getElementsByTagName("textarea")),i(e.getElementsByTagName("select")),e.addEventListener("click",n,!0)},l=function(e){angular.element(e).removeClass("disable-all"),e.style.color="inherit",r(e.getElementsByTagName("input")),r(e.getElementsByTagName("button")),r(e.getElementsByTagName("textarea")),r(e.getElementsByTagName("select")),e.removeEventListener("click",n,!0)},n=function(e){for(var t=0;t<e.target.attributes.length;t++){var a=e.target.attributes[t];if("skip-disable"===a.name)return!0}return e.stopPropagation(),e.preventDefault(),!1},i=function(e){for(var t=e.length,a=0;t>a;a++){for(var l=!0,n=0;n<e[a].attributes.length;n++){var i=e[a].attributes[n];"skip-disable"!==i.name||(l=!1)}l&&e[a].disabled===!1&&(e[a].disabled=!0,e[a].disabledIf=!0)}},r=function(e){for(var t=e.length,a=0;t>a;a++)e[a].disabled===!0&&e[a].disabledIf===!0&&(e[a].disabled=!1,e[a].disabledIf=null)}}();