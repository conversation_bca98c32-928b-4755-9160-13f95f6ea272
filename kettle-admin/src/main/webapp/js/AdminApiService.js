
// Admin API Service is for a central API handling point

adminapp.service('AdminApiService', ['$http', '$q', '$rootScope', 'AppUtil', 'toastService',
	function ($http, $q, $rootScope, AppUtil, toastService) {

		var defaultErrorHandler = function (error) {
			console.log("API Error:", error);
			var errorMessage = "Error while processing your request";

			if (!navigator.onLine) {
				errorMessage = "No internet connection. Please check your network.";
			} else if (error && error.status === 400) {
				errorMessage = "Bad request. Please check your input data.";
			} else if (error && error.status === 401) {
				errorMessage = "Unauthorized. Please login again.";
			} else if (error && error.status === 403) {
				errorMessage = "Forbidden. You don't have permission to access this resource.";
			} else if (error && error.status === 404) {
				errorMessage = "Method not found (404). Please contact support.";
			} else if (error && error.status === 408) {
				errorMessage = "Request timeout. Please try again.";
			} else if (error && error.status === 429) {
				errorMessage = "Too many requests. Please try again later.";
			} else if (error && error.status === 500) {
				errorMessage = "Internal server error , please try after some time.";
			} else if (error && error.status === 502) {
				errorMessage = "Bad gateway. Service temporarily unavailable.";
			} else if (error && error.status === 503) {
				errorMessage = "Service unavailable. Please try again later.";
			} else if (error && error.data && error.data.message) {
				errorMessage = error.data.message;
			} else if (error && error.statusText) {
				errorMessage = error.statusText;
			}
			console.error("Error Message:", errorMessage);
			toastService.error(errorMessage);
			return $q.reject(error);
		};

		var checkForErrorsAndHandle = function(deferred) {
			var responseData = deferred.promise;
			if( !AppUtil.isEmptyObject(responseData.error) ) {
				toastService.error("Error : " + responseData.error);
			} else if( !AppUtil.isEmptyObject(responseData.errorMsg) ) {
				toastService.error("Error : " + responseData.errorMsg);
			} else if( !AppUtil.isEmptyObject(responseData.errorMessage) ) {
				toastService.error("Error : " + responseData.errorMessage);
			} else {
				return responseData;
			}
			return $q.reject(responseData);
		}


		this.request = function (config) {
			var defaultConfig = {
				method: 'GET',
				url: '',
				data: null,
				params: null,
				headers: null,
				showLoader: true,
				suppressErrorToast: false,
				responseType: null,
				transformRequest: null
			};

			config = angular.extend({}, defaultConfig, config);

			if (config.showLoader) {
				$rootScope.showFullScreenLoader = true;
			}

			var requestConfig = {
				method: config.method,
				url: config.url
			};

			if (config.data && (config.method === 'POST' || config.method === 'PUT')) {
				requestConfig.data = config.data;
			}

			if (config.params) {
				requestConfig.params = config.params;
			}

			if (config.headers) {
				requestConfig.headers = config.headers;
			}
			if (config.responseType) {
				requestConfig.responseType = config.responseType;
			}
			if (config.transformRequest) {
				requestConfig.transformRequest = config.transformRequest;
			}

			var deferred = $q.defer();

			if (!navigator.onLine) {
				if (config.showLoader) {
					$rootScope.showFullScreenLoader = false;
				}
				defaultErrorHandler({ status: 0, statusText: "Offline" });
				deferred.reject({ status: 0, statusText: "Offline" });
				return deferred.promise;
			}

			$http(requestConfig)
				.then(function success(response) {
					if (config.showLoader) {
						$rootScope.showFullScreenLoader = false;
					}

					if (response.status === 404) {
						defaultErrorHandler(response);
						deferred.reject(response);
					} else if (response.status === 0) {
						defaultErrorHandler(response);
						deferred.reject(response);
					} else if(response.status === 500) {
						defaultErrorHandler(response);
						deferred.reject(response);
					}

					deferred.resolve(response.data);
				}, function error(response) {
					if (config.showLoader) {
						$rootScope.showFullScreenLoader = false;
					}

					if (!config.suppressErrorToast) {
						defaultErrorHandler(response);
					}

					deferred.reject(response);
				});
			if (!config.suppressErrorToast) {
				return checkForErrorsAndHandle(deferred);
			}
			return deferred.promise;
		};



		// GET Method
		this.get = function (url, params, options) {
			options = options || {};
			return this.request({
				method: 'GET',
				url: url,
				params: params,
				suppressErrorToast: options.byPassError,
				showLoader: options.showLoader,
				responseType: options.responseType,
				headers: options.headers,
				transformRequest: options.transformRequest,
			});
		};

		// POST Method
		this.post = function (url, data, params, options) {
			options = options || {};
			return this.request({
				method: 'POST',
				url: url,
				data: data,
				params: params,
				suppressErrorToast: options.byPassError,
				showLoader: options.showLoader || true,
				responseType: options.responseType,
				transformRequest: options.transformRequest,
			});
		};

		// PUT Method
		this.put = function (url, data, params, options) {
			options = options || {};
			return this.request({
				method: 'PUT',
				url: url,
				data: data,
				params: params,
				suppressErrorToast: options.byPassError,
				showLoader: options.showLoader || true,
				responseType: options.responseType,
				transformRequest: options.transformRequest,
			});
		};

		// PATCH
		this.patch = function (url, data, params, options) {
			options = options || {};
			return this.request({
				method: 'PATCH',
				url: url,
				data: data,
				params: params,
				suppressErrorToast: options.byPassError,
				showLoader: options.showLoader || true,
				responseType: options.responseType,
				transformRequest: options.transformRequest,
			});
		};

		// DELETE Method
		this.delete = function (url, params, options) {
			options = options || {};
			return this.request({
				method: 'DELETE',
				url: url,
				params: params,
				suppressErrorToast: options.byPassError,
				showLoader: options.showLoader || true,
				responseType: options.responseType,
				transformRequest: options.transformRequest,
			});
		};

	}]
);
