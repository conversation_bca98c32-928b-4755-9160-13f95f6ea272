/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.service('AppUtil', ['$cookieStore', 'APIJson', '$http', '$location','$rootScope', function ($cookieStore, APIJson, $http, $location,$rootScope) {

    var service = {};

    var BRAND_IDS = {
        1: "CHAAYOS",
        2: "DESI_CANTEEN",
        3: "GNT",
        4: "AHA",
        5: "SWIGGY_CAFE",
        6: "DOHFUL"
    };
    
    var COMPANY_IDS = {
        "CHAAYOS": 1000,
        "GNT": 1000,
        "DOHFUL": 1005
    };

    service.mimeTypes = {
        PDF: "application/pdf",
        XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        XLS: "application/vnd.ms-excel",
        JPG: "image/jpg",
        JPEG: "image/jpeg",
        PNG: "image/png",
        CSV: "text/plain",
        TXT: "text/plain"
    };
    service.CHAAYOS_BRAND_ID = 1;
    service.GetRequest = GetRequest;
    service.restUrls = APIJson.urls;
    service.setPermissions = setPermissions;
    service.permissions = [];
    service.getPermissions = getPermissions;
    service.checkPermission = checkPermission;
    service.setAcl = setAcl;
    service.acl = null;
    service.getAcl = getAcl;
    service.setUserValues=setUserValues
    service.userValues=null;
    service.getUserValues=getUserValues;
    service.setUserData = setUserData;
    service.userData = null;
    service.getUserData = getUserData;
    service.isValid = isValid;
    service.getCurrentUser = getCurrentUser;
    service.JSONToCSVConvertor = JSONToCSVConvertor;
    service.getIdCodeName = getIdCodeName;
    service.outletList = [];
    service.getUnitList = getUnitList;
    service.formatDate = formatDate;
    service.getCurrentFormattedDate = getCurrentFormattedDate;
    service.getCurrentDate = getCurrentDate;
    service.getDate = getDate;
    service.addDate = addDate;
    service.getBusinessDate = getBusinessDate;
    service.addToBusinessDate = addToBusinessDate;
    service.currentBusinessDate = null;
    service.CSVToJSONConverter = CSVToJSONConverter;
    service.companyId = 1000;
    service.cafe = "CAFE";
    service.getEnvType = getEnvType;
    service.getImageUrl = getImageUrl;
    service.isTakeaway = checkTakeaway;
    service.unitFamily = getUnitFamily();
    service.isCOD = checkIsCOD;
    service.getFileExtension = getFileExtension;
    service.getDesiChaiAddonsOrderForCOD = getDesiChaiAddonsOrderForCOD;
    service.getProductIdsForSplitDimension = getProductIdsForSplitDimension;
    service.splitDimensionProductIds = [];
    service.BRAND_IDS = BRAND_IDS;
    service.COMPANY_IDS = COMPANY_IDS;
    service.ListTypes = getListTypes;

    function getListTypes(callback) {
        $http({
            method: 'GET',
            url: service.restUrls.unitMetaData.listTypes
        }).then(function success(response) {
            if (callback && typeof callback === 'function') {
                callback(response.data);
            }
        }, function error(response) {
            console.error("Error fetching list types:", response);
        });
    }

    function getDesiChaiAddonsOrderForCOD(){
       return {
           '969' : 0,
           '972' : 1,
           '986'  :2,
           '981' : 3,
           '987' : 4,
           '971'  : 5,
           '980' : 6,
           '978' : 7 ,
           '1345' : 8,
           '983' : 9,
           '982' : 10,
           '970'  :11,
           '974' : 12
        }
    }

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }



    service.isEmptyObject = function (obj) {
        if (obj != undefined && obj != null) {
            if (typeof obj == 'string' || typeof obj == 'number')
                return obj.toString().length == 0;
            else
                return Object.keys(obj).length == 0;
        }
        return true;
    };

    function GetRequest(obj) {
        var requestObj = {};
        if (typeof obj != 'string') {
            requestObj = {
                session: JSON.parse(localStorage.getItem('userValues')).user,
                data: JSON.stringify(obj)
            };
        } else {
            requestObj = {

                session: JSON.parse(localStorage.getItem('userValues')).user,
                data: obj
            };
        }
        return requestObj;
    }

    function getPermissions() {
        if (this.permissions == null) {
            this.permissions = JSON.parse(localStorage.getItem("adminPermissions"));
        }
        return this.permissions;
    }

    function setPermissions(permissions) {
        this.permissions = permissions;
        localStorage.setItem("adminPermissions", JSON.stringify(permissions));
    }

    function checkPermission(permission) {
        var ret = false;
            if(JSON.parse(localStorage.getItem('userValues')).user!=null){
            var sessionKey = JSON.parse(localStorage.getItem('userValues')).sessionKeyId;
            var map = JSON.parse(localStorage.getItem("adminPermissions"));
            map = map[sessionKey];
            while (permission.length > 0) {
                // console.log(permission);
                if (permission in map) {
                    ret = true;
                    break;
                } else {
                    if (permission.indexOf(".*") != -1) {
                        permission = permission.substring(0, permission.lastIndexOf('.'));
                    }
                    if (permission.length != 0 && permission.indexOf(".") != -1) {
                        permission = permission.substring(0, permission.lastIndexOf('.'));
                        permission = permission + ".*";
                    } else {
                        break;
                    }
                }
            }
        }
        return ret;
    }

    function getAcl() {
        if (this.acl == null) {
         this.acl = JSON.parse(localStorage.getItem("acl"));
        }
        return this.acl;
    }

    function setAcl(acl) {
        this.acl = acl;
        localStorage.setItem("acl", JSON.stringify(acl));
    }
       function getUserValues() {
            if (this.userValues == null) {
                this.userValues = JSON.parse(localStorage.getItem("userValues"));
            }
            return this.userValues;
        }
        function setUserValues(userValues) {
            this.userValues = userValues;
            localStorage.setItem("userValues", JSON.stringify(userValues));
        }

    function getUserData() {
        if (this.userData == null) {
            this.userData = JSON.parse(localStorage.getItem("udata"));
        }
        return this.userData;
    }

    function checkIsCOD() {
        var unitFamily = getUnitFamily();
        if (unitFamily !== undefined) {
            return unitFamily === 'COD';
        }
        return false;

    }

    function setUserData(data) {
        this.userData = {
            id: data.id,
            name: data.name,
            department: data.department.name,
            designation: data.designation.name
        };
        localStorage.setItem("udata", JSON.stringify(this.userData));
    }

    function isValid(value) {
        return value != undefined && value != null && value !== "";
    }

    function getCurrentUser() {
        return JSON.parse(localStorage.getItem('userValues')).user;
    }

    function getIdCodeName(dataId, name, code) {
        var idCodeName = {
            id: dataId,
            code: (code === undefined ? null : code),
            name: (name === undefined ? null : name)
        };
        return idCodeName;
    }

    function getUnitList(callback) {
        if (service.outletList == null || service.outletList.length === 0) {
            $http({
                method: 'GET',
                url: service.restUrls.unitMetaData.allUnits + "?category=CAFE"
            }).then(function success(response) {
                if (service.outletList.length === 0) {
                    response.data.map(function (unit) {
                        if (unit.status === "ACTIVE") {
                            service.outletList.push(unit);
                        }
                    });
                } else {
                    angular.forEach(response.data, function (v) {
                        if (v.status === "ACTIVE") {
                            service.outletList.push(v);
                        }
                    });
                }
                if (typeof callback === "function") {
                    callback(service.outletList);
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        } else {
            if (typeof callback === "function") {
                callback(service.outletList);
            }
        }
    }


    function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {

        // If JSONData is not an object then JSON.parse will parse the JSON
        // string in an Object
        var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
        var CSV = '';
        // This condition will generate the Label/Header
        if (ShowLabel) {
            var row = "";

            // This loop will extract the label from 1st index of on array
            for (var index in arrData[0]) {
                // Now convert each value to string and comma-seprated
                row += index + ',';
            }
            row = row.slice(0, -1);
            // append Label row with line break
            CSV += row + '\r\n';
        }

        // 1st loop is to extract each row
        for (var i = 0; i < arrData.length; i++) {
            var row = "";
            // 2nd loop will extract each column and convert it in string
            // comma-seprated
            for (var index in arrData[i]) {
                row += '"' + arrData[i][index] + '",';
            }
            row.slice(0, row.length - 1);
            // add a line break after each row
            CSV += row + '\r\n';
        }

        if (CSV == '') {
            alert("Invalid data");
            return;
        }

        // this trick will generate a temp "a" tag
        var link = document.createElement("a");
        link.id = "lnkDwnldLnk";

        // this part will append the anchor tag and remove it after automatic
        // click
        document.body.appendChild(link);

        var csv = CSV;
        var blob = new Blob([csv], {
            type: 'text/csv'
        });
        var csvUrl = window.webkitURL.createObjectURL(blob);
        var filename = ReportTitle + '.csv';
        $("#lnkDwnldLnk").attr({
            'download': filename,
            'href': csvUrl
        });

        $('#lnkDwnldLnk')[0].click();
        document.body.removeChild(link);
    }

    function CSVToJSONConverter(csv_data) {
        var record = csv_data.split(/\r\n|\n/);
        var headers = record[0].split(',');
        var lines = [];
        var json = {};
        for (var i = 0; i < record.length; i++) {
            var data = record[i].split(',');
            if (data.length == headers.length) {
                var tarr = [];
                for (var j = 0; j < headers.length; j++) {
                    tarr.push(data[j]);
                }
                lines.push(tarr);
            }
        }

        for (var k = 0; k < lines.length; ++k) {
            json[k] = lines[k];
        }
        return json;
    }

    function formatDate(date, format) {
        let time = new Date(date);
        let yyyy = time.getFullYear();
        let M = time.getMonth() + 1;
        let d = time.getDate();
        let MM = M;
        const monthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        let dd = d;
        let hh = time.getHours();
        let mm = time.getMinutes();
        let ss = time.getSeconds();
        const A = hh < 12 ? "AM" : "PM";
        if (format.indexOf("A") > -1) {
            hh = (hh > 12) ? hh - 12 : hh;
        }
        MM = (M < 10) ? "0" + M : M;
        dd = (d < 10) ? "0" + d : d;
        hh = (hh < 10) ? "0" + hh : hh;
        mm = (mm < 10) ? "0" + mm : mm;
        format = format.replace("yyyy", yyyy);
        format = format.replace("MMM", monthList[M - 1]);
        format = format.replace("MM", MM);
        format = format.replace("dd", dd);
        format = format.replace("hh", hh);
        format = format.replace("mm", mm);
        format = format.replace("ss", ss);
        return format;
    }

    function getCurrentFormattedDate(date, format) {
        let time = new Date(date);
        let yyyy = time.getFullYear();
        let M = time.getMonth() + 1;
        let d = time.getDate();
        let MM = M;
        const monthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        let dd = d;
        let hh = time.getHours();
        let mm = time.getMinutes();
        let ss = time.getSeconds();
        const A = hh < 12 ? "AM" : "PM";
        if (format.indexOf("A") > -1) {
            hh = (hh > 12) ? hh - 12 : hh;
        }
        MM = (M < 10) ? "0" + M : M;
        dd = (d < 10) ? "0" + d : d;
        hh = (hh < 10) ? "0" + hh : hh;
        mm = (mm < 10) ? "0" + mm : mm;
        ss = (ss < 10) ? "0" + ss : ss;
        format = format.replace("yyyy", yyyy);
        format = format.replace("MMM", monthList[M - 1]);
        format = format.replace("MM", MM);
        format = format.replace("dd", dd);
        format = format.replace("hh", hh);
        format = format.replace("mm", mm);
        format = format.replace("ss", ss);
        return format;
    }

    function getCurrentDate(format) {
        var date = new Date();
        return date.getFullYear() + "-" + ("0" + date.getMonth()).slice(-2) + "-"
               + ("0" + date.getDate()).slice(-2) + " " + date.getHours() + ":"+ date.getMinutes() + ":" + date.getSeconds();
    }

    function addDate(date, daysToAdd) {
        var time = new Date(date);
        var d = time.getDate();
        d = d + daysToAdd;
        time.setDate(d);
        return new Date(time);
    }

    function addToBusinessDate(callback, daysToAdd) {
        if (service.currentBusinessDate != null) {
            if (callback != null && typeof callback == "function") {
                callback(service.addDate(service.currentBusinessDate, daysToAdd));
            }
        } else {
            if (callback != null && typeof callback == "function") {
                service.getBusinessDate(function () {
                    callback(service.addDate(service.currentBusinessDate, daysToAdd));
                })
            }
            /*service.getBusinessDate(function () {
                bizDate = service.addDate(service.currentBusinessDate, daysToAdd);
            });*/
        }
    }

    function getDate() {
        return new Date();
    }

    function getBusinessDate(callback) {
        if (service.currentBusinessDate != null) {
            return service.currentBusinessDate;
        } else {
            $http({
                method: "GET",
                url: service.restUrls.scmUnitManagement.businessDate
            }).then(function success(response) {
                if (response.status == 200) {
                    service.currentBusinessDate = response.data;
                    if (callback != null && typeof callback == "function") {
                        callback(service.currentBusinessDate);
                    }
                    return service.currentBusinessDate;
                } else {
                    bootbox.alert("Error loading business date!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

    function getEnvType() {
        return ($location.host().indexOf("prod") != -1 ||
            $location.host().indexOf("internal") != -1 ||
            $location.host().indexOf("relax") != -1) ? "PROD" : "DEV";
    }

    function getImageUrl() {
        var imageSuffix = {}
        if (getEnvType() == 'PROD') {
            imageSuffix =
                { //for prod
                    productImage: "https://d3pjt1af33nqn0.cloudfront.net/product_image/",
                    offerImage: "https://d1o715igja4la8.cloudfront.net/",
                    crmImage: "https://d3pjt1af33nqn0.cloudfront.net/crmapp/"

                }
        } else {
            imageSuffix =
                { // for others or dev
                    productImage: "http://d1nqp92n3q8zl7.cloudfront.net/product_image/",
                    offerImage: "https://d3rcyooxiaudps.cloudfront.net/",
                    crmImage: "http://d1nqp92n3q8zl7.cloudfront.net/crmapp/"
                }
        }
        return imageSuffix

    }

    function checkTakeaway() {
        var unitFamily = getUnitFamily();
        if (unitFamily !== undefined) {
            return (unitFamily === 'TAKE_AWAY' || unitFamily === "CHAI_MONK");
        }
        return false;
    }

    function getUnitFamily() {
        if (isEmptyObject(service.unitFamily) && $rootScope.globals !== undefined) {
            var currentUser = $rootScope.globals.currentUser;
            if (currentUser !== undefined) {
                return currentUser.unitFamily;
            }
        }
        return service.unitFamily;
    }

    function isEmptyObject(obj) {
        if (obj !== undefined && obj != null) {
            if (typeof obj == 'string' || typeof obj == 'number')
                return obj.toString().length === 0;
            else if (typeof obj == 'boolean')
                return false;
            else
                return Object.keys(obj).length === 0;
        }
        return true;
    }

    function getProductIdsForSplitDimension() {
        $http({
            method: 'GET',
            url: service.restUrls.partnerMetadata.getProductIdsForSplitDimension
        }).then(function success(response) {
            if(response.data != undefined && response.data != null){
                service.splitDimensionProductIds = response.data;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    return service;
}]);