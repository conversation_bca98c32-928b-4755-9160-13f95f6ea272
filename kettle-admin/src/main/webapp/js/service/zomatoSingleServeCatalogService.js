/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.service('zomatoSingleServeCatalogService', ['$cookieStore', 'APIJson', '$http', function ($cookieStore, APIJson, $http) {

    var service = {};

    service.prepareCatalogs = prepareCatalogs;

    var zomatoMenu = {
        "outletId": "",
        "menu": {
            "catalogues": [], // zomatoCatalogues
        },
    };
    
    var zomatoCatalogues = {
            "description": null,
            "name": null,
            "tags": [],
            "inStock": null,
            "variants": [], // zomatoVariants
            "vendorEntityId": null,
            "imageUrl": null,
            "taxGroups": [],
        };
    
    var zomatoVariants = {
            "vendorEntityId": null,
            "prices": [],// zomatoPrices
        };
    
    var zomatoPrices = {
            "service": null,
            "price": null,
            "originalPrice": null
        };
    
    var zomatoTaxGroup = { 
            "slug": null
    };
    
    var taxMap = new Map([["00009963", "D_P_2.50"],
        ["09021090", "D_P_2.50"],
        ["19054000", "D_P_2.50"],
        ["21069099", "D_P_6.00"],
        ["21069040", "D_P_2.50"],
        ["21012010", "D_P_9.00"],
        ["09024090", "D_P_2.50"],
        ["09109100", "D_P_2.50"]]);
    
    var taxNewMap = new Map([["00009963", "D_P_5.00"],
        ["09021090", "D_P_5.00"],
        ["19054000", "D_P_5.00"],
        ["21069099", "D_P_12.00"],
        ["21069040", "D_P_5.00"],
        ["21012010", "D_P_18.00"],
        ["09024090", "D_P_5.00"],
        ["19053100", "D_P_18.00"],
        ["09109100", "D_P_5.00"]]);
    
    var cataloguesIds = [];
    
    function prepareCatalogs(unitId, unitData, metadata, menuSequence, brand, addPackaging, tagMappings, productImages) {
        var catalog = angular.copy(zomatoMenu);
        if (unitId !== null && unitData !== null) {
            catalog.outletId = (unitId).toString();
            catalog.menu.catalogues = [];
            var productMap = createProductMap(unitData);
            setMenuProductList(catalog, menuSequence, unitData, productMap);
            setMenuCatalogues(catalog, menuSequence, unitData, productMap, brand, tagMappings, metadata, productImages);
            sortProductPrices(unitData);
        }
        cataloguesIds = [];
        return catalog;
    }
    
    function setMenuProductList(catalog, menuSequence, unitData, productMap) {
        menuSequence.productGroupSequences.map(function (cat) {
        	var duplicatecatal = [];
        	cat.subGroups.map(function (subCat) {
        		subCat.productSequenceList.map(function (prodSequence) {
        			var productObj = productMap[prodSequence.product.id];
        			if(!angular.isUndefined(productObj)){
        			if (!duplicatecatal.includes(productObj.id)) {
                        duplicatecatal.push(productObj.id);
                        cataloguesIds.push(productObj.id);
        			}
        			}
        		});
        	});
        });
    }
    
    function createProductMap(unitData) {
        var productMap = {};
        unitData.products.map(function (product) {
            productMap[product.id] = product;
        });
        return productMap;
    }
    
    function sortProductPrices(unitData) {
        unitData.products.map(function (product) {
            product.prices.sort(function (a, b) {
                return a.price - b.price;
            });
        })
    }
    
    function setMenuCatalogues(catalog, menuSequence, unitData, productMap, brand, tagMappings, metadata, productImages) {
        unitData.products.map(function (catal) {
        	if (cataloguesIds.includes(catal.id)) {
                catalog.menu.catalogues.push(addNewCatalogues(catalog, catal, menuSequence, productMap, unitData, brand, tagMappings, metadata, productImages));
        	}
        });
    }


    function addNewCatalogues(catalog, catal, menuSequence, productMap, unitData, brand, tagMappings, metadata, productImages) {
        console.log(catal.name);
        var catalogue = angular.copy(zomatoCatalogues);
        catalogue.vendorEntityId = "ss_" + (catal.id).toString();
        catalogue.description = catal.description;
        catalogue.name = catal.name;
        /*catalogue.imageUrl = (catal.type === 8 ? "" : "https://cafes.chaayos.com/img/products/" + catal.id + ".jpg");
        if (brand.brandId != 1) {
            catalogue.imageUrl = (catal.type === 8 ? "" : "https://cafes.chaayos.com/img/" + brand.brandCode + "/products/" + catal.id + ".jpg");
        }*/
        catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + productImages[catal.id].gridLow;
        catalogue.inStock = true;
/*        if (catal.taxCode != "COMBO" && catal.taxCode != "GIFT_CARD") {
            catalogue.taxes = ["CGST_" + taxMap.get(catal.taxCode), "SGST_" + taxMap.get(catal.taxCode)]
        }
        else if (catal.taxCode == "COMBO") {
            catalogue.taxes = ["CGST_D_P_2.50", "SGST_D_P_2.50"]
        }
*/        var taxGroup = angular.copy(zomatoTaxGroup);
         if (catal.taxCode != "COMBO" && catal.taxCode != "GIFT_CARD") {
             taxGroup.slug = "GST_"+ taxNewMap.get(catal.taxCode);
         }
         else if (catal.taxCode == "COMBO") {
         	taxGroup.slug = "GST_D_P_5.00";
         }
         catalogue.taxGroups.push(taxGroup);
        var str = [(catal.attribute != null ? catal.attribute.toLowerCase() : "veg")];
       /* var tag = str.toString().replace("_", "-");
        catalogue.tags = [tag ,"ss-snacks" ,"ss-healthy"];*/
        catalogue.tags = getTags(tagMappings, catal.id);
        catalogue.variants = addVariants(catal, catalog, unitData, brand, metadata);
        return catalogue;
    }
    
    function getTags(tagsMapping, catalId){
    	var tags = [];
    	tagsMapping[0].mappings.map(function (tagList) {
    		if(tagList.productId == catalId){
    			tagList.tags.map(function (tag) {
    				tags.push(tag.name);
    			});
    		}
    	});
    	return tags;
    }
    
    function addVariants(catal, catalog, unitData, brand, metadata){
    	var groups = [];
    	if(catal.prices.length != 0) {
            catal.prices.map(function (variant, indexmain) {
            	var variantsObj = angular.copy(zomatoVariants);
            	variantsObj.vendorEntityId = (catal.id).toString();
            	variantsObj.prices.push(addprice(variant.price, catal, unitData, metadata));
            	groups.push(variantsObj);
            	});
            }
    	return groups;
    }
    
    function addprice(variantPrice, catal, unitData, metadata) {
        var prices = angular.copy(zomatoPrices);
        prices.service = "single_serve";
        prices.price = variantPrice;
        var origPrice = getOriginalPrice(catal, unitData, metadata);
        prices.originalPrice = origPrice;
        return prices;
    }
    
    function getOriginalPrice(catal, unitData, metadata){
    	var productId;
    	var productDim;
    	var price = 0;
    	if (catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.compositeProduct != null){
    		catal.prices[0].recipe.ingredient.compositeProduct.details.map(function (detailProd) {
    			productId = detailProd.menuProducts[0].product.productId;
    			productDim = detailProd.menuProducts[0].dimension.code;
    			price = price + getProdOriginalPrice(productId, productDim, unitData);
    		});
    	}
    	else{
    		price = catal.prices[0].price;
    	}
    	var packagingCharge = getPackagingCharge(metadata, price);
    	price = (price + packagingCharge).toFixed(2);
    	return price;
    }
    
    function getPackagingCharge(metadata, itemPrice) {
        var packagingCharge = 0;
        if (metadata.packagingType === "PERCENTAGE") {
            packagingCharge = ((metadata.packagingValue / 100) * itemPrice).toFixed(2);
            return parseInt(packagingCharge);
        } else if (metadata.packagingType === "FIXED") {
            packagingCharge = metadata.packagingValue.toFixed(2);
            return parseInt(packagingCharge);
        }
    }
    
    function getProdOriginalPrice(productId, productDim, unitData){
    	var price;
    	unitData.products.map(function (catalProd) {
    		if(catalProd.id == productId){
    			catalProd.prices.map(function (dimensions) {
    				if(dimensions.dimension == productDim){
    					price = dimensions.price;
    				}
    			});
    		}
    	});
    	return price;
    }
    
    return service;

    
}]);