/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
adminapp.service('swiggyCatalogServiceV1', ['AppUtil', function (AppUtil) {

    var service = {};
    service.errorList = [];
    service.prepareCatalog = prepareCatalog;
    service.addonsList = [];
    service.addonsMap = {};
    service.addonIds = [];

    var entity = {
        "main_categories": [],
        "items": [],
    };
    var mainCategoryObj = {
        "id": null,
        "name": null,
        "description": null,
        "order": null,
        "sub_categories": []
    };
    var subCategoryObj = {
        "id": null,
        "name": null,
        "description": null,
        "order": null
    };
    var itemObj = {
        "id": null,
        "category_id": null,
        "sub_category_id": null,
        "name": null,
        "is_veg": null,
        "order": null,
        "description": null,
        "price": 0,
        "gst_details": {},
        "packing_charges": 0,
        "enable": null,
        "in_stock": null,
        "addon_free_limit": null,
        "recommended": null,
        "addon_limit": null,
        "image_url": null,
        "variant_groups": [],
        "addon_groups": [],
        "item_slots": [],
        "pricing_combinations": []
    };
    var taxGSTDetailObj = {
        "igst": 0,
        "sgst": 0,
        "cgst": 0,
        "inclusive": false
    };
    var variantGroupObj = {
        "id": null,
        "name": "",
        "order": null,
        "variants": []
    };
    var variantObj = {
        "id": null,
        "name": "",
        "price": 0,
        "default": true,
        "order": null,
        "in_stock": null,
        "is_veg": null,
        "gst_details": {},
        "default_dependent_variant_id": null,
        "default_dependent_variant_group_id": null
    };
    var addOnGroup = {
        "id": null,
        "name": null,
        "addon_free_limit": null,
        "addon_limit": null,
        "addon_min_limit": null,
        "order": null,
        "addons": null
    };
    var addOnObj = {
        "id": null,
        "name": null,
        "price": 0,
        "is_veg": true,
        "in_stock": 1,
        "order": null,
        "is_default": null,
        "gst_details": null
    };
    var itemSlotObj = {
        "day_of_week": null,
        "open_time": null,
        "close_time": null
    };
    var pricingCombinationObj = {
        "variant_combination": [],
        "price": 0,
        "addon_combination": []
    };
    var variantCombinationObj = {
        "variant_group_id": null,
        "variant_id": null
    };
    var addOnCombinationObj = {
        "addon_group_id": null,
        "addon_id": null
    };

    //var recommndMap = null;

    var milkOption = ["Regular Milk", "Full Doodh", "Doodh Kum", "Paani Kum"];

    var splitAllDesiChai;
    var miniKetliDefault;

    function prepareCatalog(selectedUnit, unitData, metadata, menuSequence, menuRecomendationData, productImages) {
        try {
            service.errorList = [];
            var unitId = selectedUnit.id;
            var addPackaging = selectedUnit.addPackaging;
            splitAllDesiChai = selectedUnit.splitAllDesiChaiDimensions;
            miniKetliDefault = selectedUnit.miniKetliDefault;
            if(miniKetliDefault == true) {
                sortDesiChaiDimensions(unitData);
            }
            prepareAddOnsMap(unitData);
            //initializeRecommendationCategory();
            var catalog = Object.assign({}, entity);
            if (unitId !== null && unitData !== null) {
                var productMap = createProductMap(unitData);
                setMenuCategories(catalog, menuSequence);
                catalog.items = [];
                var productCategoryMap = createProductCategoryMap(unitData, menuSequence);
                var flag = validateProductPricings(productCategoryMap, unitData);
                if (!flag) {
                    throw 500;
                }
                var order = 200;
                productCategoryMap.map(function (categoryInfo) {
                    unitData.products.map(function (product) {
                        if (categoryInfo.productId === product.id) {
                            if ([10, 1282].indexOf(product.id) >= 0) {
                                if (splitAllDesiChai === true) {
                                    ["MiniKetli", "ChotiKetli", "BadiKetli"].map(function (dcDimension) {
                                        catalog.items.push(addProduct(product, unitData, metadata, categoryInfo, dcDimension, menuRecomendationData, order, productImages, productMap));
                                        order--;
                                    });
                                } else {
                                    ["CKMK", "BadiKetli"].map(function (dcDimension) {
                                        if (dcDimension == "BadiKetli") {
                                            catalog.items.push(addProduct(product, unitData, metadata, categoryInfo, dcDimension, menuRecomendationData, order, productImages, productMap));
                                        } else {
                                            var prod = angular.copy(product);
                                            prod.prices = [];
                                            product.prices.map(function (priceObj) {
                                                if (priceObj.dimension != "BadiKetli") {
                                                    prod.prices.push(angular.copy(priceObj));
                                                }
                                            });
                                            catalog.items.push(addProduct(prod, unitData, metadata, categoryInfo, null, menuRecomendationData, order, productImages, productMap));
                                        }
                                        order--;
                                    });
                                }
                            } else {
                                catalog.items.push(addProduct(product, unitData, metadata, categoryInfo, null, menuRecomendationData, order, productImages, productMap));
                            }
                            order--;
                        }
                    });
                });

                var unmappedProducts = [];
                unitData.products.map(function (product) {
                    if (product.classification === "MENU" && product.billType !== "ZERO_TAX" && product.type !== 12) {
                        var found = false;
                        menuSequence.productGroupSequences.map(function (cat) {
                            cat.subGroups.map(function (subCat) {
                                subCat.productSequenceList.map(function (prodSequence) {
                                    if (!found && product.id === prodSequence.product.id) {
                                        found = true;
                                    }
                                });
                            });
                        });
                        if (!found && [11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0) {
                            /*unmappedProducts.push({
                                id:product.id,
                                name:product.name
                            });*/
                        }
                    }
                });
                //console.log({'entity':catalog, 'unmappedProducts':unmappedProducts});
                if (!showErrorList()) {
                    return {'entity': catalog, 'unmappedProducts': unmappedProducts};
                } else {
                    return {'entity': angular.copy(entity), 'unmappedProducts': []};
                }
            }
        } catch (e) {
            console.log(e);
            if (!showErrorList()) {
                bootbox.alert(e);
            }
            return {'entity': angular.copy(entity), 'unmappedProducts': []};
        }
    }

    function showErrorList() {
        if (service.errorList.length > 0) {
            var errors = "<ul>";
            service.errorList.map(function (err) {
                errors += "<li>" + err + "</li>"
            });
            errors += "</ul>";
            bootbox.alert(errors);
            return true;
        } else {
            return false;
        }
    }

    /*function initializeRecommendationCategory() {
        recommndMap = [1301,1024,1302,1234,10,460,1201,1028,1063,1113,670,680,690,1200,620,992,660,1253,1250,1251,1403,1249,1252,1341,1248,1270,130];
    }*/

    function sortDesiChaiDimensions(unitData) {
        unitData.products.map(function (product) {
            if([10,1282].indexOf(product.id) >= 0) {
                product.prices = product.prices.sort(function (a,b) {
                    return a.price - b.price;
                })
            }
        });
    }

    function validateProductPricings(productCategoryMap, unitData) {
        var flag = true;
        productCategoryMap.map(function (categoryInfo) {
            unitData.products.map(function (product) {
                if (categoryInfo.productId === product.id) {
                    if (product.subType === 3676) {
                        service.errorList.push(product.name + " is super combo. Super combo are not supported on Swiggy.");
                    }
                    if (product.prices == null || product.prices.length == 0) {
                        flag = false;
                        service.errorList.push("Product Price not found for " + product.name + ".");
                    } else {
                        product.prices.map(function (priceObj) {
                            if (priceObj.recipe == null) {
                                service.errorList.push("Product recipe not found for " + product.name + " dimension: " + priceObj.dimension);
                            }
                        })
                    }
                }
            });
        });
        return flag;
    }

    function createProductCategoryMap(unitData, menuSequence) {
        var productCategoryMap = [];
        menuSequence.productGroupSequences.sort(function(a, b) {
            return b.groupIndex - a.groupIndex;
        });
        menuSequence.productGroupSequences.map(function (cat) {
            var categoryId = createFilteredName(cat.groupName); //cat.groupId;
            var categoryName = cat.groupName;
            cat.subGroups.sort(function (a,b) {
                return b.groupIndex - a.groupIndex;
            });
            cat.subGroups.map(function (subCat) {
                var subCategoryId = createFilteredName(subCat.groupName); //subCat.groupId;
                var subCategoryName = subCat.groupName;
                subCat.productSequenceList.sort(function (a,b) {
                    return a.productIndex - b.productIndex;
                });
                subCat.productSequenceList.map(function (prodSequence) {
                    productCategoryMap.push({
                        'categoryId': categoryId,
                        'categoryName': categoryName,
                        'subCategoryId': subCategoryId,
                        'subCategoryName': subCategoryName,
                        'recommended': prodSequence.recommended,
                        'productId': prodSequence.product.id
                    });
                });
            });
        });
        return productCategoryMap;
    }

    function setMenuCategories(catalog, menuSequence) {
        catalog.main_categories = [];
        menuSequence.productGroupSequences.sort(sortfunction);
        var count = 0;
        menuSequence.productGroupSequences.map(function (cat) {
            catalog.main_categories.push(addNewCategory(cat, count, menuSequence.productGroupSequences));
            count++;
        });
        catalog.main_categories.sort(customfunction);
    }

    function addNewCategory(cat, count, prodGroup) {
        var category = angular.copy(mainCategoryObj);
        category.id = createFilteredName(cat.groupName);//cat.groupId;
        category.name = cat.groupName;
        category.description = cat.groupDescription;
        // category.order = cat.groupIndex;
        category.order = prodGroup.length - count;
        cat.subGroups.sort(sortfunction);
        var subCount = 0;
        cat.subGroups.map(function (subCat) {
            category.sub_categories.push(addNewSubCategory(cat.groupId, subCat, subCount, cat.subGroups));
            subCount++;
        });
        category.sub_categories.sort(customfunction);
        return category;
    }

    function customfunction(a, b) {
        return b.order - a.order;
    }

    function sortfunction(a, b) {
        return a.groupIndex - b.groupIndex;
    }

    function addNewSubCategory(catId, subCat, count, prodSubGroup) {
        var subCategory = angular.copy(subCategoryObj);
        subCategory.id = createFilteredName(subCat.groupName); //subCat.groupId;
        subCategory.name = subCat.groupName;
        subCategory.description = subCat.groupDescription;
        //subCategory.order = subCat.groupIndex;
        subCategory.order = prodSubGroup.length - count;
        return subCategory;
    }

    function addProduct(product, unitData, metadata, categoryInfo, dcDimension, menuRecomendationData, order, productImages, productMap) {
        var item = Object.assign({}, itemObj);
        var productAlias = getProductAlias(product);
        var name = productAlias != null ? productAlias : getProductName(product.name);
        if (dcDimension != null) {
            item.id = product.id + "_" + categoryInfo.categoryId + "_" + categoryInfo.subCategoryId + "_" + dcDimension;
            item.name = name + " " + dcDimension + " (" + getDimensionDescription(dcDimension, product.prices[0].dimensionDescriptor) + ")";
        } else {
            item.id = product.id + "_" + categoryInfo.categoryId + "_" + categoryInfo.subCategoryId;
            item.name = name;
        }
        item.category_id = categoryInfo.categoryId;
        item.sub_category_id = categoryInfo.subCategoryId;
        item.order = order;
        console.log(product.name);
        item.is_veg = isVeg(product.attribute);
        item.description = getProductDescription(product.description);
        item.price = getItemPrice(product, dcDimension);
        if(product.subType == 3675) {
            var heroProductId = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId;
            var heroProductDim = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].dimension.code;
            productMap[heroProductId].prices.map(function (price) {
                if(price.dimension === heroProductDim) {
                    item.price = price.price;
                }
            });
        }
        item.gst_details = getGSTDetails(product.taxCode, unitData.taxes);
        var packagingCharge = getPackagingCharge(metadata, product, dcDimension);
        if (product.subType != 3676) {
            item.packing_charges = packagingCharge;
        }
        item.enable = 1;
        item.in_stock = 1;
        item.addon_free_limit = -1;
        item.addon_limit = -1;
        if(categoryInfo.recommended != null) {
            item.recommended = categoryInfo.recommended;
        } else {
            item.recommended = false;
        }
        if (productImages[product.id] == null || productImages[product.id].gridLow == null) {
            if(AppUtil.getEnvType() === "PROD") {
                service.errorList.push("Product image missing for: " + product.name);
            }
        } else {
            item.image_url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + productImages[product.id].gridLow;
        }
        item.addon_groups = getAddOnGroups(product, unitData, false, metadata, menuRecomendationData, dcDimension);
        item.variant_groups = getVariantGroups(product, item, unitData, metadata, dcDimension);
        item.item_slots = [];
        item.pricing_combinations = [];
        var basePrice = getBasePrice(product, dcDimension);
        if(product.subType == 3675) {
            basePrice = item.price;
        }
        if ([10, 1282].indexOf(product.id) >= 0) {
            if (splitAllDesiChai !== true && dcDimension === "BadiKetli") {
                basePrice = 0;
            }
        }
        var pricingCombinations = getPricingCombinations(item, basePrice);
        if (Array.isArray(pricingCombinations) && pricingCombinations.length) {
            item.pricing_combinations = pricingCombinations;
            updateDefaultDependentVariant(item);
        }
        // console.log('item is  ::: ', JSON.stringify(item));
        return item;
    }

    function getBasePrice(product, dcDimension) {
        var basePrice = null; //keeping high value to start with
        if (dcDimension != null) {
            product.prices.map(function (price) {
                if (price.dimension == dcDimension) {
                    basePrice = price.price;
                }
            });
        } else {
            product.prices.map(function (price) {
                if (basePrice == null) {
                    basePrice = price.price;
                } else if (price.price < basePrice) {
                    basePrice = price.price;
                }
            });
        }
        return basePrice;
    }

    function updateDefaultDependentVariant(item) {
        var variantGroups = item.variant_groups;
        var vgDDepObjMapArray = [];
        if (Array.isArray(variantGroups) && variantGroups.length > 1) {
            variantGroups.map(function (variantGroup, index) {
                var vgDefaultDepObj = {};
                var vgDDepObjMap = {
                    "variantGroupNo": index,
                    "variantDefaultDependentObj": vgDefaultDepObj
                };
                var variants = variantGroup.variants;
                if (Array.isArray(variants) && variants.length) {
                    vgDefaultDepObj = {
                        "default_dependent_variant_id": variants[0].id,
                        "default_dependent_variant_group_id": variantGroup.id
                    };
                    vgDDepObjMap.variantDefaultDependentObj = vgDefaultDepObj;
                }
                vgDDepObjMapArray.push(vgDDepObjMap);
            });
            //console.log(vgDDepObjMapArray);
            var count = 0;
            variantGroups.map(function (variantGroup, index) {
                var variants = variantGroup.variants;
                count = count + 1;
                if (count !== variantGroups.length) { //This will skip the last object to not form a cycle
                    if (Array.isArray(variants) && variants.length) {
                        var defaultDependentObj = vgDDepObjMapArray[count];
                        variants.map(function (variant) {
                            variant.default_dependent_variant_id = defaultDependentObj.variantDefaultDependentObj.default_dependent_variant_id;
                            variant.default_dependent_variant_group_id = defaultDependentObj.variantDefaultDependentObj.default_dependent_variant_group_id;
                        });
                    }
                }
            });
        }
    }

    function getPricingCombinations(item, basePrice) {
        console.log('item  ::: ', item);
        var variantGroups = item.variant_groups;
        var pricingCombinations = [];
        if (Array.isArray(variantGroups) && variantGroups.length > 1) {
            var allIdArrays = [];
            var variant_combination_array = [];
            //   console.log(variantGroups.length, ' variant groups found');
            variantGroups.map(function (variantGroup, index) {
                var variants = variantGroup.variants;
                var variant_combination_id_array = [];
                if (Array.isArray(variants) && variants.length) {
                    variants.map(function (variant) {
                        variant_combination_id_array.push(variant.id);
                        variant_combination_array.push(
                            {
                                "variantId": variant.id,
                                "variantObj": {
                                    "variant_group_id": variantGroup.id,
                                    "variant_id": variant.id,
                                    "variant_price": variant.price,
                                }
                            });
                    });
                }
                allIdArrays.push(variant_combination_id_array);
            });
            var combinations = allPossibleCases(allIdArrays);
            combinations.map(function (combination) {
                var pricingCombination = Object.assign({}, {
                    "variant_combination": [],
                    "price": 0,
                    "addon_combination": []
                });
                if (combination.length > 0) {
                    for (var k = 0; k < combination.length; k++) {
                        var combinationString = combination[k];
                        var res = combinationString.split(",");
                        var variantCombinations = [];
                        for (var i = 0; i < res.length; i++) {
                            variant_combination_array.map(function (variantMapObject) {
                                if (variantMapObject.variantId === res[i]) {
                                    //console.log(variantMapObject.variantObj);
                                    pricingCombination.price = pricingCombination.price + variantMapObject.variantObj.variant_price;
                                    variantCombinations.push(variantMapObject.variantObj);
                                }
                            });
                        }
                        pricingCombination.price = pricingCombination.price + basePrice;
                        //console.log(pricingCombination.price);
                        if (pricingCombination.price === 0) {
                            pricingCombination.price = item.price;
                        }
                        pricingCombination.variant_combination = variantCombinations;
                        if ((item.addon_groups).length > 0) {
                            pricingCombination.addon_combination = getAddOnCombinationGroups(item);
                        }
                        //console.log('pricingCombination is::: ', pricingCombination);
                        pricingCombinations.push(pricingCombination);
                    }
                }
            });
        } else {
            console.log('Only One variant group found');
            var variantGroups = item.variant_groups;
            variantGroups.map(function (variantGroup, index) {
                var variants = variantGroup.variants;
                //console.log(variants);
                if (Array.isArray(variants) && variants.length) {
                    variants.map(function (variant) {
                        var pricingCombination = Object.assign({}, {
                            "variant_combination": [],
                            "price": 0,
                            "addon_combination": []
                        });
                        var variantCombinations = [];
                        //console.log(variant);
                        pricingCombination.price = parseFloat(pricingCombination.price) + parseFloat(variant.price);
                        pricingCombination.price = parseFloat(pricingCombination.price) + parseFloat(basePrice);
                        variantCombinations.push({
                            "variant_group_id": variantGroup.id,
                            "variant_id": variant.id,
                            "variant_price": variant.price,
                        });
                        pricingCombination.variant_combination = variantCombinations;
                        if ((item.addon_groups).length > 0) {
                            pricingCombination.addon_combination = getAddOnCombinationGroups(item);
                        }
                        //pricingCombination.addon_combination = [];
                        //console.log('pricingCombination is::: ', pricingCombination);
                        if (pricingCombination.price === 0) {
                            pricingCombination.price = item.price;
                        }
                        pricingCombinations.push(pricingCombination);
                    });
                }
            });
        }
        //console.log(pricingCombinations);
        return pricingCombinations;
    }

    function getAddOnCombinationGroups(item) {
        var groups = [];
        item.addon_groups.map(function (addOnsGroup) {
            addOnsGroup.addons.map(function (addOn) {
                var addOnCombination = Object.assign({}, addOnCombinationObj);
                addOnCombination.addon_group_id = addOnsGroup.id;
                addOnCombination.addon_id = addOn.id;
                groups.push(addOnCombination);
            });
        });
        return groups;
    }

    function allPossibleCases(arr) {
        if (arr.length === 1) {
            return arr[0];
        } else {
            var result = [];
            var allCasesOfRest = allPossibleCases(arr.slice(1));  // recur with the rest of array
            for (var i = 0; i < allCasesOfRest.length; i++) {
                for (var j = 0; j < arr[0].length; j++) {
                    var variant_combination = [];
                    variant_combination.push(arr[0][j] + ',' + allCasesOfRest[i]);
                    result.push(variant_combination);
                }
            }
            return result;
        }
    }

    function getVariantGroups(product, item, unit, metadata, dcDimension) {
        var variantGroups = [];
        var dimensionVariants = addDimensionVariants(product, item, unit, dcDimension);
        if (Array.isArray(dimensionVariants) && dimensionVariants.length) {
            dimensionVariants.map(function (dimensionVariant) {
                variantGroups.push(dimensionVariant);
            });
        }
        //console.log('In get getVariantGroups product is ', JSON.stringify(product));
        if (product.prices[0].recipe != null) {
            var ingredientVariants = addIngredientVariants(product, dcDimension);
            if (Array.isArray(ingredientVariants) && ingredientVariants.length) {
                ingredientVariants.map(function (ingredientVariant) {
                    variantGroups.push(ingredientVariant);
                });
            }
            var ingredientProductVariants = addIngredientProducts(product, item.id, item.sub_category_id);
            if (Array.isArray(ingredientProductVariants) && ingredientProductVariants.length) {
                ingredientProductVariants.map(function (ingredientProductVariant) {
                    variantGroups.push(ingredientProductVariant);
                });
            }
            var compositeProductsVariants = addCompositeProducts(product, item.id, unit, item, metadata);
            if (Array.isArray(compositeProductsVariants) && compositeProductsVariants.length) {
                compositeProductsVariants.map(function (compositeProductsVariant) {
                    variantGroups.push(compositeProductsVariant);
                });
            }
        }
        return variantGroups;
    }

    function addDimensionVariants(product, item, unit, dcDimension) {
        //console.log('Enter add dimension variant method');
        var groups = [];
        if (product.prices.length > 1 && dcDimension == null) {
            var variantGroup = Object.assign({}, variantGroupObj);
            variantGroup.id = product.id + "_size";
            variantGroup.name = "Size";
            variantGroup.order = 1;
            variantGroup.variants = [];
            //console.log('product for dimension variantGroup.name', variantGroup.name);
            var basePrice = null; //keeping high value to start with
            product.prices.map(function (price) {
                if (basePrice == null) {
                    basePrice = price.price;
                } else if (price.price < basePrice) {
                    basePrice = price.price;
                }
            });
            product.prices.map(function (price, ind) {
                if (price.recipe != null) {
                    if (([10, 1282].indexOf(item.id) < 0)) {
                        var id = product.id + "_" + price.dimension.substr(0, 1);
                        var name = price.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(price.dimension, price.dimensionDescriptor) + ")";
                        var isDefault = (ind === 0);
                        //console.log('product for dimension variant.name', name);
                        /*if (price.recipe.dimension.desc != null && price.recipe.dimension.desc.trim().length > 0) {
                            name += " (" + price.recipe.dimension.desc + ")";
                        }*/
                        var variant = buildVariant(id, name, isDefault);
                        var variantPrice = price.price - basePrice;
                        variant.price = 0;
                        if (variantPrice !== 0) {
                            //console.log("Size price :: ", variantPrice);
                            variant.price = parseFloat(variantPrice);
                        }
                        /*if(unit.taxes!== undefined){
                            variant.gst_details = getGSTDetails(product.taxCode, unit.taxes);
                        }*/
                        variantGroup.variants.push(variant);
                    }
                }
            });
            groups.push(variantGroup);
        }
        return groups;
    }

    function addIngredientVariants(product, dcDimension) {
        var groups = [];
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.variants != null) {
            product.prices[0].recipe.ingredient.variants.map(function (varItem) {
                var variantGroup = Object.assign({}, variantGroupObj);
                var displayName = varItem.product.displayName || "Option";
                //console.log("IngredientVariants group displayName:: ", displayName);
                variantGroup.id = product.id + "_" + varItem.product.productId;
                if (dcDimension != null) {
                    variantGroup.id += "_" + dcDimension;
                }
                variantGroup.name = displayName;
                variantGroup.order = groups.length + 1;
                variantGroup.variants = [];
                varItem.details.map(function (varDetail, index) {
                    var id = product.id + "_" + varDetail.productId + varDetail.alias.substr(0, 1)
                        + varDetail.alias.substr(varDetail.alias.length - 1) + displayName.length;
                    if (dcDimension != null) {
                        id += "_" + dcDimension;
                    }
                    var name = varDetail.alias;
                    var isDefault = varDetail.defaultSetting;
                    //console.log("IngredientVariants name:: ", name);
                    var variant = buildVariant(id, name, isDefault);
                    variantGroup.variants.push(variant);
                });
                groups.push(variantGroup);
            });
        }
        if (product.id === 10 || product.id === 1282) {
            var variantGroup = Object.assign({}, variantGroupObj);
            var displayName = "Milk Option";
            variantGroup.id = product.id + "_" + "MLK_OPT";
            if (dcDimension != null) {
                variantGroup.id += "_" + dcDimension;
            }
            variantGroup.name = displayName;
            variantGroup.order = groups.length + 1;
            variantGroup.variants = [];
            ["Regular Milk", "Full Doodh", "Doodh Kum", "Paani Kum"].map(function (varnt, ind) {
                var id = product.id + "_" + varnt.substr(0, 1)
                    + varnt.substr(varnt.length - 1) + displayName.length;
                if (dcDimension != null) {
                    id += "_" + dcDimension;
                }
                var name = varnt;
                var isDefault = (ind === 0);
                //console.log("IngredientVariant displayName:: ", name);
                var variant = buildVariant(id, name, isDefault);
                variantGroup.variants.push(variant);
            });
            groups.push(variantGroup);
        }
        return groups;
    }

    function addIngredientProducts(product, itemId, subCategoryId) {
        var groups = [];
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.products != null) {
            product.prices[0].recipe.ingredient.products.map(function (prodItem, ind) {
                var variantGroup = Object.assign({}, variantGroupObj);
                var displayName = prodItem.display || "Option";
                //console.log("IngredientProducts group displayName:: ", displayName);
                variantGroup.id = product.id + "_" + displayName;
                variantGroup.name = displayName;
                variantGroup.order = ind;
                variantGroup.variants = [];
                prodItem.details.map(function (prodDetail, index) {
                    var id = product.id + "_" + subCategoryId + "_" + prodDetail.product.productId;
                    var name = prodDetail.product.name;
                    var isDefault = prodDetail.defaultSetting;
                    //console.log("IngredientProducts name:: ", name);
                    var variant = buildVariant(id, name, isDefault);
                    variantGroup.variants.push(variant);
                });
                if (displayName != "Bread") {
                    groups.push(variantGroup);
                }
            });
        }
        return groups;
    }

    function addCompositeProducts(catal, itemId, unitData, item, metadata) {
        var groups = [];
        if (catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.compositeProduct != null && catal.subType != 3675 && catal.subType != 3676) {
            var prodId = catal.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId;
            var prodSecId = (catal.prices[0].recipe.ingredient.compositeProduct.details[1] != null ?
                catal.prices[0].recipe.ingredient.compositeProduct.details[1].menuProducts[0].product.productId : null);
            var milkOptionCheck = prodId == 10;
            /*if (prodId == 10) {
                milkOptionCheck = true;
            }*/
            unitData.products.map(function (compCatal) {
                if (catal.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId === compCatal.id) {
                    var variantGroupsCombo = addIngredientProductsForCombo(compCatal, catal, milkOptionCheck, item, unitData);
                    if (Array.isArray(variantGroupsCombo) && variantGroupsCombo.length) {
                        variantGroupsCombo.map(function (dimensionPropertyCombo) {
                            groups.push(dimensionPropertyCombo);
                        });
                    }
                }
            });
            if (prodSecId != null) {
                unitData.products.map(function (compCatalSec) {
                    if (catal.prices[0].recipe.ingredient.compositeProduct.details[1].menuProducts[0].product.productId === compCatalSec.id) {
                        if (compCatalSec.prices[0].recipe.ingredient.variants.length != 0) {
                            var variantGroupsComboSecnd = addIngredientProductsForCombo(compCatalSec, catal, false, item, unitData);
                            if (Array.isArray(variantGroupsComboSecnd) && variantGroupsComboSecnd.length) {
                                variantGroupsComboSecnd.map(function (dimensionPropertyComboSecnd) {
                                    groups.push(dimensionPropertyComboSecnd);
                                });
                            }
                        }
                    }
                });
            }
        } else if (catal.subType == 3675) {
            var heroProductId = catal.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId;
            var heroProdDim = catal.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].dimension.code;
            var milkOptionCheck = false;
            if (heroProductId == 10) {
                milkOptionCheck = true;
            }
            unitData.products.map(function (prod) {
                if (prod.id == heroProductId) {
                    prod.prices.map(function (pricess) {
                        if (pricess.dimension == heroProdDim) {
                            var dimensionPropertiesCombo = addIngredientProductsForCombo(prod, catal, milkOptionCheck, item, unitData);
                            if (Array.isArray(dimensionPropertiesCombo) && dimensionPropertiesCombo.length) {
                                dimensionPropertiesCombo.map(function (dimensionPropertyCombo) {
                                    groups.push(dimensionPropertyCombo);
                                });
                            }
                        }
                    });
                }
            });
        } else if (catal.subType == 3676) {
            if (catal.prices[0] != null) {
                var dimension = catal.prices[0];
                if (dimension.recipe != null) {
                    if (dimension.recipe.ingredient.compositeProduct != null) {
                        var variantGroup = Object.assign({}, variantGroupObj);
                        variantGroup.id = "rd" + "_" + catal.id;
                        variantGroup.name = "Beverages";
                        variantGroup.order = 0;
                        variantGroup.variants = [];
                        dimension.recipe.ingredient.compositeProduct.details.map(function (detail, index) {
                            if (index == 0) {
                                detail.menuProducts.map(function (menuProduct) {
                                    var variant = Object.assign({}, variantObj);
                                    var actualProduct = getActualProd(unitData, menuProduct);
                                    if (actualProduct != null && !angular.isUndefined(actualProduct)) {
                                        //var packagingCharge = getPackagingCharge(metadata, actualProduct, null);
                                        var price = getRecommendedProdPrice(unitData, menuProduct, actualProduct);
                                        if (price != null && !angular.isUndefined(price)) {
                                            variant.id = "rd" + "_" + catal.id + "_" + actualProduct.id;
                                            variant.name = actualProduct.name;
                                            var discountedPrice = getDiscountedPrice(detail.discount, price);
                                            var packagingCharge = getComboPackaginCharges(metadata, discountedPrice);
                                            variant.price = parseFloat((parseFloat(packagingCharge) + discountedPrice).toFixed(2));
                                            variant.default = menuProduct.defaultSetting;
                                            variant.order = 1;
                                            variant.in_stock = 1;
                                            variant.is_veg = 1;
                                            variant.gst_details = null;
                                            var check = false;
                                            actualProduct.prices[0].recipe.ingredient.variants.map(function (varProducts) {
                                                if (varProducts.product.name == "Sugar Syrup") {
                                                    check = true;
                                                }
                                            });
                                            if (check) {
                                                variantGroup.variants.push(variant);
                                            }
                                        }
                                    }
                                });
                            }
                        });
                        groups.push(variantGroup);
                    }
                }
            }
        }
        return groups;
    }

    function addIngredientProductsForCombo(catal, catalRoot, milkOptionCheck, item, unitData) {
        var groups = [];
        var isCombo = true;
        if (catal.prices == null || catal.prices.length === 0) {
            alert("Price not found for " + catal.name + ". Please add price first.");
        }
        if (catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.variants != null) {
            catal.prices[0].recipe.ingredient.variants.map(function (varProducts) {
                var variantGroup = Object.assign({}, variantGroupObj);
                variantGroup.id = (catal.id).toString() + "_" + varProducts.product.name + "_" + (catalRoot.id).toString();
                variantGroup.name = varProducts.product.name;
                if (varProducts.product.displayName != null && varProducts.product.displayName.trim().length > 0) {
                    variantGroup.name = varProducts.product.displayName;
                }
                variantGroup.order = 0;
                variantGroup.variants = [];
                varProducts.details.map(function (a, b) {
                    return (a.defaultSetting === b.defaultSetting) ? 0 : a.defaultSetting ? -1 : 1;
                });
                varProducts.details.map(function (varDetail, index) {
                    var id = (catal.id).toString() + "_" + varDetail.alias + "_" + (catalRoot.id).toString();
                    var name = varDetail.alias;
                    var isDefault = varDetail.defaultSetting;
                    var variant = buildVariant(id, name, isDefault);
                    variantGroup.variants.push(variant);
                });
                groups.push(variantGroup);
            });
        }
        if (catal.prices[0].dimension === "None" && catal.prices[0].recipe.ingredient.products.length !== 0) {
            catal.prices[0].recipe.ingredient.products.map(function (prodProduct) {
                var variantGroup = Object.assign({}, variantGroupObj);
                variantGroup.id = (catal.id).toString() + "_" + prodProduct.display + "_" + (catalRoot.id).toString();
                variantGroup.name = prodProduct.display;
                if (prodProduct.display != null && prodProduct.display.trim().length > 0) {
                    variantGroup.name = prodProduct.display;
                }
                variantGroup.order = 0;
                variantGroup.variants = [];
                prodProduct.details.map(function (varDetail, index) {
                    var id = (catal.id).toString() + "_" + varDetail.alias + "_" + (catalRoot.id).toString();
                    var name = varDetail.alias;
                    var isDefault = varDetail.defaultSetting;
                    var variant = buildVariant(id, name, isDefault);
                    variantGroup.variants.push(variant);
                });
                if (prodProduct.display !== "Bread") {
                    groups.push(variantGroup);
                }
            });
        }
        if (milkOptionCheck) {
            var random = Math.random().toString(36).substring(7);
            var variantGroup = Object.assign({}, variantGroupObj);
            variantGroup.id = (random).toString() + "_" + "All Variants" + "_" + (catalRoot.id).toString();
            variantGroup.name = "Milk Option";
            variantGroup.order = 0;
            variantGroup.variants = [];

            var index;
            for (index = 0; index < milkOption.length; index++) {
                var id = (catal.id).toString() + "_" + milkOption[index] + "_" + (catalRoot.id).toString();
                var name = milkOption[index];
                var isDefault = (index === 0);
                var variant = buildVariant(id, name, isDefault);
                variantGroup.variants.push(variant);
            }
            groups.push(variantGroup);
            if (catalRoot.subType != 3675) {
                item.addon_groups = getAddOnGroups(catal, unitData, isCombo, null, null);
            }
        }
        return groups;
    }

    /* function addCompositeProducts(product, itemId, unitData) {
         var groups = [];
         if (product.prices[0].recipe != null && product.prices[0]
             .recipe.ingredient.compositeProduct != null) {
             var variantGroup = Object.assign({}, variantGroupObj);
             variantGroup.id = product.id + "_" + 'composite_product';
             variantGroup.name = 'Composite Product';
             variantGroup.order = 0;
             variantGroup.variants = [];
             product.prices[0].recipe.ingredient.compositeProduct.details.map(function (copItem, ind) {
                 copItem.menuProducts.map(function (menuItem, index) {
                     var id = product.id + "_" + menuItem.product.productId;
                     var name = menuItem.product.name;
                     var isDefault = (index === 0);

                     var variant = buildVariant(id, name, isDefault);
                     variantGroup.variants.push(variant);
                 });
             });
             groups.push(variantGroup);
         }
         return groups;
     }*/

    function buildVariant(id, name, isDefault) {
        var variant = Object.assign({}, variantObj);
        variant.id = id;
        variant.name = name;
        variant.price = 0;
        variant.default = isDefault;
        variant.order = 1;
        variant.in_stock = 1;
        variant.is_veg = 1;
        variant.gst_details = null;
        return variant;
    }

    function getDimensionDescription(dimension, dimensionDescriptor) {
        if (dimensionDescriptor) {
            return dimensionDescriptor;
        }
        var desc = "";
        switch (dimension) {
            case "MiniKetli":
                desc = "Serves 2, 250ml";
                break;
            case "ChotiKetli":
                desc = "Serves 4, 400ml";
                break;
            case "BadiKetli":
                desc = "Serves 10, 1000ml";
                break;
        }
        return desc;
    }

    function getAddOnGroups(product, unitData, isCombo, metaData, menuRecomendationData, dcDimension) {
        var groups = [];
        if (([10, 11, 12, 50].indexOf(product.id) >= 0)
            || service.addonsMap[product.id] != null
            && service.addonsMap[product.id].length > 0) {
            var group = angular.copy(addOnGroup);
            var displayName = "Addons";
            group.id = product.id + "_" + displayName;
            if (dcDimension != null) {
                group.id += "_" + dcDimension;
            }
            group.name = displayName;
            group.order = 1;
            var addOnsList = ([10, 11, 12, 50].indexOf(product.id) >= 0)
                ? service.addonsMap[11] : service.addonsMap[product.id];

            group.addon_min_limit = 0;
            group.addon_free_limit = addOnsList.length;
            group.addon_limit = addOnsList.length;
            group.addons = null;
            if (product.type === 5) {
                var addOnList = [];
                addOnsList.map(function (add, indexs) {
                    var addOnProduct = add.product;
                    var addOnIds = [];
                    if (addOnIds.indexOf(addOnProduct.productId) === -1) {
                        addOnIds.push(addOnProduct.productId);
                        var addOn = Object.assign({}, addOnObj);
                        addOn.id = product.id + "_" + add.product.productId;
                        if (dcDimension != null) {
                            addOn.id += "_" + dcDimension;
                        }
                        addOn.name = addOnProduct.name;
                        addOn.price = 0;
                        addOn.is_veg = true;
                        addOn.in_stock = 1;
                        addOn.order = addOnsList.length - indexs;
                        addOn.is_default = null;
                        addOn.gst_details = null;
                        addOnList.push(addOn);
                    }
                });
                group.addons = addOnList;
            }
            group.addons.sort(customfunction);
            groups.push(group);
        }
        if (product.prices[0] != null && !isCombo) {
            var dimension = product.prices[0];
            if (dimension.recipe != null) {
                if (menuRecomendationData != null) {
                    var productId = product.id.toString().includes("_") ? product.id.split("_")[0] : product.id;
                    var data = menuRecomendationData[productId];
                    if (data != null) {
                        var group = angular.copy(addOnGroup);
                        var displayName = "Best Paired With";
                        group.id = "r_" + product.id + "_AddOn";
                        group.name = "Best Paired With";
                        group.order = 2;
                        group.addon_min_limit = 0;
                        // group.addon_free_limit = ;
                        // group.addon_limit = ;
                        var addOnList = [];
                        data.map(function (recommendedProd) {
                            var addOn = Object.assign({}, addOnObj);
                            var reccmndActualProd = getNewActualProd(unitData, recommendedProd);
                            if (reccmndActualProd != null && !angular.isUndefined(reccmndActualProd)) {
                                var packagingCharge = getPackagingCharge(metaData, reccmndActualProd, null);
                                var price = getRecommendedNewProdPrice(unitData, recommendedProd, reccmndActualProd);
                                if (price != null && !angular.isUndefined(price)) {
                                    addOn.id = "r_" + recommendedProd.productId + "_" + product.id;
                                    addOn.name = reccmndActualProd.name;
                                    addOn.price = parseFloat(packagingCharge) + price;
                                    addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                    addOn.in_stock = 1;
                                    // addOn.order = addOnsList.length - indexs;
                                    addOn.is_default = null;
                                    addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode, unitData.taxes);
                                    addOnList.push(addOn);
                                }
                            }

                        });
                        if (addOnList.length > 1) {
                            group.addons = addOnList;
                            groups.push(group);
                        }
                    }
                }
                /*if(dimension.recipe.recommendations.length != 0){
                     var group = angular.copy(addOnGroup);
                     var displayName = "Best Paired With";
                     group.id = "r_" + product.id + "_AddOn";
                     group.name = "Best Paired With";
                     group.order = 2;
                     group.addon_min_limit = 0;
                    // group.addon_free_limit = ;
                    // group.addon_limit = ;
                     var addOnList = [];
                     dimension.recipe.recommendations.map(function (recommendedProd){
                         var addOn = Object.assign({}, addOnObj);
                         var reccmndActualProd = getActualProd(unitData, recommendedProd);
                         if(reccmndActualProd != null && !angular.isUndefined(reccmndActualProd)){
                             var packagingCharge = getPackagingCharge(metaData, reccmndActualProd, null);
                             var price = getRecommendedProdPrice(unitData, recommendedProd, reccmndActualProd);
                                 if(price != null && !angular.isUndefined(price)){
                                 addOn.id = "r_" + recommendedProd.product.productId + "_" + product.id;
                                 addOn.name = getProductName(recommendedProd.product.name, recommendedProd.product.productId);
                                 addOn.price = parseFloat(packagingCharge) + price;
                                 addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                 addOn.in_stock = 1;
                                // addOn.order = addOnsList.length - indexs;
                                 addOn.is_default = null;
                                 addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode, unitData.taxes);
                                 addOnList.push(addOn);
                                 }
                            }
                     });
                     if(addOnList.length > 1){
                         group.addons = addOnList;
                         groups.push(group);
                       }
                }*/
            }
        }
        if (product.subType == 3675) {
            if (product.prices[0] != null) {
                var dimension = product.prices[0];
                if (dimension.recipe != null) {
                    if (dimension.recipe.ingredient.compositeProduct != null) {
                        dimension.recipe.ingredient.compositeProduct.details.map(function (details, index) {
                            if (index == 0) {
                                if (details.menuProducts != null) {
                                    details.menuProducts.map(function (recommendedProd) {
                                        var reccmndActualProd = getActualProd(unitData, recommendedProd);
                                        if (reccmndActualProd.id == 10) {
                                            var group = angular.copy(addOnGroup);
                                            var displayName = "Addons";
                                            group.id = reccmndActualProd.id + "_" + displayName;
                                            group.name = displayName;
                                            group.order = 3;
                                            var addOnsList = ([10, 11, 12, 50].indexOf(reccmndActualProd.id) >= 0)
                                                ? service.addonsMap[11] : service.addonsMap[reccmndActualProd.id];

                                            group.addon_min_limit = 0;
                                            group.addon_free_limit = addOnsList.length;
                                            group.addon_limit = addOnsList.length;
                                            group.addons = null;
                                            if (reccmndActualProd.type === 5) {
                                                var addOnList = [];
                                                addOnsList.map(function (add, indexs) {
                                                    var addOnProduct = add.product;
                                                    var addOnIds = [];
                                                    if (addOnIds.indexOf(addOnProduct.productId) === -1) {
                                                        addOnIds.push(addOnProduct.productId);
                                                        var addOn = Object.assign({}, addOnObj);
                                                        addOn.id = reccmndActualProd.id + "_" + add.product.productId;
                                                        addOn.name = addOnProduct.name;
                                                        addOn.price = 0;
                                                        addOn.is_veg = true;
                                                        addOn.in_stock = 1;
                                                        addOn.order = addOnsList.length - indexs;
                                                        addOn.is_default = null;
                                                        addOn.gst_details = null;
                                                        addOnList.push(addOn);
                                                    }
                                                });
                                                group.addons = addOnList;
                                            }
                                            group.addons.sort(customfunction);
                                            groups.push(group);
                                        }
                                    });
                                }
                            } else {
                                var group = angular.copy(addOnGroup);
                                if (index == 1) {
                                    group.name = "Food";
                                    group.order = 2;
                                } else {
                                    group.name = "Others";
                                    group.order = 1;
                                }
                                group.addon_limit = 1;
                                group.id = "r_" + product.id + "_AddOn";
                                group.addon_min_limit = 1;
                                group.addon_free_limit = 1;
                                var addOnList = [];
                                if (details.menuProducts != null) {
                                    details.menuProducts.map(function (recommendedProd) {
                                        var addOn = Object.assign({}, addOnObj);
                                        var reccmndActualProd = getActualProd(unitData, recommendedProd);
                                        if (reccmndActualProd != null && !angular.isUndefined(reccmndActualProd)) {
                                            // var packagingCharge = getPackagingCharge(metaData, reccmndActualProd, null);
                                            var price = getRecommendedProdPrice(unitData, recommendedProd, reccmndActualProd);
                                            if (price != null && !angular.isUndefined(price)) {
                                                price = recommendedProd.quantity > 1 ? price * recommendedProd.quantity : price;
                                                addOn.id = "r_" + recommendedProd.product.productId + "_" + product.id;
                                                addOn.name = getProductName(recommendedProd.product.name, recommendedProd.product.productId);
                                                addOn.name = recommendedProd.quantity > 1 ? (addOn.name + " x " + recommendedProd.quantity) : addOn.name;
                                                var discountedPrice = getDiscountedPrice(details.discount, price);
                                                var packagingCharge = getComboPackaginCharges(metaData, discountedPrice);
                                                addOn.price = parseFloat((parseFloat(packagingCharge) + discountedPrice).toFixed(2));
                                                addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                                addOn.in_stock = 1;
                                                // addOn.order = addOnsList.length - indexs;
                                                addOn.is_default = null;
                                                addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode, unitData.taxes);
                                                addOnList.push(addOn);
                                            }
                                        }
                                    });
                                    if (addOnList.length > 1) {
                                        group.addons = addOnList;
                                        groups.push(group);
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
        if (product.subType == 3676) {
            if (product.prices[0] != null) {
                var dimension = product.prices[0];
                if (dimension.recipe != null) {
                    if (dimension.recipe.ingredient.compositeProduct != null) {
                        dimension.recipe.ingredient.compositeProduct.details.map(function (details, index) {
                            if (index != 0) {
                                var group = angular.copy(addOnGroup);
                                if (index == 1) {
                                    group.name = "Food";
                                    group.order = 1;
                                } else {
                                    group.name = "Others";
                                    group.order = 0;
                                }
                                group.addon_limit = 1;
                                group.id = "r_" + product.id + "_" + "_AddOn";
                                group.addon_min_limit = 1;
                                group.addon_free_limit = 1;
                                var addOnList = [];
                                if (details.menuProducts != null) {
                                    details.menuProducts.map(function (recommendedProd) {
                                        var addOn = Object.assign({}, addOnObj);
                                        var reccmndActualProd = getActualProd(unitData, recommendedProd);
                                        if (reccmndActualProd != null && !angular.isUndefined(reccmndActualProd)) {
                                            //var packagingCharge = getPackagingCharge(metaData, reccmndActualProd, null);
                                            var price = getRecommendedProdPrice(unitData, recommendedProd, reccmndActualProd);
                                            if (price != null && !angular.isUndefined(price)) {
                                                addOn.id = "r_" + recommendedProd.product.productId + "_" + product.id;
                                                addOn.name = getProductName(recommendedProd.product.name, recommendedProd.product.productId);
                                                var discountedPrice = getDiscountedPrice(details.discount, price);
                                                var packagingCharge = getComboPackaginCharges(metaData, discountedPrice);
                                                addOn.price = parseFloat((parseFloat(packagingCharge) + discountedPrice).toFixed(2));
                                                addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                                addOn.in_stock = 1;
                                                // addOn.order = addOnsList.length - indexs;
                                                addOn.is_default = null;
                                                addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode, unitData.taxes);
                                                if (index == 0) {
                                                    var check = false;
                                                    reccmndActualProd.prices[0].recipe.ingredient.variants.map(function (varProducts) {
                                                        if (varProducts.product.name == "Sugar Syrup") {
                                                            check = true;
                                                        }
                                                    });
                                                    if (check) {
                                                        addOnList.push(addOn);
                                                    }
                                                } else {
                                                    addOnList.push(addOn);
                                                }
                                            }
                                        }
                                    });
                                    if (addOnList.length > 1) {
                                        group.addons = addOnList;
                                        groups.push(group);
                                    }
                                }
                            }
                        });
                        /*  var group = angular.copy(addOnGroup);
                          group.name = "Sugar";
                          group.order = 2;
                          group.addon_limit = 1;
                          group.id = "rd_" + product.id + "_" + "Sugar";
                          group.addon_min_limit = 0;
                          var addOnList = [];
                          ["Regular Sugar", "No Sugar"].map(function (varnt, ind) {
                              var addOn = Object.assign({}, addOnObj);
                              addOn.id = "rd_" + product.id + "_" + varnt;
                              addOn.name = varnt;
                              addOn.price = 0;
                              addOn.is_veg = true;
                              addOn.in_stock = 1;
                              addOn.order = 1;
                              addOn.is_default = null;
                              addOn.gst_details = null;
                              addOnList.push(addOn);
                          });
                          group.addons = addOnList;
                          groups.push(group);*/
                    }
                }
            }
        }
        return groups;
    }

    function getDiscountedPrice(discountValue, price) {
        var comboPrice = 0;
        if (discountValue != null && discountValue != 0) {
            comboPrice = price - (price * (discountValue / 100)).toFixed(2);
        } else {
            comboPrice = price;
        }
        return comboPrice;
    }

    function getRecommendedProdPrice(unitData, recommendedProd, reccmndActualProd) {
        var priceProd;
        var recomendedProdDimension;
        if (recommendedProd.dimension == null) {
            recomendedProdDimension = "None";
        } else {
            recomendedProdDimension = recommendedProd.dimension.name;
        }
        reccmndActualProd.prices.map(function (pricess) {
            if (pricess.dimension == recomendedProdDimension) {
                priceProd = pricess.price;
            }
        });
        return priceProd;
    }

    function getRecommendedNewProdPrice(unitData, recommendedProd, reccmndActualProd) {
        var priceProd;
        if (recommendedProd.dimension != null) {
            reccmndActualProd.prices.map(function (pricess) {
                if (pricess.dimension == recommendedProd.dimension) {
                    priceProd = pricess.price;
                }
            });
        } else {
            priceProd = reccmndActualProd.prices[0].price;
        }
        return priceProd;
    }

    function getActualProd(unitData, recommendedProd) {
        var actualProd;
        unitData.products.map(function (prod) {
            if (prod.id == recommendedProd.product.productId) {
                actualProd = angular.copy(prod);
            }
        });
        return actualProd;
    }

    function getNewActualProd(unitData, recommendedProd) {
        var actualProd;
        unitData.products.map(function (prod) {
            if (prod.id == recommendedProd.productId) {
                actualProd = angular.copy(prod);
            }
        });
        return actualProd;
    }

    function isVeg(type) {
        var isVeg = true;
        if (type == null) {
            return isVeg;
        }
        if (type !== "VEG") {
            isVeg = false;
        }
        return isVeg;
    }

    function prepareAddOnsMap(unitData) {
        service.addonsMap = {};
        unitData.products.map(function (prod) {
            if (prod.type === 5 && prod.subType === 501) {
                service.addonsMap[prod.id] = prod.prices[0].recipe.addons;
            }
        });
    }

    function createProductMap(unitData) {
        var productMap = {};
        unitData.products.map(function (product) {
            productMap[product.id] = product;
        });
        return productMap;
    }

    function getTaxValue(code, taxCode, taxes) {
        var taxValue = 0;
        if (taxCode == "COMBO") {
            taxValue = 2.5;
        } else {
            Object.keys(taxes).map(function (key) {
                var tax = taxes[key];
                if (tax.taxCode === taxCode) {
                    taxValue = parseFloat(tax.state[code]);
                }
            });
        }
        return taxValue;
    }

    function getGSTDetails(taxCode, taxes) {
        var gstDetails = Object.assign({}, taxGSTDetailObj);
        gstDetails.igst = 0;
        gstDetails.sgst = getTaxValue("sgst", taxCode, taxes);
        gstDetails.cgst = getTaxValue("cgst", taxCode, taxes);
        gstDetails.inclusive = false;
        return gstDetails;
    }

    function getPackagingCharge(metadata, product, dcDimension) {
        var packagingCharge = 0;
        if (metadata.packagingType === "PERCENTAGE") {
            var itemPrice = getItemPriceForPackaging(product, dcDimension);
            if (itemPrice == null) {
                itemPrice = 0;
            }
            packagingCharge = ((metadata.packagingValue / 100) * itemPrice).toFixed(2);
            return packagingCharge;
        } else if (metadata.packagingType === "FIXED") {
            packagingCharge = metadata.packagingValue.toFixed(2);
            return parseInt(packagingCharge);
        }
    }

    function getComboPackaginCharges(metadata, itemPrice) {
        var packagingCharge = 0;
        if (metadata.packagingType === "PERCENTAGE") {
            packagingCharge = ((metadata.packagingValue / 100) * itemPrice).toFixed(2);
            return packagingCharge;
        } else if (metadata.packagingType === "FIXED") {
            packagingCharge = metadata.packagingValue.toFixed(2);
            return parseInt(packagingCharge);
        }
    }

    function getItemPrice(product, dcDimension) {
        var price = 0;
        if (product.prices.length === 1) {
            price = product.prices[0].price;
        } else {
            if(product.type == 5) {
                product.prices.map(function (priceObj) {
                    if (([10, 1282].indexOf(product.id) >= 0) && dcDimension != null) {
                        if (priceObj.dimension == dcDimension) {
                            price = priceObj.price;
                        }
                    } else {
                        price = product.prices[0].price;
                    }
                });
            } else {
                product.prices.map(function (priceObj) {
                    if (([10, 1282].indexOf(product.id) >= 0) && dcDimension != null) {
                        if (priceObj.dimension == dcDimension) {
                            price = priceObj.price;
                        }
                    } else if (price == null || price == 0) {
                        price = priceObj.price;
                    } else {
                        if (price > priceObj.price) {
                            price = priceObj.price;
                        }
                    }
                });
            }
        }
        return price;
    }

    function getItemPriceForPackaging(product, dcDimension) {
        var price = null;
        var pid = parseInt(product.id);
        if (([10, 1282].indexOf(pid) >= 0) && product.prices.length > 1) {
            if (dcDimension != null) {
                product.prices.map(function (pricex) {
                    if (pricex.dimension == dcDimension) {
                        price = pricex.price;
                    }
                });
                if (price == null) {
                    price = product.prices[1].price;
                }
            } else {
                price = product.prices[0].price;
            }
        } else {
            if (product.prices.length === 1) {
                price = product.prices[0].price;
            } else {
                product.prices.map(function (priceObj) {
                    if (price == null) {
                        price = priceObj.price;
                    } else {
                        if (price > priceObj.price) {
                            price = priceObj.price;
                        }
                    }
                });
            }
        }
        return price;
    }

    function getProductAlias(product) {
        var alias = null;
        product.prices.map(function (price) {
            if (price.aliasProductName != null) {
                alias = price.aliasProductName;
            }
        });
        return alias;
    }

    function getProductName(productName) {
        if (productName.indexOf("Kulhad Wali") != -1) {
            return productName.replace("Kulhad Wali", "");
        } else if (productName.indexOf("Kulhad") != -1 && productName.indexOf("Kulhad Chai") < 0) {
            return productName.replace("Kulhad", "");
        } else {
            return productName;
        }
    }

    function getProductDescription(productDescription) {
        if (productDescription.indexOf("served in a Kulhad") != -1) {
            return productDescription.replace("served in a Kulhad", "");
        } else if (productDescription.indexOf("Kulhad") != -1) {
            return productDescription.replace("Kulhad", "");
        } else {
            return productDescription;
        }
    }

    function createFilteredName(name) {
        name = name.replace(/[^\w\s]/gi, '').replaceAll(" ", "");
        return name;
    }

    return service;
}]);