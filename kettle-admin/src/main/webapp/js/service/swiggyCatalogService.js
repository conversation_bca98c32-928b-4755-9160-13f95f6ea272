/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.service('swiggyCatalogService', ['AppUtil', function (AppUtil) {

    var service = {};
    service.errorList = [];

    var catalogItemObj = {
        "restaurantId": null,
        "mainCategoryId": null,
        "mainCategoryName": "Hot Beverages",
        "mainCategoryOrder": 99,
        "subCategoryId": null,
        "subCategoryName": "Indian Chai",
        "subCategoryOrder": 1,
        "itemId": null,
        "Name": "<PERSON><PERSON> (<PERSON><PERSON>)",
        "description": "",
        "Parent": "I1",
        "Variant Group Id": "",
        "Variant Group Name": "",
        "Variant Group Order": "",
        "Variant Id": "",
        "Variant Default Value": "",
        "Order": 0,
        "in_stock": 1,
        "price": null,
        "veg_egg_non": "veg",
        "packingCharges": "",
        "is_spicy": 0,
        "serves how many": 1,
        "Service_Charges (%)": 0,
        "Item_SGST": 0.025,
        "Item_CGST": 0.025,
        "Item_IGST": 0,
        "Item_inclusive": 0,
        "Slab Count": 1,
        "Disable": 0,
        "Preparation Style": "",
        "Monday Open 1": "",
        "Monday Close 1": "",
        "Monday Open 2": "",
        "Monday Close 2": "",
        "Monday Open 3": "",
        "Monday Close 3": "",
        "Tuesday Open 1": "",
        "Tuesday Close 1": "",
        "Tuesday Open 2": "",
        "Tuesday Close 2": "",
        "Tuesday Open 3": "",
        "Tuesday Close 3": "",
        "Wednesday Open 1": "",
        "Wednesday Close 1": "",
        "Wednesday Open 2": "",
        "Wednesday Close 2": "",
        "Wednesday Open 3": "",
        "Wednesday Close 3": "",
        "Thursday Open 1": "",
        "Thursday Close 1": "",
        "Thursday Open 2": "",
        "Thursday Close 2": "",
        "Thursday Open 3": "",
        "Thursday Close 3": "",
        "Friday Open 1": "",
        "Friday Close 1": "",
        "Friday Open 2": "",
        "Friday Close 2": "",
        "Friday Open 3": "",
        "Friday Close 3": "",
        "Saturday Open 1": "",
        "Saturday Close 1": "",
        "Saturday Open 2": "",
        "Saturday Close 2": "",
        "Saturday Open 3": "",
        "Saturday Close 3": "",
        "Sunday Open 1": "",
        "Sunday Close 1": "",
        "Sunday Open 2": "",
        "Sunday Close 2": "",
        "Sunday Open 3": "",
        "Sunday Close 3": "",
        "delete": "",
        "external_id": "",
        "restuarantCategoryName": "",
        "restuarantSubCategoryName": "",
        "restuarantType": "",
        "Eligible For Long Distance": 1,
        "Item type": "REGULAR_ITEM"
    };

    var addonItemObj = {
        "Rest Id": 20784,
        "Items Id": null,
        "Addon Id": null,
        "Addon Name": "Regular Sugar",
        "Addon Order": 0,
        "Addon Price": 0,
        "Addon IsVeg": 1,
        "Addon Instock": 1,
        "AddonGroup Name": "Preparation Type",
        "Delete": "",
        "external_addon_id": "",
        "Addon_SGST": 0,
        "Addon_CGST": 0,
        "Addon_IGST": 0,
        "Addon_inclusive": ""
    };

    service.prepareCatalog = prepareCatalog;
    service.unitId = null;
    service.itemList = [];
    service.addonIds = [];
    service.addonList = [];

    function prepareCatalog(unitId, unitData, metadata, menuSequence, productImages) {
        try {
            service.errorList = [];
            var unit = angular.copy(unitData);
            unit.packagingType = metadata.packagingType;
            unit.packagingValue = metadata.packagingValue;
            //createCatalog(unitId, unit, menuSequence);
            service.itemList = [];
            service.addonIds = [];
            service.addonList = [];
            service.unmappedProducts = [];
            service.unitId = unitId;
            var index = 1;

            menuSequence.productGroupSequences.map(function (cat) {
                cat.subGroups.map(function (subCat) {
                    subCat.productSequenceList.sort(function (a, b) {
                        return a.productIndex - b.productIndex;
                    });
                });
                cat.subGroups.sort(function (a, b) {
                    return a.groupIndex - b.groupIndex;
                });
            });
            menuSequence.productGroupSequences.sort(function (a, b) {
                return a.groupIndex - b.groupIndex;
            });

            menuSequence.productGroupSequences.map(function (cat) {
                cat.subGroups.map(function (subCat) {
                    subCat.productSequenceList.map(function (prodSequence) {
                        unit.products.map(function (product) {
                            if (product.id == prodSequence.product.id) {
                                if ([10, 1205, 1282].indexOf(product.id) >= 0) {
                                    var prices = [];
                                    product.prices.map(function (priceObj) {
                                        if (priceObj.dimension == 'BadiKetli') {
                                            var prod = Object.assign({}, product);
                                            prod.id = product.id + "_" + priceObj.dimension.replace(/([a-z])/g, '');
                                            prod.name = product.name + priceObj.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(priceObj.dimension) + ")";
                                            prod.prices = [];
                                            prod.prices.push(priceObj);
                                            index = addProduct(unitId, unit, prod, cat, subCat, prodSequence, index, productImages);
                                        } else {
                                            prices.push(priceObj);
                                        }
                                    });
                                    var prod = Object.assign({}, product);
                                    var priceObj = prices[0];
                                    prod.id = product.id + "_" + priceObj.dimension.replace(/([a-z])/g, '');
                                    prod.name = product.name;
                                    prod.prices = [];
                                    prod.prices = prices;
                                    prod.prices.sort(function (a, b) {
                                        return a.price - b.price;
                                    });
                                    index = addProduct(unitId, unit, prod, cat, subCat, prodSequence, index, productImages);
                                } else {
                                    if ([11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0) {
                                        index = addProduct(unitId, unit, product, cat, subCat, prodSequence, index, productImages);
                                    }
                                }
                            }
                        });
                    });
                });
            });

            unit.products.map(function (product) {
                if (product.classification == "MENU" && product.billType != "ZERO_TAX" && product.type != 12) {
                    var found = false;
                    menuSequence.productGroupSequences.map(function (cat) {
                        cat.subGroups.map(function (subCat) {
                            subCat.productSequenceList.map(function (prodSequence) {
                                if (!found && product.id == prodSequence.product.id) {
                                    found = true;
                                }
                            });
                        });
                    });
                    if (!found && [11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0) {
                        service.unmappedProducts.push({
                            id: product.id,
                            name: product.name
                        });
                    }
                }
            });

            service.addonList.map(function (addon) {
                var ids = addon["Items Id"].join(", ");
                addon["Items Id"] = ids;
            });
            if (!showErrorList()) {
                return {items: service.itemList, addons: service.addonList, unmappedProducts: service.unmappedProducts};
            } else {
                return {items: [], addons: [], unmappedProducts: []};
            }
        } catch (e) {
            console.log(e);
            if (!showErrorList()) {
                bootbox.alert(e);
            }
            return {items: [], addons: [], unmappedProducts: []};
        }

    }

    function showErrorList() {
        if (service.errorList.length > 0) {
            var errors = "<ul>";
            service.errorList.map(function (err) {
                errors += "<li>" + err + "</li>"
            });
            errors += "</ul>";
            bootbox.alert(errors);
            return true;
        } else {
            return false;
        }
    }

    function createCatalog(unitId, unit, menuSequence) {
        service.itemList = [];
        service.addonIds = [];
        service.addonList = [];
        var index = 1;
        unit.products.map(function (product) {
            if (product.classification == "MENU" && product.billType != "ZERO_TAX" && product.type != 12) {
                if ([10, 1205, 1282].indexOf(product.id) >= 0) {
                    product.prices.map(function (priceObj) {
                        var prod = Object.assign({}, product);
                        prod.id = product.id + "_" + priceObj.dimension.replace(/([a-z])/g, '');
                        prod.name = product.name + priceObj.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(priceObj.dimension) + ")";
                        prod.prices = [];
                        prod.prices.push(priceObj);
                        index = addProduct(unit, prod, menuSequence, index);
                    })
                } else {
                    if ([11, 12, 50].indexOf(product.id) < 0) {
                        index = addProduct(unit, product, menuSequence, index);
                    }
                }
            }
        });
        AppUtil.JSONToCSVConvertor(service.itemList, "swiggy_catalog_" + unitId, true);

        service.addonList.map(function (addon) {
            var ids = addon["Items Id"].join(", ");
            addon["Items Id"] = ids;
        });

        AppUtil.JSONToCSVConvertor(service.addonList, "swiggy_catalog_" + service.unitId + "_addons", true);
    }

    function addProduct(unitId, unit, product, cat, subCat, prodSequence, index, productImages) {
        var item = angular.copy(catalogItemObj);
        item.restaurantId = unitId;
        item.mainCategoryId = cat.groupId;
        item.mainCategoryName = cat.groupName;
        item.mainCategoryOrder = cat.groupIndex;
        item.subCategoryId = subCat.groupId;
        item.subCategoryName = subCat.groupName;
        item.subCategoryOrder = subCat.groupIndex;
        //item.external_id = ""+cat.groupId+subCat.groupId+product.id;
        item.external_id = product.id + "_" + cat.groupId + "_" + subCat.groupId;
        item.Name = product.name;
        item.description = product.description;
        item.Parent = "I" + index;
        item.Order = prodSequence.productIndex;
        item.price = getItemPrice(product);
        item.veg_egg_non = getVegNonVeg(product.attribute);
        item.Item_SGST = getTaxPercent("sgst", product.taxCode, unit.taxes);
        item.Item_CGST = getTaxPercent("cgst", product.taxCode, unit.taxes);
        item.Item_IGST = 0; //getTaxPercent("igst", product.taxCode, unit.taxes);
        if (unit.packagingType == "PERCENTAGE") {
            item.packingCharges = ((unit.packagingValue / 100) * getItemPriceForPackaging(product))
        } else if (unit.packagingType == "FIXED") {
            item.packingCharges = unit.packagingValue;
        }
        service.itemList.push(item);
        addVariantItems(product, item, index, unit);
        index++;
        return index;
    }

    function addVariantItems(product, item, index, unit) {
        addDimension(product, item, index, unit);
        if (product.prices[0].recipe != null) {
            addIngredientVariants(product, item, index);
            addIngredientProducts(product, item, index);
            addIngredientComposites(product, item, index);
            addAddons(product, item, index);
        }
    }

    function addDimension(product, item, index, unit) {
        //var unit = AppUtil.unitDetails;
        if (product.prices.length > 1) {
            product.prices.map(function (price, ind) {
                if (price.recipe != null) {
                    var variant = getVariantObj(index);
                    variant.Name = price.dimension.replace(/([A-Z])/g, ' $1').substr(1);
                    variant.Name += " (" + getDimensionDescription(price.dimension) + ")";
                    variant["Variant Group Id"] = product.id + "_size";
                    variant["Variant Group Name"] = "Size";
                    variant["Variant Group Order"] = "1";
                    variant["external_id"] = product.id + "_" + price.dimension.substr(0, 1);
                    variant["Variant Default Value"] = (ind == 0) ? 1 : 0;
                    variant.price = parseFloat(price.price - item.price).toFixed(2);
                    variant.Item_SGST = getTaxPercent("sgst", product.taxCode, unit.taxes);
                    variant.Item_CGST = getTaxPercent("cgst", product.taxCode, unit.taxes);
                    variant.Item_IGST = 0; //getTaxPercent("igst", product.taxCode, unit.taxes);
                    service.itemList.push(variant);
                }
            });
        }
    }

    function getDimensionDescription(dimension) {
        var desc = "";
        switch (dimension) {
            case "MiniKetli":
                desc = "Serves 2, 250ml";
                break;
            case "ChotiKetli":
                desc = "Serves 4, 400ml";
                break;
            case "BadiKetli":
                desc = "Serves 10, 1000ml";
                break;
        }
        return desc;
    }

    function addIngredientVariants(product, item, index) {
        //var unit = AppUtil.unitDetails;
        var recipe = product.prices[0].recipe;
        var tInd = 0;
        recipe.ingredient.variants.map(function (varItem, ind) {
            varItem.details.map(function (varDetail) {
                var displayName = varItem.product.displayName || "Option";
                var variant = getVariantObj(index);
                variant.Name = varDetail.alias;
                variant["Variant Group Id"] = product.id + "_" + varItem.product.productId;
                variant["Variant Group Name"] = displayName;
                variant["Variant Group Order"] = index + ind;
                variant["external_id"] = product.id + "_" + item.subCategoryId + "_" + varDetail.productId + varDetail.alias.substr(0, 1)
                    + varDetail.alias.substr(varDetail.alias.length - 1) + displayName.length;
                variant["Variant Default Value"] = varDetail.defaultSetting ? 1 : 0;
                service.itemList.push(variant);
            });
            tInd = index + ind;
        });
        if (product.id.toString().match(/^(10_)/) || product.id.toString().match(/^(1282_)/)) {
            ["Regular Milk", "Full Doodh", "Doodh Kum", "Paani Kum"].map(function (varnt, ind) {
                var displayName = "Milk Option";
                var variant = getVariantObj(index);
                variant.Name = varnt;
                variant["Variant Group Id"] = product.id + "_" + "MLK_OPT";
                variant["Variant Group Name"] = displayName;
                variant["Variant Group Order"] = tInd + 1;
                variant["external_id"] = product.id + "_" + item.subCategoryId + "_" + varnt.substr(0, 1)
                    + varnt.substr(varnt.length - 1) + displayName.length;
                variant["Variant Default Value"] = (ind == 0) ? 1 : 0;
                service.itemList.push(variant);
            })
        }
    }

    function addIngredientProducts(product, item, index) {
        //var unit = AppUtil.unitDetails;
        var recipe = product.prices[0].recipe;
        recipe.ingredient.products.map(function (prodItem, ind) {
            prodItem.details.map(function (prodDetail) {
                var displayName = prodItem.display || "Option";
                var variant = getVariantObj(index);
                variant.Name = prodDetail.product.name;
                variant["Variant Group Id"] = product.id + "_" + displayName;
                variant["Variant Group Name"] = displayName;
                variant["Variant Group Order"] = index + ind;
                variant["external_id"] = product.id + "_" + item.subCategoryId + "_" + prodDetail.product.productId;
                variant["Variant Default Value"] = prodDetail.defaultSetting ? 1 : 0;
                service.itemList.push(variant);
            });
        });
    }

    function addIngredientComposites(product, item, index) {
        if (product.prices[0].recipe.ingredient.compositeProduct != null) {
            product.prices[0].recipe.ingredient.compositeProduct.details.map(function (copItem, ind) {
                copItem.menuProducts.map(function (menuItem, ind) {
                    var variant = getVariantObj(index);
                    variant.Name = menuItem.product.name;
                    variant["Variant Group Id"] = product.id + "_" + copItem.name;
                    variant["Variant Group Name"] = copItem.name;
                    variant["Variant Group Order"] = index + ind;
                    variant["external_id"] = product.id + "_" + item.subCategoryId + "_" + menuItem.product.productId;
                    variant["Variant Default Value"] = (ind == 0) ? 1 : 0;
                    service.itemList.push(variant);
                });
            });
        }
    }

    function addAddons(product, item, index) {
        if (product.type == 5) {
            product.prices[0].recipe.addons.map(function (add) {
                var productId = add.product.productId;
                if (service.addonIds.indexOf(productId) == -1) {
                    service.addonIds.push(productId);
                    var obj = angular.copy(addonItemObj);
                    obj["Rest Id"] = service.unitId;
                    obj["Items Id"] = [product.id];
                    obj["Addon Name"] = add.product.name;
                    obj["Addon Order"] = service.addonIds.length + 1;
                    obj["AddonGroup Name"] = "Addons";
                    obj["external_addon_id"] = productId;
                    service.addonList.push(obj);
                } else {
                    service.addonList.map(function (addon) {
                        if (addon.external_addon_id == add.product.productId) {
                            if (addon["Items Id"].indexOf(product.id) == -1) {
                                addon["Items Id"].push(product.id);
                            }
                        }
                    });
                }
            });
        }
    }

    function getVariantObj(index) {
        var variant = angular.copy(catalogItemObj);
        variant.restaurantId = service.unitId;
        variant.mainCategoryId = "";
        variant.mainCategoryName = "";
        variant.mainCategoryOrder = "";
        variant.subCategoryId = "";
        variant.subCategoryName = "";
        variant.subCategoryOrder = "";
        variant.itemId = "";
        variant.Name = "";
        variant.description = "";
        variant.Parent = "V" + index;
        variant["Variant Group Id"] = "";
        variant["Variant Group Name"] = "";
        variant["Variant Group Order"] = "";
        variant["Variant Id"] = "";
        variant["Variant Default Value"] = "";
        variant.price = 0;
        variant.veg_egg_non = "veg";
        variant.Item_SGST = 0;
        variant.Item_CGST = 0;
        variant.Item_IGST = 0;
        return variant;
    }

    function getItemPrice(product) {
        var price = null;
        if (product.prices.length == 1) {
            price = product.prices[0].price;
        } else {
            product.prices.map(function (priceObj) {
                if (price == null) {
                    price = priceObj.price;
                } else {
                    if (price > priceObj.price) {
                        price = priceObj.price;
                    }
                }
            });
        }
        return price;
    }

    function getItemPriceForPackaging(product) {
        var price = null;
        var pid = parseInt(product.id);
        if (([10, 1205, 1282].indexOf(pid) >= 0) && product.prices.length > 1) {
            price = product.prices[1].price;
        } else {
            if (product.prices.length == 1) {
                price = product.prices[0].price;
            } else {
                product.prices.map(function (priceObj) {
                    if (price == null) {
                        price = priceObj.price;
                    } else {
                        if (price > priceObj.price) {
                            price = priceObj.price;
                        }
                    }
                });
            }
        }
        return price;
    }

    function getVegNonVeg(type) {
        var ret = null;
        if (type == "VEG") {
            ret = "veg";
        } else if (type == "NON_VEG") {
            ret = "non-veg";
        } else {
            ret = "veg";
        }
        return ret;
    }

    function getTaxPercent(code, taxCode, taxes) {
        var percentage = 0;
        Object.keys(taxes).map(function (key) {
            var tax = taxes[key];
            if (tax.taxCode == taxCode) {
                percentage = parseFloat(parseFloat(tax.state[code]) / 100).toFixed(3);
            }
        });
        return percentage;
    }

    return service;
}]);
