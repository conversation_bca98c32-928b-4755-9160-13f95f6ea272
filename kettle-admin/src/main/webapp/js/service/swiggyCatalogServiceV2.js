/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
adminapp.service('swiggyCatalogServiceV1', ['AppUtil','$rootScope', function (AppUtil,$rootScope) {

    var service = {};
    service.prepareCatalog = prepareCatalog;
    service.updateDynamicPricing = updateDynamicPricing;
    service.prepareAddOnsMap = prepareAddOnsMap;
    service.preparePaidAddonsMap=preparePaidAddOnsMap;
    service.prepareTaxMap = prepareTaxMap;
    service.createProductMap = createProductMap;
    service.createDineInProductMap = createDineInProductMap;
    service.setMenuCategories = setMenuCategories;
    service.sortProductPrices = sortProductPrices;
    service.sortDesiChaiDimensions = sortDesiChaiDimensions;
    service.validateProductPricings = validateProductPricings;
    service.createProductCategoryMap = createProductCategoryMap;
    service.setMenuCatalogues = setMenuCatalogues;
    service.splitDesiChaiDimensions = splitDesiChaiDimensions;
    service.addNewCatalogue = addNewCatalogue;
    service.getVariantGroups = getVariantGroups;
    service.addIngredientVariants = addIngredientVariants;
    service.addIngredientProducts = addIngredientProducts;
    service.addComboVariantGroups = addComboVariantGroups;
    service.addNormalComboVariantGroups = addNormalComboVariantGroups;
    service.addHeroComboVariantGroups = addHeroComboVariantGroups;
    service.addSuperComboVariantGroups = addSuperComboVariantGroups;
    service.getAddOnGroups = getAddOnGroups;
    service.addComboAddonGroups = addComboAddonGroups;
    service.addNormalProductAddonGroups = addNormalProductAddonGroups;
    service.addFixedMealProductAddonGroups = addFixedMealProductAddonGroups;
    service.addUpsellingAddonGroups = addUpsellingAddonGroups;
    service.addHeroComboAddonGroups = addHeroComboAddonGroups;
    service.addSuperComboAddonGroups = addSuperComboAddonGroups;
    service.updateDesiChaiAsPerCustomProfiles = updateDesiChaiAsPerCustomProfiles;
    service.productPackagingMappings = {};
    service.productCityImageDetails = {};
    service.addonMode = true;
    service.addSuperCombo = addSuperCombo;
    service.setSuperCombos = setSuperCombos;
    service.errorList = [];
    service.addonsList = [];
    service.addonsMap = {};
    service.paidAddonsMap = {};
    service.paidAddonsIds = {};
    service.addonIds = [];
    service.brand = null;
    service.metadata = null;
    service.addPackaging = null;
    service.unitId = null;
    service.unitData = null;
    service.menuSequence = null;
    service.productMap = null;
    service.brand = null;
    service.menuRecommendationData = null;
    service.productImages = null;
    service.catalog = null;
    service.productCategoryMap = null;
    service.unmappedProducts = null;
    service.priceMap = {};
    $rootScope.priceMapForSuperCombo = {};
    $rootScope.combosMainPrice = {};
    $rootScope.variantPaidAddOnsList = [1000206,1000205, 1000204, 1000207,1000441,1000440,1000439,1000438,1000452,1000453,1000454,1000455];
    $rootScope.specialMilkPaidAddonList = [1000703,1000704,1000705,1000706,1000799,1000814,1000828,1000800,1000815,1000827];
    $rootScope.coffeeFlavours = [1000801,1000802,1000803,1000811,1000812,1000813,1000816,1000817,1000818];

    $rootScope.splitDesiChaiDimensionName = "Regular";

    var entity = {
        "main_categories": [],
        "items": [],
    };
    var mainCategoryObj = {
        "id": null,
        "name": null,
        "description": null,
        "order": null,
        "sub_categories": []
    };
    var subCategoryObj = {
        "id": null,
        "category_id": null,
        "name": null,
        "description": null,
        "order": null
    };
    var itemObj = {
        "id": null,
        "category_id": null,
        "sub_category_id": null,
        "name": null,
        "is_veg": null,
        "order": null,
        "description": null,
        "price": 0,
        "gst_details": {},
        "packing_charges": 0,
        "enable": null,
        "in_stock": null,
        "addon_free_limit": null,
        "addon_limit": null,
        "image_url": null,
        "recommended": null,
        "variant_groups": [],
        "addon_groups": [],
        "item_slots": [],
        "pricing_combinations": []
    };
    var taxGSTDetailObj = {
        "igst": 0,
        "sgst": 0,
        "cgst": 0,
        "inclusive": false,
        "gst_liability": "SWIGGY"
    };
    var variantGroupObj = {
        "id": null,
        "name": "",
        "order": null,
        "variants": []
    };
    var variantObj = {
        "id": null,
        "name": "",
        "price": 0,
        "default": true,
        "order": null,
        "in_stock": 1,
        "is_veg": null,
        "gst_details": {},
        "default_dependent_variant_id": null,
        "default_dependent_variant_group_id": null
    };
    var addOnGroup = {
        "id": null,
        "name": null,
        "addon_free_limit": null,
        "addon_limit": null,
        "addon_min_limit": null,
        "order": null,
        "addons": null
    };
    var addOnObj = {
        "id": null,
        "name": null,
        "price": 0,
        "is_veg": true,
        "in_stock": 1,
        "order": null,
        "is_default": null,
        "gst_details": null,
        "originalName" : null
    };
    var itemSlotObj = {
        "day_of_week": null,
        "open_time": null,
        "close_time": null
    };
    var pricingCombinationObj = {
        "variant_combination": [],
        "price": 0,
        "addon_combination": []
    };
    var variantCombinationObj = {
        "variant_group_id": null,
        "variant_id": null
    };
    var addOnCombinationObj = {
        "addon_group_id": null,
        "addon_id": null
    };

    var productPriceSuperCombo = {
        "price": null,
        "discount": null,
        "discountType": null,
        "originalPrice": null,
        "productId": null
    };

    var recommndMap = null;

    var milkOption = ["Regular Milk", "Full Doodh (Full Milk)", "Doodh Kum (Less Milk)", "Paani Kum (More Milk)"];

    function prepareCatalog(selectedUnit, unitData, metadata, menuSequence, menuRecomendationData, productImages,
    productPackagingMappings,productCityImageDetails) {
        try {
            service.errorList = [];
            var unitId = selectedUnit.id;
            var addPackaging = selectedUnit.addPackaging;
            var splitAllDesiChaiDimensions = selectedUnit.splitAllDesiChaiDimensions;
            var miniKetliDefault = selectedUnit.miniKetliDefault;
            var clubAllDesiChaiDimensions = selectedUnit.clubAllDesiChaiDimensions;
            service.selectedDimension = selectedUnit.selectedDimension;
            if(service.selectedDimension!=null){
                $rootScope.splitDesiChaiDimensionName  = service.selectedDimension;
            }else{
                $rootScope.splitDesiChaiDimensionName = "Regular";
            }
            if (unitId == null || unitData == null || metadata == null) {
                service.errorList.push("unit details should not be null");
                return;
            }
            if (addPackaging == null) {
                addPackaging = true;
            }
            if (splitAllDesiChaiDimensions == null || selectedUnit.clubAllDesiChaiDimensions == true) {
                splitAllDesiChaiDimensions = false;
            }
            if(clubAllDesiChaiDimensions == true) {
                miniKetliDefault = true;
            }
            service.unitId = unitId;
            service.unitData = unitData;
            service.menuSequence = menuSequence;
            service.metadata = metadata;
            service.addPackaging = addPackaging;
            service.menuRecommendationData = menuRecomendationData;
            service.productImages = productImages;
            service.splitAllDesiChaiDimensions = splitAllDesiChaiDimensions;
            service.clubAllDesiChaiDimensions = clubAllDesiChaiDimensions;
            service.productPackagingMappings = productPackagingMappings;
            service.productCityImageDetails = productCityImageDetails;
            service.miniKetliDefault = miniKetliDefault;
            if (service.miniKetliDefault == true) {
                service.sortDesiChaiDimensions();
            }
            setCityLevelImages(selectedUnit,productCityImageDetails);
            service.updateDynamicPricing();
            service.prepareAddOnsMap();
            service.preparePaidAddonsMap();
            service.prepareTaxMap();
            service.sortProductPrices();
            service.createProductMap();
            service.createDineInProductMap();
            //initializeRecommendationCategory();
            service.catalog = angular.copy(entity);
            service.catalog.items = [];
            service.setMenuCategories();
            //service.createProductCategoryList();
            service.createProductCategoryMap();
            var flag = service.validateProductPricings();
            if (!flag) {
                throw 500;
            }
            service.setMenuCatalogues();
            //console.log({'entity': catalog, 'unmappedProducts': service.unmappedProducts});
            if (!showErrorList()) {
                return {'entity': service.catalog, 'unmappedProducts': service.unmappedProducts};
            } else {
                return {'entity': angular.copy(entity), 'unmappedProducts': []};
            }
        } catch (e) {
            console.log(e);
            if (!showErrorList()) {
                bootbox.alert(e);
            }
            return {'entity': angular.copy(entity), 'unmappedProducts': []};
        }
    }


    function setCityLevelImages(selectedUnit,productCityImageDetails){
        var cityName = selectedUnit.city;
        Object.keys(service.productImages).forEach(function(productId){
           if(productCityImageDetails[productId] != null && productCityImageDetails[productId][cityName] != null){
               service.productImages[productId].gridLow.url = productCityImageDetails[productId][cityName].imageUrl;
           }
        });
    }

    function setSuperCombos(product, order) {
        addPriceForSuperCombo(product);
        var superCombo = service.addSuperCombo(product, order);
        return superCombo;
    }

    function showErrorList() {
        if (service.errorList.length > 0) {
            var errors = "<ul>";
            service.errorList.map(function (err) {
                errors += "<li>" + err + "</li>"
            });
            errors += "</ul>";
            bootbox.alert(errors);
            return true;
        } else {
            return false;
        }
    }

    function updateDynamicPricing() {
        if (service.metadata.dynamicPriceProfile != null) {
            var profile = service.metadata.dynamicPriceProfile;
            var strategy = profile.profileType;
            switch (strategy) {
                case "FLAT_AMOUNT":
                    profile.profileRangeValueDetails.map(function (rangeVal) {
                        service.unitData.products.map(function (product) {
                            if (product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.map(function (price) {
                                    price.originalPrice = price.originalPrice == null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var threshold = (profile.thresholdPercentage / 100) * price.price;
                                        if (rangeVal.deltaPrice < 0) {
                                            threshold = -threshold;
                                        }
                                        if (Math.abs(threshold) < Math.abs(rangeVal.deltaPrice)) {
                                            price.price += threshold;
                                        } else {
                                            price.price += rangeVal.deltaPrice;
                                        }
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "RANGE_FLAT_AMOUNT":
                    profile.profileRangeValueDetails.map(function (rangeVal) {
                        service.unitData.products.map(function (product) {
                            if (product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.map(function (price) {
                                    price.originalPrice = price.originalPrice == null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var threshold = (profile.thresholdPercentage / 100) * price.price;
                                        if (rangeVal.deltaPrice < 0) {
                                            threshold = -threshold;
                                        }
                                        if (Math.abs(threshold) < Math.abs(rangeVal.deltaPrice)) {
                                            price.price += threshold;
                                        } else {
                                            price.price += rangeVal.deltaPrice;
                                        }
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "PERCENTAGE_AMOUNT":
                    profile.profileRangeValueDetails.map(function (rangeVal) {
                        service.unitData.products.map(function (product) {
                            if (product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.map(function (price) {
                                    price.originalPrice = price.originalPrice == null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var deltaValue = (rangeVal.deltaPrice / 100) * price.price;
                                        price.price += deltaValue;
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "RANGE_PERCENTAGE_AMOUNT":
                    profile.profileRangeValueDetails.map(function (rangeVal) {
                        service.unitData.products.map(function (product) {
                            if (product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.map(function (price) {
                                    price.originalPrice = price.originalPrice == null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var deltaValue = (rangeVal.deltaPrice / 100) * price.price;
                                        price.price += deltaValue;

                                    }
                                });
                            }
                        });
                    });
                    break;
                case "SLASH_PRICING":
                    break;
                case "RANGE_SLASH_PRICING":
                    break;
            }

        }
    }

    function prepareAddOnsMap() {
        service.addonsMap = {};
        service.unitData.products.map(function (prod) {
            if ((prod.type === 5 && [501,502,503].indexOf(prod.subType) >= 0) || prod.type == 6) {
             if(prod.prices.length > 0){
                         service.addonsMap[prod.id] = prod.prices[0].recipe.addons;
             }

            }
        });
    }

    function preparePaidAddOnsMap(){
        // var mapIdName={"BK":"BadiKetli","CK":"ChotiKetli","MK":"MiniKetli","Reg":"Regular","None":"none"};
        service.paidAddonsMap = {};
        service.unitData.products.map(function (prod) {
            if (prod.prices != null && prod.prices.length > 0) {
                service.paidAddonsMap[prod.id] = [];
                prod.prices.map(function (price) {
                    if (price.recipe != null && price.recipe.options != null && price.recipe.options.length > 0) {

                        var opt = {}
                        var id = price.recipe.dimension.code;
                        opt[prod.id + "_" + id] = [];
                        price.recipe.options.map(function (option) {
                            if (service.paidAddonsMap[prod.id] != null && service.paidAddonsMap[prod.id].length >= 0) {
                                opt[prod.id + "_" + id].push(option);
                            }
                        })
                        service.paidAddonsMap[prod.id].push(opt);
                    }

                })

                if (service.paidAddonsMap[prod.id] == null || service.paidAddonsMap[prod.id].length == 0) {
                    delete service.paidAddonsMap[prod.id];
                }

                // service.paidAddonsMap[prod.id]=prod.prices[0].recipe.options;
            }
        })

        for (var key in service.paidAddonsMap) {
            if (service.paidAddonsMap[key] != null && service.paidAddonsMap[key].length>0) {
                service.paidAddonsMap[key].map(function (val) {
                    console.log(val);
                    var paidAddOnsArr =Object.values(val);
                    console.log(paidAddOnsArr);
                    paidAddOnsArr.map(function (ele) {
                        console.log(ele);
                        ele.map(function (elementsPA) {
                            console.log(elementsPA);
                            service.paidAddonsIds[elementsPA.productId]=-1;

                        })
                    })

                })
            }

        }

    }

    function prepareTaxMap() {
        service.taxMap = {};
        service.unitData.taxes.map(function (tax) {
            service.taxMap[tax.taxCode] = tax;
        })
    }

    function sortProductPrices() {
        service.unitData.products.map(function (product) {
            product.prices.sort(function (a, b) {
                return a.price - b.price;
            });
        })
    }

    function createProductMap() {
        var productMap = {};
        service.unitData.products.map(function (product) {
            productMap[product.id] = product;
        });
        service.productMap = productMap;
    }

    function createDineInProductMap() {
        var productMap = {};
        service.metadata.dineInMenuProfile.products.forEach(function (product) {
            productMap[product.id] = product;
        });
        service.dineInProductMap = productMap;
        service.unitData.products.map(function (prod) {
            if (service.paidAddonsIds[prod.id] == -1 && prod.prices != null && prod.prices.length > 0
            && service.dineInProductMap[prod.id] != null && service.dineInProductMap[prod.id].prices != null && service.dineInProductMap[prod.id].prices.length > 0) {
                service.paidAddonsIds[prod.id] = prod.prices[0].price;
            }
        });
        console.log(service.paidAddonsIds);
    }

    /*function initializeRecommendationCategory() {
        recommndMap = new Map([[1033, false], [10, false], [80, false], [670, false], [640, false], [992, false], [1065, false], [1201, false],
            [1028, false], [1514, false], [1515, false], [1516, false], [1517, false], [1518, false], [1519, false], [1520, false], [1521, false]]);
    }*/

    function sortDesiChaiDimensions() {
        service.unitData.products.map(function (product) {
            if ([10, 1282].indexOf(product.id) >= 0) {
                product.prices = product.prices.sort(function (a, b) {
                    return a.price - b.price;
                })
            }
        });
    }

    function validateProductPricings() {
        var flag = true;
        service.productCategoryMap.map(function (categoryInfo) {
            service.unitData.products.map(function (product) {
                if (categoryInfo.productId === product.id) {
                    if (product.prices == null || product.prices.length === 0) {
                        flag = false;
                        service.errorList.push("Product Price not found for " + product.name);
                    } else {
                        product.prices.map(function (price) {
                            if (price.recipe == null) {
                                service.errorList.push("Product Recipe not found for " + product.name + " dimension: " + price.dimension);
                            }
                        })
                    }
                }
            });
        });
        return flag;
    }

    function createProductCategoryMap() {
        var productCategoryMap = [];
        service.menuSequence.productGroupSequences.sort(sortFunction);
        service.menuSequence.productGroupSequences.map(function (cat) {
            var categoryId = createFilteredName(cat.groupName); //cat.groupId;
            var categoryName = cat.groupName;
            cat.subGroups.sort(sortFunction);
            cat.subGroups.map(function (subCat) {
                var subCategoryId = createFilteredName(subCat.groupName); //subCat.groupId;
                var subCategoryName = subCat.groupName;
                subCat.productSequenceList.sort(function (a, b) {
                    return a.productIndex - b.productIndex;
                });
                subCat.productSequenceList.map(function (prodSequence) {
                    var product = service.productMap[prodSequence.product.id];
                    if (product == null) {
                        //service.errorList.push("Product " + prodSequence.product.name + " not mapped with this unit.");
                    }
//                     else if (product.subType == 3676) {
//                        service.errorList.push("Super combo " + product.name + " not supported by swiggy menu");
//                    }
                     else {
                        productCategoryMap.push({
                            'categoryId': categoryId,
                            'categoryName': categoryName,
                            'subCategoryId': subCategoryId,
                            'subCategoryName': subCategoryName,
                            'recommended': prodSequence.recommended,
                            'productId': prodSequence.product.id
                        });
                    }
                });
            });
        });
        service.productCategoryMap = productCategoryMap;
    }

    function setMenuCategories() {
        service.catalog.main_categories = [];
        service.menuSequence.productGroupSequences.sort(sortFunction);
        service.menuSequence.productGroupSequences.map(function (cat, index) {
            service.catalog.main_categories.push(addNewCategory(cat, index));
        });
    }

    function addNewCategory(cat, index) {
        var count = service.menuSequence.productGroupSequences.length;
        var category = angular.copy(mainCategoryObj);
        category.id = createFilteredName(cat.groupName);//cat.groupId;
        category.name = cat.groupName;
        category.description = cat.groupDescription;
        category.order = count - index;
        cat.subGroups.sort(sortFunction);
        cat.subGroups.map(function (subCat, index) {
            category.sub_categories.push(addNewSubCategory(subCat, cat, index));
        });
        //category.sub_categories.sort(customFunction);
        return category;
    }

    function customFunction(a, b) {
        return b.order - a.order;
    }

    function sortFunction(a, b) {
        return a.groupIndex - b.groupIndex;
    }

    function addNewSubCategory(subCat, cat, index) {
        var count = cat.subGroups.length;
        var subCategory = angular.copy(subCategoryObj);
        subCategory.id = createFilteredName(subCat.groupName); //subCat.groupId;
        subCategory.category_id = cat.groupId;
        subCategory.name = subCat.groupName;
        subCategory.description = subCat.groupDescription;
        subCategory.order = count - index;
        return subCategory;
    }

    function addSeperateMiniKetliDesiChaiForSwiggyBolt(product, productCategoryObj, order){
        var catalogEntry;
        var splitDimensionPrice = [];
                    var noSplitDimensionPrice = [];
                    var productsAdded = 0;
                    product.prices.map(function (price) {
                        if (price.dimension === "MiniKetli") {
                            splitDimensionPrice.push(angular.copy(price));
                        }
                    });
                    if (splitDimensionPrice.length > 0) {
                        var prodBk = angular.copy(product);
                        prodBk.id = prodBk.id + "_" + "MiniKetli" + "_" + "BOLT";
                        prodBk.prices = splitDimensionPrice;
                        catalogEntry = service.addNewCatalogue(prodBk, productCategoryObj, order,false);
                        catalogEntry.name = "Desi Chai Mini Kettle (Serves 1-2, 250ml)";
                        if(splitDimensionPrice[0].dimension == "Regular"){
                           catalogEntry.image_url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/DESI_CHAI_REGULAR.jpg";
                        }
                        order--;
                        // order = service.updateDesiChaiAsPerCustomProfiles(catalogEntry, splitDimensionPrice[0].dimension , order);
                        service.catalog.items.push(catalogEntry);
                    }

    }

    function addSpecialMilkTea(product , variantName, productName,productCategoryObj,order){
        var prod = angular.copy(product)
        prod.id = prod.id + "_" + variantName;
        prod.prices = prod.prices.filter(function(price){
            return dimensionHaveOatMilk(price);
        });
        var catalogEntry = service.addNewCatalogue(prod, productCategoryObj, order);
        catalogEntry.name = productName;
        order--;
        service.catalog.items.push(catalogEntry);
    };

    function containsOatMilk(product){
        return  product.prices.some(function(price){
            return dimensionHaveOatMilk(price);
         });
    }

    function dimensionHaveOatMilk(price){
        return price.recipe.options != null && price.recipe.options.filter(function(option){
           return $rootScope.specialMilkPaidAddonList.indexOf(option.id) > -1 && productHavePrice(option.id);
        }).length > 0;

    }

    function dimensionHaveCoffeeFlavour(price){
            return price.recipe.options != null && price.recipe.options.filter(function(option){
               return $rootScope.coffeeFlavours.indexOf(option.id) > -1 && productHavePrice(option.id);
            }).length > 0;

        }

    function productHavePrice(productId){
        return service.productMap[productId] != null &&
        service.productMap[productId].prices != null &&
        service.productMap[productId].prices.length > 0 &&
        service.dineInProductMap[productId] != null &&
        service.dineInProductMap[productId].prices != null &&
        service.dineInProductMap[productId].prices.length > 0;

    }


    function setMenuCatalogues() {
        var menuItemNames = {};
        var order = 800;
        service.productCategoryMap.map(function (productCategoryObj) {
            var product = service.productMap[productCategoryObj.productId];
            if (product == null) {
                service.errorList.push("Product id : " + productCategoryObj.productId + " not mapped with this unit.")
            } else {
                if ([10, 1282].indexOf(product.id) >= 0 && !service.clubAllDesiChaiDimensions) {
                    order = service.splitDesiChaiDimensions(product, productCategoryObj, order);
                } else if(product.subType != 3676){
                    var catalogEntry = service.addNewCatalogue(product, productCategoryObj, order);
                    // service.catalog.items.push(catalogEntry);
                    order--;
                    if(product.id == 10){
                      addSeperateMiniKetliDesiChaiForSwiggyBolt(product,productCategoryObj , order)
                    }
                    order = service.updateDesiChaiAsPerCustomProfiles(catalogEntry, null, order);
                    if(menuItemNames[catalogEntry.name] != null){
                        service.errorList.push("item with name : " + catalogEntry.name + " Is Duplicate In Menu!!" );
                        return;
                    }
                    menuItemNames[catalogEntry.name] = true;
                    service.catalog.items.push(catalogEntry);
                }else if(product.subType == 3676){
                    var catalogEntry = service.setSuperCombos(product,order);
                    order--;
                    if(menuItemNames[catalogEntry.name] != null){
                        service.errorList.push("item with name : " + catalogEntry.name + " Is Duplicate In Menu!!" );
                        return;
                    }
                    menuItemNames[catalogEntry.name] = true;
                    service.catalog.items.push(catalogEntry);
                }

                /*if(product.id == 10 && containsOatMilk(product)){
                    var oatMilkTeaEntry = addSpecialMilkTea(product,"OATS","Oat Milk Chai",productCategoryObj,order);
              }*/

            }
        });

        var unmappedProducts = [];
        service.unitData.products.map(function (product) {
            if (product.classification === "MENU" && product.type !== 12) {
                var found = false;
                service.menuSequence.productGroupSequences.map(function (cat) {
                    cat.subGroups.map(function (subCat) {
                        subCat.productSequenceList.map(function (prodSequence) {
                            if (!found && product.id === prodSequence.product.id) {
                                found = true;
                            }
                        });
                    });
                });
                if (!found && [11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0) {
                    /*unmappedProducts.push({
                        id:product.id,
                        name:product.name
                    });*/
                }
            }
        });
        service.unmappedProducts = unmappedProducts;
    }

    function addSuperCombo(product, order) {
        var prod = service.productMap[product.id];
        var productCategoryObj;
        service.productCategoryMap.map(function (category) {
            if (category.productId == product.id) {
                productCategoryObj = category;
            }
        });
        var catalogEntry = addNewSuperComboCatalogue(prod, productCategoryObj, order);
        return catalogEntry;
    }


    function addPriceForSuperCombo(product) {
        $rootScope.combosMainPrice[product.id] = 0;
        product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (detail, index) {
            var map = {};
            var minPriceProduct = null;
            var minPriceProductId = null;
            detail.menuProducts.forEach(function (prod,ind) {
                var priceProduct = angular.copy(productPriceSuperCombo);
                priceProduct.discount = detail.internalDiscount;
                var type = detail.internalDiscountType;
                priceProduct.discountType = type;
                priceProduct.productId = prod.product.productId;
                var productVal = service.productMap[prod.product.productId];
                var minProductPackagingCharge = 0;
                productVal.prices.forEach(function (pr) {
                    if (pr.dimension == prod.dimension.code) {
                        priceProduct.originalPrice = pr.price;
                        if (type == "FIXED") {
                            priceProduct.price = pr.price - detail.internalDiscount;
                        } else if (type == "PERCENTAGE") {
                            priceProduct.price = pr.price - (pr.price * (detail.internalDiscount * 0.01));
                        }else{
                            priceProduct.price = pr.price;
                        }
                        priceProduct.price = parseFloat(priceProduct.price) + parseFloat(getPackagingChargeForSuperCombo(productVal));
                        if (minPriceProduct == null) {
                            minPriceProduct = angular.copy(priceProduct);
                            minPriceProductId = prod.product.productId + "_" + prod.dimension.code + "_" + product.id + "_" + index + "_SUPERCOMBO";
                            minProductPackagingCharge = parseFloat(getPackagingChargeForSuperCombo(productVal));
                        }
                        if (priceProduct.price < minPriceProduct.price) {
                            minPriceProduct = angular.copy(priceProduct);
                            minPriceProductId = prod.product.productId + "_" + prod.dimension.code + "_" + product.id + "_" + index + "_SUPERCOMBO";
                            minProductPackagingCharge = parseFloat(getPackagingChargeForSuperCombo(productVal));
                        }
                    }
                });
                minPriceProduct.price = parseFloat(minPriceProduct.price) - parseFloat(minProductPackagingCharge);
                var key = prod.product.productId + "_" + prod.dimension.code + "_" + product.id + "_" + index + "_SUPERCOMBO";
                map[key] = priceProduct;
            });
            map[minPriceProductId].price = minPriceProduct.price;

            var produckyes = Object.keys(map);

            for (var i = 0; i < produckyes.length; i++) {
                var keyValue = map[produckyes[i]];
                // now subracting min price
                keyValue.beforeSub = angular.copy(keyValue.price);
                keyValue.price = Math.round(keyValue.price - minPriceProduct.price);
                if (keyValue.price == null || keyValue.price < 0) {
                    service.errorList.push("Price of product " + service.productMap[keyValue.productId].name + " is missing");
                }

                $rootScope.priceMapForSuperCombo[produckyes[i]] = keyValue;
            }
            console.log(minPriceProduct.price);
            $rootScope.combosMainPrice[product.id] = Math.round($rootScope.combosMainPrice[product.id] + minPriceProduct.price);
        });
        console.log($rootScope.combosMainPrice);
    }

    function splitDesiChaiDimensions(product, productCategoryObj, order) {
        var catalogEntry;
        if (service.splitAllDesiChaiDimensions) {
            product.prices.map(function (price) {
                var prod = angular.copy(product);
                prod.id = prod.id + "_" + price.dimension;
                prod.prices = [price];
                catalogEntry = service.addNewCatalogue(prod, productCategoryObj, order);
                order--;
                order = service.updateDesiChaiAsPerCustomProfiles(catalogEntry, price.dimension.replace("Ketli", " Ketli"), order);
                service.catalog.items.push(catalogEntry);
            });
        } else {
            var splitDimensionPrice = [];
            var noSplitDimensionPrice = [];
            var productsAdded = 0;
            product.prices.map(function (price) {
                if (price.dimension === $rootScope.splitDesiChaiDimensionName) {
                    splitDimensionPrice.push(angular.copy(price));
                } 
                noSplitDimensionPrice.push(angular.copy(price));
                
            });
            if (splitDimensionPrice.length > 0) {

                var prodBk = angular.copy(product);
                if(prodBk.id == 10){
                   prodBk.id = prodBk.id + "_" + $rootScope.splitDesiChaiDimensionName;
                   prodBk.prices = splitDimensionPrice;
                   catalogEntry = service.addNewCatalogue(prodBk, productCategoryObj, order,false);
                   if(splitDimensionPrice[0].dimension == "Regular"){
                                      catalogEntry.image_url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/DESI_CHAI_REGULAR.jpg";
                   }
                    order--;
                                   // order = service.updateDesiChaiAsPerCustomProfiles(catalogEntry, splitDimensionPrice[0].dimension , order);
                   service.catalog.items.push(catalogEntry);
                }

            }
            if (noSplitDimensionPrice.length > 0) {
                var prod = angular.copy(product);
                prod.prices = noSplitDimensionPrice;
                catalogEntry = service.addNewCatalogue(prod, productCategoryObj, order);
                order--;
                service.catalog.items.push(catalogEntry);
                order = service.updateDesiChaiAsPerCustomProfiles(catalogEntry, null, order);
            }
        }
        return order;
    }

    function getFilteredPrices(prices) {
                var filteredPrices = prices.filter(function(price){
                        return (prices.length ==1 || price.dimension != "1 Pack");
                     });
                return filteredPrices;

    }

    function addNewCatalogue(product, productCategoryObj, order,addDimensionInName) {
        var productId = product.id.toString().includes("_") ? product.id.toString().split("_")[0] : product.id;
        var item = Object.assign({}, itemObj);
        //removed due to character length exceeded
        //item.id = product.id + "_" + productCategoryObj.categoryId + "_" + productCategoryObj.subCategoryId;
        item.id = product.id + "_" + productCategoryObj.subCategoryId;
        var productAlias = getProductAlias(product);
        var name = productAlias != null ? productAlias : getProductName(product.name);
        item.name = name;
        var filteredPrices = getFilteredPrices(product.prices);
        if (product.id.toString().indexOf("_") >= 0) {
            if(addDimensionInName != null && addDimensionInName == false && product.prices[0].dimensionDescriptor ){
                item.name = name + " " + getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
            }else{
              item.name = name + getDimensionName(product.prices[0].dimension) +
                              getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
            }


        }else if (product.brandId == 6 && filteredPrices.length == 1 && filteredPrices[0].dimensionDescriptor){
                              item.name = name  + getDimensionDescription(filteredPrices[0].dimension, filteredPrices[0].dimensionDescriptor);
        }
        item.category_id = productCategoryObj.categoryId;
        item.sub_category_id = productCategoryObj.subCategoryId;
        item.order = order;
        console.log(product.name);
        item.is_veg = isVeg(product.attribute);
        item.description = getProductDescription(product.description, product.name);
        // && getItemPrice(product) == 0
        if (product.classification == "MENU" && product.subType == 3891 && product.type == 8 && getItemPrice(product) == 0) {
            if (product.prices != null && product.prices.length > 0) {
                if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient != null && product.prices[0].recipe.ingredient.compositeProduct != null &&
                 product.prices[0].recipe.ingredient.compositeProduct.details != null) {
                    if (product.prices[0].recipe.ingredient.compositeProduct.details.length > 0) {
                        getPricesOfAddons(product);
                        var idx = product.prices[0].recipe.ingredient.compositeProduct.details.length;
                        product.prices[0].recipe.ingredient.compositeProduct.details.map(function (prod) {
                            var group = angular.copy(addOnGroup);
                            var displayName = prod.name;
                            group.id = productId + "_" + displayName;
                            group.name = prod.name;
                            group.order = idx;
                            var addOnsList = prod.menuProducts;
                            group.addon_min_limit = 1;
                            group.addon_free_limit = 1;//addOnsList.length;
                            group.addon_limit = 1;
                            group.addons = null;
                            if (product.type === 8) {
                                var addOnList = [];
                                addOnsList.forEach(function (add, indexs) {
                                    var addOnProduct = add.product;
                                    if (indexs == 0) {
                                        item.price += service.priceMap[addOnProduct.productId];
                                    }
                                })
                            }
                        })
                    }
                }
            }
        }
        else {
            item.price = getItemPrice(product);
        }
        item.gst_details = getGSTDetails(product.taxCode);
        item.packing_charges = getPackagingCharge(product);
        item.enable = 1;
        item.in_stock = 1;
        item.addon_free_limit = -1;
        item.addon_limit = -1;
        if (productCategoryObj.recommended != null) {
            item.recommended = productCategoryObj.recommended;
        } else {
            item.recommended = false;
        }
        if (service.productImages[productId] == null || service.productImages[productId].gridLow == null ||
            service.productImages[productId].gridLow.url == null) {
            if (AppUtil.getEnvType() === "PROD") {
                service.errorList.push("Product image missing for: " + product.name);
            }
        } else {
            item.image_url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[productId].gridLow.url;
        }
        item.variant_groups = service.getVariantGroups(product, item);
        item.addon_groups = service.getAddOnGroups(product,item);
        item.item_slots = [];
        item.pricing_combinations = [];
        var basePrice = getBasePrice(product);
        service.noVariants = true;
        var pricingCombinations = getPricingCombinations(item, basePrice,product);
        if (Array.isArray(pricingCombinations) && pricingCombinations.length) {
            item.pricing_combinations = pricingCombinations;
            updateDefaultDependentVariant(item);
        }
        return item;
    }


    function addNewSuperComboCatalogue(product, productCategoryObj, order) {
        var productId = product.id.toString().includes("_") ? product.id.toString().split("_")[0] : product.id;
        var item = Object.assign({}, itemObj);
        item.id = product.id + "_" + productCategoryObj.subCategoryId;
        var productAlias = getProductAlias(product);
        var name = productAlias != null ? productAlias : getProductName(product.name);
        item.name = name;
        if (product.id.toString().indexOf("_") >= 0) {
            item.name = name + getDimensionName(product.prices[0].dimension) +
                getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
        }
        item.category_id = productCategoryObj.categoryId;
        item.sub_category_id = productCategoryObj.subCategoryId;
        item.order = order;
        console.log(product.name);
        item.is_veg = isVeg(product.attribute);
        item.description = getProductDescription(product.description, product.name);
        if (product.subType == 3676) {
            if (product.prices == null) {
                service.errorList("Product price missing for : " + name);
            }
            if (product.prices != null && product.prices.length > 0) {
                if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient != null && product.prices[0].recipe.ingredient.compositeProduct != null && product.prices[0].recipe.ingredient.compositeProduct.details != null) {
                    if (product.prices[0].recipe.ingredient.compositeProduct.details.length > 0) {
                        getPricesOfAddons(product);
                        var idx = product.prices[0].recipe.ingredient.compositeProduct.details.length;
                        product.prices[0].recipe.ingredient.compositeProduct.details.map(function (prod) {
                            var group = angular.copy(addOnGroup);
                            var displayName = prod.name;
                            group.id = productId + "_" + displayName;
                            group.name = prod.name;
                            group.order = idx;
                            var addOnsList = prod.menuProducts;
                            group.addon_min_limit = 1;
                            group.addon_free_limit = 1;//addOnsList.length;
                            group.addon_limit = 1;
                            group.addons = null;
                        })
                    }
                }
            }
        }
        item.price = $rootScope.combosMainPrice[product.id];
        item.gst_details = getGSTDetails(product.taxCode);
        item.packing_charges = getPackagingCharge(product);
        item.enable = 1;
        item.in_stock = 1;
        item.addon_free_limit = -1;
        item.addon_limit = -1;
        if (productCategoryObj.recommended != null) {
            item.recommended = productCategoryObj.recommended;
        } else {
            item.recommended = false;
        }
        if (service.productImages[productId] == null || service.productImages[productId].gridLow == null ||
            service.productImages[productId].gridLow.url == null) {
            if (AppUtil.getEnvType() === "PROD") {
                service.errorList.push("Product image missing for: " + product.name);
            }
        } else {
            item.image_url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[productId].gridLow.url;
        }
        item.variant_groups = [];
        item.addon_groups = getSuperComboAddOnGroups(product, item);
        item.item_slots = [];
        item.pricing_combinations = [];
        var basePrice = getBasePrice(product);
        return item;
    }

    function getBasePrice(product) {
        var basePrice = null;
        if(product.subType == 3675){
            return 0;
        }
        var filteredPrices = product.prices.filter(function(price){
                     return (product.prices.length ==1 || price.dimension != "1 Pack");
        });
        filteredPrices.map(function (price) {
            if (basePrice == null) {
                basePrice = price.price;
            } else if (price.price < basePrice) {
                basePrice = price.price;
            }
        });
        return basePrice;
    }

    function getVariantGroups(product, item) {
        var variantGroups = [];
        var sugarVariant = null;
        if (product.prices[0].recipe != null) {
            var dimensionVariants = addDimensionVariants(product, item.id);
            if (Array.isArray(dimensionVariants) && dimensionVariants.length) {
                dimensionVariants.map(function (dimensionVariant) {
                    variantGroups.push(dimensionVariant);
                });
            }
            addHeroComboVariantGroupsV2(variantGroups,product);
            if(service.addonMode ==  false){
                // adding combo variants
                var compositeProductsVariants = service.addComboVariantGroups(product);
                if (Array.isArray(compositeProductsVariants) && compositeProductsVariants.length) {
                    compositeProductsVariants.map(function (compositeProductsVariant) {
                        variantGroups.push(compositeProductsVariant);
                    });
                }
                var ingredientProductVariants = addIngredientProducts(product, item.id);
                if (Array.isArray(ingredientProductVariants) && ingredientProductVariants.length) {
                    ingredientProductVariants.map(function (ingredientProductVariant) {
                        variantGroups.push(ingredientProductVariant);
                    });
                }
                var ingredientVariants = addIngredientVariants(product);
                if (Array.isArray(ingredientVariants) && ingredientVariants.length) {
                    ingredientVariants.map(function (ingredientVariant) {
                        if(ingredientVariant.name == "Sugar"){
                            sugarVariant = ingredientVariant;
                        }else{
                            variantGroups.push(ingredientVariant);
                        }

                    });
                }
            }
            if(sugarVariant != null){
               variantGroups.push(sugarVariant);
            }
        }
        return variantGroups;
    }

    function getSuperComboVariantGroups(product, item) {
            var variantGroups = [];
            var dimensionVariants = addSuperComboDimensionVariants(product, item.id);
            if (Array.isArray(dimensionVariants) && dimensionVariants.length) {
                dimensionVariants.map(function (dimensionVariant) {
                    variantGroups.push(dimensionVariant);
                });
            }
            if (product.prices[0].recipe != null) {
                var ingredientVariants = addIngredientVariants(product);
                var sugarVariant = null;
                if (Array.isArray(ingredientVariants) && ingredientVariants.length) {
                    ingredientVariants.map(function (ingredientVariant) {
                        if(ingredientVariant.name == "Sugar"){
                          sugarVariant = ingredientVariant;
                        }else{
                           variantGroups.push(ingredientVariant);
                        }

                    });
                }
                var ingredientProductVariants = addIngredientProducts(product, item.id);
                if (Array.isArray(ingredientProductVariants) && ingredientProductVariants.length) {
                    ingredientProductVariants.map(function (ingredientProductVariant) {
                        variantGroups.push(ingredientProductVariant);
                    });
                }
                // adding combo variants
                var compositeProductsVariants = service.addSuperComboVariantGroups(product);
                if (Array.isArray(compositeProductsVariants) && compositeProductsVariants.length) {
                    compositeProductsVariants.map(function (compositeProductsVariant) {
                        variantGroups.push(compositeProductsVariant);
                    });
                }
                if(sugarVariant != null){
                   variantGroups.push(sugarVariant);
                }
            }
            return variantGroups;
        }

    function addDimensionVariants(product, itemId) {
        var groups = [];
        if (product.prices.length > 1) {
            var variantGroup = Object.assign({}, variantGroupObj);
            variantGroup.id = product.id + "_size";
            variantGroup.name = "Size";
            variantGroup.order = 1;
            variantGroup.variants = [];
            var basePrice = null;
            var filteredPrices = product.prices.filter(function(price){
                     return (product.prices.length ==1 || price.dimension != "1 Pack");
            });
            filteredPrices.map(function (price) {
                basePrice = (basePrice == null ? price.price : (price.price < basePrice ? price.price : basePrice));
            });
            filteredPrices.map(function (price, ind) {
                if (price.recipe != null) {
                    if (([10, 1282].indexOf(itemId) < 0)) {
                        var id = product.id + "_" + price.dimension;
                        var name = (price.dimensionDescriptor ? price.dimensionDescriptor : price.dimension);
                        if (price.dimension === "MiniKetli" || price.dimension === "ChotiKetli" || price.dimension === "BadiKetli" || price.dimension === "Regular") {
                            name = price.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(price.dimension, price.dimensionDescriptor) + ")".trim();
                        }
                        var isDefault = (ind === 0);
                        var variant = buildVariant(id, name, isDefault);
                        var variantPrice = price.price - basePrice;
                        if (variantPrice !== 0) {
                            variant.price = parseFloat(variantPrice + "");
                        }
                        variantGroup.variants.push(variant);
                    }
                }
            });
            groups.push(variantGroup);
        }
        return groups;
    }

    function addDimensionAddons(product, itemId) {
        var groups = [];
        if (product.prices.length > 1) {
            var addonGroup = Object.assign({}, addOnGroup);
            addonGroup.id = product.id + "_size";
            addonGroup.name = "Size";
            addonGroup.order = 8;
           // addonGroup.addon_min_limit=1;
            addonGroup.addon_limit=1;
            addonGroup.addon_free_limit=null;
            addonGroup.addons = [];
            var basePrice = null;
            product.prices.map(function (price) {
                basePrice = (basePrice == null ? price.price : (price.price < basePrice ? price.price : basePrice));
            });
            var length = product.prices.length;
            product.prices.map(function (price, ind) {
                if (price.recipe != null) {
                    if (([10, 1282].indexOf(itemId) < 0)) {
                        var id = product.id + "_" + price.dimension;
                        var name = (price.dimensionDescriptor ? price.dimensionDescriptor : price.dimension);
                        if (price.dimension === "MiniKetli" || price.dimension === "ChotiKetli" || price.dimension === "BadiKetli" || price.dimension === "Regular") {
                            name = price.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(price.dimension, price.dimensionDescriptor) + ")".trim();
                        }
                        var isDefault = (ind === 0);
                        var addOn = Object.assign({}, addOnObj);
                        addOn.id = id;
                        addOn.name = name;
                        addOn.order =length -  ind;
                        addOn.is_default = isDefault;
                        var addonPrice = price.price - basePrice;
                        if (addonPrice !== 0) {
                            addOn.price = parseFloat(addonPrice + "");
                        }
                        addonGroup.addons.push(addOn);
                    }
                }
            });
            groups.push(addonGroup);
        }
        return groups;
    }

    function addSuperComboDimensionVariants(product, itemId) {
        var groups = [];
        var variantGroup = Object.assign({}, variantGroupObj);
        variantGroup.id = product.id + "_size";
        variantGroup.name = "Size";
        variantGroup.order = 1;
        variantGroup.variants = [];
        var basePrice = null;
        product.prices.map(function (price) {
            basePrice = (basePrice == null ? price.price : (price.price < basePrice ? price.price : basePrice));
        });
        product.prices.map(function (price, ind) {
            if (price.recipe != null) {
                if (([10, 1282].indexOf(itemId) < 0)) {
                    var id = product.id + "_" + price.dimension;
                    var name = (price.dimensionDescriptor ? price.dimensionDescriptor : price.dimension);
                    if (price.dimension === "MiniKetli" || price.dimension === "ChotiKetli" || price.dimension === "BadiKetli" || price.dimension === "Regular") {
                        name = price.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(price.dimension, price.dimensionDescriptor) + ")".trim();
                    }
                    var isDefault = (ind === 0);
                    var variant = buildVariant(id, name, isDefault);
                    var variantPrice = price.price - basePrice;
                    if (variantPrice !== 0) {
                        variant.price = parseFloat(variantPrice + "");
                    }
                    variantGroup.variants.push(variant);
                }
            }
        });
        groups.push(variantGroup);
        return groups;
    }

    function addIngredientVariants(product) {
        var groups = [];
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.variants != null) {
            product.prices[0].recipe.ingredient.variants.map(function (varItem) {
                var variantGroup = Object.assign({}, variantGroupObj);
                var displayName = varItem.product.displayName || "Option";
                variantGroup.id = varItem.product.productId + "_" + product.id;
                variantGroup.name = displayName;
                variantGroup.order = groups.length + 1;
                variantGroup.variants = [];
                // this was added later
                varItem.details.map(function (a, b) {
                    return (a.defaultSetting === b.defaultSetting) ? 0 : a.defaultSetting ? -1 : 1;
                });
                varItem.details.map(function (varDetail, index) {
                    var id = product.id + "_" + varDetail.alias.replace(/[ ]/g, "_");
                    var name = varDetail.alias;
                    var isDefault = varDetail.defaultSetting;
                    var variant = buildVariant(id, name, isDefault);
                    variantGroup.variants.push(variant);
                });
                groups.push(variantGroup);
            });
        }
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        productId = productId + "";
        if (productId === "10" || productId === "1282") {
            var variantGroup = Object.assign({}, variantGroupObj);
            var displayName = "Milk Option";
            variantGroup.id = product.id + "_" + "_milk_option";
            variantGroup.name = displayName;
            variantGroup.order = groups.length + 1;
            variantGroup.variants = [];
            ["Regular Milk", "Full Doodh (Full Milk)", "Doodh Kum (Less Milk)", "Paani Kum (More Milk)"].map(function (varnt, ind) {
                var id = product.id + "_" + varnt.substr(0, 1)
                    + varnt.substr(varnt.length - 1) + displayName.length;
                var name = varnt;
                var isDefault = (ind === 0);
                var variant = buildVariant(id, name, isDefault);
                variantGroup.variants.push(variant);
            });
            groups.push(variantGroup);
        }
        return groups;
    }

   function addSpecialMilkTypeGroup(product,groups , price){
    // var dimensionCodeMap = {
    //     "BadiKetli": "BK",
    //     "ChotiKetli": "CK",
    //     "MiniKetli": "MK",
    //     "Regular": "Reg",
    //     "None": "None"
    // };
        var milkTypeGroup = null;
        groups.forEach(function(group){
                 if(group.name == "Milk Type"){
                    milkTypeGroup = group;
                 }
        });
        var addonGroup = Object.assign({}, addOnGroup);
        addonGroup.addons = [];
        var displayName = "Milk Type";
        addonGroup.id = product.id + "_" +  "_milk_type";
        addonGroup.name = displayName;
        addonGroup.order = 8 ;
        addonGroup.addon_limit=1;
        addonGroup.addon_min_limit = 1;
        addonGroup.addon_free_limit = null;
        addonGroup.variants = [];
        var milkOptions = price.recipe.options != null && price.recipe.options.filter(function(option){
            return $rootScope.specialMilkPaidAddonList.indexOf(option.id) > -1 && productHavePrice(option.id);
         }).map(function(option){
            var optionProduct = service.productMap[option.id];
            var optionAlias = getProductAlias(optionProduct);
            var optionMap = {};
            optionProduct.originalName = angular.copy(optionProduct.name);
             optionProduct.name = optionAlias != null ? optionAlias :optionProduct.name ;
             optionMap[option.id]=optionProduct;
            return optionMap });
            var length = milkOptions.length;
       if (product.id != "10_OATS") {
           var regularOption = {};
           regularOption[-1] = { name: "Regular" };
           milkOptions[length] = regularOption;
       }
         
        var length = milkOptions.length;
        for(var i = 0 ; i<milkOptions.length;i++){
            Object.keys(milkOptions[i]).forEach(function (addonId,ind) {
                var id = addonId;
                var optionProduct = milkOptions[i][addonId];
                var name = optionProduct.name;
                var addon = Object.assign({},addOnObj);
                addon.id = id;
                if(addonId == -1){
                    addon.id = product.id + "_" + "milk_type_regular";
                     addon.is_default = true;
                 }else{
                    addon.id = product.id +  "_" + price.dimension + "_" + addon.id + "_PAIDADDON";
                 }
                addon.name = name;
                addon.originalName = optionProduct.originalName;
                addon.order = length - ind;
                if(addonId != -1){
                    addon.price = optionProduct.prices[0].price;
                }
                if(milkTypeGroup!=null){
                    milkTypeGroup.addons.push(addon);
                }else{
                    addonGroup.addons.push(addon);
                }

            });
        }
        if(milkTypeGroup==null){
            groups.push(addonGroup);
        }
   }

   function addCoffeeFlavourGroup(product,groups , price){
           var flavourGroup = null;
           groups.forEach(function(group){
                    if(group.name == "Flavours"){
                       flavourGroup = group;
                    }
           });
           var addonGroup = Object.assign({}, addOnGroup);
           addonGroup.addons = [];
           var displayName = "Flavours";
           addonGroup.id = product.id + "_" +  "_flavour";
           addonGroup.name = displayName;
           addonGroup.order = 8 ;
           addonGroup.addon_limit=1;
           addonGroup.addon_min_limit = 0;
           addonGroup.addon_free_limit = null;
           addonGroup.variants = [];
           var coffeeFlavours = price.recipe.options != null && price.recipe.options.filter(function(option){
               return $rootScope.coffeeFlavours.indexOf(option.id) > -1 && productHavePrice(option.id);
            }).map(function(option){
               var optionProduct = service.productMap[option.id];
               var optionAlias = getProductAlias(optionProduct);
               var optionMap = {};
               optionProduct.originalName = angular.copy(optionProduct.name);
                optionProduct.name = optionAlias != null ? optionAlias :optionProduct.name ;
                optionMap[option.id]=optionProduct;
               return optionMap });
               var length = coffeeFlavours.length;

           var length = coffeeFlavours.length;
           for(var i = 0 ; i<coffeeFlavours.length;i++){
               Object.keys(coffeeFlavours[i]).forEach(function (addonId,ind) {
                   var id = addonId;
                   var optionProduct = coffeeFlavours[i][addonId];
                   var name = optionProduct.name;
                   var addon = Object.assign({},addOnObj);
                   addon.id = id;
                   addon.id = product.id +  "_" + price.dimension + "_" + addon.id + "_PAIDADDON";
                   addon.name = name;
                   addon.originalName = optionProduct.originalName;
                   addon.order = length - ind;
                   if(addonId != -1){
                       addon.price = optionProduct.prices[0].price;
                   }
                   if(flavourGroup!=null){
                       flavourGroup.addons.push(addon);
                   }else{
                       addonGroup.addons.push(addon);
                   }

               });
           }
           if(flavourGroup==null){
               groups.push(addonGroup);
           }
      }


    function addIngredientAddons(product) {
        var groups = [];
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.variants != null) {
            product.prices[0].recipe.ingredient.variants.map(function (varItem) {
                var addonGroup = Object.assign({}, addOnGroup);
                var displayName = varItem.product.displayName || "Option";
                addonGroup.id = varItem.product.productId + "_" + product.id;
                addonGroup.name = displayName;
                addonGroup.order = 6;
                addonGroup.addon_min_limit = 1;
                addonGroup.addon_limit = 1;
                addonGroup.addon_free_limit = null;
                addonGroup.addons = [];
                // this was added later
                varItem.details.map(function (a, b) {
                    return (a.defaultSetting === b.defaultSetting) ? 0 : a.defaultSetting ? -1 : 1;
                });
                var length = varItem.details.length;

                varItem.details.map(function (varDetail, index) {
                    var id = product.id + "_" + varDetail.alias.replace(/[ ]/g, "_");
                    var name = varDetail.alias;
                    var isDefault = varDetail.defaultSetting;
                    var addon = Object.assign({},addOnObj);
                    addon.id = id;
                    addon.is_default = isDefault;
                    addon.name = name;
                    addon.order = length - index;
                    addonGroup.addons.push(addon);
                });
                groups.push(addonGroup);
            });
        }
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        productId = productId + "";
        if (productId === "10" || productId === "1282") {
            var addonGroup = Object.assign({}, addOnGroup);
            addonGroup.addons = [];
            var displayName = "Milk Option";
            addonGroup.id = product.id + "_" + "_milk_option";
            addonGroup.name = displayName;
            addonGroup.order = 7 ;
           // addonGroup.addon_min_limit = 1;
            addonGroup.addon_limit=1;
            addonGroup.addon_free_limit = null;
            addonGroup.variants = [];
            var milkOptions = ["Regular Milk", "Full Doodh (Full Milk)", "Doodh Kum (Less Milk)", "Paani Kum (More Milk)"];
            var length = milkOptions.length;
            milkOptions.map(function (varnt, ind) {
                var id = product.id + "_" + varnt.substr(0, 1)
                    + varnt.substr(varnt.length - 1) + displayName.length;
                var name = varnt;
                var isDefault = (ind === 0);
                //var variant = buildVariant(id, name, isDefault);
                var addon = Object.assign({},addOnObj);
                addon.id = id;
                addon.is_default = isDefault;
                addon.name = name;
                addon.order = length - ind;
                addonGroup.addons.push(addon);
            });
            groups.push(addonGroup);
        }
        return groups;
    }

    function addIngredientProducts(product, itemId) {
        var groups = [];
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.products != null) {
            product.prices[0].recipe.ingredient.products.map(function (prodItem, ind) {
                var variantGroup = Object.assign({}, variantGroupObj);
                var displayName = prodItem.display || "Option";
                variantGroup.id = product.id + "_" + displayName;
                variantGroup.name = displayName;
                variantGroup.order = ind;
                variantGroup.variants = [];
                prodItem.details.map(function (prodDetail, index) {
                    //var id = product.id + "_" + subCategoryId + "_" + prodDet ail.product.productId;
                    var id = itemId + "_" + prodDetail.product.productId;
                    var name = prodDetail.product.name;
                    var isDefault = prodDetail.defaultSetting;
                    var variant = buildVariant(id, name, isDefault);
                    variantGroup.variants.push(variant);
                });
                // Bread options should not be given on Swiggy
                if (displayName !== "Bread") {
                    groups.push(variantGroup);
                }
            });
        }
        return groups;
    }

    function addIngredientProductsAddons(product, itemId) {
        var groups = [];
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.products != null) {
            product.prices[0].recipe.ingredient.products.map(function (prodItem, ind) {
                var addonGroup = Object.assign({}, addOnGroup);
                var displayName = prodItem.display || "Option";
                addonGroup.id = product.id + "_" + displayName;
                addonGroup.name = displayName;
                addonGroup.order = 4;
                addonGroup.addon_min_limit = 1;
                addonGroup.addon_limit = 1;
                addonGroup.addon_free_limit=null;
                addonGroup.addons = [];
                var length = prodItem.details.length;
                prodItem.details.map(function (prodDetail, index) {
                    //var id = product.id + "_" + subCategoryId + "_" + prodDetail.product.productId;
                    var id = itemId + "_" + prodDetail.product.productId;
                    var name = prodDetail.product.name;
                    var isDefault = prodDetail.defaultSetting;
                    var addon = Object.assign({},addOnObj);
                    addon.id = id;
                    addon.name = name;
                    addon.order = length - index;
                    addon.is_default = isDefault;
                    addonGroup.addons.push(addon);
                });
                // Bread options should not be given on Swiggy
                if (displayName !== "Bread") {
                    groups.push(addonGroup);
                }
            });
        }
        return groups;
    }

    function addComboVariantGroups(catal) {
        var groups = [];
        if (catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.compositeProduct != null) {
            service.addNormalComboVariantGroups(groups, catal);
            service.addHeroComboVariantGroups(groups, catal);
        }
        return groups;
    }

    function addComboAddonVariantGroups(catal) {
        var groups = [];
        if (catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.compositeProduct != null) {
            service.addNormalComboVariantGroups(groups, catal);
            service.addHeroComboVariantGroups(groups, catal);
        }
        return groups;
    }

    function addSuperComboVariantGroups(catal) {
        var groups = [];
        if (catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.compositeProduct != null) {
            addSuperComboVariantGroup(groups, catal);
        }
        return groups;
    }

    function addNormalComboVariantGroups(groups, product) {
        if (product.subType !== 3675 && product.subType !== 3676) {
            var primaryProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product;
            var secondaryProduct = (product.prices[0].recipe.ingredient.compositeProduct.details[1] != null ?
                product.prices[0].recipe.ingredient.compositeProduct.details[1].menuProducts[0].product : null);
            var compCatal = service.productMap[primaryProduct.productId];
            if (compCatal == null) {
                service.errorList.push("Combo " + product.name + " sub item: " + primaryProduct.name + " not found");
            } else {
                var variantGroupsCombo = addIngredientProductsForCombo(compCatal, product);
                if (Array.isArray(variantGroupsCombo) && variantGroupsCombo.length) {
                    variantGroupsCombo.map(function (dimensionPropertyCombo) {
                        groups.push(dimensionPropertyCombo);
                    });
                }
            }
            if (secondaryProduct != null) {
                var secondaryCompCatal = service.productMap[secondaryProduct.productId];
                if (secondaryCompCatal == null) {
                    service.errorList.push("Combo " + product.name + " sub item: " + secondaryProduct.name + " not found");
                } else {
                    var variantGroupsComboSecnd = addIngredientProductsForCombo(secondaryCompCatal, product);
                    if (Array.isArray(variantGroupsComboSecnd) && variantGroupsComboSecnd.length) {
                        variantGroupsComboSecnd.map(function (dimensionPropertyComboSecnd) {
                            groups.push(dimensionPropertyComboSecnd);
                        });
                    }
                }
            }
        }
    }

    function addNormalComboVariantGroups(groups, product) {
        if (product.subType !== 3675 && product.subType !== 3676) {
            var primaryProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product;
            var secondaryProduct = (product.prices[0].recipe.ingredient.compositeProduct.details[1] != null ?
                product.prices[0].recipe.ingredient.compositeProduct.details[1].menuProducts[0].product : null);
            var compCatal = service.productMap[primaryProduct.productId];
            if (compCatal == null) {
                service.errorList.push("Combo " + product.name + " sub item: " + primaryProduct.name + " not found");
            } else {
                var variantGroupsCombo = addIngredientProductsForCombo(compCatal, product);
                if (Array.isArray(variantGroupsCombo) && variantGroupsCombo.length) {
                    variantGroupsCombo.map(function (dimensionPropertyCombo) {
                        groups.push(dimensionPropertyCombo);
                    });
                }
            }
            if (secondaryProduct != null) {
                var secondaryCompCatal = service.productMap[secondaryProduct.productId];
                if (secondaryCompCatal == null) {
                    service.errorList.push("Combo " + product.name + " sub item: " + secondaryProduct.name + " not found");
                } else {
                    var variantGroupsComboSecnd = addIngredientProductsForCombo(secondaryCompCatal, product);
                    if (Array.isArray(variantGroupsComboSecnd) && variantGroupsComboSecnd.length) {
                        variantGroupsComboSecnd.map(function (dimensionPropertyComboSecnd) {
                            groups.push(dimensionPropertyComboSecnd);
                        });
                    }
                }
            }
        }
    }

    function addNormalComboAddonVariantGroups(groups, product) {
        if (product.subType !== 3675 && product.subType !== 3676) {
            var primaryProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product;
            var secondaryProduct = (product.prices[0].recipe.ingredient.compositeProduct.details[1] != null ?
                product.prices[0].recipe.ingredient.compositeProduct.details[1].menuProducts[0].product : null);
            var compCatal = service.productMap[primaryProduct.productId];
            if (compCatal == null) {
                service.errorList.push("Combo " + product.name + " sub item: " + primaryProduct.name + " not found");
            } else {
                var addonGroupsCombo = addIngredientProductsAddonsForCombo(compCatal, product);
                if (Array.isArray(addonGroupsCombo) && addonGroupsCombo.length) {
                    addonGroupsCombo.map(function (dimensionPropertyCombo) {
                        groups.push(dimensionPropertyCombo);
                    });
                }
            }
            if (secondaryProduct != null) {
                var secondaryCompCatal = service.productMap[secondaryProduct.productId];
                if (secondaryCompCatal == null) {
                    service.errorList.push("Combo " + product.name + " sub item: " + secondaryProduct.name + " not found");
                } else {
                    var addonGroupsComboSecnd = addIngredientProductsAddonsForCombo(secondaryCompCatal, product);
                    if (Array.isArray(addonGroupsComboSecnd) && addonGroupsComboSecnd.length) {
                        addonGroupsComboSecnd.map(function (dimensionPropertyComboSecnd) {
                            groups.push(dimensionPropertyComboSecnd);
                        });
                    }
                }
            }
        }
    }

    function addHeroComboVariantGroups(groups, product) {
        if (product.subType == 3675) {
            var heroProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product;
            var heroProductId = heroProduct.productId;
            var heroProdDim = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].dimension.code;
            var prod = service.productMap[heroProductId];
            if (prod == null) {
                service.errorList.push("Hero combo " + product.name + " sub item: " + heroProduct.name + " not found");
            } else {
                prod.prices.map(function (pricess) {
                    if (pricess.dimension == heroProdDim) {
                        var dimensionPropertiesCombo = addIngredientProductsForCombo(prod, product);
                        if (Array.isArray(dimensionPropertiesCombo) && dimensionPropertiesCombo.length) {
                            dimensionPropertiesCombo.map(function (dimensionPropertyCombo) {
                                groups.push(dimensionPropertyCombo);
                            });
                        }
                    }
                });
            }
        }
    }

    function addHeroComboVariantGroupsV2(groups, product) {
        if (product.subType == 3675) {
            addHeroProductGroupsToVariant(groups,product);

        }
    }

    function addHeroProductGroupsToVariant(groups,product){
        var variantGroup = Object.assign({}, variantGroupObj);
        var displayName = "Item 1";
        variantGroup.id = product.id + "_ITEM_1";
        variantGroup.name = displayName;
        variantGroup.order = groups.length + 1;
        variantGroup.variants = [];
        product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts.map(function (menuProd,index){
            var heroGroup = product.prices[0].recipe.ingredient.compositeProduct.details[0];
            var dimension = menuProd.dimension.code;
            var id = menuProd.product.productId + "_" + dimension  + "_" + product.id +  "_HERO" ;
            var heroProd = service.productMap[menuProd.product.productId];
            var heroProdAlias = null;
            var price = 0;
            heroProd.prices.map(function (productPrice) {
                if (productPrice.dimension == dimension) {
                    price = productPrice.price;
                    if(productPrice.aliasProductName !=null){
                       heroProdAlias = productPrice.aliasProductName;
                    }
                }
            });
            if(price == 0){
                service.errorList.push("Super combo " + product.name + " sub item: " + menuProd.product.name  + " With Dimension " + dimension + " not found");
            }
            var name = heroProdAlias != null ? heroProdAlias : menuProd.product.name + getDimensionName(dimension);
            var isDefault = index==0;
            var variant = buildVariant(id, name, isDefault);
            var discountedPrice = getDiscountedPrice(heroGroup.internalDiscount, price,heroGroup.internalDiscountType);
            variant.price = parseInt(discountedPrice);
            variantGroup.variants.push(variant);
        });
        variantGroup.variants.sort(function (variant1,variant2){
            return  variant1.price - variant2.price;
        });
        var len = variantGroup.variants.length;
        variantGroup.variants.map(function(variant){
                variant.order = len;
                len--;
        });

        groups.push(variantGroup);
    }

    function addSuperComboVariantGroup(groups, product) {
            if (product.subType == 3676) {
                var comboProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product;
                var superComboProductId = comboProduct.productId;
                var superComboProdDim = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].dimension.code;
                var prod = service.productMap[superComboProductId];
                if (prod == null) {
                    service.errorList.push("Super combo " + product.name + " sub item: " + comboProduct.name + " not found");
                } else {
                    prod.prices.map(function (prices) {
                        if (prices.dimension == superComboProdDim) {
                            var dimensionPropertiesCombo = addIngredientProductsForCombo(prod, product);
                            if (Array.isArray(dimensionPropertiesCombo) && dimensionPropertiesCombo.length) {
                                dimensionPropertiesCombo.map(function (dimensionPropertyCombo) {
                                    groups.push(dimensionPropertyCombo);
                                });
                            }
                        }
                    });
                }
            }
        }

    function addIngredientProductsForCombo(catal, catalRoot) {
        var groups = [];
        if (catal.prices == null || catal.prices.length === 0) {
            alert("Price not found for " + catal.name + ". Please add price first.");
        }
        var variantGroups = service.addIngredientVariants(catal);
        variantGroups.map(function (variantGroup) {
            variantGroup.id = variantGroup.id + "_" + (catalRoot.id).toString();
            variantGroup.variants.map(function (variant) {
                variant.id = variant.id + "_" + (catalRoot.id).toString();
            });
        });
        if (variantGroups.length > 0) {
            groups = groups.concat(variantGroups);
        }
        if (catal.prices[0].dimension === "None" && catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.products.length !== 0) {
            var productGroups = service.addIngredientProducts(catal);
            productGroups.map(function (productGroup) {
                productGroup.id = productGroup.id + "_" + (catalRoot.id).toString();
                productGroup.variants.map(function (variant) {
                    variant.id = variant.id + "_" + (catalRoot.id).toString();
                })
            });
            if (productGroups.length > 0) {
                groups = groups.concat(productGroups);
            }
        }
        return groups;
    }

    function addIngredientProductsAddonsForCombo(catal, catalRoot) {
        var groups = [];
        if (catal.prices == null || catal.prices.length === 0) {
            alert("Price not found for " + catal.name + ". Please add price first.");
        }
        var addonGroups = addIngredientAddons(catal);
        addonGroups.map(function (addonGroup) {
            addonGroup.id = addonGroup.id + "_" + (catalRoot.id).toString();
            addonGroup.addons.map(function (addon) {
                addon.id = addon.id + "_" + (catalRoot.id).toString();
            });
        });
        if (addonGroups.length > 0) {
            groups = groups.concat(addonGroups);
        }
        if (catal.prices[0].dimension === "None" && catal.prices[0].recipe != null && catal.prices[0].recipe.ingredient.products.length !== 0) {
            var productGroups = addIngredientProductsAddons(catal,catalRoot.id);
            productGroups.map(function (productGroup) {
                productGroup.id = productGroup.id + "_" + (catalRoot.id).toString();
                productGroup.addons.map(function (addon) {
                    addon.id = addon.id + "_" + (catalRoot.id).toString();
                })
            });
            if (productGroups.length > 0) {
                groups = groups.concat(productGroups);
            }
        }
        return groups;
    }

    function updateDefaultDependentVariant(item) {
        var variantGroups = item.variant_groups;
        var vgDDepObjMapArray = [];
        if (Array.isArray(variantGroups) && variantGroups.length > 1) {
            variantGroups.map(function (variantGroup, index) {
                var vgDefaultDepObj = {};
                var vgDDepObjMap = {
                    "variantGroupNo": index,
                    "variantDefaultDependentObj": vgDefaultDepObj
                };
                var variants = variantGroup.variants;
                if (Array.isArray(variants) && variants.length) {
                    vgDefaultDepObj = {
                        "default_dependent_variant_id": variants[0].id,
                        "default_dependent_variant_group_id": variantGroup.id
                    };
                    vgDDepObjMap.variantDefaultDependentObj = vgDefaultDepObj;
                }
                vgDDepObjMapArray.push(vgDDepObjMap);
            });
            //console.log(vgDDepObjMapArray);
            var count = 0;
            variantGroups.map(function (variantGroup, index) {
                var variants = variantGroup.variants;
                count = count + 1;
                if (count !== variantGroups.length) { //This will skip the last object to not form a cycle
                    if (Array.isArray(variants) && variants.length) {
                        var defaultDependentObj = vgDDepObjMapArray[count];
                        variants.map(function (variant) {
                            variant.default_dependent_variant_id = defaultDependentObj.variantDefaultDependentObj.default_dependent_variant_id;
                            variant.default_dependent_variant_group_id = defaultDependentObj.variantDefaultDependentObj.default_dependent_variant_group_id;
                        });
                    }
                }
            });
        }
    }

    function getPricingCombinations(item, basePrice,product) {
        var variantGroups = item.variant_groups;
        var pricingCombinations = [];
        if (Array.isArray(variantGroups) && variantGroups.length > 1) {
            var allIdArrays = [];
            var variant_combination_array = [];
            variantGroups.map(function (variantGroup, index) {
                var variants = variantGroup.variants;
                var variant_combination_id_array = [];
                if (Array.isArray(variants) && variants.length) {
                    variants.map(function (variant) {
                        variant_combination_id_array.push(variant.id);
                        variant_combination_array.push({
                            "variantId": variant.id,
                            "variantObj": {
                                "variant_group_id": variantGroup.id,
                                "variant_id": variant.id,
                                "variant_price": variant.price,
                            }
                        });
                    });
                }
                allIdArrays.push(variant_combination_id_array);
            });
            var combinations = allPossibleCases(allIdArrays);
            combinations.map(function (combination) {
                var pricingCombination = Object.assign({}, {
                    "variant_combination": [],
                    "price": 0,
                    "addon_combination": []
                });
                if (combination.length > 0) {
                    for (var k = 0; k < combination.length; k++) {
                        var combinationString = combination[k];
                        //console.log(combinationString);
                        var res = combinationString.split(",");
                        var variantCombinations = [];
                        for (var i = 0; i < res.length; i++) {
                            variant_combination_array.map(function (variantMapObject) {
                                if (variantMapObject.variantId === res[i]) {
                                    pricingCombination.price = pricingCombination.price + variantMapObject.variantObj.variant_price;
                                    variantCombinations.push(variantMapObject.variantObj);
                                }
                            });
                        }
                        pricingCombination.price = pricingCombination.price + basePrice;
                        if (pricingCombination.price === 0) {
                            pricingCombination.price = item.price;
                        }
                        pricingCombination.variant_combination = variantCombinations;
                        if ((item.addon_groups).length > 0) {
                            if(product.subType == 3675){
                                pricingCombination.addon_combination = getAddOnCombinationGroupsForHeroCombo(item,variantCombinations);
                            }else{
                                pricingCombination.addon_combination = getAddOnCombinationGroups(item,variantCombinations);
                            }
                        }
                        //console.log('pricingCombination is::: ', pricingCombination);
                        pricingCombinations.push(pricingCombination);
                    }
                }
            });
        } else {
            variantGroups = item.variant_groups;
            variantGroups.map(function (variantGroup, index) {
                var variants = variantGroup.variants;
                if (Array.isArray(variants) && variants.length) {
                    variants.map(function (variant) {
                        var pricingCombination = Object.assign({}, {
                            "variant_combination": [],
                            "price": 0,
                            "addon_combination": []
                        });
                        var variantCombinations = [];
                        //console.log(variant);
                        pricingCombination.price = parseFloat(pricingCombination.price) + parseFloat(variant.price);
                        pricingCombination.price = parseFloat(pricingCombination.price) + parseFloat(basePrice);
                        variantCombinations.push({
                            "variant_group_id": variantGroup.id,
                            "variant_id": variant.id,
                            "variant_price": variant.price,
                        });
                        pricingCombination.variant_combination = variantCombinations;
                        if ((item.addon_groups).length > 0) {
                            if(product.subType == 3675){
                                pricingCombination.addon_combination = getAddOnCombinationGroupsForHeroCombo(item,variantCombinations);
                            }else{
                                pricingCombination.addon_combination = getAddOnCombinationGroups(item,variantCombinations);
                            }
                        }
                        if (pricingCombination.price === 0) {
                            pricingCombination.price = item.price;
                        }
                        pricingCombinations.push(pricingCombination);
                    });
                }
            });
        }
        return pricingCombinations;
    }


    function getPricingCombinationsForAddonMode(item, basePrice,sizeAddonGroup) {
        var variantGroups = item.variant_groups;
        var pricingCombinations = [];
         sizeAddonGroup.addons.map(function (addon) {
                        var pricingCombination = Object.assign({}, {
                            "variant_combination": [],
                            "price": 0,
                            "addon_combination": []
                        });
                        var variantCombinations = [];
                        //console.log(variant);
                        pricingCombination.price = parseFloat(pricingCombination.price) + parseFloat(addon.price);
                        pricingCombination.price = parseFloat(pricingCombination.price) + parseFloat(basePrice);

                        var addOnCombination = Object.assign({}, addOnCombinationObj);
                        addOnCombination.addon_group_id = sizeAddonGroup.id;
                        addOnCombination.addon_id = addon.id;
                        var addonCombinations = [];
                        addonCombinations.push(addOnCombination);

                        pricingCombination.variant_combination = variantCombinations;
                        if ((item.addon_groups).length > 0) {
                            pricingCombination.addon_combination = getAddOnCombinationGroups(item,addonCombinations);
                        }
                        if (pricingCombination.price === 0) {
                            pricingCombination.price = item.price;
                        }
                        pricingCombinations.push(pricingCombination);
                    });

        return pricingCombinations;
    }


    function getAddOnCombinationGroupsForHeroCombo(item,variantCombinations){
        var groups = [];
        item.addon_groups.forEach(function (addOnsGroup) {
            variantCombinations.forEach(function (ele) {
                var heroProductId = ele.variant_id.split("_")[0];
                var addonGroupSplit = addOnsGroup.id.split("_");
                if(addonGroupSplit.includes(heroProductId) || addonGroupSplit.includes("HERO")){
                    addOnsGroup.addons.forEach(function (addOn) {
                        var addOnCombination = Object.assign({}, addOnCombinationObj);
                        addOnCombination.addon_group_id = addOnsGroup.id;
                        addOnCombination.addon_id = addOn.id;
                        groups.push(addOnCombination);
                    });
                }


            });
        });
        return groups;
    }



    function getAddOnCombinationGroups(item,variantCombinations) {
        var groups = [];
        item.addon_groups.forEach(function (addOnsGroup) {
            if ((addOnsGroup.name != "Addons" && addOnsGroup.name != "PaidAddons" && addOnsGroup.name.slice(0, 10) == "PaidAddons")) {
                addOnsGroup.addons.forEach(function (addOn) {
                    var addOnCombination = Object.assign({}, addOnCombinationObj);
                    var variantType = variantCombinations.variant_group_id;
                    variantCombinations.forEach(function (ele) {
                    if(ele.variant_group_id.slice(ele.variant_group_id.length-4,ele.variant_group_id.length)=="size"){
                        var idArr=ele.variant_id.split("_");
                        var dimension=idArr[idArr.length-1];console.log("**************************");
                        console.log(addOn);console.log(dimension);
                            var mapIdName = {
                                "BK": "BadiKetli",
                                "CK": "ChotiKetli",
                                "MK": "MiniKetli",
                                "Reg": "Regular",
                                "None": "None"
                            };

                              var addOnCombination = Object.assign({}, addOnCombinationObj);
                              var addOnGroupLen = addOnsGroup.id.split("_").length;

                              if (mapIdName[addOn.name.slice(addOn.name.length - 3, addOn.name.length - 1)] == dimension) {
                                            addOnCombination.addon_group_id = addOnsGroup.id;
                                            addOnCombination.addon_id = addOn.id;
                                            groups.push(addOnCombination);
                              }else if(addOnsGroup.id.split("_")[addOnGroupLen-1] == dimension){
                                          addOnCombination.addon_group_id = addOnsGroup.id;
                                          addOnCombination.addon_id = addOn.id;
                                          groups.push(addOnCombination);
                              }
                        }
                        })

                })
            }else if ((addOnsGroup.name == "Sugar" ||  addOnsGroup.name == "Milk Type" ||
            addOnsGroup.name == 'Flavours') && service.addonMode  == true){
                addOnsGroup.addons.forEach(function (addOn) {
                    var addOnCombination = Object.assign({}, addOnCombinationObj);
                    var variantType = variantCombinations.variant_group_id;
                    variantCombinations.forEach(function (ele) {
                        if (ele.variant_group_id.slice(ele.variant_group_id.length - 4, ele.variant_group_id.length) == "size") {
                            var idArr = ele.variant_id.split("_");
                            var dimension = idArr[idArr.length - 1];
                            console.log("**************************");
                            console.log(addOn);
                            console.log(dimension);
                            var mapIdName = {
                                "BK": "BadiKetli",
                                "CK": "ChotiKetli",
                                "MK": "MiniKetli",
                                "Reg": "Regular",
                                "None": "None"
                            };

                            var addOnCombination = Object.assign({}, addOnCombinationObj);
                            var nameString = addOn.name;
                            if(addOn.originalName != null){
                                nameString = addOn.originalName;
                            }
                            if(addOnsGroup.name == "Milk Type" || addOnsGroup.name == "Flavours"){
                                if(addOn.id.includes(dimension)){
                                        addOnCombination.addon_group_id = addOnsGroup.id;
                                        addOnCombination.addon_id = addOn.id;
                                       groups.push(addOnCombination);
                                }else if (addOn.name == "Regular"){
                                    addOnCombination.addon_group_id = addOnsGroup.id;
                                    addOnCombination.addon_id = addOn.id;
                                    groups.push(addOnCombination);
                                }
                            }else{
                                if (mapIdName[nameString.slice(nameString.length - 3, nameString.length - 1)] != null) {
                                    if (mapIdName[nameString.slice(nameString.length - 3, nameString.length - 1)] == dimension) {
                                         addOnCombination.addon_group_id = addOnsGroup.id;
                                         addOnCombination.addon_id = addOn.id;
                                        groups.push(addOnCombination);
                                    }
                                } else {
                                    addOnCombination.addon_group_id = addOnsGroup.id;
                                    addOnCombination.addon_id = addOn.id;
                                    groups.push(addOnCombination);
                                }
                            }
                        }

                    });
                });

            }
            else{
                addOnsGroup.addons.forEach(function (addOn) {
                    var addOnCombination = Object.assign({}, addOnCombinationObj);
                    addOnCombination.addon_group_id = addOnsGroup.id;
                    addOnCombination.addon_id = addOn.id;
                    groups.push(addOnCombination);
                });
            }
        });
        return groups;
    }


    /*function getAddOnCombinationGroupsForAddonMode(item,addonCombinations) {
        var groups = [];
        item.addon_groups.forEach(function (addOnsGroup) {
            if (addOnsGroup.name != "Addons" && addOnsGroup.name != "PaidAddons" && addOnsGroup.name.slice(0, 10) == "PaidAddons") {
                addOnsGroup.addons.forEach(function (addOn) {
                    var addOnCombination = Object.assign({}, addOnCombinationObj);
                    var variantType = variantCombinations.variant_group_id;
                    variantCombinations.forEach(function (ele) {
                        if(ele.variant_group_id.slice(ele.variant_group_id.length-4,ele.variant_group_id.length)=="size"){
                            var idArr=ele.variant_id.split("_");
                            var dimension=idArr[idArr.length-1];console.log("**************************");
                            console.log(addOn);console.log(dimension);
                            var mapIdName = {
                                "BK": "BadiKetli",
                                "CK": "ChotiKetli",
                                "MK": "MiniKetli",
                                "Reg": "Regular",
                                "None": "None"
                            };

                            var addOnCombination = Object.assign({}, addOnCombinationObj);

                            if (mapIdName[addOn.name.slice(addOn.name.length - 3, addOn.name.length - 1)] == dimension) {
                                addOnCombination.addon_group_id = addOnsGroup.id;
                                addOnCombination.addon_id = addOn.id;
                                groups.push(addOnCombination);
                            }
                        }
                    })

                })
            }
            else{
                addOnsGroup.addons.forEach(function (addOn) {
                    var addOnCombination = Object.assign({}, addOnCombinationObj);
                    addOnCombination.addon_group_id = addOnsGroup.id;
                    addOnCombination.addon_id = addOn.id;
                    groups.push(addOnCombination);
                });
            }
        });
        return groups;
    }*/

    function checkForDimensionPaidAddon(dimension,sugarVariant){
        var toAddPaidAddon = false;
        var mapIdName = {
            "BK": "BadiKetli",
            "CK": "ChotiKetli",
            "MK": "MiniKetli",
            "Reg": "Regular",
            "None": "None"
        };
        var dimensionList = ["BadiKetli" ,"ChotiKetli","MiniKetli","Regular" ,"None"]
        if(dimensionList.indexOf(dimension) < 0 ){
            return true;
        }else {
            console.log("acscs");
        }
        if(sugarVariant.split("_").indexOf("PAIDADDON") <0 ){
            toAddPaidAddon = true;
        }else{
            // var dimLen = variant[0].split(",");
            var dim = sugarVariant.split("_")[1];
            if(mapIdName[dim] == dimension){
                toAddPaidAddon = true;
            }
        }
        return toAddPaidAddon;

    }

    function allPossibleCases(arr) {
        if (arr.length === 1) {
            return arr[0];
        } else {
            var result = [];
            var allCasesOfRest = allPossibleCases(arr.slice(1));  // recur with the rest of array
            for (var i = 0; i < allCasesOfRest.length; i++) {
                for (var j = 0; j < arr[0].length; j++) {
                    var variant_combination = [];
                    //if(checkForDimensionPaidAddon(arr[0][j].split("_")[1],allCasesOfRest[i])){
                        variant_combination.push(arr[0][j] + ',' + allCasesOfRest[i]);
                        var variantComArr = variant_combination[0].split(",");
                        if(variantComArr.length === 4){
                            if(checkForDimensionPaidAddon(variantComArr[0].split("_")[1],variantComArr[3])){
                                result.push(variant_combination);
                            }
                        }else{
                            result.push(variant_combination);
                        }
                    //}
                    }
                }
            }
            return result;
        }

    function getPricesOfAddons(product) {
        var productIds = [];
        if (product.prices[0].recipe.ingredient.compositeProduct.details.length > 0) {
                product.prices[0].recipe.ingredient.compositeProduct.details.map(function (prod) {
                    prod.menuProducts.map(function (p) {
                        if (productIds.indexOf(p.product.productId) == -1) {
                            service.priceMap[p.product.productId] = 0;
                            productIds.push(p.product.productId);
                        }
                    })
                })
        }
        service.unitData.products.map(function (prod) {
            if (productIds.indexOf(prod.id)!=-1 && service.priceMap[prod.id] == 0 && prod.prices != null && prod.prices.length > 0) {
                service.priceMap[prod.id] = prod.prices[0].price;
            }
        })
    }

    function getAddOnGroups(product,item) {
        var groups = [];
        service.addUpsellingAddonGroups(product, groups);
        if(service.addonMode == true && product.subType != 3891){
            addRecipeVariantsAddons(product,item,groups);
        }
        service.addNormalProductAddonGroups(product, groups,item);
        service.addFixedMealProductAddonGroups(product, groups);
        service.addComboAddonGroups(product, groups);
        service.addHeroComboAddonGroups(product, groups);
        return groups;
    }

    function getSuperComboAddOnGroups(product, item) {
        var groups = [];
        service.addSuperComboAddonGroups(product, groups);
        return groups;
    }

    function addRecipeVariantsAddons(product,item,groups){
        if (product.prices[0].recipe != null) {
            // adding combo variants addons
            if(product.prices[0].recipe.ingredient.compositeProduct!=null){
                var compositeProductsVariantAddons = addNormalComboAddonVariantGroups(groups,product);
                if (Array.isArray(compositeProductsVariantAddons) && compositeProductsVariantAddons.length) {
                    compositeProductsVariantAddons.map(function (compositeProductsVariantAddon) {
                        groups.push(compositeProductsVariantAddon);
                    });
                }
            }
            var ingredientProductsAddons = addIngredientProductsAddons(product, item.id);
            if (Array.isArray(ingredientProductsAddons) && ingredientProductsAddons.length) {
                ingredientProductsAddons.map(function (ingredientProductsAddon) {
                    groups.push(ingredientProductsAddon);
                });
            }
            var ingredientAddons = addIngredientAddons(product);
            var sugarVariant = null;
            if (Array.isArray(ingredientAddons) && ingredientAddons.length) {
                ingredientAddons.map(function (ingredientAddon) {
                    if(ingredientAddon.name == "Sugar"){
                        sugarVariant = ingredientAddon;
                         var sugarOrder = 7;
                         sugarVariant.addons.forEach(function (addon) {
                                      addon.order = sugarOrder;
                                      sugarOrder--;
                         });
                    }else{
                        groups.push(ingredientAddon);
                    }

                });
            }
         /*   var dimensionAddons = addDimensionAddons(product, item.id);
            if (Array.isArray(dimensionAddons) && dimensionAddons.length) {
                dimensionAddons.map(function (dimensionAddon) {
                    groups.push(dimensionAddon);
                });
            }*/
            if(sugarVariant != null){
                groups.push(sugarVariant);
            }
            product.prices.forEach(function(price){
                if(dimensionHaveOatMilk(price)){
                    addSpecialMilkTypeGroup(product,groups,price);
                }
                if(dimensionHaveCoffeeFlavour(price)){
                    addCoffeeFlavourGroup(product,groups,price);
                }
            })

        }
    }

    function sortDesiChaiAddons(addons, desiChaiAddonOrderMap){
        addons.sort(function (a, b) {
            return desiChaiAddonOrderMap[a.product.productId] != null ? (
                desiChaiAddonOrderMap[b.product.productId] == null || desiChaiAddonOrderMap[a.product.productId] < desiChaiAddonOrderMap[b.product.productId] ? -1 : 1
            ) : 1
    });
}

    function addNormalProductAddonGroups(product, groups,item) {
//        var variantPaidAddOnsList = [1000206,1000205, 1000204, 1000207];
        var isVariantSugar = false;
        var sugarPaidAddonIndex = 5;
        item.variant_groups.forEach(function (variant) {
            if (variant.name == "Sugar") {
                isVariantSugar = true;
            }
        });
        groups.forEach(function (addon) {
            if (addon.name == "Sugar") {
                isVariantSugar = true;
            }
        })

//        variantPaidAddOnsList.forEach(function variantPaidAddOn{
//
//        })
        var productId = product.id.toString().includes("_") ? product.id.toString().split("_")[0] : product.id;
        if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId].length > 0) {
            var dimIds = [];
            if (product.prices != null) {
                product.prices.map(function (prod) {
                    dimIds.push(prod.dimension);
                })
            }
            dimIds.map(function (dimId) {
                var group = angular.copy(addOnGroup);
                displayName = "PaidAddons";
                group.id = productId + "_" + displayName + "_" + dimId;
                if (dimIds.length > 1) {
                    group.name = displayName + " " + dimId;
                }
                else {
                    group.name = displayName;
                }

                group.order = 1;
                var dimName = productId + "_" + dimId;
                console.log(dimName);
                // var idx = service.paidAddonsMap[productId].indexOf(dimName);
                // service.paidAddonsMap.map(function (paidAddOn) {
                //    console.log(paidAddOn);
                //
                // })
                var idx = -1;
                for (var key in service.paidAddonsMap[productId]) {

                    var arr = Object.keys(service.paidAddonsMap[productId][key]);
                    arr.forEach(function (value) {
                        if (value == dimName) {
                            idx = key;
                        }
                    })
                }

                if (idx != -1) {
                    var addOnsList = service.paidAddonsMap[productId][idx][dimName];


                    /*var addOnsList = ([10, 1282].indexOf(productId) >= 0)
                        ? service.addonsMap[11] : service.addonsMap[productId];*/
                    group.addon_min_limit = 0;
                    group.addon_free_limit = -1;//addOnsList.length;
                    if(product.brandId == 6){
                       group.addon_limit = 1;
                    }else{
                       group.addon_limit = addOnsList.length;
                    }
                    group.addons = null;

                    var addOnList = [];
                    var addOnIdx=0;
                    addOnsList.map(function (add, indexs) {
                        var addOnProduct = add;
                        var name = null;
                        var nameString = null;
                        var originalName = null;
                        var productVal = null;
                        if (service.productMap[add.productId] == null || service.productMap[add.productId] == undefined) {
                            name = addOnProduct.name;
                            originalName = addOnProduct.name
                        } else {
                            productVal = service.productMap[add.productId];
                            var productAlias = getProductAlias(productVal);
                            name = productAlias != null ? productAlias : getProductName(productVal.name);
                            originalName = getProductName(productVal.name);
                        }
                        if($rootScope.specialMilkPaidAddonList.indexOf(add.productId) != -1){
                            return;
                        }
                        if($rootScope.coffeeFlavours.indexOf(add.productId) != -1){
                            return;
                        }
                        if(productVal != undefined && productVal != null && $rootScope.variantPaidAddOnsList.includes(productVal.id)){
                           if(name.includes("gur") || name.includes("GUR") || name.includes("Gur")){
                              nameString = "Gur";
                              var aliasString = getProductAlias(productVal);
                              if(aliasString != null && aliasString != "Gur" && aliasString != "GUR"){
                                     nameString = nameString + " (" + aliasString + ")";
                              }
                           }else{
                              var aliasString = getProductAlias(productVal);
                              if(aliasString != null ){
                                      nameString = aliasString ;
                                }
                           }

                        }
                        // option.code.slice(option.code.length - 3, option.code.length - 1)
                        if ( service.paidAddonsIds[add.productId]>= 0) {
                            var addOn = Object.assign({}, addOnObj);
                            var code=add.code.slice(add.code.length - 3, add.code.length - 1);
                            if(code!="BK" && code!="CK" && code!="MK"){
                                code="None";
                            }
                             if($rootScope.variantPaidAddOnsList.includes(add.productId) && isVariantSugar ){
                               var newVariant = Object.assign({},variantObj);
                                newVariant.id = product.id + "_" + code + "_" + add.productId + "_PAIDADDON";
                                newVariant.originalName = originalName;
                                 if (nameString != null) {
                                     newVariant.name = nameString;
                                 }
                                 else {
                                     newVariant.name = name;
                                 }
                                 newVariant.order = 2;
                                 newVariant.default = false;
                                 newVariant.price = service.paidAddonsIds[add.productId];
                                 newVariant.is_veg = 1;
                                 newVariant.gst_details = getGSTDetails(service.productMap[parseInt(add.productId)].taxCode);
                                 if(service.addonMode == false){
                                     item.variant_groups.forEach(function (variantGroup) {
                                         if (variantGroup.name == "Sugar") {
                                             variantGroup.order = 4;
                                             variantGroup.variants.push(newVariant);
                                         }
                                     });
                                 }else{
                                     addOn.id = product.id + "_" + code + "_" + add.productId + "_PAIDADDON";
                                     addOn.originalName = originalName;
                                     if (nameString != null) {
                                         addOn.name = nameString;
                                     }
                                     else {
                                         addOn.name = name;
                                     }
                                     addOn.price = service.paidAddonsIds[add.productId];
                                     addOn.is_veg = true;
                                     addOn.in_stock = 1;
                                     addOn.order = sugarPaidAddonIndex;
                                     sugarPaidAddonIndex--;
                                     if(sugarPaidAddonIndex<0){
                                       sugarPaidAddonIndex = 0;
                                     }
                                     addOn.is_default = null;
                                     addOn.gst_details = getGSTDetails(service.productMap[parseInt(add.productId)].taxCode);
                                     groups.forEach(function (addonGroup) {
                                         if (addonGroup.name == "Sugar") {
                                             addonGroup.order = 4;
                                             addonGroup.addons.push(addOn);
                                         }
                                     });
                                 }
                             }else{
                            addOn.id = product.id + "_" + code + "_" + add.productId + "_PAIDADDON";
                            addOn.name = name;
                            addOn.price = service.paidAddonsIds[add.productId];
                            addOn.is_veg = true;
                            addOn.in_stock = 1;
                            addOn.order = addOnsList.length - indexs;
                            addOn.is_default = null;
                            addOn.gst_details = getGSTDetails(service.productMap[parseInt(add.productId)].taxCode);
                            addOnList.push(addOn);
                            }
                        }
                    });
                    group.addons = addOnList;


                    if (group.addons) {
                        group.addons.sort(customFunction);
                    }
                    if(group.addons.length > 0){
                         groups.push(group);
                    }

                }
            })

        }
        if (service.addonsMap[productId] != null
            && service.addonsMap[productId].length > 0) {
            var group = angular.copy(addOnGroup);
            var displayName = "Addons";
            group.id = productId + "_" + displayName;
            group.name = displayName;
            group.order = 3;
            /*var addOnsList = ([10, 1282].indexOf(productId) >= 0)
                ? service.addonsMap[11] : service.addonsMap[productId];*/
            var addOnsList = service.addonsMap[productId];
            group.addon_min_limit = 0;
            group.addon_free_limit = -1;//addOnsList.length;
            group.addon_limit = addOnsList.length;
            group.addons = null;
            if (product.type === 5) {
                if(product.id == 10){
                    var desiChaiAddonOrderMap = AppUtil.getDesiChaiAddonsOrderForCOD();
                    sortDesiChaiAddons(addOnsList,desiChaiAddonOrderMap);
                }

                var addOnList = [];
                addOnsList.forEach(function (add, indexs) {
                    var addOnProduct = add.product;
                    var name = null;
                    if (service.productMap[add.product.productId] == null || service.productMap[add.product.productId] == undefined) {
                        name = addOnProduct.name;
                    } else {
                        name = getProductName(service.productMap[add.product.productId].name);
                    }
                    var addOnIds = [];
                    if (addOnIds.indexOf(addOnProduct.productId) === -1) {
                        addOnIds.push(addOnProduct.productId);
                        var addOn = Object.assign({}, addOnObj);
                        addOn.id = productId + "_" + add.product.productId;
                        addOn.name = name;
                        addOn.price = 0;
                        addOn.is_veg = true;
                        addOn.in_stock = 1;
                        addOn.order = addOnsList.length - indexs;
                        addOn.is_default = null;
                        addOn.gst_details = null;
                        /*if(add.product.productId == 969) {
                            addOn.is_default = true;
                        }*/
                        addOnList.push(addOn);
                    }
                });
                group.addons = addOnList;
            }
            if(group.addons) {
                group.addons.sort(customFunction);
            }
            if(group.addons != null && group.addons.length > 0){
                   groups.push(group);
            }

        }
    }

    function addFixedMealProductAddonGroups(product, groups) {
        if (product.classification == "MENU" && product.subType == 3891 && product.type == 8) {
            var productId = product.id.toString().includes("_") ? product.id.toString().split("_")[0] : product.id;
            if (product.prices != null && product.prices.length > 0) {
                if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient != null && product.prices[0].recipe.ingredient.compositeProduct != null && product.prices[0].recipe.ingredient.compositeProduct.details != null) {
                    if (product.prices[0].recipe.ingredient.compositeProduct.details.length > 0) {
                        // getPricesOfAddons(product);
                        var idx=product.prices[0].recipe.ingredient.compositeProduct.details.length;
                        product.prices[0].recipe.ingredient.compositeProduct.details.map(function (prod) {
                            var group = angular.copy(addOnGroup);
                            var displayName = prod.name;
                            var noSpaceName = displayName.replace(" ", '');
                            group.id = productId + "_" + noSpaceName;
                            group.name = displayName;
                            group.order = idx;
                            var addOnsList = prod.menuProducts;
                            group.addon_min_limit = 1;
                            group.addon_free_limit = 1;//addOnsList.length;
                            group.addon_limit = 1;
                            group.addons = null;
                            if (product.type === 8) {
                                var addOnList = [];
                                addOnsList.forEach(function (add, indexs) {
                                    var addOnProduct = add.product;
                                    var name = null;
                                    var quantity = add.quantity;
                                    if (service.productMap[add.product.productId] == null || service.productMap[add.product.productId] == undefined) {
                                        name = addOnProduct.name;
                                    } else {
                                        name = getProductName(service.productMap[add.product.productId].name);
                                    }
                                    var addOnIds = [];
                                    if (addOnIds.indexOf(addOnProduct.productId) === -1) {
                                        addOnIds.push(addOnProduct.productId);
                                        var addOn = Object.assign({}, addOnObj);
                                        addOn.id = productId + "_" + add.product.productId;
                                        addOn.name = name +  ((quantity != null && quantity > 1) ? (" x " + quantity) : "");
                                        // addOn.price = service.priceMap[addOnProduct.productId];
                                        addOn.price = 0;
                                        addOn.is_veg = true;
                                        addOn.in_stock = 1;
                                        addOn.order = addOnsList.length - indexs;
                                        addOn.is_default = null;
                                        addOn.gst_details = getGSTDetails(service.productMap[parseInt(addOn.id)].taxCode);
                                        addOnList.push(addOn);
                                    }
                                });
                                group.addons = addOnList;
                            }
                            if (group.addons) {
                                group.addons.sort(customFunction);
                            }
                            idx--;
                            groups.push(group);
                        })
                    }
                }
            }
        }
    }


    function addComboAddonGroups(product, groups) {
        if (product.prices[0] != null && product.subType != 3675 && product.subType != 3676 && product.subType != 3891 ) {
            var dimension = product.prices[0];
            if (dimension.recipe != null) {
                if (dimension.recipe.ingredient.compositeProduct != null) {
                    dimension.recipe.ingredient.compositeProduct.details.map(function (details, index) {
                        if (details.menuProducts != null) {
                            details.menuProducts.map(function (menuProduct) {
                                var reccmndActualProd = service.productMap[menuProduct.product.productId];
                                if (reccmndActualProd != null && (reccmndActualProd.id == 10 || reccmndActualProd.id == 1282)) {
                                    var group = angular.copy(addOnGroup);
                                    var displayName = "Addons";
                                    group.id = reccmndActualProd.id + "_" + displayName;
                                    group.name = displayName;
                                    group.order = 3;
                                    /*var addOnsList = ([10, 11, 12, 50].indexOf(reccmndActualProd.id) >= 0)
                                        ? service.addonsMap[11] : service.addonsMap[reccmndActualProd.id];*/
                                    var addOnsList = service.addonsMap[reccmndActualProd.id];
                                    group.addon_min_limit = 0;
                                    group.addon_free_limit = -1;//addOnsList.length;
                                    group.addon_limit = addOnsList.length;
                                    group.addons = null;
                                    if (reccmndActualProd.type === 5 || reccmndActualProd.type == 6) {
                                        var addOnList = [];
                                        addOnsList.map(function (add, indexs) {
                                            var addOnProduct = add.product;
                                            var name = null;
											if (service.productMap[add.product.productId] == null || service.productMap[add.product.productId] == undefined) {
												name = addOnProduct.name;
											} else {
											    var reccomndedProduct = service.productMap[add.product.productId];
												var productAlias = getProductAlias(reccomndedProduct);
                                                name = productAlias != null ? productAlias : getProductName(reccomndedProduct.name);
											}
                                            var addOnIds = [];
                                            if (addOnIds.indexOf(addOnProduct.productId) === -1) {
                                                addOnIds.push(addOnProduct.productId);
                                                var addOn = Object.assign({}, addOnObj);
                                                addOn.id = reccmndActualProd.id + "_" + add.product.productId;
                                                addOn.name = name;
                                                addOn.price = 0;
                                                addOn.is_veg = true;
                                                addOn.in_stock = 1;
                                                addOn.order = addOnsList.length - indexs;
                                                addOn.is_default = null;
                                                addOn.gst_details = null;
                                                addOnList.push(addOn);
                                            }
                                        });
                                        group.addons = addOnList;
                                    }
                                    if(group.addons) {
                                        group.addons.sort(customFunction);
                                    }
                                    groups.push(group);
                                }

                            });
                        }
                    });
                }
            }
        }
    }

    function addUpsellingAddonGroups(product, groups) {
        if (product.prices[0] != null) {
            var productId = product.id.toString().includes("_") ? product.id.toString().split("_")[0] : product.id;
            var dimension = product.prices[0];
            if (dimension.recipe != null) {
                //multiple recom groups for a product
                if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                    var unitRecommendationsGroupsList = service.menuRecommendationData[productId];
                    // Sort by recommendationTitle in reverse alphabetical order (Z to A) only if brandid == 6
                    if (product.brandId == 6) {
                        unitRecommendationsGroupsList.sort(function(a, b) {
                            var titleA = (a.recommendationTitle || '').toLowerCase();
                            var titleB = (b.recommendationTitle || '').toLowerCase();
                            return titleB.localeCompare(titleA); // Reverse order
                        });
                    }
                    unitRecommendationsGroupsList.forEach(function (recmdGroup, index) {

                        var unitRecommendations = recmdGroup.recommendedItemsList;
                        var group = angular.copy(addOnGroup);
                        group.id = product.id + "_AddOn_RECOM" + "_" + index;
                        group.name = recmdGroup.recommendationTitle;
                        group.order = 0;
                        group.addon_min_limit = 0;
                        group.addon_free_limit = -1;
                        group.addon_limit = unitRecommendations.length;
                        var addOnList = [];
                        unitRecommendations.map(function (recommendedProd, indexs) {
                            var addOn = Object.assign({}, addOnObj);
                            var reccmndActualProd = service.productMap[recommendedProd.productId];
                            if (reccmndActualProd != null && !angular.isUndefined(reccmndActualProd) && reccmndActualProd.prices.length >0 ) {
                                var packagingCharge = getPackagingCharge(reccmndActualProd, null);
                                var price = getUpsellingProdPrice(recommendedProd, reccmndActualProd);
                                if (price != null && !angular.isUndefined(price)) {
                                        price = price - recommendedProd.discountAmount;
                                    //addOn.id = recommendedProd.productId + "_" + product.id + "_RECOM";
                                     var reccmndActualProdAlias = getProductAlias(reccmndActualProd);
                                     var reccmndActualProdName = reccmndActualProdAlias != null ? reccmndActualProdAlias : getProductName(reccmndActualProd.name);
                                    addOn.id = recommendedProd.productId + "_RECOM";
                                        addOn.name = reccmndActualProdName;
                                        if(recommendedProd.discountAmount > 0) {
                                            addOn.name = addOn.name + " - save Rs. " + recommendedProd.discountAmount;
                                        }
                                    addOn.price = parseFloat(packagingCharge) + price;
                                    addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                    addOn.in_stock = 1;
                                        addOn.order = unitRecommendations.length - indexs;
                                    addOn.is_default = null;
                                    addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode);
                                    addOnList.push(addOn);
                                }
                            }else{
                                service.errorList.push("Price not found for " + recommendedProd.productId + " in " + product.name);
                            }
                        });
                        if (addOnList.length > 0) {
                            group.addons = addOnList;
                            groups.push(group);
                        }
                    });
                }
            }
        }
    }

    function addRecipeAddonsForHeroCombo(reccmndActualProd,groups,addonOrder){
        if (reccmndActualProd != null ) {
            var group = angular.copy(addOnGroup);
            var displayName = "Addons" +  "(" + reccmndActualProd.name + ")";
            group.id = reccmndActualProd.id + "_" + displayName;
            group.name = displayName;
            group.order = addonOrder++;
            /*var addOnsList = ([10, 11, 12, 50].indexOf(reccmndActualProd.id) >= 0)
                ? service.addonsMap[11] : service.addonsMap[reccmndActualProd.id];*/
            var addOnsList = service.addonsMap[reccmndActualProd.id];
            group.addon_min_limit = 0;
            group.addon_free_limit = -1;//addOnsList.length;
            group.addon_limit = addOnsList.length;
            group.addons = null;
            if (reccmndActualProd.type === 5 || reccmndActualProd.type == 6) {
                var addOnList = [];
                addOnsList.map(function (add, indexs) {
                    var addOnProduct = add.product;
                    var name = null;
                    if (service.productMap[add.product.productId] == null || service.productMap[add.product.productId] == undefined) {
                        name = addOnProduct.name;
                    } else {
                         var reccomndedProduct = service.productMap[add.product.productId];
                         var productAlias = getProductAlias(reccomndedProduct);
                         name = productAlias != null ? productAlias : getProductName(reccomndedProduct.name);
                    }
                    var addOnIds = [];
                    if (addOnIds.indexOf(addOnProduct.productId) === -1) {
                        addOnIds.push(addOnProduct.productId);
                        var addOn = Object.assign({}, addOnObj);
                        addOn.id = reccmndActualProd.id + "_" + add.product.productId;
                        addOn.name = name;
                        addOn.price = 0;
                        addOn.is_veg = true;
                        addOn.in_stock = 1;
                        addOn.order = addOnsList.length - indexs;
                        addOn.is_default = null;
                        addOn.gst_details = null;
                        addOnList.push(addOn);
                    }
                });
                group.addons = addOnList;
            }
            group.addons.sort(customFunction);
            if(group.addons.length >0){
                groups.push(group);
            }
        }
    }

    function addHeroComboAddonGroups(product, groups) {
        if (product.subType == 3675) {
            var addonOrder = 0;
            if (product.prices[0] != null) {
                var dimension = product.prices[0];
                if (dimension.recipe != null) {
                    if (dimension.recipe.ingredient.compositeProduct != null) {
                        var groupsLen = dimension.recipe.ingredient.compositeProduct.details.length;
                        dimension.recipe.ingredient.compositeProduct.details.map(function (details, index) {
                            if (index == 0  && details.customizable == true) {
                                if (details.menuProducts != null) {
                                    details.menuProducts.map(function (menuProduct) {
                                        var reccmndActualProd = service.productMap[menuProduct.product.productId];
                                        addRecipeAddonsForHeroCombo(reccmndActualProd,groups,addonOrder);
                                        addonOrder++;
                                        var heroProduct = menuProduct;
                                        var heroProductId = menuProduct.product.productId;
                                        var heroProdDim = menuProduct.dimension.code;
                                        var prod = service.productMap[heroProductId];
                                        if (prod == null) {
                                            service.errorList.push("Hero combo " + product.name + " sub item: " + heroProduct.name + " not found");
                                        } else {
                                            prod.prices.map(function (pricess) {
                                                if (pricess.dimension == heroProdDim) {
                                                    var dimensionPropertiesCombo = addIngredientProductsAddonsForCombo(prod, product);
                                                    if (Array.isArray(dimensionPropertiesCombo) && dimensionPropertiesCombo.length) {
                                                        dimensionPropertiesCombo.map(function (dimensionPropertyCombo) {
                                                            dimensionPropertyCombo.name = dimensionPropertyCombo.name + "(" + prod.name + ")";
                                                            dimensionPropertyCombo.order = addonOrder++;
                                                            groups.push(dimensionPropertyCombo);
                                                        });
                                                    }
                                                }
                                            });
                                        }
                                    });
                                }
                            } else if (index != 0){
                                var foodGroupOrder = addonOrder +  (groupsLen - index);
                                addonOrder++;
                                var group = angular.copy(addOnGroup);
                              /*  if (index === 1) {
                                    group.name = "Food";
                                    group.order = addonOrder++;
                                } else {
                                    group.name = "Others";
                                    group.order = addonOrder++;
                                }*/
                                group.name  = details.name;
                                group.order = foodGroupOrder;
                                group.addon_limit = 1;
                                group.id = product.id + "_AddOn_" + group.name + "_HERO";
                                group.addon_min_limit = 0;
                                group.addon_free_limit = -1;
                                var addOnList = [];
                                if (details.menuProducts != null) {
                                    details.menuProducts.map(function (menuProduct) {
                                        var addOn = Object.assign({}, addOnObj);
                                        var menuProductDimension = menuProduct.dimension.code;
                                        var reccmndActualProd = service.productMap[menuProduct.product.productId];

                                        var reccmndActualProdAlias = getProductAlias(reccmndActualProd);
                                        var reccmndActualProdName = reccmndActualProdAlias != null ? reccmndActualProdAlias : getProductName(reccmndActualProd.name);
                                        if (reccmndActualProd != null && !angular.isUndefined(reccmndActualProd)) {
                                            var price = getHeroComboItemPrice(menuProduct, reccmndActualProd);
                                            if (price != null && !angular.isUndefined(price)) {
                                                addOn.id = menuProduct.product.productId + "_" +  menuProductDimension  + "_" + product.id + "_" + group.name + "_HERO";
                                                addOn.name = reccmndActualProdName +
                                                    getDimensionName(menuProductDimension);
                                                var discountedPrice = getDiscountedPrice(details.internalDiscount, price,details.internalDiscountType);
                                                var packagingCharge = getComboPackaginCharges(discountedPrice);
                                                addOn.price = parseInt((parseFloat(packagingCharge) + discountedPrice).toFixed(2));
                                                addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                                addOn.in_stock = 1;
                                                // addOn.order = addOnsList.length - indexs;
                                                addOn.is_default = null;
                                                addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode);
                                                addOnList.push(addOn);
                                            }
                                        }
                                    });
                                    addOnList.sort(function(addon1,addon2){
                                        return addon1.price - addon2.price;
                                    });
                                    var addonListLen = addOnList.length;
                                    addOnList.map(function (addon){
                                        addon.order = addonListLen--;
                                    });
                                    if (addOnList.length > 0) {
                                        group.addons = addOnList;
                                        group.addon_limit = details.maxSelection;
                                        group.addon_min_limit = details.minSelection;
                                        groups.push(group);
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    }

    function addSuperComboAddonGroups(product, groups) {
        if (product.subType == 3676) {
            if (product.prices[0] != null) {
                var dimension = product.prices[0];
                if (dimension.recipe != null) {
                    if (dimension.recipe.ingredient.compositeProduct != null) {
                        var itemLength = dimension.recipe.ingredient.compositeProduct.details.length;
                        dimension.recipe.ingredient.compositeProduct.details.map(function (details, index) {
                            var group = angular.copy(addOnGroup);
                            group.order = itemLength--;
                            group.addon_limit = 1;
                            group.id = product.id + "_AddOn_" + index + "_SUPERCOMBO";
                            group.name = details.name;
                            group.addon_min_limit = 1;
                            group.addon_free_limit = 1;
                            var addOnList = [];
                            if (details.menuProducts != null) {
                                var menuProductLength = details.menuProducts.length;
                                details.menuProducts.map(function (menuProduct,ind) {
                                    var addOn = Object.assign({}, addOnObj);
                                    var reccmndActualProd = service.productMap[menuProduct.product.productId];
                                     var reccmndActualProdAlias = getProductAlias(reccmndActualProd);
                                     var reccmndActualProdName = reccmndActualProdAlias != null ? reccmndActualProdAlias : getProductName(reccmndActualProd.name);
                                    if (reccmndActualProd != null && !angular.isUndefined(reccmndActualProd)) {
                                        var price = getSuperComboItemPrice(menuProduct, reccmndActualProd);
                                        if (price != null && !angular.isUndefined(price)) {
                                            addOn.id = menuProduct.product.productId + "_" + menuProduct.dimension.code + "_" + product.id + "_" + index + "_SUPERCOMBO";
                                            var idVal = addOn.id;
                                            if(menuProduct.product.productId == 10){
                                                var addonString = "";
                                                service.metadata.desiChaiCustomProfiles.map(function (profile) {
                                                    if(profile.superComboProduct == true){
                                                        addonString = addonString + "( ";
                                                        addOn.id = addOn.id + "_" + profile.profileName;
                                                        var listLength = profile.addons.length;
                                                        if(listLength == 1){
                                                            addonString = addonString + profile.addons[0].name;
                                                        }
                                                        else{
                                                           for(var list of profile.addons){
                                                               addonString =  addonString + list.name;
                                                               if(profile.addons.length <= listLength){
                                                                  addonString =  addonString + ",";
                                                                  listLength--;
                                                               }
                                                           }
                                                        }
                                                        addonString = addonString + " )";
                                                    }
                                                });
                                                addOn.name = menuProduct.product.name + getDimensionName(menuProduct.dimension.name) +
                                                             addonString;
                                            }
                                            else{

                                               addOn.name = reccmndActualProdName + getDimensionName(menuProduct.dimension.name);
                                            }
                                            //Price already calculated in the combo price map (packaging charge is already added)
                                            addOn.price = $rootScope.priceMapForSuperCombo[idVal].price;
                                            addOn.is_veg = isVeg(reccmndActualProd.attribute);
                                            addOn.in_stock = 1;
                                            addOn.is_default = null;
                                            addOn.gst_details = getGSTDetails(reccmndActualProd.taxCode);
                                            addOnList.push(addOn);
                                        }
                                    }
                                });
                                if (addOnList.length >= 1) {
                                    if([].indexOf(product.id) >-1){
                                       addOnList.sort(function (a, b) {
                                                   return   b.price  - a.price;
                                       });
                                    }else{
                                      addOnList.sort(function (a, b) {
                                                    return a.price - b.price;
                                      });
                                    }

                                    addOnList.forEach(function (addOn) {
                                        addOn.order = menuProductLength--;
                                    });
                                    group.addons = addOnList;
                                    groups.push(group);
                                }
                            }
                        });
                    }
                }
            }
        }
    }

    function updateDesiChaiAsPerCustomProfiles(catalog, dimensionName, order) {
        var productsAdded = 0;
        if (catalog.id === "10" || catalog.id.startsWith("10_")) {
            service.metadata.desiChaiCustomProfiles.map(function (profile) {
               /* if(profile.superComboProduct == true){
                   return;
                }*/
                if (profile.profileType === "CUSTOMIZATION") {
                    setCustomizationTypeProfile(catalog, profile);
                }
                if (profile.profileType === "PRODUCT") {
                    var catalogEntry = angular.copy(catalog);
                    setCustomizationTypeProfile(catalogEntry, profile);
                    catalogEntry.name = profile.productName;
                    catalogEntry.order = order;
                    order--;
                    if (dimensionName != null) {
                        catalogEntry.name = catalogEntry.name + " (" + dimensionName + ") ";
                    }
                    if (profile.productDescription != null) {
                        catalogEntry.description = profile.productDescription;
                    }
                    var profileName = profile.profileName.replaceAll(" ", "");
                    catalogEntry.id = catalogEntry.id + "_" + profileName;
                    catalogEntry.variant_groups.map(function (varGroup) {
                        varGroup.id = varGroup.id + "_" + profileName;
                        varGroup.variants.map(function (variant) {
                            variant.id = variant.id + "_" + profileName;
                            if (variant.default_dependent_variant_id != null) {
                                variant.default_dependent_variant_id += "_" + profileName;
                            }
                            if (variant.default_dependent_variant_group_id != null) {
                                variant.default_dependent_variant_group_id += "_" + profileName;
                            }
                        });
                    });
                    catalogEntry.pricing_combinations.forEach(function (pricingComb) {
                        pricingComb.addon_combination.forEach(function (addonComb) {
                            addonComb.addon_group_id += "_" + profileName;
                            addonComb.addon_id += "_" + profileName;
                        });
                    });
                    var addons = [];
                    profile.addons.map(function (addon) {
                        addons.push(addon.id);
                    });
                    var customProfileProductAddonGroups = [];
                    catalogEntry.addon_groups.map(function (addonGroup) {
                        addonGroup.addons.map(function (addon) {
                            addon.id += "_" + profileName;
                        });
                        if (addonGroup.name.toLowerCase().includes("addon") && !addonGroup.name.toLowerCase().includes("paidaddons")) {
                            addonGroup.id += "_" + profileName;
                            var newAddons = addonGroup.addons.filter(function (addon) {
                                return addons.filter(value => addon.id.split("_")[1] == (value)).length > 0;
                            });
                            addonGroup.addons = newAddons;
                            addonGroup.addon_min_limit = addons.length;
                            addonGroup.addon_limit = newAddons.length;
                            addonGroup.addon_free_limit = -1;//newAddons.length;
                            addonGroup.addons.map(function (addon) {
                                addon.is_default = true;
                            });
                        }else{
                            customProfileProductAddonGroups.push(addonGroup);
                        }
                    });
                    catalogEntry.addon_groups = customProfileProductAddonGroups;
                    catalogEntry.pricing_combinations.map(function (pricingComb) {
                        var newAddonCombs = [];
                        pricingComb.addon_combination = getAddOnCombinationGroups(catalogEntry,pricingComb.variant_combination);

                    });
                    catalogEntry.pricing_combinations.forEach(function (pricingComb) {
                        var newVarCombs = [];
                        pricingComb.variant_combination.forEach(function (varComb) {
                            var newVarComb = angular.copy(varComb);
                            newVarComb.variant_group_id = newVarComb.variant_group_id + "_" + profileName;
                            newVarComb.variant_id = newVarComb.variant_id + "_" + profileName;
                            newVarCombs.push(newVarComb);
                        });
                        pricingComb.variant_combination = newVarCombs;
                    });

                    service.catalog.items.push(catalogEntry);
                }
            });
        }
        return order;
    }

    function setCustomizationTypeProfile(catalog, profile) {
        catalog.variant_groups.map(function (varGroup) {
            var newValues;
            if (profile.dimensionType != null && varGroup.name.toLowerCase().includes("size")) {
                newValues = [];
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return propValue.name.toLowerCase().includes(profile.dimensionType.toLowerCase())
                }));
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return !propValue.name.toLowerCase().includes(profile.dimensionType.toLowerCase())
                }));
                newValues.forEach(function (propValue) {
                    propValue.default = propValue.name.toLowerCase().includes(profile.dimensionType.toLowerCase());
                });
                varGroup.variants = newValues;
            }
            if (profile.milkType != null && varGroup.name.toLowerCase().includes("milk")) {
                newValues = [];
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return propValue.name.toLowerCase().includes(profile.milkType.toLowerCase())
                }));
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return !propValue.name.toLowerCase().includes(profile.milkType.toLowerCase())
                }));
                newValues.forEach(function (propValue) {
                    propValue.default = propValue.name.toLowerCase().includes(profile.milkType.toLowerCase());
                });
                varGroup.variants = newValues;
            }
            if (profile.sugarType != null && varGroup.name.toLowerCase().includes("sugar")) {
                newValues = [];
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return propValue.name.toLowerCase().includes(profile.sugarType.toLowerCase())
                }));
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return !propValue.name.toLowerCase().includes(profile.sugarType.toLowerCase())
                }));
                newValues.forEach(function (propValue) {
                    propValue.default = propValue.name.toLowerCase().includes(profile.sugarType.toLowerCase());
                });
                varGroup.variants = newValues;
            }
            if (profile.pattiType != null && varGroup.name.toLowerCase().includes("patti")) {
                newValues = [];
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return propValue.name.toLowerCase().includes(profile.pattiType.toLowerCase())
                }));
                newValues = newValues.concat(varGroup.variants.filter(function (propValue) {
                    return !propValue.name.toLowerCase().includes(profile.pattiType.toLowerCase())
                }));
                newValues.forEach(function (propValue) {
                    propValue.default = propValue.name.toLowerCase().includes(profile.pattiType.toLowerCase());
                });
                varGroup.variants = newValues;
            }
        });
        var addons = [];
        profile.addons.map(function (addon) {
            addons.push(addon.name);
        });
        catalog.addon_groups.map(function (addonGroup) {
            if (addonGroup.name.toLowerCase().includes("addon") && !addonGroup.name.toLowerCase().includes("paidaddons")) {
                var newAddons = [];
                newAddons = newAddons.concat(addonGroup.addons.filter(function (addon) {
                    return addons.filter(value => addon.name.toLowerCase().includes(value.toLowerCase())).length > 0;
                }));
                newAddons = newAddons.concat(addonGroup.addons.filter(function (addon) {
                    return addons.filter(value => addon.name.toLowerCase().includes(value.toLowerCase())).length === 0;
                }));
                addonGroup.addons = newAddons;
            }
        });
    }

    function getDiscountedPrice(discountValue, price,type) {
        var comboPrice = 0;
        if (discountValue != null && discountValue !== 0 && type =="PERCENTAGE") {
            comboPrice = price - ((price * discountValue) / 100).toFixed(2);
        }else if (discountValue !=  null && discountValue !=0 && type == "FIXED"){
            comboPrice = price - discountValue;
        } else {
            comboPrice = price;
        }
        return comboPrice;
    }

    function getHeroComboItemPrice(recommendedProd, reccmndActualProd) {
        var priceProd;
        var recomendedProdDimension;
        if (recommendedProd.dimension == null) {
            recomendedProdDimension = "None";
        } else {
            recomendedProdDimension = recommendedProd.dimension.name;
        }
        reccmndActualProd.prices.map(function (pricess) {
            if (pricess.dimension == recomendedProdDimension) {
                priceProd = pricess.price;
            }
        });
        return priceProd;
    }

    function getSuperComboItemPrice(recommendedProd, reccmndActualProd) {
        var priceProd;
        var recomendedProdDimension;
        if (recommendedProd.dimension == null) {
            recomendedProdDimension = "None";
        } else {
            recomendedProdDimension = recommendedProd.dimension.code;
        }
        reccmndActualProd.prices.map(function (pricess) {
            if (pricess.dimension == recomendedProdDimension) {
                priceProd = pricess.price;
            }
        });
        return priceProd;
    }

    function getUpsellingProdPrice(recommendedProd, reccmndActualProd) {
        var priceProd;
        if (recommendedProd.dimension != null) {
            reccmndActualProd.prices.map(function (pricess) {
                if (pricess.dimension == recommendedProd.dimension) {
                    priceProd = pricess.price;
                }
            });
        } else {
            priceProd = reccmndActualProd.prices[0].price;
        }
        return priceProd;
    }

    function getTaxValue(code, taxCode) {
        var taxValue = 0;
        if (taxCode === "COMBO") {
            taxValue = 2.5;
        } else {
            var tax = service.taxMap[taxCode];
            taxValue = parseFloat(tax.state[code]);
        }
        return taxValue;
    }

    function getGSTDetails(taxCode) {
        var gstDetails = Object.assign({}, taxGSTDetailObj);
        gstDetails.igst = 0;
        gstDetails.sgst = getTaxValue("sgst", taxCode);
        gstDetails.cgst = getTaxValue("cgst", taxCode);
        gstDetails.inclusive = false;
        if(gstDetails.sgst == 2.5){
			gstDetails.gst_liability = "SWIGGY";
		}else{
			gstDetails.gst_liability = "VENDOR";
		}
        return gstDetails;
    }

    function getPackagingCharge(product) {
        var packagingCharge = 0;
        if (service.metadata.filterProductsForNoPackagingCharges != undefined && service.metadata.filterProductsForNoPackagingCharges != null
            && service.metadata.filterProductsForNoPackagingCharges.includes(product.id)) {
            return 0;
        }
        if(product.billType == 'MRP'){
                 return 0;

       }
        if(service.productPackagingMappings != null && service.productPackagingMappings[product.id] != null){
                    var productPackagingMapping  = service.productPackagingMappings[product.id];
                    if(productPackagingMapping.packagingType === "PERCENTAGE"){
                        var itemPrice = getItemPrice(product);
                                    if (itemPrice == null) {
                                        itemPrice = 0;
                                    }
                                    packagingCharge = ((productPackagingMapping.packagingValue / 100) * itemPrice).toFixed(2);
                                    /*var mulBy100=Math.floor(((service.metadata.packagingValue / 100) * itemPrice)*100)/100;
                                    packagingCharge=mulBy100.toFixed(2);*/

                                    return parseInt(packagingCharge);
                    }else{
                       packagingCharge = productPackagingMapping.packagingValue.toFixed(2);
                                   /*var mulBy100=Math.floor(service.metadata.packagingValue *100)/100;
                                   packagingCharge=mulBy100.toFixed(2);*/
                                   return parseInt(packagingCharge);
                    }
        }

        if (service.metadata.packagingType === "PERCENTAGE") {
            var itemPrice = getItemPrice(product);
            if (itemPrice == null) {
                itemPrice = 0;
            }
            packagingCharge = ((service.metadata.packagingValue / 100) * itemPrice).toFixed(2);
            /*var mulBy100=Math.floor(((service.metadata.packagingValue / 100) * itemPrice)*100)/100;
            packagingCharge=mulBy100.toFixed(2);*/

            return parseInt(packagingCharge);
        } else if (service.metadata.packagingType === "FIXED") {
            packagingCharge = service.metadata.packagingValue.toFixed(2);
            /*var mulBy100=Math.floor(service.metadata.packagingValue *100)/100;
            packagingCharge=mulBy100.toFixed(2);*/
            return parseInt(packagingCharge);
        }
    }

    function getPackagingChargeForSuperCombo(product) {
        var packagingCharge = 0;
//        if (product.billType == 'MRP') {
//            return 0;
//        }
//        if (service.metadata.packagingType === "PERCENTAGE") {
//            var itemPrice = getItemPrice(product);
//            if (itemPrice == null) {
//                itemPrice = 0;
//            }
//            packagingCharge = ((service.metadata.packagingValue / 100) * itemPrice).toFixed(2);
//            return Math.round(parseFloat(packagingCharge));
//        } else if (service.metadata.packagingType === "FIXED") {
//            packagingCharge = service.metadata.packagingValue.toFixed(2);
//            return Math.round(parseFloat(packagingCharge));
//        }
          return packagingCharge;
    }

    function getComboPackaginCharges(itemPrice) {
        var packagingCharge = 0;
        if (service.metadata.packagingType === "PERCENTAGE") {
            // packagingCharge = ((service.metadata.packagingValue / 100) * itemPrice).toFixed(2);
            var mulBy100=Math.floor(((service.metadata.packagingValue / 100) * itemPrice)*100)/100;
            packagingCharge=mulBy100.toFixed(2);
            return packagingCharge;
        } else if (service.metadata.packagingType === "FIXED") {
           /* // packagingCharge = service.metadata.packagingValue.toFixed(2);
            var mulBy100=Math.floor(service.metadata.packagingValue *100)/100;
            packagingCharge=mulBy100.toFixed(2);
            return parseInt(packagingCharge);*/
            return  0;
        }
    }

    function getItemPrice(product) {
        if(product.subType == 3675){
            return 0;
        }
        var price = 0;
        var filteredPrices = product.prices.filter(function(price){
             return (product.prices.length ==1 || price.dimension != "1 Pack");
        });
        price = filteredPrices[0].price;
        if (filteredPrices.length > 1) {
            filteredPrices.map(function (priceObj) {
                if (priceObj.price < price) {
                    price = priceObj.price;
                }
            })
        }
        return price;
    }

    function getItemPriceForPackaging(product, dcDimension) {
        var price = null;
        var pid = parseInt(product.id);
        if (([10, 1282].indexOf(pid) >= 0) && product.prices.length > 1) {
            if (dcDimension != null) {
                price = product.prices[1].price;
            }
            else {
                price = product.prices[0].price;
            }
        } else {
            if (product.prices.length == 1) {
                price = product.prices[0].price;
            } else {
                product.prices.map(function (priceObj) {
                    if (price == null) {
                        price = priceObj.price;
                    } else {
                        if (price > priceObj.price) {
                            price = priceObj.price;
                        }
                    }
                });
            }
        }
        return price;
    }

    function getProductAlias(product) {
        var alias = null;
        product.prices.map(function (price) {
            if (price.aliasProductName != null) {
                alias = price.aliasProductName;
            }
        });
        return alias;
    }

    function getProductName(productName) {
        if (productName.indexOf("Kulhad Wali") >= 0) {
            return productName.trim().replace("Kulhad Wali", "");
        } else {
            return productName.trim();
        }
    }

    function getProductDescription(productDescription, productName) {
        if (productDescription == null) {
            return productDescription;
        }
        if (productName.indexOf("Kulhad Chai") < 0 && productDescription.indexOf("served in a Kulhad") >= 0) {
            return productDescription.trim().replace("served in a Kulhad", "");
        } else {
            return productDescription.trim();
        }
    }

    function getDimensionName(dimension) {
        if (dimension == null || dimension == "None" || dimension == "none") {
            return "";
        }else if(dimension == "1 Pack"){
            return  " (" + dimension + ") "
        }else {
            return " (" + dimension.replace(/([A-Z])/g, ' $1').substring(1) + ") ";
        }
    }

    function getDimensionDescription(dimension, dimensionDescriptor) {
        if (dimensionDescriptor) {
            return dimensionDescriptor;
        }
        var desc = "";
        switch (dimension) {
            case "MiniKetli":
                desc = "Serves 2, 250ml";
                break;
            case "ChotiKetli":
                desc = "Serves 4, 400ml";
                break;
            case "BadiKetli":
                desc = "Serves 10, 1000ml";
                break;
        }
        return desc;
    }

    function isVeg(type) {
        //return !(type === "NON VEG");
        var isVeg = true;
        if (type == null) {
            return isVeg;
        }
        if (type !== "VEG") {
            isVeg = false;
        }
        return isVeg;
    }

    function buildVariant(id, name, isDefault) {
        var variant = Object.assign({}, variantObj);
        variant.id = id;
        variant.name = name;
        variant.price = 0;
        variant.default = isDefault;
        variant.order = 1;
        variant.in_stock = 1;
        variant.is_veg = 1;
        variant.gst_details = null;
        return variant;
    }

    function createFilteredName(name) {
        name = name.replace(/[^\w\s]/gi, '').replaceAll(" ", "");
        return name;
    }

    return service;
}]);
