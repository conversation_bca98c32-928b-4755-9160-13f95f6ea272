angular.module('adminapp').directive('customModal', function() {
    return {
        restrict: 'E',
        transclude: true,
        scope: {
            isOpen: '=',
            onSubmit: '&',
            onClose: '&',
            width: '@',
            height: '@',
            title: '@',
            showSubmit: '='
        },
        template: `
            <div class="c-modal-overlay" ng-class="{ 'show': isOpen }" ng-click="close($event)">
                <div class="c-modal-container c-animated-pop"
                     ng-style="{'width': width || '60%', 'height': height || 'auto'}"
                     ng-click="$event.stopPropagation()">

                    <div class="c-modal-header">
                        <h3>{{ title || 'Modal Title' }}</h3>
                        <button class="c-modal-close" ng-click="close()">✖</button>
                    </div>

                    <div class="c-modal-body" ng-transclude></div>

                    <div class="c-modal-footer">
                        <button class="c-btn c-btn-secondary" ng-click="close()">Close</button>
                        <button class="c-btn c-btn-primary" ng-if="showSubmit" ng-click="submit()">Submit</button>
                    </div>
                </div>
            </div>
        `,
        link: function(scope) {
            scope.close = function(event) {
                if (event) event.stopPropagation();
                scope.isOpen = false;
                if (scope.onClose) scope.onClose();
            };

            scope.submit = function() {
                if (scope.onSubmit) scope.onSubmit();
            };
        }
    };
});
