/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('adminapp').service('zomatoNewCatalogService', ['AppUtil','$rootScope', function (AppUtil,$rootScope) {

    var service = {};
    service.prepareCatalogs = prepareCatalogs;
    service.updateDynamicPricing = updateDynamicPricing;
    service.prepareAddonsMap = prepareAddonsMap;
    service.preparePaidAddonsMap = preparePaidAddonsMap;
    service.prepareFixedMealProductMap = prepareFixedMealProductMap;
    service.prepareTaxMap = prepareTaxMap;
    service.createProductMap = createProductMap;
    service.createDineInProductMap = createDineInProductMap;
    service.createProductNutritionMap = createProductNutritionMap;
    service.createProductMeatTypeMap = createProductMeatTypeMap;
    service.createProductAllergenTypeMap = createProductAllergenTypeMap;
    service.createProductServingInfoTypeMap=createProductServingInfoTypeMap;
    service.createProductServingSizeTypeMap=createProductServingSizeTypeMap;
    service.sortProductPrices = sortProductPrices;
    service.setMenuCategories = setMenuCategories;
    service.addNewCategory = addNewCategory;
    service.addNewSubCategory = addNewSubCategory;
    service.addCatalogCharges = addCatalogCharges;
    service.setMenuCatalogues = setMenuCatalogues;
    service.addNewCatalogue = addNewCatalogue;
    service.addNewCombo = addNewCombo;
    service.addHeroComboV1 = addHeroComboV1;
    service.addHeroComboV2 = addHeroComboV2;
    service.setSuperCombos = setSuperCombos;
    service.addSuperCombo = addSuperCombo;
    service.addSelections = addSelections;
    service.addSelectionEntities = addSelectionEntities;
    service.splitDesiChaiDimensions = splitDesiChaiDimensions;
    service.getCatalogueMapId = getCatalogueMapId;
    service.updateDesiChaiAsPerCustomProfiles = updateDesiChaiAsPerCustomProfiles;
    service.checkGstTags = checkGstTags;
    service.addonMode = true;
    service.variantPaidAddOnsList = [1000206,1000205,1000204,1000207,1000441,1000440,1000439,1000438,1000452,1000453,1000454,1000455];
    service.specialMilkPaidAddonList = [1000703,1000704,1000705,1000706,1000799,1000814,1000828,1000800,1000815,1000827];
    service.coffeeFlavours = [1000801,1000802,1000803,1000811,1000812,1000813,1000816,1000817,1000818];
    service.splitAllDesiChaiDimensions = false;
    service.errorList = [];
    service.addonsMap = {};
    service.fixedMealProductMap ={};
    service.paidAddonsMap = {};
    service.taxMap = {};
    service.brand = null;
    service.metadata = null;
    service.addPackaging = null;
    service.unitId = null;
    service.unitData = null;
    service.menuSequence = null;
    service.productMap = null;
    service.productMeatTypeMap = null;
    service.productNutritionMap = null;
    service.productAllergenTypeMap=null;
    service.productServingInfoMap=null;
    service.productServingSizeMap=null;
    service.brand = null;
    service.menuRecommendationData = null;
    service.productImages = null;
    service.catalog = null;
    $rootScope.splitDimensionProductIds = [];
    $rootScope.priceMapForSuperCombo = {};
    $rootScope.combosMainPrice = {};
    $rootScope.heroComboVersion = 'V2';
    $rootScope.heroComboIds = [];
    service.splitDesiChaiDimensionName = "ChotiKetli";
    AppUtil.getProductIdsForSplitDimension();

    var zomatoMenu = {
        "outletId": "",
        "charges": [],
        "menu": {
            "categories": [], // zomatoCategory
            "catalogues": [], // zomatoCatalogues
            "combos": [], //zomatoCombos
            "modifierGroups": [] // zomatoModifierGroups
        },
    };

    var zomatoTaxes = {
            "name": null,
            "display_name": null,
            "calculation_type": null,
            "value": null,
            "service": null,
            "slug": null
        }
    ;

    var zomatoCharges = {
        "slug": null,
        "vendorEntityId": null,
        "multiItem": null,
        "chargeValue": null,
        "applicableOnItem": null
        //"taxes": []
    };

    var zomatoCategory = {
        "vendorEntityId": null,
        "name": null,
        "order": null,
        "subCategories": [] // zomatoSubcategory
    };

    var zomatoSubcategory = {
        "vendorEntityId": null,
        "name": null,
        "order": null,
        "entities": [], // zomatoEntities

    };

    var zomatoEntities = {
        "entityType": null,
        "order": null,
        "vendorEntityId": null
    };

    var zomatoCatalogues = {
        "description": null,
        "name": null,
        "tags": [],
        "inStock": null,
        "variants": [], // zomatoVariants
        "is_visible": null,
        "vendorEntityId": null,
        "properties": [], // zomatoProperties
        "imageUrl": null,
        //"taxes": [],
        "taxGroups": [],
        "charges": [], // zomatoCharge
        "preparationTime": null
    };

    var zomatoProperties = {
        "propertyId": null,
        "name": null,
        "order": null,
        "propertyValues": [], // zomatoPropertyValues
        "vendorEntityId": null
    };

    var zomatoPropertyValues = {
        "value": null,
        "vendorEntityId": null
    };

    var zomatoCharge = {
        "vendorEntityId": null
    };

    var zomatoVariants = {
        "vendorEntityId": null,
        "propertyValues": [],// zomatoPropertyValues
        "prices": [],// zomatoPrices
        "modifierGroups": []
    };

    var zomatoModifierGroup = {
        "vendorEntityId": null,
        "order": null,
        "recipeVariant":false
    };

    var zomatoPropertyValue = { // of zomatoVariants
        "vendorEntityId": null,
    };

    var zomatoPrices = {
        "service": null,
        "price": null
    };

    var zomatoModifierGroups = {
        "vendorEntityId": null,
        "name": null,
        "max": null,
        "displayName": null,
        "min": null,
        "variants": [] // zomatoVariants
    };

    var zomatoVariant = {
        "variantId": null,
        "order": null,
        "vendorEntityId": null
    };

    var zomatoComboCatalogue = {
        "vendorEntityId": null,
        "name": null,
        "description": null,
        "reducedPrice": null,
        "taxGroups": [{slug: null}],
        "charges": [], // zomatoCharge
        "type": null, //BASIC/BOX
        "subtitle": null,
        "inStock": null,
        "media": [], //zomatoMediaObj
        "selections": [], //zomatoSelection
        "services": [], //service
    };

    var zomatoMediaObj = {
        "url": null,
        "usageType": "FOODSHOT" //FOODSHOT/COVER_IMAGE_URL
    };

    var zomatoSelections = {
        "vendorEntityId": null,
        "title": null,
        "maxSelections": null,
        "minSelections": null,
        "maxSelectionsPerItem": null,
        "discountValue": null,
        "selectionEntities": [] //zomatoSelectionEntities
    };

    var zomatoSelectionEntities = {
        "variantVendorEntityId": null,
        "catalogueVendorEntityId": null
    };

    var zomatoServices = {
        "service": null
    };

    var milkOption = ["Regular Milk", "Full Doodh (Full Milk)", "Doodh Kum (Less Milk)", "Paani Kum (More Milk)"];


    var productPriceSuperCombo = {
        "price" : null,
        "discount" : null,
        "discountType" : null,
        "originalPrice" : null,
        "productId" : null
    };

    /*var taxGroupMap = {
        "00009963": "GST_D_P_5.00",
        "09021090": "GST_D_P_5.00",
        "19054000": "GST_D_P_5.00",
        "21069099": "GST_D_P_12.00",
        "21069040": "GST_D_P_5.00",
        "21012010": "GST_D_P_18.00",
        "09024090": "GST_D_P_5.00",
        "09109100": "GST_D_P_5.00"
    };*/

    /*var taxMap = new Map([["00009963", "D_P_2.50"],
        ["09021090", "D_P_2.50"],
        ["19054000", "D_P_2.50"],
        ["21069099", "D_P_6.00"],
        ["21069040", "D_P_2.50"],
        ["21012010", "D_P_9.00"],
        ["09024090", "D_P_2.50"],
        ["09109100", "D_P_2.50"]]);*/

    var cataloguesIds = [];
    var modifierGroupIds = [];

    function prepareAddonsMap() {
        service.addonsMap = {};
        service.unitData.products.forEach(function (prod) {
            if (prod.type === 5 && [501, 502, 503].indexOf(prod.subType) >= 0) {
                if (prod.prices.length > 0 && prod.prices[0].recipe != null && prod.prices[0].recipe.addons.length > 0) {
                    service.addonsMap[prod.id] = prod.prices[0].recipe.addons;
                }
            }
        });
    }

    function preparePaidAddonsMap() {
        service.paidAddonsMap = {};
        service.unitData.products.forEach(function (prod) {
            if (prod.prices != null && prod.prices.length > 0 && prod.prices[0].recipe != null &&
                prod.prices[0].recipe.options != null && prod.prices[0].recipe.options.length > 0) {
                prod.prices.map(function (price) {
                    var productTypePaidAddOns = [];
                    if (price.recipe != null && price.recipe.options != null) {
                    price.recipe.options.forEach(function (option) {
                            if (option.type === "PRODUCT" &&
                                productTypePaidAddOns.filter(function (paidAddon) {
                                    return paidAddon.id === option.id
                                }).length >= 0) {
                            productTypePaidAddOns.push(option);
                    }
                    });
                    }
                    if (productTypePaidAddOns.length > 0) {
                        if (service.paidAddonsMap[prod.id] == null) {
                            service.paidAddonsMap[prod.id] = {}
                        }
                        service.paidAddonsMap[prod.id][price.dimension] = productTypePaidAddOns;
                    }
                });
            }
        });
    }

    function prepareFixedMealProductMap() {
        service.fixedMealProductMap = {};
        service.unitData.products.forEach(function (prod) {
            if (prod.classification === "MENU" && prod.type === 8 && prod.subType === 3891 && prod.prices != null
                && prod.prices.length > 0 && prod.prices[0].recipe != null) {
                prod.prices.map(function (price) {
                    var key = prod.id+"_"+price.dimension;
                    var fixedMealCompositeProducts = {};
                    if (price.recipe != null && price.recipe.ingredient != null && price.recipe.ingredient.compositeProduct != null && price.recipe.ingredient.compositeProduct.details != null && price.recipe.ingredient.compositeProduct.details.length > 0) {
                        price.recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail) {
                            var fixedMealOptions = {};
                            var fixedMealCompositeProductName = compositeProductDetail.name !== null ? compositeProductDetail.name : "FixedMeal_Option";
                            compositeProductDetail.menuProducts.forEach(function (menuProductDetail) {
                                var fixedMealOption = angular.copy(menuProductDetail);
                                fixedMealOption.min = compositeProductDetail.minSelection ? compositeProductDetail.minSelection : 1;
                                fixedMealOption.max = compositeProductDetail.maxSelection ? compositeProductDetail.maxSelection : 1;
                                fixedMealOption.maxSelectionsPerItem = fixedMealOption.max;
                                var fixedMealOptionKey = menuProductDetail.product.productId + "_" + menuProductDetail.dimension.name;
                                fixedMealOptions[fixedMealOptionKey] = fixedMealOption;
                            });
                            fixedMealCompositeProducts[fixedMealCompositeProductName] = angular.copy(fixedMealOptions);
                        });
                    }
                    service.fixedMealProductMap[key] = angular.copy(fixedMealCompositeProducts)
                });
            }
        });
        console.log("Fixed Meal Product Map ::::::::::::::::::::", service.fixedMealProductMap);
    }

    function prepareTaxMap() {
        service.taxMap = {};
        service.unitData.taxes.forEach(function (tax) {
            service.taxMap[tax.taxCode] = tax;
        })
    }

    function prepareCatalogs(selectedUnit, unitData, metadata, menuSequence, brand, menuRecommendationData, productImages,productPackagingMappings
    ,productCityImageDetails) {
        try {
            service.errorList = [];
            var unitId = selectedUnit.id;
            var addPackaging = selectedUnit.addPackaging;
            var splitAllDesiChaiDimensions = selectedUnit.splitAllDesiChaiDimensions;
            var miniKetliDefault = selectedUnit.miniKetliDefault;
            if (unitId == null || unitData == null || metadata == null || brand == null) {
                service.errorList.push("unit details should not be null");
                return;
            }
            if (addPackaging == null) {
                addPackaging = true;
            }
            if (splitAllDesiChaiDimensions == null || selectedUnit.clubAllDesiChaiDimensions == true) {
                splitAllDesiChaiDimensions = false;
            }
            cataloguesIds = [];
            modifierGroupIds = [];
            service.unitId = unitId;
            service.unitData = unitData;
            service.menuSequence = menuSequence;
            service.brand = brand;
            service.metadata = metadata;
            service.addPackaging = addPackaging;
            service.menuRecommendationData = menuRecommendationData;
            service.productImages = productImages;
            service.splitAllDesiChaiDimensions = splitAllDesiChaiDimensions;
            service.clubAllDesiChaiDimensions = selectedUnit.clubAllDesiChaiDimensions;
            service.productPackagingMappings = productPackagingMappings;
            service.productCityImageDetails = productCityImageDetails;
            service.selectedDimension = selectedUnit.selectedDimension;
            if(service.selectedDimension!=null){
                service.splitDesiChaiDimensionName = service.selectedDimension;
            }else{
                service.splitDesiChaiDimensionName = "ChotiKetli";
            }
            service.brand = brand;
            service.zomatoTaxMap = {};
            setCityLevelImages(selectedUnit,productCityImageDetails);
            service.updateDynamicPricing();
            service.prepareAddonsMap();
            service.preparePaidAddonsMap();
            service.prepareFixedMealProductMap();
            service.prepareTaxMap();
            service.sortProductPrices();
            service.createProductMap();
            service.createDineInProductMap();
            service.createProductMeatTypeMap();
            service.createProductAllergenTypeMap();
            service.createProductServingInfoTypeMap();
            service.createProductServingSizeTypeMap();
            service.createProductNutritionMap();
            service.catalog = angular.copy(zomatoMenu);
            service.catalog.outletId = (unitId).toString();
            service.setMenuCategories();
            service.setMenuCatalogues();
            service.setSuperCombos();
            var catHighestIndex = 0;
            var otherCat = null;
            service.menuSequence.productGroupSequences.forEach(function (cat) {
                if (catHighestIndex < cat.groupIndex) {
                    catHighestIndex = cat.groupIndex;
                }
            });
            Object.keys(service.productMap).forEach(function (key) {
                var prod = service.productMap[key];
                if (prod.selected !== true) {
                    if (prod.classification === "MENU" && prod.billType !== "ZERO_TAX" && prod.type !== 12 &&
                        [11, 12, 50, 1292, 1293, 1294].indexOf(prod.id) < 0) {
                    }
                }
            });
            if (otherCat != null && otherCat.items != null && otherCat.items.length > 0) {
                service.catalog.menu.categories.push(otherCat);
            }

            service.catalog.charges = [];
            service.catalog.charges = service.addCatalogCharges(service.catalog.charges);
            service.catalog.menu.categories.sort(customFunction);
            service.checkGstTags();
            if (!showErrorList()) {
                return service.catalog;
            } else {
                console.log(service.catalog);
                return zomatoMenu;
            }
        } catch (e) {
            console.log(e);
            if (!showErrorList()) {
                bootbox.alert(e);
            }
            console.log(service.catalog);
            return zomatoMenu;
        }
    }

     function setCityLevelImages(selectedUnit,productCityImageDetails){
            var cityName = selectedUnit.city;
            Object.keys(service.productImages).forEach(function(productId){
               if(productCityImageDetails[productId] != null && productCityImageDetails[productId][cityName] != null){
                   service.productImages[productId].gridLow.url = productCityImageDetails[productId][cityName].imageUrl;
               }
            });
        }

    function showErrorList() {
        if (service.errorList.length > 0) {
            var errors = "<ul>";
            service.errorList.forEach(function (err) {
                errors += "<li>" + err + "</li>"
            });
            errors += "</ul>";
            bootbox.alert(errors);
            return true;
        } else {
            return false;
        }
    }

    function customFunction(a, b) {
        return a.order - b.order;
    }

    function createProductMap() {
        var productMap = {};
        service.unitData.products.forEach(function (product) {
            productMap[product.id] = product;
        });
        service.productMap = productMap;
    }

    function createDineInProductMap() {
        var productMap = {};
        service.metadata.dineInMenuProfile.products.forEach(function (product) {
            productMap[product.id] = product;
        });
        service.dineInProductMap = productMap;
    }

    function createProductNutritionMap(){
        var productNutritionMap = {};
        service.metadata.productNutrition.forEach(function (nutritionData) {
            productNutritionMap[nutritionData.productId] = {
                "calorieCount": nutritionData.calorieCount,
                "proteinCount": nutritionData.proteinCount,
                "fatCount" : nutritionData.fatCount,
                "carbohydrateCount": nutritionData.carbohydrateCount,
                "fiberCount": nutritionData.fiberCount
            }
        });
        service.productNutritionMap = productNutritionMap;
    }

    function createProductMeatTypeMap() {
        var productMeatTypeMap = {};
        if (service.metadata.productMeatTags != null && service.metadata.productMeatTags.length > 0) {
            service.metadata.productMeatTags[0].mappings.forEach(function (product) {
                productMeatTypeMap[product.productId] = product.tags;
            });
        }
        service.productMeatTypeMap = productMeatTypeMap;
    }

    function createProductAllergenTypeMap() {
        var productAllergenTypeMap = {};
        if (service.metadata.allergenInfoSizeTags.partnerProductsAllergenTags != null && service.metadata.allergenInfoSizeTags.partnerProductsAllergenTags.length > 0) {
            service.metadata.allergenInfoSizeTags.partnerProductsAllergenTags[0].mappings.forEach(function (product) {
                productAllergenTypeMap[product.productId] = product.tags;
            });
        }
        service.productAllergenTypeMap = productAllergenTypeMap;
    }

    function createProductServingInfoTypeMap() {
        var productServingInfoMap = {};
        if (service.metadata.allergenInfoSizeTags.partnerProductsServingInfoTags != null && service.metadata.allergenInfoSizeTags.partnerProductsServingInfoTags.length > 0) {
            service.metadata.allergenInfoSizeTags.partnerProductsServingInfoTags[0].mappings.forEach(function (product) {
                productServingInfoMap[product.productId] = product.tags;
            });
        }
        service.productServingInfoMap = productServingInfoMap;
    }

    function createProductServingSizeTypeMap() {
        var productServingSizeMap = {};
        if (service.metadata.allergenInfoSizeTags.partnerProductsServingSizeTags != null && service.metadata.allergenInfoSizeTags.partnerProductsServingSizeTags.length > 0) {
            service.metadata.allergenInfoSizeTags.partnerProductsServingSizeTags[0].mappingsVU.forEach(function (product) {
                productServingSizeMap[product.productId] = product.tags;
            });
        }
        service.productServingSizeMap = productServingSizeMap;
    }


    function sortProductPrices() {
        service.unitData.products.forEach(function (product) {
            product.prices.sort(function (a, b) {
                return a.price - b.price;
            });
        })
    }

    function updateDynamicPricing() {
        if (service.metadata.dynamicPriceProfile != null) {
            var profile = service.metadata.dynamicPriceProfile;
            var strategy = profile.profileType;
            switch (strategy) {
                case "FLAT_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var threshold = (profile.thresholdPercentage / 100) * price.price;
                                        if(rangeVal.deltaPrice<0){
                                            threshold = -threshold;
                                        }
                                        if(Math.abs(threshold) < Math.abs(rangeVal.deltaPrice)) {
                                            price.price += threshold;
                                        } else {
                                            price.price += rangeVal.deltaPrice;
                                        }
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "RANGE_FLAT_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var threshold = (profile.thresholdPercentage / 100) * price.price;
                                        if(rangeVal.deltaPrice<0){
                                            threshold = -threshold;
                                        }
                                        if (Math.abs(threshold) < Math.abs(rangeVal.deltaPrice)) {
                                            price.price += threshold;
                                        } else {
                                            price.price += rangeVal.deltaPrice;
                                        }
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "PERCENTAGE_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var deltaValue = (rangeVal.deltaPrice / 100) * price.price;
                                        price.price += deltaValue;
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "RANGE_PERCENTAGE_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var deltaValue = (rangeVal.deltaPrice / 100) * price.price;
                                            price.price += deltaValue;
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "SLASH_PRICING":
                    break;
                case "RANGE_SLASH_PRICING":
                    break;
            }

        }
    }

    function setMenuCategories() {
        service.menuSequence.productGroupSequences.sort(customFunction);
        service.menuSequence.productGroupSequences.forEach(function (cat) {
        cat.timingsApplicable = service.menuSequence.timingsApplicable;
            var category = service.addNewCategory(cat);
            if (category != null) {
                service.catalog.menu.categories.push(category);
            }
        });
        service.catalog.menu.categories.sort(customFunction);
    }

    function addNewCategory(cat) {
        var duplicateCatal = [];
        var category = angular.copy(zomatoCategory);
        category.vendorEntityId = (cat.groupId).toString();
        category.name = cat.groupName;
        category.order = cat.groupIndex;
        if(cat.timings !== null && cat.timings !== undefined && cat.timingsApplicable ) {
            cat.timings.startDate = "";
            cat.timings.endDate = "";
            category.timings = cat.timings;
        }
        cat.subGroups.forEach(function (subCat) {
            var subCategory = service.addNewSubCategory(subCat, duplicateCatal);
            if (subCategory != null) {
                category.subCategories.push(subCategory);
            }
        });
        category.subCategories.sort(customFunction);
        if (category.subCategories.length !== 0) {
            return category;
        } else {
            return null;
        }
    }

    function addSpecialMilkTea(productObj , variantName , productName){
        var specialMilkProductObj = angular.copy(productObj);
        specialMilkProductObj.id = productObj.id + "_" + variantName;
        specialMilkProductObj.name = productName;

        var productEntry = addProduct(specialMilkProductObj);
        return productEntry;
    }

    function containsOatMilk(product){
        return  product.prices.some(function(price){
            return dimensionHaveOatMilk(price);
         });
    }

    function dimensionHaveOatMilk(price){
        return price.recipe.options != null && price.recipe.options.filter(function(option){
           return service.specialMilkPaidAddonList.indexOf(option.id) > -1 && productHavePrice(option.id);
        }).length > 0;

    }

    function dimensionHaveCoffeeFlavour(price){
            return price.recipe.options != null && price.recipe.options.filter(function(option){
               return service.coffeeFlavours.indexOf(option.id) > -1 && productHavePrice(option.id);
            }).length > 0;

        }

    function productHavePrice(productId){
        return service.productMap[productId] != null &&
        service.productMap[productId].prices != null &&
        service.productMap[productId].prices.length > 0 &&
        service.dineInProductMap[productId] != null &&
        service.dineInProductMap[productId].prices != null &&
        service.dineInProductMap[productId].prices.length > 0;

    }



    function addNewSubCategory(subCat, duplicateCatal) {
        var subCategory = angular.copy(zomatoSubcategory);
        subCategory.vendorEntityId = (subCat.groupId).toString();
        subCategory.name = subCat.groupName;
        subCategory.order = subCat.groupIndex;
        subCat.productSequenceList.forEach(function (prodSequence) {
            var productObj = service.productMap[prodSequence.product.id];
            if (productObj != null) {
                productObj.selected = true;
                productObj.productIndex = prodSequence.productIndex;
                service.productMap[prodSequence.product.id] = productObj;
                if (!duplicateCatal.includes(productObj.id)) {
                    if (AppUtil.splitDimensionProductIds.indexOf(productObj.id) >= 0 && !service.clubAllDesiChaiDimensions) {
                        splitDesiChaiProductEntry(productObj, subCategory, duplicateCatal);
                    } else {
                        var productEntry = addProduct(productObj);
                        subCategory.entities.push(productEntry);
                        duplicateCatal.push(productObj.id);
                        if (!cataloguesIds.includes(productObj.id)) {
                            cataloguesIds.push(productObj.id);
                        }

                        var customChaiEntries = [];
                        customChaiEntries.push(productEntry);
                        if (!cataloguesIds.includes(productObj.id)) {
                            cataloguesIds.push(productObj.id);
                        }
                        customChaiEntries.forEach(function (productEntry) {
                            addDesiChaiCustomizationProduct(productEntry, subCategory.entities);
                        })
                    }

                    /*if(productObj.id == 10 && containsOatMilk(productObj)){
                        var oatMilkTeaEntry = addSpecialMilkTea(productObj,"OATS","Oat Milk Chai");
                        subCategory.entities.push(oatMilkTeaEntry);
                  }*/
                }
            }
        });
        subCategory.entities.sort(customFunction);
        if (subCategory.entities.length !== 0) {
            return subCategory;
        } else {
            return null;
        }
    }

    function splitDesiChaiProductEntry(product, subCategory, duplicateCatal) {
        var customChaiEntries = [];
        if (service.splitAllDesiChaiDimensions) {
            product.prices.forEach(function (price) {
                var prod = angular.copy(product);
                prod.id = prod.id + "_" + price.dimension;
                var productEntry = addProduct(prod);
                subCategory.entities.push(productEntry);
                customChaiEntries.push(productEntry);
                duplicateCatal.push(product.id);
                if (!cataloguesIds.includes(product.id)) {
                    cataloguesIds.push(product.id);
                }
            });
        } else {
            // var noSplitDimensionPrice = product.prices.filter(function (price) {
            //     return price.dimension !== service.splitDesiChaiDimensionName;
            // });
            var noSplitDimensionPrice = product.prices;
            var splitDimensionPrice = product.prices.filter(function (price) {
                return price.dimension === service.splitDesiChaiDimensionName;
            });
            if (splitDimensionPrice.length > 0) {
                if(product.id == 10){
                    var prod = angular.copy(product);
                    prod.id = prod.id + "_" + splitDimensionPrice[0].dimension + "_" + "S";
                    var productEntry = addProduct(prod);
                    subCategory.entities.push(productEntry);
    //                customChaiEntries.push(productEntry);
                    duplicateCatal.push(product.id);
                    if (!cataloguesIds.includes(product.id)) {
                        cataloguesIds.push(product.id);
                    }
                }

            }
            if (noSplitDimensionPrice.length > 0) {
                var productEntry = addProduct(product);
                subCategory.entities.push(productEntry);
                customChaiEntries.push(productEntry);
                duplicateCatal.push(product.id);
                if (!cataloguesIds.includes(product.id)) {
                    cataloguesIds.push(product.id);
                }
            }
        }
        customChaiEntries.forEach(function (productEntry) {
            addDesiChaiCustomizationProduct(productEntry, subCategory.entities);
        })
    }

    function addDesiChaiCustomizationProduct(product, entities) {
        if (product.vendorEntityId === "10" || product.vendorEntityId.includes("10_")) {
            service.metadata.desiChaiCustomProfiles.forEach(function (profile) {
                if (profile.profileType === "PRODUCT") {
                    var newProduct = angular.copy(product);
                    var profileName = profile.profileName.replaceAll(" ", "");
                    newProduct.vendorEntityId = newProduct.vendorEntityId + "_" + profileName;
                    entities.push(newProduct);
                }
            });
        }
    }

    function addProduct(product) {
        var item = angular.copy(zomatoEntities);
        item.vendorEntityId = product.id + "";
        if(product.subType == 3676){
            item.vendorEntityId = item.vendorEntityId + "_SUPERCOMBO";
        }
        if(product.subType == 3675){
            item.vendorEntityId = item.vendorEntityId + "_HEROCOMBO";
        }
        item.entityType = "catalogue";
        item.order = (product != null ? product.productIndex : 1);
        return item;
    }


    function addCatalogCharges(charges) {
        //service.taxCodeMap = {};
        service.unitData.products.forEach(function (prod) {
            var chargeIds = [];
            if (service.addPackaging === true) {
                chargeIds.push(1043);
            }
            if(service.productPackagingMappings != null && service.productPackagingMappings[prod.id] != null && service.metadata.packagingType != "FIXED"){
               var productPackagingMapping  = service.productPackagingMappings[prod.id];
               var taxCode = prod.taxCode;
               if(prod.taxCode == "COMBO"){
                 taxCode = service.productMap[prod.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
               }
               var charge = angular.copy(zomatoCharges);
               charge.slug = (productPackagingMapping.packagingType === "FIXED") ? "PC_D_F" : "PC_D_P";
               charge.vendorEntityId = 1043 + "_" + prod.id + "_" + taxCode;//charge.slug;
               charge.multiItem = false;
               charge.chargeValue = productPackagingMapping.packagingValue;
               charge.applicableOnItem = true;
               var tax = service.taxMap[taxCode];
               charge.taxGroups = [{slug: getTaxGroupId(tax)}];
               if(productPackagingMapping.packagingValue !=0){
               charges.push(charge);
               }

            }

            if (chargeIds.indexOf(prod.id) >= 0) {
                service.unitData.taxes.forEach(function (tax) {
                    if (["COMBO", "ZERO_TAX", "GIFT_CARD"].indexOf(tax.taxCode) < 0) {
                        var charge = angular.copy(zomatoCharges);
                        charge.slug = (service.metadata.packagingType === "FIXED") ? "PC_D_F" : "PC_D_P";
                        charge.vendorEntityId = prod.id + "_" + tax.taxCode;//charge.slug;
                        charge.multiItem = false;
                        charge.chargeValue = service.metadata.packagingValue;
                        charge.applicableOnItem = service.metadata.packagingType !== "FIXED";
                        charge.taxGroups = [{slug: getTaxGroupId(tax)}];
                       // service.taxCodeMap[tax.taxCode] =tax;
                        charges.push(charge);
                    }
                });
            }
        });
        if (service.metadata.packagingType === "FIXED") {
        var x = [];
        var fixedCharge = charges[0];
        fixedCharge.taxGroups = [];
        fixedCharge.taxGroups.push({slug:'GST_D_P_5.00'})
        x.push(fixedCharge);
        charges = x;
        }
        return charges;
    }

    function addSpecialMilkTeaInCatalogue(product,catalogueMap){
        var prod = angular.copy(product);
        var oatMilkIdentifier = "OATS";
        prod.id = prod.id + "_" + oatMilkIdentifier;
        prod.prices = prod.prices.filter(function(price){
            return dimensionHaveOatMilk(price);
        });
        catalogEntry = service.addNewCatalogue(prod);
        catalogEntry.name = "Oats Milk Chai";
        catalogEntry.haveSpecialMilkPaidAddon =  true;
        catalogEntry.properties.forEach(function (prop) {
            prop.vendorEntityId = prop.vendorEntityId + "_" + oatMilkIdentifier;
            prop.propertyValues.forEach(function (propVal) {
                propVal.vendorEntityId = propVal.vendorEntityId + "_" + oatMilkIdentifier;
            });
        });
        var addonIds = [];
        catalogEntry.variants.forEach(function (variant) {
            variant.vendorEntityId = variant.vendorEntityId + "_" + oatMilkIdentifier;
            variant.propertyValues.forEach(function (propVal) {
                propVal.vendorEntityId = propVal.vendorEntityId + "_" + oatMilkIdentifier;
            });
            if (addonIds.length > 0) {
                variant.modifierGroups.forEach(function (modGroup) {
                    if (modGroup.vendorEntityId.toLowerCase().includes("addon") && !modGroup.vendorEntityId.toLowerCase().includes("paid_addon")) {
                        modGroup.vendorEntityId = modGroup.vendorEntityId + "_" + oatMilkIdentifier;
                    }
                });
            }
        });
        //service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap, price.dimension.replace("Ketli", " Ketli"));
        service.catalog.menu.catalogues.push(catalogEntry);
        catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;

    }

    function setMenuCatalogues() {
        var catalogueMap = {};
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id)) {
                if (product.taxCode !== "COMBO" && product.classification === "MENU") {
                    if (AppUtil.splitDimensionProductIds.indexOf(product.id) >= 0 && !service.clubAllDesiChaiDimensions) {
                        service.splitDesiChaiDimensions(catalogueMap, product)
                    } else {
                        var catalogEntry = service.addNewCatalogue(product);
                        service.catalog.menu.catalogues.push(catalogEntry);
                        catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;

                        service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap);
                    }

                    /*if(product.id == 10 && containsOatMilk(product)){
                        addSpecialMilkTeaInCatalogue(product,catalogueMap);
                    }*/
                }else if (product.subType == 3891){
                    var catalogEntry = service.addNewCatalogue(product);
                    service.catalog.menu.catalogues.push(catalogEntry);
                    catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;
                }
            }
        });
        //for normal combo items
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id)) {
                if (product.taxCode === "COMBO" && product.subType !== 3676 && product.subType !== 3675 && product.subType != 3891 ) {
                    service.catalog.menu.catalogues.push(service.addNewCombo(product, catalogueMap));
                }
            }
        });
        //for hero combo items
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id)) {
                if (product.taxCode === "COMBO" && product.subType === 3675) {
                    $rootScope.heroComboIds.push(product.id);
                    if($rootScope.heroComboVersion == 'V1'){
                       service.catalog.menu.catalogues.push(service.addHeroComboV1(product, catalogueMap));
                    }
                    else if($rootScope.heroComboVersion == 'V2'){
                       service.catalog.menu.catalogues.push(service.addHeroComboV2(product, catalogueMap));
                    }
                }
            }
        });
    }

    function splitDesiChaiDimensions(catalogueMap, product) {
        var catalogEntry;
        if (service.splitAllDesiChaiDimensions) {
            product.prices.forEach(function (price) {
                var prod = angular.copy(product);
                prod.id = prod.id + "_" + price.dimension;
                prod.prices = [price];
                catalogEntry = service.addNewCatalogue(prod);
                service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap, price.dimension.replace("Ketli", " Ketli"));
                service.catalog.menu.catalogues.push(catalogEntry);
                catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;
            });
        } else {
            var noSplitDimensionPrice = [];
            var splitDimensionPrice = [];
            product.prices.forEach(function (price) {
                if (price.dimension === service.splitDesiChaiDimensionName) {
                    splitDimensionPrice.push(angular.copy(price));
                }
                noSplitDimensionPrice.push(angular.copy(price));

            });
            if (splitDimensionPrice.length > 0) {
                if(product.id ==10){
                    var prodBk = angular.copy(product);
                    prodBk.id = prodBk.id + "_" + service.splitDesiChaiDimensionName + "_" + "S";
                    prodBk.prices = splitDimensionPrice;
                    catalogEntry = service.addNewCatalogue(prodBk,null,false);
                    if(splitDimensionPrice[0].dimension == "Regular"){
                         catalogEntry.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/DESI_CHAI_REGULAR.jpg";
                    }
                    // service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap,splitDimensionPrice[0].dimension);
                    service.catalog.menu.catalogues.push(catalogEntry);
                    catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;
                }
            }
            if (noSplitDimensionPrice.length > 0) {
                var prod = angular.copy(product);
                prod.prices = noSplitDimensionPrice;
                catalogEntry = service.addNewCatalogue(prod);
                service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap);
                service.catalog.menu.catalogues.push(catalogEntry);
                catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;
            }
        }
    }

    function addPaidAddonsToSugarVariant(catalogue,product){
       /* if([1282,1000066,1648].indexOf(product.id) >=0){
             return;
        }*/
        catalogue.properties.forEach(function (property){
            if(property.name == "Sugar") {
                product.prices.forEach(function (price, index) {
                    if (price.recipe.options != null) {
                        price.recipe.options.forEach(function (paidAddOn, index) {
                            if (paidAddOn.type === "PRODUCT" && service.productMap[paidAddOn.productId] != null &&
                                service.productMap[paidAddOn.productId].prices != null &&
                                service.productMap[paidAddOn.productId].prices.length > 0 &&
                                service.dineInProductMap[paidAddOn.productId] != null &&
                                service.dineInProductMap[paidAddOn.productId].prices != null &&
                                service.dineInProductMap[paidAddOn.productId].prices.length > 0 &&
                                service.productMap[paidAddOn.productId].classification === "PAID_ADDON") {
                                if (service.variantPaidAddOnsList.includes(paidAddOn.productId)) {
                                    var propertyValues = angular.copy(zomatoPropertyValues);
                                    propertyValues.value = paidAddOn.name;
                                    propertyValues.vendorEntityId = paidAddOn.productId + "_" + product.id;
                                    property.propertyValues.push(propertyValues);
                                }
                            }
                        });
                    }
                });
            }
            });
    }

     function getFilteredPrices(prices) {
            var filteredPrices = prices.filter(function(price){
                    return (prices.length ==1 || price.dimension != "1 Pack");
                 });
            return filteredPrices;

        }

    //please note that this function is called from multiple places and
    // creates catalog for items where classification != "MENU" as well
    // second param price is specifically added to take care of paid addons to get dimension
    function addNewCatalogue(product, price , addDimensionInName,dimensionPrefix) {
        console.log(product.name);
        var catalogue = angular.copy(zomatoCatalogues);
        catalogue.vendorEntityId = product.id + "";
        catalogue.description = getProductDescription(product.description, product.name);
        var productAlias = getProductAlias(product);
        var originalProductName = getProductName(product.name);
        var name = productAlias != null ? productAlias : getProductName(product.name);
        if(service.variantPaidAddOnsList.includes(parseInt(catalogue.vendorEntityId))){
        if(originalProductName.includes("gur") || originalProductName.includes("GUR") || originalProductName.includes("Gur")){
            name = "Gur";
            if(productAlias != null && productAlias != "Gur" && productAlias != "GUR"){
                     name = name + "(" + productAlias + ")";
            }
        }

        }
        catalogue.name = name;
        var filteredPrices = getFilteredPrices(product.prices);
        if (product.id.toString().indexOf("_") >= 0 && (dimensionPrefix == null || dimensionPrefix == true)) {
            if(addDimensionInName != null  && addDimensionInName == false && product.prices[0].dimensionDescriptor){
               catalogue.name = name + " " +   getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
            }else{
             catalogue.name = name + getDimensionName(product.prices[0].dimension,product.subType) +
                                           getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
            }

        }else if (product.brandId == 6 && filteredPrices.length == 1 && filteredPrices[0].dimensionDescriptor){
                     catalogue.name = name  + getDimensionDescription(filteredPrices[0].dimension, filteredPrices[0].dimensionDescriptor);
        }
        catalogue.inStock = true;
        catalogue.tags = [];
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        if(product.classification != "MENU" && product.classification != "PAID_ADDON" && product.classification != "FREE_ADDON"){
            if (service.productImages[productId] == null || service.productImages[productId].gridLow == null
                || service.productImages[productId].gridLow.url == null) {
                console.log("Product image url missing for product id :: {}",productId);
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[productId].gridLow.url;
            }
        }
        if (product.classification === "MENU" || product.classification === "PAID_ADDON") {
            if (service.productImages[productId] == null || service.productImages[productId].gridLow == null
                || service.productImages[productId].gridLow.url == null) {
                if (AppUtil.getEnvType() === "PROD" && product.classification === "MENU") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[productId].gridLow.url;
            }
            if (product.billType !== "ZERO_TAX" && product.type !== 12 &&
                [11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0 && product.taxCode !== "GIFT_CARD") {
                catalogue.properties = addNewProperties(product);
                addPaidAddonsToSugarVariant(catalogue,product);
                var taxCode;
                if(product.taxCode == "COMBO"){
                  taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
                }else{
                  taxCode = product.taxCode;
                }
                catalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[taxCode])}];

                var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
                var tag = str.toString().replace("_", "-");
                catalogue.tags.push(tag);
                catalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
                if (service.addPackaging === true && service.metadata.packagingType !== "FIXED" && product.billType != "MRP"
                    && service.metadata.filterProductsForNoPackagingCharges != undefined && service.metadata.filterProductsForNoPackagingCharges != null
                    && !service.metadata.filterProductsForNoPackagingCharges.includes(product.id)) {
                    catalogue.charges.push(addCharge(taxCode,product.id));
                }
                 if(service.productPackagingMappings != null && service.productPackagingMappings[productId] != null
                 && service.productPackagingMappings[productId].packagingValue ==0){
                     catalogue.charges = [];
                 }
                if(product.attribute === 'NON_VEG' && service.productMeatTypeMap[product.id] != null) {
                    catalogue.meatTypes = [];
                    service.productMeatTypeMap[product.id].forEach(function (meatType) {
                        catalogue.meatTypes.push(meatType.name);
                    })
                }
            }
            if(service.productNutritionMap[product.id] !=null ){
                var nutritionDetail = service.productNutritionMap[product.id];
                var nutritionInfo = {
                    "calorieCount" : nutritionDetail.calorieCount
                };
                var healthyInfo = {
                    "proteinCount" : {
                        "value" : nutritionDetail.proteinCount,
                        "unit" : "grams"
                    },
                    "fiberCount" : {
                        "value" : nutritionDetail.fiberCount,
                        "unit" : "grams"
                    },
                    "fatCount" : {
                        "value" : nutritionDetail.fatCount,
                        "unit" : "grams"
                    },
                    "carbohydrateCount" : {
                        "value" : nutritionDetail.carbohydrateCount,
                        "unit" : "grams"
                    }
                }
                catalogue.nutritionInfo = nutritionInfo;
                catalogue.healthyInfo = healthyInfo;
            }
            if(service.productAllergenTypeMap[product.id]!=null){
                catalogue.allergenTypes=[];
                service.productAllergenTypeMap[product.id].forEach(function (allergenType) {
                    catalogue.allergenTypes.push(allergenType.name);
                })
            }
            if(service.productServingInfoMap[product.id]!=null){
                catalogue.servingInfo="";
                service.productServingInfoMap[product.id].forEach(function (servingInfo) {
                    catalogue.servingInfo=servingInfo.name;
                })
            }
            if(service.productServingSizeMap[product.id]!=null){
                catalogue.servingSize={};
                catalogue.servingSize={"value": service.productServingSizeMap[product.id].value, "unit": service.productServingSizeMap[product.id].unit};
            }
        }
        if (product.classification === "PAID_ADDON") {
            catalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[product.taxCode])}];
        }
        //if ([11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0) {
        if (product.classification === "FREE_ADDON") {
            catalogue.variants = addAddonVariants(product);
        } else if (product.classification === "PAID_ADDON") {
            catalogue.variants = addPaidAddonVariants(product, price);
        } else {
            catalogue.variants = addVariants(product,catalogue);
        }
        //}
        if(catalogue.tags.length === 0) {
            var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
            var tag = str.toString().replace("_", "-");
            catalogue.tags.push(tag);
        }
        catalogue.preparationTime = getPrepTime(product.prepTime);
        return catalogue;
    }

    function addNewCombo(product, catalogueMap) {
        if (product.prices == null || product.prices.length === 0) {
            alert("Combo " + product.name + " price is missing.");
        } else if (product.prices[0].recipe == null) {
            alert("Combo " + product.name + " recipe is missing.");
        } else {
            var catalogue = angular.copy(zomatoCatalogues);
            catalogue.vendorEntityId = product.id + "";
            catalogue.name = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name));//getProductName(product.name);
            catalogue.description = getProductDescription(product.description, product.name);
            catalogue.inStock = true;
            if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null
                || service.productImages[product.id].gridLow.url == null) {
                if (AppUtil.getEnvType() == "PROD") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
            }
            catalogue.properties = [];
            catalogue.variants = [];
            var found = false;
            //Below code does the following:
            //Finds any hot beverage if available in the combo sub items and for that item, try to find the
            //zomato catalog entry because catalog for all normal items are already prepared. From sub items catalog entry
            //pick the properties and variants matching the dimension of sub item
            product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail) {
                compositeProductDetail.menuProducts.forEach(function (menuProductDetail) {
                    if (service.productMap[menuProductDetail.product.productId] == null) {
                        service.errorList.push("combo: " + product.name + " item: " + menuProductDetail.product.name + " not found in catalog.");
                    }
                    if (service.productMap[menuProductDetail.product.productId] === undefined) {
                        console.log('Cannot find entry for product id ' + menuProductDetail.product.productId);
                    }
                    if (service.productMap[menuProductDetail.product.productId].type === 5 && !found) { //if item is hot beverage
                        found = true;
                        var sizePropVendorEntityId = null;
                        var exactMatch = true;
                        var catalogueMapId = service.getCatalogueMapId(product, menuProductDetail, catalogueMap);
                        catalogueMap[catalogueMapId].properties.forEach(function (prop) {
                            var property = angular.copy(prop);
                            if (prop.name === "Size") {
                                property.propertyValues = [];
                                var found = false;
                                prop.propertyValues.forEach(function (propValue) {
                                    if (propValue.vendorEntityId.indexOf(menuProductDetail.dimension.code) >= 0) {
                                        var val = angular.copy(propValue);
                                        property.propertyValues.push(val);
                                        sizePropVendorEntityId = val.vendorEntityId;
                                        found = true;
                                    }
                                });
                                if (!found) {
                                    service.errorList.push("Combo: " + product.name + " item: " + menuProductDetail.product.name +
                                        " dimension: " + menuProductDetail.dimension.name + " not found in catalog.")
                                }
                                property.vendorEntityId = property.vendorEntityId + "_" + product.id;
                            } else {
                                property.vendorEntityId = property.vendorEntityId + "_" + product.id;
                            }
                            property.propertyValues.forEach(function (propVal) {
                                propVal.vendorEntityId = propVal.vendorEntityId + "_" + product.id;
                            });
                            catalogue.properties.push(property);
                        });
                        if (sizePropVendorEntityId == null) {
                            sizePropVendorEntityId = menuProductDetail.dimension.code;
                            exactMatch = false;
                        }

                        catalogueMap[catalogueMapId].variants.forEach(function (variant) {
                            var propMatched = false;
                            variant.propertyValues.forEach(function (propValue) {
                                if (!propMatched && exactMatch && propValue.vendorEntityId === sizePropVendorEntityId) {
                                    propMatched = true;
                                }
                                if (!propMatched && !exactMatch && propValue.vendorEntityId.indexOf(sizePropVendorEntityId) >= 0) {
                                    propMatched = true;
                                }
                            });
                            if (!propMatched && !exactMatch && variant.vendorEntityId.indexOf(sizePropVendorEntityId) >= 0) {
                                propMatched = true;
                            }
                            if (propMatched) {
                                var variant1 = angular.copy(variant);
                                var paidAddonPrice = 0;
                                var foundPaidAddonId = isPaidAddonInVariantCombination(variant1);
                                if(foundPaidAddonId!=null){
                                    paidAddonPrice = service.productMap[foundPaidAddonId].prices[0].price
                                }
                                variant1.prices.forEach(function (price) {
                                    price.price = product.prices[0].price + paidAddonPrice;
                                });
                                variant1.vendorEntityId = variant1.vendorEntityId + "_" + product.id;
                                var propValList = [];
                                variant1.propertyValues.forEach(function (propVal) {
                                    var val = angular.copy(propVal);
                                    val.vendorEntityId = val.vendorEntityId + "_" + product.id;
                                    propValList.push(val);
                                });
                                variant1.propertyValues = propValList;
                                dropUpsellingFromVariants(variant1);
                                catalogue.variants.push(variant1);
                            }
                        });
                    }
                });
            });
            if (catalogue.variants.length === 0) {
                catalogue.variants.push({
                    vendorEntityId: product.id + "_" + product.prices[0].dimension,
                    propertyValues: [],
                    prices: [{service: "delivery", price: product.prices[0].price}],
                    modifierGroups: []
                })
            }
            if (service.menuRecommendationData != null && service.menuRecommendationData[product.id] != null) {
                        catalogue.variants[0].modifierGroups.push.apply( catalogue.variants[0].modifierGroups, addModifierGroupForRecommendations(product));
            }
            var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
            catalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[taxCode])}];
            var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
            var tag = str.toString().replace("_", "-");
            catalogue.tags = [tag];
            catalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
            if (service.addPackaging === true && service.metadata.packagingType !== "FIXED" && product.billType != "MRP"
                && service.metadata.filterProductsForNoPackagingCharges != undefined && service.metadata.filterProductsForNoPackagingCharges != null
                && !service.metadata.filterProductsForNoPackagingCharges.includes(product.id)) {
                catalogue.charges.push(addCharge(taxCode,product.id));
            }
            if(service.productPackagingMappings != null && service.productPackagingMappings[product.id] != null
                             && service.productPackagingMappings[product.id].packagingValue ==0){
                                 catalogue.charges = [];
             }
            return catalogue;
        }
    }


    function getCatalogueMapId(product, menuProductDetail, catalogueMap) {
        var catalogueMapId = menuProductDetail.product.productId;
        if (AppUtil.splitDimensionProductIds.indexOf(menuProductDetail.product.productId) >= 0) {
            if (service.splitAllDesiChaiDimensions) {
                catalogueMapId = menuProductDetail.product.productId + "_" + menuProductDetail.dimension.code;
            }
            else{
                catalogueMapId = menuProductDetail.product.productId;
            }
        }
        if (catalogueMap[catalogueMapId] == null) {
            service.errorList.push("combo: " + product.name + " item: " + menuProductDetail.product.name +
                menuProductDetail.dimension.code + " not found in catalog.");
        }
        return catalogueMapId;
    }

    function addCharge(taxCode,productId) {
        if(service.productPackagingMappings != null && service.productPackagingMappings[productId] != null){
           var productPackagingMapping = service.productPackagingMappings[productId];
           /*var charge = angular.copy(zomatoCharges);
           charge.slug = (productPackagingMapping.packagingType === "FIXED") ? "PC_D_F" : "PC_D_P";
           charge.vendorEntityId = 1043 + "_" + productId + "_" + taxCode;//charge.slug;
           charge.multiItem = false;
           charge.chargeValue = productPackagingMapping.packagingValue;
           charge.applicableOnItem = productPackagingMapping.packagingType !== "FIXED";
           var tax = service.taxCodeMap[taxCode];
           charge.taxGroups = [{slug: getTaxGroupId(tax)}];
           service.catalog.charges.push(charge);*/

           var catalogCharges = angular.copy(zomatoCharge);
           catalogCharges.vendorEntityId = 1043 + "_" + productId + "_" + taxCode;
           return catalogCharges;

        }else{
           var catalogCharges = angular.copy(zomatoCharge);
           catalogCharges.vendorEntityId = 1043 + "_" + taxCode;
           return catalogCharges;
        }

    }

    function addNewProperties(product) {
        var propertyGroups = [];
        if (product.prices == null || product.prices.length === 0) {
            alert("Price not found for " + product.name + ". Please add price first.");
        }
        var dimensionProperties = addDimensionProperties(product);
        if (Array.isArray(dimensionProperties) && dimensionProperties.length) {
            dimensionProperties.forEach(function (dimensionProperty) {
                propertyGroups.push(dimensionProperty);
            });
        }
        if(service.addonMode == false){
            var ingredientProductProperties = addIngredientProducts(product);
            var sugarVariant = null;
            if (Array.isArray(ingredientProductProperties) && ingredientProductProperties.length) {
                ingredientProductProperties.forEach(function (ingredientProductProp) {
                    if(ingredientProductProp.name == "Sugar"){
                        sugarVariant = ingredientProductProp;
                    }else{
                        propertyGroups.push(ingredientProductProp);
                    }
                });
            }
            if(sugarVariant!=null){
                propertyGroups.push(sugarVariant);
            }
        }
        return propertyGroups;
    }


    function addDimensionProperties(product) {
        var groups = [];
        var property = angular.copy(zomatoProperties);
        if (product.prices.length > 1) {
            property.name = "Size";
            property.order = 1;
            property.vendorEntityId = product.id + "_size";
             var filteredPrices = product.prices.filter(function(price){
                                                 return (product.prices.length ==1 || price.dimension != "1 Pack");
             });

            filteredPrices.forEach(function (price) {
                property.propertyValues.push(addDimensionPropertyValues(price, product));
            });
            groups.push(property);
        }
        if(service.addonMode == false){
            //for desi chai and baarish wali chai milk options merging
            var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
            productId = productId + "";
            if (productId === "10" || productId === "1282") {
                var property = angular.copy(zomatoProperties);
                property.name = "Milk Option";
                property.order = 2;
                property.vendorEntityId = product.id + "_milk_option";
                var index;
                for (index = 0; index < milkOption.length; index++) {
                    property.propertyValues.push(addMilkPropertyValues(index, product));
                }
                groups.push(property);
            }
        }
        return groups;
    }

    function addMilkAddons(product,groups){
        //for desi chai and baarish wali chai milk options merging
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        productId = productId + "";
        if (productId === "10" || productId === "1282") {
            var modGroup = angular.copy(zomatoModifierGroups);
            modGroup.name = "Milk Option";
            modGroup.displayName = "Milk Option";
            modGroup.order = 2;
            modGroup.min = 1;
            modGroup.max = 1;
            modGroup.vendorEntityId = product.id + "_milk_option";
            var index;
            for (index = 0; index < milkOption.length; index++) {
                modGroup.variants.push(addMilkModifier(index, product));
            }
            if (!modifierGroupIds.includes(modGroup.vendorEntityId)) {
                groups.push(modGroup);
                modifierGroupIds.push(modGroup.vendorEntityId);
            }
        }
    }

    function addSpecialMilkTypeAddons(product,price,groups,variantModifierGroups){
       /* if(product.id ==10){
            return;
        }*/
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        productId = productId + "";
        var modGroup = angular.copy(zomatoModifierGroups);
        modGroup.name = "Milk Type";
        modGroup.displayName = "Milk Type";
        modGroup.order = 2;
        modGroup.min = 1;
        modGroup.max = 1;
        modGroup.vendorEntityId = product.id + "_milk_type" + "_" + price.dimension;
        var modifierGroupVariants = angular.copy(zomatoModifierGroup);

        modifierGroupVariants.vendorEntityId = modGroup.vendorEntityId;
        variantModifierGroups.push(modifierGroupVariants);

        var milkTypePaidAddons = price.recipe.options.filter(function (option) {
            return service.specialMilkPaidAddonList.indexOf(option.id) > -1;
        });
        var index;
        if(!(product.id + "").includes("_OATS")){
            modGroup.variants.push(addRegularMilkTypeModifier(index,product));
        }
        for (index = 0; index < milkTypePaidAddons.length; index++) {
            modGroup.variants.push(addMilkTypeModifier(index, product, price, milkTypePaidAddons[index]));
        }
        if (!modifierGroupIds.includes(modGroup.vendorEntityId)) {
            groups.push(modGroup);
            modifierGroupIds.push(modGroup.vendorEntityId);
        }

    }

    function addCoffeeFlavourAddons(product,price,groups,variantModifierGroups){
           /* if(product.id ==10){
                return;
            }*/
            var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
            productId = productId + "";
            var modGroup = angular.copy(zomatoModifierGroups);
            modGroup.name = "Flavours";
            modGroup.displayName = "Flavours";
            modGroup.order = 2;
            modGroup.min = 0;
            modGroup.max = 1;
            modGroup.vendorEntityId = product.id + "_flavours" + "_" + price.dimension;
            var modifierGroupVariants = angular.copy(zomatoModifierGroup);

            modifierGroupVariants.vendorEntityId = modGroup.vendorEntityId;
            variantModifierGroups.push(modifierGroupVariants);

            var flavourPaidAddons = price.recipe.options.filter(function (option) {
                return service.coffeeFlavours.indexOf(option.id) > -1;
            });
            var index;

            for (index = 0; index < flavourPaidAddons.length; index++) {
                modGroup.variants.push(addCoffeeFlavourModifier(index, product, price, flavourPaidAddons[index]));
            }
            if (!modifierGroupIds.includes(modGroup.vendorEntityId)) {
                groups.push(modGroup);
                modifierGroupIds.push(modGroup.vendorEntityId);
            }

        }

    function addNewPaidModifierGroups(price, product,isSugarVariant) {
        var recipe = price.recipe;
        var modGroup = angular.copy(zomatoModifierGroups);
        modGroup.vendorEntityId = getPaidModifierGroupId(product, price.dimension);
        modGroup.name = "Top it up with";
        modGroup.displayName = "Top it up with";
        modGroup.min = 0;
        var data = 0;
        if (recipe.options != null) {
            recipe.options.forEach(function (paidAddOn, index) {
                if((service.addonMode == false  || (service.variantPaidAddOnsList.includes(paidAddOn.productId) < 0 || !isSugarVariant))){
                    if (paidAddOn.type === "PRODUCT" && service.productMap[paidAddOn.productId] != null &&
                        service.productMap[paidAddOn.productId].prices != null &&
                        service.productMap[paidAddOn.productId].prices.length > 0 &&
                        service.dineInProductMap[paidAddOn.productId] != null &&
                        service.dineInProductMap[paidAddOn.productId].prices != null &&
                        service.dineInProductMap[paidAddOn.productId].prices.length > 0 &&
                        service.productMap[paidAddOn.productId].classification === "PAID_ADDON") {
                        if((!isPaidAddonInCombination(paidAddOn.id) || !isSugarVariant)){
                            var paidAddon = addPaidAddOns(paidAddOn, index, price);
                            modGroup.variants.push(paidAddon);
                            data++;
                        }
                    }
                }
            });
        }
        if(product.brandId == 6){
                    modGroup.max = 1;
                    modGroup.maxSelectionsPerItem = 1;
        }else{
                 modGroup.max = 10;
                 modGroup.maxSelectionsPerItem = 10;
        }
        return data > 0 ? modGroup : null;
    }

    function addDimensionPropertyValues(price, product) {
        var propertyValues = angular.copy(zomatoPropertyValues);
        if (price.dimension === "MiniKetli" || price.dimension === "ChotiKetli" || price.dimension === "BadiKetli" || price.dimension === "Regular") {
            var name = price.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(price.dimension, price.dimensionDescriptor) + ")".trim();
            propertyValues.value = name;
        } else {
            propertyValues.value = (price.dimensionDescriptor ? price.dimensionDescriptor : price.dimension);
        }
        /*if (price.recipe.dimension.desc != null && price.recipe.dimension.desc.trim().length > 0) {
            propertyValues.value += " (" + price.recipe.dimension.desc + ")";
        }*/
        propertyValues.vendorEntityId = getDimensionId(product, price.dimension);
        return propertyValues;
    }

    function addMilkPropertyValues(index, product) {
        var propertyValues = angular.copy(zomatoPropertyValues);
        propertyValues.value = milkOption[index];
        propertyValues.vendorEntityId = getMilkVariantDetailId(product, milkOption[index]);
        return propertyValues;
    }

    function addMilkModifier(index, product) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = getMilkVariantDetailId(product, milkOption[index]);;
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            var dummyProduct = createDummyProductForProperties(milkOption[index],addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(createDummyProductForProperties(milkOption[index],addOn.vendorEntityId), dummyProduct.prices[0]));
        }
        addOn.order = index;
        return addOn;
    }

    function addRegularMilkTypeModifier(index, product) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = getRegularMilkPaidAddonVariantId(product);;
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            var dummyProduct = createDummyProductForProperties("Regular",addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(dummyProduct, dummyProduct.prices[0]));
        }
        addOn.order = index;
        return addOn;
    }

    function addMilkTypeModifier(index , product , price , milkTypePaidAddon,index) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId =  milkTypePaidAddon.id;
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            var  milkTypeProduct = service.productMap[milkTypePaidAddon.id];
            addPaidAddOns(milkTypePaidAddon,1,milkTypeProduct.prices[0]);
            //service.catalog.menu.catalogues.push(addNewCatalogue(milkTypeProduct, milkTypeProduct.prices[0]));
        }
        addOn.order = index;
        return addOn;
    }

    function addCoffeeFlavourModifier(index , product , price , flavourPaidAddon,index) {
            var addOn = angular.copy(zomatoVariant);
            addOn.vendorEntityId =  flavourPaidAddon.id;
            if (!cataloguesIds.includes(addOn.vendorEntityId)) {
                cataloguesIds.push(addOn.vendorEntityId);
                var  flavourProduct = service.productMap[flavourPaidAddon.id];
                addPaidAddOns(flavourPaidAddon,1,flavourProduct.prices[0]);
                //service.catalog.menu.catalogues.push(addNewCatalogue(milkTypeProduct, milkTypeProduct.prices[0]));
            }
            addOn.order = index;
            return addOn;
        }

    function createDummyProductForProperties(productName, productId) {
        return {
            id: productId,
            name: productName,
            type: 12,
            subType: 1202,
            classification: 'FREE_ADDON',
            taxCode: "00009963",
            billType: "NET_PRICE",
            prices: [
                {
                    dimension: 'None',
                    price: 0,
                    recipe: null
                }
            ]
        }
    }


    function addIngredientProducts(product) {
        var groups = [];
        var propertyIndex = 3;
        if (product.prices == null || product.prices.length === 0) {
            service.errorList.push("Price not found for " + product.name + ". Please add price first.")
        } else {
            if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.variants != null) {
                product.prices[0].recipe.ingredient.variants.forEach(function (ingredientVariant, index) {
                    var property = angular.copy(zomatoProperties);
                    property.name = ingredientVariant.product.name;
                    if (ingredientVariant.product.displayName != null && ingredientVariant.product.displayName.trim().length > 0) {
                        property.name = ingredientVariant.product.displayName;
                    }
                    property.order = propertyIndex + index;
                    property.vendorEntityId = ingredientVariant.product.name + "_" + (product.id).toString();
                    ingredientVariant.details.sort(function (a, b) {
                        return (a.defaultSetting === b.defaultSetting) ? 0 : a.defaultSetting ? -1 : 1;
                    });
                    ingredientVariant.details.forEach(function (ingredientVariantDetail, indexx) {
                        property.propertyValues.push(addIngredientPropertyValues(ingredientVariantDetail, indexx, product));
                    });
                    groups.push(property);
                });
            }
            if (product.prices[0].dimension === "None" && product.prices[0].recipe.ingredient.products.length !== 0) {
                product.prices[0].recipe.ingredient.products.forEach(function (ingredientProduct, index) {
                    var property = angular.copy(zomatoProperties);
                    property.name = ingredientProduct.display;
                    property.order = propertyIndex + index;
                    property.vendorEntityId = product.id + "_" + ingredientProduct.display;
                    ingredientProduct.details.forEach(function (ingredientProductDetail) {
                        property.propertyValues.push(addIngredientProductPropertyValues(ingredientProductDetail, product));
                    });
                    groups.push(property);
                });
            }
        }
        return groups;
    }

    function addIngredientProductsToModifierGroups(product,groups,sugarProperty,price) {
        var propertyIndex = 3;
        var isSugarPropertyExist = false;
        if (product.prices == null || product.prices.length === 0) {
            service.errorList.push("Price not found for " + product.name + ". Please add price first.")
        } else {
            if (price.recipe != null && price.recipe.ingredient.variants != null) {
                price.recipe.ingredient.variants.forEach(function (ingredientVariant, index) {
                    var modGroup = angular.copy(zomatoModifierGroups);
                    modGroup.name = ingredientVariant.product.name;
                    modGroup.recipeVariant = true;
                    isSugarPropertyExist = false;
                    if(ingredientVariant.product.displayName == "Sugar"){
                        isSugarPropertyExist = true;
                    }
                    if (ingredientVariant.product.displayName != null && ingredientVariant.product.displayName.trim().length > 0) {
                        modGroup.name = ingredientVariant.product.displayName;
                    }
                    modGroup.displayName = modGroup.name;
                    modGroup.max = 1;
                    modGroup.min = 1;
                    modGroup.order = propertyIndex + index;
                    modGroup.vendorEntityId = ingredientVariant.product.name + "_" + (product.id).toString();
                    ingredientVariant.details.sort(function (a, b) {
                        return (a.defaultSetting === b.defaultSetting) ? 0 : a.defaultSetting ? -1 : 1;
                    });
                    ingredientVariant.details.forEach(function (ingredientVariantDetail, indexx) {
                        modGroup.variants.push(addIngredientProductModifierValues(ingredientVariantDetail,  product,indexx));
                    });
                    if (!modifierGroupIds.includes(modGroup.vendorEntityId)) {
                        groups.push(modGroup);
                        modifierGroupIds.push(modGroup.vendorEntityId);
                    }
                    if(isSugarPropertyExist== true){
                        sugarProperty = modGroup;
                    }
                });
            }
            if (price.dimension === "None" && price.recipe.ingredient.products.length !== 0) {
                price.recipe.ingredient.products.forEach(function (ingredientProduct, index) {
                    var modGroup = angular.copy(zomatoModifierGroups);
                    modGroup.name = ingredientProduct.display;
                    modGroup.displayName = modGroup.name;
                    modGroup.order = propertyIndex + index;
                    modGroup.max = 1;
                    modGroup.min = 1;
                    modGroup.vendorEntityId = product.id + "_" + ingredientProduct.display;
                    modGroup.recipeVariant = true;
                    ingredientProduct.details.forEach(function (ingredientProductDetail) {
                        modGroup.variants.push(addIngredientProductModifierValues(ingredientProductDetail, product,index));
                    });
                    if (!modifierGroupIds.includes(modGroup.vendorEntityId)) {
                        groups.push(modGroup);
                        modifierGroupIds.push(modGroup.vendorEntityId);
                    }
                });
            }
        }
        return sugarProperty;
    }

    function addIngredientProductPropertyValues(ingredientProductDetail, product) {
        var propertyValues = angular.copy(zomatoPropertyValues);
        propertyValues.value = ingredientProductDetail.product.name;
        propertyValues.vendorEntityId = getIngredientProductDetailId(product, ingredientProductDetail);
        return propertyValues;
    }

    function addIngredientProductModifierValues(ingredientProductDetail, product,index) {

        var addOn = angular.copy(zomatoVariant);
         var ingredientAlias =  ingredientProductDetail.alias != null ?  ingredientProductDetail.alias :  ingredientProductDetail.product.name;
        addOn.vendorEntityId = product.id + "_" + ingredientAlias.replace(/[ ]/g, "_");
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            var dummyProduct = createDummyProductForProperties(ingredientAlias,addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(dummyProduct, dummyProduct.prices[0]));
        }
        addOn.order = index;
        return addOn;
    }

    function addIngredientPropertyValues(ingredientVariantDetail, index, product) {
        var propertyValues = angular.copy(zomatoPropertyValues);
        propertyValues.value = ingredientVariantDetail.alias;
        propertyValues.vendorEntityId = getIngredientVariantDetailId(product, ingredientVariantDetail);
        return propertyValues;
    }

    function isPaidAddonInCombination(variantCombination){
        variantCombination = variantCombination + "";
        var isPaidAddonIncluded = false;
        service.variantPaidAddOnsList.forEach(function (paidAddonId){
            if(paidAddonId + "" == variantCombination.split("_")[0]){
                isPaidAddonIncluded = true;
            }
        });
        return isPaidAddonIncluded;
    }

    function isPaidAddonInVariantCombination(variant){
        var isPaidAddonIncluded = false;
        var foundPaidAddonId = null;
        if(variant.propertyValues!=null){
            variant.propertyValues.forEach(function (property){
                var propertyValue = property.vendorEntityId + "";
                service.variantPaidAddOnsList.forEach(function (paidAddonId){
                    if(paidAddonId + "" == propertyValue.split("_")[0]){
                        isPaidAddonIncluded = true;
                        foundPaidAddonId = paidAddonId;
                    }
                });
            })

        }
        return foundPaidAddonId;
    }

    function addVariants(product,catalogue) {
        var groups = [];
        var modids= [];
        var tempArr = [];
        var grps = [];
        if (product.prices == null || product.prices.length === 0) {
            service.errorList.push("Price not found for " + product.name + ". Please add price first.");
        }
        var isSugarVariant = false;
        catalogue.properties.forEach(function (property){
            if(property.name == "Sugar"){
                isSugarVariant = true;
            }
        });

        if ((product.classification === "MENU")&& product.prices.length !== 0) {
            //var propertyIdArrays = [];
            product.prices.forEach(function (price, index) {
                var haveSpecialMilkPaidAddon = dimensionHaveOatMilk(price);
                var haveCoffeeFlavourPaidAddons = dimensionHaveCoffeeFlavour(price);
                if(product.prices.length > 1 && price.dimension == "1 Pack"){
                   return;
                }
                var variantArray = [];
                if(service.addonMode == false){
                    //for dimension split products like desi chai we need to check _ in product id
                    var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
                    if ([10, 1282].indexOf(parseInt(productId)) >= 0) {
                        var detailArray = [];
                        for (var i = 0; i < milkOption.length; i++) {
                            var vendorEntityId = getMilkVariantDetailId(product, milkOption[i]);
                            detailArray.push(vendorEntityId);
                        }
                        variantArray.push(detailArray);
                    }
                    if (price.recipe.ingredient.variants.length > 0) {
                        price.recipe.ingredient.variants.forEach(function (ingredientVariant) {
                            var detailArray = [];
                            ingredientVariant.details.forEach(function (variantDetail) {
                                var vendorEntityId = getIngredientVariantDetailId(product, variantDetail);
                                detailArray.push(vendorEntityId);
                            });
                            if(isSugarVariant && ingredientVariant.product.displayName == "Sugar" ){
                                if (price.recipe.options != null) {
                                    price.recipe.options.forEach(function (paidAddOn, index) {
                                        if (paidAddOn.type === "PRODUCT" && service.productMap[paidAddOn.productId] != null &&
                                            service.productMap[paidAddOn.productId].prices != null &&
                                            service.productMap[paidAddOn.productId].prices.length > 0 &&
                                            service.dineInProductMap[paidAddOn.productId] != null &&
                                            service.dineInProductMap[paidAddOn.productId].prices != null &&
                                            service.dineInProductMap[paidAddOn.productId].prices.length > 0 &&
                                            service.productMap[paidAddOn.productId].classification === "PAID_ADDON") {
                                            if(service.variantPaidAddOnsList.includes(paidAddOn.productId)){
                                                detailArray.push(paidAddOn.productId + "_" + product.id);
                                            }
                                        }
                                    });
                                }
                            }
                            variantArray.push(detailArray);
                        });
                    }
                    if (price.recipe.ingredient.products.length > 0) {
                        price.recipe.ingredient.products.forEach(function (ingredientProduct) {
                            var detailArray = [];
                            ingredientProduct.details.forEach(function (productDetail) {
                                var vendorEntityId = getIngredientProductDetailId(product, productDetail);
                                detailArray.push(vendorEntityId)
                            });
                            variantArray.push(detailArray);
                        })
                    }
                }

                if (variantArray.length > 0) {
                    var result = [];
                    getCombinations(variantArray, result);
                    for (var i = 0; i < result.length; i++) {
                        var variantComb = result[i].split(" ");
                        var paidAddonPrice = 0;
                        for(var j = 0;j<variantComb.length ; j++){
                            if(isPaidAddonInCombination(variantComb[j])){
                                paidAddonPrice = paidAddonPrice + service.productMap[variantComb[j].split("_")[0]].prices[0].price;
                            }
                        }
                        var variantsObj = angular.copy(zomatoVariants);
                        variantsObj.vendorEntityId = product.id + "_" + price.dimension + "_" + i;
                        variantsObj.prices.push(addPrice(price.price + paidAddonPrice));

                        var propValues = result[i].trim().split(" ");
                        if (product.prices.length > 1) {
                            //adding dimension variant object
                            var propertyValue = angular.copy(zomatoPropertyValue);
                            propertyValue.vendorEntityId = getDimensionId(product, price.dimension);
                            variantsObj.propertyValues.push(propertyValue);
                        }
                        propValues.forEach(function (val) {
                            var propertyValue = angular.copy(zomatoPropertyValue);
                            propertyValue.vendorEntityId = val;
                            variantsObj.propertyValues.push(propertyValue);
                        });
                        if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                            var modGroup = addPaidModifierGroupVariants(price, product,isSugarVariant);
                            if (modGroup != null) {
                                variantsObj.modifierGroups.push(modGroup);
                            }
                        }
                        if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                            // returns list of modifier groups
                            variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForRecommendations(product));
                        }
                        //adding modifier group
                        if (service.addonsMap[productId] != null) {
                            variantsObj.modifierGroups.push(addModifierGroupVariants(price.recipe, product));
                        }
                        groups.push(variantsObj);
                    }
                } else {
                    var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
                    var variantsObj = angular.copy(zomatoVariants);
                    if(price.dimension == "MiniKetli" && service.splitDesiChaiDimensionName == "MiniKetli"
                                          && catalogue.vendorEntityId == "10_MiniKetli_S"){
                        variantsObj.vendorEntityId = productId + "_" + price.dimension + "_" + 15;
                    }else{
                      variantsObj.vendorEntityId = productId + "_" + price.dimension + "_" + index;
                    }

                    variantsObj.prices.push(addPrice(price.price));
                    var fixedMealProductKey = product.id+"_"+price.dimension;
                    if (product.prices.length > 1) {
                        var propertyValue = angular.copy(zomatoPropertyValue);
                        propertyValue.vendorEntityId = getDimensionId(product, price.dimension);
                        variantsObj.propertyValues.push(propertyValue);
                    }
                    var sugarProperty = null;
                    if(haveSpecialMilkPaidAddon){
                        addSpecialMilkTypeAddons(product,price,service.catalog.menu.modifierGroups,variantsObj.modifierGroups);
                    }
                    if(haveCoffeeFlavourPaidAddons){
                        addCoffeeFlavourAddons(product,price,service.catalog.menu.modifierGroups,variantsObj.modifierGroups);
                    }
                    addMilkAddons(product,service.catalog.menu.modifierGroups);
                    sugarProperty= addIngredientProductsToModifierGroups(product,tempArr,sugarProperty,price);
                    var sugarPropName = null;
                    if(sugarProperty!=null){
                        sugarPropName = sugarProperty.vendorEntityId;
                        if (price.recipe.options != null) {
                            var sugarPaidAddonFound = false;
                            var paidAddonSugarGroup = angular.copy(sugarProperty);
                            price.recipe.options.forEach(function (paidAddOn, index) {
                                if(service.variantPaidAddOnsList.indexOf(paidAddOn.id) >=0){
                                    if (paidAddOn.type === "PRODUCT" && service.productMap[paidAddOn.productId] != null &&
                                        service.productMap[paidAddOn.productId].prices != null &&
                                        service.productMap[paidAddOn.productId].prices.length > 0 &&
                                        service.dineInProductMap[paidAddOn.productId] != null &&
                                        service.dineInProductMap[paidAddOn.productId].prices != null &&
                                        service.dineInProductMap[paidAddOn.productId].prices.length > 0 &&
                                        service.productMap[paidAddOn.productId].classification === "PAID_ADDON") {
                                        var paidAddon = addPaidAddOns(paidAddOn, index, price);
                                        paidAddon.order = 2;
                                        //paidAddon.vendorEntityId = paidAddon.vendorEntityId + "_Paid_Addons";
                                        paidAddonSugarGroup.variants.push(paidAddon);
                                        if(paidAddonSugarGroup.vendorEntityId  == sugarProperty.vendorEntityId){
                                          paidAddonSugarGroup.vendorEntityId = paidAddonSugarGroup.vendorEntityId + "_" + price.dimension;
                                        }

                                        sugarPaidAddonFound = true;
                                        var modifierGroupVariants = angular.copy(zomatoModifierGroup);
                                      /*  if(sugarProperty!=null){
                                            modifierGroupVariants.vendorEntityId = paidAddonSugarGroup.vendorEntityId + "_" + price.dimension;
                                            variantsObj.modifierGroups.push(modifierGroupVariants);
                                        }*/

                                    }
                                }
                            });
                            if(sugarPaidAddonFound  && modids.indexOf(paidAddonSugarGroup.vendorEntityId) <0){
                                          tempArr.push(paidAddonSugarGroup);
                                          modids.push(paidAddonSugarGroup.vendorEntityId);
                           }
                        }
                       // service.catalog.menu.modifierGroups.concat(tempArr);
                    }
                    var paidAddonToShowInSugarGroup= false;
                     if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                                           service.paidAddonsMap[productId][price.dimension].forEach(function (paidAdd) {
                                               if (service.variantPaidAddOnsList.indexOf(paidAdd.id) >= 0 &&
                                                  service.productMap[paidAdd.id] != null &&
                                                           service.productMap[paidAdd.id].prices != null &&
                                                           service.productMap[paidAdd.id].prices.length > 0 &&
                                                           service.dineInProductMap[paidAdd.id] != null &&
                                                           service.dineInProductMap[paidAdd.id].prices != null &&
                                                           service.dineInProductMap[paidAdd.id].prices.length > 0 &&
                                                           service.productMap[paidAdd.id].classification === "PAID_ADDON") {
                                                   paidAddonToShowInSugarGroup = true;
                                               }
                                           })
                     }
                    if(paidAddonToShowInSugarGroup){
                        var modifierGroupVariants = angular.copy(zomatoModifierGroup);
                        if(sugarProperty!=null){
                            modifierGroupVariants.vendorEntityId = sugarPropName + "_" + price.dimension;
                            variantsObj.modifierGroups.push(modifierGroupVariants);
                        }
                    }else{
                        var modifierGroupVariants = angular.copy(zomatoModifierGroup);
                        if(sugarProperty!=null){
                            modifierGroupVariants.vendorEntityId = sugarPropName;
                            variantsObj.modifierGroups.push(modifierGroupVariants);
                        }
                    }

                    addPropertyAddonVariants(product,price,variantsObj,isSugarVariant!=null);
                    if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                        var modGroup = addPaidModifierGroupVariants(price, product,sugarProperty!=null);
                        if (modGroup != null) {
                            variantsObj.modifierGroups.push(modGroup);
                        }
                    }
                    //adding modifier group
                    if (service.addonsMap[productId] != null) {
                        variantsObj.modifierGroups.push(addModifierGroupVariants(price.recipe, product));
                    }
                    if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                        variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForRecommendations(product));
                    }
                    if (service.fixedMealProductMap != null && service.fixedMealProductMap[fixedMealProductKey] != null) {
                        variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForFixedMeal(product));
                    }
                    groups.push(variantsObj);
                }
            });
            service.catalog.menu.modifierGroups = service.catalog.menu.modifierGroups.concat(tempArr);
            service.catalog.menu.modifierGroups = service.catalog.menu.modifierGroups.concat(grps);
            return groups;
        }
    }


    function addPropertyAddonVariants(product,price,variantsObj,isSugarVariantExist){
        //for dimension split products like desi chai we need to check _ in product id
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        if ([10, 1282].indexOf(parseInt(productId)) >= 0) {

            var modifierGroupVariants = angular.copy(zomatoModifierGroup);
            modifierGroupVariants.vendorEntityId = product.id + "_milk_option"
            modifierGroupVariants.order = 2;

            variantsObj.modifierGroups.push(modifierGroupVariants);
        }
        if (price.recipe.ingredient.variants.length > 0) {
            price.recipe.ingredient.variants.forEach(function (ingredientVariant) {
                if(ingredientVariant.product.displayName == "Sugar" && !isSugarVariantExist){
                    var modifierGroupVariants = angular.copy(zomatoModifierGroup);
                    modifierGroupVariants.vendorEntityId = ingredientVariant.product.name + "_" + (product.id).toString();;
                    modifierGroupVariants.order = 2;
                    variantsObj.modifierGroups.push(modifierGroupVariants)
                }else if(ingredientVariant.product.displayName != "Sugar"){
                    var modifierGroupVariants = angular.copy(zomatoModifierGroup);
                    modifierGroupVariants.vendorEntityId = ingredientVariant.product.name + "_" + (product.id).toString();;
                    modifierGroupVariants.order = 2;
                    variantsObj.modifierGroups.push(modifierGroupVariants);
                }
            });
        }
        if (price.recipe.ingredient.products.length > 0) {
            price.recipe.ingredient.products.forEach(function (ingredientProduct) {
                var modifierGroupVariants = angular.copy(zomatoModifierGroup);
                modifierGroupVariants.vendorEntityId = product.id + "_" + ingredientProduct.display;
                modifierGroupVariants.order = 2;
                variantsObj.modifierGroups.push(modifierGroupVariants);
            })
        }
    }

    function addAddonVariants(product) {
        var groups = [];
        var variantsObj = angular.copy(zomatoVariants);
        variantsObj.vendorEntityId = product.id + "";
        variantsObj.prices.push(addPrice(0));
        groups.push(variantsObj);
        return groups;
    }

    function addPaidAddonVariants(product, priceDetail) {
        var groups = [];
        if (service.productMap[product.id] != null && service.productMap[product.id].prices.length > 0
            && service.dineInProductMap[product.id] != null && service.dineInProductMap[product.id].prices.length > 0) {
            var variantsObj = angular.copy(zomatoVariants);
            variantsObj.vendorEntityId = product.id + "";
            var price = 0;
            if (service.productMap[product.id].prices.length === 1) {
                price = service.productMap[product.id].prices[0].price;
            } else {
                price = service.productMap[product.id].prices.filter(function (data) {
                    return data.dimension === priceDetail.dimension
                })[0].price;
            }
            variantsObj.prices.push(addPrice(price));
            groups.push(variantsObj);
        }
        return groups;
    }


    function addModifierGroupVariants(recipe, product) {
        var modifierGroupVariants = angular.copy(zomatoModifierGroup);
        modifierGroupVariants.vendorEntityId = getModifierGroupId(product);
        modifierGroupVariants.order = 3;
        if (!modifierGroupIds.includes(getModifierGroupId(product))) {
            service.catalog.menu.modifierGroups.push(addNewModifierGroups(recipe, product));
            modifierGroupIds.push(getModifierGroupId(product));
        }
        return modifierGroupVariants;
    }

    function addPaidModifierGroupVariants(price, product,isSugarVariant) {

        var modifierGroupVariants = angular.copy(zomatoModifierGroup);
        modifierGroupVariants.vendorEntityId = getPaidModifierGroupId(product, price.dimension);
        modifierGroupVariants.order = 2;
        var vid = modifierGroupVariants.vendorEntityId;
        if (!modifierGroupIds.includes(vid)) {
            var modGroup = addNewPaidModifierGroups(price, product,isSugarVariant);
            if (modGroup == null) {
                return null;
            }
            service.catalog.menu.modifierGroups.push(modGroup);
            modifierGroupIds.push(vid);
        }
        return modifierGroupVariants;
    }

    function addNewModifierGroups(recipe, product) {
        var compareId = product.id + "";
        var isDesiChaiGroup = (AppUtil.splitDimensionProductIds.indexOf(product.id) >= 0) || compareId.includes("10_")
            || compareId.includes("1282_");
        if (isDesiChaiGroup) {
            return addNewModifierGroupsDesiChai(recipe, product);
        } else {
            return addNewModifierGroupsNonDesiChai(recipe, product)
        }
    }

    function addNewModifierGroupsNonDesiChai(recipe, product) {
        var modGroup = angular.copy(zomatoModifierGroups);
        modGroup.vendorEntityId = getModifierGroupId(product);
        modGroup.name = "Add Ons"; //getModifierGroupId(product);
        modGroup.displayName = "Add Ons";
        modGroup.min = 0;
        var data = 0;
        recipe.addons.forEach(function (addOn, index) {
            modGroup.variants.push(addAddOns(addOn, index));
            data++;
        });
        modGroup.max = data;
        return modGroup;
    }


    function sortDesiChaiAddons(addons, desiChaiAddonOrderMap){
    addons.sort(function (a, b) {
        return desiChaiAddonOrderMap[a.product.productId] != null ? (
            desiChaiAddonOrderMap[b.product.productId] == null || desiChaiAddonOrderMap[a.product.productId] < desiChaiAddonOrderMap[b.product.productId] ? -1 : 1
        ) : 1
    });
}

    function addNewModifierGroupsDesiChai(recipe, product) {
        var modGroup = angular.copy(zomatoModifierGroups);
        var desiChaiAddonOrderMap = AppUtil.getDesiChaiAddonsOrderForCOD();
        sortDesiChaiAddons(recipe.addons,desiChaiAddonOrderMap);
        modGroup.vendorEntityId = getModifierGroupId(product);
        modGroup.name = "Add Ons";//getModifierGroupId(product);
        modGroup.displayName = "Add Ons";
        modGroup.min = 1;
        var data = 0;
        for (var i = 0; i < recipe.addons.length; i++) {
            if (recipe.addons[i].product.productId == 969) {
                modGroup.variants.push(addAddOns(recipe.addons[i], data));
                data++;
                break;
            }
        }

        for (var i = 0; i < recipe.addons.length; i++) {
            if (recipe.addons[i].product.productId != 969) {
                modGroup.variants.push(addAddOns(recipe.addons[i], data));
                data++;
            }
        }

        modGroup.max = data;
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = "999999";
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(createNoAddonProduct('No Addon', addOn.vendorEntityId)));
        }
        addOn.order = data;
        modGroup.variants.push(addOn);
        return modGroup;
    }

    function addAddOns(addOns, index) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = addOns.product.productId + "";
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(createAddonProduct(addOns)));
        }
        addOn.order = index;
        return addOn;
    }

    function addNewPaidModifierGroups(price, product,isSugarVariant) {
        var recipe = price.recipe;
        var modGroup = angular.copy(zomatoModifierGroups);
        modGroup.vendorEntityId = getPaidModifierGroupId(product, price.dimension);
        modGroup.name = "Top it up with";
        modGroup.displayName = "Top it up with";
        modGroup.min = 0;
        var data = 0;
        if (recipe.options != null) {
            recipe.options.forEach(function (paidAddOn, index) {
                if (paidAddOn.type === "PRODUCT" && service.productMap[paidAddOn.productId] != null &&
                    service.productMap[paidAddOn.productId].prices != null &&
                    service.productMap[paidAddOn.productId].prices.length > 0 &&
                    service.dineInProductMap[paidAddOn.productId] != null &&
                    service.dineInProductMap[paidAddOn.productId].prices != null &&
                    service.dineInProductMap[paidAddOn.productId].prices.length > 0 &&
                    service.productMap[paidAddOn.productId].classification === "PAID_ADDON"
                    && service.specialMilkPaidAddonList.indexOf(paidAddOn.productId) == -1
                    && service.coffeeFlavours.indexOf(paidAddOn.productId) == -1)  {
                    if((!isPaidAddonInCombination(paidAddOn.id) || !isSugarVariant)){
                    var paidAddon = addPaidAddOns(paidAddOn, index, price);
                        modGroup.variants.push(paidAddon);
                        data++;
                    }
                }
            });
        }
        if(product.brandId == 6){
                            modGroup.max = 1;
                            modGroup.maxSelectionsPerItem = 1;
       }else{
                         modGroup.max = 10;
                         modGroup.maxSelectionsPerItem = 10;
       }

        return data > 0 ? modGroup : null;
    }

    function addPaidAddOns(paidAddOns, index, price,id) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = paidAddOns.productId + "";
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(createPaidAddonProduct(paidAddOns), price));
        }
        addOn.order = index;
        return addOn;
    }

/*    function addPaidAddOnsForSugar(paidAddOns, index, price,id) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = id + "";
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(createPaidAddonProductForSugar(paidAddOns,id), price));
        }
        addOn.order = index;
        return addOn;
    }*/

    function addPrice(variantPrice) {
        var prices = angular.copy(zomatoPrices);
        prices.service = "delivery";
        prices.price = variantPrice;
        return prices;
    }

    function addModifierGroupForRecommendations(product) {

        var modifierGroupsInVariantList = [];
        //multiple modifierGroups for single product Id/vendorEntityID
        var recomModGroupsList = addNewRecommendationModifierGroups(product);
        recomModGroupsList.forEach(function (recomGroupForProduct) {
            var modifierGroupVariants = angular.copy(zomatoModifierGroup);
            //refers id as that of recom group
            modifierGroupVariants.vendorEntityId = recomGroupForProduct.vendorEntityId;
            modifierGroupVariants.order = 1;

            if (!modifierGroupIds.includes(recomGroupForProduct.vendorEntityId)) {
                modifierGroupIds.push(recomGroupForProduct.vendorEntityId);
                service.catalog.menu.modifierGroups.push(recomGroupForProduct);
            }

            modifierGroupsInVariantList.push(modifierGroupVariants);
        });
        return modifierGroupsInVariantList;
    }

    function addModifierGroupForFixedMeal(product) {

        var modifierGroupsInVariantList = [];
        //multiple modifierGroups for single product Id/vendorEntityID
        var recomModGroupsList = addNewFixedMealModifierGroup(product);
        recomModGroupsList.forEach(function (recomGroupForProduct) {
            var modifierGroupVariants = angular.copy(zomatoModifierGroup);
            //refers id as that of recom group
            modifierGroupVariants.vendorEntityId = recomGroupForProduct.vendorEntityId;
            modifierGroupVariants.order = 1;

            if (!modifierGroupIds.includes(recomGroupForProduct.vendorEntityId)) {
                modifierGroupIds.push(recomGroupForProduct.vendorEntityId);
                service.catalog.menu.modifierGroups.push(recomGroupForProduct);
            }

            modifierGroupsInVariantList.push(modifierGroupVariants);
        });
        return modifierGroupsInVariantList;
    }

    function addNewFixedMealModifierGroup(product) {
        //multiple recommendation groups for a product
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        var productKey = productId + "_" + product.prices[0].dimension;
        console.log("Product id in Fixed Meal Modifer Group ::::", productKey);
        var fixedMealModifierGroupList = [];
        var fixedMeal = service.fixedMealProductMap[productKey];
        var fixedMealPrice = 0;
        var fixedMealPriceAvailable = false ;
        if(product.prices!=null && product.prices.length>0 && product.prices[0].price >0){
            fixedMealPriceAvailable= true;
            fixedMealPrice=product.prices[0].price;
        }
        Object.entries(fixedMeal).forEach(function([optionName,optionDetail], index) {
            var modGroup = angular.copy(zomatoModifierGroups);
            modGroup.vendorEntityId = productId + "_" + optionName;
            modGroup.name = optionName;
            modGroup.displayName = optionName;
            var min = Number.MAX_SAFE_INTEGER;
            var max = Number.MIN_SAFE_INTEGER;
            var data = 0;
            if(fixedMealPrice ===0){
                Object.entries(optionDetail).forEach(function ([item,detail] , index){
                    var product = service.productMap[detail.product.productId];
                    fixedMealPrice+=product.prices[0].price;
                    fixedMealPriceAvailable =true;
                });
            }
            Object.entries(optionDetail).forEach(function([item,detail] , index){
                min = detail.min;
                max = detail.max;
                var fixedMealOption = addFixedMealOptions(item, detail, product, index, fixedMealPrice, fixedMealPriceAvailable);
                if (!AppUtil.isEmptyObject(fixedMealOption)) {
                    modGroup.variants.push(fixedMealOption);
                    data++;
                }
            });
            modGroup.min = min ? min : 1;
            modGroup.max = max ? max : 1;
            modGroup.maxSelectionsPerItem = max ? max : 1;
            if (modGroup.variants.length !== 0) {
                fixedMealModifierGroupList.push(modGroup);
            }
        });
        return fixedMealModifierGroupList;
    }

    function addNewRecommendationModifierGroups(product) {
        //multiple recommendation groups for a product
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        var recommendationModifierGroupList = [];

        // First assign to a variable, then sort by recommendationTitle in reverse alphabetical order (Z to A) only if brandid == 6
        var unitRecommendationsGroupsList = service.menuRecommendationData[productId];
        if (product.brandId == 6) {
            unitRecommendationsGroupsList.sort(function(a, b) {
                var titleA = (a.recommendationTitle || '').toLowerCase();
                var titleB = (b.recommendationTitle || '').toLowerCase();
                return titleB.localeCompare(titleA); // Reverse order
            });
        }

        unitRecommendationsGroupsList.forEach(function (recomGroup, groupIndex) {
            var modGroup = angular.copy(zomatoModifierGroups);
            modGroup.vendorEntityId = getRecommendationModifierGroupId(product) + "_" + groupIndex;
            modGroup.name = recomGroup.recommendationTitle;
            modGroup.displayName = recomGroup.recommendationTitle;
            modGroup.min = 0;
            var data = 0;
            recomGroup.recommendedItemsList.forEach(function (item, index) {
                var upsellItem = addRecommendationItem(item, product, index);
                if(!AppUtil.isEmptyObject(upsellItem)) {
                    modGroup.variants.push(upsellItem);
                    data++;
                }
            });
            modGroup.max = 10;
            modGroup.maxSelectionsPerItem = 10;
            if(modGroup.variants.length !== 0) {
                recommendationModifierGroupList.push(modGroup);
            }
        });

        return recommendationModifierGroupList;
    }

    function addRecommendationItem(item, product, index) {
        if (service.productMap[item.productId] == null || service.productMap[item.productId].prices.length == 0) {
            //service.errorList.push("Upselling product id: " + item.productId + " for product: " + product.name + " not found in catalog");
            console.log("Upselling product id: " + item.productId + " for product: " + product.name + " not found in catalog");
            return null;
        } else {
            var upsellingItem = angular.copy(zomatoVariant);
            var recommendationProduct = service.productMap[item.productId];
            var dimension = item.dimension != null ? item.dimension : "None";
            upsellingItem.vendorEntityId = item.productId + "_" + dimension + "_" + product.id + "_RECOM"; //Recommendation item
            if (!cataloguesIds.includes(upsellingItem.vendorEntityId)) {
                cataloguesIds.push(upsellingItem.vendorEntityId);
                var createdProduct = createComboAddonProduct(recommendationProduct, item.discountAmount, upsellingItem.vendorEntityId, item.dimension, 1);
                if(item.discountAmount > 0) {
                    createdProduct.name = createdProduct.name + " - save Rs. " + item.discountAmount;
                }
                var addDimensionSuffix = recommendationProduct.prices.length !== 1;
                var createdCatalog = addNewCatalogue(createdProduct,null,null,addDimensionSuffix);
                createdCatalog.variants = [{
                    vendorEntityId: upsellingItem.vendorEntityId,
                    propertyValues: [],
                    prices: [{service: "delivery", price: recommendationProduct.prices[0].price - item.discountAmount}],
                    modifierGroups: []
                }];
                service.catalog.menu.catalogues.push(createdCatalog);
            }
            upsellingItem.order = index;
            return upsellingItem;
        }
    }

    function addFixedMealItems(item, product, index) {
        if (service.productMap[item.productId] == null) {
            service.errorList.push("Upselling product id: " + item.productId + " for product: " + product.name + " not found in catalog");
        } else {
            var upsellingItem = angular.copy(zomatoVariant);
            var recommendationProduct = service.productMap[item.productId];
            var dimension = item.dimension != null ? item.dimension : "None";
            upsellingItem.vendorEntityId = item.productId + "_" + dimension + "_" + product.id + "_FIXED_MEAL"; //Recommendation item
            if (!cataloguesIds.includes(upsellingItem.vendorEntityId)) {
                cataloguesIds.push(upsellingItem.vendorEntityId);
                var createdProduct = createComboAddonProduct(recommendationProduct, item.discountAmount, upsellingItem.vendorEntityId, item.dimension, 1);
                if(item.discountAmount > 0) {
                    createdProduct.name = createdProduct.name + " - save Rs. " + item.discountAmount;
                }
                var createdCatalog = addNewCatalogue(createdProduct);
                createdCatalog.variants = [{
                    vendorEntityId: upsellingItem.vendorEntityId,
                    propertyValues: [],
                    prices: [{service: "delivery", price: recommendationProduct.prices[0].price - item.discountAmount}],
                    modifierGroups: []
                }];
                service.catalog.menu.catalogues.push(createdCatalog);
            }
            upsellingItem.order = index;
            return upsellingItem;
        }
    }

    function addFixedMealOptions(item , detail , product, index, fixedMealPrice, fixedMealPriceAvailable) {
        var fixedMealOptionProductId = item.toString().indexOf("_") >= 0 ? item.split("_")[0] : [];
        var fixedMealOptionDimension = item.toString().indexOf("_") >= 0 ? item.split("_")[1] : "None";
        var discountAmount = product.discountAmount != null && product.discountAmount>0 ? product.discountAmount : 0 ;
        if (service.productMap[fixedMealOptionProductId] == null) {
            service.errorList.push("Fixed Meal product id: " + item.productId + " for product: " + product.name + " not found in catalog");
        }else if(service.dineInProductMap[fixedMealOptionProductId] == null || service.dineInProductMap[fixedMealOptionProductId].prices == null ||
            service.dineInProductMap[fixedMealOptionProductId].prices.length == 0){
            console.log("Fixed Meal product id: " + fixedMealOptionProductId + " for product: " + product.name + " not found in Cafe");
            return null;
        } else {
            var fixedMealOption = angular.copy(zomatoVariant);
            var fixedMealProduct = service.productMap[fixedMealOptionProductId];
            fixedMealOption.vendorEntityId = fixedMealOptionProductId + "_FIXED_M"; //Recommendation item
            if (!cataloguesIds.includes(fixedMealOption.vendorEntityId)) {
                cataloguesIds.push(fixedMealOption.vendorEntityId);
                var createdProduct = createComboAddonProduct(fixedMealProduct, discountAmount,
                    fixedMealOption.vendorEntityId, fixedMealOptionDimension, detail.quantity, fixedMealPriceAvailable);
                if(discountAmount > 0) {
                    createdProduct.name = createdProduct.name + " - save Rs. " + discountAmount;
                }
                var createdCatalog = addNewCatalogue(createdProduct);
                createdCatalog.variants = [{
                    vendorEntityId: fixedMealOption.vendorEntityId,
                    propertyValues: [],
                    prices: [{service: "delivery", price: 0}],
                    modifierGroups: []
                }];
                service.catalog.menu.catalogues.push(createdCatalog);
            }
            fixedMealOption.order = index;
            return fixedMealOption;
        }
    }

    function addHeroComboV1(product, catalogueMap) {
        if (product.prices == null || product.prices.length === 0) {
            alert("Hero Combo " + product.name + " price is missing.");
        } else if (product.prices[0].recipe == null) {
            alert("Hero Combo " + product.name + " recipe is missing.");
        } else {
            //first sub item of combo is supposed to be the hero item and should have only 1 option
            var heroProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0];
            var heroProductId = heroProduct.product.productId;
            var heroProdDim = heroProduct.dimension.code;
            var catalogue = angular.copy(zomatoCatalogues);
            catalogue.vendorEntityId = product.id + "";
            catalogue.name = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name, product.id));
            catalogue.description = getProductDescription(product.description, product.name);
            catalogue.inStock = true;
            if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null ||
                service.productImages[product.id].gridLow.url == null) {
                if (AppUtil.getEnvType() == "PROD") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
            }
            catalogue.properties = [];
            catalogue.variants = [];
            if (service.productMap[heroProductId] == null) {
                service.errorList.push("Hero combo: " + product.name + " item: " + heroProduct.product.name + " not found in catalog.");
            }
            var sizePropVendorEntityId = null;
            var exactMatch = true;
            var catalogueMapId = service.getCatalogueMapId(product, heroProduct, catalogueMap);
            catalogueMap[catalogueMapId].properties.forEach(function (prop) {
                var property = angular.copy(prop);
                if (prop.name === "Size") {
                    property.propertyValues = [];
                    var found = false;
                    prop.propertyValues.forEach(function (propValue) {
                        if (propValue.vendorEntityId.indexOf(heroProdDim) >= 0) {
                            var val = angular.copy(propValue);
                            property.propertyValues.push(val);
                            sizePropVendorEntityId = val.vendorEntityId;
                            found = true;
                        }
                    });
                    if (!found) {
                        service.errorList.push("Combo: " + product.name + " item: " + heroProduct.product.name + " dimension: "
                            + heroProduct.dimension.name + " not found in catalog.")
                    }
                    property.vendorEntityId = property.vendorEntityId + "_" + product.id;
                } else {
                    property.vendorEntityId = property.vendorEntityId + "_" + product.id;
                }
                property.propertyValues.forEach(function (propVal) {
                    propVal.vendorEntityId = propVal.vendorEntityId + "_" + product.id;
                });
                catalogue.properties.push(property);
            });
            if (sizePropVendorEntityId == null) {
                sizePropVendorEntityId = heroProdDim;
                exactMatch = false;
            }
            catalogueMap[catalogueMapId].variants.forEach(function (variant) {
                var propMatched = false;
                variant.propertyValues.forEach(function (propValue) {
                    if (!propMatched && exactMatch && propValue.vendorEntityId === sizePropVendorEntityId) {
                        propMatched = true;
                    }
                    if (!propMatched && !exactMatch && propValue.vendorEntityId.indexOf(sizePropVendorEntityId) >= 0) {
                        propMatched = true;
                    }
                });
                if (propMatched) {
                    var variant1 = angular.copy(variant);
                    variant1.prices.forEach(function (price) {
                        price.price = product.prices[0].price;
                    });
                    variant1.vendorEntityId = variant1.vendorEntityId + "_" + product.id;
                    var propValList = [];
                    variant1.propertyValues.forEach(function (propVal) {
                        var val = angular.copy(propVal);
                        val.vendorEntityId = val.vendorEntityId + "_" + product.id;
                        propValList.push(val);
                    });
                    variant1.propertyValues = propValList;
                    dropUpsellingFromVariants(variant1);
                    catalogue.variants.push(variant1);
                }
            });
            if (catalogue.variants.length === 0) {
                catalogue.variants.push({
                    vendorEntityId: product.id + "_" + product.prices[0].dimension,
                    propertyValues: [],
                    prices: [{service: "delivery", price: product.prices[0].price}],
                    modifierGroups: []
                })
            }
            //adding food and dessert addons for hero combo
            product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail, index) {
                //preparing modifier group for combo options
                if (index > 0 && index < 3) {
                    var id = index+1 ;
                    var modGroup = addComboModifierGroups(product, compositeProductDetail, "Option " + id);
                    catalogue.variants.forEach(function (variant) {
                        variant.modifierGroups.push({
                            vendorEntityId: modGroup.vendorEntityId,
                            order: variant.modifierGroups.length + 1
                        })
                    })
                }
            });
            var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
            catalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[taxCode])}];
            var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
            var tag = str.toString().replace("_", "-");
            catalogue.tags = [tag];
            catalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
            if (service.addPackaging === true) {
                catalogue.charges.push(addCharge(taxCode,product.id));
            }
            return catalogue;
        }
    }

    function addHeroComboV2(product, catalogueMap) {
        if (product.prices == null || product.prices.length === 0) {
            alert("Hero Combo " + product.name + " price is missing.");
        } else if (product.prices[0].recipe == null) {
            alert("Hero Combo " + product.name + " recipe is missing.");
        } else {
            var catalogue = angular.copy(zomatoCatalogues);
            catalogue.vendorEntityId = product.id + "_HEROCOMBO";
            catalogue.name = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name, product.id));
            catalogue.description = getProductDescription(product.description, product.name);
            catalogue.inStock = true;
            if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null ||
                service.productImages[product.id].gridLow.url == null) {
                if (AppUtil.getEnvType() == "PROD") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
            }
            catalogue.properties = [];
            catalogue.variants = [];
            var catalogProperties = [];
            product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail, index) {
                //preparing modifier group for combo options
                var property = angular.copy(zomatoProperties);
                property.propertyId = null;
                property.order = index + 1;
                property.vendorEntityId = product.id + "_HERO";
                property.name = compositeProductDetail.name;
                if (index == 0) {
                    var propertyList = [];
                    compositeProductDetail.menuProducts.forEach(function (menuProduct, ind) {
                       var menuProductVal = service.productMap[menuProduct.product.productId];
                        var zomatoAddon = angular.copy(zomatoPropertyValues);
                        var productAlias = getProductAlias(menuProductVal);
                        var menuProductName = productAlias != null ? productAlias : getProductName(menuProductVal.name);
                        zomatoAddon.vendorEntityId = menuProduct.product.productId + "_" + menuProduct.dimension.code + "_" + product.id + "_HERO";
                        zomatoAddon.value = menuProductName + getDimensionName(menuProduct.dimension.name,product.subType);
                        propertyList.push(zomatoAddon);
                        catalogue.variants.push(getPropertyVariants(product, menuProduct, zomatoAddon, ind, catalogueMap, compositeProductDetail));
                    });
                    property.propertyValues = propertyList;
                    catalogue.properties.push(property);
                }
                else {
                    var id = index + 1;
                    var modGroup = addComboModifierGroups(product, compositeProductDetail, "Item " + id);
                    catalogue.variants.forEach(function (variant, i) {
                        variant.modifierGroups.push({
                            vendorEntityId: modGroup.vendorEntityId,
                            order: index-1
                        });
                    });
                }
            });
            catalogue.variants.sort((a, b) => {
                return a.prices[0].price - b.prices[0].price;
            });
            var sortedProperty = [];
            if (catalogue.properties[0] != undefined && catalogue.properties[0] != null) {
                catalogue.variants.forEach(function (variant) {
                    catalogue.properties[0].propertyValues.forEach(function (propertyValue) {
                        if (variant.propertyValues[0].vendorEntityId == propertyValue.vendorEntityId) {
                            sortedProperty.push(propertyValue);
                        }
                    });
                    /*variant.modifierGroups.sort((a, b) => {
                        return a.order - b.order;


                    });*/
                });
                catalogue.properties[0].propertyValues = sortedProperty;
            }
            var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
            if (service.addPackaging === true && service.metadata.packagingType !== "FIXED" && product.billType != "MRP") {
                catalogue.charges.push(addCharge(taxCode,product.id));
            }
            if(service.productPackagingMappings != null && service.productPackagingMappings[product.id] != null
                             && service.productPackagingMappings[product.id].packagingValue ==0){
                                 catalogue.charges = [];
            }
            catalogue.taxGroups = [{ slug: getTaxGroupId(service.taxMap[taxCode]) }];
            var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
            var tag = str.toString().replace("_", "-");
            catalogue.tags = [tag];
            catalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
            return catalogue;
        }
    }

    function getPropertyVariants(product, menuProduct, zomatoAddon, index, catalogueMap, compositeProductDetail) {
        var variant = angular.copy(zomatoVariants);
        variant.vendorEntityId = menuProduct.product.productId + "_" + menuProduct.dimension.code + "_" + product.id + "_" + index;
        variant.prices.push(addPrice(getDiscountedPrice(compositeProductDetail.internalDiscount ,getMenuProductPrice(menuProduct), compositeProductDetail.internalDiscountType)));
        var variantProperty = angular.copy(zomatoPropertyValue);
        variantProperty.vendorEntityId = zomatoAddon.vendorEntityId;
        variant.propertyValues.push(variantProperty);
        var modifierGroupList = [];
        var modifiers = getVariantModifierGroup(product, menuProduct, catalogueMap, variant, compositeProductDetail, modifierGroupList);
        if (modifiers[0] != null && modifiers[0] != undefined) {
            modifiers[0].forEach(function (modifier) {
                variant.modifierGroups.push(modifier);
            });
        }
        return variant;
    }

    function getMenuProductPrice(menuProduct) {
        var productPrice = null;
        var product = service.productMap[menuProduct.product.productId];
        product.prices.forEach(function (priceProduct) {
            if (menuProduct.dimension.code == priceProduct.dimension) {
                productPrice = priceProduct.price;
            }
        });
        if(productPrice == null || productPrice == undefined){
            service.errorList.push("Price missing for product :",menuProduct.product.name ,  " , for dimension:",menuProduct.dimension.name);
        }
        return productPrice;
    }

    function getVariantModifierGroup(product, menuProduct, catalogueMap, variant, compositeProductDetail, modifierGroupList) {
        var catalogueMapId = service.getCatalogueMapId(product, menuProduct, catalogueMap);
        var modifiers = [];
        if (catalogueMapId == null || catalogueMapId == undefined || catalogueMap[catalogueMapId] == undefined || catalogueMap[catalogueMapId] == null) {
            service.errorList.push("Combo: " + product.name + " item : " + menuProduct.product.name + " dimension: "
                + menuProduct.dimension.name + " not found in catalog.")
        }
        if (compositeProductDetail.customizable != undefined && compositeProductDetail.customizable != null && compositeProductDetail.customizable) {
            var modGroups = [];
            catalogueMap[catalogueMapId].variants.forEach(function (variant, index) {
                var variantId = variant.vendorEntityId.split('_');
                if (menuProduct.product.productId == parseInt(variantId[0]) && menuProduct.dimension.code == variantId[1]) {
                    var grps = [];
                    variant.modifierGroups.forEach(function (group, ind) {
                        if(!group.vendorEntityId.includes("Recommendations")){
                           var grp = angular.copy(group);
                           grp.order = ind + 1;
                           grps.push(grp);
                        }
                    });
                    modifiers.push(grps);
                }
            });
        }
        return modifiers;
    }

    function addComboModifierGroups(product, compositeProductDetail, addonTag) {
        var modGroup = angular.copy(zomatoModifierGroups);
        modGroup.vendorEntityId = getHeroComboModifierGroupId(product, addonTag);
        modGroup.name = addonTag;//getHeroComboModifierGroupId(product, addonTag);
        modGroup.displayName = addonTag;
        if ($rootScope.heroComboVersion == 'V2') {
            if (compositeProductDetail.minSelection != null && compositeProductDetail.minSelection != "") { modGroup.min = compositeProductDetail.minSelection; }
            if (compositeProductDetail.maxSelection != null && compositeProductDetail.maxSelection != "") { modGroup.max = compositeProductDetail.maxSelection; }
        }
        if (modGroup.min == null) { modGroup.min = 1; }
        if (modGroup.max == null) { modGroup.max = 1; }
        compositeProductDetail.menuProducts.forEach(function (menuProduct, index) {
            var comboAddons = addComboAddOns(menuProduct, index, product, addonTag, compositeProductDetail);
            if(comboAddons != null){
               modGroup.variants.push(comboAddons);
            }

        });
        if(modGroup.max > modGroup.variants.length){
           modGroup.max = modGroup.variants.length;
        }
        if (!modifierGroupIds.includes(modGroup.vendorEntityId)) {
            service.catalog.menu.modifierGroups.push(modGroup);
            modifierGroupIds.push(modGroup.vendorEntityId);
        }
        return modGroup;
    }

    function getDiscountedPrice(discountValue, price,discountType) {
        var comboPrice = 0;
        if (discountValue != null && discountValue !== 0 && discountType == "PERCENTAGE") {
            comboPrice = price - ((price * discountValue) / 100).toFixed(2);
        }else if (discountValue != null && discountValue !== 0 && discountType == "FIXED"){
           comboPrice = price - discountValue;
        }
        else {
            comboPrice = price;
        }
        return parseInt(comboPrice);
    }

    function addComboAddOns(menuProduct, index, product, addonTag, compositeProductDetail) {
        var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = menuProduct.product.productId + "_" + menuProduct.dimension.code + "_" +
            product.id + "_" + addonTag + "_HERO"; //Hero Combo Item
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            if (service.productMap[menuProduct.product.productId] == null) {
                service.errorList.push("Hero Combo: " + product.name + " sub item: " + menuProduct.product.name + " is missing");
            }
            if(service.dineInProductMap[menuProduct.product.productId] == null ||
                                               service.dineInProductMap[menuProduct.product.productId].prices == null
                                               || service.dineInProductMap[menuProduct.product.productId].prices.length == 0){
              return null;
            }
            var addonProduct = createComboAddonProduct(service.productMap[menuProduct.product.productId], 0, addOn.vendorEntityId,
                menuProduct.dimension.code, menuProduct.quantity);
            var heroComboItemPrice = getDiscountedPrice(compositeProductDetail.internalDiscount ,addonProduct.prices[0].price, compositeProductDetail.internalDiscountType);
            var comboSubItemCatalog = addNewCatalogue(addonProduct);
            comboSubItemCatalog.variants = [{
                vendorEntityId: addOn.vendorEntityId,
                propertyValues: [],
                prices: [{service: "delivery", price: heroComboItemPrice}],
                modifierGroups: []
            }];
            service.catalog.menu.catalogues.push(comboSubItemCatalog);
        }
        addOn.order = index;
        return addOn;
    }

    function setSuperCombos() {
        //Logic for price of super combo
        service.unitData.products.forEach(function (product) {
            if (product.subType == 3676 && cataloguesIds.includes(product.id)) {
                addPriceForSuperCombo(product);
                var superCombo = service.addSuperCombo(product);
                var superComboPrice = addPrice($rootScope.combosMainPrice[product.id]);
                superCombo.variants[0].prices = [];
                var priceVal = [];
                priceVal.push(superComboPrice);
                superCombo.variants[0].prices = priceVal;
                superCombo.vendorEntityId = superCombo.vendorEntityId + "_SUPERCOMBO";
                service.catalog.menu.catalogues.push(superCombo);
            }
        });
    }
    function addPriceForSuperCombo(product) {
        $rootScope.combosMainPrice[product.id] = 0;
        product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (detail, index) {
            var map = {};
            var minPriceProduct = null;
            var maxPriceProduct = null;
            detail.menuProducts.forEach(function (prod) {
                var priceProduct = angular.copy(productPriceSuperCombo);
                priceProduct.discount = detail.internalDiscount;
                var type = detail.internalDiscountType;
                priceProduct.discountType = type;
                priceProduct.productId = prod.product.productId;
                var productVal = service.productMap[prod.product.productId];
                productVal.prices.forEach(function (pr) {
                    if (pr.dimension == prod.dimension.code) {
                        priceProduct.originalPrice = pr.price;
                        if (type == "FIXED") {
                            priceProduct.price = pr.price - detail.internalDiscount;
                        } else if (type == "PERCENTAGE") {
                            priceProduct.price = pr.price - (pr.price * (detail.internalDiscount * 0.01));
                        }else{
                            priceProduct.price = pr.price;
                        }
                        if (minPriceProduct == null) {
                            minPriceProduct = angular.copy(priceProduct);
                        }
                        if (maxPriceProduct == null) {
                            maxPriceProduct = angular.copy(priceProduct);
                        }

                        if (priceProduct.price < minPriceProduct.price) {
                            minPriceProduct = angular.copy(priceProduct);
                        }
                        if (priceProduct.price > maxPriceProduct.price) {
                            maxPriceProduct = angular.copy(priceProduct);
                        }
                    }
                });
                var key = product.id + "_" + prod.product.productId + "_" + prod.dimension.code + "_" + index;
                key = key = key.replace(/\s+/g, '');
                map[key] = priceProduct;
            });

            var produckyes = Object.keys(map);

            for (var i = 0; i < produckyes.length; i++) {
                var keyValue = map[produckyes[i]];
                // now subracting min price
                keyValue.beforeSub = angular.copy(keyValue.price);
                keyValue.price = Math.round(keyValue.price - minPriceProduct.price);
                if(keyValue.price == null || keyValue.price < 0){
                    service.errorList.push("Price of product " + service.productMap[keyValue.productId].name  + " is missing");
                }

                $rootScope.priceMapForSuperCombo[produckyes[i]] = keyValue;
            }
            console.log(minPriceProduct.price);
            if([].indexOf(product.id) >-1){
                $rootScope.combosMainPrice[product.id] = Math.round($rootScope.combosMainPrice[product.id] + maxPriceProduct.price);
            }else{
                $rootScope.combosMainPrice[product.id] = Math.round($rootScope.combosMainPrice[product.id] + minPriceProduct.price);
            }
        });
        console.log($rootScope.combosMainPrice);
    }

    function addSuperCombo(product) {
        var comboCatalogue = angular.copy(zomatoCatalogues);
        comboCatalogue.vendorEntityId = (product.id).toString();
        comboCatalogue.name = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name, product.id));
        comboCatalogue.description = getProductDescription(product.description, product.name);
        comboCatalogue.inStock = true;
        var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
        if (service.addPackaging === true && service.metadata.packagingType !== "FIXED" &&  product.billType != "MRP") {
            comboCatalogue.charges.push(addCharge(taxCode,product.id));
        }
        if(service.productPackagingMappings != null && service.productPackagingMappings[product.id] != null
                         && service.productPackagingMappings[product.id].packagingValue ==0){
                             catalogue.charges = [];
       }
        comboCatalogue.taxGroups = [{ slug: getTaxGroupId(service.taxMap[taxCode]) }];
        var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
        var tag = str.toString().replace("_", "-");
        comboCatalogue.tags = [tag];
        comboCatalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
        comboCatalogue.variants = [];
        comboCatalogue.variants.push(addSuperComboVariants(product));
        comboCatalogue.properties = [];
        if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null ||
            service.productImages[product.id].gridLow.url == null) {
            if (AppUtil.getEnvType() == "PROD") {
                service.errorList.push("Product image missing for: " + product.name);
            }
        } else {
            comboCatalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
        }
        return comboCatalogue;
    }

    function addServices() {
        var catalogService = angular.copy(zomatoServices);
        catalogService.service = "delivery";
        return catalogService;
    }

    function addSelections(product) {
        var groups = [];
        product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (detail, index) {
            var selections = angular.copy(zomatoSelections);
            selections.vendorEntityId = product.id + "_item_" + index;
            selections.title = detail.name;
            selections.maxSelections = 1;
            selections.minSelections = 1;
            selections.maxSelectionsPerItem = 1;
            if (detail.discount == null) {
                console.log("Super combo: " + product.name + ", discount Value is null");
                service.errorList.push("Super combo: " + product.name + ", discount Value is null");
                return;
            } else {
                selections.discountValue = detail.discount;
            }
            selections.selectionEntities = addSelectionEntities(detail, product);
            groups.push(selections);
        });
        return groups;
    }

    function addSuperComboVariants(product) {
        var variants = angular.copy(zomatoVariants);
        variants.vendorEntityId = product.id + "_None_0";
        variants.propertyValues = [];
        var variantPrices = [];
        variantPrices.push(addPrice(product.prices[0].price))
        variants.prices = variantPrices;
        variants.modifierGroups = addZomatoModifierGroups(product);
        if(service.catalog.menu.modifierGroups == null){
            service.catalog.menu.modifierGroups = [];
        }
        var groups = addModifierGroupForSuperCombos(product);
        service.catalog.menu.modifierGroups = service.catalog.menu.modifierGroups.concat(groups);
        return variants;
    }

    function addZomatoModifierGroups(product) {
        var groups = [];
        product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (detail, index) {
            var modifiers = angular.copy(zomatoModifierGroup);
            modifiers.vendorEntityId = product.id + "_item_" + index;
            modifiers.order = index;
            groups.push(modifiers);
        });
        return groups;
    }

    function addModifierGroupForSuperCombos(product) {
        var groups = [];
        product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (detail, index) {
            var modifiers = angular.copy(zomatoModifierGroups);
            modifiers.vendorEntityId = product.id + "_item_" + index;
            modifiers.displayName = detail.name;
            modifiers.name = detail.name;
            modifiers.max = detail.maxSelection ? detail.maxSelection : 1;
            modifiers.min = detail.minSelection ? detail.minSelection : 1;
            modifiers.maxSelectionsPerItem = modifiers.max;
            modifiers.variants = addModifierVariantForSuperCombo(detail, product,index);
            if (modifiers.variants.length == 0) {
                service.errorList.push("For Item " + detail.name + ", prices for sub-items is not mapped.");
                return;
            }
            groups.push(modifiers);
        });
        return groups;
    }

    function addSelectionEntities(detail, product) {
        var groups = [];
        detail.menuProducts.forEach(function (item) {
            var itemId = item.product.productId;
            if (item.quantity > 1) {
                service.errorList.push("super combo " + product.name + " item: " + detail.name + "quantity is " +
                    item.quantity + " allowed quantity is one");
            }
            service.catalog.menu.catalogues.forEach(function (catalogue) {
                if (catalogue.vendorEntityId === itemId.toString()) {
                    catalogue.variants.forEach(function (variant) {
                        if (variant.vendorEntityId.indexOf(item.dimension.name)) {
                            var selectionEntity = angular.copy(zomatoSelectionEntities);
                            selectionEntity.variantVendorEntityId = variant.vendorEntityId;
                            selectionEntity.catalogueVendorEntityId = catalogue.vendorEntityId;
                            groups.push(selectionEntity);
                        }
                    });
                }
            });
        });
        return groups;
    }

    function addModifierVariantForSuperCombo(detail, product,groupIndex) {
        var modifierVariants = [];
        var variants = {};
        var unsortedKey = {};
        detail.menuProducts.forEach(function (item, index) {
            var itemId = item.product.productId;
            if (item.quantity > 1) {
                service.errorList.push("super combo " + product.name + " item: " + detail.name + "quantity is " +
                    item.quantity + " allowed quantity is one");
            }
            var variantEntity = angular.copy(zomatoVariant);
            //making vendorEntityId from dimension mapped
            var key = product.id + "_" + item.product.productId + "_" + item.dimension.code.trim();
            key = key = key.replace(/\s+/g, '');
            var keyValue = key + "_" + groupIndex;
            variantEntity.vendorEntityId = key + "_" + index + "_" + groupIndex  ;
            var nameString = getAddonString(item,variantEntity);

            variantEntity.order = index;
            variants[key] = variantEntity;
            unsortedKey[key] = $rootScope.priceMapForSuperCombo[keyValue].price;
            //make catalogue obj and push to menu catalogues.
            var comboCatalogue = addModifierVariantInCatalogue(variantEntity, product, item, nameString,groupIndex);
            if(!cataloguesIds.includes(comboCatalogue.vendorEntityId)){
                     service.catalog.menu.catalogues.push(comboCatalogue);
                     cataloguesIds.push(comboCatalogue.vendorEntityId);
            }
        });
        var keys = Object.keys(unsortedKey);
        // Sort the array of keys based on the values of the object
        if([].indexOf(product.id) >-1){
           keys.sort(function (a, b) {
                                  return   unsortedKey[b] - unsortedKey[a];
           });
        }else{
           keys.sort(function (a, b) {
                       return unsortedKey[a] - unsortedKey[b];
           });
        }


        console.log(keys);
        keys.forEach(function (data) {
            var key = Object.keys(variants);
            key.forEach(function (val) {
                if (val == data) {
                    modifierVariants.push(variants[data]);
                }
            });
        });
        return modifierVariants;
    }

    function getAddonString(item,variantEntity){
        var nameString = "";
        if (item.product.productId == 10) {
            service.metadata.desiChaiCustomProfiles.map(function (profile) {
                if (profile.superComboProduct == true) {
                    nameString = nameString + "( ";
                    variantEntity.vendorEntityId = variantEntity.vendorEntityId + "_" + profile.profileName;
                    var listLength = profile.addons.length;
                    if(listLength == 1){
                        nameString = nameString + profile.addons[0].name;
                    }
                    else{
                        for (var list of profile.addons) {
                           nameString = nameString + list.name;
                           if (profile.addons.length <= listLength) {
                               nameString = nameString + ",";
                               listLength--;
                           }
                        }
                    }
                    nameString = nameString + " )";
                }
            });
        }
        return nameString;
    }

    function addModifierVariantInCatalogue(variantEntity, product, item, nameString,index) {
        var comboCatalogue = angular.copy(zomatoCatalogues);
        comboCatalogue.vendorEntityId = variantEntity.vendorEntityId;
        var productVal = service.productMap[item.product.productId];
        productVal.prices.forEach(function (pr){
            if(pr.dimension == item.dimension.code){
                var dimensionName = pr.aliasProductName != null ? pr.aliasProductName : productVal.name;
                comboCatalogue.name = dimensionName + getDimensionName(pr.dimension,productVal.subType);
            }
        });
        if(comboCatalogue.name == null){
            var alias = getProductAlias(productVal);
            comboCatalogue.name = alias != null ? alias : productVal.name;
        }
        comboCatalogue.name = comboCatalogue.name + nameString;
        comboCatalogue.description = getProductDescription(productVal.description, productVal.name);
        comboCatalogue.inStock = true;
        var str = [(productVal.attribute != null ? productVal.attribute.toLowerCase() : "veg")];
        var tag = str.toString().replace("_", "-");
        comboCatalogue.tags = [tag];
        comboCatalogue.tags.push((productVal.billType === "NET_PRICE" && service.taxMap[productVal.taxCode].state.cgst == 2.5) ? "services" : "goods");
        comboCatalogue.taxGroups = [];
        comboCatalogue.preparationTime = getPrepTime(product.prepTime);
        comboCatalogue.variants = addSuperComboVariantsForCatalogues(comboCatalogue, product,item,index);
        if (service.productImages[item.product.productId] == null || service.productImages[item.product.productId].gridLow == null
            || service.productImages[item.product.productId].gridLow.url == null) {
            console.log("Product image url missing for product id ::: {}",item.product.productId);
        } else {
            comboCatalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[item.product.productId].gridLow.url;
        }
        comboCatalogue.properties = [];
        return comboCatalogue;
    }

    function addSuperComboVariantsForCatalogues(comboCatalogue, product,item,index) {
        var catalogueVariants = [];
        var comboVariantCatalogue = angular.copy(zomatoVariants);
        comboVariantCatalogue.propertyValues = [];
        comboVariantCatalogue.modifierGroups = [];
        comboVariantCatalogue.vendorEntityId = comboCatalogue.vendorEntityId;
        var key = product.id + "_" + item.product.productId + "_" + item.dimension.code + "_" + index;
        key = key = key.replace(/\s+/g, '');
        var pricesForVariant = [];
        pricesForVariant.push(addPrice($rootScope.priceMapForSuperCombo[key].price));
        comboVariantCatalogue.prices = pricesForVariant;
        catalogueVariants.push(comboVariantCatalogue);
        return catalogueVariants;
    }

    function getProductAlias(product) {
        var alias = null;
        product.prices.forEach(function (price) {
            if (price.aliasProductName != null) {
                alias = price.aliasProductName;
            }
        });
        return alias;
    }

    function getProductName(productName) {
        if (productName.indexOf("Kulhad Wali") >= 0) {
            return productName.trim().replace("Kulhad Wali", "");
        } else if (productName.indexOf("Kulhad") >= 0 && productName.indexOf("Kulhad Chai") < 0) {
            return productName.trim().replace("Kulhad", "");
        } else {
            return productName.trim();
        }
    }

    function getProductDescription(productDescription, productName) {
        // console.log(productName, productDescription);
        if (productDescription == null) {
            return productDescription;
        }
        if (productName.indexOf("Kulhad Chai") < 0 && productDescription.indexOf("served in a Kulhad") >= 0) {
            return productDescription.trim().replace("served in a Kulhad", "");
        } else {
            return productDescription.trim();
        }
    }

    function dropUpsellingFromVariants(variant) {
        var modGroups = [];
        variant.modifierGroups.forEach(function (modGroup) {
            if (modGroup.vendorEntityId.indexOf("_Recommendations") < 0) {
                modGroups.push(modGroup);
            }
        });
        variant.modifierGroups = modGroups;
    }

    function updateDesiChaiAsPerCustomProfiles(catalog, catalogueMap, dimensionName) {
        if (catalog.vendorEntityId === "10" || catalog.vendorEntityId.includes("10_")) {
            service.metadata.desiChaiCustomProfiles.forEach(function (profile) {
               /* if(profile.superComboProduct == true){
                   return;
                }*/
                if (profile.profileType === "CUSTOMIZATION") {
                    setCustomizationTypeProfile(catalog, profile, service.catalog.menu);
                }
                if (profile.profileType === "PRODUCT") {
                    var catalogEntry = angular.copy(catalog);
                    setCustomizationTypeProfile(catalogEntry, profile, service.catalog.menu);
                    catalogEntry.name = profile.productName;
                    if (dimensionName != null) {
                        catalogEntry.name = catalogEntry.name + " (" + dimensionName + ") ";
                    }
                    if (profile.productDescription != null) {
                        catalogEntry.description = profile.productDescription;
                    }
                    var profileName = profile.profileName.replaceAll(" ", "");
                    catalogEntry.vendorEntityId = catalogEntry.vendorEntityId + "_" + profileName;
                    catalogEntry.properties.forEach(function (prop) {
                        prop.vendorEntityId = prop.vendorEntityId + "_" + profileName;
                        prop.propertyValues.forEach(function (propVal) {
                            propVal.vendorEntityId = propVal.vendorEntityId + "_" + profileName;
                        });
                    });
                    var addonIds = [];
                    profile.addons.forEach(function (addon) {
                        addonIds.push(addon.id + "");
                    });
                    catalogEntry.variants.forEach(function (variant) {
                        variant.vendorEntityId = variant.vendorEntityId + "_" + profileName;
                        variant.propertyValues.forEach(function (propVal) {
                            propVal.vendorEntityId = propVal.vendorEntityId + "_" + profileName;
                        });
                        if(addonIds.length > 0) {
                            variant.modifierGroups.forEach(function (modGroup) {
                                if (modGroup.vendorEntityId.toLowerCase().includes("addon") && !modGroup.vendorEntityId.toLowerCase().includes("paid_addon")) {
                                    modGroup.vendorEntityId = modGroup.vendorEntityId + "_" + profileName;
                                }
                            });
                        }
                    });
                    service.catalog.menu.catalogues.push(catalogEntry);
                    catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;
                }
            });
        }
    }

    function setCustomizationTypeProfile(catalog, profile, menu) {
        catalog.properties.forEach(function (property) {
            var newValues;
            if (profile.dimensionType != null && property.name.toLowerCase().includes("size")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.vendorEntityId.toLowerCase().includes(profile.dimensionType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.vendorEntityId.toLowerCase().includes(profile.dimensionType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
            if (profile.milkType != null && property.name.toLowerCase().includes("milk")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.value.toLowerCase().includes(profile.milkType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.value.toLowerCase().includes(profile.milkType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
            if (profile.sugarType != null && property.name.toLowerCase().includes("sugar")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.value.toLowerCase().includes(profile.sugarType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.value.toLowerCase().includes(profile.sugarType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
            if (profile.pattiType != null && property.name.toLowerCase().includes("patti")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.value.toLowerCase().includes(profile.pattiType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.value.toLowerCase().includes(profile.pattiType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
        });
        var addonIds = [];
        profile.addons.forEach(function (addon) {
            addonIds.push(addon.id + "");
        });
        if(profile.addons.length > 0) {
            if (profile.profileType === "CUSTOMIZATION") {
                menu.modifierGroups.forEach(function (modGroup) {
                    catalog.variants[0].modifierGroups.forEach(function (modG) {
                        if (modG.vendorEntityId.toLowerCase().includes("addon") && !modG.vendorEntityId.toLowerCase().includes("paid_addon")) {
                            if (modGroup.vendorEntityId === modG.vendorEntityId) {
                                var newAddons = [];
                                newAddons = newAddons.concat(modGroup.variants.filter(function (modVar) {
                                    return addonIds.includes(modVar.vendorEntityId);
                                }));
                                newAddons = newAddons.concat(modGroup.variants.filter(function (modVar) {
                                    return !addonIds.includes(modVar.vendorEntityId);
                                }));
                                modGroup.variants = newAddons;
                            }
                        }
                    });
                });
            }
            if (profile.profileType === "PRODUCT") {
                menu.modifierGroups.forEach(function (modGroup) {
                    catalog.variants[0].modifierGroups.forEach(function (modG) {
                        if (modG.vendorEntityId.toLowerCase().includes("addon") && !modG.vendorEntityId.toLowerCase().includes("paid_addon")) {
                            if (modGroup.vendorEntityId === modG.vendorEntityId) {
                                var newModGroup = angular.copy(modGroup);
                                newModGroup.vendorEntityId = newModGroup.vendorEntityId + "_" + profile.profileName.replaceAll(" ", "");
                                var newAddons = [];
                                newAddons = newAddons.concat(newModGroup.variants.filter(function (modVar) {
                                    return addonIds.includes(modVar.vendorEntityId);
                                }));
                                 //  newAddons = newAddons.concat(newModGroup.variants.filter(function (modVar) {
                                                                //    return !addonIds.includes(modVar.vendorEntityId);
                                                                //}));
                                newModGroup.min = addonIds.length;
                                newModGroup.max = addonIds.length;
                                newModGroup.variants = newAddons;
                                menu.modifierGroups.push(newModGroup);
                            }
                        }
                    });
                });
            }
        }
    }

    function checkGstTags() {
        console.log("%cCatalogs with missing GST tags:::::\n::::::::::::::::::::::::::::::", "color:red; font-size:30px");
        service.catalog.menu.catalogues.forEach(function (catalog) {
            if(service.productMap[parseInt(catalog.vendorEntityId)]!=null && service.productMap[parseInt(catalog.vendorEntityId)].classification == "MENU") {
                if (catalog.tags == null || catalog.tags.length == 0) {
                    console.log(catalog.vendorEntityId, catalog.name);
                } else {
                    var found = false;
                    catalog.tags.forEach(function (tag) {
                        if(!found && (tag == "goods" || tag == "services")) {
                            found = true;
                        }
                    });
                    if(!found) {
                        console.log(catalog.vendorEntityId, catalog.name);
                    }
                }
            }
        });
        console.log("%cCatalogs with missing GST tags END:::::\n::::::::::::::::::::::::::::::", "color:red; font-size:30px");
    }

    /////////////////////////////////////////////////////////
    ///                  JARVIS                           ///
    /////////////////////////////////////////////////////////

    function getIngredientVariantDetailId(product, ingredientVariantDetail) {
        return product.id + "_" + ingredientVariantDetail.alias.replace(/[ ]/g, "_")
    }

    function getIngredientProductDetailId(product, ingredientProductDetail) {
        return product.id + "_" + ingredientProductDetail.product.name.replace(/[ ]/g, "_");
    }

    function getMilkVariantDetailId(product, milkValue) {
        return product.id + "_" + milkValue.replace(/[ ]/g, "");
    }

    function getMilkPaidAddonVariantId(product,price) {
        return product.id + "_" + "MILK_TYPE" + "_" + price.recipe.options.filter(function(option){
          return   service.specialMilkPaidAddonList.indexOf(option.id) >-1
        })[0].id;
    }

    function getRegularMilkPaidAddonVariantId(product) {
        return product.id + "_" + "MILK_TYPE" + "_" + "REGULAR";
    }

    function getDimensionId(product, dimension) {
        return product.id + "_" + dimension.replace(/[ ]/g, "");
    }

    function getModifierGroupId(product) {
        return product.id + "_Addons";
    }

    function getPaidModifierGroupId(product, dimension) {
        return product.id + "_" + dimension + "_Paid_Addons";
    }

    function getHeroComboModifierGroupId(product, addonTag) {
        return product.id + "_Addons_" + addonTag;
    }

    function getRecommendationModifierGroupId(product) {
        return product.id + "_Recommendations";
    }

    function getCombinations(arr, result) {
        var n = arr.length;
        var indices = [];
        for (var i = 0; i < n; i++) {
            indices[i] = 0;
        }
        while (true) {
            var resItem = "";
            for (var i = 0; i < n; i++) {
                resItem = resItem + arr[i][indices[i]] + " ";
            }
            result.push(resItem);
            var next = n - 1;
            while (next >= 0 && (indices[next] + 1 >= arr[next].length)) {
                next -= 1;
            }
            if (next < 0) {
                return;
            }
            indices[next] += 1;
            for (var i = next + 1; i < n; i++) {
                indices[i] = 0;
            }
        }
    }

    function createAddonProduct(addon) {
		var name = null;
		if (service.productMap[addon.product.productId] == null || service.productMap[addon.product.productId] == undefined) {
			name = addon.product.name;
		} else {
			name = getProductName(service.productMap[addon.product.productId].name);
		}
        return {
            id: addon.product.productId,
            name: name,
            type: addon.product.type,
            subType: addon.product.subType,
            classification: addon.product.classification,
            taxCode: "00009963",
            billType: "NET_PRICE",
            prices: [
                {
                    dimension: addon.dimension.code,
                    price: 0,
                    recipe: null
                }
            ]
        }
    }

    function createPaidAddonProduct(paidAddon) {
        var name = null;
        if (service.productMap[paidAddon.productId] == null || service.productMap[paidAddon.productId] == undefined) {
            name = paidAddon.name;
        } else {
            name = getProductName(service.productMap[paidAddon.productId].name);
        }
        var product = service.productMap[paidAddon.productId];
        product.name = name;
        product.billType = "NET_PRICE";
        return product;
    }

/*    function createPaidAddonProductForSugar(paidAddon,id) {
        var name = null;
        if (service.productMap[paidAddon.productId] == null || service.productMap[paidAddon.productId] == undefined) {
            name = paidAddon.name;
        } else {
            name = getProductName(service.productMap[paidAddon.productId].name);
        }
        var product = service.productMap[paidAddon.productId];
        product.name = name;
        product.billType = "NET_PRICE";
        product.productId = id;
        product.id = id;
        return product;
    }*/

    function createNoAddonProduct(productName, productId) {
        return {
            id: productId,
            name: productName,
            type: 12,
            subType: 1202,
            classification: 'FREE_ADDON',
            taxCode: "00009963",
            billType: "NET_PRICE",
            prices: [
                {
                    dimension: 'Regular',
                    price: 0,
                    recipe: null
                }
            ]
        }
    }

    function createComboAddonProduct(product, discount, vendorEntityId, dimension, quantity,fixedMealPriceAvailable) {
        var price = product.prices[0].price;
        if (dimension != null) {
            product.prices.forEach(function (priceObj) {
                if (priceObj.dimension === dimension) {
                    price = priceObj.price;
                }
            });
        }
        if(fixedMealPriceAvailable !==undefined && fixedMealPriceAvailable){
            price = 0 ;
        }
        if (quantity != null && quantity > 1) {
            price = price * quantity;
        }
        if (discount != null && discount > 0) {
            price = price - ((price * discount) / 100).toFixed(2);
        }
        var alias = getProductAlias(product);
        var name = alias != null ? alias : product.name;
        return {
            id: vendorEntityId,
            name: name + ((quantity != null && quantity > 1) ? (" x " + quantity) : ""),
            type: product.type,
            subType: product.subType,
            attribute: product.attribute,
            //classification: "PAID_ADDON",
            classification: "COMBO_ADDON",
            taxCode: product.taxCode,
            billType: product.billType,
            prices: [
                {
                    dimension: dimension != null ? dimension : product.prices[0].dimension,
                    price: price,
                    recipe: null,
                }
            ]
        }
    }

    function getTaxGroupId(tax) {
        return "GST_D_P_" + parseFloat(parseFloat(tax.state.cgst) * 2).toFixed(2);
    }

    function getDimensionName(dimension,productSubType) {
        if (dimension == null || dimension == "None" || dimension == "none" || productSubType == 901) {
            return "";
        }else if(dimension == "1 Pack"){
                     return  " (" + dimension + ") "
        } else {
            return " (" + dimension.replace(/([A-Z])/g, ' $1').substring(1) + ") ";
        }
    }

    function getDimensionDescription(dimension, dimensionDescriptor) {
        if (dimensionDescriptor) {
            return dimensionDescriptor;
        }
        var desc = "";
        switch (dimension) {
            case "MiniKetli":
                desc = "Serves 2, 250ml";
                break;
            case "ChotiKetli":
                desc = "Serves 4, 400ml";
                break;
            case "BadiKetli":
                desc = "Serves 10, 1000ml";
                break;
        }
        return desc;
    }

    function getPrepTime(minutes) {
        if (minutes > 0 && minutes < 5) {
            return "0to5min";
        } else if (minutes >= 5 && minutes < 10) {
            return "5to10min";
        } else if (minutes >= 10 && minutes < 15) {
            return "10to15min";
        } else if (minutes >= 15 && minutes < 20) {
            return "15to20min";
        } else if (minutes >= 20 && minutes < 25) {
            return "20to25min";
        } else if (minutes >= 25 && minutes < 30) {
            return "25to30min";
        } else if (minutes >= 30 && minutes < 35) {
            return "30to35min";
        } else if (minutes >= 35 && minutes < 40) {
            return "35to40min";
        } else if (minutes >= 40 && minutes < 45) {
            return "40to45min";
        } else if (minutes >= 45 && minutes < 50) {
            return "45to50min";
        } else if (minutes >= 50 && minutes < 55) {
            return "50to55min";
        } else if (minutes >= 55 && minutes < 60) {
            return "55to60min";
        } else {
            return "0to5min"
        }
    }

    return service;
}]);
