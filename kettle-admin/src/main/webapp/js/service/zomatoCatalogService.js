/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.service('zomatoCatalogService', ['$cookieStore', 'APIJson', '$http', function ($cookieStore, APIJson, $http) {

    var service = {};


    service.prepareCatalog = prepareCatalog;
    service.addonsList = [];
    service.addonsMap = {};

    var zomatoMenu = {
        "outlet_id": "",
        "menu":
            {
                "taxes": [],
                "charges": [], //zomatoCharge
                "order_additional_charges":
                    [{
                        "order_type": "DELIVERY",
                        "charges": []
                    }],
                "categories": [] //zomatoCategory
            },
        "restaurant_offers": [] //zomatoOffer
    };

    var zomatoCharge = {
        "charge_id": null,
        "charge_name": null,
        "charge_type": null,
        "charge_value": null,
        "charge_is_active": null,
        "applicable_on": null,
        "charge_always_applicable": null,
        "charge_applicable_below_order_amount": null,
        "has_tier_wise_values": null,
        "tier_wise_values": [
            {
                "tier_id": null,
                "charge_value": null,
                "charge_applicable_below_order_amount": null,
                "charge_always_applicable": null
            },
            {
                "tier_id": null,
                "charge_value": null,
                "charge_applicable_below_order_amount": null,
                "charge_always_applicable": null
            }
            ,
            {
                "tier_id": null,
                "charge_value": null,
                "charge_applicable_below_order_amount": null,
                "charge_always_applicable": null
            }
        ],
        "charge_taxes":
            [{
                "order_type": "",
                "taxes": []
            }]
    };

    var zomatoCategory = {
        "category_id": null,
        "category_name": null,
        "category_description": null,
        "category_is_active": null,
        "category_image_url": null,
        "category_order": null,
        "category_schedules": [
            {
                "schedule_name": null,
                "schedule_day": null,
                "schedule_time_slots": [
                    {
                        "start_time": null,
                        "end_time": null
                    }
                ]
            }
        ],
        "has_subcategory": null,
        "items": null,
        "subcategories": [] //zomatoSubcategory
    };

    var zomatoSubcategory = {
        "subcategory_id": null,
        "subcategory_name": null,
        "subcategory_description": null,
        "subcategory_is_active": null,
        "subcategory_image_url": null,
        "subcategory_order": null,
        "items": []
    };

    var zomatoItem = {
        "item_id": null,
        "item_name": null,
        "item_unit_price": null,
        "item_final_price": null,
        "item_short_description": null,
        "item_long_description": null,
        "item_is_active": null,
        "item_is_default": null,
        "item_image_url": null,
        "item_in_stock": null,
        "item_order": null,
        "item_is_recommended": null,
        "item_is_treats_active": null,
        "item_is_bogo_active": null,
        //"item_tags": null,
        "groups": [],
        "item_taxes": [],
        "item_charges": [],
        "item_discounts": null
    };

    var zomatoGroup = {
        "group_id": null,
        "group_name": null,
        "group_description": null,
        "group_minimum": null,
        "group_maximum": null,
        "group_is_active": 1,
        "items": []
    };

    var zomatoTax = {
        "tax_id": null,
        "tax_name": null,
        "tax_type": "PERCENTAGE",
        "tax_value": null,
        "tax_is_active": 1
    };

    var zomatoOffer = {
        "offer_id": null,
        "start_date": null,
        "end_date": null,
        "timings": [{
            "start_time": null,
            "end_time": null
        }],
        "offer_type": null,
        "discount_type": null,
        "min_order_amount": null,
        "discount_value": null,
        "is_active": null
    };

    function prepareAddonsMap(unitData) {
        service.addonsMap = {};
        unitData.products.map(function (prod) {
            if (prod.type == 5 && prod.subType == 501) {
                service.addonsMap[prod.id] = prod.prices[0].recipe.addons;
            }
        });
    }

    function prepareCatalog(unitId, unitData, metadata, menuSequence, brand, addPackaging, productImages) {
        prepareAddonsMap(unitData);
        var catalog = angular.copy(zomatoMenu);
        service.zomatoTaxMap = {};
        if (unitId !== null && unitData !== null) {
            catalog.outlet_id = unitId;
            catalog.menu.categories = [];
            var productMap = createProductMap(unitData);
            setMenuCategories(catalog, menuSequence, unitData, productMap, brand);
            sortProductPrices(unitData);
            var catHighestIndex = 0;
            var otherCat = null;
            menuSequence.productGroupSequences.map(function (cat) {
                if(catHighestIndex < cat.groupIndex) {
                    catHighestIndex = cat.groupIndex;
                }
            });
            Object.keys(productMap).map(function (key) {
                var prod = productMap[key];
                if(prod.selected != true) {
                    if (prod.classification === "MENU" && prod.billType !== "ZERO_TAX" && prod.type !== 12 &&
                        [3082, 11, 12, 50, 1292, 1293, 1294].indexOf(prod.id) < 0) {
                        if(otherCat != null) {
                            prod.productIndex = key;
                            otherCat.items.push(addProduct(prod, unitData, null, null, null, null, null, null, false, brand, productImages));
                        } else {
                            var cat = {groupId:777, groupName:"Others", groupIndex:catHighestIndex+1, subGroups:[]};
                            otherCat = addNewCategory(cat, unitData, productMap, brand, productImages);
                        }
                    }
                }
            });
            if(otherCat != null && otherCat.items != null && otherCat.items.length > 0) {
                catalog.menu.categories.push(otherCat);
            }
        }
        catalog.menu.taxes = Object.values(service.zomatoTaxMap);
        catalog.menu.charges = [];
        catalog.menu.order_additional_charges = [];
        addCatalogCharges(unitData, catalog.menu.charges, catalog.menu.order_additional_charges, metadata, addPackaging);
        //filterMiniKetli(catalog.menu);
        setQVMPrices(catalog.menu, unitData);
        console.log(JSON.stringify(catalog));
        return catalog;
    }

    function createProductMap(unitData) {
        var productMap = {};
        unitData.products.map(function (product) {
            productMap[product.id] = product;
        });
        return productMap;
    }

    function sortProductPrices(unitData) {
        unitData.products.map(function (product) {
            product.prices.sort(function (a, b) {
                return a.price - b.price;
            });
        })
    }

    function setMenuCategories(catalog, menuSequence, unitData, productMap, brand) {
        menuSequence.productGroupSequences.map(function (cat) {
            catalog.menu.categories.push(addNewCategory(cat, unitData, productMap, brand));
        });
    }

    function addNewCategory(cat, unitData, productMap, brand, productImages) {
        var category = angular.copy(zomatoCategory);
        category.category_id = cat.groupId;
        category.category_name = cat.groupName;
        category.category_description = "";
        category.category_is_active = 1;
        category.category_image_url = "";
        category.category_order = cat.groupIndex;
        category.category_schedules = []; //TODO fix this
        category.items = [];
        //category.items.push(addProduct(product, unitData, null, null, null, null, null, null, false, brand));
        cat.subGroups.map(function (subCat) {
            category.subcategories.push(addNewSubCategory(subCat, unitData, productMap, brand, productImages));
        });
        category.has_subcategory = (cat.subGroups.length > 0) ? 1 : 0;
        return category;
    }

    function addNewSubCategory(subCat, unitData, productMap, brand, productImages) {
        var subCategory = angular.copy(zomatoSubcategory);
        subCategory.subcategory_id = subCat.groupId;
        subCategory.subcategory_name = subCat.groupName;
        subCategory.subcategory_description = "";
        subCategory.subcategory_is_active = 1;
        subCategory.subcategory_image_url = "";
        subCategory.subcategory_order = subCat.groupIndex;
        subCategory.items = [];
        subCat.productSequenceList.map(function (prodSequence) {
            var productObj = productMap[prodSequence.product.id];
            if(productObj != null) {
                productObj.selected = true;
                productObj.productIndex = prodSequence.productIndex;
                productMap[prodSequence.product.id] = productObj;
                subCategory.items.push(addProduct(productObj, unitData, null, null, null, null, null, null, false, brand, productImages))
            }
        });
        return subCategory;
    }

    function addProduct(product, unitData, price, id, name, description, taxCode, isDefault, isComboCustomisation, brand, productImages) {
        var item = angular.copy(zomatoItem);
        item.item_id = id == null ? product.id + "" : id + "";
        item.item_name = name == null ? product.name : name;
        //item.item_unit_price = price == null ? product.prices[0].price : price;
        item.item_unit_price = (price == null) ? (product.prices.length > 1 ? 0 : product.prices[0].price) : price;
        item.combo_reduced_price = 0;
        item.item_short_description = description == null ? product.description : description;
        item.item_long_description = description == null ? product.description : description;
        item.item_is_active = 1;
        item.item_is_default = isDefault == null ? 0 : isDefault;
        /*var imgUrl = product != null && !isComboCustomisation ? "https://cafes.chaayos.com/img/products/" + product.id + ".jpg" : "";
        if(brand.brandId != 1) {
            imgUrl = product != null && !isComboCustomisation ? "https://cafes.chaayos.com/img/"+brand.brandCode+"/products/" + product.id + ".jpg" : "";
        }*/
        /*if (product != null && product.type === 8) { //for combo
            imgUrl = "";
        }*/
        item.item_image_url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + productImages[product.id].gridLow;
        item.item_in_stock = 1;
        item.item_order = (product != null ? product.productIndex : 1);
        item.item_is_recommended = 0;
        item.item_is_treats_active = 0;
        item.item_is_bogo_active = 0;
        item.item_tags = [];
        /*if(product != null && product.attribute === "NON_VEG"){ //will be updated from tag management system
            item.item_tags.push(2);
        } else {
            item.item_tags.push(1);
        }*/
        item.item_charges = [];
        item.item_discounts = [];
        var taxObj = {order_type: "DELIVERY", taxes: []};
        if (unitData != null) {
            unitData.taxes.map(function (tax) {
                var code = taxCode == null ? product.taxCode : taxCode;
                if (product.type === 8 || product.type === 43) { //code for combo
                    //console.log(product.name);
                    var firstMenuItemId = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId;
                    unitData.products.map(function (prod) {
                        if (prod.id === firstMenuItemId) {
                            code = prod.taxCode;
                        }
                    })
                }
                if (tax.taxCode === code) {
                    addZomatoTaxObj(tax, "cgst", taxObj);
                    addZomatoTaxObj(tax, "sgst", taxObj);
                }
            });
        }
        item.item_taxes.push(taxObj);
        item.item_final_price = item.item_unit_price;
        if (item.item_unit_price > 0) {
            item.item_taxes[0].taxes.map(function (itax) {
                var taxCode = itax.substr(0, itax.indexOf("_"));
                var taxName = itax.substr(itax.indexOf("_") + 1);
                unitData.taxes.map(function (tax) {
                    if (tax.taxCode == taxCode) {
                        item.item_final_price = item.item_final_price + ((tax.state[taxName] / 100) * item.item_unit_price);
                    }
                })
            });
        }
        item.groups = [];
        if (product != null) {
            if (isComboCustomisation === false) {
                addDimensionGroup(product, item.groups, brand)
            }
            addIngredientVariants(product, item.groups, brand);
            addIngredientProducts(product, item.groups, brand);
            addCompositeProducts(product, item.groups, unitData, brand);
            addAddons(product, item.groups, brand);
        }
        return item;
    }

    function addZomatoTaxObj(tax, taxName, taxObj) {
        var key = tax.taxCode + "_" + taxName;
        if (service.zomatoTaxMap[key] == null) {
            service.zomatoTaxMap[key] = prepareTaxObj(key, tax, taxName);
            if (tax.taxCode == "COMBO") {
                service.zomatoTaxMap[key].tax_value = 2.5
            }
        }
        taxObj.taxes.push(key);
    }

    function prepareTaxObj(key, tax, taxName) {
        var taxObj = angular.copy(zomatoTax);
        taxObj.tax_id = key;
        taxObj.tax_name = taxName.toUpperCase();
        taxObj.tax_value = tax.state[taxName];
        return taxObj;
    }

    function addDimensionGroup(product, groups, brand) {
        if (product.prices.length > 1) {
            var group = angular.copy(zomatoGroup);
            group.group_id = product.id + "_size";
            group.group_name = "Size options";
            group.group_description = "";
            group.group_minimum = 1;
            group.group_maximum = 1;
            product.prices.map(function (price, index) {
                //product, taxes, price, id, name, description, taxCode
                var p = price.price; // - product.prices[0].price;
                var id = product.id + "_" + price.dimension.substr(0, 1);
                var name = price.dimension.replace(/([A-Z])/g, ' $1');
                var description = getDimensionDescription(price.dimension);
                name += " (" + description + ")";
                group.items.push(addProduct(null, null, p, id, name, description, null, index === 0 ? 1 : 0, false, brand));
            });
            groups.push(group);
        }
    }

    function getDimensionDescription(dimension) {
        var desc = "";
        switch (dimension) {
            case "MiniKetli":
                desc = "Serves 2, 250ml";
                break;
            case "ChotiKetli":
                desc = "Serves 4, 400ml";
                break;
            case "BadiKetli":
                desc = "Serves 10, 1000ml";
                break;
        }
        return desc;
    }

    function addIngredientVariants(product, groups, brand) {
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.variants != null) {
            product.prices[0].recipe.ingredient.variants.map(function (varItem) {
                var group = angular.copy(zomatoGroup);
                var displayName = varItem.product.displayName || "Option";
                group.group_id = product.id + "_" + varItem.product.productId;
                group.group_name = displayName;
                group.group_description = "";
                group.group_minimum = 1;
                group.group_maximum = 1;
                varItem.details.map(function (varDetail, index) {
                    var id = product.id + "_" + varDetail.productId + varDetail.alias.substr(0, 1)
                        + varDetail.alias.substr(varDetail.alias.length - 1) + displayName.length;
                    var name = varDetail.alias;
                    var description = "";
                    group.items.push(addProduct(null, null, 0, id, name, description, null, index === 0 ? 1 : 0, false, brand));
                });
                groups.push(group);
            });
        }
        if (product.id == 10) {
            var group = angular.copy(zomatoGroup);
            var displayName = "Milk Option";
            group.group_id = product.id + "_" + "MLK_OPT";
            group.group_name = displayName;
            group.group_description = "";
            group.group_minimum = 1;
            group.group_maximum = 1;
            ["Regular Milk", "Full Doodh", "Doodh Kum", "Paani Kum"].map(function (varnt, index) {
                var id = product.id + "_" + varnt.substr(0, 1)
                    + varnt.substr(varnt.length - 1) + displayName.length;
                var name = varnt;
                var description = "";
                group.items.push(addProduct(null, null, 0, id, name, description, null, index === 0 ? 1 : 0, false, brand));
            });
            groups.push(group);
        }
        if (product.id == 1282) {
            var group = angular.copy(zomatoGroup);
            var displayName = "Milk Option";
            group.group_id = product.id + "_" + "MLK_OPT";
            group.group_name = displayName;
            group.group_description = "";
            group.group_minimum = 1;
            group.group_maximum = 1;
            ["Regular Milk", "Full Doodh", "Doodh Kum", "Paani Kum"].map(function (varnt, index) {
                var id = product.id + "_" + varnt.substr(0, 1)
                    + varnt.substr(varnt.length - 1) + displayName.length;
                var name = varnt;
                var description = "";
                group.items.push(addProduct(null, null, 0, id, name, description, null, index === 0 ? 1 : 0, false, brand));
            });
            groups.push(group);
        }
    }

    function addIngredientProducts(product, groups, brand) {
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.products != null) {
            product.prices[0].recipe.ingredient.products.map(function (prodItem) {
                var group = angular.copy(zomatoGroup);
                var displayName = prodItem.display || "Option";
                group.group_id = product.id + "_" + displayName;
                group.group_name = displayName;
                group.group_description = "";
                group.group_minimum = 1;
                group.group_maximum = 1;
                prodItem.details.map(function (prodDetail, index) {
                    var id = product.id + "_" + prodDetail.product.productId;
                    var name = prodDetail.product.name;
                    var description = "";
                    group.items.push(addProduct(null, null, 0, id, name, description, null, index === 0 ? 1 : 0, false, brand));
                });
                groups.push(group);
            });
        }
    }

    function addCompositeProducts(product, groups, unitData, brand) {
        var sum = 0;
        if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.compositeProduct != null) {
            product.prices[0].recipe.ingredient.compositeProduct.details.map(function (copItem, ind) {
                var group = angular.copy(zomatoGroup);
                var displayName = copItem.name || "Option " + parseInt(ind + 1);
                group.group_id = product.id + "_" + copItem.name;
                group.group_name = displayName;
                group.group_description = "";
                group.group_minimum = 1;
                group.group_maximum = 1;
                copItem.menuProducts.map(function (menuItem, index) {
                    var id = product.id + "_" + menuItem.product.productId;
                    var name = menuItem.product.name;
                    var description = "";
                    var price = null;
                    unitData.products.map(function (prod) {
                        if (prod.id === menuItem.product.productId) {
                            group.items.push(addProduct(prod, null, 0, null, name, null, null, index === 0 ? 1 : 0, true, brand));
                            if(prod.prices.length == 1) {
                                //sum = sum + prod.prices[0].price;
                                price = prod.prices[0].price;
                            } else {
                                prod.prices.map(function (price) {
                                    if(price.dimension == menuItem.dimension.code) {
                                        //sum = sum + price.price;
                                        price = price.price;
                                    }
                                })
                            }
                        }
                    });
                    //calculating taxes
                    var taxes = 0;
                    unitData.taxes.map(function (tax) {
                        if(tax.taxCode == product.taxCode) {
                            taxes = taxes + (tax.state.cgst/100 * price);
                            taxes = taxes + (tax.state.sgst/100 * price);
                        }
                    });
                    price = price + taxes;
                    sum = sum + price;
                });
                groups.push(group);
            });
        }
        return sum;
    }

    function addAddons(product, groups, brand) {
        if (([10, 11, 12, 50].indexOf(product.id) >= 0) || service.addonsMap[product.id] != null && service.addonsMap[product.id].length > 0) {
            var group = angular.copy(zomatoGroup);
            var displayName = "Addons";
            group.group_id = product.id + "_" + displayName;
            group.group_name = displayName;
            group.group_description = "";
            group.group_minimum = 0;
            var addonsList = ([10, 11, 12, 50].indexOf(product.id) >= 0) ? service.addonsMap[11] : service.addonsMap[product.id];
            group.group_maximum = addonsList.length;
            addonsList.map(function (add) {
                var id = product.id + "_" + add.product.productId;
                var name = add.product.name;
                var description = "";
                group.items.push(addProduct(null, null, 0, id, name, description, null, 0, false, brand));
            });
            groups.push(group);
        }
    }

    function addCatalogCharges(unitData, charges, additional_charges, metadata, addPackaging) {
        unitData.products.map(function (prod) {
            var chargeIds = [1044];
            if(addPackaging == true) {
                chargeIds.push(1043);
            }
            if (chargeIds.indexOf(prod.id) >= 0) {
                var charge = angular.copy(zomatoCharge);
                charge.charge_id = prod.id;
                charge.charge_name = prod.id === 1044 ? "Delivery Charge" : "Restaurant Packaging Charges";
                charge.charge_type = prod.id === 1044 ? "FIXED" : metadata.packagingType;
                charge.charge_value = prod.id === 1044 ? prod.prices[0].price : metadata.packagingValue;
                charge.charge_is_active = 1;
                charge.applicable_on = "ORDER";
                if (prod.id === 1043) {
                    charge.charge_always_applicable = 1;
                } else {
                    charge.charge_always_applicable = 0;
                    charge.charge_applicable_below_order_amount = 200;
                }
                charge.has_tier_wise_values = 0;
                charge.tier_wise_values = [];
                for (var i = 0; i < 3; i++) {
                    charge.tier_wise_values.push({
                        tier_id: i + 1,
                        charge_value: charge.charge_value,
                        charge_applicable_below_order_amount: charge.charge_applicable_below_order_amount,
                        charge_always_applicable: charge.charge_always_applicable
                    })
                }
                charge.charge_taxes = [];
                var taxObj = {order_type: "DELIVERY", taxes: []};
                unitData.taxes.map(function (tax) {
                    var code = prod.taxCode;
                    if (tax.taxCode === code) {
                        addZomatoTaxObj(tax, "cgst", taxObj);
                        addZomatoTaxObj(tax, "sgst", taxObj);
                    }
                });
                charge.charge_taxes.push(taxObj);
                charges.push(charge);
            }
        });
        if (charges.length > 0) {
            var obj = {order_type: "DELIVERY", charges: []};
            charges.map(function (charge) {
                obj.charges.push(charge.charge_id);
            });
            additional_charges.push(obj);
        }
    }

    function filterMiniKetli(menu) {
        menu.charges.forEach(function (charge) {
            if (charge.charge_id == 1043 && charge.charge_type == "FIXED") {
                menu.categories.forEach(function (category) {
                    category.items.forEach(function (menuItem) {
                        menuItem.groups.forEach(function (group) {
                            var gItems = [], found = false;
                            group.items.forEach(function (gitem) {
                                if (gitem.item_name.indexOf("Mini Ketli") < 0) {
                                    gItems.push(gitem);
                                    found = true;
                                }
                            });
                            if (found) {
                                group.items = gItems;
                            }
                        });
                    })
                })
            }
        });
    }

    function setQVMPrices(menu, unitData) {
        menu.categories.map(function (cat) {
            if(cat.category_name === "QVM") {
                cat.subcategories.map(function (subCat) {
                    subCat.items.map(function (item) {
                        unitData.products.map(function (prod) {
                            if (prod.id == item.item_id) {
                                var unit_price = 0;
                                var total_price = 0;
                                prod.prices[0].recipe.ingredient.compositeProduct.details.map(function (copItem, ind) {
                                    var iprice = 0;
                                    copItem.menuProducts.map(function (menuItem, index) {
                                        unitData.products.map(function (uprod) {
                                            if (menuItem.product.productId == uprod.id) {
                                                uprod.prices.map(function (price) {
                                                    if (price.dimension == menuItem.dimension.code) {
                                                        //unit_price = unit_price + price.price;
                                                        iprice = iprice + price.price;
                                                    }
                                                });
                                                var taxes = 0;
                                                unitData.taxes.map(function (tax) {
                                                    if(tax.taxCode == uprod.taxCode) {
                                                        taxes = taxes + (tax.state.cgst/100 * iprice);
                                                        taxes = taxes + (tax.state.sgst/100 * iprice);
                                                    }
                                                });
                                                total_price = total_price + iprice;
                                                iprice = iprice + taxes;
                                            }
                                        });
                                    });
                                    unit_price = unit_price + iprice;
                                });
                                item.combo_reduced_price = item.item_final_price;
                                item.item_unit_price = unit_price;
                                item.item_final_price = item.item_unit_price;
                                /*var discount = total_price - item.combo_reduced_price;
                                var discountPercent = discount/total_price * 100;
                                prod.prices[0].recipe.ingredient.compositeProduct.details.map(function (copItem, ind) {
                                    var total_discountedTax = 0;
                                    copItem.menuProducts.map(function (menuItem, index) {
                                        unitData.products.map(function (uprod) {
                                            if (menuItem.product.productId == uprod.id) {
                                                uprod.prices.map(function (price) {
                                                    if (price.dimension == menuItem.dimension.code) {
                                                        //unit_price = unit_price + price.price;
                                                        //iprice = price + price.price;
                                                        var discountedPrice = price.price - (discountPercent/100 * price.price);
                                                        var discountedTax = 0;
                                                        unitData.taxes.map(function (tax) {
                                                            if(tax.taxCode == uprod.taxCode) {
                                                                discountedTax = discountedTax + (tax.state.cgst/100 * discountedPrice);
                                                                discountedTax = discountedTax + (tax.state.sgst/100 * discountedPrice);
                                                            }
                                                        });
                                                        total_discountedTax = total_discountedTax + discountedTax;
                                                    }
                                                });
                                            }
                                        });
                                    });
                                    item.combo_reduced_price = item.combo_reduced_price + total_discountedTax;
                                });*/
                            }
                        });
                    });
                });
            }
        });
    }

    return service;
}]);