adminapp.factory('DateTimeUtil', function (toastService) {
    return {
        toDateTime: function (value) {
            if (value instanceof Date) return value;

            if (typeof value === 'string') {
                var parts = value.split(':');
                if (parts.length === 2) {
                    var date = new Date();
                    date.setHours(parseInt(parts[0], 10));
                    date.setMinutes(parseInt(parts[1], 10));
                    date.setSeconds(0);
                    date.setMilliseconds(0);
                    return date;
                }
            }
            return null;
        },

        formatDateToTimeOnly: function (dateObj) {
            if (!(dateObj instanceof Date)) {
                toastService.error("Invalid date object");
                return null;
            }
            var hours = dateObj.getHours();
            var minutes = dateObj.getMinutes();

            return (hours < 10 ? '0' + hours : hours) + ':' +
                   (minutes < 10 ? '0' + minutes : minutes);
        },

        isValidTimeRange: function (startTime, endTime) {
            var start = this.toDateTime(startTime);
            var end = this.toDateTime(endTime);

            return start && end && end > start;
        },

        extractStartAndEndTime: function (timeRange, separator) {
            if (!timeRange || timeRange.indexOf(separator) === -1) {
                toastService.error("Invalid time range format");
                return {};
            }
            var parts = timeRange.split(separator);
            if (parts.length !== 2) {
                toastService.error("Invalid time range format");
                return {};
            }

            return {
                startTime: this.toDateTime(parts[0]),
                endTime: this.toDateTime(parts[1])
            };
        }
    };
});
