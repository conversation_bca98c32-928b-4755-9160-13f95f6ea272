adminapp.controller("PriceProfileProductMappingCtrl",
 function ($scope, $http, $rootScope, $log, $window, AppUtil, fileService, $cookieStore ,$filter) {
    $scope.init = function(){
      $rootScope.enableScreenFilter = true;
      $scope.mappingType = 'ProductToProfile';
      $scope.getAllProducts();
      $scope.currentPage = 1;   // Start at page 1
      $scope.pageSize = 50;     // Show 50 rows per page
      $scope.sortOption = "productName";
      $scope.versions = [];
      $scope.dimensionInfo = [];
      $scope.selectedProduct = null;
      $scope.filters = [];
      $scope.showPreviewModal = false;
      $scope.selectedInput = {
        dimensionId : null,
        productId : null,
        priceProfile : null,
        selectedVersion : null

      }

      $scope.searchedInput = {
        dimensions : null,
        products : null,
        priceProfiles : null,
        selectedVersions : null
  
      }

    
      $scope.statusList = ['ACTIVE','IN_ACTIVE'];
      $scope.productsInfo = [];
      $scope.productDimensions = [];
      $scope.preoductTypeList = [];
      $scope.getALLProfiles();
      $scope.getAllBrands();
      $scope.getAllDimensionsAndPriceProfile();
      $scope.filters = {};
      $scope.selectedProducts = [];
      $scope.selectedProductIds = [];
      $scope.selectedDimensions = [];
      $scope.selectedProfiles = [];
      $scope.selectedVersons = [];
      $scope.selectedProductTypes = [];
      $scope.multiSelectSettingsForProducts = {
        showEnableSearchButton: true, template: '<b> {{option.name + " : " + option.id + " - " + option.status}}</b>', scrollable: true,
        scrollableHeight: '300px'
    };
    $scope.multiSelectSettingsForDimensions = {
      showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
      scrollableHeight: '300px'
  };

  $scope.multiSelectSettingsForProfiles = {
    showEnableSearchButton: true, template: '<b> {{option.priceProfileName}}</b>', scrollable: true,
    scrollableHeight: '300px'
};

$scope.multiSelectSettingsForVersions = {
  showEnableSearchButton: true, template: '<b> {{option.versionNo + " : " + option.priceProfileName }}</b>', scrollable: true,
  scrollableHeight: '300px'
};

$scope.multiSelectSettingsForProductType = {
  showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
  scrollableHeight: '300px'
};

    
  }


  $scope.toggleMappingType = function(){
    $scope.selectedInput.dimensionId = null;
    $scope.selectedInput.productId = null;
    $scope.selectedInput.priceProfile = null;
    $scope.selectedInput.selectedVersion = null;
    $scope.filteredData = [];
    $scope.data = [];
    $scope.selectedProduct = null;
    $scope.filters = {};
    
  }

  $scope.changeSelectedBrand  =function(brandId){
    $scope.selectedProductTypes = [];
    $scope.productsInfoByBrand = $scope.productsInfo.filter(function(product){
      return product.brandId == brandId && product.status == "ACTIVE";
    });
    $scope.filteredProductsInfo = $scope.productsInfoByBrand;
    $scope.changeProducts($scope.productsInfoByBrand);
  }

  $scope.changeVersion = function(version){
    $scope.selectedInput.selectedVersion = version;
  }

  $scope.changeDimension = function(dimensionJson){
    var dimension = JSON.parse(dimensionJson);
    $scope.selectedInput.dimensionId  =dimension;
  }

  function formatFilter1(label, values) {
    if (!values || values.length === 0) return null;
    if (values.length > 5) {
        return {
            displayText: label + ": " + values.slice(0, 3).join(", ") + ` and ${values.length - 3} more…`,
            fullText: label + ": " + values.join(", ")
        };
    }
    return {
        displayText: label + ": " + values.join(", "),
        fullText: label + ": " + values.join(", ")
    };
}

  
function formatFilter(label, values, icon) {
  if (!values || values.length === 0) return null;

  let visibleCount = 3; // Show 3 by default
  let displayValues = values.slice(0, visibleCount);
  let hiddenCount = values.length - visibleCount;

  return {
      label: label,
      displayValues: displayValues,
      fullText: values.join(", "),
      hiddenCount: hiddenCount > 0 ? hiddenCount : 0,
      expand: false,
      icon: icon
  };
}
   

$scope.setAppliedFiltersArray = function () {
   $scope.filters = [];

  function formatFilter(label, values, icon) {
      if (!values || values.length === 0) return null;

      let visibleCount = 3; // Show 3 items initially
      let isExpanded = false; // Track expansion state

      return {
          label: label,
          allValues: values, // Store full list
          displayValues: values.slice(0, visibleCount),
          hiddenCount: values.length > visibleCount ? values.length - visibleCount : 0,
          expand: function () {
              this.isExpanded = !this.isExpanded;
              this.displayValues = this.isExpanded ? this.allValues : values.slice(0, visibleCount);
          },
          isExpanded: isExpanded,
          icon: icon
      };
  }

  let filtersList = [
      formatFilter("Dimensions", $scope.searchedInput.dimensions, "fa fa-ruler"),
      formatFilter("Products", $scope.searchedInput.products, "fa fa-box"),
      formatFilter("Price Profiles", $scope.searchedInput.priceProfiles, "fa fa-tags"),
      formatFilter("Versions", $scope.searchedInput.selectedVersions, "fa fa-code-branch")
  ];

  filtersList.forEach(filter => {
      if (filter) $scope.filters.push(filter);
  });

  $scope.filters =  $scope.filters.length ? $scope.filters : [{ label: "No filters applied", displayValues: ["None"], icon: "fa fa-ban" }];
};





  function setSearchInputs(){
    var selectedProductNames  =$scope.selectedProducts.map(function(product){
      return product.name;
    });
    var selectedPriceProfileNames = $scope.selectedProfiles.map(function(profile){
              return profile.priceProfileName;
    });
    var selectedVersionIds = $scope.selectedVersons.map(function(version){
      return version.versionNo + "(" + version.priceProfileName + ")" ;
    });
    var selectedDimensions = $scope.selectedDimensions.map(function(dimension){
      return dimension.name;
    });

 

    $scope.searchedInput = {
      dimensions : selectedDimensions,
      products : selectedProductNames,
      priceProfiles : selectedPriceProfileNames,
      selectedVersions : selectedVersionIds

    }
    $scope.setAppliedFiltersArray();
  }

  $scope.getProfileProductMappings = function(){
    $scope.selectedProductIds  =$scope.selectedProducts.map(function(product){
      return product.id;
    });
    $scope.selectedPriceProfileIds = $scope.selectedProfiles.map(function(profile){
              return profile.priceProfileDataId;
    });
    $scope.selectedVersionIds = $scope.selectedVersons.map(function(version){
      return version.priceProfileVersionsId;
    });
    $scope.selectedDimensionIds = $scope.selectedDimensions.map(function(dimension){
      return dimension.id;
    });
     
    setSearchInputs();

    $scope.filteredData = [];
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'POST',
      url: AppUtil.restUrls.priceProfileManagement.getProfileProductMappings,
      data : {
        productIds : $scope.selectedProductIds,
        priceProfileIds : $scope.selectedPriceProfileIds,
        versionIds :  $scope.selectedVersionIds,
        dimensionIds : $scope.selectedDimensionIds
      }
      
  }).then(function success(response) {
    $rootScope.showFullScreenLoader = false;
      $scope.data = response.data;
      $scope.data.forEach(function(row){
        row.originalPrice = row.price;
        row.newPrice = row.price;
        row.originalStatus = row.status;
        row.newStatus = row.status;
      })
      $scope.applySorting();
      //$scope.applyFilters();

  });

  $scope.showPreviewMappings = function() {
    $scope.showPreviewModal = true;
};

$scope.closePreview = function() {
    $scope.showPreviewModal = false;
};

// Get preview data
$scope.setPreviewMappings = function (mappings) {
    $scope.previewMappings =  mappings.map(mapping => ({
        productName: mapping.productId.name,
        priceProfile: mapping.priceProfileId.name,
        version: mapping.version,
        dimension: mapping.dimensionCode.name,
        originalPrice: mapping.originalPrice,
        newPrice: mapping.newPrice,
        originalStatus: mapping.originalStatus,
        newStatus: mapping.newStatus
    }));
    $scope.mappingsPayload = mappings;
    $scope.showPreviewMappings();
};


  $scope.saveMappings = function(mappings){
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'POST',
      url: AppUtil.restUrls.priceProfileManagement.saveProfileProductMappings,
      data : mappings
  }).then(function success(response) {
      $rootScope.showFullScreenLoader = false;
      if(response.data){
        $scope.closePreview();
        $scope.data=[];
        $scope.applySorting();
        alert('Mappings saved successfully');
      }
      //$scope.data = response.data;

  });
  }
  }

  // $scope.data = [
  //   {
  //     profileName: "Profile 1",
  //     version: "1.0",
  //     brand: "Brand A",
  //     productName: "Product X",
  //     dimension: "Dimension A",
  //     status: "Active",
  //   },
  //   {
  //     profileName: "Profile 2",
  //     version: "1.1",
  //     brand: "Brand B",
  //     productName: "Product Y",
  //     dimension: "Dimension B",
  //     status: "Inactive",
  //   },
  //   // Add more data as needed
  // ];

  $scope.filteredData = function () {
    return function (item) {
      if ($scope.mappingType === "ProfileToProduct") {
        return (
          (!$scope.filters.profileName || item.profileName.includes($scope.filters.profileName)) &&
          (!$scope.filters.version || item.version.includes($scope.filters.version)) &&
          (!$scope.filters.brand || item.brand === $scope.filters.brand)
        );
      } else if ($scope.mappingType === "ProductToProfile") {
        return (
          (!$scope.filters.productName || item.productName.includes($scope.filters.productName)) &&
          (!$scope.filters.dimension || item.dimension.includes($scope.filters.dimension)) &&
          (!$scope.filters.status || item.status === $scope.filters.status)
        );
      }
      return true;
    };
  };

       $scope.selectProfile = function(profile){
        var cloneProfile  = JSON.parse(profile);
        $scope.selectedInput.priceProfile = cloneProfile;
        $scope.versions = cloneProfile.priceProfileVersions;
        
      }

      $scope.selectProfiles = function(selectedProfiles){
        $scope.versions = [];
        selectedProfiles.forEach(function(profile){
          var profileVersions = profile.priceProfileVersions.map(function(version){
            version.priceProfileName = profile.priceProfileName;
            return version;
          });
          profileVersions.forEach(function(version){
            $scope.versions.push(version);
          })
        })
      
        
      }

      $scope.getProductPriceProfileSheet = function(){
        var updatedMappings = $scope.filteredData.filter(function(mapping){
            return mapping.selected;
         });
         $rootScope.showFullScreenLoader = true;
           $http(
            {
                method: 'POST',
                url: AppUtil.restUrls.priceProfileManagement.getProductPriceProfileMappingsSheet,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                data: updatedMappings
            })
            .then(
                function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != undefined && response != null) {
                        var fileName = "PRODUCT_PRICE_PROFILE_SHEET" + " - " + Date.now() + ".xlsx";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }
                },
                function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                    alert("Unable to download Sheet");
                });
    }


    $scope.uploadBulkProductPriceSheet = function () {
      if (fileService.getFile() == null || fileService.getFile() == undefined) {
          bootbox.alert("Please select a .xlsx file");
          return;
      }
      console.log("File is", fileService.getFile());
      if (confirm("Do you want to Upload and Update Prices?")) {
          var fd = new FormData();
          fd.append("file", fileService.getFile());
          fd.append("updatedBy", $rootScope.userData.id);
          $rootScope.showFullScreenLoader = true;
          $http({
              url: AppUtil.restUrls.priceProfileManagement.uploadBulkProductPriceProfileSheet,
              method: 'POST',
              data: fd,
              headers: {
                  'Content-Type': undefined,
              },
              transformRequest: angular.identity
          }).success(function (response) {
              $rootScope.showFullScreenLoader = false;
              angular.element("input[type='file']").val(null);
              fileService.push(null);
              $scope.productPriceFile = null;
              console.log(response);
              if (response.errorMessage == null) {
                      var alertMessage = "File Uploaded";
                      alertMessage = alertMessage +  
                          "\nRecords Changed: " + response.recordsUpdated +
                          "\nRecords Added: " + response.recordsAdded +
                          "\nRecord with errors: " + response.recordsWithErrors +
                          "\errors: " + response.errors;
                      alert(alertMessage);
                      //$scope.getProfileProductMappings();
              }
              else {
                  alert(response.errorMessage);
              }

          }).error(function (response) {
              $rootScope.showFullScreenLoader = false;
              alert("Error while uploading Unit Product Pricing Sheet");
          });
      }
      ;
  }

      $scope.selectProfileForRow = function(profileJSON,row){
        var profile  = JSON.parse(profileJSON);
        row.versions = profile.priceProfileVersions;
        row.priceProfileId = {id : profile.priceProfileDataId , name : profile.priceProfileName , priceProfileName : profile.priceProfileName };
        
      }

      $scope.getAllBrands = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands
        }).then(function success(response) {
            $scope.brandList = response.data;
            var allBrands = {};
            allBrands.brandId = -1;
            allBrands.brandName = 'ALL';
            $scope.brandList.unshift(allBrands);

        });
    };

       

       // Bulk Updates
       $scope.bulkPrice = null;
       $scope.bulkStatus = null;

       function getLowerCase(value){
        if(value == null){return null};
        return value.toString().toLowerCase();
       }

       $scope.customFilter = function (row) {
        var filters = $scope.filters || {};
      
            // if (!filters[key]) return true; // Skip empty filters
            // if (!item[key]) return false; // Skip undefined fields
            // return item[key].toString().toLowerCase() === filters[key].toString().toLowerCase();
            return (
                     (!$scope.filters.priceProfile || getLowerCase(row.priceProfileId.name).includes(getLowerCase($scope.filters.priceProfile))) &&
                     (!$scope.filters.product || getLowerCase(row.productId.name.includes(getLowerCase($scope.filters.product)))) &&
                     (!$scope.filters.product || getLowerCase(row.productId.name).includes(getLowerCase($scope.filters.product))) &&
                     (!$scope.filters.dimension || getLowerCase(row.dimensionCode.name).includes(getLowerCase($scope.filters.dimension))) &&
                    (!$scope.filters.version || getLowerCase(row.version).includes(getLowerCase($scope.filters.version)))  &&
                    (!$scope.filters.newStatus || row.newStatus == ($scope.filters.newStatus)) &&
                    (!$scope.filters.newPrice || row.newPrice == ($scope.filters.newPrice))
                );
    };

      $scope.applySorting = function(){
        if ($scope.sortOption === "productName") {
          $scope.data = $filter('orderBy')($scope.data, 'productId.name');
      } else if ($scope.sortOption === "profileVersion") {
          $scope.data = $filter('orderBy')($scope.data, ['priceProfileId.name', 'version']);
      }
      $scope.applyFilters();
      }

       // Apply Filters
       $scope.applyFilters = function () {
        console.log('Filters applied:', $scope.filters);
        $scope.filteredData =  $scope.data.filter(function (row) {
        return (
          (!$scope.filters.priceProfile || getLowerCase(row.priceProfileId.name).includes(getLowerCase($scope.filters.priceProfile))) &&
          (!$scope.filters.product || getLowerCase(row.productId.name.includes(getLowerCase($scope.filters.product)))) &&
          (!$scope.filters.product || getLowerCase(row.productId.name).includes(getLowerCase($scope.filters.product))) &&
          (!$scope.filters.dimension || getLowerCase(row.dimensionCode.name).includes(getLowerCase($scope.filters.dimension))) &&
         (!$scope.filters.version || getLowerCase(row.version).includes(getLowerCase($scope.filters.version)))  &&
         (!$scope.filters.newStatus || row.newStatus == ($scope.filters.newStatus)) &&
         (!$scope.filters.newPrice || row.newPrice == ($scope.filters.newPrice)) && 
         (!$scope.filters.productId || getLowerCase(row.productId.id).includes(getLowerCase($scope.filters.productId)))
     );
        });
       }
        //  console.log('Filters applied:', $scope.filters);
        //  $scope.filteredData = $scope.data.filter(function (row) {
        //   return (
        //       (!$scope.filters.priceProfile || row.priceProfileId.name.includes($scope.filters.priceProfile)) &&
        //       (!$scope.filters.product || row.productId.name.includes($scope.filters.product)) &&
        //       (!$scope.filters.dimension || row.dimensionCode.name.includes($scope.filters.dimension)) &&
        //       (!$scope.filters.version || row.version.includes($scope.filters.version))  &&
        //       (!$scope.filters.status || row.status == ($scope.filters.status)) &&
        //       (!$scope.filters.price || row.price == ($scope.filters.price))
        //   );
     

       $scope.updatePriceForFilteredRows = function(price){
        //$scope.applyFilters();
        $scope.filteredData.forEach(function(filteredRow){
          if(filteredRow.selected){
            filteredRow.newPrice = price;  
          }     
        })
       }

       $scope.updateStatusForFilteredRows = function(status){
        $scope.filteredData.forEach(function(filteredRow){
          if(filteredRow.selected){
            filteredRow.newStatus = status;
          }
        })
       }

       $scope.filterStatusRows  =function(status){
          $scope.filters.newStatus = status;
          $scope.applyFilters();
       }

       $scope.unFilterStatusRows = function(status){
        $scope.filters.newStatus = null;
        $scope.applyFilters();
       }

       $scope.filterPriceRows = function(price){
        $scope.filters.newPrice = price;
        $scope.applyFilters();
       }

       $scope.unFilterPriceRows = function(){
        $scope.filters.newPrice = null;
        $scope.applyFilters();
       }
       

       $scope.getAllProducts = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.selectedProductTypes = [];
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.products
        }).then(function success(response) {
          $rootScope.showFullScreenLoader = false;
            $scope.productsInfo = response.data;
            $scope.productsInfoByBrand  =$scope.productsInfo;
            $scope.filteredProductsInfo  =$scope.productsInfo;
            sortProductList();
            $scope.allProdcutsInfo = $scope.productsInfo;
        }, function error(response) {
          $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.filterProductsBrandWise = function () {
      if($scope.allProdcutsInfo != null){
          if(!angular.equals($scope.selectedBrand, '-1')){
              var filteredProducts = [];
              for(var i in $scope.allProdcutsInfo){
                  if(angular.equals($scope.allProdcutsInfo[i].brandId.toString(), $scope.selectedBrand.toString())){
                      filteredProducts.push($scope.allProdcutsInfo[i]);
                  }
              }
              $scope.productsInfo = filteredProducts;
          } else if(!angular.equals($scope.productsInfo.length, $scope.allProdcutsInfo.length)){
              $scope.productsInfo = $scope.allProdcutsInfo;
          }
      }
  };

    $scope.getALLProfiles = function(){
      $http({
          url: AppUtil.restUrls.priceProfileManagement.findPriceProfiles,
          dataType: 'json',
          method: 'GET',
          headers: {
              "Content-Type": "application/json"
          }
      }).then(function success(response) {
          //console.log(JSON.stringify(response));
          
          $scope.profiles = response.data;
          $scope.selectProfiles($scope.profiles);
          //alert("Successfully fetched Price Profile!!");

      }, function error(response) {
        
          console.log("error:" + response);
      });
      
    }


       // Reset Filters
       $scope.resetFilters = function () {
         $scope.filters.forEach(filter => filter.selected = '');
       };

       // Select All Rows
       $scope.selectAllRows = function () {
         const isChecked = $scope.tableData.every(row => row.selected);
         $scope.tableData.forEach(row => row.selected = !isChecked);
       };

       // Apply Bulk Updates
       $scope.applyBulkUpdates = function () {
         $scope.tableData.forEach(row => {
           if (row.selected) {
             if ($scope.bulkPrice !== null) row.price = $scope.bulkPrice;
             if ($scope.bulkStatus !== null) row.status = JSON.parse($scope.bulkStatus);
           }
         });
         $scope.bulkPrice = null;
         $scope.bulkStatus = null;
       };

       // Toggle all rows for bulk edit
    $scope.toggleAllRows = function(selectAll) {
      $scope.filteredData.forEach(row => row.selected = selectAll);
  };

  // Bulk edit functionality
  $scope.bulkEdit = function() {
      var selectedRows = $scope.data.filter(row => row.selected);
      if (selectedRows.length === 0) {
          alert('No rows selected for bulk editing!');
          return;
      }

      // Example: Change status for selected rows
      var newStatus = prompt('Enter new status (ACTIVE/IN_ACTIVE):');
      if (newStatus) {
          selectedRows.forEach(row => row.status = newStatus);
          
         
      }
  };

  $scope.$watch('selectedProducts', function(newValue, oldValue) {
  $scope.refreshDimensions();
  console.log("selected Products changed from", oldValue, "to", newValue);
},true);

$scope.$watch('selectedProfiles', function(newValue, oldValue) {
  $scope.selectProfiles($scope.selectedProfiles);
  console.log("selected Profile changed from", oldValue, "to", newValue);
},true);

$scope.$watch('selectedProductTypes', function(newValue, oldValue) {
  $scope.selectProductTypes($scope.selectedProductTypes);
  console.log("selected Product Types changed from", oldValue, "to", newValue);
},true);

  $scope.submitMappings = function(){
    var selectedRows = $scope.data.filter(row => row.selected);
    var errorList = checkForNullPrice(selectedRows);
    if(errorList.length > 0){
      alert(errorList);
    }else{
      $scope.setPreviewMappings(selectedRows);
      //$scope.saveMappings(selectedRows);
    }
  
  }

  function checkForNullPrice(selectedRows){
    var errorList = [];
    selectedRows.forEach(function(row){
      if(row.newPrice ==  null ){
        var errorMsg = "Price not Entered For Product  ::::: " + row.productId.name
         if(row.dimensionCode!= null){
          errorMsg = errorMsg + " And Dimension ::::: " + row.dimensionCode.name
         } 
         if(row.priceProfileId != null){
          errorMsg = errorMsg + " And Price profile ::::: " + row.priceProfileId.name
         }
         errorMsg = errorMsg + "\n";
         errorList.push(errorMsg);
      }
    });
    return errorList;
  }

  
  $scope.changeProduct = function (selectedProduct) {
    //var selectedProduct  = JSON.parse(selectedProductJSON);
    if (selectedProduct == null || selectedProduct === undefined) {
        return false;
    }
    var selectedProductId = selectedProduct.id;
    $scope.productDimension = {};
    for (var i = 0; i < $scope.productsInfoByBrand.length; i++) {
        if ($scope.productsInfo[i].id == selectedProductId) {
            $scope.selectedProduct = $scope.productsInfo[i];
            $scope.selectedInput.productId = $scope.selectedProduct.id;
            break;
        }
    }

    for (var i = 0; i < $scope.dimensionInfo.length; i++) {
        if ($scope.dimensionInfo[i].detail.id == $scope.selectedProduct.dimensionProfileId) {
          $scope.selectedProduct.productDimension = $scope.dimensionInfo[i];
            break;
        }
    }
    //initCheckBoxModal();
};

$scope.changeProducts = function (selectedProducts) {
  //var selectedProduct  = JSON.parse(selectedProductJSON);
  if (selectedProducts == null || selectedProducts === undefined || selectedProducts.length == 0) {
      return false;
  }
 
  var selectedProduct = null;
  $scope.productDimensions = [];
  var selectedProductDimensionIds = [];
  for (var i = 0; i < $scope.productsInfoByBrand.length; i++) {
          selectedProductDimensionIds.push($scope.productsInfoByBrand[i].dimensionProfileId);
          //$scope.selectedInput.productId = $scope.selectedProduct.id;
        
      
  }

  for (var i = 0; i < $scope.dimensionInfo.length; i++) {
      if (selectedProductDimensionIds.indexOf($scope.dimensionInfo[i].detail.id) >=0) {
        for(var j = 0 ; j < $scope.dimensionInfo[i].content.length; j++){
          $scope.productDimensions.push($scope.dimensionInfo[i].content[j]);
        }
      }
  }
  //initCheckBoxModal();
};



$scope.selectProductTypes = function(selectedProductTypes){
  var selectedProductTypeIds = selectedProductTypes.map(function(type){return type.id;});
  $scope.filteredProductsInfo = $scope.productsInfoByBrand.filter(function(product){
    return  product.status == "ACTIVE" && (selectedProductTypeIds == null || selectedProductTypeIds.length == 0 
      || selectedProductTypeIds.indexOf(product.type)>=0);
  });
            
}


$scope.refreshDimensions = function(){
  $scope.productDimensions = [];
  var selectedProductDimensionIds = [];
  for (var i = 0; i < $scope.selectedProducts.length; i++) {
          selectedProductDimensionIds.push($scope.selectedProducts[i].dimensionProfileId);
          //$scope.selectedInput.productId = $scope.selectedProduct.id;
        
      
  }

  for (var i = 0; i < $scope.dimensionInfo.length; i++) {
      if (selectedProductDimensionIds.indexOf($scope.dimensionInfo[i].detail.id) >=0) {
        for(var j = 0 ; j < $scope.dimensionInfo[i].content.length; j++){
          $scope.productDimensions.push($scope.dimensionInfo[i].content[j]);
        }
      }
  }
}

$scope.selectProductForRow = function (selectedProduct,row) {
  if (selectedProduct == null || selectedProduct === undefined) {
      return false;
  }
  var selectedProductId = selectedProduct.id;
  $scope.regions = [];
  for (var i = 0; i < $scope.filteredProductsInfo.length; i++) {
      if ($scope.productsInfo[i].id == selectedProductId) {
          $scope.selectedProduct = $scope.productsInfo[i];
          break;
      }
  }
  

  for (var i = 0; i < $scope.dimensionInfo.length; i++) {
      if ($scope.dimensionInfo[i].detail.id == $scope.selectedProduct.dimensionProfileId) {
          row['productDimensionOptions'] = $scope.dimensionInfo[i];
          break;
      }
  }
  //initCheckBoxModal();
};

  // Open Add Mapping Modal
  $scope.openAddMappingModal = function() {
      $scope.newMapping = {}; // Reset the form
      $scope.isAddMappingModalOpen = true;
  };

  $scope.getAllDimensionsAndPriceProfile = function () {
    $http({
        method: 'GET',
        url: AppUtil.restUrls.unitMetaData.listTypes
    }).then(function success(response) {
        $scope.dimensionInfo = response.data.DIMENSION;
        $scope.preoductTypeList = response.data.CATEGORY.map(function(category){
          return category.detail;
        });
    
    });
};

  $scope.addNewRow = function() {
    // Default values for a new row
    const newRow = {
        priceProfileId: $scope.selectedInput.priceProfile !=null ? {id : $scope.selectedInput.priceProfile.priceProfileDataId , name : $scope.selectedInput.priceProfile.priceProfileName
        } : '', // Auto-fill if profile is selected
        productId: $scope.selectedProduct || '', // User will select this
        dimensionCode: $scope.selectedInput.dimensionId != null ? {id : $scope.selectedInput.dimensionId.id , name : $scope.selectedInput.dimensionId.name} : '', // Dimension may depend on the selected product
        version: $scope.selectedInput.selectedVersion || '', // Auto-fill if version is selected
        price: '', // User will input this
        status: 'ACTIVE', // Default status,
        isNew : true
    };

    

    // Append the new row to the data array
    $scope.data.unshift(newRow);

    // Re-apply filters if active
    $scope.applyFilters();
};

$scope.removeRow = function(index) {
  // Remove the row from the data array
  $scope.data.splice(index, 1);

  // Re-apply filters if active
  $scope.applyFilters();
};

  // Close Add Mapping Modal
  $scope.closeAddMappingModal = function() {
      $scope.isAddMappingModalOpen = false;
  };

  // Add new mapping
  $scope.addMapping = function() {
      if (!$scope.newMapping.priceProfile || !$scope.newMapping.product || 
          !$scope.newMapping.dimension || !$scope.newMapping.version || 
          !$scope.newMapping.price || !$scope.newMapping.status) {
          alert('All fields are required!');
          return;
      }

      $http.post('/api/mappings', $scope.newMapping).then(function(response) {
          alert('Mapping added successfully!');
          $scope.data.push(response.data); // Add new mapping to table
          $scope.closeAddMappingModal();
      }).catch(function(error) {
          alert('Error adding mapping!');
      });
  };
});
