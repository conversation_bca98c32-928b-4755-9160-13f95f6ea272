/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("findRecipeController", function($rootScope, $scope, $location, $http, AppUtil, $stateParams) {

    $scope.init = function() {
	$rootScope.enableScreenFilter = true;
	$scope.onlySCM = $stateParams.onlySCM;
	console.log('Only SCM Flag ', $scope.onlySCM);
		$scope.setSelectedRegion(null);
		$scope.allRegions = [];
		$scope.getAllRegionsList();
    };

	$scope.setSelectedRegion = function (region) {
		$scope.selectedRegion = region;
	};

	$scope.getAllRegionsList = function() {
		$http({
			method: 'GET',
			url: AppUtil.restUrls.unitMetaData.regions
		}).then(function success(response) {
			$scope.allRegions = response.data;
			$scope.setSelectedRegion("NCR");
		}, function error(response) {
			console.log("error:" + response);
			$scope.allRegions = [];
			$scope.setSelectedRegion(null);
		});
	};
    
    $scope.findAllRecipe=function(){
    	$scope.recipeName='';
    	$scope.getAllRecipes();
    };
    
    $scope.findRecipeContainsName=function(recipeName){
    	if(recipeName==undefined || recipeName==''){
    		alert("Please enter name character you want to search");
    		return false;
    	}
    	var url = null;
    	if ($scope.onlySCM) {
    	    url = AppUtil.restUrls.scmRecipeManagement.findContainingName;
    	} else {
    	    url = AppUtil.restUrls.recipeManagement.findContainingName;
    	}
    	$rootScope.showFullScreenLoader = true;
    	$http({
    	    method : 'POST',
    	    url : url,
    	    params : {name : recipeName}
    	}).then(function success(response) {
    	    $rootScope.showFullScreenLoader = false;
    	    $scope.recipes = response.data;

    	    $scope.recipes.sort(function(a, b) {
    		var nameA = a.name.toLowerCase(), nameB = b.name.toLowerCase()
    		if (nameA < nameB)
    		    return -1
    		if (nameA > nameB)
    		    return 1
    		return 0
    	    });
    	    console.log($scope.recipes);
    	}, function error(response) {
    	    $rootScope.showFullScreenLoader = false;
    	    console.log("error:" + response);
    	});

    };

	$scope.findRecipeByRecipeId =function(recipeId){
    	if(recipeId==undefined || recipeId==''){
    		alert("Please enter Recipe Id");
    		return false;
    	}
    	$rootScope.showFullScreenLoader = true;
    	$http({
    	    method : 'GET',
    	    url : AppUtil.restUrls.recipeManagement.findRecipeById,
    	    params : {recipeId : recipeId}
    	}).then(function success(response) {
    	    $rootScope.showFullScreenLoader = false;
    	    $scope.recipes = response.data;
			if ($scope.recipes.length === 0) {
				alert("No Recipe Found With recipe Id : " + recipeId);
			}
    	    console.log($scope.recipes);
    	}, function error(response) {
    	    $rootScope.showFullScreenLoader = false;
    	    console.log("error:" + response);
    	});
    };

	$scope.reActivateRecipe = function(recipe) {
    	$rootScope.showFullScreenLoader = true;
    	$http({
    	    method : 'POST',
    	    url : AppUtil.restUrls.recipeManagement.reActivateRecipe,
    	    params : {
				recipeId : recipe.recipeId,
				productId : recipe.product.productId,
				dimensionInfoId : recipe.dimension.infoId,
				dimension : recipe.dimension.name,
				profile : recipe.profile,
				userId : AppUtil.getUserValues().user.id
			}
    	}).then(function success(response) {
    	    $rootScope.showFullScreenLoader = false;
			if (response.status === 200) {
				if (response.data) {
					bootbox.alert("Recipe Re Activated Successfully and Start Date Set to Tomorrow.If you want to Edit Recipe You Can Do it in Add Recipe Menu Tab.");
					$scope.recipes = [];
				} else {
					bootbox.alert("Can not Activate Recipe..!");
				}
			} else {
				if (response.data.errorMessage != null) {
					bootbox.alert(response.data.errorMessage);
				} else {
					bootbox.alert("Some thing Went Wrong While Re activating Recipe..!");
				}
			}
    	}, function error(response) {
    	    $rootScope.showFullScreenLoader = false;
    	    console.log("error:" + response);
    	});
    };

    $scope.getAllRecipes = function() {
	var url = null;
	if ($scope.onlySCM) {
	    url = AppUtil.restUrls.scmRecipeManagement.findAll;
	} else {
	    url = AppUtil.restUrls.recipeManagement.findAll;
	}
	$rootScope.showFullScreenLoader = true;
	$http({
	    method : 'POST',
	    url : url
	}).then(function success(response) {
	    $rootScope.showFullScreenLoader = false;
	    $scope.recipes = response.data;

	    $scope.recipes.sort(function(a, b) {
		var nameA = a.name.toLowerCase(), nameB = b.name.toLowerCase()
		if (nameA < nameB)
		    return -1
		if (nameA > nameB)
		    return 1
		return 0
	    });
	    console.log($scope.recipes);
	}, function error(response) {
	    $rootScope.showFullScreenLoader = false;
	    console.log("error:" + response);
	});
    };

    $scope.viewRecipe = function(recipe) {

	$scope.recipeCostDetail = null;
	$scope.recipeDetail = recipe;
	$http({
	    method : 'POST',
	    url : AppUtil.restUrls.recipeManagement.recipeCostByDetailNew,
	    data : $scope.recipeDetail,
		params : {
			"region": $scope.selectedRegion
		}
	}).then(function success(response) {
	    $scope.recipeCostDetail = response.data;
	}, function error(response) {
	    console.log("error:" + response);
	});
	$("#showPreviewModal").modal("show");
    };

    $scope.downloadRecipe = function(recipe) {
	$rootScope.showFullScreenLoader = true;
	$http({
	    method : 'POST',
	    url : AppUtil.restUrls.recipeManagement.downloadRecipe,
	    data : recipe.recipeId,
	    responseType : 'arraybuffer',
	    headers : {
		'Content-type' : 'application/json',
		'Accept' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }
	}).then(function success(response) {
	    var fileName = "Recipe Detail - " + recipe.name + " - " + Date.now() + ".xls";
	    var blob = new Blob([ response.data ], {
		type : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }, fileName);
	    saveAs(blob, fileName);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    alert("Unable to download recipe");
	    $rootScope.showFullScreenLoader = false;
	});
    };

    $scope.downloadAllRecipe = function() {
	var url = null;
	if ($scope.onlySCM) {
	    url = AppUtil.restUrls.scmRecipeManagement.downloadAllRecipe;
	} else {
	    url = AppUtil.restUrls.recipeManagement.downloadAllRecipe;
	}
	$rootScope.showFullScreenLoader = true;
	$http({
	    method : 'POST',
	    url : url,
	    responseType : 'arraybuffer',
	    headers : {
		'Content-type' : 'application/json',
		'Accept' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }
	}).then(function success(response) {
	    var fileName = "All Recipe Detail - " + Date.now() + ".xls";
	    var blob = new Blob([ response.data ], {
		type : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }, fileName);
	    saveAs(blob, fileName);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    alert("Unable to download recipe");
	    $rootScope.showFullScreenLoader = false;
	});
    };


    $scope.downloadAllRecipeInstruction = function() {
	var url = null;
	if ($scope.onlySCM) {
	    url = AppUtil.restUrls.scmRecipeManagement.downloadAllRecipeInstruction;
	}else{
		alert("Not Supported");
	}
	$rootScope.showFullScreenLoader = true;
	$http({
	    method : 'POST',
	    url : url,
	    responseType : 'arraybuffer',
	    headers : {
		'Content-type' : 'application/json',
		'Accept' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }
	}).then(function success(response) {
	    var fileName = "All Recipe Instruction Detail - " + Date.now() + ".xls";
	    var blob = new Blob([ response.data ], {
		type : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }, fileName);
	    saveAs(blob, fileName);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    alert("Unable to download recipe");
	    $rootScope.showFullScreenLoader = false;
	});
    };



    $scope.downloadAllRecipeDetailWithCost = function() {
	var url = null;
	if ($scope.onlySCM) {
	    url = AppUtil.restUrls.scmRecipeManagement.downloadAllRecipeDetailWithCost;
	} else {
	    url = AppUtil.restUrls.recipeManagement.downloadAllRecipeDetailWithCost;
	}
	$rootScope.showFullScreenLoader = true;
	$http({
	    method : 'POST',
	    url : url,
	    responseType : 'arraybuffer',
	    headers : {
		'Content-type' : 'application/json',
		'Accept' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }
	}).then(function success(response) {
	    var fileName = "All Recipe Detail With Cost - " + Date.now() + ".xls";
	    var blob = new Blob([ response.data ], {
		type : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }, fileName);
	    saveAs(blob, fileName);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    alert("Unable to download recipe with cost");
	    $rootScope.showFullScreenLoader = false;
	});
    };
    $scope.downloadAllRecipeCost = function() {
	var url = null;
	if ($scope.onlySCM) {
	    url = AppUtil.restUrls.scmRecipeManagement.downloadAllRecipeCost;
	} else {
	    url = AppUtil.restUrls.recipeManagement.downloadAllRecipeCost;
	}
	$rootScope.showFullScreenLoader = true;
	$http({
	    method : 'POST',
	    url : url,
	    responseType : 'arraybuffer',
	    headers : {
		'Content-type' : 'application/json',
		'Accept' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }
	}).then(function success(response) {
	    var fileName = "All Recipe Cost Detail - " + Date.now() + ".xls";
	    var blob = new Blob([ response.data ], {
		type : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	    }, fileName);
	    saveAs(blob, fileName);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    alert("Unable to download recipe cost");
	    $rootScope.showFullScreenLoader = false;
	});
    };

});