adminapp.controller("employeePermissionMappingctrl", function($scope,$http,$location,$window,$rootScope, $timeout, AuthService,AppUtil){
	$scope.init = function(){
		$http({
			  method: 'GET',
			  url: AppUtil.restUrls.userManagement.users
			}).then(function success(response) {
				
				$scope.employeeList = response.data;
				$rootScope.showFullScreenLoader = true;
				$scope.employeeList = $scope.employeeList.filter(function (actEmployee) {
				    return (actEmployee.status === "ACTIVE");
				})
				$rootScope.showFullScreenLoader = false;
				console.log($scope.employeeList);
			}, function error(response) {
				  console.log("error:"+response);
			});
	}
});