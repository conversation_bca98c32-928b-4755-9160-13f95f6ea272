adminapp.controller("customerDetailctrl", function($scope,$http,$location,$window,$rootScope, $timeout, AuthService,AppUtil){
	
	$scope.init = function(){
		console.log("hello")
	}	
		
	$scope.viewCustomerDetail = function(){
		console.log($scope.search);
		
	if($scope.search==undefined || $scope.search=='null' || $scope.search==0)
		{
		alert("Search can not be empty, Pleae Input correct contact number");
		return false;
		
		}
		
		
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.customer.profileView,
			  data:$scope.search
			}).then(function success(response) {
				$scope.customerDetailsList=response.data;
				console.log("ddd=",$scope.customerDetailsList);
			}, function error(response) {
				  console.log("error:"+response);
			});
		
	}
	
$scope.addBlackList = function(idd){
	
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.customer.profileBlackList,
			  data:idd
			}).then(function success(response) {
				$scope.customerDetailsList.blacklisted=true;
				console.log("ddd=",$scope.customerDetailsList);
			}, function error(response) {
				  console.log("error:"+response);
			});
		
	}
	
$scope.removeBlackList = function(idd){
	
	$http({
		  method: 'POST',
		  url: AppUtil.restUrls.customer.profileBlackListRemove,
		  data:idd
		}).then(function success(response) {
			$scope.customerDetailsList.blacklisted=false;
			console.log("ddd=",$scope.customerDetailsList);
		}, function error(response) {
			  console.log("error:"+response);
		});
	
}


$scope.subscribeSMS = function(idd){
	 $rootScope.showFullScreenLoader = true;
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.customer.subscribeSMS,
			  data:idd
			}).then(function success(response) {
				$scope.customerDetailsList.smsSubscriber=true;
				$rootScope.showFullScreenLoader = false;
			}, function error(response) {
				  console.log("error:"+response);
			});
		
	}
	
$scope.unubscribeSMS = function(idd){
	 $rootScope.showFullScreenLoader = true;
	$http({
		  method: 'POST',
		  url: AppUtil.restUrls.customer.usubscribeSMS,
		  data:idd
		}).then(function success(response) {
			$scope.customerDetailsList.smsSubscriber=false;
			$rootScope.showFullScreenLoader = false;
		}, function error(response) {
			  console.log("error:"+response);
		});
	
}

$scope.subscribeWhatsapp = function(idd){
	 $rootScope.showFullScreenLoader = true;
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.customer.subscribeWhatsapp,
			  data:idd
			}).then(function success(response) {
				$scope.customerDetailsList.optWhatsapp="Y";
				 $rootScope.showFullScreenLoader = false;
			}, function error(response) {
				  console.log("error:"+response);
			});
		
	}
	
$scope.unubscribeWhatsapp = function(idd){
	 $rootScope.showFullScreenLoader = true;
	$http({
		  method: 'POST',
		  url: AppUtil.restUrls.customer.usubscribeWhatsapp,
		  data:idd
		}).then(function success(response) {
			$scope.customerDetailsList.optWhatsapp="N";
			$rootScope.showFullScreenLoader = false;
		}, function error(response) {
			  console.log("error:"+response);
		});
	
}
	
	});