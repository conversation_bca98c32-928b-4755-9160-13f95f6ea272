/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerMenuManagementCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http', 'zomatoNewCatalogService', 'swiggyCatalogServiceV1',
        function ($location, $scope, AppUtil, $rootScope, $http, zomatoNewCatalogService, swiggyCatalogServiceV1) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.init = function () {
                $scope.getUnitList();
                $scope.getChannelPartnersList();
                $scope.actionList = ["UPDATE UNIT MENU", "MANAGE OFFER", "BOGO PRODUCTS"];
                $scope.selectedAction = "UPDATE UNIT MENU";
                $scope.selectAction($scope.selectedAction);
            };

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                switch (action) {
                    case "ADD MENU":
                        $scope.initAddMenu();
                        break;
                    case "UPDATE MENU":
                        $scope.initUpdateMenu();
                        break;
                    case "UPDATE UNIT MENU":
                        $scope.initUpdateUnitMenu();
                        break;
                    case "MANAGE OFFER":
                        $scope.initManageOffer();
                        break;
                    case "BOGO PRODUCTS":
                        $scope.initManageOffer();
                        break;
                }
            };

            $scope.initAddMenu = function () {
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
            };

            $scope.initUpdateMenu = function () {
                $scope.resetUnitList();
            };

            $scope.initUpdateUnitMenu = function () {
                $scope.resetRegions();
                $scope.resetChannelPartnersList();
            };

            $scope.initManageOffer = function () {
                $scope.showAddNewOffer = false;
                $scope.resetUnitList();
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                    $scope.unitMap = {};
                    $scope.unitList.map(function (unit) {
                        $scope.unitMap[unit.id] = unit.name;
                    });
                    $scope.getRegions();
                });
            };

            $scope.resetUnitList = function () {
                if ($scope.unitList != null) {
                    $scope.unitList.map(function (unit) {
                        unit.selected = false;
                    });
                }
            };

            $scope.expandUnit = function (unit) {
                $scope.setSelectedUnit(unit);
                $scope.addMenuObj = null;
                $scope.filteredUnits.map(function (u) {
                    if (u.id == unit.id) {
                        u.expand = true;
                        console.log(u);
                    } else {
                        u.expand = false;
                    }
                })
            };

            $scope.getRegions = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.regions
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.regions = [];
                        response.data.map(function (region, index) {
                            $scope.regions.push({id: index, name: region});
                        });
                    } else {
                        bootbox.alert("Error getting unit regions.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getChannelPartnersList = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerManagement.get
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.channelPartners = response.data;
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error getting partner list.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.resetChannelPartnersList = function () {
                if ($scope.channelPartners != null) {
                    $scope.channelPartners.map(function (partner) {
                        partner.selected = false;
                    });
                }
            };

            /*$scope.getUnitProducts = function () {
                if ($scope.productUnit != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.unitPartnerProductsTrimmed + "?unitId=" + $scope.productUnit.id,
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.unitProductList = response.data;
                            $scope.showUnitProducts = true;
                        } else {
                            bootbox.alert("Error getting unit product list.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit.");
                }
            };*/

            $scope.resetRegions = function() {
                $scope.selectedRegion = null;
                $scope.filteredUnits = [];
                $scope.resetUnitList();
            };

            $scope.setSelectedRegion = function (selectedRegion) {
                $scope.showAddNewOffer = false;
                $scope.selectedRegion = selectedRegion;
                $scope.filteredUnits = [];
                $scope.unitList.map(function (unit) {
                    if (unit.region === selectedRegion.name) {
                        $scope.unitChannelPartnerMappings.map(function (mapping) {
                            if(mapping.unit.id == unit.id && mapping.channelPartner.id == $scope.selectedPartner.kettlePartnerId && mapping.status == "ACTIVE") {
                                $scope.filteredUnits.push(unit);
                            }
                        });
                    }
                });
                if($scope.selectedAction == 'UPDATE UNIT MENU') {
                    $scope.getActiveMenuDetailForUnits();
                }

            };

            $scope.selectUnit = function (unit) {
                unit.selected === true ? unit.selected = false : unit.selected = true;
                $scope.showAddNewOffer = false;
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                if($scope.selectedAction == 'ADD MENU') {
                    $scope.currentMenu = null;
                }
            };

            $scope.setSelectedPartner = function (selectedPartner) {
                $scope.showAddNewOffer = false;
                $scope.selectedPartner = selectedPartner;
                if($scope.selectedAction == 'ADD MENU') {
                    $scope.currentMenu = null;
                }
                if($scope.unitChannelPartnerMappings == null || $scope.unitChannelPartnerMappings.length == 0) {
                    $scope.getUnitChannelPartnerMapping();
                }
            };

            $scope.getUnitChannelPartnerMapping = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getAllUnitChannelPartnerMapping
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitChannelPartnerMappings = response.data;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getMenuToAdd = function () {
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner.");
                } else if ($scope.selectedRegion == null) {
                    bootbox.alert("Please select region.");
                } else if ($scope.selectedUnit == null) {
                    bootbox.alert("Please select unit.");
                } else  {
                    $scope.getUnitProductProfile();
                }
            };

            $scope.getUnitMenuToAdd = function () {
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner.");
                } else if ($scope.selectedRegion == null) {
                    bootbox.alert("Please select region.");
                } else  {
                    $scope.getUnitProductProfile();
                }
            };

            $scope.getUnitProductProfile = function () {
                $rootScope.detailLoaderMessage = "Fetching COD menu...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.productProfile + "?partnerOrder=true&region=" +
                        $scope.selectedRegion.name + "&unitId=" + $scope.selectedUnit.id,
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitProductProfile = response.data;
                        //$scope.getRecipeData();
                        $scope.getUnitProductForCafe();
                    } else {
                        bootbox.alert("Error getting unit product profile.");
                        $rootScope.showDetailLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getUnitProductForCafe = function () {
                $rootScope.detailLoaderMessage = "Fetching cafe menu...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.unitProducts,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.dineInMenuProfile = response.data;
                        $scope.getRecipeData();
                    } else {
                        bootbox.alert("Error getting unit product profile.");
                        $rootScope.showDetailLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getRecipeData = function () {
                var unit = angular.copy($scope.unitProductProfile);
                var reqObj = [];
                unit.products.map(function (product) {
                    if (product.classification === "MENU" && product.billType !== "ZERO_TAX" && product.type !== 12) {
                        var obj = {};
                        obj.productId = product.id;
                        obj.recipes = {};
                        product.prices.map(function (price) {
                            obj.recipes[price.dimension] = null;
                        });
                        reqObj.push(obj);
                    }
                });
                $rootScope.detailLoaderMessage = "Fetching web app recipes...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.neoCache.getProductRecipes,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var res = response.data;
                        if (res != null && res.length > 0) {
                            res.map(function (item) {
                                $scope.unitProductProfile.products.map(function (product) {
                                    if (product.id == item.productId) {
                                        product.prices.map(function (price) {
                                            if (item.recipes[price.dimension] != null) {
                                                price.recipe = item.recipes[price.dimension];
                                            }
                                        });
                                    }
                                });
                            });
                        }
                        $scope.getUnitMetaData();
                    } else {
                        bootbox.alert("Error loading web recipes.");
                        $rootScope.showDetailLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getUnitMetaData = function () {
                $rootScope.detailLoaderMessage = "Fetching unit metadata...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.metadata,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var res = response.data;
                        $scope.unitMetadata = res;
                        $scope.getUnitPackagingProfile();
                        /*$scope.addMenuObj = zomatoCatalogService.prepareCatalog($scope.selectedUnit.id, $scope.unitProductProfile, $scope.unitMetadata);
                        console.log(JSON.stringify($scope.addMenuObj));*/
                    } else {
                        bootbox.alert("Error loading unit metadata.");
                        $rootScope.showDetailLoader = false;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getUnitPackagingProfile = function() {
                $rootScope.detailLoaderMessage = "Fetching unit packaging profile...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.posMetaData.packagingProfile + "?unitId="+$scope.selectedUnit.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var res = response.data;
                        $scope.unitMetadata.packagingType = res.packagingType;
                        $scope.unitMetadata.packagingValue = res.packagingValue;
                        //get filtered products for unit
                        $scope.getFilteredProducts();
                        //$scope.getUnitMenuSequence();
                        //$scope.addMenuObj = zomatoCatalogService.prepareCatalog($scope.selectedUnit.id, $scope.unitProductProfile, $scope.unitMetadata);
                    } else {
                        bootbox.alert("Error loading unit packaging profile.");
                        $rootScope.showDetailLoader = false;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getFilteredProducts = function() {
                $rootScope.detailLoaderMessage = "Fetching unit filtered products...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductFilter,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.filteredProducts = response.data;
                        //getting menu sequence
                        $scope.getUnitMenuSequence();
                        //$scope.addMenuObj = zomatoCatalogService.prepareCatalog($scope.selectedUnit.id, $scope.unitProductProfile, $scope.unitMetadata);
                    } else {
                        bootbox.alert("Error fetching unit filtered products.");
                        $rootScope.showDetailLoader = false;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getUnitMenuSequence = function() {
                $rootScope.detailLoaderMessage = "Fetching menu sequence...";
                $rootScope.showDetailLoader = true;
                var request = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedRegion.name,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getMenuSequence,
                    data: request
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var menuSequence = response.data;
                        if($scope.selectedPartner.partnerName == "ZOMATO") {
                            $scope.addMenuObj = zomatoNewCatalogService.prepareCatalogs($scope.selectedUnit.id, $scope.unitProductProfile, $scope.unitMetadata, menuSequence);
                        }
                        if($scope.selectedPartner.partnerName == "SWIGGY") {
                            $scope.addMenuObj = swiggyCatalogServiceV1.prepareCatalog($scope.selectedUnit.id, $scope.unitProductProfile, $scope.unitMetadata, menuSequence);
                        }
                        console.log(JSON.stringify($scope.addMenuObj));
                    } else {
                        bootbox.alert("Error loading unit packaging profile.");
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.showNonMatchingProductDetails = function() {
                $scope.nonMatchedProducts = [];
                $scope.unitProductProfile.products.map(function (prod) {
                    var productFound = false;
                    $scope.dineInMenuProfile.products.map(function (product) {
                        if(prod.id == product.id) {
                            productFound = true;
                            prod.prices.map(function (prc) {
                                var found = false;
                                product.prices.map(function (price) {
                                    if(price.dimension == prc.dimension) {
                                        found = true;
                                    }
                                });
                                if(!found) {
                                    $scope.nonMatchedProducts.push({id:product.id, name:product.name, dimension:prc.dimension});
                                }
                            });
                        }
                    });
                    if(!productFound && prod.type != 8 && prod.type != 43 && [1043,1044].indexOf(prod.id) < 0) {
                        $scope.nonMatchedProducts.push({id:prod.id, name:prod.name, dimension:null});
                    }
                });
            };

            $scope.validateMenu = function() {
                var valid = true;
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    $scope.addMenuObj.menu.categories.forEach(function (cat) {
                        if (valid && cat.category_name == "Others" && cat.items.length > 0) {
                            valid = false;
                            bootbox.alert("Please set category and subcategory for all the unmapped products.");
                        }
                    });
                }
                /*if ($scope.selectedPartner.partnerName == "SWIGGY") {
                    if ($scope.addMenuObj.unmappedProducts.length > 0) {
                        valid = false;
                        bootbox.alert("Please set category and subcategory for all the unmapped products.");
                    }
                }*/
                if (valid) {
                    $scope.showNonMatchingProductDetails();
                    if($scope.nonMatchedProducts.length > 0) {
                        console.log($scope.nonMatchedProducts);
                        valid = false;
                        $("#nonMatchedProductsModal").modal("show");
                    }
                }
                if(valid) {
                    //$scope.sanitizeMenu();
                    $scope.addPartnerMenuForUnit();
                }
            };

            $scope.addPartnerMenuForUnit = function() {
                $scope.sanitizeMenu();
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    $scope.addZomatoMenuForUnit();
                }
                if ($scope.selectedPartner.partnerName == "SWIGGY") {
                    $scope.addSwiggyMenuForUnit();
                    //$scope.downloadSwiggyCatalog();
                }
            };

            $scope.sanitizeMenu = function() {
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    var categories = [];
                    $scope.addMenuObj.menu.categories.map(function (cat) {
                        var category = null;
                        if(cat.category_name != "Others") {
                            category = angular.copy(cat);
                            category.subcategories = [];
                            cat.subcategories.map(function (subCat) {
                                var subCategory = angular.copy(subCat);
                                subCategory.items = [];
                                subCat.items.map(function (item) {
                                    var matched = false;
                                    $scope.nonMatchedProducts.map(function (nProd) {
                                        if(item.item_id == nProd.id) {
                                            matched = true;
                                        }
                                    });
                                    if(!matched && $scope.filteredProducts.indexOf(item.item_id) < 0) {
                                        subCategory.items.push(item);
                                    }
                                });
                                if(subCategory.items.length > 0) {
                                    category.subcategories.push(subCategory);
                                }
                            });
                            category.has_subcategory = (category.subcategories.length > 0) ? 1 : 0;
                        }
                        if(category != null && category.subcategories.length > 0) {
                            categories.push(category);
                        }
                    });
                    if(categories.length > 0) {
                        $scope.addMenuObj.menu.categories = categories;
                    }
                }
            };

            $scope.addPartnerMenu = function () {
                $rootScope.showFullScreenLoader = true;
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: $scope.addMenuObj.menu
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addMenu,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu added successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addZomatoMenuForUnit = function () {
                console.log("Menu set success:::::::::::");
                $("#nonMatchedProductsModal").modal("hide");
                $rootScope.showFullScreenLoader = true;
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: $scope.addMenuObj.menu,
                    employeeId: AppUtil.getCurrentUser().id
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addMenuForUnit,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu request submitted successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addSwiggyMenuForUnit = function () {
                console.log("Menu set success:::::::::::");
                $("#nonMatchedProductsModal").modal("hide");
                $rootScope.showFullScreenLoader = true;
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: $scope.addMenuObj.entity,
                    employeeId: AppUtil.getCurrentUser().id
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addMenuForUnit,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu request submitted successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            /*$scope.downloadSwiggyCatalog = function() {
                $("#nonMatchedProductsModal").modal("hide");
                AppUtil.JSONToCSVConvertor($scope.addMenuObj.items, "swiggy_catalog_" + $scope.selectedUnit.id, true);
                AppUtil.JSONToCSVConvertor($scope.addMenuObj.addons, "swiggy_catalog_" + $scope.selectedUnit.id + "_addons", true);
            };*/

            $scope.getActiveMenuDetailForUnits = function() {
                $rootScope.showFullScreenLoader = true;
                var unitIds = [];
                $scope.filteredUnits.map(function (unit) {
                    unitIds.push(unit.id);
                });
                var data = {
                    unitIds: unitIds,
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getActiveMenuForUnits,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.addActiveMenuDetailToUnits(response.data);
                    } else {
                        bootbox.alert("Error getting active menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addActiveMenuDetailToUnits = function (menuList) {
                var menuMap = {};
                menuList.map(function (menu) {
                    menuMap[menu.unitId] = menu;
                });
                $scope.filteredUnits.map(function (unit) {
                    if(menuMap[unit.id] != null) {
                        unit.activeMenu = menuMap[unit.id];
                        unit.activeMenu.employeeName += " [" +  menuMap[unit.id].employeeId + "]";
                    } else {
                        unit.activeMenu = {addTime: "NA", employeeName: "NA"};
                    }
                })
            };

            $scope.showAddOffer = function () {
                var unitIds = [];
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        unitIds.push(unit.id)
                    }
                });
                if (unitIds.length > 0 && $scope.selectedPartner != null) {
                    $scope.showAddNewOffer = true;
                    $scope.couponCode = null;
                    $scope.offer = null;
                    if (document.getElementById("couponCode") != null) {
                        document.getElementById("couponCode").value = null;
                    }
                } else {
                    bootbox.alert("Please select unit and partner.")
                }
            };

            $scope.setCouponCode = function (couponCode) {
                $scope.couponCode = couponCode;
            };

            $scope.couponSearch = function () {
                if ($scope.couponCode != null) {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.couponSearch,
                        data: $scope.couponCode
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.couponDetail = response.data;
                            $scope.prepareOfferDetail();
                        } else {
                            bootbox.alert("Error getting coupon data.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please enter coupon code");
                }
            };

            $scope.prepareOfferDetail = function () {
                var cd = $scope.couponDetail;
                $scope.offer = {
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    partnerName: $scope.selectedPartner.partnerName,
                    unitId: null,
                    startDate: cd.offer.startDate,
                    endDate: cd.offer.endDate,
                    active: cd.offer.status === "ACTIVE",
                    couponCode: cd.code
                };
                var offerData = null;
                if ($scope.selectedPartner.partnerName === "ZOMATO") {
                    offerData = {
                        offer_id: null,
                        start_date: AppUtil.formatDate(cd.offer.startDate, "yyyy-MM-dd"),
                        end_date: AppUtil.formatDate(cd.offer.endDate, "yyyy-MM-dd"),
                        offer_type: "DISCOUNT",
                        discount_type: cd.offer.type === "FLAT_BILL_STRATEGY" ? "FIXED" : "PERCENTAGE",
                        min_order_amount: cd.offer.minValue,
                        discount_value: cd.offer.offerValue,
                        is_active: $scope.offer.active === true ? 1 : 0,
                    }
                }
                $scope.offer.offerData = offerData;
            };

            $scope.setBogo = function (startDate, endDate) {
                $scope.offer = {
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    partnerName: $scope.selectedPartner.partnerName,
                    unitId: null,
                    startDate: startDate,
                    endDate: endDate,
                    active: true,
                    couponCode: null
                };
                var offerData = null;
                if ($scope.selectedPartner.partnerName === "ZOMATO") {
                    offerData = {
                        offer_id: null,
                        start_date: startDate,
                        end_date: endDate,
                        offer_type: "BOGO",
                        discount_type: null,
                        min_order_amount: 0,
                        discount_value: 0,
                        is_active: 1,
                    }
                }
                $scope.offer.offerData = offerData;
            };

            $scope.addNewOffer = function () {
                $rootScope.showFullScreenLoader = true;
                var reqObj = {unitIds: []};
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        reqObj.unitIds.push(unit.id)
                    }
                });
                reqObj.partnerId = $scope.selectedPartner.kettlePartnerId;
                reqObj.offerDetail = $scope.offer;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addOffer,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        bootbox.alert("Offers added successfully");
                        $scope.showAddNewOffer = false;
                        $scope.getOffers();
                    } else {
                        bootbox.alert("Error adding offer: " + response.data.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getOffers = function () {
                var reqObj = {unitIds: []};
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        reqObj.unitIds.push(unit.id)
                    }
                });
                reqObj.partnerId = $scope.selectedPartner.kettlePartnerId;
                if (reqObj.unitIds.length > 0 && $scope.selectedPartner != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getOffers,
                        data: reqObj
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.offers = response.data;
                            if ($scope.offers.length === 0) {
                                bootbox.alert("No offers found!");
                            }
                        } else {
                            bootbox.alert("Error getting offers.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit and partner.")
                }

            };

            $scope.updateOfferStatus = function (offer, activate) {
                $rootScope.showFullScreenLoader = true;
                var url = AppUtil.restUrls.partnerMetadata.activateOffer;
                if (!activate) {
                    url = AppUtil.restUrls.partnerMetadata.deactivateOffer;
                }
                $http({
                    method: 'POST',
                    url: url + "?partnerOfferId=" + offer.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        offer.active = activate;
                    } else {
                        bootbox.alert("Error updating offer data.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getCurrentMenu = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getMenu,
                    data: {
                        unitId: $scope.selectedUnit.id,
                        region: $scope.selectedUnit.region,
                        kettlePartnerId: $scope.selectedPartner.kettlePartnerId
                    }
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.currentMenu = response.data;
                        $scope.addMenuObj = response.data.menuRequest;
                    } else {
                        bootbox.alert("Error getting menu data.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.refreshMenu = function () {
                if($scope.currentMenu != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.refreshMenu,
                        data: $scope.currentMenu
                    }).then(function success(response) {
                        if (response.status === 200 && response.data === true) {
                            bootbox.alert("Menu sync request sent to partner.");
                        } else {
                            bootbox.alert("Error syncing data.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please press get menu first to fetch latest menu.");
                }
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
            };

            $scope.getUnitProducts = function () {
                if ($scope.selectedUnit != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.unitPartnerProductsTrimmed + "?unitId=" + $scope.selectedUnit.id,
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.unitProductList = response.data;
                            $scope.showUnitProducts = true;
                            if ($scope.selectedAction === 'BOGO PRODUCTS') {
                                if ($scope.selectedPartner != null) {
                                    $scope.getBogoProducts();
                                } else {
                                    bootbox.alert("Please select partner.");
                                    $rootScope.showFullScreenLoader = false;
                                }
                            }
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit.");
                }
            };

            $scope.getBogoProducts = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerBogoProducts,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null && response.data.length > 0) {
                        response.data.map(function (productId) {
                            $scope.unitProductList.map(function (product) {
                                if (productId == product.detail.id) {
                                    product.selected = true;
                                }
                            });
                        });
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setBogoProducts = function () {
                var reqObj = {productIds: [], partnerIds: [$scope.selectedPartner.kettlePartnerId]};
                $scope.unitProductList.map(function (product) {
                    if (product.selected == true) {
                        reqObj.productIds.push(product.detail.id)
                    }
                });
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerBogoProducts,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Bogo products updated successfully.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.checkToday = function (date) {
                if(date != null && date.addTime != null) {
                    return new Date().toDateString() === new Date(date.addTime).toDateString();
                }
                return false;
            };

        }]);