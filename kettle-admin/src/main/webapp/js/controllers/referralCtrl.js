'use strict';

adminapp
		.controller(
				'referralCtrl',
				[
						'$location',
						'$scope',
						'AppUtil',
						'$rootScope',
						'$http',
						function($location, $scope, AppUtil, $rootScope, $http,
								$sce) {

							$scope.init = function() {
								$scope.setText("Please enter values and click submit");
							};

							$scope.addToken = function() {
								$http({
									method : 'POST',
									url : AppUtil.restUrls.referral.generateToken,
									data : "anyUrl"
								}).then(function success(response) {
									$scope.token = response.data;
									bootbox.alert("Vaidate Token added");
								}, function error(response) {
									console.log("error:" + response);
								});
							};
								
							
							$scope.submit = function() {
								var payload = {};
								if ($scope.contact != undefined
										&& $scope.contact != null) {
									payload.contact = $scope.contact;
								}
								if ($scope.name != undefined
										&& $scope.name != null) {
									payload.name = $scope.name;
								}
								if ($scope.refCode != undefined
										&& $scope.refCode != null) {
									payload.signUpRefCode = $scope.refCode;
								}
								if ($scope.email != undefined
										&& $scope.email != null) {
									payload.email = $scope.email;
								}
								if ($scope.otp != undefined
										&& $scope.otp != null) {
									payload.otp = $scope.otp;
								}
								payload.token = $scope.token;
								$http({
									method : 'POST',
									url : AppUtil.restUrls.referral.submit,
									data : payload
								})
										.then(
												function success(response) {
													if (isJson(response.data)) {
														$scope
																.setText(response.data);
														bootbox.alert("Request Submitted");
													} else {
														$("#myDiv")
																.html(
																		response.data
																				.replace(
																						/\<style.*style\>/g,
																						''));
													}

												},
												function error(response) {
													console.log("error:"
															+ response);
													$scope.msg = response.data;
												});
							};

							
							$scope.validate = function() {
								var payload = {};
								if ($scope.contact != undefined
										&& $scope.contact != null) {
									payload.contact = $scope.contact;
								}
								if ($scope.name != undefined
										&& $scope.name != null) {
									payload.name = $scope.name;
								}
								if ($scope.refCode != undefined
										&& $scope.refCode != null) {
									payload.signUpRefCode = $scope.refCode;
								}
								if ($scope.email != undefined
										&& $scope.email != null) {
									payload.email = $scope.email;
								}
								payload.token = $scope.token;
								$http({
									method : 'POST',
									url : AppUtil.restUrls.referral.validate,
									data : payload
								})
										.then(
												function success(response) {
													if (isJson(response.data)) {
														bootbox.alert("Validate Success");
														$scope
																.setText(response.data);
													} else {
														$("#myDiv")
																.html(
																		response.data
																				.replace(
																						/\<style.*style\>/g,
																						''));
													}

												},
												function error(response) {
													console.log("error:"
															+ response);
													$scope.msg = response.data;
												});
							};

							
							function isJson(str) {
								if (typeof str === 'object') {
									return true;
								}
								try {
									JSON.parse(str);
								} catch (e) {
									return false;
								}
								return true;
							}

							$scope.setText = function(data) {
								$("#myDiv").html(
										syntaxHighlight(JSON.stringify(data,
												undefined, 2)));
							};

							function syntaxHighlight(json) {
								json = json.replace(/&/g, '&amp;').replace(
										/</g, '&lt;').replace(/>/g, '&gt;');
								return json
										.replace(
												/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
												function(match) {
													var cls = 'number';
													if (/^"/.test(match)) {
														if (/:$/.test(match)) {
															cls = 'key';
														} else {
															cls = 'string';
														}
													} else if (/true|false/
															.test(match)) {
														cls = 'boolean';
													} else if (/null/
															.test(match)) {
														cls = 'null';
													}
													return '<span class="'
															+ cls + '">'
															+ match + '</span>';
												});
							}

						} ]);