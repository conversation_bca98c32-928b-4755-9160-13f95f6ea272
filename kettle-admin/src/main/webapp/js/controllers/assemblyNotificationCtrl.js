/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        "assemblyNotificationController",
        function ($rootScope, $scope, $location, $http, AppUtil,
                  $cookieStore, $timeout) {

            $scope.init = function () {
                $scope.typeList = ["RECIPE", "PRODUCT", "SACHET", "SWEETENER_ADDON", "CHAI_MONK_1_COMPLETE_RESET", "CHAI_MONK_2_COMPLETE_RESET", "CHAI_MONK_3_COMPLETE_RESET", "CHAI_MONK_4_COMPLETE_RESET", "CHAI_MONK_5_COMPLETE_RESET", "CHAI_MONK_6_COMPLETE_RESET"];
                $scope.selectedType = [];
                $scope.selectedUnit = [];
                $scope.getUnits();
                $scope.storeSelectedCafes = [];
                $scope.currentUserId = AppUtil.getCurrentUser().id;
            };

            $scope.getAllActiveLiveUnits = function (units) {
                var result = [];
                angular.forEach(units, function (unit) {
                    if (unit.status === "ACTIVE" && unit.live) {
                        result.push(unit);
                    }
                });
                return result;
            };

            $scope.getUnits = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.allUnits,
                    params: {
                        category: 'CAFE'
                    }
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.cafelist = $scope.getAllActiveLiveUnits(response.data);
                    $scope.multiSelectSettingsCafes = {
                        showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
                        scrollableHeight: '250px', trackBy: 'id', clearSearchOnClose: true,
                        selectByGroups: $scope.getDistinctRegions(), groupByTextProvider: function(groupValue) { return groupValue;}, groupBy: 'region'
                    };
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });
            };

            $scope.getDistinctRegions = function () {
                var result = [];
                angular.forEach($scope.cafelist, function (unit) {
                    if (result.indexOf(unit.region) === -1) {
                        result.push(unit.region);
                    }
                });
                return result;
            };

            $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option}}</b>'};

            $scope.sendNotification = function () {
                var data = {
                    "unitIds": $scope.selectedUnit.map(function (unit) {
                        return unit.id
                    }),
                    "notificationType": $scope.selectedType,
                    "notificationTriggeredBy" : $scope.currentUserId
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.chaiMonkDashboard.assemblyNotification,
                    data: data
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data) {
                        alert("Notification Send Successfully");
                        window.location.reload();
                    }
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                    alert("Unable to send notification");
                });
            }
        });