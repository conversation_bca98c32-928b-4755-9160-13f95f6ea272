adminapp.controller("kiosOfficectrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

	$scope.paymentMode = 	[{name:"Employee Paid",value:"EMPLOYEE_PAID"},{value:"COMPANY_PAID", name:"Company Paid"}];
	$scope.kiosStatus = 	[{name:"ACTIVE",value:"Active"},{name:"IN ACTIVE",value:"IN_ACTIVE"}];
	
	$scope.selectedPaymentMode=$scope.paymentMode[0];
	$scope.selectedKiosStatus=$scope.kiosStatus[0];


   $scope.init = function () {
	   $scope.domainList = [];
    }
   
   
   $http({
       method: 'GET',
       url: AppUtil.restUrls.unitMetaData.regions
   }).then(function success(response) {
       $scope.regions = response.data;
       console.log($scope.regions);
       $scope.newUnitRegion = $scope.regions[0];
   }, function error(response) {
       console.log("error:" + response);
   });
   
   $http({
		method: 'GET',
		url : AppUtil.restUrls.kioskManagement.kioskCompany,
		}).then(function success(response) {
			$scope.kiosCompanyList = response.data;
			console.log($scope.kiosCompanyList);
			
			
			//console.log("dd=",$scope.totalItems);
			//console.log("listCompany=",$scope.kiosCompanyList);
			
		}, function error(response) {
			console.log("error:"+response);
	});
   
   /*$scope.kiosfullObj=[
     {
         companyId: 1,
         companyName: 'Oyo Company',
         companyDomains: ['@oyo1','@oyo2','@oyo3','@oyo4','@oyo5'],
         contactDetails: {
           contactId: 1,
           name: 'Shikhar',
           email: '<EMAIL>',
           phone: '2423423423',
           contactStatus: 'U-4,Cyber city Gurgaon Haryana'
         },
         country: 'IN',
         companyEmail: '<EMAIL>',
         companyStatus: 'ACTIVE',
         paymentMode: 'COMPANY_PAID'
       },
       {
           companyId: 2,
           companyName: 'Airtel',
           companyDomains: ['@airtel1','@airtel2','@airtel3','@airtel4','@airtel5'],
           contactDetails: {
             contactId: 1,
             name: 'Neeraj',
             email: '<EMAIL>',
             phone: '5356653',
             contactStatus: 'U-5,vibe city Gurgaon Haryana'
           },
           country: 'IN',
           companyEmail: '<EMAIL>',
           companyStatus: 'ACTIVE',
           paymentMode: 'COMPANY_PAID'
         },
         {
             companyId: 3,
             companyName: 'Google',
             companyDomains: ['@google1','@google2','@google3','@google4','@google5'],
             contactDetails: {
               contactId: 1,
               name: 'Raphel',
               email: '<EMAIL>',
               phone: '6434533343',
               contactStatus: 'U-5,Umbrella city Gurgaon Haryana'
             },
             country: 'IN',
             companyEmail: '<EMAIL>',
             companyStatus: 'ACTIVE',
             paymentMode: 'COMPANY_PAID'
           },
           {
               companyId: 4,
               companyName: 'Vodafone Company',
               companyDomains: ['@voda1','@voda2','@voda3','@voda3','@voda4'],
               contactDetails: {
                 contactId: 1,
                 name: 'Ambuj Pandey',
                 email: '<EMAIL>',
                 phone: '9973732423',
                 contactStatus: 'U-6,Ambience Mall Gurgaon Haryana'
               },
               country: 'IN',
               companyEmail: '<EMAIL>',
               companyStatus: 'ACTIVE',
               paymentMode: 'COMPANY_PAID'
             }
 ]*/
   
  // $scope.employeeList = response.data;
	//$scope.currentPage = 1; //current page
	////$scope.entryLimit = 50; //max no of items to display in a page
	//$scope.filteredItems = $scope.kiosfullObj.length; //Initially for no filter  
	//$scope.totalItems = $scope.kiosfullObj.length;
   
	//$scope.selectedOfficeCompanyName=$scope.kiosfullObj[0];
	
	$scope.sort_by = function(predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };
    
    $scope.updatePerm = function(val){
    	if(val==='true'){
    		$scope.permanentAddressSame = true;
    	}else{
    		$scope.permanentAddressSame = false;
    	}
    }
   
    $scope.OfficeStatusChange=function(officeStatus,officeID)
    {
    	 console.log(officeStatus);
  	   if(officeStatus=="ACTIVE")
  		   {
  		   officeStatusData=true;		   
  		   }
  			  else if(officeStatus=="IN_ACTIVE")
  			   {
  				officeStatusData=false;		   
  			   }
  			   else
  				   {
  				 officeStatusData=null;
  				   }
  	   console.log("STATUS="+officeStatus+"officeID="+officeID);
  	   $http({
             method: 'GET',
             url: AppUtil.restUrls.kioskManagement.kiosOfficeStatus + '?officeId=' + officeID+'&activate=' + officeStatusData,
         }).then(function success(response) {
      	   $scope.companyAllOfficeList.forEach(function(officeListData)
      	   {
      		   console.log(officeListData)
      		if(officeListData.officeId==officeID)  
      			{
      			if(officeStatus=="IN_ACTIVE"){
      				officeListData.officeStatus="IN_ACTIVE";
      			}
      			
      			else if(officeStatus=="ACTIVE"){
      				officeListData.officeStatus="ACTIVE";
          			}
      			}
      	   });
      	   
            // $scope.unitlist = response.data;
             console.log("ResultHere=",response);
             
         }, function error(response) {
             console.log("error:" + response);
         });
  	   
    	
    }
   
   $scope.addMoreDomain = function() {
	  console.log("Learning=",$scope.kiosCompanyDomains);
	   var newItemNo = $scope.domainList.length+1;
	    $scope.domainList.push({'id':'choice'+newItemNo});
	  };
	    
	  $scope.removeChoice = function() {
	    var lastItem = $scope.domainList.length-1;
	    $scope.domainList.splice(lastItem);
	  };
   
   console.log("domainList=",$scope.domainList)
     
   $scope.addKiosOffice = function(){
	$scope.action = "Add"
		$scope.officeName="",
		$scope.officeShortCode="";
		$scope.selectedPaymentMode="",
		$scope.newUnitRegion="";
		
		$scope.officeTin="",
		$scope.officeContactName="",
		$scope.officeContactEmail="";
		$scope.officeContactNumber="";
		$scope.officeline1="";
		$scope.officeline2="";
		$scope.officeline3="";
		$scope.officeLocality="";
		$scope.officeCity="";
		$scope.officeZipCode="";
		$scope.officeContact1="";
		$scope.officeContact2="";
		$scope.officeLatitude="";
		$scope.officeLongitude="";
		
		
		
	$("#locationModal").modal("show");
	
}
   
   function validateEmail(email) {
       var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
       return re.test(email);
   }
   
   $scope.submitAddOffice=function()
   {
	   
	   var companyDetailsObj= $scope.selectedOfficeCompanyName;
	   
	   if ($scope.officeName == null || $scope.officeName == "") {
           alert("Please Enter Office Name");
           return;
       }   
	   if ($scope.officeShortCode == null || $scope.officeShortCode == "") {
           alert("Please Enter Office ShortCode");
           return;
       }
	   if ($scope.selectedPaymentMode == null || $scope.selectedPaymentMode == "") {
           alert("Please Select payment mode");
           return;
       }
	   
	   if ($scope.newUnitRegion == null || $scope.newUnitRegion == "") {
           alert("Please Select Region");
           return;
       }
	   
	   if ($scope.officeTin == null || $scope.officeTin == "") {
           alert("Please Enter Tin");
           return;
       }
	   if ($scope.selectedOfficeCompanyName == null || $scope.selectedOfficeCompanyName == "") {
           alert("Please Select Company Name");
           return;
       }
	   
	  /* if ($scope.kiosCompanyEmail == null || !validateEmail($scope.kiosCompanyEmail)) {
           alert("Please fill valid company email");
           return;
       }*/
	   
	   if ($scope.officeContactName == null || $scope.officeContactName == "") {
           alert("Please Enter Office Contact Name");
           return;
       }
	   if ($scope.officeContactEmail == null || !validateEmail($scope.officeContactEmail)) {
           alert("Please Enter Office Contact Email");
           return;
       }
	   if ($scope.officeContactNumber == null || $scope.officeContactNumber == "") {
           alert("Please Enter Office contact number ");
           return;
       }
	   if ($scope.officeline1 == null || $scope.officeline1 == "") {
           alert("Please Enter Office Line 1");
           return;
       }
	   if ($scope.officeLocality == null || $scope.officeLocality == "") {
           alert("Please Enter Office Locality");
           return;
       }
	   if ($scope.officeCity == null || $scope.officeCity == "") {
           alert("Please Enter Office City");
           return;
       }
	   
	   if ($scope.officeZipCode == 'null' || $scope.officeZipCode == "") {
           alert("Please Enter Office Zipcode");
           return;
       }
	   if ($scope.officeContact1 == 'null' || $scope.officeContact1 == "") {
           alert("Please Enter Office Contact 1");
           return;
       }
	   
	   if ($scope.officeLatitude == null || $scope.officeLatitude == "") {
           alert("Please Enter Office Latitude");
           return;
       }
	   
	   if ($scope.officeLongitude == null || $scope.officeLongitude == "") {
           alert("Please Enter Office Longitude");
           return;
       }
	   
	   console.log($scope.officeZipCode+"--"+$scope.officeContact1);
	   if ($scope.officeState == null || $scope.officeState == "") {
           alert("Please Enter Office State");
           return;
       }
	   
	   
 //$scope.domainList.push({'id':'choice0','name':$scope.kiosCompanyDomains});
	   var officeContactStatusObj={
			  name :$scope.officeContactName,
			  code:$scope.officeContactEmail,
			  shortCode:$scope.officeContactNumber
	   }
	   console.log($scope.selectedOfficeCompanyName);
	   
	   var officeAddressObj={
			   line1 :$scope.officeline1,
			   line2:$scope.officeline2,
			   line3:$scope.officeline3,
			   locality: $scope.officeLocality,
			   city: $scope.officeCity,
			   state: $scope.officeState,
			   country:'INDIA',
			   zipCode:  $scope.officeZipCode,
			   contact1: $scope.officeContact1,
			   contact2: $scope.officeContact2,
			   company: $scope.selectedOfficeCompanyName.companyName,
			   latitude: $scope.officeLatitude,
			   longitude: $scope.officeLongitude,
			   addressType: 'OFFICIAL'
		   }
		   
	   var officeObj = {
			   officeName:$scope.officeName,
			   officeShortCode:$scope.officeShortCode,
			   paymentMode:$scope.selectedPaymentMode.value,
			   region:$scope.newUnitRegion,
			   tin:$scope.officeTin,
			   companyDetails:companyDetailsObj,
			   officeAddress:officeAddressObj,
			   officeContact:officeContactStatusObj,
			   country:'INDIA',
			   officeStatus:'IN_ACTIVE'
			}
	   
	   //console.log("officeObj=",officeObj);
	   $rootScope.showFullScreenLoader = true;
	   $http({
			method : 'POST',
			url : AppUtil.restUrls.kioskManagement.kioskOffice,
			data :officeObj,
			headers:{
			'Access-Control-Allow-Origin': '*'
			}
		})
		.then(
		function success(response) {
			alert("Submitted Successfully");
			$rootScope.showFullScreenLoader = false;
			$scope.companyAllOfficeList.push(response.data);
			//$("#btnSubmit").show();
			 $("#officeModal").modal("hide");
			//console.log("res",response);
		});
   }
   
 
   $scope.showCompanyOfficeList = function(companyNameResult) {
	   console.log("SingleObj=",companyNameResult);
	   
	   
	   $scope.kiosCompanyList.forEach(function(allCompanyList)
	   {
		  if(companyNameResult.companyId==allCompanyList.companyId){
			  $scope.companyAllOfficeList=allCompanyList.officeList;
		  }
		   
	   })
	   
		console.log("OFFICE=",$scope.companyAllOfficeList)
		$scope.currentPage = 1; //current page
		$scope.entryLimit = 50; //max no of items to display in a page
		$scope.filteredItems = $scope.companyAllOfficeList.length; //Initially for no filter  
		$scope.totalItems = $scope.companyAllOfficeList.length;
		
 };
		    
		  
   
   
   $("#addOfficeIdDiv").click(function(){
       $("#officeModal").modal({backdrop: false});
   });
   
});