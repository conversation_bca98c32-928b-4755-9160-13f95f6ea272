/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("productDetailsCtrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil, $stateParams) {

    var dimensionInfo = null;
    var selectedDimension = null;

    $scope.init = function () {
        $rootScope.enableScreenFilter = true;
        
        $scope.isProductRecipeMapping = $stateParams.isProductRecipeMapping;

        $scope.getAllDimensionsAndPriceProfile();
        $scope.getAllProducts();
        $scope.getAllBrands();
        $scope.productInfo = {};
        $scope.regions = [];
        $scope.cities = [];
        $scope.productDimension = {};
        $scope.selectedProduct = {};
        $scope.prodDetails = {};
        $scope.allProdcutsInfo = [];
        $scope.selectedPricingProfiles = [];
        $scope.pricingProfileNames = [];
        $scope.pricingProfileMap = new Map();
        $scope.filteredCities = [];
        $scope.trimmedRegions = [];
        $scope.storeSelectedRegion=[];

        dimensionInfo = {};
        initCheckBoxModal();
        $scope.selectedRegion = null;
        $scope.unitType = ["CAFE", "COD", "EMPLOYEE_MEAL"];
        $scope.pricingStatusList = getStatusMap();
        $scope.isDeliveryOnlyProductMap = getStatusMap();
        console.log("Logging DeliveryOnlyProductMap :::", $scope.isDeliveryOnlyProductMap);
        $scope.selectedBrand = null;
    };

    function getStatusMap(){
        return [
            {id:1, name:"IN_ACTIVE", value:"IN_ACTIVE"},
            {id:2, name:"ACTIVE", value:"ACTIVE"}
        ];
    }
    function initCheckBoxModal() {
        $scope.checkBoxModal = {};

        $scope.checkBoxModal.checkAll = false;
        $scope.checkBoxModal.checkAllPrice = false;
        $scope.checkBoxModal.checkAllProfile = false;
        $scope.checkBoxModal.checkAllStatus = false;
        $scope.checkBoxModal.checkAllAlias = false;
        $scope.checkBoxModal.checkAllDescription = false;
        $scope.checkBoxModal.checkAllDeliveryOnlyProducts= false;

        $scope.checkBoxModal.updatedPrice = '';
        $scope.checkBoxModal.updatedProfile = '';
        $scope.checkBoxModal.updatedStatus = null;
        //ss
        $scope.checkBoxModal.updatedProductAlias = '';
        $scope.checkBoxModal.updatedDimensionDescription = '';
        $scope.checkBoxModal.updatedDeliveryOnlyProducts= null;

        $scope.productListDetails = {};
        $scope.requestObject = [];
    }

    function sortProductList(){
        $scope.productsInfo.sort((a, b) => {
            var fa = a.name.replace(/\s/g,'').toLowerCase();
            var fb = b.name.replace(/\s/g,'').toLowerCase();
            return fa.localeCompare(fb);
        });
    }

    $scope.getAllProducts = function () {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.products
        }).then(function success(response) {
            $scope.productsInfo = response.data;
            sortProductList();
            $scope.allProdcutsInfo = $scope.productsInfo;
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    function mapPricingProfiles(){
        for(var i in $scope.pricingProfiles){
            $scope.pricingProfileNames.push($scope.pricingProfiles[i].name);
            $scope.pricingProfileMap.set($scope.pricingProfiles[i].name, $scope.pricingProfiles[i].id);
        }
    }

    $scope.getAllDimensionsAndPriceProfile = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.listTypes
        }).then(function success(response) {
            dimensionInfo = response.data.DIMENSION;
            var pricingProfiles = response.data.PRICING_PROFILE;
            if (pricingProfiles != null && pricingProfiles.length > 0) {
                var category = pricingProfiles[0];
                $scope.pricingProfiles = [];
                if (angular.equals(category['detail']['name'], "Pricing Profile") && angular.equals(category['detail']['status'], "ACTIVE")) {
                    for (var itr in category['content']) {
                        if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                            $scope.pricingProfiles.push(category['content'][itr]);
                        }
                    }
                }
                mapPricingProfiles();
            }
        });
    };

    $scope.getAllBrands = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands
        }).then(function success(response) {
            $scope.brandList = response.data;
            var allBrands = {};
            allBrands.brandId = -1;
            allBrands.brandName = 'ALL';
            $scope.brandList.unshift(allBrands);

        });
    };

    $scope.filterProductsBrandWise = function () {
        if($scope.allProdcutsInfo != null){
            if(!angular.equals($scope.selectedBrand, '-1')){
                var filteredProducts = [];
                for(var i in $scope.allProdcutsInfo){
                    if(angular.equals($scope.allProdcutsInfo[i].brandId.toString(), $scope.selectedBrand.toString())){
                        filteredProducts.push($scope.allProdcutsInfo[i]);
                    }
                }
                $scope.productsInfo = filteredProducts;
            } else if(!angular.equals($scope.productsInfo.length, $scope.allProdcutsInfo.length)){
                $scope.productsInfo = $scope.allProdcutsInfo;
            }
        }
    };

    $scope.changeProduct = function (selectedProductId) {
        if (selectedProductId == null || selectedProductId === undefined) {
            return false;
        }
        $scope.productDimension = {};
        $scope.prodDetails.dimensionId = null;
        $scope.selectedRegion = null;
        selectedDimension = null;
        $scope.regions = [];
        $scope.cities = [];
        for (var i = 0; i < $scope.productsInfo.length; i++) {
            if ($scope.productsInfo[i].id == selectedProductId) {
                $scope.selectedProduct = $scope.productsInfo[i];
                break;
            }
        }

        for (var i = 0; i < dimensionInfo.length; i++) {
            if (dimensionInfo[i].detail.id == $scope.selectedProduct.dimensionProfileId) {
                $scope.productDimension = dimensionInfo[i];
                break;
            }
        }
        initCheckBoxModal();
    };

    $scope.changeDimension = function () {
        for (var i = 0; i < $scope.productDimension.content.length; i++) {
            if ($scope.productDimension.content[i].id == $scope.prodDetails.dimensionId) {
                selectedDimension = $scope.productDimension.content[i];
                break;
            }
        }
        $scope.selectedRegion = null;
        getCitiesByZone();
        initCheckBoxModal();
    };
    $scope.storeSelectedRegion=[];
    $scope.selectedCities=[];
    $scope.filteredCities=[];

    $scope.$watch('storeSelectedRegion', function () {
        $scope.getAllCitiesForSelectedRegions();
    }, true);

    $scope.getAllCitiesForSelectedRegions = function () {
        $scope.filteredCities = [];
        for (var i = 0; i < $scope.storeSelectedRegion.length; i++) {
            $scope.filteredCities = $scope.filteredCities.concat($scope.cities[$scope.storeSelectedRegion[i]]);
        }

        if($scope.storeSelectedRegion.length==0) {
            for (var i = 0; i < $scope.cities.size; i++) {
                $scope.filteredCities = $scope.filteredCities.concat($scope.cities[i]);
            }
        }

        $scope.selectedCities = $scope.selectedCities.filter(function (item) {
            return $scope.filteredCities.includes(item);
        });

    }

    $scope.multiSelectSettings = {
        showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
        scrollableHeight: '200px'
    };
    $scope.multiSelectSettingsForCities = {
        showEnableSearchButton: true, template: '<b> {{option.value}}</b>', scrollable: true,
        scrollableHeight: '200px'
    }
    $scope.getAllUnitProductMapping=function(){
        if($scope.storeSelectedRegion.length==0)
        {
            alert("Please select region")
            return;
        }
       var pricingProfileList = "";
        for(var i in $scope.selectedPricingProfiles) {
            pricingProfileList+= $scope.pricingProfileMap.get($scope.selectedPricingProfiles[i])+',';
        }
        $rootScope.showFullScreenLoader = true;

        var companyIds = {
            1: 1000,
            2: 1001,
            3: 1002,
            4: 1003,
            5: 1004,
            6: 1005
        };

        var cities = $scope.selectedCities.map(city => city.key).join(",");
        var regions = $scope.storeSelectedRegion.map(r => r).join(",");

        var payload = $.param({
            unitCategory: $scope.prodDetails.unitType,
            productId: $scope.prodDetails.productId,
            dimensionId: $scope.prodDetails.dimensionId,
            pricingProfileIds: pricingProfileList,
            locationIds: cities,
            region: regions,
            companyId: companyIds[$scope.selectedBrand]
        });
        var payloadString = Object.entries(payload)
            .filter(([_, v]) => v !== "" && v !== null && v !== undefined)
            .map(([k, v]) => encodeURIComponent(k) + "=" + encodeURIComponent(v))
            .join("&");

        console.log("Payload string  ", payloadString);
        $http({
            url: AppUtil.restUrls.productMetaData.productPriceMappingsProfileWise,
            method: "POST",
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            data: payload
        }).then(function success(response) {
            initCheckBoxModal();
            $scope.productListDetailsAll = response.data;
            console.log("$scope.productListDetails ",$scope.productListDetailsAll);
            $scope.allProductListDetails = $scope.productListDetailsAll;
            $scope.productListDetails = $scope.productListDetailsAll.filter(function(data){
                                return data.unit.status == "ACTIVE";
             });
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                $scope.prodDetails[$scope.productListDetails[i].unit.id] = {};
                $scope.prodDetails[$scope.productListDetails[i].unit.id].checked = false;
                $scope.prodDetails[$scope.productListDetails[i].unit.id].price = '';
                $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = '';
                $scope.prodDetails[$scope.productListDetails[i].unit.id].status = null;
                $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail = $scope.productListDetails[i].price;
                $scope.prodDetails[$scope.productListDetails[i].unit.id].id = $scope.productListDetails[i].id;
                $scope.prodDetails[$scope.productListDetails[i].unit.id].unit = $scope.productListDetails[i].unit;
                $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = null;
            }
            getRecipeProfilesOfProduct($scope.prodDetails.productId, $scope.prodDetails.dimensionId);
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };
    $scope.getUnitProductDetails = function (region) {
        bootbox.confirm("Do you want to load details of " + region + " region ?", function (result) {
            if (result == true) {
                $scope.selectedRegion = region;
                console.log(region);
                $rootScope.showFullScreenLoader = true;
                var payload = $.param({
                    unitCategory: $scope.prodDetails.unitType,
                    unitRegion: (region == "ALL" ? null : region),
                    productId: $scope.prodDetails.productId,
                    dimensionId: $scope.prodDetails.dimensionId
                });

                $http({
                    url: AppUtil.restUrls.productMetaData.productPriceMapping,
                    method: "POST",
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    data: payload
                }).then(function success(response) {
                    initCheckBoxModal();
                    $scope.productListDetailsAll = response.data;
                    $scope.productListDetails = $scope.productListDetailsAll.filter(function(data){
                    return data.unit.status == "ACTIVE";
                    });

                    	// console.log("$scope.productListDetails ",$scope.productListDetails.length);

                    for (var i = 0; i < $scope.productListDetails.length; i++) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id] = {};
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].checked = false;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].price = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].status = null;
                        //ss
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].description = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail = $scope.productListDetails[i].price;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].id = $scope.productListDetails[i].id;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].unit = $scope.productListDetails[i].unit;
                    }
                    getRecipeProfilesOfProduct($scope.prodDetails.productId, $scope.prodDetails.dimensionId);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });
            }
        });
    };

    $scope.updateAll = function () {
        if ($scope.checkBoxModal.checkAll === true) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                $scope.prodDetails[$scope.productListDetails[i].unit.id].checked = true;
            }
        } else if ($scope.checkBoxModal.checkAll === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                $scope.prodDetails[$scope.productListDetails[i].unit.id].checked = false;
            }
        }
    };

    $scope.changeRow = function (checked, unitProdDetails, detail) {
        console.log("prodDetails[detail.unit.id].checked",checked);
        if(checked && detail != null){
            unitProdDetails.price = detail.price;
            unitProdDetails.profile = detail.profile;
            unitProdDetails.status=detail.status;
            unitProdDetails.alias=detail.aliasProductName;
            unitProdDetails.description=detail.dimensionDescriptor;
            unitProdDetails.isDeliveryOnlyProduct = detail.isDeliveryOnlyProduct;
        }else{
            unitProdDetails.price = '';
            unitProdDetails.profile = '';
            unitProdDetails.alias='';
            unitProdDetails.description='';
            //check on ui if there is a need to set this flag
            // unitProdDetails.isDeliveryOnlyProduct = false;
        }
    };

    /*$scope.changeProfile = function(newProfile, detail){

    };*/

    $scope.changeAllProfile = function () {
        if ($scope.checkBoxModal.checkAllProfile === true) {
            if ($scope.checkBoxModal.updatedProfile == '') {
                alert("Please enter new profile.");
                $scope.checkBoxModal.checkAllProfile = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                    if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                        if ($scope.prodDetails[$scope.productListDetails[i].unit.id].price == '') {
                            var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].price = (priceDetail == null ? priceDetail : priceDetail.price);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].status = (priceDetail == null ? priceDetail : priceDetail.status);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = (priceDetail == null ? priceDetail : priceDetail.aliasProductName);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].description = (priceDetail == null ? priceDetail : priceDetail.dimensionDescriptor);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = (priceDetail == null ? priceDetail : priceDetail.isDeliveryOnlyProduct);
                        }
                    }
                }
            }
        } else if ($scope.checkBoxModal.checkAllProfile === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {

                if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                    $scope.checkBoxModal.updatedProfile = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
            }
        }
        }
    };

    $scope.changeAllStatus = function () {
        if ($scope.checkBoxModal.checkAllStatus === true) {
            if ($scope.checkBoxModal.updatedStatus == null) {
                alert("Please enter new status.");
                $scope.checkBoxModal.checkAllStatus = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                   if( $scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                       $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                       if ($scope.prodDetails[$scope.productListDetails[i].unit.id].price == '') {
                           var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                           $scope.prodDetails[$scope.productListDetails[i].unit.id].price = (priceDetail == null ? priceDetail : priceDetail.price);
                           $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = (priceDetail == null ? priceDetail : priceDetail.profile);
                       $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = (priceDetail == null ? priceDetail : priceDetail.aliasProductName);
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].description = (priceDetail == null ? priceDetail : priceDetail.dimensionDescriptor);}
                       $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = (priceDetail == null ? priceDetail : priceDetail.isDeliveryOnlyProduct);
                   }
                }
            }
        } else if ($scope.checkBoxModal.checkAllStatus === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked ) {

                    $scope.checkBoxModal.updatedStatus = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
                }
            }
        }
    };

    $scope.changeAllPrice = function () {
        if ($scope.checkBoxModal.checkAllPrice === true) {
            if ($scope.checkBoxModal.updatedPrice ==null || $scope.checkBoxModal.updatedPrice <0) {
                alert("Please enter new price.");
                $scope.checkBoxModal.checkAllPrice = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                    if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked){
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    if ($scope.prodDetails[$scope.productListDetails[i].unit.id].profile == '') {
                        var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = (priceDetail == null ? priceDetail : priceDetail.profile);
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].status = (priceDetail == null ? priceDetail : priceDetail.status);
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = (priceDetail == null ? priceDetail : priceDetail.aliasProductName);
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].description = (priceDetail == null ? priceDetail : priceDetail.dimensionDescriptor);
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = (priceDetail == null ? priceDetail : priceDetail.isDeliveryOnlyProduct);

                    }
                    }
                }
            }
        } else if ($scope.checkBoxModal.checkAllPrice === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {

                if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                    $scope.checkBoxModal.updatedPrice = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
                }
            }
        }
    };

    //
    $scope.changeAllAlias = function () {
        if ($scope.checkBoxModal.checkAllAlias === true) {
            if ($scope.checkBoxModal.updatedProductAlias == '') {
                alert("Please enter new Product Alias.");
                $scope.checkBoxModal.checkAllAlias = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                    if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                        if ($scope.prodDetails[$scope.productListDetails[i].unit.id].profile == '') {
                            var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = (priceDetail == null ? priceDetail : priceDetail.profile);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].price = (priceDetail == null ? priceDetail : priceDetail.price);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].status = (priceDetail == null ? priceDetail : priceDetail.status);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].description = (priceDetail == null ? priceDetail : priceDetail.dimensionDescriptor);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = (priceDetail == null ? priceDetail : priceDetail.isDeliveryOnlyProduct);
                        }
                    }
                }
            }
        } else if ($scope.checkBoxModal.checkAllAlias === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                if ($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {

                    $scope.checkBoxModal.updatedProductAlias = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
                }
            }
        }
    };
    //
    $scope.changeAllDescription = function () {
        if ($scope.checkBoxModal.checkAllDescription === true) {
            if ($scope.checkBoxModal.updatedDimensionDescription == '') {
                alert("Please enter new Dimension Description.");
                $scope.checkBoxModal.checkAllDescription = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                    if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                        if ($scope.prodDetails[$scope.productListDetails[i].unit.id].profile == '') {
                            var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = (priceDetail == null ? priceDetail : priceDetail.profile);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].status = (priceDetail == null ? priceDetail : priceDetail.status);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].price = (priceDetail == null ? priceDetail : priceDetail.price);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = (priceDetail == null ? priceDetail : priceDetail.aliasProductName);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = (priceDetail == null ? priceDetail : priceDetail.isDeliveryOnlyProduct);
                        }
                    }
                }
            }
        } else if ($scope.checkBoxModal.checkAllDescription === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                if ($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                    $scope.checkBoxModal.updatedDimensionDescription = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;

                }
            }
        }
    };

    $scope.changeAllDeliveryOnlyProducts = function () {
        if ($scope.checkBoxModal.checkAllDeliveryOnlyProducts === true) {
            if ($scope.checkBoxModal.updatedDeliveryOnlyProducts == null) {
                alert("Please enter new status.");
                $scope.checkBoxModal.checkAllDeliveryOnlyProducts = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                    if ($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
                        if ($scope.prodDetails[$scope.productListDetails[i].unit.id].price == '') {
                            var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].price = (priceDetail == null ? priceDetail : priceDetail.price);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = (priceDetail == null ? priceDetail : priceDetail.profile);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = (priceDetail == null ? priceDetail : priceDetail.aliasProductName);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].description = (priceDetail == null ? priceDetail : priceDetail.dimensionDescriptor);
                        }
                    }
                }
            }
        } else if ($scope.checkBoxModal.checkAllDeliveryOnlyProducts === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {
                if ($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                    $scope.checkBoxModal.updatedDeliveryOnlyProducts = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
                }
            }
        }
    }

    $scope.submitDetails = function () {
        $scope.requestObject = [];
        for (var key in $scope.prodDetails) {
            if (typeof $scope.prodDetails[key] == "object" && $scope.prodDetails[key].checked == true) {
                console.log("price is ", $scope.prodDetails[key].price);
                if ($scope.prodDetails[key].profile != '' && $scope.prodDetails[key].profile != null) {
                    var request = {};
                    request.unit = getUnit($scope.prodDetails[key].unit);
                    request.price = getPrice($scope.prodDetails[key]);
                    request.product = getProduct();
                    request.id = $scope.prodDetails[key].id;
                    $scope.requestObject.push(request);
                } else {
                    alert("Please provide details for fields to update");
                    $scope.requestObject = [];
                    return false;
                }
            }
        }
        if ($scope.requestObject.length > 0) {
            $("#productDetailsModal").modal("show");
        } else {
            alert("Please provide details for fields to update");
        }
    };

    $scope.submitUpdatePriceList = function () {
        $rootScope.showFullScreenLoader = true;
        console.log("ReqObj on sending req for submitUpdatePriceList",$scope.requestObject);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.productPriceUpdate+"?updatedBy="+$rootScope.userData.id,
            data: $scope.requestObject
        }).then(function success(response) {
            if (response.status == 200) {
                console.log(response.data);
                alert("Product details updated successfully!");
                $rootScope.showFullScreenLoader = false;
                window.location.reload();
            } else {
                console.log(response);
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    function getPrice(detail) {
        var price = {
            id: (detail.priceDetail != null ? detail.priceDetail.id : detail.priceDetail),
            dimension: (detail.priceDetail != null ? detail.priceDetail.dimension : selectedDimension.code),
            price: detail.price == '' ? null : detail.price,
            codCost: null,//(detail.priceDetail.codCost == null ? 0 : detail.priceDetail.codCost),
            cost: null,//(detail.priceDetail.cost == null ? 0 : detail.priceDetail.cost),
            buffer: null,//(detail.priceDetail.buffer == null ? 0 : detail.priceDetail.buffer),
            threshold: null,//(detail.priceDetail.threshold == null ? 0 : detail.priceDetail.threshold),
            profile: detail.profile,
            status: detail.status,
            aliasProductName: detail.alias == '' ? null : detail.alias,
            dimensionDescriptor: detail.description == '' ? null : detail.description,
            isDeliveryOnlyProduct:detail.isDeliveryOnlyProduct==null || (detail.isDeliveryOnlyProduct==="" && detail.isDeliveryOnlyProduct.length==0)|| (detail.isDeliveryOnlyProduct==="IN_ACTIVE")||!detail.isDeliveryOnlyProduct?false:true,
            currentPrice: (detail.priceDetail != null ? detail.priceDetail.price : detail.priceDetail),
            currentProfile: (detail.priceDetail != null ? detail.priceDetail.profile : detail.priceDetail),
            currentStatus: (detail.priceDetail != null ? detail.priceDetail.status : detail.priceDetail),
            //ss
            currentAliasProductName:(detail.priceDetail != null ? detail.priceDetail.aliasProductName : detail.priceDetail),
            currentDimensionDescriptor:(detail.priceDetail != null ? detail.priceDetail.dimensionDescriptor : detail.priceDetail),
        };
        return price;
    }

    function getUnit(unitDetail) {
        var unit = {
            id: unitDetail.id,
            name: unitDetail.name,
            code: null,
            shortCode: null,
            type: null,
            status: unitDetail.status
        };
        return unit;
    }

    function getProduct() {
        var product = {
            id: $scope.selectedProduct.id,
            name: $scope.selectedProduct.name,
            code: null,
            shortCode: null,
            type: null,
            status: $scope.selectedProduct.status
        };
        return product;
    }

    function getCitiesByZone() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.locationByZone
        }).then(function success(response) {
            $scope.cities = response.data;
            $scope.trimmedRegions = Object.keys($scope.cities);
            $scope.changeFilteredCities();
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.changeFilteredCities = function() {
        $scope.filteredCities = [];
        for (var i = 0; i < $scope.cities.size; i++) {
            $scope.filteredCities = $scope.filteredCities.concat($scope.cities[i]);
        }
    }

    function getRecipeProfilesOfProduct(productID, dimensionId) {
        $http({
            url: AppUtil.restUrls.recipeManagement.recipeProfileOfProductDimension,
            method: "GET",
            params: {productId: productID, dimensionId: dimensionId}
        }).success(function (data) {
            console.log(data);
            $scope.recipeProfiles = [];
            var i = 0;
            for (i = 0; i < data.length; i++) {
                if ($scope.recipeProfiles.indexOf(data[i]) == -1) {
                    $scope.recipeProfiles.push(data[i]);
                }
            }
        });
    }

});
