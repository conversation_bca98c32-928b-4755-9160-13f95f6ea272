/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("deliveryMappingctrl", function ($scope, $rootScope, $http, AppUtil) {
    $scope.prodListData = {};
    $scope.productFinalList = {};
    $scope.loading = false;

    $http({
        method: 'GET',
        url: AppUtil.restUrls.unitMetaData.families
    }).then(function success(response) {
        $scope.families = response.data;
        $scope.families.push('ALL');
        console.log("families==" + $scope.families);
        $scope.newUnitFamily = $scope.families[0];
    }, function error(response) {
        console.log("error:" + response);
    });

    $http({
        method: 'GET',
        url: AppUtil.restUrls.unitMetaData.regions
    }).then(function success(response) {
        $scope.regions = response.data;
        $scope.regions.push('ALL');
        $scope.newUnitRegion = $scope.regions[0];
    }, function error(response) {
        console.log("error:" + response);
    });


    $http({
        method: 'GET',
        url: AppUtil.restUrls.posMetaData.allDeliveryPartners
    }).then(function success(response) {
        $scope.partnersList = response.data;
        console.log("partnerList=", $scope.partnersList);
        var testarr = [];
        testarr = getPartnerDetails(testarr, $scope.partnersList);
        $scope.refPartnerLists = testarr;
        console.log("partnerList111=", $scope.refPartnerLists);
    }, function error(response) {
        console.log("error:" + response);
    });


    function getPartnerDetails(testarr, resPartnerDetails) {
        resPartnerDetails.forEach(function (chkdetailPartner) {
            if (chkdetailPartner.type === 'EXTERNAL' || chkdetailPartner.status === 'ACTIVE') {
                testarr.push(chkdetailPartner);
            }
        });
        return testarr;
    }

    function showDeliveryDetailsResult() {
        alert("here73");

    }

    $scope.ViewUnitDeliveryDataList = function () {
        if ($scope.newUnitFamily == null || $scope.newUnitFamily == "") {
            alert("Please Choose Family Type");
            return false;
        }
        if ($scope.newUnitRegion == null || $scope.newUnitRegion == "") {
            alert("Please choose region");
            return false;
        }

    }
});