/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerProductStockCtrl', ['$location', '$scope', 'AppUtil', '$rootScope', '$http',
    function ($location, $scope, AppUtil, $rootScope, $http) {

        $scope.init = function () {
            $scope.channelPartnerList = [];
            $scope.getUnitList();
            $scope.getChannelPartnersList();
            $scope.getAllBrands();
            $scope.channelPartners = [];
            $scope.showUnitProducts = false;
            $scope.unitProductList = [];
            $scope.selectedUnit = null;
        };

        $scope.getAllBrands = function () {
            if ($scope.brands == null || $scope.brands.length === 0) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.brandManagement.getAllBrands
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.brands = response.data;
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error getting brands.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            }
        };

        $scope.getChannelPartnersList = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.partnerManagement.get
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.channelPartnerList = response.data;
                    $scope.channelPartners = [];
                    $scope.channelPartnerList.map(function (partner) {
                        if (partner.partnerStatus === "ACTIVE") {
                            $scope.channelPartners.push({
                                id: partner.kettlePartnerId,
                                name: partner.partnerName,
                                selected: false
                            });
                        }
                    });
                } else {
                    bootbox.alert("Error loading channel partner list.");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.getUnitList = function () {
            $scope.unitList = [];
            AppUtil.getUnitList(function (list) {
                $scope.unitList = list;
            });
        };

        $scope.resetUnitList = function () {
            $scope.unitList.map(function (unit) {
                unit.selected = false;
            });
        };

        $scope.resetChannelPartnersList = function () {
            $scope.channelPartners.map(function (partner) {
                partner.selected = false;
            });
        };

        $scope.setSelectedUnit = function (selectedUnit) {
            $scope.selectedUnit = selectedUnit;
        };

        $scope.setSelectedBrand = function (selectedBrand) {
            $scope.selectedBrand = selectedBrand;
        };

        $scope.setSelectedPartner = function (selectedPartner) {
            $scope.selectedPartner = selectedPartner;
            if ($scope.selectedAction == "PRODUCT TAGS") {
                $scope.showTagsView = false;
                $scope.partnerTags = [];
                $scope.newTag = {id: null, name: null};
                $scope.showProductTagMappingView = false;
                $scope.productsTagsMappings = [];
            }
        };

        $scope.getUnitProducts = function () {
            if ($scope.selectedUnit != null) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.unitPartnerProductsTrimmed + "?unitId=" + $scope.selectedUnit.id +
                    "&partnerId=" + $scope.selectedPartner.id + "&brandId=" + $scope.selectedBrand.brandId,
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitProductList = response.data;
                        $scope.showUnitProducts = true;
                        $rootScope.showFullScreenLoader = false;

                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            } else {
                bootbox.alert("Please select unit.");
            }
        };

        $scope.setSelectedProduct = function (selectedProduct) {
            $scope.selectedProduct = selectedProduct;
        };

        $scope.getProductStockSnapshot = function () {
            if ($scope.selectedUnit != null) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerReports.getUnitProductStockSnapshot + "?productId=" + $scope.selectedProduct.detail.id,
                    data: {
                        partnerId: $scope.selectedPartner.id,
                        brandId: $scope.selectedBrand.brandId,
                        unitId: $scope.selectedUnit.id
                    }
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitProductStock = response.data;
                        $scope.plotChart();
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            } else {
                bootbox.alert("Please select unit.");
            }
        };

        $scope.plotChart = function () {
            var labels = [];
            var stockData = [];
            $scope.unitProductStock.productStockSnapshot.map(function (data) {
                labels.push(data.updateTime);
                stockData.push(data.stockStatus === "STOCK_IN" ? 1 : 0)
            });
            const data = {
                labels: labels,
                datasets: [{
                    label: $scope.selectedProduct.detail.name + " - " + $scope.selectedUnit.name,
                    backgroundColor: 'rgb(255, 99, 132)',
                    borderColor: 'rgb(255, 99, 132)',
                    data: stockData,
                    tension: 0.1
                }]
            };
            const config = {
                type: 'line',
                data,
                options: {
                    scales: {
                        y: {
                            suggestedMin: 0,
                            suggestedMax: 1.1
                        }
                    }
                }
            };
            var myChart = new Chart(
                document.getElementById('myChart'),
                config
            );
        }

    }]
);