/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('webappLocalitiesCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http',
        function ($location, $scope, AppUtil, $rootScope, $http) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.init = function () {
                $scope.channelPartnerList = [];
                $scope.getLocalities();
                $scope.getUnitList();
                $scope.channelPartners = [];
                $scope.today = new Date();
            };

            $scope.getLocalities = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.neoData.getLocalities
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.localityMappings = response.data;

                    } else {
                        bootbox.alert("Error loading localities list.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                });
            };

            $scope.resetUnitList = function () {
                $scope.unitList.map(function (unit) {
                    unit.selected = false;
                });
            };

            $scope.changeLocalityMapping = function (mapping) {
                $scope.updateMapping = angular.copy(mapping);
                $scope.unitList.map(function (unit) {
                    if (unit.id == $scope.updateMapping.primaryUnitId) {
                        $scope.editMappingPrimaryUnit = unit;
                    }
                    if (unit.id == $scope.updateMapping.secondaryUnitId) {
                        $scope.editMappingSecondaryUnit = unit;
                    }
                });
                $("#editLocalityMappingModal").modal("show");
            };

            $scope.updateLocalityMapping = function () {
                $("#editLocalityMappingModal").modal("hide");
                $scope.updateMapping.primaryUnitName = $scope.editMappingPrimaryUnit.name;
                $scope.updateMapping.primaryUnitId = $scope.editMappingPrimaryUnit.id;
                if($scope.editMappingSecondaryUnit != null) {
                    $scope.updateMapping.secondaryUnitName = $scope.editMappingSecondaryUnit.name;
                    $scope.updateMapping.secondaryUnitId = $scope.editMappingSecondaryUnit.id;
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.neoData.updateLocalities,
                    data: [$scope.updateMapping]
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Mapping updated successfully!");
                        $scope.getLocalities();
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error occurred.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.removeLocalities = function () {
                $("#editLocalityMappingModal").modal("hide");
                var data = [];
                $scope.localityMappings.map(function (mapping) {
                    if (mapping.selected == true) {
                        data.push(mapping);
                    }
                });
                if (data.length > 0) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.neoData.removeLocalities,
                        data: data
                    }).then(function success(response) {
                        if (response.status === 200 && response.data == true) {
                            bootbox.alert("Mappings removed successfully!");
                            $scope.getLocalities();
                        } else {
                            $rootScope.showFullScreenLoader = false;
                            bootbox.alert("Error occurred.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select mapping for removal.");
                }
            };

            $scope.showAddLocalities = function () {
                $("#addLocalityMappingModal").modal("show");
                $scope.newMappingPrimaryUnit = null;
                $scope.newMappingSecondaryUnit = null;
                $scope.newMapping = {
                    "locality": null,
                    "city": null,
                    "state": null,
                    "country": "India",
                    "primaryUnitName": null,
                    "primaryUnitId": null,
                    "primaryDeliveryTime": 0,
                    "skipPrimaryDeliveryCharge": false,
                    "skipPrimaryPackagingCharge": false,
                    "secondaryUnitName": null,
                    "secondaryUnitId": null,
                    "secondaryDeliveryTime": 0,
                    "skipSecondaryDeliveryCharge": false,
                    "skipSecondaryPackagingCharge": false,
                    "tertiaryUnitName": null,
                    "tertiaryUnitId": 0,
                    "tertiaryDeliveryTime": 0,
                    "skipTertiaryDeliveryCharge": false,
                    "skipTertiaryPackagingCharge": false,
                    "primaryTakeAwayUnitName": null,
                    "primaryTakeAwayUnitId": 0,
                    "primaryTakeAwayDeliveryTime": 0,
                    "secondaryTakeAwayUnitName": null,
                    "secondaryTakeAwayUnitId": 0,
                    "secondaryTakeAwayDeliveryTime": 0,
                    "tertiaryTakeAwayUnitName": null,
                    "tertiaryTakeAwayUnitId": 0,
                    "tertiaryTakeAwayDeliveryTime": 0,
                    "default": false
                }
            };

            $scope.addLocalityMapping = function () {
                $("#addLocalityMappingModal").modal("hide");
                $scope.newMapping.primaryUnitName = $scope.newMappingPrimaryUnit.name;
                $scope.newMapping.primaryUnitId = $scope.newMappingPrimaryUnit.id;
                if($scope.newMappingSecondaryUnit != null) {
                    $scope.newMapping.secondaryUnitName = $scope.newMappingSecondaryUnit.name;
                    $scope.newMapping.secondaryUnitId = $scope.newMappingSecondaryUnit.id;
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.neoData.addLocalities,
                    data: [$scope.newMapping]
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Mappings added successfully!");
                        $scope.getLocalities();
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error occurred.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.showAddBulkLocalities = function () {
                $("#addBulkLocalityMappingModal").modal("show");
                $scope.bulkCSVFile = null;
            };

            $scope.addBulkLocalityMappings = function () {
                console.log($scope.bulkCSVFile);
                $("#addBulkLocalityMappingModal").modal("hide");
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.neoData.uploadBulkLocalities,
                    data: $scope.bulkCSVFile
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Mappings added successfully!");
                        $scope.getLocalities();
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error occurred.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

        }])
    .directive('fileModel', ['$parse', function ($parse) {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                var model = $parse(attrs.fileModel);
                var modelSetter = model.assign;

                element.bind('change', function () {
                    scope.$apply(function () {
                        modelSetter(scope, element[0].files[0]);
                    });
                });
            }
        };
    }]);