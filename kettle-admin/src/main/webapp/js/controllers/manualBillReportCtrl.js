adminapp.controller("manualBillReportCtrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {
 $scope.init = function () {
     		 $scope.msgError = null;
		 $rootScope.showFullScreenLoader = true;
			$http({
				  method: 'GET',
				  url: AppUtil.restUrls.unitMetaData.allUnits+'?category=CAFE'
				}).then(function success(response) {
					$rootScope.showFullScreenLoader = false;
					$scope.cafelist = response.data;
				}, function error(response) {
					$rootScope.showFullScreenLoader = false;
					  console.log("error:"+response);
				});
		}
	 $scope.getManualBillBooks = function(){
		 $scope.allManualBills = [];
		 $scope.msgError = null;
		 $rootScope.showFullScreenLoader = true;
		 $scope.allManualBillBooks = [];
		 var unitId=$scope.selectedCafe.id;
		 $http({
			  method: 'POST',
			  url: AppUtil.restUrls.unitMetaData.getAllManualBillBookDetail,
			  data: unitId
			}).then(function success(response) {
				$rootScope.showFullScreenLoader = false;
				$scope.allManualBillBooks = response.data;
			}, function error(response) {
				$rootScope.showFullScreenLoader = false;
				$scope.msgError="There was some error while getting the manuall bill books for unit "+unitId;
			});
		 }
	 
	 $scope.getManualBills = function(billBookId){
 		 $scope.msgError = null;
		 $rootScope.showFullScreenLoader = true;
		 $scope.allManualBills = [];
		 $http({
			  method: 'POST',
			  url: AppUtil.restUrls.unitMetaData.getAllManualBillDetail,
			  data: billBookId
			}).then(function success(response) {
				$rootScope.showFullScreenLoader = false;
				$scope.allManualBills = response.data;
			}, function error(response) {
				$rootScope.showFullScreenLoader = false;
				$scope.msgError="There was some error while getting the manual bills for bookId "+billBookId;
			});
		 }
	 
	 $scope.downloadtManualBills = function(billBookId){
 		 $scope.msgError = null;
		 $rootScope.showFullScreenLoader = true;
		 $http({
			  method: 'POST',
			  url: AppUtil.restUrls.unitMetaData.getAllManualBillDetail,
			  data: billBookId
			}).then(function success(response) {
				$rootScope.showFullScreenLoader = false;
				var bills = response.data;
				if(bills != null && bills.length > 0){
				    AppUtil.JSONToCSVConvertor(bills, "ManualBillBook_"+$scope.selectedCafe.name+'_'
						+ billBookId, true)
				}
			}, function error(response) {
				$rootScope.showFullScreenLoader = false;
				$scope.msgError="There was some error while getting the manual bills for bookId "+billBookId;
			});
		 }
});