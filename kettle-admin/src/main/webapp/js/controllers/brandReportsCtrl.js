adminapp.controller("brandReportsCtrl", function($rootScope,$scope,$http,$location,AppUtil){

	$scope.init = function(){
		$scope.reportCategories = null;
		$scope.reportData = $scope.getReportData();
		$scope.errorMessage = null;
		$scope.environments = ["DUMP", "SPROD"];
        $scope.environment = "DUMP";
        $scope.getReportEnv($scope.environment);
		$scope.allBrands = [
		    {brandId:6, brandName:"Dohful"}
		];
		$scope.selectedBrand = $scope.allBrands[0];

	}

	$scope.getReports = function(){
		$rootScope.showDetailLoader = true;
		var reportDef = $scope.report.reportDetail;
		$http({
			method: 'POST',
			  url: AppUtil.restUrls.reportServiceMetaData.reportCategoriesV1,
			  data: reportDef
			}).then(function success(response) {
				$rootScope.showDetailLoader = false;
				if (response.status != 200) {
					$scope.categories = null;
					$scope.category = null;
					$scope.reportTypes = null;
					$scope.reportType = null;
					if(response.data!= null &&
						response.data.errorMessage == "Invalid Version id") {
						alert("No Report Version Available");
					} else
						alert('Something went wrong');
					return;
				}
				if(response.data!= null && response.data == ""){
					$scope.categories = null;
					$scope.category = null;
					$scope.reportTypes = null;
					$scope.reportType = null;
				} else{
					$scope.categories = response.data.category;
					$scope.category = $scope.categories[0];
					$scope.reportTypes = [];
					for(var i=0; i<$scope.category.report.length; i++) {
					    if ($scope.category.report[i].brandId === $scope.selectedBrand.brandId) {
                            $scope.reportTypes.push($scope.category.report[i]);
                        }
					}
					if($scope.reportTypes.length > 0) {
					    $scope.reportType = $scope.reportTypes[0];
					}
				}
			}, function error(response) {
				$rootScope.showDetailLoader = false;
				console.log("error:"+response);
		});
	}

	$scope.getReportType = function(){
		$scope.reportType = $scope.reportTypes[0];
	}

	$scope.brandSelected = function(brand) {
	    $scope.selectedBrand = brand;
	}

    $scope.getReportEnv = function(environment){
		$rootScope.showDetailLoader = true;
		$http({
            method: 'POST',
            url: AppUtil.restUrls.reportServiceMetaData.adHocReportsV1,
            data: environment
        }).then(function success(response) {
			$rootScope.showDetailLoader = false;
			if (response.status != 200) {
				$scope.categories = null;
				$scope.category = null;
				$scope.reportTypes = null;
				$scope.reportType = null;
				alert('Something went wrong');
				return;
			}
			$scope.reportList = response.data;
            $scope.report = $scope.reportList[0];
            if($scope.reportList != null && $scope.reportList.length > 0){
                $scope.getReports();
            } else{
				$scope.categories = null;
				$scope.category = null;
				$scope.reportTypes = null;
				$scope.reportType = null;
				alert('No Reports Available');
			}
        }, function error(response) {
			$rootScope.showDetailLoader = false;
			console.log("error:"+response);
        });
    }

	$scope.executeQuery = function(){
		$scope.showResultMetadata = false;
		$scope.resultData = null;
		$scope.errorMessage = null;
        /*if($scope.environment == "PROD" || $scope.environment == "SPROD") {
            if (!$scope.showToAdmin){
                alert("You are not authorized to execute this query.");
                return;
            }
        }*/
        $scope.reportData = $scope.getReportData();
        $scope.reportType.environment = $scope.environment;
        $scope.loadingData = true;

        $http({
			method: 'POST',
			  url: AppUtil.restUrls.reportServiceMetaData.reportExecute,
			  data: $scope.reportType
			}).then(function success(response) {
				if(response.status == 200){
					$scope.loadingData = false;
					$scope.resultData = response.data.data;
					var fieldnames = response.data.header;
					var colDef = [];
					fieldnames.forEach(function(v){
						var name = v.field.replace(/_/," ");
						console.log(name.toLowerCase().capitalize());
						colDef.push({field:v.field,name:name.toLowerCase().capitalize()});
					});
					$scope.reportData.columnDefs = colDef;
					$scope.reportData.data = $scope.resultData;
					$scope.showResultMetadata = true;
				}else{
					$scope.loadingData = false;
					$scope.errorMessage = "Error in executing report : "
							+ $scope.reportType.name
							+ " - "
							+ response.data.errorType
							+ "\n"
							+ response.data.errorMessage;
					console.log("error:"+response);
				}
			}, function error(response) {
				$scope.loadingData = false;
				$scope.errorMessage = "Error in executing report : "
						+ $scope.reportType.name
						+ " - "
						+ response.data.errorType
						+ "\n"
						+ response.data.errorMessage;
				console.log("error:"+response);
		});
	}

	$scope.getReportData = function() {
			var data = {
					enableSorting: true,
					paginationPageSizes: [30, 60, 100],
					paginationPageSize: 30,
					onRegisterApi: function( gridApi ) {
						$scope.grid2Api = gridApi;
					},
					columnDefs :[],
					data : $scope.resultData
			}
			return data;
	};

	$scope.exportReport = function(){
		 var myElement = angular.element(document.querySelectorAll(".custom-csv-link-location"));
	      $scope.grid2Api.exporter.csvExport( "all", "all", myElement );
	}

	function camelize(str) {
		console.log(str);
		  return str.replace(/(^|\w)[A-Za-z0-9]*/g, function(letter, index) {
		    return index == 0 ? letter.toUpperCase() : letter.toLowerCase();
		  }).replace(/\s+/g, '');
	}

	String.prototype.capitalize = function() {
	    return this.replace(/(?:^|\s)\S/g, function(a) { return a.toUpperCase(); });
	};

	$scope.showQuery = function(){
        // if($scope.environment == "PROD" || $scope.environment == "SPROD") {
        //     if (!$scope.showToAdmin){
        //         alert("You are not authorized to view.");
        //         return;
        //     }
        // }
		$("#queryDisplayModal").modal("show");
	}

});