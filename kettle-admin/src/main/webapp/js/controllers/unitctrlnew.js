adminapp.controller(
    'modalCtrl', ['$log', 'AuthService', '$cookieStore', '$rootScope', '$scope', '$http', '$location', 'AppUtil', '$uibModalInstance', 'unitData','monkRecipeProfiles','uiGridConstants',
        function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil, $uibModalInstance, unitData , monkRecipeProfiles, uiGridConstants) {
            'use strict';
            var ab = this;
            $scope.userData = AppUtil.getUserData();
            $scope.emailRegEx = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            $scope.multipleEmailRegEx= /^((([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))(,(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/;
            $scope.pincodeRegEx = /^[1-9][0-9]{5}$/;
            $scope.monkRecipeProfiles=monkRecipeProfiles
            ab.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.today = new Date(new Date().setDate(new Date().getDate() - 1)).toString();
                ab.posVersionsList = [0,1,2];
                ab.businessTypeList = ["COCO", "FICO", "FIFO"];
                ab.packagingTypeList = ["FIXED", "PERCENTAGE"];
                ab.zoneList = ["NORTH","SOUTH","EAST","WEST"];
                ab.handoverDateEditable = true;
                ab.hotspotStatusList = ["YES","NO"];
                ab.otpAllowedViaEmail = ["YES","NO"];
                ab.handoverDateEditable = false;
                ab.handoverReadMore = false;
                ab.showGrid = false;
                ab.showAssetsGrid = function(){
                    ab.showGrid = !ab.showGrid;
                }
                ab.totalAssets = 0;
                ab.showAssetBtn = false;
                ab.handoverEventStatus = "NOT_INITIATED";
                ab.yesNoOption = [{
                    code: false,
                    name: "No"
                }, {
                    code: true,
                    name: "Yes"
                }];

                ab.zeroOneOption = [{
                    code: 0,
                    name: 0
                }, {
                    code: 1,
                    name: 1
                }];


                ab.noOfShifts = [{
                    code: 1,
                    name: 1
                }, {
                    code: 2,
                    name: 2
                }];

                ab.isUndefinedOrNull = function (val) {
                    return angular.isUndefined(val) || val === null || val === "";
                };

                // ab.selectedBusinessType = ab.businessTypeList[0];

                $scope.selectedBrands = [];

                if (!ab.isUndefinedOrNull(unitData)) {
                    $scope.editMode = true;
                    ab.completeUnitObj = unitData;
                    ab.backUp = angular.copy(unitData);
                    // ab.completeUnitObj.address.contact1 = parseInt(ab.completeUnitObj.address.contact1);
                    // if(!ab.isUndefinedOrNull(ab.completeUnitObj.address.contact2)) {
                    //     ab.completeUnitObj.address.contact2 = parseInt(ab.completeUnitObj.address.contact2);
                    // }
                    //ab.completeUnitObj.address.latitude = parseInt(ab.completeUnitObj.address.latitude);
                    //ab.completeUnitObj.address.longitude = parseInt(ab.completeUnitObj.address.longitude);
                   ab.completeUnitObj.handoverDate=ab.completeUnitObj.handoverDate!=null?AppUtil.formatDate(ab.completeUnitObj.handoverDate,'yyyy-MM-dd'):ab.completeUnitObj.handoverDate;
                   ab.completeUnitObj.probableOpeningDate=ab.completeUnitObj.probableOpeningDate!=null?AppUtil.formatDate(ab.completeUnitObj.probableOpeningDate,'yyyy-MM-dd'):ab.completeUnitObj.probableOpeningDate;
                    ab.completeUnitObj.workstationEnabled = ab.completeUnitObj.workstationEnabled ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.freeInternetAccess = ab.completeUnitObj.freeInternetAccess ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.tableService = ab.completeUnitObj.tableService ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.tableServiceType = ab.zeroOneOption.find(function(option) {
                        return option.code === parseInt(ab.completeUnitObj.tableServiceType);
                    }) || ab.zeroOneOption[0];
                    ab.completeUnitObj.tokenService = ab.completeUnitObj.tokenService ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.dGAvailable = ab.completeUnitObj.dGAvailable ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.liveInventoryEnabled = ab.completeUnitObj.liveInventoryEnabled ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.revenueCertificateEmail = ab.completeUnitObj.revenueCertificateEmail ? ab.completeUnitObj.revenueCertificateEmail : "<EMAIL>"
                    ab.completeUnitObj.revenueCertificateGenerationEnable = ab.completeUnitObj.revenueCertificateGenerationEnable ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.isHotspotEnabled = ab.completeUnitObj.hotspotEnabled ? ab.hotspotStatusList[0] : ab.hotspotStatusList[1];
                    ab.completeUnitObj.isOtpAllowedViaEmail=ab.completeUnitObj.isOtpViaEmail? ab.otpAllowedViaEmail[0] : ab.otpAllowedViaEmail[1];
                    ab.completeUnitObj.assemblyStrictMode = ab.completeUnitObj.assemblyStrictMode ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.milkTrackingEnabled = ab.completeUnitObj.milkTrackingEnabled ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.assemblyOtpMode = ab.completeUnitObj.assemblyOtpMode ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.testingUnit = ab.completeUnitObj.testingUnit ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.isTestingUnit = unitData.isTestingUnit !== undefined && unitData.isTestingUnit !== null ? unitData.isTestingUnit : ab.yesNoOption[0];
                    ab.completeUnitObj.isTestingUnit = unitData.isTestingUnit !== undefined && unitData.isTestingUnit !== null ? unitData.isTestingUnit : ab.yesNoOption[1];
                    ab.completeUnitObj.serviceCharge=unitData.serviceCharge !== undefined && unitData.serviceCharge !== null ? unitData.serviceCharge : null;
                    ab.completeUnitObj.serviceChargePosEnabled= unitData.serviceChargePosEnabled ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    ab.completeUnitObj.serviceChargeAppEnabled= unitData.serviceChargeAppEnabled ? ab.yesNoOption[1] : ab.yesNoOption[0];
                    // Set feedbackFormEnabledForPos to the value from the unit if available, else default to false
                    if (unitData.hasOwnProperty('feedbackFormEnabledForPos') && unitData.feedbackFormEnabledForPos !== null && typeof unitData.feedbackFormEnabledForPos !== 'undefined') {
                        ab.completeUnitObj.feedbackFormEnabledForPos = unitData.feedbackFormEnabledForPos ? ab.yesNoOption[1].code : ab.yesNoOption[0].code;
                    } else {
                        ab.completeUnitObj.feedbackFormEnabledForPos = ab.yesNoOption[0].code;
                    }
                    ab.completeUnitObj.dreamfolksOutletId = unitData.dreamfolksOutletId !== "" ? unitData.dreamfolksOutletId : "";


                    ab.dineInOpeningTimeFix = new Date();
                    ab.dineInClosingTimeFix = new Date();
                    ab.deliveryOpeningTimeFix = new Date();
                    ab.deliveryClosingTimeFix = new Date();
                    ab.takeAwayOpeningTimeFix = new Date();
                    ab.takeAwayClosingTimeFix = new Date();
                    ab.shiftOneHandoverTimeFix = new Date();

                    ab.checkedDays = true;

                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        if (item.isOperational === false)
                            ab.checkedDays = false;
                        if (!ab.isUndefinedOrNull(item.dineInOpeningTime)) {
                            ab.dineInOpeningTimeFix.setHours(item.dineInOpeningTime.slice(0, 2));
                            ab.dineInOpeningTimeFix.setMinutes(item.dineInOpeningTime.slice(3, 5));
                            item.dineInOpeningTime = ab.dineInOpeningTimeFix;
                        }
                        if (!ab.isUndefinedOrNull(item.dineInClosingTime)) {
                            ab.dineInClosingTimeFix.setHours(item.dineInClosingTime.slice(0, 2));
                            ab.dineInClosingTimeFix.setMinutes(item.dineInClosingTime.slice(3, 5));
                            item.dineInClosingTime = ab.dineInClosingTimeFix;
                            console.log(item.dineInClosingTime.toTimeString());
                        }
                        if (!ab.isUndefinedOrNull(item.deliveryOpeningTime)) {
                            ab.deliveryOpeningTimeFix.setHours(item.deliveryOpeningTime.slice(0, 2));
                            ab.deliveryOpeningTimeFix.setMinutes(item.deliveryOpeningTime.slice(3, 5));
                            item.deliveryOpeningTime = ab.deliveryOpeningTimeFix;
                        }
                        if (!ab.isUndefinedOrNull(item.deliveryClosingTime)) {
                            ab.deliveryClosingTimeFix.setHours(item.deliveryClosingTime.slice(0, 2));
                            ab.deliveryClosingTimeFix.setMinutes(item.deliveryClosingTime.slice(3, 5));
                            item.deliveryClosingTime = ab.deliveryClosingTimeFix;
                        }
                        if (!ab.isUndefinedOrNull(item.takeAwayOpeningTime)) {
                            ab.takeAwayOpeningTimeFix.setHours(item.takeAwayOpeningTime.slice(0, 2));
                            ab.takeAwayOpeningTimeFix.setMinutes(item.takeAwayOpeningTime.slice(3, 5));
                            item.takeAwayOpeningTime = ab.takeAwayOpeningTimeFix;
                        }
                        if (!ab.isUndefinedOrNull(item.takeAwayClosingTime)) {
                            ab.takeAwayClosingTimeFix.setHours(item.takeAwayClosingTime.slice(0, 2));
                            ab.takeAwayClosingTimeFix.setMinutes(item.takeAwayClosingTime.slice(3, 5));
                            item.takeAwayClosingTime = ab.takeAwayClosingTimeFix;
                        }
                        if (!ab.isUndefinedOrNull(item.shiftOneHandoverTime)) {
                            ab.shiftOneHandoverTimeFix.setHours(item.shiftOneHandoverTime.slice(0, 2));
                            ab.shiftOneHandoverTimeFix.setMinutes(item.shiftOneHandoverTime.slice(3, 5));
                            item.shiftOneHandoverTime = ab.shiftOneHandoverTimeFix;
                        }

                    });

                    $scope.getUnitBrandMappings(unitData.id);
                    console.log("unitData= " + JSON.stringify(ab.completeUnitObj));
                } else {
                    $scope.editMode = false;
                    ab.completeUnitObj = {
                        noOfTables: 1,
                        workstationEnabled: ab.yesNoOption[0],
                        freeInternetAccess: ab.yesNoOption[0],
                        revenueCertificateGenerationEnable:ab.yesNoOption[0],
                        revenueCertificateEmail:"<EMAIL>",
                        tableService: ab.yesNoOption[0],
                        tableServiceType: ab.zeroOneOption[0],
                        tokenService: ab.yesNoOption[0],
                        dGAvailable: ab.yesNoOption[0],
                        division: null,
                        family: null,
                        cafeType:null,
                        name: null,
                        shortName: null,
                        shortCode:null,
                        referenceName: null,
                        noOfTakeAway: 1,
                        noOfTakeawayTerminals: 1,
                        noOfTerminals: 1,
                        tokenLimit: 1,
                        subCategory: null,
                        unitBusinessType: ab.businessTypeList[0],
                        region: null,
                        unitZone: ab.zoneList[0],
                        company: null,
                        startDate: null,
                        status: "IN_ACTIVE",
                        tin: null,
                        fssai: null,
                        cloneUnitId: null,
                        unitEmail: null,
                        channel: null,
                        deliveryPartners: [],
                        operationalHours: [],
                        unitManager: null,
                        cafeManager: null,
                        location: null,
                        noOfMeter: 1,
                        isOtpViaEmail : ab.otpAllowedViaEmail[0],
                        liveInventoryEnabled: ab.yesNoOption[0],
                        isTestingUnit : ab.yesNoOption[1],
                        serviceCharge:null,
                        serviceChargePosEnabled : ab.yesNoOption[0],
                        serviceChargeAppEnabled : ab.yesNoOption[0],
                        address: {
                            addressType: "OFFICIAL",
                            city: null,
                            contact1: null,
                            contact2: null,
                            country: null,
                            latitude: null,
                            line1: null,
                            line2: null,
                            line3: null,
                            longitude: null,
                            state: null,
                            zipCode: null
                        },
                        inventoryCloneUnitId: null,
                        menuSequenceCloneUnitId: null,
                        salesClonedFrom: null,
                        probableOpeningDate: null,
                        pricingProfile: null,
                        assemblyStrictMode:ab.yesNoOption[1],
                        milkTrackingEnabled:ab.yesNoOption[0],
                        assemblyOtpMode:ab.yesNoOption[1],
                        googleRatingReviewUrl : null,
                        feedbackFormEnabledForPos : false,
                        dreamfolksOutletId : "",
                    };
                    

                    var operationalHourObj = {
                        dayOfTheWeekNumber: null,
                        dayOfTheWeek: null,
                        noOfShifts: 1,
                        isOperational: false,
                        hasDelivery: false,
                        hasDineIn: false,
                        hasTakeAway: false,
                        dineInOpeningTime: null,
                        dineInClosingTime: null,
                        deliveryOpeningTime: null,
                        deliveryClosingTime: null,
                        takeAwayOpeningTime: null,
                        takeAwayClosingTime: null,
                        shiftOneHandoverTime: null,
                        shiftTwoHandoverTime: null,
                        status: "ACTIVE"
                    };


                    var i = 0;
                    var days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
                    for (i = 0; i < 7; i++) {
                        var item = angular.copy(operationalHourObj);
                        item.dayOfTheWeekNumber = i + 1;
                        item.dayOfTheWeek = days[i];
                        ab.completeUnitObj.operationalHours.push(item);
                    }

                    console.log("completeUnitObj = " + JSON.stringify(ab.completeUnitObj));

                }

                $scope.hstep = 1;
                $scope.mstep = 1;

                $scope.options = {
                    hstep: [1, 2, 3],
                    mstep: [1, 5, 10, 15, 25, 30]
                };

                $scope.ismeridian = false;

                ab.headerDineInStartTime = new Date(2020, 1, 1, 8);
                ab.headerCodStartTime = new Date(2020, 1, 1, 8);
                ab.headerTakeAwayStartTime = new Date(2020, 1, 1, 8);
                ab.headerDineInCloseTime = new Date(2020, 1, 1, 23, 30);
                ab.headerCodCloseTime = new Date(2020, 1, 1, 23, 30);
                ab.headerTakeAwayCloseTime = new Date(2020, 1, 1, 23, 30);
                ab.headerHandoverTime = new Date(2020, 1, 1, 3, 30);


                ab.index = 0;

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.families
                }).then(function success(response) {
                    ab.families = response.data;
                    console.log("allFamily=", ab.families);
                    // ab.completeUnitObj.family = ab.families[0];
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.families.findIndex(function (x) {
                            return x === ab.completeUnitObj.family;
                        });
                        ab.completeUnitObj.family = ab.families[index];
                    } else {
                        ab.completeUnitObj.family = ab.families[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.cafeType
                }).then(function success(response) {
                    ab.cafeTypes = response.data;
                    console.log("allCafeType=", ab.cafeTypes);
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.cafeTypes.findIndex(function (x) {
                            return x === ab.completeUnitObj.cafeType;
                        });
                        ab.completeUnitObj.cafeType = ab.cafeTypes[index];
                    } else {
                        ab.completeUnitObj.cafeType = ab.cafeTypes[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.companies,
                    headers: { bypassCompanyBrandFilter: true, bypassBrandFilter: true }
                }).then(function success(response) {
                    ab.newUnitCompanyList = response.data;
                    // ab.completeUnitObj.company = ab.newUnitCompanyList[0];
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.newUnitCompanyList.findIndex(function (x) {
                            return x.id === ab.completeUnitObj.company.id;
                        });
                        ab.completeUnitObj.company = ab.newUnitCompanyList[index];
                    } else {
                        ab.completeUnitObj.company = ab.newUnitCompanyList[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.regions
                }).then(function success(response) {
                    ab.regions = response.data;
                    // ab.completeUnitObj.region = ab.regions[0];
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.regions.findIndex(function (x) {
                            return x === ab.completeUnitObj.region;
                        });
                        ab.completeUnitObj.region = ab.regions[index];
                    } else {
                        ab.completeUnitObj.region = ab.regions[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });



                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.subCategories
                }).then(function success(response) {
                    ab.subCategories = response.data;
                    // ab.completeUnitObj.subCategory = ab.subCategories[0];
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.subCategories.findIndex(function (x) {
                            return x === ab.completeUnitObj.subCategory;
                        });
                        ab.completeUnitObj.subCategory = ab.subCategories[index];
                    } else {
                        ab.completeUnitObj.subCategory = ab.subCategories[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });


                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.divisions
                }).then(function success(response) {
                    ab.divisions = response.data;
                    console.log("divisions= " + JSON.stringify(ab.divisions));
                    // console.log("newUnitDivision= " + JSON.stringify(ab.newUnitDivision));
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.divisions.findIndex(function (x) {
                            return x.id === ab.completeUnitObj.division.id;
                        });
                        ab.completeUnitObj.division = ab.divisions[index];
                    } else {
                        ab.completeUnitObj.division = ab.divisions[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.country
                }).then(function success(response) {
                    ab.countriesList = response.data;
                    console.log(ab.countriesList);
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.countriesList.findIndex(function (x) {
                            return x.name === ab.completeUnitObj.address.country;
                        });
                        ab.completeUnitObj.address.country = ab.countriesList[index];
                    } else {
                        ab.completeUnitObj.address.country = ab.countriesList[0];
                    }
                    console.log("country=", ab.completeUnitObj.address.country);
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.state + '?countryId=1'
                }).then(function success(response) {
                    ab.stateList = response.data;
                    console.log("stateList", ab.stateList);
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.stateList.findIndex(function (x) {
                            return x.name === ab.completeUnitObj.address.state;
                        });
                        ab.completeUnitObj.address.state = ab.stateList[index];
                        ab.getCitiesList(ab.completeUnitObj.address.state);
                    } else {
                        //ab.completeUnitObj.address.state = ab.stateList[0];
                    }
                    console.log("country=", ab.completeUnitObj.address.state);
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.listTypes
                }).then(function success(response){
                    ab.listTypes = response.data;
                    ab.pricingProfileList = ['content'];
                    var category = ab.listTypes['PRICING_PROFILE'][0];
                    ab.pricingProfileList = [];
                    if (angular.equals(category['detail']['name'], "Pricing Profile") && angular.equals(category['detail']['status'], "ACTIVE")) {
                        for (var itr in category['content']) {
                            if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                                ab.pricingProfileList.push(category['content'][itr]);
                            }
                        }
                    }
                    console.log("Pricing profile list: ", ab.pricingProfileList);
                }, function error(response) {
                    $scope.showFullScreenLoader = false;
                    console.log("error: " + response);
                });

                if(ab.completeUnitObj.family === "CAFE"){
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.checkForPendingSCMTransactions,
                        params: {id : ab.completeUnitObj.id,
                        type : "FA_GR"}
                    }).then(function (response) {
                        console.log("handoverDdate : ", response);
                        if (!response.data.canBeDeActivated) {
                            ab.handoverDateEditable = false;
                            ab.handoverDateMsg = response.data["message"];
                        }
                    }, function (response) {
                        console.log("error:" + response);
                    });
                }

                ab.gridOptions = {
                     expandableRowTemplate: 'expandableRowTemplate.html',
                     expandableRowScope: {
                           subGridVariable: 'subGridScopeVariable'
                         },
                     showColumnFooter: true,
                     enableGridMenu: true,
                     exporterExcelFilename: 'download.xlsx',
                     exporterExcelSheetName: 'Sheet1',
                     enableColumnMenus: true,
                     saveFocus: false,
                     enableRowSelection: true,
                     enableFiltering: true,
                     saveScroll: true,
                     enableSelectAll: true,
                     multiSelect: true,
                     enableColumnResizing: true,
                     exporterMenuPdf : false,
                     exporterMenuExcel : true,
                     fastWatch: true,
                     onRegisterApi: function(gridApi){
                        ab.gridApi = gridApi;
                        gridApi.expandable.on.rowExpandedStateChanged($scope,function(row){
                            if (row.isExpanded) {
                                ab.gridApi.expandable.collapseAllRows();
                                        row.isExpanded = true;
                            }
                            ab.gridOptions.expandableRowHeight = row.entity.subGridOptions.data.length * row.height + row.height + row.grid.headerHeight;
                        });
                     },
                 };
                ab.gridOptions.columnDefs = [
                { name: 'assetName', displayName: 'Asset Name'},
                { name: 'subData.length' , displayName: 'Qty', aggregationType: uiGridConstants.aggregationTypes.sum},
                { name: 'scanned', displayName: 'Scanned', aggregationType: uiGridConstants.aggregationTypes.sum },
                { name: 'pending', displayName: 'Pending', aggregationType: uiGridConstants.aggregationTypes.sum},
                { name: 'productId', displayName: 'Product Id'},
                {field: 'subData[0].subCategoryDefinition.name', name: 'category-name', displayName: 'sub-category'}]

                ab.expandAllRows = function() {
                  ab.gridApi.expandable.expandAllRows();
                };
                ab.collapseAllRows = function() {
                  ab.gridApi.expandable.collapseAllRows();
                };

                if(ab.completeUnitObj.family === "CAFE"){
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.nsoEventAssets,
                        params: {unitId : ab.completeUnitObj.id}

                    }).then(function (response) {

                        var data = response.data.assetDefinition;
                        ab.totalAssets = data.length;
                        if(response.data.eventStatus != null)
                            ab.handoverEventStatus = response.data.eventStatus;
                        if(response.data.eventStatus === "COMPLETED" && (response.data.hourDiff < 24 || $scope.userData.id == 140199)){
                            ab.handoverDateEditable = true;
                            ab.showAssetBtn = true;
                        }
                        else if(response.data.eventStatus === "COMPLETED"){
                            ab.handoverDateEditable = false;
                            ab.handoverDateMsg = "Event Completion Timed Out. Please put Handover Date within 24 Hours of Stock Take. Please Create New Event and Stock Take Again from App.";
                        }
                        else if(response.data.eventStatus === null){
                            ab.handoverDateEditable = false;
                            ab.handoverDateMsg = "No event Initiated, Please initiate a new NSO Event from App.";
                        }else{
                            ab.showAssetBtn = true;
                            ab.handoverDateEditable = false;
                            ab.handoverDateMsg= "Initiated event is still in process, please fill in the handover date only after the NSO event is completed.";
                        }
                        var subData = {};
                        var productSet = {};
                        var productScannedMap = {};
                        for(var i = 0; i < data.length; i++){
                             var product =  {
                                productId: data[i].productId,
                                assetName : data[i].assetName
                             }

                             if(subData[data[i].productId] == null){
                                subData[data[i].productId] = [];
                            }
                              subData[data[i].productId].push(data[i]);
                              product['subData'] = subData[data[i].productId];
                                    if(productScannedMap[data[i].productId] == null){
                                              productScannedMap[data[i].productId] = 0;
                                    }

                              if(data[i].scanned == true) {
                                    productScannedMap[data[i].productId] = productScannedMap[data[i].productId] + 1;
                              }

                              product['scanned'] = productScannedMap[data[i].productId] ;
                              if(product['scanned'] == null)
                                          product['scanned'] = 0;
                              product['pending'] =  product['subData'].length - productScannedMap[data[i].productId];
                              productSet[data[i].productId] = product;
                        }
                       data = Object.values(productSet);
                       console.log("data ::::" , data)
                        for(var i = 0; i < data.length; i++){
                            data[i].subGridOptions = {
                                    columnDefs: [
                                        {field: 'assetId', name: 'asset-id', displayName: 'Asset Id'},
                                        {field: 'skuId', name: 'sku-id', displayName: 'SKU Id'},
                                        {field: 'assetStatus', name: 'asset-status', displayName: 'Asset Status',cellTemplate:'colColor.html' },
                                        {field: 'tagValue', name: 'tag-value', displayName: 'Tag Value'},
                                       {field: 'procurementCost', name: 'procurement-cost', displayName: 'Cost'},
                                        {field: 'createdBy.name', name: 'created-by', displayName: 'Created By'}
                                    ],
                                data: data[i].subData,
                                };
                        }
                        ab.gridOptions.data = data;
                    }, function (response) {
                        console.log("error:" + response);
                    });
                }

                ab.selectedNoOfShifts = ab.noOfShifts[0];
                ab.selectedDeliveryPartner = null;
                ab.getAllDeliveryPartners();
                
                $http({
                    method: "GET",
                    url: AppUtil.restUrls.brandMetaData.getAllBrands,
                    headers: { bypassCompanyBrandFilter: true, bypassBrandFilter: !$scope.editMode }
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.brands = response.data;
                    } else {
                        console.log("Unable to Fetch Brands");
                    }
                }, function error(response) {
                    console.log("Unable to Fetch Brands");
                });

                $http({
                    method: "GET",
                    url: AppUtil.restUrls.brandMetaData.getCompanyBrandMapping
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.companyBrandMappings = response.data;
                        $scope.onCompanyChange(ab.completeUnitObj.company.id);
                        $scope.processCompanyBrandMap();
                    } else {
                        console.log("Unable to Fetch Company Brand Mapping");
                    }
                }, function error(response) {
                    console.log("Unable to Fetch Company Brand Mapping");
                });

            };

            ab.refreshAssetsGrid = function(){
                if(ab.completeUnitObj.family === "CAFE"){
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.nsoEventAssets,
                        params: {unitId : ab.completeUnitObj.id}

                    }).then(function (response) {

                        var data = response.data.assetDefinition;
                        ab.totalAssets = data.length;
                        if(response.data.eventStatus != null)
                            ab.handoverEventStatus = response.data.eventStatus;
                        if(response.data.eventStatus === "COMPLETED"){
                            ab.handoverDateEditable = true;
                            ab.showAssetBtn = true;
                        }
                        else if(response.data.eventStatus === null){
                            ab.handoverDateEditable = false;
                            ab.handoverDateMsg = "No event Initiated, Please initiate a new NSO Event from App.";
                        }else{
                            ab.showAssetBtn = true;
                            ab.handoverDateEditable = false;
                            ab.handoverDateMsg= "Initiated event is still in process, please fill in the handover date only after the NSO event is completed.";
                        }
                        var subData = {};
                        var productSet = {};
                        var productScannedMap = {};
                        for(var i = 0; i < data.length; i++){
                             var product =  {
                                productId: data[i].productId,
                                assetName : data[i].assetName
                             }

                             if(subData[data[i].productId] == null){
                                subData[data[i].productId] = [];
                            }
                              subData[data[i].productId].push(data[i]);
                              product['subData'] = subData[data[i].productId];
                                    if(productScannedMap[data[i].productId] == null){
                                              productScannedMap[data[i].productId] = 0;
                                    }

                              if(data[i].scanned == true) {
                                    productScannedMap[data[i].productId] = productScannedMap[data[i].productId] + 1;
                              }

                              product['scanned'] = productScannedMap[data[i].productId] ;
                              if(product['scanned'] == null)
                                          product['scanned'] = 0;
                              product['pending'] =  product['subData'].length - productScannedMap[data[i].productId];
                              productSet[data[i].productId] = product;
                        }
                       data = Object.values(productSet);
                       console.log("data ::::" , data)
                        for(var i = 0; i < data.length; i++){
                            data[i].subGridOptions = {
                                    columnDefs: [
                                        {field: 'assetId', name: 'asset-id', displayName: 'Asset Id'},
                                        {field: 'skuId', name: 'sku-id', displayName: 'SKU Id'},
                                        {field: 'assetStatus', name: 'asset-status', displayName: 'Asset Status',cellTemplate:'colColor.html' },
                                        {field: 'tagValue', name: 'tag-value', displayName: 'Tag Value'},
                                       {field: 'procurementCost', name: 'procurement-cost', displayName: 'Cost'},
                                        {field: 'createdBy.name', name: 'created-by', displayName: 'Created By'}
                                    ],
                                data: data[i].subData,
                                };
                        }
                        ab.gridOptions.data = data;
                    }, function (response) {
                        console.log("error:" + response);
                    });
                }
            };

            ab.setPriceCloningUnitId = function (cloningUnit) {
                if(cloningUnit != null) {
                    ab.completeUnitObj.cloneUnitId = cloningUnit.id;
                }
            };

            ab.setInventoryCloneUnitId = function (inventoryCloneUnit) {
                if(inventoryCloneUnit != null) {
                    ab.completeUnitObj.inventoryCloneUnitId = inventoryCloneUnit.id;
                }
            };

            ab.setMenuSequenceCloneUnitId = function (menuSequenceCloneUnit) {
                if(menuSequenceCloneUnit != null) {
                    ab.completeUnitObj.menuSequenceCloneUnitId = menuSequenceCloneUnit.id;
                }
            };

            ab.setSalesClonedFromUnitId = function (salesClonedFromUnit) {
                if (salesClonedFromUnit != null) {
                    ab.completeUnitObj.salesClonedFrom = salesClonedFromUnit.id;
                }
            };

            ab.setHotSpotStatus = function (isHotspotEnabled){
                if (isHotspotEnabled !== null) {
                    if(isHotspotEnabled === "YES")
                        ab.completeUnitObj.hotspotEnabled = true;
                    else
                        ab.completeUnitObj.hotspotEnabled = false;
                }
            };

            ab.setEmailAllowedStatus = function (isOtpAllowedViaEmail){
                if(isOtpAllowedViaEmail !== null){
                     if(isOtpAllowedViaEmail === "YES")
                        ab.completeUnitObj.isOtpViaEmail = true;
                     else
                        ab.completeUnitObj.isOtpViaEmail = false;
                }
            }

            ab.checkAllDays = function () {
                if (ab.checkedDays === false) {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.isOperational = false;
                    });
                }
                else {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.isOperational = true;
                    });
                }
            };

            ab.checkAllDineIn = function () {
                if (ab.checkedDine === false) {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.hasDineIn = false;
                    });
                }
                else {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.hasDineIn = true;
                        item.dineInOpeningTime = ab.headerDineInStartTime;
                        item.dineInClosingTime = ab.headerDineInCloseTime;
                    });
                }
            };

            ab.checkAllCOD = function () {
                if (ab.checkedCod === false) {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.hasDelivery = false;
                    });
                }
                else {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.hasDelivery = true;
                        item.deliveryOpeningTime = ab.headerCodStartTime;
                        item.deliveryClosingTime = ab.headerCodCloseTime;
                    });
                }
            };

            ab.checkAllTakeAway = function () {
                if (ab.checkedTakeAway === false) {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.hasTakeAway = false;
                    });
                }
                else {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.hasTakeAway = true;
                        item.takeAwayOpeningTime = ab.headerTakeAwayStartTime;
                        item.takeAwayClosingTime = ab.headerTakeAwayCloseTime;
                    });
                }
            };

            ab.checkAllHandOverIn = function () {
                if (ab.checkedHandOver === false) {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.handoverTick = false;
                    });
                }
                else {
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        item.handoverTick = true;
                        item.shiftOneHandoverTime = ab.headerHandoverTime;
                    });
                }
            };

            ab.headerDaysCheckBoxToggle = function (check) {
                if (check === false) {
                    ab.checkedDays = false;

                }
                else {
                    var allDaysChecked = true;
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        allDaysChecked = allDaysChecked & item.isOperational;
                    });
                    if (allDaysChecked === 1) {
                        ab.checkedDays = true;
                    }
                }
            };

            ab.headerDineInCheckBoxToggle = function (check) {
                if (check === false) {
                    ab.checkedDine = false;
                }
                else {
                    var allDineInChecked = true;
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        allDineInChecked = allDineInChecked & item.hasDineIn;
                    });
                    if (allDineInChecked === 1) {
                        ab.checkedDine = true;
                    }
                }
            };

            ab.headerCodCheckBoxToggle = function (check) {
                if (check === false) {
                    ab.checkedCod = false;
                }
                else {
                    var allCodChecked = true;
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        allCodChecked = allCodChecked & item.hasDelivery;
                    });
                    if (allCodChecked === 1) {
                        ab.checkedCod = true;
                    }
                }
            };

            ab.handoverEditableToggle = function () {
                ab.handoverReadMore = !ab.handoverReadMore;
            }

            ab.headerTakeAwayCheckBoxToggle = function (check) {
                if (check === false) {
                    ab.checkedTakeAway = false;
                }
                else {
                    var allTakeAwayChecked = true;
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        allTakeAwayChecked = allTakeAwayChecked & item.hasDelivery;
                    });
                    if (allTakeAwayChecked === 1) {
                        ab.checkedTakeAway = true;
                    }
                }
            };

            ab.headerHandoverCheckBoxToggle = function (check) {
                if (check === false) {
                    ab.checkedHandOver = false;
                }
                else {
                    var allHandoverChecked = true;
                    ab.completeUnitObj.operationalHours.forEach(function (item) {
                        allHandoverChecked = allHandoverChecked & item.handoverTick;
                    });
                    if (allHandoverChecked === 1) {
                        ab.checkedHandOver = true;
                    }
                }
            };

            ab.setUnitAddressCity = function (cityName) {
                ab.completeUnitObj.address.city = cityName;
            };
            ab.getKioskCompanyList = function (familyName) {
                if (familyName == 'CHAI_MONK') {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.kioskManagement.kioskCompany
                    }).then(function success(response) {
                        ab.kioskCompanyList = response.data;
                        console.log("company=", ab.kioskCompanyList);
                        console.log(JSON.stringify(ab.kioskCompanyList));

                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };
            ab.getKioskOfficeList = function (selectedCompany) {
                if (selectedCompany != undefined) {
                    console.log("companyData=", selectedCompany);
                    ab.companyNameMonk = selectedCompany.companyName;
                    console.log(JSON.stringify(selectedCompany.officeList));
                    ab.kioskOfficeList = selectedCompany.officeList;
                    ab.completeUnitObj.unitEmail = selectedCompany.companyEmail;
                }
            };
            ab.getLocationList = function (selectedOffice) {
                if (!angular.isUndefined(selectedOffice)) {
                    ab.kioskLocationList = selectedOffice.locationList;
                    ab.completeUnitObj.tin = selectedOffice.tin;
                    ab.completeUnitObj.address.line1 = selectedOffice.officeAddress.line1;
                    ab.completeUnitObj.address.line2 = selectedOffice.officeAddress.line2;
                    ab.completeUnitObj.address.zipCode = selectedOffice.officeAddress.zipCode;
                    ab.completeUnitObj.address.longitude = selectedOffice.officeAddress.longitude;
                    ab.completeUnitObj.address.latitude = selectedOffice.officeAddress.latitude;
                    var index = ab.stateList.findIndex(function (x) {
                        return x.name === selectedOffice.officeAddress.state;
                    });
                    ab.completeUnitObj.address.state = ab.stateList[index];
                    ab.kioskAddressCity = selectedOffice.officeAddress.city;
                    ab.getCitiesList(ab.completeUnitObj.address.state);

                }
            };

            ab.setBasicChaiMonkInfo = function (locationList) {
                ab.locationIdMonk = locationList.locationId;
                ab.completeUnitObj.name = locationList.assignedUnit.name;
                ab.completeUnitObj.referenceName = locationList.assignedUnit.name;
                ab.completeUnitObj.workstationEnabled = ab.yesNoOption[1];
            };

            ab.getCitiesList = function (state) {
                // ab.completeUnitObj.address.state = state.name;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.location + '?stateId=' + state.id
                }).then(function success(response) {
                    ab.locationList = response.data;
                    // ab.selectedAddressCity = null;
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.locationList.findIndex(function (x) {
                            return x.name === ab.completeUnitObj.address.city;
                        });
                        ab.completeUnitObj.location = ab.locationList[index];
                        ab.completeUnitObj.address.city = ab.completeUnitObj.location.name;
                    } else if (ab.completeUnitObj.family === 'CHAI_MONK') {
                        var index = ab.locationList.findIndex(function (x) {
                            return x.name === ab.kioskAddressCity;
                        });
                        ab.completeUnitObj.location = ab.locationList[index];
                        ab.completeUnitObj.address.city = ab.completeUnitObj.location.name;
                    }
                    console.log("locationList", ab.locationList)
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            function validateEmail(email) {
                var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email);
            }

            ab.validateBasicDetails = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.family)) {
                    alert("Please select a Unit Family.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.company)) {
                    alert("Please select a company  .");
                    return false;
                }
                if (ab.isUndefinedOrNull($scope.selectedBrands) || $scope.selectedBrands.length <= 0) {
                    alert("Please select atleast 1 brand.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.name)) {
                    alert("Please fill in Unit Name.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.referenceName)) {
                    alert("Please fill in Reference Name.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.shortName)) {
                    alert("Please fill in Short Name.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.shortCode)) {
                    alert("Please fill in Short Code.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.region)) {
                    alert("Please select a Region.");
                    return false;
                }
                if(ab.isUndefinedOrNull(ab.completeUnitObj.unitZone)){
                    alert("Please select a Zone.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.subCategory)) {
                    alert("Please select a Sub Category.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.unitBusinessType)) {
                    alert("Please select a Business Type.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.unitEmail) || !validateEmail(ab.completeUnitObj.unitEmail)) {
                    alert("Please verify Unit Email ID.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.tin)) {
                    alert("Please fill in GSTIN No.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.noOfTables) || ab.completeUnitObj.noOfTables < 0) {
                    alert("Please fill valid No of Tables");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.noOfTerminals) || ab.completeUnitObj.noOfTerminals < 0) {
                    alert("Please fill valid No of Terminals");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.noOfTakeawayTerminals) || ab.completeUnitObj.noOfTakeawayTerminals < 0) {
                    alert("Please fill valid No of Take Away Terminals");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.workstationEnabled)) {
                    alert("Please select Workstation Enabled");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.freeInternetAccess)) {
                    alert("Please select Free InternetAccess");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.tableService)) {
                    alert("Please select Table Service Enabled");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.tokenService)) {
                    alert("Please select Token Service Enabled");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.tokenLimit) || ab.completeUnitObj.tokenLimit < 0) {
                    alert("Please input valid Max Token Limit");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.noOfMeter)) {
                    alert("Please verify Number of Electricity Meters");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.dGAvailable)) {
                    alert("Please select DG Meter Available");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.liveInventoryEnabled)) {
                    alert("Please select Live Inventory Enabled");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.probableOpeningDate)) {
                    alert("Please select a probable unit opening date ..!");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.assemblyStrictMode)) {
                    alert("Please select Assembly Strict Mode!");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.milkTrackingEnabled)) {
                    alert("Please select Enable Milk Tracking!");
                    return false;
                }

                if (ab.isUndefinedOrNull(ab.completeUnitObj.assemblyOtpMode)) {
                    alert("Please select Assembly Otp Mode for Delivery Orders!");
                    return false;
                }

                if (ab.isUndefinedOrNull(ab.completeUnitObj.cafeType)) {
                    alert("Please select a Cafe Service Type!");
                    return false;
                }

                return true;
            };
            ab.validateDivisionDetails = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.division)) {
                    alert("Select a division to continue!");
                    return false;
                }
                return true;
            };

             ab.validateNumberInput = function (field) {
                           let value = ab.completeUnitObj[field];
                           if (value) {
                               let sanitizedValue = value.replace(/[^0-9.]/g, '');
                               let parts = sanitizedValue.split('.');
                               if (parts.length > 2) {
                                   sanitizedValue = parts[0] + '.' + parts.slice(1).join('');
                               }
                               ab["invalid" + field.charAt(0).toUpperCase() + field.slice(1)] = sanitizedValue !== value;
                               ab.completeUnitObj[field] = sanitizedValue;
                           }
                       };

            ab.validateRevenueCertificateDetails = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.revenueCertificateGenerationEnable)) {
                    alert("Select if revenue certificate to be generated or not");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.revenueCertificateEmail)) {
                    alert("Please enter Revenue certificate valid emails");
                    return false;
                }
                return true;
            }

            ab.validateAddress = function () {
				var reg = new RegExp('^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}');
                var addreg = new RegExp(/^[a-zA-Z0-9]+[\s]*[a-zA-Z0-9/\-\,\.\']+[\s]*[a-zA-Z0-9/\-\,\.\']+[a-zA-Z0-9\s/\-\,\.\']*$/)
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.line1)) {
                    alert("Please input address line 1.");
                    return false;
                }
                if(!addreg.test(ab.completeUnitObj.address.line1)){
                    alert("Please input a valid address");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.country)) {
                    alert("Please select a Country");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.state)) {
                    alert("Please select a State");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.city)) {
                    alert("Please select a city");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.zipCode)) {
                    alert("Please input Zipcode");
                    return false;
                }


                /*                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.contact1)) {
                                    alert("Please verify Contact Number 1");
                                    return false;
                                }*/
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.latitude)) {
                    alert("Please input Latitude Coordinates");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.address.longitude)) {
                    alert("Please input Longitude Coordinates");
                    return false;
                }
				if (!reg.test(ab.completeUnitObj.address.latitude) || !reg.test(ab.completeUnitObj.address.longitude)) {
					alert("Please input A Valid Latitude and Longitude");
					return false;
				}
                return true;
            };
            ab.validateProductPricing = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.cloneUnitId)) {
                    alert("Please select a unit to clone.");
                    return false;
                }
                return true;
            };
            ab.validateInventoryClone = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.inventoryCloneUnitId)) {
                    alert("Please select a unit to clone.");
                    return false;
                }
                return true;
            };
            ab.validateMenuSequenceClone = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.menuSequenceCloneUnitId)) {
                    alert("Please select a unit to clone.");
                    return false;
                }
                return true;
            };
            ab.validateSalesUnitClone = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.salesClonedFrom)) {
                    alert("Please select a unit to clone.");
                    return false;
                }
                return true;
            };
            ab.validateManagers = function () {
                if (ab.isUndefinedOrNull(ab.completeUnitObj.unitManager)) {
                    alert("Please select Area Manager.");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.completeUnitObj.cafeManager)) {
                    alert("Please select Deputy Area Manager.");
                    return false;
                }
                return true;
            };

            function filterUnitsOnPriceProfile(){
                var filteredUnitList = [];
                if(ab.completeUnitObj.pricingProfile != null) {
                    for (var i = 0; i < ab.unitList.length; i++) {
                        if (ab.unitList[i].pricingProfile == ab.completeUnitObj.pricingProfile) {
                            filteredUnitList.push(ab.unitList[i]);
                        }
                    }
                    ab.unitList = filteredUnitList;
                } else {
                    ab.unitList = ab.allUnitList;
                }
            }

            function sortUnitList(){
                ab.unitList.sort((a, b) => {
                    var fa = a.name.toLowerCase();
                    var fb = b.name.toLowerCase();
                    return fa.localeCompare(fb);
                });
            }

            ab.showAllUnits = function(cloneListType){
                filterUnitsOnPriceProfile();
                var filteredUnitList = ab.unitList;

                ab.priceCloneUnitList = filteredUnitList;
                ab.inventoryCloneUnitList = filteredUnitList;
                ab.menuCloneUnitList = filteredUnitList;
                ab.salesCloneUnitList = filteredUnitList;

                if(ab.showAllPriceUnitsFlag) {
                    ab.priceCloneUnitList = ab.allUnitList;
                }
                if(ab.showAllInventoryUnitsFlag) {
                    ab.inventoryCloneUnitList = ab.allUnitList;
                }
                if(ab.showAllMenuUnitsFlag) {
                    ab.menuCloneUnitList = ab.allUnitList;
                }
                if(ab.showAllSalesUnitsFlag) {
                    ab.salesCloneUnitList = ab.allUnitList;
                }
            };

            ab.selectProductPriceCloningUnit = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.allUnits + '?category=' + ab.completeUnitObj.family
                }).then(function success(response) {
                    $scope.unitListOriginal = response.data;
                    ab.unitList = [];
                    $scope.unitListOriginal.forEach(function (unit) {
                        if(unit.region == ab.completeUnitObj.region){
                            ab.unitList.push(unit);
                        }
                    });
                    // ab.unitList = response.data;
                    if(ab.unitList.length == 0){
                        ab.unitList = response.data;
                    }
                    sortUnitList();
                    ab.allUnitList = ab.unitList;
                    filterUnitsOnPriceProfile();
                    ab.priceCloneUnitList = ab.unitList;
                    ab.inventoryCloneUnitList = ab.unitList;
                    ab.menuCloneUnitList = ab.unitList;
                    ab.salesCloneUnitList = ab.unitList;
                    if(ab.showAllPriceUnitsFlag) {
                        ab.priceCloneUnitList = ab.allUnitList;
                    }
                    if(ab.showAllInventoryUnitsFlag) {
                        ab.inventoryCloneUnitList = ab.allUnitList;
                    }
                    if(ab.showAllMenuUnitsFlag) {
                        ab.menuCloneUnitList = ab.allUnitList;
                    }
                    if(ab.showAllSalesUnitsFlag) {
                        ab.salesCloneUnitList = ab.allUnitList;
                    }

                    console.log("unit are total :",ab.unitList.length);
                    ab.newUnitProductPricing = ab.unitList[0];
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            ab.getAllDeliveryPartners = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.posMetaData.allDeliveryPartners
                }).then(function success(response) {
                    ab.partnersList = response.data;
                    console.log("partnersList= " + JSON.stringify(ab.partnersList));
                    var testarr = [];
                    testarr = getPartnerDetails(testarr, ab.partnersList);
                    ab.refPartnerLists = testarr;
                    console.log(ab.refPartnerLists);
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function getPartnerDetails(testarr, resPartnerDetails) {
                resPartnerDetails.forEach(function (chkdetailPartner) {
                    if (chkdetailPartner.type === 'EXTERNAL' || chkdetailPartner.status === 'ACTIVE') {
                        testarr.push(chkdetailPartner);
                    }
                });
                return testarr;
            }


            ab.priorityList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
            ab.addDeliveryPartner = function (deliveryPartner) {
                var duplicate = false;
                ab.completeUnitObj.deliveryPartners.forEach(function (addedPartnersListItem) {
                    if (addedPartnersListItem.detail.id === deliveryPartner.id) {
                        duplicate = true;
                        alert("Selected delivery partner already added!")
                    }
                });
                if (duplicate === false) {
                    var partner = {};
                    partner.detail = deliveryPartner;
                    partner.priority = 1;
                    ab.completeUnitObj.deliveryPartners.push(partner);
                }
            };
            ab.removeDeliveryPartner = function (deliveryPartnerId) {
                var index = -1;
                var i = 0;
                for (i = 0; i < ab.completeUnitObj.deliveryPartners.length; i++) {
                    if (ab.completeUnitObj.deliveryPartners[i].detail.id === deliveryPartnerId) {
                        index = i;
                        break;
                    }
                }
                if (index !== -1) {
                    ab.completeUnitObj.deliveryPartners.splice(index, 1);
                }

            };


            ab.getReportingManagers = function () {
                if (!ab.isUndefinedOrNull(ab.cafeManagers)) {
                    var index = ab.cafeManagers.findIndex(function (x) {
                        return x.id === ab.completeUnitObj.cafeManager.id;
                    });
                    ab.completeUnitObj.cafeManager = ab.cafeManagers[index];
                    return;
                }
                ab.cafeManagers = [];
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.userManagement.managers
                }).then(function success(response) {
                    ab.reportingManagers = response.data;
                    if (ab.reportingManagers != null) {
                        var i = 0;
                        for (i = 0; i < ab.reportingManagers.length; i++) {
                            ab.cafeManagers.push(response.data[i]);
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.userManagement.activeEmployees + "?designation=Zonal Manager"
                }).then(function success(response) {
                    if (ab.reportingManagers == null) {
                        ab.reportingManagers = response.data;
                    }
                    if (ab.reportingManagers != null) {
                        response.data.map(function (data) {
                            ab.reportingManagers.push(data);
                            ab.cafeManagers.push(data);
                        });
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });



                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.userManagement.activeEmployees + "?designation=Deputy Area Manager"
                }).then(function success(response) {
                    if (response != null && response.data != null) {
                        var i = 0;
                        for (i = 0; i < response.data.length; i++) {
                            ab.cafeManagers.push(response.data[i]);
                        }
                    }
                    if (!angular.isUndefined(ab.completeUnitObj.id)) {
                        var index = ab.cafeManagers.findIndex(function (x) {
                            return x.id === ab.completeUnitObj.cafeManager.id;
                        });
                        ab.completeUnitObj.cafeManager = ab.cafeManagers[index];
                    } else {
                        //ab.completeUnitObj.cafeManager = ab.cafeManagers[0];
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            ab.formatTime = function () {
                ab.completeUnitObj.operationalHours.forEach(function (item) {
                    if (!ab.isUndefinedOrNull(item.dineInOpeningTime))
                        item.dineInOpeningTime = item.dineInOpeningTime.toTimeString().slice(0, 8);
                    if (!ab.isUndefinedOrNull(item.dineInClosingTime))
                        item.dineInClosingTime = item.dineInClosingTime.toTimeString().slice(0, 8);
                    if (!ab.isUndefinedOrNull(item.deliveryOpeningTime))
                        item.deliveryOpeningTime = item.deliveryOpeningTime.toTimeString().slice(0, 8);
                    if (!ab.isUndefinedOrNull(item.deliveryClosingTime))
                        item.deliveryClosingTime = item.deliveryClosingTime.toTimeString().slice(0, 8);
                    if (!ab.isUndefinedOrNull(item.takeAwayOpeningTime))
                        item.takeAwayOpeningTime = item.takeAwayOpeningTime.toTimeString().slice(0, 8);
                    if (!ab.isUndefinedOrNull(item.takeAwayClosingTime))
                        item.takeAwayClosingTime = item.takeAwayClosingTime.toTimeString().slice(0, 8);
                    if (!ab.isUndefinedOrNull(item.shiftOneHandoverTime))
                        item.shiftOneHandoverTime = item.shiftOneHandoverTime.toTimeString().slice(0, 8);
                });
            };
            ab.checkTime = function(){
                var flag = true;
                var dineInMsg="";
                var deliveryMsg="";
                var takeawayMsg="";

                ab.completeUnitObj.operationalHours.forEach(function (item) {
                   if(item.isOperational){
                    if (ab.isUndefinedOrNull(item.dineInOpeningTime) || ab.isUndefinedOrNull(item.dineInClosingTime)){
                        dineInMsg="Dine-in";
                        flag = false;
                    }
                    if (ab.isUndefinedOrNull(item.deliveryOpeningTime) || ab.isUndefinedOrNull(item.deliveryClosingTime))
                    {   deliveryMsg="Delivery";
                        flag = false;
                    }
                    if (ab.isUndefinedOrNull(item.takeAwayOpeningTime) || ab.isUndefinedOrNull(item.takeAwayClosingTime))
                    {   takeawayMsg="Take away";
                        flag = false;
                    }
                   }
                });
                if(!flag){
                    alert(dineInMsg+" "+deliveryMsg+" "+takeawayMsg+" time cannot be empty!");
                }
                return flag;
            }
            ab.getUnitManager = function () {
                $http(
                    {
                        method: 'GET',
                        url: AppUtil.restUrls.userManagement.user + '?userId='
                        + ab.completeUnitObj.unitManager.id
                    }).then(function success(response) {
                    // $scope.reportingManagerObj = response.data;
                    ab.completeUnitObj.unitManager = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            ab.validateTimes = function () {
                ab.completeUnitObj.operationalHours.forEach(function (item) {
                    if (item.hasDineIn === false) {
                        item.dineInOpeningTime = null;
                        item.dineInClosingTime = null;
                    }
                    if (item.hasDelivery === false) {
                        item.deliveryOpeningTime = null;
                        item.deliveryClosingTime = null;
                    }
                    if (item.hasTakeAway === false) {
                        item.takeAwayOpeningTime = null;
                        item.takeAwayClosingTime = null;
                    }
                    if (item.handoverTick === false) {
                        item.shiftOneHandoverTime = null;
                    }
                })
            };

            ab.validateKioskBasicDetails = function () {
                if (ab.isUndefinedOrNull(ab.selectedCompany)) {
                    alert("Please select a company");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.selectedOffice)) {
                    alert("Please select an office");
                    return false;
                }
                if (ab.isUndefinedOrNull(ab.selectedLocation)) {
                    alert("Please select a location");
                    return false;
                }
                return true;
            };
            ab.next = function () {
                switch (ab.index) {
                    case 0:
                        if (ab.completeUnitObj.family === "CHAI_MONK") {
                            if (ab.validateKioskBasicDetails() ) {
                                ab.index = (ab.index + 1) % 14;
                            }
                        }
                        else {
                            if (ab.validateBasicDetails()) {
                                ab.index = (ab.index + 1) % 14;
                            }
                        }
                        break;
                    case 1:
                        ab.index = (ab.index + 1) % 14;
                        break;
                    case 2:
                        if (ab.validateDivisionDetails()) {
                            ab.index = (ab.index + 1) % 14;
                        }
                        break;

                    case 3:
                        if (ab.validateAddress()) {
                            ab.selectProductPriceCloningUnit();
                            if (angular.isUndefined(ab.completeUnitObj.id))
                                ab.index = (ab.index + 1) % 14;
                            else
                                ab.index = (ab.index + 5) % 14;
                        }
                        break;
                    case 4:
                        if (ab.validateProductPricing()) {
                            ab.index = (ab.index + 1) % 14;
                        }
                        break;
                    case 5:
                        if (ab.validateInventoryClone()) {
                            ab.index = (ab.index + 1) % 14;
                        }
                        break;

                    case 6:
                        if(ab.validateMenuSequenceClone()) {
                            ab.index = (ab.index + 1) % 14;

                        }
                        break;
                    case 7:
                        if(ab.validateSalesUnitClone()) {
                            ab.index = (ab.index + 1) % 14;

                        }
                        break;
                    case 8:
                        if(!ab.checkTime()){
                            return;
                        }
                        ab.index = (ab.index + 1) % 14;
                        break;
                    case 9:
                        ab.getReportingManagers();
                        ab.index = (ab.index + 1) % 14;
                        break;
                    case 10:
                        if (ab.validateManagers()) {
                            ab.getUnitManager();
                            ab.index = (ab.index + 1) % 14;
                        }
                        break;
                    case 11:
                        // if(ab.completeUnitObj.deliveryPartners.length === 0){
                        //     alert("Please select a delivery partner");
                        //     break;
                        // }
                        ab.index = (ab.index + 1) % 14;
                        break;
                    case 12:
                        if (ab.validateRevenueCertificateDetails()) {
                            ab.index = (ab.index + 1) % 14;
                        }
                        break;


                    case 13:
                            ab.validateTimes();
                            if (ab.isUndefinedOrNull(ab.selectedNoOfShifts)) {
                                alert("Select number of shifts.");
                                break;
                            }
                            ab.index = (ab.index + 1) % 14;
                            break;
                    default:
                        ab.index = (ab.index + 1) % 14;
                        break;
                }
            };

            ab.prev = function () {
                if (!ab.isUndefinedOrNull(ab.completeUnitObj.id)) {
                    if (ab.index === 8)
                        ab.index = (ab.index - 5) % 14;
                    else
                        ab.index = (ab.index - 1) % 14;
                }
                else {
                    ab.index = (ab.index - 1) % 14;
                }
            };
            ab.ok = function () {
                alert("You clicked the ok button.");
                $uibModalInstance.close();
            };
            ab.cancel = function () {
                if (!ab.isUndefinedOrNull(ab.backUp)) {
                    ab.completeUnitObj = ab.backUp;
                }
                $uibModalInstance.dismiss('cancel');
            };

            ab.fixYesNoOptions = function () {
                ab.completeUnitObj.workstationEnabled = ab.completeUnitObj.workstationEnabled.code;
                ab.completeUnitObj.freeInternetAccess = ab.completeUnitObj.freeInternetAccess.code;
                ab.completeUnitObj.tableService = ab.completeUnitObj.tableService.code;
                ab.completeUnitObj.tokenService = ab.completeUnitObj.tokenService.code;
                ab.completeUnitObj.dGAvailable = ab.completeUnitObj.dGAvailable.code;
                ab.completeUnitObj.liveInventoryEnabled = ab.completeUnitObj.liveInventoryEnabled.code;
                ab.completeUnitObj.revenueCertificateGenerationEnable = ab.completeUnitObj.revenueCertificateGenerationEnable.code;
                ab.completeUnitObj.assemblyStrictMode = ab.completeUnitObj.assemblyStrictMode.code;
                ab.completeUnitObj.milkTrackingEnabled = ab.completeUnitObj.milkTrackingEnabled.code;
                ab.completeUnitObj.assemblyOtpMode = ab.completeUnitObj.assemblyOtpMode.code;

            };
            ab.fixZeroOneOptions = function () {
                ab.completeUnitObj.tableServiceType = ab.completeUnitObj.tableServiceType.code;
            };
            ab.fixAddressOptions = function () {
                ab.completeUnitObj.address.country = ab.completeUnitObj.address.country.name;
                ab.completeUnitObj.address.state = ab.completeUnitObj.address.state.name;
            };
            ab.addNewUnit = function () {
                ab.formatTime();
                ab.fixYesNoOptions();
                ab.fixAddressOptions();
                ab.fixZeroOneOptions();
                ab.setEmailAllowedStatus(ab.completeUnitObj.isOtpAllowedViaEmail);
                if($rootScope.testingUnitCategory == 'TESTING_UNIT') {
                    ab.completeUnitObj.isTestingUnit = ab.completeUnitObj.isTestingUnit !== undefined 
                                                            && ab.completeUnitObj.isTestingUnit !== null 
                                                            && ab.completeUnitObj.isTestingUnit.code ? true : false;
                } else {
                    ab.completeUnitObj.isTestingUnit = null;
                }
                
                if (ab.completeUnitObj.startDate == null) {
                    ab.completeUnitObj.startDate = new Date();
                }
                ab.completeUnitObj.serviceChargeAppEnabled = ab.completeUnitObj.serviceChargeAppEnabled.code;
                ab.completeUnitObj.serviceChargePosEnabled = ab.completeUnitObj.serviceChargePosEnabled.code;
                $rootScope.showFullScreenLoader = true;
                console.log("completeUnitObj (unitCtrlNew)=", JSON.stringify(ab.completeUnitObj));
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.addUnit,
                    data: { ...ab.completeUnitObj, brands: $scope.selectedBrands }
                }).then(
                    function success(response) {
                        if (response.status == 200) {
                            $scope.unitIdForBrandMapping = response.data;
                            if (ab.completeUnitObj.family === "CHAI_MONK") {
                                $http(
                                    {
                                        method: 'GET',
                                        url: AppUtil.restUrls.kioskManagement.kioskAssignUnit
                                        + '?unitId=' + response.data + '&locationId='
                                        + ab.locationIdMonk,
                                    }).then(function success(response) {
                                    console.log("ResultAssigned=", response.data);
                                    $rootScope.showFullScreenLoader = false;
                                    alert("Unit added successfully!");
                                    window.location.reload();
                                }, function error(response) {

                                    console.log("error:" + response);
                                });
                            } else {
                                // response.data.deliveryPartners = ab.completeUnitObj.deliveryPartners;
                                $http({
                                    method: 'POST',
                                    url: AppUtil.restUrls.unitMetaData.addUpdateDeliveryPartners,
                                    data: {
                                        id: response.data,
                                        deliveryPartners: ab.completeUnitObj.deliveryPartners
                                    }
                                }).then(function success(response) {
                                    if (response.status == 200) {
                                        $rootScope.showFullScreenLoader = false;
                                        // $scope.showfarebut2 = false;
                                        console.log(response.data);
                                        alert("Unit added successfully!");
                                        console.log("IDD=", response.data);
                                        window.location.reload();
                                    } else {
                                        $rootScope.showFullScreenLoader = false;
                                        console.log(response);
                                    }
                                }, function error(response) {
                                    console.log("error:" + response);
                                });
                            }
                        } else {
                            console.log(response);
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
            }

            ab.editUnit = function () {
                ab.formatTime();
                ab.fixYesNoOptions();
                ab.fixZeroOneOptions();
                ab.fixAddressOptions();
                ab.setHotSpotStatus(ab.completeUnitObj.isHotspotEnabled);
                ab.setEmailAllowedStatus(ab.completeUnitObj.isOtpAllowedViaEmail);
                ab.completeUnitObj.serviceChargeAppEnabled = ab.completeUnitObj.serviceChargeAppEnabled.code;
                ab.completeUnitObj.serviceChargePosEnabled = ab.completeUnitObj.serviceChargePosEnabled.code;
                if($rootScope.testingUnitCategory != 'TESTING_UNIT') {
                    ab.completeUnitObj.isTestingUnit = null;
                }
                console.log("edit Unit = " + JSON.stringify(ab.completeUnitObj));
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.updateUnit,
                    data: { ...ab.completeUnitObj, brands: $scope.selectedBrands }
                }).then(function success(response) {
                    if (response.status === 200) {
                        $scope.unitIdForBrandMapping = ab.completeUnitObj.id;
                        alert(`Unit : ${ab.completeUnitObj.id} edited successfully!`);
                        $rootScope.showFullScreenLoader = false;
                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.unitMetaData.addUpdateDeliveryPartners,
                            data: ab.completeUnitObj,
                        }).then(function success(response) {
                            if (response.status == 200) {
                                if (ab.completeUnitObj.family == "CAFE") {
                                    console.log("updating cafe")
                                    $http({
                                        method: 'GET',
                                        url: AppUtil.restUrls.location.loadLocation + "/" + ab.completeUnitObj.company.id + "/" + ab.completeUnitObj.id
                                    }).then(function success(response) {
                                        if (response.status == 200) {
                                            $rootScope.detailLoaderMessage = "Updating DineIn App ...";
                                            $rootScope.showDetailLoader = true;
                                            $http({
                                                method: 'GET',  
                                                url: AppUtil.restUrls.cache.refreshUnit + ab.completeUnitObj.company.id + "/" + AppUtil.cafe + "/" + ab.completeUnitObj.id
                                            }).then(function success(response) {
                                                if (response.status == 200) {
                                                    console.log("unit refreshes");
                                                    $rootScope.showDetailLoader = false;
                                                    window.location.reload();

                                                }
                                            }, function error(response) {

                                                console.log("error:" + response);
                                            });
                                            console.log("unit updated")

                                        }
                                    }, function error(response) {
                                        console.log("error:" + response);
                                    });
                                }
                                console.log("DeliveryPartners updated");
                                if (ab.completeUnitObj.family != "CAFE") {
                                    $rootScope.showDetailLoader = false;
                                    window.location.reload();
                                }
                            } else {
                                $rootScope.showFullScreenLoader = false;
                                console.log(response);
                            }
                        //sumo update
                            $scope.SCMEditUnit(ab.completeUnitObj)
                            console.log("updating sumo")
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        console.log(response);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.SCMEditUnit = function (data ){
                console.log("edit Unit = " + JSON.stringify(data));
                $http({
                    method:'POST',
                    url:AppUtil.restUrls.unitMetaData.EditSCMUnit,
                    data:data
                }).then(function success(response) {
                    if (response.status === 200) {
                        console.log("updated successfully")
                    }else {
                        $rootScope.showFullScreenLoader = false;
                        console.log(response);
                    }
                    }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getUnitBrandMappings = function (unitId) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.getUnitBrandMappings,
                    params: { unitId: unitId }
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        $rootScope.showFullScreenLoader = false;
                        $scope.unitBrandMappings = response.data;
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        console.log(response);
                    }
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });
            }

            $scope.toggleBrandSelection = function (brand) {
                if (brand.checked) {
                    var exists = false;
                    $scope.selectedBrands.forEach(function (b) {
                        if (b.brandId === brand.brandId) {
                            exists = true;
                        }
                    });
                    if (!exists) {
                        $scope.selectedBrands.push(brand);
                    }
                } else {
                    var updatedSelectedBrands = [];
                    $scope.selectedBrands.forEach(function (b) {
                        if (b.brandId !== brand.brandId) {
                            updatedSelectedBrands.push(b);
                        }
                    });
                    $scope.selectedBrands = updatedSelectedBrands;
                }
                // console.log("Selected Brands are ::: ", $scope.selectedBrands);
            };            

            $scope.processCompanyBrandMap = function () {
                if ($scope.editMode) {
                    var selectedBrandIds = new Set($scope.unitBrandMappings.map(function(b) {
                        return b.brandId;
                    }));
        
                    $scope.companyBrandMap.forEach(function(brand) {
                        brand.checked = selectedBrandIds.has(brand.brandId);
                    });
        
                    $scope.selectedBrands = $scope.companyBrandMap.filter(function(b) {
                        return b.checked;
                    });
                } else {
                    $scope.companyBrandMap.forEach(function(brand) {
                        brand.checked = false
                    });
                    $scope.selectedBrands = [];
                }
            }

            $scope.addUnitBrandMappings = function () {
                if ($scope.unitIdForBrandMapping == null || $scope.unitIdForBrandMapping == undefined) {
                    alert.warning("Error Adding Unit Brand Mappings as Unit ID is null or undefined");
                    return;
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.addUnitBrandMappings,
                    params: { unitId: $scope.unitIdForBrandMapping },
                    data: $scope.selectedBrands
                }).then(
                    function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.data) {
                            alert("Unit Brand Mappings Updated Successfully for Unit ID : " + $scope.unitIdForBrandMapping);
                            window.location.reload();
                        } else {
                            alert.error("Error Updating Unit Brand Mappings for Unit ID : " + $scope.unitIdForBrandMapping);
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        alert.error("Error Updating Unit Brand Mappings for Unit ID : " + $scope.unitIdForBrandMapping);
                    }
                );
            }

            $scope.onCompanyChange = function (companyId) {
                $scope.selectedBrands = [];
                $scope.companyBrandMap = $scope.companyBrandMappings[companyId];
                $scope.companyBrandMap.forEach(function(brand) {
                    brand.checked = false
                });
            }

        }]);
