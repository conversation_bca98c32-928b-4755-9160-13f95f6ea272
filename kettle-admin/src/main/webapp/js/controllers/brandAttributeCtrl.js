/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("brandAttributeCtrl", function ($rootScope, $scope, $http, $location, $cookieStore, AppUtil, fileService) {

    $scope.init = function () {

        getBrandList();
        getBrandAttributeList();
        $scope.selectedBrand = null;
        $scope.selectedBrandId = null;
        $scope.brands = [];
        $scope.brandAttributeMap = {};

    }

    $scope.initAfterUpdation = function () {
        $scope.selectedBrand = null;
        $scope.selectedBrandId = null;
    }

    function getBrandList() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.brandList = response.data;
                for (var i = 0; i < $scope.brandList.length; i++) {
                    $scope.brands.push($scope.brandList[i].brandName);
                }
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getBrandAttributeList() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrandAttributes,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.brandAttributeList = response.data;
                $scope.getBrandAttributeArray($scope.brandAttributeList);
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.getBrandAttributeArray = function (brandAttributeList) {
        for (var i = 0; i < brandAttributeList.length; i++) {
            var attributeVal = {
                attribute: brandAttributeList[i].attributeKey,
                value: brandAttributeList[i].attributeValue
            };
            if ($scope.brandAttributeMap[brandAttributeList[i].brandId] == null || $scope.brandAttributeMap[brandAttributeList[i].brandId] == undefined) {
                var attributes = [];
                attributes.push(attributeVal);
                $scope.brandAttributeMap[brandAttributeList[i].brandId] = attributes;
            }
            else {
                $scope.brandAttributeMap[brandAttributeList[i].brandId].push(attributeVal);
            }
        }
        $scope.brandAttributeMapCpy = angular.copy($scope.brandAttributeMap);
        console.log($scope.brandAttributeMap);
    }

    $scope.editBrandMappingModalOpen = function () {
        $("#editBrandMappingModal").modal("show");
    }

    $scope.getSelectedBrandId = function (selectedBrand) {
        for (var i = 0; i < $scope.brandList.length; i++) {
            if ($scope.brandList[i].brandName === selectedBrand) {
                $scope.selectedBrandId = $scope.brandList[i].brandId;
            }
        }
        console.log($scope.selectedBrandId);
    }

    $scope.updateBrandAttributeMapping = function () {
        $rootScope.showFullScreenLoader = true;
        var mappings = [];
        var previousBrandAttributeValue = $scope.brandAttributeMapCpy[$scope.selectedBrandId];
        var currentAttributeValue = $scope.brandAttributeMap[$scope.selectedBrandId];
        for (var i = 0; i < previousBrandAttributeValue.length; i++) {
            for (var j = 0; j < currentAttributeValue.length; j++) {
                if (currentAttributeValue[j].attribute === previousBrandAttributeValue[i].attribute &&
                    currentAttributeValue[j].value !== previousBrandAttributeValue[i].value) {
                    var mapping = {
                        brandId: $scope.selectedBrandId,
                        attributeKey: currentAttributeValue[j].attribute,
                        attributeValue: currentAttributeValue[j].value,
                        attributeType: "java.lang.String"
                    }
                    mappings.push(mapping);
                    break;
                }
            }
        }
        console.log(mappings);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.brandMetaData.updateBrandAttributes,
            data: mappings
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                alert("Attribute updated successfully");
                $("#editBrandMappingModal").modal("hide");
                $rootScope.showFullScreenLoader = false;
                $scope.initAfterUpdation();
                return;
            } else {
                console.log(response);
                alert("Error while updating");
                $rootScope.showFullScreenLoader = false;
                $scope.initAfterUpdation();
            }
        }, function error(response) {
            console.log("error:" + response);
            alert("Error while updating");
        });
    }

});