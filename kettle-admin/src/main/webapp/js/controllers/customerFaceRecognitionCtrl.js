

adminapp.controller("customerFaceRecognitionCtrl",
    function($scope,$http,$location,$window,$rootScope, $timeout, AuthService,AppUtil){

        $scope.init = function(){
            console.log("face rec data");
            $scope.number=null;
        }

        $scope.deRegisterCustomer= function(number){
            if(number==null || number.length!=10){
                bootbox.alert("Enter Correct Number");
            }else {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.faceRecognition.deRegisterCustomer,
                    data: number
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200) {
                        if (response.data == true) {
                            bootbox.alert("Success fully done ");
                        } else {
                            bootbox.alert("No Face Id found with contact number");
                        }
                    }else {
                        bootbox.alert("Error ");
                    }
                }, function error(response) {
                    bootbox.alert("Error");
                    console.log("error:" + response);
                });
            }
            console.log(number);
            console.log($scope.number);
        }

        $scope.optOutCustomer= function(number){
            if(number==null || number.length!=10){
                bootbox.alert("Enter Correct Number");
            }else{
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.customer.crmFaceIdDataUpdate+'?flag='+false,
                    data: number
                }).then(function success(response){
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200) {
                        if (response.data == '' || response.data == null) {
                            bootbox.alert("No Face Id found with contact number ");
                        } else {
                            bootbox.alert("Success-fully done ");
                        }
                    } else {
                        bootbox.alert("Error");
                    }
                }, function error(response) {
                    bootbox.alert("Error");
                    console.log("error:" + response);
                });
            }
        }


    }
);
