adminapp.controller("PosCustomerController", function($scope, $location,$log, $http, AuthService,$cookieStore){
	//var baseURLss="http://dev.kettle.chaayos.com:9595/kettle-crm/rest/v2/";
	$scope.getCustomerByPhone =function()
	{
		if($scope.posMobileNo=="" || $scope.posMobileNo==null)
			{
			alert("Mobile No is empty");
			return false;
			}
			
			
	};

    function convert(str) {
	    var date = new Date(str),
	    mnth 		= ("0" + (date.getMonth()+1)).slice(-2),
        day  		= ("0" + date.getDate()).slice(-2);
        hours  		= ("0" + date.getHours()).slice(-2);
        minutes 	= ("0" + date.getMinutes()).slice(-2);
        seconds 	= ("0" + date.getSeconds()).slice(-2);
        return [ hours, minutes,seconds ].join(":");
	}     
  
	
  var userObj = {id:null,name:null,contact:$scope.posMobileNo,email:null,loyalityPoints:null,contactVerified:false,emailVerified:false,unitId:null,newCustomer:true,
			  otp:null,chaiRedeemed:0,productId:null};
  
	$scope.noofShift = [{code:1, name:1},{code:2, name:2}];
	$scope.selectNoOfShift=$scope.noofShift[0];
	  
	$scope.init = function(){
		  $scope.tickDineStartTime 			= 	new Date();
		  $scope.tickDineCloseTime 			=	new Date();
		  $scope.singleDineInStartTime 		=	new Date();
		  $scope.singleDineInCloseTime 		= 	new Date();
		  
		  $scope.tickCODStartTime 			= 	new Date();
		  $scope.tickCODCloseTime 			=	new Date();
		  $scope.singleDeliveryStartTime 	=	new Date();
		  $scope.singleDeliveryCloseTime 	= 	new Date();
		  
		  $scope.tickTakeAwayStartTime 		= 	new Date();
		  $scope.tickTakeAwayCloseTime 		=	new Date();
		  $scope.singleTakeAwayStartTime 	=	new Date();
		  $scope.singleTakeAwayCloseTime 	= 	new Date();
		  
		  $scope.singleHandOverTime 		= 	new Date();
		  $scope.tickHandOverTime 			= 	new Date();
		  
		  $hoursMinsChq=" 10:59:54 "
		  
		  $scope.tickDineStartTime="Fri May 13 2016" +$hoursMinsChq+ "GMT+0530 (India Standard Time)";
		  //$scope.tickCODCloseTime 			=	new Date();
		  //$scope.singleDeliveryStartTime 	=	new Date();
		  //$scope.singleDeliveryCloseTime 	= 	new Date();
		 // $scope.defaultData				=	"true"
			  
			 
			 if($scope.selectNoOfShift.code==1)
				 {
				 $scope.defaultData	=	true; 
				 }
			 else
				 {
				 $scope.defaultData	=	false; 
				 }
	}
  
	  $scope.hstep = 1;
	  $scope.mstep = 1;

	  $scope.options = {
	    hstep: [1, 2, 3],
	    mstep: [1, 5, 10, 15, 25, 30]
	  };

	  $scope.ismeridian = false;
	  $scope.toggleMode = function() {
	    $scope.ismeridian = ! $scope.ismeridian;
	  };

	  $scope.update = function() {
	    var d = new Date();
	    d.setHours( 14 );
	    d.setMinutes( 0 );
	    $scope.mytime = d;
	  };

	  $scope.changed = function () {
	    $log.log('Time changed to: ' + $scope.tickDineStartTime);
	    alert('Time changed to: ' + $scope.tickDineStartTime);
	    alert('Time changed to: ' + $scope.tickDineCloseTime);
	    
	  };

	  $scope.changedDineStartCheck= function () {
		  $scope.checkedDine=false
	  }
	  $scope.changedCodStartCheck= function () {
		  $scope.checkedCod=false
	  }
	  $scope.changedTakeAwayStartCheck= function () {
		  $scope.checkedTakeAway=false
	  }
	  
	  $scope.changedTakeAwayStartCheck= function () {
		  $scope.checkedTakeAway=false
	  }
	  
	  $scope.changeShift= function (noOfShift) {
	 if(noOfShift==2){
			 //alert("show");
			 $scope.defaultData	=	false;
			 
			// $scope.defaultData='ng-show="true"'; 
		 }
	 else if(noOfShift==1){
		 //alert("show");
		 $scope.defaultData	=	true;
		 
		// $scope.defaultData='ng-show="true"'; 
	 }
	  }
	  
	  
	  $scope.changedd= function () {
		  alert('Time changed to: ' + $scope.singleDineInStartTime);
	  }
	  
	  $scope.changedDineStartDate= function (DaysNames,val) {
		  for(var index in $scope.Items)
		  {
			if($scope.Items[index].dayOfTheWeek==DaysNames)
		    	{
  				$scope.Items[index].dineInOpeningTime =	convert($scope.singleDineInStartTime[val]);
			    }
	    	}
		 }
	  
	  
	  $scope.changedDineCloseDate= function (DaysNames,val) {
		
		 for(var index in $scope.Items)
		  {
			if($scope.Items[index].dayOfTheWeek==DaysNames)
		    	{
 				$scope.Items[index].dineInClosingTime =	convert($scope.singleDineInCloseTime[val]);
			    }
	    	}
		 
		}
	  
	  $scope.clear = function() {
	    $scope.mytime = null;
	  };
	
	  $scope.changedDeliveryStartDate= function (DaysNames,val) {
		  for(var index in $scope.Items)
		  {
			if($scope.Items[index].dayOfTheWeek==DaysNames)
		    	{
  				$scope.Items[index].deliveryOpeningTime =	convert($scope.singleDeliveryStartTime[val]);
			    }
	    	}
		 }
	  $scope.changedDeliveryCloseDate= function (DaysNames,val) {
		  for(var index in $scope.Items)
		  {
			if($scope.Items[index].dayOfTheWeek==DaysNames)
		    	{
  				$scope.Items[index].deliveryClosingTime =	convert($scope.singleDeliveryCloseTime[val]);
			    }
	    	}
		 }
	  
	  $scope.changedTakeAwayStartDate= function (DaysNames,val) {
		  for(var index in $scope.Items)
		  {
			if($scope.Items[index].dayOfTheWeek==DaysNames)
		    	{
  				$scope.Items[index].takeAwayOpeningTime =	convert($scope.singleTakeAwayStartTime[val]);
			    }
	    	}
		 }
		  $scope.changedTakeAwayCloseDate= function (DaysNames,val) {
			  for(var index in $scope.Items)
			  {
				if($scope.Items[index].dayOfTheWeek==DaysNames)
			    	{
	  				$scope.Items[index].takeAwayClosingTime =	convert($scope.singleTakeAwayCloseTime[val]);
				    }
		    	}
			 }
	  
		  $scope.changedHandOverTime= function (DaysNames,val) {
			  for(var index in $scope.Items)
			  {
				if($scope.Items[index].dayOfTheWeek==DaysNames)
			    	{
	  				$scope.Items[index].shiftOneHandoverTime =	convert($scope.singleHandOverTime[val]);
				    }
		    	}
			 }
		  
		  
		  
		  //Fri May 13 2016 14:59:54 GMT+0530 (India Standard Time)
	  
	// $scope.Items = [{dayCheck: "Item one",day:"Sunday",dineCheck:"dine1",codCheck:"cod1",takeAwayCheck:"tk1",handOver:"ho1"}, {dayCheck: "Item two",day:"Monday",dineCheck:"dine2",codCheck:"cod2",takeAwayCheck:"tk2",handOver:"ho2"}, {dayCheck: "Item three",day:"Tuesday",dineCheck:"dine3",codCheck:"cod3",takeAwayCheck:"tk3",handOver:"ho3"} ,{dayCheck: "Item four",day:"Wednesday",dineCheck:"dine4",codCheck:"cod4",takeAwayCheck:"tk4",handOver:"ho4"}, {dayCheck: "Item five",day:"Thursday",dineCheck:"dine5",codCheck:"dine5",takeAwayCheck:"tk4",handOver:"ho5"},{dayCheck: "Item Six",day:"Friday",dineCheck:"dine6",codCheck:"cod6",takeAwayCheck:"tk6",handOver:"ho6"}, {dayCheck: "Item Seven",day:"Saturday",dineCheck:"dine7",codCheck:"dine6",takeAwayCheck:"tk7",handOver:"ho7"}];	 
 
	  //$scope.Items1 =  [
	                    //{dayOfTheWeekNumber:"1",dayOfTheWeek:"Sunday",noOfShifts:"1",isOperational: true, isOperational: true,hasDelivery: true,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "09:00:00",dineInClosingTime: "19:00:00",deliveryOpeningTime: "09:00:00", deliveryClosingTime: "19:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"}]; 
	  
		  $scope.Items =
				[{dayOfTheWeekNumber: "1",dayOfTheWeek:"Sunday",noOfShifts:"1",isOperational: true, hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00", deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"},
				{dayOfTheWeekNumber: "2",dayOfTheWeek:"Monday",noOfShifts:"1",isOperational: true,hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00",deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"},
				{dayOfTheWeekNumber: "3",dayOfTheWeek:"Tuesday",noOfShifts:"1",isOperational: true, hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00",deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"},
				{dayOfTheWeekNumber: "4",dayOfTheWeek:"Wednesday",noOfShifts:"1",isOperational: true, hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00",deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"},
				{dayOfTheWeekNumber: "5",dayOfTheWeek:"Thursday",noOfShifts:"1",isOperational: true,hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00",deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"},
				{dayOfTheWeekNumber: "6",dayOfTheWeek:"Friday",noOfShifts:"1",noOfShifts:"1",isOperational: true,hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00",deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"},
				{dayOfTheWeekNumber: "7",dayOfTheWeek:"Saturday",noOfShifts:"1",isOperational: true, hasDelivery: false,hasDineIn: true, hasTakeAway: false,dineInOpeningTime: "18:12:49",dineInClosingTime: "22:12:20",deliveryOpeningTime: "00:00:00",deliveryClosingTime: "00:00:00",takeAwayOpeningTime: null,takeAwayClosingTime: null,shiftOneHandoverTime: null,shiftTwoHandoverTime: null,status: "ACTIVE"}];	 
					 
				  
  $scope.checkAllDays = function () {
	  if ($scope.checkedDays) {
	            
	            angular.forEach($scope.Items, function (item) {
		        item.daysTick 				= 	$scope.checkedDays;
		        item.isOperational			=	true;
		        });
	        } else {
	           
	            angular.forEach($scope.Items, function (item) {
		        item.daysTick = $scope.checkedDays;
		       item.isOperational			=	false;
		      });
	        }
	      };
	      
	     
	 
	    $scope.checkAllDineIn = function () {
	    	console.log($scope.tickDineStartTime);
	    	console.log($scope.tickDineCloseTime);
	    	var tickDineStartTime1								=	convert($scope.tickDineStartTime);
	    	var tickDineCloseTime1								=	convert($scope.tickDineCloseTime);
	    	if($scope.checkedDine) {
	    	for(i in $scope.Items){
		    		$scope.singleDineInStartTime[i]				=	$scope.tickDineStartTime;
		    		$scope.singleDineInCloseTime[i]				=	$scope.tickDineCloseTime;
		    		$scope.Items[i].noOfShifts					=	$scope.selectNoOfShift.code;
		    		$scope.Items[i].hasDineIn 					= 	$scope.checkedDine;
		    		$scope.Items[i].dineTick 					= 	true;
		    		$scope.Items[i].daysTick 					= 	true;
		    		$scope.Items[i].isOperational				=	true;
		    		$scope.Items[i].dineInOpeningTime			=	tickDineStartTime1;
		    		$scope.Items[i].dineInClosingTime 			= 	tickDineCloseTime1;
	    	}
	    		}
	    	else
	    		{
	    		for(i in $scope.Items){
	    			$scope.singleDineInStartTime[i]				=	$scope.tickDineStartTime;
		    		$scope.singleDineInCloseTime[i]				=	$scope.tickDineCloseTime;
		    		$scope.Items[i].dineTick 					= 	false;
		    		$scope.Items[i].daysTick 					= 	false;
		    		$scope.Items[i].isOperational				=	false;
		    		$scope.Items[i].noOfShifts					=	$scope.selectNoOfShift.code;
		    		$scope.Items[i].dineInOpeningTime			=	tickDineStartTime1;
		    		$scope.Items[i].dineInClosingTime 			= 	tickDineCloseTime1;
		    	}
	    		}
	    	};
	  
	    $scope.checkAllCodIn = function () {
	    	var tickDeliveryStartTimes											=	convert($scope.tickCODStartTime);
	    	var tickDeliveryCloseTimes											=	convert($scope.tickCODCloseTime);
		    if($scope.checkedCod) {
		        	    	for(i in $scope.Items){
		        		    		$scope.singleDeliveryStartTime[i]			=	$scope.tickCODStartTime;
		        		    		$scope.singleDeliveryCloseTime[i]			=	$scope.tickCODCloseTime;
		        		    		$scope.Items[i].noOfShifts					=	$scope.selectNoOfShift.code;
		        		    		$scope.Items[i].hasDelivery 				= 	true
		        		    		$scope.Items[i].codeTick 					= 	true;
		        		    		$scope.Items[i].daysTick 					= 	true;
		        		    		$scope.Items[i].isOperational				=	true;
		        		    		$scope.Items[i].deliveryOpeningTime			=	tickDeliveryStartTimes;
		        		    		$scope.Items[i].deliveryClosingTime 		= 	tickDeliveryCloseTimes;
		        	    			}
		        	    		}
		        	    	else
		        	    		{
		        	    		for(i in $scope.Items){
		        	    			$scope.singleDeliveryStartTime[i]			=	$scope.tickDineStartTime;
		        		    		$scope.singleDeliveryCloseTime[i]			=	$scope.tickDineCloseTime;
		        		    		$scope.Items[i].noOfShifts					=	$scope.selectNoOfShift.code;
		        		    		$scope.Items[i].hasDelivery 				= 	false
		        		    		$scope.Items[i].codeTick 					= 	false;
		        		    		$scope.Items[i].daysTick 					= 	false;
		        		    		$scope.Items[i].isOperational				=	false;
		        		    		$scope.Items[i].deliveryOpeningTime			=	tickDeliveryStartTimes;
		        		    		$scope.Items[i].deliveryClosingTime 		= 	tickDeliveryCloseTimes;
		        		    	}
		        	    	}
	    		};
	
	    $scope.checkTakeAwayIn = function () {
	    	var tickTakeAwayStartTimes											=	convert($scope.tickTakeAwayStartTime);
	    	var tickTakeAwayCloseTimes											=	convert($scope.tickTakeAwayCloseTime);
	    	 if ($scope.checkedTakeAway) {
				    	for(i in $scope.Items){
				    		$scope.singleTakeAwayStartTime[i]					=	$scope.tickTakeAwayStartTime;
				    		$scope.singleTakeAwayCloseTime[i]					=	$scope.tickTakeAwayCloseTime;
				    		$scope.Items[i].noOfShifts							=	$scope.selectNoOfShift.code;
				    		$scope.Items[i].hasTakeAway 						= 	true
				    		$scope.Items[i].takeawayTick 						= 	true;
				    		$scope.Items[i].daysTick 							= 	true;
				    		$scope.Items[i].isOperational						=	true;
				    		$scope.Items[i].takeAwayOpeningTime					=	tickTakeAwayStartTimes;
				    		$scope.Items[i].takeAwayClosingTime 				= 	tickTakeAwayCloseTimes;
			    			}
	    	 			}
	    	 	else 
	    	 		{
		 	    		for(i in $scope.Items){
		 	    			$scope.singleTakeAwayStartTime[i]					=	$scope.tickTakeAwayStartTime;
				    		$scope.singleTakeAwayCloseTime[i]					=	$scope.tickTakeAwayCloseTime;
				    		$scope.Items[i].noOfShifts							=	$scope.selectNoOfShift.code;
				    		$scope.Items[i].hasTakeAway 						= 	false
				    		$scope.Items[i].takeawayTick 						= 	false;
				    		$scope.Items[i].daysTick 							= 	false;
				    		$scope.Items[i].isOperational						=	false;
				    		$scope.Items[i].takeAwayOpeningTime					=	tickTakeAwayStartTimes;
				    		$scope.Items[i].takeAwayClosingTime 				= 	tickTakeAwayCloseTimes;
		 		    		}
	    	 			}
	      			};
	    $scope.checkAllHandOverIn = function () {
	    	var tickHandOverStartTimes											=	convert($scope.tickHandOverTime);
	    	alert($scope.checkedHandOver);
	    	if ($scope.checkedHandOver) {
	    		 alert("1");
				    	for(i in $scope.Items){
				    		alert("2");
				    		$scope.singleHandOverTime[i]						=	$scope.tickHandOverTime;
				    		$scope.Items[i].noOfShifts							=	$scope.selectNoOfShift.code;
				    		$scope.Items[i].handoverTick 						= 	true;
				    		$scope.Items[i].daysTick 							= 	true;
				    		$scope.Items[i].isOperational						=	true;
				    		$scope.Items[i].shiftOneHandoverTime 				= 	tickHandOverStartTimes
			    			}
	    	 			}
	    	 		else 
	    	 			{
	    	 			alert("3");
		 	    		for(i in $scope.Items){
		 	    			alert("4");
		 	    			$scope.singleTakeAwayStartTime[i]					=	$scope.tickTakeAwayStartTime;
				    		$scope.singleTakeAwayCloseTime[i]					=	$scope.tickTakeAwayCloseTime;
				    		$scope.Items[i].noOfShifts							=	$scope.selectNoOfShift.code;
				    		$scope.Items[i].handoverTick 						= 	false
				    		$scope.Items[i].daysTick 							= 	false;
				    		$scope.Items[i].isOperational						=	false;
				    		$scope.Items[i].shiftOneHandoverTime 				= 	tickHandOverStartTimes;
		 		    		}
	    	 			}
	    };
	    $scope.checkDays = function (daysName,daysList) {
	    	if(daysList==true)
	    		{
	    		for(var index in $scope.Items){
	    		if($scope.Items[index].dayOfTheWeek==daysName){
		    		{
		    			$scope.Items[index].isOperational=true;
		    		}
	    			}
	    		}
	    		}
	    	else
	    		{
	    		if(daysList==false)
	    		{
	    		for(var index in $scope.Items){
		    		if($scope.Items[index].dayOfTheWeek==daysName)
		    			{
		    				$scope.Items[index].isOperational=false;
		    			}
		    			}
	    		}
	    		}
	    }
	    $scope.checkDineDetails = function (dineDaysName,dineDaysList,values) {
	    	if(dineDaysList==true)
	    		{
	    			for(var index in $scope.Items){
		    			if($scope.Items[index].dayOfTheWeek==dineDaysName)
				    			{
		    					$scope.Items[index].hasDineIn	=	true;
				    			}
			    			}
	    		}
	    	else 
    		{
		    	for(var index in $scope.Items){
	    			if($scope.Items[index].dayOfTheWeek==dineDaysName)
			    			{ 
	    						$scope.Items[index].hasDineIn	=	false;
	    					}
		    			}
    		}
	    	};
	    
	    $scope.checkDeliveryDetails = function (deliveryDaysName,deliveryDaysList) {
	    
	    	if(deliveryDaysList==true)
	    		{
			    	for(var index in $scope.Items){
		    			if($scope.Items[index].dayOfTheWeek==deliveryDaysName)
				    			{
		    					$scope.Items[index].hasDelivery	=	true;
					    		}
			    			}
	    		}
	    	else 
    		{
		    	for(var index in $scope.Items){
	    			if($scope.Items[index].dayOfTheWeek==deliveryDaysName)
			    			{
		    					$scope.Items[index].hasDelivery	=	false;
			    			}
		    			}
    		}
	    	}; 
	    
	    $scope.checkTakeAwayDetails = function (takeAwayDaysName,takeAwayDaysList) {
	    
	    	if(takeAwayDaysList==true)
    		{
		    	for(var index in $scope.Items){
	    			if($scope.Items[index].dayOfTheWeek==takeAwayDaysName)
			    			{
	    						$scope.Items[index].hasTakeAway		=	true;
			    			}
		    			}
    		}
    	else 
		{
	    	for(var index in $scope.Items){
    			if($scope.Items[index].dayOfTheWeek==takeAwayDaysName)
		    			{
	    					$scope.Items[index].hasTakeAway		=	false;
		    			}
	    			}
		}
	    	
	    };
	    $scope.checkHandOverDetails = function (HandOverName,handOverDaysList) {
	    	
	    	if(HandOverName==true)
    		{
		    	for(var index in $scope.Items){
	    			if($scope.Items[index].dayOfTheWeek==HandOverName)
			    			{
	    						$scope.Items[index].shiftOneHandoverTime			=	$scope.singleHandOverTime;
			    			}
		    			}
    				}
    	else 
		{
	    	for(var index in $scope.Items){
    			if($scope.Items[index].dayOfTheWeek==HandOverName)
		    			{
	    				$scope.Items[index].shiftOneHandoverTime		=	$scope.singleHandOverTime;
		    			}
	    			}
		}
	    };
});