adminapp.controller("DeliveryCouponUploadCtrl", function ($scope, $http, AppUtil, fileService, $rootScope) {

    $scope.init = function () {
    };

    $scope.downloadDeliveryCouponSheet = function () {
        $rootScope.showFullScreenLoader = true;
        $http(
            {
                method: 'GET',
                url: AppUtil.restUrls.offerManagement.downloadDeliveryCouponSheetTemplate,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            })
            .then(
                function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != undefined && response != null) {
                        var fileName = "DELIVERY_COUPON_TEMPLATE" + " - " + Date.now() + ".xlsx";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }
                },
                function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                    alert("Unable to download Template");
                });
    };

    $scope.uploadDeliveryCouponSheet = function () {
        if (fileService.getFile() == null || fileService.getFile() == undefined) {
            bootbox.alert("Please select a .xlsx file");
            return;
        }

        console.log("File is", fileService.getFile());

        var fd = new FormData();
        fd.append("file", fileService.getFile());
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.offerManagement.uploadDeliveryCouponSheet,
            method: 'POST',
            data: fd,
            headers: {
                'Content-Type': undefined
            },
            transformRequest: angular.identity
        }).success(function (response) {
            $rootScope.showFullScreenLoader = false;
            angular.element("input[type='file']").val(null);
            fileService.push(null);
            console.log(response);
            if (response.errorMessage == null) {
                var alertMessage = "File Uploaded";
                if (response.length > 0) {
                    alertMessage += " (" + response.length + " coupons with errors were not saved)";
                }
                alert(alertMessage);
                window.location.reload();
            }
            else {
                alert(response.errorMessage);
            }

        }).error(function (response) {
            $rootScope.showFullScreenLoader = false;
            alert("Error while uploading Delivery Coupon Sheet");
        });
    };

});