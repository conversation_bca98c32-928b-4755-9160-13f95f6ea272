adminapp.controller("generateWinbackCouponCtrl",['$scope', 'AppUtil', '$http', '$stateParams', 'fileService', '$cookieStore', '$rootScope',
    function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

    $scope.init = function (){
        $scope.Source=["DINE_IN","DELIVERY"];
        $scope.selectedSource="";
        $scope.result = null;
        $scope.deliveryCoupons = {};
        $scope.dineInCoupons = {};
        $scope.deliveryCouponsList = [];
        $scope.dineInCouponsList = [];
        $scope.showFurther = false;
        $scope.showPartner = false;
        $scope.selectedOffer = "";
        $scope.channelPartner = ["Zomato","Swiggy"];
        $scope.selectedPartner = "";
        $scope.contactNumber = "";
        $scope.customerName = "";
        $scope.validityInDays = "";
        $scope.comment = "";
        $scope.orderId = "";
        $scope.compensationReason = [];
        $scope.selectedCompensationReason = "";
        $scope.offerId = "";
        $scope.offerCode = "";
        $scope.offerDescription = "";
        $scope.channelPartnerId = "";
        $scope.couponResponse=null;
        $scope.complainSourceOption = ["Letstalk Email","Feedback Email","Twitter Complaint","On Call Complaint","Zomato Review Complaint"];
        $scope.selectedComplainSource = "";
    };

    $scope.selectComplainSourceValue = function(value){
        $scope.selectedComplainSource = value;
    }

    $scope.initializeNotifyVariables = function(){
        $scope.notifyResponse = "";
    }


    $scope.getCoupons = function(){
        var tempSource = $scope.selectedSource;
        $scope.init();
        $scope.selectedSource = tempSource;
        $scope.deliveryCoupons = {};
        $scope.dineInCoupons = {};
        $scope.deliveryCouponsList = [];
        $scope.dineInCouponsList = [];
        $scope.showFurther = false;
        if($scope.selectedSource === "DINE_IN"){
           $scope.getCouponsDineIn();
        }else{
           $scope.getCouponsDelivery();
        }
        $scope.getReasonList();
    }

    $scope.refineDineInCouponResponse = function(data){
        for(var i=0;i<data.length;i++){
            var descriptionString = data[i].offerId + " : " + data[i].offerDescription
            $scope.dineInCouponsList.push(descriptionString);
            $scope.dineInCoupons[data[i].offerId] = data[i];
        }
    }

    $scope.refineDeliveryCouponResponse = function(data){
        for(var i=0;i<data.length;i++){
            var descriptionString = data[i].id + " : " + data[i].offerDescription
            $scope.deliveryCouponsList.push(descriptionString);
            $scope.deliveryCoupons[data[i].id] = data[i];
        }
        console.log($scope.deliveryCouponsList);
        console.log($scope.deliveryCoupons);
    }

    $scope.getCouponsDineIn = function(){
        $rootScope.showFullScreenLoader = true;
        var couponUrl = AppUtil.restUrls.winback.getDineInCoupon;
        $http({
           method: 'GET',
           url: couponUrl
        }).then(function success(response) {
            $scope.refineDineInCouponResponse(response.data);
            $scope.showFurther = true;
           $rootScope.showFullScreenLoader = false;
        }, function error(response) {
           bootbox.alert("Error while fetching Winback Coupons")
           console.log("error: " + response);
        });
    }

        $scope.getCouponsDelivery = function(){
            $rootScope.showFullScreenLoader = true;
            var couponUrl = AppUtil.restUrls.winback.getDeliveryCoupn;
            $http({
               method: 'GET',
               url: couponUrl
            }).then(function success(response) {
                $scope.showFurther = true;
                $scope.showPartner = true;
                $scope.refineDeliveryCouponResponse(response.data)
               $rootScope.showFullScreenLoader = false;
            }, function error(response) {
               bootbox.alert("Error while fetching Winback Coupons")
               console.log("error: " + response);
            });
        }

        $scope.onConatctNumberInput=function(e){
            $scope.contactNumber = e;
        }

        $scope.onCustomerNameInput = function(e){
           $scope.customerName = e;
        }
        $scope.onValidityInDaysInput = function(e){
                if(e==null){
                    $scope.validityInDays = e;
                }else if(e>=1 && e <=365){
                    $scope.validityInDays = e;
                }else{
                    alert("Validity lie between 1 to 365 days");
                }
        }

        $scope.onCommentInput = function(e){
            $scope.comment = e;
        }

        $scope.onOrderIdInput = function(e){
            $scope.orderId = e;
        }
        $scope.getOfferId = function(offerString){
            var parts = offerString.split(" : ");
            $scope.offerId = parts[0].trim();
        }
        $scope.getOfferIdAndCode = function(){
            $scope.getOfferId($scope.selectedOffer);
            if($scope.selectedSource=="DINE_IN"){
                $scope.offerDescription = $scope.dineInCoupons[$scope.offerId].offerDescription;
            }else{
                $scope.offerCode = $scope.deliveryCoupons[$scope.offerId].offerCode;
                $scope.offerDescription = $scope.deliveryCoupons[$scope.offerId].offerDescription;
                if($scope.selectedPartner == "Zomato"){
                    $scope.channelPartnerId = 3;
                }else if($scope.selectedPartner=="Swiggy"){
                    $scope.channelPartnerId = 6;
                }
            }
        }
        $scope.addMappings = function(){
            $scope.getOfferIdAndCode();
            var phoneNumberPattern = /^\d{10}$/;
            if($scope.contactNumber == null || $scope.contactNumber == "" || !phoneNumberPattern.test($scope.contactNumber)){
                alert("Contact Number is Not Valid");
            }else if($scope.customerName == null || $scope.customerName == "" ){
                 alert("Customer Name is empty");
            }else if($scope.selectedCompensationReason == null || $scope.selectedCompensationReason == "" ){
                 alert("Compensation Reason is empty")
            }else if($scope.validityInDays == null || $scope.validityInDays == "" || ($scope.validityInDays<=1 && $scope.validityInDays>=365)){
                 alert("validity in days is not valid")
            }else{
            $rootScope.showFullScreenLoader = true;
            var domain = {
                contactNumber : $scope.contactNumber,
                customerName : $scope.customerName,
                offerId : $scope.offerId,
                offerCode : $scope.offerCode,
                offerDescription : $scope.offerDescription,
                validityInDays : $scope.validityInDays,
                compensationReason : $scope.selectedCompensationReason,
                comment : $scope.comment,
                orderId : $scope.orderId,
                orderSource : $scope.selectedSource,
                complainSource : $scope.selectedComplainSource,
                channelPartnerId : $scope.channelPartnerId,
                isNotified : "N",
                updatedBy : $rootScope.userData.id
            };
            $http({
               method: 'POST',
               url: AppUtil.restUrls.winback.generateCoupon,
               data : domain
            }).then(function success(response) {
               if(response.data==null || response.data == ""){
                    alert("Error While Generating Coupon");
                    $rootScope.showFullScreenLoader = false;
               }else{
                    console.log(response)
                    $scope.couponResponse = response.data;
                    $scope.notifyResponse = $scope.couponResponse;
                    $("#couponDetail").modal("show");
                    $scope.init();
                    $rootScope.showFullScreenLoader = false;
               }
            }, function error(response) {
               window.alert("Error while Generating Coupoon")
               console.log("error: " + response);
               $rootScope.showFullScreenLoader = false;
            });
            }
        }

        $scope.selectOfferValue = function (selectedOffer){
            $scope.selectedOffer = selectedOffer;
        }
        $scope.selectCompensationReasonValue = function (reason){
            $scope.selectedCompensationReason = reason;
        }
        $scope.selectChannelPartnerValue = function(partner){
            $scope.selectedPartner = partner;
        }
        $scope.selectSourceValue = function(source){
            $scope.selectedSource = source;
            $scope.showFurther=false;
        }

        $scope.markNotify = function(){
            $rootScope.showFullScreenLoader = true;
            $http({
               method: 'POST',
               url: AppUtil.restUrls.winback.notify,
               data : $scope.notifyResponse.id
            }).then(function success(response) {
               $("#couponDetail").modal("hide");
               if(response.data==null || response.data==""){
                   alert("Error while Notify Customer !")
                   $rootScope.showFullScreenLoader = false;
               }else{
               alert("Customer Notified !");
               $scope.initializeNotifyVariables();
               $scope.init();
               $rootScope.showFullScreenLoader = false;
               }
            }, function error(response) {
               window.alert("Error while Notify Customer !")
               console.log("error: " + response);
               $rootScope.showFullScreenLoader = false;
            });
        }

        $scope.notNow = function(){
             $("#couponDetail").modal("hide");
             $scope.initializeNotifyVariables();
             $scope.init();
        }

        $scope.getReasonList = function(){
            $http({
               method: 'GET',
               url: AppUtil.restUrls.winback.compensationReason
            }).then(function success(response) {
               $scope.compensationReason = response.data;
            }, function error(response) {
               bootbox.alert("Error while fetching Winback Coupons")
               console.log("error: " + response);
            });
        }


    }]);