
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
adminapp.controller('monkMetaDataCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http','fileService',
        function ($location, $scope, AppUtil, $rootScope, $http,fileService) {
            $scope.init = function () {
                if (AppUtil.getEnvType() === 'PROD') {
                    //$scope.imageSuffix = "https://d3pjt1af33nqn0.cloudfront.net/product_image/"; //prod
                } else {
                    $scope.imageSuffix = "http://d1nqp92n3q8zl7.cloudfront.net/product_image/";//dev
                }
            // console.log("welcome to monk");
                $scope.metaDataList = [{name: 'MONK_IMAGE_LOGO', value: 'image'},
                    {name: 'MONK_WELCOME_VIDEO', value: 'image'},
                    {name: 'MONK_SCREEN_SAVER', value: 'image'},
                    {name: 'MONK_BACKGROUND', value: 'image'}];
                $scope.metaData = $scope.metaDataList[0];

            }

            $scope.uploadProductPic=function (){
                if (fileService.getFile() == null
                    || fileService.getFile() == undefined) {
                    bootbox.alert('File cannot be empty');
                    return;
                }

                // if(fileService.getFile().size > 1048576){
                //     bootbox.alert('File size should not be greater than 1 MB or 1024 KB');
                //     return;
                // }

                var fileExt = getFileExtension(fileService.getFile().name);
                if (isImage(fileExt.toLowerCase())) {

                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();

                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('file', fileService.getFile());
                    fd.append('brandId', 4);
                    fd.append('attributeKey', $scope.metaData.name);
                    fd.append('attributeValue',null);
                    fd.append('isImage',true);
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: AppUtil.restUrls.brandMetaData.uploadMonkMetadata,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).then(function success(response) {
                        console.log("success:" + response);
                        $rootScope.showFullScreenLoader = false;
                        if (response != null && response.status == 200 && response.data=='true') {
                            bootbox.alert("Upload successful");
                        } else {
                            bootbox.alert("Error while uploading Product Image.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Upload failed");
                    });


                    }else {
                    bootbox.alert('Upload Failed , File Format not Supported');
                }

            };
            function isImage(fileExt) {
                return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png" || fileExt=="mp4" ;
            }

            function getFileExtension(fileName) {
                var re = /(?:\.([^.]+))?$/;
                return re.exec(fileName)[1];
            }

            $scope.uploadProductVideo = function (showCaseVideo) {
                console.log($scope.metaData.name);
                $scope.videoLink = showCaseVideo;
                var fd = new FormData();

                var data=AppUtil.getUserValues();
                fd.append('brandId', 4);
                fd.append('attributeKey', $scope.metaData.name);
                fd.append('attributeValue', $scope.videoLink);
                fd.append('isImage',false);
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: AppUtil.restUrls.brandMetaData.uploadMonkMetadata,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).then(function success(response) {
                    console.log("success:" + response);
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response.status == 200 && response.data=='true') {
                        bootbox.alert("Upload successful");
                    } else {
                        bootbox.alert("Error while uploading Product Image.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("Upload failed");
                });

            };
        }
        ]);