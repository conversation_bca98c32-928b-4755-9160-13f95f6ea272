/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("ReportSummaryController", function ($rootScope, $scope, $http, $location, AppUtil, fileService) {

    $scope.init = function () {
        $scope.reportCategories = null;
        $scope.showToAdmin = AppUtil.checkPermission('master-service.*');
        $scope.errorMessage = null;
        $scope.checkAccess = false;
        checkAccessToAddNewReport();
        $http({
            method: 'GET',
            url: AppUtil.restUrls.reportServiceMetaData.reportEnviroments
        }).then(function success(response) {
            $scope.environments = response.data;
            $scope.environment = $scope.environments[1];
            $scope.getReportCategoryListByEnv($scope.environment);
        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.departments
        }).then(function success(response) {
            $scope.departmentlist = response.data;
            $rootScope.showFullScreenLoader = false;
            $scope.selectedDepartment = $scope.departmentlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.reportServiceMetaData.reportType
        }).then(function success(response) {
            $scope.reportTypes = response.data;
            $scope.selectedReportType = $scope.reportTypes[0];
        }, function error(response) {
            console.log("error:" + response);
        });

    };

    $scope.getReportCategoryListByEnv = function (execEnv) {
        $scope.totalReport = 0;
        $scope.reportVersionList = null;
        $scope.report = null;
        $scope.reportList = null;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.reportServiceMetaData.reportCategoryNew + "?executionEnvironment=" + execEnv,
        }).then(function success(response) {
            $scope.reportList = response.data;
            $scope.report = $scope.reportList[0];
            $scope.totalReport = $scope.reportList.length;
            //console.log($scope.reportList);
            if($scope.reportList != null && $scope.reportList.length > 0){
                $scope.getReportVersionDetailsById($scope.report);
            } else {
                $scope.report = null;
                $scope.reportVersionList = null;
                $scope.totalReport = 0;
                $scope.totalItems = 0;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.getReportVersionDetailsById = function (report) {
        if (report == null){
            return;
        }
        $scope.report = report;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.reportServiceMetaData.reportVersionHistory,
            data: report.reportId
        }).then(function success(response) {
            $scope.reportVersionList = response.data;

            $scope.currentPage = 1; //current page
            $scope.entryLimit = 50; //max no of items to display in a page
            $scope.totalItems = $scope.reportVersionList.length;

        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.updateReportStatus = function () {
        var reportDetail = $scope.report;
        var status = reportDetail.reportStatus === "ACTIVE" ? "IN_ACTIVE" : "ACTIVE";
        $http({
            method: 'GET',
            url: AppUtil.restUrls.reportServiceMetaData.updateReportStatus + "?reportId=" + reportDetail.reportId + "&status=" + status,
        }).then(function success(response) {
            $scope.updated = response.data;
            console.log($scope.updated);
            if (response.status != 200) {
                alert('Something went wrong');
                return;
            } else {
                $scope.getReportCategoryListByEnv($scope.environment);
                alert("Status Successfully updated");
            }
            //window.location.reload()
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.markAsDefaultVersion = function (versionId) {
        var reportDetail = $scope.report;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.reportServiceMetaData.markDefaultVersion + "?reportId=" + reportDetail.reportId + "&versionId=" + versionId,
        }).then(function success(response) {
            $scope.updated = response.data;
            console.log($scope.updated);
            if (response.status != 200) {
                alert('Something went wrong');
                return;
            } else {
                $scope.getReportVersionDetailsById($scope.report);
                alert("Status Successfully updated");
            }
            //window.location.reload()
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.submitAddNewReportData = function () {
        var environment_type = $scope.environment;

        if ($scope.reportName == null || $scope.reportName == "") {
            alert("Please Enter Report Name.");
            return;
        }

        // if(environment_type == "PROD" || environment_type == "SPROD") {
        //     if (!$scope.showToAdmin){
        //         alert("You are not authorized to add New report.");
        //         return;
        //     }
        // }
        var reportSummary = {
            reportName: $scope.reportName,
            reportType: $scope.selectedReportType,
            departmentId: $scope.selectedDepartment.id,
            departmentName: $scope.selectedDepartment.name,
            executionEnvironment: environment_type
        };
        console.log(reportSummary);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.reportServiceMetaData.addNewReport,
            data: reportSummary
        }).then(function success(response) {
            if (response.status != 200) {
                alert('Something went wrong');
                return;
            } else {
                $scope.reportName = "";
                $scope.selectedDepartment = $scope.departmentlist[0];
                $scope.selectedReportType = $scope.reportTypes[0];
                $('#AddNewReportModalData').modal('hide');
                $scope.getReportCategoryListByEnv($scope.environment);
                alert("Report Added Successfully");
            }
            // window.location.reload()
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.updateNewReportVersionData = function () {

        if ($scope.comments == null || $scope.comments == "") {
            alert("Please Enter comments.");
            return;
        }

        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select a XML file");
            return;
        }

        if($scope.environment == "PROD" || $scope.environment == "SPROD") {
            if (!$scope.checkAccess){
                alert("You are not authorized to upload New Version.");
                return;
            }
        }

        var fd = new FormData();
        fd.append("file", fileService.getFile());
        fd.append("comments", $scope.comments);
        fd.append("reportId", $scope.report.reportId);
        console.log(fd);
        $rootScope.showDetailLoader = true;
        var URL = AppUtil.restUrls.reportServiceMetaData.uploadNewReportVersion;
        $http({
            url: URL,
            method: 'POST',
            data: fd,
            headers: {
                'Content-Type': undefined
            },
            transformRequest: angular.identity
        }).success(function (response) {
            $rootScope.showDetailLoader = false;
            $scope.comments = "";
            $scope.fileToUpload.data = {};
            $scope.fileToUpload = null;
            angular.element("input[type='file']").val(null);
            $('#UploadNewReportVersionModalData').modal('hide');
            $scope.getReportVersionDetailsById($scope.report);
            alert("Report Version Added");
        }).error(function (response) {
            $rootScope.showDetailLoader = false;
            alert("Error while uploading Expense Sheet");
        });
    };

    function camelize(str) {
        console.log(str);
        return str.replace(/(^|\w)[A-Za-z0-9]*/g, function (letter, index) {
            return index == 0 ? letter.toUpperCase() : letter.toLowerCase();
        }).replace(/\s+/g, '');
    }

    String.prototype.capitalize = function () {
        return this.replace(/(?:^|\s)\S/g, function (a) {
            return a.toUpperCase();
        });
    };

    $scope.showQuery = function () {
        $("#queryDisplayModal").modal("show");
    }

    function checkAccessToAddNewReport() {
        var aclData = $rootScope.aclData;
        var actionName = "ADM_RD_ANR";
        if ((aclData != null && aclData.action != null && aclData.action[actionName] != null ) || $scope.showToAdmin) {
            $scope.checkAccess = true;
        }
    }

    $scope.addNewReportCategoryDialog = function () {
        $("#AddNewReportModalData").modal("show");
    }

    $scope.uploadNewVersionDialog = function () {
        $("#UploadNewReportVersionModalData").modal("show");
    }

    $scope.downloadNewReportVersion = function (versionId) {
        $rootScope.showDetailLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.reportServiceMetaData.downloadNewReportVersion,
            data: versionId,
            responseType: 'arraybuffer',
            headers: {
                'Content-type': 'application/json',
                'Accept': 'application/xml'
            }
        }).then(function success(response) {
            if (response && response.data != null) {
                $rootScope.showDetailLoader = false;
                if(response.status != 200){
                    alert("Could not download report");
                } else {
                    var contentDisposition = response.headers('Content-Disposition');
                    var fileName = contentDisposition.split(';')[1].split('filename')[1].split('=')[1].trim();
                    var blob = new Blob([response.data], {
                        type: 'c'
                    }, fileName);
                    saveAs(blob, fileName);
                }
            } else {
                alert("Could not fetch report.");
            }
        }, function error(response) {
            if (response && response.errorMsg) {
                alert(errorMsg);
            } else {
                alert('Error getting Report.');
            }
            $rootScope.showDetailLoader = false;
        });
    };

    $scope.downloadReportTemplate = function () {
        $scope.downloadNewReportVersion(0)
    };
});