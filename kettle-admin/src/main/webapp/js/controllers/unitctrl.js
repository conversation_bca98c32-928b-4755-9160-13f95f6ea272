/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        "UnitController",
        function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil, $uibModal) {

            var pc = this;
            pc.unitData = null;
            $scope.open = function () {
                var modalInstance = $uibModal.open({
                    animation: true,
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'views/modal.html',
                    backdrop: 'static',
                    controller: 'modalCtrl',
                    controllerAs: 'ab',
                    size: 'lg',
                    resolve: {
                        unitData: function () {
                            return pc.unitData;
                        },
                        monkRecipeProfiles: function() {
                            return $rootScope.monkRecipeProfiles
                        }
                    }
                });

                modalInstance.result.then(function () {
                    //alert("Result");
                }, function () {
                    //alert("Dismissal");
                });
            };


            $scope.init = function () {
                $rootScope.enableScreenFilter = true;

                if ($location.path() === "/dashboard/unit") {
                    $location.path("/dashboard/unit/CAFE");
                    $scope.loading = false;
                }
                $scope.cafeManagers = [];
                $scope.tickDineStartTime = new Date();
                $scope.tickDineCloseTime = new Date();
                $scope.singleDineInStartTime = new Date();
                $scope.singleDineInCloseTime = new Date();

                $scope.tickCODStartTime = new Date();
                $scope.tickCODCloseTime = new Date();
                $scope.singleDeliveryStartTime = new Date();
                $scope.singleDeliveryCloseTime = new Date();

                $scope.tickTakeAwayStartTime = new Date();
                $scope.tickTakeAwayCloseTime = new Date();
                $scope.singleTakeAwayStartTime = new Date();
                $scope.singleTakeAwayCloseTime = new Date();

                $scope.singleHandOverTime = new Date();
                $scope.tickHandOverTime = new Date();
                $scope.businessTypeList = ["COCO", "FICO", "FIFO"];
                $scope.packagingTypeList=["FIXED","PERCENTAGE"];
                $scope.zoneList=["NORTH","SOUTH","EAST","WEST"];
                $scope.selectedBusinessType = $scope.businessTypeList[0];
                $scope.dgOptionList = [{name: "YES", value: "true"}, {name: "NO", value: "false"}];
               
                $scope.assemblyStrictMode = {
                    code: false,
                    name: "No"
                }
                $scope.assemblyOtpMode = {
                    code: false,
                    name: "No"
                }

                $scope.selectTab('tab1');
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.regions
                }).then(function success(response) {
                    $scope.regions = response.data;
                    $scope.newUnitRegion = $scope.regions[0];
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.companies
                }).then(function success(response) {
                    $scope.newUnitCompanyList = response.data;
                    $scope.newUnitCompany = $scope.newUnitCompanyList[0];
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.divisions
                }).then(function success(response) {
                    $scope.divisions = response.data;
                    console.log($scope.divisions);
                    $scope.newUnitDivision = $scope.divisions[0];
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.families
                }).then(function success(response) {
                    $scope.families = response.data;
                    // console.log("allFamily=",$scope.families);
                    $scope.newUnitFamily = $scope.families[0];
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.state + '?countryId=1'
                }).then(function success(response) {
                    $scope.stateList = response.data;
                    console.log("stateList", $scope.stateList)
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.userManagement.managers
                }).then(function success(response) {
                    $scope.reportingManagers = response.data;
                    if ($scope.reportingManagers != null) {
                        var i = 0;
                        for (i = 0; i < $scope.reportingManagers.length; i++) {
                            $scope.cafeManagers.push(response.data[i]);
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                    //$scope.selectedReportingManager = $scope.reportingManagers[0];
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.userManagement.activeEmployees + "?designation=Deputy Area Manager"
                }).then(function success(response) {
                    if (response != null && response.data != null) {
                        var i = 0;
                        for (i = 0; i < response.data.length; i++) {
                            $scope.cafeManagers.push(response.data[i]);
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                    //$scope.selectedCafeManager = $scope.cafeManagers[0];
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.subCategories
                }).then(function success(response) {
                    $scope.subCategories = response.data;
                    $scope.subCategory = $scope.subCategories[0];
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.country
                }).then(function success(response) {
                    console.log("country=", response.data);
                    $scope.countriesList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });

            };

            $scope.showCountryList = function (cid) {
                cid = 101;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.state + '?countryId=' + cid
                }).then(function success(response) {
                    $scope.stateList = response.data;
                    console.log("stateList", $scope.stateList)
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.workstationUnit = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];

            $scope.freeInternetAccessUnit = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];

            $scope.tableServiceUnit = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];

            $scope.tableServiceType = [{
                code: 0,
                name: 0
            }, {
                code: 1,
                name: 1
            }];

            $scope.tokenServiceUnit = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];

            $scope.assemblyStrictModeUnit = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];
   
            $scope.assemblyOtpModeUnit = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];
            $scope.selectTab = function (tabIndex) {
                $scope.loading = false;
                switch (tabIndex) {
                    case 'tab1':
                        $scope.tab1 = false;
                        $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab2':
                        $scope.tab2 = false;
                        $scope.tab1 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab3':
                        $scope.tab3 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab4':
                        $scope.tab4 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab5':
                        $scope.tab5 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab6':
                        $scope.tab6 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab7':
                        $scope.tab7 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab8 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab8':
                        $scope.tab8 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab9 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab9':
                        $scope.tab9 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab10 = $scope.tab11 = true;
                        break;
                    case 'tab10':
                        $scope.tab10 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab11 = true;
                        break;

                    case 'tab11':
                        $scope.tab11 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = $scope.tab9 = $scope.tab10 = true;
                        break;
                }
            };

            $scope.addNewUnitModal = function () {
                $scope.unitAction = "add";
                $scope.selectTab('tab1');
                $scope.address = {};
                $scope.address.state = $scope.stateList[0];
                $scope.address.country = "India";
                $scope.address.addressType = "OFFICIAL";
                $scope.newUnitTerminals = 1;
                $scope.noOfTakeAway = 1;
                $scope.newUnitDivision = $scope.divisions[0];
                $scope.newUnitFamily = $scope.families[0];
                $scope.newUnitRegion = $scope.regions[0];
                $scope.newUnitCompany = $scope.newUnitCompanyList[0];
                $scope.newPartnerList = $scope.refPartnerLists != null && $scope.refPartnerLists.length > 0 ? $scope.refPartnerLists[0]
                    : null;
                $scope.workstationEnabled = $scope.workstationUnit[0];
                $scope.freeInternetAccess = $scope.freeInternetAccessUnit[0];
                $scope.tableService = $scope.tableServiceUnit[0]; // tableServiceUnit
                $scope.tableServiceType = $scope.tableServiceType[0];
                $scope.tokenService = $scope.tokenServiceUnit[0];
                $scope.maxTokenLimit = null;
                $scope.selectedSubCategory = $scope.subCategories[0];
                $scope.noOfTables = 1;
                $scope.address.latitude = null;
                $scope.address.longitude = null;
                $scope.newUnitName = null;
                $scope.referenceUnitName = null;
                $scope.newUnitTin = null;
                $scope.newUnitEmail = null;
                $scope.newChannel = null;
                $scope.editUnitBtn = false;
                $scope.addUnitBtn = true;
                $scope.noOfMeter = null;
                $scope.dGAvailable = null;
                $scope.liveInventoryEnabled = null;
                $scope.selectedBusinessType = $scope.businessTypeList[0];
                $scope.assemblyStrictMode = $scope.assemblyStrictModeUnit[0];
                $scope.assemblyOtpMode = $scope.assemblyOtpModeUnit[0];

                $("#unitManagemmentModal").modal("show");

            };

            $scope.gotoAddressTab = function (tabName) {
                if ($scope.unitToedit == undefined || $scope.unitToedit == null) {
                    $scope.selectTab(tabName);
                    return;
                }
                if ($scope.countriesList != null) {
                    for (var k in $scope.countriesList) {
                        if ($scope.countriesList[k].name == $scope.unitToedit.address.country) {
                            $scope.selectedAddressCountry = $scope.countriesList[k];
                            $scope
                                .showCountryView(
                                    $scope.countriesList[k],
                                    function () {
                                        for (var i in $scope.stateList) {
                                            if ($scope.stateList[i].name == $scope.unitToedit.address.state) {
                                                $scope.selectedAddressState = $scope.stateList[i];
                                                $scope
                                                    .showStateList(
                                                        $scope.selectedAddressState.id,
                                                        function () {
                                                            for (var i in $scope.locationList) {
                                                                if ($scope.locationList[i].name == $scope.unitToedit.address.city) {
                                                                    $scope.selectedAddressCity = $scope.locationList[i];
                                                                }
                                                            }

                                                        });
                                                break;
                                            }
                                        }
                                    });
                        }
                    }
                }
                $scope.selectTab(tabName);
            }

            $scope.gotoLocationTab = function () {
                if ($scope.countriesList != null) {
                    for (var k in $scope.countriesList) {
                        if ($scope.countriesList[k].name == $scope.unitToedit.location.state.country.name) {
                            $scope.selectedCountryName = $scope.countriesList[k];
                            break;
                        }
                    }

                }
                if ($scope.stateList != null) {
                    for (var i in $scope.stateList) {
                        if ($scope.stateList[i].name == $scope.unitToedit.location.state.name) {
                            $scope.selectedStateName = $scope.stateList[i];
                            $scope.showStateList($scope.selectedAddressState.id, function () {
                                for (var i in $scope.locationList) {
                                    if ($scope.locationList[i].name == $scope.unitToedit.location.name) {
                                        $scope.selectedLocationName = $scope.locationList[i];
                                    }
                                }

                            });
                            break;
                        }
                    }
                }
            }

            $scope.validateBasicDetail = function () {
                if ($scope.newUnitTin == null || $scope.newUnitTin == "") {
                    alert("Please fill GSTIN number.");
                    return;
                }
                if ($scope.newUnitName == null || $scope.newUnitName == "") {
                    alert("Please fill unit name");
                    return;
                } else {
                    $scope.newUnitName = $scope.newUnitName.replace(/\w\S*/g, function (txt) {
                        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                    });
                }
                if ($scope.referenceUnitName == null || $scope.referenceUnitName == "") {
                    alert("Please Enter Reference name of Unit");
                    return;
                }
                if ($scope.newUnitRegion == null) {
                    alert("Please fill unit region");
                    return;
                }
                if ($scope.newUnitCompany == null) {
                    alert("Please fill unit company");
                    return;
                }
                if ($scope.newUnitFamily == null) {
                    alert("Please fill unit family");
                    return;
                }
                if ($scope.newUnitEmail == null || !validateEmail($scope.newUnitEmail)) {
                    alert("Please fill valid email address");
                    return;
                }
                if ($scope.newChannel == null) {
                    alert("Please fill valid communication channel");
                    return;
                }
                if ($scope.noOfTables == null || $scope.noOfTables == undefined) {
                    alert("No of Table is required");
                    return;
                }
                if ($scope.noOfTakeAway < 1) {
                    alert("No of Take Away is empty, it should be minimum 1");
                    return false;
                }
                if ($scope.workstationEnabled == null || $scope.workstationEnabled == "") {
                    alert("Workstation Enabled is empty");
                    return;
                }
                if ($scope.freeInternetAccess == null || $scope.freeInternetAccess == "") {
                    alert("Free Internet Access Enabled is empty");
                    return;
                }
                if ($scope.tableService == null || $scope.tableService == "") {
                    alert("Table Service Enabled is empty");
                    return;
                }
                if ($scope.tableServiceType == null || $scope.tableServiceType == "") {
                    alert("Table Service Type is empty");
                    return;
                }
                if ($scope.tokenService == null || $scope.tokenService == "") {
                    alert("Token Service Enabled is empty");
                    return;
                }
                if ($scope.tokenService.code) {
                    if ($scope.maxTokenLimit == null || $scope.maxTokenLimit < 0) {
                        alert("Please fill correct token limit");
                        return;
                    }
                }
                if ($scope.newUnitTerminals == null) {
                    alert("Please fill terminals count");
                    return;
                }

                if ($scope.noOfMeter == null || $scope.noOfMeter == undefined) {
                    alert("Please fill electricity metetr count");
                    return;
                }

                if ($scope.dGAvailable == null || $scope.dGAvailable == undefined) {
                    alert("Please choose DG avilable status");
                    return;
                }

                if ($scope.liveInventoryEnabled == null || $scope.liveInventoryEnabled == undefined) {
                    alert("Please choose Live Inventory status");
                    return;
                }
                if ($scope.assemblyStrictMode == null || $scope.assemblyStrictMode == "") {
                    alert("Assembly Stric Mode is empty");
                    return;
                }
                if ($scope.assemblyOtpMode == null || $scope.assemblyOtpMode == "") {
                    alert("Assembly Cod Orders Otp Mode is empty");
                    return;
                }

                $scope.selectTab('tab2');
            }

            function validateEmail(email) {
                var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email);
            }

            $scope.validateAddress = function () {
                if ($scope.address.line1 == null || $scope.address.line1 == "") {
                    alert("Please fill line 1");
                    return;
                }
                if ($scope.address.line2 == null || $scope.address.line2 == "") {
                    alert("Please fill line 2");
                    return;
                }
                if ($scope.address.zipCode == null || $scope.address.zipCode == 'null') {
                    alert("Please fill zipcode");
                    return false;
                }
                if ($scope.address.contact1 == null || $scope.address.contact1 == 'null'
                    || $scope.address.contact1.s) {
                    alert("Please fill contact 1");
                    return false;
                }
                if ($scope.address.latitude == null || $scope.address.latitude == 'null') {
                    alert("Please fill Latitude");
                    return false;
                }
                if ($scope.address.longitude == null || $scope.address.latitude == 'null') {
                    alert("Please fill Longitude");
                    return false;
                }
                $scope.selectTab('tab5');
                var validated = true;
                if (validated) {
                    if ($scope.unitAction == "add") {
                        selectProductPriceCloningUnit();
                        $scope.selectTab('tab5');
                    } else {
                        var priorlist = [];
                        var priorNo = [];
                        $scope.prioritiesObj = {};
                        $scope.newUnitDeliveryPartners.forEach(function (prior) {
                            if (prior.priority != 0 || prior.priority != "") {
                                priorlist.push(prior);
                                $scope.prioritiesObj[prior.detail.id] = prior.priority;
                            }
                        });
                        $scope.addedPartners = priorlist;
                        composeCompleteUnitObj();
                        $scope.selectTab('tab7');
                    }
                }
            }

            function selectProductPriceCloningUnit() {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.allUnits + '?category=' + $scope.newUnitFamily
                }).then(function success(response) {
                    $scope.unitlist = response.data;
                    $scope.newUnitProductPricing = $scope.unitlist[0];
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.validateUnitProductPricing = function () {
                if ($scope.newUnitProductPricing == null && $scope.newUnitFamily == "CAFE"
                    && $scope.newUnitFamily == "COD" && $scope.newUnitFamily == "DELIVERY") {
                    alert("Please select unit from which product pricing has to be cloned.");
                    return;
                }
                $scope.selectTab('tab6')
            }

            $scope.skipProductCloning = function () {
                $scope.selectTab('tab6');
            }

            $scope.skipInventoryCloning = function () {
                $scope.selectTab('tab7');
            }

            $scope.skipPartnerDetail = function () {
                $scope.selectTab('tab8');
            }
            $scope.validateUnitPartnerDetails = function () {
                if ($scope.unitAction == "add") {
                    $scope.tickDineStartTime = '2016-08-06T02:30:00.957Z';
                    $scope.tickCODStartTime = '2016-08-06T02:30:00.957Z';
                    $scope.tickTakeAwayStartTime = '2016-08-06T02:30:00.957Z';
                    $scope.tickHandOverTime = '2016-08-06T02:30:00.957Z';
                    $scope.tickDineCloseTime = '2016-08-06T18:00:00.957Z';
                    $scope.tickCODCloseTime = '2016-08-06T18:00:00.957Z';
                    $scope.tickTakeAwayCloseTime = '2016-08-06T18:00:00.957Z';
                    $scope.tickHandOverTime = '2016-08-06T22:00:00.957Z';
                    $scope.selectTab('tab8');
                } else {
                    if ($scope.Items.length == 0) {
                        $scope.Items = [{
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "1",
                            dayOfTheWeek: "Sunday",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: false,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }, {
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "2",
                            dayOfTheWeek: "Monday",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: false,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }, {
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "3",
                            dayOfTheWeek: "Tuesday",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: false,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }, {
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "4",
                            dayOfTheWeek: "Wednesday",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: false,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }, {
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "5",
                            dayOfTheWeek: "Thursday",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: false,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }, {
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "6",
                            dayOfTheWeek: "Friday",
                            noOfShifts: "1",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: true,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }, {
                            id: null,
                            unitId: $scope.completeUnitObj.id,
                            dayOfTheWeekNumber: "7",
                            dayOfTheWeek: "Saturday",
                            noOfShifts: "1",
                            isOperational: false,
                            hasDelivery: false,
                            hasDineIn: false,
                            hasTakeAway: false,
                            dineInOpeningTime: "00:00:00",
                            dineInClosingTime: "00:00:00",
                            deliveryOpeningTime: "00:00:00",
                            deliveryClosingTime: "00:00:00",
                            takeAwayOpeningTime: null,
                            takeAwayClosingTime: null,
                            shiftOneHandoverTime: null,
                            shiftTwoHandoverTime: null,
                            status: "ACTIVE",
                            daysTick: false,
                            dineTick: false,
                            codeTick: false,
                            takeawayTick: false
                        }];

                        $scope.completeUnitObj.operationalHours = $scope.Items;

                    } else {

                        $scope.tickDineStartTime = '2016-08-06T02:30:00.957Z';
                        $scope.tickCODStartTime = '2016-08-06T02:30:00.957Z';
                        $scope.tickTakeAwayStartTime = '2016-08-06T02:30:00.957Z';
                        $scope.tickHandOverTime = '2016-08-06T02:30:00.957Z';

                        $scope.tickDineCloseTime = '2016-08-06T18:00:00.957Z';
                        $scope.tickCODCloseTime = '2016-08-06T18:00:00.957Z';
                        $scope.tickTakeAwayCloseTime = '2016-08-06T18:00:00.957Z';
                        $scope.tickHandOverTime = '2016-08-06T22:00:00.957Z';

                        for (i in $scope.Items) {
                            console.log($scope.Items[i]);

                            if ($scope.completeUnitObj.operationalHours[i].dayOfTheWeek == $scope.Items[i].dayOfTheWeek) {
                                $scope.Items[i].id = $scope.completeUnitObj.operationalHours[i].id;
                                $scope.Items[i].unitId = $scope.completeUnitObj.operationalHours[i].unitId;
                                $scope.Items[i].dayOfTheWeekNumber = $scope.completeUnitObj.operationalHours[i].dayOfTheWeekNumber;
                                $scope.Items[i].noOfShifts = $scope.completeUnitObj.operationalHours[i].noOfShifts;
                                $scope.Items[i].daysTick = $scope.completeUnitObj.operationalHours[i].isOperational;
                                $scope.Items[i].isOperational = $scope.completeUnitObj.operationalHours[i].isOperational;
                                $scope.Items[i].hasDineIn = $scope.completeUnitObj.operationalHours[i].hasDineIn;
                                $scope.Items[i].hasTakeAway = $scope.completeUnitObj.operationalHours[i].hasTakeAway;
                                $scope.Items[i].dineTick = $scope.completeUnitObj.operationalHours[i].hasDineIn;
                                $scope.Items[i].codeTick = $scope.completeUnitObj.operationalHours[i].hasDelivery;
                                $scope.Items[i].takeawayTick = $scope.completeUnitObj.operationalHours[i].hasTakeAway;

                                $scope.Items[i].dineInOpeningTime = $scope.completeUnitObj.operationalHours[i].dineInOpeningTime;
                                $scope.Items[i].dineInClosingTime = $scope.completeUnitObj.operationalHours[i].dineInClosingTime;

                                $scope.singleDineInStartTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].dineInOpeningTime
                                    + " GMT+0530 (India Standard Time)";
                                $scope.singleDineInCloseTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].dineInClosingTime
                                    + " GMT+0530 (India Standard Time)";
                                $scope.singleDeliveryStartTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].deliveryOpeningTime
                                    + " GMT+0530 (India Standard Time)";
                                $scope.singleDeliveryCloseTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].deliveryClosingTime
                                    + " GMT+0530 (India Standard Time)";
                                $scope.singleTakeAwayStartTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].takeAwayOpeningTime
                                    + " GMT+0530 (India Standard Time)";
                                $scope.singleTakeAwayCloseTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].takeAwayClosingTime
                                    + " GMT+0530 (India Standard Time)";
                                $scope.singleHandOverTime[i] = "Fri May 13 2016 "
                                    + $scope.completeUnitObj.operationalHours[i].shiftOneHandoverTime
                                    + " GMT+0530 (India Standard Time)";

                                $scope.Items[i].dineInClosingTime = $scope.completeUnitObj.operationalHours[i].dineInClosingTime;
                                $scope.Items[i].deliveryClosingTime = $scope.completeUnitObj.operationalHours[i].deliveryClosingTime;
                                $scope.Items[i].deliveryOpeningTime = $scope.completeUnitObj.operationalHours[i].deliveryOpeningTime;
                                $scope.Items[i].takeAwayOpeningTime = $scope.completeUnitObj.operationalHours[i].takeAwayOpeningTime;
                                $scope.Items[i].takeAwayClosingTime = $scope.completeUnitObj.operationalHours[i].takeAwayClosingTime;
                                $scope.Items[i].shiftOneHandoverTime = $scope.completeUnitObj.operationalHours[i].shiftOneHandoverTime;
                                $scope.Items[i].status = $scope.completeUnitObj.operationalHours[i].status;

                            }
                        }
                    }
                }
                $scope.selectTab('tab8')
            }

            $scope.validateBusinessHours = function () {
                composeCompleteUnitObj();

                if ($scope.unitAction == "edit" && $scope.completeUnitObj.unitManager != null) {
                    var selectedReportingManagerResult = $scope.reportingManagers.filter(function (
                        reportManagerData) {
                        return reportManagerData.name == $scope.completeUnitObj.unitManager.name;
                    });
                    var indexOfReportManager = selectedReportingManagerResult[0];
                    $scope.selectedReportingManager = indexOfReportManager;
                }

                $scope.selectTab('tab9');
            }

            $scope.selectStateUnit = function (state) {

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.location + '?stateId=' + state.id
                }).then(function success(response) {
                    $scope.locationList = response.data;
                    $scope.selectedAddressCity = null;
                    console.log("locationList", $scope.locationList)
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.showReportingManager = function () {
                if ($scope.unitAction == "edit") {
                    console.log($scope.completeUnitObj);
                    $scope.gotoLocationTab();
                }
                $scope.reportingManagerObj = {}
                $http(
                    {
                        method: 'GET',
                        url: AppUtil.restUrls.userManagement.user + '?userId='
                        + $scope.selectedReportingManager.id
                    }).then(function success(response) {
                    $scope.reportingManagerObj = response.data;
                    composeCompleteUnitObj.unitManager = $scope.reportingManagerObj;
                    composeCompleteUnitObj();
                    $scope.selectTab('tab10');
                }, function error(response) {
                    console.log("error:" + response);
                });
            }
            $scope.showLocationUnit = function () {
                console.log("TT=", $scope.selectedLocationName);
                composeCompleteUnitObj.location = $scope.selectedLocationName;
                composeCompleteUnitObj();
                console.log("fullObj=", $scope.completeUnitObj);
                $scope.selectTab('tab11');

            }

            $("#btnAddNewUnitsID").click(function () {
                $("#unitManagemmentModal").modal({
                    backdrop: false
                });
            });

            function composeCompleteUnitObj() {
                var address = angular.copy($scope.address);
                address.state = $scope.selectedAddressState.name;
                address.city = $scope.selectedAddressCity.name;
                if ($scope.unitAction == "add") {
                    $scope.completeUnitObj = {
                        address: address,
                        division: $scope.newUnitDivision,
                        family: $scope.newUnitFamily,
                        name: $scope.newUnitName,
                        referenceName: $scope.referenceUnitName,
                        noOfTakeawayTerminals: parseInt($scope.noOfTakeAway),
                        noOfTerminals: $scope.newUnitTerminals,
                        workstationEnabled: $scope.workstationEnabled.code,
                        freeInternetAccess: $scope.freeInternetAccess.code,
                        tableService: $scope.tableService.code,
                        tableServiceType: $scope.tableServiceType.code,
                        tokenEnabled: $scope.tokenService.code,
                        googleMerchantId:$scope.googleMerchantId,
                        packagingTYpe:$scope.packagingType,
                        packagingValue:$scope.packagingValue,
                        tokenLimit: $scope.maxTokenLimit,
                        subCategory: $scope.selectedSubCategory,
                        unitZone: $scope.zoneList[0],
                        unitBusinessType: $scope.selectedBusinessType,
                        noOfTables: $scope.noOfTables,
                        region: $scope.newUnitRegion,
                        company: $scope.newUnitCompany,
                        startDate: moment().format('YYYY-MM-DD'),
                        status: "IN_ACTIVE",
                        tin: $scope.newUnitTin,
                        cloneUnitId: angular.isUndefined($scope.newUnitProductPricing) ? null
                            : $scope.newUnitProductPricing.id,
                        cloneUnitId: angular.isUndefined($scope.newUnitInventoryClone) ? null
                            : $scope.newUnitInventoryClone.id,
                        unitEmail: $scope.newUnitEmail,
                        channel: $scope.newChannel,
                        deliveryPartners: $scope.addedPartners,
                        operationalHours: $scope.Items,
                        unitManager: $scope.reportingManagerObj,
                        cafeManager: $scope.selectedCafeManager,
                        location: $scope.selectedAddressCity,
                        noOfMeter: $scope.noOfMeter,
                        dGAvailable: $scope.dGAvailable.value,
                        liveInventoryEnabled: $scope.liveInventoryEnabled.value,
                        assemblyStrictMode: $scope.assemblyStrictMode.code,
                        assemblyOtpMode: $scope.assemblyOtpMode.code,
                        isTestingUnit: $scope.isTestingUnit.value
                    };
                } else if ($scope.unitAction == "edit") {
                    $scope.completeUnitObj = {
                        address: address,
                        division: $scope.newUnitDivision,
                        family: $scope.newUnitFamily,
                        id: $scope.unitToedit.id,
                        lastBusinessDate: $scope.unitToedit.lastBusinessDate,
                        name: $scope.newUnitName,
                        referenceName: $scope.referenceUnitName,
                        noOfTakeawayTerminals: parseInt($scope.noOfTakeAway),
                        noOfTerminals: $scope.newUnitTerminals,
                        workstationEnabled: $scope.workstationEnabled.code,
                        freeInternetAccess: $scope.freeInternetAccess.code,
                        tableService: $scope.tableService.code,
                        tableServiceType: $scope.tableServiceType.code,
                        tokenEnabled: $scope.tokenService.code,
                        tokenLimit: $scope.maxTokenLimit,
                        googleMerchantId:$scope.googleMerchantId,
                        packagingType:$scope.packagingType,
                        packagingValue:$scope.packagingValue,
                        subCategory: $scope.selectedSubCategory,
                        noOfTables: $scope.noOfTables,
                        products: $scope.unitToedit.products,
                        startDate: $scope.unitToedit.startDate,
                        status: $scope.unitToedit.status,
                        region: $scope.newUnitRegion,
                        company: $scope.newUnitCompany,
                        tin: $scope.newUnitTin,
                        unitEmail: $scope.newUnitEmail,
                        channel: $scope.newChannel,
                        deliveryPartners: $scope.addedPartners,
                        operationalHours: $scope.Items,
                        unitManager: $scope.reportingManagerObj,
                        cafeManager: $scope.selectedCafeManager,
                        location: $scope.selectedLocationName,
                        noOfMeter: $scope.noOfMeter,
                        dGAvailable: $scope.dGAvailable.value,
                        liveInventoryEnabled: $scope.liveInventoryEnabled.value,
                        assemblyStrictMode: $scope.assemblyStrictMode.value,
                        assemblyOtpMode: $scope.assemblyOtpMode.value,
                        isTestingUnit: $scope.isTestingUnit !== undefined ?  $scope.isTestingUnit : ab.yesNoOption[1]
                    }
                }
            }

            $scope.addNewUnit = function () {
                $rootScope.showFullScreenLoader = true;
                $scope.showfarebut2 = true;
                console.log("completeUnitObj (unitCtrl)=", $scope.completeUnitObj);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.addUnit,
                    data: $scope.completeUnitObj,
                }).then(
                    function success(response) {
                        if (response.status == 200) {
                            if ($scope.familyData == "MONK") {
                                $http(
                                    {
                                        method: 'GET',
                                        url: AppUtil.restUrls.kioskManagement.kioskAssignUnit
                                        + '?unitId=' + response.data + '&locationId='
                                        + $scope.locationIdMonk,
                                    }).then(function success(response) {
                                    console.log("ResultAssigned=", response.data);
                                    $rootScope.showFullScreenLoader = false;
                                    $scope.showfarebut2 = false;
                                    alert("Unit added successfully!");
                                    window.location.reload();
                                }, function error(response) {
                                    console.log("error:" + response);
                                });
                            } else {
                                response.data.deliveryPartners = $scope.completeUnitObj.deliveryPartners;
                                $http({
                                    method: 'POST',
                                    url: AppUtil.restUrls.unitMetaData.addUpdateDeliveryPartners,
                                    data: {
                                        id: response.data,
                                        deliveryPartners: $scope.completeUnitObj.deliveryPartners
                                    },
                                }).then(function success(response) {
                                    if (response.status == 200) {
                                        $rootScope.showFullScreenLoader = false;
                                        $scope.showfarebut2 = false;
                                        console.log(response.data);
                                        alert("Unit added successfully!");
                                        console.log("IDD=", response.data);
                                        window.location.reload();
                                    } else {
                                        $rootScope.showFullScreenLoader = false;
                                        console.log(response);
                                    }
                                }, function error(response) {
                                    console.log("error:" + response);
                                });
                            }
                        } else {
                            console.log(response);
                            $scope.loading = false;
                            $scope.showfarebut2 = false;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
            }

            $scope.$on("editUnit", function (event, args) {
                $scope.unitAction = "edit";
                $scope.unitToedit = args.unitToEdit;
                $scope.unitID = $scope.unitToedit.id;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.viewUnitDeliveryPartners,
                    data: $scope.unitID,
                }).then(function success(response) {
                    if (response.status === 200) {
                        $scope.selectTab('tab1');
                        $scope.address = $scope.unitToedit.address;
                        $scope.newUnitTerminals = $scope.unitToedit.noOfTerminals;
                        $scope.noOfTakeAway = $scope.unitToedit.noOfTakeawayTerminals;
                        $scope.workstationEnabled = {
                            code: $scope.unitToedit.workstationEnabled,
                            name: $scope.unitToedit.workstationEnabled
                        };
                        $scope.freeInternetAccess = {
                            code: $scope.unitToedit.freeInternetAccess,
                            name: $scope.unitToedit.freeInternetAccess
                        };
                        $scope.tableService = {
                            code: $scope.unitToedit.tableService,
                            name: $scope.unitToedit.tableService
                        };
                        $scope.tableServiceType = {
                            code: $scope.unitToedit.tableServiceType,
                            name: $scope.unitToedit.tableServiceType
                        };
                        $scope.tokenService = {
                            code: $scope.unitToedit.tokenEnabled,
                            name: $scope.unitToedit.tokenEnabled
                        };
                        $scope.Items = $scope.unitToedit.operationalHours;
                        $scope.operationalHours = $scope.unitToedit.operationalHours;
                        $scope.reportingManagerObj = $scope.unitToedit.unitManager;
                        $scope.selectedLocationName = $scope.unitToedit.location;
                        $scope.newUnitDivision = $scope.unitToedit.division;
                        $scope.newUnitFamily = $scope.unitToedit.family;
                        $scope.newUnitRegion = $scope.unitToedit.region;
                        $scope.newUnitCompany = $scope.unitToedit.company;
                        $scope.selectedSubCategory = $scope.unitToedit.subCategory;
                        $scope.selectedAddressState = $scope.unitToedit.address.state;
                        $scope.selectedAddressCity = $scope.unitToedit.address.city;
                        $scope.noOfTables = $scope.unitToedit.noOfTables;
                        $scope.newUnitName = $scope.unitToedit.name;
                        $scope.referenceUnitName = $scope.unitToedit.referenceName;
                        $scope.newUnitTin = $scope.unitToedit.tin;
                        $scope.newUnitEmail = $scope.unitToedit.unitEmail;
                        $scope.newChannel = $scope.unitToedit.channel;
                        $scope.newUnitDeliveryPartners = response.data;
                        $scope.noOfMeter = $scope.unitToedit.noOfMeter;
                        $scope.googleMerchantId=$scope.unitToedit.googleMerchantId,
                        $scope.packagingType=$scope.unitToedit.packagingType,
                        $scope.packagingValue=$scope.unitToedit.packagingValue,
                        $scope.maxTokenLimit = $scope.unitToedit.tokenLimit;
                        $scope.dGAvailable = $scope.unitToedit.dGAvailable == true ? $scope.dgOptionList[0] : $scope.dgOptionList[1];
                        $scope.liveInventoryEnabled = $scope.unitToedit.liveInventoryEnabled == true ? $scope.dgOptionList[0] : $scope.dgOptionList[1];
                        $scope.assemblyStrictMode = $scope.unitToedit.assemblyStrictMode == true ? $scope.assemblyStrictModeUnit[1] : $scope.assemblyStrictModeUnit[0];
                        $scope.assemblyOtpMode = $scope.unitToedit.assemblyOtpMode == true ? $scope.assemblyOtpModeUnit[1] : $scope.assemblyOtpModeUnit[0];
                        
                        if ($scope.cafeManagers != null) {
                            $scope.cafeManagers.map(function (manager) {
                                if ($scope.unitToedit.cafeManager != null && manager.id == $scope.unitToedit.cafeManager.id) {
                                    $scope.selectedCafeManager = manager;
                                }
                            })
                        }
                        $scope.editUnitBtn = true;
                        $scope.addUnitBtn = false;
                        $("#unitManagemmentModal").modal("show");
                    } else {
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

            });

            $scope.editExistingUnit = function () {
                composeCompleteUnitObj();
                var flagDeliveryPartners = "";
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.updateUnit,
                    data: $scope.completeUnitObj,
                }).then(function success(response) {
                    if (response.status == 200) {
                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.unitMetaData.addUpdateDeliveryPartners,
                            data: $scope.completeUnitObj,
                        }).then(function success(response) {
                            console.log("live is "+$scope.unitToedit.live);
                            if (response.status == 200) {
                                if($scope.completeUnitObj.family=="CAFE"){
                                    console.log("updating cafe")
                                $http({
                                    method: 'GET',
                                    url: AppUtil.restUrls.location.loadLocation + "/1000" + "/" + $scope.completeUnitObj.id
                                }).then(function success(response) {
                                    if (response.status == 200) {
                                        $rootScope.detailLoaderMessage = "Updating DineIn App ...";
                                        $rootScope.showDetailLoader = true;
                                        $http({
                                            method: 'GET',
                                            url: AppUtil.restUrls.cache.refreshUnit + "1000" + "/" + AppUtil.cafe + "/" + $scope.completeUnitObj.id
                                        }).then(function success(response) {
                                            if (response.status == 200) {
                                                console.log("unit refreshes");
                                                $rootScope.showDetailLoader = false;
                                                window.location.reload();

                                            }
                                        }, function error(response) {

                                            console.log("error:" + response);
                                        });
                                        console.log("unit updated")

                                    }
                                }, function error(response) {
                                    console.log("error:" + response);
                                });
                            }
                                console.log("DeliveryPartners updated");
                                if($scope.completeUnitObj.family!="CAFE" ){
                                    $rootScope.showDetailLoader = false;
                                    window.location.reload();
                                }

                            } else {
                                $rootScope.showFullScreenLoader = false;
                                console.log(response);
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        console.log(response);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }


            $scope.addPriorityPartners = function () {
                if ($scope.deliveryPartners == undefined || $scope.deliveryPartners == "") {
                    alert("Delivery Partners can not be empty");
                    return false;
                }
                $scope.addedPartners = addToPartnerList($scope.addedPartners, $scope.deliveryPartners);
            }

            $http({
                method: 'GET',
                url: AppUtil.restUrls.posMetaData.allDeliveryPartners
            }).then(function success(response) {
                $scope.partnersList = response.data;
                var testarr = [];
                testarr = getPartnerDetails(testarr, $scope.partnersList);
                $scope.refPartnerLists = testarr;
            }, function error(response) {
                console.log("error:" + response);
            });

            function getPartnerDetails(testarr, resPartnerDetails) {
                resPartnerDetails.forEach(function (chkdetailPartner) {
                    if (chkdetailPartner.type === 'EXTERNAL' || chkdetailPartner.status === 'ACTIVE') {
                        testarr.push(chkdetailPartner);
                    }
                });
                return testarr;
            }

            $scope.priorityList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

            function addToPartnerList(delPartLists, item) {
                if (!angular.isUndefined(delPartLists) || delPartLists != null) {
                    var contains = false;
                    delPartLists.forEach(function (partListItem) {
                        if (partListItem.detail.id === item.id) {

                            contains = true;
                        }
                    });
                    if (!contains) {
                        delPartLists.push(transformDeliveryPartnerObj(item));
                    }
                } else {
                    delPartLists = [];
                    delPartLists.push(transformDeliveryPartnerObj(item));
                }
                return delPartLists;
            }

            $scope.removePartner = function (partner) {
                var partnerList = [];
                $scope.addedPartners.forEach(function (v) {
                    if (v.detail.id != partner.detail.id) {
                        partnerList.push(v);
                    }
                });
                $scope.addedPartners = partnerList;
            }

            function transformDeliveryPartnerObj(item, priority) {
                var itemObj = {};
                itemObj.detail = item;
                itemObj.priority = priority;
                return itemObj;
            }

            $scope.validateUnitInventoryCloning = function (cloneID) {

                if (($scope.newUnitInventoryClone == null || angular.isUndefined($scope.newUnitInventoryClone))
                    && $scope.newUnitFamily != "KITCHEN" && $scope.newUnitFamily != "WAREHOUSE"
                    && $scope.newUnitFamily != "OFFICE") {
                    alert("Please select Inventory clone unit");
                    return;
                }
                $scope.selectTab('tab7');
                $scope.addedPartners = undefined;
            }

            function convert(str) {
                var date = new Date(str), mnth = ("0" + (date.getMonth() + 1)).slice(-2), day = ("0" + date
                    .getDate()).slice(-2);
                hours = ("0" + date.getHours()).slice(-2);
                minutes = ("0" + date.getMinutes()).slice(-2);
                seconds = ("0" + date.getSeconds()).slice(-2);
                return [hours, minutes, seconds].join(":");
            }

            var userObj = {
                id: null,
                name: null,
                contact: $scope.posMobileNo,
                email: null,
                loyalityPoints: null,
                contactVerified: false,
                emailVerified: false,
                unitId: null,
                newCustomer: true,
                otp: null,
                chaiRedeemed: 0,
                productId: null
            };

            $scope.noofShift = [{
                code: 1,
                name: 1
            }, {
                code: 2,
                name: 2
            }];
            $scope.selectNoOfShift = $scope.noofShift[0];

            $scope.hstep = 1;
            $scope.mstep = 1;

            $scope.options = {
                hstep: [1, 2, 3],
                mstep: [1, 5, 10, 15, 25, 30]
            };

            $scope.ismeridian = false;
            $scope.toggleMode = function () {
                $scope.ismeridian = !$scope.ismeridian;
            };

            $scope.update = function () {
                var d = new Date();
                d.setHours(14);
                d.setMinutes(0);
                $scope.mytime = d;
            };

            $scope.changed = function () {
                $log.log('Time changed to: ' + $scope.tickDineStartTime);

            };

            $scope.changedDineStartCheck = function () {
                $scope.checkedDine = false
            }
            $scope.changedCodStartCheck = function () {
                $scope.checkedCod = false
            }
            $scope.changedTakeAwayStartCheck = function () {
                $scope.checkedTakeAway = false
            }

            $scope.changedTakeAwayStartCheck = function () {
                $scope.checkedTakeAway = false
            }

            $scope.changedd = function () {
                alert('Time changed to: ' + $scope.singleDineInStartTime);
            }
            $scope.changedDineStartDate = function (DaysNames, val) {
                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].dineInOpeningTime = convert($scope.singleDineInStartTime[val]);
                    }
                }
            }

            $scope.changedDineCloseDate = function (DaysNames, val) {

                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].dineInClosingTime = convert($scope.singleDineInCloseTime[val]);
                    }
                }

            }

            $scope.clear = function () {
                $scope.mytime = null;
            };

            $scope.changedDeliveryStartDate = function (DaysNames, val) {
                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].deliveryOpeningTime = convert($scope.singleDeliveryStartTime[val]);
                    }
                }
            }
            $scope.changedDeliveryCloseDate = function (DaysNames, val) {
                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].deliveryClosingTime = convert($scope.singleDeliveryCloseTime[val]);
                    }
                }
            }

            $scope.changedTakeAwayStartDate = function (DaysNames, val) {
                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].takeAwayOpeningTime = convert($scope.singleTakeAwayStartTime[val]);
                    }
                }
            }
            $scope.changedTakeAwayCloseDate = function (DaysNames, val) {
                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].takeAwayClosingTime = convert($scope.singleTakeAwayCloseTime[val]);
                    }
                }
            }

            $scope.changedHandOverTime = function (DaysNames, val) {
                for (var index in $scope.Items) {
                    if ($scope.Items[index].dayOfTheWeek == DaysNames) {
                        $scope.Items[index].shiftOneHandoverTime = convert($scope.singleHandOverTime[val]);
                    }
                }
            }

            $scope.Items = [{
                dayOfTheWeekNumber: "1",
                dayOfTheWeek: "Sunday",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: false,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }, {
                dayOfTheWeekNumber: "2",
                dayOfTheWeek: "Monday",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: false,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }, {
                dayOfTheWeekNumber: "3",
                dayOfTheWeek: "Tuesday",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: false,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }, {
                dayOfTheWeekNumber: "4",
                dayOfTheWeek: "Wednesday",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: false,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }, {
                dayOfTheWeekNumber: "5",
                dayOfTheWeek: "Thursday",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: false,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }, {
                dayOfTheWeekNumber: "6",
                dayOfTheWeek: "Friday",
                noOfShifts: "1",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: true,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }, {
                dayOfTheWeekNumber: "7",
                dayOfTheWeek: "Saturday",
                noOfShifts: "1",
                isOperational: false,
                hasDelivery: false,
                hasDineIn: false,
                hasTakeAway: false,
                dineInOpeningTime: "00:00:00",
                dineInClosingTime: "00:00:00",
                deliveryOpeningTime: "00:00:00",
                deliveryClosingTime: "00:00:00",
                takeAwayOpeningTime: null,
                takeAwayClosingTime: null,
                shiftOneHandoverTime: null,
                shiftTwoHandoverTime: null,
                status: "ACTIVE"
            }];

            $scope.checkAllDays = function () {
                if ($scope.checkedDays) {
                    angular.forEach($scope.Items, function (item) {
                        item.daysTick = $scope.checkedDays;
                        item.isOperational = true;
                    });
                } else {
                    angular.forEach($scope.Items, function (item) {
                        item.daysTick = $scope.checkedDays;
                        item.isOperational = false;
                    });
                }
            };
            $scope.checkAllDineIn = function (chk) {

                // alert(chk);
                var tickDineStartTime1 = convert($scope.tickDineStartTime);
                var tickDineCloseTime1 = convert($scope.tickDineCloseTime);
                if (chk == true) {

                    for (i in $scope.Items) {
                        $scope.singleDineInStartTime[i] = $scope.tickDineStartTime;
                        $scope.singleDineInCloseTime[i] = $scope.tickDineCloseTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].hasDineIn = $scope.checkedDine;
                        $scope.Items[i].dineTick = true;
                        $scope.Items[i].daysTick = true;
                        $scope.Items[i].isOperational = true;
                        $scope.Items[i].dineInOpeningTime = tickDineStartTime1;
                        $scope.Items[i].dineInClosingTime = tickDineCloseTime1;
                    }
                } else {
                    for (i in $scope.Items) {
                        $scope.singleDineInStartTime[i] = $scope.tickDineStartTime;
                        $scope.singleDineInCloseTime[i] = $scope.tickDineCloseTime;
                        $scope.Items[i].dineTick = false;
                        $scope.Items[i].daysTick = false;
                        $scope.Items[i].isOperational = false;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].dineInOpeningTime = tickDineStartTime1;
                        $scope.Items[i].dineInClosingTime = tickDineCloseTime1;
                    }
                }
            };

            $scope.checkAllCodIn = function () {
                var tickDeliveryStartTimes = convert($scope.tickCODStartTime);
                var tickDeliveryCloseTimes = convert($scope.tickCODCloseTime);
                if ($scope.checkedCod) {
                    for (i in $scope.Items) {
                        $scope.singleDeliveryStartTime[i] = $scope.tickCODStartTime;
                        $scope.singleDeliveryCloseTime[i] = $scope.tickCODCloseTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].hasDelivery = true
                        $scope.Items[i].codeTick = true;
                        $scope.Items[i].daysTick = true;
                        $scope.Items[i].isOperational = true;
                        $scope.Items[i].deliveryOpeningTime = tickDeliveryStartTimes;
                        $scope.Items[i].deliveryClosingTime = tickDeliveryCloseTimes;
                    }
                } else {
                    for (i in $scope.Items) {
                        $scope.singleDeliveryStartTime[i] = $scope.tickCODStartTime;
                        $scope.singleDeliveryCloseTime[i] = $scope.tickCODCloseTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].hasDelivery = false
                        $scope.Items[i].codeTick = false;
                        $scope.Items[i].daysTick = false;
                        $scope.Items[i].isOperational = false;
                        $scope.Items[i].deliveryOpeningTime = tickDeliveryStartTimes;
                        $scope.Items[i].deliveryClosingTime = tickDeliveryCloseTimes;
                    }
                }
            };

            $scope.checkTakeAwayIn = function (chkTakeAway) {
                var tickTakeAwayStartTimes = convert($scope.tickTakeAwayStartTime);
                var tickTakeAwayCloseTimes = convert($scope.tickTakeAwayCloseTime);
                if (chkTakeAway == true) {
                    for (i in $scope.Items) {
                        $scope.singleTakeAwayStartTime[i] = $scope.tickTakeAwayStartTime;
                        $scope.singleTakeAwayCloseTime[i] = $scope.tickTakeAwayCloseTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].hasTakeAway = true
                        $scope.Items[i].takeawayTick = true;
                        $scope.Items[i].daysTick = true;
                        $scope.Items[i].isOperational = true;
                        $scope.Items[i].takeAwayOpeningTime = tickTakeAwayStartTimes;
                        $scope.Items[i].takeAwayClosingTime = tickTakeAwayCloseTimes;
                    }
                } else {
                    for (i in $scope.Items) {
                        $scope.singleTakeAwayStartTime[i] = $scope.tickTakeAwayStartTime;
                        $scope.singleTakeAwayCloseTime[i] = $scope.tickTakeAwayCloseTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].hasTakeAway = false
                        $scope.Items[i].takeawayTick = false;
                        $scope.Items[i].daysTick = false;
                        $scope.Items[i].isOperational = false;
                        $scope.Items[i].takeAwayOpeningTime = tickTakeAwayStartTimes;
                        $scope.Items[i].takeAwayClosingTime = tickTakeAwayCloseTimes;
                    }
                }
            };
            $scope.checkAllHandOverIn = function (chkHandOver) {
                var tickHandOverStartTimes = convert($scope.tickHandOverTime);
                if (chkHandOver == true) {
                    for (i in $scope.Items) {
                        $scope.singleHandOverTime[i] = $scope.tickHandOverTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].handoverTick = true;
                        $scope.Items[i].daysTick = true;
                        $scope.Items[i].isOperational = true;
                        $scope.Items[i].shiftOneHandoverTime = tickHandOverStartTimes
                    }
                } else {
                    for (i in $scope.Items) {
                        $scope.singleTakeAwayStartTime[i] = $scope.tickTakeAwayStartTime;
                        $scope.singleTakeAwayCloseTime[i] = $scope.tickTakeAwayCloseTime;
                        $scope.Items[i].noOfShifts = $scope.selectNoOfShift.code;
                        $scope.Items[i].handoverTick = false
                        $scope.Items[i].daysTick = false;
                        $scope.Items[i].isOperational = false;
                        $scope.Items[i].shiftOneHandoverTime = tickHandOverStartTimes;
                    }
                }
            };
            $scope.checkDays = function (daysName, daysList) {
                if (daysList == true) {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == daysName) {
                            {
                                $scope.Items[index].isOperational = true;
                            }
                        }
                    }
                } else {
                    if (daysList == false) {
                        for (var index in $scope.Items) {
                            if ($scope.Items[index].dayOfTheWeek == daysName) {
                                $scope.Items[index].isOperational = false;
                            }
                        }
                    }
                }
            };
            $scope.checkDineDetails = function (dineDaysName, dineDaysList, values) {
                if (dineDaysList == true) {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == dineDaysName) {
                            $scope.Items[index].hasDineIn = true;
                        }
                    }
                } else {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == dineDaysName) {
                            $scope.Items[index].hasDineIn = false;
                        }
                    }
                }
            };
            $scope.checkDeliveryDetails = function (deliveryDaysName, deliveryDaysList) {

                if (deliveryDaysList == true) {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == deliveryDaysName) {
                            $scope.Items[index].hasDelivery = true;
                        }
                    }
                } else {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == deliveryDaysName) {
                            $scope.Items[index].hasDelivery = false;
                        }
                    }
                }
            };

            $scope.checkTakeAwayDetails = function (takeAwayDaysName, takeAwayDaysList) {

                if (takeAwayDaysList == true) {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == takeAwayDaysName) {
                            $scope.Items[index].hasTakeAway = true;
                        }
                    }
                } else {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == takeAwayDaysName) {
                            $scope.Items[index].hasTakeAway = false;
                        }
                    }
                }

            };
            $scope.checkHandOverDetails = function (HandOverName, handOverDaysList) {
                if (HandOverName == true) {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == HandOverName) {
                            $scope.Items[index].shiftOneHandoverTime = $scope.singleHandOverTime;
                        }
                    }
                } else {
                    for (var index in $scope.Items) {
                        if ($scope.Items[index].dayOfTheWeek == HandOverName) {
                            $scope.Items[index].shiftOneHandoverTime = $scope.singleHandOverTime;
                        }
                    }
                }
            };

            $scope.showFamily = function (familyName) {
                $scope.familyData = "";

                if (familyName == 'CHAI_MONK') {

                    $scope.familyData = "MONK";
                    $("#idChaiMonkDiv").show();
                    $scope.disabledData = "ng-disabled='true'";

                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.kioskManagement.kioskCompany
                    }).then(function success(response) {
                        $scope.kiosCompanyList = response.data;
                        console.log("company=", $scope.kiosCompanyList);

                        console.log(JSON.stringify($scope.kiosCompanyList));

                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $("#idChaiMonkDiv").hide();
                }
            };

            $scope.selectOfficeList = function (selectedCompany) {
                if (selectedCompany != undefined) {
                    console.log("companyData=", selectedCompany);
                    $scope.companyNameMonk = selectedCompany.companyName;
                    console.log(JSON.stringify(selectedCompany.officeList));
                    $scope.kioskOfficeList = selectedCompany.officeList;

                }
            };

            $scope.selectLocationList = function (selectedOffice) {

                $scope.officeShortCodeData = selectedOffice.officeShortCode;
                $scope.officeRegionData = selectedOffice.region;
                $scope.newUnitTin = selectedOffice.tin;
                $scope.noOfTables = 0;
                $scope.newUnitTerminals = 0;
                $scope.noOfTakeAway = 1;
                $scope.address.line1 = selectedOffice.officeAddress.line1;
                $scope.address.line2 = selectedOffice.officeAddress.line2;
                $scope.address.line3 = selectedOffice.officeAddress.line3;
                $scope.address.city = selectedOffice.officeAddress.city;

                $scope.address.state = selectedOffice.officeAddress.state;
                $scope.address.zipCode = selectedOffice.officeAddress.zipCode;
                $scope.address.contact1 = selectedOffice.officeAddress.contact1;
                $scope.address.contact2 = selectedOffice.officeAddress.contact2;
                $scope.address.latitude = selectedOffice.officeAddress.latitude;
                $scope.address.longitude = selectedOffice.officeAddress.longitude;

                if (selectedOffice != undefined) {
                    $scope.kioskLocationList = selectedOffice.locationList;
                    $scope.LocationShortCodeData = selectedOffice.locationList.officeShortCode;
                    $scope.monkDisabled = function (e) {
                        return true;
                    };
                }
            };

            $scope.setCompanyData = function (data) {
                $scope.newUnitCompany = data;
            }; //What is the need of it?

            $scope.makeKioskLocation = function (selectedLocationListt) {
                $scope.locationIdMonk = selectedLocationListt.locationId;
                $scope.newUnitName = $scope.companyNameMonk + "-" + $scope.officeShortCodeData + "-"
                    + selectedLocationListt.locationShortCode;
                $scope.referenceUnitName = $scope.newUnitName;
                var selectedRegionList = $scope.regions.filter(function (regionData) {
                    return regionData == $scope.officeRegionData;
                });
                var indexOfRegion = $scope.regions.indexOf(selectedRegionList[0]);
                $scope.newUnitRegion = $scope.regions[indexOfRegion];

                var selectedSubCatList = $scope.subCategories.filter(function (subCategoryData) {
                    return subCategoryData == "TG";
                });
                var indexOfSubCat = $scope.subCategories.indexOf(selectedSubCatList[0]);
                $scope.selectedSubCategory = $scope.subCategories[indexOfSubCat];

                var selectedWorkStationListKios = $scope.workstationUnit.filter(function (
                    workStationKios) {
                    return workStationKios.name == false;
                });

                var indexOfWorkStationListData = $scope.workstationUnit
                    .indexOf(selectedWorkStationListKios[0]);
                $scope.workstationEnabled = $scope.workstationUnit[indexOfWorkStationListData];

                var selectedFreeInternetAccessUnitKios = $scope.freeInternetAccessUnit.filter(function (
                    freeInternetUnitData) {
                    return freeInternetUnitData.name == false;
                });
                var indexOfFreeInternetAccessData = $scope.freeInternetAccessUnit
                    .indexOf(selectedFreeInternetAccessUnitKios[0]);
                $scope.freeInternetAccess = $scope.freeInternetAccessUnit[indexOfFreeInternetAccessData];

                var selectedDivisionKiosUnit = $scope.divisions.filter(function (divisionKiosUnits) {
                    return divisionKiosUnits.name == "Chaayos Takeaway";
                });
                var indexOfDivisionData = $scope.divisions.indexOf(selectedDivisionKiosUnit[0]);
                $scope.newUnitDivision = $scope.divisions[indexOfDivisionData];

                var selectedAssemblyStricMode = $scope.assemblyStrictModeUnit.filter(function (
                    assemblyStrictModeData) {
                    return assemblyStrictModeData.name == false;
                });
                var indexOfAssemblyStrictMode = $scope.assemblyStrictModeUnit
                    .indexOf(selectedAssemblyStricMode[0]);
                $scope.assemblyStrictMode = $scope.assemblyStrictModeUnit[indexOfAssemblyStrictMode];


                
                var selectedAssemblyOtpMode = $scope.assemblyOtpModeUnit.filter(function (
                    assemblyOtpModeData) {
                    return assemblyOtpModeData.name == false;
                });
                var indexOfAssemblyOtpMode = $scope.assemblyOtpModeUnit
                    .indexOf(selectedAssemblyOtpMode[0]);
                $scope.assemblyOtpMode = $scope.assemblyOtpModeUnit[indexOfAssemblyOtpMode];
            
            };

            $scope.showCountryView = function (country, callBack) {
                console.log(country);
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.state + '?countryId=' + country.id
                }).then(function success(response) {
                    $scope.stateList = response.data;
                    $scope.selectedAddressState = null;
                    $scope.selectedAddressCity = null;
                    if (callBack != undefined && callBack != null) {
                        callBack();
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };
            $scope.showStateList = function (lid, callBack) {
                console.log(lid);

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.location + '?stateId=' + lid
                }).then(function success(response) {
                    $scope.locationList = response.data;
                    if (callBack != undefined && callBack != null) {
                        callBack();
                    }
                    console.log($scope.locationList);
                }, function error(response) {
                    console.log("error:" + response);
                });

            };

        }
    );

adminapp.directive('myMaxlength', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModelCtrl) {
            var maxlength = Number(attrs.myMaxlength);

            function fromUser(text) {
                if (text.length > maxlength) {
                    var transformedInput = text.substring(0, maxlength);
                    ngModelCtrl.$setViewValue(transformedInput);
                    ngModelCtrl.$render();
                    return transformedInput;
                }
                return text;
            }

            ngModelCtrl.$parsers.push(fromUser);
        }
    };
});
