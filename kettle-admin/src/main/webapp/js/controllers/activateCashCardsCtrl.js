/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller(
    "activateCashCardsCtrl",
    function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

        $scope.unitSubCategory = [];
        $scope.toggleCheckAll=false;
        $scope.toggleCheckAllOffers=true;

        $scope.init = function () {
            $scope.byCard = true;
            $scope.code = null;
            $scope.offers = null;
            $scope.editMode = false;
            $scope.startDate = null;
            $scope.endDate = null;
           // $scope.directGiftCardPurchaseStatus = null;
            //$scope.getDirectGiftCardPurchaseStatus();
            //$scope.cashCardOffers();
            //$scope.cashGetUnits();
            $scope.channelPartners = [
                {id: 1, name: "Kettle"},
                /*{id:9, name:"Chaayos(Take away)"},*/
                {id: 14, name: "Chaayos Web"},
                {id: 21, name: "Dine in app"},
            ];
            getRegionDetails();
            $scope.getUnitList();
            $scope.storeSelectedPartner=[];
            for(var index in $scope.channelPartners){
                $scope.storeSelectedPartner.push($scope.channelPartners[index].name);
            }
            $scope.selectedPartner=[];
            $scope.denominationList=[50,100,500,1000,2000,5000];
            $scope.denomination=[];
            $scope.status=["IN_ACTIVE","ACTIVE"];
            $scope.storeStatus=[];
            $scope.walletType=null;
            $scope.Partnerdata=[];
            $scope.offerStatus=null;
            $scope.selectOffer =[];
            $scope.data = [];
            $scope.selectedMap = {};
        };

        // $scope.getDirectGiftCardPurchaseStatus= function (){
        //     $http({
        //         method: 'GET',
        //         url: AppUtil.restUrls.userManagement.directGiftCardPurchaseStatus,
        //         params:{reference: "DIRECT_PURCHASE_OF_GIFT_CARD"}
        //     }).then(function success(response) {
        //         if (response !=undefined && response !== null && !AppUtil.isEmptyObject(response)) {
        //             $scope.directGiftCardPurchaseStatus = response == "Y"?"ACTIVE":"IN_ACTIVE";
        //         }
        //     }, function error(response) {
        //         console.log("error:" + response);
        //     });
        // }

        $scope.getCardDetail = function () {
            var url = $scope.byCard ? AppUtil.restUrls.cashCardManagement.getCardByCode :
                AppUtil.restUrls.cashCardManagement.getCardBySerial;
            $http({
                method: 'POST',
                url: url,
                data: $scope.code
            }).then(function success(response) {
                if (response.data !== null && !AppUtil.isEmptyObject(response.data)) {
                    $scope.cardDetail = response.data;
                    $scope.activationReason = null;
                } else {
                    $scope.noResultMsg = true;
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.activateCard = function (activationReason) {
            if (activationReason != null && activationReason.trim().length > 0) {
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.cashCardManagement.activateCard,
                    data: {
                        cardNumber: $scope.cardDetail.cardNumber,
                        cardSerial: $scope.cardDetail.cardSerial,
                        reason: activationReason,
                        requestingUnit: AppUtil.getCurrentUser().unitId,
                        requestingEmployee: AppUtil.getCurrentUser().id
                    }
                }).then(function success(response) {
                    if (response.data === true) {
                        alert("Card activated successfully.");
                        $scope.getCardDetail();
                    } else {
                        alert("Error activating card.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            } else {
                alert("Please provide reason.");
            }
        };

        $scope.cashCardOffers = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.cashCardManagement.offers,
            }).then(function success(response) {
                if (!AppUtil.isEmptyObject(response.data)) {
                    $scope.offers = response.data.filter(function (o) {
                        return (o.percentage >= 0 || o.suggestWalletPercentage >=0);
                    });
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.getOffersForDate = function () {
            if ($scope.startDate == null || $scope.endDate == null) {
                bootbox.alert("Start date and end date are mandatory.");
                return;
            }
            console.log($scope.Partnerdata);
            $scope.selectedPartner=[];
            for(var index in  $scope.Partnerdata){
                console.log($scope.Partnerdata[index]);
                 var id=$scope.getPartner($scope.Partnerdata[index])
                if($scope.selectedPartner.indexOf(id)==-1){
                    $scope.selectedPartner.push(id);
                }
            }
            if ($scope.selectedPartner.length == 0) {
                bootbox.alert("Please select partner.");
                return;
            }
            console.log($scope.selectedPartner);
            $http({
                method: 'POST',
                url: AppUtil.restUrls.cashCardManagement.offersForDate + "?startDate=" + $scope.startDate + "&endDate=" + $scope.endDate,
                data:$scope.selectedPartner,
            }).then(function success(response) {
                if (!AppUtil.isEmptyObject(response.data)) {
                    if ($scope.unitList == null || $scope.unitList.length === 0) {
                        $scope.getUnitList();
                    }
                    $scope.offers = response.data.filter(function (o) {
                        return (o.percentage >= 0 || o.suggestWalletPercentage >=0);
                    });
                    $scope.newOffer=$scope.offers;
                    for(var i  in $scope.newOffer){
                        $scope.selectOffer[$scope.newOffer[i].cashCardOfferId]=false;
                    }
                } else {
                    bootbox.alert("No active offers found for selected criteria.")
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };
        $scope.addStatusRelevantOffers= function(){
            var dummyOffers=[];
            for(var i in $scope.offers){
                if($scope.offers[i].offerStatus==$scope.offerStatus){
                    dummyOffers.push($scope.offers[i]);
                }
            }
            $scope.newOffer=dummyOffers;
        }
        $scope.changeSelectedOfferStatus=function(){
                var payload = [];
                for(var index in $scope.newOffer){
                    if($scope.newOffer[index].offerStatus===$scope.offerStatus && $scope.newOffer[index].selected){
                        payload.push($scope.newOffer[index]);
                    }
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.cashCardManagement.changeStatus,
                    data: payload
                }).then(function success(response) {
                    $scope.newOffer=$scope.offers;
                    $scope.init();
                    console.log($scope.newOffer);
                    bootbox.alert("Successfully");
                }, function error(response) {
                    console.log("error:" + response);
                });

        }

        $scope.changeOfferStatus = function (offer) {
            if (!AppUtil.isEmptyObject(offer)) {
                var payload = [];
                payload.push(offer);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.cashCardManagement.changeStatus,
                    data: payload
                }).then(function success(response) {
                    $scope.cashCardOffers();
                    $scope.newOffer=$scope.offers;
                }, function error(response) {
                    console.log("error:" + response);
                });
            }
        };

        $scope.toggelEditMode = function () {
            $scope.editMode = !$scope.editMode;
            if ($scope.editMode == true) {
                $scope.Partnerdata=[];
                $scope.walletType=null;
                $scope.getUnitList();
            } else {
                $scope.selectedPartner = null;
                //$scope.selectedWalletType = null;
                $scope.offers=null;
                $scope.newOffer=null;
                $scope.storeStatus=[];
                $scope.denomination=[];
                $scope.Partnerdata=[];
                $scope.walletType=null;
            }
            filterAllSubCategory();
        };


        $scope.getUnitList = function () {
            $scope.unitList = [];
            AppUtil.getUnitList(function (list) {
                $scope.unitList = list;
                $scope.unitMap = {};
                $scope.unitList.map(function (unit) {
                    unit.selected = false;
                    $scope.unitMap[unit.id] = unit;
                });
            });
            console.log($scope.unitList);
            filterAllSubCategory();
        };

        $scope.addCashCardOffers = function () {
            if ($scope.startDate == null || $scope.endDate == null) {
                bootbox.alert("Please select start date and end date.");
            } else if ($scope.percentage == null) {
                bootbox.alert("Please select offer percentage.");
            } else if ($scope.cashcard50 == null && $scope.cashcard100 == null && $scope.cashcard500 == null && $scope.cashcard1000 == null && $scope.cashcard2000 == null && $scope.cashcard5000 == null) {
                bootbox.alert("Please select card denominations.");
            }else if($scope.Partnerdata.length==0){
                bootbox.alert("Please select Partner .");
            }else if($scope.walletType == null){
                bootbox.alert("Please select wallet type .");
            } else {
                var selectedUnits = [];
                $scope.unitList.map(function (unit) {
                    if (unit.selected) {
                        selectedUnits.push(unit);
                    }
                });
                if (selectedUnits.length == 0) {
                    bootbox.alert("Please select at least one unit.");
                    return;
                }
                $scope.selectedPartner=[];
                for(var index in  $scope.Partnerdata){
                    console.log($scope.Partnerdata[index]);
                   $scope.selectedPartner.push($scope.getPartner($scope.Partnerdata[index]));
                }
                console.log(selectedUnits);
                var req = [];
                if ($scope.cashcard50 != null) {
                    createRequestObj(50, selectedUnits, req)
                }
                if ($scope.cashcard100 != null) {
                    createRequestObj(100, selectedUnits, req)
                }
                if ($scope.cashcard500 != null) {
                    createRequestObj(500, selectedUnits, req)
                }
                if ($scope.cashcard1000 != null) {
                    createRequestObj(1000, selectedUnits, req)
                }
                if ($scope.cashcard2000 != null) {
                    createRequestObj(2000, selectedUnits, req)
                }
                if ($scope.cashcard5000 != null) {
                    createRequestObj(5000, selectedUnits, req)
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.cashCardManagement.addList,
                    data: req
                }).then(function success(response) {
                    if (response != null && response.data != null && Array.isArray(response.data) ) {
                        //console.log(response);
                        response.data.length > 0 ? bootbox.alert("Cash card offers added successfully.") : bootbox.alert("Cash card offer Updated successfully.");
                        $scope.startDate = null;
                        $scope.endDate = null;
                        $scope.percentage = null;
                        $scope.cashcard50 = null;
                        $scope.cashcard100 = null;
                        $scope.cashcard500 = null;
                        $scope.cashcard1000 = null;
                        $scope.cashcard2000 = null;
                        $scope.cashcard5000 = null;
                        $scope.toggelEditMode();
                    } else {
                        if (response != null && response.data != null && response.data.errorMessage != null) {
                            bootbox.alert(response.data.errorMessage);
                        } else {
                            bootbox.alert("Error creating cash card offer. Please try again!");
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    bootbox.alert("Error creating cash card offer. Please try again!");
                });
            }

        };

        var createRequestObj = function (denomination, selectedUnits, req) {
            selectedUnits.map(function (unit) {
                for( var index in $scope.selectedPartner) {
                        var eGiftCardPercentage=$scope.walletType =="GIFT_CARD_WALLET"?$scope.percentage:null;
                        var suggestGiftPercentage = $scope.walletType =="SUGGEST_WALLET"?$scope.percentage:null;
                        req.push({
                            description: $scope.walletType =="GIFT_CARD_WALLET" ? $scope.percentage + "% EXTRA":null,
                            suggestWalletDescription: $scope.walletType =="SUGGEST_WALLET" ? $scope.percentage + "% EXTRA":null,
                            offerStatus: "ACTIVE",
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            createdBy: AppUtil.getCurrentUser().name + " [" + AppUtil.getCurrentUser().id + "]",
                            creationTime: AppUtil.formatDate(new Date(), 'yyyy-MM-dd'),
                            unitId: unit.id,
                            denomination: denomination,
                            percentage: eGiftCardPercentage,
                            suggestWalletPercentage: suggestGiftPercentage,
                            walletType:$scope.walletType,
                            partnerId: $scope.selectedPartner[index]
                        });
                }
            });
        };

        $scope.findPartnerById = function (id) {
            return $scope.channelPartners.filter(function (partner) {
                return partner.id === id;
            })[0];
        }

        function getRegionDetails() {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.regions
            }).then(function success(response) {
                $scope.regions = response.data;
                $scope.filterRegion($scope.regions);
                $scope.regions.push('ALL');
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        $scope.trimmedRegions = [];
        $scope.filterRegion = function (regionAll) {
            $scope.trimmedRegions = regionAll.filter(function (region) {
                return region != 'ALL';
            });
        };


        $scope.getUnitForRegion = function (region) {
            $scope.regionWiseList = [];
            if (region === 'ALL') {
                $scope.regionWiseList = $scope.regionWiseList.concat($scope.unitList);
            } else {
                for (var index in $scope.unitList) {
                    if ($scope.unitList[index].region === region) {
                        //var a=$scope.unitList[index];
                        $scope.regionWiseList.push($scope.unitList[index]);
                    }
                }
            }
            console.log("out");
        }
        $scope.storeSelectedRegion = [];
        $scope.storeSubCategory = [];

        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };


        $scope.getAllUnitProductMapping = function () {
            $scope.regionWiseList = [];
            if ($scope.storeSelectedRegion.length == 0) {
                alert("Please select region")
                return;
            }
            for (var index in $scope.unitList) {
                if ($scope.storeSelectedRegion.indexOf($scope.unitList[index].region) >= 0) {
                    if ($scope.storeSubCategory.length != 0) {
                        if ($scope.storeSubCategory.indexOf($scope.unitList[index].subCategory) >= 0) {
                            $scope.regionWiseList.push($scope.unitList[index]);
                            console.log($scope.unitList[index]);
                        }
                    }else {
                        $scope.regionWiseList.push($scope.unitList[index]);
                        console.log($scope.unitList[index]);
                    }
                }
            }
        };

        function filterAllSubCategory() {
            $scope.unitSubCategory = [];
            console.log($scope.unitList);
            for (var index in $scope.unitList) {
                if ($scope.unitSubCategory.indexOf($scope.unitList[index].subCategory) == '-1') {
                    $scope.unitSubCategory.push($scope.unitList[index].subCategory)
                }
            }
        }

        $scope.checkAll = function (){
            $scope.toggleCheckAll=!$scope.toggleCheckAll;
            for(var index in $scope.regionWiseList ){
                $scope.regionWiseList[index].selected= $scope.toggleCheckAll;
            }
        };
        $scope.checkAllOffers = function () {
            for (var index in $scope.newOffer) {
                $scope.newOffer[index].selected = $scope.toggleCheckAllOffers;
                $scope.selectOffer[$scope.newOffer[index].cashCardOfferId] = $scope.newOffer[index].selected;
            }
            // $scope.toggleCheckAllOffers=!$scope.toggleCheckAllOffers;
            // $scope.selectOffer=!$scope.toggleCheckAllOffers;
        };

        $scope.testCheckbox = function (index, offer) {
            $scope.newOffer[index].selected = !$scope.newOffer[index].selected;
            $scope.selectOffer[offer.cashCardOfferId] = $scope.newOffer[index].selected;
            console.log("$scope.newOffer[index].selected", $scope.newOffer[index].selected);
            if ($scope.newOffer[index].selected) {
                if ($scope.selectedMap[offer.denomination] === undefined) {
                    $scope.selectedMap[offer.denomination] = [];
                }
                $scope.selectedMap[offer.denomination].push($scope.newOffer[index]);
            } else {
                if ($scope.selectedMap[offer.denomination] !== undefined) {
                    $scope.selectedMap[offer.denomination].delete($scope.newOffer[index]);
                }
            }
        };

        $scope.applyFilter = function () {
            $scope.newOffer = [];
            $scope.storeStatus=[];
            if ($scope.denomination.length > 0) {
                for (var i in $scope.denomination) {
                    for (var j in $scope.offers) {
                        if ($scope.offers[j].denomination === $scope.denomination[i]) {
                            //console.log($scope.offers[j]);
                            $scope.newOffer.push($scope.offers[j]);
                        }
                    }
                }
                console.log($scope.newOffer.length);
            } else {
                $scope.newOffer = $scope.offers;
            }
        }

        $scope.deactivateAll = function () {
            // var data=[];
            $scope.data = [];
            if (Object.keys($scope.selectedMap).length === 0) {
                for (var i in $scope.newOffer) {
                    if ($scope.newOffer[i].offerStatus == "ACTIVE" && $scope.newOffer[i].selected) {
                        $scope.data.push($scope.newOffer[i]);
                    }
                }
            } else {
                for (var i in $scope.selectedMap) {
                    var value = Object.values($scope.selectedMap[i]);
                    for (var index in value) {
                        if (value[index].selected) {
                            $scope.data.push(value[index]);
                        }
                    }
                }
            }
            $scope.changeOffer($scope.data);
        }

        $scope.ActivateAll = function () {
            // var data=[];
            $scope.data = [];
            if (Object.keys($scope.selectedMap).length === 0) {
                for (var i in $scope.newOffer) {
                    if ($scope.newOffer[i].offerStatus == "IN_ACTIVE" && $scope.newOffer[i].selected) {
                        $scope.data.push($scope.newOffer[i]);
                    }
                }
                console.log($scope.data.length);
            } else {
                for (var i in $scope.selectedMap) {
                    var value = Object.values($scope.selectedMap[i]);
                    for (var index in value) {
                        if (value[index].selected) {
                            $scope.data.push(value[index]);
                        }
                    }
                }
            }
            $scope.changeOffer($scope.data);
        }

        $scope.changeOffer =function (data){
            $http({
                method: 'POST',
                url: AppUtil.restUrls.cashCardManagement.changeStatus,
                data: data,
            }).then(function success(response) {
                bootbox.alert("Successfully");
                $scope.init();
            }, function error(response) {
                console.log("error:" + response);
                bootbox.alert("Error Changing Status of  cash card offer. Please try again!");
            });
        }

        // $scope.setGiftCardWalletStatus=function (flag){
        //     flag ? $scope.giftCardWalletPurchase("Y"):$scope.giftCardWalletPurchase("N");
        // }

        $scope.giftCardWalletPurchase= function (status){
            var obj={};
            if(status !=undefined && AppUtil.getCurrentUser().id != undefined){
                obj["referenceValue"] = status;
                obj["updatedBy"] = AppUtil.getCurrentUser().id;
            }
            $http({
                method: 'POST',
                url: AppUtil.restUrls.unitMetaData.setSuggestWalletStatus,
                data: obj
            }).then(function success(response) {
                if(response != undefined && response.data!=undefined && response.data==true){
                    bootbox.alert("Successfully");
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.masterCacheManagement.refreshReferenceMetadataCache,
                    }).then(function success(response){
                        //$scope.directGiftCardPurchaseStatus = status=="Y"?"ACTIVE":"IN_ACTIVE";
                    },function error(response){
                        console.log("error:" + response);
                        bootbox.alert("Error Refreshing Cache.");
                    });
                }else {
                    bootbox.alert("Error Changing Status. Please try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
                bootbox.alert("Error Changing Status. Please try again!");
            });
        }

        $scope.applyFilterStatus = function () {
            var data = [];
            for (var i in $scope.storeStatus) {
                for (var index in $scope.offers) {
                    if ($scope.offers[index].offerStatus == $scope.storeStatus[i]) {
                        if ($scope.denomination.length > 0) {
                            if ($scope.denomination.indexOf($scope.offers[index].denomination) >= 0) {
                                data.push($scope.offers[index]);
                            }
                        } else {
                            data.push($scope.offers[index]);
                        }
                    }
                }
            }
            $scope.newOffer = data;
            console.log($scope.newOffer);
        }

        $scope.getPartner = function (name) {
            for (var index in $scope.channelPartners) {
                if ($scope.channelPartners[index].name == name) {
                    return $scope.channelPartners[index].id;
                }
            }
        }

        $scope.selectStatus= function(offerStatus){
            if($scope.offerStatus==null || $scope.offerStatus==""){

                $scope.offerStatus=offerStatus;
                $scope.addStatusRelevantOffers();
            }
           else if((offerStatus!=$scope.offerStatus )){
                $scope.offerStatus=offerStatus;
                $scope.addStatusRelevantOffers();
            }


        }
    });
