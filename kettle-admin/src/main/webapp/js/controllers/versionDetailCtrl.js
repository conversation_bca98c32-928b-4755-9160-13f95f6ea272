adminapp.controller("versionDetailCtrl",['$scope', 'AppUtil', '$http', '$stateParams', 'fileService', '$cookieStore', '$rootScope',
    function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

    $scope.init = function () {
        $scope.allActiveVersion=[];
        $scope.showTable = false;
        $scope.applicationName=["POS","CAFE_APP","TABLE_SERVICE","MONK_APP","KETTLE_ATTENDANCE"]
        $scope.selectApplicationName=""
        $scope.versionList = []
        $scope.activeVersionList = []
        $scope.result = null;
        $scope.finalApplication = null
        $scope.updateStatus = false
        $scope.versionToAdd = null;
        $scope.selectedReleasedType = "MAJOR"
        $scope.releaseType = ["MAJOR","MINOR","PATCH"]
        $scope.enterVersion = false
        $scope.showVersionButton = true
        $scope.versionToShow = ["1.0.0"]
        $scope.versionResult = {}
        $scope.list =[]
        $scope.uploaderButton = false
        $scope.fileToUpload = null
        $scope.buidName = null
        $scope.isBuildUploaded = false;
        $scope.showDownloader = false
        $scope.descriptionArray = ['']
        $scope.deploymentDescription = ''
        $scope.compatiblePosVersion = []
        $scope.selectedCompatiblePosVersion = null
        $scope.existedCompatiblePosVersion = null
    };
    $scope.validateVersion=function(version) {
        const pattern = /^\d+\.\d+\.\d+$/
        return pattern.test(version);
    }

    $scope.getCloudFrontUrl=function(){
           if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
                    return "https://d2txpay8i4xknu.cloudfront.net/";
                } else if (window.location.origin.indexOf("stage") > -1) {
                    return "https://d3t8lo3xxvrk8i.cloudfront.net/kettle-ui/"
                } else if (window.location.origin.indexOf("dev") > -1) {
                    return "https://d3t8lo3xxvrk8i.cloudfront.net/kettle-ui/"
                } else if (window.location.origin.indexOf("localhost") > -1) {
                    return "https://d3t8lo3xxvrk8i.cloudfront.net/"
                }else if (window.location.origin.indexOf("relax.chaayos.com") >= 0) {
                    return "https://d2txpay8i4xknu.cloudfront.net/";
                }

    }

    $scope.getVersionToAdd=function(){
         $scope.enterVersion = false;
         if($scope.selectApplicationName === "KETTLE_CRM" || $scope.selectApplicationName === "CAFE_APP"){
            $scope.uploaderButton = true
         }
         else{
            $scope.uploaderButton = false
         }
         $scope.versionResult = {}
         $scope.compatiblePosVersion = $scope.result["POS"]
         for(i=0 ; i<$scope.applicationName.length ; i++){
            $scope.list = []
            if($scope.applicationName[i] in $scope.result){
                $scope.list = $scope.result[$scope.applicationName[i]]
            }
            $scope.sortList($scope.list)
            var version  = ""
            if($scope.list.length > 0){
                   version = $scope.list[$scope.list.length -1]
                   const versionParts = version.split('.').map(Number);
                   if($scope.selectedReleasedType==="MAJOR"){
                       versionParts[0] += 1;
                       versionParts[1] = 0;
                       versionParts[2] = 0;
                   }
                   else if($scope.selectedReleasedType==="MINOR"){
                       versionParts[1] += 1;
                       versionParts[2] = 0;
                   }
                   else{
                       versionParts[2] += 1;
                   }
                   version = versionParts.join('.')
            }
            else{
                  version="1.0.0"
            }
            if($scope.applicationName[i] in $scope.versionResult){
                $scope.versionResult[$scope.applicationName[i]].push(version)
            }
            else{
                $scope.versionResult[$scope.applicationName[i]] = []
                $scope.versionResult[$scope.applicationName[i]].push(version)
            }
         }
         $scope.versionToShow = $scope.versionResult[$scope.selectApplicationName]
    }

    $scope.toggleVersionButton=function(){
    if($scope.enterVersion===true)
    {
        $scope.enterVersion = false;
    }
    else{
        $scope.enterVersion=true;
    }
    }

    $scope.multiSelectSettings = {
        showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
        scrollableHeight: '200px'
    };

    $scope.tooggleShowVersionButton=function(){
        if($scope.showVersionButton===true){
            $scope.showVersionButton = false;
        }
        else{
            $scope.showVersionButton = true;
        }
        $scope.getVersionToAdd()
    }
    $scope.showVersionAvailable=function(){
        $scope.tooggleShowVersionButton()
    }

    $scope.downloadBuild=function(applicationName,version){
        $rootScope.showFullScreenLoader = true;
        var url = AppUtil.restUrls.unitMetaData.downloadApkBuild
        	$http({
        	    method : 'GET',
        	    url : url + "/?applicationName="+applicationName+
                "&applicationVersion="+version,
        	}).then(function success(response) {
        	    var fileName = applicationName + "_" + version + ".apk";
        	    var link = document.createElement("a");
        	    link.download = fileName;
        	    link.href = $scope.getCloudFrontUrl() + response.data;
        	    document.body.appendChild(link);
        	    link.click();
        	    document.body.removeChild(link);
        	    delete link;
        	    $rootScope.showFullScreenLoader = false;
        	}, function error(response) {
        	    console.log("error:" + response);
        	    alert("Unable to download build");
        	    $rootScope.showFullScreenLoader = false;
        	});
    }

    $scope.getallActiveVersion=function(){
        $scope.showTable = true;
        $scope.finalApplication = $scope.selectApplicationName
        if($scope.finalApplication === "KETTLE_CRM" || $scope.finalApplication === "CAFE_APP"){
            $scope.showDownloader = true
        }
        else{
            $scope.showDownloader = false
        }
        $scope.allActiveVersion=[]
        if($scope.result == null){
            $scope.fetchActiveVersions()
        }
        else{
             $scope.activeVersionList = $scope.result[$scope.finalApplication]
        }
    };

    $scope.updateVersion=function(applicationName,applicationVersion){
          $http({
                           method: 'POST',
                           url: AppUtil.restUrls.unitMetaData.updateApplicationVersion + "/?applicationName="+applicationName+
                                                                "&applicationVersion="+applicationVersion +
                                                                "&updatedBy=" + $rootScope.userData.id +
                                                                "&releaseType=" + null +
                                                                "&buildName=" + null +
                                                                "&deploymentDescription=" + null
                        }).then(function success(response) {
                            $scope.updateStatus=true
                            $scope.result=null
                            $scope.getallActiveVersion();
                        }, function error(response) {
                           $scope.updateStatus=false
                           window.alert("Error while updating version")
                           console.log("error: " + response);
                        });
    }

    $scope.updateAndAddVersion=function(applicationName,applicationVersion,releaseType,buildName,deploymentDescription){
         $http({
                   method: 'POST',
                   url: AppUtil.restUrls.unitMetaData.updateApplicationVersion + "/?applicationName="+applicationName+
                                                        "&applicationVersion="+applicationVersion +
                                                        "&updatedBy=" + $rootScope.userData.id +
                                                        "&releaseType=" + releaseType +
                                                        "&buildName=" + buildName +
                                                        "&deploymentDescription=" + deploymentDescription
                }).then(function success(response) {
                    bootbox.alert("Version Added SuccessFully !!");
                    $scope.updateStatus=true
                }, function error(response) {
                   $scope.updateStatus=false
                   window.alert("Error while updating version")
                   console.log("error: " + response);
                });
    }

    $scope.addNewVersion=function (){
        $scope.versionToAdd=null;
        $scope.selectApplicationName=""
        $scope.fetchActiveVersions()
        $("#addNewVersion").modal("show");
    }

    $scope.sortList=function(versions){
        versions.sort((a, b) => {
                    const partsA = a.split('.').map(Number);
                    const partsB = b.split('.').map(Number);

                    for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
                        if (partsA[i] === undefined) return -1;
                        if (partsB[i] === undefined) return 1;
                        if (partsA[i] < partsB[i]) return -1;
                        if (partsA[i] > partsB[i]) return 1;
                    }
                    return 0;
        });
        $scope.activeVersionList = versions
    }

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    $scope.isVersionExist=function(applicationName,version){
        if(applicationName in $scope.result){
            var data = $scope.result[applicationName]
            for(let i=0 ;i < data.length ; i++){
                if(data[i]===version){
                    return true;
                }
            }
        }
        return false;
    }
    $scope.addVersion=function(){
        $scope.deploymentDescription =''
        for(let i =0 ; i<$scope.descriptionArray.length ;i++){
            if($scope.descriptionArray[i]!==''){
                if(i!==0 && $scope.deploymentDescription!==''){
                    $scope.deploymentDescription = $scope.deploymentDescription + "@"
                }
                $scope.deploymentDescription = $scope.deploymentDescription + $scope.descriptionArray[i]
            }
        }
        if($scope.deploymentDescription===''){
            $scope.deploymentDescription = null
        }
        if($scope.versionToAdd ===""){
             bootbox.alert("Please Select Verison")
        }
        else if(!$scope.validateVersion($scope.versionToAdd)){
            bootbox.alert("Please Enter a Valid Version Type (Example : 1.0.1)")
        }
        else if($scope.isVersionExist($scope.selectApplicationName,$scope.versionToAdd)){
            bootbox.alert("Version Already Exist !!")
        }
        else{
            if($scope.result != null && (!($scope.selectApplicationName in $scope.result) || $scope.result[$scope.selectApplicationName].length<=20)){
                if($scope.selectApplicationName === "KETTLE_CRM" || $scope.selectApplicationName === "CAFE_APP"){
                    if (fileService.getFile() == null || fileService.getFile() == undefined ||
                            !angular.equals(fileService.getFile().type, "application/vnd.android.package-archive")) {
                        bootbox.alert("Please select a .apk file");
                        return;
                    }
                    var fileExt = getFileExtension(fileService.getFile().name);
                    var newName = fileService.getFile().name.replace(fileService.getFile().name,$scope.selectApplicationName + "_" + $scope.versionToAdd);
                    var newFile=new File([fileService.getFile()],newName+"."+fileExt);
                    var fd = new FormData();
                    fd.append("file", newFile);
                    $rootScope.showFullScreenLoader = true;
                    $http({
                                    url:AppUtil.restUrls.unitMetaData.uploadApkBuild ,
                                    method: 'POST',
                                    data: fd,
                                    headers: {
                                        'Content-Type': undefined
                                    },
                                    transformRequest: angular.identity
                                }).success(function (response) {
                                   $rootScope.showFullScreenLoader = false;
                                    angular.element("input[type='file']").val(null);
                                    fileService.push(null);
                                    $scope.buidName = response.key
                                    $scope.isBuildUploaded = true
                                    $scope.fileToUpload = null
                                    if(response){
//                                        bootbox.alert("File uploaded");
                                        $scope.updateAndAddVersion($scope.selectApplicationName,$scope.versionToAdd,$scope.selectedReleasedType,$scope.buidName,$scope.deploymentDescription)
                                        if($scope.selectedCompatiblePosVersion!==null || $scope.selectedCompatiblePosVersion!==""){
                                            $scope.addVersionCompatibility($scope.selectApplicationName,$scope.versionToAdd,$scope.selectedCompatiblePosVersion)
                                        }
                                        $scope.result=null
                                        $scope.buidName = null
                                        $scope.versionToAdd = null
                                        $scope.init()

                                    }
                                    else{
                                        bootbox.alert("File Not Upload");
                                        $rootScope.showFullScreenLoader = false;
                                        $scope.init()
                                    }
                                }).error(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    alert("Error while uploading Expense Sheet");
                                    $scope.init()
                                });

                }
                else{
                    $scope.updateAndAddVersion($scope.selectApplicationName,$scope.versionToAdd,$scope.selectedReleasedType,$scope.selectApplicationName + "_" + $scope.versionToAdd,$scope.deploymentDescription)
                    $scope.result=null
                    $scope.versionToAdd = null
                    $scope.init()

                }
            }
            else{
                bootbox.alert("You Can not add More than 20 active version at a time")
            }
            $("#addNewVersion").modal("hide");
        }

    }

     $scope.onVersionInput=function(e){
        $scope.versionToAdd =e;
     }

     $scope.onCompatibileVersionInput=function(e){
        $scope.selectedCompatiblePosVersion = e;
     }

     $scope.onDescriptionInput=function(e){
        $scope.deploymentDescription = e
     }

    $scope.fetchActiveVersions=function(){
        $http({
                   method: 'GET',
                   url: AppUtil.restUrls.unitMetaData.getAllActiveVersions
                }).then(function success(response) {
                   $scope.result = response.data;
                   $scope.activeVersionList = $scope.result[$scope.finalApplication]
                }, function error(response) {
                   window.alert("Error while fetching files detail")
                   console.log("error: " + response);
                });
    }

    $scope.addVersionCompatibility=function(applicationName,applicationVersion,posVersions){
        $http({
              method: 'POST',
              url: AppUtil.restUrls.unitMetaData.addVersionCompatibility +"/?applicationName="+applicationName+
                                                                        "&applicationVersion="+applicationVersion +
                                                                        "&posVersion="+posVersions +
                                                                        "&updatedBy=" + $rootScope.userData.id
           }).then(function success(response) {
              console.log("Compatibility Added !")
           }, function error(response) {
              window.alert("Error while Adding Compatibility of Versions")
              console.log("error: " + response);
           });
    }

    $scope.showCompatibilityModal=function(applicationName,version){
        $scope.selectApplicationName=applicationName
        $scope.versionToAdd = version;
        $scope.compatiblePosVersion = $scope.result["POS"]
        $scope.getCompatiblePosVersion(applicationName,version)
        fileService.push(null);
        $scope.fileToUpload = null
        $("#updateCompatibility").modal("show");
    }

    $scope.updateCompatibility=function(){
       if($scope.selectedCompatiblePosVersion != null){
            $scope.addVersionCompatibility($scope.selectApplicationName,$scope.versionToAdd,$scope.selectedCompatiblePosVersion);
       }
       if (fileService.getFile() !== null && fileService.getFile() !== undefined &&
                                   angular.equals(fileService.getFile().type, "application/vnd.android.package-archive")) {
            var fileExt = getFileExtension(fileService.getFile().name);
            var newName = fileService.getFile().name.replace(fileService.getFile().name,$scope.selectApplicationName + "_" + $scope.versionToAdd);
            var newFile=new File([fileService.getFile()],newName+"."+fileExt);
            var fd = new FormData();
            fd.append("file", newFile);
            $rootScope.showFullScreenLoader = true;
            $http({
                            url:AppUtil.restUrls.unitMetaData.uploadApkBuild ,
                            method: 'POST',
                            data: fd,
                            headers: {
                                'Content-Type': undefined
                            },
                            transformRequest: angular.identity
                        }).success(function (response) {
                           $rootScope.showFullScreenLoader = false;
                            angular.element("input[type='file']").val(null);
                            fileService.push(null);
                            $scope.fileToUpload = null
                            if(response){
                                bootbox.alert("File uploaded");
                                }
                            }).error(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    alert("Error while uploading Expense Sheet");
                                    $scope.init()
                           });
       }
       $scope.compatiblePosVersion =[]
       $("#updateCompatibility").modal("hide");
    }

    $scope.getCompatiblePosVersion=function(applicationName,version){
        $http({
                      method: 'GET',
                      url: AppUtil.restUrls.unitMetaData.getCompatiblePosVersion +"/?applicationName="+applicationName+
                                                                                "&applicationVersion="+version

                   }).then(function success(response) {
                      $scope.existedCompatiblePosVersion = response.data[0]
                   }, function error(response) {
                      window.alert("Error while Adding Compatibility of Versions")
                      console.log("error: " + response);
                   });
    }

    $scope.closeCompatibilityModal=function(){
        $scope.selectApplicationName=""
        $scope.versionToAdd = null
        $scope.compatiblePosVersion =[]
        $("#updateCompatibility").modal("hide");
    }

    $scope.addDescription = function () {
        $scope.descriptionArray.push('');
    };

    $scope.deleteDescription = function (index) {
        $scope.descriptionArray.splice(index, 1);
    };

 }])