adminapp.controller("unitContactsMappingctrl",function ($scope,$rootScope,$http,AppUtil){
  $rootScope.showFullScreenLoader = true;

  $scope.init = function () {
    $scope.regionList=[]
    $scope.category = "CAFE"
    $scope.selectedRegionList = [];
    $scope.unitFilter = null;
    $scope.unitContacts = null;
    getRegionDetails();
    getAllUnitContacts();
  }
  function getRegionDetails() {
    $http({
      method: "GET",
      url: AppUtil.restUrls.unitMetaData.regions,
    }).then(
      function success(response) {
        $scope.regionList = response.data;
        console.log($scope.regionList);
        $rootScope.showFullScreenLoader = false;
      },
      function error(response) {
        console.log("error:" + response);
      }
    );
  }

  function getAllUnitContacts(){
    $http({
      method: "GET",
      url: AppUtil.restUrls.unitMetaData.allUnitContacts,
    }).then(
      function success(response) {
          if (response!=null && response.status == 200 && response.data !=null && response.data.body !=null) {
          $scope.unitContacts = response.data.body;
          $scope.fetchUnits();
          console.log("allUnits=", $scope.unitContacts);
          $rootScope.showFullScreenLoader = false;
        }
      },
      function error(response) {
        $rootScope.showFullScreenLoader = false;
        console.log("error:" + response);
      }
    );
  }
  
  $scope.fetchUnits = function() {
    var url =
      AppUtil.restUrls.unitMetaData.allUnits +
      "?category=" +
      $scope.category ;
    if ($scope.selectedRegionList != null) {
      url =
        AppUtil.restUrls.unitMetaData.allUnits +
        "?category=" +
        $scope.category +
        "&region=" +
        $scope.selectedRegionList;
    }
    $rootScope.showFullScreenLoader = true;
    $http({
      method: "GET",
      url: url,
    }).then(
      function success(response) {
        if(response.data!=null)
        {
          $scope.unitList = response.data;
          $scope.unitList.forEach((unit)=>{
            if($scope.unitContacts[unit.id] != null)
            {
              unit.contactDetails = $scope.unitContacts[unit.id].contactDetails
              unit.contactDetailsExist = true;
            }
            else{
              unit.contactDetailsExist = false;
              unit.contactDetails = [
                {
                code:null,
                name:null
                },
                {
                  code:null,
                  name:null
                },
                {
                code:null,
                name:null
                },
              ]
          }
          })
        }  
        console.log("allUnits=", $scope.unitList);
        $rootScope.showFullScreenLoader = false;
      },
      function error(response) {
        $rootScope.showFullScreenLoader = false;
        console.log("error:" + response);
      }
    );
  };

  $scope.multiSelectSettings = {
    showEnableSearchButton: true,
    template: "<b> {{option}}</b>",
    scrollable: true,
    scrollableHeight: "200px",
  };

  
  $scope.openUpdateContactsModal = function(unit){

    $scope.unitContactsToBeUpdated = JSON.parse(JSON.stringify(unit));

    $("#contactDetailsUpdateModal").modal("show");

  }

  $scope.updateContactDetail = function(unitContactsToBeUpdated){

    var phoneNumberPattern = /^\d{10}$/;

    // Test if the input matches the pattern
    if((AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[0].name)) || !phoneNumberPattern.test(unitContactsToBeUpdated.contactDetails[0].code)){
      alert("First Contact Person Details is Not Valid.")
      $("#contactDetailsUpdateModal").modal("show");
      return;
    }  
    if(!AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[1]) && !AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[1].code) && (!phoneNumberPattern.test(unitContactsToBeUpdated.contactDetails[1].code) ||  AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[1].name))){
      alert("Second Contact Person Details is Not Valid.")
      $("#contactDetailsUpdateModal").modal("show");
      return;
    } 
    if( !AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[2]) && !AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[2].code) && (!phoneNumberPattern.test(unitContactsToBeUpdated.contactDetails[2].code) || AppUtil.isEmptyObject(unitContactsToBeUpdated.contactDetails[2].name))){
      alert("Third Contact Person Details is Not Valid.")
      $("#contactDetailsUpdateModal").modal("show");
      return;
    } 
    let data = {
      unitId:unitContactsToBeUpdated.id,
      unitName:unitContactsToBeUpdated.name,
      contactDetails:unitContactsToBeUpdated.contactDetails
    }
    $http({
      method: 'POST',
      url: AppUtil.restUrls.unitMetaData.addUnitContacts,
      data,
  })
  .then(function success(response) {
      if (response.status == 200) {
        if(response.data)
        {
          alert("Details Saved Successfully")
          getAllUnitContacts();
        }
        else{
        alert("Not  Successfully")

        }
      } 
    $("#contactDetailsUpdateModal").modal("hide");

  },function error(response){
    $("#contactDetailsUpdateModal").modal("hide");
    alert("Some Error Occurs.Please Try Again Later")
  })
  }

})