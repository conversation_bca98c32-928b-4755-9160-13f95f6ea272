adminapp.controller("CampaignBuilderCtrl", function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

    console.log($stateParams);
    $scope.campaignDetail = $stateParams.campaignDetail;

    $scope.init = function () {

        $scope.NBO = 'NBO';
        $scope.GENERAL = 'GENERAL';
        $scope.DELIVERY_NBO = 'DELIVERY_NBO';
        $scope.DELIVERY_GENERAL = 'DELIVERY_GENERAL';
        $scope.SLOT_GAME = 'SLOT_GAME';
        $scope.CAFE_LAUNCH = 'CAFE_LAUNCH';
        $scope.PRODUCT_PROMOTION='PRODUCT_PROMOTION';
        $scope.CAMPAIGN_PRIMARY_URL = 'https://cafes.chaayos.com/myoffer';
        $scope.SLOT_GAME_CAMPAIGN_PRIMARY_URL = 'https://cafes.chaayos.com/myoffer';

        $scope.userDetails=AppUtil.getUserValues();
        $scope.campaignStrategyList = [];
        $scope.campaignSourceList = [];
        $scope.campaignMediumList = [];
        // $scope.campaignNameList = [];
        $scope.campaignCategoryList = [];
        $scope.cloneCouponOptions = [];
        $scope.newCustomerOnlyOptions = [];
        $scope.regionList = [];
        $scope.cityList = [];
        $scope.allUnitList = [];
        $scope.unitList = [];
        $scope.parentCampaignStrategyList = [];

        $scope.campaignMappings = [];

        $scope.selectedRegionList = [];
        $scope.selectedCityList = [];
        $scope.selectedUnitList = [];
        $scope.unitMap = new Map();
        $scope.unitIdToNameMap = new Map();
        $scope.campaignImageFile = null;
        $scope.image1File = null;
        $scope.image2File = null;
        $scope.image3File = null;
        $scope.crmAppBannerFile = null;
        $scope.previewUrlImageFile = null;
        $scope.previewImageSource = null;

        $scope.nonMandatoryFields = new Set();
        $scope.nonMandatoryFieldsForSlotGame = new Set();

        $scope.fieldInfoMap = new Map();
        $scope.fieldInfoExampleMap = new Map();

        $scope.testContactNumber = null;
        $scope.testCustomerName = null;

        $scope.validCampaignList = [];
        $scope.linkedCampaignOptions = [];
        $scope.fetchCampaignsTag = false;

        $scope.brandList = [];

        $scope.yesNoOption = [{
            code: false,
            name: "No"
        }, {
            code: true,
            name: "Yes"
        }];

        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.listTypes
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.categoryLists = response.data;
            console.log($scope.categoryLists);
            insertData();
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });

        getBrandDetails();
        getRegionDetails();
        getCityAndUnitDetails();

        $scope.today = AppUtil.formatDate(new Date(new Date().setDate(new Date().getDate() - 1)).getTime(), 'yyyy-MM-dd');
        console.log("Today's date check: " + $scope.today);

        if (AppUtil.isEmptyObject($stateParams.campaignDetail)) {
            $scope.campaignDetail = {};
            $scope.campaignDetail.primaryUrl = $scope.CAMPAIGN_PRIMARY_URL;
            $scope.campaignDetail.campaignStrategy = null;
            $scope.campaignDetail.campaignSource = null;
            $scope.campaignDetail.campaignMedium = null;
            $scope.campaignDetail.campaignName = null;
            $scope.campaignDetail.campaignCategory = null;
            $scope.campaignDetail.campaignDesc = null;
            $scope.campaignDetail.couponClone = null;
            $scope.campaignDetail.couponPrefix = null;
            $scope.campaignDetail.couponCode = null;
            $scope.campaignDetail.couponCodeDesc = null;
            $scope.campaignDetail.campaignReach = 1;
            $scope.campaignDetail.region = null;
            $scope.campaignDetail.city = null;
            $scope.campaignDetail.unitIds = null;
            $scope.campaignDetail.usageLimit = null;
            $scope.campaignDetail.startDate = null;
            $scope.campaignDetail.endDate = null;
            $scope.campaignDetail.validity = null;
            $scope.campaignDetail.heroBannerMobile = null;
            $scope.campaignDetail.heroBannerDesktop = null;
            $scope.campaignDetail.landingPageDesc = null;
            $scope.campaignDetail.smsTemplate = null;
            $scope.campaignDetail.whatsappTemplate = null;
            $scope.campaignDetail.smsReminder = null;
            $scope.campaignDetail.whatsappReminder = null;
            $scope.campaignDetail.reminderDayGap = null;
            $scope.campaignDetail.utmHeading = null;
            $scope.campaignDetail.utmDesc = null;
            $scope.campaignDetail.utmImageUrl = null;
            $scope.campaignDetail.redirectionUrl = null;
            $scope.campaignDetail.image1 = null;
            $scope.campaignDetail.image2 = null;
            $scope.campaignDetail.image3 = null;
            $scope.campaignDetail.crmAppBannerUrl = null;
            $scope.campaignDetail.newCustomerOnly = null;
            $scope.campaignDetail.linkedCampaignId = null;
            $scope.campaignDetail.couponApplicableAfter = null;
            $scope.campaignDetail.brandId = null;
            $scope.campaignDetail.applicableForOrder = null;
            $scope.campaignDetail.launchUnitId = null;
            $scope.campaignDetail.cafeLaunchDate = null;
            getCustomerTypeDetails();
        } else {
            if ($scope.campaignDetail.campaignId != null) {
                $scope.isEditCampaignPage = true;
            }
            $scope.campaignDetail.startDate = moment($scope.campaignDetail.startDate).format('YYYY-MM-DD');
            $scope.campaignDetail.endDate = moment($scope.campaignDetail.endDate).format('YYYY-MM-DD');
            if (angular.equals($scope.campaignDetail.couponClone, true)) {
                $scope.campaignDetail.couponClone = "Yes";
            } else {
                $scope.campaignDetail.couponClone = "No";
            }
            if (angular.equals($scope.campaignDetail.newCustomerOnly, 'Y')) {
                $scope.campaignDetail.newCustomerOnly = "Yes";
            } else {
                $scope.campaignDetail.newCustomerOnly = "No";
            }
            if ($scope.campaignDetail.region && $scope.campaignDetail.region.length > 0) {
                $scope.selectedRegionList = $scope.campaignDetail.region.split(",");
            }
            if ($scope.campaignDetail.city && $scope.campaignDetail.city.length > 0) {
                $scope.selectedCityList = $scope.campaignDetail.city.split(",");
            }
            // if ($scope.campaignDetail.unitIds) {
            //     $scope.selectedUnitIdList = [];
            //     var unitIds = $scope.campaignDetail.unitIds.split(",");
            //     for(var i in unitIds){
            //         $scope.selectedUnitList.push($scope.unitIdToNameMap.get($scope.unitIds[i]));
            //     }
            // }

            switch ($scope.campaignDetail.campaignReach) {
                case "SYSTEM_SPECIFIC":
                    $scope.campaignDetail.campaignReach = 1;
                    break;
                case "REGION_SPECIFIC":
                    $scope.campaignDetail.campaignReach = 2;
                    break;
                case "UNIT_SPECIFIC":
                    $scope.campaignDetail.campaignReach = 3;
                    break;
                default:
                    $scope.campaignDetail.campaignReach = 1;
            }

            $scope.campaignMappings = [];
            for (var key in $scope.campaignDetail.mappings) {
                for (var innerKey in $scope.campaignDetail.mappings[key]) {
                    var customerCampaignObj = {};
                    customerCampaignObj.customerType = key;
                    customerCampaignObj.journey = innerKey;
                    customerCampaignObj.validityInDays = $scope.campaignDetail.mappings[key][innerKey].validityInDays;
                    customerCampaignObj.reminderDays = $scope.campaignDetail.mappings[key][innerKey].reminderDays;
                    customerCampaignObj.code = $scope.campaignDetail.mappings[key][innerKey].code;
                    customerCampaignObj.desc = $scope.campaignDetail.mappings[key][innerKey].desc;
                    customerCampaignObj.campaignCouponMappingId = $scope.campaignDetail.mappings[key][innerKey].campaignCouponMappingId;
                    customerCampaignObj.isValid = 'Check';
                    if (customerCampaignObj.customerType === 'NEW' && customerCampaignObj.code === 'LOYAL_TEA') {
                        customerCampaignObj.isValid = 'Valid';
                    }
                    customerCampaignObj.isRequired = true;
                    $scope.campaignMappings.push(customerCampaignObj);
                }
            }

            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.orderManagement.customerMappingTypes
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if (response.status == 200) {
                    $scope.customerTypeDetail = response.data;
                    console.log($scope.campaignMappings);
                }
                console.log(response);
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error: " + response);
            });

            if ($scope.campaignDetail.linkedCampaignId != null) {
                $scope.fetchCampaignsTag = true;
                $scope.fetchCampaignListForLinking();
            }

            $scope.campaignDetail.region = null;
            $scope.campaignDetail.city = null;
            $scope.isCloneOrEdit = true;
        }

        fillEntryMap();
        fillNonMandatoryFieldsSet();
        fillInfoMap();
    };

    function fillEntryMap() {
        $scope.entryMap = new Map();
        $scope.entryMap.set("primaryUrl", "Primary URL");
        $scope.entryMap.set("campaignStrategy", "Campaign Strategy");
        $scope.entryMap.set("campaignSource", "Campaign Source");
        $scope.entryMap.set("campaignMedium", "Campaign Medium");
        $scope.entryMap.set("campaignName", "Campaign Name");
        $scope.entryMap.set("campaignCategory", "Campaign Category");
        $scope.entryMap.set("campaignDesc", "Campaign Description");
        $scope.entryMap.set("couponClone", "Clone Coupon");
        $scope.entryMap.set("couponPrefix", "Coupon Prefix");
        $scope.entryMap.set("couponCode", "Coupon Code");
        $scope.entryMap.set("couponCodeDesc", "Coupon code description");
        $scope.entryMap.set("campaignReach", "Campaign Reach");
        $scope.entryMap.set("region", "Region");
        $scope.entryMap.set("city", "City");
        $scope.entryMap.set("unitIds", "Unit name and Id");
        $scope.entryMap.set("usageLimit", "Usage Limit");
        $scope.entryMap.set("startDate", "Start date");
        $scope.entryMap.set("endDate", "End date");
        $scope.entryMap.set("validity", "Validity");
        $scope.entryMap.set("heroBannerMobile", "Landing Page Hero Banner for Mobile and Desktop");
        $scope.entryMap.set("landingPageDesc", "Landing Page Description");
        $scope.entryMap.set("smsTemplate", "SMS Template");
        $scope.entryMap.set("whatsappTemplate", "Whatsapp Template");
        $scope.entryMap.set("smsReminder", "Reminder SMS");
        $scope.entryMap.set("whatsappReminder", "Reminder Whatsapp");
        $scope.entryMap.set("reminderDayGap", "Reminder Day Gap");
        $scope.entryMap.set("utmHeading", "Preview URL Heading");
        $scope.entryMap.set("utmDesc", "Preview URL Description");
        $scope.entryMap.set("utmImageUrl", "Preview URL Image");
        $scope.entryMap.set("redirectionUrl", "Redirection URL");
        $scope.entryMap.set("image1", "Campaign Image 1");
        $scope.entryMap.set("image2", "Campaign Image 2");
        $scope.entryMap.set("image3", "Campaign Image 3");
        $scope.entryMap.set("crmAppBannerUrl", "CRM App Banner");
        $scope.entryMap.set("newCustomerOnly", "New Customer Only");
        $scope.entryMap.set("linkedCampaignId", "Linked Campaign");
        $scope.entryMap.set("couponApplicableAfter", "Coupon Applicable After");
        $scope.entryMap.set("brandId", "Brand");
        $scope.entryMap.set("applicableForOrder", "Applicable for Orders");
        $scope.entryMap.set("parentCampaignStrategy", "Parent Campaign Strategy");
        $scope.entryMap.set("launchUnitId", "Cafe Launch Unit");
        $scope.entryMap.set("cafeLaunchDate", "Cafe Launch Date");
    }

    function fillNonMandatoryFieldsSet() {
        $scope.nonMandatoryFields.add("campaignId");
        $scope.nonMandatoryFields.add("campaignToken");
        $scope.nonMandatoryFields.add("campaignStatus");
        $scope.nonMandatoryFields.add("region");
        $scope.nonMandatoryFields.add("city");
        $scope.nonMandatoryFields.add("unitIds");
        $scope.nonMandatoryFields.add("validity");
        $scope.nonMandatoryFields.add("smsTemplate");
        $scope.nonMandatoryFields.add("whatsappTemplate");
        $scope.nonMandatoryFields.add("smsReminder");
        $scope.nonMandatoryFields.add("whatsappReminder");
        $scope.nonMandatoryFields.add("reminderDayGap");
        $scope.nonMandatoryFields.add("heroBannerMobile");
        $scope.nonMandatoryFields.add("heroBannerDesktop");
        $scope.nonMandatoryFields.add("longUrl");
        $scope.nonMandatoryFields.add("shortUrl");
        $scope.nonMandatoryFields.add("image2");
        $scope.nonMandatoryFields.add("image3");
        $scope.nonMandatoryFields.add("crmAppBannerUrl");
        $scope.nonMandatoryFields.add("linkedCampaignId");
        $scope.nonMandatoryFields.add("couponApplicableAfter");
        $scope.nonMandatoryFields.add("parentCampaignStrategy");

        $scope.nonMandatoryFieldsForSlotGame.add("usageLimit");
        $scope.nonMandatoryFieldsForSlotGame.add("utmImageUrl");
        $scope.nonMandatoryFieldsForSlotGame.add("image1");
        $scope.nonMandatoryFieldsForSlotGame.add("newCustomerOnly");
    }

    function getBrandDetails() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.brandList = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    function getRegionDetails() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.regionList = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    function fillUnitMap() {
        for (var i in $scope.allUnitList) {
            var unit = $scope.allUnitList[i];
            $scope.unitMap.set(unit.name, unit.id);
            $scope.unitIdToNameMap.set(unit.id, unit.name);
        }
        if ($scope.campaignDetail.unitIds && $scope.campaignDetail.unitIds.length > 0) {
            var unitIds = $scope.campaignDetail.unitIds.split(",");
            for (var i in unitIds) {
                $scope.selectedUnitList.push($scope.unitIdToNameMap.get(parseInt(unitIds[i])));
            }
        }
        $scope.campaignDetail.unitIds = null;
    }

    function getCityAndUnitDetails() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits,
            params: {
                category: 'CAFE'
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.allUnitList = response.data;
            fillUnitMap();
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    function getCustomerTypeDetails() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.orderManagement.customerMappingTypes
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status == 200) {
                $scope.customerTypeDetail = response.data;
                for (var i = 0; i < $scope.customerTypeDetail.length; i++) {
                    for (var j = 0; j < $scope.customerTypeDetail[i].journeyCount; j++) {
                        var customerCampaignObj = {};
                        customerCampaignObj.customerType = $scope.customerTypeDetail[i].customerTypes;
                        customerCampaignObj.journey = j + 1;
                        customerCampaignObj.validityInDays = $scope.customerTypeDetail[i].validityInDays;
                        customerCampaignObj.reminderDays = $scope.customerTypeDetail[i].reminderDays;
                        customerCampaignObj.code = $scope.customerTypeDetail[i].defaultCloneCode;
                        customerCampaignObj.desc = null;
                        customerCampaignObj.isValid = 'Check';
                        if (customerCampaignObj.customerType === 'NEW' && customerCampaignObj.code === 'LOYAL_TEA') {
                            customerCampaignObj.isValid = 'Valid';
                        }
                        customerCampaignObj.isRequired = false;
                        $scope.campaignMappings.push(customerCampaignObj);
                    }
                }
                $scope.allCampaignMappings = $scope.campaignMappings;
                console.log($scope.campaignMappings);
            }
            console.log(response);
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });

    }

    function insertData() {

        for (var item in $scope.categoryLists['CAMPAIGN_DETAIL']) {
            var category = $scope.categoryLists['CAMPAIGN_DETAIL'][item];
            if (angular.equals(category['detail']['name'], "Campaign Source") && angular.equals(category['detail']['status'], "ACTIVE")) {
                for (var itr in category['content']) {
                    if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                        $scope.campaignSourceList.push(category['content'][itr].name);
                    }
                }
            } else if (angular.equals(category['detail']['name'], "Campaign Medium") && angular.equals(category['detail']['status'], "ACTIVE")) {
                for (var itr in category['content']) {
                    if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                        $scope.campaignMediumList.push(category['content'][itr].name);
                    }
                }
            }
            // else if (angular.equals(category['detail']['name'], "Campaign Name") && angular.equals(category['detail']['status'], "ACTIVE")) {
            //     for (var itr in category['content']) {
            //         if (angular.equals(category['content'][itr].status, "ACTIVE")) {
            //             $scope.campaignNameList.push(category['content'][itr].name);
            //         }
            //     }
            // }
            else if (angular.equals(category['detail']['name'], "Campaign Category") && angular.equals(category['detail']['status'], "ACTIVE")) {
                for (var itr in category['content']) {
                    if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                        $scope.campaignCategoryList.push(category['content'][itr].name);
                    }
                }
            } else if (angular.equals(category['detail']['name'], "Parent Strategy") && angular.equals(category['detail']['status'], "ACTIVE")) {
                for (var itr in category['content']) {
                    if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                        $scope.parentCampaignStrategyList.push(category['content'][itr].code);
                    }
                }
            }
        }

        $scope.campaignStrategyList.push($scope.NBO, $scope.GENERAL, $scope.DELIVERY_NBO, $scope.DELIVERY_GENERAL, $scope.SLOT_GAME, $scope.CAFE_LAUNCH, $scope.PRODUCT_PROMOTION);
        $scope.cloneCouponOptions.push('Yes', 'No');
        $scope.newCustomerOnlyOptions.push('Yes', 'No');
    }

    $scope.multiSelectSettings = {
        showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
        scrollableHeight: '200px'
    };

    $scope.updateCampaignMapping = function () {
        if ($scope.campaignDetail.campaignStrategy == null || $scope.checkCampaignStrategyForGeneralOrSlotGame()) {
            var defaultList = [];
            for (var i = 0; i < $scope.campaignMappings.length; i++) {
                if (angular.equals($scope.campaignMappings[i].customerType, 'DEFAULT'))
                    defaultList.push($scope.campaignMappings[i]);
            }
            $scope.campaignMappings = defaultList;
        } else {
            $scope.campaignMappings = $scope.allCampaignMappings;
        }
        for (var i = 0; i < $scope.campaignMappings.length; i++) {
            $scope.campaignMappings[i].startDate = null;
            $scope.campaignMappings[i].endDate = null;
            $scope.invalidateCoupon(i);
        }

        $scope.campaignDetail.linkedCampaignId = null;
        $scope.campaignDetail.newCustomerOnly = null;
        $scope.campaignDetail.couponApplicableAfter = null;
        $scope.fetchCampaignListForLinking();
        $scope.campaignDetail.couponPrefix = null;
        $scope.campaignDetail.parentCampaignStrategy = null;

        if($scope.checkCampaignStrategyForSlotGame()) {
            $scope.campaignDetail.primaryUrl = $scope.SLOT_GAME_CAMPAIGN_PRIMARY_URL;
        } else {
            $scope.campaignDetail.primaryUrl = $scope.CAMPAIGN_PRIMARY_URL;
        }

    };

    function validateDeliveryCoupon(index) {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.validateDeliveryCoupon,
            params: {
                masterCoupon: $scope.campaignMappings[index].code
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            console.log(response);
            if (response.status === 200 && response.data != null && typeof(response.data) === 'number') {
                $scope.validDeliveryCouponCount = response.data;
                if ($scope.validDeliveryCouponCount > 0) {
                    $scope.campaignMappings[index].isValid = 'Valid';
                    $scope.campaignMappings[index].deliveryCouponCount = $scope.validDeliveryCouponCount;
                } else {
                    alert("Please enter Valid coupon");
                }
            } else {
                alert("Please enter Valid coupon");
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });
    }

    $scope.validateCampaignMappedCoupon = function (index) {

        if ($scope.campaignDetail.campaignStrategy === $scope.DELIVERY_GENERAL || $scope.campaignDetail.campaignStrategy === $scope.DELIVERY_NBO) {
            validateDeliveryCoupon(index);
        }
        else {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.couponSearch,
                data: $scope.campaignMappings[index].code
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                console.log(response);
                if (response.status == 200 && response.data != null && typeof(response.data) === 'object') {
                    if (angular.equals(response.data.status, 'ACTIVE')) {
                        $scope.campaignMappings[index].startDate = moment(response.data.startDate).format('YYYY-MM-DD');
                        $scope.campaignMappings[index].endDate = moment(response.data.endDate).format('YYYY-MM-DD');
                        $scope.campaignMappings[index].isValid = 'Valid';
                    } else {
                        alert("Please enter Active coupon");
                    }
                } else {
                    alert("Please enter Valid coupon");
                }
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error: " + response);
            });
        }
    };

    $scope.invalidateCoupon = function (index) {
        $scope.campaignMappings[index].isValid = 'Check';
        if ($scope.checkNewAndLoyalTea(index)) {
            $scope.campaignMappings[index].isValid = 'Valid';
        }
    };

    $scope.resetCouponData = function () {
        if (!angular.equals($scope.campaignDetail.couponClone, "Yes")) {
            // $scope.campaignDetail.couponCode = null;
            // $scope.campaignDetail.couponCodeDesc = null;
            $scope.campaignDetail.couponPrefix = null;
        }
    };

    $scope.checkIfCloneCoupon = function () {
        return angular.equals($scope.campaignDetail.couponClone, "Yes");
    };

    $scope.clearCampaignReach = function () {
        $scope.selectedRegionList = [];
        $scope.selectedCityList = [];
        $scope.selectedUnitList = [];
    };

    $scope.clearSelectedCityAndUnit = function () {
        $scope.selectedCityList = [];
        $scope.selectedUnitList = [];
    };

    $scope.clearSelectedUnit = function () {
        $scope.selectedUnitList = [];
    };

    $scope.applyCityFilter = function () {
        $scope.cityList = [];
        $scope.selectedRegionSet = new Set();
        for (var i in $scope.allUnitList) {
            var unit = $scope.allUnitList[i];
            for (var region in $scope.selectedRegionList) {
                if ($scope.selectedRegionList[region] === unit.region && unit.category === 'CAFE' && !$scope.cityList.includes(unit.city)) {
                    $scope.cityList.push(unit.city);
                }
                $scope.selectedRegionSet.add($scope.selectedRegionList[region]);
            }
        }
    };

    $scope.applyUnitFilter = function () {
        $scope.unitList = [];
        for (var i in $scope.allUnitList) {
            var unit = $scope.allUnitList[i];
            for (var city in $scope.selectedCityList) {
                if ($scope.selectedCityList[city] === unit.city && $scope.selectedRegionSet.has(unit.region) && unit.category === 'CAFE' && !$scope.unitList.includes(unit.name)) {
                    $scope.unitList.push(unit.name);
                }
            }
        }
    };

    $scope.setRegionListToString = function () {
        $scope.campaignDetail.region = $scope.selectedRegionList.toString();
    };

    $scope.setCityListToString = function () {
        $scope.campaignDetail.city = $scope.selectedCityList.toString();
    };

    $scope.setUnitListToString = function () {
        var selectedUnitIds = [];
        for (var i in $scope.selectedUnitList) {
            var unitName = $scope.selectedUnitList[i];
            selectedUnitIds.push($scope.unitMap.get(unitName));
        }
        $scope.campaignDetail.unitIds = selectedUnitIds.toString();
    };

    $scope.checkCampaignStrategyForBasicGeneral = function () {
        if ($scope.campaignDetail.campaignStrategy === $scope.GENERAL) {
            return true;
        }
        return false;
    };

    $scope.checkCampaignStrategyForGeneralOrSlotGame = function () {
        if($scope.checkCampaignStrategyForGeneral() || $scope.checkCampaignStrategyForSlotGame() || $scope.checkCampaignStrategyForCafeLaunch()) {
            return true;
        }
        return false;
    };

    $scope.checkCampaignStrategyForSlotGame = function () {
        return $scope.campaignDetail.campaignStrategy === $scope.SLOT_GAME;
    };

    $scope.checkCampaignStrategyForCafeLaunch = function () {
        return $scope.campaignDetail.campaignStrategy === $scope.CAFE_LAUNCH;
    };

    $scope.checkCampaignStrategyForGeneral = function () {
        if ($scope.campaignDetail.campaignStrategy === $scope.GENERAL || $scope.campaignDetail.campaignStrategy === $scope.DELIVERY_GENERAL) {
            return true;
        }
        return false;
    };

    $scope.checkCampaignStrategyForDelivery = function () {
        if ($scope.campaignDetail.campaignStrategy === $scope.DELIVERY_NBO || $scope.campaignDetail.campaignStrategy === $scope.DELIVERY_GENERAL) {
            return true;
        }
        return false;
    };

    $scope.fetchCampaignListForLinking = function () {

        if ($scope.fetchCampaignsTag) {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.offerManagement.campaignListByValidDate
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if (response.status === 200) {
                    $scope.validCampaignList = response.data;
                    $scope.linkedCampaignOptions = [];
                    for (var i = 0; i < $scope.validCampaignList.length; i++) {
                        if ($scope.validCampaignList[i].campaignStrategy === $scope.NBO && $scope.campaignDetail.campaignStrategy === $scope.DELIVERY_NBO) {
                            $scope.linkedCampaignOptions.push($scope.validCampaignList[i]);
                        }
                        if ($scope.validCampaignList[i].campaignStrategy === $scope.GENERAL && $scope.campaignDetail.campaignStrategy === $scope.DELIVERY_GENERAL) {
                            $scope.linkedCampaignOptions.push($scope.validCampaignList[i]);
                        }
                    }
                }
                console.log(response);
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error: " + response);
            });
        } else {
            $scope.linkedCampaignOptions = [];
            $scope.campaignDetail.linkedCampaignId = null;
        }
    };

    function copyToClipboard(copyText) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(copyText);
        } else {
            var textArea = document.createElement("textarea");
            textArea.value = copyText;
            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'successful' : 'unsuccessful';
                console.log('Fallback: Copying text command was ' + msg);
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
            }
            document.body.removeChild(textArea);
        }
    }

    $scope.copyLongUrlToClipboard = function () {
        copyToClipboard($scope.selectedCampaignLongUrl);
    };

    $scope.copyShortUrlToClipboard = function () {
        copyToClipboard($scope.selectedCampaignShortUrl);
    };

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function updateFileUploadName() {
        console.log("Original file name: " + $scope.campaignImageFile.name);
        var newFileName = $scope.campaignDetail.campaignName + "_" + Date.now() + "." + getFileExtension($scope.campaignImageFile.name);
        $scope.campaignImageFile = new File([$scope.campaignImageFile], newFileName);
        console.log("New file name: " + $scope.campaignImageFile.name);
    }

    function gcd(a, b) {
        if (b === 0) {
            return a;
        }
        return gcd(b, a % b);
    }


    function checkImageAspectRatioAndUpload(imageId) {
        var fileReader = new FileReader;
        fileReader.onload = function () {
            $scope.toUploadImagePath = new Image;
            $scope.toUploadImagePath.onload = function () {
                console.log(this.width, this.height);
                var gcdN = gcd(this.width, this.height);
                if (angular.equals(this.width / gcdN, this.height / gcdN) || (angular.equals(imageId, "Preview URL Image") && angular.equals(this.width / gcdN, 16) && angular.equals(this.height / gcdN, 9))
                    || (angular.equals(imageId, "Preview URL Image") && $scope.checkCampaignStrategyForSlotGame()) || (angular.equals(imageId, "CrmAppBanner"))) {
                    makeUploadRequest(imageId);
                } else {
                    alert("Image aspect ratio not correct");
                }
            };
            $scope.toUploadImagePath.src = fileReader.result;
        };
        fileReader.readAsDataURL($scope.campaignImageFile);
    }

    $scope.uploadCampaignImage = function (imageId) {

        if (angular.equals(imageId, 'Image1')) {
            $scope.campaignImageFile = $scope.image1File;
        } else if (angular.equals(imageId, 'Image2')) {
            $scope.campaignImageFile = $scope.image2File;
        } else if (angular.equals(imageId, 'Image3')) {
            $scope.campaignImageFile = $scope.image3File;
        } else if (angular.equals(imageId, 'CrmAppBanner')) {
            $scope.campaignImageFile = $scope.crmAppBannerFile;
        }
        else if (angular.equals(imageId, 'Preview URL Image')) {
            $scope.campaignImageFile = $scope.previewUrlImageFile;
        }

        if (fileService.getFile() == null || $scope.campaignImageFile == null) {
            alert("File not found");
            return;
        }

        var fileType = $scope.campaignImageFile.type;
        console.log("File Type: " + $scope.campaignImageFile.type);

        if ($scope.campaignDetail.campaignName == null || $scope.campaignDetail.campaignName.trim() === "") {
            alert("Please fill Campaign Name before uploading!");
            return;
        }
        if (angular.equals(fileType, "image/jpeg") || angular.equals(fileType, "image/png") || angular.equals(fileType, "image/svg+xml") || angular.equals(fileType, "image/webp")) {
            console.log("File Size: " + $scope.campaignImageFile.size);
            if (($scope.campaignImageFile.size) / 1024 > 300) {
                alert("Image size larger than 300 KB");
                return;
            }
            checkImageAspectRatioAndUpload(imageId);
        } else {
            alert("File must be jpg/png/webp/svg");
            return;
        }

    };

    function makeUploadRequest(imageId) {
        updateFileUploadName();

        var fd = new FormData();
        fd.append("file", $scope.campaignImageFile);
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.offerManagement.uploadCampaignImage,
            data: fd,
            headers: {'Content-Type': undefined},
            transformRequest: angular.identity
        }).success(function (response) {
            $rootScope.showFullScreenLoader = false;
            $scope.campaignImageFile = null;
            fileService.push(null);
            angular.element("input[type='file']").val(null);
            console.log(response.url);
            if (angular.equals(imageId, 'Image1')) {
                $scope.campaignDetail.image1 = response.url;
                $scope.image1File = null;
            } else if (angular.equals(imageId, 'Image2')) {
                $scope.campaignDetail.image2 = response.url;
                $scope.image2File = null;
            } else if (angular.equals(imageId, 'Image3')) {
                $scope.campaignDetail.image3 = response.url;
                $scope.image3File = null;
            }
            else if (angular.equals(imageId, 'CrmAppBanner')) {
                $scope.campaignDetail.crmAppBannerUrl = response.url;
                $scope.crmAppBannerFile = null;
            }
            else if (angular.equals(imageId, 'Preview URL Image')) {
                $scope.campaignDetail.utmImageUrl = response.url;
                $scope.previewUrlImageFile = null;
            }

        }).error(function (response) {
            $rootScope.showFullScreenLoader = false;
            alert("Error while uploading image" + response);
        });
    }

    $scope.openImagePreviewModal = function (imageSource) {
        $scope.previewImageSource = imageSource;
        if (imageSource == null) {
            alert("Image not found!");
        } else {
            $("#imagePreviewModal").modal("show");
        }
    };

    function validateEntries() {
        for (var obj in $scope.campaignDetail) {
            if (Object.prototype.hasOwnProperty.call($scope.campaignDetail, obj)) {
                if (typeof($scope.campaignDetail[obj]) === 'number' && $scope.campaignDetail[obj] < 0) {
                    alert($scope.entryMap.get(obj) + " should be valid!");
                    return false;
                }
                if ($scope.campaignDetail[obj] == null || (typeof($scope.campaignDetail[obj]) === 'string' && $scope.campaignDetail[obj].trim() === "")) {
                    // if (angular.equals(obj, 'heroBannerDesktop') || angular.equals(obj, 'heroBannerMobile')) {
                    //     if ($scope.campaignDetail['heroBannerDesktop'] || $scope.campaignDetail['heroBannerMobile']) {
                    //         continue;
                    //     }
                    // }
                    if ($scope.nonMandatoryFields.has(obj)) {
                        continue;
                    }

                    if($scope.checkCampaignStrategyForSlotGame() && $scope.nonMandatoryFieldsForSlotGame.has(obj)) {
                        continue;
                    }

                    if (!angular.equals($scope.campaignDetail.couponClone, 'Yes') && angular.equals(obj, 'couponPrefix')) {
                        continue;
                    }

                    if (angular.equals(obj, 'couponCode') || angular.equals(obj, 'couponCodeDesc')) { //removed these fields from ui
                        continue;
                    }
                    if (angular.equals(obj, 'newCustomerOnly') && ($scope.campaignDetail.campaignStrategy === $scope.NBO
                        || $scope.campaignDetail.campaignStrategy === $scope.DELIVERY_NBO || $scope.campaignDetail.campaignStrategy === $scope.CAFE_LAUNCH)) {
                        continue;
                    }
                    if (angular.equals(obj, 'couponPrefix') && $scope.checkCampaignStrategyForDelivery()) {
                        continue;
                    }
                    if((angular.equals(obj, 'launchUnitId') || angular.equals(obj, 'cafeLaunchDate')) && !$scope.checkCampaignStrategyForCafeLaunch()) {
                        continue;
                    }
                    alert($scope.entryMap.get(obj) + " cannot be empty!");
                    return false;
                }
                if (angular.equals(obj, 'primaryUrl') || angular.equals(obj, 'redirectionUrl')) {
                    if (!validateURL($scope.campaignDetail[obj])) {
                        alert($scope.entryMap.get(obj) + ": Please enter valid link!!");
                        return false;
                    }
                }
            }
        }
        if ($scope.campaignDetail.startDate >= $scope.campaignDetail.endDate) {
            alert("Start Date must be less than End date");
            return false;
        }
        return true;
    }

    function saveCampaignReach() {
        switch ($scope.campaignDetail.campaignReach) {
            case 1:
                $scope.campaignDetail.campaignReach = "SYSTEM_SPECIFIC";
                break;
            case 2:
                $scope.campaignDetail.campaignReach = "REGION_SPECIFIC";
                break;
            case 3:
                $scope.campaignDetail.campaignReach = "UNIT_SPECIFIC";
                break;
        }
    }

    $scope.checkNewAndLoyalTea = function (index) {
        if ($scope.campaignMappings[index].customerType === 'NEW' && $scope.campaignMappings[index].code === 'LOYAL_TEA') {
            return true;
        }
    };

    $scope.checkIfValidCoupon = function (index) {
        if ($scope.checkNewAndLoyalTea(index)) {
            return false;
        }
        return $scope.campaignMappings[index].isValid === 'Valid';
    };

    $scope.testCampaignNotification = function (index) {
        if ($scope.campaignMappings[index].customerType && $scope.campaignDetail.campaignStrategy && $scope.campaignMappings[index].journey && $scope.campaignMappings[index].code
            && $scope.campaignMappings[index].desc && $scope.campaignMappings[index].validityInDays && $scope.campaignDetail.couponClone) {
            if ($scope.testContactNumber != null && $scope.testContactNumber.toString().length === 10 && $scope.testCustomerName != null && $scope.testCustomerName.trim() !== "") {
                var reqObj = {};
                reqObj.customerType = $scope.campaignMappings[index].customerType;
                reqObj.strategy = $scope.campaignDetail.campaignStrategy;
                reqObj.journey = $scope.campaignMappings[index].journey;
                reqObj.sourceCoupon = $scope.campaignMappings[index].code;
                reqObj.couponDescription = $scope.campaignMappings[index].desc;
                reqObj.contactNumber = $scope.testContactNumber;
                reqObj.firstName = $scope.testCustomerName;
                reqObj.validityInDays = $scope.campaignMappings[index].validityInDays;
                if (angular.equals($scope.campaignDetail.couponClone, 'Yes')) {
                    reqObj.couponClone = true;
                } else {
                    reqObj.couponClone = false;
                }
                reqObj.prefix = $scope.campaignDetail.couponPrefix;
                reqObj.notificationType = "SMS";
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.orderManagement.testCampaignNotification,
                    data: reqObj
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log(response);
                    if (response.data !== true) {
                        alert("Error sending Notification!");
                    }
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error: " + response);
                });

            } else {
                alert("Please enter valid Contact Number and Name");
            }
        } else {
            alert("Please fill valid Campaign Details before testing");
        }
    };

    $scope.setPreviousJourneysRequired = function (index) {
        var customerType = $scope.campaignMappings[index].customerType;
        if ($scope.campaignMappings[index].isRequired === true) {
            for (var i = 0; i < $scope.campaignMappings.length; i++) {
                if ($scope.campaignMappings[i].customerType === customerType && $scope.campaignMappings[i].journey < $scope.campaignMappings[index].journey) {
                    $scope.campaignMappings[i].isRequired = true;
                }
            }
        } else {
            for (var i = 0; i < $scope.campaignMappings.length; i++) {
                if ($scope.campaignMappings[i].customerType === customerType && $scope.campaignMappings[i].journey > $scope.campaignMappings[index].journey) {
                    $scope.campaignMappings[i].isRequired = false;
                }
            }
        }
    };

    function parseCampaignMapping() {
        var mappings = {};
        for (var i in $scope.customerTypeDetail) {
            var customerType = $scope.customerTypeDetail[i].customerTypes;
            var journeyMap = {};
            for (var j in $scope.campaignMappings) {
                if (angular.equals(customerType, $scope.campaignMappings[j].customerType) && angular.equals($scope.campaignMappings[j].isRequired, true)) {
                    journeyMap[$scope.campaignMappings[j].journey] = $scope.campaignMappings[j];
                }
            }
            mappings[customerType] = journeyMap;
        }
        $scope.campaignDetail.mappings = mappings;
        console.log(JSON.stringify($scope.campaignDetail.mappings));
    }

    function validateCampaignMapping() {
        var count = 0;
        for (var map in $scope.campaignDetail.mappings) {
            for (var journey in $scope.campaignDetail.mappings[map]) {
                count += 1;
                var campaignMap = $scope.campaignDetail.mappings[map][journey];
                if (campaignMap.code == null || campaignMap.code.trim() === "") {
                    alert("Coupon code cannot be empty");
                    return false;
                }
                if (campaignMap.desc == null || campaignMap.desc.trim() === "" || campaignMap.desc.length > 65) {
                    alert("Please enter Campaign description (max size: 65)");
                    return false;
                }
                if (campaignMap.validityInDays == null) {
                    alert("Please enter valid Coupon Validity in Days");
                    return false;
                }
                if (campaignMap.reminderDays == null) {
                    alert("Please enter valid Coupon Reminder Days");
                    return false;
                }
                if (campaignMap.validityInDays <= campaignMap.reminderDays) {
                    alert("Coupon Validity must be greater than Reminder days");
                    return false;
                }

                if (!angular.equals(campaignMap.isValid, 'Valid')) {
                    if (angular.equals(campaignMap.customerType, "NEW") && angular.equals(campaignMap.code, "LOYAL_TEA")) {
                        continue;
                    }
                    alert("Please validate all coupons");
                    return false;
                }
            }
        }
        console.log("Coupon count " + count);
        if (count < 1) {
            alert("Please mark required coupons!");
            return false;
        }
        return true;
    }

    $scope.submitCampaignForm = function () {

        $scope.setRegionListToString();
        $scope.setCityListToString();
        $scope.setUnitListToString();
        parseCampaignMapping();
        console.log($scope.campaignDetail);
        if ($scope.isEditCampaignPage) {
            return updateCampaign();
        }
        else {
            if (angular.equals(validateEntries(), true) && angular.equals(validateCampaignMapping(), true)) {
                if (confirm("Do you want to save the Campaign?")) {
                    saveCampaignReach();
                    if (angular.equals($scope.campaignDetail.couponClone, 'Yes') || angular.equals($scope.campaignDetail.couponClone, true)) {
                        $scope.campaignDetail.couponClone = true;
                    } else {
                        $scope.campaignDetail.couponClone = false;
                    }
                    if ($scope.campaignDetail.newCustomerOnly != null) {
                        $scope.campaignDetail.newCustomerOnly = $scope.campaignDetail.newCustomerOnly.substring(0, 1);
                    }
                    $scope.campaignDetail.campaignStatus = "ACTIVE";
                    var reqObj = $scope.campaignDetail;
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.addCampaignDetail,
                        data: reqObj
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log(response);
                        if (response.status == 200 && response.data != null && typeof(response.data) === 'object') {
                            alert("Added campaign successfully!"/* \nLong url: " + response.data.longUrl*/);
                            //dialog("Added campaign successfully! \nLong url: " + response.data.longUrl +"<br/><em class='fa fa-copy' </em>");
                            $scope.selectedCampaign = response.data;
                            $scope.selectedCampaignLongUrl = response.data.longUrl;
                            $scope.selectedCampaignShortUrl = response.data.shortUrl;
                            $("#addShortUrl").modal("show");
                        } else {
                            alert("Error while adding campaign");
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("error: " + response);
                    });
                }
            }
        }
    };

    function updateCampaign() {
        if (angular.equals(validateEntries(), true) && angular.equals(validateCampaignMapping(), true)) {
            if (confirm("Do you want to save the edited Campaign(Note: Short URL will get changed)?")) {
                saveCampaignReach();
                if (angular.equals($scope.campaignDetail.couponClone, 'Yes') || angular.equals($scope.campaignDetail.couponClone, true)) {
                    $scope.campaignDetail.couponClone = true;
                } else {
                    $scope.campaignDetail.couponClone = false;
                }
                if ($scope.campaignDetail.newCustomerOnly != null) {
                    $scope.campaignDetail.newCustomerOnly = $scope.campaignDetail.newCustomerOnly.substring(0, 1);
                }
                var reqObj = $scope.campaignDetail;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.updateCampaignDetail,
                    data: reqObj
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log(response);
                    if (response.status == 200 && response.data != null && typeof(response.data) === 'object') {
                        alert("Edited campaign successfully!"/* \nLong url: " + response.data.longUrl*/);
                        //dialog("Added campaign successfully! \nLong url: " + response.data.longUrl +"<br/><em class='fa fa-copy' </em>");
                        $scope.isEditCampaignPage = true;
                        $scope.selectedCampaign = response.data;
                        $scope.selectedCampaignLongUrl = response.data.longUrl;
                        $scope.selectedCampaignShortUrl = response.data.shortUrl;
                        $("#addShortUrl").modal("show");
                        // window.location.reload();
                    } else {
                        alert("Error while updating campaign");
                    }
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error: " + response);
                });
            }
        }
    }

    $scope.clearCampaignCache = function () {
        var url = AppUtil.restUrls.campaignCache.clearCampaignCache;
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: url
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            console.log(JSON.stringify(response));
            window.location.reload();
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            alert("error:" + JSON.stringify(response));
        });
    };

    $("#addShortUrl").on("hidden.bs.modal", function () {
        if ($scope.isEditCampaignPage) {
            $scope.clearCampaignCache();
        } else {
            window.location.reload();
        }
    });

    function validateURL(string) {
        if (string === null) {
            return false;
        }
        var res = string.match(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g);
        console.log(res);
        return (res !== null)
    }

    $scope.createShortUrl = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.createCampaignShortUrl,
            params: {
                longUrl: $scope.selectedCampaignLongUrl
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status == 200) {
                $scope.selectedCampaignShortUrl = response.data;
            }
            console.log(response);
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });
    };

    $scope.saveShortUrl = function () {

        console.log($scope.selectedCampaignShortUrl);
        if (!validateURL($scope.selectedCampaignShortUrl)) {
            alert("Please enter valid link!!");
            return;
        }

        $scope.selectedCampaign.longUrl = $scope.selectedCampaignLongUrl;
        $scope.selectedCampaign.shortUrl = $scope.selectedCampaignShortUrl;
        var reqObj = $scope.selectedCampaign;
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.offerManagement.updateCampaignShortUrl,
            data: reqObj
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status == 200) {
                console.log(response);
                $("#addShortUrl").modal("hide");
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });

    };

    function fillInfoMap() {
        $scope.fieldInfoMap.set('primaryUrl', 'This is the base url where all campaigns will be displayed\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('campaignStrategy', 'type of strategy of campaign on basis of which customer type and journey fields will be decided');
        $scope.fieldInfoMap.set('campaignSource', 'source where campaign will be published\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('campaignMedium', 'medium through which campaign will be seen\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('campaignName', 'unique name to identify campaign');
        $scope.fieldInfoMap.set('campaignCategory', '');
        $scope.fieldInfoMap.set('campaignDesc', 'brief description of campaign');
        $scope.fieldInfoMap.set('campaignReach', "how campaign will be distributed:\n\tSystem Specific: For all regions\n\tRegion Specific: For particular regions\n\tUnit Specific: for particular units within region(s)");
        $scope.fieldInfoMap.set('usageLimit', 'usage per coupon per user');
        $scope.fieldInfoMap.set('startDate', 'start date for campaign');
        $scope.fieldInfoMap.set('endDate', 'end date for campaign');
        $scope.fieldInfoMap.set('landingPageDesc', 'Landing Page Description\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('smsTemplate', 'SMS Template and ID to be mentioned');
        $scope.fieldInfoMap.set('whatsappTemplate', 'Whatsapp Template Id and Message to be mentioned');
        $scope.fieldInfoMap.set('smsReminder', 'Reminder SMS Template and ID to be mentioned');
        $scope.fieldInfoMap.set('whatsappReminder', 'Reminder Whatsapp Template Id and Message to be mentioned');
        $scope.fieldInfoMap.set('reminderDayGap', 'Reminder Messages day gap');
        $scope.fieldInfoMap.set('utmHeading', 'Preview Heading of campaign\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('utmDesc', 'Preview Description of campaign\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('utmImageUrl', 'Image to be shown with Preview URL\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('redirectionUrl', 'Redirecting URL to mentioned otherwise Default URL will be used');
        $scope.fieldInfoMap.set('newCustomerOnly', 'whether for new customer only');
        $scope.fieldInfoMap.set('linkedCampaignId', 'to decide whether a particular Delivery Campaign will be linked to already existing Dine-in Campaign');
        $scope.fieldInfoMap.set('couponApplicableAfter', 'to decide after how many days coupon will be applicable');
        $scope.fieldInfoMap.set('image1', 'First image shown in Landing Page\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('image2', 'Second image shown in Landing Page\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('image3', 'Third  image shown in Landing Page\n\t\t\t(Visible to Customer)');
        $scope.fieldInfoMap.set('crmAppBannerUrl', 'CRM App Banner Image');


        $scope.fieldInfoExampleMap.set('primaryUrl', 'https://cafes.chaayos.com/myoffer');
        $scope.fieldInfoExampleMap.set('campaignStrategy', 'NBO, General');
        $scope.fieldInfoExampleMap.set('campaignSource', 'NEWSPAPER_QR_CODE, FACEBOOK');
        $scope.fieldInfoExampleMap.set('campaignMedium', 'SEARCH, DISPLAY');
        $scope.fieldInfoExampleMap.set('campaignName', 'Bangalore wfc sign up, Dine-in new customer');
        $scope.fieldInfoExampleMap.set('campaignCategory', 'CLM, Marketing Offline');
        $scope.fieldInfoExampleMap.set('campaignDesc', '');
        $scope.fieldInfoExampleMap.set('campaignReach', '');
        $scope.fieldInfoExampleMap.set('usageLimit', '');
        $scope.fieldInfoExampleMap.set('startDate', '');
        $scope.fieldInfoExampleMap.set('endDate', '');
        $scope.fieldInfoExampleMap.set('landingPageDesc', '');
        $scope.fieldInfoExampleMap.set('smsTemplate', '');
        $scope.fieldInfoExampleMap.set('whatsappTemplate', '');
        $scope.fieldInfoExampleMap.set('smsReminder', '');
        $scope.fieldInfoExampleMap.set('whatsappReminder', '');
        $scope.fieldInfoExampleMap.set('reminderDayGap', '');
        $scope.fieldInfoExampleMap.set('utmHeading', '');
        $scope.fieldInfoExampleMap.set('utmDesc', '');
        $scope.fieldInfoExampleMap.set('utmImageUrl', '');
        $scope.fieldInfoExampleMap.set('redirectionUrl', 'https://cafes.chaayos.com/');
        $scope.fieldInfoExampleMap.set('newCustomerOnly', 'Yes/No');
        $scope.fieldInfoExampleMap.set('linkedCampaignId', 'Campaign Id - Campaign Name');
        $scope.fieldInfoExampleMap.set('couponApplicableAfter', '\n\t0: to allow using coupon from today itself\n\t2: to allow using campaign after 2 days');
        $scope.fieldInfoExampleMap.set('image1', '');
        $scope.fieldInfoExampleMap.set('image2', '');
        $scope.fieldInfoExampleMap.set('image3', '');
        $scope.fieldInfoExampleMap.set('crmAppBannerUrl', '');
    }

    $scope.showInfo = function (fieldObjName) {
        $scope.infoDialog = bootbox.dialog({
            title: $scope.entryMap.get(fieldObjName),
            message: '<p style="white-space: pre-wrap;">Description: ' + $scope.fieldInfoMap.get(fieldObjName) + '</p>' + '<p style="white-space: pre-wrap;">Example: ' + $scope.fieldInfoExampleMap.get(fieldObjName) + '</p>'

        });
    };

});