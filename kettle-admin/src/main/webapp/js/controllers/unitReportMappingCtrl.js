adminapp.controller("unitReportMappingCtrl",function( $rootScope,$scope, $location, $http, AppUtil) {

            $scope.multipleEmailRegEx= /^((([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))(,(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/;

               $scope.init = function(){

                       $scope.isrevenueCertificate=["YES","NO"];
                       $scope.isFunctional = null;
                       $scope.selectedCafe = null;
                       $scope.revenueCertificateEmail = null;
                       $rootScope.showFullScreenLoader = true;
                       			$http({
                       				  method: 'GET',
                       				  url: AppUtil.restUrls.unitMetaData.allUnits+'?category=ALL'
                       				}).then(function success(response) {
                       					$rootScope.showFullScreenLoader = false;
                       					$scope.cafelist = response.data;
                       				}, function error(response) {
                       					$rootScope.showFullScreenLoader = false;
                       					  console.log("error:"+response);
                       				});
                     };

                     $scope.submitCertificateUnitMapping =function(){
                             $rootScope.showFullScreenLoader = true;
                             $http({
                                 method: 'POST',
                                 url: AppUtil.restUrls.reportMetaData.submitUnitCertificateMapping,
                                 params: {
                                  unitId:$scope.selectedCafe.id,
                                  toBeGenerated: $scope.isFunctional,
                                  emailId: $scope.revenueCertificateEmail
                                  },
                             }).then(function success(response) {
                                 $rootScope.showFullScreenLoader = false;
                                 if (response.status === 200 && response.data) {
                                     alert("Unit mapped successfully");
                                  window.location.reload();
                                 }
                             }, function error(response) {
                                 $rootScope.showFullScreenLoader = false;
                                 console.log("error:" + response);
                                 alert("Unable to map unit");
                             });
                     }

                     $scope.getmappings = function(selectedCafe){
                          $http({
                            method: 'GET',
                            url: AppUtil.restUrls.reportMetaData.getUnitCertificateMapping,
                             params:
                              {unitId : $scope.selectedCafe.id
                              }
                             }).success(function (response) {
                             console.log(response);
                               if (response!=null && response) {
                                     $scope.isFunctional = response.toBeGenerated;
                                     $scope.revenueCertificateEmail = response.emailId;
                                                              }
                          }).error(function (response) {
                           alert("Unable to get data");
                           console.log(response);
                            });
                                 };
                     }

						);