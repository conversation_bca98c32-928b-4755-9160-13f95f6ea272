/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("monkRecipeBuilder", function($rootScope, $scope, $location, $http, AppUtil, $cookieStore,
	$uibModal, $window, $state) {

    $scope.productsInfo = null;

    $scope.clearAll = function(askForConfirmation) {
	if (askForConfirmation) {
	    var result = confirm("Are You Sure You Want To Clear Everything?");
	    if (!result) {
		return;
	    }
	}
	$scope.activeTab = 0;
	// Metadata Information
	$scope.todaysDate = new Date().toString();
	$scope.selectedProductId = null;
	$scope.selectedProduct = null;
	$scope.existingRecipes = null;
	$scope.selectedDimensionData = null;
	$scope.selectedDimensionProfile = null;
    };

    $scope.init = function() {
	$scope.getAllDimensions();
	$scope.getAllProducts();
	$scope.clearAll(false);
    };

    $scope.filterByHotBeverages = function(product) {
	return product.type == 5;
    };

    function createRecipeDetail() {
	var recipe = {
	    status : 'ACTIVE',
	    datas : []
	};
	return recipe;
    }

    $scope.selectOptions = {
	width : '100%'
    };

    $scope.setNextTab = function(tabNo) {
	$scope.activeTab = tabNo + 1;
    };

    $scope.setPrevTab = function(tabNo) {
	if (tabNo == 1) {
	    var result = confirm("This will wipe out all changes. Are you sure?");
	    if (!result) {
		return;
	    }
	}
	$scope.activeTab = tabNo - 1;
    };

    $scope.selectRecipeForEdit = function(recipe) {
	recipe.startDate = moment(recipe.startDate).format('YYYY-MM-DD');
	$scope.recipeDetail = recipe;
	$scope.setNextTab(0);
    };

    $scope.removeFromExistingRecipes = function(existingRecipes, recipe) {
	if (existingRecipes == undefined || existingRecipes == null) {
	    return null;
	}
	var i;
	for (i = 0; i < existingRecipes.length; i++) {
	    if (existingRecipes[i].recipeId == recipe.recipeId) {
		existingRecipes.splice(i, 1);
		break;
	    }
	}
	return existingRecipes;
    }

    $scope.removeRecipe = function(recipe) {

    var data=AppUtil.getUserValues();
	if (data != undefined && data != null) {
	    recipe.lastUpdatedByName = data.user.name;
	    recipe.lastUpdatedById = data.user.id;
	}
	if (confirm("Are you sure, you want to delete this recipe?\n This Step cannot be undone")) {
	    $rootScope.showFullScreenLoader = true;
	    $http({
		method : 'POST',
		url : AppUtil.restUrls.monkRecipeManagement.removeRecipe,
		data : recipe
	    }).then(function success(response) {
		$scope.existingRecipes = $scope.removeFromExistingRecipes($scope.existingRecipes, recipe);
		$rootScope.showFullScreenLoader = false;
	    }, function error(response) {
		console.log("error:" + response);
		alert("Unable to remove recipe");
		$rootScope.showFullScreenLoader = false;
	    });
	}
    };

    $scope.findAllRecipes = function(productId, dimensionId, callback) {
	$rootScope.showFullScreenLoader = true;
	console.log(productId);
	console.log(dimensionId);
	$http(
		{
		    method : 'GET',
		    url : AppUtil.restUrls.monkRecipeManagement.findRecipe + '?productId=' + productId
			    + '&dimensionId=' + dimensionId
		}).then(function success(response) {
	    console.log('Found Recipes', response.data);
	    callback(response.data);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    $rootScope.showFullScreenLoader = false;
	});
    };

    // Tab 1 utility methods
    $scope.getAllProducts = function() {
	$http({
	    method : 'POST',
	    url : AppUtil.restUrls.productMetaData.products
	}).then(function success(response) {
	    $scope.productsInfo = response.data;
	}, function error(response) {
	    console.log("error:" + response);
	});
    };

    $scope.getAllDimensions = function() {
	// console.log($scope.prodListData);
	$http({
	    method : 'GET',
	    url : AppUtil.restUrls.unitMetaData.listTypes
	}).then(function success(response) {
	    $scope.categoryLists = response.data;
	    $scope.productCategory = $scope.categoryLists.CATEGORY;
	    $scope.productDimension = $scope.categoryLists.DIMENSION;
	    $scope.optionsList = $scope.categoryLists.OPTIONS;
	});
    };

    $scope.selectMonkProduct = function(selectedProductId) {
	var p = getDetail(selectedProductId, $scope.productsInfo);
	$scope.selectedProduct = p;
	$scope.recipeDetail = createRecipeDetail();
	$scope.selectedDimensionProfile = getDimensionDetail($scope.selectedProduct.dimensionProfileId,
		$scope.productDimension);
	$scope.isComboProduct = $scope.selectedProduct.type == 8;
	if ($scope.selectedDimensionData != null) {
	    $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function(value) {
		$scope.existingRecipes = value;
	    });
	}
    };

    $scope.selectDimension = function(dimensionId) {
	if ($scope.selectedDimensionProfile == null) {
	    return;
	}
	$scope.selectedDimensionData = createDimensionDetail(getDetail(dimensionId,
		$scope.selectedDimensionProfile.content));
	$scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function(value) {
	    $scope.existingRecipes = value;
	});
    }

    $scope.addNewRecipe = function() {
	$scope.recipeDetail = createRecipeDetail();
	$scope.createNewRecipeData($scope.selectedProduct, $scope.selectedDimensionData);
	$scope.setNextTab(0);
    };

    $scope.createNewRecipeData = function(product, dimension) {
	$scope.recipeDetail.product = createProductBasicDetail(product);
	$scope.recipeDetail.dimension = dimension;
	$scope.recipeDetail.preparation = product.preparation;
	$scope.recipeDetail.name = $scope.recipeDetail.product.name;
	if ($scope.recipeDetail.dimension.name != 'None') {
	    $scope.recipeDetail.name = $scope.recipeDetail.name + ' ' + $scope.recipeDetail.dimension.name
	}
	$scope.recipeDetail.name = $scope.recipeDetail.name + ' Monk Recipe';
	$scope.recipeDetail.datas = []
	var quantity = $scope.getMaxQuantity($scope.recipeDetail.dimension.code);
	for (var i = 1; i <= quantity; i++) {
	    $scope.recipeDetail.datas.push($scope.createRecipeData($scope.recipeDetail.preparation, i));
	}

    }
    $scope.createRecipeData = function(p, q) {
	return {
	    quantity : q,
	    prep : p == "STEWING" ? "S" : "T",
	    water : 0,
	    milk : 0,
	    boilSettle : 0,
	    noOfBoils : 0,
	    heatingTimeMins : 0,
	    heatingTimeSecs : 0
	};

    }

    $scope.getMaxQuantity = function(dim) {
	if (dim == "None" || dim == "Regular") {
	    return 7;
	}
	if (dim == "Full") {
	    return 4;
	}

	if (dim == "ChotiKetli") {
	    return 3;
	}
	if (dim == "BadiKetli") {
	    return 1;
	}

	return 1;
    }

    $scope.selectMenuProductDimension = function(dimensionId) {
	console.log(dimensionId);
	$scope.selectedMenuProduct.dimension = getDetail(dimensionId,
		$scope.selectedMenuProductDimensionProfile.content);
	console.log($scope.selectedMenuProduct);

    };

    function createProductBasicDetail(product) {
	var data = {};
	if (product == null) {
	    return data;
	}
	data.productId = product.id;
	data.name = product.name;
	data.type = product.type;
	data.subType = product.subType;
	data.shortCode = product.shortCode;
	data.classification = product.classification;
	data.taxCode = product.taxCode;
	return data;
    }

    function createDimensionDetail(dimension) {
	var data = {};
	data.infoId = dimension.id;
	data.name = dimension.name;
	data.code = dimension.code;
	data.shortCode = dimension.shortCode;
	data.type = dimension.type;
	data.status = dimension.status;
	return data;
    }

    $scope.removeElement = function(list, index) {
	list.splice(index, 1);
    };

    $scope.showPreview = function() {
	$("#showPreviewModal").modal("show");
    };
    $scope.saveRecipe = function() {

    var data=AppUtil.getUserValues();
	if (data != undefined && data != null) {
	    $scope.recipeDetail.lastUpdatedByName = data.user.name;
	    $scope.recipeDetail.lastUpdatedById = data.user.id;
	}
	$rootScope.showFullScreenLoader = true;
	var url = null;
	if ($scope.recipeDetail.recipeId != null && $scope.recipeDetail.recipeId > 0) {
	    url = AppUtil.restUrls.monkRecipeManagement.updateRecipe;
	} else {
	    url = AppUtil.restUrls.monkRecipeManagement.addRecipe;
	}
	$http({
	    method : 'POST',
	    url : url,
	    data : $scope.recipeDetail
	}).then(function success(response) {
	    alert("Recipe Added Successfully");
	    $scope.clearAll(false);
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    $rootScope.showFullScreenLoader = false;
	});
	$("#showPreviewModal").modal("hide");
    };
    $scope.recipeByStatus = function(status){
	$rootScope.showFullScreenLoader = true;
	console.log(status);
	$http(
		{
		    method : 'GET',
		    url : AppUtil.restUrls.monkRecipeManagement.findRecipeVersionByStatus + '?status=' + status
		}).then(function success(response) {
	    console.log('Recipes Version Data', response.data);
	    $scope.selectedRecipe = response.data;
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    $rootScope.showFullScreenLoader = false;
	});
    }
    
    $scope.activateSelected =function(){
	if($scope.selectedRecipe != null){
	    $http(
			{
			    method : 'POST',
			    url : AppUtil.restUrls.monkRecipeManagement.activateCurrentCreatedVersion,
			    data : $scope.getUserData()
			}).then(function success(response) {
		    console.log('Recipes Version Data', response.data);
		    alert("Recipe Activated Successfully");
		    $scope.recipeByStatus('ACTIVATED');
		    $rootScope.showFullScreenLoader = false;
		}, function error(response) {
		    console.log("error:" + response);
		    $rootScope.showFullScreenLoader = false;
		});
	}
    }
    $scope.getUserData = function(){
	var userData = {
	    employeeId : 0,
	    employeeName : null
	}

    var data=AppUtil.getUserValues();
        if (data != undefined && data != null) {
            userData.employeeName = data.user.name;
            userData.employeeId = data.user.id;
        }
        return userData;
    }
    $scope.createNewVersion =function(){
	if($scope.selectedRecipe != null){
	    $http(
			{
			    method : 'POST',
			    url : AppUtil.restUrls.monkRecipeManagement.createNewVersion,
			    data : $scope.getUserData()
			}).then(function success(response) {
		    console.log('Recipes Version Data', response.data);
		    alert("Recipe Version Created Successfully");
		    $scope.recipeByStatus('CREATED');
		    $rootScope.showFullScreenLoader = false;
		}, function error(response) {
		    console.log("error:" + response);
		    $rootScope.showFullScreenLoader = false;
		});
	}
    }
    // Generic Function
    function getDetail(id, list) {
	for ( var index in list) {
	    if (list[index].id == id) {
		return list[index];
	    }
	}
	return null;
    }

    function getDimensionDetail(id, list) {
	for ( var index in list) {
	    if (list[index].detail.id == id) {
		return list[index];
	    }
	}
	return null;
    }
});
