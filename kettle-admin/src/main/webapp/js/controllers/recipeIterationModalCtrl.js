
adminapp.controller('recipeIterationCostCtrl',function ($rootScope,$scope,AppUtil,$http,iterationDetails,Popeye,$cookieStore) {
	//console.log("iterationDetails  closeModal",iterationDetails);
	$scope.iterationDetails=iterationDetails;
	$scope.unitListSCM=[];
	var productIdList=[];
	$scope.priceDetailsMap = null;
	$scope.priceMap={};
	$scope.recipeCost=0;
	var subUomNameMap = {
			"GM" : {"conversion" : "1000" ,"parent" : "KG" },
			"PC" : {"conversion" : "1" ,"parent" : "PC" },
			"ML" : { "conversion" : "1000" ,"parent" : "L" },
			"SACHET" : { "conversion" : "1" ,"parent" : "SACHET" },
			"PKT" : { "conversion" : "1" ,"parent" : "PKT" }};
	$scope.closeModal =function (){
		Popeye.closeCurrentModal();
	};

	$scope.getAllRegions = function() {
		$http({
			method: 'GET',
			url: AppUtil.restUrls.unitMetaData.regions
		}).then(function success(response) {
			$scope.allRegions = response.data;
			$scope.setSelectedRegion("NCR");
		}, function error(response) {
			console.log("error:" + response);
			$scope.allRegions = [];
			$scope.setSelectedRegion(null);
		});
	};

	$scope.setSelectedRegion = function (region) {
		$scope.selectedRegion = region;
		if (region != null) {
			getPriceDetails();
		}
	};
	
	function init(){
		// getUnitList();
		getProductIdList();
		$scope.setSelectedRegion(null);
		$scope.allRegions = [];
		$scope.getAllRegions();
	}
	
	init();
	
	$scope.selectedUnit="Please select Unit";
	
	$scope.changeUnit = function(selectedUnit){
		$scope.selectedUnit=selectedUnit;
		getPriceDetails();
	};
	
	function getProductIdList(){
		$scope.iterationDetails.components.forEach(function(ingredient){
			if (productIdList.indexOf(ingredient.productId === -1)) {
				productIdList.push(ingredient.productId);
			}
		});
	}

	function getUnitList(){
		$http({
			method: 'GET',
			url: AppUtil.restUrls.unitMetaData.allUnitsList
		}).then(function success(response) {
			if (response.data != null) {
				var unitList = response.data;
			//	console.log("unitList:" , unitList);
				unitList.forEach(function (unit) {
					if(unit.category == 'KITCHEN' || unit.category == 'WAREHOUSE'){
						$scope.unitListSCM.push(unit);
					}
				});
				console.log("$scope.unitListSCM:" , $scope.unitListSCM);
			}
		}, function error(response) {
			console.log("error:" + response);
		});
	};
	
	var getPriceDetails=function(){
		$scope.recipeCost=0;
		$rootScope.showFullScreenLoader = true;
		$http({
			method: "GET",
			url: AppUtil.restUrls.scmPriceManagement.scmRecipeProductCost,
			params: {
				"region": $scope.selectedRegion,
				"productIdList" : productIdList
			}
		}).then(function success(response) {
			$rootScope.showFullScreenLoader = false;
			console.log("response:" , response);
			$scope.priceDetailsMap = {};
			$scope.priceMap = response.data;
			$scope.iterationDetails.components.forEach(function(ingredient){
				var price = getPriceForUom($scope.priceMap[ingredient.productId].cost,ingredient.uom,ingredient.quantityPerSubUom);
				$scope.priceDetailsMap[ingredient.productId] =  price;
				var copyPrice = price == '-' ? 0 : price;
				$scope.recipeCost = parseFloat($scope.recipeCost) + parseFloat(copyPrice);
				$scope.recipeCost = toFixed($scope.recipeCost,3);
			});
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			alert("Something went wrong. Please try again!");
			console.log("error:" + response);
		});
	};
	
	function toFixed(num,digits){
		if(digits){
			return Number(num).toFixed(digits); 
		}
		return Number(num).toFixed(3); 
	}
	
	function getPriceForUom(price,subUom,qunatity){
		if (price == null) {
			return '-';
		}
		var cost= toFixed((price/subUomNameMap[subUom].conversion)*qunatity);
		return cost;
	}
});

