adminapp
    .controller(
        "ManageB2BMonkCustomersCtrl", ['$rootScope', '$scope', '$http', 'AppUtil', '$cookieStore', '$timeout',
        function ($rootScope, $scope, $http, AppUtil, $cookieStorem, $timeout) {

            $scope.init = function () {
                $scope.customerTypeList = ['POSTPAID'];
                $scope.userRoleList = ['Admin', 'User'];
                $scope.statusList = ['ACTIVE', 'IN_ACTIVE'];

                // $scope.actionList = ['MANAGE CUSTOMERS', 'MANAGE USERS'];
                // $scope.selectedAction = "MANAGE CUSTOMERS";

                $scope.customerPreviewEnabled = false;
                $scope.customerViewMode = null;

                $scope.userPreviewEnabled = false;
                $scope.userViewMode = null;

                clearCustomerObj();
                clearUserObject();
                $scope.cust = {};
            };

            function clearCustomerObj() {
                $scope.customerDetail = {};
                $scope.customerDetail.id = null;
                $scope.customerDetail.customerId = null;
                $scope.customerDetail.customerName = null;
                $scope.customerDetail.customerType = null;

                $scope.customerDetail.address = {};
                $scope.customerDetail.address.line1 = null;
                $scope.customerDetail.address.line2 = null;
                $scope.customerDetail.address.line3 = null;
                $scope.customerDetail.address.city = null;
                $scope.customerDetail.address.state = null;
                $scope.customerDetail.address.zipCode = null;
                $scope.customerDetail.address.country = null;

                $scope.customerDetail.primaryContact = {};
                $scope.customerDetail.primaryContact.name = null;
                $scope.customerDetail.primaryContact.email = null;
                $scope.customerDetail.primaryContact.contact = null;
                $scope.customerDetail.primaryContact.alternateContact = null;

                $scope.customerDetail.secondaryContact = {};
                $scope.customerDetail.secondaryContact.name = null;
                $scope.customerDetail.secondaryContact.email = null;
                $scope.customerDetail.secondaryContact.contact = null;
                $scope.customerDetail.secondaryContact.alternateContact = null;
            }

            function clearUserObject() {
                $scope.userDetail = {};
                $scope.userDetail.id = null;
                $scope.userDetail.userName = null;
                $scope.userDetail.userRole = null;
                $scope.userDetail.appConfig = null;
                $scope.userDetail.machineId = null;
                $scope.userDetail.status = null;
                $scope.userDetail.customerId = null;
                $scope.userDetail.machineSerialNo = null;
                $scope.userDetail.menuVersion = null;
                $scope.userDetail.password = null;

                $scope.userDetail.machineLocation = {};
                $scope.userDetail.machineLocation.line1 = null;
                $scope.userDetail.machineLocation.line2 = null;
                $scope.userDetail.machineLocation.line3 = null;
                $scope.userDetail.machineLocation.city = null;
                $scope.userDetail.machineLocation.state = null;
                $scope.userDetail.machineLocation.zipCode = null;
                $scope.userDetail.machineLocation.country = null;

                $scope.userPassword = null;
                $scope.userReEnterPassword = null;
            }

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                switch (action) {
                    case "MANAGE CUSTOMERS":
                        clearScreen();
                        break;
                    case "MANAGE USERS":
                        clearScreen();
                        break;
                }
            };

            function clearScreen() {
                clearCustomerObj();
                clearUserObject();
            }

            var fetchCustomers = function () {
                $scope.timerStarted = false;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.b2bMonkManagement.getCustomersByName,
                    params: {
                        customerName: $scope.cust.SearchBarInput
                    }
                }).then(function success(response) {
                    if (response.status == 200) {
                        $scope.customerDetailList = response.data;
                    }
                }, function error(response) {
                    console.log("error: " + response);
                });
            }

            $scope.getCustomers = function () {
                if (!$scope.timerStarted) {
                    $timeout(fetchCustomers, 1000);
                    $scope.timerStarted = true;
                }
            };

            $scope.fetchB2BUsers = function (customer) {
                $scope.previousSelCustomerId = $scope.selectedCustomerId;
                $scope.selectedCustomerId = customer.customerId;
                if ($scope.previousSelCustomerId != $scope.selectedCustomerId) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.b2bMonkManagement.getUsersByCustomer,
                        params: {
                            customerId: customer.customerId
                        }
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.status == 200) {
                            $scope.userDetailList = response.data;
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("error: " + response);
                    });
                } else {
                    $scope.selectedCustomerId = null;
                }
            };

            $scope.openB2BCustomerModal = function (customerDetail, mode, event) {
                $scope.customerPreviewEnabled = false;
                if (mode == 'Edit' || mode == 'Preview') {
                    $scope.customerDetail = customerDetail;
                    if (mode == 'Preview') {
                        $scope.customerPreviewEnabled = true;
                    }
                } else {
                    clearCustomerObj();
                }
                $scope.customerViewMode = mode;
                console.log($scope.customerDetail, mode);
                event.stopPropagation(); //to prevent click event for row
                $("#b2bCustomerModal").modal("show");
            };

            $scope.openB2BUserModal = function (userDetail, mode, event) {
                $scope.userPreviewEnabled = false;
                if (mode == 'Edit' || mode == 'Preview') {
                    $scope.userDetail = userDetail;
                    if (mode == 'Preview') {
                        $scope.userPreviewEnabled = true;
                    }
                } else {
                    clearUserObject();
                }
                $scope.userViewMode = mode;
                event.stopPropagation();
                $("#b2bUserModal").modal("show");
            };

            $scope.changeCustomerViewMode = function (mode) {
                if (mode == 'Edit') {
                    $scope.customerPreviewEnabled = false;
                    $scope.customerViewMode = mode;
                } else {
                    $scope.customerPreviewEnabled = true;
                    $scope.customerViewMode = mode;
                }
            };

            $scope.changeUserViewMode = function (mode) {
                if (mode == 'Edit') {
                    $scope.userPreviewEnabled = false;
                    $scope.userViewMode = mode;
                } else {
                    $scope.userPreviewEnabled = true;
                    $scope.userViewMode = mode;
                }
            };

            $scope.addB2BCustomer = function () {
                console.log("Adding Customer with details: ", $scope.customerDetail);
                if (validateCustomerObj()) {
                    var reqObj = $scope.customerDetail;
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.b2bMonkManagement.addCustomer,
                        data: reqObj
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Customer add response: ", response);
                        if (response.status == 200) {
                            alert("Added Customer Successfully!");
                            $("#b2bCustomerModal").modal("hide");
                            clearCustomerObj();
                        } else {
                            alert("Error adding Customer");
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Error while adding Customer: ", response);
                        alert("Error adding User");
                    });
                }
            };

            $scope.addB2BUser = function () {
                console.log("Adding User with details: ", $scope.userDetail);
                if (validateUserObj()) {
                    $scope.userDetail.password = $scope.userPassword;
                    var reqObj = $scope.userDetail;
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.b2bMonkManagement.addUser,
                        data: reqObj
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("User add response: ", response);
                        if (response.status == 200) {
                            alert("Added User Successfully!");
                            $("#b2bUserModal").modal("hide");
                            clearUserObject();
                        } else {
                            alert("Error adding User");
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Error while adding User: ", response);
                        alert("Error adding User");
                    });
                }
            };

            $scope.updateB2BCustomer = function () {
                console.log("Updating Customer with details: ", $scope.customerDetail);
                if (validateCustomerObj()) {
                    var reqObj = $scope.customerDetail;
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.b2bMonkManagement.updateCustomer,
                        data: reqObj
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Customer update response: ", response);
                        if (response.status == 200) {
                            alert("Updated Customer Successfully!");
                            $("#b2bCustomerModal").modal("hide");
                            clearCustomerObj();
                        } else {
                            alert("Error updating Customer");
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Error while updating Customer: ", response);
                        alert("Error updating User");
                    });
                }
            };

            $scope.updateB2BUser = function () {
                console.log("Updating User with details: ", $scope.userDetail);
                if (validateUserObj()) {
                    $scope.userDetail.password = $scope.userPassword;
                    var reqObj = $scope.userDetail;
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.b2bMonkManagement.updateUser,
                        data: reqObj
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("User add response: ", response);
                        if (response.status == 200) {
                            alert("Updated User Successfully!");
                            $("#b2bUserModal").modal("hide");
                            clearUserObject();
                        } else {
                            alert("Error updating User");
                        }
                    }, function error(response) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Error while updating User: ", response);
                        alert("Error updating User");
                    });
                }
            };

            function isUndefinedOrNull(val) {
                return angular.isUndefined(val) || val === null || val === "";
            };

            function validateMobileNumber(val) {
                var regex = /^([6-9])(\d{9})$/;
                var regexReturn = regex.test(val);
                return regexReturn;
            };
            function validateEmail(val) {
                var regex = /^\w+@[a-zA-Z_]+?\.[a-zA-Z]{2,3}$/;
                var regexReturn = regex.test(val);
                return regexReturn;
            };

            function validateCustomerObj() {
                if (isUndefinedOrNull($scope.customerDetail.customerId)) {
                    alert("Please enter Customer id");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.customerName)) {
                    alert("Please enter Customer name");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.customerType)) {
                    alert("Please enter Customer type");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.line1)) {
                    alert("Please enter Address Line 1");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.line2)) {
                    alert("Please enter Address Line 2");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.line3)) {
                    alert("Please enter Address Line 3");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.city)) {
                    alert("Please enter City");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.state)) {
                    alert("Please enter State");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.zipCode)) {
                    alert("Please enter Zip code correctly");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.address.country)) {
                    alert("Please enter Country");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.primaryContact.name)) {
                    alert("Please enter Primary Contact name");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.primaryContact.email) || !validateEmail($scope.customerDetail.primaryContact.email)) {
                    alert("Please enter valid Primary Contact email");
                    return false;
                }
                if ($scope.customerDetail.primaryContact.contact == null || !validateMobileNumber($scope.customerDetail.primaryContact.contact)) {
                    alert("Please enter valid Primary Contact number");
                    return false;
                }
                if ($scope.customerDetail.primaryContact.alternateContact != null && $scope.customerDetail.primaryContact.alternateContact.length > 0
                    && !validateMobileNumber($scope.customerDetail.primaryContact.alternateContact)) {
                    alert("Please enter valid Primary Alternate Contact number");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.secondaryContact.name)) {
                    alert("Please enter Secondary Contact name");
                    return false;
                }
                if (isUndefinedOrNull($scope.customerDetail.secondaryContact.email) || !validateEmail($scope.customerDetail.secondaryContact.email)) {
                    alert("Please enter valid Secondary Contact email");
                    return false;
                }
                if ($scope.customerDetail.secondaryContact.contact == null || !validateMobileNumber($scope.customerDetail.secondaryContact.contact)) {
                    alert("Please enter Valid Secondary Contact number");
                    return false;
                }
                if ($scope.customerDetail.secondaryContact.alternateContact != null && $scope.customerDetail.secondaryContact.alternateContact.length > 0
                    && !validateMobileNumber($scope.customerDetail.secondaryContact.alternateContact)) {
                    alert("Please enter valid Secondary Alternate Contact number");
                    return false;
                }
                return true;
            }

            function validateUserObj() {
                if (isUndefinedOrNull($scope.userDetail.userName)) {
                    alert("Please enter User name");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.userRole)) {
                    alert("Please enter User role");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.appConfig)) {
                    alert("Please enter App Config");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineId)) {
                    alert("Please enter Machine Id");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.status)) {
                    alert("Please enter Status");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.customerId)) {
                    alert("Please enter Customer Id");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineSerialNo)) {
                    alert("Please enter Machine Serial No.");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.menuVersion)) {
                    alert("Please enter Menu Version");
                    return false;
                }

                if (isUndefinedOrNull($scope.userDetail.machineLocation.line1)) {
                    alert("Please enter Address Line 1");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineLocation.line2)) {
                    alert("Please enter Address Line 2");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineLocation.line3)) {
                    alert("Please enter Address Line 3");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineLocation.city)) {
                    alert("Please enter City");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineLocation.state)) {
                    alert("Please enter State");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineLocation.zipCode)) {
                    alert("Please enter Zip code correctly");
                    return false;
                }
                if (isUndefinedOrNull($scope.userDetail.machineLocation.country)) {
                    alert("Please enter Country");
                    return false;
                }

                if (isUndefinedOrNull($scope.userPassword) || isUndefinedOrNull($scope.userReEnterPassword) ||
                    $scope.userPassword != $scope.userReEnterPassword) {
                    alert("Passwords don't match!");
                    return false;
                }
                return true;
            }

        }]);