adminapp.controller("checkListDataController", function($rootScope,$log,$timeout,$window,$scope,$http,$location,AppUtil){
	
	$scope.init = function () {
		 //  $scope.mandatoryTypeItems = [];
		   //$scope.domainList
	    }
	
	
	$scope.responseType 			= 	[{code:'text', name:'text'},{code:'Yes/No', name:'Yes/No'}];
	$scope.stationResultData		=	[{code:'HOT', name:'HOT'},{code:'COLD', name:'COLD'},{code:'FOOD', name:'FOOD'},{code:'FLOOR', name:'FLOOR'},{code:'POS', name:'POS'},{code:'ASSEMBLY', name:'ASSEMBLY'}];
	$scope.categoryCheckList		=	[{code:'OPERATIONAL', name:'OPERATIONAL'},{code:'PLANNER', name:'PLANNE<PERSON>'}];
	$scope.typeCheckList			=	[{code:'PERIODIC', name:'PERIO<PERSON><PERSON>'},{code:'ADHOC', name:'ADHOC'}];
	$scope.frequencyType 			= 	[{code:'Select Frequency', name:'Select Frequency'},{code:'OPENING', name:'OPENING'},{code:'CLOSING', name:'CLOSING'},{code:'SHIFT_HANDOVER', name:'SHIFT_HANDOVER'}, {code:'INTRA_DAY', name:'INTRA_DAY'},{code:'WEEKLY', name:'WEEKLY'},{code:'MONTHLY', name:'MONTHLY'}];
	$scope.weeklyDayType 			= 	[{code:'Select Days', name:'Select Days'},{code:'1', name:'Sunday'},{code:'2', name:'Monday'},{code:'3', name:'Tuesday'},{code:'4', name:'Wednesday'},{code:'5', name:'Thursday'},{code:'6', name:'Friday'},{code:'7', name:'Saturday'}];
	$scope.hoursType 				= 	[{code:'Select Time', name:'Select Time'}, {code:'1:00', name:'1:00'}, {code:'1:15', name:'1:15'}, {code:'1:30', name:'1:30'}, {code:'1:45', name:'1:45'},{code:'2:00', name:'2:00'}, {code:'2:15', name:'2:15'}, {code:'2:30', name:'2:30'}, {code:'2:45', name:'2:45'}, {code:'3:00', name:'3:00'},{code:'3:15', name:'3:15'},	 {code:'3:30', name:'3:30'},{code:'3:45', name:'3:45'}, {code:'4:00', name:'4:00'},{code:'4:15', name:'4:15'},{code:'4:30', name:'4:30'}, {code:'4:45', name:'4:45'},{code:'5:00', name:'5:00'},{code:'5:15', name:'5:15'}, {code:'5:30', name:'5:30'}, {code:'5:45', name:'5:45'},{code:'6:00', name:'6:00'},{code:'6:15', name:'6:15'}, {code:'6:30', name:'6:30'}, {code:'6:45', name:'6:45'},	 {code:'7:00', name:'7:00'},{code:'7:15', name:'7:15'}, {code:'7:30', name:'7:30'}, {code:'7:45', name:'7:45'}, {code:'8:00', name:'8:00'},{code:'8:15', name:'8:15'},{code:'8:30', name:'8:30'},{code:'8:45', name:'8:45'},{code:'9:00', name:'9:00'},{code:'9:15', name:'9:15'}, {code:'9:30', name:'9:30'},{code:'9:45', name:'9:45'},{code:'10:00', name:'10:00'}, {code:'10:15', name:'10:15'},{code:'10:30', name:'10:30'},{code:'10:45', name:'10:45'},{code:'11:00', name:'11:00'},{code:'11:15', name:'11:15'},{code:'11:30', name:'11:30'},{code:'11:45', name:'11:45'},{code:'12:00', name:'12:00'},{code:'12:15', name:'12:15'},{code:'12:30', name:'12:30'},{code:'12:45', name:'12:45'},{code:'13:00', name:'13:00'}, {code:'13:15', name:'13:15'},{code:'13:30', name:'13:30'},{code:'13:45', name:'13:45'}, {code:'14:00', name:'14:00'},{code:'14:15', name:'14:15'}, {code:'14:30', name:'14:30'}, {code:'14:45', name:'14:45'},{code:'15:00', name:'15:00'}, {code:'15:15', name:'15:15'}, {code:'15:30', name:'15:30'}, {code:'15:45', name:'15:45'},{code:'16:00', name:'16:00'}, {code:'16:15', name:'16:15'},{code:'16:30', name:'16:30'},{code:'16:45', name:'16:45'},{code:'17:00', name:'17:00'},{code:'17:15', name:'17:15'},{code:'17:30', name:'17:30'},{code:'17:45', name:'17:45'},{code:'18:00', name:'18:00'}, {code:'18:15', name:'18:15'}, {code:'18:30', name:'18:30'},{code:'18:45', name:'18:45'},{code:'19:00', name:'19:00'},{code:'19:15', name:'19:15'},{code:'19:30', name:'19:30'},{code:'19:45', name:'19:45'},{code:'20:00', name:'20:00'},{code:'20:15', name:'20:15'}, {code:'20:30', name:'20:30'}, {code:'20:45', name:'20:45'},{code:'21:00', name:'21:00'},{code:'21:15', name:'21:15'}, {code:'21:30', name:'21:30'},{code:'21:45', name:'21:45'},{code:'22:00', name:'22:00'}, {code:'22:15', name:'22:15'},
	                 			   		{code:'22:30', name:'22:30'},{code:'22:45', name:'22:45'}, {code:'23:00', name:'23:00'},{code:'23:15', name:'23:15'},{code:'23:30', name:'23:30'},{code:'23:45', name:'23:45'}, {code:'24:00', name:'24:00'}];
	$scope.basicInfoStatus 			= 	[{code:'ACTIVE', name:'ACTIVE'},{code:'INACTIVE', name:'INACTIVE'}];
	$scope.ItemsStatus				= 	[{code:true, name:true},{code:false, name:false}];
	$scope.mandatoryTypeItems 		= 	[{code:true, name:true},{code:false, name:false}];
	
	//console.log("pp=",$scope.mandatoryTypeItems[0]);
	$scope.selectMandatoryTypeItems	=	$scope.mandatoryTypeItems[0];
	
	console.log("fff=",$scope.selectMandatoryTypeItems);
	
	$scope.selectItemsStatus		=	$scope.ItemsStatus[0];
	$scope.selectTypeCheckList		=	$scope.typeCheckList[0];
	
	$scope.selectResponseTypeItems	=	$scope.responseType[0];
	$scope.selectStationType		=	$scope.stationResultData[0];
	$scope.selectCategoryType		=	$scope.categoryCheckList[0];
	$scope.selectBasicInfoStatus	=	$scope.basicInfoStatus[0];
	$scope.selectFrequency			=	$scope.frequencyType[0];
	$scope.selectWeeklyDay			=	$scope.weeklyDayType[0];
	$scope.singleCheckListRecords={};
	$scope.editSpecificItemDetailsList={};
	
	
	var range = [];
	for(var i=1;i<=31;i++) {
	  range.push(i);  
	}
	$scope.MonthDayData = range;

	$rootScope.showFullScreenLoader = true;
	$http({
		  method: 'get',
		  url: AppUtil.restUrls.checkList.allCheckLists,
		}).then(function success(response) {
			if(response.status==200){
				$rootScope.showFullScreenLoader = false;
				$scope.checkListView	=	response.data;
				$scope.currentPage 		= 1; //current page
				$scope.entryLimit 		= 50; //max no of items to display in a page
				$scope.filteredItems 	= $scope.checkListView.length; //Initially for no filter  
				$scope.totalItems 		= $scope.checkListView.length;	
				//console.log(JSON.stringify(response.data));
			}else{
				$rootScope.showFullScreenLoader = false;
				//console.log(response);					
			}
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			console.log("error:"+response);
		});
	
	$scope.addDate = function(){
		$scope.MonthDayDatas = addToDate($scope.MonthDayDatas, $scope.myselectDate);
	}
	
	$scope.addHours = function(){
		$scope.hoursOftheDay = addToHours($scope.hoursOftheDay, $scope.selecthoursType);
	}

	$("#myBtnCheckList").click(function(){
        $("#addCheckListModal").modal({backdrop: false});
    });
	
	$scope.hoursOftheIntraDayListData = $scope.hoursOftheDay;
	
	$scope.addIntraDayHours = function(){
		$scope.hoursOftheIntraDayListData = addToHours($scope.hoursOftheIntraDayListData, $scope.selecthoursIntraDayType);
	}
	
	$scope.showC = function(listss){
		$scope.selectMonthDate = listss;
	}
	
	$scope.showHours = function(hoursLists){
		$scope.selecthoursType = hoursLists;
	}
	
	$scope.showIntraDayHours = function(intradayHoursLists){
		$scope.selecthoursIntraDayType = intradayHoursLists;	
	}
	
	$scope.showWeeklyDayRecords = function(weeklyNameLists){
		$scope.selectWeeklyDay = weeklyNameLists;	
		$scope.hoursOftheIntraDayListData = addToWeeklyRecordShow($scope.selectWeeklyDay.name, $scope.selectWeeklyDay);
	}
	
	function addToWeeklyRecordShow(dateList, item){
		/*if(!angular.isUndefined(dateList) && dateList!=null){
			var contains = false;
			dateList.forEach(function(dateItem){
				console.log("dataItem=",dateItem);
				console.log("items=",item);
				if(dateItem===item){
					contains = true;
				}
			});
			if(!contains){
				dateList.push(item);
			}		
		}else{
			console.log("C");
			dateList = [];
			dateList.push(item);
		}
		return dateList;*/
	}
	
	$scope.showWeeklyTime = function(allweekDaysName){
		$scope.selectWeeklyHoursTime = allweekDaysName;
	}

	$scope.showMonthlyTime = function(allmonthDaysNumber){
		$scope.selectMonthlyHoursTime = allmonthDaysNumber;
	}
	
	$scope.weekList = [];
	
	//add day to week
	$scope.addDayToWeek = function(dayObj){
		if(!angular.isUndefined(dayObj) && dayObj!=null){
			var contains = false;
			$scope.weekList.forEach(function(day){
				if(day.weeklyCode===dayObj.weeklyCode){
					contains = true;
					$scope.addTimeToDay(day,$scope.selectWeeklyHoursTime);
				}
			});
			if(!contains){
				$scope.weekList.push(dayObj);
			}
		}
	}
		
	//add time to day
	$scope.addTimeToDay = function(dayObj,time){
		if(!angular.isUndefined(time) && time!=null){
			var contains = false;
			dayObj.weeklyTime.forEach(function(weektime){
				if(weektime===time){
					contains = true;
					return;
				}
			});
			if(!contains){
				dayObj.weeklyTime.push(time);
			}
		}
	}
	
	$scope.addWeeklyHoursRecords = function(){
		$scope.weeklyObj = {weeklyCode:$scope.selectWeeklyDay.code,weeklyName:$scope.selectWeeklyDay.name,weeklyTime:[$scope.selectWeeklyHoursTime]}
		$scope.weeklyDaysListDetails = $scope.addDayToWeek($scope.weeklyObj);
	}
	
	$scope.addMonthlyHoursRecords = function(){
		$scope.monthlyObj = {monthlyCode:$scope.selectMonthDate,monthlyTime:[$scope.selectMonthlyHoursTime]}
		$scope.monthlyDaysListDetails = $scope.addDayToMonth($scope.monthlyObj);
	}
	
	$scope.monthList = [];
	
	$scope.addDayToMonth = function(dayObj){
		if(!angular.isUndefined(dayObj) && dayObj!=null){
			var contains = false;
			$scope.monthList.forEach(function(day){
				if(day.monthlyCode===dayObj.monthlyCode){
					contains = true;
				$scope.addTimeToMonthDay(day,$scope.selectMonthlyHoursTime);
				}
			});
			if(!contains){
				$scope.monthList.push(dayObj);
			}
		}
	}
	

	$scope.addTimeToMonthDay = function(dayObj,time){
		if(!angular.isUndefined(time) && time!=null){
			var contains = false;
			dayObj.monthlyTime.forEach(function(monthtime){
				if(monthtime===time){
					contains = true;
					return;
				}
			});
			if(!contains){
				dayObj.monthlyTime.push(time);
			}
		}
	}

	function addToWeeklyDays(weekList,weeklyObj,HoursTimeDetail, item){
		if(!angular.isUndefined(weeklyObj) && weeklyObj!=null){
			var contains = false;
			weeklyObj.forEach(function(dateItem){
				if(dateItem.weeklyCode===item.code){
					weeklyObj.weeklyTime.push(item);
					contains = true;
				}
			});
			if(!contains){
				weeklyObj.push(item);
			}		
		}
		return weeklyObj;
	}
	
	$scope.removeDate = function(itemId){
		$scope.MonthDayDatas = removeFromDate($scope.MonthDayDatas, itemId);
	}

	$scope.removeDaysHours = function(itemId){
		$scope.weekList = removeFromWeeklyHoursData($scope.weekList, itemId);
	}
	
	$scope.removeMonthHours = function(itemId){
		$scope.monthList = removeFromMonthlyHoursData($scope.monthList, itemId);
	}
	
	$scope.removeHours = function(itemId){
		$scope.hoursOftheDay = removeFromDate($scope.hoursOftheDay, itemId);
	}
	
	function removeFromMonthlyHoursData(unitList, item){
		if(!angular.isUndefined(unitList) || unitList!=null){
			var newList = [];
			unitList.forEach(function(unitItem){
				if(unitItem.monthlyCode!==item){
					newList.push(unitItem);
				}
			});
			unitList = newList;
		}else{
			unitList = [];
		}
		return unitList;
	}
	
	function removeFromWeeklyHoursData(unitList, item){
		if(!angular.isUndefined(unitList) || unitList!=null){
			var newList = [];
			unitList.forEach(function(unitItem){
				if(unitItem.weeklyName!==item){
					newList.push(unitItem);
				}
			});
			unitList = newList;
		}else{
			unitList = [];
		}
		return unitList;
	}
	
	$scope.removeIntraDayHours= function(itemId){
		$scope.hoursOftheIntraDayListData = removeFromDate($scope.hoursOftheIntraDayListData, itemId);
	}

	$scope.removeDaysNameList= function(itemId){
		$scope.weeklyDaysListDetails = removeFromDate($scope.weeklyDaysListDetails, itemId);
	}
	
	function removeFromDate(unitList, item){
		if(!angular.isUndefined(unitList) || unitList!=null){
			var newList = [];
			unitList.forEach(function(unitItem){
				if(unitItem.code!==item.code){
					newList.push(unitItem);
				}
			});
			unitList = newList;
		}else{
			unitList = [];
		}
		return unitList;
	}
	
	function addToDate(dateList, item){
		if(!angular.isUndefined(dateList) && dateList!=null){
			var contains = false;
			dateList.forEach(function(dateItem){
				if(dateItem===item){
					contains = true;
				}
			});
			if(!contains){
				dateList.push(item);
			}		
		}else{
			dateList = [];
			dateList.push(item);
		}
		return dateList;
	}
	
	function addToHours(hourList, item){
		if(!angular.isUndefined(hourList) && hourList!=null){
			var contains = false;
			hourList.forEach(function(dateItem){
				if(dateItem.code===item.code){
					contains = true;
				}
			});
			if(!contains){
				hourList.push(item);
			}		
		}else{
			hourList = [];
			hourList.push(item);
		}
		return hourList;
	}
	
	$scope.viewItemsData= function(itemsDetails){
		$("#itemsListModal").modal("show");
		$scope.itemsListDetails=itemsDetails;
	}
	
	$scope.viewScheduleData= function(scheduleDetails){
		$("#scheduleListModal").modal("show");
		$scope.scheduleListDetails=scheduleDetails;
	}

	$scope.showFrequecnyData= function(frequecnyData){
		$scope.frequencyCodeAction	=	frequecnyData.code;
	}
	
	$scope.locationCheckList=[];
	
	$scope.displayList=function(chkListData,location){	
		if(chkListData==true){
			$scope.locationCheckList.push(location);
			var contains = false;
			$scope.locationCheckList.forEach(function(chkList){
				if(chkList===location){
					contains = true;
				}
			});
			if(!contains){
				$scope.locationCheckList.push(location);
			}
		} else {
			$scope.locationCheckList = removeFromCheckListCheckBox($scope.locationCheckList, location);
		}
	}
		
	$scope.showFreq = function(){
		$scope.before = !$scope.after;
		if($scope.selectFrequency.code=="OPENING"){
			$scope.openingAfterRadio=$scope.after;
			$scope.openingBeforeRadio=$scope.before;
			$scope.openingfrequencyValue=$scope.frequencyValue;
		}
		else if($scope.selectFrequency.code=="CLOSING"){
			$scope.closingAfterRadio=$scope.after;
			$scope.closingBeforeRadio=$scope.before;
			$scope.closingfrequencyValue=$scope.frequencyValue;
		}
	}
		

	$scope.updateCheckListItemsStatus = function(itemStatus, graphId) {
				
		if (!$window.confirm("Are you sure, you want to activate/deactivate Check List item?")) {
			return false;
		}
			
		var updateChkListItemStatusObj = {
			status : itemStatus,
		};
	
		$scope.itemsListDetails.forEach(function(statusUpdate) {
			if (statusUpdate._id === graphId) {
				statusUpdate.status = itemStatus;
				if (itemStatus == "IN_ACTIVE") {
					$http({
						method : 'POST',
						url : AppUtil.restUrls.checklistitem.activate,
						data : graphId,
					})
					.then(function success(response) {
						if (response.status != 200) {
							alert("Check List Status not updated");
						}
					},
					function error(response) {
						console.log("error:"+ response);
					});
					
			} else if (itemStatus == "ACTIVE") {
					$http({
						method : 'POST',
						url : AppUtil.restUrls.checklistitem.deactivate,
						data : graphId,
					})
					.then(function success(response) {
						if (response.status != 200) {
							alert("Check List Status not updated");
						}
					},
					function error(response) {
						console.log("error:"+ response);
					});
			}}});
	}

	function removeFromCheckListCheckBox(unitList, item){
		if(!angular.isUndefined(unitList) || unitList!=null){
			var newList = [];
			for(i in unitList){
				if(unitList[i]!=item){
				newList.push(unitList[i]);
				}
			}
			unitList = newList;
		}
		return unitList;
	}
	
	$scope.completeCheckListObj={};
	$scope.completeAllCheckListObj=[];
	
	$scope.addBasicInfoDetails= function()
	{
		if($scope.basicInfoName==null || $scope.basicInfoName=="")
		{
			alert("Please input name");
			return false;
		}
		
		if($scope.basicInfoDescription==null || $scope.basicInfoDescription=="")
		{
			alert("Please input description");
			return false;
		}
		
		if($scope.cafeLocation==undefined && $scope.codLocation==undefined){
			alert("Please checked Atleast one checkbox of location");
			return false;
		}
		
		if($scope.ActionData=="add"){
			$scope.completeCheckListObj = 
				{
					id: "",
				    detachAll: null,
					name:$scope.basicInfoName,
					description:$scope.basicInfoDescription,
					type: $scope.selectTypeCheckList.name,
					category: $scope.selectCategoryType.name,
					schedule: {},
					items: [],
					template: true,
					unitCategory:$scope.locationCheckList,
					station:$scope.selectStationType.name,
					status:"IN_ACTIVE",
				}
			$scope.selectTab('tab2');
		} else if($scope.ActionData=="edit") {
			
			$scope.completeCheckListObj =
			{
				id: $scope.singleCheckListRecords._id,
			    detachAll: null,
				name:$scope.basicInfoName,
				description:$scope.basicInfoDescription,
				type: $scope.selectTypeCheckList.name,
				category: $scope.selectCategoryType.name,
				schedule: $scope.singleCheckListRecords.schedule,
				items: $scope.singleCheckListRecords.items,
				template: $scope.singleCheckListRecords.template,
				unitCategory:$scope.singleCheckListRecords.unitCategory,
				station:$scope.selectStationType.name,
				status:$scope.singleCheckListRecords.status,
			}
			$scope.selectTab('tab2');
		}
		else{
			$scope.selectTab('tab1');
		}
	};

	$scope.addItemsInfoDetails=function(){
		if($scope.stepItems==null || $scope.stepItems=="")
		{
			alert("Please input in step field");
			return false;
		}
		if($scope.actionItems==null || $scope.actionItems=="")
		{
			alert("Action is empty");
			return false;
		}
		if($scope.descriptionItems==null || $scope.descriptionItems=="")
		{
			alert("Description is empty");
			return false;
		}
		if($scope.selectMandatoryTypeItems==null || $scope.selectMandatoryTypeItems=="")
		{
			alert("Mandatory field is empty");
			return false;
		}
		if($scope.selectResponseTypeItems==null || $scope.selectResponseTypeItems=="")
		{
			alert("Response type is empty");
			return false;
		}
		if($scope.completeCheckListObj.items.length==0)
		{
			$scope.orderCount=1;
		}
		else
		{
			$scope.orderCount=$scope.orderCount+1;
		}

		if($scope.ActionData=="add" || $scope.ActionResult=="common") {
			$scope.completeCheckListObj.items.push({
				id: "",
				detachAll: null,
		        ordering: $scope.orderCount,
		        step: $scope.stepItems,
		        action: $scope.actionItems,
		        description: $scope.descriptionItems,
		        mandatory: $scope.selectMandatoryTypeItems.code,
		        multiValued: false,
		        responseType:   $scope.selectResponseTypeItems.code,
		        status: "ACTIVE",
			}),
			
			$scope.stepItems			=	null;
			$scope.actionItems			=	null;
			$scope.descriptionItems		=	null;
			
		} else if($scope.ActionData=="edit" || $scope.ActionResult=="edit"){
			$scope.editSpecificItemDetailsList.step 		= $scope.stepItems;
			$scope.editSpecificItemDetailsList.action 		= $scope.actionItems;
			$scope.editSpecificItemDetailsList.description 	= $scope.descriptionItems;
			$scope.editSpecificItemDetailsList.mandatory	=  $scope.selectMandatoryTypeItems.code;
			$scope.editSpecificItemDetailsList.responseType = $scope.selectResponseTypeItems.code;
			var i=0;
			var index=0;
			$scope.singleCheckListRecords.items.forEach(function(itemsList){
				if(itemsList._id===$scope.editSpecificItemDetailsList._id){
					index=i;
				}
				i++;
			});
			$scope.singleCheckListRecords.items[index] = $scope.editSpecificItemDetailsList;
			console.log($scope.singleCheckListRecords);
		}
	};
	
	$scope.editSpecificItemsData=function(editItemsData) {
		$scope.ActionData="edit";
		$scope.ActionResult="edit";
		console.log(editItemsData);
		$scope.stepItems					=	editItemsData.step;
		$scope.actionItems					=	editItemsData.action;
		$scope.descriptionItems				=	editItemsData.description;
		$scope.selectResponseTypeItems		=	editItemsData.responseType;
		$scope.editSpecificItemDetailsList=editItemsData;
		$scope.selectMandatoryTypeItems =editItemsData.responseType
		//$scope.selectMandatoryTypeItems=
		
		var selectedMandatoryList = $scope.mandatoryTypeItems.filter(function(chkmandatoryItem){
			return chkmandatoryItem.name == editItemsData.mandatory;
		});
		var indexOfMandatory = $scope.mandatoryTypeItems.indexOf(selectedMandatoryList[0]);
		$scope.selectMandatoryTypeItems = $scope.mandatoryTypeItems[indexOfMandatory];
		
		var selectedResponseTypeList = $scope.responseType.filter(function(chkResponseType){
			return chkResponseType.name == editItemsData.responseType;
		});
		var indexOfResponseType = $scope.responseType.indexOf(selectedResponseTypeList[0]);
		$scope.selectResponseTypeItems = $scope.responseType[indexOfResponseType];
		
		
	}
	
	$scope.showNotify=function(chkNotify) {
		if(chkNotify==true){
			$scope.notificationRequiredData='';
		}
		else{
			$scope.notificationRequiredData='Activate';
		}
	}
	
	$scope.addItemsInfoDetailsNext=function() {
		if($scope.ActionData=="edit"){
			
			$scope.notificationBeforeRequired=$scope.completeCheckListObj.schedule.notifyBeforeMinutes;
			$scope.editableBeforeMinutes=$scope.completeCheckListObj.schedule.editableBeforeMinutes;
			$scope.notificationRequired=$scope.completeCheckListObj.schedule.notificationRequired;
			
			if($scope.notificationRequired==false){
					$scope.notificationRequiredData="Activate";
					$scope.notificationBeforeRequired="";
			}
			
			if($scope.notificationRequired==true){
				$scope.notificationRequiredData="";
				$scope.notificationBeforeRequired=$scope.completeCheckListObj.schedule.notifyBeforeMinutes;
			}
			
			var selectedFrequencyList = $scope.frequencyType.filter(function(frequency) {
				return frequency.name == $scope.completeCheckListObj.schedule.frequency;
			});
			
			var indexOfFrequency = $scope.frequencyType.indexOf(selectedFrequencyList[0]);
			$scope.selectFrequency = $scope.frequencyType[indexOfFrequency];
			
			if($scope.selectFrequency.code=="OPENING") {
				
				$scope.frequencyCodeAction="OPENING";
				$scope.frequencyValue=$scope.completeCheckListObj.schedule.opening.minutesDifference;
				
				if($scope.completeCheckListObj.schedule.opening.afterOpening==true) {
					$scope.after=true;
					$scope.openingAfterRadio=true;
					$scope.openingBeforeRadio=false;
				} else if($scope.completeCheckListObj.schedule.opening.beforeOpening==true) {
					$scope.after=false;
					$scope.openingAfterRadio=false;
					$scope.openingBeforeRadio=true;
				}
			} else if($scope.selectFrequency.code=="CLOSING"){
			
				$scope.frequencyCodeAction="CLOSING";
				$scope.frequencyValue=$scope.completeCheckListObj.schedule.closing.minutesDifference;
			if($scope.completeCheckListObj.schedule.closing.afterClosing==true)
			{
			$scope.after=true;
			$scope.closingAfterRadio=true;
			$scope.closingBeforeRadio=false;
			}
			else if($scope.completeCheckListObj.schedule.closing.beforeClosing==true)
			{
			$scope.after=false;
			$scope.closingAfterRadio=false;
			$scope.closingBeforeRadio=true;
			}
		} else if($scope.selectFrequency.code=="SHIFT_HANDOVER"){
			$scope.frequencyCodeAction		=	"SHIFT_HANDOVER";
			$scope.handOverMinutesDiffernce	=	$scope.completeCheckListObj.schedule.shiftHandover.minutesDifference;
			if($scope.completeCheckListObj.schedule.shiftHandover.beforeHandover==true){
				$scope.HandOverRadio=false;
			} else if($scope.completeCheckListObj.schedule.shiftHandover.afterHandover==true){
				$scope.HandOverRadio=true;
			}
		} else if($scope.selectFrequency.code=="INTRA_DAY"){
			$scope.frequencyCodeAction="INTRA_DAY";
			$scope.hoursOftheIntraDayListData=[];
			 var dateTimeArray= [];
			 $scope.completeCheckListObj.schedule.intraDay.hoursOfTheDay.forEach(function(time){
				 $scope.hoursOftheIntraDayListData.push($scope.converToDateTimeInteger(time));
		 	 })
		} else if($scope.selectFrequency.code=="WEEKLY"){
			$scope.frequencyCodeAction="WEEKLY";
			$scope.weeklyListData=[];
			var weeklyTimeArray= [];
 
			$scope.completeCheckListObj.schedule.weekly.daysOfWeek.forEach(function(weeklyData){
				var weeklyCode = null;
				var weeklyName = null;
				var weekListData = [];
				$scope.weekList = [];
				$scope.weeklyDayType.forEach(function(weeklyDaysName) {
					if (weeklyData.dayOfWeek == weeklyDaysName.code){
						weeklyCode = weeklyData.dayOfWeek;
						weeklyName = weeklyDaysName.name;
					}
				});
				for (var j = 0; j < weeklyData.hoursOfTheDay.length; j++) {
					weekListData.push($scope.converToDateTimeInteger(weeklyData.hoursOfTheDay[j]));
				}
				
				weeklyTimeArray.push({
					weeklyCode : weeklyCode,
					weeklyName : weeklyName,
					weeklyTime : weekListData
				});
		 			
		 	 });
			 $scope.weekList=weeklyTimeArray;
		} else if($scope.selectFrequency.code=="MONTHLY") {
			$scope.frequencyCodeAction="MONTHLY";
			var monthlyTimeArray= [];
			$scope.monthList = [];
			
			$scope.completeCheckListObj.schedule.monthly.daysOfMonth.forEach(function(monthlyData){
				var monthlyCode = null;
				var monthlyName = null;
				var monthListData = [];
				$scope.monthList = [];

				monthlyCode=monthlyData.dayOfMonth;
				
				for (var j = 0; j < monthlyData.hoursOfTheDay.length; j++) {
					monthListData.push($scope.converToDateTimeInteger(monthlyData.hoursOfTheDay[j]));
				}
				monthlyTimeArray.push({
					monthlyCode : monthlyCode,
					monthlyTime : monthListData
				});
			 });
		}
		
		$scope.monthList = monthlyTimeArray;
	} else if($scope.ActionData=="add") {
		$scope.notificationRequired=true;
	}
		$scope.selectTab('tab3');
}
	$scope.removeItemsData = function(itemId){
		$scope.completeCheckListObj.items = removeFromItems($scope.completeCheckListObj.items, itemId);	
	}
	function removeFromItems(unitList, item){
		if(!angular.isUndefined(unitList) || unitList!=null){
			var newList = [];
			for(i in unitList){
				console.log(i);
				if(i!=item){
					console.log(i);
					newList.push(unitList[i]);
				}
			}
			unitList = newList;
		}
		return unitList;
	}
		
	$scope.selectTab = function(tabIndex){
		switch(tabIndex){
			case 'tab1' :
			$scope.tab1 = false;
			$scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = true;
			break;
			case 'tab2' :
			$scope.tab2 = false;
			$scope.tab1 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = true;
			break;
			case 'tab3' :
			$scope.tab3 = false;
			$scope.tab1 = $scope.tab2 = $scope.tab4 = $scope.tab5 = $scope.tab6 = true;
			break;
			case 'tab4' :
			$scope.tab4 = false;
			$scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab5 = $scope.tab6 = true;
			break;
		}
	}
	
	$scope.addCheckList = function(){
		$scope.ActionData="add"
		$scope.basicInfoName 			= 	null;
		$scope.basicInfoDescription 	= 	null;
		$scope.stepItems 				= 	null;
		$scope.actionItems 				= 	null;
		$scope.descriptionItems			= 	null;
		//$scope.selectMandatoryTypeItems	=	[];
	   // $scope.selectResponseTypeItems	=	[];
		$scope.completeAllCheckListObj	=	[];
		$scope.notificationRequired		=	false;
		$scope.monthEndCheck			=  null;
		$scope.hoursOftheIntraDayListData	= [];
		$scope.weeklyDaysListDetails		= [];
		$scope.hoursOftheDay				= [];
		$scope.MonthDayDatas				= [];
		$scope.editableBeforeMinutes		= 	null;
		$scope.notificationBeforeRequired 	=	null;
		$scope.selectTab('tab1');
		$("#addCheckListModal").modal("show");
	}	
	
	$scope.checkMonthEnd = function(checkMonthData){
		if(checkMonthData==true){
			$scope.monthEndCheck='Activate';
		}
		else {
			$scope.monthEndCheck='';
		}
	}
	
	$scope.mytime = new Date();
	$scope.hstep = 1;
	$scope.mstep = 15;
	$scope.options = {
		hstep: [1, 2, 3],
		mstep: [1, 5, 10, 15, 25, 30]
	};

	$scope.ismeridian = false;
	$scope.toggleMode = function() {
		$scope.ismeridian = ! $scope.ismeridian;
	};

	$scope.update = function() {
		var d = new Date();
		d.setHours( 14 );
		d.setMinutes( 0 );
		$scope.mytime = d;
	};

	  $scope.changed = function () {
	    $log.log('Time changed to: ' + $scope.mytime);
	  };

	  $scope.clear = function() {
	    $scope.mytime = null;
	  };

	
	  $scope.filter = function() {
        $timeout(function() { 
            $scope.filteredItems = $scope.filtered;
        }, 10);
	  };
	
		$scope.sort_by = function(predicate) {
	        $scope.predicate = predicate;
	        $scope.reverse = !$scope.reverse;
	    };
    
	    var scheduleObj="";
	    $scope.composeEmptyScheduleObject = function(){
		return {
	      notificationRequired		:  	$scope.notificationRequired,
	      notifyBeforeMinutes		:   $scope.notificationBeforeRequired,
	      editableBeforeMinutes		: 	$scope.editableBeforeMinutes,
	      frequency					: 	$scope.selectFrequency.code,
	      OPENING					: 	null,
	      CLOSING					:	null,
	      INTRA_DAY					: 	null,
	      SHIFT_HANDOVER			: 	null,
	      WEEKLY					: 	null,
	      MONTHLY					: 	null
		}
	}    
	$scope.fullCheckListObj={};
	$scope.converToTimeInteger =  function(time){
		var timeArray = time.split(":");
		return timeArray[0] * 4 + timeArray[1] / 15;
	}
	 $scope.showOpeningValue= function(radiOpen){
		if(radiOpen=='after')
			{
			$scope.radio=true;
			}
		else
			{
			$scope.radio=false;
			}
		//console.log(radiOpen);
	 }
	 $scope.converToDateTimeInteger =  function(time){
		 var hour = Math.floor(time /4);
		 var minutes = (time % 4) * 15;
		if(minutes < 10)
		{
			minutes = "0" + minutes;
		}
		return  { 
					code:hour+":"+minutes,
					name:hour+":"+minutes
				}; 
		}
	 $scope.newIntraDayList=[];
    $scope.viewAllSummary = function() {
    	if($scope.notificationBeforeRequired=="" || $scope.notificationBeforeRequired==null)
    		{
    		alert("Notification before minutes is empty")
    		return false;
    		}
	    	if($scope.editableBeforeMinutes=="" || $scope.editableBeforeMinutes==null)
			{
			alert("Editable before minutes is empty")
			return false;
			}
	    	if($scope.selectFrequency.code=="Select Frequecny" || $scope.selectFrequency==null)
			{
			alert("Please Select any Frequency");
			return false;
			}
    	
    	console.log($scope.selectFrequency.code);
    	if($scope.selectFrequency.code=="OPENING" || $scope.selectFrequency.code=="CLOSING")
    		{
	    		if($scope.after==undefined)
	    			{
	    			alert("Please Checked any checkbox Before or after");
	    			return false;
	    			}
	    		
	    		if($scope.frequencyValue=="" || $scope.frequencyValue==null)
	    		{
	    			alert("Please input time difference");
	    			return false;
	    		}
    		
    		}
    	else if($scope.selectFrequency.code=="SHIFT_HANDOVER")
		{
		CONSOLE.LOG("ADFA")
		}
    	
    	
    	
  /*  if($scope.ActionData=="edit")
    	{*/
    	$scope.completeCheckListObj.schedule.notificationRequired				=	$scope.notificationRequired;
    	$scope.completeCheckListObj.schedule.notifyBeforeMinutes				=	$scope.notificationBeforeRequired;
    	$scope.completeCheckListObj.schedule.editableBeforeMinutes				=	$scope.editableBeforeMinutes;
    	$scope.completeCheckListObj.schedule.frequency							=	$scope.selectFrequency.code;
    	$scope.completeCheckListObj.schedule.opening							=	null;
    	$scope.completeCheckListObj.schedule.closing							=	null;
    	$scope.completeCheckListObj.schedule.intraDay							=	null;
    	$scope.completeCheckListObj.schedule.shiftHandover						=	null;
    	$scope.completeCheckListObj.schedule.weekly								=	null;
    	$scope.completeCheckListObj.schedule.monthly							=	null;
    	if($scope.selectFrequency.code=="OPENING")
    		 {
    		 if($scope.after==false)
    			 {
    			 $scope.beforeOpening=true;
    			 $scope.afterOpening=false;
    			 }
    		 else if($scope.after==true)
    			 {
    			 $scope.beforeOpening=false;
    			 $scope.afterOpening=true;
    			 }
    		 $scope.completeCheckListObj.schedule.opening={minutesDifference: $scope.frequencyValue,afterOpening:$scope.afterOpening,beforeOpening:$scope.beforeOpening};
    		 }
    	 else if($scope.selectFrequency.code=="CLOSING")
    		 {
    		 if($scope.after==false)
			 {
			 $scope.beforeClosing=true;
			 $scope.afterClosing=false;
			 }
		 else if($scope.after==true)
			 {
			 $scope.beforeClosing=false;
			 $scope.afterClosing=true;
			 }
    		 $scope.completeCheckListObj.schedule.closing={minutesDifference: $scope.frequencyValue,afterClosing:$scope.afterClosing,beforeClosing:$scope.beforeClosing};
    		 //console.log($scope.completeCheckListObj.schedule.closing);
    		 }
    	 else if($scope.selectFrequency.code=="SHIFT_HANDOVER")
		 {
    		 //console.log($scope.HandOverRadio);
    		if($scope.HandOverRadio==false)
    			 {
    			 $scope.afterHandOver1=false;
    			 $scope.beforeHandOver1=true;
    			 }
    			 else if($scope.HandOverRadio==true)
    				 {
    				 $scope.afterHandOver1=true;
    				 $scope.beforeHandOver1=false;
    				 }
    		  $scope.completeCheckListObj.schedule.shiftHandover={beforeHandover: $scope.beforeHandOver1,minutesDifference: $scope.handOverMinutesDiffernce,afterHandover: $scope.afterHandOver1};
		 }
    	 else if($scope.selectFrequency.code=="INTRA_DAY")
		 {
		 var timeArray= [];
		// console.log($scope.hoursOftheIntraDayListData);
		$scope.hoursOftheIntraDayListData.forEach(function(hoursOfIntraDayLists){
			$scope.newIntraDayList.push(hoursOfIntraDayLists.code);
		});
		$scope.newIntraDayList.forEach(function(time){
			timeArray.push($scope.converToTimeInteger(time));
		});
		$scope.completeCheckListObj.schedule.intraDay = {hoursOfTheDay: timeArray};
		 }
    	 else if($scope.selectFrequency.code=="WEEKLY")
		 {
		 $scope.completeWeeklyObjDisplay=[];
	    	$scope.weekList.forEach(function(weekListObjShow){
	    		var timeFrame=[];
	    		for(i=0;i<weekListObjShow.weeklyTime.length;i++)
	    			{
	    			timeFrame.push($scope.converToTimeInteger(weekListObjShow.weeklyTime[i].code));
	    			}
	    		$scope.completeWeeklyObjDisplay.push({dayOfWeek:weekListObjShow.weeklyCode,hoursOfTheDay:timeFrame});
	    	});
	    	$scope.completeCheckListObj.schedule.weekly = {daysOfWeek:$scope.completeWeeklyObjDisplay};
		 }
    	 else if($scope.selectFrequency.code=="MONTHLY")
		 {
    		 console.log("MonthEndResult=",$scope.monthlyEnd);
    		 
    		 $scope.completeMonthlyObjDisplay=[];
    			$scope.monthList.forEach(function(monthListObjShow){
    	    		var monthTimeFrame=[];
    	    		console.log("monthListObjShow==",monthListObjShow);
    	    		for(i=0;i<monthListObjShow.monthlyTime.length;i++)
    	    			{
    	    			monthTimeFrame.push($scope.converToTimeInteger(monthListObjShow.monthlyTime[i].code));
    	    			}
    	    		$scope.completeMonthlyObjDisplay.push({monthEnd: $scope.monthlyEnd,dayOfMonth:monthListObjShow.monthlyCode,hoursOfTheDay:monthTimeFrame});
    	    	});
    			$scope.completeCheckListObj.schedule.monthly = {daysOfMonth:$scope.completeMonthlyObjDisplay};
    			 console.log($scope.completeMonthlyObjDisplay);
    			 	console.log("MONTHLY");
		 }
    	 
  /*  	}
    
   
    */
    
  //  console.log($scope.completeCheckListObj);
  //  console.log(JSON.stringify($scope.completeCheckListObj));
    
    //console.log($scope.openingAfterRadio);
    //console.log($scope.openingBeforeRadio);
    //console.log($scope.frequencyValue);
    	
    	//console.log($scope.closingAfterRadio);
    	//console.log($scope.closingBeforeRadio);
    	//console.log($scope.frequencyValue);
    		   
    		
    	   
    	/*$scope.fullCheckListObj	=	$scope.completeCheckListObj;
    	if($scope.afterHandOver==true)
    		{    $scope.afterHandOver=true;
    		}
    	else
    		{
    		$scope.afterHandOver=false;
    		}
    	if($scope.beforeHandOver==true)
		{
    		$scope.beforeHandOver=true;
		}
    	else
		{
    		$scope.beforeHandOver=false;
		}
    	$scope.newIntraDayList=[];
    	if($scope.selectFrequency.code=="INTRA_DAY")
    		{
    			$scope.hoursOftheIntraDayListData.forEach(function(hoursOfIntraDayLists){
			    		$scope.newIntraDayList.push(hoursOfIntraDayLists.code);
			    	});
    		}
    	
    	$scope.completeWeeklyObjDisplay=[];
    	$scope.weekList.forEach(function(weekListObjShow){
    		var timeFrame=[];
    		for(i=0;i<weekListObjShow.weeklyTime.length;i++)
    			{
    			timeFrame.push(weekListObjShow.weeklyTime[i].code);
    			}
    		$scope.completeWeeklyObjDisplay.push([{dayOfWeek:weekListObjShow.weeklyCode,hoursOfTheDay:timeFrame}]);
    	});
    	
    
    	$scope.scheduleObject = $scope.composeEmptyScheduleObject();
    	if($scope.selectFrequency.code=="OPENING"){
    		$scope.scheduleObject.opening = {minutesDifference: $scope.frequencyValue,beforeOpening:$scope.openingBeforeRadio,afterOpening:$scope.openingAfterRadio};
    	}
    	  
    	else if($scope.selectFrequency.code=="CLOSING"){
    		$scope.scheduleObject.closing = {minutesDifference:$scope.frequencyValue,beforeClosing:$scope.closingBeforeRadio,afterClosing:$scope.closingAfterRadio};
    	}
    	else if($scope.selectFrequency.code=="INTRA_DAY"){

    		var timeArray= [];
    		$scope.newIntraDayList.forEach(function(time){
    			timeArray.push($scope.converToTimeInteger(time));
			});
    		$scope.scheduleObject.intraDay = {hoursOfTheDay: timeArray};
    	}
    	else if($scope.selectFrequency.code=="SHIFT_HANDOVER"){
    		$scope.scheduleObject.shiftHandover = {beforeHandover: $scope.beforeHandOver,minutesDifference: $scope.handOverMinutesDiffernce,afterHandover: $scope.afterHandOver};
    	}
    	else if($scope.selectFrequency.code=="WEEKLY"){
    		$scope.scheduleObject.weekly = {daysOfWeek: [$scope.completeWeeklyObjDisplay]};
    	}
    	else if($scope.selectFrequency=="MONHTLY"){
    		$scope.scheduleObject.monthly = {daysOfMonth: [{ monthEnd: true,dayOfMonth: 3, hoursOfTheDay: [14,16,17,18]},{monthEnd: false,dayOfMonth: 15,hoursOfTheDay: [24,25,26]}]}
    	}
    	
    	//console.log($scope.scheduleObject);
    	
    	
    	
// $scope.fullCheckListObj.forEach(function(completeObjItems){
    	$scope.fullCheckListObj.items			=	$scope.completeAllCheckListObj;
    	$scope.fullCheckListObj.schedule		=	$scope.scheduleObject;
	//});
 
    	
    	*/
 $scope.fullCheckListObj	=	$scope.completeCheckListObj;
 //console.log($scope.fullCheckListObj); 
 $scope.selectTab('tab4');
    }
    $scope.updateCheckListStatus=function(chkListStatus,graphId)
	{
    //	$scope.inActiveProduct = function(stausID,catStatus){
    		if ($window.confirm("Are you sure, you want to In activate Check List?")) {
    			//console.log("YES");;
    	    } else {
    	    	return false;
    	    }
    			var updateChkListStatusObj = {
    					status:chkListStatus,
    			};
    			$scope.checkListView.forEach(function(statusUpdate){
    				if(statusUpdate._id === graphId){
    				statusUpdate.status=chkListStatus;
    				if(chkListStatus=="IN_ACTIVE")
    				{
    				console.log(chkListStatus+" ,"+graphId);
    			$http({
    				  method: 'POST',
    				  url: AppUtil.restUrls.checkList.deactivate,
    				  data: graphId,
    				}).then(function success(response){
    					if(response.status != 200){
    						alert("Check List Status not updated");				
    					}
    				}, function error(response) {
    					  console.log("error:"+response);
    			});
    				}
    				else if(chkListStatus=="ACTIVE")
    				{
    					console.log("ACTIVE");
    					$http({
    						  method: 'POST',
    						  url: AppUtil.restUrls.checkList.activate,
    						  data: graphId,
    						}).then(function success(response){
    							if(response.status != 200){
    								alert("Check List Status not updated");				
    							}
    						}, function error(response) {
    							  console.log("error:"+response);
    					});
    				}
    				
    				}
    			});
	}
    
    $scope.editCheckListDetails=function(chkListData) {
    	$scope.ActionData="edit";
    	$scope.ActionResult="common";
    	console.log(chkListData);
    	$scope.selectTab('tab1');
    	$("#addCheckListModal").modal("show");
    	$scope.basicInfoName 			= 	chkListData.name;
		$scope.basicInfoDescription 	= 	chkListData.description;
		$scope.singleCheckListRecords=chkListData;
		//$scope.hoursOftheIntraDayListData=chkListData.schedule.intraDay.hoursOfTheDay;
		var selectedStation = $scope.stationResultData.filter(function(station){
			return station.name == chkListData.station;
		});
		var indexOfStation = $scope.stationResultData.indexOf(selectedStation[0]);
		$scope.selectStationType = $scope.stationResultData[indexOfStation];
		$scope.status	=	chkListData.status;
		var selectedCat = $scope.categoryCheckList.filter(function(catData){
			return catData.name == chkListData.category;
		});
		var indexOfCategory 		= $scope.categoryCheckList.indexOf(selectedCat[0]);
		$scope.selectCategoryType	= $scope.categoryCheckList[indexOfCategory];
		chkListData.unitCategory.forEach(function(cat){
			if(cat == "CAFE"){
				$scope.cafeLocation = true;
			}
			if(cat == "COD"){
				$scope.codLocation = true;
			}
		});
		$scope.completeAllCheckListObj=chkListData.items
		console.log($scope.completeAllCheckListObj);
    }
    
    $scope.addCompleteChecklist=function(fullObjchkList) {
    	console.log(fullObjchkList);
    	$http({
			method: 'POST',
			  url: AppUtil.restUrls.checkList.addCheckList,
			  data: fullObjchkList,
			}).then(function success(response) {
				console.log(response)
				if(response.status==200){
					alert("CheckList Successfull added!");
					window.location.reload();
					}else{
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
		});
	}
    
    $scope.editCompleteChecklist=function(fullObjchkList)
	{
    	console.log(fullObjchkList);
    	$http({
			method: 'POST',
			  url: AppUtil.restUrls.checkList.updateCheckList,
			  data: fullObjchkList,
			}).then(function success(response) {
				console.log(response)
				if(response.status==200){
					alert("CheckList Successfull updated!");
					window.location.reload();
					}else{
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
		});
	} 
    
});