adminapp.controller("kiosCompanyctrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

	$scope.paymentMode = 	[{name:"Employee Paid",value:"EMPLOYEE_PAID"},{value:"COMPANY_PAID", name:"Company Paid"}];
	$scope.kiosStatus = 	[{name:"ACTIVE",value:"Active"},{name:"IN ACTIVE",value:"IN_ACTIVE"}];
	
	$scope.selectedPaymentMode=$scope.paymentMode[0];
	$scope.selectedKiosStatus=$scope.kiosStatus[0];


   $scope.init = function () {
	   $scope.domainList = [];
	   //$scope.domainList
    }
   
   
   
   $http({
		method: 'GET',
		url : AppUtil.restUrls.kioskManagement.kioskCompany,
		}).then(function success(response) {
			$scope.kiosCompanyList = response.data;
			console.log($scope.kiosCompanyList);
			
			$scope.currentPage = 1; //current page
			$scope.entryLimit = 50; //max no of items to display in a page
			$scope.filteredItems = $scope.kiosCompanyList.length; //Initially for no filter  
			$scope.totalItems = $scope.kiosCompanyList.length;
			//console.log("dd=",$scope.totalItems);
			//console.log("listCompany=",$scope.kiosCompanyList);
			
		}, function error(response) {
			console.log("error:"+response);
	});
   
   
   /* $scope.kiosfullObj=[
     {
         companyId: 1,
         companyName: 'Oyo Company',
         companyDomains: ['@oyo1','@oyo2','@oyo3','@oyo4','@oyo5'],
         contactDetails: {
           contactId: 1,
           name: 'Shikhar',
           email: '<EMAIL>',
           phone: '2423423423',
           contactStatus: 'U-4,Cyber city Gurgaon Haryana'
         },
         country: 'IN',
         companyEmail: '<EMAIL>',
         companyStatus: 'ACTIVE',
         paymentMode: 'COMPANY_PAID'
       },
       {
           companyId: 2,
           companyName: 'Airtel',
           companyDomains: ['@airtel1','@airtel2','@airtel3','@airtel4','@airtel5'],
           contactDetails: {
             contactId: 1,
             name: 'Neeraj',
             email: '<EMAIL>',
             phone: '5356653',
             contactStatus: 'U-5,vibe city Gurgaon Haryana'
           },
           country: 'IN',
           companyEmail: '<EMAIL>',
           companyStatus: 'ACTIVE',
           paymentMode: 'COMPANY_PAID'
         },
         {
             companyId: 3,
             companyName: 'Google',
             companyDomains: ['@google1','@google2','@google3','@google4','@google5'],
             contactDetails: {
               contactId: 1,
               name: 'Raphel',
               email: '<EMAIL>',
               phone: '6434533343',
               contactStatus: 'U-5,Umbrella city Gurgaon Haryana'
             },
             country: 'IN',
             companyEmail: '<EMAIL>',
             companyStatus: 'ACTIVE',
             paymentMode: 'COMPANY_PAID'
           },
           {
               companyId: 4,
               companyName: 'Vodafone Company',
               companyDomains: ['@voda1','@voda2','@voda3','@voda3','@voda4'],
               contactDetails: {
                 contactId: 1,
                 name: 'Ambuj Pandey',
                 email: '<EMAIL>',
                 phone: '9973732423',
                 contactStatus: 'U-6,Ambience Mall Gurgaon Haryana'
               },
               country: 'IN',
               companyEmail: '<EMAIL>',
               companyStatus: 'ACTIVE',
               paymentMode: 'COMPANY_PAID'
             }
 ]*/
   
  // $scope.employeeList = response.data;
	/*$scope.currentPage = 1; //current page
	$scope.entryLimit = 50; //max no of items to display in a page
	$scope.filteredItems = $scope.kiosfullObj.length; //Initially for no filter  
	$scope.totalItems = $scope.kiosfullObj.length;
   */
	
	$scope.sort_by = function(predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };
    
    $scope.updatePerm = function(val){
    	if(val==='true'){
    		$scope.permanentAddressSame = true;
    	}else{
    		$scope.permanentAddressSame = false;
    	}
    }
   
  // var baseUrl = "http://*************:8080/";
     
   /*$http({
		  method: 'GET',
		  url: AppUtil.restUrls.unitMetaData.allUnits+'?category=KITCHEN'
		}).then(function success(response) {
			$rootScope.showFullScreenLoader = false;
			$scope.kitchenlist = response.data;
			$scope.unitlist = $scope.kitchenlist;
			$scope.selectedUnit = $scope.unitlist[0];
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
		});*/
   
      /* $scope.add = function () {
         $scope.items.push({question: "",questionPlaceholder:"domain",text: ""
         });
       };*/
    $("#addCompanyIdDiv").click(function(){
        $("#companyModal").modal({backdrop: false});
    });
   
   $scope.addMoreDomain = function() {
	 // console.log("Learning=",$scope.kiosCompanyDomains);
	   var newItemNo = $scope.domainList.length+1;
	    $scope.domainList.push({'id':'choice'+$scope.kiosCompanyDomains,'name':$scope.kiosCompanyDomains});
	   $scope.kiosCompanyDomains="";
	  };
	    
	  $scope.removeChoice = function() {
	    var lastItem = $scope.domainList.length-1;
	    $scope.domainList.splice(lastItem);
	  };
   
   console.log("domainList=",$scope.domainList)
     
   $scope.addCompany = function(){
	   $scope.domainList=[];
	   	$scope.action = "Add"
		$scope.kiosCompanyName="",
		$scope.kiosCompanyDomains="";
		$scope.kiosCompanyEmail="",
		$scope.kiosContactDetailName="",
		$scope.kiosContactDetailEmail="",
		$scope.kiosContactDetailPhone="",
		$scope.selectedCompanyUrl="";
		$scope.kiosContactDetailShortCode="";
		
	
	$("#companyModal").modal("show");
	$scope.loading = false;
}
   
   function validateEmail(email) {
       var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
       return re.test(email);
   }
   
   $scope.submitAddCompany=function()
   {
	   
	   if ($scope.kiosCompanyName == null || $scope.kiosCompanyName == "") {
          alert("Company is empty, Please fill company name");
           return;
       }   
	   
	   if ($scope.kiosCompanyEmail == null || !validateEmail($scope.kiosCompanyEmail)) {
           alert("Please fill valid company email");
           return;
       }
	   
	   var keys = Object.keys($scope.domainList);
	   var len = keys.length;
	   
	   if(len==0)
		   {
		   	alert("Please Enter input domain name");
		   	return false;
		   }
	   
	   /*if ($scope.kiosCompanyDomains == null || $scope.kiosCompanyDomains == "") {
           alert("Please Enter company domain");
           return;
       }*/
	   
	   if ($scope.kiosContactDetailName == null || $scope.kiosContactDetailName == "") {
           alert("Please Enter contact name");
           return;
       }
	   
	   if ($scope.kiosContactDetailEmail == null || !validateEmail($scope.kiosContactDetailEmail)) {
           alert("Please fill valid contact email");
           return;
       }
	   
	   if ($scope.kiosContactDetailShortCode == null || $scope.kiosContactDetailShortCode == "") {
           alert("Please Enter contact Phone");
           return;
       }
	   
	   if ($scope.selectedCompanyUrl == null || $scope.selectedCompanyUrl == "") {
           alert("Please Enter Company URL");
           return;
       }
	   
	   
	   
 //$scope.domainList.push({'id':'choice0',name:""});
	   var contactStatusObj={
			  name :$scope.kiosContactDetailName,
			  code:$scope.kiosContactDetailEmail,
			  shortCode:$scope.kiosContactDetailShortCode
	   }
	   console.log($scope.selectedPaymentMode);
	   var domainList = $scope.domainList.map(function(domain){
		   delete domain.id;
	   });
	   var companyObj = {
			   companyName:$scope.kiosCompanyName,
			   companyDomains:$scope.domainList,
			   companyEmail:$scope.kiosCompanyEmail,
			   companyStatus:'IN_ACTIVE',
			   paymentMode:$scope.selectedPaymentMode.value,
			   country:'IN',
			   kioskSubDomain:$scope.selectedCompanyUrl,
			  contactDetails:contactStatusObj
			  //companyStatus:'IN'
			}
  
	   $rootScope.showFullScreenLoader = true;
	   	
	   console.log("companyObj=",companyObj);
	   
  $http({
			method : 'POST',
			url : AppUtil.restUrls.kioskManagement.kioskCompany,
			data :companyObj,
			headers:{
				'Access-Control-Allow-Origin': '*'
			}
		})
		.then(
		function success(response) {
			alert("Submitted Successfully");
			$rootScope.showFullScreenLoader = false;
			$scope.kiosCompanyList.push(response.data);
			//$("#btnSubmit").show();
			 $("#companyModal").modal("hide");
			
			//console.log("res",response);
		});
   }
  
   $scope.CompanyStatusChange=function(companyStatus,companyID)
   {
	   console.log(companyStatus);
	   if(companyStatus=="ACTIVE")
		   {
		   companyStatusData=true;		   
		   }
			  else if(companyStatus=="IN_ACTIVE")
			   {
			   companyStatusData=false;		   
			   }
			   else
				   {
				   companyStatusData=null;
				   }
	   console.log("STATUS="+companyStatus+"COMPANYID="+companyID);
	   $http({
           method: 'GET',
           url: AppUtil.restUrls.kioskManagement.kiosCompanyStatus + '?companyId=' + companyID+'&activate=' + companyStatusData,
       }).then(function success(response) {
    	   $scope.kiosCompanyList.forEach(function(companyListData)
    	   {
    		if(companyListData.companyId==companyID)  
    			{
    			if(companyStatus=="IN_ACTIVE"){
    				companyListData.companyStatus="IN_ACTIVE";
    			}
    			
    			else if(companyStatus=="ACTIVE"){
        			companyListData.companyStatus="ACTIVE";
        			}
    			}
    	   });
    	   
          // $scope.unitlist = response.data;
           console.log("ResultHere=",response);
           
       }, function error(response) {
           console.log("error:" + response);
       });
	   
   }
   
   
   $scope.editCompany = function(companyID)
   {
	   console.log(companyID);
	   $scope.action = "Edit";
	   $("#companyModal").modal("show");
	   console.log($scope.kiosfullObj);
	   
	   $scope.kiosCompanyList.forEach(function(fullCompanyObjs)
	   {
		  if(fullCompanyObjs.companyId==companyID)
			 {
			 $scope.kiosCompanyName=fullCompanyObjs.companyName;
			 $scope.kiosCompanyEmail=fullCompanyObjs.companyEmail;
			 $scope.kiosContactDetailName=fullCompanyObjs.contactDetails.name;
			 $scope.kiosContactDetailEmail=fullCompanyObjs.contactDetails.code;
			 $scope.kiosContactDetailShortCode=fullCompanyObjs.contactDetails.shortCode;
			 $scope.domainList=fullCompanyObjs.companyDomains;
			 $scope.selectedCompanyUrl=fullCompanyObjs.kioskSubDomain;
			 /*$scope.domainList.forEach(function(domainListData)
			 {
				$scope.domainData= domainListData;
			 });
			 */
			/* domainList,
			 $scope.kiosCompanyEmail=fullCompanyObjs.companyName;,
			 companyStatus:'IN_ACTIVE',
			 $scope.selectedPaymentMode.value,
			   country:'IN',
			  contactDetails:contactStatusObj*/
			 
			 }
	   });
   }
   
   $scope.submitUpdateCompany = function()
   {
	   
	   var contactStatusObj={
				  name :$scope.kiosContactDetailName,
				  code:$scope.kiosContactDetailEmail,
				  shortCode:$scope.kiosContactDetailShortCode
		   }
		   console.log("helloo=",contactStatusObj.name);
		   /*var domainList = $scope.domainList.map(function(domain){
			   delete domain.id;
		   });*/
		   var companyObj = {
				   companyName:$scope.kiosCompanyName,
				  // companyDomains:domainList,
				   companyEmail:$scope.kiosCompanyEmail,
				   companyStatus:'IN_ACTIVE',
				   paymentMode:$scope.selectedPaymentMode.value,
				   country:'IN',
				  contactDetails:contactStatusObj
				  //companyStatus:'IN'
				}
		   
		   console.log(companyObj);
   
   }
   
   
});