/*
 * SUNS<PERSON><PERSON>E TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("unitDroolMappingCtrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

    var dimensionInfo = null;
    var selectedDimension = null;

    $scope.init = function () {

        $scope.selectedDroolFileType = null;
        $scope.selectedRegion = null;
        $scope.droolFileTypes = ["OFFER_DECISION", "RECOM_OFFER_DECISION", "WALLET_RECOMMENDATION", "<PERSON>OY<PERSON><PERSON>_SCREEN_DECISION",
            "CRM_SCREEN_FLOW_DECISION", "WALLET_DECISION", "DENOMINATION_DECISION", "CUSTOMER_ENGAGEMENT_DROOL_DECISION", "FREE_PRODUCT_DROOL_DECISION","MEMBERSHIP_SUGGESTION_DROOL_DECISION"];
        $scope.trimmedRegions = [];
        getRegionDetails();
        $scope.selectedRegion = [];
        $scope.unitType = 'CAFE';
        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '350px'
        };
        $scope.unitDroolFileMappingList = [];
        $scope.unitDroolMappings = [];
        $scope.allFileDetail = [];
        $scope.fileVersionList = [];
        getUnitData();
    };


    function getUnitData() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnitsList
        }).then(function success(response) {
            $scope.unitList = response.data;
            for (var i = 0; i < $scope.unitList.length; i++) {
                if ($scope.unitList[i].status !== "ACTIVE") {
                    $scope.unitList.splice(i, 1);
                }
            }
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    function getRegionDetails() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regions = response.data;
            $scope.filterRegion($scope.regions);
            $scope.regions.push('ALL');
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.filterRegion = function (regionAll) {
        $scope.trimmedRegions = regionAll.filter(function (region) {
            return region != 'ALL';
        });
    };

    $scope.getAllFilesDetail = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.fileVersionList = [];
        var urlString = "";
        if ($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" || $scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
            || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION") {
            urlString = AppUtil.restUrls.droolsCrmFileManagement.getAllDroolFileForType + "?fileType=" + $scope.selectedDroolFileType;
        } else if ($scope.selectedDroolFileType === "WALLET_DECISION" || $scope.selectedDroolFileType === "DENOMINATION_DECISION") {
            urlString = AppUtil.restUrls.droolsTransactionManagement.getAllDroolFileForType + "?fileType=" + $scope.selectedDroolFileType;
        } else {
            urlString = AppUtil.restUrls.droolsFileManagement.getAllDroolFileForType + "?fileType=" + $scope.selectedDroolFileType;
        }
        $http({
            method: 'GET',
            url: urlString,
        }).then(function success(response) {
            $scope.allFileDetail = response.data;
            for (var i = 0; i < $scope.allFileDetail.length; i++) {
                $scope.allFileDetail[i].creationTime = new Date($scope.allFileDetail[i].creationTime).toLocaleString(undefined, { timeZone: 'Asia/Kolkata' });
                if($scope.allFileDetail[i].status == 'ACTIVE' && $scope.allFileDetail[i].version != null){
                    $scope.fileVersionList.push($scope.allFileDetail[i].version);
                }
            }
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            window.alert("Error while fetching files detail")
            console.log("error: " + response);
        });
    }

    $scope.getUnitDroolMappingDetails = function () {
        if ($scope.selectedDroolFileType == undefined || $scope.selectedDroolFileType == null) {
            alert("Please select the drool file type !!!!!");
        }
        $scope.getAllFilesDetail();
        $http({
            method: 'POST',
            url: AppUtil.restUrls.unitDroolMetadata.unitDroolMapping
        }).then(function success(response) {
            $scope.unitDroolMappings = response.data;
            setUnitDroolVersionData();
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    function setUnitDroolVersionData() {
        var unitMappingObj = {
            unitId: null,
            unitName: null,
            currentVersion: null,
            updatedVersion: null,
            currentStatus: null,
            updatedStatus: null,
            region: null
        }

        for (var i = 0; i < $scope.unitList.length; i++) {
            var unitDetail = $scope.unitList[i];
            var obj = angular.copy(unitMappingObj);
            obj.unitId = unitDetail.id;
            obj.unitName = unitDetail.name;
            obj.region = unitDetail.region;
            if ($scope.unitDroolMappings[unitDetail.id] != undefined && $scope.unitDroolMappings[unitDetail.id] != null) {
                var fileData = $scope.unitDroolMappings[unitDetail.id];
                if (fileData[$scope.selectedDroolFileType] != undefined && fileData[$scope.selectedDroolFileType] != null) {
                    obj.currentVersion = fileData[$scope.selectedDroolFileType].version;
                    obj.currentStatus = 'ACTIVE';
                    console.log("Object ::: ", obj);
                }
            }
            $scope.unitDroolFileMappingList.push(obj);
        }


        $scope.mappings = [];
        if ($scope.selectedRegion != undefined && $scope.selectedRegion != null && $scope.selectedRegion.length > 0) {
            for (var i = 0; i < $scope.unitDroolFileMappingList.length; i++) {
                if ($scope.selectedRegion.includes($scope.unitDroolFileMappingList[i].region)) {
                    $scope.mappings.push($scope.unitDroolFileMappingList[i]);
                }
            }
            $scope.unitDroolFileMappingList = $scope.mappings;
        }
    }


    $scope.changeRow = function (isChecked, detail) {
        for (var i = 0; i < $scope.unitDroolFileMappingList.length; i++) {
            if ($scope.unitDroolFileMappingList[i].unitId === detail.unit) {
                $scope.unitDetailList[i].checked = isChecked;
            }
        }
    }

    $scope.changeAllUnitsVersion = function (newVersion, selected) {
        for (var i = 0; i < $scope.unitDroolFileMappingList.length; i++) {
            if (selected) {
                $scope.unitDroolFileMappingList[i].updatedVersion = newVersion;
            }
            else {
                $scope.unitDroolFileMappingList[i].updatedVersion = null;
            }
        }
    }

    $scope.changeAllStatus = function (newStatus, selected) {
        for (var i = 0; i < $scope.unitDroolFileMappingList.length; i++) {
            if (selected) {
                $scope.unitDroolFileMappingList[i].updatedStatus = newStatus;
            }
            else {
                $scope.unitDroolFileMappingList[i].updatedStatus = null;
            }
        }
    }

    $scope.updateAll = function (selected) {
        for (var i = 0; i < $scope.unitDroolFileMappingList.length; i++) {
            $scope.unitDroolFileMappingList[i].checked = selected;
        }
    }

    $scope.submitDetails = function () {
        $("#updatedUnitVersionModal").modal("show");
    }

    $scope.submitUpdatedMappingList = function () {

        $rootScope.showFullScreenLoader = true;

        var updateMappingList = [];

        for (var i = 0; i < $scope.unitDroolFileMappingList.length; i++) {
            var mapping = $scope.unitDroolFileMappingList[i];
            if (mapping.checked != null && mapping.checked) {
                updateMappingList.push(mapping);
            }
        }

        console.log("Request data for unit drool mapping :::::: ", updateMappingList);

        $http({
            method: 'POST',
            url: AppUtil.restUrls.unitDroolMetadata.updateUnitDroolMapping + "?droolFile=" + $scope.selectedDroolFileType + "&updatedBy=" + $rootScope.userData.id,
            data: updateMappingList
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status) {
                alert("Mapping updated successfully !!!!!!");
                window.location.reload();
            }
            else {
                alert("Mapping cannot be updated !!!!!!");
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
            alert("Mapping cannot be updated !!!!!!");
        });
    }

});
