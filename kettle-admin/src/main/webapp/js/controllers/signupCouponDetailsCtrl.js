/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


adminapp.controller("signupCouponDetailsCtrl", function ($rootScope, $scope , AppUtil, $http) {

    $scope.init = function () {
        $scope.initial();
    }

    $scope.initial =function (){
        $scope.couponCode=null;
        $scope.couponPrefix=null;
        $scope.couponStatus=null;
        $scope.couponValidity=null;
    }

    $scope.updateCouponMapping =function (){
        var payload = {
            couponCode : $scope.couponCode,
            couponStatus : "ACTIVE",
            couponPrefix : $scope.couponPrefix,
            couponValidity : $scope.couponValidity
        };

        if(AppUtil.isEmptyObject($scope.couponCode) && AppUtil.isEmptyObject($scope.couponValidity) && AppUtil.isEmptyObject($scope.couponPrefix))
        {
            alert("All fields empty!!!!")
            return;
        }
        else if(AppUtil.isEmptyObject($scope.couponCode))
        {
            alert("Invalid Coupon Code!!!!")
            return;
        }
        else if(AppUtil.isEmptyObject($scope.couponValidity))
        {
            alert("Invalid Coupon Validity!!!!")
            return;
        }
        else if(AppUtil.isEmptyObject($scope.couponPrefix))
        {
            alert("Invalid Coupon Prefix!!!!")
            return;
        }
        $rootScope.showDetailLoader = true;
        $http({
            method : 'POST',
            url : AppUtil.restUrls.offerManagement.setSignupCouponMapping ,
            data : payload
        }).then(function success(response) {
            if(response.data && response.status==200) {
                alert("Coupon Added Successfully!!!!")
                console.log(response)
            }
            else
            {
                alert("Error Occurred!!")
            }
            $rootScope.showDetailLoader = false;

        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showDetailLoader = false;
        });
    }
});