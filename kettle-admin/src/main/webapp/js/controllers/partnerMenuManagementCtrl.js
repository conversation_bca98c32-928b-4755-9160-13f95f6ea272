/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerMenuManagementCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http', 'zomatoCatalogService', 'zomatoNewCatalogService', 'zomatoSingleServeCatalogService', 'swiggyCatalogServiceV1', 'swiggyCatalogService', '$cookieStore','magicPinCatalogService','fileService',
        function ($location, $scope, AppUtil, $rootScope, $http, zomatoCatalogService, zomatoNewCatalogService, zomatoSingleServeCatalogService, swiggyCatalogServiceV1, swiggyCatalogService, $cookieStore , magicPinCatalogService,fileService) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.getUnitList();
                $scope.getPricingUnits();
                $scope.getChannelPartnersList();
                $scope.getAllBrands();
                $scope.selectedMenuType = null;
                $scope.menuType = ["DEFAULT", "DAY_SLOT_BREAKFAST", "DAY_SLOT_LUNCH"
                    , "DAY_SLOT_EVENING", "DAY_SLOT_DINNER", "DAY_SLOT_POST_DINNER", "DAY_SLOT_OVERNIGHT", "SINGLE_SERVE"];
                $scope.isSelectedPartnerZomato = false;
                $scope.actionList = ["UPDATE UNIT MENU", "UPDATE BULK MENU", "PUSH UNIT MENU", "MANAGE OFFER", "BOGO PRODUCTS", "CAFE MENU AUTO PUSH"
                    , "PRODUCT PACKAGING CHARGES" , "UPLOAD MENU MAPPINGS"];
                $scope.selectedAction = "UPDATE UNIT MENU";
                $scope.selectAction($scope.selectedAction);
                $scope.splitAllDesiChaiDimensions = false;
                $scope.prodDetails = {};
                $scope.unitPartnerZomato = false;
                $scope.unitPartnerSwiggy = false;
                $scope.checked = false;
                $scope.zomato = false;
                $scope.Swiggy = false;
                $scope.updatedList = [];
                $scope.isBulkUpload = false;
                $scope.userDetails=AppUtil.getUserValues();
                $scope.addTimings = false;
                $scope.newTimings = false;
                $scope.priceProfileUnitId = null;
                $scope.productPackagingMapping=null;
                $scope.productCityImageDetails=null;
                $scope.bulkSelectedDimension=false;
                initCheckBoxModal();
            };

            $scope.setSelectedMenuType = function (menuType) {
                $scope.selectedMenuType = menuType;
            };

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                switch (action) {
                    case "ADD MENU":
                        $scope.initAddMenu();
                        break;
                    case "UPDATE MENU":
                        $scope.initUpdateMenu();
                        break;
                    case "UPDATE UNIT MENU":
                        $scope.initUpdateUnitMenu();
                        break;
                    case "UPDATE BULK MENU":
                        $scope.initUpdateBulkMenu();
                        break;
                    case "MANAGE OFFER":
                        $scope.initManageOffer();
                        break;
                    case "BOGO PRODUCTS":
                        $scope.initManageOffer();
                        break;
                    case "PUSH UNIT MENU":
                        $scope.initPushMenu();
                        break;
                    case "CAFE MENU AUTO PUSH":
                        $scope.initMenuAutoPush();
                        break;
                    case "PRODUCT PACKAGING CHARGES":
                        initProductPackagingModel();
                    case "UPLOAD MENU MAPPINGS":
                        initMappingUploadModal();
                        break;
                }
            };
            $scope.initMenuAutoPush = function () {
            };

            function initCheckBoxModal() {
                $scope.checkBoxModal = {};
                $scope.checkBoxModal.checkAll = false;
                $scope.checkBoxModal.checkAllZomato = false;
                $scope.checkBoxModal.checkAllSwiggy = false;
                $scope.partnerUnitList = [];
                $scope.requestObject = [];
            }

            function initProductPackagingModel() {
                $scope.resetPricingUnitList();
                $scope.resetProductList();
            }

            function initMappingUploadModal(){
               $scope.sheetNames = [

                   ];
                   $scope.sheetNamesByMenuApp = {
                    "CHANNEL_PARTNER" : ["CATEFORY SEQUENCING",
                    "CAFE GROUP MAPPING",
                    "SWIGGY RECOMMENDATION",
                    "MENU SEQUENCE GROUP MAPPING",
                    "GROUP UPSELLING MAPPING"]
                    ,
                    "KETTLE" : ["CATEFORY SEQUENCING","CAFE GROUP MAPPING","MENU SEQUENCE GROUP MAPPING"],
                    "DINE_IN_APP" : ["CATEFORY SEQUENCING","CAFE GROUP MAPPING","MENU SEQUENCE GROUP MAPPING"]
                   }
                    $scope.selectedSheets = []; // To store selected sheet names
                    $scope.file = null;
                    $scope.menuApps = ["CHANNEL_PARTNER", "KETTLE", "DINE_IN_APP"];
                    $scope.selectedMenuApp = null;
                    $scope.lastUploadTime = null;
                    $scope.lastUploadedBy = null;
                    $scope.lastUploadedFileUrl = null;
                    $scope.errorsList = [];
                    $scope.showMenuMappingsExcelDownload = false;
                    $scope.model = {changeLogs : {}};
                    //$scope.changeLogs = {};
            }

             $scope.onMenuAppChange = function(menuApp) {
                    $scope.selectedMenuApp = menuApp ;
                    $scope.sheetNames = $scope.sheetNamesByMenuApp[menuApp];
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.masterMetaData.getMenuMappingsEvent + "?menuApp=" + menuApp
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null && response.data != "") {
                            $scope.lastUploadTime = AppUtil.formatDate(response.data.uploadTime, "yyyy-MM-dd hh:mm:ss");
                            $scope.lastUploadedBy = response.data.uploadedBy;
                            $scope.showMenuMappingsExcelDownload = true;
                        } else {
                            $scope.lastUploadTime = null;
                             $scope.lastUploadedBy = null;
                            $scope.showMenuMappingsExcelDownload = false;
                        }
                    }, function error(response) {
                           $scope.lastUploadTime = null;
                           $scope.lastUploadedBy = null;
                           $scope.showMenuMappingsExcelDownload = false;
                    });

                }

                $scope.downloadMappingExcel = function(menuApp , template){
                    $rootScope.showDetailLoader = true;
                    $http(
                        {
                            method: 'GET',
                            url: AppUtil.restUrls.masterMetaData.downloadMenuMappingsSheet + "?menuApp=" + menuApp + "&isTemplate="
                            + template,
                            responseType: 'arraybuffer',
                            headers: {
                                'Content-type': 'application/json'
                            }
                        })
                        .then(
                            function success(response) {
                                if (response !== undefined && response.status===200) {
                                    $rootScope.showDetailLoader = false;
                                    var fileName ="Download_Template_MENU_MAPPING_" + menuApp
                                        + Date.now() + ".xlsx";
                                    var blob = new Blob(
                                        [response.data],
                                        {
                                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                        }, fileName);
                                    saveAs(blob, fileName);
                                }else {
                                    $rootScope.showDetailLoader = false;
                                    bootbox.alert("Error while downloading Menu Mappings template");
                                }

                            },
                            function error(response) {
                                console.log("error:" + response);
                                bootbox.alert("Error while downloading Menu Mappings template");
                                $rootScope.showDetailLoader = false;
                            });
                }

            $scope.toggleSheetSelection = function(sheet) {
                    const index = $scope.selectedSheets.indexOf(sheet);
                    if (index > -1) {
                        $scope.selectedSheets.splice(index, 1);
                    } else {
                        $scope.selectedSheets.push(sheet);
                    }
                };

         

                $scope.onFileChange = function(event) {
                    var inputElement = event.target;
                    $scope.file = inputElement.files[0];

                    // Apply scope changes
                    $scope.$apply();

                    // Reset the file input value to allow re-uploading the same file
                    inputElement.value = null;
                };



                $scope.uploadAndParse = function() {
                    $scope.errorsList = [];
                    $scope.model.changeLogs={};
                    if (!$scope.file || $scope.selectedSheets.length === 0) {
                        alert("Please select a file and at least one sheet.");
                        return;
                    }
                    if ($scope.selectedMenuApp == null) {
                        alert("Please select a Menu App ");
                        return;
                    }

                    var formData = new FormData();
                    formData.append('file', $scope.file);
                    formData.append('selectedSheets', $scope.selectedSheets.join(','));
                    formData.append('menuApp', $scope.selectedMenuApp);
                     $rootScope.showDetailLoader = true;
                     $http({
                            method: 'POST',
                            url : AppUtil.restUrls.masterMetaData.uploadMenuMappings,
                            data: formData,
                           headers: {
                             'Content-Type': undefined,
                           },
                          transformRequest: angular.identity
                    }).then(function(response) {
                        $rootScope.showDetailLoader = false;
                        if(response !== undefined && response.status===200){
                            $scope.file = null;
                           $scope.errorsList = response.data.errorsList || [];
                            //$scope.model.changeLogs = response.data.changeLog;
                            if($scope.errorsList.length ==0){
                                   bootbox.alert("Successfully Saved Mappings!!");
                           }
                         }else{
                           bootbox.alert("Error while parsing Uploaded Excel : " + response.data.errorMessage);
                         }

                        //alert("File parsed Response :  " + '\n' + JSON.stringify(response.data.response));
                    }).catch(function(error) {
                        $scope.file = null;
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error parsing file: " + error);
                    });
                };

                $scope.copyErrorsToClipboard = function() {
                        navigator.clipboard.writeText($scope.errorsList.join('\n')).then(function() {
                            bootbox.alert("Errors copied to clipboard.");
                        }).catch(function(error) {
                            bootbox.alert("Failed to copy errors: " + error);
                        });
                    };

            $scope.getCafeUnitList = function () {
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.brandPartnerMapping,
                    params: {
                        brandId: $scope.selectedBrand.brandId
                    }
                }).then(function success(response) {
                    initCheckBoxModal();
                    $rootScope.showDetailLoader = false;
                    $scope.partnerUnitList = response.data;
                    if ($scope.partnerUnitList.length === 0) {
                        bootbox.alert("No mapping found ");
                    }
                    $scope.partnerUnitList.forEach(function (partnerUnit) {
                        partnerUnit.unitName = $scope.unitMap[partnerUnit.unitId]
                    });
                    $scope.partnerUnitList = $scope.partnerUnitList.filter(function (partnerUnit) {
                        return partnerUnit.unitName !== undefined;
                    });
                    $scope.partnerUnitList = $scope.partnerUnitList.filter(function (partnerUnit) {
                        return (partnerUnit.unitPartnerZomato === true || partnerUnit.unitPartnerSwiggy === true);
                    });
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.partnerMetadata.cafeAutoPushData,
                        params: {
                            brandId: $scope.selectedBrand.brandId
                        }
                    }).then(function success(response) {
                        $scope.updatedList = response.data;
                        $scope.mappingList();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.updateCheck = function () {
                if ($scope.checkBoxModal.checkAll === true) {
                    for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                        $scope.partnerUnitList[i].checked = true;
                    }
                } else if ($scope.checkBoxModal.checkAll === false) {
                    for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                        $scope.partnerUnitList[i].checked = false;
                    }
                }
            };

            $scope.updateZomato = function () {
                if ($scope.checkBoxModal.checkAllZomato === true) {
                    for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                        if ($scope.partnerUnitList[i].unitPartnerZomato === true) {
                            $scope.partnerUnitList[i].checked = true;
                            $scope.partnerUnitList[i].zomato = true;
                        }
                    }
                } else if ($scope.checkBoxModal.checkAllZomato === false) {
                    for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                        if ($scope.partnerUnitList[i].unitPartnerZomato === true) {
                            $scope.partnerUnitList[i].checked = false;
                            $scope.partnerUnitList[i].zomato = false;
                        }
                    }
                }
            };

            $scope.updateSwiggy = function () {
                if ($scope.checkBoxModal.checkAllSwiggy === true) {
                    for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                        if ($scope.partnerUnitList[i].unitPartnerSwiggy === true) {
                            $scope.partnerUnitList[i].checked = true;
                            $scope.partnerUnitList[i].swiggy = true;
                        }
                    }
                } else if ($scope.checkBoxModal.checkAllSwiggy === false) {
                    for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                        if ($scope.partnerUnitList[i].unitPartnerSwiggy === true) {
                            $scope.partnerUnitList[i].checked = false;
                            $scope.partnerUnitList[i].swiggy = false;
                        }
                    }
                }
            };

            $scope.sendUpdatedList = function () {
                $rootScope.showDetailLoader = true;
                $scope.requestObject = [];
                for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                    var request = {};
                    request.unitId = $scope.partnerUnitList[i].unitId;
                    request.unitName = $scope.partnerUnitList[i].unitName;
                    request.brandId = $scope.selectedBrand.brandId;
                    request.zomatoMenu = $scope.partnerUnitList[i].zomato === true;
                    request.swiggyMenu = $scope.partnerUnitList[i].swiggy === true;
                    request.employeeId = $scope.userDetails.userId;
                    $scope.requestObject.push(request);
                }
                if ($scope.requestObject.length > 0) {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.setAutoFlag,
                        data: $scope.requestObject
                    }).then(function success(response) {
                        $rootScope.showDetailLoader = false;
                        if (response.status === 200 && response.data != null) {
                            $scope.updatedList = response.data;
                            bootbox.alert("List updated successfully ");
                        } else {
                            bootbox.alert("Error in updating the cafe menu data.");
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                    });
                } else {
                    bootbox.alert("Please provide details");
                }
            };

            $scope.singleClickZomato = function (unitId, setStatus) {
                for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                    if ($scope.partnerUnitList[i].unitId === unitId && $scope.partnerUnitList[i].unitPartnerZomato === true) {
                        if (setStatus === true) {
                            $scope.partnerUnitList[i].zomato = !$scope.partnerUnitList[i].zomato;
                        }
                        if ($scope.partnerUnitList[i].zomato === false) {
                            $scope.partnerUnitList[i].checked = false;
                        }
                        if ($scope.partnerUnitList[i].zomato === true) {
                            $scope.partnerUnitList[i].checked = true;
                        }
                        break;
                    }
                }
            };

            $scope.singleClickSwiggy = function (unitId, setStatus) {
                for (var i = 0; i < $scope.partnerUnitList.length; i++) {
                    if ($scope.partnerUnitList[i].unitId === unitId && $scope.partnerUnitList[i].unitPartnerSwiggy === true) {
                        if (setStatus === true) {
                            $scope.partnerUnitList[i].swiggy = !$scope.partnerUnitList[i].swiggy;
                        }
                        if ($scope.partnerUnitList[i].swiggy === false) {
                            $scope.partnerUnitList[i].checked = false;
                        }
                        if ($scope.partnerUnitList[i].swiggy === true) {
                            $scope.partnerUnitList[i].checked = true;
                        }
                        break;
                    }
                }
            };

            $scope.mappingList = function () {
                if ($scope.updatedList != null || $scope.updatedList.length > 0) {
                    for (var i = 0; i < $scope.updatedList.length; i++) {
                        for (var j = 0; j < $scope.partnerUnitList.length; j++) {
                            if ($scope.updatedList[i].unitId === $scope.partnerUnitList[j].unitId) {
                                if ($scope.updatedList[i].zomatoMenu === true) {
                                    $scope.partnerUnitList[j].zomato = true;
                                } else if ($scope.updatedList[i].zomatoMenu === false) {
                                    $scope.partnerUnitList[j].zomato = false;
                                }
                                if ($scope.updatedList[i].swiggyMenu === true) {
                                    $scope.partnerUnitList[j].swiggy = true;
                                } else if ($scope.updatedList[i].swiggyMenu === false) {
                                    $scope.partnerUnitList[j].swiggy = false;
                                }
                                break;
                            }
                        }
                    }
                }
            };

            $scope.initAddMenu = function () {
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
            };

            $scope.initUpdateMenu = function () {
                $scope.resetUnitList();
            };

            $scope.initUpdateUnitMenu = function () {
                $scope.filteredUnits = [];
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
                $scope.isBulkUpload = false;
            };

            $scope.initUpdateBulkMenu = function () {
                $scope.filteredUnits = [];
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
                $scope.bulkUploadStarted = false;
                $scope.isBulkUpload = false;
                $scope.bulkAddPackaging = true;
            };

            $scope.initManageOffer = function () {
                $scope.showAddNewOffer = false;
                $scope.resetUnitList();
            };

            $scope.initPushMenu = function () {
                $scope.resetUnitList();
                $scope.trackSwiggyMenuStatus = null;
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                    $scope.unitMap = {};
                    $scope.unitList.map(function (unit) {
                        $scope.unitMap[unit.id] = unit.name;
                    });
                    $scope.getRegions();
                });
            };

            $scope.getPricingUnits = function () {
                var pricingUnitGetUrl = AppUtil.restUrls.unitMetaData.activeUnits + "?category=" + "COD";
                $http({
                    method: 'GET',
                    url: pricingUnitGetUrl
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        $scope.pricingUnits = response.data;
                    } else {
                        bootbox.alert('Error getting pricing unit list.');

                    }
                }, function error(response) {
                    bootbox.alert('Error getting pricing unit list.');
                });
            }


            function getSelectedPricingUnits() {
                return $scope.pricingUnits.filter(function (pricingUnit) {
                    return pricingUnit.selected;
                }).map(function(unit){
                    return unit.id;
                });
            }

            function getSelectedProductsForPackaging(){
                return $scope.productList.filter(function(product){
                    return product.selected;
                }).map(function(filteredProduct){
                    return filteredProduct.detail.id;
                });
            }


            $scope.getPartnerProducts = function () {
                if ($scope.pricingUnits != null) {
                    $rootScope.showFullScreenLoader = true;
                    var selectedPricingUnits = getSelectedPricingUnits();
                    if (selectedPricingUnits != null && selectedPricingUnits.length > 0) {
                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.unitMetaData.partnerProductsTrimmed,
                            data: selectedPricingUnits
                        }).then(function success(response) {
                                if (response.status === 200 && response.data != null) {
                                     $scope.productList = response.data;
                                }
                                $rootScope.showFullScreenLoader = false;
                            }, function error(response) {
                                console.log('Error in getting Pricing Unit Products', response);
                                $rootScope.showFullScreenLoader = false;
                            });
                    } else {
                        bootbox.alert("Please Select Unit.");
                    }

                } else {
                    bootbox.alert("Please select unit.");
                }
            };


            $scope.downloadProductPackagingTemplate = function () {
                if ($scope.pricingUnits != null) {
                    $rootScope.showFullScreenLoader = true;
                    var selectedPricingUnits = getSelectedPricingUnits();
                    if($scope.productList !=null){
                        var selectedProducts = getSelectedProductsForPackaging();
                        if(selectedProducts != null){
                           var obj = {
                            pricingUnitIds : selectedPricingUnits,
                            productIds : selectedProducts
                           };
                            $http(
                                {
                                    method: 'POST',
                                    url: AppUtil.restUrls.productMetaData.downloadProductPackagingTemplate,
                                    responseType: 'arraybuffer',
                                    headers: {
                                        'Content-type': 'application/json',
                                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    },
                                    data: obj
                                })
                                .then(
                                    function success(response) {
                                        $rootScope.showFullScreenLoader = false;
                                        if (response != undefined && response != null) {
                                            var fileName = obj.unitCategory + " - " + "UNIT_PROD_PACKAGING_SHEET" + " - " + Date.now() + ".xlsx";
                                            var blob = new Blob(
                                                [response.data],
                                                {
                                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                                }, fileName);
                                            saveAs(blob, fileName);
                                        }
                                    },
                                    function error(response) {
                                        $rootScope.showFullScreenLoader = false;
                                        console.log("error:" + response);
                                        alert("Unable to download Sheet");
                                    });
                        }else{
                            bootbox.alert("Please Select Products.");
                        }
                    }else{
                        bootbox.alert("Please Select Products.");
                    }


                } else {
                    bootbox.alert("Please select units.");
                }
            };

            $scope.isUndefinedOrNull = function (val) {
                return angular.isUndefined(val) || val === null || val === "";
            };

            $scope.uploadProductPackagingSheet = function(){
                if (fileService.getFile() == null || fileService.getFile() == undefined) {
                    bootbox.alert("Please select a .xlsx file");
                    return;
                }
                console.log("File is", fileService.getFile());
                if (confirm("Do you want to Upload and Update Packaging?")) {
                    var fd = new FormData();
                    fd.append("file", fileService.getFile());
                    fd.append("updatedBy", $rootScope.userData.id);
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: AppUtil.restUrls.productMetaData.uploadProductPackagingSheet,
                        method: 'POST',
                        data: fd,
                        headers: {
                            'Content-Type': undefined,
                        },
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        angular.element("input[type='file']").val(null);
                        fileService.push(null);
                        //$scope.productPriceFile = null;
                        console.log(response);
                        if (response.errorMessage == null) {
                            var alertMessage = "File Uploaded";
                            alert(alertMessage);
                            window.location.reload();
                        }
                        else {
                            alert(response.errorMessage);
                        }

                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        alert("Error while uploading Unit Product Packaging Sheet");
                    });
                }
                ;
            }

            $scope.resetUnitList = function () {
                if ($scope.unitList != null) {
                    $scope.unitList.map(function (unit) {
                        unit.selected = false;
                    });
                }
            };

            $scope.resetPricingUnitList = function () {
                if ($scope.pricingUnits != null) {
                    $scope.pricingUnits.map(function (unit) {
                        unit.selected = false;
                    });
                }
            };

            $scope.resetProductList = function () {
                if ($scope.productList != null) {
                    $scope.productList.map(function (product) {
                        product.selected = false;
                    });
                }
            }

            $scope.expandUnit = function (unit) {
                $scope.setSelectedUnit(unit);
                $scope.addMenuObj = null;
                $scope.showMenuObj = null;
                $scope.filteredUnits.map(function (u) {
                    if (u.id === unit.id) {
                        u.expand = !u.expand;
                    } else {
                        u.expand = false;
                    }
                });
                if ($scope.selectedMenuType != 'SINGLE_SERVE') {
                    $scope.showUnitMenuSequenceVersion();
                }
            };

            $scope.getRegions = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.regions
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.regions = [];
                        response.data.map(function (region, index) {
                            $scope.regions.push({ id: index, name: region });
                        });
                    } else {
                        bootbox.alert("Error getting unit regions.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getChannelPartnersList = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerManagement.get
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.channelPartners = response.data;
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error getting partner list.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.resetChannelPartnersList = function () {
                if ($scope.channelPartners != null) {
                    $scope.channelPartners.map(function (partner) {
                        partner.selected = false;
                    });
                }
            };

            $scope.getAllBrands = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.brandManagement.getAllBrands
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.brands = response.data;
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error getting brands.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            /*$scope.getUnitProducts = function () {
                if ($scope.productUnit != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.unitPartnerProductsTrimmed + "?unitId=" + $scope.productUnit.id,
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.unitProductList = response.data;
                            $scope.showUnitProducts = true;
                        } else {
                            bootbox.alert("Error getting unit product list.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit.");
                }
            };*/

            $scope.resetRegions = function () {
                $scope.selectedRegion = null;
                $scope.filteredUnits = [];
                $scope.resetUnitList();
            };

            $scope.setSelectedRegion = function (selectedRegion) {
                $scope.showAddNewOffer = false;
                $scope.selectedRegion = selectedRegion;
            };

            $scope.setFilteredUnits = function () {
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner.");
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand.");
                    return;
                } else if ($scope.selectedMenuType == null) {
                    bootbox.alert("Please select menu type");
                    return;
                }
                $scope.filteredUnits = [];
                var addedUnits = [];
                $scope.unitList.map(function (unit) {
                    unit.isNew = true;
                    unit.newMenu = true;
                    unit.addPackaging = true;
                    unit.selected = false;
                    $scope.unitChannelPartnerMappings.map(function (mapping) {
                        if (mapping.unit.id === unit.id && mapping.channelPartner.id === $scope.selectedPartner.kettlePartnerId
                            && mapping.status === "ACTIVE") {
                            if (!addedUnits.includes(unit.id)) {
                                $scope.filteredUnits.push(unit);
                                addedUnits.push(unit.id);
                            }
                        }
                    });
                });
            };

            $scope.setSelectedBrand = function (selectedBrand) {
                $scope.selectedBrand = selectedBrand;
            };

            $scope.selectUnit = function (unit) {
                unit.selected === true ? unit.selected = false : unit.selected = true;
                $scope.showAddNewOffer = false;
            };

            $scope.selectProduct = function (product) {
                product.selected === true ? product.selected = false : product.selected = true;
            };



            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                if ($scope.selectedAction === 'ADD MENU') {
                    $scope.currentMenu = null;
                }
            };

            $scope.setSelectedPartner = function (selectedPartner) {
                $scope.showAddNewOffer = false;
                if (selectedPartner.partnerName !== "ZOMATO") {
                    $scope.isSelectedPartnerZomato = true;
                    /* $scope.selectedMenuType= $scope.menuType[0];
                     $scope.setSelectedMenuType($scope.menuType[0]);*/
                    $scope.selectedMenuType = null;
                } else {
                    $scope.isSelectedPartnerZomato = false;
                    $scope.selectedMenuType = null;
                }
                $scope.selectedPartner = selectedPartner;
                if ($scope.selectedAction === 'ADD MENU') {
                    $scope.currentMenu = null;
                }
                if ($scope.unitChannelPartnerMappings == null || $scope.unitChannelPartnerMappings.length === 0) {
                    $scope.getUnitChannelPartnerMapping();
                }
            };

            $scope.getUnitChannelPartnerMapping = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getAllUnitChannelPartnerMapping
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitChannelPartnerMappings = response.data;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getMenuToAdd = function () {
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner.");
                } else if ($scope.selectedRegion == null) {
                    bootbox.alert("Please select region.");
                } else if ($scope.selectedUnit == null) {
                    bootbox.alert("Please select unit.");
                } else {
                    $scope.getUnitProductProfile();
                }
            };

            $scope.getUnitMenuToAdd = function () {
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner.");
                }/* else if ($scope.selectedRegion == null) {
                    bootbox.alert("Please select region.");
                }*/ else if ($scope.selectedMenuType == null) {
                    bootbox.alert("Please select menu type");
                } else {
                    $scope.getUnitBrandPartnerMapping();
                    $scope.getUnitProductProfile();
                }
            };

            $scope.getUnitBrandPartnerMapping = function () {
                var brandPartnerKey = {
                    partnerId : $scope.selectedPartner.kettlePartnerId,
                    unitId : $scope.selectedUnit.id,
                    brandId : $scope.selectedBrand.brandId
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.brandMetaData.getAllUnitPartnerBrandMapping,
                    data : brandPartnerKey    
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.priceProfileUnitId = response.data.priceProfileUnitId;
                    } else {
                        bootbox.alert("Error getting Brand Partner Mapping");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            }


            $scope.removeDealOfTheDayProducts = function () {

                var prodDimension = {};

                $scope.selectedProductsDTDO.map(function (product) {
                    prodDimension[product.productId] = [];
                    product.dotdProductDimensionsList.map(function (value) {
                        prodDimension[product.productId].push(value.dimensionCode);
                    })
                })

                console.log(prodDimension);
                var indexProd = 0;
                console.log("UNIT PRODUCTS BEFORE DOTD:  ", $scope.unitProductProfile.products);
                var idsToRemove = [];
                $scope.unitProductProfile.products.map(function (product) {
                    var idx = 0;
                    if (prodDimension[product.id] !== undefined) {
                        if (product.prices.length > 0) {

                            var index = product.prices.length - 1;

                            for (var i = index; i >= 0; i--) {
                                var flag = false;
                                prodDimension[product.id].map(function (value) {
                                    if (!flag && value === product.prices[i].dimension) {
                                        product.prices.splice(i, 1);
                                        flag = true;
                                        return;
                                    }
                                })

                            }

                        }


                        if (product.prices.length === 0) {
                            console.log($scope.unitProductProfile.products[indexProd]);
                            idsToRemove.push(indexProd);
                            console.log($scope.unitProductProfile.products[indexProd]);
                        }
                        indexProd++;
                    }
                    else {
                        indexProd++;
                    }


                })

                for (var i = idsToRemove.length - 1; i >= 0; i--) {
                    $scope.unitProductProfile.products.splice(idsToRemove[i], 1);
                }


                console.log("UNIT PRODUCTS AFTER DOTD:  ", $scope.unitProductProfile.products);
            }

            $scope.getUnitProductProfile = function () {
                $rootScope.detailLoaderMessage = "Fetching COD menu...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.brandProductProfile + "?partnerId=" + $scope.selectedPartner.kettlePartnerId
                        + "&brandId=" + $scope.selectedBrand.brandId + "&unitId=" + $scope.selectedUnit.id,
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitProductProfile = response.data;
                        $scope.originalUnitProductProfile = angular.copy(response.data);
                        if ($scope.bulkDealOfTheDay == true) {
                            $scope.removeDealOfTheDayProducts();
                        }

                        //$scope.getRecipeData();
                        $scope.getUnitProductForCafe();
                    } else {
                        bootbox.alert("Error getting unit product profile.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getUnitProductForCafe = function () {
                $rootScope.detailLoaderMessage = "Fetching cafe menu...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.unitProducts,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.dineInMenuProfile = response.data;
                        $scope.getRecipeData();
                    } else {
                        bootbox.alert("Error getting unit product profile.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getRecipeData = function () {
                var dineInProductProfileMap = {};
                $scope.dineInMenuProfile.products.map(function (product) {
                    var dimensionProfileMap = {}
                    product.prices.map(function (price) {
                        dimensionProfileMap[price.dimension] = price.profile;
                    });
                    dineInProductProfileMap[product.id] = dimensionProfileMap;
                });
                console.log(dineInProductProfileMap);
                var unit = angular.copy($scope.unitProductProfile);
                var reqObj = [];
                unit.products.map(function (product) {
                    if (product.classification === "MENU" && product.billType !== "ZERO_TAX" && product.type !== 12) {
                        product.prices.map(function (price) {
                            if (dineInProductProfileMap[product.id] != null && dineInProductProfileMap[product.id][price.dimension] != null) {
                                reqObj.push({
                                    productId: product.id,
                                    dimension: price.dimension,
                                    // profile: price.profile
                                    profile: dineInProductProfileMap[product.id][price.dimension]
                                });
                            } else {
                                reqObj.push({
                                    productId: product.id,
                                    dimension: price.dimension,
                                    profile: price.profile
                                });
                            }
                        });
                    }
                });
                $rootScope.detailLoaderMessage = "Fetching web app recipes...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.neoCache.getProductRecipes,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var res = response.data;
                        if (res != null && res.length > 0) {
                            res.map(function (item) {
                                $scope.unitProductProfile.products.map(function (product) {
                                    if (product.id == item.productId) {
                                        product.prices.map(function (price) {
                                            item.recipes.map(function (recipe) {
                                                if (price.dimension === recipe.dimension.code) {
                                                    price.recipe = recipe;
                                                }
                                            });
                                        });
                                    }
                                });
                            });
                        }
                        $scope.getUnitMetaData();
                    } else {
                        bootbox.alert("Error loading web recipes.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getUnitMetaData = function () {
                $rootScope.detailLoaderMessage = "Fetching unit metadata...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.metadata,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata = response.data;
                        $scope.unitMetadata.dineInMenuProfile = $scope.dineInMenuProfile;
                        $scope.getUnitPackagingProfile();
                    } else {
                        bootbox.alert("Error loading unit metadata.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getUnitPackagingProfile = function () {
                $rootScope.detailLoaderMessage = "Fetching unit packaging profile...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.posMetaData.brandPartnerPackagingProfile + "?partnerId=" + $scope.selectedPartner.kettlePartnerId
                        + "&brandId=" + $scope.selectedBrand.brandId + "&unitId=" + $scope.selectedUnit.id,
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var res = response.data;
                        $scope.unitMetadata.packagingType = res.packagingType;
                        $scope.unitMetadata.packagingValue = res.packagingValue;
                        //get filtered products for unit
                        $scope.getFilteredProducts();
                    } else {
                        bootbox.alert("Error loading unit packaging profile.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getFilteredProducts = function () {
                $rootScope.detailLoaderMessage = "Fetching unit filtered products...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductFilter +
                        "?partnerId=" + $scope.selectedPartner.kettlePartnerId + "&brandId=" + $scope.selectedBrand.brandId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.filteredProducts = response.data;
                        //getting product tags
                        $scope.getPartnerProductTagsMappingsDetail();
                    } else {
                        bootbox.alert("Error fetching unit filtered products.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getPartnerProductTagsMappingsDetail = function () {
                $rootScope.detailLoaderMessage = "Fetching product tags...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductTagsMappingsDetail + "?brandId=" + $scope.selectedBrand.brandId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.partnerProductTagsMappings = response.data;
                        //getting Recommendation Data
                        $scope.getPartnerProductMeatTags();
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product tags.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product tags mapping detail response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getPartnerProductMeatTags = function () {
                $rootScope.detailLoaderMessage = "Fetching product meat type tags...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductMeatTagsMappingsDetail + "?brandId=" + $scope.selectedBrand.brandId
                        + '&partnerId=' + $scope.selectedPartner.kettlePartnerId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata.productMeatTags = response.data;
                        //getting Recommendation Data
                        $scope.getPartnerProductAllergenTags();

                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product tags.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product tags mapping detail response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getPartnerProductAllergenTags = function () {
                $rootScope.detailLoaderMessage = "Fetching product Allergen type tags...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductAllergenTagsMappingsDetail + "?brandId=" + $scope.selectedBrand.brandId
                        + '&partnerId=' + $scope.selectedPartner.kettlePartnerId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata.allergenInfoSizeTags = {};
                        $scope.unitMetadata.allergenInfoSizeTags.partnerProductsAllergenTags = response.data;
                        //getting Recommendation Data
                        $scope.getPartnerProductServingInfoTags();
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product tags.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product tags mapping detail response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getPartnerProductServingInfoTags = function () {
                $rootScope.detailLoaderMessage = "Fetching product serving info type tags...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductServingInfoTagsMappingsDetail + "?brandId=" + $scope.selectedBrand.brandId
                        + '&partnerId=' + $scope.selectedPartner.kettlePartnerId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata.allergenInfoSizeTags.partnerProductsServingInfoTags = response.data;
                        //getting Recommendation Data
                        $scope.getPartnerProductServingSizeTags();
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product tags.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product tags mapping detail response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getPartnerProductServingSizeTags = function () {
                $rootScope.detailLoaderMessage = "Fetching product Serving Size type tags...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerProductServingSizeTagsMappingsDetail + "?brandId=" + $scope.selectedBrand.brandId
                        + '&partnerId=' + $scope.selectedPartner.kettlePartnerId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata.allergenInfoSizeTags.partnerProductsServingSizeTags = response.data;
                        //getting Recommendation Data
                        $scope.getProductNutritionDetail();
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product tags.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product tags mapping detail response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };


            $scope.getProductNutritionDetail = function () {
                $rootScope.detailLoaderMessage = "Fetching product nutrition detail...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                var productIds = [];
                $scope.unitProductProfile.products.map(function (product) {
                    productIds.push(product.id)
                });
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.productMetaData.getNutritionDetail,
                    data: productIds
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata.productNutrition = response.data;
                        //getting Recommendation Data
                        $scope.getRecommendations();
                        getProductPackagingMappings();
                        getProductCityImageMappings();
                        $scope.getFilteredProductForNoPackagingCharges();
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product nutrition detail.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product nutrition detail response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getFilteredProductForNoPackagingCharges = function () {
                $rootScope.detailLoaderMessage = "Fetching products for not having packaging charges...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getFilteredProductForNoPackagingCharges + "?brandId=" + $scope.selectedBrand.brandId
                        + '&partnerId=' + $scope.selectedPartner.kettlePartnerId,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitMetadata.filterProductsForNoPackagingCharges = response.data;
                        //getting Recommendation Data
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product tags.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product products for not having packaging charges', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };


            function getProductPackagingMappings() {
                $rootScope.detailLoaderMessage = "Fetching product Packaging Mappings...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.productMetaData.downloadProductPackagingMapping + "?pricingUnitId=" + $scope.priceProfileUnitId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.productPackagingMapping = response.data;
                        //getting Recommendation Data
                    } else {
                        $rootScope.showDetailLoader = false;
                        bootbox.alert("Error fetching product packaging Mapping.");
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log('Error in getting product packaging Mappings', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

             function getProductCityImageMappings() {
                            $rootScope.detailLoaderMessage = "Fetching product city image  Mappings...";
                            if (!$scope.isBulkUpload) {
                                $rootScope.showDetailLoader = true;
                            }
                             $http({
                                           method: 'POST',
                                            url: AppUtil.restUrls.productMetaData.uploadedProductImagesByCity + "?cityName=" + $scope.selectedUnit.city
                                             + "&daySlot=" + $scope.selectedMenuType,
                                        }).then(function success(response) {
                                            console.log("success:" + response);
                                            $rootScope.showFullScreenLoader = false;
                                            if (response != null && response.status == 200) {
                                                $scope.productCityImageDetails = response.data;
                                                console.log("Product Image details ----1>>>",$scope.productImageDetails);
                                            } else {
                                                console.log("error:" + response);
                                            }
                                        }, function error(response) {
                                            console.log("error:" + response);
                                            $rootScope.showFullScreenLoader = false;
                                        });
                        };

            $scope.getRecommendations = function () {
                $rootScope.detailLoaderMessage = "Fetching Products Recommendation...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getRecommendation
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.recommendations = response.data;
                        $scope.recommendations.map(function (data) {
                            var map = {};
                            data.recommendationProduct.map(function (recommendation) {
                                //recomender prod id -> to all its' recommended items
                                var productRecommendationGroup = {
                                    recommendationTitle: recommendation.recommendationTitle,
                                    recommendedItemsList: recommendation.recommendation
                                };

                                var productRecommendationGroupList = map[recommendation.productId];
                                if (productRecommendationGroupList === undefined) {
                                    productRecommendationGroupList = [];
                                    map[recommendation.productId] = productRecommendationGroupList;
                                }
                                productRecommendationGroupList.push(productRecommendationGroup);
                                // map[recommendation.productId] = recommendation.recommendation;
                            });
                            data.recommendationProduct = map;
                        });
                        $scope.getProductImages();
                    } else {
                        bootbox.alert("Error getting recommendation list.");
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getProductImages = function () {
                $rootScope.detailLoaderMessage = "Fetching Products Images...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                var productIds = [];
                $scope.unitProductProfile.products.map(function (product) {
                    productIds.push(product.id)
                });
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.productMetaData.uploadedProductImagesById,
                    data: productIds
                }).then(function success(response) {
                    console.log("success:" + response);
                    if (response != null && response.status === 200) {
                        var data = {};
                        response.data.list.map(function (img) {
                            data[img.productId] = img;
                        });
                        $scope.productImageDetails = data;
                        $scope.getDesiChaiCustomProfiles();
                    } else {
                        console.log("error:" + response);
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getProductDimensionImages = function () {
                            $rootScope.detailLoaderMessage = "Fetching Products Images...";
                            if (!$scope.isBulkUpload) {
                                $rootScope.showDetailLoader = true;
                            }
                            var productIds = [];
                            $scope.unitProductProfile.products.map(function (product) {
                                productIds.push(product.id)
                            });
                            $http({
                                method: 'POST',
                                url: AppUtil.restUrls.productMetaData.uploadedProductDimensionImagesById,
                                data: productIds
                            }).then(function success(response) {
                                console.log("success:" + response);
                                if (response != null && response.status === 200) {
                                    var data = {};
                                    response.data.list.map(function (img) {
                                        data[img.productId] = img;
                                    });
                                    $scope.productImageDetails = data;
                                    $scope.getDesiChaiCustomProfiles();
                                } else {
                                    console.log("error:" + response);
                                    $rootScope.showDetailLoader = false;
                                    if ($scope.isBulkUpload === true) {
                                        $scope.bulkUploadCallback(false);
                                    }
                                }
                            }, function error(response) {
                                console.log("error:" + response);
                                $rootScope.showDetailLoader = false;
                                if ($scope.isBulkUpload === true) {
                                    $scope.bulkUploadCallback(false);
                                }
                            });
                        };

            $scope.getDesiChaiCustomProfiles = function () {
                $rootScope.detailLoaderMessage = "Fetching Desi Chai Profiles...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getDcCustomProfilesForUnit,
                    data: $scope.selectedUnit.id,
                    params: {
                        partnerId: $scope.selectedPartner.kettlePartnerId
                    }
                }).then(function success(response) {
                    console.log("success:" + response);
                    if (response != null && response.status === 200) {
                        $scope.unitMetadata.desiChaiCustomProfiles = response.data;
                        $scope.getUnitMenuSequence();
                    } else {
                        console.log("error:" + response);
                        $rootScope.showDetailLoader = false;
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getUnitMenuSequence = function () {
                $rootScope.detailLoaderMessage = "Fetching menu sequence...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $scope.viewMenu = false;
                var request = {
                    unitId: $scope.selectedUnit.id,
                    //region: $scope.selectedRegion.name,
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    brandId: $scope.selectedBrand.brandId,
                    menuType: $scope.selectedMenuType
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.getMenuSequence,
                    data: request
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.menuSequence = response.data;
                        if (response.data.dynamicPriceProfileId != null) {
                            $scope.getDynamicPriceProfile();
                        } else {
                            $scope.renderMenu();
                        }
                    } else {
                        bootbox.alert("Error loading unit menu sequence. Please make sure you have mapped menus correctly." + $scope.selectedUnit.name);
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.getDynamicPriceProfile = function () {
                $rootScope.detailLoaderMessage = "Fetching dynamic price profile...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $scope.viewMenu = false;
                var request = {
                    unitId: $scope.selectedUnit.id,
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    brandId: $scope.selectedBrand.brandId,
                    menuType: $scope.selectedMenuType
                };
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getDynamicPriceProfile + "?priceProfileId=" + $scope.menuSequence.dynamicPriceProfileId,
                    data: request
                }).then(function success(response) {
                    if (response.status === 200) {
                        $scope.unitMetadata.dynamicPriceProfile = response.data;
                        $scope.renderMenu();
                    } else {
                        bootbox.alert("Error loading dynamic price profile. Please make sure you have mapped dynamic price profile correctly." + $scope.selectedUnit.name);
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(false);
                        }
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.setAddTimings = function (e) {
                $scope.newTimings = document.getElementById('addTimingId').checked;

            }

            $scope.renderMenu = function () {
                var menuSequence = $scope.menuSequence;
                $scope.menuSequenceId = menuSequence.menuSequenceId;
                menuSequence.timingsApplicable = $scope.newTimings;
                var menuRecomendationData = null;
                $scope.currentMenuSequence = menuSequence;
                console.log("checking partner tags");
                if (menuSequence.menuRecommendationSequenceId != null) {
                    $scope.recommendations.map(function (menuRecommendation) {
                        if (menuRecommendation.menuRecommendationId == menuSequence.menuRecommendationSequenceId) {
                            menuRecomendationData = angular.copy(menuRecommendation.recommendationProduct);
                            console.log("RECOM data", menuRecomendationData);
                        }
                    });
                }
                console.log($scope.partnerProductTagsMappings);
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    //if ($scope.selectedUnit.newMenu == true) {
                    if ($scope.selectedMenuType == "SINGLE_SERVE") {
                        $scope.addMenuObj = zomatoSingleServeCatalogService.prepareCatalogs($scope.selectedUnit.id, $scope.unitProductProfile,
                            $scope.unitMetadata, menuSequence, $scope.selectedBrand, $scope.selectedUnit.addPackaging, $scope.partnerProductTagsMappings,
                            $scope.productImageDetails);
                        console.log(JSON.stringify($scope.addMenuObj));
                        $scope.showMenuObj = JSON.stringify($scope.addMenuObj);
                        $scope.showMenuObj = JSON.parse($scope.showMenuObj);
                    } else {
                        $scope.addMenuObj = zomatoNewCatalogService.prepareCatalogs($scope.selectedUnit, $scope.unitProductProfile,
                            $scope.unitMetadata, menuSequence, $scope.selectedBrand, menuRecomendationData, $scope.productImageDetails
                            ,$scope.productPackagingMapping,$scope.productCityImageDetails);
                        console.log(JSON.stringify($scope.addMenuObj));
                        $scope.validateMenu();
                    }
                }
                if ($scope.selectedPartner.partnerName == "SWIGGY") {
                    $scope.addMenuObj = swiggyCatalogServiceV1.prepareCatalog($scope.selectedUnit, $scope.unitProductProfile, $scope.unitMetadata,
                        menuSequence, menuRecomendationData, $scope.productImageDetails,$scope.productPackagingMapping,$scope.productCityImageDetails);
                    $scope.validateMenu();
                    console.log(JSON.stringify($scope.addMenuObj));
                }

                if ($scope.selectedPartner.partnerName == "MAGICPIN") {
                    $scope.showNonMatchingProductDetails();
                    $scope.addMenuObj = magicPinCatalogService.prepareCatalogs($scope.selectedUnit, $scope.unitProductProfile,
                        $scope.unitMetadata, menuSequence, $scope.selectedBrand, menuRecomendationData, $scope.productImageDetails,$scope.nonMatchedProducts);
                                    $scope.validateMenu();
                                    console.log(JSON.stringify($scope.addMenuObj));
                                }
            };

            $scope.showNonMatchingProductDetails = function () {
                $scope.nonMatchedProducts = [];
                $scope.unitProductProfile.products.map(function (prod) {
                    var productFound = false;
                    $scope.dineInMenuProfile.products.map(function (product) {
                        if (prod.id == product.id) {
                            productFound = !(prod.prices == null || product.prices.length === 0);
                            if (productFound) {
                                prod.prices.map(function (prc) {
                                    var found = false;
                                    product.prices.map(function (price) {
                                        if (price.dimension == prc.dimension) {
                                            found = true;
                                        }
                                    });
                                    if (!found && (prod.classification == "MENU")) {
                                        $scope.currentMenuSequence.productGroupSequences.map(function (cat) {
                                            cat.subGroups.map(function (subCat) {
                                                subCat.productSequenceList.map(function (prodSequence) {
                                                    if (prodSequence.product.id == product.id) {
                                                        $scope.nonMatchedProducts.push({
                                                            id: product.id,
                                                            name: product.name,
                                                            dimension: prc.dimension
                                                        });
                                                    }
                                                });
                                            });
                                        });
                                    }
                                });
                            }
                        }
                    });
                    if (!productFound && ([1043, 1044].indexOf(prod.id) < 0) && (prod.classification == "MENU")) {
                        $scope.currentMenuSequence.productGroupSequences.map(function (cat) {
                            cat.subGroups.map(function (subCat) {
                                subCat.productSequenceList.map(function (prodSequence) {
                                    if (prodSequence.product.id == prod.id) {
                                        $scope.nonMatchedProducts.push({ id: prod.id, name: prod.name, dimension: null });
                                    }
                                });
                            });
                        });
                    }
                });
            };

            $scope.validateMenu = function () {
                if ($scope.selectedMenuType == "SINGLE_SERVE" && $scope.selectedPartner.partnerName == "ZOMATO") {
                    $scope.addZomatoMenuForUnit();
                } else {
                    var valid = true;
                    if ($scope.selectedPartner.partnerName == "ZOMATO") {
                        $scope.addMenuObj.menu.categories.forEach(function (cat) {
                            if (valid && cat.category_name == "Others" && cat.items.length > 0) {
                                valid = false;
                                bootbox.alert("Please set category and subcategory for all the unmapped products.");
                            }
                        });
                    }
                    if ($scope.selectedPartner.partnerName == "SWIGGY") {
                        if ($scope.addMenuObj.unmappedProducts.length > 0) {
                            valid = false;
                            bootbox.alert("Please set category and subcategory for all the unmapped products.");
                        }
                    }
                    if (valid) {
                        $scope.showNonMatchingProductDetails();
                        if ($scope.nonMatchedProducts.length > 0) {
                            console.log($scope.nonMatchedProducts);
                            valid = false;
                            $("#nonMatchedProductsModal").modal("show");
                        }
                    }
                    if (valid) {
                        //$scope.sanitizeMenu();
                        $scope.addPartnerMenuForUnit();
                    }
                }
            };

            $scope.addPartnerMenuForUnit = function () {
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    //$scope.addZomatoMenuForUnit();
                    if ($scope.selectedUnit.newMenu == true) {
                        $scope.sanitizeMenuV3();
                    } else {
                        $scope.sanitizeMenu();
                    }
                    console.log(JSON.stringify($scope.addMenuObj));
                    $("#nonMatchedProductsModal").modal("hide");
                }
                if ($scope.selectedPartner.partnerName == "SWIGGY") {
                    if ($scope.selectedUnit.newMenu == true) {
                        $scope.sanitizeMenuV3();
                    } else {
                        $scope.sanitizeMenu();
                    }
                    /*if ($scope.selectedUnit.newMenu == true) {
                        $scope.addSwiggyMenuForUnit();
                    } else {
                        $scope.addSwiggyMenuForUnitOld();
                        $scope.downloadSwiggyCatalog();
                    }*/
                    console.log(JSON.stringify($scope.addMenuObj));
                    $("#nonMatchedProductsModal").modal("hide");
                }else if($scope.selectedPartner.partnerName =="MAGICPIN"){
                    $scope.prepareShowMenuObj({menuData: $scope.addMenuObj});
                    if ($scope.isBulkUpload === true){
                        $scope.uploadNewMenu();
                       //$scope.bulkUploadCallback(valid);
                    }

                    $("#nonMatchedProductsModal").modal("hide");
                    //$scope.uploadNewMenu();
                }
            };

            $scope.sanitizeMenu = function () {
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    var categories = [];
                    $scope.addMenuObj.menu.categories.map(function (cat) {
                        var category = null;
                        if (cat.category_name != "Others") {
                            category = angular.copy(cat);
                            category.subcategories = [];
                            cat.subcategories.map(function (subCat) {
                                var subCategory = angular.copy(subCat);
                                subCategory.items = [];
                                subCat.items.map(function (item) {
                                    var matched = false;
                                    $scope.nonMatchedProducts.map(function (nProd) {
                                        if (item.item_id == nProd.id) {
                                            matched = true;
                                        }
                                    });
                                    if (!matched && $scope.filteredProducts.indexOf(item.item_id) < 0) {
                                        subCategory.items.push(item);
                                    }
                                });
                                if (subCategory.items.length > 0) {
                                    category.subcategories.push(subCategory);
                                }
                            });
                            category.has_subcategory = (category.subcategories.length > 0) ? 1 : 0;
                        }
                        if (category != null && category.subcategories.length > 0) {
                            categories.push(category);
                        }
                    });
                    if (categories.length > 0) {
                        $scope.addMenuObj.menu.categories = categories;
                    }
                }
            };

            $scope.sanitizeMenuV3 = function () {
                if ($scope.selectedPartner.partnerName == "ZOMATO") {
                    $scope.sanitizeZomatoMenuV3();
                }
                if ($scope.selectedPartner.partnerName == "SWIGGY") {
                    $scope.sanitizeSwiggyMenu();
                }
            };

            $scope.sanitizeZomatoMenuV3 = function () {
                var cataloguesFiltered = [];
                var cataloguesId = [];
                $scope.addMenuObj.menu.catalogues.map(function (catalog) {
                    var matched = false;
                    $scope.nonMatchedProducts.map(function (nProd) {
                        if (catalog.vendorEntityId == nProd.id + "_" + nProd.dimension) {
                            matched = true;
                            cataloguesId.push(catalog.vendorEntityId);
                        } else if (catalog.vendorEntityId.includes("RECOM") || catalog.vendorEntityId.includes("HERO") || catalog.vendorEntityId.includes("SUPERCOMBO"))  {
                                  var prodId = catalog.vendorEntityId.split("_")[0];
                                  var prodDim = catalog.vendorEntityId.split("_")[1];
                                  if ((prodId == nProd.id || prodId  == nProd.id + '') &&
                                       (prodDim == nProd.dimension || (nProd.dimension == null && prodDim == "None") ||
                                         prodDim == 'SUPERCOMBO' || prodDim == 'HEROCOMBO')) {
                                                matched = true;
                                                cataloguesId.push(catalog.vendorEntityId);
                                                $scope.removeModifierGroupEntry(catalog.vendorEntityId);
                                       }
                        } else if (catalog.vendorEntityId == nProd.id) {
                            if (nProd.dimension == null || nProd.dimension == "None") {
                                matched = true;
                                cataloguesId.push(catalog.vendorEntityId);
                            } else {
                                var sizeExist = false;
                                catalog.properties.map(function (catalogProp) {
                                    if (catalogProp.name == 'Size') {
                                        sizeExist = true;
                                        if (catalogProp.propertyValues.length == 0) {
                                            matched = true;
                                            cataloguesId.push(catalog.vendorEntityId);
                                        } else if (catalogProp.propertyValues.length == 1 &&
                                            (catalogProp.propertyValues[0].vendorEntityId).includes(nProd.dimension)) {
                                            matched = true;
                                            cataloguesId.push(catalog.vendorEntityId);
                                        } else if (catalogProp.propertyValues.length != 1) {
                                            var validPropertyValues = [];
                                            var removedPropertyValues = [];
                                            catalogProp.propertyValues.map(function (propValues, index) {
                                                if (!(propValues.value).includes(nProd.dimension)) {
                                                    validPropertyValues.push(propValues);
                                                } else {
                                                    removedPropertyValues.push(propValues);
                                                }
                                            });
                                            catalogProp.propertyValues = validPropertyValues;
                                            var validVariants = [];
                                            catalog.variants.map(function (CatVar, varIndex) {
                                                var skip = false;
                                                CatVar.propertyValues.map(function (varProp) {
                                                    removedPropertyValues.map(function (removedPropValue) {
                                                        if (angular.equals(removedPropValue.vendorEntityId, varProp.vendorEntityId)) {
                                                            skip = true;
                                                        }
                                                    });
                                                });
                                                if (!skip) {
                                                    validVariants.push(CatVar);
                                                }
                                            });
                                            catalog.variants = validVariants;
                                        }
                                    }
                                });
                                if (!sizeExist) {
                                    matched = true;
                                    cataloguesId.push(catalog.vendorEntityId);
                                }
                            }
                        }
                    });
                    if (!matched && $scope.filteredProducts.indexOf(parseInt(catalog.vendorEntityId)) < 0) {
                        cataloguesFiltered.push(catalog);
                    } else if (catalog.vendorEntityId.includes("RECOM") && $scope.filteredProducts.indexOf(parseInt(catalog.vendorEntityId)) > 0) {
                        $scope.removeModifierGroupEntry(catalog.vendorEntityId);
                    }
                });
                $scope.addMenuObj.menu.catalogues = cataloguesFiltered;
                var categories = [];
                $scope.addMenuObj.menu.categories.map(function (cat) {
                    var category = null;
                    if (cat.category_name != "Others") {
                        category = angular.copy(cat);
                        category.subCategories = [];
                        cat.subCategories.map(function (subCat) {
                            var subCategory = angular.copy(subCat);
                            subCategory.entities = [];
                            subCat.entities.map(function (item) {
                                var matched = true;
                                if (cataloguesId.indexOf(item.vendorEntityId) < 0) {
                                    matched = false;
                                }
                                if (!matched && $scope.filteredProducts.indexOf(parseInt(item.vendorEntityId)) < 0) {
                                    subCategory.entities.push(item);
                                }
                            });
                            if (subCategory.entities.length > 0) {
                                category.subCategories.push(subCategory);
                            }
                        });
                        category.has_subcategory = (category.subCategories.length > 0) ? 1 : 0;
                    }
                    if (category != null && category.subCategories.length > 0) {
                        categories.push(category);
                    }
                });
                if (categories.length > 0) {
                    $scope.addMenuObj.menu.categories = categories;
                }
                var modGroupIds = [];
                $scope.addMenuObj.menu.catalogues.map(catalogue => {
                    catalogue.variants.map(variant => {
                        variant.modifierGroups.map(modGroup => {
                            if (!modGroupIds.includes(modGroup.vendorEntityId)) {
                                modGroupIds.push(modGroup.vendorEntityId);
                            }
                        })
                    })
                });
                var filteredModifierGroups = [];
                var modCataloguesToFilter = []
                $scope.addMenuObj.menu.modifierGroups.map(modGroup => {
                    if (modGroupIds.includes(modGroup.vendorEntityId)) {
                        filteredModifierGroups.push(modGroup);
                    } else {
                        modGroup.variants.map(variant => {
                            modCataloguesToFilter.push(variant.vendorEntityId);
                        })
                    }
                });
                $scope.addMenuObj.menu.modifierGroups = filteredModifierGroups;
                var finalModeCataloguesToFilter = [];
                //Do not filter catalog if it is found in other modifier groups which should go
                modCataloguesToFilter.forEach(function (catalogId) {
                    var found = false;
                    filteredModifierGroups.forEach(function (modGroup) {
                        modGroup.variants.forEach(function (variant) {
                            if (catalogId == variant.vendorEntityId) {
                                found = true;
                            }
                        })
                    });
                    if (!found) {
                        finalModeCataloguesToFilter.push(catalogId);
                    }
                })
                var finalCatalogues = []
                $scope.addMenuObj.menu.catalogues.map(catalogue => {
                    if (!finalModeCataloguesToFilter.includes(catalogue.vendorEntityId)) {
                        finalCatalogues.push(catalogue);
                    }
                });
                $scope.addMenuObj.menu.catalogues = finalCatalogues;

                $scope.prepareShowMenuObj({ menuData: $scope.addMenuObj.menu });
                if ($scope.isBulkUpload === true) {
                    var valid = true;
                    $scope.addMenuObj.menu.categories.forEach(function (cat) {
                        if (valid && cat.category_name === "Others" && cat.items.length > 0) {
                            valid = false;
                        }
                    });
                    if ($scope.addMenuObj.menu.categories.length === 0 || $scope.addMenuObj.menu.catalogues.length === 0) {
                        valid = false;
                    }
                    if (valid) {
                        $scope.uploadNewMenu();
                    } else {
                        $scope.bulkUploadCallback(valid);
                    }
                }
            };

            $scope.sanitizeSwiggyMenu = function () {
                var itemsFiltered = [];
                $scope.addMenuObj.entity.items.map(function (item) {
                    var matched = false;
                    var itemIds = new Set();
                    itemIds.add((item.id).split('_')[0]);
                    item.variant_groups.map(function (variantGroup) {
                        variantGroup.variants.map(function (variantId) {
                            itemIds.add((variantId.id).split('_')[0]);
                        });
                    });
                    item.addon_groups.map(function (addOnGroup) {
                        if ((addOnGroup.id).startsWith("r_")) {
                            addOnGroup.addons.map(function (addOn) {
                                if ((addOn.id).startsWith("r_")) {
                                    itemIds.add((addOn.id).split('_')[1]);
                                }
                            });
                        }
                    });
                    for (var id of itemIds.values()) {
                        $scope.nonMatchedProducts.map(function (nProd) {
                            if ((item.id).split('_')[0] == nProd.id) {
                                if (nProd.dimension == null) {
                                    matched = true;
                                }
                                item.variant_groups.map(function (itemVar) {
                                    if (itemVar.name == 'Size') {
                                        if (itemVar.variants.length == 0) {
                                            matched = true;
                                        } else if (itemVar.variants.length == 1 && (itemVar.variants[0].name).includes(nProd.dimension)) {
                                            matched = true;
                                        } else if (itemVar.variants.length != 1) {
                                            itemVar.variants.map(function (variant, varIndex) {
                                                if ((variant.name).includes(nProd.dimension)) {
                                                    itemVar.variants.splice(varIndex, 1);
                                                    item.pricing_combinations.map(function (itemPriceComb, index) {
                                                        itemPriceComb.variant_combination.map(function (varComb) {
                                                            if (angular.equals(varComb.variant_id, variant.id)) {
                                                                item.pricing_combinations.splice(index, 1);
                                                            }
                                                        });
                                                    });
                                                }
                                            });
                                        }
                                    } else {
                                        var comboConstId = itemVar.id.split("_")[1];
                                        if (comboConstId.includes(id)) {
                                            matched = true;
                                        }
                                    }
                                });
                            }
                        });
                    }
                    if (!matched && $scope.filteredProducts.indexOf(parseInt(item.id)) < 0) {
                        itemsFiltered.push(item);
                    }
                });
                $scope.addMenuObj.entity.items = itemsFiltered;
                if ($scope.isBulkUpload === true) {
                    var valid = true;
                    if ($scope.addMenuObj.unmappedProducts.length > 0) {
                        valid = false;
                    }
                    if ($scope.addMenuObj.entity.items.length === 0 || $scope.addMenuObj.entity.main_categories.length === 0) {
                        valid = false;
                    }
                    if (valid) {
                        $scope.uploadNewMenu();
                    } else {
                        $scope.bulkUploadCallback(valid);
                    }
                }
            };

            $scope.removeModifierGroupEntry = function (vendorEntityId) {
                var filteredModGroups = [];
                var removedModifierGroups = [];
                $scope.addMenuObj.menu.modifierGroups.map(function (modGroup) {
                    var filteredModVariants = [];
                    modGroup.variants.map(function (modVariant) {
                        if (modVariant.vendorEntityId != vendorEntityId) {
                            filteredModVariants.push(modVariant);
                        }
                    });
                    if (filteredModVariants.length > 0) {
                        if((modGroup.vendorEntityId.includes("_Recommendations") || modGroup.vendorEntityId.includes("_Paid_Addons") )){
                           modGroup.max = filteredModVariants.length;
                        }
                        modGroup.variants = filteredModVariants;
                        filteredModGroups.push(modGroup);
                    } else {
                        removedModifierGroups.push(modGroup.vendorEntityId);
                    }
                });
                $scope.addMenuObj.menu.modifierGroups = filteredModGroups;
                //removing modifier group entry from catalog variants
                $scope.addMenuObj.menu.catalogues.map(function (catalogue) {
                    catalogue.variants.map(function (variant) {
                        var filteredModGroups = [];
                        variant.modifierGroups.map(function (modGroup) {
                            if (!removedModifierGroups.includes(modGroup.vendorEntityId)) {
                                filteredModGroups.push(modGroup);
                            }
                        });
                        variant.modifierGroups = filteredModGroups;
                    });
                });
            };

            $scope.addPartnerMenu = function () {
                $rootScope.showFullScreenLoader = true;
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: $scope.addMenuObj.menu
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addMenu,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu added successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addZomatoMenuForUnit = function () {
                console.log("Menu set success:::::::::::");
                $("#nonMatchedProductsModal").modal("hide");
                $rootScope.showFullScreenLoader = true;
                var url;
                if ($scope.selectedMenuType == "SINGLE_SERVE") {
                    url = AppUtil.restUrls.partnerMetadata.addSingleServeMenuForUnit
                } else {
                    url = AppUtil.restUrls.partnerMetadata.addMenuForUnit
                }
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    isNew: $scope.selectedUnit.newMenu,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: $scope.addMenuObj.menu,
                    charges: $scope.addMenuObj.charges,
                    employeeId: AppUtil.getCurrentUser().id,
                    brandId: $scope.selectedBrand.brandId
                };
                $http({
                    method: 'POST',
                    url: url,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu request submitted successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addSwiggyMenuForUnit = function () {
                console.log("Menu set success:::::::::::");
                $("#nonMatchedProductsModal").modal("hide");
                $rootScope.showFullScreenLoader = true;
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    isNew: $scope.selectedUnit.newMenu,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: $scope.addMenuObj.entity,
                    employeeId: AppUtil.getCurrentUser().id,
                    brandId: $scope.selectedBrand.brandId
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addMenuForUnit,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu request submitted successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addSwiggyMenuForUnitOld = function () {
                console.log("Menu set success:::::::::::");
                $("#nonMatchedProductsModal").modal("hide");
                $rootScope.showFullScreenLoader = true;
                var productIds = [];
                $scope.addMenuObj.items.map(function (item) {
                    if (item.Parent.indexOf("I") >= 0) {
                        productIds.push(item.external_id);
                    }
                });
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    menuRequest: productIds,
                    employeeId: AppUtil.getCurrentUser().id,
                    isNew: $scope.selectedUnit.newMenu,
                    brandId: $scope.selectedBrand.brandId
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addMenuForUnit,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Menu request submitted successfully");
                    } else {
                        bootbox.alert("Error adding menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.downloadSwiggyCatalog = function () {
                $("#nonMatchedProductsModal").modal("hide");
                AppUtil.JSONToCSVConvertor($scope.addMenuObj.items, "swiggy_catalog_" + $scope.selectedUnit.id, true);
                AppUtil.JSONToCSVConvertor($scope.addMenuObj.addons, "swiggy_catalog_" + $scope.selectedUnit.id + "_addons", true);
            };

            /*$scope.getActiveMenuDetailForUnits = function () {
                $rootScope.showFullScreenLoader = true;
                var unitIds = [];
                $scope.filteredUnits.map(function (unit) {
                    unitIds.push(unit.id);
                });
                var data = {
                    unitIds: unitIds,
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    isNew: true,
                    brandId: $scope.selectedBrand.brandId
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getActiveMenuForUnits,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.addActiveMenuDetailToUnits(response.data);
                    } else {
                        bootbox.alert("Error getting active menu.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };*/

            /*$scope.addActiveMenuDetailToUnits = function (menuList) {
                var menuMap = {};
                menuList.map(function (menu) {
                    menuMap[menu.unitId] = menu;
                });
                $scope.filteredUnits.map(function (unit) {
                    unit.newMenu = true;
                    if (menuMap[unit.id] != null) {
                        unit.activeMenu = menuMap[unit.id];
                        unit.activeMenu.employeeName += " [" + menuMap[unit.id].employeeId + "]";
                    } else {
                        unit.activeMenu = {addTime: "NA", employeeName: "NA"};
                    }
                })
            };*/

            $scope.setNewMenu = function (unit, data) {
                unit.newMenu = data;
            };

            $scope.setAddPackaging = function (unit, data) {
                unit.addPackaging = data;
            };

            $scope.setSplitAllDesiChaiDimensions = function (unit, data) {
                unit.splitAllDesiChaiDimensions = data;
            };

            $scope.setMiniKetliDefault = function (unit, data) {
                unit.miniKetliDefault = data;
            };

            $scope.toggleSeparateDimension = function (unit, isChecked) {
                console.log('Add Separate Dimension Item:', isChecked);
                if (!isChecked) {
                    // Reset dropdown value if checkbox is unchecked
                    unit.selectedDimension = null;
                }
            };

            // Handler for dropdown changes
            $scope.onDimensionChange = function (unit, selectedDimension) {
                unit.selectedDimension = selectedDimension;
                console.log('Selected Dimension:', unit.selectedDimension);
                // Add custom logic here for dropdown selection changes
            };

            $scope.setClubAllDesiChaiDimensions = function (unit, data) {
                unit.clubAllDesiChaiDimensions = data;
            };

            $scope.showAddOffer = function () {
                var unitIds = [];
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        unitIds.push(unit.id)
                    }
                });
                if (unitIds.length > 0 && $scope.selectedPartner != null) {
                    $scope.showAddNewOffer = true;
                    $scope.couponCode = null;
                    $scope.offer = null;
                    if (document.getElementById("couponCode") != null) {
                        document.getElementById("couponCode").value = null;
                    }
                } else {
                    bootbox.alert("Pleas e select unit and partner.")
                }
            };

            $scope.setCouponCode = function (couponCode) {
                $scope.couponCode = couponCode;
            };

            $scope.couponSearch = function () {
                if ($scope.couponCode != null) {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.couponSearch,
                        data: $scope.couponCode
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.couponDetail = response.data;
                            $scope.prepareOfferDetail();
                        } else {
                            bootbox.alert("Error getting coupon data.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please enter coupon code");
                }
            };

            $scope.prepareOfferDetail = function () {
                var cd = $scope.couponDetail;
                $scope.offer = {
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    partnerName: $scope.selectedPartner.partnerName,
                    unitId: null,
                    startDate: cd.offer.startDate,
                    endDate: cd.offer.endDate,
                    active: cd.offer.status === "ACTIVE",
                    couponCode: cd.code
                };
                var offerData = null;
                if ($scope.selectedPartner.partnerName === "ZOMATO") {
                    offerData = {
                        offer_id: null,
                        start_date: AppUtil.formatDate(cd.offer.startDate, "yyyy-MM-dd"),
                        end_date: AppUtil.formatDate(cd.offer.endDate, "yyyy-MM-dd"),
                        offer_type: "DISCOUNT",
                        discount_type: cd.offer.type === "FLAT_BILL_STRATEGY" ? "FIXED" : "PERCENTAGE",
                        min_order_amount: cd.offer.minValue,
                        discount_value: cd.offer.offerValue,
                        is_active: $scope.offer.active === true ? 1 : 0,
                    }
                }
                $scope.offer.offerData = offerData;
            };

            $scope.setBogo = function (startDate, endDate) {
                $scope.offer = {
                    partnerId: $scope.selectedPartner.kettlePartnerId,
                    partnerName: $scope.selectedPartner.partnerName,
                    unitId: null,
                    startDate: startDate,
                    endDate: endDate,
                    active: true,
                    couponCode: null
                };
                var offerData = null;
                if ($scope.selectedPartner.partnerName === "ZOMATO") {
                    offerData = {
                        offer_id: null,
                        start_date: startDate,
                        end_date: endDate,
                        offer_type: "BOGO",
                        discount_type: null,
                        min_order_amount: 0,
                        discount_value: 0,
                        is_active: 0,
                    }
                }
                $scope.offer.offerData = offerData;
            };

            $scope.addNewOffer = function () {
                $rootScope.showFullScreenLoader = true;
                var reqObj = { unitIds: [] };
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        reqObj.unitIds.push(unit.id)
                    }
                });
                reqObj.partnerId = $scope.selectedPartner.kettlePartnerId;
                reqObj.offerDetail = $scope.offer;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addOffer,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        bootbox.alert("Offers added successfully");
                        $scope.showAddNewOffer = false;
                        $scope.getOffers();
                    } else {
                        bootbox.alert("Error adding offer: " + response.data.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getOffers = function () {
                var reqObj = { unitIds: [] };
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        reqObj.unitIds.push(unit.id)
                    }
                });
                reqObj.partnerId = $scope.selectedPartner.kettlePartnerId;
                if (reqObj.unitIds.length > 0 && $scope.selectedPartner != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getOffers,
                        data: reqObj
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.offers = response.data;
                            if ($scope.offers.length === 0) {
                                bootbox.alert("No offers found!");
                            }
                        } else {
                            bootbox.alert("Error getting offers.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit and partner.")
                }

            };

            $scope.updateOfferStatus = function (offer, activate) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.sendPartnerOffer,
                    data: offer
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        offer.active = activate;
                    } else {
                        bootbox.alert("Error adding offer: " + response.data.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.sendOffer = function (offer) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.sendPartnerOffer,
                    data: offer
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Offer Sended Successfully");
                    } else {
                        bootbox.alert("Error Sending offer data.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getCurrentMenu = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getMenu,
                    data: {
                        unitId: $scope.selectedUnit.id,
                        region: $scope.selectedUnit.region,
                        kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                        isNew: $scope.selectedUnit.newMenu,
                        brandId: $scope.selectedBrand.brandId
                    }
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.currentMenu = response.data;
                        $scope.addMenuObj = response.data.menuRequest;
                    } else {
                        bootbox.alert("Error getting menu data.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.refreshMenu = function () {
                if ($scope.currentMenu != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.refreshMenu,
                        data: $scope.currentMenu
                    }).then(function success(response) {
                        if (response.status === 200 && response.data === true) {
                            bootbox.alert("Menu sync request sent to partner.");
                        } else {
                            bootbox.alert("Error syncing data.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please press get menu first to fetch latest menu.");
                }
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
            };

            $scope.getUnitProducts = function () {
                if ($scope.selectedUnit != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.unitPartnerProductsTrimmed + "?unitId=" + $scope.selectedUnit.id,
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.unitProductList = response.data;
                            $scope.showUnitProducts = true;
                            if ($scope.selectedAction === 'BOGO PRODUCTS') {
                                if ($scope.selectedPartner != null) {
                                    $scope.getBogoProducts();
                                } else {
                                    bootbox.alert("Please select partner.");
                                    $rootScope.showFullScreenLoader = false;
                                }
                            }
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit.");
                }
            };

            $scope.getBogoProducts = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getPartnerBogoProducts,
                    data: $scope.selectedPartner.kettlePartnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null && response.data.length > 0) {
                        response.data.map(function (productId) {
                            $scope.unitProductList.map(function (product) {
                                if (productId == product.detail.id) {
                                    product.selected = true;
                                }
                            });
                        });
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setBogoProducts = function () {
                var reqObj = { productIds: [], partnerIds: [$scope.selectedPartner.kettlePartnerId] };
                $scope.unitProductList.map(function (product) {
                    if (product.selected == true) {
                        reqObj.productIds.push(product.detail.id)
                    }
                });
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerBogoProducts,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Bogo products updated successfully.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.checkToday = function (date) {
                if (date != null && date.addTime != null) {
                    return new Date().toDateString() === new Date(date.addTime).toDateString();
                }
                return false;
            };

            $scope.prepareShowMenuObj = function (addObj) {
                if ($scope.selectedPartner.kettlePartnerId === 3) {
                    addObj = { menu: addObj.menuData };
                    $scope.showMenuObj = JSON.stringify(addObj);
                    $scope.showMenuObj = JSON.parse($scope.showMenuObj);
                    $scope.showMenuObj.menu.catalogues = [];
                    var itemMap = {};
                    addObj.menu.catalogues.map(function (item) {
                        itemMap[item.vendorEntityId] = item;
                    });
                    if (addObj.menu.combos != null) {
                        addObj.menu.combos.map(function (item) {
                            itemMap[item.vendorEntityId] = item;
                        });
                    }
                    $scope.showMenuObj.menu.categories.map(function (cat) {
                        cat.subCategories.map(function (subcat) {
                            subcat.entities.map(function (item) {
                                item.item = itemMap[item.vendorEntityId];
                            });
                        })
                    });
                    console.log($scope.showMenuObj);
                }
                if ($scope.selectedPartner.kettlePartnerId === 6) {
                    addObj = { entity: addObj.menuData };
                    $scope.addMenuObj = addObj;
                    console.log($scope.addMenuObj);
                }
                if ($scope.selectedPartner.kettlePartnerId === 24) {
                    addObj = addObj.menuData;
                    $scope.addMenuObj = addObj;
                    console.log($scope.addMenuObj);
                }
            };

            /// Dynamic Menu Changes

            /*            $scope.uploadNewMenu = function(){
                            $rootScope.detailLoaderMessage = "Uploading unit menu data...";
                            $rootScope.showDetailLoader = true;
                             $http({
                                 method: 'POST',
                                 url: AppUtil.restUrls.channelPartner.unitIdsForMenuSequence,
                                 params: {menuSequenceId: $scope.menuSequenceId,kettlePartnerId: $scope.selectedPartner.kettlePartnerId,}
                             }).then(function success(response) {
                                 if (response.status === 200) {
                                     $rootScope.showDetailLoader = false;
                                     $scope.unitIdsForMenuSeq = response.data;
                                     $scope.saveUnitMenuSequence();
                                 } else {
                                     bootbox.alert("Error getting Unit Ids for Menu Sequence");
                                     $rootScope.showDetailLoader = false;
                                 }
                             }, function error(response) {
                                 console.log('Error in getting response', response);
                                 bootbox.alert('Error in getting response', response);
                                 $rootScope.showDetailLoader = false;
                             });
                        }*/

            $scope.uploadNewMenu = function () {
                $rootScope.detailLoaderMessage = "Saving unit menu data...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                var data = {
                    unitId: $scope.selectedUnit.id,
                    region: $scope.selectedUnit.region,
                    isNew: $scope.selectedUnit.newMenu,
                    kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                    charges: $scope.addMenuObj.charges,
                    employeeId: AppUtil.getCurrentUser().id,
                    brandId: $scope.selectedBrand.brandId,
                    status: 'IN_PROGRESS',
                    menuType: $scope.selectedMenuType,
                    menuRequest: $scope.addMenuObj.menu,
                    menuSequenceId: $scope.currentMenuSequence.menuSequenceId,
                    menuSequenceName: $scope.currentMenuSequence.menuSequenceName,
                };
                if ($scope.selectedPartner.kettlePartnerId == 3) {
                    if ($scope.addMenuObj.menu != null && $scope.addMenuObj.menu.catalogues != null && $scope.addMenuObj.menu.catalogues.length > 0) {
                        data.menuRequest = $scope.addMenuObj.menu;
                    }
                } else if ($scope.selectedPartner.kettlePartnerId=== 24){
                    data.taxes= $scope.addMenuObj.taxes;
                    data.chargeIds= $scope.addMenuObj.chargeIds;
                    data.timings=$scope.addMenuObj.timings;
                    data.menuRequest=$scope.addMenuObj;
                } else {
                    data.menuRequest = $scope.addMenuObj.entity;
                }
                if (data.menuRequest != null) {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.addMenuVersion,
                        data: data
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            if ($scope.isBulkUpload === true) {
                                $scope.markAsDefaultVersion(response.data, $scope.selectedUnit.id, $scope.selectedPartner.kettlePartnerId, $scope.selectedBrand.brandId);
                            } else {
                                bootbox.alert("Menu request Saved successfully");
                                $scope.showUnitMenuSequenceVersion();
                            }
                        } else {
                            bootbox.alert("Error adding menu.");
                            $rootScope.showDetailLoader = false;
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showDetailLoader = false;
                    });
                } else {
                    bootbox.alert("Menu is not valid.");
                }
            };

            $scope.showUnitMenuSequenceVersion = function () {
                $rootScope.detailLoaderMessage = "Fetching Version List...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerMetadata.getUnitMenuVersions,
                    params: {
                        unitId: $scope.selectedUnit.id,
                        kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                        brandId: $scope.selectedBrand.brandId,
                        menuType: $scope.selectedMenuType
                    }
                }).then(function success(response) {
                    if (response.status === 200) {
                        $scope.unitVersionList = response.data;
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });

            };

            $scope.getChannelPartnerById = function (id) {
                return $scope.channelPartners.filter(function (partner) {
                    return partner.kettlePartnerId === id;
                })[0];
            };

            $scope.markAsDefaultVersion = function (version, unitId, partnerId, brandId) {
                $rootScope.detailLoaderMessage = "Changing Version To Active...";
                if (!$scope.isBulkUpload) {
                    $rootScope.showDetailLoader = true;
                }
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerMetadata.markUnitVersionDefault,
                    params: {
                        unitId: unitId,
                        kettlePartnerId: partnerId,
                        brandId: brandId,
                        version: version,
                        status: "ACTIVE",
                        menuType: $scope.selectedMenuType
                    }
                }).then(function success(response) {
                    if (response.status === 200) {
                        if ($scope.isBulkUpload === true) {
                            $scope.bulkUploadCallback(true);
                        } else {
                            bootbox.alert("Unit Menu Version Marked Default Successfully");
                            $scope.unitVersionList = response.data;
                        }
                    } else {
                        bootbox.alert("Error getting Unit Menu Version Data");
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                    if ($scope.isBulkUpload === true) {
                        $scope.bulkUploadCallback(false);
                    }
                });
            };

            $scope.viewVersionMenu = function (version, unitId, partnerId, brandId) {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerMetadata.showVersionMenu,
                    params: {
                        unitId: unitId,
                        kettlePartnerId: partnerId,
                        brandId: brandId,
                        version: version,
                        menuType: $scope.selectedMenuType
                    }
                }).then(function success(response) {
                    if (response.status === 200) {
                        $scope.viewMenu = true;
                        $scope.prepareShowMenuObj(response.data);
                    } else {
                        bootbox.alert("Error getting Unit Menu Version Data");
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                });
            };

            $scope.pushMenuToUnit = function () {
                $rootScope.detailLoaderMessage = "Pushing Menu To Units...";
                $rootScope.showDetailLoader = true;
                var unitIds = [];
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        unitIds.push(unit.id)
                    }
                });
                if (unitIds.length > 0 && $scope.selectedPartner != null && $scope.selectedMenuType != null) {
                    var data = {
                        kettlePartnerId: $scope.selectedPartner.kettlePartnerId,
                        brandId: $scope.selectedBrand.brandId,
                        unitIdsForMenu: unitIds,
                        employeeId: AppUtil.getCurrentUser().id,
                        menuType: $scope.selectedMenuType
                    };
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.pushMenuToUnits,
                        data: data
                    }).then(function success(response) {
                        if (response.status === 200) {
                            bootbox.alert("Menu Pushed Successfully");
                        } else {
                            bootbox.alert("Error getting in pushing menu.");
                        }
                        $rootScope.showDetailLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        bootbox.alert('Error in getting response', response);
                        $rootScope.showDetailLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit and partner.");
                    $rootScope.showDetailLoader = false;
                }
            };

            $scope.trackSwiggyMenu = function () {
                $rootScope.detailLoaderMessage = "Tracking Menu push...";
                $rootScope.showDetailLoader = null;
                var units = [];
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        units.push({ id: unit.id, name: unit.name })
                    }
                });
                if (units.length > 0 && $scope.selectedPartner != null && $scope.selectedBrand != null) {
                    units.map(function (unit) {
                        $http({
                            method: 'GET',
                            url: AppUtil.restUrls.partnerMetadata.trackSwiggyMenuPush + "?unitId=" + unit.id + "&partnerId=" +
                                $scope.selectedPartner.kettlePartnerId + "&brandId=" + $scope.selectedBrand.brandId,
                        }).then(function success(response) {
                            if (response.status === 200) {
                                $scope.trackSwiggyMenuStatus = { id: unit.id, name: unit.name, response: response };
                            }
                            $rootScope.showDetailLoader = false;
                        }, function error(response) {
                            console.log('Error in getting response', response);
                            bootbox.alert('Error in getting response', response);
                            $rootScope.showDetailLoader = false;
                        });
                    });
                } else {
                    bootbox.alert("Please select unit and partner.");
                    $rootScope.showDetailLoader = false;
                }
            };

            $scope.setSelectAllUnits = function () {
                $scope.selectAllUnits = !$scope.selectAllUnits;
                $scope.unitList.map(function (unit) {
                    unit.selected = $scope.selectAllUnits;
                });
            };

            $scope.setSelectAllPricingUnits = function () {
                $scope.selectAllPricingUnits = !$scope.selectAllPricingUnits;
                $scope.pricingUnits.map(function (unit) {
                    unit.selected = $scope.selectAllPricingUnits;
                });
            };

            $scope.setSelectAllPartnerProducts = function () {
                $scope.selectAllPartnerProducts = !$scope.selectAllPartnerProducts;
                $scope.productList.map(function (product) {
                    product.selected = $scope.selectAllPartnerProducts;
                });
            };


            /////////////////////////////// BULK MENU FUNCTIONS /////////////////////////////

            $scope.backToBulkUpload = function () {
                $scope.bulkUploadStarted = false;
            };

            $scope.setBulkAddPackaging = function (data) {
                $scope.bulkAddPackaging = data;
            };

            $scope.setBulkSplitAllDesiChaiDimensions = function (data) {
                $scope.bulkSplitAllDesiChaiDimensions = data;
            };

            $scope.setBulkClubAllDesiChaiDimensions = function (data) {
                $scope.bulkClubAllDesiChaiDimensions = data;
            };

            $scope.setBulkMiniKetliDefault = function (data) {
                $scope.bulkMiniKetliDefault = data;
            };

            $scope.toggleBulkSeparateDimension = function (data) {
                if (!data) {
                    $scope.bulkSelectedDimension = null;
                }
                $scope.bulkAddSeparateDimension = data;
            };

            $scope.onDimensionChangeBulk = function (data) {
                $scope.bulkSelectedDimension = data;
            };

            $scope.setBulkDealOfTheDay = function (data) {
                $scope.bulkDealOfTheDay = data;
                if ($scope.bulkDealOfTheDay == true) {
                    $scope.getDOTDList();
                }
            };

            $scope.proceedBulkMenuUpload = function () {
                $scope.isBulkUpload = true;
                $scope.unitsForBulkUpload = [];
                var selectedUnits = $scope.filteredUnits.filter(function (unit) {
                    return unit.selected;
                });
                selectedUnits.map(function (unit) {
                    $scope.unitsForBulkUpload.push({
                        id: unit.id,
                        name: unit.name,
                        region: unit.region,
                        uploadStatus: "WAITING",
                        city : unit.city
                    });
                });
                $scope.bulkUploadStarted = true;
                $scope.bulkUploadStartIndex = 0;
            };

            $scope.startBulkUpload = function () {
                $scope.selectedUnit = $scope.unitsForBulkUpload[$scope.bulkUploadStartIndex];
                console.log("generating menu for unit ::::::::::::::::::::::::::::::::::" + $scope.selectedUnit.name);
                $scope.selectedUnit.uploadStatus = "GENERATING";
                $scope.selectedUnit.newMenu = true;
                $scope.selectedUnit.addPackaging = $scope.bulkAddPackaging;
                $scope.selectedUnit.splitAllDesiChaiDimensions = $scope.bulkSplitAllDesiChaiDimensions;
                $scope.selectedUnit.clubAllDesiChaiDimensions = $scope.bulkClubAllDesiChaiDimensions;
                $scope.selectedUnit.miniKetliDefault = $scope.bulkMiniKetliDefault;
                $scope.selectedUnit.selectedDimension = $scope.bulkSelectedDimension;
                $scope.getUnitMenuToAdd();
            };

            $scope.bulkUploadCallback = function (success) {
                if ($scope.bulkUploadStartIndex < $scope.unitsForBulkUpload.length) {
                    if (success === true) {
                        $scope.selectedUnit.uploadStatus = "COMPLETED";
                        $scope.bulkUploadStartIndex++;
                        $scope.startBulkUpload();
                    } else {
                        $("#bulkUploadUnitSkipModal").modal("show");
                    }
                } else {
                    bootbox.alert("Bulk upload menu completed successfully.");
                }
            };

            $scope.skipCurrentUnitBulkUpload = function () {
                $("#bulkUploadUnitSkipModal").modal("hide");
                $scope.selectedUnit.uploadStatus = "SKIPPED";
                $scope.selectedUnit = null;
                if ($scope.bulkUploadStartIndex < $scope.unitsForBulkUpload.length - 1) {
                    $scope.bulkUploadStartIndex++;
                    $scope.startBulkUpload();
                } else {
                    bootbox.alert("Bulk upload menu completed successfully.");
                }
            };

            $scope.copyUnitsWithStatus = function (status) {
                var copiedUnits = [];
                $scope.unitsForBulkUpload.map(function (unit) {
                    if (unit.uploadStatus === status) {
                        copiedUnits.push(unit.name);
                    }
                });
                $scope.copyToClipboard(copiedUnits.join(","));
            };

            $scope.copyToClipboard = function (copyText) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(copyText);
                } else {
                    var textArea = document.createElement("textarea");
                    textArea.value = copyText;
                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        console.log('Fallback: Copying text command was ' + msg);
                    } catch (err) {
                        console.error('Fallback: Oops, unable to copy', err);
                    }
                    document.body.removeChild(textArea);
                }
            };

            $scope.getDOTDList = function () {

                var ret = false;
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                if (!ret) {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.partnerMetadata.getDOTDProducts + "?partnerId=" + $scope.selectedPartner.kettlePartnerId,
                    }).then(function success(response) {
                        if (response.status == 200 && response.data != null) {
                            console.log(response);

                            $scope.selectedProductsDTDO = response.data.dotdProducts;
                            if ($scope.selectedProductsDTDO === undefined) {
                                $scope.selectedProductsDTDO = [];
                            }
                            console.log($scope.selectedProductsDTDO);
                            bootbox.alert("Fetched DOTD Products List!");
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting DOTD Products");
                    });
                }

            }
        }]);

