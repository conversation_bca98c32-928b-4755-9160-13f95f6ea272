/*
 * SUN<PERSON><PERSON><PERSON>E TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("UploadCustomerController",['$location', '$scope', 'AppUtil', '$rootScope', '$http','fileService', function ($location, $scope, AppUtil, $rootScope, $http,fileService) {

    $scope.init = function (){
        $scope.acquisitionTypes = ["FB_LEADGEN","GOOGLE","CHAAYOS-COD","NEO_SERVICE","FACEBOOK","NEWSPAP<PERSON>","INSTAGRA<PERSON>","<PERSON><PERSON>","WHA<PERSON>AP<PERSON>","SHOPIFY","CONTLO_SHOPIFY"];
        $scope.selectedAcquisition = null;
    }


    $scope.openUploadCustomerModal = function () {
        $("#uploadCustomerModal").modal("show");
    }

    $scope.uploadCustomerSheet = function () {
            if (fileService.getFile() == null
                || fileService.getFile() == undefined || fileService.getFile().type == "text/csv") {
                bootbox.alert("Please select a .xlsx file");
                return;
            }

            if($scope.selectedAcquisition == null || $scope.selectedAcquisition == ""){
                bootbox.alert("Please select acquisition source");
                return;
            }

            console.log("File is",fileService.getFile());

            var fd = new FormData();
            fd.append("file", fileService.getFile());
            $rootScope.showDetailLoader = true;

            $http({
                url:AppUtil.restUrls.customer.uploadCustomerSheet + "?acquisitionSource="+ $scope.selectedAcquisition ,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showDetailLoader = false;
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                $scope.selectedAcquisition = null;
                var data= response;
                var customerAdded = data["NewCustomersAdded"];
                var customerUpdated = data["CustomersOptInUpdated"];
                var validCustomers = data["ValidCustomers"];
                console.log(response);
                if(response !== null || response !== undefined){
                    alert( "Number of valid customers : " + validCustomers + "\n" + "Number of customers uploaded : " + customerAdded +"\n" +
                    "Number of customer's whatsapp opt in updated : " + customerUpdated);
                    window.location.reload();
                }
                else{
                    alert(response.errorMessage);
                }

            }).error(function (response) {
                $rootScope.showDetailLoader = false;
                alert("Error while uploading Expense Sheet");
            });
        }


}]);