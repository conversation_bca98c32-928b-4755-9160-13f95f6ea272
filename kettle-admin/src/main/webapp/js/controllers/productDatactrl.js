 /*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("ProductDataController", function ($scope, $http, $rootScope, $log, $window, AppUtil, fileService, $cookieStore) {
    $scope.singleProduct = {};
    var oldProductName = '';

    $scope.init = function () {
        $rootScope.enableScreenFilter = true;
        
        $scope.getBrandList();
        // $scope.imageSuffix="http://d1nqp92n3q8zl7.cloudfront.net/product_image/"
        //console.log("ENV is:" + AppUtil.getEnvType())
        if (AppUtil.getEnvType() === 'PROD') {
            //console.log(AppUtil.getEnvType())
            $scope.imageSuffix = "https://d3pjt1af33nqn0.cloudfront.net/product_image/"; //prod
            $scope.imageSuffixNew = "https://d3pjt1af33nqn0.cloudfront.net/product_image/dimension/";
        } else {
            //console.log(AppUtil.getEnvType())
            $scope.imageSuffix = "http://d1nqp92n3q8zl7.cloudfront.net/product_image/";//dev
            $scope.imageSuffixNew = "http://d1nqp92n3q8zl7.cloudfront.net/product_image/dimension/";
        }
        $scope.imageCategoryList = [
            {name: 'GRID_MENU_LOW_WEBP', value: '600px'},
            {name: 'GRID_MENU_LOW', value: '600px'},
            {name: 'LIST_MENU_HIGH',value: '280px'},
            {name: 'GRID_MENU_HIGH', value: '1200px'},
            {name: 'SHOWCASE_VIDEO', value: '600px'},
            {name: 'MARKETING_IMAGE_WEB_VIEW',value:'1200px'},
            {name:'GRID_MENU_100X100',value:'100px'},
            {name:'GRID_MENU_200X200',value:'200px'},
            {name:'GRID_MENU_300X300',value:'300px'},
            {name:'GRID_MENU_400X400',value:'400px'},
            {name:'RECOMMENDATION_IMAGE_1200X1200',value:'1200px'},
        ];
        $scope.imageCategoryType = $scope.imageCategoryList[0];
        $scope.getAllBrands();
        ClassicEditor.create(document.querySelector('#ckeditor'), {
            // toolbar: [ 'heading', '|', 'bold', 'italic', 'link' ]
        }).then(editor => {
            window.editor = editor;
        }).catch(err => {
            console.error(err.stack);
        });
        $scope.cityList = [];
        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
            scrollableHeight: '250px'
        };
        $scope.selectedRegularTags = [];
        $scope.selectedNutritionTags = [];
        $scope.idName = {
             id : null,
             name : null,
             code : null
        };
        $scope.sourceTypeList = ['CAFE','COD'];
        $scope.dayslots = ['ALL','DAY_SLOT_BREAKFAST','DAY_SLOT_LUNCH','DAY_SLOT_EVENING','DAY_SLOT_DINNER','DAY_SLOT_POST_DINNER','DAY_SLOT_OVERNIGHT']
        $scope.selectedSourceType = '';
        getProductNutritionData();
    };
    $http({
        method: 'GET',
        url: AppUtil.restUrls.unitMetaData.listTypes
    }).then(function success(response) {
        $scope.categoryLists = response.data;
        $scope.productCategory = $scope.categoryLists.CATEGORY;
        $scope.workStationsStationCategory = $scope.categoryLists.WORK_STATIONS_STATION_CATEGORY[0].content;
        $scope.selectedProductType = $scope.productCategory[0];
        $scope.productDimension = $scope.categoryLists.DIMENSION;
        //console.log($scope.productCategory);
        $scope.selectetProductDimension = $scope.productDimension[0];
        $scope.productAdons = $scope.categoryLists.ADDONS;
        $scope.selectProductAdons = $scope.productAdons[0];
        $scope.preparationModes = [{code: "STEWING", name: "STEWING"}, {code: "STEEPING", name: "STEEPING"}];
        $scope.attribute = [{code: null, name: null}, {code: "VEG", name: "VEG"}, {code: "NON_VEG", name: "NON_VEG"}];
        $scope.priceType = [{code: "MRP", name: "MRP"}, {code: "NET_PRICE", name: "NET_PRICE"}, {
            code: "ZERO_TAX",
            name: "ZERO_TAX"
        }];
        $scope.inventoryTrackedData = [{code: false, name: false}, {code: true, name: true}];
        $scope.selectProductAttribute = $scope.attribute[0];
        //console.log($scope.selectProductAttribute);
        $scope.selectPriceType = $scope.priceType[0];

        $scope.variantOrderLevel = [{code: false, name: false}, {code: true, name: true}];
        $scope.selectVariantOrderLevel = $scope.variantOrderLevel[0];
        $scope.selectBrandName =null;
        $scope.inventoryTrackedLevel = $scope.categoryLists.INVENTORY_TRACK_LEVEL[0].content;
        $scope.regularTagList = [];
        $scope.nutritionTagList = [];
        if($scope.categoryLists.REGULAR_TAG != null && $scope.categoryLists.REGULAR_TAG[0] != null
                && $scope.categoryLists.REGULAR_TAG[0].content != null){
           $scope.regularTagList = $scope.categoryLists.REGULAR_TAG[0].content;
        }
        if($scope.categoryLists.NUTRITION_TAG != null && $scope.categoryLists.NUTRITION_TAG[0] != null
                && $scope.categoryLists.NUTRITION_TAG[0].content != null){
           $scope.nutritionTagList = $scope.categoryLists.NUTRITION_TAG[0].content;
        }
        //$scope.totalItems = $scope.objects.length;
        // $scope.currentPage = 1;
        // $scope.numPerPage = 5;
        //v1/unit-metadata/categories/web
        $emptyObj = {id: null, name: ''}
        $http({
            method: 'GET',
            url: AppUtil.restUrls.productMetaData.webType
        }).then(function success(response) {
            //console.log(response.data);
            //console.log(JSON.stringify(response))
            $scope.webAppType = response.data;
            $scope.webAppType.push($emptyObj);
            $scope.selectedWebType = $scope.webAppType[0];
            //console.log($scope.webAppType);
            //console.log("WebType=",$scope.selectedWebType)

        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.productMetaData.classifications
        }).then(function success(response) {
            $scope.productClassifications = response.data;
            $scope.selectedProductClassification = $scope.productClassifications[0];
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.selectInventoryTracked = $scope.inventoryTrackedData[0];
        $scope.selectEmployeeMealComponent = $scope.inventoryTrackedData[0];
        $scope.selectTaxableCogs = $scope.inventoryTrackedData[1];

        // $http({
        //     method: 'GET',
        //     url: AppUtil.restUrls.productMetaData.imageCategoryType
        // }).then(function success(response) {
        //     $scope.imageCategoryList = response.data;
        //     $scope.imageCategoryType = $scope.imageCategoryList[0];
        // }, function error(response) {
        //     console.log("error:" + response);
        // });

        $rootScope.showFullScreenLoader = true;
        $scope.loadProducts();
    });


    $scope.getAllBrands = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandManagement.getAllBrands
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.brands = response.data;
                $rootScope.showFullScreenLoader = false;
            } else {
                bootbox.alert("Error getting brands.");
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
        });
    };

    $scope.loadProducts = function () {

        $http({
            url: AppUtil.restUrls.productMetaData.products,
            dataType: 'json',
            method: 'POST',
            data: '',
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            for (var i = 0; i < response.data.length; i++) {
                response.data[i].startDate = AppUtil.formatDate(new Date(response.data[i].startDate).toDateString(), "yyyy-MM-dd");
                response.data[i].endDate = AppUtil.formatDate(new Date(response.data[i].endDate).toDateString(), "yyyy-MM-dd");
            }
            $scope.productDetails = response;
            $scope.currentPage = 1; //current page
            $scope.entryLimit = 20; //max no of items to display in a page
            $scope.productDetailsList = response.data
            //console.log(response.data);
            $scope.numPerPage = 50;
            $scope.currentPage = 1;
            $scope.filteredItems = $scope.productDetailsList.length; //Initially for no filter
            $scope.totalItems = $scope.filteredItems;

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.paginate = function (value) {
        var begin, end, index;
        begin = ($scope.currentPage - 1) * $scope.numPerPage;
        console.log("begin=", begin);
        end = begin + $scope.numPerPage;
        console.log("end=", end);
        index = $scope.productDetailsList.indexOf(value);
        console.log("index=", index);
        return (begin <= index && index < end);
    };


    $http({
        method: 'GET',
        url: AppUtil.restUrls.taxManagement.getAllTaxCategoriesBasicInfo
    }).then(function success(response) {
        $scope.taxCategory = [];
        var taxes = response.data;
        for (var i in taxes) {
            if (taxes[i].status == 'ACTIVE') {
                $scope.taxCategory.push(taxes[i]);
            }
        }
        //console.log("TaxData=", $scope.taxCategory);
    }, function error(response) {
        console.log("error:" + response);
    });


    /*	 $scope.setPage = function (pageNo) {
                $scope.currentPage = pageNo;
              };

              $scope.pageChanged = function() {
                $log.log('Page changed to: ' + $scope.currentPage);
              };*/


    $scope.addProduct = function () {
        $scope.action = "Add"
        $scope.productName = null;
        $scope.productDescription = null;
        $scope.productShortCode = null;
        $scope.selectedProductSubTypeData = $scope.selectedProductType.content;
        $scope.selectetProductSubtype = $scope.selectedProductSubTypeData[0];
        $scope.selectPrepMode = null;
        $scope.productSKU = null;
        $scope.productStartDate = null;
        $scope.productEndDate = null;
        $scope.productPrepTime = null;
        $scope.selectedStationCategory = null;
        $scope.loading = false;
        $("#productModal").modal("show");
        //$scope.selectProductVender = $scope.vendorList.vendorID[0];
    }

    $scope.showCategoryDetails = function (id) {
        $scope.selectedProductSubTypeData = id;
        $scope.selectetProductSubtype = $scope.selectedProductSubTypeData[0];
    };

    $scope.setSelectedStationCategory = function (station) {
        $scope.selectedStationCategory = station;
    };

    $scope.openProductImageModal = function (imageSrcName) {
        $scope.ImageSrc = imageSrcName;
        $("#displayCompleProductImageModal").modal("show");
    };

    $("#addProductIdDiv").click(function () {
        $("#productModal").modal({backdrop: false});
    });

    $scope.viewProductImage = function (productID, productName) {
        $scope.productID = productID;
        $scope.productNameDesc = productName;
        $scope.getProductImagesById($scope.productID);
        $("#productImageUploadModal").modal("show");
    }

    $scope.viewProductDimensionImage = function (productID, productName,dimensionProfileId) {
        $scope.productID = productID;
        $scope.productNameDesc = productName;
//        $scope.getProductDimensionImages();
        $scope.dimensionOptions = [];

        $scope.selectedDimension = null;

        AppUtil.ListTypes(function(data) {
            if (!data.DIMENSION || !Array.isArray(data.DIMENSION)) {
                console.error("Invalid data structure:", data);
                return;
            }

            data.DIMENSION.forEach(item => {
                if (dimensionProfileId && item.detail && item.detail.id === dimensionProfileId) {
                    item.content.forEach(contentItem => {
                        $scope.dimensionOptions.push({ id: contentItem.id, name: contentItem.name });
                    });
                }
            });
            if($scope.dimensionOptions !== undefined || $scope.dimensionOptions !== []) {
                $scope.getProductImagesByIdAndDimension(productID,$scope.dimensionOptions[0].id);
                $scope.selectedDimension=$scope.dimensionOptions[0]
            }
            console.log("$scope.dimensionOptions:", $scope.dimensionOptions);
            console.log("Response::::", data.DIMENSION);
                if (!$scope.$$phase) {
                    $scope.$apply();
                }
        });

        $("#productDimensionImageUploadModal").modal("show");
    }

    $scope.viewCityProductImage = function (productID, productName) {
            $scope.productID = productID;
            $scope.productNameDesc = productName;
            var cityList = getCityList();
            $scope.selectedCity = cityList[0];
            $scope.selectedDaySlot = "ALL";
            if(cityList!=null && cityList.length > 0){
                    $scope.getCityProductImagesById($scope.productID,cityList[0],$scope.selectedDaySlot);
                    $("#productCityImageUploadModal").modal("show");
            }

    }

    $scope.updateCity = function(city){
       $scope.selectedCity = city;
       //$scope.getCityProductImagesById($scope.productID,$scope.selectedCity.name)
    }
    $scope.updateDaySlot = function(daySlot){
       $scope.selectedDaySlot = daySlot;
       $scope.getCityProductImagesById($scope.productID,$scope.selectedCity.name,$scope.selectedDaySlot)
    }

    function getCityList(){
         $http({
                 method: 'POST',
                 url: AppUtil.restUrls.masterCacheManagement.unitCities
         }).then(function success(response) {
                               if (response.status == 200) {
                                  $scope.cityList = response.data;
                               } else {
                                   console.log(response.data);
                                   alert(response.statusText);
                                   $scope.loading = false;
                               }
                           }, function error(response) {
                              console.log("error:" + response);
                           });

         return $scope.cityList;

    }


    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function isImage(fileExt) {
        return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png" || fileExt == "webp";
    }

    $scope.getProductImagesById = function (productId) {
        if (productId == null) {
            bootbox.alert('Product Id not available');
            return;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.productMetaData.uploadedProductImage + "?productId=" + productId,
        }).then(function success(response) {
            console.log("success:" + response);
            $rootScope.showFullScreenLoader = false;
            if (response != null && response.status == 200) {
                $scope.productImageDetails = response.data;
                console.log("Before printing product image details :",response);
                console.log("Product Image details ----1>>>",$scope.productImageDetails);
            } else {
                console.log("error:" + response);
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
        });
    };

    $scope.getCityProductImagesById = function (productId,city,daySlot) {
            $scope.productCityImageDetails = null;
            if (productId == null) {
                bootbox.alert('Product Id not available');
                return;
            }
             if (city == null) {
                  bootbox.alert('City not available');
                  return;
             }
             if(daySlot == null){
                 bootbox.alert('Day SLot Not Selected');
                  return;
             }
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.productMetaData.uploadedProductImageByIdAndCity + "?productId=" + productId + "&cityName=" + city + "&daySlot=" + daySlot,
            }).then(function success(response) {
                console.log("success:" + response);
                $rootScope.showFullScreenLoader = false;
                if (response != null && response.status == 200) {
                    $scope.productCityImageDetails = response.data;
                    console.log("Before printing product image details :",response);
                    console.log("Product Image details ----1>>>",$scope.productImageDetails);
                } else {
                    console.log("error:" + response);
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

    $scope.getProductImagesByIdAndDimension = function (productId,dimensionCode) {
            if (productId == null) {
                bootbox.alert('Product Id not available');
                return;
            }
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.productMetaData.uploadedProductDimensionImage + "?productId=" + productId + '&dimension='+dimensionCode,
            }).then(function success(response) {
                console.log("success:" + response);
                $rootScope.showFullScreenLoader = false;
                if (response != null && response.status == 200) {
                    $scope.productDimensionImageDetails = response.data;
                    console.log("Before printing product image details :",response);
                    console.log("Product Image details ----1>>>",$scope.productDimensionImageDetails,$scope.imageSuffixNew);
                } else {
                    console.log("error:" + response);
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

    $scope.updateIndex = function (index) {
        if (($scope.imageCategoryType.name === 'GRID_MENU_LOW' || $scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP' || $scope.imageCategoryType.name==='MARKETING_IMAGE_WEB_VIEW' || $scope.imageCategoryType.name==='MARKETING_IMAGE_MOB_VIEW' ||
         $scope.imageCategoryType.name ==='GRID_MENU_100X100' || $scope.imageCategoryType.name ==='GRID_MENU_400X400' || $scope.imageCategoryType.name ==="RECOMMENDATION_IMAGE_1200X1200") && index <= 10)
            $scope.indexOfImage = index;
        else
            $scope.indexOfImage = 1;
    }

    $scope.openCloneProductModal = function(){
       $("#cloneProductModal").modal("show");
   }

   $scope.onChangeSelectedCloneProduct = function(selectedCloneProductId){
     // $scope.selectedCloneProduct = selectedCloneProduct;
   }

   $scope.cloneProduct = function(cloneProductId){
   var cloneProduct = $scope.productDetailsList.filter(function(product){
   return product.id == cloneProductId;
   })[0];
      $scope.addProduct();
      //var cloneProduct = JSON.parse(cloneProductTemp)
      $scope.productName = cloneProduct.name;
      $scope.productDescription = cloneProduct.description;
      $scope.productShortCode = cloneProduct.shortCode;

      $scope.selectedProductType = $scope.productCategory[getIndexofProductCategory(cloneProduct.type, $scope.productCategory)];
      $scope.selectetProductSubtypeTemp = $scope.selectedProductType.content[getIndexofProductSubCategory(cloneProduct.subType, $scope.productCategory)];
      $scope.selectedProductSubTypeData = $scope.selectedProductType.content;
      $scope.selectetProductSubtype = $scope.selectetProductSubtypeTemp;
      $scope.selectedStationCategory = $scope.workStationsStationCategory[getIndexOfStationCategory(cloneProduct.stationCategory, $scope.workStationsStationCategory)];
      $scope.selectProductAttribute = {
                            code: cloneProduct.attribute,
                            name: cloneProduct.attribute
                        };
      $scope.productSKU = cloneProduct.skuCode;
       $scope.selectPriceType = {code: cloneProduct.billType, name: cloneProduct.billType};
      if(AppUtil.isEmptyObject($scope.selectedPreparationMode)){
               $scope.selectedPreparationMode = {
                  code : null
                };
      }
      $scope.selectProductAdons = $scope.productAdons[getIndexOfProduct(cloneProduct.addOnProfile, $scope.productAdons)];
      $scope.selectetProductDimension = $scope.productDimension[getIndexofDimension(cloneProduct.dimensionProfileId, $scope.productDimension)];

      $scope.selectTaxCode = $scope.taxCategory[getIndexofTaxCategory(cloneProduct.taxCode, $scope.taxCategory)];
    /*  $scope.selectProductAdons.detail ={
       name : cloneProduct.addOnProfile,
       code : cloneProduct.addOnProfile
      };*/
      $scope.selectInventoryTracked = {
                            code: cloneProduct.inventoryTracked,
                            name: cloneProduct.inventoryTracked
      };
      $scope.selectedPreparationMode = $scope.singleProduct.preparation != null ? {
                            code: cloneProduct.preparation,
                            name: cloneProduct.preparation
                        } : null;
                        $scope.selectProductAttribute = {
                            code: cloneProduct.attribute,
                            name: cloneProduct.attribute
                        };
                        $scope.selectEmployeeMealComponent = {
                            code: cloneProduct.employeeMealComponent,
                            name: cloneProduct.employeeMealComponent
                        };
                        $scope.selectVariantOrderLevel = {
                            code: cloneProduct.supportsVariantLevelOrdering,
                            name: cloneProduct.supportsVariantLevelOrdering
                        };
                        $scope.selectTaxableCogs = {
                            code: cloneProduct.taxableCogs,
                            name: cloneProduct.taxableCogs
                        };

      $scope.selectedProductClassification = cloneProduct.classification;
      $scope.selectedWebType = $scope.webAppType[getIndexofWebType(cloneProduct.webType, $scope.webAppType)];


      $scope.productPrepTime = cloneProduct.prepTime;

      $scope.selectedBrand = $scope.brands.filter(function (brand){return brand.brandId === cloneProduct.brandId})[0];
      $scope.selectedInventoryTrackedLevel = cloneProduct.inventoryTrackedLevel;
      $scope.selectedRegularTags = cloneProduct.regularTags;
      $scope.selectedNutritionTags = cloneProduct.nutritionTags;
   }
    $scope.uploadProductVideo = function (showCaseVideo) {
        $scope.indexOfImage = 1;
        $scope.videoLink = showCaseVideo;
        var fd = new FormData();

        var data=AppUtil.getUserValues();
        fd.append('imageCategoryType', $scope.imageCategoryType.name);
        fd.append('updatedBy', data.userId);
        fd.append('productId', $scope.productID);
        fd.append('Link', $scope.videoLink);
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.productMetaData.uploadProductImage,
            method: 'POST',
            data: fd,
            headers: {'Content-Type': undefined},
            transformRequest: angular.identity
        }).then(function success(response) {
            console.log("success:" + response);
            $rootScope.showFullScreenLoader = false;
            $scope.imageCategoryType = $scope.imageCategoryList[0];
            angular.element("input[type='file']").val(null);
            if (response != null && response.status == 200) {
                $scope.getProductImagesById($scope.productID)
                bootbox.alert("Upload successful");
            } else {
                bootbox.alert("Error while uploading Product Image.");
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
            bootbox.alert("Upload failed");
        });

    };

    $scope.uploadProductPicForCity = function(cityName,daySlot){

              var fileExt = getFileExtension(fileService.getFile().name);

               if (isImage(fileExt.toLowerCase())) {

                   var mimeType = fileExt.toUpperCase();
                   var fd = new FormData();
                   var data=AppUtil.getUserValues();
                   fd.append('mimeType', fileExt.toUpperCase());
                   fd.append('file', fileService.getFile());
                   fd.append('updatedBy', data.userId);
                   fd.append('productId', $scope.productID);
                   fd.append('cityName', cityName);
                   fd.append('daySlot', daySlot);
                   $rootScope.showFullScreenLoader = true;

                   $http({
                       url: AppUtil.restUrls.productMetaData.uploadCityProductImage,
                       method: 'POST',
                       data: fd,
                       headers: {'Content-Type': undefined},
                       transformRequest: angular.identity
                   }).then(function success(response) {
                       console.log("success:" + response);
                       $rootScope.showFullScreenLoader = false;
                      // $scope.imageCategoryType = $scope.imageCategoryList[0];
                       angular.element("input[type='file']").val(null);
                       if (response != null && response.status == 200) {
                           $scope.getCityProductImagesById($scope.productID,cityName,daySlot)
                           bootbox.alert("Upload successful");
                       } else {
                           bootbox.alert("Error while uploading Product Image.");
                       }
                   }, function error(response) {
                       console.log("error:" + response);
                       $rootScope.showFullScreenLoader = false;
                       bootbox.alert("Upload failed");
                   });
               } else {
                   bootbox.alert('Upload Failed , File Format not Supported');
               }


    }

    $scope.deactivateImageForCityAndSlot = function(city , daySlot){
                  $http({
                          method: 'GET',
                          url: AppUtil.restUrls.productMetaData.deactivateImageForCityAndSlot + "?productId=" + $scope.productID + "&cityName=" +  city.name + "&daySlot=" + daySlot,
                      }).then(function success(response) {
                          console.log("success:" + response);
                          $rootScope.showFullScreenLoader = false;
                          if (response != null && response.status == 200) {
                              $scope.getCityProductImagesById($scope.productID,city.name,daySlot);
                          } else {
                              console.log("error:" + response);
                          }
                      }, function error(response) {
                          console.log("error:" + response);
                          $rootScope.showFullScreenLoader = false;
                      });

    }


    $scope.uploadProductPic = function () {

        // if(fileService.getFile().size > 1048576){
        //     bootbox.alert('File size should not be greater than 1 MB or 1024 KB');
        //     return;
        // }
        //
        // if ($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP' && fileService.getFile().size > 102400) {
        //     bootbox.alert('Image size should not be greater than 100 kb');
        //     return;
        // }
        //
        // if ($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP' && getFileExtension(fileService.getFile().name) !== "webp") {
        //     bootbox.alert('Image extension should be .webp');
        //     return;
        // }
        // if ($scope.imageCategoryType.name == 'GRID_MENU_HIGH' && fileService.getFile().size > 358400) {
        //     bootbox.alert('File size should not be greater than 350 kb');
        //     return;
        // }
        // if ($scope.imageCategoryType.name == 'LIST_MENU_HIGH' && fileService.getFile().size > 102400) {
        //     bootbox.alert('File size should not be greater than 100 kb');
        //     return;
        // }

        var fileExt = getFileExtension(fileService.getFile().name);

        if (isImage(fileExt.toLowerCase())) {

            var mimeType = fileExt.toUpperCase();
            var fd = new FormData();
            var data=AppUtil.getUserValues();
            fd.append('mimeType', fileExt.toUpperCase());
            fd.append('file', fileService.getFile());
            fd.append('imageCategoryType', $scope.imageCategoryType.name);
            fd.append('updatedBy', data.userId);
            fd.append('productId', $scope.productID);
            fd.append('index', $scope.indexOfImage);
            $rootScope.showFullScreenLoader = true;

            $http({
                url: AppUtil.restUrls.productMetaData.uploadProductImage,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).then(function success(response) {
                console.log("success:" + response);
                $rootScope.showFullScreenLoader = false;
                $scope.imageCategoryType = $scope.imageCategoryList[0];
                angular.element("input[type='file']").val(null);
                if (response != null && response.status == 200) {
                    $scope.getProductImagesById($scope.productID)
                    bootbox.alert("Upload successful");
                } else {
                    bootbox.alert("Error while uploading Product Image.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
                bootbox.alert("Upload failed");
            });
        } else {
            bootbox.alert('Upload Failed , File Format not Supported');
        }
    };

    $scope.uploadProductDimensionPic = function () {

        var fileExt = getFileExtension(fileService.getFile().name);

        if (isImage(fileExt.toLowerCase())) {

            var mimeType = fileExt.toUpperCase();
            var fd = new FormData();
            var data=AppUtil.getUserValues();
            fd.append('mimeType', fileExt.toUpperCase());
            fd.append('file', fileService.getFile());
            fd.append('imageCategoryType', $scope.imageCategoryType.name);
            fd.append('updatedBy', data.userId);
            fd.append('productId', $scope.productID);
            fd.append('index', $scope.indexOfImage);
            fd.append('dimension',$scope.selectedDimension.id);
            $rootScope.showFullScreenLoader = true;

            $http({
                url: AppUtil.restUrls.productMetaData.uploadProductDimensionImage,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).then(function success(response) {
                console.log("success:" + response);
                $rootScope.showFullScreenLoader = false;
                $scope.imageCategoryType = $scope.imageCategoryList[0];
                angular.element("input[type='file']").val(null);
                if (response != null && response.status == 200) {
                    $scope.getProductImagesByIdAndDimension($scope.productID,$scope.selectedDimension.id)
                    bootbox.alert("Upload successful");
                } else {
                    bootbox.alert("Error while uploading Product Image.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
                bootbox.alert("Upload failed");
            });
        } else {
            bootbox.alert('Upload Failed , File Format not Supported');
        }
    };

    $scope.checkFileDimensionsAndUploadForCityLevel = function () {
            console.log("selected City :::: ", $scope.selectedCity)
            console.log("selected City :::: ", $scope.model.selectedCity)
            if($scope.selectedCity == null){
                bootbox.alert('Please select city First');
                return;
            }

            if (fileService.getFile() == null
                || fileService.getFile() == undefined) {
                bootbox.alert('File cannot be empty');
                return;
            }



            if(fileService.getFile().size > 1048576){
                bootbox.alert('File size should not be greater than 1 MB or 1024 KB');
                return;
            }
            var fileReader = new FileReader;
            fileReader.onload = function () {
                $scope.toUploadImagePath = new Image;
                $scope.toUploadImagePath.onload = function () {
                   return $scope.uploadProductPicForCity($scope.selectedCity.name,$scope.selectedDaySlot);
                };
                $scope.toUploadImagePath.src = fileReader.result;
            };
            console.log(fileService.getFile());
            fileReader.readAsDataURL(fileService.getFile());
        };

    $scope.checkFileDimensionsAndUpload = function () {

        if($scope.indexOfImage == null || $scope.indexOfImage == '' || $scope.indexOfImage==undefined){
            bootbox.alert('Please select or enter index');
            return;
        }

        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert('File cannot be empty');
            return;
        }

          if ($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP' && fileService.getFile().size > 102400) {
                    bootbox.alert('Image size should not be greater than 100 kb');
                    return;
          }


        if(fileService.getFile().size > 1048576){
            bootbox.alert('File size should not be greater than 1 MB or 1024 KB');
            return;
        }

        if (($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP'||$scope.imageCategoryType.name ==='GRID_MENU_100X100' || $scope.imageCategoryType.name ==='GRID_MENU_400X400' || $scope.imageCategoryType.name ==='RECOMMENDATION_IMAGE_1200X1200')  && getFileExtension(fileService.getFile().name) !== "webp") {
                    bootbox.alert('Image extension should be .webp');
                    return;
        }
        var fileReader = new FileReader;
        fileReader.onload = function () {
            $scope.toUploadImagePath = new Image;
            $scope.toUploadImagePath.onload = function () {
                if ($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP') {
                    if (this.width === 600 && this.width === this.height) {
                        return $scope.uploadProductPic();
                    } else {
                        bootbox.alert('Image aspect ratio should be 600px X 600px');
                        return false;
                    }
                } else if($scope.imageCategoryType.name ==='GRID_MENU_100X100'){
                     return $scope.uploadProductPic();
                } else if($scope.imageCategoryType.name ==='GRID_MENU_400X400'){
                     return $scope.uploadProductPic();
                }else {
                     return $scope.uploadProductPic();
                }
            };
            $scope.toUploadImagePath.src = fileReader.result;
        };
        console.log(fileService.getFile());
        fileReader.readAsDataURL(fileService.getFile());
    };

    $scope.checkFileDimensionsAndUploadNew = function () {

            if($scope.indexOfImage == null || $scope.indexOfImage == '' || $scope.indexOfImage==undefined){
                bootbox.alert('Please select or enter index');
                return;
            }

            if (fileService.getFile() == null
                || fileService.getFile() == undefined) {
                bootbox.alert('File cannot be empty');
                return;
            }

            if (!$scope.selectedDimension) {
                bootbox.alert('Please select an image dimension');
                return;
            }

            if ($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP' && fileService.getFile().size > 102400) {
                bootbox.alert('Image size should not be greater than 100 kb');
                return;
            }

            if(fileService.getFile().size > 1048576){
                bootbox.alert('File size should not be greater than 1 MB or 1024 KB');
                return;
            }

            if (($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP'||$scope.imageCategoryType.name ==='GRID_MENU_100X100' || $scope.imageCategoryType.name ==='GRID_MENU_400X400' || $scope.imageCategoryType.name ==='RECOMMENDATION_IMAGE_1200X1200')  && getFileExtension(fileService.getFile().name) !== "webp") {
                bootbox.alert('Image extension should be .webp');
                return;
            }

            var fileReader = new FileReader;
            fileReader.onload = function () {
                $scope.toUploadImagePath = new Image;
                $scope.toUploadImagePath.onload = function () {
                    if ($scope.imageCategoryType.name === 'GRID_MENU_LOW_WEBP') {
                        if (this.width === 600 && this.width === this.height) {
                            return $scope.uploadProductDimensionPic();
                        } else {
                            bootbox.alert('Image aspect ratio should be 600px X 600px');
                            return false;
                        }
                    } else if($scope.imageCategoryType.name ==='GRID_MENU_100X100'){
                         return $scope.uploadProductDimensionPic();
                    } else if($scope.imageCategoryType.name ==='GRID_MENU_400X400'){
                         return $scope.uploadProductDimensionPic();
                    }else {
                         return $scope.uploadProductDimensionPic();
                    }
                };
                $scope.toUploadImagePath.src = fileReader.result;
            };
            console.log(fileService.getFile());
            fileReader.readAsDataURL(fileService.getFile());
        };

    $scope.inActiveProduct = function (stausID, catStatus) {
        $scope.isRequiredImagesUploadedForProducts = true;
       /* if (catStatus == "ACTIVE") {
            $scope.getProductImagesById(stausID);
            checkForUploadedImageForProduct();
        }*/
        var productStatus = "";
        if (catStatus == "IN_ACTIVE") {
            productStatus = 'Deactivate';
        }
        else if (catStatus == "ACTIVE") {
            productStatus = 'Activate';
        }

        if ($window.confirm("Are you sure, you want to " + productStatus + " Product?")) {
            //console.log("YES");;
        } else {
            return false;
        }

        if(!$scope.isRequiredImagesUploadedForProducts){
            alert("Upload missing product-image with type : " + $scope.imageMissingType);
            return;
        }

        var updateCatStatusObj = {
            status: catStatus,
        };
        $scope.productDetailsList.forEach(function (statusUpdate) {
            if (statusUpdate.id === stausID) {
                statusUpdate.status = catStatus;

                if (catStatus == "IN_ACTIVE") {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.deactivateProduct,
                        data: stausID
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.status != 200) {
                            alert("Product Status not updated");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
                else if (catStatus == "ACTIVE") {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.activateProduct,
                        data: stausID
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.status != 200) {
                            alert("Product Status not updated");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }

            }
        });
    }

    $scope.sort_by = function (predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };

    $scope.submitAddProduct = function () {
        if ($scope.productName == "" || $scope.productName == null) {
            alert("Please input the Product Name.");
            return;
        }

        if ($scope.productDescription == "" || $scope.productDescription == null) {
            alert("Please input the Product Description.");
            return;
        }
        if ($scope.productShortCode == "" || $scope.productShortCode == null) {
            alert("Please input the Product Short Code.");
            return;
        }
        if ($scope.selectetProductSubtype == "" || $scope.selectetProductSubtype == null) {
            alert("Product Subtype is empty.");
            return;
        }
        if ($scope.selectedStationCategory == "" || $scope.selectedStationCategory == null) {
            alert("Product Station Category is empty.");
            return;
        }

        if ($scope.productStartDate == "" || $scope.productStartDate == null) {
            alert("Product Start Date is Empty.");
            return;
        }
        if ($scope.productEndDate == "" || $scope.productEndDate == null) {
            alert("Product End Date is Empty.");
            return;
        }
        if ($scope.productSKU == "" || $scope.productSKU == null) {
            alert("Product SKU Code is Empty.");
            return;
        }
        if ($scope.productPrepTime == "" || $scope.productPrepTime == null) {
            alert("Product Prep Time is Empty.");
            return;
        }
        if ($scope.selectedBrand == "" || $scope.selectedBrand == null) {
            alert("Brand is Empty.");
            return;
        }

        if ($scope.selectedInventoryTrackedLevel == "" || $scope.selectedInventoryTrackedLevel == null) {
            alert("Inventory Tracked level is Empty.");
            return;
        }

        var nutritionTags = [];
        var regularTags = [];
        $scope.selectedRegularTags.forEach(function(tag){
             regularTags.push(tag.id);
        });
        $scope.selectedNutritionTags.forEach(function(tag){
             nutritionTags.push(tag.id);
        });

        $scope.loading = true;
        $scope.addBtnShow = true;
        var productObj = {
            id: null,
            name: $scope.productName,
            description: $scope.productDescription,
            shortCode: $scope.productShortCode,
            hasSizeProfile: true,
            hasAddons: true,
            //vendor:$scope.selectProductVender.vendorId,
            type: $scope.selectedProductType.detail.id,
            subType: $scope.selectetProductSubtype.id,
            stationCategory: $scope.selectedStationCategory.id,
            attribute: $scope.selectProductAttribute.code,
            skuCode: $scope.productSKU,
            dimensionProfileId: $scope.selectetProductDimension.detail.id,
            billType: $scope.selectPriceType.code,
            preparation: $scope.selectedPreparationMode != null ? $scope.selectedPreparationMode.code : null,
            status: "IN_ACTIVE",
            taxCode: $scope.selectTaxCode.code,
            addOnProfile: $scope.selectProductAdons.detail.code,
            inventoryTracked: $scope.selectInventoryTracked.code,
            employeeMealComponent: $scope.selectEmployeeMealComponent.code,
            taxableCogs: $scope.selectTaxableCogs.code,
            supportsVariantLevelOrdering: $scope.selectVariantOrderLevel.code,
            startDate: $scope.productStartDate,
            endDate: $scope.productEndDate,
            classification: $scope.selectedProductClassification,
            webType: $scope.selectedWebType == null ? null : $scope.selectedWebType.id,
            prices: [],
            prepTime: parseFloat($scope.productPrepTime),
            brandId: $scope.selectedBrand.brandId,
            inventoryTrackedLevel : $scope.selectedInventoryTrackedLevel,
            regularTagIds : regularTags,
            nutritionTagIds : nutritionTags
        }

        /*if(addProductForm.productName.$valid=="false")
            {
            $scope.validation="Product Type is empty";
            return false;			}
        else
            {
            $scope.validation="";
            }*/
        //	console.log("tt=",productObj)
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.addProduct,
            data: productObj,
        }).then(function success(response) {
            if (response.status == 200) {
                alert("Product added successfully!");
                $("#productModal").modal("hide");
                $scope.loading = false;
                $scope.addBtnShow = false;
                $scope.loadProducts();
            } else {
                console.log(response.data);
                alert(response.statusText);
                $scope.loading = false;
                $scope.addBtnShow = false;
                //console.log(response);
            }
        }, function error(response) {

            console.log("error:" + response);
        });
    }


    $scope.viewProductDetailsData = function (actionData, prouductID, prodStatus) {
        $scope.loading = false;
        var newProdList = [];
        for (var i in $scope.productDetailsList) {
            if ($scope.productDetailsList[i].id === prouductID) {
                newProdList.push($scope.productDetailsList[i]);
                oldProductName = $scope.productDetailsList[i].name;
            }
        }
        $scope.singleProduct = newProdList[0];
        $scope.selectProductAdons = $scope.productAdons[getIndexOfProduct($scope.singleProduct.addOnProfile, $scope.productAdons)];
        $scope.selectetProductDimension = $scope.productDimension[getIndexofDimension($scope.singleProduct.dimensionProfileId, $scope.productDimension)];
        $scope.action = actionData;
        $scope.prodID = prouductID;
        $scope.prodStatus = prodStatus;
        $("#productModal").modal("show");

        $scope.$watch('singleProduct', function (newVal, oldVal) {
            $scope.productName = $scope.singleProduct.name;
            $scope.productDescription = $scope.singleProduct.description;
            $scope.productShortCode = $scope.singleProduct.shortCode;
            $scope.selectedProductClassification = $scope.singleProduct.classification;
            $scope.selectedProductType = $scope.productCategory[getIndexofProductCategory($scope.singleProduct.type, $scope.productCategory)];
            //console.log($scope.selectedProductType);
            //console.log("productSubTypeResult=",getIndexofProductSubCategory($scope.singleProduct.subType,$scope.productCategory));
            $scope.selectedProductSubTypeData1 = $scope.selectedProductType.content[getIndexofProductSubCategory($scope.singleProduct.subType, $scope.productCategory)];
            //console.log("result here=",$scope.selectedProductSubTypeData1);
            //console.log(getIndexofProductSubCategory($scope.singleProduct.subType,$scope.productCategory));
            $scope.selectedProductSubTypeData = $scope.selectedProductType.content;
            $scope.selectetProductSubtype = $scope.selectedProductSubTypeData1;
            $scope.selectedStationCategory = $scope.workStationsStationCategory[getIndexOfStationCategory($scope.singleProduct.stationCategory, $scope.workStationsStationCategory)];
            $scope.selectTaxCode = $scope.taxCategory[getIndexofTaxCategory($scope.singleProduct.taxCode, $scope.taxCategory)];

            $scope.selectedProductType.taxCode;


            $scope.productSKU = $scope.singleProduct.skuCode;
            $scope.productPrepTime = $scope.singleProduct.prepTime;
            //$scope.selectedWebType				=	{id:$scope.singleProduct.webType.id, name:$scope.singleProduct.webType.name};

            $scope.selectedWebType = $scope.webAppType[getIndexofWebType($scope.singleProduct.webType, $scope.webAppType)];
            $scope.productStartDate = $scope.singleProduct.startDate;
            $scope.productEndDate = $scope.singleProduct.endDate;
            $scope.selectInventoryTracked = {
                code: $scope.singleProduct.inventoryTracked,
                name: $scope.singleProduct.inventoryTracked
            };
            $scope.selectPriceType = {code: $scope.singleProduct.billType, name: $scope.singleProduct.billType};
            $scope.selectedPreparationMode = $scope.singleProduct.preparation != null ? {
                code: $scope.singleProduct.preparation,
                name: $scope.singleProduct.preparation
            } : null;
            $scope.selectProductAttribute = {
                code: $scope.singleProduct.attribute,
                name: $scope.singleProduct.attribute
            };
            $scope.selectEmployeeMealComponent = {
                code: $scope.singleProduct.employeeMealComponent,
                name: $scope.singleProduct.employeeMealComponent
            };
            $scope.selectVariantOrderLevel = {
                code: $scope.singleProduct.supportsVariantLevelOrdering,
                name: $scope.singleProduct.supportsVariantLevelOrdering
            };
            $scope.selectTaxableCogs = {
                code: $scope.singleProduct.taxableCogs,
                name: $scope.singleProduct.taxableCogs
            };
            $scope.selectedBrand = $scope.brands.filter(function (brand){return brand.brandId === $scope.singleProduct.brandId})[0];

            $scope.selectedInventoryTrackedLevel = $scope.singleProduct.inventoryTrackedLevel;
            $scope.selectedRegularTags = [];
            $scope.selectedNutritionTags = [];
            $scope.singleProduct.regularTags.forEach(function (regularTags) {
                $scope.regularTagList.forEach(function (list) {
                    if (regularTags.id == list.id) {
                        $scope.selectedRegularTags.push(list);
                    }
                });
            });
            $scope.singleProduct.nutritionTags.forEach(function (tags) {
                $scope.nutritionTagList.forEach(function (list) {
                    if (tags.id == list.id) {
                        $scope.selectedNutritionTags.push(list);
                    }
                })
            });
            //console.log($scope.attribute);

            //$scope.selectProductAttribute=$scope.attribute[1];
        });
    }

    function getIndexOfProduct(id, productAdOns) {
        for (var index in productAdOns) {
            if (productAdOns[index].detail.code == id) {
                return index;
            }
        }
    }

    function getIndexofDimension(dimensionID, productDimensions) {
        for (var index in productDimensions) {
            if (productDimensions[index].detail.id == dimensionID) {
                return index;
            }
        }
    }

    function getIndexofWebType(webTypeID, webTypeData) {
        for (var index in webTypeData) {
            if (webTypeData[index].id == webTypeID) {
                return index;
            }
        }
    }


    function getIndexofProductCategory(categoryID, productCategory) {
        //console.log("categoryID",categoryID);
        //console.log("productCategory",productCategory);

        for (var categoryDet in productCategory) {
            if (productCategory[categoryDet].detail.id == categoryID) {
                //console.log(categoryDet);
                return categoryDet;
            }
        }
    }

    function getIndexOfStationCategory(stationCategoryId, stationCategory) {
        for (var stationCat in stationCategory) {
            if (stationCategory[stationCat].id == stationCategoryId) {
                return stationCat;
            }
        }
    }

    function getIndexofTaxCategory(categoryID, taxCategory) {
        //console.log("categoryID",categoryID);
        //console.log("productCategory",productCategory);

        for (var categoryDet in taxCategory) {
            if (taxCategory[categoryDet].code == categoryID) {
                //console.log(categoryDet);
                return categoryDet;
            }
        }
    }

    function getIndexofProductSubCategory(categoryID, productSubCategory) {
        for (var categorySubDet in productSubCategory) {
            for (var j = 0; j < productSubCategory[categorySubDet].content.length; j++) {
                if (productSubCategory[categorySubDet].content[j].id === categoryID) {
                    return j
                }
            }
        }
    }

    $scope.getBrandList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.brandList = response.data;
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.setPreparationMode = function (mode) {
        $scope.selectedPreparationMode = mode;
    }

    $scope.getProductAliasMapping = function (productId) {
        $http({
            method: 'GET',
            params: {entityId: productId, entityType: "PRODUCT"},
            url: AppUtil.restUrls.aliasManagement.getEntityAliasBYIdandType,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.assocatedAliasMappings = response.data;
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
        $scope.selectedBrandAliasMapping = {
            entityAliasMappingId: null,
            entityId: productId,
            entityType: "PRODUCT",
            brandId: null,
            alias: "",
            description: "",
            imageUrl: null
        };

        $("#aliasModal").modal("show");
    }

    $scope.setAlias = function (brandId) {
        for (var i in $scope.assocatedAliasMappings) {
            if ($scope.assocatedAliasMappings[i].brandId == brandId) {
                $scope.selectedBrandAliasMapping.alias = $scope.assocatedAliasMappings[i].alias;
                $scope.selectedBrandAliasMapping.entityAliasMappingId = $scope.assocatedAliasMappings[i].entityAliasMappingId;
                $scope.selectedBrandAliasMapping.description = $scope.assocatedAliasMappings[i].description;
                return;
            }
        }
        $scope.selectedBrandAliasMapping.alias = "";
        $scope.selectedBrandAliasMapping.entityAliasMappingId = null;
        $scope.selectedBrandAliasMapping.description = null;
        console.log($scope.selectedBrandAliasMapping);
    }

    $scope.getBrandName = function (brandId) {
        for (var i in $scope.brandList) {
            if (brandId == $scope.brandList[i].brandId) {
                return $scope.brandList[i].brandName;
            }
        }
    }

    $scope.addAliasMapping = function ($event) {
        $event.stopPropagation();
        $event.preventDefault();
        if (($scope.selectedBrandAliasMapping.alias == null
            || $scope.selectedBrandAliasMapping.alias == ""
            || $scope.selectedBrandAliasMapping.alias.length == 0)
            && ($scope.selectedBrandAliasMapping.description == null
                || $scope.selectedBrandAliasMapping.description == ""
                || $scope.selectedBrandAliasMapping.description.length == 0)) {
            alert("Both Alias and Description cannot be empty");
            return;
        }
        if (($scope.selectedBrandAliasMapping.alias == null
            || $scope.selectedBrandAliasMapping.alias == ""
            || $scope.selectedBrandAliasMapping.alias.length == 0)) {
            $scope.selectedBrandAliasMapping.alias = null;
        }
        if ($scope.selectedBrandAliasMapping.description == null
            || $scope.selectedBrandAliasMapping.description == ""
            || $scope.selectedBrandAliasMapping.description.length == 0) {
            $scope.selectedBrandAliasMapping.description = null
        } else if ($scope.selectedBrandAliasMapping.description.length > 255) {
            alert("Description cannot contain more than 255 characters");
            return;
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.aliasManagement.addAliasMapping,
            data: $scope.selectedBrandAliasMapping,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.assocatedAliasMappings.push(angular.copy(response.data));
                $scope.selectedBrandAliasMapping = response.data;
                console.log(response.data);
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });

    }


    $scope.updateAliasMapping = function ($event) {
        $event.stopPropagation();
        $event.preventDefault();
        if (($scope.selectedBrandAliasMapping.alias == null
            || $scope.selectedBrandAliasMapping.alias == ""
            || $scope.selectedBrandAliasMapping.alias.length == 0)
            && ($scope.selectedBrandAliasMapping.description == null
                || $scope.selectedBrandAliasMapping.description == ""
                || $scope.selectedBrandAliasMapping.description.length == 0)) {
            alert("Both Alias and Description cannot be empty");
            return;
        }
        if (($scope.selectedBrandAliasMapping.alias == null
            || $scope.selectedBrandAliasMapping.alias == ""
            || $scope.selectedBrandAliasMapping.alias.length == 0)) {
            $scope.selectedBrandAliasMapping.alias = null;
        }
        if ($scope.selectedBrandAliasMapping.description == null
            || $scope.selectedBrandAliasMapping.description == ""
            || $scope.selectedBrandAliasMapping.description.length == 0) {
            $scope.selectedBrandAliasMapping.description = null
        } else if ($scope.selectedBrandAliasMapping.description.length > 255) {
            alert("Description cannot contain more than 255 characters");
            return;
        }

        $http({
            method: 'PUT',
            url: AppUtil.restUrls.aliasManagement.updateAliasMapping,
            data: $scope.selectedBrandAliasMapping,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                console.log(response.data);
                $scope.selectedBrandAliasMapping = response.data;
                for (var i in $scope.assocatedAliasMappings) {
                    if ($scope.selectedBrandAliasMapping.brandId == $scope.assocatedAliasMappings[i].brandId) {
                        $scope.assocatedAliasMappings.splice(i, 1, angular.copy($scope.selectedBrandAliasMapping))
                    }
                }
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }
    $scope.submitUpdateProduct = function (prouductID) {
        if ($scope.productName == "" || $scope.productName == null) {
            alert("Please input the Product Name.");
            return;
        }

        if ($scope.productDescription == "" || $scope.productDescription == null) {
            alert("Please input the Product Description.");
            return;
        }
        if ($scope.productShortCode == "" || $scope.productShortCode == null) {
            alert("Please input the Product Short Code.");
            return;
        }

        if ($scope.selectetProductSubtype == "" || $scope.selectetProductSubtype == null) {
            alert("Product Subtype is empty.");
            return;
        }

        if ($scope.selectedStationCategory == "" || $scope.selectedStationCategory == null) {
            alert("Station Category is empty.");
            return;
        }
        if ($scope.productStartDate == "" || $scope.productStartDate == null) {
            alert("Product Start Date is Empty.");
            return;
        }

        if ($scope.productEndDate == "" || $scope.productEndDate == null) {
            alert("Product End Date is Empty.");
            return;
        }

        if ($scope.productSKU == "" || $scope.productSKU == null) {
            alert("Product SKU Code is Empty.");
            return;
        }
        if($scope.productPrepTime=="" || $scope.productPrepTime==null){
            alert("Preparation Time is empty");
            return;
        }

        if ($scope.selectedBrand == "" || $scope.selectedBrand == null) {
            alert("Brand is Empty.");
            return;
        }

        var regularTagVal = [];
        var nutritionTagVal = [];
        $scope.selectedRegularTags.forEach(function(tag){
            var idVal = angular.copy($scope.idName);
            idVal.id = tag.id;
            idVal.name = tag.name;
            idVal.code = $scope.categoryLists.REGULAR_TAG[0].detail.id;
            regularTagVal.push(idVal);
        });
        $scope.selectedNutritionTags.forEach(function(tag){
            var idVal = angular.copy($scope.idName);
            idVal.id = tag.id;
            idVal.name = tag.name;
            idVal.code = $scope.categoryLists.NUTRITION_TAG[0].detail.id;
            nutritionTagVal.push(idVal);
        });

        $scope.loading = true;
        $scope.updateBtnHide = true;
        var productUpdateObj = {
            id: prouductID,
            name: $scope.productName,
            description: $scope.productDescription,
            shortCode: $scope.productShortCode,
            hasSizeProfile: true,
            hasAddons: true,
            type: $scope.selectedProductType.detail.id,
            subType: $scope.selectetProductSubtype.id,
            stationCategory: $scope.selectedStationCategory.id,
            attribute: $scope.selectProductAttribute.code,
            skuCode: $scope.productSKU,
            billType: $scope.selectPriceType.code,
            inventoryTracked: $scope.selectInventoryTracked.code,
            employeeMealComponent: $scope.selectEmployeeMealComponent.code,
            taxableCogs: $scope.selectTaxableCogs.code,
            supportsVariantLevelOrdering: $scope.selectVariantOrderLevel.code,
            addOnProfile: $scope.selectProductAdons.detail.code,
            dimensionProfileId: $scope.selectetProductDimension.detail.id,
            preparation: $scope.selectedPreparationMode != null ? $scope.selectedPreparationMode.code : null,
            //vendor: $scope.selectProductVender.vendorId,
            status: $scope.prodStatus,
            taxCode: $scope.selectTaxCode.code,
            startDate: $scope.productStartDate,
            endDate: $scope.productEndDate,
            classification: $scope.selectedProductClassification,
            webType: $scope.selectedWebType == null ? null : $scope.selectedWebType.id,
            prices: [],
            prepTime: parseFloat($scope.productPrepTime),
            brandId: $scope.selectedBrand.brandId,
            updatedBy : $rootScope.userData.id,
            inventoryTrackedLevel : $scope.selectedInventoryTrackedLevel,
            regularTags : regularTagVal,
            nutritionTags : nutritionTagVal
        };

        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.updateProduct,
            data: productUpdateObj,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.updateBtnHide = false;
                alert("Product updated successfully!");
                $("#productModal").modal("hide");
                $scope.loadProducts();
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });

    };

    $scope.getProductDescription = function (productId) {
        $scope.selectedProductIdForDescription = productId;
        $scope.loading = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.productMetaData.getProductDescription + "?productId=" + productId,
        }).then(function success(response) {
            if (response.status == 200) {
                if (response.data != null && !AppUtil.isEmptyObject(response.data)) {
                    editor.setData(response.data.description);
                } else {
                    editor.setData("");
                }
                $("#productDescriptionModal").modal("show");
            } else {
                console.log(response);
            }
            $scope.loading = false;
        }, function error(response) {
            console.log("error:" + response);
            $scope.loading = false;
        });
    };

    $scope.updateProductDescription = function () {
        $scope.loading = true;
        var request = {
            productId: $scope.selectedProductIdForDescription,
            description: editor.getData()
        };
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.updateProductDescription,
            data: request
        }).then(function success(response) {
            if (response.status == 200) {
                if (response.data != null) {
                    editor.setData(response.data.description);
                } else {
                    editor.setData("");
                }
                $("#productDescriptionModal").modal("hide");
            } else {
                console.log(response);
            }
            $scope.loading = false;
        }, function error(response) {
            console.log("error:" + response);
            $scope.loading = false;
        });
    }

    function checkForUploadedImageForProduct() {
        if ($scope.productImageDetails == undefined || $scope.productImageDetails == null) {
            $scope.isRequiredImagesUploadedForProducts = false;
            $scope.imageMissingType = "GRID_MENU_LOW_WEBP,GRID_MENU_100X100,GRID_MENU_400X400";
            return;
        }
        else {
            var imageData = $scope.productImageDetails;
            if (imageData["grids100X100"] == undefined || imageData["grids100X100"] == null || imageData["grids100X100"].length < 1){
                $scope.imageMissingType = "GRID_MENU_100X100";
                $scope.isRequiredImagesUploadedForProducts = false;
                return;
            }
            else if(imageData["grids400X400"] == undefined || imageData["grids400X400"] == null || imageData["grids400X400"].length < 1){
                $scope.imageMissingType = "GRID_MENU_400X400";
                $scope.isRequiredImagesUploadedForProducts = false;
                return;
            }
            else if(imageData["gridLowsWebp"] == undefined || imageData["gridLowsWebp"] == null || imageData["gridLowsWebp"].length < 1) {
                $scope.imageMissingType = "GRID_MENU_LOW_WEBP";
                $scope.isRequiredImagesUploadedForProducts = false;
                return;
            }
        }
    }

    $scope.setNutritionData = function (type, count) {
        if (type == 'CALORIE_COUNT') {
            $scope.calorieCount = count;
        }
        else if (type == 'PROTEIN_COUNT') {
            $scope.proteinCount = count;
        }
        else if (type == 'FAT_COUNT') {
            $scope.fatCount = count;
        }
        else if (type == 'CARBOHYDRATE_COUNT') {
            $scope.carbohydrateCount = count;
        }
        else if (type == 'FIBRE_COUNT') {
            $scope.fibreCount = count;
        }
    }

    function getProductNutritionData() {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.getNutritionDetailBySource,
            data: $scope.sourceTypeList
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.productNutritionData = response.data;
            }
            $scope.showFullScreenLoader = false;
        }, function error(response) {
            console.log("error:" + response);
            $scope.showFullScreenLoader = false;
        });
    }

    $scope.openNutritionDetailModal = function (product) {
        $scope.viewProductNutritionData = product;
        $scope.selectedSourceType = '';
        initializeCountData();
        $("#nutritionFactorModal").modal("show");
    }

    $scope.setCountData = function () {
        if ($scope.productNutritionData[$scope.selectedSourceType] != undefined || $scope.productNutritionData[$scope.selectedSourceType] != null) {
            var data = $scope.productNutritionData[$scope.selectedSourceType];
            if (data[$scope.viewProductNutritionData.id] != undefined || data[$scope.viewProductNutritionData.id] != null) {
                $scope.calorieCount = data[$scope.viewProductNutritionData.id].CALORIE_COUNT;
                $scope.proteinCount = data[$scope.viewProductNutritionData.id].PROTEIN_COUNT;
                $scope.fatCount = data[$scope.viewProductNutritionData.id].FAT_COUNT;
                $scope.carbohydrateCount = data[$scope.viewProductNutritionData.id].CARBOHYDRATE_COUNT;
                $scope.fibreCount = data[$scope.viewProductNutritionData.id].FIBRE_COUNT;
            }
            else {
                initializeCountData();
            }
        }
    }

    function initializeCountData() {
        $scope.calorieCount = null;
        $scope.proteinCount = null;
        $scope.fatCount = null;
        $scope.carbohydrateCount = null;
        $scope.fibreCount = null;
    }

    $scope.closeNutritionModal = function () {
        $scope.calorieCount = null;
        $scope.proteinCount = null;
        $scope.fatCount = null;
        $scope.carbohydrateCount = null;
        $scope.fibreCount = null;
        $("#nutritionFactorModal").modal("hide");
    }


    function checkForNullAndSetValues(updatedData, nutritionDataMap, requestData, type) {
        if (updatedData[type] != undefined && updatedData[type] != null) {
            requestData[type] = updatedData[type];
        }
        else {
            if (nutritionDataMap[type] != undefined && nutritionDataMap[type] != null) {
                requestData[type] = nutritionDataMap[type];
            }
        }
    }

    $scope.submitNutritionData = function () {
        $scope.showFullScreenLoader = true;
        var requestData = {
            CALORIE_COUNT: ($scope.calorieCount != undefined && $scope.calorieCount != null) ? $scope.calorieCount : 0,
            PROTEIN_COUNT: ($scope.proteinCount != undefined && $scope.proteinCount != null) ? $scope.proteinCount : 0,
            FAT_COUNT: ($scope.fatCount != undefined && $scope.fatCount != null) ? $scope.fatCount : 0,
            CARBOHYDRATE_COUNT: ($scope.carbohydrateCount != undefined && $scope.carbohydrateCount != null) ? $scope.carbohydrateCount : 0,
            FIBRE_COUNT: ($scope.fibreCount != undefined && $scope.fibreCount != null) ? $scope.fibreCount : 0
        };
        var updateRequestBody = {};
        updateRequestBody[$scope.viewProductNutritionData.id] = requestData;
        console.log(updateRequestBody);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.updateNutritionDetailBySource + "?sourceType=" + $scope.selectedSourceType,
            data: updateRequestBody
        }).then(function success(response) {
            if (response.status == 200 && response.data == true) {
                alert("Product Nutrition Data updated !!!!");
                $scope.showFullScreenLoader = false;
                $("#nutritionFactorModal").modal("hide");
                getProductNutritionData();
                return;
            }
            alert("Product Nutrition Data Cannot be updated !!!!");
            $scope.showFullScreenLoader = false;
        }, function error(response) {
            console.log("error:" + response);
            $scope.showFullScreenLoader = false;
        });
    }

        function getCount(val) {
            if (val == undefined || val == null) {
                return null;
            }
            return parseFloat(val);
        }
});