/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("taxMappingsCtrl", function($rootScope, $scope, $location, $http, AppUtil, $cookieStore) {

    $scope.init = function() {
	$scope.getCountryList();
	$scope.getAllTaxCategoriesBasicDetail();
	$scope.getAllTaxTypes();
	$scope.hideStateTaxGrid = true;
	$scope.searchedOnce = false;
    };

    $scope.getCountryList = function() {
	$http({
	    method : "GET",
	    dataType : 'json',
	    data : '',
	    headers : {
		"Content-Type" : "application/json"
	    },
	    url : AppUtil.restUrls.taxManagement.getAllCountries,
	}).then(function success(response) {
	    $scope.countryList = response.data;
	}, function error(response) {
	    console.log("error:" + response);
	});
    }

    $scope.getAllTaxCategoriesBasicDetail = function() {
	$http({
	    method : "GET",
	    dataType : 'json',
	    data : '',
	    headers : {
		"Content-Type" : "application/json"
	    },
	    url : AppUtil.restUrls.taxManagement.getAllTaxCategoriesBasicInfo,
	}).then(function success(response) {
	    $scope.taxCategories = response.data;
	}, function error(response) {
	    console.log("error:" + response);
	});
    }

    $scope.getAllTaxTypes = function() {
	$http({
	    method : "GET",
	    dataType : 'json',
	    data : '',
	    headers : {
		"Content-Type" : "application/json"
	    },
	    url : AppUtil.restUrls.taxManagement.getAllTaxTypesBasicInfo,
	}).then(function success(response) {
	    $scope.taxTypes = response.data;
	}, function error(response) {
	    console.log("error:" + response);
	});
    }

    $scope.getStateWiseTaxData = function() {

	if ($scope.searchedOnce
		&& !(AppUtil.isValid($scope.selectCountry) && AppUtil.isValid($scope.selectTaxCategoy) && AppUtil
			.isValid($scope.selectTaxType))) {
	    bootbox.alert("Please select all values.");
	    return;
	}
	if (!$scope.searchedOnce) {
	    $scope.searchedOnce = true;
	    return;
	}
	$rootScope.showFullScreenLoader = true;
	var urlLink = null;
	if ($scope.selectTaxType.code == "GST") {
	    urlLink = AppUtil.restUrls.taxManagement.fetchGSTCategoryTax;
	    $scope.stateTaxGridOptions = $scope.createGSTStateTaxGridOptions();
	} else if ($scope.selectTaxType.code != "GST") {
	    urlLink = AppUtil.restUrls.taxManagement.fetchCategoryAdditionalTax;
	    $scope.stateTaxGridOptions = $scope.createOtherStateTaxGridOptions();
	}

	$http({
	    method : "GET",
	    dataType : 'json',
	    data : '',
	    params : {
		countryId : $scope.selectCountry.id,
		categoryId : $scope.selectTaxCategoy.id,
		taxId : $scope.selectTaxType.id
	    },
	    headers : {
		"Content-Type" : "application/json"
	    },
	    url : urlLink,
	}).then(function success(response) {
	    $scope.categoryTax = response.data;
	    $scope.stateTaxGridOptions.data = $scope.categoryTax.taxes;
	    $scope.hideStateTaxGrid = false;
	    $rootScope.showFullScreenLoader = false;
	}, function error(response) {
	    console.log("error:" + response);
	    $rootScope.showFullScreenLoader = false;
	});
    }

    $scope.createGSTStateTaxGridOptions = function() {
	return {
	    enableFiltering : true,
	    enableColumnResizing : true,
	    exporterMenuCsv : true,
	    enableGridMenu : true,
	    exporterPdfOrientation : 'portrait',
	    exporterPdfMaxGridWidth : 450,
	    cellEditableCondition : function($scope) {
		return true;
	    },
	    columnDefs : [ {
		field : 'state.name',
		displayName : 'State',
		enableCellEdit : false,
	    }, {
		field : 'igst',
		displayName : 'IGST',
		enableCellEdit : true,
		type : 'number',
		menuItems : [ {
		    title : 'Edit All',
		    icon : 'ui-grid-icon-info-circled',
		    action : function($event) {
			$scope.openEditTaxValueModal("IGST");
		    }
		} ]
	    }, {
		field : 'cgst',
		displayName : 'CGST',
		enableCellEdit : true,
		type : 'number',
		menuItems : [ {
		    title : 'Edit All',
		    icon : 'ui-grid-icon-info-circled',
		    action : function($event) {
			$scope.openEditTaxValueModal("CGST");
		    }
		} ]
	    }, {
		field : 'sgst',
		displayName : 'SGST/UGST',
		enableCellEdit : true,
		type : 'number',
		menuItems : [ {
		    title : 'Edit All',
		    icon : 'ui-grid-icon-info-circled',
		    action : function($event) {
			$scope.openEditTaxValueModal("SGST");
		    }
		} ]
	    } ],
	    onRegisterApi : function(gridApi) {
		$scope.gridApi = gridApi;
		gridApi.edit.on.afterCellEdit($scope, function(rowEntity, colDef, newValue, oldValue) {
		    if (newValue > 100 || newValue < 0) {
			$scope.wrongDataAlert();
			rowEntity[colDef.name] = oldValue;
		    }
		    $scope.$apply();
		});
	    }
	};
    }

    $scope.createOtherStateTaxGridOptions = function() {
	return {
	    enableFiltering : true,
	    enableColumnResizing : true,
	    exporterMenuCsv : true,
	    enableGridMenu : true,
	    exporterPdfOrientation : 'portrait',
	    exporterPdfMaxGridWidth : 450,
	    cellEditableCondition : function($scope) {
		return true;
	    },
	    columnDefs : [ {
		field : 'state.name',
		displayName : 'State',
		enableCellEdit : false,
	    }, {
		field : 'tax',
		displayName : 'Tax Rate',
		enableCellEdit : true,
		type : 'number',
		menuItems : [ {
		    title : 'Edit All',
		    icon : 'ui-grid-icon-info-circled',
		    action : function($event) {
			$scope.openEditTaxValueModal("OTHER");
		    }
		} ]
	    } ],
	    onRegisterApi : function(gridApi) {
		$scope.gridApi = gridApi;
		gridApi.edit.on.afterCellEdit($scope, function(rowEntity, colDef, newValue, oldValue) {
		    if (newValue > 100 || newValue < 0) {
			$scope.wrongDataAlert();
			rowEntity[colDef.name] = oldValue;
		    } else {
			$scope.$apply();
		    }
		});
	    }
	};
    }

    $scope.wrongDataAlert = function() {
	bootbox.alert("Tax values are incorrect, Please update correct tax values.");
    };

    $scope.openEditTaxValueModal = function(type) {
	$scope.editTaxType = type;
	$("#editTaxValueModal").modal("show");
    }

    $scope.updateTaxValue = function() {
	if (!AppUtil.isValid($scope.editTaxType) || $scope.newTaxValue == null || $scope.newTaxValue < 0) {
	    return;
	}
	$scope.updateAllTaxGridRow($scope.editTaxType, $scope.newTaxValue);
	$scope.editTaxType = null;
	$scope.newTaxValue = null;
	$("#editTaxValueModal").modal("hide");
    }

    $scope.updateAllTaxGridRow = function(type, value) {
	var x = null;
	for (x in $scope.stateTaxGridOptions.data) {
	    if (type === 'IGST') {
		$scope.stateTaxGridOptions.data[x].igst = value;
	    } else if (type === 'CGST') {
		$scope.stateTaxGridOptions.data[x].cgst = value;
	    } else if (type === 'SGST') {
		$scope.stateTaxGridOptions.data[x].sgst = value;
	    } else if (type === 'OTHER') {
		$scope.stateTaxGridOptions.data[x].tax = value;
	    }
	}
    }

    $scope.resetStateWiseTaxData = function() {
	$scope.getStateWiseTaxData();
    }

    $scope.updateStateWiseTaxData = function() {

	if (!$scope.validateTaxdata()) {
	    bootbox.alert("Tax values missing, Please update tax for all states.");
	    return false;
	}

	var urlLink = null;
	if ($scope.selectTaxType.code == "GST") {
	    urlLink = AppUtil.restUrls.taxManagement.updateCategoryTax;
	} else if ($scope.selectTaxType.code == "CESS1") {
	    urlLink = AppUtil.restUrls.taxManagement.updateCategoryAdditionalTax;
	}

	var payload = $scope.categoryTax;
	payload.taxes = $scope.stateTaxGridOptions.data;
	var user = AppUtil.getCurrentUser();
	payload.employeeId = user.id;
	payload.employeeName = user.name;
	$http({
	    url : urlLink,
	    method : 'POST',
	    data : payload
	}).then(function(response) {
	    if (response.data != undefined && response.data != null) {
		bootbox.alert("Tax values Updated Successfully!");
		$scope.categoryTax = response.data;
		$scope.stateTaxGridOptions.data = $scope.categoryTax.taxes;
	    }
	}, function(response) {
	    console.log("error", response);
	    bootbox.alert("Tax value Update Failed!");
	});
    }

    $scope.validateTaxdata = function() {
	if ($scope.stateTaxGridOptions.data.length == 0) {
	    return false;
	}
	var x = null;
	for (x in $scope.stateTaxGridOptions.data) {
	    if ($scope.selectTaxType.code == "GST") {
		if (!(AppUtil.isValid($scope.stateTaxGridOptions.data[x].igst)
			&& AppUtil.isValid($scope.stateTaxGridOptions.data[x].cgst) && AppUtil
			.isValid($scope.stateTaxGridOptions.data[x].sgst))) {
		    return false;
		}
	    }
	    if ($scope.selectTaxType.code == "CESS1") {
		if (!(AppUtil.isValid($scope.stateTaxGridOptions.data[x].tax))) {
		    return false;
		}
	    }
	}
	return true;
    }
});