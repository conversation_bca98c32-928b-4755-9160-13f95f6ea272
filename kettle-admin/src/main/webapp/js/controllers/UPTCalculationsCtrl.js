/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("UPTCalculationsCtrl", function($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location,
                                                     $timeout, AppUtil) {

    $scope.init = function() {
        $scope.units= {};
        $scope.selectedpartnerID = null;
        $scope.dayCloseDate=$scope.dateformatting(AppUtil.getDate())
        getPartnerIDs("CAFE");
    };
    $scope.dateformatting = function (startDate) {
        var year = new Date(startDate).getFullYear();
        var month = new Date(startDate).getMonth() + 1;
        var day = new Date(startDate).getDate();
        if (day >= 1 && day < 10)
            day = '0' + day;
        if (month >= 1 && month < 10)
            month = '0' + month;
        return year + "-" + month + "-" + day;
    };
    function getPartnerIDs(category) {
        $http({
            method : 'GET',
            url : AppUtil.restUrls.unitMetaData.allUnits,
            params : {
                "category" : category
            }
        }).then(function success(response) {
            $scope.units = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    $scope.calculateUPT=function (){
        if(new Date($scope.dayCloseDate) > new Date()){
            alert('Check Date');
            return ;
        }
        $rootScope.showDetailLoader = true;
        $http({
            method : 'POST',
            url : AppUtil.restUrls.referenceOrderManagement.UPTCalc,
            params : {
                "date" : $scope.dayCloseDate,
                "unitId" : $scope.selectedpartnerID
            }
        }).then(function success(response) {
            $rootScope.showDetailLoader = false;
            $scope.unitData = response.data;
            alert('UPT calculated Successfully');
        }, function error(response) {
            $rootScope.showDetailLoader = false;
            console.log("error:" + response);

        });
    }
});
