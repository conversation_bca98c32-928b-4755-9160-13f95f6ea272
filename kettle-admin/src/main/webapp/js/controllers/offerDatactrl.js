/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        "OfferDataController",
        function ($scope, $rootScope, $http, $timeout, $window, $location, AppUtil,
                    $uibModal, AdminApiService, DateTimeUtil, toastService) {
            $scope.ExistingfullCompleteCouponsObj = [];
            $rootScope.showFullScreenLoader = true;
            $scope.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.offerStatus = "";
                $scope.updateMaxUsage = "";
                $scope.singleCouponsDetails = [];
                $scope.coup = [];
                $scope.coupMap = [];
                $scope.chkboxFamily = {
                    value: false
                };
                $scope.offerId=0;
                $scope.offerDescriptionMetadata = [];
                $scope.offerDescriptionMetadata.push('');
                $scope.selectedBrandId = null;
            };

            $scope.addOfferNew = function () {
                var modalInstance = $uibModal.open({
                    animation: true,
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'views/offerModal.html',
                    backdrop: 'static',
                    controller: 'offerModalCtrl',
                    controllerAs: 'offer',
                    size: 'lg',
                    resolve: {
                        editOffer: function () {
                            return null;
                        },
                        brands: function() {
                            return $scope.brands;
                        },
                        listTypes: function() {
                            return $scope.categoryLists;
                        },
                        cafelist: function() {
                            return $scope.cafelist;
                        }
                    }
                });

                modalInstance.result.then(function (text) {
                    if (text !== null) {
                        $scope.search = text;
                        $scope.searchLike();
                    }
                }, function () {
                    //alert("Dismissal");
                });
            };

            $scope.offerDetail = {
                category: null,
                type: null,
                text: null,
                description: null,
                startDate: null,
                endDate: null,
                minValue: null,
                status: null,
                validateCustomer: null,
                removeLoyalty: null,
                minQuantity: null,
                minLoyalty: null,
                offerValue: null,
                minItemCount: null,
                offerScope: null,
                otpRequired: null,
                partners: [],
                metaDataMappings: [],
                loyaltyBurnPoints : null
            };

            $scope.searchOffers = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: AppUtil.restUrls.offerManagement.offers,
                    method: "POST",
                    dataType: 'json',
                    data: '',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                }).success(function (data) {
                    $scope.offers = data
                    for(var i=0;i<data.length;i++){
                       if($scope.offers[i].type === "FREEBIE_STRATEGY_PERCENTAGE" || $scope.offers[i].type === "FREEBIE_STRATEGY_FLAT"){
                           $scope.offers[i].type = "FREEBIE_STRATEGY";
                       }
                    }
                    $rootScope.showFullScreenLoader = false;
                    $scope.currentPage = 1; // current page
                    $scope.numPerPage = 50;
                    $scope.entryLimit = 50; // max no of items to display
                    // in// a page
                    $scope.filteredItems = $scope.offers.length; // Initially
                    $scope.totalItemsOffers = $scope.offers.length;
                    $scope.colsRequired=($scope.filteredItems/$scope.entryLimit) + 1;
                });

            };

            $scope.clearForm = function () {
                $scope.offerAccountCategory = null;
                $scope.offerCategory = null;
                $scope.offerType = null;
                $scope.offerStatus = null;
                $scope.validateCustomer = null;
                $scope.removeLoyalty = null;
                $scope.offerScope = null;
                $scope.otpRequired = null;
                $scope.fullSelectedObjCategory = [];
                $scope.fullSelectedObjPartner = [];
                $scope.partObj = [];
                $scope.selectedSubCategoriesLength = 0;
                $scope.selectedCategoriesLength = 0;
                $scope.selectedProductsLength = 0;
                $scope.selectedUnits = null;
                $scope.selectedRegions = null;
                $scope.fullSelectedObjOrderSource = null;
                $scope.fullSelectedObjChannelPartner = null;
                $scope.fullSelectedObjPaymentMode = null;
                $scope.fullSelectedObjNewCustomer = null;
                $("#UnitsTab").hide();
                $("#couponsMapp").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $("#newCustomerTab").hide();
                $("#ReplicateCouponsTab").hide();
            };

            $scope.paginate = function (value) {
                var begin,
                    end,
                    index;
                begin = ($scope.currentPage - 1) * $scope.numPerPage;
                end = begin + $scope.numPerPage;
                index = $scope.offers.indexOf(value);

                $scope.colsRequired=($scope.filteredItems/$scope.entryLimit) + 1;
                return (begin <= index && index < end);
            };

            $scope.updateMaxDiscount = function (discount) {
                $scope.maxDiscountAmount = discount;
            };

            $http({
                method: 'GET',
                url: AppUtil.restUrls.offerManagement.offerAccountsCategories
            }).then(function success(response) {
                $scope.offerAccountsCategories = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                url: AppUtil.restUrls.channelPartner.getActiveChannelPartners,
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).success(function (data) {
                $scope.channelPartner = data;
                console.log("CHP=", $scope.channelPartner);
                $scope.channelDetailList = [];
                $scope.channelPartner.forEach(function (channelDetails) {
                    $scope.channelDetailList.push({
                        'name': channelDetails.name,
                        'ticked': false,
                        id: channelDetails.id.toString()
                    })
                });
                console.log("Channel=", $scope.channelDetailList)
            });

            $http({
                url: AppUtil.restUrls.offerManagement.offerCategory,
                method: "POST",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                },
            }).success(function (data) {
                $scope.offerCategoryTypeMap = data;
            });

            $http({
                url: AppUtil.restUrls.offerManagement.paymentModes,
                method: "POST",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                },
            }).success(function (data) {
                $scope.paymentMode = data
                $scope.paymentDetailList = [];
                $scope.paymentMode.forEach(function (paymentDetails) {
                    $scope.paymentDetailList.push({
                        'name': paymentDetails.name,
                        'ticked': false,
                        id: paymentDetails.id.toString()
                    })
                });
                console.log("Channel=", $scope.paymentDetailList)

                console.log("PAYMENT Mode=", $scope.paymentMode);
            });

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.regions
            }).then(function success(response) {
                $scope.regions = response.data;
                console.log("Regions=", $scope.regions);
                $scope.newUnitRegion = $scope.regions[0];
                $scope.regionDetailList = [];
                $scope.regions.forEach(function (regionDetails) {
                    $scope.regionDetailList.push({
                        'name': regionDetails,
                        'ticked': false
                    })
                });
                console.log("checkRV=", $scope.regionDetailList);
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                method: "GET",
                url: AppUtil.restUrls.brandMetaData.getAllBrands 
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    console.log("Brands Fetched Successfully! ::: ");
                    $scope.brands = response.data;
                    console.log($scope.brands);
                } else {
                    console.log("Unable to Fetch Brands");
                }
            });

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.families
            }).then(function success(response) {
                $scope.families = response.data;
                console.log("family=", $scope.families);
                $scope.newUnitFamily = $scope.families[0];
                $scope.familyDetailList = [];
                $scope.families.forEach(function (familyDetails) {
                    $scope.familyDetailList.push({
                        'name': familyDetails,
                        'ticked': false
                    })
                });
                console.log("familyies=", $scope.familyDetailList);
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnits + '?category=CAFE'
            }).then(function success(response) {
                $scope.cafelist = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.marketingPartner,
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                }
            }).then(function success(response) {
                $scope.partnerDetails = response;
                $scope.currentPage = 1; // current page
                $scope.entryLimit = 50; // max no of
                $scope.partnerDetailsList = response.data;
                $scope.filteredItems = $scope.partnerDetailsList.length; // Initially
                $scope.totalItems = $scope.partnerDetailsList.length;
            }, function error(response) {
                console.log("error:" + response);
            });
            $rootScope.showFullScreenLoader = false;

            $scope.searchLike = function () {

                $rootScope.showFullScreenLoader = true;
                $http({
                    url: AppUtil.restUrls.offerManagement.offers + "/search",
                    method: "POST",
                    dataType: 'json',
                    data: $scope.search,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                }).success(function (data) {
                    $scope.offers = data
                    for(var i=0;i<data.length;i++){
                        if($scope.offers[i].type === "FREEBIE_STRATEGY_PERCENTAGE" || $scope.offers[i].type === "FREEBIE_STRATEGY_FLAT"){
                            $scope.offers[i].type = "FREEBIE_STRATEGY";
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                    $scope.currentPage = 1; // current page
                    $scope.numPerPage = 50;
                    $scope.entryLimit = 50; // max no of items to display
                    // in// a page
                    $scope.filteredItems = $scope.offers.length; // Initially
                    $scope.totalItemsOffers = $scope.offers.length;
                    $scope.colsRequired=($scope.filteredItems/$scope.entryLimit) + 1;

                });

            };
            $scope.offerCat = [{
                code: "BILL",
                name: "BILL"
            }, {
                code: "ITEM",
                name: "ITEM"
            }];
            $scope.basicInfoStatus = [{
                code: 'ACTIVE',
                name: 'ACTIVE'
            }, {
                code: 'IN_ACTIVE',
                name: 'IN_ACTIVE'
            }];
            $scope.basicCouponStatus = [{
                code: 'ACTIVE',
                name: 'ACTIVE'
            }, {
                code: 'IN_ACTIVE',
                name: 'IN_ACTIVE'
            }];
            $scope.basicReusableCoupons = [{
                code: 'true',
                name: 'Yes'
            }, {
                code: 'false',
                name: 'No'
            }];
            $scope.basicReusableCustomer = [{
                code: 'true',
                name: 'Yes'
            }, {
                code: 'false',
                name: 'No'
            }];
            $scope.validateCustomerDetails = [{
                code: 'true',
                name: 'Yes'
            }, {
                code: 'false',
                name: 'No'
            }];

            $scope.removeLoyaltyDetails = [{
                code: 'false',
                name: 'No'
            }, {
                code: 'true',
                name: 'Yes'
            }];
            $scope.reusableStatus = [{
                code: 'true',
                name: 'true'
            }, {
                code: 'false',
                name: 'false'
            }];
            $scope.reusableCustomerStatus = [{
                code: 'true',
                name: 'Yes'
            }, {
                code: 'false',
                name: 'No'
            }];
            $scope.manualOverrideStatus = [{
                code: 'true',
                name: 'Yes'
            }, {
                code: 'false',
                name: 'No'
            }];
            $scope.offerScopeDetails = [{
                code: "INTERNAL",
                name: "INTERNAL"
            }, {
                code: "EXTERNAL",
                name: "EXTERNAL"
            }, {
                code: "MASS",
                name: "MASS"
            }, {
                code: "CUSTOMER",
                name: "CUSTOMER"
            }, {
                code: "CORPORATE",
                name: "CORPORATE"
            }];

            $scope.newCustomerArrayData = [{
                code: 'Y',
                name: 'YES'
            }, {
                code: 'N',
                name: 'NO'
            }];

            $scope.newCustomerArray = {
                value1: false
            };

            $scope.selectOfferStatus = $scope.basicInfoStatus[0];
            $scope.reusableStatusData = $scope.reusableStatus[0];
            $scope.reusableCustomerStatusData = $scope.reusableCustomerStatus[0];
            $scope.manualOverridingData = $scope.manualOverrideStatus[0];

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.listTypes
            }).then(function success(response) {
                $scope.categoryLists = response.data;
                $scope.productCategory = $scope.categoryLists.CATEGORY;
                $scope.productDimension = $scope.categoryLists.DIMENSION;
                $scope.allDimension = [];
                $scope.uniqueDimension = [];
                if ($scope.productDimension != undefined) {
                    for (var i = 0; i < $scope.productDimension.length; i++) {
                        var dimsLen = $scope.allDimension.push($scope.productDimension[i].content.length);
                        for (var k = 0; k < dimsLen; k++) {
                            if ($scope.productDimension[i].content[k] != undefined) {
                                $scope.uniqueDimension.push($scope.productDimension[i].content[k].name);
                            }
                        }
                        $scope.uniqueDimensionNames = [];
                        $.each($scope.uniqueDimension, function (i, el) {
                            if ($.inArray(el, $scope.uniqueDimensionNames) === -1)
                                $scope.uniqueDimensionNames.push(el);
                        });
                    }
                }
                $scope.uniqueDimensionNames = $scope.uniqueDimensionNames.map(function (e) {
                    return {
                        name: e,
                        checked: false
                    };
                });

                $scope.finalSubtype = [];

                for (var index in $scope.productCategory) {
                    if ($scope.productCategory[index].content[0] != undefined) {
                        var len = $scope.productCategory[index].content.length;
                        for (var i = 0; i < len; i++) {
                            $scope.productListType = $scope.productCategory[index].detail.id;
                            $scope.productListName = $scope.productCategory[index].content[i].name;
                            $scope.productListID = $scope.productCategory[index].content[i].id;
                            $scope.finalSubtype.push({
                                id: $scope.productListID,
                                name: $scope.productListName,
                                type: $scope.productListType
                            });
                        }
                    }
                    // console.log("Final Subtypes: " + JSON.stringify($scope.finalSubtype));
                }
            });

            $scope.getOfferDescriptionMetadata = function(offerId){
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.getOfferDescriptionMetadataByOfferId + "?getAll=false&offerId=" + offerId
                }).then(function success(response) {
                    if(response.data != undefined && response.data != null && response.data[offerId] != null){
                        $scope.offerDescriptionMetadata = response.data[offerId];
                        $scope.descriptionDataAvailable = true;
                    }
                    else{
                        $scope.descriptionDataAvailable = false;
                        $scope.offerDescriptionMetadata[0] = [{
                            descriptionType : null,
                            descriptionValue : null,
                            color : null,
                            fontSize : null,
                        }]
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.checkedAllProducts = function (checkVal2) {
                $scope.prodObj = [];
                $scope.fullProdObj = [];
                if (checkVal2 == 'YES') {
                    $("#fullSelectedObjCategoryDiv").hide();
                    $("#fullSelectedObjSubCategoryDiv").hide();
                    $("#fullSelectedObjProductDiv").hide();
                    $("#productSubCategoryDIV").hide();
                    $("#productDIV").show();
                    $("#prodSubCatDiv").hide();
                    $("#prodSubCatAllDiv").hide();
                    $("#productSubCategoryDIV").hide();
                    $("#btnSubmitProductCat").hide();
                    $("#btnSubmitSubProductCat").hide();
                    $("#btnSubmitProduct").show();
                    $("#btnAllDiv").hide();
                    $("#prodDiv").show();
                    $("#prodCatDiv").hide();
                    $scope.productDetailsList.forEach(function (prodList) {
                        $scope.fullProdObj.push({
                            id: prodList.id,
                            check: $scope.prodObj[prodList.id],
                            productName: prodList.name,
                            type: prodList.type
                        });
                    });
                } else {
                    $("#productDIV").hide();
                    $("#productSubCategoryDIV").hide();
                    $("#prodSubCatAllDiv").show();
                    $("#prodSubCatDiv").show();
                    $("#btnSubmitSubProductCat").hide();
                    $("#prodAllDiv").show();
                    $("#btnSubmitProduct").hide();
                    $("#btnAllDiv").show();
                    $("#prodDiv").show();
                    $("#prodAllDiv").show();
                    $("#prodCatDiv").show();
                }
            };

            $scope.showOfferCatDet = function (values) {
                $scope.displayOnlyVal = "";
                if (values == "ITEM") {
                    $scope.offerCategoryLists = $scope.offerCategoryTypeMap["ITEM"];
                    $scope.displayOnlyVal = "Percentage";
                } else if (values == "BILL") {
                    $scope.offerCategoryLists = $scope.offerCategoryTypeMap["BILL"];
                    $scope.displayOnlyVal = "Flat";
                } else {
                    $scope.offerCategoryLists = "";
                    $scope.displayOnlyVal = "";
                }
            };

            $scope.setOfferValueHeading = function (value) {
                if (value === "PERCENTAGE_BILL_STRATEGY" || value === "PERCENTAGE_BILL_MAX_CAP_STRATEGY" || value === "PERCENTAGE_ITEM_STRATEGY") {
                    $scope.displayOnlyVal = "Percentage";
                }
            };

            $scope.updateAccountsCategory = function (value) {
                $scope.offerAccountCategory = value;
            };

            $scope.checkedAllProductSubCategory = function (checkVal1) {
                $scope.subCatObj = [];
                $scope.fullSubCatObj = [];
                if (checkVal1 == 'YES') {
                    $("#productSubCategoryDIV").show();
                    $("#btnSubmitProductCat").hide();
                    $("#fullSelectedObjSubCategoryDiv").hide();
                    $("#btnSubmitSubProductCat").show();
                    $("#btnSubmitProduct").hide();
                    $("#btnAllDiv").hide();
                    $("#prodDiv").hide();
                    $("#prodAllDiv").hide();
                    $("#prodCatDiv").hide();
                    $("#fullSelectedObjCategoryDiv").hide();
                    $("#fullSelectedObjProductDiv").hide();
                    $scope.finalSubtype.forEach(function (subType) {
                        $scope.fullSubCatObj.push({
                            id: subType.id,
                            check: $scope.subCatObj[subType.id],
                            subCategoryName: subType.name
                        });
                    });
                } else {
                    $("#productSubCategoryDIV").hide();
                    $("#btnSubmitSubProductCat").hide();
                    $("#btnAllDiv").show();
                    $("#prodDiv").show();
                    $("#prodAllDiv").show();
                    $("#prodCatDiv").show();
                }
            };

            $scope.checkedAllProductCategory = function (checkVal) {
                $scope.sel = [];
                $scope.fullObj = [];
                if (checkVal == 'YES') {
                    $("#btnSubmitProductCat").show();
                    $("#productCategoryDIV").show();
                    $scope.productCategory.forEach(function (product) {
                        $scope.fullObj.push({
                            id: product.detail.id,
                            check: $scope.sel[product.detail.id],
                            categoryName: product.detail.name
                        });
                    });
                } else {
                    $("#fullSelectedObjCategoryDiv").show();
                    $("#fullSelectedObjProductDiv").show();
                    $("#fullSelectedObjSubCategoryDiv").show();
                    $("#prodSubCatAllDiv").show();
                    $("#prodSubCatDiv").show();
                    $("#prodDiv").show();
                    $("#btnSubmitProductCat").hide();
                    $("#btnAllDiv").show();
                    $("#prodAllDiv").show();
                    $("#productCategoryDIV").hide();
                }
            };

            // $http({
            //     url: AppUtil.restUrls.productMetaData.products,
            //     dataType: 'json',
            //     method: 'POST',
            //     data: '',
            //     headers: {
            //         "Content-Type": "application/json"
            //     }
            // }).then(function success(response) {
            //     $scope.productDetails = response;
            //     $scope.currentPage = 1;
            //     $scope.entryLimit = 50;
            //     $scope.productDetailsList = response.data
            //     $scope.filteredItems = $scope.productDetailsList.length;
            //     $scope.totalItems = $scope.productDetailsList.length;
            // }, function error(response) {
            //     console.log("error:" + response);
            // });
            $scope.fullObj = [];
            $scope.checkCategoryDetails = function (ids, chk, catName) {
                if ($scope.fullSelectedObjSubCategory != undefined) {
                    $scope.productContent = [];
                    $scope.productCategory
                        .forEach(function (prdDetail) {
                            if (prdDetail.detail.id == ids) {
                                for (var j = 0; j < $scope.fullSelectedObjSubCategory.length; j++) {
                                    console.log($scope.fullSelectedObjSubCategory[j].id);
                                    for (var i = 0; i < prdDetail.content.length; i++) {
                                        if ($scope.fullSelectedObjSubCategory[j].id == prdDetail.content[i].id) {
                                            if ($window
                                                .confirm("Are you sure, you want to Add Category While relevant Subcategory already added")) {
                                                var catLists = [];
                                                $scope.fullSelectedObjSubCategory.forEach(function (v) {
                                                    if (v.id != prdDetail.content[i].id) {
                                                        catLists.push(v);
                                                    }
                                                });
                                                $scope.fullSelectedObjSubCategory = catLists;
                                            } else {
                                                $scope.sel[ids] = false;
                                            }
                                            return false;
                                        } else {
                                            console.log("not available");
                                        }
                                    }
                                }
                            }
                            ;
                        });
                } else if ($scope.fullSelectedObjProduct != undefined) {
                    for (var j = 0; j < $scope.fullSelectedObjProduct.length; j++) {
                        console.log($scope.fullSelectedObjProduct[j]);
                        if ($scope.fullSelectedObjProduct[j].type == ids) {
                            if ($window
                                .confirm("Are you sure, you want to Add Category While relevant Product already added")) {
                                var catListt = [];
                                $scope.fullSelectedObjProduct.forEach(function (cprd) {
                                    if (cprd.type != ids) {
                                        catListt.push(cprd);
                                    }
                                });
                                $scope.fullSelectedObjProduct = catListt;
                            } else {
                                $scope.sel[ids] = false;
                            }
                            return false;
                        } else {
                            console.log("Not Available");
                        }
                    }
                }
            };
            $scope.fullSubCatObj = [];
            $scope.catList = [];
            $scope.checkSubCategoryDetails = function (id, chk, catName, type) {
                if ($scope.fullSelectedObjCategory != undefined) {
                    for (var j = 0; j < $scope.fullSelectedObjCategory.length; j++) {
                        if ($scope.fullSelectedObjCategory[j].id == type) {
                            if ($window
                                .confirm("Are you sure, you want to Add sub category While relevant Category already added")) {

                                $scope.fullSelectedObjCategory.forEach(function (v) {
                                    if (v.id != type) {
                                        catList.push(v);
                                    }
                                });
                                $scope.fullSelectedObjCategory = catList;
                            } else {
                                $scope.result = 'No';
                                $scope.subCatObj[id] = false;
                            }
                            return false;
                        } else {
                            console.log("not available");
                        }
                    }
                } else if ($scope.fullSelectedObjProduct != undefined) {
                    for (var j = 0; j < $scope.fullSelectedObjProduct.length; j++) {
                        if ($scope.fullSelectedObjProduct[j].subtype == id) {
                            if ($window
                                .confirm("Are you sure, you want to Add sub category While relevant Product already added")) {
                                var remPrdList = [];
                                $scope.fullSelectedObjProduct.forEach(function (rprd) {
                                    if (rprd.subtype != id) {
                                        remPrdList.push(rprd);
                                    }
                                });
                                $scope.fullSelectedObjProduct = remPrdList;
                            } else {
                                $scope.subCatObj[id] = false;
                            }
                            return false;
                        } else {
                            console.log("not available");
                        }
                    }
                }
            }
            $scope.fullProdObj = [];
            $scope.checkProductDetails = function (id, chk, catName, type, subType) {
                if ($scope.fullSelectedObjCategory != undefined) {
                    for (var j = 0; j < $scope.fullSelectedObjCategory.length; j++) {
                        if ($scope.fullSelectedObjCategory[j].id == type) {
                            if ($window
                                .confirm("Are you sure, you want to Add Product While relevant Category already added")) {
                                var remCatList = [];
                                $scope.fullSelectedObjCategory.forEach(function (rcat) {
                                    if (rcat.id != type) {
                                        remCatList.push(rcat);
                                    }
                                });
                                $scope.fullSelectedObjCategory = remCatList;
                            } else {
                                $scope.prodObj[id] = false;
                            }
                            return false;
                        } else {
                            console.log("Not Available");
                        }
                    }
                } else if ($scope.fullSelectedObjSubCategory != undefined) {
                    for (var p = 0; p < $scope.fullSelectedObjSubCategory.length; p++) {
                        if ($scope.fullSelectedObjSubCategory[p].id == subType) {
                            if ($window
                                .confirm("Are you sure, you want to Add Product While relevant Subcategory already added")) {
                                var remSubCat = [];
                                $scope.fullSelectedObjSubCategory.forEach(function (rSc) {
                                    if (rSc.id != subType) {
                                        remSubCat.push(rSc);
                                    }
                                });
                                $scope.fullSelectedObjSubCategory = remSubCat;
                            } else {
                                $scope.prodObj[id] = false;
                            }
                            return false;
                        } else {
                            console.log("not available");
                        }
                    }
                }
                ;
            }
            $scope.selectTab = function (tabIndex) {
                switch (tabIndex) {
                    case 'tab1' :
                        $scope.tab1 = false;
                        $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = true;
                        break;
                    case 'tab2' :
                        $scope.tab2 = false;
                        $scope.tab1 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = true;
                        break;
                    case 'tab3' :
                        $scope.tab3 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = true;
                        break;
                    case 'tab4' :
                        $scope.tab4 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab5 = $scope.tab6 = $scope.tab7 = true;
                        break;
                    case 'tab5' :
                        $scope.tab5 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = true;
                        break;

                    case 'tab6' :
                        $scope.tab6 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab7 = true;
                        break;

                    case 'tab7' :
                        $scope.tab7 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = true;
                        break;

                    case 'tab20' :
                        $scope.tab20 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab21 = $scope.tab22 = $scope.tab23 = $scope.tab24 = $scope.tab25 = $scope.tab26 = true;
                        break;

                    case 'tab21' :
                        $scope.tab21 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab20 = $scope.tab22 = $scope.tab23 = $scope.tab24 = $scope.tab25 = $scope.tab26 = true;
                        break;

                    case 'tab22' :
                        $scope.tab22 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab21 = $scope.tab20 = $scope.tab23 = $scope.tab24 = $scope.tab25 = $scope.tab26 = true;
                        break;

                    case 'tab23' :
                        $scope.tab23 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab21 = $scope.tab22 = $scope.tab20 = $scope.tab24 = $scope.tab25 = $scope.tab26 = true;
                        break;

                    case 'tab24' :
                        $scope.tab24 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab21 = $scope.tab22 = $scope.tab23 = $scope.tab20 = $scope.tab25 = $scope.tab26 = true;
                        break;

                    case 'tab25' :
                        $scope.tab25 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab21 = $scope.tab22 = $scope.tab23 = $scope.tab24 = $scope.tab20 = $scope.tab26 = true;
                        break;

                    case 'tab26' :
                        $scope.tab26 = false;
                        $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab21 = $scope.tab22 = $scope.tab23 = $scope.tab24 = $scope.tab20 = $scope.tab25 = true;
                        break;

                }
            }
            $("#myBtn2").click(function () {
                $("#offerModal").modal({
                    backdrop: false
                });
            });

            $scope.addOffer = function () {
                $scope.offerForEdit = null;
                $scope.action = "Activate";
                $scope.actionOffer = "add";
                $scope.activationUnits = [];
                $scope.selectTab('tab1');
                $scope.allCafesCheck = false;
                $scope.allCODsCheck = false;
                $scope.allCallCentersCheck = false;
                $scope.addedCafes = [];
                $scope.addedCODs = [];
                $scope.addedCallCenters = [];
                $scope.offerDescription = "";
                $scope.offerText = "";
                $scope.startDate = "";
                $scope.endDate = "";
                $scope.minValue = null;
                $scope.minItemCount = null;
                $scope.emailDomain = null;
                $scope.quantityLimit = null;
                $scope.offerValue = null;
                $scope.offerAccountCategory = null;
                $scope.otpRequired = null;
                $("#offerModal").modal("show");
            }

            $scope.offerCompleteObj = [];
            $scope.addOfferDetails = function () {
                // $scope.selectTab('tab4');

                if ($scope.offerAccountCategory == "null" || $scope.offerAccountCategory == null) {
                    $("#offerMessageModal").modal('show');
                    alert("Please select Offer Account Category");
                    return false;
                }
                if ($scope.offerCategory == "null" || $scope.offerCategory == null) {
                    $("#offerMessageModal").modal('show');
                    alert("Please select offer");
                    return false;
                }
                if ($scope.offerType == "null" || $scope.offerType == null) {
                    alert("Please select offer type");
                    return false;
                }
                if ($scope.offerText == "" || $scope.offerText == null) {
                    alert("Please select offer Reason");
                    return false;
                }
                if ($scope.offerDescription == "null" || $scope.offerDescription == null) {
                    alert("Please input the Offer Description");
                    return false;
                }
                if ($scope.startDate == "" || $scope.startDate == undefined) {
                    alert("Please select start date");
                    return false;
                }

                if ($scope.endDate == "" || $scope.endDate == undefined) {
                    alert("Please select end date");
                    return false;
                }

                if ($scope.offerStatus == "null" || $scope.offerStatus == null) {
                    alert("Please select offer status");
                    return;
                }

                if ($scope.minValue == "null" || $scope.minValue == null) {
                    alert("Please enter minimum bill amount");
                    return false;
                }
                if ($scope.validateCustomer == "null" || $scope.validateCustomer == null) {
                    alert("Please select customer login required field");
                    return;
                }
                if ($scope.removeLoyalty == "null" || $scope.removeLoyalty == null) {
                    alert("Please select Remove loyalty required field");
                    return;
                }
                if ($scope.offerScope == "null" || $scope.offerScope == null) {
                    alert("offer scope is empty");
                    return false;
                }
                if ($scope.minItemCount == 'null' || $scope.minItemCount == null) {
                    alert("minimum Item count is empty");
                    return false;
                }
                if ($scope.offerScope != null && $scope.offerScope == 'CORPORATE' && ($scope.emailDomain == 'null' || $scope.emailDomain == null)) {
                    alert("Email Domain is mandatory for corporate offers");
                    return false;
                }
                if ($scope.quantityLimit == "null" || $scope.quantityLimit == null) {
                    alert("quantity limit is empty");
                    return false;
                }

                if ($scope.offerValue == "null" || $scope.offerValue == null) {
                    alert("offer value is empty");
                    return false;
                }

                if ($scope.offerType == "PERCENTAGE_BILL_STRATEGY"
                    || $scope.offerType == "PERCENTAGE_ITEM_STRATEGY") {
                    if ($scope.offerValue > 100) {
                        alert("offer value can not more than 100 if offer Sub type is " + $scope.offerType);
                        return false;
                    }
                }
                if ($scope.actionOffer == "add") {
                    $scope.offerCompleteObj = {
                        id: null,
                        category: $scope.offerCategory.code,
                        type: $scope.offerType,
                        text: $scope.offerText,
                        description: $scope.offerDescription,
                        startDate: $scope.startDate,
                        endDate: $scope.endDate,
                        minValue: $scope.minValue,
                        includeTaxes: true,
                        status: $scope.offerStatus.code,
                        minQuantity: $scope.quantityLimit,
                        minLoyalty: 0,
                        offerScope: $scope.offerScope.code,
                        validateCustomer: $scope.validateCustomer.code,
                        otpRequired: $scope.otpRequired.code,
                        maxDiscountAmount: $scope.maxDiscountAmount,
                        removeLoyalty: $scope.removeLoyalty.code,
                        offerValue: $scope.offerValue,
                        priority: 0,
                        minItemCount: $scope.minItemCount,
                        emailDomain: $scope.emailDomain,
                        accountsCategory: $scope.offerAccountCategory,
                        partners: [],
                        metaDataMappings: []
                    }
                } else if ($scope.actionOffer == "edit") {
                    // console.log($scope.specificOfferDetails);
                    $scope.offerCompleteObj = {
                        id: $scope.specificOfferDetails.id,
                        category: $scope.offerCategory.code,
                        type: $scope.offerType,
                        text: $scope.offerText,
                        description: $scope.offerDescription,
                        startDate: $scope.startDate,
                        endDate: $scope.endDate,
                        minValue: $scope.minValue,
                        includeTaxes: true,
                        status: $scope.offerStatus.code,
                        minQuantity: $scope.quantityLimit,
                        minLoyalty: 0,
                        offerScope: $scope.offerScope.code,
                        validateCustomer: $scope.validateCustomer.code,
                        otpRequired: $scope.otpRequired.code,
                        maxDiscountAmount: $scope.maxDiscountAmount,
                        removeLoyalty: $scope.removeLoyalty.code,
                        offerValue: $scope.offerValue,
                        priority: 0,
                        minItemCount: $scope.minItemCount,
                        emailDomain: $scope.emailDomain,
                        accountsCategory: $scope.offerAccountCategory,
                        partners: $scope.specificOfferDetails.partners,
                        metaDataMappings: $scope.specificOfferDetails.metaDataMappings
                    }
                    $scope.fullSelectedObjPartner = $scope.specificOfferDetails.partners;
                    $scope.fullSelectedObjCategory = $scope.specificOfferDetails.metaDataMappings;
                    $scope.fullSelectedObjPartner.forEach(function (partnerListChecked) {
                        if (partnerListChecked.status == 'ACTIVE') {
                            $scope.partObj[partnerListChecked.id] = true;
                        }
                    });
                    // $("#fullSelectedObjPartnerListDiv").show();
                    $("#btnAllPartnerDiv").show();

                    $scope.selectedData = [];
                    $scope.fullSelectedObjCategory.forEach(function (selectCat) {
                        if (selectCat.name == "PRODUCT_CATEGORY") {
                            for (var i = 0; i < $scope.productCategory.length; i++) {
                                if ($scope.productCategory[i].detail.id == selectCat.code) {
                                    $scope.selectedData.push({
                                        id: null,
                                        value: $scope.productCategory[i].detail.name,
                                        categoryName: $scope.productCategory[i].detail.name,
                                        name: "PRODUCT_CATEGORY",
                                        code: $scope.productCategory[i].detail.id,
                                        shortCode: null,
                                        type: $scope.productCategory[i].detail.type,
                                        status: $scope.productCategory[i].detail.status
                                    });
                                }
                            }
                        }
                        if (selectCat.name == "PRODUCT_SUB_CATEGORY") {
                            for (var i = 0; i < $scope.productCategory.length; i++) {
                                if ($scope.productCategory[i].detail.id == selectCat.code) {
                                    $scope.selectedData.push({
                                        id: null,
                                        value: $scope.productCategory[i].detail.name,
                                        categoryName: $scope.productCategory[i].detail.name,
                                        name: "PRODUCT_CATEGORY",
                                        code: $scope.productCategory[i].detail.id,
                                        shortCode: null,
                                        type: $scope.productCategory[i].detail.type,
                                        status: $scope.productCategory[i].detail.status
                                    });
                                }
                            }
                        }

                        if (selectCat.name == "PRODUCT") {
                            for (var i = 0; i < $scope.productCategory.length; i++) {
                                if ($scope.productCategory[i].detail.id == selectCat.code) {
                                    $scope.selectedData.push({
                                        id: null,
                                        value: $scope.productCategory[i].detail.name,
                                        categoryName: $scope.productCategory[i].detail.name,
                                        name: "PRODUCT_CATEGORY",
                                        code: $scope.productCategory[i].detail.id,
                                        shortCode: null,
                                        type: $scope.productCategory[i].detail.type,
                                        status: $scope.productCategory[i].detail.status
                                    });
                                }
                            }
                        }
                    })
                    $scope.fullSelectedObjCategory = $scope.selectedData;
                    $("#fullSelectedObjCategoryDiv").show();
                }
                if ($scope.offerCategory != "") {
                    if ($scope.offerCategory.code == "BILL") {
                        $scope.selectTab('tab3');
                    } else {
                        $scope.selectTab('tab2');
                        // getUnitData('category');
                        // getUnitData('subCategory');
                        // getUnitData('product');
                    }
                }
            }
            $scope.filter = function () {
                $timeout(function () {
                    $scope.filteredItems;
                }, 10);
            };
            $scope.sort_by = function (predicate) {
                $scope.predicate = predicate;
                $scope.reverse = !$scope.reverse;
            };
            $scope.updatePerm = function (val) {
                if (val === 'true') {
                    $scope.permanentAddressSame = true;
                } else {
                    $scope.permanentAddressSame = false;
                }
            }

            $scope.fullSelectedObjCategory = [];
            $scope.selectedCategoriesLength = 0;
            $scope.manageCategories = function (category, selection) {
                if (selection === true) {
                    // $scope.fullSelectedObjCategory.push(category);
                    $scope.fullSelectedObjCategory.push({
                        id: null,
                        check: selection,
                        value: category.detail.code,
                        categoryName: category.detail.name,
                        name: "PRODUCT_CATEGORY",
                        code: category.detail.id,
                        shortCode: null,
                        type: null,
                        status: "ACTIVE"
                    });
                    $scope.selectedCategoriesLength++;
                } else {
                    var indexOfCategory = $scope.fullSelectedObjCategory.indexOf(category);
                    $scope.fullSelectedObjCategory.splice(indexOfCategory, 1);
                    $scope.selectedCategoriesLength--;
                }
            };
            $scope.selectedSubCategoriesLength = 0;
            $scope.fullSelectedObjSubCategory = [];
            $scope.manageSubCategories = function (subCategory, selection) {
                if (selection === true) {
                    // $scope.fullSelectedObjCategory.push(category);
                    $scope.fullSelectedObjSubCategory.push({
                        id: subCategory.id,
                        check: selection,
                        subCategoryName: subCategory.name,
                        name: "PRODUCT_SUB_CATEGORY",
                        code: subCategory.id,
                        value: subCategory.name,
                        shortCode: null,
                        type: null,
                        status: "ACTIVE"
                    });
                    $scope.selectedSubCategoriesLength++;
                } else {
                    var indexOfCategory = $scope.fullSelectedObjCategory.indexOf(category);
                    $scope.fullSelectedObjCategory.splice(indexOfCategory, 1);
                    $scope.selectedSubCategoriesLength--;
                }
            };
            $scope.selectedProductsLength = 0;
            $scope.fullSelectedObjProduct = [];
            $scope.manageProducts = function (product, selection) {
                if (selection === true) {
                    // $scope.fullSelectedObjCategory.push(category);
                    $scope.fullSelectedObjProduct.push({
                        id: product.id,
                        check: selection,
                        productName: product.name,
                        value: product.name,
                        type: product.type,
                        subtype: product.subType,
                        name: "PRODUCT",
                        code: product.id,
                        shortCode: null,
                        status: "ACTIVE",
                        dimensionID: product.dimensionProfileId
                    });

                    $scope.selectedProductsLength++;
                } else {
                    var indexOfCategory = $scope.fullSelectedObjCategory.indexOf(category);
                    $scope.fullSelectedObjCategory.splice(indexOfCategory, 1);
                    $scope.selectedProductsLength--;
                }
            };
            $scope.selectedSubmitProductCategory = function () {
                console.log($scope.productCategory);
                $scope.fullSelectedObjCategory = [];
                var flagCatCheck = "";
                $scope.productCategory.forEach(function (prdCat) {
                    if ($scope.sel[prdCat.detail.id] == true) {
                        flagCatCheck = true;
                        $scope.fullSelectedObjCategory.push({
                            id: null,
                            check: $scope.sel[prdCat.detail.id],
                            value: prdCat.detail.code,
                            categoryName: prdCat.detail.name,
                            name: "PRODUCT_CATEGORY",
                            code: prdCat.detail.id,
                            shortCode: null,
                            type: null,
                            status: "ACTIVE"
                        });
                    }
                });
                if (flagCatCheck != true) {
                    alert("Please check any category name");
                    return false;
                }
                // $("#btnAllDiv").show();
                // $("#fullSelectedObjCategoryDiv").show();
                // $("#fullSelectedObjSubCategoryDiv").show();
                // $("#fullSelectedObjProductDiv").show();
                // $('.panel-collapse.in').collapse('hide');
            }

            $scope.selectedSubmitSubtypeCategory = function () {
                $scope.fullSelectedObjSubCategory = [];
                var flagSubCatCheck = "";
                $scope.finalSubtype.forEach(function (fSt) {
                    if ($scope.subCatObj[fSt.id] == true) {
                        flagSubCatCheck = true;
                        $scope.fullSelectedObjSubCategory.push({
                            id: fSt.id,
                            check: $scope.subCatObj[fSt.id],
                            subCategoryName: fSt.name,
                            name: "PRODUCT_SUB_CATEGORY",
                            code: fSt.id,
                            value: fSt.name,
                            shortCode: null,
                            type: null,
                            status: "ACTIVE"
                        });
                    }
                });

                if (flagSubCatCheck != true) {
                    alert("Please check any Sub category name");
                    return false;
                }
                $("#fullSelectedObjSubCategoryDiv").show();
                $("#fullSelectedObjProductDiv").show();
                $("#fullSelectedObjCategoryDiv").show();
                $("#btnAllDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }
            $scope.partObj = [];
            $scope.fullSelectedObjPartner = [];
            $scope.managePartners = function (partner, selection) {

                if (selection === true) {
                    $scope.fullSelectedObjPartner.push(partner);
                } else {
                    var indexOfPartner = $scope.fullSelectedObjPartner.indexOf(partner);
                    $scope.fullSelectedObjPartner.splice(indexOfPartner, 1);
                }
            };
            $scope.selectedSubmitPartners = function () {
                return;
                var flagSubmitPartnerCheck = "";
                if ($scope.actionOffer == "add") {
                    $scope.fullSelectedObjPartner = [];
                    $scope.partnerDetailsList.forEach(function (prtDet) {
                        if ($scope.partObj[prtDet.id] == true) {
                            flagSubmitPartnerCheck = true;
                            $scope.fullSelectedObjPartner.push({
                                id: prtDet.id,
                                check: $scope.partObj[prtDet.id],
                                name: prtDet.name,
                                status: "ACTIVE"
                            });
                        }
                    });
                    if (flagSubmitPartnerCheck != true) {
                        alert("Please check any partner or select Chaayos");
                        return false;
                    }
                } else if ($scope.actionOffer == "edit") {
                    $scope.existObjPartner = [];
                    $scope.checkedObjPartner = [];
                    $scope.existObjPartner = $scope.fullSelectedObjPartner;
                    $scope.partnerDetailsList.forEach(function (prtDet) {
                        if ($scope.partObj[prtDet.id] == true) {
                            $scope.checkedObjPartner.push({
                                id: prtDet.id,
                                check: $scope.partObj[prtDet.id],
                                name: prtDet.name,
                                status: "ACTIVE"
                            });
                        }
                    });
                    $scope.checkedObjPartner.forEach(function (resultCheckData) {
                        var existObjLength = $scope.existObjPartner;
                        for (var i = 0; i < $scope.existObjPartner.length; i++) {
                            if ($scope.existObjPartner[i].id == resultCheckData.id) {
                                $scope.fullSelectedObjPartner = resultCheckData;
                            }
                        }
                    });
                }
                $("#fullSelectedObjPartnerListDiv").show();
                $("#btnAllPartnerDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }

            $scope.fullSelectedObjOrderSource = [];


            $scope.orderSourceObj = [];
            $scope.fullSelectedObjOrderSource = [];
            $scope.selectOrderSource = function (ordSource, selection) {
                if (selection === true) {
                    // $scope.fullSelectedObjOrderSource.push(source);
                    $scope.fullSelectedObjOrderSource.push({
                        id: null,
                        name: ordSource,
                        dimension: null,
                        type: "ORDER_SOURCE",
                        value: ordSource,
                        dataType: "java.lang.String",
                        minValue: 1,
                        group: 1,
                        status: 'ACTIVE'
                    });
                } else {
                    var indexOfSource = $scope.fullSelectedObjOrderSource.indexOf(source);
                    $scope.fullSelectedObjOrderSource.splice(indexOfSource, 1);
                }
            };
            $scope.selectedSubmitOrderSource = function () {
                $scope.fullSelectedObjOrderSource = [];
                $scope.families.forEach(function (ordSource) {
                    if ($scope.orderSourceObj[ordSource] == true) {
                        $scope.fullSelectedObjOrderSource.push({
                            id: null,
                            name: ordSource,
                            dimension: null,
                            type: "ORDER_SOURCE",
                            value: ordSource,
                            dataType: "java.lang.String",
                            minValue: 1,
                            group: 1,
                            status: 'ACTIVE'
                        });
                    }
                });

                console.log($scope.fullSelectedObjOrderSource);
                $("#fullSelectedObjOrderSourceDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }
            $scope.selectedSubmitProducts = function () {
                $scope.fullSelectedObjProduct = [];
                var flagProductCheck = "";
                $scope.productDetailsList.forEach(function (prdDetList) {
                    if ($scope.prodObj[prdDetList.id] == true) {
                        flagProductCheck = true;
                        $scope.fullSelectedObjProduct.push({
                            id: prdDetList.id,
                            check: $scope.prodObj[prdDetList.id],
                            productName: prdDetList.name,
                            value: prdDetList.name,
                            type: prdDetList.type,
                            subtype: prdDetList.subType,
                            name: "PRODUCT",
                            code: prdDetList.id,
                            shortCode: null,
                            status: "ACTIVE",
                            dimensionID: prdDetList.dimensionProfileId
                        });
                    }
                });

                if (flagProductCheck != true) {
                    alert("Please check any product");
                    return false;
                }

                $("#btnAllDiv").show();
                $("#fullSelectedObjCategoryDiv").show();
                $("#fullSelectedObjSubCategoryDiv").show();
                $("#fullSelectedObjProductDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }

            $scope.getPartnerData = function (pt) {
                if (pt == 'partnersList') {
                    $scope.partnerUnit = 'partnersList';
                }
            }

            $scope.getOrderSourceData = function (pt) {
                console.log(pt)
                if (pt == 'orderSourceList') {
                    $scope.orderSourceDatas = 'orderSourceList';
                    $scope.partObj = [];
                    $scope.partnerDetailsList.forEach(function (prdChk) {
                        $scope.partObj[prdChk.id] == false;
                    });
                }
            }

            $scope.getChannelPartnerData = function (cp) {
                if (cp == 'channelPartnerList') {
                    $scope.partnerChannel = 'channelPartnerList';
                    // $scope.channelPartObj = [];
                    // $scope.channelPartner.forEach(function(channelPartnerChk) {
                    // 	$scope.channelPartObj[channelPartnerChk.id] == false;
                    // });
                }
            }
            $scope.getPaymentModeData = function (pm) {
                if (pm == 'paymentModeList') {
                    $scope.paymentModeLists = 'paymentModeList';
                    // $scope.paymentModeObj = [];
                    // $scope.paymentMode.forEach(function(payMode) {
                    // 	$scope.paymentModeObj[payMode.id] == false;
                    // });
                }
            }

            $scope.getNewCustomerModeData = function (nCustomer) {
                if (nCustomer == 'newCustomerModeList') {
                    $scope.newCustomerModeLists = 'newCustomerModeList';
                    // $scope.paymentModeObj = [];
                    // $scope.paymentMode.forEach(function(payMode) {
                    // 	$scope.paymentModeObj[payMode.id] == false;
                    // });
                }
            }

            $scope.getProductDataMap = function (prodMap, dim) {
                $scope.dimensionNameListShow = [];
                for (var k = 0; k < $scope.productDimension.length; k++) {
                    if ($scope.productDimension[k].detail.id == dim) {
                        for (var j = 0; j < $scope.productDimension[k].content.length; j++) {
                            if(j==0){
                                $scope.dimensionNameListShow.push({
                                    name: $scope.productDimension[k].content[j].name,
                                    checked: true
                                });
                                continue;
                            }
                            $scope.dimensionNameListShow.push({
                                name: $scope.productDimension[k].content[j].name,
                                checked: false
                            });
                        }
                    }
                }
            }
            $scope.getCategoryDataMap = function (itemListCategory) {
                $scope.categoryListView = itemListCategory;
                $scope.uniqueDimensionNames.forEach(function (catMod) {
                    catMod.checked = false;
                });
            }
            $scope.getSubCategoryDataMap = function () {
                $scope.uniqueDimensionNames.forEach(function (catMod) {
                    catMod.checked = false;
                });
            }
            $scope.getRegionsData = function (unitID) {
                if (unitID == 'regionUnitList') {
                    // $scope.unitObjs = [];
                    $scope.regionUnitList = 'regionUnitList';
                }
            }
            $scope.getUnitsData = function (unitID) {
                if (unitID == 'UnitList') {
                    // $scope.unitAllObj = [];
                    $scope.UnitList = 'UnitList';
                }
            }
            $scope.getUnitData = function (cat) {
                if (cat == 'category') {
                    $scope.unit = 'category';
                    // $scope.sel = [];
                    /*$scope.productCategory.forEach(function(prdChk) {
						$scope.sel[prdChk.detail.id] == false
					});*/
                } else if (cat == 'subCategory') {
                    $scope.unit = 'subCategory';
                    // $scope.subCatObj = [];
                    /*$scope.finalSubtype.forEach(function(subCat) {
						$scope.subCatObj[subCat.id] == false
					});*/
                } else if (cat == 'product') {
                    $scope.unit = 'product';
                    // $scope.prodObj = [];
                    /*$scope.productDetailsList.forEach(function(prds) {
						$scope.prodObj[prds.id] == false
					});*/
                }
            }
            $scope.offerMapping = function () {
                if ($scope.fullSelectedObjCategory != undefined) {
                    $scope.fullSelectedObjCategory.forEach(function (mt) {
                        $scope.offerCompleteObj.metaDataMappings.forEach(function (existList) {
                            if (existList.code != mt.code) {
                                $scope.offerCompleteObj.metaDataMappings.push(mt);
                            }
                        });
                    });

                }
                if ($scope.fullSelectedObjSubCategory != undefined) {
                    $scope.fullSelectedObjSubCategory.forEach(function (fullSelObSubCat) {
                        $scope.offerCompleteObj.metaDataMappings.push(fullSelObSubCat);
                    });
                }

                if ($scope.fullSelectedObjProduct != undefined) {
                    $scope.fullSelectedObjProduct.forEach(function (fullSelObPrds) {
                        $scope.offerCompleteObj.metaDataMappings.push(fullSelObPrds);
                    });
                }

                console.log("complete Obj1107=", $scope.offerCompleteObj);
                $scope.selectTab('tab3');
            }
            $scope.fullCompleteOfferObjects = [];
            $scope.addPartnersData = function () {
                if ($scope.actionOffer == "add") {
                    if ($scope.fullSelectedObjPartner == "") {
                        alert("Please select at least one partner");
                        return false;
                    } else {
                        $scope.fullSelectedObjPartner.forEach(function (prtList) {
                            $scope.offerCompleteObj.partners.push(prtList);
                        });

                        console.log($scope.offerCompleteObj);
                        $scope.couponsStatus = $scope.offerStatus;
                        $scope.startDateCoupons = $scope.startDate;
                        $scope.endDateCoupons = $scope.endDate;
                        // $scope.selectTab('tab4');
                        // return;
                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.offerManagement.offersAdd,
                            data: $scope.offerCompleteObj,
                        })
                            .then(
                                function success(response) {
                                    if (response.status == 200) {
                                        bootbox.alert("Offer Added with Id:" + response.data.id);
                                        console.log(response);
                                        $scope.fullCompleteOfferObjects = response.data;
                                        $scope.fullCompleteOfferObjectsDup = [];
                                        $scope.fullCompleteOfferObjects.metaDataMappings
                                            .forEach(function (fulComOffObj) {
                                                for (var j = 0; j < $scope.offerCompleteObj.metaDataMappings.length; j++) {
                                                    if (fulComOffObj.code == $scope.offerCompleteObj.metaDataMappings[j].code) {
                                                        $scope.fullCompleteOfferObjectsDup
                                                            .push({
                                                                code: fulComOffObj.code,
                                                                id: fulComOffObj.id,
                                                                name: fulComOffObj.name,
                                                                shortCode: fulComOffObj.shortCode,
                                                                status: fulComOffObj.status,
                                                                type: fulComOffObj.type,
                                                                value: $scope.offerCompleteObj.metaDataMappings[j].value
                                                            })
                                                    }
                                                }
                                            });
                                        $scope.fullCompleteOfferObjects.metaDataMappings = $scope.fullCompleteOfferObjectsDup;
                                        $scope.selectTab('tab4');
                                    }
                                }, function error(response) {
                                    console.log("error:" + response);
                                });
                    }
                } else if ($scope.actionOffer == "edit") {
                    $scope.offerCompleteObj.partners = $scope.fullSelectedObjPartner;
                    $scope.validateCustomer = $scope.validateCustomer.code;
                    $scope.otpRequired = $scope.otpRequired.code;
                    $scope.removeLoyalty = $scope.removeLoyalty.code;
                    console.log("BeforeObject=", $scope.offerCompleteObj)
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.offersUpdate,
                        data: $scope.offerCompleteObj,
                    }).then(function success(response) {
                        if (response.status == 200) {
                            alert("Offers Updated");
                            window.location.reload();
                        } else {
                            alert("Offers not updated");
                            console.log(response);
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }
            $scope.checkCouponsAvail = function (id, prefixData) {
                $scope.prefixCoupons = prefixData;
                if (id != undefined) {
                    $scope.prefixCouponsDetail = prefixData + id;
                    $scope.prefixCouponsDetail = $scope.prefixCouponsDetail.toUpperCase()
                    $scope.loading = true

                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.couponAvailablity,
                        data: $scope.prefixCouponsDetail
                    }).then(function success(response) {
                        if (response.data === true) {
                            $scope.coupon = response.data;
                            $scope.loading = false;
                            $scope.couponsAvail = "Available";
                            $scope.color = "green";
                        } else {
                            $scope.coupon = response.data;
                            $scope.loading = false;
                            $scope.couponsAvail = "Not Available";
                            $scope.color = "red";
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                    console.log($scope.couponsAvail);
                }
            }

            $scope.viewOfferCouponsList = function (offerId) {
                $scope.offerId=offerId;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.offerCoupons+"?applyLimit=true",
                    data: offerId
                }).then(function success(response) {
                    $scope.couponsListData1 = response.data;
                    $scope.couponsListData = [];
                    $scope.couponsListData1.forEach(function (listCoupons) {
                        listCoupons.checked = false;
                        $scope.couponsListData.push(listCoupons)
                    });

                    $rootScope.showFullScreenLoader = false;
                    console.log($scope.couponsListData);
                    $scope.currentPage = 1; // current
                    $scope.entryLimit = 50; // max no of
                    $scope.filteredItemsCouponsList = $scope.couponsListData.length; // Initially
                    $scope.totalItemsCoupons = $scope.couponsListData.length;
                }, function error(response) {
                    console.log("error:" + response);
                });
                $scope.getOfferDescriptionMetadata(offerId);
                $("#fullOfferDiv").hide();
                $("#allcouponsMappingListDetailsDiv").show();
            }
            $scope.backOfferCoupons = function () {
                $("#fullOfferDiv").show();
                $("#allcouponsMappingListDetailsDiv").hide();
            }
            $scope.backBasicCouponsDetails = function () {
                $("#allcouponsMappingListDetailsDiv").show();
                $("#CouponsMappingsViewDiv").hide();
            }

            $scope.fullCompleteCouponsObj = [];
            $scope.prefixCoupons = [];
            $scope.addCoupons = function () {
                $scope.addCouponsObj = "";
                if ($scope.actionOffer == "add") {
                    if ($scope.prefixCoupons == "" || $scope.prefixCoupons == null) {
                        alert("prefix is empty");
                        return false;
                    }
                    if ($scope.couponsAvail == "Not Available" || $scope.couponsAvail == "") {
                        alert("Code is not Available");
                        return false;
                    }
                }
                if ($scope.couponsStatus == "" || $scope.couponsStatus == undefined) {
                    alert("Status is empty");
                    return false;
                }
                if ($scope.startDateCoupons == "" || $scope.startDateCoupons == undefined) {
                    alert("Start Date is empty");
                    return false;
                }
                if ($scope.endDateCoupons == "" || $scope.endDateCoupons == undefined) {
                    alert("End Date is empty");
                    return false;
                }
                if ($scope.reusableCoupons == "" || $scope.reusableCoupons == undefined) {
                    alert("Reusable Coupons is empty");
                    return false;
                }
                if ($scope.reusableCustomerCoupons == "" || $scope.reusableCustomerCoupons == undefined) {
                    alert("Reusable Customer Coupons is empty");
                    return false;
                }
                if ($scope.maxUsageCoupons == "" || $scope.maxUsageCoupons == undefined) {
                    alert("Max Usage Coupons is empty");
                    return false;
                }
                if ($scope.customerVisibility == "" || $scope.customerVisibility == null || $scope.customerVisibility == undefined) {
                    alert("Please Select Customer Visibility");
                    return;
                }

                if ($scope.actionOffer == "add") {
                    console.log("add");
                    $scope.addCouponsObj = {
                        id: null,
                        code: $scope.prefixCouponsDetail,
                        usage: 0,
                        status: $scope.couponsStatus.code,
                        startDate: $scope.startDateCoupons,
                        endDate: $scope.endDateCoupons,
                        prefix: $scope.prefixCoupons,
                        reusable: $scope.reusableCoupons.code,
                        couponApplicability : parseInt($scope.couponApplicability),
                        reusableByCustomer: $scope.reusableCustomerCoupons.code,
                        maxUsage: $scope.maxUsageCoupons,
                        manualOverride: false,
                        offer: $scope.fullCompleteOfferObjects,
                        couponMappingList: [],
                    }
                } else if ($scope.actionOffer == "edit") {
                    console.log("edit");
                    $scope.selectedRegions = [];
                    $scope.selectedUnits = [];
                    $scope.fileContent = [];
                    $scope.fullSelectedObjOrderSource = [];
                    $scope.fullSelectedObjProduct = [];
                    $scope.fullSelectedObjPaymentMode = [];
                    $scope.fullSelectedObjChannelPartner = [];
                    $scope.newCustomerArray = [];
                    $scope.addCouponsObj = {
                        id: $scope.ExistCouponsData.id,
                        code: $scope.ExistCouponsData.code,
                        usage: $scope.ExistCouponsData.usage,
                        status: $scope.couponsStatus.code,
                        startDate: $scope.startDateCoupons,
                        endDate: $scope.endDateCoupons,
                        reusable: $scope.reusableCoupons.code,
                        couponApplicability : parseInt($scope.couponApplicability),
                        reusableByCustomer: $scope.reusableCustomerCoupons.code,
                        maxUsage: $scope.maxUsageCoupons,
                        manualOverride: false,
                        offer: $scope.ExistCouponsData.offer,
                        couponMappingList: $scope.ExistCouponsData.couponMappingList,
                        customerVisibility: $scope.customerVisibility === "Y" ? true : false
                    }

                    $scope.addCouponsObj.couponMappingList.forEach(function (couponsMap) {
                        if (couponsMap.type == "UNIT_REGION") {
                            $scope.selectedRegions.push({
                                id: couponsMap.id,
                                dimension: couponsMap.dimension,
                                type: couponsMap.type,
                                name: couponsMap.value,
                                value: couponsMap.value,
                                dataType: "java.lang.String",
                                minValue: couponsMap.minValue,
                                group: couponsMap.group,
                                status: couponsMap.status
                            });
                        }
                        if (couponsMap.type == "CONTACT_NUMBER") {
                            $scope.fileContent.push({
                                id: couponsMap.id,
                                dimension: couponsMap.dimension,
                                type: couponsMap.type,
                                name: couponsMap.value,
                                value: couponsMap.value,
                                dataType: "java.lang.String",
                                minValue: couponsMap.minValue,
                                "group": couponsMap.group,
                                status: couponsMap.status
                            });
                        }
                        if (couponsMap.type == "UNIT") {
                            $scope.selectedUnits.push({
                                id: couponsMap.id,
                                dimension: couponsMap.dimension,
                                type: couponsMap.type,
                                name: couponsMap.value,
                                value: couponsMap.value,
                                dataType: "java.lang.String",
                                minValue: couponsMap.minValue,
                                "group": couponsMap.group,
                                status: couponsMap.status
                            });
                        }
                        if (couponsMap.type == "PRODUCT_CATEGORY") {
                            $scope.fullSelectedObjProduct.push({
                                id: couponsMap.id,
                                dimension: couponsMap.dimension,
                                type: couponsMap.type,
                                name: couponsMap.value,
                                value: couponsMap.value,
                                dataType: "java.lang.String",
                                minValue: couponsMap.minValue,
                                group: couponsMap.group,
                                status: couponsMap.status
                            });
                        }
                        if (couponsMap.type == "ORDER_SOURCE") {
                            $scope.fullSelectedObjOrderSource.push({
                                id: couponsMap.id,
                                dimension: couponsMap.dimension,
                                type: couponsMap.type,
                                name: couponsMap.value,
                                value: couponsMap.value,
                                dataType: "java.lang.String",
                                minValue: couponsMap.minValue,
                                group: couponsMap.group,
                                status: couponsMap.status
                            });
                        }

                        if (couponsMap.type == "NEW_CUSTOMER") {
                            $scope.newCustomerArray.push({
                                id: couponsMap.id,
                                dimension: couponsMap.dimension,
                                type: couponsMap.type,
                                name: couponsMap.value,
                                value: couponsMap.value,
                                dataType: "java.lang.String",
                                minValue: couponsMap.minValue,
                                group: couponsMap.group,
                                status: couponsMap.status
                            });
                        }
                        $("#fullSelectedObjRegionListDiv").show();
                        $("#fullSelectedObjUnitListDiv").show();
                        $("#fullSelectedObjOrderSourceDiv").show();
                        $('.panel-collapse.in').collapse('hide');
                    });
                }
                console.log("editCoupons2===>>", $scope.addCouponsObj);
                $scope.fullCompleteCouponsObj = $scope.addCouponsObj;
                $scope.selectTab('tab5');
                $scope.selectTab('tab20');

                $("#couponsMapp").show();
                $("#fullSelectedObjRegionListDiv").show();
                $("#fullSelectedObjUnitListDiv").show();
                $("#UnitsTab").show();
            }

            $scope.showUnits = function () {
                $scope.couponsMappingUnitt = [];
                if ($scope.actionOffer == "add") {
                    if ($scope.selectedUnits != undefined) {
                        $scope.selectedUnits.forEach(function (selectUnitData) {
                            $scope.couponsMappingUnitt.push(selectUnitData);
                        });
                    }

                    if ($scope.selectedRegions != undefined) {
                        $scope.selectedRegions.forEach(function (selectRegionData) {
                            $scope.couponsMappingUnitt.push(selectRegionData);
                        });
                    }

                    $scope.couponsMappingUnitt.forEach(function (unitMap) {
                        $scope.fullCompleteCouponsObj.couponMappingList.push(unitMap);
                    });

                } else if ($scope.actionOffer == "edit") {
                    var values = [];
                    for (i in $scope.selectedRegions) {
                        console.log($scope.selectedRegions[i]);
                        values.push($scope.selectedRegions[i].value);
                    }
                    console.log("val=", values);
                    var inactiveMappings = $scope.fullCompleteCouponsObj.couponMappingList.filter(function (
                        mapping) {
                        return mapping.type == "UNIT_REGION" && mapping.status == "ACTIVE"
                            && values.indexOf(mapping.value) == -1;
                    });
                    angular.forEach(inactiveMappings, function (mapping) {
                        console.log("IN=", mapping);
                        mapping.status = "IN_ACTIVE";
                    });
                    console.log(inactiveMappings);
                    console.log("final=", $scope.fullCompleteCouponsObj);

                }
                $scope.selectTab('tab21');
                $("#UnitsTab").hide();
                $("#CustomerTab").show();
            }
            $scope.dimensionsForProducts = [];
            $scope.showCustomers = function () {
                if ($scope.fullCompleteCouponsObj.offer.category == "BILL") {
                    $scope.selectTab('tab23');
                    $("#OrderSourceTab").show();
                    $("#CustomerTab").hide();
                } else if ($scope.fullCompleteCouponsObj.offer.category == "ITEM") {
                    $scope.selectTab('tab22');
                    $("#ProductsTab").show();
                    $("#CustomerTab").hide();
                    if ($scope.fullSelectedObjProduct.length !== 0) {
                        $scope.fullSelectedObjProduct.forEach(function (product) {
                            console.log($scope.fullSelectedObjProduct)
                            for (var i = 0; i < $scope.productDimension.length; i++) {
                                if (product.dimensionId === $scope.productDimension[i].detail.id) {
                                    product.dimensions = $scope.productDimension[i].content;
                                }
                            }
                            // $scope.dimensionsForProducts.push(product.dimensionId)
                        })
                    }
                    console.log($scope.fullSelectedObjProduct);
                }
                $scope.fileContent.forEach(function (customer) {
                    $scope.fullCompleteCouponsObj.couponMappingList.push(customer);
                });
                console.log("FilesData2=", $scope.fullCompleteCouponsObj);
            }

            $scope.showProduct = function () {

                if ($scope.fullSelectedObjCategory != undefined) {
                    $scope.fullSelectedObjCateDimension.forEach(function (fselSubCat) {
                        $scope.fullCompleteCouponsObj.couponMappingList.push(fselSubCat);
                    });
                }

                if ($scope.fullSelectedObjSubCategory != undefined) {
                    $scope.fullSelectedObjSubCateDimension.forEach(function (fselSubCatDim) {
                        $scope.fullCompleteCouponsObj.couponMappingList.push(fselSubCatDim);
                    });
                }

                if ($scope.fullSelectedObjProduct != undefined) {
                    $scope.fullSelectedObjProductDimension.forEach(function (fselSubProd) {
                        $scope.fullCompleteCouponsObj.couponMappingList.push(fselSubProd);
                    });

                }

                console.log($scope.fullCompleteCouponsObj);

                $scope.selectTab('tab23');
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").show();
            }

            $scope.showOrderSource = function () {
                if ($scope.actionOffer == "add") {
                    console.log($scope.fullSelectedObjOrderSource);
                    if ($scope.fullSelectedObjOrderSource != undefined) {
                        $scope.fullSelectedObjOrderSource.forEach(function (fOrderSource) {
                            $scope.fullCompleteCouponsObj.couponMappingList.push(fOrderSource);
                        });
                    }
                }

                if ($scope.actionOffer == "edit") {
                    $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (ExistOrderSourceData) {
                        if (ExistOrderSourceData.type == 'ORDER_SOURCE') {
                            if ($scope.fullSelectedObjOrderSource.length > 0) {
                                $scope.fullSelectedObjOrderSource.forEach(function (selectOrderSourceData) {
                                    if (selectOrderSourceData.value == ExistOrderSourceData.value) {
                                        ExistOrderSourceData.status = "ACTIVE";
                                    }

                                    if (selectOrderSourceData.value != ExistOrderSourceData.value) {
                                        ExistOrderSourceData.status = "IN_ACTIVE";
                                        $scope.fullCompleteCouponsObj.couponMappingList
                                            .push(selectOrderSourceData);
                                    }
                                });

                            } else {
                                ExistOrderSourceData.status = "IN_ACTIVE";
                            }
                        }
                    });
                }

                console.log("Os7=", $scope.fullCompleteCouponsObj);
                $scope.selectTab('tab24');
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#newCustomerTab").hide();
                $("#ChannelPartnerTab").show();
                $("#PaymentModeTab").hide();
            }

            $scope.showChannelPartner = function () {
                if ($scope.actionOffer == "add") {
                    if ($scope.fullSelectedObjChannelPartner != undefined) {
                        $scope.fullSelectedObjChannelPartner.forEach(function (fChannelSource) {
                            console.log(fChannelSource);
                            $scope.fullCompleteCouponsObj.couponMappingList.push(fChannelSource);
                        });
                    }
                }
                if ($scope.actionOffer == "edit") {
                    console.log("His");
                    $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (ExistChannelData) {
                        console.log("Hi");
                        if (ExistChannelData.type == 'CHANNEL_PARTNER') {
                            console.log("cc=", $scope.fullSelectedObjChannelPartner);
                            console.log("dd=", $scope.fullSelectedObjChannelPartner.length);
                            if ($scope.fullSelectedObjChannelPartner.length > 0) {
                                $scope.fullSelectedObjChannelPartner.forEach(function (DataChannel) {
                                    if (ExistChannelData.value != $scope.fullSelectedObjChannelPartner.value) {
                                        ExistChannelData.status = "IN_ACTIVE";
                                        console.log("TT1", ExistChannelData.value);
                                        console.log("TT2", $scope.fullSelectedObjChannelPartner.value);
                                        $scope.fullCompleteCouponsObj.couponMappingList.push(DataChannel);
                                    }
                                });

                                $scope.fullSelectedObjChannelPartner.forEach(function (ChannelDataSelected) {
                                    if (ExistChannelData.value == ChannelDataSelected.value) {
                                        ExistChannelData.status = "ACTIVE";
                                    }
                                });
                            }
                        }
                    });
                }
                console.log($scope.fullCompleteCouponsObj);
                $scope.selectTab('tab25');
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").show();
            }

            $scope.showPaymentMode = function () {
                if ($scope.actionOffer == "add") {
                    if ($scope.fullSelectedObjPaymentMode != undefined) {
                        $scope.fullSelectedObjPaymentMode.forEach(function (fPayMode) {
                            $scope.fullCompleteCouponsObj.couponMappingList.push(fPayMode);
                        });
                    }
                }

                if ($scope.actionOffer == "edit") {
                    $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (ExistPaymentMode) {
                        if (ExistPaymentMode.type == 'PAYMENT_MODE') {
                            if ($scope.fullSelectedObjPaymentMode.length > 0) {
                                $scope.fullSelectedObjPaymentMode.forEach(function (DataPaymentMode) {
                                    if (ExistPaymentMode.value != $scope.fullSelectedObjPaymentMode.value) {
                                        ExistPaymentMode.status = "IN_ACTIVE";
                                        $scope.fullCompleteCouponsObj.couponMappingList.push(DataPaymentMode);
                                    }
                                });

                                $scope.fullSelectedObjPaymentMode.forEach(function (paymentModeDataSelected) {
                                    if (ExistPaymentMode.value == paymentModeDataSelected.value) {
                                        ExistPaymentMode.status = "ACTIVE";
                                    }
                                });
                            }
                        }
                    });
                }

                console.log($scope.fullCompleteCouponsObj);
                $scope.selectTab('tab26');
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $("#newCustomerTab").show();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
            }

            $scope.showPrevTab = function () {
                $scope.selectTab('tab4');
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $("#newCustomerTab").hide();
                $("#couponsMapp").hide();
            }

            $scope.showNewCustomer = function () {
                if ($scope.fullSelectedObjNewCustomer != undefined) {
                    $scope.fullSelectedObjNewCustomer.forEach(function (fNewCust) {
                        if (fNewCust.value == 'Y') {
                            $scope.fullCompleteCouponsObj.couponMappingList.push(fNewCust);
                        }
                    });
                }
                $scope.selectTab('tab6');
                $("#couponsMapp").hide();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $("#newCustomerTab").hide();
            }

            $scope.prevCustomer = function () {
                $scope.selectTab('tab20');
                $("#UnitsTab").show();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $("#OrderSourceTab").hide();
                $scope.fullCompleteCouponsObj.couponMappingList = [];

            }

            $scope.prevProduct = function () {
                $scope.selectTab('tab21');
                $("#CustomerTab").show();
                $("#ProductsTab").hide();
                $("#UnitsTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $("#OrderSourceTab").hide();

            }
            $scope.prevOrdSour = function () {
                if ($scope.fullCompleteCouponsObj.offer.category == "BILL") {
                    $scope.selectTab('tab21');
                    $("#CustomerTab").show();
                    $("#ProductsTab").hide();
                    $("#UnitsTab").hide();
                    $("#ChannelPartnerTab").hide();
                    $("#PaymentModeTab").hide();
                    $("#OrderSourceTab").hide();
                    $scope.prevContactNumberDetails = [];

                    $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (backCustomer) {
                        if (backCustomer.type != "CONTACT_NUMBER") {
                            $scope.prevContactNumberDetails.push(backCustomer);
                        }
                    });
                    $scope.fullCompleteCouponsObj.couponMappingList = [];
                    $scope.prevContactNumberDetails.forEach(function (custData) {
                        $scope.fullCompleteCouponsObj.couponMappingList.push(custData);
                    });

                } else if ($scope.fullCompleteCouponsObj.offer.category == "ITEM") {
                    $scope.selectTab('tab22');
                    $("#ProductsTab").show();
                    $("#UnitsTab").hide();
                    $("#CustomerTab").hide();
                    $("#ChannelPartnerTab").hide();
                    $("#PaymentModeTab").hide();
                    $("#OrderSourceTab").hide();
                    $scope.prevContactProductNumberDetails = [];
                    $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (backCustomerProduct) {
                        if (backCustomerProduct.type != "PRODUCT") {
                            $scope.prevContactProductNumberDetails.push(backCustomerProduct);
                        }
                    });
                    $scope.fullCompleteCouponsObj.couponMappingList = [];
                    $scope.prevContactProductNumberDetails.forEach(function (custDataProduct) {
                        $scope.fullCompleteCouponsObj.couponMappingList.push(custDataProduct);
                    });
                }
            }
            $scope.prevChaPar = function () {
                $scope.selectTab('tab23');
                $("#OrderSourceTab").show();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#PaymentModeTab").hide();
                $scope.prevorderSourceDetails = [];
                $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (orderSourceData) {
                    if (orderSourceData.type != "ORDER_SOURCE") {
                        $scope.prevorderSourceDetails.push(orderSourceData);
                    }
                });
                $scope.fullCompleteCouponsObj.couponMappingList = [];
                $scope.prevorderSourceDetails.forEach(function (channelData) {
                    $scope.fullCompleteCouponsObj.couponMappingList.push(channelData);
                });
            }

            $scope.prevPayMode = function () {
                $scope.selectTab('tab24');
                $("#ChannelPartnerTab").show();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#PaymentModeTab").hide();

                $scope.prevchannelParnterDetails = [];
                $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (channelPartnersData) {
                    if (channelPartnersData.type != "CHANNEL_PARTNER") {
                        $scope.prevchannelParnterDetails.push(channelPartnersData);
                    }
                });
                $scope.fullCompleteCouponsObj.couponMappingList = [];
                $scope.prevchannelParnterDetails.forEach(function (channelData) {
                    $scope.fullCompleteCouponsObj.couponMappingList.push(channelData);
                });
            }

            $scope.prevNewCustomer = function () {
                $scope.selectTab('tab25');
                $("#PaymentModeTab").show();
                $("#ChannelPartnerTab").hide();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#newCustomerTab").hide();

                $scope.prevPayModeDetails = [];
                $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (paymentModeData) {
                    if (paymentModeData.type != "PAYMENT_MODE") {
                        $scope.prevPayModeDetails.push(paymentModeData);
                    }
                });
                $scope.fullCompleteCouponsObj.couponMappingList = [];
                $scope.prevPayModeDetails.forEach(function (paymentModeDatass) {
                    $scope.fullCompleteCouponsObj.couponMappingList.push(paymentModeDatass);
                });
                console.log("PAYMENT_MODE-", $scope.fullCompleteCouponsObj);
            }

            $scope.prevCouponsMapping = function () {
                $("#couponsMapp").show();
                $scope.selectTab('tab5');
                $scope.selectTab('tab26');
                $("#newCustomerTab").show();
                $("#PaymentModeTab").hide();
                $("#ChannelPartnerTab").hide();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $scope.prevCustomerDetails = [];
                $scope.fullCompleteCouponsObj.couponMappingList.forEach(function (newCustomerData) {
                    if (newCustomerData.type != "NEW_CUSTOMER") {
                        $scope.prevCustomerDetails.push(newCustomerData);
                    }
                });
                $scope.fullCompleteCouponsObj.couponMappingList = [];
                // console.log("prev=",$scope.prevCustomerDetails);
                $scope.prevCustomerDetails.forEach(function (custNewData) {
                    $scope.fullCompleteCouponsObj.couponMappingList.push(custNewData);
                });
                console.log("customer-", $scope.fullCompleteCouponsObj);
            }
            $scope.prevRepCoupMode = function () {

                $scope.selectTab('tab5');
                $("#couponsMapp").show();
                $scope.selectTab('tab25');
                // $("#PaymentModeTab").show();
                $("#ReplicateCouponsTab").hide();
                $("#UnitsTab").hide();
                $("#CustomerTab").hide();
                $("#ProductsTab").hide();
                $("#OrderSourceTab").hide();
                $("#ChannelPartnerTab").hide();
            };

            $scope.selectedRegions = [];
            $scope.selectRegions = function (region, selection) {
                if (selection === true) {
                    // $scope.selectedRegions.push(region);
                    $scope.selectedRegions.push({
                        id: null,
                        dimension: null,
                        type: "UNIT_REGION",
                        name: region,
                        value: region,
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    });
                } else {
                    var indexOfRegion = $scope.selectedRegions.indexOf(region);
                    $scope.selectedRegions.splice(indexOfRegion, 1);
                }
            };
            $scope.selectedUnits = [];
            $scope.selectUnits = function (unit, selection) {
                if (selection === true) {
                    // $scope.selectedUnits.push(unit);
                    $scope.selectedUnits.push({
                        id: unit.id,
                        dimension: null,
                        type: "UNIT",
                        value: unit.id,
                        dataType: "java.lang.String",
                        minValue: 1,
                        name: unit.name,
                        "group": 1,
                        region: unit.region,
                        status: 'ACTIVE'
                    });
                } else {
                    var indexOfUnit = $scope.selectedUnits.indexOf(unit);
                    $scope.selectedUnits.splice(indexOfUnit, 1);
                }
            };
            $scope.checkRegionDetails = function (rgn, chk) {
                if ($scope.selectedUnits != undefined) {
                    for (var j = 0; j < $scope.selectedUnits.length; j++) {
                        // console.log($scope.selectedUnits[j]);
                        // console.log(rgn);
                        if ($scope.selectedUnits[j].region == rgn) {
                            if ($window
                                .confirm("Are you sure, you want to Add Region While relevant Units already added")) {
                                var remUnitList = [];
                                $scope.selectedUnits.forEach(function (rUnit) {

                                    console.log(rUnit)
                                    if (rUnit.region != rgn) {
                                        remUnitList.push(rUnit);
                                    }
                                });
                                // console.log(remUnitList);
                                $scope.selectedUnits = remUnitList;
                            } else {
                                $scope.unitObjs[rgn] = false;
                            }
                            return false;
                        } else {
                            console.log("not available");
                        }
                    }
                }
                // console.log($scope.selectedUnits);
                // console.log("out");
            }
            $scope.checkUnitDetails = function (unitRgn, unitID) {
                if ($scope.selectedRegions != undefined) {
                    for (var j = 0; j < $scope.selectedRegions.length; j++) {
                        if ($scope.selectedRegions[j].value == unitRgn) {
                            if ($window
                                .confirm("Are you sure, you want to Add Unit While relevant Region already added")) {
                                var remRegionList = [];
                                $scope.selectedRegions.forEach(function (rReg) {
                                    if (rReg.value != unitRgn) {
                                        remRegionList.push(rReg);
                                    }
                                });
                                $scope.selectedRegions = remRegionList;
                            } else {
                                $scope.unitAllObj[unitID] = false;
                            }
                            return false;
                        } else {
                            console.log("not available");
                        }
                    }

                }
            }

            $scope.selectedSubmitRegions = function (reg) {
                $scope.selectedRegions = [];
                // $scope.selectedRegionsIndex=[];

                for (var i = 0; i < reg.length; i++) {
                    if ($scope.unitObjs[reg[i]] == true) {
                        // console.log(reg[i]);

                        // $scope.selectedRegionsIndex.push(reg[i])
                        $scope.selectedRegions.push({
                            id: null,
                            dimension: null,
                            type: "UNIT_REGION",
                            name: reg[i],
                            value: reg[i],
                            dataType: "java.lang.String",
                            minValue: 1,
                            "group": 1,
                            status: 'ACTIVE'
                        });
                    }
                }
                // console.log($scope.selectedRegions);
                $("#fullSelectedObjRegionListDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }
            $scope.selectedSubmitUnits = function (Unitt) {
                console.log(Unitt);
                $scope.selectedUnits = [];
                for (var i = 0; i < Unitt.length; i++) {
                    if ($scope.unitAllObj[Unitt[i].id] == true) {
                        $scope.selectedUnits.push({
                            id: Unitt[i].id,
                            dimension: null,
                            type: "UNIT",
                            value: Unitt[i].id,
                            dataType: "java.lang.String",
                            minValue: 1,
                            name: Unitt[i].name,
                            "group": 1,
                            region: Unitt[i].region,
                            status: 'ACTIVE'
                        });
                    }
                }
                // console.log($scope.selectedUnits);
                $("#fullSelectedObjUnitListDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }

            $scope.fullSelectedObjChannelPartner = [];
            $scope.selectChannelPartner = function (partner, selection) {
                if (selection === true) {
                    // $scope.fullSelectedObjChannelPartner.push(partner);
                    $scope.fullSelectedObjChannelPartner.push({
                        id: null,
                        dimension: null,
                        name: partner.name,
                        type: "CHANNEL_PARTNER",
                        value: partner.id,
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    });
                } else {
                    var indexOfChannelPartner = $scope.fullSelectedObjChannelPartner.indexOf(partner);
                    $scope.fullSelectedObjChannelPartner.splice(indexOfChannelPartner, 1);
                }
            };

            $scope.selectedSubmitChannelPartner = function (channelPart) {
                console.log(channelPart);

                $scope.fullSelectedObjChannelPartner = [];
                for (var i = 0; i < channelPart.length; i++) {
                    if ($scope.channelPartObj[channelPart[i].id] == true) {
                        $scope.fullSelectedObjChannelPartner.push({
                            id: null,
                            dimension: null,
                            name: channelPart[i].name,
                            type: "CHANNEL_PARTNER",
                            value: channelPart[i].id,
                            dataType: "java.lang.String",
                            minValue: 1,
                            "group": 1,
                            status: 'ACTIVE'
                        });
                    }
                }
                console.log($scope.fullSelectedObjChannelPartner);
                $("#fullSelectedObjChannelPartnerDiv").show();
                $('.panel-collapse.in').collapse('hide');

            };

            $scope.fullSelectedObjPaymentMode = [];
            $scope.selectPaymentModes = function (mode, selection) {
                if (selection === true) {
                    // $scope.fullSelectedObjPaymentMode.push(mode);
                    $scope.fullSelectedObjPaymentMode.push({
                        id: null,
                        name: mode.name,
                        dimension: null,
                        type: "PAYMENT_MODE",
                        value: mode.id,
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    });
                } else {
                    var indexOfPaymentMode = $scope.fullSelectedObjPaymentMode.indexOf(mode);
                    $scope.fullSelectedObjPaymentMode.splice(indexOfPaymentMode, 1);
                }
            };

            $scope.selectedSubmitPaymentMode = function (paymodeLists) {
                console.log(paymodeLists);
                $scope.fullSelectedObjPaymentMode = [];
                for (var i = 0; i < paymodeLists.length; i++) {
                    if ($scope.paymentModeObj[paymodeLists[i].id] == true) {
                        $scope.fullSelectedObjPaymentMode.push({
                            id: null,
                            name: paymodeLists[i].name,
                            dimension: null,
                            type: "PAYMENT_MODE",
                            value: paymodeLists[i].id,
                            dataType: "java.lang.String",
                            minValue: 1,
                            "group": 1,
                            status: 'ACTIVE'
                        });
                    }
                }
                console.log($scope.fullSelectedObjPaymentMode);
                $("#fullSelectedObjPaymentModeDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }

            $scope.fullSelectedObjNewCustomer = [];
            $scope.selectNewCustomer = function (selection) {
                if (selection === true) {
                    var NewCustomer = {
                        id: null,
                        type: "NEW_CUSTOMER",
                        name: 'Yes',
                        dimension: null,
                        value: 'Y',
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    }
                    $scope.fullSelectedObjNewCustomer.push(NewCustomer);
                } else {
                    $scope.fullSelectedObjNewCustomer = [];
                }
            };
            $scope.selectedSubmitNewCustomer = function () {
                console.log($scope.newCustomerArray);
                $scope.fullSelectedObjNewCustomer = [];
                var NewCustomer = {
                    id: null,
                    type: "NEW_CUSTOMER",
                    name: 'Yes',
                    dimension: null,
                    value: 'Y',
                    dataType: "java.lang.String",
                    minValue: 1,
                    "group": 1,
                    status: 'ACTIVE'
                }

                if ($scope.newCustomerArray.value1 == true) {
                    $scope.fullSelectedObjNewCustomer.push(NewCustomer);
                } else if ($scope.newCustomerArray.value1 == false) {
                    $scope.fullSelectedObjNewCustomer;

                }

                console.log($scope.fullSelectedObjNewCustomer);
                $("#fullSelectedObjNewCustomerModeDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }

            $scope.fullSelectedObjCateDimension = [];
            $scope.selectedSubmitCategoryDimension = function (selSubCatDim, catID, catName) {
                console.log(selSubCatDim);
                for (var j = 0; j < selSubCatDim.length; j++) {
                    if (selSubCatDim[j].checked == true) {
                        console.log("TT=", $scope.actionOffer)
                        // if
                        // ($scope.fullSelectedObjCateDimension.length >
                        // 0) {
                        if ($scope.actionOffer == "add") {
                            if (!$scope.dimensionExists(selSubCatDim[j], catID)) {
                                $scope.fullSelectedObjCateDimension.push({
                                    id: null,
                                    name: catName,
                                    type: "PRODUCT_CATEGORY",
                                    dimension: selSubCatDim[j].name,
                                    value: catID,
                                    dataType: "java.lang.String",
                                    minValue: 1,
                                    "group": 1,
                                    status: 'ACTIVE'
                                });
                            }
                        }
                        /*
					 * if
					 * ($scope.fullSelectedObjCateDimension.length ==
					 * 0) {
					 * $scope.fullSelectedObjCateDimension.push({ id :
					 * null, name : catName, type :
					 * "PRODUCT_CATEGORY", dimension :
					 * selSubCatDim[j].name, value : catID, dataType :
					 * "java.lang.String", minValue : 1, group : 1,
					 * status : 'ACTIVE' }); }
					 */
                    }
                }
                console.log($scope.fullSelectedObjCateDimension);
                $("#fullSelectedObjCatDimDiv").show();
                $('.panel-collapse.in').collapse('hide');

            }

            $scope.dimensionExists = function (selected, catID) {
                var exists = false;
                $scope.fullSelectedObjCateDimension.forEach(function (fSelectObjCatDim) {
                    if (fSelectObjCatDim.dimension + fSelectObjCatDim.value == selected.name + catID) {
                        exists = true;
                    }
                });
                return exists;
            }
            $scope.fullSelectedObjSubCateDimension = [];
            $scope.selectedSubmitSubCategoryDimension = function (selSubCatDimList, catID, catName) {
                console.log(selSubCatDimList);
                console.log(catID);
                console.log(catName);

                for (var j = 0; j < selSubCatDimList.length; j++) {
                    if (selSubCatDimList[j].checked == true) {
                        console.log("Max 1");
                        if ($scope.fullSelectedObjSubCateDimension.length > 0) {
                            if (!$scope.SubCatdimensionExists(selSubCatDimList[j], catID)) {
                                $scope.fullSelectedObjSubCateDimension.push({
                                    id: null,
                                    name: catName,
                                    type: "PRODUCT_SUB_CATEGORY",
                                    dimension: selSubCatDimList[j].name,
                                    value: catID,
                                    dataType: "java.lang.String",
                                    minValue: 1,
                                    group: 1,
                                    status: 'ACTIVE'
                                });
                            }
                        }

                        if ($scope.fullSelectedObjSubCateDimension.length == 0) {
                            console.log("Max 0");
                            $scope.fullSelectedObjSubCateDimension.push({
                                id: null,
                                name: catName,
                                type: "PRODUCT_SUB_CATEGORY",
                                dimension: selSubCatDimList[j].name,
                                value: catID,
                                dataType: "java.lang.String",
                                minValue: 1,
                                "group": 1,
                                status: 'ACTIVE'
                            });
                        }
                    }
                }
                console.log($scope.fullSelectedObjSubCateDimension);
                $("#fullSelectedObjSubCatDimDiv").show();
                $('.panel-collapse.in').collapse('hide');

            }
            $scope.SubCatdimensionExists = function (selected, catID) {
                var exists = false;
                $scope.fullSelectedObjSubCateDimension.forEach(function (fSelectObjSubCatDim) {
                    if (fSelectObjSubCatDim.dimension + fSelectObjSubCatDim.value == selected.name + catID) {
                        exists = true;
                    }
                });
                return exists;
            }

            $scope.fullSelectedObjProductDimension = [];
            $scope.selectedSubmitProductDimension = function (selProductDim, catID, prodName) {
                console.log(selProductDim);
                console.log(catID);
                console.log(prodName);
                for (var j = 0; j < selProductDim.length; j++) {
                    if (selProductDim[j].checked == true) {
                        // console.log("Max 1");
                        if ($scope.fullSelectedObjProductDimension.length > 0) {

                            if (!$scope.productDimensionExists(selProductDim[j], catID)) {
                                $scope.fullSelectedObjProductDimension.push({
                                    id: null,
                                    name: prodName,
                                    type: "PRODUCT",
                                    dimension: selProductDim[j].name,
                                    value: catID,
                                    dataType: "java.lang.String",
                                    minValue: 1,
                                    "group": 1,
                                    status: 'ACTIVE'
                                });
                            }

                        }

                        if ($scope.fullSelectedObjProductDimension.length == 0) {
                            // console.log("Max 0");
                            $scope.fullSelectedObjProductDimension.push({
                                id: null,
                                name: prodName,
                                type: "PRODUCT",
                                dimension: selProductDim[j].name,
                                value: catID,
                                dataType: "java.lang.String",
                                minValue: 1,
                                "group": 1,
                                status: 'ACTIVE'
                            });
                        }
                    }
                }
                console.log($scope.fullSelectedObjProductDimension);
                $("#fullSelectedObjProductDimDiv").show();
                $('.panel-collapse.in').collapse('hide');
            }

            $scope.productDimensionExists = function (selected, catID) {
                var exists = false;
                $scope.fullSelectedObjProductDimension.forEach(function (fSelectObjSubCatDim) {
                    if (fSelectObjSubCatDim.dimension + fSelectObjSubCatDim.value == selected.name + catID) {
                        exists = true;
                    }
                });
                return exists;
            }
            $scope.delCatDimension = function (dimension, val) {
                var fullString = dimension + val;
                var removeCatList = [];
                $scope.fullSelectedObjCateDimension.forEach(function (fulObjCatDims) {
                    var fullStringObjStr = fulObjCatDims.dimension + fulObjCatDims.value;
                    if (fullStringObjStr != fullString) {
                        removeCatList.push(fulObjCatDims);
                    }
                });
                $scope.fullSelectedObjCateDimension = removeCatList;
            }

            $scope.delSubCatDimension = function (dimension, val) {
                var fullString = dimension + val;
                var removeSubCatList = [];
                $scope.fullSelectedObjSubCateDimension.forEach(function (fulObjSubCatDims) {
                    var fullStringObjStr = fulObjSubCatDims.dimension + fulObjSubCatDims.value;
                    console.log(fullStringObjStr);
                    if (fullStringObjStr != fullString) {
                        removeSubCatList.push(fulObjSubCatDims);

                    }
                });
                $scope.fullSelectedObjSubCateDimension = removeSubCatList;
            }

            $scope.delProductDim = function (dimension, val) {
                var fullString = dimension + val;
                var removeProdList = [];
                $scope.fullSelectedObjProductDimension.forEach(function (fulObjProdDims) {
                    var fullStringObjStr = fulObjProdDims.dimension + fulObjProdDims.value;
                    console.log(fullStringObjStr);
                    if (fullStringObjStr != fullString) {
                        removeProdList.push(fulObjProdDims);

                    }
                });
                $scope.fullSelectedObjProductDimension = removeProdList;
            }

            $scope.submitFinalCouponsMapp = function () {
                $scope.resultMessage = {};
                console.log(JSON.stringify($scope.fullCompleteCouponsObj));
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.couponAdd,
                    data: $scope.fullCompleteCouponsObj
                }).then(function success(response) {
                    if (response.status == 200) {
                        console.log(response.data);
                        $scope.resultMessage = "success";
                        $scope.selectTab('tab7');
                    } else {
                        console.log(response);
                        $scope.resultMessage = "failed";
                        $scope.selectTab('tab7');
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $scope.selectTab('tab7');
                $("#ReplicateCouponsTab").show();

            }

            $scope.exportData = function () {
                var blob = new Blob([document.getElementById('exportable').innerHTML], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                });
                saveAs(blob, "Report.xls");
            };
            $scope.showRepCoupMode = function (prefixx, couponCodee) {
                if ($scope.replicateCount == "" || $scope.replicateCount == null) {
                    alert("Coupons Count is Empty");
                    return false
                }
                if(angular.equals(prefixx,[])){
                    prefixx = couponCodee.slice(0,6).toUpperCase();
                    console.log(prefixx);
                }
                else {
                    prefixx = couponCodee.toUpperCase();
                    console.log(prefixx);
                }

                $scope.couponsList = [];
                var payload = $.param({
                    couponCode: couponCodee,
                    couponPrefix: prefixx,
                    replicateCount: $scope.replicateCount
                });
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: AppUtil.restUrls.offerManagement.couponAuto,
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    data: payload
                }).success(function (data) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.couponsList = data;
                    console.log(data);
                    // $("#viewProductPriceDiv").show();
                    // $scope.loading = false;
                });
            }
            $scope.$watch('fileContent', function (newVal, oldVal) {
                $scope.fileContent = newVal;
            });

            $scope.showCustomerClk = function (custDet) {
                if (custDet == "phoneNo") {
                    $("#dvPhoneNoCust").show();
                    $("#dvCustIDCust").hide();
                }
                if (custDet == "custID") {
                    $("#dvCustIDCust").show();
                    $("#dvPhoneNoCust").hide();
                }
                console.log(custDet);
            };
            $scope.showAllCouponsMap = function (couponsListDetail) {
                if (couponsListDetail.length == 0) {
                    alert("No Mapping Available on Specific Coupon");
                    return false;
                }
                $scope.search = "";
                $("#allcouponsMappingListDetailsDiv").hide();
                $("#couponsInCoupons").show();
                console.log(couponsListDetail.length);
                $scope.coupLen = couponsListDetail;
                $scope.allCouponsMap = couponsListDetail;
                $scope.currentPage = 1; // current page
                $scope.entryLimit = 50; // max no of items to display in
                // a page
                $scope.filteredItems = $scope.allCouponsMap.length; // Initially
                // for
                // no
                // filter
                $scope.totalItems = $scope.allCouponsMap.length;
                // $("#couponsListMapModals").modal("show");
            };

            $scope.backCouponsDetail = function (couponsListDetail) {
                $("#allcouponsMappingListDetailsDiv").show();
                $("#couponsInCoupons").hide();

            };
            $scope.changeStatusMappingList = function (couponsMapp, statusDetail, id) {
                $scope.allCouponsMap = couponsMapp;
                $scope.allCouponsMap.forEach(function (fullCouponsMapCoupons) {
                    if (fullCouponsMapCoupons.id == id) {
                        if (statusDetail == "ACTIVE") {
                            fullCouponsMapCoupons.status = "IN_ACTIVE";
                            $http({
                                url: AppUtil.restUrls.offerManagement.couponMappingDeactivate,
                                method: "POST",
                                dataType: 'json',
                                data: id,
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                            }).success(function (data) {
                                console.log(data);
                            });
                        } else if (statusDetail == "IN_ACTIVE") {
                            fullCouponsMapCoupons.status = "ACTIVE";
                            $http({
                                url: AppUtil.restUrls.offerManagement.couponMappingActivate,
                                method: "POST",
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                data: id
                            }).success(function (data) {
                                console.log(data)
                            });
                        }
                    }
                });
            }


            $scope.submitFinalCouponsMappUpdate = function (fullCouponsMapDataDetail) {
                console.log("eeee", JSON.stringify(fullCouponsMapDataDetail));
                var CouponDetail = fullCouponsMapDataDetail;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.couponUpdate,
                    data:CouponDetail,
                }).then(function success(response) {
                    if (response.status == 200) {
                        alert("Updated Successfully!!");
                        if (response.status == 200) {
                            console.log(response.data);
                            $scope.resultMessage = "success";
                            $scope.selectTab('tab7');
                        } else {
                            console.log(response);
                            $scope.resultMessage = "failed";
                            $scope.selectTab('tab7');
                        }
                        console.log(response.data);
                        $scope.coup={};
                        $scope.viewOfferCouponsList($scope.offerId);
                    } else {
                        console.log(response);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
                $scope.selectTab('tab7');
                $("#ReplicateCouponsTab").show();
            };

            $scope.viewFullDetailOffer = function (offerDetailList, offerID) {
                $scope.offerCouponsList = "";
                $scope.fullOfferObj = [];
                $scope.ppartnerListDisplayDatas = [];
                offerDetailList.forEach(function (fulObjOffers) {
                    if (fulObjOffers.id == offerID) {
                        $scope.fullOfferObj.push(fulObjOffers);
                    }
                });
                console.log("offer=", $scope.fullOfferObj);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.offerCoupons+"?applyLimit=true",
                    data: offerID
                }).then(function success(response) {
                    $scope.offerCouponsList = response.data;
                    $scope.mappingLength = response.data[0].couponMappingList.length;
                    console.log("LL=", $scope.mappingLength)
                    console.log("JJ==", $scope.offerCouponsList);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                });
                // console.log("fullObject=",$scope.fullOfferObj);
                $("#offerCouponsDetailsModal").modal("show");
            }
            $scope.updateCouponsDetails = function (code) {
                $scope.actionOffer = "edit";
                // $("#fullOfferDiv").show();
                $("#offerModal").modal("show");
                $scope.selectTab('tab4');
                $("#UnitsTab").show();

                $scope.ExistCouponsData = {};

                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.couponSearch+"?applyLimit=true",
                    data: code
                })
                    .then(
                        function success(response) {
                            if (response.status == 200) {
                                console.log("response=", response.data);
                                $scope.basicManulaOverriding = [];
                                $scope.ExistCouponsData = response.data;
                                $scope.prefixCouponsDetail = $scope.ExistCouponsData.code;
                                $scope.maxUsageCoupons = $scope.ExistCouponsData.maxUsage;
                                $scope.couponApplicability = $scope.ExistCouponsData.couponApplicability;
                                $scope.customerVisibility = $scope.ExistCouponsData.customerVisibility === true ? "Y" : "N";
                                var selectedCouponStatus = $scope.basicCouponStatus.filter(function (
                                    couponStatusData) {
                                    return couponStatusData.name == $scope.ExistCouponsData.status;
                                });
                                var indexOfCouponsStatus = $scope.basicCouponStatus
                                    .indexOf(selectedCouponStatus[0]);
                                $scope.couponsStatus = $scope.basicCouponStatus[indexOfCouponsStatus];

                                var startTime = moment($scope.ExistCouponsData.startDate).format(
                                    "YYYY-MM-DD");
                                var endTime = moment($scope.ExistCouponsData.endDate).format(
                                    "YYYY-MM-DD");
                                $scope.startDateCoupons = startTime;
                                $scope.endDateCoupons = endTime;

                                var selectedReusableCoupons = $scope.basicReusableCoupons
                                    .filter(function (reusableCouponsData) {

                                        if ($scope.ExistCouponsData.reusable == true) {
                                            var vals = 'true';
                                        } else if ($scope.ExistCouponsData.reusable == false) {
                                            var vals = 'false';
                                        }
                                        return reusableCouponsData.code == vals;
                                    });
                                var indexOfReusableCoupons = $scope.basicReusableCoupons
                                    .indexOf(selectedReusableCoupons[0]);
                                $scope.reusableCoupons = $scope.basicReusableCoupons[indexOfReusableCoupons];

                                var selectedReusableCustomer = $scope.basicReusableCustomer
                                    .filter(function (reusableStatusData) {
                                        if ($scope.ExistCouponsData.reusableByCustomer == true) {
                                            var valsCust = 'true';
                                        } else if ($scope.ExistCouponsData.reusableByCustomer == false) {
                                            var valsCust = 'false';
                                        }
                                        return reusableStatusData.code == valsCust;
                                    });

                                var indexOfReusableCustomer = $scope.basicReusableCustomer
                                    .indexOf(selectedReusableCustomer[0]);
                                $scope.reusableCustomerCoupons = $scope.basicReusableCustomer[indexOfReusableCustomer];

                                var selectedManualOverriding = $scope.basicManulaOverriding
                                    .filter(function (manualOverridingData) {
                                        if ($scope.ExistCouponsData.manualOverride == true) {
                                            var valsManualOverriding = 'true';
                                        } else if ($scope.ExistCouponsData.manualOverride == false) {
                                            var valsManualOverriding = 'false';
                                        }
                                        return manualOverridingData.code == valsManualOverriding;
                                    });

                                var indexOfManualOverriding = $scope.basicManulaOverriding
                                    .indexOf(selectedManualOverriding[0]);
                                $scope.mannulOverriding = $scope.basicManulaOverriding[indexOfManualOverriding];
                                $scope.filterCafeList($scope.ExistCouponsData.offer.brandId);
                            } else {
                                alert("none");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
            }

            $scope.closedModal = function () {
                $("#offerModal").modal("hide");
                window.location.reload();
            }
            $scope.closedModalOnly = function () {
                $("#offerModal").modal("hide");
                // window.location.reload();
                // console.log($scope.offerId);
                $scope.selectTab('tab4');
                $scope.viewOfferCouponsList($scope.offerId);
                $("#ReplicateCouponsTab").hide();
                $("#replicateCouponsDisplayDiv").hide();
                $scope.replicateCount=0;
                $scope.couponsList =[];
            }
            $scope.displayReplicateCoupons = function () {
                $("#replicateCouponsDisplayDiv").show();
            };

            $scope.editOfferNew = function (offerToEdit,offerId,isCloning) {
                var editOffer = null;
                if(isCloning !== undefined && isCloning != null){
                   $scope.isCloning = isCloning;
                }else{
                   $scope.isCloning = false;
                }
                $http({
                    method: "POST",
                    url: AppUtil.restUrls.offerManagement.offer,
                    dataType: 'json',
                    data: offerToEdit.id

                }).then(function success(response) {
                    editOffer = response.data;
                    if(editOffer !==null && editOffer !== undefined){
                        if(editOffer.type === "FREEBIE_STRATEGY_PERCENTAGE"){
                            editOffer.offerValueType = "PERCENTAGE";
                            editOffer.type = "FREEBIE_STRATEGY";
                        }
                        if(editOffer.type === "FREEBIE_STRATEGY_FLAT"){
                            editOffer.offerValueType = "FLAT";
                            editOffer.type = "FREEBIE_STRATEGY";
                        }
                    }
                    editOffer.isCloning = $scope.isCloning;
                    if (editOffer === null)
                        return;
                    var modalInstance = $uibModal.open({
                        animation: true,
                        ariaLabelledBy: 'modal-title',
                        ariaDescribedBy: 'modal-body',
                        templateUrl: 'views/offerModal.html',
                        backdrop: 'static',
                        controller: 'offerModalCtrl',
                        controllerAs: 'offer',
                        size: 'lg',
                        resolve: {
                            editOffer: function () {
                                return editOffer;
                            },
                            brands: function() {
                                return $scope.brands;
                            },
                            listTypes: function() {
                                return $scope.categoryLists;
                            },
                            cafelist: function() {
                                return $scope.cafelist;
                            }
                        }
                    });

                    modalInstance.result.then(function (text) {
                        if (text !== null) {
                            $scope.search = text;
                            $scope.searchLike();
                        }
                    }, function () {
                        //alert("Dismissal");
                    });
                }, function error(response) {
                    console.log("error:" + response);
                });

            };

            $scope.editOffer = function (offerEdit, id) {
                $scope.offerForEdit = null;
                console.log("offerEdists===", offerEdit);
                $scope.actionOffer = "edit";
                $scope.offerEditDetails = offerEdit;
                $scope.specificOfferDetails = {};
                /*if($scope.offerEditDetails.otpRequired === true){
                	$scope.otpRequired = $scope.validateCustomerDetails[0];
				} else {
                    $scope.otpRequired = $scope.validateCustomerDetails[1];
				}*/
                $scope.offerEditDetails.forEach(function (offerDetails) {
                    if (offerDetails.id == id) {
                        $scope.offerForEdit = offerDetails;
                        console.log(offerDetails);
                        // $scope.viewMetaList=rMetaList.metaDataMappings;
                        $scope.fullSelectedObjPartner = [];
                        $scope.specificOfferDetails = offerDetails;
                        $scope.offerCategory = offerDetails.category;
                        $scope.offerAccountCategory = offerDetails.accountsCategory;
                        // $scope.offerCategory =
                        // offerDetails.category;
                        $scope.offerText = offerDetails.text;
                        $scope.offerDescription = offerDetails.description;
                        $scope.startDate = offerDetails.startDate;
                        var startTime = moment(offerDetails.startDate).format("YYYY-MM-DD");
                        var endTime = moment(offerDetails.endDate).format("YYYY-MM-DD");
                        $scope.startDate = startTime;
                        $scope.endDate = endTime;
                        $scope.minValue = offerDetails.minValue;
                        $scope.priority = offerDetails.priority;
                        $scope.minItemCount = offerDetails.minItemCount;
                        $scope.emailDomain = offerDetails.emailDomain;
                        $scope.quantityLimit = offerDetails.minQuantity;
                        $scope.loyalityLimit = offerDetails.minLoyalty;
                        $scope.offerValue = offerDetails.offerValue;
                        var selectedofferCat = $scope.offerCat.filter(function (cats) {
                            return cats.name == offerDetails.category;
                        });
                        var indexOfCategory = $scope.offerCat.indexOf(selectedofferCat[0]);
                        $scope.offerCategory = $scope.offerCat[indexOfCategory];
                        $scope.showOfferCatDet($scope.offerCategory.code);
                        var selectedoffertype = $scope.offerCategoryLists.filter(function (selectedofferType) {
                            return selectedofferType == offerDetails.type;
                        });
                        var indexOfofferType = $scope.offerCategoryLists.indexOf(selectedoffertype[0]);
                        $scope.offerType = $scope.offerCategoryLists[indexOfofferType];

                        var selectedofferstatus = $scope.basicInfoStatus.filter(function (offerStatus) {
                            return offerStatus.name == offerDetails.status;
                        });

                        var indexOfStatus = $scope.basicInfoStatus.indexOf(selectedofferstatus[0]);
                        $scope.offerStatus = $scope.basicInfoStatus[indexOfStatus];

                        $scope.otpRequired = offerDetails.otpRequired === true ? $scope.validateCustomerDetails[0] : $scope.validateCustomerDetails[1];
                        $scope.validateCustomer = offerDetails.validateCustomer === true ? $scope.validateCustomerDetails[0] : $scope.validateCustomerDetails[1];
                        $scope.removeLoyalty = offerDetails.removeLoyalty === true ? $scope.validateCustomerDetails[0] : $scope.validateCustomerDetails[1];

                        var selectedOfferScope = $scope.offerScopeDetails.filter(function (offerScopeList) {
                            return offerScopeList.name == offerDetails.offerScope;
                        });
                        var indexOfOfferScope = $scope.offerScopeDetails.indexOf(selectedOfferScope[0]);
                        $scope.offerScope = $scope.offerScopeDetails[indexOfOfferScope];

                        $scope.fullSelectedObjPartner.partners = offerDetails.partners;
                        console.log("PartnersArray=", offerDetails.partners);
                    }
                });
                $("#offerModal").modal("show");
                $scope.selectTab('tab1');
            };

            $scope.checkAllCoupons = function (checkValue) {
                if (checkValue == "YES") {
                    console.log("allCheckedDetails", checkValue);
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: true
                        }
                    }
                } else if (checkValue == "NO") {
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
                // console.log("dd=",$scope.couponsListData=$scope.coup);
            }
            $scope.showCouponsStatus = function (offerStatus) {
                $scope.selectOfferStatus = offerStatus;
                $scope.checkboxModel = {
                    value: false
                }
            }
            $scope.showReusableCoupons = function (reusableStatusDetails) {
                $scope.reusableStatusData = reusableStatusDetails;
                $scope.checkboxReusableModel = {
                    value: false
                }
            }

            $scope.showStartDate = function (showStarDateDetails) {
                $scope.startDate = showStarDateDetails;
                $scope.checkboxStartDateModel = {
                    value: false
                }
            }

            $scope.showEndDate = function (showEndDateDetails) {
                $scope.endDate = showEndDateDetails;
                $scope.checkboxEndDateModel = {
                    value: false
                }
            }

            $scope.showReusableCustomerCoupons = function (reusableCustomerStatusDetails) {
                $scope.reusableCustomerStatusData = reusableCustomerStatusDetails;
                $scope.checkboxReusableCustomerModel = {
                    value: false
                }
            }
            $scope.showMaxUsage = function (masUsageDetails) {
                $scope.updateMaxUsage = masUsageDetails;
                $scope.checkboxMaxUsageModel = {
                    value: false
                }
            }

            $scope.showManualOveridingCoupons = function (manualOveridingDetails) {
                $scope.manualOverridingData = manualOveridingDetails;
                $scope.checkboxManualOverideModel = {
                    value: false
                }
            }

            $scope.checkCouponsUpdate = function () {
                if ($scope.coup.length != 0) {
                    console.log("data available");
                    return;
                }
                for (var i in $scope.couponsListData) {
                    $scope.coup[$scope.couponsListData[i].id] = {
                        code: $scope.couponsListData[i].code,
                        couponMappingList: $scope.couponsListData[i].couponMappingList,
                        endDate: $scope.couponsListData[i].endDate,
                        id: $scope.couponsListData[i].id,
                        manualOverride: $scope.couponsListData[i].manualOverride,
                        mappings: $scope.couponsListData[i].mappings,
                        maxUsage: $scope.couponsListData[i].maxUsage,
                        offer: $scope.couponsListData[i].offer,
                        reusable: $scope.couponsListData[i].reusable,
                        reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                        startDate: $scope.couponsListData[i].startDate,
                        status: $scope.couponsListData[i].status,
                        usage: $scope.couponsListData[i].usage,
                        checked: true
                    }
                }
            }

            $scope.checkedAllCouponStatusApply = function (checkValue) {
                console.log("Firsttt=", checkValue);
                var flag = true;
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();
                    for (var categorySubDet in $scope.coup) {
                        $scope.couponsListData.forEach(function (couponsListDetailss) {
                            if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                if ($scope.coup[categorySubDet].checked == true) {
                                    couponsListDetailss.status = $scope.selectOfferStatus.name;
                                }
                            }
                        });
                    }
                    $scope.coup = $scope.couponsListData;
                }


                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            }

            $scope.checkedReusableAllApply = function (checkValue) {
                var flag = true;
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();

                    if (checkValue == "YES") {
                        for (var categorySubDet in $scope.coup) {
                            $scope.couponsListData.forEach(function (couponsListDetailss) {
                                if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                    if ($scope.coup[categorySubDet].checked == true) {
                                        if($scope.reusableStatusData.code ==="true"){
                                            couponsListDetailss.reusable = true;
                                        }
                                        else{
                                            couponsListDetailss.reusable = false;
                                        }
                                    }
                                }
                            });
                        }
                    }
                    $scope.coup = $scope.couponsListData;
                }

                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            };

            $scope.checkedStartDateAllApply = function (checkValue) {
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();
                    for (var categorySubDet in $scope.coup) {
                        $scope.couponsListData.forEach(function (couponsListDetailss) {
                            if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                if ($scope.coup[categorySubDet].checked == true) {
                                    couponsListDetailss.startDate = $scope.startDate;
                                }
                            }
                        });
                    }
                    $scope.coup = $scope.couponsListData;
                }

                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            };

            $scope.checkedEndDateAllApply = function (checkValue) {
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();
                    if (checkValue == "YES") {
                        for (var categorySubDet in $scope.coup) {
                            $scope.couponsListData.forEach(function (couponsListDetailss) {
                                if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                    if ($scope.coup[categorySubDet].checked == true) {
                                        couponsListDetailss.endDate = $scope.endDate;
                                    }
                                }
                            });
                        }
                    }
                    $scope.coup = $scope.couponsListData;
                }
                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            }

            $scope.checkedReusableCustomerAllApply = function (checkValue) {
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();
                    for (var categorySubDet in $scope.coup) {
                        console.log($scope.reusableCustomerStatusData);
                        $scope.couponsListData
                            .forEach(function (couponsListDetailss) {
                                if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                    if ($scope.coup[categorySubDet].checked == true) {
                                        if($scope.reusableCustomerStatusData.code ==="true"){
                                            couponsListDetailss.reusableByCustomer = true;
                                        }
                                        else{
                                            couponsListDetailss.reusableByCustomer = false;
                                        }
                                    }
                                }
                            });
                    }
                    $scope.coup = $scope.couponsListData;
                }

                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            }
            $scope.checkedMaxUsageAllApply = function (checkValue) {
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();

                    if (checkValue == "YES") {
                        for (var categorySubDet in $scope.coup) {
                            $scope.couponsListData.forEach(function (couponsListDetailss) {
                                if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                    if ($scope.coup[categorySubDet].checked == true) {
                                        couponsListDetailss.maxUsage = $scope.updateMaxUsage;
                                    }
                                }
                            });
                        }
                    }
                    $scope.coup = $scope.couponsListData;
                }

                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            }

            $scope.checkedManualOverideAllApply = function (checkValue) {
                if (checkValue == "YES") {
                    for (var i in $scope.coup) {
                        $scope.coup[i].checked=true;
                    }
                    $scope.checkCouponsUpdate();

                    if (checkValue == "YES") {
                        for (var categorySubDet in $scope.coup) {
                            $scope.couponsListData.forEach(function (couponsListDetailss) {
                                if (couponsListDetailss.id == $scope.coup[categorySubDet].id) {
                                    if ($scope.coup[categorySubDet].checked == true) {
                                        if($scope.manualOverridingData.code ="true"){
                                            couponsListDetailss.manualOverride =true;
                                        }
                                        else {
                                            couponsListDetailss.manualOverride = false;
                                        }
                                    }
                                }
                            });
                        }
                    }
                    $scope.coup = $scope.couponsListData;
                }


                if (checkValue == "NO") {
                    console.log("BLANK INPUT");
                    $scope.coup = {};
                    for (i in $scope.couponsListData) {
                        $scope.coup[$scope.couponsListData[i].id] = {
                            code: $scope.couponsListData[i].code,
                            couponMappingList: $scope.couponsListData[i].couponMappingList,
                            endDate: $scope.couponsListData[i].endDate,
                            id: $scope.couponsListData[i].id,
                            manualOverride: $scope.couponsListData[i].manualOverride,
                            mappings: $scope.couponsListData[i].mappings,
                            maxUsage: $scope.couponsListData[i].maxUsage,
                            offer: $scope.couponsListData[i].offer,
                            reusable: $scope.couponsListData[i].reusable,
                            reusableByCustomer: $scope.couponsListData[i].reusableByCustomer,
                            startDate: $scope.couponsListData[i].startDate,
                            status: $scope.couponsListData[i].status,
                            usage: $scope.couponsListData[i].usage,
                            checked: false
                        }
                    }
                }
            }
            $scope.checkSingleCoupons = function (id, code, couponMappingList, offer, mapping, status,
                                                  startDate, endDate, reusable, reusableByCustomer, maxUsage, manualOverride, usage) {
                console.log("ee=", $scope.coup.length);
                if ($scope.coup[id].checked == true) {
                    $scope.coup[id] = {
                        code: code,
                        couponMappingList: couponMappingList,
                        endDate: endDate,
                        id: id,
                        manualOverride: manualOverride,
                        maxUsage: maxUsage,
                        offer: offer,
                        reusable: reusable,
                        reusableByCustomer: reusableByCustomer,
                        startDate: startDate,
                        status: status,
                        usage: usage,
                        checked: true
                    }
                } else if ($scope.coup[id].checked == false) {
                    $scope.coup[id] = {
                        code: code,
                        couponMappingList: couponMappingList,
                        endDate: endDate,
                        id: id,
                        manualOverride: manualOverride,
                        maxUsage: maxUsage,
                        offer: offer,
                        reusable: reusable,
                        reusableByCustomer: reusableByCustomer,
                        startDate: startDate,
                        status: status,
                        usage: usage,
                        checked: false
                    }
                } else {
                    $scope.coup[id] = {
                        code: code,
                        couponMappingList: couponMappingList,
                        endDate: endDate,
                        id: id,
                        manualOverride: manualOverride,
                        maxUsage: maxUsage,
                        offer: offer,
                        reusable: reusable,
                        reusableByCustomer: reusableByCustomer,
                        startDate: startDate,
                        status: status,
                        usage: usage,
                        checked: true
                    }
                }
                console.log("dd", $scope.coup);
            }
            $scope.updateBasicCouponsDetails = function () {
                console.log("ddd=", $scope.couponsListData);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.couponUpdateall,
                    data: $scope.coup,
                }).then(function success(response) {
                    if (response.status == 200 && response.data) {
                        alert("Updated Successfully!!")
                        console.log(response.data);
                        $scope.coup={};
                    } else {
                        console.log(response);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

            }

            $scope.allCouponsMappingView = function () {
                console.log("TEST=", $scope.couponsListData);
                $('#allcouponsMappingListDetailsDiv').hide();
                $('#CouponsMappingsViewDiv').show();
                $scope.unitRegionWiseMap = {};
                $scope.couponsListData
                    .forEach(function (listMappedCouponsDetail) {
                        console.log(listMappedCouponsDetail);
                        if ($scope.unitRegionWiseMap[listMappedCouponsDetail.code] == undefined) {
                            $scope.unitRegionWiseMap[listMappedCouponsDetail.code] = {};
                        }
                        for (var i = 0; i < listMappedCouponsDetail.couponMappingList.length; i++) {
                            var type = listMappedCouponsDetail.couponMappingList[i].type;
                            if ($scope.unitRegionWiseMap[listMappedCouponsDetail.code][type] == undefined) {
                                $scope.unitRegionWiseMap[listMappedCouponsDetail.code][type] = {};
                            }
                            $scope.unitRegionWiseMap[listMappedCouponsDetail.code][type][listMappedCouponsDetail.couponMappingList[i].value] = listMappedCouponsDetail.couponMappingList[i];
                        }
                    });
                console.log("UNITWise=", $scope.unitRegionWiseMap);
            };

            $scope.checkedMappingRegionApply = function () {
                console.log("pp=", $scope.regionDetailList);
                console.log("dd=", $scope.couponsListData);
            };

            $scope.checkSingleCouponsMapping = function (id, code, couponMappingList, offer, mapping, status,
                                                         startDate, endDate, reusable, reusableByCustomer, maxUsage, manualOverride, usage) {
                if ($scope.coupMap[id].checked == true) {
                    $scope.coupMap[id] = {
                        code: code,
                        couponMappingList: couponMappingList,
                        endDate: endDate,
                        id: id,
                        manualOverride: manualOverride,
                        maxUsage: maxUsage,
                        offer: offer,
                        reusable: reusable,
                        reusableByCustomer: reusableByCustomer,
                        startDate: startDate,
                        status: status,
                        usage: usage,
                        checked: true
                    }
                } else if ($scope.coupMap[id].checked == false) {
                    $scope.coupMap[id] = {
                        code: code,
                        couponMappingList: couponMappingList,
                        endDate: endDate,
                        id: id,
                        manualOverride: manualOverride,
                        maxUsage: maxUsage,
                        offer: offer,
                        reusable: reusable,
                        reusableByCustomer: reusableByCustomer,
                        startDate: startDate,
                        status: status,
                        usage: usage,
                        checked: false
                    }
                } else {
                    $scope.coupMap[id] = {
                        code: code,
                        couponMappingList: couponMappingList,
                        endDate: endDate,
                        id: id,
                        manualOverride: manualOverride,
                        maxUsage: maxUsage,
                        offer: offer,
                        reusable: reusable,
                        reusableByCustomer: reusableByCustomer,
                        startDate: startDate,
                        status: status,
                        usage: usage,
                        checked: true
                    }
                }
            }
            $scope.showRegionCheckBox = function () {
                $scope.chkboxRegion = {
                    value: false
                };
            }

            $scope.showFamilyCheckBox = function () {
                $scope.chkboxFamily = {
                    value: false
                };
            }

            $scope.showChannelPartnerCheckBox = function () {
                $scope.chkboxChannelPartner = {
                    value: false
                };
            }

            $scope.showPaymentCheckBox = function () {
                $scope.chkboxPayment = {
                    value: false
                };
            }

            $scope.showNewCustomers = function () {
                $scope.chkboxNewCustomer = {
                    value: false
                };
            }

            $scope.checkedAllCouponMappingRegionApply = function (checkCouponStatus) {
                if (checkCouponStatus == "YES") {
                    $scope.selectedRegions = $scope.regionDetailList.filter(function (region) {
                        return region.ticked;
                    }).map(function (region) {
                        return region.name;
                    });
                    console.log($scope.selectedRegions);

                    angular.forEach($scope.unitRegionWiseMap, function (value, key) {
                        if (value["UNIT_REGION"] != undefined) {
                            var regionMappings = Object.keys(value["UNIT_REGION"]);
                            console.log(regionMappings);
                            $scope.selectedRegions.forEach(function (key) {
                                if (regionMappings.indexOf(key) == -1) {
                                    value["UNIT_REGION"][key] = {
                                        id: null,
                                        dimension: null,
                                        dataType: "java.lang.Integer",
                                        group: 1,
                                        minValue: 1,
                                        value: key,
                                        status: "ACTIVE",
                                        type: "UNIT_REGION"
                                    };
                                } else {
                                    value["UNIT_REGION"][key].status = "ACTIVE";
                                }
                            });

                            regionMappings.forEach(function (key) {
                                if ($scope.selectedRegions.indexOf(key) == -1) {
                                    value["UNIT_REGION"][key].status = "IN_ACTIVE";
                                } else {
                                    value["UNIT_REGION"][key].status = "ACTIVE";
                                }
                            });
                        } else {
                            value["UNIT_REGION"] = {};
                            $scope.selectedRegions.forEach(function (key) {
                                value["UNIT_REGION"][key] = {
                                    id: null,
                                    dimension: null,
                                    dataType: "java.lang.Integer",
                                    group: 1,
                                    minValue: 1,
                                    value: key,
                                    status: "ACTIVE",
                                    type: "UNIT_REGION"
                                };
                            });
                        }
                    });

                    for (var couponCode in $scope.unitRegionWiseMap) {
                        var mappingList = $scope.unitRegionWiseMap[couponCode];
                        var regionMappings = mappingList["UNIT_REGION"];
                    }
                    console.log("final=", $scope.unitRegionWiseMap);
                }
            }

            $scope.checkedAllCouponMappingOrderSourceApply = function (checkCouponOrderStatus) {

                if (checkCouponOrderStatus == "YES") {
                    $scope.selectedFamily = $scope.familyDetailList.filter(function (family) {
                        return family.ticked;
                    }).map(function (family) {
                        return family.name;
                    });
                    console.log($scope.selectedFamily);

                    angular.forEach($scope.unitRegionWiseMap, function (value, key) {
                        if (value["ORDER_SOURCE"] != undefined) {
                            var familyMappings = Object.keys(value["ORDER_SOURCE"]);
                            console.log(familyMappings);
                            $scope.selectedFamily.forEach(function (key) {
                                if (familyMappings.indexOf(key) == -1) {
                                    value["ORDER_SOURCE"][key] = {
                                        id: null,
                                        dimension: null,
                                        dataType: "java.lang.Integer",
                                        group: 1,
                                        minValue: 1,
                                        value: key,
                                        status: "ACTIVE",
                                        type: "ORDER_SOURCE"
                                    };
                                } else {
                                    value["ORDER_SOURCE"][key].status = "ACTIVE";
                                }
                            });

                            familyMappings.forEach(function (key) {
                                if ($scope.selectedFamily.indexOf(key) == -1) {
                                    value["ORDER_SOURCE"][key].status = "IN_ACTIVE";
                                } else {
                                    value["ORDER_SOURCE"][key].status = "ACTIVE";
                                }
                            });
                        } else {
                            value["ORDER_SOURCE"] = {};
                            $scope.selectedFamily.forEach(function (key) {
                                value["ORDER_SOURCE"][key] = {
                                    id: null,
                                    dimension: null,
                                    dataType: "java.lang.Integer",
                                    group: 1,
                                    minValue: 1,
                                    value: key,
                                    status: "ACTIVE",
                                    type: "ORDER_SOURCE"
                                };
                            });
                        }
                    });

                }
            }

            $scope.openBulkCouponUpdateModal = function(){
                $scope.couponApplicability = null;
                $scope.customerVisibility = "Y";
                $scope.enableDaySchedule = false;
                $scope.enableTimeSchedule = false;
                $scope.TIMING_SEPARATOR = "--";
                getOfferDayTimeSchedule();
                $("#bulkCouponUpdateModal").modal("show");
            }

            function getOfferDayTimeSchedule() {
                var param = { offerId :  $scope.offerId };
                AdminApiService.get(
                    AppUtil.restUrls.offerManagement.getOffersDayTimeSchedule, param, { showLoader: false }
                ).then(function (responseData) {
                    if(responseData != null && responseData.data != null) {
                        var offerDayTimeSchedule = responseData.data;
                        getOffersDaySchedule(offerDayTimeSchedule.offerDayDetails.applicableDays);
                        getOffersTimeSchedule(offerDayTimeSchedule.offerTimeDetails);
                    }
                })
            }

            function getOffersDaySchedule(applicableDays) {
                $scope.daysOfWeekOptions = [];
                $scope.selectedDays = [];
                for (var i = 0; i < applicableDays.length; i++) {
                    var option = {
                        id: applicableDays[i].key,
                        label: applicableDays[i].key
                    };
                    $scope.daysOfWeekOptions.push(option);

                    if (applicableDays[i].value) {
                        $scope.selectedDays.push(option);
                    }
                }
                if($scope.selectedDays.length > 0) {
                    $scope.enableDaySchedule = true;
                }
            }

            $scope.multiSelectSettingsForDays = {
                scrollableHeight: '200px',
                scrollable: true,
                enableSearch: false,
                displayProp: 'label',
                idProp: 'id',
                externalIdProp: ''
            };

            function getOffersTimeSchedule(timingDetails) {
                $scope.startTime = null;
                $scope.endTime = null;
                if(timingDetails != null) {
                    var result = DateTimeUtil.extractStartAndEndTime(timingDetails.timeRange, $scope.TIMING_SEPARATOR);
                    $scope.startTime = result.startTime;
                    $scope.endTime = result.endTime;
                }
                if($scope.startTime != null && $scope.endTime != null) {
                    $scope.enableTimeSchedule = true;
                }
            }

            $scope.toggleScheduleTimeSettings = function() {
                $scope.timeValidationError = false;
                $scope.timeValidationMessage = "";
            };

            $scope.validateTimeRange = function () {
                $scope.timeValidationError = false;
                $scope.timeValidationMessage = "";

                var start = $scope.startTime;
                var end = $scope.endTime;

                // If only one of the fields is filled, show error
                if (start && !end) {
                    $scope.timeValidationError = true;
                    $scope.timeValidationMessage = "End time is required when start time is selected.";
                    return;
                }

                if (end && !start) {
                    $scope.timeValidationError = true;
                    $scope.timeValidationMessage = "Start time is required when end time is selected.";
                    return;
                }

                // If both are filled, check validity
                if (start && end) {
                    var startTime = DateTimeUtil.toDateTime(start);
                    var endTime = DateTimeUtil.toDateTime(end);

                    if (!startTime || !endTime) {
                        $scope.timeValidationError = true;
                        $scope.timeValidationMessage = "Invalid time format.";
                        return;
                    }

                    if (startTime >= endTime) {
                        $scope.timeValidationError = true;
                        $scope.timeValidationMessage = "End time must be after start time.";
                        return;
                    }
                }
            };

            function getSelectedDays() {
                 var selectedMap = {};

                // Mark selected days
                for (var i = 0; i < $scope.selectedDays.length; i++) {
                    selectedMap[$scope.selectedDays[i].id] = true;
                }

                // Reconstruct full list
                var finalDays = [];
                for (var j = 0; j < $scope.daysOfWeekOptions.length; j++) {
                    var key = $scope.daysOfWeekOptions[j].id;
                    finalDays.push({
                        key: key,
                        value: !!selectedMap[key]
                    });
                }
                return finalDays;
            }

            function deselectAllDays() {
                var selectedMap = {};
                for (var i = 0; i < $scope.selectedDays.length; i++) {
                    selectedMap[$scope.selectedDays[i].id] = false;
                }

                // Reconstruct full list
                var finalDays = [];
                for (var j = 0; j < $scope.daysOfWeekOptions.length; j++) {
                    var key = $scope.daysOfWeekOptions[j].id;
                    finalDays.push({
                        key: key,
                        value: !!selectedMap[key]
                    });
                }
                return finalDays;
            }

            function getBulkCouponUpdateObj() {
                var bulkCouponUpdateObj = {
                      offerId : $scope.offerId,
                      couponApplicability : parseInt($scope.couponApplicability),
                      customerVisibility : $scope.customerVisibility === "Y" ? true : false,
                      offerDayDetails : {},
                      offerTimeDetails : {}
                }
                if ($scope.enableDaySchedule) {
                    bulkCouponUpdateObj.offerDayDetails.applicableDays = getSelectedDays();
                } else {
                    bulkCouponUpdateObj.offerDayDetails.applicableDays = deselectAllDays();
                }
                bulkCouponUpdateObj.offerTimeDetails.timeRange = null;
                if($scope.enableTimeSchedule) {
                    if($scope.startTime != null && $scope.endTime != null) {
                        bulkCouponUpdateObj.offerTimeDetails.timeRange = DateTimeUtil.formatDateToTimeOnly($scope.startTime) + $scope.TIMING_SEPARATOR + DateTimeUtil.formatDateToTimeOnly($scope.endTime);
                    }
                }

                return bulkCouponUpdateObj;
            }

            $scope.updateBulkCoupons = function(){
                if ($scope.timeValidationError) {
                    return;
                }
                var bulkCouponUpdateObj = getBulkCouponUpdateObj();
                $http({
                       method: 'POST',
                       url: AppUtil.restUrls.offerManagement.couponBulkUpdate,
                       data: bulkCouponUpdateObj,
                       }).then(function success(response) {
                          if (response.status == 200) {
                             console.log(response.data);
                             toastService.create("Bulk Update Coupon fields updated successfully!");
                             $("#bulkCouponUpdateModal").modal("hide");
                          } else {
                             console.log(response);
                             alert("Error in Bulk Update");
                          }
                       }, function error(response) {
                          console.log("error:" + response);
                          alert("Error in Bulk Update");
                       });
            }

            $scope.checkedAllCouponMappingChannelApply = function (checkCouponChannelStatus) {

                if (checkCouponChannelStatus == "YES") {
                    $scope.channelList = [];
                    $scope.selectedChannel = $scope.channelDetailList.filter(function (channel) {
                        return channel.ticked;
                    }).map(function (channel) {
                        $scope.channelList.push(channel);
                        return channel;
                    });
                    console.log("List=", $scope.channelList);
                    console.log("selectedChannel->", $scope.selectedChannel);
                    angular.forEach($scope.unitRegionWiseMap, function (value, key) {
                        if (value["CHANNEL_PARTNER"] != undefined) {
                            var channelMappings = Object.keys(value["CHANNEL_PARTNER"]);
                            // console.log("ChannelMapping-",channelMappings);
                            $scope.channelList.forEach(function (keys) {
                                if (channelMappings.indexOf(keys.id) == -1) {
                                    value["CHANNEL_PARTNER"][keys.id] = {
                                        id: null,
                                        dimension: null,
                                        dataType: "java.lang.String",
                                        group: 1,
                                        minValue: 1,
                                        value: keys.id,
                                        status: "ACTIVE",
                                        type: "CHANNEL_PARTNER"
                                    };
                                } else {
                                    value["CHANNEL_PARTNER"][keys.id].status = "ACTIVE";
                                }
                            });

                            channelMappings.forEach(function (keyT) {
                                var channelMappingsData = Object.keys($scope.channelList);

                                if (channelMappingsData.indexOf(keyT) == -1) {
                                    console.log("not Available");
                                    console.log("NA3=", value["CHANNEL_PARTNER"][keyT].status = "IN_ACTIVE")

                                } else {
                                    console.log("Available");
                                    value["CHANNEL_PARTNER"][keyT].status = "ACTIVE";
                                }
                            });
                        }
                        // console.log($scope.unitRegionWiseMap);
                        else {
                            value["CHANNEL_PARTNER"] = {};
                            $scope.channelList.forEach(function (keyY) {
                                value["CHANNEL_PARTNER"][keyY.id] = {
                                    id: null,
                                    dimension: null,
                                    dataType: "java.lang.String",
                                    group: 1,
                                    minValue: 1,
                                    value: keyY.id,
                                    status: "ACTIVE",
                                    type: "CHANNEL_PARTNER"
                                };
                            });
                        }
                    });

                    console.log($scope.unitRegionWiseMap);
                }
            }

            $scope.checkedAllCouponMappingPaymentApply = function (checkCouponPaymentStatus) {

                if (checkCouponPaymentStatus == "YES") {
                    $scope.paymentList = [];
                    $scope.selectedPayment = $scope.paymentDetailList.filter(function (payment) {
                        return payment.ticked;
                    }).map(function (payment) {
                        $scope.paymentList.push(payment);
                        return payment;
                    });
                    console.log($scope.selectedPayment);
                    console.log("PaymentList=", $scope.paymentList);

                    angular.forEach($scope.unitRegionWiseMap, function (value, key) {
                        if (value["PAYMENT_MODE"] != undefined) {
                            var paymentMappings = Object.keys(value["PAYMENT_MODE"]);
                            console.log(paymentMappings);
                            $scope.paymentList.forEach(function (key) {
                                if (paymentMappings.indexOf(key.id) == -1) {
                                    value["PAYMENT_MODE"][key.id] = {
                                        id: null,
                                        dimension: null,
                                        dataType: "java.lang.String",
                                        group: 1,
                                        minValue: 1,
                                        value: key.id,
                                        name: key.name,
                                        status: "ACTIVE",
                                        type: "PAYMENT_MODE"
                                    };
                                } else {
                                    value["PAYMENT_MODE"][key.status] = "ACTIVE";
                                }
                            });

                            paymentMappings.forEach(function (key) {
                                if ($scope.paymentList.indexOf(key.id) == -1) {
                                    value["PAYMENT_MODE"][key.status] = "IN_ACTIVE";
                                } else {
                                    value["PAYMENT_MODE"][key.status] = "ACTIVE";
                                }
                            });
                        } else {
                            value["PAYMENT_MODE"] = {};
                            $scope.paymentList.forEach(function (key) {
                                value["PAYMENT_MODE"][key.id] = {
                                    id: null,
                                    dimension: null,
                                    dataType: "java.lang.String",
                                    group: 1,
                                    minValue: 1,
                                    name: key.name,
                                    value: key.id,
                                    status: "ACTIVE",
                                    type: "PAYMENT_MODE"
                                };
                            });
                        }
                    });
                }
            }
            $scope.checkedAllCouponMappingNewCustomerApply = function (checkCouponNewCustomerStatus) {
                if (checkCouponNewCustomerStatus == "Y") {
                    $scope.newCustomerList = [];
                    $scope.selectedNewCustomer = $scope.newCustomerArrayData.filter(function (newCustomer) {
                        return newCustomer.ticked;
                    }).map(function (newCustomer) {
                        $scope.newCustomerList.push(newCustomer)
                        return newCustomer;
                    });
                    console.log("OLD=", $scope.selectedNewCustomer);
                    console.log("NEW=", $scope.newCustomerList);

                    angular.forEach($scope.unitRegionWiseMap, function (value, key) {
                        if (value["NEW_CUSTOMER"] != undefined) {
                            var newCustomerMappings = Object.keys(value["NEW_CUSTOMER"]);
                            console.log(newCustomerMappings);

                            $scope.newCustomerList.forEach(function (keyNC) {
                                if (newCustomerMappings.indexOf(keyNC.code) == -1) {
                                    value["NEW_CUSTOMER"][keyNC.code] = {
                                        id: null,
                                        dimension: null,
                                        dataType: "java.lang.String",
                                        group: 1,
                                        minValue: 1,
                                        name: keyNC.name,
                                        value: keyNC.code,
                                        status: "ACTIVE",
                                        type: "NEW_CUSTOMER"
                                    };
                                } else {
                                    value["NEW_CUSTOMER"][keyNC.status] = "ACTIVE";
                                }
                            });

                            newCustomerMappings.forEach(function (key) {
                                if ($scope.newCustomerList.indexOf(key) == -1) {
                                    value["NEW_CUSTOMER"][key].status = "IN_ACTIVE";
                                } else {
                                    value["NEW_CUSTOMER"][key].status = "ACTIVE";
                                }
                            });
                        } else {
                            value["NEW_CUSTOMER"] = {};
                            $scope.newCustomerList.forEach(function (key) {
                                value["NEW_CUSTOMER"][key.code] = {
                                    id: null,
                                    dimension: null,
                                    dataType: "java.lang.String",
                                    group: 1,
                                    minValue: 1,
                                    name: key.name,
                                    value: key.code,
                                    status: "ACTIVE",
                                    type: "NEW_CUSTOMER"
                                };
                            });
                        }
                    });
                }
            }

            $scope.updatefullMappingCouponsDetails = function () {
                var flagChecks = false;

                $scope.couponsListData.forEach(function (listOfCoupons) {
                    for (var i = 0; i < listOfCoupons.couponMappingList.length; i++) {
                        if ($scope.selectedRegions != undefined) {
                            listOfCoupons.couponMappingList = [];
                        }

                    }
                    console.log("Coupons=", $scope.couponsListData);
                    console.log("Unit-Region", $scope.unitRegionWiseMap);
                    console.log("Select-Region", $scope.selectedRegions);

                    angular.forEach($scope.unitRegionWiseMap, function (value, key) {
                        if (listOfCoupons.code == key) {
                            flagChecks = true;
                            if ($scope.selectedRegions != undefined) {
                                if (value["UNIT_REGION"]) {
                                    $scope.selectedRegions.forEach(function (key) {
                                        if (value["UNIT_REGION"] != undefined) {
                                            listOfCoupons.couponMappingList.push(value["UNIT_REGION"][key]);
                                        }
                                    });
                                }
                            }
                            if ($scope.selectedFamily != undefined) {
                                if (value["ORDER_SOURCE"]) {
                                    $scope.selectedFamily.forEach(function (okeys) {
                                        if (value["ORDER_SOURCE"] != undefined) {
                                            listOfCoupons.couponMappingList.push(value["ORDER_SOURCE"][okeys]);
                                        }
                                    });
                                }
                            }
                            if ($scope.selectedChannel != undefined) {
                                if (value["CHANNEL_PARTNER"]) {
                                    $scope.selectedChannel.forEach(function (ckeys) {
                                        if (value["CHANNEL_PARTNER"] != undefined) {
                                            listOfCoupons.couponMappingList
                                                .push(value["CHANNEL_PARTNER"][ckeys.id]);
                                        }
                                    });
                                }
                            }
                            if ($scope.selectedPayment != undefined) {
                                if (value["PAYMENT_MODE"]) {
                                    $scope.selectedPayment.forEach(function (pkeys) {
                                        if (value["PAYMENT_MODE"] != undefined) {
                                            listOfCoupons.couponMappingList
                                                .push(value["PAYMENT_MODE"][pkeys.id]);
                                        }
                                    });
                                }
                            }
                            if ($scope.selectedNewCustomer != undefined) {
                                if (value["NEW_CUSTOMER"]) {
                                    $scope.selectedNewCustomer.forEach(function (nkeys) {
                                        if (value["NEW_CUSTOMER"] != undefined) {
                                            listOfCoupons.couponMappingList
                                                .push(value["NEW_CUSTOMER"][nkeys.code]);
                                        }
                                    });
                                }
                            }
                        }
                    });
                });
                // console.log("Result=",$scope.couponsListData);
                if (flagChecks) {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.couponMappingUpdate,
                        data: $scope.couponsListData,
                    }).then(function success(response) {
                        if (response.status == 200) {
                            console.log(response.data);
                        } else {
                            console.log(response);
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

            $scope.openOfferDescriptionMetadataModal = function(){
                if(!$scope.descriptionDataAvailable){
                    $scope.offerDescriptionMetadata = [];
                    $scope.offerDescriptionMetadata = [{
                        descriptionType : null,
                        descriptionValue : null,
                        color : null,
                        fontSize : null,
                    }]
                }
                $("#offerDescriptionMetadataModal").modal("show");
            };

            $scope.addDescriptionMetadata = function (index) {
                var data = $scope.offerDescriptionMetadata[index];
                if(data.descriptionType == null){
                    alert("Metadata Type cannot be null!!!");
                }
                else if(data.descriptionValue == null && data.descriptionType == "TEXT"){
                    alert("Metadata Value cannot be null!!!");
                }
                else{
                    $scope.offerDescriptionMetadata.push('');
                }
            };

            $scope.deleteDescriptionMetadata = function (index) {
                $scope.offerDescriptionMetadata.splice(index, 1);
            };

            $scope.updateOfferDescriptionMetadata = function(){
                var requestData = {};
                if($scope.descriptionDataAvailable){
                    for(var i=0;i<$scope.offerDescriptionMetadata.length;i++){
                        var data = $scope.offerDescriptionMetadata[i];
                        if(data.isBold == undefined || data.isBold == null){
                            data.isBold = 'N';
                        }
                        $scope.offerDescriptionMetadata[i] = data;
                    }
                    requestData[$scope.offerId] = $scope.offerDescriptionMetadata;
                }
                else{
                    var req = [];
                    for(var i = 0 ; i< $scope.offerDescriptionMetadata.length ;i++){
                        var data = $scope.offerDescriptionMetadata[i];
                        data.offerId = $scope.offerId;
                        data.status = "ACTIVE";
                        if(data.isBold == undefined || data.isBold == null){
                            data.isBold = 'N';
                        }
                        req.push(data);
                    }
                    requestData[$scope.offerId] = req;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.updateOfferDescriptionMetadata +  "/update-offer-description-metadata",
                    data: requestData
                }).then(function success(response) {
                    if(response.data != null && response.data){
                        $scope.offerDescriptionMetadata[$scope.offerId] = response.data;
                        $scope.getOfferDescriptionMetadata($scope.offerId);
                        alert("Data updated successfully!!!!!");
                        $("#offerDescriptionMetadataModal").modal("hide");
                    }
                    else{
                        alert("Data cannot be updated!!!!!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.refreshCouponsDetails = function() {
                $scope.viewOfferCouponsList($scope.offerId);
            }

            $scope.filterCafeList = function (brandId) {
                $scope.filteredCafeListBrandWise = $scope.cafelist.filter(cafe => {
                    return cafe.companyId === AppUtil.COMPANY_IDS[AppUtil.BRAND_IDS[brandId]];
                });
            }
        })
;

adminapp.directive('myMaxlength', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModelCtrl) {
            var maxlength = Number(attrs.myMaxlength);

            function fromUser(text) {
                if (text.length > maxlength) {
                    var transformedInput = text.substring(0, maxlength);
                    ngModelCtrl.$setViewValue(transformedInput);
                    ngModelCtrl.$render();
                    return transformedInput;
                }
                return text;
            }

            ngModelCtrl.$parsers.push(fromUser);
        }
    };
});

adminapp.directive('fileReader', function () {
    return {
        scope: {
            fileReader: "="
            // fileReader:"&"
        },
        link: function (scope, element) {
            $(element).on('change', function (changeEvent) {
                var id = $(element).parent('span').attr('id');
                if (id == 'dvPhoneNoCust') {
                    var custDet = 'CONTACT_NUMBER';
                } else if (id == 'dvCustIDCust') {
                    var custDet = 'CUSTOMER';
                }
                var files = changeEvent.target.files;
                if (files.length) {
                    var r = new FileReader();
                    scope.newArrayData = [];
                    r.onload = function (e) {
                        var contents = e.target.result;
                        scope.$apply(function () {
                            scope.fileReader = CSV2JSON(contents);
                            scope.fileReader.forEach(function (fReader) {
                                console.log(fReader);
                                if (fReader.CUSTOMER) {
                                    scope.newArrayData.push({
                                        id: null,
                                        type: custDet,
                                        name: fReader.CUSTOMER,
                                        value: fReader.CUSTOMER,
                                        status: "ACTIVE",
                                        dataType: "java.lang.String",
                                        minValue: 1,
                                        "group": 1
                                    })
                                }
                            });

                            scope.fileReader = scope.newArrayData;
                            console.log(scope.fileReader);

                        });
                    };
                    r.readAsText(files[0]);
                }
            });
        }
    };

    function CSVToArray(strData, strDelimiter) {
        strDelimiter = (strDelimiter || ",");
        var objPattern = new RegExp(("(\\" + strDelimiter + "|\\r?\\n|\\r|^)" + "(?:\"([^\"]*(?:\"\"[^\"]*)*)\"|"
            + "([^\"\\" + strDelimiter + "\\r\\n]*))"), "gi");
        var arrData = [[]];
        var arrMatches = null;
        while (arrMatches = objPattern.exec(strData)) {
            var strMatchedDelimiter = arrMatches[1];
            if (strMatchedDelimiter.length && (strMatchedDelimiter != strDelimiter)) {
                arrData.push([]);
            }
            if (arrMatches[2]) {
                var strMatchedValue = arrMatches[2].replace(new RegExp("\"\"", "g"), "\"");
            } else {
                var strMatchedValue = arrMatches[3];
            }
            arrData[arrData.length - 1].push(strMatchedValue);
        }
        console.log(arrData)
        return (arrData);
    }

    function CSV2JSON(csv) {
        var array = CSVToArray(csv);
        var objArray = [];
        for (var i = 1; i < array.length; i++) {
            objArray[i - 1] = {};
            for (var k = 0; k < array[0].length && k < array[i].length; k++) {
                var key = array[0][k];
                objArray[i - 1][key] = array[i][k]
            }
        }
        var json = JSON.stringify(objArray);
        var str = json.replace(/},/g, "},\r\n");

        return JSON.parse(str);
    }
});
