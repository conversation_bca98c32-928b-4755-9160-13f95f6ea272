/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("CreditAccountsController", function($scope, $location, $http,AppUtil){
	
	$http({
		  method: 'POST',
		  url: AppUtil.restUrls.posMetaData.creditAccountAll,
		  data: '',
		}).then(function success(response) {
			if(response.status==200){
				console.log(response.data);
				$scope.creditAccounts=response.data;
				$scope.currentPage = 1; //current page
				$scope.entryLimit = 50; //max no of items to display in a page
				$scope.filteredItems = $scope.creditAccounts.length; //Initially for no filter  
				$scope.totalItems = $scope.creditAccounts.length;
				
			}else{
				console.log(response);					
			}
		}, function error(response) {
			  console.log("error:"+response);
		});
	

	
	
	$scope.filter = function() {
        $timeout(function() { 
            $scope.filteredItems = $scope.filtered.length;
        }, 10);
    };
	
	$scope.sort_by = function(predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };
    	
	

	$scope.addCreditAccounts = function(){
				$scope.action = "Add"
				$("#creditAccountsModal").modal("show");
				$scope.legalName="";
				$scope.displayName="";
				$scope.address="";
				$scope.tanNo="";
				$scope.panNo="";
				$scope.coi="";
				$scope.bankDetail="";
				$scope.contactPerson="";
				$scope.contactPersonPhone="";
				$scope.contactPersonEmail="";
				$scope.accountPerson="";
				$scope.accountPersonPhone="";
				$scope.accountPersonEmail="";
				$scope.creditDays="";
				$scope.concernPersonChaayos="";
	}		
		
	$scope.addCreditAccountsData = function(){
		
		if($scope.legalName==null || $scope.legalName=="")
		{
		alert("Please Enter Legal Name");
		return false;
		}
		
		if($scope.contactPerson==null || $scope.contactPerson=="")
		{
		alert("Please Enter contact Person Name");
		return false;
		}
		
		
		if(!validateEmail($scope.contactPersonEmail)){
			alert("Please fill valid email address of Contact Person"); return;
		}
		
		
		if($scope.contactPersonPhone==null || $scope.contactPersonPhone=="")
		{
		alert("Please Enter contact Person Phone Number");
		return false;
		}
		
		
		if($scope.accountPerson==null || $scope.accountPerson=="")
		{
		alert("Please Enter Account Person");
		return false;
		}
		
		
		if($scope.accountPersonPhone==null || $scope.accountPersonPhone=="")
		{
		alert("Please Enter Account Person Phone Number");
		return false;
		}
		
		
		
		if($scope.creditDays==null || $scope.creditDays=="")
		{
		alert("Please Enter Credit Days");
		return false;
		}
		
		if($scope.concernPersonChaayos==null || $scope.concernPersonChaayos=="")
		{
		alert("Please Enter Concern Person in Chaayos");
		return false;
		}
		
		
		var creditAccounts = {
				legalName:$scope.legalName,
				displayName:$scope.displayName,
				address:$scope.address,
				tanNumber:$scope.tanNo,
				panNumber:$scope.panNo,
				certificateOfIncorporation:$scope.coi,
				bankDetail:$scope.bankDetail,
				contactPerson:$scope.contactPerson,
				contactPersonNumber:$scope.contactPersonPhone,
				contactPersonEmail:$scope.contactPersonEmail,
				accountContactPerson:$scope.accountPerson,
				accountContactPersonNumber:$scope.accountPersonPhone,
				accountContactPersonEmail:$scope.accountPersonEmail,
				creditDays:$scope.creditDays,
				companyContact:$scope.concernPersonChaayos,
				//concernPersonChaayos:$scope.concernPersonChaayos,
				accountStatus:"ACTIVE",
				
				}
				
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.posMetaData.creditAccountAdd,
			  data: creditAccounts
			}).then(function success(response) {
				if(response.status==200){
					//$scope.loading = false;
					//$scope.showEmpBtn=false;
					alert("Credit Account successfully Added!");
					window.location.reload();
				}else{
					//$scope.loading = false;
					
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
			});
		console.log(creditAccounts);
	}
	
	
$scope.updateCreditAccounts = function(creditAccountsSummary,creditAccountID){
		creditAccountsSummary.forEach(function(creAccSumm)
			{
			if(creAccSumm.creditAccountDetailId==creditAccountID)
				{
					$scope.legalName 			=	creAccSumm.legalName;
					$scope.displayName			=	creAccSumm.displayName;
					$scope.address				=	creAccSumm.address;
					$scope.tanNo				=	creAccSumm.tanNumber;
					$scope.panNo				=	creAccSumm.panNumber;
					$scope.coi					=	creAccSumm.certificateOfIncorporation;
					$scope.bankDetail			=	creAccSumm.bankDetail;
					$scope.contactPerson		=	creAccSumm.contactPerson;
					$scope.contactPersonPhone	=	creAccSumm.contactPersonNumber;
					$scope.contactPersonEmail	=	creAccSumm.contactPersonEmail;
					$scope.accountPerson		=	creAccSumm.accountContactPerson;
					$scope.accountPersonPhone	=	creAccSumm.accountContactPersonNumber;
					$scope.accountPersonEmail	=	creAccSumm.accountContactPersonEmail;
					$scope.creditDays			=	creAccSumm.creditDays;
					$scope.concernPersonChaayos	=	creAccSumm.companyContact;
					$scope.creditAccountDetailId=	creAccSumm.creditAccountDetailId;
					$scope.accountStatus		=	creAccSumm.accountStatus
					
					//untStatus:"Active",
				}
				});
		//console.log(creditAccountID);
		$scope.action = "Update"
		$("#creditAccountsModal").modal("show");
		
		
	};
	
	function validateEmail(email) {
		var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
		return re.test(email);
	}	
	
	
$scope.changeStatusAccountList=function(creditAccountsData,statusDetail,AccountID)
	{
	var creditAccountsObject=[];
		$scope.creditAccounts.forEach(function(fullCreditAccounts){
		if(fullCreditAccounts.creditAccountDetailId==AccountID)
		{
					fullCreditAccounts.accountStatus=statusDetail;
					creditAccountsObject.push({
					legalName:fullCreditAccounts.legalName,
					displayName:fullCreditAccounts.displayName,
					address:fullCreditAccounts.address,
					tanNumber:fullCreditAccounts.tanNumber,
					panNumber:fullCreditAccounts.panNumber,
					certificateOfIncorporation:fullCreditAccounts.certificateOfIncorporation,
					bankDetail:fullCreditAccounts.bankDetail,
					contactPerson:fullCreditAccounts.contactPerson,
					contactPersonNumber:fullCreditAccounts.contactPersonNumber,
					contactPersonEmail:fullCreditAccounts.contactPersonEmail,
					accountContactPerson:fullCreditAccounts.accountContactPerson,
					accountContactPersonNumber:fullCreditAccounts.accountContactPersonNumber,
					accountContactPersonEmail:fullCreditAccounts.accountContactPersonEmail,
					creditDays:fullCreditAccounts.creditDays,
					companyContact:fullCreditAccounts.companyContact,
					accountStatus:statusDetail,
					creditAccountDetailId:fullCreditAccounts.creditAccountDetailId
						});
				}
				});
				$http({
				url: AppUtil.restUrls.posMetaData.creditAccountUpdate,
			    method: "POST",
				dataType: 'json',
				data: creditAccountsObject[0],
				headers: { 'Content-Type': 'application/json' },
				}).success(function(data) 
				{
				});
		}

	$scope.submitUpdateCreditAccounts = function(creditAccountID){
		var creditAccountsUpdate = {
				legalName:$scope.legalName,
				displayName:$scope.displayName,
				address:$scope.address,
				tanNumber:$scope.tanNo,
				panNumber:$scope.panNo,
				certificateOfIncorporation:$scope.coi,
				bankDetail:$scope.bankDetail,
				contactPerson:$scope.contactPerson,
				contactPersonNumber:$scope.contactPersonPhone,
				contactPersonEmail:$scope.contactPersonEmail,
				accountContactPerson:$scope.accountPerson,
				accountContactPersonNumber:$scope.accountPersonPhone,
				accountContactPersonEmail:$scope.accountPersonEmail,
				creditDays:$scope.creditDays,
				companyContact:$scope.concernPersonChaayos,
				creditAccountDetailId:creditAccountID,
				accountStatus:$scope.accountStatus
				}
		
		
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.posMetaData.creditAccountUpdate,
			  data: creditAccountsUpdate
			}).then(function success(response) {
				if(response.status==200){
					//$scope.loading = false;
					//$scope.showEmpBtn=false;
					alert("Credit Account successfully Updated!");
					window.location.reload();
				}else{
					//$scope.loading = false;
					
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
			});
		
		console.log(creditAccountsUpdate);
	}
		
});