/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        "budgetUploadCtrl",
        function ($log, AuthService, $cookieStore, $rootScope, $scope,
                  $http, $location, AppUtil, fileService) {

            $scope.init = function () {
                $scope.units = [];
                // $scope.maxDate = AppUtil.formatDate(AppUtil.addDate(AppUtil.getDate(),365),"yyyy-MM-dd");
                // console.log($scope.maxDate);
                $scope.selectedUnit = null;
                $scope.currentUser=AppUtil.getUserValues().user;
                $scope.getUnits("CAFE");
            };

            $scope.selectUnit = function (value) {
                $scope.selectedUnit = value;
            };

            $scope.getUnits = function (category) {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.allUnits,
                    params: {
                        "category": category
                    }
                }).then(function success(response) {
                    $scope.units = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.downloadBudgetTemplate = function () {
                var url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplate;
                $rootScope.showDetailLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: url,
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            if (response != undefined
                                && response != null) {
                                var fileName = "BudgetTemplate - "
                                    + Date.now() + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                            $rootScope.showDetailLoader = false;
                        },
                        function error(response) {
                            console.log("error:" + response);
                            alert("Unable to download Budget Template");
                            $rootScope.showDetailLoader = false;
                        });
            };
            // download actual budget sheet (new api)
            $scope.downloadBudgetTemplateActual = function (seletedDate) {
                if (!seletedDate) {
                    alert("please select year and month");
                    return;
                }
                var url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateActual;
                var data = seletedDate.split("-");
                var year = data[0];
                var month = data[1];
                $rootScope.showDetailLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: url,
                        params: {
                            year: year, month: month
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            if (response != undefined
                                && response != null) {
                                var fileName = "BudgetTemplate - "
                                    + Date.now() + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                            $rootScope.showDetailLoader = false;
                            $scope.budgetDate = null;
                        },
                        function error(response) {
                            console.log("error:" + response);
                            alert("Unable to download Budget Template");
                            $rootScope.showDetailLoader = false;
                        });
            };


            // download template for  budget manpower

            $scope.downloadBudgetTemplateManpower = function (seletedDate) {
                if (!seletedDate) {
                    alert("please select year and month");
                    return;
                }
                var url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateManpower;
                var data = seletedDate.split("-");
                var year = data[0];
                var month = data[1];
                $rootScope.showDetailLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: url,
                        params: {
                            year: year, month: month
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            if (response != undefined
                                && response != null) {
                                var fileName = "BudgetTemplateManpower - "
                                    + Date.now() + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                            $rootScope.showDetailLoader = false;
                            $scope.manpowerDate = null;
                        },
                        function error(response) {
                            console.log("error:" + response);
                            alert("Unable to download Budget Template");
                            $rootScope.showDetailLoader = false;
                        });
            };
            $scope.uploadBudgetSheet = function () {

                if (fileService.getFile() == null
                    || fileService.getFile() == undefined) {
                    bootbox.alert("Please select a .xlsx file");
                    return;
                }

                var fd = new FormData();
                fd.append("file", fileService.getFile());
                $rootScope.showDetailLoader = true;
                var URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplate;
                $http({
                    url: URL,
                    method: 'POST',
                    data: fd,
                    params: {
                        userId: $scope.currentUser.id
                    },
                    headers: {
                        'Content-Type': undefined
                    },
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showDetailLoader = false;
                    angular.element("input[type='file']").val(null);
                    fileService.push(null);
                    bootbox.alert("Response:" + JSON.stringify(response));
                }).error(function (response) {
                    $rootScope.showDetailLoader = false;
                    alert("Error while uploading Expense Sheet");
                });
            };

            //upload manpower budget
            $scope.uploadBudgetSheetManpower = function () {

                if (fileService.getFile() == null
                    || fileService.getFile() == undefined) {
                    bootbox.alert("Please select a .xlsx file");
                    return;
                }

                var fd = new FormData();
                fd.append("file", fileService.getFile());
                $rootScope.showDetailLoader = true;
                var URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplateManpower;
                $http({
                    url: URL,
                    method: 'POST',
                    data: fd,
                    params: {
                        userId: $scope.currentUser.id
                    },
                    headers: {
                        'Content-Type': undefined
                    },
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showDetailLoader = false;
                    angular.element("input[type='file']").val(null);
                    fileService.push(null);
                    bootbox.alert("Response:" + JSON.stringify(response));
                }).error(function (response) {
                    $rootScope.showDetailLoader = false;
                    alert("Error while uploading Expense Sheet");
                });
            };

            $scope.regeneratePnlForUnit = function () {

                var payload = {
                    startDate: $scope.startDate,
                    endDate: $scope.endDate,
                    unitId: $scope.selectedUnit.id
                };
                var URL = AppUtil.restUrls.posMetaData.regenratePNLUnit;
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: URL,
                    data: payload
                }).then(
                    function success(response) {
                        $rootScope.showDetailLoader = false;
                        if (response.status === 200
                            && response.data != null) {
                            bootbox
                                .alert("PNL regenrated Successfully!");
                        } else {
                            bootbox
                                .alert("Error regenrating PNL data.\n"
                                    + JSON
                                        .stringify(response.data));
                        }
                    },
                    function error(response) {
                        $rootScope.showDetailLoader = false;
                        console.log("error:" + response);
                    });

            };

            $scope.regeneratePnlForAll = function () {

                var payload = {
                    startDate: $scope.startDate,
                    endDate: $scope.endDate
                };
                var URL = AppUtil.restUrls.posMetaData.regenratePNLAll;
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: URL,
                    data: payload
                }).then(
                    function success(response) {
                        $rootScope.showDetailLoader = false;
                        if (response.status === 200
                            && response.data != null) {
                            bootbox
                                .alert("PNL regenrated Successfully!");
                        } else {
                            bootbox
                                .alert("Error regenrating PNL data.\n"
                                    + JSON
                                        .stringify(response.data));
                        }
                    },
                    function error(response) {
                        $rootScope.showDetailLoader = false;
                        console.log("error:" + response);
                    });
            };

            $scope.regenerateFinalizedForAll = function () {

                var URL = AppUtil.restUrls.posMetaData.regenerateFinalizedForAll;
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: URL,
                    data: $scope.finalizedDate
                }).then(
                    function success(response) {
                        $rootScope.showDetailLoader = false;
                        if (response.status === 200
                            && response.data != null) {
                            bootbox
                                .alert("Finalized PNL regenrated Successfully!");
                        } else {
                            bootbox
                                .alert("Error regenrating PNL data.\n"
                                    + JSON
                                        .stringify(response.data));
                        }
                    },
                    function error(response) {
                        $rootScope.showDetailLoader = false;
                        console.log("error:" + response);
                    });
            };

            $scope.regenerateClosedForAll = function () {

                var URL = AppUtil.restUrls.posMetaData.regenerateClosedForAll;
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: URL,
                    data: $scope.closedDate
                }).then(
                    function success(response) {
                        $rootScope.showDetailLoader = false;
                        if (response.status === 200
                            && response.data != null) {
                            bootbox
                                .alert("Closed PNL regenrated Successfully!");
                        } else {
                            bootbox
                                .alert("Error regenrating PNL data.\n"
                                    + JSON
                                        .stringify(response.data));
                        }
                    },
                    function error(response) {
                        $rootScope.showDetailLoader = false;
                        console.log("error:" + response);
                    });
            };

            $scope.uploadMonthlyPenetrationData = function () {

                var URL = AppUtil.restUrls.analytics.monthlySalesTarget;
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'GET',
                    url: URL,
                    params: {
                        "fileName": "monthly_targets"
                    }
                })
                    .then(
                        function success(response) {
                            $rootScope.showDetailLoader = false;
                            if (response.status === 200
                                && response.data != null) {
                                bootbox
                                    .alert("Targets Uploaded Successfully!");
                            } else {
                                bootbox
                                    .alert("Error in uploading targers.\n"
                                        + JSON
                                            .stringify(response.data));
                            }
                        },
                        function error(response) {
                            $rootScope.showDetailLoader = false;
                            console.log("error:" + response);
                        });
            };


            $scope.downloadTemplate = function (seletedDate, type) {
                if (!seletedDate) {
                    alert("please select year and month");
                    return;
                }
                var url = " "
                if (type == 'BUDGET') {
                    url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateActual;
                } else if (type == 'MANPOWER_BUDGET') {
                    url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateManpower;
                } else if (type == 'CHANNEL_PARTNER_BUDGET') {
                    url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateChannelPartner;
                } else if (type == 'BANK_CHARGES_BUDGET') {
                    url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateBankCharges;
                } else if (type == 'FACILITY_CHARGES_BUDGET') {
                    url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateFacilityCharges;
                } else if (type == 'SERVICE_CHARGES_BUDGET') {
                    url = AppUtil.restUrls.budgetManagement.downloadBudgetTemplateServiceCharges;
                }
                var data = seletedDate.split("-");
                var year = data[0];
                var month = data[1];
                $rootScope.showDetailLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: url,
                        params: {
                            year: year, month: month
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            if (response != undefined
                                && response != null) {
                                var fileName = type + "_TEMPLATE" + " - "
                                    + Date.now() + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                            $rootScope.showDetailLoader = false;
                            $scope.budgetDate = null;
                        },
                        function error(response) {
                            console.log("error:" + response);
                            alert("Unable to download" + type + "Template");
                            $rootScope.showDetailLoader = false;
                        });
            };

            $scope.uploadSheet = function (type) {

                if (fileService.getFile() == null
                    || fileService.getFile() == undefined) {
                    bootbox.alert("Please select a .xlsx file");
                    return;
                }

                var fd = new FormData();
                fd.append("file", fileService.getFile());
                $rootScope.showDetailLoader = true;
                var URL = " "
                if (type == 'BUDGET') {
                    URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplate;
                } else if (type == 'MANPOWER_BUDGET') {
                    URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplateManpower;
                } else if (type == 'CHANNEL_PARTNER_BUDGET') {
                    URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplateChannelPartner;
                } else if (type == 'BANK_CHARGES_BUDGET') {
                    URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplateBankCharges;
                } else if (type == 'FACILITY_CHARGES_BUDGET') {
                    URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplateFacilityCharges;
                } else if (type == 'SERVICE_CHARGES_BUDGET') {
                    URL = AppUtil.restUrls.budgetManagement.uploadBudgetTemplateServiceCharges;
                }
                $http({
                    url: URL,
                    method: 'POST',
                    data: fd,
                    params: {
                        userId: $scope.currentUser.id
                    },
                    headers: {
                        'Content-Type': undefined
                    },
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showDetailLoader = false;
                    angular.element("input[type='file']").val(null);
                    fileService.push(null);
                    var data= response
                    console.log(response);
                    if(response==""){
                        bootbox.alert("File uploaded");
                    }
                    else{
                        bootbox.alert(response.errorMessage);
                    }
                    // bootbox.alert("Response:" + JSON.stringify(response));
                    // if (response.status == 200) {

                    // } else {
                    //     alert("Error while uploading Expense Sheet");
                    // }
                }).error(function (response) {
                    $rootScope.showDetailLoader = false;
                    alert("Error while uploading Expense Sheet");
                });
            };
        });
