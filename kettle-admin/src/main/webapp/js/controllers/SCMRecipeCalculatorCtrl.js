/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("SCMRecipeCalculatorCtrl", function ($rootScope, $scope, $location, $http,
                                                         $cookieStore, AuthService, AppUtil,fileService, $state,$window,Popeye, ITERATION_STATUS, $stateParams) {

    var subUomNameMap = {
        "KG": {"name": "KG", "conversion": "1000", "child": "GM"},
        "PC": {"name": "PC", "conversion": "1", "child": "PC"},
        "PKT": {"name": "PKT", "conversion": "1", "child": "PKT"}
    };
    var iterationMap = {
        existingConstruct: "existingConstruct",
        existingProduct: "existingProduct"
    };
    $scope.contentType = ["IMAGE"];
    $scope.selectedContentType = null;
    $scope.imgURL = null;

    function init() {
        $scope.isDecomissoned = false;
        $scope.availableProfiles = ['P0', 'P1', 'P2', 'P3', 'ALL'];
        $scope.iterationByProfile = "ALL";
        $scope.iterationPageDetails = {
            isShowDetails: false,
            isViewMode: false
        };
        $scope.showEmptyMsg = false;
        $scope.isNameVerified = false;
        $scope.isNameAvailable = false;
        $scope.iterationDetails = {};
        var scmConstructsInfoMap = {};
        $scope.scmProductsInfo = [];
        $scope.scmConstructsInfo = [];
        $scope.iterationList = [];
        $scope.cloningIteration = false;
        $scope.activeIteration = iterationMap.existingConstruct;
        var productsInfoList = [];
        $scope.changeIterationType($scope.activeIteration);
        getProductInfoList();
        getRecipeProfiles();
        $scope.currentUserId = AppUtil.getUserValues().user.id;
    };

    $scope.openHindiEditor = function (){
        $window.open("https://translate.google.com/?hl=hi");
    }

    $scope.updateIterationComment = function (iterationDetail) {
        console.log('iterationDetail',iterationDetail);
        if (iterationDetail.linkedProductId == null || iterationDetail.linkedProductId == undefined) {
            alert("Please link a product with this iteration to approve");
            return false;
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.scmRecipeManagement.updateIterationsComment,
            data: iterationDetail
        }).then(function success(response) {
            if (response.data.errorType) {
                console.log(" response.data.errorType  ", response.data)
            }
            $scope.isSuccessful = response.data;
            if ($scope.isSuccessful === true) {
                alert("Your review for " + $scope.iterationDetails.iterationName + " is updated Successfully!");
            } else {
                alert("Something went wrong. Please try again!");
            }
        }, function error(response) {
            alert("Something went wrong. Please try again!");
            console.log("error:" + response);
        });
    };

    $scope.createIterationFromConstruct = function (selectedConstructName, iterationDetails) {
        if (!selectedConstructName) {
            alert("Please  select a construct first!");
            return false;
        }
        $scope.iterationDetails = {};
        if (iterationDetails) {
            $scope.iterationDetails = iterationDetails;
        } else {
            var details = scmConstructsInfoMap[selectedConstructName];
            $scope.iterationDetails.productUom = details.productUom;
            $scope.iterationDetails.outputUom = details.outputUom;
            $scope.iterationDetails.outputConversion = details.outputConversion;
            $scope.iterationDetails.productConversion = details.productConversion;
            $scope.iterationDetails.linkedConstructName = details.linkedConstructName;
        }
        $scope.iterationPageDetails.isShowDetails = true;
        $state.go("dashboard.SCMRecipeCalculator.SCMRecipeDetails");
    };

    function getProductInfoList() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmProductManagement.productBasicDetails
        }).then(function success(response) {
            productsInfoList = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getRecipeProfiles() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.recipeManagement.recipeProfiles,
            params: {type: "SCM_R"}
        }).then(function success(response) {
            $scope.recipeProfiles = response.data;
            if($scope.recipeProfiles != null && $scope.recipeProfiles.length > 0){
                $scope.availableProfiles = [];
                for(var i = 0 ; i<$scope.recipeProfiles.length ;i++){
                    $scope.availableProfiles.push($scope.recipeProfiles[i].code);
                }
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.createIterationFromProduct = function (productId, iterationDetails) {
        if (!productId) {
            alert("Please  select a product first!");
            return false;
        }
        $scope.iterationDetails = {};
        if (iterationDetails) {
            $scope.iterationDetails = iterationDetails;
        } else {
            for (var i = 0; i < productsInfoList.length; i++) {
                if (productsInfoList[i].productId == productId) {
                    var details = productsInfoList[i];
                    $scope.iterationDetails.linkedProductName = details.productName;
                    $scope.iterationDetails.linkedProductId = details.productId;
                    $scope.iterationDetails.productUom = subUomNameMap[details.unitOfMeasure].child;
                    $scope.iterationDetails.productConversion = subUomNameMap[details.unitOfMeasure].conversion;
                    if ($scope.iterationDetails.productUom == "GM") {
                        $scope.iterationDetails.outputUom = $scope.iterationDetails.productUom;
                        $scope.iterationDetails.outputConversion = $scope.iterationDetails.productConversion;
                    }
                    break;
                }
            }
        }
        $scope.iterationPageDetails.isShowDetails = true;
        $state.go("dashboard.SCMRecipeCalculator.SCMRecipeDetails");
    };

    $scope.changeSelectedProfile = function (profile) {
        $scope.iterationByProfile = profile;
    };

    $scope.filterByProfile = function (iteration) {
        if ($scope.iterationByProfile == "ALL")
            return iteration;
        else
            return iteration.profile == $scope.iterationByProfile;
    };


    $scope.changeStatus = function (changedStatus) {
        $scope.isDecomissoned = changedStatus;
    }

    $scope.filterDecomissioned = function (iteration) {
        if ($scope.isDecomissoned == true) {
            return iteration.status == ITERATION_STATUS.DECOMISSONED;
        }
        else
            return iteration;
    }

    $scope.viewIterationDetails = function (iterationDetails) {
        console.log(iterationDetails.status == ITERATION_STATUS.INITIATED);
        if (iterationDetails.status == ITERATION_STATUS.INITIATED) {
            return false;
        }
        $scope.iterationDetails = iterationDetails;
        $scope.iterationPageDetails.isShowDetails = true;
        $scope.iterationPageDetails.isViewMode = true;
        $state.go("dashboard.SCMRecipeCalculator.SCMRecipeDetails");
    };

    $scope.editIteration = function (iterationdetails) {
        $scope.iterationPageDetails.isShowDetails = true;
        $state.go("dashboard.SCMRecipeCalculator.SCMRecipeDetails");
    };

    $scope.changeIterationType = function (activeIteration) {
        $scope.activeIteration = iterationMap[activeIteration];
        switch (activeIteration) {
            case iterationMap.existingConstruct :
                getAllSCMConstruct();
                break;
            case iterationMap.existingProduct :
                getAllSCMProducts();
                break;
        }
        resetData();
    };

    $scope.createNewConstruct = function () {
        var mappingModal = Popeye.openModal({
            templateUrl: "newConstruct.html",
            controller: "newConstructModalCtrl",
            click: false,
            keyboard: false
        });

        mappingModal.closed
            .then(function (isSuccessful) {
                if (isSuccessful) {
                    $scope.changeIterationType($scope.activeIteration);
                }
            });
    };

    function resetData() {
        $scope.iterationList = [];
        $scope.scmConstructsInfo = [];
        $scope.showEmptyMsg = false;
    }

    $scope.filterByCategory = function (product) {
        return product.recipeRequired == true;
    };



    var getAllSCMProducts = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmProductManagement.productBasicDetails
        }).then(function success(response) {
            //	console.log("response:" , response);
            $scope.scmProductsInfo = response.data;
        }, function error(response) {
            console.log("error:", response);
        });
    };

    function getAllSCMConstruct() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmRecipeManagement.allSCMConstruct,
        }).then(function success(response) {
            scmConstructsInfoMap = response.data;
            for (var key in scmConstructsInfoMap) {
                $scope.scmConstructsInfo.push(scmConstructsInfoMap[key]);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.archivedIterationsForConstruct = function (name) {
        if (!name) {
            alert("Please select construct first!");
            return false;
        }
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmRecipeManagement.archivedIterationsForConstruct,
            params: {constructName: name}
        }).then(function success(response) {
            $scope.iterationList = response.data;
            $scope.showEmptyMsg = true;
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.getArchivedSCMIterationsForProduct = function (productId) {
        if (!productId) {
            alert("Please select product first!");
            return false;
        }
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmRecipeManagement.archivedIterationsForProduct,
            params: {productId: productId}
        }).then(function success(response) {
            $scope.showEmptyMsg = true;
            $scope.iterationList = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    };


    $scope.changeProduct = function (productId) {
        console.log("productId", productId);

    };

    $scope.openReviewDetailsModal = function (iterationDetails, review) {
        var resolveData = {
            review: review,
            iterationDetails: iterationDetails
        }
        var mappingModal = Popeye.openModal({
            templateUrl: "reviewModal.html",
            controller: "reviewModalCtrl",
            resolve: {
                iterationData: function () {
                    return resolveData;
                }
            },
            click: false,
            keyboard: false
        });

        mappingModal.closed
            .then(function (isSuccessful) {

            });
    };

    $scope.iterationActions = function (iterationDetails, action, type) {
        var resolveData = {
            action: ITERATION_STATUS[action],
            iterationDetails: iterationDetails
        }
        var mappingModal = Popeye.openModal({
            templateUrl: "views/SCMRecipeActionModal.html",
            controller: "actionModalCtrl",
            resolve: {
                iterationData: function () {
                    return resolveData;
                }
            },
            click: false,
            keyboard: false
        });

        mappingModal.closed
            .then(function (result) {
                //console.log("isSuccessful",isSuccessful  );
                //	console.log("iterationDetails  ",iterationDetails  );
                if (result != undefined && result != null) {
                    if (result.seeCost) {
                        $scope.openRecipeIterationCostModal(iterationDetails);
                    } else {
                        if (result.isSuccessful) {
                            if (type == 'existingConstruct') {
                                $scope.getIterationForConstruct(iterationDetails.linkedConstructName);
                            } else {
                                $scope.getIterationForProduct(iterationDetails.linkedProductId);
                            }
                        }
                    }
                }
            });
    };

    $scope.openCommentUpdateModal = function (iterationDetailData) {
        $scope.iteration = iterationDetailData;
    }
    $scope.openUploadImageModal = function (iterationDetailData) {
        $scope.iteration = iterationDetailData;
    }

    $scope.cloningOptions = function (iterationDetailData, type) {
        $scope.iteration = iterationDetailData;
        $scope.type = type;
    }

    $scope.cloneAction = function (iterationDetailData, type) {
        var result = confirm("You are creating new iteration by cloning this one!");
        if (!result) {
            return false;
        }
        var iterationDetail = angular.copy(iterationDetailData);
        iterationDetail.createdById = null;
        iterationDetail.createdByName = null;
        iterationDetail.creationDate = null;
        iterationDetail.iterationId = null;
        iterationDetail.iterationName = null;
        iterationDetail.lastUpdatedById = null;
        iterationDetail.lastUpdatedByName = null;
        iterationDetail.modificationDate = null;
        iterationDetail.review = null;
        iterationDetail.profile = $scope.selectedProfile;
        iterationDetail._id = null;
        iterationDetail.status = null;
        $scope.cloningIteration = true;
        if (type == 'existingConstruct') {
            $scope.createIterationFromConstruct(iterationDetail.linkedConstructName, iterationDetail);
        } else {
            $scope.createIterationFromProduct(iterationDetail.linkedProductId, iterationDetail);
        }
        //console.log("iterationDetail",iterationDetail);
    };

    $scope.getIterationForProduct = function (productId) {
        if (!productId) {
            alert("Please select product first!");
            return false;
        }

        $http({
            method: 'POST',
            url: AppUtil.restUrls.scmRecipeManagement.getIterationForProduct,
            data: productId,
            params: {checkDecomissoned: $scope.isDecomissoned}
        }).then(function success(response) {
            $scope.iterationList = response.data;
            $scope.showEmptyMsg = true;
            //		console.log("$scope.iterationList  :  " , $scope.iterationList);
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.openRecipeIterationCostModal = function (iterationDetails) {
        var mappingModal = Popeye.openModal({
            templateUrl: "views/recipeIterationCostModal.html",
            controller: "recipeIterationCostCtrl",
            resolve: {
                iterationDetails: function () {
                    return iterationDetails;
                }
            },
            click: false,
            keyboard: false
        });

        mappingModal.closed
            .then(function (isSuccessful) {
            });
    };
    $scope.uploadSCMImage=function(iterationData){
        var getFiles = document.getElementById("scmRecipeImages");
        if($scope.selectedContentType === null){
            bootbox.alert("Please select content type");
            return false;
        }

        var imageFiles = getFiles.files;
        if(imageFiles.length===0){
            bootbox.alert("Please select an Image");
            return false;
        }else{
            var fd = new FormData();
            fd.append('recipeIteration_id',iterationData._id);
            for(var i=0;i<imageFiles.length;i++){
                        var fileType = imageFiles[i].type;
                        if(!(angular.equals(fileType, "image/jpeg") || angular.equals(fileType, "image/jpg") || angular.equals(fileType, "image/png"))){
                            alert("File is not a image");
                            return;
                        }
                        if(imageFiles[i].size > 500000){
                            alert('File size should not be greater than 500 kb');
                            return;
                        }
                        var currentTimestamp=new Date().getTime();
                        var fileExt = getFileExtension(imageFiles[i].name);
                        var nameWithoutExt = imageFiles[i].name.replace("."+fileExt,"");
                        var newFile=new File([imageFiles[i]],nameWithoutExt+currentTimestamp+"."+fileExt);
                        fd.append("file", newFile);
            }
            $rootScope.showDetailLoader = true;
            var URL = AppUtil.restUrls.scmRecipeManagement.uploadSCMMedia;
            $http({
                url:URL,
                method:'POST',
                data:fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response){
                $rootScope.showDetailLoader = false;
                fileService.push(null);
                angular.element("input[type='file']").val(null);
                $scope.selectedContentType = null;
                alert("Image Uploaded");
            }).error(function (response){
                $rootScope.showDetailLoader = false;
                $scope.reset();
                alert("Error while uploading Media");
            });
        }
    }
    $scope.reset = function () {
        fileService.push(null);
        angular.element("input[type='file']").val(null);
        $scope.selectedContentType = null;
    }

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function isImage(fileExt) {
        return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png";
    }

    $scope.getIterationForConstruct = function (name) {
        if (!name) {
            alert("Please select construct");
            return false;
        }

        $http({
            method: 'POST',
            url: AppUtil.restUrls.scmRecipeManagement.iterationForConstruct,
            params: {constructName: name}
        }).then(function success(response) {
            $scope.iterationList = response.data;
            $scope.showEmptyMsg = true;
            //	console.log("$scope.iterationList  :  " , $scope.iterationList);
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    init();

});

adminapp.controller("newConstructModalCtrl", function ($rootScope, $scope, $http,
                                                       $cookieStore, AppUtil, $state, Popeye, ITERATION_STATUS) {
    $scope.subUomNameList = [{"name": "GM"}, {"name": "PC"}, {"name": "PKT"}];

    function init() {
        $scope.isSuccessful = false;
        $scope.productUom = $scope.subUomNameList[0];
        $scope.outputUom = $scope.subUomNameList[0];
        $scope.conversionUom = 1000;
        $scope.iterationDetails = {
            productUom: $scope.productUom.name,
            outputUom: $scope.outputUom.name,
            outputConversion: $scope.conversionUom,
            productConversion: $scope.conversionUom,
            status: ITERATION_STATUS.INITIATED
        };

        var data=AppUtil.getUserValues();
        if (data != undefined && data != null) {
            $scope.iterationDetails.createdById = data.user.id;
            $scope.iterationDetails.createdByName = data.user.name;
        }
    }

    $scope.validateConstructName = function (name) {
        $scope.isNameVerified = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmRecipeManagement.validateConstructName,
            params: {constructName: name}
        }).then(function success(response) {
            //	console.log("$scope.response.data  :  " + response.data);
            if (response.data == true) {
                $scope.isNameAvailable = response.data;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.updateCosntructName = function () {
        $scope.isNameVerified = false;
        $scope.isNameAvailable = false;
    };

    $scope.changeProductUom = function (uom) {
        $scope.iterationDetails.productUom = uom.name;
        if (uom.name == 'PC' && $scope.outputUom.name == 'GM') {
            $scope.conversionUom = undefined;
            $scope.iterationDetails.productUom = uom.name;
            $scope.iterationDetails.outputConversion = $scope.conversionUom;
            $scope.iterationDetails.productConversion = 1;
        } else if (uom.name == 'GM' && $scope.outputUom.name == 'GM') {
            $scope.conversionUom = 1000;
            $scope.iterationDetails.productUom = uom.name;
            $scope.iterationDetails.outputConversion = $scope.conversionUom;
            $scope.iterationDetails.productConversion = $scope.conversionUom;
        }
    };

    $scope.updateOutputUom = function (uomName) {
        $scope.iterationDetails.outputConversion = uomName;
    };

    $scope.changeOutputUom = function (uom) {
        $scope.iterationDetails.outputUom = uom.name;
        if (uom.name == 'PC') {
            $scope.conversionUom = 1;
            $scope.productUom = uom;
            $scope.iterationDetails.productUom = $scope.productUom.name;
            $scope.iterationDetails.outputConversion = $scope.conversionUom;
            $scope.iterationDetails.productConversion = $scope.conversionUom;
        } else if (uom.name == 'PKT') {
            $scope.conversionUom = 1;
            $scope.productUom = uom;
            $scope.iterationDetails.productUom = $scope.productUom.name;
            $scope.iterationDetails.outputConversion = $scope.conversionUom;
            $scope.iterationDetails.productConversion = $scope.conversionUom;
        } else if (uom.name == 'GM') {
            $scope.conversionUom = 1000;
            $scope.productUom = uom;
            $scope.iterationDetails.productUom = $scope.productUom.name;
            $scope.iterationDetails.outputConversion = $scope.conversionUom;
            $scope.iterationDetails.productConversion = $scope.conversionUom;
        }
    };

    $scope.createConstruct = function (isIterationCreateFormValid) {
        if (isIterationCreateFormValid) {
            if (!$scope.isNameAvailable) {
                alert("Please verify  name to create unique !");
                return false;
            }
            $http({
                method: 'POST',
                url: AppUtil.restUrls.scmRecipeManagement.addIteration,
                data: $scope.iterationDetails
            }).then(function success(response) {
                alert("Construct created successfully Now you can add iterarion to this :" + $scope.iterationDetails.linkedConstructName);
                $scope.iterationDetails = response.data;
                $scope.isSuccessful = true;
                $scope.closeModal();
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    };

    $scope.closeModal = function () {
        Popeye.closeCurrentModal($scope.isSuccessful);
    }

    init();

});

adminapp.controller('actionModalCtrl', function ($rootScope, $scope, AppUtil, $http, Popeye, $cookieStore, iterationData) {
    $scope.iterationDetails = iterationData.iterationDetails;
    $scope.isSuccessful = false;
    $scope.action = iterationData.action;
    $scope.actionText = iterationData.action.substr(0, $scope.action.length - 1);

    //console.log("iterationDetails  action",$scope.action);

    function init() {
        $scope.review = {};
        if (iterationData.iterationDetails.profile != null) {
            $scope.selectedProfile = iterationData.iterationDetails.profile;
        }
        else {
            $scope.selectedProfile = null
        }
        getRecipeProfiles();
        $scope.scmProductsInfo = [];
       var data=AppUtil.getUserValues();
        if (data != undefined && data != null) {
            $scope.review.reviewedById = data.user.id;
            $scope.review.reviewedByName = data.user.name;
        }
        if ($scope.action == "APPROVED" && ($scope.iterationDetails.linkedProductId == null || $scope.iterationDetails.linkedProductId == undefined)) {
            getAllSCMProducts();
        }
    }

    $scope.changeProfile = function (profileName) {
        $scope.selectedProfile = profileName;
    };

    $scope.seeCost = function () {
        $scope.closeModal(true);
    };

    function getRecipeProfiles() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.recipeManagement.recipeProfiles,
            params: {type: "SCM_R"}
        }).then(function success(response) {
            $scope.recipeProfiles = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.filterByCategory = function (product) {
        return product.recipeRequired == true;
    };

    $scope.changeProduct = function (product) {
        $scope.iterationDetails.linkedProductId = product.productId;
        $scope.iterationDetails.linkedProductName = product.productName;

    };

    var getAllSCMProducts = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmProductManagement.productBasicDetails
        }).then(function success(response) {
            $scope.scmProductsInfo = response.data;
            //	console.log("scmProductsInfo",$scope.scmProductsInfo);
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:", response);
        });
    }

    $scope.submit = function (formValid) {
        if ($scope.action == "APPROVED" && ($scope.iterationDetails.linkedProductId == null || $scope.iterationDetails.linkedProductId == undefined)) {
            alert("Please link a product with this iteration to approve");
            return false;
        }
        if ($scope.action == "APPROVED" && ($scope.selectedProfile == null && $scope.selectedProfile == undefined)) {
            alert("Please attach a product profile with this iteration to approve");
            return false;
        }
        if (formValid) {
            $scope.iterationDetails.profile = $scope.selectedProfile;
            $scope.iterationDetails.review = $scope.review;
            $scope.iterationDetails.lastUpdatedById = $scope.review.reviewedById;
            $scope.iterationDetails.lastUpdatedByName = $scope.review.reviewedByName;
            $scope.iterationDetails.status = $scope.action;
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.scmRecipeManagement.updateIterationStatus,
                data: $scope.iterationDetails
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if (response.data.errorType) {
                    console.log(" response.data.errorType  ", response.data)
                }
                $scope.isSuccessful = response.data;
                if ($scope.isSuccessful === true) {
                    alert("Your review for " + $scope.iterationDetails.iterationName + " is recorded");
                } else {
                    alert("Something went wrong. Please try again!");
                }
                $scope.closeModal();
            }, function error(response) {
                alert("Something went wrong. Please try again!");
                $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
            });
        }
    };

    $scope.closeModal = function (seeCost) {
        Popeye.closeCurrentModal({
            "isSuccessful" : $scope.isSuccessful,
            "seeCost" : seeCost
        });
    };

    init();
});

adminapp.controller('reviewModalCtrl', function ($scope, Popeye, iterationData) {
    $scope.iterationDetails = iterationData.iterationDetails;
    $scope.reviewDetails = iterationData.review;


    $scope.closeModal = function () {
        Popeye.closeCurrentModal();
    };

});
