/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("loyaltyManagementCtrl", ['$location', '$scope', 'AppUtil', '$rootScope', '$http', function ($location, $scope, AppUtil, $rootScope, $http) {

    $scope.init = function () {
        $scope.searchValue = null;
        $scope.searchByCustomerId = false;
        $scope.searchByOrderId = false;
        $scope.searchByGeneratedOrderId = false;
        $scope.loyaltyEventsData = {};

    }

    $scope.getLoyaltyEvents = function (searchType, limit) {
        $rootScope.showFullScreenLoader = true;
        if (searchType === "ORDER_ID") {
            $scope.searchByOrderId = true;
        }
        else if (searchType === "CUSTOMER_ID") {
            $scope.searchByCustomerId = true;
        }
        else if (searchType === "GENERATED_ORDER_ID") {
            $scope.searchByGeneratedOrderId = true;
        }
        var isNumber = $scope.searchValue.match("[0-9]+");
        if (!isNumber) {
            alert("Please enter correct Customer Id !!!!");
            $rootScope.showFullScreenLoader = false;
        }
        else {
            $scope.getApiForLoyaltyEvents(searchType, limit);
        }
    }

    $scope.getApiForLoyaltyEvents = function (searchType, limit) {
        var urlString = urlString = AppUtil.restUrls.orderManagement.getLoyaltyEvents + "?id=" + $scope.searchValue + "&limit=" + limit + "&searchType=" + searchType;
        $http({
            method: 'GET',
            url: urlString
        }).then(function success(response) {
            if (response.status === 200) {
                $scope.loyaltyEventsData = response.data;
                for (var i = 0; i < $scope.loyaltyEventsData.length; i++) {
                    $scope.loyaltyEventsData[i].transactionTime = AppUtil.formatDate($scope.loyaltyEventsData[i].transactionTime, "yyyy-MM-dd hh:mm:ss");
                }
                $rootScope.showFullScreenLoader = false;
            }
            else {
                alert("No data found");
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
        });
    }

    $scope.getMoreData = function () {
        if ($scope.searchByCustomerId) {
            $scope.getApiForLoyaltyEvents("CUSTOMER_ID", 20);
        }
        else if ($scope.searchByOrderId) {
            $scope.getApiForLoyaltyEvents("ORDER_ID", 20);
        }
        else if ($scope.searchByGeneratedOrderId) {
            $scope.getApiForLoyaltyEvents("GENERATED_ORDER_ID", 20);
        }
    }


}]);