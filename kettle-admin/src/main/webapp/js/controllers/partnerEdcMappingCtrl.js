/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("partnerEdcMappingCtrl", function ($scope, $rootScope, $http, AppUtil) {

  $scope.init = function (){

    $scope.unitlist=[];
    $scope.selectedUnit = null;
    $scope.statusList=["ACTIVE", "IN_ACTIVE"];
    $scope.edcMappingStatus = null;
    $scope.partnerName=null;
    $scope.merchantId=null;
    $scope.terminalId=null;
    $scope.merchantKey=null;
    $scope.tId=null;
    $scope.secretKey=null;
    $scope.version=null;
    $scope.partnerEdcMapperList=[]
    $scope.newPartnerEdcMapperList=[]
}

$scope.toggelEditMode = function () {
  $scope.editMode = !$scope.editMode;
  if($scope.editMode==true)
  {
    $scope.statusList=["ACTIVE", "IN_ACTIVE","All"];
  }
  else{

    $scope.statusList=["ACTIVE", "IN_ACTIVE"];

  }

}

$scope.getPartnerEdcMapperList = function () {
  $scope.newPartnerEdcMapperList=[]
  $rootScope.showFullScreenLoader = true;
  $http({
    method: 'GET',
    url: AppUtil.restUrls.unitMetaData.getPartnerEdcMapperList,
    params: {
      "status": $scope.edcMappingStatus
  }
  }).then(function success(response) {

    $rootScope.showFullScreenLoader = false;
    if(response.data!=null)
    {
      $scope.showList=!$scope.showList;
       $scope.partnerEdcMapperList=response.data;
    }
  },function error(response) {
    $rootScope.showFullScreenLoader = false;
    console.log("error:" + response);
    bootbox.alert("Something Went Wrong Try Again!");

  });
}

 $scope.changeStatus = function(partnerEdcMapper){
  if(partnerEdcMapper.status=="ACTIVE")
  {
    partnerEdcMapper.status="IN_ACTIVE"
  }
  else{
    partnerEdcMapper.status="ACTIVE"
  }
  partnerEdcMapper.unitBasicDetail={};
  partnerEdcMapper.edcMappingStatus=partnerEdcMapper.status;
  partnerEdcMapper.unitBasicDetail.id=partnerEdcMapper.unitId;
  $scope.newPartnerEdcMapperList.push(partnerEdcMapper);
 }


 $scope.selectCafesMapping = function (){
  if($scope.edcMappingStatus==null)
  {
    bootbox.alert("Please Select Status First")
    return;
  }
  $("#unitpartnerEdcModal").modal("show");
 }

  $http({
    method: 'GET',
    url: AppUtil.restUrls.unitMetaData.allUnits,
    params: {
        category: 'CAFE'
    }
  }).then(function success(response){
    $rootScope.showFullScreenLoader = false;
    $scope.cafelist = response.data;
    $scope.unitlist = $scope.cafelist;
  }, function error(response) {
    $rootScope.showFullScreenLoader = false;
    console.log("error:" + response);
  });

  $scope.storeSelectedCafes=[];
  $scope.multiSelectSettingsCafes = {
      showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
      scrollableHeight: '250px',trackBy:'id',clearSearchOnClose: true
  };


 $scope.savePartnerEdcMapping = function () {


    if($scope.edcMappingStatus == null || $scope.edcMappingStatus.trim().length ==0){
      bootbox.alert("Invalid Status")
      return;
    }
    if($scope.selectedUnit == null){
      bootbox.alert("Please Select Cafe")
      return;
    }
    if($scope.partnerName == null || $scope.partnerName.trim().length ==0){
      bootbox.alert("Invalid Partner Name")
      return;
    }
    if($scope.merchantId == null || $scope.merchantId.trim().length ==0){
      bootbox.alert("Invalid Merchant Id")
      return;
    }
    if($scope.terminalId == null || $scope.terminalId.trim().length ==0){
      bootbox.alert("Invalid Terminal Id")
      return;
    }
    if($scope.merchantKey == null || $scope.merchantKey.trim().length ==0){
      bootbox.alert("Invalid Merchant Key")
      return;
    }
     if($scope.tId == null || $scope.tId.trim().length ==0){
         bootbox.alert("Invalid T_Id")
         return;
     }
    if($scope.version == null || $scope.version == 0){
      bootbox.alert("Invalid Version")
      return;
    }


    $scope.selectedUnit=JSON.parse($scope.selectedUnit)

    var data = {
      "unitBasicDetail": $scope.selectedUnit,
      "edcMappingStatus": $scope.edcMappingStatus,
      "partnerName": $scope.partnerName,
      "merchantId": $scope.merchantId,
      "terminalId": $scope.terminalId,
      "merchantKey": $scope.merchantKey,
      "tid" : $scope.tId,
      "version": $scope.version
    }
    console.log("saving data ::::", data);
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'POST',
      url: AppUtil.restUrls.unitMetaData.addPartnerEdcMapping,
      data: data
    }).then(function success(response) {
      $rootScope.showFullScreenLoader = false;
      if(response.data==true)
      {
         bootbox.alert("Details Saved Successfully")
         $scope.unitlist=[];
         $scope.selectedUnit = null;
         $scope.statusList=["ACTIVE", "IN_ACTIVE"];
         $scope.edcMappingStatus = null;
         $scope.partnerName=null;
         $scope.merchantId=null;
         $scope.terminalId=null;
         $scope.merchantKey=null;
         $scope.tId=null;
         $scope.version=null;
         $scope.partnerEdcMapperList=[]
         $scope.newPartnerEdcMapperList=[]
      }
    },function error(response) {
      $rootScope.showFullScreenLoader = false;
      console.log("error:" + response);
      bootbox.alert("Something Went Wrong Try Again!");

    });


  };

  $scope.updatePartnerEdcMapperList = function() {
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'POST',
      url: AppUtil.restUrls.unitMetaData.updatePartnerEdcMapperList,
      data: $scope.newPartnerEdcMapperList
    }).then(function success(response) {
      $rootScope.showFullScreenLoader = false;
      if(response.data==true)
      {
         bootbox.alert("Details Saved Successfully")
          $scope.unitlist=[];
          $scope.selectedUnit = null;
          $scope.statusList=["ACTIVE", "IN_ACTIVE","All"];
          $scope.edcMappingStatus = null;
          $scope.partnerName=null;
          $scope.merchantId=null;
          $scope.terminalId=null;
          $scope.merchantKey=null;
          $scope.tId=null;
          $scope.version=null;

          $scope.partnerEdcMapperList=[]
          $scope.newPartnerEdcMapperList=[]

      }
      else{
        bootbox.alert("Something Went Wrong Try Again!");
      }
    },function error(response) {
      $rootScope.showFullScreenLoader = false;
      console.log("error:" + response);
      bootbox.alert("Something Went Wrong Try Again!");

    });


  };

});
