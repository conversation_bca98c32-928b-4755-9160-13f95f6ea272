/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("OrderManagementController",['$location', '$scope', 'AppUtil', '$rootScope', '$http', function ($location, $scope, AppUtil, $rootScope, $http) {

    $scope.init = function (){
        $scope.statusList = ["CANCEL_ORDER","SETTLE_ORDER"];
        $scope.userSessionData = AppUtil.getUserData();
        $scope.reasonList = [];
        $scope.orderId = null;
        $scope.orderDetail = [];
        $scope.selectedStatus = null;
        $scope.transactionalReasonMetaData = {};
        $scope.userId = null;
        $scope.pwd = null;
        $scope.emp = AppUtil.getCurrentUser();
        $scope.selectedReason = null;
        $scope.cancelOrderReason = null;
        $scope.userVerifiedForCancelation = false;
        $("#adminOrderStatusUpdateModal").modal("hide");
        getTransactionalMetaData();
        $scope.isOrderDineInChaayosSelect = false;
    }


    function getTransactionalMetaData(){
        $http({
            method: 'POST',
            url: AppUtil.restUrls.posMetaData.getCancellationalMetaData,
            data : 'CAFE'
        }).then(function success(response) {
            $scope.transactionalReasonMetaData = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    $scope.getOrderByGeneratedOrderId = function () {
        $scope.orderDetail = [];
        $rootScope.showFullScreenLoader  = true;
        var isNumber = $scope.orderId.match("[0-9]+");
        if (!isNumber) {
            alert("Please enter correct order Id");
            $rootScope.showFullScreenLoader  = false;
        }
        else {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.orderManagement.getOrderByOrderId,
                data: $scope.orderId
            }).then(function success(response) {
                if(response.status === 200) {
                    response.data.billCreationTime = AppUtil.formatDate(new Date(response.data.billCreationTime).toDateString(), "yyyy-MM-dd");
                    $scope.orderDetail.push(response.data);
                    $rootScope.showFullScreenLoader = false;
                    if($scope.orderDetail[0].channelPartner === 21){
                       checkForDineInSelect();
                    }
                }
                else{
                    alert("No data found for this order Id");
                    $rootScope.showFullScreenLoader  = false;
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader  = false;
            });
        }
    }


    $scope.openCancelOrderModal = function (){
        console.log($scope.selectedStatus);
        $("#adminOrderCancelUpdateModal").modal("show");
    }

    $scope.openSettleOrderModal = function () {
        $("#adminOrderSettleModal").modal("show");
    }

    $scope.updateOrderStatusByAdmin = function () {
        $rootScope.showFullScreenLoader   = true;
        if ($scope.selectedReason === null) {
            updateOrderSettleStatus();
        } else if ($scope.selectedReason != null) {
            cancelOrder();
        } else {
            alert("Error while updating order status");
            $rootScope.showFullScreenLoader   = false;
        }
    }

    $scope.cancelOrderSettleCancel = function (){
        $scope.pwd = null;
        $("#adminOrderSettleModal").modal("hide");
    }

    $scope.checkUserForCancellation = function (){
        console.log($scope.selectedReason);
        $rootScope.showFullScreenLoader   = true;
        $http({
            method: "POST",
            url: AppUtil.restUrls.users.verifyUserForCancellation,
            data: {
                "empId" : $scope.userId,
                "passcode" : $scope.pwd
            }
        })
            .then(function (response) {
                if (response.data) {
                    $scope.userVerifiedForCancelation = response.data;
                    if ($scope.userVerifiedForCancelation) {
                        $scope.updateOrderStatusByAdmin();
                    } else {
                        alert("Incorrect userId or password");
                        $("#adminOrderCancellationModal").modal("show");
                    }
                } else {
                    alert("User not verified");
                    $rootScope.showFullScreenLoader = false;
                }
            }, function (err) {
                console.log("Error in verifying user :::: {} ", err);
            });
    }

    $scope.initCancellation = function(){
        var category = $scope.orderDetail.settlements[0].modeDetail.category;
        if (AppUtil.isCOD()){
            $scope.refund = false;
            $scope.isOnlineOrder = (category === "ONLINE");
        }else{
            $scope.showRefundMessage = (category === "ONLINE");
        }
    };

    $scope.setRefund = function(refund){
        $scope.refund=refund;
    };

    $scope.cancelOrderCancel = function () {
        $scope.pwd = null;
        $scope.selectedReason = null;
        $scope.cancelOrderReason = null;
        $("#adminOrderCancelUpdateModal").modal("hide");
    };
    
    $scope.resetPrintCount = function(order) {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.orderManagement.resetPrintCount,
            params: {orderId: order.orderId, employeeId: $scope.emp.id}
        }).then(function success(response) {
            if(response.data && response.status === 200) {
                order.printCount = 0;
                alert("Print count reset to 0");
            }
            else{
                alert("Error in resetting print count");
            }
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    function cancelOrder(){
        if($scope.isOrderDineInChaayosSelect){
            alert("Order cannot be cancelled since it a Dine In (Chaayos-select) order");
            $("#adminOrderCancelUpdateModal").modal("hide");
            $rootScope.showFullScreenLoader = false;
            return;
        }
        if($scope.orderDetail[0].status === 'CANCELLED'){
            alert("Order already cancelled");
            $("#adminOrderCancelUpdateModal").modal("hide");
            $rootScope.showFullScreenLoader = false;
            return;
        }
        if($scope.orderDetail[0].orderType == "paid-employee-meal" || $scope.orderDetail[0].orderType == "PAID-EMPLOYEE-MEAL"){
            alert("Order cannot be cancelled since it is an employee meal order");
            $("#adminOrderCancelUpdateModal").modal("hide");
            $rootScope.showFullScreenLoader = false;
            return;
        }
        if($scope.orderDetail[0].orderType === "complimentary-order"){
            alert("Order cannot be cancelled since it is a complimentary order");
            $("#adminOrderCancelUpdateModal").modal("hide");
            $rootScope.showFullScreenLoader = false;
            return;
        }
        var wastageType ;
        if($scope.selectedReason.noWastage && !$scope.selectedReason.completeWastage){
            wastageType = 'NO_WASTAGE';
        }else if(!$scope.selectedReason.noWastage && $scope.selectedReason.completeWastage){
            wastageType = 'COMPLETE_WASTAGE';
        }else{
            wastageType = 'COMPLETE_WASTAGE';
        }
        $scope.cancelOrderReason = $scope.cancelOrderReason + ":Approved by " + $scope.userId;
        var requests = {
            generatedOrderId: $scope.orderId,
            orderId: $scope.orderDetail[0].orderId,
            approvedBy: $scope.userId,
            reason: $scope.cancelOrderReason,
            unitId: $scope.orderDetail[0].unitId,
            channelPartner: $scope.orderDetail[0].channelPartner,
            orderSource: $scope.orderDetail[0].source,
            reasonId: $scope.selectedReason.id,
            wastageType: wastageType,
            //noTimeConstraint: $scope.forceCancel === true
        };

        if (AppUtil.isCOD() || AppUtil.isTakeaway()) {
            requests['orderId'] = $scope.orderDetail[0].orderId;
            requests['category'] = $scope.orderDetail[0].source;
            requests['unitCategory'] = AppUtil.unitFamily;
            requests['orderStatus'] = "CANCELLED";
            requests['unitId'] = $scope.orderDetail[0].unitId;
            if ($scope.refund !== undefined) {
                requests['refund'] = $scope.refund;
            }
        }

        var reqObj = AppUtil.GetRequest(requests);
        console.log("cancellation object");
        console.log(reqObj);

        $http({
            method: 'POST',
            url: AppUtil.restUrls.orderManagement.cancelOrder + "?isAdminRequest=true",
            data: reqObj
        }).then(function (response) {
            if (response === undefined) {
                $rootScope.showFullScreenLoader  = false;
                alert("There was an error while cancelling the order");
                return;
            }
            if (response.data.errorType != undefined) {
                alert(response.data.errorMessage);
                $rootScope.showFullScreenLoader  = false;
            }
            else {
                if (response.data) {
                    console.log("cancellation response :::", response);
                    cancelChannelPartnerOrder();
                    $rootScope.showFullScreenLoader  = false;
                } else {
                    $rootScope.showFullScreenLoader  = false;
                    alert("Order can not be cancelled");
                }
            }
        }, function (err) {
            $rootScope.showFullScreenLoader  = false;
            alert(err.data.errorMessage);
        });
    }

    function cancelChannelPartnerOrder() {
        if($scope.orderDetail.channelPartner === 6){
            $http({
                method: 'POST',
                url: AppUtil.restUrls.partnerOrder.markCancelled + "?kettleOrderId=" + $scope.orderDetail.orderId,
            }).then(function (response) {
                if (response !== undefined ) {
                    if (response.data) {
                        alert("Order Cancelled Successfully");
                        $rootScope.showFullScreenLoader  = false;
                        $("#adminOrderStatusUpdateModal").modal("hide");
                        window.location.reload();
                    } else {
                        alert("Order can not be cancelled");
                    }
                }
            }, function (err) {
                console.log('Error in getting response', err);
                $rootScope.showFullScreenLoader  = false;
                alert("Order cannot be cancelled");
            });
        } else {
            $rootScope.showFullScreenLoader  = false;
            $("#adminOrderStatusUpdateModal").modal("hide");
            alert("Order cancelled successfully");
            window.location.reload();
        }
    }

    function updateOrderSettleStatus(){
        if($scope.orderDetail[0].status === "CANCELLED" || $scope.orderDetail[0].status === "CANCELLED_REQUESTED"){
            alert("Order already cancelled");
            $("#adminOrderSettleModal").modal("hide");
            $rootScope.showFullScreenLoader = false;
            return;
        }
        if($scope.orderDetail[0].status === "SETTLED"){
            alert("Order already settled");
            $("#adminOrderSettleModal").modal("hide");
            $rootScope.showFullScreenLoader = false;
            return;
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.orderManagement.updateOrderStatus,
            data : $scope.orderId
        }).then(function (response) {
            if(response.status === 200 && response.data > 0){
                $("#adminOrderStatusUpdateModal").modal("hide");
                $rootScope.showFullScreenLoader   = false;
                alert("Order Status updated successfully");
                window.location.reload();
            }
        }, function (err) {
            console.log('Error in updating status of order with generated order id ::: {}',$scope.orderId, err);
        });
    }

    function checkForDineInSelect(){
        var orderItem = $scope.orderDetail[0].orders;
        for(var i=0; i < orderItem.length ; i++){
            if(orderItem[i]['productId'] === 1000064){
                $scope.isOrderDineInChaayosSelect = true;
                break;
            }
        }
    }


}]);