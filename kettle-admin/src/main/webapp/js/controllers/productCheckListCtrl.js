/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("productCheckListCtrl", function ($scope, $http, $rootScope, AppUtil, $timeout) {
    $scope.init = function () {
        $rootScope.enableScreenFilter = true;
        $scope.getUnitsData();
        $scope.getAllDimensions();
        $scope.getBrands();
        $scope.getCitiesByZone();
        $scope.getProductsData();
        $scope.currentDate = AppUtil.getCurrentFormattedDate(new Date(), "yyyy-MM-dd");
        $scope.newItem = {
            product: null,
            dimensions: [],
            units: [],
            brandId: null,
            liveDate: null
        };
        $scope.eventId = null;
    }

    $scope.productsData = [];
    $scope.unitsData = [];
    $scope.dimensions = [];
    $scope.brands = [];
    $scope.checklistItems = [];
    $scope.dimensionsForProduct = [];
    $scope.selectedItem = null;
    $scope.storeSelectedRegion = [];
    $scope.selectedCities = [];
    $scope.selectedRegions = [];
    $scope.filteredCities = [];
    $scope.filteredUnits = [];
    $scope.multiSelectSettingsForUnits = {
        showEnableSearchButton: true,
        template: '<b>{{option.id}} - {{option.name}}</b>',
        scrollable: true,
        scrollableHeight: '300px'
    };
    $scope.multiSelectSettingsForRegion = {
        showEnableSearchButton: true, 
        template: '<b> {{option}}</b>', 
        scrollable: true,
        scrollableHeight: '300px'
    };
    $scope.multiSelectSettingsForCities = {
        showEnableSearchButton: true, 
        template: '<b> {{option.value}}</b>', 
        scrollable: true,
        scrollableHeight: '300px'
    };

    $scope.getProductsData = function () {
        $scope.toggleLoader(true);
        $http({
            url: AppUtil.restUrls.productMetaData.products,
            dataType: 'json',
            method: 'POST',
            data: '',
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            $scope.productsData = response.data;
            $scope.toggleLoader(false);
        }, function error(response) {
            console.log("Error while fetching products data: ", response);
            $scope.toggleLoader(false);
        });
    }

    $scope.getUnitsData = function () {
        // $scope.toggleLoader(true);
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + "?category=CAFE"
        }).then(function success(response) {
            $scope.unitsData = response.data.filter(unit => unit.status === "ACTIVE");
            // $scope.toggleLoader(false);
        }, function error(response) {
            console.log("Error while fetching units data: " + response);
            // $scope.toggleLoader(false);
        });
    }

    $scope.getAllDimensions = function () {
        // $scope.toggleLoader(true);
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.listTypes
        }).then(function success(response) {
            $scope.dimensions = response.data.DIMENSION;
            // $scope.toggleLoader(false);
        }, function error(response) {
            console.log("Error while fetching list types: ", response);
            // $scope.toggleLoader(false);
        })
    }

    $scope.getBrands = function () {
        $http({
            method: "GET",
            url: AppUtil.restUrls.brandMetaData.getAllBrands 
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.brands = response.data;
            } else {
                console.log("Unable to Fetch Brands");
            }
        });
    }

    $scope.addCheckListItem = function (item) {
        if ($scope.checkForEmptyItem(item)) {
            alert("Kindly Provide All Details.");
            return;
        }
        if ($scope.checkForExistingProduct(item)) {
            alert("This Product Already Exists in Check List.");
            return;
        }
        $scope.checklistItems.push(angular.copy($scope.newItem));
        $scope.resetAfterAdding();
    };

    $scope.editCheckListItem = function (item, index) {
        $scope.isEditing = true;
        $scope.editIndex = index;
        $scope.newItem = angular.copy(item);
        $scope.newItem.units = angular.copy(item.units);
    };

    $scope.removeCheckListItem = function (index) {
        $scope.checklistItems.splice(index, 1);
    }

    $scope.resetNewItem = function () {
        $scope.newItem = {
            product: null,
            dimensions: [],
            units: [],
            brandId: null,
            liveDate: null
        };
        $scope.filteredUnits = [];
        $timeout(function () {
            angular.element('select[data-ui-select2]').trigger('change');
        });
    };

    $scope.resetAfterAdding = function () {
        $scope.newItem = {
            ...$scope.newItem,
            product: null,
            dimensions: [],
        };
        $timeout(function () {
            angular.element('select[data-ui-select2]').trigger('change');
        });
    }

    $scope.checkForEmptyItem = function (item) {
        if (item.product == null || $scope.getLength(item.dimensions) <= 0 || 
            $scope.getLength(item.units) <= 0 || item.liveDate == null || item.brandId == null) {
            return true;
        }
        return false;
    }

    $scope.onChangeProductName = function (product) {
        $scope.dimensions.forEach((dimension) => {
            if (dimension.detail.id === product.dimensionProfileId) {
                $scope.dimensionsForProduct = dimension.content;
                return;
            }
        });
    }

    $scope.showUnitsModal = function (item) {
        $scope.selectedItem = item;
        $("#unitsModal").modal("show");
    }

    $scope.showDimensionsModal = function (item) {
        $scope.selectedItem = item;
        $("#dimensionsModal").modal("show");
    }

    $scope.checkForExistingProduct = function (item) {
        var found = false;
        $scope.checklistItems.forEach((listItem) => {
            if (item.product.id === listItem.product.id) {
                listItem.dimensions.forEach(dimension => {
                    item.dimensions.forEach(dim => {
                        if (dimension.id === dim.id) {
                            found = true;
                        }
                    });
                });
            }
        });
        return found;
    }

    $scope.postCheckList = function () {
        var formattedCheckList = $scope.formatCheckListForPosting();

        if ($scope.getLength(formattedCheckList) <= 0) {
            alert("Product Checklist Cannot be Empty!");
            return;
        }

        $http({
            url: AppUtil.restUrls.productMetaData.postProductCheckList,
            method: 'POST',
            data: formattedCheckList
        }).then(function success(response) {
            if (response != undefined && response != null) {
                $scope.eventId = response.data;
                $scope.getCheckList();
                bootbox.alert("Product Checklist Posted Successfully!");
            }
        }, function error() {
            bootbox.alert("Product CheckList Posting Failed!");
        });
        // $scope.checklistItems = [];
    }

    $scope.getCheckList = function () {
        $scope.toggleLoader(true);
        if ($scope.eventId === null) {
            alert("No Checklist Posted!");
            $scope.toggleLoader(false);
            return;
        }
        $http({
            url: AppUtil.restUrls.productMetaData.getProductCheckList,
            method: 'GET',
            params: {
                eventId: $scope.eventId
            }
        }).then(function success(response) {
            $scope.checkListEvent = response.data;
            $scope.toggleLoader(false);
        }, function error() {
            alert("Error while getting checklist!");
            $scope.toggleLoader(false);
        });
    }

    $scope.downloadCheckList = function () {
        var fileUrl = $scope.checkListEvent.fileLink;
        var fileName = "PRODUCT_CHECKLIST_" + AppUtil.getCurrentFormattedDate(Date.now(), 'yyyy-MM-dd') + ".xlsx";
    
        if (fileUrl) {
            fetch(fileUrl)
                .then(response => response.blob())
                .then(blob => {
                    var blobUrl = URL.createObjectURL(blob);
                    var a = document.createElement('a');
                    a.href = blobUrl;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(blobUrl);
                })
                .catch(error => {
                    console.error("Error downloading file:", error);
                    alert("Failed to download file!");
                });
        } else {
            alert("File not available!");
        }
    };
    
    $scope.clearCheckList = function () {
        $scope.checklistItems = [];
    }

    $scope.formatCheckListForPosting = function () {
        var formattedCheckList = [];

        $scope.checklistItems.forEach(item => {
            item.liveDate = new Date(item.liveDate);
            var trimmedUnits = item.units.map(unit => ({
                id: unit.id,
                name: unit.name
            }));
            item.dimensions.forEach(dimension => {
                var tempItem = { 
                    ...item, 
                    dimension: dimension, 
                    units: trimmedUnits
                };
                delete tempItem.dimensions;
                formattedCheckList.push(tempItem);
            });
        });

        // $scope.checklistItems = formattedCheckList;
        return formattedCheckList;
    };
    

    $scope.getLength = function (param) {
        return param.length;
    }

    $scope.toggleLoader = function (val) {
        $rootScope.showFullScreenLoader = val;
    }

    $scope.onChangeBrandId = function () {
        $scope.filteredProductListBrandWise = $scope.productsData.filter(product => {
            return product.brandId === $scope.newItem.brandId;
        });
        $scope.newItem.units = [];
        $scope.filterUnits();
    }

    $scope.getCitiesByZone = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.locationByZone
        }).then(function success(response) {
            $scope.cities = response.data;
            $scope.trimmedRegions = Object.keys($scope.cities);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.onRegionChange = function () {
        $scope.filteredCities = [];
        if ($scope.selectedRegions.length === 0) {
            for (var i = 0; i < $scope.cities.size; i++) {
                $scope.filteredCities = $scope.filteredCities.concat($scope.cities[i]);
            }
        }
        $scope.selectedRegions.forEach(region => {
            $scope.filteredCities = $scope.filteredCities.concat($scope.cities[region]);
        });
        $scope.selectedCities = $scope.selectedCities.filter(function (item) {
            return $scope.filteredCities.includes(item);
        });
        $scope.filterUnits();
    };

    $scope.onCityChange = function () {
        $scope.filterUnits();
    };

    $scope.filterUnits = function () {
        let filteredByBrand = $scope.unitsData.filter(cafe => {
            return cafe.companyId === AppUtil.COMPANY_IDS[AppUtil.BRAND_IDS[$scope.newItem.brandId]];
        });

        if ($scope.selectedRegions.length === 0) {
            // if ($scope.selectedCities.length === 0) {
            //     $scope.filteredUnits = filteredByBrand;
            // } else {
            //     $scope.filteredUnits = filteredByBrand.filter(unit => {
            //         return $scope.selectedCities.some(city => city.value === unit.city);
            //     });
            // }
            $scope.filteredUnits = filteredByBrand;
        } else {
            if ($scope.selectedCities.length === 0) {
                $scope.filteredUnits = filteredByBrand.filter(unit => {
                    return $scope.selectedRegions.includes(unit.unitZone);
                });
            } else {
                $scope.filteredUnits = filteredByBrand.filter(unit => {
                    return $scope.selectedCities.some(city => city.value === unit.city);
                });
            }
        }
    };

    $scope.clearFilter = function () {
        $scope.selectedRegions = [];
        $scope.selectedCities = [];
        $scope.filterUnits();
    }
    
});