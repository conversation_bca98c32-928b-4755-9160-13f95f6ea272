adminapp.controller("DroolForOfferCtrl",['$scope', 'AppUtil', '$http', '$stateParams', 'fileService', '$cookieStore', '$rootScope',
    function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

        console.log($stateParams);
        $scope.campaignDetail = $stateParams.campaignDetail;

        $scope.init = function () {
            // $scope.getAllFilesDetail();
            $scope.allFileDetail=[];
            $scope.fileToUpload=null;
            $scope.droolFileTypes=["OFFER_DECISION","RECOM_OFFER_DECISION","WALLET_RECOMMENDATION","LOYALTY_SCREEN_DECISION",
            "CRM_SCREEN_FLOW_DECISION","WALLET_DECISION","DENOMINATION_DECISION","CUSTOMER_ENGAGEMENT_DROOL_DECISION","FREE_PRODUCT_DROOL_DECISION","CUSTOMER_RECOM_DROOL_DECISION","MEMBERSHIP_SUGGESTION_DROOL_DECISION", 
            "CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"];
            $scope.selectedDroolFileType="OFFER_DECISION";
        };

        $scope.getAllFilesDetail=function (){
            $rootScope.showFullScreenLoader = true;
            var urlString = "";
            if($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" ||$scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION"|| $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
                || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_RECOM_DROOL_DECISION"|| $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION" || 
                ["CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"].includes($scope.selectedDroolFileType)) {
               urlString = AppUtil.restUrls.droolsCrmFileManagement.getAllDroolFileForType+"?fileType="+$scope.selectedDroolFileType;
            }else if($scope.selectedDroolFileType === "WALLET_DECISION" ||$scope.selectedDroolFileType === "DENOMINATION_DECISION"){
               urlString = AppUtil.restUrls.droolsTransactionManagement.getAllDroolFileForType+"?fileType="+$scope.selectedDroolFileType;
            }else{
                urlString = AppUtil.restUrls.droolsFileManagement.getAllDroolFileForType+"?fileType="+$scope.selectedDroolFileType;
            }
            $http({
                method: 'GET',
                url: urlString,
            }).then(function success(response) {
                $scope.allFileDetail = response.data;
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                window.alert("Error while fetching files detail")
                console.log("error: " + response);
            });
        }
        $scope.activateFile=function (index){
            if($scope.allFileDetail[index].status === "ACTIVE"){
                window.alert("Already Active")
                return ;
            }
            $rootScope.showFullScreenLoader = true;
            var urlString = "";
                var version = $scope.allFileDetail[index].version;
               if($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" ||$scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
                    || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_RECOM_DROOL_DECISION"|| $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION" || 
                    ["CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"].includes($scope.selectedDroolFileType)){
                  urlString = AppUtil.restUrls.droolsCrmFileManagement.activateDroolFile+"/"+$scope.selectedDroolFileType+"?fileName="+$scope.allFileDetail[index].fileName + "&version=" + version;
               }else if($scope.selectedDroolFileType === "WALLET_DECISION" ||$scope.selectedDroolFileType === "DENOMINATION_DECISION"){
                   urlString = AppUtil.restUrls.droolsTransactionManagement.activateDroolFile+"/"+$scope.selectedDroolFileType+"?fileName="+$scope.allFileDetail[index].fileName + "&version=" + version;
               }else{
                  urlString = AppUtil.restUrls.droolsFileManagement.activateDroolFile+"/"+$scope.selectedDroolFileType+"?fileName="+$scope.allFileDetail[index].fileName + "&version=" + version;
            }
            $http({
                method: 'GET',
                url: urlString,
            }).then(function success(response) {
                if(response.data === true){
                    for(var i in $scope.allFileDetail){
                        if($scope.allFileDetail[i].status != "PROCESSING"){
                            $scope.allFileDetail[i].status="IN_ACTIVE";
                        }
                    }
                    $scope.allFileDetail[index].status="ACTIVE";
                }
                $rootScope.showFullScreenLoader = false;
                $scope.getAllFilesDetail();
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                window.alert("Error while activating  file ")
                console.log("error: " + response);
            });
        }

        $scope.downloadFile=function (index){
            $rootScope.showFullScreenLoader = true;
            var urlString = "";
            var version = $scope.allFileDetail[index].version;
            if($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" ||$scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
                || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_RECOM_DROOL_DECISION"|| $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION" || 
                ["CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"].includes($scope.selectedDroolFileType)){
                    urlString = AppUtil.restUrls.droolsCrmFileManagement.downLoadDroolFile+"/"+$scope.selectedDroolFileType+"?fileName="+$scope.allFileDetail[index].fileName + "&version=" + version;
            }else if($scope.selectedDroolFileType === "WALLET_DECISION" ||$scope.selectedDroolFileType === "DENOMINATION_DECISION"){
                    urlString = AppUtil.restUrls.droolsTransactionManagement.downLoadDroolFile+"/"+$scope.selectedDroolFileType+"?fileName="+$scope.allFileDetail[index].fileName + "&version=" + version;
            }else{
                    urlString = AppUtil.restUrls.droolsFileManagement.downLoadDroolFile+"/"+$scope.selectedDroolFileType+"?fileName="+$scope.allFileDetail[index].fileName + "&version=" + version;
            }
            $http({
                method: 'GET',
                url: urlString,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = $scope.allFileDetail[index].fileName;
                var blob = new Blob(
                    [response.data],
                    {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }, fileName);
                saveAs(blob, fileName);
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                window.alert("Error while downloading  file ")
            });
        }

        $scope.addNewVersion=function (){
            if (fileService.getFile() == null || fileService.getFile() == undefined ) {
                bootbox.alert("Please select a .xls file");
                return;
            }
            console.log("File is",fileService.getFile());
            var fd = new FormData();
            fd.append("file", fileService.getFile());
            $rootScope.showFullScreenLoader = true;
            var urlString = "";
            if($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" ||$scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
                    || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_RECOM_DROOL_DECISION"|| $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION" || 
                    ["CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"].includes($scope.selectedDroolFileType)){
                 urlString =AppUtil.restUrls.droolsCrmFileManagement.addNewFile+"/"+$scope.selectedDroolFileType;
            }else if($scope.selectedDroolFileType === "WALLET_DECISION" ||$scope.selectedDroolFileType === "DENOMINATION_DECISION"){
                 urlString =AppUtil.restUrls.droolsTransactionManagement.addNewFile+"/"+$scope.selectedDroolFileType;
            }else{
                 urlString = AppUtil.restUrls.droolsFileManagement.addNewFile+"/"+$scope.selectedDroolFileType;
            }
            $http({
                url:urlString,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined
                },
                transformRequest: angular.identity
            }).success(function (response) {
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                var data= response
                console.log(response);
                if(response){
                    bootbox.alert("File uploaded");
                    window.location.reload();
                }
                else{
                    bootbox.alert(response.errorMessage);
                }
                $rootScope.showFullScreenLoader = false;

            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error while uploading Expense Sheet");
            });
        }

        $scope.uploadFile=function (){
            $("#addDroolDecisionFile").modal("show");
        }

        $scope.setAsDefault = function (index) {
            if ($scope.allFileDetail[index].status !== 'ACTIVE') {
                bootbox.alert("Please activate the file first");
                return;
            }
            var version = $scope.allFileDetail[index].version;
            if ($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" || $scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
                || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_RECOM_DROOL_DECISION"|| $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION" || 
                ["CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"].includes($scope.selectedDroolFileType)) {
                urlString = AppUtil.restUrls.droolsCrmFileManagement.setAsDefault + "/" + $scope.selectedDroolFileType + "?version=" + version + "&fileName=" + $scope.allFileDetail[index].fileName ;
            } else if ($scope.selectedDroolFileType === "WALLET_DECISION" || $scope.selectedDroolFileType === "DENOMINATION_DECISION") {
                urlString = AppUtil.restUrls.droolsTransactionManagement.setAsDefault + "/" + $scope.selectedDroolFileType + "?version=" + version + "&fileName=" + $scope.allFileDetail[index].fileName ;
            } else {
                urlString = AppUtil.restUrls.droolsFileManagement.setAsDefault + "/" + $scope.selectedDroolFileType + "?version=" + version + "&fileName=" + $scope.allFileDetail[index].fileName ;
            }
            $http({
                url: urlString,
                method: 'POST',
            }).success(function (response) {
                if (response != undefined && response != null) {
                    window.location.reload();
                }
                else {
                    bootbox.alert(response.errorMessage);
                }
                $rootScope.showFullScreenLoader = false;
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error setting the file as default!!!!");
            });
        }

        $scope.inActivateVersion = function (index) {
            if ($scope.allFileDetail[index].status === 'IN_ACTIVE') {
                bootbox.alert("File is already in-active!!!");
                return;
            }
            if($scope.allFileDetail[index].status == 'ACTIVE' && $scope.allFileDetail[index].isDefault == 'Y'){
                bootbox.alert("Please make another version default before deleting!!!");
                return;
            }
            var version = $scope.allFileDetail[index].version;
            if ($scope.selectedDroolFileType === "LOYALTY_SCREEN_DECISION" || $scope.selectedDroolFileType === "CRM_SCREEN_FLOW_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_ENGAGEMENT_DROOL_DECISION"
                || $scope.selectedDroolFileType === "FREE_PRODUCT_DROOL_DECISION" || $scope.selectedDroolFileType === "CUSTOMER_RECOM_DROOL_DECISION"|| $scope.selectedDroolFileType === "MEMBERSHIP_SUGGESTION_DROOL_DECISION" || 
                ["CART_RULES_DROOL_DECISION", "INPUT_RULES_DROOL_DECISION", "OUTPUT_RULES_DROOL_DECISION"].includes($scope.selectedDroolFileType)) {
                urlString = AppUtil.restUrls.droolsCrmFileManagement.inActivateFile + "/" + $scope.selectedDroolFileType + "?version=" + version + "&fileName=" + $scope.allFileDetail[index].fileName ;
            } else if ($scope.selectedDroolFileType === "WALLET_DECISION" || $scope.selectedDroolFileType === "DENOMINATION_DECISION") {
                urlString = AppUtil.restUrls.droolsTransactionManagement.inActivateFile + "/" + $scope.selectedDroolFileType + "?version=" + version + "&fileName=" + $scope.allFileDetail[index].fileName ;
            } else {
                urlString = AppUtil.restUrls.droolsFileManagement.inActivateFile + "/" + $scope.selectedDroolFileType + "?version=" + version + "&fileName=" + $scope.allFileDetail[index].fileName ;
            }
            $http({
                url: urlString,
                method: 'POST',
            }).success(function (response) {
                if (response != undefined && response != null) {
                    window.location.reload();
                }
                else {
                    bootbox.alert(response.errorMessage);
                }
                $rootScope.showFullScreenLoader = false;
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error in-activating the file !!!!");
            });
        }

    }]);
