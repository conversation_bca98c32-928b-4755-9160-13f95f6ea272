adminapp.controller("productPriceBulkUpdateCtrl", function ($scope, $http, AppUtil, fileService, $rootScope) {

    $scope.init = function () {
        $rootScope.enableScreenFilter = true;
        $scope.prodDetails = {};
        $scope.pricingProfiles = [];
        $scope.pricingProfileNames = [];
        $scope.pricingProfileMap = new Map();
        $scope.getAllProducts();
        $scope.regionList = [];
        $scope.selectedPricingProfiles = [];
        $scope.selectedRegions = [];
        $scope.unitType = ["CAFE", "COD", "EMPLOYEE_MEAL"];
        $scope.selectedProducts = [];
        $scope.productList = [];
        getAllBrands();
        getAllPricingProfile();
        getAllRegions();
    };

    $scope.isUndefinedOrNull = function (val) {
        return angular.isUndefined(val) || val === null || val === "";
    };

    $scope.multiSelectSettings = {
        showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
        scrollableHeight: '200px'
    };

    $scope.multiSelectSettingsForProducts = {
        showEnableSearchButton: true, template: '<b> {{option.name + " : " + option.id + " - " + option.status}}</b>', scrollable: true,
        scrollableHeight: '300px'
    };

    function getAllBrands() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.brandList = response.data;
        });
        $rootScope.showFullScreenLoader = false;
    }

    $scope.getAllProducts = function () {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.productMetaData.products
            }).then(function success(response) {
                $scope.productsInfo = response.data;
                sortProductList();
            }, function error(response) {
                console.log("error:" + response);
            });
     };

     function sortProductList(){
        $scope.productsInfo.sort((a, b) => {
           var fa = a.name.replace(/\s/g,'').toLowerCase();
           var fb = b.name.replace(/\s/g,'').toLowerCase();
           return fa.localeCompare(fb);
        });
     }

    function mapPricingProfiles() {
        for (var i in $scope.pricingProfiles) {
            $scope.pricingProfileNames.push($scope.pricingProfiles[i].name);
            $scope.pricingProfileMap.set($scope.pricingProfiles[i].name, $scope.pricingProfiles[i].id);
        }
    }

    function getAllPricingProfile() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.listTypes
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            var category = response.data.PRICING_PROFILE[0];
            if (angular.equals(category['detail']['name'], "Pricing Profile") && angular.equals(category['detail']['status'], "ACTIVE")) {
                for (var itr in category['content']) {
                    if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                        $scope.pricingProfiles.push(category['content'][itr]);
                    }
                }
            }
            mapPricingProfiles();
        });
        $rootScope.showFullScreenLoader = false;
    }

    function getAllRegions() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.regionList = response.data;
            console.log($scope.regionList);
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.checkProdPriceObj = function () {
        if ($scope.isUndefinedOrNull($scope.prodDetails.unitCategory)) {
            return false;
        }
        if ($scope.isUndefinedOrNull($scope.prodDetails.brandId)) {
            return false;
        }
        if (($scope.isUndefinedOrNull($scope.selectedPricingProfiles) || $scope.selectedPricingProfiles.length < 1) &&
            ($scope.isUndefinedOrNull($scope.selectedRegions) || $scope.selectedRegions.length < 1) &&
            ($scope.isUndefinedOrNull($scope.selectedProducts) || $scope.selectedProducts.length < 1)) {
            return false;
        }
        return true;
    };

    $scope.downloadBulkProductPriceSheet = function () {
        var pricingProfileIds = [];
        for (var prProfile in $scope.selectedPricingProfiles) {
            pricingProfileIds.push($scope.pricingProfileMap.get($scope.selectedPricingProfiles[prProfile]))
        }
        var obj = {};
        obj.unitCategory = $scope.prodDetails.unitCategory;
        obj.brandId = $scope.prodDetails.brandId;
        var pricingProfile = [];
        if(pricingProfileIds.toString().length>0){
           pricingProfile.push(pricingProfileIds.toString());
        }
        obj.pricingProfile = pricingProfile;
        obj.regions = $scope.selectedRegions.toString();
        var ids = [];
        if($scope.selectedProducts !== undefined && $scope.selectedProducts != null && $scope.selectedProducts.length > 0){
           for(var i =0 ;i<$scope.selectedProducts.length;i++){
              ids.push($scope.selectedProducts[i].id.toString());
           }
        }
        obj.productIds = ids;
        $rootScope.showFullScreenLoader = true;
        $http(
            {
                method: 'POST',
                url: AppUtil.restUrls.priceManagement.getUnitProductPriceSheet,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                data: obj
            })
            .then(
                function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != undefined && response != null) {
                        var fileName = obj.unitCategory + " - " + "UNIT_PROD_PRICING_SHEET" + " - " + Date.now() + ".xlsx";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }
                },
                function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                    alert("Unable to download Sheet");
                });
    };

    $scope.uploadBulkProductPriceSheet = function () {
        if (fileService.getFile() == null || fileService.getFile() == undefined) {
            bootbox.alert("Please select a .xlsx file");
            return;
        }
        console.log("File is", fileService.getFile());
        if (confirm("Do you want to Upload and Update Prices?")) {
            var fd = new FormData();
            fd.append("file", fileService.getFile());
            fd.append("updatedBy", $rootScope.userData.id);
            $rootScope.showFullScreenLoader = true;
            $http({
                url: AppUtil.restUrls.priceManagement.updateBulkUnitProductPrice,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined,
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                $scope.productPriceFile = null;
                console.log(response);
                if (response.errorMessage == null) {
                    if (response.totalRecords != null) {
                        var alertMessage = "File Uploaded";
                        alertMessage = alertMessage + "\nTotal Records: " + response.totalRecords +
                            "\nRecords Changed: " + response.totalRecordsChanged +
                            "\nSuccessfully Updated: " + response.totalRecordsUpdatedSuccessfully +
                            "\nRecord with errors: " + response.totalErrorRecords +
                            "\nFailed: " + response.totalFailureRecords;
                        alert(alertMessage);
                    } else {
                        alert("Error in bulk updating prices!");
                    }
                    window.location.reload();
                }
                else {
                    alert(response.errorMessage);
                }

            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error while uploading Unit Product Pricing Sheet");
            });
        }
        ;
    }

});