/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
adminapp.controller("developerDashboardCtrl", function ($scope, $location,$rootScope,$http, AuthService, $cookieStore, AppUtil, AdminApiService, toastService) {

    $scope.init = function () {
        $scope.showKettleCache = false;
        $scope.showDineInCache = false;
        $scope.showSumoCache = false;
        $scope.isKettleButton = true;
        $scope.isDineInButton = true;
        $scope.isSumoButton = true;
        $scope.selectedAction = "Kettle Cache";
        $scope.actionList = ["Kettle Cache", "Dinein Cache", "Sumo Cache", "Neo Cache","Kettle2 Cache"];
        $scope.selectAction($scope.selectedAction);
        $scope.currentUserId = AppUtil.getUserValues().user.id;
        $scope.allUnitsList = [];
        $scope.selectedUnitsForAssetRefresh = [];
        $scope.getAllUnits();
    };

    $scope.getAllUnits = function () {
        $scope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.getAllMasterUnits,
        }).then(function success(response) {
            $scope.showFullScreenLoader = false;
            $scope.allUnitsList = response.data;
        }, function error(response) {
            $scope.showFullScreenLoader = false;
            $scope.allUnitsList = [];
            console.log("error:" + response);
        });
    };

    $scope.clearCache = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.masterCacheManagement[type];
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    };

    $scope.clearCampaignCache = function () {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.campaignCache.clearCampaignCache;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }

    $scope.selectedMilkBreadUnits = [];
    $scope.multiSelectSettingsMilkBread = {
        showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true, showCheckAll: true, showUncheckAll: true,
        scrollableHeight: '250px', trackBy: 'id', clearSearchOnClose: true
    };

    $scope.multiSelectSettingsForAssetRefresh = {
        showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true, showCheckAll: false, showUncheckAll: true,
        scrollableHeight: '250px', trackBy: 'id', clearSearchOnClose: true
    };

    $scope.markMilkBreadCompleteForUnits = function () {
        var url = AppUtil.restUrls.scmCacheManagement.markMilkBreadComplete;
        var unitIds = [];
        angular.forEach($scope.selectedMilkBreadUnits, function (unit) {
            unitIds.push(unit.id);
        });
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: url,
            params: {
                userId : $scope.currentUserId
            },
            data: unitIds
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.selectedMilkBreadUnits = [];
            bootbox.alert(JSON.stringify(response));
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.selectedMilkBreadUnits = [];
            bootbox.alert("error:" + JSON.stringify(response));
        });
    }

    $scope.refreshAssetCacheForUnits = function() {
        var url = AppUtil.restUrls.scmCacheManagement.refreshAssetCacheForUnits;
        var unitIds = [];
        angular.forEach($scope.selectedUnitsForAssetRefresh, function (unit) {
            unitIds.push(unit.id);
        });
        $scope.selectedMilkBreadUnits = [];
        AdminApiService.post(url, unitIds).then(function success(response) {
            toastService.success("Asset cache refreshed successfully for selected units.");
        });
    }

    $scope.clearScmCache = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.scmCacheManagement[type];
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }

    $scope.clearNeoCache = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.neoCacheManagement[type];
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }

    $scope.clearDineInCache = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.cache[type] + AppUtil.companyId;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }

    $scope.clearKettle2CacheWithSource = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.kettle2CacheManagement[type];
                $rootScope.showFullScreenLoader = true;

                $http({
                    method: 'POST',
                    url: url,
                    params: {
                        "partnerId": 1,
                    },
                    headers: {
                        'Content-type': 'application/json',
                        'compid': "1000",
                        'src': "CAFE",
                    }
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }

    $scope.clearKettle2Cache = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.kettle2CacheManagement[type];
                $rootScope.showFullScreenLoader = true;

                $http({
                    method: 'POST',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }


    $scope.clearDineInCacheWithSource = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.cache[type] + AppUtil.companyId + "/" + AppUtil.cafe;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }


    $scope.clearDineInCrm = function (type) {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " " + type + " ?" + '</p>', function (result) {
            if (result == true) {
                var url = AppUtil.restUrls.dineInAppCrm[type] + AppUtil.companyId;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    }

    $scope.clearProductionLinesCache = function () {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " ?" + '</p>', function (result) {
            if (result == true) {
                $rootScope.showFullScreenLoader = false;
                $http({
                    method: 'get',
                    url: AppUtil.restUrls.scmCacheManagement.clearProductionLineCache
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    };

    $scope.clearSkuDefinitionListCache = function () {
        bootbox.confirm('<p style="display:inline-block";>' + "Do you really want to clear&nbsp" + '<div style="color:red;display:inline-block;font-weight:bold;font-size:18px">' + AppUtil.getEnvType().toUpperCase() + '</div>' + " ?" + '</p>', function (result) {
            if (result == true) {
                $rootScope.showFullScreenLoader = false;
                $http({
                    method: 'get',
                    url: AppUtil.restUrls.scmCacheManagement.clearSkuDefinitionListCache
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            } else {
                return;
            }
        });
    };

    $scope.selectAction = function (action) {
        $scope.selectedAction = action;
    };

});
