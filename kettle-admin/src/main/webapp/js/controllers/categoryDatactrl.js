/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("CategoryDataController", function($scope,$http,$window,AppUtil){
	
	$http({
		method: 'GET',
		  url: AppUtil.restUrls.unitMetaData.listTypes
		}).then(function success(response) {
			$scope.categoryDatas = response.data;
			//console.log($scope.categoryDatas);
			$scope.selectedCatData = $scope.categoryDatas[0];
			
			var keys = [];
			$.each($scope.categoryDatas, function(key,val){ keys.push(key); });
			//console.log(keys); 
			$scope.categoryArrList=keys;
			$scope.selectedItem=$scope.categoryArrList[0];
			
			
			//console.log("data=",$scope.selectedCatData)
		}, function error(response) {
			console.log("error:"+response);
	});
	$scope.showCategoryData = function(catData){
		$scope.showCatData=catData;
		//console.log(catData.detail.type);
	}
	
	$scope.editCategoryDetails = function(catID,type){
			$scope.msg 				= 	"Update"
			$scope.action 			= 	"UpdateCatAction"
			$("#categoryModalData").modal("show");
			//console.log($scope.showCatData);
			
			$scope.showCatData.forEach(function(updatedet){
			if(updatedet.detail.id === catID){
			//	console.log("update45--",updatedet.detail);
				$scope.selectedItem	=	updatedet.detail.type;
				$scope.catName		=	updatedet.detail.name;
				$scope.catCode		=	updatedet.detail.code;
				$scope.updateIDS	=	updatedet.detail.id;
				$scope.catStatus	=	updatedet.detail.status;
			}
			});
	}
	$scope.addCategoryData = function(detail){
		$scope.msg = "AddData"
		$scope.action = "AddCatData"
		$scope.catName = null;
		$scope.catCode = null;	
		$("#categoryModalData").modal("show");
	}
	
$scope.submitAddCategoryData = function(nameCat){
	var type=$scope.selectedItem;
		if($scope.catName==null || $scope.catName==""){
			alert("Please Enter Category Name."); 
			return;
		}	
		if($scope.catCode==null || $scope.catCode==""){
			alert("Please Enter Category Code."); 
			return;
		}		var catObj = {
				detail:{
				id:null,
				name:$scope.catName,
				code:$scope.catCode,
				status:"IN_ACTIVE",
				type:type},
				content:null
		};
		
		$scope.selectedObj2	=	catObj;
		console.log($scope.selectedObj2);
		
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.posMetaData.refLookUpUpsert,
			  data: $scope.selectedObj2
			}).then(function success(response){
				if(response.status != 200){
					alert('Invalid');
				}
				else
				$scope.selectedCatDataList=response.data; 
				$('#categoryModalData').modal('hide');
				alert("Category Successfully added");
				window.location.reload()
				}, function error(response) {
				  console.log("error:"+response);
		});
	}

$scope.submitUpdateCategory = function(upDetID,catStatus,type){
	if($scope.catName==null || $scope.catName==""){
		alert("Please Enter Category Name."); 
		return;
	}	
	if($scope.catCode==null || $scope.catCode==""){
		alert("Please Enter Category Code."); 
		return;
	}
	$scope.showCatData.forEach(function(statusUpdate){
	if(statusUpdate.detail.id === upDetID){
	statusUpdate.detail.name=$scope.catName;
	statusUpdate.detail.code=$scope.catCode;
	$scope.selectedObj=statusUpdate;
	//console.log("status=",$scope.selectedObj)
	
	$http({
		  method: 'POST',
		  url: AppUtil.restUrls.posMetaData.refLookUpUpsert,
		  data: $scope.selectedObj
		}).then(function success(response){
			if(response.status != 200){
				alert("Cateogory Not updated");				
			}
			else
				{
				 $('#categoryModalData').modal('hide');
				 }
		}, function error(response) {
			  console.log("error:"+response);
	}); 
	
	}
	});
}
$scope.activeNinactive = function(stausID,catStatus,count){
	//console.log(stausID);
	//console.log(catStatus);
	if ($window.confirm("Are you sure, you want to Active/DeActive Category List?")) {
		//console.log("YES");;
    } else {
    	return false;
    }
	
	$scope.showCatData.forEach(function(statusUpdate){
	if(statusUpdate.detail.id === stausID){
	
	statusUpdate.detail.status=catStatus;
	statusUpdate.detail.id=stausID;
	$scope.selectedObj=statusUpdate;
	//console.log(JSON.stringify($scope.selectedObj));
	//return false;
	$http({
		  method: 'POST',
		  url: AppUtil.restUrls.posMetaData.refLookUpUpsert,
		 //url: 'http://172.16.16.52:8080/kettle-service/rest/v1/pos-metadata/refLookUp/upsert',
		  data: $scope.selectedObj
		}).then(function success(response){
			if(response.status != 200){
				alert("Category Status not updated");				
			}
		}, function error(response) {
			  console.log("error:"+response);
	});
	
	//dataType: 'json',
	//data: creditAccountsObject[0],
	//headers: { 'Content-Type': 'application/json' },
	
	
	}
	});
}
});