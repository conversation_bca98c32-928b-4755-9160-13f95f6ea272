/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("dsrUploadCtrl", function($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location,
		$timeout, AppUtil, fileService) {

	$scope.init = function() {
		$scope.units = [];
		$scope.selectedUnit = null;
		$scope.completedList = [];
		getUnits("CAFE");
	};

	function getUnits(category){
		$http({
			method : 'GET',
			url : AppUtil.restUrls.unitMetaData.allUnits,
			params : {
				"category" : category
			}
		}).then(function success(response) {
			$scope.units = response.data;
		}, function error(response) {
			console.log("error:" + response);
		});
	}

	$scope.startUpload = function() {
		var unitId = $scope.selectedUnit.id;
		var startDate = $scope.startDate;
		var endDate = $scope.endDate;
		$scope.callWithDelay(startDate, endDate, unitId);

	};

	$scope.callWithDelay = function(startDate, endDate, unitId) {
		if (startDate <= endDate) {
			console.log("running...");
			/*
			 * $scope.completedList.push({ "businessDate" : new Date(startDate),
			 * "status" : true }); console.log($scope.completedList);
			 */
			$scope.callUpload(new Date(startDate), unitId);
			startDate.setDate(startDate.getDate() + 1);
			$timeout(function() {
				$scope.callWithDelay(startDate, endDate, unitId)
			}, 6000) //every 6 sec
		}
	};

	$scope.callUpload = function(businessDate, unitId) {

		var url = AppUtil.restUrls.dsrManagement.uploadDsr;
		var year = businessDate.getFullYear() + "";
		var month = businessDate.getMonth() + 1 + "";
		var dayOfMonth = businessDate.getDate() + "";

		var date = year + "";
		date = date + "-" + (month.length < 2 ? "0" + month : month);
		date = date + "-" + (dayOfMonth.lenght < 2 ? "0" + dayOfMonth : dayOfMonth);
		$http({
			method : 'POST',
			url : url,
			params : {
				"businessDate" : date,
				"unitId" : unitId
			}
		}).then(function success(response) {
			console.log(response)
			if (response != undefined && response != null) {
				$scope.completedList.push({
					"businessDate" : new Date(businessDate),
					"status" : response.data
				});
			}
		}, function error(response) {
			console.log("error:" + response);
			alert("error:" + response);
		});

	};
	
	$scope.selectUnit = function(value) {
		$scope.selectedUnit = value;
	};

});
