/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        "recipeBuilder",
        function ($rootScope, $scope, $location, $http, AppUtil, $cookieStore, $uibModal, $window, $state,$timeout) {

            $scope.productsInfo = null;
            $scope.optionsList = null;
            $scope.categoryLists = null;
            $scope.ingredientTypes = null;
            $scope.scmProductsInfo = null;
            $scope.recipeProfiles = null;
            $scope.scmMetadata = null;

            $scope.clearAll = function (askForConfirmation) {
                if (askForConfirmation) {
                    var result = confirm("Are You Sure You Want To Clear Everything?");
                    if (!result) {
                        return;
                    }

                }
                $scope.setSelectedRegion(null);
                $scope.activeTab = 0;
                $scope.activeIngredientTab = 0;
                // Metadata Information
                $scope.todaysDate = new Date().toString();
                $scope.recipeDetail = createRecipeDetail();
                $scope.isComboProduct = false;
                $scope.isQVM = false;
                $scope.isHeroCombo = false;
                $scope.isSuperCombo = false;
                $scope.isBoxCombo = false;
                $scope.isFixedMealProduct = false;
                $scope.disableOptionforHeroCombo = false;
                $scope.item1Check = false
                // Tab 1 Selections
                $("#select2-productSelected-container").html('');
                $scope.product = {};
                $scope.product.selectedProductId = null;
                $scope.selectedProduct = null;
                $scope.existingRecipes = null;
                $scope.selectedDimensionData = null;
                $scope.selectedDimensionProfile = null;
                $scope.clearCloneModal();
                $scope.clearSCMCloneModal();
                $scope.recipeProfileAvailable = [];
                $scope.selectedVariantProductForEdit = {
                    details: []
                };
                $scope.cloneRecipeProfile = false;
                $scope.selectedRecipeId = null;
                $scope.isForApproval = false;
                $scope.allRegions = [];
                $scope.getAllRegions();
            };

            $scope.clearCloneModal = function () {
                $scope.cloneRecipeProfile = false;
                $scope.selectedCloneProductId = null;
                $scope.selectedCloneProduct = null;
                $scope.cloneRecipes = null;
                $scope.selectedCloneDimensionData = null;
                $scope.selectedCloneProfileData = null;
                $scope.selectedCloneDimensionProfile = null;
                $scope.updatedRecipeProfile = null;
            };

            $scope.clearSCMCloneModal = function () {
                $scope.selectedCloneProductId = null;
                $scope.selectedCloneProduct = null;
                $scope.cloneRecipes = null;
            };

            $scope.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.currentUserId = AppUtil.getUserValues().user.id;
                $scope.displayTypes = ["ACTIVE","PROPOSED","COMPARE"];
                $scope.setSelectedDisplay(null);
                $scope.isForApproval = false;
                $scope.getAllDimensions();
                $scope.getAllProducts();
                $scope.getSCMMetadata();
                $scope.getAllSCMProductsForRecipe();
                $scope.getAllSCMProductsWithRecipe();
                $scope.getIngedientTypes();
                $scope.getDispenseTag();
                $scope.getDispenserData();
                $scope.dispenserMapping = [];
                $scope.product = {}
                $scope.dispenserMapping.addOns = [];
                $scope.dispenserMapping.variants = [];
                $scope.dispenserMapping.mandatoryAddons = [];
                $scope.selectedVariantTab = true;
                $scope.variantDispenserDetail = [];
                $scope.addonDispenserDetail = [];
                $scope.mandatoryAddonDispenserDetail = [];
                $scope.heroOrSuperComboCheck = true;
                $scope.selectedRecommendationQuantity = 1;
                $scope.tagList = ["NONE", "Recommended", "Best Seller", "Our Special"];
                $scope.instructionList = [];

                $scope.selectedAllUnits = false;
                $scope.selectedProfileForAllUnits = null;
                $scope.model = {
                    unitPPStatus : null,
                    unitStatus : null
                }
                setInstructionList();
                $scope.clearAll(false);
                getRecipeProfiles();
                getCondimentList();
                AppUtil.getBusinessDate(function (date) {
                    $scope.tomorrow = AppUtil.formatDate(new Date(date).getTime(), 'yyyy-MM-dd');
                    console.log($scope.tomorrow);
                }, 1);
                $scope.setRecipeCondimentInititalValue();

            };

            $scope.setSelectedDisplay = function (display) {
                if ($scope.loadingData) {
                    alert("Please Wait till the data is Loaded for " + $scope.selectedDisplay + " Recipe");
                    return;
                }
                $scope.selectedDisplay = display;
                $scope.loadingData = true;
                if ($scope.selectedDisplay != null) {
                    if ($scope.selectedDisplay === "ACTIVE") {
                        $scope.setCurrentDisplayRecipe('OLD', true);
                    } else if ($scope.selectedDisplay === "PROPOSED") {
                        $scope.setCurrentDisplayRecipe('NEW', true);
                    } else {
                        $scope.setCurrentDisplayRecipe('NEW', true);
                    }
                }
            };

            function getRecipeProfiles() {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.recipeProfiles,
                    params: {type: "NON_SCM"}
                }).then(function success(response) {
                    $scope.recipeProfiles = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.setRecipeCondimentInititalValue = function (){
                $scope.condimentMap = null;
                $scope.dineCondiment = null;
                $scope.codCondiment = null;
                $scope.takeawayCondiment =null;
                $scope.dineQuantity = null;
                $scope.codQuantity = null;
                $scope.takeawayQuantity =null;
                if($scope.recipeDetail.condiments !=undefined && $scope.recipeDetail.condiments !=null){
                    if($scope.recipeDetail.condiments.dineIn !=undefined && $scope.recipeDetail.condiments.dineIn !=null &&$scope.recipeDetail.condiments.dineIn.groupId!=undefined && $scope.recipeDetail.condiments.dineIn.groupId!=null && $scope.condimentCafeList !=undefined && $scope.condimentCafeList!=null){
                        for(var i=0; i<$scope.condimentCafeList.length;i++){
                            if($scope.recipeDetail.condiments.dineIn.groupId == $scope.condimentCafeList[i].groupId){
                                $scope.dineCondiment = $scope.condimentCafeList[i];
                                $scope.dineQuantity = $scope.recipeDetail.condiments.dineIn.quantity;
                            }
                        }
                    }
                    if($scope.recipeDetail.condiments.delivery !=undefined && $scope.recipeDetail.condiments.delivery !=null && $scope.recipeDetail.condiments.delivery.groupId!=undefined && $scope.recipeDetail.condiments.delivery.groupId!=null && $scope.condimentCODList !=undefined && $scope.condimentCODList!=null){
                        for(var i=0; i<$scope.condimentCODList.length;i++){
                            if($scope.recipeDetail.condiments.delivery.groupId == $scope.condimentCODList[i].groupId){
                                $scope.codCondiment = $scope.condimentCODList[i];
                                $scope.codQuantity = $scope.recipeDetail.condiments.delivery.quantity;
                            }
                        }
                    }
                    if($scope.recipeDetail.condiments.takeaway !=undefined && $scope.recipeDetail.condiments.takeaway !=null && $scope.recipeDetail.condiments.takeaway.groupId!=undefined && $scope.recipeDetail.condiments.takeaway.groupId!=null && $scope.condimentTakeAwayList!=undefined && $scope.condimentTakeAwayList!=null){
                        for(var i=0; i<$scope.condimentTakeAwayList.length;i++){
                            if($scope.recipeDetail.condiments.takeaway.groupId == $scope.condimentTakeAwayList[i].groupId){
                                $scope.takeawayCondiment = $scope.condimentTakeAwayList[i];
                                $scope.takeawayQuantity = $scope.recipeDetail.condiments.takeaway.quantity;
                            }
                        }
                    }
                }
            }

            function getCondimentList(){
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.srcToCondimentMap,
                }).then(function success(response) {
                    if(response!=undefined && response!=null){
                        $scope.condimentMap = response.data;
                        $scope.condimentSource=[]
                        for(src in $scope.condimentMap){
                            $scope.condimentSource.push(src);
                        }
                        if($scope.condimentMap["CAFE"] !=undefined && $scope.condimentMap["CAFE"]!=null){
                            $scope.condimentCafeList = $scope.condimentMap["CAFE"]
                        }
                        if($scope.condimentMap["COD"] !=undefined && $scope.condimentMap["COD"]!=null){
                            $scope.condimentCODList = $scope.condimentMap["COD"]
                        }
                        if($scope.condimentMap["TAKE_AWAY"] !=undefined && $scope.condimentMap["TAKE_AWAY"]!=null){
                            $scope.condimentTakeAwayList = $scope.condimentMap["TAKE_AWAY"]
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.onChangeSelectedCondimentSource =function(condiment,src){
                console.log("Printing value of condiment",src);
                condiment = JSON.parse(condiment);
                if($scope.recipeDetail.condiments==undefined || $scope.recipeDetail.condiments==null){
                    $scope.recipeDetail.condiments ={
                        dineIn:{
                            groupId:null,
                            groupName:null,
                            quantity:null
                        },
                        delivery:{
                            groupId:null,
                            groupName:null,
                            quantity:null
                        },
                        takeaway:{
                            groupId:null,
                            groupName:null,
                            quantity:null
                        }
                    }
                }
                if ($scope.recipeDetail.condiments.dineIn === undefined || $scope.recipeDetail.condiments.dineIn == null) {
                    $scope.recipeDetail.condiments.dineIn = {
                        groupId:null,
                        groupName:null,
                        quantity:null
                    };
                }
                if ($scope.recipeDetail.condiments.delivery === undefined || $scope.recipeDetail.condiments.delivery == null) {
                    $scope.recipeDetail.condiments.delivery = {
                        groupId:null,
                        groupName:null,
                        quantity:null
                    };
                }
                if ($scope.recipeDetail.condiments.takeaway === undefined || $scope.recipeDetail.condiments.takeaway == null) {
                    $scope.recipeDetail.condiments.takeaway = {
                        groupId:null,
                        groupName:null,
                        quantity:null
                    };
                }
                if(condiment!="" && condiment.groupId!=undefined && condiment.groupId!=null){
                    if(src=="CAFE"){
                        $scope.recipeDetail.condiments.dineIn.groupId = condiment.groupId;
                        $scope.recipeDetail.condiments.dineIn.groupName = condiment.groupName;
                        $scope.dineCondiment = condiment;
                    }else if(src=="COD"){
                        $scope.recipeDetail.condiments.delivery.groupId = condiment.groupId;
                        $scope.recipeDetail.condiments.delivery.groupName = condiment.groupName;
                        $scope.codCondiment = condiment;
                    }else{
                        $scope.recipeDetail.condiments.takeaway.groupId = condiment.groupId;
                        $scope.recipeDetail.condiments.takeaway.groupName = condiment.groupName;
                        $scope.takeawayCondiment = condiment;
                    }
                }
                console.log("Printing Value of recipe line 207",$scope.recipeDetail);
            }

            $scope.changeDineCondimentQuantity = function (qty,src) {
                if(qty<=0){
                    alert("Enter Quantity greater than 0 for",src);
                    if(src='CAFE'){
                        $scope.dineQuantity=null;
                    }
                    return;
                }
                if(qty!=undefined && qty!=null){
                    if(src='CAFE'){
                        $scope.recipeDetail.condiments.dineIn.quantity = qty;
                        $scope.dineQuantity=qty;
                    }
                }
                console.log("Prining Value of recipe",$scope.recipeDetail);
            }

            $scope.changeCODCondimentQuantity = function (qty,src) {
                if(qty<=0){
                    alert("Enter Quantity greater than 0 for",src);
                    if(src='COD'){
                        $scope.codQuantity =null;
                    }
                    return;
                }
                if(qty!=undefined && qty!=null){
                    if(src='COD'){
                        $scope.recipeDetail.condiments.delivery.quantity =qty;
                        $scope.codQuantity =qty;
                    }
                }
                console.log("Prining Value of recipe",$scope.recipeDetail);
            }

            $scope.changeTAKECondimentQuantity = function (qty,src) {
                if(qty<=0){
                    alert("Enter Quantity greater than 0 for",src);
                    if(src='TAKE_AWAY'){
                        $scope.takeawayQuantity = null;
                    }
                    return;
                }
                if(qty!=undefined && qty!=null){
                    if(src='TAKE_AWAY'){
                        $scope.recipeDetail.condiments.takeaway.quantity = qty;
                        $scope.takeawayQuantity =qty;
                    }
                }
                console.log("Prining Value of recipe",$scope.recipeDetail);
            }


            function setInstructionList(){
                var instruction1 = {};
                instruction1.name = 'RED';
                instruction1.color = '#FF0000';
                var instruction2 = {};
                instruction2.name = 'GREEN';
                instruction2.color = '#00FF00';
                $scope.instructionList.push(instruction1, instruction2);

            }

            $scope.filterByCategoryId = function (product) {
                return $scope.selectedSCMCategoryId && product.category.id == $scope.selectedSCMCategoryId;
            };

            $scope.filterByCategory = function (product) {
                return product.recipeRequired == true;
            };

            $scope.cloneRecipeByProfile = function (selectedRecipeId) {
                if ($scope.selectedRecipeId === null || $scope.selectedRecipeId === undefined) {
                    alert("Please select any recipe to clone");
                    return false;
                }
                if ($scope.recipeProfileAvailable.length == 0) {
                    alert("You have already cloned all " + $scope.recipeProfiles.length + " profiles.Profiles Not available to clone");
                    return false;
                }
                for (var i = 0; i < $scope.existingRecipes.length; i++) {
                    if ($scope.selectedRecipeId == $scope.existingRecipes[i].recipeId) {
                        $scope.cloneRecipes = [];
                        if (!($scope.existingRecipes[i].product.classification == "MENU" || $scope.existingRecipes[i].product.classification == "PAID_ADDON")) {
                            alert("You can only create profiles for menu product!");
                            return false;
                        }
                    }
                }
                $scope.clearCloneModal();
                $scope.cloneRecipeProfile = true;
                for (var i = 0; i < $scope.existingRecipes.length; i++) {
                    if ($scope.selectedRecipeId == $scope.existingRecipes[i].recipeId) {
                        $scope.cloneRecipes = [];
                        $scope.cloneRecipes.push($scope.existingRecipes[i]);
                        break;
                    }
                }
                $("#cloneRecipeModal").modal("show");
            };

            $scope.changeRecipeId = function (selectedRecipeId) {
                $scope.selectedRecipeId = selectedRecipeId;
            };

            function createRecipeDetail() {
                var recipe = {
                    status: 'IN_PROGRESS',
                    profile: 'P0',
                    ingredient: {
                        products: [],
                        variants: [],
                        components: []
                    },
                    addons: [],
                    dineInConsumables: [],
                    deliveryConsumables: [],
                    takeawayConsumables: [],
                    condiments :{
                        dineIn:{
                            groupId:null,
                            groupName:null,
                            quantity:null
                        },
                        delivery:{
                            groupId:null,
                            groupName:null,
                            quantity:null
                        },
                        takeaway:{
                            groupId:null,
                            groupName:null,
                            quantity:null
                        }
                    },

                };
                return recipe;
            };

            $scope.selectOptions = {
                width: '100%'
            };

            $scope.setNextTab = function (tabNo) {
                var errors = validate(tabNo, $scope.recipeDetail);
                if (errors.length != 0) {
                    alert(printErrors(errors));
                    return;
                }
                $scope.activeTab = tabNo + 1;
                if($scope.activeTab ==8){
                    $scope.setRecipeCondimentInititalValue();
                }
                // console.log($scope.recipeDetail);
            };

            function validate(tabNo, recipe) {
                var errors = [];
                switch (tabNo) {

                    case 0:
                        if (angular.isUndefined(recipe.product) || recipe.product == null) {
                            addError(errors, -1, "Product Not Selected");
                        }
                        if (angular.isUndefined(recipe.dimension) || recipe.dimension == null) {
                            addError(errors, -1, "Dimension for the product Not Selected");
                        }
                        break;
                    case 1:
                        if (recipe.name == null || !recipe.name.endsWith('Recipe')) {
                            addError(errors, -1, "Incorrect Recipe Name");
                        }
                        if (recipe.startDate == null) {
                            addError(errors, -1, "Start Date Not Entered");
                        }
                        break;
                    case 2:
                        errors = errors.concat(validateIngredient(recipe))
                        break;
                    case 3:
                        if (!isEmptyArray(recipe.addons)) {
                            errors = errors.concat(validateOtherProducts('Addons List', recipe.addons));
                        }
                        break;
                    case 4:
                        if (!isEmptyArray(recipe.dineInConsumables)) {
                            errors = errors.concat(validateOtherProducts('Dine In Consumables List',
                                recipe.dineInConsumables));
                        }
                        break;
                    case 5:
                        if (!isEmptyArray(recipe.deliveryConsumables)) {
                            errors = errors.concat(validateOtherProducts('Delivery Consumables List',
                                recipe.deliveryConsumables));

                        }
                        break;
                    case 6:
                        if (!isEmptyArray(recipe.takeawayConsumables)) {
                            errors = errors.concat(validateOtherProducts('Takeaway Consumables List',
                                recipe.takeawayConsumables));

                        }
                        break;
                }
                return errors;
            }

            $scope.setPrevTab = function (tabNo) {
                if (tabNo == 1) {
                    var result = confirm("This will wipe out all changes. Are you sure?");
                    if (!result) {
                        return;
                    } else if (result == true) {
                        $scope.cloneRecipeProfile = false;
                    }
                }
                $scope.activeTab = tabNo - 1;
                console.log($scope.recipeDetail);
            };

            $scope.setNextIngredientTab = function (tabNo) {
                console.log(tabNo)
                if (tabNo == 2) {
                    if ($scope.isHeroCombo || $scope.isSuperCombo || $scope.isFixedMealProduct) {
                        for (var index in $scope.recipeDetail.ingredient.compositeProduct.details) {
                            var item = $scope.recipeDetail.ingredient.compositeProduct.details[index];
                            if ($scope.recipeDetail.ingredient.compositeProduct.details[index].discount == null || $scope.recipeDetail.ingredient.compositeProduct.details[index].discount <= 0) {
                                if ($scope.isHeroCombo && index == 0) {
                                    console.log("no action for item1");
                                }
                                else{
                                    $scope.recipeDetail.ingredient.compositeProduct.details[index].discount = 0;
                                }
                                // else {
                                //     alert("Discount has to be greater than zero "
                                //         + $scope.recipeDetail.ingredient.compositeProduct.details[index].name
                                //     );
                                //     return;
                                // }
                            }
                            if ($scope.recipeDetail.ingredient.compositeProduct.details[index].internalDiscount == null || $scope.recipeDetail.ingredient.compositeProduct.details[index].internalDiscount <= 0) {
                                if ($scope.isHeroCombo && index == 0) {
                                    console.log("no action for item1");
                                }
                                else{
                                    $scope.recipeDetail.ingredient.compositeProduct.details[index].internalDiscount = 0;
                                }
                            }
                            if($scope.recipeDetail.ingredient.compositeProduct.details[index].customizable == null){
                                $scope.recipeDetail.ingredient.compositeProduct.details[index].customizable = false;
                            }
                            if($scope.recipeDetail.ingredient.compositeProduct.details[index].discountApplicable == null){
                                $scope.recipeDetail.ingredient.compositeProduct.details[index].discountApplicable = false;
                            }
                            if ($scope.recipeDetail.ingredient.compositeProduct.isAllowMultiselection) {
                                if (item.minSelection == null || item.maxSelection == null) {
                                    alert("Please select Min and Max Selection for " + item.name);
                                    return;
                                }
                                else if (parseInt(item.minSelection) > parseInt(item.maxSelection)) {
                                    alert("Min Selection cannot be greater than Max Selection for " + item.name);
                                    return;
                                }
                            }
                        }
                    }
                }
                $scope.activeIngredientTab = tabNo + 1;
                console.log($scope.recipeDetail);


            };

            $scope.setPrevIngredientTab = function (tabNo) {
                $scope.activeIngredientTab = tabNo - 1;
                console.log($scope.recipeDetail);
            };

            function validateIngredient(recipe) {
                console.log(recipe);
                var errors = [];
                if (recipe.ingredient.products.length == 0 && recipe.ingredient.variants.length == 0
                    && recipe.ingredient.components.length == 0
                    && recipe.ingredient.compositeProduct == null) {
                    addError(errors, -1, "At least One Ingredient in mandatory");
                }
                if (!isEmptyArray(recipe.ingredient.variants)) {
                    var variantErrors = validateIngredientVariants('Variants List', recipe.ingredient.variants);
                    errors = errors.concat(variantErrors);

                }
                if (!isEmptyArray(recipe.ingredient.products)) {
                    errors = errors.concat(validateIngredientSCMProducts('SCM Product List',
                        recipe.ingredient.products));

                }
                if (!isEmptyArray(recipe.ingredient.components)) {
                    errors = errors.concat(validateOtherProducts('Components List',
                        recipe.ingredient.components));

                }
                if (recipe.ingredient.compositeProduct != null) {
                    for (var index in recipe.ingredient.compositeProduct.details) {
                        if (!isEmptyArray(recipe.ingredient.compositeProduct.details[index].menuProducts)) {
                            errors = errors.concat(validateOtherProducts('Menu Product List For '
                                + recipe.ingredient.compositeProduct.details[index].name,
                                recipe.ingredient.compositeProduct.details[index].menuProducts));
                        } else {
                            addError(errors, index, "Atleast One Menu Product in "
                                + recipe.ingredient.compositeProduct.details[index].name
                                + " list is mandatory");
                        }
                        if ($scope.heroOrSuperComboCheck) {
                            if ($scope.isHeroCombo || $scope.isSuperCombo) {
                                if (recipe.ingredient.compositeProduct.details[index].discount == null || recipe.ingredient.compositeProduct.details[index].discount < 0) {
                                    if ($scope.isHeroCombo && index == 0) {
                                        console.log("no action for item1");
                                    } else {
                                        addError(errors, index, "Discount must be greater than zero "
                                            + recipe.ingredient.compositeProduct.details[index].name
                                        );
                                    }
                                }
                            }
                        }
                    }


                }

                return errors;
            }

            function validateIngredientVariants(listName, list) {
                var errors = [];
                for (var i in list) {
                    var product = list[i];
                    if (product == null || product.product == null) {
                        addError(errors, i, listName + ': Product Cannot Be Null');
                    }
                    if (product == null || product.uom == null) {
                        addError(errors, i, listName + ': UOM Cannot Be Null');
                    }
                    if (isEmptyArray(product.details)) {
                        addError(errors, i, listName + ': Aliases Cannot Be Null or Empty');
                    }
                    if (product != null && product.details != null && product.details.length > 0) {
                        for (var j in product.details) {
                            if (product.details[j].alias == null
                                || angular.isUndefined(product.details[j].quantity)
                                || product.details[j].quantity == null) {
                                addError(errors, i, listName
                                    + ': Aliases Name or Quantity Cannot Be Null or Empty');
                            }
                        }
                    }
                }
                console.log(errors);
                return errors;

            }

            function validateOtherProducts(listName, list) {
                var errors = [];
                for (var i in list) {
                    var product = list[i];
                    if (product == null || product.product == null) {
                        addError(errors, i, listName + ': Product Cannot Be Null');
                    }
                    if (product == null || angular.isUndefined(product.quantity) || product.quantity == null) {
                        addError(errors, i, listName + ': Quantity Be Null');
                    }
                }
                console.log(errors);
                return errors;

            }

            function isEmptyArray(array) {
                if (angular.isUndefined(array) || array == null || array.length == 0) {
                    return true;
                }
                return false;
            }

            function validateIngredientSCMProducts(listName, list) {
                var errors = [];
                for (var i in list) {
                    var product = list[i];
                    if (product == null || product.category == null) {
                        addError(errors, i, listName + ': Category Cannot Be Null');
                    }
                    if (isEmptyArray(product.details)) {
                        addError(errors, i, listName + ': Products within Category Cannot Be Null or Empty');
                    }
                    if (product != null && product.details != null && product.details.length > 0) {
                        for (var j in product.details) {
                            if (product.details[j].product == null
                                || angular.isUndefined(product.details[j].quantity)
                                || product.details[j].quantity == null || product.details[j].quantity == 0) {
                                addError(errors, i, listName
                                    + ': Product Details or Quantity Cannot Be Null or Empty');
                            }
                        }
                    }
                }
                return errors;

            }

            $scope.selectRecipeForEdit = function (recipe) {
                recipe.startDate = moment(recipe.startDate).format('YYYY-MM-DD');
                $scope.recipeDetail = recipe;
                if ($scope.isHeroCombo) {
                    console.log(recipe);
                    if (recipe.ingredient.compositeProduct.details != null) {
                        $scope.disableOptionforHeroCombo = true;
                    }
                }

                /*if ($scope.recipeDetail.profile != "P0") {
                    $scope.cloneRecipeProfile = true;
                }*/
                $scope.setNextTab(0);
            };

            $scope.editActiveRecipe = function (recipe) {
                $scope.recipeForEdit = recipe;
                $scope.showStartDateMandatory = false;
                $("#editActiveRecipeModal").modal("show");
            };

            $scope.previewRecipe = function (recipe, isForApproval) {
                if ($scope.isHeroCombo || $scope.isSuperCombo) {
                    $scope.heroOrSuperComboCheck = false;
                }
                $scope.recipeDetail = recipe;
                if (isForApproval) {
                    $scope.isForApproval = true;
                } else {
                    $scope.isForApproval = false;
                    if (recipe.status === "ACTIVE") {
                        $scope.displayTypes = [];
                    }
                }
                $scope.showPreview(false);
            };

            $scope.addInProgressRecipe = function () {
                if ($scope.startDate != null) {
                    $scope.editRecipeDetail = JSON.parse(JSON.stringify($scope.recipeForEdit, function (key, value) {
                        if (key === "_id" || key === "recipeId") {
                            return undefined;
                        }
                        return value;
                    }));
                    $scope.editRecipeDetail.status = "IN_PROGRESS";
                    $scope.editRecipeDetail.startDate = moment($scope.startDate).format('YYYY-MM-DD');
                    var reqObj = {
                        recipeDetail: $scope.editRecipeDetail,
                        recipeUpdateLogDetail: {updatedBy: AppUtil.getCurrentUser()}
                    };
                    $("#editActiveRecipeModal").modal("hide");
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.recipeManagement.addCloneRecipe,
                        data: reqObj
                    }).then(function success(response) {
                        var result = response.data;
                        if (result == null) {
                            bootbox.alert("Unable to edit " + $scope.recipeForEdit.name + " : " + $scope.recipeForEdit.profile);
                            $rootScope.showFullScreenLoader = false;
                        } else {
                            updateRecipeMedia(result, $scope.recipeForEdit);
                            $scope.editRecipeDetail = null;
                            $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function (value) {
                                $scope.existingRecipes = value;
                            });
                        }
                        if (result.errorMessage != null) {
                            bootbox.alert(result.errorMessage);
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        alert("Unable to save edit recipe");
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    $scope.showStartDateMandatory = true;
                }
            };

            function updateRecipeMedia(recipeId, oldRecipeDetail) {
                console.log(oldRecipeDetail);
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.getRecipeStepByRecipeId,
                    params: {recipeId: oldRecipeDetail.recipeId}
                }).success(function (response) {
                    if(response.length > 0) {
                        console.log("Recipe steps", response[0].recipeSteps);
                        var oldRecipeMediaDetail = response[0];
                        var recipeMediaDetail = {
                            recipeId: recipeId,
                            recipeSteps: oldRecipeMediaDetail.recipeSteps,
                            name: oldRecipeMediaDetail.name,
                            product: oldRecipeMediaDetail.product,
                            dimension: oldRecipeMediaDetail.dimension
                        };

                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.recipeManagement.addRecipeMedia,
                            data: recipeMediaDetail
                        }).then(function success(response) {
                            console.log("Recipe media: ", response);
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    }
                }).error(function (response) {
                    console.log(response);
                });
            }

            $scope.removeFromExistingRecipes = function (existingRecipes, recipe) {
                if (existingRecipes == undefined || existingRecipes == null) {
                    return null;
                }
                var i;
                for (i = 0; i < existingRecipes.length; i++) {
                    if (existingRecipes[i].recipeId == recipe.recipeId) {
                        existingRecipes.splice(i, 1);
                        break;
                    }
                }
                return existingRecipes;
            };

            $scope.getRecipeLogs = function (recipeId) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.recipeManagement.getLogs,
                    data: recipeId
                }).then(function success(response) {
                    $scope.recipeLogs = response.data;
                    $rootScope.showFullScreenLoader = false;
                    if ($scope.recipeLogs != null && $scope.recipeLogs.length > 0) {
                        $("#showLogModal").modal("show");
                    } else {
                        bootbox.alert("No logs available");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.inactivateRecipeProfile = function (recipe) {
                console.log("recipe is : ",recipe);
                $scope.selectedRecipeForInactivation = recipe;
                $scope.recipeInactiveUnits = [];
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.productMetaData.recipeProfileInactivateCheck,
                    params: {
                        "productId" : recipe.product.productId,
                        "dimensionId" : recipe.dimension.infoId,
                        "profile" : recipe.profile
                    }
                }).then(function success(response) {
                    $scope.recipeInactiveUnits = response.data;
                    for(var i = 0; i < $scope.recipeInactiveUnits.length; i++) {
                        $scope.recipeInactiveUnits[i].selected = false;
                        $scope.recipeInactiveUnits[i].profile = null;
                        $scope.recipeInactiveUnits[i].isVisible = true;
                        $scope.recipeInactiveUnits[i].canEdit = ($scope.recipeInactiveUnits[i].status == 'ACTIVE' && $scope.recipeInactiveUnits[i].code == 'ACTIVE') ? false : true;
                    }
                    $scope.selectedAllUnits = false;
                    $scope.selectedProfileForAllUnits = null;
                    $rootScope.showFullScreenLoader = false;
                    $scope.model.unitPPStatus = null;
                    $scope.model.unitStatus = null;
                    $scope.searchText = null;

                    $scope.statusOptions = ["ACTIVE", "IN_ACTIVE"];

                    $("#showRecipeProfileInactiveModal").modal("show");
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.selectedAllUnitCheck = function(selectedAllUnits) {
                $scope.selectedAllUnits = selectedAllUnits;
                $scope.selectedProfileForAllUnits = null;
                for(var i=0; i<$scope.recipeInactiveUnits.length; i++) {
                    if($scope.recipeInactiveUnits[i].isVisible && $scope.recipeInactiveUnits[i].canEdit) {
                        $scope.recipeInactiveUnits[i].selected = selectedAllUnits;
                    }
                }
                console.log("$scope.recipeInactiveUnits 1: ", $scope.recipeInactiveUnits)
            }

            $scope.onChangeSelectedProfileForAllUnits = function(selectedProfileForAllUnits) {
                if(!$scope.selectedAllUnits) {
                    bootbox.alert("Please select 'SELECT ALL'!");
                    $scope.selectedProfileForAllUnits = null;
                    return;
                }
                $scope.selectedProfileForAllUnits = selectedProfileForAllUnits;
                for(var i=0; i<$scope.recipeInactiveUnits.length; i++) {
                    if($scope.recipeInactiveUnits[i].isVisible && $scope.recipeInactiveUnits[i].selected) {
                        $scope.recipeInactiveUnits[i].profile = selectedProfileForAllUnits;
                    }
                }
                $timeout(function(){
                    $scope.$apply();
                });
                console.log("$scope.recipeInactiveUnits 2: ", $scope.recipeInactiveUnits);
            }

            $scope.applyFilters = function (text, uppStatus, uStatus) {
                $scope.model.unitPPStatus = uppStatus;
                $scope.model.unitStatus = uStatus;
                $scope.searchText = text;
                for (var i = 0; i < $scope.recipeInactiveUnits.length; i++) {
                    var unit = $scope.recipeInactiveUnits[i];

                    var matchesText = !text || unit.name.toLowerCase().includes(text.toLowerCase());
                    var matchesStatus = !uppStatus || (unit.status == uppStatus);
                    var matchesCode = !uStatus || (unit.code == uStatus);

                    unit.isVisible = matchesText && matchesStatus && matchesCode;
                }
            };

            $scope.unfilter = function(field) {
                if(field == 'status') {
                    $scope.model.unitPPStatus = null;
                } else if(field == 'code') {
                    $scope.model.unitStatus = null;
                }
                $timeout(function(){
                    $scope.$apply();
                });
                $scope.applyFilters($scope.searchText, $scope.model.unitPPStatus, $scope.model.unitStatus);
            }


            $scope.changeRecipeProfiles = function () {
                var data = [];
                for(var i = 0; i < $scope.recipeInactiveUnits.length; i++) {
                    if($scope.recipeInactiveUnits[i].selected && $scope.recipeInactiveUnits[i].profile != null && $scope.recipeInactiveUnits[i].canEdit) {
                        var mapping = {};
                        mapping.unit = { id: $scope.recipeInactiveUnits[i].id };
                        mapping.product = { id: $scope.selectedRecipeForInactivation.product.productId };
                        mapping.price = { 
                            dimension: $scope.selectedRecipeForInactivation.dimension.infoId + "", 
                            profile: $scope.recipeInactiveUnits[i].profile 
                        };
                        data.push(mapping);
                    }
                }
                console.log("Final profile change list : ", data);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.productMetaData.unitProductProfileUpdate+"?updatedBy="+$rootScope.userData.id,
                    data: data
                }).then(function success(response) {
                    $("#showRecipeProfileInactiveModal").modal("hide");
                    bootbox.alert("Updated Successfully");
                }, function error(response) {
                    bootbox.alert("Error while updating recipe profiles");
                    console.log("error:" , response);
                });
            }

            $scope.changeRecipeStatus = function () {
                $rootScope.showFullScreenLoader = true;

            var data=AppUtil.getUserValues();
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.recipeManagement.updateRecipeProfileStatus,
                    params: {
                        "recipeId" : $scope.selectedRecipeForInactivation.recipeId,
                        "userId" : data.user.id
                    }
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status == 200 && response.data == true) {
                        bootbox.alert("Status Updated Successfully");
                        $("#showRecipeProfileInactiveModal").modal("hide");
                        $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function (value) {
                            $scope.existingRecipes = value;
                            $scope.selectedRecipeId = null;
                        });
                    }
                    else {
                        bootbox.alert("Error Occurred While Updating the Status of Selected Recipe..!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.removeRecipe = function (recipe) {
                bootbox.alert("Remove recipe is not allowed.");
                /*var data = $cookieStore.get('adminglobals');
                if (data != undefined && data != null) {
                    recipe.lastUpdatedByName = data.user.name;
                    recipe.lastUpdatedById = data.user.id;
                }
                if (confirm("Are you sure, you want to delete this recipe?\n This Step cannot be undone")) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.recipeManagement.removeRecipe,
                        data: recipe
                    }).then(
                        function success(response) {
                            var result = response.data;
                            if (result == null) {
                                bootbox.alert("Unable to remove " + recipe.name + " : " + recipe.profile);
                            } else if (result.length > 0) {
                                var msg = recipe.name + " : " + recipe.profile + " is attached with units : ";
                                for (var i = 0; i < result.length; i++) {
                                    msg += result[i].name;
                                    if (i < result.length - 1) {
                                        msg += ", "
                                    } else {
                                        msg += ". "
                                    }
                                }
                                bootbox.alert(msg + " So it can not be removed! ");
                            } else {
                                bootbox.alert("Removed successfully recipe of : " + recipe.name + " : " + recipe.profile);
                                $scope.existingRecipes = $scope.removeFromExistingRecipes(
                                    $scope.existingRecipes, recipe);
                            }
                            $rootScope.showFullScreenLoader = false;
                        }, function error(response) {
                            console.log("error:" + response);
                            alert("Unable to remove recipe");
                            $rootScope.showFullScreenLoader = false;
                        });
                }*/
            };

            $scope.getDispenserDetail = function (recipe) {
                $scope.clearDispenserDetail();
                $scope.dispenserRecipeDetail.map(function (dispenserRecipe) {
                    if ((recipe.product.productId == dispenserRecipe.product.id) && (recipe.dimension.infoId == dispenserRecipe.dimension.id)) {
                        console.log("for product" + recipe.product.productId + "and dimension id" + dispenserRecipe.dimension.id);
                        console.log(dispenserRecipe.mapping);
                        for (var key in dispenserRecipe.mapping.addOns) {
                            $scope.dispenserMapping.addOns.push({
                                tag: dispenserRecipe.mapping.addOns[key].tag,
                                revolution: dispenserRecipe.mapping.addOns[key].value,
                                product: {
                                    id: key.split('#')[0],
                                    name: key.split('#')[1]
                                }
                            })
                        }
                        for (var key in dispenserRecipe.mapping.mandatoryAddons) {
                            $scope.dispenserMapping.mandatoryAddons.push({
                                tag: dispenserRecipe.mapping.mandatoryAddons[key].tag,
                                revolution: dispenserRecipe.mapping.mandatoryAddons[key].value,
                                product: {
                                    id: key.split('#')[0],
                                    name: key.split('#')[1]
                                }
                            })
                        }
                        for (var key in dispenserRecipe.mapping.variants) {
                            $scope.dispenserMapping.variants.push({
                                tag: dispenserRecipe.mapping.variants[key].tag,
                                revolution: dispenserRecipe.mapping.variants[key].value,
                                product: {
                                    id: key.split('#')[0],
                                    name: key.split('#')[1]
                                }
                            })
                        }
                        console.log("managing mappings");
                        console.log($scope.dispenserMapping);
                    }
                })
                recipe.ingredient.variants.map(function (variant) {
                    var aliasAndRevolution = [];
                    if (variant.dispensed) {
                        variant.details.map(function (detail) {
                            console.log(detail)

                            var flag = false;
                            for (var i in $scope.dispenserMapping.variants) {
                                if (detail.alias == $scope.dispenserMapping.variants[i].product.name) {
                                    console.log("inside variant")
                                    aliasAndRevolution.push({
                                        revolution: $scope.dispenserMapping.variants[i].revolution,
                                        alias: detail
                                    })
                                    flag = true;
                                }
                            }
                            if (!flag) {
                                aliasAndRevolution.push({
                                    // revolution: $scope.dispenserMapping[i].revolution,
                                    alias: detail
                                })
                                // $scope.variantDispenserDetail.push({
                                //     alias: detail.alias
                                // })
                            }

                        })
                        $scope.variantDispenserDetail.push({
                            aliasAndRevolution: aliasAndRevolution,
                            product: variant.product,
                            dispenseTag: variant.dispenseTag
                        })
                    }
                })

                recipe.addons.map(function (addon) {
                    if (addon.dispensed) {
                        var flag = false;
                        console.log("checking add ons")
                        for (var i in $scope.dispenserMapping.addOns) {
                            if (addon.product.productId == $scope.dispenserMapping.addOns[i].product.id) {
                                console.log("testing")
                                console.log(addon)
                                $scope.addonDispenserDetail.push({
                                    revolution: $scope.dispenserMapping.addOns[i].revolution,
                                    product: addon.product,
                                    dispenseTag: addon.dispenseTag
                                })
                                flag = true;
                            }
                        }
                        if (!flag) {
                            console.log("testing")
                            console.log(addon)
                            $scope.addonDispenserDetail.push({
                                product: addon.product,
                                dispenseTag: addon.dispenseTag
                            })
                        }
                        $scope.addonDispenserDetail.map(function (addonDispenser) {
                            if (addonDispenser.product.productId == 972 || addon.product.productId == 983) {
                                addonDispenser.product.name = addonDispenser.product.name.replace(/eliachi/i, "Elaichi");
                            }
                        })


                    }
                })

                recipe.mandatoryAddons.map(function (mandatoryAddon) {
                    if (mandatoryAddon.dispensed) {
                        var flag = false;
                        console.log("checking mandatory")
                        console.log($scope.mandatoryAddonDispenserDetail)
                        for (var i in $scope.dispenserMapping.mandatoryAddons) {
                            if (mandatoryAddon.product.productId == $scope.dispenserMapping.mandatoryAddons[i].product.id) {
                                console.log("setting mandatory addons with revolution")
                                console.log(mandatoryAddon)
                                $scope.mandatoryAddonDispenserDetail.push({
                                    revolution: $scope.dispenserMapping.mandatoryAddons[i].revolution,
                                    product: mandatoryAddon.product,
                                    dispenseTag: mandatoryAddon.dispenseTag
                                })
                                flag = true;
                            }
                        }
                        if (!flag) {
                            console.log("mandatory initial setup")
                            console.log(mandatoryAddon)
                            $scope.mandatoryAddonDispenserDetail.push({
                                product: mandatoryAddon.product,
                                dispenseTag: mandatoryAddon.dispenseTag
                            })
                        }
                        // $scope.addonDispenserDetail.map(function (addonDispenser) {
                        //     if (addonDispenser.product.productId == 972 || addon.product.productId == 983) {
                        //         addonDispenser.product.name = addonDispenser.product.name.replace(/eliachi/i, "Elaichi");
                        //     }
                        // })


                    }
                })

                console.log("variant")
                console.log($scope.variantDispenserDetail)
                console.log("addon")
                console.log($scope.addonDispenserDetail)
                console.log("mandatoryaddon")
                console.log($scope.mandatoryAddonDispenserDetail)
                $("#dispenserModal").modal("show");
                $scope.dispenserDetail = recipe;


            }

            $scope.clearDispenserDetail = function () {
                $scope.variantDispenserDetail = [];
                $scope.addonDispenserDetail = [];
                $scope.mandatoryAddonDispenserDetail = [];
                $scope.addonDispenserDetail.name = null;
                $scope.addonDispenserDetail.revolution = null;
                $scope.addonDispenserDetail.dispenseTag = null;
                $scope.mandatoryAddonDispenserDetail.name = null;
                $scope.mandatoryAddonDispenserDetail.revolution = null;
                $scope.mandatoryAddonDispenserDetail.dispenseTag = null;
                $scope.variantDispenserDetail.name = null;
                $scope.variantDispenserDetail.dispenseTag = null;
                $scope.variantDispenserDetail.aliasAndRevolution = [];
                $scope.variantDispenserDetail.aliasAndRevolution.alias = null;
                $scope.variantDispenserDetail.aliasAndRevolution.revolution = null;
                $scope.dispenserMapping = [];
                $scope.dispenserMapping.addOns = [];
                $scope.dispenserMapping.variants = [];
                $scope.dispenserMapping.mandatoryAddons = [];

            }


            $scope.submitDispenser = function () {
                var result = confirm("Are You Sure You Want  To Save Both variant and Addons?");
                if (!result) {
                    return;
                }
                var payload = {
                    product: {
                        id: $scope.dispenserDetail.product.productId,
                        name: $scope.dispenserDetail.product.name
                    },
                    dimension: {
                        id: $scope.dispenserDetail.dimension.infoId,
                        code: $scope.dispenserDetail.dimension.code,
                        name: $scope.dispenserDetail.dimension.name
                    },
                    profile: $scope.dispenserDetail.profile,
                    addOns: $scope.dispenserMapping.addOns,
                    variants: $scope.dispenserMapping.variants,
                    mandatoryAddons: $scope.dispenserMapping.mandatoryAddons
                    // mappingRequest: $scope.dispenserMapping
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.recipeManagement.saveDispenserData,
                    data: payload
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("data added");
                    $scope.clearDispenserDetail()
                    $scope.getDispenserData();
                    $("#dispenserModal").modal("hide");
                }, function error(response) {
                    bootbox.alert("Error in adding data");
                    console.log("error:" + response);
                    $("#dispenserModal").modal("hide");
                    $rootScope.showFullScreenLoader = false;
                });


            }
            $scope.saveRevolutionDetails = function (product, dispenseTag, revolution, type) {
                console.log(type)
                $scope.flag = false;
                if (revolution < 0) {
                    alert("please enter value greater than or equal to 0");
                    return
                }
                for (var key in $scope.dispenserMapping[type]) {
                    if (type == "addOns") {
                        if (product.productId == $scope.dispenserMapping[type][key].product.id) {
                            if (product.productId == 972 || product.productId == 983) {
                                console.log("checking name")
                                console.log(product)
                                $scope.dispenserMapping[type][key].product.name = $scope.dispenserMapping[type][key].product.name.replace(/eliachi/i, "Elaichi");
                                console.log("replacing name");
                            }
                            console.log("updating addon")
                            $scope.flag = true;
                            $scope.dispenserMapping[type][key].revolution = revolution;
                        }
                    } else if (type == "mandatoryAddons") {
                        if (product.productId == $scope.dispenserMapping[type][key].product.id) {
                            console.log("updating mandatory add ons")
                            $scope.flag = true;
                            $scope.dispenserMapping[type][key].revolution = revolution;
                        }
                    }
                    else {
                        if (product.alias == $scope.dispenserMapping[type][key].product.name) {
                            console.log("updating variant")
                            $scope.flag = true;
                            $scope.dispenserMapping[type][key].revolution = revolution;
                        }
                    }
                }
                if (!$scope.flag) {
                    $scope.dispenserMapping[type].push({
                        tag: dispenseTag,
                        revolution: revolution,
                        product: {
                            id: product.productId,
                            name: type == 'variants' ? product.alias : product.name
                        }
                    })
                }
                console.log($scope.dispenserMapping);
            }

            $scope.removeDispenserDetail = function (name, type) {
                for (var key in $scope.dispenserMapping[type]) {

                    if (name == $scope.dispenserMapping[type][key].product.name) {
                        $scope.dispenserMapping[type].splice(key, 1);
                        key--;
                        console.log("remove  value")

                    }
                }
                console.log($scope.dispenserMapping);
            }
            $scope.changeOption = function (chosenTab) {

                if (chosenTab == 1) {
                    $scope.selectedVariantTab = true;
                    $scope.selectedAddonTab = false;
                    $scope.selectedMandatory = false;
                } else if (chosenTab == 2) {
                    $scope.selectedVariantTab = false;
                    $scope.selectedAddonTab = true;
                    $scope.selectedMandatory = false;
                }
                else if (chosenTab == 3) {
                    $scope.selectedVariantTab = false;
                    $scope.selectedAddonTab = false;
                    $scope.selectedMandatory = true;
                }
            }

            $scope.selectRecipeForClone = function (recipe) {
                $scope.recipeDetail = JSON.parse(JSON.stringify(recipe, function (key, value) {
                    if (key === "_id" || key === "recipeId") {
                        return undefined;
                    }
                    return value;
                }));
                if ($scope.cloneRecipeProfile && $scope.updatedRecipeProfile == null) {
                    alert("Please select Recipe Profile.");
                    return false;
                }
                $scope.recipeDetail.startDate = null;
                if ($scope.updatedRecipeProfile != null) {
                    $scope.recipeDetail.profile = $scope.updatedRecipeProfile;
                }
                $scope.createNewRecipeData($scope.selectedProduct, $scope.selectedDimensionData);
                $("#cloneRecipeModal").modal("hide");
                $scope.setNextTab(0);

            };

            $scope.selectCloneProductProfile = function (profile) {
                console.log(profile);
                $scope.updatedRecipeProfile = profile;
            };

            $scope.filterFunction = function (element) {
                if (!$scope.cloneRecipeProfile) {
                    return element.profile == 'P0';
                }
                return true;
            };

            $scope.selectSCMRecipeForClone = function (recipe) {
                $scope.recipeDetail = JSON.parse(JSON.stringify(recipe, function (key, value) {
                    if (key === "_id" || key === "recipeId") {
                        return undefined;
                    }

                    return value;
                }));
                $scope.recipeDetail.startDate = moment($scope.recipeDetail.startDate).format('YYYY-MM-DD');
                $scope.createNewRecipeData($scope.selectedProduct, $scope.getDimensionData());
                $("#cloneSCMRecipeModal").modal("hide");
                $scope.setNextTab(0);

            };

            $scope.findAllRecipes = function (productId, dimensionId, callback) {
                $rootScope.showFullScreenLoader = true;
                console.log(productId);
                console.log(dimensionId);
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.findRecipe + '?productId=' + productId + '&dimensionId=' + dimensionId + '&active=true'
                }).then(function success(response) {
                    console.log('Found Recipes', response.data);
                    $scope.recipeProfileAvailable = $scope.recipeProfiles.slice();
                    $scope.allRecipesForProduct = [];
                    for (var i = 0; i < response.data.length; i++) {
                        for (var j = 0; j < $scope.recipeProfileAvailable.length; j++) {
                            if (!$scope.allRecipesForProduct.includes(response.data[i].profile)) {
                                $scope.allRecipesForProduct.push(response.data[i].profile);
                            }
                            if (response.data[i].profile == $scope.recipeProfileAvailable[j].name) {
                                $scope.recipeProfileAvailable.splice(j, 1);
                            }
                        }
                    }
                    $scope.checkEditable(response.data);
                    callback(response.data);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.findAllRecipesByUom = function (productId, dimensionId, callback) {
                $rootScope.showFullScreenLoader = true;
                console.log(productId);
                console.log(dimensionId);
                $http(
                    {
                        method: 'GET',
                        url: AppUtil.restUrls.recipeManagement.findRecipeByUom + '?productId=' + productId
                        + '&uom=' + dimensionId
                    }).then(function success(response) {
                    console.log('Found Recipes', response.data);
                    callback(response.data);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };
            // Tab 1 utility methods
            $scope.getAllProducts = function () {
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.productMetaData.products
                }).then(function success(response) {
                    $scope.productsInfo = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            // Tab 1 utility methods
            $scope.getAllSCMProducts = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.scmProductManagement.productBasicDetails
                }).then(function success(response) {
                    $scope.scmProductsInfo = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            // Tab 1 utility methods
            $scope.getAllSCMProductsForRecipe = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.scmProductManagement.productBasicDetailsForRecipe,
                    params: {
                        "isScm" : false
                    }
                }).then(function success(response) {
                    $scope.scmProductsInfo = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAllSCMProductsWithRecipe = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.scmRecipeManagement.findAllBasic,
                }).then(function success(response) {
                    $scope.scmProductsWithRecipe = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            // Tab 1 utility methods
            $scope.getSCMMetadata = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.scmProductManagement.metadata
                }).then(function success(response) {
                    $scope.scmMetadata = response.data;
                    console.log($scope.scmMetadata);
                }, function error(response) {
                    console.log("error:" + response);
                });
            };
            $scope.getAllDimensions = function () {
                // console.log($scope.prodListData);
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.listTypes
                }).then(function success(response) {
                    $scope.categoryLists = response.data;
                    $scope.productCategory = $scope.categoryLists.CATEGORY;
                    $scope.productDimension = $scope.categoryLists.DIMENSION;
                    $scope.optionsList = $scope.categoryLists.OPTIONS;
                });
            };

            $scope.getDispenseTag = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.getDispenseTag
                }).then(function success(response) {
                    $scope.dispenseTagList = response.data;
                    console.log($scope.dispenseTagList);
                });
            };

            $scope.getDispenserData = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.getDispenserData
                }).then(function success(response) {
                    $scope.dispenserRecipeDetail = response.data;
                    console.log($scope.dispenseTagList);
                });
            }

            $scope.getIngedientTypes = function () {
                // console.log($scope.prodListData);
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.ingredientType
                }).then(function success(response) {
                    $scope.ingredientTypes = response.data;
                });
            };

            $scope.onSelectProduct = function (selectedProductId) {
                console.log("selecting product")
                console.log($scope.product.selectedProductId + " " + selectedProductId);
                var p = getDetail(selectedProductId, $scope.productsInfo);
                if (!$scope.validateProduct(p)) {
                    return;
                }
                $scope.cloneRecipeProfile = false;
                $scope.selectedProduct = p;
                $scope.recipeDetail = createRecipeDetail();
                $scope.selectedDimensionProfile = getDimensionDetail($scope.selectedProduct.dimensionProfileId,
                    $scope.productDimension);
                $scope.isComboProduct = $scope.selectedProduct.type == 8;
                $scope.isFixedMealProduct = $scope.selectedProduct.type == 8 && $scope.selectedProduct.subType == 3891;
                $scope.isQVM = $scope.selectedProduct.type == 43;
                $scope.isHeroCombo = $scope.selectedProduct.subType == 3675;
                if ($scope.isHeroCombo) {
                    $scope.disableOptionforHeroCombo=true;
                    $scope.item1Check = true;
                }
                $scope.isSuperCombo = $scope.selectedProduct.subType == 3676;
                $scope.isBoxCombo = $scope.selectedProduct.subType == 3677;
                if ($scope.selectedDimensionData != null) {
                    $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function (value) {
                        $scope.existingRecipes = value;
                    });
                }
            };

            $scope.checkEditable = function (existingRecipes) {
                existingRecipes.map(function (recipe) {
                    if (recipe.status == "ACTIVE") {
                        var found = false;
                        existingRecipes.map(function (match) {
                            if (recipe.profile == match.profile && (match.status == "IN_PROGRESS" || match.status == "PENDING_APPROVAL")) {
                                found = true;
                            }
                        });
                        recipe.isEditable = !found;
                    }
                    if (recipe.status == "IN_PROGRESS") {
                        recipe.isEditable = true;
                    }
                });
                console.log($scope.existingRecipes);
            };

            $scope.validateProduct = function (p) {
                if (p == null) {
                    return true;
                }
                if ((p.taxCode == undefined && p.code == undefined) || (p.taxCode != undefined && p.taxCode == null) || (p.code != undefined && p.code == null)) {
                    bootbox.alert("Please set the Tax Category Code for " + p.name +
                        ' before adding its recipe');
                    return false;
                }
                return true;
            };

            $scope.selectSCMMainProduct = function (selectedProductId) {
                console.log('selected productid ++', selectedProductId);
                $scope.recipeDetail = createRecipeDetail();
                var productInfo = getVariantDetail(selectedProductId, $scope.scmProductsInfo);
                $scope.selectedProduct = convertSCMProductToBasicDetail(productInfo);
                if (!$scope.validateProduct($scope.selectedProduct)) {
                    return;
                }
                console.log('selected product ++', $scope.selectedProduct);
                $scope.findAllRecipesByUom($scope.selectedProduct.id, $scope.selectedProduct.unitOfMeasure,
                    function (value) {
                        $scope.existingRecipes = value;
                    });
            };
            $scope.selectDimension = function (dimensionId) {
                if ($scope.selectedDimensionProfile == null) {
                    return;
                }
                $scope.cloneRecipeProfile = false;
                $scope.selectedDimensionData = createDimensionDetail(getDetail(dimensionId,
                    $scope.selectedDimensionProfile.content));
                $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function (value) {
                    $scope.existingRecipes = value;
                    $scope.selectedRecipeId = null;
                });
            };

            $scope.addNewRecipe = function () {
                $scope.recipeDetail = createRecipeDetail();
                $scope.createNewRecipeData($scope.selectedProduct, $scope.selectedDimensionData);
                $scope.setNextTab(0);
            };

            $scope.addNewSCMRecipe = function () {
                $scope.recipeDetail = createRecipeDetail();
                $scope.createNewRecipeData($scope.selectedProduct, $scope.getDimensionData());
                $scope.setNextTab(0);
            };

            $scope.getDimensionData = function () {
                var code = $scope.selectedProduct.unitOfMeasure;
                var id = 0;
                if (code == 'KG') {
                    id = 0;
                } else if (code == 'GM') {
                    id = 1;
                } else if (code == 'ML') {
                    id = 2;
                } else if (code == 'L') {
                    id = 3;
                } else if (code == 'PC') {
                    id = 4;
                } else if (code == 'PACKET') {
                    id = 5;
                } else if (code == 'SACHET') {
                    id = 6;
                } else if (code == 'PKT') {
                    id = 7;
                } else {
                    id = -1;
                }
                return {
                    infoId: id,
                    code: code,
                    name: code
                }
            };

            $scope.createNewRecipeData = function (product, dimension) {
                $scope.recipeDetail.status = "IN_PROGRESS";
                $scope.recipeDetail.product = createProductBasicDetail(product);
                $scope.recipeDetail.dimension = dimension;
                $scope.recipeDetail.name = $scope.recipeDetail.product.name;
                if ($scope.recipeDetail.dimension.name != 'None') {
                    $scope.recipeDetail.name = $scope.recipeDetail.name + ' '
                        + $scope.recipeDetail.dimension.name
                }
                $scope.recipeDetail.name = $scope.recipeDetail.name + ' Recipe';
            };

            $scope.cloneFromRecipe = function () {
                $scope.clearCloneModal();
                $("#cloneRecipeModal").modal("show");
            };

            $scope.cloneFromSCMRecipe = function () {
                $scope.clearSCMCloneModal();
                $("#cloneSCMRecipeModal").modal("show");
            };

            $scope.openOtherSCMProduct = function () {
                $scope.clearOtherSCMProduct();
                $("#addOtherSCMProductModal").modal("show");
            };
            $scope.editOtherSCMProduct = function (variant) {
                $scope.isAdd = false;
                $scope.onChangeSelectedOtherSCMProduct(variant.product.productId);
                $scope.selectedOtherProductQuantity = variant.quantity;
                $scope.selectedOtherProductCritical = variant.critical;
                $scope.selectedOtherProductYield = variant.yield;
                $scope.selectedOtherProductDisplay=variant.display!=undefined && variant.display!=null ?variant.display:false;
                $scope.selectedOtherProductDisplayShortCode=variant.displayCode!=undefined && variant.displayCode !=null ? variant.displayCode : null;
                $("#addOtherSCMProductModal").modal("show");
            };

            $scope.clearOtherSCMProduct = function () {
                $scope.isAdd = true;
                $scope.selectedOtherSCMProduct = null;
                $scope.selectedOtherSCMProductId = null;
                $scope.selectedOtherProductQuantity = null;
                $scope.selectedOtherProductYield = null;
                $scope.selectedOtherProductCritical = false;
                $scope.selectedOtherProductDisplayShortCode=null;
                $scope.selectedOtherProductDisplay=false;
            };
            $scope.onChangeSelectedOtherSCMProduct = function (productId) {
                $scope.selectedOtherSCMProduct = getVariantDetail(productId, $scope.scmProductsInfo);
            };

            $scope.createComboProductItems = function (selectedItemCount) {
                $scope.recipeDetail.ingredient.compositeProduct = {
                    maxQuantity: selectedItemCount,
                    details: [],
                    isAllowMultiselection: false
                };
                for (var i = 1; i <= selectedItemCount; i++) {
                    $scope.recipeDetail.ingredient.compositeProduct.details.push({
                        name: 'Item ' + i,
                        menuProducts: [],
                        customizable : null,
                        minSelection : null,
                        maxSelection : null,
                        discountApplicable : null
                    });
                }
                $scope.compositeProductDetail = null;
            };

            // $scope.submitDiscount=function(detail,productDiscount){
            //     console.log(detail)
            //     console.log(productDiscount)
            //     for(var i in $scope.recipeDetail.ingredient.compositeProduct.details){
            //         if($scope.recipeDetail.ingredient.compositeProduct.details[i].name==detail.name){
            //             $scope.recipeDetail.ingredient.compositeProduct.details[i].discount=productDiscount;
            //         }
            //     }
            //
            // }

            $scope.onChangeSelectedMenuProduct = function (productId) {
                if (productId == null) {
                    return;
                }
                $scope.selectedMenuProduct = {};
                var productInfo = getDetail(productId, $scope.productsInfo);

                $scope.selectedMenuProduct.product = createProductBasicDetail(productInfo);
                $scope.selectedMenuProductDimensionProfile = getDimensionDetail(productInfo.dimensionProfileId,
                    $scope.productDimension);
                $scope.selectMenuProductDimension($scope.selectedMenuProductDimensionProfile.content[0].id);
            };

            $scope.onChangeSelectedCloneProduct = function (productId) {
                $scope.selectedCloneProduct = {};
                var productInfo = getDetail(productId, $scope.productsInfo);
                $scope.selectedCloneProduct.product = createProductBasicDetail(productInfo);
                $scope.selectedCloneProductDimensionProfile = getDimensionDetail(
                    productInfo.dimensionProfileId, $scope.productDimension);
                $scope.selectCloneProductDimension($scope.selectedCloneProductDimensionProfile.content[0].id);
            };

            $scope.onChangeSelectedCloneSCMProduct = function (productId) {
                $scope.selectedCloneProduct = {};
                var productInfo = getVariantDetail(productId, $scope.scmProductsInfo);
                $scope.findAllRecipesByUom(productInfo.productId, productInfo.unitOfMeasure, function (value) {
                    $scope.cloneRecipes = value;
                });
            };

            $scope.selectCloneProductDimension = function (dimensionId) {
                console.log(dimensionId);
                $scope.selectedCloneDimensionData = getDetail(dimensionId,
                    $scope.selectedCloneProductDimensionProfile.content);
                $scope.selectedCloneProduct.dimension = $scope.selectedCloneDimensionData;
                $scope.findAllRecipes($scope.selectedCloneProduct.product.productId, dimensionId, function (value) {
                    $scope.cloneRecipes = value;
                });
            };

            $scope.selectMenuProductDimension = function (dimensionId) {
                console.log(dimensionId);
                $scope.selectedMenuProduct.dimension = getDetail(dimensionId,
                    $scope.selectedMenuProductDimensionProfile.content);
                console.log($scope.selectedMenuProduct);

            };

            $scope.addMenuProduct = function (selectedMenuProductQuantity) {
                console.log($scope.selectedMenuProduct);
                console.log(selectedMenuProductQuantity);
                if (selectedMenuProductQuantity == undefined || selectedMenuProductQuantity == null
                    || selectedMenuProductQuantity == '') {
                    alert("Quantity Cannot Be Empty");
                    return;
                }
                $scope.selectedMenuProduct.quantity = selectedMenuProductQuantity;
                //todo get menu product recipe and add ingredient data to the menu product
                //$scope.getRecipeAndAddIngredients($scope.selectedMenuProduct);
                if ($scope.isQVM) {
                    if ($scope.compositeProductDetail.menuProducts.length > 0) {
                        bootbox.alert("Only one product can be added to an item in QVM.");
                    } else {
                        $rootScope.showFullScreenLoader = true;
                        $scope.findAllRecipes($scope.selectedMenuProduct.product.productId, $scope.selectedMenuProduct.dimension.id,
                            function (values) {
                                values.map(function (value) {
                                    if (value.profile == $scope.recipeDetail.profile && value.status == "ACTIVE") {
                                        $scope.selectedMenuProduct.ingredient = value.ingredient;
                                        $scope.selectedMenuProduct.ingredient.components = [];
                                        $scope.selectedMenuProduct.addons = value.addons;
                                    }
                                });
                                $rootScope.showFullScreenLoader = false;
                                if ($scope.selectedMenuProduct.ingredient != null) {
                                    $scope.compositeProductDetail.menuProducts.push($scope.selectedMenuProduct);
                                    $("#addMenuProductModal").modal("hide");
                                    // TODO convert to the product and add to the ingredient
                                    $scope.selectedMenuProduct = null;
                                    $scope.compositeProductDetail = null;
                                } else {
                                    bootbox.alert("Error getting combo constituent product recipe.");
                                }
                            });
                    }

                } else {
                    if ($scope.isHeroCombo) {
                        $scope.disableOptionforHeroCombo = true;
                    }
                    $scope.compositeProductDetail.menuProducts.push($scope.selectedMenuProduct);
                    $("#addMenuProductModal").modal("hide");
                    // TODO convert to the product and add to the ingredient
                    $scope.selectedMenuProduct = null;
                    $scope.compositeProductDetail = null;
                }
            };

            $scope.menuProductfilter = function (product) {
                if ($scope.compositeProductDetail !== undefined && $scope.compositeProductDetail !==null && $scope.compositeProductDetail.isFixedMealApplicable && $scope.isFixedMealProduct) {
                    if (product.classification === "MEALS")
                        return true;
                } else {
                    return product.type != 8;
                }
                return false;
            }

            $scope.openMenuProduct = function (detail) {
                $scope.selectedMenuProduct = null;
                $scope.selectedMenuProductId = null;
                $scope.selectedMenuProductDimensionId = null;
                $scope.selectedMenuProductQuantity = null;
                $scope.compositeProductDetail = detail;
                $("#addMenuProductModal").modal("show");
                $(".select2-selection__rendered").html("");
            };

            $scope.setSelAddonDisplayShortCode= function(shortCode){
                $scope.selectedAddonDisplayShortCode= shortCode;
            }

            $scope.setSelOtherProductDisplayShortCode= function(shortCode){
                $scope.selectedOtherProductDisplayShortCode= shortCode;
            }

            $scope.addOtherSCMProduct = function (product, selectedOtherProductQuantity,
                                                  selectedOtherProductCritical, selectedOtherProductYield,selectedOtherProductDisplay,selectedOtherProductDisplayShortCode) {
                console.log(product);
                console.log(selectedOtherProductQuantity);

                selectedOtherProductDisplayShortCode = validateDisplayAttributes(selectedOtherProductDisplay,selectedOtherProductDisplayShortCode);
                if (product.category.code === 'SEMI_FINISHED') {
                    var hasRecipe = false;
                    var index = 0;
                    for (index = 0; index < $scope.scmProductsWithRecipe.length; index++) {
                        if ($scope.scmProductsWithRecipe[index].id == product.productId) {
                            hasRecipe = true;
                        }
                    }
                    if (!hasRecipe) {
                        bootbox.alert("Recipe for " + product.productName
                            + " does not exist please create recipe for the product before adding.");
                        return;
                    }
                }
                if ($scope.recipeDetail.ingredient == null) {
                    $scope.recipeDetail.ingredient = {};
                }
                if ($scope.recipeDetail.ingredient.components == null) {
                    $scope.recipeDetail.ingredient.components = [];
                }
                addToList($scope.recipeDetail.ingredient.components, product, selectedOtherProductQuantity,
                    selectedOtherProductCritical, selectedOtherProductYield, false,selectedOtherProductDisplay,selectedOtherProductDisplayShortCode);
                $("#addOtherSCMProductModal").modal("hide");
                // TODO convert to the product and add to the ingredient
                $scope.clearOtherSCMProduct();
            };

            function validateDisplayAttributes(selectedOtherProductDisplay,selectedOtherProductDisplayShortCode){
                console.log("Display Property:::::::: and display ShortCode ",selectedOtherProductDisplay,selectedOtherProductDisplayShortCode);

                if(selectedOtherProductDisplay !=undefined && selectedOtherProductDisplay !=null && selectedOtherProductDisplay){
                    if(selectedOtherProductDisplayShortCode ==undefined || selectedOtherProductDisplayShortCode ==null || selectedOtherProductDisplayShortCode.length ==0){
                        bootbox.alert("Please add display short code before submitting.");
                        return;
                    }
                }
                //if display prop is set to false
                if(selectedOtherProductDisplay !=undefined && selectedOtherProductDisplay !=null && selectedOtherProductDisplay === false){
                    selectedOtherProductDisplayShortCode = null;
                }
                return selectedOtherProductDisplayShortCode;
            }
            $scope.addAddonProduct = function (activeTab, addonProduct, selectedAddonQuantity,
                                               selectedAddonYield, selectedAddonCustomize,selectedAddonDisplay,selectedAddonDisplayShortCode) {
                $("#addAddonsProductModal").modal("hide");
                // TODO convert to the product and add to the ingredient
                if (activeTab == 3) {

                    addToList($scope.recipeDetail.addons, addonProduct, selectedAddonQuantity, false,
                        selectedAddonYield, selectedAddonCustomize);
                    console.log('Addon List', $scope.recipeDetail.addons);
                } else if (activeTab == 5) {

                    addToList($scope.recipeDetail.dineInConsumables, addonProduct, selectedAddonQuantity,
                        false, selectedAddonYield, false,selectedAddonDisplay,selectedAddonDisplayShortCode);
                } else if (activeTab == 6) {

                    addToList($scope.recipeDetail.deliveryConsumables, addonProduct, selectedAddonQuantity,
                        false, selectedAddonYield, false,selectedAddonDisplay,selectedAddonDisplayShortCode);
                } else if (activeTab == 7) {

                    addToList($scope.recipeDetail.takeawayConsumables, addonProduct, selectedAddonQuantity,
                        false, selectedAddonYield, false,selectedAddonDisplay,selectedAddonDisplayShortCode);
                }
                $scope.clearAddonProduct();
            };

            $scope.changeSelectedOptionType = function (selectedOptionType) {
                $scope.selectedOptionType = selectedOptionType;
            };


            $scope.changeSelectedOption = function (selectedOption) {
                $scope.selectedOption = selectedOption;
            };

            $scope.addOptionMenuProduct = function (activeTab, optionType, selectedOption) {

                if (selectedOption == null || selectedOption.name == null) {
                    return;
                }
                selectedOption.type = optionType.detail.type;

                $("#addOptionProductModal").modal("hide");
                for (var i in $scope.recipeDetail.options) {
                    if ($scope.recipeDetail.options[i].name == selectedOption.name) {
                        $scope.recipeDetail.options[i].type = selectedOption.type;
                        return;
                    }
                }
                if ($scope.recipeDetail.options == null) {
                    $scope.recipeDetail.options = [];
                }
                $scope.recipeDetail.options.push(selectedOption);
                console.log('Addon List', $scope.recipeDetail.options);
            };

            $scope.addOptionAddonProduct = function (activeTab, product) {

                if (product == null || product.name == null) {
                    return;
                }
                var selectedOption = {
                    id: product.id,
                    name: product.name,
                    code: product.name,
                    shortCode: product.shortCode,
                    type: 'PRODUCT',
                    productId: product.id
                };

                $("#addOptionAddonProductModal").modal("hide");
                for (var i in $scope.recipeDetail.options) {
                    if ($scope.recipeDetail.options[i].name == selectedOption.name) {
                        $scope.recipeDetail.options[i].type = selectedOption.type;
                        return;
                    }
                }
                if ($scope.recipeDetail.options == null) {
                    $scope.recipeDetail.options = [];
                }
                $scope.recipeDetail.options.push(selectedOption);
                console.log('Addon List', $scope.recipeDetail.options);
            };

            $scope.addAddonMenuProduct = function (activeTab, addonProduct, dispensedFlag, dispenseTag, selectedAddonQuantity,
                                                   selectedAddonYield, selectedAddonMenuCustomize, tag) {
                $scope.isAdd = false;

                if (typeof addonProduct === 'string') {
                    addonProduct = JSON.parse(addonProduct);
                }

                console.log('Active Tab : ', activeTab);
                console.log('Addon Product : ', addonProduct);
                console.log('Addon Quantity : ', selectedAddonQuantity);
                console.log('Addon Yield : ', selectedAddonYield);
                console.log('Addon Dispensed : ', dispensedFlag);
                console.log('Addon DispenseTag : ', dispenseTag);
                $("#addAddonsMenuProductModal").modal("hide");
                if ($scope.mandatoryOroptional === 'OPTIONAL') {
                    for (var i in $scope.recipeDetail.addons) {
                        if ($scope.recipeDetail.addons[i].product.productId == addonProduct.id) {
                            $scope.recipeDetail.addons[i].quantity = selectedAddonQuantity;
                            $scope.recipeDetail.addons[i].yield = selectedAddonYield;
                            $scope.recipeDetail.addons[i].customize = selectedAddonMenuCustomize;
                            $scope.recipeDetail.addons[i].tag = tag;
                            $scope.recipeDetail.addons[i].dispensed = dispensedFlag;
                            $scope.recipeDetail.addons[i].dispenseTag = dispenseTag;
                            return;
                        }
                    }
                    var content = createVariantMenuProduct(addonProduct, selectedAddonQuantity, selectedAddonYield, tag, dispensedFlag, dispenseTag);
                    $scope.recipeDetail.addons.push(content);
                    console.log('Addon List', $scope.recipeDetail.addons);
                }
                else {
                    for (var i in $scope.recipeDetail.mandatoryAddons) {
                        if ($scope.recipeDetail.mandatoryAddons[i].product.productId == addonProduct.id) {
                            $scope.recipeDetail.mandatoryAddons[i].quantity = selectedAddonQuantity;
                            $scope.recipeDetail.mandatoryAddons[i].yield = selectedAddonYield;
                            $scope.recipeDetail.mandatoryAddons[i].customize = selectedAddonMenuCustomize;
                            $scope.recipeDetail.mandatoryAddons[i].tag = tag;
                            $scope.recipeDetail.mandatoryAddons[i].dispensed = dispensedFlag;
                            $scope.recipeDetail.mandatoryAddons[i].dispenseTag = dispenseTag;
                            return;
                        }
                    }
                    if ($scope.recipeDetail.mandatoryAddons == null) {
                        $scope.recipeDetail.mandatoryAddons = [];
                    }
                    var content = createVariantMenuProduct(addonProduct, selectedAddonQuantity, selectedAddonYield, tag, dispensedFlag, dispenseTag);
                    $scope.recipeDetail.mandatoryAddons.push(content);
                    console.log('Mandatory Addon List', $scope.recipeDetail.mandatoryAddons);
                }
            };

            function addToList(list, addonProduct, selectedAddonQuantity, selectedOtherProductCritical,
                               selectedOtherProductYield, selectedAddonCustomize,selectedOtherProductDisplay,selectedOtherProductDisplayShortCode) {

                selectedOtherProductDisplayShortCode =validateDisplayAttributes(selectedOtherProductDisplay,selectedOtherProductDisplayShortCode);
                for (var i in list) {
                    if (list[i].product.productId == addonProduct.productId) {
                        list[i].quantity = selectedAddonQuantity;
                        list[i].critical = selectedOtherProductCritical;
                        list[i].yield = selectedOtherProductYield;
                        list[i].customize = selectedAddonCustomize;
                        if(selectedOtherProductDisplay !==undefined && selectedOtherProductDisplay !=null){
                            list[i].display=selectedOtherProductDisplay;
                        }
                        if(selectedOtherProductDisplayShortCode !=undefined && selectedOtherProductDisplayShortCode !=null){
                            list[i].displayCode=selectedOtherProductDisplayShortCode;
                        }
                        return;
                    }
                }
                var content = createVariantProduct(addonProduct);
                content.quantity = selectedAddonQuantity;
                content.critical = selectedOtherProductCritical;
                content.yield = selectedOtherProductYield;
                content.customize = selectedAddonCustomize;
                if(selectedOtherProductDisplay !=undefined && selectedOtherProductDisplay !=null){
                    content.display=selectedOtherProductDisplay;
                }
                if(selectedOtherProductDisplayShortCode !=undefined && selectedOtherProductDisplayShortCode !=null){
                    content.displayCode=selectedOtherProductDisplayShortCode;
                }
                console.log("Before pushing content to recipeDetail.ingredient.components::::::::::",content);
                list.push(content);
            }

            $scope.openOptionProduct = function () {
                $scope.clearOptionProduct();
                $("#addOptionProductModal").modal("show");
            };

            $scope.openOptionAddonProductModal = function () {
                $scope.clearOptionProduct();
                $("#addOptionAddonProductModal").modal("show");
            };

            $scope.clearOptionProduct = function () {
                $scope.isAdd = true;
                $scope.selectedOptionType = null;
                $scope.selectedOption = null;
            };

            $scope.openAddonMenuProduct = function (type) {
                console.log(type)
                $scope.clearAddonMenuProduct();
                $scope.mandatoryOroptional = type;
                $("#addAddonsMenuProductModal").modal("show");
            };

            $scope.editAddonMenuProduct = function (variant, type) {
                $scope.mandatoryOroptional = type;
                $scope.isAdd = false;
                $scope.onChangeSelectedAddonMenuProduct(variant.product.productId);
                $scope.selectedAddonMenuProductQuantity = variant.quantity;
                $scope.selectedAddonMenuProductYield = variant.yield;
                $scope.selectedAddonMenuCustomize = variant.customize;
                $scope.addOnTag = variant.tag;
                $scope.addonDispensedFlag = variant.dispensed;
                $scope.selectedAddonDispenseTag = variant.dispenseTag;
                $("#addAddonsMenuProductModal").modal("show");
            };

            $scope.clearAddonMenuProduct = function () {
                $scope.isAdd = true;
                $scope.selectedAddonMenuProduct = null;
                $scope.selectedAddonMenuProductId = null;
                $scope.selectedAddonMenuProductQuantity = null;
                $scope.selectedAddonMenuProductYield = null;
                $scope.addOnTag = null;
                $scope.addonDispensedFlag = false;
                $scope.selectedAddonDispenseTag = null;
            };

            $scope.onChangeSelectedAddonMenuProduct = function (productId) {
                $scope.selectedAddonMenuProduct = getDetail(productId, $scope.productsInfo);
            };

            $scope.openRecommendation = function () {
                //  $scope.clearRecommendation();
                $scope.isRecommendation = true;
                $("#recomendationsModal").modal("show");
            };

            $scope.editRecommendation = function (variant) {
                $scope.selectedRecommendationProduct = variant.product;
                //console.log(getDetail(variant.product.productId,$scope.productsInfo));
                $scope.isRecommendation = false;
                $scope.selectedDimension = variant.dimension;
                $scope.selectedRecommendationCustomize = variant.customize;
                $scope.selectedRecommendationYield = variant.yield;
                $("#recomendationsModal").modal("show");
            };

            $scope.onChangeProduct = function (product) {
                $scope.getRecommendationProductDimension = getDimensionDetail(product.dimensionProfileId, $scope.productDimension);

            }

            $scope.addRecommendation = function (activeTab, product, quantity, yield, customize) {
                var customize = customize == true ? true : false;
                $("#recomendationsModal").modal("hide");
                for (var i in $scope.recipeDetail.recommendations) {
                    if ($scope.recipeDetail.recommendations[i].product.productId == product.productId) {
                        $scope.recipeDetail.recommendations[i].quantity = quantity;
                        $scope.recipeDetail.recommendations[i].yield = yield;
                        $scope.recipeDetail.recommendations[i].customize = customize;
                        return;
                    }
                }
                if ($scope.recipeDetail.recommendations == null) {
                    $scope.recipeDetail.recommendations = [];
                }
                var content = createVariantRecommendation(product, quantity, yield, customize);
                $scope.recipeDetail.recommendations.push(content);
                $scope.clearRecommendation();
            }

            $scope.clearRecommendation = function () {
                $scope.isRecommendation = true;
                $("#select2-selectedRecommendationProductId-container").html('');
                $scope.selectedRecommendationProduct = null;
                $scope.selectedDimension = null;
                $scope.getRecommendationProductDimension = null
                $scope.selectedRecommendationYield = null;
                $scope.selectedRecommendationCustomize = false;

            }


            $scope.openAddonProduct = function () {
                $scope.clearAddonProduct();
                $("#addAddonsProductModal").modal("show");
            };

            $scope.editAddonProduct = function (variant) {
                $scope.isAdd = false;
                $scope.onChangeSelectedAddonProduct(variant.product.productId);
                $scope.selectedAddonQuantity = variant.quantity;
                $scope.selectedAddonDisplay=variant.display!=undefined && variant.display!=null ?variant.display:false;
                $scope.selectedAddonDisplayShortCode=variant.displayCode!=undefined && variant.displayCode !=null ? variant.displayCode : null;
                $("#addAddonsProductModal").modal("show");
            };

            $scope.clearAddonProduct = function () {
                $scope.isAdd = true;
                $scope.selectedAddonProduct = null;
                $scope.selectedAddonProductId = null;
                $scope.selectedAddonQuantity = null;
                $scope.selectedAddonCustomize = false;
                $scope.selectedAddonDisplayShortCode=null;
                $scope.selectedAddonDisplay=false;
            };
            $scope.onChangeSelectedAddonProduct = function (productId) {
                $scope.selectedAddonProduct = getVariantDetail(productId, $scope.scmProductsInfo);
            };

            $scope.selectVariantProduct = function (selectedProductId) {
                console.log('select variant product id : ' + selectedProductId);
                var p = getVariantDetail(selectedProductId, $scope.scmProductsInfo);
                if (!$scope.validateProduct(p)) {
                    return;
                }
                if (p.alias === null) {
                    p.alias = "";
                }
                $scope.selectedVariantProduct = p;
                console.log('select product detail : ', $scope.selectedVariantProduct);
            };

            $scope.addSelectedSCMProducts = function () {
                $scope.recipeDetail.ingredient.products.push($scope.selectedSCMProductForAdd);
            };
            $scope.addSelectedSCMProduct = function (p) {
                if (p == null) {
                    return;
                }

                var product = JSON.parse(p);
                console.log(product);
                if ($scope.selectedSCMProductForAdd == null) {
                    $scope.selectedSCMProductForAdd = {
                        category: getCategory(product.category.id),
                        status: 'ACTIVE',
                        details: []
                    }
                } else if ($scope.selectedSCMProductForAdd.category == undefined) {
                    $scope.selectedSCMProductForAdd.category = getCategory(product.category.id);
                    $scope.selectedSCMProductForAdd.status = 'ACTIVE';
                    $scope.selectedSCMProductForAdd.details = [];

                }
                var productDetail = {
                    product: createProductBasicDetailFromSCMProduct(product),
                    uom: product.unitOfMeasure
                };
                if ($scope.selectedSCMProductForAdd.details == null) {
                    $scope.selectedSCMProductForAdd.details = [];
                }
                $scope.selectedSCMProductForAdd.details.push(productDetail);
                $scope.selectedSCMProduct = null;
                console.log($scope.selectedSCMProductForAdd);

            };

            function getCategory(categoryId) {
                for (var index in $scope.scmMetadata.categoryDefinitions) {
                    if ($scope.scmMetadata.categoryDefinitions[index].categoryId == categoryId) {
                        var category = {
                            id: $scope.scmMetadata.categoryDefinitions[index].categoryId,
                            name: $scope.scmMetadata.categoryDefinitions[index].categoryName
                        };
                        console.log(category);
                        return category;
                    }
                }

            }

            $scope.addVariantProduct = function () {
                if ($scope.newVariantProductDisplayName != null && $scope.newVariantProductDisplayName.trim().length > 0) {
                    $scope.selectedVariantProduct.displayName = $scope.newVariantProductDisplayName;

                    if ($scope.selectedVariantProduct != null) {
                        $scope.recipeDetail.ingredient.variants
                            .push(createVariantProduct($scope.selectedVariantProduct));
                    }
                    console.log($scope.recipeDetail.ingredient.variants);
                    $("#addVariantModal").modal("hide");
                } else {
                    alert("Please add product display name.");
                }

            };

            function createVariantProduct(selectedVariantProduct) {
                console.log("SelectedVariantProduct::::::::",selectedVariantProduct)
                var variantProduct = {};
                variantProduct.product = createProductBasicDetailFromSCMProduct(selectedVariantProduct);
                variantProduct.product.displayName = selectedVariantProduct.displayName;
                variantProduct.uom = selectedVariantProduct.unitOfMeasure;
                variantProduct.status = 'ACTIVE';
                variantProduct.dispensed = selectedVariantProduct.dispensed;
                variantProduct.dispenseTag = selectedVariantProduct.dispenseTag;
                console.log("SelectedVariantProduct after setting other props::::::::",selectedVariantProduct)
                return variantProduct;

            }

            function createVariantMenuProduct(selectedVariantProduct, selectedAddonQuantity, selectedAddonYield, tag, dispenseFlag, dispenseTag) {
                var variantProduct = {};
                variantProduct.product = createProductBasicDetail(selectedVariantProduct);
                variantProduct.status = 'ACTIVE';
                variantProduct.dimension = $scope.selectedDimensionData;
                variantProduct.quantity = selectedAddonQuantity;
                variantProduct.yield = selectedAddonYield;
                variantProduct.dispensed = dispenseFlag;
                variantProduct.dispenseTag = dispenseTag;
                variantProduct.tag = tag;
                return variantProduct;

            }

            function createVariantRecommendation(product, quantity, yield, customize) {
                var variantProduct = {};
                variantProduct.product = createProductBasicDetail(product);
                variantProduct.status = 'ACTIVE';
                variantProduct.dimension = $scope.selectedDimension;
                variantProduct.quantity = quantity;
                variantProduct.yield = yield;
                variantProduct.customize = customize;
                return variantProduct;

            }

            $scope.addNewVarient = function () {
                $scope.newVariantProductDisplayName = null;
                $("#addVariantModal").modal("show");
            };

            $scope.addNewSCMProduct = function () {
                $scope.selectedSCMProductForAdd = null;
                $scope.selectedSCMProduct = null;
                $scope.selectedSCMCategoryId = null;
                $("#addSCMProductModal").modal("show");
            };

            $scope.selectSCMCategory = function () {

            };

            function createProductBasicDetail(product) {
                var data = {};
                if (product == null) {
                    return data;
                }
                data.productId = product.id;
                data.name = product.name;
                data.type = product.type;
                data.subType = product.subType;
                data.shortCode = product.shortCode;
                data.classification = product.classification;
                data.taxCode = product.taxCode;
                return data;
            }

            function convertSCMProductToBasicDetail(product) {
                var data = {};
                data.id = product.productId;
                data.name = product.productName;
                data.type = product.category.id;
                data.subType = product.subCategory.id;
                data.classification = 'SCM_PRODUCT';
                data.unitOfMeasure = product.unitOfMeasure;
                data.taxCode = product.code;
                return data;
            }

            function createSCMProductBasicDetail(product) {
                var data = {};
                data.productId = product.productId;
                data.name = product.productName;
                data.type = product.category.id;
                data.subType = product.subCategory.id;
                data.classification = 'SCM_PRODUCT';
                data.unitOfMeasure = product.unitOfMeasure;
                data.taxCode = product.code;
                return data;
            }

            function createDimensionDetail(dimension) {
                var data = {};
                data.infoId = dimension.id;
                data.name = dimension.name;
                data.code = dimension.code;
                data.shortCode = dimension.shortCode;
                data.type = dimension.type;
                data.status = dimension.status;
                return data;
            }

            function createProductBasicDetailFromSCMProduct(product) {
                var data = {};
                data.productId = product.productId;
                data.name = product.productName;
                data.variantLevelOrdering = product.variantLevelOrdering;
                return data;
            }

            $scope.removeElement = function (list, index) {
                list.splice(index, 1);
            };

            $scope.removeVariantSCMProduct = function (index) {
                if ($scope.recipeDetail.ingredient.products != null) {
                    $scope.recipeDetail.ingredient.products.splice(index, 1);
                }
            };

            $scope.editVariantSCMProduct = function (variant) {
                $scope.selectedSCMProductForAdd = variant;
                $scope.selectedSCMProductForAdd.editAction = true;
                $("#addSCMProductModal").modal("show");
            };

            $scope.addonFilter = function (item) {
                return item.classification === 'PAID_ADDON' || item === 'FREE_ADDON';
            };

            $scope.closeEditModal = function () {
                $scope.isForApproval = false;
                $scope.selectedRegion = null;
                $scope.loadingData = false;
                if ($scope.displayDifference != null || $scope.isForApproval) {
                    $scope.setCurrentDisplayRecipe("NEW");
                }
                $scope.displayTypes = ["ACTIVE","PROPOSED","COMPARE"];
            };

            $scope.showPreview = function (isEdit) {
                $scope.setRecipeCondimentInititalValue();
                $scope.currentUsingRecipe = null;
                $scope.recipeCostDetail = null;
                $scope.colneOfRecipeCostDetail = null;
                $scope.displayDifference = null;
                $scope.currentUsingRecipeDetail = null;
                var errors = [];
                for (var i = 1; i <= 6; i++) {
                    errors = errors.concat(validate(i, $scope.recipeDetail))
                }
                $scope.heroOrSuperComboCheck = true;
                if (errors.length != 0) {
                    alert(printErrors(errors));
                    return;
                }
                $scope.cloneOfRecipeDetail = angular.copy($scope.recipeDetail);
                $scope.isPreview = isEdit == false;
                if ($scope.selectedRegion == null) {
                    $scope.selectedRegion = "NCR";
                }
                $("#showPreviewModal").modal("show");
                $scope.selectedDisplay = "ACTIVE";
                $scope.getCostOfRecipes($scope.selectedRegion);
            };

            $scope.getCostOfRecipes = function (passedRegion, openedFromEdit, type) {
                if ($scope.recipeDetail != null && passedRegion != null) {
                    $rootScope.showFullScreenLoader = true;
                    $scope.selectedRegion = passedRegion;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.recipeManagement.recipeCostByDetailNew,
                        data: $scope.recipeDetail,
                        params: {
                            "region": $scope.selectedRegion
                        }
                    }).then(function success(response) {
                        $scope.recipeCostDetail = response.data;
                        $scope.colneOfRecipeCostDetail = response.data;
                        if ($scope.recipeDetail.status != "ACTIVE") {
                            var productRecipeKey = {
                                "productId": $scope.recipeDetail.product.productId,
                                "dimension": $scope.recipeDetail.dimension.code,
                                "profile": $scope.recipeDetail.profile
                            }
                            $http({
                                method: 'POST',
                                url: AppUtil.restUrls.recipeManagement.recipeCostByKey,
                                data: productRecipeKey,
                                params: {
                                    "region": $scope.selectedRegion
                                }
                            }).then(function success(response) {
                                if (response != null && response.status == 200 && response.data != undefined && response.data != null && response.data != "") {
                                    $scope.currentUsingRecipe = response.data;
                                    $scope.currentUsingRecipeDetail = null;
                                    for (var i = 0; i < $scope.existingRecipes.length; i++) {
                                        if ($scope.existingRecipes[i].recipeId === $scope.currentUsingRecipe.recipeId) {
                                            $scope.currentUsingRecipeDetail = $scope.existingRecipes[i];
                                            break;
                                        }
                                    }
                                    $scope.compareRecipeDetail = null;
                                    getDifferenceCost($scope.recipeCostDetail, $scope.currentUsingRecipe, function (passedDiff) {
                                        $scope.displayDifference = passedDiff;
                                        if (openedFromEdit === undefined || openedFromEdit === null) {
                                            $scope.setCurrentDisplayRecipe("OLD");
                                        }
                                    });
                                    $rootScope.showFullScreenLoader = false;
                                } else {
                                    $scope.currentUsingRecipe = null;
                                    $scope.compareRecipeDetail = null;
                                    getDifferenceCost($scope.recipeCostDetail, $scope.currentUsingRecipe, function (passedDiff) {
                                        $scope.displayDifference = passedDiff;
                                        if (type != undefined && type != null) {
                                            $scope.setCurrentDisplayRecipe(type);
                                        } else {
                                            $scope.setCurrentDisplayRecipe("OLD");
                                        }
                                    });
                                    $rootScope.showFullScreenLoader = false;
                                }
                            }, function error(response) {
                                $scope.currentUsingRecipe = null;
                                $scope.currentUsingRecipeDetail = null;
                                $scope.compareRecipeDetail = null;
                                console.log("error:" + response);
                                $rootScope.showFullScreenLoader = false;
                            });
                        } else {
                            $scope.loadingData = false;
                        }
                        $scope.compareRecipeDetail = null;
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $scope.loadingData = false;
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    $scope.loadingData = false;
                }
            };

            $scope.prepareCompareRecipeObject = function ( displayDiff, currentRecipeCost ,previousRecipeCost) {
                $scope.compareRecipeDetail = angular.copy($scope.cloneOfRecipeDetail);
                $scope.compareRecipeDetail.displayOnUi = false;
                $scope.currentActiveRecipe = null;
                for (var i=0;i<$scope.existingRecipes.length;i++) {
                    if ($scope.existingRecipes[i].profile === $scope.compareRecipeDetail.profile && $scope.existingRecipes[i].status === "ACTIVE") {
                        $scope.currentActiveRecipe = $scope.existingRecipes[i];
                        break;
                    }
                }
                if ($scope.currentActiveRecipe == null) {
                    alert("No Active Recipe Found to Compare....!");
                    $scope.loadingData = false;
                } else {
                    if (previousRecipeCost === undefined || previousRecipeCost === null) {
                        alert("No Recipe Found To Compare....!");
                        return;
                    }
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.orderManagement.getMonthlyOrdersOfRecipe,
                        params: {
                            "recipeId" : $scope.currentActiveRecipe.recipeId,
                            "region" : $scope.selectedRegion
                        }
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = true;
                        $scope.monthlyOrdersOfRecipe = response.data;
                        if (response.status === 200) {
                            $scope.monthlyOrdersOfRecipe = response.data;
                        } else {
                            alert("Something Went wrong while getting Last month Active Recipe Sales....!");
                            $scope.monthlyOrdersOfRecipe = {
                                "kettleOrdersQuantity" : 0,
                                "kettleOrdersSale" : 0
                            };
                        }

                        $scope.alreadyAddedProducts = {};
                        $scope.alreadyAddedDineIn = {};
                        $scope.alreadyAddedDelivery = {};
                        $scope.alreadyAddedTakeAway = {};
                        previousRecipeCost.type = "ACTIVE";
                        currentRecipeCost.type = "PROPOSED";

                        angular.forEach([previousRecipeCost, currentRecipeCost], function(currentOrPrevCost) {
                            angular.forEach(currentOrPrevCost.commonIngredient, function (pRecipeCost) {
                                var key = "common" + pRecipeCost.type + pRecipeCost.productId;
                                var copyOfpRecipeCost = angular.copy(pRecipeCost);
                                if ($scope.alreadyAddedProducts[key]) {
                                    copyOfpRecipeCost = $scope.alreadyAddedProducts[key];
                                    if (currentOrPrevCost.type ==="ACTIVE") {
                                        copyOfpRecipeCost.activeQuantity = pRecipeCost.quantity;
                                        copyOfpRecipeCost.activePrice = pRecipeCost.price;
                                        copyOfpRecipeCost.activeCost = pRecipeCost.cost;
                                        copyOfpRecipeCost.isInActive = true;
                                    } else {
                                        copyOfpRecipeCost.proposedQuantity = pRecipeCost.quantity;
                                        copyOfpRecipeCost.proposedPrice =  pRecipeCost.price;
                                        copyOfpRecipeCost.proposedCost = pRecipeCost.cost;
                                        copyOfpRecipeCost.isInProposed = true;
                                    }
                                } else {
                                    if (currentOrPrevCost.type ==="ACTIVE") {
                                        copyOfpRecipeCost.proposedQuantity = "-";
                                        copyOfpRecipeCost.proposedPrice = "-";
                                        copyOfpRecipeCost.proposedCost = "-";
                                        copyOfpRecipeCost.activeQuantity = pRecipeCost.quantity;
                                        copyOfpRecipeCost.activePrice = pRecipeCost.price;
                                        copyOfpRecipeCost.activeCost = pRecipeCost.cost;
                                        copyOfpRecipeCost.isInProposed = false;
                                        copyOfpRecipeCost.isInActive = true;
                                    } else {
                                        copyOfpRecipeCost.proposedQuantity = pRecipeCost.quantity;
                                        copyOfpRecipeCost.proposedPrice =  pRecipeCost.price;
                                        copyOfpRecipeCost.proposedCost = pRecipeCost.cost;
                                        copyOfpRecipeCost.activeQuantity = "-";
                                        copyOfpRecipeCost.activePrice = "-";
                                        copyOfpRecipeCost.activeCost = "-";
                                        copyOfpRecipeCost.isInProposed = true;
                                        copyOfpRecipeCost.isInActive = false;
                                    }
                                }
                                $scope.alreadyAddedProducts[key] = copyOfpRecipeCost;
                            });

                           angular.forEach(currentOrPrevCost.categoryCost, function (categoryCost) {

                                    angular.forEach(categoryCost.ingredients, function (dineInOrDelOrTakeAway) {
                                        var key = categoryCost.costType + dineInOrDelOrTakeAway.type + dineInOrDelOrTakeAway.productId;
                                        var copyOfpRecipeCost = angular.copy(dineInOrDelOrTakeAway);
                                        var checkType = categoryCost.costType === "CAFE" ? $scope.alreadyAddedDineIn : (categoryCost.costType === "DELIVERY" ? $scope.alreadyAddedDelivery : $scope.alreadyAddedTakeAway);
                                        if (checkType[key]) {
                                            copyOfpRecipeCost = checkType[key];
                                            if (currentOrPrevCost.type ==="ACTIVE") {
                                                copyOfpRecipeCost.activeQuantity = dineInOrDelOrTakeAway.quantity;
                                                copyOfpRecipeCost.activePrice = dineInOrDelOrTakeAway.price;
                                                copyOfpRecipeCost.activeCost = dineInOrDelOrTakeAway.cost;
                                                copyOfpRecipeCost.isInActive = true;
                                            } else {
                                                copyOfpRecipeCost.proposedQuantity = dineInOrDelOrTakeAway.quantity;
                                                copyOfpRecipeCost.proposedPrice =  dineInOrDelOrTakeAway.price;
                                                copyOfpRecipeCost.proposedCost = dineInOrDelOrTakeAway.cost;
                                                copyOfpRecipeCost.isInProposed = true;
                                            }
                                        } else {
                                            if (currentOrPrevCost.type ==="ACTIVE") {
                                                copyOfpRecipeCost.proposedQuantity = "-";
                                                copyOfpRecipeCost.proposedPrice = "-";
                                                copyOfpRecipeCost.proposedCost = "-";
                                                copyOfpRecipeCost.activeQuantity = dineInOrDelOrTakeAway.quantity;
                                                copyOfpRecipeCost.activePrice = dineInOrDelOrTakeAway.price;
                                                copyOfpRecipeCost.activeCost = dineInOrDelOrTakeAway.cost;
                                                copyOfpRecipeCost.isInProposed = false;
                                                copyOfpRecipeCost.isInActive = true;
                                            } else {
                                                copyOfpRecipeCost.proposedQuantity = dineInOrDelOrTakeAway.quantity;
                                                copyOfpRecipeCost.proposedPrice =  dineInOrDelOrTakeAway.price;
                                                copyOfpRecipeCost.proposedCost = dineInOrDelOrTakeAway.cost;
                                                copyOfpRecipeCost.activeQuantity = "-";
                                                copyOfpRecipeCost.activePrice = "-";
                                                copyOfpRecipeCost.activeCost = "-";
                                                copyOfpRecipeCost.isInProposed = true;
                                                copyOfpRecipeCost.isInActive = false;
                                            }
                                        }
                                        if (categoryCost.costType === "CAFE") {
                                            $scope.alreadyAddedDineIn[key] = copyOfpRecipeCost;
                                        } else if (categoryCost.costType === "DELIVERY") {
                                            $scope.alreadyAddedDelivery[key] = copyOfpRecipeCost;
                                        } else {
                                            $scope.alreadyAddedTakeAway[key] = copyOfpRecipeCost;
                                        }
                                    });
                            });
                        });

                        $scope.addedIngredients = {};
                        $scope.addonProducts = {};
                        $scope.mandatoryAddonProducts = {};
                        $scope.paidAddonProducts = {};
                        $scope.otherProducts = {};

                        angular.forEach([$scope.currentActiveRecipe, $scope.compareRecipeDetail], function (currentComparingRecipe) {
                        if (currentComparingRecipe.ingredient != null && currentComparingRecipe.ingredient.variants != null && currentComparingRecipe.ingredient.variants.length > 0) {
                            angular.forEach(currentComparingRecipe.ingredient.variants, function(variant) {
                                var key = variant.product.productId;
                                var copyOfVariant = angular.copy(variant);
                                if ($scope.addedIngredients[key]) {
                                    copyOfVariant = $scope.addedIngredients[key];
                                    if (currentComparingRecipe.status ==="ACTIVE") {
                                        copyOfVariant.activeCritical = variant.critical;
                                        copyOfVariant.activeCustomizable = variant.customize;
                                        copyOfVariant.activeDetails = variant.details;
                                        copyOfVariant.isInActive = true;
                                    } else {
                                        copyOfVariant.proposedCritical = variant.critical;
                                        copyOfVariant.proposedCustomizable = variant.customize;
                                        copyOfVariant.proposedDetails = variant.details;
                                        copyOfVariant.isInProposed = true;
                                    }
                                } else {
                                    if (currentComparingRecipe.status ==="ACTIVE") {
                                        copyOfVariant.proposedCritical = "-";
                                        copyOfVariant.proposedCustomizable = "-";
                                        copyOfVariant.proposedDetails = "-";
                                        copyOfVariant.activeCritical = variant.critical;
                                        copyOfVariant.activeCustomizable = variant.customize;
                                        copyOfVariant.activeDetails = variant.details;
                                        copyOfVariant.isInProposed = false;
                                        copyOfVariant.isInActive = true;
                                    } else {
                                        copyOfVariant.proposedCritical = variant.critical;
                                        copyOfVariant.proposedCustomizable = variant.customize;
                                        copyOfVariant.proposedDetails = variant.details;
                                        copyOfVariant.activeCritical = "-";
                                        copyOfVariant.activeCustomizable = "-";
                                        copyOfVariant.activeDetails = "-";
                                        copyOfVariant.isInProposed = true;
                                        copyOfVariant.isInActive = false;
                                    }
                                }
                                $scope.addedIngredients[key] = copyOfVariant;
                            });
                        }

                            if (currentComparingRecipe.addons != null && currentComparingRecipe.addons.length > 0) {
                                angular.forEach(currentComparingRecipe.addons, function(variant) {
                                    var key = variant.product.productId;
                                    var copyOfVariant = angular.copy(variant);
                                    if ($scope.addonProducts[key]) {
                                        copyOfVariant = $scope.addonProducts[key];
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.activeQuantity = variant.quantity;
                                            copyOfVariant.activeTag = variant.tag;
                                            copyOfVariant.activeYield = variant.yield;
                                            copyOfVariant.activeCritical = variant.critical;
                                            copyOfVariant.activeCustomize = variant.customize;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.proposedQuantity = variant.quantity;
                                            copyOfVariant.proposedTag = variant.tag;
                                            copyOfVariant.proposedYield = variant.yield;
                                            copyOfVariant.proposedCritical = variant.critical;
                                            copyOfVariant.proposedCustomize = variant.customize;
                                            copyOfVariant.isInProposed = true;
                                        }
                                    } else {
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.proposedQuantity = "-";
                                            copyOfVariant.proposedTag = "-";
                                            copyOfVariant.proposedYield = "-";
                                            copyOfVariant.proposedCritical = "-";
                                            copyOfVariant.proposedCustomize = "-";
                                            copyOfVariant.activeQuantity = variant.quantity;
                                            copyOfVariant.activeTag = variant.tag;
                                            copyOfVariant.activeYield = variant.yield;
                                            copyOfVariant.activeCritical = variant.critical;
                                            copyOfVariant.activeCustomize = variant.customize;
                                            copyOfVariant.isInProposed = false;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.proposedQuantity = variant.quantity;
                                            copyOfVariant.proposedTag = variant.tag;
                                            copyOfVariant.proposedYield = variant.yield;
                                            copyOfVariant.proposedCritical = variant.critical;
                                            copyOfVariant.proposedCustomize = variant.customize;
                                            copyOfVariant.activeQuantity = "-";
                                            copyOfVariant.activeTag = "-";
                                            copyOfVariant.activeYield = "-";
                                            copyOfVariant.activeCritical = "-";
                                            copyOfVariant.activeCustomize = "-";
                                            copyOfVariant.isInProposed = true;
                                            copyOfVariant.isInActive = false;
                                        }
                                    }
                                    $scope.addonProducts[key] = copyOfVariant;
                                });
                            }

                            if (currentComparingRecipe.mandatoryAddons != null && currentComparingRecipe.mandatoryAddons.length > 0) {
                                angular.forEach(currentComparingRecipe.mandatoryAddons, function(variant) {
                                    var key = variant.product.productId;
                                    var copyOfVariant = angular.copy(variant);
                                    if ($scope.mandatoryAddonProducts[key]) {
                                        copyOfVariant = $scope.mandatoryAddonProducts[key];
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.activeQuantity = variant.quantity;
                                            copyOfVariant.activeTag = variant.tag;
                                            copyOfVariant.activeYield = variant.yield;
                                            copyOfVariant.activeCustomize = variant.customize;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.proposedQuantity = variant.quantity;
                                            copyOfVariant.proposedTag = variant.tag;
                                            copyOfVariant.proposedYield = variant.yield;
                                            copyOfVariant.proposedCustomize = variant.customize;
                                            copyOfVariant.isInProposed = true;
                                        }
                                    } else {
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.proposedQuantity = "-";
                                            copyOfVariant.proposedTag = "-";
                                            copyOfVariant.proposedYield = "-";
                                            copyOfVariant.proposedCustomize = "-";
                                            copyOfVariant.activeQuantity = variant.quantity;
                                            copyOfVariant.activeTag = variant.tag;
                                            copyOfVariant.activeYield = variant.yield;
                                            copyOfVariant.activeCustomize = variant.customize;
                                            copyOfVariant.isInProposed = false;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.proposedQuantity = variant.quantity;
                                            copyOfVariant.proposedTag = variant.tag;
                                            copyOfVariant.proposedYield = variant.yield;
                                            copyOfVariant.proposedCustomize = variant.customize;
                                            copyOfVariant.activeQuantity = "-";
                                            copyOfVariant.activeTag = "-";
                                            copyOfVariant.activeYield = "-";
                                            copyOfVariant.activeCustomize = "-";
                                            copyOfVariant.isInProposed = true;
                                            copyOfVariant.isInActive = false;
                                        }
                                    }
                                    $scope.mandatoryAddonProducts[key] = copyOfVariant;
                                });
                            }

                            if (currentComparingRecipe.options != null && currentComparingRecipe.options.length > 0) {
                                angular.forEach(currentComparingRecipe.options, function(variant) {
                                    var key = variant.name;
                                    var copyOfVariant = angular.copy(variant);
                                    if ($scope.paidAddonProducts[key]) {
                                        copyOfVariant = $scope.paidAddonProducts[key];
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.isInProposed = true;
                                        }
                                    } else {
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.isInProposed = false;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.isInProposed = true;
                                            copyOfVariant.isInActive = false;
                                        }
                                    }
                                    $scope.paidAddonProducts[key] = copyOfVariant;
                                });
                            }

                            if (currentComparingRecipe.ingredient != null && currentComparingRecipe.ingredient.components != null && currentComparingRecipe.ingredient.components.length > 0) {
                                angular.forEach(currentComparingRecipe.ingredient.components, function(variant) {
                                    var key = variant.product.productId;
                                    var copyOfVariant = angular.copy(variant);
                                    if ($scope.otherProducts[key]) {
                                        copyOfVariant = $scope.otherProducts[key];
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.activeQuantity = variant.quantity;
                                            copyOfVariant.activeCritical = variant.critical;
                                            copyOfVariant.activeCustomize = variant.customize;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.proposedQuantity = variant.quantity;
                                            copyOfVariant.proposedCritical = variant.critical;
                                            copyOfVariant.proposedCustomize = variant.customize;
                                            copyOfVariant.isInProposed = true;
                                        }
                                    } else {
                                        if (currentComparingRecipe.status ==="ACTIVE") {
                                            copyOfVariant.activeQuantity = variant.quantity;
                                            copyOfVariant.activeCritical = variant.critical;
                                            copyOfVariant.activeCustomize = variant.customize;
                                            copyOfVariant.proposedQuantity = "-";
                                            copyOfVariant.proposedCritical = "-";
                                            copyOfVariant.proposedCustomize = "-";
                                            copyOfVariant.isInProposed = false;
                                            copyOfVariant.isInActive = true;
                                        } else {
                                            copyOfVariant.proposedQuantity = variant.quantity;
                                            copyOfVariant.proposedCritical = variant.critical;
                                            copyOfVariant.proposedCustomize = variant.customize;
                                            copyOfVariant.activeQuantity = "-";
                                            copyOfVariant.activeCritical = "-";
                                            copyOfVariant.activeCustomize = "-";
                                            copyOfVariant.isInProposed = true;
                                            copyOfVariant.isInActive = false;
                                        }
                                    }
                                    $scope.otherProducts[key] = copyOfVariant;
                                });
                            }
                        });

                        $scope.compareRecipeDetail.commonIngredient = Object.values($scope.alreadyAddedProducts);
                        $scope.compareRecipeDetail.dineInProds = Object.values($scope.alreadyAddedDineIn);
                        $scope.compareRecipeDetail.deliveryProds = Object.values($scope.alreadyAddedDelivery);
                        $scope.compareRecipeDetail.takeAwayProds = Object.values($scope.alreadyAddedTakeAway);
                        $scope.compareRecipeDetail.ingredientVariants = Object.values($scope.addedIngredients);
                        $scope.compareRecipeDetail.addonProducts = Object.values($scope.addonProducts);
                        $scope.compareRecipeDetail.mandatoryAddonProducts = Object.values($scope.mandatoryAddonProducts);
                        $scope.compareRecipeDetail.paidAddonProducts = Object.values($scope.paidAddonProducts);
                        $scope.compareRecipeDetail.otherProducts = Object.values($scope.otherProducts);
                        $rootScope.showFullScreenLoader = false;
                        $scope.compareRecipeDetail.displayOnUi = true;
                        $scope.loadingData = false;

                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                        $scope.loadingData = false;
                        alert("Error While Getting Monthly Cost Of Recipes");
                    });
                }
            };

            $scope.getCostTypeProducts = function (costType, compareRecipe) {
                if (compareRecipe != undefined && compareRecipe != null) {
                    if (costType === 'CAFE') {
                        return compareRecipe.dineInProds || [];
                    } else if (costType === 'DELIVERY') {
                        return compareRecipe.deliveryProds || [];
                    } else {
                        return compareRecipe.takeAwayProds || [];
                    }
                } else {
                    return [];
                }
            };

            $scope.setCurrentDisplayRecipe = function (type, isRefresh) {
                $scope.currentDisplayRecipe = type;
                if (type === "OLD") {
                    $scope.recipeCostDetail = $scope.currentUsingRecipe;
                    $scope.recipeDetail = $scope.currentUsingRecipeDetail;
                } else {
                    $scope.recipeCostDetail = $scope.colneOfRecipeCostDetail;
                    $scope.recipeDetail = $scope.cloneOfRecipeDetail;
                }

                if (isRefresh && $scope.recipeDetail != null) {
                    $scope.getCostOfRecipes($scope.selectedRegion, true, type);
                } else {
                    $scope.loadingData = false;
                }
            };

            function getDifferenceCost(currentRecipe,previousRecipe, callBack) {
                var result = {};
                if (currentRecipe != null && currentRecipe.categoryCost != null) {
                    for (var i=0;i<currentRecipe.categoryCost.length;i++) {
                        var name = "current"+currentRecipe.categoryCost[i].costType;
                        result[name] = currentRecipe.categoryCost[i].cost;
                    }
                }
                if (previousRecipe != null && previousRecipe.categoryCost != null) {
                    for (var j=0;j<previousRecipe.categoryCost.length;j++) {
                        var name = "previous"+previousRecipe.categoryCost[j].costType;
                        result[name] = previousRecipe.categoryCost[j].cost;
                        var current = "current"+previousRecipe.categoryCost[j].costType;
                        if (result[current] != undefined && result[current] != null) {
                            var diff = "diff"+previousRecipe.categoryCost[j].costType;
                            var costDiff = ((result[current] - previousRecipe.categoryCost[j].cost)/previousRecipe.categoryCost[j].cost)*100;
                            result[diff] =  costDiff;
                        }
                    }
                }
                console.log("result is : ",result);
                if ($scope.selectedDisplay === "COMPARE") {
                    $scope.prepareCompareRecipeObject(result,currentRecipe,previousRecipe);
                } else {
                    $scope.loadingData = false;
                }
                callBack(result);
            }

            $scope.sendRecipeForApproval = function (recipe, isSentForApproval) {
                bootbox.confirm( isSentForApproval ? "Are you Sure you want to Send For Approval?"
                    : "Are You Sure You Want to Approve ?", function (result) {
                    if (result) {
                        var data = AppUtil.getUserValues();
                        if (data != undefined && data != null) {
                            recipe.lastUpdatedByName = data.user.name;
                            recipe.lastUpdatedById = data.user.id;
                        }
                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.recipeManagement.approveRecipe,
                            params: {
                                sentForApproval: isSentForApproval
                            },
                            data: {
                                recipeDetail: recipe,
                                recipeUpdateLogDetail: {updatedBy: AppUtil.getCurrentUser()},
                            }
                        }).then(function success(response) {
                            var result = response.data;
                            $rootScope.showFullScreenLoader = false;
                            if (result.errorMessage != null) {
                                bootbox.alert(result.errorMessage);
                            } else {
                                bootbox.alert(isSentForApproval ? "Recipe Sent For Approval" : "Recipe Approved Successfully..!");
                                if (!isSentForApproval) {
                                    $("#showPreviewModal").modal("hide");
                                    $scope.isForApproval =false;
                                }
                                $scope.clearAll(false);
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                            $rootScope.showFullScreenLoader = false;
                        });
                    }
                });
            };

            $scope.rejectRecipeForApproval = function (recipe, isSentForRejection) {
                bootbox.confirm( !isSentForRejection ? "Are you Sure you want to Cancel Request For Approval..!"
                    : "Are You Sure You Want to Reject ?", function (result) {
                    if (result) {
                        var data = AppUtil.getUserValues();
                        if (data != undefined && data != null) {
                            recipe.lastUpdatedByName = data.user.name;
                            recipe.lastUpdatedById = data.user.id;
                        }
                        $http({
                            method: 'POST',
                            url: AppUtil.restUrls.recipeManagement.rejectRecipe,
                            params: {
                                sentForRejection: isSentForRejection
                            },
                            data: {
                                recipeDetail: recipe,
                                recipeUpdateLogDetail: {updatedBy: AppUtil.getCurrentUser()},
                            }
                        }).then(function success(response) {
                            var result = response.data;
                            $rootScope.showFullScreenLoader = false;
                            if (result.errorMessage != null) {
                                bootbox.alert(result.errorMessage);
                            } else {
                                bootbox.alert(isSentForRejection ? "Recipe Rejected..!" : "Cancelled Request For Approval Successfully..!");
                                $scope.clearAll(false);
                            }
                            if (isSentForRejection) {
                                $("#showPreviewModal").modal("hide");
                                $scope.isForApproval =false;
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                            $rootScope.showFullScreenLoader = false;
                        });
                    }
                });
            };

            $scope.getAllRegions = function() {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.regions
                }).then(function success(response) {
                    $scope.allRegions = response.data;
                    $scope.setSelectedRegion("NCR");
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.allRegions = [];
                    $scope.setSelectedRegion(null);
                });
            };

            $scope.setSelectedRegion = function (region) {
                $scope.selectedRegion = region;
            };

            $scope.saveRecipe = function () {

            var data=AppUtil.getUserValues();
                if (data != undefined && data != null) {
                    $scope.recipeDetail.lastUpdatedByName = data.user.name;
                    $scope.recipeDetail.lastUpdatedById = data.user.id;
                }
                if ($scope.isHeroCombo) {
                    $scope.recipeDetail.ingredient.compositeProduct.details[0].discount = 0;
                }
                $rootScope.showFullScreenLoader = true;
                var url = null;
                var action = "ADD";
                var reqObj = $scope.recipeDetail;
                if ($scope.recipeDetail.recipeId != null && $scope.recipeDetail.recipeId > 0) {
                    action = "UPDATE";
                    url = AppUtil.restUrls.recipeManagement.updateRecipe;
                    reqObj = {
                        recipeDetail: $scope.recipeDetail,
                        recipeUpdateLogDetail: {updatedBy: AppUtil.getCurrentUser()}
                    };
                } else {
                    url = AppUtil.restUrls.recipeManagement.addRecipe;
                    reqObj.status = "IN_PROGRESS";
                }
                $http({
                    method: 'POST',
                    url: url,
                    data: reqObj
                }).then(function success(response) {
                    if (response.data.errorMessage != null) {
                        bootbox.alert(response.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Recipe " + (action === "ADD" ? "added" : "updated") + " Successfully");
                        $scope.clearAll(false);
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
                $("#showPreviewModal").modal("hide");
            };
            $scope.validateVariantAliases = function () {
                var errors = [];
                var hasDefault = false;
                if ($scope.selectedVariantProductForEdit != null
                    && $scope.selectedVariantProductForEdit.product != null) {
                    if ($scope.selectedVariantProductForEdit.product.displayName == null ||
                        $scope.selectedVariantProductForEdit.product.displayName.trim().length == 0) {
                        addError(errors, -1, 'Please add product display name.');
                    }
                }
                if ($scope.selectedVariantProductForEdit != null
                    && $scope.selectedVariantProductForEdit.details != null) {
                    for (var index in $scope.selectedVariantProductForEdit.details) {
                        if ($scope.selectedVariantProductForEdit.details[index].defaultSetting) {
                            hasDefault = true;
                        }
                        if ($scope.selectedVariantProductForEdit.details[index].alias == null) {
                            addError(errors, index, 'Alias Name Cannot Be Empty');
                        }
                        if ($scope.selectedVariantProductForEdit.details[index].quantity == null) {
                            addError(errors, index, 'Alias Quantity Cannot Be Empty');
                        }
                    }
                    if ($scope.selectedVariantProductForEdit.details.length > 0 && !hasDefault) {
                        addError(errors, -1, 'Atmost one alias should be default');
                    }
                }
                if (errors.length == 0) {
                    $("#addVariantAliasModal").modal("hide");
                } else {
                    alert(printErrors(errors));
                }
            };

            $scope.validateVariantSCMProduct = function () {
                var errors = [];
                var hasDefault = false;
                if ($scope.selectedSCMProductForAdd != null && $scope.selectedSCMProductForAdd.details != null) {
                    for (var index in $scope.selectedSCMProductForAdd.details) {
                        if ($scope.selectedSCMProductForAdd.details[index].defaultSetting) {
                            hasDefault = true;
                        }
                        if ($scope.selectedSCMProductForAdd.details[index].product.name == null) {
                            addError(errors, index, 'Alias Name Cannot Be Empty');
                        }
                        if ($scope.selectedSCMProductForAdd.details[index].quantity == null) {
                            addError(errors, index, 'Alias Quantity Cannot Be Empty');
                        }
                    }
                    console.log(hasDefault);
                    if ($scope.selectedSCMProductForAdd.details.length > 0 && !hasDefault) {
                        addError(errors, -1, 'Atmost one alias should be default');
                    }
                }
                if (errors.length == 0) {
                    $("#addSCMProductModal").modal("hide");
                    if ($scope.selectedSCMProductForAdd.details.length > 0) {
                        if ($scope.selectedSCMProductForAdd.editAction != undefined
                            && $scope.selectedSCMProductForAdd.editAction == true) {
                            // edit action
                        } else {
                            $scope.recipeDetail.ingredient.products.push($scope.selectedSCMProductForAdd);
                        }
                    }
                } else {
                    alert(printErrors(errors));
                }
            };

            function printErrors(errors) {
                var data = null;
                if (errors != null) {
                    data = 'Errors : ';
                    for (var index in errors) {
                        data = data + 'Row Number : ' + errors[index].index + ' - ' + errors[index].error + ',';

                    }
                }
                return data;

            }

            function addError(array, index, error) {
                array.push(getErrorMessage(index, error));
            }

            function getErrorMessage(index, error) {
                var obj = {
                    index: index,
                    error: error
                };
                return obj;
            }

            $scope.removeVariant = function (index) {
                $scope.recipeDetail.ingredient.variants.splice(index, 1);
            };

            $scope.printVariantDetails = function (details) {
                var data = '';
                if (details != null) {
                    for (var index in details) {
                        if (details[index].defaultSetting) {
                            data = data + '**';
                        }
                        data = data + details[index].alias + '(' + details[index].quantity + ', '
                            + details[index].yield + '),';

                    }
                }
                return data;
            };

            $scope.printTag = function (details) {
                var data = '';
                if (details != null) {
                    for (var index in details) {
                        if (details[index].defaultSetting) {
                            data = data + '**';
                        }
                        data = data + details[index].tag + ",\n";
                    }
                }
                return data;
            }

            $scope.printInstruction = function (details) {
                var data = '';
                if (details != null) {
                    for (var index in details) {
                        if (details[index].defaultSetting) {
                            data = data + '**';
                        }
                        if( details[index].instruction != null) {
                            data = data + details[index].instruction.name + ",\n";
                        }
                    }
                }
                return data;
            }

            $scope.printDescription = function (details) {
                var data = '';
                if (details != null) {
                    for (var index in details) {
                        if (details[index].defaultSetting) {
                            data = data + '**';
                        }
                        data = data + details[index].desc + ",\n";

                    }
                }
                return data;
            }

            $scope.printSCMProductDetails = function (details) {
                var data = '';
                if (details != null) {
                    for (var index in details) {
                        if (details[index].defaultSetting) {
                            data = data + '**';
                        }
                        data = data + details[index].product.name + '(' + details[index].quantity + ', '
                            + details[index].yield + '),';

                    }
                }
                return data;
            };

            /*
			 * $scope.markDefaultVariantAlias = function(index) { if
			 * ($scope.selectedVariantProductForEdit != null) { markDefault(
			 * $scope.selectedVariantProductForEdit.details, index); } }
			 *
			 * $scope.markDefaultSCMProduct = function(index) { if
			 * ($scope.selectedSCMProductForAdd != null) { markDefault(
			 * $scope.selectedSCMProductForAdd.details, index); } }
			 */

            $scope.markDefault = function (list, index) {
                if (list == null) {
                    return;
                }
                for (var i in list) {
                    if (i == index) {
                        list[i].defaultSetting = true;
                    } else {
                        list[i].defaultSetting = false;
                    }
                }
            };
            $scope.addAnotherVariantAlias = function () {
                if ($scope.selectedVariantProductForEdit.details == null) {
                    $scope.selectedVariantProductForEdit.details = [];
                }

                $scope.selectedVariantProductForEdit.details.push({
                    productId: $scope.selectedVariantProductForEdit.product.productId,
                    uom: $scope.selectedVariantProductForEdit.uom,
                    defaultSetting: false
                });
            };

            $scope.addVariantAliases = function (variant) {
                console.log(variant)
                $scope.selectedVariantProductForEdit = variant;
                $("#addVariantAliasModal").modal("show");

            };

            $scope.setSelectedOtherProduct = function (otherSCMProduct) {
                $scope.selectedOtherSCMProduct = JSON.parse(otherSCMProduct);
            };

            $scope.setSelectedAddonProduct = function (addonProduct) {
                $scope.selectedAddonProduct = JSON.parse(addonProduct);
            };
            $scope.setSelectedAddonMenuProduct = function (addonProduct) {
                $scope.selectedAddonMenuProduct = JSON.parse(addonProduct);
            };

            $scope.setDefaultQVMVariant = function (details, detail) {
                details.map(function (d) {
                    if (d.alias == detail.alias) {
                        d.defaultSetting = true;
                    } else {
                        d.defaultSetting = false;
                    }
                })
            };

            $scope.selectQVMAddon = function (addon) {
                if (addon.defaultSetting == true) {
                    addon.defaultSetting = false;
                } else {
                    addon.defaultSetting = true;
                }
            };

            $scope.removeDiscount = function (detail) {
                detail.discount = null;
            };

            $scope.removeInternalDiscount = function (detail) {
                detail.internalDiscountType = null;
                detail.internalDiscount = null;
            };

            // Generic Function
            function getDetail(id, list) {
                for (var index in list) {
                    if (list[index].id == id) {
                        return list[index];
                    }
                }
                return null;
            }

            // Generic Function
            function getVariantDetail(id, list) {
                for (var index in list) {
                    if (list[index].productId == id) {
                        return list[index];
                    }
                }
                return null;
            }

            function getDimensionDetail(id, list) {
                for (var index in list) {
                    if (list[index].detail.id == id) {
                        return list[index];
                    }
                }
                return null;
            }
        }).directive('addonMenuProductDirective', function () {
    return {
        restrict: 'E',
        scope: {
            addonList: "=addonList",
            mandatoryAddonList: "=mandatoryAddonList",
            openAddonMenuProduct: '&openAddonMenuProduct',
            editAddonMenuProduct: '&editAddonMenuProduct',
            cloneRecipeProfile: '=cloneRecipeProfile'
        },
        templateUrl: 'addonMenuProductDirectiveTemplate.html'
    };
}).directive('optionsProductDirective', function () {
    return {
        restrict: 'E',
        scope: {
            optionList: "=optionList",
            openOptionProduct: '&openOptionProduct',
            openOptionAddonProductModal: '&openOptionAddonProductModal',
            cloneRecipeProfile: '=cloneRecipeProfile'
        },
        templateUrl: 'optionProductDirectiveTemplate.html'
    };
}).directive('addonDirective', function () {
    return {
        restrict: 'E',
        scope: {
            addonList: "=addonList",
            openAddonProduct: '&openAddonProduct',
            editAddonProduct: '&editAddonProduct'
        },
        templateUrl: 'addonDirectiveTemplate.html'
    };
}).directive('recommendationDirective', function () {
    return {
        restrict: 'E',
        scope: {
            recommendationList: "=recommendationList",
            openRecommendation: '&openRecommendation',
            editRecommendation: '&editRecommendation'
        },
        templateUrl: 'recommendationDirectiveTemplate.html'
    };
}).directive('condimentDirective', function () {
    return {
        restrict: 'E',
        templateUrl: 'condimentDirectiveTemplate.html'
    };
});
