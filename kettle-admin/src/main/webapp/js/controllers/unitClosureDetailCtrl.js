/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("unitClosureDetailCtrl",
    function ($rootScope, $scope, $window, $http, $location, AppUtil, $cookieStore,$uibModal,fileService) {
        $rootScope.showFullScreenLoader = true;

        $scope.init = function () {
            
            $scope.selectedClosureStatus = null;
            $scope.idSelected = null;
            $scope.groupSelected = true;
            $scope.validation= {};
            $scope.isUnitClosure={};
            $scope.unitStateStatusLoaderFlag = {};
            $scope.unitClosureFormDataResponse = [];
            $scope.showUnitClosureForm = false;
            $scope.errorList = [];
            $scope.completedCount = 0;
            $scope.requiredTaskCount = 0;
            $scope.totalRequiredTask=0;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitClosure.getClosureStatus
            }).then(function success(response) {
                $scope.statusList = response.data;
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };

         $scope.setSelectedRow = function (idSelected) {
             if($scope.selectedClosureStatus!=="PROCESSING") return;
             $scope.unitClosureFormDataResponse = [];
             $scope.showUnitClosureForm = false;
             $scope.stateList = [];
             $scope.errorList = [];
             $scope.completedCount = 0;
             if ($scope.idSelected == idSelected.unitId) {
                 $scope.groupSelected = !$scope.groupSelected;
             }
             $scope.idSelected = idSelected.unitId;
             if ($scope.groupSelected){
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitClosure.getClosureByStates,
                    params : {
                       requestId : idSelected.requestId
                    }
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.data.length>0) {
                         for (var index in response.data) {
                            $scope.stateList.push(response.data[index].unitClosureStateDomain);
                         }
                    }
                    for(var idx in $scope.stateList){
                        $scope.validate($scope.stateList[idx],idSelected.unitId);
                    }
                    
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
             }
         };

        $scope.getUnitDetails = function(){
            $rootScope.showFullScreenLoader = true;
            $scope.unitDetailList = [];
            $http({
               method: 'GET',
               url: AppUtil.restUrls.unitClosure.getClosureByStatus,
               params : {
                    closureStatus : $scope.selectedClosureStatus
               }
           }).then(function success(response) {
               $scope.unitDetailList = response.data;
               $scope.stateList = [];
               $scope.validation= {};
               $scope.isUnitClosure={};
               $scope.idSelected = null;
               $scope.unitClosureFormDataResponse = [];
               $rootScope.showFullScreenLoader = false;
               $scope.completedCount = 0;
               $scope.requiredTaskCount = 0;
               $scope.totalRequiredTask=0;
               $scope.errorList = [];
           }, function error(response) {
               console.log("error:" + response);
               $rootScope.showFullScreenLoader = false;
           });
           
            console.log("filtered units",$scope.unitDetailList)
        }

        $scope.validate = function(state,unitId) {
            var url = '';
            if (state.destinationSource =='SCM') {
                url = AppUtil.restUrls.unitClosure.checkStateSCM;
            } else if (state.destinationSource =='KETTLE') {
                url = AppUtil.restUrls.unitClosure.checkStateKETTLE;
            } else if (state.destinationSource =='MASTER') {
                url = AppUtil.restUrls.unitClosure.checkStateMASTER;
            }
            
            if($scope.unitStateStatusLoaderFlag[unitId] === null || $scope.unitStateStatusLoaderFlag[unitId] === undefined){
                $scope.unitStateStatusLoaderFlag[unitId] = {}
             }

            $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = true;
            
            $http({
                           method: 'GET',
                           url: url,
                           params : {
                                stateId : state.stateId,
                                unitId : unitId
                           }
                       }).then(function success(response) {
                        $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = false;
                        if($scope.validation[unitId] === null || $scope.validation[unitId] === undefined){
                            $scope.validation[unitId] = {}
                         }   
                        if(response.data.length > 0){
                             
                            if(response.data.length===1 && response.data[0].checkPassed === true){
                                $scope.validation[unitId][state.stateId] = true;
                               return;
                             }
                               response.data.forEach(function(e){ 
                                if(e.taskId !== null){
                                    $scope.errorList.push(e);
                                }    
                                
                             })
                                $scope.validation[unitId][state.stateId] = false;
                               
                            
                           }else{
                            $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = false;
                            $scope.validation[unitId][state.stateId] = false;
                            alert(response.data.errorMsg);
                        }
                                                  
                       }, function error(response) {
                           alert("error:" + response);
                           $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = false;
                       });
        }

        $scope.proceedUnitClosure = function(detail){

            $scope.showUnitClosureForm = true;
            $scope.unitClosureFormDataResponse = [];
            $rootScope.showFullScreenLoader = true;
            $http({
               method: 'GET',
               url: AppUtil.restUrls.unitClosure.getClosureFormMetaData,
               params : {unitId : detail.unitId}
           }).then(function success(response) {
             
               if(response.data.length>0){                  
                   $scope.unitClosureFormDataResponse = response.data.map(function(e){
                    if(e.date!=null){
                       return {
                        attachmentId : e.attachmentId ,
                        comment : e.comment ,
                        date : new Date(e.date) ,
                        unitClosureEvent : e.unitClosureEvent ,
                        unitClosureFormDataId : e.unitClosureFormDataId ,
                        unitClosureMetaData : e.unitClosureMetaData 
                       }
                    }
                    return e;
                   });
                   $scope.completedCount = 0;
                   $scope.unitClosureFormDataResponse.forEach(function(e){
                    var isDateFilled =    false;
                    var commentFilled =  false;
                    var attachmentFilled =  false;
                  
                        if(e.date !== null && e.date !== ""){
                            isDateFilled = true;
                        }
                       if( e.comment !== null && e.comment !== ""){
                        commentFilled = true;
                        }
    
                      if( e.attachmentId !== null && e.attachmentId !== ""){
                        attachmentFilled = true;
                        }
    
                if(isDateFilled && commentFilled && attachmentFilled) {
                    $scope.completedCount++;
                
                };
                });
                $scope.totalRequiredTask=0;
                $scope.unitClosureFormDataResponse.forEach(function(e){
                    calculateTotalRequiredTask(e);
              });
                
                $scope.requiredTaskCount = 0;
                 $scope.unitClosureFormDataResponse.forEach(function(e){
                    calculateCompletedRequiredTasks(e);
                 });
                 
                   $rootScope.showFullScreenLoader = false;
               }else{
                alert(response.data.errorMsg);
               }               
           }, function error(response) {
               console.log("error:" + response);
               $rootScope.showFullScreenLoader = false;
           });
        }

        $scope.submitUnitClosure = function(isSave,detail){

            if(!isSave){
                var isDateCheckPassed = true;
                var commentCheckPassed = true;
                var attachmentCheckPassed = true;
                var taskIdsDate = [];
                var taskIdsComment = [];
                var taskIdsAttachment = [];
                $scope.unitClosureFormDataResponse.forEach(function(e,idx){
                    if(e.unitClosureMetaData.dateRequired === "Y" && (e.date === null || e.date === "")){
                    isDateCheckPassed = false;
                    taskIdsDate.push(idx);
                    }
                   if(e.unitClosureMetaData.commentRequired === "Y" && (e.comment === null || e.comment === "")){
                    commentCheckPassed = false;
                    taskIdsComment.push(idx);
                    }

                  if(e.unitClosureMetaData.attachmentRequired === "Y" && (e.attachmentId === null || e.attachmentId === "")){
                    attachmentCheckPassed = false;
                    taskIdsAttachment.push(idx);
                    }
                });

                if(!isDateCheckPassed) { alert("Date is requires, Please fill date in task Ids : "+taskIdsDate);  }
                if(!commentCheckPassed) { alert("Comment is requires, Please fill comment in task Ids : "+taskIdsComment);  }
                if(!attachmentCheckPassed) { alert("Attachment is requires, Please fill attachment in task Ids : "+taskIdsAttachment);}

                if( !isDateCheckPassed ||  !commentCheckPassed ||  !attachmentCheckPassed) return;
            }

            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.unitClosure.addUnitClosureFormData,
                params : {
                    unitClosureEventId : detail.requestId,
                    isSaved  : isSave
                },
                data: $scope.unitClosureFormDataResponse
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if(response.data===true && isSave === false){
                    alert("Unit Closure Completed successfully !")
                    $scope.unitClosureFormDataResponse = [];
                    $scope.getUnitDetails();                  
                }else if(response.data===true && isSave === true){
                    $scope.completedCount = 0;
                    $scope.unitClosureFormDataResponse.forEach(function(e){
                        var isDateFilled = false;
                        var commentFilled = false;
                        var attachmentFilled = false;
                      
                            if(e.date !== null && e.date !== ""){
                                isDateFilled = true;
                            }
                           if( e.comment !== null && e.comment !== ""){
                            commentFilled = true;
                            }
        
                          if( e.attachmentId !== null && e.attachmentId !== ""){
                            attachmentFilled = true;
                            }
        
                    if( isDateFilled && commentFilled && attachmentFilled) {
                        $scope.completedCount++;
                    };
        
                    });

                    $scope.requiredTaskCount = 0;
                    $scope.unitClosureFormDataResponse.forEach(function(e){
                        calculateCompletedRequiredTasks(e);
                    });
                    
                
                         alert("Form Data Saved successfully !")
                }else{
                 alert(response.data.errorMsg);
                 $scope.unitClosureFormDataResponse = [];
                }               
            }, function error(response) {
                console.log("error:" + response);
                $scope.unitClosureFormDataResponse = [];
                $rootScope.showFullScreenLoader = false;
            });
        }

        $scope.checkNextStatus = function(detail){
           var val =  Object.values($scope.validation[detail.unitId]).includes(false);  
            
           if(val===true && $scope.errorList.length > 0){
            $scope.gridOptions = { 
                enableGridMenu: true,
                exporterExcelFilename: 'download.xlsx',
                exporterExcelSheetName: 'Sheet1',
                enableColumnMenus: true,
                saveFocus: false,
                enableRowSelection: true,
                saveScroll: true,
                enableSelectAll: true,
                multiSelect: true,
                enableColumnResizing: true,
                exporterMenuPdf : false,
                exporterMenuExcel : true,
                fastWatch: true,
                data : $scope.errorList,
                columnDefs : $scope.showStateError(),
            };
           }
          
            return !val;

              
        }

    $scope.showStateError = function () {
        return [
            {
                field: 'taskId',
                name: 'taskId',
                enableCellEdit: false,
                displayName: 'Task Id'
            }, 
            , {
                field: 'keyType',
                name: 'keyType',
                enableCellEdit: false,
                displayName: 'Key Type'
            }, {
                field: 'keyValue',
                name: 'keyValue',
                enableCellEdit: false,
                displayName: 'Key Value',
            }, {
                field: 'currentKeyStatus',
                name: 'currentKeyStatus',
                enableCellEdit: false,
                displayName: 'Current Status',
            },
            {
                field: 'initiatedBy',
                name: 'initiatedBy',
                enableCellEdit: false,
                displayName: 'Initiated By'
            },    
        ];
    };


        $scope.uploadUnitClosureAttachment = function(idx){
            $rootScope.showFullScreenLoader = true;
            $scope.uploadedDoc = null;
            var fd = new FormData();
            var file = fileService.getFile();
            if (file == undefined || file == null) {
                bootbox.alert("Please Select a file to upload..!");
                $rootScope.showFullScreenLoader = false;
                return;
            }
    
            var fileExt = AppUtil.getFileExtension(file.name);
            var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
            if (file.size > fileLimit) {
                var msg = ""
                if (fileExt.toLowerCase() == 'png') {
                    msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                }
                bootbox.alert('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
                $rootScope.showFullScreenLoader = false;
                return;
            }
            fd.append('file', file);
            fd.append('type', "OTHERS");
            fd.append('mimeType', fileExt.toUpperCase());
            fd.append('userId', AppUtil.getCurrentUser().id);
            fd.append('docType', "UNIT_CLOSURE_FORM_ATTACHMENT");
            fd.append('file', file);
            fd.append('docName', "UNIT_CLOSURE_FORM_ATTACHMENT");

            $http({
                url: AppUtil.restUrls.userManagement.uploadDocument,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                console.log(response);
                $rootScope.showFullScreenLoader = false;
                if (response.documentId == undefined || response.documentId == null) {
                    alert("Something Went Wrong Please try Again..!");
                } else {
                    alert("File Uploaded Successfully..!");
                    $scope.unitClosureFormDataResponse[idx].attachmentId = response.documentId;
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Upload failed");
                $scope.uploadedDoc = null;
            });


        };

        function calculateTotalRequiredTask(e){
            if(e.unitClosureMetaData.dateRequired === "Y"
              || e.unitClosureMetaData.commentRequired === "Y"
             || e.unitClosureMetaData.attachmentRequired === "Y"){
                $scope.totalRequiredTask++;
             }
        }

        function calculateCompletedRequiredTasks(e){
            
        if(e.unitClosureMetaData.dateRequired === "Y" || e.unitClosureMetaData.commentRequired === "Y" || e.unitClosureMetaData.attachmentRequired === "Y"){
            var isDateFilled =  e.unitClosureMetaData.dateRequired === "Y" ?  false : true;
            var commentFilled = e.unitClosureMetaData.commentRequired === "Y" ? false : true;
            var attachmentFilled = e.unitClosureMetaData.attachmentRequired === "Y" ? false : true;
            
            if(e.date !== null && e.date !== ""){
                isDateFilled = true;
            }
           if( e.comment !== null && e.comment !== ""){
            commentFilled = true;
            }

          if( e.attachmentId !== null && e.attachmentId !== ""){
            attachmentFilled = true;
            }

         if( isDateFilled && commentFilled && attachmentFilled) {
                 $scope.requiredTaskCount++;  
                };
           }
                
    
        }
        
        $scope.isCompleted = function(e){
               

              var isDateFilled =  e.unitClosureMetaData.dateRequired === "Y" ?  false : true;
                var commentFilled = e.unitClosureMetaData.commentRequired === "Y" ? false : true;
                var attachmentFilled = e.unitClosureMetaData.attachmentRequired === "Y" ? false : true;
              
                    if(e.date !== null && e.date !== ""){
                        isDateFilled = true;
                    }
                   if( e.comment !== null && e.comment !== ""){
                    commentFilled = true;
                    }

                  if( e.attachmentId !== null && e.attachmentId !== ""){
                    attachmentFilled = true;
                    }

            if( isDateFilled && commentFilled && attachmentFilled) {
                return false;
            };

            return true;
        
        }


        $scope.sendPendingFormDataEmail = function(detail){
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitClosure.sendPendingTaskNotificaton,
                params : {unitId : detail.unitId}
            }).then(function success(response) {
                if(response.status === 200){
                    alert("Notification sent successfully !");
                }else{
                    alert("Not able to send notification, Something went wrong !");
                }
                $rootScope.showFullScreenLoader = false;
            },function error(err){
                console.log(err);
                alert(err);
                $rootScope.showFullScreenLoader = false;
            });
        }


});
