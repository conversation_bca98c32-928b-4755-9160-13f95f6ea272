adminapp.controller("CampaignDetailsViewCtrl", function ($scope, $http, AppUtil, $state, $rootScope) {

    $scope.init = function () {
        $scope.searchDescInput = null;
        $scope.fetchAllTag = false;
        $scope.selectedCampaign = {};
        $scope.selectedCampaignLongUrl = null;
        $scope.selectedCampaignShortUrl = null;
        $scope.brandMap = new Map();
        $scope.unitMap = new Map();
        $scope.previewCampaign = {};
        getAllBrands();
        getAllUnits();
        $scope.previewBrandName = null;
        $scope.previewUnitNames = null;
        $scope.campaignIdToIndexMap={};
    };

    function changeDateFormat() {
        for (var i = 0; i < $scope.campaignDetailsList.length; i++) {
            $scope.campaignDetailsList[i].startDate = moment($scope.campaignDetailsList[i].startDate).format('YYYY-MM-DD');
            $scope.campaignDetailsList[i].endDate = moment($scope.campaignDetailsList[i].endDate).format('YYYY-MM-DD');
        }
    }

    function getAllBrands() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.brandList = response.data;
            for (var i in $scope.brandList) {
                $scope.brandMap.set($scope.brandList[i].brandId, $scope.brandList[i].brandName);
            }
        });
        $rootScope.showFullScreenLoader = false;
    }

    function getAllUnits() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits,
            params: {
                category: 'CAFE'
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.allUnitList = response.data;
            for (var i in $scope.allUnitList) {
                $scope.unitMap.set($scope.allUnitList[i].id, $scope.allUnitList[i].name);
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    function fetchActiveCampaignsByDesc() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.getCampaignsByCampaignDesc,
            params: {
                campaignDesc: $scope.searchDescInput,
                fetchAll: false
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status == 200) {
                $scope.campaignDetailsList = response.data;
            }
            changeDateFormat();
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });
    }

    function fetchAllCampaignsByDesc() {
        if ($scope.searchDescInput == null) {
            $scope.searchDescInput = '';
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.getCampaignsByCampaignDesc,
            params: {
                campaignDesc: $scope.searchDescInput,
                fetchAll: true
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status == 200) {
                $scope.campaignDetailsList = response.data;
                for(var i in $scope.campaignDetailsList){
                    $scope.campaignIdToIndexMap[$scope.campaignDetailsList[i].campaignId]=i
                }
            }
            changeDateFormat();
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });
    }

    $scope.fetchCampaignsByDesc = function () {
        if ($scope.fetchAllTag) {
            return fetchAllCampaignsByDesc();
        } else {
            return fetchActiveCampaignsByDesc();
        }
    };

    $scope.changeStatus = function (id) {
        var index = $scope.campaignIdToIndexMap[id]
        console.log($scope.campaignDetailsList[index]);
        if (confirm("Do you really want to change status?")) {
            $scope.campaignDetailsList[index].campaignStatus = angular.equals($scope.campaignDetailsList[index].campaignStatus, "ACTIVE") ? "IN_ACTIVE" : "ACTIVE";
            var reqObj = $scope.campaignDetailsList[index];
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.updateCampaignStatus,
                data: reqObj
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if (response.status == 200) {
                    console.log(response);
                    window.location.reload();
                }
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error: " + response);
            });
        }
    };

    $scope.showPreview = function (id) {
        var index = $scope.campaignIdToIndexMap[id]
        $scope.setPreviewCampaign(index);
        $("#previewCampaignModal").modal("show");
    };

    $scope.cloneCampaign = function (id) {
        var index = $scope.campaignIdToIndexMap[id]
        if (confirm("Do you want to clone campaign with id: " + $scope.campaignDetailsList[index].campaignId + "?")) {
            $scope.campaignDetailsList[index].campaignId = null;
            $scope.campaignDetailsList[index].longUrl = null;
            $scope.campaignDetailsList[index].shortUrl = null;
            $state.go('dashboard.campaignBuilder', {"campaignDetail": $scope.campaignDetailsList[index]});
        }
    };

    $scope.editCampaign = function (id) {
        var index = $scope.campaignIdToIndexMap[id]
        if (confirm("Do you want to edit campaign with id: " + $scope.campaignDetailsList[index].campaignId + "?")) {
            $state.go('dashboard.campaignBuilder', {"campaignDetail": $scope.campaignDetailsList[index]});
        }
    };

    $scope.openImagePreviewModal = function (imageSource) {
        $scope.previewImageSource = imageSource;
        if (imageSource == null) {
            alert("Image not found!");
        } else {
            $("#imagePreviewModal").modal("show");
        }
    };

    $scope.setSelectedCampaign = function (id) {
        var index = $scope.campaignIdToIndexMap[id]
        $scope.selectedCampaign = $scope.campaignDetailsList[index];
        $scope.selectedCampaignLongUrl = $scope.campaignDetailsList[index].longUrl;
        $scope.selectedCampaignShortUrl = $scope.campaignDetailsList[index].shortUrl;
    };

    $scope.setPreviewCampaign = function (index) {
        $scope.previewCampaign = $scope.campaignDetailsList[index];

        $scope.campaignMappings = [];
        for (var key in $scope.previewCampaign.mappings) {
            for (var innerKey in $scope.previewCampaign.mappings[key]) {
                var customerCampaignObj = {};
                customerCampaignObj.customerType = key;
                customerCampaignObj.journey = innerKey;
                customerCampaignObj.validityInDays = $scope.previewCampaign.mappings[key][innerKey].validityInDays;
                customerCampaignObj.reminderDays = $scope.previewCampaign.mappings[key][innerKey].reminderDays;
                customerCampaignObj.code = $scope.previewCampaign.mappings[key][innerKey].code;
                customerCampaignObj.desc = $scope.previewCampaign.mappings[key][innerKey].desc;
                customerCampaignObj.campaignCouponMappingId = $scope.previewCampaign.mappings[key][innerKey].campaignCouponMappingId;
                $scope.campaignMappings.push(customerCampaignObj);
            }
        }

        $scope.previewBrandName = $scope.brandMap.get($scope.previewCampaign.brandId);
        $scope.previewUnitNames = null;

        if ($scope.previewCampaign.unitIds != null && $scope.previewCampaign.unitIds.length > 0) {
            var unitIds = $scope.previewCampaign.unitIds.split(',');
            var previewUnitArr = [];
            for (var i in unitIds) {
                previewUnitArr.push($scope.unitMap.get(parseInt(unitIds[i])));
            }
            $scope.previewUnitNames = previewUnitArr.join(', ');
        }

        $scope.previewCityNames = $scope.previewCampaign.city.split(',').join(', ');
        $scope.previewRegionNames = $scope.previewCampaign.region.split(',').join(', ');

        if ($scope.previewCampaign.newCustomerOnly === 'Y') {
            $scope.previewNewCustomerOnly = "YES";
        } else {
            $scope.previewNewCustomerOnly = "NO";
        }
        if ($scope.previewCampaign.couponClone === true) {
            $scope.previewCouponClone = "YES";
        } else {
            $scope.previewCouponClone = "NO";
        }
        if ($scope.previewCampaign.applicableForOrder === true) {
            $scope.previewApplicableForOrder = "YES";
        } else {
            $scope.previewApplicableForOrder = "NO";
        }

    };

    function copyToClipboard(copyText) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(copyText);
        } else {
            var textArea = document.createElement("textarea");
            textArea.value = copyText;
            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'successful' : 'unsuccessful';
                console.log('Fallback: Copying text command was ' + msg);
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
            }
            document.body.removeChild(textArea);
        }
    }

    $scope.copyLongUrlToClipboard = function () {
        copyToClipboard($scope.selectedCampaignLongUrl);
    };

    $scope.copyShortUrlToClipboard = function () {
        copyToClipboard($scope.selectedCampaignShortUrl);
    };

    $scope.disableSearchButton = function () {
        if ($scope.searchDescInput == null || $scope.searchDescInput.trim() === '') {
            if ($scope.fetchAllTag === true) {
                return false;
            }
            return true;
        }
        return false;
    };

    $scope.createShortUrl = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.createCampaignShortUrl,
            params: {
                longUrl: $scope.selectedCampaignLongUrl
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status == 200) {
                $scope.selectedCampaignShortUrl = response.data;
            }
            console.log(response);
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error: " + response);
        });
    };

    $scope.saveShortUrl = function () {

        console.log($scope.selectedCampaignShortUrl);
        if (!validateURL($scope.selectedCampaignShortUrl)) {
            alert("Please enter valid link!!");
            return;
        }

        if (confirm("Confirm saving Short URL?")) {
            $scope.selectedCampaign.longUrl = $scope.selectedCampaignLongUrl;
            $scope.selectedCampaign.shortUrl = $scope.selectedCampaignShortUrl;
            var reqObj = $scope.selectedCampaign;
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.updateCampaignShortUrl,
                data: reqObj
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if (response.status == 200) {
                    console.log(response);
                    $("#addShortUrl").modal("hide");
                }
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error: " + response);
            });
        }
    };

    function validateURL(string) {
        var res = string.match(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g);
        console.log(res);
        return (res !== null)
    }

    $scope.clearCampaignCache = function () {
        var url = AppUtil.restUrls.campaignCache.clearCampaignCache;
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'get',
            url: url
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            console.log(JSON.stringify(response));
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            alert("error:" + JSON.stringify(response));
        });
    };

});