/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("priceProfileCtrl", function ($scope, $window, $http, $location, AppUtil) {

    $scope.init = function() {
        $rootScope.enableScreenFilter = true;
        $scope.description = "";
        $scope.profileTypes = null;
        $scope.threshold = 0;
        $scope.searchprofileType = null;
        $scope.profileType = null;
        $scope.generalMessage = null;
        $scope.errorMessage = null;
        $scope.showAddForm = false;
        $scope.newDescription = null;
        $scope.newThreshold = null;
        $scope.initialiseValues();
        $scope.profileRangeValueDetails = [];
        $scope.profileStatus = ["ACTIVE","INACTIVE"];
        $scope.activeprofileStatus = $scope.profileStatus[0];
        $scope.rangeValueStatus = ["ACTIVE","INACTIVE"];
        $scope.activerangeValueStatus = $scope.rangeValueStatus[0];
        $scope.priceProfileData = [];
        $scope.getProfileTypes();
        $scope.fixedValues = false;
    }

    $scope.getUserId = function(){
            var userData = JSON.parse(localStorage.getItem("udata"));
            return String(userData.id);
    }
    $scope.initialiseValues = function () {
        $scope.currentStartPrice = 0;
        $scope.currentEndPrice = 99999;
        $scope.currentDeltaPrice = 0;
        $scope.prevEndPrice = null;
        $scope.profileRangeValueDetails = [];
        $scope.newId = null;
        $scope.newDescription = null;
        $scope.newThreshold = null;
        $scope.newProfileStatus = null;
    }

    $scope.addPriceProfileNew = function () {
        console.log($scope.description);
        $scope.showAddForm = !$scope.showAddForm;
    }

    $scope.getPriceProfileData = function () {
        var param = {
            priceProfileType:$scope.searchprofileType,
        }
        $http({
            method: 'GET',
            url: AppUtil.restUrls.channelPartner.getPriceProfile,
            params: param
        }).then(function success(response) {
            console.log("this is profile response",response.data);
            if(response.data.length==0){
                $scope.showMessage("No data found");
            }
            $scope.priceProfileData = response.data;


        })
    }
    $scope.showError = function(text) {
            $scope.errorMessage = text;
            var x = document.getElementById("snackbar");
            x.classList.add("show");
            setTimeout(function(){
                $scope.errorMessage = null;
                x.classList.remove("show"); }, 3000);
    }

    $scope.showMessage = function(text) {
        $scope.generalMessage = text;
        var x = document.getElementById("toast");
        x.classList.add("show");
        setTimeout(function(){
            $scope.generalMessage = null;
            x.classList.remove("show"); }, 3000);
    }

    $scope.getProfileTypes = function () {
        if($scope.profileTypes===null) {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.getPriceProfileStrategy
            }).then(function success(response) {
                console.log("this is profile response",response.data);
                $scope.profileTypes = response.data;
                $scope.profileType = $scope.profileTypes[0];
                $scope.searchprofileType = $scope.profileTypes[0];
            })
        }
    }

    $scope.showPriceRangeModal = function () {
        if(!$scope.profileType.includes("RANGE")) {
            $scope.fixedValues = true;
            $scope.currentStartPrice = 0;
            $scope.currentEndPrice = 99999;
        }
        else {
            $scope.fixedValues = false;
        }
        console.log($scope.fixedValues,$scope.profileType.includes("RANGE"),$scope.profileType);
        $("#priceRangeModal").modal("show");
    }

    $scope.checkStartPrice = function() {

        console.log("start price",$scope.currentStartPrice,$scope.currentStartPrice<0)
        if($scope.currentStartPrice<0) {
            $scope.showError("Start Price cannot be negative");
            $scope.currentStartPrice = 0;
            return false;
        }
        if($scope.currentStartPrice>$scope.currentEndPrice) {
            $scope.showError("Start Price cannot be greater than end Price");
                $scope.currentStartPrice = $scope.currentEndPrice - 1;
            return false;
        }
        if($scope.prevEndPrice!=null){
            if($scope.currentStartPrice<=$scope.prevEndPrice){
                $scope.showError("Start Price cannot be less than previous end price");
                $scope.currentStartPrice = $scope.prevEndPrice + 1;
                return false;
            }
        }
        return true;
    }

    $scope.deleteRangeValue = function(index) {
        $scope.profileRangeValueDetails.splice(parseInt(index),1);
        var len = $scope.profileRangeValueDetails.length;
        if(len>0) {
            $scope.prevEndPrice = $scope.profileRangeValueDetails[len - 1].endPrice;
            $scope.currentStartPrice = $scope.prevEndPrice + 1;
            $scope.currentEndPrice = $scope.currentStartPrice + 1;
            $scope.currentDeltaPrice = 0;
        }
        else {
            $scope.initialiseValues();
        }
    }

    $scope.checkEndPrice = function() {
        if($scope.currentEndPrice<$scope.currentStartPrice){
            $scope.showError("End Price cannot be less than start price");
                $scope.currentEndPrice = $scope.currentStartPrice;
            return false;
        }
        return true;
    }

    $scope.checkDeltaPrice = function() {
        if(Math.abs($scope.currentDeltaPrice)>($scope.currentEndPrice - $scope.currentStartPrice)){
            $scope.showError("Please check the delta value");
            $scope.currentDeltaPrice = 0;
            return false;
        }
        return true;
    }

    $scope.addNewRangeValue = function() {
        if($scope.currentDeltaPrice == null || $scope.currentStartPrice==null || $scope.currentEndPrice ==null) {
            $scope.showError("Please fill all the details correctly");
            return;
        }
        else if($scope.checkStartPrice() && $scope.checkEndPrice() && $scope.checkDeltaPrice()) {
            $scope.profileRangeValueDetails.push(
                {
                    "startPrice": $scope.currentStartPrice,
                    "endPrice": $scope.currentEndPrice,
                    "deltaPrice": $scope.currentDeltaPrice,
                    "rangeValuesStatus": $scope.activerangeValueStatus,
                    "lastUpdatedBy": $scope.getUserId(),
                    "lastUpdateTime":new Date().getTime(),
                    "rangeValuesActivationTime": null
                }
            );
            $scope.prevEndPrice = $scope.currentEndPrice;
            $scope.currentStartPrice = $scope.currentEndPrice + 1;
            $scope.currentEndPrice = 99999;
            $scope.currentDeltaPrice = 0;
            console.log("range value array", $scope.profileRangeValueDetails);
            $("#priceRangeModal").modal("hide");
        }
    }

    $scope.addPriceProfile = function() {
        if ($scope.description==null || $scope.description.trim()=="") {
            $scope.showError("Please fill all the details");
        } else {
            var priceRangeCount=$scope.profileRangeValueDetails.length;
            if($scope.profileRangeValueDetails[priceRangeCount-1].endPrice!=99999){
                $scope.showError("Last price range must have end price 99999");
            }
            else{
                var payload = {
                    "profileDescription": $scope.description,
                    "profileType": $scope.profileType,
                    "profileStatus": $scope.activeprofileStatus,
                    "thresholdPercentage": $scope.threshold,
                    "lastUpdatedBy": $scope.getUserId(),
                    "profileCreationTime":new Date().getTime(),
                    "lastUpdateTime": new Date().getTime() ,
                    "profileRangeValueDetails": $scope.profileRangeValueDetails
                };
                console.log($scope.profileRangeValueDetails);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.addPriceProfile,
                    data: payload
                }).then(function success(response) {
                    console.log("this is upsert response",response.data);
                    $scope.description = "";
                    $scope.threshold = 0;
                    $scope.initialiseValues();
                    $scope.showMessage("Price Profile upsert successful");
                }).catch(function(error){
                    console.log(error.message);
                    $scope.showError("Some error happened");
                })
            }
        }
    }

    $scope.showEditPriceProfileModal = function(data) {
            $scope.newId = data.priceProfileId;
            $scope.newDescription = data.profileDescription;
            $scope.newThreshold = data.thresholdPercentage;
            $scope.newProfileStatus = data.profileStatus;
            $("#editpriceProfileModal").modal("show");
    }

    $scope.showPriceRangeDetails = function (data) {
        $scope.priceRangeDetailsData = data;
        $("#PriceRangeDetailsModal").modal("show");
    }

    $scope.updatePriceProfileData = function() {

        if($scope.newDescription==null || $scope.newThreshold==null){
            $scope.showError("Please fill all the details");
        }

        var payload = {
                "id": $scope.newId,
                "name":$scope.newDescription,
                "code":$scope.newThreshold,
                "shortCode" : $scope.getUserId(),
                "status":$scope.newProfileStatus
        }
        console.log(payload);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.channelPartner.updatePriceProfile,
            data: payload
        }).then(function success(response) {
            console.log("this is upsert response",response.data);
            $scope.newDescription = "";
            $scope.newThreshold = 0;
            $scope.newId = null;
            $scope.showMessage("Price Profile update successful");
            $scope.priceRangeDetailsData = null;
            $("#editpriceProfileModal").modal("hide");
            $scope.getPriceProfileData();
        })

    }

    $scope.checkThreshold = function () {
        if($scope.threshold>100) {
            $scope.showError("Threshold cannot be greater than 100");
            $scope.threshold = 100;
        }
        else if($scope.threshold<0) {
            $scope.showError("Threshold cannot be less than 0");
            $scope.threshold = 0;
        }
    }

    $scope.checkNewThreshold = function () {
        if($scope.newThreshold>100) {
            $scope.showError("Threshold cannot be greater than 100");
            $scope.newThreshold = 100;
        }
        else if($scope.newThreshold<0) {
            $scope.showError("Threshold cannot be less than 0");
            $scope.newThreshold = 0;
        }
    }

});