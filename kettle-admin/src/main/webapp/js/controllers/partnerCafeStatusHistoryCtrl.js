/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerCafeStatusHistoryCtrl', ['$location', '$scope', 'AppUtil', '$rootScope', '$http',
    function ($location, $scope, AppUtil, $rootScope, $http) {

        $scope.init = function () {
            $scope.selectedUnit = null;
            $scope.selectedBrand = null;
            $scope.selectedPartner = null;
            getUnits();
            $scope.getAllBrands();
            $scope.getChannelPartnersList();
        };

        $scope.getAllBrands = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.brandManagement.getAllBrands
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.brands = response.data;
                    $rootScope.showFullScreenLoader = false;
                } else {
                    bootbox.alert("Error getting brands.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        function getUnits() {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnits,
                params: {
                    "category": "CAFE"
                }
            }).then(function success(response) {
                $scope.units = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        $scope.getPartnerHistory = function () {

            var obj = {
                id: $scope.selectedUnit.id,
                code: $scope.selectedBrand.brandId,
                name: angular.lowercase($scope.selectedPartner.name)
            };

            $http({
                method: 'POST',
                url: AppUtil.restUrls.partnerManagement.getPartnerHistory,
                data: obj
            }).then(function success(response) {
                $scope.partnerHistory = response.data;
                if ($scope.partnerHistory.length === 0) {
                    bootbox.alert("Unable to fetch data");
                }
            }, function error(response) {
                bootbox.alert("Error Faced while fetching data!!");
            });
        };

        $scope.getChannelPartnersList = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.partnerManagement.get
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.channelPartnerList = response.data;
                    $scope.channelPartners = [];
                    $scope.channelPartnerList.map(function (partner) {
                        if (partner.partnerStatus === "ACTIVE") {
                            $scope.channelPartners.push({
                                id: partner.kettlePartnerId,
                                name: partner.partnerName,
                                selected: false
                            });
                        }
                    });
                } else {
                    bootbox.alert("Error loading channel partner list.");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };
    }]
);