/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("deliveryMappingsCtrl", function ($scope, $rootScope, $http, AppUtil,fileService) {
    $scope.downloadMappings = function () {
        console.log("download locality mappings ::::");
        $http({
            url: AppUtil.restUrls.masterCacheManagement.downloadLocalities,
            method: 'GET',
            responseType: 'arraybuffer',
            headers: {
                'Content-type': 'application/json',
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        }).success(function (data) {
            var fileName = "Chaayos Cafe Locality Mappings.xls";
            var blob = new Blob([data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }, fileName);
            saveAs(blob, fileName);
        }).error(function (err) {
            console.log("Error during getting data", err);
        });
    };

    $scope.uploadMappings = function () {
        var fd = new FormData();
        fd.append('file', fileService.getFile());
        $rootScope.showFullScreenLoader = true;
        $http.post(AppUtil.restUrls.masterCacheManagement.uploadLocalities, fd, {
            transformRequest: angular.identity,
            headers: {
                'Content-Type': undefined
            }
        }).success(function (response) {
            console.log(response);
            $rootScope.showFullScreenLoader = false;
            if (!response) {
                alert("Upload failed");
            }
        }).error(function (response) {
            $rootScope.showFullScreenLoader = false;
            alert("Upload failed");
        });
    };

});