  adminapp.controller('ProfileController', function ($scope,$rootScope,$http,AppUtil) {

    $scope.init = function(){
      $rootScope.enableScreenFilter = true;
      $scope.activeView = 'create';
      $scope.newProfile = {};
      $scope.search = {};
      $scope.showVersionModal = false;
      $scope.selectedProfile = null;
      $scope.showCloneOptions = false;
      $scope.cloneOptions = { profile: null, version: null };
      $scope.showVersionModal = false;
      $scope.selectedProfile = null;
      $scope.cloneVersions = [];
      $scope.filteredProfiles = [];
      $scope.getALLProfiles();
      $scope.getMaxVersion();
    }
    



      $scope.partners = [{name : '<PERSON><PERSON><PERSON>(Dine In)' , id : 1}, {name : '<PERSON><PERSON><PERSON>' , id : 3}];
      $scope.brands = [{name : '<PERSON><PERSON><PERSON>' , id : 1}, {name : 'GNT' , id : 3}];
      // $scope.profiles = [
      //   { name: 'Profile 1', partner : "Partner A",brand : 'Brand X',createdBy: 'Admin', creationDate: '2025-01-01', status: 'Active',
      //    versions: [{'number' : 1 ,'createdBy' : 140199 ,'creationDate' : "2025-01-18" , 'status' : 'Active'}] },
      //   { name: 'Profile 2', partner : "Partner B",brand : 'Brand Y',createdBy: 'Admin', creationDate: '2025-01-10', status: 'Inactive', versions: [] }
      // ];

      $scope.switchView = function (view) {
        $scope.activeView = view;
        if(view == 'cloneVersion'){
          $scope.getMaxVersion();
        }
        if(view == 'searchVersion'){
          clubversions($scope.profiles);
        }
        $scope.getALLProfiles();
      };

      function clubversions(profiles){
        var versionMap = {};
        var versionMap = new Map();

    profiles.forEach(profile => {
        profile.priceProfileVersions.forEach(version => {
            let versionKey = version.versionNo; // Group by version number

            if (!versionMap.has(versionKey)) {
                versionMap.set(versionKey, {
                    versionNo: version.versionNo,
                    status: "INACTIVE", // Default, will update if any profile is ACTIVE
                    creationTime: version.creationTime,
                    createdBy: version.createdBy,
                    profiles: []
                });
            }

            let versionEntry = versionMap.get(versionKey);
            versionEntry.profiles.push({
                priceProfileDataId: profile.priceProfileDataId,
                priceProfileName: profile.priceProfileName,
                creationTime: version.creationTime,
                createdBy: version.createdBy,
                status: version.status
            });

            // If any profile is ACTIVE, mark the version as ACTIVE
            if (version.status === "ACTIVE") {
                versionEntry.status = "ACTIVE";
            }
        });
      });
      $scope.clubbedVersions =  Array.from(versionMap.values());
    }

      $scope.selectProfileForClone = function(profile){
        var cloneProfile  = JSON.parse(profile);
        $scope.cloneVersions = cloneProfile.priceProfileVersions;
        $scope.newProfile.clonePriceProfileId = cloneProfile.priceProfileDataId;
        
      }

      // $scope.searchProfiles = function () {
      //   $scope.filteredProfiles = $scope.profiles;
      //   // Implement search logic here
      // };

      $scope.createProfile = function(profile){
        if(profile.priceProfileName == null){
            alert("Please enter all data to create profile");
            return;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.addPriceProfile,
            dataType: 'json',
            method: 'POST',
            data: profile,
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
          if (response.data.errorMessage != null) {
            $rootScope.showFullScreenLoader = false;
            bootbox.alert(response.data.errorMessage);
          }else{
            $rootScope.showFullScreenLoader = false;
            alert("Successfully Created Price Profile!!");
          }
            
            

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
        
      }

      $scope.getALLProfiles = function(callback){
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.findPriceProfiles,
            dataType: 'json',
            method: 'GET',
            params : {status : 'ACTIVE'},
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            $scope.profiles = response.data;
            if(callback!=null){
              callback();
            }
            //alert("Successfully fetched Price Profile!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
        
      }

      $scope.searchProfiles = function(){
        if($scope.search.status == null){
            alert("Please Select Status to search profiles");
            return;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.findPriceProfiles,
            dataType: 'json',
            method: 'GET',
            params: {status : $scope.search.status},
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            $scope.filteredProfiles = response.data;
            //alert("Successfully fetched Price Profile!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
        
      }


      $scope.getMaxVersion = function(){
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.getMaxVersion,
            dataType: 'json',
            method: 'GET',
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            $scope.maxVersion = response.data;
            $scope.allVersionNumbers = [];
            for(var i = 1;i<=$scope.maxVersion;i++){
              $scope.allVersionNumbers.push(i);
            }

            //alert("Successfully fetched Price Profile!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
        
      }


      $scope.viewVersions = function (profile) {
        $scope.selectedProfile = profile;
        $scope.showVersionModal = true;
      };

      $scope.closeVersionModal = function () {
        $scope.showVersionModal = false;
      };

      $scope.toggleProfileStatus = function (profile) {
        var requestProfile = angular.copy(profile);
        requestProfile.status = profile.status === 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.togglePriceProfileStatus,
            dataType: 'json',
            method: 'POST',
            data : requestProfile,
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
          $rootScope.showFullScreenLoader = false;
            //console.log(JSON.stringify(response));
            if (response.data.errorMessage == null) {
              profile.status = profile.status === 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
              alert("Succesfully Toggled Status")
            } else {
               alert(response.data.errorMessage);
            }
  
         }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            alert(response.errorMessage);
        });
      
      };

      $scope.toggleVersionStatus = function (version) {
        var requestVersion = angular.copy(version);
        requestVersion.status = version.status === 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.togglePriceProfileVersionStatus,
            dataType: 'json',
            method: 'POST',
            data : requestVersion,
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            if (response.data.errorMessage == null) {
              version.status = version.status === 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
              alert("Succesfully Toggled Status")
            } else {
               alert(response.data.errorMessage);
            }
            //alert("Successfully fetched Price Profile!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            alert(response.errorMessage);
        });
      };

      $scope.toggleBulkVersionStatus = function (versionNo, currentStatus) {
        newStatus = currentStatus === 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.toggleBulkPriceProfileVersion,
            dataType: 'json',
            method: 'POST',
            params : {
              status : newStatus,
              versionNo : versionNo
            },
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            if (response.data.errorMessage == null) {
              //version.status = version.status === 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
              $scope.getALLProfiles(clubversions($scope.profiles));
              alert("Succesfully Toggled Status")
            } else {
               alert(response.data.errorMessage);
            }
            //alert("Successfully fetched Price Profile!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            alert(response.errorMessage);
        });
      };

      $scope.openVersionModal = function (profile) {
        $scope.selectedProfile = profile; // Set the selected profile
        $scope.showVersionModal = true;
        $("#versionModal").modal("show");
        //angular.element('#versionModal').modal('show'); // Show the modal
      };

      $scope.openProfileModal = function (profile) {
        $scope.selectedVersion = profile; // Set the selected profile
        $scope.showProfileModal = true;
        $("#profileModal").modal("show");
        //angular.element('#versionModal').modal('show'); // Show the modal
      };
      
      $scope.closeVersionModal = function () {
        $scope.showVersionModal = false;
        $("#versionModal").modal("hide");
       // angular.element('#versionModal').modal('hide'); // Hide the modal
      };

      $scope.closeProfileModal = function () {
        $scope.showProfileModal = false;
        $("#profileModal").modal("hide");
       // angular.element('#versionModal').modal('hide'); // Hide the modal
      };

      
      $scope.createNewVersion = function (profile, version) {
        if (!profile || !version) {
          alert("Please select both profile and version to clone from.");
          return;
        }
        
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.createPriceProfileVersion,
            dataType: 'json',
            method: 'POST',
            params : {
              priceProfileId :  $scope.selectedProfile.priceProfileDataId ,
              clonePriceProfileId : profile.priceProfileDataId,
              clonePriceProfileVersion : version.versionNo
            },
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            $scope.selectedProfile  =response.data;
            $scope.searchProfiles();
            alert("Successfully Created New Version!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
      };

      $scope.createNewVersions = function (version) {
        if (!version) {
          alert("Please select version to clone from.");
          return;
        }
        
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.createPriceProfileVersions,
            dataType: 'json',
            method: 'POST',
            params : {
              clonePriceProfileVersion : version
            },
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            alert("Successfully Created New Versions!!");

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
      
      };
    });