adminapp.controller('monk2ConfCtrl', function ($log, $scope, $http, $location, AppUtil) {
    $scope.unitId = $location.search().selectedUnit || 10000;

    $scope.init = function () {
        if (!$scope.unitId) {
            alert('Unit ID not found in URL');
            $location.path("dashboard/unit/CAFE");
            return;
        }

        $scope.viewMode = false;
        $scope.configData = {
            noOfXTwos: null,
            vesselSenseDelay: null,
            spiceSenseDelay: null,
            monkUrls: [] // New field
        };

        getMonkConfigData();
    };

    function getMonkConfigData() {
        const apiUrl = AppUtil.restUrls.unitMetaData.getMonkMetaData;

        $http.get(apiUrl, {
            params: { unitId: $scope.unitId }
        }).then(function (response) {
            if (response.data) {
                $scope.configData = {
                    noOfXTwos: response.data.noOfXTwoMachines,
                    vesselSenseDelay: response.data.vesselSenseDelay,
                    spiceSenseDelay: response.data.spiceSenseDelay,
                    monkUrls: response.data.monkUrls || []
                };

                $scope.adjustMonkUrls(); // Adjust based on initial value
                $scope.viewConfigData = angular.copy($scope.configData);
            }
        }).catch(function (error) {
            $log.error("Error fetching monk metadata:", error);
            alert("Failed to load configuration data");
        });
    }

    $scope.toggleViewMode = function () {
        $scope.viewMode = !$scope.viewMode;
        if ($scope.viewMode) {
            $scope.viewConfigData = angular.copy($scope.configData);
        }
    };

    $scope.saveConfiguration = function () {
        const payload = {
            unitId: $scope.unitId,
            noOfXTwoMachines: $scope.configData.noOfXTwos,
            vesselSenseDelay: $scope.configData.vesselSenseDelay,
            spiceSenseDelay: $scope.configData.spiceSenseDelay,
            monkUrls: $scope.configData.monkUrls
        };

        $http({
            url: AppUtil.restUrls.unitMetaData.updateMonkMetaData,
            method: "POST",
            data: payload
        }).then(function (response) {
            if (response.data && !response.data.error) {
                alert("Configuration saved successfully");
                $scope.toggleViewMode();
            } else {
                alert("Configuration saving failed. Please try again.");
            }
        }).catch(function (error) {
            $log.error("Error saving configuration:", error);
            alert("Failed to save configuration!");
        });
    };

//    $scope.adjustMonkUrls = function () {
//        const count = parseInt($scope.configData.noOfXTwos || 0) * 2;
//        $scope.configData.monkUrls = $scope.configData.monkUrls || [];
//        while ($scope.configData.monkUrls.length < count) {
//            $scope.configData.monkUrls.push('');
//        }
//        if ($scope.configData.monkUrls.length > count) {
//            $scope.configData.monkUrls.length = count;
//        }
//    };

function createDefaultMonkUrl(index) {
    return {
        url: "",
        machineIndex: index,
        isActive: true,
        lastUpdatedTimestamp: new Date().toISOString()
    };
}

$scope.adjustMonkUrls = function () {
    const count = parseInt($scope.configData.noOfXTwos || 0) * 2;
    $scope.configData.monkUrls = $scope.configData.monkUrls || [];

    for (let i = 0; i < count; i++) {
        if (
            !$scope.configData.monkUrls[i] ||
            typeof $scope.configData.monkUrls[i] === 'string'
        ) {
            $scope.configData.monkUrls[i] = createDefaultMonkUrl(i);
        }
    }

    if ($scope.configData.monkUrls.length > count) {
        $scope.configData.monkUrls.length = count;
    }
};


    // Watcher to adjust monkUrls dynamically when X-Twos change
    $scope.$watch('configData.noOfXTwos', function (newVal, oldVal) {
        if (newVal !== oldVal) {
            $scope.adjustMonkUrls();
        }
    });

    $scope.goBack = function () {
        $location.path("dashboard/unit/CAFE");
    };

    $scope.$on('$destroy', function () {
        $location.search({});
    });
});
