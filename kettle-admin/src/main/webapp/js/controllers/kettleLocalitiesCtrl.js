/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('kettleLocalitiesCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http', 'fileService',
        function ($location, $scope, AppUtil, $rootScope, $http, fileService) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.init = function () {
                $scope.channelPartnerList = [];
                $scope.getLocalities();
                $scope.getUnitList();
                $scope.channelPartners = [];
                $scope.today = new Date();
                $scope.cityMap = [{city: 'Bangalore', state: 'Karnataka'},
                    {city: 'Chandigarh', state: 'Chandigarh'},
                    {city: 'Faridabad', state: 'Haryana'},
                    {city: 'Ghaziabad', state: 'Uttar Pradesh'},
                    {city: 'Gurgaon', state: 'Haryana'},
                    {city: 'Karnal', state: 'Haryana'},
                    {city: 'Mumbai', state: 'Maharashtra'},
                    {city: 'New Delhi', state: 'Delhi'},
                    {city: 'Noida', state: 'Uttar Pradesh'},
                ]
            };

            $scope.getLocalities = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.masterCacheManagement.localities
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.localityMappings = response.data;

                    } else {
                        bootbox.alert("Error loading localities list.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.downloadLocalities = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.masterCacheManagement.downloadLocalities,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                })
                    .then(
                        function success(response) {
                            if (response != undefined
                                && response != null) {
                                var fileName = "Chaayos Cafe Locality Mappings.xls";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                            $rootScope.showFullScreenLoader = false;
                        },
                        function error(response) {
                            console.log("error:" + response);
                            alert("Unable to download localities  file");
                            $rootScope.showFullScreenLoader = false;
                        });
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                });
            };

            $scope.onCityChange = function (map) {
               $scope.newMapping.state=map.state;
               $scope.newMapping.city=map.city;
            };

            $scope.resetUnitList = function () {
                $scope.unitList.map(function (unit) {
                    unit.selected = false;
                });
            };

            $scope.changeLocalityMapping = function (mapping) {
                $scope.updateMapping = angular.copy(mapping);
                $scope.editMappingSecondaryUnit = {};
                $scope.unitList.map(function (unit) {
                    if (unit.id == $scope.updateMapping.primaryUnitId) {
                        $scope.editMappingPrimaryUnit = unit;
                    }
                    if (unit.id == $scope.updateMapping.secondaryUnitId) {
                        $scope.editMappingSecondaryUnit = unit;
                    }
                });
                $("#editLocalityMappingModal").modal("show");
            };

            $scope.updateLocalityMapping = function () {

                if (!$scope.editMappingPrimaryUnit) {
                    alert("please select primary unit");
                    return;
                }
                $("#editLocalityMappingModal").modal("hide");
                $scope.updateMapping.primaryCOD = $scope.editMappingPrimaryUnit.name;
                $scope.updateMapping.primaryUnitId = $scope.editMappingPrimaryUnit.id;
                if ($scope.editMappingSecondaryUnit != null) {
                    $scope.updateMapping.secondaryCOD = $scope.editMappingSecondaryUnit.name;
                    $scope.updateMapping.secondaryUnitId = $scope.editMappingSecondaryUnit.id;
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.masterCacheManagement.updateLocality,
                    data: $scope.updateMapping
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Mapping updated successfully!");
                        $scope.getLocalities();
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error occurred.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.showAddLocalities = function () {
                $("#addLocalityMappingModal").modal("show");
                $scope.newMappingPrimaryUnit = null;
                $scope.newMappingSecondaryUnit = null;
                $scope.newMapping = {
                    "locality": null,
                    "city": null,
                    "state": null,
                    "country": "India",
                    "primaryUnitName": null,
                    "primaryUnitId": null,
                    "secondaryUnitName": null,
                    "secondaryUnitId": null,
                    "tertiaryUnitName": null,


                }
            };

            $scope.addLocalityMapping = function () {

                if (!$scope.newMappingPrimaryUnit) {
                    alert("please select primary unit");
                    return;
                }
                $("#addLocalityMappingModal").modal("hide");
                $scope.newMapping.primaryCOD = $scope.newMappingPrimaryUnit.name;
                $scope.newMapping.primaryUnitId = $scope.newMappingPrimaryUnit.id;
                if ($scope.newMappingSecondaryUnit != null) {
                    $scope.newMapping.secondaryCOD = $scope.newMappingSecondaryUnit.name;
                    $scope.newMapping.secondaryUnitId = $scope.newMappingSecondaryUnit.id;
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.masterCacheManagement.uploadLocalitiesObj,
                    data: $scope.newMapping
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Mappings added successfully!");
                        $scope.city="";
                        $scope.getLocalities();
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error occurred.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.showAddBulkLocalities = function () {
                $("#addBulkLocalityMappingModal").modal("show");
                $scope.bulkCSVFile = null;
            };

            function isCorrectFile(fileExt) {
                return fileExt == "xls" || fileExt == "csv";
            }

            function getFileExtension(fileName) {
                var re = /(?:\.([^.]+))?$/;
                return re.exec(fileName)[1];
            }

            $scope.addBulkLocalityMappings = function () {
                var fileExt = getFileExtension(fileService.getFile().name);
                if (isCorrectFile(fileExt)) {
                    var fd = new FormData();
                    fd.append("file", fileService.getFile());
                    console.log(fd);
                    console.log($scope.bulkCSVFile);
                    $("#addBulkLocalityMappingModal").modal("hide");
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.masterCacheManagement.uploadLocalities,
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).then(function success(response) {
                        if (response.status === 200 && response.data == true) {
                            bootbox.alert("Mappings added successfully!");
                            angular.element("input[type='file']").val(null);
                            fileService.push(null);
                            $scope.getLocalities();
                        } else {
                            $rootScope.showFullScreenLoader = false;
                            bootbox.alert("Error occurred.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    alert("please upload xls file only");
                    angular.element("input[type='file']").val(null);
                    fileService.push(null);
                    return;
                }
            };

        }]);
// .directive('fileModel', ['$parse', function ($parse) {
//     return {
//         restrict: 'A',
//         link: function (scope, element, attrs) {
//             var model = $parse(attrs.fileModel);
//             var modelSetter = model.assign;
//
//             element.bind('change', function () {
//                 scope.$apply(function () {
//                     modelSetter(scope, element[0].files[0]);
//                 });
//             });
//         }
//     };
// }]);