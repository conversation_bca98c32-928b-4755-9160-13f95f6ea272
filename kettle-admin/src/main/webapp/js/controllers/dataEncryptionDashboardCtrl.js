adminapp
	.controller(
		"DataEncryptionDashboardCtrl", ['$rootScope', '$scope', '$http', 'AppUtil', '$cookieStore',
		function($rootScope, $scope, $http, AppUtil, $cookieStore) {

			$scope.init = function() {
				clearObj();
			};

			function clearObj() {
				$scope.plainText = null;
				$scope.encryptedText = null;
			}

			$scope.isUndefinedOrNull = function(val) {
				return angular.isUndefined(val) || val === null || val === "";
			};

			$scope.encryptData = function() {
				$rootScope.showFullScreenLoader = true;
				$http(
					{
						method: 'POST',
						url: AppUtil.restUrls.dataEncryption.encrypt,
						data: $scope.plainText,
					})
					.then(
						function success(response) {
							$rootScope.showFullScreenLoader = false;
							if (response != undefined && response != null) {
								$scope.encryptedText = response.data;
							}
						},
						function error(response) {
							$rootScope.showFullScreenLoader = false;
							console.log("error:" + response);
							alert("Unable to encrypt Data");
						});
			};
		}]);