adminapp
	.controller(
		"DineInAppRestartDashboardCtrl", ['$rootScope', '$scope', '$http', 'AppUtil', '$cookieStore',
		function($rootScope, $scope, $http, AppUtil, $cookieStore) {
			
$scope.init = function() {
				clearObj();
			};

			function clearObj() {
				
			}


    $scope.restart = function (type) {
        var url = AppUtil.restUrls.serverRestart[type];
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'get',
            url: url
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            bootbox.alert(JSON.stringify("Sent Restart Event"));
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            bootbox.alert("error:" + JSON.stringify(response));
        });
    };


}]);