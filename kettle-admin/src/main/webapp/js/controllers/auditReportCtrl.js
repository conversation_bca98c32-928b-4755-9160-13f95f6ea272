adminapp.controller("auditReportCtrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {
 $scope.init = function () {
		 $rootScope.showFullScreenLoader = true;
			$http({
				  method: 'GET',
				  url: AppUtil.restUrls.unitMetaData.allUnits+'?category=ALL'
				}).then(function success(response) {
					$rootScope.showFullScreenLoader = false;
					$scope.cafelist = response.data;
				}, function error(response) {
					$rootScope.showFullScreenLoader = false;
					  console.log("error:"+response);
				});
		}
	 $scope.getAuditReport = function(){
		 $rootScope.showFullScreenLoader = true;
		 var unitId=$scope.selectedCafe.id;
		 $http({
			  method: 'POST',
			  url: AppUtil.restUrls.posMetaData.auditReport,
			  data: unitId
			}).then(function success(response) {
				$rootScope.showFullScreenLoader = false;
				if(response.data==true){
					$scope.msgAudit="Report has been <NAME_EMAIL>";
				}
				else{
					$scope.msgAudit="There was some error in generating report. <NAME_EMAIL>";
				}
			}, function error(response) {
				$rootScope.showFullScreenLoader = false;
				$scope.msgAudit="There was some error in generating report. <NAME_EMAIL>";
			});
		 }
});