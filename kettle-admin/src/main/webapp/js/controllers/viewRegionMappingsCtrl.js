adminapp.controller("viewRegionMappingsCtrl", function($scope,$rootScope,$http,AppUtil,$window, AppUtil){

    $scope.init = function (){
        $scope.allRegions = [];
        $scope.regionFulfillmentMappings = null;
        $scope.selectedRegion = null;
        $scope.selectedkitchen = null;
        $scope.selectedWarehouse = null;
        getAllRegions();
        getRegionFulfilmentMappings();
        getAllCompanies();
    }

    $scope.setData = function (){
        if($scope.selectedCompany == null) {
            $scope.selectedRegion = null;
            $window.confirm("Please select company!");
            return;
        }
        var warehouse = $scope.regionFulfillmentMappings[$scope.selectedRegion + '-' + 'WAREHOUSE' + '-' + $scope.selectedCompany.id];
        var kitchen = $scope.regionFulfillmentMappings[$scope.selectedRegion + '-' + 'KITCHEN' + '-' + $scope.selectedCompany.id];
        $scope.selectedKitchen = kitchen != null ? kitchen.unitName + "(" + kitchen.unitId + ")" : "None";
        $scope.selectedWarehouse = warehouse != null ? warehouse.unitName + "(" + warehouse.unitId + ")" : "None";

    }

    function getAllRegions() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.allRegions = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getRegionFulfilmentMappings() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmProductManagement.getRegionFulfillmentMappings
        }).then(function success(response) {
            $scope.regionFulfillmentMappings = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getAllCompanies() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.companies
        }).then(function success(response) {
            $scope.allCompanies = response.data;
            $scope.selectedCompany = $scope.allCompanies[1];
        });
    }




});
