adminapp
	.controller(
		"monkRealTimeDashboardCtrl", ['$rootScope', '$scope', '$http', 'AppUtil', '$cookieStore','$uibModal',
		function($rootScope, $scope, $http, AppUtil, $cookieStore,$uibModal) {

			$scope.init = function() {
				clearObj();
			};

			function clearObj() {
				$scope.dashboardData = null;
				$scope.orderId = null;
			}

			$scope.isUndefinedOrNull = function(val) {
				return angular.isUndefined(val) || val === null || val === "";
			};

			  $scope.openModal = function (row) {
                        var modalInstance = $uibModal.open({
                            animation: true,
                            templateUrl: 'monkRealTimeDashboardExtendedDetails.html',
                            controller: 'monkRealTimeDashboardExtendedDetailsCtrl',
                            scope: $scope,
                            resolve: {
                                data: function () {
                                    return row.orderItemAddons;
                                }
                            }
                        });
                    };

                      $scope.openStatusCodes = function (row) {
                                            var modalInstance = $uibModal.open({
                                                animation: true,
                                                templateUrl: 'monkRealTimeDashboardStatusCodes.html',
                                                controller: 'monkRealTimeDashboardExtendedDetailsCtrl',
                                                scope: $scope,
                                                resolve: {
                                                    data: function () {
                                                        return row.statusPayloadList;
                                                    }
                                                }
                                            });
                                        };


			$scope.submitOrderId = function() {
				$rootScope.showFullScreenLoader = true;
				$http(
					{
						method: 'POST',
						url: AppUtil.restUrls.chaiMonkDashboard.realTime,
						data: $scope.orderId,
					})
					.then(
						function success(response) {
							$rootScope.showFullScreenLoader = false;
							if (response != undefined && response != null) {
								$scope.dashboardData = response.data;
							}
						},
						function error(response) {
							$rootScope.showFullScreenLoader = false;
							console.log("error:" + response);
							alert("Unable to get monk real time data");
						});
			};

		}]);

adminapp.controller('monkRealTimeDashboardExtendedDetailsCtrl', function ($scope, $uibModalInstance,AppUtil, $filter,data) {
        	$scope.nestedData = data;
                            $scope.close = function () {
                                $uibModalInstance.dismiss('cancel');
          };
        });