/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("unitVersionDetailCtrl",
    function ($rootScope, $scope, $window, $http, $location, AppUtil, $cookieStore) {
        $rootScope.showFullScreenLoader = true;

        $scope.init = function () {

            $scope.selectedRegionList = [];
            $scope.unitFilter = null;
            $scope.isGetUnitsClicked=false;
            $scope.unitDetailList=[];
            $scope.checkAll=false;
            $scope.unitVersionList = [];
            $scope.appNameList = ["POS","CAFE_APP","KETTLE_CRM"];
            $scope.selectedAppNameList=[];
            $scope.allActiveVersionsAvailable = {};
            $scope.unitCategoryList = ["CAFE","COD","KITCHEN","DELIVERY","TAKE_AWAY","WAREHOUSE","OFFICE"];
            $scope.selectedUnitCategory = [];


            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.regions
            }).then(function success(response) {
                $scope.regionList = response.data;
                console.log($scope.regionList);
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnitsList
            }).then(function success(response) {
                $scope.unitlist = response.data;
                console.log("allUnits=", $scope.unitlist);
                for(var i=0;i<$scope.unitlist.length;i++){
                     if($scope.unitlist[i].status !=="ACTIVE"){
                         $scope.unitlist.splice(i,1);
                     }
                 }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
            });

             $http({
                 method: 'GET',
                 url: AppUtil.restUrls.versionManagement.getUnitActiveVersion
                  }).then(function success(response) {
                     $scope.unitVersionList = response.data;
                     for (var key in $scope.unitVersionList) {
                         var list = $scope.unitVersionList[key];
                         var application = [];
                         var i = list.length-1;
                         while(i >= 0){
                            if(application.includes(list[i].applicationName)){
                                 list.splice(i,1);
                            }
                            else{
                               application.push(list[i].applicationName);
                               i--;
                               continue;
                            }
                            i--;
                         }
                         $scope.unitVersionList[key] = list;
                      }
                      console.log("allUnits=", $scope.unitVersionList);
                      $rootScope.showFullScreenLoader = false;
                  }, function error(response) {
                     $rootScope.showFullScreenLoader = false;
                     console.log("error:" + response);
             });

             $http({
                   method: 'GET',
                   url: AppUtil.restUrls.versionManagement.getAllActiveVersions
                   }).then(function success(response) {
                       $scope.allActiveVersionsAvailable = response.data;
                       console.log("allActiveVersions=", $scope.allActiveVersionsAvailable);

                       $rootScope.showFullScreenLoader = false;
                   }, function error(response) {
                       $rootScope.showFullScreenLoader = false;
                       console.log("error:" + response);
              });
        };

        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };

        $scope.getUnitDetails = function(){
            $scope.unitDetailList = [];
            if($scope.selectedRegionList.length === 0){
                $scope.unitDetailList = $scope.unitlist ;
            }
            else{
                for(var i=0;i<$scope.unitlist.length;i++){
                    if($scope.selectedRegionList.includes($scope.unitlist[i].region) && $scope.unitlist[i].status === 'ACTIVE'){
                        $scope.unitDetailList.push($scope.unitlist[i]);
                    }
                }
            }
            var x = [];
            if($scope.selectedUnitCategory.length !== 0){
                for(var i=0;i<$scope.unitDetailList.length;i++){
                   if($scope.selectedUnitCategory.includes($scope.unitDetailList[i].category) && $scope.unitDetailList[i].status === 'ACTIVE'){
                     x.push($scope.unitDetailList[i])
                   }
                }
                $scope.unitDetailList = x;
            }
            console.log("filtered units",$scope.unitDetailList)
        }

        $scope.toShow = function(version){
            if($scope.selectedAppNameList.length === 0){
            return true;
            }
            else if($scope.selectedAppNameList.includes(version.applicationName)){
            return true;
            }
            else{
            return false;
            }
        }


});
