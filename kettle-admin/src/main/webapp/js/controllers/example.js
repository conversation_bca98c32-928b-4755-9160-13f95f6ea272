angular.module('adminapp').controller('TimepickerDemoCtrl', function ($scope, $log) {
  
	  
	  $scope.businessHoursObj = {
		  dayOfTheWeekNumber: 7,
	      dayOfTheWeek: "Saturday",
	      noOfShifts: 1,
	      isOperational: true,
	      hasDelivery: true,
	      hasDineIn: true,
	      hasTakeAway: false,
	      dineInOpeningTime: "09:00:00",
	      dineInClosingTime: "19:00:00",
	      deliveryOpeningTime: "09:00:00",
	      deliveryClosingTime: "19:00:00",
	      takeAwayOpeningTime: null,
	      takeAwayClosingTime: null,
	      shiftOneHandoverTime: null,
	      shiftTwoHandoverTime: null,
	      status: "ACTIVE"
	  }
	  
	  $scope.checkAllDays = function () {
		  
		  console.log($scope.businessHoursObj);
		  
		    if ($scope.checkedDays) {
	            $scope.checkedDays = true;
	        } else {
	            $scope.checkedDays = false;
	        }
	        angular.forEach($scope.Items, function (item) {
	            item.daysTick = $scope.checkedDays;
	        });

	    };
	    
	    
	    $scope.checkAllDineIn = function () {
	    	
	    	console.log("hello"+$scope.time3);
		    if ($scope.checkedDine) {
	            $scope.checkedDine = true;
	        } else {
	            $scope.checkedDine = false;
	        }
	        angular.forEach($scope.Items, function (item) {
	        	console.log(item);
	        	
	            item.dineTick = $scope.checkedDine;
	        });

	    };
	  
	    
	    $scope.checkAllCodIn = function () {
		    if ($scope.checkedCod) {
	            $scope.checkedCod = true;
	        } else {
	            $scope.checkedCod = false;
	        }
	        angular.forEach($scope.Items, function (item) {
	            item.codeTick = $scope.checkedCod;
	        });

	    };
	
	    $scope.checkTakeAwayIn = function () {
		    if ($scope.checkedTakeAway) {
	            $scope.checkedTakeAway = true;
	        } else {
	            $scope.checkedTakeAway = false;
	        }
	        angular.forEach($scope.Items, function (item) {
	            item.takeawayTick = $scope.checkedTakeAway;
	        });

	    };
	    
	    $scope.checkAllHandOverIn = function () {
		    if ($scope.checkedHandOver) {
	            $scope.checkedHandOver = true;
	        } else {
	            $scope.checkedHandOver = false;
	        }
	        angular.forEach($scope.Items, function (item) {
	            item.handoverTick = $scope.checkedHandOver;
	        });

	    };
	
  $scope.mytime = new Date();

  $scope.hstep = 1;
  $scope.mstep = 15;

  $scope.options = {
    hstep: [1, 2, 3],
    mstep: [1, 5, 10, 15, 25, 30]
  };

  $scope.ismeridian = false;
  $scope.toggleMode = function() {
    $scope.ismeridian = ! $scope.ismeridian;
  };

  $scope.update = function() {
    var d = new Date();
    d.setHours( 14 );
    d.setMinutes( 0 );
    $scope.mytime = d;
  };

  $scope.changed = function () {
    $log.log('Time changed to: ' + $scope.mytime);
  };

  $scope.clear = function() {
    $scope.mytime = null;
  };
});