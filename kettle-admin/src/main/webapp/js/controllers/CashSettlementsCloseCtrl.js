/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("CashSettlementsCloseController", function ($rootScope,$scope, $location, $http, AppUtil, $cookieStore, $uibModal) {

    $rootScope.$on('cashSettlement',function(e,args){
        $scope.init(args.searchByUnitId);
    });


    $scope.init = function (searchByUnitId) {
        $scope.today = new Date(new Date().setDate(new Date().getDate() + 1)).toString();
        $scope.showPullView = false;
        $scope.searchByUnit = searchByUnitId || $location.search().searchByUnitId;
        $scope.paymentModes = [];
        $scope.showFullScreenLoader = false;
        $scope.outletList = [];
        if($scope.searchByUnit){
            fetchOutlets();
        }else{
            fetchPaymentModes();
        }
        $scope.pullSettlements = [];
    };

    $scope.getPullSettlements = function () {
        if($scope.searchByUnit){
            var obj = {
                unitId: $scope.unit.id,
                settlementId: $scope.settlementId
            };
            $scope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.cashManagement.pullSettlementsOpenGet,
                data: obj
            }).then(function success(response) {
                if (response != null) {
                    $scope.pullSettlements = response.data;
                    $scope.showFullScreenLoader = false;
                }
            }, function error(response) {
                $scope.showFullScreenLoader = false;
                console.log("error:" + response);
            });
        }else{
            $scope.getPullSettlementsByType();
        }

    };
    
    $scope.changeSettlement=function(paymentMode){
    	$scope.paymentMode=paymentMode;
    }
    
    $scope.getPullSettlementsByType = function () {
        var obj = {
            settlementTypeId: $scope.paymentMode.id
        };
        $scope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.cashManagement.pullSettlementsOpenGetByType,
            data: obj
        }).then(function success(response) {
            $scope.showFullScreenLoader = false;
            if (response != null) {
                $scope.pullSettlements = response.data;
            }
        }, function error(response) {
            $scope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

    };

    $scope.viewPull = function (settlement) {
        $scope.selectedSettlement = settlement;
        $scope.showPullView = true;

    };

    $scope.hidePullView = function () {
        $scope.showPullView = false;
    };

    $scope.viewDenominations = function (denominations) {
        $scope.denomEntity = denominations;
        $uibModal.open({
            animation: true,
            templateUrl: "viewDenominationModal.html",
            controller: 'viewDenominationModalCtrl',
            backdrop: 'static',
            scope: $scope,
            size: "lg"
        });
    };

    $scope.viewClosure = function (closurePaymentDetail) {
        $scope.closureEntity = closurePaymentDetail;
        $uibModal.open({
            animation: true,
            templateUrl: "viewClosureModal.html",
            controller: 'viewClosureModalCtrl',
            backdrop: 'static',
            scope: $scope,
            size: "lg"
        });
    };

    $scope.closeSettlement = function (settlement,index) {
        $scope.selectedSettlement = settlement;
        var modalInstance = $uibModal.open({
            animation: true,
            templateUrl: "CloseSettlementModal.html",
            controller: 'CloseSettlementModalCtrl',
            backdrop: 'static',
            scope: $scope,
            size: "lg"
        });

        modalInstance.result.then(function (result) {
            if(result){
                $scope.pullSettlements.splice(index,1);
            }
        });
    };

    function fetchOutlets() {
        $scope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnitsList
        }).then(function success(response) {
            $scope.showFullScreenLoader = false;
            $scope.outletList = response.data;
        }, function error(response) {
            $scope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }
    
    function fetchPaymentModes() {
        $scope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.offerManagement.paymentModes
        }).then(function success(response) {
            $scope.showFullScreenLoader = false;
            $scope.paymentModes = response.data;
        }, function error(response) {
            $scope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.autoClose = function(selectedSettlement,index){
        selectedSettlement.settledAmount = selectedSettlement.totalAmount;
        selectedSettlement.unsettledAmount = 0;
        selectedSettlement.closingAmount = selectedSettlement.settledAmount;
        console.log("selectedSettlement :::::",selectedSettlement);
        $scope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.cashManagement.pullSettlementClose,
            data: selectedSettlement
        }).then(function success(response) {
            $scope.showFullScreenLoader = false;
            if (response != null) {
                alert("Updated successfully!");
                $scope.pullSettlements.splice(index,1);
            }
        }, function error(response) {
            $scope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.downloadSlip = function(settlementId){
        //window.location = AppUtil.restUrls.cashManagement.downloadSlip + "?id="+settlementId;
        $http({
            method:"GET",
            url:AppUtil.restUrls.cashManagement.downloadSlip + "?id="+settlementId,
            responseType: 'arraybuffer'
        }).then(function(response){
            var blob = new Blob([response.data], {type: AppUtil.mimeTypes["JPG"]}, "slip"+settlementId+".jpg");
            saveAs(blob, "slip"+settlementId+".jpg");
        },function(error){
            console.log("Could not download the document... Please try again");
        });
    };

});

adminapp.controller('CloseSettlementModalCtrl', function ($scope, $uibModalInstance, AppUtil, $http, $filter) {
    function setUnsettledAmount() {
        if($scope.selectedSettlement.settlementAmount!=null){
            if($scope.selectedSettlement.totalAmount == $scope.selectedSettlement.settlementAmount){
                $scope.selectedSettlement.closingAmount = $scope.selectedSettlement.settlementAmount;
            }

            if($scope.selectedSettlement.closingAmount!=null &&
                ($scope.selectedSettlement.settlementAmount == $scope.selectedSettlement.closingAmount)){
                $scope.selectedSettlement.unsettledAmount = 0;
            }
        }
    }

    $scope.init = function () {
        $scope.loading = false;
        setUnsettledAmount();
    };
    $scope.ok = function () {
        if ($scope.selectedSettlement.settlementAmount == null || $scope.selectedSettlement.unsettledAmount == null
            || $scope.selectedSettlement.closingAmount == null) {
            alert("Please fill all the values properly!");
            return false;
        }
        $scope.loading = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.cashManagement.pullSettlementClose,
            data: $scope.selectedSettlement
        }).then(function success(response) {
            //console.log(response);
            $scope.loading = false;
            if (response != null) {
                $uibModalInstance.close(true);
            }
        }, function error(response) {
            $scope.loading = false;
            console.log("error:" + response);
        });

    };
    $scope.cancel = function () {
        $scope.selectedSettlement.unsettledAmount = null;
        $scope.selectedSettlement.closingAmount = null;
        $scope.selectedSettlement.settlementAmount = null;
        $uibModalInstance.dismiss("cancel");
    };

    $scope.calculateUnsettled = function () {
        $scope.selectedSettlement.unsettledAmount = $scope.selectedSettlement.totalAmount - $scope.selectedSettlement.settlementAmount;
        $scope.selectedSettlement.closingAmount = $scope.selectedSettlement.settlementAmount;
    };
});