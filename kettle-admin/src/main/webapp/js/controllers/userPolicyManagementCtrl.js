/*
 * Created By Shanmukh
 */
adminapp.controller("userPolicyManagementCtrl", function ($scope, $http, $location, $window, $rootScope, $timeout,
                                                          AuthService, AppUtil, fileService) {
    $scope.init = function () {
        $rootScope.showFullScreenLoader = false;
        $scope.userPolicies = [];
        $scope.getAllPolicies();
        $scope.uploadedDoc = null;
        $scope.isDocRequired = false;

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.allActiveRoles
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.activeRoles = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $scope.categoryGridOptions = $scope.createGridOptions();
        $scope.selectedDepartment = null;
        $scope.selectedDesignation = null;
        $scope.departmentList = [];
        $scope.designationList = [];
        $scope.getAllDepartments();
        $scope.getAllDesignations();
    };

    $scope.getAllDepartments = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.departments
        }).then(function success(response) {
            $scope.departmentList = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.getAllDesignations = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.designations
        }).then(function success(response) {
            $scope.designationList = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.setSelectedDepartment = function (dept) {
        $scope.selectedDepartment = dept;
        if (dept != null && $scope.selectedDesignation != null && $scope.selectedDesignation != '') {
            $scope.setPolicyName();
        }
    };

    $scope.setSelectedDesignation = function (desg) {
        $scope.selectedDesignation = desg;
        if ($scope.selectedDesignation != null && $scope.selectedDepartment == null) {
            bootbox.alert("Please select a department..!");
            $scope.selectedDesignation = null;
        } else {
            if (desg != null) {
                $scope.setPolicyName();
            }
        }
    };

    $scope.closeModal = function () {
        $scope.selectedDepartment = null;
        $scope.selectedDesignation = null;
        $timeout(function () {
            $('#selectedDepartment').val(null).trigger('change');
            $('#selectedDesignation').val(null).trigger('change');
        });
    };

    $scope.createGridOptions = function () {
        return {
            enableFiltering: true,
            enableColumnResizing: true,
            columnDefs: [{
                field: 'actionDetailId',
                displayName: 'Action Detail Id',
                enableCellEdit: false
            }, {
                field: 'actionType',
                displayName: 'Action Type',
                enableCellEdit: false
            }, {
                field: 'actionCategory',
                displayName: 'Action Category',
                enableCellEdit: false
            }, {
                field: 'actionCode',
                displayName: 'Action Code',
                enableCellEdit: false
            }, {
                field: 'actionDescription',
                displayName: 'Action Description',
                enableCellEdit: false,
            }]
        };
    }

    $scope.getAllPolicies = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.getUserPolicies
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.userPolicies = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.showActionsForRole = function (role) {
        $scope.currentSelectedRole = role.roleId;
        if (role.expanded != null && role.expanded != undefined) {
            role.expanded = !role.expanded;
            $scope.categoryGridOptions.data = role.roleActions;
        } else {
            role.expanded = true;
            $scope.categoryGridOptions.data = role.roleActions;
        }
        angular.forEach($scope.finalRows, function (row) {
            if (row.roleId != $scope.currentSelectedRole && row.expanded) {
                row.expanded = false;
            }
        });
    };

    $scope.setSearchPolicy = function (searched) {
        $scope.searchPolicy = searched;
    };

    $scope.setFilterRole = function (searched) {
        $scope.filterBySearch = searched;
    };

    $scope.setCurrentViewType = function (action) {
        $scope.filterBySearch = "";
        $('#filterBySearch').val("").trigger('change');
        $scope.uploadedDoc = null;
        $scope.isDocRequired = false;
        if ($scope.currentAction == 'ADD') {
            $scope.isDocRequired = true;
            if (action == 'previous') {
                if ($scope.currentViewType == "PREVIEW_ROLES_SCREEN") {
                    $scope.currentViewType = "ALL_ROLES_SCREEN";
                } else {
                    $scope.currentViewType = "ADD_POLICY";
                }
            } else {
                if ($scope.currentViewType == "ADD_POLICY") {
                    if ($scope.selectedDepartment == undefined || $scope.selectedDepartment == null || $scope.selectedDepartment == '') {
                        bootbox.alert("Please Select the Department ...!");
                        return;
                    }
                    if ($scope.currentSelectedPolicy.policyName == undefined || $scope.currentSelectedPolicy.policyName == null || $scope.currentSelectedPolicy.policyName == '') {
                        bootbox.alert("Please Enter the Policy Name...!");
                        return;
                    }
                    if ($scope.selectedDesignation == undefined || $scope.selectedDesignation == null || $scope.selectedDesignation == '') {
                        bootbox.alert("Please Select the Designation...!");
                        return;
                    }
                    if ($scope.currentSelectedPolicy.policyDescription == undefined || $scope.currentSelectedPolicy.policyDescription == null || $scope.currentSelectedPolicy.policyDescription == '') {
                        bootbox.alert("Please Enter the Policy Description...!");
                        return;
                    }
                    if ($scope.currentSelectedPolicy.policyName != null && $scope.currentSelectedPolicy.policyName.indexOf(' ') !== -1) {
                        $scope.currentSelectedPolicy.policyName = null;
                        bootbox.alert("No Spaces Allowed in the Policy Name....!");
                    }
                    for (var i = 0; i < $scope.userPolicies.length; i++) {
                        if ($scope.departmentDesignation == $scope.userPolicies[i].departmentDesignation || $scope.userPolicies[i].policyName === $scope.currentSelectedPolicy.policyName) {
                            bootbox.alert("Policy With same Department and Designation " + $scope.userPolicies[i].policyName + " already Exists..!");
                            $scope.selectedDesignation = null;
                            $scope.currentSelectedPolicy['policyName'] = null;
                            $scope.departmentDesignation = null;
                            $timeout(function () {
                                $('#selectedDesignation').val(null).trigger('change');
                            });
                            return;
                        }
                    }
                    $scope.currentViewType = "ALL_ROLES_SCREEN";
                } else {
                    $scope.currentViewType = "PREVIEW_ROLES_SCREEN";
                }
            }
        } else {
            if (action == 'previous') {
                $scope.currentViewType = "ALL_ROLES_SCREEN";
            } else {
                $scope.currentViewType = "PREVIEW_ROLES_SCREEN";
                var roleIds = [];
                $scope.activeEmployeeRoles.forEach(function (role) {
                    if (role.status == 'ACTIVE') {
                        roleIds.push(role.id);
                    }
                });
                $scope.isDocRequired = false;
                if ($scope.beforeUpdateRoles < roleIds.length) {
                    $scope.isDocRequired = true;
                }
            }
        }
        $scope.filterBySearch = null;
        $scope.getFilteredRows();
    };

    $scope.getFilteredRows = function () {
        if ($scope.currentViewType == "ALL_ROLES_SCREEN") {
            $scope.finalRows = $scope.activeEmployeeRoles;
        } else {
            if ($scope.currentViewType != "ADD_POLICY") {
                if ($scope.activeEmployeeRoles == undefined || $scope.activeEmployeeRoles == null) {
                    $scope.activeEmployeeRoles = [];
                }
                $scope.filteredRows = $scope.activeEmployeeRoles.filter(function (row) {
                    return row.status == "ACTIVE";
                });
                if ($scope.filteredRows.length == 0) {
                    bootbox.alert("Please Select at least One Role to Proceed...!");
                    $scope.currentViewType = "ALL_ROLES_SCREEN";
                    $scope.finalRows = $scope.activeEmployeeRoles;
                } else {
                    $scope.currentViewType = "PREVIEW_ROLES_SCREEN";
                    $scope.finalRows = $scope.filteredRows;
                }
            }
        }
    };

    $scope.setPolicyName = function () {
        if ($scope.selectedDepartment != null && $scope.selectedDesignation != null) {
            var departmentCode = JSON.parse($scope.selectedDepartment).name.replace(/\s/g, '').toUpperCase();
            var designationCode = JSON.parse($scope.selectedDesignation).name.replace(/\s/g, '').toUpperCase();
            $scope.currentSelectedPolicy['policyName'] = departmentCode + "_" + designationCode;
            $scope.departmentDesignation = JSON.parse($scope.selectedDepartment).id + "_" + JSON.parse($scope.selectedDesignation).id;
            if ($scope.currentSelectedPolicy.policyName != null && $scope.currentSelectedPolicy.policyName.indexOf(' ') !== -1) {
                $scope.currentSelectedPolicy.policyName = null;
                $scope.departmentDesignation = null;
                bootbox.alert("No Spaces Allowed in the Policy Name....!");
            }
        }
    };

    $scope.setPolicyDescription = function (policyDescription) {
        $scope.currentSelectedPolicy['policyDescription'] = policyDescription;
    };

    $scope.openPolicyRoles = function (policy, action) {
        $scope.uploadedDoc = null;
        var roles = null;
        if (action == "ADD") {
            $scope.currentViewType = "ADD_POLICY";
            $scope.currentSelectedPolicy = {};
        } else {
            $scope.currentViewType = "ALL_ROLES_SCREEN";
            roles = policy.userPolicyRoleMappings;
            if (roles != null) {
                $scope.beforeUpdateRoles = roles.length;
            }
            $scope.currentSelectedPolicy = policy;
        }
        $scope.currentAction = action;
        $scope.activeEmployeeRoles = angular.copy($scope.activeRoles);
        $scope.activeEmployeeRoles.forEach(function (role) {
            role.status = 'IN_ACTIVE';
        });
        if (roles != null && roles.length > 0) {
            for (var i in roles) {
                $scope.activeEmployeeRoles.forEach(function (role) {
                    if (role.id == roles[i].roleId) {
                        role.status = 'ACTIVE';
                        role.roleId = roles[i].roleId;
                    }
                });
            }
            $scope.activeEmployeeRoles.sort(function (a, b) {
                return a.status.localeCompare(b.status)
            });
        }
        $("#editPolicyRolesModal").modal("show");
        $scope.getFilteredRows();
    };

    $scope.changeRoleStatus = function (role) {
        if (role.status == "ACTIVE") {
            role.status = "IN_ACTIVE";
        } else {
            role.status = "ACTIVE";
        }
    };


    $scope.submitUpdatePolicyRoles = function () {

        if ($scope.activeEmployeeRoles == null || $scope.activeEmployeeRoles.length == 0) {
            return;
        }
        var roleIds = [];
        $scope.activeEmployeeRoles.forEach(function (role) {
            if (role.status == 'ACTIVE') {
                roleIds.push(role.id);
            }
        });
        if ($scope.currentAction != "ADD") {
            if ($scope.beforeUpdateRoles < roleIds.length && $scope.uploadedDoc == null) {
                bootbox.alert("Please Upload Policy Edit Proof..!");
                return;
            }
        }
        $rootScope.showFullScreenLoader = true;
        var updateObject = {
            roles: roleIds,
            updatedBy: AppUtil.getCurrentUser().id
        }

        if ($scope.currentAction == "ADD") {
            updateObject.policyName = $scope.currentSelectedPolicy.policyName.trim();
            updateObject.policyDescription = $scope.currentSelectedPolicy.policyDescription;
            updateObject.updatePolicy = false;
        } else {
            updateObject.userPolicyId = $scope.currentSelectedPolicy.userPolicyId;
            updateObject.updatePolicy = true;
        }

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.createUpdatePolicyRoles,
            data: updateObject,
            params: {
                uploadedDocId: $scope.isDocRequired ? $scope.uploadedDoc.documentId : null,
                departmentDesignation: $scope.currentAction == "ADD" ? JSON.parse($scope.selectedDepartment).id + "_" + JSON.parse($scope.selectedDesignation).id : null
            }
        }).then(function success(response) {
            if (response.status == 200) {
                $("#editPolicyRolesModal").modal('toggle');
                alert("Policy Roles " + ($scope.currentAction == "ADD" ? "ADDED" : "UPDATED") + " Successfully!");
                $rootScope.showFullScreenLoader = false;
                $scope.init();
            } else {
                alert("Error in " + ($scope.currentAction == "ADD" ? "ADDING" : "UPDATING") + " roles for the policy!");
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            alert("Error in " + ($scope.currentAction == "ADD" ? "ADDING" : "UPDATING") + " roles for the policy!");
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

    };

    $scope.uploadPolicyEditProof = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.uploadedDoc = null;
        var fd = new FormData();
        var file = fileService.getFile();
        if (file == undefined || file == null) {
            bootbox.alert("Please Select a file to upload..!");
            $rootScope.showFullScreenLoader = false;
            return;
        }

        var fileExt = AppUtil.getFileExtension(file.name);
        var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
        if (file.size > fileLimit) {
            var msg = ""
            if (fileExt.toLowerCase() == 'png') {
                msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
            }
            bootbox.alert('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
            $rootScope.showFullScreenLoader = false;
            return;
        }
        fd.append('file', file);
        fd.append('type', "OTHERS");
        fd.append('mimeType', fileExt.toUpperCase());
        fd.append('userId', AppUtil.getCurrentUser().id);
        fd.append('docType', "EMPLOYEE_POLICY_EDIT");
        fd.append('file', file);
        fd.append('docName', "EMPLOYEE_POLICY_EDIT");

        $http({
            url: AppUtil.restUrls.userManagement.uploadDocument,
            method: 'POST',
            data: fd,
            headers: {'Content-Type': undefined},
            transformRequest: angular.identity
        }).success(function (response) {
            console.log(response);
            $rootScope.showFullScreenLoader = false;
            if (response.documentId == undefined || response.documentId == null) {
                bootbox.alert("Something Went Wrong Please try Again..!");
            } else {
                bootbox.alert("File Uploaded Successfully..!");
                $scope.uploadedDoc = response;
            }
        }).error(function (response) {
            $rootScope.showFullScreenLoader = false;
            alert("Upload failed");
            $scope.uploadedDoc = null;
        });
    };
});

