/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


adminapp.controller("AppOfferDataController", function ($rootScope, $scope, $http, $location, $cookieStore, AppUtil, fileService) {

    $scope.init = function () {
        $rootScope.enableScreenFilter = true;
        $scope.userDetails=AppUtil.getUserValues();

        $scope.offerTypes = [];
        $scope.actionCategories = [];
        $scope.actionLists = [];
        // $scope.imageSuffix = "https://d3rcyooxiaudps.cloudfront.net/"
        $scope.imageSuffix = AppUtil.getImageUrl().offerImage;

        $scope.couponalreadypresent = false;
        $scope.brandIdNotEditable = false;
        $scope.reportCategories = null;
        $scope.errorMessage = null;
        $scope.OfferStatus = [{name: "ACTIVE", value: "ACTIVE"}, {name: "IN ACTIVE", value: "IN_ACTIVE"}];
        $scope.offerObject = {};
        $scope.offerObject.selectedOfferStatus = $scope.OfferStatus[0];
        $scope.applicabilityOptions = ['Y','N']
        $scope.appOfferApplicabilityList = {};
        $scope.isAddNewOffer = false;
        $scope.isEditOffer = false;
        $scope.isViewOffer = false;
        $scope.isCouponVerified = false;
        $scope.offerIndex;
        $scope.offerObject.CategoriesSelected = [];
        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b>{{option.unitId}} {{option.unitName}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };
        $scope.Categories = [];
        $scope.offerObject.couponCOde = "";
        $scope.unitList = [];
        // $scope.description="";
        $scope.offerObject.startDate = "";
        $scope.offerObject.endDate = "";
        $scope.offerObject.couponId;
        $scope.offerObject.offerId;
        $scope.offerObject.brandId = null;
        $scope.offerObject.listImageToUpload;
        $scope.offerObject.gridImageToUpload;
        $scope.offerObject.listImage = null;
        $scope.offerObject.gridImage = null;
        $scope.offerObject.selectedPartnerId;
        $scope.offerObject.appOfferType = null;
        $scope.offerObject.appOfferApplicabilityList = [{
            appOfferApplicabilityFlag: "CUSTOMER_PROFILE_COMPLETION",
            appOfferApplicabilityId : null,
            appOfferDetail: null,
            isApplicable :"N",
        }]
        $scope.searchFields = {}
        $scope.selectedPartnerId=null;

        $scope.partnerId=[{name:"DINE IN",partnerId:21},{name:"BAZAAR",partnerId: 23}];

        $scope.brands = [];
        $scope.filteredUnitList = [];

        Object.keys(AppUtil.BRAND_IDS).forEach(id => {
            $scope.brands.push( {id: parseInt(id), name: AppUtil.BRAND_IDS[id]} );
        });
        console.log($scope.brands);

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + "?category=CAFE"
        }).then(function success(response) {
            $scope.unitList = response.data;
            $scope.filteredUnitList = $scope.unitList;
            console.log("Unit List is ::: ", $scope.unitList);
            $scope.createCategories();
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.createCategories = function () {
            $scope.Categories = [];
            for (var i = 0; i < $scope.filteredUnitList.length; i++) {
                $scope.Categories.push({unitName: $scope.filteredUnitList[i].name, unitId: $scope.filteredUnitList[i].id});
            }
        }

        // $http({
        //     method: 'GET',
        //     url: AppUtil.restUrls.offerManagement.appOfferTypes
        // }).then(function success(response) {
        //     // $scope.offerTypes = response.data;
        //     // $scope.offerObject.selectedOfferType = $scope.offerTypes[0];
        //     console.log($scope.offerObject.selectedOfferType)
        // }, function error(response) {
        //     console.log("error:" + response);
        // });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.getBannerMetadata
        }).then(function success(response) {
            $scope.metadata = response.data;

            $scope.metadata.map(function (data) {
                if (data.type == 'ACTION_LIST') {
                    $scope.actionLists.push(data);
                } else if (data.type == 'ACTION_TYPE') {
                    $scope.actionCategories.push(data);
                } else if (data.type == 'BANNER_TYPE') {
                    $scope.offerTypes.push(data)
                }
                // $scope.offerObject.actionCategory=$scope.actionCategories[0];
                // $scope.offerObject.selectedOfferType=$scope.offerTypes[0];
                // $scope.offerObject.offerActionType=$scope.actionLists[0];


            })
            console.log("banner type:", $scope.offerTypes)
            console.log("action type:", $scope.actionCategories)
            // console.log("action list:",$scope.actionList)
        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.getCategoryFromMenuSequence,
            params: {
                "menuApp": "DINE_IN_APP"
            }
        }).then(function success(response) {
            $scope.categoryMenuList = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.appOfferTypeList = ['GENERIC_OFFER', 'CUSTOMER_OFFER']
    };

    $scope.addNewAppOfferDialog = function () {
        $scope.isAddNewOffer = true;
        $scope.isEditOffer = false;
        $scope.isViewOffer = false;
        $scope.offerObject.appOfferApplicabilityList = [{
            appOfferApplicabilityFlag: "CUSTOMER_PROFILE_COMPLETION",
            appOfferApplicabilityId : null,
            appOfferDetail: null,
            isApplicable :"N",
        }]
        $("#AddNewOfferAppModalData").modal("show");
    }

    $scope.hideNewAppOfferDialog = function () {
        $("#AddNewOfferAppModalData").modal("hide");
    }


    $scope.viewUnitLists = function (unitList) {
        $scope.unitDetailsList = unitList;
        $('#UnitListViewModal').modal('show');
    }
    $scope.openProductImageModal = function (imageUrl) {
        $scope.imageSrc = imageUrl;
        if (imageUrl == null) {
            alert("Image not found!")
        }
        else {
            $("#displayImageModal").modal("show");
        }

    };

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function isImage(fileExt) {
        return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png";
    }

    $scope.addNewAppOfferData = function (offerType, couponCode, startDate, endDate, offerStatus, CategoriesSelected, actiontype, description, bannerGoTLink, couponId, offerId,partnerId, appOfferType,appOfferApplicabilityList, brandId) {
        console.log($scope.offerObject.listImage)
        console.log($scope.offerObject.appOfferApplicabilityList)
        console.log(appOfferApplicabilityList)

        if (!$scope.offerObject.selectedOfferType) {
            alert("please select offer type")
            return;
        }

        if (!$scope.offerObject.selectedPartnerId) {
            alert("please select Partner name")
            return;
        }

        if (!$scope.offerObject.actionCategory) {
            alert("please select action category")
            return;
        }

        if ($scope.offerObject.actionCategory.value == 'CHANGE_CART_SOURCE') {
            if ($scope.offerObject.offerActionType == "" || $scope.offerObject.offerActionType == null) {
                alert("Please Provide Action type");
                return false;
            }
        }

        if ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE') {
            if ($scope.offerObject.couponCOde == "" || $scope.offerObject.couponCOde == null) {
                alert("Please Provide Coupon Code");
                return false;
            }
        }
        if ($scope.isCouponVerified == false && ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE')) {
            alert("Please verify coupon first");
            return;
        }

        if ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE') {
            if ($scope.offerObject.couponId == null || $scope.offerObject.couponId == "") {
                alert("Please Provide Coupon Id");
                return;
            }
        }
        if ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE') {
            if ($scope.offerObject.offerId == null || $scope.offerObject.offerId == "") {
                alert("Please Provide Offer Id");
                return;
            }
        }
        if ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE') {
            if ($scope.offerObject.brandId == null || $scope.offerObject.brandId == "") {
                alert("Please Provide Brand Id");
                return;
            }
        }
        if ($scope.offerObject.description == null || $scope.offerObject.description == "") {
            alert("Please Provide Desc.");
            return;
        }


        if ($scope.offerObject.startDate == null || $scope.offerObject.startDate == "") {
            alert("Please Provide Start Date");
            return;
        }


        if ($scope.offerObject.endDate == null || $scope.offerObject.endDate == "") {
            alert("Please Provide End Date");
            return;
        }

        if ($scope.offerObject.selectedOfferStatus == null || $scope.offerObject.selectedOfferStatus == "") {
            alert("Please Provide Status");
            return;
        }

        if ($scope.offerObject.appOfferType == null || $scope.offerObject.appOfferType == "") {
            alert("Please Provide App Offer Type");
            return;
        }

        // if($scope.offerObject.selectedOfferType.offerType=='BANNER' && $scope.offerObject.redirectionLink==null){
        //     alert("please entert banner go to link");
        //
        // }

        if ($scope.offerObject.brandId == null || $scope.offerObject.brandId == undefined || $scope.offerObject.brandId == "") {
            alert("Please Provide Brand ID");
            return;
        }

        console.log($scope.offerObject);
        $scope.offerObject.appOfferApplicabilityList = appOfferApplicabilityList;
        $scope.offerObject.offerActionType = actiontype;
        $scope.offerObject.selectedOfferType = offerType.value;
        $scope.offerObject.selectedPartnerId=partnerId;
        $scope.offerObject.couponCOde = couponCode;
        $scope.offerObject.startDate = startDate;
        $scope.offerObject.endDate = endDate;
        $scope.offerObject.selectedOfferStatus = offerStatus;
        $scope.offerObject.description = description;
        $scope.offerObject.couponId = couponId;
        $scope.offerObject.offerId = offerId;
        $scope.offerObject.brandId = brandId;
        $scope.offerObject.CategoriesSelected = CategoriesSelected;
        $scope.offerObject.unitDetailList = [];
        $scope.offerObject.appOfferType = appOfferType;
        for (var i = 0; i < $scope.offerObject.CategoriesSelected.length; i++) {
            $scope.offerObject.unitDetailList.push({
                unitId: $scope.offerObject.CategoriesSelected[i].unitId,
                unitName: $scope.offerObject.CategoriesSelected[i].unitName
            });
        }
        if ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE') {
            // if ($scope.offerObject.listImage == null) {
            //     console.log("list image null");
            //     alert("Please Provide list Image");
            //     return;
            // }
            // if ($scope.offerObject.gridImage == null) {
            //     alert("Please Provide grid Image");
            //     return;
            // }

            var appOfferDetail = {
                appOfferId: null,
                offerType: $scope.offerObject.selectedOfferType,
                partnerId:$scope.offerObject.selectedPartnerId,
                description: $scope.offerObject.description,
                redirectionLink: $scope.offerObject.redirectionLink,
                title: $scope.offerObject.title,
                actionCategory: $scope.offerObject.actionCategory.value,
                offerIndex: null,
                startDate: $scope.offerObject.startDate,
                endDate: $scope.offerObject.endDate,
                couponCode: $scope.offerObject.couponCOde,
                offerId: $scope.offerObject.offerId,
                brandId: $scope.offerObject.brandId,
                couponId: $scope.offerObject.couponId,
                listImage: $scope.offerObject.listImage != null ? $scope.offerObject.listImage.name : null,
                listImageUrl: $scope.offerObject.listImage != null ? $scope.offerObject.listImage.url : null,
                gridImage: $scope.offerObject.gridImage != null ? $scope.offerObject.gridImage.name : null,
                gridImageUrl: $scope.offerObject.gridImage != null ? $scope.offerObject.gridImage.url : null,
                actionType: null,
                status: $scope.offerObject.selectedOfferStatus.value,
                unitDetailList: $scope.offerObject.unitDetailList,
                menuCategoryId: $scope.offerObject.menuCategoryId,
                appOfferType: $scope.offerObject.appOfferType,
                /*menuCategoryImage: $scope.offerObject.menuCategoryImage != null ? $scope.offerObject.menuCategoryImage.name : null,
                menuCategoryDescription: $scope.offerObject.menuCategoryDescription*/
            };
        } else {
            // if ($scope.offerObject.gridImage == null) {
            //     alert("Please Provide grid Image");
            //     return;
            // }
            // if ($scope.offerObject.listImage == null) {
            var appOfferDetail = {
                appOfferId: null,
                offerType: $scope.offerObject.selectedOfferType,
                partnerId:$scope.offerObject.selectedPartnerId,
                description: $scope.offerObject.description,
                redirectionLink: $scope.offerObject.redirectionLink,
                title: $scope.offerObject.title,
                actionCategory: $scope.offerObject.actionCategory.value,
                offerIndex: null,
                startDate: $scope.offerObject.startDate,
                endDate: $scope.offerObject.endDate,
                couponCode: $scope.offerObject.couponCOde,
                offerId: $scope.offerObject.offerId,
                brandId: $scope.offerObject.brandId,
                couponId: $scope.offerObject.couponId,
                listImage: $scope.offerObject.listImage != null ? $scope.offerObject.listImage.name : null,
                listImageUrl: $scope.offerObject.listImage != null ? $scope.offerObject.listImage.url : null,
                gridImage: $scope.offerObject.gridImage != null ? $scope.offerObject.gridImage.name : null,
                gridImageUrl: $scope.offerObject.gridImage != null ? $scope.offerObject.gridImage.url : null,
                actionType: null,
                status: $scope.offerObject.selectedOfferStatus.value,
                unitDetailList: $scope.offerObject.unitDetailList,
                menuCategoryId: $scope.offerObject.menuCategoryId,
                appOfferType: $scope.offerObject.appOfferType,
                /*menuCategoryImage: $scope.offerObject.menuCategoryImage != null ? $scope.offerObject.menuCategoryImage.name : null,
                menuCategoryDescription: $scope.offerObject.menuCategoryDescription*/
            };
            // }
            // else {
            //     var appOfferDetail = {
            //         appOfferId: null,
            //         offerType: $scope.offerObject.selectedOfferType,
            //         description: $scope.offerObject.description,
            //         redirectionLink: $scope.offerObject.redirectionLink,
            //         title: $scope.offerObject.title,
            //         actionCategory:$scope.offerObject.actionCategory.value,
            //         offerIndex: null,
            //         startDate: $scope.offerObject.startDate,
            //         endDate: $scope.offerObject.endDate,
            //         couponCode: $scope.offerObject.couponCOde,
            //         offerId: $scope.offerObject.offerId,
            //         couponId: $scope.offerObject.couponId,
            //         listImage: $scope.offerObject.listImage.name,
            //         listImageUrl: $scope.offerObject.listImage.url,
            //         gridImage: $scope.offerObject.gridImage.name,
            //         gridImageUrl: $scope.offerObject.gridImage.url,
            //         actionType: null,
            //         status: $scope.offerObject.selectedOfferStatus.value,
            //         unitDetailList: $scope.offerObject.unitDetailList,
            //         menuCategoryId:$scope.offerObject.menuCategoryId,
            //         menuCategoryImage:$scope.offerObject.menuCategoryImage.name
            //     };
            // }
        }


        if ($scope.offerObject.offerActionType != null) {
            appOfferDetail.actionType = $scope.offerObject.offerActionType.value;
        }
        if($scope.offerObject.actionCategory.value == 'MOVE_TO_CATEGORY' ){
            appOfferDetail.actionType='NAVIGATE_TO_CATEGORY';
        }

        if ($scope.offerObject.actionCategory.value == 'CHANGE_CART_SOURCE') {
            appOfferDetail.actionType = $scope.offerObject.offerActionType;
        }


        // if ($scope.offerObject.actionCategory.value == 'TAP_TO_COPY_CODE') {
        //     appOfferDetail.actionType = "TTC";
        // }

        if ($scope.isEditOffer == true) {
            appOfferDetail.offerIndex = $scope.offerIndex;
            appOfferDetail.appOfferId = $scope.appofferid;
            appOfferDetail.createdBy = $scope.createdBy;
            appOfferDetail.updatedBy = $scope.userDetails.userId;
            if(appOfferDetail.offerType=='CUSTOMER_BANNER'){
                appOfferDetail.appOfferApplicabilityList = $scope.offerObject.appOfferApplicabilityList;
            }
            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.updateAppOffer,
                data: appOfferDetail
            }).then(function success(response) {
                $scope.offerObject.listImage = null;
                $scope.offerObject.gridImage = null;
                alert("Offer updated");
                $scope.getAllOffers();

            }, function error(response) {
                console.log("error:" + response);
            });
        }
        else if ($scope.isAddNewOffer == true) {
            appOfferDetail.createdBy = $scope.userDetails.userId;
            appOfferDetail.updatedBy = null;
            if($scope.couponData != undefined && $scope.couponData != null){
                appOfferDetail.mov = $scope.couponData.offer.minValue;
                appOfferDetail.offerValue = $scope.couponData.offer.offerValue;
                appOfferDetail.maxDiscount = $scope.couponData.offer.maxDiscountAmount;
                appOfferDetail.offerCategory = $scope.couponData.offer.type;
                appOfferDetail.offerOrderType = $scope.couponData.offer.category;
            }

            if(appOfferDetail.offerType=='CUSTOMER_BANNER'){
                appOfferDetail.appOfferApplicabilityList = $scope.offerObject.appOfferApplicabilityList;
            }

            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.addAppOffer,
                data: appOfferDetail
            }).then(function success(response) {
                $scope.offerObject.listImage = null;
                $scope.offerObject.gridImage = null;
                alert("Offer added");
                $scope.getAllOffers();

            }, function error(response) {
                console.log("error:" + response);
            });
        }
        $scope.hideNewAppOfferDialog();
        $scope.reset();
    };

    $scope.uploadListImage = function (couponCOde) {
        console.log(angular.element(document.querySelector('listImage')));
        $scope.offerObject.couponCOde = couponCOde;
        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select an Image");
            return false;
        }
        var fileExt = getFileExtension(fileService.getFile().name);

        if (isImage(fileExt.toLowerCase())) {
            var fd = new FormData();
            fd.append("mimeType", fileExt.toUpperCase());
            fd.append("couponCode", $scope.offerObject.couponCOde);
            fd.append("imageType", "list");
            fd.append("file", fileService.getFile());
            $rootScope.showDetailLoader = true;
            var URL = AppUtil.restUrls.offerManagement.uploadImage;
            $http({
                url: URL,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showDetailLoader = false;
                $scope.offerObject.listImageToUpload = null;
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                $scope.offerObject.listImage = {url: response.url, name: response.name};
                alert("Image Added");
                $scope.listImageUploaded = true;
            }).error(function (response) {
                $rootScope.showDetailLoader = false;
                alert("Error while uploading Image");
            });
        }
        else {
            alert("Cannnot upload image");
            return false;
        }
    }

    $scope.uploadGridImage = function (couponCOde) {
        $scope.offerObject.couponCOde = couponCOde;
        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select an Image");
            return false;
        }
        var fileExt = getFileExtension(fileService.getFile().name);

        if (isImage(fileExt.toLowerCase())) {
            var fd = new FormData();
            fd.append("mimeType", fileExt.toUpperCase());
            fd.append("couponCode", $scope.offerObject.couponCOde);
            fd.append("imageType", "grid");
            fd.append("file", fileService.getFile());
            console.log(fd);
            $rootScope.showDetailLoader = true;
            var URL = AppUtil.restUrls.offerManagement.uploadImage;
            $http({
                url: URL,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showDetailLoader = false;
                $scope.offerObject.gridImageToUpload = null;
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                $scope.offerObject.gridImage = {url: response.url, name: response.name};
                alert("Image Added");
                $scope.gridImageUploaded = true;
            }).error(function (response) {
                $rootScope.showDetailLoader = false;
                alert("Error while uploading Image");
            });
        }
        else {
            alert("Cannnot upload image");
            return false;
        }
    }

    $scope.reset = function () {
        $scope.offerObject = {};
        $scope.offerObject.actionCategory = null;
        $scope.offerObject.CategoriesSelected = [];
        $scope.offerObject.menuCategoryId = null;
        $scope.offerObject.selectedOfferType = $scope.offerTypes[0];
        $scope.offerObject.selectedPartnerId = null;
        $scope.gridImageUploaded = false;
        $scope.couponalreadypresent = false;
        angular.element("input[type='file']").val(null);
        $scope.offerObject.selectedOfferStatus = $scope.OfferStatus[0];
        $scope.offerObject.offerActionType = null;
        $scope.listImageUploaded = false;
        $scope.categoryImageUploaded = false;
        fileService.push(null);
        $scope.offerObject.listImage = null;
        $scope.offerObject.gridImage = null;
       /* $scope.offerObject.menuCategoryImage = null;
        $scope.offerObject.menuCategoryDescription = null;*/
        $scope.addNewReportForm.$setPristine();
        $scope.addNewReportForm.$setUntouched();
        // $scope.offerObject.actionCategory=$scope.actionCategories[0];
        // $scope.offerObject.selectedOfferType=$scope.offerTypes[0];
        // $scope.offerObject.offerActionType=$scope.actionLists[0];
    }

    $scope.getAllOffers = function () {
        if($scope.selectedPartnerId==null){
            alert("Select Partner Name");
            return;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.offerManagement.getAppOffersByPartnerId,
            params :{
                partnerId :$scope.selectedPartnerId.partnerId
            }
        }).then(function success(response) {

            $rootScope.showFullScreenLoader = false;
            $scope.appOffers = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    $scope.setOrdering = function () {
        $scope.appOffersTypeOffer = [];
        $scope.appOffersTypePromotion = [];
        $scope.appOffersTypeAlliance = [];
        $scope.appOffersTypeWalletBanner = [];
        $scope.appOffersTypeWalletChaayosGifting = [];
        $scope.appOffers.sort(function (a, b) {
            return a.offerIndex - b.offerIndex
        })
        $scope.appOffers.map(function (offers) {
            if (offers.offerType == 'OFFER') {
                $scope.appOffersTypeOffer.push(offers)
            } else if (offers.offerType == 'PROMOTION') {
                $scope.appOffersTypePromotion.push(offers)
            } else if (offers.offerType == 'ALLIANCE') {
                $scope.appOffersTypeAlliance.push(offers)
            } else if (offers.offerType == 'WALLET_BANNER') {
                $scope.appOffersTypeWalletBanner.push(offers)
            } else if (offers.offerType == 'CHAAYOS_GIFTING') {
                $scope.appOffersTypeWalletChaayosGifting.push(offers)
            }

        })


        $('#SetOrderingModal').modal('show');
    }

    $scope.submitOrdering = function () {
        console.log("offers", $scope.appOffersTypeOffer)
        console.log("promotion", $scope.appOffersTypePromotion)

        $scope.listIndex = [];
        var promotionCount = 0;
        var offerCount = 0;
        var allianceCount = 0;
        var walletCount = 0;
        var giftingCount = 0;
        for (var i = 0; i < $scope.appOffersTypeOffer.length; i++) {
            id = $scope.appOffersTypeOffer[i].appOfferId;
            index = offerCount + 1;
            offerCount++;
            $scope.listIndex.push({id: id, index: index, userId: $scope.userDetails.userId});
        }
        for (var i = 0; i < $scope.appOffersTypePromotion.length; i++) {
            id = $scope.appOffersTypePromotion[i].appOfferId;
            index = promotionCount + 1;
            promotionCount++;
            $scope.listIndex.push({id: id, index: index, userId: $scope.userDetails.userId});
        }

        for (var i = 0; i < $scope.appOffersTypeWalletChaayosGifting.length; i++) {
            id = $scope.appOffersTypeWalletChaayosGifting[i].appOfferId;
            index = giftingCount + 1;
            giftingCount++;
            $scope.listIndex.push({id: id, index: index, userId: $scope.userDetails.userId});
        }
        for (var i = 0; i < $scope.appOffersTypeAlliance.length; i++) {
            id = $scope.appOffersTypeAlliance[i].appOfferId;
            index = allianceCount + 1;
            allianceCount++;
            $scope.listIndex.push({id: id, index: index, userId: $scope.userDetails.userId});
        }
        for (var i = 0; i < $scope.appOffersTypeWalletBanner.length; i++) {
            id = $scope.appOffersTypeWalletBanner[i].appOfferId;
            index = walletCount + 1;
            walletCount++;
            $scope.listIndex.push({id: id, index: index, userId: $scope.userDetails.userId});
        }
        console.log($scope.listIndex);

        $http({
            method: 'POST',
            url: AppUtil.restUrls.offerManagement.updateOrderingForAppOffers,
            data: $scope.listIndex
        }).then(function success(response) {
            if (response.data == true) {
                $scope.getAllOffers();
            }

        }, function error(response) {
            console.log("error:" + response);
        });

        $('#SetOrderingModal').modal('hide');
    }

    $scope.updateStatus = function (offer) {
        if (offer.status == "ACTIVE") {
            status = "IN_ACTIVE"
        } else {
            status = "ACTIVE"
        }
        var payload = {
            id: offer.appOfferId,
            status: status,
            code: $scope.userDetails.userId
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.offerManagement.updateStatusForAppOffers,
            data: payload
        }).then(function success(response) {
            if (response.data == true) {
                $scope.getAllOffers();
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.verifyCoupon = function (c) {
        if (!c) {
            alert("Enter coupon code ")
        } else {
            var coupon = c;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.couponSearch,
                data: coupon
            }).then(function success(response) {
                $scope.couponData = response.data;
                if ($scope.offerObject.brandId !== null && $scope.offerObject.brandId !== undefined && $scope.couponData.offer.brandId !== $scope.offerObject.brandId) {
                    alert("Coupon Brand ID doesn't match current Offer Brand ID");
                    $scope.isCouponVerified = false;
                    return;
                }
                if ($scope.couponData == null || $scope.couponData == "") {
                    alert("Coupon Code doesnt exist");
                    $scope.isCouponVerified = false;
                }
                else {
                    $scope.offerObject.startDate = $scope.dateformatting($scope.couponData.startDate);//new Date($scope.couponData.startDate);
                    $scope.offerObject.endDate = $scope.dateformatting($scope.couponData.endDate);//new Date($scope.couponData.endDate).getFullYear()+"-"+new Date($scope.couponData.endDate).getMonth()+"-"+new Date($scope.couponData.endDate).getDate();
                    $scope.offerObject.couponId = $scope.couponData.id;
                    $scope.offerObject.offerId = $scope.couponData.offer.id;
                    $scope.offerObject.brandId = $scope.couponData.offer.brandId;
                    $scope.offerObject.description = $scope.couponData.offer.description;
                    $scope.couponalreadypresent = true;
                    $scope.isCouponVerified = true;
                    $scope.brandIdChanged();
                }

            }, function error(response) {
                console.log("error:" + response);
            });

        }
    }

    $scope.dateformatting = function (startDate) {
        var year = new Date(startDate).getFullYear();
        var month = new Date(startDate).getMonth() + 1;
        var day = new Date(startDate).getDate();
        if (day >= 1 && day < 10)
            day = '0' + day;
        if (month >= 1 && month < 10)
            month = '0' + month;
        return year + "-" + month + "-" + day;
    }

    $scope.onEdit = function (offer) {
        console.log(offer);
        $scope.offerObject.appOfferApplicabilityList = offer.appOfferApplicabilityList;
        console.log($scope.offerObject.appOfferApplicabilityList);
        if (offer.actionCategory == 'TAP_TO_COPY_CODE')
            $scope.couponalreadypresent = true;
        else
            $scope.couponalreadypresent = false;
        $scope.actionCategories.map(function (actionType) {
            if (actionType.value == offer.actionCategory) {
                $scope.offerObject.actionCategory = actionType;
            }
        })

        $scope.brandIdNotEditable = true;
        $scope.isEditOffer = true;
        $scope.isAddNewOffer = false;
        $scope.isViewOffer = false;
        $scope.isCouponVerified = true;
        $scope.offerIndex = offer.offerIndex;
        $scope.offerTypes.forEach(function (selectedOffer) {
            if (offer.offerType === selectedOffer.value) {
                $scope.offerObject.selectedOfferType = selectedOffer;
            }
        });

        $scope.partnerId.forEach(function(partnerName)
        {
            if(partnerName.partnerId==offer.partnerId){
                $scope.offerObject.selectedPartnerId=partnerName;
            }
        });
        console.log($scope.offerObject.selectedPartnerId);
        $scope.appofferid = offer.appOfferId;
        $scope.createdBy = offer.createdBy;
        $scope.offerObject.couponCOde = offer.couponCode;
        $scope.offerObject.startDate = $scope.dateformatting(offer.startDate);
        $scope.offerObject.endDate = $scope.dateformatting(offer.endDate);
        if (offer.status == "ACTIVE") {
            $scope.offerObject.selectedOfferStatus = $scope.OfferStatus[0];
        } else {
            $scope.offerObject.selectedOfferStatus = $scope.OfferStatus[1];
        }
        $scope.actionLists.map(function (actionType) {
            if (offer.actionType == actionType.value) {
                $scope.offerObject.offerActionType = actionType;
            }
        });
        $scope.offerObject.description = offer.description;
        $scope.offerObject.redirectionLink = offer.redirectionLink;
        $scope.offerObject.menuCategoryId = offer.menuCategoryId;
        $scope.offerObject.title = offer.title;
        $scope.offerObject.couponId = offer.couponId;
        $scope.offerObject.offerId = offer.offerId;
        $scope.offerObject.brandId = offer.brandId;
        $scope.offerObject.appOfferType = offer.appOfferType;
        $scope.offerObject.appofferApplicabilityFlags = offer.appofferApplicabilityFlags;
        if (offer.listImage == null) {
            $scope.listImageUploaded = false;
        } else {
            $scope.listImageUploaded = true;
            $scope.offerObject.listImage = {
                url: offer.listImageUrl,
                name: offer.listImage
            }
        }

        if (offer.gridImage == null) {
            $scope.gridImageUploaded = false;
        } else {
            $scope.gridImageUploaded = true;
            $scope.offerObject.gridImage = {
                url: offer.gridImageUrl,
                name: offer.gridImage
            }
        }
       /* $scope.offerObject.menuCategoryDescription = offer.menuCategoryDescription;
        if (offer.menuCategoryImage == null) {
            $scope.categoryImageUploaded = false;
        } else {
            $scope.categoryImageUploaded = true;
            $scope.offerObject.menuCategoryImage = {
                url: null,
                name: offer.menuCategoryImage
            }
        }*/
        $scope.brandIdChanged();
        $scope.setUnitList(offer.unitDetailList);
        $("#AddNewOfferAppModalData").modal("show");
    }

    $scope.onView = function (offer) {
        $scope.actionCategories.map(function (actionType) {
            if (actionType.value == offer.actionCategory) {
                $scope.offerObject.actionCategory = actionType;
            }
        })
        $scope.brandIdNotEditable = true;
        $scope.offerObject.appOfferApplicabilityList = offer.appOfferApplicabilityList;
        console.log($scope.offerObject.appOfferApplicabilityList);
        $scope.isEditOffer = false;
        $scope.isAddNewOffer = false;
        $scope.isViewOffer = true;
        $scope.offerTypes.forEach(function (selectedOffer) {
            if (offer.offerType === selectedOffer.value) {
                console.log(selectedOffer.offerType)
                $scope.offerObject.selectedOfferType = selectedOffer;
            }
        })
        $scope.appofferid = offer.appOfferId;
        $scope.createdBy = offer.createdBy;
        $scope.offerObject.couponCOde = offer.couponCode;
        $scope.offerObject.startDate = $scope.dateformatting(offer.startDate);
        $scope.offerObject.endDate = $scope.dateformatting(offer.endDate);
        if (offer.status == "ACTIVE") {
            $scope.offerObject.selectedOfferStatus = $scope.OfferStatus[0];
        } else {
            $scope.offerObject.selectedOfferStatus = $scope.OfferStatus[1];
        }
        $scope.actionLists.map(function (actionType) {
            if (offer.actionType == actionType.value) {
                $scope.offerObject.offerActionType = actionType;
            }

        });
        $scope.offerObject.description = offer.description;
        $scope.offerObject.redirectionLink = offer.redirectionLink;
        $scope.offerObject.menuCategoryId = offer.menuCategoryId;
        $scope.offerObject.title = offer.title;
        $scope.offerObject.couponId = offer.couponId;
        $scope.offerObject.offerId = offer.offerId;
        $scope.offerObject.brandId = offer.brandId;
        $scope.offerObject.appOfferType = offer.appOfferType;

        $scope.offerObject.appOfferApplicabilityDataListFlags = offer.appOfferApplicabilityDataListFlags;
        if (offer.listImage == null) {
            $scope.listImageUploaded = false;
            $scope.listImage = [];
        } else {

            $scope.listImageUploaded = true;
            $scope.offerObject.listImage = {
                url: offer.listImageUrl,
                name: offer.listImage
            }
        }

        if (offer.gridImage == null) {
            $scope.gridImageUploaded = false;
        } else {

            $scope.gridImageUploaded = true;
            $scope.offerObject.gridImage = {
                url: offer.gridImageUrl,
                name: offer.gridImage
            }
        }
        $("#AddNewOfferAppModalData").modal("show");
        $scope.brandIdChanged();
        $scope.setUnitList(offer.unitDetailList);

    }

    $scope.setUnitList = function (list) {
        for (var i = 0; i < list.length; i++) {
            for (var j = 0; j < $scope.Categories.length; j++) {
                if ($scope.Categories[j].unitId == list[i].unitId) {
                    $scope.offerObject.CategoriesSelected.push($scope.Categories[j])
                }
            }
        }

    }

//handling  modal closed  when clicked outside
//     $('.modal').on('hidden.bs.modal', function () {
//         console.log("calling reset");
//         $scope.reset();
//     });


//css property to make modal scrolling after opening two modal one on one
    $('.modal').css('overflow-y', 'auto');


    $scope.sort_by = function (predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };

    $scope.onEditCoupon = function () {
        console.log("edit coupon called");
        $scope.isCouponVerified = false;
    };

    $scope.onSearchByChange = function () {
        $scope.searchFields = {};
    };

    $scope.brandIdChanged = function () {
        $scope.getFilteredUnitList($scope.offerObject.brandId);
        $scope.createCategories();
    }

    $scope.getFilteredUnitList = function (brandId) {
        $scope.filteredUnitList = $scope.unitList.filter(unit => {
            return unit.companyId === AppUtil.COMPANY_IDS[AppUtil.BRAND_IDS[brandId]];
        });
    }

    // $scope.uploadCategoryImage = function () {
    //     console.log(angular.element(document.querySelector('listImage')));
    //     if (fileService.getFile() == null
    //         || fileService.getFile() == undefined) {
    //         bootbox.alert("Please select an Image");
    //         return false;
    //     }
    //     var fileExt = getFileExtension(fileService.getFile().name);
    //
    //     if (isImage(fileExt.toLowerCase())) {
    //         var fd = new FormData();
    //         fd.append("mimeType", fileExt.toUpperCase());
    //         fd.append("couponCode", $scope.offerObject.couponCOde);
    //         fd.append("imageType", "list");
    //         fd.append("file", fileService.getFile());
    //         $rootScope.showDetailLoader = true;
    //         var URL = AppUtil.restUrls.offerManagement.uploadImage;
    //         $http({
    //             url: URL,
    //             method: 'POST',
    //             data: fd,
    //             headers: {'Content-Type': undefined},
    //             transformRequest: angular.identity
    //         }).success(function (response) {
    //             $rootScope.showDetailLoader = false;
    //             // $scope.offerObject.categoryImage = null;
    //             angular.element("input[type='file']").val(null);
    //             fileService.push(null);
    //             $scope.offerObject.menuCategoryImage = {url: response.url, name: response.name};
    //             alert("Image Added");
    //             $scope.categoryImageUploaded = true;
    //         }).error(function (response) {
    //             $rootScope.showDetailLoader = false;
    //             alert("Error while uploading Image");
    //         });
    //     }
    //     else {
    //         alert("Cannot upload image");
    //         return false;
    //     }
    // }

    //
    // $scope.clearImage = function (image) {
    //     if (image == 'CATEGORY_IMAGE') {
    //         $scope.categoryImageUploaded = false;
    //         $scope.offerObject.menuCategoryImage = null;
    //     }
    // }


});