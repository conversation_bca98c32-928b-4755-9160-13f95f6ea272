/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("preAuthenticatedApiMgtCtrl", function ($scope, $window, $http, $location, AppUtil) {

    $scope.init = function(){
        $scope.getApis();
        $scope.noResultMsg = false;
        $scope.api = null;
        $scope.status = "ACTIVE";
    }

    $scope.getApis = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.accessControlManagement.preAuthenticatedApis
        }).then(function success(response) {
            $scope.apiList = response.data;
            if($scope.apiList.length==0){
                $scope.noResultMsg = true;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.openAddNewApiModal = function () {
        $("#addApiModal").modal("show");
    }

    $scope.addApi = function () {
        if($scope.api==null || $scope.api.trim().length==0){
            alert("Please fill API name properly!");
            return false;
        }else{
            $http({
                method: 'POST',
                url: AppUtil.restUrls.accessControlManagement.preAuthenticatedApi,
                data: {id:null,api:$scope.api,status:$scope.status}
            }).then(function success(response) {
                console.log(response)
                if(response.status==200){
                    alert(response.data);
                    if(response.data=="API Added successfully"){
                        $scope.getApis();
                    }
                }else{
                    alert("Something went wrong. Try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
            $("#addApiModal").modal("hide");
        }
    }

    $scope.deactivate = function (id) {
        if(window.confirm("Are you sure?")){
            $http({
                method: 'PUT',
                url: AppUtil.restUrls.accessControlManagement.deactivatePreAuthenticatedApi,
                data: id
            }).then(function success(response) {
                if(response.status==200 && response.data==true){
                    alert("API deactivated successfully");
                    $scope.getApis();
                }else{
                    alert("Something went wrong. Try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

    $scope.activate = function (id) {
        if(window.confirm("Are you sure?")){
            $http({
                method: 'PUT',
                url: AppUtil.restUrls.accessControlManagement.activatePreAuthenticatedApi,
                data: id
            }).then(function success(response) {
                if(response.status==200 && response.data==true){
                    alert("API activated successfully");
                    $scope.getApis();
                }else{
                    alert("Something went wrong. Try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

}); 