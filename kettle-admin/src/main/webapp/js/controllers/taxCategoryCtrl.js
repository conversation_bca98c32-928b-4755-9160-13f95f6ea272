/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("taxCategoryCtrl", function($rootScope, $scope, $location, $http, AppUtil, $cookieStore) {

    $scope.init = function() {
	$scope.categoryGridOptions = $scope.createGridOptions();
	$scope.getAllTaxCategories();
	$scope.hideCategoryGrid = true;
    };

    $scope.getAllTaxCategories = function() {
	$http({
	    method : "GET",
	    dataType : 'json',
	    data : '',
	    headers : {
		"Content-Type" : "application/json"
	    },
	    url : AppUtil.restUrls.taxManagement.getAllTaxCategories,
	}).then(function success(response) {
	    $scope.categoryGridOptions.data = response.data;
	    $scope.hideCategoryGrid = false;
	}, function error(response) {
	    console.log("error:" + response);
	});
    }

    $scope.createGridOptions = function() {
	return {
	    enableFiltering : true,
	    enableColumnResizing : true,
	    cellEditableCondition : function($scope) {
		return false;
	    },
	    paginationPageSizes : [ 100, 200, 500 ],
	    paginationPageSize : 100,
	    columnDefs : [ {
		field : 'code',
		displayName : 'HSN Code',
		enableCellEdit : false
	    }, {
		field : 'desc',
		displayName : 'Description',
		enableCellEdit : false
	    }, {
		field : 'intDesc',
		displayName : 'Internal Description',
		enableCellEdit : false
	    }, {
		field : 'status',
		displayName : 'Status',
		enableCellEdit : false
	    }, {
		field : 'exempted',
		displayName : 'Exempted',
		enableCellEdit : false,
		cellTemplate: '<div class="ui-grid-cell-contents" >{{row.entity.exempted == true? "Yes" : "No"}}</div>'
	    }, {
		field : 'action',
		displayName : 'Action',
		cellTemplate : 'statusChangeButton.html',
		enableCellEdit : false
	    } ]
	};
    }

    $scope.changeStatus = function(value) {

	bootbox.confirm({
	    message : "Are you sure?",
	    buttons : {
		confirm : {
		    label : 'Yes',
		},
		cancel : {
		    label : 'No',
		}
	    },
	    callback : function(result) {
		if (!result) {
		    return;
		}
		var oldStatus = value.status;
		var status = value.status == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
		value.status = status;
		$http({
		    url : AppUtil.restUrls.taxManagement.changeTaxCategoyStatus,
		    method : 'POST',
		    data : value
		}).then(function(response) {
			    		    if (response != null && response.data != undefined && response.data != null
						    && response.data.errorType != undefined
						    && response.data.errorType != null) {
			    			bootbox.alert(response.data.errorMessage);
			    			value.status = oldStatus;
					    }else  if (response.data == 'true') {
						$scope.updateCategoryGridRow(value);
					    }
		}, function(response) {
		    console.log("error", response);
		    bootbox.alert('Failed To update the status with reason : '+response.data.errorMessage);
		});
	    }
	});
    };

    $scope.editData = function(value) {
	alert("edit data");
    };

    $scope.openAddTaxCategoryModal = function() {
	$("#addTaxCategoryModal").modal("show");
    }

    $scope.addTaxCategory = function () {
        if (AppUtil.isValid($scope.code) && AppUtil.isValid($scope.description)
            && AppUtil.isValid($scope.internalDescription) && AppUtil.isValid($scope.exempted)) {
        } else {
            return;
        }

        var payload = {
            code: $scope.code,
            desc: $scope.description,
            intDesc: $scope.internalDescription,
            exempted: $scope.exempted,
        };
        $http({
            url: AppUtil.restUrls.taxManagement.addTaxCategory,
            method: 'POST',
            data: payload
        }).then(function (response) {
            if (response != null) {
                $scope.categoryGridOptions.data.push(response.data)
                $("#addTaxCategoryModal").modal("hide");
                $scope.code = null;
                $scope.description = null;
                $scope.internalDescription = null;
                $scope.exempted = null;
            }
        }, function (response) {
            console.log("error", response);
        });

    };

    $scope.updateCategoryGridRow = function(value) {
	var x = null;
	var id = null;
	for (x in $scope.categoryGridOptions.data) {
	    if ($scope.categoryGridOptions.data[x].keyId == value.keyId) {
		id = x;
	    }
	}
	if (id != null) {
	    $scope.categoryGridOptions.data[id] = value;
	}
    }
});