/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("CategoryListController", function ($scope, $window, $http, $location, AppUtil,$rootScope) {
	$scope.showFullScreenLoader = true;
	$scope.removeSequencing = false;
	$rootScope.fileUploaded = [];
	getCategoryAttributeData();
	$http({
		method: 'GET',
		url: AppUtil.restUrls.unitMetaData.listTypes
	}).then(function success(response) {
		$scope.categoryLists = response.data;
		$scope.showFullScreenLoader = false;
		//console.log($scope.categoryLists);
		$scope.selectedCatList = $scope.categoryLists[0];
	}, function error(response) {
		$scope.showFullScreenLoader = false;
		console.log("error:" + response);
	});
	$scope.showDetailsData = function (id) {
		$scope.selectedCatList.forEach(function (det) {
			if (det.detail.name === id) {
				$scope.selectedCatList.det = det;
				$scope.detLists = $scope.selectedCatList.det;
			}
		});
	} 	//  end method showDetailsData
	$scope.addCategoryDetails = function (detail) {
		$scope.detailId = detail.id;
		$scope.msg = "Add"
		$scope.action = "AddCatDet"
		$scope.catName = null;
		$scope.catCode = null;
		$scope.catShortCode = null;
		$("#categoryModal").modal("show");
	}
	$scope.submitAddCategory = function () {

		if ($scope.catName == null || $scope.catName == "") {
			alert("Please Enter Category Name.");
			return;
		}
		if ($scope.catCode == null || $scope.catCode == "") {
			alert("Please Enter Category Code.");
			return;
		}
		if ($scope.catShortCode == null || $scope.catShortCode == "") {
			alert("Please Enter Category Short Code.");
			return;
		}
		var catObj = {
			content: [{
				name: $scope.catName,
				code: $scope.catCode,
				shortCode: $scope.catShortCode,
				status: "IN_ACTIVE",
			}], detail: $scope.detLists.detail
		};

		$scope.detLists.content.push({
			name: $scope.catName,
			code: $scope.catCode,
			shortCode: $scope.catShortCode,
			status: "IN_ACTIVE",
		});

		$http({
			method: 'POST',
			url: AppUtil.restUrls.posMetaData.refLookUpUpsert,
			data: catObj
		}).then(function success(response) {
			//console.log(response.data);
			$scope.showFullScreenLoader = false;
			//$scope.detLists.content.pop(catObj);
			if (response.status != 200) {
				$scope.detLists.content.pop(catObj);
			}
			else {
				alert("succesfully added");
				window.location.reload();

				///window.reload(true);
				//$('#categoryModal').modal('hide');
			}

			//console.log(response);
		}, function error(response) {
			$scope.showFullScreenLoader = false;
			console.log("error:" + response);
		});
	}
	$scope.updateCategoryDetails = function (contentID) {
		$scope.msg = "Update"
		$scope.action = "UpdateCatAction"
		$("#categoryModal").modal("show");

		$scope.detLists.content.forEach(function (updatedet) {
			if (updatedet.id === contentID) {
				$scope.catName = updatedet.name;
				$scope.catCode = updatedet.code;
				$scope.catShortCode = updatedet.shortCode;
				$scope.updateIDS = updatedet.id;
			}
		});
	}
	$scope.submitUpdateCategory = function (upDetID) {

		if ($scope.catName == null || $scope.catName == "") {
			alert("Please Enter Category Name.");
			return;
		}
		if ($scope.catCode == null || $scope.catCode == "") {
			alert("Please Enter Category Code.");
			return;
		}
		if ($scope.catShortCode == null || $scope.catShortCode == "") {
			alert("Please Enter Category Short Code.");
			return;
		}
		var updateCatObj = {
			name: $scope.catName,
			code: $scope.catCode,
			shortCode: $scope.catShortCode,
		};
		$scope.detLists.content.forEach(function (updatedet) {
			if (updatedet.id === upDetID) {
				updatedet.name = $scope.catName;
				updatedet.code = $scope.catCode;
				updatedet.shortCode = $scope.catShortCode;
			}
		});
		$http({
			method: 'POST',
			url: AppUtil.restUrls.posMetaData.refLookUpUpsert,
			//url: 'http://172.16.16.52:8080/kettle-service/rest/v1/pos-metadata/refLookUp/upsert',
			data: $scope.detLists
		}).then(function success(response) {
			if (response.status != 200) {
				$scope.detLists.content.pop(updateCatObj);
			}
			else {
				$scope.refreshCategoryListCache()
				$('#categoryModal').modal('hide');
			}

		}, function error(response) {
			console.log("error:" + response);
		});
	}

	$scope.refreshCategoryListCache = function () {
		$http({
			method: 'POST',
			url: AppUtil.restUrls.orderManagement.refreshCategoryListCache,
			//url: 'http://172.16.16.52:8080/kettle-service/rest/v1/pos-metadata/refLookUp/upsert',
			data: {}
		}).then(function success(response) {

		}, function error(response) {
			console.log("error:" + response);
		});
	}

	$scope.activeNinactive = function (stausID, catStatus) {

		if ($window.confirm("Are you sure, you want to Active/DeActive Category?")) {
			console.log("YES");;
		} else {
			return false;
		}

		//console.log("contentBefore",stausID+"catStatus="+catStatus);

		var updateCatStatusObj = {
			status: catStatus,
		};
		//console.log("contentBefore",$scope.detLists.content);
		$scope.detLists.content.forEach(function (statusUpdate) {
			if (statusUpdate.id === stausID) {
				statusUpdate.status = catStatus;
			}
		});
		//console.log("contentAfter",$scope.detLists.content);

		$http({
			method: 'POST',
			dataType: 'json',
			data: $scope.detLists,
			headers: { 'Content-Type': 'application/json' },
			url: AppUtil.restUrls.posMetaData.refLookUpUpsert,
			//url: 'http://172.16.16.52:8080/kettle-service/rest/v1/pos-metadata/refLookUp/upsert',

		}).then(function success(response) {
			if (response.status != 200) {
				alert("Category Status not updated");
			} else {
				$scope.refreshCategoryListCache()
			}
		}, function error(response) {
			console.log("error:" + response);
		});
	}

	function getCategoryAttributeData (){
		$http({
			method: 'GET',
			url: AppUtil.restUrls.masterMetaData.getCategoryAttributes
		}).then(function success(response) {
			$scope.categoryAttributeData = response.data;
			console.log($scope.categoryAttributeData);
		}, function error(response) {
			$scope.showFullScreenLoader = false;
			console.log("error:" + response);
		});
	}

	$scope.openAddPropertyModal = function (data) {
		if($scope.selectedCatList == undefined || $scope.selectedCatList == null){
			alert("Please select the category first !!!!");
		}
		console.log(data);
		$scope.lastSequenceNumber = 1;
		$scope.catSequence = {};
		$scope.categorySequenceData =[];
		$scope.removeSequencing = false;
		$scope.fileUploaded = {};
		var lastSequenceNumber =  0;
		var seq = [];
		$scope.detLists.content.forEach(function(content){
			content.defaultVisibility = false;
		});
		var filterSequenceNonNull = [];
		var mappedRtlId = '';
		$scope.categoryAttributeData.forEach(function (attributeData) {
			if (attributeData.sequenceNumber != null) {
				var sequence = {
					id: attributeData.rlId,
					name: attributeData.rlName,
					number: attributeData.sequenceNumber,
					tagImageFile : null
				}
				seq.push(sequence);
				data.content.forEach(function(type){
					if(type.id == attributeData.rlId){
						mappedRtlId = data.detail.id;
					}
				});
				if(lastSequenceNumber < attributeData.sequenceNumber){
					lastSequenceNumber = attributeData.sequenceNumber;
				}
				filterSequenceNonNull.push(attributeData.rlId);
			}
			if(attributeData.tagImageUrl != undefined && attributeData.tagImageUrl != null){
				$scope.fileUploaded[attributeData.rlId] = attributeData.tagImageUrl;
			}
			$scope.detLists.content.forEach(function (content) {
				if (attributeData.rlId == content.id) {
					content.defaultVisibility = attributeData.defaultVisibility == 'Y' ? true : false;
					if(filterSequenceNonNull.includes(attributeData.rlId)){
						content.clicked = true;
					}
				}
			});
		});
		$scope.lastSequenceNumber = lastSequenceNumber;
		if (mappedRtlId == data.detail.id) {
			$scope.catSequence[data.detail.id] = seq;
			$scope.catSequence[data.detail.id].sort(function (a, b) {
				return a.number - b.number;
			});
		}
		$scope.categorySequenceData = $scope.catSequence[data.detail.id];
		$('#categoryPropertiesModal').modal('show');
	}

	$scope.addPropertySequence = function (data,rtlId,flag){
		data.clicked = flag;
		var sequence = {
			id : data.id,
			name:data.name,
			number:$scope.lastSequenceNumber + 1
		}
		if ($scope.catSequence[rtlId] != undefined && $scope.catSequence[rtlId] != null) {
			var seq = $scope.catSequence[rtlId];
			seq.push(sequence);
			$scope.catSequence[rtlId] = seq
		}
		else {
			var val = [];
			val.push(sequence);
			$scope.catSequence[rtlId] = val;
		}
		$scope.categorySequenceData = $scope.catSequence[rtlId];
		$scope.lastSequenceNumber = $scope.lastSequenceNumber + 1;
		console.log($scope.catSequence);
		console.log($scope.categorySequenceData);
	}

	$scope.clearSequenceData = function (detailList){
		detailList.forEach(function(listData){
			listData.clicked = false;
		});
		$scope.lastSequenceNumber = 1;
		$scope.catSequence = {};
		$scope.categorySequenceData =[];
	}

	$scope.removeCategorySequencing = function(detailList){
		$scope.removeSequencing = true;
		$scope.clearSequenceData(detailList);
	}

	$scope.selectVisibility = function (detDatas,visibilityStatus){
		console.log(visibilityStatus);
		$scope.detLists.content.forEach(function(content){
			if(content.id == detDatas.id){
				content.defaultVisibility = visibilityStatus;
			}
		})
	}

	$scope.removeCategoryFromSequence = function(sequenceData,rtlId){
		$scope.catSequence[rtlId].forEach(function(seqData, index) {
			var list = $scope.catSequence[rtlId];
			if (seqData.id === sequenceData.id) {
				// list.splice(index, 1);		
				seqData.number = null;
				for (let i = index; i < $scope.catSequence[rtlId].length; i++) {
					list[i].number--;
				}
			}
		});
		$scope.detLists.content.forEach(function(content){
			if(content.id == sequenceData.id){
				content.clicked = false;
			}
		})
	}

	$scope.updateCategoryProperties = function(){
	    var requestData = {};
		var req = {
			rlId : null,
			rlName : null,
			defaultVisibility : 'N',
			isSequencingActive : 'N',
			sequenceNumber : null
		}

		var filterDefaultVisibilityTrue = [];
		var filterDefaultVisibilityFalse = [];
		var contentMap = {};
		$scope.detLists.content.forEach(function(content){
			if(content.defaultVisibility != undefined && content.defaultVisibility != null & content.defaultVisibility){
				filterDefaultVisibilityTrue.push(content.id);
			}
			else{
				filterDefaultVisibilityFalse.push(content.id);
			}
			contentMap[content.id] = content.name;
		});

		$scope.categorySequenceData.map(function(sequence,key){
			var domain = angular.copy(req);
			domain.rlId = sequence.id;
			domain.rlName = contentMap[sequence.id];
			domain.defaultVisibility = (filterDefaultVisibilityTrue.includes(sequence.id)) ? 'Y' : 'N';
			domain.isSequencingActive = 'Y';
			domain.sequenceNumber = sequence.number;
			requestData[sequence.id] = domain;
			var index = filterDefaultVisibilityTrue.indexOf(sequence.id);
			var indexFalse = filterDefaultVisibilityFalse.indexOf(sequence.id);
			if(index >= 0){
				filterDefaultVisibilityTrue.splice(index,1);
			}
			if(indexFalse >= 0){
				filterDefaultVisibilityFalse.splice(indexFalse,1);
			}
		});
		console.log(filterDefaultVisibilityTrue);

		if (filterDefaultVisibilityTrue.length > 0) {
			filterDefaultVisibilityTrue.forEach(function (prop) {
				var domain = angular.copy(req);
				domain.rlId = prop;
				domain.rlName = contentMap[prop];
				domain.defaultVisibility = 'Y';
				domain.isSequencingActive ='N';
				domain.sequenceNumber = null;
				requestData[prop] = domain;
			});
		}

		if(filterDefaultVisibilityFalse.length > 0){
			filterDefaultVisibilityFalse.forEach(function (prop) {
				var domain = angular.copy(req);
				domain.rlId = prop;
				domain.rlName = contentMap[prop];
				domain.defaultVisibility = 'N';
				domain.isSequencingActive ='N';
				domain.sequenceNumber = null;
				requestData[prop] = domain;
			});
		}

		console.log(requestData);

		$scope.showFullScreenLoader = true;
		$http({
			method: 'POST',
			data: requestData,
			url: AppUtil.restUrls.masterMetaData.updateCategoryAttributes + "?removeSequencing=" + $scope.removeSequencing,
		}).then(function success(response) {
			if (response.status == 200 && response.data == true) {
				alert("Category Attributes updated successfully !!!!");
			} else {
				alert('Category Attributes cannot be updated !!!!');
			}
			$scope.showFullScreenLoader = false;
			getCategoryAttributeData();
			$('#categoryPropertiesModal').modal('hide');
		}, function error(response) {
			console.log("error:" + response);
		});
	}

	$scope.getSelectedStatus = function(id) {
		if($scope.selected[id] != undefined && $scope.selected[id] != null){
			return $scope.selected[id];
		}
		return false;
	}

	$scope.showImageModal = function(id){
		if($scope.fileUploaded[id] == undefined || $scope.fileUploaded[id] == null){
			alert("No Image is Uploaded !!!!!");
			return;
		}
		else{
			window.open($scope.fileUploaded[id], '_blank');
			return;
		}
	}

	$scope.openUploadImageModal = function(data){
		$rootScope.fileUploaded = {};
		if($scope.selectedCatList == undefined || $scope.selectedCatList == null){
			alert("Please select the category first !!!!");
		}
		$('#uploadImageModal').modal('show');
	}

	$scope.uploadImages =  function(){
		var req = {
			rlId : null,
			rlName : null,
			defaultVisibility : 'N',
			sequenceNumber : null,
			tagImageFile : null
		}
		console.log($rootScope.fileUploaded);
		var reqData = {};
		$scope.detLists.content.forEach(function(content){
			if($rootScope.fileUploaded[content.id] != undefined && $rootScope.fileUploaded[content.id] != null){
				var domain = angular.copy(req);
				domain.rlId = content.id;
				domain.rlName = content.name;
				domain.tagImageFile = $rootScope.fileUploaded[content.id];
				reqData[content.id] = domain;
			}
		});
		console.log(reqData);
		$http({
			method: 'POST',
			data: reqData,
			url: AppUtil.restUrls.masterMetaData.uploadCategoryImage,
		}).then(function success(response) {
			if (response.status == 200 && response.data == true) {
				alert("Category Attributes updated successfully !!!!");
			} else {
				alert('Category Attributes cannot be updated !!!!');
			}
			$scope.showFullScreenLoader = false;
			getCategoryAttributeData();
		}, function error(response) {
			console.log("error:" + response);
		});
	}
	

}); 


adminapp.directive('fileModel', ['$parse', '$rootScope', function ($parse, $rootScope) {
    return {
        restrict: 'A',
        scope: {
            fileModel: '=',
			detData: '='
		},
		link: function (scope, element, attrs) {
			element.bind('change', function () {
				scope.$apply(function () {
					scope.loading = true;
					var file = element[0].files[0];
					var fileSizeInMB = file.size / (1024 * 1024);

					var maxSizeInMB = 1;
					if (fileSizeInMB > maxSizeInMB) {
						console.error('File size exceeds the maximum limit of ' + maxSizeInMB + ' MB');
						scope.loading = false;
						return; 
					}

					var reader = new FileReader();

					reader.onload = function (e) {
						scope.$apply(function () {
							$rootScope.fileUploaded[scope.detData] = e.target.result;
							scope.loading = false; 
						});
					};

					reader.onerror = function (error) {
						scope.$apply(function () {
							console.error('Error reading file:', error);
							scope.loading = false;
						});
					};

					reader.readAsDataURL(file);
				});
            });
        }
    };
}]);