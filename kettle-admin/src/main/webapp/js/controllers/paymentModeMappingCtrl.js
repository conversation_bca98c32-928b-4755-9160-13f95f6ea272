adminapp.controller("PaymentModeMappingCtrl", ['$scope', '$window', '$http', 'AppUtil', '$rootScope',
                                function ($scope, $window, $http, AppUtil, $rootScope) {
    $scope.init = function () {
        $scope.modeName = null;
        $scope.modeType = null;
        $scope.modeDescription = null;
        $scope.settlementType = null;
        $scope.generatePull = null;
        $scope.commissionRate = null;
        $scope.modeStatus = null;
        $scope.modeCategory = null;
        $scope.editable = null;
        $scope.applicableOnDiscountedOrders = null;
        $scope.automaticPullValidate = null;
        $scope.automaticTransfer = null;
        $scope.automaticCloseTransfer = null;
        $scope.needsSettlementSlip = null;
        $scope.validationSource = null;
        $scope.ledgerName = null;
        $scope.otherModeType = null;
        $scope.textRegEx = /^[a-zA-Z ]+$/;

        $scope.modeTypeList = ["CARD", "CASH", "AMEX Card", "COUPON", "CREDIT", "PREPA<PERSON>", "GYFTR", "OTHERS"]
        $scope.settlementTypeList = ["CREDIT", "DEBIT"]
        $scope.generatePullList = ["YES", "NO"]
        $scope.modeStatusList = ["ACTIVE", "INACTIVE"]
        $scope.modeCategoryList = ["OFFLINE", "ONLINE"]
        $scope.editableList = ["YES", "NO"]
        $scope.applicableOnDiscountedOrdersList = ["YES", "NO"]
        $scope.automaticPullValidateList = ["YES", "NO"]
        $scope.automaticTransferList = ["YES", "NO"]
        $scope.automaticCloseTransferList = ["YES", "NO"]
        $scope.needsSettlementSlipList = ["YES", "NO"]
    }

    function matchRegex(str, pattern) {
        var reg = new RegExp(pattern);
        if(reg.test(str)){
            return true;
        } else {
            return false;
        }
    }

    $scope.savePaymentMethod = function () {
        if ($scope.modeName == null || $scope.modeName.trim().length === 0) {
            bootbox.alert("Invalid Mode Name")
            return;
        }
        if ($scope.modeType == null || $scope.modeType.trim().length === 0) {
            bootbox.alert("Invalid Mode Type")
            return;
        }
        if ($scope.modeDescription == null || $scope.modeDescription.trim().length === 0) {
            bootbox.alert("Invalid Mode Description")
            return;
        }
        if ($scope.settlementType == null || $scope.settlementType.trim().length === 0) {
            bootbox.alert("Invalid Settlement Type")
            return;
        }
        if ($scope.generatePull == null) {
            bootbox.alert("Invalid Generate Pull")
            return;
        }
        if ($scope.commissionRate == null) {
            bootbox.alert("Invalid Commission Rate")
            return;
        }
        if ($scope.modeStatus == null || $scope.modeStatus.trim().length === 0) {
            bootbox.alert("Invalid Mode Status")
            return;
        }
        if ($scope.modeCategory == null || $scope.modeCategory.trim().length === 0) {
            bootbox.alert("Invalid Mode Category")
            return;
        }
        if ($scope.editable == null || $scope.editable.trim().length === 0) {
            bootbox.alert("Invalid editable data")
            return;
        }
        if ($scope.applicableOnDiscountedOrders == null || $scope.applicableOnDiscountedOrders.trim().length === 0) {
            bootbox.alert("Invalid Applicable On Discounted Orders data")
            return;
        }
        if ($scope.automaticPullValidate == null || $scope.automaticPullValidate.trim().length === 0) {
            bootbox.alert("Invalid Automatic Pull Validate Data")
            return;
        }
        if ($scope.automaticTransfer == null || $scope.automaticTransfer.trim().length === 0) {
            bootbox.alert("Invalid Automatic Transfer Data")
            return;
        }
        if ($scope.automaticCloseTransfer == null || $scope.automaticCloseTransfer.trim().length === 0) {
            bootbox.alert("Invalid Automatic Close Transfer Data")
            return;
        }
        if ($scope.needsSettlementSlip == null || $scope.needsSettlementSlip.trim().length === 0) {
            bootbox.alert("Invalid Needs Settlement Slip Data")
            return;
        }
        if ($scope.validationSource == null || $scope.validationSource.trim().length === 0 || !matchRegex($scope.validationSource, /^[a-zA-Z ]+$/)) {
            bootbox.alert("Invalid Validation Source")
            return;
        }
        if ($scope.ledgerName == null || $scope.ledgerName.trim().length === 0 ) {
            bootbox.alert("Invalid Ledger Name")
            return;
        }
        if($scope.modeType === "OTHERS"){
            if($scope.otherModeType == null || $scope.otherModeType.trim().length ===0){
                bootbox.alert("Invalid Specific Mode Type")
                return;
            }
        }
        if ($scope.generatePull === "YES") {
            $scope.generatePull = 1;
        } else {
            $scope.generatePull = 0;
        }
        if ($scope.modeStatus=== "INACTIVE") {
            $scope.modeStatus = "IN_ACTIVE";
        } else {
            $scope.modeStatus = "ACTIVE";
        }
        if ($scope.editable === "YES") {
            $scope.editable = "Y";
        } else {
            $scope.editable = "N";
        }

        if ($scope.applicableOnDiscountedOrders === "YES") {
            $scope.applicableOnDiscountedOrders = "Y";
        } else {
            $scope.applicableOnDiscountedOrders = "N";
        }

        if ($scope.automaticPullValidate === "YES") {
            $scope.automaticPullValidate = "Y";
        } else {
            $scope.automaticPullValidate = "N";
        }
        if ($scope.automaticTransfer === "YES") {
            $scope.automaticTransfer = "Y";
        } else {
            $scope.automaticTransfer = "N";
        }
        if ($scope.automaticCloseTransfer === "YES") {
            $scope.automaticCloseTransfer = "Y";
        } else {
            $scope.automaticCloseTransfer = "N";
        }

        if ($scope.needsSettlementSlip === "YES") {
            $scope.needsSettlementSlip = "Y";
        } else {
            $scope.needsSettlementSlip = "N";
        }
        if($scope.modeType === "OTHERS"){
            $scope.modeType = $scope.otherModeType;
        }

        var data = {
            "modeName": $scope.modeName,
            "modeType": $scope.modeType,
            "modeDescription": $scope.modeDescription,
            "settlementType": $scope.settlementType,
            "generatePull": $scope.generatePull,
            "commissionRate": $scope.commissionRate,
            "modeStatus": $scope.modeStatus,
            "modeCategory": $scope.modeCategory,
            "editable": $scope.editable,
            "applicableOnDiscountedOrders": $scope.applicableOnDiscountedOrders,
            "automaticPullValidate": $scope.automaticPullValidate,
            "automaticTransfer": $scope.automaticTransfer,
            "automaticCloseTransfer": $scope.automaticCloseTransfer,
            "needsSettlementSlip": $scope.needsSettlementSlip,
            "validationSource": $scope.validationSource,
            "ledgerName": $scope.ledgerName,
            //"otherModeType":$scope.otherModeType
        }
        console.log("saving data ::::", data);
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.paymentManagement.addPaymentMapping,
            data: data
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status === 200 && response.data) {
                alert("Payment Mode Method added successfully");
                window.location.reload();
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
            alert("Unable to add Payment Mode Method");
        });
    }

}])
