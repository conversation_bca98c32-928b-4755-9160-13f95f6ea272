/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("monkConfCtrl",function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {
	var monkCount = 0; 
	$scope.MONKCONSTANT = "MONK";
	var params= $location.search();
	$scope.selectedUnitId = params.selectedUnit;

	if($scope.selectedUnitId  == "" || $scope.selectedUnitId == null){
		$location.path("dashboard/unit/CAFE");
	}

	$scope.init = function () {
		$scope.viewMode = false;
		getMonkConfMetadata();
		$scope.unitMonkConf = {};
		/**
		 * id : confValue
		 */
		$scope.monkConfValue={};
		$scope.monkArr = [];
	};

	$scope.getMonkArr = function() {
		return $scope.monkArr;   
	};

	$scope.toggleViewMode = function(){
		$scope.viewMode = !$scope.viewMode;
		if($scope.viewMode){
			getUnitMonkConfData();
		}
	};

	$scope.updateMonkConfValue = function(type, value){
		if(value == undefined){
			value= 0;
		}else if(value == 6){
			alert("cannot add more than 6 monk details");
			return;
		}
		if(type == "NUMBER_OF_MONKS"){
			if(monkCount == 0){
				monkCount = value;
				for(var monkNumber = 1;monkNumber <= value; monkNumber++){
					$scope.monkArr.push(monkNumber);
					initMonkConfValueObject(monkNumber);
				}
			}else{
				if(value > monkCount){
					for(var monkNumber = monkCount;monkNumber <= value; monkNumber++){
						$scope.monkArr.push(monkNumber);
						initMonkConfValueObject(monkNumber);
					}
				}else if(value < monkCount){
					for(var monkNumber = value;monkNumber <= monkCount; monkNumber++){
						for(var j = $scope.monkArr.length - 1; j >= 0; j--) {
							if($scope.monkArr[j] === monkNumber) {
								$scope.monkArr.splice(j, 1);
							}
						}
						delete $scope.monkConfValue[monkNumber];
					}
				}
				monkCount = value;
			}
		}
	};

	$scope.changeMonkConfValue = function(type, id ,action){
		if($scope.monkConfValue[id].value == undefined){
			$scope.monkConfValue[id].value = 0;
		}else if($scope.monkConfValue[id].value == 6 && action == 'plus'){
			alert("cannot add more than 6 monk details");
			return;
		}
		if(action == 'plus'){
			$scope.monkConfValue[id].value = $scope.monkConfValue[id].value + 1;
			$scope.monkArr.push($scope.monkConfValue[id].value);
			initMonkConfValueObject($scope.monkConfValue[id].value);
		}else if(action == 'minus'){
			if($scope.monkConfValue[id].value == 0){
				return;
			}
			$scope.monkConfValue[id].value = $scope.monkConfValue[id].value - 1;
			$scope.monkArr.splice($scope.monkArr.length-1, 1);
			delete $scope.monkConfValue[$scope.MONKCONSTANT+monkCount];
		}
		monkCount = $scope.monkConfValue[id].value;
	};

	function initMonkConfValueObject (monkNumber){
		$scope.monkConfValue[$scope.MONKCONSTANT+monkNumber] = {};
		for(var i = 0;i < $scope.monkConfMetadata.length; i++){
			if($scope.monkConfMetadata[i].status == 'ACTIVE'  && $scope.monkConfMetadata[i].scope == 'MONK'){
				var key = $scope.MONKCONSTANT+monkNumber;
				$scope.monkConfValue[key][$scope.monkConfMetadata[i].attrId] = {};
				fillMonkConfValueObject($scope.monkConfValue[key][$scope.monkConfMetadata[i].attrId], $scope.monkConfMetadata[i], key);
			}
		}
	}

	$scope.saveUnitMonkConfiguration = function(){
		if($scope.monkArr.length ==0){
			alert("Please add atleast one monk details");
			return;
		}
		$scope.unitMonkConf = unitMonkConfObject();
		var arr = [];
		for(var key in $scope.monkConfValue){
			if(key.indexOf($scope.MONKCONSTANT) > -1){
				for(var inKey in $scope.monkConfValue[key]){
					arr.push($scope.monkConfValue[key][inKey]);
				}
			}else{
				arr.push($scope.monkConfValue[key]);
			}
		}

		$scope.unitMonkConf.configurationList = arr;
		//	console.log("$scope.unitMonkConf", $scope.unitMonkConf);

		$http({
			url : AppUtil.restUrls.unitMetaData.saveMonkConfData,
			method : "POST",
			data : $scope.unitMonkConf
		}).success(function(data) {
			if(data.errorType == undefined){
				alert("Configuration saved successfully");
				$scope.goBack();
			}else{
				alert("Configuration saving failed.Please try again.");
			}
		});
	};

	$scope.goBack = function(){
		$location.path("dashboard/unit/CAFE");
	};
	
	$scope.$on('$destroy', function iVeBeenDismissed() {
		$location.search({});
	});

	function getMonkConfMetadata () {
		$http({
			method: 'GET',
			url: AppUtil.restUrls.unitMetaData.monkConfMetadata
		}).then(function success(response) {
			//console.log("response",response.data);
			if (response.data !== null) {
				$scope.monkConfMetadata = response.data;
				for(var i = 0;i < $scope.monkConfMetadata.length; i++){
					if($scope.monkConfMetadata[i].status == 'ACTIVE'  && $scope.monkConfMetadata[i].scope == 'UNIT'){
						$scope.monkConfValue[$scope.monkConfMetadata[i].attrId] = {};
						fillMonkConfValueObject($scope.monkConfValue[$scope.monkConfMetadata[i].attrId], $scope.monkConfMetadata[i], "ALL");
					}
				}
			} 
		}, function error(response) {
			console.log("error:" + response);
		});
	}

	function getUnitMonkConfData () {
		$http({
			method: 'GET',
			url: AppUtil.restUrls.unitMetaData.unitConfData,
			params : { unitId : $scope.selectedUnitId}
		}).then(function success(response) {
		//	console.log("response",response.data);
			if (response.data !== null) {
				$scope.unitConfData = response.data;
			} 
		}, function error(response) {
			console.log("error:" + response);
		});
	}

	function fillMonkConfValueObject (confObject,metadataObj, scope){
		confObject.scope = scope;
		confObject.type = metadataObj.attr;
	}

	function monkConfValueObject (){
		var confValue = {
				scope : '',
				type : '',
				value : ''
		};
	}

	function unitMonkConfObject (){
		var unitMonkConf = {
				unitId : $scope.selectedUnitId,
				status : 'ACTIVE',
				updatedBy : getUpdatedBy(),
				configurationList : []
		};
		return unitMonkConf;
	}	

	function getUpdatedBy(){

    var data=AppUtil.getUserValues();
		var idCodeName = {
				id : '',
				name : ''
		};
		if (data != undefined && data != null) {
			idCodeName.id = data.user.id;
			idCodeName.name = data.user.name;
		}
		return idCodeName;
	}
});
