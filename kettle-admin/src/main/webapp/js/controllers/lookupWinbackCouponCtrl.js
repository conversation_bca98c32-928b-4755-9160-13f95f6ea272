adminapp.controller("lookupWinbackCouponCtrl",['$scope', 'AppUtil', '$http', '$stateParams', 'fileService', '$cookieStore', '$rootScope',
    function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

        $scope.init = function(){
            $scope.resultList = [];
            $scope.filteredItems = "" //Initially for no filter
            $scope.totalItems = "";
            $scope.currentPage = 1; //current page
            $scope.entryLimit = 50;
            $scope.startDate = "";
            $scope.endDate = "";
            $scope.getWinbackCouponList();
        }

        $scope.getWinbackCouponList = function(){
            $http({
               method: 'GET',
               url: AppUtil.restUrls.winback.getWinbackCouponList
            }).then(function success(response) {
                $scope.resultList = response.data;
                $scope.filteredItems = $scope.resultList.length; //Initially for no filter
                $scope.totalItems = $scope.resultList.length;
                console.log(response)
                $scope.currentPage = 1; //current page
                $scope.entryLimit = 50;
            }, function error(response) {
               bootbox.alert("Error while fetching Winback Coupons")
               console.log("error: " + response);
            });
        }

        $scope.sort_by = function(predicate) {
            $scope.predicate = predicate;
            $scope.reverse = !$scope.reverse;
        };

        $scope.downloadSheetBetweenDates = function(){
             console.log($scope.startDate);
             console.log($scope.endDate);
             $rootScope.showFullScreenLoader = true;
             $http({
                       method: 'GET',
                       url: AppUtil.restUrls.winback.downloadSheetBetweenDates +"/?startDate="+$scope.startDate+
                                                                                   "&endDate="+$scope.endDate,
                       responseType: 'arraybuffer',
                       headers: {
                           'Content-type': 'application/json',
                           'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                       }
                   })
                   .then(
                       function success(response) {
                           $rootScope.showFullScreenLoader = false;
                           $("#downloadSheet").modal("hide");
                           if (response != undefined && response != null) {
                               var fileName = "WINBACK_COUPON_DATA" + " - " + Date.now() + ".xlsx";
                               var blob = new Blob(
                                   [response.data],
                                   {
                                       type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                   }, fileName);
                               saveAs(blob, fileName);
                           }
                       },
                       function error(response) {
                           $rootScope.showFullScreenLoader = false;
                           console.log("error:" + response);
                           alert("Unable to download Sheet");
                       });
        }


                $scope.downloadSheet = function(){
                     $rootScope.showFullScreenLoader = true;
                     $http({
                               method: 'GET',
                               url: AppUtil.restUrls.winback.downloadSheet,
                               responseType: 'arraybuffer',
                               headers: {
                                   'Content-type': 'application/json',
                                   'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                               }
                           })
                           .then(
                               function success(response) {
                                   $rootScope.showFullScreenLoader = false;
                                   $("#downloadSheet").modal("hide");
                                   if (response != undefined && response != null) {
                                       var fileName = "WINBACK_COUPON_DATA" + " - " + Date.now() + ".xlsx";
                                       var blob = new Blob(
                                           [response.data],
                                           {
                                               type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                           }, fileName);
                                       saveAs(blob, fileName);
                                   }
                               },
                               function error(response) {
                                   $rootScope.showFullScreenLoader = false;
                                   console.log("error:" + response);
                                   alert("Unable to download Sheet");
                               });
                }


        $scope.markNotify = function(id){
            $rootScope.showFullScreenLoader = true;
            $http({
               method: 'POST',
               url: AppUtil.restUrls.winback.notify,
               data : id
            }).then(function success(response) {
               if(response.data==null || response.data==""){
                   alert("Error while Notify Customer !")
                   $rootScope.showFullScreenLoader = false;
               }else{
               alert("Customer Notified !");
               $scope.init();
               $rootScope.showFullScreenLoader = false;
               }
            }, function error(response) {
               window.alert("Error while Notify Customer !")
               console.log("error: " + response);
               $rootScope.showFullScreenLoader = false;
            });
        }

        $scope.openDownloadModal = function(){
            $("#downloadSheet").modal("show");
        }

    }])