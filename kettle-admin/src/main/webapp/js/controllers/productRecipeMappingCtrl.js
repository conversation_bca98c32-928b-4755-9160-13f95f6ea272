
adminapp.controller("productRecipeMappingCtrl",
    function (
        $log, AuthService,
        $cookieStore, $rootScope,
        $scope, $http,
        $location, AppUtil, $stateParams,
        AdminApiService, toastService, $q,
        $timeout, fileService
    ) {
        $scope.init = function () {
            $rootScope.enableScreenFilter = true;

            $scope.isProductRecipeMapping = $stateParams.isProductRecipeMapping;

            // default values and select values
            $scope.SelectedUnitType = "CAFE";
            $scope.selectedBrand = "1";
            $scope.selectedCategories = [];
            $scope.selectedProducts = [];
            $scope.selectedDimensions = [];
            $scope.selectedRegions = [];
            $scope.selectedCities = [];
            $scope.selectedUnits = [];
            $scope.showInactiveProducts = false;

            // Filter values 
            $scope.unitType = ["CAFE", "COD", "EMPLOYEE_MEAL"];
            $scope.dimensionInfo = {};
            $scope.productsByBrand = [];
            $scope.brandTypeAndProductInfo = new Map();
            $scope.allProductsInfoByCategory = new Map();
            $scope.allCategories = [];
            $scope.allCategoriesInfo = [];
            $scope.regions = [];
            $scope.citiesByRegion = [];

            // table content
            $scope.resultProductListDetails = [];
            $scope.selectedRecordsCount = 0;

            // table filter parameters...
            $scope.filter = {
                selectAll: false,
                unitName: null,
                productName: null,
                dimensionName: null,
                profile: null,
                updatedStatus: null,
                updatedDOP: null,
                updatedDIC:null,
                aliasSearch: null,
                DDSearch: null,

                profileFilter: false,

                statusFilter: false,

                dopFilter: false,

                aliasFilter: false,

                DDFilter: false,

                allFilteredProducts: [],
                allFilteredDimensions: [],
                selectedProducts: [],
                selectedDimensions: []
            }

            //Excel related fields
            $scope.bulkFile = null;
            $scope.showExportModal = false;

            // initially load these data
            getAllProducts();
            getAllBrands();
            getCitiesBasedOnRegion();
            $scope.defaultValues = getDefaultStatusValues();

            // pagination values
            $scope.itemsPerPage = 30;
            $scope.currentPage = 0;
            $scope.totalPages = 0;

            // result after submittion
            $scope.successMsgs = [];
            $scope.failureMsgs = [];
            $scope.defaultMsgs = [];
            $scope.showMsgsModal = false;

        }

        function getDefaultStatusValues() {
            return [
                { id: 1, name: "ACTIVE", value: "ACTIVE", flagName: "YES", flagValue: true },
                { id: 2, name: "IN_ACTIVE", value: "IN_ACTIVE", flagName: "NO", flagValue: false }
            ];
        }

        function getAllBrands() {
            AdminApiService.get(
                AppUtil.restUrls.brandMetaData.getAllBrandsShort,
                {},
                { byPassErrorHandling: true, showLoader: false }
            ).then(function (data) {
                $scope.brandList = data;
                if (AppUtil.isEmptyObject(data)) {
                    toastService.warning("Brand Details not found !");
                    return;
                }
            }).catch(function (error) {
                console.log("Error while getting brands : ", error);
                toastService.error("Error while getting brands");
            })
        };

        function getAllUnitsByBrand() {
            $scope.selectedUnits = [];
            $scope.unitsByBrand = [];
            var url = AppUtil.restUrls.productMetaData.getUnitsShortByBrandId + '/' + $scope.selectedBrand;
            var unitType = $scope.SelectedUnitType;
            AdminApiService.get(url, unitType).then(function (unitsData) {
                if (unitsData == null || AppUtil.isEmptyObject(unitsData) || AppUtil.isEmptyObject(unitsData.data)) {
                    toastService.warning("Units details not found!");
                    return;
                }
                $scope.unitsByBrand = unitsData.data;
            })
        };

        function getAllProducts() {
            var url = AppUtil.restUrls.productMetaData.productsByBrand;
            AdminApiService.get(url).then(function (productData) {
                if (productData == null || AppUtil.isEmptyObject(productData)) {
                    toastService.warning("Product details not found!");
                    return;
                }
                $scope.getAllDimensionsAndCategory(productData);
            })
        };

        $scope.getAllDimensionsAndCategory = function (productData) {
            var refTypes = ["CATEGORY", "DIMENSION"];
            var url = AppUtil.restUrls.unitMetaData.listTypesByTypes + "?refTypes=" + refTypes.join("&refTypes=");
            AdminApiService.get(url).then(function (data) {
                $scope.brandTypeAndProductInfo = productData;
                $scope.allProductsInfoByCategory = Object.entries(productData).reduce((acc, [brand, categories]) => {
                    Object.entries(categories).forEach(([category, products]) => {
                        acc[category] = (acc[category] || []).concat(products);
                    });
                    return acc;
                }, {});
                $scope.allCategories = Object.keys($scope.allProductsInfoByCategory);
                $scope.productsInfo = Object.values($scope.allProductsInfoByCategory).flat();

                $scope.dimensionInfo = data.DIMENSION;
                $scope.findCategory(data);
            });
        };

        $scope.findCategory = function (data) {
            var categoryList = data.CATEGORY;
            if (categoryList == null || AppUtil.isEmptyObject(categoryList)) {
                toastService.warning("unable to find Categories!");
                return;
            }
            var categoryMap = categoryList.reduce((map, category) => {
                map[category.detail.id] = category.detail.name;
                return map;
            }, {});
            $scope.allCategories = $scope.allCategories.map(function (categoryId) {
                return {
                    id: categoryId,
                    name: categoryMap[categoryId] || "Unknown Category"
                };
            });
            $scope.allCategoriesInfo = $scope.allCategories;
            $scope.changeCategoryOnBrand();
        };

        function getCitiesBasedOnRegion() {
            if ($scope.citiesByRegion != null && $scope.citiesByRegion.length > 0) {
                return;
            }
            AdminApiService.get(
                AppUtil.restUrls.unitMetaData.locationByZone,
                {}, { showLoader: false }
            ).then(function (data) {
                $scope.citiesByRegion = data;
                $scope.regions = Object.keys(data);
                $scope.getAllCitiesForSelectedRegions();
            })
        };

        $scope.getAllCitiesForSelectedRegions = function () {
            $scope.filteredCities = [];

            for (var i = 0; i < $scope.selectedRegions.length; i++) {
                $scope.filteredCities = $scope.filteredCities.concat($scope.citiesByRegion[$scope.selectedRegions[i]]);
            }

            if ($scope.selectedRegions.length == 0) {
                if ($scope.citiesByRegion) {
                    Object.values($scope.citiesByRegion).forEach(list => {
                        $scope.filteredCities.push(...list);
                    });
                }
            }

            $scope.selectedCities = $scope.selectedCities.filter(function (item) {
                return $scope.filteredCities.includes(item);
            });
        };


        $scope.$watch('selectedCategories', function () {
            $scope.changeProductOnCategory();
        }, true);

        $scope.$watch('selectedProducts', function () {
            $scope.changeDimensionOnProduct();
            sortSelectedProducts();
        }, true);

        $scope.$watch('selectedRegions', function () {
            $scope.getAllCitiesForSelectedRegions();
        }, true);

        $scope.$watch('selectedUnits', function () {
            sortSelectedUnits();
        }, true);

        $scope.changeCategoryOnBrand = function () {
            $scope.selectedCategories = [];
            $scope.allCategories = [];
            if ($scope.selectedBrand != null) {
                var brandCategories = $scope.brandTypeAndProductInfo[$scope.selectedBrand] || {};
                $scope.allCategories = Object.keys(brandCategories);
            } else {
                $scope.allCategories = Object.keys($scope.allProductsInfoByCategory);
            }

            $scope.allCategories = $scope.allCategories.map(function (categoryId) {
                var category = $scope.allCategoriesInfo.find(cat => cat.id === categoryId);
                if (categoryId == null || category == null) {
                    return;
                }
                return {
                    id: categoryId,
                    name: category.name
                };
            });

            $scope.selectedCategories = $scope.selectedCategories.filter(function (cat) {
                return $scope.allCategories.includes(cat);
            });

            // change child(product also)...
            $scope.changeProductOnCategory();
            getAllUnitsByBrand();
        };

        $scope.changeProductOnCategory = function () {
            $scope.productsInfo = [];
            if (AppUtil.isEmptyObject($scope.selectedBrand) && AppUtil.isEmptyObject($scope.selectedCategories)) {
                // brand and cat is null;
                $scope.productsInfo = Object.values($scope.allProductsInfoByCategory).flat();
            } else if (!AppUtil.isEmptyObject($scope.selectedBrand) && AppUtil.isEmptyObject($scope.selectedCategories)) {
                getProductsInfoForCategories(1);
            } else if (AppUtil.isEmptyObject($scope.selectedBrand)) {
                getProductsInfoForCategories(2)
            } else if (AppUtil.isEmptyObject($scope.selectedCategories)) {
                var productsByCategorys = $scope.brandTypeAndProductInfo[$scope.selectedBrand];
                $scope.productsInfo = Object.values(productsByCategorys).flat();
            } else {
                getProductsInfoForCategories(3);
            }

            if (!$scope.showInactiveProducts) {
                $scope.productsInfo = $scope.productsInfo.filter(function (product) {
                    return product.status !== 'IN_ACTIVE';
                });
            }

            $scope.selectedProducts = $scope.selectedProducts.filter(function (product) {
                return $scope.productsInfo.includes(product);
            });

            $scope.changeDimensionOnProduct();
        };

        function getProductsInfoForCategories(check) {
            if (check == 1) {
                // brand not null but cat is null
                var catAndProductsMap = $scope.brandTypeAndProductInfo[$scope.selectedBrand];
                if (catAndProductsMap) {
                    Object.values(catAndProductsMap).forEach(list => {
                        $scope.productsInfo.push(...list);
                    });
                }
                return;
            }
            if (check == 2) {
                // brand is null but cat is not null
                for (var i = 0; i < $scope.selectedCategories.length; i++) {
                    var list = $scope.allProductsInfoByCategory[$scope.selectedCategories[i].id];
                    $scope.productsInfo.push(...list);
                }
            }
            if (check == 3) {
                // brand and category is not null
                for (var i = 0; i < $scope.selectedCategories.length; i++) {
                    var productsByCategorys = $scope.brandTypeAndProductInfo[$scope.selectedBrand];
                    var category = $scope.selectedCategories[i];
                    var list = productsByCategorys[category.id];
                    $scope.productsInfo.push(...list);
                }
                return;
            }
        }

        $scope.changeDimensionOnProduct = function () {
            $scope.productDimensions = [];
            var selectedProductDimensionIds = [];
            for (var i = 0; i < $scope.selectedProducts.length; i++) {
                selectedProductDimensionIds.push(+$scope.selectedProducts[i].code);
            }

            for (var i = 0; i < $scope.dimensionInfo.length; i++) {
                if (selectedProductDimensionIds.indexOf($scope.dimensionInfo[i].detail.id) >= 0) {
                    for (var j = 0; j < $scope.dimensionInfo[i].content.length; j++) {
                        $scope.productDimensions.push($scope.dimensionInfo[i].content[j]);
                    }
                }
            }

            $scope.selectedDimensions = $scope.selectedDimensions.filter(function (dimesnsion) {
                return $scope.productDimensions.includes(dimesnsion);
            });

        };

        $scope.multiSelectSettingsForProduct = {
            enableSearch: true,
            template: '<b ng-style="{\'color\': option.status === \'IN_ACTIVE\' ? \'red\' : \'green\'}"> {{option.name}} [{{option.id}}]</b>',
            scrollableHeight: '600px',
            scrollableWidth: '200px'
        };

        $scope.multiSelectSettingsForProductInTableFilter = {
            enableSearch: true,
            template: '<b ng-style="{\'color\': option.status === \'IN_ACTIVE\' ? \'red\' : \'green\'}"> {{option.name}} [{{option.id}}]</b>',
            scrollableHeight: '600px',
            scrollableWidth: '200px'
        };

        function sortSelectedProducts() {
            $scope.productsInfo.sort((a, b) => {
                let isSelectedA = $scope.selectedProducts.some(sel => sel.id === a.id);
                let isSelectedB = $scope.selectedProducts.some(sel => sel.id === b.id);

                return isSelectedB - isSelectedA;
            });
        };

        function sortSelectedUnits() {
            $scope.unitsByBrand.sort((a, b) => {
                let isSelectedA = $scope.selectedUnits.some(sel => sel.id === a.id);
                let isSelectedB = $scope.selectedUnits.some(sel => sel.id === b.id);

                return isSelectedB - isSelectedA;
            });
        };

        function sortSelectedProductsInTableFilter() {
            $scope.filter.allFilteredProducts.sort((a, b) => {
                let isSelectedA = $scope.filter.selectedProducts.some(sel => sel.id === a.id);
                let isSelectedB = $scope.filter.selectedProducts.some(sel => sel.id === b.id);

                return isSelectedB - isSelectedA;
            });
        };

        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };

        $scope.multiSelectSettingsForCities = {
            showEnableSearchButton: true, template: '<b> {{option.value}}</b>', scrollable: true,
            scrollableHeight: '200px'
        }

        $scope.multiSelectSettingsForCatDim = {
            showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
            scrollableHeight: '200px'
        }

        $scope.multiSelectSettingsForUnit = {
            showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
            scrollableHeight: '200px', showCheckAll: false
        }

        $scope.getDataForSelectedFilters = function (isGet) {
            getRecipeProfilesOfProducts(isGet);
        }

        function getRecipeProfilesOfProducts(isGet) {
            var request = {
                productIds: $scope.selectedProducts.map(product => product.id),
                dimensionIds: $scope.selectedDimensions.map(dimension => dimension.id)
            }
            if (AppUtil.isEmptyObject(request.dimensionIds)) {
                request.dimensionIds = $scope.productDimensions.map(dimension => dimension.id);
            }
            AdminApiService.post(
                AppUtil.restUrls.recipeManagement.recipeProfileOfProductsDimensions,
                request
            ).then(function (data) {
                if (AppUtil.isEmptyObject(data)) {
                    toastService.warning("Recipe price profiles was not found for the filtered products and dimensions");
                    return false;
                }
                $scope.prod_dimensionRecipeProfiles = data;
                $scope.recipeProfiles = [...new Set(Object.values(data).flat())];
                getUnitProductRecipeMappings(isGet);
                return true;
            })
        };

        function getUnitProductRecipeMappings(isGet) {

            var requestBody = {};
            requestBody.dimensionIds = $scope.selectedDimensions.map(dimension => dimension.id);
            requestBody.isDimensionsSelected = true;
            if (AppUtil.isEmptyObject(requestBody.dimensionIds)) {
                requestBody.isDimensionsSelected = false;
                requestBody.dimensionIds = $scope.productDimensions.map(dimension => dimension.id);
            }
            if (AppUtil.isEmptyObject(requestBody.dimensionIds)) {
                toastService.warning("Dimensions are not available for the selected products , please reverify the filters");
                return;
            }

            requestBody.unitType = $scope.SelectedUnitType;
            requestBody.brandId = $scope.selectedBrand;
            requestBody.categoryIds = $scope.selectedCategories.map(cat => cat.id);
            requestBody.productIds = $scope.selectedProducts.map(product => product.id);
            requestBody.regions = $scope.selectedRegions;
            requestBody.cityIds = $scope.selectedCities.map(city => city.key);
            requestBody.unitIds = $scope.selectedUnits.map(unit => unit.id);

            var url = isGet ? AppUtil.restUrls.productMetaData.unitProductsMappings : AppUtil.restUrls.productMetaData.unitProductsMappingsToEmail;

            AdminApiService.post(
                url,
                requestBody
            ).then(function (resultData) {
                if( !isGet ) {
                    toastService.create(resultData.message);
                    return;
                }
                if (!AppUtil.isEmptyObject(resultData.errorMsg)) {
                    toastService.warning(resultData.errorMsg);
                } else if (AppUtil.isEmptyObject(resultData.message)) {
                    toastService.warning("Unit product mapping not found for selected filters");
                    return;
                } else {
                    toastService.create(resultData.message);
                }
                tableContentManipulation(resultData.data);
            });
        };

        function tableContentManipulation(data) {
            $scope.resultProductListDetails = [];
            for (var i = 0; i < data.length; i++) {
                data[i].price.currentProfile = data[i].price.profile;
                data[i].price.currentStatus = data[i].price.status;
                data[i].price.isDeliveryOnlyProduct = (data[i].price.isDeliveryOnlyProduct != null && data[i].price.isDeliveryOnlyProduct) ? "YES" : "NO";
                data[i].price.isDeliveryOnlyProductOldValue = data[i].price.isDeliveryOnlyProduct;
                data[i].price.pickDineInConsumables = (data[i].price.pickDineInConsumables == null || data[i].price.pickDineInConsumables) ? "YES" : "NO";
                data[i].price.pickDineInConsumablesOldValue = data[i].price.pickDineInConsumables;
                data[i].price.currentDimensionDescriptor = data[i].price.dimensionDescriptor;
                data[i].price.currentAliasProductName = data[i].price.aliasProductName;

                // profiles based on product and dimension
                var key = data[i].product.id + "_" + data[i].dimension.id;
                data[i].recipeProfiles = $scope.prod_dimensionRecipeProfiles[key];

                // default setup
                data[i].selected = false;
                data[i].visible = true;
                $scope.resultProductListDetails.push(data[i]);
            }
            $scope.filteredRecordsCount = data.length;
            $scope.currentPage = 0;
            applyPaginationForUpdateList();
            $scope.selectedRecordsCount = 0;

            // clear filters if previously applied...
            $scope.filter = {
                selectAll: false,
                unitName: null,
                productName: null,
                dimensionName: null,
                profile: null,
                updatedStatus: null,
                updatedDOP: null,
                updatedDIC:null,
                aliasSearch: null,
                DDSearch: null,
                
                profileFilter: false,

                statusFilter: false,

                dopFilter: false,

                aliasFilter: false,

                DDFilter: false,

                allFilteredProducts: $scope.selectedProducts,
                allDimensionsRequired: [],
                allFilteredDimensions: [],
                selectedProducts: [],
                selectedDimensions: []
            };
            if (AppUtil.isEmptyObject($scope.selectedDimensions)) {
                $scope.filter.allDimensionsRequired = $scope.productDimensions;
            } else {
                $scope.filter.allDimensionsRequired = $scope.selectedDimensions;
            }
            $scope.filter.allFilteredDimensions = $scope.filter.allDimensionsRequired;
        }

        $scope.showApplyBtnForProfile = function () {
            if ($scope.filter.allFilteredProducts.length == 1 && $scope.filter.allFilteredDimensions.length == 1) {
                changeRecipeProfilesForProductAndDimension($scope.filter.allFilteredProducts[0], $scope.filter.allFilteredDimensions[0]);
                return true;
            }
            if ($scope.filter.selectedProducts.length == 1 && $scope.filter.selectedDimensions.length == 1) {
                changeRecipeProfilesForProductAndDimension($scope.filter.selectedProducts[0], $scope.filter.selectedDimensions[0]);
                return true;
            }
            if ($scope.filter.selectedProducts.length == 1 && $scope.filter.allFilteredDimensions.length == 1) {
                changeRecipeProfilesForProductAndDimension($scope.filter.selectedProducts[0], $scope.filter.allFilteredDimensions[0]);
                return true;
            }
            if ($scope.filter.allFilteredProducts.length == 1 && $scope.filter.selectedDimensions.length == 1) {
                changeRecipeProfilesForProductAndDimension($scope.filter.allFilteredProducts[0], $scope.filter.selectedDimensions[0]);
                return true;
            }
            $scope.recipeProfiles = [...new Set(Object.values($scope.prod_dimensionRecipeProfiles).flat())];
            return false;
        }

        function changeRecipeProfilesForProductAndDimension(product, dimension) {
            var key = product.id + "_" + dimension.id;
            $scope.recipeProfiles = $scope.prod_dimensionRecipeProfiles[key];
        }

        $scope.showInfoForProfile = function () {
            toastService.warning("Select 1 Product and 1 Dimension at a time to apply profile in bulk", 5000);
        }

        //pagination after getting data
        function applyPaginationForUpdateList() {
            $scope.productsInThisPage = [];
            var startIndex;
            var limit = 0;

            if ($scope.filteredRecordsCount != 0) {
                $scope.totalPages = Math.ceil(($scope.filteredRecordsCount) / ($scope.itemsPerPage));
            }

            if ($scope.currentPage === 0) {
                startIndex = 0;
                $scope.pageLastItemIdx = -1;
            } else if ($scope.direction === 'prev') {
                startIndex = Math.max(0, $scope.pageLastItemIdx - (2 * $scope.itemsPerPage));
            } else if ($scope.direction === 'next') {
                startIndex = $scope.pageLastItemIdx + 1;
            } else {
                startIndex = $scope.currentPage * $scope.itemsPerPage;
                $scope.pageLastItemIdx = startIndex - 1;
            }

            for (var i = startIndex; i < $scope.resultProductListDetails.length; i++) {
                if ($scope.resultProductListDetails[i].visible) {
                    $scope.productsInThisPage.push($scope.resultProductListDetails[i]);
                    limit++;
                }
                if (limit >= $scope.itemsPerPage) {
                    $scope.pageLastItemIdx = i;
                    break;
                }
            }
        };

        $scope.prevPage = function () {
            $scope.direction = 'prev';
            $scope.currentPage--;
            applyPaginationForUpdateList();
            $scope.direction = null;
        };

        $scope.goToPage = function (pageNumber) {
            $scope.direction = null;
            $scope.currentPage = pageNumber;
            applyPaginationForUpdateList();
        };

        $scope.nextPage = function () {
            $scope.direction = 'next';
            $scope.currentPage++;
            applyPaginationForUpdateList();
            $scope.direction = null;
        };

        // select all
        $scope.onChangeSelectAll = function () {
            for (var i = 0; i < $scope.resultProductListDetails.length; i++) {
                var item = $scope.resultProductListDetails[i];
                if (AppUtil.isEmptyObject(item.price.profile)) {
                    continue;
                }
                if (item.visible) {
                    item.selected = $scope.filter.selectAll;
                }
            }
            $scope.selectedRecordsCount = $scope.resultProductListDetails.filter(item => item.selected).length;
        };

        $scope.onClickSelect = function (detail) {
            if (AppUtil.isEmptyObject(detail.price.profile)) {
                toastService.warning("Please Map recipe profile first to select");
                detail.selected = false;
                return;
            }
            if (detail.selected) {
                $scope.selectedRecordsCount += 1;
            } else {
                $scope.selectedRecordsCount -= 1;
            }
        };

        $scope.onChangeMarkSelect = function (mapping) {
            if (AppUtil.isEmptyObject(mapping.price.profile)) {
                toastService.warning("Please Map profile first to select");
                mapping.price.profile = mapping.price.currentProfile;
                mapping.price.status = mapping.price.currentStatus;
                mapping.price.isDeliveryOnlyProduct = mapping.price.isDeliveryOnlyProductOldValue;
                mapping.price.pickDineInConsumables = mapping.price.pickDineInConsumablesOldValue;
                mapping.price.aliasProductName = mapping.price.currentAliasProductName;
                mapping.price.dimensionDescriptor = mapping.price.currentDimensionDescriptor;
                mapping.selected = false;
                return;
            }
            if ($scope.checkIsAnyFieldChanged(mapping)) {
                if (!mapping.selected) {
                    mapping.selected = true;
                    $scope.selectedRecordsCount += 1;
                }
            } else {
                if (mapping.selected) {
                    mapping.selected = false;
                    $scope.selectedRecordsCount -= 1;
                }
            }
        }

        $scope.$watch('filter.selectedProducts', function () {
            changeDimensionsOnProducts();
            $scope.applyFilterOnAll();
            sortSelectedProductsInTableFilter();
        }, true);

        function changeDimensionsOnProducts() {

            if (AppUtil.isEmptyObject($scope.filter.selectedProducts)) {
                $scope.filter.allFilteredDimensions = $scope.filter.allDimensionsRequired;
                return;
            }

            $scope.filter.allFilteredDimensions = [];
            var selectedProductDimensionIds = [];
            for (var i = 0; i < $scope.filter.selectedProducts.length; i++) {
                selectedProductDimensionIds.push(+$scope.filter.selectedProducts[i].code);
            }

            for (var i = 0; i < $scope.dimensionInfo.length; i++) {
                if (selectedProductDimensionIds.indexOf($scope.dimensionInfo[i].detail.id) >= 0) {
                    for (var j = 0; j < $scope.dimensionInfo[i].content.length; j++) {
                        $scope.filter.allFilteredDimensions.push($scope.dimensionInfo[i].content[j]);
                    }
                }
            }

            $scope.filter.selectedDimensions = $scope.filter.selectedDimensions.filter(function (dimesnsion) {
                return $scope.filter.allFilteredDimensions.includes(dimesnsion);
            });
        }

        $scope.clearFilters = function () {
            $scope.filter = {
                unitName: null,
                profile: null,
                updatedStatus: null,
                updatedDOP: null,
                updatedDIC:null,
                aliasSearch: null,
                DDSearch: null,

                profileFilter: false,

                statusFilter: false,

                dopFilter: false,

                aliasFilter: false,

                DDFilter: false,

                allFilteredProducts: $scope.filter.allFilteredProducts,
                allDimensionsRequired: $scope.filter.allDimensionsRequired,
                allFilteredDimensions: $scope.filter.allFilteredDimensions,
                selectedProducts: [],
                selectedDimensions: []
            };
            $scope.applyFilterOnAll();
        }

        $scope.filterApplied = function () {
            var filter = $scope.filter;
            if (filter.selectAll || (filter.unitName != null && filter.unitName.length != 0)
                || filter.selectedProducts.length != 0 || filter.selectedDimensions.length != 0
                || filter.profile != null || filter.updatedStatus != null || filter.updatedDOP != null
                || filter.updatedDIC != null
                || (filter.aliasSearch != null && filter.aliasSearch.length != 0)
                || (filter.DDSearch != null && filter.DDSearch.length != 0)) {
                return true;
            }
            return false;
        }

        $scope.$watch('filter.selectedDimensions', function () {
            $scope.applyFilterOnAll();
        }, true);

        $scope.toggleSliderChecked = function() {
            $scope.applyFilterOnAll();
        }

        $scope.applyFilterOnAll = function () {
            applyFilterAfterTimeOut();
        }

        function applyFilterAfterTimeOut() {

            var filters = {
                unitName: $scope.filter.unitName?.toLowerCase(),
                productIds: $scope.filter.selectedProducts.map(product => product.id),
                dimensionIds: $scope.filter.selectedDimensions.map(dimension => dimension.id),
                profile: $scope.filter.profileFilter ? $scope.filter.profile?.toLowerCase() : null,
                updatedStatus: $scope.filter.statusFilter ? $scope.filter.updatedStatus?.toLowerCase() : null,
                updatedDOP: $scope.filter.dopFilter ? $scope.filter.updatedDOP?.toLowerCase() : null,
                updatedDIC: $scope.filter.dicFilter ? $scope.filter.updatedDIC?.toLowerCase() : null,
                aliasSearch: $scope.filter.aliasFilter ? $scope.filter.aliasSearch?.toLowerCase() : "",
                DDSearch: $scope.filter.DDFilter ? $scope.filter.DDSearch?.toLowerCase() : ""
            };

            $scope.resultProductListDetails.forEach(item => {
                item.visible = true;

                if (!matchesFilter(item.unit.name, filters.unitName)) {
                    return (item.visible = false);
                }
                if (!AppUtil.isEmptyObject(filters.productIds) && !filters.productIds.includes(item.product.id)) {
                    return (item.visible = false);
                }
                if (!AppUtil.isEmptyObject(filters.dimensionIds) && !filters.dimensionIds.includes(item.dimension.id)) {
                    return (item.visible = false);
                }
                if ((filters.profile != null) && !matchesFilterOfProfile(item.price.profile, filters.profile)) {
                    return (item.visible = false);
                }
                if (!matchesFilterOfStatus(item.price.currentStatus, filters.updatedStatus)) {
                    return (item.visible = false);
                }
                if ((filters.updatedDOP != null) && !matchesFilter(String(item.price.isDeliveryOnlyProduct), filters.updatedDOP)) {
                    return (item.visible = false);
                }
                if ((filters.updatedDIC != null) && !matchesFilter(String(item.price.pickDineInConsumables), filters.updatedDIC)) {
                    return (item.visible = false);
                }
                if (!matchesFilter(item.price.aliasProductName, filters.aliasSearch)) {
                    return (item.visible = false);
                }
                if (!matchesFilter(item.price.dimensionDescriptor, filters.DDSearch)) {
                    return (item.visible = false);
                }
            });
            $scope.filteredRecordsCount = $scope.resultProductListDetails.filter(item => item.visible).length;
            applyPaginationForUpdateList();
        };

        function matchesFilter(value, filter) {
            return !filter || (value?.toLowerCase() || "").includes(filter);
        }
        function matchesFilterOfStatus(value, filter) {
            if ((filter == undefined || filter == null)) {
                return true;
            }
            return (value?.toLowerCase() || "") == filter;
        }
        function matchesFilterOfProfile(value, filter) {
            if ((filter == undefined || filter == null)) {
                return true;
            }
            if (filter == 'unmapped' && value == null) {
                return true;
            }
            if(filter == 'mapped' && value != null) {
                return true;
            }
            return (value?.toLowerCase() || "") == filter;
        }

        // apply filtered values to entries...
        $scope.applyProfilesFilter = function () {
            if ($scope.filter.profile == null || $scope.filter.profile == "mapped" || $scope.filter.profile == "unmapped") {
                toastService.warning("Unable to apply this filter on Recipe Profile");
                return;
            }
            $scope.resultProductListDetails.forEach(item => {
                if (item.visible) {
                    item.price.profile = $scope.filter.profile;
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        if (!item.selected) {
                            item.selected = true;
                            $scope.selectedRecordsCount++;
                        }
                    } else {
                        if (item.selected) {
                            item.selected = false;
                            $scope.selectedRecordsCount--;
                        }
                    }
                }
            });
        };

        $scope.applyProfilesStatus = function () {
            if ($scope.filter.updatedStatus == null) {
                toastService.warning("Unable to apply empty field on status");
                return;
            }
            $scope.resultProductListDetails.forEach(item => {
                if (item.visible && !AppUtil.isEmptyObject(item.price.profile)) {
                    item.price.status = $scope.filter.updatedStatus;
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        if (!item.selected) {
                            item.selected = true;
                            $scope.selectedRecordsCount++;
                        }
                    } else {
                        if (item.selected) {
                            item.selected = false;
                            $scope.selectedRecordsCount--;
                        }
                    }
                }
            });
        };

        $scope.applyDeliveryOnlyFilter = function () {
            if ($scope.filter.updatedDOP == null) {
                toastService.warning("Unable to apply empty field on delivery only");
                return;
            }
            $scope.resultProductListDetails.forEach(item => {
                if (item.visible && !AppUtil.isEmptyObject(item.price.profile)) {
                    item.price.isDeliveryOnlyProduct = $scope.filter.updatedDOP;
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        if (!item.selected) {
                            item.selected = true;
                            $scope.selectedRecordsCount++;
                        }
                    } else {
                        if (item.selected) {
                            item.selected = false;
                            $scope.selectedRecordsCount--;
                        }
                    }
                }
            });
        };

        $scope.applyPickDineInConsumablesFilter = function () {
            if ($scope.filter.updatedDIC == null) {
                toastService.warning("Unable to apply empty field on dine in consumables");
                return;
            }
            $scope.resultProductListDetails.forEach(item => {
                if (item.visible && !AppUtil.isEmptyObject(item.price.profile)) {
                    item.price.pickDineInConsumables = $scope.filter.updatedDIC;
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        if (!item.selected) {
                            item.selected = true;
                            $scope.selectedRecordsCount++;
                        }
                    } else {
                        if (item.selected) {
                            item.selected = false;
                            $scope.selectedRecordsCount--;
                        }
                    }
                }
            });
        };

        $scope.applyAlias = function () {
            if( AppUtil.isEmptyObject($scope.filter.aliasSearch) ) {
                toastService.warning("Cannot apply Empty Product Alias");
                return;
            }

            $scope.resultProductListDetails.forEach(item => {
                if (item.visible && !AppUtil.isEmptyObject(item.price.profile)) {
                    item.price.aliasProductName = $scope.filter.aliasSearch;
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        if (!item.selected) {
                            item.selected = true;
                            $scope.selectedRecordsCount++;
                        }
                    } else {
                        if (item.selected) {
                            item.selected = false;
                            $scope.selectedRecordsCount--;
                        }
                    }
                }
            });
        };

        $scope.applyDimensionDesc = function () {
            if( AppUtil.isEmptyObject($scope.filter.DDSearch) ) {
                toastService.warning("Cannot apply Empty Dimension Description");
                return;
            }

            $scope.resultProductListDetails.forEach(item => {
                if (item.visible && !AppUtil.isEmptyObject(item.price.profile)) {
                    item.price.dimensionDescriptor = $scope.filter.DDSearch;
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        if (!item.selected) {
                            item.selected = true;
                            $scope.selectedRecordsCount++;
                        }
                    } else {
                        if (item.selected) {
                            item.selected = false;
                            $scope.selectedRecordsCount--;
                        }
                    }
                }
            });
        };

        //Export to excel
        $scope.changeShowExportModal = function () {
            $scope.showExportModal = !$scope.showExportModal;
        };

        $scope.closeExportModal = function () {
            $scope.showExportModal = !$scope.showExportModal;
            toastService.create("NO action performed");
        };

        $scope.exportAllToExcel = function () {
            exportToExcel('all');
            $scope.showExportModal = false;
        };

        $scope.exportSelectedToExcel = function () {
            exportToExcel('selected');
            $scope.showExportModal = false;
        };

        $scope.exportFilteredToExcel = function () {
            exportToExcel('filtered');
            $scope.showExportModal = false;
        };

        function exportToExcel(type) {
            var exportData = [];
            for (var i = 0; i < $scope.resultProductListDetails.length; i++) {
                if (type == 'selected' && !$scope.resultProductListDetails[i].selected) {
                    continue;
                }
                if(type == 'filtered' && !$scope.resultProductListDetails[i].visible) {
                    continue;
                }
                var item = $scope.resultProductListDetails[i];
                var row = [
                    item.unit.id,
                    item.unit.name,
                    item.product.id,
                    item.product.name,
                    item.dimension.id,
                    item.dimension.code,
                    item.price.currentProfile, (item.price.profile != item.price.currentProfile ? item.price.profile : item.price.currentProfile),
                    item.price.currentStatus, (item.price.status != item.price.currentStatus ? item.price.status : item.price.currentStatus),
                    item.price.isDeliveryOnlyProductOldValue, (item.price.isDeliveryOnlyProduct != item.price.isDeliveryOnlyProductOldValue ? item.price.isDeliveryOnlyProduct : item.price.isDeliveryOnlyProductOldValue),
                    item.price.pickDineInConsumablesOldValue, (item.price.pickDineInConsumables != item.price.pickDineInConsumablesOldValue ? item.price.pickDineInConsumables : item.price.pickDineInConsumablesOldValue),
                    item.price.currentAliasProductName, item.price.aliasProductName,
                    item.price.currentDimensionDescriptor, item.price.dimensionDescriptor
                ];
                exportData.push(row);
            }
            if (AppUtil.isEmptyObject(exportData)) {
                toastService.warning("No data found to export all");
                return;
            } else {
                toastService.create("Exporting Selected from the table");
            }

            var headerNames = [
                "UNIT_ID", "UNIT_NAME", "PRODUCT_ID", "PRODUCT_NAME", "DIMENSION_ID", "DIMENSION_CODE",
                "OLD_PROFILE", "NEW_PROFILE", "OLD_STATUS", "NEW_STATUS", "OLD_DELIVERY_ONLY", "NEW_DELIVERY_ONLY",
                "OLD_PICK_DINE_IN_CONSUMABLES", "NEW_PICK_DINE_IN_CONSUMABLES",
                "OLD_PRODUCT_ALIAS", "NEW_PRODUCT_ALIAS", "OLD_DIMENSION_DESC", "NEW_DIMENSION_DESC"
            ];
            var nonEditableColumnNames = [
                "UNIT_ID", "PRODUCT_ID", "DIMENSION_ID", "OLD_PROFILE",
                "OLD_STATUS", "OLD_DELIVERY_ONLY", "OLD_PICK_DINE_IN_CONSUMABLES", "OLD_PRODUCT_ALIAS", "OLD_DIMENSION_DESC"
            ];

            var fileName = "UNIT_PRODUCT_PROFILE_MAPPING_" + $rootScope.userData.id;

            var data = {
                headerNames,
                fileName,
                body: exportData,
                nonEditableColumnNames
            };

            AdminApiService
                .post(AppUtil.restUrls.genericExcelManagement.getExcelFromData, data, {}, { responseType: 'arraybuffer' })
                .then(function (data) {
                    if (data != null) {
                        var blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        saveAs(blob, fileName + "-Date-" + new Date().getDate() + ".xlsx");
                    } else {
                        toastService.error("Failed to export data to excel.");
                    }
                })

        };

        // uploading excel and manipulating excel and showing data

        $scope.removePreviousExcel = function() {
            angular.element("input[type='file']").val(null);
            $scope.bulkFile = null;
            fileService.push(null);
        }

        $scope.uploadBulkSheet = function () {
            if (!fileService.getFile()) {
                toastService.warning("Please select a file to proceed");
                return;
            }

            console.log("File is", fileService.getFile());

            var fd = new FormData();
            fd.append("file", fileService.getFile());
            fd.append("className", "com.stpl.tech.master.data.model.UnitProductProfileExcelData");
            $rootScope.showFullScreenLoader = true;
            $http({
                url: AppUtil.restUrls.genericExcelManagement.getListDataFromExcel,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined,
                },
                transformRequest: angular.identity
            }).then(function (response) {
                if (AppUtil.isEmptyObject(response.data) || !AppUtil.isEmptyObject(response.data.errorMessage)) {
                    toastService.warning("Excel data after parsing was empty, please re-check data in excel");
                    return;
                }
                manipulateData(response.data);
            }).catch(function (response) {
                $rootScope.showFullScreenLoader = false;
                console.log("Error  ", response);
                toastService.error("Error while uploading bulk data");
            });
        };

        function manipulateData(data) {
            $scope.bulkUploadedData = [];
            for (var i = 0; i < data.length; i++) {
                var item = data[i];
                if( item.newRecipeProfile != item.oldRecipeProfile || item.newStatus != item.oldStatus || item.newDeliveryOnly != item.oldDeliveryOnly
                        || item.newPickDineInConsumables != item.oldPickDineInConsumables
                        || item.newDimensionDesc != item.oldDimensionDesc || item.newProductAlias != item.oldProductAlias) {
                    $scope.bulkUploadedData.push(item);
                }
            }
            $rootScope.showFullScreenLoader = false;
            if (AppUtil.isEmptyObject($scope.bulkUploadedData)) {
                toastService.warning("Please update at-least one field to proceed, re-check the uploaded excel");
                return;
            }
            toastService.create("Verify data and submit");
            $scope.showBulkUploadModal = true;
        };

        $scope.isFieldsChanged = function(oldValue, newValue) {
            if( AppUtil.isEmptyObject(oldValue) && AppUtil.isEmptyObject(newValue) ) {
                return false;
            } else if ( (!AppUtil.isEmptyObject(oldValue) && AppUtil.isEmptyObject(newValue)) || (AppUtil.isEmptyObject(oldValue) && !AppUtil.isEmptyObject(newValue)) ) {
                return true;
            }
            return oldValue.trim() != newValue.trim();
        }

        $scope.closeBulkUploadModal = function () {
            $scope.showBulkUploadModal = false;
        }

        $scope.submitBulkUploadModal = function () {
            if (!fileService.getFile()) {
                toastService.warning("Please select a file to proceed");
                return;
            }

            var fd = new FormData();
            fd.append("file", fileService.getFile());
            fd.append("pageType", $scope.isProductRecipeMapping ? "UNIT_PRODUCT_PRICING_PROFILE" : "UNIT_PRODUCT_PRICING_DESC");

            var requestDto = {
                pageType: $scope.isProductRecipeMapping ? "UNIT_PRODUCT_PRICING_PROFILE" : "UNIT_PRODUCT_PRICING_DESC",
                excelData: $scope.bulkUploadedData
            };

            fd.append('requestDto', new Blob([JSON.stringify(requestDto)], {
                type: 'application/json'
            }));

            $rootScope.showFullScreenLoader = true;
            $http({
                url: AppUtil.restUrls.productMetaData.bulkUpdatedUnitProductProfiles,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined,
                },
                transformRequest: angular.identity
            }).then(function (response) {
                $rootScope.showFullScreenLoader = false;
                angular.element("input[type='file']").val(null);
                $scope.bulkFile = null;
                fileService.push(null);
                if ( !AppUtil.isEmptyObject(response.data.errorMessage) || !AppUtil.isEmptyObject(response.data.errorMessage) ) {
                    toastService.error("Error !");
                    return;
                }
                toastService.create(response.data.message, 7000);
                if( !AppUtil.isEmptyObject(response.data.data) ) {
                    $scope.successMsgs = response.data.data.success;
                    $scope.failureMsgs = response.data.data.failure;
                    if( !AppUtil.isEmptyObject($scope.successMsgs) ||  !AppUtil.isEmptyObject($scope.failureMsgs) ) {
                        $scope.showMsgsModal = true;
                    }
                    $scope.failureMsgsTrimmed = [];
                    for(var idx = 0; idx <= $scope.failureMsgs.length; idx++) {
                        $scope.failureMsgsTrimmed.push( $scope.failureMsgs[idx] );
                        if(idx == 10) {
                            return;
                        }
                    }
                }
                $scope.showBulkUploadModal = false;

            }).catch(function (response) {
                $rootScope.showFullScreenLoader = false;
                angular.element("input[type='file']").val(null);
                console.log("Error  ", response);
                toastService.error("Error while uploading bulk data");
            });
        }

        $scope.showMoreErrorMsgs = function() {
            var i = $scope.failureMsgsTrimmed.length;
            for(var idx = i; idx <= $scope.failureMsgs.length; idx++) {
                $scope.failureMsgsTrimmed.push( $scope.failureMsgs[idx] );
                if(idx == i+10) {
                    return;
                }
            }
        }

        //below function is for data manupulation after making changes in table
        $scope.viewChangesInModal = function () {
            $scope.modalData = [];
            $scope.updatedRecordsCount = 0;
            $scope.resultProductListDetails.forEach(item => {
                if (item.selected) {
                    if ($scope.checkIsAnyFieldChanged(item)) {
                        $scope.updatedRecordsCount++;
                        $scope.modalData.push(item);
                    }
                }
            });

            if (AppUtil.isEmptyObject($scope.modalData)) {
                toastService.warning("Please select or update at-least one entry to view mappings");
                return;
            } else {
                toastService.create("Selected " + ($scope.modalData.length == 1 ? " 1 entry !" : ($scope.modalData.length + " entries !")));
            }
            $scope.isModalOpen = true;
        };

        $scope.checkIsAnyFieldChanged = function (item) {
            if (item.price.profile != item.price.currentProfile) {
                return true;
            }
            if (item.price.status != undefined && item.price.status != item.price.currentStatus) {
                return true;
            }
            if (item.price.isDeliveryOnlyProduct != item.price.isDeliveryOnlyProductOldValue) {
                return true;
            }
            if (item.price.pickDineInConsumables != item.price.pickDineInConsumablesOldValue) {
                return true;
            }
            if (item.price.dimensionDescriptor != item.price.currentDimensionDescriptor) {
                // if (!AppUtil.isEmptyObject(item.price.dimensionDescriptor)) {
                    return true;
                // }
            }
            if (item.price.aliasProductName != item.price.currentAliasProductName) {
                // if (!AppUtil.isEmptyObject(item.price.aliasProductName)) {
                    return true;
                // }
            }
            return false;
        };

        $scope.submitModal = function () {
            // $scope.isModalOpen = false;
            if ($scope.updatedRecordsCount == 0) {
                toastService.warning("Please update atleast one from the selected list to submit data!");
                return;
            }
            var upmList = [];
            $scope.modalData.forEach(item => {
                item.price.isDeliveryOnlyProduct = item.price.isDeliveryOnlyProduct == "YES" ? true : false;
                item.price.pickDineInConsumables = item.price.pickDineInConsumables == "YES" ? true : false;
                upmList.push(item);
            });

            if (AppUtil.isEmptyObject(upmList)) {
                toastService.warning("NO Entries found to update");
                $scope.isModalOpen = false;
                return;
            }
            var pageType = $scope.isProductRecipeMapping ? "UNIT_PRODUCT_PRICING_PROFILE" : "UNIT_PRODUCT_PRICING_DESC";

            var url = AppUtil.restUrls.productMetaData.changeUnitProductProfile + "?pageType=" + pageType;
            AdminApiService
                .post(url, upmList)
                .then(function (resultData) {
                    if (AppUtil.isEmptyObject(resultData)) {
                        toastService.warning("Something went wrong please try again!");
                        return;
                    }
                    showCompleteMsgOnModal(resultData);
                });
        }

        function showCompleteMsgOnModal(resultData) {
            if( AppUtil.isEmptyObject(resultData.data) ) {
                toastService.create(resultData.message);
            } else {
                $scope.successMsgs = resultData.data.success;
                $scope.failureMsgs = resultData.data.failure;
                if( !AppUtil.isEmptyObject($scope.successMsgs) || !AppUtil.isEmptyObject($scope.failureMsgs) ) {
                    $scope.showMsgsModal = true;
                }
            }
            $scope.isModalOpen = false;
            $scope.$apply();
        }

        $scope.closeShowMsgModal = function () {
            $scope.showMsgsModal = false;
            $scope.init();
        }

    })
