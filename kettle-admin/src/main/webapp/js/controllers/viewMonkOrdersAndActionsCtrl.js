adminapp
    .controller(
        "ViewMonkOrdersAndActionsCtrl", ['$rootScope', '$scope', '$http', 'AppUtil', '$cookieStore', '$timeout',
        function ($rootScope, $scope, $http, AppUtil, $cookieStore, $timeout) {
            var selectedCustomer;
            $scope.init = function () {
                $scope.actionList = ['VIEW ORDERS', 'VIEW ACTIONS', 'VIEW ERRORS'];
                $scope.selectedAction = 'VIEW ORDERS';

                clearObj();
            };

            function clearObj() {
                $scope.order = {};
                $scope.action = {};
                $scope.error = {};
                $scope.customerDetailList = [];
                $scope.userDetailList = [];
            }

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                clearObj();
                switch (action) {
                    case 'VIEW ORDERS':
                        break;
                    case 'VIEW ACTIONS':
                        break;
                    case 'VIEW ERRORS':
                        break;
                }
            };

            $scope.checkOrderDetailObj = function () {
                if ($scope.isUndefinedOrNull($scope.order.userName)) {
                    return false;
                }
                if ($scope.isUndefinedOrNull($scope.order.startDate)) {
                    return false;
                }
                if ($scope.isUndefinedOrNull($scope.order.endDate)) {
                    return false;
                }
                return true;
            };

            $scope.checkActionDetailObj = function () {
                if ($scope.isUndefinedOrNull($scope.action.userName)) {
                    return false;
                }
                if ($scope.isUndefinedOrNull($scope.action.startDate)) {
                    return false;
                }
                if ($scope.isUndefinedOrNull($scope.action.endDate)) {
                    return false;
                }
                return true;
            };

            $scope.checkErrorDetailObj = function () {
                if ($scope.isUndefinedOrNull($scope.error.userName)) {
                    return false;
                }
                if ($scope.isUndefinedOrNull($scope.error.startDate)) {
                    return false;
                }
                if ($scope.isUndefinedOrNull($scope.error.endDate)) {
                    return false;
                }
                return true;
            };

            $scope.isUndefinedOrNull = function (val) {
                return angular.isUndefined(val) || val === null || val === "";
            };

            $scope.downloadOrderDetails = function () {
                $rootScope.showFullScreenLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: AppUtil.restUrls.b2bMonkManagement.getOrderDetailsByUsername,
                        params: {
                            userName: $scope.order.userName,
                            startDate: $scope.order.startDate,
                            endDate: $scope.order.endDate,
                            machineId: $scope.order.machineId
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != undefined && response != null) {
                                var fileName = "ORDER_DETAILS" + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                        },
                        function error(response) {
                            $rootScope.showFullScreenLoader = false;
                            console.log("error:" + response);
                            alert("Unable to download Order report");
                        });
            };

            $scope.downloadActionDetails = function () {
                $rootScope.showFullScreenLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: AppUtil.restUrls.b2bMonkManagement.getActionDetailsByUsername,
                        params: {
                            userName: $scope.action.userName,
                            startDate: $scope.action.startDate,
                            endDate: $scope.action.endDate,
                            machineId: $scope.action.machineId
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != undefined && response != null) {
                                var fileName = "ACTION_DETAILS" + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                        },
                        function error(response) {
                            $rootScope.showFullScreenLoader = false;
                            console.log("error:" + response);
                            alert("Unable to download actions report");
                        });
            };

            $scope.downloadErrorDetails = function () {
                $rootScope.showFullScreenLoader = true;
                $http(
                    {
                        method: 'GET',
                        url: AppUtil.restUrls.b2bMonkManagement.getErrorDetailsByUsername,
                        params: {
                            userName: $scope.error.userName,
                            startDate: $scope.error.startDate,
                            endDate: $scope.error.endDate,
                            machineId: $scope.error.machineId
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                    .then(
                        function success(response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != undefined && response != null) {
                                var fileName = "ERROR_DETAILS" + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }
                        },
                        function error(response) {
                            $rootScope.showFullScreenLoader = false;
                            console.log("error:" + response);
                            alert("Unable to download error report");
                        });
            };

            var fetchCustomers = function () {
                $scope.timerStarted = false;
                if (selectedCustomer != null && selectedCustomer.length > 0) {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.b2bMonkManagement.getCustomersByName,
                        params: {
                            customerName: selectedCustomer
                        }
                    }).then(function success(response) {
                        if (response.status == 200) {
                            $scope.customerDetailList = response.data;
                        }
                    }, function error(response) {
                        console.log("error: " + response);
                    });
                    return $scope.customerDetailList;
                }
            }

            $scope.getCustomers = function (customerName) {
                console.log(customerName);
                selectedCustomer = customerName;
                if (!$scope.timerStarted) {
                    $timeout(fetchCustomers, 300);
                    $scope.timerStarted = true;
                }
                return $scope.customerDetailList;
            };

            $scope.getUserDetailList = function (customer) {
                console.log("Getting users...");
                if (customer != null) {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.b2bMonkManagement.getUsersByCustomer,
                        params: {
                            customerId: customer.customerId
                        }
                    }).then(function success(response) {
                        if (response.status == 200) {
                            $scope.userDetailList = response.data;
                            console.log($scope.userDetailList);
                        }
                    }, function error(response) {
                        console.log("error: " + response);
                    });
                }
            };

            $scope.selectUserAndMachineForOrders = function () {
                $scope.order.userName = $scope.order.selectedUser.userName;
                $scope.order.machineId = $scope.order.selectedUser.machineId;
            };
            
            $scope.selectUserAndMachineForActions = function () {
                $scope.action.userName = $scope.action.selectedUser.userName;
                $scope.action.machineId = $scope.action.selectedUser.machineId;
            };
            
            $scope.selectUserAndMachineForErrors = function () {
                $scope.error.userName = $scope.error.selectedUser.userName;
                $scope.error.machineId = $scope.error.selectedUser.machineId;
            };

        }]);