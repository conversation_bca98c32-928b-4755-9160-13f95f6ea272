adminapp.controller("CashbackOfferCtrl", function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

    console.log($stateParams);
    $scope.campaignDetail = $stateParams.campaignDetail;

    $scope.init = function () {
        $scope.regionList=[]
        $scope.selectedRegionList=[]
        $scope.selectedRegionListForOfferAddition=[]
        $scope.allUnitList = []
        $scope.unitList = []
        $scope.unitListForOfferAddition = []
        $scope.selectedUnitList = []
        $scope.selectedUnitListForOfferAddition = []
        $scope.offerList = []
        $scope.statusList = ['ACTIVE', 'IN_ACTIVE']
        $scope.offerForUpdation = {}
        $scope.unitMap={}
        $scope.actionType="ADD"
        getRegionDetails()
        getCityAndUnitDetails()
    };
    function getRegionDetails() {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.regions
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                $scope.regionList = response.data;
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
            });
        }
    function getCityAndUnitDetails() {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnits,
                params: {
                    category: 'CAFE'
                }
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                $scope.allUnitList = response.data;
                for(var i in $scope.allUnitList){
                    $scope.unitMap[$scope.allUnitList[i].id]=$scope.allUnitList[i]
                }
                // fillUnitMap();
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
            });
        }

    $scope.refreshCache= function(){
    $rootScope.showFullScreenLoader = true;
        $http({
        		  method: 'POST',
        		  url: AppUtil.restUrls.offerManagement.refreshCashbackOfferCache,
        		  data: {},
        		}).then(function success(response) {
        			console.log(response.data);
        			if(response.status==200){
        				$rootScope.showFullScreenLoader = false;
        				alert("Cache refresh successfully");
        				$scope.refreshOnNewKettle();
        			}else{
        				$rootScope.showFullScreenLoader = false;
        				console.log(response);
        			}
        		}, function error(response) {
        			  console.log("error:"+response);
        			  $rootScope.showFullScreenLoader = false;
        		});
    }

    $scope.refreshOnNewKettle=function(){
        $rootScope.showFullScreenLoader = true;
                $http({
                		  method: 'POST',
                		  url: AppUtil.restUrls.offerManagement.refreshCashbackOfferCacheV2,
                		  data: {},
                		}).then(function success(response) {
                			console.log(response.data);
                			if(response.status==200){
                				$rootScope.showFullScreenLoader = false;
                				alert("Cache refresh successfully on v2");
                			}else{
                				$rootScope.showFullScreenLoader = false;
                				console.log(response);
                			}
                		}, function error(response) {
                			  console.log("error:"+response);
                			  $rootScope.showFullScreenLoader = false;
                		});
    }

    $scope.changeStatus=function(index){
        var status = $scope.offerList[index].offerStatus==="ACTIVE" ? "IN_ACTIVE" : "ACTIVE"
        var unitIds = []
        unitIds.push($scope.offerList[index].unitId)
        $rootScope.showFullScreenLoader = true;
                    $http({
                    		  method: 'POST',
                    		  url: AppUtil.restUrls.offerManagement.addCashbackOfferCache,
                    		  data: {...$scope.offerList[index],unitIds:unitIds, offerStatus: status},
                    		}).then(function success(response) {
                    			console.log(response.data);
                    			if(response.status==200 && response.data === true){
                    				$rootScope.showFullScreenLoader = false;
                    				$scope.offerList[index].offerStatus = status;
                    			}else{
                    				$rootScope.showFullScreenLoader = false;
                    				alert("unable to change status")
                    			}
                    		}, function error(response) {
                    			  console.log("error:"+response);
                    			  $rootScope.showFullScreenLoader = false;
                    		});
    }

    function getFormatedTime(timeStamp){
        var getYear = new Date(timeStamp).toLocaleString("default", { year: "numeric" });
        var getMonth = new Date(timeStamp).toLocaleString("default", { month: "2-digit" });
        var getDay = new Date(timeStamp).toLocaleString("default", { day: "2-digit" });
        return getYear + "-" + getMonth + "-" + getDay;
    }

    $scope.loadOffers= function(){
        var selectUnitIds = []
        for(var i in $scope.selectedUnitList){
            selectUnitIds.push($scope.selectedUnitList[i].id)
        }
        $rootScope.showFullScreenLoader = true;
            $http({
            		  method: 'POST',
            		  url: AppUtil.restUrls.offerManagement.getUnitWiseOffers,
            		  data: {unitIds:selectUnitIds},
            		}).then(function success(response) {
            			console.log(response.data);
            			if(response.status==200){
            				$rootScope.showFullScreenLoader = false;
            				$scope.offerList= response.data;
            				for(var i in $scope.offerList){
            				    $scope.offerList[i].offerStartDate = getFormatedTime($scope.offerList[i].offerStartDate)
            				    $scope.offerList[i].offerEndDate = getFormatedTime($scope.offerList[i].offerEndDate)
            				}
            			}else{
            				$rootScope.showFullScreenLoader = false;
            				console.log(response);
            			}
            		}, function error(response) {
            			  console.log("error:"+response);
            			  $rootScope.showFullScreenLoader = false;
            		});
        }

    $scope.multiSelectSettings = {
        showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
        scrollableHeight: '200px'
    };

    $scope.multiSelectSettingsUnits = {
            showEnableSearchButton: true, template: '<b>{{option.name}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };

    $scope.updateUnitList = function(){
        $scope.unitList =[];
        for(var i in $scope.allUnitList){
            if($scope.selectedRegionList.includes($scope.allUnitList[i].region)){
                $scope.unitList.push($scope.allUnitList[i])
            }
        }
    }

    $scope.updateUnitListForOfferAddition = function(){
    $scope.unitListForOfferAddition =[];
            for(var i in $scope.allUnitList){
                if($scope.selectedRegionListForOfferAddition.includes($scope.allUnitList[i].region)){
                    $scope.unitListForOfferAddition.push($scope.allUnitList[i])
                }
            }
    }

    $scope.editOffer = function(index){
        $scope.offerForUpdation = {...$scope.offerList[index], index:index}
        $scope.actionType = "EDIT"
        $("#updationModal").modal("show");
    }

    $scope.addNewOffer = function(){
        $scope.selectedUnitListForOfferAddition = []
        $scope.unitListForOfferAddition = []
        $scope.selectedRegionListForOfferAddition=[]
        $scope.offerForUpdation = {}
        $scope.actionType = "ADD"
        $("#updationModal").modal("show");
    }

    $scope.updateOffer = function(index){
        var unitIds = []
        unitIds.push($scope.offerForUpdation.unitId)
        $rootScope.showFullScreenLoader = true;
             $http({
             		  method: 'POST',
             		  url: AppUtil.restUrls.offerManagement.addCashbackOfferCache,
             		  data: {...$scope.offerForUpdation, unitIds:unitIds},
             		}).then(function success(response) {
             			console.log(response.data);
             			if(response.status==200 && response.data === true){
             				$rootScope.showFullScreenLoader = false;
             				$scope.offerList[index] = {...$scope.offerForUpdation};
             				$("#updationModal").modal("hide");
             			}else{
             				$rootScope.showFullScreenLoader = false;
             				alert("unable to change status")
             			}
             		}, function error(response) {
             			  console.log("error:"+response);
             			  $rootScope.showFullScreenLoader = false;
             		});
    }

    $scope.addOffer = function(){
            var unitIds = []
            for(var i in $scope.selectedUnitListForOfferAddition){
                unitIds.push($scope.selectedUnitListForOfferAddition[i].id)
            }
            $rootScope.showFullScreenLoader = true;
                 $http({
                 		  method: 'POST',
                 		  url: AppUtil.restUrls.offerManagement.addCashbackOfferCache,
                 		  data: {...$scope.offerForUpdation, unitIds:unitIds},
                 		}).then(function success(response) {
                 			console.log(response.data);
                 			if(response.status==200 && response.data === true){
                 				$rootScope.showFullScreenLoader = false;
                 				$scope.offerList[index] = {...$scope.offerForUpdation};
                 				$("#updationModal").modal("hide");
                 				alert("Offer added")
                 			}else{
                 				$rootScope.showFullScreenLoader = false;
                 				alert("unable to change status")
                 			}
                 		}, function error(response) {
                 			  console.log("error:"+response);
                 			  $rootScope.showFullScreenLoader = false;
                 		});
        }

});