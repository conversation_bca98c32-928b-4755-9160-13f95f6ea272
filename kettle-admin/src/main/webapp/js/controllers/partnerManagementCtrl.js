/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerManagementCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http',
        function ($location, $scope, AppUtil, $rootScope, $http) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            var selectedDimension=[];
            var productIdsForDOTD = [];
            $scope.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.channelPartnerList = [];
                $scope.getUnitList();
                $scope.channelPartners = [];
                $scope.unitChannelPartnerMappings = [];
                $scope.tagGroupList=["Default","Dietary","Single Serve Cuisines","Single Serve Meal Time",
                    "Miscellaneous", "Info", "Single Serve Meal Type", "Life Extension Selection"];
                $scope.actionList = ["PARTNER VIEW", "UNIT STATUS", "PRODUCT STATUS", "PRODUCT FILTER", "PRODUCT TAGS","MEAT TAGS","ALLERGEN TYPES", "SERVING INFO","SERVING SIZE","DEAL OF THE DAY"];
                $scope.selectedAction = "PARTNER VIEW";
                $scope.selectAction($scope.selectedAction);
                console.log($scope.actionList);
                $scope.today = new Date();
                $scope.newTag = {id:null, name:null,group:null};
                $scope.selectAllUnits = false;
                $scope.selectedTag=false;
                $scope.servingSizeUnits=["grams","ml"];
                $scope.selectedProductsDTDO=[];
                $scope.getSwiggyStockStatus();
                //$scope.getIntegratedChannelPartners();
                /*$scope.channelPartners.map(function (partner) {
                    $scope.actionList.push(partner.name);
                });*/
            };

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                switch (action) {
                    case "PARTNER VIEW":
                        $scope.initPartnerView();
                        break;
                    case "UNIT STATUS":
                        $scope.initUnitStatus();
                        break;
                    case "PRODUCT STATUS":
                        $scope.initProductStatus();
                        break;
                    case "PRODUCT FILTER":
                        $scope.initProductStatus();
                        break;
                    case "PRODUCT TAGS":
                        $scope.initProductTags();
                        break;
                    case "SWIGGY":
                        $scope.initSwiggy();
                        break;
                    case "ZOMATO":
                        $scope.initZomato();
                        break;
                    case "MEAT TAGS":
                        $scope.initMeatTags();
                        break;
                    case "ALLERGEN TYPES":
                        $scope.initAllergenTypes();
                        break;
                    case "SERVING INFO":
                        $scope.initServingInfo();
                        break;
                    case "SERVING SIZE":
                        $scope.initServingSize();
                        break;
                    case "DEAL OF THE DAY":
                        $scope.initDealOfTheDay();
                        break;

                }
            };

            $scope.initPartnerView = function () {
                $scope.getChannelPartnersList();
            };

            $scope.initUnitStatus = function () {
                $scope.selectAllUnits = false;
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
                $scope.getAllBrands();
            };

            $scope.initProductStatus = function () {
                $scope.showUnitProducts = false;
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
                $scope.unitProductList = [];
                $scope.selectedUnit = null;
                $scope.getAllBrands();
            };

            $scope.initProductTags = function () {
                $scope.resetChannelPartnersList();
                $scope.showTagsView = false;
                $scope.partnerTags = [];
                $scope.newTag = {id:null, name:null,group:null};
                $scope.showProductTagMappingView = false;
                $scope.productsTagsMappings = [];
                $scope.getAllBrands();
            };

            $scope.initSwiggy = function () {

            };

            $scope.initZomato = function () {
                $scope.getZomatoTreatsItem();
            };

            $scope.initMeatTags = function () {
                $scope.showMeatTagsView = false;
                $scope.meatTags = [];
                $scope.newMeatTag = {id:null, name:null};
                $scope.showMeatTagMappingView = false;
                $scope.meatTagsMappings = [];
                $scope.getAllBrands();
            };
            $scope.initAllergenTypes = function () {
                $scope.showAllergenTagsView = false;
                $scope.allergenTags = [];
                $scope.newAllergenTag = {id:null, name:null};
                $scope.showAllergenTagMappingView = false;
                $scope.allTagsMappings = [];
                $scope.getAllBrands();
            };
            $scope.initServingInfo = function () {
                $scope.showServingInfoView = false;
                $scope.servingInfo = [];
                $scope.newServingInfo = {id:null, name:null};
                $scope.showServingInfoMappingView = false;
                $scope.servingInfoTagsMappings = [];
                $scope.getAllBrands();
            };
            $scope.initServingSize = function () {
                $scope.showServingSizeView = false;
                $scope.servingSize = [];
                $scope.newServingSize = {id:null, value:null, unit: null};
                $scope.showServingSizeMappingView = false;
                $scope.servingSizeMappings = [];
                $scope.getAllBrands();
            };

            $scope.setSelectAllUnits = function() {
                $scope.selectAllUnits = !$scope.selectAllUnits;
                $scope.unitList.map(function (unit) {
                    unit.selected = $scope.selectAllUnits;
                });
            };

            $scope.initDealOfTheDay = function () {
                $scope.showUnitProducts = false;
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
                $scope.unitProductList = [];
                $scope.selectedUnit = null;
                $scope.getAllBrands();
                $scope.getActiveProductsDTDO();
                $scope.dimensionsInfo={};
                $scope.getAllDimensions();
            };

            $scope.getAllBrands = function () {
                if ($scope.brands == null || $scope.brands.length == 0) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.brandManagement.getAllBrands
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.brands = response.data;
                            $rootScope.showFullScreenLoader = false;
                        } else {
                            bootbox.alert("Error getting brands.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.getChannelPartnersList = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerManagement.get
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.channelPartnerList = response.data;
                        $scope.channelPartners = [];
                        $scope.actionList = ["PARTNER VIEW", "UNIT STATUS", "PRODUCT STATUS", "PRODUCT FILTER", "PRODUCT TAGS","MEAT TAGS","ALLERGEN TYPES","SERVING INFO","SERVING SIZE","DEAL OF THE DAY"];
                        $scope.channelPartnerList.map(function (partner) {
                            if (partner.partnerStatus === "ACTIVE") {
                                $scope.channelPartners.push({
                                    id: partner.kettlePartnerId,
                                    name: partner.partnerName,
                                    selected: false
                                });
                                $scope.actionList.push(partner.partnerName);
                            }
                        });
                    } else {
                        bootbox.alert("Error loading channel partner list.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                });
            };

            $scope.resetUnitList = function () {
                $scope.unitList.map(function (unit) {
                    unit.selected = false;
                });
            };

            $scope.resetChannelPartnersList = function () {
                $scope.channelPartners.map(function (partner) {
                    partner.selected = false;
                });
            };

            $scope.togglePartnerStatus = function (partner) {
                $rootScope.showFullScreenLoader = true;
                var url = AppUtil.restUrls.partnerManagement.activate;
                if (partner.partnerStatus === "ACTIVE") {
                    url = AppUtil.restUrls.partnerManagement.deactivate;
                }
                $http({
                    method: 'POST',
                    url: url,
                    data: partner.partnerId
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Partner status updated successfully!");
                        partner = response.data;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("Error in getting response:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            /*$scope.reloadUnitChannelPartnerMapping = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartnerCache.reloadUnitChannelPartnerMapping,
                    data: {}
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Unit partner mapping updated successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("Error in getting response:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };*/

            $scope.reloadChannelPartnerCache = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartnerCache.reloadPartnersCache,
                    data: {}
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Channel partner cache updated successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("Error in getting response:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setUnitStatus = function (status, startDate, endDate) {
                var ret = false;
                var unitIds = [], partnerIds = [];
                $scope.channelPartners.map(function (partner) {
                    if (partner.selected === true) {
                        partnerIds.push(partner.id);
                    }
                });
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        unitIds.push(unit.id);
                    }
                });
                if (unitIds.length === 0) {
                    bootbox.alert("Please select at least one unit!");
                    ret = true;
                    return;
                } else if (partnerIds.length === 0) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                if (!ret) {
                    var req = {
                        unitIds: unitIds,
                        partnerIds: partnerIds,
                        startDate: new Date(startDate),
                        endDate: new Date(endDate),
                        status: status,
                        brandId: $scope.selectedBrand.brandId
                    };
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.unitToggle,
                        data: req
                    }).then(function success(response) {
                        if (response.status === 200 && response.data === true) {
                            bootbox.alert("Unit status updated successfully!");
                            $scope.initUnitStatus();
                        } else {
                            if(response.data.errorMsg != null) {
                                bootbox.alert(response.data.errorMsg);
                            } else {
                                bootbox.alert("Error in updating status:" + response.data);
                            }
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        bootbox.alert(response.errorMsg);
                        console.log("Error in getting response:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setUnitTakeawayStatus = function (status) {
                var ret = false;
                var unitIds = [], partnerIds = [];
                $scope.channelPartners.map(function (partner) {
                    if (partner.selected === true) {
                        partnerIds.push(partner.id);
                    }
                });
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        unitIds.push(unit.id);
                    }
                });
                if (unitIds.length === 0) {
                    bootbox.alert("Please select at least one unit!");
                    ret = true;
                    return;
                } else if (partnerIds.length === 0) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                if (!ret) {
                    var req = {
                        unitIds: unitIds,
                        partnerIds: partnerIds,
                        brandId: $scope.selectedBrand.brandId,
                        status: status
                    };
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.updateOutletTakeawayStatus,
                        data: req
                    }).then(function success(response) {
                        if (response.status === 200 && response.data === true) {
                            bootbox.alert("Unit takeaway status update requested successfully!");
                            $scope.initUnitStatus();
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("Error in getting response:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setUnitStock = function () {
                var ret = false;
                var unitIds = [], partnerIds = [];
                $scope.channelPartners.map(function (partner) {
                    if (partner.selected === true) {
                        partnerIds.push(partner.id);
                    }
                });
                $scope.unitList.map(function (unit) {
                    if (unit.selected === true) {
                        unitIds.push(unit.id);
                    }
                });
                if (unitIds.length === 0) {
                    bootbox.alert("Please select at least one unit!");
                    ret = true;
                    return;
                } else if (partnerIds.length === 0) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                if (!ret) {
                    var req = {
                        unitIds: unitIds,
                        partnerIds: partnerIds,
                        brandId: $scope.selectedBrand.brandId
                    };
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.publishInventoryToPartner,
                        data: req
                    }).then(function success(response) {
                        if (response.status === 200) {
                            bootbox.alert("Unit stock updated successfully!");
                            $scope.initUnitStatus();
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("Error in getting response:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
            };

            $scope.setSelectedBrand = function (selectedBrand) {
                $scope.selectedBrand = selectedBrand;
            };

            $scope.setSelectedPartner = function (selectedPartner) {
                $scope.selectedPartner = selectedPartner;
                if($scope.selectedAction == "PRODUCT TAGS") {
                    $scope.showTagsView = false;
                    $scope.partnerTags = [];
                    $scope.newTag = {id:null, name:null};
                    $scope.showProductTagMappingView = false;
                    $scope.productsTagsMappings = [];
                }
            };

            $scope.getUnitProducts = function () {
                if ($scope.selectedUnit != null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.unitPartnerProductsTrimmed + "?unitId=" + $scope.selectedUnit.id +
                        "&partnerId="+$scope.selectedPartner.id + "&brandId="+$scope.selectedBrand.brandId,
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.unitProductList = response.data;
                            $scope.showUnitProducts = true;
                            if ($scope.selectedAction === 'PRODUCT FILTER') {
                                if ($scope.selectedPartner != null) {
                                    $scope.getFilteredProducts();
                                } else {
                                    bootbox.alert("Please select partner.");
                                    $rootScope.showFullScreenLoader = false;
                                }
                            } else {
                                $scope.createTreatEligibleProducts();
                                $rootScope.showFullScreenLoader = false;
                            }
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    bootbox.alert("Please select unit.");
                }
            };

            $scope.getFilteredProducts = function () {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand.");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner.");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductFilter+
                        "?partnerId="+$scope.selectedPartner.id+"&brandId="+$scope.selectedBrand.brandId,
                        data:$scope.selectedPartner.id
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null && response.data.length > 0) {
                            response.data.map(function (productId) {
                                $scope.unitProductList.map(function (product) {
                                    if(productId == product.detail.id){
                                        product.selected = true;
                                    }
                                });
                            });
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setFilteredProducts = function () {
                var reqObj = {productIds:[],partnerIds:[$scope.selectedPartner.id], brandId:$scope.selectedBrand.brandId};
                $scope.unitProductList.map(function (product) {
                    if(product.selected == true){
                        reqObj.productIds.push(product.detail.id)
                    }
                });
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductFilter,
                    data:reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Filter products updated successfully.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.createTreatEligibleProducts = function () {
                $scope.treatProductList = [];
                $scope.unitProductList.map(function (product) {
                    if(product.inventoryTracked === true){
                       $scope.treatProductList.push(product);
                    }
                });
            };

            $scope.setSelectedProduct = function (selectedProduct) {
                $scope.selectedProduct = selectedProduct;
            };

            $scope.setProductStatus = function (status) {
                var productIds = [], partnerIds = [], ret = false;
                $scope.channelPartners.map(function (partner) {
                    if (partner.selected === true) {
                        partnerIds.push(partner.id);
                    }
                });
                $scope.unitProductList.map(function (product) {
                    if (product.selected === true) {
                        productIds.push(product.detail.id);
                    }
                });
                if (productIds.length === 0) {
                    bootbox.alert("Please select at least one product!");
                    ret = true;
                    return;
                } else if ($scope.selectedUnit == null) {
                    bootbox.alert("Please select unit!");
                    ret = true;
                    return;
                } else if (partnerIds.length === 0) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                if (!ret) {
                    var req = {
                        productIds: productIds,
                        partnerIds: partnerIds,
                        brandId: $scope.selectedBrand.brandId,
                        unitId: $scope.selectedUnit.id,
                        status: status
                    };
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.itemStockUpdate,
                        data: req
                    }).then(function success(response) {
                        if (response.status === 200 && response.data === true) {
                            bootbox.alert("Items status updated successfully!");
                            $scope.getUnitProducts();
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setZomatoTreatsItem = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setZomatoTreatsItem,
                    data: $scope.selectedProduct.detail.id
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data === true) {
                        $scope.partnerMenuCategories = response.data;
                        $scope.getZomatoTreatsItem();
                        bootbox.alert("Update request added to queue!");
                    } else {
                        bootbox.alert("Error processing request!");
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getZomatoTreatsItem = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getZomatoTreatsItem
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data != null) {
                        $scope.zomatoTreatsItem = response.data;
                    } else {
                        bootbox.alert("Error getting treats item!");
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.removeZomatoTreatsItem = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.removeZomatoTreatsItem
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data != null) {
                        $scope.zomatoTreatsItem = response.data;
                        $scope.getZomatoTreatsItem();
                    } else {
                        bootbox.alert("Error getting treats item!");
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getLogisticsStatus = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getOutletLogisticsStatus,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data != null) {
                        $scope.zomatoUnitLogisticsStatus = response.data;
                    } else {
                        bootbox.alert("Error getting logistics status!");
                    }
                    $scope.showUpdateLogisticsStatus = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setSelfDeliveryStatus = function (status) {
                if ($scope.logisticsChangeRequest == null) {
                    $scope.logisticsChangeRequest = {}
                }
                $scope.logisticsChangeRequest.self_delivery_serviceability_status = status;
            };

            $scope.updateLogisticsStatus = function () {
                $rootScope.showFullScreenLoader = true;
                $scope.logisticsChangeRequest.outlet_id = $scope.selectedUnit.id;
                if ($scope.selectedUnit == null) {
                    bootbox.alert("Please select unit.")
                } else if ($scope.logisticsChangeRequest.self_delivery_serviceability_status == null) {
                    bootbox.alert("Please select logisitics status.");
                } else {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.updateOutletLogisticsStatus,
                        data: $scope.logisticsChangeRequest
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.status === 200 && response.data != null) {
                            bootbox.alert(response.data.message);
                            $scope.getLogisticsStatus();
                        } else {
                            bootbox.alert("Error getting logistics status!");
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.getDeliveryStatus = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getOutletDeliveryStatus,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data != null) {
                        $scope.zomatoUnitDeliveryStatus = response.data;
                    } else {
                        bootbox.alert("Error getting delivery status!");
                    }
                    $scope.showUpdateDeliveryStatus = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setOutletDeliveryStatus = function (status) {
                $scope.outlet_delivery_status = status === true ? 1 : 0;
            };

            $scope.updateDeliveryStatus = function () {
                $rootScope.showFullScreenLoader = true;
                if ($scope.selectedUnit == null) {
                    bootbox.alert("Please select unit.")
                } else if ($scope.outlet_delivery_status) {
                    bootbox.alert("Please select delivery status.");
                } else {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.updateOutletDeliveryStatus,
                        data: {
                            outlet_id: $scope.selectedUnit.id,
                            outlet_delivery_status: $scope.outlet_delivery_status === true ? 1 : 0,
                            outlet_delivery_status_update_reason: $scope.outlet_delivery_status_update_reason
                        }
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.status === 200 && response.data != null) {
                            bootbox.alert(response.data.message);
                            $scope.getDeliveryStatus();
                        } else {
                            bootbox.alert("Error getting logistics status!");
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.getTakeawayStatus = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getOutletTakeawayStatus,
                    data: $scope.selectedUnit.id
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data != null) {
                        $scope.zomatoUnitTakeawayStatus = response.data;
                    } else {
                        bootbox.alert("Error getting takeaway status!");
                    }
                    $scope.showUpdateDeliveryStatus = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getPartnerTags = function () {
                if($scope.selectedPartner != null && $scope.selectedPartner.id != null) {
                    $scope.showProductTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductTags,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            if(response.data.length > 0) {
                                $scope.partnerTags = response.data;
                            }
                            $scope.showTagsView = true;
                        }
                    }, function error(error) {
                        bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                    });
                } else {
                    bootbox.alert("Please select channel partner first.");
                }

            };

            $scope.addPartnerTags = function (id, name) {
                console.log($scope.newTag);
                $scope.showProductTagMappingView = false;
                var found = false;
                for(var i=0; i<$scope.partnerTags.length; i++) {
                    if($scope.partnerTags[i].id == $scope.newTag.id) {
                        bootbox.alert("Tag already added.");
                        found = true;
                        break;
                    }
                }
                if(!found) {
                    $scope.partnerTags.push($scope.newTag);
                    $scope.newTag = {id:null, name:null,group:null};
                }
            };

            $scope.removePartnerTags = function (tagId) {
                var tags = [];
                $scope.partnerTags.map(function (tag) {
                    if(tag.id != tagId) {
                        tags.push(tag);
                    }
                });
                $scope.partnerTags = tags;
                $scope.showProductTagMappingView = false;
            };

            $scope.setPartnerTags = function () {
                $scope.showProductTagMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductTags,
                    data:{partnerId: $scope.selectedPartner.id, tags:$scope.partnerTags}
                }).then(function success(response) {
                    if(response.status == 200 && response.data == true) {
                        bootbox.alert("Tags updated successfully.");
                    }
                }, function error(error) {
                    bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                });
            };

            $scope.getActiveProducts = function () {
                if($scope.partnerTags.length > 0) {
                    $rootScope.showFullScreenLoader = true;
                    $scope.showProductTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.productsActive,
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            var products = [];
                            response.data.map(function (product) {
                                if(product.classification == "MENU"){
                                    products.push(product);
                                }
                            });
                            $scope.getActiveProductsWithTags(products);
                        }
                    }, function error(error) {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error getting active products list.");
                    });
                } else {
                    bootbox.alert("Please load tags first");
                }

            };

            $scope.getActiveProductsWithTags = function (productList) {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else {
                    $scope.showProductTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductTagsMappings +
                        "?partnerId="+$scope.selectedPartner.id+"&brandId="+$scope.selectedBrand.brandId,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            console.log(response.data);
                            response.data.map(function (productTags) {
                                productList.map(function (product) {
                                    if(product.id == productTags.productId){
                                        product.tags = productTags.tags;
                                    }
                                })
                            });
                            $scope.productsTagsMappings = productList;
                            $scope.showProductTagMappingView = true;
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting product tags mappings");
                    });
                }
            };

            $scope.updateTagMappings = function (mapping) {
                var tags = angular.copy($scope.partnerTags);
                tags.map(function (tag) {
                    if(mapping.tags != null) {
                        mapping.tags.map(function (addedTag) {
                            if(addedTag.id == tag.id) {
                                tag.selected = true;
                            }
                        });
                    }
                });
                $scope.updateTagObject = {product : {id:mapping.id, name:mapping.name}};
                $scope.updateTagObject.tags = tags;
                $("#productTagMappingModal").modal("show");
            };

            $scope.selectUniqueTag = function (tag) {
                for(var i=0;i< $scope.updateServingInfoObject.length;i++){
                    console.log($scope.updateServingInfoObject.tags[i]);
                    if($scope.updateServingInfoObject.tags[i]!==tag){
                        $scope.updateServingInfoObject.tags[i].selected=false;
                        console.log($scope.updateServingInfoObject.tags[i]);
                    }
                }
                $scope.selectTag(tag);
            };
            $scope.selectTag = function (tag) {
                if(tag.selected == true) {
                    tag.selected = false;
                } else {
                    tag.selected = true;
                }
            };

            $scope.setProductTagMapping = function () {
                $scope.productsTagsMappings.map(function (mapping) {
                    if(mapping.id == $scope.updateTagObject.product.id){
                        mapping.tags = [];
                        $scope.updateTagObject.tags.map(function (tag) {
                            if(tag.selected == true) {
                                mapping.tags.push(tag);
                            }
                        })
                    }
                });
                $("#productTagMappingModal").modal("hide");
            };

            $scope.submitProductTagMappings = function () {
                var req = {partnerId:$scope.selectedPartner.id, mappings:{brandId:$scope.selectedBrand.brandId, mappings:[]}};
                $scope.productsTagsMappings.map(function (mapping) {
                    if(mapping.tags != null && mapping.tags.length > 0) {
                        var tagMapping = {productId: mapping.id, tags: []};
                        mapping.tags.map(function (tag) {
                            tagMapping.tags.push({id:tag.id, name:tag.name});
                        });
                        req.mappings.mappings.push(tagMapping)
                    }
                });
                $scope.showProductTagMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductTagsMappings,
                    data: req
                }).then(function success(response) {
                    if(response.status == 200 && response.data != null) {
                        console.log(response.data);
                        if(response.data == true) {
                            $scope.getActiveProducts();
                        } else {
                            bootbox.alert("Error updating product tag mappings");
                        }
                    }
                }, function error(error) {
                    bootbox.alert("Error updating product tags mappings");
                });
            };
            
            $scope.getSwiggyStockStatus = function() {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.partnerMetadata.getSwiggyStockStatus
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.stockCheck = response.data;
                        } else {
                            bootbox.alert("Error getting Swiggy Stock Status.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            
            $scope.updateStockStatus = function(check) {
            	$http({
            	 method:"GET",
                 url: AppUtil.restUrls.partnerMetadata.updateSwiggyStockStatus,
                 params:{
                     status: check
                 }
            	 }).then(function success(response) {
                     if (response.status === 200 && response.data != null && response.data) {
                    	 $scope.stockCheck = check;
                    	 bootbox.alert("Swiggy stock code version updated successfully.");
                     } else {
                         bootbox.alert("Error getting Swiggy Stock Status.");
                     }
                 }, function error(response) {
                     console.log("error:" + response);
                     $rootScope.showFullScreenLoader = false;
                 });
            }



            $scope.getMeatTags = function () {
                if($scope.selectedPartner != null && $scope.selectedPartner.id != null) {
                    $scope.showMeatTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerMeatTags,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            if(response.data.length > 0) {
                                $scope.meatTags = response.data;
                                console.log($scope.meatTags);
                            }
                            $scope.showMeatTagsView = true;
                        }
                    }, function error(error) {
                        bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                    });
                } else {
                    bootbox.alert("Please select channel partner first.");
                }

            };


            $scope.addMeatTags = function (id, name) {
                console.log($scope.newMeatTag);
                $scope.showMeatTagMappingView = false;
                var found = false;
                for(var i=0; i<$scope.meatTags.length; i++) {
                    if($scope.meatTags[i].id == $scope.newMeatTag.id) {
                        bootbox.alert("Tag already added.");
                        found = true;
                        break;
                    }
                }
                if(!found) {
                    $scope.meatTags.push($scope.newMeatTag);
                    $scope.newMeatTag = {id:null, name:null};
                }
            };


            $scope.removeMeatTags = function (tagId) {
                var tags = [];
                $scope.meatTags.map(function (tag) {
                    if(tag.id != tagId) {
                        tags.push(tag);
                    }
                });
                $scope.meatTags = tags;
                $scope.showMeatTagMappingView = false;
            };


            $scope.setMeatTags = function () {
                $scope.showMeatTagMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerMeatTags,
                    data:{partnerId: $scope.selectedPartner.id, tags:$scope.meatTags}
                }).then(function success(response) {
                    if(response.status == 200 && response.data == true) {
                        console.log(response);
                        bootbox.alert("Tags updated successfully.");
                    }
                }, function error(error) {
                    bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                });
            };


            $scope.getActiveProductsMeat = function () {
                if($scope.meatTags.length > 0) {
                    $rootScope.showFullScreenLoader = true;
                    $scope.showMeatTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.productsActive,
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            var products = [];
                            response.data.map(function (product) {
                                if(product.classification == "MENU" && product.attribute === 'NON_VEG'){
                                    products.push(product);
                                }
                            });
                            $scope.getActiveProductsWithMeatTags(products);
                        }
                    }, function error(error) {
                        console.log(error);
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error getting active products list.");
                    });
                } else {
                    bootbox.alert("Please load tags first");
                }

            };
            $scope.getActiveProductsAllergen = function () {
                if($scope.allergenTags.length > 0) {
                    $rootScope.showFullScreenLoader = true;
                    $scope.showAllergenTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.productsActive,
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            var products = [];
                            response.data.map(function (product) {
                                if(product.classification == "MENU"){
                                    products.push(product);
                                }
                            });
                            $scope.getActiveProductsWithAllergenTags(products);
                        }
                    }, function error(error) {
                        console.log(error);
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error getting active products list.");
                    });
                } else {
                    bootbox.alert("Please load tags first");
                }

            };


            $scope.submitMeatTagMappings = function () {
                var req = {partnerId:$scope.selectedPartner.id, mappings:{brandId:$scope.selectedBrand.brandId, mappings:[]}};
                $scope.meatTagsMappings.map(function (mapping) {
                    if(mapping.tags != null && mapping.tags.length > 0) {
                        var tagMapping = {productId: mapping.id, tags: []};
                        mapping.tags.map(function (tag) {
                            tagMapping.tags.push({id:tag.id, name:tag.name});
                        });
                        req.mappings.mappings.push(tagMapping)
                    }
                });
                $scope.showMeatTagMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductMeatTagsMappings,
                    data: req
                }).then(function success(response) {
                    if(response.status == 200 && response.data != null) {
                        console.log(response.data);
                        if(response.data == true) {
                            $scope.getActiveProductsMeat();
                        } else {
                            bootbox.alert("Error updating product tag mappings");
                        }
                    }
                }, function error(error) {
                    bootbox.alert("Error updating product tags mappings");
                });
            };
            $scope.submitAllergenTagMappings = function () {
                var req = {partnerId:$scope.selectedPartner.id, mappings:{brandId:$scope.selectedBrand.brandId, mappings:[]}};
                $scope.allergenTagsMappings.map(function (mapping) {
                    if(mapping.tags != null && mapping.tags.length > 0) {
                        var tagMapping = {productId: mapping.id, tags: []};
                        mapping.tags.map(function (tag) {
                            tagMapping.tags.push({id:tag.id, name:tag.name});
                        });
                        req.mappings.mappings.push(tagMapping)
                    }
                });
                $scope.showAllergenTagMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductAllergenTagsMappings,
                    data: req
                }).then(function success(response) {
                    if(response.status == 200 && response.data != null) {
                        console.log(response.data);
                        if(response.data == true) {
                            $scope.getActiveProductsAllergen();
                        } else {
                            bootbox.alert("Error updating product tag mappings");
                        }
                    }
                }, function error(error) {
                    bootbox.alert("Error updating product tags mappings");
                });
            };



            $scope.updateMeatTagMappings = function (mapping) {
                var tags = angular.copy($scope.meatTags);
                tags.map(function (tag) {
                    if(mapping.tags != null) {
                        mapping.tags.map(function (addedTag) {
                            if(addedTag.id == tag.id) {
                                tag.selected = true;
                            }
                        });
                    }
                });
                $scope.updateMeatTagObject = {product : {id:mapping.id, name:mapping.name}};
                console.log(mapping.name);
                $scope.updateMeatTagObject.tags = tags;
                console.log($scope.updateMeatTagObject.tags);

                $("#productMeatTagMappingModal").modal("show");
            };

            $scope.updateAllergenTagMappings = function (mapping) {
                var tags = angular.copy($scope.allergenTags);
                tags.map(function (tag) {
                    if(mapping.tags != null) {
                        mapping.tags.map(function (addedTag) {
                            if(addedTag.id == tag.id) {
                                tag.selected = true;
                            }
                        });
                    }
                });
                $scope.updateAllergenTagObject = {product : {id:mapping.id, name:mapping.name}};
                $scope.updateAllergenTagObject.tags = tags;
                $("#productAllergenTagMappingModal").modal("show");
            };


            $scope.setProductMeatTagMapping = function () {
                $scope.meatTagsMappings.map(function (mapping) {
                    if(mapping.id == $scope.updateMeatTagObject.product.id){
                        mapping.tags = [];
                        console.log("1282");
                        console.log($scope.updateMeatTagObject);
                        $scope.updateMeatTagObject.tags.map(function (tag) {
                            if(tag.selected == true) {
                                mapping.tags.push(tag);
                            }
                        })
                    }
                });
                $("#productMeatTagMappingModal").modal("hide");
            };
            $scope.setProductAllergenTagMapping = function () {
                $scope.allergenTagsMappings.map(function (mapping) {
                    if(mapping.id == $scope.updateAllergenTagObject.product.id){
                        mapping.tags = [];
                        $scope.updateAllergenTagObject.tags.map(function (tag) {
                            if(tag.selected == true) {
                                console.log("1264");
                                console.log(tag);
                                mapping.tags.push(tag);
                            }
                        })
                    }
                });
                $("#productAllergenTagMappingModal").modal("hide");
            };

            $scope.getActiveProductsWithMeatTags = function (productList) {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else {
                    $scope.showMeatTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductMeatTagsMappings +
                        "?partnerId="+$scope.selectedPartner.id+"&brandId="+$scope.selectedBrand.brandId,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            console.log(response.data);
                            response.data.map(function (productTags) {
                                productList.map(function (product) {
                                    if(product.id == productTags.productId){
                                        product.tags = productTags.tags;
                                    }
                                })
                            });
                            $scope.meatTagsMappings = productList;
                            $scope.showMeatTagMappingView = true;
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting product tags mappings");
                    });
                }
            };
            $scope.getActiveProductsWithAllergenTags = function (productList) {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else {
                    $scope.showAllergenTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductAllergenTagsMappings +
                        "?partnerId="+$scope.selectedPartner.id+"&brandId="+$scope.selectedBrand.brandId,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            console.log(response.data);
                            response.data.map(function (productTags) {
                                productList.map(function (product) {
                                    if(product.id == productTags.productId){
                                        product.tags = productTags.tags;
                                    }
                                })
                            });
                            $scope.allergenTagsMappings = productList;
                            $scope.showAllergenTagMappingView = true;
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting product tags mappings");
                    });
                }
            };

            $scope.getAllergenTags = function () {
                console.log("partnerManagementCtrl 1224");
                if($scope.selectedPartner != null && $scope.selectedPartner.id != null) {
                    $scope.showAllergenTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerAllergenTags,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            if(response.data.length > 0) {
                                $scope.allergenTags = response.data;
                                console.log($scope.allergenTags);
                            }
                            $scope.showAllergenTagsView = true;
                        }
                    }, function error(error) {
                        bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                    });
                } else {
                    bootbox.alert("Please select channel partner first.");
                }

            };


            $scope.addAllergenTags = function (id, name) {
                console.log($scope.newAllergenTag);
                $scope.showAllergenTagMappingView = false;
                var found = false;
                for(var i=0; i<$scope.allergenTags.length; i++) {
                    if($scope.allergenTags[i].id == $scope.allergenTags.id) {
                        bootbox.alert("Tag already added.");
                        found = true;
                        break;
                    }
                }
                if(!found) {
                    $scope.allergenTags.push($scope.newAllergenTag);
                    $scope.newAllergenTag = {id:null, name:null};
                }
            };


            $scope.removeAllergenTags = function (tagId) {
                var tags = [];
                $scope.allergenTags.map(function (tag) {
                    if(tag.id != tagId) {
                        tags.push(tag);
                    }
                });
                $scope.allergenTags = tags;
                $scope.showAllergenTagMappingView = false;
            };


            $scope.setAllergenTags = function () {
                $scope.showAllergenTagMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerAllergenTags,
                    data:{partnerId: $scope.selectedPartner.id, tags:$scope.allergenTags}
                }).then(function success(response) {
                    if(response.status == 200 && response.data == true) {
                        console.log(response);
                        bootbox.alert("Tags updated successfully.");
                    }
                }, function error(error) {
                    bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                });
            };

            $scope.getActiveProductsServingInfo = function () {
                if($scope.servingInfo.length > 0) {
                    $rootScope.showFullScreenLoader = true;
                    $scope.showServingInfoMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.productsActive,
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            var products = [];
                            response.data.map(function (product) {
                                if(product.classification == "MENU"){
                                    products.push(product);
                                }
                            });
                            $scope.getActiveProductsWithServingInfo(products);
                        }
                    }, function error(error) {
                        console.log(error);
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error getting active products list.");
                    });
                } else {
                    bootbox.alert("Please load tags first");
                }

            };

            $scope.submitServingInfoMappings = function () {
                var req = {partnerId:$scope.selectedPartner.id, mappings:{brandId:$scope.selectedBrand.brandId, mappings:[]}};
                $scope.servingInfoTagsMappings.map(function (mapping) {
                    if(mapping.tags != null && mapping.tags.length > 0) {
                        var tagMapping = {productId: mapping.id, tags: []};
                        mapping.tags.map(function (tag) {
                            tagMapping.tags.push({id:tag.id, name:tag.name});
                        });
                        req.mappings.mappings.push(tagMapping)
                    }
                });
                $scope.showServingInfoMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductServingInfoTagsMappings,
                    data: req
                }).then(function success(response) {
                    if(response.status == 200 && response.data != null) {
                        console.log(response.data);
                        if(response.data == true) {
                            $scope.getActiveProductsServingInfo();
                        } else {
                            bootbox.alert("Error updating product tag mappings");
                        }
                    }
                }, function error(error) {
                    bootbox.alert("Error updating product tags mappings");
                });
            };

            $scope.updateServingInfoMapping = function (mapping) {
                var tags = angular.copy($scope.servingInfo);
                tags.map(function (tag) {
                    if(mapping.tags != null) {
                        mapping.tags.map(function (addedTag) {
                            // if($scope.selectedTag==true){
                            //     bootbox.alert("Cannot select more than one option");
                            // }
                            // else{
                                if(addedTag.id == tag.id) {
                                    tag.selected = true;
                                    // $scope.selectedTag=true;
                                }
                            // }
                        });
                    }
                });
                $scope.updateServingInfoObject = {product : {id:mapping.id, name:mapping.name}};
                $scope.updateServingInfoObject.tags = tags;
                // $scope.selectedTag=false;
                $("#productServingInfoMappingModal").modal("show");
            };

            $scope.setProductServingInfoMapping = function () {
                $scope.servingInfoTagsMappings.map(function (mapping) {
                    if(mapping.id == $scope.updateServingInfoObject.product.id){
                        mapping.tags = [];
                        var countNum=0;
                        $scope.updateServingInfoObject.tags.map(function (tag) {
                            if(tag.selected == true) {
                                countNum=countNum+1;
                            }
                            });
                   if(countNum>1){
                       bootbox.alert("Cannot select multiple options");
                   }
                   else{
                       $scope.updateServingInfoObject.tags.map(function (tag) {
                           if($scope.selectedTag==false){
                               if(tag.selected == true) {
                                   console.log("1264");
                                   console.log(tag);
                                   mapping.tags.push(tag);
                                   $scope.selectedTag=true;
                               }
                           }

                       })
                   }

                    }
                });
                $scope.selectedTag=false;
                $("#productServingInfoMappingModal").modal("hide");
            };

            $scope.getActiveProductsWithServingInfo = function (productList) {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else {
                    $scope.showServingInfoMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductServingInfoTagsMappings +
                        "?partnerId="+$scope.selectedPartner.id+"&brandId="+$scope.selectedBrand.brandId,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            console.log(response.data);
                            response.data.map(function (productTags) {
                                console.log(productTags);
                                productList.map(function (product) {
                                    if(product.id == productTags.productId){
                                        product.tags = productTags.tags;
                                        console.log(product);
                                    }
                                })
                            });
                            $scope.servingInfoTagsMappings = productList;
                            console.log($scope.servingInfoTagsMappings);
                            $scope.showServingInfoMappingView = true;
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting product tags mappings");
                    });
                }
            };

            $scope.getServingInfo = function () {
                console.log("partnerManagementCtrl 1224");
                if($scope.selectedPartner != null && $scope.selectedPartner.id != null) {
                    $scope.showServingInfoMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerServingInfo,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            if(response.data.length > 0) {
                                $scope.servingInfo = response.data;
                                console.log($scope.servingInfo);
                            }
                            $scope.showServingInfoView = true;
                        }
                    }, function error(error) {
                        bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                    });
                } else {
                    bootbox.alert("Please select channel partner first.");
                }

            };


            $scope.addServingInfo = function (id, name) {
                console.log($scope.newServingInfo);
                $scope.showServingInfoMappingView = false;
                var found = false;
                for(var i=0; i<$scope.servingInfo.length; i++) {
                    if($scope.servingInfo[i].id == $scope.servingInfo.id) {
                        bootbox.alert("Tag already added.");
                        found = true;
                        break;
                    }
                }
                if(!found) {
                    $scope.servingInfo.push($scope.newServingInfo);
                    $scope.newServingInfo = {id:null, name:null};
                }
            };


            $scope.removeServingInfo = function (tagId) {
                var tags = [];
                $scope.servingInfo.map(function (tag) {
                    if(tag.id != tagId) {
                        tags.push(tag);
                    }
                });
                $scope.servingInfo = tags;
                $scope.showServingInfoMappingView = false;
            };


            $scope.setServingInfo = function () {
                $scope.showServingInfoMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerServingInfo,
                    data:{partnerId: $scope.selectedPartner.id, tags:$scope.servingInfo}
                }).then(function success(response) {
                    if(response.status == 200 && response.data == true) {
                        console.log(response);
                        bootbox.alert("Tags updated successfully.");
                    }
                }, function error(error) {
                    bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                });
            };

            $scope.getActiveProductsServingSize = function () {
                console.log("1649");
                if($scope.servingSize.length > 0) {
                    console.log("1651");
                    $rootScope.showFullScreenLoader = true;
                    $scope.showServingSizeMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.productsActive,
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            var products = [];
                            console.log(response);
                            response.data.map(function (product) {
                                if(product.classification == "MENU"){
                                    products.push(product);
                                }
                            });
                            console.log(products);
                            $scope.getActiveProductsWithServingSize(products);
                        }
                    }, function error(error) {
                        console.log(error);
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error getting active products list.");
                    });
                } else {
                    bootbox.alert("Please load tags first");
                }

            };

            $scope.submitServingSizeMappings = function () {
                var req = {partnerId:$scope.selectedPartner.id, mappings:{brandId:$scope.selectedBrand.brandId, mappingsVU:[]}};
                $scope.servingSizeMappings.map(function (mapping) {
                    if(mapping.tags != null) {
                        var tagMapping = {productId: mapping.id, tags: {}};
                        tagMapping.tags=mapping.tags;
                        console.log(tagMapping);
                        req.mappings.mappingsVU.push(tagMapping)
                        console.log(req.mappings.mappingsVU);
                    }
                });
                $scope.showServingSizeMappingView = false;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerProductServingSizeTagsMappings,
                    data: req
                }).then(function success(response) {
                    if(response.status == 200 && response.data != null) {
                        console.log(response.data);
                        if(response.data == true) {
                            $scope.getActiveProductsServingSize();
                        } else {
                            bootbox.alert("Error updating product tag mappings");
                        }
                    }
                }, function error(error) {
                    bootbox.alert("Error updating product tags mappings");
                });
            };

            $scope.updateServingSizeMappings = function (mapping) {
                var tags = angular.copy($scope.servingSize);
                tags.map(function (tag) {
                    if(mapping.tags != null ) {
                    if(mapping.tags.id==tag.id){
                        tag.selected=true;
                    }
                    }
                });
                $scope.updateServingSizeObject = {product : {id:mapping.id, name:mapping.name}};
                $scope.updateServingSizeObject.tags = tags;
                console.log($scope.updateServingSizeObject.tags);
                $("#productServingSizeMappingModal").modal("show");
            };

            $scope.setProductServingSizeMapping = function () {
                $scope.servingSizeMappings.map(function (mapping) {
                    if(mapping.id == $scope.updateServingSizeObject.product.id) {
                        mapping.tags = {};
                        console.log("1730");
                        console.log($scope.updateServingSizeObject);
                        var countNumServingSize = 0;
                        $scope.updateServingSizeObject.tags.map(function (tag) {
                            if (tag.selected == true) {
                                countNumServingSize = countNumServingSize + 1;
                            }
                        });
                        if (countNumServingSize > 1) {
                            bootbox.alert("Cannot select multiple options");
                        }
                        else {
                            $scope.updateServingSizeObject.tags.map(function (tag) {
                                if (tag.selected == true) {
                                    console.log("1264");
                                    console.log(tag);
                                    mapping.tags=tag;
                                    console.log(mapping);
                                }
                            })
                        }
                    }

                });
                $("#productServingSizeMappingModal").modal("hide");
            };

            $scope.getActiveProductsWithServingSize = function (productList) {
                console.log("1742");
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    $rootScope.showFullScreenLoader = false;
                    return;
                } else {
                    $scope.showServingSizeMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerProductServingSizeTagsMappings +
                        "?partnerId="+$scope.selectedPartner.id+"&brandId="+$scope.selectedBrand.brandId,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            console.log(response);
                            response.data.map(function (productTags) {
                                productList.map(function (product) {
                                    // console.log(product);
                                    if(product.id == productTags.productId){
                                        product.tags = productTags.tags;
                                        console.log(product.tags);
                                    }
                                })
                            });
                            console.log("1776" + productList);
                            $scope.servingSizeMappings = productList;
                            $scope.showServingSizeMappingView = true;
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting product tags mappings");
                    });
                }
            };

            $scope.getServingSizes = function () {
                console.log("partnerManagementCtrl 1224");
                if($scope.selectedPartner != null && $scope.selectedPartner.id != null) {
                    $scope.showServingSizeMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerMetadata.getPartnerServingSize,
                        data: $scope.selectedPartner.id
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            if(response.data.length > 0) {
                                console.log(response.data);
                                $scope.servingSize = response.data;
                                console.log($scope.servingSize);
                            }
                            $scope.showServingSizeView = true;
                        }
                    }, function error(error) {
                        bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                    });
                } else {
                    bootbox.alert("Please select channel partner first.");
                }

            };

            $scope.addServingSizes = function (id, value, unit) {
                console.log($scope.newServingSize);
                $scope.showServingSizeMappingView = false;
                var found = false;
                for(var i=0; i<$scope.servingSize.length; i++) {
                    if($scope.servingSize[i].id == $scope.newServingSize.id) {
                        bootbox.alert("Tag already added.");
                        found = true;
                        break;
                    }
                }
                if(!found) {
                    $scope.servingSize.push($scope.newServingSize);
                     console.log($scope.servingSize);
                    $scope.newAllergenTag = {id:null, value:null, unit:null};
                }
            };

            $scope.removeServingSizes = function (tagId) {
                var tags = [];
                $scope.servingSize.map(function (tag) {
                    if(tag.id != tagId) {
                        tags.push(tag);
                    }
                });
                $scope.servingSize = tags;
                $scope.showServingSizeMappingView = false;
            };

            $scope.setServingSizes = function () {
                $scope.showServingSizeMappingView = false;
                console.log($scope.servingSize);
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setPartnerServingSize,
                    data:{partnerId: $scope.selectedPartner.id, tags:$scope.servingSize}
                }).then(function success(response) {
                    if(response.status == 200 && response.data == true) {
                        console.log(response);
                        bootbox.alert("Tags updated successfully.");
                    }
                }, function error(error) {
                    bootbox.alert("Error refreshing channel partner unit partner mapping cache");
                });
            };


            $scope.getActiveProductsDTDO = function () {
                // if($scope.partnerTags.length > 0) {
                    $rootScope.showFullScreenLoader = true;
                    // $scope.showProductTagMappingView = false;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.productMetaData.productsActive,
                    }).then(function success(response) {
                        if(response.status == 200 && response.data != null) {
                            var products = [];
                            $scope.showUnitProducts = true;
                            console.log(response);
                            var index=0;
                            response.data.map(function (product) {
                                if(product.classification == "MENU"){
                                    // if(product.id==10 || product.id==670){
                                    //     $scope.selectedProductsDTDO.push(product);
                                    // }
                                    product.idx=index;
                                    products.push(product);
                                    index++;
                                }
                            });
                            // $scope.getActiveProductsWithTags(products);
                            $scope.productsDTDO=products;
                            // $scope.selectedProductsDTDO=products;
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(error) {
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Error getting active products list.");
                    });
                // } else {
                //     bootbox.alert("Please load tags first");
                // }

            };

            $scope.addProductsDOTD = function () {
                var partnerId = null, ret = false;

                console.log($scope.selectedDimensions);

                console.log($scope.selectedProduct);

                if ($scope.selectedProduct === null) {
                    bootbox.alert("Please select at least one product!");
                    ret = true;
                    return;
                }  else if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                else if($scope.selectedDimensions === null || $scope.selectedDimensions.length == 0){
                    bootbox.alert("Please select dimension with the product");
                    ret = true;
                    $scope.selectedProductsDTDO.map(function (value) {
                        if (value.dotdProductDimensionsList === null || value.dotdProductDimensionsList.length === 0) {

                            $scope.selectedProductsDTDO.splice($scope.selectedProductsDTDO.indexOf(value.dimensionId),1);

                        }
                    })
                    return;

                }
                $scope.selectedProduct.dotdProductDimensionsList=$scope.selectedDimensions;

                console.log($scope.selectedProductsDTDO);

               if($scope.selectedProduct.id){
                   $scope.changeKeyName($scope.selectedProduct,"id","productId");
                   $scope.changeKeyName($scope.selectedProduct,"name","productName");
               }


                console.log($scope.selectedProduct);
                if (!ret) {
                    if($scope.selectedProductsDTDO){
                        $scope.selectedProductsDTDO.map(function (value) {
                            if(value.productId === $scope.selectedProduct.productId){
                                $scope.selectedProductsDTDO.splice($scope.selectedProductsDTDO.indexOf(value),1);
                            }
                        })

                    }

                    productIdsForDOTD.push($scope.selectedProduct.productId);

                    console.log($scope.selectedProductsDTDO);
                    $scope.selectedProductsDTDO.push($scope.selectedProduct);
                    console.log($scope.selectedProductsDTDO);

                }



            };

            $scope.removeProductsDOTD =function (product) {
                console.log($scope.selectedProductsDTDO);

                var idx=$scope.selectedProductsDTDO.indexOf(product);
                $scope.selectedProductsDTDO.splice(idx,1);
                console.log($scope.selectedProductsDTDO);

            }

            $scope.selectType = function (product) {

                product.selected=true;
                for(var i=0;i<$scope.dimensionsInfo.length;i++){
                    if(product.dimensionProfileId==$scope.dimensionsInfo[i].detail.id){
                        $scope.productDimension=$scope.dimensionsInfo[i];
                        break;
                    }
                }
                console.log("clicked");
                console.log($scope.productDimension);
            }


            $scope.getAllDimensions = function () {

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.listTypes
                }).then(function success(response) {
                    $scope.dimensionsInfo = response.data.DIMENSION;
                    console.log($scope.dimensionsInfo);
                })

            }



            $scope.changeDimension = function () {
                console.log($scope.productsDTDO.id);
                for (var i = 0; i < $scope.productDimension.content.length; i++) {
                    if ($scope.productDimension.content[i].id == $scope.selectedProduct.dimensionProfileId) {
                        selectedDimension.push($scope.productDimension.content[i]);
                        break;
                    }
                }
                console.log(selectedDimension);

            };

            $scope.multiSelectSettings = {
                showEnableSearchButton: true, template: '<b> {{option.dimensionName}}</b>', scrollable: true,
                scrollableHeight: '200px',
                // changeDimension: $scope.changeDimension()
            };

            $scope.changeKeyName= function(value,oldKey,newKey){
                Object.defineProperty(value, newKey,
                    Object.getOwnPropertyDescriptor(value, oldKey));
                delete value[oldKey];
            }
            $scope.changeProduct = function (selectedProductId) {
                if (selectedProductId == null || selectedProductId === undefined) {
                    return false;
                }
                $scope.productDimension = {};
                $scope.productsDTDO.dimensionId = null;
                $scope.selectedRegion = null;
                selectedDimension = null;
                $scope.regions = [];
                for (var i = 0; i < $scope.productsDTDO.length; i++) {
                    if ($scope.productsDTDO[i].id == selectedProductId) {
                        $scope.selectedProduct = $scope.productsDTDO[i];
                        $scope.selectedProduct.productId=$scope.productsDTDO[i].id;
                        $scope.selectedProduct.productName=$scope.productsDTDO[i].name;
                        break;
                    }
                }

                console.log($scope.selectedProduct);

                for (var i = 0; i < $scope.dimensionsInfo.length; i++) {
                    if ($scope.dimensionsInfo[i].detail.id == $scope.selectedProduct.dimensionProfileId) {
                        $scope.productDimension = $scope.dimensionsInfo[i];
                        break;
                    }
                }

                $scope.selectedDimensions = [];
                console.log($scope.productDimension);
                $scope.productDimension.content.map(function(value){

                  $scope.changeKeyName(value,"id","dimensionId");
                  $scope.changeKeyName(value,"name","dimensionName");
                  $scope.changeKeyName(value,"code","dimensionCode");
                })
                console.log($scope.productDimension);
                $scope.selectedDimensions = [];
            };


            $scope.submitDOTDList = function(){

                var req = {
                    kettlePartnerId: $scope.selectedPartner.id,
                    partnerName: $scope.selectedPartner.name,
                    dotdProducts: $scope.selectedProductsDTDO,
                    productIds: productIdsForDOTD,
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.setDOTDProducts,
                    data: req,
                }).then(function success(response) {
                    if (response.status == 200) {
                        console.log(response);
                        $scope.productDimension = {};
                        bootbox.alert("DOTD Product List Updated Successfully!");
                        $scope.initDealOfTheDay();
                    }
                }, function error(error) {
                    bootbox.alert("Error setting DOTD Products");
                });
            }

            $scope.getDOTDList = function(){

                var ret = false;
                if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select at least one partner!");
                    ret = true;
                    return;
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand!");
                    ret = true;
                    return;
                }
                if (!ret) {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.partnerMetadata.getDOTDProducts + "?partnerId=" + $scope.selectedPartner.id,
                    }).then(function success(response) {
                        if (response.status == 200 && response.data != null) {
                            console.log(response);

                            $scope.selectedProductsDTDO = response.data.dotdProducts;
                            if ($scope.selectedProductsDTDO === undefined) {
                                $scope.selectedProductsDTDO = [];
                            }
                            console.log($scope.selectedProductsDTDO);
                            bootbox.alert("Fetched DOTD Products List!");
                        }
                    }, function error(error) {
                        bootbox.alert("Error getting DOTD Products");
                    });
                }

            }

        }]);
