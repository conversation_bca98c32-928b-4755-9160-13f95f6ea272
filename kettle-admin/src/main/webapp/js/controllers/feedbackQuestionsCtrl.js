adminapp.controller("feedbackQuestionsCtrl",['$scope', 'AppUtil', '$http', '$stateParams', 'fileService', '$cookieStore', '$rootScope',
    function ($scope, AppUtil, $http, $stateParams, fileService, $cookieStore, $rootScope) {

    // YES_NO, TEXT,MCQ

    $scope.init = function (){
         $scope.finalQuestionType = null;
         $scope.showTable = false;
         $scope.questionType=["YES_NO","TEXT","MCQ","ERQ"];
         $scope.unitStatuses = ["ACTIVE","IN_ACTIVE"];
         $scope.selectedStatus = "";
         $scope.selectQuestionType="";
         $scope.questionList = [];
         $scope.result = null;
         $scope.showOption = false;
         $scope.questionToAdd = null;
         $scope.optionsArray = [''];
         $scope.finalQuestionToAdd = null;
         $scope.currentQues = null;
         $scope.regionlist = [];
         $scope.selectedRegions = [];
         $scope.unitlist = [];
         $scope.unitDetailList =[];
         $scope.unitNameList = [];
         $scope.selectedUnitNames = [];
         $scope.questionMappingList = [];
         $scope.alreadyConsistUnits = [];
         $scope.questionReason = "";
         $scope.questionToUpdate = "";
         $scope.questionReasonToUpdate = "";
         $scope.optionsArrayNew = [''];
         $scope.updatedQuestionId = null;
         $scope.unitListByStatus = [];
         $scope.selectedUnitListByStatus = [];
         $scope.allowToChangeStatus = false;
         $scope.mappingIdstoChangeStatus = [];
    }

    $scope.getallActiveQuestions = function(){
        $scope.showTable = false;
        $scope.finalQuestionType = $scope.selectQuestionType;
        $rootScope.showFullScreenLoader = true;
        $scope.fetchQuestions($scope.finalQuestionType);

    }

    $scope.fetchQuestions = function(finalQuestionType){
        $http({
           method: 'GET',
           url: AppUtil.restUrls.feedbackQuestionManagement.getFeedbackQuestion +"/?questionType="+finalQuestionType
        }).then(function success(response) {
           $scope.result = response.data;
           $scope.showTable = true;
           $rootScope.showFullScreenLoader = false;
        }, function error(response) {
           bootbox.alert("Error while fetching feedbackQuestions")
           console.log("error: " + response);
        });
    }

    $scope.addNewQuestion=function (){
        $scope.selectQuestionType=""
        $scope.finalQuestionType = null;
        $scope.optionsArray = [''];
        $("#addNewQuestion").modal("show");
    }

    $scope.getSelectedQuestionType=function(){
        $scope.showOption = false;
        $scope.questionToAdd = null;
        $scope.finalQuestionToAdd = null;
        $scope.finalQuestionType = $scope.selectQuestionType;
        if($scope.finalQuestionType == "MCQ"){
            $scope.showOption = true;
        }
    }

    $scope.onQuestionInput=function(e){
       $scope.questionToAdd =e;
    }

    $scope.addOptions = function () {
        $scope.optionsArray.push('');
    }

    $scope.deleteOptions = function (index) {
        $scope.optionsArray.splice(index, 1);
    };

    $scope.refineMcqQuestion = function (ques){
        for(let i =0 ; i<$scope.optionsArray.length ;i++){
            if($scope.optionsArray[i]!==''){
                if(ques!=='' || ques!==null){
                    ques = ques + "@"
                }
                ques = ques + $scope.optionsArray[i]
            }
        }
        $scope.finalQuestionToAdd = ques;
    }

    $scope.addQuestion = function(){
        if($scope.selectQuestionType == "MCQ"){
            if($scope.optionsArray.length == 1 && $scope.optionsArray[0]==''){
                bootbox.alert("Please select Option for MCQ type question");
            }
            $scope.refineMcqQuestion($scope.questionToAdd);
        }else{
            $scope.finalQuestionToAdd = $scope.questionToAdd;
        }
        if($scope.selectQuestionType == null || $scope.selectQuestionType == ""){
            bootbox.alert("Question type can not be empty");
        }
        else if($scope.finalQuestionToAdd == "" ||  $scope.finalQuestionToAdd==null){
            bootbox.alert("Question can not be empty");
        }
        else{
            var feedbackQuesDomain = {
                question : $scope.finalQuestionToAdd,
                questionType : $scope.selectQuestionType,
                questionReason : $scope.questionReason,
                updatedBy : $rootScope.userData.id
            }
            $http({
               method: 'POST',
               url: AppUtil.restUrls.feedbackQuestionManagement.updateFeedbackQuestion,
               data : feedbackQuesDomain
            }).then(function success(response) {
               bootbox.alert("Question Added !");
               console.log("Question Added !")
               $scope.showOption = false;
               $("#addNewQuestion").modal("hide");
               $scope.init();
            }, function error(response) {
               window.alert("Error while Adding Question")
               console.log("error: " + response);
            });
        }
    }

    $scope.updateFeedbackQuestion = function(){
        if($scope.finalQuestionType == "MCQ"){
            if($scope.optionsArray.length == 1 && $scope.optionsArray[0]==''){
                bootbox.alert("Please select Option for MCQ type question");
            }
            $scope.refineMcqQuestion($scope.questionToUpdate);
        }else{
            $scope.finalQuestionToAdd = $scope.questionToUpdate;
        }
        var feedbackQuesDomain = {
            question : $scope.finalQuestionToAdd,
            questionType : $scope.finalQuestionType,
            questionId : $scope.updatedQuestionId,
            questionReason : $scope.questionReasonToUpdate,
            updatedBy : $rootScope.userData.id
        }
        $http({
           method: 'POST',
           url: AppUtil.restUrls.feedbackQuestionManagement.updateFeedbackQuestion,
           data : feedbackQuesDomain
        }).then(function success(response) {
           bootbox.alert("Question Updated !");
           console.log("Question Updated !")
           $scope.showOption = false;
           $("#updateQuestion").modal("hide");
           $scope.fetchQuestions(scope.finalQuestionType);
        }, function error(response) {
           window.alert("Error while updating Question")
           console.log("error: " + response);
        });
    }

    $scope.changeQuestionStatus = function(res){
        $http({
           method: 'POST',
           url: AppUtil.restUrls.feedbackQuestionManagement.changeQuestionStatus+"/?questionId="+res.id+
                                                                   "&updatedBy="+ $rootScope.userData.id
        }).then(function success(response) {
           bootbox.alert("Question Updated !");
           console.log("Question Updated !")
           $scope.fetchQuestions(res.questionType);
        }, function error(response) {
           window.alert("Error while updating Question")
           console.log("error: " + response);
        });
    }

    $scope.showMappings = function(res){
        $scope.currentQues = res;
        $scope.getQuestionMapping();
        $("#showMappings").modal("show");
    }

    $scope.updateMappings = function(){
        $scope.getRegionList();
        $scope.getUnitList();
        $("#showMappings").modal("hide");
        $("#updateMappings").modal("show");
    }

    $scope.getUnitList = function(){
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnitsList
        }).then(function success(response) {
            $scope.unitlist = response.data;
            for(var i=0;i<$scope.unitlist.length;i++){
                 if($scope.unitlist[i].status !=="ACTIVE"){
                     $scope.unitlist.splice(i,1);
                 }
            }
            console.log("allUnits=", $scope.unitlist);
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.getRegionList = function(){
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regionlist = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.multiSelectSettings = {
         template: '<b> {{option}}</b>',
         scrollableHeight : '300px',scrollable : true
    };

    $scope.getUnitDetails = function(){
        $scope.unitDetailList = [];
        for(var i=0;i<$scope.questionMappingList.length;i++){
            $scope.alreadyConsistUnits.push($scope.questionMappingList[i].unitName);
        }
        if($scope.selectedRegions.length === 0){
            $scope.unitDetailList = $scope.unitlist ;
        }
        else{
            for(var i=0;i<$scope.unitlist.length;i++){
                if($scope.selectedRegions.includes($scope.unitlist[i].region) && $scope.unitlist[i].status === 'ACTIVE'){
                    $scope.unitDetailList.push($scope.unitlist[i]);
                }
            }
        }
        var x = [];
        for(var i=0;i<$scope.unitDetailList.length;i++){
           if($scope.unitDetailList[i].category == "CAFE" && $scope.unitDetailList[i].status === 'ACTIVE'
                && !$scope.alreadyConsistUnits.includes($scope.unitDetailList[i].name)){
             x.push($scope.unitDetailList[i].name)
           }
        }
        $scope.unitNameList = x;
    }

    $scope.getQuestionMapping=function(){
        $http({
           method: 'GET',
           url: AppUtil.restUrls.feedbackQuestionManagement.getAllFeedbackUnitMapping +"/?questionId="+$scope.currentQues.id
        }).then(function success(response) {
           $scope.questionMappingList = response.data;
           console.log($scope.questionMappingList)
        }, function error(response) {
           bootbox.alert("Error while fetching feedbackQuestions")
           console.log("error: " + response);
        });
    }

    $scope.updateQuestionMapping = function(){
        console.log($scope.currentQues)
        var feedbackQuesMappingDomain = {
            questionId : $scope.currentQues.id,
            questionType : $scope.currentQues.questionType,
            unitName : $scope.selectedUnitNames,
            updateBy : $rootScope.userData.id
        }
        console.log(feedbackQuesMappingDomain);
        $http({
           method: 'POST',
           url: AppUtil.restUrls.feedbackQuestionManagement.addFeedbackUnitMapping,
           data : feedbackQuesMappingDomain
        }).then(function success(response) {
           $rootScope.showFullScreenLoader = false;
           console.log("Mapping Added Succesfully");
           $("#updateMappings").modal("hide");
           $scope.regionlist = [];
           $scope.selectedRegions = [];
           $scope.unitlist = [];
           $scope.unitDetailList =[];
           $scope.unitNameList = [];
           $scope.selectedUnitNames = [];
           $scope.alreadyConsistUnits = [];
           $scope.getQuestionMapping();
           $("#showMappings").modal("show");
           }, function error(response) {
           $rootScope.showFullScreenLoader = false;
           alert("Error while updating");
           console.log("error:" + response);
        });

    }

    $scope.changeStatusOfMapping = function(data){
        $http({
           method: 'POST',
           url: AppUtil.restUrls.feedbackQuestionManagement.updateFeedbackUnitMapping +"/?mappingId="+data.id+
                                                                                 "&updatedBy="+ $rootScope.userData.id
        }).then(function success(response) {
           if(data.mappingStatus == "ACTIVE"){
            bootbox.alert("Mapping status changed to Inactive Successfully !");
           }
           if(data.mappingStatus == "IN_ACTIVE"){
            bootbox.alert("Mapping status changed to active Successfully !");
           }
           $scope.getQuestionMapping();
        }, function error(response) {
           window.alert("Error while updating Mapping")
           console.log("error: " + response);
        });
    }

    $scope.extractOption = function(ques){
        var data  = ques.split('@');
        var question = data[0];
        $scope.optionsArray = data.slice(1);
        $scope.questionToUpdate = question;
    }

    $scope.updateQuestionModal = function(res){
        if(res.questionType == "MCQ"){
            $scope.extractOption(res.question);
            $scope.showOption = true;
        }else{
            $scope.questionToUpdate = res.question;
        }
        $scope.updatedQuestionId = res.id;
        $scope.questionReasonToUpdate = res.questionReason;
        $("#updateQuestion").modal("show");
    }

    $scope.onUpdateQuestionInput=function(e){
       $scope.questionToUpdate =e;
    }

    $scope.onUpdateQuestionReason=function(e){
        $scope.questionReasonToUpdate = e;
    }

    $scope.onReasonInput = function(e){
        $scope.questionReason = e;
    }

    $scope.bulkStatusModal = function(){
        $("#showMappings").modal("hide");
        $("#bulkStatusChange").modal("show");
    }

    $scope.getUnitsByStatus = function(){
        $scope.unitListByStatus = [];
        for(var i=0;i<$scope.questionMappingList.length;i++){
            if($scope.questionMappingList[i].mappingStatus==$scope.selectedStatus){
                $scope.unitListByStatus.push($scope.questionMappingList[i]);
            }
        }
        if($scope.unitListByStatus.length>0){
            $scope.allowToChangeStatus = true;
        }
    }

    $scope.multiSelectSettingsForStatusChange = {
       template: '<b> {{option.unitName}}</b>',
       scrollableHeight : '300px',scrollable : true
    };

    $scope.changeBulkStatus = function(){
        console.log($scope.selectedUnitListByStatus)
        $rootScope.showFullScreenLoader = true;
        for(var i=0;i<$scope.selectedUnitListByStatus.length;i++){
            $scope.mappingIdstoChangeStatus.push($scope.selectedUnitListByStatus[i].id)
        }
        if($scope.mappingIdstoChangeStatus.length<=0){
            $rootScope.showFullScreenLoader = false;
            bootbox.alert("Please Select Units to change status")
        }else{
            var feedbackQuesMappingDomain = {
               updateBy : $rootScope.userData.id,
               mappingIds : $scope.mappingIdstoChangeStatus
            }
            $http({
               method: 'POST',
               url: AppUtil.restUrls.feedbackQuestionManagement.bulkUpdateFeedbackUnitMapping,
               data : feedbackQuesMappingDomain
            }).then(function success(response) {
               $rootScope.showFullScreenLoader = false;
               if($scope.selectedStatus == "ACTIVE"){
                bootbox.alert("Mapping Status change to Inactive Successfully")
               }
               if($scope.selectedStatus == "IN_ACTIVE"){
                 bootbox.alert("Mapping Status change to Active Successfully")
               }
               $("#bulkStatusChange").modal("hide");
               $scope.selectedStatus = "";
               $scope.unitListByStatus = [];
               $scope.selectedUnitListByStatus = [];
               $scope.allowToChangeStatus = false;
               $scope.mappingIdstoChangeStatus = [];
               $scope.getQuestionMapping();
               $("#showMappings").modal("show");
               }, function error(response) {
               $rootScope.showFullScreenLoader = false;
               alert("Error while changing status");
               console.log("error:" + response);
            });
        }
    }


}])