/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
adminapp.controller("productWebTagsctrl", function ($scope, $http, $location, $window, $rootScope, $timeout, AuthService, AppUtil) {

    $scope.filterProductTagList = [];

    $scope.init = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.fullTagsData = [];
        $http({
            url: AppUtil.restUrls.productMetaData.activeWebProducts,
            dataType: 'json',
            method: 'GET',
            data: '',
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(resp) {
            $scope.productDetails = resp.data;
            $scope.productDetails.forEach(function (filterProduct) {
                $scope.filterProductTagList.push({
                    id: filterProduct.detail.id,
                    text: filterProduct.detail.name,
                    status: filterProduct.status
                });
            });
            //console.log("ProductList-",$scope.productDetails);
            $http({
                url: AppUtil.restUrls.neoData.getTags,
                method: "POST",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                },
            }).then(function success(response) {
                $scope.webTags = response.data;
                $rootScope.showFullScreenLoader = false;
                $scope.webTags.forEach(function (ListTags) {
                    var productNamesArr = [];
                    ListTags.values.forEach(function (arrayListData) {
                        $scope.productDetails.forEach(function (filterProduct) {
                            if (arrayListData == filterProduct.detail.id) {
                                productNamesArr.push({
                                    id: arrayListData,
                                    name: filterProduct.detail.name
                                });
                            }
                        });
                    });
                    ListTags.products = productNamesArr;
                    if(ListTags.tagColor != undefined && ListTags.tagColor != null){
                        $scope.selectedTagColor[ListTags.name] = ListTags.tagColor;
                    }
                });
            }, function error(response) {
                console.log("error:" + response);
            });
        }, function error(response) {
            console.log("error:" + response);
        });
        $scope.isValidHex = true;
    };

    $http({
        method: 'POST',
        url: AppUtil.restUrls.neoData.typeTags,
        dataType: 'json',
        data: '',
        headers: {
            'Content-Type': 'application/json'
        },
    }).then(function success(response) {
        $scope.selectedCategoryTypeList = response.data;
        $scope.selectedTypeCategory = $scope.selectedCategoryTypeList[0];
    }, function error(response) {
        console.log("error:" + response);
    });

    $scope.managingOrderingList = function (orderList) {
        $scope.models = {
            name: orderList.name,
            category: orderList.category,
            values: orderList.values,
            status: orderList.status,
            maxProducts: orderList.maxProducts,
            products: {
                "A": orderList.products
            }
        };
        $("#orderingModal").modal("show");
    };

    $scope.addWebCategory = function () {
        $("#webCategoryModalModal").modal("show");
    };

    $scope.submitWebProduct = function () {
        if ($scope.nameProduct == null || $scope.nameProduct == "") {
            alert("Please Enter Web Product Name.");
            return;
        }
        if ($scope.maxProduct == null || $scope.maxProduct == "") {
            alert("Max Product can not empty.");
            return;
        }
        $scope.categoryObjAdd = {
            category: $scope.selectedTypeCategory,
            name: $scope.nameProduct,
            maxProducts: $scope.maxProduct,
            status: "IN_ACTIVE",
            values: [],
            tagColor : ($scope.selectedTagColor[$scope.nameProduct] != undefined && $scope.selectedTagColor[$scope.nameProduct] != null) ? $scope.selectedTagColor[$scope.nameProduct] : null
        };
        $http({
            method: 'POST',
            url: AppUtil.restUrls.neoData.addTags,
            data: $scope.categoryObjAdd
        }).then(function success(response) {
            //console.log(response.data);
            $("#webCategoryModalModal").modal("hide");
            window.location.reload();
        }, function error(response) {
            console.log("error:" + response);
        });


    };

    $scope.editWebCategoryTags = function (orderList) {
        console.log(orderList)
        $scope.maxProductDetails = "";
        $scope.maxProductDetails = orderList.maxProducts;
        var allProductList = angular.copy($scope.filterProductTagList);
        var selectedProductTagList = [];
        $scope.selectedColor = (orderList.tagColor != undefined && orderList.tagColor != null) ? orderList.tagColor : null;
        for (var k in orderList.products) {
            var found = false;
            for (var i in allProductList) {
                if (allProductList[i].id == orderList.products[k].id) {
                    allProductList.splice(i, 1);
                    found = true;
                    break;
                }
            }
            if (!found) {
                orderList.products.splice(k, 1);
            } else {
                selectedProductTagList.push({
                    id: orderList.products[k].id,
                    text: orderList.products[k].name,
                    status: orderList.products[k].status
                });
            }
        }
        $("#pickListContainer").html("");
        $("#pickListContainer")
            .html("<div id=\"pickList\"></div><br><br><button class=\"btn btn-primary\" id=\"getSelected\">Update</button>");
        var pick = $("#pickList").pickList({
            data: allProductList,
            selected: selectedProductTagList
        });
        $("#getSelected").click(function () {
            var values = [];
            var selectedVals = pick.getValues();
            if (Object.keys(selectedVals).length === 0) {
                alert("Empty List cannot be updated")
                return false;
            }
            if ($scope.maxProductDetails < selectedVals.length) {
                alert("added product is more than max product")
                return false;
            }
            for (var z in selectedVals) {
                values.push('' + selectedVals[z].id);
            }
            orderList.maxProducts = $scope.maxProductDetails;
            $scope.updateTag(orderList, values);
        });
        //console.log("Filter=", $scope.filterProductTagList);
        $("#editWebCategoryModal").modal("show");
    }
    $scope.reorderTag = function (orderList, selectedVals) {
        var values = [];
        for (var z in selectedVals) {
            values.push('' + selectedVals[z].id);
        }
        $scope.updateTag(orderList, values);
    };
    $scope.updateTag = function (orderList, selectedVals) {
        orderList.values = selectedVals;
        orderList.tagColor = ($scope.selectedColor != undefined && $scope.selectedColor != null) ? $scope.selectedColor : null;
        $scope.update(orderList);
    };

    $scope.update = function (updateObject) {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.neoData.updateTags,
            data: updateObject,
            dataType: 'json',
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            console.log(response.data);
            if (response.data == true) {
                $("#webCategoryModalModal1").modal("hide");
                alert("succesfully update");
                window.location.reload();
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.deactivateCategoryTags = function (webTagsList, maxProductNumber) {
        if ($window.confirm("Are you sure, to change the status?")) {
            webTagsList.status = "IN_ACTIVE";
            webTagsList.maxProducts = maxProductNumber;
            $scope.update(webTagsList);
        } else {
            return false;
        }
    };
    $scope.activateCategoryTags = function (webTagsList, maxProductNumber) {
        if ($window.confirm("Are you sure, to change the status?")) {
            webTagsList.status = "ACTIVE";
            webTagsList.maxProducts = maxProductNumber;
            $scope.update(webTagsList);
        } else {
            return false;
        }
    };

    $scope.refreshWebappCache = function () {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.neoCache.refreshCache,
            data: {},
            dataType: 'json',
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            if (response.data && response.data == true) {
                console.log(response.data);
                alert("succesfully update");
            } else {
                console.log("error in cache refresh");
                alert("error updating cache");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.validateHex = function(hexCode) {
        var HEX_REGEX = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        $scope.isValidHex = HEX_REGEX.test(hexCode);
    };
});
