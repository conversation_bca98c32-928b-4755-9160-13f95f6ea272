/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("accessListController", function ($scope, $window, $http, $location, AppUtil) {

    $scope.init = function(){
        $scope.getApplications();
        $scope.noResultMsg = false;
    }

    $scope.getApplications = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.accessControlManagement.applications
        }).then(function success(response) {
            $scope.appList = response.data;
            $scope.selectedApp = $scope.appList[0];
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.getAccessControls = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.accessControlManagement.getAccessControls+"?appName="+$scope.selectedApp
        }).then(function success(response) {
            $scope.accessList = response.data;
            if($scope.accessList.length==0){
                $scope.noResultMsg = true;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.openAddNewAccessModal = function () {
        $("#addAccessModal").modal("show");
    }

    $scope.addAccess = function () {
        if($scope.moduleName==null || $scope.moduleName.trim().length==0){
            alert("Please fill module name properly!");
            return false;
        }else{
            $http({
                method: 'GET',
                url: AppUtil.restUrls.accessControlManagement.getAccessControls+"?appName="+$scope.selectedApp
            }).then(function success(response) {
                $scope.accessList = response.data;
                if($scope.accessList.length==0){
                    $scope.noResultMsg = true;
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

    $scope.activateAccess = function (accessId) {
        if(window.confirm("Are you sure?")){
            $http({
                method: 'PUT',
                url: AppUtil.restUrls.accessControlManagement.activateAccessControls,
                data: accessId
            }).then(function success(response) {
                if(response.status==200 && response.data==true){
                    $scope.getAccessControls();
                }else{
                    alert("Something went wrong. Try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

    $scope.deactivateAccess = function (accessId) {
        if(window.confirm("Are you sure?")){
            $http({
                method: 'PUT',
                url: AppUtil.restUrls.accessControlManagement.deactivateAccessControls,
                data: accessId
            }).then(function success(response) {
                if(response.status==200 && response.data==true){
                    $scope.getAccessControls();
                }else{
                    alert("Something went wrong. Try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

}); 