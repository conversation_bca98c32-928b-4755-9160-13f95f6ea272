/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("EmployeeController", function ($scope, $http, $location, $window, $rootScope, $timeout,
                                                    AuthService, AppUtil, fileService) {
    $scope.init = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.currentAddress = {};
        $scope.permanentAddress = {};
        $scope.viewedEmployee = {};
        $scope.viewedEmployeeMapping = {};
        $scope.updatedEmployee = {};
        $scope.updatedEmployeeMapping = {};
        $scope.activeRoles = {};
        $scope.multiSelectSettings = {showEnableSearchButton: true, template: '<b>{{option}}</b>'};
        $scope.selectedRegion = [];
        $scope.selectedCity = [];
        $scope.empAppMapping = {};
        $scope.dateOfBirth = null;
        $scope.terminationReason = null;
        $scope.hrExecutive = null;
        $scope.leaveApprovalAuthority = null;
        $scope.locationCode = null;
        $scope.userPolicies = [];
        $scope.getAllPolicies();
        $scope.uploadedDoc = null;
        $scope.isDocRequired = false;
        $scope.categoryGridOptions = $scope.createGridOptions();
        var aclData = $rootScope.aclData;
        $scope.hasSyncEmpAccess = false;
        if(aclData!=null){
          if(aclData.action!=null && aclData.action["ESA"]!=null) {
               $scope.hasSyncEmpAccess = true;
          }
        }
        $scope.hasEmpActivateAccess = false;
        if(aclData!=null){
            if(aclData.action!=null && aclData.action["EMACT"]!=null) {
                 $scope.hasEmpActivateAccess = true;
            }
          }

        $scope.brands = [];
        $scope.multiSelectSettingsForBrands = {
            showEnableSearchButton: true,
            template: '<b>{{option.brandId}} - {{option.brandName}}</b>',
            scrollable: true,
            scrollableHeight: '300px'
        };
        $scope.selectedBrandsForFiltering = [];
        $scope.employeeRoleBrandMapping = {};
        $scope.updatedEmployeeRoleMappings = {};
        $scope.brandIdNameMap = {};
        $scope.brandColorMap = {
            1: 'lightgreen',
            3: 'lightsalmon',
            6: 'lightskyblue'
        }
        $scope.modalTabIndex = 0;

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.families
        }).then(function success(response) {
            // console.log(response);
            $rootScope.showFullScreenLoader = false;
            $scope.families = response.data;
            $scope.selectedFamily = $scope.families[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.allActiveRoles
        }).then(function success(response) {
            // console.log(response);
            $rootScope.showFullScreenLoader = false;
            $scope.activeRoles = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=CAFE'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.cafelist = response.data;
            $scope.unitlist = $scope.cafelist;
            $scope.selectedUnit = $scope.unitlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=KITCHEN'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.kitchenlist = response.data;
            $scope.unitlist = $scope.kitchenlist;
            $scope.selectedUnit = $scope.unitlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=WAREHOUSE'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.warehouselist = response.data;
            $scope.unitlist = $scope.warehouselist;
            $scope.selectedUnit = $scope.unitlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=OFFICE'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.officelist = response.data;
            $scope.unitlist = $scope.officelist;
            $scope.selectedUnit = $scope.unitlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=CHAI_MONK'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.chaiMonkList = response.data;
            $scope.unitlist = $scope.chaiMonkList;
            $scope.selectedUnit = $scope.unitlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=DELIVERY'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.codlist = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=COD'
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.callcenterlist = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.departments
        }).then(function success(response) {
            $scope.departmentlist = response.data;
            $rootScope.showFullScreenLoader = false;
            $scope.selectedDepartment = $scope.departmentlist[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regionlist = response.data;
            console.log($scope.regionlist);
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.cities
        }).then(function success(response) {
            $scope.citylist = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });


        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.managers
        }).then(function success(response) {
            // console.log("ReportingManagers=",response.data);
            $scope.reportingManagers = [];
            $scope.reportingManagersData = response.data;
            // console.log("ReportingManagers-->",$scope.reportingManagersData);

            $scope.reportingManagersData.forEach(function (allEmployeeResult) {
                if (allEmployeeResult.status != "IN_ACTIVE") {
                    $scope.reportingManagers.push(allEmployeeResult);
                }
            });
            $rootScope.showFullScreenLoader = false;
            $scope.selectedReportingManager = $scope.reportingManagers[0];
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.users
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.employeeList = response.data;
            // console.log("Employeees-",$scope.employeeList);
            $scope.currentPage = 1; // current page
            $scope.entryLimit = 50; // max no of items to display in a page
            $scope.filteredItems = $scope.employeeList.length; // Initially for
            // no filter
            $scope.totalItems = $scope.employeeList.length;

            $scope.ListManager = [];
            $scope.employeeList.forEach(function (user) {
                if (user.status == "ACTIVE") {
                    // if(user.designation=="Manager" ||
                    // user.designation=="Shift Manager" ||
                    // user.designation=="Shift Manager"){
                    $scope.ListManager.push(user);
                    // }
                }
            });

            $scope.downloadEmployeeList = [];
            $scope.employeeList.forEach(function (downloadListEmp) {
                $scope.downloadEmployeeList.push({
                    id: downloadListEmp.id,
                    name: downloadListEmp.name,
                    status: downloadListEmp.status,
                    department: downloadListEmp.departmentName,
                    designation: downloadListEmp.designation,
                    employeeCode: downloadListEmp.employeeCode
                });

            });

            console.log("DownLoadData=", $scope.downloadEmployeeList);

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.companies
        }).then(function success(response) {
            $scope.companiesList = response.data;
            console.log($scope.companiesList);
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.getBrands();
    };

    $scope.getBrands = function () {
        $http({
            method: "GET",
            url: AppUtil.restUrls.brandMetaData.getAllBrands,
            headers : { bypassCompanyBrandFilter: true }
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.brands = response.data;
                $scope.brands.forEach(brand => {
                    $scope.brandIdNameMap = { ...$scope.brandIdNameMap, [brand.brandId]: brand.brandName }
                });
            } else {
                console.log("Unable to Fetch Brands");
            }
        });
    }

    $scope.createGridOptions = function () {
        return {
            enableFiltering: true,
            enableColumnResizing: true,
            columnDefs: [{
                field: 'actionDetailId',
                displayName: 'Action Detail Id',
                enableCellEdit: false
            }, {
                field: 'actionType',
                displayName: 'Action Type',
                enableCellEdit: false
            }, {
                field: 'actionCategory',
                displayName: 'Action Category',
                enableCellEdit: false
            }, {
                field: 'actionCode',
                displayName: 'Action Code',
                enableCellEdit: false
            }, {
                field: 'actionDescription',
                displayName: 'Action Description',
                enableCellEdit: false,
            }]
        };
    };

    $scope.showActionsForRole = function (role) {
        $scope.currentSelectedRole = role.roleId;
        if (role.expanded != null && role.expanded != undefined) {
            role.expanded = !role.expanded;
        } else {
            role.expanded = true;
        }
        $scope.categoryGridOptions.data = role.roleActions;
        angular.forEach($scope.activeEmployeeRoles, function (row) {
            if (row.roleId != $scope.currentSelectedRole) {
                row.expanded = false;
            }
        });
    };

    $scope.getAllPolicies = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.getUserPolicies
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.userPolicies = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.isHR = function () {
        var user = AppUtil.getCurrentUser();
        return user != undefined && user != null
            && (user.department.id == 106 || user.department.id == 136 || (user.department.id == 102 && user.designation.id == 1009));
    }

    $scope.empActivateAccess  =function(){
        return $scope.hasEmpActivateAccess;
    }

    $scope.isHod = function(empId) {
        if($scope.hodList[empId] == undefined || $scope.hodList[empId] == null) {
            return false;
        } else if($scope.hodList[empId] == 'IN_ACTIVE'){
            return false;
        }
        return true;
    }

    $scope.changeEligibility = function (value) {
        $scope.employeeMealEligible = value;
    };

    /*
     * $scope.filter = function() { $timeout(function() { $scope.filteredItems =
     * $scope.filtered.length; }, 10); };
     */

    $scope.sort_by = function (predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };

    $scope.updatePerm = function (val) {
        if (val === 'true') {
            $scope.permanentAddressSame = true;
        } else {
            $scope.permanentAddressSame = false;
        }
    }

    $scope.addEmployee = function () {
        $scope.action = "Add"
        $scope.empName = null;
        $scope.empGender = "M";
        $scope.currentAddress = {};
        $scope.permanentAddress = {};
        $scope.currentAddress.city = "";
        $scope.permanentAddress.city = "";
        $scope.currentAddress.state = "";
        $scope.permanentAddress.state = "";
        $scope.currentAddress.country = "India";
        $scope.permanentAddress.country = "India";
        $scope.currentAddress.addressType = "OFFICIAL";
        $scope.permanentAddress.addressType = "OFFICIAL";
        $scope.permanentAddressSame = true;
        $scope.showPermanentAddress = true;
        $scope.selectedFamily = $scope.families[0];
        $scope.unitlist = null;
        $scope.selectedUnit = null;
        $scope.selectedDepartment = $scope.departmentlist[0];
        $scope.selectedCompany = $scope.companiesList[0];
        $scope.selectedDesignation = $scope.selectedDepartment.designations[0];
        $scope.employmentType = "FULL_TIME";
        $scope.employmentStatus = "IN_ACTIVE";
        $scope.selectedReportingManager = $scope.reportingManagers[0];
        $scope.employeeCode = null;
        $scope.employeeEmail = "";
        $scope.slackId = null;
        $scope.biometricId = null;
        $scope.selectedMappingType = null;
        $scope.selectedRegion = [];
        $scope.selectedCity = [];
        $scope.recordStatus = null;
        $scope.employeeMealEligible = $scope.eligible = false;
        $scope.dateOfBirth = null;
        $scope.terminationReason = null;
        $scope.hrExecutive = null;
        $scope.leaveApprovalAuthority = null;
        $scope.locationCode = 0;
        $("#employeeModal").modal("show");
        $scope.loading = false;
    }

    $scope.addBulkEmployee = function () {
        $("#employeeBulkModal").modal("show");
    }

    $scope.syncEmployee = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.employeeManagement.syncEmployee
        }).then(function success(response) {
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
            bootbox.alert("Error in syncing Employee");
        });
        bootbox.alert("Employee Syncing Process started in Background it will complete in some time.");


    }


    $scope.uploadEmployeeSheet = function () {
        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select a .xlsx file");
            return;
        }

        console.log("File is", fileService.getFile());

        var fd = new FormData();
        fd.append("file", fileService.getFile());
        $rootScope.showDetailLoader = true;

        $http({
            url: AppUtil.restUrls.userManagement.uploadOnboardingSheet,
            method: 'POST',
            data: fd,
            headers: {
                'Content-Type': undefined
            },
            transformRequest: angular.identity
        }).success(function (response) {
            $rootScope.showDetailLoader = false;
            angular.element("input[type='file']").val(null);
            fileService.push(null);
            var data = response
            console.log(response);
            if (response == "") {
                bootbox.alert("File uploaded");
                window.location.reload();
            } else {
                bootbox.alert(response.errorMessage);
            }

        }).error(function (response) {
            $rootScope.showDetailLoader = false;
            alert("Error while uploading Expense Sheet");
        });
    }

    $scope.getFamilyUnits = function (selectedFamily) {
        if (selectedFamily.name === "CAFE") {
            $scope.unitlist = $scope.cafelist;
        } else if (selectedFamily.name === "DELIVERY") {
            $scope.unitlist = $scope.codlist;
        } else if (selectedFamily.name === "COD") {
            $scope.unitlist = $scope.callcenterlist;
        }
    }

    function addEmployeeApplicationMapping(employeeId) {
        var empMappingObj = {};
        if ($scope.selectedMappingType === "OVER_ALL") {
            empMappingObj = {
                employeeId: employeeId,
                mappingType: $scope.selectedMappingType,
                mappingValue: ['OVER_ALL'],
                recordStatus: "ACTIVE"
            }
        } else if ($scope.selectedMappingType === "REGION") {
            empMappingObj = {
                employeeId: employeeId,
                mappingType: $scope.selectedMappingType,
                mappingValue: $scope.selectedRegion,
                recordStatus: "ACTIVE"
            }
        } else if ($scope.selectedMappingType === "CITY") {
            empMappingObj = {
                employeeId: employeeId,
                mappingType: $scope.selectedMappingType,
                mappingValue: $scope.selectedCity,
                recordStatus: "ACTIVE"
            }
        }
        $rootScope.showFullScreenLoader = true;
        $scope.showEmpBtn = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.addEmpAppMapping,
            data: empMappingObj
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.showEmpBtn = false;
                console.log(response.data);
            } else {
                $rootScope.showFullScreenLoader = false;
                $scope.showEmpBtn = false;
            }
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    $scope.submitAddEmployee = function () {
        if ($scope.permanentAddressSame) {
            $scope.permanentAddress = $scope.currentAddress;
        }
        var empObj = {
            name: $scope.empName,
            gender: $scope.empGender,
            primaryContact: $scope.currentAddress.contact1,
            secondaryContact: $scope.currentAddress.contact2,
            biometricId: $scope.biometricId,
            department: $scope.selectedDepartment,
            company: $scope.selectedCompany,
            designation: $scope.selectedDesignation,
            currentAddress: $scope.currentAddress,
            permanentAddress: $scope.permanentAddress,
            employmentType: $scope.employmentType,
            employmentStatus: "IN_ACTIVE",
            employeeEmail: $scope.employeeEmail,
            joiningDate: $scope.joiningDate,
            reportingManager: {
                id: $scope.selectedReportingManager.id
            },
            employeeCode: $scope.employeeCode,
            communicationChannel: $scope.slackId,
            employeeMealEligible: $scope.employeeMealEligible,
            reasonForTermination: $scope.terminationReason,
            hrExecutive: $scope.hrExecutive,
            leaveApprovalAuthority: $scope.leaveApprovalAuthority,
            locCode: $scope.locationCode,
            dateOfBirth: $scope.dateOfBirth
        }

        /*
         * if(!$scope.addEmployeeForm.$valid){ alert("Please fill all the
         * required fields correctly."); return; }
         */
        /*
         * if($scope.addEmployeeForm=="" || $scope.addEmployeeForm=="") {
         *
         * alert("Please fill all the required fields correctly."); }
         */

        if ($scope.empName == "" || $scope.empName == null) {
            alert("Employee Name is empty.");
            return false;
        }
        if ($scope.currentAddress.line1 == "" || $scope.currentAddress.line1 == null) {
            alert("line1 is empty.");
            return false;
        }
        if ($scope.currentAddress.city == "" || $scope.currentAddress.city == null) {
            alert("City is empty.");
            return false;
        }
        if ($scope.currentAddress.state == "" || $scope.currentAddress.state == null) {
            alert("State is empty.");
            return false;
        }
        if ($scope.currentAddress.zipCode == "" || $scope.currentAddress.zipCode == "null"
            || $scope.currentAddress.zipCode == null) {
            alert("Zip Code is empty.");
            return false;
        }
        if ($scope.currentAddress.contact1 == "" || $scope.currentAddress.contact1 == "null"
            || $scope.currentAddress.contact1 == null) {
            alert("Contact1 is empty.");
            return false;
        }

        if ($scope.permanentAddressSame == false) {
            if ($scope.permanentAddress.line1 == "" || $scope.permanentAddress.line1 == null) {
                alert("Line 1 is empty in permanent address.");
                return false;
            }

            if ($scope.permanentAddress.city == "" || $scope.permanentAddress.city == "null") {
                alert("City is empty in permanent address.");
                return false;
            }

            if ($scope.permanentAddress.state == "" || $scope.permanentAddress.state == null) {
                alert("State is empty.");
                return false;
            }
            if ($scope.permanentAddress.zipCode == "" || $scope.permanentAddress.zipCode == "null") {
                alert("Zip Code is empty.");
                return false;
            }
            if ($scope.permanentAddress.contact1 == "" || $scope.permanentAddress.contact1 == "null") {
                alert("Contact1 is empty.");
                return false;
            }
        }
        if ($scope.employeeEmail != "") {
            if ($scope.employeeEmail.indexOf("@chaayos.com") == -1 && $scope.employeeEmail.indexOf("@dohful.com") == -1 ) {
                alert("Email should be associated with-'@chaayos.com' or 'dohful.com' ")
                return false;
            }

        }

        if ($scope.joiningDate == "" || $scope.joiningDate == null) {
            alert("Joining Date is empty.");
            return false;
        }

        if ($scope.employeeCode == "" || $scope.employeeCode == null) {
            alert("Employee code should not empty.");
            return false;
        }

        if ($scope.selectedMappingType == 'REGION') {
            $scope.selectedCity = [];
            if ($scope.selectedRegion == []) {
                alert("Mapped region is not selected.");
                return false;
            }
        }

        if ($scope.selectedMappingType == 'CITY') {
            $scope.selectedRegion = [];
            if ($scope.selectedCity == []) {
                alert("Mapped city is not selected.");
                return false;
            }
        }

        if ($scope.locationCode === null || $scope.locationCode === '') {
            alert("Location Code is required");
            return false;
        }

        $rootScope.showFullScreenLoader = true;
        $scope.showEmpBtn = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userAdd,
            data: empObj
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.showEmpBtn = false;
                if ($scope.selectedMappingType != "" || $scope.selectedMappingType != null) {
                    addEmployeeApplicationMapping(response.data.id);
                }
                alert("Employee added successfully!");
                window.location.reload();
            } else {
                $rootScope.showFullScreenLoader = false;
                $scope.showEmpBtn = false;
                alert("Error in adding Employee!");
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $("#addEmployeeIdDiv").click(function () {
        $("#employeeModal").modal({
            backdrop: false
        });
    });

    $scope.viewEmployee = function (employee) {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.user + '?userId=' + employee.id
        }).then(function success(response) {
            console.log(response);
            $scope.viewedEmployee = response.data;
            var employeeMapping = getEmpAppMapping(employee.id);
            if (employeeMapping != null) {
                $scope.viewedEmployee.mappingType = employeeMapping.mappingType;
                $scope.viewedEmployee.mappingValue = employeeMapping.mappingValue;
            }
            console.log("ViewEmployee=", $scope.viewedEmployee);
            console.log(JSON.stringify($scope.viewedEmployee));
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.action = "View"
        $("#employeeModal").modal("show");
    };

    $scope.$watch('viewedEmployee', function (newVal, oldVal) {
        console.log("in watch");
        $scope.empName = $scope.viewedEmployee.name;
        $scope.empGender = $scope.viewedEmployee.gender;
        $scope.currentAddress = $scope.viewedEmployee.currentAddress;
        if ($scope.viewedEmployee.permanentAddress == null) {
            $scope.showPermanentAddress = false;
        } else {
            $scope.showPermanentAddress = true;
            if ($scope.viewedEmployee.permanentAddress.id === $scope.viewedEmployee.currentAddress.id) {
                $scope.permanentAddressSame = true;
            } else {
                $scope.permanentAddressSame = false;
            }
        }
        $scope.permanentAddress = $scope.viewedEmployee.permanentAddress;
        $scope.selectedFamily = $scope.viewedEmployee.family;
        $scope.selectedDepartment = $scope.viewedEmployee.department;
        $scope.selectedCompany = $scope.viewedEmployee.company;
        $scope.selectedDesignation = $scope.viewedEmployee.designation;
        $scope.employmentType = $scope.viewedEmployee.employmentType;
        $scope.employmentStatus = $scope.viewedEmployee.employmentStatus;
        $scope.selectedReportingManager = $scope.viewedEmployee.reportingManager;
        $scope.biometricId = $scope.viewedEmployee.biometricId;
        $scope.joiningDate = $scope.dateformatting($scope.viewedEmployee.joiningDate);
        $scope.employeeCode = $scope.viewedEmployee.employeeCode;
        $scope.eligible = $scope.viewedEmployee.employeeMealEligible;
        $scope.employeeMealEligible = $scope.viewedEmployee.employeeMealEligible;
        $scope.selectedMappingType = $scope.viewedEmployee.mappingType;
        $scope.dateOfBirth = $scope.viewedEmployee.dob;
        $scope.terminationReason = $scope.viewedEmployee.reasonForTermination;
        $scope.hrExecutive = $scope.viewedEmployee.hrExecutive;
        $scope.leaveApprovalAuthority = $scope.viewedEmployee.leaveApprovalAuthority;
        $scope.locationCode = $scope.viewedEmployee.locCode;
        if ($scope.selectedMappingType == 'OVER_ALL') {
            $scope.selectedCity = [];
            $scope.selectedRegion = [];
        }
        if ($scope.selectedMappingType == 'CITY') {
            $scope.selectedCity = $scope.viewedEmployee.mappingValue;
        }
        if ($scope.selectedMappingType == 'REGION') {
            $scope.selectedRegion = $scope.viewedEmployee.mappingValue;
        }
    });

    function fetchEmployeeUnits(employeeId, onSuccessCallback, showLoader) {
        if (showLoader) $rootScope.showFullScreenLoader = true;

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userUnits,
            data: {
                employeeId: employeeId,
                onlyActive: true
            }
        }).then(function success(response) {
            if (showLoader) $rootScope.showFullScreenLoader = false;

            if (response.data) {
                onSuccessCallback(response.data);
            }
        }, function error(response) {
            if (showLoader) $rootScope.showFullScreenLoader = false;
            console.error("Error fetching employee units:", response);
        });
    }

    $scope.activateEmployee = function (emp) {
        $scope.action = "Activate";
        $scope.empForMapping = emp;
        $scope.emplyeeSelected = emp;
        $scope.activationUnits = [];
        $scope.selectTab('tab1');
        $scope.selectedCafe = $scope.cafelist[0];
        $scope.selectedCOD = $scope.codlist[0];
        $scope.selectedCallCenter = $scope.callcenterlist[0];
        $scope.selectedKitchen = $scope.kitchenlist[0];
        $scope.selectedWareHouse = $scope.warehouselist[0];
        $scope.selectedOffice = $scope.officelist[0];
        $scope.selectedChainMonk = $scope.chaiMonkList[0];
        $scope.allCafesCheck = false;
        $scope.allCODsCheck = false;
        $scope.allCallCentersCheck = false;
        $scope.addedCafes = [];
        $scope.addedCODs = [];
        $scope.addedCallCenters = [];
        $scope.addedKitchens = [];
        $scope.addedWareHouse = [];
        $scope.addedOffices = [];
        $scope.addedChaiMonk = [];

        // Load current employee's mappings
        fetchEmployeeUnits(emp.id, function(data) {
            fillAddedUnits(data);
            $scope.allEmployeeData = data;
        }, false);

        $("#employeeActivationModal").modal("show");
    };

    $scope.selectTab = function (tabIndex, cafeData) {

        console.log("TabIndex435=", tabIndex);
        console.log("cafeData=", $scope.addedCafes);

        if (tabIndex == 'tab2') {
            console.log("cafeData111=", $scope.addedCafes.length);
            // $scope.addedCafes.length=0;
            // $scope.addedCODs.length=0;
            if (cafeData != undefined) {
                if ($scope.empForMapping.maxAllocatedUnits > 0) {
                    if (cafeData.length > $scope.empForMapping.maxAllocatedUnits) {
                        alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                        return false;
                    }
                }
            }
        } else if (tabIndex == 'tab3') {
            // console.log("cod=",$scope.addedCODs.length);
            // $scope.addedCallCenters.length=0;
            // $scope.addedKitchens.length=0;
            // $scope.addedWareHouse.length=0;
            // $scope.addedOffices.length=0;
            // $scope.addedChaiMonk.length=0;
            // var sumCod =
            // $scope.addedCafes.length+$scope.addedCODs.length+$scope.addedCallCenters.length+$scope.addedKitchens.length+$scope.addedWareHouse.length+$scope.addedOffices.length+$scope.addedChaiMonk.length;
            var sumCod = $scope.addedCafes.length + $scope.addedCODs.length;

            if ($scope.empForMapping.maxAllocatedUnits > 0) {
                if (sumCod > $scope.empForMapping.maxAllocatedUnits) {
                    alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                    return false;
                }
            }

        } else if (tabIndex == 'tab4') {
            // $scope.addedKitchens.length=0;
            // $scope.addedWareHouse.length=0;
            // $scope.addedOffices.length=0;
            // $scope.addedChaiMonk.length=0;

            var sumCod = $scope.addedCafes.length + $scope.addedCODs.length + $scope.addedCallCenters.length;
            /*
             * if(sumCod>2) { alert("Unable to add Call center unit as already
             * mapped 2 unit") return false; } }
             */

            if ($scope.empForMapping.maxAllocatedUnits > 0) {
                if (sumCod > $scope.empForMapping.maxAllocatedUnits) {
                    alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                    return false;
                }
            }
        } else if (tabIndex == 'tab5') {
            // $scope.addedWareHouse.length=0;
            // $scope.addedOffices.length=0;
            // $scope.addedChaiMonk.length=0;
            var sumCod = $scope.addedCafes.length + $scope.addedCODs.length + $scope.addedCallCenters.length
                + $scope.addedKitchens.length;

            if ($scope.empForMapping.maxAllocatedUnits > 0) {
                if (sumCod > $scope.empForMapping.maxAllocatedUnits) {
                    alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                    return false;
                }
            }
        } else if (tabIndex == 'tab6') {
            // $scope.addedOffices.length=0;
            // $scope.addedChaiMonk.length=0;
            var sumCod = $scope.addedCafes.length + $scope.addedCODs.length + $scope.addedCallCenters.length
                + $scope.addedKitchens.length + $scope.addedWareHouse.length;

            if ($scope.empForMapping.maxAllocatedUnits > 0) {
                if (sumCod > $scope.empForMapping.maxAllocatedUnits) {
                    alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                    return false;
                }
            }

        } else if (tabIndex == 'tab7') {
            // $scope.addedChaiMonk.length=0;
            var sumCod = $scope.addedCafes.length + $scope.addedCODs.length + $scope.addedCallCenters.length
                + $scope.addedKitchens.length + $scope.addedWareHouse.length + $scope.addedOffices.length;

            if ($scope.empForMapping.maxAllocatedUnits > 0) {
                if (sumCod > $scope.empForMapping.maxAllocatedUnits) {
                    alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                    return false;
                }
            }
        } else if (tabIndex == 'tab8') {

            var sumCod = $scope.addedCafes.length + $scope.addedCODs.length + $scope.addedCallCenters.length
                + $scope.addedKitchens.length + $scope.addedWareHouse.length + $scope.addedOffices.length
                + $scope.addedChaiMonk.length;
            if ($scope.empForMapping.maxAllocatedUnits > 0) {
                if (sumCod > $scope.empForMapping.maxAllocatedUnits) {
                    alert("Unable to update more than " + $scope.empForMapping.maxAllocatedUnits + " units");
                    return false;
                }
            }
        }

        // }
        // console.log("23=",$scope.allEmployeeData);

        /*
         * if($scope.addedCafes.length==2 || $scope.addedCafes.length>2) {
         * $('#cafeValidID').show(); //console.log("Sorry, you can not add more
         * than 2 unit"); return false; }
         */

        switch (tabIndex) {
            case 'tab1' :
                $scope.tab1 = false;
                $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = true;
                break;
            case 'tab2' :
                $scope.tab2 = false;
                $scope.tab1 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = true;
                break;
            case 'tab3' :
                $scope.tab3 = false;
                $scope.tab1 = $scope.tab2 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = true;
                break;

            case 'tab4' :
                $scope.tab4 = false;
                $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab5 = $scope.tab6 = $scope.tab7 = $scope.tab8 = true;
                break;
            case 'tab5' :
                $scope.tab5 = false;
                $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab6 = $scope.tab7 = $scope.tab8 = true;
                break;

            case 'tab6' :
                $scope.tab6 = false;
                $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab7 = $scope.tab8 = true;
                break;

            case 'tab7' :
                $scope.tab7 = false;
                $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab8 = true;
                break;

            case 'tab8' :
                $scope.tab8 = false;
                $scope.tab1 = $scope.tab2 = $scope.tab3 = $scope.tab4 = $scope.tab5 = $scope.tab6 = $scope.tab7 = true;
                break;
        }
    }

    $scope.selectAllCafe = function () {
        if ($scope.allCafesCheck === true) {
            $scope.addedCafes = $scope.cafelist;
        } else {
            $scope.addedCafes = [];
        }
    }

    $scope.selectAllCOD = function () {
        if ($scope.allCODsCheck === true) {
            $scope.addedCODs = $scope.codlist;
        } else {
            $scope.addedCODs = [];
        }
    }

    $scope.selectAllCallCenter = function () {
        if ($scope.allCallCentersCheck === true) {
            $scope.addedCallCenters = $scope.callcenterlist;
        } else {
            $scope.addedCallCenters = [];
        }
    }

    $scope.selectAllKitchen = function () {
        if ($scope.allKitchensCheck === true) {
            $scope.addedKitchens = $scope.kitchenlist;
        } else {
            $scope.addedKitchens = [];
        }
    }

    $scope.selectAllOffice = function () {
        if ($scope.allOfficeCheck === true) {
            $scope.addedOffices = $scope.officelist;
        } else {
            $scope.addedOffices = [];
        }
    }

    $scope.selectAllWareHouse = function () {
        if ($scope.allWareHouseCheck === true) {
            $scope.addedWareHouse = $scope.warehouselist;
        } else {
            $scope.addedWareHouse = [];
        }
    }
    $scope.selectAllChaiMonk = function () {
        if ($scope.allchaiMonkCheck === true) {
            $scope.addedChaiMonk = $scope.chaiMonkList;
        } else {
            $scope.addedChaiMonk = [];
        }
    }
    $scope.selectAllUnit = function (category) {
        if (category === 'cafe') {
            if ($scope.allCallCentersCheck === true) {
                $scope.addedCallCenters = $scope.callcenterlist;
            } else {
                $scope.addedCallCenters = [];
            }
        }
        if (category === 'cod') {
            if ($scope.allCODsCheck === true) {
                $scope.addedCODs = $scope.codlist;
            } else {
                $scope.addedCODs = [];
            }
        }
        if (category === 'callCenter') {
            if ($scope.allCallCentersCheck === true) {
                $scope.addedCallCenters = $scope.callcenterlist;
            } else {
                $scope.addedCallCenters = [];
            }
        }
    }

    $scope.addUnit = function (category) {
        console.log($scope.empForMapping);
        $scope.Unitvalidation = "";
        var sumCafes = $scope.addedCafes.length + $scope.addedCODs.length + $scope.addedCallCenters.length
            + $scope.addedKitchens.length + $scope.addedWareHouse.length + $scope.addedOffices.length
            + $scope.addedChaiMonk.length;
        if ($scope.empForMapping.maxAllocatedUnits > 0) {
            if (sumCafes >= $scope.empForMapping.maxAllocatedUnits) {
                $('#cafeValidID').show();
                return false;
            }
            if ($scope.addedCafes.length < $scope.empForMapping.maxAllocatedUnits) {
                $('#cafeValidID').hide();
            }
        }
        if (category === 'cafe') {
            $scope.storeSelectedCafes.forEach(function (value) {
                $scope.addedCafes = addToUnit($scope.addedCafes, value);
            });
            $scope.storeSelectedCafes = [];
        }

        if (category === 'cod') {
            $scope.storeSelectedCOD.forEach(function (value) {
                $scope.addedCODs = addToUnit($scope.addedCODs, value);
            });
            $scope.storeSelectedCOD = [];
        }
        if (category === 'callCenter') {
            $scope.storeSelectedCallCenter.forEach(function (value) {
                $scope.addedCallCenters = addToUnit($scope.addedCallCenters, value);
            });
            $scope.storeSelectedCallCenter = [];
        }
        if (category === 'kitchen') {
            $scope.storeSelectedKitchen.forEach(function (value) {
                $scope.addedKitchens = addToUnit($scope.addedKitchens, value);
            });
            $scope.storeSelectedKitchen = [];
        }
        if (category === 'wareHouse') {
            $scope.storeSelectedWarehouse.forEach(function (value) {
                $scope.addedWareHouse = addToUnit($scope.addedWareHouse, value);
            });
            $scope.storeSelectedWarehouse = [];
        }
        if (category === 'office') {
            $scope.storeSelectedOffice.forEach(function (value) {
                $scope.addedOffices = addToUnit($scope.addedOffices, value);
            });
            $scope.storeSelectedOffice = [];
        }
        if (category === 'chaimonk') {
            $scope.storeSelectedChaiMonk.forEach(function (value) {
                $scope.addedChaiMonk = addToUnit($scope.addedChaiMonk, value);
            });
            $scope.storeSelectedChaiMonk = [];
        }
    }

    $scope.removeUnit = function (category, itemId) {
        // console.log(itemId);
        if (category === 'cafe') {
            $scope.addedCafes = removeFromUnit($scope.addedCafes, itemId);
        }
        if (category === 'cod') {
            $scope.addedCODs = removeFromUnit($scope.addedCODs, itemId);
        }
        if (category === 'callCenter') {
            $scope.addedCallCenters = removeFromUnit($scope.addedCallCenters, itemId);
        }
        if (category === 'kitchen') {
            $scope.addedKitchens = removeFromUnit($scope.addedKitchens, itemId);
        }
        if (category === 'wareHouse') {
            $scope.addedWareHouse = removeFromUnit($scope.addedWareHouse, itemId);
        }
        if (category === 'office') {
            $scope.addedOffices = removeFromUnit($scope.addedOffices, itemId);
        }
        if (category === 'chaimonk') {
            $scope.addedChaiMonk = removeFromUnit($scope.addedChaiMonk, itemId);
        }
    }

    $scope.activateEmployeeSubmit = function () {
        var unitList = [];
        $scope.addedCafes.forEach(function (unit) {
            unitList.push(unit.id);
        });
        $scope.addedCODs.forEach(function (unit) {
            unitList.push(unit.id);
        });
        $scope.addedCallCenters.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedKitchens.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedWareHouse.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedOffices.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedChaiMonk.forEach(function (unit) {
            unitList.push(unit.id);
        });

        var empObj = {
            employeeId: $scope.emplyeeSelected.id,
            unitIds: unitList
        }
        if (unitList.length === 0 || unitList == null) {
            alert("Please select at least one unit for employee.");
            return;
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userActivate,
            data: empObj
        }).then(function success(response) {
            if (response.status == 200) {
                alert("Employee activated successfully!");
                window.location.reload();
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.dateformatting = function (startDate) {
        var year = new Date(startDate).getFullYear();
        var month = new Date(startDate).getMonth() + 1;
        var day = new Date(startDate).getDate();
        if (day >= 1 && day < 10)
            day = '0' + day;
        if (month >= 1 && month < 10)
            month = '0' + month;
        return year + "-" + month + "-" + day;
    };

    function addToUnit(unitList, item) {
        if (!angular.isUndefined(unitList) || unitList != null) {
            var contains = false;
            unitList.forEach(function (unitItem) {
                if (unitItem.id === item.id) {
                    contains = true;
                }
            });
            if (!contains) {
                unitList.push(item);
            }
        } else {
            unitList = [];
            unitList.push(item);
        }

        return unitList;

    }

    function removeFromUnit(unitList, item) {
        if (!angular.isUndefined(unitList) || unitList != null) {
            var newList = [];
            unitList.forEach(function (unitItem) {
                if (unitItem.id !== item) {
                    newList.push(unitItem);
                }
            });
            unitList = newList;
        } else {
            unitList = [];
        }
        return unitList;
    }

    $scope.storeSelectedCafes = [];
    $scope.multiSelectSettingsCafes = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };

    $scope.storeSelectedCOD = [];
    $scope.multiSelectSettingsCOD = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };
    $scope.storeSelectedCallCenter = [];
    $scope.multiSelectSettingsCallCenter = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };
    $scope.storeSelectedKitchen = [];
    $scope.multiSelectSettingsKitchen = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };
    $scope.storeSelectedWarehouse = [];
    $scope.multiSelectSettingsWarehouse = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };
    $scope.storeSelectedOffice = [];
    $scope.multiSelectSettingsOffice = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };
    $scope.storeSelectedChaiMonk = [];
    $scope.multiSelectSettingsChaiMonk = {
        showEnableSearchButton: true,
        template: '<b> {{option.name}}</b>',
        scrollable: true,
        showCheckAll: false,
        showUncheckAll: false,
        scrollableHeight: '250px',
        trackBy: 'id',
        clearSearchOnClose: true
    };
     $scope.select2Options = {
        allowClear: true,
        placeholder: 'Search employee...',
        width: '300px',
        dropdownParent: angular.element('#employeeActivationModal')  // fix for modal
     };
    $scope.editEmpMapping = function (emp) {
        $scope.storeSelectedChaiMonk = [];
        $scope.storeSelectedOffice = [];
        $scope.storeSelectedWarehouse = [];
        $scope.storeSelectedKitchen = [];
        $scope.storeSelectedCallCenter = [];
        $scope.storeSelectedCOD = [];
        $scope.storeSelectedCafes = [];
        $scope.empForMapping = emp;
        $scope.action = "Edit Mapping";
        $scope.emplyeeSelected = emp;
        $scope.activationUnits = [];
        $scope.selectTab('tab1');
        $scope.selectedCafe = $scope.cafelist[0];
        $scope.selectedCOD = $scope.codlist[0];
        $scope.selectedCallCenter = $scope.callcenterlist[0];
        $scope.allCafesCheck = false;
        $scope.allCODsCheck = false;
        $scope.allCallCentersCheck = false;
        $scope.allKitchensCheck = false;
        $scope.allWareHouseCheck = false;
        $scope.allOfficeCheck = false;
        $scope.allchaiMonkCheck = false;
        $scope.addedCafes = [];
        $scope.addedCODs = [];
        $scope.addedCallCenters = [];
        $scope.addedKitchens = [];
        $scope.addedWareHouse = [];
        $scope.addedOffices = [];
        $scope.addedChaiMonk = [];
        $scope.allEmployeeData = {};
        $scope.selectedEmployeeId = null;
        $timeout(function () {
            angular.element('#employeeDropdown').val(null).trigger('change');
        }, 0);
        $scope.clonedUnits = []; // To store cloned units for highlighting

        fetchEmployeeUnits(emp.id, function(data) {
            fillAddedUnits(data);
            $scope.allEmployeeData = data;
        }, false);

        // Function to handle cloning employee mappings
        $scope.handleCloneClick = function () {
            if (!$scope.selectedEmployeeId) {
                alert("Please select an employee to clone mapping from.");
                return;
            }

            var employeeIdInt = parseInt($scope.selectedEmployeeId); // Convert to integer

            if (isNaN(employeeIdInt)) {
                alert("Invalid employee ID selected.");
                return;
            }

            fetchEmployeeUnits(employeeIdInt, function(data) {
               $scope.clonedUnits = data;

               const categoryMap = {
                  "CAFE": $scope.addedCafes,
                  "DELIVERY": $scope.addedCODs,
                  "COD": $scope.addedCallCenters,
                  "KITCHEN": $scope.addedKitchens,
                  "WAREHOUSE": $scope.addedWareHouse,
                  "OFFICE": $scope.addedOffices,
                  "CHAI_MONK": $scope.addedChaiMonk
               };

               const result = processUnits(data, categoryMap);

               alert("Employee mapping cloned successfully from employee: " + employeeIdInt +
                  "\n" + result.added + " units added\n" + result.skipped + " duplicate units skipped");
            }, true);
        };

        $("#employeeActivationModal").modal("show");
    }

    function processUnits(units, categoryMap) {
        let added = 0, skipped = 0;

        units.forEach(function(unit) {
            unit.isCloned = true;
            let targetList = categoryMap[unit.category];
            if (!targetList) return;

            let isDuplicate = targetList.some(u => u.id === unit.id);
            if (!isDuplicate) {
                targetList.push(unit);
                added++;
            } else {
                skipped++;
            }
        });

        return { added, skipped };
    }

    $scope.excludeCurrentEmployee = function(employee) {
        if (!employee || !$scope.empForMapping) {
            return true;
        }
        return employee.id !== $scope.empForMapping.id;
    };

    function fillAddedUnits(units) {
        // console.log(units);
        var cafeList = [];
        var codList = [];
        var callCenterList = [];
        var kitchenlist = [];
        var warehouselist = [];
        var officelist = [];
        var chaiMonklist = [];
        units.forEach(function (unit) {
            console.log("Unit Data=", unit);
            if (unit.category === "CAFE") {
                cafeList.push(unit);
            } else if (unit.category === "DELIVERY") {
                codList.push(unit);
            } else if (unit.category === "COD") {
                callCenterList.push(unit);
            } else if (unit.category === "KITCHEN") {
                kitchenlist.push(unit);
            } else if (unit.category === "WAREHOUSE") {
                warehouselist.push(unit);
            } else if (unit.category === "OFFICE") {
                officelist.push(unit);
            } else if (unit.category === "CHAI_MONK") {
                chaiMonklist.push(unit);
            }
            $scope.addedCafes = cafeList;
            $scope.addedCODs = codList;
            $scope.addedCallCenters = callCenterList;
            $scope.addedKitchens = kitchenlist;
            $scope.addedWareHouse = warehouselist;
            $scope.addedOffices = officelist;
            $scope.addedChaiMonk = chaiMonklist;
        });
    }

    $scope.editEmployeeMappingSubmit = function () {
        $rootScope.showFullScreenLoader = true;
        var unitList = [];
        $scope.addedCafes.forEach(function (unit) {
            unitList.push(unit.id);
        });
        $scope.addedCODs.forEach(function (unit) {
            unitList.push(unit.id);
        });
        $scope.addedCallCenters.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedKitchens.forEach(function (unit) {
            unitList.push(unit.id);
        });
        $scope.addedWareHouse.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedOffices.forEach(function (unit) {
            unitList.push(unit.id);
        });

        $scope.addedChaiMonk.forEach(function (unit) {
            unitList.push(unit.id);
        });

        var empObj = {
            employeeId: $scope.emplyeeSelected.id,
            unitIds: unitList
        }
        if (unitList.length === 0 || unitList == null) {
            alert("Please select at least one unit for employee.");
            return;
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userUpdateMapping,
            data: empObj
        }).then(function success(response) {
            if (response.status == 200) {
                alert("Employee updated successfully!");
                window.location.reload();
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getEmpAppMapping(employeeId) {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.empMapping + '?userId=' + employeeId
        }).then(function success(response) {
            var employeeMapping = response.data;
            $scope.selectedMappingType = employeeMapping.mappingType;
            if ($scope.selectedMappingType == 'OVER_ALL') {
                $scope.selectedCity = [];
                $scope.selectedRegion = [];
            }
            if ($scope.selectedMappingType == 'CITY') {
                $scope.selectedCity = employeeMapping.mappingValue;
            }
            if ($scope.selectedMappingType == 'REGION') {
                $scope.selectedRegion = employeeMapping.mappingValue;
            }
            return employeeMapping;
            //$scope.recordStatus = employeeMapping.recordStatus;
        }, function error(response) {
            console.log("error:" + response);
        });
        return null;
    }

    $scope.updateEmployee = function (employee) {
        console.log(employee);

        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.user + '?userId=' + employee.id
        }).then(function success(response) {
            console.log(response);
            $scope.updatedEmployee = response.data;
            getEmpAppMapping(employee.id);
            setUpdateView($scope.updatedEmployee);
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.action = "UpdateEmp"
        $("#employeeModal").modal("show");
    }

    function setUpdateView() {
        console.log("Checking function......");
        $scope.empName = $scope.updatedEmployee.name;
        $scope.ids = $scope.updatedEmployee.id;

        $scope.empGender = $scope.updatedEmployee.gender;
        $scope.currentAddress = $scope.updatedEmployee.currentAddress;
        if ($scope.updatedEmployee.permanentAddress == null) {
            $scope.showPermanentAddress = false;
        } else {
            $scope.showPermanentAddress = true;
            if ($scope.updatedEmployee.permanentAddress.id === $scope.updatedEmployee.currentAddress.id) {
                $scope.permanentAddressSame = true;
            } else {
                $scope.permanentAddressSame = false;
            }
        }
        $scope.permanentAddress = $scope.updatedEmployee.permanentAddress;
        $scope.selectedFamily = $scope.updatedEmployee.family;
        $scope.selectedDepartment = $scope.updatedEmployee.department;
        $scope.selectedCompany = $scope.updatedEmployee.company;
        $scope.selectedDesignation = $scope.updatedEmployee.designation;
        $scope.employmentType = $scope.updatedEmployee.employmentType;
        $scope.employmentStatus = $scope.updatedEmployee.employmentStatus;
        $scope.employeeEmail = $scope.updatedEmployee.employeeEmail;
        $scope.selectedReportingManager = $scope.updatedEmployee.reportingManager;
        $scope.biometricId = $scope.updatedEmployee.biometricId;
        $scope.joiningDate = $scope.dateformatting($scope.updatedEmployee.joiningDate);
        $scope.employeeCode = $scope.updatedEmployee.employeeCode;
        $scope.slackId = $scope.updatedEmployee.communicationChannel;
        $scope.eligible = $scope.updatedEmployee.employeeMealEligible;
        $scope.employeeMealEligible = $scope.updatedEmployee.employeeMealEligible;
        $scope.dateOfBirth = $scope.updatedEmployee.dob;
        $scope.terminationReason = $scope.updatedEmployee.reasonForTermination;
        $scope.hrExecutive = $scope.updatedEmployee.hrExecutive;
        $scope.leaveApprovalAuthority = $scope.updatedEmployee.leaveApprovalAuthority;
        $scope.locationCode = $scope.updatedEmployee.locCode;

    }

    function updateEmployeeApplicationMapping(employeeId) {
        var empMappingObj = {};
        if ($scope.selectedMappingType === "OVER_ALL") {
            empMappingObj = {
                employeeId: employeeId,
                mappingType: $scope.selectedMappingType,
                mappingValue: ['OVER_ALL'],
                recordStatus: "ACTIVE"
            }
        } else if ($scope.selectedMappingType === "REGION") {
            empMappingObj = {
                employeeId: employeeId,
                mappingType: $scope.selectedMappingType,
                mappingValue: $scope.selectedRegion,
                recordStatus: "ACTIVE"
            }
        } else if ($scope.selectedMappingType === "CITY") {
            empMappingObj = {
                employeeId: employeeId,
                mappingType: $scope.selectedMappingType,
                mappingValue: $scope.selectedCity,
                recordStatus: "ACTIVE"
            }
        }
        $rootScope.showFullScreenLoader = true;
        $scope.showEmpBtn = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.updateEmpMapping,
            data: empMappingObj,
        }).then(function success(response) {
            if (response.status == 200) {
                $scope.loading = false;
                $scope.showEmpBtn = false;
                window.location.reload();
            } else {
                $rootScope.showFullScreenLoader = false;
                $scope.showEmpBtn = false;
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    $scope.submitUpdateEmployee = function () {
        var empObj = {
            id: $scope.ids,
            name: $scope.empName,
            gender: $scope.empGender,
            primaryContact: $scope.currentAddress.contact1,
            secondaryContact: $scope.currentAddress.contact2,
            biometricId: $scope.biometricId,
            department: $scope.selectedDepartment,
            company: $scope.selectedCompany,
            designation: $scope.selectedDesignation,
            currentAddress: $scope.currentAddress,
            permanentAddress: $scope.permanentAddress,
            employmentType: $scope.employmentType,
            employeeEmail: $scope.employeeEmail,
            employmentStatus: $scope.employmentStatus,
            joiningDate: $scope.joiningDate,
            reportingManager: {
                id: $scope.selectedReportingManager.id
            },
            employeeCode: $scope.employeeCode,
            communicationChannel: $scope.slackId,
            employeeMealEligible: $scope.employeeMealEligible,
            reasonForTermination: $scope.reasonForTermination,
            hrExecutive: $scope.hrExecutive,
            leaveApprovalAuthority: $scope.leaveApprovalAuthority,
            locCode: $scope.locationCode,
            dob: $scope.dateOfBirth
        }
        /*
         * if(!$scope.addEmployeeForm.$valid){ alert("Please fill all the
         * required fields correctly."); return; }
         */

        if ($scope.empName == "" || $scope.empName == null) {
            alert("Employee Name is empty.");
            return false;
        }
        if ($scope.currentAddress.line1 == "" || $scope.currentAddress.line1 == null) {
            alert("line1 is empty.");
            return false;
        }
        if ($scope.currentAddress.city == "" || $scope.currentAddress.city == null) {
            alert("City is empty.");
            return false;
        }
        if ($scope.currentAddress.state == "" || $scope.currentAddress.state == null) {
            alert("State is empty.");
            return false;
        }
        if ($scope.currentAddress.zipCode == "" || $scope.currentAddress.zipCode == "null"
            || $scope.currentAddress.zipCode == null) {
            alert("Zip Code is empty.");
            return false;
        }
        if ($scope.currentAddress.contact1 == "" || $scope.currentAddress.contact1 == "null"
            || $scope.currentAddress.contact1 == null) {
            alert("Contact1 is empty.");
            return false;
        }

        if ($scope.permanentAddressSame == false) {
            if ($scope.permanentAddress.line1 == "" || $scope.permanentAddress.line1 == null) {
                alert("Line 1 is empty in permanent address.");
                return false;
            }

            if ($scope.permanentAddress.city == "" || $scope.permanentAddress.city == "null") {
                alert("City is empty in permanent address.");
                return false;
            }

            if ($scope.permanentAddress.state == "" || $scope.permanentAddress.state == null) {
                alert("State is empty.");
                return false;
            }
            if ($scope.permanentAddress.zipCode == "" || $scope.permanentAddress.zipCode == "null") {
                alert("Zip Code is empty.");
                return false;
            }
            if ($scope.permanentAddress.contact1 == "" || $scope.permanentAddress.contact1 == "null") {
                alert("Contact1 is empty.");
                return false;
            }
        }

        if ($scope.employeeCode == "" || $scope.employeeCode == null) {
            alert("Employee code should not empty.");
            return false;
        }

        if ($scope.joiningDate == "" || $scope.joiningDate == null) {
            alert("Joining Date is empty.");
            return false;
        }
        if ($scope.selectedMappingType == 'REGION') {
            $scope.selectedCity = [];
            if ($scope.selectedRegion == []) {
                alert("Employee Region assigned is empty.");
                return false;
            }
        }
        if ($scope.selectedMappingType == 'CITY') {
            $scope.selectedRegion = [];
            if ($scope.selectedCity == []) {
                alert("Employee City assigned is empty.");
                return false;
            }
        }

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userUpdate,

            data: empObj
        }).then(function success(response) {
            if (response.status == 200) {
                console.log(response.data);
                updateEmployeeApplicationMapping(response.data.id);
                alert("Employee updated successfully!");
                window.location.reload();
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.resetPassword = function (ids) {
        $scope.idss = ids;
        $scope.action = "Reset"
        $("#resetPasswordModal").modal("show");
    }

    $scope.updateRoles = function (employee) {
        $scope.selectedEmployee = employee;
        $scope.empForMapping = employee;
        $scope.uploadedDoc = null;
        $scope.isDocRequired = false;
        $scope.editRolesUserId = employee.id;
        $scope.selectedEmployeeId = null; // Reset selected employee for cloning
        $timeout(function () {
            angular.element('#cloneEmployeeRoleDropdown').val(null).trigger('change');
        }, 0);
        $scope.clonedRoles = []; // To store cloned roles for highlighting
        $scope.activeRoles.forEach(role => {
            $scope.employeeRoleBrandMapping = {
                ...$scope.employeeRoleBrandMapping,
                [role.id]: $scope.brands.reduce((acc, brand) => {
                    acc[brand.brandId] = false;
                    return acc;
                }, {})
            };
        });
        // console.log("Employee Role Mapping Before Marking is ::: ", $scope.employeeRoleBrandMapping);
        $scope.updatedEmployeeRoleMappings = {
            employeeId: $scope.editRolesUserId,
            updatedBy: AppUtil.getCurrentUser().id,
            userPolicyId: ($scope.autoSelectedPolicy != undefined && $scope.autoSelectedPolicy != null) ? $scope.autoSelectedPolicy.userPolicyId : null,
            roleBrandMappings: []
        };
        $scope.activeEmployeeRoles = angular.copy($scope.activeRoles);
        $scope.activeEmployeeRoles.forEach(function (role) {
            role.status = 'IN_ACTIVE';
        });
        $rootScope.showFullScreenLoader = true;
        $scope.fetchEmployeeRoles(employee.id, function (roles) {
            $scope.userActiveRoles = [];
            if (roles != null && roles.length > 0) {
                for (var i in roles) {
                    $scope.activeEmployeeRoles.forEach(function (role) {
                        if (role.id == roles[i].id) {
                            role.status = 'ACTIVE';
                        }
                    });
                }
                $scope.activeEmployeeRoles.sort(function (a, b) {
                    return a.status.localeCompare(b.status);
                });
                $scope.userActiveRoles = roles.map(r => r.id);
                // console.log("User Active Role IDs ", $scope.userActiveRoles);
                $scope.numberPerPage = 15;
                $scope.curtPage = 1;
                $scope.total = $scope.activeEmployeeRoles.length;
                $scope.beforeUpdateRoles = roles.length;
            } else {
                $scope.beforeUpdateRoles = 0;
            }
            roles.forEach(role => {
                var employeeMappedBrands = $scope.convertStringToArray(role.type);
                employeeMappedBrands.forEach(bId => {
                    $scope.employeeRoleBrandMapping[role.id][bId] = true;
                });
            });
            // console.log("Employee role brand mapping after marking is ::: ", $scope.employeeRoleBrandMapping);
            $scope.autoSelectedPolicy = null;
            $scope.appliedPolicy = null;
            var foundPolicy = false;
            $("#editRolesModal").modal("show");
            if (employee.departmentId != null && employee.designationId != null) {
                var departmentDesignation = employee.departmentId + "_" + employee.designationId;
                var countOfActiveRoles = 0;
                for (var i = 0; i < $scope.userPolicies.length; i++) {
                    if (employee.userPolicyId == null) {
                        if ($scope.userPolicies[i].departmentDesignation == departmentDesignation) {
                            foundPolicy = true;
                            $scope.autoSelectedPolicy = $scope.userPolicies[i];
                            bootbox.alert("Auto Applying Roles of User Policy " + $scope.userPolicies[i].policyName);
                            for (var j = 0; j < $scope.activeEmployeeRoles.length; j++) {
                                for (var k = 0; k < $scope.autoSelectedPolicy.userPolicyRoleMappings.length; k++) {
                                    if ($scope.autoSelectedPolicy.userPolicyRoleMappings[k].roleId == $scope.activeEmployeeRoles[j].id) {
                                        $scope.activeEmployeeRoles[j].status = 'ACTIVE';
                                        countOfActiveRoles++;
                                    }
                                }
                            }
                            $scope.activeEmployeeRoles.sort(function (a, b) {
                                return a.status.localeCompare(b.status);
                            });
                            if ($scope.beforeUpdateRoles < countOfActiveRoles) {
                                $scope.isDocRequired = true;
                            }
                            $scope.numberPerPage = 15;
                            $scope.curtPage = 1;
                            $scope.total = $scope.activeEmployeeRoles.length;
                            break;
                        }
                    } else {
                        if (employee.userPolicyId == $scope.userPolicies[i].userPolicyId) {
                            $scope.appliedPolicy = $scope.userPolicies[i];
                            foundPolicy = true;
                            break;
                        }
                    }
                }
            }
            if (!foundPolicy) {
                bootbox.alert("No User Role Policy Found for the Employees Designation and Role. Please Create a Policy..!");
            }
            $scope.filteredRoles = [ ...$scope.activeEmployeeRoles ];
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.uploadRoleEditProof = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.uploadedDoc = null;
        var fd = new FormData();
        var file = fileService.getFile();
        if (file == undefined || file == null) {
            bootbox.alert("Please Select a file to upload..!");
            $rootScope.showFullScreenLoader = false;
            return;
        }

        var fileExt = AppUtil.getFileExtension(file.name);
        var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
        if (file.size > fileLimit) {
            var msg = ""
            if (fileExt.toLowerCase() == 'png') {
                msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
            }
            bootbox.alert('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
            $rootScope.showFullScreenLoader = false;
            return;
        }
        fd.append('file', file);
        fd.append('type', "OTHERS");
        fd.append('mimeType', fileExt.toUpperCase());
        fd.append('userId', AppUtil.getCurrentUser().id);
        fd.append('docType', "EMPLOYEE_ROLE_EDIT");
        fd.append('file', file);
        fd.append('docName', "EMPLOYEE_ROLE_EDIT");

        $http({
            url: AppUtil.restUrls.userManagement.uploadDocument,
            method: 'POST',
            data: fd,
            headers: {'Content-Type': undefined},
            transformRequest: angular.identity
        }).success(function (response) {
            console.log(response);
            $rootScope.showFullScreenLoader = false;
            if (response.documentId == undefined || response.documentId == null) {
                bootbox.alert("Something Went Wrong Please try Again..!");
            } else {
                bootbox.alert("File Uploaded Successfully..!");
                $scope.uploadedDoc = response;
            }
        }).error(function (response) {
            $rootScope.showFullScreenLoader = false;
            alert("Upload failed");
            $scope.uploadedDoc = null;
        });
    };

    // $scope.paginate = function (value) {
    //     var begin, end, index;
    //     begin = ($scope.curtPage - 1) * $scope.numberPerPage;
    //     console.log("begin=", begin);
    //     end = begin + $scope.numberPerPage;
    //     console.log("end=", end);
    //     index = $scope.activeEmployeeRoles.indexOf(value);
    //     console.log("index=", index);
    //     return (begin <= index && index < end);
    // };


    $scope.submitUpdateRoles = function () {

        if ($scope.activeEmployeeRoles == null || $scope.activeEmployeeRoles.length == 0) {
            return;
        }
        $rootScope.showFullScreenLoader = true;
        var roleIds = [];
        $scope.activeEmployeeRoles.forEach(function (role) {
            if (role.status == 'ACTIVE') {
                roleIds.push(role.id);
            }
        });
        if ($scope.beforeUpdateRoles < roleIds.length && $scope.uploadedDoc == null) {
            bootbox.alert("Please Upload Policy Edit Proof..!");
            return;
        }
        var updateObject = {
            employeeId: $scope.editRolesUserId,
            roles: roleIds,
            updatedBy: AppUtil.getCurrentUser().id,
            userPolicyId: ($scope.autoSelectedPolicy != undefined && $scope.autoSelectedPolicy != null) ? $scope.autoSelectedPolicy.userPolicyId : null
        }

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userUpdateRoles,
            data: updateObject,
            params: {
                uploadedDocId: $scope.isDocRequired ? $scope.uploadedDoc.documentId : null
            }
        }).then(function success(response) {

            $rootScope.showFullScreenLoader = false;
            if (response.status === 200) {
                $("#editRolesModal").modal('toggle');
                alert("Employee Roles Updated Successfully!");
                if ($scope.autoSelectedPolicy != null) {
                    $scope.selectedEmployee.userPolicyId = $scope.autoSelectedPolicy.userPolicyId;
                }
            } else {
                alert("Error in updating roles for the user!");
            }

        }, function error(response) {
            $("#editRolesModal").modal('toggle');
            alert("Error in updating roles for the user!");
            $rootScope.showFullScreenLoader = false;

            console.log("error:" + response);
        });

    }

    $scope.submitUpdateRolesV2 = function () {
        $rootScope.showFullScreenLoader = true;

        if ($scope.isDocRequired && $scope.uploadedDoc == null) {
            alert("Kindly Upload Required Document!");
            return false;
        }

        var requestObj = $scope.formatEmployeeRoleMappings();

        // console.log("Final Request Object is ::: ", requestObj);

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userUpdateRolesV2,
            data: requestObj,
            params: {
                uploadedDocId: $scope.isDocRequired ? $scope.uploadedDoc.documentId : null
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response != null && response.status === 200) {
                $scope.onCloseModal();
                alert("Employee Roles Updated Successfully!");
                if ($scope.autoSelectedPolicy != null) {
                    $scope.selectedEmployee.userPolicyId = $scope.autoSelectedPolicy.userPolicyId;
                }
            } else {
                alert("Error in updating roles for the user!");
            }

        }, function error(response) {
            $scope.onCloseModal();
            alert("Error in updating roles for the user!");
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.validateEmployeeRoleMappings = function () {
        if ($scope.updatedEmployeeRoleMappings.roleBrandMappings.length <= 0) {
            alert("No Updations Found!");
            return false;
        }
        for (var i = 0; i < $scope.updatedEmployeeRoleMappings.roleBrandMappings.length; i++) {
            var mapping = $scope.updatedEmployeeRoleMappings.roleBrandMappings[i];
            if (mapping.status == "ACTIVE" && mapping.brands.size <= 0) {
                alert("Kindly Add Atleast 1 Brand Mapping to Role Name: " + mapping.roleName);
                return false;
            }
        }
        return true;
    }

    $scope.fetchEmployeeRoles = function (employeeId, successCallback, errorCallback) {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.allUserRoles,
            data: employeeId
        }).then(function success(response) {
            successCallback(response.data || []);
        }, function error(response) {
            errorCallback(response);
        });
    };

    // Function to handle cloning role mappings from another employee
    $scope.handleCloneRoleClick = function () {
        if (!$scope.selectedEmployeeId) {
            alert("Please select an employee to clone role mappings from.");
            return;
        }

        $rootScope.showFullScreenLoader = true;

         $scope.fetchEmployeeRoles($scope.selectedEmployeeId, function onSuccess(clonedRoles) {
            $rootScope.showFullScreenLoader = false;

            const {
                rolesAdded,
                rolesSkipped,
                brandsAdded,
                brandsSkipped
            } = $scope.processClonedRoles(clonedRoles);

            const message =
                "Employee role mappings cloned successfully!\n" +
                `${rolesAdded} roles added\n${rolesSkipped} duplicate roles skipped`;
            alert(message);

        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("Error cloning role mappings:", response);
        });

    };

    $scope.processClonedRoles = function (clonedRoles) {
        let rolesAdded = 0;
        let rolesSkipped = 0;
        let brandsAdded = 0;
        let brandsSkipped = 0;

        $scope.clonedRoles = clonedRoles;

        clonedRoles.forEach(function (role) {
            const brandIds = role.type ? $scope.convertStringToArray(role.type) : [];

            const existingRole = $scope.activeEmployeeRoles.find(r => r.id === role.id);
            const allBrandsExist = brandIds.every(brandId =>
                $scope.employeeRoleBrandMapping[role.id]?.[brandId]);

            if (existingRole && allBrandsExist) {
                rolesSkipped++;
                brandsSkipped += brandIds.length;
                return;
            }

            let roleMapping = $scope.getOrCreateRoleMapping(role);

            if (!existingRole) {
                $scope.addNewActiveRole(role);
                rolesAdded++;
            } else if (existingRole.status !== 'ACTIVE') {
                existingRole.status = 'ACTIVE';
                rolesAdded++;
            }

            if (!$scope.employeeRoleBrandMapping[role.id]) {
                $scope.employeeRoleBrandMapping[role.id] = {};
            }

            let { added: brandAddCount, skipped: brandSkipCount } = $scope.addBrandsToRole(role.id, brandIds, roleMapping);
            brandsAdded += brandAddCount;
            brandsSkipped += brandSkipCount;
        });

        $scope.activeEmployeeRoles.sort((a, b) => a.status.localeCompare(b.status));
        $scope.filterRolesByBrand();

        return { rolesAdded, rolesSkipped, brandsAdded, brandsSkipped };
    };

    $scope.getOrCreateRoleMapping = function (role) {
        let mapping = $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(m => m.roleId === role.id);

        if (!mapping) {
            $scope.createRoleBrandMappingObject(role, 'ACTIVE', false);
            mapping = $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(m => m.roleId === role.id);
        }

        return mapping;
    };

    $scope.addNewActiveRole = function (role) {
        $scope.activeEmployeeRoles.push({
            id: role.id,
            code: role.code,
            status: 'ACTIVE'
        });
    };

    $scope.addBrandsToRole = function (roleId, brandIds, roleMapping) {
        let added = 0;
        let skipped = 0;

        brandIds.forEach(function (brandId) {
            if (!$scope.employeeRoleBrandMapping[roleId][brandId]) {
                $scope.employeeRoleBrandMapping[roleId][brandId] = true;
                roleMapping.brands.add(brandId);
                added++;
            } else {
                skipped++;
            }
        });

        return { added, skipped };
    };

    $scope.formatEmployeeRoleMappings = function () {
        var requestObj = {
            employeeId: $scope.updatedEmployeeRoleMappings.employeeId,
            updatedBy: $scope.updatedEmployeeRoleMappings.updatedBy,
            userPolicyId: $scope.updatedEmployeeRoleMappings.userPolicyId,
            roleBrandMappings: []
        }
        $scope.updatedEmployeeRoleMappings.roleBrandMappings.forEach(mapping => {
            var obj = {
                id: mapping.roleId,
                name: mapping.roleName,
                status: mapping.status,
                type: $scope.convertSetToArray(mapping.brands).join(", ")
            }
            requestObj.roleBrandMappings.push(obj);
        });
        return requestObj;
    }

    $scope.submitResetPassword = function (employeeId) {
        $scope.actionPwd = true;
        var empResetObj = {
            employeeId: employeeId,
            newPasscode: $scope.pw1,
            updatedBy: AuthService.getAuthorization().userId,
        }
        console.log($scope.pw1 + "???" + $scope.pw2);
        if ($scope.pw1 == $scope.pw2) {
            $scope.actionPwd = true;
        } else {
            $scope.actionPwd = false;
            return false;
        }

        if ($scope.pw1 === $scope.pw2) {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.userManagement.userResetPasscode,
                data: empResetObj
            }).then(function success(response) {
                if (response.status == 200) {
                    alert("Password Reset successfully!");
                    window.location.reload();
                } else {
                    console.log(response);
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

    $scope.inActiveEmployee = function (ids) {
        var employeeId = ids;

        if ($window.confirm("Are you sure, you want to deactivate employee?")) {
            console.log("YES");
            ;
        } else {
            return false;
        }

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.userDeactivate,
            data: employeeId
        }).then(function success(response) {
            if (response.status == 200) {
                alert("Employee Inactivate Successfully!");
                window.location.reload();
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });

    };

    $scope.downloadCSV = function (args) {

        var data, filename, link;

        var csv = convertArrayOfObjectsToCSV({
            data: $scope.downloadEmployeeList
        });
        if (csv == null)
            return;

        filename = args.filename || 'export.csv';

        if (!csv.match(/^data:text\/csv/i)) {
            csv = 'data:text/csv;charset=utf-8,' + csv;
        }
        data = encodeURI(csv);

        link = document.createElement('a');
        link.setAttribute('href', data);
        link.setAttribute('download', filename);
        link.click();
    };

    $scope.changeRoleStatus = function (role) {
        if (role.status == "ACTIVE") {
            role.status = "IN_ACTIVE";
            $scope.brands.forEach(brand => {
                $scope.employeeRoleBrandMapping[role.id][brand.brandId] = false;
            });
            var roleMappingToChange = $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id);
            if (roleMappingToChange == undefined || roleMappingToChange == null) {
                $scope.createRoleBrandMappingObject(role, "IN_ACTIVE", true);
            } else {
                $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id).status = "IN_ACTIVE";
                $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id).brands = new Set([]);
            }
        } else {
            role.status = "ACTIVE";
            var roleMappingToChange = $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id);
            if (roleMappingToChange == undefined || roleMappingToChange == null) {
                $scope.createRoleBrandMappingObject(role, "ACTIVE", true);
            } else {
                $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id).status = "ACTIVE";
                $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id).brands = new Set([]);
            }
        }
        // console.log("Updated Employee Role Brand Mappings are ::: ", $scope.updatedEmployeeRoleMappings);
        $scope.isDocRequired = false;
        var activeRoles = 0;
        for (var i = 0; i < $scope.activeEmployeeRoles.length; i++) {
            if ($scope.activeEmployeeRoles[i].status == "ACTIVE") {
                activeRoles++;
            }
        }
        if ($scope.beforeUpdateRoles < activeRoles) {
            $scope.isDocRequired = true;
        }
    };

    $scope.downloadOnboardingSheet = function () {
        $rootScope.showDetailLoader = true;
        $http(
            {
                method: 'GET',
                url: AppUtil.restUrls.userManagement.downloadOnboardingSheet,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            })
            .then(
                function success(response) {
                    if (response != undefined
                        && response != null) {
                        var fileName = "ONBOARDING_TEMPLATE" + " - "
                            + Date.now() + ".xls";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }
                    $rootScope.showDetailLoader = false;
                },
                function error(response) {
                    console.log("error:" + response);
                    alert("Unable to download Template");
                    $rootScope.showDetailLoader = false;
                });
    }

    function convertArrayOfObjectsToCSV(args) {
        var result, ctr, keys, columnDelimiter, lineDelimiter, data;

        data = args.data || null;
        if (data == null || !data.length) {
            return null;
        }

        columnDelimiter = args.columnDelimiter || ',';
        lineDelimiter = args.lineDelimiter || '\n';

        keys = Object.keys(data[0]);

        result = '';
        result += keys.join(columnDelimiter);
        result += lineDelimiter;

        data.forEach(function (item) {
            ctr = 0;
            keys.forEach(function (key) {
                if (ctr > 0)
                    result += columnDelimiter;

                result += item[key];
                ctr++;
            });
            result += lineDelimiter;
        });

        return result;
    }

    $scope.hasRoleType = function (role, type) {
        if (!role || !role.type) {
            return false;
        }
        var typeArray = role.type.split(', ').map(function(s) {
            return s.trim();
        });
        return typeArray.includes(type);
    };

    $scope.filterRolesByBrand = function () {

        if ($scope.selectedBrandsForFiltering.length <= 0) {
            $scope.filteredRoles = [ ...$scope.activeEmployeeRoles ];
        } else {
            var selectedBrandIds = $scope.selectedBrandsForFiltering.map(b => b.brandId.toString());

            $scope.filteredRoles = $scope.activeEmployeeRoles.filter(role => {
                if (!role || !role.type) return false;
                var typeArray = role.type.split(', ').map(s => s.trim());
                return typeArray.some(type => selectedBrandIds.includes(type));
            });
        }

    };

    $scope.toggleBrandMapping = function (role, brandId) {
        brandId = parseInt(brandId);
        $scope.employeeRoleBrandMapping[role.id][brandId] = !$scope.employeeRoleBrandMapping[role.id][brandId];
        var mapping = $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId == role.id);
        if (mapping != undefined || mapping != null) {
            if ($scope.employeeRoleBrandMapping[role.id][brandId] === true) {
                $scope.updatedEmployeeRoleMappings.roleBrandMappings
                .find(rbm => rbm.roleId == role.id).brands.add(brandId);
            } else {
                $scope.updatedEmployeeRoleMappings.roleBrandMappings
                .find(rbm => rbm.roleId == role.id).brands.delete(brandId);
            }
        } else {
            $scope.createRoleBrandMappingObject(role, "ACTIVE", false);
            if ($scope.employeeRoleBrandMapping[role.id][brandId] === true) {
                $scope.updatedEmployeeRoleMappings.roleBrandMappings
                .find(rbm => rbm.roleId == role.id).brands.add(brandId);
            } else {
                $scope.updatedEmployeeRoleMappings.roleBrandMappings
                .find(rbm => rbm.roleId == role.id).brands.delete(brandId);
            }
        }
        // console.log("Updated Employee Role Brand Mappings are ::: ", $scope.updatedEmployeeRoleMappings);
    }

    $scope.onCloseModal = function () {
        $scope.selectedBrandsForFiltering = [];
        $scope.employeeRoleBrandMapping = {};
        $scope.updatedEmployeeRoleMappings = {};
        $scope.modalTabIndex = 0;
        $("#editRolesModal").modal("hide");
    }
$scope.moveForward = function () {
        if ($scope.validateEmployeeRoleMappings() == false) { return; }
        $scope.modalTabIndex++;
    }

    $scope.moveBack = function () {
        if ($scope.modalTabIndex > 0) {
            $scope.modalTabIndex--;
        }
    }

    $scope.convertSetToArray = function (set) {
        return Array.from(set).sort((a, b) => a - b);
    }

    $scope.convertStringToArray = function (str) {
        return str.split(',').map(num => parseInt(num.trim(), 10));
    }

    $scope.formatBrands = function (arr) {
        var formattedArray = [];
        $scope.brands.forEach(brand => {
            if (arr.includes(brand.brandId)) {
                formattedArray.push(brand.brandName);
            }
        });
        return formattedArray.join(', ');
    }

    $scope.createRoleBrandMappingObject = function (role, status, isNew) {
        $scope.updatedEmployeeRoleMappings.roleBrandMappings = [ ...$scope.updatedEmployeeRoleMappings.roleBrandMappings,
            {
                roleId: role.id,
                roleName: role.code,
                status: status,
                brands: isNew ? new Set([]) : new Set($scope.convertStringToArray(role.type))
            }
        ];
        // console.log("Updated Role BRand mappings is ::: ", $scope.updatedEmployeeRoleMappings.roleBrandMappings)
        var brandSet = $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId === role.id).brands;
        if (brandSet) {
            var filteredBrandSet = Object.keys($scope.employeeRoleBrandMapping[role.id] || {})
                .filter(brandId => $scope.employeeRoleBrandMapping[role.id][brandId] === true)
                .map(brandId => parseInt(brandId, 10));

            brandSet = new Set(filteredBrandSet);
        }
        $scope.updatedEmployeeRoleMappings.roleBrandMappings.find(rbm => rbm.roleId === role.id).brands = brandSet;
    }
});

adminapp.filter('startFrom', function () {
    return function (input, start) {
        if (input) {
            start = +start; // parse to int
            return input.slice(start);
        }
        return [];
    };
});

adminapp.directive('pwCheck', [function () {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            var firstPassword = '#' + attrs.pwCheck;
            elem.add(firstPassword).on('keyup', function () {
                scope.$apply(function () {
                    var v = elem.val() === $(firstPassword).val();
                    ctrl.$setValidity('pwmatch', v);
                });
            });
        }
    }
}

]);

adminapp.directive('myMaxlength', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModelCtrl) {
            var maxlength = Number(attrs.myMaxlength);

            function fromUser(text) {
                if (text.length > maxlength) {
                    var transformedInput = text.substring(0, maxlength);
                    ngModelCtrl.$setViewValue(transformedInput);
                    ngModelCtrl.$render();
                    return transformedInput;
                }
                return text;
            }

            ngModelCtrl.$parsers.push(fromUser);
        }
    };
});