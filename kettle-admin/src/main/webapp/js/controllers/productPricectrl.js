/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("PriceListController",function($scope, $rootScope, $http, AppUtil) {
	$scope.init = function() {
		$scope.checkboxModel = {
				value : 'NO'
		}
	}
	$scope.prodListData={};
	$scope.productFinalList={};
	$scope.loading = false;	
	$http({
		method: 'GET',
		url: AppUtil.restUrls.unitMetaData.families
	}).then(function success(response) {
		$scope.families = response.data;
		$scope.families.push('ALL');
		$scope.newUnitFamily = $scope.families[0];
	}, function error(response) {
		console.log("error:"+response);
	});

	$http({
		method: 'GET',
		url: AppUtil.restUrls.unitMetaData.regions
	}).then(function success(response) {
		$scope.regions = response.data;
		$scope.regions.push('ALL');
		$scope.newUnitRegion = $scope.regions[0];
	}, function error(response) {
		console.log("error:"+response);
	});
	$http({
		url : AppUtil.restUrls.productMetaData.products,
		dataType : 'json',
		method : 'POST',
		data : '',
		headers : {"Content-Type" : "application/json"}
	}).then(function success(response) {
		$scope.productDetails = response.data;
		$scope.selectProductDetail = $scope.productDetails[0];
		$scope.prodListData = $scope.productDetails[0];
		for ( var index in $scope.productDimension) {
			if ($scope.productDimension[index].detail.id == $scope.prodListData.dimensionProfileId) {
				$scope.selectProductDimensionData = $scope.productDimension[index].content;
				$scope.selectProductDimension = $scope.selectProductDimensionData[0];
			}
		}
	}, function error(response) {
		console.log("error:" + response);
	});
	$http({
		method: 'GET',
		url: AppUtil.restUrls.unitMetaData.listTypes
	}).then(function success(response) {
		$scope.categoryLists = response.data;
		$scope.productCategory=$scope.categoryLists.CATEGORY;
		$scope.selectedProductType =$scope.productCategory[0];
		$scope.productDimension=$scope.categoryLists.DIMENSION;
		$scope.selectetProductDimension = $scope.productDimension[0];
	});

	$scope.showProductList = function(id){
		for(var index in $scope.productDimension){
			if($scope.productDimension[index].detail.id==id)
			{
				$scope.selectProductDimensionData=$scope.productDimension[index].content;
				$scope.selectProductDimension=$scope.selectProductDimensionData[0];
			}
		}
	}
	$scope.showPriceDataList = function(){
		if($scope.selectProductType==null || $scope.selectProductType==""){
			alert("Please select Category");
			return false;
		}
		if($scope.selectProductSubtype==null || $scope.selectProductSubtype==""){
			alert("Please select Sub Category");
			return false;
		}

		if($scope.selectProductDetail.name==null || $scope.selectProductDetail.name==undefined)	{
			alert("Please select Product");
			return false;
		}

		if($scope.selectProductDimension1==null || $scope.selectProductDimension1=="")	{
			alert("Please select Dimension");
			return false;
		}

		$rootScope.showFullScreenLoader = true;
		var unitCategory	=	$scope.newUnitFamily;
		var unitRegion 		=	$scope.newUnitRegion;
		var productId 		=	$scope.selectProductDetail.id;
		var dimensionId 	=	$scope.selectProductDimension1.id;

		$scope.allChecked					=	"NO";
		$scope.checkboxModel				=	"NO";
		$scope.checkboxCafeModel			=	"NO";
		$scope.checkboxCodModel				=	"NO";
		$scope.checkboxBufferModel			=	"NO";
		$scope.checkboxThresholdModel		=	"NO";

		$scope.updatePriceInput				=	"";
		$scope.updateCafeCost				=	"";
		$scope.updateCodCost				=	"";
		$scope.updateBufferCost				=	"";
		$scope.updateThreshold				=	"";
		for(i in $scope.productListDetails){

			$scope.productUpdatedPrice[$scope.productListDetails[i].unit.id]="";
			$scope.cafeCost[$scope.productListDetails[i].unit.id]="";
			$scope.codCost[$scope.productListDetails[i].unit.id]="";
			$scope.bufferValue[$scope.productListDetails[i].unit.id]="";
			$scope.threshValue[$scope.productListDetails[i].unit.id]="";
			$scope.sel[$scope.productListDetails[i].unit.id]={checked:false};
		}
		/*for(var categorySubDet in $scope.sel)
{
	console.log("checkedData142",$scope.sel);
	//$scope.sel[categorySubDet];
	//console.log($scope.sel[categorySubDet].changeBuffer.push(""));
	$scope.sel[categorySubDet].checked=false;

}*/
		if ($scope.newUnitFamily == "ALL") {
			$scope.newUnitFamilyData = null;
		} else {
			$scope.newUnitFamilyData = $scope.newUnitFamily;
		}

		if ($scope.newUnitRegion == "ALL") {
			$scope.newUnitRegionData = null;
		} else {
			$scope.newUnitRegionData = $scope.newUnitRegion;
		}
		var payload = $.param({ unitCategory: $scope.newUnitFamilyData,
			unitRegion:$scope.newUnitRegionData,
			productId: $scope.selectProductDetail.id,
			dimensionId: $scope.selectProductDimension1.id});
		$http({
			url : AppUtil.restUrls.productMetaData.productPriceMapping,
			method : "POST",
			headers : {'Content-Type' : 'application/x-www-form-urlencoded'
			},data : payload
		}).success(
				function(data) {
					$scope.productListDetails = data
					for (i in $scope.productListDetails) {
						if ($scope.productListDetails[i].price == null) {
							$scope.productListDetails[i].price = {};
							$scope.productListDetails[i].price.price = 0;
							$scope.productListDetails[i].price.dimension = $scope.selectProductDimension1.name;
							$scope.productListDetails[i].price.codCost = 0;
							$scope.productListDetails[i].price.cost = 0;
							$scope.productListDetails[i].price.changeCodCosts = 0;
							$scope.productListDetails[i].price.changeCafecosts = 0;
							$scope.productListDetails[i].price.receipe = null;
							$scope.productListDetails[i].price.buffer = 0;
							$scope.productListDetails[i].price.threshold = 0;
							$scope.productListDetails[i].price.id = null;
						}
					}
					$("#viewProductPriceDiv").show();
					$rootScope.showFullScreenLoader = false;
				});

	}	
	$scope.sel = {};
	$scope.checkCompleteObj={};
	$scope.productUpdatedPrice = [];
	$scope.cafeCost = [];
	$scope.codCost = [];
	$scope.bufferValue = [];
	$scope.threshValue = [];

	$scope.checkAll = function(){

		if($scope.allChecked=='YES'){
			$scope.checkboxModel={value: "NO"}
			$scope.checkboxCafeModel={value: "NO"}
			$scope.checkboxCodModel={value: "NO"}
			$scope.checkboxBufferModel={value: "NO"}
			$scope.checkboxThresholdModel={value: "NO"}
			for(i in $scope.productListDetails){
				if($scope.productListDetails[i].price==null)
				{
					$scope.productListDetails[i].price={};
					$scope.productListDetails[i].price.price=0;
					$scope.productListDetails[i].price.dimension=$scope.selectProductDimension.name;
					$scope.productListDetails[i].price.codCost=0;
					$scope.productListDetails[i].price.cost=0;
					$scope.productListDetails[i].price.changeCodCosts=0;
					$scope.productListDetails[i].price.changeCafecosts=0;
					$scope.productListDetails[i].price.id=null;
				}
				//console.log("ProdObj=",$scope.productListDetails[i].unit.id);
				$scope.sel[$scope.productListDetails[i].unit.id]={checked:true,unintNamee:$scope.productListDetails[i].unit.name,}

				$scope.sel[$scope.productListDetails[i].unit.id]=true;
				$scope.sel[$scope.productListDetails[i].unit.id]=
				{
						idd:$scope.productListDetails[i].id,
						checked:true,
						price:$scope.productListDetails[i].price.price,
						currentPrices:$scope.productListDetails[i].price.price,
						unintNamee:$scope.productListDetails[i].unit.name,
						dimension:$scope.productListDetails[i].price.dimension,
						product:$scope.productListDetails[i].product.name,
						unitIDs:$scope.productListDetails[i].unit.id,
						productIDs:$scope.productListDetails[i].product.id,
						priceeID:$scope.productListDetails[i].price.id,
						cafeCosts:$scope.productListDetails[i].price.cost,
						codCosts:$scope.productListDetails[i].price.codCost,
						changeCodCosts:$scope.productListDetails[i].price.codCost,
						changeCafecosts:$scope.productListDetails[i].price.cost,
						buffer:$scope.productListDetails[i].price.buffer,
						threshold:$scope.productListDetails[i].price.threshold,
						changeBuffer:$scope.productListDetails[i].price.buffer,
						changeThreshold:$scope.productListDetails[i].price.threshold,
				};

				$scope.productUpdatedPrice[$scope.productListDetails[i].unit.id]=$scope.sel[$scope.productListDetails[i].unit.id].price;
				$scope.cafeCost[$scope.productListDetails[i].unit.id]=$scope.sel[$scope.productListDetails[i].unit.id].cafeCosts;
				$scope.codCost[$scope.productListDetails[i].unit.id]=$scope.sel[$scope.productListDetails[i].unit.id].codCosts;
				$scope.bufferValue[$scope.productListDetails[i].unit.id]=$scope.sel[$scope.productListDetails[i].unit.id].buffer;
				$scope.threshValue[$scope.productListDetails[i].unit.id]=$scope.sel[$scope.productListDetails[i].unit.id].threshold;
			}
		}
		else
		{
			for(i in $scope.productListDetails){
				$scope.sel[$scope.productListDetails[i].unit.id]=false;
				$scope.productUpdatedPrice[$scope.productListDetails[i].unit.id]="";
				$scope.cafeCost[$scope.productListDetails[i].unit.id]="";
				$scope.codCost[$scope.productListDetails[i].unit.id]="";
				$scope.bufferValue[$scope.productListDetails[i].unit.id]="";
				$scope.threshValue[$scope.productListDetails[i].unit.id]="";
				//$scope.thresholdValue[$scope.productListDetails[i].unit.id]="";
			}
		}
	}
	$scope.showSubCategoryDetails=function(id)
	{
		var newProdList = [];
		$scope.productDetails.forEach(function(viewproductDetailsList){
			if(viewproductDetailsList.subType===id){
				newProdList.push(viewproductDetailsList);
			}
		});
		$scope.allProductList=newProdList;
		$scope.selectProductDetail=$scope.allProductList;
	}

	$scope.showDimensionDetails=function(id){
		for(var index in $scope.productDimension){
			if($scope.productDimension[index].detail.id==id){
				$scope.selectProductDimensionData1=$scope.productDimension[index].content;
				$scope.selectProductDimension=$scope.selectProductDimensionData1[0];
			}
		}
	}

	$scope.showCategoryDetails = function(id){
		$scope.selectedProductSubTypeData=id;
	} 
	$scope.checkedAllApply= function(checkedApplys)
	{
		if($scope.checkboxModel=='YES' && $scope.updatePriceInput=="")	{
			alert("Current Price can not empty");
			$scope.checkboxModel='NO'
				return false;
		}
		if($scope.updatePriceInput=="" || $scope.updatePriceInput==null){
			if($scope.checkboxModel=='YES')	{
				$scope.checkboxModel='NO';
			}
			return false;
		}
		var checkTypeValue="";
		for(var categorySubDet in $scope.sel){
			if($scope.sel[categorySubDet].checked==true){
				checkTypeValue=true;
			}
		}
		if(checkTypeValue!=true){
			alert("checkbox is not checked");
			$scope.checkboxModel='NO';
			return false;
		}
		else {
			for(var categorySubDet in $scope.sel){
				if($scope.sel[categorySubDet].checked==true){
					checkTypeValue=true;
					$scope.productUpdatedPrice[categorySubDet]=$scope.updatePriceInput;
					$scope.sel[categorySubDet].price=$scope.updatePriceInput;
				}
				if($scope.checkboxModel=='NO'){
					$scope.sel[categorySubDet].price=$scope.sel[categorySubDet].currentPrices;
					$scope.productUpdatedPrice[categorySubDet]=$scope.sel[categorySubDet].price;
				}
			}
		}
	}


	$scope.checkedCafeAllApply= function(checkedCafeApply)
	{
		if($scope.checkboxCafeModel=='YES' && $scope.updateCafeCost=="")	{
			alert("Current Cafe Cost can not empty");
			$scope.checkboxCafeModel='NO'
				return false;
		}
		var checkTypeCafeValue="";
		for(var categoryCafeDet in $scope.sel){
			if($scope.sel[categoryCafeDet].checked==true)
			{
				checkTypeCafeValue=true;
			}
		}
		if(checkTypeCafeValue!=true)
		{
			alert("checkbox is not checked");
			$scope.checkboxCafeModel='NO';
			return false;
		}
		else
		{
			for(var categoryCafeDet in $scope.sel)
			{
				if($scope.sel[categoryCafeDet].checked==true){
					checkTypeCafeValue=true;
					$scope.cafeCost[categoryCafeDet]=$scope.updateCafeCost;
					$scope.sel[categoryCafeDet].changeCafecosts=$scope.updateCafeCost;
				}
				if($scope.checkboxCafeModel=='NO'){
					$scope.sel[categoryCafeDet].changeCafecosts=$scope.sel[categoryCafeDet].cafeCosts;
					$scope.cafeCost[categoryCafeDet]=$scope.sel[categoryCafeDet].cafeCosts;
				}

			}
		}

	}

	$scope.checkedCodAllApply= function(checkedCodApply)
	{
		if($scope.checkboxCodModel=='YES' && $scope.updateCodCost==""){
			alert("Current COD Cost can not empty");
			$scope.checkboxCodModel='NO'
				return false;
		}

		var checkTypeCodValue="";
		for(var categoryCodDet in $scope.sel){
			if($scope.sel[categoryCodDet].checked==true){
				checkTypeCodValue=true;
			}
		}
		if(checkTypeCodValue!=true)	{
			alert("checkbox is not checked");
			$scope.checkboxCodModel='NO';
			return false;
		}
		else
		{
			for(var categoryCodDett in $scope.sel)
			{
				if($scope.sel[categoryCodDett].checked==true){
					checkTypeCodValue=true;
					$scope.codCost[categoryCodDett]=$scope.updateCodCost;
					$scope.sel[categoryCodDett].changeCodCosts=$scope.updateCodCost;
				}
				if($scope.checkboxCodModel=='NO'){
					$scope.sel[categoryCodDett].changeCodCosts=$scope.sel[categoryCodDett].codCosts;
					$scope.codCost[categoryCodDett]=$scope.sel[categoryCodDett].codCosts;
				}
			}
		}
	}

	$scope.checkedBufferAllApply= function(checkedBufferApply)
	{
		if($scope.checkboxBufferModel=='YES' && $scope.updateBufferCost==""){
			alert("Current Buffer can not empty");
			$scope.checkboxBufferModel='NO'
				return false;
		}
		var checkTypeBufferValue="";
		for(var categoryBufferDet in $scope.sel)
		{
			if($scope.sel[categoryBufferDet].checked==true)	{
				checkTypeBufferValue=true;
			}
		}
		if(checkTypeBufferValue!=true){
			alert("checkbox is not checked");
			$scope.checkboxBufferModel='NO';
			return false;
		}
		else
		{
			for ( var categoryBufferDet in $scope.sel) {
				if ($scope.sel[categoryBufferDet].checked == true) {
					checkTypeBufferValue = true;
					$scope.bufferValue[categoryBufferDet] = $scope.updateBufferCost;
					$scope.sel[categoryBufferDet].changeBuffer = $scope.updateBufferCost;
				}
				if ($scope.checkboxBufferModel == 'NO') {
					$scope.sel[categoryBufferDet].changeBuffer = $scope.sel[categoryBufferDet].buffer;
					$scope.bufferValue[categoryBufferDet] = $scope.sel[categoryBufferDet].buffer;
				}
			}
		}
	}

	$scope.checkedThresholdAllApply= function(checkedThreshApply){
		if($scope.checkboxThresholdModel=='YES' && $scope.updateThreshold==""){
			alert("Current Threshold can not empty");
			$scope.checkboxThresholdModel='NO'
				return false;
		}

		var checkTypeThreshValue="";
		for(var categoryThresholdDet in $scope.sel){
			if($scope.sel[categoryThresholdDet].checked==true){
				checkTypeThreshValue=true;
			}
		}
		if(checkTypeThreshValue!=true){
			alert("checkbox is not checked");
			$scope.checkboxThresholdModel='NO';
			return false;
		}
		else
		{
			for(var categoryThresholdDet in $scope.sel){
				if($scope.sel[categoryThresholdDet].checked==true){
					checkTypeThreshValue=true;
					$scope.threshValue[categoryThresholdDet]			=	$scope.updateThreshold;
					$scope.sel[categoryThresholdDet].changeThreshold	=	$scope.updateThreshold;
				}
				if($scope.checkboxThresholdModel=='NO'){
					$scope.sel[categoryThresholdDet].changeThreshold	=	$scope.sel[categoryThresholdDet].threshold;
					$scope.threshValue[categoryThresholdDet]		=	$scope.sel[categoryThresholdDet].threshold;
				}
			}
		}
	}
	$scope.checkDataOnly = function(id,priceID,unitName,currentPrice,chkbox,unitID,productID,priceeID,cafeCostData,codCostData,bufferData,thresholdData){

		var family=$scope.newUnitFamily;
		var region=$scope.newUnitRegion;
		var dimensionName=$scope.selectProductDimension.name;
		var productName=$scope.selectProductDetail.name
		$scope.checkboxModel={value: "NO"}
		if(chkbox){
			$scope.sel[unitID]={idd:id,checked:chkbox,price:currentPrice,currentPrices:currentPrice,unintNamee:unitName,dimension:dimensionName,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:cafeCostData,codCosts:codCostData,changeCodCosts:codCostData,changeCafecosts:cafeCostData,changeBuffer:bufferData,buffer:bufferData,threshold:thresholdData,changeThreshold:thresholdData};
		}else{
			$scope.sel[unitID]={idd:id,checked:chkbox,price:"",currentPrices:"",unintNamee:unitName,dimension:dimensionName,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:"",codCosts:"",changeCodCosts:"",changeCafecosts:"",changeBuffer:"",changeThreshold:""};
			$scope.checkCompleteObj=$scope.sel[unitID];
		}
		$scope.productUpdatedPrice[unitID]=	$scope.sel[unitID].price;
		$scope.cafeCost[unitID]=$scope.sel[unitID].cafeCosts;
		$scope.codCost[unitID]=$scope.sel[unitID].codCosts;
		$scope.bufferValue[unitID]=$scope.sel[unitID].changeBuffer;
		$scope.threshValue[unitID]=$scope.sel[unitID].changeThreshold;
	}


	$scope.changeUpdatePrice = function(id,priceID,unitName,currentPrice,chkbox,dimensionName,productName,unitID,productID,priceeID,cafeCostData,codCostData,bufferData,thresholdData){
		var prices=$scope.productUpdatedPrice[unitID];
		var changeCafeCost=$scope.cafeCost[unitID];
		var changeCodCost=$scope.codCost[unitID];
		var bufferDataResult=$scope.bufferValue[unitID];
		var thresholdDataResult=$scope.threshValue[unitID];
		if(chkbox==false || chkbox==undefined)
		{
			alert(unitName+" is not checked");
			$scope.productUpdatedPrice[unitID]=currentPrice;
			return false
		}

		if(chkbox==true)
		{
			$scope.sel[unitID]={idd:id,checked:chkbox,price:prices,currentPrices:currentPrice,unintNamee:unitName,dimension:$scope.selectProductDimension1.code,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:cafeCostData,codCosts:codCostData,changeCafecosts:changeCafeCost,changeCodCosts:changeCodCost,changeBuffer:bufferDataResult,changeThreshold:thresholdDataResult};
		}
	}


	$scope.changeCafePrice = function(id,priceID,unitName,currentPrice,chkbox,dimensionName,productName,unitID,productID,priceeID,cafeCostData,codCostData,bufferData,thresholdData){
		var prices				=	$scope.productUpdatedPrice[unitID];
		var changeCafeCost 		= 	$scope.cafeCost[unitID];
		var changeCodCost		=	$scope.codCost[unitID];
		var codCost				=	$scope.codCost[unitID];
		var bufferDataResult	=	$scope.bufferValue[unitID];
		var thresholdDataResult	=	$scope.threshValue[unitID];
		if(chkbox==false || chkbox==undefined)
		{
			alert(unitName+" is not checked");
			$scope.codCost[unitID]=cafeCost;
			return false
		}
		if(chkbox==true)
		{
			$scope.sel[unitID]={idd:id,checked:chkbox,price:prices,currentPrices:currentPrice,unintNamee:unitName,dimension:$scope.selectProductDimension1.code,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:cafeCostData,codCosts:codCostData,changeCafecosts:changeCafeCost,changeCodCosts:changeCodCost,changeBuffer:bufferDataResult,changeThreshold:thresholdDataResult};
		}
	}

	$scope.changeCodPrice = function(id,priceID,unitName,currentPrice,chkbox,dimensionName,productName,unitID,productID,priceeID,cafeCostData,codCostData,bufferData,thresholdData){
		var prices				=	$scope.productUpdatedPrice[unitID];
		var changeCafeCost 		= 	$scope.cafeCost[unitID];
		var changeCodCost		=	$scope.codCost[unitID];
		var bufferDataResult	=	$scope.bufferValue[unitID];
		var thresholdDataResult	=	$scope.threshValue[unitID];
		var codCost				=	$scope.codCost[unitID];
		if(chkbox==false || chkbox==undefined)
		{
			alert(unitName+" is not checked");
			$scope.codCost[unitID]=codCost;
			return false
		}
		if(chkbox==true)
		{
			$scope.sel[unitID]={idd:id,checked:chkbox,price:prices,currentPrices:currentPrice,unintNamee:unitName,dimension:$scope.selectProductDimension1.code,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:cafeCostData,codCosts:codCostData,changeCafecosts:changeCafeCost,changeCodCosts:changeCodCost,changeBuffer:bufferDataResult,changeThreshold:thresholdDataResult};
		}
	}

	$scope.changeBufferValue = function(id,priceID,unitName,currentPrice,chkbox,dimensionName,productName,unitID,productID,priceeID,cafeCostData,codCostData,bufferData,thresholdData){
		var prices				=	$scope.productUpdatedPrice[unitID];
		var changeCafeCost 		= 	$scope.cafeCost[unitID];
		var changeCodCost		=	$scope.codCost[unitID];
		var changeBufferCost	=	$scope.bufferValue[unitID];
		var changeThreshCost	=	$scope.threshValue[unitID];
		if(chkbox==false || chkbox==undefined)
		{
			alert(unitName+" is not checked");
			$scope.bufferValue[unitID]=changeBufferCost;
			return false
		}
		if(chkbox==true)
		{
			$scope.sel[unitID]={idd:id,checked:chkbox,price:prices,currentPrices:currentPrice,unintNamee:unitName,dimension:$scope.selectProductDimension1.code,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:cafeCostData,codCosts:codCostData,changeCafecosts:changeCafeCost,changeCodCosts:changeCodCost,changeBuffer:changeBufferCost,buffer:bufferData,threshold:thresholdData,changeThreshold:changeThreshCost};
		}
	}
	$scope.changeThresholdValue = function(id,priceID,unitName,currentPrice,chkbox,dimensionName,productName,unitID,productID,priceeID,cafeCostData,codCostData,bufferData,thresholdData){
		var prices				=	$scope.productUpdatedPrice[unitID];
		var changeCafeCost 		= 	$scope.cafeCost[unitID];
		var changeCodCost		=	$scope.codCost[unitID];
		var changeBufferCost	=	$scope.bufferValue[unitID];
		var changeThreshCost	=	$scope.threshValue[unitID];
		if(chkbox==false || chkbox==undefined)
		{
			alert(unitName+" is not checked");
			$scope.threshValue[unitID]=changeThreshCost;
			return false
		}
		if(chkbox==true)
		{
			$scope.sel[unitID]={idd:id,checked:chkbox,price:prices,currentPrices:currentPrice,unintNamee:unitName,dimension:$scope.selectProductDimension1.code,product:productName,unitIDs:unitID,productIDs:productID,priceeID:priceeID,cafeCosts:cafeCostData,codCosts:codCostData,changeCafecosts:changeCafeCost,changeCodCosts:changeCodCost,changeBuffer:changeBufferCost,buffer:bufferData,threshold:thresholdData,changeThreshold:changeThreshCost};
		}
	}


	$scope.viewProductPriceList = function(){
		$scope.productFinalList={};
		var flag=true;
		var flag1=true;
		var flag2=true;
		$scope.buttonNotDisplay="";
		//var flagFalse=false;
		var checkVal=angular.equals({}, $scope.sel);
		if(checkVal==true)
		{
			alert("check box is empty")
			return false;
		}


		for(var index in $scope.sel)
		{
			if($scope.sel[index].checked==true)
			{
				if($scope.sel[index].price=="null")
				{
					flag=false;
					alert($scope.sel[index].unintNamee + " can not Empty")
					break;
				}
				else
				{
					$scope.productFinalList[index]=$scope.sel[index];
				}
				if($scope.sel[index].changeCafecosts==null || $scope.sel[index].changeCafecosts=="0" || $scope.sel[index].changeCafecosts=='null')
				{
					flag1=false;
					alert($scope.sel[index].unintNamee + " Cafe Cost can not Empty or zero")
					break;
				}

				if($scope.sel[index].changeCodCosts==null || $scope.sel[index].changeCodCosts=="0" || $scope.sel[index].changeCodCosts=='null')
				{
					flag2=false;
					alert($scope.sel[index].unintNamee + " COD Cost can not Empty or zero")
					break;
				}

				if($scope.sel[index].changeBuffer==null || $scope.sel[index].changeBuffer=='null')
				{
					flag2=false;
					alert($scope.sel[index].unintNamee + " Buffer can not Empty")
					break;
				}

				if($scope.sel[index].changeThreshold==null || $scope.sel[index].changeThreshold=="null")
				{
					flag2=false;
					alert($scope.sel[index].unintNamee + " Threshold can not Empty")
					break;
				}
			}

		}
		if(flag && flag1 && flag2){
			$("#productPriceModal").modal("show");
		}
	}

	$scope.submitUpdatePriceList = function(priceList){
		$scope.completeObjResArray = [];
		$scope.completePriceUnitObjs={};

		for(var index in priceList)
		{	
			$scope.completePriceUnitObjs ={
					"id":priceList[index].idd,
					"unit":{id:priceList[index].unitIDs,name:priceList[index].unintNamee,code:null,shortCode:null,type:null,status:'ACTIVE'},
					"product":{id:priceList[index].productIDs,name:priceList[index].product,code:null,shortCode:null,type:null,status:'ACTIVE'},
					"price":{id:priceList[index].priceeID,dimension:priceList[index].dimension,price:priceList[index].price,codCost:priceList[index].changeCodCosts,cost:priceList[index].changeCafecosts,buffer:priceList[index].changeBuffer,threshold:priceList[index].changeThreshold},
			}
			$scope.completeObjResArray.push($scope.completePriceUnitObjs);
		}
		$rootScope.showFullScreenLoader = true;
		$http({
			method: 'POST',
			url: AppUtil.restUrls.productMetaData.productPriceUpdate+"?updatedBy="+$rootScope.userData.id,
			data: $scope.completeObjResArray
		}).then(function success(response) {
			if(response.status==200){
				//console.log(response.data);
				alert("Price updated successfully!");
				$rootScope.showFullScreenLoader = false;
				window.location.reload();
			}else{
				console.log(response);	
				$rootScope.showFullScreenLoader = false;
			}
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			console.log("error:"+response);
		});
	}

});