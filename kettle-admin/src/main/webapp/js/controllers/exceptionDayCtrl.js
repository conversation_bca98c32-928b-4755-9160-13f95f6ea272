/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("exceptionDayCtrl", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

    $scope.init = function () {

        $scope.userDetails=AppUtil.getUserValues();
        $scope.exceptionDate=null;
        $scope.regions = [];
        $scope.prodDetails = {};

        initCheckBoxModal();
        $scope.selectedRegion = null;
        $scope.unitType = "CAFE";
        getRegionDetails();
    };

    function initCheckBoxModal() {
        $scope.checkBoxModal = {};

        $scope.checkBoxModal.checkAll = false;
        $scope.checkBoxModal.checkAllPrice = false;
        $scope.checkBoxModal.checkAllProfile = false;
        $scope.checkBoxModal.checkAllStatus = false;

        $scope.productListDetails = {};
        $scope.requestObject = [];
    }

    $scope.getUnitList = function (region) {
        $scope.resultFound=true;
        if(!$scope.exceptionDate){
            alert("Select a date");
            return;
        }
        bootbox.confirm("Do you want to load details of " + region + " region ?", function (result) {
            if (result == true) {
                $scope.selectedRegion = region;
                $rootScope.showFullScreenLoader = true;
                var payload = $.param({
                    unitCategory: $scope.unitType,
                    unitRegion: (region == "ALL" ? null : region)
                });

                $http({
                    url: AppUtil.restUrls.referenceOrderManagement.unitDetail,
                    method: "POST",
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    data: payload
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    $http({
                        method: "GET",
                        url: AppUtil.restUrls.referenceOrderManagement.getExceptionDateEntry,
                        params : {
                          businessDate : $scope.exceptionDate
                        }
                    }).then(function success(resp) {
                        $scope.unitListDetails = response.data;
                        console.log("$scope.productListDetails ",$scope.productListDetails);
                        for (var i = 0; i < $scope.unitListDetails.length; i++) {
                            $scope.prodDetails[$scope.unitListDetails[i].id] = {};
                            $scope.prodDetails[$scope.unitListDetails[i].id].checked = false;
                        }
                        $rootScope.showFullScreenLoader = false;
                        $scope.getExceptionDate=resp.data;
                        console.log($scope.getExceptionDate);
                        for(var i = 0; i < $scope.getExceptionDate.length;i++){
                                if ($scope.prodDetails[$scope.getExceptionDate[i].unitId]) {
                                    if ($scope.getExceptionDate[i].status == "ACTIVE") {
                                        $scope.prodDetails[$scope.getExceptionDate[i].unitId].checked = true;
                                    }
                                }
                        }
                        initCheckBoxModal();
                    },function error(resp){
                        $rootScope.showFullScreenLoader = false;
                        console.log("error:" + resp);
                    });
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });
            }
        });
    };

    $scope.updateAll = function () {
        if ($scope.checkBoxModal.checkAll === true) {
            for (var i = 0; i < $scope.unitListDetails.length; i++) {
                $scope.prodDetails[$scope.unitListDetails[i].id].checked = true;
            }
        } else if ($scope.checkBoxModal.checkAll === false) {
            for (var i = 0; i < $scope.unitListDetails.length; i++) {
                $scope.prodDetails[$scope.unitListDetails[i].id].checked = false;
            }
        }
    };

    $scope.submitDetails = function () {
        $scope.requestObject = [];
        for (var i = 0; i < $scope.unitListDetails.length; i++) {
            if($scope.prodDetails[$scope.unitListDetails[i].id].checked==true){
                var request ={};
                request.unitId=$scope.unitListDetails[i].id;
                request.unitName=$scope.unitListDetails[i].name;
                request.businessDate=$scope.exceptionDate
                request.status="ACTIVE";
                request.updatedBy=$scope.userDetails.user.name;
                request.updatedTime=new Date();
                $scope.requestObject.push(request);
            }
            else{
                var request ={};
                request.unitId=$scope.unitListDetails[i].id;
                request.unitName=$scope.unitListDetails[i].name;
                request.businessDate=$scope.exceptionDate
                request.status="IN_ACTIVE";
                request.updatedBy=$scope.userDetails.user.name;
                request.updatedTime=new Date();
                $scope.requestObject.push(request);
            }
        }
        console.log($scope.requestObject);
        $http({
            method: "POST",
            url: AppUtil.restUrls.referenceOrderManagement.setExceptionDateEntry,
            data: $scope.requestObject
        }).then(function success(response) {

            $scope.unitListDetails = response.data;
            $scope.init();
            $rootScope.showFullScreenLoader = false;
            initCheckBoxModal();
            alert("Exception Days Added Successfully!");
            $scope.resultFound=false;
        }, function error(response) {
            alert("Something want wrong!!!!!!!!!!");
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    function getRegionDetails() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regions = response.data;
            $scope.regions.push('ALL');
        }, function error(response) {
            console.log("error:" + response);
        });
    }
});
