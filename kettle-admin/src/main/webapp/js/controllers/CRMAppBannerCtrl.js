/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


adminapp.controller("CRMAppBannerCtrl", function ($rootScope, $scope, $http, $location, $cookieStore, AppUtil, fileService) {

    $scope.init = function () {

        $scope.userDetails= AppUtil.getUserValues();

        $scope.contentType = ["IMAGE", "VIDEO", "GIF"];
        $scope.regions = [];
        $scope.screenType = [];
        $scope.crmObject = {};
        getRegionDetails();
        getCrmScreenType();
        // getCityDetails();

        $scope.selectedRegion = null;
        $scope.imageSuffix = AppUtil.getImageUrl().crmImage;
        $scope.screenDetails = null;
        $scope.crmObject.screenCode = "";
        $scope.crmObject.listImage = null;
        $scope.crmObject.listImageToUpload;
        $scope.selectedUnit = [];
        $scope.unitMultiSelect = [];
        $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
        $scope.mappingStatusList = [
            {id: 1, name: "ACTIVE", value: "ACTIVE"},
            {id: 2, name: "IN_ACTIVE", value: "IN_ACTIVE"}
        ];
        $scope.statusMap = {"ACTIVE": $scope.mappingStatusList[0], "IN_ACTIVE": $scope.mappingStatusList[1]};
        $scope.globalSelect = false;
        $scope.globalStatus = null;
        $scope.newStatus = null;
        $scope.unitDetail = [];
        $scope.unitIdToUnitNameObj = {};
        $scope.unitNameToUnitRegionObj = {};
        $scope.selectedRegion = null;
        $scope.filteredScreenDetails = [];
        $scope.isUpdateScreen = false;
        $scope.unitToBeUpdated = null;
        $scope.listImageUploaded = false;
        //getUserDetails();
    }

    $scope.getUserDetails = function (region) {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits+ "?category=CAFE",
            data: {
                "employeeId": $scope.userDetails.user.id,
                "onlyActive": true,
                "category":"CAFE"
            },
        }).then(function success(response) {
            console.log(region);

            $scope.selectedRegion = region;
            $scope.unitDetail = response.data;
            $scope.unitDetail.forEach(function (data) {
                $scope.unitIdToUnitNameObj[data.id] = data.name;
                $scope.unitNameToUnitRegionObj[data.name] = data.region;
            });
            if ($scope.selectedRegion != "ALL") {
                $scope.units = response.data.filter(function (unit) {
                    return unit.category == 'CAFE' && unit.status == 'ACTIVE' && unit.region == $scope.selectedRegion;
                });
            }
            else {
                $scope.units = response.data.filter(function (unit) {
                    return unit.category == 'CAFE' && unit.status == 'ACTIVE';
                });
            }
            console.log($scope.units);
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    $scope.addCRMAppOfferDialog = function () {
        $scope.isAddNewOffer = true;
        $scope.listImageUploaded = false;
        console.log($scope.regions);
        console.log($scope.contentType);
    }

    $scope.viewUnitLists = function (unitList) {
        $scope.unitDetailsList = unitList;
        $('#UnitListViewModal').modal('show');
    }
    $scope.openProductImageModal = function (imageUrl) {
        console.log($scope.crmObject);
        console.log(imageUrl);
        if (imageUrl == null) {
            alert("Image not found!")
        }
        else {
            if(imageUrl.url===undefined){
                $scope.imageSrc =imageUrl;
            }
            else{
                $scope.imageSrc =imageUrl.url;
            }
            console.log($scope.imageSrc);
            console.log(imageUrl);
            $("#displayImageModal").modal("show");
            $('#displayRightImageModal').modal('show');
        }
    };

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function isImage(fileExt) {
        return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png" || fileExt == "mp4" || fileExt == "gif";
    }

    $scope.addCrmScreenDetail = function (crmObject) {
        if (!crmObject.screenType) {
            alert("please select screen type")
            return;
        }
        if (!crmObject.city) {
            alert("please select city")
            return;
        }
        if (!crmObject.contentType) {
            alert("please select content type")
            return;
        }
        if (!crmObject.listImage) {
            alert("please upload image")
            return;
        }

        var selectedUnitIdList = [];
        var selectedUnitNameList = [];
        $scope.unitMultiSelect.forEach(function (unit) {
            selectedUnitIdList.push(unit.id);
            selectedUnitNameList.push($scope.unitIdToUnitNameObj[unit.id]);
        });

        var crmScreenDetail = {
            screenType: crmObject.screenType,
            city: $scope.selectedRegion,
            contentType: crmObject.contentType,
            cityType: null,
            status: "ACTIVE",
            updatedBy: $scope.userDetails.userId,
            imagePath: crmObject.listImage != null ? crmObject.listImage.url : null,
            unitId: selectedUnitIdList,
            selected: false,
            unitName: selectedUnitNameList,
            newStatus: null
        };

        console.log(crmScreenDetail);
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.posMetaData.setCrmBannerDetail,
            data: crmScreenDetail
        }).then(function success(response) {
            if (response.status === 200) {
                alert("Added Successfully");
                $rootScope.showFullScreenLoader = false;
            }
            else {
                alert("Error to Add Data!!!");
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            alert("Error to Add Data!!!");
            $rootScope.showFullScreenLoader = false;
        });
        $scope.crmObject.city = "";
        $scope.reset();
    }

    $scope.updateCrmScreenDetail = function (crmObject) {

        if (!crmObject.listImage) {
            alert("please upload image")
            return;
        }

        console.log(crmObject);
        $scope.unitToBeUpdated.imagePath = crmObject.listImage.url;
        console.log($scope.unitToBeUpdated);

        var crmScreenDetail = {
            key: $scope.unitToBeUpdated.key,
            screenType: $scope.unitToBeUpdated.screenType,
            city: $scope.unitToBeUpdated.city,
            contentType: $scope.unitToBeUpdated.contentType,
            imagePath: $scope.unitToBeUpdated.imagePath,
            cityType: $scope.unitToBeUpdated.cityType,
            status: $scope.unitToBeUpdated.status,
            updatedBy: $scope.unitToBeUpdated.updatedBy,
            unitId: $scope.unitToBeUpdated.unitId,
            unitName: $scope.unitToBeUpdated.unitName,
            updatedOn: $scope.unitToBeUpdated.updatedOn
        };

        console.log(crmScreenDetail);
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.customer.updateCrmScreenUrl,
            data: crmScreenDetail
        }).then(function success(response) {

            console.log(response);
            if (response.status === 200) {
                alert("Added Successfully");
                $rootScope.showFullScreenLoader = false;
            }
            else {
                alert("Error to Add Data!!!");
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            alert("Error to Add Data!!!");
            $rootScope.showFullScreenLoader = false;
        });
        $scope.crmObject.city = "";
        $scope.reset();
    }

    $scope.onEditScreenDetail = function (unit) {
        $scope.crmObject.screenType = unit.screenType;
        $scope.crmObject.city = unit.city;
        $scope.crmObject.contentType = unit.contentType;
        $scope.isUpdateScreen = true;
        $scope.unitToBeUpdated = unit;
        $scope.addCRMAppOfferDialog();
        console.log(unit);
    }

    $scope.uploadScreenImage = function (Code) {
        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select an Image");
            return false;
        }
        var currentTimestamp=new Date().getTime();
        var fileExt = getFileExtension(fileService.getFile().name);
        var nameWithoutExt = fileService.getFile().name.replace("."+fileExt,"");
        var newFile=new File([fileService.getFile()],nameWithoutExt+currentTimestamp+"."+fileExt);
        if (isImage(fileExt.toLowerCase())) {
            if (isImage(fileExt.toLowerCase()) === "jpg" || isImage(fileExt.toLowerCase()) === "jpeg" || isImage(fileExt.toLowerCase()) === "png" && fileService.getFile().size > 500000) {
                bootbox.alert('File size should not be greater than 500 kb');
                return;
            }
            if (isImage(fileExt.toLowerCase()) === "mp4" && fileService.getFile().size > 30000000) {
                bootbox.alert('File size should not be greater than 30Mb');
                return;
            }
            if (isImage(fileExt.toLowerCase()) === "gif" && fileService.getFile().size > 1000000) {
                bootbox.alert('File size should not be greater than 1Mb');
                return;
            }
            var fd = new FormData();
            fd.append("mimeType", fileExt.toUpperCase());
            fd.append("couponCode", $scope.crmObject.screenCode);
            fd.append("imageType", "list");
            fd.append("file", newFile);
            $rootScope.showDetailLoader = true;
            var URL = AppUtil.restUrls.posMetaData.crmScreenUploadImage;
            $http({
                url: URL,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showDetailLoader = false;
                $scope.crmObject.listImageToUpload = null;
                angular.element("input[type='file']").val(null);
                $scope.crmObject.listImage = {url: response.url, name: response.name};
                fileService.push(null);
                alert("Image Added");
                $scope.listImageUploaded = true;
            }).error(function (response) {
                $rootScope.showDetailLoader = false;
                alert("Error while uploading Image");
            });
        }
        else {
            alert("Cannnot upload image");
            return false;
        }
    }


    $scope.reset = function () {
        $scope.crmObject = {};
        $scope.selectedUnit = [];
        $scope.unitMultiSelect = [];
        $scope.isUpdateScreen = false;
    }

    function getCrmScreenType() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.posMetaData.getCrmScreenType
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.screenType = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getRegionDetails() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regions = response.data;
            $scope.regions.push('ALL');
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getCrmAppScreenDetail() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.posMetaData.getCrmAppScreenDetail + '?region=' + $scope.selectedRegion
        }).then(function success(response) {
            if (response.status === 200) {
                $scope.screenDetails = response.data;
                $scope.screenDetails.map(function (data) {
                    // data.unit = $scope.unitMap[data.unitId];
                    // data.brand = $scope.brandMap[data.brandId];
                    // data.partner = $scope.channelPartnerMap[data.partnerId];
                    data.selected = false;
                    // data.newValue = data.value;
                    data.newStatus = $scope.statusMap[data.status];
                });
                // var dataMap = {};
                // $scope.screenDetails.map(function (data) {
                //     dataMap[data.unitId] = data;
                // });
                $scope.filteredScreenDetails = $scope.screenDetails;
                console.log($scope.screenDetails);
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.getScreenDetails = function () {
        getCrmAppScreenDetail();
    }


    $scope.toggleSelectMapping = function (data, setStatus) {
        if (setStatus === true) {
            data.selected = !data.selected;
        }
        if (data.selected === true) {
            data.newStatus = $scope.globalStatus != null ? $scope.globalStatus : $scope.statusMap[data.status];
        } else {
            data.newStatus = $scope.statusMap[data.status];
        }
        console.log($scope.screenDetails);
    };

    $scope.selectAll = function () {
        $scope.globalSelect = !$scope.globalSelect;
        $scope.screenDetails.map(function (data) {
            console.log($scope.globalSelect);
            data.selected = $scope.globalSelect;
            if ($scope.globalSelect === true) {
                console.log($scope.globalStatus)
                data.newStatus = $scope.globalStatus;
            }
            //$scope.toggleSelectMapping(data);
        });
        console.log($scope.screenDetails);
    };

    $scope.updateGlobalStatus = function (status) {
        $scope.globalStatus = status;
        console.log($scope.globalStatus);
    }

    $scope.setNewStatus = function (data) {
        //$scope.globalStatus=data.newStatus.value;
        console.log(data.newStatus.value);
        console.log($scope.screenDetails);
    };

    $scope.submitAddMetadata = function () {
        var finalMetadata = [];
        $scope.screenDetails.map(function (data) {
            if (data.selected === true) {
                if (data.newStatus != null)
                    data.status = data.newStatus.value;
                finalMetadata.push({
                    key: data.key,
                    screenType: data.screenType,
                    city: data.city,
                    contentType: data.contentType,
                    imagePath: data.imagePath,
                    cityType: data.cityType,
                    status: data.status,
                    updatedBy: data.updatedBy,
                    unitId: data.unitId,
                    unitName: data.unitName,
                    updatedOn: data.updatedOn
                });
            }
        });
        console.log(finalMetadata);
        var dialog = bootbox.dialog({
            title: 'Updating Status. Please wait.',
            message: '<p><i class="fa fa-spin fa-spinner" /> Processing...</p>'
        });
        $http({
            method: 'POST',
            url: AppUtil.restUrls.customer.updateCrmScreenStatus,
            data: finalMetadata
        }).then(function success(response) {
            var message = "", title = "";
            if (response.status === 200) {
                title = "Congratulations!!";
                message = 'Status updated successfully!';
                $scope.loading = false;
            } else {
                title = 'Oops sorry!';
                message = response.data ? response.data.errorMessage : "Something went wrong";
                console.log(response);
            }
            dialog.find('.bootbox-head').html(title);
            dialog.find('.bootbox-body').html(message);
            setTimeout(function () {
                dialog.modal('hide');
            }, 2000);
        }, function error(response) {
            console.log("error:" + response);
            dialog.find('.bootbox-head').html('Oops sorry!');
            dialog.find('.bootbox-body').html("Error updating status");
        });
    }
});
