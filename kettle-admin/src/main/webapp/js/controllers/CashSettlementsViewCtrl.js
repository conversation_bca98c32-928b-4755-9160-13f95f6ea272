/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("CashSettlementsViewController", function($rootScope,$scope, $location, $http, AppUtil, $cookieStore, $uibModal,AppUtil){

	$rootScope.$on('cashSettlementView',function(e,args){
		$scope.init(args.searchByUnitId);
	});

	
	$scope.init = function(searchByUnitId){
		$scope.today = new Date(new Date().setDate(new Date().getDate() + 1)).toString();
		$scope.showPullView = false;
		$scope.searchByUnitId = searchByUnitId || $location.search().searchByUnitId;
		$scope.paymentModes = [];
		$scope.pullSettlements = [];
		$scope.showFullScreenLoader = false;
		$scope.pullSettlementsTotalCount = 1;
		$scope.startPosition = 1;
		$scope.selectPageNumber = 1;
		$scope.batchSize = 50;
		$scope.startDate = null;
		$scope.endDate = null;
		if($scope.searchByUnitId){
			$scope.outletList = [];
			fetchOutlets("CAFE");
		}else{
			fetchPaymentModes();
		}
	};

	$scope.changeUnit = function(outlet){
		$scope.unit = outlet;
	};

	$scope.changeType = function(type){
		$scope.paymentMode = type;
	};

	function getRequestObj() {
		return $scope.searchByUnitId ? {
			unitId : $scope.unit.id,
			startDate : $scope.startDate,
			endDate :  $scope.endDate,
			start : $scope.startPosition,
			batchSize: $scope.batchSize
		} : {
			settlementTypeId : $scope.paymentMode.id,
			startDate : $scope.startDate,
			endDate :  $scope.endDate,
			start : $scope.startPosition,
			batchSize: $scope.batchSize
		};
	}

	function getTotalCountRequestObj() {
		return $scope.searchByUnitId ? {
			unitId : $scope.unit.id,
			startDate : $scope.startDate,
			endDate :  $scope.endDate
		} : {
			settlementTypeId : $scope.paymentMode.id,
			startDate : $scope.startDate,
			endDate :  $scope.endDate
		};
	}

	$scope.getPullSettlements = function(){
		if($scope.startDate==null || $scope.startDate=="" || $scope.endDate==null || $scope.endDate==""){
			alert("Please select proper start date and end date!");
			return false;
		}
		$scope.startPosition = 1;
		$scope.totalPages = [];
		$scope.showFullScreenLoader = true;
		$http({
			method: 'POST',
			url: $scope.searchByUnitId ? AppUtil.restUrls.cashManagement.pullSettlementsGetTotalCount : AppUtil.restUrls.cashManagement.pullSettlementsGetByTypeTotalCount,
			data : getTotalCountRequestObj()
		}).then(function success(response){
			$scope.pullSettlementsTotalCount = response.data;
		},function error(response){
			console.log("error:"+response);
		});
    	$http({
  			method: 'POST',
  			  url: $scope.searchByUnitId ? AppUtil.restUrls.cashManagement.pullSettlementsGet : AppUtil.restUrls.cashManagement.pullSettlementsGetByType,
  			  data : getRequestObj()
  			}).then(function success(response) {
			$scope.showFullScreenLoader = false;
  				 if(response!=null){
  					$scope.pullSettlements = response.data;
  				 }
  			}, function error(response) {
			$scope.showFullScreenLoader = false;
  				console.log("error:"+response);
  		});
    
	};
	
	$scope.viewPull = function(settlement){
		$scope.selectedSettlement = settlement;
		$scope.showPullView = true;
	};
	
	$scope.hidePullView = function(){
		$scope.showPullView = false;
	};
	
	$scope.viewDenominations = function(denominations){
		$scope.denomEntity = denominations;
		$uibModal.open({
            animation: true,
            templateUrl: "viewDenominationModal.html" ,
            controller: 'viewDenominationModalCtrl',
            backdrop: 'static',
            scope: $scope,
            size: "lg"
        });
	};
	
	$scope.viewClosure = function(closurePaymentDetail){
		$scope.closureEntity = closurePaymentDetail;
		$uibModal.open({
            animation: true,
            templateUrl: "viewClosureModal.html" ,
            controller: 'viewClosureModalCtrl',
            backdrop: 'static',
            scope: $scope,
            size: "lg"
        });
	}
	
	function fetchOutlets(code) {
		$scope.showFullScreenLoader = true;
		$http({
			method: 'GET',
			  url: AppUtil.restUrls.unitMetaData.allUnits+'?category='+code
			}).then(function success(response) {
			$scope.showFullScreenLoader = false;
				if($scope.outletList.length==0){
            		$scope.outletList = response.data;
            	}else{
            		angular.forEach(response.data, function(v){
            			$scope.outletList.push(v);
            		});
            	}
				if(code!="DELIVERY"){
					fetchOutlets("DELIVERY");
				}
				$scope.unit=$scope.outletList[0];
			}, function error(response) {
			$scope.showFullScreenLoader = false;
				console.log("error:"+response);
		});
    }

	function fetchPaymentModes() {
		$scope.showFullScreenLoader = true;
		$http({
			method: 'POST',
			url: AppUtil.restUrls.offerManagement.paymentModes
		}).then(function success(response) {
			$scope.showFullScreenLoader = false;
			$scope.paymentModes = response.data;
			$scope.paymentMode = $scope.paymentModes[0];
		}, function error(response) {
			$scope.showFullScreenLoader = false;
			console.log("error:" + response);
		});
	}

	$scope.nextBatch = function (){
		$scope.startPosition = $scope.startPosition +1;
		$http({
			method: 'POST',
			url: $scope.searchByUnitId ? AppUtil.restUrls.cashManagement.pullSettlementsGet : AppUtil.restUrls.cashManagement.pullSettlementsGetByType,
			data : getRequestObj()
		}).then(function success(response) {
			$scope.showFullScreenLoader = false;
			if(response!=null){
				$scope.pullSettlements = response.data;
			}
		}, function error(response) {
			$scope.showFullScreenLoader = false;
			console.log("error:"+response);
		});
	}

	$scope.prevBatch = function (){
		$scope.startPosition = $scope.startPosition - 1;
		$http({
			method: 'POST',
			url: $scope.searchByUnitId ? AppUtil.restUrls.cashManagement.pullSettlementsGet : AppUtil.restUrls.cashManagement.pullSettlementsGetByType,
			data : getRequestObj()
		}).then(function success(response) {
			$scope.showFullScreenLoader = false;
			if(response!=null){
				$scope.pullSettlements = response.data;
			}
		}, function error(response) {
			$scope.showFullScreenLoader = false;
			console.log("error:"+response);
		});
	}
	
});

adminapp.controller('viewDenominationModalCtrl', function ($scope, $uibModalInstance,AppUtil, $filter) {
	$scope.init = function(){
		$scope.getTotal();
	}
	$scope.getTotal = function(){
		$scope.denomTotalAmount = 0;
		$scope.denomEntity.forEach(function(v){
			$scope.denomTotalAmount += v.totalAmount;
		});
	}
    $scope.ok = function() {
    	$uibModalInstance.close("cancel");
    };
    $scope.cancel = function() {
    	$uibModalInstance.dismiss("cancel");
    };
});

adminapp.controller('viewClosureModalCtrl', function ($scope, $uibModalInstance,AppUtil, $filter) {
	$scope.init = function(){
	}
    $scope.ok = function() {
    	$uibModalInstance.close("cancel");
    };
    $scope.cancel = function() {
    	$uibModalInstance.dismiss("cancel");
    };
});