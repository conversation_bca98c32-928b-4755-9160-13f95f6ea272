// Add capitalize filter
adminapp.filter('capitalize', function() {
    return function(input) {
        if (!input) return '';
        input = input.toLowerCase();
        return input.charAt(0).toUpperCase() + input.slice(1);
    };
});

// Add bytes filter
adminapp.filter('bytes', function() {
    return function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
});

adminapp.controller('LCDMenuFileUploadController', ['$scope', 'ImageUploadService','$timeout', function($scope, ImageUploadService,$timeout) {

  // Data models
  $scope.formData = {
    version: '',
    newVersion: '',
    region: '',
    priceProfile: '',
    orientation: '',
    slot: '',
    lcdType: '',
    variants: [] // Array to store variant selections
  };

  $scope.versions = [];
  $scope.regions = [];
  $scope.priceProfiles = [];
  $scope.orientations = ['Portrait', 'Landscape'];
  $scope.slots = [];
  $scope.lcdTypes = [];
  $scope.variantGroups = []; // Array to store variant groups
  $scope.uploadedImages = [];
  $scope.selectedFiles = [];

  // UI state
  $scope.isLoading = false;
  $scope.errorMessage = '';
  $scope.successMessage = '';
  $scope.isEditMode = false;

  // Initialize currentStep
  $scope.currentStep = 'version';

  // Initialize navigation history
  $scope.navigationHistory = [];

  // Add current path display
  $scope.currentPath = '';

  // Add upload mode state
  $scope.uploadMode = false;

  // Add view mode state
  $scope.viewMode = 'grid';

  // Add modal control functions
  $scope.showUploadModal = false;

  // Add UI state variables
  $scope.showSearchResults = false;
  $scope.showVariantModal = false;

  // Add new folder creation state
  $scope.showNewFolderModal = false;
  $scope.newFolderName = '';
  $scope.currentStepFolders = [];

  // Add variant selection state
  $scope.selectedVariantGroup = null;
  $scope.showVariantGroupModal = false;
  $scope.variantGroupSelections = [];

  // Add file-related data models
  $scope.currentStepFiles = [];
  $scope.selectedFile = null;

  // Initialize with empty arrays for dynamic folders
  $scope.versions = [];
  $scope.regions = [];
  $scope.priceProfiles = [];
  $scope.orientations = [];
  $scope.slots = [];
  $scope.lcdTypes = [];

  // Add new step state
  $scope.showNewStepModal = false;
  $scope.newStepName = '';
  $scope.availableSteps = [];

  // Add variant management state variables
  $scope.showVariantManagementModal = false;
  $scope.showGroupModal = false;
  $scope.showItemModal = false;
  $scope.editingGroup = false;
  $scope.editingItem = false;
  $scope.selectedGroup = null;
  $scope.variantItems = [];
  $scope.newGroup = {
    groupName: '',
    status: 'ACTIVE'
  };
  $scope.newItem = {
    itemName: '',
    groupId: null,
    status: 'ACTIVE'
  };

  // Initialize steps
  $scope.loadSteps = function() {
    ImageUploadService.getAllSteps()
      .success(function(steps) {
        $scope.availableSteps = steps;
        // Update formData with new steps
        steps.forEach(function(step) {
          if (!$scope.formData[step]) {
            $scope.formData[step] = '';
          }
        });
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to load steps: ' + error;
      });
  };

  $scope.openNewStepModal = function() {
    $scope.showNewStepModal = true;
    $scope.newStepName = '';
  };

  $scope.closeNewStepModal = function() {
    $scope.showNewStepModal = false;
    $scope.newStepName = '';
  };

  $scope.createNewStep = function() {
    if (!$scope.newStepName) return;

    $scope.isLoading = true;
    ImageUploadService.createStep($scope.newStepName)
      .success(function(response) {
        $scope.successMessage = 'Step created successfully';
        $scope.closeNewStepModal();
        // Refresh steps list
        $scope.loadSteps();
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to create step: ' + error;
      })
      .finally(function() {
        $scope.isLoading = false;
      });
  };

  // Initialize
  $scope.init = function() {
    // Check if we should use real API or dummy data
    var useDummyData = false; // Set this to false when you're ready to use real APIs

    if (useDummyData) {
      $scope.initDummyData();
    } else {
      $scope.loadVersions();
      $scope.loadMetadata();
      $scope.loadSteps();
      // Initialize navigation history with version step
      $scope.navigationHistory = [{
        step: 'version',
        value: '',
        label: 'Version'
      }];
    }
  };

  // Load versions
  $scope.loadVersions = function() {
    ImageUploadService.getVersions()
      .success(function(data) {
        $scope.versions = data;
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to load versions: ' + error;
      });
  };

  $scope.downloadExcel = function() {
    // Prepare data for Excel
    const excelData = $scope.uploadedImages.map(image => ({
        'File Name': image.name,
        'File Path': image.url,
        'Action': 'Add'
    }));

    // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Images');

    // Generate Excel file and trigger download
    const excelFileName = `Image_Details_${new Date().toISOString().slice(0, 10)}.xlsx`;
    XLSX.writeFile(workbook, excelFileName);
  };

  // Load metadata (regions, price profiles, etc.)
  $scope.loadMetadata = function() {
    ImageUploadService.getMetadata()
      .success(function(data) {
        $scope.regions = data.regions;
        $scope.priceProfiles = data.priceProfiles;
        $scope.slots = data.slots;
        $scope.lcdTypes = data.lcdTypes;
        $scope.variantGroups = data.variants || [];
        // Initialize variants array based on number of groups
        $scope.formData.variants = new Array($scope.variantGroups.length).fill('');
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to load metadata: ' + error;
      });
  };

  // Handle version change
  $scope.onVersionChange = function() {
    if ($scope.formData.version) {
      $scope.currentStep = 'region'; // Move to region step after version selection
    } else {
      $scope.uploadedImages = [];
    }
  };

  // Load images for a specific version
  $scope.loadImagesForVersion = function(version) {
    var useDummyData = false; // Set this to false when you're ready to use real APIs

    if (useDummyData) {
      $scope.uploadedImages = $scope.dummyImageSets[version] || [];
    } else {
      $scope.isLoading = true;
      ImageUploadService.getImagesByVersion(version)
        .success(function(data) {
          $scope.uploadedImages = data;
          $scope.isLoading = false;
        })
        .error(function(error) {
          $scope.errorMessage = 'Failed to load images: ' + error;
          $scope.isLoading = false;
        });
    }
  };

  // Helper function to clear messages
  $scope.clearMessages = function() {
    $scope.errorMessage = '';
    $scope.successMessage = '';
  };

  // Toggle upload mode
  $scope.toggleUploadMode = function() {
    $scope.uploadMode = !$scope.uploadMode;
    if ($scope.uploadMode) {
      $scope.successMessage = 'Upload Mode: You can quickly change selections without navigation';
    } else {
      $scope.successMessage = 'Normal Mode: Full navigation enabled';
    }
  };

  // Toggle search results modal
  $scope.toggleSearchResults = function() {
    $scope.showSearchResults = !$scope.showSearchResults;
  };

  // Update searchImages to show modal
  $scope.searchImages = function() {
    $scope.clearMessages();

    if(!$scope.formData.version){
      $scope.errorMessage = 'Please select a version';
      return;
    }

    $scope.isLoading = true;

//    var params = {
//      version: $scope.formData.version,
//      region: $scope.formData.region,
//      priceProfile: $scope.formData.priceProfile,
//      orientation: $scope.formData.orientation,
//      slot: $scope.formData.slot,
//      lcdType: $scope.formData.lcdType
//    };

      var params  = angular.copy($scope.formData);

    // Show current search path
    $scope.currentSearchPath = Object.entries(params)
      .filter(([_, value]) => value) // Only show non-empty values
      .map(([key, value]) => `${key}: ${value}`)
      .join(' > ');

    ImageUploadService.searchImagesWithParams(params)
      .success(function(response) {
        if (response && response.errorMessage) {
          $scope.errorMessage = response.errorMessage;
        } else {
          // Ensure response is an array
          $scope.uploadedImages = Array.isArray(response) ? response : [];
          if ($scope.uploadedImages.length === 0) {
            $scope.successMessage = 'No images found in the current path';
          } else {
            $scope.showSearchResults = true; // Show the modal when results are found
          }
        }
        $scope.isLoading = false;
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to search images: ' + error;
        $scope.isLoading = false;
      });
  };

  // File selection handler
  $scope.onFileSelect = function(files) {
    console.log('Files selected:', files);
    if($scope.isEditMode && files.length > 1){
      $scope.errorMessage = 'You can only select one file to replace the existing image';
      return;
    }
    $scope.selectedFiles = Array.from(files);
    console.log('Selected files in scope:', $scope.selectedFiles);
    $timeout(function() {
      $scope.$apply(); // Ensure the scope is updated
    });
  };

  // Create new version
  $scope.createVersion = function() {
    $scope.clearMessages();

    if (!$scope.formData.newVersion || $scope.formData.newVersion.trim() === '') {
      $scope.errorMessage = 'Please enter a version name';
      return;
    }
    if($scope.formData.newVersion < 0){
      $scope.errorMessage = 'Version name cannot be negative';
      return;
    }

      $scope.isLoading = true;
      ImageUploadService.createVersion($scope.formData.newVersion)
      .success(function(response) {
        if (response && response.errorMessage) {
          $scope.errorMessage = response.errorMessage;
        } else {
          $scope.successMessage = 'Version created successfully';
          $scope.loadVersions();
          $scope.formData.version = $scope.formData.newVersion;
          $scope.formData.newVersion = '';
        }
          $scope.isLoading = false;
        })
        .error(function(error) {
        $scope.successMessage = '';
          $scope.errorMessage = 'Failed to create version: ' + error;
          $scope.isLoading = false;
        });
  };

  // Exit edit mode
  $scope.exitEditMode = function() {
    $scope.clearMessages();
    $scope.clearForm();
    $scope.isEditMode = false;
  };

  // Update clearForm to reset modals
  $scope.clearForm = function() {
    $scope.clearMessages();
    $scope.isEditMode = false;
    $scope.formData.editImageId = null;
    $scope.originalImageData = null;

    $scope.formData = {
      version: '',
      region: '',
      priceProfile: '',
      orientation: '',
      slot: '',
      lcdType: '',
      variants: [],
      newVersion: ''
    };
    $scope.selectedFiles = [];
    $scope.currentStep = 'version';
    $scope.currentPath = '';
    $scope.currentSearchPath = '';
    $scope.uploadedImages = [];
    $scope.viewMode = 'grid';
    $scope.showSearchResults = false;
    $scope.showVariantModal = false;
    $scope.showUploadModal = false;
    $scope.showNewFolderModal = false;
    $scope.newFolderName = '';
    $scope.currentStepFolders = [];
    $scope.selectedVariantGroup = null;
    $scope.variantGroupSelections = [];

    // Reset navigation history to initial state
    $scope.navigationHistory = [{
      step: 'version',
      value: '',
      label: 'Version'
    }];
    document.getElementById('file-input').value = '';
  };

  // Update editImage function
  $scope.editImage = function(image) {
    $scope.isEditMode = true;
    $scope.selectedImage = image;
    $scope.selectedFiles = [];

    // Store original path before editing
    $scope.originalPath = {
        version: $scope.formData.version,
        region: $scope.formData.region,
        priceProfile: $scope.formData.priceProfile,
        orientation: $scope.formData.orientation,
        slot: $scope.formData.slot,
        lcdType: $scope.formData.lcdType
    };

    // Set current path to image's path for editing
    $scope.formData.version = image.version;
    $scope.formData.region = image.region;
    $scope.formData.priceProfile = image.priceProfile;
    $scope.formData.orientation = image.orientation;
    $scope.formData.slot = image.slot;
    $scope.formData.lcdType = image.lcdType;

    // Update current path
    $scope.updateCurrentPath();

    // Update navigation history
    $scope.navigationHistory = [
      { step: 'version', value: image.version, label: 'Version' },
      { step: 'region', value: image.region, label: 'Region' },
      { step: 'priceProfile', value: image.priceProfile, label: 'Price Profile' },
      { step: 'orientation', value: image.orientation, label: 'Orientation' },
      { step: 'slot', value: image.slot, label: 'Slot' },
      { step: 'lcdType', value: image.lcdType, label: 'LCD Type' }
    ];

    $scope.currentStep = 'lcdType';
    $scope.successMessage = 'Edit mode: Select a new image to replace the existing one';
  };

  // Variant modal functions
  $scope.openVariantModal = function() {
    $scope.showVariantModal = true;
  };

  $scope.closeVariantModal = function() {
    $scope.showVariantModal = false;
  };

  $scope.saveVariants = function() {
    $scope.closeVariantModal();
    $scope.successMessage = 'Variants saved successfully';
    $scope.updateCurrentPath();
  };

  // Update uploadImages to handle file naming properly
  $scope.uploadImages = function() {
    $scope.clearMessages();
    $scope.isLoading = true;

    if (!$scope.formData.version) {
        $scope.errorMessage = 'Please select a version';
        return;
    }

    if (!$scope.selectedFiles || $scope.selectedFiles.length === 0) {
        $scope.errorMessage = 'Please select files to upload';
        return;
    }

    // Validate file types and sizes
    for (var i = 0; i < $scope.selectedFiles.length; i++) {
        const file = $scope.selectedFiles[i];
        const fileType = file.type;
        const fileSize = file.size / (1024 * 1024); // Convert to MB

        if (fileType.startsWith('image/')) {
            if (fileSize > 20) { // 20MB limit for images
                $scope.errorMessage = `File ${file.name} exceeds the 100MB size limit for images`;
                return;
            }
        } else if (fileType === 'video/mp4') {
            if (fileSize > 100) { // 100MB limit for videos
                $scope.errorMessage = `File ${file.name} exceeds the 500MB size limit for videos`;
                return;
            }
        } else {
            $scope.errorMessage = `File ${file.name} has an unsupported file type. Only images and MP4 videos are allowed.`;
            return;
        }
    }

    $scope.isLoading = true;
     // Get all variants and sort them
    const allVariants = $scope.variantGroupSelections.reduce((acc, group) => {
                    return acc.concat(group.variants);
    }, []);
    var formData = new FormData();

    // Add form data for all steps (including custom steps)
    Object.keys($scope.formData).forEach(key => {
        if ($scope.formData[key] && key !== 'variants') {
            formData.append(key, $scope.formData[key]);
        }
    });

    // Add variant groups
    formData.append('variantGroups', JSON.stringify($scope.variantGroupSelections));

    // Add files with proper naming and type
    for (var i = 0; i < $scope.selectedFiles.length; i++) {
        const file = $scope.selectedFiles[i];
        const fileExt = file.name.split('.').pop();
        const fileNameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.'));

        // Build path components based on all available steps
        const pathComponents = [];

        // Add predefined steps
        if ($scope.formData.version) pathComponents.push($scope.formData.version);
        if ($scope.formData.region) pathComponents.push($scope.formData.region);
        if ($scope.formData.priceProfile) pathComponents.push($scope.formData.priceProfile);
        if ($scope.formData.orientation) pathComponents.push($scope.formData.orientation);
        if ($scope.formData.slot) pathComponents.push($scope.formData.slot);
        if ($scope.formData.lcdType) pathComponents.push($scope.formData.lcdType);

        // Add custom steps
        $scope.availableSteps.forEach(step => {
            if (!['version', 'region', 'priceProfile', 'orientation', 'slot', 'lcdType'].includes(step)) {
                if ($scope.formData[step]) {
                    pathComponents.push($scope.formData[step]);
                }
            }
        });

        // Sort variants alphabetically
        allVariants.sort();

        // Create new filename with path components and variants
        let newFileName = pathComponents.join('_');

        // Add variants in sorted order if present
        if ($scope.selectedVariantsPreview) {
            newFileName += '_' + $scope.selectedVariantsPreview;
        }

        // Add index based on current count (i+1 for 1-based indexing)
        newFileName += '_image' + (i+1) + '.' + fileExt;

        // Create a new File object with the updated name
        const newFile = new File([file], newFileName, { type: file.type });

        // Add file type information
        formData.append('fileTypes', file.type);

        // Add the file
        formData.append('files', newFile);
    }
    formData.append('variants', allVariants);

    ImageUploadService.uploadImages(formData)
        .success(function(response) {
            if (response && response.errorMessage) {
                $scope.errorMessage = response.errorMessage;
            } else {
                $scope.successMessage = 'Upload successful';
                $scope.selectedFiles = [];
                document.getElementById('file-input').value = '';
                $scope.searchImages();
                $scope.loadFilesForStep($scope.currentStep);
                $scope.closeUploadModal(); // Close the modal after successful upload
            }
            $scope.isLoading = false;
        })
        .error(function(error) {
            $scope.errorMessage = 'Upload failed: ' + error;
            $scope.isLoading = false;
        });
  };

  // Remove/delete image
  $scope.removeImage = function(imageId) {
    if (!confirm('Are you sure you want to remove this image? It will be marked as inactive.')) {
        return;
    }

        $scope.isLoading = true;
        ImageUploadService.deleteImage(imageId)
        .success(function(response) {
            $scope.successMessage = 'Image removed successfully';
            $scope.searchImages();
            $scope.isLoading = false;
          })
          .error(function(error) {
            $scope.errorMessage = 'Failed to remove image: ' + error;
            $scope.isLoading = false;
          });
  };

  // Add navigation functions
  $scope.goToStep = function(step) {
    if ($scope.availableSteps.includes(step)) {
      $scope.currentStep = step;
      $scope.loadFoldersForStep(step);
      $scope.updateCurrentPath();
    }
  };

  // Selection functions
  $scope.selectVersion = function(version) {
    console.log('Selecting version:', version);
    $scope.quickNavigate('version', version);
  };

  $scope.selectRegion = function(region) {
    console.log('Selecting region:', region);
    $scope.quickNavigate('region', region);
  };

  $scope.selectPriceProfile = function(profile) {
    console.log('Selecting price profile:', profile);
    $scope.quickNavigate('priceProfile', profile);
  };

  $scope.selectOrientation = function(orientation) {
    console.log('Selecting orientation:', orientation);
    $scope.quickNavigate('orientation', orientation);
  };

  $scope.selectSlot = function(slot) {
    console.log('Selecting slot:', slot);
    $scope.quickNavigate('slot', slot);
  };

  $scope.selectLcdType = function(type) {
    console.log('Selecting LCD type:', type);
    $scope.quickNavigate('lcdType', type);
  };

  // Add step icons mapping
  $scope.getStepIcon = function(step) {
    const icons = {
      'version': 'fa-folder',
      'region': 'fa-globe',
      'priceProfile': 'fa-tag',
      'orientation': 'fa-arrows-alt',
      'slot': 'fa-square',
      'lcdType': 'fa-desktop',
      'variants': 'fa-list'
    };
    return icons[step] || 'fa-folder';
  };

  // Add step labels mapping
  $scope.getStepLabel = function(step) {
    const labels = {
      'version': 'Version',
      'region': 'Region',
      'priceProfile': 'Price Profile',
      'orientation': 'Orientation',
      'slot': 'Slot',
      'lcdType': 'LCD Type',
      'variants': 'Variants'
    };
    return labels[step] || step;
  };

  // Update navigation history
  $scope.updateNavigationHistory = function(step, value) {
    // Find the index of the current step
    const currentIndex = $scope.navigationHistory.findIndex(item => item.step === step);

    if (currentIndex === -1) {
      // If step not found, add it to the end
      $scope.navigationHistory.push({
        step: step,
        value: value,
        label: value || $scope.getStepLabel(step)
      });
    } else {
      // If step found, update it and remove any future steps
      $scope.navigationHistory[currentIndex] = {
        step: step,
        value: value,
        label: value || $scope.getStepLabel(step)
      };
      // Remove any steps after this one
      $scope.navigationHistory = $scope.navigationHistory.slice(0, currentIndex + 1);

      // Clear form data for removed steps
      const steps = ['version', 'region', 'priceProfile', 'orientation', 'slot', 'lcdType'];
      steps.forEach((s, i) => {
        if (i > currentIndex) {
          $scope.formData[s] = '';
        }
      });

      // For custom steps, also clear any custom step data after the current step
      $scope.availableSteps.forEach((s) => {
        if (!steps.includes(s) && $scope.navigationHistory.findIndex(item => item.step === s) > currentIndex) {
          $scope.formData[s] = '';
        }
      });
    }
  };

  // Update current path
  $scope.updateCurrentPath = function() {
    var path = [];

    // In upload mode, use navigation history to build path
    if ($scope.uploadMode) {
        $scope.navigationHistory.forEach(item => {
            if (item.value) {
                path.push(item.value);
            }
        });
    } else {
        // In normal mode, use form data
        // Add predefined steps
        if ($scope.formData.version) path.push($scope.formData.version);
        if ($scope.formData.region) path.push($scope.formData.region);
        if ($scope.formData.priceProfile) path.push($scope.formData.priceProfile);
        if ($scope.formData.orientation) path.push($scope.formData.orientation);
        if ($scope.formData.slot) path.push($scope.formData.slot);
        if ($scope.formData.lcdType) path.push($scope.formData.lcdType);

        // Add custom steps
        $scope.availableSteps.forEach(step => {
            if (!['version', 'region', 'priceProfile', 'orientation', 'slot', 'lcdType'].includes(step)) {
                if ($scope.formData[step]) {
                    path.push($scope.formData[step]);
                }
            }
        });
    }

    $scope.currentPath = path.join(' / ');
  };

  // Update goToHistoryItem function to original behavior
  $scope.goToHistoryItem = function(index) {
    const item = $scope.navigationHistory[index];
    if (item) {
        if (!$scope.uploadMode) {
            // In normal mode, clear and restore form data
            $scope.formData = {
                version: '',
                region: '',
                priceProfile: '',
                orientation: '',
                slot: '',
                lcdType: '',
                variants: [],
                newVersion: ''
            };

            // Restore form data for this step and all previous steps
            for (let i = 0; i <= index; i++) {
                const historyItem = $scope.navigationHistory[i];
                switch (historyItem.step) {
                    case 'version':
                        $scope.formData.version = historyItem.value;
                        break;
                    case 'region':
                        $scope.formData.region = historyItem.value;
                        break;
                    case 'priceProfile':
                        $scope.formData.priceProfile = historyItem.value;
                        break;
                    case 'orientation':
                        $scope.formData.orientation = historyItem.value;
                        break;
                    case 'slot':
                        $scope.formData.slot = historyItem.value;
                        break;
                    case 'lcdType':
                        $scope.formData.lcdType = historyItem.value;
                        break;
                    case 'variants':
                        // Keep variants as is
                        break;
                }
            }

            // Update navigation history to only include steps up to the selected index
            $scope.navigationHistory = $scope.navigationHistory.slice(0, index + 1);

            // Update current step
            $scope.currentStep = item.step;

            // Load folders for the current step
            $scope.loadFoldersForStep(item.step);
        } else {
            // In upload mode, update the current step without shortening path
            const historyItem = $scope.navigationHistory[index];
            switch (historyItem.step) {
                case 'version':
                    $scope.formData.version = historyItem.value;
                    break;
                case 'region':
                    $scope.formData.region = historyItem.value;
                    break;
                case 'priceProfile':
                    $scope.formData.priceProfile = historyItem.value;
                    break;
                case 'orientation':
                    $scope.formData.orientation = historyItem.value;
                    break;
                case 'slot':
                    $scope.formData.slot = historyItem.value;
                    break;
                case 'lcdType':
                    $scope.formData.lcdType = historyItem.value;
                    break;
                case 'variants':
                    // Keep variants as is
                    break;
            }

            // Update current step
            $scope.currentStep = historyItem.step;

            // Load folders for the current step
            $scope.loadFoldersForStep(historyItem.step);
        }

        // Update current path
        $scope.updateCurrentPath();

        // Clear any uploaded images
        $scope.uploadedImages = [];

        // Clear any selected files
        $scope.selectedFiles = [];
        document.getElementById('file-input').value = '';
    }
};

  // Add getNextStep function before quickNavigate
  $scope.getNextStep = function(currentStep) {
    const currentIndex = $scope.availableSteps.indexOf(currentStep);
    return $scope.availableSteps[currentIndex + 1] || currentStep;
  };

  // Update quickNavigate to handle upload mode properly
  $scope.quickNavigate = function(step, value) {
    console.log('Quick navigate called:', { step, value });

    if ($scope.uploadMode) {
        // In upload mode, only update the current step's value
        $scope.formData[step] = value;

        // Update navigation history without shortening
        const currentIndex = $scope.navigationHistory.findIndex(item => item.step === step);
        if (currentIndex === -1) {
            // If step not found, add it to the end
            $scope.navigationHistory.push({
                step: step,
                value: value,
                label: value || $scope.getStepLabel(step)
            });
        } else {
            // If step found, update it
            $scope.navigationHistory[currentIndex] = {
                step: step,
                value: value,
                label: value || $scope.getStepLabel(step)
            };
        }

        // In upload mode, stay on the current step
        $scope.currentStep = step;

        // Load folders for the current step
        $scope.loadFoldersForStep(step);
    } else {
        // In normal mode, navigate to the next step
        $scope.formData[step] = value;

        // For custom steps, stay on the same step
        if (!['version', 'region', 'priceProfile', 'orientation', 'slot', 'lcdType'].includes(step)) {
            $scope.currentStep = $scope.getNextStep(step);;
            // Update navigation history
            $scope.updateNavigationHistory(step, value);
            // Load folders for the current step
            $scope.loadFoldersForStep($scope.currentStep);
        } else {
            $scope.currentStep = $scope.getNextStep(step);
            // Update navigation history
            $scope.updateNavigationHistory(step, value);
            // Load folders for the next step
            $scope.loadFoldersForStep($scope.currentStep);
        }
    }

    // Update current path
    $scope.updateCurrentPath();
};

  // Reset path and search
  $scope.resetPath = function() {
    if (confirm('Are you sure you want to reset the current path? This will clear all selections.')) {
      $scope.clearForm();
      $scope.currentPath = '';
      $scope.currentSearchPath = '';
      $scope.successMessage = 'Path has been reset';
    }
  };

  // Update downloadPathImages to use backend download instead of direct S3 access
  $scope.downloadPathImages = function() {
    if (!$scope.formData.version) {
      $scope.errorMessage = 'Please select a version first';
      return;
    }

    $scope.isLoading = true;
//    var params = {
//      version: $scope.formData.version,
//      region: $scope.formData.region,
//      priceProfile: $scope.formData.priceProfile,
//      orientation: $scope.formData.orientation,
//      slot: $scope.formData.slot,
//      lcdType: $scope.formData.lcdType
//    };
    var params = angular.copy($scope.formData);

    // Use the backend to handle the download
    ImageUploadService.downloadImages(params)
      .success(function(response, status, headers) {
        // Create a blob from the response
        var blob = new Blob([response], { type: headers('Content-Type') });

        // Create download link
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `lcd-menu-images-${params.version}-${new Date().getTime()}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        $scope.successMessage = 'Images downloaded successfully';
        $scope.isLoading = false;
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to download images: ' + error;
        $scope.isLoading = false;
      });
  };

  // Add function to remove selected file
  $scope.removeSelectedFile = function(index) {
    $scope.selectedFiles.splice(index, 1);
  };

  // Update the file input click handler
  $scope.openFileInput = function() {
    document.getElementById('file-input').click();
  };

  // Open upload modal
  $scope.openUploadModal = function() {
    $scope.showUploadModal = true;
    $scope.selectedFiles = [];
    document.getElementById('file-input').value = '';
  };

  // Close upload modal
  $scope.closeUploadModal = function() {
    $scope.showUploadModal = false;
    $scope.selectedFiles = [];
    document.getElementById('file-input').value = '';
  };

  // Close search results modal
  $scope.closeSearchResults = function() {
    $scope.showSearchResults = false;
  };

  // Cancel edit mode
  $scope.cancelEdit = function() {
    $scope.isEditMode = false;
    $scope.selectedImage = null;
    $scope.selectedFiles = [];

    // Restore original path
    if ($scope.originalPath) {
        $scope.formData.version = $scope.originalPath.version;
        $scope.formData.region = $scope.originalPath.region;
        $scope.formData.priceProfile = $scope.originalPath.priceProfile;
        $scope.formData.orientation = $scope.originalPath.orientation;
        $scope.formData.slot = $scope.originalPath.slot;
        $scope.formData.lcdType = $scope.originalPath.lcdType;

        // Update navigation history to match original path
        $scope.navigationHistory = [];
        if ($scope.originalPath.version) {
            $scope.navigationHistory.push({
                step: 'version',
                value: $scope.originalPath.version,
                label: 'Version'
            });
        }
        if ($scope.originalPath.region) {
            $scope.navigationHistory.push({
                step: 'region',
                value: $scope.originalPath.region,
                label: 'Region'
            });
        }
        if ($scope.originalPath.priceProfile) {
            $scope.navigationHistory.push({
                step: 'priceProfile',
                value: $scope.originalPath.priceProfile,
                label: 'Price Profile'
            });
        }
        if ($scope.originalPath.orientation) {
            $scope.navigationHistory.push({
                step: 'orientation',
                value: $scope.originalPath.orientation,
                label: 'Orientation'
            });
        }
        if ($scope.originalPath.slot) {
            $scope.navigationHistory.push({
                step: 'slot',
                value: $scope.originalPath.slot,
                label: 'Slot'
            });
        }
        if ($scope.originalPath.lcdType) {
            $scope.navigationHistory.push({
                step: 'lcdType',
                value: $scope.originalPath.lcdType,
                label: 'LCD Type'
            });
        }

        // Update current step to the last step in the path
        if ($scope.navigationHistory.length > 0) {
            $scope.currentStep = $scope.navigationHistory[$scope.navigationHistory.length - 1].step;
        }

        // Update current path display
        $scope.updateCurrentPath();

        // Clear the original path
        $scope.originalPath = null;
    }
  };

  // Update loadFoldersForStep to handle folder loading
  $scope.loadFoldersForStep = function(step) {
    console.log('Loading folders for step:', step);
    $scope.isLoading = true;

    // For version step, use getVersions
    if (step === 'version') {
      ImageUploadService.getFolders(step)
            .success(function(response) {
                console.log('Version folders response:', response);
                $scope.currentStepFolders = response.map(folder => ({
                    name: folder.name,
                    id: folder.id,
                    status: folder.status
                }));
                $scope.isLoading = false;
            })
            .error(function(error) {
                $scope.errorMessage = 'Failed to load versions: ' + error;
                $scope.isLoading = false;
            });
    } else {
        // For other steps, use getFoldersForStep with current form data
        ImageUploadService.getFoldersForStep(step, $scope.formData)
            .success(function(response) {
                console.log('Folders response:', response);
                $scope.currentStepFolders = response.map(folder => ({
                    name: folder.name,
                    id: folder.id,
                    status: folder.status
                }));
                $scope.isLoading = false;
            })
            .error(function(error) {
                $scope.errorMessage = 'Failed to load folders: ' + error;
                $scope.isLoading = false;
            });
    }
  };

  // Update createNewFolder function
  $scope.createNewFolder = function() {
    console.log('Creating new folder');
    if (!$scope.newFolderName) {
        $scope.errorMessage = 'Please enter a name';
        return;
    }

    $scope.isLoading = true;

    // For version step, use createVersion API
    if ($scope.currentStep === 'version') {
        ImageUploadService.createVersion($scope.newFolderName)
            .success(function(response) {
                if (response && response.errorMessage) {
                    $scope.errorMessage = response.errorMessage;
                } else {
                    $scope.successMessage = 'Version created successfully';
                    $scope.loadFoldersForStep('version');
                    $scope.showNewFolderModal = false; // Close modal on success
                }
                $scope.isLoading = false;
            })
            .error(function(error) {
                $scope.errorMessage = 'Failed to create version: ' + error;
                $scope.isLoading = false;
            });
    } else {
        // For all other steps, use createFolder API
        ImageUploadService.createFolder({
            step: $scope.currentStep,
            name: $scope.newFolderName,
            parentPath: $scope.getCurrentPath() // Add parent path for proper folder structure
        })
        .success(function(response) {
            if (response && response.errorMessage) {
                $scope.errorMessage = response.errorMessage;
            } else {
                $scope.successMessage = 'Folder created successfully';
                $scope.loadFoldersForStep($scope.currentStep);
                $scope.showNewFolderModal = false; // Close modal on success
            }
            $scope.isLoading = false;
        })
        .error(function(error) {
            $scope.errorMessage = 'Failed to create folder: ' + error;
            $scope.isLoading = false;
        });
    }
  };

  // Add helper function to get current path
  $scope.getCurrentPath = function() {
    let path = '';
    $scope.navigationHistory.forEach(function(item) {
      if (item.value) {
        path += '/' + item.value;
      }
    });
    return path;
  };

  // Update variant group selection functions
  $scope.addVariantGroup = function() {
    console.log('Adding variant group:', $scope.selectedVariantGroup);
    if (!$scope.selectedVariantGroup) {
        $scope.errorMessage = 'Please select a variant group';
        return;
    }

    // Find the selected group
    const selectedGroup = $scope.variantGroups.find(group => group.groupName === $scope.selectedVariantGroup);
    if (!selectedGroup) {
        $scope.errorMessage = 'Selected variant group not found';
        return;
    }

    // Check if group is already added
    if ($scope.variantGroupSelections.some(selection => selection.group === selectedGroup.groupName)) {
        $scope.errorMessage = 'This variant group is already added';
        return;
    }

    // Add to selections with empty variants array
    $scope.variantGroupSelections.push({
        group: selectedGroup.groupName,
        variants: [],
        items: selectedGroup.items // Store the available items for this group
    });

    // Reset selection
    $scope.selectedVariantGroup = null;
    $scope.showVariantGroupModal = false;

    $scope.successMessage = 'Variant group added successfully';
  };

  $scope.addVariantToGroup = function(groupIndex, variant) {
    if (!variant) {
        $scope.errorMessage = 'Please select a variant';
        return;
    }

    const selection = $scope.variantGroupSelections[groupIndex];
    if (!selection) {
        $scope.errorMessage = 'Variant group not found';
        return;
    }

    // Check if variant is already added
    if (selection.variants.includes(variant)) {
        $scope.errorMessage = 'This variant is already added';
        return;
    }

    // Add variant to the group
    selection.variants.push(variant);
    selection.selectedVariant = null; // Reset the selection

    // Sort variants alphabetically
    selection.variants.sort();

    // Update the preview of selected variants
    $scope.updateSelectedVariantsPreview();

    $scope.successMessage = 'Variant added successfully';
  };

  $scope.removeVariantFromGroup = function(groupIndex, variantIndex) {
    $scope.variantGroupSelections[groupIndex].variants.splice(variantIndex, 1);
    $scope.updateSelectedVariantsPreview();
  };

  $scope.removeVariantGroup = function(index) {
    $scope.variantGroupSelections.splice(index, 1);
    $scope.updateSelectedVariantsPreview();
  };

  // Add function to update selected variants preview
  $scope.updateSelectedVariantsPreview = function() {
    // Get all selected variants from all groups
    const allVariants = $scope.variantGroupSelections.reduce((acc, group) => {
        return acc.concat(group.variants);
    }, []);

    // Sort all variants alphabetically
    allVariants.sort();

    // Update the preview
    $scope.selectedVariantsPreview = allVariants.join('_');
  };

  $scope.openVariantGroupModal = function() {
    console.log('Opening variant group modal');
    $scope.showVariantGroupModal = true;
    $scope.selectedVariantGroup = null;
    $scope.$apply(); // Force digest cycle
  };

  $scope.closeVariantGroupModal = function() {
    console.log('Closing variant group modal');
    $scope.showVariantGroupModal = false;
    $scope.selectedVariantGroup = null;
    $scope.$apply(); // Force digest cycle
  };

  // Add deleteFolder function
  $scope.deleteFolder = function(folder) {
    if (!confirm('Are you sure you want to delete this folder? This action cannot be undone.')) {
        return;
    }

    $scope.isLoading = true;
    ImageUploadService.deleteFolder(folder.step, folder.name)
        .success(function(response) {
            $scope.successMessage = 'Folder deleted successfully';
            $scope.loadFoldersForStep($scope.currentStep);
            $scope.isLoading = false;
        })
        .error(function(error) {
            $scope.errorMessage = 'Failed to delete folder: ' + error;
            $scope.isLoading = false;
        });
  };

  // Initialize on load
  $scope.loadFoldersForStep('version');
  $scope.init();

  // Initialize modal state
  $scope.showNewFolderModal = false;

  // Add function to open new folder modal
  $scope.openNewFolderModal = function() {
      console.log('Opening new folder modal');
      $scope.showNewFolderModal = true;
      $scope.newFolderName = '';
      $scope.clearMessages();
      $scope.$apply(); // Force digest cycle
  };

  // Add function to close new folder modal
  $scope.closeNewFolderModal = function() {
      console.log('Closing new folder modal');
      $scope.showNewFolderModal = false;
      $scope.newFolderName = '';
      $scope.clearMessages();
      $scope.$apply(); // Force digest cycle
  };

  // Add file-related functions
  $scope.loadFilesForStep = function(step) {
    $scope.isLoading = true;
    ImageUploadService.getFilesForStep(step, $scope.formData)
        .success(function(response) {
            // Filter files based on current step's value
            $scope.currentStepFiles = response.filter(function(file) {
                // For custom steps, check if the step value matches
                if (!['version', 'region', 'priceProfile', 'orientation', 'slot', 'lcdType'].includes(step)) {
                    return file[step] === $scope.formData[step];
                }
                // For predefined steps, check if the step value matches
                return file[step] === $scope.formData[step];
            }).map(function(file) {
                return {
                    id: file.id,
                    name: file.name,
                    url: file.url,
                    type: file.type,
                    size: file.size,
                    modified: file.modified,
                    path: file.path,
                    version: file.version,
                    region: file.region,
                    priceProfile: file.priceProfile,
                    orientation: file.orientation,
                    slot: file.slot,
                    lcdType: file.lcdType,
                    variants: file.variants,
                    uploadTime: file.uploadTime
                };
            });
            $scope.isLoading = false;
        })
        .error(function(error) {
            $scope.errorMessage = 'Failed to load files: ' + error;
            $scope.isLoading = false;
        });
  };

  $scope.previewFile = function(file) {
    $scope.selectedFile = file;
    $scope.showPreviewModal = true;
  };

  $scope.closePreviewModal = function() {
    $scope.showPreviewModal = false;
    $scope.selectedFile = null;
  };

  $scope.editFile = function(file) {
    $scope.selectedFile = file;
    $scope.isEditMode = true;
    // Load file data into edit form
  };

  $scope.deleteFile = function(file) {
    if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
        return;
    }

    $scope.isLoading = true;
    ImageUploadService.deleteFile(file.id)
        .success(function(response) {
            $scope.successMessage = 'File deleted successfully';
            $scope.loadFilesForStep($scope.currentStep);
            $scope.isLoading = false;
        })
        .error(function(error) {
            $scope.errorMessage = 'Failed to delete file: ' + error;
            $scope.isLoading = false;
        });
  };

  // Update loadFoldersForStep to also load files
  $scope.loadFoldersForStep = function(step) {
    $scope.isLoading = true;
    ImageUploadService.getFoldersForStep(step, $scope.formData)
        .success(function(response) {
            $scope.currentStepFolders = response;
            $scope.loadFilesForStep(step);
        })
        .error(function(error) {
            $scope.errorMessage = 'Failed to load folders: ' + error;
            $scope.isLoading = false;
        });
  };

  // Add bytes filter for file size display
  $scope.$filter = function(name) {
    return function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
  };

  // Variant Management Modal Functions
  $scope.openVariantManagementModal = function() {
    $scope.showVariantManagementModal = true;
    $scope.loadVariantGroups();
  };

  $scope.closeVariantManagementModal = function() {
    $scope.showVariantManagementModal = false;
    $scope.selectedGroup = null;
    $scope.variantItems = [];
  };

  // Group Management Functions
  $scope.loadVariantGroups = function() {
    $scope.isLoading = true;
    ImageUploadService.getVariantGroups()
      .success(function(response) {
        $scope.variantGroups = response;
        $scope.isLoading = false;
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to load variant groups: ' + error;
        $scope.isLoading = false;
      });
  };

  $scope.openNewGroupModal = function() {
    $scope.editingGroup = false;
    $scope.newGroup = {
      groupName: '',
      status: 'ACTIVE'
    };
    $scope.showGroupModal = true;
  };

  $scope.editGroup = function(group) {
    $scope.editingGroup = true;
    $scope.newGroup = angular.copy(group);
    $scope.showGroupModal = true;
  };

  $scope.closeGroupModal = function() {
    $scope.showGroupModal = false;
    $scope.newGroup = {
      groupName: '',
      status: 'ACTIVE'
    };
  };

  $scope.saveGroup = function() {
    $scope.isLoading = true;
    if ($scope.editingGroup) {
      ImageUploadService.updateVariantGroup($scope.newGroup.id, $scope.newGroup)
        .success(function(response) {
          $scope.successMessage = 'Group updated successfully';
          $scope.loadVariantGroups();
          $scope.closeGroupModal();
        })
        .error(function(error) {
          $scope.errorMessage = 'Failed to update group: ' + error;
        })
        .finally(function() {
          $scope.isLoading = false;
        });
    } else {
      ImageUploadService.createVariantGroup($scope.newGroup)
        .success(function(response) {
          $scope.successMessage = 'Group created successfully';
          $scope.loadVariantGroups();
          $scope.closeGroupModal();
        })
        .error(function(error) {
          $scope.errorMessage = 'Failed to create group: ' + error;
        })
        .finally(function() {
          $scope.isLoading = false;
        });
    }
  };

  $scope.deleteGroup = function(group) {
    if (!confirm('Are you sure you want to delete this group? This will also delete all its items.')) {
      return;
    }
    $scope.isLoading = true;
    ImageUploadService.deleteVariantGroup(group.id)
      .success(function(response) {
        $scope.successMessage = 'Group deleted successfully';
        $scope.loadVariantGroups();
        if ($scope.selectedGroup && $scope.selectedGroup.id === group.id) {
          $scope.selectedGroup = null;
          $scope.variantItems = [];
        }
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to delete group: ' + error;
      })
      .finally(function() {
        $scope.isLoading = false;
      });
  };

  // Item Management Functions
  $scope.selectGroup = function(group) {
    $scope.selectedGroup = group;
     ImageUploadService.loadVariantItems(group.id).success(function(items) {
      $scope.variantItems = items;
    })
    .error(function(error) {
      $scope.errorMessage = 'Failed to load items: ' + error;
    });;
    console.log($scope.variantItems);
  };

  $scope.openNewItemModal = function() {
    $scope.editingItem = false;
    $scope.newItem = {
      itemName: '',
      groupId: $scope.selectedGroup.id,
      status: 'ACTIVE'
    };
    $scope.showItemModal = true;
  };

  $scope.editItem = function(item) {
    $scope.editingItem = true;
    item.groupId = $scope.selectedGroup.id;
    $scope.newItem = angular.copy(item);
    $scope.showItemModal = true;
  };

  $scope.closeItemModal = function() {
    $scope.showItemModal = false;
    $scope.newItem = {
      itemName: '',
      groupId: null,
      status: 'ACTIVE'
    };
  };

  $scope.saveItem = function() {
    $scope.isLoading = true;
    if ($scope.editingItem) {
      ImageUploadService.updateVariantItem($scope.newItem.id, $scope.newItem,$scope.selectedGroup.id)
        .success(function(response) {
          $scope.successMessage = 'Item updated successfully';
          ImageUploadService.loadVariantItems($scope.selectedGroup.id).success(function(items) {
            $scope.variantItems = items;
          })
          .error(function(error) {
            $scope.errorMessage = 'Failed to load items: ' + error;
          });;
          $scope.closeItemModal();
        })
        .error(function(error) {
          $scope.errorMessage = 'Failed to update item: ' + error;
        })
        .finally(function() {
          $scope.isLoading = false;
        });
    } else {
      ImageUploadService.createVariantItem($scope.newItem,$scope.selectedGroup.id)
        .success(function(response) {
          $scope.successMessage = 'Item created successfully';
          ImageUploadService.loadVariantItems($scope.selectedGroup.id).success(function(items) {
            $scope.variantItems = items;
          })
          .error(function(error) {
            $scope.errorMessage = 'Failed to load items: ' + error;
          });;
          $scope.closeItemModal();
        })
        .error(function(error) {
          $scope.errorMessage = 'Failed to create item: ' + error;
        })
        .finally(function() {
          $scope.isLoading = false;
        });
    }
  };

  $scope.deleteItem = function(item) {
    if (!confirm('Are you sure you want to delete this item?')) {
      return;
    }
    $scope.isLoading = true;
    ImageUploadService.deleteVariantItem(item.id)
      .success(function(response) {
        $scope.successMessage = 'Item deleted successfully';
        ImageUploadService.loadVariantItems($scope.selectedGroup.id).success(function(items) {
          $scope.variantItems = items;
        })
        .error(function(error) {
          $scope.errorMessage = 'Failed to load items: ' + error;
        });;
      })
      .error(function(error) {
        $scope.errorMessage = 'Failed to delete item: ' + error;
      })
      .finally(function() {
        $scope.isLoading = false;
      });
  };
}]);

// Service for API communication
adminapp.service('ImageUploadService', ['$http', function($http) {
  var baseUrl = window.location.origin + "/master-service/rest/v1/" + "lcd-menu-image";
  return {
    getVersions: function() {
      return $http.get(baseUrl + '/versions');
    },

    getMetadata: function() {
      return $http.get(baseUrl + '/metadata');
    },

    getImagesByVersion: function(version) {
      return $http.get(baseUrl + '/images/version/' + version);
    },

    searchImages: function(params) {
      return $http.get(baseUrl + '/images/search', { params: params });
    },

    searchImagesWithParams: function(params) {
          return $http.get(baseUrl + '/images/search-by-params', { params: params });
        },

    createVersion: function(version) {
      return $http.post(baseUrl  + '/versions?VersionNo=' + version);
    },

    uploadImages: function(formData) {
      return $http.post(baseUrl + '/images/upload-with-params', formData, {
        transformRequest: angular.identity,
        headers: {
          'Content-Type': undefined
        }
      });
    },

    updateImage: function(formData) {
      return $http.post(baseUrl + '/images/update', formData, {
        transformRequest: angular.identity,
        headers: {
          'Content-Type': undefined
        }
      });
    },

    deleteImage: function(imageId) {
      return $http.delete(baseUrl + '/images/' + imageId);
    },

    downloadImages: function(params) {
      return $http.get(baseUrl + '/images/download-by-params', {
        params: params,
        responseType: 'arraybuffer'  // Important for handling binary data
      });
    },

    getFolders: function(step) {
      return $http.get(baseUrl + '/folders/' + step);
    },

    createFolder: function(folderData) {
      return $http.post(baseUrl + '/folders', folderData);
    },

    deleteFolder: function(step, name) {
      return $http.delete(baseUrl + '/folders/' + step + '/' + name);
    },

    getFilesForStep: function(step, formData) {
      return $http.get(baseUrl + '/files/' + step, { params: formData });
    },

    getFoldersForStep: function(step, formData) {
      return $http.get(baseUrl + '/folders/' + step, { params: formData });
    },

    deleteFile: function(fileId) {
      return $http.delete(baseUrl + '/files/' + fileId);
    },

    getAllSteps: function() {
      return $http.get(baseUrl + '/steps');
    },

    createStep: function(stepName) {
      return $http.post(baseUrl + '/steps?name=' + stepName);
    },

    getVariantGroups: function() {
      return $http.get(baseUrl + '/groups');
    },

    updateVariantGroup: function(groupId, groupData) {
      return $http.put(baseUrl + '/groups/' + groupId, groupData);
    },

    createVariantGroup: function(groupData) {
      return $http.post(baseUrl + '/groups', groupData);
    },

    deleteVariantGroup: function(groupId) {
      return $http.delete(baseUrl + '/groups/' + groupId);
    },

    loadVariantItems: function(groupId) {
      return $http.get(baseUrl + '/items?groupId=' + groupId);
    },

    createVariantItem: function(itemData,groupId) {
      return $http.post(baseUrl + '/items?groupId=' + groupId, itemData);
    },

    updateVariantItem: function(itemId, itemData,groupId) {
      return $http.put(baseUrl + '/items/' + itemId + '?groupId=' + groupId, itemData);
    },

    deleteVariantItem: function(itemId) {
      return $http.delete(baseUrl + '/items/' + itemId);
    }
  };
}]);

adminapp.directive('lcdFileModel', ['$parse', '$timeout', function($parse, $timeout) {
    return {
      restrict: 'A',
      link: function(scope, element, attrs) {
        var model = $parse(attrs.lcdFileModel);

        element.bind('change', function(changeEvent) {
          var files = Array.from(changeEvent.target.files || []);
          $timeout(function() {
            model.assign(scope, files);
            if (scope.onFileSelect) {
              scope.onFileSelect(files);
            }
          });
        });
      }
    };
  }]);