/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        "recipeMediaBuilder",
        function ($rootScope, $scope, $location, $http, AppUtil, $cookieStore, fileService) {

            $scope.productsInfo = null;
            $scope.categoryLists = null;
            $scope.recipeProfiles = null;

            $scope.clearAll = function (askForConfirmation) {
                if (askForConfirmation) {
                    var result = confirm("Are You Sure You Want To Clear Everything?");
                    if (!result) {
                        return;
                    }
                }
                $scope.activeTab = 0;
                // Metadata Information
                $scope.todaysDate = new Date().toString();
                $scope.recipeDetail = createRecipeDetail();
                // Tab 1 Selections
                $("#select2-productSelected-container").html('');
                $scope.product = {};
                $scope.product.selectedProductId = null;
                $scope.selectedProduct = null;
                $scope.existingRecipes = null;
                $scope.selectedDimensionData = null;
                $scope.selectedDimensionProfile = null;
                $scope.clearCloneModal();
                $scope.recipeProfileAvailable = [];
                $scope.cloneRecipeProfile = false;
                $scope.selectedRecipeId = null;

                $scope.clearRecipeMediaDetails();
            };

            $scope.clearCloneModal = function () {
                $scope.cloneRecipeProfile = false;
                $scope.selectedCloneProductId = null;
                $scope.selectedCloneProduct = null;
                $scope.cloneRecipes = null;
                $scope.selectedCloneDimensionData = null;
                $scope.selectedCloneProfileData = null;
                $scope.selectedCloneDimensionProfile = null;
                $scope.updatedRecipeProfile = null;
            };

            $scope.clearRecipeMediaDetails = function () {
                $scope.newRecipeStepName = null;
                $scope.getRecipeMediaToUpload = null;
                $scope.uploadedMediaResponse = {};
                $scope.uploadedMediaType = null;

                $scope.recipeMediaDetail = [];
                $scope.recipeMediaDetail.recipe = {};
                $scope.recipeMediaDetail.id = null;
                $scope.recipeMediaDetail.mediaType = null;
                $scope.recipeMediaDetail.recipeStepName = null;
                $scope.recipeMediaDetail.stepCount = null;
                $scope.recipeMediaDetail.s3Key = null;

                $scope.cloneSelectedRecipeDetail = null;
            };

            $scope.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.getAllDimensions();
                $scope.getAllProducts();
                $scope.product = {};
                $scope.clearAll(false);
                getRecipeProfiles();
                $scope.clearRecipeMediaDetails();
            };

            function getRecipeProfiles() {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.recipeProfiles,
                    params: {type: "NON_SCM"}
                }).then(function success(response) {
                    $scope.recipeProfiles = response.data;
                }, function error(response) {
                    console.log("error:", response);
                });
            }

            $scope.cloneRecipeByProfile = function (selectedRecipeId) {
                if ($scope.selectedRecipeId === null || $scope.selectedRecipeId === undefined) {
                    alert("Please select any recipe to clone");
                    return false;
                }
                for (var i = 0; i < $scope.existingRecipes.length; i++) {
                    if ($scope.selectedRecipeId == $scope.existingRecipes[i].recipeId) {
                        $scope.cloneRecipes = [];
                        if ($scope.existingRecipes[i].product.classification != "MENU") {
                            alert("You can only create profiles for menu product!");
                            return false;
                        }
                    }
                }
                $scope.clearCloneModal();
                $scope.cloneRecipeProfile = true;
                for (var i = 0; i < $scope.existingRecipes.length; i++) {
                    if ($scope.selectedRecipeId == $scope.existingRecipes[i].recipeId) {
                        $scope.cloneRecipes = [];
                        $scope.cloneRecipes.push($scope.existingRecipes[i]);
                        break;
                    }
                }

                console.log("Existing recipes: ", $scope.existingRecipes.toString() + " " + $scope.selectedRecipeId)
                $scope.recipesAvailableForClone = [];
                for (var i = 0; i < $scope.existingRecipes.length; i++) {
                    if ($scope.existingRecipes[i].recipeId != $scope.selectedRecipeId) {
                        $scope.recipesAvailableForClone.push($scope.existingRecipes[i]);
                    }
                }

                $("#cloneRecipeModal").modal("show");
            };

            $scope.changeRecipeId = function (selectedRecipeId) {
                $scope.selectedRecipeId = selectedRecipeId;
            };

            function createRecipeDetail() {
                var recipe = {
                    status: 'ACTIVE',
                    profile: 'P0',
                    ingredient: {
                        products: [],
                        variants: [],
                        components: [],
                    },
                    addons: [],
                    dineInConsumables: [],
                    deliveryConsumables: [],
                    takeawayConsumables: []
                };
                return recipe;
            };

            $scope.selectOptions = {
                width: '100%'
            };

            $scope.setNextTab = function (tabNo) {
                var errors = validate(tabNo, $scope.recipeDetail);
                if (errors.length != 0) {
                    alert(printErrors(errors));
                    return;
                }
                $scope.activeTab = tabNo + 1;
            };

            function validate(tabNo, recipe) {
                var errors = [];
                switch (tabNo) {

                    case 0:
                        if (angular.isUndefined(recipe.product) || recipe.product == null) {
                            addError(errors, -1, "Product Not Selected");
                        }
                        if (angular.isUndefined(recipe.dimension) || recipe.dimension == null) {
                            addError(errors, -1, "Dimension for the product Not Selected");
                        }
                        break;
                    case 1:
                        if (recipe.name == null || !recipe.name.endsWith('Recipe')) {
                            addError(errors, -1, "Incorrect Recipe Name");
                        }
                        if (recipe.startDate == null) {
                            addError(errors, -1, "Start Date Not Entered");
                        }
                        break;
                    case 2:
                        errors = errors.concat(validateIngredient(recipe))
                        break;
                    case 3:
                        if (!isEmptyArray(recipe.addons)) {
                            errors = errors.concat(validateOtherProducts('Addons List', recipe.addons));
                        }
                        break;
                    case 4:
                        if (!isEmptyArray(recipe.dineInConsumables)) {
                            errors = errors.concat(validateOtherProducts('Dine In Consumables List',
                                recipe.dineInConsumables));
                        }
                        break;
                    case 5:
                        if (!isEmptyArray(recipe.deliveryConsumables)) {
                            errors = errors.concat(validateOtherProducts('Delivery Consumables List',
                                recipe.deliveryConsumables));

                        }
                        break;
                    case 6:
                        if (!isEmptyArray(recipe.takeawayConsumables)) {
                            errors = errors.concat(validateOtherProducts('Takeaway Consumables List',
                                recipe.takeawayConsumables));

                        }
                        break;
                }
                return errors;
            }

            $scope.setPrevTab = function (tabNo) {
                if (tabNo == 1) {
                    var result = confirm("This will wipe out all changes. Are you sure?");
                    if (!result) {
                        return;
                    } else if (result == true) {
                        $scope.cloneRecipeProfile = false;
                    }
                }
                $scope.activeTab = tabNo - 1;
                console.log($scope.recipeDetail);
            };

            function isEmptyArray(array) {
                if (angular.isUndefined(array) || array == null || array.length == 0) {
                    return true;
                }
                return false;
            }

            $scope.selectRecipeForEdit = function (recipe) {
                recipe.startDate = moment(recipe.startDate).format('YYYY-MM-DD');
                $scope.recipeDetail = recipe;

                $scope.isRecipeMediaForEdit = true;
                $scope.recipeMediaId = null;
                $scope.getRecipeStepMediaByRecipeId(recipe.recipeId); // called to search and get all recipe steps

                $scope.setNextTab(0);
            };

            $scope.editActiveRecipe = function (recipe) {
                $scope.recipeForEdit = recipe;
                $scope.showStartDateMandatory = false;
                recipe.startDate = moment(recipe.startDate).format('YYYY-MM-DD');
                $scope.recipeDetail = recipe;

                $scope.isRecipeMediaForEdit = true;

                $scope.recipeMediaDetail = [];
                $scope.recipeMediaId = null;
                $scope.getRecipeStepMediaByRecipeId(recipe.recipeId);
                console.log($scope.recipeMediaDetail);

                $scope.setNextTab(0);
            };

            //to pause the video player on modal close
            $("#displayRecipeVideoModal").on("hidden.bs.modal", function () {
                var video = angular.element('#videoPlayer');
                video[0].pause();
            });

            $scope.selectRecipeForClone = function (recipe) {
                $scope.recipeDetail = JSON.parse(JSON.stringify(recipe, function (key, value) {
                    if (key === "_id" || key === "recipeId") {
                        return undefined;
                    }
                    return value;
                }));
                if ($scope.cloneRecipeProfile && $scope.updatedRecipeProfile == null) {
                    alert("Please select Recipe Profile.");
                    return false;
                }
                $scope.recipeDetail.startDate = moment($scope.recipeDetail.startDate).format('YYYY-MM-DD');
                // if ($scope.updatedRecipeProfile != null) {
                //     $scope.recipeDetail.profile = $scope.updatedRecipeProfile;
                // }

                $scope.isRecipeMediaForEdit = false;
                $scope.selectedRecipeMediaId = null;
                $scope.recipeMediaId = null;
                $scope.getRecipeStepMediaByRecipeId($scope.cloneSelectedRecipeDetail.recipeId);
                $scope.getSelectedRecipeMediaDetail();


                $scope.createNewRecipeData($scope.selectedProduct, $scope.selectedDimensionData);
                $("#cloneRecipeModal").modal("hide");
                $scope.setNextTab(0);

            };


            $scope.selectCloneProductProfile = function (profile) {
                console.log(profile);
                $scope.updatedRecipeProfile = profile;
            };

            $scope.selectCloneRecipeDetail = function (recipeDetail) {
                $scope.cloneSelectedRecipeDetail = JSON.parse(recipeDetail);
                $scope.selectCloneProductProfile($scope.cloneSelectedRecipeDetail.profile);
            };

            $scope.filterFunction = function (element) {
                if (!$scope.cloneRecipeProfile) {
                    return element.profile == 'P0';
                }
                return true;
            };

            $scope.findAllRecipes = function (productId, dimensionId, callback) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.findRecipe + '?productId=' + productId + '&dimensionId=' + dimensionId + '&active=true'
                }).then(function success(response) {
                    console.log('Found Recipes', response.data);
                    $scope.recipeProfileAvailable = $scope.recipeProfiles.slice();
                    for (var i = 0; i < response.data.length; i++) {
                        for (var j = 0; j < $scope.recipeProfileAvailable.length; j++) {
                            if (response.data[i].profile == $scope.recipeProfileAvailable[j].name) {
                                $scope.recipeProfileAvailable.splice(j, 1);
                            }
                        }
                    }
                    $scope.checkEditable(response.data);
                    callback(response.data);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            // Tab 1 utility methods
            $scope.getAllProducts = function () {
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.productMetaData.products
                }).then(function success(response) {
                    $scope.productsInfo = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            // Tab 1 utility methods
            $scope.getAllDimensions = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.listTypes
                }).then(function success(response) {
                    $scope.categoryLists = response.data;
                    $scope.productCategory = $scope.categoryLists.CATEGORY;
                    $scope.productDimension = $scope.categoryLists.DIMENSION;
                });
            };

            $scope.onSelectProduct = function (selectedProductId) {
                console.log("selecting product")
                console.log($scope.product.selectedProductId + " " + selectedProductId);
                var p = getDetail(selectedProductId, $scope.productsInfo);
                if (!$scope.validateProduct(p)) {
                    return;
                }
                $scope.cloneRecipeProfile = false;
                $scope.selectedProduct = p;
                $scope.recipeDetail = createRecipeDetail();
                $scope.selectedDimensionProfile = getDimensionDetail($scope.selectedProduct.dimensionProfileId,
                    $scope.productDimension);
                if ($scope.selectedDimensionData != null) {
                    $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function (value) {
                        $scope.existingRecipes = value;
                    });
                }
            };

            $scope.checkEditable = function (existingRecipes) {
                existingRecipes.map(function (recipe) {
                    if (recipe.status == "ACTIVE") {
                        var found = false;
                        existingRecipes.map(function (match) {
                            if (recipe.profile == match.profile && match.status == "IN_PROGRESS") {
                                found = true;
                            }
                        });
                        recipe.isEditable = !found;
                    }
                    if (recipe.status == "IN_PROGRESS") {
                        recipe.isEditable = true;
                    }
                });
                console.log($scope.existingRecipes);
            };

            $scope.validateProduct = function (p) {
                if (p == null) {
                    return true;
                }
                if ((p.taxCode == undefined && p.code == undefined) || (p.taxCode != undefined && p.taxCode == null) || (p.code != undefined && p.code == null)) {
                    bootbox.alert("Please set the Tax Category Code for " + p.name +
                        ' before adding its recipe');
                    return false;
                }
                return true;
            };

            $scope.selectDimension = function (dimensionId) {
                if ($scope.selectedDimensionProfile == null) {
                    return;
                }
                $scope.cloneRecipeProfile = false;
                $scope.selectedDimensionData = createDimensionDetail(getDetail(dimensionId,
                    $scope.selectedDimensionProfile.content));
                $scope.findAllRecipes($scope.selectedProduct.id, $scope.selectedDimensionData.infoId, function (value) {
                    $scope.existingRecipes = value;
                    $scope.selectedRecipeId = null;
                });
            };

            $scope.addNewRecipeMedia = function () {
                $scope.recipeDetail = createRecipeDetail();
                $scope.createNewRecipeData($scope.selectedProduct, $scope.selectedDimensionData);
                $scope.setNextTab(0);
            };

            $scope.deleteRecipeStepMedia = function (index) {

                var result = confirm("Do you really want to delete?");

                if (result) {
                    $scope.recipeMediaDetail.splice(index, 1);
                }

            };

            $scope.getRecipeStepMediaByRecipeId = function (recipeId) {

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.getRecipeStepByRecipeId,
                    params: {recipeId: recipeId}
                }).success(function (response) {
                    // $scope.$apply(); //for making synchronous
                    console.log(response);
                    if (response == null || response[0] == null) {
                        $scope.recipeMediaDetail = [];
                        return [];
                    }
                    console.log("Recipe steps", response[0].recipeSteps);
                    var recipeStepMediaDetail = [];
                    $scope.recipeMediaId = response[0]._id;
                    for (var i = 0; i < response[0].recipeSteps.length; i++) {
                        var item = response[0].recipeSteps[i];
                        console.log("item: ", item.recipeStepName);
                        var responseItem = {};
                        responseItem.mediaType = item.mediaType;
                        responseItem.recipeStepName = item.recipeStepName;
                        responseItem.stepCount = item.stepCount;
                        responseItem.s3Key = item.s3Key;
                        recipeStepMediaDetail.push(responseItem);
                    }
                    console.log("Checking response: ", recipeStepMediaDetail);
                    $scope.recipeMediaDetail = recipeStepMediaDetail;
                    return recipeStepMediaDetail;
                }).error(function (response) {
                    console.log(response);
                    return [];
                });

            };

            $scope.getSelectedRecipeMediaDetail = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.recipeManagement.getRecipeStepByRecipeId,
                    params: {recipeId: $scope.selectedRecipeId}
                }).success(function (response) {
                    console.log(response);
                    if (response == null || response[0] == null) {
                        return;
                    }
                    $scope.selectedRecipeMediaId = response[0]._id;
                }).error(function (response) {
                    console.log(response);
                });
            };

            $scope.addRecipeStepMediaDetail = function () {
                $scope.newRecipeStepName = null;
                $scope.uploadedMediaType = null;
                $scope.uploadedMediaResponse = null;
            };

            $scope.addRecipeMediaDetailVal = function () {
                console.log("check......", $scope.getRecipeMediaToUpload);
                if ($scope.newRecipeStepName == null) {
                    alert("Recipe step name cannot be empty");
                    return;
                }
                if ($scope.uploadedMediaResponse == null) {
                    alert("Please upload recipe step media");
                    return;
                }

                if ($scope.recipeMediaDetail == null) {
                    $scope.recipeMediaDetail = [];
                }

                $scope.recipeMediaDetail.push({
                    "mediaType": $scope.uploadedMediaType,
                    "recipeStepName": $scope.newRecipeStepName,
                    "stepCount": $scope.recipeMediaDetail.length + 1,
                    "s3Key": $scope.uploadedMediaResponse.s3Key
                });

                $scope.newRecipeStepName = null;
                $scope.uploadedMediaResponse = null;
                $scope.uploadedMediaType = null;
                angular.element("input[type='file']").val(null);

                $scope.hideAddRecipeModal();

            };

            $scope.editRecipeStepMediaDetail = function (index) {
                $scope.editRecipeStepIndex = index;
                $scope.newRecipeStepName = $scope.recipeMediaDetail[index].recipeStepName;
                $scope.uploadedMediaType = $scope.recipeMediaDetail[index].mediaType;
                $scope.uploadedMediaResponse = {};
                $scope.uploadedMediaResponse.s3Key = $scope.recipeMediaDetail[index].s3Key;
            };

            $scope.editRecipeMediaDetailVal = function () {

                if ($scope.newRecipeStepName == null) {
                    alert("Recipe step name cannot be empty");
                    return;
                }
                if ($scope.uploadedMediaResponse == null) {
                    alert("Please upload recipe step media");
                    return;
                }

                var index = $scope.editRecipeStepIndex;
                console.log("Check index ", index);
                $scope.recipeMediaDetail[index].recipeStepName = $scope.newRecipeStepName;
                $scope.recipeMediaDetail[index].mediaType = $scope.uploadedMediaType;
                $scope.recipeMediaDetail[index].s3Key = $scope.uploadedMediaResponse.s3Key;
                console.log("Check edit ", $scope.recipeMediaDetail[index]);

                $scope.newRecipeStepName = null;
                $scope.uploadedMediaType = null;
                $scope.uploadedMediaResponse = null;
                angular.element("input[type='file']").val(null);

                $scope.hideEditRecipeModal();
            };

            $scope.hideAddRecipeModal = function () {
                $("#addRecipeMedia").modal("hide");
            };

            $scope.hideEditRecipeModal = function () {
                $("#editRecipeStepMedia").modal("hide");
            };

            $scope.resetRecipeStepMediaDetail = function () {
                $scope.newRecipeStepName = null;
                $scope.uploadedMediaType = null;
                $scope.uploadedMediaResponse = null;
                angular.element("input[type='file']").val(null);
            };

            $scope.uploadRecipeMedia = function () {

                // if (fileService.getFile() == null
                //     || fileService.getFile() == undefined) {
                //     bootbox.alert("Please select an Image");
                //     return false;
                // }

                if (fileService.getFile() == null) {
                    alert("File not found");
                    return;
                }
                var fileType = $scope.getRecipeMediaToUpload.type;
                console.log("File Type: " + $scope.getRecipeMediaToUpload.type);

                if (angular.equals(fileType, "image/jpeg")) {
                    console.log("File Size: " + $scope.getRecipeMediaToUpload.size);
                    if (($scope.getRecipeMediaToUpload.size) / 1024 > 200) {
                        alert("Image size larger than 200 KB");
                        return;
                    }
                } else if (angular.equals(fileType, "video/mp4")) {
                    console.log("File Size: " + $scope.getRecipeMediaToUpload.size);
                    if (($scope.getRecipeMediaToUpload.size) / 1024 > (7 * 1024)) {
                        alert("Video size larger than 7 MB");
                        return;
                    }
                } else {
                    alert("File not a jpeg or mp4");
                    return;
                }

                updateFileUploadName(); //change file name before uploading

                var fd = new FormData();
                fd.append("mediaType", "list");
                fd.append("file", $scope.getRecipeMediaToUpload);
                $rootScope.showDetailLoader = true;
                var URL = AppUtil.restUrls.recipeManagement.uploadRecipeMedia;
                $http({
                    url: URL,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showDetailLoader = false;
                    $scope.getRecipeMediaToUpload = null;
                    fileService.push(null);
                    angular.element("input[type='file']").val(null);
                    $scope.uploadedMediaResponse = response;
                    $scope.uploadedMediaType = fileType;
                    alert("File uploaded!");
                }).error(function (response) {
                    $rootScope.showDetailLoader = false;
                    alert("Error while uploading Media");
                });
            };

            function updateFileUploadName() {
                var fileNameSubstr = "";
                if ($scope.newRecipeStepName != null) {
                    fileNameSubstr = $scope.newRecipeStepName.split(' ').join('_');
                }
                var newFileName = $scope.recipeDetail.recipeId + "_" + fileNameSubstr + "_" + Date.now() + "." + getFileExtension($scope.getRecipeMediaToUpload.name);

                $scope.getRecipeMediaToUpload = new File([$scope.getRecipeMediaToUpload], newFileName);
            }

            function getFileExtension(fileName) {
                var re = /(?:\.([^.]+))?$/;
                return re.exec(fileName)[1];
            }

            $scope.getDimensionData = function () {
                var code = $scope.selectedProduct.unitOfMeasure;
                var id = 0;
                if (code == 'KG') {
                    id = 0;
                } else if (code == 'GM') {
                    id = 1;
                } else if (code == 'ML') {
                    id = 2;
                } else if (code == 'L') {
                    id = 3;
                } else if (code == 'PC') {
                    id = 4;
                } else if (code == 'PACKET') {
                    id = 5;
                } else if (code == 'SACHET') {
                    id = 6;
                } else if (code == 'PKT') {
                    id = 7;
                } else {
                    id = -1;
                }
                return {
                    infoId: id,
                    code: code,
                    name: code
                }
            };

            $scope.createNewRecipeData = function (product, dimension) {

                for (var i = 0; i < $scope.existingRecipes.length; i++) {
                    if (angular.equals($scope.existingRecipes[i].recipeId, $scope.selectedRecipeId)) {
                        $scope.recipeDetail.product = $scope.existingRecipes[i].product;
                        $scope.recipeDetail.dimension = $scope.existingRecipes[i].dimension;
                        $scope.recipeDetail.recipeId = $scope.selectedRecipeId;
                        $scope.recipeDetail._id = $scope.existingRecipes[i]._id;
                    }
                }
                $scope.recipeDetail.name = $scope.recipeDetail.product.name;
                if ($scope.recipeDetail.dimension.name != 'None') {
                    $scope.recipeDetail.name = $scope.recipeDetail.name + ' '
                        + $scope.recipeDetail.dimension.name
                }
                $scope.recipeDetail.name = $scope.recipeDetail.name + ' Recipe';

                // $scope.recipeDetail.product = createProductBasicDetail(product);
                // $scope.recipeDetail.dimension = dimension;
                // $scope.recipeDetail.name = $scope.recipeDetail.product.name;
                // if ($scope.recipeDetail.dimension.name != 'None') {
                //     $scope.recipeDetail.name = $scope.recipeDetail.name + ' '
                //         + $scope.recipeDetail.dimension.name
                // }
                // $scope.recipeDetail.name = $scope.recipeDetail.name + ' Recipe';
            };

            $scope.cloneFromRecipe = function () {
                $scope.clearCloneModal();
                $("#cloneRecipeModal").modal("show");
            };

            $scope.onChangeSelectedCloneProduct = function (productId) {
                $scope.selectedCloneProduct = {};
                var productInfo = getDetail(productId, $scope.productsInfo);
                $scope.selectedCloneProduct.product = createProductBasicDetail(productInfo);
                $scope.selectedCloneProductDimensionProfile = getDimensionDetail(
                    productInfo.dimensionProfileId, $scope.productDimension);
                $scope.selectCloneProductDimension($scope.selectedCloneProductDimensionProfile.content[0].id);
            };

            $scope.selectCloneProductDimension = function (dimensionId) {
                console.log(dimensionId);
                $scope.selectedCloneDimensionData = getDetail(dimensionId,
                    $scope.selectedCloneProductDimensionProfile.content);
                $scope.selectedCloneProduct.dimension = $scope.selectedCloneDimensionData;
                $scope.findAllRecipes($scope.selectedCloneProduct.product.productId, dimensionId, function (value) {
                    $scope.cloneRecipes = value;
                });
            };

            function createProductBasicDetail(product) {
                var data = {};
                if (product == null) {
                    return data;
                }
                data.productId = product.id;
                data.name = product.name;
                data.type = product.type;
                data.subType = product.subType;
                data.shortCode = product.shortCode;
                data.classification = product.classification;
                data.taxCode = product.taxCode;
                return data;
            }

            function createDimensionDetail(dimension) {
                var data = {};
                data.infoId = dimension.id;
                data.name = dimension.name;
                data.code = dimension.code;
                data.shortCode = dimension.shortCode;
                data.type = dimension.type;
                data.status = dimension.status;
                return data;
            }

            $scope.downloadRecipeMedia = function (recipeStep) {
                var mediaType = recipeStep.mediaType;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.recipeMediaManagement.downloadRecipeMediaByStep,
                    data: recipeStep,
                    responseType: 'blob'
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    var blob = new Blob([response.data], {
                        type: mediaType
                    });
                    saveAs(blob, recipeStep.recipeStepName + '.' + mediaType.substring(mediaType.lastIndexOf('/') + 1));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });
            };

            $scope.submitRecipeMediaDetail = function () {
                // var data = $cookieStore.get('adminglobals');
                // if (data != undefined && data != null) {
                //     $scope.recipeDetail.lastUpdatedByName = data.user.name;
                //     $scope.recipeDetail.lastUpdatedById = data.user.id;
                // }
                $rootScope.showFullScreenLoader = true;
                var url = null;
                var action = "ADD";

                var recipeMediaDetail = {};
                if ($scope.selectedRecipeId != null && $scope.isRecipeMediaForEdit !== true) {
                    recipeMediaDetail = {
                        _id: $scope.selectedRecipeMediaId,
                        recipeId: $scope.selectedRecipeId,
                        recipeSteps: $scope.recipeMediaDetail,
                        name: $scope.recipeDetail.name,
                        product: $scope.recipeDetail.product,
                        dimension: $scope.recipeDetail.dimension
                    };
                } else {
                    recipeMediaDetail = {
                        _id: $scope.recipeMediaId,
                        recipeId: $scope.recipeDetail.recipeId,
                        recipeSteps: $scope.recipeMediaDetail,
                        name: $scope.recipeDetail.name,
                        product: $scope.recipeDetail.product,
                        dimension: $scope.recipeDetail.dimension
                    };
                }
                console.log($scope.recipeDetail.product);
                var reqObj = recipeMediaDetail;
                url = AppUtil.restUrls.recipeManagement.addRecipeMedia;
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: url,
                    data: reqObj
                }).then(function success(response) {
                    alert("Recipe media " + (action === "ADD" ? "added" : "updated") + " Successfully");
                    $scope.clearAll(false);
                    $rootScope.showFullScreenLoader = false;
                    window.location.reload();
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            function printErrors(errors) {
                var data = null;
                if (errors != null) {
                    data = 'Errors : ';
                    for (var index in errors) {
                        data = data + 'Row Number : ' + errors[index].index + ' - ' + errors[index].error + ',';

                    }
                }
                return data;

            }

            function addError(array, index, error) {
                array.push(getErrorMessage(index, error));
            }

            function getErrorMessage(index, error) {
                var obj = {
                    index: index,
                    error: error
                };
                return obj;
            }

            // Generic Function
            function getDetail(id, list) {
                for (var index in list) {
                    if (list[index].id == id) {
                        return list[index];
                    }
                }
                return null;
            }

            function getDimensionDetail(id, list) {
                for (var index in list) {
                    if (list[index].detail.id == id) {
                        return list[index];
                    }
                }
                return null;
            }

        });
