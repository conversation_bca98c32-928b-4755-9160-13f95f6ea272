	/*
	 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
	 * __________________
	 *
	 * [2015] - [2017] Sunshine Teahouse Private Limited
	 * All Rights Reserved.
	 *
	 * NOTICE:  All information contained herein is, and remains
	 * the property of Sunshine Teahouse Private Limited and its suppliers,
	 * if any.  The intellectual and technical concepts contained
	 * herein are proprietary to Sunshine Teahouse Private Limited
	 * and its suppliers, and are protected by trade secret or copyright law.
	 * Dissemination of this information or reproduction of this material
	 * is strictly forbidden unless prior written permission is obtained
	 * from Sunshine Teahouse Private Limited.
	 */

	adminapp.controller("unitDeliveryMappingctrl", function($scope,$rootScope,$http,AppUtil){
		$scope.delivery=[];
		$scope.priority=[];
		$scope.deliveryDupsData=[];
	$scope.loading = false;	
		$http({
			method: 'GET',
			  url: AppUtil.restUrls.unitMetaData.families
			}).then(function success(response) {
				$scope.families = response.data;
				$scope.families.push('ALL');
				console.log("families=="+$scope.families);
				$scope.newUnitFamily = $scope.families;
			}, function error(response) {
				  console.log("error:"+response);
		});
		

		$scope.showUnitFamilyDetails = function (familyData) {
			 $http({
				  method: 'GET',
				  url: AppUtil.restUrls.unitMetaData.allUnits+'?category='+familyData
				}).then(function success(response) {
					$scope.unitlist = response.data;
					console.log("allUnitsss=",$scope.unitlist);
					$scope.selectedUnitData=$scope.unitlist[0];
					//$rootScope.showFullScreenLoader = false;
					
				}, function error(response) {
					//$rootScope.showFullScreenLoader = false;
					  console.log("error:"+response);
				});
			}
		
		$scope.checkAllDelivery=function()
		{
			$scope.deliveryDupsData=[];
			//$scope.delivery=[];
			if($scope.allCheckedDelivery=='YES'){
				//$scope.deliveryDups={};
				
				$scope.unitDeliveryDetail.forEach(function(deliveryUnits){
					$scope.deliveryDups={
							checked:true,
							mappingId:deliveryUnits.mappingId,
							priority:deliveryUnits.priority,
							changePriority:deliveryUnits.priority,
							detail:deliveryUnits.detail	
					}
					
					$scope.priority[deliveryUnits.mappingId]	=	deliveryUnits.priority;
					$scope.delivery[deliveryUnits.detail.id]	=	{checked:true};
					$scope.deliveryDupsData.push($scope.deliveryDups);
					
				});
			}
			else
			{
				$scope.unitDeliveryDetail.forEach(function(deliveryUnits){
					$scope.deliveryDups={
							checked:false,
							mappingId:deliveryUnits.mappingId,
							priority:deliveryUnits.priority,
							changePriority:"",
							detail:deliveryUnits.detail	
					}
					$scope.priority[deliveryUnits.mappingId]	=	"";
					$scope.delivery[deliveryUnits.detail.id]	=	{checked:false};
					$scope.deliveryDupsData.push($scope.deliveryDups);
				});
				
			}
			console.log($scope.deliveryDupsData);
		}
		
		$scope.checkDelivery=function(detailID,mappingId,priority,detail)
		{
			//$scope.deliveryDupsData=[];
			if($scope.delivery[detailID].checked==true){
				$scope.deliveryDups={
					checked:true,
					mappingId:mappingId,
					priority:priority,
					changePriority:priority,
					detail:detail
					}
			$scope.priority[mappingId]	=	priority;	
			$scope.deliveryDupsData.push($scope.deliveryDups);
			console.log($scope.deliveryDupsData);
			//console.log("true=",$scope.deliveryDupsData);
			}
			
		if($scope.delivery[detailID].checked==false){
			$scope.deliveryDups={
				checked:false,
				mappingId:mappingId,
				priority:priority,
				changePriority:"",
				detail:detail
				}
		    $scope.priority[mappingId]	=	"";	
			$scope.deliveryDupsData=$scope.deliveryDups;
			console.log("false=",$scope.deliveryDupsData);
		}
		}
			
		
		$scope.showUnitDeliveryDetails = function (unitDeliveryData) {
			$rootScope.showFullScreenLoader = true;
			$scope.unitDeliveryDetai="";
			$http({
		        method: 'POST',
		        url: AppUtil.restUrls.unitMetaData.viewUnitDeliveryPartners,
		        data: $scope.selectedUnitData.id,
		    }).then(function success(response) {
		        if (response.status==200) {
		        	$scope.unitDeliveryDetail=response.data;
		        	$rootScope.showFullScreenLoader = false;
		        	$scope.allCheckedDelivery={checked:false};
		        	for(i in $scope.unitDeliveryDetail){
		        		$scope.priority[$scope.unitDeliveryDetail[i].mappingId]="";
						console.log($scope.delivery[$scope.unitDeliveryDetail[i].detail.id]={checked:false});
		        	}
		        		console.log($scope.unitDeliveryDetail);
		        		$("#viewDeliveryPartnerListDiv").show();
		        		console.log(response.data)
		        }
		        });
		    }
		
		$scope.SubmitUnitDelivery = function () {
		
		}
		
		
		$scope.changePriorityValue=function(detailID,mappingId,priority,detail,checkDeliveryData)
		{
			//$scope.delivery=[];
			console.log("checking=",checkDeliveryData.detail.id);
			console.log($scope.delivery[checkDeliveryData.detail.id]={checked:true});
			//console.log("details=",detail.id);
			
			//console.log("checking=",$scope.delivery[detail.id].checked);
			/*if($scope.delivery[detailID].checked==false)
				{
				alert("Please check the checkbox first to change the Priority");
				return false;
				}
			
			console.log("Result=",$scope.delivery[detailID].checked);
			console.log("detailID=",detailID);
			console.log("mappingID=",mappingId);
			
			console.log("detail=",detail);
			
			console.log($scope.priority[checkDeliveryData.mappingId]);
			
			console.log("priority=",priority);*/
			
			console.log(console.log($scope.priority[checkDeliveryData.mappingId]));
			
			if($scope.delivery[detailID].checked==true){
				$scope.delivery[detailID]={
					checked:true,
					mappingId:mappingId,
					priority:parseInt($scope.priority[checkDeliveryData.mappingId]),
					detail:detail
					}
			    	$scope.priority[mappingId]	=	parseInt($scope.priority[checkDeliveryData.mappingId]);	
					console.log("fff=",$scope.delivery);
			}
			
		/*if($scope.delivery[detailID].checked==false){
			$scope.delivery[detailID]={
				checked:false,
				mappingId:mappingId,
				priority:priority,
				detail:detail
				}
		    	$scope.priority[mappingId]	=	"";	
				console.log("fff=",$scope.delivery);
		}*/
		}
		
		$scope.submitDeliveryDetails=function()
		{
			
			$scope.deliveryModifyList=[];
			$scope.deliveryDupsData.forEach(function(deliveryDupsDataDetail){
				console.log("fff=",deliveryDupsDataDetail);
				if(deliveryDupsDataDetail.checked==true){
					$scope.deliveryModifyList.push(deliveryDupsDataDetail);
				}
			});
			
			$("#deliveryUpdatedModal").modal("show");
			console.log($scope.deliveryModifyList);
		}
		
		
});
