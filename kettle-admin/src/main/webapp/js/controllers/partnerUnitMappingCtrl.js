/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerUnitMappingCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http','$cookieStore',
        function ($location, $scope, AppUtil, $rootScope, $http, $cookieStore) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.init = function () {
                $rootScope.enableScreenFilter = true;
                $scope.regions = [];
                $scope.screenType = [];
                $scope.crmObject = {};
                $scope.userDetails=AppUtil.getUserValues();
                $scope.unitMenuMappingRegions = [];
                getRegionDetails();
                $scope.channelPartnerList = [];
                $scope.menus = [];
                $scope.getUnitList();
                $scope.getChannelPartnersList();
                $scope.event=null;
                $scope.channelPartners = [];
                $scope.mappingActions = [
                    {id: 1, name: "ACTIVATE", value: true},
                    {id: 2, name: "DEACTIVATE", value: false}
                ];
                $scope.appList = [{id: 1, name: "CHANNEL_PARTNER"}, {id: 2, name: "KETTLE"}, {
                    id: 3,
                    name: "DINE_IN_APP"
                }];
                $scope.selectedApp = $scope.appList[0];
                $scope.unitChannelPartnerMappings = [];
                $scope.selectAll = false;
                $scope.unitPartnerBrandMappings = [];
                $scope.actionList = ["UNIT MAPPING"];
                $scope.selectedAction = "UNIT MAPPING";
                $scope.allActions = $scope.mappingActions[0];
                // $scope.menuType = ["DEFAULT", "SINGLE_SERVE"];
                $scope.isSelectedPartnerZomato = false;
                $scope.selectAction($scope.selectedAction);
                $scope.today = new Date();
                $scope.newTag = {id: null, name: null};
                $scope.getMenus();
                // $scope.getMenuType();
                $scope.selectedRegion = null;
                $scope.imageSuffix = AppUtil.getImageUrl().crmImage;
                $scope.screenDetails = null;
                $scope.selectedUnit = [];
                $scope.selectLiveDate = null;
                $scope.selectEditLiveDate={};
                $rootScope.selectEditLiveDateFlags={};
                $scope.selectUpdateLiveDateFlags={};
                $scope.swiggyCloudKitchen = false;
                // $scope.partnerRestaurantId =null;
                $scope.unitMultiSelect = [];
                // $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
                $scope.mappingStatusList = [
                    {id: 1, name: "ACTIVE", value: "ACTIVE"},
                    {id: 2, name: "IN_ACTIVE", value: "IN_ACTIVE"}
                ];
                $scope.statusMap = {"ACTIVE": $scope.mappingStatusList[0], "IN_ACTIVE": $scope.mappingStatusList[1]};
                $scope.globalSelect = false;
                $scope.globalStatus = null;
                $scope.newStatus = null;
                $scope.unitDetail = [];
                $scope.unitIdToUnitNameObj = {};
                $scope.unitNameToUnitRegionObj = {};
                $scope.selectedRegion = null;
                $scope.filteredScreenDetails = [];
                $scope.isUpdateScreen = false;
                $scope.unitToBeUpdated = null;
                $scope.listImageUploaded = false;
                getRegionDetails();
                $scope.hasFinanceHeadPermissions = false ;
                $scope.getUserAclData();
            };

            $scope.getUserDetails = function (region) {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.allUnits+ "?category=CAFE",
                    data: {
                        "employeeId": $scope.userDetails.user.id,
                        "onlyActive": true,
                        "category":"CAFE"
                    },
                }).then(function success(response) {
                    console.log(region);

                    $scope.selectedRegion = region;
                    $scope.unitDetail = response.data;
                    $scope.unitDetail.forEach(function (data) {
                        $scope.unitIdToUnitNameObj[data.id] = data.name;
                        $scope.unitNameToUnitRegionObj[data.name] = data.region;
                    });
                    if ($scope.selectedRegion != "ALL") {
                        $scope.units = response.data.filter(function (unit) {
                            return unit.category == 'CAFE' && unit.status == 'ACTIVE' && unit.region == $scope.selectedRegion;
                        });
                    }
                    else {
                        $scope.units = response.data.filter(function (unit) {
                            return unit.category == 'CAFE' && unit.status == 'ACTIVE';
                        });
                    }
                    $scope.filterUnitPartnerMappingByPartner();

                    var unitIArray=[];
                    for(var i in $scope.units ){
                        unitIArray.push($scope.units[i].id);
                    }
                    $scope.unitPartnerMappings = $scope.unitPartnerMappings.filter(i => unitIArray.includes(i.unit.id));


                }, function error(response) {
                    console.log("error:" + response);
                });
            }
            $scope.getUserAclData = function(){
                $scope.userAclData= AppUtil.getAcl();
                if(!AppUtil.isEmptyObject($scope.userAclData) && !AppUtil.isEmptyObject($scope.userAclData.action)){
                    if($scope.userAclData.action.ADMN_UPBM_EDIT_LD !==undefined && $scope.userAclData.action.ADMN_UPBM_EDIT_LD !==null  && $scope.userAclData.action.ADMN_UPBM_EDIT_LD === true){
                        $scope.hasFinanceHeadPermissions=true;
                    }
                }
            }

            $scope.getMenus = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getMenusShort,
                    params: {
                        "menu-status": "ACTIVE"
                    }
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.menus = response.data;
                    } else {
                        bootbox.alert("Error getting menu list.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            // $scope.getMenuType = function () {
            //     $rootScope.showFullScreenLoader = true;
            //     $http({
            //         method: 'GET',
            //         url: AppUtil.restUrls.channelPartner.menuType
            //     }).then(function success(response) {
            //         if (response.status === 200 && response.data != null) {
            //             $scope.menuType = response.data;
            //             $rootScope.showFullScreenLoader = false;
            //         } else {
            //             bootbox.alert("Error getting menu type.");
            //         }
            //     }, function error(response) {
            //         console.log("error:" + response);
            //         $rootScope.showFullScreenLoader = false;
            //     });
            // };

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                switch (action) {
                    case "UNIT MAPPING":
                        $scope.initUnitMapping();
                        break;
                    case "UNIT PRICING":
                        $scope.initUnitPricing();
                        break;
                    case "UNIT MENU MAPPING":
                        $scope.initUniMenuMapping();
                        break;
                    case "UNIT RECOMMENDATION MENU MAPPING":
                        $scope.initRecommendationMenuMapping();
                        break;
                    case "UNIT PRICE PROFILE MAPPING":
                        $scope.initPriceProfileMenuMapping();
                        break;
                }
            };

            $scope.initUnitMapping = function () {
                $scope.resetUnitList();
                $scope.resetChannelPartnersList();
                $scope.selectedUnit = null;
                $scope.selectedPartner = null;
                $scope.unitChannelPartnerMappings = [];
                $scope.getUnitChannelPartnerMapping();
            };

            $scope.resetUnitList = function () {
                $scope.unitList.map(function (unit) {
                    unit.selected = false;
                });
            };

            $scope.resetChannelPartnersList = function () {
                $scope.channelPartners.map(function (partner) {
                    partner.selected = false;
                });
            };
            $scope.setSelectLiveDate = function(selectLiveDate){
                $scope.selectLiveDate = selectLiveDate;
            }
            $scope.setSwiggyCloudKitchen = function(swiggyCloudKitchen){
                $scope.swiggyCloudKitchen = swiggyCloudKitchen

            }
            $scope.setSelectEditLiveDate = function(selectEditLiveDate,mappingId){
                $scope.selectEditLiveDate[mappingId] = selectEditLiveDate[mappingId];
            }
            $scope.setEditEnable = function(mappingId,flag){
                if(flag==='cancel'){
                    $scope.selectUpdateLiveDateFlags[mappingId] = null;
                    return;    
                }
                $scope.selectUpdateLiveDateFlags[mappingId] = true;
            }
            $scope.storeSelectedUnits=[];
            $scope.multiSelectSettings = {
                showEnableSearchButton: true, template: '<b> {{option.unit.name}}</b>', scrollable: true,
                scrollableHeight: '250px',trackBy:'unit.id',clearSearchOnClose: true
            };

            $scope.getChannelPartnersList = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getActiveChannelPartners
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.channelPartnerList = response.data;
                        $scope.channelPartners = [];
                        $scope.actionList = ["UNIT MAPPING", "UNIT PRICING", "UNIT MENU MAPPING", "UNIT RECOMMENDATION MENU MAPPING", "UNIT PRICE PROFILE MAPPING"];
                        $scope.channelPartnerList.map(function (partner) {
                            if (partner.status === "ACTIVE") {
                                $scope.channelPartners.push({
                                    id: partner.id,
                                    name: partner.name,
                                    selected: false
                                });
                            }
                        });
                    } else {
                        bootbox.alert("Error loading channel partner list.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                });
            };

            $scope.reloadChannelPartnerCache = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartnerCache.reloadPartnersCache,
                    data: {}
                }).then(function success(response) {
                    if (response.status === 200 && response.data === true) {
                        bootbox.alert("Channel partner cache updated successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("Error in getting response:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addUnitChannelPartnerMapping = function () {
                $rootScope.showFullScreenLoader = true;
                var req = {
                    unit: {id: $scope.selectedUnit.id},
                    channelPartner: {id: $scope.selectedPartner.id},
                    deliveryPartner: {id: 5}
                };
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.addUnitChannelPartnerMapping,
                    data: req
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        $scope.getUnitChannelPartnerMapping();
                    } else {
                        if (response.data.errorMessage) {
                            bootbox.alert(response.data.errorMessage);
                        } else {
                            bootbox.alert("Error adding mapping!");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert(response.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.updateUnitChannelPartnerMapping = function (mapping, activate) {
                $rootScope.showFullScreenLoader = true;
                var url = AppUtil.restUrls.channelPartner.activateUnitChannelPartnerMapping;
                if (activate != true) {
                    url = AppUtil.restUrls.channelPartner.deactivateUnitChannelPartnerMapping;
                }
                $http({
                    method: 'POST',
                    url: url,
                    data: mapping.id
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data != true) {
                        bootbox.alert("Error " + (activate == true ? "activating" : "deactivating") + " mapping!");
                    } else {
                        mapping.status = (activate == true ? "ACTIVE" : "IN_ACTIVE");
                        $scope.reloadUnitChannelPartnerMapping();
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getUnitChannelPartnerMapping = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getAllUnitChannelPartnerMapping
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitChannelPartnerMappings = response.data;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.initUnitPricing = function () {
                $scope.getCODUnitList();
                $scope.getAllBrands();
                $scope.showCodUnitSelection = false;
            };

            $scope.getCODUnitList = function () {
                if ($scope.codUnitList == null || $scope.codUnitList.length == 0) {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.unitMetaData.allUnits + "?category=COD"
                    }).then(function success(response) {
                        $scope.codUnitList = [];
                        response.data.map(function (unit) {
                            if (unit.status === "ACTIVE") {
                                $scope.codUnitList.push(unit);
                            }
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.saveRestaurantId= function(restaurantId){
                console.log("Restaurant Id :", restaurantId);
                $scope.partnerRestaurantId=restaurantId;
            }

            $scope.getAllBrands = function () {
                if ($scope.brands == null || $scope.brands.length == 0) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.brandManagement.getAllBrands
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.brands = response.data;
                            $rootScope.showFullScreenLoader = false;
                        } else {
                            bootbox.alert("Error getting brands.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setSelectedCodUnit = function (selectedCodUnit) {
                $scope.selectedCodUnit = selectedCodUnit;
            };

            $scope.getUnitPartnerBrandMapping = function (event) {
                $scope.unitPartnerBrandMappings = [];
                $scope.event=event;
                $scope.event.search=null;
                var reqObj = {
                    userId: 0,
                    partnerId: $scope.selectedPartner.id,
                    brandId: $scope.selectedBrand.brandId,
                };
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.brandManagement.getAllUnitPartnerBrandMapping,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.selectAll = false;
                        var newMappingData = response.data;
                        newMappingData.forEach(function (unit) {
                            unit.brand = $scope.selectedBrand;
                            unit.partner = $scope.selectedPartner;
                            unit.unit = $scope.unitList.find(function (mappings) {
                                return mappings.id == unit.unitId;
                            })
                            unit.isChecked = $scope.selectAll;
                            $scope.codUnitList.map(function (cod) {
                                if (unit.priceProfileUnitId == cod.id) {
                                    unit.pricingUnit = cod;
                                }
                            });
                        });
                        $scope.unitPartnerBrandMappings = [...newMappingData];
                    } else {
                        bootbox.alert("Error getting unit partner brand mapping.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setShowCodUnitSelection = function (show) {
                $scope.showCodUnitSelection = show;
            };

            $scope.getBrandCode = function () {
                if ($scope.selectedBrand.brandId == AppUtil.CHAAYOS_BRAND_ID) {
                    return "";
                } else {
                    return $scope.selectedBrand.brandCode;
                }
            };

            $scope.addUnitPartnerBrandMapping = function (mapping) {
                var reqObj;
                if(mapping){
                    reqObj = {
                    unitId: mapping.unitId,
                    partnerId: mapping.partnerId,
                    brandId: mapping.brandId,
                    priceProfileUnitId: mapping.priceProfileUnitId,
                    restaurantId: mapping.restaurantId,
                    partnerSourceSystemId:mapping.partnerSourceSystemId,
                    swiggyCloudKitchen : mapping.swiggyCloudKitchen,
                    liveDate: $scope.selectEditLiveDate[mapping.mappingId],
                    status: mapping.status
                    }
                }else{
                    var brandCode = $scope.getBrandCode();
                     reqObj = {
                        unitId: $scope.selectedUnit.id,
                        partnerId: $scope.selectedPartner.id,
                        brandId: $scope.selectedBrand.brandId,
                        priceProfileUnitId: $scope.selectedCodUnit.id,
                        restaurantId: $scope.selectedUnit.id + brandCode,
                        partnerSourceSystemId:$scope.partnerRestaurantId,
                        swiggyCloudKitchen : $scope.swiggyCloudKitchen==false ? 'N' : 'Y',
                        liveDate: $scope.selectLiveDate,
                        status: "ACTIVE",
                    };
                }
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.brandManagement.addUnitPartnerBrandMapping,
                    params:{editLiveDate:mapping!=null ? "Y":"N"},
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitPartnerBrandMapping = response.data;
                        $scope.unitPartnerBrandMapping.brand = $scope.selectedBrand;
                        $scope.unitPartnerBrandMapping.partner = $scope.selectedPartner;
                        $scope.unitPartnerBrandMapping.unit = $scope.selectedUnit;
                        $scope.unitPartnerBrandMapping.pricingUnit = $scope.selectedCodUnit;
                        $scope.setShowCodUnitSelection(false);
                       if(mapping){
                        $rootScope.selectEditLiveDateFlags[response.data.mappingId] = true;}
                    } else {
                        bootbox.alert("Error getting unit partner brand mapping.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };
            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                if ($scope.codUnitList != null) {
                    var codList = [];
                    $scope.codUnitList.map(function (unit) {
                        if (unit.category == "COD") {
                            codList.push(unit);
                        }
                    });
                    codList.push(selectedUnit);
                    $scope.codUnitList = codList;
                }
            };

            $scope.setSelectedBrand = function (selectedBrand) {
                $scope.selectedBrand = selectedBrand;
            };

            $scope.setSelectedPartner = function (selectedPartner) {
                console.log("in partner unit")
                console.log(selectedPartner)
                if (selectedPartner.name == "ZOMATO" || selectedPartner.id==1) {
                    $scope.menuType = ["DEFAULT", "SINGLE_SERVE", "DAY_SLOT_BREAKFAST", "DAY_SLOT_LUNCH"
                        , "DAY_SLOT_EVENING", "DAY_SLOT_DINNER", "DAY_SLOT_POST_DINNER", "DAY_SLOT_OVERNIGHT","DAY_SLOT_ALL"];
                    $scope.newMenuMapping = {menuType: $scope.menuType};
                } else if (selectedPartner.name == "DINE IN APP" ) {
                    $scope.menuType = ["DEFAULT","DAY_SLOT", "DAY_SLOT_BREAKFAST", "DAY_SLOT_LUNCH", "DAY_SLOT_EVENING", "DAY_SLOT_DINNER", "DAY_SLOT_POST_DINNER",
                        "DAY_SLOT_OVERNIGHT","DAY_SLOT_ALL"];
                    $scope.newMenuMapping = {menuType: $scope.menuType};
                } else if (selectedPartner.name == "SWIGGY") {
                    $scope.menuType = ["DEFAULT", "DAY_SLOT_BREAKFAST", "DAY_SLOT_LUNCH", "DAY_SLOT_EVENING", "DAY_SLOT_DINNER", "DAY_SLOT_POST_DINNER",
                        "DAY_SLOT_OVERNIGHT"];
                    $scope.newMenuMapping = {menuType: $scope.menuType};
                } else {
                    $scope.menuType = ["DEFAULT"];
                    $scope.onSelectingMenuType($scope.menuType[0]);
                    $scope.newMenuMapping = {menuType: $scope.menuType[0]};
                }
                $scope.selectedPartner = selectedPartner;
            };

            $scope.updateUnitPartnerBrandMapping = function (mapping, activate) {
                $rootScope.showFullScreenLoader = true;
                var url = AppUtil.restUrls.brandManagement.activateUnitPartnerBrandMapping;
                if (activate == false) {
                    url = AppUtil.restUrls.brandManagement.deactivateUnitPartnerBrandMapping;
                }
                $http({
                    method: 'POST',
                    url: url,
                    data: mapping.mappingId
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.unitPartnerBrandMapping = response.data;
                        $scope.unitPartnerBrandMapping.brand = $scope.selectedBrand;
                        $scope.unitPartnerBrandMapping.partner = $scope.selectedPartner;
                        $scope.unitPartnerBrandMapping.unit = $scope.selectedUnit;
                        $scope.unitPartnerBrandMapping.pricingUnit = $scope.selectedCodUnit;
                        $scope.getUnitPartnerBrandMapping($scope.event);
                        $scope.setShowCodUnitSelection(false);
                    } else {
                        bootbox.alert("Error getting unit partner brand mapping.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.initUniMenuMapping = function () {
                $scope.initManageMenuMapping();
            };

            $scope.initManageMenuMapping = function () {
                //$scope.resetUnitList();
                $scope.menuMapping = true;
                $scope.menuMappings = [];
                $scope.unitMenuMappings = [];
                $scope.recommendationmapping = false;
                $scope.selectedBrand = {};
                $scope.selectedPartner = {};
                $scope.selectedMenuType = '';
                $scope.selectedMenu = {};
                $scope.selectedApp = {};
                $scope.regions = [];
                if (!$scope.channelPartners) {
                    $scope.getChannelPartnersList();
                }
                if (!$scope.brands) {
                    $scope.getAllBrands();
                }
            };

            $scope.initRecommendationMenuMapping = function () {
                //$scope.resetUnitList();
                $scope.recommendationMapping = true;
                $scope.menuMapping = false;
                $scope.menuMappings = [];
                $scope.regions = [];
                $scope.selectedRecommendation = {};
                $scope.selectedMenuSequence = {};
                $scope.selectedMenuType = '';
                $scope.selectedRegion = null;
                initCheckBoxModal();
                $scope.unitDetails = {};
                $scope.selectedBrand = {};
                $scope.selectedPartner = {};
                $scope.selectedApp = {};
                $scope.getRecommendations();
                if (!$scope.channelPartners) {
                    $scope.getChannelPartnersList();
                }
                if (!$scope.brands) {
                    $scope.getAllBrands();
                }
            };

            $scope.initPriceProfileMenuMapping = function () {
                //$scope.resetUnitList();
                $scope.recommendationMapping = true;
                $scope.menuMapping = false;
                $scope.menuMappings = [];
                $scope.regions = [];
                $scope.selectedPriceProfile = {};
                $scope.selectedMenuSequence = {};
                $scope.selectedMenuType = '';
                $scope.selectedRegion = null;
                initCheckBoxModal();
                $scope.unitDetails = {};
                $scope.selectedBrand = {};
                $scope.selectedPartner = {};
                $scope.selectedApp = {};
                $scope.getPriceProfile();
                if (!$scope.channelPartners) {
                    $scope.getChannelPartnersList();
                }
                if (!$scope.brands) {
                    $scope.getAllBrands();
                }
            };

            $scope.setSelectedApp = function (selectedApp) {
                $scope.selectedApp = selectedApp;
            };

            $scope.getUnitMenuMapping = function (optedMenuType, selectedMenu , event) {
                $scope.selectedMenuType = optedMenuType;
                $scope.selectedMenu = selectedMenu == "ALL" ? selectedMenu :
                    typeof selectedMenu === 'string' ? JSON.parse(selectedMenu) : selectedMenu;
                $scope.regions = []
                $scope.unitMenuMappings = []
                $scope.getMenuMappings();
                $scope.event=event;
                $scope.event.search=null;
                getRegionDetails();
            };

            $scope.getMenuDetails = function (region) {
                $scope.unitMenuMappings = [];
                if (region == 'ALL') {
                    if ($scope.selectedMenu != 'ALL') {
                        for (var i in $scope.menuMappings) {
                            if ($scope.menuMappings[i].menuSequence.id == $scope.selectedMenu.menuSequenceId) {
                                $scope.unitMenuMappings.push($scope.menuMappings[i]);
                            }
                        }
                    } else {
                        $scope.unitMenuMappings = $scope.menuMappings
                    }
                } else {
                    for (var i in $scope.menuMappings) {
                        if ($scope.selectedMenu != 'ALL') {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].menuSequence.id == $scope.selectedMenu.menuSequenceId) {
                                $scope.unitMenuMappings.push($scope.menuMappings[i]);
                            }
                        } else {
                            if ($scope.menuMappings[i].unit.code == region) {
                                $scope.unitMenuMappings.push($scope.menuMappings[i]);
                            }
                        }
                    }
                }
                console.log($scope.unitMenuMappings);
                if ($scope.unitMenuMappings.length == 0) {
                    alert("No Data found for required criteria");
                }
            };

            $scope.getMenuMappings = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.getUnitPartnerMenuMapping,
                    data: {
                        partnerId: $scope.selectedPartner.id,
                        brandId: $scope.selectedBrand.brandId
                    }
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.allMenuMappings = response.data;
                        $scope.selectAll = false;
                        if ($scope.allMenuMappings.length > 0) {
                            $scope.filterMenuMappingByApp();
                        } else {
                            bootbox.alert("No mappings found.");
                        }
                    } else {
                        bootbox.alert("Error getting menu mappings.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addMenuMappings = function () {
                // $scope.newMenuMapping = {menuType:"DEFAULT"};
                $scope.storeSelectedUnits=[];
                $scope.selectedMenu=null;
                $scope.newMenuMapping.menuType=null;
                if ($scope.allUnitPartnerMappings == null) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.channelPartner.getAllUnitChannelPartnerMapping,
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.allUnitPartnerMappings = response.data;
                            $scope.filterUnitPartnerMappingByPartner();
                            $("#addMenuMappingModal").modal("show");
                        } else {
                            bootbox.alert("Error getting menu mappings.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    $scope.filterUnitPartnerMappingByPartner();
                    $("#addMenuMappingModal").modal("show");
                }
            };

            $scope.filterMenuMappingByApp = function () {
                $scope.menuMappings = [];
                if ($scope.allMenuMappings != null) {
                    $scope.allMenuMappings.map(function (mapping) {
                        if (mapping.menuApp == $scope.selectedApp.name) {
                            $scope.menus.map(function (menu) {
                                if (mapping.menuSequence.id == menu.menuSequenceId) {
                                    mapping.menuSequence.name = menu.menuSequenceName;
                                }
                            });
                            $scope.menuMappings.push(mapping);
                        }
                    });
                }
            };

            $scope.filterUnitPartnerMappingByPartner = function () {
                $scope.unitPartnerMappings = [];
                if ($scope.allUnitPartnerMappings != null) {
                    $scope.allUnitPartnerMappings.map(function (mapping) {
                        if (mapping.channelPartner.id == $scope.selectedPartner.id && mapping.status == "ACTIVE") {
                            $scope.unitPartnerMappings.push(mapping);
                        }
                    });
                }
            };

            $scope.setSelectedMenu = function (selectedMenu) {
                $scope.selectedMenu = selectedMenu;
            };

            $scope.refreshUnitMenuMappingCache = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.masterCacheManagement.reloadUnitMenuSequenceMapping,
                    data: {}
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        bootbox.alert("Cache refreshed successfully.");
                    } else {
                        bootbox.alert("Error refreshing cache. Try again.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.activateMenuMapping = function (mapping, activate) {

                if(activate=="ACTIVE"){
                    bootbox.confirm({
                        title: "Are you sure?",
                        size:'large',

                        message: "Previous mappings will get de-activated",
                        buttons: {
                            cancel: {
                                label: '<i class="fa fa-times"></i> Cancel'
                            },
                            confirm: {
                                label: '<i class="fa fa-check"></i> Confirm'
                            }
                        },
                        callback: function (result) {
                            if(result == true) {
                                $scope.activateMenuMappingHelper(mapping,activate);
                            }
                        }
                    });
                }else{
                    $scope.activateMenuMappingHelper(mapping,activate);
                }

            };

            $scope.activateMenuMappingHelper=function(mapping,activate){
                $rootScope.showFullScreenLoader = true;
                var url = AppUtil.restUrls.channelPartner.activateUnitPartnerMenuMapping;
                if (activate != "ACTIVE"){
                    url = AppUtil.restUrls.channelPartner.deactivateUnitPartnerMenuMapping;
                }
                $http({
                    method: 'POST',
                    url: url,
                    data: mapping.id
                }).then(function success(response) {
                    if (response.status === 200 && response.data == true) {
                        mapping.status = activate;
                        bootbox.alert("Menu mapping updated successfully.");
                    } else {
                        bootbox.alert("Error updating menu mapping.");
                    }
                    $scope.getUnitMenuMapping($scope.selectedMenuType,$scope.selectedMenu,$scope.event);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.onSelectingMenuType = function (menuType) {
                $scope.filteredMenu = [];
                for (var i in $scope.menus) {
                    if ($scope.menus[i].menuType == menuType || $scope.menus[i].menuType == "DAY_SLOT_ALL" ) {
                        $scope.filteredMenu.push($scope.menus[i]);
                    }
                }
            };

            $scope.addNewMenuMapping = function () {
                var finalMenuMappings = [];
                for (var i = 0; i < $scope.storeSelectedUnits.length; i++) {
                    var duplicateOfMenuMapping = [];
                    $scope.newMenuMapping.createdBy = {id: AppUtil.getCurrentUser().id};
                    $scope.newMenuMapping.updatedBy = {id: AppUtil.getCurrentUser().id};
                    $scope.newMenuMapping.unit = {id: $scope.storeSelectedUnits[i].unit.id};
                    $scope.newMenuMapping.channelPartner = $scope.selectedPartner;
                    $scope.newMenuMapping.brand = {id: $scope.selectedBrand.brandId};
                    $scope.newMenuMapping.menuApp = $scope.selectedApp.name;
                    $scope.newMenuMapping.unitChannelPartnerMappingId = $scope.storeSelectedUnits[i].id;
                    $scope.newMenuMapping.menuSequence = {id: $scope.selectedMenu.menuSequenceId};
                    $scope.newMenuMapping.status = "ACTIVE";
                    duplicateOfMenuMapping = angular.copy($scope.newMenuMapping);
                    finalMenuMappings.push(duplicateOfMenuMapping);
                }

                bootbox.confirm({
                    title: "Are you sure?",
                    size:'large',

                    message: "Requested mappings will be marked as active If already active then it will be marked as Inactive",
                    buttons: {
                        cancel: {
                            label: '<i class="fa fa-times"></i> Cancel'
                        },
                        confirm: {
                            label: '<i class="fa fa-check"></i> Confirm'
                        }
                    },
                    callback: function (result) {
                        if(result == true) {
                            $rootScope.showFullScreenLoader = true;
                            $http({
                                method: 'POST',
                                url: AppUtil.restUrls.channelPartner.addUnitPartnerMenuMappingBulk,
                                data: finalMenuMappings
                            }).then(function success(response) {
                                if (response.status === 200 && response.data != null) {
                                    //list needs to be pushed
                                    // for (var i = 0; i < response.data.length; i++) {
                                    //     $scope.allUnitPartnerMappings.push(response.data[i]);
                                    // }
                                    $scope.filterUnitPartnerMappingByPartner();
                                    $("#addMenuMappingModal").modal("hide");
                                    bootbox.alert("Menu mapping added successfully.");
                                } else if (response.data.errorMessage != null) {
                                    bootbox.alert(response.data.errorMessage);
                                } else {
                                    bootbox.alert("Error adding menu mapping.");
                                }
                                $rootScope.showFullScreenLoader = false;
                            }, function error(response) {
                                console.log("error:" + response);
                                $rootScope.showFullScreenLoader = false;
                            });
                        }
                    }
                });
            };

            $scope.getAllRegionsDetails= function (){
                getRegionDetails();
            };

            function getRegionDetails() {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.regions
                }).then(function success(response) {
                    $scope.regions = response.data;
                    // $scope.regions.push('ALL');
                    $scope.unitMenuMappingRegions = response.data;
                    $scope.unitMenuMappingRegions.push('ALL');
                    console.log($scope.unitMenuMappingRegions);
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getRecommendations = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getRecommendation
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.recommendations = response.data;
                    } else {
                        bootbox.alert("Error getting recommendation list.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getPriceProfile = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.channelPartner.getPriceAllProfile
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.priceProfile = response.data;
                    } else {
                        bootbox.alert("Error getting price profile list.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.onSelectingDetalis = function (selectedMenuType, selectedMenuSequence, selectedRecommendation) {
                $scope.selectedRecommendation = selectedRecommendation == "ALL" ? selectedRecommendation : JSON.parse(selectedRecommendation);
                $scope.selectedMenuSequence = selectedMenuSequence == "ALL" ? selectedMenuSequence : JSON.parse(selectedMenuSequence);
                $scope.selectedMenuType = selectedMenuType;
                $scope.regions = []
                $scope.menuRecommendationMappings = []
                console.log($scope.selectedPartner);
                console.log($scope.selectedApp);
                console.log($scope.selectedBrand);
                console.log($scope.selectedRecommendation);
                console.log($scope.selectedMenuSequence);
                console.log($scope.selectedMenuType);
                $scope.getMenuMappings();
                getRegionDetails();

            };

            $scope.onSelectingPriceProfileDetails = function (selectedMenuType, selectedMenuSequence, selectedPriceProfile) {
                $scope.selectedPriceProfile = selectedPriceProfile == "ALL" ? selectedPriceProfile : JSON.parse(selectedPriceProfile);
                $scope.selectedMenuSequence = selectedMenuSequence == "ALL" ? selectedMenuSequence : JSON.parse(selectedMenuSequence);
                $scope.selectedMenuType = selectedMenuType;
                $scope.regions = []
                $scope.menuRecommendationMappings = []
                console.log($scope.selectedPartner);
                console.log($scope.selectedApp);
                console.log($scope.selectedBrand);
                console.log($scope.selectedPriceProfile);
                console.log($scope.selectedMenuSequence);
                console.log($scope.selectedMenuType);
                $scope.getMenuMappings();
                getRegionDetails();
            };

            $scope.getDetails = function (region) {
                $scope.menuFilter = $scope.selectedMenuSequence == "ALL";
                $scope.recommendationFilter = $scope.selectedRecommendation == "ALL";
                $scope.menuRecommendationMappings = [];
                if (region == 'ALL') {
                    for (i in $scope.menuMappings) {
                        if (!$scope.menuFilter && !$scope.recommendationFilter) {
                            if ($scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId
                                && $scope.menuMappings[i].menuRecommendationSequenceId == $scope.selectedRecommendation.menuRecommendationId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if ($scope.menuFilter && !$scope.recommendationFilter) {
                            if ($scope.menuMappings[i].menuRecommendationSequenceId == $scope.selectedRecommendation.menuRecommendationId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if (!$scope.menuFilter && $scope.recommendationFilter) {
                            if ($scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else {
                            $scope.menuRecommendationMappings = $scope.menuMappings
                        }
                    }

                }

                else {
                    for (i in $scope.menuMappings) {
                        if (!$scope.menuFilter && !$scope.recommendationFilter) {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId
                                && $scope.menuMappings[i].menuRecommendationSequenceId == $scope.selectedRecommendation.menuRecommendationId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if ($scope.menuFilter && !$scope.recommendationFilter) {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].menuRecommendationSequenceId == $scope.selectedRecommendation.menuRecommendationId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if (!$scope.menuFilter && $scope.recommendationFilter) {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else {
                            if ($scope.menuMappings[i].unit.code == region) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        }
                    }
                }
                for (var i in $scope.menuRecommendationMappings) {
                    $scope.recommendations.forEach(function (recommendation) {
                        if ($scope.menuRecommendationMappings[i].menuRecommendationSequenceId != null && $scope.menuRecommendationMappings[i].menuRecommendationSequenceId == recommendation.menuRecommendationId) {
                            $scope.menuRecommendationMappings[i].recommendation = recommendation.menuRecommendationName;
                        }
                    })
                }
                $scope.unitDetails = {};
                $scope.selectedRegion = region;
                for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id] = {};
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = false;
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].recommendation = '';
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].id = $scope.menuRecommendationMappings[i].id;
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].unit = $scope.menuRecommendationMappings[i].unit;
                }
                console.log($scope.menuRecommendationMappings)
                if ($scope.menuRecommendationMappings.length == 0) {
                    alert("No Data found for required criteria");
                }
            };

            $scope.getPriceProfileDetails = function (region) {
                $scope.menuFilter = $scope.selectedMenuSequence == "ALL";
                $scope.priceProfileFilter = $scope.selectedPriceProfile == "ALL";
                $scope.menuRecommendationMappings = [];
                if (region == 'ALL') {
                    for (i in $scope.menuMappings) {
                        if (!$scope.menuFilter && !$scope.priceProfileFilter) {
                            if ($scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId
                                && $scope.menuMappings[i].priceProfileId == $scope.selectedPriceProfile.priceProfileId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if ($scope.menuFilter && !$scope.priceProfileFilter) {
                            if ($scope.menuMappings[i].priceProfileId == $scope.selectedPriceProfile.priceProfileId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if (!$scope.menuFilter && $scope.priceProfileFilter) {
                            if ($scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else {
                            $scope.menuRecommendationMappings = $scope.menuMappings
                        }
                    }

                }

                else {
                    for (i in $scope.menuMappings) {
                        if (!$scope.menuFilter && !$scope.priceProfileFilter) {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId
                                && $scope.menuMappings[i].priceProfileId == $scope.selectedPriceProfile.priceProfileId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if ($scope.menuFilter && !$scope.priceProfileFilter) {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].priceProfileId == $scope.selectedPriceProfile.priceProfileId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else if (!$scope.menuFilter && $scope.priceProfileFilter) {
                            if ($scope.menuMappings[i].unit.code == region && $scope.menuMappings[i].menuSequence.id == $scope.selectedMenuSequence.menuSequenceId) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        } else {
                            if ($scope.menuMappings[i].unit.code == region) {
                                $scope.menuRecommendationMappings.push($scope.menuMappings[i]);
                            }
                        }
                    }
                }
                for (var i in $scope.menuRecommendationMappings) {
                    $scope.priceProfile.forEach(function (priceProfile) {
                        if ($scope.menuRecommendationMappings[i].priceProfileId != null && $scope.menuRecommendationMappings[i].priceProfileId == priceProfile.priceProfileId) {
                            $scope.menuRecommendationMappings[i].priceProfile = priceProfile.priceProfileId;
                            $scope.menuRecommendationMappings[i].profileDescription = priceProfile.profileDescription;
                        }
                    })
                }
                $scope.unitDetails = {};
                $scope.selectedRegion = region;
                for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id] = {};
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = false;
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].priceProfile = '';
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].id = $scope.menuRecommendationMappings[i].id;
                    $scope.unitDetails[$scope.menuRecommendationMappings[i].id].unit = $scope.menuRecommendationMappings[i].unit;
                }
                console.log($scope.menuRecommendationMappings);
                if ($scope.menuRecommendationMappings.length == 0) {
                    alert("No Data found for required criteria");
                }
            };

            $scope.updateAll = function () {
                if ($scope.checkBoxModal.checkAll === true) {
                    for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                        $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = true;
                    }
                } else if ($scope.checkBoxModal.checkAll === false) {
                    for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                        $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = false;
                    }
                }
            };

            function initCheckBoxModal() {
                $scope.checkBoxModal = {};
                $scope.checkBoxModal.checkAll = false;
                $scope.checkBoxModal.checkAllRecommendation = false;
                $scope.checkBoxModal.updatedRecommendation = '';
                $scope.menuMappings = {};
            }

            $scope.changeAllRecommendation = function () {
                console.log($scope.checkBoxModal.updatedRecommendation)
                if ($scope.checkBoxModal.checkAllRecommendation === true) {
                    if ($scope.checkBoxModal.updatedRecommendation == '') {
                        alert("Please enter new Recommendation.");
                        $scope.checkBoxModal.checkAllRecommendation = false;
                        return false;
                    } else {
                        for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                            $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = true;
                            $scope.unitDetails[$scope.menuRecommendationMappings[i].id].recommendation = $scope.checkBoxModal.updatedRecommendation;
                        }
                    }
                } else if ($scope.checkBoxModal.checkAllRecommendation === false) {
                    for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                        $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = $scope.checkBoxModal.checkAll;
                        $scope.checkBoxModal.updatedRecommendation = '';
                        $scope.unitDetails[$scope.menuRecommendationMappings[i].id].recommendation = $scope.checkBoxModal.updatedRecommendation;
                    }
                }
            };

            $scope.changeAllPriceProfile = function () {
                console.log($scope.checkBoxModal.updatedRecommendation)
                if ($scope.checkBoxModal.checkAllRecommendation === true) {
                    if ($scope.checkBoxModal.updatedRecommendation == '') {
                        alert("Please enter new Recommendation.");
                        $scope.checkBoxModal.checkAllRecommendation = false;
                        return false;
                    } else {
                        for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                            $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = true;
                            $scope.unitDetails[$scope.menuRecommendationMappings[i].id].priceProfile = $scope.checkBoxModal.updatedRecommendation;
                        }
                    }
                } else if ($scope.checkBoxModal.checkAllRecommendation === false) {
                    for (var i = 0; i < $scope.menuRecommendationMappings.length; i++) {
                        $scope.unitDetails[$scope.menuRecommendationMappings[i].id].checked = $scope.checkBoxModal.checkAll;
                        $scope.checkBoxModal.updatedRecommendation = '';
                        $scope.unitDetails[$scope.menuRecommendationMappings[i].id].priceProfile = $scope.checkBoxModal.updatedRecommendation;
                    }
                }
            };

            $scope.submitRecommendationMapping = function () {
                var payload = [];
                for (var i in $scope.unitDetails)
                    if ($scope.unitDetails[i].checked) {
                        payload.push({
                            id: $scope.unitDetails[i].id,
                            index: $scope.unitDetails[i].recommendation == "Cancel Recommendation"?"":$scope.unitDetails[i].recommendation
                        })
                    }
                console.log(payload);
                if (payload.length == 0) {
                    alert("please check atleast one unit");
                    return;
                }
                var result = confirm("Are You Sure You Want To Submit");
                if (!result) {
                    return;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.recommendationUnitChannelPartnerMapping,
                    data: payload
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        alert("Recommendation Updated  successfully!");
                        $rootScope.showFullScreenLoader = false;
                        window.location.reload();
                        // $scope.clear();
                    } else {
                        bootbox.alert("Error adding recommendation.");
                        $rootScope.showFullScreenLoader = false;
                    }

                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.submitPriceProfileMapping = function () {
                var payload = [];
                for (var i in $scope.unitDetails)
                    if ($scope.unitDetails[i].checked) {
                        payload.push({
                            id: $scope.unitDetails[i].id,
                            index: $scope.unitDetails[i].priceProfile
                        })
                    }
                console.log(payload);
                if (payload.length == 0) {
                    alert("please check atleast one unit");
                    return;
                }
                var result = confirm("Are You Sure You Want To Submit");
                if (!result) {
                    return;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.priceProfileUnitChannelPartnerMapping,
                    data: payload
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        alert("Price profile Updated  successfully!");
                        $rootScope.showFullScreenLoader = false;
                        window.location.reload();
                        // $scope.clear();
                    } else {
                        bootbox.alert("Error adding price profile.");
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };


            $scope.filterRecommendationStatus = function (recommendation) {
                return recommendation.status == 'ACTIVE';

            };


            $scope.filterPriceProfileStatus = function (priceProfile) {
                return priceProfile.profileStatus == 'ACTIVE';
            };


            // $scope.clear = function () {
            //     $scope.menuRecommendationMappings = [];
            //     $scope.regions = null;
            //     $scope.initRecommendationMenuMapping();
            //
            // }

            $scope.setMenuTypeFilter = function (details) {
                if ($scope.selectedMenuType == "ALL") {
                    return details;
                } else {
                    return details.menuType == $scope.selectedMenuType;
                }
            };

            $scope.refreshUnitPartnerBrandMappingCache = function () {
                var url = AppUtil.restUrls.masterCacheManagement['refreshPartnerBrandMappingData'];
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'get',
                    url: url
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert(JSON.stringify(response));
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("error:" + JSON.stringify(response));
                });
            }

            $scope.setMappingActions = function (value) {
                console.log("values is", value);
                $scope.allActions = value;
            }

            $scope.selectAllMapping = function () {
                $scope.selectAll = !$scope.selectAll;
                $scope.unitPartnerBrandMappings.forEach(function (mapping) {
                    console.log(mapping.isChecked);
                    $scope.selectMapping(mapping);
                });
            }

            $scope.selectAllMenuMapping = function () {
                $scope.selectAll = !$scope.selectAll;
                console.log("select all is :"+$scope.selectAll);
                $scope.unitMenuMappings.forEach(function (mapping) {
                    console.log(mapping.isChecked);
                    $scope.selectMenuMapping(mapping);
                });
            };

            $scope.selectMapping = function (mapping) {
                var index = $scope.unitPartnerBrandMappings.indexOf(mapping);
                if (index > -1) {
                    $scope.unitPartnerBrandMappings[index].isChecked = $scope.selectAll;
                }
            }

            $scope.selectMenuMapping = function (mapping) {
                var index = $scope.unitMenuMappings.indexOf(mapping);
                if (index > -1) {
                    $scope.unitMenuMappings[index].isChecked = $scope.selectAll;
                }
            }

            $scope.bulkUpdateMappingStatus = function () {
                var updationList = $scope.unitPartnerBrandMappings.filter(function (mapping) {
                    return mapping.isChecked == true;
                });

                var updationIdList = updationList.map(function (mapping) {
                    console.log(mapping);
                    return mapping.mappingId;
                })
                var reqObj = {
                    mappingIds: updationIdList,
                    status: $scope.allActions
                };
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.brandManagement.toggleUnitChannelPartnerMapping,
                    data: reqObj,
                }).then(function success(response) {
                    if (response.status === 200) {
                        $scope.getUnitPartnerBrandMapping($scope.event);
                    } else {
                        bootbox.alert("Error updating unit partner brand mapping.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });

            };

            $scope.bulkUpdateMenuMappingStatus = function () {
                var isDuplicatedUnitMapping=false;
                var updationList = $scope.unitMenuMappings.filter(function (mapping) {
                    return mapping.isChecked == true;
                });

                var updationIdList = updationList.map(function (mapping) {
                    console.log(mapping);
                    return mapping.id;
                });

                var reqObj = {
                    mappingIds: updationIdList,
                    status: $scope.allActions
                };
                for(var i in updationList){
                    var firstMapping=updationList[i];
                    if(isDuplicatedUnitMapping){
                        break;
                    }
                    for(var j in updationList){
                        var secondMapping=updationList[j];
                        if(firstMapping["unitChannelPartnerMappingId"]==secondMapping["unitChannelPartnerMappingId"] &&
                            firstMapping["brand"]["id"]==secondMapping["brand"]["id"] &&
                            firstMapping["channelPartner"]["id"]==secondMapping["channelPartner"]["id"] &&
                            firstMapping["menuType"]==secondMapping["menuType"] && i!=j){
                            isDuplicatedUnitMapping=true;
                            break;
                        }
                    }
                }
                if(isDuplicatedUnitMapping){
                    bootbox.alert("Cannot activate multiple units with same unit and day part! Please Check");
                }else{
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.channelPartner.toggleUnitChannelPartnerMenuMapping,
                        data: reqObj,
                    }).then(function success(response) {
                        if (response.status === 200) {
                            $scope.getUnitMenuMapping($scope.selectedMenuType,$scope.selectedMenu , $scope.event);
                            $scope.allActions = null;
                            $scope.getUnitMenuMapping($scope.selectedMenuType,$scope.selectedMenu,$scope.event);
                            bootbox.alert("Updated Successfuly.");
                        } else {
                            bootbox.alert("Error updating unit partner menu mapping.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }

            }

        }]);
