/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('desiChaiCustomProfilesCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http',
        function ($location, $scope, AppUtil, $rootScope, $http) {

            $scope.init = function () {
                $scope.actionList = ["DESI CHAI PROFILES", "UNIT PROFILE MAPPING"];
                $scope.selectedAction = "DESI CHAI PROFILES";
                $scope.selectAction($scope.selectedAction);
            };

            $scope.selectAction = function (action) {
                $scope.selectedAction = action;
                switch (action) {
                    case "DESI CHAI PROFILES":
                        $scope.initDesiChaiProfiles();
                        break;
                    case "UNIT PROFILE MAPPING":
                        $scope.initDesiChaiProfileMapping();
                        break;
                }
            };

            $scope.initDesiChaiProfiles = function () {
                $scope.desiChaiRecipe = null;
                $scope.desiChaiProfiles = null;
                $scope.getDesiChaiCustomProfiles();
            };

            $scope.initDesiChaiProfileMapping = function () {
                $scope.channelPartners = [];
                $scope.unitMap = {};
                $scope.partnerMap = {};
                $scope.brandMap = {};
                $scope.getUnitList();
                $scope.selectAllUnits = false;
                //$scope.resetUnitList();
                $scope.getChannelPartnersList();
                $scope.getAllBrands();
                $scope.selectedPartner = null;
                $scope.selectedBrand = null;
            };

            $scope.setSelectAllUnits = function () {
                $scope.selectAllUnits = !$scope.selectAllUnits;
                $scope.unitList.map(function (unit) {
                    unit.selected = $scope.selectAllUnits;
                });
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                    $scope.resetUnitList()
                });
            };

            $scope.resetUnitList = function () {
                $scope.unitList.map(function (unit) {
                    unit = {id: unit.id, name: unit.name, selected: false};
                    $scope.unitMap[unit.id] = unit;
                });
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
            };

            $scope.getDesiChaiCustomProfiles = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerMetadata.getDcCustomProfiles,
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.desiChaiProfiles = response.data;
                    } else {
                        if(response.data.errorMsg != null) {
                            bootbox.alert(response.data.errorMsg);
                        } else {
                            bootbox.alert("Error getting desi chai profiles");
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addDesiChaiProfile = function () {
                if ($scope.desiChaiRecipe == null) {
                    $scope.getDesiChaiRecipe();
                }
                $scope.newProfile = {
                    profileName: null,
                    milkType: null,
                    dimensionType: null,
                    sugarType: null,
                    pattiType: null,
                    addons: null,
                    productName: null,
                    profileType: null,
                };
                $("#desiChaiProfileModal").modal("show");
            };

            $scope.getDesiChaiRecipe = function () {
                $rootScope.detailLoaderMessage = "Fetching desi chai customizations...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.unitProducts,
                    data: 10000
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        response.data.products.map(function (product) {
                            if (product.id === 10) {
                                $scope.addonMap = {};
                                $scope.desiChaiRecipe = product.prices[0].recipe;
                                $scope.desiChaiRecipe.addons.map(function (addon) {
                                    $scope.addonMap[addon.product.productId] = addon.product;
                                })

                            }
                        });
                        $rootScope.showDetailLoader = false;
                    } else {
                        bootbox.alert("Error getting desi chai customizations");
                        $rootScope.showDetailLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.saveDesiChaiProfile = function () {
                if ($scope.newProfile.profileType == null) {
                    bootbox.alert("Please select profile type.");
                    return;
                } else if ($scope.newProfile.profileName == null) {
                    bootbox.alert("Please add profile name.");
                    return;
                } else {
                    if ($scope.newProfile.profileType === "PRODUCT") {
                        if ($scope.newProfile.productName == null) {
                            bootbox.alert("Please fill product name");
                            return;
                        }
                        if ($scope.newProfile.milkType == null && $scope.newProfile.dimensionType == null && $scope.newProfile.sugarType == null &&
                            $scope.newProfile.pattiType == null && $scope.newProfile.addons == null) {
                            bootbox.alert("Please fill at least one from Dimension Type, Milk Type, Sugar Type, Patti Type, Addons");
                            return;
                        }
                    }
                    if ($scope.newProfile.profileType === "CUSTOMIZATION") {
                        if ($scope.newProfile.milkType == null && $scope.newProfile.dimensionType == null && $scope.newProfile.sugarType == null &&
                            $scope.newProfile.pattiType == null && $scope.newProfile.addons == null) {
                            bootbox.alert("Please fill at least one from Dimension Type, Milk Type, Sugar Type, Patti Type, Addons");
                            return;
                        }
                    }
                }

                var reqObj = angular.copy($scope.newProfile);
                reqObj.addons = [];
                if ($scope.newProfile.addons != null) {
                    Object.keys($scope.newProfile.addons).map(function (addon) {
                        if ($scope.newProfile.addons[addon] === true) {
                            reqObj.addons.push({id:addon, name:$scope.addonMap[addon].name});
                        }
                    });
                }
                if (reqObj.profileType === "PRODUCT") {
                    reqObj.dimensionType = null;
                } else {
                    reqObj.productName = null;
                }
                $rootScope.detailLoaderMessage = "Saving desi chai profile...";
                $rootScope.showDetailLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addDcCustomProfiles,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        console.log(response.data);
                        $scope.desiChaiProfiles.push(response.data);
                        $("#desiChaiProfileModal").modal("hide");
                    } else {
                        if(response.data.errorMsg != null) {
                            bootbox.alert(response.data.errorMsg);
                        } else {
                            bootbox.alert("Error saving desi chai profile");
                        }
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.getChannelPartnersList = function () {
                if ($scope.channelPartners == null || $scope.channelPartners.length == 0) {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.partnerManagement.get
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.channelPartnerList = response.data;
                            $scope.channelPartners = [];
                            $scope.channelPartnerList.map(function (partner) {
                                if (partner.partnerStatus === "ACTIVE") {
                                    partner = {
                                        id: partner.kettlePartnerId,
                                        name: partner.partnerName,
                                    };
                                    $scope.channelPartners.push(partner);
                                    $scope.partnerMap[partner.id] = partner;
                                }
                            });
                        } else {
                            bootbox.alert("Error loading channel partner list.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.getAllBrands = function () {
                if ($scope.brands == null || $scope.brands.length == 0) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.brandManagement.getAllBrands
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.brands = response.data;
                            $scope.brands.map(function (brand) {
                                $scope.brandMap[brand.brandId] = brand;
                            });
                            $rootScope.showFullScreenLoader = false;
                        } else {
                            bootbox.alert("Error getting brands.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setSelectedBrand = function (selectedBrand) {
                $scope.selectedBrand = selectedBrand;
            };

            $scope.setSelectedPartner = function (selectedPartner) {
                $scope.selectedPartner = selectedPartner;
            };

            $scope.getDesiChaiProfileMappings = function () {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    return;
                }
                if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    return;
                }
                var selectedUnits = [];
                $scope.unitList.map(function (unit) {
                    if(unit.selected === true) {
                        selectedUnits.push(unit);
                    }
                });
                if(selectedUnits.length === 0) {
                    bootbox.alert("Please select units");
                    return;
                }
                var reqObj = {unitIds:[], partnerId: $scope.selectedPartner.id, brandId: $scope.selectedBrand.brandId};
                $scope.unitList.map(function (unit) {
                    if(unit.selected === true) {
                        reqObj.unitIds.push(unit.id);
                    }
                });
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.getDcCustomProfileMappings,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.desiChaiProfileMappings = response.data;
                        $scope.desiChaiProfileMappings.map(function (mapping) {
                            mapping.unitName = $scope.unitMap[mapping.unitId].name;
                            mapping.brandName = $scope.brandMap[mapping.brandId].brandName;
                            mapping.partnerName = $scope.partnerMap[mapping.partnerId].name;
                        });
                    } else {
                        bootbox.alert("Error getting desi chai profile mappings.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.addDesiChaiProfileMappings = function () {
                if($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand");
                    return;
                }
                if($scope.selectedPartner == null) {
                    bootbox.alert("Please select partner");
                    return;
                }
                var selectedUnits = [];
                $scope.unitList.map(function (unit) {
                    if(unit.selected === true) {
                        selectedUnits.push(unit);
                    }
                });
                if(selectedUnits.length === 0) {
                    bootbox.alert("Please select units");
                    return;
                }
                $("#desiChaiProfileMappingModal").modal("show");
            };

            $scope.setSelectedProfile = function (selectedProfile) {
                $scope.selectedProfile = selectedProfile;
            };

            $scope.saveDesiChaiProfileMappings = function () {
                if($scope.selectedProfile == null) {
                    bootbox.alert("Please select partner");
                    return;
                }
                var reqObj = [];
                $scope.unitList.map(function (unit) {
                    if(unit.selected === true) {
                        reqObj.push({unitId: unit.id, partnerId: $scope.selectedPartner.id, brandId: $scope.selectedBrand.brandId,
                            profileId: $scope.selectedProfile.id, profileName: $scope.selectedProfile.profileName, status: 'ACTIVE'});
                    }
                });
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.addDcCustomProfileMappings,
                    data: reqObj
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $("#desiChaiProfileMappingModal").modal("hide");
                        $scope.getDesiChaiProfileMappings();
                    } else {
                        if(response.data.errorMsg != null) {
                            bootbox.alert(response.data.errorMsg);
                        } else {
                            bootbox.alert("Error saving desi chai profile mappings");
                        }
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

            $scope.viewProfileData = function (profileId) {
                $scope.viewProfile = null;
                $scope.desiChaiProfiles.map(function (profile) {
                    if(profile.id === profileId) {
                        $scope.viewProfile = profile;
                    }
                });
                $("#desiChaiProfileViewModal").modal("show");
            };

            $scope.updateProfileMappingStatus = function (mapping, status) {
                $rootScope.showDetailLoader = true;
                var data = angular.copy(mapping);
                data.status = status;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerMetadata.updateDcCustomProfileMappingStatus,
                    data: data
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.getDesiChaiProfileMappings();
                    } else {
                        if(response.data.errorMsg != null) {
                            bootbox.alert(response.data.errorMsg);
                        } else {
                            bootbox.alert("Error updating desi chai profile mapping");
                        }
                    }
                    $rootScope.showDetailLoader = false;
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };

        }
    ]
);