adminapp.controller("UpdatePaymentModeMappingCtrl", ['$scope', '$window', '$http', 'AppUtil', '$rootScope',function ($scope, $window, $http, AppUtil, $rootScope) {
    // For updating Data
    $scope.init = function () {
        $scope.typeList = ["CARD", "CASH", "AMEX Card", "COUPON", "CREDIT", "PREPAID", "GYFTR", "OTHERS"]
        $scope.generatePullList = ["YES", "NO"]
        $scope.editableList = ["YES", "NO"]
        $scope.applicableOnDiscountedOrdersList = ["YES", "NO"]
        $scope.autoPullValidateList = ["YES", "NO"]
        $scope.autoTransferList = ["YES", "NO"]
        $scope.autoCloseTransferList = ["YES", "NO"]
        $scope.needsSettlementSlipList = ["YES", "NO"]

    }
    $http({
        method: 'GET',
        url: AppUtil.restUrls.paymentManagement.getPaymentMapping
    }).then(function success(response) {
        if (response.status == -1) {
            alert("Incorrect Data")
        }
        $scope.paymentModeList = response.data;
        console.log($scope.paymentModeList);
    }, function error(response) {
        console.log("error:" + response);
    });

    $scope.getPaymentMethod = function (payments) {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.paymentManagement + payments
        }).then(function success(response) {
            if (response.status == -1) {
                alert("Incorrect Data")
            }
            $scope.updatePaymentMethod = response.data;
            console.log($scope.updatePaymentMethod);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.getPaymentMappingByIndex = function(index){
        console.log($scope.paymentModeList[index]);
        $scope.id= $scope.paymentModeList[index].id;
        $scope.name = $scope.paymentModeList[index].name;
        $scope.type = $scope.paymentModeList[index].type;
        if($scope.typeList.indexOf($scope.type) === -1){
            $scope.otherModeType= $scope.type;
            $scope.type = "OTHERS";
        }
        //$scope.otherModeType=$scope.paymentModeList[index].otherModeType;
        $scope.description= $scope.paymentModeList[index].description;
        $scope.settlementType= $scope.paymentModeList[index].settlementType;
        $scope.status= $scope.paymentModeList[index].status;
        $scope.generatePull= $scope.getBoolString($scope.paymentModeList[index].generatePull);
        $scope.editable= $scope.getBoolString($scope.paymentModeList[index].editable);
        $scope.applicableOnDiscountedOrders= $scope.getBoolString($scope.paymentModeList[index].applicableOnDiscountedOrders);
        $scope.commissionRate= $scope.paymentModeList[index].commissionRate;
        $scope.autoPullValidate= $scope.getBoolString($scope.paymentModeList[index].autoPullValidate);
        $scope.autoTransfer= $scope.getBoolString($scope.paymentModeList[index].autoTransfer);
        $scope.autoCloseTransfer= $scope.getBoolString($scope.paymentModeList[index].autoCloseTransfer);
        $scope.needsSettlementSlip= $scope.getBoolString($scope.paymentModeList[index].needsSettlementSlip);
        $scope.category= $scope.paymentModeList[index].category;
        $scope.validationSource= $scope.paymentModeList[index].validationSource;
        $scope.ledgerName= $scope.paymentModeList[index].ledgerName;
        $("#paymentMethodModal").modal("show");
    };

    $scope.getBoolString = function(val){
        if(val){
            return "YES";
        }else{
            return "NO";
        }
    }

    function getStringToBool(val){
        return val === "YES";
    }

    $scope.submitUpdatePaymentMethod = function () {
        console.log("line 46  update call");
        var obj = {
            "id" : $scope.id,
            "name": $scope.name,
            "type": $scope.type,
            "otherModeType":null,
            "description": $scope.description,
            "settlementType": $scope.settlementType,
            "generatePull": getStringToBool($scope.generatePull),
            "commissionRate": $scope.commissionRate,
            "status": $scope.status,
            "category": $scope.category,
            "editable":getStringToBool( $scope.editable),
            "applicableOnDiscountedOrders":getStringToBool( $scope.applicableOnDiscountedOrders),
            "autoPullValidate": getStringToBool($scope.autoPullValidate),
            "autoTransfer": getStringToBool($scope.autoTransfer),
            "autoCloseTransfer": getStringToBool($scope.autoCloseTransfer),
            "needsSettlementSlip": getStringToBool($scope.needsSettlementSlip),
            "validationSource": $scope.validationSource,
            "ledgerName": $scope.ledgerName
        }
        if ($scope.name == null || $scope.name.trim().length === 0) {
            bootbox.alert("Invalid Mode Name")
            return;
        }
        if ($scope.type == null || $scope.type.trim().length === 0) {
            bootbox.alert("Invalid Mode Type")
            return;
        }
        if ($scope.description == null || $scope.description.trim().length === 0) {
            bootbox.alert("Invalid Mode Description")
            return;
        }
        if ($scope.settlementType == null || $scope.settlementType.trim().length === 0) {
            bootbox.alert("Invalid Settlement Type")
            return;
        }
        if ($scope.status == null || $scope.status.trim().length === 0) {
            bootbox.alert("Invalid Mode Status")
            return;
        }
        if ($scope.commissionRate == null) {
            bootbox.alert("Invalid Commission Rate")
            return;
        }
        if ($scope.category == null || $scope.category.trim().length === 0) {
            bootbox.alert("Invalid Mode Category")
            return;
        }
        if($scope.editable==null || $scope.editable.trim().length===0){
            bootbox.alert("Invalid Editable Information")
        }
        if($scope.applicableOnDiscountedOrders==null || $scope.applicableOnDiscountedOrders.trim().length===0){
            bootbox.alert("Invalid Applicable On Discounted Orders Information")
        }
        if ($scope.generatePull == null) {
            bootbox.alert("Invalid Generate Pull")
            return;
        }
        if($scope.autoPullValidate==null){
            bootbox.alert("Invalid Auto Pull Validate Information")
        }
        if($scope.autoCloseTransfer==null){
            bootbox.alert("Invalid Auto Close Transfer Information")
        }
        if($scope.autoTransfer==null){
            bootbox.alert("Invalid Auto Transfer Information")
        }
        if($scope.needsSettlementSlip==null){
            bootbox.alert("Invalid Auto Transfer Information")
        }
        if ($scope.validationSource == null || $scope.validationSource.trim().length === 0) {
            bootbox.alert("Invalid Validation Source")
            return;
        }
        if ($scope.ledgerName == null || $scope.ledgerName.trim().length === 0) {
            bootbox.alert("Invalid Ledger Name")
            return;
        }
        if($scope.type === "OTHERS"){
            if($scope.otherModeType == null || $scope.otherModeType.trim().length ===0){
                bootbox.alert("Invalid Specific Mode Type")
                return;
            }
        }
        if($scope.type === "OTHERS"){
            obj.type = $scope.otherModeType;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.paymentManagement.updatePaymentMapping,
            data: obj
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status === 200 && response.data) {
                alert("Payment Mode Method Updated successfully");
                window.location.reload();
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
            alert("Unable to update Payment Mode Method");
        });
    }

    var data = {
        "id": $scope.id,
        "modeName": $scope.name,
        "modeType": $scope.type,
        "otherModeType":$scope.otherModeType,
        "modeDescription": $scope.description,
        "settlementType": $scope.settlementType,
        "generatePull": $scope.generatePull,
        "commissionRate": $scope.commissionRate,
        "modeStatus": $scope.status,
        "modeCategory": $scope.category,
        "editable": $scope.editable,
        "applicableOnDiscountedOrders": $scope.applicableOnDiscountedOrders,
        "automaticPullValidate": $scope.autoPullValidate,
        "automaticTransfer": $scope.autoTransfer,
        "automaticCloseTransfer": $scope.autoCloseTransfer,
        "needsSettlementSlip": $scope.needsSettlementSlip,
        "validationSource": $scope.validationSource,
        "ledgerName": $scope.ledgerName
    }
    // For Updating Mapping
    $http({
        method: 'GET',
        url: AppUtil.restUrls.unitMetaData.allUnits,
        params: {
            category: 'CAFE'
        }
    }).then(function success(response) {
        $rootScope.showFullScreenLoader = false;
        $scope.cafelist = response.data;
       $scope.unitlist = $scope.cafelist;
        $scope.selectedUnit = $scope.unitlist[0];
    }, function error(response) {
        $rootScope.showFullScreenLoader = false;
        console.log("error:" + response);
    });

    $scope.addUnit = function (category) {
        $scope.storeSelectedCafes.forEach(function (value) {
            $scope.selectedUnitPaymentMappings = $scope.addToUnit($scope.selectedUnitPaymentMappings, value);
        });
        $scope.storeSelectedCafes=[];
    }
    $scope.removeUnit = function (category, unitId) {
        $scope.selectedUnitPaymentMappings = $scope.removeFromUnit($scope.selectedUnitPaymentMappings, unitId);
    }

    $scope.addToUnit = function (paymentMappings, unit) {
       console.log(unit);
        if (!angular.isUndefined(paymentMappings) || paymentMappings != null) {
            var contains = false;
            var newPaymentMappings=[];
            paymentMappings.forEach(function (mappings) {
                if (mappings.unitDetail.id === unit.id && mappings.mappingStatus ==="IN_ACTIVE") {
                    mappings.mappingStatus = 'ACTIVE';
                    newPaymentMappings.push(mappings);
                    contains = true;
                } else if (mappings.unitDetail.id === unit.id && mappings.mappingStatus ==="ACTIVE") {
                    newPaymentMappings.push(mappings);
                    contains = true;
                }else{
                    newPaymentMappings.push(mappings);
                }
            });
            if (!contains) {

                var unitPaymentMapping = {};
                unitPaymentMapping.mappingStatus = 'ACTIVE';
                unitPaymentMapping.paymentMode = $scope.pymtSelected;
                unitPaymentMapping.unitDetail = {};
                unitPaymentMapping.unitDetail.id =unit.id;
                unitPaymentMapping.unitDetail.name = unit.name;
                newPaymentMappings.push(unitPaymentMapping);
            }
        }
        return newPaymentMappings;
    }

    $scope.removeFromUnit = function (paymentMappings, unitId) {
        //console.log(unitId)
        var newPaymentMapping = [];
        if (!angular.isUndefined(paymentMappings) || paymentMappings != null) {
            var newList = [];
            paymentMappings.forEach(function (mapping) {
                if (mapping.unitDetail.id !== unitId ) {
                    newList.push(mapping);
                }
                else{
                    mapping.mappingStatus='IN_ACTIVE';
                    newList.push(mapping);
                }
            });
            newPaymentMapping = newList;
        }
        return newPaymentMapping;
    }
    $scope.storeSelectedCafes=[];
    $scope.multiSelectSettingsCafes = {
        showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
        scrollableHeight: '250px',trackBy:'id',clearSearchOnClose: true
    };

    $scope.editPymtMapping = function (paymentMode) {
        $scope.storeSelectedCafes=[];
        $scope.pymtForMapping = paymentMode;
        $scope.action = "Edit Mapping";
         $scope.pymtSelected = paymentMode;
        $scope.activationUnits = [];
        $scope.selectedCafe = $scope.cafelist[0];
        $scope.allCafesCheck = false;
        $scope.addedCafes = [];
        $scope.unitlist=[];
        $scope.allPaymentDataData = {};
        $scope.selectedUnit = null;
        $("#unitpaymentModal").modal("show");
        $http({
            method: 'GET',
            url: AppUtil.restUrls.paymentManagement.getPaymentModeMapping  ,
            params: {
                id: paymentMode.id,
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.unitPaymentMappings = response.data;
            $scope.selectedUnitPaymentMappings = []
            for(var mapping in $scope.unitPaymentMappings) {
                $scope.selectedUnitPaymentMappings.push($scope.unitPaymentMappings[mapping]);
            }
            console.log($scope.selectedUnitPaymentMappings);
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.saveUpdatePaymentMethodMapping = function () {
        console.log("Updating for mappings = ",$scope.selectedUnitPaymentMappings);
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.paymentManagement.updatePaymentModeMapping,
            data: $scope.selectedUnitPaymentMappings
        }).then(function success(response) {
           $rootScope.showFullScreenLoader=false;
            $("#unitpaymentModal").modal("hide");
            if(response.status === 200){
                alert("Mapping updated")
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }
 }])
