/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("manageAppsCtrl",
    function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil, Popeye) {

        function reset() {
            $scope.all = false;
            $scope.selectedApps = {};
            $scope.selectedUnits = {};
        }

        $scope.init = function () {
            $rootScope.showFullScreenLoader = true;
            $scope.activeVersion = null;
            reset();
            $scope.getUnitList();
        };

        $scope.getUnitList = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnits,
                params: {
                    category: "CAFE"
                }
            }).then(function success(response) {
                $scope.unitList = response.data;
                $scope.unitList = $scope.unitList.filter(function (unit) {
                    return unit.status === "ACTIVE";
                });
                $scope.getActiveVersion();
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.getActiveVersion = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.appsManagement.latest
            }).then(function success(response) {
                $scope.activeVersion = response.data;
                prepareUpdateArrayForUnits();
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        function prepareUpdateArrayForUnits() {
            var updateArray = {};
            for(var i in $scope.activeVersion.apps){
                var app = $scope.activeVersion.apps[i];
                app.units.filter(function (unit) {
                    return unit.code!=null && unit.code!=undefined;
                }).forEach(function (unit) {
                    if(updateArray[unit.id]==undefined){
                        updateArray[unit.id]=[];
                    }
                    updateArray[unit.id].push({name:unit.code,current:app.appName==unit.code});
                });
            }
            $scope.unitList.forEach(function (unit) {
                if(updateArray[unit.id] != undefined && updateArray[unit.id].length > 0){
                    unit["updated"] = updateArray[unit.id];
                }
            });
        }

        function getSelectedUnits(app) {
            var alreadyUpdated = {};
            app.units.forEach(function (unit) {
                if(unit.code!=undefined && unit.code!=null){
                    alreadyUpdated[unit.id] = unit.code;
                }
            });

            return $scope.unitList.filter(function (unit) {
                return $scope.selectedUnits[unit.id];
            }).map(function (unit) {
                if(alreadyUpdated[unit.id] === undefined || alreadyUpdated[unit.id] === null){
                    return {
                        id: unit.id,
                        name: unit.name
                    };
                }else{
                    return {
                        id: unit.id,
                        code:alreadyUpdated[unit.id],
                        name: unit.name
                    };
                }

            });
        }

        function prepareRequestObj() {
            for (var i in $scope.activeVersion.apps) {
                var app = $scope.activeVersion.apps[i];
                if (Object.keys($scope.selectedApps).indexOf(app.appName) != -1) {
                    app['units'] = getSelectedUnits(app);
                }
            }
            return $scope.activeVersion;
        }

        $scope.pushBuild = function () {
            if (Object.keys($scope.selectedUnits).length > 0
                && Object.keys($scope.selectedApps).length > 0) {
                var obj = prepareRequestObj();

                var previewModal = Popeye.openModal({
                    templateUrl: "buildPreview.html",
                    controller: "submitBuildCtrl",
                    resolve: {
                        activeVersion: function(){
                            return angular.copy($scope.activeVersion);
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                previewModal.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful) {
                            $rootScope.showFullScreenLoader = true;
                            $http({
                                method: 'POST',
                                url: AppUtil.restUrls.appsManagement.activate,
                                data: obj
                            }).then(function success(response) {
                                if(response.status==200){
                                    bootbox.alert("Builds pushed onto cafes successfully");
                                    $scope.activeVersion = response.data;
                                    reset();
                                }else{
                                    bootbox.alert("Something went wrong! Please contact support or try again later");
                                }
                                $rootScope.showFullScreenLoader = false;
                            }, function error(response) {
                                console.log("error:" + response);
                                bootbox.alert("Something went wrong! Please contact support or try again later");
                                $rootScope.showFullScreenLoader = false;
                            });
                        }
                    });
            } else {
                bootbox.alert("Please select at least one cafe and app to continue");
            }
        };

        $scope.checkAll = function (val) {
            for (var i in $scope.unitList) {
                $scope.selectedUnits[$scope.unitList[i].id] = val;
            }
        };

        $scope.addToSelected = function (app) {
            $scope.selectedApps[app.appName] = app;
        };

        $scope.removeFromSelected = function (app) {
            delete $scope.selectedApps[app.appName];
        };

        $scope.checkInSelected = function (app) {
            return Object.keys($scope.selectedApps).indexOf(app.appName) !== -1;
        };

    }
).controller('submitBuildCtrl', function ($rootScope, $scope, Popeye, activeVersion) {

        $scope.initPreview = function(){
            $scope.version = activeVersion;
        };

        $scope.submit = function (){
            Popeye.closeCurrentModal(true);
        };

        $scope.cancel = function (){
            Popeye.closeCurrentModal(false);
        };

    }
);
