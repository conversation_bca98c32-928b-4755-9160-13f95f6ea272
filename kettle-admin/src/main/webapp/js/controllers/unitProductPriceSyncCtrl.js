
adminapp.controller("unitProductPriceSyncCtrl", function ($scope, $rootScope, $http, AppUtil, $timeout) {

    $scope.init = function () {
        $scope.unitCategories = [];
        getCategories();
        $scope.selectedCategory = null;

        $scope.selectedUnits = {
            syncFromUnit : null,
            syncToUnit : null
        }
        $scope.allUnits = [];
        $scope.hideUnits = true;
    }

    function getCategories() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.families
        }).then(function success(response) {
            if(response != null && response.data != null) {
                $scope.unitCategories = response.data;
            }
        }, function error(response) {
             console.log("error:" + response);
        });
    }

    $scope.onChangeCategory = function() {
        $scope.hideUnits = true;
        $scope.selectedUnits.syncToUnit = null;
        $scope.selectedUnits.syncFromUnit = null;
    }

     $scope.getUnits = function() {
        $rootScope.showFullScreenLoader = true;
        $scope.hideUnits = false;
        clearUnits();
        var url ="";
        if($scope.selectedCategory !== undefined && $scope.selectedCategory != null){
            url = AppUtil.restUrls.unitMetaData.activeUnits + "?category=" + $scope.selectedCategory;
        }else {
            url = AppUtil.restUrls.unitMetaData.activeUnits + "?category=ALL"
        }
        $http({
            method: 'GET',
            url: url
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.data != null && Array.isArray(response.data)) {
                $scope.allUnits = response.data;
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("Error : " , response.errorMessage);
        });
    }

    function clearUnits() {
        $scope.selectedUnits.syncToUnit = null;
        $scope.selectedUnits.syncFromUnit = null;
        $timeout(function(){
            $('.unit-select').select2('val', null);
        });
    }

    $scope.showSyncBtn = function() {
        return ($scope.selectedUnits.syncToUnit != null && $scope.selectedUnits.syncFromUnit != null);
    }

    $scope.excludeToUnitSelected = function(unit) {
        if($scope.selectedUnits.syncToUnit == null) {
            return true;
        }
        return unit.id !== $scope.selectedUnits.syncToUnit.id;
    };

    $scope.excludeFromUnitSelected = function(unit) {
        if($scope.selectedUnits.syncFromUnit == null) {
            return true;
        }
        return unit.id !== $scope.selectedUnits.syncFromUnit.id;
    };

    $scope.syncUnitProductPrice = function() {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.unitMetaData.syncUnitProductPrice,
            params: {
                fromUnitId : $scope.selectedUnits.syncFromUnit.id,
                toUnitId : $scope.selectedUnits.syncToUnit.id
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if(response.data != null && response.data.error != undefined) {
                bootbox.alert("Error while syncing data : " +  response.data.error);
                return;
            }
            bootbox.alert("Unit product pricing was" + (response.data == true ? " Successfully synced " : " Failed to Sync ") + "from " + $scope.selectedUnits.syncFromUnit.name + " to " + $scope.selectedUnits.syncToUnit.name);
            if (response.data) {
                $scope.allUnits = [];
                $scope.hideUnits = true;
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("Error : " , response.errorMessage);
            bootbox.alert("Error while syncing data");
        });
    }

});