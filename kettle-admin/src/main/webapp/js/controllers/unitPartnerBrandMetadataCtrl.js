/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("unitPartnerBrandMetadataCtrl", function ($scope, $http, $rootScope, $log, $window, AppUtil) {

    $scope.init = function () {
        $scope.getMetadataTypes();
        $scope.getBrandList();
        $scope.getUnitList();
        $scope.getPartnerList();
        $scope.mappingStatusList = [
            {id: 1, name: "ACTIVE", value: "ACTIVE"},
            {id: 2, name: "IN_ACTIVE", value: "IN_ACTIVE"}
        ];
        $scope.mappingMetaStatusList = ["Y","N"];
        $scope.statusMap = {"ACTIVE": $scope.mappingStatusList[0], "IN_ACTIVE": $scope.mappingStatusList[1]};
        $scope.globalValue = null;
        $scope.globalSelect = false;
        $scope.newValue = null;
        $scope.newStatus = null;
        $scope.globalStatus = null;
        $scope.globalMetaStatus = true;
    };

    $scope.getMetadataTypes = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getUnitPartnerBrandMetadataKeys,
        }).then(function success(response) {
            if (response.status === 200) {
                $scope.loading = false;
                $scope.unitPartnerBrandMetadataKeys = [];
                response.data.map(function (key, index) {
                    $scope.unitPartnerBrandMetadataKeys.push({id: index, name: key});
                });
                $scope.setSelectedKey($scope.unitPartnerBrandMetadataKeys[0]);
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.getBrandList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands,
        }).then(function success(response) {
            if (response.status === 200) {
                $scope.loading = false;
                $scope.brandList = response.data;
                $scope.brandMap = {};
                $scope.brandList.map(function (brand) {
                    $scope.brandMap[brand.brandId] = brand;
                });
                $scope.setSelectedBrand($scope.brandList[0])
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.getUnitList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits + '?category=CAFE'
        }).then(function success(response) {
            $scope.unitList = response.data;
            $scope.unitMap = {};
            $scope.unitList.map(function (unit) {
                $scope.unitMap[unit.id] = unit;
            });
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.getPartnerList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.channelPartner.getActiveChannelPartners
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.channelPartners = response.data.filter(function (partner) {
                    return partner.type === "DINE_IN";
                });
                $scope.channelPartnerMap = {};
                $scope.channelPartners.map(function (partner) {
                    if (partner.type === "DINE_IN") {
                        $scope.channelPartnerMap[partner.id] = partner;
                    }
                });
                $scope.setSelectedPartner($scope.channelPartners[0]);
                $rootScope.showFullScreenLoader = false;
            } else {
                bootbox.alert("Error loading channel partner list.");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.getMetadata = function () {
        if($scope.selectedKey.name==="LOYALTY_POINTS"){
            $scope.globalMetaStatus=true;
        }
        else {
            $scope.globalMetaStatus=false;
        }
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getUnitPartnerBrandMetadata + "?key=" + $scope.selectedKey.name,
        }).then(function success(response) {
            if (response.status === 200) {
                $scope.loading = false;
                var metadata = response.data.filter(function (data) {
                    return data.brandId === $scope.selectedBrand.brandId && data.partnerId === $scope.selectedPartner.id;
                });
                metadata.map(function (data) {
                    data.unit = $scope.unitMap[data.unitId];
                    data.brand = $scope.brandMap[data.brandId];
                    data.partner = $scope.channelPartnerMap[data.partnerId];
                    data.selected = false;
                    data.newValue = data.value;
                    data.newStatus = $scope.statusMap[data.status];
                });
                var dataMap = {};
                metadata.map(function (data) {
                    dataMap[data.unitId] = data;
                });
                var unitPartnerBrandMetadata = [];
                $scope.unitList.map(function (unit) {
                    var data = dataMap[unit.id];
                    unitPartnerBrandMetadata.push({
                        unitId: unit.id,
                        brandId: $scope.selectedBrand.brandId,
                        partnerId: $scope.selectedPartner.id,
                        unit: unit,
                        brand: $scope.selectedBrand,
                        partner: $scope.selectedPartner,
                        selected: false,
                        key: $scope.selectedKey.name,
                        value: data != null ? data.value : null,
                        newValue: data != null ? data.value : null,
                        status: data != null ? data.status : null,
                        newStatus: data != null ? $scope.statusMap[data.status] : null,
                    });
                });
                $scope.unitPartnerBrandMetadata = unitPartnerBrandMetadata;
                console.log($scope.unitPartnerBrandMetadata);
            } else {
                console.log(response);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.setSelectedBrand = function (selectedBrand) {
        $scope.selectedBrand = selectedBrand;
    };

    $scope.setGlobalValue = function (value) {
        $scope.globalValue = value;
    };
    $scope.setNewValue = function (data) {
        console.log(data);
        // $scope.globalValue=data.newValue;
        // console.log($scope.globalValue);
    };

    $scope.setNewStatus = function (data) {
        // $scope.globalStatus=data.newStatus.value;
        console.log(data);
    };

    $scope.setSelectedPartner = function (selectedPartner) {
        $scope.selectedPartner = selectedPartner;
    };

    $scope.setSelectedKey = function (selectedKey) {
        $scope.selectedKey = selectedKey;
    };

    $scope.toggleSelectMapping = function (data, setStatus) {
        if (setStatus === true) {
            data.selected = !data.selected;
        }
        if (data.selected === true) {
            data.newValue = $scope.globalValue != null ? $scope.globalValue : data.newValue;
            data.newStatus = $scope.globalStatus != null ? $scope.globalStatus : $scope.statusMap[data.status];
        } else {
            data.newValue = data.value;
            data.newStatus = $scope.statusMap[data.status];
        }
    };

    $scope.selectAll = function () {
        $scope.unitPartnerBrandMetadata.map(function (data) {
            data.selected = $scope.globalSelect;
            $scope.toggleSelectMapping(data);
        });
    };

    $scope.submitAddMetadata = function () {
        var unitList = [];
        var req = [];
        $scope.unitPartnerBrandMetadata.forEach(function (data) {
            if (data.selected) {
                if (data.newStatus != null && data.newValue != null) {
                    req.push({
                        unitId: data.unitId,
                        partnerId: data.partnerId,
                        brandId: data.brandId,
                        status: data.newStatus.value,
                        value: data.newValue,
                        key: data.key
                    })
                } else {
                    unitList.push(data.unit.name);
                    return;
                }
            }
        });
        if (unitList.length > 0) {
            bootbox.alert("Incorrect data for units: " + unitList.join());
            return;
        }
        var dialog = bootbox.dialog({
            title: 'Adding metadata. Please wait.',
            message: '<p><i class="fa fa-spin fa-spinner" /> Processing...</p>'
        });
        $http({
            method: 'POST',
            url: AppUtil.restUrls.brandMetaData.addUnitPartnerBrandMetadataBulk,
            data: req
        }).then(function success(response) {
            var message = "", title = "";
            if (response.status === 200) {
                title = "Congratulations!!";
                message = 'Metadata added successfully!';
                $scope.loading = false;
                $scope.getMetadata();
                $scope.clearCache('reloadUnitPartnerBrandMetadata');
            } else {
                title = 'Oops sorry!';
                message = response.data ? response.data.errorMessage : "Something went wrong";
                console.log(response);
            }
            dialog.find('.bootbox-head').html(title);
            dialog.find('.bootbox-body').html(message);
            setTimeout(function () {
                dialog.modal('hide');
            }, 2000);
        }, function error(response) {
            console.log("error:" + response);
            dialog.find('.bootbox-head').html('Oops sorry!');
            dialog.find('.bootbox-body').html("Error adding metadata");
        });
    };



    $scope.clearCache = function (type) {
        var url = AppUtil.restUrls.masterCacheManagement[type];
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'get',
            url: url
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if(response.data != true) {
                bootbox.alert("Error refreshing cache after update. Please refresh cache manually.");
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            bootbox.alert("error:" + JSON.stringify(response));
        });
    };
});