adminapp.controller("kiosLocationctrl", function ($state,$log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

	$http({
		method: 'GET',
		url : AppUtil.restUrls.kioskManagement.kioskCompany,
		}).then(function success(response) {
			$scope.kiosCompanyList = response.data;
			console.log($scope.kiosCompanyList);
			//console.log("dd=",$scope.totalItems);
			//console.log("listCompany=",$scope.kiosCompanyList);
			
		}, function error(response) {
			console.log("error:"+response);
	});
	
	
    /*$scope.init = function () {
        //console.log($location.path());
        if ($location.path() === "/dashboard/unit") {
            $location.path("/dashboard/unit/CAFE");
            $scope.loading = false;
        }
    }*/
	 $scope.addKiosLocation = function(){
		$scope.action = "Add"
		$scope.officeName="",
		$scope.officeShortCode="";
		$scope.selectedPaymentMode="",
		$scope.newUnitRegion="";
		$("#locationModal").modal("show");
	 }	
	 
	 /*$scope.locationChange = function(status,locationID){
		// console.log(status,"")
		 
		 $http({
             method: 'GET',
             url: AppUtil.restUrls.kioskManagement.kiosLocationStatus + '?locationId=' + locationID+'&activate=' + status,
         }).then(function success(response) {
            // $scope.unitlist = response.data;
             console.log("ResultAssigned=",response);
             
         }, function error(response) {
             console.log("error:" + response);
         });
		 
	 }*/
	 
	 
	 
	 $scope.showCompanyLocationList = function(companyNameResult) {
		   console.log("SingleObj=",companyNameResult);
		 $scope.kiosCompanyList.forEach(function(allCompanyList)
		   {
			if(companyNameResult.companyId==allCompanyList.companyId){
				 $scope.companyAllOfficeList=allCompanyList.officeList;
			  }
		   })
		   
		  // $scope.kiosCompanyList.companyDetails=companyNameResult;
		   
			console.log("OFFICE=",$scope.companyAllOfficeList);
	 };
	 $scope.sort_by = function(predicate) {
	        $scope.predicate = predicate;
	        $scope.reverse = !$scope.reverse;
	    };
	    
	    $scope.updatePerm = function(val){
	    	if(val==='true'){
	    		$scope.permanentAddressSame = true;
	    	}else{
	    		$scope.permanentAddressSame = false;
	    	}
	    } 
	 $scope.submitAddLocation=function()
	 {
		 if ($scope.selectedCompanyNameList == null || $scope.selectedCompanyNameList == "") {
	           alert("Please Select company name ");
	           return;
	       }
		 if ($scope.selectedOfficeNameList == null || $scope.selectedOfficeNameList == "") {
	           alert("Please Select Office name ");
	           return;
	       }
		 
		 if ($scope.locationName == null || $scope.locationName == "") {
	           alert("Please enter location name ");
	           return;
	       }
		 if ($scope.locationShortCode == null || $scope.locationShortCode == "") {
	           alert("Please enter location name ");
	           return;
	       }
		 if ($scope.locationAddress == null || $scope.locationAddress == "") {
	           alert("Please enter location address ");
	           return;
	       }
		 
		 //console.log("Company=",$scope.selectedCompanyNameList);
		 //console.log("Office=",$scope.selectedOfficeNameList);
		 $scope.selectedOfficeNameList.companyDetails=$scope.selectedCompanyNameList;
		 console.log("Office123=",$scope.selectedOfficeNameList);
		 		var locationObj = {
				 locationName:$scope.locationName,
				 locationShortCode:$scope.locationShortCode,
				 locationAddress:$scope.locationAddress,
				 assigned:false,
				 locationStatus:'IN_ACTIVE',
				 officeDetails:$scope.selectedOfficeNameList 
				}
		 		 console.log("locationObject=",locationObj);
		 		
		 		$rootScope.showFullScreenLoader = true;
		 		$http({
					method : 'POST',
					url : AppUtil.restUrls.kioskManagement.kioskLocation,
					data :locationObj,
					headers:{
					'Access-Control-Allow-Origin': '*'
					}
				})
				.then(
				function success(response) {
					alert("Submitted Successfully");
					$rootScope.showFullScreenLoader = false;
					$scope.officeLocationListView.push(response.data);
					//$("#btnSubmit").show();
					 $("#locationModal").modal("hide");
					//$("#btnSubmit").show();
					//console.log("res",response);
				});	
	 }
	 
	 $scope.LocationStatusChange=function(locationStatus,locationID)
	    {
	    	 //console.log(locationStatus);
	  	   if(locationStatus=="ACTIVE")
	  		   {
	  		   locationStatusData=true;		   
	  		   }
	  			  else if(locationStatus=="IN_ACTIVE")
	  			   {
	  				locationStatusData=false;		   
	  			   }
	  			   else
	  				   {
	  				 locationStatusData=null;
	  				   }
	  	  // console.log("STATUS="+locationStatus+"locationID="+locationID);
	  	   $http({
	             method: 'GET',
	             url: AppUtil.restUrls.kioskManagement.kiosLocationStatus + '?locationId=' + locationID+'&activate=' + locationStatusData,
	         }).then(function success(response) {
	      	   $scope.officeLocationListView.forEach(function(locationListData)
	      	   {
	      		   //console.log(officeListData)
	      		if(locationListData.locationId==locationID)  
	      			{
	      			if(locationStatus=="IN_ACTIVE"){
	      				locationListData.locationStatus="IN_ACTIVE";
	      			}
	      			
	      			else if(locationStatus=="ACTIVE"){
	      				locationListData.locationStatus="ACTIVE";
	          			}
	      			}
	      	   });
	      	   
	            // $scope.unitlist = response.data;
	           //  console.log("ResultHere=",response);
	             
	         }, function error(response) {
	             console.log("error:" + response);
	         });
	  	   
	    	
	    }
	 
	 
	 $scope.showCompanyViewList = function(companyViewList) {
		   console.log("SingleObj=",companyViewList);
		 $scope.kiosCompanyList.forEach(function(allCompanyList)
		   {
			if(companyViewList.companyId==allCompanyList.companyId){
				 $scope.companyAllOfficeView=allCompanyList.officeList;
			  }
		   })
		   
		  // $scope.kiosCompanyList.companyDetails=companyNameResult;
			//console.log("OFFICE=",$scope.companyAllOfficeView);
	 };	
	 
	 $scope.submitViewLocation=function(ddd)
	 {
		$scope.officeLocationListView=[]; 
		console.log($scope.selectedLocationCompanyView);
		console.log($scope.selectedOfficeNameView);
		$scope.selectedOfficeNameView.companyDetails=$scope.selectedLocationCompanyView;
		console.log($scope.selectedOfficeNameView);
		$scope.officeLocationListView=$scope.selectedOfficeNameView.locationList;
		//$scope.officeLocationListView;
		$scope.numPerPage 		= 50;
		$scope.currentPage 		= 1;
		$scope.filteredItems 	= 	$scope.officeLocationListView.length; //Initially for no filter  
		$scope.totalItems 		=	$scope.filteredItems;
	 }
	 
	 /*$scope.addUnit = function(locationObj){
		 locationObj.officeDetails = $scope.selectedOfficeNameView;
		 AppUtil.location = locationObj;
		 $location.path("/dashboard/unit");
	 }*/
	 
	
});