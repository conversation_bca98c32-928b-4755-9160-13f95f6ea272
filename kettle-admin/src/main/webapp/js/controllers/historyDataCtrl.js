adminapp.controller('historyDataCtrl',
    ['$log', 'AuthService', '$cookieStore', '$rootScope', '$scope', '$http', '$location', 'AppUtil',
        function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

            $scope.init = function () {
                $scope.CafeMenuHistoryList = [];
                $scope.unitList = [];
                $scope.getAllBrands();
                $scope.getUnitList();
                $scope.zomatoMenuFlag = false;
                $scope.swiggyMenuFlag = false;
                $scope.lastUpdatedTimeString = '';
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                    $scope.unitMap = {};
                    $scope.unitList.map(function (unit) {
                        $scope.unitMap[unit.id] = unit.name;
                    });
                });
            };

            $scope.getAllBrands = function () {
                if ($scope.brands == null || $scope.brands.length == 0) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.brandManagement.getAllBrands
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.brands = response.data;
                            $rootScope.showFullScreenLoader = false;
                        } else {
                            bootbox.alert("Error getting brands.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setSelectedBrand = function (selectedBrand) {
                $scope.selectedBrand = selectedBrand;
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                console.log($scope.selectedUnit);
            };

            $scope.logData = function () {
                $rootScope.showFullScreenLoader=true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerMetadata.menuAutoPushHistory,
                    params: {
                        brandId: $scope.selectedBrand.brandId,
                        unitId: $scope.selectedUnit.id
                    }
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader=false;
                    if (response.status === 200 && response.data != null) {
                        $scope.CafeMenuHistoryList = response.data;
                        if( $scope.CafeMenuHistoryList.length===0) {
                            bootbox.alert("Error getting menu auto push  History data.");
                        }
                    } else {
                        bootbox.alert("Error getting menu auto push  History data.");
                    }

                }, function error(response) {
                    console.log("error:" + response);
                });
            };
        }
    ]);
