/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
    .controller(
        'offerModalCtrl',
        function ($scope, $rootScope, $http, $timeout, $window, $location, AppUtil, $uibModalInstance, editOffer, brands, listTypes, cafelist) {

            $scope.init = function(){
                $rootScope.enableScreenFilter = true;
                if(editOffer.isCloning !== undefined && editOffer.isCloning != null){
                   $scope.isCloning = editOffer.isCloning;
                }
                if (editOffer !== null) {
                    $scope.processingForEditMode();
                }
            }
            $scope.yesNoOption = [{
                code: false,
                name: "No"
            }, {
                code: true,
                name: "Yes"
            }];
            $scope.offerCategories = ["BILL", "ITEM"];
            $scope.offerValueTypes = ["PERCENTAGE", "FLAT"];
            $scope.offerStatuses = ["ACTIVE", "IN_ACTIVE"];
            $scope.offerScopes = ["INTERNAL", "EXTERNAL", "MASS", "CUSTOMER", "CORPORATE"];
            $scope.frequencyStrategyList = ["TIMED_BASED","QUANTITY_BASED","TIME_QUANTITY_BASED"];
            $scope.newCustomerCheck = false;
            $scope.acquisitionTypes = ["FB_LEADGEN","GOOGLE","CHAAYOS-COD","NEO_SERVICE","FACEBOOK","NEWSPAPER","INSTAGRAM","CLM","WHATSAPP","SHOPIFY","CONTLO_SHOPIFY"];$scope.sourceMap = new Map();
            $scope.selectedAcquisitionSource = [];
            $scope.source = [];
            $scope.brands = brands;
            $scope.sel = [];
            $scope.subCatObj = [];
            $scope.prodObj = [];
            $scope.freeProdObj = [];

            $scope.unitAllObj = [];
            $scope.cityAllObj = [];
            $scope.unitObjs = [];

            $scope.orderSourceObj = [];
            $scope.partObj = [];
            $scope.channelPartObj = [];
            $scope.paymentModeObj = [];

            $scope.selectedCategoriesLength = 0;
            $scope.selectedSubCategoriesLength = 0;
            $scope.selectedProductsLength = 0;

            $scope.selectedFreeProductLength = 0;
            $scope.allCitiesList = [];

            $scope.cancel = function () {
                $uibModalInstance.close(null);
            };
            $scope.submit = function () {
                $uibModalInstance.close($scope.offerDetail.text);
                window.location.reload();
            };
            $scope.tabIndex = 0;
            $scope.subIndex = 0;
            $scope.selectedProducts = [];
            $scope.tempselectedProducts = [];
            $scope.tempselectedProductsLength = 0;
            $scope.tempMetaDataMappings =[];

            $scope.selectedFreeProduct = [];

            $scope.filterCategory = null;
            $scope.filterSubCategory = null;
            $scope.isSelected = false;
            $scope.hideFilter = true;
            $scope.productBySubCategory = [];
            $scope.productByCategory = [];
            $scope.prodByCatAndSubCat = [];

            $scope.tncInputs = [];
            $scope.tncInputs.push('');

            $scope.acqSources = [''];
            
            $scope.selectedBrandId = null;
            $scope.productDetailsList = [];
            $scope.filteredCafeListBrandWise = [];
            $scope.productCategoryBrandWise = [];
            $scope.productSubCategoryBrandWise = [];

            $scope.next = function () {
                $scope.tabIndex++;
                console.log(JSON.stringify($scope.offerDetail));

            };
            $scope.prev = function () {
                if ($scope.tabIndex === 4 && $scope.offerDetail.category === "BILL") {
                    $scope.tabIndex = 3 ;
                    return;
                }
                /*if ($scope.tabIndex === 3 && $scope.editMode) {
                    $scope.tabIndex = 1;
                    return;

                }*/
                if($scope.tabIndex === 1 ){
                    $scope.tabIndex = 0;
                    return;
                }
                if ($scope.tabIndex === 6 && $scope.editMode) {
                    $scope.tabIndex = 4;
                    return;
                }
                if ($scope.tabIndex == 2 ) {
                    $scope.tabIndex = 1;
                    return;
                }
                if ($scope.tabIndex == 3 && $scope.offerDetail.type == "FREEBIE_STRATEGY" && $scope.editMode) {
                    $scope.tabIndex = 2;
                    return;
                }
                if ($scope.tabIndex === 3 && $scope.offerDetail.category == "BILL") {
                    $scope.tabIndex = 0;
                    return;
                }
                if ($scope.tabIndex === 3 && $scope.offerDetail.category == "ITEM") {
                    $scope.tabIndex = 1;
                    return;
                }
                $scope.tabIndex--;
            };
            $scope.nextSubIndex = function () {
                $scope.subIndex++;
                console.log(JSON.stringify($scope.couponDetail));

            };
            $scope.prevSubIndex = function () {
                if ($scope.subIndex === 4 && $scope.offerDetail.category === "BILL") {
                    $scope.subIndex = 0;
                    return;
                } else if ($scope.subIndex === 4 && $scope.offerDetail.category === "ITEM") {
                    if ($scope.selectedProductsLength === 0 && $scope.selectedFreeProductLength === 0) {
                        $scope.subIndex = 0;
                        return;
                    }
                    if ($scope.selectedProductsLength === 0 && $scope.selectedFreeProductLength !== 0) {
                        $scope.subIndex = 3;
                        return;
                    }
                    if ($scope.selectedProductsLength !== 0 && $scope.selectedFreeProductLength === 0) {
                        $scope.subIndex = 2;
                         return;
                    }
                    if ($scope.selectedProductsLength !== 0 && $scope.selectedFreeProductLength !== 0 && $scope.offerDetail.type === "FREEBIE_STRATEGY") {
                        $scope.subIndex = 3;
                        return;
                    }
                    if ($scope.selectedProductsLength !== 0 && $scope.selectedFreeProductLength !== 0 && $scope.offerDetail.type !== "FREEBIE_STRATEGY") {
                        $scope.subIndex = 2;
                        return;
                    }

                } else if ($scope.subIndex === 2) {
                    $scope.subIndex = 0;
                    return;
                }
                $scope.subIndex--;
            };
            
            // $http({
            //     method: 'GET',
            //     url: AppUtil.restUrls.unitMetaData.allUnits + '?category=CAFE'
            // }).then(function success(response) {
            //     $scope.cafelist = response.data;
                
            // }, function error(response) {
            //     console.log("error:" + response);
            // });
            $scope.allCitiesList.forEach(function (city) {
                $scope.cityAllObj[city.id] = false;
                });

            cafelist.forEach(function (unit) {
                $scope.unitAllObj[unit.id] = false;
                $scope.unitAllObj[unit.id] = false;
            });

            $scope.processingForEditMode = function() {
                $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                    if (mapping.type === "UNIT" && mapping.status === "ACTIVE") {
                        cafelist.some(function (cafe, index) {
                            if (mapping.value === cafe.id.toString() && mapping.status === "ACTIVE") {
                                $scope.unitAllObj[cafe.id] = true;
                                $scope.selectUnits(cafe, $scope.unitAllObj[cafe.id]);
                                return true;
                            }
                        })
                    }
                });

                $scope.offerDetail.metaDataMappings.forEach(function (mapping) {
                    if (mapping.status === "ACTIVE")
                        $scope.sel[mapping.code] = true;
                });

                $scope.editOfferMetadataMappings.forEach(function (mapping, index, array) {
                    if (mapping.name === "PRODUCT_SUB_CATEGORY" && mapping.status === "ACTIVE") {
                        $scope.finalSubtype.some(function (subCat, index) {
                            if (mapping.code === subCat.id.toString()) {
                                $scope.subCatObj[subCat.id] = true;
                                $scope.manageSubCategories(subCat, true);
                                return true;
                            }
                        })
                    }
                });

                $scope.editOfferMetadataMappings.forEach(function (mapping, index, array) {
                    if (mapping.name === "PRODUCT_CATEGORY" && mapping.status === "ACTIVE") {
                        $scope.productCategory.some(function (category, index) {
                            if (mapping.code === category.detail.id.toString()) {
                                $scope.sel[category.detail.id] = true;
                                $scope.manageCategories(category, true);
                                return true;
                            }
                        })
                    }
                });

                $scope.editOfferMetadataMappings.forEach(function (mapping, index, array) {
                    if (mapping.name === "ACQUISITION_SOURCE" && mapping.status === "ACTIVE") {
                            if($scope.acquisitionTypes.includes(mapping.code)){
                                $scope.manageAcquisitionSource(mapping.code, true);
                                $scope.source.push(mapping.code);
                                $scope.isSelectedSource(mapping.code);
                            }
                            else{
                            $scope.acquisitionTypes.push(mapping.code);
                            $scope.manageAcquisitionSource(mapping.code, true);
                            $scope.source.push(mapping.code);
                            }
                    }
                });
            }

            $scope.categoryLists = listTypes;
            $scope.productCategory = $scope.categoryLists.CATEGORY;
            $scope.productDimension = $scope.categoryLists.DIMENSION;

            $scope.productCategory.forEach(function (prdChk) {
                $scope.sel[prdChk.detail.id] = false;
            });
            

            /*$scope.allDimension = [];
            $scope.uniqueDimension = [];
            if ($scope.productDimension != undefined) {
                for (var i = 0; i < $scope.productDimension.length; i++) {
                    var dimsLen = $scope.allDimension.push($scope.productDimension[i].content.length);
                    for (var k = 0; k < dimsLen; k++) {
                        if ($scope.productDimension[i].content[k] != undefined) {
                            $scope.uniqueDimension.push($scope.productDimension[i].content[k].name);
                        }
                        ;
                    }
                    $scope.uniqueDimensionNames = [];
                    $.each($scope.uniqueDimension, function (i, el) {
                        if ($.inArray(el, $scope.uniqueDimensionNames) === -1)
                            $scope.uniqueDimensionNames.push(el);
                    });
                }
            }
            $scope.uniqueDimensionNames = $scope.uniqueDimensionNames.map(function (e) {
                return {
                    name: e,
                    checked: false
                };
            });*/

            $scope.finalSubtype = [];

            for (var index in $scope.productCategory) {
                if ($scope.productCategory[index].content[0] != undefined) {
                    var len = $scope.productCategory[index].content.length;
                    for (var i = 0; i < len; i++) {
                        $scope.productListType = $scope.productCategory[index].detail.id;
                        $scope.productListName = $scope.productCategory[index].content[i].name;
                        $scope.productListID = $scope.productCategory[index].content[i].id;
                        $scope.finalSubtype.push({
                            id: $scope.productListID,
                            name: $scope.productListName,
                            type: $scope.productListType
                        });
                    }
                }
                        // console.log("Final Subtypes: " + JSON.stringify($scope.finalSubtype));
            }
            $scope.finalSubCat = $scope.finalSubtype;
            $scope.finalSubtype.forEach(function (subCat) {
                $scope.subCatObj[subCat.id] = false;
            });
            /*if ($scope.editMode) {
                $scope.offerDetail.metaDataMappings.forEach(function (mapping) {
                    $scope.subCatObj[mapping.code] = true;
                });
            }*/

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.families
            }).then(function success(response) {
                $scope.families = response.data;
                console.log("family=", $scope.families);
                $scope.newUnitFamily = $scope.families[0];
                $scope.familyDetailList = [];
                $scope.families.forEach(function (familyDetails) {
                    $scope.familyDetailList.push({
                        'name': familyDetails,
                        'ticked': false
                    })
                });
                $scope.familyDetailList.forEach(function(family){
                    $scope.orderSourceObj[family.name] = false;
                });
                console.log("Order Source Families:",$scope.familyDetailList);
                console.log("Order Source Objects:", $scope.orderSourceObj);
                if ($scope.editMode) {
                    $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                        if (mapping.type === "ORDER_SOURCE" && mapping.status === "ACTIVE") {
                            for (var i = 0; i < $scope.families.length; i++) {
                                if (mapping.value === $scope.families[i]) {
                                    $scope.orderSourceObj[$scope.families[i]] = true;
                                    $scope.selectOrderSource($scope.families[i], true);
                                }
                            }
                        }
                    });
                }
                console.log("families=", $scope.familyDetailList);
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                url: AppUtil.restUrls.channelPartner.getActiveChannelPartners,
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).success(function (data) {
                $scope.channelPartner = data;
                $scope.channelPartner.forEach(function (partner) {
                    $scope.channelPartObj[partner.id] = false;
                });
                console.log("CHP=", $scope.channelPartner);
                $scope.channelDetailList = [];
                $scope.channelPartner.forEach(function (channelDetails) {
                    $scope.channelDetailList.push({
                        'name': channelDetails.name,
                        'ticked': false,
                        id: channelDetails.id.toString()
                    })
                });

                if ($scope.editMode) {
                    $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                        if (mapping.type === "CHANNEL_PARTNER" && mapping.status === "ACTIVE") {
                            $scope.channelPartner.some(function (partner, index) {
                                if (mapping.value === partner.id.toString()) {
                                    $scope.channelPartObj[partner.id] = true;
                                    $scope.selectChannelPartner(partner, true);
                                    return true;
                                }
                            })
                        }
                    });
                }

                console.log("Channel=", $scope.channelDetailList)
            });

            $http({
                url: AppUtil.restUrls.offerManagement.paymentModes,
                method: "POST",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).success(function (data) {
                $scope.paymentMode = data;
                $scope.paymentMode.forEach(function (payMode) {
                    $scope.paymentModeObj[payMode.id] = false;
                });
                $scope.paymentDetailList = [];
                $scope.paymentMode.forEach(function (paymentDetails) {
                    $scope.paymentDetailList.push({
                        'name': paymentDetails.name,
                        'ticked': false,
                        id: paymentDetails.id.toString()
                    })
                });
                console.log("Channel=", $scope.paymentDetailList);

                if ($scope.editMode) {
                    $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                        if (mapping.type === "PAYMENT_MODE" && mapping.status === "ACTIVE") {
                            $scope.paymentMode.some(function (mode, index) {
                                if (mapping.value === mode.id.toString()) {
                                    $scope.paymentModeObj[mode.id] = true;
                                    $scope.selectPaymentModes(mode, true);
                                    return true;
                                }
                            })
                        }
                    });
                }


                console.log("PAYMENT Mode=", $scope.paymentMode);
            });

            $scope.getProductsByBrandId = function(brandId) {
                $scope.productDetailsList = [];
                $scope.filteredCafeListBrandWise = [];
                $scope.productCategoryBrandWise = [];
                $scope.productSubCategoryBrandWise = [];
                $http({
                    url: AppUtil.restUrls.productMetaData.products,
                    params: {'brandId': brandId},
                    dataType: 'json',
                    method: 'POST',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    }
                }).then(function success(response) {
                    $scope.productDetails = response;
                    $scope.currentPage = 1;
                    $scope.entryLimit = 50;
                    $scope.productDetailsList = response.data;
                    $scope.productFilteredList = response.data;
                    $scope.productUnfilteredList = response.data;
                    $scope.filteredItems = $scope.productDetailsList.length;
                    $scope.totalItems = $scope.productDetailsList.length;
                    $scope.selectedProducts = [];
                    $scope.offerDetail.metaDataMappings = [];
                    $scope.selectedProductsLength = 0;
                    
                    if ($scope.editMode && 
                        $scope.currentEditOfferBrandId !== null && 
                        $scope.currentEditOfferBrandId !== editOffer.brandId
                    ) {
                        $scope.editOfferMetadataMappings = [];
                        $scope.editOfferCouponMappings = [];
                        if (editOffer?.isCloning) {
                            $scope.selectedUnitsLength = 0;
                            $scope.selectedCitiesLength = 0;
                            $scope.offerDetail.couponMappingList = [];
                            $scope.offerDetail.metaDataMappings = [];
                        }
                    }

                    if (brandId == 1) {
                        var desiChai = {
                            name: "All Variant Desi Chai ",
                            id: 0
                        };
                        $scope.productDetailsList.push(desiChai);
                    }
                    $scope.productDetailsList.forEach(function (prds) {
                        $scope.prodObj[prds.id] = false;
                        $scope.freeProdObj[prds.id] = false;
                    });

                    $scope.filterListsByBrand();

                    if ($scope.editMode) {
                        $scope.editOfferMetadataMappings.forEach(function (mapping, index, array) {
                            if (mapping.name === "PRODUCT" && mapping.status === "ACTIVE") {
                                $scope.productDetailsList.some(function (product, index) {
                                    if (mapping.code === product.id.toString()) {
                                        $scope.prodObj[product.id] = true;
                                        $scope.manageProducts(product, true);
                                        return true;
                                    }
                                })
                            }
                        });
                        $scope.editOfferMetadataMappings.forEach(function (mapping, index, array) {
                                                if (mapping.name === "FREEBIE_PRODUCT" && mapping.status === "ACTIVE") {
                                                    $scope.productDetailsList.some(function (product, index) {
                                                        if (mapping.code === product.id.toString()) {
                                                            $scope.freeProdObj[product.id] = true;
                                                            $scope.manageFreeProduct(product, true);
                                                            return true;
                                                        }
                                                    })
                                                }
                                            });
                        if ($scope.selectedProductsLength > 0) {
                            $scope.selectedProducts.forEach(function (prod) {
                                $scope.productDimension.forEach(function (dim) {
                                    if (dim.detail.id === prod.dimensionID) {
                                        prod.productDimensions = dim.content;
                                    }
                                });
                                if (prod.dimensionSelectionMap.length === 0) {
                                    prod.dimensionSelectionMap = prod.productDimensions.map(function (value, index, array) {
                                        return {
                                            dimension: value,
                                            checked: false
                                        };
                                    });
                                    // prod.dimensionSelectionMap[0].checked=true;
                                }
                            });
                            console.log($scope.selectedProducts);
                        }
                        if ($scope.selectedFreeProductLength > 0) {
                                                $scope.selectedFreeProduct.forEach(function (prod) {
                                                    $scope.productDimension.forEach(function (dim) {
                                                        if (dim.detail.id === prod.dimensionID) {
                                                            prod.productDimensions = dim.content;
                                                        }
                                                    });
                                                    if (prod.dimensionSelectionMap.length === 0) {
                                                        prod.dimensionSelectionMap = prod.productDimensions.map(function (value, index, array) {
                                                            return {
                                                                dimension: value,
                                                                checked: false
                                                            };
                                                        });
                                                        // prod.dimensionSelectionMap[0].checked=true;
                                                    }
                                                });
                                                console.log($scope.selectedFreeProduct);
                                            }
                        /*$scope.offerDetail.couponMappingList.forEach(function (mapping, index) {
                            if (mapping.type === "PRODUCT") {
                                $scope.selectedProducts.forEach()
                            }
                        });*/
                        for (var i = 0; i < $scope.editOfferCouponMappings.length; i++) {
                            var mapping = $scope.editOfferCouponMappings[i];
                            if (mapping.type === "PRODUCT" && mapping.status === "ACTIVE") {
                                for (var j = 0; j < $scope.selectedProducts.length; j++) {
                                    var product = $scope.selectedProducts[j];
                                    if (mapping.value === product.code.toString()) {
                                        for (var k = 0; k < product.dimensionSelectionMap.length; k++) {
                                            var dimensionMap = product.dimensionSelectionMap[k];
                                            if (dimensionMap.dimension.code === mapping.dimension) {
                                                dimensionMap.checked = true;
                                                $scope.manageProductDimensions(product, dimensionMap, true);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        for (var i = 0; i < $scope.editOfferCouponMappings.length; i++) {
                                                var mapping = $scope.editOfferCouponMappings[i];
                                                if (mapping.type === "FREEBIE_PRODUCT" && mapping.status === "ACTIVE") {
                                                    for (var j = 0; j < $scope.selectedFreeProduct.length; j++) {
                                                        var product = $scope.selectedFreeProduct[j];
                                                        if (mapping.value === product.code.toString()) {
                                                            for (var k = 0; k < product.dimensionSelectionMap.length; k++) {
                                                                var dimensionMap = product.dimensionSelectionMap[k];
                                                                if (dimensionMap.dimension.name === mapping.dimension) {
                                                                    dimensionMap.checked = true;
                                                                    $scope.manageFreeProductDimension(product, dimensionMap, true);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                    }
                    $scope.removeFilter();
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $http({
                method: 'GET',
                url: AppUtil.restUrls.offerManagement.offerAccountsCategories
            }).then(function success(response) {
                $scope.offerAccountsCategories = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                url: AppUtil.restUrls.offerManagement.offerCategory,
                method: "POST",
                dataType: 'json',
                data: '',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).success(function (data) {
                $scope.offerCategorySubTypes = data;
                if (editOffer !== null) {
                    $scope.getSubTypes(editOffer.category);
                }

            });

            $http({
                method: 'POST',
                url: AppUtil.restUrls.offerManagement.marketingPartner,
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                }
            }).then(function success(response) {
                $scope.partnerDetails = response;
                $scope.currentPage = 1; // current page
                $scope.entryLimit = 50; // max no of
                $scope.partnerDetailsList = response.data;
                $scope.partnerDetailsList.forEach(function (prdChk) {
                    $scope.partObj[prdChk.id] = false;

                });
                /*if ($scope.editMode === true) {
                    $scope.offerDetail.partners.forEach(function (partner) {
                        $scope.partObj[partner.id] = true;
                    });
                }*/
                $scope.filteredItems = $scope.partnerDetailsList.length; // Initially
                $scope.totalItems = $scope.partnerDetailsList.length;
                if ($scope.editMode) {
                    $scope.editPartners.forEach(function (partner, index, array) {
                        $scope.partnerDetailsList.some(function (part, index) {
                            if (partner.id === part.id && partner.status === "ACTIVE") {                           // Might add mapping.status === "ACTIVE"
                                $scope.partObj[part.id] = true;
                                $scope.managePartners(part, true);
                                return true;
                            }
                        })
                    });
                }

            }, function error(response) {
                console.log("error:" + response);
            });

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.regions
            }).then(function success(response) {
                $scope.regions = response.data;
                // $scope.regions.forEach(function (region) {
                //     console.log("UnitObj for region,",$scope.unitObjs[region]);
                //     $scope.unitObjs[region] = false;
                // });
                console.log("Regions=", $scope.regions);
                $scope.newUnitRegion = $scope.regions[0];
                $scope.regionDetailList = [];
                $scope.regions.forEach(function (regionDetails) {
                    $scope.regionDetailList.push({
                        'name': regionDetails,
                        'ticked': false
                    })
                });
                $scope.regionDetailList.forEach(function(region){
                    // var newObj={};
                    // newObj[region.name]=region.ticked;
                    $scope.unitObjs[region.name]=false;
                });
                console.log("checkRV=", $scope.regionDetailList);
                console.log("checkUnitObjs=",$scope.unitObjs);
                if ($scope.editMode) {
                    $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                        $scope.regions.some(function (region, index) {
                            if (mapping.type === "UNIT_REGION" && mapping.status === "ACTIVE" && mapping.value === region) {
                                $scope.unitObjs[region] = true;
                                $scope.selectRegions({name:region, ticked:true}, true);
                                return true;
                            }
                        })
                    });
                }
            }, function error(response) {
                console.log("error:" + response);
            });
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allCities
            }).then(function success(response) {
                $scope.allCitiesList = response.data;
                $scope.allCitiesList.forEach(function (unit) {
                    $scope.cityAllObj[unit.id] = false;
                });

                if($scope.editMode) {
                    $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                                        $scope.allCitiesList.forEach(function (city, index) {
                                            if (mapping.type === "CITY" && mapping.status === "ACTIVE" && mapping.value === city.code) {
                                                $scope.cityAllObj[city.id] = true;
                                                $scope.selectCities(city, true);
                                                return true;
                                            }
                                        })
                                    });
                }
                console.log("allCitiesList=", $scope.allCitiesList);
            }, function error(response) {
                console.log("error:" + response);
            })


            $http({
               method: 'GET',
               url: AppUtil.restUrls.unitMetaData.subCategories
            }).then(function success(response) {
               $scope.unitSubCategories = response.data;
               $scope.newUnitSubCategory = ["AIRPORT","CLOUD KITCHEN","EDUCATIONAL","FCC","HIGHWAY","HOSPITAL","METRO","PETROL PUMP"];
               $scope.newUnitSubCategory.forEach(function (subCategory){
                    $scope.unitSubCategories.push(subCategory);
               })
               $scope.unitSubCategoryDetailList = [];
               $scope.unitSubCategories.forEach(function (subCategory) {
                   $scope.unitSubCategoryDetailList.push({
                       'name': subCategory,
                       'ticked': false
                   })
               });
               $scope.unitSubCategoryDetailList.forEach(function(category){
                   $scope.unitObjs[category.name]=false;
               });
               if ($scope.editMode) {
                      $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                         $scope.unitSubCategories.some(function (category, index) {
                           if (mapping.type === "UNIT_SUB_CATEGORY" && mapping.status === "ACTIVE" && mapping.value === category) {
                               $scope.unitObjs[category] = true;
                               $scope.selectUnitSubCategories({name:category, ticked:true}, true);
                               return true;
                           }
                         })
                     });
               }
            }, function error(response) {
               console.log("error:" + response);
            });

            // $scope.offerCategorySubType = [];
            $scope.getSubTypes = function (category) {
                $scope.offerCategorySubType = $scope.offerCategorySubTypes[category];
                if (category === "BILL") {
                    $scope.tempselectedProducts= $scope.selectedProducts;
                    $scope.tempselectedProductsLength = $scope.selectedProductsLength;
                    $scope.tempMetaDataMappings = $scope.offerDetail.metaDataMappings;
                    $scope.offerDetail.metaDataMappings = [];
                    $scope.selectedProducts = [];
                    $scope.selectedProductsLength = 0;
                    // $scope.offerDetail.couponMappingList = [];
                    // $scope.sel = [];
                    // $scope.subCatObj = [];
                    // $scope.prodObj = [];
                    // $scope.unitAllObj = [];
                    // $scope.unitObjs = [];
                    // $scope.orderSourceObj = [];
                    // $scope.channelPartObj = [];
                    // $scope.paymentModeObj = [];
                    $scope.selectedRegionsLength = 0;
                    // $scope.selectedUnitsLength = 0;
                    $scope.selectedOrderSourcesLength = 0;
                    // $scope.selectedChannelPartnersLength = 0;
                    $scope.selectedPaymentModesLength = 0;
                    $scope.newCustomerCheck = false;
                    // if (editOffer?.isCloning === true) {
                    //     $scope.selectedUnitsLength = 0;
                    // }
                }
                else if(category === "ITEM") {
                    $scope.selectedProducts = $scope.tempselectedProducts;
                    $scope.selectedProductsLength = $scope.tempselectedProductsLength;
                    $scope.offerDetail.metaDataMappings = $scope.tempMetaDataMappings;
                }
            };

            $scope.setOfferValueHeading = function (value) {
                if (value === "PERCENTAGE_BILL_STRATEGY" || value === "PERCENTAGE_BILL_MAX_CAP_STRATEGY" ||
                 value === "PERCENTAGE_ITEM_STRATEGY" || value === "PERCENTAGE_ITEM_BOGO_SLICE_STRATEGY" ) {
                    $scope.displayOnlyVal = "Percentage ";
                } else if (value === "FLAT_ITEM_STRATEGY" || value === "FLAT_BILL_STRATEGY") {
                    $scope.displayOnlyVal = "Flat ";
                } else {
                    $scope.displayOnlyVal = "";
                }
            };

            $scope.getUnitData = function (cat) {
                if (cat === 'category') {
                    $scope.unit = 'category';
                } else if (cat === 'subCategory') {
                    $scope.unit = 'subCategory';
                } else if (cat === 'product') {
                    $scope.unit = 'product';
                }
            };

            $scope.getRegionsData = function (unitID) {
                if (unitID == 'regionUnitList') {
                    // $scope.unitObjs = [];
                    $scope.regionUnitList = 'regionUnitList';
                }
            };

            $scope.getUnitSubCategoryData = function (unitID) {
                if (unitID == 'unitSubCategoryList') {
                    $scope.unitSubCategoryList = 'unitSubCategoryList';
                }
            };

            $scope.getPartnerData = function (pt) {
                if (pt == 'partnersList') {
                    $scope.partnerUnit = 'partnersList';
                }
            };

            $scope.getUnitsData = function (unitID) {
                if (unitID == 'UnitList') {
                    // $scope.unitAllObj = [];
                    $scope.UnitList = 'UnitList';
                }
            };
            $scope.getCitiesData = function (unitID) {
                if (unitID == 'cityList') {
                    // $scope.cityAllObj = [];
                    $scope.cityList = 'cityList';
                }
            }

            $scope.getOrderSourceData = function (pt) {
                console.log(pt);
                if (pt == 'orderSourceList') {
                    $scope.orderSourceDatas = 'orderSourceList';
                }
            };

            $scope.getChannelPartnerData = function (cp) {
                if (cp == 'channelPartnerList') {
                    $scope.partnerChannel = 'channelPartnerList';
                    // $scope.channelPartObj = [];
                    // $scope.channelPartner.forEach(function(channelPartnerChk) {
                    // 	$scope.channelPartObj[channelPartnerChk.id] == false;
                    // });
                }
            };

            $scope.getPaymentModeData = function (pm) {
                if (pm == 'paymentModeList') {
                    $scope.paymentModeLists = 'paymentModeList';
                }
            };

            $scope.getNewCustomerModeData = function (nCustomer) {
                if (nCustomer == 'newCustomerModeList') {
                    $scope.newCustomerModeLists = 'newCustomerModeList';
                }
            };

            $scope.addTNC = function () {
                $scope.tncInputs.push('');
            };

            $scope.deleteTNC = function (index) {
                $scope.tncInputs.splice(index, 1);
            };

            $scope.addAcq = function () {
                $scope.acqSources.push('');
            };

            $scope.deleteAcq = function (index) {
                $scope.acqSources.splice(index, 1);
            };

            $scope.addAcqToAcqList = function (){
                for(var i =0;i<$scope.acqSources.length;i++){
                    $scope.acquisitionTypes.push($scope.acqSources[i].toUpperCase());
                }
            }

            $scope.checkFrequencyApplicable = function () {
                if($scope.offerDetail.frequencyApplicable == null || $scope.offerDetail.frequencyApplicable === false){
                    $scope.offerDetail.frequencyCount = null;
                    $scope.offerDetail.applicableHour = null;
                    $scope.offerDetail.dailyFrequencyCount = null;
                }
            };

            $scope.validateOfferFrequency = function () {
                if($scope.offerDetail.frequencyApplicable && $scope.offerDetail.frequencyCount == null){
                    alert("Please enter valid frequency count!");
                    return false;
                }
                if($scope.offerDetail.frequencyApplicable && $scope.offerDetail.applicableHour == null){
                    alert("Please enter valid applicable hour!");
                    return false;
                }
                if($scope.offerDetail.frequencyApplicable && $scope.offerDetail.dailyFrequencyCount == null){
                    alert("Please enter valid Daily Frequency count!");
                    return false;
                }
                if($scope.offerDetail.frequencyCount != null){
                    if($scope.offerDetail.frequencyCount <1 || $scope.offerDetail.frequencyCount >31){
                        alert("Frequency count must be between 1 and 31");
                        return false;
                    }
                }
                if($scope.offerDetail.applicableHour!=null && $scope.offerDetail.applicableHour <1 || $scope.offerDetail.applicableHour >24){
                    alert("Applicable hours must be between 1 and 24");
                    return false;
                }
                if($scope.offerDetail.dailyFrequencyCount!=null && $scope.offerDetail.dailyFrequencyCount <1 || $scope.offerDetail.dailyFrequencyCount >24){
                    alert("Daily Frequency count must be between 1 and 24");
                    return false;
                }
                return true;
            };

            $scope.validateTNC = function () {
                var tncCount = 0;
                var tncLength = $scope.tncInputs.length;
                $scope.offerDetail.termsAndConditions = "";
                for(var i in $scope.tncInputs) {
                    if($scope.tncInputs[i] != null) {
                        $scope.offerDetail.termsAndConditions += $scope.tncInputs[i];
                        if(i != tncLength - 1) {
                            $scope.offerDetail.termsAndConditions += "#";
                        }
                        tncCount += 1;
                    }
                }
                if($scope.offerDetail.termsAndConditions.length > 300 + tncCount || $scope.offerDetail.termsAndConditions.length > 400) {
                    alert("Terms and Conditions should be limited to 300 characters");
                    return false;
                }
                return true;
            };


            $scope.manageCategories = function (category, selection) {
                if (selection === true) {
                    $scope.offerDetail.metaDataMappings.push({
                        id: null,
                        check: selection,
                        value: category.detail.code,
                        categoryName: category.detail.name,
                        name: "PRODUCT_CATEGORY",
                        code: category.detail.id,
                        shortCode: null,
                        type: null,
                        status: "ACTIVE"
                    });
                    $scope.selectedCategoriesLength++;
                } else {

                    /*var flagToInactive = false;
                    $scope.offerDetail.metaDataMappings.forEach(function (mapping, index) {
                        if (mapping.id > 0 && mapping.code === category.detail.id.toString() && mapping.status === "ACTIVE") {
                            $scope.offerDetail.metaDataMappings[index].status = "IN_ACTIVE";
                            $scope.selectedCategoriesLength--;
                            flagToInactive = true;
                        }
                    });
                    if (flagToInactive)
                        return;


                    var indexOfCategory = $scope.offerDetail.metaDataMappings.indexOf(category);
                    $scope.offerDetail.metaDataMappings.splice(indexOfCategory, 1);
                    $scope.selectedCategoriesLength--;*/
                    $scope.offerDetail.metaDataMappings.forEach(function (mapping, index, array) {
                        if (mapping.name === "PRODUCT_CATEGORY" && mapping.categoryName === category.detail.name) {
                            array.splice(index, 1);
                            $scope.selectedCategoriesLength--;
                        }
                    });
                }
                console.log($scope.offerDetail.metaDataMappings);
            };

            $scope.allAcquisitionSources = function (check) {
                $scope.acquisitionTypes.forEach(function (src) {
                        if(check === true){
                            $scope.source.push(src);
                        }
                        if(check === false){
                            $scope.source.forEach(function (val,i,arr){
                                 if(val === src){
                                       arr.splice(i,1);
                                  }
                            });
                        }
                        $scope.source[src] = check;
                        $scope.manageAcquisitionSource(src, check);
               });
            };

            $scope.manageAcquisitionSource = function (src, selection) {
                     if (selection === true) {
                        $scope.offerDetail.metaDataMappings.push({
                               id: null,
                               check: selection,
                               value: src,
                               categoryName: src,
                               name: "ACQUISITION_SOURCE",
                               code: src,
                               shortCode: null,
                               type: null,
                               status: "ACTIVE"
                        });
                     } else {
                     $scope.offerDetail.metaDataMappings.map(function (mapping, index, array) {
                         if (mapping.name === "ACQUISITION_SOURCE" && mapping.categoryName === src) {
                             array.splice(index, 1);
                             $scope.source.forEach(function (val,i,arr){
                                if(val === mapping.categoryName){
                                    arr.splice(i,1);
                                }
                             });
                             document.getElementById(src).checked=false;
                         }
                     });
                 }
                 console.log($scope.offerDetail.metaDataMappings);
            };

            $scope.isSelectedSource =  function(src){
                if($scope.source.includes(src)){
                    document.getElementById(src).checked=true;
                    return true;
                }
            }


            $scope.manageSubCategories = function (subCategory, selection) {
                var newSubCategory = {
                    id: subCategory.id,
                    check: selection,
                    subCategoryName: subCategory.name,
                    name: "PRODUCT_SUB_CATEGORY",
                    code: subCategory.id,
                    value: subCategory.name,
                    shortCode: null,
                    type: null,
                    status: "ACTIVE"
                };
                if ($scope.editMode)
                    newSubCategory.id = 0
                if (selection === true) {
                    $scope.offerDetail.metaDataMappings.push(newSubCategory);
                    $scope.selectedSubCategoriesLength++;
                } else {
                    /* var flagToInactive = false;
                     $scope.offerDetail.metaDataMappings.forEach(function (mapping, index) {
                         if (mapping.id > 0 && mapping.code === subCategory.id.toString() && mapping.status === "ACTIVE") {
                             $scope.offerDetail.metaDataMappings[index].status = "IN_ACTIVE";
                             $scope.selectedSubCategoriesLength--;
                             flagToInactive = true;
                         }
                     });
                     if (flagToInactive)
                         return;

                     var indexOfSubCategory = $scope.offerDetail.metaDataMappings.indexOf(subCategory);
                     $scope.offerDetail.metaDataMappings.splice(indexOfSubCategory, 1);
                     $scope.selectedSubCategoriesLength--;*/
                    $scope.offerDetail.metaDataMappings.forEach(function (mapping, index, array) {
                        if (mapping.name === "PRODUCT_SUB_CATEGORY" && mapping.subCategoryName === subCategory.name) {
                            array.splice(index, 1);
                            $scope.selectedSubCategoriesLength--;
                        }
                    });
                }
            };


            function manageChaayosProduct(product, selection){

                console.log(product);
                var newProduct = {
                    id: null,
                    check: selection,
                    productName: product.name,
                    value: product.name,
                    type: product.type,
                    subtype: product.subType,
                    name: "PRODUCT",
                    code: product.id,
                    shortCode: null,
                    status: "ACTIVE",
                    dimensionID: product.dimensionProfileId,
                    productDimensions: [],
                    dimensionSelectionMap: []
                };
//                if ($scope.editMode)
//                    newProduct.id = 0;
                if (selection === true) {
                    $scope.offerDetail.metaDataMappings.push(newProduct);
                    $scope.selectedProducts.push(newProduct);
                    $scope.selectedProductsLength++;
                } else {
                    var indexOfProduct = -1;
                    for (var i = 0; i < $scope.offerDetail.metaDataMappings.length; i++) {
                        prod = $scope.offerDetail.metaDataMappings[i];
                        if (product.name === prod.productName) {
                            indexOfProduct = i;
                            break;
                        }
                    }
                    if (indexOfProduct !== -1)
                        $scope.offerDetail.metaDataMappings.splice(indexOfProduct, 1);
                    indexOfProduct = -1;
                    for (var j = 0; j < $scope.selectedProducts.length; j++) {
                        prod = $scope.selectedProducts[j];
                        if (product.name === prod.productName) {
                            indexOfProduct = j;
                            break;
                        }
                    }
                    if (indexOfProduct !== -1)
                        $scope.selectedProducts.splice(indexOfProduct, 1);
                    $scope.selectedProductsLength--;
                    var len = $scope.offerDetail.couponMappingList.length;
                    for (var k = 0; k < $scope.offerDetail.couponMappingList.length; k++) {
                        var mapping = $scope.offerDetail.couponMappingList[k];
                        if (mapping.type === "PRODUCT" && mapping.name === product.name) {
                            $scope.offerDetail.couponMappingList.splice(k, 1);
                            k--;
                        }
                    }
                    /*$scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "PRODUCT" && mapping.name === product.name) {
                            array.splice(index, 1);
                        }
                    });*/
                }
                console.log($scope.offerDetail.metaDataMappings);
                console.log($scope.offerDetail.couponMappingList);
            }

            $scope.manageProducts = function (product, selection) {
                if(product.id===0){
                    var desiChaiVariant = [10, 11, 12, 14, 15, 50, 1375];
                    for(var i = 0; i < desiChaiVariant.length; i++){
                        for(var j=0;j<$scope.productDetailsList.length;j++){
                            if($scope.productDetailsList[j].id===desiChaiVariant[i]){
                                manageChaayosProduct($scope.productDetailsList[j],selection);
                            }
                        }
                    }
                }
                else {
                    manageChaayosProduct(product, selection);
                }
            }

            $scope.manageFreeProduct = function (product, selection) {
                            if(product.id===0){
                                var desiChaiVariant = [10, 11, 12, 14, 15, 50, 1375];
                                for(var i = 0; i < desiChaiVariant.length; i++){
                                    for(var j=0;j<$scope.productDetailsList.length;j++){
                                        if($scope.productDetailsList[j].id===desiChaiVariant[i]){
                                            manageChaayosFreeProduct($scope.productDetailsList[j],selection);
                                        }
                                    }
                                }
                            }
                            else {
                                manageChaayosFreeProduct(product, selection);
                            }
            }

             $scope.applyFilter = function(){
                  $scope.catId = null;
                  $scope.subCatId = null;
                  $scope.prodByCatAndSubCat = [];
                  $scope.productByCategory = [];
                  $scope.productBySubCategory = [];
                   $scope.productDetailsList=$scope.productUnfilteredList;
                  $scope.productCategoryBrandWise.forEach(function (productCategory) {
                        if(productCategory.detail.name === $scope.offerDetail.filterCategory){
                            $scope.catId = productCategory.detail.id;
                        }
                        productCategory.content.forEach(function (productSub){
                            if(productSub.name === $scope.offerDetail.filterSubCategory){
                               $scope.subCatId = productSub.id;
                            }
                        });
                  });
                  $scope.productDetailsList.forEach(function (prod) {
                        if(prod.type === $scope.catId && prod.subType === $scope.subCatId){
                            $scope.prodByCatAndSubCat.push(prod);
                        }
                        if(prod.type === $scope.catId && $scope.subCatId === null){
                            $scope.productByCategory.push(prod);
                        }
                        if(prod.subType === $scope.subCatId &&  $scope.catId === null){
                            $scope.productBySubCategory.push(prod);
                        }
                  });
                  if($scope.catId !== null && $scope.subCatId !== null){
                        $scope.productDetailsList = $scope.prodByCatAndSubCat;
                  }
                  else if($scope.catId !== null && $scope.subCatId === null){
                        $scope.productDetailsList = $scope.productByCategory;
                  }
                  else if($scope.subCatId !== null && $scope.catId === null ){
                        $scope.productDetailsList = $scope.productBySubCategory;
                  }
                  else{
                        $scope.productDetailsList = $scope.productUnfilteredList;
                  }
             }

             $scope.removeFilter = function(){
                $scope.offerDetail.filterCategory = null;
                $scope.offerDetail.filterSubCategory = null;
                $scope.productDetailsList = $scope.productUnfilteredList;
                // $scope.finalSubtype = $scope.finalSubCat;
                $scope.isSelected = false;
                $scope.hideFilter = true;
                // $scope.filterListsByBrand();
                $scope.selectedBrandId = null;
             }

             $scope.selected = function(){
                $scope.isSelected = true;
                $scope.hideFilter = false;
             }

             $scope.categoryChanged = function(){
                $scope.getSubCatAccToCat = [];
                $scope.offerDetail.filterSubCategory = null;
             }

             $scope.getSubCategory = function(){
                $scope.getSubCatAccToCat = [];
                $scope.productCategoryBrandWise.forEach(function (productCategory) {
                      if(productCategory.detail.name === $scope.offerDetail.filterCategory){
                          productCategory.content.forEach(function (productSub){
                                    $scope.getSubCatAccToCat.push({
                                      id : productSub.id,
                                      name : productSub.name,
                                      type : productSub.type
                                    });
                          });
                      }
                });
                if($scope.offerDetail.filterCategory === null){
                    $scope.productSubCategoryBrandWise = $scope.finalSubCat;
                }
                else{
                    $scope.productSubCategoryBrandWise = $scope.getSubCatAccToCat;
                }
             }

            function manageChaayosFreeProduct (product, selection) {
                console.log(product);
                var newProduct = {
                    id: null,
                    check: selection,
                    productName: product.name,
                    value: product.name,
                    type: product.type,
                    subtype: product.subType,
                    name: "FREEBIE_PRODUCT",
                    code: product.id,
                    shortCode: null,
                    status: "ACTIVE",
                    dimensionID: product.dimensionProfileId,
                    productDimensions: [],
                    dimensionSelectionMap: []
                };
//                if ($scope.editMode)
//                    newProduct.id = 0;
                if (selection === true) {
                    $scope.offerDetail.metaDataMappings.push(newProduct);
                    $scope.selectedFreeProduct.push(newProduct);
                    $scope.selectedFreeProductLength++;
                } else {
                    var indexOfProduct = -1;
                    for (var i = 0; i < $scope.offerDetail.metaDataMappings.length; i++) {
                        prod = $scope.offerDetail.metaDataMappings[i];
                        if (product.name === prod.productName) {
                            indexOfProduct = i;
                            break;
                        }
                    }
                    if (indexOfProduct !== -1)
                        $scope.offerDetail.metaDataMappings.splice(indexOfProduct, 1);
                    indexOfProduct = -1;
                    for (var j = 0; j < $scope.selectedFreeProduct.length; j++) {
                        prod = $scope.selectedFreeProduct[j];
                        if (product.name === prod.productName) {
                            indexOfProduct = j;
                            break;
                        }
                    }
                    if (indexOfProduct !== -1)
                        $scope.selectedFreeProduct.splice(indexOfProduct, 1);
                    $scope.selectedFreeProductLength--;
                    var len = $scope.offerDetail.couponMappingList.length;
                    for (var k = 0; k < $scope.offerDetail.couponMappingList.length; k++) {
                        var mapping = $scope.offerDetail.couponMappingList[k];
                        if (mapping.type === "FREEBIE_PRODUCT" && mapping.name === product.name) {
                            $scope.offerDetail.couponMappingList.splice(k, 1);
                            k--;
                        }
                    }
                    /*$scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "PRODUCT" && mapping.name === product.name) {
                            array.splice(index, 1);
                        }
                    });*/
                }
                console.log($scope.offerDetail.metaDataMappings);
                console.log($scope.offerDetail.couponMappingList);
            }


            $scope.managePartners = function (partner, selection) {

                if (selection === true) {
                    $scope.offerDetail.partners.push(partner);
                } else {
                    var indexOfPartner = $scope.offerDetail.partners.indexOf(partner);
                    $scope.offerDetail.partners.splice(indexOfPartner, 1);
                }
            };

            $scope.managePaymentModes = function (mode, selection) {

                if (selection === true) {
                    $scope.offerDetail.paymentModes.push(mode);
                } else {
                    var indexOfPartner = $scope.offerDetail.paymentModes.indexOf(mode);
                    $scope.offerDetail.paymentModes.splice(indexOfPartner, 1);
                }
            };

            $scope.selectedRegionsLength = 0;
            $scope.selectedUnitsLength = 0;
            $scope.selectedCitiesLength = 0;
            $scope.selectRegions = function (region, selection) {
                if (selection === true) {
                    $scope.offerDetail.couponMappingList.push({
                        id: null,
                        check:selection,
                        dimension: null,
                        type: "UNIT_REGION",
                        name: region.name,
                        value: region.name,
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    });
                    $scope.selectedRegionsLength++;
                } else {
                    // var indexOfRegion = $scope.offerDetail.couponMappingList.indexOf(region);
                    // $scope.offerDetail.couponMappingList.splice(indexOfRegion, 1);
                    // $scope.selectedRegionsLength--;
                    $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "UNIT_REGION" && mapping.name === region.name) {
                            array.splice(index, 1);
                            $scope.selectedRegionsLength--;
                        }
                    });
                }
            };

            $scope.selectedUnitSubCategoryLength = 0;
            $scope.selectUnitSubCategories = function (category, selection) {
                if (selection === true) {
                   $scope.offerDetail.couponMappingList.push({
                       id: null,
                       check:selection,
                       dimension: null,
                       type: "UNIT_SUB_CATEGORY",
                       name: category.name,
                       value: category.name,
                       dataType: "java.lang.String",
                       minValue: 1,
                       "group": 1,
                       status: 'ACTIVE'
                   });
                   $scope.selectedUnitSubCategoryLength++;
                } else {
                   $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "UNIT_SUB_CATEGORY" && mapping.name === category.name) {
                            array.splice(index, 1);
                            $scope.selectedUnitSubCategoryLength--;
                        }
                   });
                }
            };

            $scope.selectUnits = function (unit, selection) {
                var newUnit = {
                    id: unit.id,
                    check:selection,
                    dimension: null,
                    type: "UNIT",
                    value: unit.id,
                    dataType: "java.lang.String",
                    minValue: 1,
                    name: unit.name,
                    "group": 1,
                    region: unit.region,
                    status: 'ACTIVE'
                };
                if ($scope.editMode)
                    newUnit.id = 0;
                if (selection === true) {
                    $scope.offerDetail.couponMappingList.push(newUnit);
                    $scope.selectedUnitsLength++;
                } else {
                    // var indexOfUnit = -1;
                    // $scope.offerDetail.couponMappingList.indexOf(unit);
                    $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "UNIT" && mapping.value === unit.id) {
                            array.splice(index, 1);
                            $scope.selectedUnitsLength--;
                        }
                    });
                    // if (indexOfUnit !== -1){
                    //     $scope.offerDetail.couponMappingList.splice(indexOfUnit, 1);

                    // }
                }
//                console.log($scope.offerDetail.couponMappingList);
            };
            $scope.selectCities = function (city, selection) {
                console.log("City selected: " + city.id);
                var newUnit = {
                    id: city.id,
                    check:selection,
                    dimension: null,
                    type: "CITY",
                    value: city.code,
                    dataType: "java.lang.String",
                    minValue: 1,
                    name: city.name,
                    "group": 1,
                    status: 'ACTIVE'
                };
                if ($scope.editMode)
                    newUnit.id = 0;
                if (selection === true) {
                    $scope.offerDetail.couponMappingList.push(newUnit);
                    $scope.selectedCitiesLength++;
                } else {
                    // var indexOfUnit = -1;
                    // $scope.offerDetail.couponMappingList.indexOf(unit);
                    $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "CITY" && mapping.value === city.code) {
                            array.splice(index, 1);
                            $scope.selectedCitiesLength--;
                        }
                    });
                    // if (indexOfUnit !== -1){
                    //     $scope.offerDetail.couponMappingList.splice(indexOfUnit, 1);

                    // }
                }
//                console.log($scope.offerDetail.couponMappingList);
            };


            $scope.selectedOrderSourcesLength = 0;
            $scope.selectOrderSource = function (ordSource, selection) {
                if (selection === true) {
                    $scope.offerDetail.couponMappingList.push({
                        id: 0,
                        name: ordSource,
                        dimension: null,
                        type: "ORDER_SOURCE",
                        value: ordSource,
                        dataType: "java.lang.String",
                        minValue: 1,
                        group: 1,
                        status: 'ACTIVE'
                    });
                    $scope.selectedOrderSourcesLength++;
                } else {
                    /*var indexOfSource = $scope.offerDetail.couponMappingList.indexOf(ordSource);
                    $scope.offerDetail.couponMappingList.splice(indexOfSource, 1);
                    $scope.selectedOrderSourcesLength--;*/
                    $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "ORDER_SOURCE" && mapping.name === ordSource) {
                            array.splice(index, 1);
                            $scope.selectedOrderSourcesLength--;
                        }
                    });
                }
            };

            $scope.selectedChannelPartnersLength = 0;
            $scope.selectChannelPartner = function (partner, selection) {
                if (selection === true) {
                    $scope.offerDetail.couponMappingList.push({
                        id: 0,
                        dimension: null,
                        name: partner.name,
                        type: "CHANNEL_PARTNER",
                        value: partner.id,
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    });
                    $scope.selectedChannelPartnersLength++;
                } else {
                    /*var indexOfChannelPartner = $scope.offerDetail.couponMappingList.indexOf(partner);
                    $scope.offerDetail.couponMappingList.splice(indexOfChannelPartner, 1);
                    $scope.selectedChannelPartnersLength--;*/
                    $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "CHANNEL_PARTNER" && mapping.name === partner.name) {
                            array.splice(index, 1);
                            $scope.selectedChannelPartnersLength--;
                        }
                    });
                }
            };

            $scope.selectedPaymentModesLength = 0;
            $scope.selectPaymentModes = function (mode, selection) {
                if (selection === true) {

                    $scope.offerDetail.couponMappingList.push({
                        id: 0,
                        check:selection,
                        name: mode.name,
                        dimension: null,
                        type: "PAYMENT_MODE",
                        value: mode.id,
                        dataType: "java.lang.String",
                        minValue: 1,
                        "group": 1,
                        status: 'ACTIVE'
                    });
                    console.log("Payment Mode added: " + mode.name);
                    $scope.selectedPaymentModesLength++;
                } else {
                    /*var indexOfPaymentMode = $scope.offerDetail.couponMappingList.indexOf(mode);
                    $scope.offerDetail.couponMappingList.splice(indexOfPaymentMode, 1);
                    $scope.selectedPaymentModesLength--;*/
                    $scope.offerDetail.couponMappingList.forEach(function (mapping, index, array) {
                        if (mapping.type === "PAYMENT_MODE" && mapping.name === mode.name) {
                            array.splice(index, 1);
                            $scope.selectedPaymentModesLength--;
                        }
                    });
                }
            };

            $scope.manageNewCustomer = function (newCustomerCheck) {
                var NewCustomer = {
                    id: 0,
                    type: "NEW_CUSTOMER",
                    name: 'Yes',
                    dimension: null,
                    value: 'Y',
                    dataType: "java.lang.String",
                    minValue: 1,
                    "group": 1,
                    status: 'ACTIVE'
                };
                if (newCustomerCheck === true) {
                    $scope.offerDetail.couponMappingList.push(NewCustomer);
                } else {
                    var indexOfNewCustomer = -1;
                    var indexOfAcq = -1;
                    $scope.offerDetail.couponMappingList.some(function (mapping, index) {
                        if (mapping.type === "NEW_CUSTOMER") {
                            indexOfNewCustomer = index;
                            return true;
                        }
                    });
                    $scope.offerDetail.metaDataMappings.map(function (mapping, index) {
                        if (mapping.name === "ACQUISITION_SOURCE") {
                            $scope.offerDetail.metaDataMappings.splice(index);
                        }
                    });
                    if (indexOfNewCustomer !== -1)
                        $scope.offerDetail.couponMappingList.splice(indexOfNewCustomer, 1);
                }
                console.log($scope.offerDetail.couponMappingList);
            };

            $scope.checkCouponsAvail = function (id, prefixData) {
                $scope.prefixCoupons = prefixData;
                if (id != undefined) {
                    $scope.prefixCouponsDetail = prefixData + id;
                    $scope.prefixCouponsDetail = $scope.prefixCouponsDetail.toUpperCase()
                    $scope.loading = true
                    $scope.disableCouponNextButton = true;
                    // return;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.offerManagement.couponAvailablity,
                        data: $scope.prefixCouponsDetail
                    }).then(function success(response) {
                        $scope.disableCouponNextButton = false;
                        if (response.data === true) {
                            $scope.coupon = response.data;
                            $scope.loading = false;
                            $scope.couponsAvail = "Available";
                            $scope.color = "green";
                        } else {
                            $scope.coupon = response.data;
                            $scope.loading = false;
                            $scope.couponsAvail = "Not Available";
                            $scope.color = "red";
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                    console.log($scope.couponsAvail);
                }
            };

            $scope.showCustomerClk = function (custDet) {
                if (custDet == "phoneNo") {
                    $("#dvPhoneNoCust").show();
                    $("#dvCustIDCust").hide();
                }
                if (custDet == "custID") {
                    $("#dvCustIDCust").show();
                    $("#dvPhoneNoCust").hide();
                }
                console.log(custDet);
            };

            $scope.$watch('fileContent', function (newVal, oldVal) {
                $scope.fileContent = newVal;
            });

            $scope.validateOfferTab = function () {
                if ($scope.offerDetail.accountsCategory === null) {
                    alert("Please select a category!");
                    return;
                }
                if ($scope.offerDetail.category === null) {
                    alert("Please select offer type!");
                    return;
                }
                if ($scope.offerDetail.type === null) {
                    alert("Please select auto applicable for unit");
                    return;
                }
                if ($scope.offerDetail.brandId === null) {
                    alert("Please select Brand ID");
                    return;
                }
                if ($scope.offerDetail.autoApplicableforUnit === null) {
                    alert("Please select whether offer is auto applicable for unit");
                    return;
                }
                if (typeof $scope.offerDetail.text === "undefined") {
                    alert("Please enter offer reason!");
                    return;
                }
                if (typeof $scope.offerDetail.description === "undefined") {
                    alert("Please enter offer description!");
                    return;
                }
                if ($scope.offerDetail.startDate === null) {
                    alert("Please select a start date for the offer!");
                    return;
                }
                if ($scope.offerDetail.endDate === null) {
                    alert("Please select an end date for the offer!");
                    return;
                }
                if ($scope.offerDetail.endDate < $scope.offerDetail.startDate) {
                    alert("Please ensure endDate is greater than ::" + $scope.offerDetail.startDate);
                    return;
                }
                if ($scope.offerDetail.status === null) {
                    alert("Please select an offer status!");
                    return;
                }
                if ($scope.offerDetail.minValue === null) {
                    alert("Please select minimum bill value!");
                    return;
                }
                if ($scope.offerDetail.validateCustomer === null) {
                    alert("Please select validate customer!");
                    return;
                }
                if ($scope.offerDetail.removeLoyalty === null) {
                    alert("Please select remove loyalty!");
                    return;
                }
                if ($scope.offerDetail.offerScope === null) {
                    alert("Please select offer scope!");
                    return;
                }
                if ($scope.offerDetail.offerScope === 'CORPORATE' && $scope.offerDetail.emailDomain === null) {
                    alert("Please enter email domain!");
                    return;
                }
                if ($scope.offerDetail.minItemCount === null) {
                    alert("Please select minimum item count!");
                    return;
                }
                if ($scope.offerDetail.minQuantity === null) {
                    alert("Please select min quantity limit!");
                    return;
                }
                if ($scope.offerDetail.offerValue === null) {
                    alert("Please select offer value!");
                    return;
                }
                if ($scope.offerDetail.type === 'PERCENTAGE_BILL_MAX_CAP_STRATEGY' && $scope.offerDetail.maxDiscountAmount === null) {
                    alert("Please enter max capped discount!");
                    return;
                }
                if ($scope.offerDetail.otpRequired === null) {
                    alert("Please select OTP required!");
                    return;
                }
                if(!$scope.validateOfferFrequency()){
                    return;
                }
                if(!$scope.validateTNC()) {
                    return;
                }

                if ($scope.offerDetail.category === 'BILL') {
                    /*if ($scope.editMode === true)
                        $scope.tabIndex = 3;
                    else*/
                    $scope.tabIndex = 3;
                } else {
                    $scope.tabIndex = 1;
                }

            };

            $scope.validateOfferMappingTab = function () {
                if ($scope.selectedCategoriesLength === 0 && $scope.selectedSubCategoriesLength === 0
                    && $scope.selectedProductsLength === 0) {
                    alert("Please select at least one category/ sub-category/ product!");
                    return;
                }
                // if ($scope.editMode) {
                //     $scope.tabIndex = 3;
                // } else {
                if($scope.offerDetail.type == "FREEBIE_STRATEGY" && $scope.offerDetail.category == "ITEM"){
                    $scope.tabIndex = 2;
                    $scope.subIndex = 0;
                }
                else{
                  $scope.tabIndex = 3;
                  $scope.subIndex = 0;
                }
                // }
            };

            $scope.validateFreeProductMappingTab = function () {
                 if($scope.selectedFreeProductLength <= 0){
                    alert("Select atleast one product");
                    return;
                 }
                 $scope.tabIndex = 3;
            };

            $scope.validateUnitSubTab = function () {
                // if ($scope.selectedUnitsLength === 0 && $scope.selectedRegionsLength === 0) {
                //     alert("Please select at least one region/ unit!");
                //     return;
                // }
                if ($scope.selectedProductsLength > 0) {

                    console.log($scope.selectedProducts);
                    $scope.selectedProducts.forEach(function (prod) {

                        $scope.productDimension.forEach(function (dim) {
                            if (dim.detail.id === prod.dimensionID) {
                                prod.productDimensions = dim.content;
                            }
                        });
                        if (prod.dimensionSelectionMap.length === 0) {
                            prod.dimensionSelectionMap = prod.productDimensions.map(function (value, index, array) {
                                return {
                                    dimension: value,
                                    checked: false
                                };
                            });
                            if(prod.id!=0){
                                prod.dimensionSelectionMap[0].checked=true;
                            }
                            $scope.manageProductDimensions(prod,prod.dimensionSelectionMap[0],true);
                        }
                    });
                    console.log($scope.selectedProducts);
                    $scope.subIndex = 2;
                }
                if ($scope.selectedFreeProductLength > 0) {

                                    console.log($scope.selectedFreeProduct);
                                    $scope.selectedFreeProduct.forEach(function (prod) {

                                        $scope.productDimension.forEach(function (dim) {
                                            if (dim.detail.id === prod.dimensionID) {
                                                prod.productDimensions = dim.content;
                                            }
                                        });
                                        if (prod.dimensionSelectionMap.length === 0) {
                                            prod.dimensionSelectionMap = prod.productDimensions.map(function (value, index, array) {
                                                return {
                                                    dimension: value,
                                                    checked: false
                                                };
                                            });
                                            if(prod.id!=0){
                                                prod.dimensionSelectionMap[0].checked=true;
                                            }
                                            $scope.manageFreeProductDimension(prod,prod.dimensionSelectionMap[0],true);
                                        }
                                    });
                                    console.log($scope.selectedFreeProduct);
                                    $scope.subIndex = 2;
                                }
                else
                    $scope.subIndex = 2;

                if($scope.offerDetail.category === "BILL"){
                    $scope.subIndex = 4;
                    console.log("offerDetail ::::::: ", $scope.offerDetail);
                    $scope.tempselectedProducts= $scope.selectedProducts;
                    $scope.tempselectedProductsLength = $scope.selectedProductsLength;
                    $scope.tempMetaDataMappings = $scope.offerDetail.metaDataMappings;
                    $scope.offerDetail.metaDataMappings = [];
                    $scope.selectedProducts = [];
                    $scope.selectedProductsLength = 0;
                }
            };

            $scope.validateCustomerSubTab = function () {
                if ($scope.selectedProductsLength > 0) {
                    $scope.selectedProducts.forEach(function (prod) {
                        $scope.productDimension.forEach(function (dim) {
                            if (dim.detail.id === prod.dimensionID) {
                                prod.productDimensions = dim.content;
                            }
                        });
                        prod.dimensionSelectionMap = prod.productDimensions.map(function (e) {
                            return {
                                dimension: e,
                                checked: false
                            };
                        });
                        // prod.dimensionSelectionMap[0].checked=true;
                    });

                    console.log($scope.selectedProducts);
                    $scope.subIndex = 2;
                } else
                    $scope.subIndex = 3;
            };

            $scope.manageProductDimensions = function (product, dimensionList, selection) {
                var productDimension = {
                    id: null,
                    name: product.productName,
                    type: "PRODUCT",
                    dimension: dimensionList.dimension.code,
                    value: product.code,
                    dataType: "java.lang.String",
                    minValue: 1,
                    "group": 1,
                    status: 'ACTIVE'
                };
                if (selection === true) {
                    $scope.offerDetail.couponMappingList.push(productDimension);
                } else {
                    var indexOfProductDimension = -1;
                    for (var i = 0; i < $scope.offerDetail.couponMappingList.length; i++) {
                        if ($scope.offerDetail.couponMappingList[i].type === productDimension.type
                            && $scope.offerDetail.couponMappingList[i].dimension === productDimension.dimension
                            && $scope.offerDetail.couponMappingList[i].value === productDimension.value) {
                            indexOfProductDimension = i;
                            break;
                        }
                    }
                    if (indexOfProductDimension !== -1)
                        $scope.offerDetail.couponMappingList.splice(indexOfProductDimension, 1);
                }

            };

            $scope.manageFreeProductDimension = function (product, dimensionList, selection) {
                            var productDimension = {
                                id: null,
                                name: product.productName,
                                type: "FREEBIE_PRODUCT",
                                dimension: dimensionList.dimension.name,
                                value: product.code,
                                dataType: "java.lang.String",
                                minValue: 1,
                                "group": 1,
                                status: 'ACTIVE'
                            };
                            if (selection === true) {
                                $scope.offerDetail.couponMappingList.push(productDimension);
                            } else {
                                var indexOfProductDimension = -1;
                                for (var i = 0; i < $scope.offerDetail.couponMappingList.length; i++) {
                                    if ($scope.offerDetail.couponMappingList[i].type === productDimension.type
                                        && $scope.offerDetail.couponMappingList[i].dimension === productDimension.dimension
                                        && $scope.offerDetail.couponMappingList[i].value === productDimension.value) {
                                        indexOfProductDimension = i;
                                        break;
                                    }
                                }
                                if (indexOfProductDimension !== -1)
                                    $scope.offerDetail.couponMappingList.splice(indexOfProductDimension, 1);
                            }

                        };

            $scope.validateProductSubTab = function () {
                var nextTab = true;
                for (var i = 0; i < $scope.selectedProducts.length; i++) {
                    var atLeastOneDimensionSelected = false;
                    var product = $scope.selectedProducts[i];
                    for (var j = 0; j < product.dimensionSelectionMap.length; j++) {
                        var value = product.dimensionSelectionMap[j];
                        if (value.checked === true) {
                            atLeastOneDimensionSelected = true;
                        }
                    }
                    if (!atLeastOneDimensionSelected) {
                        nextTab = false;
                        alert("Select dimensions for product " + product.productName);
                        break;
                    }
                }
                if (nextTab){
                    if($scope.selectedFreeProductLength > 0 && $scope.offerDetail.type=="FREEBIE_STRATEGY"){
                        $scope.subIndex = 3;
                    }
                    else{
                        $scope.subIndex = 4;
                    }
                }
            };

            $scope.validateFreeProductSubTab = function () {
                            var nextTab = true;
                            for (var i = 0; i < $scope.selectedFreeProduct.length; i++) {
                                var atLeastOneDimensionSelected = false;
                                var product = $scope.selectedFreeProduct[i];
                                for (var j = 0; j < product.dimensionSelectionMap.length; j++) {
                                    var value = product.dimensionSelectionMap[j];
                                    if (value.checked === true) {
                                        atLeastOneDimensionSelected = true;
                                    }
                                }
                                if (!atLeastOneDimensionSelected) {
                                    nextTab = false;
                                    alert("Select dimensions for product " + product.productName);
                                    break;
                                }
                            }
                            if (nextTab)
                                $scope.subIndex = 4;
                        };

            $scope.validateOrderSourceSubTab = function () {
                // if ($scope.selectedOrderSourcesLength === 0) {
                //     alert("Please select at least one order source!");
                //     return;
                // }
                $scope.subIndex = 5;
            };

            $scope.validateChannelPartnersSubTab = function () {
                // if ($scope.selectedChannelPartnersLength === 0) {
                //     alert("Please select at least one channel partner!");
                //     return
                // }
                $scope.subIndex = 6;
            };

            $scope.validatePaymentModeSubTab = function () {
                // if ($scope.selectedPaymentModesLength === 0) {
                //     alert("Please select at least one payment mode!");
                //     return;
                // }
                $scope.subIndex = 7;
            };

            $scope.isLoyaltyPointsBurnOffer = function () {
                if($scope.offerDetail.loyaltyBurnPoints != null && $scope.offerDetail.loyaltyBurnPoints > 0){
                    return true;
                }
                return false;
            }

            $scope.validateNewCustomerSubTab = function () {
                $scope.tabIndex = 4;
            };

            $scope.validatePartnersTab = function () {
                console.log($scope.offerDetail);

                // if ($scope.offerDetail.partners.length === 0) {
                //     alert("Please select at least one partner");
                //     return;
                // }
                if ($scope.couponDetail.status === null) {
                    $scope.couponDetail.status = $scope.offerDetail.status;
                }
                if ($scope.couponDetail.startDate === null) {
                    $scope.couponDetail.startDate = $scope.offerDetail.startDate;
                }
                if ($scope.couponDetail.endDate === null) {
                    $scope.couponDetail.endDate = $scope.offerDetail.endDate;
                }
                if ($scope.editMode && !$scope.isCloning) {
                    $scope.tabIndex = 6;
                    return;
                } else {
                    $scope.tabIndex = 5;
                }
            };

            $scope.validateCouponsTab = function () {
                if ($scope.couponDetail.prefix === null || $scope.couponDetail.prefix === "") {
                    alert("Please enter prefix");
                    return;
                }
                if ($scope.couponDetail.code === null || $scope.couponDetail.code === "") {
                    alert("Please enter code");
                    return;
                }
                if ($scope.couponsAvail === "Not Available" || $scope.couponsAvail === null || $scope.couponsAvail === "") {
                    alert("Please choose a valid coupon prefix/code");
                    return;
                }
                if ($scope.couponDetail.status === null) {
                    alert("Please select status!");
                    return;
                }
                if ($scope.couponDetail.startDate === null) {
                    alert("Please select start date!");
                    return;
                }
                if ($scope.couponDetail.endDate === null) {
                    alert("Please select end date!");
                    return;
                }
                if ($scope.couponDetail.reusable === null) {
                    alert("Please select value for reusable coupons!");
                    return;
                }
                if ($scope.couponDetail.reusableByCustomer === null) {
                    alert("Please select value for reusable customer!");
                    return;
                }
                if ($scope.couponDetail.maxUsage === null) {
                    alert("Please enter max usage value!");
                    return;
                }
                if ($scope.couponDetail.startDate < $scope.offerDetail.startDate) {
                    alert("Please select a start date as equal or after : " + $scope.offerDetail.startDate);
                    return;
                }
                if ($scope.couponDetail.endDate > $scope.offerDetail.endDate) {
                    alert("Please select an end date as equal of after : " + $scope.offerDetail.endDate);
                    return;
                }
                $scope.tabIndex = 6;
            };

            console.log("offerDetail ::::::", $scope.offerDetail);
            $scope.addOffer = function () {
                $scope.checkFrequencyApplicable();
                if($scope.offerDetail.offerValueType === "PERCENTAGE"){
                     $scope.offerDetail.type = "FREEBIE_STRATEGY_PERCENTAGE";
                }
                else if($scope.offerDetail.offerValueType === "FLAT")  {
                     $scope.offerDetail.type = "FREEBIE_STRATEGY_FLAT";
                }
                if($scope.isCloning){
                    $scope.offerDetail.id = null;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.offersAdd,
                    data: $scope.offerDetail
                })
                    .then(
                        function success(response) {
                            if (response.status === 200) {
                                bootbox.alert("Offer Added with Id:" + response.data.id);
                                console.log(response);
                                $scope.offerDetailResponse = response.data;
                                $scope.couponDetail.offer = $scope.offerDetailResponse;
                                $scope.addCouponsMapping();
                            } else if(response.status == 500){
                                bootbox.alert(response.data.errorMessage);
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
            };

            $scope.addCouponsMapping = function () {
                $scope.tabIndex = 7;
                $scope.resultMessage = {};
                $scope.couponDetail.code = $scope.prefixCouponsDetail;
                $scope.couponDetail.couponApplicability = parseInt($scope.couponDetail.couponApplicability);
                console.log(JSON.stringify($scope.couponDetail));
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.couponAdd,
                    data: $scope.couponDetail
                }).then(function success(response) {
                    if (response.status === 200) {
                        console.log(response.data);
                        $scope.resultMessage = "success";
                    } else {
                        console.log(response);
                        $scope.resultMessage = "failed";
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
                $("#ReplicateCouponsTab").show();
            }

            $scope.displayReplicateCoupons = function () {
                $("#replicateCouponsDisplayDiv").show();
            };

            $scope.showRepCoupMode = function (prefixx, couponCodee, count) {
                if (count === "" || count == null) {
                    alert("Coupons Count is Empty");
                    return false;
                }
                prefixx = prefixx.toUpperCase();
                $scope.couponsList = [];
                var payload = $.param({
                    couponCode: couponCodee,
                    couponPrefix: prefixx,
                    replicateCount: count
                });
                console.log("payload", payload);
                $scope.disableCouponGeneration = true;
                $rootScope.showFullScreenLoader = true;
                // return;
                $http({
                    url: AppUtil.restUrls.offerManagement.couponAuto,
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    data: payload
                }).success(function (data) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.couponsList = data;
                    console.log(data);
                });
            };

            $scope.exportData = function () {
                var blob = new Blob([document.getElementById('exportable').innerHTML], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                });
                saveAs(blob, "Report.xls");
            };

            /* $scope.tabIndex = 1;
             $scope.subIndex = 1;*/

            $scope.allCategories = function (check) {
                $scope.productCategoryBrandWise.forEach(function (category) {
                    if ($scope.sel[category.detail.id] === !check) {
                        $scope.sel[category.detail.id] = check;
                        $scope.manageCategories(category, check);
                    }
                });
            };

            $scope.allSubCategories = function (check) {
                $scope.productSubCategoryBrandWise.forEach(function (subCategory) {
                    if ($scope.subCatObj[subCategory.id] === !check) {
                        $scope.subCatObj[subCategory.id] = check;
                        $scope.manageSubCategories(subCategory, check);
                    }
                });
            };

            $scope.allProducts = function (check) {
                $scope.productDetailsList.forEach(function (product) {
                    console.log(product);
                    if(product.id==0){
                        console.log("All Variant desi Chai");
                    }
                    else{
                        if ($scope.prodObj[product.id] === !check) {
                            $scope.prodObj[product.id] = check;
                            manageChaayosProduct(product, check);
                        }
                    }
                });
            };

            $scope.allUnits = function (check) {
                $scope.filteredCafeListBrandWise.forEach(function (unit) {
                    if ($scope.unitAllObj[unit.id] === !check) {
                        $scope.unitAllObj[unit.id] = check;
                        $scope.selectUnits(unit, check);
                    }
                });
            };

            $scope.allCities = function (check) {
                            $scope.allCitiesList.forEach(function (unit) {
                                if ($scope.cityAllObj[unit.id] === !check) {
                                    $scope.cityAllObj[unit.id] = check;
                                    $scope.selectCities(unit, check);
                                }
                            });
                        };

            $scope.allRegions = function (check) {
                // for (var i = 0; i < $scope.regions.length; i++) {
                console.log("Inside all Regions called when top checkbox selected ");
                console.log("Region Data List :", $scope.regionDetailList);
                    $scope.regionDetailList.forEach(function(region) {
                            if ($scope.unitObjs[region.name] === !check) {
                                $scope.unitObjs[region.name] = check;
                                $scope.selectRegions(region, check);
                        }

                });
            };

            $scope.allUnitSubCategories = function (check) {
                 $scope.unitSubCategoryDetailList.forEach(function(category) {
                     if ($scope.unitObjs[category.name] === !check) {
                         $scope.unitObjs[category.name] = check;
                         $scope.selectUnitSubCategories(category, check);
                     }
                 });
            };

            $scope.allOrderSources = function (check) {
                $scope.families.forEach(function (orderSrc) {
                    if ($scope.orderSourceObj[orderSrc] === !check) {
                        $scope.orderSourceObj[orderSrc] = check;
                        $scope.selectOrderSource(orderSrc, check);
                    }
                });
            };

            $scope.allChannelPartners = function (check) {
                $scope.channelPartner.forEach(function (partner) {
                    if ($scope.channelPartObj[partner.id] === !check) {
                        $scope.channelPartObj[partner.id] = check;
                        $scope.selectChannelPartner(partner, check);
                    }
                });
            };

            $scope.allPaymentModes = function (check) {
                $scope.paymentMode.forEach(function (mode) {
                    if ($scope.paymentModeObj[mode.id] === !check) {
                        $scope.paymentModeObj[mode.id] = check;
                        $scope.selectPaymentModes(mode, check);
                    }
                });
            };

            $scope.allPartners = function (check) {
                $scope.partnerDetailsList.forEach(function (partner) {
                    if ($scope.partObj[partner.id] === !check) {
                        $scope.partObj[partner.id] = check;
                        $scope.managePartners(partner, check);
                    }
                });
            };

            $scope.getStartDate =function(startDate){
                if(startDate!=null) {
                    $scope.startDate = new Date(startDate);
                }
            }

            $scope.getDate1 =function(){
                return new Date().toString();
            }

            $scope.getBusinessDateForDate =function( date){
                return new Date(date).toString();
            }

            $scope.compareTwoDates = function(startDate, endDate){

             if(startDate !==null && endDate!==null ) {
                 var date1 = new Date(startDate);
                 console.log("StartDate:", date1);
                 var date2 = new Date(endDate);
                 console.log("EndDate:", endDate);
                 if(date2<date1)
                 {   alert("Please ensure endDate is greater than startDate!");
                     return;
                 }
             }
             return false ;
            }

            if (editOffer !== null) {
                $scope.currentEditOfferBrandId = editOffer.brandId;
                $scope.offerDetail = editOffer;
                var startTime = moment($scope.offerDetail.startDate).format("YYYY-MM-DD");
                var endTime = moment($scope.offerDetail.endDate).format("YYYY-MM-DD");
                $scope.offerDetail.startDate = startTime;
                $scope.offerDetail.endDate = endTime;
                console.log($scope.offerDetail);
                $scope.editMode = true;
                $scope.editOfferCouponMappings = $scope.offerDetail.couponMappingList;
                $scope.offerDetail.couponMappingList = [];
                console.log($scope.editOfferCouponMappings);
                console.log($scope.offerDetail.couponMappingList);
                $scope.editOfferMetadataMappings = $scope.offerDetail.metaDataMappings;
                $scope.offerDetail.metaDataMappings = [];
                console.log($scope.editOfferMetadataMappings);
                console.log($scope.offerDetail.metaDataMappings);
                $scope.editPartners = $scope.offerDetail.partners;
                $scope.offerDetail.partners = [];
                console.log($scope.editPartners);
                console.log($scope.offerDetail.partners);

                if($scope.offerDetail.termsAndConditions != null) {
                    $scope.tncInputs = $scope.offerDetail.termsAndConditions.split("#");
                }

                /*$scope.offerDetail.metaDataMappings.forEach(function (mapping, index, array) {
                    if (mapping.name === "PRODUCT_CATEGORY") {
                        $scope.selectedCategoriesLength++;
                        // array.splice(index,1);
                        // $scope.manageCategories(mapping,true);
                    } else if (mapping.name === "PRODUCT_SUB_CATEGORY") {
                        $scope.selectedSubCategoriesLength++;
                        // $scope.subCatObj[mapping.id] = true;
                        // array.splice(index,1);
                        // $scope.manageSubCategories(mapping,true);
                    } else {
                        $scope.selectedProductsLength++;
                        // for( var i = 0 ; i < $scope.productDetailsList.length; i ++){
                        //     if($scope.productDetailsList[i].id.toString() === mapping.code){
                        //         $scope.
                        //     }
                        // }
                        // array.splice(index,1);
                        // $scope.manageProducts(mapping,true);
                    }
                });*/
                console.log($scope.offerDetail);
                $scope.editOfferCouponMappings.forEach(function (mapping) {
                    if (mapping.type === "NEW_CUSTOMER" && mapping.status === "ACTIVE") {
                        $scope.newCustomerCheck = true;
                        $scope.manageNewCustomer($scope.newCustomerCheck);
                    }
                });
                $scope.getProductsByBrandId(editOffer.brandId);
            } else {
                $scope.offerDetail = {
                    accountsCategory: null,
                    category: null,
                    type: null,
                    text: null,
                    description: null,
                    startDate: null,
                    endDate: null,
                    minValue: null,
                    status: null,
                    validateCustomer: null,
                    removeLoyalty: null,
                    otpRequired: null,
                    minQuantity: null,
                    minLoyalty: null,
                    maxDiscountAmount: null,
                    offerValue: null,
                    emailDomain: null,
                    minItemCount: null,
                    offerScope: null,
                    partners: [],
                    paymentModes:[],
                    metaDataMappings: [],
                    couponMappingList: [],
                    frequencyApplicable: null,
                    frequencyCount: null,
                    autoApplicableforUnit: null,
                    maxQuantity: null,
                    frequencyStrategy: null,
                    termsAndConditions: null,
                    loyaltyBurnPoints : null,
                    signupOfferApplicable: null,
                    brandId: null
                };
            }
            $scope.couponDetail = {
                id: null,
                code: null,
                prefix: null,
                status: null,
                usage: null,
                startDate: null,
                endDate: null,
                reusable: null,
                reusableByCustomer: null,
                maxUsage: null,
                offer: $scope.offerDetail,
                couponMappingList: []
            };

            $scope.setOtpRequired = function (modal) {
                $scope.offerDetail.validateCustomer = modal;
                $scope.isOtpEditable = true;
                if(modal) {
                    $scope.offerDetail.otpRequired = modal;
                }
                else {
                    $scope.isOtpEditable = false;
                    $scope.offerDetail.otpRequired = modal;
                }
            };

            $scope.editOffer = function () {
                $scope.checkFrequencyApplicable();
                if($scope.offerDetail.type === "FREEBIE_STRATEGY"){
                    if($scope.offerDetail.offerValueType === "PERCENTAGE"){
                        $scope.offerDetail.type = "FREEBIE_STRATEGY_PERCENTAGE";
                    }
                    if($scope.offerDetail.offerValueType === "FLAT"){
                        $scope.offerDetail.type = "FREEBIE_STRATEGY_FLAT";
                    }
                }
                console.log($scope.offerDetail);
                console.log(JSON.stringify($scope.offerDetail));
                $scope.finalMetadataMappings = [];

                $scope.editOfferMetadataMappings.forEach(function (mapping, index, array) {
                    var found = false;
                    $scope.offerDetail.metaDataMappings.forEach(function (map, ind, arr) {
                        if (map.code.toString() === mapping.code && map.name === mapping.name && map.name !== "FREEBIE_PRODUCT") {
                            found = true;
                            arr.splice(ind, 1);
                            mapping.status = "ACTIVE";
                            arr.push(mapping);
                        }
                        else if(map.code.toString() === mapping.code && map.name === mapping.name && map.name === "FREEBIE_PRODUCT"){
                            if($scope.offerDetail.type === "FREEBIE_STRATEGY_FLAT" || $scope.offerDetail.type === "FREEBIE_STRATEGY_PERCENTAGE" ){
                                found = true;
                                arr.splice(ind, 1);
                                mapping.status = "ACTIVE";
                                arr.push(mapping);
                            }
                            else{
                                found = false;
                                arr.splice(ind, 1);
                                mapping.status = "IN_ACTIVE";
                                arr.push(mapping);
                            }
                        }
                    });
                    if (!found) {
                        mapping.status = "IN_ACTIVE";
                        $scope.offerDetail.metaDataMappings.push(mapping);
                        // $scope.finalMetadataMappings.push()
                    }

                });
                console.log("metadata fixes");
                console.log($scope.offerDetail);
                // console.log(JSON.stringify($scope.offerDetail));

                $scope.editOfferCouponMappings.forEach(function (mapping, index, array) {
                    var found = false;
                    $scope.offerDetail.couponMappingList.forEach(function (map, ind, arr) {
                        if (map.type === mapping.type) {
                            if (map.type === "NEW_CUSTOMER") {
                                found = true;
                                arr.splice(ind, 1);
                                mapping.status = "ACTIVE";
                                arr.push(mapping);
                            } else if (map.type === "PAYMENT_MODE") {
                                if (map.value.toString() === mapping.value) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            } else if (map.type === "UNIT") {
                                if (map.value.toString() === mapping.value) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            } else if (map.type === "PRODUCT") {
                                if (map.value.toString() === mapping.value && map.dimension === mapping.dimension) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            } else if (map.type === "ORDER_SOURCE") {
                                if (map.value === mapping.value) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            } else if (map.type === "CHANNEL_PARTNER") {
                                if (map.value.toString() === mapping.value) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            } else if (map.type === "UNIT_REGION") {
                                if (map.name === mapping.value) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            }  else if (map.type === "UNIT_SUB_CATEGORY") {
                                if (map.name === mapping.value) {
                                   found = true;
                                   arr.splice(ind, 1);
                                   mapping.status = "ACTIVE";
                                   arr.push(mapping);
                                }
                            }else if (map.type === "FREEBIE_PRODUCT") {
                                if (map.value.toString() === mapping.value && ($scope.offerDetail.type === "FREEBIE_STRATEGY_PERCENTAGE" || $scope.offerDetail.type === "FREEBIE_STRATEGY_FLAT")) {
                                   found = true;
                                   arr.splice(ind, 1);
                                   mapping.status = "ACTIVE";
                                   arr.push(mapping);
                               }
                               else if (map.value.toString() === mapping.value && ($scope.offerDetail.type !== "FREEBIE_STRATEGY_PERCENTAGE" || $scope.offerDetail.type !== "FREEBIE_STRATEGY_FLAT")){
                                    found = false;
                                    arr.splice(ind, 1);
                                    mapping.status = "IN_ACTIVE";
                                    arr.push(mapping);
                               }
                            }
                            else if (map.type === "ACQUISITION_SOURCE") {
                                  if (map.name === mapping.value) {
                                     found = true;
                                     arr.splice(ind, 1);
                                     mapping.status = "ACTIVE";
                                     arr.push(mapping);
                               }
                            }
                            else if(map.type === "CITY") {
                                if (map.name === mapping.value) {
                                    found = true;
                                    arr.splice(ind, 1);
                                    mapping.status = "ACTIVE";
                                    arr.push(mapping);
                                }
                            }
                        }
                    });
                    if (!found) {
                        mapping.status = "IN_ACTIVE";
                        $scope.offerDetail.couponMappingList.push(mapping);
                        // $scope.finalMetadataMappings.push()
                    }

                });

                // console.log($scope.offerDetail);
                // console.log(JSON.stringify($scope.offerDetail));

                $scope.editPartners.forEach(function (mapping, index, array) {
                    var found = false;
                    $scope.offerDetail.partners.forEach(function (map, ind, arr) {
                        if (map.id === mapping.id) {
                            found = true;
                            arr.splice(ind, 1);
                            mapping.status = "ACTIVE";
                            arr.push(mapping);
                        }
                    });
                    if (!found) {
                        mapping.status = "IN_ACTIVE";
                        $scope.offerDetail.partners.push(mapping);
                        // $scope.finalMetadataMappings.push()
                    }

                });

                console.log($scope.offerDetail);
                console.log(JSON.stringify($scope.offerDetail));

                // return;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.offerManagement.offersUpdate,
                    data: $scope.offerDetail
                }).then(function success(response) {
                    if (response.status === 200) {
                        alert("Offers Updated");
                        $uibModalInstance.close($scope.offerDetail.text)
                        // window.location.reload();
                    } else {
                        alert("Offers not updated");
                        console.log(response);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.filterListsByBrand = function() {

                // Creating Filtered Cafe List According to Selected Brand ID
                $scope.filteredCafeListBrandWise = cafelist.filter(cafe => {
                    return cafe.companyId === AppUtil.COMPANY_IDS[AppUtil.BRAND_IDS[$scope.offerDetail.brandId]];
                });
                
                // Extracting Product Types
                var filteredProductTypes = [];
                $scope.productDetailsList.forEach(product => {
                    filteredProductTypes.push(product.type);
                });
                
                // Creating Product Types Set
                var filteredProductTypesSet = [...new Set(filteredProductTypes)];
                
                // Creating Filtered Category List According to Selected Brand ID
                $scope.productCategoryBrandWise = $scope.productCategory.filter(category => {
                    return filteredProductTypesSet.includes(category.detail.id);
                });
                
                // Creating Filtered SubCategory List According to Selected Brand ID
                $scope.productSubCategoryBrandWise = $scope.finalSubtype.filter(subCategory => {
                    return filteredProductTypesSet.includes(subCategory.type);
                });

            }

            $scope.isBrandIdEditable = function () {
                if (editOffer !== null) {
                    return !editOffer?.isCloning;
                }
                return false;
            }

        });
