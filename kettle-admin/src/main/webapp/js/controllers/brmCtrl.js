/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
		.controller(
				"brmController",
				function($rootScope, $scope, $location, $http, AppUtil,
						$cookieStore) {

					$scope.init = function() {
						$scope.filesSelected = [];
						$scope.selectedOption = "consolidated";
					};

					$scope.runMOMReport = function() {
						$rootScope.showFullScreenLoader = true;
						var URL = AppUtil.restUrls.reportMetaData.momReport;
						$http({
							method : 'GET',
							url : URL,
						}).then(function success(response) {
							$rootScope.showFullScreenLoader = false;
							$scope.processResponse(response);
						}, function error(response) {
							$rootScope.showFullScreenLoader = false;
							console.log("error:" + response);
						});
					};

					$scope.runWOWReport = function() {
						$rootScope.showFullScreenLoader = true;
						var URL = AppUtil.restUrls.reportMetaData.wowReport;
						$http({
							method : 'GET',
							url : URL,
						}).then(function success(response) {
							$scope.processResponse(response);
						}, function error(response) {
							$rootScope.showFullScreenLoader = false;
							console.log("error:" + response);
						});
					};

					$scope.setFilesSelected=function (){
						var htmlEle = document.getElementById("multi-tds-pdf");
						var filesArr = htmlEle.files;
						$scope.filesSelected = [];
						for (var i = 0; i < filesArr.length; i++) {
							let obj = {status: false, message: "Sending Mail ....", fileName: filesArr[i].name}
							$scope.filesSelected.push(obj);
						}
					}

					$scope.generateRevenueCertificate = function(date) {
						if (date == undefined || date == null) {
							return;
						}
						if($scope.selectedOption == "segregated"){
						$rootScope.showFullScreenLoader = true;
						var data = date.split("-")
						var year = data[0];
						var month = data[1];
						var URL = AppUtil.restUrls.reportMetaData.revenueCertificate;
						$http({
							method: 'GET',
							url: URL,
							params: {
								month: month,
								year: year,
								type: "Gross"
							},
							responseType: "arraybuffer"
						}).then(function success(response) {
							$scope.processRevenueCertificateResponse(response, "Gross");
							$http({
								method: 'GET',
								url: URL,
								params: {
									month: month,
									year: year,
									type: "DineInDelivery"
								},
								responseType: "arraybuffer"
							}).then(function success(response) {
								$scope.processRevenueCertificateResponse(response, "DineInDelivery");
							}, function error(response) {
								$rootScope.showFullScreenLoader = false;
								console.log("error:" + response);
							});
						}, function error(response) {
							$rootScope.showFullScreenLoader = false;
							console.log("error:" + response);
						});
						}
						if($scope.selectedOption == "" || $scope.selectedOption == "consolidated"){
						$rootScope.showFullScreenLoader = true;
                        var data = date.split("-")
                        var year = data[0];
                        var month = data[1];
                        var URL = AppUtil.restUrls.reportMetaData.revenueCertificate;
                        	$http({
                        		method: 'GET',
                        		url: URL,
                        		params: {
                        		         month: month,
                        		         year: year,
                        				 reportType: consolidated,
                        				},
                        	   responseType: "arraybuffer"
                        		}).then(function success(response) {
                                  		$scope.processRevenueCertificateResponse(response, "Consolidated");
                                  		}, function error(response) {
                                  		$rootScope.showFullScreenLoader = false;
                                  		console.log("error:" + response);
                                  });
                        }
                         else{console.log($scope.selectedOption)};
					};

					function getFileExtension(fileName) {
						var re = /(?:\.([^.]+))?$/;
						return re.exec(fileName)[1];
					}

					$scope.sendEmails = function () {
						$scope.setFilesSelected();
						var htmlEle = document.getElementById("multi-tds-pdf");
						var filesArr = htmlEle.files;
						for (var i = 0; i < filesArr.length; i++) {
							var fd = new FormData();
							var fileExt = getFileExtension(filesArr[i].name);
							var nameWithoutExt = filesArr[i].name.replace("." + fileExt, "");
							var newFile = new File([filesArr[i]], nameWithoutExt+ "." + fileExt);
							var URL = AppUtil.restUrls.reportMetaData.revenueCertificateMails;
							if (!isNaN(filesArr[i].name.split("_")[0]) && !isNaN(filesArr[i].name.split("_")[1]) && !isNaN(filesArr[i].name.split("_")[2])) {
								fd.append("file", newFile);
								fd.append("month", parseInt(filesArr[i].name.split("_")[2]));
								fd.append("year", parseInt(filesArr[i].name.split("_")[1]));
								$scope.triggerMails(URL, fd);
							}else {
								console.log("not a valid file " + filesArr[i].name);
								$scope.filesSelected[i].status = false;
								$scope.filesSelected[i].message = "not a valid file " + filesArr[i].name;
							}
						}
					};

					$scope.triggerMails = function (URL, fd) {
						$http({
							method: 'POST',
							url: URL,
							data: fd,
							headers: {'Content-Type': undefined}
						}).then(function success(response) {
							console.log(response);
							for (var i = 0; i < $scope.filesSelected.length; i++) {
								if ($scope.filesSelected[i].fileName === response.data.fileName) {
									$scope.filesSelected[i].status = response.data.status;
									$scope.filesSelected[i].message = response.data.message;
								}
							}
						}, function error(response) {
							$rootScope.showFullScreenLoader = false;
							bootbox.alert("error while sending mail ",response);
							console.log("error:" + response);
						});
					}

					$scope.processRevenueCertificateResponse = function(response,type) {
						if (type === "DineInDelivery") {
							$rootScope.showFullScreenLoader = false;
						}else if(type === "Consolidated"){
						$rootScope.showFullScreenLoader = false;
						}
						if (response) {
							var blob = new Blob([response.data], {type: 'application/zip'});
							if(type == "Consolidated"){
						     saveAs(blob,"RevenueCertificates.zip");
							}
							else{
							saveAs(blob, type + "_RevenueCertificates.zip");
							}
							$scope.reportStatus = " Report Generated Successfully";
							$("#alertModal").modal("show");
						} else {
							$scope.reportStatus = "Error while Generating Revenue Certificate, Consult the Technical Department";
							$("#alertModal").modal("show");
						}
					};

					$scope.processResponse = function(response) {
						$rootScope.showFullScreenLoader = false;
						if (response) {
							$scope.reportStatus = "Report Generated Successfully";
							$("#alertModal").modal("show");
						} else {
							$scope.reportStatus = "Error while Generating Report, Consult the Technical Department";
							$("#alertModal").modal("show");
						}
					};
				});