/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('partnerCategoryManagementCtrl', [
    '$location', '$scope', 'AppUtil', '$rootScope', '$http', 'zomatoNewCatalogService', 'fileService', '$cookieStore',
    function ($location, $scope, AppUtil, $rootScope, $http, zomatoNewCatalogService, fileService, $cookieStore) {

        $scope.init = function () {
            $rootScope.enableScreenFilter = true;
            /*$scope.getUnitList();
            $scope.getChannelPartnersList();*/
            $scope.productGroups = [];
            $scope.groupTypes = [{id: 1, name: "All"}, {id: 2, name: "CATEGORY"}, {id: 3, name: "SUB_CATEGORY"}];
            $scope.selectedType = $scope.groupTypes[0];
            $scope.days=[{id:0,name:"MONDAY"},{id:1,name:"TUESDAY"},{id:2,name:"WEDNESDAY"},{id:3,name:"THURSDAY"},{id:4,name:"FRIDAY"},{id:5,name:"SATURDAY"},{id:6,name:"SUNDAY"}];
            console.log($scope.days);
            $scope.keys=null;
            $scope.values  = []
            $scope.selctedDaysAndSlots = {}
            $scope.mapOfDaysAndSlots = null;
            $scope.selectedDays=[];
            $scope.selectedSlots = [];
            $scope.selectedGroupName = null;
            $scope.viewData = null;
            $scope.alreadyPresentDaysAndSlots = {};
            $scope.slots = [{id:0,name:"DAY_SLOT_BREAKFAST"},{id:1,name:"DAY_SLOT_LUNCH"},{id:2,name:"DAY_SLOT_EVENING"},{id:3,name:"DAY_SLOT_DINNER"},{id:4,name:"DAY_SLOT_POST_DINNER"},{id:5,name:"DAY_SLOT_OVERNIGHT"}];
            $scope.actionList = ["MANAGE GROUPS", "MANAGE MENU", "MANAGE RECOMMENDATIONS", "MANAGE ICONS"];
            $scope.tncInputs = [];
            $scope.tncInputs.push('');
            $scope.selectedTypeForSearch = null;
            $scope.selectedMenuApp = null;
            $scope.productSearch = "";
            $scope.recommendationSearch = "";
            $scope.openIndex = null;
            $scope.openAll = false;

            $scope.categoryTags = [
                {
                    name: 'CHAAYOS SPECIAL',
                    id: 'SP'
                },
                {
                    name: 'TRENDING TODAY ',
                    id: 'TT'
                },
                {
                    name: 'SPECIAL_CATEGORY ',
                    id: 'SC'
                },
                {
                    name: 'REGULAR',
                    id: 'REG'
                },
                {
                    name: 'SUPPER_ADDON',
                    id: 'SA'
                },
                {
                    name: 'BESTSELLERS ',
                    id: 'BS'
                },
                {
                    name: 'CHAI_SELECTION_SCREEN ',
                    id: 'CSS'
                },
                {
                    name: 'USE_CASE_A ',
                    id: 'UC_A'
                },
                {
                    name: 'USE_CASE_B ',
                    id: 'UC_B'
                },
                {
                    name: 'USE_CASE_C ',
                    id: 'UC_C'
                },
                {
                    name: 'USE_CASE_D ',
                    id: 'UC_D'
                },
                {
                    name: 'USE_CASE_E ',
                    id: 'UC_E'
                },
                {
                    name: 'USE_CASE_F ',
                    id: 'UC_F'
                },
                {
                    name: 'USE_CASE_G ',
                    id: 'UC_G'
                },
                {
                    name: 'USE_CASE_H ',
                    id: 'UC_H'
                },
                {
                    name: 'USE_CASE_I ',
                    id: 'UC_I'
                },
                {
                    name: 'USE_CASE_J ',
                    id: 'UC_J'
                },
                {
                    name: 'USE_CASE_K ',
                    id: 'UC_K'
                },
                {
                    name: 'USE_CASE_L ',
                    id: 'UC_L'
                },
                {
                    name:'CHAAYOS MEMBERSHIP',
                    id:'CM'
                }
            ];
            $scope.selectedAction = "MANAGE GROUPS";
            $scope.searchtype = ["GROUP TAG"];
            // $scope.MENU_APP = ['DINE_IN_APP',"OTHERS"];
            $scope.getMenuType();
            $scope.getMenuStatus();
            $scope.getMenuApp();
            // $scope.menuType = ["DEFAULT", "SINGLE_SERVE"];
            $scope.recommendationTypes = ["MENU"];
            $scope.clone = false;
            $scope.groupSelected = true;
            $scope.idSelected = null;
            $scope.selectAction($scope.selectedAction);
            $scope.imageSuffix = AppUtil.getImageUrl().productImage;
            $scope.categoryImageSuffix = AppUtil.getImageUrl().offerImage;
        };

        $scope.multiSelectSettings = {
                template: '<b> {{option.name}}</b>'
            };

        $scope.selectAction = function (action) {
            $scope.selectedAction = action;
            switch (action) {
                case "MANAGE GROUPS":
                    $scope.initManageGroups();
                    break;
                case "MANAGE MENU":
                    $scope.initManageMenu();
                    break;
                case "MANAGE ICONS":
                    $scope.initManageIcon();
                    break;
                case "MANAGE RECOMMENDATIONS":
                    $scope.initManageRecommendation();
                    break;

            }
        };

        $scope.setGroupName = function(group){
            $scope.viewData = []
            $scope.viewTimings(group.groupName)
            $scope.selectedGroupName = group.groupName;

           // $scope.selctedDaysAndSlots[$scope.selectedGroupName] = group.timings;
        }

        $scope.refreshAllVariables = function(){
//            console.log($scope.selctedDaysAndSlots)
//            $scope.selectedGroupName= null;
            $scope.mapOfDaysAndSlots= null;
            $scope.viewData = []
            $('#addTimingsModal').modal('hide');

        }

        $scope.refreshAllVariablesOnModalClose = function(){
        //            console.log($scope.selctedDaysAndSlots)
        //            $scope.selectedGroupName= null;
                    $scope.selctedDaysAndSlots = {};
                    $scope.mapOfDaysAndSlots= null;
                    $scope.viewData = []
                    $scope.alreadyPresentDaysAndSlots=[]
                    $('#addTimingsModal').modal('hide');

                }



//           $scope.addTNC = function () {
//                $scope.tncInputs.push('');
//            };

        $scope.initManageGroups = function () {
            $scope.newGroup = {};
            $scope.showCreateGroups = false;
            $scope.showGroups = true;
            $scope.productGroups = [];
            $scope.selectedTypeForSearch = null;
            $scope.selectedMenuApp = null;
            $scope.getAllIcons();
            $scope.getGroupMapping();
            if ($scope.activeProducts == null) {
                $scope.getActiveProducts();
            }
            $scope.getAllProductGroupsTemp();
            /*$scope.resetUnitList();
            $scope.resetChannelPartnersList();*/
        };

        $scope.initManageMenu = function () {
            $scope.menus = [];
            $scope.showCreateMenu = false;
            $scope.newMenu = {};
            $scope.getMenus();
            //$scope.resetUnitList();
        };

        $scope.initManageIcon = function () {
            $scope.showCreateIcon = false;
            $scope.newIcon = {};
            $scope.getAllIcons();

        };

        $scope.initManageRecommendation = function () {
            $scope.recommendations = [];
            $scope.showCreateRecommendation = false;
            $scope.newRecommendation = {};
            $scope.getRecommendations();
            $scope.getAllProducts();


        };

//        $scope.addToMapSlots = function(){
//             //check map key
//             var data = {
//                days : $scope.selectedDays,
//                slots : $scope.selectedSlots
//             }
//             $scope.keys = $scope.selectedGroupName;
//             $scope.values.days = $scope.selectedDays;
//             $scope.values.slots = $scope.selectedSlots;
//             $scope.selctedDaysAndSlots.push($scope.keys,$scope.values);
//             $scope.mapOfDaysAndSlots.push($scope.selectedGroupName,data);
//             $scope.selectedDays = [];
//             $scope.selectedSlots = [];
//
//        };

        $scope.removeFromMapOfDayAndSlots = function(days,slots){
            var resultList = []
            for(var i in $scope.selctedDaysAndSlots[$scope.selectedGroupName]){
                var data = $scope.selctedDaysAndSlots[$scope.selectedGroupName][i];
                if(days.join(",")!=data.days.join(",")){
                    resultList.push(data);
                }
            }
            $scope.selctedDaysAndSlots[$scope.selectedGroupName] = resultList;

        }

        $scope.removeFromViewData = function(days,slots){
            var resultList = [];
            var viewList = []
            var setOfExistingDays = new Set();
            for(var i in $scope.viewData){
                var data = $scope.viewData[i]
                if(days!=data.days){
                    viewList.push(data)
                }
                else{
                    if(slots != data.slots){
                       viewList.push(data)

                    }
                }
            }
            $scope.viewData = viewList;

            for(var i in $scope.alreadyPresentDaysAndSlots[$scope.selectedGroupName]){
                var data = $scope.alreadyPresentDaysAndSlots[$scope.selectedGroupName][i];
                if(days!=data.days.join(",")){
                   resultList.push(data);
                }
                else{
                    if(slots != data.slots.join(",")){
                        resultList.push(data);
                    }
                }
            }
            $scope.alreadyPresentDaysAndSlots[$scope.selectedGroupName] = resultList;
        }

        $scope.checkRepeatMapping = function(days,slots){
            if($scope.selectedGroupName in $scope.selctedDaysAndSlots){
                var listOfDaysAndSlots = $scope.selctedDaysAndSlots[$scope.selectedGroupName]
                for(var i in listOfDaysAndSlots){
                    var existingDays = listOfDaysAndSlots[i].days;
                    var existingSlots = listOfDaysAndSlots[i].slots;
                    var setOfDays = new Set();
                    var setOfSlots = new Set();
                    for(var i in existingDays){
                        setOfDays.add(existingDays[i]);
                    }
                    for(var i in existingSlots){
                        setOfSlots.add(existingSlots[i]);
                    }
                    for(var i in days){
                        for(var j in slots){
                            if(setOfDays.has(days[i]) && setOfSlots.has(slots[j])){
                                return false;
                            }
                        }

                    }

                }
            }
            if($scope.selectedGroupName in $scope.alreadyPresentDaysAndSlots){
                var listOfDaysAndSlots = $scope.alreadyPresentDaysAndSlots[$scope.selectedGroupName]
                for(var i in listOfDaysAndSlots){
                                var existingDays = listOfDaysAndSlots[i].days;
                                var existingSlots = listOfDaysAndSlots[i].slots;
                                var setOfDays = new Set();
                                var setOfSlots = new Set();
                                for(var i in existingDays){
                                    setOfDays.add(existingDays[i]);
                                }
                                for(var i in existingSlots){
                                    setOfSlots.add(existingSlots[i]);
                                }
                                for(var i in days){
                                    for(var j in slots){
                                        if(setOfDays.has(days[i]) && setOfSlots.has(slots[j])){
                                            return false;
                                        }
                                    }

                                }

                            }
            }

            return true;
        }

        $scope.addToMapSlots = function(){
            if($scope.checkRepeatMapping($scope.getDaysFromMap($scope.selectedDays),$scope.getSlotFromMap($scope.selectedSlots))== true){
            var data = {
                days : $scope.getDaysFromMap($scope.selectedDays),
                slots : $scope.getSlotFromMap($scope.selectedSlots)
            }
            $scope.keys = $scope.selectedGroupName;
            if($scope.keys in $scope.selctedDaysAndSlots){
//                $scope.values.push(data);
                $scope.selctedDaysAndSlots[$scope.keys].push(data);
            }
            else{
                $scope.values.push(data);
                $scope.selctedDaysAndSlots[$scope.keys] = $scope.values;

            }
            $scope.keys = null;
            $scope.values = [];
            $scope.selectedDays = [];
            $scope.selectedSlots = [];
            }
            else{
                bootbox.alert("Don't Select same Slot For Same Day")
            }
        }

        $scope.checkMapOfDayAndSlots = function(){
           $scope.mapOfDaysAndSlots = {}
           if($scope.selectedGroupName in $scope.selctedDaysAndSlots){
                $scope.mapOfDaysAndSlots = $scope.selctedDaysAndSlots[$scope.selectedGroupName]
                return true;
           }
           else if($scope.viewData != null){
            return true;
           }
           else{
               return false;
           }
        }

        $scope.getAllProducts = function () {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.productMetaData.products
            }).then(function success(response) {
                $scope.productsInfo = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.setSelectedType = function (selectedType) {
            $scope.selectedType = selectedType;
        };
        $scope.setSelectedTypeForSearch = function (selectedTypeForSearch) {
            $scope.selectedTypeForSearch = selectedTypeForSearch;
        };
        $scope.setSelectedMenuAppForSearch = function (selectedMenuAppType) {
            $scope.selectedMenuApp = selectedMenuAppType;
        };

        $scope.setSelectedCloningSubCategory = function (subCategory) {
            console.log(subCategory);
            $scope.subCategoryCloning = subCategory;
        };

        $scope.cloneStatus = function (clone) {
            $scope.clone = clone;
            console.log(clone);
        }

        $scope.resetAppList = function () {
            $scope.appList.map(function (app) {
                app.selected = false;
            });
        };

        $scope.getMenuType = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.menuType
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.menuType = response.data;
                    $rootScope.showFullScreenLoader = false;
                } else {
                    bootbox.alert("Error getting menu type.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getMenuStatus = function () {
            $rootScope.showFullScreenLoader = true;

            // Menu Status List Hard Coded For Now

            // $http({
            //     method: 'GET',
            //     url: AppUtil.restUrls.channelPartner.menuStatus
            // }).then(function success(response) {
            //     if (response.status === 200 && response.data != null) {
            //         $scope.menuStatusList = response?.data;
            //         $rootScope.showFullScreenLoader = false;
            //     } else {
            //         bootbox.alert("Error getting Menu Status.");
            //     }
            // }, function error(response) {
            //     console.log("error:" + response);
            //     $rootScope.showFullScreenLoader = false;
            // });
            
            $scope.menuStatusList = ['ALL', 'ACTIVE', 'IN_ACTIVE', 'ARCHIVED'];
        };

        $scope.getMenuApp = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.menuApp
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.menuAppList = ['ALL'];
                    response.data.forEach((e) => $scope.menuAppList.push(e));
                    // console.log("response data is ::: ", response.data);
                    $rootScope.showFullScreenLoader = false;
                } else {
                    bootbox.alert("Error getting menu app.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getGroupMapping = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.groupMapping
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    console.log(response.data);
                    $scope.groupMapping = response.data;
                    $rootScope.showFullScreenLoader = false;
                } else {
                    bootbox.alert("Error getting menu type.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getAllBrands = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.brandManagement.getAllBrands
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.brands = response.data;
                    $rootScope.showFullScreenLoader = false;
                } else {
                    bootbox.alert("Error getting brands.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.setSelectedBrand = function (selectedBrand) {
            $scope.selectedBrand = selectedBrand;
        };

        $scope.getActiveProducts = function () {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.productMetaData.productsActive,
            }).then(function success(response) {
                if (response.status == 200 && response.data != null) {
                    var products = [];
                    response.data.map(function (product) {
                        if (product.classification == "MENU" || product.classification == "PAID_ADDON") {
                            products.push(product);
                        }
                    });
                    $scope.activeProducts = products;
                }
            }, function error(error) {
                $rootScope.showFullScreenLoader = false;
                bootbox.alert("Error getting active products list.");
            });
        };

        $scope.getAllProductGroupsTemp = function(){
            if($scope.productGroups.length === 0) {
                            $rootScope.showFullScreenLoader = true;
                            $http({
                                method: 'GET',
                                url: AppUtil.restUrls.channelPartner.getProductGroups
                            }).then(function success(response) {
                                if (response.status === 200 && response.data != null) {
                                    $scope.productGroups = response.data;
                                } else {
                                    bootbox.alert("Error getting product groups.");
                                }
                                $rootScope.showFullScreenLoader = false;
                            }, function error(response) {
                                console.log("error:" + response);
                                $rootScope.showFullScreenLoader = false;
                 });
        }
        }

        $scope.getAllProductGroups = function () {
            if($scope.selectedMenuApp == null) {
                bootbox.alert("Please select Menu App *");
                return;
            }
            $rootScope.showFullScreenLoader = true;

            $scope.groupTypeSelected = ($scope.selectedTypeForSearch && $scope.selectedTypeForSearch.name) || "";
            if($scope.groupTypeSelected === 'All') {
                $scope.groupTypeSelected = "";
            }
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.getProductGroupsByFilter,
                params: {groupType: $scope.groupTypeSelected, menuAppType: $scope.selectedMenuApp}
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.productGroups = response.data;
                } else {
                    bootbox.alert("Error getting product groups.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.addNewGroup = function () {
            console.log($scope.newGroup)
            if (!$scope.newGroup.groupName) {
                bootbox.alert("please enter group name");
                return
            }
            if (!$scope.newGroup.groupTag) {
                bootbox.alert("please enter  group tag");
                return
            }
            if (!$scope.newGroup.groupDescription) {
                bootbox.alert("please enter group description");
                return
            }
            if (!$scope.newGroup.menuApp) {
                bootbox.alert("please choose menu app")
                return;
            }


            // if ($scope.newGroup.menuApp == "DINE_IN_APP" && $scope.selectedType.name == "CATEGORY" && (!$scope.newGroup.icon)) {
            //     bootbox.alert("please select icon as it is mandatory for dine in app");
            //     return;
            // }


            var result = confirm("Are You Sure You Want To Add Group");
            if (!result) {
                return;
            }
            $rootScope.showFullScreenLoader = true;
            $scope.newGroup.createdBy = {id: AppUtil.getCurrentUser().id};
            $scope.newGroup.updatedBy = {id: AppUtil.getCurrentUser().id};
            $scope.newGroup.groupType = $scope.selectedType.name;
            $scope.newGroup.cloneId = $scope.clone ? $scope.subCategoryCloning.groupId : null;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.createProductGroups,
                data: [$scope.newGroup]
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.productGroups.push(response.data[0]);
                    $scope.newGroup = {};
                    $scope.newGroup.menuType = null;
                    $scope.showCreateGroups = false;
                    // $('#groupCheckBox').value(false);
                    $('#select2-selectedGroupCloneId-container').html("");
                    $('#select2-iconId-container').html("");
                    $scope.clone = false;
                    $scope.categoryImageUploaded = false;
                    bootbox.alert("New group created successfully.");
                } else {
                    bootbox.alert("Error adding product group.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.openProductSequenceModal = function (group) {
            console.log(group);
            $scope.selectedGroup = group;
            $scope.selectedProductSequence = [];
            $scope.productGroupMap = [];
            var productList = [];
            $scope.selectedGroup.productSequenceList.map(function (prod) {
                $scope.activeProducts.map(function (product) {
                    if (prod.product.id == product.id) {
                        var item = angular.copy(product);
                        item.index = prod.productIndex;
                        item.selected = false;
                        item.recommended = prod.recommended;
                        $scope.selectedProductSequence.push(item);
                        productList.push(prod.product.id);
                    }
                });
            });
            $scope.selectedProductSequence.sort(function (a, b) {
                return a.index - b.index;
            });
            $scope.groupAvialableProductSequence = [];
            $scope.activeProducts.map(function (product) {
                // if(group.menuType=="DEFAULT" && product.type!=43) {
                if (productList.indexOf(product.id) < 0) {
                    product.selected = false;
                    $scope.groupAvialableProductSequence.push(product);
                }
                // }
                // if(group.menuType=="SINGLE_SERVE" && product.type==43) {
                //     if (productList.indexOf(product.id) < 0) {
                //         product.selected = false;
                //         $scope.groupAvialableProductSequence.push(product);
                //     }
                // }
            });
            $scope.groupAvialableProductSequence.sort(function (a, b) {
                return ('' + a.name).localeCompare(b.name);
            });

            $scope.activeProducts.map(function (product) {
                //  if(group.menuType=="DEFAULT" && product.type!=43){
                //      var prod = {id: product.id, name: product.name, groups: []};
                //
                //  }
                // else  if(group.menuType=="SINGLE_SERVE" && product.type==43) {
                var prod = {id: product.id, name: product.name, groups: []};
                // }
                // else{
                //     return;
                // }
                $scope.productGroups.map(function (group) {
                    if (group.groupTag == "SUB_CATEGORY") {
                        group.productSequenceList.map(function (sequence) {
                            if (sequence.product.id == product.id) {
                                console.log(JSON.stringify(sequence.productGroup.name));
                                prod.groups.push(group.groupName);
                            }
                        });
                    }
                });
                $scope.productGroupMap.push(prod);
            });
            $scope.active = 0;
            $("#productSequenceModal").modal("show");
        };

        $scope.openProductRecommendationModal = function (group) {
            $scope.selectedGroup = group;
            $("#productRecommendationModal").modal("show");
        };

        $scope.toggleProductRecommended = function (product) {
            if (product.recommended === true) {
                product.recommended = false;
            } else {
                product.recommended = true;
            }
        };

        $scope.setGroupProductRecommendation = function () {
            $scope.selectedGroup.updatedBy = {id: AppUtil.getCurrentUser().id};
            $scope.selectedGroup.productSequenceList.map(function (product) {
                product.updatedBy = {id: AppUtil.getCurrentUser().id};
            });
            console.log($scope.selectedGroup);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.updateProductSequence,
                data: [$scope.selectedGroup]
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.selectedGroup = response.data;
                    $("#productRecommendationModal").modal("hide");
                    bootbox.alert("Group recommended products updated successfully.");
                } else {
                    bootbox.alert("Error updating recommended products in group.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getKeyForDay = function(day){
            var days = ["dayMonday","dayTuesday","dayWednesday","dayThursday","dayFriday","daySaturday","daySunday"];
            var key;
            for(var i in days){
               if(days[i].toLowerCase().indexOf(day.name.toLowerCase()) > -1){
                 key = days[i];
               }
            }
            return key;
        }

        $scope.getKeyForDayV1 = function(day){
                    var days = ["dayMonday","dayTuesday","dayWednesday","dayThursday","dayFriday","daySaturday","daySunday"];
                    var key;
                    for(var i in days){
                       if(days[i].toLowerCase().indexOf(day.toLowerCase()) > -1){
                         key = days[i];
                       }
                    }
                    return key;
                }



        $scope.getDaysFromMap =function(selectedDays){
            var days = [];
            for(var i in selectedDays){
                days.push(selectedDays[i].name);
            }
            return days;
        }

        $scope.getSlotFromMap = function(selctedSlots){
            var slots = [];
            for(var i in selctedSlots){
                slots.push(selctedSlots[i].name);
            }
            return slots;
        }

        $scope.getTimings = function() {
            $scope.menuSequence.timings={
                "dayMonday":false,
                "dayTuesday":false,
                "dayWednesday":false,
                "dayThursday":false,
                "dayFriday":false,
                "daySaturday":false,
                "daySunday":false
            };
            for(var i in $scope.selectedDays){
                $scope.menuSequence.timings[$scope.getKeyForDay($scope.selectedDays[i])]=true;
                }
        $scope.getSlotsTimimgs();
        $scope.menuSequence.timings.time1From = $scope.timeFrom.length>0 ? $scope.timeFrom[0] :null;
        $scope.menuSequence.timings.time1To =$scope.timeFrom.length>0 ? $scope.timeTo[0]:null;
        $scope.menuSequence.timings.time2From = $scope.timeFrom.length>1 ?$scope.timeFrom[1]:null;
        $scope.menuSequence.timings.time2To =$scope.timeFrom.length>1 ? $scope.timeTo[1]:null;
        $scope.menuSequence.timings.time3From = $scope.timeFrom.length>2 ?$scope.timeFrom[2]:null;
        $scope.menuSequence.timings.time3To =$scope.timeFrom.length>2 ? $scope.timeTo[2]:null;
        return $scope.menuSequence.timings;

    }

    $scope.getTimingsV1 = function(days,slots){
        $scope.menuSequence.timings={
                        "dayMonday":false,
                        "dayTuesday":false,
                        "dayWednesday":false,
                        "dayThursday":false,
                        "dayFriday":false,
                        "daySaturday":false,
                        "daySunday":false
                    };
        for(var i in days){
            $scope.menuSequence.timings[$scope.getKeyForDayV1(days[i])]=true;
        }
        $scope.getSlotsTimimgsV1(slots);
                $scope.menuSequence.timings.time1From = $scope.timeFrom.length>0 ? $scope.timeFrom[0] :null;
                $scope.menuSequence.timings.time1To =$scope.timeFrom.length>0 ? $scope.timeTo[0]:null;
                $scope.menuSequence.timings.time2From = $scope.timeFrom.length>1 ?$scope.timeFrom[1]:null;
                $scope.menuSequence.timings.time2To =$scope.timeFrom.length>1 ? $scope.timeTo[1]:null;
                $scope.menuSequence.timings.time3From = $scope.timeFrom.length>2 ?$scope.timeFrom[2]:null;
                $scope.menuSequence.timings.time3To =$scope.timeFrom.length>2 ? $scope.timeTo[2]:null;
                var daySlots = "";
                for(var i in slots){
                  daySlots = daySlots + slots[i];
                  if(i< slots.length-1){
                    daySlots = daySlots + ",";
                  }
                }
                $scope.menuSequence.timings.daySlots = daySlots;
                return $scope.menuSequence.timings;
    }

    $scope.getSlotsTimimgsV1 =function(slots){
                $scope.timeFrom = [];
                $scope.timeTo =[];
                for(var i in slots)
                if (slots[i] == "DAY_SLOT_BREAKFAST") {
                    $scope.timeFrom[i] = "05:00";
                    $scope.timeTo[i] = "11:00";
                } else if (slots[i] == "DAY_SLOT_LUNCH") {
                    $scope.timeFrom[i] = "12:00";
                    $scope.timeTo[i] = "14:00";
                } else if (slots[i] == "DAY_SLOT_EVENING") {
                    $scope.timeFrom[i] = "15:00";
                    $scope.timeTo[i] = "19:00";
                } else if (slots[i] == "DAY_SLOT_DINNER") {
                    $scope.timeFrom[i] = "20:00";
                    $scope.timeTo[i] = "21:00";
                } else if (slots[i] == "DAY_SLOT_POST_DINNER") {
                    $scope.timeFrom[i] = "22:00";
                    $scope.timeTo[i] = "23:00";
                } else if (slots[i] == "DAY_SLOT_OVERNIGHT") {
                    $scope.timeFrom[i] = "00:00";
                    $scope.timeTo[i] = "04:00";
                }

                $scope.timeFrom.sort(function(a,b){
                    return Number(a)-Number(b);
                });

                $scope.timeTo.sort(function(a,b){
                    return Number(a)-Number(b);
                });
            };
        
        $scope.getSlotsTimimgs =function(){
            $scope.timeFrom = [];
            $scope.timeTo =[];
            for(var i in $scope.selectedSlots)
            if ($scope.selectedSlots[i].name == "DAY_SLOT_BREAKFAST") {
                $scope.timeFrom[i] = "05:00";
                $scope.timeTo[i] = "11:00";
            } else if ($scope.selectedSlots[i].name == "DAY_SLOT_LUNCH") {
                $scope.timeFrom[i] = "12:00";
                $scope.timeTo[i] = "14:00";
            } else if ($scope.selectedSlots[i].name == "DAY_SLOT_EVENING") {
                $scope.timeFrom[i] = "15:00";
                $scope.timeTo[i] = "19:00";
            } else if ($scope.selectedSlots[i].name == "DAY_SLOT_DINNER") {
                $scope.timeFrom[i] = "20:00";
                $scope.timeTo[i] = "21:00";
            } else if ($scope.selectedSlots[i].name == "DAY_SLOT_POST_DINNER") {
                $scope.timeFrom[i] = "22:00";
                $scope.timeTo[i] = "23:00";
            } else if ($scope.selectedSlots[i].name == "DAY_SLOT_OVERNIGHT") {
                $scope.timeFrom[i] = "00:00";
                $scope.timeTo[i] = "04:00";
            }

            $scope.timeFrom.sort(function(a,b){
                return Number(a)-Number(b);
            });
    
            $scope.timeTo.sort(function(a,b){
                return Number(a)-Number(b);
            });
        };


      $scope.viewTimings = function(category){
        $scope.viewData = [];
        for(var seq in $scope.menuSequence){
            if($scope.menuSequence[seq].groupName === category ){
                for(var i in $scope.menuSequence[seq].timings){
                    if($scope.menuSequence[seq].timings[i]["service"]!="TAKEAWAY"){
                       $scope.viewDays = "";
                       $scope.viewSlots ="";
                    Object.keys($scope.menuSequence[seq].timings[i]).forEach(function(key){
                        if(($scope.menuSequence[seq].timings[i])[key]==true){
                            if($scope.viewDays == ""){
                                $scope.viewDays = $scope.convertToDay(key);
                            }
                            else{$scope.viewDays = $scope.viewDays +"," +$scope.convertToDay(key);}
                        }
                        if(key.slice(5) == "From"){
                            if($scope.viewSlots == ""){
                                if($scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key])!=""){
                                    $scope.viewSlots = $scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key]);
                                }
                            } else{
                                if($scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key])!=""){
                                    $scope.viewSlots = $scope.viewSlots +"," + $scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key]);
                                }
                            }
                        }
                    });
                    var dataToView = {
                        days : $scope.viewDays,
                        slots : $scope.viewSlots,
                        daySlots: $scope.menuSequence[seq].timings[0].daySlots
                    }
                    $scope.viewData.push(dataToView);


//
                }
                }
            }
        }
      }
        
      $scope.convertToDay = function(day){
      day = day.slice(3).toUpperCase();
      return day;
      }

      $scope.getSlotTimings=function(slot){
      if(slot == "05:00"){
      return "DAY_SLOT_BREAKFAST";
      } else if(slot == "12:00"){
      return "DAY_SLOT_LUNCH";
      } else if(slot == "15:00"){
      return "DAY_SLOT_EVENING";
      } else if(slot == "20:00"){
         return "DAY_SLOT_DINNER";
      }else if(slot == "22:00"){
      return "DAY_SLOT_POST_DINNER";
      } else if(slot == "00:00"){
      return "DAY_SLOT_OVERNIGHT";
      }else{
        return "";
      }
      }

        /*$scope.selectAvailableProduct = function (prod) {
            if (prod.selected === true) {
                prod.selected = false;
            } else {
                prod.selected = true;
            }
        };*/

        /*$scope.selectAddedProduct = function (prod) {
            if (prod.selected === true) {
                prod.selected = false;
            } else {
                prod.selected = true;
            }
        };*/

        /*$scope.addProductTagMapping = function () {
            var newSequence = [];
            $scope.groupAvialableProductSequence.map(function (item) {
                if (item.selected == true) {
                    var itemToAdd = item;
                    itemToAdd.selected = false;
                    $scope.selectedProductSequence.push(itemToAdd)
                } else {
                    newSequence.push(item);
                }
            });
            $scope.groupAvialableProductSequence = newSequence;
        };*/

        /*$scope.removeProductTagMapping = function () {
            var newSequence = [];
            $scope.selectedProductSequence.map(function (item) {
                if (item.selected == true) {
                    var itemToRemove = item;
                    itemToRemove.selected = false;
                    $scope.groupAvialableProductSequence.push(itemToRemove)
                } else {
                    newSequence.push(item);
                }
            });
            $scope.selectedProductSequence = newSequence;
        };*/

        /*$scope.setProductTagSequencing = function () {
            $scope.productSequenceMap = {};
            var unIndexedItems = [];
            $scope.selectedProductSequence.map(function (item) {
                item.selected = false;
                if (item.index != null) {
                    $scope.productSequenceMap[item.index] = item;
                } else {
                    unIndexedItems.push(item)
                }
            });
            var lastIndex = 0;
            if (Object.keys($scope.productSequenceMap).length > 0) {
                var keyList = Object.keys($scope.productSequenceMap);
                lastIndex = keyList[Object.keys($scope.productSequenceMap).length - 1];
            }
            unIndexedItems.map(function (item) {
                item.selected = false;
                lastIndex = parseInt(lastIndex) + 1;
                $scope.productSequenceMap[lastIndex] = item;
            });
            $scope.active = 1;
        };*/

        /*$scope.setPreviousIndex = function () {
            Object.keys($scope.productSequenceMap).map(function (key) {
                if ($scope.productSequenceMap[key].selected == true) {
                    var selectedItem = $scope.productSequenceMap[key];
                    var previousItem = $scope.productSequenceMap[key - 1];
                    if (previousItem != null) {
                        $scope.productSequenceMap[key - 1] = selectedItem;
                        $scope.productSequenceMap[key] = previousItem;
                    }
                }
            });
        };*/

        /*$scope.setNextIndex = function () {
            var selectedItem, nextItem, keyName;
            Object.keys($scope.productSequenceMap).map(function (key) {
                if ($scope.productSequenceMap[key].selected == true) {
                    keyName = key;
                    selectedItem = $scope.productSequenceMap[key];
                    nextItem = $scope.productSequenceMap[parseInt(keyName) + 1];
                }
            });
            if (nextItem != null) {
                $scope.productSequenceMap[parseInt(keyName) + 1] = selectedItem;
                $scope.productSequenceMap[keyName] = nextItem;
            }
        };*/

        /*$scope.selectSequencingProduct = function (product) {
            Object.values($scope.productSequenceMap).map(function (value) {
                if (value.id == product.id) {
                    value.selected = true;
                } else {
                    value.selected = false;
                }
            });
        };*/

        /*$scope.setProductTagMapping = function () {
            $scope.selectedProductSequence = Object.values($scope.productSequenceMap);
            $scope.selectedProductSequence.map(function (item) {
                item.selected = false;
            });
            $scope.active = 0;
        };*/

        $scope.setGroupProductSequence = function () {
            $scope.selectedGroup.productSequenceList = [];
            $scope.selectedGroup.updatedBy = {id: AppUtil.getCurrentUser().id};
            $scope.selectedProductSequence.map(function (value, index) {
                $scope.selectedGroup.productSequenceList.push({
                    product: {id: value.id, name: value.name},
                    productIndex: index + 1,
                    productGroup: {id: $scope.selectedGroup.groupId},
                    createdBy: {id: AppUtil.getCurrentUser().id},
                    updatedBy: {id: AppUtil.getCurrentUser().id},
                    recommended: value.recommended,
                    status: "ACTIVE"
                });
            });
            console.log($scope.selectedGroup);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.updateProductSequence,
                data: [$scope.selectedGroup]
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.selectedGroup = response.data;
                    $("#productSequenceModal").modal("hide");
                    bootbox.alert("Group mapping updated successfully.");
                } else {
                    bootbox.alert("Error updating product group mapping.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getMenus = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.getMenusShort
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.menus = response.data;
                } else {
                    bootbox.alert("Error getting menu list.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getMenuBySequenceId = function (sequenceId, callback) {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.getMenuBySequenceId + "?menuSequenceId="+sequenceId
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.menuSequenceDetail = response.data;
                } else {
                    bootbox.alert("Error getting menu list.");
                }
                if(callback != null && typeof callback == 'function') callback(response.data);
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
                if(callback != null && typeof callback == 'function') callback(null);
            });
        };


        $scope.getRecommendations = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.getRecommendation
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.recommendations = response.data;
                } else {
                    bootbox.alert("Error getting recommendation list.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


        $scope.setSelectedCloningMenu = function (cloningMenu) {
            console.log(cloningMenu)
            $scope.selectedMenuCloning = cloningMenu;
        }

        $scope.addNewMenu = function () {

            if (!$scope.newMenu.menuApp) {
                bootbox.alert("please select menu app");
                return;
            }


            var result = confirm("Are You Sure You Want To Add Menu");
            if (!result) {
                return;
            }
            $rootScope.showFullScreenLoader = true;
            $scope.newMenu.createdBy = {id: AppUtil.getCurrentUser().id};
            $scope.newMenu.cloneId = $scope.clone ? $scope.selectedMenuCloning.menuSequenceId : null;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.createMenu,
                data: $scope.newMenu
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.menus.push(response.data);
                    $scope.newMenu = {};
                    $scope.showCreateMenu = false;
                    $("#select2-selectedCloningMenuId-container").html("");
                    $scope.clone = false;
                    $scope.getMenus();

                    bootbox.alert("New menu created successfully.");
                } else {
                    bootbox.alert("Error adding menu.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.setChangeMenuStatusParams = function (menuSeqId, status) {
            $scope.menuSeqId = menuSeqId;
            $scope.status = status;
            $scope.openConfirmationModal();
        }

        $scope.changeMenuStatus = function () {
            console.log("Changing Menu Status for ", $scope.menuSeqId, " to ", $scope.status);
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.changeMenuStatus,
                data: {
                    menuSeqId: $scope.menuSeqId,
                    status: $scope.status
                }
            }).then(function success(response) {
                $scope.closeConfirmationModal();
                if (response.status === 200 && response.data != null) {
                    if (response.data === true) {
                        bootbox.alert("Menu Status Changed Successfully!", function() {
                            $scope.getMenus();
                        });
                    } else {
                        bootbox.alert("Menu Status could not be changed as the Menu is already mapped to some Units.");
                    }
                } else {
                    bootbox.alert("Error Updating Menu Status!");
                }
            });
        }

        $scope.openConfirmationModal = function () {
            $("#confirmationModal").modal("show");
        }

        $scope.closeConfirmationModal = function () {
            $("#confirmationModal").modal("hide");
        }

        $scope.openMenuSequenceModal = function (menu) {
            $scope.selectedSearchType = null;
            $scope.selection = null;
            $scope.selectedMenu = menu;
            $scope.menuSequence = [];
            $scope.getMenuBySequenceId(menu.menuSequenceId, function (menuSequenceData) {
                if(menuSequenceData == null) {
                    bootbox.alert("Error getting menu sequence data by id: ", menu.menuSequenceId);
                }
                /*if ($scope.selectedMenu.productGroupSequences != null && $scope.selectedMenu.productGroupSequences.length > 0) {
                    $scope.menuSequence = $scope.selectedMenu.productGroupSequences;
                }*/
                if (menuSequenceData.productGroupSequences != null && menuSequenceData.productGroupSequences.length > 0) {
                    $scope.menuSequence = menuSequenceData.productGroupSequences;
                }
                $scope.menuSequence.sort(function (a, b) {
                    return a.groupIndex - b.groupIndex;
                });
                $scope.menuSequence.map(function (group) {
                    if (group.subGroups != null && group.subGroups.length > 0) {
                        group.subGroups.sort(function (a, b) {
                            return a.groupIndex - b.groupIndex;
                        });
                    }
                });
                $scope.categoryGroups = [];
                $scope.subCategoryGroups = [];
                $scope.productGroups.map(function (group) {
                    if (group.groupType == 'CATEGORY') {
                        var found = false;
                        $scope.menuSequence.map(function (grp) {
                            if (!found && grp.groupId == group.groupId) {
                                found = true;
                            }
                        });
                        if (!found) {
                            if (menu.menuApp == group.menuApp) {
                                if (menu.menuType == 'SINGLE_SERVE') {
                                    if (group.menuType == 'SINGLE_SERVE') {
                                        $scope.categoryGroups.push(group);
                                    }
                                } else {
                                    $scope.categoryGroups.push(group);
                                }
                            }
                        }
                    } else {
                        var found = false;
                        $scope.menuSequence.map(function (grp) {
                            if (grp.subGroups != null && grp.subGroups.length > 0) {
                                grp.subGroups.map(function (sbGrp) {
                                    if (!found && sbGrp.groupId == group.groupId) {
                                        found = true;
                                    }
                                });
                            }
                        });
                        if (!found) {
                            if (menu.menuApp == group.menuApp) {
                                if (menu.menuType == 'SINGLE_SERVE') {
                                    if (group.menuType == 'SINGLE_SERVE') {
                                        $scope.subCategoryGroups.push(group);
                                    }
                                } else {
                                    $scope.subCategoryGroups.push(group);
                                }
                            }
                        }
                    }
                });
                $scope.menuSequenceError = null;
                $scope.alreadyPresentDaysAndSlots = {}
                            for(var seq in $scope.menuSequence){
                                            for(var i in $scope.menuSequence[seq].timings){
                                                if($scope.menuSequence[seq].timings[i]["service"]!="TAKEAWAY"){
                                                   $scope.viewDays = "";
                                                   $scope.viewSlots ="";
                                                Object.keys($scope.menuSequence[seq].timings[i]).forEach(function(key){
                                                    if(($scope.menuSequence[seq].timings[i])[key]==true){
                                                        if($scope.viewDays == ""){
                                                            $scope.viewDays = $scope.convertToDay(key);
                                                        }
                                                        else{$scope.viewDays = $scope.viewDays +"," +$scope.convertToDay(key);}
                                                    }
                                                    if(key.slice(5) == "From"){
                                                        if($scope.viewSlots == ""){
                                                            if($scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key])!=""){
                                                                $scope.viewSlots = $scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key]);
                                                            }
                                                        } else{
                                                            if($scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key])!=""){
                                                                $scope.viewSlots = $scope.viewSlots +"," + $scope.getSlotTimings(($scope.menuSequence[seq].timings[i])[key]);
                                                            }
                                                        }
                                                    }
                                                });
                                                var dataToPut = {
                                                    days : $scope.viewDays.split(","),
                                                    slots : $scope.viewSlots.split(",")
                                                }
                                                var list = [];
                                                list.push(dataToPut);
                                                if($scope.menuSequence[seq].groupName in $scope.alreadyPresentDaysAndSlots){
                                                    $scope.alreadyPresentDaysAndSlots[$scope.menuSequence[seq].groupName].push(dataToPut);
                                                }
                                                else{
                                                    $scope.alreadyPresentDaysAndSlots[$scope.menuSequence[seq].groupName] = list;
                                                }
                            //
                                            }
                                    }
                        }
                $("#menuSequenceModal").modal("show");

            });
        };


        $scope.categoryDrop = function (index, item, external, type) {
            if (!item.subGroups) {
                item.subGroups = [];
            }
            return item;
        };

        $scope.categoryBackDrop = function (index, item, external, type) {
            if (item.subGroups) {
                item.subGroups.map(function (subGroup) {
                    $scope.subCategoryGroups.push(subGroup);
                });
            }
            item.subGroups = [];
            return item;
        };

        $scope.setMenu = function () {
            $scope.menuSequenceError = null;
            var valid = true;
            for (var i = 0; i < $scope.menuSequence.length; i++) {
                if ($scope.menuSequence[i].subGroups == null || $scope.menuSequence[i].subGroups.length == 0) {
                    valid = false;
                    $scope.menuSequenceError = "Please add sub categories to " + $scope.menuSequence[i].groupName;
                    break;
                }
//                if($scope.selectedDays.length == 0 || $scope.selectedSlots.length == 0){
//                   valid = false;
//                   $scope.menuSequenceError = "Please add slots and days to " + $scope.menuSequence[i].groupName;
//                   break;
//                }
                if($scope.selectedSlots.length > 3){
                valid = false;
                $scope.menuSequenceError = "Please add only 3 slots in =" + $scope.menuSequence[i].groupName;
                }
            }
            if (valid) {
                console.log($scope.menuSequence);
                var request = angular.copy($scope.selectedMenu);
                request.productGroupSequences = [];
                //request.productGroupSequences.timings= $scope.getTimings();
                $scope.menuSequence.map(function (group, index) {
                    var sequence = group;
                    sequence.updatedBy = {id: AppUtil.getCurrentUser().id};
                    sequence.groupIndex = index + 1;
                    sequence.timings = []
                    if(group.groupName in $scope.alreadyPresentDaysAndSlots){
                        var listOfTime = $scope.alreadyPresentDaysAndSlots[group.groupName];
                        for(var i in listOfTime){
                            var data = listOfTime[i];
                            sequence.timings.push($scope.getTimingsV1(data.days,data.slots));
                        }
                    }
                    if(group.groupName in $scope.selctedDaysAndSlots){
                        var listOfTime = $scope.selctedDaysAndSlots[group.groupName];
                        for(var i in listOfTime){
                            var data = listOfTime[i];
                            sequence.timings.push($scope.getTimingsV1(data.days,data.slots));
                        }
                    }
                    sequence.subGroups.map(function (subGroup, ind) {
                        subGroup.updatedBy = {id: AppUtil.getCurrentUser().id};
                        subGroup.groupIndex = ind + 1;
                        subGroup.productSequenceList = [];
                    });
                    request.productGroupSequences.push(sequence);
                });
                console.log(request);
                $rootScope.showFullScreenLoader = true;
                $scope.newMenu.createdBy = {id: AppUtil.getCurrentUser().id};
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.channelPartner.createMenuSequence,
                    data: request
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.selectedMenu = response.data;
                        //$scope.refreshMenuSequenceCache();
                        $("#menuSequenceModal").modal("hide");
                        bootbox.alert("New menu sequence created successfully.");
                    } else {
                        bootbox.alert("Error setting menu sequence.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.selctedDaysAndSlots = {};
            $scope.mapOfDaysAndSlots = {}
        };

        /*$scope.refreshMenuSequenceCache = function() {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartnerCache.reloadUnitMenuSequence,
            }).then(function success(response) {
                if (response.status === 200 && response.data == true) {
                    $("#menuSequenceModal").modal("hide");
                    bootbox.alert("New menu sequence created successfully.");
                } else {
                    bootbox.alert("Error refreshing menu sequence.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.refreshMenuSequenceCache = function () {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartnerCache.reloadUnitMenuSequence,
                data: {}
            }).then(function success(response) {
                if (response.status === 200 && response.data == true) {
                    bootbox.alert("Cache refreshed successfully.");
                } else {
                    bootbox.alert("Error refreshing cache. Try again.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };*/

        $scope.getChannelPartnersList = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.partnerManagement.get
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.channelPartnerList = response.data;
                    $scope.channelPartners = [];
                    $scope.channelPartnerList.map(function (partner) {
                        if (partner.partnerStatus === "ACTIVE") {
                            $scope.channelPartners.push({
                                id: partner.kettlePartnerId,
                                name: partner.partnerName,
                                selected: false
                            });
                        }
                    });
                } else {
                    bootbox.alert("Error loading channel partner list.");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.setSelectedPartner = function (selectedPartner) {
            $scope.selectedPartner = selectedPartner;
        };

        $scope.setSelectedUnit = function (selectedUnit) {
            $scope.selectedUnit = selectedUnit;
        };


        $scope.addNewIcon = function () {
            if (fileService.getFile() == null
                || fileService.getFile() == undefined) {
                bootbox.alert("Please select an Image");
                return false;
            }
            var fileExt = getFileExtension(fileService.getFile().name);

            if (isImage(fileExt.toLowerCase())) {

                var mimeType = fileExt.toUpperCase();
                var fd = new FormData();
                var data=AppUtil.getUserValues();
                fd.append('mimeType', fileExt.toUpperCase());
                fd.append('file', fileService.getFile());
                fd.append('iconName', $scope.newIcon.iconName);
                fd.append('iconDescription', $scope.newIcon.iconDescription);
                fd.append('createdBy', data.userId);
                $rootScope.showFullScreenLoader = true;

                $http({
                    url: AppUtil.restUrls.channelPartner.uploadIcon,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).then(function success(response) {

                    if (response != null && response.status == 200) {
                        console.log("success:" + response);
                        $rootScope.showFullScreenLoader = false;
                        angular.element("input[type='file']").val(null);
                        $scope.newIcon = {}
                        fileService.push(null);
                        $scope.iconDetails = response.data;
                        $scope.getAllIcons();
                        bootbox.alert("Upload successful");
                    } else {
                        bootbox.alert("Error while uploading Product Image.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                    bootbox.alert("Upload failed");
                });
            } else {
                bootbox.alert('Upload Failed , File Format not Supported');
            }
        }


        $scope.getAllIcons = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.channelPartner.allIcons
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.icons = response.data;
                    console.log(response.data)
                } else {
                    bootbox.alert("Error getting icons.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


        function getFileExtension(fileName) {
            var re = /(?:\.([^.]+))?$/;
            return re.exec(fileName)[1];
        }

        function isImage(fileExt) {
            return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png" || fileExt == "svg" || fileExt == "webp";
        }


        $scope.setSelectedRow = function (idSelected) {
            $scope.subCategoryInCategory = []
            if ($scope.idSelected == idSelected.groupId) {
                $scope.groupSelected = !$scope.groupSelected;
            }
            $scope.idSelected = idSelected.groupId;

            for (var i in $scope.groupMapping) {
                if (idSelected.groupId == $scope.groupMapping[i].productGroupParentId) {
                    console.log("after comparing group id")
                    $scope.productGroups.map(function (group) {
                        if ((group.groupId === $scope.groupMapping[i].productGroupId) && (group.groupType == "SUB_CATEGORY")) {
                            console.log("in pushing data")
                            if (($.inArray(group, $scope.subCategoryInCategory)) == -1) {
                                $scope.subCategoryInCategory.push(group);
                            }
                        }
                    })
                }
            }


        };

        $scope.generateCategory = function (group) {
            if (group.groupType == 'SUB_CATEGORY') {
                var flag = true;
                $scope.groupMapping.map(function (groupMap) {
                    if (groupMap.productGroupId == group.groupId) {
                        flag = false;
                    }

                })
                return flag;
            }
            return group.groupType === 'CATEGORY'
        }

        $scope.setSelectedSearchtype = function (selectedSearchType) {
            $scope.setSelection = [];
            // if(selectedSearchType=="MENU"){
            //
            //         $scope.menus.map(function (value) {
            //             $scope.setSelection.push(value.menuSequenceName+"-"+value.menuSequenceDescription)
            //         });
            // }
            // else{
            $scope.productGroups.map(function (group) {
                if (($.inArray(group.groupTag, $scope.setSelection)) == -1) {
                    $scope.setSelection.push(group.groupTag)
                }
            })
            // }
            console.log($scope.setSelection);
            console.log($scope.categoryGroups)
            console.log($scope.subCategoryGroups)
        }

        $scope.openEditGroup = function (groups) {
            console.log(groups)
            $scope.editGroup = groups
            if (groups.menuCategoryImage != null) {
                $scope.categoryImageUploaded = true;
            }
            $("#editGroupModal").modal("show");

        }

        $scope.updateGroup = function () {
            console.log($scope.editGroup)
            $rootScope.showFullScreenLoader = true;
            var result = confirm("Are You Sure You Want  To Update Group");
            if (!result) {
                return;
            }
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.updateProductGroups,
                data: $scope.editGroup
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.editGroup = {};
                    $("#editGroupModal").modal("hide");
                    $('#select2-editGroupIconId-container').html("");
                    bootbox.alert("Group Updated successfully.");
                    $scope.categoryImageUploaded = false;

                } else {
                    bootbox.alert("Error updating product group.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


        $scope.addNewRecommendation = function () {
            console.log($scope.newRecommendation)

            if (!$scope.newRecommendation.menuRecommendationName) {
                alert("please enter recommendation name");
                return;
            }
            if (!$scope.newRecommendation.menuRecommendationDescription) {
                alert("please enter recommendation description");
                return;
            }
            if (!$scope.newRecommendation.recommendationType) {
                alert("please enter recommendation recommendationType");
                return;
            }

            var result = confirm("Are You Sure You Want To Add Recommendation");
            if (!result) {
                return;
            }
            $rootScope.showFullScreenLoader = true;
            $scope.newRecommendation.createdBy = AppUtil.getCurrentUser().id;
            $scope.newRecommendation.cloneId = $scope.newRecommendation.recommendationClone ? $scope.selectedRecommendationCloning.menuRecommendationId : null;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.createRecommendation,
                data: $scope.newRecommendation
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.recommendations.push(response.data);
                    $scope.newRecommendation = {};
                    $scope.showCreateMenu = false;
                    $("#select2-selectedCloningRecommendationId-container").html("");
                    $scope.newRecommendation.recommendationClone = false;
                    $scope.getRecommendations();

                    bootbox.alert("New Recommendation created successfully.");
                } else {
                    bootbox.alert("Error adding recommendation.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        }


        $scope.setSelectedCloningRecommendation = function (cloningRecommendation) {
            console.log(cloningRecommendation);
            $scope.selectedRecommendationCloning = cloningRecommendation;
        };


        $scope.openRecommendationModal = function (recommendation) {
            console.log(recommendation);
            $scope.productClassifications = [];
            $scope.productsInfo.forEach(function (product) {
                if (($.inArray(product.addOnProfile, $scope.productClassifications)) == -1) {
                    $scope.productClassifications.push(product.addOnProfile);
                }
            });
            console.log($scope.productClassifications);
            $scope.productList = [];
            $scope.selectedRecommendation = recommendation;
            $scope.recommendationSequence = [];
            if ($scope.selectedRecommendation.recommendationProduct != null && $scope.selectedRecommendation.recommendationProduct.length > 0) {
                $scope.selectedRecommendation.recommendationProduct.forEach(function (recommendations) {
                    $scope.productsInfo.forEach(function (product) {
                        if (product.status == 'ACTIVE') {
                            var mainProduct = false;
                            var recommendedProduct = false;
                            if (product.id == recommendations.productId) {
                                var request = {};
                                mainProduct = true;
                                request = JSON.parse(JSON.stringify(product));
                                request.recommendationTitle = recommendations.recommendationTitle;
                                request.recommendedProduct = [];
                                recommendations.recommendation.forEach(function (recommend) {
                                    $scope.productsInfo.forEach(function (matchRecommendationProduct) {
                                        if (matchRecommendationProduct.id == recommend.productId) {
                                            recommendedProduct = true;
                                            var product = matchRecommendationProduct;
                                            product.discountType = recommend.discountType;
                                            product.discountAmount = recommend.discountAmount;
                                            request.recommendedProduct.push(angular.copy(product));
                                        }
                                    })
                                });
                                $scope.recommendationSequence.push(request)
                            }
                            if (!mainProduct && !recommendedProduct) {
                                if (($.inArray(product, $scope.productList)) == -1) {
                                    $scope.productList.push(product)
                                }
                            }
                        }
                    })
                })
            } else {
                $scope.productsInfo.forEach(function (product) {
                    if (product.status == 'ACTIVE') {
                        $scope.productList.push(product);
                    }
                })
            }
            console.log("(Left) product list", $scope.productList);
            console.log("(Right) Recommendation groups:", $scope.recommendationSequence);
            $("#menuRecommendationModal").modal('show');
        };

        $scope.recommenderProductTracker = function(group, recommended) {
            return group.id+'-'+recommended.id;
        }


        function printErrors(errors) {
            var data = null;
            if (errors != null) {
                data = 'Errors : ';
                for (var index in errors) {
                    data = data + errors[index].error + ' - ' + errors[index].name + ',';

                }
            }
            return data;

        }

        function addError(array, index, errorReason) {
            array.push(getErrorMessage(index, errorReason));
        }

        function getErrorMessage(name, error) {
            var obj = {
                name: name,
                error: error
            };
            return obj;
        }

        function validateRecommendationMapping(error) {
            var productIdRecommendationTitleMap = {};
            for (var i in $scope.recommendationSequence) {
                if ($scope.recommendationSequence[i].recommendedProduct != null && $scope.recommendationSequence[i].recommendedProduct.length > 0) {

                    if($scope.recommendationSequence[i].recommendedProduct.length > 7) {
                        addError(error, $scope.recommendationSequence[i].name, "Upto 7 recommendations allowed in a group");
                    }

                    if(AppUtil.isEmptyObject($scope.recommendationSequence[i].recommendationTitle)) {
                        addError(error, $scope.recommendationSequence[i].name, "Recommendation title cannot be empty for ");
                    }else {
                        var titleList = productIdRecommendationTitleMap[$scope.recommendationSequence[i].id];
                        if(titleList === undefined) {
                            titleList = [];
                            productIdRecommendationTitleMap[$scope.recommendationSequence[i].id] = titleList;
                        }
                        if (titleList.findIndex(function (titleName) {
                            return $scope.recommendationSequence[i].recommendationTitle === titleName;
                        }) !== -1) {
                            addError(error, $scope.recommendationSequence[i].name,
                                "Found duplicate recommendation title for product")
                        } else {
                            titleList.push($scope.recommendationSequence[i].recommendationTitle);
                        }
                    }

                    $scope.recommendationSequence[i].recommendedProduct.forEach(function (value) {
                        if ($scope.recommendationSequence[i].id == value.id) {
                            addError(error, $scope.recommendationSequence[i].name, "parent product and recommended product cannot be  in same")
                            // alert("parent product and recommended product cannot be same in " + $scope.recommendationSequence[i].name);
                            return;
                        }
                    });
                }

                if ($scope.recommendationSequence[i].recommendedProduct == null || $scope.recommendationSequence[i].recommendedProduct.length == 0) {
                    addError(error, $scope.recommendationSequence[i].name, "Please select a recommendation for " +
                        $scope.recommendationSequence[i].name + " or remove it from the list");
                }
            }
            console.log("MAP is ", productIdRecommendationTitleMap);
            return error;
        }

        //check for repetitions
        function isPresentRecommendedProduct(recommendations, productId) {
            var index = recommendations.findIndex(function (rec) {
                return rec.productId === productId;
            });
            return index !== -1;
        }

        $scope.setRecommendation = function () {
            $scope.request = {};
            $scope.request = $scope.selectedRecommendation;
            console.log($scope.selectedRecommendation);
            console.log($scope.recommendationSequence);
            $scope.request.recommendationProduct = [];
            var error = [];
            error = validateRecommendationMapping(error, $scope.recommendationSequence);
            if (error.length > 0) {
                alert(printErrors(error));
                return;
            }

            $scope.recommendationSequence.map(function (product) {
                var index = 0;
                var recommendations = [];
                product.recommendedProduct.forEach(function (recommended) {

                    if(isPresentRecommendedProduct(recommendations, recommended.id)) {
                        // console.log("Repetition found skip this item", recommended.id);
                        return;
                    }
                    var recommendation = {
                        index: index + 1,
                        productId: recommended.id,
                        discountType: 'FIXED',
                        discountAmount: recommended.discountAmount
                        // dimension:recommendedProduct.dimensionProfileId
                    };
                    index++;
                    recommendations.push(recommendation);
                });

                $scope.request.recommendationProduct.push({
                    productId: product.id,
                    recommendation: recommendations,
                    recommendationTitle: product.recommendationTitle
                });
            });

            console.log("Request object is ", $scope.request);

            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.createRecommendationMapping,
                data: $scope.request
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.request = {};
                    // $scope.getMenus();
                    $("#menuRecommendationModal").modal('hide');
                    $scope.productClassifications = '';
                    $scope.productList = [];
                    $scope.selectedRecommendation = '';
                    $scope.closeRecommendationModal();
                    $scope.getRecommendations();
                    bootbox.alert(" Recommendation mapping created successfully.");
                } else {
                    bootbox.alert("Error adding recommendation.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.recommendationDrop = function (index, item, external, type) {
            // console.log("Drop recommendation header", index, item, external, type);
            if (!item.recommendedProduct) {
                item.recommendedProduct = [];
            }
            return item;
        };

        // dnd-inserted="recommendedItemDropped(index, item, external, type, group.recommendedProduct)
        // $scope.recommendedItemDropped = function (index, item, external, type, recoGroup) {
        //     console.log("Dropped in nested list/intra shifted", index, item, external, type, recoGroup);
        //     var indexToRemove = isProductAlreadyPresent(item.id, recoGroup);
        //     if (indexToRemove !== -1) {
        //         recoGroup.splice(indexToRemove, 1);
        //     }
        // };

        $scope.recommendationBackDrop = function (index, item, external, type) {
            // console.log("DROP BACK ", index, "item is", item, "type is", type);
            if (item.recommendedProduct) {
                item.recommendedProduct.map(function (subGroup) {
                    // $scope.productList.push(subGroup);
                });
            }

            for (var key in $scope.recommendationSequence) {
                if (item.id == $scope.recommendationSequence[key].id) {
                    $scope.recommendationSequence.splice(key, 1);
                    key--;
                    console.log("remove value from recom sequence");
                    break;
                }
            }

            for (var key in $scope.productList) {
                if (item.id == $scope.productList[key].id) {
                    $scope.productList.splice(key, 1);
                    key--;
                    console.log("remove value from product list");
                }
            }

            item.recommendedProduct = [];
            item.recommendationTitle = "";
            return item;
        };


        $scope.changeRecommendationStatus = function (recommendation, status) {
            var payload = {
                id: recommendation.menuRecommendationId,
                status: status
            }
            $http({
                method: 'POST',
                url: AppUtil.restUrls.channelPartner.updateRecommendationStatus,
                data: payload
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    bootbox.alert(" Status changed successfully.");
                    $scope.getRecommendations();
                } else {
                    bootbox.alert("Error adding recommendation.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        }

        $scope.filterProduct = function (product) {
            // console.log($scope.selectedProductType);
            // console.log(product);
            if ($scope.selectedProductType == "ALL") {
                return product.classification == "MENU"
            }
            else {
                return product.classification == "MENU" && product.addOnProfile == $scope.selectedProductType
            }


        }

        $scope.uploadCategoryImage = function (type) {
            console.log(angular.element(document.querySelector('listImage')));
            if (fileService.getFile() == null
                || fileService.getFile() == undefined) {
                bootbox.alert("Please select an Image");
                return false;
            }
            var fileExt = getFileExtension(fileService.getFile().name);
            if (isImage(fileExt.toLowerCase())) {
                var fd = new FormData();
                fd.append("file", fileService.getFile());
                $rootScope.showDetailLoader = true;
                var URL = AppUtil.restUrls.channelPartner.uploadCategoryImage;
                $http({
                    url: URL,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showDetailLoader = false;
                    angular.element("input[type='file']").val(null);
                    fileService.push(null);
                    if (type === 'EDIT') {
                        $scope.editGroup.menuCategoryImage = response.code;
                    } else if (type === 'CREATE') {
                        $scope.newGroup.menuCategoryImage = response.code;
                    }
                    // $scope.offerObject.menuCategoryImage = {url: response.url, name: response.name};
                    alert("Image Added");
                    $scope.categoryImageUploaded = true;
                }).error(function (response) {
                    $rootScope.showDetailLoader = false;
                    alert("Error while uploading Image");
                });
            }
            else {
                alert("Cannot upload image");
                return false;
            }
        }

        $scope.clearImage = function (image, type) {
            if (image == 'CATEGORY_IMAGE') {
                $scope.categoryImageUploaded = false;
                type == 'EDIT' ? $scope.editGroup.menuCategoryImage = null : $scope.newGroup.menuCategoryImage = null;
            }
        }

        $('.modal').css('overflow-y', 'auto');

        $scope.openProductImageModal = function (imageUrl) {
            $scope.imageSrc = imageUrl;
            if (imageUrl == null) {
                alert("Image not found!")
            }
            else {
                $("#displayImageModal").modal("show");
            }

        };

        $scope.refreshUnitMenuMappingCache = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.masterCacheManagement.reloadUnitMenuSequenceMapping,
                data: {}
            }).then(function success(response) {
                if (response.status === 200 && response.data == true) {
                    bootbox.alert("Cache refreshed successfully.");
                } else {
                    bootbox.alert("Error refreshing cache. Try again.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.productSearchFilter = function(group) {
            if (!$scope.productSearch) return true;
            var search = $scope.productSearch.toLowerCase();
            return (group.id && group.id.toString().includes(search)) || 
                (group.name && group.name.toLowerCase().includes(search));
        };

        $scope.recommendationSearchFilter = function(group) {
            if (!$scope.recommendationSearch) return true;
            var search = $scope.recommendationSearch.toLowerCase();
            return (group.id && group.id.toString().includes(search)) || 
                (group.name && group.name.toLowerCase().includes(search)) ||
                (group.recommendationTitle && group.recommendationTitle.toLowerCase().includes(search));
        };

        $scope.toggleAccordion = function(index) {
            $scope.openIndex = $scope.openIndex === index ? null : index;
        };

        $scope.closeRecommendationModal = function () {
            $scope.productSearch = "";
            $scope.recommendationSearch = "";
            $scope.openIndex = null;
            $scope.openAll = false;
        }

        function getCurrentMenuType(){
                        var getHour = new Date();
                        var hour=localStorage.getItem("serverTime");
                        hour=Math.abs(hour-getHour.getHours());
                        if ((hour >= 5 && hour <= 11)) {
                            return "DAY_SLOT_BREAKFAST";
                        } else if (hour >= 12 && hour <= 14) {
                            return "DAY_SLOT_LUNCH";
                        } else if (hour >= 15 && hour <= 19) {
                            return "DAY_SLOT_EVENING";
                        } else if (hour >= 20 && hour <= 21) {
                            return "DAY_SLOT_DINNER";
                        } else if ((hour >= 22 && hour <= 23)) {
                            return "DAY_SLOT_POST_DINNER";
                        } else if (hour >= 0 && hour <= 4) {
                            return "DAY_SLOT_OVERNIGHT";
                        }
                        return "DEFAULT";
                    };
        
                    $scope.customSearch = function (menu) {
                        var menuApp = $scope.newGroup.menuApp;
                        var menuStatus = $scope.newGroup.menuStatus;
                    
                        var appMatch = !menuApp || menuApp === 'ALL' || menu.menuApp === menuApp;
                        var statusMatch = !menuStatus || menuStatus === 'ALL' || menu.menuStatus === menuStatus;
                    
                        return appMatch && statusMatch;
                    };
                    
    }]
);
