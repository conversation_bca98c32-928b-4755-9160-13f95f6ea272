/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("dsrConfigurationCtrl", function($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location,
                                              $timeout, AppUtil) {

    $scope.init = function() {
        $scope.units= {};
        $scope.statusreport=['ACTIVE','INACTIVE'];
        $scope.reportList=['LOGIX_SIMPLE','DLF_TERMINAL_WISE','PATHFINDER','AIRIA_SIMPLE'];
        $scope.selectedpartnerID = null;
        $scope.completedList = [];
        getPartnerIDs("CAFE");
    };
    function getPartnerIDs(category) {
        $http({
            method : 'GET',
            url : AppUtil.restUrls.unitMetaData.allUnits,
            params : {
                "category" : category
            }
        }).then(function success(response) {
            $scope.units = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }
    $scope.selectPartnerId = function(value) {
        $scope.tenant=null;
        $scope.ftpserver=null;
        $scope.ftpport=null;
        $scope.username=null;
        $scope.password=null;
        $scope.status=null;
        $scope.reporttype=null;
        $scope.directory=null;
        $scope.protocol=null;
        $scope.email=null;
        for(var partnerId in $scope.units){
            if(partnerId.name==value){
                $scope.selectedpartnerID = partnerId.id;
            }
            break;
        }
        console.log($scope.selectedpartnerID.id);
        $scope.showTableData();
    }

    $scope.showTableData = function(){
        $http({
            method : 'GET',
            url : AppUtil.restUrls.dsrManagement.getDsrConfig,
            params : {
                "partnerId" : $scope.selectedpartnerID.id
            }
        }).then(function success(response){
            $scope.dsrData = response.data;
            console.log($scope.dsrData);
            if(angular.equals([], $scope.dsrData)){
                $scope.resultFound=false;
            }
            else {
                $scope.resultFound=true;
            }
            for(var x in $scope.dsrData){
                    if($scope.dsrData[x].mappingType=="TENANT_ID"){
                        $scope.tenant=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="FTP_SERVER"){
                        $scope.ftpserver=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="FTP_PORT"){
                        $scope.ftpport=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="USERNAME"){
                        $scope.username=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="PASSWORD"){
                        $scope.password=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="STATUS"){
                        $scope.status=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="REPORT_TYPE"){
                        $scope.reporttype=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="DIRECTORY"){
                        $scope.directory=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="PROTOCOL"){
                        $scope.protocol=$scope.dsrData[x].mappingValue;
                    }
                    if($scope.dsrData[x].mappingType=="REPORT_EMAIL"){
                        $scope.email=$scope.dsrData[x].mappingValue;
                    }
                }
        }, function error(response) {
            console.log("error:" + response);
        });
    }
    function responseData(){
        $scope.list=[];
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"TENANT_ID", mappingValue: $scope.tenant});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"FTP_SERVER", mappingValue: $scope.ftpserver});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"FTP_PORT", mappingValue: $scope.ftpport});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"USERNAME", mappingValue: $scope.username});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"PASSWORD", mappingValue: $scope.password});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"STATUS",mappingValue:$scope.status});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"REPORT_TYPE",mappingValue: $scope.reporttype});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"DIRECTORY", mappingValue: $scope.directory});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"PROTOCOL", mappingValue: $scope.protocol});
            $scope.list.push({partnerId: $scope.selectedpartnerID.id,mappingType:"REPORT_EMAIL", mappingValue: $scope.email});
            console.log("list:",$scope.list);
    }

    $scope.updateDsrConfig=function (){
        responseData();
        if(!$scope.email){
            alert("Enter Email correctly");
            return;
        }
        var nullCheck=requiredMandatoryFieldsCheck();
        if(nullCheck){
        alert("please fill all the mandatory fields");
        return;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method : 'POST',
            url : AppUtil.restUrls.dsrManagement.updateDsrConfig,
            data:$scope.list
        }).then(function success(response){
        if(response.status = 200 || response.data){
            $scope.updatedDsr=response.data;
             $rootScope.showFullScreenLoader = false;
             alert("Updated Successfully!!!!");
             }
        }),function error(response){
            $rootScope.showFullScreenLoader = false;
            alert("Error while updating configuration!!!!");
         };
        window.location.reload();
    };

    $scope.addDsrConfig=function (){
        responseData();
        if(!$scope.email){
            alert("Enter Email correctly");
            return ;
        }
        var nullCheck=requiredMandatoryFieldsCheck();
                if(nullCheck){
                alert("please fill all the mandatory fields");
                return;
                }
        console.log("Body in api -------------->", $scope.list);
        $rootScope.showFullScreenLoader = true;
        $http({
            method : 'POST',
            url : AppUtil.restUrls.dsrManagement.addDsrConfig,
            data:$scope.list
        }).then(function success(response){
        if(response.status = 200 || response.data){
            $scope.addDsr=response.data;
            $rootScope.showFullScreenLoader = false;
            alert("Added Successfully!!!!");
            }
        }),function error(response){
         $rootScope.showFullScreenLoader = false;
        alert("Error while adding configuration!!!!");
        }
        window.location.reload();
    };
    function requiredMandatoryFieldsCheck(){
        return (!$scope.tenant  ||
                !$scope.ftpserver ||
                !$scope.ftpport ||
                !$scope.username ||
                !$scope.password ||
                !$scope.status ||
                !$scope.reporttype ||
                !$scope.protocol);
    }
});
