/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp
		.controller(
				"brmUnitWiseController",
				function($rootScope, $scope, $location, $http, AppUtil,
						$cookieStore) {

					$scope.init = function() {
						$scope.filesSelected = [];
						$scope.selectedOption = "consolidated";
						$scope.getUnits();
					};

                    		$scope.getUnits = function () {
					$http({
                            method: 'GET',
                            url: AppUtil.restUrls.unitMetaData.allUnits,
                            params: {
                                category: 'CAFE'
                            }
                        }).then(function success(response) {
                            $rootScope.showFullScreenLoader = false;
                            $scope.cafelist = response.data;
                           $scope.unitlist = $scope.cafelist;
                            $scope.selectedUnit = '';
                        }, function error(response) {
                            $rootScope.showFullScreenLoader = false;
                            console.log("error:" + response);
                        });
                        }

					$scope.generateRevenueCertificate = function(date,unit) {
						if (date == undefined || date == null || unit == null) {
							return;
						}
						if($scope.selectedOption == "segregated"){
						$rootScope.showFullScreenLoader = true;
						var data = date.split("-")
						var year = data[0];
						var month = data[1];
						var unitId = unit.id;
						var URL = AppUtil.restUrls.reportMetaData.revenueCertificate;
						$http({
							method: 'GET',
							url: URL,
							params: {
								month: month,
								year: year,
								type: "Gross",
								unitId : unitId
							},
							responseType: "arraybuffer"
						}).then(function success(response) {
							$scope.processRevenueCertificateResponse(response, "Gross");
							$http({
								method: 'GET',
								url: URL,
								params: {
									month: month,
									year: year,
									type: "DineInDelivery",
									unitId : unitId
								},
								responseType: "arraybuffer"
							}).then(function success(response) {
								$scope.processRevenueCertificateResponse(response, "DineInDelivery");
							}, function error(response) {
								$rootScope.showFullScreenLoader = false;
								console.log("error:" + response);
							});
						}, function error(response) {
							$rootScope.showFullScreenLoader = false;
							console.log("error:" + response);
						});
						}
						if($scope.selectedOption == "" || $scope.selectedOption == "consolidated"){
						$rootScope.showFullScreenLoader = true;
                        var data = date.split("-")
                        var year = data[0];
                        var month = data[1];
                        var unitId = unit.id;
                        var URL = AppUtil.restUrls.reportMetaData.revenueCertificate;
                        	$http({
                        		method: 'GET',
                        		url: URL,
                        		params: {
                        		         month: month,
                        		         year: year,
                        				 reportType: consolidated,
  								         unitId : unitId
                        				},
                        	   responseType: "arraybuffer"
                        		}).then(function success(response) {
                                  		$scope.processRevenueCertificateResponse(response, "Consolidated");
                                  		}, function error(response) {
                                  		$rootScope.showFullScreenLoader = false;
                                  		console.log("error:" + response);
                                  });
                        }
                         else{console.log($scope.selectedOption)};
					};


					$scope.processRevenueCertificateResponse = function(response,type) {
						if (type === "DineInDelivery") {
							$rootScope.showFullScreenLoader = false;
						}else if(type === "Consolidated"){
						$rootScope.showFullScreenLoader = false;
						}
						if (response) {
							var blob = new Blob([response.data], {type: 'application/zip'});
							if(type == "Consolidated"){
						     saveAs(blob,"RevenueCertificates.zip");
							}
							else{
							saveAs(blob, type + "_RevenueCertificates.zip");
							}
							$scope.reportStatus = " Report Generated Successfully";
							$("#alertModal").modal("show");
						} else {
							$scope.reportStatus = "Error while Generating Revenue Certificate, Consult the Technical Department";
							$("#alertModal").modal("show");
						}
					};

				});