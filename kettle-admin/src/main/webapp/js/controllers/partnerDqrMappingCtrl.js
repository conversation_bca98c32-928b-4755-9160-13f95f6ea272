/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("partnerDqrMappingCtrl", function ($scope, $rootScope, $http, AppUtil) {

  $scope.init = function () {

    $scope.unitlist = [];
    $scope.selectedUnit = null;
    $scope.statusList = ["ACTIVE", "IN_ACTIVE"];
    $scope.dqrMappingStatus = null;
    $scope.partnerName = null;
    $scope.merchantId = null;
    $scope.terminalId = null;
    $scope.merchantKey = null;
    $scope.tId = null;
    $scope.secretKey = null;
    $scope.version = null;
    $scope.partnerDqrMapperList = []
    $scope.newPartnerEdcMapperList = []
  }

  $scope.toggelEditMode = function () {
    $scope.editMode = !$scope.editMode;
    if ($scope.editMode == true) {
      $scope.statusList = ["ACTIVE", "IN_ACTIVE", "All"];
    }
    else {
      $scope.statusList = ["ACTIVE", "IN_ACTIVE"];
    }

  }

  $scope.getPartnerDqrMapperList = function () {
    $scope.newPartnerDqrMapperList = []
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'GET',
      url: AppUtil.restUrls.unitMetaData.getPartnerDqrMapperList,
      params: {
        "status": $scope.dqrMappingStatus
      }
    }).then(function success(response) {

      $rootScope.showFullScreenLoader = false;
      if (response.data != null) {
        $scope.showList = !$scope.showList;
        $scope.partnerDqrMapperList = response.data;
      }
    }, function error(response) {
      $rootScope.showFullScreenLoader = false;
      console.log("error:" + response);
      bootbox.alert("Something Went Wrong Try Again!");

    });
  }

  $scope.changeStatus = function (partnerDqrMapper) {
    if (partnerDqrMapper.status == "ACTIVE") {
      partnerDqrMapper.status = "IN_ACTIVE"
    }
    else {
      partnerDqrMapper.status = "ACTIVE"
    }
    $scope.newPartnerDqrMapperList.push(partnerDqrMapper);
  }


  $scope.selectCafesMapping = function () {
    if ($scope.dqrMappingStatus == null) {
      bootbox.alert("Please Select Status First")
      return;
    }
    $("#unitpartnerEdcModal").modal("show");
  }

  $http({
    method: 'GET',
    url: AppUtil.restUrls.unitMetaData.allUnits,
    params: {
      category: 'CAFE'
    }
  }).then(function success(response) {
    $rootScope.showFullScreenLoader = false;
    $scope.cafelist = response.data;
    $scope.unitlist = $scope.cafelist;
  }, function error(response) {
    $rootScope.showFullScreenLoader = false;
    console.log("error:" + response);
  });

  $scope.storeSelectedCafes = [];
  $scope.multiSelectSettingsCafes = {
    showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
    scrollableHeight: '250px', trackBy: 'id', clearSearchOnClose: true
  };


  $scope.savePartnerDqrMapping = function () {


    if ($scope.dqrMappingStatus == null || $scope.dqrMappingStatus.trim().length == 0) {
      bootbox.alert("Invalid Status")
      return;
    }
    if ($scope.selectedUnit == null) {
      bootbox.alert("Please Select Cafe")
      return;
    }
    if ($scope.partnerName == null || $scope.partnerName.trim().length == 0) {
      bootbox.alert("Invalid Partner Name")
      return;
    }
    if ($scope.merchantId == null || $scope.merchantId.trim().length == 0) {
      bootbox.alert("Invalid Merchant Id")
      return;
    }
    if ($scope.merchantKey == null || $scope.merchantKey.trim().length == 0) {
      bootbox.alert("Invalid Merchant Key")
      return;
    }

    $scope.selectedUnit = JSON.parse($scope.selectedUnit);

    var data = {
      "unitId" :  $scope.selectedUnit.id,
      "status": $scope.dqrMappingStatus,
      "partnerName": $scope.partnerName,
      "merchantId": $scope.merchantId,
      "merchantKey": $scope.merchantKey
    }
    console.log("saving data ::::", data);
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'POST',
      url: AppUtil.restUrls.unitMetaData.addPartnerDqrMapping,
      data: data
    }).then(function success(response) {
      $rootScope.showFullScreenLoader = false;
      if (response.data == true) {
        bootbox.alert("Details Saved Successfully")
        $scope.unitlist = [];
        $scope.selectedUnit = null;
        $scope.statusList = ["ACTIVE", "IN_ACTIVE"];
        $scope.dqrMappingStatus = null;
        $scope.partnerName = null;
        $scope.merchantId = null;
        $scope.terminalId = null;
        $scope.merchantKey = null;
        $scope.tId = null;
        $scope.version = null;
        $scope.partnerDqrMapperList = []
        $scope.newPartnerDqrMapperList = []
      }
    }, function error(response) {
      $rootScope.showFullScreenLoader = false;
      console.log("error:" + response);
      bootbox.alert("Something Went Wrong Try Again!");

    });


  };

  $scope.updatePartnerDqrMapperList = function () {
    $rootScope.showFullScreenLoader = true;
    $http({
      method: 'POST',
      url: AppUtil.restUrls.unitMetaData.updatePartnerDqrMapperList,
      data: $scope.newPartnerDqrMapperList
    }).then(function success(response) {
      $rootScope.showFullScreenLoader = false;
      if (response.data == true) {
        bootbox.alert("Details Saved Successfully")
        $scope.unitlist = [];
        $scope.selectedUnit = null;
        $scope.statusList = ["ACTIVE", "IN_ACTIVE", "All"];
        $scope.dqrMappingStatus = null;
        $scope.partnerName = null;
        $scope.merchantId = null;
        $scope.terminalId = null;
        $scope.merchantKey = null;
        $scope.tId = null;
        $scope.version = null;

        $scope.partnerDqrMapperList = []
        $scope.newPartnerDqrMapperList = []

      }
      else {
        bootbox.alert("Something Went Wrong Try Again!");
      }
    }, function error(response) {
      $rootScope.showFullScreenLoader = false;
      console.log("error:" + response);
      bootbox.alert("Something Went Wrong Try Again!");

    });


  };

});
