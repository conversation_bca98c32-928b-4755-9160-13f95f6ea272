adminapp.controller("unitPriceProfileController", function ($log, AuthService, $cookieStore, $rootScope, 
    $scope, $http, $location, AppUtil,$timeout , fileService) {

    var dimensionInfo = null;
    var selectedDimension = null;

    $scope.init = function () {
        $rootScope.enableScreenFilter = true;
        $scope.regions = [];
        $scope.trimmedRegions=[];
        $scope.getAllBrands();
        $scope.getALLProfiles();
        $scope.getChannelPartnersList();
        getRegionDetails();
        initCheckBoxModal();
        $scope.selectedRegion = null;
        $scope.selectedPartnerId = null;
        $scope.selectedPartner = 1;
        $scope.selectedBrand = 1;
        $scope.selectedBrandId = null
        $scope.unitType = ["CAFE", "COD", "EMPLOYEE_MEAL"];
        $scope.pricingStatusList = ["ACTIVE","IN_ACTIVE"];
        $scope.unitStatusList = ["ALL","ACTIVE","IN_ACTIVE"];
        $scope.filters = {};
        $scope.filteredData = [];
        $scope.fetchAll  =false;
    };

    $scope.toggleFetchAll = function () {
        $scope.fetchAll = !$scope.fetchAll;
    };

    function getStatusMap(){
        return [
            {id:1, name:"IN_ACTIVE", value:"IN_ACTIVE"},
            {id:2, name:"ACTIVE", value:"ACTIVE"}
        ];
    }

    function getLowerCase(value){
        if(value == null){return null};
        return value.toString().toLowerCase();
    }

    
    $scope.customFilter = function (row) {
        var filters = $scope.filters || {};
      
            // if (!filters[key]) return true; // Skip empty filters
            // if (!item[key]) return false; // Skip undefined fields
            // return item[key].toString().toLowerCase() === filters[key].toString().toLowerCase();
            return (
                (!$scope.filters.unitName || getLowerCase(row.unit.name).includes(getLowerCase($scope.filters.unitName)))
                );
    };

    $scope.applyFilters = function () {
        console.log('Filters applied:', $scope.filters);
        $scope.filteredData =  $scope.unitPriceProfileMappings.filter(function (row) {
        return (
          (!$scope.filters.unitName || getLowerCase(row.unit.name).includes(getLowerCase($scope.filters.unitName))) &&
          (!$scope.filters.unitStatus || $scope.filters.unitStatus == 'ALL' || getLowerCase(row.unit.status) == (getLowerCase($scope.filters.unitStatus)))
 
     );
        });
        // if (!$scope.$$phase) {
        //     $scope.$apply();
        // }
        $timeout(function () {
            $('.select2-element').each(function () {
                if ($(this).hasClass("select2-hidden-accessible")) {
                    $(this).select2('destroy');  // Destroy previous instance
                }
                $(this).select2(); // Reinitialize Select2
            });
        }); // Delay to ensure Angular updates the DOM first
       }

     $scope.getUnitPriceProfileSheet = function(){
        var updatedMappings = $scope.unitPriceProfileMappings.filter(function(mapping){
            return mapping.checked;
         });
           $http(
            {
                method: 'POST',
                url: AppUtil.restUrls.priceProfileManagement.getUnitPriceProfileMappingsSheet,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                data: updatedMappings
            })
            .then(
                function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != undefined && response != null) {
                        var fileName = "UNIT_PRICE_PROFILE_SHEET" + " - " + Date.now() + ".xlsx";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }
                },
                function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                    alert("Unable to download Sheet");
                });
    }
    

    $scope.selectProfileForRows = function(newProfileJson , checkAll){
        if(checkAll){
            var newProfile  = JSON.parse(newProfileJson);
            var priceProfile = {id : newProfile.priceProfileDataId , name : newProfile.priceProfileName, code : ''}
            var updatedMappings = $scope.filteredData.filter(function(mapping){
                return mapping.checked;
         }).forEach(function(mp){
            if(mp.priceProfile!= null && mp.priceProfile.name == priceProfile.name){

            }else{
                mp.priceProfile = priceProfile;
                mp.priceProfileVersion = null;
            }
         });

        }
    
    }

    $scope.selectProfileVersionForRows = function(newProfileVersionJson , checkAll){
        if(checkAll){
            var newProfileVersion  = JSON.parse(newProfileVersionJson);
            var updatedMappings = $scope.filteredData.filter(function(mapping){
                return mapping.checked;
         }).forEach(function(mp){
            mp.priceProfileVersion = newProfileVersion.versionNo;
         });

        }
    
    }
    

    $scope.saveMappings = function(){
        var updatedMappings = $scope.unitPriceProfileMappings.filter(function(mapping){
               return mapping.checked;
        });
        if(updatedMappings.length == 0){
            alert("Please Make Atleast One Selection !!");
            return;
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.priceProfileManagement.addUnitPriceProfileMappings,
            data : updatedMappings,
            params : {partnerId: $scope.selectedPartner,
                brandId: $scope.selectedBrand}
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status === 200 && response.data != null) {
                bootbox.alert("Successfully Saved Mappings");
            } else {
                bootbox.alert("Error saving Mappings");
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    }

    $scope.uploadBulkUnitPriceSheet = function () {
        if (fileService.getFile() == null || fileService.getFile() == undefined) {
            bootbox.alert("Please select a .xlsx file");
            return;
        }
        console.log("File is", fileService.getFile());
        if (confirm("Do you want to Upload and Update Mappings?")) {
            var fd = new FormData();
            fd.append("file", fileService.getFile());
            fd.append("updatedBy", $rootScope.userData.id);
            $rootScope.showFullScreenLoader = true;
            $http({
                url: AppUtil.restUrls.priceProfileManagement.uploadBulkUnitPriceSheet,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined,
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                $scope.unitPriceProfileFile = null;
                console.log(response);
                if (response.errorMessage == null) {
                        var alertMessage = "File Uploaded";
                        alertMessage = alertMessage +  
                            "\nRecords Changed: " + response.recordsUpdated +
                            "\nRecords Added: " + response.recordsAdded +
                            "\nRecord with errors: " + response.recordsWithErrors +
                            "\errors: " + response.errors;
                        alert(alertMessage);
                }
                else {
                    alert(response.errorMessage);
                }
  
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error while uploading Unit Pricing Sheet");
            });
        }
        ;
    }

    function initCheckBoxModal() {
        $scope.checkBoxModal = {};

        $scope.checkBoxModal.checkAll = false;
        $scope.checkBoxModal.checkAllPrice = false;
        $scope.checkBoxModal.checkAllProfile = false;
        $scope.checkBoxModal.checkAllStatus = false;
        $scope.checkBoxModal.checkAllAlias = false;
        $scope.checkBoxModal.checkAllDescription = false;
        $scope.checkBoxModal.checkAllDeliveryOnlyProducts= false;

        $scope.checkBoxModal.updatedPrice = '';
        $scope.checkBoxModal.updatedProfile = '';
        $scope.checkBoxModal.updatedStatus = null;
        //ss
        $scope.checkBoxModal.updatedProductAlias = '';
        $scope.checkBoxModal.updatedDimensionDescription = '';
        $scope.checkBoxModal.updatedDeliveryOnlyProducts= null;

        $scope.productListDetails = {};
        $scope.requestObject = [];
    }

    $scope.getChannelPartnersList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.channelPartner.getActiveChannelPartners
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.channelPartnerList = response.data;
                $scope.channelPartners = [];
                $scope.channelPartnerList.map(function (partner) {
                    if (partner.status === "ACTIVE") {
                        $scope.channelPartners.push({
                            id: partner.id,
                            name: partner.name,
                            selected: false
                        });
                    }
                });
            } else {
                bootbox.alert("Error loading channel partner list.");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    function sortProductList(){
        $scope.productsInfo.sort((a, b) => {
            var fa = a.name.replace(/\s/g,'').toLowerCase();
            var fb = b.name.replace(/\s/g,'').toLowerCase();
            return fa.localeCompare(fb);
        });
    }

    $scope.getAllProducts = function () {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.productMetaData.products
        }).then(function success(response) {
            $scope.productsInfo = response.data;
            sortProductList();
            $scope.allProdcutsInfo = $scope.productsInfo;
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    function mapPricingProfiles(){
        for(var i in $scope.pricingProfiles){
            $scope.pricingProfileNames.push($scope.pricingProfiles[i].name);
            $scope.pricingProfileMap.set($scope.pricingProfiles[i].name, $scope.pricingProfiles[i].id);
        }
    }

    $scope.getAllDimensionsAndPriceProfile = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.listTypes
        }).then(function success(response) {
            dimensionInfo = response.data.DIMENSION;
            var pricingProfiles = response.data.PRICING_PROFILE;
            if (pricingProfiles != null && pricingProfiles.length > 0) {
                var category = pricingProfiles[0];
                $scope.pricingProfiles = [];
                if (angular.equals(category['detail']['name'], "Pricing Profile") && angular.equals(category['detail']['status'], "ACTIVE")) {
                    for (var itr in category['content']) {
                        if (angular.equals(category['content'][itr].status, "ACTIVE")) {
                            $scope.pricingProfiles.push(category['content'][itr]);
                        }
                    }
                }
                mapPricingProfiles();
            }
        });
    };

    $scope.getAllBrands = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.brandMetaData.getAllBrands
        }).then(function success(response) {
            $scope.brandList = response.data;
            // var allBrands = {};
            // allBrands.brandId = -1;
            // allBrands.brandName = 'ALL';
            // $scope.brandList.unshift(allBrands);

        });
    };

    $scope.filterProductsBrandWise = function () {
        if($scope.allProdcutsInfo != null){
            if(!angular.equals($scope.selectedBrand, '-1')){
                var filteredProducts = [];
                for(var i in $scope.allProdcutsInfo){
                    if(angular.equals($scope.allProdcutsInfo[i].brandId.toString(), $scope.selectedBrand.toString())){
                        filteredProducts.push($scope.allProdcutsInfo[i]);
                    }
                }
                $scope.productsInfo = filteredProducts;
            } else if(!angular.equals($scope.productsInfo.length, $scope.allProdcutsInfo.length)){
                $scope.productsInfo = $scope.allProdcutsInfo;
            }
        }
    };

    $scope.changeProduct = function (selectedProductId) {
        if (selectedProductId == null || selectedProductId === undefined) {
            return false;
        }
        $scope.productDimension = {};
        $scope.prodDetails.dimensionId = null;
        $scope.selectedRegion = null;
        selectedDimension = null;
        $scope.regions = [];
        for (var i = 0; i < $scope.productsInfo.length; i++) {
            if ($scope.productsInfo[i].id == selectedProductId) {
                $scope.selectedProduct = $scope.productsInfo[i];
                break;
            }
        }

        for (var i = 0; i < dimensionInfo.length; i++) {
            if (dimensionInfo[i].detail.id == $scope.selectedProduct.dimensionProfileId) {
                $scope.productDimension = dimensionInfo[i];
                break;
            }
        }
        initCheckBoxModal();
    };

    $scope.changeDimension = function () {
        for (var i = 0; i < $scope.productDimension.content.length; i++) {
            if ($scope.productDimension.content[i].id == $scope.prodDetails.dimensionId) {
                selectedDimension = $scope.productDimension.content[i];
                break;
            }
        }
        $scope.selectedRegion = null;
        getRegionDetails();
        initCheckBoxModal();
    };
    $scope.storeSelectedRegion=[];
    $scope.multiSelectSettings = {
        showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
        scrollableHeight: '200px'
    };

    $scope.selectProfile = function(profile ,detail){
        var cloneProfile  = JSON.parse(profile);
        detail.priceProfileVersion = null;
       // $scope.selectedInput.priceProfile = cloneProfile;
        detail.versions = cloneProfile.priceProfileVersions.filter(function(version){
            return version.status  == "ACTIVE";
        });
        detail.priceProfile = {id : cloneProfile.priceProfileDataId , name : cloneProfile.priceProfileName, code : ''}
    }

    $scope.selectProfileForAll = function(profileJson){
        var selectAllProfile = JSON.parse(profileJson);
        $scope.allProfileVersions = selectAllProfile.priceProfileVersions;
    }

    $scope.getALLProfiles = function(){
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.findPriceProfiles,
            dataType: 'json',
            method: 'GET',
            params : {status : 'ACTIVE'},
            headers: {
                "Content-Type": "application/json"
            }
        }).then(function success(response) {
            //console.log(JSON.stringify(response));
            $rootScope.showFullScreenLoader = false;
            $scope.profiles = response.data;
            //alert("Successfully fetched Price Profile!!");
  
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
        
      }
    $scope.getAllUnitPriceProfileMapping=function(){
        if($scope.storeSelectedRegion.length==0)
        {
            alert("Please select region")
            return;
        }
       var regionName='';
       for(var i=0;i<$scope.storeSelectedRegion.length;i++)
       {
           regionName+=$scope.storeSelectedRegion[i]+' ';
       }
    
        $rootScope.showFullScreenLoader = true;
        $http({
            url: AppUtil.restUrls.priceProfileManagement.getUnitPriceProfileMappings,
            method: "GET",
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            params : {
                unitCategory: $scope.prodDetails.unitType,
                unitRegion : regionName,
                partnerId: $scope.selectedPartner,
                brandId: $scope.selectedBrand,
                fetchAll : $scope.fetchAll
            }
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            initCheckBoxModal();
            console.log("Response on loading units  ::::", response.data);
            $scope.unitPriceProfileMappings = response.data;
            $scope.allProductListDetails = $scope.productListDetailsAll;
            $scope.applyFilters();
            $scope.productListDetails = $scope.productListDetailsAll.filter(function(data){
                                return data.unit.status == "ACTIVE";
             });
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };
    $scope.getUnitProductDetails = function (region) {
        bootbox.confirm("Do you want to load details of " + region + " region ?", function (result) {
            if (result == true) {
                $scope.selectedRegion = region;
                console.log(region);
                $rootScope.showFullScreenLoader = true;
                var payload = $.param({
                    unitCategory: $scope.prodDetails.unitType,
                    unitRegion: (region == "ALL" ? null : region),
                    productId: $scope.prodDetails.productId,
                    dimensionId: $scope.prodDetails.dimensionId
                });

                $http({
                    url: AppUtil.restUrls.productMetaData.productPriceMapping,
                    method: "POST",
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    data: payload
                }).then(function success(response) {
                    initCheckBoxModal();
                    $scope.productListDetailsAll = response.data;
                    $scope.productListDetails = $scope.productListDetailsAll.filter(function(data){
                    return data.unit.status == "ACTIVE";
                    });

                    	// console.log("$scope.productListDetails ",$scope.productListDetails.length);

                    for (var i = 0; i < $scope.productListDetails.length; i++) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id] = {};
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].checked = false;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].price = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].status = null;
                        //ss
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].description = '';
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail = $scope.productListDetails[i].price;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].id = $scope.productListDetails[i].id;
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].unit = $scope.productListDetails[i].unit;
                    }
                    getRecipeProfilesOfProduct($scope.prodDetails.productId, $scope.prodDetails.dimensionId);
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("error:" + response);
                });
            }
        });
    };

    $scope.updateAll = function () {
        if ($scope.checkBoxModal.checkAll === true) {
            for (var i = 0; i < $scope.filteredData.length; i++) {
                $scope.filteredData[i].checked = true;
            }
        } else if ($scope.checkBoxModal.checkAll === false) {
            for (var i = 0; i < $scope.filteredData.length; i++) {
                $scope.filteredData[i].checked = false;
            }
        }
    };

    $scope.changeRow = function (checked, unitProdDetails, detail) {
        console.log("prodDetails[detail.unit.id].checked",checked);
        if(checked && detail != null){
            unitProdDetails.price = detail.price;
            unitProdDetails.profile = detail.profile;
            unitProdDetails.status=detail.status;
            unitProdDetails.alias=detail.aliasProductName;
            unitProdDetails.description=detail.dimensionDescriptor;
            unitProdDetails.isDeliveryOnlyProduct = detail.isDeliveryOnlyProduct;
        }else{
            unitProdDetails.price = '';
            unitProdDetails.profile = '';
            unitProdDetails.alias='';
            unitProdDetails.description='';
            //check on ui if there is a need to set this flag
            // unitProdDetails.isDeliveryOnlyProduct = false;
        }
    };

    /*$scope.changeProfile = function(newProfile, detail){

    };*/

    $scope.changeAllProfile = function () {
        if ($scope.checkBoxModal.checkAllProfile === true) {
            if ($scope.checkBoxModal.updatedProfile == '') {
                alert("Please enter new profile.");
                $scope.checkBoxModal.checkAllProfile = false;
                return false;
            } else {
                for (var i = 0; i < $scope.productListDetails.length; i++) {
                    if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                        $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                        if ($scope.prodDetails[$scope.productListDetails[i].unit.id].price == '') {
                            var priceDetail = $scope.prodDetails[$scope.productListDetails[i].unit.id].priceDetail;
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].price = (priceDetail == null ? priceDetail : priceDetail.price);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].status = (priceDetail == null ? priceDetail : priceDetail.status);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = (priceDetail == null ? priceDetail : priceDetail.aliasProductName);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].description = (priceDetail == null ? priceDetail : priceDetail.dimensionDescriptor);
                            $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = (priceDetail == null ? priceDetail : priceDetail.isDeliveryOnlyProduct);
                        }
                    }
                }
            }
        } else if ($scope.checkBoxModal.checkAllProfile === false) {
            for (var i = 0; i < $scope.productListDetails.length; i++) {

                if($scope.prodDetails[$scope.productListDetails[i].unit.id].checked) {
                    $scope.checkBoxModal.updatedProfile = '';
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].profile = $scope.checkBoxModal.updatedProfile;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].price = $scope.checkBoxModal.updatedPrice;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].status = $scope.checkBoxModal.updatedStatus;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].alias = $scope.checkBoxModal.updatedProductAlias;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].description = $scope.checkBoxModal.updatedDimensionDescription;
                    $scope.prodDetails[$scope.productListDetails[i].unit.id].isDeliveryOnlyProduct = $scope.checkBoxModal.updatedDeliveryOnlyProducts;
            }
        }
        }
    };

    $scope.changeAllStatus = function (status) {
        if ($scope.checkBoxModal.checkAllStatus === true) {
            if ($scope.checkBoxModal.updatedStatus == null) {
                alert("Please enter new status.");
                $scope.checkBoxModal.checkAllStatus = false;
                return false;
            } else {
                for (var i = 0; i < $scope.unitPriceProfileMappings.length; i++) {
                }
            }
        } else if ($scope.checkBoxModal.checkAllStatus === false) {
            for (var i = 0; i < $scope.filteredData.length; i++) {
                $scope.filteredData[i].checked = status;
            }
        }
    };

   

   $scope.trimmedRegions=[];
    $scope.filterRegion=function (regionAll) {
        $scope.trimmedRegions=regionAll.filter(function(region){
            return region !='ALL';
        });
    };
    function getRegionDetails() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regions = response.data;
            $scope.filterRegion($scope.regions);
            $scope.regions.push('ALL');
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getRecipeProfilesOfProduct(productID, dimensionId) {
        $http({
            url: AppUtil.restUrls.recipeManagement.recipeProfileOfProductDimension,
            method: "GET",
            params: {productId: productID, dimensionId: dimensionId}
        }).success(function (data) {
            console.log(data);
            $scope.recipeProfiles = [];
            var i = 0;
            for (i = 0; i < data.length; i++) {
                if ($scope.recipeProfiles.indexOf(data[i]) == -1) {
                    $scope.recipeProfiles.push(data[i]);
                }
            }
        });
    }

});