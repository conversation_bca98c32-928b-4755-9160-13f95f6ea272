/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller("PnLReportController",
    function ($scope, $rootScope, AppUtil, $http, $cookieStore) {

        $scope.init = function () {
            $scope.userDetails=AppUtil.getUserValues();

            $scope.backToCover = AppUtil.backToCover;
            $scope.pnlDetail = {};
            $scope.budgetExceeded = null;
            $scope.request = {};
            $scope.netProfitPerCent = 0;
            $scope.today = new Date(new Date().setDate(new Date().getDate() - 1)).toString();
            $scope.request.tillDate = new Date(new Date().setDate(new Date().getDate() - 1)).toISOString().slice(0, 10);
            $scope.limit = 5;
            $scope.groupSelected = true;
            $scope.selectedpartnerID = {};
            $scope.selectedUnit=[];
            $scope.unitMultiSelect = [];
            $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
            getUserDetails();
        };


        function getUserDetails() {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.userManagement.userUnits,
                data: {
                    "employeeId": $scope.userDetails.user.id,
                    "onlyActive": true
                },
            }).then(function success(response) {
                $scope.units = response.data.filter(function (unit) {
                    return unit.category == 'CAFE' && unit.status == 'ACTIVE';
                })
                console.log($scope.units);
            }, function error(response) {
                console.log("error:" + response);
            });
        }


        $scope.downloadMTDPnlDetailSheet = function () {
            // var unitDetailList = [];
            // for (var i=0; i < $scope.selectedpartnerIdDAM.length; i++) {
            //     unitDetailList.push($scope.selectedpartnerIdDAM[i].id);
            // }
            for(var i=0;i<$scope.unitMultiSelect.length;i++){
                $scope.selectedUnit.push($scope.unitMultiSelect[i].id);
            }

            var data = {
                name: $scope.request.tillDate,
                list: $scope.selectedUnit
            };
            console.log(data);

            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.expenseManagement.getMTDPnlAggregateDetailSheetDAM,
                data: data,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).then(function success(response) {
                var fileName = "MTD Pnl Detail Sheet- " + Date.now() + ".xlsx";
                var blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }, fileName);
                saveAs(blob, fileName);
                $scope.selectedUnit=[];
                $scope.unitMultiSelect = [];
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                alert("Unable to download MTD detail sheet.");
                $scope.selectedUnit=[];
                $scope.unitMultiSelect = [];
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.updateLimit = function (value) {
            $scope.limit = value;
        };


        $scope.selectedpartnerIdDAM = function (val) {
            $scope.selectedpartnerIdDAM.push(val);
        }

        $scope.selectedPartner = function (value) {
            $scope.selectedpartnerID = value;
        };
        $scope.getPnLAggregateReport = function () {

            if ($scope.request.tillDate == null || $scope.request.tillDate === undefined) {
                alert("Please select till date for report!");
                return false;
            }
            if (!$scope.selectedpartnerID.id) {
                alert("Please select Unit for report!");
                return false;
            }
            $rootScope.showFullScreenLoader = true;
            var unitDetail = {
                unitId: $scope.selectedpartnerID.id,
                tillDate: $scope.request.tillDate
            };
            $http({
                method: 'POST',
                url: AppUtil.restUrls.expenseManagement.PnLAggregateReport,
                data: unitDetail
            }).then(function success(response) {
                console.log(response);
                if (response.data != null && response.data.length) {
                    $scope.aggregateList = response.data;
                    $scope.aggregateList.shift();
                    $scope.aggregateList.shift();
                    $scope.aggregateList.shift();
                    $scope.aggregateList = response.data.map(function (aggregate) {
                        aggregate.budget = aggregate.budget != null && aggregate.budget != "" ? parseInt(aggregate.budget) : aggregate.budget;
                        aggregate.mtdValue[0] = aggregate.mtdValue[0] != "null" && aggregate.mtdValue[0] != "" ? parseInt(aggregate.mtdValue[0]) : 0;
                        return aggregate;
                    });
                } else {
                    $scope.aggregateList = [];
                    alert("Not able to fetch data");

                }
                $rootScope.showFullScreenLoader = false;

            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                alert(response.data.errorMessage);
                console.log("error:" + response);
            });
        }


        $scope.setSelectedRow = function (idSelected) {
            $scope.drilldowns = []
            if ($scope.idSelected == idSelected.key) {
                $scope.groupSelected = !$scope.groupSelected;
            }
            $scope.idSelected = idSelected.key;
            $scope.drilldowns = idSelected.drilldowns;
        };


    });


