/*
 * Created By Shanmukh
 */
adminapp.controller("employeeRolePolicyResetCtrl", function ($scope, $http, $location, $window, $rootScope, $timeout,
                                                             AuthService, AppUtil, fileService) {
    $scope.init = function () {
        $scope.selectedDepartment = null;
        $scope.selectedDesignation = null;
        $scope.selectedEmployee = null;
        $scope.selectedEmployeeJson = null;
        $scope.departmentList = [];
        $scope.designationList = [];
        $scope.employeesList = [];
        $scope.userPolicies = [];
        $scope.getAllDepartments();
        $scope.getAllDesignations();
        $scope.categoryGridOptions = $scope.createGridOptions();
        $scope.getAllPolicies();
        $scope.uploadedDoc = null;
    };

    $scope.getAllDepartments = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.departments
        }).then(function success(response) {
            $scope.departmentList = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.getAllDesignations = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.designations
        }).then(function success(response) {
            $scope.designationList = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.setSelectedDepartment = function (dept) {
        $scope.selectedDepartment = dept;
        if ($scope.selectedDesignation != null && $scope.selectedDesignation != '') {
            $scope.getEmployeesWithDepartmentDesignation();
        }
    };

    $scope.setSelectedDesignation = function (desg) {
        $scope.selectedDesignation = desg;
        if ($scope.selectedDepartment == null) {
            bootbox.alert("Please select a department..!");
            $scope.selectedDesignation = null;
        } else {
            $scope.getEmployeesWithDepartmentDesignation();
        }
    };

    $scope.setSelectedEmployee = function (employee) {
        $scope.selectedEmployee = employee;
        if (employee != null) {
            $scope.selectedEmployeeJson = JSON.parse(employee);
            if ($scope.selectedEmployeeJson.userPolicyId != null) {
                for (var i = 0; i < $scope.userPolicies.length; i++) {
                    if ($scope.userPolicies[i].userPolicyId == $scope.selectedEmployeeJson.userPolicyId) {
                        $scope.selectedEmployeeJson.userPolicy = null;
                        $scope.selectedEmployeeJson.userPolicy = $scope.userPolicies[i];
                        break;
                    }
                }
            }
        }
    };

    $scope.getEmployeesWithDepartmentDesignation = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.employeesWithDepartmentDesignation,
            params: {
                "departmentId": JSON.parse($scope.selectedDepartment).id,
                "designationId": JSON.parse($scope.selectedDesignation).id
            }
        }).then(function success(response) {
            $scope.employeesList = response.data;
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.createGridOptions = function () {
        return {
            enableFiltering: true,
            enableColumnResizing: true,
            columnDefs: [{
                field: 'actionDetailId',
                displayName: 'Action Detail Id',
                enableCellEdit: false
            }, {
                field: 'actionType',
                displayName: 'Action Type',
                enableCellEdit: false
            }, {
                field: 'actionCategory',
                displayName: 'Action Category',
                enableCellEdit: false
            }, {
                field: 'actionCode',
                displayName: 'Action Code',
                enableCellEdit: false
            }, {
                field: 'actionDescription',
                displayName: 'Action Description',
                enableCellEdit: false,
            }]
        };
    };

    $scope.getAllPolicies = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.getUserPolicies
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            $scope.userPolicies = response.data;
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
        });
    };

    $scope.showActionsForRole = function (role) {
        $scope.currentSelectedRole = role.roleId;
        if (role.expanded != null && role.expanded != undefined) {
            role.expanded = !role.expanded;
            $scope.categoryGridOptions.data = role.roleActions;
        } else {
            role.expanded = true;
            $scope.categoryGridOptions.data = role.roleActions;
        }
        angular.forEach($scope.finalRows, function (row) {
            if (row.roleId != $scope.currentSelectedRole) {
                row.expanded = false;
            }
        });
    };

    $scope.getFilteredRows = function () {
        if ($scope.currentViewType == "ALL_ROLES_SCREEN") {
            $scope.finalRows = $scope.activeEmployeeRoles;
        } else {
            $scope.filteredRows = $scope.activeEmployeeRoles.filter(function (row) {
                return row.status == "ACTIVE";
            });
            if ($scope.filteredRows.length == 0) {
                bootbox.alert("Please Select atleast One Role to Proceed...!");
                $scope.currentViewType = "ALL_ROLES_SCREEN";
                $scope.finalRows = $scope.activeEmployeeRoles;
            } else {
                $scope.currentViewType = "PREVIEW_ROLES_SCREEN";
                $scope.finalRows = $scope.filteredRows;
            }
        }
    };

    $scope.setSelectedPolicy = function (policy) {
        $scope.selectedPolicy = policy;
        if (policy != undefined && policy != null && policy != '') {
            $scope.selectedPolicyJson = JSON.parse(policy);
            $scope.openPolicyRoles($scope.selectedPolicyJson, "RESET", true);
        }
    };

    $scope.resetPolicy = function () {
        $scope.selectedPolicy = null;
        $scope.selectedPolicyJson = null;
        $scope.uploadedDoc = null;
        $timeout(function () {
            $('#selectedPolicy').val(null).trigger('change');
        });
        $scope.currentSelectedPolicy = $scope.selectedEmployeeJson.userPolicy;
        $scope.openPolicyRoles($scope.selectedEmployeeJson.userPolicy, 'RESET', false)
    };

    $scope.openPolicyRoles = function (policy, action, isSelected) {
        if (action == "ADD") {
            $scope.currentViewType = "ADD_POLICY";
            $scope.currentSelectedPolicy = {};
        } else {
            $scope.currentViewType = "ALL_ROLES_SCREEN";
            $scope.currentSelectedPolicy = policy;
        }
        $scope.currentAction = action;
        $scope.activeEmployeeRoles = [];
        if (policy != undefined && policy != null) {
            $scope.activeEmployeeRoles = policy.userPolicyRoleMappings;
        }
        $scope.activeEmployeeRoles.forEach(function (role) {
            role.status = 'ACTIVE';
        });
        $scope.activeEmployeeRoles.sort(function (a, b) {
            return a.roleName.localeCompare(b.roleName);
        });
        $("#editPolicyRolesModal").modal("show");
        if (action == "RESET" && !isSelected) {
            $scope.activeEmployeeRoles = [];
        }
        $scope.getFilteredRows();
    };

    $scope.resetEmployeeUserPolicy = function () {
        if ($scope.selectedPolicyJson == undefined || $scope.selectedPolicyJson == null) {
            bootbox.alert("Please select a policy..!");
            return;
        }
        if ($scope.activeEmployeeRoles == null || $scope.activeEmployeeRoles.length == 0) {
            return;
        }
        if ($scope.uploadedDoc == null) {
            bootbox.alert("Please Upload Policy Change Proof..!");
            return;
        }
        bootbox.confirm({
            message: "Do You Want to Override the Existing Roles with the Roles in the Policy ? <br>" +
                "Press <b>YES</b> to Override the Roles of Employee with Cutrrent Policy Roles <br>" +
                "Press <b>NO</b> to Add the Roles Of Current Policy to the Employee mapped Roles ",
            buttons: {
                confirm: {
                    label: 'Yes',
                },
                cancel: {
                    label: 'No',
                }
            },
            callback:
                function (result) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.userManagement.resetUpdateEmployeeUserPolicy,
                        params: {
                            employeeId: $scope.selectedEmployeeJson.id,
                            policyId: $scope.selectedPolicyJson.userPolicyId,
                            updatedBy: AppUtil.getCurrentUser().id,
                            uploadedDocId: $scope.uploadedDoc.documentId,
                            overrideRoles: result
                        }
                    }).then(function success(response) {
                        if (response.status === 200) {
                            $("#editPolicyRolesModal").modal('toggle');
                            alert("Employee Policy Reset Successful ..!");
                            $rootScope.showFullScreenLoader = false;
                            for (var i = 0; i < $scope.employeesList.length; i++) {
                                if ($scope.employeesList[i].id === $scope.selectedEmployeeJson.id) {
                                    $scope.employeesList[i].userPolicyId = $scope.selectedPolicyJson.userPolicyId;
                                    $scope.selectedEmployee = null;
                                    $scope.selectedEmployeeJson = null;
                                    $timeout(function () {
                                        $('#selectedPolicy').val(null).trigger('change');
                                        $('#selectedEmployee').val(null).trigger('change');
                                    });
                                }
                            }
                        } else {
                            alert("Error in Resetting Policy for the Employee ..!");
                            $rootScope.showFullScreenLoader = false;
                        }
                    }, function error(response) {
                        alert("Error in Resetting Policy for the Employee ..!");
                        $rootScope.showFullScreenLoader = false;
                        console.log("error:" + response);
                    });
                }});
    };

    $scope.uploadPolicyChangeProof = function () {
        $rootScope.showFullScreenLoader = true;
        $scope.uploadedDoc = null;
        var fd = new FormData();
        var file = fileService.getFile();
        if (file == undefined || file == null) {
            bootbox.alert("Please Select a file to upload..!");
            $rootScope.showFullScreenLoader = false;
            return;
        }

        var fileExt = AppUtil.getFileExtension(file.name);
        var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
        if (file.size > fileLimit) {
            var msg = ""
            if (fileExt.toLowerCase() == 'png') {
                msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
            }
            bootbox.alert('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
            $rootScope.showFullScreenLoader = false;
            return;
        }
        fd.append('file', file);
        fd.append('type', "OTHERS");
        fd.append('mimeType', fileExt.toUpperCase());
        fd.append('userId', AppUtil.getCurrentUser().id);
        fd.append('docType', "EMPLOYEE_POLICY_RESET");
        fd.append('file', file);
        fd.append('docName', "EMPLOYEE_POLICY_RESET");

        $http({
            url: AppUtil.restUrls.userManagement.uploadDocument,
            method: 'POST',
            data: fd,
            headers: {'Content-Type': undefined},
            transformRequest: angular.identity
        }).success(function (response) {
            console.log(response);
            $rootScope.showFullScreenLoader = false;
            if (response.documentId == undefined || response.documentId == null) {
                bootbox.alert("Something Went Wrong Please try Again..!");
            } else {
                bootbox.alert("File Uploaded Successfully..!");
                $scope.uploadedDoc = response;
            }
        }).error(function (response) {
            $rootScope.showFullScreenLoader = false;
            alert("Upload failed");
            $scope.uploadedDoc = null;
        });
    };
});

