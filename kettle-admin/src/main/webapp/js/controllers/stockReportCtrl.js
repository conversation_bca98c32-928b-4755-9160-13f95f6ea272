/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("stockReportController", function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location,
                                                       $timeout, AppUtil) {

    $scope.init = function () {
        $scope.units = {};
        $scope.unitReportData = null;
        $scope.selectedpartnerID = [];
        $scope.startDate = $scope.dateformatting(AppUtil.getDate());
        $scope.endDate = $scope.dateformatting(AppUtil.getDate());
        getPartnerIDs("CAFE");
        console.log($scope.startDate);
    };
    $scope.dateformatting = function (startDate) {
        var year = new Date(startDate).getFullYear();
        var month = new Date(startDate).getMonth() + 1;
        var day = new Date(startDate).getDate();
        if (day >= 1 && day < 10)
            day = '0' + day;
        if (month >= 1 && month < 10)
            month = '0' + month;
        return year + "-" + month + "-" + day;
    }

    function getPartnerIDs(category) {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnits,
            params: {
                "category": category
            }
        }).then(function success(response) {
            $scope.units = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.selectPartnerId = function (val) {
        console.log(val);
        $scope.selectedpartnerID.push(val);
    }

    $scope.downloadExcel = function () {
        var url = AppUtil.restUrls.posMetaData.getStockReport;
        $rootScope.showDetailLoader = true;
        $scope.unitID = [];
        for (var cafeID in $scope.selectedpartnerID) {
            $scope.unitID.push($scope.selectedpartnerID[cafeID].id);
        }
        console.log($scope.selectedpartnerID);
        var unitData = {
            startDate: $scope.startDate,
            endDate: $scope.endDate,
            unitId: $scope.unitID
        };
        $http({
            method: 'POST',
            url: url,
            responseType: 'arraybuffer',
            data: unitData,
            headers: {
                'Content-type': 'application/json',
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        }).then(
            function success(response) {
                if (response != undefined && response != null) {
                    var fileName = "StockReport - "
                        + unitData.startDate + " to " + unitData.endDate + ".xlsx";
                    console.log(fileName);
                    var blob = new Blob(
                        [response.data],
                        {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }, fileName);
                    saveAs(blob, fileName);
                }
                $rootScope.showDetailLoader = false;
            },
            function error(response) {
                console.log("error:" + response);
                alert("Unable to download Budget Template");
                $rootScope.showDetailLoader = false;
            });
    };
});
