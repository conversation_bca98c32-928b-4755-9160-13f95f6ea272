/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


adminapp.controller("AppSectionDataController", function ($rootScope, $scope, $http, $location, $cookieStore, AppUtil, fileService) {

    $scope.init = function () {
        $scope.isTrue = false;
        $scope.userDetails=AppUtil.getUserValues();
        $scope.resultFound = false;
        $scope.optedCompanyId = null;
        $scope.sequence = [];
        $scope.statusVal = [true, false];
        $scope.displayTypes = ["HOME_TYPE", "MENU_TYPE"];
        $scope.bannerTypes = ["WITH_BANNER", "WITHOUT_BANNER"];
        $scope.addSequence = [];
        $scope.shuffledata = [];
        // $scope.addSequence.companyId=null;
        // $scope.addSequence.name=null;
        // $scope.addSequence.title=null;
        // $scope.addSequence.desc=null;
        // $scope.addSequence.status=null;
        $scope.selectedPartnerId=null;
        $scope.selectedShufflePartnerId=null;
        $scope.partnerId=[{name:"DINE IN",partnerId:21},{name:"BAZAAR",partnerId: 23}];

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.companies
        }).then(function success(response) {
            $scope.companiesList = response.data;
            console.log($scope.companiesList);
        }, function error(response) {
            console.log("error:" + response);
        });
    };


    $scope.getSequenceVal = function () {
        console.log($scope.optedCompanyId.id);
        $http({
            method: 'GET',
            url: AppUtil.restUrls.appSection.getSequence + "/" + $scope.optedCompanyId.id,
            params:{
                partnerId : $scope.selectedPartnerId.partnerId
            }
        }).then(function success(response) {
            $scope.getSection = response.data;
            if (angular.equals([], $scope.getSection)) {
                console.log($scope.contactResponse.applicable);
                $scope.resultFound = false;
            } else {
                $scope.resultFound = true;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.addSection = function () {

        if (!$scope.addSequence.name) {
            alert("please select name")
            return;
        }
        if (!$scope.addSequence.title) {
            alert("please enter the title")
            return;
        }
        if (!$scope.addSequence.desc) {
            alert("please enter the description")
            return;
        }
        if (!$scope.addSequence.type) {
            alert("please select the type")
            return;
        }
        if ($scope.addSequence.status == undefined && $scope.addSequence.status==null) {
            alert("please select the status")
            return;
        }

        if ($scope.addSequence.partnerName == undefined && $scope.addSequence.status==null) {
            alert("please select the partner")
            return;
        }

        if ($scope.addSequence.displayType == undefined || $scope.addSequence.displayType ==null) {
            alert("please select where to show the section")
            return;
        }

        if ($scope.addSequence.bannerType == undefined || $scope.addSequence.bannerType ==null) {
            alert("please select where to show the section")
            return;
        }

        var appSectionDetail = {
            companyId: $scope.addSequence.companyId.id,
            name: $scope.addSequence.name,
            partnerId: $scope.addSequence.partnerName.partnerId,
            title: $scope.addSequence.title,
            code: $scope.addSequence.type,
            desc: $scope.addSequence.desc,
            active: $scope.addSequence.status,
            displayType: $scope.addSequence.displayType,
            bannerType: $scope.addSequence.bannerType
        }
        $scope.sequence.push(appSectionDetail);
        console.log($scope.addSequence.companyId.id);
        console.log(appSectionDetail);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.appSection.addSectionDetails + "/" + $scope.addSequence.companyId.id,
            data: appSectionDetail
        }).then(function success(response) {
            $scope.getSequenceVal();
            alert("Success");
            console.log(response);
        }, function error(response) {
            console.log("error:" + response);
        });

        $scope.resetAppSection();
    };
    $scope.resetAppSection = function () {
        $scope.addSequence.companyId = null;
        $scope.addSequence.name = null;
        $scope.addSequence.title = null;
        $scope.addSequence.type = null;
        $scope.addSequence.desc = null;
        $scope.addSequence.status = null;
        $scope.addSequence.displayType = null;
        $scope.addSequence.bannerType = null;
    };
    $scope.remove = function ($index) {
        $scope.shuffleSection.splice($index, 1);
    }
    $scope.setList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.appSection.getSequence + "/" + $scope.optedCompanyId.id,
            params:{
                partnerId : $scope.selectedShufflePartnerId.partnerId
            }
        }).then(function success(response) {
            $scope.shuffleSection = response.data;
            $scope.lastUpdatedSequences = [];
            $scope.sequences = {};

            $http({
                method: 'GET',
                url: AppUtil.restUrls.appSection.getCategorySequence + "/" + $scope.optedCompanyId.id,
                params:{
                    partnerId : $scope.selectedShufflePartnerId.partnerId
                }
            }).then(function success(categoryResponse) {
                $scope.categorySequenceData = categoryResponse.data;
                $scope.categorySequenceIdentifier = [];
                for (var i = 0; i < $scope.categorySequenceData.identifiers.length; i++) {
                    $scope.categorySequenceIdentifier.push($scope.categorySequenceData.identifiers[i]);
                }

                for (var i = 0; i < $scope.shuffleSection.length; i++) {
                    if ($scope.shuffleSection[i].active) {
                        $scope.sequences[$scope.shuffleSection[i].code] = $scope.shuffleSection[i];
                    }
                }
                $scope.lastUpdateids=[];
                for (var i = 0; i < $scope.categorySequenceIdentifier.length; i++) {
                    for (var j = 0; j < $scope.shuffleSection.length; j++) {
                        if ($scope.shuffleSection[j].active) {
                            if ($scope.categorySequenceIdentifier[i] == $scope.shuffleSection[j].code) {
                                $scope.lastUpdatedSequences.push($scope.shuffleSection[j]);
                                $scope.lastUpdateids.push($scope.shuffleSection[j].code);

                                continue;
                            }
                        }
                    }
                }

                $scope.shuffleSection.forEach(function (shuffle) {
                    if (($.inArray(shuffle.code, $scope.lastUpdateids)) == -1 && shuffle.active==true) {
                        $scope.lastUpdatedSequences.push(shuffle);
                        $scope.lastUpdateids.push(shuffle.code);
                    }
                })
$scope.result=true;
            }, function error(response) {
                $scope.result=false;
                console.log("error:" + response);
            });

        }, function error(response) {
            $scope.result=false;
            console.log("error:" + response);
        });
    };
    $scope.submitSequence = function () {
        $scope.identifiers = [];
        for (var i in $scope.lastUpdatedSequences) {
            $scope.identifiers.push($scope.lastUpdatedSequences[i].code);
        }
        var shufflesequence = {
            description: $scope.shuffledata.desc,
            companyId: $scope.optedCompanyId.id,
            partnerId:$scope.selectedShufflePartnerId.partnerId,
            active: true,
            identifiers: $scope.identifiers,
            sequences: $scope.sequences
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.appSection.setShuffle + "/" + $scope.optedCompanyId.id,
            data: shufflesequence
        }).then(function success(response) {
            if (response.data == true) {
                alert("Success");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };
    $scope.onUpdate = function (value) {
        console.log("On open update modal", value);
        $scope.id = value.id;
        $scope.code = value.code;
        $scope.name = value.name;
        $scope.companiesList.map(function (company) {
            if (company.id == value.companyId) {
                $scope.company = company;
            }
        });
        // $scope.company=value.companyId;
        $scope.title = value.title;
        $scope.description = value.desc;
        $scope.status = value.active;
        $scope.displayType = value.displayType;
        $scope.bannerType = value.bannerType;
    };
    $scope.updateSection = function () {
        if (!$scope.name) {
            alert("please select name")
            return;
        }
        if (!$scope.title) {
            alert("please enter the title")
            return;
        }
        if (!$scope.description) {
            alert("please enter the description")
            return;
        }
        if ($scope.status== undefined && $scope.status==null) {
            alert("please select the status")
            return;
        }
        if (!$scope.company) {
            alert("please select the company")
            return;
        }

        console.log("SELECTEd", $scope.displayType);
        console.log("SELECTEd", $scope.bannerType);
        var appSectionDetail = {
            _id: $scope.id,
            companyId: $scope.company.id,
            name: $scope.name,
            title: $scope.title,
            code: $scope.code,
            desc: $scope.description,
            active: $scope.status,
            partnerId: $scope.selectedPartnerId.partnerId,
            displayType: $scope.displayType,
            bannerType: $scope.bannerType
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.appSection.addSectionDetails + "/" + $scope.company.id,
            data: appSectionDetail
        }).then(function success(response) {
            $scope.getSequenceVal();
            alert("Success");
            console.log(response);
        }, function error(response) {
            console.log("error:" + response);
        });
    }
});