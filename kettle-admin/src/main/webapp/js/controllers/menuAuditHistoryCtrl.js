/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

adminapp.controller('menuAuditHistoryCtrl',
    ['$location', '$scope', 'AppUtil', '$rootScope', '$http', 'zomatoCatalogService', 'zomatoNewCatalogService', 'zomatoSingleServeCatalogService', 'swiggyCatalogServiceV1', 'swiggyCatalogService',
        function ($location, $scope, AppUtil, $rootScope, $http, zomatoCatalogService, zomatoNewCatalogService, zomatoSingleServeCatalogService, swiggyCatalogServiceV1, swiggyCatalogService) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.init = function () {
                $scope.getUnitList();
                $scope.getChannelPartnersList();
                $scope.getAllBrands();
                $scope.selectedMenuType = null;
                $scope.menuType = ["DEFAULT", "DAY_SLOT_BREAKFAST", "DAY_SLOT_LUNCH"
                    , "DAY_SLOT_EVENING", "DAY_SLOT_DINNER", "DAY_SLOT_POST_DINNER", "DAY_SLOT_OVERNIGHT", "SINGLE_SERVE"];
                $scope.isSelectedPartnerZomato = false;
                $scope.selectedAction = "UPDATE UNIT MENU";
                $scope.initUpdateUnitMenu();
                $scope.splitAllDesiChaiDimensions = false;
            };

            $scope.setSelectedMenuType = function (menuType) {
                $scope.selectedMenuType = menuType;
            };

            $scope.initUpdateUnitMenu = function () {
                $scope.resetChannelPartnersList();
            };

            $scope.getUnitList = function () {
                $scope.unitList = [];
                AppUtil.getUnitList(function (list) {
                    $scope.unitList = list;
                });
            };

            $scope.resetUnitList = function () {
                if ($scope.unitList != null) {
                    $scope.unitList.map(function (unit) {
                        unit.selected = false;
                    });
                }
            };

            $scope.getChannelPartnersList = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerManagement.get
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.channelPartners = response.data;
                    } else {
                        bootbox.alert("Error getting partner list.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.resetChannelPartnersList = function () {
                if ($scope.channelPartners != null) {
                    $scope.channelPartners.map(function (partner) {
                        partner.selected = false;
                    });
                }
            };

            $scope.getAllBrands = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.brandManagement.getAllBrands
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        $scope.brands = response.data;
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error getting brands.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setSelectedBrand = function (selectedBrand) {
                $scope.selectedBrand = selectedBrand;
            };

            $scope.selectUnit = function (unit) {
                unit.selected === true ? unit.selected = false : unit.selected = true;
                $scope.showAddNewOffer = false;
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                if ($scope.selectedAction === 'ADD MENU') {
                    $scope.currentMenu = null;
                }
            };

            $scope.setSelectedPartner = function (selectedPartner) {

                if (selectedPartner.partnerName !== "ZOMATO") {
                    $scope.isSelectedPartnerZomato = true;
                } else {
                    $scope.isSelectedPartnerZomato = false;
                }
                $scope.selectedPartner = selectedPartner;

            };
            $scope.getUnitMenuToAdd = function () {
                if ($scope.selectedUnit == null) {
                    bootbox.alert("Please select partner.");
                } else if ($scope.selectedPartner == null) {
                    bootbox.alert("Please select region.");
                } else if ($scope.selectedMenuType == null) {
                    bootbox.alert("Please select menu type");
                } else if ($scope.selectedBrand == null) {
                    bootbox.alert("Please select brand ");
                } else {
                    $scope.getUnitProductProfile();
                }
            };

            $scope.getUnitProductProfile = function () {
                $rootScope.detailLoaderMessage = "Fetching  menu Audit History...";
                $rootScope.showDetailLoader = true;
                $scope.historyData = null;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.partnerMetadata.menuAuditHistorys + "?unitId=" + $scope.selectedUnit.id + "&partnerId=" + $scope.selectedPartner.kettlePartnerId + "&brandId=" + $scope.selectedBrand.brandId + "&menuType=" + $scope.selectedMenuType + "&addTime=",
                }).then(function success(response) {
                    if (response.status === 200 && response.data != null) {
                        var menuAuditData = response.data;
                        if (menuAuditData != null && menuAuditData.length > 0) {
                            $scope.historyData = response.data;
                        }
                        $rootScope.showDetailLoader = false;
                    } else {
                        bootbox.alert("Error getting unit product profile.");
                        $rootScope.showDetailLoader = false;
                    }
                }, function error(response) {
                    console.log('Error in getting response', response);
                    bootbox.alert('Error in getting response', response);
                    $rootScope.showDetailLoader = false;
                });
            };
        }]);