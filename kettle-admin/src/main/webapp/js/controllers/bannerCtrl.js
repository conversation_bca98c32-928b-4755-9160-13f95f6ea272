/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("BannerController", function ($rootScope, $scope, $http, $location, $cookieStore, AppUtil, fileService) {

    $scope.init = function () {
        $scope.banner = {};
        $scope.getBannerActionType();
        $scope.getBannerType();
        $scope.getSectionType();
        // $scope.bannerType = ["INFO"]
        // $scope.actionType = ["HOME"]
        //  console.log(AppUtil.formatDate(AppUtil.getDate(),"dd-MM-yyyy-hh-mm-ss"));
        // $scope.setAction = false;
        $scope.getBannerList();
        $scope.banner.start = $scope.dateformatting(yesterday())
        $scope.banner.exp = $scope.dateformatting(AppUtil.getDate())
        console.log($scope.dateformatting(yesterday()));


        function yesterday() {
            var currentDate = new Date();
            // //console.log(currentDate);
            currentDate = currentDate.setDate(currentDate.getDate() - 1);
            return currentDate;
        }


    }


    $scope.getBannerList = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.bannerManagement.getBanner
        }).then(function success(response) {
            $scope.bannerList = response.data;
            console.log($scope.bannerList);
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    $scope.getBannerType = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.bannerManagement.getBannerType
        }).then(function success(response) {
            $scope.bannerType = response.data;
            console.log($scope.bannerList);
        }, function error(response) {
            console.log("error:" + response);
        });

    }


    $scope.getSectionType = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.bannerManagement.getSectionType
        }).then(function success(response) {
            $scope.bannerSectionType = response.data;
            console.log($scope.bannerSectionType);
        }, function error(response) {
            console.log("error:" + response);
        });

    }


    $scope.getBannerActionType = function () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.bannerManagement.getBannerActionType
        }).then(function success(response) {
            $scope.actionType = response.data;
            console.log($scope.bannerList);
        }, function error(response) {
            console.log("error:" + response);
        });

    }


    $scope.selectingBannerType = function (bannerType) {
        if (bannerType == 'INFO') {
            $scope.buttonText = false;
            $scope.actionButton = false;
            $scope.code = false;
            $scope.section = false;
        } else if (bannerType == 'ACTION') {
            $scope.buttonText = true;
            $scope.actionButton = true;
            $scope.code = false;
            $scope.section = false;
        } else if (bannerType == 'OFFER_INFO_CODE_COPY') {
            $scope.buttonText = true;
            $scope.actionButton = false;
            $scope.code = true;
            $scope.section = false;
        } else if (bannerType == 'NAVIGATE_TO_MENU_ITEM') {
            $scope.buttonText = false;
            $scope.section = true;
            $scope.actionButton = false;
            $scope.code = false;
        }

    }

    $scope.openNewBannerModal = function () {
        $scope.clear();
        $("#AddNewBannerModalData").modal("show");
    }

    $scope.uploadBanner = function () {
        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            alert("Please select an Image");
            return false;
        }
        var fileExt = getFileExtension(fileService.getFile().name);
        if (isImage(fileExt.toLowerCase())) {
            var mimeType = fileExt.toUpperCase();
            var fd = new FormData();
            fd.append('mimeType', fileExt.toUpperCase());
            fd.append('file', fileService.getFile());
            fd.append('name', fileService.getFile().name);
            console.log(fd)
            $rootScope.showFullScreenLoader = true;

            $http({
                url: AppUtil.restUrls.bannerManagement.uploadBanner,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).then(function success(response) {
                console.log("success:" + response);
                $rootScope.showFullScreenLoader = false;

                if (response != null && response.status == 200) {
                    console.log(response);
                    $scope.banner.imageUrl = response.data.name;
                    alert("Upload successful");
                } else {
                    alert("Error while uploading Product Image.");
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
                bootbox.alert("Upload failed");
            });
        } else {
            alert('Upload Failed , File Format not Supported');
        }

    }


    $scope.saveBanner = function () {

        if ($scope.actionButton) {
            if ($scope.banner.buttonAction == null) {
                alert("Please select banner action type");
                return;
            }
        }

        if ($scope.buttonText) {
            if ($scope.banner.buttonText == null) {
                alert("Please enter button text");
                return;
            }
        }


        if ($scope.section) {
            if ($scope.banner.sectionType == null) {
                alert("Please select section type");
                return;
            } else {
                if ($scope.banner.sectionType.shortName == 'REG') {
                    if ( !$scope.banner.categoryId) {
                        alert("Please enter  category Id");
                        return;
                    }
                }

            }
        } else {
            $scope.banner.sectionType = null;
        }


        if ($scope.code) {
            if ($scope.banner.code == null) {
                alert("Please enter banner code");
                return;
            }
        }


        if (!$scope.banner.exp || !$scope.banner.start) {
            alert("Enter start and expiry date");
            return;
        }

        if ($scope.banner.imageUrl == null) {
            alert("please upload image first")
            return;
        }

        var result = confirm("Are You Sure You Want To save Banner");
        if (!result) {
            return;
        }

        if ($scope.banner.buttonAction != null ) {
            $scope.banner.buttonAction = $scope.banner.buttonAction.actionType;
        }

        if ($scope.banner.sectionType != null ) {
            $scope.banner.sectionType = $scope.banner.sectionType.shortName;
        }

        var data=AppUtil.getUserValues();
        $scope.banner.createdBy = data.userId;
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.bannerManagement.saveBanner,
            data: $scope.banner
        }).then(function success(response) {
            if (response != null && response.status == 200) {
                alert("banner saved successfully");
                $("#AddNewBannerModalData").modal("hide");
                $rootScope.showFullScreenLoader = false;
                $scope.clear();
                $scope.getBannerList();
            }
            else {
                alert("Error while saving  banner.");
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;

        });
    }


    $scope.editBanner = function (banner) {

        $scope.buttonText = false;
        $scope.actionButton = false;
        $scope.code = false;
        $scope.section = false;
        $scope.banner = banner;

        // $scope.buttonText = false;
        // $scope.actionButton = false;
        // $scope.code = false;
        // $scope.section = false;

        if (banner.buttonAction != null) {
            $scope.actionButton = true;
        }
        if (banner.buttonText != null) {
            $scope.buttonText = true;
        }
        if (banner.code != null) {
            $scope.code = true;
        }
        if (banner.sectionType != null) {
            $scope.section = true;
        }


        $scope.actionType.forEach(function (actions) {
            if (banner.buttonAction == actions.actionType) {
                $scope.banner.buttonAction = actions;
            }
        });

        $scope.bannerSectionType.forEach(function (type) {
            if (banner.sectionType == type.shortName) {
                $scope.banner.sectionType = type;
            }
        });

        $scope.banner.exp = $scope.dateformatting(banner.exp);
        $scope.banner.start = $scope.dateformatting(banner.start);
        $("#AddNewBannerModalData").modal("show");
    }


    $scope.updateStatus = function (banner) {

        if (banner.status == "ACTIVE") {
            status = "IN_ACTIVE"
        } else {
            if (banner.exp < new Date()) {
                alert("banner cannot be activated, Please change banner expiry date");
                return;
            }
            status = "ACTIVE"
        }
        var payload = {
            id: banner.id,
            name: status,
        }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.bannerManagement.updateBannerStatus,
            data: payload
        }).then(function success(response) {
            if (response.data == true) {
                $scope.getBannerList();
                $rootScope.showFullScreenLoader = false;
            }
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
        });
    }


    $scope.clear = function () {
        angular.element("input[type='file']").val(null);
        fileService.push(null);
        $scope.buttonText = false;
        $scope.actionButton = false;
        $scope.code = false;
        $scope.banner = {};
    }


    $scope.openProductImageModal = function (imageUrl) {
        $scope.imageSrc = imageUrl;
        if (imageUrl == null) {
            alert("Image not found!")
        }
        else {
            $("#displayImageModal").modal("show");
        }

    };

    $scope.dateformatting = function (startDate) {
        var year = new Date(startDate).getFullYear();
        var month = new Date(startDate).getMonth() + 1;
        var day = new Date(startDate).getDate();
        if (day >= 1 && day < 10)
            day = '0' + day;
        if (month >= 1 && month < 10)
            month = '0' + month;
        return year + "-" + month + "-" + day;
    }


    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function isImage(fileExt) {
        return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png";
    }
});