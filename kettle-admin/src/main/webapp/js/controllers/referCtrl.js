/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("ReferController", function($scope, $location, $http, AuthService,$cookieStore,AppUtil){
	
	$scope.contactNumber="";
	$scope.referralNumber="";
	$scope.referralName="";
	
	$scope.init = function(){
		$scope.clear();
	};
	
	$scope.refer = function(){
		if($scope.contactNumber.trim().length!=0 
				&& $scope.referralNumber.trim().length!=0 
					&& $scope.referralName.trim().length!=0){
			
			var reqObj = {
						 name:$scope.referralName,
						 reffererContactNumber:$scope.referralNumber,
						 contactNumber:$scope.referralName
						};
			$http({
				  method: 'POST',
				  url: AppUtil.restUrls.customerProfile.refer,
				  data: reqObj,
				}).then(function success(response) {
					alert(JSON.stringify(response));
				}, function error(response) {
					alert(JSON.stringify(response));
			});
			
		}else{
			if($scope.contactNumber.trim().length==0){
				$scope.contactNumberError = true;
			} 
			if($scope.referralNumber.trim().length==0){
				$scope.referralNumberError = true;
			} 
			if( $scope.referralName.trim().length==0){
				$scope.referralNameError = true;
			}
		}
	};
	
	$scope.clear = function(){
		$scope.contactNumber=""; 
		$scope.referralNumber=""; 
		$scope.referralName="";
		$scope.referralNameError = $scope.contactNumberError = $scope.referralNumberError = false;
	};
});