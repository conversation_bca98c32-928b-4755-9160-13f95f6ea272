/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("CouponsDataController", function($scope, $http,AppUtil) {

	$scope.coupon ={};
	
	$scope.searchCouponCode = function(search) {
		$http({
			method : 'POST',
			url : AppUtil.restUrls.offerManagement.couponSearch+"?applyLimit=true",
			data : $scope.search
		}).then(function success(response) {
			if (response.status==200) {
				$scope.coupon = response.data;
				console.log($scope.coupon);
			} else {
				console.log(response);
			}
		}, function error(response) {
			console.log("error:" + response);
		});
	}
});
