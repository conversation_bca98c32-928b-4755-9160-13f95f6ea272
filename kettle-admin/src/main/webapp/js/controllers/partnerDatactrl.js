/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("PartnerDataController", function($scope,$http,AppUtil){
	
	
	$http({
		url: AppUtil.restUrls.offerManagement.marketingPartner,
		dataType: 'json',
	    method: 'POST',
	    data: '',
	    headers: {
	        "Content-Type": "application/json"
	    }
		}).then(function success(response) {
		//console.log(JSON.stringify(response));
		$scope.partnerDetails=response;
		$scope.currentPage = 1; //current page
		$scope.entryLimit = 50; //max no of items to display in a page
		$scope.partnerDetailsList = response.data
		console.log(response.data);
		$scope.filteredItems = $scope.partnerDetailsList.length; //Initially for no filter  
		$scope.totalItems = $scope.partnerDetailsList.length;
		}, function error(response) {
		console.log("error:"+response);
	});
	

	$scope.partnerType = [{code:1, name:"INTERNAL"},{code:2, name:"EXTERNAL"}, {code:3, name:"MASS"}];
	
	//$scope.selectPartnerType=$scope.partnerType[0];
	$scope.addPartner = function()
	{
		$("#partnerModal").modal("show");
		
	}
	
	
	$scope.sort_by = function(predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };
    
   
    $scope.partnerStatusChange=function(partnerStatusList,marketingPartnerId)
    {
    	console.log(partnerStatusList);
    	partnerStatusList.forEach(function(listPartner)
    	{
    		if(listPartner.id==marketingPartnerId)
    			{
    			if(listPartner.status=="ACTIVE")
    					{
    					$http({
    						url: AppUtil.restUrls.offerManagement.marketingPartnerDeactivate,
    							  method: "POST",
    							  dataType: 'json',
    							  data: marketingPartnerId,
    							  headers: { 'Content-Type': 'application/json' },
    							}).success(function(data) {
    							alert("Successfully Deactivated");
    							window.location.reload();
    							//window.relaod()=true
    							console.log(data);
    					});
    					}
    				else if(listPartner.status=="IN_ACTIVE")
    					{
    					console.log("IN_ACTIVE=",listPartner.status,listPartner);
    					$http({
    						url: AppUtil.restUrls.offerManagement.marketingPartnerActivate,
    							  method: "POST",
    							  dataType: 'json',
    							  data: marketingPartnerId,
    							  headers: { 'Content-Type': 'application/json' },
    							}).success(function(data) {
    							alert("Successfully Activated");
        						window.location.reload();
    							console.log(data);
    					});
    					
    					}
    				else 
    					{
    					alert("Status Not Changed")
    					}
    			}
    	});
    }
 
    
	
	
	$scope.submitAddPartner=function()
	{
		//console.log($scope.selectPartnerType);
		var partnerObj = {
				id:null,
				name: $scope.partnerName,
				code: null,
				type: $scope.selectPartnerType.name,
				shortCode: null,
				status: "ACTIVE"
			}
		
		$http({
			method: 'POST',
			  url: AppUtil.restUrls.offerManagement.marketingPartnerAdd,
			  data: partnerObj,
			}).then(function success(response) {
				if(response.status==200){
					alert("partner added successfully!");
					//$scope.loading = false;
					//$scope.addBtnShow=false;
					window.location.reload();
					}else{
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
		});
		
	}
});