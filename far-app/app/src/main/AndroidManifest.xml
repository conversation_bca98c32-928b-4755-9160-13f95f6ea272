<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.chaayosStockTake"
    tools:ignore="GoogleAppIndexingWarning">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android:requestLegacyExternalStorage" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>

    <application
        android:allowBackup="true"
        android:icon="@drawable/chaayos_logo_green"
        android:label="@string/app_name"
        android:roundIcon="@drawable/chaayos_logo_green_circle"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true">
        <activity android:name="com.example.chaayosStockTake.AssetLookup"></activity>
        <activity
            android:name="com.example.chaayosStockTake.StockTakingEventSummary"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.example.chaayosStockTake.DayCloseSubCategorySelection"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.example.chaayosStockTake.NonScannableStockEventSummary"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.example.chaayosStockTake.ExtraScannedScreen"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.example.chaayosStockTake.PendingReceiving"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name="com.example.chaayosStockTake.Splash"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.chaayosStockTake.StockTakingScanner"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.example.chaayosStockTake.StockTaking"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.example.chaayosStockTake.Events"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.example.chaayosStockTake.Login"
            android:label="@string/title_activity_login"
            android:screenOrientation="portrait" />
        <activity android:name="com.example.chaayosStockTake.MainActivity" />

        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />
    </application>

</manifest>
