<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#75FFFFFF"
    tools:context=".AssetLookup">

    <com.journeyapps.barcodescanner.DecoratedBarcodeView
        android:id="@+id/dbv_barcode_scanner"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        app:layout_constraintBottom_toTopOf="@+id/guideline2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    </com.journeyapps.barcodescanner.DecoratedBarcodeView>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />

    <TextView
        android:id="@+id/scan_something_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text="Scan an asset's QR code to see details!"
        android:textAlignment="center"
        android:textAppearance="@style/TextAppearance.AppCompat.Small"
        android:textSize="14sp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/scan_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dbv_barcode_scanner">

    </TextView>

    <Button
        android:id="@+id/scan_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@color/chaayosGreen"
        android:text="Start Scanning"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/asset_details_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/scan_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dbv_barcode_scanner">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/asset_lookup_guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.3" />

        <TextView
            android:id="@+id/qr_tag_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#CCEBB16D"
            android:fontFamily="monospace"
            android:paddingTop="5dp"
            android:paddingBottom="8dp"
            android:text="abc14u"
            android:textAlignment="center"
            android:textColor="#000000"
            android:textSize="24sp"
            app:layout_constraintEnd_toEndOf="@+id/asset_id_tv"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/asset_id_header_tv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#27EBB16D"
            android:fontFamily="sans-serif"
            android:gravity="center_horizontal|center_vertical"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="Asset :"
            android:textAlignment="center"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="@+id/asset_id_tv"
            app:layout_constraintEnd_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/qr_tag_value">

        </TextView>

        <TextView
            android:id="@+id/asset_name_header_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#11000000"
            android:fontFamily="sans-serif-medium"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="Asset Name :"
            android:textSize="18sp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/asset_id_header_tv" />

        <TextView
            android:id="@+id/unit_id_header_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#21EBB16D"
            android:fontFamily="sans-serif"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="Unit :"
            android:textAlignment="center"
            android:textSize="18sp"
            app:layout_constraintEnd_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/asset_name_header_tv" />

        <TextView
            android:id="@+id/asset_id_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#27EBB16D"
            android:fontFamily="sans-serif-smallcaps"
            android:gravity="bottom|top"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="8dp"
            android:text="Asset Value"
            android:textColor="@color/colorPrimaryDark"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintTop_toBottomOf="@+id/qr_tag_value" />

        <TextView
            android:id="@+id/asset_name_tv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="sans-serif-black"
            android:gravity="bottom|top"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="Asset Name"
            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
            app:layout_constraintBottom_toBottomOf="@+id/asset_name_header_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintTop_toBottomOf="@+id/asset_id_tv" />

        <TextView
            android:id="@+id/unit_id_tv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#21EBB16D"
            android:fontFamily="sans-serif-smallcaps"
            android:gravity="bottom|top"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="Unit ID"
            android:textColor="@color/colorPrimaryDark"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="@+id/unit_id_header_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/unit_id_header_tv"
            app:layout_constraintTop_toBottomOf="@+id/asset_name_tv" />

        <TextView
            android:id="@+id/attribute_id_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#CC517736"
            android:paddingStart="8dp"
            android:paddingTop="12dp"
            android:paddingEnd="8dp"
            android:paddingBottom="12dp"
            android:text="Attribute ID"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/unit_id_header_tv" />

        <TextView
            android:id="@+id/attribute_name_header"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#CC517736"
            android:gravity="bottom|top"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="Attribute Name"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/attribute_id_header"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/asset_lookup_guideline"
            app:layout_constraintTop_toBottomOf="@+id/unit_id_tv" />

        <ListView
            android:id="@+id/attributes_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/attribute_id_header" />

    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>