<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_layout_events_activity"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".Events">


        <com.example.chaayosStockTake.MovableButton
            android:id="@+id/pending_receiving_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="32dp"
            android:layout_marginBottom="32dp"
            android:text="Pending/Receiving Event"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/welcome_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="TextView"
            android:textAppearance="@style/TextAppearance.AppCompat.Large"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/avatar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="50dp"
            app:layout_constraintBottom_toTopOf="@+id/welcome_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/male_avatar" />

        <Button
            android:id="@+id/refresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/refresh"
            android:layout_marginTop="10dp"
            android:layout_marginRight="5dp"
            tools:ignore="MissingConstraints"></Button>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.example.chaayosStockTake.MovableButton
        android:id="@+id/transfer_out"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:backgroundTint="@color/colorPrimary"
        android:text="Transfer Out"
        android:textColor="@color/white"
        app:layout_anchor="@id/asset_lookup_button" />

    <com.example.chaayosStockTake.MovableButton
        android:id="@+id/asset_lookup_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="24dp"
        android:backgroundTint="@color/colorPrimary"
        android:text="Asset Lookup"
        android:textColor="@color/white"
        app:layout_anchor="@id/stock_taking_button" />


    <com.example.chaayosStockTake.MovableButton
        android:id="@+id/stock_taking_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="24dp"
        android:backgroundTint="@color/colorPrimary"
        android:text="Stock Taking Event"
        android:textColor="@color/white"
        tools:text="Stock Taking Event"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>