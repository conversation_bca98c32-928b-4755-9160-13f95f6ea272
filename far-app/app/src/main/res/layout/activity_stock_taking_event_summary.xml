<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/chaayosGreen"
    tools:context=".StockTakingEventSummary">



    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/assets_summary_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="8dp"
        android:background="#FFFFFF"
        app:layout_constraintBottom_toTopOf="@+id/scan_again_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <TableLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/table"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp" >
        <TableRow android:background="@color/yellow" android:padding="5dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Type"
                android:textColor="@android:color/black"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Assets Found"
                android:textColor="@android:color/black"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Assets Lost"
                android:textColor="@android:color/black"/>
        </TableRow>
        <TableRow android:id="@+id/scannable" android:background="@color/cornerlightGreen" android:padding="5dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Scannable"
                android:textColor="@android:color/black"/>
            <TextView
                android:id="@+id/scannableFound"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@android:color/black"
                 />
            <TextView
                android:id="@+id/scannableLost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@android:color/black"
                 />
        </TableRow>
        <TableRow  android:id="@+id/nonscannable" android:background="@color/cornerlightGreen" android:padding="5dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Non Scannable"
                android:textColor="@android:color/black"/>
            <TextView
                android:id="@+id/nonscannableFound"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@android:color/black"
                 />
            <TextView
                android:id="@+id/nonscannableLost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@android:color/black"
                />
        </TableRow>
    </TableLayout>


    <RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/relativeLayot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:layout_marginTop="150dp"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/activity_vertical_margin"
        android:orientation="horizontal"
        tools:context=".StockTakingEventSummary">

        <Spinner
            android:id="@+id/listTypeFilterSummary"
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:background="@color/yellow"
            android:layout_marginVertical="10dp"
            android:visibility="visible" />

        <ExpandableListView
            android:id="@+id/expandableListViewFound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@android:color/background_light"
            android:dividerHeight="10dp"
            android:layout_marginBottom="100dp"
            android:indicatorLeft="?android:attr/expandableListPreferredItemIndicatorLeft"
            android:nestedScrollingEnabled="true"
            android:layout_below="@id/listTypeFilterSummary"/>


    </RelativeLayout>

    <Button
        android:id="@+id/scan_again_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/yellow"
        android:text="Back"
        android:visibility="gone"
        android:textColor="@color/colorPrimaryDark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <Button
        android:id="@+id/submit_anyway_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/colorPrimaryDark"
        android:text="Submit Anyway"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline" />


</androidx.constraintlayout.widget.ConstraintLayout>
