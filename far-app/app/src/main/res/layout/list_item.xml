<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/expandedListItem"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="4dp"
        android:padding="4dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:id="@+id/outline_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/cardview_light_background">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/sub_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="6dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/rounded_shape_green_fill"

                android:padding="8dp">

                <TextView
                    android:id="@+id/asset_name_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/alice"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:text="Asset Name"
                    android:textAlignment="center"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_corners"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/asset_tag_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"

                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:text="TagValue"
                    android:textAlignment="center"
                    android:textColor="#FFFFFF"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/asset_name_tv" />


                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <TextView
                    android:id="@+id/asset_tag_validity_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="AssetTagValidity"
                    android:textAlignment="center"
                    android:textColor="#FFFFFF"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/asset_tag_tv"
                    app:layout_constraintTop_toBottomOf="@+id/asset_name_tv" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>