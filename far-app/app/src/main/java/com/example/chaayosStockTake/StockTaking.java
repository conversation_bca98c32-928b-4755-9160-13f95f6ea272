package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.domain.StockTakeInitResponse;
import com.example.chaayosStockTake.domain.SubCategoryFlattenData;
import com.example.chaayosStockTake.util.AppConstants;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class StockTaking extends AppCompatActivity implements View.OnClickListener, AdapterView.OnItemSelectedListener {

    private AlertDialog.Builder builder;
    private AlertDialog progressDialog;
    private List<StockTakingRequest> initializedEventsList;

    private StockTakeInitResponse stockTakeInitResponse;

    private List<String> eventSubtypes = new ArrayList<>();
    private CardView initializedEventCardView;
    private TextView eventId;
    private TextView eventStatus;
    private CardView newEventDetailsCardView;
    private Integer generatedEventId;
    private Spinner eventTypeSpinner;
    private String eventType;
    private Spinner auditorSpinner;
    private Map<String, String> auditors;
    private User eventAuditedBy;
    private TextView auditorTV;
    private EditText auditorPassword;
    private TextView auditorPasswordTV;

    private Button createSTEventFinal;
    private Button validateAuditorButton;
    private ProgressBar authenticationProgressBar;
    private TextView eventSubtypeTV;
    private Spinner eventSubtypeSpinner;
    private JsonObject auditorRequirements;
    private ArrayAdapter<StringToStringMapToList> managerAuditorsAdapter;
    private ArrayAdapter<StringToStringMapToList> auditorsAdapter;
    private boolean isAuditorRequired;
    private String eventSubtype;

    private StockTakingRequest initiatedEvent;

    private Integer userId;

    private Button abandonButton;
     private  Button resumeButton;

     private Button createNewEventBtn;

    private String appVersion ;

    private Map<String, String> subCategoryListMap = new HashMap<>();

    private List<SubCategoryFlattenData> subCategoryFlattenData = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_stock_taking);
        createNewEventBtn = findViewById(R.id.new_stock_taking_event_btn);
        initializedEventCardView = findViewById(R.id.intialized_event_card_view);
        resumeButton = findViewById(R.id.resume_ST_event_btn);
        abandonButton = findViewById(R.id.cancel_ST_event_btn);
        eventId = findViewById(R.id.event_id_cv);
        eventStatus = findViewById(R.id.event_status_cv);
        newEventDetailsCardView = findViewById(R.id.new_ST_event_spinners_cv);
        createSTEventFinal = findViewById(R.id.create_new_event_btn_final);
        initializedEventCardView.setVisibility(View.GONE);
        eventTypeSpinner = findViewById(R.id.event_type_spinner);
        auditorSpinner = findViewById(R.id.auditor_spinner);
        auditorTV = findViewById(R.id.auditor_tv);
        auditorPassword = findViewById(R.id.auditor_password);
        auditorPasswordTV = findViewById(R.id.auditor_password_tv);
        validateAuditorButton = findViewById(R.id.validate_auditor_button);
        validateAuditorButton.setOnClickListener(this);
        authenticationProgressBar = findViewById(R.id.authentication_progressbar);
        eventSubtypeTV = findViewById(R.id.event_sub_type_tv);
        eventSubtypeSpinner = findViewById(R.id.event_sub_type_spinner);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();


        Log.d("version",  pref.getString("version","0"));

        appVersion = pref.getString("version","0");


        createNewEventBtn.setOnClickListener(this);
        resumeButton.setOnClickListener(this);
        abandonButton.setOnClickListener(this);
        createSTEventFinal.setOnClickListener(this);
        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Stock Taking");
        if(Objects.nonNull( prefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }

            userId =  pref.getInt("id",-1);


        auditorSpinner.setOnItemSelectedListener(this);

        isAuditorRequired = true;

        progressDialog = getDialogProgressBar().create();
        progressDialog.show();
        progressDialog.setCancelable(false);

        getStockTakingInitializedEventsList();                  // Calling API to fetch previously initialized Stock Taking Events
        getAuditors();
        getEventType();
        getEventSubtype();
        getAuditorRequirements();
//        if (Events.getIsAuditorRequired()) {
//            validateAuditorButton.setVisibility(View.VISIBLE);
//            createSTEventFinal.setVisibility(View.GONE);
//            getAuditors();
//        }
    }

    private void getEventSubtype() {
//        eventSubtypes.add("AUDIT");
//        eventSubtypes.add("REGULAR");
//        eventSubtypes.add("NSO");
//        eventSubtypes.add("MANAGER_HANDOVER");
//        eventSubtypes.add("DAILY_DAYCLOSE");
//        eventSubtypes.add("WEEKLY_DAYCLOSE");
//        eventSubtypes.add("MONTHLY_DAYCLOSE");
        //eventSubtypes.add("RENOVATION");
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<>(StockTaking.this, android.R.layout.simple_spinner_item, eventSubtypes);
        spinnerArrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        eventSubtypeSpinner.setAdapter(spinnerArrayAdapter);
        eventSubtypeSpinner.setOnItemSelectedListener(StockTaking.this);
    }


    private void getAuditorRequirements() {

        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<Object> auditorRequirementCall = unitsApiService.getStockTakeSubList(token);
        auditorRequirementCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if (response.body() == null) {
                    Toast.makeText(getApplicationContext(), "Auditor Requirement could not be fetched. Null response", Toast.LENGTH_SHORT).show();
                    return;
                }
                String auditorRequirementObject = new Gson().toJson(response.body());
                auditorRequirements = new Gson().fromJson(auditorRequirementObject, JsonObject.class);
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Auditor Requirement could not be fetched.", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void getAuditors() {
        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sh.getString("jwtToken", null);
        Integer unitId = sh.getInt("unitId", -1);
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);


        Call<Object> employeesDataCall = unitsApiService.getEmployeesForUnit(token, unitId);
        employeesDataCall.enqueue(new Callback<Object>() {
            @RequiresApi(api = Build.VERSION_CODES.N)
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if (response.body() == null) {
                    Toast.makeText(getApplicationContext(), "Null response in fetching Manager Auditors", Toast.LENGTH_SHORT).show();
                    return;
                }
                Object resp = response.body();
                String json = new Gson().toJson(resp);
                JsonArray jsonArray = new Gson().fromJson(json, JsonArray.class);
                Map<String, String> managerAuditors = new HashMap<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject employee = jsonArray.get(i).getAsJsonObject();
                    String designation = employee.get("designation").getAsString();
                    if (designation.equals("Manager")) {
                        Integer id = employee.get("id").getAsInt();
                        if(id.equals(userId)){
                            continue;
                        }
                        String name = employee.get("name").getAsString();
                        managerAuditors.put(id.toString(), name);
                    }
                }
                List<StringToStringMapToList> auditorsList = new ArrayList<StringToStringMapToList>();
                for (Map.Entry<String, String> entry : managerAuditors.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    auditorsList.add(new StringToStringMapToList(value, key));
                }

                auditorsList.sort((o1, o2) -> o1.getKey().charAt(0) - o2.getKey().charAt(0));
                managerAuditorsAdapter = new ArrayAdapter<>(StockTaking.this, android.R.layout.simple_spinner_item, auditorsList);
                managerAuditorsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
//                auditorSpinner.setAdapter(auditorsAdapter);

            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {

            }
        });
        Call<Map<String, String>> auditorsListCall = unitsApiService.getAuditors(token, "STOCK_EVENT_AUDITOR");
        auditorsListCall.enqueue(new Callback<Map<String, String>>() {
            @Override
            public void onResponse(Call<Map<String, String>> call, Response<Map<String, String>> response) {
                if (response.body() == null) {
                    Toast.makeText(StockTaking.this, "Auditors List API: Response Null!", Toast.LENGTH_SHORT).show();
                    return;
                }
                auditors = response.body();
                List<StringToStringMapToList> auditorsList = new ArrayList<StringToStringMapToList>();
                for (Map.Entry<String, String> entry : auditors.entrySet()) {
                    String key = entry.getKey();
                    if(Integer.parseInt(key) == userId){
                        continue;
                    }
                    String value = entry.getValue();
                    auditorsList.add(new StringToStringMapToList(value, key));
                }
                auditorsAdapter = new ArrayAdapter<>(StockTaking.this, android.R.layout.simple_spinner_item, auditorsList);
                auditorsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
//                auditorSpinner.setAdapter(spinnerArrayAdapter);
            }

            @Override
            public void onFailure(Call<Map<String, String>> call, Throwable t) {
                Toast.makeText(StockTaking.this, "Auditors List API: No response. Check internet connection!", Toast.LENGTH_SHORT).show();
            }
        });

    }

    private void getEventType() {
        List<String> eventTypes = new ArrayList<>();
        eventTypes.add("STOCK_TAKE");
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(StockTaking.this, android.R.layout.simple_spinner_item, eventTypes);
        spinnerArrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        eventTypeSpinner.setAdapter(spinnerArrayAdapter);
        eventTypeSpinner.setOnItemSelectedListener(StockTaking.this);
    }



    private void getStockTakingInitializedEventsList() {

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);
        Integer unitId = pref.getInt("unitId", -1);
        Call<StockTakeInitResponse> stockTakingEventsCall = unitsApiService.getStockTakingEvents(token, unitId,"IN_PROCESS");
        stockTakingEventsCall.enqueue(new Callback<StockTakeInitResponse>() {
            @Override
            public void onResponse(Call<StockTakeInitResponse> call, Response<StockTakeInitResponse> response) {
                if (response.body() == null) {
                    Toast.makeText(getApplicationContext(), "getStockTakingEvents: Null response.body()", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                    finish();
                    return;
                }
                stockTakeInitResponse = response.body();
                initializedEventsList = stockTakeInitResponse.getStockEventDefinition();
                eventSubtypes = stockTakeInitResponse.getSubTypeList();
                if (initializedEventsList.size() > 0) {
                    if(!initializedEventsList.get(0).getAppVersion().equalsIgnoreCase(appVersion)){
                        progressDialog.dismiss();
                           setContentView(R.layout.app_version_not_matched);
                           return;
                    }
                    Map<String,List<StockTakingScannedItem>> scannedItemMap = new HashMap<>();
                    Map<String,List<StockTakingScannedItem>> pendingAssetMap = new HashMap<>();
                    initializedEventCardView.setVisibility(View.VISIBLE);
                    CardView headerView = findViewById(R.id.header);

                    Button newEventButton  = findViewById(R.id.new_stock_taking_event_btn);
                    newEventButton.setVisibility(View.GONE);

                    Gson gson = new Gson();
                    SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                    SharedPreferences.Editor edt = pref.edit();
                    List<StockTakingScannedItem> asssetList = initializedEventsList.size() > 1 ? initializedEventsList.get(1).getAvailableAssets() : initializedEventsList.get(0).getAvailableAssets();
                    TextView eventType = findViewById(R.id.event_sub_type);
                    TextView userIdView = findViewById(R.id.user_id);
                    eventType.setText(initializedEventsList.size() > 1 ? initializedEventsList.get(1).getSubType() :initializedEventsList.get(0).getSubType());
                    if(!userId.equals(initializedEventsList.get(0).getInitiatedBy().getId()) &&
                            ( Objects.isNull(initializedEventsList.get(0).getAuditedBy()) ||
                            !userId.equals(initializedEventsList.get(0).getAuditedBy().getId()))){
                        userIdView.setText(String.valueOf(initializedEventsList.get(0).getInitiatedBy().getId()));
                        resumeButton.setVisibility(View.GONE);
                        abandonButton.setVisibility(View.GONE);
                        headerView.setVisibility(View.VISIBLE);
                        eventType.setVisibility(View.VISIBLE);
                        userIdView.setVisibility(View.VISIBLE);
                        if (Objects.nonNull(initializedEventsList.get(0).getIsSplit()) && initializedEventsList.get(0).getIsSplit().equalsIgnoreCase(AppConstants.YES)) {
                            resumeButton.setVisibility(View.VISIBLE);
                            userIdView.setVisibility(View.GONE);
                            headerView.setVisibility(View.GONE);
                            eventType.setVisibility(View.GONE);
                            userIdView.setVisibility(View.GONE);
                        }
                    }else{
                        resumeButton.setVisibility(View.VISIBLE);
                        abandonButton.setVisibility(View.VISIBLE);
                        userIdView.setVisibility(View.GONE);
                        headerView.setVisibility(View.GONE);
                        eventType.setVisibility(View.GONE);
                        userIdView.setVisibility(View.GONE);
                    }
                    for(StockTakingScannedItem item  : asssetList){
                        if(Boolean.TRUE.equals(item.getNonScannable())){
                            continue;
                        }
                        if(Boolean.TRUE.equals(item.isFound())){
                            if(!scannedItemMap.containsKey(item.getSubCategory())){
                                scannedItemMap.put(item.getSubCategory(),new ArrayList<>());
                            }
                            scannedItemMap.get(item.getSubCategory()).add(item);
                        }else{
                            if(!pendingAssetMap.containsKey(item.getSubCategory())){
                                pendingAssetMap.put(item.getSubCategory(),new ArrayList<>());
                            }
                            pendingAssetMap.get(item.getSubCategory()).add(item);
                        }
                    }

                    String pendingAssetsJson = gson.toJson(pendingAssetMap);
                    String scannedAssetsJson = gson.toJson(scannedItemMap);

                    edt.putString("pendingAssetsJson",pendingAssetsJson);
                    edt.putString("scannedAssets",scannedAssetsJson);
                    if(Objects.nonNull(asssetList) && asssetList.size() > 0){
                        initiatedEvent = initializedEventsList.size() > 1 ? initializedEventsList.get(1) :initializedEventsList.get(0);

                        String assetListJson = gson.toJson(asssetList);
                        edt.putString("AssetList",assetListJson);
                        edt.apply();
                    }
                    edt.apply();
                    eventId.setText(initializedEventsList.size() > 1 ? initializedEventsList.get(1).getEventId().toString() :initializedEventsList.get(0).getEventId().toString());
                    eventStatus.setText(initializedEventsList.size() > 1 ? initializedEventsList.get(1).getEventStatus() : initializedEventsList.get(0).getEventStatus());
                    if (initializedEventsList.size() > 1) {
                        resumeButton.setText("RESUME - " + initializedEventsList.get(1).getSubCategory());
                    } else {
                        resumeButton.setText("RESUME");
                    }
                    if (Objects.nonNull(initializedEventsList.get(0).getIsSplit())) {
                        edt.putString("isEventSplit", initializedEventsList.get(0).getIsSplit());
                        edt.apply();
                    } else {
                        edt.putString("isEventSplit", AppConstants.NO);
                        edt.apply();
                    }
                } else {
                    initializedEventCardView.setVisibility(View.GONE);
                    createNewEventBtn.setVisibility(View.VISIBLE);
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<StockTakeInitResponse> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "getStockTakingInitializedEventsList: Check internet connection!", Toast.LENGTH_SHORT).show();
                finish();
                progressDialog.dismiss();
            }
        });
    }

    public AlertDialog.Builder getDialogProgressBar() {

        if (builder == null) {
            builder = new AlertDialog.Builder(this);

            builder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            builder.setView(progressBar);
        }
        return builder;
    }       // AlertDialog

    @Override
    public void onClick(View view) {
        switch (view.getId()) {

            case R.id.validate_auditor_button:
                if (!isConnected()) {
                    Snackbar.make(view, "Check internet connection!", Snackbar.LENGTH_SHORT).show();
                    break;
                }
                validateAuditor();
                break;
            case R.id.create_new_event_btn_final:
                if (!isConnected()) {
                    Snackbar.make(view, "Check internet connection!", Snackbar.LENGTH_SHORT).show();
                    break;
                }
                createNewEvent();
                break;

            case R.id.new_stock_taking_event_btn:

                if (initializedEventsList.size() > 0) {
                    AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
                    alertDialog.setTitle("Already initialized event found!");
                    alertDialog.setMessage("Please abandon the already initialized stock taking event from the list below before proceeding to create a new request. ");
                    alertDialog.setNegativeButton("OK", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.cancel();
                        }
                    });
                    alertDialog.show();
                    break;
                }

                if (newEventDetailsCardView.getVisibility() == View.VISIBLE)
                    newEventDetailsCardView.setVisibility(View.GONE);
                else
                    newEventDetailsCardView.setVisibility(View.VISIBLE);
                break;
            case R.id.resume_ST_event_btn:

                SharedPreferences sh = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                String list = sh.getString("StockTakingPrevListData", null);

                Intent intent = new Intent(StockTaking.this, NonScannableStockEventSummary.class);
                Gson googleJson = new Gson();
                if(Objects.nonNull(initiatedEvent)){
                    Log.d("extra",new Gson().toJson(initiatedEvent.getExtraScannedItems()));
                    String initiatedEventJson = googleJson.toJson(initiatedEvent, StockTakingRequest.class);
              /*      stockTakingEventResponse = new Gson().fromJson(temp, StockTakingRequest.class);*/
                    SharedPreferences.Editor edt = sh.edit();
                    edt.putString("eventResponse",initiatedEventJson);
                    edt.apply();
                   // intent.putExtra("eventResponse",initiatedEventJson);
                }
                if (initializedEventsList.size() == 1 && Objects.nonNull(initializedEventsList.get(0).getIsSplit()) && initializedEventsList.get(0).getIsSplit().equalsIgnoreCase("Y")) {
                    intent = new Intent(StockTaking.this, DayCloseSubCategorySelection.class);
                }
                SharedPreferences.Editor edt = sh.edit();
                edt.putString("subCategoryFlattenData",new Gson().toJson(getSubCategoryFlattenData(initializedEventsList.get(0).getStockTakeStatusSubCategoryMap())));
                edt.putString("completeUnitAssetList", new Gson().toJson(initializedEventsList.get(0).getAvailableAssets()));
                edt.apply();
                if (list != null) {
                    ArrayList javaArrayListFromGSON = googleJson.fromJson(list, ArrayList.class);
                    intent.putExtra("initializedEventsList", javaArrayListFromGSON);
                    intent.putExtra("eventId", initializedEventsList.size() > 1 ? initializedEventsList.get(1).getEventId() : initializedEventsList.get(0).getEventId());
                }
                startActivity(intent);
                break;
            case R.id.cancel_ST_event_btn:
                if (Objects.nonNull(initiatedEvent.getSubCategory())) {
                    if (Objects.nonNull(userId) && userId == 140199) {
                        abandonEvent();
                    } else {
                        AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(StockTaking.this, "Cannot Cancel", "Note: Please contact SCM support team to Cancel this");
                        alertDialog.show();
                    }
                } else {
                    abandonEvent();
                }
                break;
            default:
                break;
        }
    }

    private void validateAuditor() {
        if (auditorPassword.getText().toString().equals("")) {
            Toast.makeText(getApplicationContext(), "Enter auditor's password to continue! ", Toast.LENGTH_SHORT).show();
            return;
        }
        authenticationProgressBar.setVisibility(View.VISIBLE);
        validateAuditorButton.setVisibility(View.GONE);
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();
        Retrofit retrofit = new Retrofit.Builder().
                baseUrl(BuildConfig.BASE)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);
        Integer unitId = sharedPreferences.getInt("unitId", -1);

        String password = auditorPassword.getText().toString();
        LoginRequest validation = new LoginRequest(eventAuditedBy.getId(), password, unitId, 0, "SCM_SERVICE");


        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call auditorValidationCall = unitsApiService.getLoginDetails(validation);
        auditorValidationCall.enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) {
                if (response.body() == null) {
                    Toast.makeText(getApplicationContext(), "Error validating auditor. Null response!", Toast.LENGTH_SHORT).show();
                    authenticationProgressBar.setVisibility(View.GONE);
                    validateAuditorButton.setVisibility(View.VISIBLE);
                    return;
                }
                Toast.makeText(getApplicationContext(), "Authentication Successful!", Toast.LENGTH_SHORT).show();
                auditorPassword.setVisibility(View.GONE);
                auditorPasswordTV.setVisibility(View.GONE);
                authenticationProgressBar.setVisibility(View.INVISIBLE);
                validateAuditorButton.setVisibility(View.GONE);
                createSTEventFinal.setVisibility(View.VISIBLE);
            }

            @Override
            public void onFailure(Call call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Error validating auditor. Failure!", Toast.LENGTH_SHORT).show();
                authenticationProgressBar.setVisibility(View.INVISIBLE);
                validateAuditorButton.setVisibility(View.VISIBLE);
                return;
            }
        });

    }

    private void abandonEvent() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(StockTaking.this);
        alertDialog.setTitle("Cancel Event");
        alertDialog.setMessage("Are you sure you want to cancel this Stock Taking Event?");
        alertDialog.setNegativeButton("NO", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
            }
        });
        alertDialog.setPositiveButton("YES", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                StockTakingRequest abandonThisEvent = initializedEventsList.size() > 1 ? initializedEventsList.get(1) : initializedEventsList.get(0);
                abandonThisEvent.setEventStatus("ABANDONED");
                Retrofit retrofit = new Retrofit.Builder()
                        .baseUrl(BuildConfig.SCM_BASE)
                        .addConverterFactory(GsonConverterFactory.create())
                        .build();

                UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
                SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
                String token = pref.getString("jwtToken", null);

                Call<StockTakingRequest> abandonInitializedStockEventCall = unitsApiService.abandonEvent(token, abandonThisEvent);
                abandonInitializedStockEventCall.enqueue(new Callback<StockTakingRequest>() {
                    @Override
                    public void onResponse(Call<StockTakingRequest> call, Response<StockTakingRequest> response) {
                        if (response.body() == null) {
                            Toast.makeText(getApplicationContext(), "Abandon response null", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        StockTakingRequest result = response.body();
                        Toast.makeText(getApplicationContext(), "Event abandoned successfully!", Toast.LENGTH_SHORT).show();
                        Button newEventButton  = findViewById(R.id.new_stock_taking_event_btn);
                        newEventButton.setVisibility(View.VISIBLE);
                        /*auditorPassword.setVisibility(View.VISIBLE);
                        auditorPasswordTV.setVisibility(View.VISIBLE);
                        auditorPassword.setText("");
                        createSTEventFinal.setVisibility(View.GONE);
                        validateAuditorButton.setVisibility(View.VISIBLE);*/
                        initializedEventsList.clear();
                        initializedEventCardView.setVisibility(View.GONE);
                        SharedPreferences sharedPref = getSharedPreferences("SavedStockTakingEventPREF", MODE_PRIVATE);
                        SharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                        sharedPrefEditor.remove("SavedStockTakingEvent");
                        sharedPrefEditor.apply();
                        SharedPreferences sharedPreferences = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                        SharedPreferences.Editor myEdit = sharedPreferences.edit();
                        myEdit.remove("pendingAssetsJson");
                        myEdit.remove("scannedAssets");
                        myEdit.putString("StockTakingPrevListData", "");
                        myEdit.apply();
                        isAuditorRequired = false;
                        Intent intent = getIntent();
                        startActivity(intent);
                    }

                    @Override
                    public void onFailure(Call<StockTakingRequest> call, Throwable t) {
                        Toast.makeText(getApplicationContext(), "Abandon Response not received!", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });

        AlertDialog dialog = alertDialog.create();
        dialog.show();
    }

    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }

    private void createNewEvent() {
        if (eventSubtype.equals("DAILY_DAYCLOSE") || eventSubtype.equals("WEEKLY_DAYCLOSE") || eventSubtype.equals("MONTHLY_DAYCLOSE") || eventSubtype.equals("REGULAR")) {
            AlertDialog.Builder alertDialog = new AlertDialog.Builder(StockTaking.this);
            alertDialog.setTitle("Split Event");
            alertDialog.setMessage("Do You Want to Split By Sub Category Wise ?");
            alertDialog.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                    SharedPreferences.Editor edt = pref.edit();
                    edt.putString("isEventSplit", "Y");
                    edt.apply();
                    dialog.cancel();
                    continueProcess("Y");
                }
            });

            alertDialog.setNegativeButton("No", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                    SharedPreferences.Editor edt = pref.edit();
                    edt.putString("isEventSplit", "N");
                    edt.apply();
                    dialogInterface.cancel();
                    continueProcess("N");
                }
            });
            alertDialog.show();
        } else {
            SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
            SharedPreferences.Editor edt = pref.edit();
            edt.putString("isEventSplit", "N");
            edt.apply();
            continueProcess("N");
        }
    }

    private void continueProcess(String isSplit) {
        progressDialog.show();
        Retrofit retrofit = new Retrofit.Builder().baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);
        Integer userId = pref.getInt("id", -1);
        String userName = pref.getString("name", null);
        User eventInitiatedBy = new User(userId, userName);
        Integer unitId = pref.getInt("unitId", -1);
        String unitCategory = pref.getString("unitCategory", null);

        StockTakingRequest stockTakingRequest;
        if (isAuditorRequired)
            stockTakingRequest = new StockTakingRequest(unitId, unitCategory, eventType, "INITIATED", eventInitiatedBy, eventAuditedBy,eventSubtype, isSplit);
        else
            stockTakingRequest = new StockTakingRequest(unitId, unitCategory, eventType, "INITIATED", eventInitiatedBy, null,eventSubtype, isSplit);

        Call<StockTakeInitResponse> newStockEventCall = unitsApiService.initiateNewStockEvent(token, stockTakingRequest);

        newStockEventCall.enqueue(new Callback<StockTakeInitResponse>() {
            @Override
            public void onResponse(Call<StockTakeInitResponse> call, Response<StockTakeInitResponse> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && Objects.nonNull(scmError.getErrorMessage())) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "API Response is Not Successful", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "New event could not be created!", Toast.LENGTH_SHORT).show();
                    }
                }
                if (response.body() == null) {
                    Toast.makeText(getApplicationContext(), "New event could not be created!", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                    return;
                }
                stockTakeInitResponse = response.body();
                initializedEventsList = stockTakeInitResponse.getStockEventDefinition();
                StockTakingRequest STResponse = stockTakeInitResponse.getStockEventDefinition().get(0);
                generatedEventId = STResponse.getEventId();
                getStockTakingInitializedEventsList();
                newEventDetailsCardView.setVisibility(View.GONE);
                Intent i = null;
                SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                SharedPreferences.Editor edt = pref.edit();
                edt.putString("subCategoryFlattenData",new Gson().toJson(getSubCategoryFlattenData(stockTakeInitResponse.getStockTakeStatusSubCategoryMap())));
                if (Objects.nonNull(isSplit) && isSplit.equals("Y")) {
                    i = new Intent(StockTaking.this, DayCloseSubCategorySelection.class);
                    edt.putString("completeUnitAssetList", new Gson().toJson(STResponse.getAvailableAssets()));
                } else {
                    i = new Intent(StockTaking.this, NonScannableStockEventSummary.class);
                }
                edt.putInt("eventId", generatedEventId);
                i.putExtra("eventId", generatedEventId);
                Gson googleJson = new Gson();
                String stockTakingEventResponseJSON = googleJson.toJson(STResponse, StockTakingRequest.class);
                i.putExtra("eventResponse",stockTakingEventResponseJSON);
                if (eventAuditedBy != null && isAuditorRequired) {
                    edt.putString("auditedByName", eventAuditedBy.getName());
                    i.putExtra("auditedByName", eventAuditedBy.getName());
                    edt.putInt("auditedById", eventAuditedBy.getId());
                    i.putExtra("auditedById", eventAuditedBy.getId());
                }
                edt.apply();
                //i.putExtra("auditedBy", (Parcelable) eventAuditedBy);
//                startActivityForResult(i, 2);
                SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                SharedPreferences.Editor edt = pref.edit();
                List<StockTakingScannedItem> asssetList = STResponse.getAvailableAssets();
                String assetListJson = googleJson.toJson(asssetList);
                edt.putString("AssetList",assetListJson);
                edt.apply();
                startActivity(i);
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<StockTakeInitResponse> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "createNewEvent: Failure", Toast.LENGTH_SHORT).show();
                progressDialog.dismiss();
            }
        });
    }

    private List<SubCategoryFlattenData> getSubCategoryFlattenData(Map<String, String> subCategoryListMap) {
        List<SubCategoryFlattenData> result = new ArrayList<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            subCategoryListMap.forEach((key, value) -> {
                SubCategoryFlattenData flattenData = new SubCategoryFlattenData(key, value.equalsIgnoreCase("#") ?
                        null : value.split("#")[0],value.equalsIgnoreCase("#") ? null : value.split("#")[1]);
                result.add(flattenData);
            });
        }
        return result;
    }

    @Override
    protected void onResume() {
        super.onResume();

        if(eventType!= null && !eventType.equals("WH_OPENING")){
            if (eventSubtype.equals("AUDIT") || eventSubtype.equals("REGULAR") || eventSubtype.equals("NSO") || eventSubtype.equals("RENOVATION") || eventSubtype.equals("DAILY_DAYCLOSE") || eventSubtype.equals("WEEKLY_DAYCLOSE") || eventSubtype.equals("MONTHLY_DAYCLOSE")) {
                if (auditorRequirements.get(eventSubtype).getAsBoolean()) {
                    isAuditorRequired = true;
                    auditorTV.setVisibility(View.VISIBLE);
                    auditorSpinner.setVisibility(View.VISIBLE);
                    auditorPassword.setVisibility(View.VISIBLE);
                    auditorPasswordTV.setVisibility(View.VISIBLE);
                    auditorPassword.setText("");
                    validateAuditorButton.setVisibility(View.VISIBLE);
                    createSTEventFinal.setVisibility(View.GONE);
                } else {
                    isAuditorRequired = false;
                    auditorTV.setVisibility(View.GONE);
                    auditorSpinner.setVisibility(View.GONE);
                    auditorPassword.setVisibility(View.GONE);
                    auditorPasswordTV.setVisibility(View.GONE);
                    validateAuditorButton.setVisibility(View.GONE);
                    createSTEventFinal.setVisibility(View.VISIBLE);
                }
            } else if (eventSubtype.equals("MANAGER_HANDOVER")) {
                if(auditorRequirements.get(eventSubtype).getAsBoolean()){
                    isAuditorRequired = true;
                    auditorTV.setVisibility(View.VISIBLE);
                    auditorSpinner.setVisibility(View.VISIBLE);
                    auditorPassword.setVisibility(View.VISIBLE);
                    auditorPassword.setText("");
                    auditorPasswordTV.setVisibility(View.VISIBLE);
                    validateAuditorButton.setVisibility(View.VISIBLE);
                    createSTEventFinal.setVisibility(View.GONE);
                } else {
                    isAuditorRequired = false;
                    auditorTV.setVisibility(View.GONE);
                    auditorSpinner.setVisibility(View.GONE);
                    auditorPassword.setVisibility(View.GONE);
                    auditorPasswordTV.setVisibility(View.GONE);
                    validateAuditorButton.setVisibility(View.GONE);
                    createSTEventFinal.setVisibility(View.VISIBLE);
                }
            }
        }

        getStockTakingInitializedEventsList();
    }


    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getId() == R.id.event_type_spinner) {
            eventType = (String) parent.getSelectedItem();
            if (eventType.equals("WH_OPENING")) {
                auditorTV.setVisibility(View.GONE);
                auditorSpinner.setVisibility(View.GONE);
                auditorPassword.setVisibility(View.GONE);
                auditorPasswordTV.setVisibility(View.GONE);
                eventSubtypeTV.setVisibility(View.GONE);
                auditorPassword.setText("");
                eventSubtypeSpinner.setVisibility(View.GONE);
                validateAuditorButton.setVisibility(View.GONE);
                createSTEventFinal.setVisibility(View.VISIBLE);
                isAuditorRequired = false;
            } else {
                auditorTV.setVisibility(View.VISIBLE);
                auditorSpinner.setVisibility(View.VISIBLE);
                auditorPassword.setVisibility(View.VISIBLE);
                auditorPasswordTV.setVisibility(View.VISIBLE);
                eventSubtypeTV.setVisibility(View.VISIBLE);
                eventSubtypeSpinner.setVisibility(View.VISIBLE);
                validateAuditorButton.setVisibility(View.VISIBLE);
                createSTEventFinal.setVisibility(View.GONE);
            }
        } else if (parent.getId() == R.id.event_sub_type_spinner) {
            eventSubtype = (String) parent.getSelectedItem();
            if (auditorRequirements == null) {
                Toast.makeText(getApplicationContext(), "Check internet connection!", Toast.LENGTH_SHORT).show();
                onBackPressed();
                return;
            }
            if (eventSubtype.equals("AUDIT") || eventSubtype.equals("REGULAR") || eventSubtype.equals("NSO") || eventSubtype.equals("RENOVATION")
            || eventSubtype.equals("DAILY_DAYCLOSE") || eventSubtype.equals("WEEKLY_DAYCLOSE") || eventSubtype.equals("MONTHLY_DAYCLOSE")) {
                auditorSpinner.setAdapter(auditorsAdapter);
                if( eventSubtype.equals("NSO")){
                    auditorSpinner.setAdapter(managerAuditorsAdapter);
                    auditorTV.setText("Manager");
                    auditorPasswordTV.setText("Password");
                }else{
                    auditorTV.setText("Auditor");
                    auditorPasswordTV.setText("Password");
                }
                if(eventSubtype.equals("REGULAR") || eventSubtype.equals("DAILY_DAYCLOSE") || eventSubtype.equals("WEEKLY_DAYCLOSE") || eventSubtype.equals("MONTHLY_DAYCLOSE")){
                    isAuditorRequired = false;
                    auditorTV.setVisibility(View.GONE);
                    auditorSpinner.setVisibility(View.GONE);
                    auditorPassword.setVisibility(View.GONE);
                    auditorPasswordTV.setVisibility(View.GONE);
                    validateAuditorButton.setVisibility(View.GONE);
                    createSTEventFinal.setVisibility(View.VISIBLE);
                }else if (auditorRequirements.get(eventSubtype).getAsBoolean()) {
                    isAuditorRequired = true;
                    auditorTV.setVisibility(View.VISIBLE);
                    auditorSpinner.setVisibility(View.VISIBLE);
                    auditorPassword.setText("");
                    auditorPassword.setVisibility(View.VISIBLE);
                    auditorPasswordTV.setVisibility(View.VISIBLE);
                    validateAuditorButton.setVisibility(View.VISIBLE);
                    createSTEventFinal.setVisibility(View.GONE);
                } else {
                    isAuditorRequired = false;
                    auditorTV.setVisibility(View.GONE);
                    auditorSpinner.setVisibility(View.GONE);
                    auditorPassword.setVisibility(View.GONE);
                    auditorPasswordTV.setVisibility(View.GONE);
                    validateAuditorButton.setVisibility(View.GONE);
                    createSTEventFinal.setVisibility(View.VISIBLE);
                }
            } else if (eventSubtype.equals("MANAGER_HANDOVER")) {
                auditorSpinner.setAdapter(managerAuditorsAdapter);
                auditorTV.setText("Manager");
                auditorPasswordTV.setText("Password");
                if(auditorRequirements.get(eventSubtype).getAsBoolean()){
                    isAuditorRequired = true;
                    auditorTV.setVisibility(View.VISIBLE);
                    auditorSpinner.setVisibility(View.VISIBLE);
                    auditorPassword.setVisibility(View.VISIBLE);
                    auditorPasswordTV.setVisibility(View.VISIBLE);
                    validateAuditorButton.setVisibility(View.VISIBLE);
                    auditorPassword.setText("");
                    createSTEventFinal.setVisibility(View.GONE);
                } else {
                    isAuditorRequired = false;
                    auditorTV.setVisibility(View.GONE);
                    auditorSpinner.setVisibility(View.GONE);
                    auditorPassword.setVisibility(View.GONE);
                    auditorPasswordTV.setVisibility(View.GONE);
                    validateAuditorButton.setVisibility(View.GONE);
                    createSTEventFinal.setVisibility(View.VISIBLE);
                }
            }
        } else {
            /*String select = (String) parent.getSelectedItem();
            eventAuditedBy = getKey(auditors,select);*/
            StringToStringMapToList element = (StringToStringMapToList) parent.getSelectedItem();
            String tid = element.getValue();
            eventAuditedBy = new User(Integer.parseInt(tid), element.getKey());
            auditorPasswordTV.setVisibility(View.VISIBLE);
            auditorPassword.setVisibility(View.VISIBLE);
            auditorPassword.setText("");
            createSTEventFinal.setVisibility(View.GONE);
            validateAuditorButton.setVisibility(View.VISIBLE);
        }

    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
}
