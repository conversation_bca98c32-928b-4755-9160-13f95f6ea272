package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Bundle;
import android.util.Log;
import android.util.Pair;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.coordinatorlayout.widget.CoordinatorLayout;

import com.example.chaayosStockTake.domain.StockTakeInitResponse;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class Events extends AppCompatActivity implements View.OnClickListener {
    CoordinatorLayout coordinatorLayout;

    private StockTakingRequest transferOutEvent;

    private Button transferOutButton;

    private AlertDialog.Builder builder;
    private AlertDialog progressDialog;

    private HashMap<String,List<StockTakingScannedItem>> productAssetMap = new HashMap<>();

    private Boolean isScanningRequired;

    private StockTakeInitResponse stockTakeInitResponse;

    private List<String> eventSubtypes;


    @SuppressLint("RestrictedApi")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_events);
        Button stockTakingEventButton = findViewById(R.id.stock_taking_button);
        Button pendingReceivingEventButton = findViewById(R.id.pending_receiving_button);
        Button assetLookupButton = findViewById(R.id.asset_lookup_button);
        Button refreshButton = findViewById(R.id.refresh);
        transferOutButton = findViewById(R.id.transfer_out);
        transferOutButton.setVisibility(View.GONE);
        stockTakingEventButton.setOnClickListener(this);
        pendingReceivingEventButton.setOnClickListener(this);
        assetLookupButton.setOnClickListener(this);
        transferOutButton.setOnClickListener(this);
        refreshButton.setOnClickListener(this);

        progressDialog = getDialogProgressBar().create();
        progressDialog.show();
        progressDialog.setCancelable(false);

        checkForTransferOutEvent(false);




        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        SharedPreferences.Editor edt = pref.edit();
        edt.putString("version","1.2.1");
        edt.apply();
        TextView welcomeName = findViewById(R.id.welcome_name);
        String userName = "Welcome, " + pref.getString("name", null) + "!";
        welcomeName.setText(userName);
        coordinatorLayout= findViewById(R.id.coordinator_layout_events_activity);
        Map<String,?> prefs = pref.getAll();
        assert getSupportActionBar() != null;
        if(Objects.nonNull( prefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }
        verifyLogin();

    }


    private void checkForTransferOutEvent(Boolean submit){
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);
        Integer unitId = pref.getInt("unitId", -1);
        Call<StockTakeInitResponse> stockTakingEventsCall = unitsApiService.getStockTakingEvents(token, unitId,
                "INITIATED");
        stockTakingEventsCall.enqueue(new Callback<StockTakeInitResponse>() {
            @Override
            public void onResponse(Call<StockTakeInitResponse> call, Response<StockTakeInitResponse> response) {
                if (response.body() == null) {
                    progressDialog.dismiss();
                    //finish();
                    return;
                }
                stockTakeInitResponse = response.body();
                List<StockTakingRequest> stockTakingRequests = stockTakeInitResponse.getStockEventDefinition();

                if (stockTakingRequests.size() > 0) {
                    productAssetMap = new HashMap<>();
                    transferOutButton.setVisibility(View.VISIBLE);
                    transferOutEvent = stockTakingRequests.get(0);
                    List<StockTakingScannedItem> assetList = transferOutEvent.getAvailableAssets();
                    Map<Integer, BigDecimal> productRequestQtyMap = transferOutEvent.getProductRequestQtyMap();


                    for(StockTakingScannedItem item  : assetList){
                            if(!productAssetMap.containsKey(item.getProductName())){
                                productAssetMap.put(item.getProductName(),new ArrayList<>());
                            }
                            productAssetMap.get(item.getProductName()).add(new StockTakingScannedItem());
                    }

                }

                    if(Boolean.TRUE.equals(submit)){
                        Intent intent = new Intent(getApplicationContext(), StockTakingScanner.class);
                        Gson gson = new Gson();
                        String transferOutEventJson = gson.toJson(transferOutEvent);
                        String productAssetMapJson = gson.toJson(productAssetMap);
                        intent.putExtra("transferOut", transferOutEventJson);
                        intent.putExtra("productMap", productAssetMapJson);
                        startActivity(intent);
                    }

                }else{
                    transferOutButton.setVisibility(View.GONE);
                }
                progressDialog.dismiss();

            }

            @Override
            public void onFailure(Call<StockTakeInitResponse> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "getStockTakingInitializedEventsList: Check internet connection!", Toast.LENGTH_SHORT).show();
                finish();
                progressDialog.dismiss();
            }
        });
    }

    private void verifyLogin() {

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .client(okHttpClient)
                .build();

        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<Object> auditorRequirementCall = unitsApiService.getStockTakeSubList(token);
        auditorRequirementCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if(response.code() == 401){
                    clearPreferences();
                    startActivity(new Intent(getApplicationContext(), Login.class));
                    finish();
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Auditor Requirement could not be fetched.", Toast.LENGTH_SHORT).show();
            }
        });



    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.stock_taking_button:
                if (!isConnected()) {
                    Snackbar.make(coordinatorLayout,"Check internet connection!",Snackbar.LENGTH_SHORT).show();
                } else {
                    startActivity(new Intent(getApplicationContext(), StockTaking.class));
                }
                break;
            case R.id.pending_receiving_button:
                startActivity(new Intent(getApplicationContext(), PendingReceiving.class));
                break;
            case R.id.asset_lookup_button:
                startActivity(new Intent(getApplicationContext(),AssetLookup.class));
                break;
            case R.id.transfer_out:
                checkForTransferOutEvent(true);
                Intent intent = new Intent(getApplicationContext(),StockTakingScanner.class);
                Gson gson = new Gson();
                String transferOutEventJson = gson.toJson(transferOutEvent);
                String productAssetMapJson = gson.toJson(productAssetMap);
                intent.putExtra("transferOut",transferOutEventJson);
                intent.putExtra("productMap",productAssetMapJson);
                startActivity(intent);
            case R.id.refresh:
                checkForTransferOutEvent(false);
            default:
                break;
        }

    }

    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        switch (item.getItemId()) {
            case R.id.action_logout:
                logout();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private void logout() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(Events.this);
        alertDialog.setTitle("Logging out...");
        alertDialog.setMessage("Are you sure you want to log out of your current session?");
        alertDialog.setPositiveButton("CANCEL", (dialog, which) -> dialog.cancel());
        alertDialog.setNegativeButton("LOGOUT", (dialog, which) -> {
            dialog.cancel();
            clearPreferences();
            startActivity(new Intent(getApplicationContext(), Login.class));
            finish();
        });
        alertDialog.show();
    }

    public AlertDialog.Builder getDialogProgressBar() {

        if (builder == null) {
            builder = new AlertDialog.Builder(this);

            builder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            builder.setView(progressBar);
        }
        return builder;
    }

    private void clearPreferences() {
        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
        SharedPreferences.Editor edt = pref.edit();
        edt.putBoolean("activity_executed", false);
        edt.clear();
        edt.apply();
    }
}
