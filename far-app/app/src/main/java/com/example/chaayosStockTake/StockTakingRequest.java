package com.example.chaayosStockTake;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
public class StockTakingRequest {
    private Integer eventId;
    private Integer unitId;
    private String unitType;
    private String eventType;
    private String eventStatus;
    private User initiatedBy;
    private Long eventCreationDate;
    private User auditedBy;
    private Long auditDate;
    private String auditStatus;

    private String subType;

    private String appVersion;

    private String isSplit;

    private String subCategory;

    protected Integer parentId;

    @SerializedName("extraScannedItems")
    private List<StockTakingScannedItem> extraScannedItems;

    @SerializedName("availableAssets")
    private List<StockTakingScannedItem> availableAssets;

    @SerializedName("productRequestQtyMap")
    private Map<Integer, BigDecimal> productRequestQtyMap;

    @SerializedName("stockTakeStatusSubCategoryMap")
    private Map<String, String> stockTakeStatusSubCategoryMap = new HashMap<>();

    public StockTakingRequest(Integer unitId, String unitType, String eventType, String eventStatus, User initiatedBy, User auditedBy,
    String subType, String isSplit) {
        this.eventId = null;
        this.unitId = unitId;
        this.unitType = unitType;
        this.eventType = eventType;
        this.eventStatus = eventStatus;
        this.initiatedBy = initiatedBy;
        this.eventCreationDate = null;
        this.auditedBy = auditedBy;
        this.auditDate = null;
        this.auditStatus = null;
        this.subType = subType;
        this.isSplit = isSplit;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(String eventStatus) {
        this.eventStatus = eventStatus;
    }

    public User getInitiatedBy() {
        return initiatedBy;
    }

    public void setInitiatedBy(User initiatedBy) {
        this.initiatedBy = initiatedBy;
    }

    public Long getEventCreationDate() {
        return eventCreationDate;
    }

    public void setEventCreationDate(Long eventCreationDate) {
        this.eventCreationDate = eventCreationDate;
    }

    public User getAuditedBy() {
        return auditedBy;
    }

    public void setAuditedBy(User auditedBy) {
        this.auditedBy = auditedBy;
    }

    public Long getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Long auditDate) {
        this.auditDate = auditDate;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }


    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public List<StockTakingScannedItem> getAvailableAssets() {
        return availableAssets;
    }

    public void setAvailableAssets(List<StockTakingScannedItem> availableAssets) {
        this.availableAssets = availableAssets;
    }

    public Map<Integer, BigDecimal> getProductRequestQtyMap() {
        return productRequestQtyMap;
    }

    public void setProductRequestQtyMap(Map<Integer, BigDecimal> productRequestQtyMap) {
        this.productRequestQtyMap = productRequestQtyMap;
    }

    public List<StockTakingScannedItem> getExtraScannedItems() {
        return extraScannedItems;
    }

    public void setExtraScannedItems(List<StockTakingScannedItem> extraScannedItems) {
        this.extraScannedItems = extraScannedItems;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(String isSplit) {
        this.isSplit = isSplit;
    }

    public Map<String, String> getStockTakeStatusSubCategoryMap() {
        return stockTakeStatusSubCategoryMap;
    }

    public void setStockTakeStatusSubCategoryMap(Map<String, String> stockTakeStatusSubCategoryMap) {
        this.stockTakeStatusSubCategoryMap = stockTakeStatusSubCategoryMap;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }
}
