package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SortedList;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.DataSetObserver;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ExpandableList<PERSON>dapter;
import android.widget.ExpandableListView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;
import android.widget.Toast;

import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.w3c.dom.Text;

import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class StockTakingEventSummary extends AppCompatActivity implements Serializable, View.OnClickListener, AdapterView.OnItemSelectedListener {

    private StockTakingEvent stockTakingEventResponse;
    private int lostAssetsCount = 0;
    private AlertDialog.Builder pleaseWaitBuilder = null;
    private AlertDialog pleaseWait;
    private View v;

    private ExpandableListView expandableListViewFound;
    private ExpandableListView expandableListViewLost;
    private ExpandableListAdapter expandableListAdapterFound;
    private ExpandableListAdapter expandableListAdapterLost;
    private List<String> expandableTitleList;

     private HashMap<String, List<StockTakingScannedItem>> expandableDetailListFound;
     private HashMap<String, List<StockTakingScannedItem>> expandableDetailListLost;

     private Map<String,List<StockTakingScannedItem>> assetCategoryMap;


     private List<StockTakingScannedItem> assetsList;

     private List<StockTakingScannedItem> extraScannedItems;

    private List<String> dropDownListFilter = UtilClass.utilClass.getFilterTypes();

    private Spinner dropDownFilterSelection;

    private String currentSelectedFilter = "CATEGORY";


    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_stock_taking_event_summary);
        RecyclerView assetsSummaryRecyclerView = findViewById(R.id.assets_summary_recycler_view);
        dropDownFilterSelection = findViewById(R.id.listTypeFilterSummary);
        Button scanAgainButton = findViewById(R.id.scan_again_btn);
        Button submitAnywayButton = findViewById(R.id.submit_anyway_btn);
        v = findViewById(R.id.scan_again_btn);
        pleaseWaitBuilder = getDialogProgressBar();
        pleaseWait = pleaseWaitBuilder.create();

        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Summary");
        if(Objects.nonNull( prefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }

        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(StockTakingEventSummary.this, android.R.layout.simple_spinner_item, dropDownListFilter);
        dropDownFilterSelection.setAdapter(spinnerArrayAdapter);
        dropDownFilterSelection.setOnItemSelectedListener(StockTakingEventSummary.this);


        assetsSummaryRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        StockTakingSummaryListAdapter mAdapter = new StockTakingSummaryListAdapter(StockTakingEventSummary.this, new ArrayList<>());
        assetsSummaryRecyclerView.setAdapter(mAdapter);


        scanAgainButton.setOnClickListener(this);
        submitAnywayButton.setOnClickListener(this);



    }

    private void processItemsToDisplayByFilter() {
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        SharedPreferences previousPref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        Map<String,?> previousPrefs = previousPref.getAll();
        Log.d("sd",new Gson().toJson(prefs));
        Log.d("version",  pref.getString("version","0"));


        Intent intent = getIntent();
        if (previousPrefs.containsKey("StockTakingEventResponse")) {
            Gson gson = new Gson();
            String temp  = (String) previousPrefs.get("StockTakingEventResponse");
            stockTakingEventResponse = gson.fromJson(temp,StockTakingEvent.class);
        }
        Gson gson = new Gson();
        if(previousPrefs.containsKey("pendingAssetsJson")){
            String pendingAssetsJson = (String) previousPrefs.get("pendingAssetsJson");
            String scannedAssetsJson = (String) previousPrefs.get("scannedAssets");

            Type type = new TypeToken<HashMap<String,List<StockTakingScannedItem>>>(){}.getType();
            expandableDetailListLost = gson.fromJson(pendingAssetsJson,type);
            expandableDetailListFound = gson.fromJson(scannedAssetsJson,type);

        }


        if(intent.hasExtra("extraScannedItems")){
            String extraScannedItemsJson = intent.getStringExtra("extraScannedItems");
            Type type = new TypeToken<List<StockTakingScannedItem>>(){}.getType();
            extraScannedItems = gson.fromJson(extraScannedItemsJson,type);
        }
        List<StockTakingScannedItem> assets = stockTakingEventResponse.getStockTakingScannedItemsList();
        Integer nonScannableFound = 0;
        Integer nonScannableLost = 0;
        Integer scannableFound = 0;
        Integer scannableLost = 0;
        for(StockTakingScannedItem item : assets){
            if(Boolean.TRUE.equals(item.getNonScannable())){
                if(item.isFound()){
                    if(!expandableDetailListFound.containsKey(getType(item))){
                        expandableDetailListFound.put(getType(item),new ArrayList<>());
                        if(!expandableDetailListLost.containsKey(getType(item))){
                            expandableDetailListLost.put(getType(item),new ArrayList<>());
                        }
                    }
                    expandableDetailListFound.get(getType(item)).add(item);
                    nonScannableFound++;
                }else{
                    if(!expandableDetailListLost.containsKey(getType(item))){
                        expandableDetailListLost.put(getType(item),new ArrayList<>());
                        if(!expandableDetailListFound.containsKey(getType(item))){
                            expandableDetailListFound.put(getType(item),new ArrayList<>());
                        }
                    }
                    expandableDetailListLost.get(getType(item)).add(item);
                    nonScannableLost++;
                }
            }else{
                if(item.isFound()){
                    scannableFound++;
                }else{
                    scannableLost++;
                }
            }
        }

        TextView nonScannableFoundView = findViewById(R.id.nonscannableFound);
        nonScannableFoundView.setText(String.valueOf(nonScannableFound));

        TextView nonScannableLostView = findViewById(R.id.nonscannableLost);
        nonScannableLostView.setText(String.valueOf(nonScannableLost));

        TextView scannableFoundView = findViewById(R.id.scannableFound);
        scannableFoundView.setText(String.valueOf(scannableFound));

        TextView scannableLostView = findViewById(R.id.scannableLost);
        scannableLostView.setText(String.valueOf(scannableLost));


        assetCategoryMap = mergeMaps();


        expandableListViewFound = (ExpandableListView) findViewById(R.id.expandableListViewFound);
        expandableTitleList = new ArrayList<String>(assetCategoryMap.keySet());
        expandableListAdapterFound = new CustomizedExpandableListAdapter(this, expandableTitleList, assetCategoryMap,
                expandableDetailListFound,expandableDetailListLost,false);
        expandableListViewFound.setAdapter(expandableListAdapterFound);
    }


    private Map<String,List<StockTakingScannedItem>> mergeMaps(){
        List<StockTakingScannedItem> allAssets = new ArrayList<>();
        Map<String, List<StockTakingScannedItem>> mergedMap = new HashMap<>();
        for(List<StockTakingScannedItem> items : expandableDetailListFound.values()){
            allAssets.addAll(items);
            for(StockTakingScannedItem item : items){
                if(!mergedMap.containsKey(getType(item))){
                    mergedMap.put(getType(item),new ArrayList<>());
                }
                item.setFound(true);
                mergedMap.get(getType(item)).add(item);

            }
        }

        for(List<StockTakingScannedItem> items : expandableDetailListLost.values()){
            allAssets.addAll(items);
            for(StockTakingScannedItem item : items){
                if(!mergedMap.containsKey(getType(item))){
                    mergedMap.put(getType(item),new ArrayList<>());
                }
                item.setFound(false);
                lostAssetsCount++;
                mergedMap.get(getType(item)).add(item);

            }
        }

        for(Map.Entry<String,List<StockTakingScannedItem>> itemEntry  : mergedMap.entrySet()){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                itemEntry.getValue().sort(new Comparator<StockTakingScannedItem>() {
                    @Override
                    public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                        return o1.isFound().compareTo(Boolean.TRUE);
                    }
                });
                mergedMap.put(itemEntry.getKey(),itemEntry.getValue());
            }
        }

        stockTakingEventResponse.setStockTakingScannedItemsList(allAssets);
        assetsList = allAssets;
        return mergedMap;
    }

    private int getLostAssetsCount() {
        List<StockTakingScannedItem> list = stockTakingEventResponse.getStockTakingScannedItemsList();
        int n = list.size();
        int lostCount = 0;
        for (int i = 0; i < n; i++) {
            StockTakingScannedItem item = list.get(i);
            if (item.isExists() && !item.isFound())
                lostCount++;
        }
        return lostCount;
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.scan_again_btn) {
            onBackPressed();
        } else if (v.getId() == R.id.submit_anyway_btn) {
            if(!isConnected()){
                Snackbar.make(v,"Check internet connection!",Snackbar.LENGTH_SHORT).show();
                return;
            }
            if(lostAssetsCount>0)
                showAlert();
            else
                submitEventAnyway();
        }
    }

    private void showAlert() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(StockTakingEventSummary.this);
        alertDialog.setTitle("Submit Event");
        alertDialog.setPositiveButton("CANCEL", (dialog, which) -> dialog.cancel());
        alertDialog.setMessage(lostAssetsCount + " assets are not scanned. You Can't Submit");
        alertDialog.setNegativeButton("OK", (dialog, which) -> {
                dialog.cancel();
            });
alertDialog.show();
    }



    private void submitEventAnyway() {
        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sh.getString("jwtToken", null);

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder().baseUrl(BuildConfig.SCM_BASE)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        //stockTakingEventResponse.setStatus(EventAssetMappingStatus.CREATE);
        stockTakingEventResponse.setStockTakingScannedItemsList(assetsList);
        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        if (prefs.containsKey("eventResponse")) {
            try {
                StockTakingRequest stockTakingRequest = UtilClass.gson.fromJson((String) prefs.get("eventResponse"), StockTakingRequest.class);
                if (Objects.nonNull(stockTakingRequest)) {
                    stockTakingEventResponse.setParentId(stockTakingRequest.getParentId());
                    stockTakingEventResponse.setSubCategory(stockTakingRequest.getSubCategory());
                }
            } catch (Exception e) {
                Toast.makeText(getApplicationContext(), "Exception Occurred while Setting Parent Id", Toast.LENGTH_SHORT).show();
            }
        }
        Call<Boolean> submitCall = unitsApiService.pauseSubmitStockEvent(token, stockTakingEventResponse,true);
        Gson gson = new Gson();
        String json = gson.toJson(stockTakingEventResponse);
        submitCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && Objects.nonNull(scmError.getErrorMessage())) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "Submit API Response is Not Successful", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Can not Submit Event.!", Toast.LENGTH_SHORT).show();
                    }
                    return;
                }
                if (response.body() == null) {
                    Snackbar.make(v, "Submit Anyway: Null response received.", Snackbar.LENGTH_SHORT).show();
                }
                    SharedPreferences sharedPreferences = getSharedPreferences("SavedStockTakingEventPREF", MODE_PRIVATE);
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.clear();
                    editor.apply();

                    SharedPreferences sharedPreferences1 = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                    SharedPreferences.Editor myEditor = sharedPreferences1.edit();
                    myEditor.clear();
                    myEditor.apply();

                    pleaseWait.dismiss();
//                if(response.errorBody())
                    Toast.makeText(StockTakingEventSummary.this, "Event submitted successfully!", Toast.LENGTH_LONG).show();
                    Intent intent = new Intent(StockTakingEventSummary.this, StockTaking.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                    finish();
            }

            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Snackbar.make(v, "Submit Anyway: Some error Occurred", Snackbar.LENGTH_SHORT).show();
                pleaseWait.dismiss();
            }
        });
    }

    private void generateReport(){
        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sh.getString("jwtToken", null);

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder().baseUrl(BuildConfig.SCM_BASE)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        stockTakingEventResponse.setStockTakingScannedItemsList(assetsList);
        Call<Boolean> reportCall = unitsApiService.generateReport(token,stockTakingEventResponse.getEventId());
        Gson gson = new Gson();
        String json = gson.toJson(stockTakingEventResponse);
        reportCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (response.body() == null) {
                    Snackbar.make(v, "Couldn't Generate Report", Snackbar.LENGTH_SHORT).show();
                }else{
                    Snackbar.make(v, "SuccessFully Generated Report", Snackbar.LENGTH_SHORT).show();
                }
                pleaseWait.dismiss();
            }
            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Snackbar.make(v, "Couldn't Generate Report", Snackbar.LENGTH_SHORT).show();
                pleaseWait.dismiss();
            }
        });

    }

    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }


    public AlertDialog.Builder getDialogProgressBar() {

        if (pleaseWaitBuilder == null) {
            pleaseWaitBuilder = new AlertDialog.Builder(this);

            pleaseWaitBuilder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            pleaseWaitBuilder.setView(progressBar);
        }
        return pleaseWaitBuilder;
    }       // AlertDialog


    private String getType(StockTakingScannedItem scannedItem1) {
        if (Objects.nonNull(currentSelectedFilter)) {
            if (currentSelectedFilter.equalsIgnoreCase("CATEGORY")) {
                return scannedItem1.getSubCategory();
            } else if (currentSelectedFilter.equalsIgnoreCase("PRODUCT")) {
                return scannedItem1.getProductName();
            } else {
                return scannedItem1.getSkuName();
            }
        }
        return scannedItem1.getSubCategory();
    }
    @Override
    public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
        currentSelectedFilter = dropDownListFilter.get(i);
        processItemsToDisplayByFilter();
    }

    @Override
    public void onNothingSelected(AdapterView<?> adapterView) {

    }
}
