package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*"eventId",
        "unitId",
        "status",
        "stockEventAssetMappingDefinitions",*/

public class StockTakingEvent implements Serializable {
    private Integer eventId;
    private Integer unitId;
    private EventAssetMappingStatus status;

    private String subType;

    private Integer parentId;

    private String subCategory;
    @SerializedName("stockEventAssetMappingDefinitions")
    private List<StockTakingScannedItem> stockTakingScannedItemsList = new ArrayList<>();

    private List<StockTakingScannedItem> extraScannedItems = new ArrayList<>();

    public StockTakingEvent(Integer eventId, Integer unitId, EventAssetMappingStatus status, List<StockTakingScannedItem> stockTakingScannedI<PERSON><PERSON><PERSON><PERSON>) {
        this.eventId = eventId;
        this.unitId = unitId;
        this.status = status;
        this.stockTakingScannedItemsList = stockTakingScannedItemsList;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public EventAssetMappingStatus getStatus() {
        return status;
    }

    public void setStatus(EventAssetMappingStatus status) {
        this.status = status;
    }

    public List<StockTakingScannedItem> getStockTakingScannedItemsList() {
        return stockTakingScannedItemsList;
    }

    public void setStockTakingScannedItemsList(List<StockTakingScannedItem> stockTakingScannedItemsList) {
        this.stockTakingScannedItemsList = stockTakingScannedItemsList;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public List<StockTakingScannedItem> getExtraScannedItems() {
        return extraScannedItems;
    }

    public void setExtraScannedItems(List<StockTakingScannedItem> extraScannedItems) {
        this.extraScannedItems = extraScannedItems;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }
}
