package com.example.chaayosStockTake;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.Manifest;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;


import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class AssetLookup extends AppCompatActivity implements View.OnClickListener {
    private TextView assetUnitId;
    private TextView assetId;
    private TextView assetName;
    private ListView attributesListView;
    private DecoratedBarcodeView decoratedBarcodeViewScanner;
    private Button scanButton;
    private TextView scanSomethingMessage;
    private ConstraintLayout assetDetailsConstraintLayout;
    private TextView qrTagValue;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_asset_lookup);
        requestPermission();

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Asset Lookup");

        assetId = findViewById(R.id.asset_id_tv);
        assetName = findViewById(R.id.asset_name_tv);
        assetUnitId = findViewById(R.id.unit_id_tv);
        attributesListView = findViewById(R.id.attributes_list);
        scanSomethingMessage = findViewById(R.id.scan_something_message);
        scanButton = findViewById(R.id.scan_btn);
        assetDetailsConstraintLayout = findViewById(R.id.asset_details_constraint_layout);
        qrTagValue = findViewById(R.id.qr_tag_value);
        scanButton.setOnClickListener(this);



        decoratedBarcodeViewScanner = findViewById(R.id.dbv_barcode_scanner);
        decoratedBarcodeViewScanner.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                String scannedItem = result.getText();
                qrTagValue.setText(scannedItem);
                showAssetDetails(scannedItem);
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {

            }
        });

    }

    private void showAssetDetails(String scannedItem) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);

        Call<Object> assetLookupCall = unitsApiService.getAssetDetails(token, scannedItem);
        assetLookupCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if(response.body()== null){
                    decoratedBarcodeViewScanner.pause();
                    Toast.makeText(getApplicationContext(),"Invalid QR Code",Toast.LENGTH_SHORT).show();
                    return;
                }
                scanSomethingMessage.setVisibility(View.GONE);
                assetDetailsConstraintLayout.setVisibility(View.VISIBLE);

                String asset = new Gson().toJson(response.body());
                JsonObject jsonObject = new Gson().fromJson(asset, JsonObject.class);

                String assetNameValue = jsonObject.get("assetName").toString();
                String assetIdValue = jsonObject.get("assetId").toString();
                String assetUnitIdValue = jsonObject.get("unitId").toString();


                assetNameValue = assetNameValue.substring(1, assetNameValue.length()-1);
                assetIdValue = assetIdValue.substring(0, assetIdValue.length()-2);
                assetUnitIdValue = assetUnitIdValue.substring(0, assetUnitIdValue.length()-2);

                String assetValue = assetNameValue + " ( " + assetIdValue + " )";

                String unitNameValue = jsonObject.get("unitName").toString();
                unitNameValue = unitNameValue.substring(1,unitNameValue.length()-1);

                String assetUnitValue = unitNameValue + " ( " + assetUnitIdValue + " ) ";

                assetName.setText(assetNameValue);
                assetId.setText(assetValue);
                assetUnitId.setText(assetUnitValue);
                JsonArray attributes = jsonObject.getAsJsonArray("entityAttributeValueMappings");
                decoratedBarcodeViewScanner.pause();
                if(attributes!=null)
                    setAttributesListView(attributes);
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(),"Server Error",Toast.LENGTH_SHORT).show();
                return;
            }
        });
    }

    private void setAttributesListView(JsonArray attributes) {
        ArrayList<String> attributeIdList = new ArrayList<>();
        ArrayList<String> attributeNameList = new ArrayList<>();
        for(int i=0;i<attributes.size();i++){
            JsonObject attr = attributes.get(i).getAsJsonObject();
            String attrName = attr.get("attributeName").toString();
            String attrId = attr.get("attributeId").toString();
            attrId = attrId.substring(0,attrId.length()-2);
            attrName = attrName.substring(1,attrName.length()-1);
            attributeIdList.add(attrId);
            attributeNameList.add(attrName);
        }

        AttributesListAdapter adapter1 = new AttributesListAdapter(this,attributeIdList,attributeNameList);
        attributesListView.setAdapter(adapter1);
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    protected void resumeScanner() {
        if (!decoratedBarcodeViewScanner.isActivated())
            decoratedBarcodeViewScanner.resume();
    }

    protected void pauseScanner() {
        //Toast.makeText(getApplicationContext(),itemsListData.toString(),Toast.LENGTH_SHORT).show();
        decoratedBarcodeViewScanner.pause();
    }

    void requestPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, 0);
        }
    }


    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.scan_btn){
            if (!decoratedBarcodeViewScanner.isActivated()) {
                decoratedBarcodeViewScanner.resume();
                scanSomethingMessage.setVisibility(View.VISIBLE);
                assetDetailsConstraintLayout.setVisibility(View.GONE);
                scanButton.setText("Scan Again");
            }
        }
    }
}
