package com.example.chaayosStockTake.util;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Build;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Toast;

import com.example.chaayosStockTake.StockTakingScannedItem;
import com.google.gson.Gson;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public class UtilClass {

    public static Gson gson;

    public static UtilClass utilClass;

    static {
        gson = new Gson();
        utilClass = new UtilClass();
    }


    private AlertDialog.Builder pleaseWaitBuilder;

    public AlertDialog alertDialog;

    public Toast toast;

    public AlertDialog.Builder getDialogProgressBar(Context context) {

            pleaseWaitBuilder = new AlertDialog.Builder(context);

            pleaseWaitBuilder.setTitle("Please Wait...");

            ProgressBar progressBar = new ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(false);
            pleaseWaitBuilder.setView(progressBar);
        return pleaseWaitBuilder;
    }

    public AlertDialog getAlertDialog(Context context, String title, String msg) {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(context);
        alertDialog.setTitle(title);
        alertDialog.setMessage(msg);
        alertDialog.setPositiveButton("Ok", (dialog, which) -> dialog.cancel());
        return alertDialog.create();
    }

    public Toast getToast(Context context, String textMessage) {
        return Toast.makeText(context, textMessage, Toast.LENGTH_SHORT);
    }

    public List<String> getFilterTypes() {
        return Arrays.asList("CATEGORY","PRODUCT","SKU");
    }

    public List<StockTakingScannedItem> sortAssets(List<StockTakingScannedItem> assetList){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            assetList.sort(new Comparator<>() {
                @Override
                public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                    return o1.getAssetName().compareTo(o2.getAssetName());
                }
            });
        }
        return assetList;
    }
}
