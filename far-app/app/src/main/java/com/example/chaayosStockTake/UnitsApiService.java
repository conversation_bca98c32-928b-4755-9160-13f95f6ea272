package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */

import com.example.chaayosStockTake.domain.StockTakeInitResponse;

import java.util.List;
import java.util.Map;

import okhttp3.MultipartBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Part;
import retrofit2.http.Query;

public interface UnitsApiService {

/*    String BASE = "http://**************:8089/master-service/rest/v1/";
    String SCM_BASE = "http://**************:9090/scm-service/rest/v1/";*/

    /*@GET("unit-metadata/all-units-list")
    Call<List<Unit>> getAllUnits();
*/
    @POST("user-management/user/units")
    Call<List<Unit>> getAllUnits(@Body Map<String, Object> request);

    @POST("users/login")
    Call<Object> getLoginDetails(@Body LoginRequest loginDetails);

    @Headers("Content-Type: application/json")
    @POST("asset-management/stock-event")
    Call<StockTakeInitResponse> initiateNewStockEvent(@Header("auth") String jwtToken, @Body StockTakingRequest request);

    @GET("asset-management/stock-event-unit")
    Call<StockTakeInitResponse> getStockTakingEvents(@Header("auth") String jwtToken, @Query("unitId") Integer unitId,
                                                     @Query("eventStatus") String eventStatus);

    @Headers("Content-Type: application/json")
    @PUT("asset-management/stock-event")
    Call<StockTakingRequest> abandonEvent(@Header("auth") String jwtToken, @Body StockTakingRequest request);

    @Headers("Content-Type: application/json")
    @POST("asset-management/stock-event-verification-creation")
    Call<StockTakingEvent> verifyAndCreateEventAssetMapping(@Header("auth") String jwtToken, @Body StockTakingEvent stockTakingEvent);

    @Headers("Content-Type: application/json")
    @POST("asset-management/stock-event-pause-submit")
    Call<Boolean> pauseSubmitStockEvent(@Header("auth") String jwtToken, @Body StockTakingEvent stockTakingEvent , @Query("isSubmit") Boolean isSubmit);

    @GET("asset-management/stock-take-list")
    Call<List<String>> getStockTakeList(@Header("auth") String jwtToken);

    @GET("user-management/user-role")
    Call<Map<String, String>> getAuditors(@Header("auth") String token, @Query("role") String role);

    @GET("asset-management/asset-tagValue")
    Call<Object> getAssetDetails(@Header("auth") String token, @Query("tagValue") String tagValue);

    @GET("asset-management/stock-take-list-sub")
    Call<Object> getStockTakeSubList(@Header("auth") String token);

    @POST("user-management/users/unit")
    Call<Object> getEmployeesForUnit(@Header("auth") String token, @Body Integer unitId);

    @Multipart
    @POST("asset-management/save-excess-asset-found")
    Call<Object> saveExtraItemData(@Header("auth") String token , @Part MultipartBody.Part file ,
                                   @Part("comment") String comment , @Part("eventId") Integer eventId ,
                                   @Part("productName") String productname);

    @POST("asset-management/fa-stock-take-report")
    Call<Boolean> generateReport(@Header("auth") String token , @Query("eventId") Integer eventId);


    @Headers("Content-Type: application/json")
    @POST("asset-management/child-stock-event")
    Call<StockTakingRequest> initiateChildEvent(@Header("auth") String jwtToken, @Body StockTakingRequest stockTakingChildRequest);
}
