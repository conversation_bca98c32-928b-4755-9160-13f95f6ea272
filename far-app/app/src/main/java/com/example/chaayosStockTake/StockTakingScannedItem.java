package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
/*        "eventAssetMappingId",
                "eventId",
                "unitId",
                "assetId",
                "assetTagValue",
                "assetStatus",
                "found",
                "exists",
                "creationDate",
                "auditedBy",
                "auditDate",
                "auditStatus"*/

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.Objects;

public class StockTakingScannedItem implements Serializable {
    private Integer eventAssetMappingId;
    private Integer eventId;
    private Integer unitId;
    private Integer assetId;
    private String assetTagValue;
    private String assetName;
    private AssetStatusType assetStatus;
    private boolean found;
    private boolean exists;
    private Long creationDate;
    private User auditedBy;
    private Long auditDate;
    private String auditStatus;

    private Boolean checked;

    private Boolean nonScannable;

    private String subCategory;

    private Integer productId;

    @SerializedName("productName")
    private String productName;

    private String imageUrl;

    private String unitName;

    private String skuName;


    public StockTakingScannedItem(Integer eventId, Integer unitId, String assetTagValue, boolean found, User auditedBy) {
        this.eventId = eventId;
        this.unitId = unitId;
        this.assetTagValue = assetTagValue;
        this.found = found;
        this.eventAssetMappingId = null;
        this.assetId = null;
        this.assetStatus = null;
        this.exists = false;
        this.creationDate = null;
        this.auditedBy = auditedBy;
        this.auditDate = null;
        this.auditStatus = null;
        this.assetName=null;
        this.checked =null;
        this.nonScannable = null;
        this.subCategory =null;
    }

    public StockTakingScannedItem() {

    }

    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    public Integer getEventAssetMappingId() {
        return eventAssetMappingId;
    }

    public void setEventAssetMappingId(Integer eventAssetMappingId) {
        this.eventAssetMappingId = eventAssetMappingId;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getAssetTagValue() {
        return assetTagValue;
    }

    public void setAssetTagValue(String assetTagValue) {
        this.assetTagValue = assetTagValue;
    }

    public AssetStatusType getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(AssetStatusType assetStatus) {
        this.assetStatus = assetStatus;
    }

    public Boolean isFound() {
        return found;
    }

    public void setFound(boolean found) {
        this.found = found;
    }

    public boolean isExists() {
        return exists;
    }

    public void setExists(boolean exists) {
        this.exists = exists;
    }

    public Long getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Long creationDate) {
        this.creationDate = creationDate;
    }

    public User getAuditedBy() {
        return auditedBy;
    }

    public void setAuditedBy(User auditedBy) {
        this.auditedBy = auditedBy;
    }

    public Long getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Long auditDate) {
        this.auditDate = auditDate;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public Boolean getNonScannable() {
        return nonScannable;
    }

    public void setNonScannable(Boolean nonScannable) {
        this.nonScannable = nonScannable;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }


    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof StockTakingScannedItem)) return false;
        StockTakingScannedItem that = (StockTakingScannedItem) o;
        return Objects.equals(getUnitId(), that.getUnitId()) && Objects.equals(getAssetId(), that.getAssetId()) && Objects.equals(getAssetTagValue(), that.getAssetTagValue()) && Objects.equals(getAssetName(), that.getAssetName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getUnitId(), getAssetId(), getAssetTagValue(), getAssetName());
    }
}
