{"name": "chaiwala", "version": "0.0.1", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest"}, "dependencies": {"native-base": "^2.8.1", "react": "16.6.1", "react-native": "^0.57.7", "react-native-gesture-handler": "^1.0.10", "react-native-i18n": "^2.0.15", "react-native-vector-icons": "^6.1.0", "react-navigation": "^3.0.4", "react-redux": "^5.1.1", "redux": "^4.0.1"}, "devDependencies": {"babel-jest": "23.6.0", "jest": "23.6.0", "metro-react-native-babel-preset": "0.50.0", "react-test-renderer": "16.6.1"}, "jest": {"preset": "react-native"}}