# To learn about <PERSON> see [Doc<PERSON>](https://buckbuild.com/).
# To run your application with <PERSON>:
# - install Buck
# - `npm start` - to start the packager
# - `cd android`
# - `keytool -genkey -v -keystore keystores/debug.keystore -storepass android -alias androiddebugkey -keypass android -dname "CN=Android Debug,O=Android,C=US"`
# - `./gradlew :app:copyDownloadableDepsToLibs` - make all Gradle compile dependencies available to Buck
# - `buck install -r android/app` - compile, install and run application
#

lib_deps = []

for jarfile in glob(['libs/*.jar']):
  name = 'jars__' + jarfile[jarfile.rindex('/') + 1: jarfile.rindex('.jar')]
  lib_deps.append(':' + name)
  prebuilt_jar(
    name = name,
    binary_jar = jarfile,
  )

for aarfile in glob(['libs/*.aar']):
  name = 'aars__' + aarfile[aarfile.rindex('/') + 1: aarfile.rindex('.aar')]
  lib_deps.append(':' + name)
  android_prebuilt_aar(
    name = name,
    aar = aarfile,
  )

android_library(
    name = "all-libs",
    exported_deps = lib_deps,
)

android_library(
    name = "app-code",
    srcs = glob([
        "src/main/java/**/*.java",
    ]),
    deps = [
        ":all-libs",
        ":build_config",
        ":res",
    ],
)

android_build_config(
    name = "build_config",
    package = "com.chaiwala",
)

android_resource(
    name = "res",
    package = "com.chaiwala",
    res = "src/main/res",
)

android_binary(
    name = "app",
    keystore = "//android/keystores:debug",
    manifest = "src/main/AndroidManifest.xml",
    package_type = "debug",
    deps = [
        ":app-code",
    ],
)
