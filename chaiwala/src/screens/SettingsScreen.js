import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View, Image } from 'react-native';
import { Header, Left, Icon, Body, Right, Title } from "native-base";
import HeaderView from "./HeaderView";

export default class SettingsScreen extends Component {

  render() {
    return (
      <View style={styles.container}>
        <HeaderView menu={false} title={'Settings'} {...this.props} />
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <Text style={styles.textStyle}>Settings screen</Text>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5FCFF',
  },
  icon: {
    width: 24,
    height: 24,
  },
  textStyle: {
    color: "#000"
  }
});
