import React, {Component} from 'react';
import { Header, Left, Right, Icon, Body, Title } from "native-base";

export default class HeaderView extends Component {

  static navigationOptions = {
  };

  toggleDrawer = () => {
    this.props.navigation.toggleDrawer()
  }

  render() {
    return (
      <Header>
        <Left>
          {this.props.menu?(
            <Icon ios="menu" android="md-menu" style={{fontSize: 20, color: 'white'}} onPress={this.toggleDrawer} />
          ):(
            <Icon ios="ios-arrow-back" android="md-arrow-back" style={{fontSize: 20, color: 'white'}} onPress={()=> this.props.navigation.goBack()} />
          )}
        </Left>
        <Body>
          <Title>
            {this.props.title}
          </Title>
        </Body>
        <Right />
      </Header>
    );
  }
}
