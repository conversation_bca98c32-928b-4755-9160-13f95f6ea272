import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View, Image } from 'react-native';
import { Icon } from "native-base";
import HeaderView from "./HeaderView";

export default class CustomerHomeScreen extends Component {

  render() {
    return (
      <View style={styles.container}>
        <HeaderView menu={true} title={'Home'} {...this.props} />
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <Text style={styles.textStyle}>Home screen</Text>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5FCFF',
  },
  icon: {
    width: 24,
    height: 24,
  },
  textStyle: {
    color: "#000"
  }
});
