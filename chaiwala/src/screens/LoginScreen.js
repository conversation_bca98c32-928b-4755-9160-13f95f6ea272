import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View, Image, TextInput, TouchableOpacity, TouchableHighlight } from 'react-native';
import { Icon, Form, Item as FormItem, Input, Container, Grid, Col, Label, Button } from "native-base";
import HeaderView from "./HeaderView";

export default class LoginScreen extends Component {

  onButtonPress = () => {

  }
  login = () => {

  }

  render() {
    return (
      <Container>
        <View style={styles.container}>
          <View style={styles.loginContainer}>
            <Image resizeMode="contain" style={styles.logo} source={require('../assets/img/loginLogo.png')} />
          </View>

          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <Icon ios="ios-contact" android="md-contact" style={{color:'#fff', fontSize:20}} />
              <TextInput style={styles.inputs}
                  placeholder="Contact"
                  keyboardType="number-pad"
                  underlineColorAndroid='transparent'
                  placeholderTextColor='rgba(225,225,225,1)'
                  onSubmitEditing={() => this.passwordInput.focus()}
                  onChangeText={(email) => this.setState({email})} />
            </View>
            <View style={styles.inputContainer}>
              <Icon ios="ios-key" android="md-key" style={{color:'#fff', fontSize:20}} />
              <TextInput style={styles.inputs}
                  placeholder="Password"
                  returnKeyType="go"
                  underlineColorAndroid='transparent'
                  placeholderTextColor='rgba(225,225,225,1)'
                  ref={(input)=> this.passwordInput = input}
                  secureTextEntry
                  onChangeText={(email) => this.setState({email})}/>
            </View>

            <TouchableHighlight style={[styles.buttonContainer, styles.loginButton]} onPress={() => this.onClickListener('login')}>
              <Text style={styles.loginText}>LOGIN</Text>
            </TouchableHighlight>

            {this.props.showError?(
              <TouchableHighlight>
                  <Text style={[styles.errorMessage]}>{this.props.errorMessage}</Text>
              </TouchableHighlight>
            ):null}
          </View>
        </View>
      </Container>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    //backgroundColor: 'rgba(45,61,84,1)',
    backgroundColor: '#d4272a'
  },
  loginContainer:{
    alignItems: 'center',
    flexGrow: 1,
    justifyContent: 'center'
  },
  logo: {
    position: 'absolute',
    width: 300,
    height: 100
  },
  formContainer:{
    alignItems: 'center',
    flexGrow: 1,
    justifyContent: 'center'
  },
  inputContainer: {
      borderBottomColor: '#FFF',
      backgroundColor: 'transparent',
      borderBottomWidth: .5,
      width:250,
      height:45,
      marginBottom:20,
      flexDirection: 'row',
      alignItems:'center'
  },
  inputs:{
      height:45,
      marginLeft:16,
      flex:1,
  },
  buttonContainer: {
    height:45,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom:20,
    width:250,
    //backgroundColor: 'rgba(7,181,206,1)',
    backgroundColor: '#b70808'
  },
  errorMessage:{
    color: 'red'
  }
});
