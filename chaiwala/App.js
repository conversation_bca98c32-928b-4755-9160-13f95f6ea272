/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow
 */

import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View, SafeAreaView, ScrollView, Dimensions, Image } from 'react-native';
import { createDrawerNavigator, createAppContainer, DrawerItems } from "react-navigation";
import { Icon } from "native-base";
import LoginScreen from "./src/screens/LoginScreen";
import CustomerHomeScreen from "./src/screens/CustomerHomeScreen";
import SettingsScreen from "./src/screens/SettingsScreen";

type Props = {};
const language = [
  {lang:"English", code:"en"},
  {lang:"Hindi", code:"hi"}
]

export default class App extends Component<Props> {

  someEvent() {
    // call navigate for AppNavigator here:
    this.navigator &&
      this.navigator.dispatch(
        NavigationActions.navigate({ routeName: someRouteName })
      );
  }


  render() {
    return (
      <SafeAreaView style={{flex:1, backgroundColor: '#fff'}}>
        <AppContainer />
      </SafeAreaView>
    );
  }
}

const CustomDrawerComponent = (props) => (
  <SafeAreaView style={{flex:1}}>
    <View style={{alignItems:'center', justifyContent:'center', padding:20}}>
      <Image source={require('./src/assets/img/chat-icon.png')}
        style={[styles.icon, {tintColor: 'red'}]} />
      <Text>ChaiWala</Text>
    </View>
    <ScrollView>
      <DrawerItems {...props} />
    </ScrollView>
  </SafeAreaView>
  )

const AppDrawerNavigator = createDrawerNavigator({
  Home: {
    screen:CustomerHomeScreen,
    navigationOptions:{
      drawerLabel: 'Home',
      drawerIcon: ({ tintColor }) => (
        <Icon name="home" style={{color: tintColor, fontSize:20}} />
      ),
    }
  },
  Login: {
    screen:LoginScreen,
    navigationOptions:{
      drawerLabel: 'Login',
      drawerIcon: ({ tintColor }) => (
        <Icon name="home" style={{color: tintColor, fontSize:20}} />
      ),
    }
  },
  Settings: {
    screen: SettingsScreen,
    navigationOptions : {
      drawerLabel: 'Settings',
      drawerIcon: ({ tintColor }) => (
        <Icon ios="ios-settings" android="md-settings" style={{color: tintColor, fontSize:20}} />
      ),
    }
  }
},{
    contentComponent:CustomDrawerComponent,
    initialRoute: "Login",
    contentOptions: {
      activeTintColor: 'red',
      inactiveTintColor: 'grey'
    }
});

  const AppContainer = createAppContainer(AppDrawerNavigator);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5FCFF',
  },
  welcome: {
    fontSize: 20,
    textAlign: 'center',
    margin: 10,
  },
  instructions: {
    textAlign: 'center',
    color: '#333333',
    marginBottom: 5,
  },
  icon:{
    width: 80,
    height: 80
  }
});
