/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.09 at 05:35:16 PM IST 
//

package com.stpl.tech.neo.domain.model.mongo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Entity;
import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TaxDetail", propOrder = { "type", "code", "percentage", "value" })
@Entity
@Document
public class TaxDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5577464341032140963L;

	@Id
	private String _id;

	/*
	 * @Version
	 * 
	 * @JsonIgnore private Long version;
	 * 
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal percentage;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal value;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal total;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal taxable;

	protected String type;

	protected String code;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*
	 * public Long getVersion() { return version; }
	 * 
	 * public void setVersion(Long version) { this.version = version; }
	 * 
	 * public String getDetachAll() { return detachAll; }
	 * 
	 * public void setDetachAll(String detachAll) { this.detachAll = detachAll;
	 * }
	 */
	/**
	 * Gets the value of the percentage property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getPercentage() {
		return percentage;
	}

	/**
	 * Sets the value of the percentage property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setPercentage(BigDecimal value) {
		this.percentage = value;
	}

	/**
	 * Gets the value of the value property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getValue() {
		return value;
	}

	/**
	 * Sets the value of the value property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setValue(BigDecimal value) {
		this.value = value;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public BigDecimal getTaxable() {
		return taxable;
	}

	public void setTaxable(BigDecimal taxable) {
		this.taxable = taxable;
	}

}
