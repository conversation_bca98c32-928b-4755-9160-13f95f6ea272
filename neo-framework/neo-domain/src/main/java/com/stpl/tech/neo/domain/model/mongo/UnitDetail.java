//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.14 at 11:12:30 AM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for UnitLocalityMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitLocalityMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="mappingId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="appLink" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="webLink" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitDetail", propOrder = {
    "id",
    "unitId",
    "unitName",
    "googleLink",
    "city",
    "region"
})
@Document
public class UnitDetail {

	@Id
    @XmlElement(required = true)
    protected String id;
    @XmlElement(required = true)
    protected Integer unitId;
    @XmlElement(required = true)
    protected String unitName;
    @XmlElement(required = true)
    protected String googleLink;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String region;
    @XmlElement(required = true)
    protected String address;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getGoogleLink() {
        return googleLink;
    }

    public void setGoogleLink(String googleLink) {
        this.googleLink = googleLink;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        UnitDetail that = (UnitDetail) o;

        return new EqualsBuilder()
                .append(id, that.id)
                .append(unitId, that.unitId)
                .append(unitName, that.unitName)
                .append(googleLink, that.googleLink)
                .append(city, that.city)
                .append(region, that.region)
                .append(address, that.address)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(id)
                .append(unitId)
                .append(unitName)
                .append(googleLink)
                .append(city)
                .append(region)
                .append(address)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "UnitDetail{" +
                "id='" + id + '\'' +
                ", unitId=" + unitId +
                ", unitName='" + unitName + '\'' +
                ", googleLink='" + googleLink + '\'' +
                ", city='" + city + '\'' +
                ", region='" + region + '\'' +
                ", address='" + address + '\'' +
                '}';
    }
}
