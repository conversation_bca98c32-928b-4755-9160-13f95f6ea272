//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.11.22 at 11:04:38 AM IST
//

package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for CustomerData complex type.
 * <p>
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="CustomerData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="contact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deviceKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sessionKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="otp" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CustomerData", propOrder = {"contact", "name", "email", "deviceKey", "sessionKey", "otp","verifiedByTrueCaller"})
public class CustomerData {

    @XmlElement(required = true)
    protected String contact;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String email;
    @XmlElement(required = true)
    protected String deviceKey;
    @XmlElement(required = true)
    protected String sessionKey;
    protected boolean update;
    protected String otp;
    protected Integer tcId;
    protected String acquisitionSource;
    protected String acquisitionToken;
    protected Integer registrationUnitId;
    protected Integer acquisitionBrandId;
    protected Integer campaignId;
    protected Boolean loginNeo;
    protected String authToken;
    protected String utmSource;
    protected String utmMedium;
    protected Integer flow;
    protected Boolean whatsappOpt;
    protected String customerEmail;
    protected String campaignToken;
    protected Integer gameScore;
    protected boolean createOffer;
    protected String sessionToken;
    protected String truecallerRequestId;



    protected boolean verifiedByTrueCaller;


    public boolean isVerifiedByTrueCaller() {
        return verifiedByTrueCaller;
    }

    public void setVerifiedByTrueCaller(boolean verifiedByTrueCaller) {
        this.verifiedByTrueCaller = verifiedByTrueCaller;
    }
    /**
     * Gets the value of the contact property.
     *
     * @return possible object is {@link String }
     */
    public String getContact() {
        return contact;
    }

    /**
     * Sets the value of the contact property.
     *
     * @param value allowed object is {@link String }
     */
    public void setContact(String value) {
        this.contact = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the email property.
     *
     * @return possible object is {@link String }
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     *
     * @param value allowed object is {@link String }
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the deviceKey property.
     *
     * @return possible object is {@link String }
     */
    public String getDeviceKey() {
        return deviceKey;
    }

    /**
     * Sets the value of the deviceKey property.
     *
     * @param value allowed object is {@link String }
     */
    public void setDeviceKey(String value) {
        this.deviceKey = value;
    }

    /**
     * Gets the value of the sessionKey property.
     *
     * @return possible object is {@link String }
     */
    public String getSessionKey() {
        return sessionKey;
    }

    /**
     * Sets the value of the sessionKey property.
     *
     * @param value allowed object is {@link String }
     */
    public void setSessionKey(String value) {
        this.sessionKey = value;
    }

    /**
     * Gets the value of the otp property.
     */
    public String getOtp() {
        return otp;
    }

    /**
     * Sets the value of the otp property.
     */
    public void setOtp(String value) {
        this.otp = value;
    }

    public boolean isUpdate() {
        return update;
    }

    public void setUpdate(boolean update) {
        this.update = update;
    }

    public Integer getTcId() {
        return tcId;
    }

    public void setTcId(Integer tcId) {
        this.tcId = tcId;
    }

    public String getAcquisitionSource() {
        return acquisitionSource;
    }

    public void setAcquisitionSource(String acquisitionSource) {
        this.acquisitionSource = acquisitionSource;
    }

    public String getAcquisitionToken() {
        return acquisitionToken;
    }

    public void setAcquisitionToken(String acquisitionToken) {
        this.acquisitionToken = acquisitionToken;
    }

    /**
     * Gets the value of the registrationUnitId property.
     */
    public Integer getRegistrationUnitId() {
        if (this.registrationUnitId == null) {
            return 0;
        }
        return this.registrationUnitId;
    }

    /**
     * Sets the value of the registrationUnitId property.
     */
    public void setRegistrationUnitId(Integer value) {
        this.registrationUnitId = value;
    }

    public Integer getAcquisitionBrandId() {
        return acquisitionBrandId;
    }

    public void setAcquisitionBrandId(Integer acquisitionBrandId) {
        this.acquisitionBrandId = acquisitionBrandId;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Boolean getLoginNeo() {
        return loginNeo;
    }

    public void setLoginNeo(Boolean loginNeo) {
        this.loginNeo = loginNeo;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }

    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }

    public Boolean getWhatsappOpt() {
        return whatsappOpt;
    }

    public void setWhatsappOpt(Boolean whatsappOpt) {
        this.whatsappOpt = whatsappOpt;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public String getCampaignToken() {
        return campaignToken;
    }

    public void setCampaignToken(String campaignToken) {
        this.campaignToken = campaignToken;
    }

    public Integer getGameScore() {
        return gameScore;
    }

    public void setGameScore(Integer gameScore) {
        this.gameScore = gameScore;
    }

    public boolean isCreateOffer() {
        return createOffer;
    }

    public void setCreateOffer(boolean createOffer) {
        this.createOffer = createOffer;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public String getTruecallerRequestId() {
        return truecallerRequestId;
    }

    public void setTruecallerRequestId(String truecallerRequestId) {
        this.truecallerRequestId = truecallerRequestId;
    }
}
