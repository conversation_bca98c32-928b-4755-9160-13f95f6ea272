//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.neo.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for Customer complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="Customer"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="firstName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="middleName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lastName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="gender" type="{http://www.w3schools.com}Gender"/&gt;
 *         &lt;element name="dob" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="profilePic" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contactNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="countryCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="emailVerified" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="emailId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="loyaltyPoints" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="registrationUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="acquisitionSource" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contactNumberVerified" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="availedSignupOffer" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="smsSubscriber" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="emailSubscriber" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="loyaltySubscriber" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="addresses" type="{http://www.w3schools.com}Address" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="createdAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastLoginTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Customer", propOrder = {
    "customerId",
    "id",
    "firstName",
    "middleName",
    "lastName",
    "gender",
    "dob",
    "profilePic",
    "contactNumber",
    "countryCode",
    "emailVerified",
    "emailId",
    "loyaltyPoints",
    "registrationUnitId",
    "acquisitionSource",
    "contactNumberVerified",
    "availedSignupOffer",
    "smsSubscriber",
    "emailSubscriber",
    "loyaltySubscriber",
    "addresses",
    "createdAt",
    "lastLoginTime",
    "eligibleForSignupOffer"
})
@Document
public class Customer {

    @Id
    @XmlElement(required = true)
    protected String customerId;
    protected int id;
    @XmlElement(required = true)
    protected String firstName;
    @XmlElement(required = true)
    protected String middleName;
    @XmlElement(required = true)
    protected String lastName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected Gender gender;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date dob;
    @XmlElement(required = true)
    protected String profilePic;
    @XmlElement(required = true)
    protected String contactNumber;
    @XmlElement(required = true)
    protected String countryCode;
    protected boolean emailVerified;
    @XmlElement(required = true)
    protected String emailId;
    protected int loyaltyPoints;
    @JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal chaayosCash;
    protected String refCode;
    protected int registrationUnitId;
    @XmlElement(required = true)
    protected String acquisitionSource;
    @XmlElement(required = true)
    protected String acquisitionToken;
    protected boolean contactNumberVerified;
    protected boolean availedSignupOffer;
    protected boolean smsSubscriber;
    protected boolean internal;
    protected boolean emailSubscriber;
    protected boolean loyaltySubscriber;
    protected List<Address> addresses;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date createdAt;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date lastLoginTime;
    @XmlElement(required = true)
    protected String cartId;
    protected Integer tcId;
	protected boolean optOutOfFaceIt = false;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
	protected Date optOutTime;
    protected boolean eligibleForSignupOffer;
    protected Integer acquisitionBrandId;
    protected SubscriptionInfoDetail subscriptionInfoDetail;



    /**
     * Gets the value of the customerId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * Sets the value of the customerId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * Gets the value of the id property.
     *
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the firstName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * Sets the value of the firstName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFirstName(String value) {
        this.firstName = value;
    }

    /**
     * Gets the value of the middleName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getMiddleName() {
        return middleName;
    }

    /**
     * Sets the value of the middleName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setMiddleName(String value) {
        this.middleName = value;
    }

    /**
     * Gets the value of the lastName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * Sets the value of the lastName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastName(String value) {
        this.lastName = value;
    }

    /**
     * Gets the value of the gender property.
     *
     * @return
     *     possible object is
     *     {@link Gender }
     *
     */
    public Gender getGender() {
        return gender;
    }

    /**
     * Sets the value of the gender property.
     *
     * @param value
     *     allowed object is
     *     {@link Gender }
     *
     */
    public void setGender(Gender value) {
        this.gender = value;
    }

    /**
     * Gets the value of the dob property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getDob() {
        return dob;
    }

    /**
     * Sets the value of the dob property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDob(Date value) {
        this.dob = value;
    }

    /**
     * Gets the value of the profilePic property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getProfilePic() {
        return profilePic;
    }

    /**
     * Sets the value of the profilePic property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setProfilePic(String value) {
        this.profilePic = value;
    }

    /**
     * Gets the value of the contactNumber property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getContactNumber() {
        return contactNumber;
    }

    /**
     * Sets the value of the contactNumber property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setContactNumber(String value) {
        this.contactNumber = value;
    }

    /**
     * Gets the value of the countryCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCountryCode() {
        return countryCode;
    }

    /**
     * Sets the value of the countryCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCountryCode(String value) {
        this.countryCode = value;
    }

    /**
     * Gets the value of the emailVerified property.
     *
     */
    public boolean isEmailVerified() {
        return emailVerified;
    }

    /**
     * Sets the value of the emailVerified property.
     *
     */
    public void setEmailVerified(boolean value) {
        this.emailVerified = value;
    }

    /**
     * Gets the value of the emailId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getEmailId() {
        return emailId;
    }

    /**
     * Sets the value of the emailId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setEmailId(String value) {
        this.emailId = value;
    }

    /**
     * Gets the value of the loyaltyPoints property.
     *
     */
    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    /**
     * Sets the value of the loyaltyPoints property.
     *
     */
    public void setLoyaltyPoints(int value) {
        this.loyaltyPoints = value;
    }

    /**
     * Gets the value of the registrationUnitId property.
     *
     */
    public int getRegistrationUnitId() {
        return registrationUnitId;
    }

    /**
     * Sets the value of the registrationUnitId property.
     *
     */
    public void setRegistrationUnitId(int value) {
        this.registrationUnitId = value;
    }

    /**
     * Gets the value of the acquisitionSource property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAcquisitionSource() {
        return acquisitionSource;
    }

    /**
     * Sets the value of the acquisitionSource property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAcquisitionSource(String value) {
        this.acquisitionSource = value;
    }

    /**
     * Gets the value of the contactNumberVerified property.
     *
     */
    public boolean isContactNumberVerified() {
        return contactNumberVerified;
    }

    /**
     * Sets the value of the contactNumberVerified property.
     *
     */
    public void setContactNumberVerified(boolean value) {
        this.contactNumberVerified = value;
    }

    /**
     * Gets the value of the availedSignupOffer property.
     *
     */
    public boolean isAvailedSignupOffer() {
        return availedSignupOffer;
    }

    /**
     * Sets the value of the availedSignupOffer property.
     *
     */
    public void setAvailedSignupOffer(boolean value) {
        this.availedSignupOffer = value;
    }

    /**
     * Gets the value of the smsSubscriber property.
     *
     */
    public boolean isSmsSubscriber() {
        return smsSubscriber;
    }

    /**
     * Sets the value of the smsSubscriber property.
     *
     */
    public void setSmsSubscriber(boolean value) {
        this.smsSubscriber = value;
    }

    /**
     * Gets the value of the emailSubscriber property.
     *
     */
    public boolean isEmailSubscriber() {
        return emailSubscriber;
    }

    /**
     * Sets the value of the emailSubscriber property.
     *
     */
    public void setEmailSubscriber(boolean value) {
        this.emailSubscriber = value;
    }

    /**
     * Gets the value of the loyaltySubscriber property.
     *
     */
    public boolean isLoyaltySubscriber() {
        return loyaltySubscriber;
    }

    /**
     * Sets the value of the loyaltySubscriber property.
     *
     */
    public void setLoyaltySubscriber(boolean value) {
        this.loyaltySubscriber = value;
    }

    /**
     * Gets the value of the addresses property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the addresses property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAddresses().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Address }
     *
     *
     */
    public List<Address> getAddresses() {
        if (addresses == null) {
            addresses = new ArrayList<Address>();
        }
        return this.addresses;
    }

    /**
     * Gets the value of the createdAt property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * Sets the value of the createdAt property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCreatedAt(Date value) {
        this.createdAt = value;
    }

    /**
     * Gets the value of the lastLoginTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    /**
     * Sets the value of the lastLoginTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastLoginTime(Date value) {
        this.lastLoginTime = value;
    }

    public String getCartId() {
        return cartId;
    }

    public void setCartId(String cartId) {
        this.cartId = cartId;
    }

    public boolean isInternal() {
        return internal;
    }

    public void setInternal(boolean internal) {
        this.internal = internal;
    }

    public Integer getTcId() {
        return tcId;
    }

    public void setTcId(Integer tcId) {
        this.tcId = tcId;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }


    public String getAcquisitionToken() {
        return acquisitionToken;
    }

    public void setAcquisitionToken(String acquisitionToken) {
        this.acquisitionToken = acquisitionToken;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String referralCode) {
        this.refCode = referralCode;
    }

    public boolean isOptOutOfFaceIt() {
        return optOutOfFaceIt;
    }

    public void setOptOutOfFaceIt(boolean optOutOfFaceIt) {
        this.optOutOfFaceIt = optOutOfFaceIt;
    }

    public Date getOptOutTime() {
        return optOutTime;
    }

    public void setOptOutTime(Date optOutTime) {
        this.optOutTime = optOutTime;
    }

    public boolean isEligibleForSignupOffer() {
        return eligibleForSignupOffer;
    }

    public void setEligibleForSignupOffer(boolean eligibleForSignupOffer) {
        this.eligibleForSignupOffer = eligibleForSignupOffer;
    }

    public Integer getAcquisitionBrandId() {
        return acquisitionBrandId;
    }

    public void setAcquisitionBrandId(Integer acquisitionBrandId) {
        this.acquisitionBrandId = acquisitionBrandId;
    }

    public SubscriptionInfoDetail getSubscriptionInfoDetail() {
        return subscriptionInfoDetail;
    }

    public void setSubscriptionInfoDetail(SubscriptionInfoDetail subscriptionInfoDetail) {
        this.subscriptionInfoDetail = subscriptionInfoDetail;
    }
}
