package com.stpl.tech.neo.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SubscriptionInfoDetail implements Serializable {

    private static final long serialVersionUID= -4262016717342042863L;

    private Integer customerId;
    private String subscriptionCode;
    private boolean hasSubscription;
    private Date startDate;
    private Date endDate;
    private Integer daysLeft;
    private BigDecimal overAllSaving;
    private boolean eligible;

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getSubscriptionCode() {
        return subscriptionCode;
    }

    public void setSubscriptionCode(String subscriptionCode) {
        this.subscriptionCode = subscriptionCode;
    }

    public boolean isHasSubscription() {
        return hasSubscription;
    }

    public void setHasSubscription(boolean hasSubscription) {
        this.hasSubscription = hasSubscription;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getDaysLeft() {
        return daysLeft;
    }

    public void setDaysLeft(Integer daysLeft) {
        this.daysLeft = daysLeft;
    }

    public BigDecimal getOverAllSaving() {
        return overAllSaving;
    }

    public void setOverAllSaving(BigDecimal overAllSaving) {
        this.overAllSaving = overAllSaving;
    }

    public boolean isEligible() {
        return eligible;
    }

    public void setEligible(boolean eligible) {
        this.eligible = eligible;
    }
}
