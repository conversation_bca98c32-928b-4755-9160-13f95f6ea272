package com.stpl.tech.neo.domain.model.mongo;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExternalSettlement", propOrder = {
        "externalSettlementId",
        "amount",
        "externalTransactionId"
})

public class ExternalSettlement{

    protected int externalSettlementId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal amount;
    @XmlElement(required = true)
    protected String externalTransactionId;

    public ExternalSettlement() {
        // TODO Auto-generated constructor stub
    }

    public ExternalSettlement(int externalSettlementId, BigDecimal amount, String externalTransactionId) {
        super();
        this.externalSettlementId = externalSettlementId;
        this.amount = amount;
        this.externalTransactionId = externalTransactionId;
    }

    /**
     * Gets the value of the settlementId property.
     */
    public int getExternalSettlementId() {
        return externalSettlementId;
    }

    /**
     * Sets the value of the settlementId property.
     */
    public void setExternalSettlementId(int value) {
        this.externalSettlementId = value;
    }

    /**
     * Gets the value of the amount property.
     *
     * @return possible object is {@link BigDecimal }
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     *
     * @param value allowed object is {@link BigDecimal }
     */
    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    /**
     * Gets the value of the externalTransactionId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getExternalTransactionId() {
        return externalTransactionId;
    }

    /**
     * Sets the value of the externalTransactionId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExternalTransactionId(String value) {
        this.externalTransactionId = value;
    }

}

