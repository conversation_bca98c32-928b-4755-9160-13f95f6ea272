package com.stpl.tech.neo.domain.model.mongo;



public class GameLeaderBoard {
    private Integer gameLeaderBoardId;
    private Integer campaignId;
    private Integer customerId;
    private String refCode;
    private Integer gameScore;
    private Integer refScore;
    private Integer totalScore;
    private Integer gamePlayFrequency;

    public GameLeaderBoard() {
    }

    public Integer getGameLeaderBoardId() {
        return gameLeaderBoardId;
    }

    public void setGameLeaderBoardId(Integer gameLeaderBoardId) {
        this.gameLeaderBoardId = gameLeaderBoardId;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public Integer getGameScore() {
        return gameScore;
    }

    public void setGameScore(Integer gameScore) {
        this.gameScore = gameScore;
    }

    public Integer getRefScore() {
        return refScore;
    }

    public void setRefScore(Integer refScore) {
        this.refScore = refScore;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getGamePlayFrequency() {
        return gamePlayFrequency;
    }

    public void setGamePlayFrequency(Integer gamePlayFrequency) {
        this.gamePlayFrequency = gamePlayFrequency;
    }
}
