//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 08:01:27 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.sql.Time;
import java.text.ParseException;

public class Adapter2
    extends XmlAdapter<String, Time>
{


    public Time unmarshal(String value) throws ParseException {
        return (com.stpl.tech.neo.domain.model.DateAdapter.parseTime(value));
    }

    public String marshal(Time value) {
        return (com.stpl.tech.neo.domain.model.DateAdapter.printTime(value));
    }

}
