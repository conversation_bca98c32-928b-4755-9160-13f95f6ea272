//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.14 at 11:12:30 AM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import com.google.gson.JsonElement;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.Date;

/**
 * <p>Java class for UnitLocalityMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitLocalityMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="trackingId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deviceKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sessionKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="time" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RedirectTracking", propOrder = {
    "trackingId",
    "deviceKey",
    "sessionKey",
    "contact",
    "name",
    "email",
    "userAgent",
    "time",
    "type"
})
@Document
public class RedirectTracking {

	@Id
    @XmlElement(required = true)
    protected String trackingId;
    @XmlElement(required = true)
    protected String deviceKey;
    @XmlElement(required = true)
    protected String sessionKey;
    @XmlElement(required = true)
    protected String contact;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String email;
    @XmlElement(required = true)
    protected String userAgent;
    @XmlElement(required = true)
    protected String type;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String locality;
    @XmlElement(required = true)
    protected Date time;
    @XmlElement(required = true)
    protected String source;

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public String getDeviceKey() {
        return deviceKey;
    }

    public void setDeviceKey(String deviceKey) {
        this.deviceKey = deviceKey;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        RedirectTracking that = (RedirectTracking) o;

        return new EqualsBuilder()
                .append(trackingId, that.trackingId)
                .append(deviceKey, that.deviceKey)
                .append(sessionKey, that.sessionKey)
                .append(contact, that.contact)
                .append(name, that.name)
                .append(email, that.email)
                .append(userAgent, that.userAgent)
                .append(type, that.type)
                .append(time, that.time)
                .append(city, that.city)
                .append(locality, that.locality)
                .append(source, that.source)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(trackingId)
                .append(deviceKey)
                .append(sessionKey)
                .append(contact)
                .append(name)
                .append(email)
                .append(userAgent)
                .append(type)
                .append(time)
                .append(city)
                .append(locality)
                .append(source)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "RedirectTracking{" +
                "trackingId='" + trackingId + '\'' +
                ", deviceKey='" + deviceKey + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", contact='" + contact + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", type='" + type + '\'' +
                ", time=" + time +
                ", city=" + city +
                ", locality=" + locality +
                ", source=" + source +
                '}';
    }
}
