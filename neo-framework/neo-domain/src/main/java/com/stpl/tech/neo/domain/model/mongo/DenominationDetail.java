//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DenominationDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DenominationDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="denominationId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="denominationCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="denominationText" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="displayOrder" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="denominationValue" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="bundleSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="paymentMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DenominationDetail", propOrder = {
    "denominationId",
    "denominationCode",
    "denominationText",
    "status",
    "displayOrder",
    "denominationValue",
    "bundleSize",
    "paymentMode"
})
public class DenominationDetail {

    protected int denominationId;
    @XmlElement(required = true)
    protected String denominationCode;
    @XmlElement(required = true)
    protected String denominationText;
    @XmlElement(required = true)
    protected String status;
    protected int displayOrder;
    protected int denominationValue;
    protected int bundleSize;
    protected int paymentMode;

    /**
     * Gets the value of the denominationId property.
     * 
     */
    public int getDenominationId() {
        return denominationId;
    }

    /**
     * Sets the value of the denominationId property.
     * 
     */
    public void setDenominationId(int value) {
        this.denominationId = value;
    }

    /**
     * Gets the value of the denominationCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDenominationCode() {
        return denominationCode;
    }

    /**
     * Sets the value of the denominationCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDenominationCode(String value) {
        this.denominationCode = value;
    }

    /**
     * Gets the value of the denominationText property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDenominationText() {
        return denominationText;
    }

    /**
     * Sets the value of the denominationText property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDenominationText(String value) {
        this.denominationText = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the displayOrder property.
     * 
     */
    public int getDisplayOrder() {
        return displayOrder;
    }

    /**
     * Sets the value of the displayOrder property.
     * 
     */
    public void setDisplayOrder(int value) {
        this.displayOrder = value;
    }

    /**
     * Gets the value of the denominationValue property.
     * 
     */
    public int getDenominationValue() {
        return denominationValue;
    }

    /**
     * Sets the value of the denominationValue property.
     * 
     */
    public void setDenominationValue(int value) {
        this.denominationValue = value;
    }

    /**
     * Gets the value of the bundleSize property.
     * 
     */
    public int getBundleSize() {
        return bundleSize;
    }

    /**
     * Sets the value of the bundleSize property.
     * 
     */
    public void setBundleSize(int value) {
        this.bundleSize = value;
    }

    /**
     * Gets the value of the paymentMode property.
     * 
     */
    public int getPaymentMode() {
        return paymentMode;
    }

    /**
     * Sets the value of the paymentMode property.
     * 
     */
    public void setPaymentMode(int value) {
        this.paymentMode = value;
    }

}
