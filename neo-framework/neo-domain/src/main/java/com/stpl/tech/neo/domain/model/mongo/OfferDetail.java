package com.stpl.tech.neo.domain.model.mongo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OfferDetail", propOrder = {
        "id",
        "productId",
        "productName",
        "originalPrice",
        "offerPrice",
        "status",
        "description"
})
@Document
public class OfferDetail {

    @Id
    @XmlElement(required = true)
    protected String id;

    @XmlElement(required = true)
    protected Integer productId;

    @XmlElement(required = true)
    protected String productName;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal originalPrice;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal offerPrice;

    @XmlElement(required = true)
    protected boolean status;

    @XmlElement(required = false)
    protected String description;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getOfferPrice() {
        return offerPrice;
    }

    public void setOfferPrice(BigDecimal offerPrice) {
        this.offerPrice = offerPrice;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "OfferDetail{" +
                "id='" + id + '\'' +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", originalPrice=" + originalPrice +
                ", offerPrice=" + offerPrice +
                ", status=" + status +
                ", description='" + description + '\'' +
                '}';
    }
}
