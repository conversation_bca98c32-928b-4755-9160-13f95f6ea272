//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.05 at 04:03:31 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WebTagCategory.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="WebTagCategory"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="MENU"/&gt;
 *     &lt;enumeration value="PAYMENT_MODE"/&gt;
 *     &lt;enumeration value="PRODUCT"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "WebTagCategory")
@XmlEnum
public enum WebTagCategory {

    MENU("MENU"),
    PAYMENT_MODE("PAYMENT_MODE"),
    PRODUCT("PRODUCT"),
    INDICATOR("INDICATOR");

	private final String value;

    WebTagCategory(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static WebTagCategory fromValue(String v) {
        for (WebTagCategory c: WebTagCategory.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
