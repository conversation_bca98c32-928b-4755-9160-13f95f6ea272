//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.neo.domain.model.mongo package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.neo.domain.model.mongo
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link DeviceDetail }
     * 
     */
    public DeviceDetail createDeviceDetail() {
        return new DeviceDetail();
    }

    /**
     * Create an instance of {@link SessionDetail }
     * 
     */
    public SessionDetail createSessionDetail() {
        return new SessionDetail();
    }

    /**
     * Create an instance of {@link CartDetail }
     * 
     */
    public CartDetail createCartDetail() {
        return new CartDetail();
    }

    /**
     * Create an instance of {@link Order }
     * 
     */
    public Order createOrder() {
        return new Order();
    }

    /**
     * Create an instance of {@link EnquiryItem }
     * 
     */
    public EnquiryItem createEnquiryItem() {
        return new EnquiryItem();
    }

    /**
     * Create an instance of {@link OrderItem }
     * 
     */
    public OrderItem createOrderItem() {
        return new OrderItem();
    }

    /**
     * Create an instance of {@link TransactionDetail }
     * 
     */
    public TransactionDetail createTransactionDetail() {
        return new TransactionDetail();
    }

    /**
     * Create an instance of {@link Settlement }
     * 
     */
    public Settlement createSettlement() {
        return new Settlement();
    }

    /**
     * Create an instance of {@link ActionDetail }
     * 
     */
    public ActionDetail createActionDetail() {
        return new ActionDetail();
    }

    /**
     * Create an instance of {@link OrderMetadata }
     * 
     */
    public OrderMetadata createOrderMetadata() {
        return new OrderMetadata();
    }

    /**
     * Create an instance of {@link IdCodeName }
     * 
     */
    public IdCodeName createIdCodeName() {
        return new IdCodeName();
    }

    /**
     * Create an instance of {@link DiscountDetail }
     * 
     */
    public DiscountDetail createDiscountDetail() {
        return new DiscountDetail();
    }

    /**
     * Create an instance of {@link ComplimentaryDetail }
     * 
     */
    public ComplimentaryDetail createComplimentaryDetail() {
        return new ComplimentaryDetail();
    }

    /**
     * Create an instance of {@link AddonData }
     * 
     */
    public AddonData createAddonData() {
        return new AddonData();
    }

    /**
     * Create an instance of {@link PercentageDetail }
     * 
     */
    public PercentageDetail createPercentageDetail() {
        return new PercentageDetail();
    }

    /**
     * Create an instance of {@link PaymentMode }
     * 
     */
    public PaymentMode createPaymentMode() {
        return new PaymentMode();
    }

    /**
     * Create an instance of {@link OrderPaymentDenomination }
     * 
     */
    public OrderPaymentDenomination createOrderPaymentDenomination() {
        return new OrderPaymentDenomination();
    }

    /**
     * Create an instance of {@link DenominationDetail }
     * 
     */
    public DenominationDetail createDenominationDetail() {
        return new DenominationDetail();
    }

    /**
     * Create an instance of {@link Customer }
     * 
     */
    public Customer createCustomer() {
        return new Customer();
    }

    /**
     * Create an instance of {@link Address }
     * 
     */
    public Address createAddress() {
        return new Address();
    }

    /**
     * Create an instance of {@link Preferences }
     * 
     */
    public Preferences createPreferences() {
        return new Preferences();
    }

}
