package com.stpl.tech.neo.domain.model.mongo;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SMSRequest", propOrder = { "contact", "message", "campaign", "smsType", "application", "userAgent",
		"token" })
@Document
@XmlRootElement(name = "SMSRequest")
public class SMSRequest {

	@Id
	@XmlElement(required = true)
	protected String id;

	@XmlElement(required = true)
	private String contact;
	@XmlElement(required = true)
	private String campaign;
	@XmlElement(required = true)
	private String message;
	@XmlElement(required = true)
	private String smsType;
	@XmlElement(required = true)
	private String application;
	@XmlElement(required = true)
	private String token;
	@XmlElement(required = true)
	private boolean sendNotification;
	@XmlElement(required = true)
	private String userAgent;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	private Date creationTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getCampaign() {
		return campaign;
	}

	public void setCampaign(String campaign) {
		this.campaign = campaign;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getSmsType() {
		return smsType;
	}

	public void setSmsType(String smsType) {
		this.smsType = smsType;
	}

	public String getApplication() {
		return application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public boolean isSendNotification() {
		return sendNotification;
	}

	public void setSendNotification(boolean sendNotification) {
		this.sendNotification = sendNotification;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Override
	public String toString() {
		return "SMSRequest [contact=" + contact + ", campaign=" + campaign + ", message=" + message + ", smsType="
				+ smsType + ", application=" + application + ", token=" + token + ", sendNotification="
				+ sendNotification + ", userAgent=" + userAgent + ", creationTime=" + creationTime + "]";
	}

}
