package com.stpl.tech.neo.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class RazorPayPaymentResponse {

	@JsonProperty("razorpay_payment_id")
	private String transactionId;
	@JsonProperty("externalOrderId")
	private String orderId;
	@JsonProperty("razorpay_signature")
	private String signature;
	@JsonProperty("razorpay_order_id")
	private String razorPayOrderId;
	@JsonProperty("status")
	private String status;

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getRazorPayOrderId() {
		return razorPayOrderId;
	}

	public void setRazorPayOrderId(String razorPayOrderId) {
		this.razorPayOrderId = razorPayOrderId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public String getOrderId() {
		return orderId;
	}

	public String getStatus() {
		return status;
	}

}
