//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.14 at 11:12:30 AM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <p>Java class for UnitLocalityMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitLocalityMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="mappingId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locality" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="default" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="primaryUnitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="primaryDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skipPrimaryDeliveryCharge" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="skipPrimaryPackagingCharge" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="secondaryUnitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="secondaryDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skipSecondaryDeliveryCharge" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="skipSecondaryPackagingCharge" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="tertiaryUnitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tertiaryUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="tertiaryDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skipTertiaryDeliveryCharge" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="skipTertiaryPackagingCharge" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="primaryTakeAwayUnitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryTakeAwayUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="primaryTakeAwayDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="secondaryTakeAwayUnitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryTakeAwayUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="secondaryTakeAwayDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="tertiaryTakeAwayUnitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tertiaryTakeAwayUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="tertiaryTakeAwayDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitLocalityMapping", propOrder = {
    "mappingId",
    "locality",
    "city",
    "state",
    "country",
    "_default",
    "primaryUnitName",
    "primaryUnitId",
    "primaryDeliveryTime",
    "skipPrimaryDeliveryCharge",
    "skipPrimaryPackagingCharge",
    "secondaryUnitName",
    "secondaryUnitId",
    "secondaryDeliveryTime",
    "skipSecondaryDeliveryCharge",
    "skipSecondaryPackagingCharge",
    "tertiaryUnitName",
    "tertiaryUnitId",
    "tertiaryDeliveryTime",
    "skipTertiaryDeliveryCharge",
    "skipTertiaryPackagingCharge",
    "primaryTakeAwayUnitName",
    "primaryTakeAwayUnitId",
    "primaryTakeAwayDeliveryTime",
    "secondaryTakeAwayUnitName",
    "secondaryTakeAwayUnitId",
    "secondaryTakeAwayDeliveryTime",
    "tertiaryTakeAwayUnitName",
    "tertiaryTakeAwayUnitId",
    "tertiaryTakeAwayDeliveryTime"
})
@Document
public class UnitLocalityMapping {

	@Id
    @XmlElement(required = true)
    protected String mappingId;
    @XmlElement(required = true)
    protected String locality;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String state;
    @XmlElement(required = true)
    protected String country;
    @XmlElement(name = "default")
    protected boolean _default;
    @XmlElement(required = true)
    protected String primaryUnitName;
    protected int primaryUnitId;
    protected int primaryDeliveryTime;
    protected boolean skipPrimaryDeliveryCharge;
    protected boolean skipPrimaryPackagingCharge;
    @XmlElement(required = true)
    protected String secondaryUnitName;
    protected int secondaryUnitId;
    protected int secondaryDeliveryTime;
    protected boolean skipSecondaryDeliveryCharge;
    protected boolean skipSecondaryPackagingCharge;
    @XmlElement(required = true)
    protected String tertiaryUnitName;
    protected int tertiaryUnitId;
    protected int tertiaryDeliveryTime;
    protected boolean skipTertiaryDeliveryCharge;
    protected boolean skipTertiaryPackagingCharge;
    @XmlElement(required = true)
    protected String primaryTakeAwayUnitName;
    protected int primaryTakeAwayUnitId;
    protected int primaryTakeAwayDeliveryTime;
    @XmlElement(required = true)
    protected String secondaryTakeAwayUnitName;
    protected int secondaryTakeAwayUnitId;
    protected int secondaryTakeAwayDeliveryTime;
    @XmlElement(required = true)
    protected String tertiaryTakeAwayUnitName;
    protected int tertiaryTakeAwayUnitId;
    protected int tertiaryTakeAwayDeliveryTime;

    /**
     * Gets the value of the mappingId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMappingId() {
        return mappingId;
    }

    /**
     * Sets the value of the mappingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMappingId(String value) {
        this.mappingId = value;
    }

    /**
     * Gets the value of the locality property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocality() {
        return locality;
    }

    /**
     * Sets the value of the locality property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocality(String value) {
        this.locality = value;
    }

    /**
     * Gets the value of the city property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the state property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setState(String value) {
        this.state = value;
    }

    /**
     * Gets the value of the country property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountry() {
        return country;
    }

    /**
     * Sets the value of the country property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCountry(String value) {
        this.country = value;
    }

    /**
     * Gets the value of the default property.
     * 
     */
    public boolean isDefault() {
        return _default;
    }

    /**
     * Sets the value of the default property.
     * 
     */
    public void setDefault(boolean value) {
        this._default = value;
    }

    /**
     * Gets the value of the primaryUnitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrimaryUnitName() {
        return primaryUnitName;
    }

    /**
     * Sets the value of the primaryUnitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrimaryUnitName(String value) {
        this.primaryUnitName = value;
    }

    /**
     * Gets the value of the primaryUnitId property.
     * 
     */
    public int getPrimaryUnitId() {
        return primaryUnitId;
    }

    /**
     * Sets the value of the primaryUnitId property.
     * 
     */
    public void setPrimaryUnitId(int value) {
        this.primaryUnitId = value;
    }

    /**
     * Gets the value of the primaryDeliveryTime property.
     * 
     */
    public int getPrimaryDeliveryTime() {
        return primaryDeliveryTime;
    }

    /**
     * Sets the value of the primaryDeliveryTime property.
     * 
     */
    public void setPrimaryDeliveryTime(int value) {
        this.primaryDeliveryTime = value;
    }

    /**
     * Gets the value of the skipPrimaryDeliveryCharge property.
     * 
     */
    public boolean isSkipPrimaryDeliveryCharge() {
        return skipPrimaryDeliveryCharge;
    }

    /**
     * Sets the value of the skipPrimaryDeliveryCharge property.
     * 
     */
    public void setSkipPrimaryDeliveryCharge(boolean value) {
        this.skipPrimaryDeliveryCharge = value;
    }

    /**
     * Gets the value of the skipPrimaryPackagingCharge property.
     * 
     */
    public boolean isSkipPrimaryPackagingCharge() {
        return skipPrimaryPackagingCharge;
    }

    /**
     * Sets the value of the skipPrimaryPackagingCharge property.
     * 
     */
    public void setSkipPrimaryPackagingCharge(boolean value) {
        this.skipPrimaryPackagingCharge = value;
    }

    /**
     * Gets the value of the secondaryUnitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecondaryUnitName() {
        return secondaryUnitName;
    }

    /**
     * Sets the value of the secondaryUnitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecondaryUnitName(String value) {
        this.secondaryUnitName = value;
    }

    /**
     * Gets the value of the secondaryUnitId property.
     * 
     */
    public int getSecondaryUnitId() {
        return secondaryUnitId;
    }

    /**
     * Sets the value of the secondaryUnitId property.
     * 
     */
    public void setSecondaryUnitId(int value) {
        this.secondaryUnitId = value;
    }

    /**
     * Gets the value of the secondaryDeliveryTime property.
     * 
     */
    public int getSecondaryDeliveryTime() {
        return secondaryDeliveryTime;
    }

    /**
     * Sets the value of the secondaryDeliveryTime property.
     * 
     */
    public void setSecondaryDeliveryTime(int value) {
        this.secondaryDeliveryTime = value;
    }

    /**
     * Gets the value of the skipSecondaryDeliveryCharge property.
     * 
     */
    public boolean isSkipSecondaryDeliveryCharge() {
        return skipSecondaryDeliveryCharge;
    }

    /**
     * Sets the value of the skipSecondaryDeliveryCharge property.
     * 
     */
    public void setSkipSecondaryDeliveryCharge(boolean value) {
        this.skipSecondaryDeliveryCharge = value;
    }

    /**
     * Gets the value of the skipSecondaryPackagingCharge property.
     * 
     */
    public boolean isSkipSecondaryPackagingCharge() {
        return skipSecondaryPackagingCharge;
    }

    /**
     * Sets the value of the skipSecondaryPackagingCharge property.
     * 
     */
    public void setSkipSecondaryPackagingCharge(boolean value) {
        this.skipSecondaryPackagingCharge = value;
    }

    /**
     * Gets the value of the tertiaryUnitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTertiaryUnitName() {
        return tertiaryUnitName;
    }

    /**
     * Sets the value of the tertiaryUnitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTertiaryUnitName(String value) {
        this.tertiaryUnitName = value;
    }

    /**
     * Gets the value of the tertiaryUnitId property.
     * 
     */
    public int getTertiaryUnitId() {
        return tertiaryUnitId;
    }

    /**
     * Sets the value of the tertiaryUnitId property.
     * 
     */
    public void setTertiaryUnitId(int value) {
        this.tertiaryUnitId = value;
    }

    /**
     * Gets the value of the tertiaryDeliveryTime property.
     * 
     */
    public int getTertiaryDeliveryTime() {
        return tertiaryDeliveryTime;
    }

    /**
     * Sets the value of the tertiaryDeliveryTime property.
     * 
     */
    public void setTertiaryDeliveryTime(int value) {
        this.tertiaryDeliveryTime = value;
    }

    /**
     * Gets the value of the skipTertiaryDeliveryCharge property.
     * 
     */
    public boolean isSkipTertiaryDeliveryCharge() {
        return skipTertiaryDeliveryCharge;
    }

    /**
     * Sets the value of the skipTertiaryDeliveryCharge property.
     * 
     */
    public void setSkipTertiaryDeliveryCharge(boolean value) {
        this.skipTertiaryDeliveryCharge = value;
    }

    /**
     * Gets the value of the skipTertiaryPackagingCharge property.
     * 
     */
    public boolean isSkipTertiaryPackagingCharge() {
        return skipTertiaryPackagingCharge;
    }

    /**
     * Sets the value of the skipTertiaryPackagingCharge property.
     * 
     */
    public void setSkipTertiaryPackagingCharge(boolean value) {
        this.skipTertiaryPackagingCharge = value;
    }

    /**
     * Gets the value of the primaryTakeAwayUnitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrimaryTakeAwayUnitName() {
        return primaryTakeAwayUnitName;
    }

    /**
     * Sets the value of the primaryTakeAwayUnitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrimaryTakeAwayUnitName(String value) {
        this.primaryTakeAwayUnitName = value;
    }

    /**
     * Gets the value of the primaryTakeAwayUnitId property.
     * 
     */
    public int getPrimaryTakeAwayUnitId() {
        return primaryTakeAwayUnitId;
    }

    /**
     * Sets the value of the primaryTakeAwayUnitId property.
     * 
     */
    public void setPrimaryTakeAwayUnitId(int value) {
        this.primaryTakeAwayUnitId = value;
    }

    /**
     * Gets the value of the primaryTakeAwayDeliveryTime property.
     * 
     */
    public int getPrimaryTakeAwayDeliveryTime() {
        return primaryTakeAwayDeliveryTime;
    }

    /**
     * Sets the value of the primaryTakeAwayDeliveryTime property.
     * 
     */
    public void setPrimaryTakeAwayDeliveryTime(int value) {
        this.primaryTakeAwayDeliveryTime = value;
    }

    /**
     * Gets the value of the secondaryTakeAwayUnitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecondaryTakeAwayUnitName() {
        return secondaryTakeAwayUnitName;
    }

    /**
     * Sets the value of the secondaryTakeAwayUnitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecondaryTakeAwayUnitName(String value) {
        this.secondaryTakeAwayUnitName = value;
    }

    /**
     * Gets the value of the secondaryTakeAwayUnitId property.
     * 
     */
    public int getSecondaryTakeAwayUnitId() {
        return secondaryTakeAwayUnitId;
    }

    /**
     * Sets the value of the secondaryTakeAwayUnitId property.
     * 
     */
    public void setSecondaryTakeAwayUnitId(int value) {
        this.secondaryTakeAwayUnitId = value;
    }

    /**
     * Gets the value of the secondaryTakeAwayDeliveryTime property.
     * 
     */
    public int getSecondaryTakeAwayDeliveryTime() {
        return secondaryTakeAwayDeliveryTime;
    }

    /**
     * Sets the value of the secondaryTakeAwayDeliveryTime property.
     * 
     */
    public void setSecondaryTakeAwayDeliveryTime(int value) {
        this.secondaryTakeAwayDeliveryTime = value;
    }

    /**
     * Gets the value of the tertiaryTakeAwayUnitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTertiaryTakeAwayUnitName() {
        return tertiaryTakeAwayUnitName;
    }

    /**
     * Sets the value of the tertiaryTakeAwayUnitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTertiaryTakeAwayUnitName(String value) {
        this.tertiaryTakeAwayUnitName = value;
    }

    /**
     * Gets the value of the tertiaryTakeAwayUnitId property.
     * 
     */
    public int getTertiaryTakeAwayUnitId() {
        return tertiaryTakeAwayUnitId;
    }

    /**
     * Sets the value of the tertiaryTakeAwayUnitId property.
     * 
     */
    public void setTertiaryTakeAwayUnitId(int value) {
        this.tertiaryTakeAwayUnitId = value;
    }

    /**
     * Gets the value of the tertiaryTakeAwayDeliveryTime property.
     * 
     */
    public int getTertiaryTakeAwayDeliveryTime() {
        return tertiaryTakeAwayDeliveryTime;
    }

    /**
     * Sets the value of the tertiaryTakeAwayDeliveryTime property.
     * 
     */
    public void setTertiaryTakeAwayDeliveryTime(int value) {
        this.tertiaryTakeAwayDeliveryTime = value;
    }

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((city == null) ? 0 : city.hashCode());
		result = prime * result + ((locality == null) ? 0 : locality.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitLocalityMapping other = (UnitLocalityMapping) obj;
		if (city == null) {
			if (other.city != null)
				return false;
		} else if (!city.equalsIgnoreCase(other.city))
			return false;
		if (locality == null) {
			if (other.locality != null)
				return false;
		} else if (!locality.equalsIgnoreCase(other.locality))
			return false;
		return true;
	}

}
