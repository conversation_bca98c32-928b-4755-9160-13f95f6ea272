package com.stpl.tech.neo.domain.model.mongo;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
public class EzetapCreateRequest {

	@Id
	private String razorPayCreateRequestId;

	@JsonProperty("receipt")
	private String orderId;
//	@JsonProperty("currency")
//	private String currency;
	@JsonProperty("amount")
	private int transactionAmount;
//	@JsonProperty("payment_capture")
//	private boolean paymentCapture;
	private String status;
	@JsonProperty("id")
	private String paymentTransactionId;

	public EzetapCreateRequest() {

	}

	public String getRazorPayCreateRequestId() {
		return razorPayCreateRequestId;
	}

	public void setRazorPayCreateRequestId(String razorPayCreateRequestId) {
		this.razorPayCreateRequestId = razorPayCreateRequestId;
	}

	public String getPaymentTransactionId() {
		return paymentTransactionId;
	}

	public void setPaymentTransactionId(String paymentTransactionId) {
		this.paymentTransactionId = paymentTransactionId;
	}

	public String getStatus() {
		return status;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	/*public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}*/

	public int getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(int transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	/*public boolean getPaymentCapture() {
		return paymentCapture;
	}

	public void setPaymentCapture(boolean paymentCapture) {
		this.paymentCapture = paymentCapture;
	}*/

}
