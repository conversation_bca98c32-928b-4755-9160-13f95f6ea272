package com.stpl.tech.neo.domain.model;

import com.stpl.tech.neo.domain.model.mongo.CustomerInfo;

public class CustomerLoginRequest {
	private CustomerInfo customer;
	private Integer unitId;
	private Integer terminal;

	public CustomerLoginRequest(CustomerInfo customer, Integer unitId, Integer terminal) {
		super();
		this.customer = customer;
		this.unitId = unitId;
		this.terminal = terminal;
	}

	public CustomerLoginRequest() {
		super();
	}

	public CustomerInfo getCustomer() {
		return customer;
	}

	public void setCustomer(CustomerInfo customer) {
		this.customer = customer;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Integer getTerminal() {
		return terminal;
	}

	public void setTerminal(Integer terminal) {
		this.terminal = terminal;
	}

}
