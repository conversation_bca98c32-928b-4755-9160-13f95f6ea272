package com.stpl.tech.neo.domain.model.mongo;

import java.io.Serializable;

public class GameLeaderBoardDTO implements Serializable {
    private static final long serialVersionUID = -6462079647212856496L;

    private String refCode;
    private Integer gameScore;
    private Integer refScore;
    private Integer totalScore;
    private String name;

    public GameLeaderBoardDTO() {
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public Integer getGameScore() {
        return gameScore;
    }

    public void setGameScore(Integer gameScore) {
        this.gameScore = gameScore;
    }

    public Integer getRefScore() {
        return refScore;
    }

    public void setRefScore(Integer refScore) {
        this.refScore = refScore;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
