//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for Settlement complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="Settlement"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="settlementId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="mode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="modeDetail" type="{http://www.w3schools.com}PaymentMode"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="extraVouchers" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="denominations" type="{http://www.w3schools.com}OrderPaymentDenomination" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Settlement", propOrder = {
        "settlementId",
        "mode",
        "modeDetail",
        "amount",
        "extraVouchers",
        "denominations",
        "externalSettlements"
})
public class Settlement {

    protected int settlementId;
    protected int mode;
    @XmlElement(required = true, nillable = true)
    protected PaymentMode modeDetail;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal amount;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal extraVouchers;
    protected List<OrderPaymentDenomination> denominations;
    protected List<ExternalSettlement> externalSettlements;

    /**
     * Gets the value of the settlementId property.
     */
    public int getSettlementId() {
        return settlementId;
    }

    /**
     * Sets the value of the settlementId property.
     */
    public void setSettlementId(int value) {
        this.settlementId = value;
    }

    /**
     * Gets the value of the mode property.
     */
    public int getMode() {
        return mode;
    }

    /**
     * Sets the value of the mode property.
     */
    public void setMode(int value) {
        this.mode = value;
    }

    /**
     * Gets the value of the modeDetail property.
     *
     * @return possible object is
     * {@link PaymentMode }
     */
    public PaymentMode getModeDetail() {
        return modeDetail;
    }

    /**
     * Sets the value of the modeDetail property.
     *
     * @param value allowed object is
     *              {@link PaymentMode }
     */
    public void setModeDetail(PaymentMode value) {
        this.modeDetail = value;
    }

    /**
     * Gets the value of the amount property.
     *
     * @return possible object is
     * {@link String }
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    /**
     * Gets the value of the extraVouchers property.
     *
     * @return possible object is
     * {@link String }
     */
    public BigDecimal getExtraVouchers() {
        return extraVouchers;
    }

    /**
     * Sets the value of the extraVouchers property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExtraVouchers(BigDecimal value) {
        this.extraVouchers = value;
    }

    /**
     * Gets the value of the denominations property.
     * <p>
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the denominations property.
     * <p>
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDenominations().add(newItem);
     * </pre>
     * <p>
     * <p>
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OrderPaymentDenomination }
     */
    public List<OrderPaymentDenomination> getDenominations() {
        if (denominations == null) {
            denominations = new ArrayList<OrderPaymentDenomination>();
        }
        return this.denominations;
    }

    public List<ExternalSettlement> getExternalSettlements() {
        if (externalSettlements == null) {
            externalSettlements = new ArrayList<ExternalSettlement>();
        }
        return this.externalSettlements;
    }

    public void setExternalSettlements(List<ExternalSettlement> externalSettlements) {
        this.externalSettlements = externalSettlements;
    }

}
