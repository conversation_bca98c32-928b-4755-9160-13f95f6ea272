package com.stpl.tech.neo.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class EzetapPaymentResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8606066843308061274L;

	@JsonProperty("orderId")
	private String orderId;
	@JsonProperty("txnId")
	private String transactionId;
	@JsonProperty("reverseReferenceNumber")
	private String reverseReferenceNumber;
	@JsonProperty("authCode")
	private String authCode;
	@JsonProperty("receiptUrl")
	private String receiptUrl;
	@JsonProperty("batchNo")
	private String batchNo;
	@JsonProperty("invoice")
	private String invoice;
	@JsonProperty("serviceFeeAmount")
	private String serviceFeeAmount;
	@JsonProperty("card")
	private String card;
	@JsonProperty("status")
	private String status;

	public String getOrderId() {
		return orderId;
	}

	public String getStatus() {
		return status;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReason() {
		return "Failed to process in external system";
	}

	public String getReverseReferenceNumber() {
		return reverseReferenceNumber;
	}

	public void setReverseReferenceNumber(String reverseReferenceNumber) {
		this.reverseReferenceNumber = reverseReferenceNumber;
	}

	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}

	public String getReceiptUrl() {
		return receiptUrl;
	}

	public void setReceiptUrl(String receiptUrl) {
		this.receiptUrl = receiptUrl;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getInvoice() {
		return invoice;
	}

	public void setInvoice(String invoice) {
		this.invoice = invoice;
	}

	public String getServiceFeeAmount() {
		return serviceFeeAmount;
	}

	public void setServiceFeeAmount(String serviceFeeAmount) {
		this.serviceFeeAmount = serviceFeeAmount;
	}

	public String getCard() {
		return card;
	}

	public void setCard(String card) {
		this.card = card;
	}
}
