//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//

package com.stpl.tech.neo.domain.model.mongo;

import org.springframework.data.mongodb.core.index.Indexed;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for Order complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Order"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="generateOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="campaignId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="webCustomerId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="employeeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="pointsRedeemed" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="source" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sourceId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="hasParcel" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="orderStatus" type="{http://www.w3schools.com}OrderStatus"/&gt;
 *         &lt;element name="orders" type="{http://www.w3schools.com}OrderItem" maxOccurs="unbounded"/&gt;
 *         &lt;element name="enquiryItems" type="{http://www.w3schools.com}EnquiryItem" maxOccurs="unbounded"/&gt;
 *         &lt;element name="transactionDetail" type="{http://www.w3schools.com}TransactionDetail"/&gt;
 *         &lt;element name="settlementType" type="{http://www.w3schools.com}SettlementType"/&gt;
 *         &lt;element name="settlements" type="{http://www.w3schools.com}Settlement" maxOccurs="unbounded"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="terminalId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="tableNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="billStartTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="billCreationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="billCreationSeconds" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="billingServerTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="channelPartner" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="deliveryPartner" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="offerCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cancellationDetails" type="{http://www.w3schools.com}ActionDetail"/&gt;
 *         &lt;element name="orderRemark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deliveryAddress" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="customerName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="containsSignupOffer" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tempCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="metadataList" type="{http://www.w3schools.com}OrderMetadata" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Order", propOrder = { "orderId", "generateOrderId", "unitOrderId", "campaignId", "customerId", "webCustomerId",
		"employeeId", "pointsRedeemed", "source", "sourceId", "hasParcel", "status", "orders", "enquiryItems",
		"transactionDetail", "settlementType", "settlements", "unitId", "unitName", "terminalId",
		"billStartTime", "billCreationTime", "billCreationSeconds", "billingServerTime", "channelPartner",
		"deliveryPartner", "offerCode", "cancellationDetails", "orderRemark", "deliveryAddress", "customerName",
		"containsSignupOffer", "tempCode", "metadataList" })
public class Order {

	@Indexed
	@XmlElement(required = true)
	protected String orderId;
	@Indexed
	protected String generateOrderId;
	@XmlElement(required = true, nillable = true)
	@Indexed
	protected String externalOrderId;
	@XmlElement(required = true)
	protected String unitOrderId;
	@XmlElement(required = true, nillable = true)
	protected String campaignId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer customerId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected String webCustomerId;
	protected int employeeId;
	protected int pointsRedeemed;
	protected int cashRedeemed;
	@XmlElement(required = true)
	protected String source;
	protected String sourceId;
	protected boolean hasParcel;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected OrderStatus status;
	@XmlElement(required = true)
	protected List<OrderItem> orders;
	@XmlElement(required = true)
	protected List<EnquiryItem> enquiryItems;
	@XmlElement(required = true)
	protected TransactionDetail transactionDetail;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected SettlementType settlementType;
	@XmlElement(required = true)
	protected List<Settlement> settlements;
	protected int unitId;
	@XmlElement(required = true)
	protected String unitName;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer terminalId;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	protected Date billStartTime;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	protected Date billCreationTime;
	protected int billCreationSeconds;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	protected Date billingServerTime;
	protected int channelPartner;
	protected int deliveryPartner;
	@XmlElement(required = true)
	protected String offerCode;
	@XmlElement(required = true)
	protected ActionDetail cancellationDetails;
	@XmlElement(required = true)
	protected String orderRemark;
	protected Integer deliveryAddress;
	protected Address address;
	@XmlElement(required = true)
	protected String customerName;
	@XmlElement(required = true)
	protected String containsSignupOffer;
	@XmlElement(required = true)
	protected String tempCode;
	protected List<OrderMetadata> metadataList;
	@XmlElement(required = false, type = Integer.class, nillable = true)
	protected Integer tokenNumber;
	protected Integer brandId;

	/**
	 * Gets the value of the orderId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOrderId() {
		return orderId;
	}

	/**
	 * Sets the value of the orderId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOrderId(String value) {
		this.orderId = value;
	}

	/**
	 * Gets the value of the generateOrderId property.
	 * 
	 */
	public String getGenerateOrderId() {
		return generateOrderId;
	}

	/**
	 * Sets the value of the generateOrderId property.
	 * 
	 */
	public void setGenerateOrderId(String value) {
		this.generateOrderId = value;
	}

	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String externalOrderId) {
		this.externalOrderId = externalOrderId;
	}

	/**
	 * Gets the value of the unitOrderId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUnitOrderId() {
		return unitOrderId;
	}

	/**
	 * Sets the value of the unitOrderId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitOrderId(String value) {
		this.unitOrderId = value;
	}

	/**
	 * Gets the value of the sourceId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCampaignId() {
		return campaignId;
	}

	/**
	 * Sets the value of the sourceId property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setCampaignId(String value) {
		this.campaignId = value;
	}

	/**
	 * Gets the value of the employeeId property.
	 * 
	 */
	public int getEmployeeId() {
		return employeeId;
	}

	/**
	 * Sets the value of the employeeId property.
	 * 
	 */
	public void setEmployeeId(int value) {
		this.employeeId = value;
	}

	/**
	 * Gets the value of the pointsRedeemed property.
	 *
	 */
	public int getPointsRedeemed() {
		return pointsRedeemed;
	}

	/**
	 * Sets the value of the pointsRedeemed property.
	 *
	 */
	public void setPointsRedeemed(int value) {
		this.pointsRedeemed = value;
	}

	/**
	 * Sets the value of the cashRedeemed property.
	 *
	 */
	public void setCashRedeemed(int value) {
		this.cashRedeemed = value;
	}


	/**
	 * Gets the value of the cashRedeemed property.
	 *
	 */
	public int getCashRedeemed() {
		return cashRedeemed;
	}

	/**
	 * Gets the value of the source property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSource() {
		return source;
	}

	/**
	 * Sets the value of the source property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSource(String value) {
		this.source = value;
	}

	/**
	 * Gets the value of the sourceId property.
	 * 
	 */
	public String getSourceId() {
		return sourceId;
	}

	/**
	 * Sets the value of the sourceId property.
	 * 
	 */
	public void setSourceId(String value) {
		this.sourceId = value;
	}

	/**
	 * Gets the value of the hasParcel property.
	 * 
	 */
	public boolean isHasParcel() {
		return hasParcel;
	}

	/**
	 * Sets the value of the hasParcel property.
	 * 
	 */
	public void setHasParcel(boolean value) {
		this.hasParcel = value;
	}

	/**
	 * Gets the value of the orderStatus property.
	 * 
	 * @return possible object is {@link OrderStatus }
	 * 
	 */
	public OrderStatus getStatus() {
		return status;
	}

	/**
	 * Sets the value of the orderStatus property.
	 * 
	 * @param value
	 *            allowed object is {@link OrderStatus }
	 * 
	 */
	public void setStatus(OrderStatus value) {
		this.status = value;
	}

	/**
	 * Gets the value of the orders property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the orders property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getOrders().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link OrderItem
	 * }
	 * 
	 * 
	 */
	public List<OrderItem> getOrders() {
		if (orders == null) {
			orders = new ArrayList<OrderItem>();
		}
		return this.orders;
	}

	public void setOrders(List<OrderItem> orderItems) {
		this.orders = orderItems;
	}

	/**
	 * Gets the value of the enquiryItems property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the enquiryItems property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getEnquiryItems().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link EnquiryItem }
	 * 
	 * 
	 */
	public List<EnquiryItem> getEnquiryItems() {
		if (enquiryItems == null) {
			enquiryItems = new ArrayList<EnquiryItem>();
		}
		return this.enquiryItems;
	}

	/**
	 * Gets the value of the transactionDetail property.
	 * 
	 * @return possible object is {@link TransactionDetail }
	 * 
	 */
	public TransactionDetail getTransactionDetail() {
		return transactionDetail;
	}

	/**
	 * Sets the value of the transactionDetail property.
	 * 
	 * @param value
	 *            allowed object is {@link TransactionDetail }
	 * 
	 */
	public void setTransactionDetail(TransactionDetail value) {
		this.transactionDetail = value;
	}

	/**
	 * Gets the value of the settlementType property.
	 * 
	 * @return possible object is {@link SettlementType }
	 * 
	 */
	public SettlementType getSettlementType() {
		return settlementType;
	}

	/**
	 * Sets the value of the settlementType property.
	 * 
	 * @param value
	 *            allowed object is {@link SettlementType }
	 * 
	 */
	public void setSettlementType(SettlementType value) {
		this.settlementType = value;
	}

	/**
	 * Gets the value of the settlements property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the settlements property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getSettlements().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link Settlement }
	 * 
	 * 
	 */
	public List<Settlement> getSettlements() {
		if (settlements == null) {
			settlements = new ArrayList<Settlement>();
		}
		return this.settlements;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 * 
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the unitName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUnitName() {
		return unitName;
	}

	/**
	 * Sets the value of the unitName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitName(String value) {
		this.unitName = value;
	}

	/**
	 * Gets the value of the terminalId property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getTerminalId() {
		return terminalId;
	}

	/**
	 * Sets the value of the terminalId property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setTerminalId(Integer value) {
		this.terminalId = value;
	}

	/**
	 * Gets the value of the billStartTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getBillStartTime() {
		return billStartTime;
	}

	/**
	 * Sets the value of the billStartTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBillStartTime(Date value) {
		this.billStartTime = value;
	}

	/**
	 * Gets the value of the billCreationTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getBillCreationTime() {
		return billCreationTime;
	}

	/**
	 * Sets the value of the billCreationTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBillCreationTime(Date value) {
		this.billCreationTime = value;
	}

	/**
	 * Gets the value of the billCreationSeconds property.
	 * 
	 */
	public int getBillCreationSeconds() {
		return billCreationSeconds;
	}

	/**
	 * Sets the value of the billCreationSeconds property.
	 * 
	 */
	public void setBillCreationSeconds(int value) {
		this.billCreationSeconds = value;
	}

	/**
	 * Gets the value of the billingServerTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getBillingServerTime() {
		return billingServerTime;
	}

	/**
	 * Sets the value of the billingServerTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBillingServerTime(Date value) {
		this.billingServerTime = value;
	}

	/**
	 * Gets the value of the channelPartner property.
	 * 
	 */
	public int getChannelPartner() {
		return channelPartner;
	}

	/**
	 * Sets the value of the channelPartner property.
	 * 
	 */
	public void setChannelPartner(int value) {
		this.channelPartner = value;
	}

	/**
	 * Gets the value of the deliveryPartner property.
	 * 
	 */
	public int getDeliveryPartner() {
		return deliveryPartner;
	}

	/**
	 * Sets the value of the deliveryPartner property.
	 * 
	 */
	public void setDeliveryPartner(int value) {
		this.deliveryPartner = value;
	}

	/**
	 * Gets the value of the offerCode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOfferCode() {
		return offerCode;
	}

	/**
	 * Sets the value of the offerCode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOfferCode(String value) {
		this.offerCode = value;
	}

	/**
	 * Gets the value of the cancellationDetails property.
	 * 
	 * @return possible object is {@link ActionDetail }
	 * 
	 */
	public ActionDetail getCancellationDetails() {
		return cancellationDetails;
	}

	/**
	 * Sets the value of the cancellationDetails property.
	 * 
	 * @param value
	 *            allowed object is {@link ActionDetail }
	 * 
	 */
	public void setCancellationDetails(ActionDetail value) {
		this.cancellationDetails = value;
	}

	/**
	 * Gets the value of the orderRemark property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOrderRemark() {
		return orderRemark;
	}

	/**
	 * Sets the value of the orderRemark property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOrderRemark(String value) {
		this.orderRemark = value;
	}

	/**
	 * Gets the value of the deliveryAddress property.
	 * 
	 */
	public Integer getDeliveryAddress() {
		return deliveryAddress;
	}

	/**
	 * Sets the value of the deliveryAddress property.
	 * 
	 */
	public void setDeliveryAddress(Integer value) {
		this.deliveryAddress = value;
	}

	/**
	 * Gets the value of the customerName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * Sets the value of the customerName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCustomerName(String value) {
		this.customerName = value;
	}

	/**
	 * Gets the value of the containsSignupOffer property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getContainsSignupOffer() {
		return containsSignupOffer;
	}

	/**
	 * Sets the value of the containsSignupOffer property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setContainsSignupOffer(String value) {
		this.containsSignupOffer = value;
	}

	/**
	 * Gets the value of the tempCode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTempCode() {
		return tempCode;
	}

	/**
	 * Sets the value of the tempCode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTempCode(String value) {
		this.tempCode = value;
	}

	/**
	 * Gets the value of the metadataList property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the metadataList property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getMetadataList().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link OrderMetadata }
	 * 
	 * 
	 */
	public List<OrderMetadata> getMetadataList() {
		if (metadataList == null) {
			metadataList = new ArrayList<OrderMetadata>();
		}
		return this.metadataList;
	}

	/**
	 * Gets the value of the customerId property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * Sets the value of the customerId property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setCustomerId(Integer value) {
		this.customerId = value;
	}

	/**
	 * Gets the value of the customerId property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public String getWebCustomerId() {
		return webCustomerId;
	}

	/**
	 * Sets the value of the customerId property.
	 * 
	 * @param webCustomerId
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setWebCustomerId(String webCustomerId) {
		this.webCustomerId = webCustomerId;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

	public Integer getTokenNumber() {
		return tokenNumber;
	}

	public void setTokenNumber(Integer tokenNumber) {
		this.tokenNumber = tokenNumber;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}
}
