package com.stpl.tech.neo.domain.model;

import java.io.Serializable;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.neo.domain.model.mongo.Adapter3;

@XmlAccessorType(XmlAccessType.FIELD)
public class LaunchOfferData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1522872673865628807L;

	private String token;
	private int offerDetailId;
	private String contactNumber;
	private String customerName;
	private int unitId;
	private String acquisitionSource;
	private String couponCode;
	private String offerText;
	private int offerDayCount;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	private Date startDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	private Date endDate;
	private boolean notified;
	private String errorMessage;
	private String urlEndPoint;
	private String unitName;
	private String unitLocation;

	public int getOfferDetailId() {
		return offerDetailId;
	}

	public void setOfferDetailId(int offerDetailId) {
		this.offerDetailId = offerDetailId;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getAcquisitionSource() {
		return acquisitionSource;
	}

	public void setAcquisitionSource(String acquisitionSource) {
		this.acquisitionSource = acquisitionSource;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public boolean isNotified() {
		return notified;
	}

	public void setNotified(boolean notified) {
		this.notified = notified;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public String getOfferText() {
		return offerText;
	}

	public void setOfferText(String offerText) {
		this.offerText = offerText;
	}

	public int getOfferDayCount() {
		return offerDayCount;
	}

	public void setOfferDayCount(int offerDayCount) {
		this.offerDayCount = offerDayCount;
	}

	public String getUrlEndPoint() {
		return urlEndPoint;
	}

	public void setUrlEndPoint(String urlEndPoint) {
		this.urlEndPoint = urlEndPoint;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getUnitLocation() {
		return unitLocation;
	}

	public void setUnitLocation(String unitLocation) {
		this.unitLocation = unitLocation;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	@Override
	public String toString() {
		return "LaunchOfferData [offerDetailId=" + offerDetailId + ", contactNumber=" + contactNumber
				+ ", customerName=" + customerName + ", unitId=" + unitId + ", acquisitionSource=" + acquisitionSource
				+ ", couponCode=" + couponCode + ", offerText=" + offerText + ", offerDayCount=" + offerDayCount
				+ ", startDate=" + startDate + ", endDate=" + endDate + ", notified=" + notified + ", errorMessage="
				+ errorMessage + ", urlEndPoint=" + urlEndPoint + ", unitName=" + unitName + ", unitLocation="
				+ unitLocation + "]";
	}

}
