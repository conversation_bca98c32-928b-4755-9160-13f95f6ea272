//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CartStatus.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CartStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="CREATED"/&gt;
 *     &lt;enumeration value="CHECKOUT"/&gt;
 *     &lt;enumeration value="PAYMENT_DONE"/&gt;
 *     &lt;enumeration value="PAYMENT_FAILED"/&gt;
 *     &lt;enumeration value="SETTLED"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "CartStatus")
@XmlEnum
public enum CartStatus {

    CREATED,
    CHECKOUT,
    PAYMENT_DONE,
    PAYMENT_FAILED,
    SETTLED,
    INVALID;

    public String value() {
        return name();
    }

    public static CartStatus fromValue(String v) {
        return valueOf(v);
    }

}
