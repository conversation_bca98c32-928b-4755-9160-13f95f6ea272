//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.21 at 07:35:35 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for OrderView complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OrderView"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="generateOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pointsRedeemed" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="source" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}OrderStatus"/&gt;
 *         &lt;element name="orders" type="{http://www.w3schools.com}OrderItem" maxOccurs="unbounded"/&gt;
 *         &lt;element name="transactionDetail" type="{http://www.w3schools.com}TransactionDetail"/&gt;
 *         &lt;element name="settlements" type="{http://www.w3schools.com}Settlement" maxOccurs="unbounded"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="billingServerTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancellationDetails" type="{http://www.w3schools.com}ActionDetail"/&gt;
 *         &lt;element name="orderRemark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="offerCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderView", propOrder = {
    "generateOrderId",
    "pointsRedeemed",
    "source",
    "status",
    "orders",
    "transactionDetail",
    "settlements",
    "unitName",
    "billingServerTime",
    "cancellationDetails",
    "orderRemark",
    "offerCode"
})
public class OrderView {

    @XmlElement(required = true)
    protected String generateOrderId;
	@XmlElement(required = true, nillable = true)
	protected String externalOrderId;
    protected int pointsRedeemed;
    @XmlElement(required = true)
    protected String source;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected OrderStatus status;
    @XmlElement(required = true)
    protected List<OrderItem> orders;
    @XmlElement(required = true)
    protected TransactionDetail transactionDetail;
    @XmlElement(required = true)
    protected List<Settlement> settlements;
    @XmlElement(required = true)
    protected String unitName;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date billingServerTime;
    @XmlElement(required = true)
    protected ActionDetail cancellationDetails;
    @XmlElement(required = true)
    protected String orderRemark;
	protected Address address;
    @XmlElement(required = false, type = Integer.class, nillable = true)
    protected Integer tokenNumber;
    @XmlElement(required = true)
    protected String offerCode;

    /**
     * Gets the value of the generateOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGenerateOrderId() {
        return generateOrderId;
    }

    /**
     * Sets the value of the generateOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGenerateOrderId(String value) {
        this.generateOrderId = value;
    }

    /**
     * Gets the value of the pointsRedeemed property.
     * 
     */
    public int getPointsRedeemed() {
        return pointsRedeemed;
    }

    /**
     * Sets the value of the pointsRedeemed property.
     * 
     */
    public void setPointsRedeemed(int value) {
        this.pointsRedeemed = value;
    }

    /**
     * Gets the value of the source property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getSource() {
        return source;
    }

    /**
     * Sets the value of the source property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setSource(String value) {
        this.source = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link OrderStatus }
     *     
     */
    public OrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link OrderStatus }
     *     
     */
    public void setStatus(OrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the orders property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the orders property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOrders().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OrderItem }
     * 
     * 
     */
    public List<OrderItem> getOrders() {
        if (orders == null) {
            orders = new ArrayList<OrderItem>();
        }
        return this.orders;
    }

    /**
     * Gets the value of the transactionDetail property.
     * 
     * @return
     *     possible object is
     *     {@link TransactionDetail }
     *     
     */
    public TransactionDetail getTransactionDetail() {
        return transactionDetail;
    }

    /**
     * Sets the value of the transactionDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link TransactionDetail }
     *     
     */
    public void setTransactionDetail(TransactionDetail value) {
        this.transactionDetail = value;
    }

    /**
     * Gets the value of the settlements property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the settlements property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSettlements().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Settlement }
     * 
     * 
     */
    public List<Settlement> getSettlements() {
        if (settlements == null) {
            settlements = new ArrayList<Settlement>();
        }
        return this.settlements;
    }

    /**
     * Gets the value of the unitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * Sets the value of the unitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitName(String value) {
        this.unitName = value;
    }

    /**
     * Gets the value of the billingServerTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getBillingServerTime() {
        return billingServerTime;
    }

    /**
     * Sets the value of the billingServerTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingServerTime(Date value) {
        this.billingServerTime = value;
    }

    /**
     * Gets the value of the cancellationDetails property.
     * 
     * @return
     *     possible object is
     *     {@link ActionDetail }
     *     
     */
    public ActionDetail getCancellationDetails() {
        return cancellationDetails;
    }

    /**
     * Sets the value of the cancellationDetails property.
     * 
     * @param value
     *     allowed object is
     *     {@link ActionDetail }
     *     
     */
    public void setCancellationDetails(ActionDetail value) {
        this.cancellationDetails = value;
    }

    /**
     * Gets the value of the orderRemark property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderRemark() {
        return orderRemark;
    }

    /**
     * Sets the value of the orderRemark property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderRemark(String value) {
        this.orderRemark = value;
    }
    
	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String externalOrderId) {
		this.externalOrderId = externalOrderId;
	}
	
	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

    public Integer getTokenNumber() {
        return tokenNumber;
    }

    public void setTokenNumber(Integer tokenNumber) {
        this.tokenNumber = tokenNumber;
    }

    /**
     * Gets the value of the offerCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOfferCode() {
        return offerCode;
    }

    /**
     * Sets the value of the offerCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOfferCode(String value) {
        this.offerCode = value;
    }

}
