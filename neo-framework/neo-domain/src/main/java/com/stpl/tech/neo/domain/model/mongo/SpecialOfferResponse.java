package com.stpl.tech.neo.domain.model.mongo;

import java.math.BigDecimal;

public class SpecialOfferResponse {
    private String offerType;
    private String offerCode;
    private String validityFrom;
    private String validityTill;
    private String text;
    private Boolean isExistingOffer;
    private BigDecimal chaayosCash;
    private String offerTnCString;
    private Integer maxUsage;
    private String sessionToken;
    private String refCode;

    public SpecialOfferResponse() {
    }

    public SpecialOfferResponse(String offerType) {
        this.offerType = offerType;
    }

    public SpecialOfferResponse(String offerType, String sessionToken) {
        this.offerType = offerType;
        this.sessionToken = sessionToken;
    }

    public String getOfferType() {
        return offerType;
    }

    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    public String getOfferCode() {
        return offerCode;
    }

    public void setOfferCode(String offerCode) {
        this.offerCode = offerCode;
    }

    public String getValidityFrom() {
        return validityFrom;
    }

    public void setValidityFrom(String validityFrom) {
        this.validityFrom = validityFrom;
    }

    public String getValidityTill() {
        return validityTill;
    }

    public void setValidityTill(String validityTill) {
        this.validityTill = validityTill;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getIsExistingOffer() {
        return isExistingOffer;
    }

    public void setIsExistingOffer(Boolean existingOffer) {
        isExistingOffer = existingOffer;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public String getOfferTnCString() {
        return offerTnCString;
    }

    public void setOfferTnCString(String offerTnCString) {
        this.offerTnCString = offerTnCString;
    }

    public Integer getMaxUsage() {
        return maxUsage;
    }

    public void setMaxUsage(Integer maxUsage) {
        this.maxUsage = maxUsage;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }
}
