//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.17 at 08:05:26 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


/**
 * <p>Java class for CartDetail complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="CartDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="cartId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deviceId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sessionId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="orderDetail" type="{http://www.w3schools.com}Order"/&gt;
 *         &lt;element name="creationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="checkoutTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cartStatus" type="{http://www.w3schools.com}CartStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CartDetail", propOrder = {
        "cartId",
        "deviceId",
        "customerId",
        "sessionId",
        "orderDetail",
        "creationTime",
        "checkoutTime",
        "cartStatus",
        "redirectCartId"
})
@Document
@XmlRootElement(name = "CartDetail")
public class CartDetail {

    @Id
    @XmlElement(required = true)
    protected String cartId;
    @XmlElement(required = true)
    protected String deviceId;
    @XmlElement(required = true, nillable = true)
    @Indexed
    protected String customerId;
    @XmlElement(required = true, nillable = true)
    protected String sessionId;
    @XmlElement(required = true)
    protected Order orderDetail;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3.class)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter3.class)
    @XmlSchemaType(name = "date")
    protected Date checkoutTime;
    @Indexed
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected CartStatus cartStatus;
    protected boolean isInterState = false;

    protected String orderType;
    protected String redirectCartId;

    /**
     * Gets the value of the cartId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCartId() {
        return cartId;
    }

    /**
     * Sets the value of the cartId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCartId(String value) {
        this.cartId = value;
    }

    /**
     * Gets the value of the deviceId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDeviceId() {
        return deviceId;
    }

    /**
     * Sets the value of the deviceId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDeviceId(String value) {
        this.deviceId = value;
    }

    /**
     * Gets the value of the customerId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * Sets the value of the customerId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * Gets the value of the sessionId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * Sets the value of the sessionId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSessionId(String value) {
        this.sessionId = value;
    }

    /**
     * Gets the value of the orderDetail property.
     *
     * @return possible object is
     * {@link Order }
     */
    public Order getOrderDetail() {
        return orderDetail;
    }

    /**
     * Sets the value of the orderDetail property.
     *
     * @param value allowed object is
     *              {@link Order }
     */
    public void setOrderDetail(Order value) {
        this.orderDetail = value;
    }

    /**
     * Gets the value of the creationTime property.
     *
     * @return possible object is
     * {@link String }
     */
    public Date getCreationTime() {
        return creationTime;
    }

    /**
     * Sets the value of the creationTime property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCreationTime(Date value) {
        this.creationTime = value;
    }

    /**
     * Gets the value of the checkoutTime property.
     *
     * @return possible object is
     * {@link String }
     */
    public Date getCheckoutTime() {
        return checkoutTime;
    }

    /**
     * Sets the value of the checkoutTime property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCheckoutTime(Date value) {
        this.checkoutTime = value;
    }

    /**
     * Gets the value of the cartStatus property.
     *
     * @return possible object is
     * {@link CartStatus }
     */
    public CartStatus getCartStatus() {
        return cartStatus;
    }

    /**
     * Sets the value of the cartStatus property.
     *
     * @param value allowed object is
     *              {@link CartStatus }
     */
    public void setCartStatus(CartStatus value) {
        this.cartStatus = value;
    }

    public boolean isInterState() {
        return isInterState;
    }

    public void setInterState(boolean interState) {
        isInterState = interState;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    /**
     * Gets the value of the cartId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRedirectCartId() {
        return redirectCartId;
    }

    /**
     * Sets the value of the cartId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRedirectCartId(String value) {
        this.redirectCartId = value;
    }
}
