package com.stpl.tech.neo.domain.model;

import java.util.Date;

public class CampaignDetailResponse {
    private Integer campaignId;
    private String image1;
    private String image2;
    private String image3;
    private String utmHeading;
    private String utmDesc;
    private String utmImageUrl;
    private Date startDate;
    private Date endDate;
    private String landingPageDescription;

    private Integer unitId;

    private String unitName;
    private Date offerStartDate;

    public CampaignDetailResponse() {
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public String getImage1() {
        return image1;
    }

    public void setImage1(String image1) {
        this.image1 = image1;
    }

    public String getImage2() {
        return image2;
    }

    public void setImage2(String image2) {
        this.image2 = image2;
    }

    public String getImage3() {
        return image3;
    }

    public void setImage3(String image3) {
        this.image3 = image3;
    }

    public String getUtmHeading() {
        return utmHeading;
    }

    public void setUtmHeading(String utmHeading) {
        this.utmHeading = utmHeading;
    }

    public String getUtmDesc() {
        return utmDesc;
    }

    public void setUtmDesc(String utmDesc) {
        this.utmDesc = utmDesc;
    }

    public String getUtmImageUrl() {
        return utmImageUrl;
    }

    public void setUtmImageUrl(String utmImageUrl) {
        this.utmImageUrl = utmImageUrl;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getLandingPageDescription() {
        return landingPageDescription;
    }

    public void setLandingPageDescription(String landingPageDescription) {
        this.landingPageDescription = landingPageDescription;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getOfferStartDate() {
        return offerStartDate;
    }

    public void setOfferStartDate(Date offerStartDate) {
        this.offerStartDate = offerStartDate;
    }
}
