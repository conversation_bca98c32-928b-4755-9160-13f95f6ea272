package com.stpl.tech.neo.domain.model.mongo;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

@Document
public class PaytmCreateRequest {

	@Id
	private String paytmCreateRequestId;

	@JsonProperty("REQUEST_TYPE")
	private String requestType;
	@JsonProperty("MID")
	private String mid;
	@JsonProperty("ORDER_ID")
	private String orderId;
	@JsonProperty("CUST_ID")
	private String customerId;
	@JsonProperty("TXN_AMOUNT")
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal transactionAmount;
	@JsonProperty("CHANNEL_ID")
	private String channelId;
	@JsonProperty("INDUSTRY_TYPE_ID")
	private String industryTypeId;
	@JsonProperty("WEBSITE")
	private String website;
	@JsonProperty("CHECKSUMHASH")
	private String checksumHash;
	@JsonProperty("MOBILE_NO")
	private String contactNumber;
	@JsonProperty("EMAIL")
	private String emailId;
	@JsonIgnore
	@JsonProperty("PROMO_CAMP_ID")
	private String promoCode;
	@JsonIgnore
	@JsonProperty("ORDER_DETAILS")
	private String orderDetail;
	@JsonIgnore
	@JsonProperty("VERIFIED_BY")
	private String verifiedBy;
	@JsonIgnore
	@JsonProperty("IS_USER_VERIFIED")
	private String isUserVerified;
	@JsonProperty("CALLBACK_URL")
	private String callbackUrl;

	@JsonIgnore
	@JsonProperty("QR_CODE_ID")
	private String qrCodeId;

	public PaytmCreateRequest() {

	}

	public String getPaytmCreateRequestId() {
		return paytmCreateRequestId;
	}

	public void setPaytmCreateRequestId(String paytmCreateRequestId) {
		this.paytmCreateRequestId = paytmCreateRequestId;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public String getIndustryTypeId() {
		return industryTypeId;
	}

	public void setIndustryTypeId(String industryTypeId) {
		this.industryTypeId = industryTypeId;
	}

	public String getWebsite() {
		return website;
	}

	public void setWebsite(String website) {
		this.website = website;
	}

	public String getChecksumHash() {
		return checksumHash;
	}

	public void setChecksumHash(String checksumHash) {
		this.checksumHash = checksumHash;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getPromoCode() {
		return promoCode;
	}

	public void setPromoCode(String promoCode) {
		this.promoCode = promoCode;
	}

	public String getOrderDetail() {
		return orderDetail;
	}

	public void setOrderDetail(String orderDetail) {
		this.orderDetail = orderDetail;
	}

	public String getIsUserVerified() {
		return isUserVerified;
	}

	public void setIsUserVerified(String isUserVerified) {
		this.isUserVerified = isUserVerified;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getVerifiedBy() {
		return verifiedBy;
	}

	public void setVerifiedBy(String verifiedBy) {
		this.verifiedBy = verifiedBy;
	}

	public String getQrCodeId() {
		return qrCodeId;
	}

	public void setQrCodeId(String qrCodeId) {
		this.qrCodeId = qrCodeId;
	}
}
