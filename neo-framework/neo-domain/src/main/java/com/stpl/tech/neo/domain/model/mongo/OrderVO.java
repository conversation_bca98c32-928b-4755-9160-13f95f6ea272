//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.12.09 at 07:35:40 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for OrderVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OrderVO"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="generateOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="hasParcel" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}OrderStatus"/&gt;
 *         &lt;element name="orders" type="{http://www.w3schools.com}OrderItemVO" maxOccurs="unbounded"/&gt;
 *         &lt;element name="billingServerTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="address" type="{http://www.w3schools.com}AddressVO"/&gt;
 *         &lt;element name="source" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderVO", propOrder = {
    "generateOrderId",
    "hasParcel",
    "status",
    "orders",
    "billingServerTime",
    "address",
    "source",
    "unitName"
})
public class OrderVO {

    @XmlElement(required = true)
    protected String generateOrderId;
    protected boolean hasParcel;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected OrderStatus status;
    @XmlElement(required = true)
    protected List<OrderItemVO> orders;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date billingServerTime;
    @XmlElement(required = true)
    protected AddressVO address;
    @XmlElement(required = true)
    protected String source;
    protected String unitName;

    /**
     * Gets the value of the generateOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGenerateOrderId() {
        return generateOrderId;
    }

    /**
     * Sets the value of the generateOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGenerateOrderId(String value) {
        this.generateOrderId = value;
    }

    /**
     * Gets the value of the hasParcel property.
     * 
     */
    public boolean isHasParcel() {
        return hasParcel;
    }

    /**
     * Sets the value of the hasParcel property.
     * 
     */
    public void setHasParcel(boolean value) {
        this.hasParcel = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link OrderStatus }
     *     
     */
    public OrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link OrderStatus }
     *     
     */
    public void setStatus(OrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the orders property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the orders property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOrders().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OrderItemVO }
     * 
     * 
     */
    public List<OrderItemVO> getOrders() {
        if (orders == null) {
            orders = new ArrayList<OrderItemVO>();
        }
        return this.orders;
    }

    /**
     * Gets the value of the billingServerTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getBillingServerTime() {
        return billingServerTime;
    }

    /**
     * Sets the value of the billingServerTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingServerTime(Date value) {
        this.billingServerTime = value;
    }

    /**
     * Gets the value of the deliveryAddress property.
     * 
     * @return
     *     possible object is
     *     {@link AddressVO }
     *     
     */
    public AddressVO getAddress() {
        return address;
    }

    /**
     * Sets the value of the deliveryAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link AddressVO }
     *     
     */
    public void setAddress(AddressVO value) {
        this.address = value;
    }

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

}
