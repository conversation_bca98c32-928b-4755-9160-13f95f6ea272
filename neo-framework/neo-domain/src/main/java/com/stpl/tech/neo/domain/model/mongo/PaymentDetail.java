//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.17 at 08:05:26 PM IST 
//

package com.stpl.tech.neo.domain.model.mongo;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentDetail", propOrder = { "externalPaymentId", "paymentStatus", "cartId", "sessionId",
		"paymentMode", "razorPayPayload", "paytmPayload" })
@Document
@XmlRootElement(name = "PaymentDetail")
public class PaymentDetail {

	@Id
	@XmlElement(required = true)
	protected String paymentId;
	@XmlElement(required = true)
	@Indexed
	protected String externalPaymentId;
	@XmlElement(required = true)
	protected String paymentStatus;
	@XmlElement(required = true, nillable = true)
	@Indexed
	protected String cartId;
	@XmlElement(required = true, nillable = true)
	protected String sessionId;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal amount;
	@XmlElement(required = true, nillable = true)
	protected Integer paymentModeId;
	@XmlElement(required = true, nillable = true)
	protected RazorPayCreateRequest razorPayPayload;

	@XmlElement(required = true, nillable = true)
	protected PaytmCreateRequest paytmPayload;

	@XmlElement(required = true, nillable = true)
	protected EzetapCreateRequest ezetapPayload;

	@XmlElement(required = true, nillable = true)
	protected AGSCreateRequest agsPayload;

	public String getPaymentId() {
		return paymentId;
	}

	public void setPaymentId(String paymentId) {
		this.paymentId = paymentId;
	}

	public String getExternalPaymentId() {
		return externalPaymentId;
	}

	public void setExternalPaymentId(String externalPaymentId) {
		this.externalPaymentId = externalPaymentId;
	}

	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public String getCartId() {
		return cartId;
	}

	public void setCartId(String cartId) {
		this.cartId = cartId;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public Integer getPaymentModeId() {
		return paymentModeId;
	}

	public void setPaymentModeId(Integer paymentMode) {
		this.paymentModeId = paymentMode;
	}

	public RazorPayCreateRequest getRazorPayPayload() {
		return razorPayPayload;
	}

	public void setRazorPayPayload(RazorPayCreateRequest razorPayPayload) {
		this.razorPayPayload = razorPayPayload;
	}

	public PaytmCreateRequest getPaytmPayload() {
		return paytmPayload;
	}

	public void setPaytmPayload(PaytmCreateRequest paytmPayload) {
		this.paytmPayload = paytmPayload;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public EzetapCreateRequest getEzetapPayload() {
		return ezetapPayload;
	}

	public void setEzetapPayload(EzetapCreateRequest ezetapPayload) {
		this.ezetapPayload = ezetapPayload;
	}

	public AGSCreateRequest getAgsPayload() {
		return agsPayload;
	}

	public void setAgsPayload(AGSCreateRequest agsPayload) {
		this.agsPayload = agsPayload;
	}
}
