package com.stpl.tech.neo.domain.model;

public class RequestClient {

	private String key;
	private int requestCount;
	private long endTime;

	public RequestClient() {
		super();
	}

	public RequestClient(String key, int requestCount, long delay) {
		super();
		this.key = key;
		this.requestCount = requestCount;
		this.endTime = System.currentTimeMillis() + delay;
	}

	public boolean isExpired(long time) {
		return endTime < time;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String value) {
		this.key = value;
	}

	public int getRequestCount() {
		return requestCount;
	}

	public void setRequestCount(int value) {
		this.requestCount = value;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long startTime) {
		this.endTime = startTime;
	}

}
