package com.stpl.tech.neo.domain.model.mongo;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;

@Document
public class CustomerSignupData {

    @Id
    String id;
    String contactNumber;
    boolean offerAvailed;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public boolean getOfferAvailed() {
        return offerAvailed;
    }

    public void setOfferAvailed(boolean offerAvailed) {
        this.offerAvailed = offerAvailed;
    }
}
