//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.14 at 11:12:30 AM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for UnitLocalityMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitLocalityMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="mappingId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="appLink" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="webLink" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitLocalityMapping", propOrder = {
    "mappingId",
    "unitId",
    "unitName",
    "appLink",
    "webLink"
})
@Document
public class UnitLinkMapping {

	@Id
    @XmlElement(required = true)
    protected String mappingId;
    @XmlElement(required = true)
    protected Integer unitId;
    @XmlElement(required = true)
    protected String unitName;
    @XmlElement(required = true)
    protected String appLink;
    @XmlElement(required = true)
    protected String webLink;

    public String getMappingId() {
        return mappingId;
    }

    public void setMappingId(String mappingId) {
        this.mappingId = mappingId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getAppLink() {
        return appLink;
    }

    public void setAppLink(String appLink) {
        this.appLink = appLink;
    }

    public String getWebLink() {
        return webLink;
    }

    public void setWebLink(String webLink) {
        this.webLink = webLink;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        UnitLinkMapping that = (UnitLinkMapping) o;

        return new EqualsBuilder()
                .append(mappingId, that.mappingId)
                .append(unitId, that.unitId)
                .append(unitName, that.unitName)
                .append(appLink, that.appLink)
                .append(webLink, that.webLink)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(mappingId)
                .append(unitId)
                .append(unitName)
                .append(appLink)
                .append(webLink)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "UnitLinkMapping{" +
                "mappingId='" + mappingId + '\'' +
                ", unitId=" + unitId +
                ", unitName='" + unitName + '\'' +
                ", appLink='" + appLink + '\'' +
                ", webLink='" + webLink + '\'' +
                '}';
    }
}
