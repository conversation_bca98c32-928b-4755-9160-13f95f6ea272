package com.stpl.tech.neo.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerInfoResponse {

    private int id;
    private String name;
    private String contact;
    private String email;
    private boolean contactVerified;
    private boolean emailVerified;
    private int unitId;
    private boolean eligibleForSignupOffer;
    private boolean newCustomer;
    private String otp;
    private boolean feedbackRequired;
    private boolean otpVerified;
    private Integer tcId;
    private String signUpRefCode;
    private Date lastVisitTime;
    private boolean optOutOfFaceIt = false;
    private Date optOutTime;

    protected int loyalityPoints;
    protected BigDecimal chaayosCash;

    public CustomerInfoResponse() {
        super();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getLoyalityPoints() {
        return loyalityPoints;
    }

    public void setLoyalityPoints(int loyalityPoints) {
        this.loyalityPoints = loyalityPoints;
    }

    public boolean isContactVerified() {
        return contactVerified;
    }

    public void setContactVerified(boolean contactVerified) {
        this.contactVerified = contactVerified;
    }

    public boolean isEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(boolean emailverified) {
        this.emailVerified = emailverified;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public boolean isNewCustomer() {
        return newCustomer;
    }

    public void setNewCustomer(boolean newCustomer) {
        this.newCustomer = newCustomer;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public boolean isEligibleForSignupOffer() {
        return eligibleForSignupOffer;
    }

    public void setEligibleForSignupOffer(boolean eligibleForSignupOffer) {
        this.eligibleForSignupOffer = eligibleForSignupOffer;
    }

    public boolean isFeedbackRequired() {
        return feedbackRequired;
    }

    public void setFeedbackRequired(boolean takeFeedback) {
        this.feedbackRequired = takeFeedback;
    }

    public boolean isOtpVerified() {
        return otpVerified;
    }

    public void setOtpVerified(boolean otpVerified) {
        this.otpVerified = otpVerified;
    }

    public Integer getTcId() {
        return tcId;
    }

    public void setTcId(Integer tcId) {
        this.tcId = tcId;
    }

    public String getSignUpRefCode() {
        return signUpRefCode;
    }

    public void setSignUpRefCode(String refCode) {
        this.signUpRefCode = refCode;
    }

    public Date getLastVisitTime() {
        return lastVisitTime;
    }

    public void setLastVisitTime(Date lastVisitTime) {
        this.lastVisitTime = lastVisitTime;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

	public boolean isOptOutOfFaceIt() {
		return optOutOfFaceIt;
	}

	public void setOptOutOfFaceIt(boolean optOutOfFaceIt) {
		this.optOutOfFaceIt = optOutOfFaceIt;
	}

	public Date getOptOutTime() {
		return optOutTime;
	}

	public void setOptOutTime(Date optOutTime) {
		this.optOutTime = optOutTime;
	}


}

