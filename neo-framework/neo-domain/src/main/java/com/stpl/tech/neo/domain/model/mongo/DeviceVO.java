//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.02 at 12:25:11 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DeviceVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DeviceVO"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="deviceKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sessionKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cartDetail" type="{http://www.w3schools.com}CartDetail"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DeviceVO", propOrder = {
    "deviceKey",
    "sessionKey",
    "cartDetail"
})
public class DeviceVO {

    @XmlElement(required = true, nillable = true)
    protected String deviceKey;
    @XmlElement(required = true, nillable = true)
    protected String sessionKey;
    @XmlElement(required = true, nillable = true)
    protected CartDetail cartDetail;

    
    public DeviceVO() {
		super();
	}

	public DeviceVO(String deviceKey, String sessionKey, CartDetail cartDetail) {
		super();
		this.deviceKey = deviceKey;
		this.sessionKey = sessionKey;
		this.cartDetail = cartDetail;
	}

	/**
     * Gets the value of the deviceKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeviceKey() {
        return deviceKey;
    }

    /**
     * Sets the value of the deviceKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeviceKey(String value) {
        this.deviceKey = value;
    }

    /**
     * Gets the value of the sessionKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSessionKey() {
        return sessionKey;
    }

    /**
     * Sets the value of the sessionKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSessionKey(String value) {
        this.sessionKey = value;
    }

    /**
     * Gets the value of the cartDetail property.
     * 
     * @return
     *     possible object is
     *     {@link CartDetail }
     *     
     */
    public CartDetail getCartDetail() {
        return cartDetail;
    }

    /**
     * Sets the value of the cartDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link CartDetail }
     *     
     */
    public void setCartDetail(CartDetail value) {
        this.cartDetail = value;
    }

}
