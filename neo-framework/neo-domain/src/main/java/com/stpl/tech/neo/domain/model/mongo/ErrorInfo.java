/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.neo.domain.model.mongo;

public class ErrorInfo {

	public final Integer errorCode;
	public final String errorType;
	public final String errorMessage;

	public ErrorInfo(String errorType, Exception errorMessage) {
		this.errorCode = 500;
		this.errorType = errorType;
		this.errorMessage = errorMessage.getLocalizedMessage();
	}

	public ErrorInfo(Integer errorCode, String errorType, Exception errorMessage) {
		this.errorCode = errorCode;
		this.errorType = errorType;
		this.errorMessage = errorMessage.getLocalizedMessage();
	}
}