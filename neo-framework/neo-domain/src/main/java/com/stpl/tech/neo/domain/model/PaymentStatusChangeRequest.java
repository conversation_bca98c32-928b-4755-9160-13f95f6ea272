package com.stpl.tech.neo.domain.model;

import java.io.Serializable;

public class PaymentStatusChangeRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2295423520911212199L;

	private String receiptId;
	private String status;
	private String cancelledBy;
	private String cancellationReason;
	private String failureReason;

	public String getReceiptId() {
		return receiptId;
	}

	public void setReceiptId(String receiptId) {
		this.receiptId = receiptId;
	}

	public String getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(String cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCancellationReason() {
		return cancellationReason;
	}

	public void setCancellationReason(String cancellationReason) {
		this.cancellationReason = cancellationReason;
	}

	public String getFailureReason() {
		return failureReason;
	}

	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

}
