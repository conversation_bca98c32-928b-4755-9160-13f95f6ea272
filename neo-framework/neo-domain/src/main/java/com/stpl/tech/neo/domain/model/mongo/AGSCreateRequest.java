package com.stpl.tech.neo.domain.model.mongo;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
public class AGSCreateRequest {

    @Id
    private String AGSCreateRequestId;

    @JsonProperty("receipt")
    private String orderId;

    @JsonProperty("amount")
    private int transactionAmount;

    private String status;

    @JsonProperty("id")
    private String paymentTransactionId;

    public AGSCreateRequest() {

    }

    public String getAGSCreateRequestId() {
        return AGSCreateRequestId;
    }

    public void setAGSCreateRequestId(String AGSCreateRequestId) {
        this.AGSCreateRequestId = AGSCreateRequestId;
    }

    public String getPaymentTransactionId() {
        return paymentTransactionId;
    }

    public void setPaymentTransactionId(String paymentTransactionId) {
        this.paymentTransactionId = paymentTransactionId;
    }

    public String getStatus() {
        return status;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public int getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(int transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
