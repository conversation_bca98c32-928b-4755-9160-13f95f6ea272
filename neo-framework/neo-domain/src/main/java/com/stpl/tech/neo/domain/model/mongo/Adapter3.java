//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 08:01:27 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.text.ParseException;
import java.util.Date;

public class Adapter3
    extends XmlAdapter<String, Date>
{


    public Date unmarshal(String value) throws ParseException {
        return (com.stpl.tech.neo.domain.model.DateAdapter.parseDate(value));
    }

    public String marshal(Date value) {
        return (com.stpl.tech.neo.domain.model.DateAdapter.printDate(value));
    }

}
