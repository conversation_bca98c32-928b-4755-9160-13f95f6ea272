//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.18 at 07:41:58 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for OrderItem complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="OrderItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="itemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="itemName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="productCategory" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="price" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="discountDetail" type="{http://www.w3schools.com}DiscountDetail"/&gt;
 *         &lt;element name="complimentaryDetail" type="{http://www.w3schools.com}ComplimentaryDetail"/&gt;
 *         &lt;element name="addons" type="{http://www.w3schools.com}AddonData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="billType" type="{http://www.w3schools.com}BillType"/&gt;
 *         &lt;element name="recipeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderItem", propOrder = {
        "itemId",
        "productId",
        "itemName",
        "productName",
        "productCategory",
        "quantity",
        "price",
        "totalAmount",
        "amount",
        "discountDetail",
        "complimentaryDetail",
        "addons",
        "dimension",
        "billType",
        "composition",
        "recipeId",
        "cardType",
        "isCardValid",
        "code"
})
public class OrderItem {

    protected int itemId;
    protected int productId;
    protected String itemName;
    protected int customizationStrategy;
    @XmlElement(required = true)
    protected String productName;
    @XmlElement(required = true)
    protected IdCodeName productCategory;
    protected int quantity;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal price;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal totalAmount;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal amount;
    @XmlElement(required = true, nillable = true)
    protected DiscountDetail discountDetail;
    @XmlElement(required = true)
    protected ComplimentaryDetail complimentaryDetail;
    @XmlElement(nillable = true)
    protected List<AddonData> addons;
    @XmlElement(required = true, nillable = true)
    protected String dimension;
    @XmlElement(required = true, defaultValue = "NET_PRICE")
    @XmlSchemaType(name = "string")
    protected BillType billType;
    @XmlElement(nillable = true)
    @Field
    protected OrderItemComposition composition;
    protected int recipeId;
    @XmlElement(required = true)
    protected String itemCode;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    protected BigDecimal tax;
    @Field
    protected String code;
    @Field
    protected String cardType;
    @Field
    protected String isCardValid;
    @Field
    protected List<TaxDetail> taxes;

    /**
     * Gets the value of the itemId property.
     */
    public int getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     */
    public void setItemId(int value) {
        this.itemId = value;
    }

    /**
     * Gets the value of the productId property.
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     */
    public void setProductId(int value) {
        this.productId = value;
    }

    /**
     * Gets the value of the productName property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the productCategory property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getProductCategory() {
        return productCategory;
    }

    /**
     * Sets the value of the productCategory property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setProductCategory(IdCodeName value) {
        this.productCategory = value;
    }

    /**
     * Gets the value of the quantity property.
     */
    public int getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the quantity property.
     */
    public void setQuantity(int value) {
        this.quantity = value;
    }

    /**
     * Gets the value of the price property.
     *
     * @return possible object is
     * {@link String }
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * Sets the value of the price property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPrice(BigDecimal value) {
        this.price = value;
    }

    /**
     * Gets the value of the totalAmount property.
     *
     * @return possible object is
     * {@link String }
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTotalAmount(BigDecimal value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the amount property.
     *
     * @return possible object is
     * {@link String }
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    /**
     * Gets the value of the discountDetail property.
     *
     * @return possible object is
     * {@link DiscountDetail }
     */
    public DiscountDetail getDiscountDetail() {
        return discountDetail;
    }

    /**
     * Sets the value of the discountDetail property.
     *
     * @param value allowed object is
     *              {@link DiscountDetail }
     */
    public void setDiscountDetail(DiscountDetail value) {
        this.discountDetail = value;
    }

    /**
     * Gets the value of the complimentaryDetail property.
     *
     * @return possible object is
     * {@link ComplimentaryDetail }
     */
    public ComplimentaryDetail getComplimentaryDetail() {
        return complimentaryDetail;
    }

    /**
     * Sets the value of the complimentaryDetail property.
     *
     * @param value allowed object is
     *              {@link ComplimentaryDetail }
     */
    public void setComplimentaryDetail(ComplimentaryDetail value) {
        this.complimentaryDetail = value;
    }

    /**
     * Gets the value of the addons property.
     * <p>
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the addons property.
     * <p>
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAddons().add(newItem);
     * </pre>
     * <p>
     * <p>
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AddonData }
     */
    public List<AddonData> getAddons() {
        if (addons == null) {
            addons = new ArrayList<AddonData>();
        }
        return this.addons;
    }

    /**
     * Gets the value of the dimension property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDimension() {
        return dimension;
    }

    /**
     * Sets the value of the dimension property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDimension(String value) {
        this.dimension = value;
    }

    /**
     * Gets the value of the billType property.
     *
     * @return possible object is
     * {@link BillType }
     */
    public BillType getBillType() {
        return billType;
    }

    /**
     * Sets the value of the billType property.
     *
     * @param value allowed object is
     *              {@link BillType }
     */
    public void setBillType(BillType value) {
        this.billType = value;
    }

    public OrderItemComposition getComposition() {
        return composition;
    }

    public void setComposition(OrderItemComposition composition) {
        this.composition = composition;
    }

    /**
     * Gets the value of the recipeId property.
     */
    public int getRecipeId() {
        return recipeId;
    }

    /**
     * Sets the value of the recipeId property.
     */
    public void setRecipeId(int value) {
        this.recipeId = value;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public int getCustomizationStrategy() {
        return customizationStrategy;
    }

    public void setCustomizationStrategy(int customizationStrategy) {
        this.customizationStrategy = customizationStrategy;
    }

    /**
     * Gets the value of the itemCode property.
     *
     * @return possible object is {@link String }
     */
    public String getItemCode() {
        return itemCode;
    }

    /**
     * Sets the value of the itemCode property.
     *
     * @param value allowed object is {@link String }
     */
    public void setItemCode(String value) {
        this.itemCode = value;
    }

    public List<TaxDetail> getTaxes() {
        if (taxes == null) {
            taxes = new ArrayList<>();
        }
        return taxes;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsCardValid() {
        return isCardValid;
    }

    public void setIsCardValid(String isCardValid) {
        this.isCardValid = isCardValid;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
}
