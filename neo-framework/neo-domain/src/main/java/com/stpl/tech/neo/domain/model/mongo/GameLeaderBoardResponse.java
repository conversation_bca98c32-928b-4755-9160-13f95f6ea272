package com.stpl.tech.neo.domain.model.mongo;

import java.util.List;

public class GameLeaderBoardResponse {
    private GameLeaderBoardDTO yourScore;
    private List<GameLeaderBoardDTO> top10Score;
    private boolean isInTop10;
    private Integer rank;

    public GameLeaderBoardResponse() {
    }

    public GameLeaderBoardDTO getYourScore() {
        return yourScore;
    }

    public void setYourScore(GameLeaderBoardDTO yourScore) {
        this.yourScore = yourScore;
    }

    public List<GameLeaderBoardDTO> getTop10Score() {
        return top10Score;
    }

    public void setTop10Score(List<GameLeaderBoardDTO> top10Score) {
        this.top10Score = top10Score;
    }

    public boolean isInTop10() {
        return isInTop10;
    }

    public void setInTop10(boolean inTop10) {
        isInTop10 = inTop10;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }
}
