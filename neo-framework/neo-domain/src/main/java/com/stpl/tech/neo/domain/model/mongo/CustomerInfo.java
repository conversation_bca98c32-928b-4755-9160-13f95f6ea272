package com.stpl.tech.neo.domain.model.mongo;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;

public class CustomerInfo {

    protected String contact;
    protected String name;
    protected String email;
    protected int loyalityPoints;
    protected BigDecimal chaayosCash;
    protected String refCode;
    protected DeviceVO device;
    protected int customerId;
    protected boolean optOutOfFaceIt = false;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3.class)
    @XmlSchemaType(name = "date")
    protected Date optOutTime;
    protected boolean eligibleForSignupOffer;
    protected boolean signUpOfferAvailed;
    protected boolean oldCustomer;
    protected boolean cafeOrdersExist;
    protected boolean deliveryOrdersExist;
    protected String offerCode;



    public CustomerInfo() {
        super();
    }

    public CustomerInfo(String name, String email, int loyalityPoints, BigDecimal chaayosCash,
                        String refCode, boolean optOutOfFaceIt, boolean eligibleForSignupOffer, boolean signUpOfferAvailed) {
        super();
        this.name = name;
        this.email = email;
        this.loyalityPoints = loyalityPoints;
        this.chaayosCash = chaayosCash;
        this.refCode = refCode;
        this.optOutOfFaceIt = optOutOfFaceIt;
        this.eligibleForSignupOffer = eligibleForSignupOffer;
        this.signUpOfferAvailed = signUpOfferAvailed;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }


    public DeviceVO getDevice() {
        return device;
    }

    public void setDevice(DeviceVO device) {
        this.device = device;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String referralCode) {
        this.refCode = referralCode;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public int getLoyalityPoints() {
        return loyalityPoints;
    }

    public void setLoyalityPoints(int loyalityPoints) {
        this.loyalityPoints = loyalityPoints;
    }

    public boolean isOptOutOfFaceIt() {
        return optOutOfFaceIt;
    }

    public void setOptOutOfFaceIt(boolean optOutOfFaceIt) {
        this.optOutOfFaceIt = optOutOfFaceIt;
    }

    public Date getOptOutTime() {
        return optOutTime;
    }

    public void setOptOutTime(Date optOutTime) {
        this.optOutTime = optOutTime;
    }

    public boolean isEligibleForSignupOffer() {
        return eligibleForSignupOffer;
    }

    public void setEligibleForSignupOffer(boolean eligibleForSignupOffer) {
        this.eligibleForSignupOffer = eligibleForSignupOffer;
    }

    public boolean isSignUpOfferAvailed() {
        return signUpOfferAvailed;
    }

    public void setSignUpOfferAvailed(boolean signUpOfferAvailed) {
        this.signUpOfferAvailed = signUpOfferAvailed;
    }

    public boolean isOldCustomer() {
        return oldCustomer;
    }

    public void setOldCustomer(boolean oldCustomer) {
        this.oldCustomer = oldCustomer;
    }

    public boolean isCafeOrdersExist() {
        return cafeOrdersExist;
    }

    public void setCafeOrdersExist(boolean cafeOrdersExist) {
        this.cafeOrdersExist = cafeOrdersExist;
    }

    public boolean isDeliveryOrdersExist() {
        return deliveryOrdersExist;
    }

    public void setDeliveryOrdersExist(boolean deliveryOrdersExist) {
        this.deliveryOrdersExist = deliveryOrdersExist;
    }

    public String getOfferCode() {
        return offerCode;
    }

    public void setOfferCode(String offerCode) {
        this.offerCode = offerCode;
    }
}
