//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.neo.domain.model.mongo;

import com.stpl.tech.redis.domain.model.Adapter1;
import com.stpl.tech.redis.domain.model.BasicInfo;
import com.stpl.tech.redis.domain.model.ProductData;
import com.stpl.tech.redis.domain.model.UnitOfMeasure;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>Java class for IngredientProductDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="IngredientProductDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="product" type="{http://www.w3schools.com}ProductData"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3schools.com}BasicInfo"/&gt;
 *         &lt;element name="uom" type="{http://www.w3schools.com}UnitOfMeasure"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="defaultSetting" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientProductDetail", propOrder = {
    "product",
    "dimension",
    "uom",
    "quantity",
    "defaultSetting"
})
public class IngredientProductDetail implements Serializable {

    private static final long serialVersionUID = -5632729081529842241L;
    @XmlElement(required = true)
    protected ProductData product;
    @XmlElement(required = true)
    protected BasicInfo dimension;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected UnitOfMeasure uom;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal quantity;
    @XmlElement(defaultValue = "false")
    protected boolean defaultSetting;
    protected boolean critical;

    /**
     * Gets the value of the product property.
     * 
     * @return
     *     possible object is
     *     {@link ProductData }
     *     
     */
    public ProductData getProduct() {
        return product;
    }

    /**
     * Sets the value of the product property.
     * 
     * @param value
     *     allowed object is
     *     {@link ProductData }
     *     
     */
    public void setProduct(ProductData value) {
        this.product = value;
    }

    /**
     * Gets the value of the dimension property.
     * 
     * @return
     *     possible object is
     *     {@link BasicInfo }
     *     
     */
    public BasicInfo getDimension() {
        return dimension;
    }

    /**
     * Sets the value of the dimension property.
     * 
     * @param value
     *     allowed object is
     *     {@link BasicInfo }
     *     
     */
    public void setDimension(BasicInfo value) {
        this.dimension = value;
    }

    /**
     * Gets the value of the uom property.
     * 
     * @return
     *     possible object is
     *     {@link UnitOfMeasure }
     *     
     */
    public UnitOfMeasure getUom() {
        return uom;
    }

    /**
     * Sets the value of the uom property.
     * 
     * @param value
     *     allowed object is
     *     {@link UnitOfMeasure }
     *     
     */
    public void setUom(UnitOfMeasure value) {
        this.uom = value;
    }

    /**
     * Gets the value of the quantity property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the quantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQuantity(BigDecimal value) {
        this.quantity = value;
    }

    /**
     * Gets the value of the defaultSetting property.
     * 
     */
    public boolean isDefaultSetting() {
        return defaultSetting;
    }

    /**
     * Sets the value of the defaultSetting property.
     * 
     */
    public void setDefaultSetting(boolean value) {
        this.defaultSetting = value;
    }

    public boolean isCritical() {
        return critical;
    }

    public void setCritical(boolean critical) {
        this.critical = critical;
    }

}
