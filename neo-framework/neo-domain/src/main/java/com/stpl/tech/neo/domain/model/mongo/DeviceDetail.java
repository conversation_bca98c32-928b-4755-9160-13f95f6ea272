//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.17 at 08:05:26 PM IST 
//

package com.stpl.tech.neo.domain.model.mongo;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <p>
 * Java class for DeviceDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="DeviceDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="deviceId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="userAgent" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="platform" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ipAddress" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lastSessionId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lastCartId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="createdAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastAccessedAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DeviceDetail", propOrder = { "deviceId", "userAgent", "platform", "ipAddress", "customerId",
		"lastSessionId", "lastCartId", "createdAt", "lastAccessedAt" })
@Document
@XmlRootElement(name = "DeviceDetail")
public class DeviceDetail {

	@Id
	@XmlElement(required = true)
	protected String deviceId;
	@XmlElement(required = true, nillable = true)
	protected String userAgent;
	@XmlElement(required = true)
	protected String platform;
	@XmlElement(required = true)
	protected String ipAddress;
	@XmlElement(required = true, nillable = true)
	protected String customerId;
	@XmlElement(required = true, nillable = true)
	protected String lastSessionId;
	@XmlElement(required = true, nillable = true)
	protected String lastCartId;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	protected Date createdAt;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "date")
	protected Date lastAccessedAt;

	/**
	 * Gets the value of the deviceId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDeviceId() {
		return deviceId;
	}

	/**
	 * Sets the value of the deviceId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDeviceId(String value) {
		this.deviceId = value;
	}

	/**
	 * Gets the value of the userAgent property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUserAgent() {
		return userAgent;
	}

	/**
	 * Sets the value of the userAgent property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUserAgent(String value) {
		this.userAgent = value;
	}

	/**
	 * Gets the value of the platform property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * Sets the value of the platform property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPlatform(String value) {
		this.platform = value;
	}

	/**
	 * Gets the value of the ipAddress property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getIpAddress() {
		return ipAddress;
	}

	/**
	 * Sets the value of the ipAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setIpAddress(String value) {
		this.ipAddress = value;
	}

	/**
	 * Gets the value of the customerId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCustomerId() {
		return customerId;
	}

	/**
	 * Sets the value of the customerId property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setCustomerId(String value) {
		this.customerId = value;
	}

	/**
	 * Gets the value of the lastSessionId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getLastSessionId() {
		return lastSessionId;
	}

	/**
	 * Sets the value of the lastSessionId property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setLastSessionId(String value) {
		this.lastSessionId = value;
	}

	/**
	 * Gets the value of the lastCartId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getLastCartId() {
		return lastCartId;
	}

	/**
	 * Sets the value of the lastCartId property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setLastCartId(String value) {
		this.lastCartId = value;
	}

	/**
	 * Gets the value of the createdAt property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getCreatedAt() {
		return createdAt;
	}

	/**
	 * Sets the value of the createdAt property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setCreatedAt(Date value) {
		this.createdAt = value;
	}

	/**
	 * Gets the value of the lastAccessedAt property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getLastAccessedAt() {
		return lastAccessedAt;
	}

	/**
	 * Sets the value of the lastAccessedAt property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setLastAccessedAt(Date value) {
		this.lastAccessedAt = value;
	}

	public String getDeviceKey() {
		return deviceId + ":" + lastAccessedAt.getTime();
	}

}
