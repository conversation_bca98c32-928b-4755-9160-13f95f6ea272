package com.stpl.tech.neo.domain.model;

import java.util.Date;

public class CustomerCampaignJourney implements java.io.Serializable{

    private static final long serialVersionUID = 4961420913469444864L;
    private Integer campaignJourneyId;
    private Date sessionStartTime;
    private String utmSource;
    private String utmMedium;
    private String contactNumber;
    private String finalState;
    private Integer orderId;
    private Integer campaignId;
    private String deviceType;
    private String browserName;
    private String deviceOS;
    private String gclid;

    public CustomerCampaignJourney() {
    }

    public Integer getCampaignJourneyId() {
        return campaignJourneyId;
    }

    public void setCampaignJourneyId(Integer campaignJourneyId) {
        this.campaignJourneyId = campaignJourneyId;
    }

    public Date getSessionStartTime() {
        return sessionStartTime;
    }

    public void setSessionStartTime(Date sessionStartTime) {
        this.sessionStartTime = sessionStartTime;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getFinalState() {
        return finalState;
    }

    public void setFinalState(String finalState) {
        this.finalState = finalState;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getBrowserName() {
        return browserName;
    }

    public void setBrowserName(String browserName) {
        this.browserName = browserName;
    }

    public String getDeviceOS() {
        return deviceOS;
    }

    public void setDeviceOS(String deviceOS) {
        this.deviceOS = deviceOS;
    }

    public String getGclid() {
        return gclid;
    }

    public void setGclid(String gclid) {
        this.gclid = gclid;
    }
}
