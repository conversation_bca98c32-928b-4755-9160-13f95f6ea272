<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:complexType name="DeviceDetail">
		<xs:sequence>
			<xs:element name="deviceId" type="xs:string" />
			<xs:element name="userAgent" type="xs:string" nillable="true" />
			<xs:element name="platform" type="xs:string" nillable="false" />
			<xs:element name="ipAddress" type="xs:string" nillable="false" />
			<xs:element name="customerId" type="xs:string" nillable="true" />
			<xs:element name="lastSessionId" type="xs:string"
				nillable="true" />
			<xs:element name="lastCartId" type="xs:string" nillable="true" />
			<xs:element name="createdAt" type="xs:date" />
			<xs:element name="lastAccessedAt" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SessionDetail">
		<xs:sequence>
			<xs:element name="sessionId" type="xs:string" />
			<xs:element name="deviceId" type="xs:string" nillable="false" />
			<xs:element name="customerId" type="xs:string" nillable="false" />
			<xs:element name="createdAt" type="xs:date" />
			<xs:element name="lastAccessedAt" type="xs:date" />
			<xs:element name="status" type="SessionStatus" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CartDetail">
		<xs:sequence>
			<xs:element name="cartId" type="xs:string" />
			<xs:element name="deviceId" type="xs:string" />
			<xs:element name="customerId" type="xs:string" nillable="true" />
			<xs:element name="sessionId" type="xs:string" nillable="true" />
			<xs:element name="orderDetail" type="Order" />
			<xs:element name="creationTime" type="xs:date" nillable="false" />
			<xs:element name="checkoutTime" type="xs:date" nillable="true" />
			<xs:element name="cartStatus" type="CartStatus" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CartItem">
		<xs:sequence>
			<xs:element name="cartId" type="xs:string" />
			<xs:element name="orderItem" type="OrderItem" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Order">
		<xs:sequence>
			<xs:element name="orderId" type="xs:string" />
			<xs:element name="generateOrderId" type="xs:string" />
			<xs:element name="unitOrderId" type="xs:string" />
			<xs:element name="campaignId" type="xs:string" />
			<xs:element name="employeeId" type="xs:int" />
			<xs:element name="pointsRedeemed" type="xs:int" />
			<xs:element name="source" type="xs:string" />
			<xs:element name="sourceId" type="xs:string" />
			<xs:element name="hasParcel" type="xs:boolean" />
			<xs:element name="orderStatus" type="OrderStatus" />
			<xs:element name="orders" type="OrderItem" minOccurs="1"
				maxOccurs="unbounded" />
			<xs:element name="enquiryItems" type="EnquiryItem"
				minOccurs="1" maxOccurs="unbounded" />
			<xs:element name="transactionDetail" type="TransactionDetail" />
			<xs:element name="settlementType" type="SettlementType" />
			<xs:element name="settlements" type="Settlement"
				minOccurs="1" maxOccurs="unbounded" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="unitName" type="xs:string" />
			<xs:element name="terminalId" type="xs:int" nillable="true" />
			<xs:element name="billStartTime" type="xs:date" />
			<xs:element name="billCreationTime" type="xs:date" />
			<xs:element name="billCreationSeconds" type="xs:int" />
			<xs:element name="billingServerTime" type="xs:date" />
			<xs:element name="channelPartner" type="xs:int" />
			<xs:element name="deliveryPartner" type="xs:int" />
			<xs:element name="offerCode" type="xs:string" />
			<xs:element name="cancellationDetails" type="ActionDetail" />
			<xs:element name="orderRemark" type="xs:string" />
			<xs:element name="deliveryAddress" type="xs:int" />
			<xs:element name="customerName" type="xs:string" />
			<xs:element name="containsSignupOffer" type="xs:string" />
			<xs:element name="tempCode" type="xs:string" />
			<xs:element name="metadataList" type="OrderMetadata"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderView">
		<xs:sequence>
			<xs:element name="generateOrderId" type="xs:string" />
			<xs:element name="pointsRedeemed" type="xs:int" />
			<xs:element name="source" type="xs:string" />
			<xs:element name="status" type="OrderStatus" />
			<xs:element name="orders" type="OrderItem" minOccurs="1"
				maxOccurs="unbounded" />
			<xs:element name="transactionDetail" type="TransactionDetail" />
			<xs:element name="settlements" type="Settlement"
				minOccurs="1" maxOccurs="unbounded" />
			<xs:element name="unitName" type="xs:string" />
			<xs:element name="billingServerTime" type="xs:date" />
			<xs:element name="cancellationDetails" type="ActionDetail" />
			<xs:element name="orderRemark" type="xs:string" />
			<xs:element name="offerCode" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderVO">
		<xs:sequence>
			<xs:element name="generateOrderId" type="xs:string" />
			<xs:element name="hasParcel" type="xs:boolean" />
			<xs:element name="orderStatus" type="OrderStatus" />
			<xs:element name="orders" type="OrderItemVO" minOccurs="1"
				maxOccurs="unbounded" />
			<xs:element name="billingServerTime" type="xs:date" />
			<xs:element name="deliveryAddress" type="AddressVO" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EnquiryItem">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="orderedQuantity" type="xs:int" />
			<xs:element name="availableQuantity" type="xs:int" />
			<xs:element name="replacementServed" type="xs:boolean" />
			<xs:element name="linkedOrderId" type="xs:int" />
			<xs:element name="linkedCustomerId" type="xs:int" />
			<xs:element name="linkedUnitId" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderItem">
		<xs:sequence>
			<xs:element name="itemId" type="xs:int" />
			<xs:element name="productId" type="xs:int" />
			<xs:element name="productName" type="xs:string" />
			<xs:element name="productCategory" type="IdCodeName" />
			<xs:element name="quantity" type="xs:int" />
			<xs:element name="price" type="xs:decimal" />
			<xs:element name="totalAmount" type="xs:decimal" />
			<xs:element name="amount" type="xs:decimal" />
			<xs:element name="discountDetail" type="DiscountDetail"
				nillable="true" />
			<xs:element name="complimentaryDetail" type="ComplimentaryDetail" />
			<xs:element name="addons" type="AddonData" nillable="true"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="dimension" type="xs:string" nillable="true" />
			<xs:element name="billType" type="BillType" default="NET_PRICE" />
			<xs:element name="recipeId" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderItemVO">
		<xs:sequence>
			<xs:element name="productId" type="xs:int" />
			<xs:element name="productName" type="xs:string" />
			<xs:element name="quantity" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TransactionDetail">
		<xs:sequence>
			<xs:element name="saleAmount" type="xs:decimal" />
			<xs:element name="totalAmount" type="xs:decimal" />
			<xs:element name="taxableAmount" type="xs:decimal" />
			<xs:element name="discountDetail" type="DiscountDetail"
				nillable="true" />
			<xs:element name="netPriceVat" type="PercentageDetail" />
			<xs:element name="mrpVat" type="PercentageDetail" />
			<xs:element name="surchargeOnTax" type="PercentageDetail" />
			<xs:element name="serviceTax" type="PercentageDetail" />
			<xs:element name="sbCess" type="PercentageDetail" />
			<xs:element name="kkCess" type="PercentageDetail" />
			<xs:element name="paidAmount" type="xs:decimal" />
			<xs:element name="roundOffValue" type="xs:decimal" />
			<xs:element name="savings" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Settlement">
		<xs:sequence>
			<xs:element name="settlementId" type="xs:int" />
			<xs:element name="mode" type="xs:int" />
			<xs:element name="modeDetail" type="PaymentMode" nillable="true" />
			<xs:element name="amount" type="xs:decimal" />
			<xs:element name="extraVouchers" type="xs:decimal"
				nillable="true" />
			<xs:element name="denominations" type="OrderPaymentDenomination"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ActionDetail">
		<xs:sequence>
			<xs:element name="reason" type="xs:string" nillable="true" />
			<xs:element name="approvedBy" type="xs:int" nillable="true" />
			<xs:element name="generatedBy" type="xs:int" nillable="true" />
			<xs:element name="actionTime" type="xs:date" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderMetadata">
		<xs:sequence>
			<xs:element name="metadataId" type="xs:int" />
			<xs:element name="orderId" type="xs:int" />
			<xs:element name="attributeName" type="xs:string" />
			<xs:element name="attributeValue" type="xs:string"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IdCodeName">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" nillable="true" />
			<xs:element name="type" type="xs:string" nillable="true" />
			<xs:element name="status" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountDetail">
		<xs:sequence>
			<xs:element name="discountCode" type="xs:int" nillable="true" />
			<xs:element name="discountReason" type="xs:string"
				nillable="true" />
			<xs:element name="discount" type="PercentageDetail"
				nillable="true" />
			<xs:element name="promotionalOffer" type="xs:decimal"
				nillable="true" />
			<xs:element name="totalDiscount" type="xs:decimal"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ComplimentaryDetail">
		<xs:sequence>
			<xs:element name="isComplimentary" type="xs:boolean"
				default="false" />
			<xs:element name="reasonCode" type="xs:int" nillable="true" />
			<xs:element name="reason" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddonData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" nillable="true" />
			<xs:element name="linkedProductId" type="xs:int" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PercentageDetail">
		<xs:sequence>
			<xs:element name="percentage" type="xs:decimal" />
			<xs:element name="value" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PaymentMode">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="settlementType" type="xs:string" />
			<xs:element name="generatePull" type="xs:boolean" />
			<xs:element name="denominations" type="DenominationDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderPaymentDenomination">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="orderId" type="xs:int" />
			<xs:element name="settlementId" type="xs:int" />
			<xs:element name="denominationDetailId" type="xs:int" />
			<xs:element name="count" type="xs:int" />
			<xs:element name="totalAmount" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DenominationDetail">
		<xs:sequence>
			<xs:element name="denominationId" type="xs:int" />
			<xs:element name="denominationCode" type="xs:string" />
			<xs:element name="denominationText" type="xs:string" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="displayOrder" type="xs:int" />
			<xs:element name="denominationValue" type="xs:int" />
			<xs:element name="bundleSize" type="xs:int" />
			<xs:element name="paymentMode" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Customer">
		<xs:sequence>
			<xs:element name="customerId" type="xs:string" />
			<xs:element name="id" type="xs:int" />
			<xs:element name="firstName" type="xs:string" />
			<xs:element name="middleName" type="xs:string" />
			<xs:element name="lastName" type="xs:string" />
			<xs:element name="gender" type="Gender" />
			<xs:element name="dob" type="xs:date" />
			<xs:element name="profilePic" type="xs:string" />
			<xs:element name="contactNumber" type="xs:string" />
			<xs:element name="countryCode" type="xs:string" />
			<xs:element name="emailVerified" type="xs:boolean" />
			<xs:element name="emailId" type="xs:string" />
			<xs:element name="loyaltyPoints" type="xs:int" />
			<xs:element name="registrationUnitId" type="xs:int" />
			<xs:element name="acquisitionSource" type="xs:string" />
			<xs:element name="contactNumberVerified" type="xs:boolean" />
			<xs:element name="availedSignupOffer" type="xs:boolean" />
			<xs:element name="smsSubscriber" type="xs:boolean" />
			<xs:element name="emailSubscriber" type="xs:boolean" />
			<xs:element name="loyaltySubscriber" type="xs:boolean" />
			<xs:element name="addresses" type="Address" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="createdAt" type="xs:date" />
			<xs:element name="lastLoginTime" type="xs:date" />
			<xs:element name="cartId" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CustomerData">
		<xs:sequence>
			<xs:element name="contact" type="xs:string" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="emailId" type="xs:string" />
			<xs:element name="deviceKey" type="xs:string" />
			<xs:element name="sessionKey" type="xs:string" />
			<xs:element name="update" type="xs:boolean" />
			<xs:element name="otp" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Address">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="line1" type="xs:string" />
			<xs:element name="line2" type="xs:string" />
			<xs:element name="line3" type="xs:string" />
			<xs:element name="locality" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="country" type="xs:string" />
			<xs:element name="zipCode" type="xs:string" />
			<xs:element name="contact1" type="xs:string" />
			<xs:element name="contact2" type="xs:string" />
			<xs:element name="addressType" type="AddressType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddressVO">
		<xs:sequence>
			<xs:element name="line1" type="xs:string" />
			<xs:element name="line2" type="xs:string" />
			<xs:element name="line3" type="xs:string" />
			<xs:element name="locality" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="zipCode" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CustomerAddress">
		<xs:sequence>
			<xs:element name="id" type="xs:string" />
			<xs:element name="contact" type="xs:string" />
			<xs:element name="address" type="Address" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CustomerProductConsumption">
		<xs:sequence>
			<xs:element name="id" type="xs:string" />
			<xs:element name="customerId" type="xs:string" nillable="true" />
			<xs:element name="currentProducts" type="ProductConsumption"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="allProducts" type="ProductConsumption"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProductConsumption">
		<xs:sequence>
			<xs:element name="id" type="xs:string" />
			<xs:element name="productId" type="xs:string" nillable="true" />
			<xs:element name="productName" type="xs:string" nillable="true" />
			<xs:element name="quantity" type="xs:int" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Preferences">
		<xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitLocalityMapping">
		<xs:sequence>
			<xs:element name="mappingId" type="xs:string" />
			<xs:element name="locality" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="country" type="xs:string" />
			<xs:element name="default" type="xs:boolean" />
			<xs:element name="primaryUnitName" type="xs:string" />
			<xs:element name="primaryUnitId" type="xs:int" />
			<xs:element name="primaryDeliveryTime" type="xs:int" />
			<xs:element name="skipPrimaryDeliveryCharge" type="xs:boolean" />
			<xs:element name="skipPrimaryPackagingCharge" type="xs:boolean" />
			<xs:element name="secondaryUnitName" type="xs:string" />
			<xs:element name="secondaryUnitId" type="xs:int" />
			<xs:element name="secondaryDeliveryTime" type="xs:int" />
			<xs:element name="skipSecondaryDeliveryCharge" type="xs:boolean" />
			<xs:element name="skipSecondaryPackagingCharge" type="xs:boolean" />
			<xs:element name="tertiaryUnitName" type="xs:string" />
			<xs:element name="tertiaryUnitId" type="xs:int" />
			<xs:element name="tertiaryDeliveryTime" type="xs:int" />
			<xs:element name="skipTertiaryDeliveryCharge" type="xs:boolean" />
			<xs:element name="skipTertiaryPackagingCharge" type="xs:boolean" />
			<xs:element name="primaryTakeAwayUnitName" type="xs:string" />
			<xs:element name="primaryTakeAwayUnitId" type="xs:int" />
			<xs:element name="primaryTakeAwayDeliveryTime" type="xs:int" />
			<xs:element name="secondaryTakeAwayUnitName" type="xs:string" />
			<xs:element name="secondaryTakeAwayUnitId" type="xs:int" />
			<xs:element name="secondaryTakeAwayDeliveryTime" type="xs:int" />
			<xs:element name="tertiaryTakeAwayUnitName" type="xs:string" />
			<xs:element name="tertiaryTakeAwayUnitId" type="xs:int" />
			<xs:element name="tertiaryTakeAwayDeliveryTime" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitLocalityMappingVO">
		<xs:sequence>
			<xs:element name="city" type="xs:string" />
			<xs:element name="locality" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="WebTag">
		<xs:sequence>
			<xs:element name="name" type="xs:string" />
			<xs:element name="category" type="WebTagCategory" />
			<xs:element name="values" type="xs:string" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeviceVO">
		<xs:sequence>
			<xs:element name="deviceKey" type="xs:string" nillable="true" />
			<xs:element name="sessionKey" type="xs:string" nillable="true" />
			<xs:element name="cartDetail" type="CartDetail" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Counter">
		<xs:sequence>
			<xs:element name="id" type="xs:string" />
			<xs:element name="seq" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="Gender" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MALE" />
			<xs:enumeration value="FEMALE" />
			<xs:enumeration value="OTHER" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AddressType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFFICE" />
			<xs:enumeration value="HOME" />
			<xs:enumeration value="OTHER" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OrderStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INITIATED" />
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="PROCESSING" />
			<xs:enumeration value="READY_TO_PARTIALLY_DISPATCH" />
			<xs:enumeration value="READY_TO_DISPATCH" />
			<xs:enumeration value="SETTLED" />
			<xs:enumeration value="CANCELLED_REQUESTED" />
			<xs:enumeration value="CANCELLED" />
			<xs:enumeration value="CLOSED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CartStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="CHECKOUT" />
			<xs:enumeration value="PAYMENT_DONE" />
			<xs:enumeration value="PAYMENT_FAILED" />
			<xs:enumeration value="SETTLED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SettlementType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DEBIT" />
			<xs:enumeration value="CREDIT" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BillType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MRP" />
			<xs:enumeration value="NET_PRICE" />
			<xs:enumeration value="ZERO_TAX" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SessionStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="LOGOUT" />
			<xs:enumeration value="EXPIRED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="WebTagCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MENU" />
			<xs:enumeration value="PAYMENT_MODE" />
			<xs:enumeration value="PRODUCT" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
