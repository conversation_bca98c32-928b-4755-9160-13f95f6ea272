<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>neo-framework</artifactId>
        <groupId>com.stpl.tech.neo</groupId>
        <version>5.0.15</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>neo-domain</artifactId>
    <packaging>jar</packaging>
    <name>neo-domain</name>
    <version>4.1.0-SNAPSHOT</version>
    <url>http://maven.apache.org</url>
<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.jvnet.jaxb2.maven2</groupId>-->
<!--                <artifactId>maven-jaxb2-plugin</artifactId>-->
<!--                <version>0.11.0</version>-->
<!--                <executions>-->
<!--                    &lt;!&ndash;<execution>-->
<!--                        <id>neo-domain-mongo-schema</id>-->
<!--                        <phase>generate-sources</phase>-->
<!--                        <goals>-->
<!--                            <goal>generate</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <schemaDirectory>src/main/xsds/mongo</schemaDirectory>-->
<!--                            <generatePackage>com.stpl.tech.neo.domain.model.mongo</generatePackage>-->
<!--                            <generateDirectory>${project.build.directory}/generated-sources/xjc-mongo</generateDirectory>-->
<!--                        </configuration>-->
<!--                    </execution>&ndash;&gt;-->
<!--                </executions>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->

    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.redis</groupId>
            <artifactId>redis-domain</artifactId>
            <version>5.0.15</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate.javax.persistence</groupId>
            <artifactId>hibernate-jpa-2.1-api</artifactId>
            <version>1.0.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
         <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jettison</groupId>
            <artifactId>jettison</artifactId>
            <version>1.3.8</version>
        </dependency>
    </dependencies>
</project>
