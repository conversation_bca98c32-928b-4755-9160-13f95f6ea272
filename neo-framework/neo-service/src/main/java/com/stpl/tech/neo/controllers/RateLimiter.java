package com.stpl.tech.neo.controllers;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.Semaphore;

import com.stpl.tech.neo.core.NeoServiceConstants;
import com.stpl.tech.util.AppUtils;

public class RateLimiter {
	
	private Semaphore semaphore;
	private Date lastAccessTime;

	public static Optional<RateLimiter> create() {
		RateLimiter limiter = new RateLimiter();
		return Optional.of(limiter);
	}

	private RateLimiter() {
		this.semaphore = new Semaphore(NeoServiceConstants.MAX_PERMITS);
		this.lastAccessTime = AppUtils.getCurrentTimestamp();
	}

	public boolean tryAcquire() {
		this.lastAccessTime = AppUtils.getCurrentTimestamp();
		return semaphore.tryAcquire();
	}

	public Semaphore getSemaphore() {
		return semaphore;
	}

	public Date getLastAccessTime() {
		return lastAccessTime;
	}
	
	

}