package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.CartService;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoOrderService;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartItem;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.redis.domain.model.ResponseCode;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_CART_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
        + WEB_CART_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
// 'v1/wcrt'
public class WebCartResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(WebCartResource.class);

    @Autowired
    private NeoOrderService orderService;

    @Autowired
    private CartService cartService;

    @Autowired
    private EnvironmentProperties props;

    // checkout cart only on cash on delivery
    @RequestMapping(value = "ckt")
    public String checkout(@RequestBody CartDetail cart) throws WebOrderException {
        LOG.info("Request to save and create order for cart {}", JSONSerializer.toJSON(cart));
        return orderService.saveAndCheckoutCart(cart);
    }

    // checkout cart only KIOSK , pay by cash
    @RequestMapping(value = "ckt/kiosk")
    public String checkoutKIOSKPayByCash(@RequestBody CartDetail cart) throws WebOrderException {
        LOG.info("Request to save and create order for cart {}", JSONSerializer.toJSON(cart));
        return orderService.saveAndCheckoutCartForKIOSKPayByCash(cart);
    }

    // checkout cart by id
    @RequestMapping(value = "ckt/id")
    public String checkoutByCartId(@RequestBody String cartId) throws WebOrderException {
        LOG.info("Request to create order for cartId {}", cartId);
        return orderService.checkout(cartId);
    }

    // checkout cart by webOrderId
    @RequestMapping(value = "ckt/wo")
    public String checkoutByWebOrderId(@RequestBody String woId) throws WebOrderException {
        LOG.info("Request to create order for Web Order Id {}", woId);
        return orderService.checkoutByWebOrderId(woId);
    }

    // sync cart
    @RequestMapping(value = "sync")
    public ResponseCode syncCart(@RequestBody CartDetail cart) throws WebOrderException {
        LOG.info("Request to sync cart {}", JSONSerializer.toJSON(cart));
        return orderService.syncCart(cart);
    }

    // create cart
    @RequestMapping(value = "c")
    @Deprecated
    public CartDetail createCart(@RequestBody CartDetail cartDetail) {
        LOG.info("Request to create cart: {}", JSONSerializer.toJSON(cartDetail));
        return cartService.createCart(cartDetail);
    }

    // create cart2
    @RequestMapping(value = "c2")
    public CartDetail createCartByGeneratedOrderId(@RequestBody String gOrderId) {
        LOG.info("Request to reorder with order id: {}", gOrderId);
        return cartService.addReOrderItemsToCart(gOrderId);
    }

    // add to cart
    @RequestMapping(value = "i/a")
    public boolean addToCart(@RequestBody final CartItem item) throws WebOrderException {
        LOG.info("Request to add cart item: {}", JSONSerializer.toJSON(item));
        return cartService.addToCart(item);
    }

    // update item
    @RequestMapping(value = "i/u")
    public boolean updateCartItem(@RequestBody final CartItem item) throws WebOrderException {
        LOG.info("Request to update cart item: {}", JSONSerializer.toJSON(item));
        return cartService.updateCartItem(item);
    }

    // remove from cart
    @RequestMapping(value = "i/r")
    public boolean removeCartItem(@RequestBody final CartItem item) throws WebOrderException {
        LOG.info("Request to remove cart item: {}", JSONSerializer.toJSON(item));
        return cartService.removeCartItem(item);
    }

    // clear cart
    @RequestMapping(value = "clr")
    public boolean clearCart(@RequestBody final String cartId) {
        LOG.info("Request to clear cart: {}", cartId);
        return cartService.clearCart(cartId);
    }

    // create gift card cart
    @RequestMapping(value = "cgc")
    public DeviceVO createGiftCardCart(@RequestBody final DeviceVO deviceVO){
        LOG.info("Request to create gift card cart:");
        try {
            return orderService.createGiftCardCart(deviceVO);
        } catch (Exception ex) {
            LOG.error("Exception Occurred while  creating gift card ", ex);
        }
        return null;
    }

    // sync old device id and customer id with device id after checkout of gift card order
    @RequestMapping(value = "soc")
    public DeviceVO syncOldCart(@RequestBody final DeviceVO deviceVO){
        LOG.info("Request to sync old cart:");
        try {
            orderService.attachCart(deviceVO.getCartDetail());
        } catch (Exception ex) {
            LOG.error("Exception Occurred while  creating gift card ", ex);
        }
        return null;
    }

    @RequestMapping(value = "or/{orderId}/dld", method = RequestMethod.GET, consumes = MediaType.ALL_VALUE)
    public void getOrderReceipt(@PathVariable String orderId, HttpServletResponse response) throws IOException {
        BufferedInputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        File file = null;
        try {
            LOG.info("Request to download order receipt where orderId is ", orderId);
            file = writeByte(orderService.downloadOrderReceipt(orderId), "/tmp/orderReceipt-" + orderId + ".pdf");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());
            inputStream = new BufferedInputStream(new FileInputStream(file));
            outputStream = new BufferedOutputStream(response.getOutputStream());
            byte[] buffer = new byte[1024];
            int bytesRead = 0;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred while downloading receipt", ex);
        } finally {
            if (outputStream != null) {
                outputStream.flush();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    // checkout cart only on cash on delivery
    @RequestMapping(value = "sos")
    public boolean setOrderSource(@RequestBody CartDetail request) throws WebOrderException {
        LOG.info("Request to save and create order for cart {}", JSONSerializer.toJSON(request));
        return orderService.setOrderSource(request);
    }

    private File writeByte(byte[] bytes, String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
            OutputStream os = new FileOutputStream(file);
            os.write(bytes);
            LOG.info("Successfully byte inserted");
            os.close();
            return file;
        } catch (Exception e) {
            LOG.error("Exception while creating file on neo " + e);
        }
        return null;
    }


}
