package com.stpl.tech.neo.core;

public class NeoServiceConstants {

	public static final String API_VERSION = "v1";

	public static final String SEPARATOR = "/";

	public static final String STAMPING_ROOT_CONTEXT = "st";

	public static final String NEO_CACHE_ROOT_CONTEXT = "nc";

	public static final String NEO_CACHE_MANAGEMENT_ROOT_CONTEXT = "ncm";

	public static final String WEB_CART_ROOT_CONTEXT = "wcrt";

	public static final String WEB_ORDER_ROOT_CONTEXT = "word";

	public static final String WEB_CUSTOMER_ROOT_CONTEXT = "c";

	public static final String WEB_CUSTOMER_INTERNAL_ROOT_CONTEXT = "cn";

	public static final String WEB_OFFER_ROOT_CONTEXT = "woff";

	public static final String WEB_PAYMENT_ROOT_CONTEXT = "wp";
	
	public static final String WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT = "wpe";

	public static final String WEB_INVENTORY_ROOT_CONTEXT = "win";

    public static final String TRUE_CALLER_ROOT_CONTEXT = "tc";
    
    public static final String EXTERNAL_PARTNER_ROOT_CONTEXT = "exp";
    
    public static final String REFERRAL_RESOURCE_ROOT_CONTEXT = "ref";
    
	public static final int MAX_PERMITS = 3;

	public static final long HOURS_FOR_EVICTION = 24;

}
