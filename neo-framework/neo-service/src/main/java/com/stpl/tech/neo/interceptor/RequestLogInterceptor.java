/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.neo.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class RequestLogInterceptor implements AsyncHandlerInterceptor {

    private static final Logger LOG = LoggerFactory.getLogger(RequestLogInterceptor.class);

    @Autowired
    private Environment env;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //LOG.info(":::::::::::::::::::Inside Session Auth Interceptor ::::::::::::::::: {} for {}", request.getMethod(), request.getPathInfo());
        try {
            boolean log = Boolean.valueOf(env.getProperty("request.log", "false"));
            if (log) {
                LOG.info("==============================================");
                LOG.info("requestURI:" + request.getRequestURI());
                LOG.info("queryString:" + request.getQueryString());
                LOG.info("pathInfo:" + request.getPathInfo());
                LOG.info("contextPath:" + request.getContextPath());
                LOG.info("method:" + request.getMethod());
                LOG.info("remoteUser:" + request.getRemoteUser());
                LOG.info("servletPath:" + request.getServletPath());
                LOG.info("contentType:" + request.getContentType());
                LOG.info("localAddr:" + request.getLocalAddr());
                LOG.info("localName:" + request.getLocalName());
                LOG.info("protocol:" + request.getProtocol());
                LOG.info("remoteAddr:" + request.getRemoteAddr());
                LOG.info("remoteHost:" + request.getRemoteHost());
                LOG.info("remotePort:" + Integer.toString(request.getRemotePort()));
                String headers = "";
                if (request.getHeaderNames() != null) {
                    while (request.getHeaderNames().hasMoreElements()) {
                        headers = headers + " :::: " + request.getHeader(request.getHeaderNames().nextElement());
                    }
                }
                LOG.info("Headers: " + headers);
                final String[] params = {""};
                request.getParameterMap().forEach((s, strings) -> {
                    params[0] = params[0] + "::::" + s + "=" + strings.toString();
                });
                LOG.info(params[0]);
            }
        } catch (Exception e) {
            LOG.info("Error:" + e);
        }

        return true;
    }

}
