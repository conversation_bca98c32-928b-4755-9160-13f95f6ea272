package com.stpl.tech.neo.core;

public enum RateLimiterListType {

    USER_LOOK_UP("USER_LOOK_UP"),
    USER_GET_INFO("USER_GET_INFO");

    private final String value;

    RateLimiterListType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static RateLimiterListType fromValue(String v) {
        for (RateLimiterListType c: RateLimiterListType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }
}
