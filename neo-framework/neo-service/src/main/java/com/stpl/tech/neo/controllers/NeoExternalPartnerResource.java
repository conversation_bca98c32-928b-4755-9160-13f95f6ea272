package com.stpl.tech.neo.controllers;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.EXTERNAL_PARTNER_ROOT_CONTEXT;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.NeoExternalPartnerService;
import com.stpl.tech.neo.domain.model.ExternalPartnerResponse;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ EXTERNAL_PARTNER_ROOT_CONTEXT, produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/exp'
public class NeoExternalPartnerResource {

	private static final Logger LOG = LoggerFactory.getLogger(NeoCustomerResource.class);

	@Autowired
	private NeoExternalPartnerService customerService;

	// login
	@RequestMapping(value = "r/s" , method = RequestMethod.GET)
	public ExternalPartnerResponse requestSession(@RequestParam String accessKey, @RequestParam String token) throws WebOrderException {
		LOG.info("Request to login external partner customer for accessKey {}, token {}", accessKey, token);
		return customerService.requestCustomerLoginByKey(accessKey, token);
	}
}
