package com.stpl.tech.neo.config;

import java.util.TimeZone;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.stpl.tech.neo.core.config.NeoConfig;
import com.stpl.tech.redis.core.config.RedisConfig;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * Created by Chaayos on 01-10-2016.
 */
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@Configuration
@EnableScheduling
@EnableWebMvc
@Import(value = {NeoConfig.class, RedisConfig.class})
public class NeoServiceConfig extends SpringBootServletInitializer{

    static {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
    }

    public static void main(String[] args) {
        SpringApplication.run(NeoServiceConfig.class,args);
    }

    @Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(NeoServiceConfig.class);
	}

}
