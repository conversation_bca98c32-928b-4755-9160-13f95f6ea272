package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.core.service.NeoMetadataCacheManagementService;
import com.stpl.tech.neo.core.service.NeoMetadataService;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.redis.domain.model.ProductRecipeKey;
import com.stpl.tech.redis.domain.model.ProductRecipes;
import com.stpl.tech.redis.domain.model.Unit;
import com.stpl.tech.redis.domain.model.UnitDataVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.NEO_CACHE_ROOT_CONTEXT;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
        + NEO_CACHE_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
// 'v1/nc'
public class NeoMetadataResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(NeoMetadataResource.class);

    @Autowired
    private NeoMetadataService metaService;
    @Autowired
    private NeoMetadataCacheManagementService service;

    // get all cities
    @RequestMapping(method = RequestMethod.GET, value = "cts")
    public Map<String, Integer> getCities() throws IOException, URISyntaxException {
        // LOG.info("Request to get all cities");
        return metaService.getCities();
    }

    // localities for a city
    @RequestMapping(value = "lcts")
    public List<String> getLocalities(@RequestBody String city) {
        // LOG.info("Request to get localities for city {}", city);
        return metaService.getLocalitiesForCity(city);
    }

    // get delivery unit for city locality mapping
    @Deprecated
    @RequestMapping(value = "lct/d/u")
    public Unit getLocalityUnit(@RequestBody UnitLocalityMappingVO mapping) throws NeoMetadataException {
        // LOG.info("Request to get delivery unit for city {} and locality {}",
        // mapping.getCity(), mapping.getLocality());
        return metaService.getDeliveryUnit(mapping).getUnit();
    }

    // get delivery unit for city locality mapping
    @RequestMapping(value = "lct/d/u2")
    public UnitDataVO getLocalityUnitData(@RequestBody UnitLocalityMappingVO mapping) throws NeoMetadataException {
        // LOG.info("Request to get delivery unit for city {} and locality {}",
        // mapping.getCity(), mapping.getLocality());
        return metaService.getDeliveryUnit(mapping);
    }

    // get delivery unit for city locality mapping
    @RequestMapping(value = "ct/d/u1")
    public String getDefaultLocalityForCity(@RequestBody String city) throws NeoMetadataException {
        // LOG.info("Request to get delivery unit for city {} and locality {}",
        // mapping.getCity(), mapping.getLocality());
        return metaService.getDefaultLocalityForCity(city);
    }

    // get all take away units for city
    @RequestMapping(value = "lct/t/us")
    public List<IdName> getTakeAwayUnits(@RequestBody String city) throws NeoMetadataException, URISyntaxException {
        // LOG.info("Request to get take-away units for city {}", city);
        return metaService.getTakeAwayUnitsForCity(city);
    }

    // get recipes for products
    @RequestMapping(value = "p/r")
    public List<ProductRecipes> getProductRecipes(@RequestBody List<ProductRecipeKey> products)
            throws URISyntaxException {
        // LOG.info("Request to get product with id {}",
        // JSONSerializer.toJSON(products));
        return metaService.getProductRecipes(products);
    }

    // get Web Categories
    @RequestMapping(method = RequestMethod.GET, value = "c/w")
    public List<IdName> getWebCategories() throws URISyntaxException {
        // LOG.info("Request to get web product categories");
        return metaService.getWebCategories();
    }

    // get all tags
    @RequestMapping(method = RequestMethod.GET, value = "ts")
    public Set<String> getTag() throws URISyntaxException {
        // LOG.info("Request to get all tags");
        return metaService.getAllTags();
    }

    // get data for a tag
    @RequestMapping(value = "t")
    public List<String> getTagData(@RequestBody String tagName) throws URISyntaxException {
        // LOG.info("Request to get tag data for {}", tagName);
        return metaService.getTagData(tagName);
    }

    // get product map for all tags
    @RequestMapping(value = "pts")
    public Map<String, List<String>> getProductTagData() throws URISyntaxException {
        // LOG.info("Request to get map of product tag data");
        return metaService.getProductTagData();
    }

    // get product map for all tags
    @RequestMapping(value = "mts")
    public Map<String, List<String>> getMenuTagData() throws URISyntaxException {
        // LOG.info("Request to get map of menu tag data");
        return metaService.getMenuTagData();
    }

    // reload all data
    @RequestMapping(value = "r")
    public boolean reload() throws URISyntaxException {
        LOG.info("Request to reload redis and meta cache");
        metaService.reloadCache();
        return true;
    }

    // reload all data
    @RequestMapping(value = "rr")
    public boolean reloadRecipes() throws URISyntaxException {
        LOG.info("Request to reload recipe cache");
        metaService.reloadRecipeCache();
        return true;
    }


    @RequestMapping(value = "ru")
    public boolean updateUnitBasicDetails() throws URISyntaxException {
        LOG.info("Request to reload unit  cache");
        service.updateUnitBasicDetails();
        return true;
    }


    @RequestMapping(value = "pi")
    public Map<Integer, String> getProductImages(@RequestBody List<Integer> productIds) throws URISyntaxException {
        LOG.info("Request to reload unit  cache");
        return metaService.getProductImages(productIds);
    }

}
