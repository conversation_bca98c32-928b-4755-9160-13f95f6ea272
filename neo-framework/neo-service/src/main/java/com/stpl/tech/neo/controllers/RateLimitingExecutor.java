package com.stpl.tech.neo.controllers;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import com.stpl.tech.neo.core.RateLimiterListType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.neo.core.NeoServiceConstants;
import com.stpl.tech.util.AppUtils;

@Component
public class RateLimitingExecutor {

	private static final Logger LOG = LoggerFactory.getLogger(RateLimitingExecutor.class);

	private static final RateLimitingExecutor INSTANCE = new RateLimitingExecutor();

	private Map<String, Optional<RateLimiter>> limitersOfLookup = new ConcurrentHashMap<>();

	private Map<String, Optional<RateLimiter>> limitersOfGetInfo = new ConcurrentHashMap<>();

	public Map<String, Optional<RateLimiter>> getLimitersByType(String type){
		switch (type) {
			case "USER_LOOK_UP":
				return limitersOfLookup;
			case "USER_GET_INFO":
				return limitersOfGetInfo;
			default:
				LOG.error("Do not have limiters for type" + type);
				return null;
		}
	}

	public static RateLimitingExecutor getInstance() {
		return INSTANCE;
	}

	@Scheduled(fixedRate = 21600000L)
	public void schedulePermitReplenishment() {
		LOG.info("Running Eviction Scheduler");
		Arrays.asList(RateLimiterListType.values()).forEach(
				limitersType -> {
					if(limitersType.value().equalsIgnoreCase(RateLimiterListType.USER_GET_INFO.value())){
						replenishLimiters(limitersOfGetInfo);
					}
					if(limitersType.value().equalsIgnoreCase(RateLimiterListType.USER_LOOK_UP.value())){
						replenishLimiters(limitersOfLookup);
					}
				}
		);
	}

	private void replenishLimiters(Map<String, Optional<RateLimiter>> limiters) {
		Set<String> limitersToBeRemoved = new HashSet<>();
		for (String key : limiters.keySet()) {
			RateLimiter limiter = limiters.get(key).get();
			long diffInMillies = Math
					.abs(AppUtils.getCurrentTimestamp().getTime() - limiter.getLastAccessTime().getTime());
			long diff = TimeUnit.HOURS.convert(diffInMillies, TimeUnit.MILLISECONDS);
			if (diff >= NeoServiceConstants.HOURS_FOR_EVICTION) {
				limitersToBeRemoved.add(key);
			}
			limiter.getSemaphore().release(NeoServiceConstants.MAX_PERMITS - limiter.getSemaphore().availablePermits());

		}
		for (String key : limitersToBeRemoved) {
			limiters.remove(key);
		}
	}

}
