package com.stpl.tech.neo.controllers;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.STAMPING_ROOT_CONTEXT;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.neo.core.service.StampingService;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ STAMPING_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/st'
public class StampingResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(StampingResources.class);

	@Autowired
	private StampingService stampingService;

	// register new device
	@RequestMapping(value = "rd")
	public DeviceVO registerDevice(@RequestBody DeviceDetail deviceDetail, HttpServletRequest request) {
		LOG.info("Request to register device");
		deviceDetail.setIpAddress(request.getRemoteAddr());
		return stampingService.registerDevice(deviceDetail);
	}

	// stamp existing device
	@RequestMapping(value = "sd")
	public DeviceVO stampDevice(@RequestBody DeviceVO deviceVO) {
		LOG.info("Request to stamp device");
		return stampingService.stampDevice(deviceVO);
	}

}
