package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.core.service.NeoMetadataCacheManagementService;
import com.stpl.tech.neo.domain.model.mongo.RedirectTracking;
import com.stpl.tech.neo.domain.model.mongo.UnitDetail;
import com.stpl.tech.neo.domain.model.mongo.UnitLinkMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.WebTag;
import com.stpl.tech.neo.domain.model.mongo.WebTagCategory;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import static com.stpl.tech.neo.core.NeoServiceConstants.*;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ NEO_CACHE_MANAGEMENT_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE) // 'ncm'
public class NeoMetadataManagementResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(NeoMetadataManagementResource.class);

	@Autowired
	private NeoMetadataCacheManagementService service;

	@RequestMapping(value = "localities/get-localities")
	public List<UnitLocalityMapping> getAllUnitLocalityMappings() {
		LOG.info("Request to get All localities");
		return service.getAllUnitLocalityMappings();
	}

	@RequestMapping(value = "localities/add-locality")
	public boolean addUnitLocalityMapping(@RequestBody UnitLocalityMapping mapping) {
		LOG.info("Request to add locality {}", JSONSerializer.toJSON(mapping));
		return service.addUnitLocalityMapping(mapping);
	}

	@RequestMapping(value = "localities/add-localities")
	public boolean addUnitLocalityMappings(@RequestBody List<UnitLocalityMapping> mappings)
			throws IOException, URISyntaxException {
		LOG.info("Request to add localities {}", JSONSerializer.toJSON(mappings));
		return service.addUnitLocalityMappings(mappings);
	}

	@RequestMapping(value = "localities/remove-locality")
	public boolean removeUnitLocalityMapping(@RequestBody UnitLocalityMapping mapping)
			throws IOException, URISyntaxException {
		LOG.info("Request to remove localities {}", JSONSerializer.toJSON(mapping));
		return service.removeUnitLocalityMapping(mapping);
	}

	@RequestMapping(value = "localities/remove-localities")
	public boolean removeUnitLocalityMappings(@RequestBody List<UnitLocalityMapping> mappings)
			throws IOException, URISyntaxException {
		LOG.info("Request to remove localities {}", JSONSerializer.toJSON(mappings));
		return service.removeUnitLocalityMappings(mappings);
	}

	@RequestMapping(value = "localities/update-locality")
	public boolean updateUnitLocalityMappings(@RequestBody UnitLocalityMapping mapping)
			throws IOException, URISyntaxException {
		LOG.info("Request to update localities {}");
		return service.updateUnitLocalityMappings(mapping);
	}

	@RequestMapping(value = "localities/update-localities")
	public boolean updateUnitLocalityMappings(@RequestBody List<UnitLocalityMapping> mappings)
			throws IOException, URISyntaxException {
		LOG.info("Request to update localities {}");
		return service.updateUnitLocalityMappings(mappings);
	}

	@RequestMapping(value = "tags")
	public List<WebTag> getWebTags() throws IOException, URISyntaxException {
		LOG.info("Request to get all web tags");
		return service.getAllWebTags();
	}
	
	@RequestMapping(value = "tags/refresh")
	public boolean refreshTags() throws IOException, URISyntaxException {
		LOG.info("Request to update all web tags");
		service.updateTagData();
		return true;
	}

	@RequestMapping(value = "tag/types")
	public WebTagCategory[] getWebTagTypes() throws IOException, URISyntaxException {
		LOG.info("Request to get all web tags");
		return WebTagCategory.values();
	}

	@RequestMapping(value = "tag/add")
	public boolean addWebTag(@RequestBody WebTag tag) throws IOException, URISyntaxException {
		LOG.info("Request to add web tag {}", tag);
		return service.addWebTag(tag);
	}

	@RequestMapping(value = "tag/update")
	public boolean updateWebTag(@RequestBody WebTag tag) throws IOException, URISyntaxException {
		LOG.info("Request to update web tag {}", tag);
		return service.updateWebTag(tag);
	}

	@RequestMapping(value = "tag/remove")
	public boolean removeWebTag(@RequestBody WebTag tag) throws IOException, URISyntaxException {
		LOG.info("Request to remove web tag {}", tag);
		return service.removeWebTag(tag);
	}

	@RequestMapping(method = RequestMethod.GET, value = "reload")
	public boolean reload() throws IOException, URISyntaxException {
		LOG.info("Request to reload cache");
		service.clearCache();
		return true;
	}

	@RequestMapping(method = RequestMethod.GET, value = "location/reload")
	public boolean updateLocationData() throws IOException, URISyntaxException {
		LOG.info("Request to reload cache");
		service.loadLocationCache();
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "redirect/links/add")
	public boolean addZomatoRedirectLinks(@RequestBody List<UnitLinkMapping> mappings) throws NeoMetadataException {
		LOG.info("Request to reload cache");
		service.addRedirectLinkMappings(mappings);
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "redirect/link/get")
	public UnitLinkMapping addZomatoRedirectLinks(@RequestBody Integer unitId) {
		LOG.info("Request to reload cache");
		return service.getRedirectLinkMapping(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "redirect/links/get")
	public List<UnitLinkMapping> getZomatoRedirectLinks() {
		LOG.info("Request to reload cache");
		return service.getRedirectLinkMappings();
	}

	@RequestMapping(method = RequestMethod.POST, value = "units/add")
	public List<UnitDetail> addCityUnits(@RequestBody List<UnitDetail> units) {
		LOG.info("Request to add unit detail for google location");
		return service.addCityUnits(units);
	}

	@RequestMapping(method = RequestMethod.GET, value = "units/get")
	public List<UnitDetail> getCityUnits(@RequestParam(required = false) String city) {
		LOG.info("Request to get city units");
		return service.getCityUnits(city);
	}

	@RequestMapping(method = RequestMethod.POST, value = "redirect/track")
	public void trackZomatoRedirect(@RequestBody RedirectTracking request) {
		LOG.info("Request to track redirect");
		service.trackZomatoRedirect(request);
	}
}
