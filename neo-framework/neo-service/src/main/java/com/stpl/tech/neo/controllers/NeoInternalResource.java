package com.stpl.tech.neo.controllers;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_CUSTOMER_INTERNAL_ROOT_CONTEXT;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.neo.core.service.CRMService;
import com.stpl.tech.neo.domain.model.LaunchOfferData;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ WEB_CUSTOMER_INTERNAL_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/c'
public class NeoInternalResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(NeoInternalResource.class);

	@Autowired
	private CRMService customerService;

	@RequestMapping(value = "mcai")
	public boolean markCustomersAsInternals() {
		LOG.info("Request to Mark Customers as internals");
		return customerService.markAllInternalCustomers();
	}

	@RequestMapping(value = "sclo")
	public LaunchOfferData cafeLaunchOffer(@RequestBody LaunchOfferData input) {
		LOG.info("Request to sign up for launch offer " + input);
		return customerService.cafeLaunchOffer(input);
	}

	@RequestMapping(value = "scfio")
	public LaunchOfferData cafeFreeItemOffer(@RequestBody LaunchOfferData input) {
		LOG.info("Request to sign up for free Item offer " + input);
		return customerService.cafeFreeItemOffer(input);
	}

	@RequestMapping(value = "tgr")
	public String tokenGenerationRequest(@RequestBody String input) {
		LOG.info("Request to generate token for launch offer " + input);
		return customerService.generateToken(input);
	}

}