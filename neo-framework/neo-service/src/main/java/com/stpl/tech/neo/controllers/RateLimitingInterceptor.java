package com.stpl.tech.neo.controllers;

import java.util.Optional;

import javax.annotation.PreDestroy;

import com.stpl.tech.neo.core.RateLimiterListType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RateLimitingInterceptor {

	private static final Logger LOG = LoggerFactory.getLogger(RateLimitingInterceptor.class);

	@Autowired
	private RateLimitingExecutor executor;

	public boolean

	checkForClient(String clientId, RateLimiterListType type) {
		if (clientId == null) {
			return true;
		}
		Optional<RateLimiter> rateLimiter = getRateLimiter(clientId, type);
		boolean allowed = rateLimiter.get().tryAcquire();
		if (!allowed) {
			LOG.info(
					"Blocked request from {} rate limiter details availablePermits: {} , drainPermits: {} ,getQueueLength: {} ,hasQueuedThreads: {} ",
					clientId, rateLimiter.get().getSemaphore().availablePermits(),
					rateLimiter.get().getSemaphore().drainPermits(), rateLimiter.get().getSemaphore().getQueueLength(),
					rateLimiter.get().getSemaphore().hasQueuedThreads());
		} else {
			LOG.info("Received request from {}", clientId);

		}
		return allowed;
	}

	private Optional<RateLimiter> getRateLimiter(String clientId, RateLimiterListType type) {
		if (executor.getLimitersByType(type.value()).containsKey(clientId)) {
			return executor.getLimitersByType(type.value()).get(clientId);
		} else {
			synchronized (clientId.intern()) {
				// double-checked locking to avoid multiple-reinitializations
				if (executor.getLimitersByType(type.value()).containsKey(clientId)) {
					return executor.getLimitersByType(type.value()).get(clientId);
				}

				Optional<RateLimiter> rateLimiter = createRateLimiter(clientId);

				executor.getLimitersByType(type.value()).put(clientId, rateLimiter);
				return rateLimiter;
			}
		}
	}

	private Optional<RateLimiter> createRateLimiter(String clientId) {
		return RateLimiter.create();
	}

	@PreDestroy
	public void destroy() {
	}

	public static void main(String[] args) throws Throwable {
		RateLimitingExecutor.getInstance();
		RateLimitingInterceptor interceptor = new RateLimitingInterceptor();
		for (int j = 0; j < 10; j++) {
			if (j > 0) {
				Thread.sleep(10000L);
			}
			for (int i = 0; i < 10; i++) {
				LOG.info("Interception Cient :" + i + "  "
						+ interceptor.checkForClient(i + "", RateLimiterListType.USER_GET_INFO)
						+ " attempt " + j);

			}
		}
		interceptor.finalize();
	}
}