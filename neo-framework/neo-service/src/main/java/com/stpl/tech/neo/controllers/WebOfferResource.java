package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.WebOfferException;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.NeoOfferService;
import com.stpl.tech.neo.core.service.NeoOrderService;
import com.stpl.tech.neo.domain.model.CampaignDetailResponse;
import com.stpl.tech.neo.domain.model.mongo.GiftCardOffer;
import com.stpl.tech.neo.domain.model.mongo.OfferDetail;
import com.stpl.tech.neo.domain.model.mongo.SignUpOfferRequest;
import com.stpl.tech.neo.domain.model.mongo.WebOfferOrder;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_OFFER_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ WEB_OFFER_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/woff'
public class WebOfferResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(WebOfferResource.class);

	@Autowired
	private NeoOfferService offerService;

	@Autowired
	private NeoOrderService orderService;

	// apply coupon
	@RequestMapping(value = "a")
	public WebOfferOrder checkout(@RequestBody WebOfferOrder woo) throws WebOrderException {
		LOG.info("Request to apply order for cart {}", JSONSerializer.toJSON(woo));
		return offerService.applyOffer(woo);
	}

	// apply chaayos cash
	@RequestMapping(value = "cc")
	public WebOfferOrder chaayosCash(@RequestBody WebOfferOrder woo) throws WebOrderException {
		LOG.info("Request to apply chaayos cash for cart {}", JSONSerializer.toJSON(woo));
		try {
			return offerService.applyChaayosCashViaKettle(woo);
		} catch (Exception e) {
			LOG.error("Exception occured while apply chaayos cash {}", e);
		}
		return null;
	}

	@RequestMapping(value = "god")
	public List<OfferDetail> getOfferDetails(@RequestParam int unitId) {
		LOG.info("Request to getOfferDetails for unit " + unitId);
		List<GiftCardOffer> giftCardOffers = orderService.giftCardOffer(unitId);
		List<OfferDetail> offerDetails = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(giftCardOffers)){
			offerDetails = offerService.getOfferDetails(true);
			for(GiftCardOffer giftCardOffer : giftCardOffers){
				for(OfferDetail offerDetail : offerDetails){
					//LOG.info("offerDetail.getOriginalPrice() " + offerDetail.getOriginalPrice());
					//LOG.info("giftCardOffer.getDenomination() " + giftCardOffer.getDenomination());
					if(offerDetail.getOriginalPrice().compareTo(giftCardOffer.getDenomination()) == 0){
						BigDecimal originalPrice = giftCardOffer.getDenomination();
						BigDecimal offerPercent = giftCardOffer.getOffer();
						BigDecimal offerPrice = originalPrice.multiply(offerPercent).setScale(2, BigDecimal.ROUND_HALF_UP)
								.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
						offerDetail.setOfferPrice(offerPrice);
					}
				}
			}
		}
		return offerDetails;
	}

	@RequestMapping(value = "sup")
	public boolean availSignUpOffer(@RequestBody SignUpOfferRequest request)throws WebOfferException {
		LOG.info("Request to redeemSignUpOffer for customer " + JSONSerializer.toJSON(request));
		try {
			if(request != null && request.getCustomerId() != null && request.getCustomerName() !=null && request.getProduct() != null &&
					request.getTimeOfDelivery() != null && request.getDateOfDelivery() != null && request.getCompleteAddress() != null &&
					request.getCity() != null && request.getPinCode() != null){
				return offerService.availSignUpOfferViaKettle(request);
			}
		} catch (Exception e) {
			throw new WebOfferException("Exception occurred while availing sign up offer {}");
		}
		return false;
	}

	@RequestMapping(value = "campaign-detail")
	public CampaignDetailResponse getCampaignDetail(@RequestBody String token)throws WebOfferException {
		try {
			LOG.info("Fetching Campaign detail with token :: {}",token);
			CampaignDetailResponse response=offerService.getCampaignDetail(token);
			LOG.info("Found Campaign detail :: {}",JSONSerializer.toJSON(response));
			return  response;
		} catch (Exception e) {
			throw new WebOfferException("Exception occurred while while fetching campaign detail for token");
		}
	}

}
