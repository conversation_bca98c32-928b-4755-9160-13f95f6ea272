package com.stpl.tech.neo.controllers;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT;

import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.RedirectView;

import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.WebPaymentService;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.RazorPayPaymentResponse;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.util.JSONSerializer;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT) // 'v1/wpe'
public class WebPaymentExternalResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(WebPaymentExternalResource.class);

	@Autowired
	private WebPaymentService service;

	@Autowired
	private EnvironmentProperties env;

	// update API
	@RequestMapping(value = "rco", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	public RedirectView checkoutRazorPayPayment(HttpServletResponse httpServletResponse,
			@ModelAttribute("razorpay_payment_id") final String paymentId, @ModelAttribute("error") Object error,
			HttpServletResponse response) throws Exception {
		LOG.info("Request to create object for razor pay " + paymentId);
		if (error != null) {
			LOG.info("Error Data " + JSONSerializer.toJSON(error));
		}

		URIBuilder builder = new URIBuilder(env.getRazorPayRedirectUrl());
		if (paymentId != null && paymentId.trim().length() > 0) {
			boolean status = false;
			String responseText = null;
			RazorPayPaymentResponse updatedPaymentResponse = null;
			try {
				LOG.info("Request to get payment details for request id " + paymentId);
				updatedPaymentResponse = service.fetchRazorPayPayment(paymentId);
				responseText = JSONSerializer.toJSON(updatedPaymentResponse);
				LOG.info("Received Payment Response Data " + responseText);
				status = service.processPayment(updatedPaymentResponse);
				LOG.info("Received Payment Response Status " + status);

			} catch (Exception e) {
				LOG.error("Error while processing payment request in kettle", e);
				NeoUtil.slackIt(env.getEnvType(), e);
			}
			if (status) {
				LOG.info("Payment Successfull captured in kettle " + responseText);
				builder.addParameter("orderId", updatedPaymentResponse.getOrderId());
				builder.addParameter("txnId", updatedPaymentResponse.getTransactionId());
			} else {
				LOG.info("Payment Failed " + responseText);
				if (updatedPaymentResponse != null) {
					builder.addParameter("error", updatedPaymentResponse.getOrderId());
					builder.addParameter("reason", "Failed while validating in Transaction System");
				} else {
					builder.addParameter("error", "");
					builder.addParameter("error", "Failed while fetching from Transaction System");

				}
			}
			// create order in neo and kettle
		} else {
			String errorString = JSONSerializer.toJSON(error);
			LOG.info("Adding error cookie " + errorString);
			builder.addParameter("error", "");
			builder.addParameter("reason", "Payment Failed At the Partner Level");
		}

		return new RedirectView(builder.toString());

	}


	// update API
	@RequestMapping(value = "pco", method = {RequestMethod.GET, RequestMethod.POST})
	public RedirectView checkoutPaytmPayPayment(HttpServletRequest request) throws Exception {

		final Map<String, String[]> parameterMap = request.getParameterMap();
		Map<String, String> parameters = parameterMap.keySet().stream()
				.collect(Collectors.toMap(s -> s, t -> parameterMap.get(t)[0]));
		String url = env.getKettleServiceBasePath()+ KettleServiceClientEndpoints.UPDATE_PAYTM_RESPONSE;
		Map<String,String> responseObject = WebServiceHelper.postWithAuth(url,env.getNeoClientToken(),parameters,Map.class);
		URIBuilder builder = new URIBuilder(env.getPaytmRedirectUrl());
		if(!responseObject.containsKey("error")){
			String orderId = responseObject.get("orderId");
			String txnId = responseObject.get("txnId");
			LOG.info("Payment Successful captured in kettle " + orderId);
			builder.addParameter("orderId", orderId);
			builder.addParameter("txnId", txnId);
		}else{
			String error = responseObject.get("error");
			String reason = responseObject.get("reason");
			LOG.info("Payment Failed in Kettle");
			builder.addParameter("error", error);
			builder.addParameter("reason", reason);
		}
		return new RedirectView(builder.toString());
	}


}
