package com.stpl.tech.neo.controllers;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.neo.core.exception.CheckoutFailureException;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.WebPaymentService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.domain.model.AGSPaymentResponse;
import com.stpl.tech.neo.domain.model.CartCheckoutRequest;
import com.stpl.tech.neo.domain.model.EzetapPaymentResponse;
import com.stpl.tech.neo.domain.model.PaymentStatusChangeRequest;
import com.stpl.tech.neo.domain.model.RazorPayPaymentResponse;
import com.stpl.tech.neo.domain.model.mongo.AGSCreateRequest;
import com.stpl.tech.neo.domain.model.mongo.AGSPaymentCMResponse;
import com.stpl.tech.neo.domain.model.mongo.EzetapCreateRequest;
import com.stpl.tech.neo.domain.model.mongo.PaytmCreateRequest;
import com.stpl.tech.neo.domain.model.mongo.PaytmPaymentStatus;
import com.stpl.tech.neo.domain.model.mongo.RazorPayCreateRequest;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.net.URISyntaxException;
import java.security.SignatureException;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_PAYMENT_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ WEB_PAYMENT_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/wp'
public class WebPaymentResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(WebPaymentResource.class);
	private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
	@Autowired
	private WebPaymentService service;

	@Autowired
	private EnvironmentProperties env;

	// create payment via razor pay
	@RequestMapping(value = "c")
	@Deprecated
	public Object paymentCheckout(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Razor Pay Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createOrderPaymentRequest(request, WebOrderingConstants.NEO_SERVICE);
	}

	// create payment via Paytm
	@RequestMapping(value = "pc")
	public PaytmCreateRequest payTMPaymentCheckout(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Paytm Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createPaytmOrderPaymentRequest(request, WebOrderingConstants.NEO_SERVICE);
	}

	// create payment via razor pay
	@RequestMapping(value = "rc")
	public RazorPayCreateRequest razorPayPaymentCheckout(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Razor pay Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createRazorPayOrderPaymentRequest(request, WebOrderingConstants.NEO_SERVICE);
	}

	// create payment via ezetap for kiosk service
	@RequestMapping(value = "ezc")
	public EzetapCreateRequest ezetapPaymentCheckout(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Razor pay Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createEzetapOrderPaymentRequest(request, WebOrderingConstants.KIOSK_SERVICE);
	}

	// update API
	@RequestMapping(value = "u")
	public boolean processRazorPayPayment(@RequestBody final RazorPayPaymentResponse request) throws Exception {
		LOG.info("Response received for Payment for order " + JSONSerializer.toJSON(request));
		return service.processPayment(request);
	}

	// update API
	@RequestMapping(value = "eu")
	public boolean processEzetapPayment(@RequestBody final EzetapPaymentResponse request) throws Exception {
		LOG.info("Response received for Payment for order " + JSONSerializer.toJSON(request));
		return service.processEzetapPayment(request);
	}

	// update API
	@RequestMapping(value = "cc")
	public boolean cancelPayment(@RequestBody final PaymentStatusChangeRequest request) throws Exception {
		LOG.info("Cancellation request for the payment with receipt id  " + JSONSerializer.toJSON(request));
		return service.cancelPayment(request);
	}

	// update API
	@RequestMapping(value = "fc")
	public boolean failurePayment(@RequestBody final PaymentStatusChangeRequest request) throws Exception {
		LOG.info("Failure Update request for the payment with receipt id  " + JSONSerializer.toJSON(request));
		return service.paymentFailure(request);
	}

	// get payment key
	@RequestMapping(method = RequestMethod.GET, value = "pk")
	public String getRazorPayKey() throws URISyntaxException {
		LOG.info("Request to get RazorPay Payment Key ");
		return env.getRazorPayPaymentSecretKey();
	}

	// stamp payment from razorpay
	@RequestMapping(method = RequestMethod.POST, value = "st")
	public void stampRazorPayUpdate(@RequestHeader(value = "X-Razorpay-Signature") String signature,
			@RequestBody final Object data) throws URISyntaxException {
		String request = JSONSerializer.toJSON(data);
		LOG.info("Webhook request to stamp the request from razorpay \n"+ request);
		if (signature == null) {
			SlackNotificationService.getInstance().sendNotification(env.getEnvType(),"Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(env.getEnvType()),
					"No Signature Found in Payment Stamping from Razor Pay for \n" + request);
			return;
		}
		/*
		 * try { if (!signature.equals(calculateRFC2104HMAC(request,
		 * env.getRazorPaySignatureSecretKey()))) {
		 * SlackNotificationService.getInstance().send(env.getEnvType(),
		 * "Error is validating stamping request from Razor Pay for signatue : "
		 * + signature + " and request\n" + request); return; } } catch
		 * (Exception e) { NeoUtil.slackIt(env.getEnvType(), e,
		 * "Exception in validating stamping request from Razor Pay for signatue : "
		 * + signature + " and request\n" + request); return; }
		 */
		try {
			service.stampPaymentUpdate(data);
		} catch (WebOrderException e) {
			NeoUtil.slackIt(env.getEnvType(), e,
					"Error in sending stamping request to kettle from Razor Pay for signatue : " + signature
							+ " and request\n" + request);
			return;
		}

	}
	
	// stamp payment from razorpay
	@RequestMapping(method = RequestMethod.POST, value = "st/c")
	public void stampRazorPayUpdateForCovid(@RequestHeader(value = "X-Razorpay-Signature") String signature,
			@RequestBody final Object data) throws URISyntaxException {
		String request = JSONSerializer.toJSON(data);
		LOG.info("Webhook request to stamp the request from razorpay for standalone request \n" + request);
		if (signature == null) {
			SlackNotificationService.getInstance().sendNotification(env.getEnvType(), "Neo", null,SlackNotification.SYSTEM_ERRORS.getChannel(env.getEnvType()),
					"No Signature Found in Payment Stamping from Razor Pay for standalone request for\n" + request);
			return;
		}
		try {
			service.stampPaymentUpdateForCovid(data);
		} catch (WebOrderException e) {
			NeoUtil.slackIt(env.getEnvType(), e,
					"Error in sending stamping request to kettle from Razor Pay for signatue for standalone request: "
							+ signature + " and request\n" + request);
			return;
		}

	}

	@RequestMapping(value = "cpqr")
	public PaytmCreateRequest createPayTMQRCodeIdForKIOSK(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Paytm Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createPayTMQRCodeIdForKIOSK(request, WebOrderingConstants.KIOSK_SERVICE);
	}

	@RequestMapping(value = "cpps")
	public PaytmPaymentStatus checkPaytmPaymentStatus(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Paytm Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.isPaytmPaymentCompleted(request, WebOrderingConstants.KIOSK_SERVICE);
	}

	// create payment via AGS for kiosk service
	@RequestMapping(value = "agsc")
	public AGSCreateRequest AGSPaymentCheckout(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Razor pay Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createAGSOrderPaymentRequest(request, WebOrderingConstants.KIOSK_SERVICE);
	}

	// update API
	@RequestMapping(value = "agsu")
	public boolean processAGSPayment(@RequestBody final AGSPaymentResponse request) throws Exception {
		LOG.info("Response received for Payment for order " + JSONSerializer.toJSON(request));
		return service.processAGSPayment(request);
	}

	/**
	 * Check the status of AGS card payment.
	 * @param externalOrderId
	 * @return
	 * @throws WebOrderException
	 * @throws URISyntaxException
	 * @throws CheckoutFailureException
	 */
	@RequestMapping(value = "cagsps")
	public AGSPaymentCMResponse checkAGSPaymentStatus(@RequestBody final String externalOrderId)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Check AGS Payment Request received for external order Id {}", externalOrderId);
		return service.isAGSPaymentCompleted(externalOrderId);
	}

	@RequestMapping(value = "cpupi")
	public PaytmCreateRequest createPayTMUPIForKIOSK(@RequestBody final CartCheckoutRequest request)
			throws WebOrderException, URISyntaxException, CheckoutFailureException {
		LOG.info("Paytm Payment Request received for cart Id {}", JSONSerializer.toJSON(request));
		return service.createPayTMUPIForKIOSK(request, WebOrderingConstants.KIOSK_SERVICE);
	}

	/**
	 * Computes RFC 2104-compliant HMAC signature. * @param data The data to be
	 * signed.
	 * 
	 * @param data
	 *            The signing key.
	 * @return The Base64-encoded RFC 2104-compliant HMAC signature.
	 * @throws SignatureException
	 *             when signature generation fails
	 */
	public static String calculateRFC2104HMAC(String data, String secret) throws SignatureException {
		String result;
		try {

			// get an hmac_sha256 key from the raw secret bytes
			SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);

			// get an hmac_sha256 Mac instance and initialize with the signing
			// key
			Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
			mac.init(signingKey);

			// compute the hmac on input data bytes
			byte[] rawHmac = mac.doFinal(data.getBytes());

			// base64-encode the hmac
			result = DatatypeConverter.printHexBinary(rawHmac).toLowerCase();

		} catch (Exception e) {
			throw new SignatureException("Failed to generate HMAC : " + e.getMessage());
		}
		return result;
	}

}
