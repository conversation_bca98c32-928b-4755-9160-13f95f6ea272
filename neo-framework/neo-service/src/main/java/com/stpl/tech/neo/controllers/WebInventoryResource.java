package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.service.WebInventoryService;
import com.stpl.tech.neo.domain.model.InventoryInfo;
import com.stpl.tech.redis.domain.model.IdNameList;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_INVENTORY_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ WEB_INVENTORY_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/win'
public class WebInventoryResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(WebInventoryResource.class);

	@Autowired
	private WebInventoryService service;

	// get unit inventory
	@RequestMapping(value = "u")
	public Map<Integer, Integer> getUnitInventory(@RequestBody int unitId) {
		LOG.info("Request to get Inventory for unit {}", unitId);
		return service.getUnitInventory(unitId);
	}

	// get unit inventory
	@RequestMapping(value = "u/l")
	public Map<Integer, InventoryInfo> getLiveUnitInventory(@RequestBody int unitId) {
		LOG.info("Request to get Inventory for unit {}", unitId);
		return service.getLiveUnitInventory(unitId);
	}

	// get unit product inventory
	@RequestMapping(value = "u/p")
	public Map<Integer, Integer> getUnitInventoryStatus(@RequestBody IdNameList list) {
		LOG.info("Request to get Inventory for unit and products {}", JSONSerializer.toJSON(list));
		return service.getUnitInventoryStatus(list);
	}

}