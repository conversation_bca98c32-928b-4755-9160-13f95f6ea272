package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.NeoOrderService;
import com.stpl.tech.neo.core.service.SlackService;
import com.stpl.tech.neo.domain.model.CustomerOrderPage;
import com.stpl.tech.neo.domain.model.mongo.GiftCardOffer;
import com.stpl.tech.neo.domain.model.mongo.OrderStatus;
import com.stpl.tech.neo.domain.model.mongo.OrderView;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_ORDER_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ WEB_ORDER_ROOT_CONTEXT, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/word'
public class WebOrderResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(WebOrderResource.class);

	@Autowired
	private NeoOrderService orderService;

	@Autowired
	private SlackService slackService;

	// customer order search on web order id
	@RequestMapping(value = "s/w")
	public OrderView searchByWebId(@RequestBody final String webOrderId) throws WebOrderException {
		LOG.info("Request to search with web order id {}", webOrderId);
		return orderService.searchByWebId(webOrderId);
	}

	// customer order search on kettle order id
	@RequestMapping(value = "s/id")
	public OrderView searchByGeneratedOrderId(@RequestBody final String orderId) throws WebOrderException {
		LOG.info("Request to search with generated order id {}", orderId);
		return orderService.search(orderId);
	}

	// list of customer order search
	@RequestMapping(value = "s/c")
	public CustomerOrderPage searchCustomerOrders(@RequestBody final CustomerOrderPage page) throws WebOrderException {
		LOG.info("Request to search customer orders {}", JSONSerializer.toJSON(page));
		return orderService.searchCustomerOrders(page);
	}

	// Slack data for a cart
	@RequestMapping(value = "slk/cid")
	public boolean slackDataByCartId(@RequestBody String cartId) {
		LOG.info("Request to slack cart details {}", cartId);
		slackService.slackDataByCartId(cartId);
		return true;
	}

	// Slack data for a order
	@RequestMapping(value = "slk/oid")
	public boolean slackDataByOrderId(@RequestBody String orderId) {
		LOG.info("Request to slack cart details {}", orderId);
		slackService.slackDataByOrderId(orderId);
		return true;
	}
	
	// customer order status search on kettle order id
	@RequestMapping(value = "s/id/s")
	public OrderStatus searchOrderStatusByGeneratedOrderId(@RequestBody final String orderId) throws WebOrderException {
		LOG.info("Request to search order status with generated order id {}", orderId);
		return orderService.fetchOrderStatus(orderId);
	}

	// is first order for customer
	@RequestMapping(value = "ifo")
	public boolean isFirstOrderForCustomer(@RequestBody final String customerId) throws WebOrderException {
		LOG.info("Request to to check is first order for customer {}", customerId);
		return orderService.isFirstOrderForCustomer(customerId);
	}

	// get offer on gift card
	@RequestMapping(value = "gco")
	public List<GiftCardOffer> giftCardOffer(@RequestBody int unitId) throws WebOrderException {
		LOG.info("Request to get gift card offer for unit: {}", unitId);
		return orderService.giftCardOffer(unitId);
	}
}
