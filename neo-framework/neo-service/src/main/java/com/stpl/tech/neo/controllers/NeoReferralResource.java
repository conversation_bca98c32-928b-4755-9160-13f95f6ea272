package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoReferralService;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.ReferralRequest;
import com.stpl.tech.neo.domain.model.ReferralResponse;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.REFERRAL_RESOURCE_ROOT_CONTEXT;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ REFERRAL_RESOURCE_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/ref'
public class NeoReferralResource {

	private static final Logger LOG = LoggerFactory.getLogger(NeoReferralResource.class);

	@Autowired
	private NeoReferralService refService;

	@Autowired
	private EnvironmentProperties env;

	/**
	 * submit referral
	 * 
	 * @param request
	 * @return
	 * @throws WebOrderException
	 */
	@RequestMapping(value = "s")
	public ReferralResponse submit(@RequestBody ReferralRequest request) throws WebOrderException {
		LOG.info("Referral Submit Request for {} ", JSONSerializer.toJSON(request));
		return refService.submit(request);
	}

	/**
	 * validate code and customer
	 * 
	 * @param request
	 * @return
	 * @throws WebOrderException
	 */
	@RequestMapping(value = "v")
	public ReferralResponse validate(@RequestBody ReferralRequest request) throws WebOrderException {
		LOG.info("Referral Validate Request for {} ", JSONSerializer.toJSON(request));
		return refService.validate(request);
	}

	/**
	 * re-send validation OTP
	 * 
	 * @param contact
	 * @return
	 * @throws WebOrderException
	 */
	@RequestMapping(value = "r")
	public OTPResponse resendOTP(@RequestBody String contact) throws WebOrderException {
		LOG.info("Request to resend OTP for {}", contact);
		return refService.resendOTP(contact);
	}

	/*
	 *
	 */
	@RequestMapping(value = "tgr")
	public String tokenGenerationRequest(@RequestBody String input) {
		LOG.info("Request to generate token for referral " + input);
		return refService.generateToken(input);
	}

	@RequestMapping(value = "omt")
	public String generateToken(@RequestBody String input) {
		try {
			LOG.info("Request to generate token order " + input);
			return refService.generateTokenForOrderManagement(input);
		}catch (Exception e){
			LOG.error("Error while creating token for order management for url {}",input,e);
		}
		return  null;
	}

	@RequestMapping(value = "order-detail-feedback")
	public String getOrderDetailForFeedback(@RequestBody String token){
		LOG.info("In Neo service");
		LOG.info("Request to fetch order detail for token == {}",token);
		return refService.getOrderDetailForFeedback(token);
	}

	@RequestMapping(value = "submit-feedback")
	public Boolean submitFeedback(@RequestBody Object orderDetail){
		return refService.submitFeedback(orderDetail);
	}
	/*
	 *
	 */
	@RequestMapping(value = "ssoc")
	public Boolean sendOfferToCustomer(@RequestBody SMSRequest input) {
		LOG.info("Request to send offer to customer" + input);
		input.setCreationTime(AppUtils.getCurrentTimestamp());
		input.setSendNotification(AppUtils.isProd(env.getEnvType()));
		return refService.sendSMS(input);
	}

}
