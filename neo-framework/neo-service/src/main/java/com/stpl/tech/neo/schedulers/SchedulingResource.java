package com.stpl.tech.neo.schedulers;

import java.net.URISyntaxException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.neo.core.service.CartService;
import com.stpl.tech.neo.core.service.KettleOrderService;
import com.stpl.tech.neo.core.service.NeoMetadataService;

@Component
public class SchedulingResource {

	private static final Logger LOG = LoggerFactory.getLogger(SchedulingResource.class);

	@Autowired
	private CartService cartService;

	@Autowired
	private KettleOrderService kettleOrderService;

	@Autowired
	private NeoMetadataService metaService;

	// @Scheduled(cron = "0 0 24 * * *", zone = "GMT+05:30")
	public void sessionExpirationSchedule() {
		LOG.info("Expiring Sessions");
		// Expire Sessions here
	}

	// @Scheduled(fixedRate = 30000)
	@Scheduled(cron = "0 5 0,4 * * *", zone = "GMT+05:30")
	public void orderStatusUpdateScheduleDineInAndTakeAway() {
		LOG.info("Order Status update Started for Dine In and Take away Orders");
		List<String> l = cartService.getUnsettledOrders();
		requestToPublish(l);
	}

	// @Scheduled(fixedRate = 30000)
	@Scheduled(cron = "0 10 0,4 * * *", zone = "GMT+05:30")
	public void orderStatusUpdateScheduleDelivery() {
		LOG.info("Order Status update Started for Delivery Orders");
		List<String> l = cartService.getUndeliveredOrders();
		requestToPublish(l);
	}

	private void requestToPublish(List<String> l) {
		try {
			kettleOrderService.publishCurrentOrderStatus(l);
		} catch (Exception e) {
			LOG.error("Error in order Sync", e);
		}
	}

	@Scheduled(cron = "0 0 6,12 * * *", zone = "GMT+05:30")
	public void refreshCache() {
		LOG.info("Referesh cache");
		try {
			metaService.reloadCache();
		} catch (Exception e) {
			LOG.error("Error while refreshing Cache", e);
		}
	}

}
