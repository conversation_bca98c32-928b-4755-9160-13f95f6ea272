#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.        
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#
log.base.dir=/usr/share/tomcat8/logs/
spring.application.name=neo-service
monitoring.user-name=admin
monitoring.user-password=Chaayos12345

# Enable JavaMelody auto-configuration (optional, default: true)
javamelody.enabled=false
# Data source names to exclude from monitoring (optional, comma-separated)
#javamelody.excluded-datasources=secretSource,topSecretSource
# Enable monitoring of Spring services and controllers (optional, default: true)
javamelody.spring-monitoring-enabled=false
# Initialization parameters for JavaMelody (optional)
# See: https://github.com/javamelody/javamelody/wiki/UserGuide#6-optional-parameters
#    log http requests:
javamelody.init-parameters.log=true
#    to exclude images, css, fonts and js urls from the monitoring:
javamelody.init-parameters.url-exclude-pattern=(/webjars/.*|/css/.*|/images/.*|/fonts/.*|/js/.*)
#    to aggregate digits in http requests:
# javamelody.init-parameters.http-transform-pattern: \d+
#    to add basic auth:
javamelody.init-parameters.authorized-users=${monitoring.user-name}:${monitoring.user-password}
#    to change the default storage directory:
javamelody.init-parameters.storage-directory==${log.base.dir}/${spring.application.name}/javamelody
#    to change the default "/monitoring" path:
# javamelody.init-parameters.monitoring-path=/admin/performance

management.endpoints.web.exposure.include=*
javamelody.management-endpoint-monitoring-enabled=true
spring.main.allow-bean-definition-overriding=true

# Enable response compression
server.compression.enabled=true
# The comma-separated list of mime types that should be compressed
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
# Compress the response only if the response size is at least 1KB
server.compression.min-response-size=1024

#management.server.servlet.contextPath=/${spring.application.name}-management
log.location=${log.base.dir}/${spring.application.name}/log
server.servlet.context-path=/${spring.application.name}
spring.mvc.servlet.path=/rest

server.port=9692


environment.type=DEV

default.customer.id=5

#mongo configuration
spring.data.mongodb.database=neo_dev
spring.data.mongodb.uri=mongodb://neousrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000
spring.data.mongodb.auto-connect-retry=true

#Neo Redis Source
redis.host=localhost
redis.port=8181
redis.db.index=1
redis.pass=R3d15D3V

redis.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw

base.path.kettle.service=http://dev.kettle.chaayos.com:9595
base.path.master.service=http://dev.kettle.chaayos.com:9595
base.path.kettle.crm=http://dev.kettle.chaayos.com:9595
base.path.kettle.rekognition=http://dev.kettle.chaayos.com:9595
base.path.truecaller=http://localhost:8080

paytm.callback.url=https://dev-staging.chaayos.com/neo-service/rest/v1/wpe/pco
paytm.redirect=https://dev-staging.chaayos.com/payProcess
razorPay.redirect=https://dev-staging.chaayos.com/payProcess

razorpay.signature.secret.key=Chaayos@12345
razorpay.payment.secret.key=rzp_test_5IXQfPSH0NO8Bk


send.otp=true
aws.queue.region=EU_WEST_1
request.log=false

report.service.rest.url=http://localhost:8080/report-service/rest/v1/
report.service.auth.internal=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6InJlcG9ydC1zZXJ2aWNlIiwiZW52VHlwZSI6IlNQUk9EIiwicGFydG5lcklkIjoyMywicGFzc0NvZGUiOiJWNDdMVDk3R0IzMjNFRkQiLCJpYXQiOjE2NjU3MjcxODF9.xQeP4Utvhv-mHeM-nAWUGFH8OKQLuT1J0TsSkAHwCy4