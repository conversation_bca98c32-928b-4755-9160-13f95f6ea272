// server.js
var express = require('express');
var path = require('path');
var compression = require('compression');
var request = require('request');
var helmet = require('helmet');
const fs = require("fs");

var app = express();
const indexPath  = path.join(__dirname, 'dist/index.html');
app.use(compression());
//for security
app.use(helmet());
app.disable('x-powered-by');

const env= "LOCAL";
const metaDefaultDesc = "Chaayos - a Chai adda to order chai online with 1200 personalised chai options. " +
    "Get the authentic taste delivered in Delhi, Gurgaon, Noida and Mumbai."
const metaDefaultTitle = "Chaayos - Order Chai Online - Experiments With Chai";
app.getBaseUrl=function (){
    if(env === "LOCAL"){
        return "http://localhost:8080";
    }else if(env === "DEV"){
        return "http://dev.kettle.chaayos.com:8080";
    }else if(env === "STAGE"){
        return "http://stage.kettle.chaayos.com:8080";
    }else{
        return "https://relax.chaayos.com";
    }
}

app.getToken = function(data, callback){
    var returnData = null;
    var url = app.getBaseUrl() + "/neo-service/rest/v1/cn/tgr";
    var options = {
        url:url,
        method:"POST",
        body:JSON.stringify(data),
        headers: {"Content-type":"application/json"}
    };
    request(options, function (error, response, body) {
        console.log('error:', error);
        console.log('statusCode:', response && response.statusCode);
        if(body!=undefined && typeof callback == "function"){
            return callback(body);
        }
    });
};

app.getRefToken = function(data, callback){
    var returnData = null;
    var url = app.getBaseUrl() + "/neo-service/rest/v1/ref/tgr";
    var options = {
        url:url,
        method:"POST",
        body:JSON.stringify(data),
        headers: {"Content-type":"application/json"}
    };
    request(options, function (error, response, body) {
        console.log('error:', error);
        console.log('statusCode:', response && response.statusCode);
        if(body!=undefined && typeof callback == "function"){
            return callback(body);
        }
    });
};

app.getOrderManagementToken = function(data, callback){
    var returnData = null;
    var url = app.getBaseUrl() + "/neo-service/rest/v1/ref/omt";
    var options = {
        url:url,
        method:"POST",
        body:JSON.stringify(data),
        headers: {"Content-type":"application/json"}
    };
    request(options, function (error, response, body) {
        console.log('error:', error);
        console.log('statusCode:', response && response.statusCode);
        if(body!=undefined && typeof callback == "function"){
            return callback(body);
        }
    });
};

app.getOrderDetail=function (data,callback){
    var url = app.getBaseUrl() + "/neo-service/rest/v1/ref/order-detail-feedback";
    var urlx = require('url');
    var url_parts = urlx.parse(data, true);
    var params = url_parts.query;
    console.log(params);
    var option = {
        url : url,
        method : "POST",
        body: JSON.stringify(params.token),
        headers: {"Content-type":"application/json"}
    };
    request(option, function (error, response, body) {
        console.log('error:', error);
        console.log('body: ',body);
        console.log('statusCode:', response && response.statusCode);
        // console.log(JSON.parse(body));
        if(body!=undefined && typeof callback == "function"){
            var x = JSON.parse(body);
            console.log('json body:',body);
            return callback(JSON.parse(body));
        }
    });
}

app.getCampaignDetails = function (data , callback){
    var url = app.getBaseUrl() + "/neo-service/rest/v1/woff/campaign-detail";
    var urlx = require('url');
    var url_parts = urlx.parse(data, true);
    var params = url_parts.query;
    console.log(params);
    var option = {
        url : url,
        method : "POST",
        body: JSON.stringify(params.token),
        headers: {"Content-type":"application/json"}
    };
    request(option, function (error, response, body) {
        console.log('error:', error);
        console.log('body: ',body);
        console.log('statusCode:', response && response.statusCode);
        // console.log(JSON.parse(body));
        if(body!=undefined && typeof callback == "function"){
            return callback(JSON.parse(body),response.statusCode);
        }
    });
}

app.get('/chai4change', function (req, res) {
    //console.log(req);
    res.sendFile(path.join(__dirname, 'dist/chai4change.html'));
});

app.get('/hhm', function (req, res) {
    //console.log(req);
    res.sendFile(path.join(__dirname, 'dist/hhm.html'));
});

app.get('/platina', function (req, res) {
    console.log(req.protocol + '://' + req.hostname + req.url);
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        console.log(token);
        res.cookie('token', token.substr(1, token.length-2), {});
        console.log(token.substr(1, token.length-2));
        res.sendFile(path.join(__dirname, 'dist/newLaunch.html'));
    });
});

app.get('/greenpark', function (req, res) {
    console.log(req.protocol + '://' + req.hostname + req.url);
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        console.log(token);
        res.cookie('token', token.substr(1, token.length-2), {});
        console.log(token.substr(1, token.length-2));
        console.log(__dirname);
        // res.render(__dirname+ 'dist/newLaunchGreenPark.html',{name:name})
        res.sendFile(path.join(__dirname, 'dist/newLaunchGreenPark.html'));
    });
});

app.get('/lulumall', function (req, res) {
    console.log(req.protocol + '://' + req.hostname + req.url);
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        console.log(token);
        res.cookie('token', token.substr(1, token.length-2), {});
        console.log(token.substr(1, token.length-2));
        console.log(__dirname);
        res.sendFile(path.join(__dirname, 'dist/newLaunchLuluMall.html'));
    });
});

app.get('/sector50', function (req, res) {
    console.log(req.protocol + '://' + req.hostname + req.url);
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        console.log(token);
        res.cookie('token', token.substr(1, token.length-2), {});
        console.log(token.substr(1, token.length-2));
        res.sendFile(path.join(__dirname, 'dist/launchNoidaSector50.html'));
    });
});

app.get('/chdsector8', function (req, res) {
  console.log(req.protocol + '://' + req.hostname + req.url);
  app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
      console.log(token);
      res.cookie('token', token.substr(1, token.length-2), {});
      console.log(token.substr(1, token.length-2));
      res.sendFile(path.join(__dirname, 'dist/newLaunchSector8Chandigarh.html'));
  });
});

app.get('/bandra', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBandra.html'));
    });
});

app.get('/bandraturnerroad', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBandraTurner.html'));
    });
});
app.get('/churchgate', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchChurchgate.html'));
    });
});
app.get('/eastpatelnagar', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchEastPatelNagar.html'));
    });
});
app.get('/onebkc', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchOneBkc.html'));
    });
});
app.get('/arssmallpaschimvihar', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchARSSMallPaschimVihar.html'));
    });
});
app.get('/sec15Faridabad', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchSector15Faridabad.html'));
    });
});
app.get('/irisbroadwaysec85gurugram', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchIrisBroadway.html'));
    });
});


app.get('/kamlamills', function (req, res) {
  app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
      res.cookie('token', token.substr(1, token.length-2), {});
      res.sendFile(path.join(__dirname, 'dist/newLaunchKamalaMills.html'));
  });
});


app.get('/panchpakhadithane', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchPanchpakhadiThane.html'));
    });
});

app.get('/kalyaninagar', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKalyaniNagar.html'));
    });
});

app.get('/balewali', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBalewali.html'));
    });
});

app.get('/bandrachapelrd2mum', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBandraChapelRd2Mum.html'));
    });
});

app.get('/sherepunjabandheri', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchSherePunjabAndheri.html'));
    });
});
app.get('/juhutarard', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchJuhuTara.html'));
    });
});
app.get('/jayangr', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchJayaNgr.html'));
    });
});
app.get('/jayanagar', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchJayaNagar.html'));
    });
});
// app.get('/phoenixpalladiummall', function (req, res) {
//     app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
//         res.cookie('token', token.substr(1, token.length-2), {});
//         res.sendFile(path.join(__dirname, 'dist/newLaunchPhoenixPalladiumMall.html'));
//     });
// });
app.get('/kalkaji', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKalkaji.html'));
    });
});
// app.get('/sahkarnagar', function (req, res) {
//     app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
//         res.cookie('token', token.substr(1, token.length-2), {});
//         res.sendFile(path.join(__dirname, 'dist/newLaunchSahkarNagar.html'));
//     });
// });
app.get('/preetvihar', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchPreetVihar.html'));
    });
});
app.get('/vileparle', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchVileParle.html'));
    });
});
app.get('/oneindiabulls', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchOneIndiaBulls.html'));
    });
});
app.get('/lotusskymark', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchLotusSkyMark.html'));
    });
});
app.get('/khargharnavimumbai', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKhargharNaviMumbai.html'));
    });
});

app.get('/kashmeregate', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKashmereGate.html'));
    });
});
app.get('/belapur', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBelapur.html'));
    });
});

app.get('/cunninghamroad', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchCunninghamRoad.html'));
    });
});

app.get('/reach3roads', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchReach3Road.html'));
    });
});
// app.get('/cyberhub', function (req, res) {
//     app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
//         res.cookie('token', token.substr(1, token.length-2), {});
//         res.sendFile(path.join(__dirname, 'dist/newLaunchCyberHub.html'));
//     });
// });
app.get('/ecoworld', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchEcoWorld.html'));
    });
});
// app.get('/pacificmall', function (req, res) {
//     app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
//         res.cookie('token', token.substr(1, token.length-2), {});
//         res.sendFile(path.join(__dirname, 'dist/newLaunchPacificMall.html'));
//     });
// });
app.get('/vegasmall', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchVegasMall.html'));
    });
});
app.get('/mantrisquaremall', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchMantriSquareMall.html'));
    });
});
app.get('/kammanhalli', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKammanhalli.html'));
    });
});

app.get('/chandivali', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchChandivali.html'));
    });
});

app.get('/powai', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchPowai.html'));
    });
});

app.get('/gk1', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/launchGk1.html'));
    });
});

app.get('/advantnoida', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/launchAdvant.html'));
    });
});

app.get('/advant', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/launchAdvant.html'));
    });
});

app.get('/prestigett', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/launchPrestige.html'));
    });
});

app.get('/janakpuri', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchJanakpuri.html'));
    });
});

app.get('/panchpakadi', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchPanchPakadi.html'));
    });
});

app.get('/vileparlemum', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchVileParleMum.html'));
    });
});

app.get('/kammanahalli', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKammanahalli.html'));
    });
});

app.get('/hsrlayout', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchHsrlayout.html'));
    });
});

app.get('/freechaidelivery', function (req, res) {
    res.redirect('https://stpltd.typeform.com/to/im9hmWYm');
});

/*app.get('/refer', function (req, res) {
    res.sendFile(path.join(__dirname, 'dist/refer.html'));
    //res.sendFile(path.join(__dirname, 'src/commons/refer.html'));
});*/

/*app.get('/signup', function (req, res) {
    app.getRefToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/signup.html'));
    });
});*/

app.get('/obbservSignup', function (req, res) {
    app.getRefToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/obbservSignup.html'));
    });
});

app.get('/feedback', function (req, res) {
    app.getRefToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        app.getOrderDetail(req.protocol + '://' + req.hostname + req.url, function (data){
            res.cookie('orderDetail',data,{});
            res.cookie('token', token.substr(1, token.length-2), {});
            res.sendFile(path.join(__dirname, 'dist/orderFeedbackForm.html'));
            console.log("data   ===== ",data);
        })
    });
});

app.get('/thankyou', function (req, res) {
    app.getRefToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/thankYouPageDefault.html'));
    });
});


app.get('/lbbOffer', function (req, res) {
    app.getRefToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/lbbOffer.html'));
    });
});

app.get('/khargarnavimum', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchKhargarNaviMum.html'));
    });
});

app.get('/preetvihardelhi', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchPreetViharDelhi.html'));
    });
});

app.get('/bhartiyamallblr', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBhartiyaMallBLR.html'));
    });
});

app.get('/luluglobalmall', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchLuluGlobalMall.html'));
    });
});

app.get('/omaxefaridabad', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchOmaxeFaridabad.html'));
    });
});

app.get('/bagmanewtc', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchBagmaneWTC.html'));
    });
});

app.get('/lotusisleskymarknoida', function (req, res) {
    app.getToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        res.cookie('token', token.substr(1, token.length-2), {});
        res.sendFile(path.join(__dirname, 'dist/newLaunchLotusIsleSkymarkNoida.html'));
    });
});


app.get('/signupOffer', function (req, res) {
    res.sendFile(path.join(__dirname, 'dist/signupOffer.html'));
});

app.get('/verifyemail', function (req, res) {
    //console.log(req);
    res.sendFile(path.join(__dirname, 'dist/verifyemail.html'));
});

app.get('/freechai', function (req, res) {
    res.redirect("https://cafes.chaayos.com/myoffer?utm_source=Google&utm_medium=Top+of+the+Funnel&token=6EA9AB1BAA0EFB9E19094440C317E21B");
});

app.get('/validate', function (req, res) {
    var query = req.query;
    var token = query["token"];
    console.log(token);

    var basePath = (process.env.NODE_ENV == "dev" || process.env.NODE_ENV == "local")
                    ? "http://dev.kettle.chaayos.com:9595"
                    : "http://relax.chaayos.com";

    if(process.env.NODE_ENV == "local"){
        basePath = "http://localhost:8080"
    }
    console.log("basePath is :::: " + basePath);

    var url = basePath + "/kettle-service/rest/v1/authorization/validate?token="+encodeURIComponent(token);
    request(url, function (error, response, body) {
        console.log('error:', error); // Print the error if one occurred
        console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received
        console.log('body:', body); // Print the HTML for the Google homepage.
        res.send(body!=undefined ? body : false);
    });
});

// serve our static stuff like index.css
app.use(express.static(path.join(__dirname, 'dist/')));



app.get('/myoffer', function (req, res) {
    //console.log(req);
    app.getCampaignDetails(req.protocol + '://' + req.hostname + req.url, function (data){
        if(data.campaignId === undefined){
            res.sendFile(path.join(__dirname, 'dist/index.html'));
        }else{
            fs.readFile(indexPath, 'utf8', (err, htmlData) => {
                if(err){
                    res.sendFile(path.join(__dirname, 'dist/index.html'));
                }else {
                    htmlData = htmlData.replace(
                        "<title>Chaayos - Order Chai Online - Experiments With Chai</title>",
                        `<title>${data.utmHeading}</title>`
                    ).replace(/__META_DESCRIPTION__/g,data.utmDesc)
                        .replace(/__META_OG_TITLE__/g,data.utmHeading)
                        .replace(/__META_OG_DESCRIPTION__/g, data.utmDesc)
                        .replace(/__META_OG_IMAGE__/g, data.image1)
                        .replace(/__META_OG_IMAGE_ALT__/g, data.utmHeading)
                        .replace(/__META_URL_ENDPOINT__/g, "myoffer")
                        .replace("<!--META","")
                        .replace("META-->","");
                    res.send(htmlData);
                }
            });
        }
    })
});

app.get('/game/slot/:flow', function (req, res) {
    app.getOrderManagementToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        console.log(token);
        console.log("flow :::::::::",req.url.split("?")[0].split("/")[2]);
        var flow = req.url.split("?")[0].split("/")[3];
        res.clearCookie('token');
        if(parseInt(flow) < 3 && parseInt(flow) >=0){
            res.cookie('token', token.substr(1, token.length-2), {});
        }
        app.getCampaignDetails(req.protocol + '://' + req.hostname + req.url, function (data){
            if(data.campaignId === undefined){
                res.sendFile(path.join(__dirname, 'dist/index.html'));
            }else{
                res.cookie('campaignId',data.campaignId)
                fs.readFile(indexPath, 'utf8', (err, htmlData) => {
                    if(err){
                        res.sendFile(path.join(__dirname, 'dist/index.html'));
                    }else {
                        htmlData = htmlData.replace(
                            "<title>Chaayos - Order Chai Online - Experiments With Chai</title>",
                            `<title>${data.utmHeading}</title>`
                        ).replace(/__META_DESCRIPTION__/g,data.utmDesc)
                            .replace(/__META_OG_TITLE__/g,data.utmHeading)
                            .replace(/__META_OG_DESCRIPTION__/g, data.utmDesc)
                            .replace(/__META_OG_IMAGE__/g, data.image1)
                            .replace(/__META_OG_IMAGE_ALT__/g, data.utmHeading)
                            .replace(/__META_URL_ENDPOINT__/g, "10yearsOfChaayos/1")
                            .replace("<!--META","")
                            .replace("META-->","")
                            .replace("https://checkout.razorpay.com/v1/checkout.js","");
                        res.send(htmlData);
                    }
                });
            }
        })
    });
});

app.get('/gameScreen', function (req, res) {
    res.sendFile(path.join(__dirname, 'dist/js/components/OfferGame/GameScreen/gameIndex.html'));
});

app.get('/ninjaRef/:refCode', function (req, res) {
    var refCode = req.url.split("?")[0].split("/")[2];
    res.redirect("https://cafes.chaayos.com/game/ninja/0?token=3416A75F4CEA9109507CACD8E2F2AEFC&utm_source=REFERRAL&utm_medium="+refCode);
});

app.get('/game/ninja/:flow', function (req, res) {
    app.getOrderManagementToken(req.protocol + '://' + req.hostname + req.url, function (token) {
        console.log(token);
        console.log("flow :::::::::",req.url.split("?")[0].split("/")[2]);
        var flow = req.url.split("?")[0].split("/")[3];
        res.clearCookie('token');
        if(parseInt(flow) < 3 && parseInt(flow) >=0){
            res.cookie('token', token.substr(1, token.length-2), {});
        }
        app.getCampaignDetails(req.protocol + '://' + req.hostname + req.url, function (data){
            console.log("Campaign id =>" , data.campaignId)
            if(data.campaignId === undefined){
                res.sendFile(path.join(__dirname, 'dist/index.html'));
            }else{
                res.cookie('campaignId',data.campaignId)
                fs.readFile(path.join(__dirname, 'dist/index.html'), 'utf8', (err, htmlData) => {
                    if(err){
                        res.sendFile(path.join(__dirname, 'dist/index.html'));
                    }else {
                        htmlData = htmlData.replace(
                            "<title>Chaayos - Order Chai Online - Experiments With Chai</title>",
                            `<title>${data.utmHeading}</title>`
                        ).replace(/__META_DESCRIPTION__/g,data.utmDesc)
                            .replace(/__META_OG_TITLE__/g,data.utmHeading)
                            .replace(/__META_OG_DESCRIPTION__/g, data.utmDesc)
                            .replace(/__META_OG_IMAGE__/g, data.image1)
                            .replace(/__META_OG_IMAGE_ALT__/g, data.utmHeading)
                            .replace(/__META_URL_ENDPOINT__/g, "/game/chai/1")
                            .replace("<!--META","")
                            .replace("META-->","")
                            .replace("https://checkout.razorpay.com/v1/checkout.js","");
                        res.send(htmlData);
                    }
                });
            }
        })
    });
});

app.get('*', function (req, res) {
    res.sendFile(path.join(__dirname, 'dist/index.html'));
});


//var PORT = process.env.PORT || 8080
var PORT = 80;
if(env !== "PROD"){
    PORT=8989;
}
app.listen(PORT, function () {
    process.env.NODE_ENV = process.argv[process.argv.length-1];
    console.log('Production Express server running at localhost:' + PORT + " and ENV :: " + process.env.NODE_ENV)
});
