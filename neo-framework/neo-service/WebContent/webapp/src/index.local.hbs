<!DOCTYPE html>
<html>
<head>
    <title>Chaayos - Order Chai Online - Experiments With Chai</title>
    <meta name="Description" content="Chaayos - a Chai adda to order chai online with 1200 personalised chai options. Get the authentic taste delivered in Delhi, Gurgaon, Noida and Mumbai." />
    <meta name="Keywords" content="Order Chai Online, Order Tea Online, Order Food Online, order breakfast online, Masala Chai, desi chai, indian chai, vada pav" />
    <!--META
    <meta name="description" content="__META_DESCRIPTION__"/>
    <meta name="og:title" content="__META_OG_TITLE__"/>
    <meta name="og:description" content="__META_OG_DESCRIPTION__"/>
    <meta name="og:image" content="__META_OG_IMAGE__"/>
    <meta property="og:url" content="https://cafes.chaayos.com/__META_URL_ENDPOINT__" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="__META_OG_TITLE__" />
    <meta property="og:description" content="__META_DESCRIPTION__" />
    <meta property="og:image" content="__META_OG_IMAGE__" />
    <meta property="og:image:secure_url" content="__META_OG_IMAGE__" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:type" content="image/jpeg" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="__META_OG_IMAGE_ALT__" />
    <meta property="fb:app_id" content="1300275053451615" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@chaayos" />
    <meta name="twitter:title" content="__META_OG_TITLE__" />
    <meta name="twitter:description" content="__META_DESCRIPTION__" />
    <meta property="twitter:image" content="__META_OG_IMAGE__" />
    META-->
    <meta name="google-site-verification" content="" />
    <link rel="canonical" href="https://cafes.chaayos.com" />
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta http-equiv="X-Frame-Options" content="ALLOW-FROM *"/>
    <meta name="theme-color" content="#5e7e47" id="themeColor" />
    <meta name="full-screen" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#5e7e47" />
    <meta name="apple-mobile-web-app-title" content="Chaayos" />
    <meta name="facebook-domain-verification" content="9ejplx5w44q5a303c6nlfli8dozdy6" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <link rel="icon" type="image/x-icon" href="/img/favicon-96x96.png" />
    <link href="https://fonts.cdnfonts.com/css/gotham" rel="stylesheet">
    <link rel="apple-touch-icon" href="/img/favicon-96x96.png" type="image/x-icon" />
    <style type="text/css">
        .splash{position: absolute;top:0;right:0;bottom: 0;left: 0;text-align: center;background: #fff; vertical-align: middle;-webkit-transition: all 0.25s linear;transition: all 0.25s linear;}
        .splash.inactive{opacity: 0;}.dummyHead{height: 52px;background: #5e7e47;}.dummyHead img{height: 27px;margin-top: 12px;}
        .load8.loader,.load8.loader:after{border-radius:50%;width:35px;height:35px;text-align:center}
        .load8.loader{margin:35px auto;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(189,189,189,.2);
            border-right:2px solid rgba(189,189,189,.2);border-bottom:2px solid rgba(189,189,189,.2);border-left:2px solid #065904;-webkit-transform:translateZ(0);
            -ms-transform:translateZ(0);transform:translateZ(0);-webkit-animation:load8 .6s infinite linear;animation:load8 .6s infinite linear}
        @-webkit-keyframes load8{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}
        @keyframes load8{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/js-confetti@latest/dist/js-confetti.browser.js"></script>
    <!--<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous"> -->
    <script data-cfasync="false" type="application/javascript" nonce>
        function loadjscssfile(filename, filetype) {
            if (filetype == "js") { //if filename is a external JavaScript file
                var fileref = document.createElement('script');
                fileref.setAttribute("type", "text/javascript");
                fileref.setAttribute("src", filename);
            }
            else if (filetype == "css") { //if filename is an external CSS file
                var fileref = document.createElement("link");
                fileref.setAttribute("rel", "stylesheet");
                fileref.setAttribute("type", "text/css");
                fileref.setAttribute("href", filename);
            }
            if (typeof fileref != "undefined")
                document.getElementsByTagName("head")[0].appendChild(fileref)
        }
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            loadjscssfile("/css/style.css", "css");
            loadjscssfile("/css/react-datepicker.css", "css");
            loadjscssfile("/css/ChaayosSelectMembership_css/ChaayosSelectMembershipDesigns.css", "css");


        }else{
            loadjscssfile("/css/styled.css", "css");

            loadjscssfile("/css/react-datepicker.css", "css");
            loadjscssfile("/css/ChaayosSelectMembership_css/ChaayosSelectMembershipDesigns.css", "css");
        }
    </script>
    <link rel="manifest" href="/manifest.json" />
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-TL77XHD');</script>
    <!-- End Google Tag Manager -->
    <script data-cfasync="false">
            !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
                n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
                    document,'script','https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '578314930322960_DEV'); // Insert your pixel ID here.
            fbq('track', 'PageView');
        </script>
        <noscript><img height="1" width="1" style="display:none"
                       src="https://www.facebook.com/tr?id=578314930322960_DEV&ev=PageView&noscript=1"
            /></noscript>
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-857135357"></script>
    <!-- Global site tag (gtag.js) - Google Ads: 857135357 -->
    <script data-cfasync="false" async src="https://www.googletagmanager.com/gtag/js?id=AW-857135357"></script>
    <script data-cfasync="false">
        window.dataLayer = window.dataLayer || [];
        function gtag(){console.log(arguments);dataLayer.push(arguments);};
        gtag('js', new Date());
        gtag('config', 'AW-857135357');
    </script>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@600&display=swap" rel="stylesheet">
</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TL77XHD"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<div id="app" style="background-color:white"></div>
<div class="splash">
    <div class="dummyHead"><img src="/img/logo.svg" /></div>
    <div class="loader load8" style="margin-top: 100px;"></div>
    <p id="splashPay"></p>
</div>
<script type="text/javascript">
    if(window.location.pathname=="/payProcess" && document.getElementById("splashPay")!=null){
        document.getElementById("splashPay").innerHTML = "Please wait while we are checking your payment status.";
    }
    if (Object.prototype.toString.call(window.operamini) === "[object OperaMini]") {
        alert("We have limited support for this browser. For best experience please turn of data saving mode or switch to Google Chrome.");
    }
</script>
<link rel="stylesheet" href="/css/slick.min.css" />
<link rel="stylesheet" href="/css/slick-theme.min.css" />
<link href="https://fonts.googleapis.com/css?family=Varela+Round" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="/css/react-select.min.css" />
<script crossorigin="anonymous" src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script type="text/javascript">
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').then(function (registration) {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
        }).catch(function (err) {
            console.error('ServiceWorker registration failed: ', err);
        });
    }
</script>
<script type="application/javascript" data-cfasync="false" src="/{{htmlWebpackPlugin.options.data.vendor.js}}"></script>
<script type="application/javascript" data-cfasync="false" src="/{{htmlWebpackPlugin.options.data.home.js}}"></script>
</body>
</html>
