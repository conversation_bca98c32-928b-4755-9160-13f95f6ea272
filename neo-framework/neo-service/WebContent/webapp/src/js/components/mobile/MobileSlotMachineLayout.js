import React from "react";
import {connect} from "react-redux";
import * as SlotMachineAction from "../../actions/SlotMachineActions";
import {styles} from './styles/MobileSlotMachineStyles';
import appUtil from "../../AppUtil";
import * as UtilityActions from "../../actions/UtilityActions";
import {gamifiedOfferStage, slotMachineJourneyState, slotMachineStage} from "../../actions/SlotMachineActions";
import trackUtils from "../../utils/TrackUtils";
import * as MyOfferAction from "../../actions/MyOfferActions";
import {journeyStage, recordJourney} from "../../actions/MyOfferActions";
import {
    EmailIcon,
    EmailShareButton,
    FacebookIcon,
    FacebookShareButton,
    TwitterIcon,
    TwitterShareButton,
    WhatsappIcon,
    WhatsappShareButton
} from "react-share";

@connect((store) => {
    return {
        currentStage: store.slotMachineReducer.currentStage,
        showNoOffer: store.slotMachineReducer.showNoOffer,
        showNBOOffer: store.slotMachineReducer.showNBOOffer,
        showDNBOOffer: store.slotMachineReducer.showDNBOOffer,
        showChaayosCashOffer: store.slotMachineReducer.showChaayosCashOffer,
        showMembershipOffer: store.slotMachineReducer.showMembershipOffer,
        innerHeight: store.slotMachineReducer.innerHeight,
        innerWidth: store.slotMachineReducer.innerWidth,
        campaignDetail: store.slotMachineReducer.campaignDetail,
        specialOffer: store.slotMachineReducer.specialOffer,
        utmData: store.slotMachineReducer.utmData,
        isButtonLoading: store.slotMachineReducer.isButtonLoading,
        enableShowOfferButton: store.slotMachineReducer.enableShowOfferButton,
    };
})

export default class MobileSlotMachineLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            name: null,
            email: null,
            contact: null,
            otp: null,
            otpResendCounter: 30,
            showShareOptions: false,
            isKeyboardOpen:false,
            flow:0,
            authToken:null,
            isContactFromUrl:false,
            spinning:false,
            revealing:false,
            optWhatsapp:true,
            isCrmScreen:false,
            redirectionSecond:15
        };
        this.initializeGame = this.initializeGame.bind(this);
        this.setSlotMachineBackgroundImage = this.setSlotMachineBackgroundImage.bind(this);
        this.getOffer = this.getOffer.bind(this);
        this.resizeViewPort = this.resizeViewPort.bind(this);
        this.getTermAndCondition = this.getTermAndCondition.bind(this);
        this.processCheckBox = this.processCheckBox.bind(this);
        this.onPhoneInputChange = this.onPhoneInputChange.bind(this);
        this.onSkipClicked = this.onSkipClicked.bind(this);
    }

    componentWillMount() {
        window.scrollTo(0, 0);
    }

    componentDidMount() {
        // this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.showThankYouPage));
        // document.getElementById("parentContainer").style.display='block'
        // var i = 15;
        // const redirectionInterval = setInterval(()=>{
        //     this.setState({redirectionSecond:i})
        //     if(i === 0){
        //         clearInterval(redirectionInterval);
        //         window.location.href='https://zomato.onelink.me/xqzv/5fbcb81a'
        //     }
        //     i-=1
        // },1000)
        this.props.dispatch(UtilityActions.showFullPageLoader("Loading your game ..."))
        const queryParams = new URLSearchParams(window.location.search);
        if(appUtil.checkEmpty(queryParams.get("token"))){
            this.props.dispatch(SlotMachineAction.setCampaignDetail({"campaignToken":"3416A75F4CEA9109507CACD8E2F2AEFC","campaignId":41}));
        }else{
            var campaignId = !appUtil.checkEmpty(this.getCookie("campaignId")) ? this.getCookie("campaignId") : 41;
            this.props.dispatch(SlotMachineAction.setCampaignDetail({"campaignToken":queryParams.get("token"),"campaignId": campaignId}))
        }
        window.addEventListener('load', this.initializeGame);
        window.addEventListener('resize', this.resizeViewPort);
        const params = this.props.props.params;
        this.setState({flow : params.flow});
        this.setSlotMachineBackgroundImage();
        var token = this.getCookie("token");
        this.setState({authToken:token})
        this.props.dispatch(SlotMachineAction.updateScreenDimension(window.innerHeight,window.innerWidth));
        this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getBasicInfo));

        var utmData={
            "utmSource":!appUtil.checkEmpty(queryParams.get("utm_source")) ? queryParams.get("utm_source") : "DEFAULT",
            "utmMedium":!appUtil.checkEmpty(queryParams.get("utm_medium")) ? queryParams.get("utm_medium") : "DEFAULT"
        }
        if(utmData.utmMedium === "CRM_APP"){
            this.setState({isCrmScreen:true});
        }
        if(params.flow === "2"){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.onlySpinPage));
        }else{
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getBasicInfo));
        }
        this.props.dispatch(SlotMachineAction.setUtmData(utmData));
        var urlContact = queryParams.get("contact");
        if(urlContact != null && urlContact.length >= 10){
            urlContact=urlContact.substring(urlContact.length-10);
        }
        if(!appUtil.checkEmpty(urlContact) && appUtil.validContact(urlContact)){
            this.setState({contact:urlContact});
            this.setState({isContactFromUrl:true});
        }
        fbq('trackCustom', 'MachineGamePageView');
        trackUtils.trackSlotMachineGamePageView();
        this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.pageView,"mobile"))
        // document.querySelector(".boxes").style.backgroundImage=`url("./../../../img/SlotGame/slotGame8.webp")`;
    }

    onPhoneInputChange(){
        if(this.state.flow !== "2"){
            return;
        }
        var value =  document.getElementById("phoneNumber").value;
        this.setState({"contact":value});
        if (appUtil.validContact(value)){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            // this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,value,this.props.campaignDetail.campaignToken,name,this.state.email,
            //     null,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"desktop"))
        }else if(value.length === 10){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false))
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
        }else{
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false))
        }
    }

    onSkipClicked(){
        this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.skipButtonClicked,"mobile"));
        window.ReactNativeWebView.postMessage(JSON.stringify({action : "SKIP"}));
    }

     getOffer(){
         if(!this.props.enableShowOfferButton){
             return;
         }
         if(this.props.currentStage === slotMachineStage.onlySpinPage){
             this.spin();
             fbq('trackCustom', 'MachineGamePlayedFlow2');
             trackUtils.trackSlotMachineGamePlayedFlow2();
             this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.spinMachineFlow2,"mobile"))
             return;
         }
        if(this.props.currentStage === slotMachineStage.getBasicInfo){
            var contact = (this.state.isContactFromUrl) ? this.state.contact : document.getElementById("phoneNumber").value;
            var email = null;
            if(this.props.updateButtonLoader){
                return;
            }
            if(this.state.flow === "1"){
                email = document.getElementById("emailId").value;
                if(appUtil.checkEmpty(email) || !appUtil.validEmail(email)){
                    this.props.dispatch(UtilityActions.showPopup("Please enter valid email!", "error", 3000));
                    return;
                }else{
                    this.setState({email:email});
                }
            }
            if(appUtil.checkEmpty(contact) || !appUtil.validContact(contact)){
                this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
                return;
            }else{
                this.setState({contact:contact});
            }
            if(this.state.flow === "2"){
                this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,contact,this.props.campaignDetail.campaignToken,name,this.state.email,
                    null,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"desktop"))
                return;
            }
            this.spin(contact,email,this.state.name,this.state.otp,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp);
        }else{
            if(this.state.revealing){
                return;
            }
            var name = document.getElementById("name").value;
            var otp = document.getElementById("otp").value;
            if(appUtil.checkEmpty(name) || !appUtil.validName(name)){
                this.props.dispatch(UtilityActions.showPopup("Please enter valid name!", "error", 3000));
                return;
            }else{
                this.setState({name:name});
            }
            if(appUtil.checkEmpty(otp) || !appUtil.validOtp(otp)){
                this.props.dispatch(UtilityActions.showPopup("Please enter valid otp!", "error", 3000));
                return;
            }else{
                this.setState({otp:otp});
            }
            this.setState({revealing:true})
            this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,this.state.contact,this.props.campaignDetail.campaignToken,name,this.state.email,
                otp,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"mobile"))
        }
    }

    showConfetti(){
        var times =0;
        const canvas = document.getElementById('your_custom_canvas_id')
        const jsConfetti = new JSConfetti({ canvas })
        document.querySelector("#crowdCheer").play();
        jsConfetti.addConfetti({
            confettiColors: [
                '#ffcd2b','#070105','rgba(255,233,0,0.83)','rgba(189,87,0,0.83)',
                'rgba(238,255,0,0.83)','#ffaa63'
            ],
            confettiNumber: 300,
            confettiRadius: 5,
        })
        var timeInterval = setInterval(()=>{
            jsConfetti.addConfetti({
                confettiColors: [
                    '#ffcd2b','#070105','rgba(255,233,0,0.83)','rgba(189,87,0,0.83)',
                    'rgba(238,255,0,0.83)','#ffaa63'
                ],
                confettiNumber: 300,
                confettiRadius: 5,
            })
            if(times === 1){
                clearInterval(timeInterval);
            }
            times+=1;
        },2000);
        var stopCheerSound = setTimeout(()=>{
            document.querySelector("#crowdCheer").pause();
            document.querySelector("#crowdCheer").currentTime =0;
            clearInterval(stopCheerSound);
        },6000);
    }

    resizeViewPort(){
        if(window.innerHeight< this.props.innerHeight){
            document.querySelector("#parentContainer").style.overflow='scroll';
        }else{
            document.querySelector("#parentContainer").style.height=this.props.innerHeight;
            document.querySelector("#parentContainer").style.width=this.props.innerWidth;
            setTimeout(()=>{
                document.querySelector("#parentContainer").style.overflow='auto';
            },500);
        }
    }

    getDays(startDate, endDate){
        return (new Date(endDate) - new Date(startDate))/(1000*60*60*24);
    }

    getTermAndCondition(tncString, startDate, endDate){
        var tncList = tncList.split("#");
        var innerHtml = "valid from : "+startDate+" valid till : "+endDate;
        for(var i in tncList){
            innerHtml+="<br/>"+tncList[i];
        }
        return innerHtml;
    }

    setSlotMachineBackgroundImage(){
        const imageUrlMachine = "./../../../img/SlotGame/slotMachine.webp";
        const imageUrlJackpot = "./../../../img/SlotGame/jackpotImage.webp";
        const imageUrlOfferDetail = "./../../../img/SlotGame/offerDetailBanner.webp";
        const imageUrlBalloons = "./../../../img/SlotGame/desktopBalloons.webp";
        const slotMachineDown = "./../../../img/SlotGame/slotMachine.webp";
        const parentContainer = document.querySelector("#parentContainer");
        let preloaderImgJackpot = document.createElement("img");
        let preloaderImgSlotMachine = document.createElement("img");
        let preloaderImgOfferDetail = document.createElement("img");
        let preloaderImgBalloons = document.createElement("img");
        let preloaderslotMachineDown = document.createElement("img");
        preloaderImgJackpot.src = imageUrlJackpot;
        preloaderImgJackpot.addEventListener('load', (event1) => {
            preloaderImgSlotMachine.src = imageUrlMachine;
            preloaderImgSlotMachine.addEventListener('load', (event2) => {
                preloaderImgOfferDetail.src = imageUrlOfferDetail;
                preloaderImgOfferDetail.addEventListener('load', (event3) => {
                    preloaderImgBalloons.src = imageUrlBalloons;
                    preloaderImgBalloons.addEventListener('load', (event4) => {
                        preloaderslotMachineDown.src = slotMachineDown;
                        preloaderslotMachineDown.addEventListener('load', (event5) => {
                        parentContainer.style.display = 'flex';
                    });
                    });
                });
            });
        });
    }

    getDateString(date){
        return new Date(date).toDateString().substring(8,10)+" "+
            new Date(date).toDateString().substring(4,7)+" "+
            new Date(date).toDateString().substring(11)
    }

    async spin(contact,email,name,otp,token,flow,callback,opt) {
        this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.spinMachine,"mobile",contact));
        const doors = document.querySelectorAll(".door");
        document.querySelector(".boxes").style.backgroundImage=``;
        if(flow === undefined){
            flow = this.state.flow;
        }
        this.initializeGame(false, 1, 4,contact,email,name,otp,token,flow,callback,opt);
        for (const door of doors) {
            const boxes = door.querySelector(".boxes");
            const duration = parseInt(boxes.style.transitionDuration);
            boxes.style.transform = "translateY(0)";
            await new Promise((resolve) => setTimeout(resolve, duration * 100));
        }
    }

    initializeGame(firstInit = true, groups = 1, duration = 1,contact,email,name,otp,token,flow,callback,opt) {
        this.props.dispatch(UtilityActions.hideFullPageLoader());
        var jackPotSlot = Math.floor(Math.random()*100)%3;
        const doors = document.querySelectorAll(".door");
        var props=this.props;
        var doorIndex=0;
        var doorSize = doors.length;
        for (const door of doors) {
            if (firstInit) {
                door.dataset.spinned = "0";
            } else if (firstInit && door.dataset.spinned !== "1") {
                return;
            }

            const boxes = door.querySelector(".boxes");
            const boxesClone = boxes.cloneNode(false);

            const pool = ["slotGame0.webp"];
            if (!firstInit) {
                pool.pop();
                const arr = [];
                for (let n = 0; n < (groups > 0 ? groups : 1); n++) {
                    arr.push(...SlotMachineAction.slotMachineItems);
                }
                var newArray=[];
                for (var i = 0; i <= 4; i++) {
                    newArray = newArray.concat(arr);
                };
                if(doorIndex !== jackPotSlot){
                    newArray.push("slotGame8.webp");
                    newArray.push("slotGame8.webp");
                    newArray.push("slotGame8.webp");
                    newArray.push("slotGame8.webp");
                }
                pool.push(...this.shuffle(newArray));
                boxesClone.addEventListener(
                    "transitionstart",
                    function () {
                        // document.querySelector("#slotMachineContainer").style.backgroundImage=`url("./../../../img/SlotGame/slotMachineDown.webp")`;
                        door.dataset.spinned = "1";
                        this.querySelectorAll(".box").forEach((box) => {
                            //   box.style.filter = "blur(1px)";
                        });
                        document.querySelector("#audio").play();
                    },
                    { once: true }
                );

                boxesClone.addEventListener(
                    "transitionend",
                    function () {
                        this.querySelectorAll(".box").forEach((box, index) => {
                            box.style.filter = "blur(0)";
                            if (index > 0) this.removeChild(box);
                        });

                        if(doorIndex === doorSize){
                            doorIndex=0;
                            if(flow !== "2"){
                                const noOfferBg = "./../../../img/SlotGame/noOfferPop.webp";
                                const offerFoundBg = "./../../../img/SlotGame/offerFoundBg.webp";
                                let preloaderNoOfferBg = document.createElement("img");
                                let preloaderOfferFoundBg = document.createElement("img");
                                preloaderNoOfferBg.src = noOfferBg;
                                preloaderNoOfferBg.addEventListener('load', (event5) => {
                                    preloaderOfferFoundBg.src = offerFoundBg;
                                    preloaderOfferFoundBg.addEventListener('load', (event6) => {
                                        var getOfferTimeOut = setTimeout(()=>{
                                            props.dispatch(SlotMachineAction.getSpecialOffer(props.currentStage,contact,props.campaignDetail.campaignToken,name,email,
                                                otp,props.utmData.utmSource,props.utmData.utmMedium,token,flow,callback,opt,"desktop"))
                                            clearTimeout(getOfferTimeOut);
                                        },2000)
                                    });
                                });
                            }else{
                                var getOfferTimeOut = setTimeout(()=>{
                                    props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getBasicInfo));
                                    props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false));
                                    clearTimeout(getOfferTimeOut);
                                },2000)
                            }
                        }
                        var soundStopTimer = setTimeout(()=>{
                            document.querySelector("#audio").pause();
                            document.querySelector("#audio").currentTime =0;
                        },600)
                    },
                    { once: true }
                );
            }
            // console.log(pool);

            for (let i = pool.length - 1; i >= 0; i--) {
                const box = document.createElement("div");
                box.classList.add("box");
                box.style.width = door.clientWidth + "px";
                box.style.height = door.clientHeight + "px";
                box.style.display = "flex";
                box.style.alignItems = "center";
                box.style.justifyContent = "center";
                const image = document.createElement("img");
                image.src="./../../../img/SlotGame/"+pool[i];
                if(this.state.isCrmScreen){
                image.style.height="50px";
                image.style.width="50px";
                }else{
                    image.style.height="35px";
                    image.style.width="35px";
                }
                box.appendChild(image);
                boxesClone.appendChild(box);
            }
            boxesClone.style.transitionDuration = `${duration > 0 ? duration : 1}s`;
            boxesClone.style.transform = `translateY(-${
                door.clientHeight * (pool.length - 1)
            }px)`;
            door.replaceChild(boxesClone, boxes);
            doorIndex+=1;
            // console.log(door);
        }
    }

    shuffle([...arr]) {
        let m = arr.length;
        while (m) {
            const i = Math.floor(Math.random() * m--);
            [arr[m], arr[i]] = [arr[i], arr[m]];
        }
        return arr;
    }

    startOTPResendWaitCounter() {
        let sec = 30;
        let interval = setInterval(() => {
            this.setState({otpResendCounter: --sec});
            //console.log("seconds:: " + sec);
            if (sec === 0) {
                clearInterval(interval);
            }
        }, 1000);
    }

    getCookie(cname) {
        let name = cname + "=";
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for(let i = 0; i <ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }
    processCheckBox(){
        if(this.state.optWhatsapp){
            this.setState({optWhatsapp:false});
        }else{
            this.setState({optWhatsapp:true});
        }
    }

    handleOTPChange = otp => this.setState({otp});

    showExploreButton(utmMedium){
        var show ="visible";
        if(utmMedium.toLowerCase()==="dine_in_app" || utmMedium.toLowerCase() === "crm_app"
            || this.props.utmData.utmSource.toLowerCase() === "cafe" || this.props.utmData.utmSource.toLowerCase() === "dine_in_app"){
            show = "hidden";
        }
        return(
            <div style={{...styles.exploreMenuButton, visibility:show}}
                 onClick={() => window.open('https://zomato.onelink.me/xqzv/5fbcb81a')}>
                <p style={{color:'white',fontSize:'12px',fontFamily:'Nunito'}}>Explore Menu</p>
            </div>
        );
    }

    redirectToNBO(){
        return(
            <div style={{...styles.exploreMenuButton, visibility:true}}
                 onClick={() => {
                     window.location.href='https://zomato.onelink.me/xqzv/5fbcb81a'
                 }}>
                <p style={{color:'white',fontSize:'12px'}}>I am Feeling Lucky</p>
            </div>
        );
    }

    showDownloadAppButtons(utmMedium,marginBottom){
        var pointerEvents = "";
        if(utmMedium.toLowerCase()==="dine_in_app" || this.props.utmData.utmSource.toLowerCase() === "dine_in_app"){
            return (<div/>)
        }else if(utmMedium.toLowerCase() === "crm_app"){
            pointerEvents="none";
        }
        return (
            <div style={{
                width: !this.state.isCrmScreen ? '70%' : '55%',
                marginBottom: !this.state.isCrmScreen ? marginBottom : '30px'
            }}>
                <h1 style={styles.downloadText}>Download the app Now</h1>
                <div style={styles.downloadContainer}>
                    <div style={{...styles.downloadStoreContainer,pointerEvents:pointerEvents}}
                         onClick={() => window.open('https://chaayos.org/freechai_signup_app_download')}>
                        <img style={styles.downloadImg}
                             src="./../../../img/downloadappstore.png"/>
                    </div>
                    <div style={{...styles.downloadStoreContainer,pointerEvents:pointerEvents}}
                         onClick={() => window.open('https://chaayos.org/freechai_signup_app_download')}>
                        <img style={styles.downloadImg}
                             src="./../../../img/downloadplaystore.png"/>
                    </div>
                </div>
            </div>
        );
    }

    getNBOGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center',justifyContent:'space-between',height:'60%'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'8vw', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>Congratulation</p>
                <p style={{fontSize:'4.5vw', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'14vw', textAlign:'center', marginTop:'20px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>{offerData.text}</p>
            <div style={{...styles.couponCodeContainer, width:'80%'}}>
                <div style={{...styles.couponCodeBox,height:'20vw'}}>
                    <p style={{...styles.couponCodeText, fontSize:'6vw'}}>{offerData.offerCode}</p>
                </div>
            </div>
        </div>)
    }
    getNoOfferPage(offerData){
        return (<div style={{width:'60%', display:'flex', flexDirection:'column', alignItems:'center'}}>
            <p style={{fontSize:'30px', fontWeight:'bold', color:'white'}}>Sorry!</p>
            <p style={{fontSize:'8vw', textAlign:'center', lineHeight:'40px',
                fontFamily:"'Gotham Black', sans-serif", width:'100%', fontWeight:'900'}}>Better Luck Next Time</p>
            <div style={styles.couponCodeContainer}>
                <div style={{width:'100%'}}>
                    <p style={styles.useCodeText}>{'uh oh!'}</p>
                    <p style={styles.useCodeText}>{'Looks Like it is taking longer!'}</p>
                    <p style={styles.useCodeText}>{'to brew your offer'}</p>
                </div>
            </div>
        </div>)
    }

    getDNBOGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center',justifyContent:'space-between',height:'60%'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'8vw', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>Congratulation</p>
                <p style={{fontSize:'4.5vw', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'14vw', textAlign:'center', marginTop:'20px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>{offerData.text}</p>
            <div style={{...styles.couponCodeContainer, width:'80%'}}>
                <div style={{...styles.couponCodeBox,height:'20vw'}}>
                    <p style={{...styles.couponCodeText, fontSize:'6vw'}}>{offerData.offerCode}</p>
                </div>
            </div>
        </div>)
    }

    getMembershipGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'8vw', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>Congratulation</p>
                <p style={{fontSize:'4.5vw', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'12vw', textAlign:'center', marginTop:'75px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>Chaayos Select Membership</p>

            <div style={styles.couponCodeContainer}>
                <div style={{width:'100%'}}>
                    <p style={{...styles.useCodeText, fontSize:'5vw',color:'#000000'}}>Worth 199&#x20b9;</p>
                </div>
                <div style={{width:'100%'}}>
                    <p style={{...styles.useCodeText, textDecoration:'none', fontSize:'4vw',color:'#000000'}}>for next {this.getDays(offerData.validityFrom,offerData.validityTill)} days</p>
                </div>
            </div>
        </div>)
    }

    getCashGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center',justifyContent:'space-between',height:'50%'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'8vw', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>Congratulation</p>
                <p style={{fontSize:'4.5vw', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'20vw', textAlign:'center', marginTop:'75px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>200</p>
            <p style={{fontSize:'9vw', textAlign:'center', textShadow:'10px 4px 14px #0000008c', color:'#fff', marginTop:'20px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'bolder'}}>Chaayos Cash</p>

            <div style={styles.couponCodeContainer}>
                <div style={{...styles.flexCenterColumn,width:'100%'}}>
                    <p style={{...styles.useCodeText, fontSize:'5vw'}}>use before {this.getDateString(offerData.validityTill)}</p>
                </div>
            </div>
        </div>)
    }

    showTnc(){
        return (
            <div style={{
                ...styles.tncContainer,
                width: !this.state.isCrmScreen ? '90%' : '70%'
            }}>
                <p style={styles.tncHeading}>Term and Conditions</p>
                {}
            </div>
        );
    }

    getOfferTextSize(text){
        var size = text.length;
        if(size<=10){
            return '40px'
        }else if(size<=16){
            return '30px'
        }else{
            return '22px'
        }
    }
    getDeviceOS(){
        var name = "Unknown OS";
        if (navigator.userAgent.indexOf("Win") != -1)
            name = "Windows OS";
        if (navigator.userAgent.indexOf("Mac") != -1)
            name = "Macintosh";
        if (navigator.userAgent.indexOf("Linux") != -1)
            name = "Linux OS";
        if (navigator.userAgent.indexOf("Android") != -1)
            name = "Android OS";
        if (navigator.userAgent.indexOf("like Mac") != -1)
            name = "iOS";
        return name
    }

    render() {
        var modalBodyHeight=(window.innerWidth*13)/8;
        return (
                this.props.currentStage === slotMachineStage.getBasicInfo || this.props.currentStage === slotMachineStage.getOTP || this.props.currentStage === slotMachineStage.onlySpinPage?
                    <div id="parentContainer" style={{...styles.parentContainer, minHeight:  '700px'}}>
                        {this.state.isCrmScreen?
                            <div onClick={()=>{window.ReactNativeWebView.postMessage(JSON.stringify({action : "SKIP"}))}} style={{height:"60px",width:'60px', display:'flex',
                                justifyContent:'center',alignItems:'center',backgroundColor:'#ffffff8f',position:'absolute',top:'10px',right:'10px'}}>
                                <p style={{fontSize:'35px'}}>X</p>
                            </div>:<div/>
                        }
                        <audio id="audio" src={"./../../../img/SlotGame/slotGameSound.wav"}/>
                        {
                            !this.state.isContactFromUrl || this.props.utmData.utmSource === "dine_in_app" ?
                                <img style={{width: '100%'}}
                                     src={"./../../../img/SlotGame/jackpotMobile.webp"}/> :
                                <img style={{width: '100%'}}
                                     src={!this.state.isCrmScreen ? "./../../../img/SlotGame/jackpotBannerMedium.png" : "./../../../img/SlotGame/jackpotBannerMedium.png"}/>
                        }
                        {
                            !this.state.isCrmScreen ?
                                <div id="slotMachineContainer" style={styles.slotMachineContainer}>
                                    <div id="slotGameBoxes" style={styles.slotBoxContainer}>
                                        <div className="door" style={styles.door}>
                                            <div className="boxes" style={styles.boxes}>
                                            </div>
                                        </div>
                                        <div className="door" style={styles.door}>
                                            <div className="boxes" style={styles.boxes}>
                                            </div>
                                        </div>
                                        <div className="door" style={styles.door}>
                                            <div className="boxes" style={styles.boxes}>
                                            </div>
                                        </div>
                                    </div>
                                </div> :
                                <div id="slotMachineContainer"
                                     style={{...styles.slotMachineContainer, minHeight: '0px', width: '70%'}}>
                                    <div id="slotGameBoxes" style={{
                                        ...styles.slotBoxContainer,
                                        width: '67%',
                                        maxWidth: '67%',
                                        maxHeight: '40%',
                                        marginTop: '2%',
                                        marginRight: '8%',
                                        marginBottom: '0%'
                                    }}>
                                        <div className="door"
                                             style={{...styles.door, maxWidth: '30%', maxHeight: '80%', margin: '5px'}}>
                                            <div className="boxes" style={styles.boxes}>
                                            </div>
                                        </div>
                                        <div className="door"
                                             style={{...styles.door, maxWidth: '30%', maxHeight: '80%', margin: '5px'}}>
                                            <div className="boxes" style={styles.boxes}>
                                            </div>
                                        </div>
                                        <div className="door"
                                             style={{...styles.door, maxWidth: '30%', maxHeight: '80%', margin: '5px'}}>
                                            <div className="boxes" style={styles.boxes}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        }
                        {this.props.currentStage === slotMachineStage.getBasicInfo ?
                            <div style={{
                                ...styles.formAndButtonContainer,
                                height: this.state.isCrmScreen ? '20%' : '40%',
                                justifyContent:this.props.utmData.utmSource === "dine_in_app" ? 'space-evenly' : 'flex-start'
                            }}>
                                {
                                    this.state.flow === "2" ?
                                        <p style={{fontSize: '17px', fontWeight: 'bold', marginBottom: '10px'}}>Yay! You have won a special offer.</p>:<div/>
                                }
                                {!this.state.isContactFromUrl ?
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'flex-start',
                                        width: '80%'
                                    }}>
                                        <p style={{
                                            padding: '5px 0',
                                            color: '#0a6001',
                                            fontWeight: 'bolder',
                                            fontSize: '15px'
                                        }}>MOBILE NUMBER*</p>
                                        <div style={styles.phoneNumberBox}>
                                            <div style={styles.phoneNumberBoxChildLeft}>
                                                <p style={{color: '#fff', fontSize: '13px'}}>+91</p>
                                            </div>
                                            <div style={styles.phoneNumberBoxChildRight}>
                                                <input
                                                    type={'tel'}
                                                    maxLength={10} id="phoneNumber"
                                                    autoComplete="off"
                                                    onChange={()=> this.onPhoneInputChange()}
                                                    style={styles.phoneNumberTextField}
                                                />
                                            </div>
                                        </div>
                                        <div style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            justifyContent: 'flex-start',
                                            margin: '5px'
                                        }}>
                                            <div style={{width: '30px'}}>
                                                <div style={{
                                                    ...styles.checkBox,
                                                    backgroundColor: this.state.optWhatsapp ? "#008000" : "#e9e9e9"
                                                }} onClick={this.processCheckBox}>
                                                    {
                                                        this.state.optWhatsapp ?
                                                            <img src={"./../../../img/SlotGame/tick.png"}
                                                                 height={'13px'} width={'13px'}/> : <div/>
                                                    }
                                                </div>
                                            </div>
                                            <p style={styles.smallGreyText}>Offer Details will be sent via sms/whatsapp.</p>
                                        </div>
                                    </div> : <div/>
                                }
                                {this.state.flow === "1" && !this.state.isContactFromUrl ?
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'flex-start',
                                        width: '80%'
                                    }}>
                                        <p style={{
                                            padding: '5px 0',
                                            color: '#0a6001',
                                            fontWeight: 'bolder',
                                            fontSize: '15px'
                                        }}>EMAIL ID*</p>
                                        <div style={styles.phoneNumberBox}>
                                            <div style={styles.phoneNumberBoxChildLeft}>
                                                <p style={{color: '#fff', fontSize: '18px'}}>@</p>
                                            </div>
                                            <div style={styles.phoneNumberBoxChildRight}>
                                                <input
                                                    id="emailId"
                                                    autoComplete="off"
                                                    maxLength={60}
                                                    style={{...styles.phoneNumberTextField,textTransform: 'lowercase'}}
                                                />
                                            </div>
                                        </div>
                                    </div> : <div/>
                                }
                                <div style={{
                                    ...styles.spinButton,
                                    backgroundColor: this.props.enableShowOfferButton ? 'green' : 'grey'
                                }} onClick={() => this.getOffer()}>
                                    {
                                        this.props.isButtonLoading ?
                                            <div className="spinner-border text-light" role="status">
                                            </div> :
                                            <p style={styles.spinButtonText}>{this.state.flow !== "2" ? "PLAY AND WIN" : "GET OFFER"}</p>
                                    }
                                </div>
                                { this.showGameRule(this.props.utmData.utmSource)}
                            </div> : <div/>
                        }
                        {
                            this.props.currentStage === slotMachineStage.onlySpinPage ?
                                <div style={styles.formAndButtonContainer}>
                                    <p style={{fontSize: '17px', fontWeight: 'bold', marginBottom: '10px',color:'#7a0202'}}>100% guaranteed prizes for everyone.</p>
                                    <div style={{...styles.spinButton, backgroundColor:this.props.enableShowOfferButton ? 'green':'grey'}} onClick={()=>this.getOffer()}>
                                        {
                                            this.props.isButtonLoading ?
                                                <div class="spinner-border text-light" role="status">
                                                </div>:
                                                <p style={styles.spinButtonText}>PLAY AND WIN</p>
                                        }
                                    </div>
                                </div>:<div/>
                        }
                        {this.props.currentStage === slotMachineStage.getOTP ?
                            <div style={styles.formAndButtonContainer}>
                                <p style={{fontSize: '17px', fontWeight: 'bold', marginBottom: '10px'}}>Enter details to
                                    reveal your offer</p>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'flex-start',
                                    width: '80%',
                                    marginBottom: '10px'
                                }}>
                                    <p style={{
                                        padding: '5px 0',
                                        color: '#0a6001',
                                        fontWeight: 'bolder',
                                        fontSize: '15px'
                                    }}>NAME*</p>
                                    <div style={styles.phoneNumberBox}>
                                        <div style={styles.phoneNumberBoxChildLeft}>
                                            <img width={'40%'} src={"./../../../img/SlotGame/nameIcon.svg"}/>
                                        </div>
                                        <div style={{...styles.phoneNumberBoxChildRight}}>
                                            <input
                                                id="name"
                                                autoComplete="off"
                                                maxLength={60}
                                                style={{...styles.phoneNumberTextField,textTransform: 'capitalize'}}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'flex-start',
                                    width: '80%'
                                }}>
                                    <p style={{
                                        padding: '5px 0',
                                        color: '#0a6001',
                                        fontWeight: 'bolder',
                                        fontSize: '15px'
                                    }}>OTP*</p>
                                    <div style={styles.phoneNumberBox}>
                                        <div style={styles.phoneNumberBoxChildLeft}>
                                            <p style={{color: '#fff', fontSize: '18px'}}>****</p>
                                        </div>
                                        <div style={{...styles.phoneNumberBoxChildRight}}>
                                            <input
                                                type={'tel'}
                                                maxLength={4} id="otp"
                                                autoComplete="off"
                                                style={styles.phoneNumberTextField}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div style={{...styles.spinButton, marginTop: '20px'}} onClick={() => this.getOffer()}>
                                    {
                                        this.state.revealing ?
                                            <div className="spinner-border text-light" role="status">
                                            </div> :
                                            <p style={styles.spinButtonText}>REVEAL OFFER</p>
                                    }
                                </div>
                            </div> : <div/>
                        }
                    </div>:
                    <div id="parentContainer" style={styles.parentContainerOffer}>
                        {this.state.isCrmScreen?
                            <div onClick={()=>{window.ReactNativeWebView.postMessage(JSON.stringify({action : "SKIP"}))}} style={{height:"60px",width:'60px', display:'flex',
                                justifyContent:'center',alignItems:'center',backgroundColor:'#ffffff8f',position:'absolute',top:'10px',left:'10px'}}>
                                <p style={{fontSize:'35px'}}>X</p>
                            </div>:<div/>
                        }
                        <audio id="crowdCheer" src={"./../../../img/SlotGame/crowdCheer.mp3"}/>
                        {this.props.currentStage === slotMachineStage.showOffer ?
                            <div style={styles.gameOfferScreenContainer} id="offerCanvas">
                                {this.props.specialOffer.offerType === "NBO"?this.getNBOGameOfferScreenView(this.props.specialOffer) :
                                    this.props.specialOffer.offerType === "DNBO"?this.getDNBOGameOfferScreenView(this.props.specialOffer):
                                        this.props.specialOffer.offerType === "CHAAYOS_CASH"?this.getCashGameOfferScreenView(this.props.specialOffer):
                                            this.props.specialOffer.offerType === "MEMBERSHIP"?  this.getMembershipGameOfferScreenView(this.props.specialOffer):
                                                this.props.getNoOfferPage(this.prop.specialOffer)
                                }
                                <div style={{...styles.tncContainer,marginTop:'30px'}}>
                                    <p style={{...styles.tncHeading, fontSize:'27px',fontWeight:'900'}}>Terms and Conditions</p>
                                    <p id="tnc" style={{...styles.tncText, fontSize:'23px'}}/>
                                </div>
                            </div> : <div/>
                        }
                        {/*{this.props.currentStage === slotMachineStage.showOffer && this.props.showNoOffer ?*/}
                        {/*    <div  style={styles.modal}>*/}
                        {/*        <div  style={{*/}
                        {/*            ...styles.modalBody,*/}
                        {/*            height: !this.state.isCrmScreen ? modalBodyHeight : '100%',*/}
                        {/*            justifyContent: 'flex-end',*/}
                        {/*            textAlign: 'center',*/}
                        {/*            backgroundImage: `url("./../../../img/SlotGame/noOfferPop.webp")`*/}
                        {/*        }}>*/}
                        {/*            { this.showGameRule(this.props.utmData.utmSource)}*/}
                        {/*            {this.showDownloadAppButtons(this.props.utmData.utmMedium,'60px')}*/}
                        {/*        </div>*/}
                        {/*    </div> : <div/>*/}
                        {/*}*/}
                        {/*{this.props.currentStage === slotMachineStage.showOffer && this.props.showNBOOffer ?*/}
                        {/*    <div  style={styles.modal}>*/}
                        {/*        <div  style={{*/}
                        {/*            ...styles.modalBody,*/}
                        {/*            height: !this.state.isCrmScreen ? modalBodyHeight : '100%',*/}
                        {/*            justifyContent: 'space-between',*/}
                        {/*            textAlign: 'center',*/}
                        {/*            backgroundImage: `url("./../../../img/SlotGame/offerFoundBg.webp")`*/}
                        {/*        }}>*/}
                        {/*            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.youHaveWonText,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*            }}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.offerTextStyle,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? this.getOfferTextSize(this.props.specialOffer.text) : '50px'*/}
                        {/*            }}>{this.props.specialOffer.text}</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.youHaveWonText,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*            }}>on your*/}
                        {/*                next {this.props.specialOffer.maxUsage > 1 ? this.props.specialOffer.maxUsage : ""} visit{this.props.specialOffer.maxUsage > 1 ? "s" : ""} at*/}
                        {/*                Chaayos</p>*/}
                        {/*            <div style={{*/}
                        {/*                ...styles.couponCodeContainer,*/}
                        {/*                width: !this.state.isCrmScreen ? '' : '60%'*/}
                        {/*            }}>*/}
                        {/*                <div style={{width: '80%', marginRight: '20px'}}>*/}
                        {/*                    <p style={{*/}
                        {/*                        ...styles.useCodeText,*/}
                        {/*                        fontSize: !this.state.isCrmScreen ? '16px' : '30px'*/}
                        {/*                    }}>Use Code</p>*/}
                        {/*                </div>*/}
                        {/*                <div style={styles.couponCodeBox}>*/}
                        {/*                    <p style={{*/}
                        {/*                        ...styles.couponCodeText,*/}
                        {/*                        fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*                    }}>{this.props.specialOffer.offerCode}</p>*/}
                        {/*                </div>*/}
                        {/*            </div>*/}
                        {/*            {this.state.isCrmScreen ?*/}
                        {/*                <img src={"./../../../img/SlotGame/twoCups.webp"} height={'15%'}/> : <div/>*/}
                        {/*            }*/}
                        {/*            <div style={{*/}
                        {/*                ...styles.tncContainer,*/}
                        {/*                width: !this.state.isCrmScreen ? '' : '70%'*/}
                        {/*            }}>*/}
                        {/*                <p style={styles.tncHeading}>Term and Conditions</p>*/}
                        {/*                <p id="tnc" style={styles.tncText}></p>*/}
                        {/*            </div>*/}
                        {/*            {*/}
                        {/*                !this.state.isCrmScreen && !this.props.utmData.utmSource==="dine_in_app" ?*/}
                        {/*                    <p>Take a screenshot for future use</p> : <div/>*/}
                        {/*            }*/}
                        {/*            { this.showGameRule(this.props.utmData.utmSource)}*/}
                        {/*            {this.showExploreButton(this.props.utmData.utmMedium)}*/}
                        {/*            {this.showDownloadAppButtons(this.props.utmData.utmMedium,'0px')}*/}
                        {/*        </div>*/}
                        {/*    </div> : <div/>*/}
                        {/*}*/}
                        {/*{this.props.currentStage === slotMachineStage.showOffer && this.props.showDNBOOffer ?*/}
                        {/*    <div  style={styles.modal}>*/}
                        {/*        <div  style={{*/}
                        {/*            ...styles.modalBody,*/}
                        {/*            height: !this.state.isCrmScreen ? modalBodyHeight : '100%',*/}
                        {/*            justifyContent: 'space-between',*/}
                        {/*            textAlign: 'center',*/}
                        {/*            backgroundImage: `url("./../../../img/SlotGame/offerFoundBg.webp")`*/}
                        {/*        }}>*/}
                        {/*            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.youHaveWonText,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*            }}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.offerTextStyle,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? this.getOfferTextSize(this.props.specialOffer.text) : '50px'*/}
                        {/*            }}>{this.props.specialOffer.text}</p>*/}
                        {/*            <div style={{width:'100%',display:'flex',justifyContent:'center',alignItems:'center'}}>*/}
                        {/*                <p style={{*/}
                        {/*                    ...styles.youHaveWonText,*/}
                        {/*                    fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*                }}>on your*/}
                        {/*                    next {this.props.specialOffer.maxUsage > 1 ? this.props.specialOffer.maxUsage : ""} order{this.props.specialOffer.maxUsage > 1 ? "s" : ""} from*/}
                        {/*                </p>*/}
                        {/*                <img style={{width:'25%',marginLeft:'10px'}} src={'./../../../img/SlotGame/zomatoIcon.webp'}/>*/}
                        {/*            </div>*/}
                        {/*            <div style={{*/}
                        {/*                ...styles.couponCodeContainer,*/}
                        {/*                width: !this.state.isCrmScreen ? '' : '60%'*/}
                        {/*            }}>*/}
                        {/*                <div style={{width: '80%', marginRight: '20px'}}>*/}
                        {/*                    <p style={{*/}
                        {/*                        ...styles.useCodeText,*/}
                        {/*                        fontSize: !this.state.isCrmScreen ? '16px' : '30px'*/}
                        {/*                    }}>Use Code</p>*/}
                        {/*                </div>*/}
                        {/*                <div style={styles.couponCodeBox}>*/}
                        {/*                    <p style={{*/}
                        {/*                        ...styles.couponCodeText,*/}
                        {/*                        fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*                    }}>{this.props.specialOffer.offerCode}</p>*/}
                        {/*                </div>*/}
                        {/*            </div>*/}
                        {/*            {this.state.isCrmScreen ?*/}
                        {/*                <img src={"./../../../img/SlotGame/twoCups.webp"} height={'15%'}/> : <div/>*/}
                        {/*            }*/}
                        {/*            <div style={{*/}
                        {/*                ...styles.tncContainer,*/}
                        {/*                width: !this.state.isCrmScreen ? '90%' : '70%'*/}
                        {/*            }}>*/}
                        {/*                <p style={styles.tncHeading}>Term and Conditions</p>*/}
                        {/*                <p id="tnc" style={styles.tncText}></p>*/}
                        {/*            </div>*/}
                        {/*            {*/}
                        {/*                !this.state.isCrmScreen && !this.props.utmData.utmSource==="dine_in_app" ?*/}
                        {/*                    <p>Take a screenshot for future use</p> : <div/>*/}
                        {/*            }*/}
                        {/*            { this.showGameRule(this.props.utmData.utmSource)}*/}
                        {/*            {this.showExploreButton(this.props.utmData.utmMedium)}*/}
                        {/*            {this.showDownloadAppButtons(this.props.utmData.utmMedium,'0px')}*/}
                        {/*        </div>*/}
                        {/*    </div> : <div/>*/}
                        {/*}*/}
                        {/*{this.props.currentStage === slotMachineStage.showOffer && this.props.showMembershipOffer ?*/}
                        {/*    <div  style={styles.modal}>*/}
                        {/*        <div  style={{*/}
                        {/*            ...styles.modalBody,*/}
                        {/*            height: !this.state.isCrmScreen ? modalBodyHeight : '100%',*/}
                        {/*            justifyContent: 'space-between',*/}
                        {/*            textAlign: 'center',*/}
                        {/*            backgroundImage: `url("./../../../img/SlotGame/offerFoundBg.webp")`*/}
                        {/*        }}>*/}
                        {/*            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>*/}
                        {/*            <p style={styles.youHaveWonText}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.offerTextStyle,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '5vw' : '40px',*/}
                        {/*                marginTop: '10px'*/}
                        {/*            }}>Chaayos Select Membership</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.offerTextStyle,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '25px',*/}
                        {/*                marginBottom: '10px'*/}
                        {/*            }}>Worth 199&#x20b9;</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.youHaveWonText,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '18px'*/}
                        {/*            }}>for*/}
                        {/*                next {this.getDays(this.props.specialOffer.validityFrom, this.props.specialOffer.validityTill)} days </p>*/}
                        {/*            {this.state.isCrmScreen ?*/}
                        {/*                <img src={"./../../../img/SlotGame/twoCups.webp"} height={'15%'}/> : <div/>*/}
                        {/*            }*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.offerTextStyle,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '20px'*/}
                        {/*            }}>{this.props.specialOffer.text}</p>*/}
                        {/*            <div style={{*/}
                        {/*                ...styles.tncContainer,*/}
                        {/*                width: !this.state.isCrmScreen ? '90%' : '70%'*/}
                        {/*            }}>*/}
                        {/*                <p style={styles.tncHeading}>Term and Conditions</p>*/}
                        {/*                <p id="tnc" style={styles.tncText}></p>*/}
                        {/*            </div>*/}
                        {/*            {*/}
                        {/*                !this.state.isCrmScreen && !this.props.utmData.utmSource==="dine_in_app" ?*/}
                        {/*                    <p>Take a screenshot for future use</p> : <div/>*/}
                        {/*            }*/}
                        {/*            { this.showGameRule(this.props.utmData.utmSource)}*/}
                        {/*            {this.showExploreButton(this.props.utmData.utmMedium)}*/}
                        {/*            {this.showDownloadAppButtons(this.props.utmData.utmMedium,'0px')}*/}
                        {/*        </div>*/}
                        {/*    </div> : <div/>*/}
                        {/*}*/}
                        {/*{this.props.currentStage === slotMachineStage.showOffer && this.props.showChaayosCashOffer ?*/}
                        {/*    <div  style={styles.modal}>*/}
                        {/*        <div  style={{*/}
                        {/*            ...styles.modalBody,*/}
                        {/*            height: !this.state.isCrmScreen ? modalBodyHeight : '100%',*/}
                        {/*            justifyContent: 'space-between',*/}
                        {/*            textAlign: 'center',*/}
                        {/*            backgroundImage: `url("./../../../img/SlotGame/offerFoundBg.webp")`*/}
                        {/*        }}>*/}
                        {/*            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'30%'}/>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.youHaveWonText,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '15px' : '20px'*/}
                        {/*            }}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>*/}
                        {/*            <p style={{*/}
                        {/*                ...styles.offerTextStyle,*/}
                        {/*                fontSize: !this.state.isCrmScreen ? '50px' : '85px'*/}
                        {/*            }}>&#x20b9; {this.props.specialOffer.chaayosCash}* </p>*/}
                        {/*            <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center',width:'90%'}}>*/}
                        {/*                <div style={{minHeight: '3px', minWidth: '15%', backgroundColor: '#C2541A'}}/>*/}
                        {/*                <div>*/}
                        {/*                    <p style={{*/}
                        {/*                        fontSize: !this.state.isCrmScreen ? '18px' : '25px',*/}
                        {/*                        marginLeft:'10px',*/}
                        {/*                        marginRight:'10px'*/}
                        {/*                    }}>Chaayos Cash</p>*/}
                        {/*                </div>*/}
                        {/*                <div style={{minHeight: '3px', minWidth: '15%', backgroundColor: '#C2541A'}}/>*/}
                        {/*            </div>*/}
                        {/*            <p style={styles.youHaveWonText}>Redeem in next visit & get free items</p>*/}
                        {/*            {this.state.isCrmScreen ?*/}
                        {/*                <img src={"./../../../img/SlotGame/twoCups.webp"} height={'15%'}/> : <div/>*/}
                        {/*            }*/}
                        {/*            <div style={{*/}
                        {/*                ...styles.tncContainer,*/}
                        {/*                width: !this.state.isCrmScreen ? '90%' : '70%',*/}
                        {/*                padding: !this.state.isCrmScreen ? '10px 0px' : '15px 0px'*/}
                        {/*            }}>*/}
                        {/*                <p style={styles.tncHeading}>{"Term and Conditions"}</p>*/}
                        {/*                <p id="tnc" style={styles.tncText}></p>*/}
                        {/*            </div>*/}
                        {/*            {*/}
                        {/*                !this.state.isCrmScreen && !this.props.utmData.utmSource==="dine_in_app" ?*/}
                        {/*                    <p>Take a screenshot for future use</p> : <div/>*/}
                        {/*            }*/}
                        {/*            { this.showGameRule(this.props.utmData.utmSource)}*/}
                        {/*            {this.showExploreButton(this.props.utmData.utmMedium)}*/}
                        {/*            {this.showDownloadAppButtons(this.props.utmData.utmMedium,'0px')}*/}
                        {/*        </div>*/}
                        {/*    </div> : <div/>*/}
                        {/*}*/}
                        {this.props.currentStage === slotMachineStage.showThankYouPage?
                            <div  style={styles.modal}>
                                <div  style={{
                                    ...styles.modalBody,
                                    height: !this.state.isCrmScreen ? modalBodyHeight : '100%',
                                    justifyContent: 'space-between',
                                    textAlign: 'center',
                                    backgroundImage: `url("./../../../img/SlotGame/thankYouBackground.webp")`
                                }}>
                                    <img src={"./../../../img/SlotGame/10YearsLogo.webp"} height={'20%'}/>
                                    <p style={styles.thankYouHeading}>Thank you for being a part of Chaayos 10th Birthday Celebration</p>
                                    <p style={styles.giftDistributionHeading}>Over 10 Crore worth gifts distributed</p>
                                    <p style={styles.offersText}>1 Lucky winner got <span style={{...styles.offersText, fontWeight:'bold'}}>1 Lakh Chaayos Cash</span></p>
                                    <p style={styles.offersText}>More than <span style={{...styles.offersText, fontWeight:'bold'}}>50K</span>of <span style={{...styles.offersText, fontWeight:'bold'}}>Buy 1 Get 1</span></p>
                                    <p style={styles.offersText}>More than <span style={{...styles.offersText, fontWeight:'bold'}}>1 Lakh</span> of <span style={{...styles.offersText, fontWeight:'bold'}}>Flat 50% off</span></p>
                                    <p style={styles.offersText}>More than <span style={{...styles.offersText, fontWeight:'bold'}}>1 Lakh</span> of <span style={{...styles.offersText, fontWeight:'bold'}}>Rs. 250 Off on Chaayos Delivery</span></p>
                                    {this.redirectToNBO()}
                                    <p>Redirecting you in <span style={{fontWeight:'bold', fontSize:'16px'}}>{this.state.redirectionSecond}</span> sec</p>
                                </div>
                            </div> : <div/>
                        }
                    </div>


        );
    }

    showGameRule(utmSource) {
        if(utmSource !== "dine_in_app"){
            return (<div/>)
        }
        return (
            <div style={styles.gameTncContainer}>
                <p style={{...styles.gameTncText,textAlign:'center',textDecoration:'underline',marginBottom:'5px',fontSize:'11px'}}>Spin and Win Rules</p>
                <p style={styles.gameTncText}>1. This game does not includes any CASH Price, hence all the winnings are discounts benefits for users.</p>
                <p style={styles.gameTncText}>2. Chaayos Cash is a Flat discount of Rs.100 in each order which comes with a validity.</p>
                <p style={styles.gameTncText}>3. Each user can play this game multiple times to try their luck for offers.</p>
                <p style={styles.gameTncText}>4. This is not a betting game , hence a simple illustration of slot machine.</p>
                <p style={styles.gameTncText}>5. This contest/game is only limited to Chaayos Customers.</p>
            </div>
        )
    }
}

export function recordExploreMenuClicked(deviceType){
    return dispatch =>{
        dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.exploreMenuClicked,deviceType,"",function (){
            window.open('https://zomato.onelink.me/xqzv/5fbcb81a');
        }))
    }
}

