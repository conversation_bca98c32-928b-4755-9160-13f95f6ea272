import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux";
import Slider from "react-slick";
import Select from 'react-select';
import MobileHeader from "./MobileHeader";
import SidebarLayout from "../SidebarLayout";
import appUtil from "../../AppUtil";
import * as LocalityActions from "../../actions/LocalityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as UtilityActions from "../../actions/UtilityActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        showLocationWrapper: store.localityReducer.showLocationWrapper,
        criteria: store.localityReducer.criteria,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedCity: store.localityReducer.selectedCity,
        selectedOutlet: store.localityReducer.selectedOutlet,
        localities: store.localityReducer.localities,
        cities: store.localityReducer.cities,
        outlets: store.localityReducer.outlets,
        showLocality:store.localityReducer.showLocality,
        showOutlet:store.localityReducer.showOutlet,
        showError:store.localityReducer.showError,
        showLoader:store.localityReducer.showLoader,
        sidebarOpen: store.sidebarReducer.sidebarOpen,
        cityStateMap: store.localityReducer.cityStateMap
    };
})
export default class MobileHomeLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            citiesList:[],
            localitiesList: [],
            outletsList:[],
        };
        this.init = this.init.bind(this);
        this.loadCities = this.loadCities.bind(this);
        this.formatCities = this.formatCities.bind(this);
        this.selectCity = this.selectCity.bind(this);
        this.switchCriteria = this.switchCriteria.bind(this);
        this.goToMenu = this.goToMenu.bind(this);
        this.goToSearch = this.goToSearch.bind(this);
    }

    init() {
        //console.log("Initialize MobileHomeLayout");
        //console.log("window.location.pathname is :: " +window.location.pathname);
        var criteria = this.props.criteria;
        if(appUtil.checkEmpty(criteria)){
            criteria = "TAKE_AWAY";
        }
        this.switchCriteria(criteria);
        if(this.props.criteria!="DELIVERY"){
            this.props.dispatch(LocalityActions.loadCitiesList(criteria));
        }
    }

    formatCities(){
        this.state.citiesList = [];
        const selectedCity = this.props.selectedCity!=null?this.props.selectedCity.city:null;
        this.props.cities.map((item, index) =>
        {
            if(selectedCity==item){
                this.state.citiesList.push(<div key={index} class="cityBtn active" onClick={this.selectCity.bind(this, item)}>{item}</div>);
            }else{
                this.state.citiesList.push(<div key={index} class="cityBtn" onClick={this.selectCity.bind(this, item)}>{item}</div>);
            }
        });
    }

    loadCities(){
        if(this.props.cities.length==0){
            this.props.dispatch(LocalityActions.loadCitiesList(this.props.criteria));
            if(this.props.selectedCity!=null && this.props.selectedCity.city!=null && this.props.selectedCity.state!=null){
                this.props.dispatch(LocalityActions.setCity(this.props.selectedCity.city,this.props.selectedCity.state));
            }
        }else{
            this.props.dispatch(LocalityActions.hideError());
        }
    }

    selectCity(val){
        if(val!=null){
            this.props.dispatch(LocalityActions.selectCity(val, this.props.cityStateMap[val]));
            if(this.props.criteria=="DELIVERY"){
                this.props.dispatch(LocalityActions.setShowLocality(true));
            }else{
                this.props.dispatch(LocalityActions.setShowOutlet(true));
                this.props.dispatch(LocalityActions.loadOutletsList(val));
            }
        }else{
        }
    }

    switchCriteria(val){
        this.props.dispatch(LocalityActions.setCriteria(val));
        if(val=="DELIVERY"){
            this.props.dispatch(LocalityActions.setShowLocality(true));
        }else{
            this.props.dispatch(LocalityActions.setShowOutlet(true));
        }
        this.props.dispatch(OutletMenuActions.discardUnit());
    }

    goToMenu(){
        if(this.props.criteria==null){
            this.props.dispatch(UtilityActions.showPopup("Please select delivery or pickup.", "info"));
        }else if(this.props.criteria=="DELIVERY" && this.props.selectedLocality==null){
            this.props.dispatch(UtilityActions.showPopup("Please select delivery location.", "info"));
        }else if((this.props.criteria=="TAKE_AWAY" || this.props.criteria=="TAKE_AWAY") && this.props.selectedOutlet==null){
            this.props.dispatch(UtilityActions.showPopup("Please select outlet for dine in/pickup.", "info"));
        }else{
            browserHistory.push("/menu");
        }
    }

    goToSearch(){
        document.getElementsByTagName("body")[0].scrollTop = 0;
        browserHistory.push("/search");
    }

    redirect(link) {
        window.location.href = link;
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.init();
    }

    componentDidUpdate(){
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"home",device:"mobile",custom:true});
    }

    render() {

        var settings = {
            dots: true,
            infinite: true,
            speed: 500,
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 7000
        };

        var cityData = [];
        if(this.props.cities.length>0){
            this.formatCities();
        }

        return (
            <div id="appRoot">
                <SidebarLayout />
                <MobileHeader menu={true} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                <Slider {...settings}>
                    {/*<div class="imgSlide" onClick={this.redirect.bind(this, "https://pages.razorpay.com/giveback-chaayos")}
                         style={{backgroundImage: 'url(../../../img/banner/givebackMobile.jpg)'}}></div>*/}

                    <div className="imgSlide" onClick={this.redirect.bind(this, "https://bazaar.chaayos.com")}
                         style={{backgroundImage: 'url(../../../img/banner/chaayosBazaarMobile.jpg)'}} />
                    <div class="imgSlide" style={{backgroundImage: 'url(../../../img/banner/contactlessChaayosAppMobile.jpg)'}} />
                    <div class="imgSlide" style={{backgroundImage: 'url(../../../img/banner/meriWaliChaiMobile.jpg)'}} />
                    <div class="imgSlide" style={{backgroundImage: 'url(../../../img/banner/loyaltyMobile.jpg)'}} />
                    <div class="imgSlide" style={{backgroundImage: 'url(../../../img/banner/baarishAurChaiMobile.jpg)'}} />
                    <div class="imgSlide" style={{backgroundImage:'url(../../../img/banner/merchandiseMobile.jpg)'}} />
                </Slider>
                <div class="actionAreaContainer">
                    <p class="actionAreaHeader">I want my order for</p>

                    <div className="actionInputWrapper">
                        <div className="radioLabels" onClick={this.switchCriteria.bind(this, "TAKE_AWAY")}>
                            <input type="radio" id="pickup" name="orderType" value="TAKE_AWAY"
                                   className="addrTypeInput"
                                   readOnly checked={this.props.criteria == "TAKE_AWAY"}/>
                            <label htmlFor="pickup"><span><span></span></span>Take Away</label>
                        </div>
                        <div className="radioLabels" onClick={this.switchCriteria.bind(this, "DINE_IN")}>
                            <input type="radio" id="dinein" name="orderType" value="DINE_IN"
                                   className="addrTypeInput"
                                   readOnly checked={this.props.criteria == "DINE_IN"}/>
                            <label htmlFor="dinein"><span><span></span></span>Dine In</label>
                        </div>
                        <div className="radioLabels" onClick={this.switchCriteria.bind(this, "DELIVERY")}>
                            <input type="radio" id="delivery" name="orderType" value="DELIVERY"
                                   className="addrTypeInput"
                                   readOnly checked={this.props.criteria == "DELIVERY"}/>
                            <label htmlFor="delivery"><span><span></span></span>Delivery</label>
                        </div>
                    </div>

                    <div class="citySelector">
                        {this.state.citiesList}
                    </div>
                    {this.props.showLocality?(
                        <div class="localitySelector" onClick={this.goToSearch.bind(this)}>
                            {this.props.selectedLocality!=null?this.props.selectedLocality.label:"Select Locality"}
                        </div>
                    ):null}
                    {this.props.showOutlet?(
                        <div class="outletSelector" onClick={this.goToSearch.bind(this)}>
                            {this.props.selectedOutlet!=null?this.props.selectedOutlet.label:"Select Outlet"}
                        </div>
                    ):null}
                    {(this.props.showLocality || this.props.showOutlet) && this.props.selectedOutlet!=null?(
                        <div class="btn btn-primary" style={{marginTop:"30px", marginBottom:"30px"}} onClick={this.goToMenu.bind(this)}>Place Order</div>
                    ):(null)}
                    {this.props.showLoader?(
                        <div class="text-center load8 loader"></div>
                    ):(null)}
                    {this.props.showError?(
                        <p class="errorMessage">Error loading data. Please <span>Try again!</span></p>
                    ):(null)}
                </div>
            </div>
        )
    }
}