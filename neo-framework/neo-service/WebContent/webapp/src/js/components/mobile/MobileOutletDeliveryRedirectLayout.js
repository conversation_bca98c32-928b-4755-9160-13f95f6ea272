import React from "react";
import {connect} from "react-redux";
import {browserHistory} from "react-router";
import $ from "jquery";
import _ from "lodash";
import MobileHeader from "./MobileHeader";
import SidebarLayout from "../SidebarLayout";
import MobileCustomizationModal from "./MobileCustomizationModal";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as CustomizationModalActions from "../../actions/CustomizationModalActions";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as LocalityActions from "../../actions/LocalityActions";
import {showPopup} from "../../actions/UtilityActions";
import * as CampaignManagementActions from "../../actions/CampaignManagementActions";
import appUtil from "../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import productService from "../../service/ProductService";

@connect((store) => {
    return {
        redirectDetails: store.outletMenuReducer.redirectDetails,
    };
})
export default class MobileOutletDeliveryRedirectLayout extends React.Component {

    constructor() {
        super();
        this.state = {};
        this.init = this.init.bind(this);
        this.redirectWeb = this.redirectWeb.bind(this);
    }

    init() {
        const params = this.props.props.params;
        this.props.dispatch(OutletMenuActions.getZomatoRedirectLink({id:params.unitId}, true));
    }

    redirectWeb() {
        this.props.dispatch(OutletMenuActions.redirectToZomatoWeb(this.props.redirectDetails, true));
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.init();
    }

    componentDidMount() {
        trackUtils.trackPageView({page: "deliveryRedirect", device: "mobile", custom: true});
    }


    render() {
        return (
            <div id="appRoot">
                <div class="staticHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false}
                                  props={this.props.props}/>
                </div>
                <div style={{background: "#FFF", padding: "100px 20px", textAlign: "center", fontSize: "18px"}}>
                    <div className="loaderWrapper">
                        <div class="load8 loader"></div>
                    </div>
                    <h1 style={{fontSize: "18px", marginBottom: "15px"}}>Please wait while
                        we redirect you to partner site for ordering.</h1>
                    <h3>Please click below button if you are not redirected
                        automatically within few seconds.</h3>
                    <input type="button" value="Order" class="redirectBtn" style={{
                        border: "none",
                        borderRadius: "50px",
                        padding: "15px",
                        color: "#FFF",
                        marginTop: "20px",
                        background: "darkolivegreen",
                        fontSize: "18px",
                        width: "200px",
                        cursor: "pointer"
                    }} onClick={this.redirectWeb.bind(this)}/>
                </div>
            </div>
        )
    }

}