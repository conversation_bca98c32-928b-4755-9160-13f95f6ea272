/**
 * Created by Chaayos on 06-12-2016.
 */
import React from "react";
import { connect } from "react-redux";
import browserHistory from "react-router/lib/browserHistory";
import MobileHeader from "./MobileHeader";
import * as CustomerActions from "../../actions/CustomerActions";
import * as UtilityActions from "../../actions/UtilityActions";
import * as CartManagementActions from "../../actions/CartManagementActions";
import appUtil from "../../AppUtil";
import StorageUtils from "../../utils/StorageUtils";
import trackUtils from "../../utils/TrackUtils";
import * as LocalityActions from "../../actions/LocalityActions";

@connect((store) => {
    return {
        sessionKey: store.customerReducer.sessionKey,
        addresses: store.customerReducer.addresses,
        selectedAddress: store.customerReducer.selectedAddress,
        status: store.customerReducer.status,
        addressViewType: store.customerReducer.addressViewType,
        unit:store.outletMenuReducer.unit,
    };
})
export default class MobileAddressesLayout extends React.Component {
    constructor(){
        super();
        this.state = {
        };
        this.selectAddress = this.selectAddress.bind(this);
        this.addNewAddress = this.addNewAddress.bind(this);
        this.proceedToPayment = this.proceedToPayment.bind(this);
    }

    selectAddress(addressId){
        this.props.dispatch(CustomerActions.selectAddress(addressId));
    }

    addNewAddress(linkPosition){
        trackUtils.trackAddAddressClicked(linkPosition);
        browserHistory.push("/newAddress");
    }

    proceedToPayment(){
        if(this.props.selectedAddress==null){
            this.props.dispatch(UtilityActions.showPopup("Please select an address for delivery.","info"));
        }else{
            this.props.dispatch(CartManagementActions.updateDeliveryAddressToCart(this.props.selectedAddress.id));
        }
    }

    componentWillMount(){
        window.scrollTo(0, 0);
        /*if(appUtil.checkEmpty(this.props.sessionKey)){
            browserHistory.push("/login");
        }else{
            this.props.dispatch(CustomerActions.removeSelectedAddress());
            if(this.props.sessionKey==null){
                browserHistory.push("/login");
            }else{
                this.props.dispatch(CustomerActions.getCustomerAddresses(StorageUtils.getCustomerId()));
            }
        }*/
        this.props.dispatch(LocalityActions.setCriteria("DINE_IN"));
        browserHistory.push("/menu");
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"addresses",device:"mobile",custom:true});
    }

    render (){
        var validAddresses = [];
        var validAddressItems = [];
        /*var disabledAddresses = [];
        var disabledAddressItems = [];*/
        if(!appUtil.checkEmpty(this.props.addresses)){
            if(this.props.addressViewType=="DELIVERY"){
                this.props.addresses.map((address) => {
                    if(address.unitId!=null && address.units.indexOf(this.props.unit.id)>=0){
                        validAddresses.push(address);
                    }/*else{
                     disabledAddresses.push(address);
                     }*/
                });
                validAddresses.map((address) => {
                    validAddressItems.push(
                        <div key={address.id} class="addressContainer" onClick={this.selectAddress.bind(this, address.id)}>
                            <div class="addressTypeWrapper">{address.addressType}</div>
                            <div class="radioWrapper">
                                <input type="radio" name="validAddress" readOnly={true} checked={(this.props.selectedAddress!=null && this.props.selectedAddress.id==address.id)?true:false} />
                                <label><span><span></span></span></label>
                            </div>
                            <div class="addressDetail">
                                {address.landmark!=null?(
                                    <div class="addressName">{address.landmark}</div>
                                ):(null)}
                                <div class="address">{address.line1}{address.line2!=null?",":null}<br />{address.locality},{address.city}
                                    {address.zipCode!=null?", "+address.zipCode:null}</div>
                            </div>
                        </div>
                    );
                });
                /*disabledAddresses.map((address) => {
                 disabledAddressItems.push(
                 <div key={address.id} class="addressContainer disabled">
                 <div class="addressBtn"></div>
                 <div class="addressDetail">
                 {address.landmark!=null?(
                 <div class="addressName">{address.landmark}</div>
                 ):(null)}
                 <div class="address">{address.line1}{address.line2!=null?",":null}<br />{address.locality},{address.city}, {address.zipCode!=null?","+address.zipCode:null}</div>
                 </div>
                 </div>
                 );
                 });*/
            }else{
                this.props.addresses.map((address) => {
                    validAddressItems.push(
                        <div key={address.id} class="addressContainer">
                            <div class="addressTypeWrapper">{address.addressType}</div>
                            <div class="addressDetail" style={{margin:"0px"}}>
                                {address.landmark!=null?(
                                    <div class="addressName">{address.landmark}</div>
                                ):(null)}
                                <div class="address">{address.line1}{address.line2!=null?",":null}<br />{address.locality}, {address.city}
                                    {address.zipCode!=null?", "+address.zipCode:null}</div>
                            </div>
                        </div>
                    );
                });
            }
        }

        return(
            <div>
                <div class="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                {(this.props.status==null || this.props.status=="PENDING")?(
                    <div class="mobilePageContainer">
                        <div class="ptop50">
                            <div class="loader load8"></div>
                        </div>
                        <p class="text-center">Fetching saved addresses.</p>
                    </div>
                ):(null)}
                {this.props.status=="FULFILLED"?(
                    <div class="mobilePageContainer">
                        {this.props.addressViewType=="DELIVERY"?(
                            <div class="mobilePageHead">Select Delivery Addresses</div>
                        ):(
                            <div class="mobilePageHead">Saved Addresses</div>
                        )}
                        {(this.props.addressViewType=="DELIVERY" && validAddresses.length>0) ||
                        (this.props.addressViewType=="PROFILE" && this.props.addresses.length>0)?(
                            <div>
                                <div style={{padding:"10px"}}>
                                    {this.props.addressViewType=="PROFILE"?(
                                        <span>{this.props.addresses.length + (this.props.addresses.length>1?" Addresses":" Address")}</span>
                                    ):(
                                        <span>{validAddresses.length + (validAddresses.length>1?" Addresses":" Address")}</span>
                                    )}
                                    <span style={{color:"#5e7e47", textDecoration:"underline"}} class="right" onClick={this.addNewAddress.bind(this,"TOP")}>Add Address</span>
                                </div>
                                {validAddressItems}
                            </div>
                        ):(
                            this.props.addressViewType=="DELIVERY"?(
                                <div class="alert info">No addresses matching your delivery location.</div>
                            ):(
                                <div class="alert info">No addresses available.</div>
                            )
                        )}
                        <div class="addNewAddressBtn" onClick={this.addNewAddress.bind(this,"BOTTOM")}>Add Address</div>
                        {(validAddresses.length>0) && (this.props.addressViewType=="DELIVERY")?(
                            <div class="btn btn-primary" style={{marginTop:'20px'}} onClick={this.proceedToPayment.bind(this)}>Deliver Here</div>
                        ):(null)}
                    </div>
                ):(null)}
                {this.props.status=="REJECTED"?(
                    <div class="mobilePageContainer">
                        <div class="alert error">Error getting addresses. Please try again later!</div>
                    </div>
                ):(null)}
            </div>
        )

    }
}

/*
{disabledAddresses.length>0?(
    <div>
        <div class="addressesSubHead">Can't be delivered to</div>
        {disabledAddressItems}
    </div>
):(null)}*/
