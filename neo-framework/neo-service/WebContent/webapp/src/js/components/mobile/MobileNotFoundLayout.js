import React from "react";
import {browserHistory} from "react-router";
import MobileHeader from "./MobileHeader";
import trackUtils from "../../utils/TrackUtils";

export default class MobileNotFoundLayout extends React.Component {

    constructor() {
        super();
        this.state = {
        };
        this.goHome = this.goHome.bind(this);
    }

    goHome() {
        browserHistory.push("/");
    }

    componentWillMount() {
        window.scrollTo(0, 0);
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"redirect",device:"mobile",custom:true});
    }

    render() {
        return (
            <div>
                <div class="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                <div class="mobilePageContainer text-center">
                    <div style={{margin:"0 auto", padding:"100px 0", fontSize:"16px"}}>
                        <div style={{fontSize: "118px", color:"#5e7e47"}}>404</div>
                        <p style={{textTransform:"uppercase", marginBottom:"30px", fontSize:"21px"}}>Page not found.</p>
                        <p style={{fontSize:"30px"}}>But...</p>
                        <p>You can find a Kadak cup of chai.</p>
                        <p>Just click the button below.</p>
                        <input type="button" style={{border:"none", borderRadius:"50px", padding:"15px", color:"#FFF", marginTop:"20px",
                            background:"darkolivegreen", fontSize:"18px", width:"200px", cursor:"pointer"}} value="Go Home" onClick={this.goHome.bind(this)} />
                    </div>
                </div>
            </div>
        )
    }
}