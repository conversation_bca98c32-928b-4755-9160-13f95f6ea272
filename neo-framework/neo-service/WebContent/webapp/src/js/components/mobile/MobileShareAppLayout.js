import React from "react";
import MobileHeader from "./MobileHeader";
import {browserHistory} from "react-router";
import trackUtils from "../../utils/TrackUtils";
import {connect} from "react-redux"

import {setCustomerDetail} from "../../actions/CustomerActions";

@connect((store) => {
    return {
        customer: store.customerReducer.customer,
    };
})

export default class MobileShareAppLayout extends React.Component {

    constructor() {
        super();
        this.goToLogIn = this.goToLogIn.bind(this);
        this.gotToSignUp = this.gotToSignUp.bind(this);
    }

    componentWillMount() {
        window.scrollTo(0, 0);
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"shareapp",device:"mobile",custom:true});
        this.props.dispatch(setCustomerDetail());
    }

    isUserLogIn() {
        this.props.dispatch(setCustomerDetail());
        const {customer} = this.props
        if (customer && customer.customerId) {
            return true;
        }
        return false;
    }

    getUserName() {
        this.props.dispatch(setCustomerDetail());
        const {customer} = this.props
        if (customer && customer.name) {
            return customer.name;
        }
        return 'NA';
    }

    goToLogIn() {
        browserHistory.push("/login?ref=shareApp");
    }

    gotToSignUp() {
        browserHistory.push("/login?ref=shareApp");
    }

    navigateToAppStore() {
        let path = "https://apps.apple.com/in/app/zomato-food-restaurant-finder/id434613896?_branch_match_id=782898633919361442";
        browserHistory.push(path);
    }

    navigateToGooglePlay() {
        browserHistory.push("https://play.google.com/store/apps/details?id=com.application.zomato&hl=en_IN");
    }

    renderUser() {
        const isUserLogIn = this.isUserLogIn();
        const userName = this.getUserName();
        if (isUserLogIn) {
            return <div class="headLine" style={{marginLeft: '50px', marginRight: '10px'}}>
                Welcome {userName}
            </div>
        }
        return <div>
            <button class="btn btn-default right" style={{marginTop: '15px', marginRight: '110px'}}
                    onClick={this.goToLogIn}>Log in
            </button>
            <button class="btn btn-default right" style={{marginTop: '15px', marginRight: '20px'}}
                    onClick={this.gotToSignUp}>Sign up
            </button>
        </div>
    }

    renderCheckBox() {
        return <div style={{marginTop: '15px', marginLeft: '10px', marginRight: '10px'}}>
            <div className="newAddressInputContainerr">
                <div className="radioLabels">
                    <input type="radio" id="email" value="EMAIL" className="userEmail"/>
                    <label htmlFor="email"><span><span></span></span>EMAIL</label>
                </div>
                <div className="radioLabels">
                    <input type="radio" id="phone" value="PHONE"/>
                    <label htmlFor="phone"><span><span></span></span>PHONE</label>
                </div>
            </div>
        </div>
    }

    renderShareAppLink() {
        return <div>
            <div class="headLine">Get the Chaayos App</div>
            <div class="loginSectionTagline">We will send you a link, open it on your phone to download the app.</div>
            <div class="contactContainer">
                <input id="emailInput" type="email" placeholder="Enter your email" maxLength="100"
                       autoComplete="off"/>
            </div>

            <div className="contactContainer">
                <input id="userContactInput" type="tel" placeholder="Enter Mobile No." maxLength="10"
                       autoComplete="off"/>
            </div>

            <div class="btn btn-primary" style={{marginTop: '16px'}}>
                Share App Link
            </div>

            <div className="loginSectionTagline">Download app from
            </div>

            <div>
                <button id="app_store_btn" className="btn btn-default right" style={{marginTop: '15px', marginRight: '110px'}}
                        onClick={this.navigateToAppStore}>App Store
                </button>
                <button id="google_play_btn" className="btn btn-default right" style={{marginTop: '15px', marginRight: '20px'}}
                        onClick={this.navigateToGooglePlay}>Google Play
                </button>
            </div>
        </div>
    }

    render() {
        return (
            <div>
                <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props}/>
                <div class="mobilePageContainer">
                    {this.renderUser()}
                    {this.renderCheckBox()}
                    {this.renderShareAppLink()}
                </div>
            </div>
        )
    }
}