/**
 * Created by Chaayos on 09-12-2016.
 */
import React from "react";
import { connect } from "react-redux";
import { browserHistory } from "react-router";
import MobileHeader from "./MobileHeader";
import appUtil from "../../AppUtil";
import * as UtilityActions from "../../actions/UtilityActions";
import * as PaymentActions from "../../actions/PaymentActions";
import * as CustomerActions from "../../actions/CustomerActions";
import trackUtils from "../../utils/TrackUtils";
import * as OrderManagementActions from "../../actions/OrderManagementActions";

@connect((store) => {
    return {
        criteria:store.localityReducer.criteria,
        cart:store.cartManagementReducer.cart,
        deviceKey:store.customerReducer.deviceKey,
        sessionKey:store.customerReducer.sessionKey,
        orderStatusTimeouts:store.orderManagementReducer.orderStatusTimeouts,
        membershipIds: store.membershipReducer.membershipIds,
        membershipData: store.membershipReducer.membershipData
    };
})
export default class MobilePaymentModesLayout extends React.Component {
    constructor(){
        super();
        this.state = {
        };
        this.payByRazorPay = this.payByRazorPay.bind(this);
        this.payByCash = this.payByCash.bind(this);
        this.payByPaytm = this.payByPaytm.bind(this);
    }

    payByRazorPay(type){
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        browserHistory.push("/payProcess");
        this.props.dispatch(PaymentActions.payByRazorPay(this.props.cart, type, this.props.orderStatusTimeouts));
    }

    payByPaytm(){
        this.props.dispatch(PaymentActions.setPaymentInitiated(true));
        browserHistory.push("/payProcess");
        this.props.dispatch(PaymentActions.payByPaytm(this.props.cart));
    }

    payByCash(){
        if(this.props.cart.orderDetail.transactionDetail.paidAmount>1500){
            try{trackUtils.trackCashDenied({amount:this.props.cart.orderDetail.transactionDetail.paidAmount});}catch(e){}
            this.props.dispatch(UtilityActions.showPopup("Orders with value greater than 1500 are not allowed on cash payment. Please pay online.","error"));
        }else{
            var props = this.props;
            var success = function(){
                props.dispatch(PaymentActions.setPaymentInitiated(true));
                browserHistory.push("/payProcess");
                props.dispatch(PaymentActions.payByCash(props.cart, props.orderStatusTimeouts));
            };
            this.props.dispatch(UtilityActions.showPrompt("Are you sure?", success, function(){}));
        }
    }

    componentWillMount(){
        window.scrollTo(0, 0);
        if(appUtil.checkEmpty(this.props.deviceKey)){
            window.location.href = "https://cafes.chaayos.com";
        }else if(appUtil.checkEmpty(this.props.cart)){
            browserHistory.push("/menu");
        }else if(appUtil.checkEmpty(this.props.sessionKey)){
            browserHistory.push("/login");
        }else{
            var len = 0;
            this.props.cart.orderDetail.orders.map((orderItem) => {
                if(orderItem.productId!=1043 && orderItem.productId!=1044){
                    len++;
                }
            });
            if(len==0){
                browserHistory.push("/menu");
            }
            if(this.props.cart.orderDetail.transactionDetail==null || this.props.cart.orderDetail.transactionDetail.paidAmount<1){
                browserHistory.push("/menu");
            }
            this.props.dispatch(OrderManagementActions.getMembershipProductIds('26254', () => {
            }))
        }
    }

    componentDidMount(){
        if(!appUtil.checkEmpty(this.props.cart) && this.props.cart.orderDetail!=null && (this.props.cart.orderDetail.source=="COD" && this.props.cart.orderDetail.deliveryAddress==null)){
            this.props.dispatch(UtilityActions.showPopup("Please select add delivery address."));
            this.props.dispatch(CustomerActions.setAddressViewType("DELIVERY"));
            browserHistory.push("/addresses");
        }
        trackUtils.trackPageView({page:"paymentModes",device:"mobile",custom:true});
    }

    render (){
        if(this.props.cart!=null && this.props.cart.orderDetail.transactionDetail!=null && this.props.cart.orderDetail.transactionDetail.paidAmount>=1 && this.props.membershipData != null && !appUtil.checkEmpty(this.props.membershipIds.data)){
            const payableAmount = Math.round(this.props.cart.orderDetail.transactionDetail.paidAmount);
            var style = [];
            this.props.membershipData.data.subscriptionProducts.map((data) => {
                if (data.id === this.props.cart.orderDetail.orders[0].productId) {
                    if (!appUtil.checkEmpty(data.marketingImage[0])) {
                        style = {backgroundImage: "url(https://d3pjt1af33nqn0.cloudfront.net/product_image/" + data.marketingImage[0].url + ")"};
                    }
                }
            })
            return(
                <div>
                    {this.props.membershipFlag === false ?
                    <div class="colouredHead">
                        <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                    </div>:<div></div>}
                    <div class="mobilePageContainer">
                        <div class="mobilePageHead">Amount Payable</div>
                        {this.props.membershipIds.data.includes(this.props.cart.orderDetail.orders[0].productId) ?
                            <div class="orderItemContainer">
                                <div className="pic" style={style}></div>
                                <div className="productTitle">{this.props.cart.orderDetail.orders[0].productName}
                                </div>
                                <div className="clear"></div>
                                <div className="customizationDetail">
                                    {this.props.cart.orderDetail.orders[0].dimension != "None" ? (
                                        <span> {this.props.cart.orderDetail.orders[0].dimension} </span>
                                    ) : null}
                                </div>
                            </div> : null}
                        <div class="payModeTypeContainer" style={{borderBottom:"none", boxShadow: '0 1px 2px 0 #e4e4df'}}>
                            <div class="payableAmount">
                                Total
                                <span class="right"><img class="rupeeIcon" src="../../img/rupee.png" />{payableAmount}</span></div>
                        </div>
                        <div class="mobilePageHead">Payment Options</div>
                        <div class="payModeTypeContainer" onClick={this.payByRazorPay.bind(this, "card")}>
                            <div class="mode">Card <span class="right"><img src="../../../img/chevronRight.svg" /></span></div>
                        </div>
                        <div class="payModeTypeContainer" onClick={this.payByRazorPay.bind(this, "netbanking")}>
                            <div class="mode">Net Banking <span class="right"><img src="../../../img/chevronRight.svg" /></span></div>
                        </div>
                        <div class="payModeTypeContainer" onClick={this.payByRazorPay.bind(this, "wallet")}>
                            <div class="mode">Wallet <span class="right"><img src="../../../img/chevronRight.svg" /></span></div>
                        </div>
                        {/*<div class="payModeTypeContainer" onClick={this.payByPaytm.bind(this)}>
                            <div class="mode">PayTm <span class="right"><img src="../../../img/chevronRight.svg" /></span></div>
                        </div>*/}
                        {this.props.criteria=="DELIVERY"?(
                            <div class="payModeTypeContainer" onClick={this.payByCash.bind(this)}>
                                <div class="mode">Cash on Delivery <span class="right"><img src="../../../img/chevronRight.svg" /></span></div>
                            </div>
                        ):(null)}
                    </div>
                </div>
            )
        }else{
            return null;
        }
    }
}
