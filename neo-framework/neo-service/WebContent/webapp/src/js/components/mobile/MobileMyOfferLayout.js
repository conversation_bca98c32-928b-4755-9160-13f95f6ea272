import React from "react";
import {connect} from "react-redux";
import {lookupCustomer, resendVerification} from "../../actions/CustomerActions";
import * as UtilityActions from "../../actions/UtilityActions";
import appUtil from "../../AppUtil";
import OtpInput from 'react-otp-input';
import Select from 'react-select';
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import {
    defaultUnitId,
    shareData,
    dateFormat,
    staticCities,
    getAvailableTimeSlots,
    getAllowedDays,
    verifySignUpOfferCustomer,
    updateSignUpOfferStage,
    copyCodeToClipboard,recordJourney,recordVisitChaayosClicked,recordOrderOnZomatoClicked,
    updateName, myOfferStage, setUtmData, getCampaignDetail, setCustomerName, setMyOffer
} from "../../actions/MyOfferActions";
import productService from "../../service/ProductService";
import CartLessCustomizationModal from "../common/CartLessCustomizationModal";
import * as CustomizationModalActions from "../../actions/CustomizationModalActions";
import {styles} from './styles/MobileMyOfferStyles';
import trackUtils from "../../utils/TrackUtils";
import AppUtil from "../../AppUtil";
import OfferContainerMobileLayout from "../elements/OfferContainerMobileLayout";
let DatePicker = require('react-datepicker');


@connect((store) => {
    return {
        customer: store.myOfferReducer.customer,
        product: store.myOfferReducer.product,
        currentStage: store.myOfferReducer.currentStage,
        customizedOfferProduct: store.myOfferReducer.customizedOfferProduct,
        availableTimeSlots: store.myOfferReducer.availableTimeSlots,
        campaignDetails: store.myOfferReducer.campaignDetails,
        myOfferDetail: store.myOfferReducer.myOfferDetail,
        customerName: store.myOfferReducer.customerName
    };
})

export default class MobileMyOfferLayout extends React.Component {
    colorList=["#fc0000","#fcd200","#00fc26","#0032fc","#a000fc","#fc004c"];
    constructor() {
        super();
        this.state = {
            name: null,
            email: null,
            contact: null,
            otp: null,
            otpResendCounter: 30,
            deliveryDate: null,
            deliveryTimeSlot: null,
            deliveryCity: null,
            focusedField: null,
            sizeDescription: productService.getSizeDescriptions(),
            opacity: 3,
            currentImageIndex:0,
            isContactFeildFocused: false,
            offerTextColorIndex:0
        };
        this.getBasicInfo = this.getBasicInfo.bind(this);
        this.verifySignUpOfferCustomer = this.verifySignUpOfferCustomer.bind(this);
        this.availSignUpOffer = this.availSignUpOffer.bind(this);
    }

    componentWillMount() {
        window.scrollTo(0, 0);
    }

    componentDidMount() {
        trackUtils.trackPageView({page: "myoffer", device: "mobile", custom: true});
        this.props.dispatch(OutletMenuActions.getUnitProductsForSignUpOffer(defaultUnitId));
        if (document.getElementById("userEmail") != null) {
            document.getElementById("userEmail").focus();
            this.setState({focusedField: 'userEmail'});
        }
        const queryParams = new URLSearchParams(window.location.search);
        var utmData={
            "utmSource":queryParams.get("utm_source"),
            "utmMedium":queryParams.get("utm_medium"),
            "utmCampaign":queryParams.get("utm_campaign")
        }
        this.props.dispatch(setUtmData(utmData));
        if(queryParams.get('token') !== undefined){
            this.props.dispatch(getCampaignDetail(queryParams.get('token'),queryParams.get("contact"),"mobile"));
        }else{
            window.location.replace("https://cafes.chaayos.com/notfound");
        }
        this.props.dispatch(getAvailableTimeSlots(null));
        // this.props.dispatch(setMyOffer())
        // this.props.dispatch(updateSignUpOfferStage(myOfferStage.showCouponView))
        this.changeImageAnimate();
        this.changeOfferTextColor();
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.currentStage !== this.props.currentStage
            && this.props.currentStage === myOfferStage.getOTP) {
            this.startOTPResendWaitCounter();
        }
    }

    isChaayosOffer=(offers)=>{
        var chaayosOffer = false;
        offers.forEach((offer)=>{
            if(offer.channelPartnerId === null || offer.channelPartnerId === 1){
                chaayosOffer = true;
            }
        })
        return chaayosOffer;
    }

    isZomatoOffer=(offers)=>{
        var zomatoOffer = false;
        offers.forEach((offer)=>{
            if(offer.channelPartnerId === 3){
                zomatoOffer = true;
            }
        })
        return zomatoOffer;
    }

    startOTPResendWaitCounter() {
        let sec = 30;
        let interval = setInterval(() => {
            this.setState({otpResendCounter: --sec});
            //console.log("seconds:: " + sec);
            if (sec === 0) {
                clearInterval(interval);
            }
        }, 1000);
    }

    getBasicInfo(e) {
        console.log("On getBasicInfo");
        e.preventDefault();
        let contact = document.getElementById("userContact").value;
        if (appUtil.checkEmpty(contact) || !appUtil.validContact(contact)) {
            this.setState({contact: null});
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
        } else {
            fbq('track','MyOfferSignup',{contact:contact})
            trackUtils.trackMyOfferUserSignup({contact:contact, deviceType : 'mobile'})
        // this.props.dispatch(recordJourney(journeyStage.signUp,"mobile",contact))
            this.setState({contact: contact});
            this.props.dispatch(lookupCustomer(contact,"mobile"));
        }
    }

    verifySignUpOfferCustomer(e) {
        e.preventDefault();
        console.log("On verifyOTP this.state.otp ::: this.state.otp.length " + this.state.otp.length);
        if (appUtil.checkEmpty(this.state.otp) || this.state.otp.length < 4) {
            this.setState({otp: null});
            this.props.dispatch(UtilityActions.showPopup("Please enter valid OTP!", "error", 3000));
        }
        if (!appUtil.checkEmpty(this.state.contact)) {
            this.props.dispatch(verifySignUpOfferCustomer(this.props.customerName, this.state.contact, this.state.otp,this.props.campaignDetails.campaignId,"mobile"));
        }
    }

    availSignUpOffer(e) {
        e.preventDefault();
        if (this.props.customer != null) {
            this.props.dispatch(availSignUpOffer(this.props.customer.customerId,
                !appUtil.checkEmpty(this.state.name) ? this.state.name : document.getElementById("userName").value,
                this.state.deliveryTimeSlot, this.state.deliveryDate,
                document.getElementById("userAddress").value,
                this.state.deliveryCity, document.getElementById("userPinCode").value,
                this.props.customizedOfferProduct));
        }
    }

    handleOTPChange = otp => this.setState({otp});

    resetData = () => {
        this.setState({
            email: null,
            contact: null,
            otp: null,
            focusedField: 'userEmail'
        });
        this.props.dispatch(updateSignUpOfferStage(myOfferStage.getBasicInfo));
    };

    resendVerification = () => {
        this.startOTPResendWaitCounter();
        this.props.dispatch(resendVerification(this.state.contact));
    };

    isStage = (stage) => {
        return this.props.currentStage !== null && this.props.currentStage === stage;
    };

    openCustomization(product) {
        this.props.dispatch(CustomizationModalActions.showCustomizationModal(product, "MENU_ITEM", null));
        this.loadProductRecipes(product);
    }

    loadProductRecipes(product) {
        if (!product.recipesLoaded) {
            var prod = {...product};
            this.props.dispatch(OutletMenuActions.loadProductRecipe(this.props.unit, prod));
        }
    }

    productCard(showCustomize) {
        let variants = [];
        let products = [];
        let addons = [];
        let menuProducts = [];
        let dcIds = [10, 11, 12, 50];
        let dcNames = {10: "Regular", 11: "Full Doodh", 12: "Doodh Kum", 50: "Pani Kum"};
        if (this.props.customizedOfferProduct !== null) {
            if (this.props.customizedOfferProduct.composition.variants.length > 0) {
                this.props.customizedOfferProduct.composition.variants.map((variant) => {
                    variants.push(variant.alias);
                });
            }
            if (this.props.customizedOfferProduct.composition.products.length > 0) {
                this.props.customizedOfferProduct.composition.products.map((product) => {
                    products.push(product.product.name);
                });
            }
            if (this.props.customizedOfferProduct.composition.addons.length > 0) {
                this.props.customizedOfferProduct.composition.addons.map((addon) => {
                    addons.push(addon.product.name);
                });
            }
            if (this.props.customizedOfferProduct.composition.menuProducts.length > 0) {
                this.props.customizedOfferProduct.composition.menuProducts.map((product) => {
                    menuProducts.push(product.productName);
                });
            }
        }
        if (this.props.product != null) {
            return (<div style={styles.productSection}>
                <div style={styles.productImageContainer}>
                    <img style={styles.productImage} src={'../../../img/products/' + this.props.product.id + '.jpg'}/>
                </div>
                <div style={styles.productInfo}>
                    <div style={styles.productName}>
                        {this.props.product.name}
                        {this.props.product.attribute == null || this.props.product.attribute === 'VEG' ?
                            (<img style={{
                                width: '15px',
                                height: '15px',
                                margin: 5,
                                alignItems: 'center',
                                justifyContent: 'center'
                            }} src="../../../../img/veg.png"/>) : (null)}
                    </div>
                    {this.props.customizedOfferProduct === null ? (
                        <div style={styles.productDesc}>{this.props.product.description}</div>) : null}
                    {this.props.customizedOfferProduct != null ? (
                        <div className="itemDetail" style={styles.productDesc}>
                            <div>{menuProducts}</div>
                            <div>
                                <span>
                                    <span>{this.props.customizedOfferProduct.dimension === "None" ? "Regular" : this.props.customizedOfferProduct.dimension}</span>
                                    {dcIds.indexOf(this.props.customizedOfferProduct.productId) >= 0 ? ", Milk - " + dcNames[this.props.customizedOfferProduct.productId] : ""}
                                    {", " + variants.join(", ")}
                                    {products.join(", ")}
                                </span>
                                <span>{variants.length > 0 ? variants.join(", ") : products.length > 0 ? products.join(", ") : ""}</span>
                            </div>
                            {addons.length > 0 ? (<div>{"Add On : " + addons.join(", ")}</div>) : null}
                        </div>
                    ) : null}
                </div>
                {showCustomize ? (
                    <div style={styles.addButtonContainer}>
                        <div style={styles.addButton} onClick={() => this.openCustomization(this.props.product)}>
                            Customise
                        </div>
                    </div>
                ) : null}
            </div>);
        }
        return null;
    }

    share() {
        if (navigator.share !== undefined) {
            console.log("Enter share()");
            navigator.share({title: shareData.title, text: shareData.text, url: shareData.url})
                .then(() => console.log('Successful share'),
                    error => console.log('Error sharing:', error));
        }
    }
    changeImageAnimate() {
        setInterval(() => {
            if (!AppUtil.checkEmpty(this.props.campaignDetails) && !AppUtil.checkEmpty(this.props.campaignDetails.backgroundImages)) {
                if (this.state.currentImageIndex === this.props.campaignDetails.backgroundImages.length-1) {
                    this.setState({currentImageIndex: 0});
                } else {
                    this.setState({currentImageIndex: this.state.currentImageIndex + 1});
                }
            }
        }, 3000);
    }

    changeOfferTextColor() {
        setInterval(()=>{
            var index = this.state.offerTextColorIndex;
            index = index + 1;
            if(index >= this.colorList.length){
                this.setState({offerTextColorIndex:0});
            }else{
                this.setState({offerTextColorIndex:index});
            }
        },500)
    }

    copyCodeToClipboard() {
        navigator.clipboard.writeText(this.props.myOfferDetail.offerCouponCode);
        this.props.dispatch(UtilityActions.showPopup("Coupon code copied to clipboard.", "info", 2000));
    }
    redirectToChaayos(){
        window.location.replace("https://cafes.chaayos.com");
    }
4
    render() {
        if (this.isStage(myOfferStage.getBasicInfo) && this.state.email === null && this.state.contact === null) {
            return (
                <div style={styles.container}>
                    <div style={styles.headerImageContainer}>
                        <img style={{width: "100%"}} src={this.props.campaignDetails.backgroundImages[this.state.currentImageIndex]}/>
                    </div>
                    <div style={styles.formContent}>
                        <form name="lookupForm" action="#" onSubmit={this.getBasicInfo.bind(this)}>
                            <div style={styles.formTitle}> Signup for relaxing free chai</div>
                            <div style={styles.highlightSubTitle}> A hot cup of CHAI can fix anything &#128154;</div>

                            {/*<div style={styles.verifyEmailInfo}>
                                *Verify your email and Redeem a Free Desi Chai Regular at any cafe.
                            </div>*/}
                            <div
                                style={this.state.focusedField === 'userContact' ? styles.focusedFormFieldBox : styles.formFieldBox}>
                                <input style={styles.formFieldInput} id="userContact"
                                       onFocus={() => this.setState({focusedField: 'userContact'})}
                                       type="tel" placeholder="Mobile Number" maxLength="10" autoComplete="off"/>
                            </div>
                            <div className="btn btn-primary" style={styles.actionButton}
                                 onClick={this.getBasicInfo.bind(this)}>Sign Up
                            </div>
                        </form>
                    </div>
                </div>
            );
        }
        if (this.isStage(myOfferStage.getOTP) && this.state.contact !== null) {
            return (
                <div style={styles.container}>
                    <div style={styles.headerImageContainer}>
                        <img style={{width: "100%"}} src={this.props.campaignDetails.backgroundImages[this.state.currentImageIndex]}/>
                    </div>
                    <div style={styles.oTPSection}>
                        <div style={styles.oTPSectionInfoContainer}>
                            <div style={styles.oTPSectionTitle}>
                                <p>OTP Verification</p>
                            </div>
                            <div style={styles.oTPInfoContent}>
                                <p>We have sent the OTP to your </p>
                                <p>mobile number</p>
                            </div>
                        </div>
                        <div style={styles.oTPInputSection}>
                            <div style={styles.oTPInputMainContainer}>
                                <OtpInput
                                    containerStyle={styles.oTPInputContainer}
                                    inputStyle={styles.oTPInputBox}
                                    numInputs={4}
                                    errorStyle={styles.oTPErrorStyle}
                                    onChange={this.handleOTPChange}
                                    separator={<span> </span>}
                                    isInputNum={true}
                                    shouldAutoFocus
                                    value={this.state.otp}
                                    placeholder={'    '}
                                />
                            </div>
                            <div style={styles.resendOTPSection}>
                                <span>Didn't receive OTP?</span>
                                {this.state.otpResendCounter === 0 ? (
                                    <span className={'resendLink'} style={{fontWeight: 'bold'}}
                                          onClick={this.resendVerification}> RESEND OTP</span>
                                ) : (
                                    <span
                                        style={{fontWeight: 'bold'}}> Try again in {this.state.otpResendCounter}</span>
                                )}
                            </div>
                        </div>
                        <div className="btn btn-primary" style={styles.actionButton}
                             onClick={this.verifySignUpOfferCustomer.bind(this)}>Verify
                        </div>
                    </div>
                </div>);
        }
        if (this.isStage(myOfferStage.getName) &&  this.state.contact !== null) {
            return (
                <div style={styles.container}>
                    <div style={styles.headerImageContainer}>
                        <img style={{width: "100%"}} src={this.props.campaignDetails.backgroundImages[this.state.currentImageIndex]}/>
                    </div>
                    <div style={styles.formContent}>
                        <form name="lookupForm" action="#" onSubmit={this.getBasicInfo.bind(this)}>
                            <div style={styles.formTitle}> Enter your name</div>

                            {/*<div style={styles.verifyEmailInfo}>
                                *Verify your email and Redeem a Free Desi Chai Regular at any cafe.
                            </div>*/}
                            <div
                                style={this.state.focusedField === 'userName' ? styles.focusedFormFieldBox : styles.formFieldBox}>
                                <input style={styles.formFieldInput} id="userName"
                                       onFocus={() => this.setState({focusedField: 'userName'})}
                                       type="text" placeholder="Enter Name" maxLength="100" autoComplete="off"/>
                            </div>
                            <div className="btn btn-primary" style={styles.actionButton}
                                 onClick={()=>{
                                     var name = document.getElementById("userName").value;
                                     this.setState({name: name});
                                     this.props.dispatch(setCustomerName(name));
                                     this.props.dispatch(updateSignUpOfferStage(myOfferStage.getOTP));
                                 }}>Save
                            </div>
                        </form>
                    </div>
                </div>
            );
        }
        if (this.isStage(myOfferStage.getProductAndDeliveryInfo)) {
            return (
                <div style={styles.container}>
                    <div style={styles.backHeader}>
                        <div style={styles.backBox} onClick={this.resetData}><img width={'20px'} height={'20px'}
                                                                                  src="../../../../img/back_simple.svg"/>
                        </div>
                        <div style={{width: '80%'}}/>
                    </div>
                    <div style={styles.pSectionHeaderContainer}>
                        <div style={styles.pSectionHeader}>
                            <p>Choose your free Chai Ketli</p>
                        </div>
                    </div>
                    <div style={{backgroundColor: '#eeeeee', boxShadow: '5px 3px 10px #9E9E9E'}}>
                        {this.productCard(true)}
                    </div>
                    <div style={styles.pSectionHeaderContainer}>
                        <div style={styles.pSectionHeader}>
                            <p>Enter your address</p>
                        </div>
                    </div>
                    <div style={{flex: 1, flexDirection: 'row', height: '15%', padding: 10, marginTop: 5}}>
                        <p style={styles.inputNameText}>Name</p>
                        <div
                            style={this.state.focusedField === 'userName' ? styles.addressFormFieldFocused : styles.addressFormField}>
                            {!appUtil.checkEmpty(this.state.name) ? (
                                <input disabled={true} style={styles.formFieldInput} id="userName"
                                       type="text" placeholder={this.state.name} maxLength="100" autoComplete="off"/>

                            ) : (
                                <input style={styles.formFieldInput} id="userName"
                                       onFocus={() => this.setState({focusedField: 'userName'})}
                                       type="text" placeholder="Enter Name .." maxLength="100" autoComplete="off"/>
                            )}
                        </div>
                    </div>
                    <div style={{
                        flex: 1,
                        flexDirection: 'row',
                        height: '15%',
                        padding: 10,
                        marginTop: -15,
                        width: '100%'
                    }}>
                        <div style={{flex: 1, flexDirection: 'column', padding: 5, float: 'left', width: '50%'}}>
                            <p style={styles.inputNameText}>Date of Delivery</p>
                            {this.state.focusedField != null && this.state.focusedField === 'userDeliveryDate' ? (
                                <div style={styles.dataPicker}>
                                    <DatePicker
                                        dateFormat={dateFormat}
                                        autoFocus={true}
                                        onFocus={() => this.setState({focusedField: 'userDeliveryDate'})}
                                        selected={this.state.deliveryDate}
                                        includeDates={getAllowedDays(new Date())}
                                        onChange={(date) => {
                                            this.setState({
                                                deliveryDate: date,
                                                deliveryTimeSlot: null,
                                                focusedField: null
                                            });
                                            this.props.dispatch(getAvailableTimeSlots(date));
                                        }
                                        }/>
                                </div>
                            ) : (
                                <div
                                    style={this.state.focusedField === 'userDeliveryDate' ? styles.addressFormFieldFocused : styles.addressFormField}>
                                    <input style={styles.formFieldInput} id="userDeliveryDate"
                                           onFocus={() => this.setState({focusedField: 'userDeliveryDate'})}
                                           type="text" placeholder={this.state.deliveryDate != null ?
                                        this.state.deliveryDate.format(dateFormat) : dateFormat} maxLength="100"
                                           autoComplete="off"/>
                                </div>
                            )}
                        </div>
                        <div style={{flex: 1, flexDirection: 'column', padding: 5, float: 'right', width: '50%'}}>
                            <p style={styles.inputNameText}>Time of Delivery</p>
                            {this.state.focusedField != null && this.state.focusedField === 'userDeliveryTime' ? (
                                <Select
                                    style={styles.selectItem}
                                    wrapperStyle={styles.selectItemWrapper}
                                    name="userDeliveryTime" value={""}
                                    options={this.props.availableTimeSlots}
                                    onFocus={() => this.setState({focusedField: 'userDeliveryTime'})}
                                    autofocus={true}
                                    openOnFocus={true}
                                    backspaceRemoves={false}
                                    onChange={(timeSlot) => {
                                        console.log("timeSlot:: " + JSON.stringify(timeSlot));
                                        this.setState({
                                            deliveryTimeSlot: timeSlot,
                                            focusedField: null
                                        })
                                    }
                                    }
                                    clearable={false}
                                    placeholder={""}
                                    autoBlur={true}/>
                            ) : (
                                <div
                                    style={this.state.focusedField === 'userDeliveryTime' ? styles.addressFormFieldFocused : styles.addressFormField}>
                                    <div style={styles.formFieldInput} id="userDeliveryTime"
                                         onClick={() => this.setState({focusedField: 'userDeliveryTime'})}
                                    >{this.state.deliveryTimeSlot != null ? this.state.deliveryTimeSlot.label : "00:00 - 00:00 PM"}</div>
                                </div>
                            )}
                        </div>
                    </div>
                    <div style={{
                        flex: 1,
                        flexDirection: 'row',
                        height: '15%',
                        padding: 10,
                        marginTop: -15,
                        width: '100%'
                    }}>
                        <div style={{flex: 1, flexDirection: 'column', padding: 5, float: 'left', width: '100%'}}>
                            <p style={styles.inputNameText}>Complete Address</p>
                            <div
                                style={this.state.focusedField === 'userAddress' ? styles.addressTextFocused : styles.addressText}>
                                <input style={styles.formFieldInput} id="userAddress"
                                       onFocus={() => this.setState({focusedField: 'userAddress'})}
                                       type="text" placeholder="Flat No. Building.. Street Name.." maxLength="100"
                                       autoComplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div style={{
                        flex: 1,
                        flexDirection: 'row',
                        height: '15%',
                        padding: 10,
                        marginTop: -15,
                        width: '100%'
                    }}>
                        <div style={{flex: 1, flexDirection: 'column', padding: 5, float: 'left', width: '50%'}}>
                            <p style={styles.inputNameText}>Pin Code</p>
                            <div
                                style={this.state.focusedField === 'userPinCode' ? styles.addressFormFieldFocused : styles.addressFormField}>
                                <input style={styles.formFieldInput} id="userPinCode"
                                       onFocus={() => this.setState({focusedField: 'userPinCode'})}
                                       type="text" placeholder="" maxLength="100" autoComplete="off"/>
                            </div>
                        </div>
                        <div style={{flex: 1, flexDirection: 'column', padding: 5, float: 'right', width: '50%'}}>
                            <p style={styles.inputNameText}>City</p>
                            {this.state.focusedField != null && this.state.focusedField === 'userCity' ? (
                                <Select
                                    style={styles.selectItem}
                                    wrapperStyle={styles.selectItemWrapper}
                                    name="userCity" value={""}
                                    options={staticCities}
                                    onFocus={() => this.setState({focusedField: 'userCity'})}
                                    autofocus={true}
                                    openOnFocus={true}
                                    backspaceRemoves={false}
                                    onChange={(cityObj) => {
                                        console.log("timeSlot:: " + JSON.stringify(cityObj));
                                        this.setState({
                                            deliveryCity: cityObj,
                                            focusedField: null
                                        })
                                    }
                                    }
                                    clearable={false}
                                    placeholder={""}
                                    autoBlur={true}/>
                            ) : (
                                <div style={styles.cityContainer}>
                                    <div style={styles.formFieldInput} id="userCity"
                                         onClick={() => this.setState({focusedField: 'userCity'})}>
                                        {this.state.deliveryCity != null ? this.state.deliveryCity.label : ""}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                    <div style={{
                        flex: 1,
                        flexDirection: 'row',
                        height: '15%',
                        padding: 10,
                        marginTop: 5,
                        width: '100%'
                    }}>
                        <div className="btn btn-primary" style={styles.actionButton}
                             onClick={this.availSignUpOffer.bind(this)}>Continue
                        </div>
                    </div>
                    <CartLessCustomizationModal/>
                </div>);
        }
        if (this.isStage(myOfferStage.showCongratulationView)) {
            return (<div style={{...styles.container, ...styles.confettiContainer}}>
                <div style={styles.congratsInner}>
                    <div style={{height: 150, backgroundColor: '#eeeeee', boxShadow: '5px 3px 10px #9E9E9E'}}>
                        {this.productCard(false)}
                    </div>
                    <img style={{width: '80%'}} src="../../../../img/Relax- facade_ revised_final-02.png"/>
                    <div style={styles.congratsTitle}>
                        <h1>Congratulations</h1>
                        <p style={styles.congratsSub}>on your free Chai</p>
                    </div>
                    <p style={styles.congratsText}>You'll get a call from our team member on your registered mobile
                        number.</p>
                    <button style={styles.congratsButton} onClick={() => this.share()}>Share With Friends</button>
                </div>
            </div>);
        }
        if (this.isStage(myOfferStage.showCouponView) && this.props.myOfferDetail != null && this.props.myOfferDetail.length > 0) {
            var isOldOffer = false;
            this.props.myOfferDetail.forEach((data)=>{
                if(data.existingOffer){
                    isOldOffer = true;
                }
            })
            return (
                <div style={{...styles.container, ...styles.confettiContainer}}>
                <div style={styles.congratsInner}>
                    <div style={styles.congratsTitle}>
                        <h1 style={styles.congratulationHeading}>Congratulations! {this.props.customerName.split(" ")[0]}</h1>
                        <h1 style={styles.congratulationSubHeading}>You have won</h1>
                    </div>
                    <div  style={{width : '100%', display:'flex', justifyContent:'space-evenly',marginTop:'30px', flexDirection:'row'}}>
                        {this.props.myOfferDetail.map(data => (
                            <OfferContainerMobileLayout
                                key={data.offerCouponCode}
                                data={data}
                                widthPercent={this.props.myOfferDetail.length == 2 ? .45 : .7}
                                onCodeCopy={(code)=>this.props.dispatch(copyCodeToClipboard(code))}
                            />
                        ))}
                    </div>
                    <img src="../../../img/offer_cup_relax.webp" height={200}/>
                    {this.props.campaignDetails.campaignId===31 ?
                        this.props.myOfferDetail[0].offerCouponCode === "FLASH15" ?
                        <p style={{...styles.alreadyShareText,fontSize:'16px'}}> Minimum Order value is 299&#x20b9;</p> :
                            <p style={{...styles.alreadyShareText,fontSize:'16px'}}> Minimum Order value is 99&#x20b9;</p>
                        :null}
                        {isOldOffer ?
                            <p style={styles.alreadyShareText}>Note :- We have already shared this coupon code with you. </p>
                            :null}
                    { this.isChaayosOffer(this.props.myOfferDetail) ?
                        <div onClick={()=> this.props.dispatch(recordVisitChaayosClicked("desktop"))} >
                            <img src="../../../../img/visit_chaayos.webp" draggable={false} style={styles.visitChaayosButton}/>
                        </div>: null
                    }
                    { this.isZomatoOffer(this.props.myOfferDetail) ?
                        <div onClick={()=> this.props.dispatch(recordOrderOnZomatoClicked("desktop"))}>
                            <img src="../../../../img/order_on_zomato.webp" draggable={false} style={styles.visitChaayosButton} />
                        </div>:null
                    }
                    {/*<p style={{ marginTop: '20px', textAlign:'center', fontSize:'35px', fontWeight:'bold',color: this.colorList[this.state.offerTextColorIndex]}}>{this.props.myOfferDetail.offerDesc}</p>*/}
                    {/*<p style={styles.validitytext}>Valid from : {this.props.myOfferDetail.validityFrom}</p>*/}
                    {/*<p style={styles.validitytext}>Valid till : {this.props.myOfferDetail.validityTill}</p>*/}
                    {/*<div style={styles.codecontainer}>*/}
                    {/*    <h1 style={styles.codeheading}>USE CODE</h1>*/}
                    {/*    <div style={styles.codediv} onClick={()=> this.props.dispatch(copyCodeToClipboard(this.props.myOfferDetail.offerCouponCode))}>*/}
                    {/*        <h1 style={styles.codemain} >{this.props.myOfferDetail.offerCouponCode}</h1>*/}
                    {/*    </div>*/}
                    {/*    <p style={styles.copyText}>Tap on code to copy.</p>*/}
                    {/*    {this.props.myOfferDetail.existingOffer ?*/}
                    {/*        <p style={styles.alreadyShareText}>Note :- We have already shared this coupon code with you. </p>*/}
                    {/*        :null}*/}
                    {/*</div>*/}

                    {/*<div>*/}
                    {/*    <h1 style={styles.downloadtext}>Download the app Now</h1>*/}
                    {/*    <div style={styles.downloadcontainer}>*/}
                    {/*        <div style={styles.downloadstorecontainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>*/}
                    {/*            <img style={styles.downloadimg} src="../../../../img/downloadappstore.png"/>*/}
                    {/*        </div>*/}
                    {/*        <div style={styles.downloadstorecontainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>*/}
                    {/*            <img style={styles.downloadimg} src="../../../../img/downloadplaystore.png"/>*/}
                    {/*        </div>*/}
                    {/*    </div>*/}
                    {/*</div>*/}
                    <div>
                        {/*<div style={styles.sharecontainer} onClick={() => this.share()}>*/}
                        {/*    <img style={styles.shareicon} width={'20px'} height={'20px'}*/}
                        {/*         src="../../../../img/shareicon.svg"/>*/}
                        {/*    <h1 style={styles.sharetxt}>Share With Friends</h1>*/}
                        {/*</div>*/}
                        <h1 style={styles.alreadyShareText}>Term and Conditions apply</h1>
                    </div>
                </div>
                {/*<img style={styles.gifStyle} src={"../../../../img/celebrate_pop.gif"}/>*/}
            </div>);
        }
        if (this.isStage(myOfferStage.noOfferView)) {
            var name = ""
            if(this.props.customerName !== null){
                name = this.props.customerName.split(" ")[0];
            }
            return (<div style={{...styles.container, ...styles.confettiContainer}}>
                <div style={styles.congratsInner}>

                    <img style={styles.congratschaiimg} src="../../../../img/Relax- facade_ revised_final-02.png"/>
                    <div style={styles.congratsTitle}>
                        <h1>Sorry! {name} </h1>
                        <p style={styles.congratsSub}>We don't have any offer for you at this moment.</p>
                    </div>
                    <div className="btn btn-primary" style={styles.visitChaayosButton}
                         onClick={this.redirectToChaayos.bind(this)}>Visit Chaayos
                    </div>
                    <div>
                        <h1 style={styles.downloadtext}>Download the app Now</h1>
                        <div style={styles.downloadcontainer}>
                            <div style={styles.downloadstorecontainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                <img style={styles.downloadimg} src="../../../../img/downloadappstore.png"/>
                            </div>
                            <div style={styles.downloadstorecontainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                <img style={styles.downloadimg} src="../../../../img/downloadplaystore.png"/>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div style={styles.sharecontainer} onClick={() => this.share()}>
                            <img style={styles.shareicon} width={'20px'} height={'20px'}
                                 src="../../../../img/shareicon.svg"/>
                            <h1 style={styles.sharetxt}>Share With Friends</h1>
                        </div>
                        <p style={styles.termstext}>
                            T&C: Coupon code is applicable once per user & can be availed for Dine-in and Takeaway
                            orders only at Chaayos Cafe. Take a Screenshot of coupon code as proof.
                        </p>
                    </div>
                </div>
            </div>);
        }
        if (this.isStage(myOfferStage.offerAlreadyAvailedView)) {
            return (<div style={{...styles.container, ...styles.confettiContainer}}>
                <div style={styles.congratsInner}>
                    <img style={{width: '80%'}} src="../../../../img/Relax- facade_ revised_final-02.png"/>
                    <p style={styles.congratsSub}>You have already registered for free Chai</p>
                    <button style={styles.congratsButton} onClick={() => this.share()}>Share With Friends</button>
                </div>
            </div>);
        }
        if (this.isStage(myOfferStage.secondFreeChaiView) && this.props.myOfferDetail != null) {
            return (<div style={{...styles.container, ...styles.confettiContainer}}>
                <div style={styles.congratsInner}>
                    <img style={styles.congratschaiimg} src="../../../../img/Relax- facade_ revised_final-02.png"/>
                    <div style={styles.congratsTitle}>
                        <h1 style={styles.congratulationHeading}>Congratulations! {this.props.myOfferDetail.customerName.split(" ")[0]}</h1>
                        <p style={styles.congratsSub}> You have got a free chai on your second visit.</p>
                    </div>
                    <p style={styles.validitytext}>Valid from : {this.props.myOfferDetail.validityFrom} and Valid till : {this.props.myOfferDetail.validityTill}</p>
                    {this.props.myOfferDetail.existingOffer ?
                        <p style={styles.alreadyShareText}>Note :- We have already shared this offer with you. </p>
                        :null}
                    <div>
                        <h1 style={styles.downloadtext}>Download the app Now</h1>
                        <div style={styles.downloadcontainer}>
                            <div style={styles.downloadstorecontainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                <img style={styles.downloadimg} src="../../../../img/downloadappstore.png"/>
                            </div>
                            <div style={styles.downloadstorecontainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                <img style={styles.downloadimg} src="../../../../img/downloadplaystore.png"/>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div style={styles.sharecontainer} onClick={() => this.share()}>
                            <img style={styles.shareicon} width={'20px'} height={'20px'}
                                 src="../../../../img/shareicon.svg"/>
                            <h1 style={styles.sharetxt}>Share With Friends</h1>
                        </div>
                        <p style={styles.termstext}>
                            T&C: Coupon code is applicable once per user & can be availed for Dine-in and Takeaway
                            orders only at Chaayos Cafe. Take a Screenshot of coupon code as proof.
                        </p>
                    </div>
                </div>
            </div>);
        }
        if (this.isStage(myOfferStage.newCustomerNoOfferView)) {
            return (<div style={{...styles.container, ...styles.confettiContainer}}>
                <div style={styles.congratsInner}>
                    <img style={{width: '80%'}} src="../../../../img/Relax- facade_ revised_final-02.png"/>
                    <p style={styles.congratsSub}>Offer is applicable for new customers only</p>
                    <button style={styles.congratsButton} onClick={() => this.share()}>Share With Friends</button>
                </div>
            </div>);
        }
        return null;
    }
}
