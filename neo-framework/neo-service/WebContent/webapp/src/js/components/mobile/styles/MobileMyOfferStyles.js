export const styles = {
    container: {
        flex: 1,
        minHeight: window.innerHeight,
        maxHeight: window.innerHeight,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
        backgroundColor: '#ffffff'
    },
    headerImageContainer: {flex: 1, height: 'fit-content', width: '100vw', marginBottom: 5},
    formContent: {flex: 1, width: '100%', justifyContent: 'center', alignItems: 'center'},
    formTitle: {
        textAlign: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#5e7e47',
        fontSize: 22,
        marginTop: 5,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5
    },
    highlightSubTitle: {
        textAlign: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#ffffff',
        fontSize: 21,
        marginTop: 5,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        backgroundColor: '#5e7e47',
        fontFamily: "'Caveat', cursive",
        letterSpacing: 2,
        lineHeight: 1,
        paddingBottom: 10,
        paddingTop: 10
    },
    emailFormFieldBox: {
        backgroundColor: '#eeeeee',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10
    },
    emailFocusedFormFieldBox: {
        backgroundColor: '#ffffff',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E'
    },
    formFieldBox: {
        backgroundColor: '#eeeeee',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10
    },
    focusedFormFieldBox: {
        backgroundColor: '#ffffff',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E'
    },
    formFieldInput: {
        width: '100%',
        padding: 20,
        marginLeft: 10,
        backgroundColor: 'transparent',
        border: 0,
        fontSize: 12,
        color: '#787775'
    },
    actionButton: {
        textAlign: 'center',
        backgroundColor: '#5e7e47',
        color: '#ffffff',
        borderWidth: '2px',
        borderColor: '#5e7e47',
        borderRadius: '10px',
        marginTop: 35,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        fontSize: 20
    },
    visitChaayosButton: {
        textAlign: 'center',
        backgroundColor: '#5e7e47',
        color: '#ffffff',
        borderWidth: '2px',
        borderColor: '#5e7e47',
        borderRadius: '10px',
        marginTop: 35,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        fontSize: 20
    },
    backHeader: {flexDirection: 'row', height: '5%'},
    backBox: {width: '20%', alignItem: 'center', justifyContent: 'center', padding: 20},
    oTPSection: {
        flex: 1,
        width: '100%',
        marginTop: '15px',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPSectionInfoContainer: {
        flex: 1,
        flexDirection: 'column',
        height: '40%',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPSectionTitle: {
        flex: 1,
        height: '50%',
        textAlign: 'center',
        alignItem: 'center',
        justifyContent: 'center',
        color: '#5e7e47',
        fontSize: 28,
        marginBottom: 5
    },
    oTPInfoContent: {
        flex: 1,
        height: '50%',
        textAlign: 'center',
        fontSize: 18,
        color: '#70777D'
    },
    oTPInputSection: {
        flex: 1,
        flexDirection: 'column',
        marginTop: '50px',
        height: '60%',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPInputMainContainer: {
        flex: 1,
        height: '50%',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPInputContainer: {flex: 1, alignItem: 'center', justifyContent: 'center'},
    oTPInputBox: {
        width: '5rem',
        height: '5rem',
        margin: '0.5rem',
        color: '#5e7e47',
        fontSize: 25,
        borderRadius: 6,
        border: 0,
        backgroundColor: '#eeeeee'
    },
    oTPErrorStyle: {borderWidth: 2, borderColor: 'red'},
    resendOTPSection: {flex: 1, height: '50%', textAlign: 'center', fontSize: 15, color: '#70777D'},
    productSection: {
        display: 'flex',
        flex: 1,
        flexDirection: 'row',
        width: '100vw',
        padding: 10,
        backgroundColor: '#eeeeee'
    },
    pSectionHeaderContainer: {
        flex: 1,
        height: '5%',
        marginTop: '30px',
        alignItem: 'center',
        justifyContent: 'center'
    },
    pSectionHeader: {
        flex: 1,
        height: '50%',
        textAlign: 'flex-start',
        alignItem: 'flex-start',
        justifyContent: 'flex-start',
        fontWeight: 'bold',
        color: '#5e7e47',
        fontSize: 20,
        marginBottom: 5,
        paddingLeft: '20px'
    },
    productImageContainer: {alignItems: 'flex-start', justifyContent: 'center', padding: 10,},
    productImage: {
        width: '80px',
        height: '80px',
        objectFit: 'cover',
        borderRadius: 15,
        boxShadow: '5px 3px 10px #9E9E9E'
    },
    productInfo: {flex: 1, width: '45vw', alignItems: 'flex-end', justifyContent: 'flex-end', padding: 10},
    productName: {
        textAlign: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'center',
        color: '#5e7e47',
        fontSize: 20,
        fontWeight: 600,
        marginBottom: 10
    },
    productDesc: {
        textAlign: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'center',
        color: '#787775',
        fontSize: 11,
        marginBottom: 5,
    },
    addButtonContainer: {padding: '10px', paddingLeft: 0},
    addButton: {
        padding: "8px 10px",
        borderRadius: '20px',
        top: 0,
        textAlign: 'center',
        color: '#ffffff',
        backgroundColor: '#5e7e47',
        fontSize: 10,
        boxShadow: '0 1px 2px 0 #c8c8c3', border: 'solid 2px #5e7e47'
    },
    confettiContainer: {
        backgroundImage: 'url("../../../../img/congrats_view_confetti_bg.svg")',
        backgroundSize: '80%',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        display: 'flex',
    },
    congratsInner: {
        width: '100%',
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-evenly',
        alignItems: 'center',
    },
    congratsTitle: {
        fontWeight: '700',
        color: '#3e601a',
        fontSize: '45.67px',
        fontFamily: "'Caveat', cursive",
        textAlign: 'center',
        lineHeight: 1,
    },
    congratsSub: {
        fontFamily: "'Caveat', cursive",
        fontSize: '25px',
        color: '#3e601a',
        fontWeight: '600',
        textAlign: 'center',
        width: '100%',
        marginTop: '20px',
        wordSpacing: '6px',
        letterSpacing: '1px'
    },
    secondChaiFreeText: {
        fontFamily: "'Caveat', cursive",
        fontSize: '20px',
        color: '#3e601a',
        fontWeight: '600',
        textAlign: 'center',
        width: '100%',
        marginTop: '40px',
        wordSpacing: '6px',
        letterSpacing: '1px'
    },
    validitytext:{
        color: 'grey',
        textAlign:'center',
    },
    copyText:{
        color: 'grey',
        fontSize: '11px',
        textAlign:'center'
    },
    congratsText: {
        textAlign: 'center',
        color: '#606060',
        fontSize: '1rem',
        padding: "0 10%",
    },
    congratsButton: {
        fontFamily: 'inherit',
        textAlign: 'center',
        backgroundColor: '#5e7e47',
        color: '#ffffff',
        width: '100%',
        borderWidth: '2px',
        borderColor: '#5e7e47',
        borderRadius: '25px',
        padding: 10,
        fontSize: 20,
        outline: 'none',
        border: 'none'
    },
    addressFormField: {
        flex: 1,
        backgroundColor: '#eeeeee',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        marginTop: 5
    },
    addressFormFieldFocused: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E',
        marginTop: 5
    },
    addressText: {
        flex: 1,
        backgroundColor: '#eeeeee',
        borderWidth: 10,
        borderColor: '#000000',
        borderRadius: 10,
        marginTop: 5,
        height: 100
    },
    addressTextFocused: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 10,
        borderColor: '#000000',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E',
        marginTop: 5,
        height: 100
    },
    inputNameText: {flex: 1, paddingLeft: 10, justifyContent: 'center', color: '#989898', fontSize: '12px'},
    selectItem: {
        fontSize: '12px',
        flex: 1,
        textAlign: 'center',
        alignItem: 'center',
        padding: 10,
        borderRadius: 10,
        marginTop: 5,
        height: '55px',
        backgroundColor: '#ffffff'
    },
    selectItemWrapper: {
        fontSize: '12px',
        color: '#989898',
        alignItem: 'center',
        borderRadius: 10,
        height: '55px'
    },
    dataPicker: {
        flex: 1,
        height: '55px',
        alignItem: 'center',
        padding: 10,
        borderColor: '#787775',
        borderRadius: 10,
        marginTop: 5
    },
    cityContainer: {
        flex: 1,
        backgroundColor: '#eeeeee',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        marginTop: 5,
        height: '55px'
    },
    verifyEmailInfo: {
        marginLeft: 35,
        marginTop: 10,
        color: '#5e7e47',
        fontSize: 10,
        marginBottom: 5
    },
    congratschaiimg: {
        width: '60%',
    },

    codeheading: {
        color: '#5e7e47',
        fontSize: '15px',
        textAlign: 'center',
        fontFamily: 'Nunito',
    },
    codediv: {
        backgroundColor: 'grey',
        display: 'block',
        padding: '10 15',
        borderWidth: '3px',
        borderStyle: 'dashed',
        borderColor: 'black',
        borderRadius: 1000,
    },
    codemain: {
        fontSize: 25,
        color: '#ffffff',
        textAlign: 'center',
        fontFamily: 'Nunito',
        marginLeft: '30px',
        marginRight: '30px'
    },
    codesubtext: {
        color: 'grey',
        marginTop: '20px',
        textAlign:'center',
        fontSize:'25px',
        fontWeight:'bold'
    },
    codecontainer: {
        marginTop: 25,
    },
    secondFreeChaiTitle: {
        fontWeight: '700',
        color: '#3e601a',
        fontSize: '45px',
        fontFamily: "'Caveat', cursive",
        textAlign: 'center',
        lineHeight: 1.3,
        wordSpacing:'15px',
        letterSpacing: '2px',
        marginBottom: '30px'
    },
    downloadcontainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '80%',
        marginLeft: '10%',

    },
    downloadstorecontainer: {
        width: '49%',

    },
    downloadimg: {
        width: '100%',
    },
    downloadtext: {
        textAlign: 'center',
        marginBottom: 3,
        fontFamily: 'Nunito',
        fontSize: 14,
        color: 'grey',
    },
    sharetxt: {
        color: '#5e7e47',
        fontFamily: 'Nunito-SemiBold, Nunito',
        fontSize: 20,
        fontWeight: '700',
        textAlign: 'center',

    },
    termstext: {
        color: 'grey',
        fontFamily: 'Nunito',
        fontSize: 8,
        marginTop: 15,
    },
    sharecontainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
    },
    shareicon: {
        marginTop: 3,
        marginRight: 4,
    },
    congratulationHeading: {
        fontSize: '30px',

    },
    alreadyShareText: {
        color: '#3e601a',
        textAlign:'center',
        fontWeight:'bold',
    },
    gifStyle: {
        position:'absolute',
        top: 0,
        width: window.innerWidth,
        height: window.innerHeight*.5
    },
    congratulationSubHeading:{
        fontSize: '25px',
    },
    visitChaayosButton:{
        width: window.innerWidth*.9
    }

};
