import React from "react";
import browserHistory from "react-router/lib/browserHistory";
import {connect} from "react-redux";
import appUtil from "../../AppUtil";
import * as SidebarActions from "../../actions/SidebarActions";
import trackUtils from "../../utils/TrackUtils";
import StorageUtils from "../../utils/StorageUtils";

@connect((store) => {
    return {
        cart: store.cartManagementReducer.cart,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedCity: store.localityReducer.selectedCity,
        selectedOutlet: store.localityReducer.selectedOutlet,
        showLocality: store.localityReducer.showLocality,
        showOutlet: store.localityReducer.showOutlet,
        criteria: store.localityReducer.criteria,
        restrictDineIn:store.utilityReducer.restrictDineIn,
        membershipType: store.utilityReducer.membershipType
    };
})
export default class MobileHeader extends React.Component {

    constructor() {
        super();
        this.state = {
            /*showLocalityWrapper: {display: "none"},
            showOutletWrapper: {display: "none"},
            locality: {value: 0, label: ""},
            city: null,
            outlet: {value: 0, label: ""},*/
        };
        this.init = this.init.bind(this);
        this.processLocationClick = this.processLocationClick.bind(this);
        this.goToCart = this.goToCart.bind(this);
        this.toggleSidebar = this.toggleSidebar.bind(this);
        this.goBack = this.goBack.bind(this);
    }

    init() {
        /*if (this.props.selectedCity != null) {
            if (this.props.criteria === "DELIVERY" && this.props.selectedLocality != null) {
                this.setState({
                    showLocalityWrapper: {display: "block"},
                    showOutletWrapper: {display: "none"},
                    locality: this.props.selectedLocality != null ? this.props.selectedLocality : {value: 0, label: ""},
                    city: this.props.selectedCity != null ? this.props.selectedCity.city : {value: 0, label: ""},
                    outlet: this.props.selectedOutlet != null ? this.props.selectedOutlet : {value: 0, label: ""}
                });
            }
            if ((this.props.criteria === "TAKE_AWAY" || this.props.criteria === "DINE_IN") && this.props.selectedOutlet != null) {
                this.setState({
                    showLocalityWrapper: {display: "none"},
                    showOutletWrapper: {display: "block"},
                    locality: this.props.selectedLocality != null ? this.props.selectedLocality : {value: 0, label: ""},
                    city: this.props.selectedCity != null ? this.props.selectedCity.city : {value: 0, label: ""},
                    outlet: this.props.selectedOutlet != null ? this.props.selectedOutlet : {value: 0, label: ""}
                });
            }
        }*/
    }

    processLocationClick() {
        if(!this.props.restrictDineIn){
            trackUtils.trackLocalityChangeClicked(window.location.pathname=="/"?"home":window.location.pathname.substring(1));
            //this.props.dispatch(LocalityActions.setCriteria(val));
            browserHistory.push("/");
        }
    }

    goToCart() {
        browserHistory.push("/cart");
    }

    toggleSidebar() {
        this.props.dispatch(SidebarActions.toggleSidebar());
    }

    goBack(){
        if (StorageUtils.getMembershipFlag() == "true") {
            let type = this.props.membershipType;
            browserHistory.push("/membership/"+ type);
        } else {
            if (window.location.pathname == "/paymentModes") {
                trackUtils.trackReturnedFromPayment();
            }
            if (window.location.pathname == "/orderDetail") {
                browserHistory.push("/orders");
            } else {
                browserHistory.push("/menu");
            }
        }
    }

    componentWillMount() {
        this.init();
    }

    render() {
        var cart = this.props.cart;
        var cartItems = 0;
        if(!appUtil.checkEmpty(cart) && cart.orderDetail.orders.length>0){
            cartItems = cart.orderDetail.orders.length;
            cart.orderDetail.orders.map((orderItem) => {
                if(orderItem.productId==1043 || orderItem.productId==1044){
                    cartItems--;
                }
            })
        }
        return (
            <div class='headerWrapper'>
                {this.props.menu?(
                    <div class='headerBtn left' onClick={this.toggleSidebar.bind(this)}>
                        <img class="menuIcon" src="../../../img/menu.svg" />
                    </div>
                ):(
                    <div class='headerBtn left' onClick={this.goBack.bind(this)}>
                        <img class="menuIcon" src="../../../img/back.svg" />
                    </div>
                )}
                {this.props.showLocationMetadata ? (
                    <div>
                        {this.props.showLocality?(
                            <div class="localityWrapper ellipsis" onClick={this.processLocationClick.bind(this)}>
                                <div class="tagLine">Deliver To</div>
                                {this.props.selectedLocality!=null?this.props.selectedLocality.label+", ":""} {this.props.selectedCity.city}
                                <img class="downIcon" src="../../../img/iconDown.png"/>
                            </div>
                        ):(null)}
                        {this.props.showOutlet?(
                            <div class="outletWrapper ellipsis" onClick={this.processLocationClick.bind(this)}>
                                <div class="tagLine">{this.props.criteria == "DINE_IN" ? "Dine In" : "Take Away from"}</div>
                                {this.props.selectedOutlet!=null?this.props.selectedOutlet.label:""}
                                <img class="downIcon" src="../../../img/iconDown.png"/>
                            </div>
                        ):(null)}
                    </div>
                ) : (
                    <div class="headerLogo"><img src="../../../img/logo.svg"/></div>
                )}
                <span class="right">
                    {this.props.showCartBtn ? (
                        <span class="headerBtn rel" onClick={this.goToCart.bind(this)}>
                            {(cartItems > 0) ? (
                                <span class="cartSizeLabel">{cartItems}</span>
                            ) : (null)}
                            <svg width="20px" height="20px"><use xlinkHref={"../../../img/customizationSVGBundle.svg#cart"} /></svg>
                        </span>
                    ) : (null)}
                </span>
            </div>
        )
    }
}
