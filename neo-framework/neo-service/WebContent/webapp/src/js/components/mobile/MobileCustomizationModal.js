import React from "react";
import { connect } from "react-redux";
import appUtil from "../../AppUtil";
import _ from "lodash";
import * as CustomizationModalActions from "../../actions/CustomizationModalActions";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as UtilityActions from "../../actions/UtilityActions";
import productService from "../../service/ProductService";

@connect((store) => {
    return {
        unit: store.outletMenuReducer.unit,
        skipDeliveryPackaging:store.outletMenuReducer.skipDeliveryPackaging,
        product: store.customizationModalReducer.product,
        showError: store.customizationModalReducer.showError,
        showLoader: store.customizationModalReducer.showLoader,
        showModal: store.customizationModalReducer.showModal,
        selectedDimension: store.customizationModalReducer.selectedDimension,
        customizationType: store.customizationModalReducer.customizationType,
        selectedAddons: store.customizationModalReducer.selectedAddons,
        quantity: store.customizationModalReducer.quantity,
        isConstituent: store.customizationModalReducer.isConstituent,
        editItemId:store.cartManagementReducer.editItemId,
        cart:store.cartManagementReducer.cart
    };
})
export default class MobileCustomizationModal extends React.Component {

    constructor() {
        super();
        this.state = {
            product: {},
            sizeDescription:productService.getSizeDescriptions()
        };
        this.init = this.init.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.addAddon = this.addAddon.bind(this);
        this.removeAddon = this.removeAddon.bind(this);
        this.addToCart = this.addToCart.bind(this);
        this.updateQty = this.updateQty.bind(this);
        this.setIngredientProduct = this.setIngredientProduct.bind(this);
        this.setIngredientVariant = this.setIngredientVariant.bind(this);
        this.selectMenuProduct = this.selectMenuProduct.bind(this);
        this.backToCompositeProduct = this.backToCompositeProduct.bind(this);
        this.customizationSubmit = this.customizationSubmit.bind(this);
        this.changeProduct = this.changeProduct.bind(this);
    }

    init() {
    }

    closeModal() {
        this.props.dispatch(CustomizationModalActions.closeModal());
        if(this.props.editItemId!=null){
            this.props.dispatch(CartManagementActions.setEditItemId(null));
        }
    }

    changeDimension(dimension) {
        _.map(dimension.recipe.ingredient.variants, (variant) =>{
            _.map(this.props.selectedDimension.recipe.ingredient.variants, (variantx) =>{
                if(variant.alias==variantx.alias){
                    _.map(variant.details, (detail)=>{
                        _.map(variantx.details, (detailx)=>{
                            //if(detailx.)
                        })
                    })
                }
            })
        });
        dimension.recipe.ingredient.variants.map((variant) => {

        });
        //this.props.selectedDimension.recipe.variants
        this.props.dispatch(CustomizationModalActions.setDimension(dimension));
    }

    changeProduct(product){
        var prod = {...this.props.product, id:product.id, name:product.name};
        if(this.props.customizationType=="MENU_ITEM"){
            this.props.dispatch(CustomizationModalActions.updateCustomizationProduct(prod));
        }else{
            this.props.dispatch(CustomizationModalActions.updateCustomizationProduct(prod));
        }

    }

    addAddon(addon) {
        if(addon==988){
            var addons = [];
            this.props.selectedDimension.recipe.addons.map((addon) => {
                addons.push(addon.product.productId);
            });
            this.props.dispatch(CustomizationModalActions.addAllAddons(addons));
        }else{
            this.props.dispatch(CustomizationModalActions.addAddon(addon));
        }
    }

    removeAddon(addon) {
        if(addon==988){
            this.props.dispatch(CustomizationModalActions.addAllAddons([]));
        }else{
            this.props.dispatch(CustomizationModalActions.removeAddon(addon));
        }
    }

    setIngredientProduct(ingredientProductId, displayName) {
        this.props.dispatch(CustomizationModalActions.setIngredientProduct(ingredientProductId, displayName));
    }

    setIngredientVariant(productId, alias) {
        this.props.dispatch(CustomizationModalActions.setVariant(productId, alias));
    }

    selectMenuProduct(compositeProductDetail, menuProduct) {
        var constituentProduct = null;
        var addons = [];
        this.props.unit.products.map((unitProduct) => {
            if (unitProduct.id == menuProduct.product.productId) {
                constituentProduct = Object.assign({}, unitProduct);
                unitProduct.prices.map((price) => {
                    if (menuProduct.dimension.code == price.dimension) {
                        constituentProduct.prices = [];
                        var constPrice = Object.assign({}, price);
                        if(this.props.customizationType=="MENU_ITEM"){
                            price.recipe.ingredient.products.map((product) => {
                                product.details.map((detail) => {
                                    detail.active = detail.defaultSetting;
                                })
                            });
                            price.recipe.ingredient.variants.map((variant) => {
                                variant.details.map((detail) => {
                                    detail.active = detail.defaultSetting;
                                })
                            });
                        }else{
                            var productFound = false;
                            this.props.cart.orderDetail.orders.map((orderItem) => {
                                if(orderItem.itemId == this.props.editItemId){
                                    orderItem.composition.menuProducts.map((mp) => {
                                        if(mp.itemName==compositeProductDetail.name){
                                            if(menuProduct.product.productId==mp.productId){
                                                productFound = true;
                                                price.recipe.ingredient.products.map((product) => {
                                                    product.details.map((detail) => {
                                                        mp.composition.products.map((cproduct) => {
                                                            if(cproduct.product.productId==detail.product.productId){
                                                                detail.active = true;
                                                            }else{
                                                                detail.active = false;
                                                            }
                                                        });
                                                    });
                                                });
                                                price.recipe.ingredient.variants.map((variant) => {
                                                    variant.details.map((detail) => {
                                                        mp.composition.variants.map((cvariant) => {
                                                            if(cvariant.alias==detail.alias){
                                                                detail.active = true;
                                                            }else{
                                                                detail.active = false;
                                                            }
                                                        });
                                                    });
                                                });
                                                mp.composition.addons.map((addon) => {
                                                    addons.push(addon.product.productId);
                                                })
                                            }
                                        }
                                    })
                                }
                            });
                            if(!productFound){
                                price.recipe.ingredient.products.map((product) => {
                                    product.details.map((detail) => {
                                        detail.active = detail.defaultSetting;
                                    })
                                });
                                price.recipe.ingredient.variants.map((variant) => {
                                    variant.details.map((detail) => {
                                        detail.active = detail.defaultSetting;
                                    })
                                });
                            }
                        }
                        constituentProduct.prices.push(price);
                    }
                })
            }
        });
        this.props.dispatch(CustomizationModalActions.selectMenuProduct(compositeProductDetail.name, menuProduct.product.productId, constituentProduct, addons));
    }

    backToCompositeProduct() {
        this.props.dispatch(CustomizationModalActions.backToCompositeProduct());
    }

    customizationSubmit() {
        var cartObj = this.prepareCartItem();
        this.props.dispatch(CustomizationModalActions.customizationSubmit(cartObj));
    }

    updateQty(amount) {
        var qty = this.props.quantity + amount;
        if (qty > 0) {
            if(this.props.cart!=null && this.props.cart.orderDetail.offerCode!=null){
                this.props.dispatch(CartManagementActions.removeCoupon());
                document.getElementById("couponInput").value = "";
                this.props.dispatch(UtilityActions.showPopup("We have removed coupon from your cart as it is being updated. Please reapply coupon once you have made all the changes.", "info", 6000));
            }
            this.props.dispatch(CustomizationModalActions.updateQty(qty));
        }else if(qty==0){
            if(this.props.editItemId!=null){
                this.props.cart.orderDetail.orders.map((item) => {
                    if(item.itemId==this.props.editItemId){
                        if(this.props.cart!=null && this.props.cart.orderDetail.offerCode!=null){
                            this.props.dispatch(CartManagementActions.removeCoupon());
                            document.getElementById("couponInput")!=null?document.getElementById("couponInput").value = "":null;
                            this.props.dispatch(UtilityActions.showPopup("We have removed coupon from your cart as it is being updated. Please reapply coupon once you have made all the changes.", "info", 6000));
                        }
                        this.props.dispatch(CartManagementActions.removeItem(this.props.cart, item, "cart", this.props.skipDeliveryPackaging));
                    }
                });
            }
            this.closeModal();
        }
    }

    prepareCartItem() {
        var cartItem = {...this.props.product, prices: []};
        var addons = [];
        this.props.selectedAddons.map((addonProductId) => {
            this.props.selectedDimension.recipe.addons.map((addon) => {
                if (addon.product.productId == addonProductId) {
                    addons.push(addon);
                }
            });
        });
        cartItem.selectedDimension = this.props.selectedDimension;
        return {cartItem: cartItem, addons: addons, quantity: this.props.quantity};
    }

    addToCart() {
        var productValid = true;
        if (this.props.product.type == 8) {
            this.props.selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.cartProduct == null && productValid) {
                    this.props.dispatch(UtilityActions.showPopup("Please select " + detail.name, "info"));
                    productValid = false;
                }
            })
        }
        if (productValid) {
            var cartObj = this.prepareCartItem();
            cartObj.customizationStrategy = this.props.product.strategy;
            if(this.props.customizationType=="MENU_ITEM"){
                this.props.dispatch(CartManagementActions.addItemToCart(cartObj));
            }else{
                this.props.dispatch(CartManagementActions.editCartItem(cartObj, this.props.editItemId, this.props.skipDeliveryPackaging));
                this.props.dispatch(CustomizationModalActions.closeModal());
            }
        }
    }

    componentWillMount() {
        this.init();
    }

    render() {
        //console.log(this.props.product, this.props.quantity, this.props.selectedDimension, this.props.isConstituent);
        if(this.props.unit!=null && this.props.product!=null && this.props.selectedDimension!=null){
            var data = [];
            var compositeProductData = null;
            var singleProductData = null;
            var dimensions = [];
            var desiChaiSection = null;
            var dcIds = [10,11,12,50];
            var bwcIds = [1282,1292,1293,1294];
            var dcNames = {10:"Regular",11:"Full Doodh",12:"Doodh Kum",50:"Pani Kum"};
            var bwcNames = {1282:"Regular",1292:"Full Doodh",1294:"Doodh Kum",1293:"Pani Kum"};
            var dcn, dcd, bwcn, bwcd;
            if (!appUtil.checkEmpty(this.props.product)) {
                if(!this.props.isConstituent && dcIds.indexOf(this.props.product.id)>=0){
                    var dcp = [];
                    if(this.props.unit.products!=null){
                        this.props.unit.products.map((product) => {
                            if(product.id==10){
                                dcn = product.name;
                                dcd = product.description;
                                return false;
                            }
                        });
                        this.props.unit.products.map((product) => {
                            if(dcIds.indexOf(product.id)>=0){
                                if(product.id==this.props.product.id){
                                    dcp.push(
                                        <div class="dimensionBtn" key={product.id}>
                                            <div class="active" onClick={this.changeProduct.bind(this, product)}>
                                                {dcNames[product.id]}
                                            </div>
                                        </div>
                                    )
                                }else{
                                    dcp.push(
                                        <div class="dimensionBtn" key={product.id}>
                                            <div onClick={this.changeProduct.bind(this, product)}>
                                                {dcNames[product.id]}
                                            </div>
                                        </div>
                                    )
                                }
                            }
                        });
                    }

                    desiChaiSection = (
                        <div class="customizationSection">
                            <div class="customizationHead">Milk</div>
                            {dcp}
                        </div>
                    );
                }
                if(!this.props.isConstituent && bwcIds.indexOf(this.props.product.id)>=0){
                    var bwcp = [];
                    if(this.props.unit.products!=null){
                        this.props.unit.products.map((product) => {
                            if(product.id==1282){
                                bwcn = product.name;
                                bwcd = product.description;
                                return false;
                            }
                        });
                        this.props.unit.products.map((product) => {
                            if(bwcIds.indexOf(product.id)>=0){
                                if(product.id==this.props.product.id){
                                    bwcp.push(
                                        <div class="dimensionBtn" key={product.id}>
                                            <div class="active" onClick={this.changeProduct.bind(this, product)}>
                                                {bwcNames[product.id]}
                                            </div>
                                        </div>
                                    )
                                }else{
                                    bwcp.push(
                                        <div class="dimensionBtn" key={product.id}>
                                            <div onClick={this.changeProduct.bind(this, product)}>
                                                {bwcNames[product.id]}
                                            </div>
                                        </div>
                                    )
                                }
                            }
                        });
                    }

                    desiChaiSection = (
                        <div class="customizationSection">
                            <div class="customizationHead">Milk</div>
                            {bwcp}
                        </div>
                    );
                }
                if (this.props.product.prices.length > 1) {
                    var dimension = [];
                    var prices = this.props.product.prices.sort(function (a,b) {
                        return a.price - b.price;
                    });
                    prices.map((price, index) => {
                        var hideMiniKetli = this.props.unit.packagingType != "PERCENTAGE";
                        if((hideMiniKetli && price.dimension != "MiniKetli") || !hideMiniKetli) {
                            if (price.dimension == this.props.selectedDimension.dimension) {
                                dimension.push(
                                    <div class="dimensionBtn" key={price.dimension}>
                                        <div class="active" onClick={this.changeDimension.bind(this, price)}>
                                            <svg width="21px" height="24px"><use xlinkHref={"../../../img/customizationSVGBundle.svg#"+(price.dimension=="None"?"Regular":price.dimension.replace(" ",""))+"active"} /></svg>
                                            {price.dimension=="None"?"Regular":price.dimension}
                                        </div>
                                        <span class="sizeDescription">{this.props.product.webType==3625?this.state.sizeDescription[price.dimension+"3625"]:this.state.sizeDescription[price.dimension]}</span>
                                    </div>
                                );
                            } else {
                                dimension.push(
                                    <div class="dimensionBtn" key={price.dimension}>
                                        <div onClick={this.changeDimension.bind(this, price)}>
                                            <svg width="21px" height="24px"><use xlinkHref={"../../../img/customizationSVGBundle.svg#"+(price.dimension=="None"?"Regular":price.dimension.replace(" ",""))} /></svg>
                                            {price.dimension=="None"?"Regular":price.dimension}
                                        </div>
                                        <span class="sizeDescription">{this.props.product.webType==3625?this.state.sizeDescription[price.dimension+"3625"]:this.state.sizeDescription[price.dimension]}</span>
                                    </div>
                                );
                            }
                        }
                    });
                    dimensions = (
                        <div class="customizationSection">
                            <div class="customizationHead">Size</div>
                            {dimension}
                            <div class="clear"></div>
                        </div>
                    );
                }
                if (this.props.product.type == 8) {
                    //composite product customization
                    var items = [];
                    if (this.props.selectedDimension.recipe != null) {
                        this.props.selectedDimension.recipe.ingredient.compositeProduct.details.map((detail, index) => {
                            var productData = [];
                            detail.menuProducts.map((menuProduct) => {
                                if (detail.selectedProduct != null && detail.selectedProduct.product.productId == menuProduct.product.productId) {
                                    productData.push(
                                        <div key={menuProduct.product.productId} class="menuProductBtn">
                                            <div onClick={this.selectMenuProduct.bind(this, detail, menuProduct)}
                                                 class="ellipsis active">{menuProduct.product.name}</div>
                                        </div>
                                    )
                                } else {
                                    productData.push(
                                        <div key={menuProduct.product.productId} class="menuProductBtn">
                                            <div onClick={this.selectMenuProduct.bind(this, detail, menuProduct)}
                                                 class="ellipsis">{menuProduct.product.name}</div>
                                        </div>
                                    )
                                }
                            });
                            items.push(
                                <div key={index} class="customizationSection">
                                    <div class="customizationHead">{detail.name}</div>
                                    {productData}
                                    <div class="clear"></div>
                                </div>
                            )
                        })
                    }
                    compositeProductData = (
                        <div>
                            {items}
                            {!this.props.showLoader ? (
                                <div class="pairBtnWrapper">
                                    <div class="btn btn-defualt leftBtn" onClick={this.closeModal.bind(this)}>Cancel</div>
                                    <div class="btn btn-primary mainBtn" onClick={this.addToCart.bind(this)}>
                                        {this.props.customizationType=="MENU_ITEM"?"Add to cart":"Edit Item"}
                                    </div>
                                </div>
                            ) : (null)}
                        </div>
                    )
                } else {
                    //menu product customization
                    var addons = [];
                    var ingredientProducts = [];
                    var ingredientVariants = [];

                    if (this.props.selectedDimension.recipe != null) {
                        if (this.props.selectedDimension.recipe.addons.length > 0 && [3638,3639,3640,3641,3642].indexOf(this.props.product.webType)==-1) {
                            var addonData = [];
                            addonData.push(
                                <div key={56453245} class="customizationHead">Addons</div>
                            );
                            this.props.selectedDimension.recipe.addons.map((addon) => {
                                var found = false;
                                if (this.props.selectedAddons != null && this.props.selectedAddons.length > 0) {
                                    this.props.selectedAddons.map((addonProductId) => {
                                        if (addonProductId == addon.product.productId) {
                                            addonData.push(
                                                <div key={addon.product.productId}  class={addon.product.productId==988?"addonBtn subKuch":"addonBtn"}>
                                                    <div onClick={this.removeAddon.bind(this, addon.product.productId)} class="active ellipsis">
                                                        <svg width="20px" height="22px"><use xlinkHref={"../../../img/customizationSVGBundle.svg#"+addon.product.productId+"active"} /></svg>
                                                        {addon.product.name}
                                                    </div>
                                                </div>
                                            );
                                            found = true;
                                        }
                                    })
                                }
                                if (!found) {
                                    addonData.push(
                                        <div key={addon.product.productId} class={addon.product.productId==988?"addonBtn subKuch":"addonBtn"}>
                                            <div onClick={this.addAddon.bind(this, addon.product.productId)} class="ellipsis">
                                                <svg width="20px" height="22px"><use xlinkHref={"../../../img/customizationSVGBundle.svg#"+addon.product.productId} /></svg>
                                                {addon.product.name}
                                            </div>
                                        </div>
                                    )
                                }
                                addons = (
                                    <div class="customizationSection">
                                        {addonData}
                                        <div class="clear"></div>
                                    </div>
                                );
                            });
                        }

                        if (this.props.selectedDimension.recipe.ingredient.products.length > 0) {
                            this.props.selectedDimension.recipe.ingredient.products.map((product, pindex) => {
                                var detail = [];
                                product.details.map((productDetail, index) => {
                                    detail.push(
                                        <div key={index} class="ingredientProductBtn">
                                            <div class={productDetail.active==true?"ellipsis active":"ellipsis"}
                                                 onClick={this.setIngredientProduct.bind(this, productDetail.product.productId, product.display)}>{productDetail.product.name}</div>
                                        </div>
                                    )
                                });
                                ingredientProducts.push(
                                    <div key={pindex} class="customizationSection">
                                        <div class="customizationHead">{product.display}</div>
                                        {detail}
                                        <div class="clear"></div>
                                    </div>
                                )
                            });
                        }

                        if (this.props.selectedDimension.recipe.ingredient.variants.length > 0 && [3638,3639,3640,3641,3642].indexOf(this.props.product.webType)==-1) {
                            this.props.selectedDimension.recipe.ingredient.variants.map((variant) => {
                                var detail = [];
                                variant.details.map((variantDetail, index) => {
                                    detail.push(
                                        <div key={index} class="ingredientVariantBtn">
                                            <div class={variantDetail.active==true?"ellipsis active":"ellipsis"} onClick={this.setIngredientVariant.bind(this, variant.product.productId, variantDetail.alias)}>
                                                <svg width="19px" height="20px"><use xlinkHref={"../../../img/customizationSVGBundle.svg#"+
                                                (variantDetail.active==true?variantDetail.alias.toLowerCase()+"active":variantDetail.alias.toLowerCase())} /></svg>
                                                {variantDetail.alias}
                                            </div>
                                        </div>
                                    )
                                })
                                ingredientVariants.push(
                                    <div key={variant.product.productId} class="customizationSection">
                                        <div class="customizationHead">{variant.product.displayName!=null?variant.product.displayName:variant.product.name}</div>
                                        {detail}
                                        <div class="clear"></div>
                                    </div>
                                )
                            });
                        }
                    }

                    singleProductData = (
                        <div>
                            {dimensions}
                            {!this.props.showLoader ? (
                                desiChaiSection
                            ):(null)}
                            {ingredientProducts}
                            {ingredientVariants}
                            {addons}
                            {!this.props.showLoader ? (
                                <div class="pairBtnWrapper">
                                    {!this.props.isConstituent ? (
                                        <div>
                                            <div class="btn btn-default leftBtn" onClick={this.closeModal.bind(this)}>Cancel</div>
                                            <div class="btn btn-primary mainBtn" onClick={this.addToCart.bind(this)}>
                                                {this.props.customizationType=="MENU_ITEM"?"Add to cart":"Edit Item"}
                                            </div>
                                        </div>
                                    ) : (
                                        <div>
                                            <div class="btn btn-default leftBtn" onClick={this.backToCompositeProduct.bind(this)}>Cancel
                                            </div>
                                            <div class="btn btn-primary mainBtn" onClick={this.customizationSubmit.bind(this)}>Done
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ) : (null)}
                        </div>
                    )

                }

                //common header for both composite as well as single product customization

                data = (
                    <div>
                        {this.props.isConstituent ? (
                            <div class="customizationSection productHead">
                                <div class="rel constituent">
                                    <div class="productName">{this.props.product.name}</div>
                                    <div class="qtyWrapper">Qty: {this.props.quantity}</div>
                                    <div class="productDesc">{this.props.product.description}</div>
                                </div>
                            </div>
                        ) : (
                            <div class="customizationSection productHead">
                                <div class="rel">
                                    <div class="productName">
                                        {dcIds.indexOf(this.props.product.id)>=0?dcn:null}
                                        {bwcIds.indexOf(this.props.product.id)>=0?bwcn:null}
                                        {(dcIds.indexOf(this.props.product.id)<0 && bwcIds.indexOf(this.props.product.id)<0)?this.props.product.name:null}
                                    </div>
                                    <div class="productPrice">
                                        <img class="rupeeIcon" src="../../img/rupee.png"/>
                                        {Math.round(this.props.selectedDimension.priceInclusiveTax * this.props.quantity)}
                                    </div>
                                </div>
                                <div class="rel">
                                    <div class="productDesc">{dcIds.indexOf(this.props.product.id)>=0?dcd:this.props.product.description}</div>
                                    <div class="qtyWrapper">
                                        <div class="incr" onClick={this.updateQty.bind(this, 1)}>&#43;</div>
                                        <div class="qty">{this.props.quantity}</div>
                                        <div class="dcr" onClick={this.updateQty.bind(this, -1)}>&#45;</div>
                                    </div>
                                </div>
                            </div>
                        )}
                        {this.props.product.type == 8 ? (
                            <div>{compositeProductData}</div>
                        ) : (
                            <div>{singleProductData}</div>
                        )}
                    </div>
                )

            }

            if (this.props.showModal) {
                return (
                    <div class="modal">
                        <div class="modalBody">
                            {!this.props.isConstituent ? (
                                <div class="modalCloseBtn" onClick={this.closeModal.bind(this)}>
                                    &times;
                                </div>
                            ) : (
                                <div class="backArrow" onClick={this.backToCompositeProduct.bind(this)}>
                                    <img class="menuIcon" src="../../../img/backGrey.svg" />
                                </div>
                            )}
                            <p class="modalTitle">
                                Customize Item
                            </p>
                            {data}
                            {this.props.showLoader ? (
                                <div class="load8 loader"></div>
                            ) : (null)}
                        </div>
                    </div>
                )
            } else {
                return (null);
            }
        }else {
            return (null);
        }
    }
}