import React from "react";
import {connect} from "react-redux"
import MobileOutletMenuLayout from "./MobileOutletMenuLayout";
import * as LocalityActions from "../../actions/LocalityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as UtilityActions from "../../actions/UtilityActions";

@connect((store) => {
    return {
        showFullPageLoader: store.utilityReducer.showFullPageLoader,
        loaderMessage: store.utilityReducer.loaderMessage,
    };
})
export default class MobileUtilityLayout extends React.Component {

    constructor(){
        super();
        this.init = this.init.bind(this);
    }

    init(){
    }


    componentWillMount(){
        this.init();
        const params = this.props.props.params;
        this.props.dispatch(OutletMenuActions.loadDineinMenu(params.unitId));
    }

    componentDidMount(){
        this.props.dispatch(UtilityActions.hideFullPageLoader());
    }

    render() {

        return (
            <div>
                {this.props.showFullPageLoader ? (
                    <div class="fullPageLoader">
                        <div class="loaderWrapper">
                            <div class="load8 loader"></div>
                            {this.props.loaderMessage != null ? (
                                <p class="loaderMessage">{this.props.loaderMessage}</p>
                            ) : (null)}
                        </div>
                    </div>
                ) : (
                    <MobileOutletMenuLayout />
                )}
            </div>
        )
    }
}