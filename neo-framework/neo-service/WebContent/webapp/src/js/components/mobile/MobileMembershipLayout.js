import React from "react";
import trackUtils from "../../utils/TrackUtils";
import * as CartManagementActions from "../../actions/CartManagementActions";
import {connect} from "react-redux";
import * as MembershipActions from "../../actions/MembershipActions";
import appUtil from "../../AppUtil";
import Slider from "react-slick";


@connect((store) => {
    return {
        selectedCity: store.localityReducer.selectedCity,
        unit: store.outletMenuReducer.unit,
        cart: store.cartManagementReducer.cart,
        product: store.membershipReducer.product
    };
})

export default class MobileMembershipLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            images: [{
                "index": 1,
                "url": "https://hello.jpg"
            }]
        };
        this.onPressSubscribe = this.onPressSubscribe.bind(this);
        this.placeMembershipOrder = this.placeMembershipOrder.bind(this);
        this.init = this.init.bind(this);
    }

    placeMembershipOrder() {
        this.props.dispatch(CartManagementActions.placeOrder(this.props.cart, this.props.unit, this.props.selectedCity));
    }

    componentDidMount() {
        trackUtils.trackPageView({page: "membership", device: "mobile", custom: true});
    }

    onPressSubscribe = () => {
        this.props.dispatch(MembershipActions.setCartMembershipItem(this.props.product.skuCodeProduct, () => {
            this.props.dispatch(this.placeMembershipOrder());
        }));
    }

    componentWillMount() {
        this.init();
    }

    init() {
        let code = " ";
        let obj = [];
        const params = this.props.props.params.type;
        if (appUtil.checkEmpty(params)) {
            code = "chaayos_select";
        } else if (params === 'select') {
            code = "chaayos_select";
        } else if (params === 'workpass') {
            code = "chaayos_workpass";
        } else {
            code = params;
        }
        this.props.dispatch(MembershipActions.getMembershipData("26254", {code}, product => {
            obj = product.skuCodeMarketingImageMap.MARKETING_IMAGES_MOB_VIEW;
            this.setState({images: obj});
        }));
        this.props.dispatch(MembershipActions.setMembershipType(code));
    }

    render() {
        var settings = {
            dots: true,
            infinite: true,
            speed: 500,
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 5000,
        };
        let h = window.innerHeight;
        let w = window.innerWidth;
        return (
            <div className="membershipContainer">
                <div>
                    {this.state.images.length >= 1 ?
                        <Slider {...settings}>
                            {
                                this.state.images.map((data, index) => {
                                    return (
                                        <div key={index}>
                                            <img className="membershipMobile" style={{width: w, height: h}}
                                                 src={"https://d3pjt1af33nqn0.cloudfront.net/product_image/" + data.url}/>
                                        </div>
                                    )
                                })
                            }
                        </Slider> : null}
                    <button className="membershipButtonMobile" style={{top: h * 0.9, height: h * 0.1}}
                            onClick={this.onPressSubscribe.bind(this)}>Subscribe Now
                    </button>
                </div>
            </div>
        )
    }

}
