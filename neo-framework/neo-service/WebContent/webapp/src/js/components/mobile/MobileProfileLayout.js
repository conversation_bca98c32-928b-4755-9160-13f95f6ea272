import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux"
import MobileHeader from "./MobileHeader";
import {
    setAddressViewType,
    getLoyalteaScore,
    setCustomerDetail,
    faceItOptOut,
    getAvailableGiftCardInfo
} from "../../actions/CustomerActions";
import trackUtils from "../../utils/TrackUtils";
import appUtil from "../../AppUtil";

@connect((store) => {
    return {
        loyalteaPending: store.customerReducer.loyalteaPending,
        customerLoyalteaScore: store.customerReducer.customerLoyalteaScore,
        customer: store.customerReducer.customer,
    };
})
export default class MobileProfileLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            customer:null
        };
        this.goToAddresses =this.goToAddresses.bind(this);
        this.goToRefer =this.goToRefer.bind(this);
    }

    goToAddresses(){
        this.props.dispatch(setAddressViewType("PROFILE"));
        browserHistory.push("/addresses");
    }

    goToRefer(){
        window.location.href = "https://cafes.chaayos.com/refer?r="+this.props.customer.refCode+"&n="+this.props.customer.name+"&c=CLM&s=WEBAPP";
    }

    faceItOptOut() {
        this.props.dispatch(faceItOptOut(this.props.customer));
    }

    componentWillMount() {
        this.props.dispatch(getLoyalteaScore());
        this.props.dispatch(getAvailableGiftCardInfo())
    }

    componentDidMount(){
        this.props.dispatch(setCustomerDetail());
        trackUtils.trackPageView({page:"account",device:"mobile",custom:true});
    }

    render() {

        return (
            <div>
                <div class="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                <div class="mobilePageContainer">
                    <div class="mobileProfileHead">
                        {this.props.customer!=null?(
                            <div>
                                <img class="profilePic" src="../../../img/profilePic.png" />
                                <div class="userName">{this.props.customer.name}</div>
                                <div class="userContact">{this.props.customer.contact}</div>
                                <div class="userEmail">{this.props.customer.email}</div>
                            </div>
                        ):(
                            <div>
                                <img class="profilePic" src="../../../img/profilePic.png" />
                                <div class="userName" style={{background:"#ddd", padding:"5px", margin:"auto", width:"100px", marginTop:"15px", marginBottom:"10px"}}></div>
                                <div class="userContact" style={{background:"#ddd", padding:"3px", margin:"auto", width:"70px", marginBottom:"5px"}}></div>
                                <div class="userEmail" style={{background:"#ddd", padding:"2px", margin:"auto", width:"90px", marginBottom:"5px"}}></div>
                            </div>
                        )}
                    </div>
                    <div class="profileCard">
                        {this.props.loyalteaPending?(
                            <div class="loader load8"></div>
                        ):(
                            this.props.customerLoyalteaScore!=null?(
                                <div class="loyalteaScore">{this.props.customerLoyalteaScore}</div>
                            ):(
                                <div style={{color:"red",fontSize:"14px", textAlign:"center"}}>Error in getting LoyalTea score.</div>
                            )
                        )}
                        <div class="cardTitle text-center">LoyalTea Points</div>
                    </div>
                    <div class="profileCard">
                        {this.props.loyalteaPending ? (
                            <div class="loader load8"></div>
                        ) : (
                            this.props.customer != null ? (
                                <div class="loyalteaScore">{this.props.customer.chaayosCash}</div>
                            ) : (
                                <div style={{color: "red", fontSize: "14px", textAlign: "center"}}>Error in getting
                                    Chaayos Cash.</div>
                            )
                        )}
                        <div class="cardTitle text-center">Chaayos Cash</div>
                    </div>
                    <div class="profileCard">
                        {this.props.giftcardAmountPending ? (
                            <div class="loader load8"></div>
                        ) : (
                            !appUtil.checkEmpty(this.props.customer)
                                && !appUtil.checkEmpty(this.props.customer.cardAmount) ? (
                                <div class="giftCardAmount">{this.props.customer.cardAmount}</div>
                            ) : (
                                <div style={{color: "red", fontSize: "14px", textAlign: "center"}}>Error in getting
                                    available card amount.</div>
                            )
                        )}
                        <div className="cardTitle text-center">Gift Card Balance</div>
                    </div>
                    {(this.props.customer != null && this.props.customer.refCode!= null) ?
                        <div class="profileCard">
                            <div class="loyalteaScore">{this.props.customer.refCode}</div>
                            <div class="cardTitle text-center">Referral Code</div>
                        </div>
                        :null
                    }
                    {(this.props.customer != null && this.props.customer.refCode!= null) ?
                        <div class="profileCard" style={{textAlign:"center"}}>
                            <div style={{background:"#5e7e47", display:"inline-block", borderRadius:"5px", padding:"13px 20px", fontSize:"21px", color:"#FFF", cursor:"pointer"}}
                            onClick={this.goToRefer.bind(this)}>
                                Start referring
                            </div>
                        </div>
                        :null
                    }
                    {(this.props.customer != null) ?
                        <div class="profileCard" style={{textAlign:"center"}}>
                            {this.props.customer.optOutOfFaceIt == false ? (
                                <div style={{background:"#5e7e47", display:"inline-block", borderRadius:"5px", padding:"13px 20px", fontSize:"21px", color:"#FFF", cursor:"pointer"}}
                                     onClick={this.faceItOptOut.bind(this)}>
                                    FaceIT Opt Out
                                </div>
                            ) : (
                                <div>You have opted out of FaceIT</div>
                            )}
                        </div>
                        :null
                    }
                    {/*<div class="profileCard">
                        <div class="cardTitle">My Addresses</div>
                        <div class="cardFooter" onClick={this.goToAddresses.bind(this)}>
                            <img src="../../../img/rightArrow.svg" /> View your addresses
                        </div>
                    </div>*/}
                </div>
            </div>
        )
    }
}
