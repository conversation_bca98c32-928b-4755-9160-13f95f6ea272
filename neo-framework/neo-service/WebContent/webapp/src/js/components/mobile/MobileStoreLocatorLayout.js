import React from "react";
import {connect} from "react-redux";
import MobileHeader from "./MobileHeader";
import trackUtils from "../../utils/TrackUtils";
import * as UtilityActions from "../../actions/UtilityActions";
import {browserHistory} from "react-router";

@connect((store) => {
    return {
        showError: store.customizationModalReducer.showError,
        showLoader: store.customizationModalReducer.showLoader,
        cityOutlet: store.utilityReducer.cityOutlet,
        cityOutlets: store.utilityReducer.cityOutlets,
        cityOutletsLoaded: store.utilityReducer.cityOutletsLoaded,
    };
})
export default class MobileStoreLocatorLayout extends React.Component {

    constructor() {
        super();
        this.goToMenu = this.goToMenu.bind(this);
        this.loadCityLocator = this.loadCityLocator.bind(this);
        this.loadMap = this.loadMap.bind(this);
    }

    goToMenu(unit) {
        trackUtils.trackStoreLocatorDineInClicked({});
        this.props.dispatch(UtilityActions.showFullPageLoader("Loading menu..."));
        browserHistory.push("/dinein/" + unit.unitId);
    }

    goToDeliveryMenu(unit) {
        trackUtils.trackStoreLocatorDeliveryClicked({});
        browserHistory.push("/deliveryRedirect/" + unit.unitId);
    }

    loadCityLocator(city) {
        browserHistory.push("/stores/" + city);
    }

    loadMap(unit) {
        window.open(unit.googleLink,'_blank');
    }

    gtag_report_conversion() {
        try{
            /*const url = window.location;
            var callback = function () {
                if (typeof(url) != 'undefined') {
                    window.location = url;
                }
            };*/
            window.gtag('event', 'conversion', {
                'send_to': 'AW-857135357/ISfXCLeo2KQBEP2x25gD',
                'event_callback': null
            });
            return false;
        } catch(e){
            console.log(e);
        }
    }

    componentWillMount() {
        const params = this.props.props.params;
        if (params.city == null) {
            params.city = "Delhi";
        }
        this.props.dispatch(UtilityActions.getOutletByCity(params.city));
    }

    componentDidMount() {
        this.props.dispatch(UtilityActions.hideFullPageLoader());
        trackUtils.trackPageView({page: "stores", device: "mobile", custom: true});
        this.gtag_report_conversion();
    }

    componentDidUpdate(prevProps, prevState) {
        if(prevProps.props.params.city != this.props.props.params.city) {
            const params = this.props.props.params;
            if (params.city == null) {
                params.city = "Delhi";
            }
            this.props.dispatch(UtilityActions.getOutletByCity(params.city));
        }
    }

    render() {

        const params = this.props.props.params;
        if (params.city == null) {
            params.city = "Delhi";
        }

        let outlets = [];
        if (this.props.cityOutlets != null) {
            const _this = this;
            this.props.cityOutlets.map(function (outlet) {
                outlets.push(
                    <div class="cityOutletItem" key={outlet.unitId}>
                        <div class="head">Chaayos {outlet.unitName}</div>
                        <div class="body">
                            <div class="address">
                                <img src="../../../img/location-pointer.png" /> <span>{outlet.address}</span>
                            </div>
                            {/*<a href="tel:18001202424" class="call">Call</a>*/}
                            <div className="call" onClick={_this.loadMap.bind(_this, outlet)}>
                                <img src="../../../img/location-pointer.png" /> Locate
                            </div>
                            <div className="locate" onClick={_this.goToDeliveryMenu.bind(_this, outlet)}>
                                <img src="../../../img/scooter.png" />Delivery
                            </div>
                            <div className="order" onClick={_this.goToMenu.bind(_this, outlet)}>
                                <img src="../../../img/coffee-cup.png" />Dine In
                            </div>
                        </div>
                    </div>
                )
            })
        }

        return (
            <div class="colouredHead">
                <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false}
                              props={this.props.props}/>
                <div class="mobilePageContainer" style={{background: "#FFF"}}>
                    <div class="mobilePageHead">Store Locator</div>
                    <div class="citySelector">
                        <div class={params.city == "Delhi" ? "cityIcon active" : "cityIcon"}
                             onClick={this.loadCityLocator.bind(this, "Delhi")}>
                            <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                 xmlnsXlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 40 40"
                                 enableBackground="new 0 0 100 100" xmlSpace="preserve" style={{maxWidth:"60px"}}>
                                <use xlinkHref="/img/regions-icons.svg#icon-mumbai"></use>
                            </svg>
                            <div class="cityTag">Delhi NCR</div>
                        </div>
                        <div class={params.city == "Mumbai" ? "cityIcon active" : "cityIcon"}
                             onClick={this.loadCityLocator.bind(this, "Mumbai")}>
                            <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                 xmlnsXlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 40 40"
                                 enableBackground="new 0 0 100 100" xmlSpace="preserve" style={{maxWidth:"60px"}}>
                                <use xlinkHref="/img/regions-icons.svg#icon-ncr"></use>
                            </svg>
                            <div class="cityTag">Mumbai</div>
                        </div>
                        <div class={params.city == "Chandigarh" ? "cityIcon active" : "cityIcon"}
                             onClick={this.loadCityLocator.bind(this, "Chandigarh")}>
                            <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                 xmlnsXlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 40 40"
                                 enableBackground="new 0 0 100 100" xmlSpace="preserve" style={{maxWidth:"60px"}}>
                                <use xlinkHref="/img/regions-icons.svg#icon-chd"></use>
                            </svg>
                            <div class="cityTag">Chandigarh</div>
                        </div>
                        <div class={params.city == "Bangalore" ? "cityIcon active" : "cityIcon"}
                             onClick={this.loadCityLocator.bind(this, "Bangalore")}>
                            <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                 xmlnsXlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 40 40"
                                 enableBackground="new 0 0 100 100" xmlSpace="preserve" style={{maxWidth:"60px"}}>
                                <use xlinkHref="/img/regions-icons.svg#icon-bang"></use>
                            </svg>
                            <div class="cityTag">Bangalore</div>
                        </div>
                    </div>

                    {this.props.cityOutletsLoaded == true ? (
                        outlets.length == 0 ?
                            <p style={{fontSize: "18px", textAlign: "center", padding: "50px"}}>Coming soon...</p>
                            :
                            <div class="cityOutletListContainer">
                                <div class="cityOutletList">
                                    {outlets}
                                </div>
                            </div>
                    ) : (
                        <div class="loaderWrapper">
                            <div class="load8 loader"></div>
                            <p class="loaderMessage">Loading outlets...</p>
                        </div>
                    )}
                </div>

            </div>
        )
    }
}