export const styles = {
    parentContainer: {
        height:window.innerHeight,
        width:window.innerWidth,
        display:'none',
        flexDirection:'column',
        justifyContent:'space-around',
        alignItems: 'center',
        overflowY:'scroll',
        overflowX:'hidden'
    },parentContainerOffer: {
        height:window.innerHeight,
        width:window.innerWidth,
        display:'none',
        flexDirection:'column',
        justifyContent:'center',
        alignItems: 'center',
        overflow:'scroll',
    },
    leftChildContainer:{
        flex:'50%',
        display: 'flex',
        flexDirection: 'column',
    },
    rightChildContainer:{
        flex:'50%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent:'flex-end',
        alignItems: 'center',
        backgroundSize:'contain',
        backgroundRepeat: 'no-repeat'
    },
    slotMachineContainer: {
        width: "100%",
        height: "30%",
        backgroundSize: 'contain',
        backgroundImage: `url("./../../../img/SlotGame/slotMachine.png")`,
        backgroundRepeat:'no-repeat',
        backgroundPosition:'center',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        marginLeft: '10%',
        marginRight: '10%',
        flexDirection:'column',
        minHeight:'30%'
    },
    gameOfferScreenContainer: {
        height: '100%',
        width: '100%',
        backgroundImage: `url("../../../img/ninjaGameImg/images/game_offer_screen.jpg")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        display:'flex',
        flexDirection:'column',
        justifyContent:'space-between',
        alignItems:'flex-end',
        padding:'5%'
    },
    slotBoxContainer: {
        width:'60%',
        height:'40%',
        display:'flex',
        flexDirection:'row',
        justifyContent:'space-around',
        alignItems:'center',
        marginTop:'9%',
        maxHeight:window.innerHeight*.4*.4,
        maxWidth: '300px'
    },
    formAndButtonContainer: {
        width: "90%",
        height: "40%",
        display:'flex',
        flexDirection:'column',
        justifyContent:'flex-start',
        alignItems:'center',
        textAlign: 'center'
    },
    door: {
        background: '#fafafa',
        boxShadow: '0 0 3px 2px rgba(0, 0, 0, 0.4) inset',
        width: '30%',
        height: '80%',
        overflow: 'hidden',
        borderRadius: '10px',
        margin: '1px',
        maxWidth:'73px',
        maxHeight: '73px'
    },
    boxes: {
        transition: 'transform 1s ease-in-out'
    },
    spinButton: {
        width:'60%',
        height:'60px',
        borderRadius: '100px',
        backgroundColor: 'green',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        marginTop:'5%',
        boxShadow:'1px 1px 10px grey',
        cursor:'pointer'
    },
    phoneNumberBox: {
        display:'flex',
        flexDirection:'row',
        height : '50px',
        width: '100%',
        borderRadius:'10px',
    },
    phoneNumberBoxChildLeft:{
        flex:2,
        borderRadius:'5px 0px 0px 5px',
        backgroundColor: '#0a6001',
        height:'100%',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        padding: '0 10px'
    },
    phoneNumberBoxChildRight:{
        flex:10,
        borderRadius:'0px 5px 5px 0px',
        backgroundColor: 'white',
        height:'100%',
        borderRight: '2px solid #0a6001',
        borderTop: '2px solid #0a6001',
        borderBottom: '2px solid #0a6001',
    },
    phoneNumberTextField: {
        width: '100%',
        height:'100%',
        marginLeft: 10,
        backgroundColor: 'transparent',
        border: 0,
        fontSize: 15,
        color: '#0a6001',
        fontWeight:'bold',
        fontFamily:'Nunito'
    },
    spinButtonText: {
        color: 'white',
        fontSize: '22px',
        fontWeight:'bold',
        fontFamily:'Nunito'
    },
    smallGreyText: {
        color:'#0a6001',
        fontFamily: 'Nunito',
        fontSize:'3vw',
        fontWeight: 'bold',
        textAlign:'center',
        alignSelf:'center'
    },
    modal: {
        display:'flex',
        width:window.innerWidth,
        height:window.innerHeight,
        overflow: "scroll",
        justifyContent:'center',
        justifyItems:'center',
        flexDirection:'column',
        backgroundColor:'#ffe9ca'
    },
    modalBody: {
        padding: '20px',
        backgroundRepeat:'no-repeat',
        backgroundSize:'contain',
        backgroundPosition: 'center',
        backgroundColor:'rgb(0,0,0,0)',
        display:'flex',
        flexDirection:'column',
        justifyContent:'center',
        alignItems:'center',
        left:0,
        width:'100%',
        height:(window.innerWidth*13)/8,
        overflow:'scroll'
    },
    downloadImg: {
        width: '100%',
    },
    downloadStoreContainer: {
        width: '49%',

    },
    downloadContainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '80%',
        marginLeft: '10%',

    },
    downloadText: {
        textAlign: 'center',
        marginBottom: 3,
        fontFamily: 'Nunito',
        fontSize: '14px',
        color: '#4a4a4a',
    }, couponCodeBox: {
        width:'100%',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        padding:'5px 25px 5px 25px',
        height:'70px',
        backgroundImage:`url(./../../../img/ninjaGameImg/images/couponCodeBg.png)`,
        backgroundSize:'contain',
        backgroundRepeat:'no-repeat'
    },
    couponCodeContainer: {
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        flexDirection:'column',
        marginTop:'20px'
    },
    tncContainer: {
        display:'flex',
        justifyContent:'flex-start',
        alignItems:'center',
        borderRadius:'10px',
        flexDirection:'column',
        padding:'5px',
        minWidth:'20%',
        width:'100%',
        marginBottom: '30px',
        backgroundColor:'#ffffff70'
    },
    tncText:{
        fontSize:'17px',
        color:'black',
        wordSpacing:'1px',
        textAlign:'center'
    },
    youHaveWonText: {
        fontSize: '15px',
        color: '#093D16',
        fontWeight: 'bold'
    },
    offerTextStyle: {
        fontSize:'30px',
        color:'#C2541A',
        fontWeight:'bold'
    },
    couponCodeText: {
        fontSize:'35px',
        color:'green',
        fontWeight:'bold',
        letterSpacing:'4px'
    },
    useCodeText:{
        fontSize:'30px',
        color:'green',
        fontWeight:'bolder',
        marginTop:'30px',
        textAlign:'center'
    },
    winJackPotText: {
        marginRight: '6%',
        fontWeight:'bold',
        marginBottom: '10px',
        color:'#f2df95'
    },

    tncHeading: {
        fontSize:'23px',
        color:'black',
        wordSpacing:'2px',
        marginBottom:'10px'
    },
    canvasStyles : {
        position: "fixed",
        pointerEvents: "none",
        width: "100%",
        height: "100%",
        top: 0,
        left: 0
    },
    // loaderStyle: {
    //     border: '16px solid #f3f3f3', /* Light grey */
    //     borderTop: '16px solid #3498db', /* Blue */
    //     borderRadius: '50%',
    //     width: '120px',
    //     height: '120px',
    //     animation: 'spin 2s linear infinite'
    // },
    checkBox: {
        height:'20px',
        width:'20px',
        borderRadius:'7px',
        border:'2px solid #008000',
        backgroundSize:'contain',
        backgroundRepeat:'no-repeat',
        backgroundColor:'#e9e9e9',
        boxShadow: '1px 1px 10px #a5a5a5',
        display:'flex',
        justifyContent:'center',
        alignItems:'center'
    },
    tabModalBody:{
        width:'90%',
        height:'90%',
        maxWidth:'90%',
        maxHeight:'90%',
        margin:'5px 5px',
    }, exploreMenuButton: {
        width:'50%',
        height:'25px',
        borderRadius:'100px',
        backgroundColor:"#3c8616",
        flexDirection:'column',
        display:'flex',
        justifyContent:'center',
        justifyItems: 'center'
    },
    gameTncContainer: {
        display:'flex',
        justifyContent:'flex-start',
        alignItems:'center',
        width:'95%',
        borderRadius:'10px',
        border:'1px green solid',
        flexDirection:'column',
        padding:'10px',
        marginTop:'5%'
    },
    gameTncText:{
        fontSize:'10px',
        color:'green',
        textAlign:'left'
    },
};
