import React, { Component } from 'react'

export default class SavingsCalculator extends Component {
  constructor(props){
    super(props);
    this.state={
        value1:10,
        value2:500,
    }
  }
 
  render() {
    const self=this
    function handleChange1(e){
      if(e.target.value>180){return}
        self.setState({
          value1:e.target.value
        })
    }
    function handleChange2(e){
        if(e.target.value>100000){return}
        self.setState({
          
          value2:e.target.value
        })
    }
    if(!this.props.open) return null;
    return (
      
      <div className='mainsavingCalcOverlay'  onClick={this.props.onClose}>
        <div className='mainsavingCalcModalContainer' onClick={(e)=>e.stopPropagation()}>
          <div className='mainsavingCalcfirstRow'>
              <div className='mainsavingCalcfirstbox' >
                <p className='mainsavingCalcheading' >Savings Calculator</p>
                <p className='mainsavingCalcheadingDiscription'  >Calculate your savings with chaayos select</p>
              </div>
            

              <div className='mainsavingCalcRightColFirstBox mainsavingCalcSelectOptionPlans' >
                <select name="plans" id="plans">
                  {/* <option value="one">1 month</option> */}
                  <option value="three">3 month</option>
                </select>
                <p style={{fontSize: "2.8vmin",color:"#0E8B45",fontWeight:"bold"}}>PLAN</p>
              </div>
              <img className='mainsavingCalcCrossBtn' onClick={this.props.onClose} src="./../../../img/ChaayosSelectMembership_images/Cross.svg"/>

          </div>
        
   
    
          <div className='mainSavingCalcLeftSecondRow'>
            <div className='mainSavingCalcLeftSecondRowFirstCol'>
              <div className='mainSavingCalcSecondRowFirstColFirstBox'>
                <div  className='mainSavingCalcSecondRowFirstColFirstSecondBox'>
                  <p className='mainsavingCalcSecondbox' >Minimum visits per month</p> 
                  <input className='mainsavingCalcSlider' type="range" min="1" max="40"  value={this.state.value1} onChange={handleChange1}   list="markers" step="1" />
                  <p>Value: {this.state.value1}</p>
                  <br />
                  <label>Enter any Value: &nbsp;  &nbsp; &nbsp;</label><input className='mainSavingCalcInput' type="number" placeholder='Enter any Value' value={this.state.value1} onChange={handleChange1} min="1" max="90" />
                </div>

                <div> 
                  <p className='mainsavingCalcSecondbox' >Minimum Order Value</p>
                  <input  className="mainsavingCalcSlider"  type="range" min="100" max="2900"   value={this.state.value2} onChange={handleChange2} step="200" list='price'/>
                  <p style={{padding:"0vh 0vw 1vh",}}>Value: {this.state.value2}</p>
                  <br />
                  <label>Enter any Value:  &nbsp; &nbsp; &nbsp;</label><input className='mainSavingCalcInput' type="number" placeholder='Enter any Value' value={this.state.value2} onChange={handleChange2} min="100" max="100000"/>   
                </div>
              </div>
            
              <div className='mainsavingCalcRightCol' >
                <div className='mainsavingCalcRightSavingBox' >
                  <p>Your Total Savings with Chaayos select will be</p>
                  <p style={{alignSelf: "center",fontSize:"4vmin",color:"#0E8B45",fontWeight: "bolder",color:"rgb(14, 139, 69)",margin:"2vh 1vw"}}>&#8377;{(0.15*(this.state.value1*this.state.value2)*3).toFixed(2)}</p>
                  <div style={{display: "flex"}}>
                    <p style={{fontSize:"2vmin",textAlign:"center"}}>Your Total spendings without select
                      <p style={{color:"#0E8B45", WebkitTextDecoration: "line-through red",textDecoration:"line-through red"}}>&#8377;{this.state.value1*this.state.value2*3}</p>
                    </p>
                    
                  </div>
                </div>

                <div className='mainsavingCalcRightCol' >
                  <p className='mainsavingCalcRightColThirdRow' style={{fontSize:"4vmin",margin:"3vh 0vw 3vh 1vw",width: "70%",fontWeight: "bolder"}}>Are you ready to save more?</p>
                </div>

                <div className='mainsavingCalcSubscribeBtnDiv'>
                  <button className='mainsavingCalcSubscribeBtn' onClick={this.props.onSubs} >
                  Subscribe now</button>
                </div>
              </div>
            </div>         
         


        

          </div>
       </div>
      </div>
    )
  }
}
