import React from "react";

export const styles={
	container:{
		position:"relative",
		backgroundColor:"#F7F7F8",
		height:"46vh",
		zIndex:"1"


	},
	row:{
		display:"flex",
		flexDirection:"row"
	},
	col:{
		display:"flex",
		flexDirection:"column"
	},
	heading:{
		fontWeight:"Bolder",
		fontSize: "7vh",
    marginTop: "5vh",
		color:"#0E8B45"
	},
	subheading:{
		fontWeight:"Bolder",
		fontSize: "4vh",
    marginTop: "5vh",
    marginBottom: "2vh",
		color:"#0E8B45"
	},
	subsubheading:{
		fontSize:"3vh",
		margin:"1vh 1vh 1vh 0",

	},
	first:{
		alignItems:"center",
		fontSize:"1vw",
		width:"40%"

	},
	second:{
		// position:"absolute",
		
		width:"20%"
	},
	third:{
		// position:"absolute",

		width:"20%"
	},
	fourth:{
		// position:"absolute",
		
		width:"20%"
	},
	img:{
		// position:"absolute",
		height: "100%",
    width: "30%",
    border: "1px",
    borderRadius: "50%",
		margin:"0 0.5vw"
	},
	copyright:{
		backgroundColor: "#F7F7F8",
    textAlign: "center",
    padding: "2vh",
		fontSize:"2vh"
	},
	leaves:{
		position: "absolute",
    height: "60vmin",
    top: "-37vh",
    left: "87vw",
    transform: `rotate(205deg)`
},



}