import React from "react";
import Faq from "./Faq.js";
import Review from "./Review.js";
import Footer from "./Footer/Footer.js";
import SavingsCalculator from "./SavingsCalculator.js";
import appUtil from "../../../AppUtil";
import {connect} from "react-redux";
import * as CartManagementActions from "../../../actions/CartManagementActions"
import * as MembershipActions from "../../../actions/MembershipActions"
import trackUtils from "../../../utils/TrackUtils"
import AboutUs from "./AboutUs.js";

@connect((store) => {
  return {
      selectedCity: store.localityReducer.selectedCity,
      unit: store.outletMenuReducer.unit,
      cart: store.cartManagementReducer.cart,
      product: store.membershipReducer.product
  };
})

export default class ChaayosSelectLayout extends React.Component {
  constructor(props){
    super(props);

    this.state={
      openModal:false,
      tooltip:false,
      heuristicValue1:116222,
      heuristicValue2:336000,
      heuristicValue3:21980000,

    }
    this.onPressSubscribe = this.onPressSubscribe.bind(this);
    this.placeMembershipOrder = this.placeMembershipOrder.bind(this);
    this.init = this.init.bind(this);


    this.handleScroll = this.handleScroll.bind(this);

  }
  
  init() {

    let code = "";
    let obj = [];
    let params = this.props.props.params.type;
    if (appUtil.checkEmpty(params)) {
        code = "chaayos_select";
    } else if (params === 'select') {
        code = "chaayos_select";
    } else if (params === 'workpass') {
        code = "chaayos_workpass";
    } else {
        code = params;
    }
    this.props.dispatch(MembershipActions.getMembershipData("26254", {code}, product => {
      obj = product.skuCodeMarketingImageMap.MARKETING_IMAGES_WEB_VIEW;
     
  }));
    this.props.dispatch(MembershipActions.setMembershipType(code));
  
    }

    placeMembershipOrder() {
      this.props.dispatch(CartManagementActions.placeOrder(this.props.cart, this.props.unit, this.props.selectedCity));
  }

  componentDidMount(){
    
    const mainSavingCalcBtn = document.getElementsByClassName('mainSavingCalcBtn');
    const mobileView = window.matchMedia('(max-width: 803px)').matches;
    if(!mobileView){
      mainSavingCalcBtn[0].classList.add("DesktopViewSavingCalcBtn")
    }

     setTimeout(()=>{
       
     setTimeout(()=>{
        this.setState({
            tooltip:false
        })},6000); 
        this.setState({
            tooltip:true
        })
      
      },5000); 

      
 
    

    trackUtils.trackPageView({page: "membership", device: "desktop", custom: true});


    var date1 = new Date("20/02/2023");
    var date2 = (new Date()).getTime();
      
    var Difference_In_Time = date2-date1.getTime();
      
    var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
    
    console.log("Total number of days between dates ->"+Difference_In_Days
               );
    var tempVal1=this.state.heuristicValue1,tempVal2=this.state.heuristicValue2,tempVal3=this.state.heuristicValue3;
    for (let i = 0; i < Difference_In_Days; i++) {
          tempVal1=tempVal1+(0.01*tempVal1);
          tempVal2=tempVal2+(0.01*tempVal2);
          tempVal3=tempVal3+(0.005*tempVal3);
    }
    tempVal1=(tempVal1/100000).toFixed(1);
    tempVal2=(tempVal2/100000).toFixed(1);
    tempVal3=(tempVal3/10000000).toFixed(1);
    this.setState({heuristicValue1:tempVal1,heuristicValue2:tempVal2,heuristicValue3:tempVal3});


    window.addEventListener("scroll", this.handleScroll,true)




  }
  componentWillMount() {
    this.init();
  }


  onPressSubscribe = () => {
    this.props.dispatch(MembershipActions.setCartMembershipItem(this.props.product.skuCodeProduct, () => {
        this.props.dispatch(this.placeMembershipOrder());
    }));
  
  }

  

  handleScroll=()=>{
  
    const mainSavingCalcBtn = document.getElementsByClassName('mainSavingCalcBtn');
   
    if(document.body.scrollTop*3>document.body.clientHeight){
      mainSavingCalcBtn[0].classList.add("scrollToTopSavingCalcBtn")
      setTimeout(()=>{
        this.setState({
            tooltip:true
        })

      setTimeout(()=>{
        this.setState({
            tooltip:false
        })},6000);
      },7000); 
      
    }
    else
    {
      mainSavingCalcBtn[0].classList.remove("scrollToTopSavingCalcBtn")
 
        this.setState({
            tooltip:false
        })
    }
    
  }  


     
  render()
  {

    
    return(
      <div style={{overflow: "hidden",position:"relative"}}>
        <div onScroll={this.handleScroll} className="maincontainer" > 
          <a href="https://chaayos.com/"><img className="mainChaayosSelectLogo" src="./../../../img/ChaayosSelectMembership_images/chaayos_logo.png"/></a>     
          <img className="mainfirstimg"  src="./../../../img/ChaayosSelectMembership_images/first.webp"/>
          <div className="mainchaayosSelectText1" >Chaayos Select</div>
          <div className="mainchaayosSelectText2" >For 3 Months</div>
          <div className="mainchaayosSelectText3" >In just &#8377; 199/- only </div>
          <div className="mainchaayosSelectText4" >(+ taxes)</div>
          <div class="mainbenefits">
            <ul>
              <li style={{color:"#CA561A"}}  class="item-1">Get 15% off </li>
              <li style={{color:"#FECE24"}} class="item-2">10% extra on wallet </li>
              <li style={{color:"#FD0000"}} class="item-3">Auto applied</li>
              <li style={{color:"#AAD36C"}} class="item-4">Unlimited internet access</li>
              <li style={{color:"#CA561A"}} class="item-5">Get 15% off</li>
            </ul>  
          </div>
          {/* <img src="sample.jpg"/> */}
         
        <button className="mainSubscribebtn" onClick={this.onPressSubscribe.bind(this)}>
          Subscribe now
        </button>
        
        </div>
        <div className="mainsecondContainer">
         <img  className="mainellipse" src="./../../../img/ChaayosSelectMembership_images/secondpagebackground.png"/>
           <div className="maincircles">
            <div  className="mainCircleWithText" >
              <div className="mainCircle1" ><p style={{alignSelf:"center",color: "white",fontSize:"7vmin" }}>{this.state.heuristicValue1}L</p></div>
              <div className="mainCircle1Text" style={{    margin: "5vh 1vw",color:"white",fontSize:"2.5vmin"}}>Number of customer bought membership till now</div>
            </div>
            <div  className="mainCircleWithText" >
              <div  className="mainCircle1" ><p style={{alignSelf:"center",color: "white",fontSize:"7vmin"}}>{this.state.heuristicValue2}L</p></div>
              <div className="mainCircle1Text"  style={{    margin: "5vh 1vw",color:"#0E8B45",fontSize:"2.5vmin"}}>Number of Order Placed with Membership</div>
            </div>
            <div  className="mainCircleWithText">
              <div className="mainCircle1" ><p style={{alignSelf:"center",color: "white",fontSize:"7vmin"}}>{this.state.heuristicValue3}Cr</p></div>
              <div className="mainCircle1Text"  style={{    margin: "5vh 1vw",color:"#0E8B45",fontSize:"2.5vmin"}}>Total Saving till now</div>
            </div>
          </div>

          <div className="mainwhyChaayos" style={{position: "absolute",top: "90vh"}}> 
            <div className="mainWCCS">Why choose Chaayos Select</div>
            <div className="maingifs" >  
              <div className="maingifWithText" >
                <img className="maingif" src="./../../../img/ChaayosSelectMembership_images/sale-outline.gif" />
                
            
                <div className="maingifText">
                  <div className="maingifheadings" style={{fontWeight: "bold"}}>15% off on all products</div>
                  <div className="maingifSubheadings" style={{fontSize:"2vmin"}}>Flat 15% Off on all your favorite Chaayos products</div>
                </div>
              </div>
              <div className="maingifWithText" >
                <img className="maingif"  src="./../../../img/ChaayosSelectMembership_images/pig-outline.gif" />

                <div className="maingifText">
                  <div className="maingifheadings" style={{fontWeight: "bold"}}>10% extra value on wallets</div>
                  <div className="maingifSubheadings" style={{fontSize:"2vmin"}}>Get Flat 10% extra value by purchasing Chaayos Wallet</div>
                </div>
              </div>
              <div className="maingifWithText">
                <img className="maingif"  src="./../../../img/ChaayosSelectMembership_images/confetti-outline.gif" />
                <div className="maingifText">
                  <div className="maingifheadings" style={{fontWeight: "bold"}}>Unlimited access</div>
                  <div className="maingifSubheadings" style={{fontSize:"2vmin"}}>Unlimited number of transactions, all at 15% Off</div>      
                </div>
              </div>
            </div>
            <button className="mainSubscribebtn mainSecondarySubscribeBtn mainSecondaryFirstSubscribeBtn"
                  onClick={this.onPressSubscribe.bind(this)} >
                  Subscribe now
            </button>   
          </div>
        </div>
        <AboutUs />
        <div className="fourthpage" >
        <div className="mainfourthimgText mobileView ">Why Choose Us</div>
        <img className="mainfourthimg"  src="./../../../img/ChaayosSelectMembership_images/Why_Choose_Us.webp"/>
        <img className="mainfourthimg mobileView"  src="./../../../img/ChaayosSelectMembership_images/Why_Choose_Us_mobileView.webp"/>
        </div>
        <Review />
        <Faq />
        <button className="mainSubscribebtn mainSecondarySubscribeBtn mainSecondarySecondSubscribeBtn" 
          onClick={this.onPressSubscribe.bind(this)}>
          Subscribe now
        </button> 
        <Footer />
        <div className="mainSavingCalcBtn" onClick={()=>{this.setState({openModal:true})}}  >
          <img className="mainOpenModal" src="./../../../img/ChaayosSelectMembership_images/calcgif.gif" /> 
           {
          this.state.tooltip?
          <div className="maintooltip">
            <p>Hi, Do you want to calculate your savings?</p>
          </div>:null
          }
        </div>  
        <SavingsCalculator onSubs={this.onPressSubscribe.bind(this)} open={this.state.openModal} onClose={()=>this.setState({openModal:false})} />
      </div>
    
    );
  }
}