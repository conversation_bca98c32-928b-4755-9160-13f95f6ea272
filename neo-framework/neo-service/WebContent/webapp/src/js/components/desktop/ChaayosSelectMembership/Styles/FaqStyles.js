export const styles={ 

  faqHeading:{

    borderBottom: "#777",
    padding:" 20px 60px",
    fontSize:" 10vh",
    color: "#0E8B45B2",
    fontWeight: "bolder",
    textAlign:"center"
  },

 faqContainer:{
    position:"relative",
    display: "flex",
    justifyContent: "center",
    flexDirection: "column",
  },

  faqPage: {
  
  color: "#444",
  cursor: "pointer",
  padding: "4.5vh 1.5vw 2vh",
  width: "100%",
  border: "none",
  outline: "none",
  transition:" 0.4s",
  fontSize:"3vh", 
  


},
  faqBody: {
  margin: "auto",
  textAlign: "center",
  width: "50%",
  padding: "auto",
  
 
},
arrow:{
  position: "sticky",
  height: "2vh",
  margin:"2vw",
  alignSelf:"center"

},

ques:
{
    backgroundColor: "#F3F3F3",
    margin: "0vh 8vw 7vh",
    boxShadow: "-20px -20px 50px #ffffff, 20px 20px 50px #d2d2d2",
    borderRadius: "14px",
   
    
  },
  faqOne:{
    position: "relative",
    display: "flex",
    flexDirection: "row"
  },
  leaves:{
    position:"absolute",
    height:"58vmin",
    top: "52vh"
  }
}








