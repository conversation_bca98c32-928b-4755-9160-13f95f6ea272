export const styles = {
    container: {
        flex: 1,
        height: '100vh',
        width: '100vw',
        minHeight: '100vh',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#ffffff',
        display: 'flex',
    },
    headerImageContainer: {
        width: "100%",
        marginBottom: 5,
        maxHeight: '100vh',
        overflowY: "hidden"
    },
    formContent: {
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: '5%',
        paddingRight: '5%',
    },
    formTitle: {
        textAlign: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#5e7e47',
        fontSize: 28,
        marginTop: 5,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: '5%'
    },
    highlightSubTitle: {
        textAlign: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#ffffff',
        fontSize: 21,
        marginTop: 5,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: '5%',
        backgroundColor: '#5e7e47',
        fontFamily: "'Caveat', cursive",
        letterSpacing: 2,
        lineHeight: 1,
        paddingBottom: 10,
        paddingTop: 10
    },
    emailFormFieldBox: {
        backgroundColor: '#eeeeee',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10
    },
    emailFocusedFormFieldBox: {
        backgroundColor: '#ffffff',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E'
    },
    formFieldBox: {
        backgroundColor: '#eeeeee',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: '5%',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10
    },
    focusedFormFieldBox: {
        backgroundColor: '#ffffff',
        marginTop: 15,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: '5%',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E'
    },
    formFieldInput: {
        width: '100%',
        padding: 20,
        marginLeft: 10,
        backgroundColor: 'transparent',
        border: 0,
        fontSize: 15,
        color: '#787775'
    },
    actionButton: {
        textAlign: 'center',
        backgroundColor: '#5e7e47',
        color: '#ffffff',
        borderWidth: '2px',
        borderColor: '#5e7e47',
        borderRadius: '10px',
        marginTop: 35,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        fontSize: 20
    },
    visitChaayosButton: {
        textAlign: 'center',
        backgroundColor: '#5e7e47',
        color: '#ffffff',
        borderWidth: '2px',
        borderColor: '#5e7e47',
        borderRadius: '10px',
        marginTop: 35,
        marginLeft: 35,
        marginRight: 35,
        marginBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        fontSize: 20,
        width: '60%'
    },
    backHeader: {flexDirection: 'row', height: '5%', width: '100%'},
    backBox: {width: '20%', alignItem: 'center', justifyContent: 'center', padding: 20, cursor: 'pointer'},
    rightOTPContainer: {
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        paddingLeft: '3.5%',
        paddingRight: '8%'
    },
    oTPSection: {
        flex: 1,
        height: '40%',
        alignItem: 'center',
        justifyContent: 'center',
        marginTop: '15%',
    },
    oTPSectionInfoContainer: {
        flex: 1,
        flexDirection: 'column',
        height: '40%',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPSectionTitle: {
        flex: 1,
        height: '50%',
        textAlign: 'center',
        alignItem: 'center',
        justifyContent: 'center',
        color: '#5e7e47',
        fontSize: 28,
        marginBottom: 5
    },
    oTPInfoContent: {
        flex: 1,
        height: '50%',
        textAlign: 'center',
        fontSize: 18,
        color: '#70777D'
    },
    oTPInputSection: {
        flex: 1,
        flexDirection: 'column',
        marginTop: '50px',
        height: '60%',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPInputMainContainer: {
        flex: 1,
        height: '50%',
        alignItem: 'center',
        justifyContent: 'center'
    },
    oTPInputContainer: {flex: 1, alignItem: 'center', justifyContent: 'center'},
    oTPInputBox: {
        width: '5rem',
        height: '5rem',
        margin: '0.5rem',
        color: '#5e7e47',
        fontSize: 25,
        borderRadius: 6,
        border: 0,
        backgroundColor: '#eeeeee'
    },
    oTPErrorStyle: {borderWidth: 2, borderColor: 'red'},
    resendOTPSection: {flex: 1, height: '50%', textAlign: 'center', fontSize: 15, color: '#70777D'},
    verifyButton: {
        width: '40%',
        margin: '10% auto 15%',
    },
    productLeftPart: {
        width: '50%',
        padding: '0 5%',
        display: 'flex',
        flexDirection: 'column',
    },
    pSectionInnerContainer: {
        marginLeft: '15%',
    },
    productRightPart: {
        width: '50%',
        paddingRight: '15%',
        paddingLeft: '5%',
        display: 'flex',
        flexDirection: 'column',
    },
    productSection: {
        flex: 1,
        width: '100%',
        padding: '5%',
    },
    pSectionHeaderContainer: {
        flex: 1,
        height: '5%',
        marginTop: '30px',
        alignItem: 'center',
        justifyContent: 'center'
    },
    pSectionHeader: {
        flex: 1,
        height: '50%',
        textAlign: 'flex-start',
        alignItem: 'flex-start',
        justifyContent: 'flex-start',
        fontWeight: 'bold',
        color: '#5e7e47',
        fontSize: 20,
        marginBottom: 5,
        paddingLeft: '20px'
    },
    productImageContainer: {
        flex: 1,
        width: '40%',
        alignItems: 'flex-start',
        justifyContent: 'center',
        paddingTop: 10,
        paddingBottom: 10
    },
    productImage: {
        width: '100%',
        height: '120px',
        objectFit: 'cover',
        borderRadius: 4,
        boxShadow: '2px 2px 3px #9E9E9E'
    },
    productInfo: {flex: 1, width: '50%', alignItems: 'flex-end', justifyContent: 'flex-end',},
    productName: {
        textAlign: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'center',
        color: '#5e7e47',
        fontSize: 25,
        marginBottom: 10
    },
    productDesc: {
        textAlign: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'center',
        color: '#787775',
        fontSize: 13,
        marginBottom: 10,
        paddingBottom: 20
    },
    addButtonContainer: {flex: 1, width: '35%'},
    addButton: {
        width: '100%',
        padding: 8,
        borderRadius: '20px',
        top: 0,
        textAlign: 'center',
        color: '#ffffff',
        backgroundColor: '#5e7e47',
        fontSize: 15,
        boxShadow: '0 1px 2px 0 #c8c8c3', border: 'solid 2px #5e7e47'
    },
    confettiContainer: {
        backgroundImage: 'url("../../../../img/congrats_view_confetti_bg_desktop.svg")',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        display: 'flex',
    },
    congLeftPart: {
        flex: 40,
        width: '40%',
    },
    congRightPart: {
        flex: 60,
        width: '60%',
    },
    congratsInner: {
        width: '100%',
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-around',
        alignItems: 'center',
        minHeight: '90vh',
    },
    congratsTitle: {
        fontWeight: '700',
        color: '#3e601a',
        fontSize: '45px',
        fontFamily: "'Caveat', cursive",
        textAlign: 'center',
        lineHeight: 1.3,
        wordSpacing:'15px',
        letterSpacing: '2px'
    },
    youHaveWonText:{
            fontWeight: '700',
            color: '#3e601a',
            fontSize: '35px',
            fontFamily: "'Caveat', cursive",
            textAlign: 'center',
            lineHeight: 1.3,
            wordSpacing:'15px',
            letterSpacing: '2px'
    },
    secondFreeChaiTitle: {
        fontWeight: '700',
        color: '#3e601a',
        fontSize: '35px',
        fontFamily: "'Caveat', cursive",
        textAlign: 'center',
        lineHeight: 1.3,
        wordSpacing:'15px',
        letterSpacing: '2px',
        marginBottom: '30px',
        marginTop: '30px'
    },
    congratsSub: {
        fontSize: '45px',
        color: '#3e601a',
        fontFamily: "'Caveat', cursive",
        fontWeight: '700',
        textAlign: 'center',
        margin: '0 auto',
    },
    congratsText: {
        textAlign: 'center',
        color: '#606060',
        marginTop: '15%',
        marginBottom: '10%',
        fontSize: '17px',
        padding: "0 10%",
    },
    congratsButton: {
        fontFamily: 'inherit',
        textAlign: 'center',
        backgroundColor: '#5e7e47',
        color: '#ffffff',
        width: '85%',
        borderWidth: '2px',
        borderColor: '#5e7e47',
        borderRadius: '25px',
        padding: 10,
        fontSize: 28,
        outline: 'none',
        border: 'none'
    },
    addressFormField: {
        flex: 1,
        backgroundColor: '#eeeeee',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        marginTop: 5
    },
    addressFormFieldFocused: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E',
        marginTop: 5
    },
    addressText: {
        flex: 1,
        backgroundColor: '#eeeeee',
        borderWidth: 10,
        borderColor: '#000000',
        borderRadius: 10,
        marginTop: 5,
        height: 100
    },
    addressTextFocused: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 10,
        borderColor: '#000000',
        borderRadius: 10,
        boxShadow: '5px 3px 10px #9E9E9E',
        marginTop: 5,
        height: 100
    },
    inputNameText: {flex: 1, paddingLeft: 10, justifyContent: 'center', color: '#989898', fontSize: 15},
    selectItem: {
        fontSize: '20px',
        flex: 1,
        textAlign: 'center',
        alignItem: 'center',
        padding: 10,
        borderRadius: 10,
        marginTop: 5,
        height: '55px',
        backgroundColor: '#ffffff'
    },
    selectItemWrapper: {
        fontSize: '15px',
        color: '#989898',
        alignItem: 'center',
        borderRadius: 10,
        height: '55px'
    },
    dataPicker: {
        flex: 1,
        height: '55px',
        alignItem: 'center',
        padding: 10,
        borderColor: '#787775',
        borderRadius: 10,
        marginTop: 5
    },
    cityContainer: {
        flex: 1,
        backgroundColor: '#eeeeee',
        borderWidth: 10,
        borderColor: '#787775',
        borderRadius: 10,
        marginTop: 5,
        height: '55px'
    },
    verifyEmailInfo: {
        marginLeft: 35,
        marginTop: 10,
        color: '#5e7e47',
        fontSize: 10,
        marginBottom: 5
    },
    codecontainer: {
        marginTop: 25,
    },
    codeheading: {
        color: '#5e7e47',
        fontSize: 20,
        textAlign: 'center',
        fontFamily: 'Nunito',
    },
    codediv: {
        backgroundColor: 'grey',
        display: 'block',
        padding: '2px 10px',
        borderWidth: '3px',
        borderStyle: 'dashed',
        borderColor: 'black',
        borderRadius: 1000,
    },
    codemain: {
        fontSize: 25,
        color: '#ffffff',
        textAlign: 'center',
        fontFamily: 'Nunito',
    },
    codesubtext: {
        color: 'grey',
        marginTop: '20px',
        textAlign:'center',
        fontSize:'35px',
        fontWeight:'bold'
    },
    validitytext:{
        color: 'grey',
        marginTop: '20px',
        textAlign:'center'
    },
    copyText:{
        color: 'grey',
        fontSize: '11px',
        textAlign:'center'
    },
    downloadcontainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        width: '80%',
        marginLeft: '10%',

    },
    downloadstorecontainer: {
        marginLeft: 20,
        marginRight: 20
    },
    downloadimg: {
        width: '130px',
    },
    downloadtext: {
        marginTop: 20,
        textAlign: 'center',
        marginBottom: 3,
        fontFamily: 'Nunito',
        fontSize: 20,
        color: 'grey',
    },
    sharetxt: {
        color: '#3e601a',
        fontSize: 30,
        fontFamily: 'Nunito-SemiBold, Nunito',
        fontWeight: '600',
        textAlign: 'center',


    },
    sharecontainer: {
        display: 'flex',
        flexDirection: 'row',
        marginTop: 25,
    },
    shareicon: {
        marginTop: 4,
        marginRight: 4,
    },
    alreadyShareText: {
        color: '#3e601a',
        marginTop: '20px',
        textAlign:'center',
        fontWeight:'bold',
    },
    gifStyle: {
        position:'absolute',
        top: 0,
        width: '50%',
        left:'50%',
        height: window.innerHeight*.5
    },
    congLeftPartCenter: {
        flex: 40,
        width: '40%',
        textAlign:'center'
    },
    congratulationSubHeading: {
        fontWeight: '700',
        color: '#3e601a',
        fontSize: '35px',
        fontFamily: "'Caveat', cursive",
        textAlign: 'center',
        lineHeight: 1.3,
        wordSpacing:'15px',
        letterSpacing: '2px'
    }
};
