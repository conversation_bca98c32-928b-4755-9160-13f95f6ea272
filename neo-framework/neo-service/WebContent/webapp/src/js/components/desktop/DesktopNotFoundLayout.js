/**
 * Created by Chaayos on 06-12-2016.
 */
import React from "react";
import {browserHistory} from "react-router";
import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import trackUtils from "../../utils/TrackUtils";

export default class DesktopNotFoundLayout extends React.Component {
    constructor(){
        super();
        this.goHome = this.goHome.bind(this);
    }

    goHome() {
        browserHistory.push("/");
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"about",device:"desktop",custom:true});
    }

    render (){
        return(
            <div>
                <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                <div className="desktopPageContainer">
                    <div style={{width:"500px", margin:"0 auto", padding:"100px 0", fontSize:"16px"}}>
                        <div style={{fontSize: "150px", color:"#5e7e47"}}>404</div>
                        <p style={{textTransform:"uppercase", marginBottom:"30px", fontSize:"21px"}}>Page not found.</p>
                        <p style={{fontSize:"43px"}}>But...</p>
                        <p>You can find a Kadak cup of chai.</p>
                        <p>Just click the button below.</p>
                        <input type="button" style={{border:"none", borderRadius:"50px", padding:"17px", color:"#FFF", marginTop:"20px",
                            background:"darkolivegreen", fontSize:"18px", width:"200px", cursor:"pointer"}} value="Go Home" onClick={this.goHome.bind(this)} />
                    </div>
                </div>
                <DesktopFooterLayout />
            </div>
        )
    }
}