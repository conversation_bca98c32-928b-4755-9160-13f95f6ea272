/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 25-04-2020.
 */
import React from "react";
import { connect } from "react-redux"
import { browserHistory } from "react-router";
import trackUtils from "../../utils/TrackUtils";

import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import { setCustomerDetail } from "../../actions/CustomerActions";

@connect((store) => {
    return {
        customer: store.customerReducer.customer,
    };
})

export default class DesktopShareAppLayout extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            selectedRef: 'email',
            email: '',
            phoneNumber: '',
            sucessMsg: '',
            errMsg: ''
        };
        this.goToLogIn = this.goToLogIn.bind(this);
        this.gotToSignUp = this.gotToSignUp.bind(this);
        this.shareLink = this.shareLink.bind(this);
        this.updateEmail = this.updateEmail.bind(this);
        this.updatePhone = this.updatePhone.bind(this);
        this.updateSelectedRefEmail = this.updateSelectedRefEmail.bind(this);
        this.updateSelectedRefPhone = this.updateSelectedRefPhone.bind(this);
    }

    componentDidMount() {
        trackUtils.trackPageView({ page: "shareapp", device: "desktop", custom: true });
        this.props.dispatch(setCustomerDetail());
    }

    componentWillMount() {

    }

    isUserLogIn() {
        this.props.dispatch(setCustomerDetail());
        const { customer } = this.props
        if (customer && customer.customerId) {
            return true;
        }
        return false;
    }

    getUserName() {
        this.props.dispatch(setCustomerDetail());
        const { customer } = this.props
        if (customer && customer.name) {
            return customer.name;
        }
        return 'NA';
    }

    goToLogIn() {
        browserHistory.push("/login?ref=shareApp");
    }

    gotToSignUp() {
        browserHistory.push("/login?ref=shareApp");
    }

    navigateToAppStore() {
        const path = "https://apps.apple.com/in/app/zomato-food-restaurant-finder/id434613896?_branch_match_id=782898633919361442";
        window.open(path, _blank)
    }

    navigateToGooglePlay() {
        const path = "https://play.google.com/store/apps/details?id=com.application.zomato&hl=en_IN";
        window.open(path, _blank)
    }

    updateEmail(e) {
        console.log('fff', e)
        this.setState({ email: e.target.value });
    }

    updatePhone(e) {
        this.setState({ phoneNumber: e.target.value });
    }

    validateEmail(email) {
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        return emailPattern.test(email);
    }

    validatePhoneNumber(phoneNumber) {
        const phoneNumberPattern = /^\d{10}$/;
        return phoneNumberPattern.test(phoneNumber);
    }

    updateSelectedRefEmail() {
        this.setState({
            sucessMsg: '',
            errMsg: '',
            email: '',
            phoneNumber: '',
            selectedRef: 'email'
        });
    }

    updateSelectedRefPhone() {
        this.setState({
            sucessMsg: '',
            errMsg: '',
            email: '',
            phoneNumber: '',
            selectedRef: 'phone'
        });
    }

    shareLink() {
        const { selectedRef, email, phoneNumber } = this.state
        console.log('sssdd', selectedRef, email, phoneNumber)
        if (selectedRef === 'email' && email) {
            if (this.validateEmail(email)) {
                this.setState({
                    errMsg: '',
                    sucessMsg: 'App link has baan shared on your email'
                });
            } else {
                this.setState({
                    errMsg: 'Please Enter a valid Email Id',
                    sucessMsg: ''
                });
            }
        } else if (selectedRef === 'phone' && phoneNumber) {
            if (this.validatePhoneNumber(phoneNumber)) {
                this.setState({
                    errMsg: '',
                    sucessMsg: 'App link has baan shared on your mobile number'
                });
            } else {
                this.setState({
                    errMsg: 'Please Enter a valid Phone number',
                    sucessMsg: ''
                });
            }
        }
    }

    renderUser() {
        const isUserLogIn = this.isUserLogIn();
        const userName = this.getUserName();
        if (isUserLogIn) {
            return <div class="headLine" style={{ marginLeft: '50px', marginRight: '10px' }}>
                Welcome {userName}
            </div>
        }
        return <div>
            <button class="btn btn-default right" style={{ marginTop: '15px', marginRight: '110px' }}
                onClick={this.goToLogIn}>Log in
            </button>
            <button class="btn btn-default right" style={{ marginTop: '15px', marginRight: '20px' }}
                onClick={this.gotToSignUp}>Sign up
            </button>
        </div>
    }

    renderCheckBox() {
        const { selectedRef } = this.state
        return <div style={{ marginTop: '15px', marginLeft: '10px', marginRight: '10px' }}>
            <div className="newAddressInputContainerr">
                <div className="radioLabels">
                    <input type="radio" id="email" className="userEmail" checked={selectedRef === 'email'} name='userInformationField' onChange={this.updateSelectedRefEmail} />
                    <label htmlFor="email"><span><span></span></span>EMAIL</label>
                </div>
                <div className="radioLabels">
                    <input type="radio" id="phone" name='userInformationField' onChange={this.updateSelectedRefPhone} />
                    <label htmlFor="phone"><span><span></span></span>PHONE</label>
                </div>
            </div>
        </div>
    }

    renderShareAppLink() {
        const { selectedRef, phoneNumber, email, sucessMsg, errMsg } = this.state
        return <div>
            <div class="headLine">Get the Chaayos App</div>
            <div class="loginSectionTagline">We will send you a link, open it on your phone to download the app.</div>
            {
                selectedRef === 'email' ? <div class="contactContainer">
                    <input placeholder="Enter your email" maxLength="100" autoComplete="off" value={email} onChange={this.updateEmail}></input>
                </div>
                    : <div className="contactContainer">
                        <input placeholder="Enter mobile number" maxLength="100" autoComplete="off" value={phoneNumber} onChange={this.updatePhone}></input>
                    </div>
            }
            <p>{errMsg}</p>
            <p>{sucessMsg}</p>
            <button class="btn btn-primary" style={{ marginTop: '16px' }} onClick={this.shareLink}>Share App Link</button>
            <div className="loginSectionTagline">Download app from</div>
            <div>
                <a className="btn btn-default right" style={{ marginTop: '15px', marginRight: '110px' }}
                    href='https://apps.apple.com/in/app/zomato-food-restaurant-finder/id434613896?_branch_match_id=782898633919361442' target='_blank'>App Store
                </a>
                <a className="btn btn-default right" style={{ marginTop: '15px', marginRight: '20px' }}
                    href='https://play.google.com/store/apps/details?id=com.application.zomato&hl=en_IN' target='_blank'>Google Play
                </a>
            </div>
        </div>
    }

    render() {
        return (
            <div>
                <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                <div class="shareAppContainer">
                    {this.renderUser()}
                    {this.renderCheckBox()}
                    {this.renderShareAppLink()}
                </div>
                <DesktopFooterLayout />
            </div>
        )
    }
}