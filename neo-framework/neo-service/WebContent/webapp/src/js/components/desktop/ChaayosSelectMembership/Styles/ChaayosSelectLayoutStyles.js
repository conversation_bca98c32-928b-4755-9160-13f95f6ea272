export const styles={
container :{
  position: "relative",
  width: "100%",
  margin:"0vw 0vw -0.1945vw 0vw",
  
  
},


 chaayos_logo:{
  position: "absolute",
  height: "13vh",
  width: "20vw",
  left: "75vw",

 },
 chaayosSelect:{
  
  position: "absolute",
  top: "23vh",
  fontSize: "4vw",
  color: "#0E8B45",
  fontWeight: "bolder",
  left: "5vw"

 },

 for3months:{
  position: "absolute",
  top: "45vh",
  fontSize: "3vw",
  color: "#0E8B45",
  left: "5vw",
  fontWeight: "bold",
 },
 justonly:{
  position: "absolute",
  top: "53vh",
  fontSize: "3vmin",
  color: "black",
  fontWeight: "bolder",
  left: "5vw",

 },



circles:{

    position: "relative",
    display: "flex",
    flexWrap: "wrap",
    gridTemplateColumns: "auto auto auto",
    top: "28vh",
    justifyContent: "space-between",
    margin: "0vh 10vw",

  
    
 

    
},
circle1:{
  filter: `url(drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.15)))`,
  height: "30vmin",
  width: "30vmin",
  borderRadius: "50%",
  backgroundImage:`url(./../../../img/ChaayosSelectMembership_images/gradient_image.png)`,
  display: "flex",
  border: "1.5vmin solid #FFFFFF",
  boxShadow: "0px 2px 4px rgb(0 0 0 / 15%)",
  backgroundRepeat:"no-repeat",
  backgroundPosition: "center",
  justifyContent: "center"
},
hrline:{
  borderTop: "1px dashed red"
},
ellipse:{
  height: "200vh",
  width: "100vw",
  backgroundColor: "rgba(14, 139, 69, 0.7)",
  position:"absolute",
  borderRadius: "50%",
  left: "-60%",
  top: "-10%",
  zIndex: "0",
  boxShadow: "inset 0px 41px 16px rgba(0, 0, 0, 0.01), inset 0px 23px 14px rgba(0, 0, 0, 0.05), inset 0px 10px 10px rgba(0, 0, 0, 0.09), inset 0px 3px 6px rgba(0, 0, 0, 0.1)"

  
},

gif:{
  
  boxShadow: "0px 2px 4px rgb(0 0 0 / 15%)",
  borderRadius: "15px",
  padding: "1vw",
  height:"35vh",
  width:"16vw",
  background: "white"

},
gifheadings:{
  width: "16vw",
  textAlign: "center",
  margin: "5vh 0vw 2vh 11vw",
  fontSize: "3.5vmin",
  
},
WCCS:{
    color: "#1C1919",
    fontSize: "7vh",
    textAlign: "center",
    fontWeight: "bolder",
    padding: "3vmax"

},
img : {

  width: "81vw",
  height: "105vh",
  marginLeft: "20vw",


},

 btn :{
  position: "absolute",
  width: "27%",
  height:"9vh",
  top: "65%",
  left: "5%",
  zIndex:3,
  textOverflow: "clip",
  backgroundColor: "#0E8B45",
  color: "white",
  fontSize: "2vw",

  border: "none",
  cursor: "pointer",
  borderRadius: "108px",

  
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  boxShadow:"10px 14px 30px rgba(0, 0, 0, 0.2)"

   
},
secondarySubscribeBtn:{

   width: "18vw",
   top: "123%",
   left: "72%",
   fontSize:"1.5vw",
   left: "50%",
   transform: `translate(-50%, -50%)`

},
dots:{
  height: "15vmin",
  width: "17vmin",
  position: "absolute",
  top: "90vh",
  left: "5vw"
},
secondContainer :{
  position: "relative",
    height: "200vh",
    width: "100vw",

 
},

secondimg:{
  
  position: "absolute",

    top: "-36vh",
    height: "208vh",
    width: "90vw",

   
   
},


thirdimg:{ 
  width:"100%",
  margin:"10vh 0vh"
},
btnText :{
  position: "absolute",
  top: "63%",
  left: "14.5%",
  fontSize: "2vw",
  color:"white"
},
  input:/*input:focus*/
  {
  outline: "none",
  borderStyle: "none"
  },
  userInfo:{
  position:"absolute",  
  top:"41%",
  width:"fit-content",
  fontSize: "6vw",
  border: "none",
  backgroundColor: "inherit",
  color: "#FFFFFF",
  zIndex:"2",
  padding:"0 10vw"


  },
  userInfo1:{
  
  left: "6.5%",

  

},
 userInfo2:{
 
  left: "36%",

},
 userInfo3:{
    
 
  left: "63%",
 
},
tooltip:{
  backgroundColor:"white",
  borderRadius:"20px",
  zIndex:"10",
  height: "10vh",
  display: "flex",
  color: "black",
  alignItems: "center",
  justifyContent: "center",
  width: "18vw",
  position:"fixed",
  top:"48rem",
  left:"90rem",
  boxShadow:"1px 2px 2px black, 24px 20px 50px #d2d2d2"
  

},

}