import React from "react";
import {connect} from "react-redux"
import * as LocalityActions from "../../actions/LocalityActions";
import * as UtilityActions from "../../actions/UtilityActions";
import DesktopOutletMenuLayout from "./DesktopOutletMenuLayout";
import appUtil from "../../AppUtil";
import {setShowLocality} from "../../actions/LocalityActions";

@connect((store) => {
    return {
        showFullPageLoader: store.utilityReducer.showFullPageLoader,
        loaderMessage: store.utilityReducer.loaderMessage,
    };
})
export default class DesktopDeliveryRoutingLayout extends React.Component {

    constructor(){
        super();
        this.init = this.init.bind(this);
    }

    init(){
        let state;
        let city;
        const params = this.props.props.params;
        if (!appUtil.checkEmpty(params.city)) {
            if(params.city === 'new_delhi'){
                state = 32;
                city = "New Delhi";
            }
            if(params.city === 'gurgaon'){
                state = 8;
                city = "Gurgaon";
            }
            if(params.city === 'chandigarh'){
                state = 31;
                city = "Chandigarh";
            }
            if(params.city === 'ghaziabad'){
                state = 28;
                city = "Ghaziabad";
            }
            if(params.city === 'noida'){
                state = 28;
                city = "Noida";
            }
            if(params.city === 'mumbai'){
                state = 15;
                city = "Mumbai";
            }
            if(params.city === 'karnal'){
                state = 8;
                city = "Karnal";
            }
            if(params.city === 'bangalore'){
                state = 12;
                city = "Bangalore";
            }
            if(params.city === 'faridabad'){
                state = 8;
                city = "Faridabad";
            }
        }
        this.props.dispatch(LocalityActions.setCriteria("DELIVERY"));
        console.log("Delivery for city:: " + city);
        this.props.dispatch(LocalityActions.selectCity(city, state));
        this.props.dispatch(LocalityActions.setShowLocality(true));
        this.props.dispatch(LocalityActions.loadLocalitiesList(city));
    }

    componentWillMount(){
        this.init();
    }

    componentDidMount(){
        this.props.dispatch(UtilityActions.hideFullPageLoader());
    }

    render() {

        return (
            <div>
                {this.props.showFullPageLoader ? (
                    <div class="fullPageLoader">
                        <div class="loaderWrapper">
                            <div class="load8 loader"></div>
                            {this.props.loaderMessage != null ? (
                                <p class="loaderMessage">{this.props.loaderMessage}</p>
                            ) : (null)}
                        </div>
                    </div>
                ) : (
                    <DesktopOutletMenuLayout />
                )}
            </div>
        )
    }
}