import React, { Component } from 'react'
import appUtil from "../../../AppUtil";

export default class AboutUs extends Component {
  render() {
    return (
      <div style={{margin:"10vh 0vw"}}>
      <div className='mainAboutUsHeading'>About us</div>

      <div className='mainAboutUsDiscription'>  
        <img className="mainAboutUsImg"  src="./../../../img/ChaayosSelectMembership_images/About_us.webp"/>
        <div className='mainAboutUsText'>
          <p>
              Chaayos is a chai-café chain offering different varieties of Chai customizable in over 80,000 ways.
            Each Chai is handcrafted and made upon order.The variety of teas which Chaayos offers ranges from Indian 
            Chais like Ku<PERSON>had <PERSON> and Kashmiri <PERSON> to Specialty Chais like Honey Ginger Lemon Chai and Golden Green Chai.
            Chaayos also offers a broad range of Nashta- A perfect accompaniment to your Chai! Customers swear by the 
            innovative snacks like Chicken Cheese Max and Paneer Thepla Tacos, and finger-licking street food like <PERSON><PERSON> and <PERSON><PERSON><PERSON>.
          </p>
          <br />
          <p>
            Chaayos Select is a membership program which offers Flat 15% on a wide range of Chai & Snacks for 3 months
            at just Rs.199/-*. When combined with Chaayos Wallet, the consumers get additional 10% value.
          </p>
        </div>
      {!appUtil.isMobile()?
        <img className='mainAboutUsLeaves'  src="./../../../img/ChaayosSelectMembership_images/leaves.webp"/>

      :
        <img className='mainAboutUsLeaves'  src="./../../../img/ChaayosSelectMembership_images/leavesMobile.png"/>
       
      }  
        
   
      </div>
      </div>
    )
  }
}
