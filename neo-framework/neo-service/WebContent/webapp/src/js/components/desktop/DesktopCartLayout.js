import React from "react";
import {Link, browserHistory} from "react-router";
import {connect} from "react-redux";
import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import * as CartManagementActions from "../../actions/CartManagementActions";
import * as UtilityActions from "../../actions/UtilityActions";
import DesktopCartItemLayout from "./DesktopCartItemLayout";
import MobileCustomizationModal from "../mobile/MobileCustomizationModal";
import appUtil from "../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import StorageUtils from "../../utils/StorageUtils";

@connect((store) => {
    return {
        criteria: store.localityReducer.criteria,
        selectedCity:store.localityReducer.selectedCity,
        unit: store.outletMenuReducer.unit,
        skipDeliveryPackaging:store.outletMenuReducer.skipDeliveryPackaging,
        cart: store.cartManagementReducer.cart,
        cartItems: store.cartManagementReducer.cartItems,
        syncCart: store.cartManagementReducer.syncCart,
        offerErrorCode: store.cartManagementReducer.offerErrorCode,
        offerErrorMessage: store.cartManagementReducer.offerErrorMessage,
        offerApplied: store.cartManagementReducer.offerApplied,
        offerErrorMap: store.cartManagementReducer.offerErrorMap,
        offerRemovedInitCart: store.cartManagementReducer.offerRemovedInitCart,
        unitInventory: store.outletMenuReducer.unitInventory,
        campaignDetail:store.campaignReducer.campaignDetail,
        customerAccepted:store.promoOfferReducer.customerAccepted,
        promoOfferCode:store.promoOfferReducer.promoOfferCode,
        offerDetails:store.outletMenuReducer.offerDetails
    };
})
export default class DesktopCartLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            taxDetail: ""
        };
        this.init = this.init.bind(this);
        this.goToMenu = this.goToMenu.bind(this);
        this.setOrderRemark = this.setOrderRemark.bind(this);
        this.applyCoupon = this.applyCoupon.bind(this);
        this.removeCoupon = this.removeCoupon.bind(this);
        this.placeOrder = this.placeOrder.bind(this);
        this.showTaxDetail = this.showTaxDetail.bind(this);
        this.syncAndLogin = this.syncAndLogin.bind(this);
        this.setCouponAndApply = this.setCouponAndApply.bind(this);
        this.setOrderAndTakeAwayRemark = this.setOrderAndTakeAwayRemark.bind(this);
    }

    init() {
        this.props.dispatch(CartManagementActions.setSyncCart(true));
        this.props.dispatch(CartManagementActions.initCart(this.props.unit, this.props.skipDeliveryPackaging,
        appUtil.isInterstate(this.props.unit,this.props.selectedCity)));
        this.props.dispatch(CartManagementActions.setCartStock(this.props.unitInventory));
    }

    goToMenu() {
        trackUtils.trackGoToMenuClicked("cart");
        browserHistory.push("/menu");
    }

    setOrderRemark() {
        if(this.props.criteria === 'TAKE_AWAY'){
            this.setOrderAndTakeAwayRemark();
        }else{
            let remark = document.getElementById("orderRemarkInput").value;
            this.props.dispatch(CartManagementActions.setOrderRemark(this.props.cart, remark));
        }
    }

    setOrderAndTakeAwayRemark() {
        let orderRemark = document.getElementById("orderRemarkInput").value;
        let takeAwayRemark = document.getElementById("takeAwayDetailInput").value;
        if(appUtil.checkEmpty(orderRemark)){
            orderRemark = 'NA'
        }
        if(appUtil.checkEmpty(takeAwayRemark)){
            takeAwayRemark = 'NA'
        }
        let remark = 'Order Remarks : ' + orderRemark + ' and Take Away Details : ' + takeAwayRemark;
        this.props.dispatch(CartManagementActions.setOrderRemark(this.props.cart, remark));
    }

    showTaxDetail() {
        if (this.state.taxDetail == "open") {
            this.setState({
                taxDetail: ""
            })
        } else {
            try{trackUtils.trackTaxDetailViewed(this.props.cart.cartId);}catch(e){}
            this.setState({
                taxDetail: "open"
            })
        }
    }

    applyCoupon() {
        var couponCode = document.getElementById("couponInput").value;
        if (!appUtil.checkEmpty(couponCode)) {
            this.props.dispatch(CartManagementActions.applyCoupon(couponCode.toUpperCase()
                , appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
        } else {
            this.props.dispatch(UtilityActions.showPopup("Please enter a coupon code."));
        }
    }

    setCouponAndApply(code){
        if(code!=null){
            document.getElementById("couponInput").value = code.toUpperCase();
        }
        this.applyCoupon();
    }

    removeCoupon() {
        document.getElementById("couponInput").value = "";
        try{trackUtils.trackCouponRemoved(this.props.cart.orderDetail.offerCode);}catch(e){}
        this.props.dispatch(CartManagementActions.removeCoupon(appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
    }

    placeOrder() {
        this.props.dispatch(CartManagementActions.placeOrder(this.props.cart,this.props.unit,this.props.selectedCity));
        //this.props.dispatch(CartManagementActions.syncCart());
    }

    syncAndLogin(){
        this.props.dispatch(CartManagementActions.syncCartAndLogin());
    }

    calculateGiftCardAmount(){
        let totalGCAmount = 0;
        if(!appUtil.checkEmpty(this.props.cart)
            && !appUtil.checkEmpty(this.props.cart.orderDetail)
            && !appUtil.checkEmpty(this.props.cart.orderDetail.orders)){
            let offerAmount = 0;
            this.props.cart.orderDetail.orders.map((orderItem, index) => {
                //console.log(orderItem);
                if(orderItem.cardType === "ECARD"){
                    if(this.props.offerDetails!=null
                        && this.props.offerDetails instanceof Map
                        && this.props.offerDetails.get(orderItem.productId)!=null){
                        let offerDetail = this.props.offerDetails.get(orderItem.productId);
                        if(!appUtil.checkEmpty(offerDetail) && !appUtil.checkEmpty(offerDetail.offerPrice)){
                            offerAmount = offerDetail.offerPrice;
                        }
                    }
                    totalGCAmount = totalGCAmount + orderItem.totalAmount + (offerAmount * orderItem.quantity);
                }
            });
        }
        //console.log('Total Gift card amount', totalGCAmount);
        return totalGCAmount;

    }

    componentWillMount() {
        if (appUtil.checkEmpty(this.props.unit)) {
            browserHistory.push("/menu");
        } else {
            this.init();
        }
    }

    componentDidMount() {
        trackUtils.trackPageView({page:"cart",device:"desktop",custom:true});
        window.scrollTo(0, 0);
        if (appUtil.checkEmpty(this.props.unit)) {
            browserHistory.push("/menu");
        } else if (!appUtil.checkEmpty(this.props.cart) && this.props.cart.orderDetail.orders.length > 0) {
            if (this.props.unit.id == this.props.cart.orderDetail.unitId) {
                document.getElementById("orderRemarkInput") != null ? document.getElementById("orderRemarkInput").value = this.props.cart.orderDetail.orderRemark : null;
                document.getElementById("couponInput") != null ? document.getElementById("couponInput").value = "" : null;
                if (this.props.offerRemovedInitCart) {
                    this.props.dispatch(UtilityActions.showPopup("We have removed coupon from your cart as it has been updated. Please reapply coupon.", "info", 5000));
                    this.props.dispatch(CartManagementActions.removeOfferInitCart(false));
                }
            } else {
                browserHistory.push("/menu");
            }
        }
    }

    render() {
        if (this.props.unit != null) {
            var cartLength = 0;
            var showTotalGCAmount = false;
            if (!appUtil.checkEmpty(this.props.cart) && this.props.cart.orderDetail.orders.length > 0) {
                cartLength = this.props.cart.orderDetail.orders.length;
                this.props.cart.orderDetail.orders.map((orderItem, index) => {
                    if(orderItem.cardType === "ECARD"){
                        showTotalGCAmount = true;
                    }
                    if (orderItem.productId == 1043 || orderItem.productId == 1044) {
                        cartLength--;
                    }
                });
            }
            if (!appUtil.checkEmpty(this.props.cart) && cartLength > 0) {
                var items = [];
                var delivery = 0, packaging = 0;
                this.props.cart.orderDetail.orders.map((orderItem, index) => {
                    if (orderItem.productId != 1043 && orderItem.productId != 1044) {
                        items.push(<DesktopCartItemLayout key={index} item={orderItem}/>);
                    } else {
                        if (orderItem.productId == 1043) {
                            packaging = orderItem.totalAmount + parseFloat(orderItem.tax);
                        }
                        if (orderItem.productId == 1044) {
                            delivery = orderItem.totalAmount + parseFloat(orderItem.tax);
                        }
                    }
                });
                var taxDetails = [];
                this.props.cart.orderDetail.transactionDetail.taxes.map(tax => {
                    taxDetails.push(
                        <div key={tax.code+tax.percentage} class="txDetailItem">
                            {tax.code} ({tax.percentage}%)
                            <span class="right">
                                <img class="rupeeIcon" src="../../img/rupee.png"/>
                                {tax.value}
                            </span>
                        </div>
                    )
                });

                return (
                    <div>
                        <div class="colouredHead">
                            <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                        </div>
                        <div class="desktopPageContainer" style={{background: "#fbfbfb"}}>
                            <div class="desktopPageHead">My Cart</div>
                            <div class="movingContainer">
                                <div class="cartHead" style={{marginRight:"1%",boxShadow:"0 1px 2px 0 #e4e4df"}}>
                                    {cartLength} {cartLength > 1 ? "Items" : "Item"}
                                </div>
                                {items}
                            </div>
                            <div class="fixedContainer">
                                {this.props.criteria == "TAKE_AWAY" ? (
                                    <div class="cartBox">
                                        <div class="cartHead">Take Away</div>
                                        <div class="cartItemContainer" style={{padding:"20px"}}>
                                            {this.props.unit.name}
                                        </div>
                                    </div>
                                ) : null}
                                <div class="cartBox">
                                    <div class="cartHead">Instructions</div>
                                    <div class="cartItemContainer">
                                        <input class="orderRemark" type="text" id="orderRemarkInput" maxLength="200"
                                               placeholder="Add Instructions" onBlur={this.setOrderRemark.bind(this)}/>
                                    </div>
                                </div>
                                {this.props.criteria == "TAKE_AWAY" ? (
                                    <div class="cartBox">
                                        <div className="cartHead">From Cafe / Car Delivery</div>
                                        <div className="cartItemContainer">
                                            <input className="orderRemark" type="text" id="orderRemarkInput"
                                                   maxLength="200"
                                                   placeholder="If car delivery, Please enter car details."
                                                   onBlur={this.setOrderAndTakeAwayRemark.bind(this)}/>
                                        </div>
                                    </div>
                                ) : null}
                                <div class="cartBox">
                                    <div class="cartHead">Coupon</div>
                                    <div class="cartItemContainer">
                                        <div class="rel">
                                            <input class="orderCoupon" type="text" id="couponInput"
                                                   placeholder="Apply Coupon" maxLength="30"/>
                                            {this.props.offerApplied ? (
                                                <div class="couponLink" onClick={this.removeCoupon.bind(this)}>Remove</div>
                                            ) : (
                                                <div class="couponLink" onClick={this.applyCoupon.bind(this)}>Apply</div>
                                            )}
                                        </div>
                                    </div>
                                    {this.props.campaignDetail!=null && this.props.campaignDetail.cp!=null?(
                                        <div class="campaignCoupon" onClick={this.setCouponAndApply.bind(this, this.props.campaignDetail.cp)}>Use coupon {this.props.campaignDetail.cp} : Click to apply</div>
                                    ):null}
                                    {!this.props.offerApplied && this.props.customerAccepted == true && this.props.offerErrorCode == null?(
                                        <div class="campaignCoupon" onClick={this.setCouponAndApply.bind(this, this.props.promoOfferCode)}>Use coupon {this.props.promoOfferCode} : Click to apply</div>
                                    ):null}
                                    {!this.props.offerApplied && this.props.offerErrorCode != null ? (
                                        <div class="offerError">{this.props.offerErrorCode != 101 ? (
                                            <span>{this.props.offerErrorMessage}</span>
                                        ) : (
                                            <span>{this.props.offerErrorMessage} Please
                                            <span class="couponLogin" onClick={this.syncAndLogin.bind(this)}>login</span>to apply offer.</span>
                                        )}</div>
                                    ) : null}
                                </div>
                                <div class="cartBox">
                                    <div class="cartHead">Price details</div>
                                    <div class="cartItemContainer">
                                        <div class="transactionDetails">
                                            {/*<div class="txDetailItem">
                                                Sub Total
                                                <span class="right">
										            <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                    {Math.round(this.props.cart.orderDetail.transactionDetail.totalAmount - (delivery + packaging))}
									            </span>
                                            </div>*/}
                                            {delivery > 0 ? (
                                                <div class="txDetailItem">
                                                    Delivery Charges
                                                    <span class="right">
                                                        <img class="rupeeIcon" src="../../img/rupee.png"/>{Math.round(delivery)}
                                                    </span>
                                                </div>
                                            ) : (null)}
                                            {packaging > 0 ? (
                                                <div class="txDetailItem">
                                                    Packaging Charges
                                                    <span class="right">
                                                        <img class="rupeeIcon" src="../../img/rupee.png"/>{Math.round(packaging)}
                                                    </span>
                                                </div>
                                            ) : (null)}
                                            <div class="txDetailItem">
                                                Coupon Discount
                                                <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {Math.round(this.props.cart.orderDetail.transactionDetail.discountDetail.totalDiscount)}
                                                </span>
                                            </div>
                                            <div class="txDetailItem" onClick={this.showTaxDetail.bind(this)}>
                                                Tax <img class="taxInfo" src="../../img/info-icon.png"/>
                                                <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {Math.round(this.props.cart.orderDetail.transactionDetail.tax)}
                                                </span>
                                            </div>
                                            <div class={"taxDetailWrapper " + this.state.taxDetail}>
                                                {taxDetails}
                                            </div>
                                            <div class="txDetailItem totalAmount" style={{borderTop:"#ddd 1px dashed"}}>
                                                Amount Payable
                                                <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                    {Math.round(this.props.cart.orderDetail.transactionDetail.paidAmount)}
                                                </span>
                                            </div>
                                            <div class="txDetailItem">
                                                Savings
                                                <span class="right">
                                                    <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {Math.round(this.props.cart.orderDetail.transactionDetail.savings)}
                                                </span>
                                            </div>
                                            {showTotalGCAmount === true ?
                                                <div class="txDetailItem">
                                                    Total Wallet Amount
                                                    <span class="right">
                                                <img class="rupeeIcon" src="../../img/rupee.png"/>
                                                        {this.calculateGiftCardAmount()}
									        </span>
                                                </div>
                                                :null}
                                        </div>
                                    </div>
                                </div>
                                <div class="btn btn-primary" style={{margin:"10px 0"}} onClick={this.placeOrder.bind(this)}>
                                    Place Order
                                </div>
                            </div>
                        </div>
                        <MobileCustomizationModal />
                        <DesktopFooterLayout />
                    </div>
                )
            } else {
                return (
                    <div class="cartContainer">
                        <div class="colouredHead">
                            <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false} />
                        </div>
                        <div class="desktopPageContainer" style={{background: "#fbfbfb"}}>
                            <div class="desktopPageHead">My Cart</div>
                            <div class="text-center">
                                <img class="emptyCartIcon" src="../../img/icon-sad-cup.png"/>
                                <div class="emptyCartHead">Your cart is empty.</div>
                                <div class="emptyCartTag">Try some delicious food and beverages from our menu.</div>
                                <div class="btn btn-primary small" style={{width:"200px",margin:"auto",marginTop:"20px"}} onClick={this.goToMenu.bind(this)}>Go To Menu</div>
                            </div>
                        </div>
                        <DesktopFooterLayout />
                    </div>
                )
            }
        } else {
            return null;
        }

    }
}