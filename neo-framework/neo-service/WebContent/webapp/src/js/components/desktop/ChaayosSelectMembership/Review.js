import React from 'react'
export default function Review() {
  const reviews = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>"
      ,
      img:'./../../../img/ChaayosSelectMembership_images/review1.jpg',
      text:
        "I have been a loyal customer of CHAAYOS for years, and I can honestly say that they consistently serve up some of the most delicious food and drink options out there. Whether I'm grabbing a quick tea on my way to work or stopping in for a sit-down meal with friends, I know I can always count on chaayos for quality and taste."    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      img:'./../../../img/ChaayosSelectMembership_images/review3.jpg',

      text:
        "I've visited Chaayos stores all over the country, and no matter where I go, I'm always blown away by the quality of the food and drink options. Whether I'm in the mood for something savory, sweet, or spicy, <PERSON><PERSON><PERSON> has a menu item that hits the spot every time. And with so many stores to choose from, it's easy to find one no matter where I'm traveling." },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      img:'./../../../img/ChaayosSelectMembership_images/review4.jpg',

      text:
        "I've been a foodie for years, and I have to say that <PERSON><PERSON><PERSON> is one of my favorite places to eat and drink. From their fresh ingredients to their creative flavor combinations, everything on the menu is top-notch especially the Customization, as i always want my meals to be my way. And with over 200 stores, I can always find a location nearby when I'm craving something delicious."   },
    {
      id: 4,
      name: "Shubhi Gupta",
      img:'./../../../img/ChaayosSelectMembership_images/review5.jpg',
      text:
        "I recently visited Chaayos for the first time, and I was blown away by the quality of the food and drink options. Not only was everything delicious, but the presentation was beautiful as well. I can't wait to visit again and try more items on the menu!"    },
      
  ];
  
  return (
 <div className="mainreviewtestimonialWrapper">
    {reviews.map((el,i)=>(
      <div className='mainreviewTestimonials' key={i} >
      <img className='mainreviewImg' src={el.img}/>
      <div className='mainreviewRating' >
        <img className="mainreviewStar" src='./../../../img/ChaayosSelectMembership_images/Star.svg'/>
        <img className="mainreviewStar" src='./../../../img/ChaayosSelectMembership_images/Star.svg'/>
        <img className="mainreviewStar" src='./../../../img/ChaayosSelectMembership_images/Star.svg'/>
        <img className="mainreviewStar" src='./../../../img/ChaayosSelectMembership_images/Star.svg'/>
        <img className="mainreviewStar" src='./../../../img/ChaayosSelectMembership_images/Star.svg'/>
      </div>
      <div className='mainreviewRemainingPart'>
        <h1 className='mainreviewName' >{el.name}</h1>
        <p className='mainreviewDiscription' >{el.text} </p> 
      </div>
      </div>
      ))}
      <img className='mainreviewLeaves'  src="./../../../img/ChaayosSelectMembership_images/leaves_pattern.png" />
  </div>   
    
  )
}
