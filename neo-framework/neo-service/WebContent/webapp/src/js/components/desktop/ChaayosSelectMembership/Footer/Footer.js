import React, { Component } from 'react'
export default class Footer extends Component {
 
  render() { 
    const  date = new Date();
    const year = date.getFullYear();
    return (
    <div style={{position:"relative"}}>  
      <div className='mainfooterRow mainfooterContainer' >
        <div className='mainfooterCol mainfooterFirst' >
          <a className="mainfooterHeading" href='https://chaayos.com/' ><img  src='./../../../img/ChaayosSelectMembership_images/footerLogo.webp' /></a>
          <p className='mainfooterHeadingText' style={{width:"34%",display:"none",textAlign:"center"}}>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Mollitia, tenetur.</p>
          <div  className='mainfooterRow mainfooterSocialMediaIcons'>
            <a className='mainfooterSocialMediaIconItem' href="https://www.facebook.com/Chaayos/"><img className='mainfooterImg ' src='./../../../img/ChaayosSelectMembership_images/facebook.png' /></a>
            <a className='mainfooterSocialMediaIconItem' href="https://www.instagram.com/chaayos/"><img className='mainfooterImg' src='./../../../img/ChaayosSelectMembership_images/instagram.png' /></a>
            <a className='mainfooterSocialMediaIconItem' href='https://twitter.com/chaayos'><img className='mainfooterImg' src='./../../../img/ChaayosSelectMembership_images/twitter.png'/></a>
          </div>
          <div  className='mainfooterRow mainfooterAppStores'>
            <a  className='mainfooterAppStoreItem' href='https://play.google.com/store/apps/details?id=com.chaayos'><img className='mainfooterImg mainfooterAppstoreIcons' src='./../../../img/ChaayosSelectMembership_images/GooglePlay.png'  style={{borderRadius:"30%",height:"150%"}}/></a>
            <a className='mainfooterAppStoreItem' href='https://apps.apple.com/in/app/chaayos-india/id1521406820'><img className='mainfooterImg mainfooterAppstoreIcons' src='./../../../img/ChaayosSelectMembership_images/Appstore.png' style={{borderRadius:"30%",height:"150%"}}/></a>
          </div>
        </div>
        <div   className='mainfooterCol mainfooterSecond'>
          <div className='mainfooterSubHeading'>Get in touch</div>
            <div className='hover-underline-animation mainfooterSubSubHeading'> Email us:</div>
            <a className=' mainfooterSubSubHeading' href='mailto:<EMAIL>'> <EMAIL></a>
            <div className='hover-underline-animation mainfooterSubSubHeading'>Phone:</div>
            <a className=' mainfooterSubSubHeading' href='Tel:18001231234'> 1800 123 1234</a>
        </div>
        <div className='mainfooterCol mainfooterThird'>
          <div className='mainfooterSubHeading'>Links</div>
            <a className='hover-underline-animation mainfooterSubSubHeading'  href='https://chaayos.com/'>Home</a>
            <a className='hover-underline-animation mainfooterSubSubHeading'  href='https://chaayos.com/pages/about-brand'>About</a>
            <a className='hover-underline-animation mainfooterSubSubHeading'  href='https://chaayos.com/collections/all'>Shop</a>
        </div>

        <div className='mainfooterCol mainfooterFourth'>
          <div className='mainfooterSubHeading'>Company</div>
            <a className='hover-underline-animation mainfooterSubSubHeading'  href='https://chaayos.com/pages/chaayos-terms-and-conditions'>Terms & Conditions</a>
            <a className='hover-underline-animation mainfooterSubSubHeading'  href='https://chaayos.com/pages/chaayos-privacy-policy'>Privacy Policy</a>
        </div>

      
      </div> 
      <div className='mainfooterCopyright' >
          Copyright © {year} <a href="https://chaayos.com/"style={{color:'#0E8B45'}}>Chaayos</a>. All Rights Reserved.
      </div> 
      <img className='mainfooterLeaves' src="./../../../img/ChaayosSelectMembership_images/leaves_pattern.png" />

    </div>
       
      
    )
  }
}
