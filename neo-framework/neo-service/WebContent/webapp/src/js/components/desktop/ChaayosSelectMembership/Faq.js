import React from 'react'    
export default class Faq  extends React.Component {
  
    constructor(props)
    {
        super(props);
        this.state={
            
                flag:[true,false,false,false],
                faqs:[],
                            
        }
    }
    componentWillMount() {
         this.setState({

            faqs:[
                {
                    id:0,
                    ques:"What is Chaayos Select?",
                    ans:"For the generation that knows how to make profitable investment portfolio, Chaayos has come up with Chaayos Select, which is a membership program which offers excellent return on investment."
        
                },
                {
                    id:1,
                    ques:"How to avail it? ",
                    ans:"Chaayos Select can be availed from the Chaayos App, Website, or the Chaayos Cafe"
                },
                {
                    id:2,
                    ques:"How to redeem it? ",
                    ans:" Chaayos Select can be redeemed from the Chaayos App & the Chaayos Cafe."
                },
                {
                    id:3,
                    ques:"What are the inclusions of Chaayos select?",
                    ans:"Flat 15% Off on all the products. Unlimited number of transactions, all at 15% Off."
                }
            ]

        });
    }
    
 render(){
    const self=this
    function handleChange(index){
        var val=self.state.flag[index]
        let newFlag=[false,false,false,false]
        newFlag[index]=!val
        self.setState({flag:newFlag})
       
    }
 

  return (
            
            <div class="mainfaqContainer" >
                <div className='mainfaqHeading'>FAQs </div>
                <div style={{ position: "static",zIndex: "1"}}>{this.state.faqs.map((faq)=>(
                    
                    <div  key={faq.id} class="mainfaqQues"  onClick=
                        {
                            
                            ()=>handleChange(faq.id)
                        }
                         >
                        {/* <!-- faq question --> */}              
                        <div className='mainfaqOne' >
                            <p class="mainfaqPage" style={{fontWeight:"700"}} onClick={(e)=> handleChange(e)}>
                                {faq.ques} 

                            </p>
                            {
                                this.state.flag[faq.id] ? (
                                    <img  className='mainfaqUpArrow' src='./../../../img/ChaayosSelectMembership_images/up.svg' />
                                ):(
                                <img className='mainfaqDownArrow' src='./../../../img/ChaayosSelectMembership_images/down.svg' />)
                            }       
                            
                        </div>
                        {/* <!-- faq answer --> */}
                        {this.state.flag[faq.id] ? <div  class="faq-body">
                            <p className='mainfaqPage mainfaqans' style={{fontSize:"2.4vh",padding: "0vh 1.5vw 2vh"}}>{faq.ans}</p>
                            </div> : null }          
                    </div>
                    
                ) )}


                </div>
                <img className='mainfaqLeaves' src="./../../../img/ChaayosSelectMembership_images/leaves_pattern.png" />
        </div>
  )
}
  
  
}




  



