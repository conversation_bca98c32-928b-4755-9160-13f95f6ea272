import React from "react";
import {
    EmailShareButton,
    FacebookShareButton,
    TwitterShareButton,
    WhatsappShareButton,
} from "react-share";
import {
    EmailIcon,
    FacebookIcon,
    TwitterIcon,
    WhatsappIcon,
} from "react-share";
export default function DesktopSocial({text, url, title, urlForFB,
                              urlForWhatsapp, urlForTwitter, urlForEmail}) {
    return (
        <div style ={{display : 'flex', marginTop:'10px'}}>
                <div style = {{margin : '5px'}}>
                    <FacebookShareButton quote={text} url={urlForFB}>
                        <FacebookIcon size={45} logoFillColor="white" round={true} />
                    </FacebookShareButton>
                </div>

                <div style = {{margin : '5px'}}>
                    <WhatsappShareButton title={text} url={urlForWhatsapp}>
                        <WhatsappIcon size={45} logoFillColor="white" round={true} />
                    </WhatsappShareButton>
                </div>

                <div style = {{margin : '5px'}}>
                    <TwitterShareButton title={title} via={text} url={urlForTwitter}>
                        <TwitterIcon size={45} logoFillColor="white" round={true} />
                    </TwitterShareButton>
                </div>

                <div style = {{margin : '5px'}}>
                    <EmailShareButton subject={title} body={text} url={urlForEmail}>
                        <EmailIcon size={45} logoFillColor="white" round={true} />
                    </EmailShareButton>
                </div>
        </div>
    );
}
