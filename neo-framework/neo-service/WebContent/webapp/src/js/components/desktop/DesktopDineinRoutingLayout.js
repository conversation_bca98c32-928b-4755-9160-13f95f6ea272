import React from "react";
import {connect} from "react-redux"
import * as LocalityActions from "../../actions/LocalityActions";
import DesktopOutletMenuLayout from "./DesktopOutletMenuLayout";
import * as UtilityActions from "../../actions/UtilityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";

@connect((store) => {
    return {
        showFullPageLoader: store.utilityReducer.showFullPageLoader,
        loaderMessage: store.utilityReducer.loaderMessage,
    };
})
export default class DesktopDineinRoutingLayout extends React.Component {

    constructor(){
        super();
        this.init = this.init.bind(this);
    }

    init(){
    }

    componentWillMount(){
        this.init();
        const params = this.props.props.params;
        this.props.dispatch(OutletMenuActions.loadDineinMenu(params.unitId));
    }

    componentDidMount(){
        this.props.dispatch(UtilityActions.hideFullPageLoader());
    }

    render() {

        return (
            <div>
                {this.props.showFullPageLoader ? (
                    <div class="fullPageLoader">
                        <div class="loaderWrapper">
                            <div class="load8 loader"></div>
                            {this.props.loaderMessage != null ? (
                                <p class="loaderMessage">{this.props.loaderMessage}</p>
                            ) : (null)}
                        </div>
                    </div>
                ) : (
                    <DesktopOutletMenuLayout />
                )}
            </div>
        )
    }
}