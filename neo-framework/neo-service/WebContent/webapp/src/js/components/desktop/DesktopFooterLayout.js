/**
 * Created by Chaayos on 06-12-2016.
 */
import React from "react";
import { Link, browserHistory } from "react-router";
import {connect} from "react-redux";

@connect((store) => {
    return {
        restrictDineIn:store.utilityReducer.restrictDineIn,
    };
})
export default class DesktopAboutLayout extends React.Component {

    constructor(){
        super();
    }

    render (){
        return(
            <div>
                {!this.props.restrictDineIn ? (
                    <div style={{paddingTop: "50px"}}>
                        <div className="footerContainer">
                            <div className="footerContent">
                                <ul>
                                    <li className="listItem"><Link to={"/about"} alt="About Chaayos">About Us</Link></li>
                                    <li className="listItem"><Link to={"/contact"} alt="Contact Page">Contact Us</Link></li>
                                    <li className="listItem"><Link to={"/terms"} alt="Terms and Conditions">Terms &
                                        Conditions</Link></li>
                                </ul>
                                <div className="socialLinkWrapper">
                                    <a href="https://www.facebook.com/chaayos" target="_blank" alt="Chaayos at Facebook">
                                        <img src="../../../img/desktop/facebook.png"/>
                                    </a>
                                    <a href="https://twitter.com/chaayos" target="_blank" alt="Chaayos Twitter page">
                                        <img src="../../../img/desktop/twitter.png"/>
                                    </a>
                                    <a href="https://instagram.com/chaayos" target="_blank" alt="Chaayos Instagram Page">
                                        <img src="../../../img/desktop/instagram.png"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div className="footerStrip">
                            <div className="footerStripContent">
                                &copy; Chaayos, Sunshine Teahouse Pvt. Ltd. All rights Reserved. CIN: U55204DL2012PTC304447
                            </div>
                        </div>
                    </div>
                ):null}
            </div>
        )
    }
}