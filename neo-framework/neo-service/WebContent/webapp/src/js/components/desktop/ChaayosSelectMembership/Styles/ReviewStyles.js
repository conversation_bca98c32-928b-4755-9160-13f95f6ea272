
export const styles={
  wrapper:{
    display:"flex",
    margin:"5vmin",
    flexDirection:"row",
    overflow:"auto",
    scrollbarWidth: "none",
    height:"54vh",
    padding: "6vh",
    marginLeft:"-2vmin",
    position:"relative"


  
 },

  testimonials: {  

  position: "relative",
  display: "flex",
  flexDirection: "column",
  background: "#F3F3F3",
  boxShadow: "-20px -20px 50px #FFFFFF, 20px 20px 50px #D2D2D2",
  zIndex:"1",
  padding: "3vw",
  borderRadius: "43px",
  margin:"3vh 5vw",

  

  },
  discription: {
    fontSize: "auto",
    textAlign:"center",
    width:"15vw",
 

  },
  name:{
    fontWeight:"bolder",
    fontSize: "2vw",
    textAlign: "center",
    padding: "0vh 0vh 1vh 0vh"

  },
  image:{
    position: "absolute",
    height: "32%",
    width: "25%",
    border: "1px",
    borderRadius: "50%",
    top: "-15%",
    left: "10%",


  },
  rating:{

    position: "absolute",
    left: "60%",
    top: "6%",
    fontSize: "2.5vw", 

  },
  remainingPart:{
    marginTop:"3vh"
   },
   leaves:{
    position: "absolute",
    height: "61vmin",
    transform: `rotate(-92deg)`,
    left: "53vw",
    top: "-17vh"

   }



}