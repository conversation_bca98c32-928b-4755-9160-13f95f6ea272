import React from "react";
import {connect} from "react-redux";
import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        redirectDetails: store.outletMenuReducer.redirectDetails,
    };
})
export default class DesktopOutletDeliveryRedirectLayout extends React.Component {

    constructor() {
        super();
        this.state = {};
        this.init = this.init.bind(this);
        this.redirectWeb = this.redirectWeb.bind(this);
    }

    init() {
        const params = this.props.props.params;
        this.props.dispatch(OutletMenuActions.getZomatoRedirectLink({id:params.unitId}, true));
    }

    redirectWeb() {
        this.props.dispatch(OutletMenuActions.redirectToZomatoWeb(this.props.redirectDetails, true));
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.init();
    }

    componentDidMount() {
        trackUtils.trackPageView({page: "deliveryRedirect", device: "desktop", custom: true});
    }

    render() {


        return (
            <div>
                <div class="colouredHead">
                    <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false}
                                   props={this.props.props}/>
                </div>
                <div
                    style={{background: "#FFF", padding: "50px 0 100px 0", marginBottom: "200px", textAlign: "center"}}>
                    <div class="loaderWrapper">
                        <div class="load8 loader"></div>
                    </div>
                    <h1 style={{fontSize: "18px", marginBottom: "15px"}}>Please wait while we
                        redirect you to partner site for ordering.</h1>
                    <h3>Please click below button if you are not redirected
                        automatically withing few seconds.</h3>
                    <input type="button" value="Order" class="redirectBtn" style={{
                        border: "none",
                        borderRadius: "50px",
                        padding: "15px",
                        color: "#FFF",
                        marginTop: "20px",
                        background: "darkolivegreen",
                        fontSize: "18px",
                        width: "200px",
                        cursor: "pointer"
                    }} onClick={this.redirectWeb.bind(this)}/>
                </div>
                <DesktopFooterLayout/>
            </div>
        )
    }
}