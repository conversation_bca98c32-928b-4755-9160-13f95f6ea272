/**
 * Created by Chaayos on 23-03-2017.
 */
import React from "react";
import {browserHistory} from "react-router";
import {connect} from "react-redux";
import Select from "react-select";
import appUtil from "../../AppUtil";
import * as LocalityActions from "../../actions/LocalityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import * as UtilityActions from "../../actions/UtilityActions";
import productService from "../../service/ProductService";

@connect((store) => {
    return {
        criteria: store.localityReducer.criteria,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedCity: store.localityReducer.selectedCity,
        selectedOutlet: store.localityReducer.selectedOutlet,
        localities: store.localityReducer.localities,
        cities: store.localityReducer.cities,
        outlets: store.localityReducer.outlets,
        showLocality:store.localityReducer.showLocality,
        showOutlet:store.localityReducer.showOutlet,
        showError:store.localityReducer.showError,
        showLoader:store.localityReducer.showLoader,
        sidebarOpen: store.sidebarReducer.sidebarOpen,
        showLocationWrapper: store.localityReducer.showLocationWrapper,
        cityStateMap:store.localityReducer.cityStateMap,
        campaignInitialized: store.campaignReducer.campaignInitialized,
    };
})
export default class DesktopLocationLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            sizeDescription:productService.getSizeDescriptions(),
            products: {},
            citiesList:[],
            localitiesList: [],
            outletsList:[],
        };
        this.init = this.init.bind(this);
        this.loadCities = this.loadCities.bind(this);
        this.loadLocalities = this.loadLocalities.bind(this);
        this.loadOutlets = this.loadOutlets.bind(this);
        this.formatLocalities = this.formatLocalities.bind(this);
        this.formatCities = this.formatCities.bind(this);
        this.formatOutlets = this.formatOutlets.bind(this);
        this.selectCity = this.selectCity.bind(this);
        this.selectLocality = this.selectLocality.bind(this);
        this.selectOutlet = this.selectOutlet.bind(this);
        this.switchCriteria = this.switchCriteria.bind(this);
        this.closeLocationWrapper = this.closeLocationWrapper.bind(this);
        this.loadUnitData = this.loadUnitData.bind(this);
    }

    init() {
        //console.log("Initialize DesktopLocationLayout");
        //console.log("window.location.pathname is :: " +window.location.pathname);
    }

    formatLocalities(){
        this.state.localitiesList = [];
        this.props.localities.map((item, index) =>
        {
            this.state.localitiesList.push({value:index, label:item});
        });
    }

    formatCities(){
        this.state.citiesList = [];
        const selectedCity = this.props.selectedCity!=null?this.props.selectedCity.city:null;
        this.props.cities.map((item, index) =>
        {
            if(selectedCity==item){
                this.state.citiesList.push(<div key={index} class="cityBtn active" onClick={this.selectCity.bind(this, item)}>{item}</div>);
            }else{
                this.state.citiesList.push(<div key={index} class="cityBtn" onClick={this.selectCity.bind(this, item)}>{item}</div>);
            }
        });
    }

    formatOutlets(){
        if(this.props.outlets.length>0){
            this.state.outletsList = [];
            this.props.outlets.map((item) => {
                this.state.outletsList.push({value:item.id, label:item.name});
            });
        }
    }

    loadCities(){
        //var data = StorageUtils.getLocalityMetadata();
        if(this.props.cities.length==0){
            this.props.dispatch(LocalityActions.loadCitiesList(this.props.criteria));
            /*if(data!=null && data.city!=null){
                this.props.dispatch(LocalityActions.setCity(data.city));
            }*/
        }else{
            this.props.dispatch(LocalityActions.hideError());
        }
    }

    loadLocalities(val){
        this.props.dispatch(LocalityActions.loadLocalitiesList(val));
    }

    loadOutlets(val){
        this.props.dispatch(LocalityActions.loadOutletsList(val));
    }

    selectCity(val){
        if(val!=null){
            this.props.dispatch(LocalityActions.selectCity(val, this.props.cityStateMap[val]));
            if(this.props.criteria=="DELIVERY"){
                this.loadLocalities(val);
            }else{
                this.loadOutlets(val);
            }
        }else{
        }
    }

    selectLocality(val){
        if(val !=null && val.value!=null && val.label!=null){
            //var data = appUtil.getLocalityMetadata();
            this.props.dispatch(LocalityActions.selectLocality(val));
            //this.loadUnitData(this.props.criteria,this.props.selectedCity,val,null);
            this.loadUnitData("DELIVERY",this.props.selectedCity,val,null);
            var val = this.props.showLocationWrapper;
            this.props.dispatch(LocalityActions.toggleLocationWrapper(!val));
            document.getElementsByTagName("body")[0].scrollTop = 0;
        }else{
            this.props.dispatch(UtilityActions.showPopup("Please select locality again!"));
        }
    }

    selectOutlet(val){
        if(val!=null && val.value!=null && val.label!=null){
            this.props.dispatch(LocalityActions.selectOutlet(val));
            this.loadUnitData("TAKE_AWAY",this.props.selectedCity,null,val);
            var val = this.props.showLocationWrapper;
            this.props.dispatch(LocalityActions.toggleLocationWrapper(!val));
            document.getElementsByTagName("body")[0].scrollTop = 0;
        }
    }

    switchCriteria(val){
        this.props.dispatch(LocalityActions.setCriteria(val));
        if(this.props.selectedCity!=null && this.props.selectedCity.city!=null){
            if(val=="DELIVERY"){
                this.loadLocalities(this.props.selectedCity.city);
            }else{
                this.loadOutlets(this.props.selectedCity.city);
            }
        }else{
            this.props.dispatch(UtilityActions.showPopup("Please select city!"));
        }
        this.props.dispatch(OutletMenuActions.discardUnit());
    }

    closeLocationWrapper(){
        if(this.props.criteria==null){
            this.props.dispatch(UtilityActions.showPopup("Please select delivery or pickup.", "info"));
        }else if(this.props.criteria=="DELIVERY" && this.props.selectedLocality==null){
            this.props.dispatch(UtilityActions.showPopup("Please select delivery location.", "info"));
        }else if(this.props.criteria=="TAKE_AWAY" && this.props.selectedOutlet==null){
            this.props.dispatch(UtilityActions.showPopup("Please select outlet for pickup.", "info"));
        }else{
            var val = this.props.showLocationWrapper;
            this.props.dispatch(LocalityActions.toggleLocationWrapper(!val));
            document.getElementsByTagName("body")[0].scrollTop = 0;
            if(appUtil.checkEmpty(this.props.unit)){
                this.loadUnitData(this.props.criteria,this.props.selectedCity,
                    this.props.criteria=="DELIVERY"?this.props.selectedLocality:null,this.props.criteria=="DELIVERY"?null:this.props.selectedOutlet);
            }
        }
    }

    loadUnitData(criteria, city, locality, outlet) {
        this.props.dispatch(OutletMenuActions.getUnitProducts(criteria, city, locality, outlet));
        this.props.dispatch(OutletMenuActions.getTags());
        if(window.location.pathname=="/"){
            browserHistory.push("/menu");
        }
    }

    componentWillMount(){
        this.init();
    }

    componentDidMount(){
        /*var data = appUtil.getLocalityMetadata();
        if(data!=null && data.criteria!=null){
            if(appUtil.checkEmpty(data.locality) && appUtil.checkEmpty(data.outlet)){
                this.props.dispatch(LocalityActions.toggleLocationWrapper(true));
            }
        }*/
        if (this.props.campaignInitialized==null && (this.props.criteria == "DELIVERY" && appUtil.checkEmpty(this.props.selectedLocality)) ||
            ((this.props.criteria == "TAKE_AWAY" || this.props.criteria == "DINE_IN") && appUtil.checkEmpty(this.props.selectedOutlet))) {
            this.props.dispatch(LocalityActions.toggleLocationWrapper(true));
        }
        setTimeout(function () {
            if (document.getElementsByClassName("actionAreaContainer")[0] != null && document.getElementsByClassName("actionAreaContainer").length > 0) {
                document.getElementsByClassName("actionAreaContainer")[0].className += " active";
            }
        }, 1000);
    }

    render (){

        var cityData = [];
        if(this.props.cities.length>0){
            this.formatCities();
        }
        if(this.props.localities.length>0){
            this.formatLocalities();
        }
        if(this.props.outlets.length>0){
            this.formatOutlets();
        }

        return(
            <div class={this.props.showLocationWrapper?"locationSelectionWrapper active":"locationSelectionWrapper"}>
                <div class="actionAreaContainer">
                    <div class="closeLocationWrapper" onClick={this.closeLocationWrapper.bind(this)}>&times;</div>
                    <div class="chevronDown"></div>
                    <p class="headline">Select your choices to start placing order</p>
                    <div class="actionInputWrapper">
                        <div className="radioLabels" onClick={this.switchCriteria.bind(this, "TAKE_AWAY")}>
                            <input type="radio" id="pickup" name="orderType" value="TAKE_AWAY" className="addrTypeInput"
                                   readOnly checked={this.props.criteria == "TAKE_AWAY"}/>
                            <label htmlFor="pickup"><span><span></span></span>Take Away</label>
                        </div>
                        <div className="radioLabels" onClick={this.switchCriteria.bind(this, "DINE_IN")}>
                            <input type="radio" id="dinein" name="orderType" value="DINE_IN" className="addrTypeInput"
                                   readOnly checked={this.props.criteria == "DINE_IN"}/>
                            <label htmlFor="dinein"><span><span></span></span>Dine In</label>
                        </div>
                        <div className="radioLabels" onClick={this.switchCriteria.bind(this, "DELIVERY")}>
                            <input type="radio" id="delivery" name="orderType" value="DELIVERY" className="addrTypeInput"
                                   readOnly checked={this.props.criteria == "DELIVERY"}/>
                            <label htmlFor="delivery"><span><span></span></span>Delivery</label>
                        </div>
                    </div>
                    <div class="citySelector">
                        {this.state.citiesList}
                    </div>
                    <div class={this.props.showLocality?"localitySelector active":"localitySelector"}>
                        <Select name="localitiesList" value={this.props.selectedLocality} options={this.state.localitiesList}
                                openOnFocus={true} backspaceRemoves={false} onChange={this.selectLocality.bind(this)} clearable={false} placeholder="Type location" autoBlur={true} />
                    </div>
                    <div class={this.props.showOutlet?"outletSelector active":"outletSelector"}>
                        <Select name="outletsList" value={this.props.selectedOutlet} options={this.state.outletsList}
                                openOnFocus={true} backspaceRemoves={false} onChange={this.selectOutlet.bind(this)} clearable={false} placeholder="Type outlet name" autoBlur={true} />
                    </div>
                    {this.props.showLoader?(
                        <div class="text-center load8 loader"></div>
                    ):(null)}
                    {this.props.showError?(
                        <p class="errorMessage">Error loading data. Please <span onClick={browserHistory.push("/")}>Try again!</span></p>
                    ):(null)}
                </div>
            </div>
        )
    }
}