import React from "react";
import { connect } from "react-redux"
import * as UtilityActions from "../../actions/UtilityActions";
import * as PromoActions from "../../actions/PromoActions";

@connect((store) => {
    return {
        showPopupMessage: store.utilityReducer.showPopupMessage,
        showFullPageLoader: store.utilityReducer.showFullPageLoader,
        messageText: store.utilityReducer.messageText,
        loaderMessage: store.utilityReducer.loaderMessage,
        messageType: store.utilityReducer.messageType,
        showPrompt: store.utilityReducer.showPrompt,
        promptMessage:store.utilityReducer.promptMessage,
        promptSuccess:store.utilityReducer.promptSuccess,
        promptDismiss:store.utilityReducer.promptDismiss,
        showPromoOffer:store.promoOfferReducer.showPromoOffer,
        promoOfferCode:store.promoOfferReducer.promoOfferCode,
        promoOfferImage:store.promoOfferReducer.promoOfferImage,
        promoOfferHeadline:store.promoOfferReducer.promoOfferHeadline,
        promoOfferText:store.promoOfferReducer.promoOfferText,
        isLoginPromo:store.promoOfferReducer.isLoginPromo,
    };
})
export default class DesktopUtilityLayout extends React.Component {

    constructor(){
        super();
        this.init = this.init.bind(this);
        this.handlePrompt = this.handlePrompt.bind(this);
        /*this.onChange = this.onChange.bind(this);*/
        this.closePromo = this.closePromo.bind(this);
        this.applyPromoOffer = this.applyPromoOffer.bind(this);
    }

    init(){
        //console.log(this.props);
        //this.props.dispatch(PromoActions.checkPromoShow());
    }

    handlePrompt(isSuccess){
        if(isSuccess){
            if(this.props.promptSuccess!=null && typeof this.props.promptSuccess =="function"){
                this.props.promptSuccess();
            }
        }else{
            if(this.props.promptDismiss!=null && typeof this.props.promptDismiss =="function"){
                this.props.promptDismiss();
            }
        }
        this.props.dispatch(UtilityActions.handlePrompt(isSuccess));
    }

    /*onChange() {
        window.innerWidth - window.innerHeight > 200 ? this.props.dispatch(UtilityActions.handleRotate(true)) : this.props.dispatch(UtilityActions.handleRotate(false));
    }*/

    closePromo(){
        this.props.dispatch(PromoActions.showPromo(false));
    }

    applyPromoOffer(){
        this.props.dispatch(PromoActions.customerAccepted(true, this.props.isLoginPromo));
    }

    componentWillMount(){
        this.init();
    }

    componentDidMount(){
        //this.onChange();
        //window.addEventListener("resize", this.onChange);
    }

    render() {

        return (
            <div>
                <div
                    class={this.props.showPopupMessage?"popupWrapper active "+this.props.messageType:"popupWrapper "+this.props.messageType}>
                    {this.props.messageText}
                </div>
                {this.props.showFullPageLoader ? (
                    <div class="fullPageLoader">
                        <div class="loaderWrapper">
                            <div class="load8 loader"></div>
                            {this.props.loaderMessage != null ? (
                                <p class="loaderMessage">{this.props.loaderMessage}</p>
                            ) : (null)}
                        </div>
                    </div>
                ) : (null)}
                {this.props.showPrompt ? (
                    <div class="fullPageLoader">
                        <div class="loaderWrapper">
                            <p class="promptMessage">{this.props.promptMessage}</p>
                            <div class="text-right">
                                <button class="btn btn-default small" onClick={this.handlePrompt.bind(this, false)}>Cancel</button>
                                <button class="btn btn-primary small" onClick={this.handlePrompt.bind(this, true)}>OK</button>
                            </div>
                        </div>
                    </div>
                ) : (null)}
                {this.props.showPromoOffer ? (
                    <div>
                        <div class="fullPageLoader" onClick={this.closePromo.bind(this)}></div>
                        <div class="promoContainer">
                            <div class="promoWrapper">
                                <div class="promoClose" onClick={this.closePromo.bind(this)}>&times;</div>
                                <div class="promoImage" style={{backgroundImage:"url(../../../img/promo/"+this.props.promoOfferImage+")"}}></div>
                                <div class="promoBody">
                                    <h1 class="promoHeadline">{this.props.promoOfferHeadline}</h1>
                                    <p class="promoText">{this.props.promoOfferText}</p>
                                    <p class="applyBtn" onClick={this.applyPromoOffer.bind(this)}>Avail Offer</p>
                                    <p class="bottomNote">* Applicable only on first order</p>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (null)}
            </div>

        )
    }
}