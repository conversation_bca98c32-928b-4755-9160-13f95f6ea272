export const styles={
  overlay:{
    position:"fixed",
    height:"100%",
    width:"100%",
    backgroundColor:"rgba(255,255,255,0.9)",
    // backgroundColor:"rgba(5, 255, 255, 1.9)",
    zIndex:"10",
    
    top:"0%"
    
  },
  modalContainer:{
    width:"fit-content",
    height:"fit-content",
    top:"10%",
    position:"fixed",
    left:"25%",
    right:"25%",
    display:"grid",
    backgroundColor:"white",
    gridTemplateColumns: "auto auto",
    padding:"6vh 2vw",
    border: "26px solid #D9D9D9",
    borderRadius: "30px",
  },
  firstbox:{
      margin:"0vh 0vw 4vh"
  },
  heading:{
    fontSize: "3.5vmin",
    fontWeight: "bolder",
    marginBottom: "0.5vh",
    width:"18vw",
  },
  slider:{
    width: "100%",
    padding:"4vh 0vw 1vh",
  },
  selectOptionPlans:{
   
    height: "3vmin",
    margin:"0vh 0vw 0vh 1vw",
    
    
  },
  secondBox:{
    fontSize: "2.5vmin",
    fontWeight:"bold"
    
  },
  rightCol:{
    display: "flex",
    justifyContent: "flex-end"
  },
  savingBox:{
    padding:"3vh 2vw",
    fontSize: "2vmin",
    background: "#F9FAFE",
    boxShadow:"18px 39px 17px rgba(0, 0, 0, 0.01), 10px 22px 14px rgba(0, 0, 0, 0.05), 5px 10px 11px rgba(0, 0, 0, 0.09), 1px 2px 6px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1)",
    borderRadius: "10px",
    width:"60%",
   
    display: "flex",
    flexDirection:"column"
  },
  subscribeBtn:{
    display: "flex",
    justifyContent: "center",
    padding:"8px 16px",
    background:"#0E8B45",
    borderRadius: "50px",
    width: "12vw",
    color: "white",
    border:"none",
    alignItems: "center",
    fontSize:"2.5vmin"



  }

  


}