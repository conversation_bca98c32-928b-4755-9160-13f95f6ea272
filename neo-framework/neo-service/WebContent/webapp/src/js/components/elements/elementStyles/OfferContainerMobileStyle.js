export const styles = {
    offerCard : {
        width:'200px',
        display: 'flex',
        flexDirection:'column',
        justifyContent:'center',
        textAlign:'center',
        padding:'10px',
        position:'absolute'
    },
    youWonText : {
        color: 'white',
        fontFamily: 'Nunito',
        fontWeight: 'bold',
        fontSize: '15px'
    },
    offerText:{
        color: 'white',
        fontSize: window.innerWidth*.06,
        fontWeight: 'bolder',
        marginBottom: '5px',
        fontFamily: 'Nunito',
        marginTop:'20px'
    },
    nextVisitText:{
        fontFamily: 'Nunito',
        color:'white',
        marginBottom:'5px',
        marginLeft:'30px',
        alignSelf:'start',
        marginTop:'10px'
    },
    offerCardDivider:{
        height:'2px',
        backgroundColor:'black',
        opacity:0.3,
        marginTop:'5px',
        marginBottom:'5px'
    },
    useCodeText: {
        textAlign: 'center',
        fontWeight:'bolder',
        marginBottom:'2px'
    },
    couponBox: {
        position: 'absolute',
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        height: '35px',
        alignItems: 'center'
    },
    coponCode: {
        fontSize: '15px',
        fontFamily: 'Nunito',
        color:'white',
        fontWeight:'bolder',
        marginRight:'20px'
    },
    validityText: {
        textAlign: 'center',
        fontWeight:'bolder'
    },
    smallText: {fontSize:'10px',fontWeight:'bolder'}
}
