export const styles = {
    offerCard : {
        width:'250px',
        display: 'flex',
        flexDirection:'column',
        justifyContent:'center',
        textAlign:'center',
        padding:'10px',
        position:'absolute'
    },
    youWonText : {
        color: 'white',
        fontFamily: 'Nunito',
        fontWeight: 'bold',
        fontSize: '12px'
    },
    offerText:{
        color: 'white',
        fontSize: '26px',
        fontWeight: 'bolder',
        marginBottom: '5px',
        marginTop: '20px',
        fontFamily: 'Nunito',
    },
    nextVisitText:{
        fontFamily: 'Nunito',
        color:'white',
        marginBottom:'5px',
        marginLeft:'30px',
        alignSelf:'start',
        marginTop:'10px'
    },
    offerCardDivider:{
        height:'2px',
        backgroundColor:'black',
        opacity:0.3,
        marginTop:'5px',
        marginBottom:'5px'
    },
    useCodeText: {
        textAlign: 'center',
        fontWeight:'bolder',
        marginBottom:'2px'
    },
    couponBox: {
        position: 'absolute',
        display: 'flex',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        width: '250px',
        height: '35px'
    },
    coponCode: {
        fontSize: '20px',
        fontFamily: 'Nunito',
        color:'white'
    },
    validityText: {
        textAlign: 'center',
        fontWeight:'bolder'
    }
}
