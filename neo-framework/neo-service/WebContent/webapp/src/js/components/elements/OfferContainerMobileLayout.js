import React from "react";
import {connect} from "react-redux";
import {styles} from './elementStyles/OfferContainerMobileStyle'

export default class OfferContainerMobileLayout extends React.Component{


    render() {
        let data = this.props.data;
        let color = '#11a01d';
        let partnerImage="../../../img/chaayos_offer_card_mobile.webp";
        let couponBackground="../../../img/chaayos_coupon_card_mobile.webp";
        let nextVisitText="on your next visit at";
        if(data.channelPartnerId===null || data.channelPartnerId === 1){
            color = '#11a01d';
            partnerImage="../../../img/chaayos_offer_card_mobile.webp"
            couponBackground="../../../img/chaayos_coupon_card_mobile.webp"
            nextVisitText="on your next visit at";
        }else if(data.channelPartnerId === 3){
            color = '#e22a4a';
            partnerImage="../../../img/zomato_offer_card_mobile.webp"
            couponBackground="../../../img/zomato_coupon_card_mobile.webp"
            nextVisitText="on your next order on";
        }
        return <div style={{textAlign:'center',flex:1}}>
            <div style={{position:'relative'}}>
                <div style={{position:'absolute',width:'100%'}}>
                    <p style={styles.offerText}>{data.offerDesc} *</p>
                </div>
                <img src={partnerImage} width={window.innerWidth*this.props.widthPercent} draggable={false}/>
            </div>
            {data.offerCouponCode !== "LOYAL_TEA" ?
                <div>
                    <p style={{...styles.smallText,color:color}}>USE CODE</p>
                    <div style={{position:'relative'}}>
                        <div style={styles.couponBox}>
                            <p style={styles.coponCode}>{data.offerCouponCode}</p>
                            <img  src="../../../img/copy_code.svg" height={12} style={{alignSelf:'center'}} draggable={false} onClick={()=> this.props.onCodeCopy(data.offerCouponCode)}/>
                        </div>
                        <img src={couponBackground} width={window.innerWidth*this.props.widthPercent} height={'35px'} draggable={false}/>
                    </div>
                </div>:<p style={{...styles.smallText,color:color}}>No coupon needed</p>}
            <p style={{...styles.smallText,color:color}}>Valid from {data.validityFrom} </p>
            <p style={{...styles.smallText,color:color}}>Valid till {data.validityTill} </p>
        </div>
    }
}
