import React from "react";
import ReactDOM from "react-dom";
import {browserHistory, Redirect, Route, Router} from "react-router";
import IndexRoute from "react-router/lib/IndexRoute";
import {Provider} from "react-redux";
import * as firebase from "firebase";
//import Promise from 'promise-polyfill';
import customPolyFills from "./CustomPolyFills";
import store from "./store";
import MainRoutingLayout from "./components/MainRoutingLayout";
import HomeLayout from "./components/HomeLayout";
import OutletMenuLayout from "./components/OutletMenuLayout";
import CartLayout from "./components/CartLayout";
import LoginLayout from "./components/LoginLayout";
import AddressesLayout from "./components/AddressesLayout";
import NewAddressLayout from "./components/NewAddressLayout";
import PaymentModesLayout from "./components/PaymentModesLayout";
import OrdersLayout from "./components/OrdersLayout";
import OrderSummaryLayout from "./components/OrderSummaryLayout";
import TermsLayout from "./components/TermsLayout";
import PrivacyLayout from "./components/PrivacyLayout";
import ProfileLayout from "./components/ProfileLayout";
import AboutLayout from "./components/AboutLayout";
import ContactLayout from "./components/ContactLayout";
import NotFoundLayout from "./components/NotFoundLayout";
import PaymentProcessLayout from "./components/PaymentProcessLayout";
import MobileLocationLayout from "./components/mobile/MobileLocationLayout";
import DineinRoutingLayout from "./components/DineinRoutingLayout";
import TakeawayRoutingLayout from "./components/TakeawayRoutingLayout";
import StoreLocatorLayout from "./components/StoreLocatorLayout";
import OauthLayout from "./components/OauthLayout";
import OutletDeliveryRedirectLayout from "./components/OutletDeliveryRedirectLayout";
import DeliveryRoutingLayout from "./components/DeliveryRoutingLayout";
import GiveAwayRoutingLayout from "./components/GiveAwayRoutingLayout";
import MediumRoutingLayout from "./components/MediumRoutingLayout";
import ShareAppLayout from "./components/ShareAppLayout";
import SignUpDeliveryOfferLayout from "./components/SignUpDeliveryOfferLayout";
import MyOfferLayout from "./components/MyOfferLayout";
import MembershipLayout from "./components/MembershipLayout";
import SlotMachineLayout from "./components/SlotMachineLayout";
import GamifiedOffer from "./components/GamifiedOffer";

const app = document.getElementById('app');
const env = "DEV";

(function () {
    // promise polyfill
    if (!window.Promise) {
        window.Promise = Promise;
    }
    customPolyFills.initiatePolyFills();
    document.getElementsByClassName("splash")[0] != null ? document.getElementsByClassName("splash")[0].className += " inactive" : null;
    setTimeout(function () {
        if (document.getElementsByClassName("splash")[0] != null) {
            document.getElementsByClassName("splash")[0].parentNode.removeChild(document.getElementsByClassName("splash")[0]);
        }
    }, 2500);
    //dev config
    var devConfig = {
        apiKey: "AIzaSyDaeMgQZr_HDfYY1SdMXUOptBGshrh-12E",
        authDomain: "chaayos-webapp.firebaseapp.com",
        databaseURL: "https://chaayos-webapp.firebaseio.com",
        storageBucket: "chaayos-webapp.appspot.com",
        messagingSenderId: "1022795285764"
    };
    //prod config
    var prodConfig = {
        apiKey: "AIzaSyAUuPwz1fjjdFzVgkDrfb14zKm33w_8LuY",
        authDomain: "chaayos-prod-web-app.firebaseapp.com",
        databaseURL: "https://chaayos-prod-web-app.firebaseio.com",
        projectId: "chaayos-prod-web-app",
        storageBucket: "chaayos-prod-web-app.appspot.com",
        messagingSenderId: "************"
    };
    if(env === "PROD"){
        firebase.initializeApp(prodConfig);
    }else {
        firebase.initializeApp(devConfig);
    }
})();

ReactDOM.render(
    <Provider store={store}>
        <Router history={browserHistory}>
            <Route path="/" component={MainRoutingLayout}>
                <IndexRoute component={HomeLayout}/>
                <Route path="search" component={MobileLocationLayout}/>
                <Route path="menu" component={OutletMenuLayout}/>
                <Route path="cart" component={CartLayout}/>
                <Route path="addresses" component={AddressesLayout}/>
                <Route path="newAddress" component={NewAddressLayout}/>
                <Route path="paymentModes" component={PaymentModesLayout}/>
                <Route path="orders" component={OrdersLayout}/>
                <Route path="freechai" component={SignUpDeliveryOfferLayout}/>
                <Route path="orderDetail" component={OrderSummaryLayout}/>
                <Route path="login" component={LoginLayout}/>
                <Route path="terms" component={TermsLayout}/>
                <Route path="privacy" component={PrivacyLayout}/>
                <Route path="account" component={ProfileLayout}/>
                <Route path="about" component={AboutLayout}/>
                <Route path="contact" component={ContactLayout}/>
                <Route path="shareapp" component={ShareAppLayout}/>
                <Route path="payProcess" component={PaymentProcessLayout}/>
                <Route path="myoffer" component={MyOfferLayout}/>
                <Route path="deliveryRedirect/:unitId" component={OutletDeliveryRedirectLayout}/>
                <Route path="dinein/:unitId" component={DineinRoutingLayout}/>
                <Route path="takeaway/:unitId" component={TakeawayRoutingLayout}/>
                <Route path="takeaway/city/:city" component={TakeawayRoutingLayout}/>
                <Route path="delivery/:city" component={DeliveryRoutingLayout}/>
                <Route path="stores/:city" component={StoreLocatorLayout}/>
                <Route path="giveback" component={GiveAwayRoutingLayout}/>
                <Route path="blog" component={MediumRoutingLayout}/>
                <Route path="membership" component={MembershipLayout}/>
                <Route path="game/slot/:flow" component={SlotMachineLayout}/>
                <Route path="membership/:type" component={MembershipLayout}/>
                <Route path="game/ninja/:flow" component={GamifiedOffer}/>
                <Route path="ninjaRef/:refCode" component={GamifiedOffer}/>
                <Redirect from='/stores' to='/stores/Delhi'/>
                <Redirect from='/delivery' to='/delivery/new_delhi'/>
                <Route path="oauth" component={OauthLayout}/>
                <Route path="*" component={NotFoundLayout}/>
            </Route>
        </Router>
    </Provider>,
    app
);
