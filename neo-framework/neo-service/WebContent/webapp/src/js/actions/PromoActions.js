import appUtil from "../AppUtil";
import apis from "../APIs";
import axios from "axios";
import { browserHistory } from "react-router";
import trackUtils from "../utils/TrackUtils";
import StorageUtils from "../utils/StorageUtils";
import * as UtilityActions from "./UtilityActions";

export function checkPromoShow() {
    return dispatch => {
        var promoData = StorageUtils.getPromoData();
        if(promoData==null || typeof promoData == 'undefined'){
            var sessionKey = StorageUtils.getAuthDetail()!=null?StorageUtils.getAuthDetail().sessionKey:null;
            if(sessionKey==null || typeof sessionKey == 'undefined' || sessionKey.trim().length==0){
                //user not logged in
                dispatch(showPromo(true));
            }else{
                //check user orders
                var customerId = StorageUtils.getCustomerId();
                if(customerId!=null){
                    axios({
                        method: "POST",
                        url: apis.getUrls().order.isFirstOrder,
                        data: JSON.stringify(customerId),
                        headers: {'Content-Type': 'application/json'}
                    }).then(function (response) {
                        if(response.data==true){
                            dispatch(showPromo(true));
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                }
            }
        }
    }
}

export function showPromo(show) {
    return dispatch =>{
        dispatch({type:"SET_SHOW_PROMO", payload:show});
        if(show){
            StorageUtils.setPromoData("1",1);
            try{trackUtils.trackShowPromoModal()}catch(e){console.log(e)}
        }else{
            try{trackUtils.trackRejectedPromoOffer()}catch(e){console.log(e)}
        }
    }
}

export function customerAccepted(accepted, isLoginPromo) {
    return dispatch =>{
        dispatch({type:"SET_CUSTOMER_ACCEPTED_PROMO", payload:accepted});
        dispatch(showPromo(false));
        var session = StorageUtils.getAuthDetail()!=null?StorageUtils.getAuthDetail().sessionKey:null;
        if(isLoginPromo && session == null){
            dispatch(UtilityActions.showPopup("Offer added to cart please apply while checking out. Please login to continue."));
            browserHistory.push("/login");
        }else{
            dispatch(UtilityActions.showPopup("Offer added to cart please apply while checking out."));
        }
        try{trackUtils.trackAcceptedPromoOffer()}catch(e){console.log(e)}
    }
}