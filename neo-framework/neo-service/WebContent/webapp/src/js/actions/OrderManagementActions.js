import {browserHistory} from "react-router";
import axios from "axios";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import * as UtilityActions from "./UtilityActions";
import * as PaymentActions from "./PaymentActions";
import * as CartManagementActions from "./CartManagementActions";
import * as CampaignManagementActions from "./CampaignManagementActions";
import * as CustomerActions from "./CustomerActions";
import trackUtils from "../utils/TrackUtils";

export function getOrderDetail(orderDetail, currentOrderId, trackSuccess, timeoutsArray) {
    return dispatch => {
        if (currentOrderId != null) {
            if (!appUtil.checkEmpty(orderDetail) && ((currentOrderId.type == "GEN" && orderDetail.generateOrderId == currentOrderId.id) ||
                (currentOrderId.type == "WEB" && orderDetail.externalOrderId == currentOrderId.id))) {
                dispatch({type: "GET_ORDER_SUMMARY_FULFILLED", payload: {data: orderDetail}});
            } else {
                var url;
                if (currentOrderId.type == "WEB") {
                    url = apis.getUrls().order.searchByWebId;
                } else {
                    url = apis.getUrls().order.searchByOrderId;
                }
                dispatch({type: "GET_ORDER_SUMMARY_PENDING"});
                axios({
                    method: "POST",
                    url: url,
                    data: JSON.stringify(currentOrderId.id),
                    headers: {'Content-Type': 'application/json'}
                }).then((response) => {
                    dispatch(getMembershipProductIds('26254', ()=>{
                    }));
                    dispatch({type: "GET_ORDER_SUMMARY_FULFILLED", payload: response});
                    if (!appUtil.isOrderCompleted(response.data.status, response.data.source)) {
                        const x = setTimeout(() => {
                            dispatch(getOrderStatus(currentOrderId.id, response.data.source, timeoutsArray));
                        }, 300000);
                        timeoutsArray.push(x);
                    }
                    if (trackSuccess == true) {
                        var d = StorageUtils.getLocalityMetadata();
                        try {
                            trackUtils.trackSuccessOrder({
                                criteria: d.criteria,
                                city: d.city,
                                locality: !appUtil.checkEmpty(d.locality) ? d.locality.label : "",
                                outlet: !appUtil.checkEmpty(d.outlet) ? d.outlet.label : "",
                                cart: response.data
                            });
                        } catch (e) {
                        }

                        browserHistory.push("/orderDetail");
                    }
                }).catch((error) => {
                    dispatch({type: "GET_ORDER_SUMMARY_REJECTED", payload: error});
                })
            }
        } else {
            browserHistory.push("/orders");
        }
    }
}

export function getOrderStatus(orderId, source, timeoutsArray) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().order.searchStatusByOrderId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {
            if (response.data) {
                dispatch({type: "ORDER_STATUS_UPDATE", payload: response.data});
                if (!appUtil.isOrderCompleted(response.data, source)) {
                    const x = setTimeout(() => {
                        dispatch(getOrderStatus(orderId, source, timeoutsArray));
                    }, 300000);
                    timeoutsArray.push(x);
                }
            } else {
                dispatch(UtilityActions.showPopup("Error in updating order status. Please reload the page."));
            }
        }).catch(e => {
            dispatch(UtilityActions.showPopup("Error in updating order status."));
            const x = setTimeout(() => {
                dispatch(getOrderStatus(orderId, source, timeoutsArray));
            }, 300000);
            timeoutsArray.push(x);
        })
    }
}

export function removeStatusTimeouts(timeoutsArray) {
    return dispatch => {
        timeoutsArray.map(timeout => {
            clearTimeout(timeout);
        });
        dispatch(setStatusTimeoutsArray([]));
    }
}

export function setStatusTimeoutsArray(data) {
    return dispatch => {
        dispatch({type: "SET_ORDER_STATUS_TIMEOUTS", payload: data});
    }
}

export function getCustomerOrders(page, callback) {
    return dispatch => {
        var customerId = StorageUtils.getCustomerId();
        if (customerId != null) {
            dispatch({type: "GET_CUSTOMER_ORDERS_PENDING"});
            axios({
                method: "POST",
                url: apis.getUrls().order.searchCustomerOrders,
                data: JSON.stringify({customerId: StorageUtils.getCustomerId(), page: page, limit: 5}),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                dispatch({type: "GET_CUSTOMER_ORDERS_FULFILLED", payload: response});
                dispatch(getMembershipProductIds('26254', callback));
            }).catch(function (error) {
                dispatch({type: "GET_CUSTOMER_ORDERS_REJECTED", payload: error});
            })
        } else {
            //send to login page and show message session expired
            browserHistory.push("/login");
            dispatch(UtilityActions.showPopup("Session expired. Please login again!", 'info'));
        }
    }
}

export function resetPage() {
    return dispatch => {
        dispatch({type: "RESET_PAGE"});
    }
}

export function orderCheckout(mode, timeoutsArray) {

    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        cart.orderDetail.settlements = [];
        cart.orderDetail.settlements.push({
            mode: mode,
            amount: cart.orderDetail.transactionDetail.paidAmount
        });
        if (cart.orderDetail.externalOrderId == null) {
            cart.orderDetail.externalOrderId = "WO433665635";
        }
        cart.orderType = "NEO_CASH";
        dispatch(PaymentActions.setPaymentData({
            paymentMessage: "Please wait while we place your order.",
            paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
        }));
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkout,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {

            if (response.data != null) {
                if (response.data.errorCode != null) {
                    dispatch(handleOrderCreationException(response.data));
                } else {
                    var currentOrderId = {id: response.data, type: "GEN"};
                    dispatch(setCurrentOrderId(currentOrderId));
                    dispatch(getOrderDetail(null, currentOrderId, true, timeoutsArray));
                    dispatch(PaymentActions.setPaymentInitiated(false));
                    dispatch(stampDevice(function (response) {
                        const cartDetail = response.data.cartDetail;
                        response.data.cartDetail = null;
                        StorageUtils.setAuthDetail(response.data);
                        dispatch(CartManagementActions.updateCart(cartDetail));
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
                        dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                    }, function (error) {
                        browserHistory.push("/orderDetail");
                        dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                    }));
                    StorageUtils.removeCampaignDetails();
                    dispatch(CampaignManagementActions.setCampaignDetail(null));
                    //dispatch(PromoActions.customerAccepted(false));
                }
            } else {

                slackCartId(cart.cartId);
                dispatch(PaymentActions.setPaymentData({
                    paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.1",
                    paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
                }));
            }
        }).catch(function (error) {
            dispatch(handleOrderCreationException(error.response.data, cart.cartId, null));
        });
    }
}

export function orderCheckoutByWOId(orderId, timeoutsArray) {
    return dispatch => {
        dispatch(PaymentActions.setPaymentData({
            paymentMessage: "Payment success. Please wait while we place your order.",
            paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
        }));
        axios({
            method: "POST",
            url: apis.getUrls().cart.checkoutWOId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {

            if (response.data != null) {
                var currentOrderId = {id: response.data, type: "GEN"};
                dispatch(setCurrentOrderId(currentOrderId));
                dispatch(getOrderDetail(null, currentOrderId, true, timeoutsArray));
                dispatch(PaymentActions.setPaymentInitiated(false));
                dispatch(stampDevice(function (response) {
                    const cartDetail = response.data.cartDetail;
                    response.data.cartDetail = null;
                    StorageUtils.setAuthDetail(response.data);
                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch(CustomerActions.getAvailableGiftCardInfo());
                    dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
                    dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                }, function (error) {
                    browserHistory.push("/orderDetail");
                    dispatch(UtilityActions.showPopup("Order placed successfully!", 'info'));
                }));

                const cart = StorageUtils.getCartDetail();
                dispatch(trackMembershipClaimed(cart))
                StorageUtils.setMembershipFlag(false);
                StorageUtils.removeCampaignDetails();
                dispatch(CampaignManagementActions.setCampaignDetail(null));
            } else {
                slackWOId(orderId);
                dispatch(PaymentActions.setPaymentData({
                    paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.",
                    paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
                }));
            }
        }).catch(function (error) {
            if (error.response != null) {
                dispatch(handleOrderCreationException(error.response.data, null, orderId));
            } else {
                error.response = {data: {errorCode: 0}};
                dispatch(handleOrderCreationException(error.response.data, null, orderId));
            }
        });
    }
}

export function slackCartId(cartId) {
    axios({
        method: "POST",
        url: apis.getUrls().order.slackCartId,
        data: JSON.stringify(cartId),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {

    });
}

export function slackWOId(WOId) {
    axios({
        method: "POST",
        url: apis.getUrls().order.slackWOId,
        data: JSON.stringify(WOId),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {

    });
}

export function reOrder(screen, orderId, isInterState, skipDeliveryPackaging) {
    return dispatch => {
        trackUtils.trackReorderClicked(screen);
        //var order = StorageUtils.getCurrentOrderId();
        var unit = StorageUtils.getUnitDetails();
        axios({
            method: "POST",
            url: apis.getUrls().cart.createCartFromOrderId,
            data: JSON.stringify(orderId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                var cart = response.data;
                var orderItems = response.data.orderDetail.orders;
                cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
                cart.orderDetail.orders.map(function (item) {
                    if (item.productId != 1043 && item.productId != 1044) {
                        orderItems.push({...item});
                    }
                });
                cart.orderDetail.orders = orderItems;
                dispatch(CartManagementActions.updateCart(cart));
                dispatch(CartManagementActions.removeCoupon(isInterState));
                cart = StorageUtils.getCartDetail();
                if (StorageUtils.getLocalityMetadata().criteria != "DINE_IN") {
                    dispatch(CartManagementActions.addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
                }
                cart = appUtil.calculateTaxes(cart, isInterState);
                dispatch(CartManagementActions.updateCart(cart));
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup("Your cart is ready." + (unit == null ? " Please select your location." : ""), "info", 3000));
                unit != null ? browserHistory.push("/cart") : browserHistory.push("/");
            } else {
                dispatch(UtilityActions.showPopup("Error in creating the order. Please try again!", "error"));
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch((error) => {
            dispatch(UtilityActions.showPopup("Error in creating the order. Please try again!", "error"));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function stampDevice(success, errorCall) {
    return dispatch => {
        var authDetail = StorageUtils.getAuthDetail();
        if (!appUtil.checkEmpty(authDetail) && authDetail.deviceKey != null) {
            axios({
                method: "POST",
                url: apis.getUrls().stamping.stampDevice,
                data: JSON.stringify(authDetail),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (typeof success == 'function') {
                    success(response);
                } else {
                    window.location.reload();
                }
            }).catch(function (error) {
                if (typeof errorCall == 'function') {
                    errorCall(error);
                } else {
                    window.location.reload();
                }
            });
        } else {
            this.props.dispatch(UtilityActions.createDevice());
        }
    }
}

export function setCurrentOrderId(order) {
    return dispatch => {
        dispatch({type: "SET_CURRENT_ORDER_ID", payload: order});
        StorageUtils.setCurrentOrderId(order);
    }
}

export function handleOrderCreationException(response, cartId, woId) {
    return dispatch => {
        if (response.errorCode == 711) {
            stampDevice(function (response) {
                const cartDetail = response.data.cartDetail;
                response.data.cartDetail = null;
                StorageUtils.setAuthDetail(response.data);
                dispatch(CartManagementActions.updateCart(cartDetail));
                dispatch({type: "SET_LOGIN_REDIRECT", payload: null});
            }, function (error) {
                //window.location.reload();
            });
            dispatch(UtilityActions.showPopup("Order already placed. Check order history!", 'info'));
            browserHistory.push("/orders");
        } else if ([701, 702, 711, 715, 708].indexOf(response.errorCode) >= 0) {
            dispatch(UtilityActions.showPrompt("Error Placing order. Please call at 1800-120-2424 for support.", function () {
                window.location.reload();
            }, function () {
                window.location.reload();
            }));
        } else {
            if (cartId != null) {
                slackCartId(cartId);
            }
            if (woId != null) {
                slackWOId(woId);
            }
            dispatch(PaymentActions.setPaymentData({
                paymentMessage: "Error placing order. Please call at 1800-120-2424 for support.",
                paymentStatus: "SUCCESS", showLoader: true, failureMessage: null
            }));
        }
    }
}

export function getMembershipProductIds(unitId, callback) {
    return dispatch => {
        axios({
            method: "GET",
            url: apis.getUrls().neoCache.getMembershipProductIds + "?unitId=" + unitId,
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "SAVE_MEMBERSHIP_IDS", payload: response});
            dispatch(getMembershipProductsData('26254', callback));
        }).catch(function (error) {
            console.log(error);
        });
    }
}

export function getMembershipProductsData(unitId, callback) {
    return dispatch => {
        axios({
            method: "GET",
            url: apis.getUrls().neoCache.getMembershipProductsData + "?unitId=" + unitId,
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "SAVE_MEMBERSHIP_DATA", payload: response});
            callback();
        }).catch(function (error) {
            console.log(error);
        });
    }
}

export function trackMembershipClaimed(cart) {
    return dispatch => {
        axios({
            method: "GET",
            url: apis.getUrls().neoCache.getMembershipProductIds + "?unitId=26254",
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(cart!=null && response.data.includes(cart.orderDetail.orders[0].productId)){
                trackUtils.trackMembershipClaimed({});
            }
        }).catch(function (error) {
            console.log(error);
        });
    }
}
