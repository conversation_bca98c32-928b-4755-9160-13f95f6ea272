/**
 * Created by Chaayos on 01-12-2016.
 */
import axios from "axios";
import appUtil from "../AppUtil";
import AppUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import {browserHistory} from "react-router";
import * as UtilityActions from "./UtilityActions";
import * as CartManagementActions from "./CartManagementActions";
import {setCustomerInfo} from "./MembershipActions";
import trackUtils from "../utils/TrackUtils";
import {journeyStage, myOfferStage, recordJourney, updateSignUpOfferStage} from "./MyOfferActions";
import * as SlotMachineAction from './SlotMachineActions';

export function lookupCustomer(contact, deviceType) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        dispatch({type: "LOOKUP_CUSTOMER_PENDING", payload: contact});
        dispatch({type: "SET_CONTACT_NUMBER", payload: contact});
        var customer = StorageUtils.getCustomerDetail();
        customer = customer == null ? {} : customer;
        StorageUtils.setCustomerDetail({...customer, contact: contact});
        axios({
            method: "POST",
            url: apis.getUrls().customer.lookup,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(response.data == -5){
                dispatch(UtilityActions.showPopup("Maximum Otp limit reached","error",3000));
            }
            else if(response.data == -2){
                dispatch(UtilityActions.showPopup("You are an internal customer and hence not authorised to use Chaayos web app."));
            }else if(response.data == -1){
                dispatch(updateSignUpOfferStage(myOfferStage.getName));
                dispatch(recordJourney(journeyStage.otpPage,deviceType,contact));
                dispatch(SlotMachineAction.setShowNameField(true));
                dispatch(SlotMachineAction.setShowContactField(false));
                dispatch(SlotMachineAction.setShowOtpField(true));
            }else{
                dispatch(recordJourney(journeyStage.otpPage,deviceType,contact));
                dispatch(updateSignUpOfferStage(myOfferStage.getOTP));
                dispatch(SlotMachineAction.setShowOtpField(true));
                dispatch(SlotMachineAction.setShowContactField(false));
            }
            dispatch({type: "LOOKUP_CUSTOMER_FULFILLED", payload: response.data});
            dispatch(UtilityActions.hideFullPageLoader());
            trackUtils.trackCustomerLookup({contact:contact,status:"SUCCESS",isRegistered:response.data!=-1});
        }).catch(function (error) {
            dispatch({type: "LOOKUP_CUSTOMER_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
            trackUtils.trackCustomerLookup({contact:contact,status:"FAILED",isRegistered:false});
        });
    }
}

export function loginCustomer(name, email, otp, getName, getEmail, contact, url) {
    return (dispatch,getState) => {
        var data = StorageUtils.getAuthDetail();
        if (appUtil.checkEmpty(data)) {
            browserHistory.push("/");
            dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
        }else{
            dispatch(UtilityActions.showFullPageLoader(""));
            dispatch({type: "LOGIN_CUSTOMER_PENDING", payload: contact});
            const refQueryParam = appUtil.getParameterByName('ref');
            if (refQueryParam === 'shareApp') {
                url = (url != null) ? url : "/shareapp";
            } else {
                url = (url != null) ? url : "/menu";
            }
            var reqObj = {otp: otp, contact: contact, deviceKey: data.deviceKey, update: false};
            if (getName) {
                reqObj.name = name;
                reqObj.update = true;
            }
            if (getEmail) {
                reqObj.email = email;
                reqObj.update = true;
            }
            axios({
                method: "POST",
                url: apis.getUrls().customer.login,
                data: JSON.stringify(reqObj),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response.data != null) {
                    //console.log("customer login info:: ", response.data);
                    const {membershipIds} = getState().membershipReducer;
                    var cart = response.data.device.cartDetail;
                    response.data.device.cartDetail = null;
                    StorageUtils.setAuthDetail(response.data.device);
                    StorageUtils.getCustomerContact() != null ? trackUtils.trackUser(StorageUtils.getCustomerContact()) : null;
                    var cartDetail = StorageUtils.getCartDetail();
                    if (appUtil.checkEmpty(cartDetail)) {
                        cartDetail = cart;
                    } else {
                        cartDetail.orderDetail.orders = cart.orderDetail.orders;
                        cartDetail.orderDetail.webCustomerId = cart.customerId;
                        cartDetail.cartId = cart.cartId;
                        cartDetail.deviceId = cart.deviceId;
                        cartDetail.customerId = cart.customerId;
                        cartDetail.orderDetail.customerName = response.data.name;
                        cartDetail.sessionId = cart.sessionId;
                    }
                    //if(!appUtil.checkEmpty(cart))
                    //cartDetail = appUtil.calculateTaxes(cartDetail);
                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch({type: "SET_DEVICE_KEY", payload: response.data.device.deviceKey});
                    dispatch({type: "LOGIN_CUSTOMER_FULFILLED", payload: response.data.device.sessionKey});
                    dispatch(UtilityActions.hideFullPageLoader());
                    var customer = StorageUtils.getCustomerDetail();
                    if (customer == null) {
                        customer = {}
                    }
                    customer = {
                        ...customer,
                        contact: response.data.contact,
                        name: response.data.name,
                        email: response.data.email,
                        customerId: response.data.customerId,
                        loyalty: response.data.loyalty,
                        optOutOfFaceIt: response.data.optOutOfFaceIt,
                        optOutTime: response.data.optOutTime
                    };
                    dispatch({type: "SET_CUSTOMER", payload: customer});
                    StorageUtils.setCustomerDetail(customer);
                    StorageUtils.getCustomerDetail() != null ? trackUtils.trackUserData(StorageUtils.getCustomerDetail()) : null;
                    trackUtils.trackLogin({
                        contact: contact,
                        name: response.data.name,
                        email: response.data.email,
                        otp: otp,
                        targetUrl: url,
                        status: "SUCCESS"
                    });
                    //console.log("Fetching available gift card info");
                    dispatch(getAvailableGiftCardInfo());
                    if (!AppUtil.checkEmpty(membershipIds.data)) {
                        if (membershipIds.data.includes(cart.orderDetail.orders[0].productId)) {
                            dispatch(setCustomerInfo(response.data.contact, (data) => {
                                if (AppUtil.checkEmpty(data.subscriptionInfoDetail)) {
                                    browserHistory.push(url);
                                } else {
                                    if(data.subscriptionInfoDetail.eligible ==true){
                                        browserHistory.push(url);
                                    }
                                    else {
                                        dispatch(UtilityActions.showPopup("You already have a subscription ", "info", 5000));
                                        dispatch(CartManagementActions.updateCart(null));
                                        window.location.replace("https://cafes.chaayos.com/menu");
                                    }
                                }
                            }));
                        }
                    } else {
                        browserHistory.push(url);
                    }
                } else {
                    dispatch(UtilityActions.showPopup("Error verifying OTP. Please enter correct OTP", "error", 2000));
                    trackUtils.trackLogin({
                        contact: contact,
                        name: name,
                        email: email,
                        otp: otp,
                        targetUrl: url,
                        status: "FAILED"
                    });
                }
            }).catch(function (error) {
                dispatch({type: "LOGIN_CUSTOMER_REJECTED", payload: error});
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup("Error in OTP verification. Please try again!", "error", 2000));
                trackUtils.trackLogin({
                    contact: contact,
                    name: name,
                    email: email,
                    otp: otp,
                    targetUrl: url,
                    status: "FAILED"
                });
            });
        }
    }
}

export function signUpCustomer(name, email, otp, contact, url, isInterState) {
    return dispatch => {
        var data = StorageUtils.getAuthDetail();
        if (appUtil.checkEmpty(data)) {
            browserHistory.push("/");
            dispatch(UtilityActions.showPopup("Something went wrong. Please try again."));
        }else{
            dispatch(UtilityActions.showFullPageLoader(""));
            dispatch({type: "SIGNUP_CUSTOMER_PENDING", payload: contact});
            axios({
                method: "POST",
                url: apis.getUrls().customer.signUp,
                data: JSON.stringify({
                    name: name,
                    contact: contact,
                    otp: otp,
                    email: email,
                    deviceKey: data.deviceKey
                }),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if(response.data!=null){
                    var cart = response.data.device.cartDetail;
                    response.data.device.cartDetail = null;
                    StorageUtils.setAuthDetail(response.data.device);
                    StorageUtils.getCustomerContact()!=null?trackUtils.trackUser(StorageUtils.getCustomerContact()):null;
                    var cartDetail = StorageUtils.getCartDetail();
                    if (appUtil.checkEmpty(cartDetail)) {
                        cartDetail = cart;
                    } else {
                        cartDetail.orderDetail.orders = cart.orderDetail.orders;
                        cartDetail.orderDetail.webCustomerId = cart.customerId;
                        cartDetail.cartId = cart.cartId;
                        cartDetail.deviceId = cart.deviceId;
                        cartDetail.customerId = cart.customerId;
                        cartDetail.orderDetail.customerName = response.data.name;
                        cartDetail.sessionId = cart.sessionId;
                    }
                    cartDetail.orderDetail.transactionDetail==null?cartDetail.orderDetail.transactionDetail=appUtil.getNewTransactionObject():null;
                    cartDetail = appUtil.calculateTaxes(cartDetail, isInterState);
                    dispatch(CartManagementActions.updateCart(cartDetail));
                    dispatch({type: "SIGNUP_CUSTOMER_FULFILLED", payload: response.data.device.sessionKey});
                    dispatch(UtilityActions.hideFullPageLoader());
                    var customer = StorageUtils.getCustomerDetail();
                    if (customer == null) {
                        customer = {}
                    }
                    customer = {
                        ...customer,
                        contact: response.data.contact,
                        name: response.data.name,
                        email: response.data.email,
                        customerId: response.data.customerId,
                        loyalty: response.data.loyalty,
                        optOutOfFaceIt: response.data.optOutOfFaceIt,
                        optOutTime: response.data.optOutTime
                    };
                    dispatch({type: "SET_CUSTOMER", payload: customer});
                    StorageUtils.setCustomerDetail(customer);
                    StorageUtils.getCustomerDetail()!=null?trackUtils.trackUserData(StorageUtils.getCustomerDetail()):null;
                    trackUtils.trackSignup({contact:contact,name:name,email:email,otp:otp,targetUrl:url,status:"SUCCESS"});
                    const refQueryParam = appUtil.getParameterByName('ref');
                    if (refQueryParam === 'shareApp') {
                        url = (url != null) ? url : "/shareapp";
                    } else {
                        url = (url != null) ? url : "/menu";
                    }
                    browserHistory.push(url);
                } else {
                    dispatch(UtilityActions.showPopup("Error verifying OTP. Please enter correct OTP", "error", 2000));
                    trackUtils.trackSignup({
                        contact: contact,
                        name: name,
                        email: email,
                        otp: otp,
                        targetUrl: url,
                        status: "FAILED"
                    });
                }
            }).catch(function (error) {
                dispatch({type: "SIGNUP_CUSTOMER_REJECTED", payload: error});
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup("Error in OTP verification. Please try again!", "error", 2000));
                trackUtils.trackSignup({
                    contact: contact,
                    name: name,
                    email: email,
                    otp: otp,
                    targetUrl: url,
                    status: "FAILED"
                });
            });
        }
    }
}

export function getCustomerAddresses(customerId) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        dispatch({type: "CUSTOMER_ADDRESSES_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.addresses,
            data: JSON.stringify(customerId),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "CUSTOMER_ADDRESSES_FULFILLED", payload: response.data});
            trackUtils.trackAddressCount(response.data.length);
            if (appUtil.checkEmpty(response.data)) {
                browserHistory.push("/newAddress");
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch({type: "CUSTOMER_ADDRESSES_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function selectAddress(id) {
    return dispatch => {
        dispatch({type: "SELECT_ADDRESS", payload: id});
        trackUtils.trackAddressSelected("EXISTING");
    }
}

export function removeSelectedAddress() {
    return dispatch => {
        dispatch({type: "REMOVE_SELECTED_ADDRESS"});
    }
}

export function addAddress(landmark, line1, locality, city, type, redirectTo) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().customer.addAddress,
            data: JSON.stringify({
                id: StorageUtils.getCustomerId(),
                address: {
                    line1: line1,
                    locality: locality,
                    city: city.city,
                    landmark: landmark,
                    state: "Haryana",
                    country: "India",
                    addressType: type
                },
                cartId: StorageUtils.getCartDetail()!=null?StorageUtils.getCartDetail().cartId:null
            }),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "ADD_ADDRESS", payload: response.data});
            var cart = StorageUtils.getCartDetail();
            cart.orderDetail.deliveryAddress = response.data.id;
            dispatch(CartManagementActions.updateCart(cart));
            trackUtils.trackAddressAdd({status:"SUCCESS"});
            if (redirectTo != null) {
                trackUtils.trackAddressSelected("NEW");
                browserHistory.push("/paymentModes");
            } else {
                browserHistory.push("/addresses");
            }
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Address added successfully!", "info"));
        }).catch(function (error) {
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error adding address. Try again!", "info"));
            trackUtils.trackAddressAdd({status:"FAILED"});
        })
    }
}

export function resetLogin() {
    return dispatch => {
        dispatch({type: "RESET_LOGIN"});
    }
}

export function resendVerification(contact) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().customer.resendVerification,
            data: JSON.stringify(contact),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(appUtil.checkEmpty(response.data)){
                if(response.data.success==true){
                    dispatch(UtilityActions.showPopup("OTP resent successfully. Please check your phone.","info"));
                    document.getElementById("otpInput").focus();
                    trackUtils.trackOTPResent({contact:contact,status:"SUCCESS"});
                }else{
                    dispatch(UtilityActions.showPopup("You have tried too many OTP attempts. Please try again in "+response.data.sec+"seconds.","info"));
                    dispatch(initOTPResendClock(response.data.sec));
                }
            }else{
                dispatch(UtilityActions.showPopup("Error sending OTP. Please try later.","info"));
                //trackUtils.trackOTPResent({contact:contact,status:"FAILED"});
            }
        }).catch(function (error) {
            dispatch(UtilityActions.showPopup("Error sending OTP. Please try later.","info"));
            trackUtils.trackOTPResent({contact:contact,status:"FAILED"});
        })
    }
}

export function setAddressViewType(type) {
    return dispatch => {
        dispatch({type:"SET_ADDRESS_VIEW_TYPE", payload:type});
    }
}

export function getLoyalteaScore() {
    return dispatch => {
        dispatch({type:"GET_LOYALTEA_SCORE_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.loyaltea,
            data: JSON.stringify(StorageUtils.getCustomerId()),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(!appUtil.checkEmpty(response.data)){
                var customer = {...StorageUtils.getCustomerDetail(),
                    name:response.data.name,
                    email:response.data.email,
                    chaayosCash:response.data.chaayosCash,
                    refCode:response.data.refCode,
                    optOutOfFaceIt: response.data.optOutOfFaceIt,
                    optOutTime: response.data.optOutTime
                };
                StorageUtils.setCustomerDetail(customer);
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
            }
            dispatch({type: "GET_LOYALTEA_SCORE_FULFILLED", payload: response.data.loyalityPoints});
        }).catch(function (error) {
            dispatch({type: "GET_LOYALTEA_SCORE_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error getting Loyaltea score!", 'error'));
        })
    }
}

export function setCustomerDetail() {
    return dispatch => {
        dispatch({type:"SET_CUSTOMER_DETAIL", payload: StorageUtils.getCustomerDetail()});
    }
}

export function setDeviceKey(key) {
    return dispatch => {
        dispatch({type:"SET_DEVICE_KEY", payload: key});
    }
}

export function setSessionKey(key) {
    return dispatch => {
        dispatch({type:"SET_SESSION_KEY", payload: key});
    }
}

export function initOTPResendClock(seconds) {
    return dispatch => {
        var sec = seconds;
        var clock = setInterval(()=>{
            if(sec==0){
                clearInterval(clock);
            }
            dispatch(setOTPResendSeconds(sec--));
        },1000)
    }
}

export function setOTPResendSeconds(sec) {
    return dispatch => {
        dispatch({type:"SET_OTP_RESEND_SECONDS",payload:sec});
    }
}

export function faceItOptOut(customer) {
    return dispatch => {
        dispatch({type:"FACE_IT_OPT_OUT_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.faceItOptOut,
            data: JSON.stringify(StorageUtils.getCustomerId()),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(response != null && response.data == true){
                let c = {...customer, optOutOfFaceIt:true};
                StorageUtils.setCustomerDetail(c);
                dispatch({type: "SET_CUSTOMER_DETAIL", payload: c});
                dispatch({type:"FACE_IT_OPT_OUT_SUCCESS"});
                dispatch(UtilityActions.showPopup("FaceIt opt out success!", 'success'));
            } else {
                dispatch({type:"FACE_IT_OPT_OUT_FAILED"});
                dispatch(UtilityActions.showPopup("Error opting out of FaceIt!", 'error'));
            }
        }).catch(function (error) {
            dispatch({type:"FACE_IT_OPT_OUT_FAILED"});
            dispatch(UtilityActions.showPopup("Error opting out of FaceIt!", 'error'));
        })
    }
}

export function getAvailableGiftCardInfo() {
    return dispatch => {
        let customer = StorageUtils.getCustomerDetail();
        //console.log('Customer Details', customer);
        if(!appUtil.checkEmpty(customer) && !appUtil.checkEmpty(customer.customerId)){
            //console.log('Enter getAvailableGiftCardInfo, customerId', customer.customerId);
            dispatch({type:"GET_GIFTCARD_AMOUNT_PENDING"});
            axios({
                method: "POST",
                url: apis.getUrls().customer.availableGiftCardAmount,
                data: customer.customerId,
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if(!appUtil.checkEmpty(response.data)){
                    //console.log("Card Details:: " , response.data);
                    var customer = {...StorageUtils.getCustomerDetail(),
                        cardAmount:response.data.cardAmount
                    };
                    StorageUtils.setCustomerDetail(customer);
                    dispatch({type: "SET_CUSTOMER_DETAIL", payload: customer});
                }
                dispatch({type: "GET_GIFTCARD_AMOUNT_FULFILLED", payload: response.data.cardAmount});
            }).catch(function (error) {
                dispatch({type: "GET_GIFTCARD_AMOUNT_REJECTED", payload: error});
                dispatch(UtilityActions.showPopup("Error getting customer gift card amount!", 'error'));
            })
        }
    }
}
