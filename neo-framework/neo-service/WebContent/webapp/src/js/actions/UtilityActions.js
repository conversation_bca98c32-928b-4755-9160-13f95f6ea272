import appUtil from "../AppUtil";
import apis from "../APIs";
import axios from "axios";
import trackUtils from "../utils/TrackUtils";
import StorageUtils from "../utils/StorageUtils";
import * as CartManagementActions from "./CartManagementActions";
import * as LocalityActions from "./LocalityActions";
import * as OrderManagementActions from "./OrderManagementActions";
import * as CampaignManagementActions from "./CampaignManagementActions";
import * as UtilityActions from "./UtilityActions";
import * as CustomerActions from "./CustomerActions";
import {browserHistory} from "react-router";

export function createDevice() {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().stamping.registerDevice,
            data: JSON.stringify({userAgent: navigator.userAgent, platform: navigator.platform}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            let cartDetail = response.data.cartDetail;
            response.data.cartDetail = null;
            StorageUtils.setAuthDetail(response.data);
            dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));
            cartDetail.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria || "TAKE_AWAY");
            dispatch(CartManagementActions.updateCart(cartDetail));
            const o = getDefParams();
            trackUtils.initWebEngageParams(o.orderMode, o.city, o.locality, o.outlet);
        }).catch(function (error) {
            dispatch({type: "SET_INTERNET_ERROR", payload: true});
        });
    }
}

export function stampDevice(authDetail) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().stamping.stampDevice,
            data: JSON.stringify(authDetail),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (appUtil.checkEmpty(response.data)) {
                dispatch(createDevice());
            } else {
                let cartDetail = response.data.cartDetail;
                response.data.cartDetail = null;
                StorageUtils.setAuthDetail(response.data);
                dispatch(CustomerActions.setDeviceKey(response.data.deviceKey));
                dispatch(CustomerActions.setSessionKey(response.data.sessionKey));
                cartDetail.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria || "TAKE_AWAY");
                //API will never return null cart from backend(vivek)
                if(StorageUtils.getMembershipFlag() == "true"){
                    cartDetail.orderDetail.orders=[];
                    StorageUtils.setMembershipFlag(false);
                }
                dispatch(CartManagementActions.updateCart(cartDetail));
                var contact = StorageUtils.getCustomerContact();
                contact != null ? trackUtils.trackUser(contact) : null;
                const o = getDefParams();
                trackUtils.initWebEngageParams(o.orderMode, o.city, o.locality, o.outlet);
                /*if (!appUtil.checkEmpty(cartDetail)) {
                    appUtil.setCartDetail(cartDetail);
                }else{
                    appUtil.removeCartDetail();
                }*/
            }
        }).catch(function (error) {
            dispatch({type: "SET_INTERNET_ERROR", payload: true});
        });
    }
}

const getDefParams = function () {
    var orderMode = null, city = null, locality = null, outlet = null;
    var data = StorageUtils.getLocalityMetadata();
    !appUtil.checkEmpty(data) ? (
        orderMode = data.criteria,
            city = data.city,
            locality = (data.locality != null ? data.locality.label : ""),
            outlet = (data.outlet != null ? data.outlet.label : "")/*,
        device_id=(StorageUtils.getAuthDetail()!=null?StorageUtils.getDeviceId():"")*/
    ) : null;
    return {orderMode: orderMode, city: city, locality: locality, outlet: outlet};
};

export function setDefaultData() {
    return dispatch => {
        //console.log('setting locality data from cookie if available');
        var localityData = StorageUtils.getLocalityMetadata();
        if (localityData != null) {
            //console.log('localityData ' + JSON.stringify(localityData));
            if (!appUtil.checkEmpty(localityData.criteria)) {
                //console.log('setting criteria ' + localityData.criteria);
                dispatch(LocalityActions.setCriteria(localityData.criteria));
            }
            if (localityData.city != null && localityData.state != null) {
                //console.log('setting city ' + localityData.city);
                //console.log('setting state ' + localityData.state);
                dispatch(LocalityActions.setCity(localityData.city, localityData.state));
            }
            if (!appUtil.checkEmpty(localityData.locality)) {
                //console.log('setting locality ' + localityData.locality);
                dispatch(LocalityActions.selectLocality(localityData.locality));
            }
            if (!appUtil.checkEmpty(localityData.outlet)) {
                //console.log('setting outlet ' + localityData.outlet);
                dispatch(LocalityActions.selectOutlet(localityData.outlet));
            }
        }
        var currentOrderId = StorageUtils.getCurrentOrderId();
        if (!appUtil.checkEmpty(currentOrderId)) {
            dispatch(OrderManagementActions.setCurrentOrderId(currentOrderId));
        }
        var authDetail = StorageUtils.getAuthDetail();
        if (!appUtil.checkEmpty(authDetail)) {
            dispatch(CustomerActions.setDeviceKey(authDetail.deviceKey));
            dispatch(CustomerActions.setSessionKey(authDetail.sessionKey));
        }
        dispatch(CampaignManagementActions.setCampaignCookie(StorageUtils.getCampaignDetails()));
    }
};

export function showPopup(message, type, timeout) {
    return dispatch => {
        if (type != null) {
            dispatch({type: "SHOW_POPUP", payload: {message: message, type: type}});
        } else {
            dispatch({type: "SHOW_POPUP", payload: {message: message, type: "info"}});
        }
        var milliSec = 2000;
        if (timeout != null) {
            milliSec = timeout;
        }
        setTimeout(function () {
            dispatch({type: "HIDE_POPUP"});
        }, milliSec);
    }
}

export function showFullPageLoader(message) {
    return dispatch => {
        dispatch({type: "SHOW_FULLPAGE_LOADER", payload: message});
    }
}


export function hideFullPageLoader() {
    return dispatch => {
        dispatch({type: "HIDE_FULLPAGE_LOADER"});
    }
}

export function handlePrompt() {
    return dispatch => {
        dispatch({type: "HANDLE_PROMPT"});
    }
}

export function showPrompt(message, success, dismiss) {
    return dispatch => {
        dispatch({type: "SHOW_PROMPT", payload: {message: message, success: success, dismiss: dismiss}});
    }
}

export function handleRotate(isLandscape) {
    return dispatch => {
        dispatch({type: "SET_ORIENTATION", payload: isLandscape});
    }
}


export function getOutletByCity(city) {
    return dispatch => {
        dispatch({type:"SET_CITY_OUTLET_LOADED", payload:false});
        axios({
            method: "GET",
            url: apis.getUrls().neoMetadata.getUnits + "?city=" + city,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type:"SET_CITY_OUTLET_LOADED", payload:true});
            if (response.data != null) {
                dispatch({type: "SET_CITY_OUTLET", payload: response.data});
                dispatch({type:"SET_CITY_MAP_UNIT", payload:response.data[0]});
                //dispatch(CustomerActions.setSessionKey(response.data.sessionKey));
            }
        }).catch(function (error) {
            dispatch({type:"SET_CITY_OUTLET_LOADED", payload:true});
        });
    }
}


export function authenticateExternalPartner(accessKey, token) {
    return dispatch => {
        dispatch({type:"SET_CITY_OUTLET_LOADED", payload:false});
        axios({
            method: "GET",
            url: apis.getUrls().neoExternalPartner.requestSession + "?accessKey=" + accessKey + "&token=" + token,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(response.data != null && response.data.status == true) {
                var key = response.data.redirectUrl.substring(response.data.redirectUrl.indexOf("=")+1,response.data.redirectUrl.length);
                dispatch(getAccessDetails(key));
            } else {
                UtilityActions.showFullPageLoader("Invalid parameters passed.");
            }
        }).catch(function (error) {
            dispatch({type:"SET_CITY_OUTLET_LOADED", payload:true});
        });
    }
}

export function getAccessDetails(key) {
    return dispatch => {
        dispatch({type:"SET_CITY_OUTLET_LOADED", payload:false});
        axios({
            method: "GET",
            url: apis.getUrls().customer.loginKey + "?key=" + key,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if(response.data != null && response.data.customer != null && response.data.unitId != null) {
                //console.log(response.data);
                dispatch(LocalityActions.setCriteria("DINE_IN"));
                dispatch(LocalityActions.selectOutlet({label:"", value:response.data.unitId}));
                dispatch(setupEnvironment(response.data));
            } else {
                UtilityActions.showFullPageLoader("Error in authenticating request. Please try again.");
            }
        }).catch(function (error) {
            dispatch({type:"SET_CITY_OUTLET_LOADED", payload:true});
        });
    }
}

export function setupEnvironment(data) {
    return dispatch => {
        var cart = data.customer.device.cartDetail;
        data.customer.device.cartDetail = null;
        StorageUtils.setAuthDetail(data.customer.device);
        //var cartDetail = StorageUtils.getCartDetail();
        /*if (appUtil.checkEmpty(cartDetail)) {
            cartDetail = cart;
        } else {
            cartDetail.orderDetail.orders = cart.orderDetail.orders;
            cartDetail.orderDetail.webCustomerId = cart.customerId;
            cartDetail.cartId = cart.cartId;
            cartDetail.deviceId = cart.deviceId;
            cartDetail.customerId = cart.customerId;
            cartDetail.orderDetail.customerName = response.data.name;
            cartDetail.sessionId = cart.sessionId;
        }*/
        //if(!appUtil.checkEmpty(cart))
        //cartDetail = appUtil.calculateTaxes(cartDetail);
        dispatch(CartManagementActions.updateCart(cart));
        dispatch({type: "SET_DEVICE_KEY", payload: data.customer.device.deviceKey});
        dispatch({type: "LOGIN_CUSTOMER_FULFILLED", payload: data.customer.device.sessionKey});
        dispatch(UtilityActions.hideFullPageLoader());
        dispatch(LocalityActions.toggleLocationWrapper(false));
        var customer = StorageUtils.getCustomerDetail();
        if (customer == null) {
            customer = {}
        }
        customer = {
            ...customer,
            contact: data.customer.contact,
            name: data.customer.name,
            email: data.customer.email,
            loyalty: data.customer.loyalty
        };
        dispatch({type: "SET_CUSTOMER", payload: customer});
        dispatch(restrictDineIn());
        StorageUtils.setCustomerDetail(customer);
        dispatch(LocalityActions.setShowOutlet(true));
        browserHistory.push("/menu");
    }
}

export function restrictDineIn() {
    return dispatch => {
        dispatch({type: "RESTRICT_DINE_IN", payload: true});
    }
}
