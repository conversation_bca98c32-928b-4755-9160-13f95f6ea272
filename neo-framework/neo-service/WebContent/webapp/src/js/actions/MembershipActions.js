import * as CartManagementActions from "./CartManagementActions";
import * as OutletMenuActions from "./OutletMenuActions";
import * as LocalityActions from "./LocalityActions";
import StorageUtils from "../utils/StorageUtils";
import appUtil from "../AppUtil";
import apis from "../APIs";
import axios from "axios";
import * as UtilityActions from "../actions/UtilityActions";
import * as OrderManagementActions from "../actions/OrderManagementActions";

export function getMembershipData(unitId, skuCode, callback) {
    return dispatch => {
        dispatch(OrderManagementActions.getMembershipProductIds('26254', () => {
        }));
        var contact = StorageUtils.getCustomerContact();
        dispatch(setCustomerInfo(contact, () => {
        }));
        skuCode.code = skuCode.code.toUpperCase();
        dispatch({type: "LOAD_MEMBERSHIP_DATA"});
        axios({
            method: "GET",
            url: apis.getUrls().neoCache.membershipProduct + "?unitId=" + unitId + "&skuCode=" + skuCode.code,
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data.skuCodeProduct === null) {
                if (skuCode.code !== "chaayos_select") {
                    skuCode = {code: "chaayos_select"};
                    dispatch(setMembershipType(skuCode.code));
                    dispatch(getMembershipData('26254', skuCode, callback));
                }
            } else {
                dispatch({type: "SET_MEMBERSHIP_DATA", payload: response.data});
                callback(response.data);
            }
        }).catch(function (error) {
            console.log(error);
        });
    }
}

export function setCustomerInfo(contact, callback) {
    return dispatch => {
        if (!appUtil.checkEmpty(contact)) {
            axios({
                method: "POST",
                url: apis.getUrls().customer.getCustomerInfoByContact,
                data: contact,
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                dispatch({type: "SET_CUSTOMER_INFO", payload: response.data});
                callback(response.data);
            }).catch(function (error) {
                console.log(error);
            });
        } else {
            callback();
        }
    }
}

export function setCartMembershipItem(product, callback) {
    return dispatch => {
        dispatch(CartManagementActions.updateCart(null));
        StorageUtils.setMembershipFlag(true);
        dispatch(getUnitProducts(
            "DINE_IN", {"city": "New Delhi", "state": 32}, null, {
                "value": 26254,
                "label": "Chaayos Bazaar"
            }, function () {
                dispatch(LocalityActions.setCriteria("DINE_IN"));
                dispatch(LocalityActions.setCity({"city": "Gurgaon", "state": 8}));
                dispatch(LocalityActions.selectOutlet({"value": 10000, "label": "Good Earth City Centre"}));
                dispatch(LocalityActions.selectLocality(null));
                dispatch(addNewItemToCart(product));
                callback();
            }))
    }
}

export function getUnitProducts(criteria, city, locality, outlet, callback) {
    console.log("getUnitProducts:: criteria:: " + criteria + " city:: " + JSON.stringify(city) + " locality:: " + JSON.stringify(locality) + " outlet:: " + JSON.stringify(outlet));
    return dispatch => {
        let url;
        let method;
        let reqData;
        url = apis.getUrls().unitCache.getUnit + "?unitId=" + outlet.value;
        method = "GET";
        reqData = {};
        axios({
            method: method,
            url: url,
            data: reqData,
            headers: {"Content-Type": "application/json"}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.status === 200) {
                console.log("unit data loaded:::::::::::::::::::::", response.data);
                let unitData = response.data.unit;
                appUtil.setTaxes(unitData.taxes);
                var skipDeliveryPackaging = {
                    delivery: response.data.skipDeliveryCharge,
                    packaging: response.data.skipPackagingCharge
                };
                dispatch({type: "SET_SKIP_DELIVERY_PACKAGING", payload: skipDeliveryPackaging});
                StorageUtils.setUnitDetails({id: unitData.id, name: unitData.name});
                appUtil.setInclusiveTaxPrices(unitData, appUtil.isInterstate(unitData, city));
                dispatch({type: "LOAD_UNIT_PRODUCTS_FULFILLED", payload: unitData});
                dispatch(CartManagementActions.initCart(unitData, skipDeliveryPackaging, appUtil.isInterstate(unitData, city)));
                dispatch(OutletMenuActions.loadUnitInventory(unitData));
                if (response.data.error && response.data.error > 0) {
                    dispatch(UtilityActions.showPopup(response.data.message, "info", 4000));
                }
                if (!appUtil.checkEmpty(StorageUtils.getCartDetail())) {
                    let crt = StorageUtils.getCartDetail();
                    crt.orderDetail.unitId = unitData.id;
                    crt.orderDetail.unitName = unitData.name;
                    dispatch({type: "UPDATE_CART", payload: crt});
                }
                callback();
            } else {
                dispatch(UtilityActions.showPopup("Error adding membership. Try Again .", "error", 4000));
            }
        }).catch(function (error) {
            console.log(error);
            dispatch(UtilityActions.showPopup("Error adding membership. Try Again after sometime.", "error", 4000));
        });
    }
}

export function addNewItemToCart(product) {
    return dispatch => {
        let cartItem = product;
        cartItem.selectedDimension = product.prices[0];
        dispatch(CartManagementActions.addItemToCart({
            cartItem: cartItem,
            addons: [],
            quantity: 1,
            customizationStrategy: product.strategy,
        }));
    }
}

export function setMembershipType(type) {
    return dispatch => {
        dispatch({type: "SET_MEMBERSHIP_TYPE", payload: type});
    }
}
