import {browserHistory} from "react-router";
import axios from "axios";
import _ from "lodash";
import appUtil from "../AppUtil";
import AppUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import * as UtilityActions from "./UtilityActions";
import trackUtils from "../utils/TrackUtils";

export function initCart(unit, skipDeliveryPackaging, isInterstate) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var itemRemoved = false;
        var removedItems = [];
        if (!appUtil.checkEmpty(cart) && unit != null) {
            if (cart.orderDetail.orders.length > 0) {
                var orderItems = [];
                var index = 1;
                var cartChanged = false;
                cart.orderDetail.orders.map((item) => {
                    var found = false;
                    if (item.code != null) { //for backward compatibility issues
                        item.discountDetail = appUtil.getEmptyDiscountObj();
                        unit.products.map((product) => {
                            if (product.id == item.productId) {
                                product.prices.map((price) => {
                                    if (price.dimension == item.dimension) {
                                        if (item.price != price.price) {
                                            item.price = price.price;
                                            cartChanged = true;
                                        }
                                        item.itemId = index;
                                        index++;
                                        orderItems.push(item);
                                        found = true;
                                    }
                                });
                                item.customizationStrategy = product.strategy;
                            }
                        });
                        if (!found) {
                            if ([1043,1044].indexOf(item.productId) < 0) {
                                itemRemoved = true;
                                removedItems.push(item.productName + (item.dimension == "None" ? "-Regular" : "-" + item.dimension));
                            }
                            sendRemoveItemFromCart(cart, item);
                        }
                    }
                });
                cart.orderDetail.orders = orderItems;
                cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();//cart.orderDetail.transactionDetail==null?{}:cart.orderDetail.transactionDetail;
                //setTaxProfile(unit, cart);
                //cart.orderDetail.offerCode = null;
                cart.orderDetail.unitId = unit.id;
                cart.orderDetail.unitName = unit.name;
                cart.orderDetail.brandId = 1;
                cart.orderDetail.stateId = unit.location.state.id;
                cart.orderDetail.terminalId = 786;
                if (StorageUtils.getCampaignDetails() != null && StorageUtils.getCampaignDetails().cid != null) {
                    cart.orderDetail.campaignId = StorageUtils.getCampaignDetails().cid;
                }
                //cart.orderDetail.hasParcel = StorageUtils.getLocalityMetadata().criteria == "DELIVERY";
                cart.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria);
                var auth = StorageUtils.getAuthDetail();
                if (!appUtil.checkEmpty(auth)) {
                    cart.deviceId = StorageUtils.getDeviceId();
                    cart.customerId = StorageUtils.getCustomerId();
                    if (!appUtil.checkEmpty(cart.orderDetail)) {
                        cart.orderDetail.webCustomerId = StorageUtils.getCustomerId();
                    }
                }
            }
            //dispatch(updateCart(cart));
            dispatch(removeDeliveryAndPackagingFromCart(cart, skipDeliveryPackaging, isInterstate));
            if (cart.orderDetail.source == "COD" && cart.orderDetail.orders.length > 0) {
                dispatch(addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
            }
            if (!appUtil.checkEmpty(cart.orderDetail)) {
                cart.orderDetail.transactionDetail == null ? cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject() : null;
                cart = appUtil.calculateTaxes(cart, isInterstate);
            }
            dispatch(updateCart(cart));
        }
        if (itemRemoved) {
            cartChanged = true;
            var msg;
            if (cart.orderDetail.source == "COD") {
                msg = "We have removed " + removedItems.join(", ") + " from your cart because " + (removedItems.length == 1 ? "this item is" : "these items are") + " not available in delivery menu for selected location."
            } else {
                msg = "We have removed " + removedItems.join(", ") + " from your cart because " + (removedItems.length == 1 ? "this item is" : "these items are") + " not available in menu of selected outlet."
            }
            dispatch(UtilityActions.showPopup(msg, 'info', 7000));
        }
        if (cartChanged) {
            if (cart.orderDetail.offerCode != null) {
                dispatch(removeCoupon());
                dispatch(removeOfferInitCart(true));
            }
        }
    }
}

export function addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging) {
    return dispatch => {
        var cartAmount = 0;
        var orderItems = [];
        cart.orderDetail.orders.map((orderItem) => {
            if ([1043,1044].indexOf(orderItem.productId) < 0) {
                cartAmount += (orderItem.price * orderItem.quantity);
                orderItems.push(orderItem);
            }
        });
        cart.orderDetail.orders = orderItems;
        var item = StorageUtils.getDeliveryPackagingCartItems();
        if (!skipDeliveryPackaging.packaging && item.packaging != null) {
            item.packaging.itemId = cart.orderDetail.orders.length + 1;
            if(item.packaging.packagingType == "FIXED") {
                item.packaging.price = item.packaging.packagingValue;
            } else if(item.packaging.packagingType == "PERCENTAGE") {
                item.packaging.price = ((item.packaging.packagingValue/100)*cartAmount);
            }
            item.packaging.quantity = 1;
            cart.orderDetail.orders.push(item.packaging);
            cartAmount += item.packaging.price;
        }
        if (cartAmount < 200) {
            if (!skipDeliveryPackaging.delivery && item.delivery != null) {
                item.delivery.itemId = cart.orderDetail.orders.length + 1;
                item.delivery.quantity = 1;
                cart.orderDetail.orders.push(item.delivery);
            }
        }
        dispatch(updateCart(cart));
    }
}

const removeDeliveryAndPackagingFromCart = (cart, skipDeliveryPackaging, isInterState) => {
    return dispatch => {
        var orderItems = [];
        cart.orderDetail.orders.map((orderItem) => {
            if([1043,1044].indexOf(orderItem) < 0) {
                orderItems.push(orderItem);
            }
        });
        cart.orderDetail.orders = orderItems;
        cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
        cart = appUtil.calculateTaxes(cart, isInterState);
        dispatch(updateCart(cart));
    }
};

export function setDeliveryAndPackagingItem(unit) {
    var packaging = null, delivery = null, deliveryItem = null, packagingItem = null;
    unit.products.map((product) => {
        if (product.id == 1044) {
            delivery = product;
        }
        if (product.id == 1043) {
            packaging = product;
        }
    });
    if (delivery != null) {
        deliveryItem = {
            id: delivery.id,
            name: delivery.name,
            price: delivery.prices[0].price,
            dimension: delivery.prices[0].dimension,
            billType: delivery.billType,
            type: delivery.type,
            taxCode: delivery.taxCode
        };
        //appUtil.getDeliveryPackagingCartItem(delivery);
    }
    if (packaging != null) {
        packagingItem = {
            id: packaging.id,
            name: packaging.name,
            price: packaging.prices[0].price,
            packagingValue: unit.packagingValue,
            packagingType: unit.packagingType,
            dimension: packaging.prices[0].dimension,
            billType: packaging.billType,
            type: packaging.type,
            taxCode: delivery.taxCode
        };
        //appUtil.getDeliveryPackagingCartItem(packaging);
    }
    StorageUtils.setDeliveryPackagingCartItems({delivery: deliveryItem, packaging: packagingItem});
}

export function addItemToCart(cartObj) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var action = "ADD_ITEM";
        if (appUtil.checkEmpty(cart)) {
            cart = appUtil.getNewCartObj();
            action = "CREATE_CART";
        }
        var variants = [];
        if (!appUtil.checkEmpty(cartObj.cartItem.selectedDimension.recipe))
            cartObj.cartItem.selectedDimension.recipe.ingredient.variants.map((variant) => {
                variant.details.map((detail) => {
                    if (detail.active) {
                        variants.push(detail);
                    }
                })
            });
        var ingredientProducts = [];
        if (!appUtil.checkEmpty(cartObj.cartItem.selectedDimension.recipe))
            cartObj.cartItem.selectedDimension.recipe.ingredient.products.map((product) => {
                product.details.map((detail) => {
                    if (detail.active) {
                        ingredientProducts.push(detail);
                    }
                })
            });
        var menuProducts = [];
        if (!appUtil.checkEmpty(cartObj.cartItem.selectedDimension.recipe))
            if (cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct != null) {
                cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                    if (detail.cartProduct != null) {
                        menuProducts.push(detail.cartProduct);
                    }
                })
            }
        var cartItem = {
            itemId: cart.orderDetail.orders.length + 1,
            productId: cartObj.cartItem.id,
            customizationStrategy: cartObj.customizationStrategy,
            productName: appUtil.getProductName(cartObj.cartItem.id, cartObj.cartItem.name),
            productCategory: {id: cartObj.cartItem.webType},
            quantity: cartObj.quantity,
            price: cartObj.cartItem.selectedDimension.price,
            totalAmount: null,
            amount: cartObj.quantity * cartObj.cartItem.selectedDimension.price,
            discountDetail: appUtil.getEmptyDiscountObj(),
            addons: [],
            dimension: cartObj.cartItem.selectedDimension.dimension,
            billType: cartObj.cartItem.billType,
            isCombo: false, // TODO fix this by webtype cartObj.cartItem.type == 8,
            composition: {
                variants: variants,
                products: ingredientProducts,
                menuProducts: menuProducts,
                addons: cartObj.addons
            },
            recipeId: cartObj.cartItem.selectedDimension.recipeId,
            code: cartObj.cartItem.taxCode,
            taxes: [],
            tax: 0,
            originalTax: 0
        };
        if(cartObj.cartItem.taxCode === 'GIFT_CARD' ){
            cartItem.cardType = 'ECARD';
        }
        /*if (!appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
         cart = appUtil.calculateTaxes(cart);
         }*/
        //code to add cart to backend
        if (action == "CREATE_CART") {
            cart.orderDetail.orders.push(cartItem);
            StorageUtils.setCartDetail(cart);
            dispatch({type: "CREATE_CART", payload: cart});
            dispatch(sendCreateCart(cart));
        } else if (action == "ADD_ITEM") {
            /*dispatch({type: "ADD_ITEM_TO_CART", payload: cartItem});
             sendAddItemToCart(cart, cartItem, cartObj.webCategory, cartObj.tags);*/
            //dispatch(addItem(cart, cartItem, cartObj.webCategory, cartObj.tags));
            dispatch({type: "ADD_ITEM_TO_CART"});
            cart.orderDetail.orders.push(cartItem);
            dispatch(updateCart(cart));
            sendAddItemToCart(cart, cartItem, cartObj.webCategory, cartObj.tags);
        }
    }
}

const sendCreateCart = (cart) => {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().cart.createCart,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            var cart = StorageUtils.getCartDetail();
            cart.cartId = response.data.cartId;
            dispatch(updateCart(cart));
        }).catch(function (error) {

        });
    }
};

const sendAddItemToCart = (cart, cartItem, webCategory, tags) => {
    if (cart == null || cart.cartId == null) {
        window.location.reload();
    }
    try {
        trackUtils.trackAddCartItem({
            cartItem: cartItem, cartId: cart.cartId, unitId: cart.orderDetail.unitId,
            unitName: cart.orderDetail.unitName, webCategory: webCategory, tags: tags
        });
    } catch (e) {
    }
    axios({
        method: "POST",
        url: apis.getUrls().cart.addItem,
        data: JSON.stringify({cartId: cart.cartId, orderItem: cartItem}),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {
        console.log("..........................", error);
    });
};

const sendRemoveItemFromCart = (cart, item, screen) => {
    try {
        trackUtils.trackRemoveCartItem({
            cartItem: item,
            cartId: cart.cartId,
            unitId: cart.orderDetail.unitId,
            unitName: cart.orderDetail.unitName,
            screen: screen
        });
    } catch (e) {
    }
    axios({
        method: "POST",
        url: apis.getUrls().cart.removeItem,
        data: JSON.stringify({cartId: cart.cartId, orderItem: item}),
        headers: {'Content-Type': 'application/json'}
    }).then(function (response) {

    }).catch(function (error) {

    });
};

/*export function addItem(cart, cartItem, webCategory, tags) {
 return dispatch => {
 cart.orderDetail.orders.push(cartItem);
 dispatch(updateCart(cart));
 sendAddItemToCart(cart, cartItem, webCategory, tags);
 }
 }*/

export function updateItemQty(cart, qty, itemId, skipDeliveryPackaging, isInterState) {
    return dispatch => {
        var cartx = Object.assign({}, cart);
        cartx.orderDetail.orders.map((item) => {
            if (item.itemId == itemId) {
                item.quantity = item.quantity + qty;
                item.amount = item.quantity * item.price;
            }
        });
        dispatch(removeDeliveryAndPackagingFromCart(cartx));
        if (cartx.orderDetail.source == "COD") {
            dispatch(addDeliveryAndPackagingToCart(cartx, skipDeliveryPackaging));
        }/* else {
            dispatch(removeDeliveryAndPackagingFromCart(cartx));
        }*/
        if (!appUtil.checkEmpty(cartx.orderDetail) && !appUtil.checkEmpty(cartx.orderDetail.transactionDetail)) {
            cartx = appUtil.calculateTaxes(cartx, isInterState);
        }
        dispatch(updateCart(cartx));
    }
}

export function removeItem(cart, item, screen, skipDeliveryPackaging, isInterState) {
    return dispatch => {
        var items = [];
        var index = 1;
        cart.orderDetail.orders.map((itemx) => {
            if (itemx.itemId != item.itemId) {
                itemx.itemId = index++;
                items.push(itemx);
            }
        });
        cart.orderDetail.orders = items;
        if (!appUtil.checkEmpty(cart.orderDetail) && !appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
            cart = appUtil.calculateTaxes(cart, isInterState);
        }
        dispatch(updateCart(cart));
        if(item.productId != 1043 && item.productId != 1044){
            sendRemoveItemFromCart(cart, item, screen);
        }
        dispatch(removeDeliveryAndPackagingFromCart(cart));
        if (cart.orderDetail.source == "COD") {
            dispatch(addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
        }/* else {
            dispatch(removeDeliveryAndPackagingFromCart(cart));
        }*/
        cart = appUtil.calculateTaxes(cart, isInterState);
        dispatch(updateCart(cart));
    }
}

export function setOrderRemark(cart, remark) {
    return dispatch => {
        if (!appUtil.checkEmpty(remark)) {
            trackUtils.trackOrderRemark(remark);
            var cartx = Object.assign({}, cart);
            cartx.orderDetail.orderRemark = remark;
            dispatch(updateCart(cartx));
        }
    }
}

export function setSyncCart(value) {
    return dispatch => {
        dispatch({type: "SET_SYNC_CART", payload: value});
    }
}

export function placeOrder(cart, unit, selectedCity) {
    return dispatch => {
        var isInterstate = appUtil.isInterstate(unit, selectedCity);
        var cart = StorageUtils.getCartDetail();
        var productList = [];
        _.map(cart.orderDetail.orders, (oi)=> {
            if (productList.indexOf(oi.productId) < 0) {
                productList.push(oi.productId);
            }
        });
        dispatch(UtilityActions.showFullPageLoader("Syncing your cart. Please wait!"));
        axios({
            method: "POST",
            url: apis.getUrls().webInventory.unitProducts,
            data: JSON.stringify({id: unit.id, name: unit.name, list: productList}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data)) {
                dispatch({type: "INVENTORY_UPDATE", payload: response.data});
                dispatch(updateStockInCart(cart, response.data, true, isInterstate, selectedCity));
            } else {
                cart.orderDetail.orders.length > 0 ? dispatch(syncCart(isInterstate, selectedCity)) : null;
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function updateStockInCart(cart, stock, isRejected, isInterstate, selectedCity) {
    return dispatch => {
        if (cart.orderDetail && cart.orderDetail.orders && cart.orderDetail.orders.length > 0) {
            var inventoryShortage = false;
            var shortItems = [];
            var shortStock = [];
            var remove = [];
            _.map(cart.orderDetail.orders, (oi)=> {
                if (stock[oi.productId] <= 0) {
                    remove.push(oi);
                } else if (stock[oi.productId] != null) {
                    if (oi.quantity > stock[oi.productId]) {
                        oi.quantity = stock[oi.productId];
                        inventoryShortage = true;
                        shortItems.push(oi.productName);
                        shortStock.push({
                            product: oi.productName,
                            cartQuantity: oi.quantity,
                            shortQuantity: oi.quantity - stock[oi.productId]
                        });
                    }
                }
            });
            _.map(remove, (item)=> {
                dispatch(removeItem(cart, item));
            });
            dispatch(updateCart(cart));
            if (remove.length > 0) {
                dispatch(UtilityActions.showPopup("Few items have been removed from your cart as they are not available in stock.", "info", 2000));
            }
            if (inventoryShortage) {
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup(shortItems.join(", ") + " in your cart are not available in requested quantity. So, we have updated their quantity.", "error", 2000));
            } else {
                (cart.orderDetail.orders.length > 0 && isRejected == true) ? dispatch(syncCart(isInterstate, selectedCity)) : null;
            }
            if (shortStock.length > 0 && isRejected == true) {
                trackUtils.trackRejectedStock(shortItems);
            }
        }
    }
}

export function syncCart(isInterState, selectedCity, handleError) {
    return (dispatch, getState) => {
        var cart = StorageUtils.getCartDetail();
        const {membershipIds} = getState().membershipReducer;
        const {customerInfo} = getState().membershipReducer;
        cart.orderDetail.transactionDetail == null ? cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject() : null;
        if (cart.orderDetail.source == null || typeof cart.orderDetail.source == 'undefined' || cart.orderDetail.source.length == 0) {
            var crit = StorageUtils.getLocalityMetadata().criteria;
            if (crit == null || crit.length == 0) {
                browserHistory.push("/menu");
                dispatch(UtilityActions.showPopup("Something went wrong. Please check cart!", "info"));
            } else {
                cart.orderDetail.source = appUtil.getSourceFromCriteria(crit);
            }
        }
        if (cart.orderDetail.unitId == null || appUtil.checkEmpty(cart.orderDetail.unitName)) {
            cart.orderDetail.unitId = StorageUtils.getUnitDetails().id;
            cart.orderDetail.unitName = StorageUtils.getUnitDetails().name;
        }
        if (selectedCity.state) {
            cart.currentStateId = selectedCity.state;
        }
        cart = appUtil.calculateTaxes(cart, isInterState);
        cart.orderDetail.brandId = 1;
        dispatch(updateCart(cart));
        var d = StorageUtils.getLocalityMetadata();
        trackUtils.trackInitiateCheckout({
            criteria: d.criteria,
            city: d.city,
            locality: !appUtil.checkEmpty(d.locality) ? d.locality.label : "",
            outlet: !appUtil.checkEmpty(d.outlet) ? d.outlet.label : "",
            cart: cart
        });
        dispatch(UtilityActions.showFullPageLoader("Syncing your cart. Please wait!"));
        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                dispatch({type: "SYNC_CART_FULFILLED"});
                const criteria = StorageUtils.getLocalityMetadata().criteria;
                dispatch({type: "SET_ADDRESS_VIEW_TYPE", payload: "DELIVERY"});
                if (!appUtil.checkEmpty(StorageUtils.getSessionId())) {
                    if (criteria == "DELIVERY") {
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: "/addresses"});
                        browserHistory.push("/addresses");
                    } else {
                        if (!AppUtil.checkEmpty(membershipIds.data)) {
                            if (membershipIds.data.includes(cart.orderDetail.orders[0].productId)) {
                                if (AppUtil.checkEmpty(customerInfo.subscriptionInfoDetail)) {
                                    browserHistory.push("/paymentModes");
                                } else {
                                    if (customerInfo.subscriptionInfoDetail.eligible == true) {
                                        browserHistory.push("/paymentModes");
                                    } else {
                                        dispatch(UtilityActions.showPopup("You already have a subscription ", "info", 5000));
                                        dispatch(updateCart(null));
                                    }
                                }
                            }
                        } else {
                            browserHistory.push("/paymentModes");
                        }
                    }
                    dispatch(UtilityActions.hideFullPageLoader());
                } else {
                    if (criteria == "DELIVERY") {
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: "/addresses"});
                    } else {
                        dispatch({type: "SET_LOGIN_REDIRECT", payload: "/paymentModes"});
                    }
                    browserHistory.push("/login");
                    dispatch(UtilityActions.hideFullPageLoader());
                }
            } else {
                dispatch({type: "SYNC_CART_REJECTED", payload: null});
                dispatch(UtilityActions.showPopup(response.data.msg, "error", 2000));
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            if (handleError != false) {
                dispatch(handleSyncError(error.response.data, isInterState, selectedCity));
            }
        });
    }
}

export function handleSyncError(response, isInterState, selectedCity) {
    return dispatch => {
        if (response.errorCode == 716) { //INVALID_SOURCE
            var cart = StorageUtils.getCartDetail();
            cart.orderDetail.source = appUtil.getSourceFromCriteria(StorageUtils.getLocalityMetadata().criteria);
            dispatch(updateCart(cart));
            dispatch(syncCart(isInterState, selectedCity, false));
        } else if (response.errorCode == 703) { //INVALID_UNIT
            var cart = StorageUtils.getCartDetail();
            cart.orderDetail.unitId = StorageUtils.getUnitDetails().id;
            cart.orderDetail.unitName = StorageUtils.getUnitDetails().name;
            dispatch(updateCart(cart));
            dispatch(syncCart(isInterState, selectedCity, false));
        } else if ([701, 702, 715, 708].indexOf(response.errorCode) >= 0) {
            dispatch(UtilityActions.showPrompt("Error in validating cart. Please try again!", function () {
                window.location.reload();
            }, function () {
                window.location.reload();
            }));
        } else {
            dispatch({type: "SYNC_CART_REJECTED", payload: null});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later or call 1800-120-2424 for support.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        }
    }
}

export function updateDeliveryAddressToCart(addressId) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        cart.orderDetail.deliveryAddress = addressId;
        dispatch(UtilityActions.showFullPageLoader(""));
        dispatch({type: "UPDATE_DELIVERY_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().customer.addAddressToCart,
            data: JSON.stringify({id: addressId, name: cart.cartId}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data) {
                dispatch(updateCart(cart));
                dispatch({type: "UPDATE_DELIVERY_CART_FULFILLED"});
                browserHistory.push("/paymentModes");
            } else {
                dispatch({type: "UPDATE_DELIVERY_CART_REJECTED", payload: error});
                dispatch(UtilityActions.showPopup("Error setting address to cart. Try again.", 'error'));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            dispatch({type: "UPDATE_DELIVERY_CART_REJECTED", payload: error});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error setting address to cart. Try again.", 'error'));
        });
    }
}

export function applyCoupon(couponCode, isInterState) {
    return dispatch => {
        trackUtils.trackCouponTried(couponCode);
        var cartValid = validCartItems();
        if (cartValid.status == "success") {
            var cart = StorageUtils.getCartDetail();
            var data;
            if (!appUtil.checkEmpty(StorageUtils.getCustomerDetail())) {
                data = {
                    couponCode: couponCode,
                    order: cart.orderDetail,
                    contact: StorageUtils.getCustomerDetail().contact
                };
            } else {
                data = {couponCode: couponCode, order: cart.orderDetail};
            }
            if(data.order.externalOrderId == null) {
                data.order.externalOrderId = "WO433665635";
            }
            dispatch(UtilityActions.showFullPageLoader(""));
            axios({
                method: "POST",
                url: apis.getUrls().offer.apply,
                data: JSON.stringify(data),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response.status == 200 && !appUtil.checkEmpty(response.data)) {
                    if (response.data.error == false) {
                        _.map(cart.orderDetail.orders, (orderItem) => {
                            _.map(response.data.order.orders, (oi)=> {
                                if (orderItem.itemId == oi.itemId) {
                                    orderItem.discountDetail = oi.discountDetail;
                                }
                            });
                        });
                        cart.orderDetail.offerCode = response.data.couponCode;
                        cart.orderDetail.transactionDetail = response.data.order.transactionDetail;
                        cart = appUtil.calculateTaxes(cart, isInterState);
                        StorageUtils.setCartDetail(cart);
                        dispatch({type: "OFFER_APPLIED", payload: cart});
                        dispatch(UtilityActions.showPopup("Offer applied.", "info"));
                        try {
                            trackUtils.trackOfferSuccess({
                                coupon: cart.orderDetail.offerCode,
                                discount: cart.orderDetail.transactionDetail.totalDiscount
                            });
                        } catch (e) {
                        }
                    } else {
                        dispatch({
                            type: "OFFER_ERROR",
                            payload: {code: response.data.errorCode, message: response.data.errorMessage}
                        });
                        dispatch(UtilityActions.showPopup("Offer not applicable see error message.", "info"));
                        try {
                            trackUtils.trackOfferFailed({
                                coupon: couponCode,
                                errorCode: response.data.errorCode,
                                reason: response.data.errorMessage
                            });
                        } catch (e) {
                        }
                    }
                }
                dispatch(UtilityActions.hideFullPageLoader());
            }).catch(function (error) {
                dispatch(UtilityActions.hideFullPageLoader());
                dispatch(UtilityActions.showPopup("Offer not applicable.", 'error'));
            });
        } else {
            dispatch(UtilityActions.showPopup(cartValid.msg, 'error'));
        }
    }
}

const validCartItems = ()=> {
    const cart = StorageUtils.getCartDetail();
    var ret = null, set = false;
    if (cart != null && cart.orderDetail != null && cart.orderDetail.orders.length > 0) {
        cart.orderDetail.orders.map((oi)=> {
            if (oi.billType == "ZERO_TAX") {
                ret = {status: "fail", msg: "Offer not application on following item: " + oi.productName};
                set = true;
            }
        });
        if (!set) {
            ret = {status: "success", msg: ""}
        }
    } else {
        if (!set) {
            ret = {status: "fail", msg: "Cart is not available. Please reload page."}
        }
    }
    return ret;
}

export function removeCoupon(isInterState) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var discountObj = appUtil.getEmptyDiscountObj();
        cart.orderDetail.orders.map((item) => {
            item.discountDetail = {...discountObj};
        });
        if (appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
            cart.orderDetail.transactionDetail = appUtil.getNewTransactionObject();
        }
        cart.orderDetail.transactionDetail.discountDetail = {...discountObj};
        cart.orderDetail.transactionDetail.discountDetail.wasValueSet = false;
        cart.orderDetail.offerCode = null;
        cart = appUtil.calculateTaxes(cart, isInterState);
        dispatch(updateCart(cart));
    }
}

export function setEditItemId(id) {
    return dispatch => {
        dispatch({type: "SET_EDIT_ITEM_ID", payload: id});
    }
}

export function editCartItem(cartObj, itemId, skipDeliveryPackaging, isInterState) {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        var variants = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.variants.map((variant) => {
            variant.details.map((detail) => {
                if (detail.active) {
                    variants.push(detail);
                }
            })
        });
        var ingredientProducts = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.products.map((product) => {
            product.details.map((detail) => {
                if (detail.active) {
                    ingredientProducts.push(detail);
                }
            })
        });
        var menuProducts = [];
        if (cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct != null) {
            cartObj.cartItem.selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.cartProduct != null) {
                    menuProducts.push(detail.cartProduct);
                }
            })
        }
        var cartItem = {
            itemId: itemId,
            productId: cartObj.cartItem.id,
            productName: appUtil.getProductName(cartObj.cartItem.id, cartObj.cartItem.name),
            customizationStrategy: cartObj.customizationStrategy,
            quantity: cartObj.quantity,
            price: cartObj.cartItem.selectedDimension.price,
            totalAmount: null,
            amount: cartObj.quantity * cartObj.cartItem.selectedDimension.price,
            discountDetail: {
                discountCode: null,
                discountReason: null,
                promotionalOffer: 0,
                discount: {
                    percentage: 0,
                    value: 0
                },
                totalDiscount: 0
            },
            addons: [],
            dimension: cartObj.cartItem.selectedDimension.dimension,
            billType: cartObj.cartItem.billType,
            isCombo: cartObj.cartItem.type == 8,
            composition: {
                variants: variants,
                products: ingredientProducts,
                menuProducts: menuProducts,
                addons: cartObj.addons
            },
            recipeId: cartObj.cartItem.selectedDimension.recipe.recipeId,
            code: cartObj.cartItem.taxCode,
            taxes: [],
            tax: 0,
            originalTax: 0
        };
        var orders = [];
        cart.orderDetail.orders.map((orderItem) => {
            if (orderItem.itemId == itemId) {
                orders.push(cartItem);
            } else {
                orders.push(orderItem);
            }
        });
        cart.orderDetail.orders = orders;
        dispatch(removeDeliveryAndPackagingFromCart(cart));
        if (cart.orderDetail.source == "COD") {
            dispatch(addDeliveryAndPackagingToCart(cart, skipDeliveryPackaging));
        }/* else {
            dispatch(removeDeliveryAndPackagingFromCart(cart));
        }*/
        if (!appUtil.checkEmpty(cart.orderDetail.transactionDetail)) {
            cart = appUtil.calculateTaxes(cart, isInterState);
        }
        dispatch(updateCart(cart));
    }
}

export function removeOfferInitCart(val) {
    return dispatch => {
        dispatch({type: "OFFER_REMOVED_IN_INIT_CART", payload: val});
    }
}

export function setCartStock(unitInventory) {
    return dispatch => {
        var cartInventory = [];
        var order = !appUtil.checkEmpty(StorageUtils.getCartDetail()) ? StorageUtils.getCartDetail().orderDetail : null;
        if (order != null) {
            _.map(order.orders, (oi)=> {
                if (unitInventory[oi.productId] != null) {
                    cartInventory[oi.productId] = (cartInventory[oi.productId] != null ? cartInventory[oi.productId] - oi.quantity : unitInventory[oi.productId] - oi.quantity);
                }
            })
        }
        dispatch({type: "SET_CART_INVENTORY", payload: cartInventory});
    }
}

export function updateCartStock(stock) {
    return dispatch => {
        dispatch({type: "SET_CART_INVENTORY", payload: stock});
    }
}

export function syncCartAndLogin() {
    return dispatch => {
        var cart = StorageUtils.getCartDetail();
        if (cart.orderDetail.source == null || typeof cart.orderDetail.source == 'undefined' || cart.orderDetail.source.length == 0) {
            var crit = StorageUtils.getLocalityMetadata().criteria;
            if(crit==null || crit.length==0){
                browserHistory.push("/menu");
                dispatch(UtilityActions.showPopup("Something went wrong. Please check cart!", "info"));
            }else{
                cart.orderDetail.source = appUtil.getSourceFromCriteria(crit);
            }
        }
        if (cart.orderDetail.unitId == null || appUtil.checkEmpty(cart.orderDetail.unitName)) {
            cart.orderDetail.unitId = StorageUtils.getUnitDetails().id;
            cart.orderDetail.unitName = StorageUtils.getUnitDetails().name;
        }
        dispatch(updateCart(cart));
        trackUtils.trackInitiateCheckout(cart);
        dispatch(UtilityActions.showFullPageLoader("Syncing your cart. Please wait!"));
        dispatch({type: "SYNC_CART_PENDING"});
        axios({
            method: "POST",
            url: apis.getUrls().cart.sync,
            data: JSON.stringify(cart),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.data.code == 1) {
                dispatch({type: "SYNC_CART_FULFILLED"});
                const criteria = StorageUtils.getLocalityMetadata().criteria;
                dispatch({type: "SET_ADDRESS_VIEW_TYPE", payload: "DELIVERY"});
                browserHistory.push("/login");
                dispatch(UtilityActions.hideFullPageLoader());
            } else {
                dispatch({type: "SYNC_CART_REJECTED", payload: null});
                dispatch(UtilityActions.showPopup(response.data.msg, "error", 2000));
                dispatch(UtilityActions.hideFullPageLoader());
            }
        }).catch(function (error) {
            dispatch({type: "SYNC_CART_REJECTED", payload: error});
            dispatch(UtilityActions.showPopup("Error syncing cart. Please try later.", "error", 2000));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function updateCart(cart) {
    return dispatch => {
        var c = {...cart};
        dispatch({type: "UPDATE_CART", payload: c});
        StorageUtils.setCartDetail(c);
    }
}

export function updateGiftCardItem(cart, itemId) {
    return dispatch => {
        var cartx = Object.assign({}, cart);
        cartx.orderDetail.orders.map((item) => {
            if (item.itemId == itemId) {
                item.tax = 0;
                item.cardType = 'ECARD';
            }
        });
        dispatch(removeDeliveryAndPackagingFromCart(cartx));
        if (!appUtil.checkEmpty(cartx.orderDetail) && !appUtil.checkEmpty(cartx.orderDetail.transactionDetail)) {
            cartx = appUtil.calculateTaxes(cartx, isInterState);
        }
        dispatch(updateCart(cartx));
    }
}