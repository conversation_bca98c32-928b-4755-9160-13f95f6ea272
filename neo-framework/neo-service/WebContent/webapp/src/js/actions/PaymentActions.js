import axios from "axios";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import apis from "../APIs";
import { browserHistory } from "react-router";
import * as OrderManagementActions from "./OrderManagementActions";
import * as UtilityActions from "./UtilityActions";
import trackUtils from "../utils/TrackUtils";

export function payByRazorPay(cart, paymentMethod, timeoutsArray) {
    return dispatch => {
        if(StorageUtils.getCustomerDetail()!=null){
            trackUtils.trackPaymentModeSelected({paymentPartner:"RAZORPAY",mode:paymentMethod,
                orderMode:StorageUtils.getLocalityMetadata().criteria, amount:cart.orderDetail.transactionDetail.paidAmount});
            axios({
                method: "GET",
                url: apis.getUrls().payment.paymentKey,
                data: {},
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                if(response.data){
                    var pk = response.data;
                    axios({
                        method: "POST",
                        url: apis.getUrls().payment.createRazorPay,
                        data: JSON.stringify({cartId:StorageUtils.getCartDetail().cartId, paymentPartner:"RAZOR_PAY"}),
                        headers: {'Content-Type': 'application/json'}
                    }).then(function (response) {
                        if(!appUtil.checkEmpty(response.data) && response.data.receipt!=null){
                            var paymentRequest = response.data;
                            // doing payment by Razorpay
                            var options = {
                                key: pk,
                                amount: paymentRequest.amount,
                                name: "Chaayos",
                                description: "Payment For Bill " + paymentRequest.receipt,
                                image: "/img/favicon-96x96.png",
                                prefill: {
                                    name: StorageUtils.getCustomerDetail().name,
                                    email: StorageUtils.getCustomerDetail().email,
                                    contact: StorageUtils.getCustomerDetail().contact,
                                    method: paymentMethod
                                },
                                handler: function (response) {
                                    dispatch(setPaymentData({
                                        paymentMessage:"Processing payment. Please wait and do not refresh this page.",
                                        paymentStatus:"SUCCESS", failureMessage: null, showLoader:true
                                    }));
                                    response.externalOrderId = paymentRequest.receipt;
                                    dispatch(validateRzp(response));
                                    trackUtils.trackPaymentSuccess({paymentPartner:"RAZORPAY",mode:paymentMethod,orderMode:StorageUtils.getLocalityMetadata().criteria,
                                        amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:paymentRequest.receipt});
                                    dispatch(OrderManagementActions.orderCheckoutByWOId(paymentRequest.receipt, timeoutsArray));
                                },
                                modal: {
                                    ondismiss: function () {
                                        dispatch(cancelPayment(paymentRequest.receipt,"CANCELLED","CUSTOMER","Cancelled after opening.",""));
                                        trackUtils.trackPaymentFailed({paymentPartner:"RAZORPAY",mode:paymentMethod,orderMode:StorageUtils.getLocalityMetadata().criteria,
                                            amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:paymentRequest.receipt,reason:"Cancelled after opening."});
                                        dispatch(setPaymentData({
                                            paymentMessage:null, paymentStatus:"FAILED",
                                            failureMessage: "Payment failed. Please try again.",
                                            showLoader:false
                                        }));
                                    }
                                },
                                theme: {
                                    "color": "#5e7e47"
                                },
                                order_id: paymentRequest.id,
                                callback_url: apis.getUrls().paymentExternal.razorPayCheckoutAPI
                            };
                            var rzp1 = new Razorpay(options);
                            rzp1.open();
                        }else{
                            OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                            trackUtils.trackPaymentFailed({paymentPartner:"RAZORPAY",mode:paymentMethod,orderMode:StorageUtils.getLocalityMetadata().criteria,
                                amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:paymentRequest.receipt,reason:"Payment creation request returned wrong data"});
                            dispatch(setPaymentData({
                                paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
                                failureMessage: "Error in initiating payment request. Please try again."
                            }));
                        }
                    }).catch(function (error) {
                        OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                        trackUtils.trackPaymentFailed({paymentPartner:"RAZORPAY",mode:paymentMethod,orderMode:StorageUtils.getLocalityMetadata().criteria,
                            amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:paymentRequest.receipt,reason:"Payment creation request failed"});
                        dispatch(setPaymentData({
                            paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
                            failureMessage: "Error in initiating payment request. Please try again."
                        }));
                    })
                }else{
                    OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                    trackUtils.trackPaymentFailed({paymentPartner:"RAZORPAY",mode:paymentMethod,orderMode:StorageUtils.getLocalityMetadata().criteria,
                        amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:"NA",reason:"No payment key returned"});
                    dispatch(setPaymentData({
                        paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
                        failureMessage: "Something went wrong. Please try again."
                    }));
                }
            }).catch((error) => {
                    console.log(error);
                OrderManagementActions.slackCartId(StorageUtils.getCartDetail().cartId);
                trackUtils.trackPaymentFailed({paymentPartner:"RAZORPAY",mode:paymentMethod,orderMode:StorageUtils.getLocalityMetadata().criteria,
                    amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:"NA",reason:"Payment key request failed"});
                dispatch(setPaymentData({
                    paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
                    failureMessage: "Something went wrong. Please try again."
                }));
            });
        }else{
            dispatch(UtilityActions.showPopup("Please login to continue."));
            browserHistory.push("/login");
        }
    }
}

export function payByPaytm(cart) {
    return dispatch => {
        if(cart!=null){
            trackUtils.trackPaymentModeSelected({paymentPartner:"PAYTM",mode:"wallet",
                orderMode:StorageUtils.getLocalityMetadata().criteria, amount:cart.orderDetail.transactionDetail.paidAmount});
            axios({
                method: "POST",
                url: apis.getUrls().payment.createPaytm,
                    data: JSON.stringify({cartId:cart.cartId, paymentPartner:"PAYTM"}),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                var paymentRequest = response.data;
                // doing payment by paytm
                if(!appUtil.checkEmpty(paymentRequest) && paymentRequest!=null){
                    Object.keys(paymentRequest).forEach(function (k) {
                        if (paymentRequest[k] == null || paymentRequest[k].toString().trim() == "") {
                            delete paymentRequest[k];
                        }
                        if (typeof paymentRequest[k] == "number") {
                            paymentRequest[k] = paymentRequest[k].toFixed(2).toString();
                        }
                    });
                    if(paymentRequest!=undefined && paymentRequest!=null){
                        var host = location.host.toLowerCase();
                        var paymentHost = (host=="chaayos.com" || host=="m.chaayos.com" || host=="www.chaayos.com") ? "secure.paytm.in" : "pguat.paytm.com";
                        var form = '<form id="payForm" action="https://' + paymentHost + '/oltp-web/processTransaction" method="post">';
                        for (var key in paymentRequest) {
                            form += '<input type="hidden" name="' + key + '" value="' + paymentRequest[key] + '"/>';
                        }
                        form += '</form>';
                        var div = document.createElement('div');
                        div.setAttribute("id","payFormWrapper");
                        div.innerHTML = form;
                        document.getElementsByTagName("body")[0].appendChild(div);
                        document.getElementById("payForm").submit();
                    }
                }else{
                        OrderManagementActions.slackCartId(cart.cartId);
                    dispatch(setPaymentData({
                        paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
                        failureMessage: "Error in initiating payment request. Please try again"
                    }));
                }
            }).catch(function (error) {
                console.log(error);
                if(StorageUtils.getCartDetail()!=null){
                    OrderManagementActions.slackCartId(cart.cartId);
                }
                dispatch(setPaymentData({
                    paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
                    failureMessage: "Error in initiating payment request. Please try again"
                }));
            })
        }else{
            dispatch(UtilityActions.showPopup("Please login to continue."));
            browserHistory.push("/login");
        }
    }
}

export function payByCash(cart, timeoutsArray) {
    return dispatch => {
        trackUtils.trackPaymentModeSelected({paymentPartner:"CHAAYOS",mode:"CASH",
            orderMode:StorageUtils.getLocalityMetadata().criteria, amount:cart.orderDetail.transactionDetail.paidAmount});
        dispatch(OrderManagementActions.orderCheckout(1, timeoutsArray));
    }
}

export function setPaymentData(data) {
    return dispatch => {
        dispatch({type:"SET_PAYMENT_DATA", payload:data});
    }
}

export function setPaymentInitiated(data) {
    return dispatch => {
        dispatch({type:"SET_PAYMENT_INITIATED", payload:data});
        dispatch(setPaymentData({
            paymentMessage:"Initiating payment request. Please wait and do not refresh this page.",
            paymentStatus:"INITIATED",
            failureMessage: null,
            showLoader:true
        }));
    }
}

export function cancelPayment(receipt, status, cancelledBy, cancellationReason, failureReason) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().payment.cancel,
            data: JSON.stringify({receiptId:receipt,status:status,cancelledBy:cancelledBy,cancellationReason:cancellationReason,failureReason:failureReason}),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}

export function failurePayment(receipt, status, cancelledBy, cancellationReason, failureReason) {
    return dispatch => {
        OrderManagementActions.slackWOId(receipt);
        axios({
            method: "POST",
            url: apis.getUrls().payment.failure,
            data: JSON.stringify({receiptId:receipt,status:status,cancelledBy:cancelledBy,cancellationReason:cancellationReason,failureReason:failureReason}),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}

export function cancelPaymentBackend(error, reason) {
    return dispatch => {
        if(!appUtil.checkEmpty(error)){
            dispatch(failurePayment(error, "FAILED", "CUSTOMER", "", reason));
        }
        dispatch(setPaymentData({
            paymentMessage:null, paymentStatus:"FAILED", showLoader:false,
            failureMessage: "Payment failed. Please try again or call at 1800-120-2424 for support."
        }));
    }
}

export function validateRzp(obj) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().payment.validate,
            data: JSON.stringify(obj),
            headers: {'Content-Type': 'application/json'}
        }).then((response) => {

        }).catch((error) => {

        });
    }
}