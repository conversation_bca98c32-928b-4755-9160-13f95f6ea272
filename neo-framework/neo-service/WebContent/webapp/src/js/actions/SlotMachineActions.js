import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";
import trackUtils from "../utils/TrackUtils";
import * as UtilityActions from "./UtilityActions";
import * as MyOfferAction from "./MyOfferActions";

import {hideFullPageLoader, showFullPageLoader} from "./UtilityActions";



export const slotMachineStage = {
    onlySpinPage:"SPIN_MACHINE",
    getBasicInfo: 'GET_BASIC_INFO',
    getOTP: 'GET_OTP',
    showOffer: 'SHOW_OFFER'
};

export const gamifiedOfferStage = {
    gameHomeScreen:"GAME_HOME_SCREEN",
    gameScreen: 'GAME_SCREEN',
    leaderboardScreen: 'LEADERBOARD_SCREEN',
    signIn: 'SIGN_IN_SCREEN',
    yourScore: 'YOUR_SCORE',
    otp:'OTP_PAGE',
    offerScreen:'OFFER_SCREEN',
    gameRule:'GAME_RULE'
};

export const slotMachineJourneyState = {
    pageView: 'PAGE_VIEW',
    spinMachine: 'SPIN_MACHINE',
    spinMachineFlow2: 'SPIN_MACHINE_FLOW_2',
    newOfferPage: 'NEW_OFFER_PAGE',
    noOfferPage: 'NO_OFFER_PAGE',
    existingOfferPage: 'EXISTING_OFFER_PAGE',
    exploreMenuClicked: 'EXPLORE_MENU_CLICKED'
}

export const slotMachineItems = [
    "slotGame1.webp",
    "slotGame2.svg",
    "slotGame4.svg",
    "slotGame5.svg",
    "slotGame6.svg",
    "slotGame9.webp",
    "slotGame11.svg",
    "slotGame12.svg",
    "slotGame13.svg",
    "slotGame14.svg",
];

export function updateSlotMachineStage(stage) {
    return dispatch => {
        if (stage !== null && Object.values(slotMachineStage).includes(stage)) {
            dispatch({type: "SET_SLOT_GAME_STAGE", payload: stage});
        }
    }
}

export function updateShowNBOOffer(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_NBO_OFFER", payload: value});
    }
}

export function updateEnableShowOfferButton(value) {
    return dispatch => {
        dispatch({type: "ENABLE_SHOW_OFFER_BUTTON", payload: value});
    }
}

export function updateButtonLoader(value) {
    return dispatch => {
        dispatch({type: "SET_BUTTON_LOADER", payload: value});
    }
}

export function clearReducer() {
    return dispatch => {
        dispatch({type: "CLEAR_REDUCER", payload: null});
    }
}

export function updateShowDNBOOffer(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_DNBO_OFFER", payload: value});
    }
}

export function updateShowChaayosCashOffer(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_CHAAYOS_CASH_OFFER", payload: value});
    }
}

export function updateShowMembershipOffer(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_MEMBERSHIP_OFFER", payload: value});
    }
}

export function updateShowNoOffer(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_NO_OFFER", payload: value});
    }
}
export function updateScreenDimension(height, width) {
    return dispatch => {
        dispatch({type: "SET_SCREEN_HEIGHT", payload: height});
        dispatch({type: "SET_SCREEN_WIDTH", payload: width});
    }
}

export function setCampaignDetail(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_CAMPAIGN_DETAILS", payload: detail});
    }
}

export function setUtmData(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_UTM_DATA", payload: detail});
    }
}

export function setSpecialOffer(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_SPECIAL_OFFER", payload: detail});
    }
}

export function setTermAndConditions(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_TERM_AND_CONDITION", payload: detail});
    }
}

export function setGamifiedCurrentScreen(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_GAMIFIED_CURRENT_SCREEN", payload: detail});
    }
}

export function setGamifiedFutureScreen(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_GAMIFIED_FUTURE_SCREEN", payload: detail});
    }
}

export function setGameScore(score) {
    return dispatch => {
        if (score !== null)
            dispatch({type: "SET_GAME_SCORE", payload: score});
    }
}

export function setShowContactField(score) {
    return dispatch => {
        if (score !== null)
            dispatch({type: "SET_SHOW_CONTACT_FIELD", payload: score});
    }
}
export function setShowNameField(score) {
    return dispatch => {
        if (score !== null)
            dispatch({type: "SET_SHOW_NAME_FIELD", payload: score});
    }
}
export function setShowOtpField(score) {
    return dispatch => {
        if (score !== null)
            dispatch({type: "SET_SHOW_OTP_FIELD", payload: score});
    }
}

export function setGamifiedOffer(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_GAMIFIED_OFFER", payload: data});
    }
}

export function setContactNumber(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_CONTACT_NUMBER", payload: data});
    }
}

export function setCustomerName(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_CUSTOMER_NAME", payload: data});
    }
}

export function setSessionKey(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_SESSION_KEY", payload: data});
    }
}

export function setCustomerEmail(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_EMAIL", payload: data});
    }
}

export function setCustomerOtp(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_OTP", payload: data});
    }
}

export function setLeaderboardData(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_LEADERBOARD_DATA", payload: data});
    }
}

export function setAuthToken(data) {
    return dispatch => {
        if (data !== null)
            dispatch({type: "SET_AUTH_TOKEN", payload: data});
    }
}

export function setRefLink(value) {
    return dispatch => {
        dispatch({type: "SET_REF_LINK", payload: value});
    }
}

export function setShowGame(value) {
    return dispatch => {
        dispatch({type: "SET_SHOW_GAME", payload: value});
    }
}

export function setStartGame(value) {
    return dispatch => {
        dispatch({type: "SET_START_GAME", payload: value});
    }
}

export function setScreenshotString(value) {
    return dispatch => {
        dispatch({type: "SET_SCREENSHOT_STRING", payload: value});
    }
}

export function getSpecialOffer(currentStage,contact,campaignToken,name,email,otp,utmSource,utmMedium,token,flow,callback,opt,deviceType) {
    return (dispatch,getState) => {
        dispatch(updateButtonLoader(true));
        if(flow != 2){
            fbq('trackCustom', 'MachineGamePlayed');
            trackUtils.trackSlotMachineGamePlayed({});
        }
        dispatch(showFullPageLoader("Creating offer..."))
        const {currentStage} = getState().slotMachineReducer;
        if(currentStage === slotMachineStage.getBasicInfo){
            var data ={
                "contact":contact,
                "customerEmail":!appUtil.checkEmpty(email) ? email.toLowerCase() : email,
                "campaignToken":campaignToken,
                "authToken":token,
                "utmSource":utmSource,
                "utmMedium":utmMedium,
                "flow":parseInt(flow),
                "whatsappOpt":opt
            }
            axios({
                method: "POST",
                url: apis.getUrls().customer.lookupAndGetOffer,
                data: data,
                headers: {'Content-Type': 'application/json'}
            }).then((response)=>{
                if(response != null && !appUtil.checkEmpty(response.data)){
                    if(response.data.key === -1){
                        dispatch(updateSlotMachineStage(slotMachineStage.getOTP));
                    }else{
                        dispatch(setSpecialOffer(response.data.value[0]));
                        dispatch(updateSlotMachineStage(slotMachineStage.showOffer));
                        if(!response.data.value[0].existingOffer) {
                            fbq('trackCustom', 'MachineGameOfferGenerated');
                            trackUtils.trackSlotMachineGameOfferGenerated({});
                            if(response.data.value[0].offerType === "NO_OFFER"){
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                            }else{
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.newOfferPage,deviceType,contact))
                            }
                        }else{
                            if(response.data.value[0].offerType === "NO_OFFER"){
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                            }else{
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.existingOfferPage,deviceType,contact))
                            }
                        }
                        dispatch(showPopUp(response.data.value[0]));
                        if(response.data.value[0].offerType !=null && response.data.value[0].offerType !== "NO_OFFER"){
                            callback();
                        }
                        if(window.ReactNativeWebView != null){
                            if(response.data.value[0].existingOffer == false) {
                                let offerText;
                                if(response.data.value[0].offerType == "DNBO") {
                                    offerText = "Customer has got " + response.data.value[0].text + " on next order on Zomato. Use code "
                                        + response.data.value[0].offerCode + ". Valid till " + response.data.value[0].validityTill;
                                }
                                if(response.data.value[0].offerType == "NBO") {
                                    offerText = "Customer has got " + response.data.value[0].text + " on next visit at Chaayos. Use code "
                                        + response.data.value[0].offerCode + ". Valid till " + response.data.value[0].validityTill;
                                }
                                if(response.data.value[0].offerType == "CHAAYOS_CASH") {
                                    offerText = "Customer has got Rs. " + response.data.value[0].chaayosCash + " Chaayos cash. " +
                                        "Pay with Chaayos cash on your next visit & get free items";
                                }
                                if(response.data.value[0].offerType == "MEMBERSHIP") {
                                    offerText = "Customer has got Chaayos Select Membership worth Rs. 199 for Free. Get " +
                                        response.data.value[0].text + " on all Chaayos visits till " + response.data.value[0].validityTill;
                                }
                                window.ReactNativeWebView.postMessage(JSON.stringify({offer : offerText}));
                            }
                            setTimeout(function () {
                                window.ReactNativeWebView.postMessage(JSON.stringify({action : "CLOSE"}));
                            }, 15000);
                        }
                        dispatch(setTermAndConditions(getListOfTnC(response.data.value[0])))
                    }
                }
            }).catch((e)=>{
                console.log("Error while getting offer",e)
            })
        }else if(currentStage === slotMachineStage.getOTP){
            var data ={
                "contact":contact,
                "campaignToken":campaignToken,
                "authToken":token,
                "utmSource":utmSource,
                "utmMedium":utmMedium,
                "name":capitalizeName(name),
                "otp":otp,
                "customerEmail":!appUtil.checkEmpty(email) ? email.toLowerCase() : email,
                "acquisitionToken":"CHAI_FRAPPE",
                "flow":parseInt(flow),
                "whatsappOpt":opt
            }
            axios({
                method: "POST",
                url: apis.getUrls().customer.registerAndGetOffer,
                data: data,
                headers: {'Content-Type': 'application/json'}
            }).then((response)=>{
                if(response != null && !appUtil.checkEmpty(response.data)){
                    dispatch(setSpecialOffer(response.data));
                    dispatch(updateSlotMachineStage(slotMachineStage.showOffer));
                    if(response.data[0].offerType !=null && response.data[0].offerType !== "NO_OFFER"){
                        callback();
                    }
                    if(!response.data[0].existingOffer) {
                        fbq('trackCustom', 'MachineGameOfferGenerated');
                        trackUtils.trackSlotMachineGameOfferGenerated({});
                        if(response.data[0].offerType === "NO_OFFER"){
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                        }else{
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.newOfferPage,deviceType,contact))
                        }
                    }else{
                        if(response.data[0].offerType === "NO_OFFER"){
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                        }else{
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.existingOfferPage,deviceType,contact))
                        }
                    }
                    dispatch(showPopUp(response.data[0]));
                    dispatch(setTermAndConditions(getListOfTnC(response.data[0])));
                }
                dispatch(hideFullPageLoader());
            }).catch((e)=>{
                dispatch(hideFullPageLoader());
                console.log("Error while getting offer",e)
            })
        }
        dispatch(hideFullPageLoader());
        this.props.dispatch(SlotMachineAction.updateButtonLoader(true));
    }
}

export function getGamifiedOffer(currentStage,contact,campaignToken,name,email,otp,utmSource,utmMedium,token,flow,callback,opt,deviceType) {
    return (dispatch,getState) => {
        if(flow != 2){
            fbq('trackCustom', 'MachineGamePlayed');
            trackUtils.trackSlotMachineGamePlayed({});
        }
        dispatch(showFullPageLoader("Creating offer..."))
        const {currentStage} = getState().slotMachineReducer;
        const {futureScreen,contactNumber,sessionKey,gameScore} = getState().gamifiedOfferReducer;
        if(currentStage === slotMachineStage.getBasicInfo){
            var data ={
                "contact":contact,
                "customerEmail":!appUtil.checkEmpty(email) ? email.toLowerCase() : email,
                "campaignToken":campaignToken,
                "authToken":token,
                "utmSource":utmSource,
                "utmMedium":utmMedium,
                "flow":parseInt(flow),
                "whatsappOpt":opt
            }
            axios({
                method: "POST",
                url: apis.getUrls().customer.lookupAndGetOffer,
                data: data,
                headers: {'Content-Type': 'application/json'}
            }).then((response)=>{
                if(response != null && !appUtil.checkEmpty(response.data)){
                    if(response.data.key === -1){
                        dispatch(setShowContactField(false));
                        dispatch(setShowNameField(true));
                        dispatch(setShowOtpField(true));
                    }else{
                        dispatch(setShowContactField(false));
                        dispatch(setShowNameField(false));
                        dispatch(setShowOtpField(true));
                        dispatch(setGamifiedOffer(response.data.value));
                        dispatch(setGamifiedCurrentScreen(gamifiedOfferStage.offerScreen));
                        if(!response.data.value.existingOffer) {
                            fbq('trackCustom', 'MachineGameOfferGenerated');
                            trackUtils.trackSlotMachineGameOfferGenerated({});
                            if(response.data.value.offerType === "NO_OFFER"){
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                            }else{
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.newOfferPage,deviceType,contact))
                            }
                        }else{
                            if(response.data.value.offerType === "NO_OFFER"){
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                            }else{
                                dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.existingOfferPage,deviceType,contact))
                            }
                        }
                        dispatch(showPopUp(response.data.value));
                        dispatch(setTermAndConditions(getListOfTnC(response.data.value)))
                    }
                }
            }).catch((e)=>{
                console.log("Error while getting offer",e)
            })
        }else if(currentStage === slotMachineStage.getOTP){
            var data ={
                "contact":contact,
                "campaignToken":campaignToken,
                "authToken":token,
                "utmSource":utmSource,
                "utmMedium":utmMedium,
                "name":capitalizeName(name),
                "otp":otp,
                "customerEmail":!appUtil.checkEmpty(email) ? email.toLowerCase() : email,
                "acquisitionToken":"CHAI_FRAPPE_LAUNCH",
                "flow":parseInt(flow),
                "whatsappOpt":opt,
                "createOffer": true,
                "sessionToken":sessionKey,
                "gameScore":gameScore
            }
            axios({
                method: "POST",
                url: apis.getUrls().customer.registerAndGetOffer,
                data: data,
                headers: {'Content-Type': 'application/json'}
            }).then((response)=>{
                if(response != null && !appUtil.checkEmpty(response.data)){
                    dispatch(updateSlotMachineStage(slotMachineStage.showOffer));
                    if(response.data[0].offerType === "OFFER_NOT_CREATED"){
                        dispatch(setSessionKey(response.data[0].sessionToken))
                        if(futureScreen === gamifiedOfferStage.leaderboardScreen){
                            dispatch(getLeaderBoardData(contactNumber,campaignToken))
                            dispatch(setGamifiedCurrentScreen(futureScreen))
                            dispatch(setGamifiedFutureScreen(null))
                            return;
                        }
                        dispatch(setGamifiedCurrentScreen(futureScreen))
                        dispatch(setGamifiedFutureScreen(null))
                    }if(response.data[0].offerType==="OTP_FAILED"){
                        dispatch(UtilityActions.showPopup("OTP verification failed! try again","error",3000));
                        dispatch(hideFullPageLoader());
                        return;
                    }if(response.data[0].offerType==="MAX_OTP_LIMIT"){
                        dispatch(UtilityActions.showPopup("Max OTP limit reached","error",3000));
                        dispatch(hideFullPageLoader());
                        return;
                    }
                    dispatch(setGamifiedOffer(response.data));

                    dispatch(setRefLink(getReferralMessage(response.data[0].refCode,campaignToken)))
                    dispatch(setGamifiedCurrentScreen(gamifiedOfferStage.offerScreen))
                    if(response.data[0].offerType !=null && response.data[0].offerType !== "NO_OFFER" && response.data[0].offerType !== "OFFER_NOT_CREATED"){
                        callback();
                    }
                    if(!response.data[0].isExistingOffer) {
                        fbq('trackCustom', 'NinjaGameOfferGenerated');
                        trackUtils.trackNinjaGameOfferGenerated({});
                        if(response.data[0].offerType === "NO_OFFER"){
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                        }else{
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.newOfferPage,deviceType,contact))
                        }
                    }else{
                        if(response.data[0].offerType === "NO_OFFER"){
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.noOfferPage,deviceType,contact))
                        }else{
                            dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.existingOfferPage,deviceType,contact))
                        }
                    }
                    dispatch(hideFullPageLoader());
                    dispatch(showPopUp(response.data));
                    dispatch(setTermAndConditions(getListOfTnC(response.data[0])));
                }
            }).catch((e)=>{
                dispatch(hideFullPageLoader());
                console.log("Error while getting offer",e)
            })
        }

    }
}

export function showPopUp(data){
    return dispatch=>{
        if(data.offerType === "NBO"){
            dispatch(updateShowNBOOffer(true));
        }else if(data.offerType === "DNBO"){
            dispatch(updateShowDNBOOffer(true))
        }else if(data.offerType === "MEMBERSHIP"){
            dispatch(updateShowMembershipOffer(true))
        }else if(data.offerType === "CHAAYOS_CASH"){
            dispatch(updateShowChaayosCashOffer(true))
        }else{
            dispatch(updateShowNoOffer(true));
        }
    }
}

export function getCampaignDetail(token,contactFromUrl,deviceType,stage,utmSource,utmMedium,authToken){
    return dispatch=>{
        dispatch(setIsCampaignLoading(true));
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().offer.getCampaignDetails,
            data: JSON.stringify(token),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch(setIsCampaignLoading(false));
            if (response.data != null) {
                var campaignDetailData = response.data;
                if(campaignDetailData.endDate < new Date() || campaignDetailData.startDate > new Date()){
                    window.location.replace("https://cafes.chaayos.com/notfound");
                }
                dispatch(setCampaignDetail(campaignDetailData));
                dispatch(updateSlotMachineStage(slotMachineStage.getBasicInfo));
                // if(contactFromUrl !== undefined && contactFromUrl.trim().length === 10){
                //     dispatch(getSpecialOffer(stage,contactFromUrl,campaignDetailData.campaignId,null,null,null,utmSource,utmMedium,authToken))
                // }
            }else{
                window.location.replace("https://cafes.chaayos.com/notfound");
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            console.log("Error while fetching campaign detail");
            dispatch(setIsCampaignLoading(false));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function getLeaderBoardData(contact, token,deviceType){
    return (dispatch,getState)=>{
        dispatch(UtilityActions.showFullPageLoader("Loading Leaderboard ..."));
        const {sessionKey} = getState().gamifiedOfferReducer;
        axios({
            method: "POST",
            url: apis.getUrls().customer.leaderboard+"?contact="+contact+"&token="+token+"&getRank=true",
            data: JSON.stringify({"sessionToken":sessionKey}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data != null) {
                dispatch(setLeaderboardData(response.data))
                dispatch(setGamifiedCurrentScreen(gamifiedOfferStage.leaderboardScreen))
                this.props.dispatch(MyOfferAction.recordJourney(gamifiedOfferStage.leaderboardScreen,"mobile",null))
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            console.log("Error while fetching campaign detail");
            dispatch(setIsCampaignLoading(false));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}

export function setIsCampaignLoading(value) {
    return dispatch => {
        dispatch({type: "IS_CAMPAIGN_LOADING", payload: value});
        if(value){
            document.getElementsByClassName("splash")[0] != null ? document.getElementsByClassName("splash")[0].className += " inactive" : null;
        }else{
            if (document.getElementsByClassName("splash")[0] != null) {
                document.getElementsByClassName("splash")[0].parentNode.removeChild(document.getElementsByClassName("splash")[0]);
            }
        }
    }
}

 export function getListOfTnC(offer){
    var tNCs = [];
    if(offer.offerType === "NO_OFFER"){
        return null;
    }
    if(offer.offerTnCString == null){
        offer.offerTnCString ="";
    }
    tNCs.push("Valid from "+getDateString(offer.validityFrom)+" till "+getDateString(offer.validityTill));
    tNCs=tNCs.concat(offer.offerTnCString.split("#"))
     var tncHtml =``;
    tNCs.map((value => {
        if(value !== ""){
            tncHtml = tncHtml + `${value}` + `<br/>`
        }
    }))
     document.getElementById("tnc").innerHTML=tncHtml
     return tNCs;
}

function getDateString(date){
    return new Date(date).toDateString().substring(8,10)+" "+
        new Date(date).toDateString().substring(4,7)+" "+
        new Date(date).toDateString().substring(11)
}

function capitalizeName(name){
    if(name == null){
        return null;
    }
    const words = name.split(" ");
    for (let i = 0; i < words.length; i++) {
        words[i] = words[i][0].toUpperCase() + words[i].substr(1);
    }
    return words.join(" ");
}

export function copyCodeToClipboard(data, message){
    return dispatch=>{
        var isCopied=false;
        if (navigator.clipboard && window.isSecureContext) {
            isCopied = true;
            navigator.clipboard.writeText(data);
        } else {
            let textArea = document.createElement("textarea");
            textArea.value = data;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            var isCopied = document.execCommand('copy');
            textArea.blur();
        }
        if(isCopied){
            dispatch(UtilityActions.showPopup(message, "info", 2000));
        }
    }
}

function getReferralMessage(refCode, token){
    var link ="https://cafes.chaayos.com/ninjaRef/"+refCode;
    return {
        message:"Just played the Chaayos' #ChaiFrappeNinja game. I'm on the leader board. Beat that & win exciting gifts\n" +
            "Use this link to play Game\n",
        url:link
    }
}




