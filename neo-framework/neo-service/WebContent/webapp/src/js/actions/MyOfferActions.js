import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";

var moment = require('moment');
import * as UtilityActions from "./UtilityActions";
import {closeModal} from "./CustomizationModalActions";
import {getUnitProductsForSignUpOffer} from "./OutletMenuActions";
import trackUtils from "../utils/TrackUtils";
import {app} from "firebase/app";
import AppUtil from "../AppUtil";

export const defaultSize = '';
export const defaultUnitId = 12018;
export const dateFormat = 'DD - MM - YYYY';
export const myOfferStage = {
    getBasicInfo: 'GET_BASIC_INFO',
    getOTP: 'GET_OTP',
    getName:'GET_NAME',
    getProductAndDeliveryInfo: 'GET_PRODUCT_AND_DELIVERY_INFO',
    showCongratulationView: 'SHOW_CONGRATULATION_VIEW',
    offerAlreadyAvailedView: 'OFFER_ALREADY_AVAILED_VIEW',
    newCustomerNoOfferView: 'NEW_CUSTOMERS_NO_OFFER_VIEW',
    showCouponView: 'SHOW_COUPON_VIEW',
    secondFreeChaiView: 'SECOND_FREE_CHAI_VIEW',
    noOfferView: 'NO_OFFER_VIEW'
};

export const journeyStage= {
    "pageView":"PAGE_VIEW",
    "signUp":"SIGNED_UP",
    "otpPage":"OTP_PAGE",
    "couponFound":"COUPON_FOUND_PAGE",
    "noCouponFound":"NO_COUPON_FOUND_PAGE",
    "clickedVisitChaayos":"VISIT_CHAAYOS_CLICKED",
    "clickedOrderOnZomato":"ORDER_ON_ZOMATO_CLICKED"
}

export const timeSlot = {
    slotOne: '8:00 AM - 10:00 AM',
    slotTwo: '12:00 PM - 2:00 PM',
    slotThree: '2:00 PM - 4:00 PM',
    slotFour: '4:00 PM - 6:00 PM',
    slotFive: '6:00 PM - 8:00 PM',
    slotSix: '8:00 PM - 11:00 PM'
};

export  function getJourneyStage(stage){
    switch (stage){
        case myOfferStage.getBasicInfo:
            return journeyStage.contact;
        case myOfferStage.getOTP:
            return journeyStage.otp;
        case myOfferStage.getName:
            return journeyStage.name;
        case myOfferStage.noOfferView:
            return journeyStage.noCouponFound;
        case myOfferStage.secondFreeChaiView:
            return journeyStage.secondFreeChai;
        case myOfferStage.showCouponView:
            return journeyStage.couponFound
        default:
            return null
    }

}

 function getOfferPixelData(offerData, contactNumber){
    var pixelData = [];
    offerData.forEach((data)=>{
        pixelData.push({
            customer_name:data.customerName,
            contact_number:contactNumber,
            offer_desc:data.offerDesc,
            coupon_code:data.offerCouponCode,
            campaign_id:data.campaignId,
            valid_from:data.validityFrom,
            valid_till:data.validityTill,
            is_old_offer:data.existingOffer,
        })
    })
    return pixelData;
}

export function getAvailableTimeSlots(deliveryDate) {
    return dispatch => {
        if (deliveryDate != null) {
            dispatch(UtilityActions.showFullPageLoader(""));

            console.log("Static time slot::: " + deliveryDate);
            let timeSlots = [];
            if (deliveryDate !== null && isToday(deliveryDate)) {
                let currentHour = new Date().getHours();
                console.log("currentHour:: " + currentHour);
                if (currentHour < 8) {
                    timeSlots.push({value: timeSlot.slotOne, label: timeSlot.slotOne});
                }
                if (currentHour < 12) {
                    timeSlots.push({value: timeSlot.slotTwo, label: timeSlot.slotTwo});
                }
                if (currentHour < 14) {
                    timeSlots.push({value: timeSlot.slotThree, label: timeSlot.slotThree});
                }
                if (currentHour < 16) {
                    timeSlots.push({value: timeSlot.slotFour, label: timeSlot.slotFour});
                }
                if (currentHour < 18) {
                    timeSlots.push({value: timeSlot.slotFive, label: timeSlot.slotFive});
                }
                if (currentHour < 20) {
                    timeSlots.push({value: timeSlot.slotSix, label: timeSlot.slotSix});
                }
            } else {
                timeSlots.push({value: timeSlot.slotOne, label: timeSlot.slotOne});
                timeSlots.push({value: timeSlot.slotTwo, label: timeSlot.slotTwo});
                timeSlots.push({value: timeSlot.slotThree, label: timeSlot.slotThree});
                timeSlots.push({value: timeSlot.slotFour, label: timeSlot.slotFour});
                timeSlots.push({value: timeSlot.slotFive, label: timeSlot.slotFive});
                timeSlots.push({value: timeSlot.slotSix, label: timeSlot.slotSix});
            }
            dispatch({type: "SET_AVAILABLE_TIME_SLOTS", payload: timeSlots});
            deliveryDate = deliveryDate.format(dateFormat).toString();
            let data = {"name": deliveryDate};
            axios({
                method: "POST",
                url: apis.getUrls().customer.signUpOfferTimeSlots,
                data: data,
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response != null && !appUtil.checkEmpty(response.data)) {
                    console.log("getAvailableTimeSlots response.data!! " + JSON.stringify(response.data));
                    if (!appUtil.checkEmpty(response.data) && !appUtil.checkEmpty(timeSlots)) {
                        let slotCountData = response.data;
                        let allowedTimeSlots = [];
                        timeSlots.map(slot => {
                            if (slotCountData.slotOneName === slot.value && slotCountData.slotOneCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotOne, label: timeSlot.slotOne})
                            }
                            if (slotCountData.slotTwoName === slot.value && slotCountData.slotTwoCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotTwo, label: timeSlot.slotTwo});
                            }
                            if (slotCountData.slotThreeName === slot.value && slotCountData.slotThreeCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotThree, label: timeSlot.slotThree});
                            }
                            if (slotCountData.slotFourName === slot.value && slotCountData.slotFourCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotFour, label: timeSlot.slotFour});
                            }
                            if (slotCountData.slotFiveName === slot.value && slotCountData.slotFiveCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotFive, label: timeSlot.slotFive});
                            }
                            if (slotCountData.slotSixName === slot.value && slotCountData.slotSixCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotSix, label: timeSlot.slotSix});
                            }
                        });
                        dispatch({type: "SET_AVAILABLE_TIME_SLOTS", payload: allowedTimeSlots});
                    }
                }
                dispatch(UtilityActions.hideFullPageLoader());
            }).catch(function (error) {
                console.log("getAvailableTimeSlots error" + error);
                dispatch(UtilityActions.hideFullPageLoader());
            });
        }
    }
}

export const isToday = (someDate) => {
    if (someDate != null) {
        let date = new Date(someDate);
        const today = new Date();
        console.log('selected' + date);
        console.log('today' + date);
        return date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear()
    }
    return false;
};

export const staticCities = [
    {value: 'Gurgaon', label: 'Gurgaon'},
    {value: 'Delhi', label: 'Delhi'},
    {value: 'Noida', label: 'Noida'},
    {value: 'Chandigarh', label: 'Chandigarh'},
    {value: 'Mumbai', label: 'Mumbai'},
    {value: 'Bangalore', label: 'Bangalore'},
    {value: 'Pune', label: 'Pune'}
];

export const shareData = {
    title: 'Free Desi Chai Ketli',
    text: 'Signup and get a free chai delivered at your door step.',
    url: 'https://onelink.chaayos.com/fcd_share_mobile',
    urlForFB: 'https://onelink.chaayos.com/fcd_share_fb',
    urlForWhatsapp: 'https://onelink.chaayos.com/fcd_share_whatsapp',
    urlForTwitter: 'https://onelink.chaayos.com/fcd_share_twitter',
    urlForEmail: 'https://onelink.chaayos.com/fcd_share_email'
};

export function getAllowedDays(fromDate) {
    let allowedInterval = 8;
    let momentsAllowed = [];
    //momentsAllowed.push(moment(fromDate));
    for (let i = 2; i < allowedInterval; i++) {
        let result = new Date();
        result.setDate(result.getDate() + i);
        momentsAllowed.push(moment(result));
    }
    return momentsAllowed;
}

export function completeCustomization(productObj) {
    return dispatch => {
        dispatch({
            type: "SET_CUSTOMIZED_OFFER_PRODUCT",
            payload: appUtil.updateCartLessProductCustomizations(productObj)
        });
        dispatch(closeModal());
    }
}

export function updateJourneyData(journey) {
    return dispatch => {
        dispatch({
            type: "MY_OFFER_JOURNEY",
            payload: journey
        });
    }
}

export function updateSignUpOfferStage(stage) {
    return dispatch => {
        if (stage !== null && Object.values(myOfferStage).includes(stage)) {
            dispatch({type: "SET_MY_OFFER_STAGE", payload: stage});
            // dispatch(recordJourney(stage));
        }
    }
}

export function setSignUpOfferCustomer(customer) {
    return dispatch => {
        if (customer !== null)
            dispatch({type: "SET_SIGN_UP_OFFER_CUSTOMER", payload: customer});
    }
}

export function setCampaignDetail(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_CAMPAIGN_DETAILS", payload: detail});
    }
}

export function setUtmData(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_UTM_DATA", payload: detail});
    }
}

export function setCustomerName(detail) {
    return dispatch => {
        if (detail !== null)
            dispatch({type: "SET_CUSTOMER_NAME", payload: detail});
    }
}

export function setIsCampaignLoading(value) {
    return dispatch => {
        dispatch({type: "IS_CAMPAIGN_LOADING", payload: value});
        if(value){
            document.getElementsByClassName("splash")[0] != null ? document.getElementsByClassName("splash")[0].className += " inactive" : null;
        }else{
            if (document.getElementsByClassName("splash")[0] != null) {
                document.getElementsByClassName("splash")[0].parentNode.removeChild(document.getElementsByClassName("splash")[0]);
            }
        }
    }
}

export function verifySignUpOfferCustomer(name, contact, otp, campaignId,deviceType) {
    return (dispatch,getState) => {
        dispatch(UtilityActions.showFullPageLoader(""));
        const {utmData} = getState().myOfferReducer
        axios({
            method: "POST",
            url: apis.getUrls().customer.myOffer,
            // url: "http://localhost:8080/neo-service/rest/v1/c/my-offer",
            data: JSON.stringify({
                name: name,
                contact: contact,
                otp: otp,
                isOtpVerified: false,
                campaignId: campaignId,
                utmSource: utmData.utmSource,
                utmMedium: utmData.utmMedium
            }),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response != null && !appUtil.checkEmpty(response.data)) {
                //Default unit set to Cyber Green
                dispatch(recordJourney(journeyStage.signUp,deviceType,contact,function (){
                    console.log("verifySignUpOfferCustomer response.data!! " + JSON.stringify(response.data));
                    var validOffers = [];
                    response.data.forEach((data)=>{
                        dispatch({type: "SET_CUSTOMER_NAME", payload: data.customerName});
                        if(data.offerStatus !== undefined && data.offerStatus === "OFFER_FOUND"){
                            validOffers.push(data);
                        }
                    });
                    dispatch({type: "SET_MY_OFFER_DETAIL", payload: validOffers});
                    if(validOffers.length <= 0){
                        console.log("No coupon code available");
                        fbq('track','NoOfferFound',{contact: contact, campaignId: campaignId})
                        trackUtils.trackMyOfferNoOfferFound({contact: contact, campaignId: campaignId})
                        dispatch(recordJourney(journeyStage.noCouponFound,deviceType))
                        dispatch(updateSignUpOfferStage(myOfferStage.noOfferView));
                    } else{
                        fbq('track','OfferFound',{offer_data:getOfferPixelData(response.data,contact)});
                        trackUtils.trackMyOfferOfferFound({offer_data:getOfferPixelData(response.data,contact)})
                        dispatch(recordJourney(journeyStage.couponFound,deviceType))
                        dispatch(updateSignUpOfferStage(myOfferStage.showCouponView));
                    }
                }))

            }else {
                // trackUtils.trackVerifySignUpOfferCustomer({"customer": null});
                dispatch(UtilityActions.showPopup("Error verifying OTP. Please enter correct OTP", "error", 2000));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            // trackUtils.trackVerifySignUpOfferCustomer({"customer": null});
            console.log("error" + error);
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error in OTP verification. Please try again!", "error", 2000));
        });
    }
}



export function updateName(name, contact, campaignId, offerDetail){
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().customer.signUpVerified,
            data: JSON.stringify({
                name: name,
                contact: contact,
                loginNeo: false,
                campaignId: campaignId
            }),
            headers: {'Content-Type': 'application/json'}
        }).then((response)=>{
            if(!appUtil.checkEmpty(response.data)){
                offerDetail.customerName = response.data.name;
                dispatch(updateSignUpOfferStage(myOfferStage.getOTP));
            }else{
                dispatch(UtilityActions.showPopup("Unable to save name", "error", 2000));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch((error)=>{
            console.log("error" + error);
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Unable to update name", "error", 2000));
        })
    }
}

function getTimestampForJourney(){
    const today = new Date();
    return today.getDate()+"_"+today.getMonth()+"_"+today.getFullYear()+"_"+today.getHours()+":"+today.getMinutes()+":"+today.getSeconds();
}

function getParam(p) {
    var match = RegExp('[?&]' + p + '=([^&]*)').exec(window.location.search);
    return match && decodeURIComponent(match[1].replace(/\+/g, ' '));
}

export function recordJourney(stage,deviceType,contact,callback){
    return (dispatch, getState) => {
        const {myOfferJourney,utmData,campaignDetails,contactNumber} = getState().myOfferReducer;
        if(AppUtil.checkEmpty(myOfferJourney) || stage === myOfferStage.getBasicInfo){
            var googleClickId = null;
            try {
                googleClickId = getParam('gclid');
            } catch (e) {
                console.error("Error while extracting Gclid", e);
            }
            var journey = {
                sessionStartTime : new Date(),
                utmSource:utmData.utmSource,
                utmMedium:utmData.utmMedium,
                finalState:stage,
                campaignId:campaignDetails.campaignId,
                deviceType:deviceType,
                browserName:getBrowserName(),
                deviceOS:getDeviceOS(),
                contactNumber:contact,
                gclid: googleClickId
            }
        }else{
            var journey = {...myOfferJourney}
            if(!AppUtil.checkEmpty(campaignDetails.customerName)){
                journey.customerName=campaignDetails.customerName
            }
            if(AppUtil.checkEmpty(journey.contactNumber)){
                journey.contactNumber=contact
            }
            journey.finalState=stage
        }
        axios({
            method: "POST",
            url: apis.getUrls().customer.saveJourney,
            data: JSON.stringify(journey),
            headers: {'Content-Type': 'application/json'}
        }).then((response)=>{
            if(!appUtil.checkEmpty(response) && !appUtil.checkEmpty(response.data)){
                dispatch(updateJourneyData(response.data));
            }
            if(AppUtil.checkEmpty(callback) && typeof callback === "function"){
                callback();
            }
        }).catch((error)=>{
            console.log("Unable to save jouney " + error);
        })
    }
}
export function availSignUpOffer(customerId, customerName, timeOfDelivery, dateOfDelivery,
                                 completeAddress, deliveryCity, pinCode, product) {
    return dispatch => {
        let requestBody = {};
        if (product === null) {
            dispatch(UtilityActions.showPopup("Please customize your offer product!", "error", 3000));
            return;
        } else if (appUtil.checkEmpty(customerName)) {
            dispatch(UtilityActions.showPopup("Please provide customer name!", "error", 3000));
            return;
        } else if (timeOfDelivery === null || timeOfDelivery.value === null) {
            dispatch(UtilityActions.showPopup("Please select valid delivery time slot!", "error", 3000));
            return;
        } else if (dateOfDelivery === null || dateOfDelivery.format(dateFormat) === null) {
            dispatch(UtilityActions.showPopup("Please select valid delivery date!", "error", 3000));
            return;
        } else if (appUtil.checkEmpty(completeAddress) || completeAddress.length > 1000) {
            dispatch(UtilityActions.showPopup("Please enter valid delivery address!", "error", 3000));
            return;
        } else if (pinCode === null || !appUtil.validPinCode(pinCode)) {
            dispatch(UtilityActions.showPopup("Please select valid pin code!", "error", 3000));
            return;
        } else if (deliveryCity === null || deliveryCity.value === null) {
            dispatch(UtilityActions.showPopup("Please select valid delivery city!", "error", 3000));
            return;
        } else {
            requestBody = {
                customerId: customerId,
                customerName: customerName,
                timeOfDelivery: timeOfDelivery.value,
                dateOfDelivery: dateOfDelivery.format(dateFormat),
                completeAddress: completeAddress,
                city: deliveryCity.value,
                pinCode: pinCode,
                product: product,
                unitId: defaultUnitId,
                brandId: 1
            };
            console.log("request is:: " + JSON.stringify(requestBody));
        }
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().offer.signUp,
            data: JSON.stringify(requestBody),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log("availSignUpOffer:: Response:: " + response);
            if (response != null && response.data === true) {
                console.log("availSignUpOffer:: response.data:: " + response.data);
                // trackUtils.trackAvailSignUpOffer({"success": response.data});
                dispatch(updateSignUpOfferStage(myOfferStage.showCongratulationView))
            } else {
                // trackUtils.trackAvailSignUpOffer({"success": false});
                dispatch(UtilityActions.showPopup("Error availing offer. Try again!", "error"));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            // trackUtils.trackAvailSignUpOffer({"success": false});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error availing offer. Try again!", "info"));
        })
    }
}


export function copyCodeToClipboard(code){
    return dispatch=>{
        var isCopied=false;
        if (navigator.clipboard && window.isSecureContext) {
            isCopied = true;
            navigator.clipboard.writeText(code);
        } else {
            let textArea = document.createElement("textarea");
            textArea.value = code;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            var isCopied = document.execCommand('copy');
            textArea.blur();
        }
        if(isCopied){
            dispatch(UtilityActions.showPopup("Coupon code copied to clipboard.", "info", 2000));
        }else{
            dispatch(UtilityActions.showPopup("Unable to copy code to clipboard.", "error", 2000));
        }
    }
}
export function getCampaignDetail(token,contactFromUrl,deviceType){
    return dispatch=>{
        dispatch(setIsCampaignLoading(true));
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().offer.getCampaignDetails,
            data: JSON.stringify(token),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch(setIsCampaignLoading(false));
            if (response.data != null) {
                var campaignDetailData = response.data;
                campaignDetailData.backgroundImages=[];
                if(response.data.image1 != null) {
                    campaignDetailData.backgroundImages.push(response.data.image1);
                }
                if(response.data.image2 != null){
                    campaignDetailData.backgroundImages.push(response.data.image2);
                }
                if(response.data.image3 != null){
                    campaignDetailData.backgroundImages.push(response.data.image3);
                }
                if(campaignDetailData.backgroundImages.length == 0 ){
                    campaignDetailData.backgroundImages.push("https://cafes.chaayos.com/img/banner/signup_banner_desktop2.png");
                }
                if(campaignDetailData.endDate < new Date() || campaignDetailData.startDate > new Date()){
                    window.location.replace("https://cafes.chaayos.com/notfound");
                }
                dispatch(setCampaignDetail(campaignDetailData));
                dispatch(recordJourney(journeyStage.pageView,deviceType));
                dispatch(updateSignUpOfferStage(myOfferStage.getBasicInfo));
                if(contactFromUrl !== undefined && contactFromUrl.trim().length === 10){
                    document.getElementById("userContact").value=contactFromUrl;
                }
            }else{
                window.location.replace("https://cafes.chaayos.com/notfound");
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            console.log("Error while fetching campaign detail");
            dispatch(setIsCampaignLoading(false));
            dispatch(UtilityActions.hideFullPageLoader());
        });
    }
}
function getBrowserName(){
    let userAgent = navigator.userAgent;
    let browserName;
    if(userAgent.match(/chrome|chromium|crios/i)){
        browserName = "chrome";
    }else if(userAgent.match(/firefox|fxios/i)){
        browserName = "firefox";
    }  else if(userAgent.match(/safari/i)){
        browserName = "safari";
    }else if(userAgent.match(/opr\//i)){
        browserName = "opera";
    } else if(userAgent.match(/edg/i)){
        browserName = "edge";
    }
    return browserName;
}

function getDeviceOS(){
    var name = "Unknown OS";
    if (navigator.userAgent.indexOf("Win") != -1)
        name = "Windows OS";
    if (navigator.userAgent.indexOf("Mac") != -1)
        name = "Macintosh";
    if (navigator.userAgent.indexOf("Linux") != -1)
        name = "Linux OS";
    if (navigator.userAgent.indexOf("Android") != -1)
        name = "Android OS";
    if (navigator.userAgent.indexOf("like Mac") != -1)
        name = "iOS";
    return name
}

export function recordVisitChaayosClicked(deviceType){
    return dispatch =>{
        dispatch(recordJourney(journeyStage.clickedVisitChaayos,deviceType,"",function (){
            window.open('https://cafes.chaayos.com/menu');
        }))
    }
}

export function recordOrderOnZomatoClicked(deviceType){
    return dispatch =>{
        dispatch(recordJourney(journeyStage.clickedOrderOnZomato,deviceType,"",function (){
            window.open('https://zomato.onelink.me/xqzv/5fbcb81a');
        }))
    }
}



