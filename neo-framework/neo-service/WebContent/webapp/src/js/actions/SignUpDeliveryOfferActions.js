import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";

var moment = require('moment');
import * as UtilityActions from "./UtilityActions";
import {closeModal} from "./CustomizationModalActions";
import {getUnitProductsForSignUpOffer} from "./OutletMenuActions";
import trackUtils from "../utils/TrackUtils";

export const defaultSize = '';
export const defaultUnitId = 12018;
export const dateFormat = 'DD - MM - YYYY';
export const signUpOfferStage = {
    getBasicInfo: 'GET_BASIC_INFO',
    getOTP: 'GET_OTP',
    getProductAndDeliveryInfo: 'GET_PRODUCT_AND_DELIVERY_INFO',
    showCongratulationView: 'SHOW_CONGRATULATION_VIEW',
    offerAlreadyAvailedView: 'OFFER_ALREADY_AVAILED_VIEW',
    newCustomerNoOfferView: 'NEW_CUSTOMERS_NO_OFFER_VIEW',
    showCouponView: 'SHOW_COUPON_VIEW',
};

export const timeSlot = {
    slotOne: '8:00 AM - 10:00 AM',
    slotTwo: '12:00 PM - 2:00 PM',
    slotThree: '2:00 PM - 4:00 PM',
    slotFour: '4:00 PM - 6:00 PM',
    slotFive: '6:00 PM - 8:00 PM',
    slotSix: '8:00 PM - 11:00 PM'
};

export function getAvailableTimeSlots(deliveryDate) {
    return dispatch => {
        if (deliveryDate != null) {
            dispatch(UtilityActions.showFullPageLoader(""));

            console.log("Static time slot::: " + deliveryDate);
            let timeSlots = [];
            if (deliveryDate !== null && isToday(deliveryDate)) {
                let currentHour = new Date().getHours();
                console.log("currentHour:: " + currentHour);
                if (currentHour < 8) {
                    timeSlots.push({value: timeSlot.slotOne, label: timeSlot.slotOne});
                }
                if (currentHour < 12) {
                    timeSlots.push({value: timeSlot.slotTwo, label: timeSlot.slotTwo});
                }
                if (currentHour < 14) {
                    timeSlots.push({value: timeSlot.slotThree, label: timeSlot.slotThree});
                }
                if (currentHour < 16) {
                    timeSlots.push({value: timeSlot.slotFour, label: timeSlot.slotFour});
                }
                if (currentHour < 18) {
                    timeSlots.push({value: timeSlot.slotFive, label: timeSlot.slotFive});
                }
                if (currentHour < 20) {
                    timeSlots.push({value: timeSlot.slotSix, label: timeSlot.slotSix});
                }
            } else {
                timeSlots.push({value: timeSlot.slotOne, label: timeSlot.slotOne});
                timeSlots.push({value: timeSlot.slotTwo, label: timeSlot.slotTwo});
                timeSlots.push({value: timeSlot.slotThree, label: timeSlot.slotThree});
                timeSlots.push({value: timeSlot.slotFour, label: timeSlot.slotFour});
                timeSlots.push({value: timeSlot.slotFive, label: timeSlot.slotFive});
                timeSlots.push({value: timeSlot.slotSix, label: timeSlot.slotSix});
            }
            dispatch({type: "SET_AVAILABLE_TIME_SLOTS", payload: timeSlots});
            deliveryDate = deliveryDate.format(dateFormat).toString();
            let data = {"name": deliveryDate};
            axios({
                method: "POST",
                url: apis.getUrls().customer.signUpOfferTimeSlots,
                data: data,
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response != null && !appUtil.checkEmpty(response.data)) {
                    console.log("getAvailableTimeSlots response.data!! " + JSON.stringify(response.data));
                    if (!appUtil.checkEmpty(response.data) && !appUtil.checkEmpty(timeSlots)) {
                        let slotCountData = response.data;
                        let allowedTimeSlots = [];
                        timeSlots.map(slot => {
                            if (slotCountData.slotOneName === slot.value && slotCountData.slotOneCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotOne, label: timeSlot.slotOne})
                            }
                            if (slotCountData.slotTwoName === slot.value && slotCountData.slotTwoCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotTwo, label: timeSlot.slotTwo});
                            }
                            if (slotCountData.slotThreeName === slot.value && slotCountData.slotThreeCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotThree, label: timeSlot.slotThree});
                            }
                            if (slotCountData.slotFourName === slot.value && slotCountData.slotFourCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotFour, label: timeSlot.slotFour});
                            }
                            if (slotCountData.slotFiveName === slot.value && slotCountData.slotFiveCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotFive, label: timeSlot.slotFive});
                            }
                            if (slotCountData.slotSixName === slot.value && slotCountData.slotSixCount <= 30) {
                                allowedTimeSlots.push({value: timeSlot.slotSix, label: timeSlot.slotSix});
                            }
                        });
                        dispatch({type: "SET_AVAILABLE_TIME_SLOTS", payload: allowedTimeSlots});
                    }
                }
                dispatch(UtilityActions.hideFullPageLoader());
            }).catch(function (error) {
                console.log("getAvailableTimeSlots error" + error);
                dispatch(UtilityActions.hideFullPageLoader());
            });
        }
    }
}

export const isToday = (someDate) => {
    if (someDate != null) {
        let date = new Date(someDate);
        const today = new Date();
        console.log('selected' + date);
        console.log('today' + date);
        return date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear()
    }
    return false;
};

export const staticCities = [
    {value: 'Gurgaon', label: 'Gurgaon'},
    {value: 'Delhi', label: 'Delhi'},
    {value: 'Noida', label: 'Noida'},
    {value: 'Chandigarh', label: 'Chandigarh'},
    {value: 'Mumbai', label: 'Mumbai'},
    {value: 'Bangalore', label: 'Bangalore'},
    {value: 'Pune', label: 'Pune'}
];

export const shareData = {
    title: 'Free Desi Chai Ketli',
    text: 'Signup and get a free chai delivered at your door step.',
    url: 'https://onelink.chaayos.com/fcd_share_mobile',
    urlForFB: 'https://onelink.chaayos.com/fcd_share_fb',
    urlForWhatsapp: 'https://onelink.chaayos.com/fcd_share_whatsapp',
    urlForTwitter: 'https://onelink.chaayos.com/fcd_share_twitter',
    urlForEmail: 'https://onelink.chaayos.com/fcd_share_email'
};

export function getAllowedDays(fromDate) {
    let allowedInterval = 8;
    let momentsAllowed = [];
    //momentsAllowed.push(moment(fromDate));
    for (let i = 2; i < allowedInterval; i++) {
        let result = new Date();
        result.setDate(result.getDate() + i);
        momentsAllowed.push(moment(result));
    }
    return momentsAllowed;
}

export function completeCustomization(productObj) {
    return dispatch => {
        dispatch({
            type: "SET_CUSTOMIZED_OFFER_PRODUCT",
            payload: appUtil.updateCartLessProductCustomizations(productObj)
        });
        dispatch(closeModal());
    }
}

export function updateSignUpOfferStage(stage) {
    return dispatch => {
        if (stage !== null && Object.values(signUpOfferStage).includes(stage)) {
            dispatch({type: "SET_SIGN_UP_OFFER_STAGE", payload: stage});
        }
    }
}

export function setSignUpOfferCustomer(customer) {
    return dispatch => {
        if (customer !== null)
            dispatch({type: "SET_SIGN_UP_OFFER_CUSTOMER", payload: customer});
    }
}

export function verifySignUpOfferCustomer(name, contact, email, otp) {
    return dispatch => {
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().customer.signUpOfferSignUp,
            data: JSON.stringify({
                name: name,
                contact: contact,
                otp: otp,
                email: email
            }),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response != null && !appUtil.checkEmpty(response.data)) {
                //Default unit set to Cyber Green
                console.log("verifySignUpOfferCustomer response.data!! " + JSON.stringify(response.data));
                trackUtils.trackVerifySignUpOfferCustomer({"customer": response.data});
                dispatch(setSignUpOfferCustomer(response.data));

                let cafeOrdersExist = response.data.cafeOrdersExist;
                let deliveryOrdersExist = response.data.deliveryOrdersExist;
                console.log("cafeOrdersExist !! " + cafeOrdersExist);
                console.log("deliveryOrdersExist !! " + deliveryOrdersExist);

                if (response.data.signUpOfferAvailed === true) {
                    console.log("response.data.signUpOfferAvailed!! " + response.data.signUpOfferAvailed);
                    dispatch(updateSignUpOfferStage(signUpOfferStage.offerAlreadyAvailedView));
                } else if (response.data.oldCustomer === true) {
                    console.log("response.data.oldCustomer!! "+response.data.oldCustomer);
                    dispatch(updateSignUpOfferStage(signUpOfferStage.newCustomerNoOfferView));
                }else if (cafeOrdersExist && !deliveryOrdersExist) {
                    dispatch(getUnitProductsForSignUpOffer(defaultUnitId));
                    dispatch(updateSignUpOfferStage(signUpOfferStage.newCustomerNoOfferView));
                } else if (cafeOrdersExist && deliveryOrdersExist) {
                    dispatch(getUnitProductsForSignUpOffer(defaultUnitId));
                    dispatch(updateSignUpOfferStage(signUpOfferStage.newCustomerNoOfferView));
                } else if (!cafeOrdersExist && deliveryOrdersExist) {
                    dispatch(updateSignUpOfferStage(signUpOfferStage.showCouponView));
                } else if (!cafeOrdersExist && !deliveryOrdersExist) {
                    dispatch(updateSignUpOfferStage(signUpOfferStage.showCouponView));
                } /*else if(response.data.eligibleForSignupOffer) {
                    dispatch(updateSignUpOfferStage(signUpOfferStage.showCouponView));
                } */else {
                    dispatch(updateSignUpOfferStage(signUpOfferStage.offerAlreadyAvailedView));
                }
            } else {
                trackUtils.trackVerifySignUpOfferCustomer({"customer": null});
                dispatch(UtilityActions.showPopup("Error verifying OTP. Please enter correct OTP", "error", 2000));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            trackUtils.trackVerifySignUpOfferCustomer({"customer": null});
            console.log("error" + error);
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error in OTP verification. Please try again!", "error", 2000));
        });
    }
}

export function availSignUpOffer(customerId, customerName, timeOfDelivery, dateOfDelivery,
                                 completeAddress, deliveryCity, pinCode, product) {
    return dispatch => {
        let requestBody = {};
        if (product === null) {
            dispatch(UtilityActions.showPopup("Please customize your offer product!", "error", 3000));
            return;
        } else if (appUtil.checkEmpty(customerName)) {
            dispatch(UtilityActions.showPopup("Please provide customer name!", "error", 3000));
            return;
        } else if (timeOfDelivery === null || timeOfDelivery.value === null) {
            dispatch(UtilityActions.showPopup("Please select valid delivery time slot!", "error", 3000));
            return;
        } else if (dateOfDelivery === null || dateOfDelivery.format(dateFormat) === null) {
            dispatch(UtilityActions.showPopup("Please select valid delivery date!", "error", 3000));
            return;
        } else if (appUtil.checkEmpty(completeAddress) || completeAddress.length > 1000) {
            dispatch(UtilityActions.showPopup("Please enter valid delivery address!", "error", 3000));
            return;
        } else if (pinCode === null || !appUtil.validPinCode(pinCode)) {
            dispatch(UtilityActions.showPopup("Please select valid pin code!", "error", 3000));
            return;
        } else if (deliveryCity === null || deliveryCity.value === null) {
            dispatch(UtilityActions.showPopup("Please select valid delivery city!", "error", 3000));
            return;
        } else {
            requestBody = {
                customerId: customerId,
                customerName: customerName,
                timeOfDelivery: timeOfDelivery.value,
                dateOfDelivery: dateOfDelivery.format(dateFormat),
                completeAddress: completeAddress,
                city: deliveryCity.value,
                pinCode: pinCode,
                product: product,
                unitId: defaultUnitId,
                brandId: 1
            };
            console.log("request is:: " + JSON.stringify(requestBody));
        }
        dispatch(UtilityActions.showFullPageLoader(""));
        axios({
            method: "POST",
            url: apis.getUrls().offer.signUp,
            data: JSON.stringify(requestBody),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            console.log("availSignUpOffer:: Response:: " + response);
            if (response != null && response.data === true) {
                console.log("availSignUpOffer:: response.data:: " + response.data);
                trackUtils.trackAvailSignUpOffer({"success": response.data});
                dispatch(updateSignUpOfferStage(signUpOfferStage.showCongratulationView))
            } else {
                trackUtils.trackAvailSignUpOffer({"success": false});
                dispatch(UtilityActions.showPopup("Error availing offer. Try again!", "error"));
            }
            dispatch(UtilityActions.hideFullPageLoader());
        }).catch(function (error) {
            trackUtils.trackAvailSignUpOffer({"success": false});
            dispatch(UtilityActions.hideFullPageLoader());
            dispatch(UtilityActions.showPopup("Error availing offer. Try again!", "info"));
        })
    }
}


