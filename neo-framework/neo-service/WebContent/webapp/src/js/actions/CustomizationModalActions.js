import axios from "axios";
import apis from "../APIs";
import StorageUtils from "../utils/StorageUtils";
import appUtil from "../AppUtil";
import {updateCart} from "./CartManagementActions";

export function showCustomizationModal(product, type, cartItem) {
    return dispatch => {
        document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
        dispatch({type: "SHOW_CUSTOMIZATION_MODAL", payload: {product: product, type: type, cartItem: cartItem}});
    }
}

export function closeModal() {
    return dispatch => {
        document.getElementsByTagName("BODY")[0].style.overflow = "auto";
        dispatch({type: "HIDE_CUSTOMIZATION_MODAL"});
    }
}

export function setDimension(dimension) {
    return dispatch => {
        dispatch({type: "SET_SELECTED_DIMENSION", payload: dimension});
    }
}

export function addAddon(addon) {
    return dispatch => {
        dispatch({type: "ADD_ADDON", payload: addon});
    }
}

export function removeAddon(addon) {
    return dispatch => {
        dispatch({type: "REMOVE_ADDON", payload: addon});
    }
}

export function addAllAddons(addons) {
    return dispatch => {
        dispatch({type: "ADD_ALL_ADDONS", payload: addons});
    }
}

export function setVariant(productId, alias) {
    return dispatch => {
        dispatch({type: "SET_SELECTED_VARIANT", payload: {productId: productId, alias: alias}});
    }
}

export function setIngredientProduct(productId, displayName) {
    return dispatch => {
        dispatch({type: "SET_INGREDIENT_PRODUCT", payload: {productId: productId, display: displayName}});
    }
}

export function updateQty(qty) {
    return dispatch => {
        dispatch({type: "UPDATE_PRODUCT_QTY", payload: qty});
    }
}

export function selectMenuProduct(itemName, productId, constituentProduct, addons) {
    return dispatch => {
        dispatch({
            type: "SELECT_MENU_PRODUCT",
            payload: {
                itemName: itemName,
                productId: productId,
                constituentProduct: constituentProduct,
                selectedAddons: addons
            }
        });
    }
}

export function backToCompositeProduct() {
    return dispatch => {
        dispatch({type: "BACK_TO_COMPOSITE_PRODUCT"});
    }
}

export function customizationSubmit(cartObj) {
    return dispatch => {
        var variants = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.variants.map((variant) => {
            variant.details.map((detail) => {
                if (detail.active) {
                    variants.push(detail);
                }
            })
        });
        var ingredientProducts = [];
        cartObj.cartItem.selectedDimension.recipe.ingredient.products.map((product) => {
            product.details.map((detail) => {
                if (detail.active) {
                    ingredientProducts.push(detail);
                }
            })
        });
        var addons = [];
        var containsSabKuch = false;
        cartObj.addons.map((cartAddon) => {
            if(cartAddon.product.productId==988) {
                containsSabKuch = true;
                addons.push(cartAddon);
            }
        });
        var item = {
            itemId: 1,
            productId: cartObj.cartItem.id,
            productName: cartObj.cartItem.name,
            quantity: cartObj.quantity,
            price: cartObj.cartItem.selectedDimension.price,
            totalAmount: null,
            amount: cartObj.quantity * cartObj.cartItem.selectedDimension.price,
            discountDetail: {},
            addons: [],
            dimension: cartObj.cartItem.selectedDimension.dimension,
            billType: cartObj.cartItem.billType,
            isCombo: false,
            composition: {
                variants: variants,
                products: ingredientProducts,
                menuProducts: [],
                addons: containsSabKuch?addons:cartObj.addons
            },
            recipeId: cartObj.cartItem.selectedDimension.recipe.recipeId
        };
        dispatch({type: "SUBMIT_MENU_PRODUCT_CUSTOMIZATION", payload: item});
    }
}

export function updateCustomizationProduct(product) {
    return dispatch => {
        dispatch({type: "UPDATE_CUSTOMIZATION_PRODUCT", payload: product});
    }
}
