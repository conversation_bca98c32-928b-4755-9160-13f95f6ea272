// class MembershipTrackUtils {
//
//     constructor() {
//
//         this.trackMembershipPage = this.trackMembershipPage.bind(this);
//         this.trackPaymentPage = this.trackPaymentPage.bind(this);
//         this.trackOrderDetailPage = this.trackOrderDetailPage.bind(this);
//         this.trackOrdersPage = this.trackOrdersPage.bind(this);
//         this.trackEvent = this.trackEvent.bind(this);
//         this.sendTrackingDetails =this.sendTrackingDetails(this);
//     };
//
//     trackMembershipPage() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     trackPaymentPage() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     trackOrderDetailPage() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     trackOrdersPage() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     trackEvent() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     sendTrackingDetails(event,data){
//
//     }
// }
//
// const membershipTrackUtils = new MembershipTrackUtils();
// export default membershipTrackUtils;
