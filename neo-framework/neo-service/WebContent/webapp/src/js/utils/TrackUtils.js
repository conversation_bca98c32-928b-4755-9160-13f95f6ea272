/**
 * Created by Chaayos on 23-02-2017.
 */
import StorageUtils from "./StorageUtils";
import analyticsUtils from "./AnalyticsUtils";
// import webEngageUtils from "./WebEngageUtils";

class TrackUtils {

    constructor() {
        this.checkEmpty = this.checkEmpty.bind(this);
        this.initWebEngageParams = this.initWebEngageParams.bind(this);
        this.trackPageView = this.trackPageView.bind(this);
        this.trackInitiateCheckout = this.trackInitiateCheckout.bind(this);
        this.trackSuccessOrder = this.trackSuccessOrder.bind(this);
        this.trackUser = this.trackUser.bind(this);
        this.trackUserData = this.trackUserData.bind(this);
        this.logout = this.logout.bind(this);
        this.trackOrderModeChange = this.trackOrderModeChange.bind(this);
        this.trackLocalityMetadata = this.trackLocalityMetadata.bind(this);
        this.trackUnitClosed = this.trackUnitClosed.bind(this);
        this.trackCategoryClicked = this.trackCategoryClicked.bind(this);
        this.trackCategoryChanged = this.trackCategoryChanged.bind(this);
        this.trackLocalityChangeClicked = this.trackLocalityChangeClicked.bind(this);
        this.trackFullMenuExplored = this.trackFullMenuExplored.bind(this);
        this.trackCouponTried = this.trackCouponTried.bind(this);
        this.trackCouponRemoved = this.trackCouponRemoved.bind(this);
        this.trackOfferSuccess = this.trackOfferSuccess.bind(this);
        this.trackOfferFailed = this.trackOfferFailed.bind(this);
        this.trackOrderRemark = this.trackOrderRemark.bind(this);
        this.trackTaxDetailViewed = this.trackTaxDetailViewed.bind(this);
        this.trackGoToMenuClicked = this.trackGoToMenuClicked.bind(this);
        this.trackCustomerLookup = this.trackCustomerLookup.bind(this);
        this.trackOTPResent = this.trackOTPResent.bind(this);
        this.trackLogin = this.trackLogin.bind(this);
        this.trackSignup = this.trackSignup.bind(this);
        this.trackAddressCount = this.trackAddressCount.bind(this);
        this.trackAddressSelected = this.trackAddressSelected.bind(this);
        this.trackAddAddressClicked = this.trackAddAddressClicked.bind(this);
        this.trackAddressAdd = this.trackAddressAdd.bind(this);
        this.trackPaymentModeSelected = this.trackPaymentModeSelected.bind(this);
        this.trackPaymentSuccess = this.trackPaymentSuccess.bind(this);
        this.trackPaymentFailed = this.trackPaymentFailed.bind(this);
        this.trackReturnedFromPayment = this.trackReturnedFromPayment.bind(this);
        this.trackCashDenied = this.trackCashDenied.bind(this);
        this.trackAddCartItem = this.trackAddCartItem.bind(this);
        this.trackRemoveCartItem = this.trackRemoveCartItem.bind(this);
        this.trackReorderClicked = this.trackReorderClicked.bind(this);
        this.trackInitialStockout = this.trackInitialStockout.bind(this);
        this.trackSelectionStockout = this.trackSelectionStockout.bind(this);
        this.trackRejectedStock = this.trackRejectedStock.bind(this);
       // this.trackMembershipPageOpened= this.trackMembershipPageOpened(this);
    }

    checkEmpty(obj) {
        if (obj === undefined || obj === null || obj === {}) {
            return true;
        }
        if (typeof obj === "string" || typeof obj === "number") {
            return obj.toString().trim().length === 0;
        }
        for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
        }
        return true;
    }

    initWebEngageParams(orderMode, city, locality, outlet) {
        // webEngageUtils.setDefaultAttributes(orderMode, city, locality, outlet);
    }

    setCampaignInData(data){
        data.campaign = StorageUtils.getCampaignDetails();
        return data;
    }

    trackPageView(data) {
        try {
            this.setCampaignInData(data);
            // webEngageUtils.trackEvent("PAGE_VISIT", data);
            //facebookPixelUtils.handlePixelTrack("PAGE_VISIT", facebookPixelUtils.getPageViewObject(data.page, data.device), data.custom);
        } catch (e) {
        }
    }

    trackInitiateCheckout(data) {
        try {
            this.setCampaignInData(data);
            // webEngageUtils.trackEvent("ORDER_INITIATED", data);
            analyticsUtils.trackInitiateCheckout(data.cart);
            /*facebookPixelUtils.handlePixelTrack("ORDER_INITIATED", facebookPixelUtils.getCheckoutObject(data.cart.orderDetail.transactionDetail.paidAmount,
                "INR", data.cart.orderDetail.orders.length), true);*/
        } catch (e) {
        }
    }

    trackSuccessOrder(data) {
        try {
            this.setCampaignInData(data);
            // webEngageUtils.trackEvent("ORDER_SUCCESS_NEW", data);
            var obj = analyticsUtils.sendOrderForTracking(data.cart);
            /*if (obj != null) {
                facebookPixelUtils.handlePixelTrack("ORDER_SUCCESS", facebookPixelUtils.getOrderSuccessObject(obj.purchase, obj.orderDetail), true);
            }*/
        } catch (e) {
        }
    }

    trackUser(data) {
        // try {
        //     !this.checkEmpty(data) ? webEngageUtils.trackUser(data) : null;
        // } catch (e) {
        // }
    }

    trackUserData(data) {
        // try {
        //     !this.checkEmpty(data) ? webEngageUtils.setUserAttributes(data) : null;
        // } catch (e) {
        // }
    }

    logout() {
        // try {
        //     webEngageUtils.logoutUser();
        // } catch (e) {
        // }
    }

    trackOrderModeChange(mode) {
        // try {
        //     if (!this.checkEmpty(mode)) {
        //         var data = {mode:mode};
        //         this.setCampaignInData(data);
        //         this.initWebEngageParams(data.mode, null,null,null);
        //         webEngageUtils.trackEvent("ORDER_MODE_SELECTED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackLocalityMetadata(data) {
        // try {
        //     if (!this.checkEmpty(data)) {
        //         this.setCampaignInData(data);
        //         this.initWebEngageParams(null, data.city, data.locality, data.outlet);
        //         webEngageUtils.trackEvent("LOCATION_OUTLET_SELECTED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackUnitClosed(data){
        // try {
        //     if (!this.checkEmpty(data)) {
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("OUTLET_CLOSED", data);
        //     }
        // } catch (e) {
        // }
    }


    trackCategoryClicked(cat) {
        // try {
        //     if(!this.checkEmpty(cat)){
        //         var data = {cat:cat};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("CATEGORY_CLICKED", data) ;
        //     }
        // } catch (e) {
        // }
    }

    trackCategoryChanged(cat) {
        // try {
        //     if(!this.checkEmpty(cat)){
        //         var data = {cat:cat};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("CATEGORY_CHANGED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackLocalityChangeClicked(page) {
        // try {
        //     if(!this.checkEmpty(page)){
        //         var data = {page:page};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("LOCALITY_CHANGE_CLICKED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackFullMenuExplored() {
        // try {
        //     webEngageUtils.trackEvent("FULL_MENU_EXPLORED", null)
        // } catch (e) {
        // }
    }

    trackCouponTried(data){
        // try {
        //     if(data!=null){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("COUPON_TRIED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackCouponRemoved(data){
        // try {
        //     if(data!=null){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("COUPON_REMOVED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackOfferSuccess(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("OFFER_SUCCESS", data);
        //     }
        // }catch (e){}
    }

    trackOfferFailed(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("OFFER_FAILED", data);
        //     }
        // }catch (e){}
    }

    trackTaxDetailViewed(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("TAX_DETAIL_VIEWED", data, true);
        //     }
        // }catch (e){}
    }

    trackOrderRemark(remark){
        // try{
        //     if(!this.checkEmpty(remark)){
        //         var data = {remark:remark};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("ORDER_INSTRUCTION_ADDED", data);
        //     }
        // }catch (e){}
    }

    trackGoToMenuClicked(source){
        // try{
        //     if(!this.checkEmpty(source)){
        //         var data = {source:source};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("GO_TO_MENU_CLICKED", data);
        //     }
        // }catch (e){}
    }

    trackCustomerLookup(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("CUSTOMER_LOOKUP", data);
        //     }
        // }catch (e){}
    }

    trackOTPResent(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("OTP_RESENT", data);
        //     }
        // }catch (e){}
    }

    trackLogin(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("USER_LOGIN", data);
        //     }
        // }catch (e){}
    }

    trackSignup(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("USER_SIGNUP", data);
        //     }
        // }catch (e){}
    }

    trackAddressCount(count){
        // try{
        //     if(!this.checkEmpty(count)){
        //         var data = {count:count};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("EXISTING_ADDRESS_COUNT", data);
        //     }
        // }catch (e){}
    }

    trackAddressSelected(type){
        // try{
        //     if(!this.checkEmpty(type)){
        //         var data = {type:type};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("ADDRESS_SELECTED", data);
        //     }
        // }catch (e){}
    }

    trackAddAddressClicked(pos){
        // try{
        //     if(!this.checkEmpty(pos)){
        //         var data = {pos:pos};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("ADD_ADDRESS_CLICKED", data);
        //     }
        // }catch (e){}
    }

    trackAddressAdd(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("ADDRESS_ADD", data);
        //     }
        // }catch (e){}
    }

    trackPaymentModeSelected(data){
        try{
            /*if(!this.checkEmpty(data)){
                this.setCampaignInData(data);
                webEngageUtils.trackEvent("PAYMENT_MODE_SELECTED", data);
            }*/
            analyticsUtils.trackPaymentModeSelected(data);
        }catch (e){}
    }

    trackCashDenied(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("CASH_ON_DELIVERY_DENIED", data);
        //     }
        // }catch (e){}
    }

    trackPaymentSuccess(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("PAYMENT_SUCCESS", data);
        //     }
        // }catch (e){}
    }

    trackPaymentFailed(data){
        // try{
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("PAYMENT_FAILED", data);
        //     }
        // }catch (e){}
    }

    trackReturnedFromPayment(){
        // try{
        //     webEngageUtils.trackEvent("RETURNED_FROM_PAYMENT");
        // }catch (e){}
    }

    trackAddCartItem(data) {
        try {
            // if(!this.checkEmpty(data)){
            //     this.setCampaignInData(data);
            //     webEngageUtils.trackEvent("PRODUCT_ADDED", data);
            // }
            analyticsUtils.trackAddCartItem(data);
        } catch (e) {
        }
    }

    trackRemoveCartItem(data) {
        // try {
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("PRODUCT_REMOVED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackReorderClicked(data) {
        // try {
        //     if(!this.checkEmpty(data)){
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("REORDER_CLICKED", data);
        //     }
        // } catch (e) {
        // }
    }

    trackInitialStockout(products){
        // try{
        //     if(!this.checkEmpty(products)){
        //         var data = {products:products};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("INITIAL_STOCKOUT", data);
        //     }
        // }catch(e){}
    }

    trackSelectionStockout(products){
        // try{
        //     if(!this.checkEmpty(products)){
        //         var data = {products:products};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("SELECTION_STOCKOUT", data);
        //     }
        // }catch(e){}
    }

    trackRejectedStock(products){
        // try{
        //     if(!this.checkEmpty(products)){
        //         var data = {products:products};
        //         this.setCampaignInData(data);
        //         webEngageUtils.trackEvent("REJECTED_STOCK", data);
        //     }
        // }catch(e){}
    }

    trackShowPromoModal(){
        // try{
        //     var data = {};
        //     this.setCampaignInData(data);
        //     webEngageUtils.trackEvent("FIRST_TIME_OFFER_MODAL_SHOWN", data);
        // }catch(e){}
    }

    trackAcceptedPromoOffer(){
        // try{
        //     var data = {};
        //     this.setCampaignInData(data);
        //     webEngageUtils.trackEvent("FIRST_TIME_OFFER_ACCEPTED", data);
        // }catch(e){}
    }

    trackRejectedPromoOffer(){
        // try{
        //     var data = {};
        //     this.setCampaignInData(data);
        //     webEngageUtils.trackEvent("FIRST_TIME_OFFER_REJECTED", data);
        // }catch(e){}
    }

    trackStoreLocatorDineInClicked(data) {
        try {
            this.setCampaignInData(data);
            analyticsUtils.trackStoreLocatorDineInClicked(data);
        } catch (e) {
        }
    }

    trackStoreLocatorDeliveryClicked(data) {
        try {
            this.setCampaignInData(data);
            analyticsUtils.trackStoreLocatorDeliveryClicked(data);
        } catch (e) {
        }
    }

    trackDeliveryRedirected(data) {
        try {
            this.setCampaignInData(data);
            analyticsUtils.trackDeliveryRedirected(data);
        } catch (e) {
        }
    }

    trackAvailSignUpOffer(data) {
        try {
            this.setCampaignInData(data);
            analyticsUtils.trackAvailSignUpOffer(data);
        } catch (e) {
        }
    }

    trackMyOfferPageView(){
        try {
            analyticsUtils.trackMyOfferPageView()
        }catch (e){

        }
    }

    trackMyOfferOfferFound(data){
        try {
            analyticsUtils.trackMyOfferOfferFound(data);
        }catch (e){

        }
    }

    trackMyOfferNoOfferFound(data){
        try {
            analyticsUtils.trackMyOfferNoOfferFound(data);
        }catch (e){

        }
    }

    trackMyOfferUserSignup(data){
        try {
            analyticsUtils.trackMyOfferUserSignup(data);
        }catch (e){

        }
    }

    trackVerifySignUpOfferCustomer(data){
        try{
            this.setCampaignInData(data);
            analyticsUtils.trackVerifySignUpOfferCustomer(data);
        }catch (e) {

        }
    }

    trackMembershipClaimed(data){
        try{
            this.setCampaignInData(data);
            analyticsUtils.trackMembershipClaimed(data);
        }catch (e) {

        }
    }

    trackSlotMachineGamePlayed(data) {
        try{
            analyticsUtils.trackSlotMachineGamePlayed(data);
        }catch (e) {
            console.log("Error while sending data to GTM");
        }
    }

    trackSlotMachineGamePlayedFlow2(data) {
        try{
            analyticsUtils.trackSlotMachineGamePlayedFlow2(data);
        }catch (e) {
            console.log("Error while sending data to GTM");
        }
    }

    trackSlotMachineGameOfferGenerated(data) {
        try{
            console.log("Machine Game Offer Generated data sent to GTM");
            analyticsUtils.trackSlotMachineGameOfferGenerated(data);
        }catch (e) {

        }
    }

    trackSlotMachineGamePageView() {
        try{
            analyticsUtils.trackSlotMachineGamePageView();
        }catch (e) {

        }
    }

    trackNinjaGameOfferGenerated(data) {
        try{
            console.log("Ninja Game Offer Generated data sent to GTM");
            analyticsUtils.trackCustomEvent("NinjaGameOfferGenerated", data)
        }catch (e) {
        }
    }

    trackNinjaGamePlayed(data) {
            try{
                console.log("Ninja Game Offer Generated data sent to GTM");
                analyticsUtils.trackCustomEvent("NinjaGamePlayed", data)
            }catch (e) {
            }
    }

    trackNinjaGamePageView() {
                try{
                    console.log("Ninja Game Offer Generated data sent to GTM");
                    analyticsUtils.trackCustomEvent("NinjaGamePageView", null);
                }catch (e) {
                }
            }
}

const trackUtils = new TrackUtils();
export default trackUtils;
