// class MembershipEngageUtils {
//     constructor() {
//         this.getEventData = this.getEventData.bind(this);
//         this.getMembershipData = this.getMembershipData.bind(this);
//         this.getPaymentData = this.getPaymentData.bind(this);
//         this.getOrderDetailData = this.getOrderDetailData.bind(this);
//         this.getOrdersData = this.getOrdersData.bind(this);
//     }
//
//     getEventData(event, data) {
//         switch (event) {
//             case "MEMBERSHIP_PAGE_VISIT": {
//                 return this.getMembershipData(data);
//             }
//             case "PAYMENT_PAGE_VISIT": {
//                 return this.getPaymentData(data);
//             }
//             case "ORDER_DETAIL_PAGE_VISIT": {
//                 return this.getOrderDetailData(data);
//             }
//             case "ORDERS_PAGE_VISIT": {
//                 return this.getOrdersData(data);
//             }
//         }
//     }
//
//     getMembershipData() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     getPaymentData() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     getOrderDetailData() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
//     getOrdersData() {
//         try {
//
//
//         } catch (e) {
//         }
//     }
//
// }
//
// const membershipEngageUtils = new MembershipEngageUtils();
// export default membershipEngageUtils;
