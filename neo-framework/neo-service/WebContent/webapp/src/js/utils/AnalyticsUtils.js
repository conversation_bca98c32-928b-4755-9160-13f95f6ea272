/**
 * Created by Chaayos on 04-03-2017.
 */
import appUtil from "../AppUtil";

class AnalyticsUtils {

    constructor() {
        this.category = {
            3623: "IndianChai",
            3624: "ChaiUnChai",
            3625: "Cold",
            3626: "Breakfast",
            3627: "Feasts",
            3628: "Nash<PERSON>",
            3629: "Bakery",
            3631: "Merchandise"
        };
        this.getCategory = this.getCategory.bind(this);
        this.sendOrderForTracking = this.sendOrderForTracking.bind(this);
        this.trackInitiateCheckout = this.trackInitiateCheckout.bind(this);
        this.trackPaymentModeSelected = this.trackPaymentModeSelected.bind(this);
        this.trackAddCartItem = this.trackAddCartItem.bind(this);
        this.trackMembershipClaimed = this.trackMembershipClaimed.bind(this);
        this.trackMyOfferPageView = this.trackMyOfferPageView.bind(this);
    }

    getCategory(id) {
        return this.category[id];
    }

    sendOrderForTracking(order) {
        try {
            //var cart = order;
            var products = [];
            var packaging = 0;

            var deliveryCharge = 0;
            order.orders.map(orderItem => {
                if (orderItem.productId != 1044 && orderItem.productId != 1043) {
                    products.push({
                        name: orderItem.productName,
                        id: orderItem.productId,
                        brand: "Chaayos",
                        category: this.getCategory(orderItem.productCategory.id),
                        price: orderItem.price,
                        quantity: orderItem.quantity,
                        variant: orderItem.dimension
                    });
                } else {
                    if (orderItem.productId == 1044) {
                        deliveryCharge = orderItem.price;
                    }
                    if (orderItem.productId == 1043) {
                        packaging = orderItem.price;
                    }
                }
            });
            var tax = order.transactionDetail.tax;
            window.dataLayer = window.dataLayer || [];
            var ecommerce = {
                purchase: {
                    actionField: {
                        id: order.generateOrderId,
                        revenue: order.transactionDetail.paidAmount,
                        tax: tax.toFixed(2),
                        shipping: deliveryCharge,
                        coupon: order.offerCode
                    },
                    products: products
                }
            };
            dataLayer.push({
                ecommerce: ecommerce,
                event: "ORDER_SUCCESS"
            });
            var criteria = order.source;
            var source = "";
            (criteria == "COD") ? source = "Delivery" : null;
            (criteria == "TAKE_AWAY") ? source = "Takeaway" : null;
            (criteria == "CAFE") ? source = "DineIn" : null;
            dataLayer.push({
                transactionAmount: order.transactionDetail.paidAmount,
                transactionId: order.generateOrderId,
                transactionSource: source,
                transactionUnit: order.unitName,
                event: "FetchOrderDetail"
            });
            return {purchase: ecommerce.purchase}
        } catch (e) {
            return null;
        }
    }


    trackInitiateCheckout(cart) {
        dataLayer.push({
            transactionAmount: cart.orderDetail.transactionDetail.paidAmount,
            event: "InitiateCheckout"
        });
    }

    trackPaymentModeSelected(data) {
        dataLayer.push({
            transactionAmount: data.amount,
            event: "AddPaymentInfo"
        });
    }

    trackAddCartItem(data) {
        console.log("data" + dataLayer);
        dataLayer.push({
            itemPrice: data.cartItem.price,
            cartId: data.cartId,
            productId: data.cartItem.productId,
            event: "AddToCart"
        });

    }

    trackStoreLocatorDineInClicked(data) {
        dataLayer.push({
            event: "StoreLocatorDineInClicked"
        });
    }

    trackStoreLocatorDeliveryClicked(data) {
        dataLayer.push({
            event: "StoreLocatorDeliveryClicked"
        });
    }

    trackDeliveryRedirected(data) {
        dataLayer.push({
            source: data.source,
            type: data.type,
            event: "StoreLocatorDeliveryRedirected"
        })
    }

    trackAvailSignUpOffer(data) {
        dataLayer.push({
            event: "AvailSignUpOffer",
            isSuccess: !appUtil.checkEmpty(data) ? data.success : false
        });
    }

    trackVerifySignUpOfferCustomer(data) {
        dataLayer.push({
            event: "VerifySignUpOfferCustomer",
            isSuccess: !appUtil.checkEmpty(data) ? data.customer : null
        });
    }

    trackMembershipClaimed(data) {
        dataLayer.push({
            event: "MembershipClaimed",
        })
    }
    trackMyOfferPageView(){
        dataLayer.push({
            event:"PageView"
        })
    }
    trackMyOfferOfferFound(data){
        dataLayer.push({
            event:"MyOfferFoundOffer",
            data:data
        })
    }
    trackMyOfferNoOfferFound(data){
        dataLayer.push({
            event:"MyOfferNoOfferFound",
            data:data
        })
    }

    trackMyOfferUserSignup(data) {
        dataLayer.push({
            event:"MyOfferUserSignup",
            data:data
        })
    }

    trackSlotMachineGamePlayed(data) {
        dataLayer.push({
            event: "MachineGamePlayed",
        })
    }

    trackSlotMachineGamePlayedFlow2(data) {
        dataLayer.push({
            event: "MachineGamePlayedFlow2",
        })
    }

    trackSlotMachineGameOfferGenerated(data) {
        dataLayer.push({
            event: "MachineGameOfferGenerated"
        })
    }

    trackSlotMachineGamePageView(){
        dataLayer.push({
            event:"MachineGamePageView"
        })
    }

    trackCustomEvent(eventName, data) {
        let obj = {};
        obj.event = eventName;
        if(data != null) {
            obj.data = data;
        }
        dataLayer.push(obj);
    }
}

const analyticsUtils = new AnalyticsUtils();
export default analyticsUtils;
