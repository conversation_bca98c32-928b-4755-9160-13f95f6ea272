/**
 * Created by Chaayos on 04-03-2017.
 */
import webEngageDataConverter from "./WebEngageDataConverter";
import appUtil from "../AppUtil";

class WebEngageUtils {

    constructor(){
        this.defaultAttributes = {
            orderMode:"",
            city:"",
            locality:"",
            outlet:"",
            orderSrc:(appUtil.getParameterByName("start_url")!=null?"WEBAPP_HOMESCREEN":appUtil.isMobile()?"WEBAPP_MOBILE":"WEBAPP_DESKTOP"),
            host:window.location.origin
            /*customerId:"",
            acquisitionSrc:"",
            empId:"",
            deviceId:"",*/
        };
        this.getDefaultAttributes = this.getDefaultAttributes.bind(this);
        this.setDefaultAttributes = this.setDefaultAttributes.bind(this);
        this.singletonEvents = [];
        this.getSingletonEvents = this.getSingletonEvents.bind(this);
        this.addSingletonEvents = this.addSingletonEvents.bind(this);
        this.trackPageView = this.trackPageView.bind(this);
        this.trackUser = this.trackUser.bind(this);
        this.logoutUser = this.logoutUser.bind(this);
        this.setUserAttributes = this.setUserAttributes.bind(this);
        this.trackEvent = this.trackEvent.bind(this);
        this.category = {
            3623:"IndianChai",
            3624:"ChaiUnChai",
            3625:"Cold",
            3626:"Breakfast",
            3627:"Feasts",
            3628:"Nashta",
            3629:"Bakery",
            3631:"Merchandise"
        };
        this.getCategory = this.getCategory.bind(this);
        this.milkMap = {
            10:"Regular",
            11:"Full Doodh",
            12:"Doodh Kum",
            50:"Pani Kum"
        };
        this.getMilk();
    }

    setDefaultAttributes(orderMode, city, locality, outlet){
        orderMode!=null?this.defaultAttributes.orderMode=orderMode:null;
        city!=null?this.defaultAttributes.city=city:null;
        locality!=null?this.defaultAttributes.locality=locality:null;
        outlet!=null?this.defaultAttributes.outlet=outlet:null;
        //orderSrc!=null?this.defaultAttributes.orderSrc=orderSrc:null;
        //empId!=null?this.defaultAttributes.empId=empId:null;
        //deviceId!=null?this.defaultAttributes.deviceId=deviceId:null;
        //customerId!=null?this.defaultAttributes.customerId=customerId:null;
        //acquisitionSrc!=null?this.defaultAttributes.acquisitionSrc=acquisitionSrc:null;
    }

    getDefaultAttributes(){
        return this.defaultAttributes;
    }

    getSingletonEvents(){
        return this.singletonEvents;
    }

    addSingletonEvents(event){
        if(this.singletonEvents.indexOf(event)<0){
            this.singletonEvents.push(event);
        }
    }

    trackPageView(){
        try{
            webengage.reload();
        }catch(e){}
    }

    trackUser(userId){
        try{
            //console.log("user login::::::::::::::"+userId);
            webengage.user.login(userId);
        }catch(e){}
    }

    logoutUser(){
        try{
            //console.log("user logout::::::::::::::::::::::::");
            webengage.user.logout();
        }catch(e){}
    }

    setUserAttributes(data){
        try{
            //console.log("user attributes::::::::::::::"+JSON.stringify(webEngageDataConverter.convertUserData(data)));
            webengage.user.setAttribute(webEngageDataConverter.convertUserData(data));
        }catch(e){}
    }

    trackEvent(event, data, isSingleton){
        try{
            var call = true;
            if(isSingleton && this.singletonEvents.indexOf(event)>-1){
                call = false;
            }
            if(call){
                //console.log(event+":::::::::::"+JSON.stringify(webEngageDataConverter.getConvertedObject(event, data)));
                webengage.track(event, webEngageDataConverter.getConvertedObject(event,data));
                isSingleton==true?this.addSingletonEvents(event):null;
            }
        }catch(e){}
    }

    getCategory(id){
        return this.category[id];
    }

    getMilk(id){
        return this.milkMap[id];
    }
}

const webEngageUtils = new WebEngageUtils();
export default webEngageUtils;