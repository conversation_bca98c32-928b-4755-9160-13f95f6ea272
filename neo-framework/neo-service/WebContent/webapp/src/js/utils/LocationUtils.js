/**
 * Created by Chaayos on 27-03-2017.
 */

class LocationUtils {
    constructor(){
        this.createMenuUrl = this.createMenuUrl.bind(this);
    }

    createMenuUrl(criteria,city,locality,outlet){
        var url = "/";
        if(criteria=="DELIVERY"){
            url += criteria.toLowerCase()+"/"+city+"/"+locality.label;
        }else if(criteria=="TAKE_AWAY"){
            url += "pickup/"+city+"/"+outlet.label;
        }else{
            //TODO handle this
        }
        return url;
    }
}

const locationUtils = new LocationUtils();
export default locationUtils;