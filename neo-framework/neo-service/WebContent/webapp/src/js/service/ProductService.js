/**
 * Created by Chaayos on 23-03-2017.
 */
import React from "react";
import appUtil from "../AppUtil";
import _ from "lodash";

class ProductService {

    constructor(){
        this.stockAvailable = this.stockAvailable.bind(this);
        this.setCartQuantityToProduct = this.setCartQuantityToProduct.bind(this);
        this.setRecipeCustomizationCount = this.setRecipeCustomizationCount.bind(this);
        this.createProductTagsLayout = this.createProductTagsLayout.bind(this);
        /*this.setTopSellers = this.setTopSellers.bind(this);*/
        this.setBoughtByYou = this.setBoughtByYou.bind(this);
        this.sortProductsByPrice = this.sortProductsByPrice.bind(this);
        this.getDummyProductLayout = this.getDummyProductLayout.bind(this);
        this.getDesiChaiArr = this.getDesiChaiArr.bind(this);
        this.getBaarishWaliChaiArr = this.getBaarishWaliChaiArr.bind(this);
        this.getSizeDescriptions = this.getSizeDescriptions.bind(this);
        this.sortSpecialProductsInMap = this.sortSpecialProductsInMap.bind(this);
    }

    stockAvailable(product){
        var stockLeft = true;
        if(product.inventoryLoaded && product.cartQuantity>=product.stock){
            stockLeft = false;
        }
        return stockLeft;
    }

    setCartQuantityToProduct(cart, product){
        if (cart != null && cart.orderDetail.orders.length > 0) {
            cart.orderDetail.orders.map((cartItem) => {
                if (cartItem.productId == product.id) {
                    product.cartItems.push(cartItem);
                    product.cartQuantity += cartItem.quantity;
                }
            });
        }
    }

    setRecipeCustomizationCount(product){
        if (product.recipesLoaded) {
            product.prices.map((price) => {
                product.recipeCustomizationCount += (price.recipe!=null && price.recipe.customizationCount!=null)?price.recipe.customizationCount:0;
            });
        }
    }

    createProductTagsLayout(productTags){
        var tags = [];
        if(!appUtil.checkEmpty(productTags)){
            productTags.map((tag,index)=>{
                tags.push(
                    <span key={tag+":"+index} class="tagName">{appUtil.formatTag(tag)}</span>
                );
            });
        }
        return tags;
    }

    /*setTopSellers(topSellersLoaded, topSellers, productsMap, prodId, productObj){
        if (topSellersLoaded) {
            topSellers.map((productId) => {
                if (productId == prodId) {
                    var topSellers = productsMap[1];
                    if (topSellers == null) {
                        topSellers = [];
                    }
                    topSellers.push(productObj);
                    productsMap[1] = topSellers;
                }
            })
        }
    }*/

    setSpecialProductsMap(specialProductsMap, specialMenu, prodId, productObj){
        if (!appUtil.checkEmpty(specialMenu)) {
            let keys = Object.keys(specialMenu);
            if(keys.length>0){
                keys.map(key => {
                    if(specialProductsMap[key]==null){
                        specialProductsMap[key] = [];
                    }
                    specialMenu[key].map(productId => {
                        if(productId==prodId){
                            specialProductsMap[key].push(productObj);
                        }
                    });
                })
            }
        }
    }

    sortSpecialProductsInMap(specialProductsMap, specialMenu){
        let keys = Object.keys(specialMenu);
        let specialMenuKeys = Object.keys(specialMenu);
        if(keys.length>0){
            keys.map(key => {
                if(specialMenuKeys.length>0){
                    keys.map(specialMenuKey => {
                        if(specialMenuKey==key){
                            var products = specialProductsMap[key];
                            if(products != null && products.length>1){
                                products.sort(function (a,b) {
                                    return specialMenu[specialMenuKey].indexOf(a.key.substring(0,a.key.indexOf(":"))) -
                                        specialMenu[specialMenuKey].indexOf(b.key.substring(0,b.key.indexOf(":")));
                                });
                            }
                        }
                    })
                }
            })
        }
    }

    setBoughtByYou(boughtByYou, productsMap, prod, productObj){
        if(prod.webType != null && prod.webType !== 0 && prod.webType !== 3632){
            if (boughtByYou && boughtByYou.length>0) {
                boughtByYou.map((productId) => {
                    if (productId == prod.id) {
                        var bby = productsMap[2];
                        if (bby == null) {
                            bby = [];
                        }
                        bby.push(productObj);
                        productsMap[2] = bby;
                    }
                })
            }
        }
    }

    sortProductsByPrice(productsMap){
        _.forEach(productsMap, function(value, key) {
            if(key!=1){
                productsMap[key] = _.orderBy(value, [function(o) {
                    return parseFloat(o.key.substring(o.key.indexOf(":")+1));
                }],["desc"]);
            }
        });
    }

    getDummyProductLayout(){
        return (
            <div class="productsWrapper">
                <div class="productContainer">
                    <div class="productImage" style={{background: '#f4f4f4'}}></div>
                    <div class="productDetail">
                        <div class="productTitleWrapper" style={{padding: '0 10px'}}>
                            <div class="productTitle" style={{background: '#f4f4f4'}}></div>
                        </div>
                        <div class="productDescriptionWrapper" style={{padding: '0 10px'}}>
                            <div class="productDescription" style={{marginRight:'0px'}}>
                                <div style={{background: "rgb(244, 244, 244)",margin: "10px 10px 10px",padding: "3px"}}></div>
                                <div style={{background: "rgb(244, 244, 244)",margin: "10px 10px 0px",padding: "3px"}}></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    getDesiChaiArr(){
        return [10, 12, 13, 50];
    }

    getSizeDescriptions(){
        return {  //TODO fix this from backend
            ChotiKetli3625:"Serves 4",
            BadiKetli3625:"Serves 10",
            MiniKetli3625:"Serves 1-2",
            ChotiKetli:"Serves 4",
            BadiKetli:"Serves 10",
            MiniKetli:"Serves 1-2",
            Regular3625:"350ml",
            None3625:"350ml",
            Full3625:"500ml",
            Regular:"200ml",
            None:"200ml",
            Full:"300ml",
            "Oat Mix3625":"350ml",
            "Oat Mix":"350ml"
        }
    }

    getBaarishWaliChaiArr() {
        return [1292, 1293, 1294];
    }
}

const productService = new ProductService();
export default productService;