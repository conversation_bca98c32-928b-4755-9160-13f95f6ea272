export default function reducer(state = {
    product: {},
    membershipIds: [],
    membershipData: null,
    customerInfo: {}
}, action) {
    switch (action.type) {
        case "SET_MEMBERSHIP_DATA": {
            return {...state, product: action.payload};
            break;
        }
        case "SAVE_MEMBERSHIP_IDS": {
            return {...state, membershipIds: action.payload};
            break;
        }
        case "SAVE_MEMBERSHIP_DATA": {
            return {...state, membershipData: action.payload};
            break;
        }
        case "SET_CUSTOMER_INFO": {
            return {...state, customerInfo: action.payload};
            break;
        }
    }
    return state;
}
