import _ from "lodash";

export default function reducer(state = {
    unit: null,
    showError: false,
    fetchingUnitData: false,
    /*topSellersLoaded:false,
    tags: [],
    tagDetail:{},*/
    specialMenu: {},
    productToCustomize: null,
    recipeLoadingProductId: null,
    recipeProducts: [],
    unitInventory: [],
    productTags: {},
    boughtByYou: [],
    initMenu: false,
    skipDeliveryPackaging: {},
    redirectDetails: null,
    loadingRedirect:false,
    offerDetails: [],
    productImages: {}
}, action) {

    switch (action.type) {
        case "LOAD_UNIT_PRODUCTS_PENDING": {
            return {...state, fetchingUnitData: true, productTags: {}, boughtByYou: [], initMenu: false};
            break;
        }
        case "LOAD_UNIT_PRODUCTS_FULFILLED": {
            return {
                ...state,
                unit: action.payload,
                fetchingUnitData: false,
                productTags: {},
                boughtByYou: [],
                initMenu: false
            };
            break;
        }
        case "LOAD_UNIT_PRODUCTS_REJECTED": {
            return {
                ...state,
                showError: true,
                fetchingUnitData: false,
                productTags: {},
                boughtByYou: [],
                initMenu: false
            };
            break;
        }
        case "LOAD_RECIPE_TO_PRODUCT_PENDING": {
            return {...state, recipeLoadingProductId: action.payload};
            break;
        }
        case "LOAD_RECIPE_TO_PRODUCT_REJECTED": {
            return {...state, recipeLoadingProductId: null};
            break;
        }
        case "LOAD_RECIPE_TO_PRODUCT_FULFILLED" : {
            var unit = Object.assign({}, state.unit);
            action.payload.map((productRecipe) => {
                unit.products.map((product) => {
                    if (product.id == productRecipe.productId) {
                        product.prices.map((price) => {
                            price.recipe = productRecipe.recipes[price.dimension];
                        });
                        product.recipesLoaded = true;
                    }
                });
            });
            return {...state, unit: unit, recipeLoadingProductId: null};
            break;
        }
        case "DISCARD_UNIT" : {
            return {...state, unit: null};
            break;
        }
        case "SPECIAL_MENU_LOADED" : {
            var specialMenu = Object.assign({}, state.specialMenu);
            specialMenu = action.payload;
            return {...state, specialMenu: specialMenu, initMenu: true};
            break;
        }
        case "SPECIAL_MENU_REJECTED" : {
            //return {...state, unit: null};
            break;
        }
        case "SET_SHOW_CUSTOMIZATION" : {
            var unit = Object.assign({}, state.unit);
            unit.products.map((product) => {
                if (product.id == action.payload.id) {
                    product.showCustomization = true;
                }
            });
            return {...state, unit: unit};
            break;
        }
        case "SET_RECIPE_PRODUCTS" : {
            return {...state, recipeProducts: action.payload};
            break;
        }
        case "SILENT_LOAD_PRODUCT_RECIPE" : {
            var unit = Object.assign({}, state.unit);
            action.payload.map((productRecipe) => {
                if (unit != null && unit.products != null && unit.products.length > 0) {
                    unit.products.map((product) => {
                        if (product.id == productRecipe.productId) {
                            product.prices.map((price) => {
                                price.recipe = productRecipe.recipes[price.dimension];
                            });
                            product.recipesLoaded = true;
                        }
                    });
                }
            });
            return {...state, unit: unit};
            break;
        }
        case "INVENTORY_FULFILLED" : {
            var unit = Object.assign({}, state.unit);
            _.map(unit.products, (product) => {
                product.stock = action.payload[product.id];
                product.inventoryLoaded = true;
            });
            return {...state, unit: unit, unitInventory: action.payload};
            break;
        }
        case "INVENTORY_UPDATE" : {
            var unit = Object.assign({}, state.unit);
            _.map(unit.products, (product) => {
                if (action.payload[product.id] != null) {
                    product.stock = action.payload[product.id];
                    product.inventoryLoaded = true;
                }
            });
            return {...state, unit: unit, unitInventory: action.payload};
            break;
        }
        case "PRODUCT_TAGS_LOADED" : {
            return {...state, productTags: action.payload};
            break;
        }
        case "BOUGHT_BY_YOU_LOADED" : {
            return {...state, boughtByYou: action.payload, initMenu: true};
            break;
        }
        case "SET_INIT_MENU" : {
            return {...state, initMenu: action.payload};
            break;
        }
        case "SET_SKIP_DELIVERY_PACKAGING" : {
            return {...state, skipDeliveryPackaging: action.payload};
            break;
        }
        case "SET_REDIRECT_DETAILS" : {
            return {...state, redirectDetails: action.payload};
            break;
        }
        case "LOADING_REDIRECT" : {
            return {...state, loadingRedirect: action.payload};
            break;
        }
        case "SET_OFFER_DETAILS" : {
            return {...state, offerDetails: action.payload};
        }
        case "PRODUCT_IMAGES_LOADED" : {
            return {...state, productImages: action.payload};
        }
    }

    return state;

}