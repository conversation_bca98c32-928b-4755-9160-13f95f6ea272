export default function reducer(state = {
    sidebarOpen: false,
    loginStatus:null
}, action) {

    switch (action.type) {
        case "TOGGLE_SIDEBAR":
        {
            var status = state.sidebarOpen;
            return {...state, sidebarOpen:!status };
            break;
        }
        case "SET_LOGIN_STATUS":
        {
            return {...state, loginStatus:action.payload };
            break;
        }
    }

    return state;

}