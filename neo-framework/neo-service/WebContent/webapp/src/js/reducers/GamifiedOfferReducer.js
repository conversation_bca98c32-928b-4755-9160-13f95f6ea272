export default function reducer(state = {
    futureScreen:false,
    gameScore:0,
    showContactNumberField:true,
    showNameField:false,
    showOTPField:false,
    gamifiedOffer:{},
    contactNumber:null,
    customerName:null,
    email:null,
    otp:null,
    name:null,
    leaderboardData:{},
    sessionKey:null,
    authToken:null,
    refLink:null,
    isShowGame:false,
    isStartGame:false,
    screenshotString:'',

}, action) {

    switch (action.type) {
        case "CLEAR_REDUCER":
        {
            return {
                ...state,

            };
        }
        case "SET_GAMIFIED_CURRENT_SCREEN":
        {
            return {...state, currentScreen: action.payload};
        }
        case "SET_GAMIFIED_FUTURE_SCREEN":
        {
            return {...state, futureScreen: action.payload};
        }
        case "SET_GAME_SCORE":
        {
            return {...state, gameScore: action.payload};
        }
        case "SET_SHOW_CONTACT_FIELD":
        {
            return {...state, showContactNumberField: action.payload};
        }
        case "SET_SHOW_NAME_FIELD":
        {
            return {...state, showNameField: action.payload};
        }
        case "SET_SHOW_OTP_FIELD":
        {
            return {...state, showOTPField: action.payload};
        }
        case "SET_GAMIFIED_OFFER":
        {
            return {...state, gamifiedOffer: action.payload};
        }
        case "SET_CONTACT_NUMBER":
        {
            return {...state, contactNumber: action.payload};
        }
        case "SET_CUSTOMER_NAME":
        {
            return {...state, customerName: action.payload};
        }
        case "SET_EMAIL":
        {
            return {...state, contactNumber: action.payload};
        }
        case "SET_OTP":
        {
            return {...state, otp: action.payload};
        }
        case "SET_NAME":
        {
            return {...state, name: action.payload};
        }
        case "SET_LEADERBOARD_DATA":
        {
            return {...state, leaderboardData: action.payload};
        }
        case "SET_SESSION_KEY":
        {
            return {...state, sessionKey: action.payload};
        }
        case "SET_AUTH_TOKEN":
        {
            return {...state, authToken: action.payload};
        }
        case "SET_REF_LINK":{
            return {...state, refLink: action.payload}
        }
        case "SET_SHOW_GAME":{
            return {...state, isShowGame: action.payload}
        }
        case "SET_START_GAME":{
            return {...state, isStartGame: action.payload}
        }
        case "SET_SCREENSHOT_STRING":{
            return {...state, screenshotString: action.payload}
        }
    }

    return state;

}
