import { combineReducers } from "redux";
import localityReducer from "./LocalityReducer";
import outletMenuReducer from "./OutletMenuReducer";
import customizationModalReducer from "./CustomizationModalReducer";
import utilityReducer from "./UtilityReducer";
import cartManagementReducer from "./CartManagementReducer";
import customerReducer from "./CustomerReducer";
import orderManagementReducer from "./OrderManagementReducer";
import sidebarReducer from "./SidebarReducer";
import paymentReducer from "./PaymentReducer";
import campaignReducer from "./CampaignReducer";
import promoOfferReducer from "./PromoOfferReducer";
import signUpOfferReducer from "./SignUpOfferReducer";
import myOfferReducer from "./MyOfferReducer";
import membershipReducer from "./MembershipReducer";
import slotMachineReducer from "./SlotMachineReducer";
import gamifiedOfferReducer from "./GamifiedOfferReducer";

export default combineReducers({
    localityReducer,
    outletMenuReducer,
    customizationModalReducer,
    utilityReducer,
    cartManagementReducer,
    customerReducer,
    orderManagementReducer,
    sidebarReducer,
    paymentReducer,
    campaignReducer,
    promoOfferReducer,
    signUpOfferReducer,
    myOfferReducer,
    membershipReducer,
    slotMachineReducer,
    gamifiedOfferReducer
})
