/**
 * Created by Chaayos on 18-02-2017.
 */
export default function reducer(state = {
    paymentInitiated:false,
    paymentMessage: "",
    paymentStatus:"CREATED",
    showLoader: false,
    failureMessage:null
}, action) {

    switch (action.type) {
        case "SET_PAYMENT_DATA":
        {
            return {...state, paymentMessage: action.payload.paymentMessage,
                paymentStatus: action.payload.paymentStatus,
                showLoader: action.payload.showLoader,
                failureMessage: action.payload.failureMessage,
            };
            break;
        }
        case "SET_PAYMENT_INITIATED":
        {
            return {...state, paymentInitiated: action.payload};
            break;
        }
    }

    return state;

}