import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";

export default function reducer(state = {
    product: {},
    compositeProduct: null,
    compositeDimension: null,
    compositeQuantity: null,
    compositeAddons: [],
    isConstituent: false,
    showLoader: true,
    showError: false,
    showModal: false,
    selectedDimension: null,
    selectedAddons: [],
    quantity: 1,
    customizationType: "MENU_ITEM",
    cartEditItemId:null
}, action) {

    switch (action.type) {
        case "SHOW_CUSTOMIZATION_MODAL":
        {
            var retObj = appUtil.setCustomizationModal(action.payload.product,action.payload.cartItem);
            return {
                ...state,
                product: retObj.product,
                showLoader: !retObj.product.recipesLoaded,
                showModal: true,
                selectedDimension: retObj.selectedDimension,
                selectedAddons: retObj.selectedAddons,
                quantity: retObj.quantity,
                customizationType: action.payload.type,
                cartEditItemId: retObj.cartEditItemId
            };
            break;
        }
        case "HIDE_CUSTOMIZATION_MODAL":
        {
            return {...state, showLoader: true, showModal: false};
            break;
        }
        /*case "LOAD_RECIPE_TO_PRODUCT_PENDING":{
         return {...state, showLoader:true};
         break;
         }*/
        case "LOAD_RECIPE_TO_PRODUCT_FULFILLED":
        {
            var product = Object.assign({}, state.product);
            if(!appUtil.checkEmpty(product)){
                action.payload.map((productRecipe) => {
                    var pid = productRecipe.productId==11?[10,11,12,50]:[productRecipe.productId];
                    if (pid.indexOf(product.id)>=0) {
                        product.prices.map((price) => {
                            price.recipe = productRecipe.recipes[price.dimension];
                            price.recipe.ingredient.products.map((product) => {
                                product.details.map((detail) => {
                                    detail.active = detail.defaultSetting;
                                })
                            });
                            price.recipe.ingredient.variants.map((variant) => {
                                variant.details.map((detail) => {
                                    detail.active = detail.defaultSetting;
                                })
                            });
                        });
                        product.recipesLoaded = true;
                    }
                });
                var retObj;
                if(state.customizationType=="MENU_ITEM"){
                    retObj = appUtil.setCustomizationModal(product,null);
                }else{
                    var cartItem = null;
                    StorageUtils.getCartDetail().orderDetail.orders.map((orderItem) => {
                        if(orderItem.itemId==state.cartEditItemId){
                            cartItem = orderItem;
                        }
                    })
                    retObj = appUtil.setCustomizationModal(product, cartItem);
                }
                return {
                    ...state,
                    product: retObj.product,
                    showLoader: !retObj.product.recipesLoaded,
                    selectedDimension: retObj.selectedDimension,
                    selectedAddons: retObj.selectedAddons,
                    quantity: retObj.quantity,
                    customizationType: state.customizationType
                };
            }
            break;
        }
        case "LOAD_RECIPE_TO_PRODUCT_REJECTED":
        {
            return {...state, showLoader: false, showError: true};
            break;
        }
        case "SET_SELECTED_DIMENSION":
        {
            return {...state, selectedDimension: action.payload};
            break;
        }
        case "ADD_ADDON":
        {
            var selectedAddons = Object.assign([], state.selectedAddons);
            if (selectedAddons.indexOf(action.payload)==-1) {
                selectedAddons.push(action.payload);
            }
            return {...state, selectedAddons: selectedAddons};
            break;
        }
        case "REMOVE_ADDON":
        {
            var selectedAddons = Object.assign([], state.selectedAddons);
            var contains = false;
            selectedAddons.map((addon, index) => {
                if (addon == action.payload) {
                    contains = true;
                    selectedAddons.splice(index, 1);
                }
            });
            if(selectedAddons.indexOf(988)>=0){
                selectedAddons.splice(selectedAddons.indexOf(988), 1);
            }
            return {...state, selectedAddons: selectedAddons};
            break;
        }
        case "ADD_ALL_ADDONS":
        {
            return {...state, selectedAddons: action.payload};
            break;
        }
        case "SET_SELECTED_VARIANT":
        {
            var selectedDimension = Object.assign([], state.selectedDimension);
            selectedDimension.recipe.ingredient.variants.map((variant) => {
                if (variant.product.productId == action.payload.productId) {
                    variant.details.map((variantDetail) => {
                        if (variantDetail.alias == action.payload.alias) {
                            variantDetail.active = true;
                        } else {
                            variantDetail.active = false;
                        }
                    })
                }
            })
            return {...state, selectedDimension: selectedDimension};
            break;
        }
        case "SET_INGREDIENT_PRODUCT":
        {
            var selectedDimension = Object.assign([], state.selectedDimension);
            selectedDimension.recipe.ingredient.products.map((product) => {
                //if (product.display==action.payload.display) {
                product.details.map((productDetail) => {
                    if (productDetail.product.productId == action.payload.productId) {
                        productDetail.active = true;
                    } else {
                        productDetail.active = false;
                    }
                })
                //}
            })
            return {...state, selectedDimension: selectedDimension};
            break;
        }
        case "UPDATE_PRODUCT_QTY":
        {
            return {...state, quantity: action.payload};
            break;
        }
        case "SELECT_MENU_PRODUCT":
        {
            var selectedDimension = Object.assign([], state.selectedDimension);
            var constituentQuantity = 0;
            selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.name == action.payload.itemName) {
                    detail.menuProducts.map((product) => {
                        if (product.product.productId == action.payload.productId) {
                            detail.selectedProduct = product;
                            detail.currentItem = true;
                        }
                    });
                    constituentQuantity = detail.selectedProduct.quantity;
                } else {
                    detail.currentItem = false;
                }
            });
            var product = Object.assign({}, state.product);
            var quantity = state.quantity;
            var addons = Object.assign({}, state.selectedAddons);
            return {
                ...state, compositeProduct: product, compositeDimension: selectedDimension,
                compositeQuantity: quantity, product: action.payload.constituentProduct,
                selectedDimension: action.payload.constituentProduct.prices[0],
                compositeAddons: addons, selectedAddons: action.payload.selectedAddons, quantity: constituentQuantity,
                isConstituent: true
            };
            break;
        }
        case "BACK_TO_COMPOSITE_PRODUCT":
        {
            var selectedDimension = Object.assign([], state.compositeDimension);
            var product = Object.assign({}, state.compositeProduct);
            var quantity = state.compositeQuantity;
            var selectedAddons = Object.assign([], state.compositeAddons);
            selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.currentItem) {
                    detail.selectedProduct = null;
                    detail.cartProduct = null;
                }
            });
            return {
                ...state, compositeProduct: null, compositeDimension: null, compositeQuantity: null,
                product: product, selectedDimension: selectedDimension, compositeAddons: null,
                selectedAddons: selectedAddons, quantity: quantity, isConstituent: false
            };
            break;
        }
        case "SUBMIT_MENU_PRODUCT_CUSTOMIZATION":
        {
            var selectedDimension = Object.assign([], state.compositeDimension);
            var product = Object.assign({}, state.compositeProduct);
            var quantity = state.compositeQuantity;
            var selectedAddons = Object.assign([], state.compositeAddons);
            selectedDimension.recipe.ingredient.compositeProduct.details.map((detail) => {
                if (detail.currentItem) {
                    detail.cartProduct = action.payload;
                    detail.cartProduct.itemName = detail.name;
                }
            });
            return {
                ...state, compositeProduct: null, compositeDimension: null, compositeQuantity: null,
                product: product, selectedDimension: selectedDimension, compositeAddons: null,
                selectedAddons: selectedAddons, quantity: quantity, isConstituent: false
            };
            break;
        }
        case "CREATE_CART":
        {
            document.getElementsByTagName("BODY")[0].style.overflow = "auto";
            return {...state, showLoader: true, showModal: false};
            break;
        }
        case "ADD_ITEM_TO_CART":
        {
            document.getElementsByTagName("BODY")[0].style.overflow = "auto";
            return {...state, showLoader: true, showModal: false};
            break;
        }
        case "UPDATE_CUSTOMIZATION_PRODUCT":
        {
            return {...state, product: action.payload};
            break;
        }
        /*case "PRODUCT_TO_CUSTOMIZE":
        {
            return {
                ...state,
                product: action.payload,
                showLoader: action.payload.recipesLoaded!=null?!action.payload.recipesLoaded:false,
                showModal: false,
                selectedDimension: action.payload.prices[0],
                selectedAddons: [],
                quantity: 0,
                customizationType: "MENU_ITEM",
                cartEditItemId: null
            };
            break;
        }*/
    }

    return state;

}