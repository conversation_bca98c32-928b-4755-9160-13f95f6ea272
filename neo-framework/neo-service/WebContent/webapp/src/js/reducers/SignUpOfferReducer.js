export default function reducer(state = {
    customer: null,
    currentStage: null,
    product: null,
    customizedOfferProduct: null,
    availableTimeSlots: null
}, action) {

    switch (action.type) {
        case "SET_SIGN_UP_OFFER_CUSTOMER":
        {
            return {...state, customer: action.payload};
        }
        case "SET_SIGN_UP_OFFER_STAGE":
        {
            return {...state, currentStage: action.payload};
        }
        case "SET_SIGN_UP_OFFER_PRODUCT":
        {
            return {...state, product: action.payload};
        }
        case "SET_CUSTOMIZED_OFFER_PRODUCT":
        {
            return {...state, customizedOfferProduct: action.payload};
        }
        case "SET_AVAILABLE_TIME_SLOTS":
        {
            return {...state, availableTimeSlots: action.payload};
        }
    }

    return state;

}
