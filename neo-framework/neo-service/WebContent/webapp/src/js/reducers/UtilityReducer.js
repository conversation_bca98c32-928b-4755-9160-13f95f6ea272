export default function reducer(state = {
    internetError:false,
    showPopupMessage: false,
    showFullPageLoader: false,
    messageText: null,
    messageType: "info",
    loaderMessage: null,
    promptMessage: null,
    showPrompt: false,
    promptSuccess:null,
    promptDismiss:null,
    isLandscape:null,
    cityOutlets:[],
    cityOutlet:null,
    cityOutletsLoaded: false,
    restrictDineIn:false,
    membershipType: "select"
}, action) {

    switch (action.type) {
        case "SET_INTERNET_ERROR":
        {
            return {...state, internetError: action.payload};
            break;
        }
        case "SHOW_POPUP":
        {
            if(action.payload.type==null){
                action.payload.type="info"
            }
            return {...state, showPopupMessage: true, messageText: action.payload.message, messageType:action.payload.type};
            break;
        }
        case "HIDE_POPUP":
        {
            return {...state, showPopupMessage: false, messageText: null};
            break;
        }
        case "SHOW_FULLPAGE_LOADER":
        {
            return {...state, showPopupMessage: false, showFullPageLoader: true, loaderMessage: action.payload};
            break;
        }
        case "HIDE_FULLPAGE_LOADER":
        {
            return {...state, showFullPageLoader: false, loaderMessage: null};
            break;
        }
        case "SHOW_PROMPT":
        {
            var success = null;
            if(action.payload.success!=null && typeof action.payload.success=="function"){
                success = action.payload.success;
            }
            var dismiss = null;
            if(action.payload.dismiss!=null && typeof action.payload.dismiss=="function"){
                dismiss = action.payload.dismiss;
            }
            return {...state, showPrompt: true, promptMessage: action.payload.message, promptSuccess:success, promptDismiss:dismiss};
            break;
        }
        case "HANDLE_PROMPT":
        {
            return {...state, showPrompt: false, promptMessage: null, promptSuccess:null, promptDismiss:null};
            break;
        }
        case "SET_ORIENTATION":
        {
            return {...state, isLandscape: action.payload};
            break;
        }
        case "SET_CITY_OUTLET":
        {
            return {...state, cityOutlets: action.payload};
            break;
        }
        case "SET_CITY_OUTLET_LOADED":
        {
            return {...state, cityOutletsLoaded: action.payload};
            break;
        }
        case "SET_CITY_MAP_UNIT":
        {
            return {...state, cityOutlet: action.payload};
            break;
        }
        case "RESTRICT_DINE_IN":
        {
            return {...state, restrictDineIn: action.payload};
            break;
        }
        case "SET_MEMBERSHIP_TYPE":
        {
            return {...state, membershipType: action.payload};
            break;
        }
    }

    return state;

}
