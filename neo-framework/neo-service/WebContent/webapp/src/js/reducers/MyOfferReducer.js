export default function reducer(state = {
    customer: null,
    currentStage: null,
    product: null,
    customizedOfferProduct: null,
    availableTimeSlots: null,
    myOfferDetail:null,
    myOfferJourney:null,
    isLoadingCampaign:false,
    campaignDetails: {},
    contactNumber:null,
    customerName:null,
    utmData: {},
    isShowName: null
}, action) {

    switch (action.type) {
        case "SET_SIGN_UP_OFFER_CUSTOMER":
        {
            return {...state, customer: action.payload};
        }
        case "SET_MY_OFFER_STAGE":
        {
            return {...state, currentStage: action.payload};
        }
        case "SET_MY_OFFER_DETAIL":{
            return {...state, myOfferDetail: action.payload}
        }
        case "MY_OFFER_JOURNEY":
        {
            return {...state, myOfferJourney: action.payload};
        }
        case "IS_CAMPAIGN_LOADING":
        {
            return {...state, isLoadingCampaign: action.payload}
        }
        case "SET_CAMPAIGN_DETAILS":
        {
            return {...state, campaignDetails: action.payload}
        }
        case "SET_UTM_DATA":
        {
            return {...state, utmData: action.payload}
        }
        case "SET_CONTACT_NUMBER":
        {
            return {...state, contactNumber: action.payload}
        }
        case "SET_CUSTOMER_NAME":
        {
            return {...state, customerName: action.payload}
        }
        case "SET_IS_SHOW_NAME":
        {
            return {...state, isShowName: action.payload}
        }
    }

    return state;

}
