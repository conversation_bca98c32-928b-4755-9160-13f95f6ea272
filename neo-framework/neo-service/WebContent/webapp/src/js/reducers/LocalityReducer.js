import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";

export default function reducer(state = {
    localities: [],
    cities: [],
    outlets: [],
    cityStateMap:{},
    selectedLocality: null,
    selectedCity: null,
    selectedOutlet: null,
    error: null,
    criteria: null,
    showLocality: false,
    showOutlet: false,
    showLoader: false,
    showError: false,
    showLocationWrapper:false,

}, action) {

    switch (action.type) {
        case "LOAD_CITIES_PENDING":
        {
            return {...state, showLoader: true, showLocality:false, showOutlet: false};
            break;
        }
        case "LOAD_LOCALITIES_PENDING":
        {
            return {...state, showLoader: true, showLocality:false, showOutlet: false};
            break;
        }
        case "LOAD_OUTLETS_PENDING":
        {
            return {...state, showLoader: true, showLocality:false, showOutlet: false};
            break;
        }
        case "LOAD_CITIES_FULFILLED":
        {
            return {...state, cities: action.payload, showLoader: false, showOutlet: false, showLocality: false};
            break;
        }
        case "SET_CITY_STATE_MAP":
        {
            return {...state, cityStateMap: action.payload};
            break;
        }
        case "LOAD_LOCALITIES_FULFILLED":
        {
            var selectedLocality = null;
            if (!appUtil.checkEmpty(StorageUtils.getLocalityMetadata().locality)) {
                selectedLocality = StorageUtils.getLocalityMetadata().locality;
            }
            return {
                ...state,
                localities: action.payload.data,
                showLoader: false,
                showLocality: true,
                showOutlet: false,
                selectedLocality: selectedLocality
            };
            break;
        }
        case "LOAD_OUTLETS_FULFILLED":
        {
            var selectedOutlet = null;
            if (!appUtil.checkEmpty(StorageUtils.getLocalityMetadata().outlet)) {
                selectedOutlet = StorageUtils.getLocalityMetadata().outlet;
            }
            return {
                ...state,
                outlets: action.payload.data,
                showLoader: false,
                showOutlet: true,
                showLocality: false,
                selectedOutlet: selectedOutlet
            };
            break;
        }
        case "LOAD_CITIES_REJECTED":
        {
            return {...state, showError: true, showLoader: false, showLocality: false, showOutlet: false};
            break;
        }
        case "LOAD_LOCALITIES_REJECTED":
        {
            return {
                ...state, showError: true, showLoader: false,
                showLocality: false, showOutlet: false
            };
            break;
        }
        case "LOAD_OUTLETS_REJECTED":
        {
            return {...state, showError: true, showLoader: false, showLocality: false, showOutlet: false};
            break;
        }
        case "SELECT_CITY":
        {
            return {...state, selectedCity: action.payload, selectedLocality: null, selectedOutlet: null};
            break;
        }
        case "SELECT_LOCALITY":
        {
            return {...state, selectedLocality: action.payload};
            break;
        }
        case "SELECT_OUTLET":
        {
            return {...state, selectedOutlet: action.payload};
            break;
        }
        case "HIDE_ERROR":
        {
            return {...state, showError: false};
            break;
        }
        case "SET_CRITERIA":
        {
            return {...state, criteria: action.payload};
            break;
        }
        case "SET_CITY":
        {
            return {...state, selectedCity: action.payload};
            break;
        }
        case "SET_LOCATION_WRAPPER":
        {
            return {...state, showLocationWrapper: action.payload};
            break;
        }
        case "SET_SHOW_LOCALITY":
        {
            return {...state, showLocality: action.payload, showOutlet: !action.payload};
            break;
        }
        case "SET_SHOW_OUTLET":
        {
            return {...state, showOutlet: action.payload, showLocality: !action.payload};
            break;
        }
    }

    return state;

}