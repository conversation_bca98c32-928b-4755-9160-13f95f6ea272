/**
 * Created by Chaayos on 01-12-2016.
 */
import StorageUtils from "../utils/StorageUtils";

export default function reducer(state = {
    customer: null,
    showContactSection: true,
    isLogin:true,
    getName: false,
    getEmail:false,
    verifiedByTrueCaller:false,
    redirectTo: null,
    sessionKey: null,
    deviceKey:null,
    addresses: [],
    selectedAddress: null,
    status:null,
    showCaptcha:false,
    addressViewType:"PROFILE",
    customerLoyalteaScore:null,
    loyalteaPending:true,
    otpResendSeconds:0,
    optOutPending: false,
    giftcardAmountPending: true,
    customerGiftcardAmount: null
}, action) {

    switch (action.type) {
        case "RESET_LOGIN":
        {
            return {...state, showContactSection:true, isLogin:true, getName: false, getEmail:false};
        }
        case "SET_LOGIN_REDIRECT":
        {
            return {...state, redirectTo: action.payload};
        }
        case "LOOKUP_TRUECALLER_CUSTOMER_PENDING":
        {
            var customer = Object.assign({}, state.customer);
            customer.requestId = action.payload;
            return {...state, customer: customer};
        }
        case "LOOKUP_CUSTOMER_PENDING":
        {
            var customer = Object.assign({}, state.customer);
            customer.contact = action.payload;
            return {...state, customer: customer};
        }
        case "LOOKUP_CUSTOMER_FULFILLED":
        {
            //as they say, there is always a better way to do this
            if(action.payload==-2){
                return {...state, getName: false, getEmail:false, isLogin:false, showContactSection: true}
            }
            if(action.payload==-1){
                return {...state, getName: true, getEmail:true, isLogin:false, showContactSection: false}
            }
            if(action.payload==1){
                return {...state, getName: false, getEmail:false, isLogin:true, showContactSection: false}
            }
            if(action.payload==2){
                return {...state, getName: true, getEmail:false, isLogin:true, showContactSection: false}
            }
            if(action.payload==3){
                return {...state, getName: false, getEmail:true, isLogin:true, showContactSection: false}
            }
            if(action.payload==4){
                return {...state, getName: true, getEmail:true, isLogin:true, showContactSection: false}
            }
            if(action.payload==5){
                return {...state, getName: false, getEmail:false, isLogin:false, showContactSection: false}
            }
            if(action.payload==6){
                return {...state, getName: true, getEmail:false, isLogin:false, showContactSection: false}
            }
            if(action.payload==7){
                return {...state, getName: false, getEmail:true, isLogin:false, showContactSection: false}
            }
            if(action.payload==8){
                return {...state, getName: true, getEmail:true, isLogin:false, showContactSection: false}
            }
            break;
        }
        case "VERIFIED_BY_TRUECALLER":
        {
            return{...state, verifiedByTrueCaller: action.payload }
        }
        case "LOOKUP_CUSTOMER_REJECTED":
        {
            break;
        }
        case "LOGIN_CUSTOMER_PENDING":
        {
            break;
        }
        case "LOGIN_CUSTOMER_FULFILLED":
        {
            return {...state, sessionKey: action.payload};
        }
        case "LOGIN_CUSTOMER_REJECTED":
        {
            break;
        }
        case "SIGNUP_CUSTOMER_PENDING":
        {
            break;
        }
        case "SIGNUP_CUSTOMER_FULFILLED":
        {
            return {...state, sessionKey: action.payload};
        }
        case "SIGNUP_CUSTOMER_REJECTED":
        {
            break;
        }
        case "CUSTOMER_ADDRESSES_PENDING":
        {
            return {...state, status:"PENDING"};
        }
        case "CUSTOMER_ADDRESSES_FULFILLED":
        {
            return {...state, addresses: action.payload, status:"FULFILLED"};
        }
        case "CUSTOMER_ADDRESSES_REJECTED":
        {
            return {...state, status:"REJECTED"};
        }
        case "SELECT_ADDRESS":
        {
            var selectedAddress;
            state.addresses.map((address) => {
                if (address.id == action.payload) {
                    selectedAddress = address;
                }
            });
            return {...state, selectedAddress: selectedAddress};
        }
        case "ADD_ADDRESS":
        {
            var selectedAddress = action.payload;
            var addresses = Object.assign([], state.addresses);
            addresses.push(action.payload);

            return {...state, addresses:addresses, selectedAddress: selectedAddress};
        }
        case "REMOVE_SELECTED_ADDRESS":
        {
            return {...state, selectedAddress: null};
        }
        case "SET_CUSTOMER":
        {
            return {...state, customer: action.payload};
        }
        case "SHOW_CAPTCHA":
        {
            return {...state, showCaptcha:true};
        }
        case "SET_ADDRESS_VIEW_TYPE":
        {
            return {...state, addressViewType:action.payload};
        }
        case "GET_LOYALTEA_SCORE_PENDING":
        {
            return {...state, loyalteaPending:true};
        }
        case "GET_LOYALTEA_SCORE_FULFILLED":
        {
            return {...state, loyalteaPending:false, customerLoyalteaScore:action.payload};
        }
        case "GET_LOYALTEA_SCORE_REJECTED":
        {
            return {...state, loyalteaPending:false};
        }
        case "SET_CUSTOMER_DETAIL":
        {
            return {...state, customer: action.payload};
        }
        case "SET_DEVICE_KEY":
        {
            return {...state, deviceKey: action.payload};
        }
        case "SET_SESSION_KEY":
        {
            return {...state, sessionKey: action.payload};
        }
        case "SET_OTP_RESEND_SECONDS":
        {
            return {...state, otpResendSeconds: action.payload};
        }
        case "FACE_IT_OPT_OUT_PENDING":
        {
            return {...state, optOutPending: true};
        }
        case "FACE_IT_OPT_OUT_SUCCESS":
        {
            return {...state, optOutPending: false};
        }
        case "FACE_IT_OPT_OUT_FAILED":
        {
            return {...state, optOutPending: true};
        }
        case "GET_GIFTCARD_AMOUNT_PENDING":
        {
            return {...state, giftcardAmountPending:true};
        }
        case "GET_GIFTCARD_AMOUNT_FULFILLED":
        {
            return {...state, giftcardAmountPending:false, customerGiftcardAmount:action.payload};
        }
        case "GET_GIFTCARD_AMOUNT_REJECTED":
        {
            return {...state, giftcardAmountPending:false};
        }
    }
    return state;

}