class APIs {

    constructor() {
       //this.neoBaseUrl = "http://dev.kettle.chaayos.com:8080/neo-service/rest/v1/";
       this.neoBaseUrl = "https://relax.chaayos.com/neo-service/rest/v1/";
       //this.neoBaseUrl = "http://dev.chaayos.com:9595/neo-service/rest/v1/";
       //this.neoBaseUrl = "http://localhost:8080/neo-service/rest/v1/";
       //this.neoBaseUrl = "http://stage.kettle.chaayos.com:8080/neo-service/rest/v1/";

        // this.truecallerBaseUrl="http://dev.kettle.chaayos.com:8080/truecaller-service/rest/v1/";
        this.truecallerBaseUrl="https://relax.chaayos.com/truecaller-service/rest/v1/";
        // this.truecallerBaseUrl="http://dev.chaayos.com:9595/truecaller-service/rest/v1/";
        // this.truecallerBaseUrl="http://localhost:8080/truecaller-service/rest/v1/";
        // this.truecallerBaseUrl="http://stage.kettle.chaayos.com:8080/truecaller-service/rest/v1/";


        this.NEO_CACHE_ROOT_CONTEXT = this.neoBaseUrl + "nc/";
        this.STAMPING_ROOT_CONTEXT = this.neoBaseUrl + "st/";
        this.UNIT_CACHE_ROOT_CONTEXT = this.neoBaseUrl + "uc/";
        this.WEB_CART_ROOT_CONTEXT = this.neoBaseUrl + "wcrt/";
        this.WEB_ORDER_ROOT_CONTEXT = this.neoBaseUrl + "word/";
        this.WEB_CUSTOMER_ROOT_CONTEXT = this.neoBaseUrl + "c/";
        this.TRUECALLER_CUSTOMER_ROOT_CONTEXT= this.truecallerBaseUrl + "tc/";

        
        this.WEB_PAYMENT_ROOT_CONTEXT = this.neoBaseUrl + "wp/";
        this.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT = this.neoBaseUrl + "wpe/";
        this.WEB_OFFER_ROOT_CONTEXT = this.neoBaseUrl + "woff/";
        this.WEB_INVENTORY_ROOT_CONTEXT = this.neoBaseUrl + "win/";
        this.NEO_METADATA_ROOT_CONTEXT = this.neoBaseUrl + "ncm/";
        this.NEO_EXTERNAL_PARTNER_ROOT_CONTEXT = this.neoBaseUrl + "exp/";

        this.urls = {
            neoCache: {
                getLocalities: this.NEO_CACHE_ROOT_CONTEXT + "lcts",
                getCities: this.NEO_CACHE_ROOT_CONTEXT + "cts",
                getDeliveryUnit: this.NEO_CACHE_ROOT_CONTEXT + "lct/d/u2",
                getLocalityData: this.NEO_CACHE_ROOT_CONTEXT + "ct/d/u1",
                getTakeawayUnits: this.NEO_CACHE_ROOT_CONTEXT + "lct/t/us",
                productRecipes: this.NEO_CACHE_ROOT_CONTEXT + "p/r",
                tags: this.NEO_CACHE_ROOT_CONTEXT + "ts",
                tag: this.NEO_CACHE_ROOT_CONTEXT + "t",
                specialMenu: this.NEO_CACHE_ROOT_CONTEXT + "mts",
                productTags: this.NEO_CACHE_ROOT_CONTEXT + "pts",
                productImages: this.NEO_CACHE_ROOT_CONTEXT + "pi",
                membershipProduct: this.UNIT_CACHE_ROOT_CONTEXT + "get-sku-code-product-images",
                membershipProductUrl: this.UNIT_CACHE_ROOT_CONTEXT + "get-single-product-images",
                getMembershipProductIds: this.UNIT_CACHE_ROOT_CONTEXT + "get-subscription-products-for-unit",
                getMembershipProductsData:this.UNIT_CACHE_ROOT_CONTEXT+"get-subscription-products-images-for-unit",
            },
            unitCache: {
                getUnit: this.UNIT_CACHE_ROOT_CONTEXT + "u"
            },
            stamping: {
                registerDevice: this.STAMPING_ROOT_CONTEXT + "rd",
                stampDevice: this.STAMPING_ROOT_CONTEXT + "sd"
            },
            cart: {
                createCart: this.WEB_CART_ROOT_CONTEXT + "c",
                createCartFromOrderId: this.WEB_CART_ROOT_CONTEXT + "c2",
                addItem: this.WEB_CART_ROOT_CONTEXT + "i/a",
                updateItem: this.WEB_CART_ROOT_CONTEXT + "i/u",
                removeItem: this.WEB_CART_ROOT_CONTEXT + "i/r",
                clearCart: this.WEB_CART_ROOT_CONTEXT + "clr",
                checkout: this.WEB_CART_ROOT_CONTEXT + "ckt",
                checkoutId: this.WEB_CART_ROOT_CONTEXT + "ckt/id",
                checkoutWOId: this.WEB_CART_ROOT_CONTEXT + "ckt/wo",
                sync: this.WEB_CART_ROOT_CONTEXT + "sync"
            },
            customer: {
                lookup: this.WEB_CUSTOMER_ROOT_CONTEXT + "lkp",
                lookupOnly:this.WEB_CUSTOMER_ROOT_CONTEXT+"lkpo",
                truecallerLookup: this.TRUECALLER_CUSTOMER_ROOT_CONTEXT + "get-profile",
                login: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgn",
                signUp: this.WEB_CUSTOMER_ROOT_CONTEXT + "su",
                addresses: this.WEB_CUSTOMER_ROOT_CONTEXT + "as",
                addAddress: this.WEB_CUSTOMER_ROOT_CONTEXT + "add/as",
                addAddressToCart: this.WEB_CUSTOMER_ROOT_CONTEXT + "up/a/c",
                logout: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgt",
                resendVerification: this.WEB_CUSTOMER_ROOT_CONTEXT + "v/r2",
                loyaltea: this.WEB_CUSTOMER_ROOT_CONTEXT + "lyt",
                boughtByYou: this.WEB_CUSTOMER_ROOT_CONTEXT + "bbu",
                loginKey: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgn/key",
                faceItOptOut: this.WEB_CUSTOMER_ROOT_CONTEXT + "oofiid",
                contactOptOut: this.WEB_CUSTOMER_ROOT_CONTEXT + "oofic",
                availableGiftCardAmount: this.WEB_CUSTOMER_ROOT_CONTEXT + "gagca",
                signUpOfferSignUp: this.WEB_CUSTOMER_ROOT_CONTEXT + "supo/su",
                myOffer: this.WEB_CUSTOMER_ROOT_CONTEXT + "my-offer",
                saveJourney: this.WEB_CUSTOMER_ROOT_CONTEXT + "cj",
                signUpVerified: this.WEB_CUSTOMER_ROOT_CONTEXT + "suv",
                signUpOfferTimeSlots: this.WEB_CUSTOMER_ROOT_CONTEXT + "supo/ts",
                getCustomerInfoByContact:this.WEB_CUSTOMER_ROOT_CONTEXT +'customer-by-contact',
                lookupAndGetOffer:this.WEB_CUSTOMER_ROOT_CONTEXT + 'lkp-and-offer',
                registerAndGetOffer:this.WEB_CUSTOMER_ROOT_CONTEXT + 'register-and-offer',
                leaderboard:this.WEB_CUSTOMER_ROOT_CONTEXT + 'leaderboard',
            },
            payment: {
                create: this.WEB_PAYMENT_ROOT_CONTEXT + "c",
                createRazorPay: this.WEB_PAYMENT_ROOT_CONTEXT + "rc",
                createPaytm: this.WEB_PAYMENT_ROOT_CONTEXT + "pc",
                paymentKey: this.WEB_PAYMENT_ROOT_CONTEXT + "pk",
                validate: this.WEB_PAYMENT_ROOT_CONTEXT + "u",
                cancel: this.WEB_PAYMENT_ROOT_CONTEXT + "cc",
                failure: this.WEB_PAYMENT_ROOT_CONTEXT + "fc"
            },
            paymentExternal: {
                razorPayCheckoutAPI: this.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT + "rco"
            },
            order: {
                searchByWebId: this.WEB_ORDER_ROOT_CONTEXT + "s/w",
                searchByOrderId: this.WEB_ORDER_ROOT_CONTEXT + "s/id",
                searchStatusByOrderId: this.WEB_ORDER_ROOT_CONTEXT + "s/id/s",
                searchCustomerOrders: this.WEB_ORDER_ROOT_CONTEXT + "s/c",
                slackCartId: this.WEB_ORDER_ROOT_CONTEXT + "slk/cid",
                slackWOId: this.WEB_ORDER_ROOT_CONTEXT + "slk/oid",
                isFirstOrder: this.WEB_ORDER_ROOT_CONTEXT + "ifo",
            },
            offer: {
                apply: this.WEB_OFFER_ROOT_CONTEXT + "a",
                getOfferDetails: this.WEB_OFFER_ROOT_CONTEXT + "god",
                signUp: this.WEB_OFFER_ROOT_CONTEXT + "sup",
                getCampaignDetails: this.WEB_OFFER_ROOT_CONTEXT + "campaign-detail"
            },
            webInventory: {
                unit: this.WEB_INVENTORY_ROOT_CONTEXT + "u",
                unitProducts: this.WEB_INVENTORY_ROOT_CONTEXT + "u/p",
            },
            neoMetadata: {
                getRedirectLink: this.NEO_METADATA_ROOT_CONTEXT + "redirect/link/get",
                trackRedirect: this.NEO_METADATA_ROOT_CONTEXT + "redirect/track",
                getUnits: this.NEO_METADATA_ROOT_CONTEXT + "units/get"
            },
            neoExternalPartner: {
                requestSession: this.NEO_EXTERNAL_PARTNER_ROOT_CONTEXT + "r/s",
            }
        };
        this.getUrls = this.getUrls.bind(this);
    }

    getUrls() {
        return this.urls;
    }
}

const apis = new APIs();
export default apis;
