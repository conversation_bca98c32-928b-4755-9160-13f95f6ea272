import * as toolbox from "sw-toolbox";

export default class Sw {

    constructor(){
        const version = 0.1;

        var cacheList = ["vendor","shell","apis","image","css","fonts"];
        var cacheVersion = [];
        cacheVersion["vendor"] = 0.1;
        cacheVersion["shell"] = 0.1;
        cacheVersion["apis"] = 0.1;
        cacheVersion["image"] = 0.1;
        cacheVersion["css"] = 0.1;
        cacheVersion["fonts"] = 0.1;
        var cacheNames = {};

        cacheList.forEach(function (name) {
            cacheNames[name] = name+cacheVersion[name];
        });

        self.addEventListener("install", function(e) {
            e.waitUntil(self.skipWaiting())
        });
        self.addEventListener("activate", function(e) {
            e.waitUntil(self.clients.claim());
            e.waitUntil(caches.keys().then(function (e) {
                var t = Object.keys(cacheNames).map(function (e) {
                    return cacheNames[e];
                });
                return Promise.all(e.map(function (e) {
                    return t.indexOf(e) === -1 && e.indexOf("$$$inactive$$$") === -1 ? caches["delete"](e) : Promise.resolve()
                }))
            }));
        });
        navigator.userAgent.indexOf("Firefox/44.0") > -1 && self.addEventListener("fetch", function(e) {
            e.respondWith(fetch(e.request))
        });

        toolbox.options.debug = true;

        var cacheConfig = {
            origin: "https://" + window.location.host
        };

        toolbox.router.get('/css/*', toolbox.cacheFirst, Object.assign({}, cacheConfig, {
            cache: {
                name: cacheNames.css,
                maxEntries: 5,
            },
            // Set a timeout threshold of 2 seconds
            //networkTimeoutSeconds: 2
        }));

        toolbox.router.default = toolbox.cacheFirst;
    }


}