<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>neo-framework</artifactId>
        <groupId>com.stpl.tech.neo</groupId>
        <version>5.0.15</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>neo-admin</artifactId>
    <packaging>jar</packaging>
    <name>neo-admin</name>
    <url>http://maven.apache.org</url>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
</project>
