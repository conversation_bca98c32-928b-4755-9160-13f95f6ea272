package com.stpl.tech.neo.core.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoRekognitionService;
import com.stpl.tech.neo.core.util.KettleRekognitionClientEndpoints;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.redis.core.util.WebServiceHelper;

import java.util.Optional;

@Service
public class NeoRekognitionServiceImpl implements NeoRekognitionService {

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private CustomerDao customerDao;

	@Override
	public boolean optOutOfFaceItById(String customerId) {
		Optional<Customer> customer = customerDao.findById(customerId);
		return WebServiceHelper.postWithAuth(getOptOutCustomerByIdEndPoint(), props.getNeoClientToken(),
				customer.get().getId() + "", Boolean.class);
	}

	@Override
	public boolean optOutOfFaceItByContact(String contactNumber) {
		return WebServiceHelper.postWithAuth(getOptOutCustomerByContactEndPoint(), props.getNeoClientToken(),
				contactNumber, Boolean.class);
	}

	private String getOptOutCustomerByIdEndPoint() {
		return props.getKettleRekognitionBasePath() + KettleRekognitionClientEndpoints.OPT_OUT_CUSTOMER_BY_ID;
	}

	private String getOptOutCustomerByContactEndPoint() {
		return props.getKettleRekognitionBasePath() + KettleRekognitionClientEndpoints.OPT_OUT_CUSTOMER_BY_CONTACT;
	}

}
