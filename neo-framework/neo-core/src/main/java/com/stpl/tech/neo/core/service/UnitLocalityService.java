package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.redis.domain.model.Unit;

import java.util.List;

public interface UnitLocalityService {

	public List<UnitLocalityMapping> getUnitLocalityMappingData();
	
	public Unit getDeliveryUnit(UnitLocalityMappingVO mapping);
	
	public boolean addUnitLocalityMapping(UnitLocalityMapping mapping);

	public boolean addUnitLocalityMappings(List<UnitLocalityMapping> mapping);

	public boolean removeUnitLocalityMapping(UnitLocalityMapping mapping);

	public boolean updateUnitLocalityMapping(UnitLocalityMapping mapping);

	public boolean updateUnitLocalityMappings(List<UnitLocalityMapping> mappings);

	public boolean removeUnitLocalityMappings(List<UnitLocalityMapping> mappings);

}
