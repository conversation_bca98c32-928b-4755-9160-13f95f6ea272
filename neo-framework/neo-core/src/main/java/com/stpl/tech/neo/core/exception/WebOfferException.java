package com.stpl.tech.neo.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.neo.core.util.WebErrorCodes;

@ResponseStatus(value = HttpStatus.OK, reason = "Offer Redemption Failure")
public class WebOfferException extends Exception {

    private static final long serialVersionUID = 1L;

    private WebErrorCodes code;

    public WebOfferException() {
    }

    public WebOfferException(String message) {
        super(message);
    }

    public WebOfferException(WebErrorCodes code) {
        super(code.getMsg());
        this.code = code;
    }

    public WebOfferException(WebErrorCodes code, String message) {
        super(message);
        this.code = code;
    }

    public WebOfferException(Throwable cause) {
        super(cause);
    }

    public WebOfferException(String message, Throwable cause) {
        super(message, cause);
    }

    public WebOfferException(WebErrorCodes code, Throwable cause) {
        super(code.getMsg(), cause);
        this.code = code;
    }

    public WebOfferException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public WebErrorCodes getCode() {
        return code;
    }

    public void setCode(WebErrorCodes code) {
        this.code = code;
    }

}
