package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.SlackService;
import com.stpl.tech.neo.domain.model.mongo.Address;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartStatus;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.OrderItem;
import com.stpl.tech.neo.domain.model.mongo.Settlement;
import com.stpl.tech.neo.domain.model.mongo.TransactionDetail;
import com.stpl.tech.util.AppConstants;

import java.util.Optional;

@Service
public class SlackServiceImpl implements SlackService {

	@Autowired
	private CartDao cartDao;
	@Autowired
	private CustomerDao customerDao;
	@Autowired
	private EnvironmentProperties env;

	@Override
	public void slackDataByCartId(String cartId) {
		Optional<CartDetail> cart = cartDao.findById(cartId);
		if(cart.isPresent()){
			slackCart(cart.get());
		}
	}

	private void slackCart(CartDetail cart) {

		try {
			/*
			 * PaymentVO paymentVO = new PaymentVO();
			 * paymentVO.setOrderId(webOrderId);
			 * paymentVO.setAmount(cart.getOrderDetail().getTransactionDetail().
			 * getPaidAmount()); //boolean paymentSuccess =
			 * paymentService.getPaymentStatus(paymentVO); boolean
			 * paymentSuccess = true;
			 */
			boolean cartSuccess = CartStatus.SETTLED.equals(cart.getCartStatus());
			if (!cartSuccess) {
				slackOrderDetails(cart);
			} else {
				slackToKettleError(cart);
			}
		} catch (Exception e) {
			slackOrderDetails(cart);
		}
	}

	private void slackToKettleError(CartDetail cart) {
		StringBuilder sb = new StringBuilder();
		sb.append(String.format("*Incorrect Payment Failure Request for cart %s \nNot published to web orders channel",
				cart.getCartId()));
		getCustomerInfo(sb, cart);
		getOrderInfo(sb, cart);
		getTransactionInfo(sb, cart);
		getSettlementInfo(sb, cart);
		SlackNotificationService.getInstance().sendNotification(env.getEnvType(), AppConstants.NEO, SlackNotification.SYSTEM_ERRORS, sb.toString());
	}

	@Override
	public void slackDataByOrderId(String orderId) {
		CartDetail cart = cartDao.searchByExternalOrderId(orderId);
		slackCart(cart);
	}

	private void slackOrderDetails(CartDetail cart) {

		StringBuilder sb = new StringBuilder();
		sb.append(String
				.format("*Order Request Failure!* \n Please contact customer regarding Order Placement and Payments"));
		getCustomerInfo(sb, cart);
		getOrderInfo(sb, cart);
		getTransactionInfo(sb, cart);
		getSettlementInfo(sb, cart);

		SlackNotificationService.getInstance().sendNotification(env.getEnvType(), AppConstants.NEO, SlackNotification.ORDER_FAILURE_INFO,
				sb.toString());

	}

	private void getSettlementInfo(StringBuilder sb, CartDetail cart) {
		sb.append(String.format("\n *Settlement Details*"));
		for (Settlement s : cart.getOrderDetail().getSettlements()) {
			sb.append(String.format("\nPayment Mode %s", s.getMode() == 1 ? "CASH" : "ONLINE"));
			sb.append(String.format("\nPayment Mode Id %s", s.getMode()));
			sb.append(String.format("\nPaid Amount %s", s.getAmount()));
		}
	}

	private void getTransactionInfo(StringBuilder sb, CartDetail cart) {
		sb.append("\n*Transaction Details*");
		TransactionDetail tx = cart.getOrderDetail().getTransactionDetail();
		sb.append(String.format("\nTaxable Amount %s", tx.getTaxableAmount()));
		sb.append(String.format("\nDiscount %s", tx.getDiscountDetail().getTotalDiscount()));
		sb.append(String.format("\nPayable Amount %s", tx.getPaidAmount()));
		sb.append(String.format("\nSavings %s", tx.getSavings()));
		sb.append(String.format("\nTax %s", tx.getTax()));
		sb.append(String.format("\nTaxes %s", tx.getTaxes()));
	}

	private void getOrderInfo(StringBuilder sb, CartDetail cart) {
		sb.append("\n*Order Details*");
		Order o = cart.getOrderDetail();
		sb.append(String.format("\nOrder Type %s", o.getSource()));
		for (OrderItem item : o.getOrders()) {
			addItem(sb, item);
		}
	}

	private void addItem(StringBuilder sb, OrderItem item) {
		sb.append(String.format("\n%s %s   %d", item.getProductName(),
				"none".equals(item.getDimension().toLowerCase()) ? "" : item.getDimension(), item.getQuantity()));
	}

	private void getCustomerInfo(StringBuilder sb, CartDetail cart) {
		String webCustomerId = cart.getCustomerId();
		if (webCustomerId == null) {
			return;
		}
		Optional<Customer> customer = customerDao.findById(webCustomerId);
		Customer c= customer.orElse(null);
		if (c == null) {
			return;
		}
		sb.append("\n*Customer Details*");
		sb.append(String.format("\nName %s", c.getFirstName()));
		sb.append(String.format("\nContact %s", c.getContactNumber()));

		Integer da = cart.getOrderDetail().getDeliveryAddress();
		if (da != null) {
			for (Address a : c.getAddresses()) {
				if (a.getId() == da.intValue()) {
					sb.append(String.format("\nAddress %s, %s, %s, %s, %s", a.getLine1(), a.getLandmark(), a.getCity(),
							a.getState(), a.getCountry()));
				}
			}
		}
	}
}
