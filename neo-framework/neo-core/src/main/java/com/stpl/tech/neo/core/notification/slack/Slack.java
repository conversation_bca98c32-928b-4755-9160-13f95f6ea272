package com.stpl.tech.neo.core.notification.slack;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

public class Slack implements ExternalNotificationService {

	private static final Logger LOG = LoggerFactory.getLogger(Slack.class);
	private static final Slack INSTANCE = new Slack();
	private BlockingQueue<SlackMessage> queue = new DelayQueue<SlackMessage>();

	public static Slack getInstance() {
		return INSTANCE;
	}

	public Slack() {
		ScheduledExecutorService service = Executors.newScheduledThreadPool(1);
		service.scheduleAtFixedRate(new SlackMessageThread(queue), 0, 1, TimeUnit.MINUTES);
	}

	public synchronized SlackMessage addToQueue(SlackMessage data) {
		try {
			if (!queue.contains(data)) {
				queue.put(data);
			}
		} catch (InterruptedException e) {
			LOG.error(String.format("Error while putting Slack message %s for channel %s  to queue", data.getText(),
					data.getChannel()), e);
			return null;
		}
		return data;
	}

	public BlockingQueue<SlackMessage> getQueue() {
		return queue;
	}

	public void send(EnvType env, String user, SlackNotification channel, String text) {
		sendNotification(env, user, null, channel.getChannel(env), text);
	}

	public void send(EnvType env, String text) {
		sendNotification(env, "Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(env), text);
	}

	public void send(EnvType env, String user, SlackNotification channel, Notification notification) {
		send(env, user, channel, notification.getNotificationMessage());
	}

	private void sendNotification(EnvType env, String user, String directUser, String channel, String text) {
		if (text == null) {
			return;
		}
		text = AppUtils.isProd(env) ? text : env.name() + " : " + text;
		addToQueue(new SlackMessage(env, channel, directUser, user, text));
	}

}
