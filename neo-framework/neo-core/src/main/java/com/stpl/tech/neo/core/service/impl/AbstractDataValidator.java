package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.NeoCustomerService;
import com.stpl.tech.neo.core.service.NeoMetadataService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.core.util.WebErrorCodes;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.domain.model.mongo.BillType;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.DiscountDetail;
import com.stpl.tech.neo.domain.model.mongo.IngredientProductDetail;
import com.stpl.tech.neo.domain.model.mongo.IngredientVariantDetail;
import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.OrderItem;
import com.stpl.tech.neo.domain.model.mongo.OrderStatus;
import com.stpl.tech.neo.domain.model.mongo.PercentageDetail;
import com.stpl.tech.neo.domain.model.mongo.TaxDetail;
import com.stpl.tech.neo.domain.model.mongo.TransactionDetail;
import com.stpl.tech.redis.domain.model.AdditionalTax;
import com.stpl.tech.redis.domain.model.RecipeDetail;
import com.stpl.tech.redis.domain.model.ResponseCode;
import com.stpl.tech.redis.domain.model.TaxApplicability;
import com.stpl.tech.redis.domain.model.TaxData;
import com.stpl.tech.redis.domain.model.TaxType;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.redis.domain.model.UnitBusinessHours;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractDataValidator extends AbstractTimeValidator {

	// private static final Logger LOG =
	// LoggerFactory.getLogger(AbstractDataValidator.class);

	@Autowired
	private NeoMetadataService metaService;

	protected ResponseCode getResponseCode(CartDetail cart) {
		ResponseCode rc = null;
		UnitBusinessHours ubs = metaService.getBusinessHours(cart.getOrderDetail().getUnitId());
		if (isValidTime(ubs, cart.getOrderDetail().getSource())) {
			rc = new ResponseCode(1, WebOrderingConstants.CART_SYNC_SUCCESS);
		} else {
			String msg = getbusinessHoursMessage(ubs, NeoUtil.isCOD(cart.getOrderDetail().getSource()));
			rc = new ResponseCode(-1, msg);
		}
		return rc;
	}

	protected TransactionDetail createTransactionalObject() {
		TransactionDetail td = new TransactionDetail();
		td.setDiscountDetail(new DiscountDetail());
		td.getDiscountDetail().setDiscount(new PercentageDetail());
		td.getDiscountDetail().getDiscount().setPercentage(BigDecimal.ZERO);
		td.getDiscountDetail().getDiscount().setValue(BigDecimal.ZERO);
		td.getDiscountDetail().setTotalDiscount(BigDecimal.ZERO);
		td.getDiscountDetail().setPromotionalOffer(BigDecimal.ZERO);
		td.setPaidAmount(BigDecimal.ZERO);
		td.setRoundOffValue(BigDecimal.ZERO);
		td.setSaleAmount(BigDecimal.ZERO);
		td.setSavings(BigDecimal.ZERO);
		td.setTaxableAmount(BigDecimal.ZERO);
		td.setTotalAmount(BigDecimal.ZERO);
		td.setTax(BigDecimal.ZERO);
		return td;
	}

	protected BigDecimal toFixed(BigDecimal value) {
		if (value != null) {
			return value.setScale(4, BigDecimal.ROUND_HALF_UP);
		}
		return BigDecimal.ZERO;
	}

	protected boolean isValidData(BigDecimal value1, BigDecimal value2) {
		return AppUtils.isEqual(value1, value2) || isAcceptableVariance(value1, value2);
	}

	protected boolean isAcceptableVariance(BigDecimal value, BigDecimal value2) {
		// variance should be less then 0.02
		if (value == null) {
			value = BigDecimal.ZERO;
		}
		if (value2 == null) {
			value2 = BigDecimal.ZERO;
		}
		return value.subtract(value2).abs().compareTo(new BigDecimal(0.02)) < 0;
	}

	protected void verifyTransactionalData(CartDetail cart) throws WebOrderException {
		Map<String, TaxData> unitTaxDataMap = createTaxDataMap(metaService.getDeliveryUnit(cart.getOrderDetail().getUnitId()).getTaxes());
		List<OrderItem> cartItems = new ArrayList<>();
		for(OrderItem orderItem : cart.getOrderDetail().getOrders()){
			cartItems.add(createNewCartItem(orderItem));
		}
		BigDecimal originalTax = updateTaxDataToProducts(cartItems, unitTaxDataMap, cart.isInterState());
		TransactionDetail td = createTransactionalObject();
		td = updateTransactionDetailsMetadata(cart.getOrderDetail().getTransactionDetail(), cartItems, td, originalTax);
		if (!isValidTransaction(cart.getOrderDetail().getTransactionDetail(), td)) {
			throwAxe(WebErrorCodes.INVALID_TRANSACTION);
		}
	}

	private OrderItem createNewCartItem(OrderItem orderItem){
		OrderItem orderItem1 = new OrderItem();
		orderItem1.setItemId(orderItem.getItemId());
		orderItem1.setProductId(orderItem.getProductId());
		orderItem1.setItemName(orderItem.getItemName());
		orderItem1.setCustomizationStrategy(orderItem.getCustomizationStrategy());
		orderItem1.setProductName(orderItem.getProductName());
		orderItem1.setProductCategory(orderItem.getProductCategory());
		orderItem1.setQuantity(orderItem.getQuantity());
		orderItem1.setPrice(orderItem.getPrice());
		orderItem1.setTotalAmount(orderItem.getTotalAmount());
		orderItem1.setAmount(orderItem.getAmount());
		orderItem1.setDiscountDetail(orderItem.getDiscountDetail());
		orderItem1.setComplimentaryDetail(orderItem.getComplimentaryDetail());
		orderItem1.getAddons().addAll(orderItem.getAddons());
		orderItem1.setDimension(orderItem.getDimension());
		orderItem1.setBillType(orderItem.getBillType());
		orderItem1.setComposition(orderItem.getComposition());
		orderItem1.setRecipeId(orderItem.getRecipeId());
		orderItem1.setItemCode(orderItem.getItemCode());
		orderItem1.setTax(orderItem.getTax());
		orderItem1.setCode(orderItem.getCode());
		return orderItem1;
	}

	private Map<String, TaxData> createTaxDataMap(List<TaxData> taxDatas){
		Map<String, TaxData> taxDataMap = new HashMap<>();
		for (TaxData data : taxDatas){
			taxDataMap.put(data.getTaxCode(), data);
		}
		return taxDataMap;
	}

	private BigDecimal updateTaxDataToProducts(List<OrderItem> cartItems, Map<String, TaxData> unitTaxData, boolean isInterstate){
		BigDecimal originalTax = BigDecimal.ZERO;
		for (OrderItem item : cartItems){
			if(!item.getBillType().equals(BillType.ZERO_TAX.name())){
				item.getTaxes().clear();
				TaxData td = unitTaxData.get(item.getCode());
				BigDecimal itemTax = BigDecimal.ZERO;
				BigDecimal itemOriginalTax = BigDecimal.ZERO;
				if(isInterstate){
					TaxDetail txd = getTaxDetailObject(TaxType.GST.value(), "IGST", td.getState().getIgst(), item, null);
					itemTax = itemTax.add(txd.getValue());
					item.getTaxes().add(txd);
					itemOriginalTax = itemOriginalTax.add(getOriginalTax(td.getState().getIgst(), item, null));
				}else{
					TaxDetail txd = getTaxDetailObject(TaxType.GST.value(), "CGST", td.getState().getCgst(), item, null);
					itemTax = itemTax.add(txd.getValue());
					item.getTaxes().add(txd);
					itemOriginalTax = itemOriginalTax.add(getOriginalTax(td.getState().getCgst(), item, null));
					txd = getTaxDetailObject(TaxType.GST.value(), "SGST/UTGST", td.getState().getSgst(), item, null);
					itemTax = itemTax.add(txd.getValue());
					item.getTaxes().add(txd);
					itemOriginalTax = itemOriginalTax.add(getOriginalTax(td.getState().getSgst(), item, null));
				}
				BigDecimal gstTax = itemTax;
				BigDecimal originalGstTax = itemOriginalTax;
				for (AdditionalTax additionalTax:td.getOthers()) {
					BigDecimal amount, originalAmount;
					if(TaxApplicability.ON_SALE.equals(additionalTax.getApplicability())){
						amount = null;
						originalAmount = null;
					}else{
						amount = gstTax;
						originalAmount = originalGstTax;
					}
					TaxDetail txd = getTaxDetailObject(additionalTax.getType(), additionalTax.getType(),
						additionalTax.getTax(), item, amount);
					itemTax = itemTax.add(txd.getValue());
					item.getTaxes().add(txd);
					itemOriginalTax = itemOriginalTax.add(getOriginalTax(additionalTax.getTax(), item, originalAmount));
				}
				item.setTax(itemTax.setScale(2, BigDecimal.ROUND_HALF_UP));
				originalTax = originalTax.add(itemOriginalTax);
			}
		}
		return originalTax;
	}

	private TaxDetail getTaxDetailObject(String type, String code, BigDecimal percentage, OrderItem item, BigDecimal amount){
		TaxDetail taxDetail = new TaxDetail();
		taxDetail.setType(type);
		taxDetail.setCode(code);
		taxDetail.setPercentage(percentage);
		taxDetail.setTotal(item.getPrice().multiply(new BigDecimal(item.getQuantity())));
		taxDetail.setTaxable(item.getPrice().multiply(new BigDecimal(item.getQuantity())).subtract(item.getDiscountDetail().getTotalDiscount()));
		if(amount==null){
			taxDetail.setValue(taxDetail.getPercentage().divide(new BigDecimal(100)).multiply(taxDetail.getTaxable()));
		}else{
			taxDetail.setValue(taxDetail.getPercentage().divide(new BigDecimal(100)).multiply(amount));
		}
		return taxDetail;
	}

	private BigDecimal getOriginalTax(BigDecimal percentage, OrderItem item, BigDecimal amount){
		BigDecimal taxableAmount;
		if(amount==null){
			taxableAmount = item.getPrice().multiply(new BigDecimal(item.getQuantity()));
		}else{
			taxableAmount = amount;
		}
		return percentage.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP).multiply(taxableAmount);
	}

	/**
	 * Replicated from AppUtil.js for tax calculation in front end
	 * 
	 * WARNING if there are changes in transaction calculation on front end then
	 * this will throw error. Please keep both functions in sync
	 * 
	 * @param transactionDetail
	 * @param cartItems
	 * @param td
	 * @throws WebOrderException
	 */
	private TransactionDetail updateTransactionDetailsMetadata(TransactionDetail transactionDetail, List<OrderItem> cartItems, TransactionDetail td, BigDecimal originalTax) throws WebOrderException {
		if (transactionDetail == null) {
			throwAxe(WebErrorCodes.INVALID_TRANSACTION);
		}
		//TransactionDetail tx = cart.getOrderDetail().getTransactionDetail();
		// taxes
		//List<TaxData> unitTaxData = metaService.getDeliveryUnit(cart.getOrderDetail().getUnitId()).getTaxes();
		BigDecimal promotionalDiscount = BigDecimal.ZERO;
		BigDecimal discountValue = BigDecimal.ZERO;
		BigDecimal totalDiscount = BigDecimal.ZERO;
		BigDecimal nonDiscountedPayable = BigDecimal.ZERO;
		for (OrderItem item : cartItems) {
			td.setTotalAmount(td.getTotalAmount().add(item.getPrice().multiply(new BigDecimal(item.getQuantity()))));
			td.setTaxableAmount(td.getTaxableAmount().add(item.getAmount()));
			td.setTax(td.getTax().add(item.getTax()));
			for (TaxDetail atax:item.getTaxes()){
				boolean found = false;
				for (TaxDetail tdx : td.getTaxes()){
					if(tdx.getCode().equals(atax.getCode()) && tdx.getPercentage().equals(atax.getPercentage())){
						tdx.setTaxable(tdx.getTaxable().add(atax.getTaxable()));
						tdx.setTotal(tdx.getTotal().add(atax.getTotal()));
						tdx.setValue(tdx.getValue().add(atax.getValue()));
						found = true;
					}
				}
				if(!found){
					td.getTaxes().add(atax);
				}
			}
			promotionalDiscount = promotionalDiscount.add(item.getDiscountDetail().getPromotionalOffer());
			discountValue = discountValue.add(item.getDiscountDetail().getDiscount().getPercentage().divide(new BigDecimal(100)).multiply(item.getAmount()));
			totalDiscount = totalDiscount.add(item.getDiscountDetail().getTotalDiscount());
			nonDiscountedPayable = nonDiscountedPayable.add(item.getTotalAmount());
		}
		nonDiscountedPayable = nonDiscountedPayable.add(originalTax);
		td.getDiscountDetail().setPromotionalOffer(promotionalDiscount);
		td.getDiscountDetail().getDiscount().setValue(discountValue);
		td.getDiscountDetail().setTotalDiscount(totalDiscount);
		BigDecimal paidAmount = td.getTaxableAmount().add(td.getTax());
		td.setPaidAmount(paidAmount.setScale(0, BigDecimal.ROUND_HALF_UP));
		td.setRoundOffValue(td.getPaidAmount().subtract(paidAmount));
		BigDecimal saving = nonDiscountedPayable.subtract(td.getPaidAmount()).setScale(0, RoundingMode.HALF_UP);
		td.setSavings(saving.compareTo(BigDecimal.ZERO)>0?saving:BigDecimal.ZERO);
		return td;
	}

	private boolean isValidTransaction(TransactionDetail td1, TransactionDetail td2) throws WebOrderException {
		StringBuilder sb = new StringBuilder();
		boolean response = true;
		if (!isValidData(td1.getPaidAmount(), td2.getPaidAmount())) {
			sb.append("\nPaid Amount" + td1.getPaidAmount() + "/" + td2.getPaidAmount());
			response = false;
		}
		if (!isValidData(td1.getRoundOffValue(), td2.getRoundOffValue())
				&& !isAcceptableVariance(td1.getRoundOffValue(), td2.getRoundOffValue())) {
			sb.append("\nRound Off Value" + td1.getRoundOffValue() + "/" + td2.getRoundOffValue());
			response = false;
		}
		//TODO add savings checks in transactional data
		if (!isValidData(td1.getSavings(), td2.getSavings())) {
			sb.append("\nGet Savings" + td1.getSavings() + "/" + td2.getSavings());
			response = false;
		}
		if (!isValidData(td1.getTaxableAmount(), td2.getTaxableAmount())) {
			sb.append("\nTaxable Amount" + td1.getTaxableAmount() + "/" + td2.getTaxableAmount());
			response = false;
		}
		if (!isValidData(td1.getTotalAmount(), td2.getTotalAmount())) {
			sb.append("\nTotal Amount" + td1.getTotalAmount() + "/" + td2.getTotalAmount());
			response = false;
		}
		if (!isValidData(td1.getTax(), td2.getTax())) {
			sb.append("\nTax" + td1.getTax() + "/" + td2.getTax());
			response = false;
		}
		if (!isValidData(td1.getDiscountDetail().getPromotionalOffer(),
				td1.getDiscountDetail().getPromotionalOffer())) {
			sb.append("\nPromotional Offer" + td1.getDiscountDetail().getPromotionalOffer() + "/"
					+ td2.getDiscountDetail().getPromotionalOffer());
			response = false;
		}
		if (!isValidData(td1.getDiscountDetail().getTotalDiscount(), td1.getDiscountDetail().getTotalDiscount())) {
			sb.append("\nTotal Discount" + td1.getDiscountDetail().getTotalDiscount() + "/"
					+ td2.getDiscountDetail().getTotalDiscount());
			response = false;
		}
		if (!isValidData(td1.getDiscountDetail().getDiscount().getValue(),
				td1.getDiscountDetail().getDiscount().getValue())) {
			sb.append("\nDiscount" + td1.getDiscountDetail().getTotalDiscount() + "/"
					+ td2.getDiscountDetail().getTotalDiscount());
			response = false;
		}
		for (TaxDetail txd1 : td1.getTaxes()){
			for (TaxDetail txd2 : td2.getTaxes()){
				if(txd1.getCode().equals(txd2.getCode()) && txd1.getPercentage().equals(txd2.getPercentage())){
					if(!isValidData(txd1.getValue(), txd2.getValue())){
						sb.append("\nTax " + txd1.getCode() + " " + td1.getTax() + "/" + td2.getTax());
						response = false;
					}
				}
			}
		}
		if (sb.length() > 0) {
			throwAxe("ERROR in data transactional comparison: " + sb.toString());
		}
		return response;
	}

	protected void updateOrderMetadata(CartDetail cart) {
		String serviceName = null;
		if(cart.getOrderDetail().getExternalOrderId() != null) {
			serviceName = cart.getOrderDetail().getExternalOrderId().substring(0, 2)
					.equalsIgnoreCase(WebOrderingConstants.KIOSK_ORDER) ? WebOrderingConstants.KIOSK_SERVICE : WebOrderingConstants.NEO_SERVICE;
		} else {
			serviceName = WebOrderingConstants.NEO_SERVICE;
		}
		updateOrderMetadata(cart.getOrderDetail(), cart.getCartId(), serviceName);
	}

	protected void updateOrderMetadata(Order order, String cartId, String serviceName) {
		if (order != null) {
			if (NeoUtil.isCOD(order.getSource())) {
				order.setSource(AppConstants.COD);
				order.setDeliveryPartner(AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY);
				order.setTerminalId(AppConstants.ZERO);
			} else if (NeoUtil.isCafe(order.getSource())) {
				order.setSource(AppConstants.CAFE);
				order.setDeliveryPartner(AppConstants.DELIVERY_PARTNER_NONE);
				order.setDeliveryAddress(null);
				order.setTerminalId(AppConstants.ONE);
			} else {
				order.setSource(AppConstants.TAKE_AWAY);
				order.setDeliveryPartner(AppConstants.DELIVERY_PARTNER_PICKUP);
				order.setDeliveryAddress(null);
				order.setTerminalId(AppConstants.TAKEAWAY_SEED + AppConstants.ONE);
			}
			if(serviceName.equals(WebOrderingConstants.KIOSK_SERVICE)) {
				order.setSourceId(WebOrderingConstants.KIOSK + AppConstants.HYPHEN + cartId);
			} else {
				order.setSourceId(AppConstants.NEO + AppConstants.HYPHEN + cartId);
			}
			order.setGenerateOrderId(cartId);
			order.setStatus(OrderStatus.CREATED);
			order.setBillStartTime(NeoUtil.getCurrentTimestamp());
			order.setBillCreationSeconds(AppConstants.ZERO);
			order.setBillCreationTime(NeoUtil.getCurrentTimestamp());
			order.setBillingServerTime(NeoUtil.getCurrentTimestamp());
			order.setEmployeeId(AppConstants.SYSTEM_EMPLOYEE_ID);
			if(order.getOrders().get(0).getProductId() == 1000064){
				order.setChannelPartner(AppConstants.BAZAAR_PARTNER_ID);
			}else{
				order.setChannelPartner(AppConstants.CHANNEL_PARTNER_WEB_APP);
			}
		}
	}

	// need to fix the flow for the cart
	protected void fillCriticalData(Order newOrder, Order oldOrder, boolean isCashOnDelivery) {
		// update any values regarding Web Order here
		if(StringUtils.isNotBlank(oldOrder.getExternalOrderId())){
			newOrder.setExternalOrderId(oldOrder.getExternalOrderId());
		}
		// Order Item level data addition here
		if (isCashOnDelivery) {
			newOrder.setExternalOrderId(null);
		}
	}

	protected void verifyOrder(CartDetail cart, boolean isSyncTask, boolean isPaymentRequest) throws WebOrderException {
		primaryChecks(cart);
		if (!isSyncTask) {
			verifyCartItems(cart);
			if (!isPaymentRequest) {
				verifySettlemetData(cart);
			}
			verifyCriticalData(cart);
		}
		verifyProductPrices(cart);
		verifyOfferData(cart);
		verifyTransactionalData(cart);
	}

	private void primaryChecks(CartDetail cart) throws WebOrderException {
		if (cart.getOrderDetail().getUnitId() == 0) {
			throwAxe(WebErrorCodes.INVALID_UNIT);
		}
		if (cart.getOrderDetail().getSettlementType() == null || cart.getOrderDetail().getSettlements() == null) {
			throwAxe(WebErrorCodes.INVALID_SETTLEMENT);
		}
		if (!isValidSource(cart.getOrderDetail().getSource())) {
			throwAxe(WebErrorCodes.INVALID_SOURCE);
		}
		if (cart.getOrderDetail().getOrderId() != null
				|| cart.getOrderDetail().getGenerateOrderId() != null
				|| OrderStatus.SETTLED.equals(cart.getOrderDetail().getStatus())
				|| OrderStatus.DELIVERED.equals(cart.getOrderDetail().getStatus())) {
			throwAxe(WebErrorCodes.SETTLED_CART);
		}
	}

	private boolean isValidSource(String source) {
		if (source == null) {
			return false;
		}
		return NeoUtil.isCOD(source) || NeoUtil.isCafe(source) || NeoUtil.isTakeAway(source);
	}

	private void verifyCartItems(CartDetail cart) throws WebOrderException {
		if (cart.getOrderDetail().getOrders() == null || cart.getOrderDetail().getOrders().isEmpty()) {
			throwAxe(WebErrorCodes.INVALID_CART, "Zero Cart Items");
		}
		// check for packaging and delivery charges
		boolean containsZeroMenuItems = true;
		for (OrderItem item : cart.getOrderDetail().getOrders()) {
			if (!AppConstants.HIDDEN_PRODUCTS_SET.contains(item.getProductId())) {
				containsZeroMenuItems = false;
				break;
			}
		}
		if (containsZeroMenuItems) {
			throwAxe(WebErrorCodes.INVALID_CART, "No items other than packaging and delivery charges");
		}
	}

	private void verifySettlemetData(CartDetail cart) throws WebOrderException {
		// TODO how to verify settlement data ?

		if (cart.getOrderDetail().getSettlements().size() != 1) {
			throwAxe(WebErrorCodes.INVALID_SETTLEMENT, " More then 1 settlement found");
		}
	}

	private void verifyOfferData(CartDetail cart) {
		// TODO Auto-generated method stub

	}

	/**
	 * Add here anything and everything that is critical for creation of order
	 * 
	 * @param cart
	 * @throws WebOrderException
	 */
	private void verifyCriticalData(CartDetail cart) throws WebOrderException {
		if (cart.getCustomerId() == null || !cart.getCustomerId().equals(cart.getOrderDetail().getWebCustomerId())) {
			throwAxe(WebErrorCodes.INVALID_CUSTOMER, "Error in Web Customer Id");
		}
		if (cart.getOrderDetail().getCustomerId() == null || cart.getOrderDetail().getCustomerName() == null) {
			throwAxe(WebErrorCodes.INVALID_CUSTOMER, "Error in Order level Customer Id and Customer Name");
		}
		if (NeoUtil.isCOD(cart.getOrderDetail().getSource()) && cart.getOrderDetail().getDeliveryAddress() == null) {
			throwAxe(WebErrorCodes.INVALID_ADDRESS, "Error in Delivery Address Id");
		}
		if (cart.getOrderDetail().getOrderId() != null || cart.getOrderDetail().getGenerateOrderId() != null
				|| OrderStatus.SETTLED.equals(cart.getOrderDetail().getStatus())
				|| OrderStatus.DELIVERED.equals(cart.getOrderDetail().getStatus())) {
			throwAxe(WebErrorCodes.SETTLED_CART);
		}
	}

	private void verifyProductPrices(CartDetail cart) throws WebOrderException {
		if (NeoUtil.isCOD(cart.getOrderDetail().getSource())) {
			// DELIVERY
			checkDeliveryPrices(cart);
		} else {
			// TAKE AWAY || CAFE
			checkTakeAwayPrices(cart);
		}
	}

	private void checkTakeAwayPrices(CartDetail cart) throws WebOrderException {
		// key unitId:ProductId:dimension
		try {
			BigDecimal expectedPrice = null;
			int unitId = cart.getOrderDetail().getUnitId();
			for (OrderItem item : cart.getOrderDetail().getOrders()) {
				if(item.getProductId() != 1043) {
					expectedPrice = metaService.getTakeAwayUnitProductPrice(unitId, item.getProductId(),
							item.getDimension());
					if (!NeoUtil.checkEqual(expectedPrice, item.getPrice())) {
						throwAxe("Incorrect Price for " + item.getProductName());
					}
				}
			}
		} catch (NeoMetadataException nme) {
			throwAxe(nme.getMessage(), nme);
		}
	}

	private void checkDeliveryPrices(CartDetail cart) throws WebOrderException {
		// key region:ProductId:dimension
		BigDecimal expectedPrice = null;
		UnitBasicDetail unit = metaService.getUnitBasicDetail(cart.getOrderDetail().getUnitId());
		String region = unit.getRegion();
		for (OrderItem item : cart.getOrderDetail().getOrders()) {
			if (item.getProductId() != 1043) {
				expectedPrice = metaService.getDeliveryUnitProductPrice(region, item.getProductId(), item.getDimension());
				if (!NeoUtil.checkEqual(expectedPrice, item.getPrice())) {
					throwAxe("Incorrect Price for " + item.getProductName());
				}
			}
		}
	}

	protected void updateOrderItemRecipeDefaults(CartDetail cart) throws WebOrderException {
		try {
			for (OrderItem item : cart.getOrderDetail().getOrders()) {
				RecipeDetail recipe = metaService.getProductRecipe(item.getProductId(), item.getDimension(), cart.getOrderDetail().getUnitId());
				if (recipe != null) {
					recipe.getIngredient().getProducts().forEach(p -> {
						if (!p.isCustomize()) {
							p.getDetails().forEach(d -> {
								if (d.isDefaultSetting()) {
									item.getComposition().getProducts()
											.add(NeoUtil.clone(d, IngredientProductDetail.class));
								}
							});
						}
					});
					recipe.getIngredient().getVariants().forEach(v -> {
						if (!v.isCustomize()) {
							v.getDetails().forEach(d -> {
								if (d.isDefaultSetting()) {
									item.getComposition().getVariants()
											.add(NeoUtil.clone(d, IngredientVariantDetail.class));
								}
							});
						}
					});
				}
			}
		} catch (URISyntaxException e) {
			throwAxe("Error while updating default recipe details:" + e.getMessage());
		}
	}

	protected void updateCustomerIdAndName(CartDetail cart) {
		if (cart.getCustomerId() != null) {
			Customer customer = getCustomerService().getCustomer(cart.getCustomerId());
			if (customer != null) {
				cart.getOrderDetail().setCustomerId(customer.getId());
				cart.getOrderDetail().setCustomerName(customer.getFirstName());
			}
		}
	}

	protected abstract NeoCustomerService getCustomerService();

	/**
	 * Local exception thrower this can be used to format and design exception
	 * reporting
	 * 
	 * @param string
	 * @throws WebOrderException
	 */
	protected void throwAxe(String string) throws WebOrderException {
		throw new WebOrderException(string);
	}

	protected void throwAxe(WebErrorCodes code) throws WebOrderException {
		throw new WebOrderException(code);
	}

	protected void throwAxe(WebErrorCodes code, String string) throws WebOrderException {
		throw new WebOrderException(code, string);
	}

	/**
	 * Local exception thrower this can be used to format and design exception
	 * reporting
	 * 
	 * @param string
	 * @throws WebOrderException
	 */
	protected void throwAxe(String string, Exception e) throws WebOrderException {
		throw new WebOrderException(string, e);
	}

	protected void throwAxe(WebErrorCodes code, Exception e) throws WebOrderException {
		throw new WebOrderException(code, e);
	}

	public static void main(String[] args) {
		String test = "WO213847587465385";
		System.out.println(test.substring(0,2));
	}
}
