package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.neo.core.cache.NeoMetadataCache;
import com.stpl.tech.neo.core.dao.OfferDetailDao;
import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.core.notification.slack.Slack;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoMetadataCacheManagementService;
import com.stpl.tech.neo.core.service.NeoMetadataService;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.redis.domain.model.*;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class NeoMetadataServiceImpl extends AbstractTimeValidator implements NeoMetadataService {

    private static final Logger LOG = LoggerFactory.getLogger(NeoMetadataServiceImpl.class);

    @Autowired
    private NeoMetadataCache neoCache;

    @Autowired
    private NeoMetadataCacheManagementService neoCacheManger;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private OfferDetailDao offerDetailDao;

    @Override
    public Map<String, Integer> getCities() {
        if (neoCache.getCityToStateMapping().isEmpty()) {
            try {
                neoCacheManger.loadLocationCache();
            } catch (Exception e) {
                LOG.error("Error in reloading cache to redis from master server", e);
            }
        }
        return neoCache.getCityToStateMapping();
    }

    @Override
    public List<String> getLocalitiesForCity(String city) {
        return neoCache.getLocalitiesForCity(city);
    }

    @Override
    public UnitDataVO getDeliveryUnit(UnitLocalityMappingVO mapping) throws NeoMetadataException {
        if (mapping.getLocality() == null || mapping.getLocality().trim().equals("")) {
            return getByCity(mapping);
        } else {
            return getByCityAndLocality(mapping);
        }
    }

    private UnitDataVO getByCityAndLocality(UnitLocalityMappingVO mapping) throws NeoMetadataException {
        UnitLocalityMapping m = neoCache.getUnitLocalityMapping(mapping);
        UnitDataVO vo = null;
        if (m == null) {
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Neo",null, SlackNotification.SYSTEM_ERRORS.getChannel(props.getEnvType()),"Invalid Mapping: " + JSONSerializer.toJSON(mapping));
            return vo;
        }
        try {
            vo = selectUnit(m, "CAFE");
        } catch (Exception e) {
            LOG.error("Error while getting delivery unit", e);
            throw new NeoMetadataException(e.getMessage());
        }
        return vo;
    }

    private UnitDataVO getByCity(UnitLocalityMappingVO mapping) throws NeoMetadataException {
        String locality = neoCache.getLocalityForCity(mapping.getCity());
        mapping.setLocality(locality);
        return getByCityAndLocality(mapping);
    }

    private UnitDataVO selectUnit(UnitLocalityMapping m, String orderSource) {
        UnitDataVO vo = null;
        if (m.getPrimaryUnitId() > 0) {
            vo = getOpenUnit(m.getPrimaryUnitId(), 1, m.isSkipPrimaryDeliveryCharge(), m.isSkipPrimaryPackagingCharge(),
                orderSource);
        }
        if (vo == null && m.getSecondaryUnitId() > 0) {
            vo = getOpenUnit(m.getSecondaryUnitId(), 2, m.isSkipSecondaryDeliveryCharge(),
                m.isSkipSecondaryPackagingCharge(), orderSource);
        }
        if (vo == null && m.getTertiaryUnitId() > 0) {
            vo = getOpenUnit(m.getTertiaryUnitId(), 3, m.isSkipTertiaryDeliveryCharge(),
                m.isSkipPrimaryPackagingCharge(), orderSource);
        }
        if (vo == null) {
            vo = createUnitVO(getBusinessHours(m.getPrimaryUnitId()), m.getPrimaryUnitId(), true, 1,
                m.isSkipPrimaryDeliveryCharge(), m.isSkipPrimaryPackagingCharge());
        }
        return vo;
    }

    private UnitDataVO getOpenUnit(int unitId, int type, boolean skipDeliveryCharge, boolean skipPackagingCharge,
                                   String orderSource) {
        UnitBusinessHours ubh = getBusinessHours(unitId);
        if (isValidTime(ubh, orderSource)) {
            return createUnitVO(ubh, unitId, true, type, skipDeliveryCharge, skipPackagingCharge);
        }
        return null;
    }

    private UnitDataVO createUnitVO(UnitBusinessHours ubh, int unitId, boolean isDelivery, int type,
                                    boolean skipDeliveryCharge, boolean skipPackagingCharge) {
        UnitDataVO vo = new UnitDataVO();
        vo.setType(type);
        String msg = null;
        int dayOfWeek = AppUtils.getDayOfWeek(AppUtils.getCurrentTimestamp());
        for (UnitHours uhrs : ubh.getOperationalHours()) {
            if (uhrs.getDayOfTheWeek() != null && uhrs.getDayOfTheWeek().trim() != ""
                && uhrs.getDayOfTheWeekNumber() == dayOfWeek) {
                if (isDelivery) {
                    vo.setOpeningTime(uhrs.getDeliveryOpeningTime());
                    vo.setClosingTime(uhrs.getDeliveryClosingTime());
                } else {
                    vo.setOpeningTime(uhrs.getTakeAwayOpeningTime());
                    vo.setClosingTime(uhrs.getTakeAwayClosingTime());
                }
                try {
                    msg = "Order can placed between " + AppUtils.msgTimeFormat(vo.getOpeningTime()) + " and "
                        + AppUtils.msgTimeFormat(vo.getClosingTime()) + " only.";
                } catch (Exception e) {
                    LOG.error("Error While parsing date for error msg", e);
                    msg = null;
                }
                vo.setMessage(msg);
                if (isDelivery) {
                    vo.setOperational(uhrs.isHasDelivery() && uhrs.isIsOperational());
                } else {
                    vo.setOperational(uhrs.isHasTakeAway() && uhrs.isIsOperational());
                }
            }
        }
        vo.setSkipDeliveryCharge(skipDeliveryCharge);
        vo.setSkipPackagingCharge(skipPackagingCharge);
        vo.setUnit(getDeliveryUnit(unitId));
        return vo;
    }

    @Override
    public List<ProductRecipes> getProductRecipes(List<ProductRecipeKey> products) throws URISyntaxException {
        Map<Integer, List<ProductRecipeKey>> productsMap = new HashMap<>();
        products.forEach(productRecipeKey -> {
            if (!productsMap.containsKey(productRecipeKey.getProductId())) {
                productsMap.put(productRecipeKey.getProductId(), new ArrayList<>());
            }
            productsMap.get(productRecipeKey.getProductId()).add(productRecipeKey);
        });
        List<ProductRecipes> productRecipes = new ArrayList<>();
        for (Map.Entry<Integer, List<ProductRecipeKey>> entry : productsMap.entrySet()) {
            ProductRecipes recipes = getProductRecipe(entry.getKey());
            ProductRecipes productRecipe = new ProductRecipes();
            productRecipe.setProductId(entry.getKey());
            productRecipe.setRecipes(new ArrayList<>());
            if (recipes != null) {
                recipes.getRecipes().forEach(recipeDetail ->
                    entry.getValue().forEach(productRecipeKey -> {
                        if (productRecipeKey.getProfile().equalsIgnoreCase(recipeDetail.getProfile())
                            && productRecipeKey.getDimension().equalsIgnoreCase(recipeDetail.getDimension().getCode())) {
                            productRecipe.getRecipes().add(recipeDetail);
                        }
                    })
                );
            }
            productRecipes.add(productRecipe);
        }
        return productRecipes;
    }

    @Override
    public RecipeDetail getProductRecipe(int pId, String dimension, Integer unitId) throws URISyntaxException {
        ProductRecipes pr = getProductRecipe(pId);
        if (pr != null) {
            String profile = neoCache.getProductPriceProfilesMap().get(unitId).get(pId).stream()
                .filter(productRecipeKey -> productRecipeKey.getDimension().equalsIgnoreCase(dimension))
                .collect(Collectors.toList()).get(0).getProfile();
            List<RecipeDetail> recipes = pr.getRecipes().stream().filter(recipeDetail ->
                recipeDetail.getDimension().getCode().equalsIgnoreCase(dimension) && recipeDetail.getProfile().equalsIgnoreCase(profile)
            ).collect(Collectors.toList());
            if (!recipes.isEmpty()) {
                return recipes.get(0);
            }
        }
        return null;
    }

    private ProductRecipes getProductRecipe(int pId) throws URISyntaxException {
        ProductRecipes pr = neoCache.getRecipe(pId);
        if (pr == null) {
            neoCacheManger.updateRecipeInCache(pId);
            pr = neoCache.getRecipe(pId);
        }
        return pr;
    }

    @Override
    public List<IdName> getTakeAwayUnits(UnitLocalityMappingVO mapping) {
        return neoCache.getTakeAwayUnits(mapping);
    }

    @Override
    public List<IdName> getWebCategories() {
        if (neoCache.getWebCategories().isEmpty()) {
            neoCacheManger.updateWebCategories();
        }
        return neoCache.getWebCategories();
    }

    @Override
    public Unit getDeliveryUnit(int unitId) {
        Unit u = neoCache.getDeliveryUnit(unitId);
        if (u == null) {
            u = neoCacheManger.getUnitDataFromRedis(unitId);
        }
        return u;
    }

    @Override
    public List<IdName> getTakeAwayUnitsForCity(String city) throws URISyntaxException {
        List<IdName> l = neoCache.getTakeAwayUnitsForCity(city);
        if (l == null || l.isEmpty()) {
            neoCacheManger.updateUnitBasicDetails();
            l = neoCache.getTakeAwayUnitsForCity(city);
        }
        return l;
    }

    @Override
    public Set<String> getAllTags() {
        return neoCache.getAllTags();
    }

    @Override
    public List<String> getTagData(String tagName) {
        return neoCache.getTagData(tagName);
    }

    @Override
    public BigDecimal getTakeAwayUnitProductPrice(int unitId, int productId, String dimension)
        throws NeoMetadataException {
        BigDecimal price = neoCache.getTakeAwayUnitProductPrice(unitId, productId, dimension);
        if (price == null) {
            neoCacheManger.updateTakeAwayProductPrices(unitId);
            price = neoCache.getTakeAwayUnitProductPrice(unitId, productId, dimension);
        }
        return price;
    }

    @Override
    public BigDecimal getDeliveryUnitProductPrice(String region, int productId, String dimension) {
        BigDecimal price = neoCache.getDeliveryUnitProductPrice(region, productId, dimension);
        if (price == null) {
            neoCacheManger.updateDeliveryProductPrices(region);
            price = neoCache.getDeliveryUnitProductPrice(region, productId, dimension);
        }
        return price;
    }

    @Override
    public UnitBasicDetail getUnitBasicDetail(int unitId) {
        UnitBasicDetail ubd = neoCache.getUnitBasicDetail(unitId);
        if (ubd == null && unitId > 0) {
            ubd = neoCacheManger.getUnitBasicDetail(unitId);
        }
        return ubd;
    }

    @Override
    public void reloadCache() throws URISyntaxException {
        neoCacheManger.reloadCache();
    }

    @Override
    public void reloadRecipeCache() throws URISyntaxException {
        neoCacheManger.reloadRecipeCache();
    }

    @Override
    public UnitLocalityMapping getUnitLocalityMapping(String city, String locality) {
        return neoCache.getUnitLocalityMapping(new UnitLocalityMappingVO(city, locality));
    }

    @Override
    public Unit getUnit(int unitId) {
        return neoCacheManger.getUnit(unitId);
    }

    @Override
    public UnitBusinessHours getBusinessHours(int unitId) {
        if (unitId > 0) {
            return neoCacheManger.getBusinessHours(unitId);
        }
        return null;
    }

    @Override
    public Map<String, List<String>> getProductTagData() {
        return neoCache.getProductTagData();
    }

    @Override
    public Map<String, List<String>> getMenuTagData() {
        return neoCache.getMenuTagData();
    }

    @Override
    public String getDefaultLocalityForCity(String city) {
        return neoCache.getLocalityForCity(city);
    }

    @Override
    public Map<Integer, String> getProductImages(List<Integer> productIds) throws URISyntaxException {
        if(neoCache.getProductImages() == null || neoCache.getProductImages().isEmpty()) {
            neoCacheManger.loadProductImages();
        }
        return neoCache.getProductImages().entrySet().stream().filter(integerStringEntry -> {
            return productIds.contains(integerStringEntry.getKey());
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
