package com.stpl.tech.neo.core.service.impl;

import java.net.URISyntaxException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import com.stpl.tech.neo.domain.model.mongo.GameLeaderBoardResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.cache.NeoLoginTokenCache;
import com.stpl.tech.neo.core.cache.NeoMetadataCache;
import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.neo.core.dao.CustomerSignupDataDao;
import com.stpl.tech.neo.core.dao.DeviceDao;
import com.stpl.tech.neo.core.dao.SessionDao;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.BlankCartService;
import com.stpl.tech.neo.core.service.CRMService;
import com.stpl.tech.neo.core.service.CustomerProductService;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoCustomerService;
import com.stpl.tech.neo.core.service.NeoKettleOfferService;
import com.stpl.tech.neo.core.service.NeoMetadataService;
import com.stpl.tech.neo.core.service.StampingService;
import com.stpl.tech.neo.core.util.AppEmailNotification;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.core.util.WebErrorCodes;
import com.stpl.tech.neo.domain.model.CreateNextOfferRequest;
import com.stpl.tech.neo.domain.model.CustomerCampaignJourney;
import com.stpl.tech.neo.domain.model.CustomerCardInfo;
import com.stpl.tech.neo.domain.model.CustomerInfoResponse;
import com.stpl.tech.neo.domain.model.CustomerLoginRequest;
import com.stpl.tech.neo.domain.model.EmailTemplate;
import com.stpl.tech.neo.domain.model.LoginToken;
import com.stpl.tech.neo.domain.model.MyOfferResponse;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.mongo.Address;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartStatus;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.CustomerAddress;
import com.stpl.tech.neo.domain.model.mongo.CustomerCurrentAddresses;
import com.stpl.tech.neo.domain.model.mongo.CustomerData;
import com.stpl.tech.neo.domain.model.mongo.CustomerInfo;
import com.stpl.tech.neo.domain.model.mongo.CustomerProductConsumption;
import com.stpl.tech.neo.domain.model.mongo.CustomerSignupData;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.neo.domain.model.mongo.ProductConsumption;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.neo.domain.model.mongo.SessionDetail;
import com.stpl.tech.neo.domain.model.mongo.SessionStatus;
import com.stpl.tech.neo.domain.model.mongo.SignUpTimeSlotCount;
import com.stpl.tech.neo.domain.model.mongo.SpecialOfferRequest;
import com.stpl.tech.neo.domain.model.mongo.SpecialOfferResponse;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.IdCodeName;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.redis.domain.model.ProductBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;

import java.util.Objects;

@Service
public class NeoCustomerServiceImpl implements NeoCustomerService {

	private static final Logger LOG = LoggerFactory.getLogger(NeoCustomerServiceImpl.class);

	private static final SecureRandom secureRandom = new SecureRandom(); //threadsafe
	private static final Base64.Encoder base64Encoder = Base64.getUrlEncoder(); //threadsafe

	@Autowired
	private EnvironmentProperties env;

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private CRMService crmService;

	@Autowired
	private DeviceDao deviceDao;

	@Autowired
	private CartDao cartDao;

	@Autowired
	private SessionDao sessionDao;

	@Autowired
	private StampingService stampService;

	@Autowired
	private NeoMetadataService metaService;

	@Autowired
	private BlankCartService blankCartService;

	@Autowired
	private CustomerProductService customerProductService;

	@Autowired
	private CustomerSignupDataDao customerSignupDataDao;

	@Autowired
	private NeoMetadataCache neoMetadataCache;

	@Autowired
	private NeoLoginTokenCache tokenCache;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private NeoKettleOfferService offerService;

	@Value("${report.service.rest.url}")
    private String reportServiceBaseURL;

    @Value("${report.service.auth.internal}")
    private String reportServiceAuth;


	/**
	 * lookup customer before login and send OTP for login validation if customer
	 * does not exist then searches in kettle
	 *
	 * @param contact
	 * @return true if customer exists
	 */
	@Override
	public int lookupAndSendOTP(String contact, boolean sendOtp) {
		Customer c = customerDao.findByContactNumber(contact);
		boolean otpSent=false;
		if (c != null) {
			if (c.getAcquisitionBrandId() == null) {
				c.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
			}
			if (c.isInternal()) {
				return -2;
			}
			if (sendOtp) {
				otpSent=crmService.sendOTP(contact);
			}
			return getCode(c);
		} else {
			// search customer in kettle
			try {
				c = crmService.lookup(contact);
			} catch (Exception e) {
				LOG.info("Error message: " + e.getMessage());
				if (e.getMessage()
						.equalsIgnoreCase("Customer With Contact Number " + contact + " is an Internal Employee")) {
					return -2;
				}
				throw e;
			}
			if (c != null) {
				c = customerDao.save(c);
				if (sendOtp) {
					otpSent=crmService.sendOTP(contact);
				}
				return getCode(c);
			} else {
				otpSent=crmService.sendOTP(contact);
			}
		}
		if(!otpSent){
			return -5;
		}
		return -1;
	}

	@Override
	public int lookup(String contact) {
		int returnValue = lookupAndSendOTP(contact, false);
		if (returnValue != 1 && returnValue != 3) {
			crmService.sendOTP(contact);
		}
		return returnValue;
	}

	@Override
	public int oldCustomerLookUp(String contact) {
		if (crmService.oldCustomerLookUp(contact)) {
			return -1;
		} else {
			return 1;
		}
	}

	@Override
	public IdCodeName getCustomerUniqueCoupon(CustomerData data) {
		if (data != null) {
			return crmService.getCustomerUniqueCoupon(data.getContact());
		}
		return null;
	}

	@Override
	public CustomerSignupData findCustomerSignupData(String contact) {
		CustomerSignupData signupData = customerSignupDataDao.findByContactNumber(contact);
		signupData.setOfferAvailed(true);
		customerSignupDataDao.save(signupData);
		return signupData;
	}

	private Customer syncAddresses(Customer c) {
		try {
			List<Integer> addresses = new ArrayList<>();
			c.getAddresses().forEach(p -> addresses.add(p.getId()));
			List<Address> newAddresses = crmService
					.getNewCustomerAddress(new CustomerCurrentAddresses(c.getId(), addresses));
			if (newAddresses != null && newAddresses.size() > 0) {
				c.getAddresses().addAll(newAddresses);
				return customerDao.save(c);
			}
		} catch (Exception e) {
			NeoUtil.slackIt(env.getEnvType(), e);
		}
		return c;
	}

	private int getCode(Customer c) {
		// there can always be a better way to do this
		int code = 1;
		if (c.isContactNumberVerified()) {
			if (c.getFirstName() == null || c.getFirstName().trim().length() == 0) {
				code = 2;
			}
			if (c.getEmailId() == null || c.getEmailId().trim().length() == 0) {
				code = 3;
			}
			if ((c.getFirstName() == null || c.getFirstName().trim().length() == 0)
					&& (c.getEmailId() == null || c.getEmailId().trim().length() == 0)) {
				code = 4;
			}
		} else {
			code = 5;
			if (c.getFirstName() == null || c.getFirstName().trim().length() == 0) {
				code = 6;
			}
			if (c.getEmailId() == null || c.getEmailId().trim().length() == 0) {
				code = 7;
			}
			if ((c.getFirstName() == null || c.getFirstName().trim().length() == 0)
					&& (c.getEmailId() == null || c.getEmailId().trim().length() == 0)) {
				code = 8;
			}
		}
		return code;
	}

	/**
	 * validates login call for the customer, searches customer creates a session
	 * and merges current device cart to customers cart
	 *
	 * @param request data+OTP
	 * @return null on failure
	 */
	@Override
	public CustomerInfo login(CustomerData request) {
		if (crmService.verifyOTP(request.getContact(), request.getOtp())) {
			return loginUser(request);
		}
		return null;
	}

	@Override
	public CustomerLoginRequest loginByKey(String key) throws WebOrderException {
		LoginToken token = tokenCache.getTokenData(key);
		if (token != null) {
			return token.getData();
		} else {
			throw new WebOrderException(WebErrorCodes.INVALID_CUSTOMER);
		}
	}

	/**
	 * validates devicekey and searches customer details by contact number and
	 * returns customer detail only if contact number id verified
	 *
	 * @param request data
	 * @return null on failure
	 */
	@Override
	public CustomerInfo getCustomerInfo(CustomerData request) throws WebOrderException {
		// verify if device id is valid
		// update cart to device
		DeviceVO device = new DeviceVO(request.getDeviceKey(), null, null);
		boolean validDevice = stampService.verifyDevice(device);
		if (validDevice) {
			Customer customer = customerDao.findByContactNumber(request.getContact());
			// get device
			String deviceId = NeoUtil.getDeviceIdFromDeviceKey(request.getDeviceKey());
			// create session
			SessionDetail session = createSession(customer.getCustomerId(), customer.getContactNumber(), deviceId);
			// update session to device
			DeviceDetail deviceDetail = updateSessionInDeviceDetail(deviceId, session.getSessionId());
			if (customer.isContactNumberVerified()) {
				CustomerInfo cf = new CustomerInfo();
				cf.setName(customer.getFirstName());
				cf.setContact(customer.getContactNumber());
				cf.setEmail(customer.getEmailId());
				cf.setOptOutOfFaceIt(customer.isOptOutOfFaceIt());
				cf.setOptOutTime(customer.getOptOutTime());
//				cf.setLoyalty(customer.getLoyaltyPoints());
//				cf.setChaayosCash(customer.getChaayosCash());
				cf.setRefCode(customer.getRefCode());
				cf.setCustomerId(customer.getId());
				CartDetail cartDetail = new CartDetail();
				cartDetail.setDeviceId(deviceId);
				cartDetail.setSessionId(session.getSessionId());
				cartDetail.setCustomerId(session.getCustomerId());
				cf.setDevice(new DeviceVO(deviceDetail.getDeviceKey(), session.getSessionKey(), cartDetail));
				return cf;
			} else {
				throw new WebOrderException("Customer is not verified.");
			}
		}
		throw new WebOrderException("Device is not valid.");
	}

	/**
	 * logout for customer. removes session and customer from device details
	 *
	 * @param request
	 * @return new Device key
	 */
	@Override
	public DeviceVO logout(CustomerData request) {

		String deviceId = NeoUtil.getDeviceIdFromDeviceKey(request.getDeviceKey());
		String sessionId = NeoUtil.getSessionIdFromSessionKey(request.getSessionKey());
		DeviceVO vo = null;
		if (sessionId != null && deviceId != null) {
			Optional<SessionDetail> session = sessionDao.findById(sessionId);
			if (session.isPresent()) {
				// Expire session here
				session.get().setStatus(SessionStatus.LOGOUT);
				sessionDao.save(session.get());
			}
			// update device details
			Optional<DeviceDetail> device = deviceDao.findById(deviceId);
			if (device.isPresent()) {
				device.get().setCustomerId(null);
				device.get().setLastCartId(null);
				device.get().setLastSessionId(null);
				DeviceDetail deviceDetail = deviceDao.save(device.get());
				CartDetail cart = blankCartService.createBlankCart(null, deviceDetail.getDeviceId(), null);
				vo = new DeviceVO(deviceDetail.getDeviceKey(), null, cart);
			}
		}
		return vo;
	}

	/**
	 * sign-up call for new customers, validates OTP and then creates customer in
	 * web ordering and kettle CRM
	 *
	 * @param request
	 * @return
	 */
	@Override
	public CustomerInfo signUp(CustomerData request, boolean loginNeo) {
		// create customer and then call login functionality
		if (crmService.verifyOTP(request.getContact(), request.getOtp())) {
			return signUpVerified(request, loginNeo);
			/*
			 * Customer c = crmService.signup(request.getContact(), request.getName(),
			 * request.getEmail(), request.getTcId()); if (c != null) { Customer customer =
			 * customerDao.findByContactNumber(request.getContact()); if(customer!=null){
			 * c.setCustomerId(customer.getCustomerId()); } c = customerDao.save(c); return
			 * loginUser(request); }
			 */
		}
		return null;
	}

	/**
	 * sign-up call for new customers, validates OTP and then creates customer in
	 * web ordering and kettle CRM
	 *
	 * @param request
	 * @return
	 */
	@Override
	public CustomerInfo signUpVerified(CustomerData request, boolean loginNeo) {
		// create customer and then call login functionality
		// Customer c = crmService.signup(request.getContact(), request.getName(),
		// request.getEmail(), request.getTcId());
		// added this new call to accommodate acquisition details
		Customer c = crmService.signup(request);
		if (c != null) {
			Customer customer = customerDao.findByContactNumber(request.getContact());
			if (customer != null) {
				c.setCustomerId(customer.getCustomerId());
				if (customer.getAcquisitionBrandId() == null) {
					c.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
				}
			}
			c = customerDao.save(c);
			String response = WebServiceHelper.postWithAuth(reportServiceBaseURL
					+ "clevertap/clevertap-push/push-customer-profile?customerId=" + customer.getId(),
					reportServiceAuth, null, String.class);
			LOG.info("Sending customer data to clevertap {}", response);
			if (loginNeo) {
				return loginUser(request);
			} else {
				CustomerInfo cf = new CustomerInfo();
				if (c.getFirstName() != null) {
					cf.setName(c.getFirstName());
				}
				if (c.getEmailId() != null) {
					cf.setEmail(c.getEmailId());
				}
				if (c.getCustomerId() != null) {
					cf.setCustomerId(c.getId());
				}
				cf.setContact(c.getContactNumber());
				return cf;
			}
		}
		return null;
	}

	@Override
	public CustomerInfo signInPartnerCustomer(Customer c, DeviceDetail device) {
		if (c != null) {
			Customer customer = customerDao.findByContactNumber(c.getContactNumber());
			if (customer == null) {
				c = customerDao.save(c);
				String response = WebServiceHelper.postWithAuth(reportServiceBaseURL
						+ "clevertap/clevertap-push/push-customer-profile?customerId=" + customer.getCustomerId(),
						reportServiceAuth, null, String.class);
				LOG.info("Sending customer data to clevertap {}", response);
			}
			CustomerData cd = new CustomerData();
			cd.setContact(c.getContactNumber());
			cd.setEmail(cd.getEmail());
			cd.setName(cd.getName());
			cd.setDeviceKey(device.getDeviceKey());
			// TODO check if we need extra info here
			return loginUser(cd);
		}
		return null;
	}

	@Override
	public List<Address> getCustomerAddresses(String customerId) {
		Optional<Customer> c = customerDao.findById(customerId);
		if (c.isPresent()) {
			Customer customer = syncAddresses(c.get());
			customer.getAddresses().forEach(p -> updateUnit(p));
			return customer.getAddresses();
		}
		return null;
	}

	private void updateUnit(Address p) {
		UnitLocalityMapping locality = metaService.getUnitLocalityMapping(p.getCity(), p.getLocality());
		if (locality != null) {
			p.setUnitId(locality.getPrimaryUnitId());
			p.getUnits().add(locality.getPrimaryUnitId());
			if (locality.getSecondaryUnitId() > 0) {
				p.getUnits().add(locality.getSecondaryUnitId());
			}
			if (locality.getTertiaryUnitId() > 0) {
				p.getUnits().add(locality.getTertiaryUnitId());
			}
		}
	}

	private DeviceDetail updateSessionInDeviceDetail(String deviceId, String sessionId) {
		Optional<DeviceDetail> device = deviceDao.findById(deviceId);
		device.get().setLastSessionId(sessionId);
		return deviceDao.save(device.get());
	}

	public SessionDetail createSession(String customerId, String contactNumber, String deviceId) {
		SessionDetail session = new SessionDetail();
		session.setCustomerId(customerId);
		session.setContactNumber(contactNumber);
		session.setDeviceId(deviceId);
		session.setCreatedAt(NeoUtil.getCurrentTimestamp());
		session.setLastAccessedAt(NeoUtil.getCurrentTimestamp());
		session.setStatus(SessionStatus.ACTIVE);
		return sessionDao.save(session);
	}

	private CustomerInfo loginUser(CustomerData request) {
		Customer customer = customerDao.findByContactNumber(request.getContact());
		if (request.isUpdate()) {
			customer = updateCustomerData(request, customer);
		}
		// get device
		String deviceId = NeoUtil.getDeviceIdFromDeviceKey(request.getDeviceKey());
		// create session
		SessionDetail session = createSession(customer.getCustomerId(), customer.getContactNumber(), deviceId);
		// update session to device
		DeviceDetail device = updateSessionInDeviceDetail(deviceId, session.getSessionId());
		// Expected behavior:
		// customer logs in -
		// if device cart has items remove customer cart details
		// give customer the device cart i.e that is currently on device
		// else continue with his cart

		Optional<CartDetail> customerCart = null;
		if (customer.getCartId() != null) {
			// get cart which belongs to user
			customerCart = cartDao.findById(customer.getCartId());
		}

		Optional<CartDetail> deviceCart = null;
		if (device.getLastCartId() != null) {
			deviceCart = cartDao.findById(device.getLastCartId());
		}

		// merge carts
		// mergeDeviceCartToCustomerCart(customerCart, deviceCart);

		if (Objects.nonNull(deviceCart) &&  deviceCart.isPresent()) {
			if (deviceCart.get().getOrderDetail() !=	 null && deviceCart.get().getOrderDetail().getOrders() != null
					&& !deviceCart.get().getOrderDetail().getOrders().isEmpty()) {
				// cart change only when device cart is not empty
				// Invalidate current cart(or remove it)
				if (Objects.nonNull(customerCart) && customerCart.isPresent()) {
					customerCart.get().setCartStatus(CartStatus.INVALID);
					cartDao.save(customerCart.get());
				}

				// now set customer cart as device cart
				customerCart = deviceCart;
			}
		}

		// in case device cart was null and customer cart is also null
		CartDetail cartDetail = new CartDetail();
		if (Objects.isNull(customerCart) || !customerCart.isPresent()) {
			customerCart = Optional.of(blankCartService.createBlankCart(customer.getCustomerId(), device.getDeviceId(),
					session.getSessionId()));
		}

		// set correct data to cart
		setCartDetails(customerCart.get(), customer.getCustomerId(), device.getDeviceId(), session.getSessionId());

		// update cart to device
		stampService.updateCartInDevice(customerCart.get());

		// update cart to customer
		customer.setCartId(customerCart.get().getCartId());
		customer = customerDao.save(customer);

		// return Device VO with updated cart
		CustomerInfo cf = new CustomerInfo();
		cf.setName(customer.getFirstName());
		cf.setContact(customer.getContactNumber());
		cf.setEmail(customer.getEmailId());
		cf.setLoyalityPoints(customer.getLoyaltyPoints());
		cf.setRefCode(customer.getRefCode());
		cf.setChaayosCash(customer.getChaayosCash());
		cf.setCustomerId(customer.getId());
		cf.setDevice(new DeviceVO(device.getDeviceKey(), session.getSessionKey(), customerCart.get()));
		return cf;
	}

	private Customer updateCustomerData(CustomerData cd, Customer c) {
		String name = getName(cd, c);
		String email = getEmail(cd, c);
		updateCRM(cd.getContact(), name, email);
		return updateWeb(c, name, email);
	}

	private String getName(CustomerData cd, Customer c) {
		return cd.getName() != null && cd.getName().trim().length() > 0 ? cd.getName() : c.getFirstName();
	}

	private String getEmail(CustomerData cd, Customer c) {
		return cd.getEmail() != null && cd.getEmail().trim().length() > 0 ? cd.getEmail() : c.getEmailId();
	}

	private Customer updateWeb(Customer customer, String name, String email) {
		customer.setFirstName(name);
		customer.setEmailId(email);
		return customerDao.save(customer);
	}

	private void updateCRM(String contact, String name, String email) {
		crmService.update(contact, name, email);
	}

	private void setCartDetails(CartDetail cart, String customerId, String deviceId, String sessionId) {
		cart.setCustomerId(customerId);
		cart.setDeviceId(deviceId);
		cart.setSessionId(sessionId);
		cart.setCartStatus(CartStatus.CREATED);
		if (cart.getOrderDetail() != null) {
			cart.getOrderDetail().setWebCustomerId(customerId);
		}
		cartDao.save(cart);
	}

	@Override
	public Address addCustomerAddress(CustomerAddress request) {
		if (request.getId() != null) {
			Optional<Customer> customer = customerDao.findById(request.getId());
			if (customer.isPresent()) {
				request.setContact(customer.get().getContactNumber());
				Address a = request.getAddress();
				UnitLocalityMapping map = metaService.getUnitLocalityMapping(a.getCity(), a.getLocality());
				a.setState(map.getState());
				a.setCountry(map.getCountry());
				Address address = crmService.addCustomerAddress(request);
				if (address != null) {
					// add to customer
					customer.get().getAddresses().add(address);
					customerDao.save(customer.get());
					// add to cart
					if (request.getCartId() != null) {
						Optional<CartDetail> cart = cartDao.findById(request.getCartId());
						if (cart.isPresent() && cart.get().getOrderDetail() != null) {
							cart.get().getOrderDetail().setDeliveryAddress(address.getId());
							cartDao.save(cart.get());
						}
					}
				}
				return address;
			}
		}
		return null;
	}

	@Override
	public void changeCart(CartDetail cart) {
		if (cart.getCustomerId() != null) {
			Optional<Customer> customer = customerDao.findById(cart.getCustomerId());
			// CartDetail newCart = createBlankCart(cart.getCustomerId(),
			// cart.getDeviceId(), cart.getSessionId());
			// customer.setCartId(newCart.getCartId());
			if (customer.isPresent()) {
				customer.get().setCartId(null);
				customerDao.save(customer.get());
			}
		}
	}

	@Override
	public void attachCartToCustomer(CartDetail cart) {
		if (cart.getCustomerId() != null) {
			Optional<Customer> customer = customerDao.findById(cart.getCustomerId());
			if (customer.isPresent()) {
				customer.get().setCartId(cart.getCartId());
				customerDao.save(customer.get());
			}
		}
	}

	@Override
	public Customer getCustomer(String customerId) {
		if (customerId != null) {
			return customerDao.findById(customerId).orElse(null);
		}
		return null;
	}

	@Override
	public OTPResponse resendOTP(String contact) {
		return crmService.resendOTP(contact);
	}

	@Override
	public boolean generateOtp(String contact) {
		return crmService.sendOTP(contact);
	}

	@Override
	public CustomerInfo getCustomerLoyaltyScore(String customerId) {
		if (customerId == null) {
			return null;
		}
		Optional<Customer> customer = customerDao.findById(customerId);
		if (customer.isPresent()) {
			Customer c = crmService.lookup(customer.get().getContactNumber());
			customer.get().setLoyaltyPoints(c.getLoyaltyPoints());
			customer.get().setChaayosCash(c.getChaayosCash());
			customer.get().setFirstName(c.getFirstName());
			customer.get().setEmailId(c.getEmailId());
			customer.get().setRefCode(c.getRefCode());
			customer.get().setOptOutOfFaceIt(c.isOptOutOfFaceIt());
			customer.get().setOptOutTime(c.getOptOutTime());
			customerDao.save(customer.get());
			return new CustomerInfo(customer.get().getFirstName(), customer.get().getEmailId(),
					customer.get().getLoyaltyPoints(), customer.get().getChaayosCash(), customer.get().getRefCode(),
					customer.get().isOptOutOfFaceIt(), customer.get().isEligibleForSignupOffer(), false);
		}
		return null;
	}

	@Override
	public boolean updateAddressToCart(IdName request) {
		if (request != null && request.getName() != null && request.getId() > 0) {
			Optional<CartDetail> cart = cartDao.findById(request.getName());
			if (cart.isPresent()) {
				cart.get().getOrderDetail().setDeliveryAddress(request.getId());
				cartDao.save(cart.get());
				return true;
			}
		}
		return false;
	}

	@Override
	public Set<String> getRecentProducts(String customerId) {
		Set<String> s = new HashSet<String>();
		Map<Integer, ProductBasicDetail> productBasicDetails = neoMetadataCache.getProductBasicDetailMap();
		CustomerProductConsumption c = customerProductService.getCustomerProductConsumption(customerId);
		if (c != null) {
			for (ProductConsumption p : c.getCurrentProducts()) {
				ProductBasicDetail productBasicDetail = productBasicDetails.get(Integer.valueOf(p.getProductId()));
				// Skipping gift card products in brought to you by
				if (productBasicDetail != null && "ACTIVE".equals(productBasicDetail.getStatus())
						&& !p.getProductName().toLowerCase().contains("gift card")) {
					s.add(p.getProductId());
				}
			}
		}
		return s;
	}

	@Override
	public Integer submitNPS(Object formEvent) {
		return crmService.submitNPS(formEvent);
	}

	@Override
	public boolean verifyOTP(CustomerData request) {
		if (crmService.verifyOTP(request.getContact(), request.getOtp())) {
			return true;
		}
		return false;
	}

	@Override
	public Object getGiftCard(int customerId) throws Exception {
		LOG.info("Get gift card of customer");
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_GIFT_CARD;
		String token = env.getNeoClientToken();
		Map<String, Integer> map = new HashMap<String, Integer>();
		map.put("customerId", customerId);
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.GET, Object.class, null, map);
	}

	@Override
	public CustomerInfo getLoyality(CustomerInfoResponse customerInfo) {
		CustomerInfo cInfo = new CustomerInfo();
		try {
			LOG.info("Get Loyality and chaayos cash of customer");
			CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
			String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_LOYALITY;
			String token = env.getNeoClientToken();
			customerInfoResponse = WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST,
					CustomerInfoResponse.class, JSONSerializer.toJSON(customerInfo), null);
			cInfo.setLoyalityPoints(customerInfoResponse.getLoyalityPoints());
			cInfo.setChaayosCash(customerInfoResponse.getChaayosCash());
			cInfo.setOptOutOfFaceIt(customerInfoResponse.isOptOutOfFaceIt());
			cInfo.setOptOutTime(customerInfoResponse.getOptOutTime());
			cInfo.setEligibleForSignupOffer(customerInfoResponse.isEligibleForSignupOffer());
		} catch (Exception e) {
			LOG.error("Exception occured while getting loyalty points ", e);
		}
		return cInfo;
	}

	@Override
	public CustomerCardInfo getCustomerCardInfo(String customerId) {
		LOG.info("Get gift card info of customer  " + customerId);
		CustomerCardInfo response = null;
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("customerId", customerId);
		try {
			response = WebServiceHelper.exchangeWithAuth(getCustomerCardInfoEndPoint(), props.getNeoClientToken(),
					HttpMethod.GET, CustomerCardInfo.class, null, uriVariables);
		} catch (URISyntaxException e) {
			LOG.error("Failed to fetch customer card info for customer {}", customerId, e);
		}
		return response;
	}

	@Override
	public Boolean sendSMS(SMSRequest input) {
		return crmService.sendSMS(input);
	}

	@Override
	public boolean sendEmail(EmailTemplate emailTemplate) {
		try {
			AppEmailNotification notification = new AppEmailNotification(emailTemplate.getTemplate(), env.getEnvType(),
					emailTemplate.getCustomerEmail(), emailTemplate.getCustomerName());
			notification.sendEmail();
		} catch (EmailGenerationException e) {
			return false;
		}
		return true;
	}

	@Override
	public CustomerSignupData addCustomerNumber(String contact) {
		CustomerSignupData data = customerSignupDataDao.findByContactNumber(contact);
		if (data == null) {
			CustomerSignupData signupData = new CustomerSignupData();
			signupData.setContactNumber(contact);
			signupData.setOfferAvailed(false);
			customerSignupDataDao.save(signupData);
			return signupData;
		}
		return data;
	}

	private String getCustomerCardInfoEndPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_AVAILABLE_CARD_AMOUNT;
	}

    private String getMyOfferEndPoint(){
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_MY_OFFER;
    }

    private String getSaveJourneyEndpoint(){
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.SAVE_CUSTOMER_JOURNEY;
    }

    private String getSpecialOfferEndpoint(){
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_SPECIAL_OFFER;
    }

	private String getGamifiedOfferEndpoint(){
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_GAMIFIED_OFFER;
	}

	private String getLeaderBoardEndpoint(){
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_LEADER_BOARD;
	}


    @Override
    public CustomerInfo signUpForOffer(CustomerData request) {
        try{
            if (crmService.verifyOTP(request.getContact(), request.getOtp())) {
                CustomerInfo customerInfo = signUpVerified(request, false);
                if(customerInfo!=null){
                    boolean isOldCustomer = crmService.oldCustomerLookUp(customerInfo.getContact());
                    boolean hasOrdersForCafe = crmService.hasOrdersForCafe(customerInfo.getContact());
                    boolean hasOrdersForDelivery = crmService.hasOrdersForDelivery(customerInfo.getContact());
                    boolean hasAvailed = offerService.checkIfSignUpOfferAvailedViaKettle(customerInfo.getContact());
                    customerInfo.setSignUpOfferAvailed(hasAvailed);
                    customerInfo.setOldCustomer(isOldCustomer);
                    customerInfo.setCafeOrdersExist(hasOrdersForCafe);
                    customerInfo.setDeliveryOrdersExist(hasOrdersForDelivery);
                    if(!hasAvailed &&
                        ((!hasOrdersForCafe && !hasOrdersForDelivery) || (!hasOrdersForCafe && hasOrdersForDelivery))){
                        IdCodeName codeInfo = crmService.getCustomerUniqueCoupon(customerInfo.getContact());
                        if(codeInfo!=null && !AppUtils.isBlank(codeInfo.getCode())){
                            customerInfo.setOfferCode(codeInfo.getCode());

							CustomerInfoResponse info = new CustomerInfoResponse();
							info.setContact(customerInfo.getContact());
							info.setName(customerInfo.getName());
							info.setSignUpRefCode(codeInfo.getCode());
							boolean couponSent = crmService.sendSignUpCoupon(info);
							LOG.info("Coupon SMS sent : " + couponSent);
						}
					}
					LOG.info("Customer " + customerInfo.getName() + " customer contact: " + customerInfo.getContact()
							+ " hasAvailed: " + hasAvailed + " isOldCustomer: " + isOldCustomer + " hasOrdersForCafe: "
							+ hasOrdersForCafe + " hasOrdersForDelivery: " + hasOrdersForDelivery + " offerCode: "
							+ customerInfo.getOfferCode());
					return customerInfo;
				}
			}
		} catch (Exception ex) {
			LOG.error("Exception occurred while signUpForOffer " + ex);
			return null;
		}
		return null;
	}

	@Override
	public SignUpTimeSlotCount getTimeSlotCounts(String dateOfDelivery) {
		try {
			return offerService.getTimeSlotCounts(dateOfDelivery);
		} catch (URISyntaxException ex) {
			LOG.error("Exception occurred while getTimeSlotCounts " + ex);
			return null;
		}
	}

    @Override
    public List<MyOfferResponse> getMyOffer(CustomerData data) {
        try{
			boolean isOtpVerified = crmService.verifyOTP(data.getContact(), data.getOtp());
			boolean isTrueCallerVerified = crmService.verifyTruecallerLogin(data.getTruecallerRequestId(), data.getContact());
            if (isOtpVerified || isTrueCallerVerified) {
                CustomerInfo customerInfo = signUpVerified(data, false);
                CreateNextOfferRequest createNextOfferRequest = new CreateNextOfferRequest();
                createNextOfferRequest.setCustomerId(customerInfo.getCustomerId());
                createNextOfferRequest.setContactNumber(customerInfo.getContact());
                createNextOfferRequest.setCampaignId(data.getCampaignId());
                createNextOfferRequest.setCampaignToken(data.getCampaignToken());
                createNextOfferRequest.setBrandId(1);
                createNextOfferRequest.setUtmMedium(data.getUtmMedium());
                createNextOfferRequest.setUtmSource(data.getUtmSource());
				createNextOfferRequest.setAuthToken(data.getAuthToken());
                LOG.info("Fetching my offer from neo with request data {}",JSONSerializer.toJSON(createNextOfferRequest));
                List<MyOfferResponse> myOfferResponses = Arrays.asList(WebServiceHelper.postWithAuth(getMyOfferEndPoint(), props.getNeoClientToken(),createNextOfferRequest,MyOfferResponse[].class));
                for (MyOfferResponse response : myOfferResponses){
                    LOG.info("******************************* offer  ****************************");
                    LOG.info("Receive my offer from neo as data {}", JSONSerializer.toJSON(response));
                    LOG.info("*******************************************************************");
                }
                return myOfferResponses;
            }else{
				return Arrays.asList(new MyOfferResponse("OTP_VERIFICATION_FAILURE"));
			}
        }catch (Exception ex){
            LOG.error("Exception occurred while getting my offer "+ ex);
            return null;
        }
    }

    @Override
    public CustomerCampaignJourney saveCustomerJourney(CustomerCampaignJourney data) {
        String endpoint = getSaveJourneyEndpoint();
        LOG.info("Endpoint to save customer journey :: {}",endpoint);
        CustomerCampaignJourney journey = WebServiceHelper.postWithAuth(endpoint, props.getNeoClientToken(),data,CustomerCampaignJourney.class);
        LOG.info("Received saved journey detail :: {}", journey);
        return journey;
    }

    @Override
    public Customer getCustomerInfoByContact(String contact){
        if(Objects.nonNull(contact) && contact.length()>0){
            return crmService.getCustomerByContact(contact);
        }
        return null;
    }

    @Override
    public List<SpecialOfferResponse> getSpecialOffer(CustomerData request) {
        SpecialOfferRequest offerRequest = new SpecialOfferRequest();
        LOG.info("EMAIL #################### {}",request.getEmail());
        offerRequest.setContactNumber(request.getContact());
        offerRequest.setAuthToken(request.getAuthToken());
        offerRequest.setCampaignToken(request.getCampaignToken());
        offerRequest.setUtmSource(request.getUtmSource());
        offerRequest.setUtmMedium(request.getUtmMedium());
        offerRequest.setEmail(request.getCustomerEmail());
        offerRequest.setFlow(request.getFlow());
        offerRequest.setWhatsappOpt(request.getWhatsappOpt());
		offerRequest.setGameScore(request.getGameScore());
		LOG.info("Game Request {}",JSONSerializer.toJSON(request));
        List<SpecialOfferResponse> response = Arrays.asList(WebServiceHelper.postWithAuth(getGamifiedOfferEndpoint(), props.getNeoClientToken(), offerRequest, SpecialOfferResponse[].class));
        if (!response.isEmpty()) {
			tokenCache.removeGameLoginToken(request.getSessionKey());
            LOG.info("NEO-SPECIAL-OFFER ::: found offer : {}", JSONSerializer.toJSON(response));
        } else {
            LOG.info("NEO-SPECIAL-OFFER ::: no offer found : ");
        }
        return response;
    }

	private String generateNewToken() {
		byte[] randomBytes = new byte[24];
		secureRandom.nextBytes(randomBytes);
		return base64Encoder.encodeToString(randomBytes);
	}

    @Override
    public List<SpecialOfferResponse> registerAndOffer(CustomerData request) {
        try {
			boolean isOtpVerified = crmService.verifyOTP(request.getContact(), request.getOtp());
            if (isOtpVerified) {
				String sessionToken = generateNewToken();
				if(Objects.isNull(request.getSessionToken())){
					tokenCache.setGameLoginToken(sessionToken);
					request.setSessionKey(sessionToken);
				}
                CustomerInfo customerInfo = signUpVerified(request, false);
				if(!request.isCreateOffer()){
					List<SpecialOfferResponse> offerResponses = new ArrayList<>();
					offerResponses.add(new SpecialOfferResponse("OFFER_NOT_CREATED",sessionToken));
					return offerResponses;
				}
                return getSpecialOffer(request);
            }else {
				List<SpecialOfferResponse> offerResponses = new ArrayList<>();
				offerResponses.add(new SpecialOfferResponse("OTP_FAILED",null));
				return offerResponses;
			}
        } catch (Exception ex) {
            LOG.error("Exception occurred while getting special offer ", ex);
            return null;
        }
    }

	@Override
	public GameLeaderBoardResponse getLeaderBoard(String contact, String token, Boolean getRank, CustomerData request) {
		try{
			Map<String,String> uriVariable = new HashMap<>();
			uriVariable.put("contact",contact);
			uriVariable.put("token",token);
			uriVariable.put("getRank",String.valueOf(getRank));
			GameLeaderBoardResponse leaderBoardResponses = WebServiceHelper.exchangeWithAuth(getLeaderBoardEndpoint(),
					props.getNeoClientToken(), HttpMethod.GET, GameLeaderBoardResponse.class, null,uriVariable);
			return leaderBoardResponses;
		}catch (Exception e){
			LOG.error("error while fetching leader board for contact : {}",contact,e);
		}
		return null;
	}
}
