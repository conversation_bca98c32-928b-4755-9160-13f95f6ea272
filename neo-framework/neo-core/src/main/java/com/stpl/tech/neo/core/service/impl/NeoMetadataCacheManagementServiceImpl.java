package com.stpl.tech.neo.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.neo.core.cache.NeoMetadataCache;
import com.stpl.tech.neo.core.dao.RedirectTrackingDao;
import com.stpl.tech.neo.core.dao.UnitDetailDao;
import com.stpl.tech.neo.core.dao.UnitLinkMappingDao;
import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoMetadataCacheManagementService;
import com.stpl.tech.neo.core.service.UnitLocalityService;
import com.stpl.tech.neo.core.service.WebTagService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.mongo.RedirectTracking;
import com.stpl.tech.neo.domain.model.mongo.UnitDetail;
import com.stpl.tech.neo.domain.model.mongo.UnitLinkMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.neo.domain.model.mongo.WebTag;
import com.stpl.tech.neo.domain.model.mongo.WebTagCategory;
import com.stpl.tech.redis.core.service.RedisCacheService;
import com.stpl.tech.redis.domain.model.DeliveryUnit;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.redis.domain.model.IngredientDetail;
import com.stpl.tech.redis.domain.model.IngredientProduct;
import com.stpl.tech.redis.domain.model.IngredientProductDetail;
import com.stpl.tech.redis.domain.model.IngredientVariant;
import com.stpl.tech.redis.domain.model.Location;
import com.stpl.tech.redis.domain.model.Product;
import com.stpl.tech.redis.domain.model.ProductBasicDetail;
import com.stpl.tech.redis.domain.model.ProductPrice;
import com.stpl.tech.redis.domain.model.ProductRecipes;
import com.stpl.tech.redis.domain.model.ProductUnit;
import com.stpl.tech.redis.domain.model.RecipeDetail;
import com.stpl.tech.redis.domain.model.Unit;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.redis.domain.model.UnitBusinessHours;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class NeoMetadataCacheManagementServiceImpl implements NeoMetadataCacheManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(NeoMetadataCacheManagementServiceImpl.class);

    @Autowired
    private NeoMetadataCache neoCache;

    @Autowired
    private RedisCacheService redisService;

    @Autowired
    private UnitLocalityService localityService;

    @Autowired
    private WebTagService webTagService;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private UnitLinkMappingDao unitLinkMappingDao;

    @Autowired
    private RedirectTrackingDao redirectTrackingDao;

    @Autowired
    private UnitDetailDao unitDetailDao;

    @Override
    public List<UnitLocalityMapping> getUnitLocalityMappingData() {
        return localityService.getUnitLocalityMappingData();
    }

    @PostConstruct
    public void loadNeoData() throws URISyntaxException {
        updateLocalityData();
        updateTagData();
        loadProductCache();
        loadProductPriceProfiles();
    }

    @Override
    public void clearCache() throws URISyntaxException {
        neoCache.clearAll();
        loadNeoData();
    }

    @Override
    public void clearWebTagCache() {
        updateTagData();
    }

    @Override
    public void reloadCache() throws URISyntaxException {
        redisService.loadAll();
        clearCache();
        List<ProductRecipes> recipes = redisService.getAllProductRecipes();
        if (recipes != null) {
            loadProductRecipes(recipes);
        }
        List<UnitBasicDetail> ubds = redisService.getAllUnitBasicDetails();
        List<String> regions = new ArrayList<>();
        List<Integer> unitIds = new ArrayList<>();

        if (ubds != null) {
            updateUnitBasicDetails(ubds);
            updateCity2TakeAwayMapping(ubds);
            ubds.forEach(u -> regions.add(u.getRegion()));
        }
        for (String r : regions) {
            try {
                updateDeliveryProductPrices(r);
            } catch (Exception e) {
                LOG.error("Error in updating delivery prices for region {}", r, e);
            }
        }
        for (Integer id : unitIds) {
            try {
                updateTakeAwayProductPrices(id);
            } catch (Exception e) {
                LOG.error("Error in updating prices for unitId {}", id, e);
            }
        }
        loadProductCache();
    }

    @Override
    public void reloadRecipeCache() {
        List<ProductRecipes> recipes = redisService.getAllProductRecipes();
        if (recipes != null) {
            loadProductRecipes(recipes);
        }
    }

    @Override
    public void loadLocationCache() throws URISyntaxException, IOException {
        neoCache.getCityToStateMapping().clear();
        List<Location> locations = redisService.loadLocationCache();
        Map<String, Location> locationMap = new HashMap<>();
        locations.forEach(location ->
            locationMap.put(location.getName(), location)
        );
        neoCache.getCityToLocalityMapping().keySet().forEach(s -> {
            if (!neoCache.getCityToStateMapping().containsKey(s)) {
                neoCache.getCityToStateMapping().put(s, locationMap.get(s).getState().getId());
            }
        });
    }

    @Override
    public void updateTagData() {
        List<WebTag> tags = webTagService.getAllTags();
        if (tags != null) {
            clearTags();
            tags.forEach(tag -> {
                if (tag.getStatus().equals("ACTIVE")) {
                    addTagToCache(tag);
                }
            });
        }
    }

    private void loadProductCache() throws URISyntaxException {
        List<ProductBasicDetail> productBasicDetails = redisService.getAllProductBasicDetails();
        neoCache.getProductBasicDetailMap().clear();
        productBasicDetails.forEach(productBasicDetail ->
            neoCache.getProductBasicDetailMap().put(productBasicDetail.getId(), productBasicDetail)
        );
        loadProductImages();
    }

    @Override
    public void loadProductImages() throws URISyntaxException {
        neoCache.getProductImages().putAll(redisService.getAllProductImages());
    }

    private void clearTags() {
        neoCache.getWebTags().clear();
        neoCache.getMenuTagData().clear();
        neoCache.getProductTagData().clear();
    }

    private void addTagToCache(WebTag tag) {
        neoCache.getWebTags().put(tag.getName(), tag.getValues());

        if (WebTagCategory.MENU.equals(tag.getCategory())) {
            // menu tags are the tags for menu
            neoCache.getMenuTagData().put(tag.getName(), tag.getValues());
        } else {
            // tags applied on products
            for (String s : tag.getValues()) {
                if (neoCache.getProductTagData().containsKey(s)) {
                    neoCache.getProductTagData().get(s).add(tag.getName());
                } else {
                    List<String> l = new ArrayList<>();
                    l.add(tag.getName());
                    neoCache.getProductTagData().put(s, l);
                }
            }
        }
    }

    @Override
    public void updateLocalityData() {
        List<UnitLocalityMapping> list = getUnitLocalityMappingData();
        neoCache.getCityToStateMapping().clear();
        neoCache.getLocalityData().clear();
        neoCache.getCityToLocalityMapping().clear();
        neoCache.getCityToUnitMapping().clear();
        neoCache.getTakeAwayMappings().clear();

        list.forEach(this::addCityStateMapping);
        list.forEach(p -> neoCache.getLocalityData().put(createLocalityVO(p), p));
        list.forEach(this::addLocalityToCityMapping);
        list.forEach(this::addTakeAwayMapping);
    }

    private void addCityStateMapping(UnitLocalityMapping p) {
        if (!neoCache.getCityToStateMapping().containsKey(p.getCity())) {
            //neoCache.getCityToStateMapping().put(p.getCity(), p.getState())
        }
    }

    private void addTakeAwayMapping(UnitLocalityMapping p) {
        List<IdName> l = new ArrayList<>();
        if (p.getPrimaryTakeAwayUnitId() > 0) {
            l.add(new IdName(p.getPrimaryTakeAwayUnitId(), p.getPrimaryTakeAwayUnitName()));
        }
        if (p.getSecondaryTakeAwayUnitId() > 0) {
            l.add(new IdName(p.getSecondaryTakeAwayUnitId(), p.getSecondaryTakeAwayUnitName()));
        }
        if (p.getTertiaryTakeAwayUnitId() > 0) {
            l.add(new IdName(p.getTertiaryTakeAwayUnitId(), p.getTertiaryTakeAwayUnitName()));
        }
        neoCache.getTakeAwayMappings().put(createLocalityVO(p), l);
    }

    private void addLocalityToCityMapping(UnitLocalityMapping p) {
        List<String> l = neoCache.getCityToLocalityMapping().get(p.getCity());
        if (l != null) {
            l.add(p.getLocality());
        } else {
            l = new ArrayList<String>();
            l.add(p.getLocality());
            neoCache.getCityToLocalityMapping().put(p.getCity(), l);
        }
        if (p.isDefault()) {
            neoCache.getCityToUnitMapping().put(p.getCity(), p.getLocality());
        }
    }

    private UnitLocalityMappingVO createLocalityVO(UnitLocalityMapping p) {
        return new UnitLocalityMappingVO(p.getCity(), p.getLocality());
    }

    @Override
    public boolean addUnitLocalityMapping(UnitLocalityMapping mapping) {
        if (localityService.addUnitLocalityMapping(mapping)) {
            updateLocalityData();
            return true;
        }
        return false;
    }

    @Override
    public boolean addUnitLocalityMappings(List<UnitLocalityMapping> mappings) {
        if (localityService.addUnitLocalityMappings(mappings)) {
            updateLocalityData();
            return true;
        }
        return false;
    }

    @Override
    public boolean removeUnitLocalityMapping(UnitLocalityMapping mapping) {
        if (localityService.removeUnitLocalityMapping(mapping)) {
            updateLocalityData();
            return true;
        }
        return false;
    }

    @Override
    public boolean removeUnitLocalityMappings(List<UnitLocalityMapping> mappings) {
        if (localityService.removeUnitLocalityMappings(mappings)) {
            updateLocalityData();
            return true;
        }
        return false;
    }

    @Override
    public boolean updateUnitLocalityMappings(UnitLocalityMapping mapping) {
        if (localityService.updateUnitLocalityMapping(mapping)) {
            updateLocalityData();
            return true;
        }
        return false;
    }

    @Override
    public boolean addDeliveryUnit(Unit unit) {
        neoCache.getDeliveryUnitMappings().put(unit.getId(), unit);
        return true;
    }

    @Override
    public void updateCity2TakeAwayMapping(List<UnitBasicDetail> details) {
        Map<String, List<IdName>> mappings = neoCache.getCity2takeAwayUnitMappings();
        mappings.clear();
        List<IdName> l = null;
        for (UnitBasicDetail u : details) {
            if (isAvailable(u)) {
                l = mappings.get(u.getCity());
                if (l == null) {
                    l = new ArrayList<>();
                    mappings.put(u.getCity(), l);
                }
                l.add(new IdName(u.getId(), u.getName()));
            }
        }
    }

    private boolean isAvailable(UnitBasicDetail u) {
//        return AppConstants.ACTIVE.equals(u.getCafeNeoStatus()) && WebOrderingConstants.CAFE.equals(u.getCategory())
//                && !WebOrderingConstants.TG.equals(u.getSubCategory());
        return AppConstants.ACTIVE.equals(u.getCafeNeoStatus());
    }

    @Override
    public boolean addWebTag(WebTag tag) {
        if (webTagService.add(tag)) {
            updateTagData();
            return true;
        }
        return false;
    }

    @Override
    public boolean updateWebTag(WebTag tag) {
        if (webTagService.update(tag)) {
            updateTagData();
            return true;
        }
        return false;
    }

    @Override
    public boolean removeWebTag(WebTag tag) {
        if (webTagService.remove(tag)) {
            updateTagData();
            return true;
        }
        return false;
    }

    @Override
    public void updateUnitBasicDetails(List<UnitBasicDetail> details) {
        if (details != null) {
            for (UnitBasicDetail ubd : details) {
                updateUnitBasicDetail(ubd);
            }
        }
    }

    private void updateUnitBasicDetail(UnitBasicDetail ubd) {
        neoCache.getUnitBasicDetails().put(ubd.getId(), ubd);
    }

    @Override
    public void loadProductRecipes(List<ProductRecipes> recipes) {
        for (ProductRecipes r : recipes) {
            saveRecipeInCache(r);
        }
    }

    @Override
    public void saveRecipeInCache(ProductRecipes recipe) {
        neoCache.getCompleteRecipe().put(recipe.getProductId(), recipe);
        ProductRecipes clone = clone(recipe, ProductRecipes.class);
        setOnlyCustomizable(clone);
        neoCache.getRecipes().put(recipe.getProductId(), clone);
    }

    private void setOnlyCustomizable(ProductRecipes clone) {
        for (RecipeDetail recipe : clone.getRecipes()) {
            if(Objects.isNull(recipe.getIngredient())){
                recipe.setIngredient(new IngredientDetail());
            }
            if(Objects.isNull(recipe.getIngredient().getProducts())){
                recipe.getIngredient().setProducts(new ArrayList<>());
            }
            if(Objects.isNull(recipe.getIngredient().getVariants())){
                recipe.getIngredient().setVariants(new ArrayList<>());
            }
            if(Objects.isNull(recipe.getIngredient().getComponents())){
                recipe.getIngredient().setComponents(new ArrayList<>());
            }
            if (recipe.getIngredient().getProducts() != null && !recipe.getIngredient().getProducts().isEmpty()) {
                List<IngredientProduct> products = new ArrayList<>();
                for (IngredientProduct product : recipe.getIngredient().getProducts()) {
                    if (product.isCustomize()) {
                        products.add(product);
                    }
                }
                recipe.getIngredient().getProducts().clear();
                recipe.getIngredient().getProducts().addAll(products);

            }
            if (recipe.getIngredient().getVariants() != null && !recipe.getIngredient().getVariants().isEmpty()) {
                List<IngredientVariant> variants = new ArrayList<>();
                for (IngredientVariant variant : recipe.getIngredient().getVariants()) {
                    if (variant.isCustomize()) {
                        variants.add(variant);
                    }
                }
                recipe.getIngredient().getVariants().clear();
                recipe.getIngredient().getVariants().addAll(variants);
            }
            if (recipe.getIngredient().getComponents() != null) {
                recipe.getIngredient().getComponents().clear();
            }
			/*if (recipe.getAddons() != null
					&& clone.getProductId() != WebOrderingConstants.DESI_CHAI_PRODUCT_ID_FOR_RECIPE) {
				recipe.getAddons().clear();
			}*/
            if (recipe.getAddons() != null) {
                Optional<List<IngredientProductDetail>> addons = Optional.of(recipe.getAddons().stream().filter(ingredientProductDetail -> {
                    return ingredientProductDetail.isCustomize() || ingredientProductDetail.getProduct().getName().equalsIgnoreCase("Adrak");
                }).collect(Collectors.toList()));
                recipe.getAddons().clear();
                recipe.getAddons().addAll(addons.orElse(Collections.emptyList()));
            }
            //TODO Vishal this makes not sense. Please remove this code and get tested again.
            if (recipe.getRecommendations() != null && !recipe.getRecommendations().isEmpty()) {
                List<IngredientProductDetail> recomendationsList = new ArrayList<>();
                for (IngredientProductDetail prodDetail : recipe.getRecommendations()) {
                    recomendationsList.add(prodDetail);
                }
                recipe.getRecommendations().clear();
                recipe.getRecommendations().addAll(recomendationsList);
            }
        }
    }

    private <T> T clone(T object, Class<T> clazz) {
        Gson gson = new Gson();
        String str = gson.toJson(object);
        return gson.fromJson(str, clazz);
    }

    @Override
    public boolean updateUnitLocalityMappings(List<UnitLocalityMapping> mappings) {
        if (localityService.updateUnitLocalityMappings(mappings)) {
            updateLocalityData();
            return true;
        }
        return false;
    }

    @Override
    public List<UnitLocalityMapping> getAllUnitLocalityMappings() {
        return localityService.getUnitLocalityMappingData();
    }

    @Override
    public Unit getUnitDataFromRedis(int unitId) {
        Unit u = null;
        try {
            UnitBasicDetail ubd = redisService.getUnitbasicDetails(unitId);
            ProductUnit pd = redisService.getProductUnit(ubd.getRegion());
            DeliveryUnit du = redisService.getDeliveryUnit(ubd.getId());
            u = new Unit(ubd.getId(), ubd.getName(), pd.getProducts(), du.getTaxProfiles(), null, du.getTaxes(),
                du.getLocation(), ubd.getPackagingType(), ubd.getPackagingValue());
            addDeliveryUnit(u);
        } catch (URISyntaxException e) {
            LOG.error("Error in getting data from redis server", e);
            NeoUtil.slackIt(env.getEnvType(), e);
        }
        return u;
    }

    @Override
    public void updateTakeAwayProductPrices(int unitId) throws NeoMetadataException {
        try {
            Unit unit = redisService.getUnit(unitId);
            if (unit != null) {
                for (Product p : unit.getProducts()) {
                    for (ProductPrice pp : p.getPrices()) {
                        neoCache.getTakeAwayUnitProductPriceMapping().put(
                            neoCache.getTakeAwayUnitProductPriceKey(unit.getId(), p.getId(), pp.getDimension()),
                            pp.getPrice());
                    }
                }
            }
        } catch (IOException | URISyntaxException e) {
            String msg = "Error in fetching unit from redis cache: " + e.getMessage();
            LOG.error(msg, e);
            throw new NeoMetadataException(msg, e);
        }
    }

    @Override
    public void updateDeliveryProductPrices(String region) {
        try {
            ProductUnit unit = redisService.getProductUnit(region);
            for (Product p : unit.getProducts()) {
                for (ProductPrice pp : p.getPrices()) {
                    neoCache.getDeliveryUnitProductPriceMapping().put(
                        neoCache.getDeliveryUnitProductPriceKey(region, p.getId(), pp.getDimension()),
                        pp.getPrice());
                }
            }
        } catch (URISyntaxException e) {
            LOG.error("Error while updating Delivery Product Prices", e);
            NeoUtil.slackIt(env.getEnvType(), e);
        }
    }

    @Override
    public void updateRecipeInCache(int pId) throws URISyntaxException {
        ProductRecipes pr = redisService.getProductRecipe(pId);
        if (pr != null) {
            saveRecipeInCache(pr);
        }
    }

    @Override
    public void updateWebCategories() {
        neoCache.getWebCategories().clear();
        neoCache.getWebCategories().addAll(redisService.getWebCategories());
    }

    @Override
    public void updateUnitBasicDetails() throws URISyntaxException {
        List<UnitBasicDetail> details = redisService.getAllUnitBasicDetails();
        updateCity2TakeAwayMapping(details);
        updateUnitBasicDetails(details);
    }

    @Override
    public Unit getUnit(int unitId) {
        try {
            return redisService.getUnit(unitId);
        } catch (Exception e) {
            LOG.error("Unable to fetch unit {} from redis cache", unitId, e);
        }
        return null;
    }

    @Override
    public UnitBusinessHours getBusinessHours(int unitId) {
        try {
            return redisService.getBusinessHours(unitId);
        } catch (Exception e) {
            LOG.error("Unable to fetch unit {} from redis cache", unitId, e);
        }
        return null;
    }

    @Override
    public UnitBasicDetail getUnitBasicDetail(int unitId) {
        try {
            UnitBasicDetail ubd = redisService.getUnitbasicDetails(unitId);
            updateUnitBasicDetail(ubd);
            return ubd;
        } catch (Exception e) {
            LOG.error("Unable to fetch Unit Basic Detail form redis for Unit Id {}", unitId, e);
        }
        return null;
    }

    @Override
    public List<WebTag> getAllWebTags() {
        return webTagService.getAllTags();
    }

    @Override
    public List<UnitLinkMapping> addRedirectLinkMappings(List<UnitLinkMapping> mappings) throws NeoMetadataException {
        List<UnitLinkMapping> existingMappings = unitLinkMappingDao.findAll();
        List<UnitLinkMapping> updatedMappings = new ArrayList<>();
        Map<Integer, UnitLinkMapping> existingMappingsMap = new HashMap<>();
        for (UnitLinkMapping unitLinkMapping : existingMappings) {
            existingMappingsMap.put(unitLinkMapping.getUnitId(), unitLinkMapping);
        }
        for (UnitLinkMapping unitLinkMapping : mappings) {
            Integer unitId = unitLinkMapping.getUnitId();
            if (existingMappingsMap.containsKey(unitLinkMapping.getUnitId())) {
                throw new NeoMetadataException("Mappings for unit id " + unitLinkMapping.getUnitId() + " already exists!");
            } else {
                unitLinkMapping = unitLinkMappingDao.save(unitLinkMapping);
                if (unitLinkMapping != null) {
                    updatedMappings.add(unitLinkMapping);
                } else {
                    throw new NeoMetadataException("Error saving mapping for unit id" + unitId);
                }
            }
        }
        return updatedMappings;
    }

    @Override
    public List<UnitLinkMapping> getRedirectLinkMappings() {
        return unitLinkMappingDao.findAll();
    }

    @Override
    public List<UnitDetail> addCityUnits(List<UnitDetail> units) {
        if (units != null) {
            units = unitDetailDao.saveAll(units);
        }
        return units;
    }

    @Override
    public List<UnitDetail> getCityUnits(String city) {
        if (city == null) {
            return unitDetailDao.findAll();
        } else {
            return unitDetailDao.findAllByCity(city);
        }
    }

    @Override
    public void trackZomatoRedirect(RedirectTracking request) {
        request.setTime(NeoUtil.getCurrentTimestamp());
        redirectTrackingDao.save(request);
    }

    @Override
    public UnitLinkMapping getRedirectLinkMapping(Integer unitId) {
        return unitLinkMappingDao.findAllByUnitId(unitId);
    }

    @Override
    public void addCartIdToCache(String deviceId, String cartId) {
        neoCache.getCartId().put(deviceId, cartId);
    }

    private void loadProductPriceProfiles() throws URISyntaxException {
        neoCache.getProductPriceProfilesMap().clear();
        redisService.loadUnitProductPriceProfile().forEach((unitId, priceListMap) -> neoCache.getProductPriceProfilesMap().put(unitId, priceListMap));
    }

}
