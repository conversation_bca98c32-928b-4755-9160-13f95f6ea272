package com.stpl.tech.neo.core.service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.domain.model.mongo.*;
import com.stpl.tech.redis.domain.model.*;

public interface NeoMetadataCacheManagementService {

    public List<UnitLocalityMapping> getUnitLocalityMappingData();

    void loadProductImages() throws URISyntaxException;

    public void updateLocalityData();

    void reloadRecipeCache();

    void loadLocationCache() throws URISyntaxException, IOException;

    public void updateTagData();

    public boolean addUnitLocalityMapping(UnitLocalityMapping mapping);

    public boolean addUnitLocalityMappings(List<UnitLocalityMapping> mappings);

    public boolean removeUnitLocalityMapping(UnitLocalityMapping mapping);

    public boolean updateUnitLocalityMappings(UnitLocalityMapping mapping);

    public boolean addDeliveryUnit(Unit unit);

    public void updateCity2TakeAwayMapping(List<UnitBasicDetail> details);

    public boolean addWebTag(WebTag tag);

    public boolean updateWebTag(WebTag tag);

    public boolean removeWebTag(WebTag tag);

    public void updateUnitBasicDetails(List<UnitBasicDetail> details);

    public void updateTakeAwayProductPrices(int unit) throws NeoMetadataException;

    public void updateDeliveryProductPrices(String region);

    public void clearCache() throws URISyntaxException;

    public void loadProductRecipes(List<ProductRecipes> recipes);

    public boolean updateUnitLocalityMappings(List<UnitLocalityMapping> mappings);

    public List<UnitLocalityMapping> getAllUnitLocalityMappings();

    public boolean removeUnitLocalityMappings(List<UnitLocalityMapping> mappings);

    public void saveRecipeInCache(ProductRecipes recipe);

    public Unit getUnitDataFromRedis(int unitId);

    public void updateRecipeInCache(int pId) throws URISyntaxException;

    public void updateWebCategories();

    public void updateUnitBasicDetails() throws URISyntaxException;

    public void reloadCache() throws URISyntaxException;

    public Unit getUnit(int unitId);

    public UnitBusinessHours getBusinessHours(int unitId);

    public UnitBasicDetail getUnitBasicDetail(int unitId);

    public List<WebTag> getAllWebTags();

    List<UnitLinkMapping> addRedirectLinkMappings(List<UnitLinkMapping> mappings) throws NeoMetadataException;

    List<UnitLinkMapping> getRedirectLinkMappings();

    List<UnitDetail> addCityUnits(List<UnitDetail> units);

    List<UnitDetail> getCityUnits(String city);

    void trackZomatoRedirect(RedirectTracking request);

    UnitLinkMapping getRedirectLinkMapping(Integer unitId);

    public void addCartIdToCache(String deviceId, String cartId);

	void clearWebTagCache();

}
