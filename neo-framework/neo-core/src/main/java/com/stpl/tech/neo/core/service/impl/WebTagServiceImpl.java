package com.stpl.tech.neo.core.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.WebTagDao;
import com.stpl.tech.neo.core.service.WebTagService;
import com.stpl.tech.neo.domain.model.mongo.WebTag;

@Service
public class WebTagServiceImpl implements WebTagService {

	@Autowired
	WebTagDao tagDao;

	@Override
	public List<WebTag> getAllTags() {
		return tagDao.findAll();
	}

	@Override
	public boolean add(WebTag tag) {
		tagDao.save(tag);
		return true;
	}

	@Override
	public boolean update(WebTag tag) {
		tagDao.save(tag);
		return true;
	}

	@Override
	public boolean remove(WebTag tag) {
		tagDao.delete(tag);
		return true;
	}

}
