package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.dao.SMSRequestDao;
import com.stpl.tech.neo.core.service.CRMService;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoReferralService;
import com.stpl.tech.neo.core.util.KettleCrmClientEndpoints;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.ReferralRequest;
import com.stpl.tech.neo.domain.model.ReferralResponse;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NeoReferralServiceImpl implements NeoReferralService {

    private static final Logger LOG = LoggerFactory.getLogger(NeoReferralServiceImpl.class);

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private CRMService crmService;

	@Autowired
	private SMSRequestDao smsDao;


	@Override
	public ReferralResponse submit(ReferralRequest request) {
        ReferralResponse response =  WebServiceHelper.postWithAuth(getRefSubmitEndPoint(), props.getNeoClientToken(), request,
            ReferralResponse.class);
        LOG.info("Referral Response is: {} ", JSONSerializer.toJSON(response));
		return response;
	}

	@Override
	public Boolean sendSMS(SMSRequest request) {
		Boolean result = crmService.sendSMS(request);
		if (result) {
			smsDao.save(request);
		}
		return result;
	}

	@Override
	public String getOrderDetailForFeedback(String input) {
		String result = WebServiceHelper.postWithAuth(getOrderDetailForFeedbackEndPoint(), props.getNeoClientToken(), input,
				String.class);
		LOG.info("Order detail found == {}", result);
		return result;
	}

	@Override
	public Boolean submitFeedback(Object input) {
		return WebServiceHelper.postWithAuth(getSubmitFeedbackEndPoint(), props.getNeoClientToken(), JSONSerializer.toJSON(input),
				Boolean.class);
	}

	@Override
	public ReferralResponse validate(ReferralRequest request) {
		return WebServiceHelper.postWithAuth(getRefValidateEndPoint(), props.getNeoClientToken(), request,
				ReferralResponse.class);
	}

	@Override
	public OTPResponse resendOTP(String contact) {
		return crmService.resendOTP(contact);
	}

	@Override
	public String generateToken(String input) {
		return WebServiceHelper.postWithAuth(getRefGenerateTokenEndPoint(), props.getNeoClientToken(), input,
				String.class);
	}

	@Override
	public String generateTokenForOrderManagement(String input) {
		return WebServiceHelper.postWithAuth(getOrderManagementTokenEndPoint(), props.getNeoClientToken(), input,
				String.class);
	}


	private String getRefGenerateTokenEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.GENERATE_TOKEN;
	}

	private String getOrderManagementTokenEndPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_ORDER_MANAGEMENT_TOKEN;
	}

	private String getRefSubmitEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SUBMIT_REFERRAL;
	}

	private String getRefValidateEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.VALIDATE_REFERRAL;
	}

	private String getOrderDetailForFeedbackEndPoint(){
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.ORDER_DETAIL_FOR_FEEDBACK;
	}

	private String getSubmitFeedbackEndPoint(){
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SUBMIT_FEEDBACK;
	}
}
