package com.stpl.tech.neo.core.service.impl;

import java.net.URISyntaxException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoKettleOfferService;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.domain.model.mongo.SignUpTimeSlotCount;
import com.stpl.tech.redis.core.util.WebServiceHelper;

@Service
public class NeoKettleOfferServiceImpl implements NeoKettleOfferService {

    @Autowired
    private EnvironmentProperties env;

    @Override
    public boolean checkIfSignUpOfferAvailedViaKettle(String customerContact) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CHECK_IF_SIGN_UP_OFFER_AVAILED;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Boolean.class,
                customerContact, null);
    }

    @Override
    public SignUpTimeSlotCount getTimeSlotCounts(String dateOfDelivery) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_TIMESLOT_COUNTS;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, SignUpTimeSlotCount.class,
                dateOfDelivery, null);
    }

}
