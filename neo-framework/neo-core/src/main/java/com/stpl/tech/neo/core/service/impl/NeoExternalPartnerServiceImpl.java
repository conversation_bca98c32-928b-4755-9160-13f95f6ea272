package com.stpl.tech.neo.core.service.impl;

import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.cache.NeoLoginTokenCache;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoCustomerService;
import com.stpl.tech.neo.core.service.NeoExternalPartnerService;
import com.stpl.tech.neo.core.service.StampingService;
import com.stpl.tech.neo.core.util.KettleCrmClientEndpoints;
import com.stpl.tech.neo.domain.model.ExternalPartnerResponse;
import com.stpl.tech.neo.domain.model.NeoCustomerResponse;
import com.stpl.tech.neo.domain.model.mongo.CustomerInfo;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

@Service
public class NeoExternalPartnerServiceImpl implements NeoExternalPartnerService {

	private static final Logger LOG = LoggerFactory.getLogger(NeoCustomerServiceImpl.class);

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private NeoCustomerService customerService;
	
	@Autowired
	private NeoLoginTokenCache tokenCache;
	
	@Autowired
	private StampingService stampingService;


	@Override
	public ExternalPartnerResponse requestCustomerLoginByKey(String accessKey, String token) {
		ExternalPartnerResponse response = new ExternalPartnerResponse();
		try {
			NeoCustomerResponse data = searchOrAddCustomer(accessKey, token);
			String loginToken = generateLoginToken(data);
			response.setError(null);
			response.setRedirectUrl(getURL() + "?key=" + loginToken);
			response.setStatus(true);
		} catch (Exception e) {
			response.setError(e.getMessage());
			response.setRedirectUrl(getURL());
			response.setStatus(false);
		}
		return response;
	}
	
	private String getURL() {
		if (AppUtils.isProd(props.getEnvType())) {
			return "https://cafes.chaayos.com";
		} else {
			return "https://dev-staging.chaayos.com";
		}
	}

	private String generateLoginToken(NeoCustomerResponse data) {
		DeviceDetail device = new DeviceDetail();
		stampingService.registerDevice(device);
		CustomerInfo info = customerService.signInPartnerCustomer(data.getCustomer(), device);
		return tokenCache.generateToken(info, data.getUnitId(), data.getTerminal());
	}

	public NeoCustomerResponse searchOrAddCustomer(String accessKey, String token) {
		String endPoint = getCustomerSearchOrAddCustomer();
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("accessKey", accessKey);
		uriVariables.put("token", token);
		NeoCustomerResponse response = null;
		try {
			response = WebServiceHelper.exchangeWithAuth(endPoint, null, HttpMethod.GET, NeoCustomerResponse.class, null,
					uriVariables);
		} catch (URISyntaxException e) {
			LOG.error("Failed to login customer", e);
		}
		return response;
	}

	private String getCustomerSearchOrAddCustomer() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.EXTERNAL_CUSTOMER;
	}

}
