package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.redis.domain.model.UnitBusinessHours;
import com.stpl.tech.redis.domain.model.UnitHours;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public abstract class AbstractTimeValidator {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractTimeValidator.class);

    protected String getbusinessHoursMessage(UnitBusinessHours ubs, boolean isDelivery) {
        Date date = AppUtils.getCurrentTimestamp();
        int dayOfWeek = AppUtils.getDayOfWeek(date);
        String msg = null;
        String start = "";
        String end = "";
        for (UnitHours uhrs : ubs.getOperationalHours()) {
            if (uhrs.getDayOfTheWeek() != null && !uhrs.getDayOfTheWeek().trim().equals("")
                && uhrs.getDayOfTheWeekNumber() == dayOfWeek && uhrs.isIsOperational()) {
                if (isDelivery) {
                    start = uhrs.getDeliveryOpeningTime();
                    end = uhrs.getDeliveryClosingTime();
                } else {
                    start = uhrs.getTakeAwayOpeningTime();
                    end = uhrs.getTakeAwayClosingTime();
                }
                try {
                    msg = "Order can placed between " + AppUtils.msgTimeFormat(start) + " and "
                        + AppUtils.msgTimeFormat(end) + " only.";
                } catch (Exception e) {
                    LOG.error("Error While parsing date for error msg", e);
                    msg = null;
                }
            }
        }
        if (msg == null) {
            msg = "Cafe is closed";
        }
        return msg;
    }

    protected boolean isValidTime(UnitBusinessHours ubs, String orderSource) {
        Date date = AppUtils.getCurrentTimestamp();
        int dayOfWeek = AppUtils.getDayOfWeek(date);
        for (UnitHours uhrs : ubs.getOperationalHours()) {
            if (uhrs.getDayOfTheWeek() != null && uhrs.getDayOfTheWeek().trim() != ""
                && uhrs.getDayOfTheWeekNumber() == dayOfWeek && uhrs.isIsOperational()) {
                if (NeoUtil.isCOD(orderSource)) {
                    return compareTime(uhrs.getDeliveryOpeningTime(), uhrs.getDeliveryClosingTime(), date);
                } else if (NeoUtil.isCafe(orderSource)) {
                    return compareTime(uhrs.getDineInOpeningTime(), uhrs.getDineInClosingTime(), date);
                } else  if (NeoUtil.isTakeAway(orderSource)){
                    return compareTime(uhrs.getTakeAwayOpeningTime(), uhrs.getTakeAwayClosingTime(), date);
                }
            }
        }
        return false;
    }

    /*protected boolean compareTime(String open, String close, Date current) {
        try {
            Date start = AppUtils.convertTimeToDate(open);
            Date end = AppUtils.convertTimeToDate(close);
            // for time less then 5AM
            end = AppUtils.getCorrectedDateTime(end);
            if (start.compareTo(current) > 0) {
                return false;
            }
            if (end.compareTo(current) < 0) {
                return false;
            }
        } catch (Exception e) {
            LOG.error("Error while time comaprison", e);
            return false;
        }
        return true;
    }*/

    private boolean compareTime(String open, String close, Date current) {
        try {
            Date start = AppUtils.setTimeToDate(AppUtils.getCurrentTimestamp(), AppUtils.timeToDate(open), true);
            Date end = AppUtils.setTimeToDate(AppUtils.getCurrentTimestamp(), AppUtils.timeToDate(close), false);
            if (end.compareTo(start) > 0) {
                return start.compareTo(current) <= 0 && end.compareTo(current) >= 0;
            } else {
                Date end1 = AppUtils.getUpdatedTimeInDate(23, 59, 59, end);
                if (current.compareTo(start) >= 0 && current.compareTo(end1) < 0) {
                    return start.compareTo(current) <= 0 && end1.compareTo(current) >= 0;
                } else {
                    Date start1 = AppUtils.getUpdatedTimeInDate(0, 0, 0, start);
                    return start1.compareTo(current) <= 0 && end.compareTo(current) >= 0;
                }
            }
        } catch (Exception e) {
            LOG.error("Error while time comparison", e);
            return false;
        }
    }
}
