package com.stpl.tech.neo.core.util;

public class KettleCrmClientEndpoints {

	public static final String CRM_ENTRY_POINT = "/kettle-crm/rest/v1/";
	public static final String SEARCH_CUSTOMER = CRM_ENTRY_POINT + "neo/customer/lookup";
	public static final String OLD_SEARCH_CUSTOMER = CRM_ENTRY_POINT + "neo/old-customer/lookup";
	public static final String HAS_ORDERS_FOR_CAFE = CRM_ENTRY_POINT + "neo/has-orders/cafe";
	public static final String HAS_ORDERS_FOR_DELIVERY = CRM_ENTRY_POINT + "neo/has-orders/delivery";
	public static final String GET_INTERNAL_CUSTOMERS = CRM_ENTRY_POINT + "neo/get-internal-customers";
	public static final String SIGNUP_CUSTOMER = CRM_ENTRY_POINT + "neo/customer/signup";
	public static final String SEND_OTP = CRM_ENTRY_POINT + "neo/otp/generate";
	public static final String VERIFY_OTP = CRM_ENTRY_POINT + "neo/otp/verify";
	public static final String ADD_ADDRESS = CRM_ENTRY_POINT + "neo/address/add";
	public static final String NEW_ADDRESS = CRM_ENTRY_POINT + "neo/address/new";
	public static final String UPDATE_CUSTOMER = CRM_ENTRY_POINT + "neo/customer/update";
	public static final String RESEND_OTP = CRM_ENTRY_POINT + "neo/otp/resend";
	public static final String NPS_SUBMIT = "form/external/type-form/submit";
	public static final String EXTERNAL_CUSTOMER = CRM_ENTRY_POINT + "neo/partner/customer/login";
	public static final String SUBMIT_REFERRAL = CRM_ENTRY_POINT + "ref/submit/code";
	public static final String VALIDATE_REFERRAL = CRM_ENTRY_POINT + "ref/validate";
	public static final String ORDER_DETAIL_FOR_FEEDBACK = CRM_ENTRY_POINT + "form/order-detail-feedback";
	public static final String SUBMIT_FEEDBACK = CRM_ENTRY_POINT + "form/submit-feedback";
	public static final String GENERATE_TOKEN = CRM_ENTRY_POINT + "ref/generate-token";
	public static final String SEND_SMS = CRM_ENTRY_POINT + "ref/generic/sms/send";
	public static final String SIGNUP_COUPON = CRM_ENTRY_POINT + "neo/signup/coupon";
	public static final String CUSTOMER_BY_CONTACT = CRM_ENTRY_POINT + "crm/customer-by-contact";


}
