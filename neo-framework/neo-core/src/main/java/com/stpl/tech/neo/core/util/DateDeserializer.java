package com.stpl.tech.neo.core.util;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;

public class DateDeserializer implements JsonDeserializer<Date> {

    public static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_TL = new ThreadLocal<SimpleDateFormat>() {

        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }

    };

    @Override
    public Date deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext)
            throws JsonParseException {
        try {
            String s = jsonElement.getAsString();
            try {
                return parseDate2(s);
            } catch (NumberFormatException e) {
                return parseDate(s);
            }
        } catch (ParseException e) {

        }
        throw new JsonParseException("Unparseable time: \"" + jsonElement.getAsString());
    }

    public static Date parseDate(String s) throws ParseException {
        return DATE_FORMAT_TL.get().parse(s);
    }

    public static Date parseDate2(String s) throws ParseException {
        long time = Long.valueOf(s);
        return new Date(time);
    }

}
