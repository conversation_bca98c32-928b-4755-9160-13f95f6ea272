package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.ReferralRequest;
import com.stpl.tech.neo.domain.model.ReferralResponse;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;

public interface NeoReferralService {

	public ReferralResponse submit(ReferralRequest request);

	public ReferralResponse validate(ReferralRequest request);

	public OTPResponse resendOTP(String contact);

	public String generateToken(String input);

	public String generateTokenForOrderManagement(String input);

	Boolean sendSMS(SMSRequest request);

    String getOrderDetailForFeedback(String token);

    Boolean submitFeedback(Object token);
}
