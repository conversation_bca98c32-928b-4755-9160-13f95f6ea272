package com.stpl.tech.neo.core.dao;

import com.stpl.tech.neo.domain.model.mongo.UnitLinkMapping;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Chaayos on 18-10-2016.
 */
@Repository
public interface UnitLinkMappingDao extends MongoRepository<UnitLinkMapping, String> {

    public UnitLinkMapping findAllByUnitId(Integer unitId);
}
