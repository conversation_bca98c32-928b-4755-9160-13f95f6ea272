package com.stpl.tech.neo.core.util;

public enum WebErrorCodes {

	INVALID_CART(701, "Invalid Cart"),
	CART_NOT_FOUND(702, "Cart Not Found"),
	INVALID_UNIT(703, "Invalid Unit"),
	INVALID_SETTLEMENT(704, "Invalid Settlement"),
	INVALID_CUSTOMER(705, "Invalid Customer"),
	INVALID_ADDRESS(706, "Invalid Address"),
	ORDER_FAILED(707, "Order Creation Failed"),
	INVALID_TRANSACTION(708, "Invalid Transaction Data"),
	OFFER_FAILED(709, "Unable to apply offer"),
	INVALID_ORDER(710, "Invalid Order"),
	SETTLED_CART(711, "Order Already Placed"),
	PAYMENT_ALREADY_COMPLETED(712, "Payment Already Completed"),
	ORDER_NOT_FOUND(714, "Order Not Found"),
	PAYMENT_DONE(715, "Payment Already Done"),
	INVALID_SOURCE(716, "Invalid Order Source"),
    INVALID_REQUEST(501,"Unable to validate requestId against accessToken"),
    CUSTOMER_NOT_FOUND(502, "Customer not found with True Caller");

	private String msg;
	private int value;

	WebErrorCodes(int errorCode, String msg) {
		this.msg = msg;
		this.value = errorCode;
	}

	public String getMsg() {
		return msg;
	}

	public int getErrorValue() {
		return value;
	}

}
