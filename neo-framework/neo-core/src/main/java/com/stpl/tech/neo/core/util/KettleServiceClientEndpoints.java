package com.stpl.tech.neo.core.util;

public class KettleServiceClientEndpoints {

	public static final String KETTLE_SERVICE_BASE_URL = "/kettle-service/rest/v1/";
	public static final String KETTLE_SERVICE_BASE_URL_V2 = "/kettle-service/rest/v2/";
	public static final String KETTLE_CRM_BASE_URL = "/kettle-crm/rest/v1/";
	public static final String KETTLE_MASTER_BASE_URL="/master-service/rest/v1/";
	public static final String REPORT_SERVICE_BASE_URL="/report-service/rest/v1/";
	public static final String OFFER_SERVICE_BASE_URL="/offer-service/rest/v1/";
	public static final String CREATE_ORDER = KETTLE_SERVICE_BASE_URL + "order-management/order/create/web";
	public static final String APPLY_OFFER = KETTLE_SERVICE_BASE_URL + "customer-offer-management/coupon/apply";
	public static final String APPLY_CHAAYOS_CASH = KETTLE_SERVICE_BASE_URL + "customer-offer-management/cash/apply";
	public static final String AVAIL_SIGN_UP_OFFER = KETTLE_SERVICE_BASE_URL + "customer-offer-management/sign-up/avail";
	public static final String CHECK_IF_SIGN_UP_OFFER_AVAILED = KETTLE_SERVICE_BASE_URL + "customer-offer-management/sign-up/availed";
    public static final String GET_GIFT_CARD = KETTLE_SERVICE_BASE_URL + "order-management/get-cards";
	public static final String GET_GIFT_CARD_OFFER = KETTLE_SERVICE_BASE_URL + "order-management/get-cash-card-offer";
    public static final String CREATE_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/create";
	public static final String CREATE_PAYTM_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/paytm/create";
	public static final String CREATE_RAZORPAY_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/razorpay/create";
	public static final String UPDATE_RAZOR_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/razorpay/update";
	public static final String FETCH_RAZOR_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/razorpay/fetch";
	public static final String CANCEL_PAYMENT_REQUEST = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/cancel";
	public static final String FAILURE_PAYMENT_REQUEST = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/failure";
	public static final String STAMP_RAZOR_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/razorpay/event";
	public static final String STAMP_RAZOR_PAYMENT_COVID = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/razorpay/event/covid";
	public static final String UNIT_INVENTORY = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/web";
	public static final String UNIT_INVENTORY_LIVE = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/live/web";
	public static final String UNIT_INVENTORY_STATUS = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/products/web";
	public static final String GET_PAYMENT_STATUS = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/check";
	public static final String UPDATE_PAYTM_RESPONSE = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/paytm/update";
	public static final String PUBLISH_ORDER_STATUS_EVENT = KETTLE_SERVICE_BASE_URL + "order-management/publish-order-status";
	public static final String CREATE_EZETAP_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/ezetap/create";
	public static final String UPDATE_EZETAP_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/ezetap/update";
	public static final String CREATE_KIOSK_PAYTM_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/paytm/kiosk/qr/create";
	public static final String CHECK_KIOSK_PAYTM_PAYMENT_STATUS = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/paytm/kiosk/qr/status";
	public static final String LAUNCH_OFFER = KETTLE_SERVICE_BASE_URL + "offer-management/secure-launch-offer";
	public static final String FREE_ITEM_OFFER = KETTLE_SERVICE_BASE_URL + "offer-management/secure-free-item-offer";
	public static final String GENERATE_TOKEN = KETTLE_SERVICE_BASE_URL + "offer-management/generate-token";
	public static final String GET_ORDER_RECEIPT = KETTLE_SERVICE_BASE_URL + "order-management/order/receipt/download";
	public static final String GET_LOYALITY = KETTLE_CRM_BASE_URL + "crm/signin";
	public static final String CREATE_AGS_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/ags/create";
	public static final String UPDATE_AGS_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/ags/update";
	public static final String CHECK_AGS_PAYMENT_STATUS = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/ags/status/check";
	public static final String CREATE_KIOSK_PAYTM_UPI_PAYMENT = KETTLE_SERVICE_BASE_URL_V2 + "payment-management/payment/paytm-upi/qr/create";
	public static final String GET_AVAILABLE_CARD_AMOUNT = KETTLE_SERVICE_BASE_URL + "order-management/get-available-card-amount";
	public static final String COUPON_SIGNUP = KETTLE_SERVICE_BASE_URL + "offer-management/coupon/signup";
	public static final String GET_TIMESLOT_COUNTS = KETTLE_SERVICE_BASE_URL + "customer-offer-management/sign-up/timeslot";
    public static final String GET_MY_OFFER = KETTLE_SERVICE_BASE_URL + "order-management/my-offer";
    public static final String GET_CAMPAIGN_DETAIL = KETTLE_SERVICE_BASE_URL + "order-management/get-campaign-by-token";
    public static final String SAVE_CUSTOMER_JOURNEY = REPORT_SERVICE_BASE_URL + "customer-campaign/campaign-journey";
	public static final String GET_SPECIAL_OFFER = KETTLE_SERVICE_BASE_URL + "order-management/get-special-offer";
	public static final String GET_GAMIFIED_OFFER = KETTLE_SERVICE_BASE_URL + "gamified-offer-resource/offer-via-games";
	public static final String GET_LEADER_BOARD = KETTLE_SERVICE_BASE_URL + "gamified-offer-resource/get-leader-board";
	public static final String GET_ORDER_MANAGEMENT_TOKEN = KETTLE_SERVICE_BASE_URL + "order-management/generate-token";
}
