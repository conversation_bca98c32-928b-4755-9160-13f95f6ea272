package com.stpl.tech.neo.core.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.neo.domain.model.mongo.PaymentDetail;

@Repository
public interface PaymentDetailDao extends MongoRepository<PaymentDetail, String> {

	@Query("{'externalPaymentId' : ?0}")
	public PaymentDetail searchByExternalPaymentId(String id);

	@Query("{'cartId' : ?0}")
	public List<PaymentDetail> searchByCartId(String id);

}