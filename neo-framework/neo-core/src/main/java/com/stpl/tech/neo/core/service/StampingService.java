package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.neo.domain.model.mongo.SessionDetail;

public interface StampingService {

	public DeviceVO registerDevice(DeviceDetail deviceDetail);

	public SessionDetail createSession(SessionDetail sessionDetail);

	public DeviceVO stampDevice(DeviceVO deviceVO);

    boolean verifyDevice(DeviceVO deviceVO);

    public CartDetail stampCart(CartDetail cartDetail);

	public SessionDetail stampSession(SessionDetail sessionDetail);

	public void updateCartInDevice(CartDetail cartDetail);

	public void updateCartInCustomer(CartDetail cartDetail);

	public void removeCartFromDevice(CartDetail cart);

	public void addCartToDevice(CartDetail c);

}
