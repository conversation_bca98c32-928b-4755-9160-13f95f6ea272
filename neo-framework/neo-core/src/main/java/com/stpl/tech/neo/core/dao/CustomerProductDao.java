package com.stpl.tech.neo.core.dao;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.neo.domain.model.mongo.CustomerProductConsumption;

@Repository
public interface CustomerProductDao extends MongoRepository<CustomerProductConsumption, String> {

	public CustomerProductConsumption findByCustomerId(String customerId);

}
