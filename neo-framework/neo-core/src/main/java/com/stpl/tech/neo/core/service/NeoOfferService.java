package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.domain.model.CampaignDetailResponse;
import com.stpl.tech.neo.domain.model.mongo.OfferDetail;
import com.stpl.tech.neo.domain.model.mongo.SignUpOfferRequest;
import com.stpl.tech.neo.domain.model.mongo.SignUpTimeSlotCount;
import com.stpl.tech.neo.domain.model.mongo.WebOfferOrder;

import java.net.URISyntaxException;
import java.util.List;

public interface NeoOfferService {

    public WebOfferOrder applyOffer(WebOfferOrder woo) throws WebOrderException;

    public WebOfferOrder applyChaayosCash<PERSON><PERSON><PERSON><PERSON>(WebOfferOrder woo) throws URISyntaxException;

    List<OfferDetail> getOfferDetails(boolean status);

    boolean availSignUpOfferViaKettle(SignUpOfferRequest request) throws URISyntaxException;

    boolean checkIfSignUpOfferAvailedViaKettle(String customerContact) throws URISyntaxException;

    SignUpTimeSlotCount getTimeSlotCounts(String dateOfDelivery) throws URISyntaxException;

    CampaignDetailResponse getCampaignDetail(String token) throws URISyntaxException;
}
