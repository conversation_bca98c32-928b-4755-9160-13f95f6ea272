package com.stpl.tech.neo.core.util;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-12-2017.
 */
public class AppEmailNotification extends EmailNotification{

    private String template;
    private EnvType envType;
    private String customerEmail;
    private String customerName;

    public AppEmailNotification(String template, EnvType envType,
                                String customerEmail, String name) {
        this.template = template;
        this.envType = envType;
        this.customerEmail = customerEmail;
        this.customerName = AppUtils.checkBlank(name)!=null ? name : "";
    }

    @Override
    public String[] getToEmails() {
        return new String[] { customerEmail };
    }

    @Override
    public String getFromEmail() {
        return NeoUtil.isDev(getEnvironmentType())?"<EMAIL>":"<EMAIL>";
    }

    @Override
    public String subject() {
        return "Hi "+ customerName +", Verify your email and earn 10 loyaltea points";
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template;
        } catch (Exception e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
