package com.stpl.tech.neo.core.listner;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoMetadataService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.Activity;
import com.stpl.tech.util.JSONSerializer;

public class ActivityMessageListener implements MessageListener {

	private static final Logger LOG = LoggerFactory.getLogger(ActivityMessageListener.class);

	private MessageProducer errorQueue;
	private NeoMetadataService metaService;
	private EnvironmentProperties env;

	public ActivityMessageListener(MessageProducer errorQueue, NeoMetadataService metaService,
			EnvironmentProperties env) {
		this.errorQueue = errorQueue;
	}

	@Override
	public void onMessage(Message message) {
		try {
			LOG.info("On Message " + message.getJMSMessageID());
			if (message instanceof SQSObjectMessage) {
				SQSObjectMessage object = (SQSObjectMessage) message;
				if (object.getObject() instanceof String) {
					// message.acknowledge();
					String response = (String) object.getObject();
					Activity event = JSONSerializer.toJSON(response, Activity.class);
					if (processMessage(event)) {
						message.acknowledge();
					}
				}
			}
			if (message instanceof SQSTextMessage) {
				SQSTextMessage object = (SQSTextMessage) message;
				if (object.getText() instanceof String) {
					String response = (String) object.getText();
					Activity event = JSONSerializer.toJSON(response, Activity.class);
					if (processMessage(event)) {
						message.acknowledge();
					}
				}
			}
		} catch (JMSException e) {
			LOG.error("Error while saving the message", e);
			try {
				LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
				errorQueue.send(message);
			} catch (JMSException e1) {
				LOG.error("Error while saving the message to error queue", e);
			}
			return;
		}
	}

	private boolean processMessage(Activity event) {
		LOG.info("Got Message " + event.toString());
		try {
			if ("RELOAD_CACHE".equals(event.getKey())) {
				metaService.reloadCache();
			}
		} catch (Exception ex) {
			NeoUtil.slackIt(env.getEnvType(), ex);
		}
		return true;
	}
}
