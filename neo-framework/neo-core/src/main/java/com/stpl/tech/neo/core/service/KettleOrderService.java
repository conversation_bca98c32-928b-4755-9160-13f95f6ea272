package com.stpl.tech.neo.core.service;

import java.net.URISyntaxException;
import java.util.List;

import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.Receipt;

public interface KettleOrderService {

	public Order sendOrder(Order order) throws URISyntaxException;

	public void publishCurrentOrderStatus(List<String> l) throws URISyntaxException;

	Receipt getOrderReceipt(String orderId) throws Exception;

}
