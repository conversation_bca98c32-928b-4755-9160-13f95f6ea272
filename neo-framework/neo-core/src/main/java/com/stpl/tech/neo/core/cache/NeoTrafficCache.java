package com.stpl.tech.neo.core.cache;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.neo.core.notification.slack.Slack;
import com.stpl.tech.neo.core.notification.slack.SlackConstants;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.domain.model.RequestClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class NeoTrafficCache {

	@Autowired
	EnvironmentProperties env;

	private final long longDelay = 900000;
	private final long shortDelay = 30000;
	// for 15 minute delays
	private final Map<String, RequestClient> longDelayPool = new HashMap<>();
	// for 30 second delays
	private final Map<String, RequestClient> shortDelayPool = new HashMap<>();

	public boolean allowRequest(String key) {
		boolean b = false;
		RequestClient c = shortDelayPool.get(key);
		if (c == null) {
			c = longDelayPool.get(key);
			if (c != null && c.getRequestCount() > 3) {
				b = false;
			} else {
				updateShortDelay(key);
				b = true;
			}
		}
		updateLongDelay(key);
		return b;
	}

	public int timeLeftInSeconds(String key) {
		RequestClient c = longDelayPool.get(key);
		if (c == null) {
			c = shortDelayPool.get(key);
		}
		if (c != null) {
			return new Long((c.getEndTime() - System.currentTimeMillis()) / 1000).intValue();
		}
		return 0;
	}

	private void updateShortDelay(String key) {
		addToPool(key, shortDelayPool, shortDelay);
	}

	private void updateLongDelay(String key) {
		addToPool(key, longDelayPool, longDelay);
	}

	private void addToPool(String key, Map<String, RequestClient> pool, long delay) {
		RequestClient c = pool.get(key);
		if (c != null) {
			c.setRequestCount(c.getRequestCount() + 1);
			if (c.getRequestCount() > 10) {
				slackIt(c);
			}
		} else {
			pool.put(key, new RequestClient(key, 1, delay));
		}
	}

	private void slackIt(RequestClient c) {
		String text = SlackConstants.EMOJI_SKULL_AND_CROSS + " Too many OTP request from client\nContact: "
				+ c.getKey();
//		env, "Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(env), text
		SlackNotificationService.getInstance().sendNotification(env.getEnvType(),"Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(env.getEnvType()), text);
	}

	@Scheduled(fixedRate = 10000)
	public void removeRequestClients() {
		removeFromPool(longDelayPool);
		removeFromPool(shortDelayPool);
	}

	private void removeFromPool(Map<String, RequestClient> pool) {
		List<String> l = getExpired(pool);
		l.forEach(p -> pool.remove(p));
	}

	private List<String> getExpired(Map<String, RequestClient> pool) {
		List<String> l = new ArrayList<>();
		long time = System.currentTimeMillis();
		for (RequestClient c : pool.values()) {
			if (c.isExpired(time)) {
				l.add(c.getKey());
			}
		}
		return l;
	}
}
