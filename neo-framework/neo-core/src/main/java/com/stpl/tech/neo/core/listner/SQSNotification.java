/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.neo.core.listner;

import com.amazon.sqs.javamessaging.ProviderConfiguration;
import com.amazon.sqs.javamessaging.SQSConnection;
import com.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazon.sqs.javamessaging.SQSMessageConsumer;
import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.stpl.tech.util.notification.MyAWSCredentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Queue;
import javax.jms.Session;
import java.util.HashMap;
import java.util.Map;

public class SQSNotification {

    private static final Logger LOG = LoggerFactory.getLogger(SQSNotification.class);

    private static SQSNotification INSTANCE;
    static {
        try {
            INSTANCE = new SQSNotification();
        } catch (JMSException e) {
            LOG.error("Failed to Initialize SQS ", e);
        }
    }

    private final Map<Regions, SQSConnection> sqsConnections = new HashMap<>();

    private SQSNotification() throws JMSException {

    }

    public static SQSNotification getInstance() throws JMSException {

        return INSTANCE;
    }

    public Queue getQueue(SQSSession session, String env, String appIdentifier, String queueNameSuffix)
            throws JMSException {
        String queueName = env + getAppIndentifier(appIdentifier) + queueNameSuffix;
        return session.createQueue(queueName);
    }

    private String getAppIndentifier(String appIdentifier) {
        return appIdentifier == null || appIdentifier.trim().length() == 0 ? "" : "_" + appIdentifier;
    }

    public SQSSession getSession(Regions region) throws JMSException {
        return (SQSSession) sqsConnection(region).createSession(false, Session.AUTO_ACKNOWLEDGE);
    }

    private SQSConnection sqsConnection(Regions region) throws JMSException {
        if (!sqsConnections.containsKey(region)) {
            ClientConfiguration configuration = new ClientConfiguration();
            configuration.setMaxErrorRetry(3);
            configuration.setConnectionTimeout(0);
            configuration.setSocketTimeout(0);
            configuration.setProtocol(Protocol.HTTPS);
            /*SQSConnection sqs = SQSConnectionFactory.builder().withClientConfiguration(configuration)
                    .withAWSCredentialsProvider(new AWSCredentialsProvider() {

                        private AWSCredentials credentials = new MyAWSCredentials();

                        @Override
                        public void refresh() {

                        }

                        @Override
                        public AWSCredentials getCredentials() {
                            return credentials;
                        }
                    }).withRegion(Region.getRegion(region)).withNumberOfMessagesToPrefetch(10).build()
                    .createConnection();*/
            SQSConnectionFactory sqsConnectionFactory = new SQSConnectionFactory(new ProviderConfiguration().withNumberOfMessagesToPrefetch(10),
                    AmazonSQSClientBuilder.standard().withClientConfiguration(configuration).withRegion(region).withCredentials(new MyAWSCredentials()));
            SQSConnection sqsConnection = sqsConnectionFactory.createConnection();
            sqsConnections.put(region, sqsConnection);
        }
        return sqsConnections.get(region);
    }

    public MessageConsumer getConsumer(SQSSession session, String env, String appIdentifier, String queueNameSuffix)
            throws JMSException {
        return (SQSMessageConsumer) session.createConsumer(getQueue(session, env, appIdentifier, queueNameSuffix));
    }

    public MessageProducer getProducer(SQSSession session, String env, String appIdentifier, String queueNameSuffix)
            throws JMSException {
        return session.createProducer(getQueue(session, env, appIdentifier, queueNameSuffix));
    }

    public SQSConnection getSqs(Regions region) {
        try {
            return sqsConnection(region);
        } catch (JMSException e) {
            return null;
        }
    }

}
