package com.stpl.tech.neo.core.dao;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.neo.domain.model.mongo.Customer;

/**
 * Created by Chaayos on 18-10-2016.
 */
@Repository
public interface CustomerDao extends MongoRepository<Customer, String> {

	public Customer findByContactNumber(String contactNumber);

}
