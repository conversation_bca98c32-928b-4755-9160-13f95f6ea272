package com.stpl.tech.neo.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.neo.core.cache.NeoTrafficCache;
import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.neo.core.service.CRMService;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.util.KettleCrmClientEndpoints;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.core.util.TruecallerServiceEndpoints;
import com.stpl.tech.neo.domain.model.CustomerInfoResponse;
import com.stpl.tech.neo.domain.model.LaunchOfferData;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.mongo.Address;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.CustomerAddress;
import com.stpl.tech.neo.domain.model.mongo.CustomerCurrentAddresses;
import com.stpl.tech.neo.domain.model.mongo.CustomerData;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.neo.domain.model.mongo.SessionDetail;
import com.stpl.tech.neo.domain.model.mongo.TrueCallerVerifiedProfile;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.IdCodeName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
public class CRMServiceImpl implements CRMService {

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private NeoTrafficCache requestCache;

	@Autowired
	private MongoTemplate mongoTemplate;

	@Autowired
	private CustomerDao customerDao;

	private static final Logger LOG = LoggerFactory.getLogger(CRMServiceImpl.class);

	@Override
	public Customer lookup(String contact) {
		return WebServiceHelper.postWithAuth(getCustomerSearchEndPoint(), props.getNeoClientToken(), contact,
				Customer.class);
	}

	@Override
	public Customer getCustomerByContact(String contact) {
		try {
			Customer customer = WebServiceHelper.postWithAuth(getCustomerByContactInfoEndPoint(), props.getNeoClientToken(), contact,
					Customer.class);
			LOG.info("Customer info by contact ::{}", contact, new Gson().toJson(customer));
			return customer;
		}catch(Exception e){
			LOG.error("Exception while getting customer info for contact :::{} from crm resource", contact, e.getMessage());
			return null;
		}
	}

	@Override
	public boolean oldCustomerLookUp(String contact){
		return WebServiceHelper.postWithAuth(getOldCustomerSearchEndPoint(), props.getNeoClientToken(), contact,
				Boolean.class);
	}

	@Override
	public boolean hasOrdersForCafe(String contact){
		return WebServiceHelper.postWithAuth(getHasOrdersForCafeEndPoint(), props.getNeoClientToken(), contact,
				Boolean.class);
	}

	@Override
	public boolean hasOrdersForDelivery(String contact){
		return WebServiceHelper.postWithAuth(getHasOrdersForDeliveryEndPoint(), props.getNeoClientToken(), contact,
				Boolean.class);
	}

	@Override
	public Boolean sendSMS(SMSRequest request) {
		return WebServiceHelper.postWithAuth(getSendSMSEndPoint(), props.getNeoClientToken(), request,
				Boolean.class);
	}

	@Override
	public IdCodeName getCustomerUniqueCoupon(String contact) {
		return WebServiceHelper.postWithAuth(getUniqueCouponPoint(),
				props.getNeoClientToken(), contact, IdCodeName.class);
	}

	@Override
	public List<String> getInternalCustomers() {
		return WebServiceHelper.postWithAuth(getInternalCustomersEndPoint(), props.getNeoClientToken(), null,
				List.class);
	}

	@Override
	public boolean sendOTP(String contact) {
		if (!props.getSendOtp()) {
			return true;
		}
		if (requestCache.allowRequest(contact)) {
			return WebServiceHelper.postWithAuth(getSendOtpEndPoint(), props.getNeoClientToken(), contact,
					Boolean.class);
		}
		return false;
	}

	@Override
	public boolean verifyOTP(String contact, String otp) {
		if (!props.getSendOtp()) {
			return WebOrderingConstants.DEFAULT_OTP.equals(otp);
		}
		CustomerData data = new CustomerData();
		data.setContact(contact);
		data.setOtp(otp);
		return WebServiceHelper.postWithAuth(getVerifyOtpEndPoint(), props.getNeoClientToken(), data, Boolean.class);
	}

	@Override
	public Customer signup(String contact, String name, String email, Integer tcId) {
		Customer customer = new Customer();
		customer.setContactNumber(contact);
		customer.setFirstName(name);
		customer.setEmailId(email);
		customer.setTcId(tcId);
		return WebServiceHelper.postWithAuth(getCustomerSignUpEndPoint(), props.getNeoClientToken(), customer,
				Customer.class);
	}

	@Override
	public Customer signup(CustomerData request) {
		Customer customer = new Customer();
		customer.setContactNumber(request.getContact());
		customer.setFirstName(request.getName());
		customer.setEmailId(request.getEmail());
		customer.setTcId(request.getTcId());
		if(request.getAcquisitionSource() != null) {
			customer.setAcquisitionSource(request.getAcquisitionSource());
		}
		if(request.getAcquisitionToken() != null) {
			customer.setAcquisitionToken(request.getAcquisitionToken());
		}
		if(request.getRegistrationUnitId() != null) {
			customer.setRegistrationUnitId(request.getRegistrationUnitId());
		}
		if(request.getAcquisitionBrandId() != null) {
			customer.setAcquisitionSource(request.getAcquisitionSource());
		}
		return WebServiceHelper.postWithAuth(getCustomerSignUpEndPoint(), props.getNeoClientToken(), customer,
				Customer.class);
	}

	@Override
	public Address addCustomerAddress(CustomerAddress customerAddress) {
		return WebServiceHelper.postWithAuth(getAddCustomerAddressEndPoint(), props.getNeoClientToken(),
				customerAddress, Address.class);
	}

	@Override
	public boolean update(String contact, String name, String emailId) {
		Customer data = new Customer();
		data.setContactNumber(contact);
		data.setFirstName(name);
		data.setEmailId(emailId);
		return WebServiceHelper.postWithAuth(getUpdateCustomerEndPoint(), props.getNeoClientToken(), data,
				Boolean.class);
	}

	@Override
	public OTPResponse resendOTP(String contact) {

		OTPResponse r = new OTPResponse();

		if (!props.getSendOtp()) {
			r.setSuccess(true);
		}

		if (requestCache.allowRequest(contact)) {
			LOG.info("Request allowed, sending the OTP");
			CustomerData data = new CustomerData();
			data.setContact(contact);
			r.setSuccess(WebServiceHelper.postWithAuth(getResendOtpEndPoint(), props.getNeoClientToken(), data,
					Boolean.class));
		} else {
			LOG.info("Blocked and made to wait for a while, before resent otp");
			r.setSec(requestCache.timeLeftInSeconds(contact));
			r.setSuccess(false);
		}
		return r;
	}

	@Override
	public List<Address> getNewCustomerAddress(CustomerCurrentAddresses addresses) {
		List<?> list = WebServiceHelper.postWithAuth(getNewCustomerAddressEndPoint(), props.getNeoClientToken(),
				addresses, List.class);
		List<Address> l = new ArrayList<>();
		// done to remove _id field from address which f# ^ the mongodb
		for (Object o : list) {
			l.add(NeoUtil.clone(o, Address.class));
		}
		return l;
	}



	private String getUpdateCustomerEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.UPDATE_CUSTOMER;
	}

	private String getAddCustomerAddressEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.ADD_ADDRESS;
	}

	private String getLaunchOfferEndPoint() {
		return props.getMasterServiceBasePath() + KettleServiceClientEndpoints.LAUNCH_OFFER;
	}

	private String getFreeItemOfferEndPoint() {
		return props.getMasterServiceBasePath() + KettleServiceClientEndpoints.FREE_ITEM_OFFER;
	}

	private String getGenerateTokenEndPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GENERATE_TOKEN;
	}

	private String getNewCustomerAddressEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.NEW_ADDRESS;
	}

	private String getCustomerSignUpEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SIGNUP_CUSTOMER;
	}

	private String getCustomerSearchEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SEARCH_CUSTOMER;
	}

	private String getCustomerByContactInfoEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.CUSTOMER_BY_CONTACT;
	}

	private String getOldCustomerSearchEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.OLD_SEARCH_CUSTOMER;
	}

	private String getHasOrdersForCafeEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.HAS_ORDERS_FOR_CAFE;
	}

	private String getHasOrdersForDeliveryEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.HAS_ORDERS_FOR_DELIVERY;
	}

	private String getSendSMSEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SEND_SMS;
	}

	private String getUniqueCouponPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.COUPON_SIGNUP;
	}

	private String getFormsEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.NPS_SUBMIT;
	}

	private String getSendOtpEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SEND_OTP;
	}

	private String getInternalCustomersEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.GET_INTERNAL_CUSTOMERS;
	}

	private String getVerifyOtpEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.VERIFY_OTP;
	}

	private String getResendOtpEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.RESEND_OTP;
	}

	private String getSendSignUpCouponEndPoint() {
		return props.getKettleCRMBasePath() + KettleCrmClientEndpoints.SIGNUP_COUPON;
	}

	private String getTruecallerProfileData(){
		return props.getTruecallerBasePath() + TruecallerServiceEndpoints.GET_PROFILE;
	}

	@Override
	public boolean markAllInternalCustomers() {
		try {
			List<String> contacts = getInternalCustomers();
			Query query = new Query();
			query.addCriteria(
					Criteria.where("contactNumber").in(contacts).andOperator(Criteria.where("internal").is(false)));
			Update update = new Update();
			update.set("internal", true);
			mongoTemplate.updateMulti(query, update, Customer.class);
			Query query1 = new Query();
			query1.addCriteria(
					Criteria.where("contactNumber").in(contacts).andOperator(Criteria.where("internal").exists(false)));
			Update update1 = new Update();
			update1.set("internal", true);
			mongoTemplate.updateMulti(query1, update1, Customer.class);

			Query query3 = new Query();
			query3.addCriteria(Criteria.where("contactNumber").in(contacts));
			Update update3 = new Update();
			update3.set("status", "LOGOUT");
			mongoTemplate.updateMulti(query3, update3, SessionDetail.class);

			Query query2 = new Query();
			query2.addCriteria(
					Criteria.where("contactNumber").nin(contacts).andOperator(Criteria.where("internal").is(true)));
			List<Customer> customers1 = mongoTemplate.find(query2, Customer.class);
			if (customers1 != null && customers1.size() > 0) {
				for (Customer customer : customers1) {
					customer.setInternal(false);
					customerDao.save(customer);
				}
			}
			return true;
		} catch (Exception e) {
			LOG.error("Unable to execute marking of internal customers", e);
		}
		return false;
	}

	@Override
	public Integer submitNPS(Object formEvent) {
		return WebServiceHelper.postWithAuth(getInternalCustomersEndPoint(), props.getNeoClientToken(), formEvent,
				Integer.class);
	}

	@Override
	public LaunchOfferData cafeLaunchOffer(LaunchOfferData input) {
		return WebServiceHelper.postWithAuth(getLaunchOfferEndPoint(), props.getNeoClientToken(), input,
				LaunchOfferData.class);
	}

	@Override
	public LaunchOfferData cafeFreeItemOffer(LaunchOfferData input) {
		return WebServiceHelper.postWithAuth(getFreeItemOfferEndPoint(), props.getNeoClientToken(), input,
				LaunchOfferData.class);
	}

	@Override
	public String generateToken(String input) {
		return WebServiceHelper.postWithAuth(getGenerateTokenEndPoint(), props.getNeoClientToken(), input,
				String.class);
	}

	@Override
	public boolean sendSignUpCoupon(CustomerInfoResponse customerInfoResponse) {
		return WebServiceHelper.postWithAuth(getSendSignUpCouponEndPoint(), props.getNeoClientToken(), customerInfoResponse,
				Boolean.class);
	}

	@Override
	public boolean verifyTruecallerLogin(String truecallerRequestId, String contact) throws URISyntaxException {
		if(Objects.isNull(truecallerRequestId)){
			return  false;
		}
		Map<String,String> uriVariable = new HashMap<>();
		uriVariable.put("requestId",truecallerRequestId);
		TrueCallerVerifiedProfile profiles = WebServiceHelper.exchangeWithAuth(getTruecallerProfileData(), props.getNeoClientToken(),
				HttpMethod.GET, TrueCallerVerifiedProfile.class, null, uriVariable);
		if(Objects.nonNull(profiles) && Objects.nonNull(profiles.getContact()) && profiles.getContact().equals(contact)){
			return true;
		}
		return false;
	}

}