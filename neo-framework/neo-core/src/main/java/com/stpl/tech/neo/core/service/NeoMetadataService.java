package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.core.exception.NeoMetadataException;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.redis.domain.model.ProductRecipeKey;
import com.stpl.tech.redis.domain.model.ProductRecipes;
import com.stpl.tech.redis.domain.model.RecipeDetail;
import com.stpl.tech.redis.domain.model.Unit;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.redis.domain.model.UnitBusinessHours;
import com.stpl.tech.redis.domain.model.UnitDataVO;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface NeoMetadataService {

    public Map<String, Integer> getCities();

    public List<String> getLocalitiesForCity(String city);

    public String getDefaultLocalityForCity(String city);

    public UnitDataVO getDeliveryUnit(UnitLocalityMappingVO mapping) throws NeoMetadataException;

    public List<ProductRecipes> getProductRecipes(List<ProductRecipeKey> products) throws URISyntaxException;

    public List<IdName> getTakeAwayUnits(UnitLocalityMappingVO mapping);

    public List<IdName> getWebCategories();

    public Unit getDeliveryUnit(int unitId);

    public List<IdName> getTakeAwayUnitsForCity(String city) throws URISyntaxException;

    public Set<String> getAllTags();

    public List<String> getTagData(String tagName);

    public BigDecimal getTakeAwayUnitProductPrice(int unitId, int productId, String dimension) throws NeoMetadataException;

    public BigDecimal getDeliveryUnitProductPrice(String region, int productId, String dimension);

    public UnitBasicDetail getUnitBasicDetail(int unitId);

    public void reloadCache() throws URISyntaxException;

    void reloadRecipeCache() throws URISyntaxException;

    public UnitLocalityMapping getUnitLocalityMapping(String city, String locality);

    public RecipeDetail getProductRecipe(int pId, String dimension, Integer unitId) throws URISyntaxException;

    public Unit getUnit(int unitId);

    public UnitBusinessHours getBusinessHours(int unitId);

    public Map<String, List<String>> getProductTagData();

    public Map<String, List<String>> getMenuTagData();

    Map<Integer, String> getProductImages(List<Integer> productIds) throws URISyntaxException;
}
