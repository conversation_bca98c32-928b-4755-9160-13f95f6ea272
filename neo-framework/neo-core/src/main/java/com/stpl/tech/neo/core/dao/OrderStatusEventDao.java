package com.stpl.tech.neo.core.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.neo.domain.model.mongo.OrderStatusEvent;

/**
 * Created by Chaayos on 18-10-2016.
 */
@Repository
public interface OrderStatusEventDao extends MongoRepository<OrderStatusEvent, String> {

	List<OrderStatusEvent> findByOrderIdAndTransitionStatusOrderByUpdateTimeDesc(String orderId, String success);

}
