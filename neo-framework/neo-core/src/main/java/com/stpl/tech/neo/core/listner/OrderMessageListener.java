package com.stpl.tech.neo.core.listner;

import java.util.List;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.OrderStatusEventDao;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.OrderStatus;
import com.stpl.tech.neo.domain.model.mongo.OrderStatusEvent;
import com.stpl.tech.util.JSONSerializer;

public class OrderMessageListener implements MessageListener {

	private static final Logger LOG = LoggerFactory.getLogger(OrderMessageListener.class);

	private OrderStatusEventDao eventDao;
	private CartDao cartDao;
	private MessageProducer errorQueue;

	public OrderMessageListener(MessageProducer errorQueue, OrderStatusEventDao eventDao, CartDao cartDao) {
		this.errorQueue = errorQueue;
		this.eventDao = eventDao;
		this.cartDao = cartDao;
	}

	@Override
	public void onMessage(Message message) {
		try {
			LOG.info("On Message " + message.getJMSMessageID());
			if (message instanceof SQSObjectMessage) {
				SQSObjectMessage object = (SQSObjectMessage) message;
				if (object.getObject() instanceof String) {
					message.acknowledge();
					String response = (String) object.getObject();
					OrderStatusEvent event = JSONSerializer.toJSON(response, OrderStatusEvent.class);
					if (processMessage(event)) {
						message.acknowledge();
					}
				}
			}
			if (message instanceof SQSTextMessage) {
				SQSTextMessage object = (SQSTextMessage) message;
				if (object.getText() instanceof String) {
					message.acknowledge();
					String response = (String) object.getText();
					OrderStatusEvent event = JSONSerializer.toJSON(response, OrderStatusEvent.class);
					if (processMessage(event)) {
						message.acknowledge();
					}
				}
			}
		} catch (JMSException e) {
			LOG.error("Error while saving the message", e);
			try {
				LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
				errorQueue.send(message);
			} catch (JMSException e1) {
				LOG.error("Error while saving the message to error queue", e);
			}
			return;
		}
	}

	private boolean processMessage(OrderStatusEvent event) {
		LOG.info("Got Message " + event.toString());
		try {
			event = eventDao.save(event);
			CartDetail cart = cartDao.findByKettleOrderId(event.getOrderId());
			// we are making queries on large data set.
			// this might be bad.
			if (event != null && cart != null) {
				// find all events related to order
				List<OrderStatusEvent> events = eventDao.findByOrderIdAndTransitionStatusOrderByUpdateTimeDesc(
						event.getOrderId(), WebOrderingConstants.SUCCESS);
				// get event with max updated event
				OrderStatusEvent lastEvent = events.get(0);
				// put that status to order in cart
				cart.getOrderDetail().setStatus(OrderStatus.valueOf(lastEvent.getToStatus()));
				cartDao.save(cart);
				// save! status is updated
				return true;
			}
		} catch (Exception e) {
			LOG.error("Error in message processing", e);
		}
		return false;
	}
}
