package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.core.exception.CheckoutFailureException;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.domain.model.AGSPaymentResponse;
import com.stpl.tech.neo.domain.model.CartCheckoutRequest;
import com.stpl.tech.neo.domain.model.EzetapPaymentResponse;
import com.stpl.tech.neo.domain.model.PaymentStatusChangeRequest;
import com.stpl.tech.neo.domain.model.PaymentVO;
import com.stpl.tech.neo.domain.model.RazorPayPaymentResponse;
import com.stpl.tech.neo.domain.model.mongo.AGSCreateRequest;
import com.stpl.tech.neo.domain.model.mongo.AGSPaymentCMResponse;
import com.stpl.tech.neo.domain.model.mongo.EzetapCreateRequest;
import com.stpl.tech.neo.domain.model.mongo.PaytmCreateRequest;
import com.stpl.tech.neo.domain.model.mongo.PaytmPaymentStatus;
import com.stpl.tech.neo.domain.model.mongo.RazorPayCreateRequest;
import org.codehaus.jettison.json.JSONException;

import java.net.URISyntaxException;

public interface WebPaymentService {

	@Deprecated
	public Object createOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException;

	public PaytmCreateRequest createPaytmOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws WebOrderException, CheckoutFailureException, URISyntaxException;

	public RazorPayCreateRequest createRazorPayOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws WebOrderException, CheckoutFailureException, URISyntaxException;

    boolean processEzetapPayment(EzetapPaymentResponse request);

    public void stampPaymentUpdate(Object data) throws URISyntaxException, WebOrderException;

	public boolean processPayment(RazorPayPaymentResponse request);
	
	public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws URISyntaxException, JSONException;

	EzetapCreateRequest createEzetapOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
            throws WebOrderException, CheckoutFailureException, URISyntaxException;

    boolean cancelPayment(PaymentStatusChangeRequest request) throws URISyntaxException;

	public boolean paymentFailure(PaymentStatusChangeRequest request) throws URISyntaxException;

	public boolean getPaymentStatus(PaymentVO vo) throws URISyntaxException;

	PaytmCreateRequest createPayTMQRCodeIdForKIOSK(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException;

    PaytmPaymentStatus isPaytmPaymentCompleted(CartCheckoutRequest request, String kioskService)
			throws URISyntaxException, WebOrderException, CheckoutFailureException;

	AGSCreateRequest createAGSOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws WebOrderException, CheckoutFailureException, URISyntaxException;

	boolean processAGSPayment(AGSPaymentResponse request);

    AGSPaymentCMResponse isAGSPaymentCompleted(String externalOrderId);

	PaytmCreateRequest createPayTMUPIForKIOSK(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException;

	void stampPaymentUpdateForCovid(Object request) throws URISyntaxException, WebOrderException;
}
