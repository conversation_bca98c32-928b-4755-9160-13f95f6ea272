package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.dao.OfferDetailDao;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoCustomerService;
import com.stpl.tech.neo.core.service.NeoOfferService;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.core.util.WebErrorCodes;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.domain.model.CampaignDetailResponse;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.OfferDetail;
import com.stpl.tech.neo.domain.model.mongo.SignUpOfferRequest;
import com.stpl.tech.neo.domain.model.mongo.SignUpTimeSlotCount;
import com.stpl.tech.neo.domain.model.mongo.WebOfferOrder;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class NeoOfferServiceImpl extends AbstractDataValidator implements NeoOfferService {

    private static final Logger LOG = LoggerFactory.getLogger(NeoOfferServiceImpl.class);

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private NeoCustomerService customerService;

    @Autowired
    private OfferDetailDao offerDetailDao;

    @Override
    public WebOfferOrder applyOffer(WebOfferOrder woo) throws WebOrderException {
        try {
            if (AppUtils.isChaayosCashOffer(woo.getCouponCode())) {
                woo.setError(true);
                woo.setErrorCode(107);
                woo.setErrorMessage("Coupon Code not found");
                return woo;
            }
            Customer c = customerService.getCustomer(woo.getOrder().getWebCustomerId());
            if (c != null) {
                woo.getOrder().setCustomerId(c.getId());
            } else {
                woo.getOrder().setCustomerId(null);
            }
            String serviceName = woo.getOrder().getExternalOrderId().substring(0, 2)
                    .equalsIgnoreCase(WebOrderingConstants.KIOSK_ORDER) ? WebOrderingConstants.KIOSK_SERVICE
                    : WebOrderingConstants.NEO_SERVICE;
            updateOrderMetadata(woo.getOrder(), null, serviceName);
            WebOfferOrder updatedOrder = applyOfferViaKettle(woo);
            if (updatedOrder != null && updatedOrder.getOrder() != null) {
                // this is not required but check before removing
                updatedOrder.getOrder().setHasParcel(woo.getOrder().isHasParcel());
            }
            return updatedOrder;
        } catch (Exception e) {
            LOG.error("ERROR in applying Offer", e);
            throw new WebOrderException(WebErrorCodes.OFFER_FAILED);
        }
    }

    private WebOfferOrder applyOfferViaKettle(WebOfferOrder woo) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.APPLY_OFFER;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, WebOfferOrder.class,
                JSONSerializer.toJSON(woo), null);
    }

    @Override
    public WebOfferOrder applyChaayosCashViaKettle(WebOfferOrder woo) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.APPLY_CHAAYOS_CASH;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, WebOfferOrder.class,
                JSONSerializer.toJSON(woo), null);
    }

    @Override
    protected NeoCustomerService getCustomerService() {
        return customerService;
    }

    @Override
    public List<OfferDetail> getOfferDetails(boolean status){
        LOG.info("Enter getOfferDetails where status is "+ status);
        return offerDetailDao.findAllByStatus(status);
    }

    @Override
    public boolean availSignUpOfferViaKettle(SignUpOfferRequest request) throws URISyntaxException  {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.AVAIL_SIGN_UP_OFFER;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Boolean.class,
                JSONSerializer.toJSON(request), null);
    }

    @Override
    public boolean checkIfSignUpOfferAvailedViaKettle(String customerContact) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CHECK_IF_SIGN_UP_OFFER_AVAILED;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Boolean.class,
                customerContact, null);
    }

    @Override
    public SignUpTimeSlotCount getTimeSlotCounts(String dateOfDelivery) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_TIMESLOT_COUNTS;
        String token = env.getNeoClientToken();
        return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, SignUpTimeSlotCount.class,
                dateOfDelivery, null);
    }

    @Override
    public CampaignDetailResponse getCampaignDetail(String token) throws URISyntaxException {
        String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_CAMPAIGN_DETAIL;
        String authToken = env.getNeoClientToken();
        Map<String,String> uriVariable = new HashMap<>();
        uriVariable.put("campaignToken",token);
        uriVariable.put("status","ACTIVE");
        CampaignDetailResponse campaignDetailResponse = WebServiceHelper.exchangeWithAuth(endpoint, authToken, HttpMethod.GET,CampaignDetailResponse.class, null, uriVariable, MediaType.APPLICATION_JSON_UTF8);
        return campaignDetailResponse;
    }

}
