package com.stpl.tech.neo.core.cache;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.util.EncriptionHelper;
import com.stpl.tech.neo.domain.model.CustomerLoginRequest;
import com.stpl.tech.neo.domain.model.LoginToken;
import com.stpl.tech.neo.domain.model.mongo.CustomerInfo;
import com.stpl.tech.util.AppUtils;

@Component
public class NeoLoginTokenCache {

	private static final Logger LOG = LoggerFactory.getLogger(NeoLoginTokenCache.class);

	@Autowired
	EnvironmentProperties env;

	private final long storageTime = 1200000;
	// for 2 minute Storage
	private final Map<String, LoginToken> tokenPool = new HashMap<>();
	private Map<String,Date> gameLoginToken = new HashMap<>();

	public LoginToken getTokenData(String key) {
		LoginToken c = null;
		try {
			String secret = EncriptionHelper.decryptUrlCodec(key);
			c = tokenPool.get(secret);
			if (c != null) {
				tokenPool.remove(secret);
			}
		} catch (Exception e) {
			LOG.error("Token check failed", e);
		}
		return c;
	}

	public String generateToken(CustomerInfo info, Integer unitId, Integer terminal) {
		try {
			String token = AppUtils.generateRandomAlphaNumericCode(20);
			CustomerLoginRequest req = new CustomerLoginRequest(info, unitId, terminal);
			tokenPool.put(token, new LoginToken(token, req, storageTime));
			return EncriptionHelper.encryptUrlCodec(token);
		} catch (Exception e) {
			LOG.error("Token generation failed", e);
		}
		return "";
	}

	/*
	 * private void slackIt(RequestClient c) { String text =
	 * SlackConstants.EMOJI_SKULL_AND_CROSS +
	 * " Too many OTP request from client\nContact: " + c.getKey();
	 * SlackNotificationService.getInstance().send(env.getEnvType(), text); }
	 */

	// every 10 seconds
	@Scheduled(fixedRate = 10000)
	public void removeRequestClients() {
		removeFromPool(tokenPool);
	}

	@Scheduled(fixedRate = 100000)
	public void removeAllGameLoginToken() {
		Map<String,Date> newData = new HashMap<>();
		for(Map.Entry<String, Date> entry : gameLoginToken.entrySet()){
			if(AppUtils.getTimeDiffernceInMinutes(AppUtils.getCurrentDate(), entry.getValue()) < 2){
				newData.put(entry.getKey(),entry.getValue());
			}
		}
		gameLoginToken=newData;
	}

	public void removeGameLoginToken(String token){
		if(gameLoginToken.containsKey(token)){
			gameLoginToken.remove(token);
		}
	}

	public boolean getGameLoginToken(String token){
		if(gameLoginToken.containsKey(token)){
			return true;
		}
		return false;
	}

	public void setGameLoginToken(String token){
		gameLoginToken.put(token, AppUtils.getCurrentDate());
	}

	private void removeFromPool(Map<String, LoginToken> pool) {
		List<String> l = getExpired(pool);
		l.forEach(p -> pool.remove(p));
	}

	private List<String> getExpired(Map<String, LoginToken> pool) {
		List<String> l = new ArrayList<>();
		long time = System.currentTimeMillis();
		for (LoginToken c : pool.values()) {
			if (c.isExpired(time)) {
				l.add(c.getToken());
			}
		}
		return l;
	}

}
