package com.stpl.tech.neo.core.service.impl;

import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.neo.core.dao.DeviceDao;
import com.stpl.tech.neo.core.dao.SessionDao;
import com.stpl.tech.neo.core.service.BlankCartService;
import com.stpl.tech.neo.core.service.StampingService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartStatus;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.neo.domain.model.mongo.SessionDetail;
import com.stpl.tech.neo.domain.model.mongo.SessionStatus;

@Service
public class StampingServiceImpl implements StampingService {

	@Autowired
	private DeviceDao deviceDao;

	@Autowired
	private CartDao cartDao;

	@Autowired
	private SessionDao sessionDao;

	@Autowired
	private CustomerDao customerDao;

	@Autowired
    private BlankCartService blankCartService;

	@Override
	public DeviceVO registerDevice(DeviceDetail deviceDetail) {
		deviceDetail.setCreatedAt(NeoUtil.getCurrentTimestamp());
		deviceDetail.setLastAccessedAt(NeoUtil.getCurrentTimestamp());
		deviceDetail = deviceDao.save(deviceDetail);
		DeviceVO device = new DeviceVO();
		device.setDeviceKey(
				NeoUtil.getDeviceKey(deviceDetail.getDeviceId(), deviceDetail.getLastAccessedAt().getTime()));
		return stampDevice(device);
	}

	@Override
	public SessionDetail createSession(SessionDetail sessionDetail) {
		sessionDetail.setCreatedAt(NeoUtil.getCurrentTimestamp());
		sessionDetail = sessionDao.save(sessionDetail);
		return sessionDetail;
	}

	/**
	 * Stamping service is called when the application is opened to refresh
	 * device key session key and Cart Details. when no customer - cart belongs
	 * to device, if customer is logged in his cart is updated
	 *
	 * @param deviceVO
	 *
	 * @return new device-key, session-key and cart
	 */
	@Override
	public DeviceVO stampDevice(DeviceVO deviceVO) {

		if (deviceVO == null) {
			return null;
		}

		Optional<DeviceDetail> device = Optional.empty();
		Optional<SessionDetail> session = Optional.empty();

		String deviceId = NeoUtil.getDeviceIdFromDeviceKey(deviceVO.getDeviceKey());
		String sessionId = NeoUtil.getSessionIdFromSessionKey(deviceVO.getSessionKey());

		if (deviceId != null) {
			device = deviceDao.findById(deviceId);
		}

		if (sessionId != null) {
			session = sessionDao.findById(sessionId);
		}

		if (!device.isPresent()) {
			return null;
		}

		if (isValidDeviceKey(deviceVO, device.get())) {
			CartDetail cartDetail = null;
			if (isValidSessionKey(deviceVO, Objects.nonNull(session) && session.isPresent() ? session.get() : null)) {
				cartDetail = getCartFromCustomer(Objects.nonNull(session) && session.isPresent() ? session.get() : null);
				if (cartDetail != null && !CartStatus.SETTLED.equals(cartDetail.getCartStatus())) {
					deviceVO.setCartDetail(cartDetail);
				}
			} else {
				cartDetail = getCartFromDevice(Objects.nonNull(device) && device.isPresent() ? device.get() : null);
				if (cartDetail != null && !CartStatus.SETTLED.equals(cartDetail.getCartStatus())) {
					deviceVO.setCartDetail(cartDetail);
				}
				// session validation failure or session is null
				deviceVO.setSessionKey(null);
				// explicitly set session null to invalidate it
				// check if session status to be invalidated here
				session = null;
			}
			// stamp device and session only when both are valid
			deviceVO.setDeviceKey(getStampedDeviceKey(Objects.nonNull(device) && device.isPresent() ? device.get() : null));
			deviceVO.setSessionKey(getStampedSessionKey(Objects.nonNull(session) && session.isPresent() ? session.get() : null));
			if (deviceVO.getCartDetail() == null) {
				// creating blank cart for no cart
				String d = device == null ? null : device.get().getDeviceId();
				String s = session == null ? null : session.get().getSessionId();
				String c = session == null ? null : session.get().getCustomerId();
				deviceVO.setCartDetail(blankCartService.createBlankCart(c, d, s));
			}
		} else {
			return null;
		}
		return deviceVO;
	}

	/**
	 * check to validate device
	 * @param deviceVO
	 * @return boolean
	 */
	@Override
	public boolean verifyDevice(DeviceVO deviceVO) {
		if (deviceVO == null) {
			return false;
		}
		Optional<DeviceDetail> device = Optional.empty();
		String deviceId = NeoUtil.getDeviceIdFromDeviceKey(deviceVO.getDeviceKey());
		if (deviceId != null) {
			device = deviceDao.findById(deviceId);
		}
		if (!device.isPresent()) {
			return false;
		}
		return isValidDeviceKey(deviceVO, device.get());
	}

	private CartDetail getCartFromDevice(DeviceDetail device) {
		if (device.getLastCartId() != null) {
			Optional<CartDetail> cartDetail = cartDao.findById(device.getLastCartId());
			return cartDetail.orElse(null);
		}
		return null;
	}

	// BAD BAD BAD
	private CartDetail getCartFromCustomer(SessionDetail session) {
		if (session.getCustomerId() != null) {
			Optional<Customer> c = customerDao.findById(session.getCustomerId());
			if (c.isPresent() && c.get().getCartId() != null) {
				return cartDao.findById(c.get().getCartId()).orElse(null);
			}
		}
		return null;
	}

	private boolean isValidSessionKey(DeviceVO deviceVO, SessionDetail session) {
		return session != null && deviceVO.getSessionKey() != null
				&& deviceVO.getSessionKey().equals(NeoUtil.getSessionKey(session.getSessionId(), session.getDeviceId(),
						session.getLastAccessedAt().getTime(), session.getCustomerId(), session.getContactNumber()))
				&& isActive(session);
	}

	private boolean isActive(SessionDetail session) {
		return SessionStatus.ACTIVE.equals(session.getStatus());
	}

	private boolean isValidDeviceKey(DeviceVO deviceVO, DeviceDetail device) {
		return deviceVO != null && device != null && deviceVO.getDeviceKey()
				.equals(NeoUtil.getDeviceKey(device.getDeviceId(), device.getLastAccessedAt().getTime()));
	}

	private String getStampedSessionKey(SessionDetail sd) {
		if (sd != null) {
			sd = stampSession(sd);
			return NeoUtil.getSessionKey(sd.getSessionId(), sd.getDeviceId(), sd.getLastAccessedAt().getTime(),
					sd.getCustomerId(), sd.getContactNumber());
		}
		return null;
	}

	private String getStampedDeviceKey(DeviceDetail device) {
		if (device != null) {
			device.setLastAccessedAt(NeoUtil.getCurrentTimestamp());
			deviceDao.save(device);
			return NeoUtil.getDeviceKey(device.getDeviceId(), device.getLastAccessedAt().getTime());
		}
		return null;
	}

	@Override
	public CartDetail stampCart(CartDetail cartDetail) {
		cartDetail.setCreationTime(NeoUtil.getCurrentTimestamp());
		cartDetail = cartDao.save(cartDetail);
		return cartDetail;
	}

	@Override
	public SessionDetail stampSession(SessionDetail sessionDetail) {
		sessionDetail.setCreatedAt(NeoUtil.getCurrentTimestamp());
		sessionDetail = sessionDao.save(sessionDetail);
		return sessionDetail;
	}

	@Override
	public void updateCartInDevice(CartDetail c) {
		Optional<DeviceDetail> d = deviceDao.findById(c.getDeviceId());
		if (d.isPresent()) {
			d.get().setLastCartId(c.getCartId());
			deviceDao.save(d.get());
		}
	}

	@Override
	public void updateCartInCustomer(CartDetail cd) {
		if (cd.getCustomerId() != null) {
			Optional<Customer> c = customerDao.findById(cd.getCustomerId());
			if (c.isPresent()) {
				c.get().setCartId(cd.getCartId());
				customerDao.save(c.get());
			}
		}
	}

	@Override
	public void removeCartFromDevice(CartDetail c) {
		Optional<DeviceDetail> d = deviceDao.findById(c.getDeviceId());
		if (d.isPresent()) {
			d.get().setLastCartId(null);
			deviceDao.save(d.get());
		}
	}

	@Override
	public void addCartToDevice(CartDetail c) {
		Optional<DeviceDetail> d = deviceDao.findById(c.getDeviceId());
		if (d.isPresent()) {
			d.get().setLastCartId(c.getCartId());
			deviceDao.save(d.get());
		}
	}

}
