package com.stpl.tech.neo.core.cache;

import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.redis.domain.model.ProductBasicDetail;
import com.stpl.tech.redis.domain.model.ProductRecipeKey;
import com.stpl.tech.redis.domain.model.ProductRecipes;
import com.stpl.tech.redis.domain.model.Unit;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class NeoMetadataCache {

    private final Map<UnitLocalityMappingVO, UnitLocalityMapping> localityData = new HashMap<>();
    private final Map<String, List<String>> cityToLocalityMapping = new HashMap<>();
    private final Map<String, String> cityToUnitMapping = new HashMap<>();
    private final Map<String, Integer> cityToStateMapping = new HashMap<>();
    private final Map<UnitLocalityMappingVO, List<IdName>> takeAwayMappings = new HashMap<>();
    private final Map<String, List<IdName>> city2takeAwayUnitMappings = new HashMap<>();
    private final Map<Integer, Unit> deliveryUnitMappings = new HashMap<>();
    // key - unitId:ProductId:dimension
    private final Map<String, BigDecimal> takeAwayUnitProductPriceMapping = new HashMap<>();
    // key - region:ProductId:dimension
    private final Map<String, BigDecimal> deliveryUnitProductPriceMapping = new HashMap<>();

    private final List<IdName> webCategories = new ArrayList<>();
    private final Map<String, List<String>> webTags = new HashMap<>();
    private final Map<Integer, UnitBasicDetail> unitBasicDetails = new HashMap<>();

    private final Map<Integer, ProductRecipes> recipes = new HashMap<>();
    private final Map<Integer, ProductRecipes> completeRecipe = new HashMap<>();

    private final Map<String, List<String>> productTagData = new HashMap<>();
    private final Map<String, List<String>> menuTagData = new HashMap<>();

    private final Map<Integer, ProductBasicDetail> productBasicDetailMap = new HashMap<>();

    private final Map<Integer, Map<Integer, List<ProductRecipeKey>>> productPriceProfilesMap = new HashMap<>();

    public final Map<String, String> cartId = new HashMap<>();

    private final Map<Integer, String> productImages = new HashMap<>();

    public NeoMetadataCache() {
    }

    public Set<String> getCities() {
        return cityToLocalityMapping.keySet();
    }


    public List<String> getLocalitiesForCity(String city) {
        return cityToLocalityMapping.get(city);
    }

    public Map<String, List<String>> getCityToLocalityMapping() {
        return cityToLocalityMapping;
    }

    public String getLocalityForCity(String city) {
        return cityToUnitMapping.get(city);
    }

    public Map<String, String> getCityToUnitMapping() {
        return cityToUnitMapping;
    }

    public UnitLocalityMapping getUnitLocalityMapping(UnitLocalityMappingVO key) {
        return localityData.get(key);
    }

    public Map<UnitLocalityMappingVO, UnitLocalityMapping> getLocalityData() {
        return localityData;
    }

    public List<IdName> getTakeAwayUnits(UnitLocalityMappingVO key) {
        return takeAwayMappings.get(key);
    }

    public Map<UnitLocalityMappingVO, List<IdName>> getTakeAwayMappings() {
        return takeAwayMappings;
    }

    public Unit getDeliveryUnit(int unitId) {
        return deliveryUnitMappings.get(unitId);
    }

    public Map<Integer, Unit> getDeliveryUnitMappings() {
        return deliveryUnitMappings;
    }

    public List<IdName> getWebCategories() {
        return webCategories;
    }

    public Map<String, BigDecimal> getTakeAwayUnitProductPriceMapping() {
        return takeAwayUnitProductPriceMapping;
    }

    public BigDecimal getTakeAwayUnitProductPrice(int unitId, int pId, String dia) {
        return takeAwayUnitProductPriceMapping.get(getTakeAwayUnitProductPriceKey(unitId, pId, dia));
    }

    public String getTakeAwayUnitProductPriceKey(int unitId, int pId, String dia) {
        return unitId + ":" + pId + ":" + dia;
    }

    public Map<String, BigDecimal> getDeliveryUnitProductPriceMapping() {
        return deliveryUnitProductPriceMapping;
    }

    public BigDecimal getDeliveryUnitProductPrice(String region, int pId, String dia) {
        return deliveryUnitProductPriceMapping.get(getDeliveryUnitProductPriceKey(region, pId, dia));
    }

    public String getDeliveryUnitProductPriceKey(String region, int pId, String dia) {
        return region + ":" + pId + ":" + dia;
    }

    public List<IdName> getTakeAwayUnitsForCity(String city) {
        return city2takeAwayUnitMappings.get(city);
    }

    public Map<String, List<IdName>> getCity2takeAwayUnitMappings() {
        return city2takeAwayUnitMappings;
    }

    public Set<String> getAllTags() {
        return webTags.keySet();
    }

    public List<String> getTagData(String tagName) {
        return webTags.get(tagName);
    }

    public Map<String, List<String>> getWebTags() {
        return webTags;
    }

    public UnitBasicDetail getUnitBasicDetail(int id) {
        return unitBasicDetails.get(id);
    }

    public Map<Integer, UnitBasicDetail> getUnitBasicDetails() {
        return unitBasicDetails;
    }

    public ProductRecipes getRecipe(int productId) {
        return recipes.get(productId);
    }

    public Map<Integer, ProductRecipes> getRecipes() {
        return recipes;
    }

    public Map<Integer, ProductRecipes> getCompleteRecipe() {
        return completeRecipe;
    }

    public Map<String, List<String>> getProductTagData() {
        return productTagData;
    }

    public Map<String, List<String>> getMenuTagData() {
        return menuTagData;
    }

    public Map<String, Integer> getCityToStateMapping() {
        return cityToStateMapping;
    }

    public Map<Integer, ProductBasicDetail> getProductBasicDetailMap() {
        return productBasicDetailMap;
    }

    public Map<String, String> getCartId() {
        return cartId;
    }

    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> getProductPriceProfilesMap() {
        return productPriceProfilesMap;
    }

    public Map<Integer, String> getProductImages() {
        return productImages;
    }

    // call NeoMetadataCacheManagementServiceImpl
    // to clear data do not use this directly
    public void clearAll() {
        // add variable to clear here
        localityData.clear();
        cityToLocalityMapping.clear();
        cityToStateMapping.clear();
        takeAwayMappings.clear();
        city2takeAwayUnitMappings.clear();
        deliveryUnitMappings.clear();
        takeAwayUnitProductPriceMapping.clear();
        deliveryUnitProductPriceMapping.clear();
        webCategories.clear();
        webTags.clear();
        unitBasicDetails.clear();
        recipes.clear();
        completeRecipe.clear();
        productTagData.clear();
        menuTagData.clear();
        productBasicDetailMap.clear();
        productPriceProfilesMap.clear();
        productImages.clear();
        cartId.clear();
    }

}
