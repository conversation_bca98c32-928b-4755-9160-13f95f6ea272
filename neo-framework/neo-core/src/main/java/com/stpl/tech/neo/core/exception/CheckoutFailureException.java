/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.neo.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.redis.domain.model.ResponseCode;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Payment Failure Exception")
public class CheckoutFailureException extends Exception {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3610247704652230558L;
	
	private ResponseCode code;

	public CheckoutFailureException() {
	}

	public CheckoutFailureException(ResponseCode code) {
		super(code.getMsg());
		this.code = code;
	}

	public CheckoutFailureException(Throwable cause) {
		super(cause);
	}

	public CheckoutFailureException(ResponseCode code, Throwable cause) {
		super(code.getMsg(), cause);
	}

	public CheckoutFailureException(String message, Throwable cause, boolean enableSuppression,
			boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public ResponseCode getCode() {
		return code;
	}

}
