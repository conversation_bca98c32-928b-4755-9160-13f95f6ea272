package com.stpl.tech.neo.core.listner;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;

import com.amazonaws.regions.Regions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.amazon.sqs.javamessaging.SQSSession;
import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.OrderStatusEventDao;
import com.stpl.tech.neo.core.service.EnvironmentProperties;

@Service
public class SQSOrderMessageService {

    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private OrderStatusEventDao eventDao;
    @Autowired
    private CartDao cartDao;

    @PostConstruct
    public void init() throws JMSException {
        Regions region = Regions.valueOf(props.getAWSQueueRegion());
        SQSSession session = SQSNotification.getInstance().getSession(region);
        MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvType().name(),
                props.getApplicationIdentifier(), "_ORDER_STATUS_EVENTS");
        OrderMessageListener listener = new OrderMessageListener(producer, eventDao, cartDao);
        MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvType().name(),
                props.getApplicationIdentifier(), "_ORDER_STATUS_EVENTS");
        consumer.setMessageListener(listener);
        SQSNotification.getInstance().getSqs(region).start();
    }
}
