package com.stpl.tech.neo.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Authentication Failure")
public class AuthenticationFailureException extends Exception {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public AuthenticationFailureException() {
	}

	public AuthenticationFailureException(String message) {
		super(message);
	}

	public AuthenticationFailureException(Throwable cause) {
		super(cause);
	}

	public AuthenticationFailureException(String message, Throwable cause) {
		super(message, cause);
	}

	public AuthenticationFailureException(String message, Throwable cause, boolean enableSuppression,
			boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
}
