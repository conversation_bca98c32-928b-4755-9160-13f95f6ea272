package com.stpl.tech.neo.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;

import com.stpl.tech.neo.core.util.CustomRedisSerializer;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Configuration
@EnableRedisRepositories(basePackages = {"com.stpl.tech.redis.core.dao"})
@ComponentScan(basePackages = {"com.stpl.tech.neo.core.service", "com.stpl.tech.redis.core"})
public class NeoRedisConfig {

    public NeoRedisConfig() {
        super();
    }

	/*
	 * @Bean JedisConnectionFactory jedisConnectionFactory() {
	 * JedisConnectionFactory jedisConFactory = new JedisConnectionFactory();
	 * jedisConFactory.setHostName(env.getRedisHost());
	 * jedisConFactory.setPort(env.getRedisPort());
	 * jedisConFactory.setUsePool(true); jedisConFactory.setTimeout(100000); return
	 * jedisConFactory; }
	 * 
	 * @Bean public RedisTemplate<String, Object> redisTemplate() {
	 * RedisTemplate<String, Object> template = new RedisTemplate<>();
	 * template.setConnectionFactory(jedisConnectionFactory());
	 * template.setKeySerializer(new StringRedisSerializer());
	 * template.setHashKeySerializer(new StringRedisSerializer());
	 * template.setValueSerializer(stringRedisSerializer());
	 * template.setHashValueSerializer(stringRedisSerializer());
	 * template.setStringSerializer(stringRedisSerializer()); return template; }
	 */

    @Bean
    public CustomRedisSerializer stringRedisSerializer() {
        return new CustomRedisSerializer();
    }
}
