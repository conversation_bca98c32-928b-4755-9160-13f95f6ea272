package com.stpl.tech.neo.core.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.neo.core.dao.DeviceDao;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.CartService;
import com.stpl.tech.neo.core.service.StampingService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartItem;
import com.stpl.tech.neo.domain.model.mongo.CartStatus;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.DiscountDetail;
import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.OrderItem;
import com.stpl.tech.neo.domain.model.mongo.SettlementType;

@Service
public class CartServiceImpl implements CartService {

    @Autowired
    private CartDao cartDao;
    @Autowired
    private DeviceDao deviceDao;
    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private StampingService stampService;

    /**
     * Creates cart with the details provided
     */
    @Override
    @Deprecated
    public CartDetail createCart(CartDetail cartDetail) {
        /*
         * required to check if cart for a particular device already exits to stop
         * multiple ACTIVE cart creation for the same device
         */
        cartDetail.setCreationTime(NeoUtil.getCurrentTimestamp());
        cartDetail.setCartStatus(CartStatus.CREATED);
        cartDetail = cartDao.save(cartDetail);
        // this might be useful when stamping!
        stampService.updateCartInDevice(cartDetail);
        stampService.updateCartInCustomer(cartDetail);
        return cartDetail;
    }

    @Override
    public boolean addToCart(CartItem item) throws WebOrderException {
        if (isValidCartItem(item)) {
            Optional<CartDetail> cart = cartDao.findById(item.getCartId());
            if (cart.isPresent() && CartStatus.CREATED.equals(cart.get().getCartStatus())) {
                addItemToCart(cart.get(), item.getOrderItem());
                cartDao.save(cart.get());
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean updateCartItem(CartItem item) throws WebOrderException {
        if (isValidCartItem(item)) {
            Optional<CartDetail> cart = cartDao.findById(item.getCartId());
            if (cart.isPresent() && CartStatus.CREATED.equals(cart.get().getCartStatus())) {
                removeItemFromCart(cart.get(), item.getOrderItem());
                addItemToCart(cart.get(), item.getOrderItem());
                cartDao.save(cart.get());
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean removeCartItem(CartItem item) throws WebOrderException {
        if (isValidCartItem(item)) {
            Optional<CartDetail> cart = cartDao.findById(item.getCartId());
            if (cart.isPresent() && CartStatus.CREATED.equals(cart.get().getCartStatus())) {
                removeItemFromCart(cart.get(), item.getOrderItem());
                updateItemIds(cart.get());
                cartDao.save(cart.get());
                return true;
            }
        }
        return false;
    }

    private void updateItemIds(CartDetail cart) {
        int i = 0;
        for (OrderItem item : cart.getOrderDetail().getOrders()) {
            i++;
            item.setItemId(i);
        }
    }

    @Override
    public boolean clearCart(String cartId) {
        Optional<CartDetail> cart = cartDao.findById(cartId);
        if (cart.isPresent() && CartStatus.CREATED.equals(cart.get().getCartStatus())) {
            cart.get().getOrderDetail().getOrders().clear();
            cartDao.save(cart.get());
        }
        return false;
    }

    private void addItemToCart(CartDetail cart, OrderItem item) {
        cart.getOrderDetail().getOrders().add(item);
    }

    private void removeItemFromCart(CartDetail cart, OrderItem item) {
        List<OrderItem> orderItems = cart.getOrderDetail().getOrders();
        // TODO check if we can make this run faster
        for (int i = 0; i < orderItems.size(); i++) {
            if (orderItems.get(i).getItemId() == item.getItemId()) {
                orderItems.remove(i);
            }
        }
    }

    private boolean isValidCartItem(CartItem item) throws WebOrderException {
        if (item != null && item.getCartId() != null) {
            return true;
        } else {
            throw new WebOrderException("Cart Id is Null");
        }
    }

    @Override
    public CartDetail addReOrderItemsToCart(String gOrderId) {
        CartDetail sourceCart = cartDao.searchByGeneratedOrderId(gOrderId);
        Optional<Customer> c = customerDao.findById(sourceCart.getCustomerId());
        Optional<CartDetail> targetCart = cartDao.findById(c.get().getCartId());
        targetCart.get().setOrderDetail(new Order());
        targetCart.get().getOrderDetail().setWebCustomerId(c.get().getCustomerId());
        targetCart.get().getOrderDetail().setSettlementType(SettlementType.DEBIT);
        int i = 1;
        for (OrderItem oi : sourceCart.getOrderDetail().getOrders()) {
            oi.setItemId(i);
            oi.setDiscountDetail(new DiscountDetail());
            targetCart.get().getOrderDetail().getOrders().add(oi);
            i = i + 1;
        }
        return cartDao.save(targetCart.get());
    }

    @Override
    public List<String> getUnsettledOrders() {
        return getOrderIsList(cartDao.findUnSettledOrderCarts());
    }

    @Override
    public List<String> getUndeliveredOrders() {
        return getOrderIsList(cartDao.findUnDeliveredOrderCarts());
    }

    private List<String> getOrderIsList(List<CartDetail> carts) {
        return Optional.ofNullable(carts).map(Collection::stream).orElse(Stream.empty()).map(CartDetail::getOrderDetail)
                .map(Order::getOrderId).collect(Collectors.toList());
    }

    @Override
    public void updateCart(CartDetail cartDetail) {
        cartDao.save(cartDetail);
    }

}
