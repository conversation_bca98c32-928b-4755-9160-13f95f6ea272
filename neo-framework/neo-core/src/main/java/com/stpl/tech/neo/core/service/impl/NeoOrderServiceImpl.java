package com.stpl.tech.neo.core.service.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.PaymentDetailDao;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.*;
import com.stpl.tech.neo.core.util.DateDeserializer;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.core.util.WebErrorCodes;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.domain.model.CustomerOrderPage;
import com.stpl.tech.neo.domain.model.PaymentVO;
import com.stpl.tech.neo.domain.model.mongo.*;
import com.stpl.tech.redis.domain.model.ResponseCode;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import org.springframework.http.HttpMethod;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class NeoOrderServiceImpl extends AbstractDataValidator implements NeoOrderService {

    private static final Logger LOG = LoggerFactory.getLogger(NeoOrderServiceImpl.class);

    @Autowired
    private CartDao cartDao;
    @Autowired
    private StampingService stampService;
    @Autowired
    private NeoCustomerService customerService;
    @Autowired
    private KettleOrderService kettle;
    @Autowired
    private PaymentDetailDao paymentDao;
    @Autowired
    private WebPaymentService paymentService;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private CustomerProductService customerProductService;
    @Autowired
    private NeoMetadataCacheManagementService neoCacheManger;
    @Autowired
    private CartService cartService;

    /**
     * Use this to checkout cart by using cart Id only, method validates cart and
     * then performs regular cart checkout. Saves time from sending whole cart again
     * and again.
     * <p>
     * WARNING: call sync cart before using this method to ensure that cart is
     * updated to current data.
     *
     * @param cartId
     * @return generatedOrderId
     * @throws WebOrderException
     */
    @Override
    public String checkout(String cartId) throws WebOrderException {
        CartDetail cart = validateCart(cartId);
        return checkout(cart);
    }

    @Override
    public String checkoutByWebOrderId(String woId) throws WebOrderException {
        String cartId = null;
        CartDetail cart = cartDao.searchByExternalOrderId(woId);
        LOG.info("cart object {} " , JSONSerializer.toJSON(cart));
        if (cart != null) {
            cartId = cart.getCartId();
        }
        cart = validateCart(cartId);
        return checkout(cart);
    }

    /**
     * requires updated cart, first validates cart for anomalies and then fills
     * critical data to the cart from previous state this saves from data loss if
     * any from front end. updated cart is then sent for normal checkout procedure.
     *
     * @param cart
     * @return generatedOrderId
     * @throws WebOrderException
     */
    @Override
    public String saveAndCheckoutCart(CartDetail cart) throws WebOrderException {
        CartDetail oldCart = validateCart(cart.getCartId());
        fillCriticalData(cart.getOrderDetail(), oldCart.getOrderDetail(), true);
        CartDetail updatedCart = cartDao.save(cart);
        return checkout(updatedCart);
    }

    @Override
    public String saveAndCheckoutCartForKIOSKPayByCash(CartDetail cart) throws WebOrderException {
        CartDetail oldCart = validateCart(cart.getCartId());
        fillCriticalData(cart.getOrderDetail(), oldCart.getOrderDetail(), false);
        CartDetail updatedCart = cartDao.save(cart);
        return checkout(updatedCart);
    }

    /**
     * Regular sync method for a cart, use this when ever we need to update a cart.
     * it validates card for anomalies before updating it and then saves cart.
     * <p>
     * WARNING: no data is preserved in cart and everything is taken as it is.
     *
     * @param cart
     * @return success
     * @throws WebOrderException
     */
    @Override
    public ResponseCode syncCart(CartDetail cart) throws WebOrderException {
        validateCart(cart.getCartId());
        verifyOrder(cart, true, false);
        cartDao.save(cart);
        return getResponseCode(cart);
    }

    /**
     * Search Order using Kettle Generated Order Id, generated order Id is the 16
     * digit code printed on the bill as bill number, this will be unique for all
     * orders
     * <p>
     * throws web order exception if order is not found
     *
     * @param id
     * @return OrderView
     * @throws WebOrderException
     */
    @Override
    public OrderView search(String id) throws WebOrderException {
        if (id == null) {
            throwAxe(WebErrorCodes.INVALID_ORDER);
        }
        CartDetail cart = cartDao.searchByGeneratedOrderId(id);
        if (cart == null) {
            throwAxe(WebErrorCodes.ORDER_NOT_FOUND);
        }
        return JSONSerializer.toJSON(JSONSerializer.toJSON(cart.getOrderDetail()), OrderView.class);
    }

    /**
     * Search Order using Kettle Generated Order Id, generated order Id is the 16
     * digit code printed on the bill as bill number, this will be unique for all
     * orders
     * <p>
     * throws web order exception if order is not found
     *
     * @param id
     * @return OrderView
     * @throws WebOrderException
     */
    @Override
    public OrderView searchByWebId(String id) throws WebOrderException {
        if (id == null) {
            throwAxe(WebErrorCodes.INVALID_ORDER);
        }
        CartDetail cart = cartDao.searchByExternalOrderId(id);
        if (cart == null) {
            throwAxe(WebErrorCodes.ORDER_NOT_FOUND);
        }
        return JSONSerializer.toJSON(JSONSerializer.toJSON(cart.getOrderDetail()), OrderView.class);
    }

    /**
     * Search Customer Orders This method search customer orders dynamically. Number
     * of Orders returned in a single page can be altered according to the
     * requirements.
     * <p>
     * <p>
     * <p>
     * Orders are arranged in descending order according to the billing time i.e.
     * most recent order comes first
     * <p>
     * NOTE: Paging is indexed i.e. index starts for 0 therefore page number 1
     * should be treated as 0
     *
     * @param page
     * @return {@link CustomerOrderPage}
     */
    @Override
    public CustomerOrderPage searchCustomerOrders(CustomerOrderPage page) throws WebOrderException {
        if (page == null || page.getCustomerId() == null) {
            throwAxe(WebErrorCodes.INVALID_CUSTOMER);
        }
        int pageNumber = page.getPage() - 1 > 0 ? page.getPage() - 1 : 0;
        // Paging is indexed i.e. it starts from zero
//        PageRequest request = new PageRequest(pageNumber, page.getLimit(),
//                new Sort(Direction.DESC, "orderDetail.billingServerTime"));

        List<CartDetail> carts = cartDao.findByCustomerId(page.getCustomerId(),
                PageRequest.of(pageNumber, page.getLimit(), Sort.by(Direction.DESC, "orderDetail.billingServerTime"))).getContent();
        List<OrderVO> orders = new ArrayList<>();
        carts.forEach(p -> orders.add(JSONSerializer.toJSON(JSONSerializer.toJSON(p.getOrderDetail()), OrderVO.class)));
        page.getOrders().clear();
        page.getOrders().addAll(orders);
        return page;
    }

    /**
     * Fetch Order Status using Kettle Generated Order Id, generated order Id is the
     * 16 digit code printed on the bill as bill number, this will be unique for all
     * orders
     * <p>
     * throws web order exception if order is not found
     *
     * @param id
     * @return order
     * @throws WebOrderException
     */
    @Override
    public OrderStatus fetchOrderStatus(String id) throws WebOrderException {
        if (id == null) {
            throwAxe(WebErrorCodes.INVALID_ORDER);
        }
        CartDetail cart = cartDao.searchByGeneratedOrderId(id);
        if (cart == null) {
            throwAxe(WebErrorCodes.ORDER_NOT_FOUND);
        }
        return cart.getOrderDetail().getStatus();
    }

    /**
     * Check if customer has already placed an order. This method is used as a check
     * for offers applicable on first order only.
     *
     * @param customerId
     * @return boolean
     */
    @Override
    public boolean isFirstOrderForCustomer(String customerId) throws WebOrderException {
        if (customerId == null) {
            throwAxe(WebErrorCodes.INVALID_CUSTOMER);
        }
        List<CartDetail> carts = cartDao.findSettledCartByCustomerId(customerId);
        if (carts != null && carts.size() > 0) {
            return false;
        }
        return true;
    }

    @Override
    public byte[] downloadOrderReceipt(String orderId) {
        try {
            Receipt orderReceipt = kettle.getOrderReceipt(orderId);
            if (orderReceipt != null) {
                LOG.info("Order Receipt fetched from kettle, now sending for download.");
                return orderReceipt.getOrderReceipt();
            }
        } catch (Exception ex) {
            LOG.error("Failed to download the order receipt", ex);
        }
        return new byte[0];
    }

    private CartDetail validateCart(String cartId) throws WebOrderException {
        if (cartId == null) {
            throwAxe(WebErrorCodes.INVALID_CART);
        }
        Optional<CartDetail> cartDetail = cartDao.findById(cartId);
        CartDetail cart = cartDetail.orElse(null);
        if (cart == null) {
            throwAxe(WebErrorCodes.CART_NOT_FOUND);
        }
        if (CartStatus.PAYMENT_DONE.equals(cart.getCartStatus())) {
            throwAxe(WebErrorCodes.PAYMENT_DONE);
        }
        if (CartStatus.SETTLED.equals(cart.getCartStatus())) {
            detachCart(cart);
            throwAxe(WebErrorCodes.SETTLED_CART);
        }
        if (cart.getOrderDetail().getOrderId() != null || cart.getOrderDetail().getGenerateOrderId() != null
                || OrderStatus.SETTLED.equals(cart.getOrderDetail().getStatus())
                || OrderStatus.DELIVERED.equals(cart.getOrderDetail().getStatus())) {
            detachCart(cart);
            throwAxe(WebErrorCodes.SETTLED_CART);
        }
        return cart;
    }

    @Override
    public boolean setOrderSource(CartDetail request) throws WebOrderException {
        if (request.getCartId() == null) {
            throwAxe(WebErrorCodes.INVALID_CART);
        }
        Optional<CartDetail> cartDetail = cartDao.findById(request.getCartId());
        CartDetail cart = cartDetail.orElse(null);
        if (cart == null) {
            throwAxe(WebErrorCodes.CART_NOT_FOUND);
        } else {
            if (CartStatus.PAYMENT_DONE.equals(cart.getCartStatus())) {
                throwAxe(WebErrorCodes.PAYMENT_DONE);
            }
            if (CartStatus.SETTLED.equals(cart.getCartStatus())) {
                detachCart(cart);
                throwAxe(WebErrorCodes.SETTLED_CART);
            }
            cart.getOrderDetail().setSource(request.getOrderDetail().getSource());
            CartDetail updatedCart = cartDao.save(cart);
            if (updatedCart != null) {
                return true;
            }
        }
        return false; //checkout(updatedCart);
    }

    private String checkout(CartDetail cart) throws WebOrderException {

        String response = null;
        updateCustomerIdAndName(cart);
        updateSettlemetDetails(cart);
        verifyOrder(cart, false, false);
        // verifyPayment(cart);
        updateOrderMetadata(cart);
        updateOrderItemRecipeDefaults(cart);
        try {
            // check payment status !!
            LOG.info("cart object {} " , JSONSerializer.toJSON(cart));
            Order order = kettle.sendOrder(cart.getOrderDetail());
            response = updateCart(cart, order);
            updateCustomerRecords(cart, order);
            detachCart(cart);
        } catch (Exception e) {
            LOG.error("Error while creating Order for cart {}", JSONSerializer.toJSON(cart), e);
            throwAxe(WebErrorCodes.ORDER_FAILED);
        }
        return response;
    }

    private void updateCustomerRecords(CartDetail cart, Order order) {
        // TODO Make this method independent of Order Creation
        customerProductService.updateProductConsuption(cart.getCustomerId(), order);
    }

    private void verifyPayment(CartDetail cart) throws WebOrderException {
        String webOrderId = cart.getOrderDetail().getExternalOrderId();
        if (webOrderId != null) {
            validateWithRetry(webOrderId, cart.getOrderDetail().getTransactionDetail().getPaidAmount(), 0);
        }
    }

    private void validateWithRetry(String webOrderId, BigDecimal amount, int count) throws WebOrderException {

        // TODO this sucks
        // Under development
        PaymentVO vo = new PaymentVO();
        vo.setOrderId(webOrderId);
        vo.setAmount(amount);

        if (count >= 5) {
            // stops retry after 5 counts
            String msg = WebErrorCodes.ORDER_FAILED + " Unable to validate Payment - " + webOrderId + " amount - "
                    + amount + "\nFailed after " + (count + 1) + " tries";
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(),"Neo",null, SlackNotification.SYSTEM_ERRORS.getChannel(props.getEnvType()), msg);
            return;
        }

        try {
            boolean success = paymentService.getPaymentStatus(vo);
            if (!success) {
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(),"Neo",null, SlackNotification.SYSTEM_ERRORS.getChannel(props.getEnvType()), WebErrorCodes.ORDER_FAILED + " Payment Not Done for " + webOrderId + " amount - " + amount);
            }
        } catch (Exception e) {
            NeoUtil.slackIt(props.getEnvType(), e,
                    "Payment Validation Failed for " + webOrderId + " amount - " + amount + "count - " + count);
            validateWithRetry(webOrderId, amount, count + 1);
        }
    }

    private void updateSettlemetDetails(CartDetail cart) {
        String webOrderId = cart.getOrderDetail().getExternalOrderId();
//        cart.getOrderDetail().getSettlements().clear();
        Settlement s = new Settlement();
        if (StringUtils.isNotBlank(cart.getOrderType()) && cart.getOrderType().equalsIgnoreCase("KIOSK_CASH")) {
            s.setMode(WebOrderingConstants.PAYMENT_MODE_CASH);
            s.setAmount(cart.getOrderDetail().getTransactionDetail().getPaidAmount());
        } else if (StringUtils.isNotBlank(cart.getOrderType()) && cart.getOrderType().equalsIgnoreCase("NEO_CASH")) {
            s.setMode(WebOrderingConstants.PAYMENT_MODE_CASH);
            s.setAmount(cart.getOrderDetail().getTransactionDetail().getPaidAmount());
        } else if (StringUtils.isNotBlank(cart.getOrderType()) && cart.getOrderType().equalsIgnoreCase("KIOSK_GIFT_CARD")) {
            s.setMode(WebOrderingConstants.PAYMENT_MODE_GIFT_CARD);
            s.setAmount(cart.getOrderDetail().getTransactionDetail().getPaidAmount());
            s.setExternalSettlements(getExternalSettlements(cart.getOrderDetail().getSettlements()));

        } else {
            if (webOrderId != null) {
                PaymentDetail pd = paymentDao.searchByExternalPaymentId(webOrderId);
                s.setMode(pd.getPaymentModeId());
                s.setAmount(pd.getAmount());
                // if (cart.getOrderDetail().getSettlements().size() > 0) {
                //     for (Settlement settlement : cart.getOrderDetail().getSettlements()) {
                //         s.setModeDetail(new PaymentMode());
                //         s.getModeDetail().setCategory(settlement.getModeDetail().getCategory());
                //     }
                // }
            } else {
                s.setMode(WebOrderingConstants.PAYMENT_MODE_CASH);
                s.setAmount(cart.getOrderDetail().getTransactionDetail().getPaidAmount());
            }
        }
        cart.getOrderDetail().getSettlements().clear();
        cart.getOrderDetail().getSettlements().add(s);
    }

    private void detachCart(CartDetail cart) {
        detachCartFromDevice(cart);
        detachCartFromCustomer(cart);
    }

    private List<ExternalSettlement> getExternalSettlements(List<Settlement> settlements) {
        for (Settlement s : settlements) {
            return s.getExternalSettlements();
        }
        return null;
    }

    private void detachCartFromDevice(CartDetail cart) {
        stampService.removeCartFromDevice(cart);
    }

    private void addCartToDevice(CartDetail cart) {
        stampService.addCartToDevice(cart);
    }

    private void attachCartToCustomer(CartDetail cart) {
        customerService.attachCartToCustomer(cart);
    }


    private void detachCartFromCustomer(CartDetail cart) {
        customerService.changeCart(cart);
    }

    private String updateCart(CartDetail cart, Order order) {
        Order webOrder = cart.getOrderDetail();
        fillCriticalData(order, webOrder, false);
        order.setWebCustomerId(webOrder.getWebCustomerId());
        cart.setOrderDetail(order);
        List<Address> adds = customerService.getCustomerAddresses(cart.getCustomerId());
        if (cart.getOrderDetail().getDeliveryAddress() != null) {
            // Address for delivery orders only
            for (Address add : adds) {
                if (add.getId() == cart.getOrderDetail().getDeliveryAddress()) {
                    cart.getOrderDetail().setAddress(add);
                }
            }
        }
        cart.setCartStatus(CartStatus.SETTLED);
        cart = cartDao.save(cart);
        return cart.getOrderDetail().getGenerateOrderId();
    }

    @Override
    protected NeoCustomerService getCustomerService() {
        return customerService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public DeviceVO createGiftCardCart(DeviceVO deviceVO) throws WebOrderException {
        detachCart(deviceVO.getCartDetail());
        neoCacheManger.addCartIdToCache(deviceVO.getCartDetail().getDeviceId(), deviceVO.getCartDetail().getCartId());
        CartDetail cd = deviceVO.getCartDetail();
        deviceVO.setCartDetail(null);
        DeviceVO dVo = stampService.stampDevice(deviceVO);

        OrderItem orderItem = null;
        ArrayList giftCardItem = new ArrayList<OrderItem>();
        for (OrderItem item : cd.getOrderDetail().getOrders()) {
            orderItem = item;
            giftCardItem.add(item);
        }
        dVo.getCartDetail().getOrderDetail().setOrders(giftCardItem);
        dVo.getCartDetail().getOrderDetail().setTransactionDetail(cd.getOrderDetail().getTransactionDetail());
        dVo.getCartDetail().getOrderDetail().setUnitId(cd.getOrderDetail().getUnitId());
        dVo.getCartDetail().getOrderDetail().setTerminalId(cd.getOrderDetail().getTerminalId());
        dVo.getCartDetail().getOrderDetail().setSource(cd.getOrderDetail().getSource());
        dVo.getCartDetail().getOrderDetail().setBrandId(cd.getOrderDetail().getBrandId());

        CartItem cartItem = new CartItem();
        cartItem.setCartId(dVo.getCartDetail().getCartId());
        cartItem.setOrderItem(orderItem);

        cartService.addToCart(cartItem);
        cartService.updateCart(dVo.getCartDetail());

        return dVo;
    }

    @Override
    public void attachCart(CartDetail cart) {
        addCartToDevice(cart);
        attachCartToCustomer(cart);
    }

    @Override
    public List<GiftCardOffer> giftCardOffer(int unitId) {
        List<GiftCardOffer> offers = new ArrayList<>();
        List<GiftCardOffer> giftCardOffer = new ArrayList<>();
        try {
            LOG.info("Get gift card offer of unitId");
            String endpoint = props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_GIFT_CARD_OFFER;
            String token = props.getNeoClientToken();
            List<?> list = WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, List.class,
                    String.valueOf(unitId), null);
            GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer());
            if (list != null) {
                list.forEach(p -> {
                    Gson gson = gSonBuilder.create();
                    String str = gson.toJson(p);
                    GiftCardOffer cat = gson.fromJson(str, GiftCardOffer.class);
                    if (cat != null) {
                        giftCardOffer.add(cat);
                    }
                });
            } else {
                LOG.info("No Inventory Available for cafe {}", unitId);
            }
        } catch (Exception e) {
            LOG.error("Got exception while getting offer in gift card ", e );
        }
        return giftCardOffer;
    }

}
