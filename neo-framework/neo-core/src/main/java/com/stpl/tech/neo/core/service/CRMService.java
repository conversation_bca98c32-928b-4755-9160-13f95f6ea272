package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.domain.model.CustomerInfoResponse;
import com.stpl.tech.neo.domain.model.LaunchOfferData;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.mongo.Address;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.CustomerAddress;
import com.stpl.tech.neo.domain.model.mongo.CustomerCurrentAddresses;
import com.stpl.tech.neo.domain.model.mongo.CustomerData;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.redis.domain.model.IdCodeName;

import java.net.URISyntaxException;
import java.util.List;

public interface CRMService {

	public Customer lookup(String contact);

	Customer getCustomerByContact(String contact);

	boolean oldCustomerLookUp(String contact);
	
	public List<Address> getNewCustomerAddress(CustomerCurrentAddresses addresses);

	public boolean sendOTP(String contact);

	public boolean verifyOTP(String contact, String otp);

	public Customer signup(String contact, String name, String email, Integer tcId);

    Customer signup(CustomerData request);

    public Address addCustomerAddress(CustomerAddress request);

	public boolean update(String contact, String name, String emailId);

	public OTPResponse resendOTP(String contact);

	List<String> getInternalCustomers();
	
	public boolean markAllInternalCustomers();

	public Integer submitNPS(Object formEvent);

	public String generateToken(String input);

	public LaunchOfferData cafeLaunchOffer(LaunchOfferData input);

	public LaunchOfferData cafeFreeItemOffer(LaunchOfferData input);

	Boolean sendSMS(SMSRequest request);

    IdCodeName getCustomerUniqueCoupon(String contact);

	boolean hasOrdersForCafe(String contact);

	boolean hasOrdersForDelivery(String contact);

	boolean sendSignUpCoupon(CustomerInfoResponse customerInfoResponse);

	boolean verifyTruecallerLogin(String truecallerRequestId, String contact) throws URISyntaxException;
}
