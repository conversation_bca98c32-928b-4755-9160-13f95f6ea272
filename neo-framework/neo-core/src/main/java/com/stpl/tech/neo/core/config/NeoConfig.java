package com.stpl.tech.neo.core.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Created by Chaayos on 10-10-2016.
 */
@Configuration
@EnableScheduling
@EnableMongoRepositories(basePackages = "com.stpl.tech.neo.core.dao")
@ComponentScan(basePackages = { "com.stpl.tech.neo.core.service", "com.stpl.tech.neo.core.cache",
		"com.stpl.tech.neo.core.listner" })
public class NeoConfig {

}
