package com.stpl.tech.neo.core.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.stpl.tech.util.EnvType;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Service
public class EnvironmentProperties {

	@Autowired
	Environment environment;

	public String getRedisHost() {
		return environment.getProperty("neo.redis.host", "localhost");
	}

	public int getRedisPort() {
		return Integer.valueOf(environment.getProperty("neo.redis.port", "6379")).intValue();
	}

	public EnvType getEnvType() {
		return EnvType.valueOf(environment.getProperty("environment.type", "DEV"));
	}

	public String getNeoClientToken() {
		return environment.getProperty("redis.client.token");
	}

	public String getMongoHost() {
		return environment.getProperty("neo.mongo.host", "localhost");
	}

	public int getMongoPort() {
		return Integer.valueOf(environment.getProperty("neo.mongo.port", "27017")).intValue();
	}

	public String getMongoSchema() {
		return environment.getProperty("spring.data.mongodb.database", "neo");
	}

	public String getMongoUser() {
		return environment.getProperty("neo.mongo.user", "root");
	}

	public String getMongoPass() {
		return environment.getProperty("neo.mongo.pass", "root");
	}

	public String getKettleCRMBasePath() {
		return environment.getProperty("base.path.kettle.crm");
	}

	public String getTruecallerBasePath() {
		return environment.getProperty("base.path.truecaller");
	}

	public String getKettleRekognitionBasePath() {
		return environment.getProperty("base.path.kettle.rekognition");
	}

	public String getKettleServiceBasePath() {
		return environment.getProperty("base.path.kettle.service");
	}

	public String getMasterServiceBasePath() {
		return environment.getProperty("base.path.master.service");
	}

	public String getMongoURI() {
		return environment.getProperty("spring.data.mongodb.uri");
	}

	public int getDefaultCustomerId() {
		return Integer.parseInt(environment.getProperty("default.customer.id"));
	}

	public String getPaymentGateway() {
		return environment.getProperty("default.payment.gateway", "PAYTM");
	}

	public String getPaytmRedirectUrl() {
		return environment.getProperty("paytm.redirect", "");
	}

	public String getPaytmCallBackUrl() {
		return environment.getProperty("paytm.callback.url", "");
	}

	public String getRazorPayRedirectUrl() {
		return environment.getProperty("razorPay.redirect", "");
	}

	public String getRazorPayPaymentSecretKey() {
		return environment.getProperty("razorpay.payment.secret.key");
	}

	public String getRazorPaySignatureSecretKey() {
		return environment.getProperty("razorpay.signature.secret.key");
	}

	public boolean getSendOtp() {
		return Boolean.valueOf(environment.getProperty("send.otp", "true"));
	}

	public String getApplicationIdentifier() {
		return environment.getProperty("application.identifier", "");
	}

	public String getAWSQueueRegion() {
		return environment.getProperty("aws.queue.region", "AP_SOUTH_1");
	}
}
