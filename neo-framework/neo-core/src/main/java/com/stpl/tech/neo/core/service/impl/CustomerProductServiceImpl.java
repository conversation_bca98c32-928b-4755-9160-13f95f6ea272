package com.stpl.tech.neo.core.service.impl;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.CustomerProductDao;
import com.stpl.tech.neo.core.service.CustomerProductService;
import com.stpl.tech.neo.domain.model.mongo.CustomerProductConsumption;
import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.OrderItem;
import com.stpl.tech.neo.domain.model.mongo.ProductConsumption;
import com.stpl.tech.util.AppConstants;

@Service
public class CustomerProductServiceImpl implements CustomerProductService {

	@Autowired
	private CustomerProductDao customerProductDao;

	@Override
	public CustomerProductConsumption getCustomerProductConsumption(String customerId) {
		if (customerId != null) {
			CustomerProductConsumption c = customerProductDao.findByCustomerId(customerId);
			return c;
		}
		return null;
	}

	@Override
	public void updateProductConsuption(String customerId, Order order) {
		if (customerId != null) {

			// existing consumption
			CustomerProductConsumption c = customerProductDao.findByCustomerId(customerId);

			if (c == null) {
				c = new CustomerProductConsumption();
				c.setCustomerId(customerId);
			}

			Set<String> productSet = new HashSet<String>();
			Set<ProductConsumption> allProductSet = new HashSet<>();
			allProductSet.addAll(c.getAllProducts());

			for (OrderItem i : order.getOrders()) {
				if (i.getProductCategory().getId() == AppConstants.CATEGORY_OTHERS) {
					continue;
				}
				productSet.add(String.valueOf(i.getProductId()));
				addConsumption(i, allProductSet);
			}

			c.getAllProducts().clear();
			c.getAllProducts().addAll(allProductSet);

			Collections.sort(c.getAllProducts(), new Comparator<ProductConsumption>() {
				@Override
				public int compare(ProductConsumption o1, ProductConsumption o2) {
					// -1 for descending order
					return o1.getQuantity().compareTo(o2.getQuantity()) * -1;
				}
			});

			c.getCurrentProducts().clear();

			for (String s : productSet) {
				if (c.getCurrentProducts().size() < 6) {
					for (ProductConsumption pc : c.getAllProducts()) {
						if (pc.getProductId().equals(s)) {
							c.getCurrentProducts().add(pc);
						}
					}
				} else {
					break;
				}
			}

			if (c.getCurrentProducts().size() < 6) {
				for (ProductConsumption pc : c.getAllProducts()) {
					if (c.getCurrentProducts().size() < 6) {
						if (!productSet.contains(String.valueOf(pc.getProductId()))) {
							c.getCurrentProducts().add(pc);
						}
					} else {
						break;
					}
				}
			}

			customerProductDao.save(c);
		}
	}

	private void addConsumption(OrderItem i, Set<ProductConsumption> allProducts) {
		ProductConsumption c = createConsumption(i);
		if (allProducts.contains(c)) {
			for (ProductConsumption pc : allProducts) {
				if (pc.equals(c)) {
					pc.setQuantity(pc.getQuantity() + c.getQuantity());
				}
			}
		} else {
			allProducts.add(c);
		}
	}

	private ProductConsumption createConsumption(OrderItem i) {
		ProductConsumption c = new ProductConsumption();
		c.setProductId(String.valueOf(i.getProductId()));
		c.setProductName(i.getProductName());
		c.setQuantity(i.getQuantity());
		return c;
	}

}
