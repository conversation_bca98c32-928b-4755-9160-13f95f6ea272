package com.stpl.tech.neo.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.neo.core.util.WebErrorCodes;

@ResponseStatus(value = HttpStatus.OK, reason = "Order Creation Failure")
public class WebOrderException extends Exception {

	private static final long serialVersionUID = 1L;

	private WebErrorCodes code;

	public WebOrderException() {
	}

	public WebOrderException(String message) {
		super(message);
	}

	public WebOrderException(WebErrorCodes code) {
		super(code.getMsg());
		this.code = code;
	}

	public WebOrderException(WebErrorCodes code, String message) {
		super(message);
		this.code = code;
	}

	public WebOrderException(Throwable cause) {
		super(cause);
	}

	public WebOrderException(String message, Throwable cause) {
		super(message, cause);
	}

	public WebOrderException(WebErrorCodes code, Throwable cause) {
		super(code.getMsg(), cause);
		this.code = code;
	}

	public WebOrderException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public WebErrorCodes getCode() {
		return code;
	}

	public void setCode(WebErrorCodes code) {
		this.code = code;
	}

}
