package com.stpl.tech.neo.core.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.UnitLocalityMappingDao;
import com.stpl.tech.neo.core.service.UnitLocalityService;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMapping;
import com.stpl.tech.neo.domain.model.mongo.UnitLocalityMappingVO;
import com.stpl.tech.redis.domain.model.Unit;

@Service
public class UnitLocalityServiceImpl implements UnitLocalityService {

	@Autowired
	private UnitLocalityMappingDao localityDao;

	// @Autowired
	// private CounterService counterService;

	@Override
	public List<UnitLocalityMapping> getUnitLocalityMappingData() {
		return localityDao.findAll();
	}

	@Override
	public Unit getDeliveryUnit(UnitLocalityMappingVO mapping) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean addUnitLocalityMapping(UnitLocalityMapping mapping) {
		// mapping.setMappingId(String.valueOf((counterService.getNextSequence(UnitLocalityMapping.class.getCanonicalName()))));
		return localityDao.save(mapping) != null;
	}

	@Override
	public boolean addUnitLocalityMappings(List<UnitLocalityMapping> mappings) {
		// mappings.forEach(p ->
		// p.setMappingId(counterService.getNextStringSequence(UnitLocalityMapping.class.getCanonicalName())));
		return localityDao.saveAll(mappings) != null;
	}

	@Override
	public boolean removeUnitLocalityMapping(UnitLocalityMapping mapping) {
		localityDao.delete(mapping);
		return true;
	}

	@Override
	public boolean removeUnitLocalityMappings(List<UnitLocalityMapping> mappings) {
		localityDao.deleteAll(mappings);
		return true;
	}

	@Override
	public boolean updateUnitLocalityMapping(UnitLocalityMapping mapping) {
		localityDao.save(mapping);
		return true;
	}

	@Override
	public boolean updateUnitLocalityMappings(List<UnitLocalityMapping> mappings) {
		localityDao.saveAll(mappings);
		return true;
	}

}
