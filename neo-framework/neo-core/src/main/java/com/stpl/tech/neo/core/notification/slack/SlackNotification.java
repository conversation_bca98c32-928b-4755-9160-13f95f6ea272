package com.stpl.tech.neo.core.notification.slack;

import com.stpl.tech.util.EnvType;

public enum SlackNotification {

	FEEDBACK_CAFE_LOW_RATING("feedback_cafe","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAhjHthns/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=uYFWshvDjxBvUiAxm-cHimfasqPzStrlbrCmB4uDZHA%3D"),
	FEEDBACK_DELIVERY_LOW_RATING("feedback_delivery","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA0znWeSI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=dtT-WLUEVXJCi_apT1Y9rTh4uypak-pL4oEe21Lj3IA%3D"),
	SYSTEM_ERRORS("kettle_errors","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAdxGOvHw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=coXZOEUDjF1YZz8i33GoW1XDKwZfeHk4PDKquZiZk9s%3D"),
	ORDER_FAILURE_INFO("webapp_orders","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA30Qv1is/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=7Ghv0pgLMQocy_7yUZg2OtkQMKm3CxRuQZUsQqQE4Gk%3D");

	private String channel;
	private String webHookDev;
	private String webHookProd;

	private SlackNotification(String channel,String webHookDev,String webHookProd) {
		this.channel = channel;
		this.webHookDev = webHookDev;
		this.webHookProd = webHookProd;
	}

	public String getChannel(EnvType env) {
		switch (env) {
		case DEV:
			return webHookDev;
		case STAGE:
			return webHookDev;
		case PROD:
			return webHookProd;
		case SPROD:
			return webHookProd;
		default:
			break;
		}
		return null;
	}

}
