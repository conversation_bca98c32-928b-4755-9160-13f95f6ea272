package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.domain.model.CustomerCampaignJourney;
import com.stpl.tech.neo.domain.model.CustomerCardInfo;
import com.stpl.tech.neo.domain.model.CustomerInfoResponse;
import com.stpl.tech.neo.domain.model.CustomerLoginRequest;
import com.stpl.tech.neo.domain.model.EmailTemplate;
import com.stpl.tech.neo.domain.model.MyOfferResponse;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.mongo.Address;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.CustomerAddress;
import com.stpl.tech.neo.domain.model.mongo.CustomerData;
import com.stpl.tech.neo.domain.model.mongo.CustomerInfo;
import com.stpl.tech.neo.domain.model.mongo.CustomerSignupData;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.neo.domain.model.mongo.GameLeaderBoardResponse;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.neo.domain.model.mongo.SignUpTimeSlotCount;
import com.stpl.tech.neo.domain.model.mongo.SpecialOfferResponse;
import com.stpl.tech.redis.domain.model.IdCodeName;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.util.EmailGenerationException;

import java.util.List;
import java.util.Set;

public interface NeoCustomerService {

	public int lookupAndSendOTP(String contact, boolean sendOtp);

    int lookup(String contact);

    int oldCustomerLookUp(String contact);

    public CustomerInfo login(CustomerData request);

	public CustomerInfo signUp(CustomerData request, boolean loginNeo);

	CustomerInfo getCustomerInfo(CustomerData request) throws WebOrderException;

	public DeviceVO logout(CustomerData request);

    CustomerInfo signUpVerified(CustomerData request, boolean loginNeo);

    public List<Address> getCustomerAddresses(String customerId);

	public Address addCustomerAddress(CustomerAddress request);

	public void changeCart(CartDetail cart);

	public OTPResponse resendOTP(String contact);

	public Customer getCustomer(String customerId);

	public CustomerInfo getCustomerLoyaltyScore(String customerId);

	public boolean updateAddressToCart(IdName request);

	public Set<String> getRecentProducts(String customerId);

	public Integer submitNPS(Object formEvent);

	public boolean verifyOTP(CustomerData request);

	public CustomerInfo signInPartnerCustomer(Customer c, DeviceDetail device);

	public CustomerLoginRequest loginByKey(String key) throws WebOrderException;

	public Object getGiftCard(int customerId) throws Exception;

	public void attachCartToCustomer(CartDetail cart);

	public CustomerInfo getLoyality(CustomerInfoResponse customerInfo) throws Exception;

	public boolean generateOtp(String contact);

	CustomerCardInfo getCustomerCardInfo(String customerId);

    Boolean sendSMS(SMSRequest input);

    boolean sendEmail(EmailTemplate emailTemplate) throws EmailGenerationException;

	CustomerSignupData addCustomerNumber(String contact);

	IdCodeName getCustomerUniqueCoupon(CustomerData data);

	CustomerSignupData findCustomerSignupData(String contact);

	CustomerInfo signUpForOffer(CustomerData request);

	SignUpTimeSlotCount getTimeSlotCounts(String dateOfDelivery);

	List<MyOfferResponse> getMyOffer(CustomerData data);

    CustomerCampaignJourney saveCustomerJourney(CustomerCampaignJourney data);

	Customer getCustomerInfoByContact(String contact);

    List<SpecialOfferResponse> getSpecialOffer(CustomerData request);

	List<SpecialOfferResponse> registerAndOffer(CustomerData request);

	GameLeaderBoardResponse getLeaderBoard(String contact, String token, Boolean getRank, CustomerData customerData);
}
