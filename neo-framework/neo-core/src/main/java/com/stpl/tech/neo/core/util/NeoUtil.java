package com.stpl.tech.neo.core.util;

import com.google.gson.Gson;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.neo.core.notification.slack.SlackConstants;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;

/**
 * Created by Chaayos on 19-10-2016.
 */
public class NeoUtil extends AppUtils {

	private static final Logger LOG = LoggerFactory.getLogger(NeoUtil.class);

	public static String getDeviceIdFromDeviceKey(String deviceKey) {
		return deviceKey.split(":")[0];
	}

	public static String getDeviceTimeStampFromDeviceKey(String deviceKey) {
		return deviceKey == null ? null : deviceKey.split(":")[1];
	}

	public static String getDeviceKey(String deviceId, long timeStamp) {
		return deviceId + ":" + timeStamp;
	}

	public static String getSessionIdFromSessionKey(String sessionKey) {
		return sessionKey == null ? null : sessionKey.split("#")[0];
	}

	public static String getSessionKey(String sessionId, String deviceId, long timeStamp, String customerId,
			String contactNumber) {
		return sessionId + "#" + deviceId + "#" + timeStamp + "#" + customerId + "#" + contactNumber;
	}

	public static boolean isDev(String env) {
		return !EnvType.PROD.equals(EnvType.valueOf(env)) && !EnvType.SPROD.equals(EnvType.valueOf(env));
	}

	public static long logTime(String text, StringBuilder sb, long startTime) {
		sb.append(String.format("\n --- %s ------ ,%d", text, System.currentTimeMillis() - startTime));
		return System.currentTimeMillis();
	}

	public static BigDecimal roundOff(BigDecimal value) {
		return value.setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	public static boolean checkEqual(BigDecimal v1, BigDecimal v2) {
		if (v1 == null || v2 == null) {
			return false;
		}
		return v1.compareTo(v2) == 0;
	}

	public static void slackIt(EnvType type, Exception ex, String message) {
		try {
			String error = getFormattedMsgWithMessage(message, ex);
			LOG.error("{}\n{}", HttpStatus.INTERNAL_SERVER_ERROR.name(), error, ex);
			SlackNotificationService.getInstance().sendNotification(type,"Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(type), error);
		} catch (Exception e) {
			LOG.error("Slack is Failing", e);
		}
	}

	public static void slackIt(EnvType type, Exception ex) {
		try {
			String error = getFormattedMsgWithMessage(null, ex);
			LOG.error("{}\n{}", HttpStatus.INTERNAL_SERVER_ERROR.name(), error, ex);
			SlackNotificationService.getInstance().sendNotification(type,"Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(type), error);
		} catch (Exception e) {
			LOG.error("Slack is Failing", e);
		}
	}

	public static void slackIt(String url, EnvType type, Exception ex) {
		try {
			String error = getFormattedMsg(null, url, ex);
			SlackNotificationService.getInstance().sendNotification(type,"Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(type), error);
			LOG.error("{}\n{}", HttpStatus.INTERNAL_SERVER_ERROR.name(), error, ex);
		} catch (Exception e) {
			LOG.error("Slack is Failing", e);
		}
	}

	private static String getFormattedMsg(String userAgent, String url, Exception ex) {
		StringBuilder sb = new StringBuilder();
		sb.append(SlackConstants.EMOJI_POOP_X_4 + SlackConstants.NEW_LINE);
		sb.append("*Error Token:* " + AppUtils.generateTimeString(AppConstants.ETKN) + SlackConstants.NEW_LINE);
		sb.append("*Error Notification:* " + ex.getMessage() + SlackConstants.NEW_LINE);
		sb.append("*Error Generation Timestamp:* " + AppUtils.getCurrentTimeISTString() + SlackConstants.NEW_LINE);
		sb.append("*Request URL:* " + url + SlackConstants.NEW_LINE);
		if (userAgent != null) {
			sb.append("*User Agent:* " + userAgent + SlackConstants.NEW_LINE);
		}
		sb.append("*Stacktrace:* "
				+ org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(ex).substring(0, 1000));
		return sb.toString();
	}

	private static String getFormattedMsgWithMessage(String message, Exception ex) {
		StringBuilder sb = new StringBuilder();
		sb.append(SlackConstants.EMOJI_POOP_X_4 + SlackConstants.NEW_LINE);
		sb.append("*Error Token:* " + AppUtils.generateTimeString(AppConstants.ETKN) + SlackConstants.NEW_LINE);
		if (message != null) {
			sb.append("*Error Message:* " + AppUtils.generateTimeString(AppConstants.ETKN) + SlackConstants.NEW_LINE);
		}
		sb.append("*Error Notification:* " + ex.getMessage() + SlackConstants.NEW_LINE);
		sb.append("*Error Generation Timestamp:* " + AppUtils.getCurrentTimeISTString() + SlackConstants.NEW_LINE);
		sb.append("*Stacktrace:* "
				+ org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(ex).substring(0, 1000));
		return sb.toString();
	}

	public static <T, E> T clone(E object, Class<T> clazz) {
		Gson gson = new Gson();
		String str = gson.toJson(object);
		return gson.fromJson(str, clazz);
	}

	public static void slackIt(String userAgent, String url, EnvType envType, Exception ex) {
		try {
			String error = getFormattedMsg(userAgent, url, ex);
			SlackNotificationService.getInstance().sendNotification(envType,"Neo", null, SlackNotification.SYSTEM_ERRORS.getChannel(envType), error);
			LOG.error("{}\n{}", HttpStatus.INTERNAL_SERVER_ERROR.name(), error, ex);
		} catch (Exception e) {
			LOG.error("Slack is Failing", e);
		}
	}

	public static boolean isCOD(String source) {
		return AppConstants.COD.equals(source);
	}

	public static boolean isCafe(String source) {
		return AppConstants.CAFE.equals(source);
	}

	public static boolean isTakeAway(String source) {
		return AppConstants.TAKE_AWAY.equals(source);
	}
}
