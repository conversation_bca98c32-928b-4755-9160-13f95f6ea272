package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.KettleOrderService;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.Receipt;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.util.JSONSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class KettleOrderServiceImpl implements KettleOrderService {

	@Autowired
	EnvironmentProperties env;

	@Override
	public Order sendOrder(Order order) throws URISyntaxException {
		Map<String, Object> uriVariable = new HashMap<>();
		uriVariable.put("addMetadata", new Boolean(false));
		return WebServiceHelper.exchangeWithAuth(getKettleOrderEndPoint(), env.getNeoClientToken(), HttpMethod.POST,
				Order.class, JSONSerializer.toJSON(order), uriVariable);
	}

	@Override
	public Receipt getOrderReceipt(String orderId) throws Exception {
		Map<String, String> uriVariable = new HashMap<>();
		uriVariable.put("orderId", orderId);
		return WebServiceHelper.exchangeWithAuth(getKettleOrderReceiptDownloadEndPoint(),
				env.getNeoClientToken(), HttpMethod.GET,
				Receipt.class, null, uriVariable);
	}

	@Override
	public void publishCurrentOrderStatus(List<String> l) throws URISyntaxException {
		WebServiceHelper.exchangeWithAuth(getKettlePublishOrderStatusEndPoint(), env.getNeoClientToken(),
				HttpMethod.POST, String.class, JSONSerializer.toJSON(l), null);
	}

	private String getKettleOrderEndPoint() {
		return env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_ORDER;
	}

	private String getKettlePublishOrderStatusEndPoint() {
		return env.getKettleServiceBasePath() + KettleServiceClientEndpoints.PUBLISH_ORDER_STATUS_EVENT;
	}

	private String getKettleOrderReceiptDownloadEndPoint() {
		return env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_ORDER_RECEIPT;
	}


}