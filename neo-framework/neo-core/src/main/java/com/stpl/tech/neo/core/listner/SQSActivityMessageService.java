package com.stpl.tech.neo.core.listner;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;

import com.amazonaws.regions.Regions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.amazon.sqs.javamessaging.SQSSession;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoMetadataService;

@Service
public class SQSActivityMessageService {

    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private NeoMetadataService metaService;

    @PostConstruct
    public void init() throws JMSException {
        Regions region = Regions.valueOf(props.getAWSQueueRegion());
        SQSSession session = SQSNotification.getInstance().getSession(region);
        MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvType().name(),
                props.getApplicationIdentifier(), "_ACTIVITY_QUEUE");
        ActivityMessageListener listener = new ActivityMessageListener(producer, metaService, props);
        MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvType().name(),
                props.getApplicationIdentifier(), "_ACTIVITY_QUEUE");
        consumer.setMessageListener(listener);
        SQSNotification.getInstance().getSqs(region).start();
    }
}
