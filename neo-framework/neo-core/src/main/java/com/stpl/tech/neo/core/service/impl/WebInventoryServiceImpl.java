package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.WebInventoryService;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.domain.model.InventoryInfo;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.IdNameList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class WebInventoryServiceImpl implements WebInventoryService {

	@Autowired
	EnvironmentProperties props;

	@Override
	public Map<Integer, Integer> getUnitInventory(int unitId) {
		return WebServiceHelper.postWithAuth(getUnitInventoryEndPoint(), props.getNeoClientToken(), unitId, Map.class);
	}

	@Override
	public Map<Integer, InventoryInfo> getLiveUnitInventory(int unitId) {
		return WebServiceHelper.postWithAuth(getLiveUnitInventoryEndPoint(), props.getNeoClientToken(), unitId, Map.class);
	}

	@Override
	public Map<Integer, Integer> getUnitInventoryStatus(IdNameList data) {
		return WebServiceHelper.postWithAuth(getUnitInventoryStatusEndPoint(), props.getNeoClientToken(), data,
				Map.class);
	}

	private String getUnitInventoryStatusEndPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY_STATUS;
	}

	private String getUnitInventoryEndPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY;
	}

	private String getLiveUnitInventoryEndPoint() {
		return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY_LIVE;
	}

}
