package com.stpl.tech.neo.core.service.impl;

import com.stpl.tech.neo.core.cache.NeoMetadataCache;
import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.PaymentDetailDao;
import com.stpl.tech.neo.core.exception.CheckoutFailureException;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoCustomerService;
import com.stpl.tech.neo.core.service.WebPaymentService;
import com.stpl.tech.neo.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.neo.core.util.WebErrorCodes;
import com.stpl.tech.neo.core.util.WebOrderingConstants;
import com.stpl.tech.neo.domain.model.AGSPaymentResponse;
import com.stpl.tech.neo.domain.model.CartCheckoutRequest;
import com.stpl.tech.neo.domain.model.EzetapPaymentResponse;
import com.stpl.tech.neo.domain.model.OrderPaymentRequest;
import com.stpl.tech.neo.domain.model.PaymentPartner;
import com.stpl.tech.neo.domain.model.PaymentStatusChangeRequest;
import com.stpl.tech.neo.domain.model.PaymentVO;
import com.stpl.tech.neo.domain.model.RazorPayPaymentResponse;
import com.stpl.tech.neo.domain.model.mongo.*;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.ResponseCode;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jettison.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class WebPaymentServiceImpl extends AbstractDataValidator implements WebPaymentService {

	private static final Logger LOG = LoggerFactory.getLogger(WebPaymentServiceImpl.class);

	@Autowired
	private EnvironmentProperties env;

	@Autowired
	private NeoMetadataCache neoMetadataCache;

	@Autowired
	private NeoCustomerService customerService;

	@Autowired
	private CartDao dao;

	@Autowired
	private PaymentDetailDao paymentDao;

	@Override
	public Object createOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException {
		CartDetail cart = updateCart(req, serviceName);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		if (opr.getPaymentModeId() == WebOrderingConstants.PAYMENT_MODE_RAZOR_PAY) {
			RazorPayCreateRequest response = createRazorPayPayment(opr);
			addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(),
					response, cart.getOrderDetail().getTransactionDetail().getPaidAmount());
			return response;
		} else if (opr.getPaymentModeId() == WebOrderingConstants.PAYMENT_MODE_PAYTM) {
			return createPaytmPayment(opr);
		}
		return null;
	}

	public synchronized String generateNeoOrderId(String serviceName) {
		if(serviceName.equalsIgnoreCase(WebOrderingConstants.KIOSK_SERVICE)) {
			return AppUtils.generateWebOrderId(WebOrderingConstants.KIOSK_ORDER);
		} else {
			return AppUtils.generateWebOrderId(WebOrderingConstants.WEB_ORDER);
		}
	}

	@Override
	public boolean processPayment(RazorPayPaymentResponse request) {
		try {
			updatePaymentToKettle(request);
			return true;
		} catch (Exception e) {
			LOG.error("ERROR while updating payment", e);
		}
		return false;
	}

	@Override
	public boolean processEzetapPayment(EzetapPaymentResponse request) {
		try {
			updateEzetapPaymentToKettle(request);
			return true;
		} catch (Exception e) {
			LOG.error("ERROR while updating payment", e);
		}
		return false;
	}

	@Override
	protected NeoCustomerService getCustomerService() {
		return customerService;
	}

	@Override
	public void stampPaymentUpdate(Object request) throws URISyntaxException, WebOrderException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.STAMP_RAZOR_PAYMENT;
		String token = env.getNeoClientToken();
		WebServiceHelper.postWithAuth(endpoint, token, request, Object.class);
	}

	@Override
	public void stampPaymentUpdateForCovid(Object request) throws URISyntaxException, WebOrderException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.STAMP_RAZOR_PAYMENT_COVID;
		String token = env.getNeoClientToken();
		WebServiceHelper.postWithAuth(endpoint, token, request, Object.class);
	}

	@Override
	public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws URISyntaxException, JSONException {
		Map<String, Object> object = new HashMap<String, Object>();
		object.put("paymentId", paymentId);
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.FETCH_RAZOR_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, RazorPayPaymentResponse.class,
				JSONSerializer.toJSON(object), null);
	}

	@Override
	public PaytmCreateRequest createPaytmOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException {
		CartDetail cart = updateCart(req, serviceName);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		PaytmCreateRequest response = createPaytmPayment(opr);
		addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(), response,
				cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		return response;
	}

	@Override
	public RazorPayCreateRequest createRazorPayOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws WebOrderException, CheckoutFailureException, URISyntaxException {
		CartDetail cart = updateCart(req, serviceName);
		cancelExistingPaymentRequest(cart);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		RazorPayCreateRequest response = createRazorPayPayment(opr);
		addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(), response,
				cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		return response;
	}

	@Override
	public EzetapCreateRequest createEzetapOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws WebOrderException, CheckoutFailureException, URISyntaxException {
		CartDetail cart = updateCart(req, serviceName);
		cancelExistingPaymentRequest(cart);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		EzetapCreateRequest response = createEzetapPayment(opr);
		addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(), response,
				cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		return response;
	}

	@Override
	public boolean cancelPayment(PaymentStatusChangeRequest request) throws URISyntaxException {
		PaymentDetail detail = paymentDao.searchByExternalPaymentId(request.getReceiptId());
		if (detail != null) {
			if (PaymentStatus.INITIATED.name().equals(detail.getPaymentStatus())
					|| PaymentStatus.CREATED.name().equals(detail.getPaymentStatus())) {
				detail.setPaymentStatus(request.getStatus());
				paymentDao.save(detail);
			}
		}
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CANCEL_PAYMENT_REQUEST;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Boolean.class,
				JSONSerializer.toJSON(request), null);

	}

	@Override
	public boolean paymentFailure(PaymentStatusChangeRequest request) throws URISyntaxException {
		PaymentDetail detail = paymentDao.searchByExternalPaymentId(request.getReceiptId());
		if (detail != null) {
			if (PaymentStatus.INITIATED.name().equals(detail.getPaymentStatus())
					|| PaymentStatus.CREATED.name().equals(detail.getPaymentStatus())) {
				detail.setPaymentStatus(request.getStatus());
				paymentDao.save(detail);
			}
		}
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.FAILURE_PAYMENT_REQUEST;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Boolean.class,
				JSONSerializer.toJSON(request), null);
	}

	@Override
	public boolean getPaymentStatus(PaymentVO payment) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_PAYMENT_STATUS;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Boolean.class,
				JSONSerializer.toJSON(payment), null);

	}

	@Override
	public PaytmCreateRequest createPayTMQRCodeIdForKIOSK(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException {
		CartDetail cart = updateCart(req, serviceName);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		PaytmCreateRequest response = createPayTMQRCodeId(opr);
		addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(), response,
				cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		return response;
	}

	@Override
	public PaytmCreateRequest createPayTMUPIForKIOSK(CartCheckoutRequest req, String serviceName)
			throws URISyntaxException, WebOrderException, CheckoutFailureException {
		CartDetail cart = updateCart(req, serviceName);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		PaytmCreateRequest response = createPayTMUPI(opr);
		addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(), response,
				cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		return response;
	}

	private PaytmCreateRequest createPayTMUPI(OrderPaymentRequest request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_KIOSK_PAYTM_UPI_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, PaytmCreateRequest.class,
				JSONSerializer.toJSON(request), null);
	}

	@Override
	public PaytmPaymentStatus isPaytmPaymentCompleted(CartCheckoutRequest req, String serviceName){
		try{
			Optional<CartDetail> cartDetail = dao.findById(req.getCartId());
			CartDetail cart = cartDetail.orElse(null);
			OrderPaymentRequest orderPaymentRequest = new OrderPaymentRequest();
			orderPaymentRequest.setGenerateOrderId(cart.getOrderDetail().getExternalOrderId());
			orderPaymentRequest.setPosId(cart.getDeviceId());
			return checkPaytmPaymentStatus(orderPaymentRequest);
		}catch (Exception ex){
			LOG.error("Failed to fetch the paytm payment status");
			return null;
		}
	}

	@Override
	public AGSCreateRequest createAGSOrderPaymentRequest(CartCheckoutRequest req, String serviceName)
			throws WebOrderException, CheckoutFailureException, URISyntaxException {
		CartDetail cart = updateCart(req, serviceName);
		cancelExistingPaymentRequest(cart);
		OrderPaymentRequest opr = createOrderPaymentRequest(cart, req.getPaymentPartner().name(), serviceName);
		AGSCreateRequest response = createAGSPayment(opr);
		addPaymentDetail(req.getCartId(), cart.getOrderDetail().getExternalOrderId(), opr.getPaymentModeId(), response,
				cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		return response;
	}

	@Override
	public boolean processAGSPayment(AGSPaymentResponse request) {
		try {
			updateAGSPaymentToKettle(request);
			return true;
		} catch (Exception e) {
			LOG.error("ERROR while updating payment", e);
		}
		return false;
	}

	@Override
	public AGSPaymentCMResponse isAGSPaymentCompleted(String externalOrderId) {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CHECK_AGS_PAYMENT_STATUS;
		String token = env.getNeoClientToken();
		return WebServiceHelper.postWithAuth(endpoint, token, externalOrderId, AGSPaymentCMResponse.class);
	}

	private PaytmPaymentStatus checkPaytmPaymentStatus(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CHECK_KIOSK_PAYTM_PAYMENT_STATUS;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST,
				PaytmPaymentStatus.class, JSONSerializer.toJSON(request), null);
	}

	private PaytmCreateRequest createPayTMQRCodeId(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_KIOSK_PAYTM_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, PaytmCreateRequest.class,
				JSONSerializer.toJSON(request), null);
	}

	private OrderPaymentRequest createOrderPaymentRequest(CartDetail cart, String defPaymentGateway, String serviceName)
			throws WebOrderException {
		OrderPaymentRequest opr = new OrderPaymentRequest();
		opr.setGenerateOrderId(cart.getOrderDetail().getExternalOrderId());
		opr.setPaidAmount(cart.getOrderDetail().getTransactionDetail().getPaidAmount());
		opr.setPaymentModeId(getPaymentMode(defPaymentGateway));
		opr.setPaymentSource(serviceName);
		opr.setCartId(cart.getCartId());
		if (cart.getCustomerId() != null) {
			Customer customer = customerService.getCustomer(cart.getCustomerId());
			if (customer != null) {
				opr.setCustomerName(customer.getFirstName());
				opr.setContactNumber(customer.getContactNumber());
				opr.setCustomerId(customer.getId());
			}
		}
		opr.setRedirectUrl(getRedirectUrl(defPaymentGateway));
		if(StringUtils.isNotBlank(cart.getDeviceId())){
			opr.setPosId(cart.getDeviceId());
		}
		return opr;
	}

	private int getPaymentMode(String defPaymentGateway) throws WebOrderException {
		if (defPaymentGateway.equals(PaymentPartner.PAYTM.name())) {
			return WebOrderingConstants.PAYMENT_MODE_PAYTM;
		} else if (defPaymentGateway.equals(PaymentPartner.RAZOR_PAY.name())) {
			return WebOrderingConstants.PAYMENT_MODE_RAZOR_PAY;
		} else if (defPaymentGateway.equals(PaymentPartner.EZETAP.name())) {
			return WebOrderingConstants.PAYMENT_MODE_EZETAP;
		} else if (defPaymentGateway.equals(PaymentPartner.AGS.name())) {
			return WebOrderingConstants.PAYMENT_MODE_AGS;
		} else if (defPaymentGateway.equals(PaymentPartner.PAYTMQR.name())) {
			return WebOrderingConstants.PAYMENT_MODE_PAYTM_QR;
		} else {
			throwAxe("Invalid payment mode as no payment partner selected.");
			return 0;
		}
	}

	private String getRedirectUrl(String defPaymentGateway) throws WebOrderException {
		if (defPaymentGateway.equals(PaymentPartner.PAYTM.name())) {
			return env.getPaytmCallBackUrl();
		} else if (defPaymentGateway.equals(PaymentPartner.RAZOR_PAY.name())) {
			return env.getRazorPayRedirectUrl();
		} else if (defPaymentGateway.equals(PaymentPartner.EZETAP.name())) {
			return null;
		} else if (defPaymentGateway.equals(PaymentPartner.AGS.name())) {
			return null;
		} else if (defPaymentGateway.equals(PaymentPartner.PAYTMQR.name())) {
			return "";
		} else {
			throwAxe("No redirect resource as no payment partner selected.");
			return "";
		}
	}

	private Object createPayment(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, Object.class,
				JSONSerializer.toJSON(request), null);
	}

	private PaytmCreateRequest createPaytmPayment(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_PAYTM_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, PaytmCreateRequest.class,
				JSONSerializer.toJSON(request), null);
	}

	private RazorPayCreateRequest createRazorPayPayment(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_RAZORPAY_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, RazorPayCreateRequest.class,
				JSONSerializer.toJSON(request), null);

	}

	private EzetapCreateRequest createEzetapPayment(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_EZETAP_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, EzetapCreateRequest.class,
				JSONSerializer.toJSON(request), null);

	}

	private void addPaymentDetail(String cartId, String paymentId, Integer paymentMode, RazorPayCreateRequest response,
								  BigDecimal amount) {
		PaymentDetail detail = initiatePayment(cartId, paymentId, paymentMode, amount);
		detail.setRazorPayPayload(response);
		paymentDao.save(detail);
	}

	private void addPaymentDetail(String cartId, String paymentId, Integer paymentMode, EzetapCreateRequest response,
								  BigDecimal amount) {
		PaymentDetail detail = initiatePayment(cartId, paymentId, paymentMode, amount);
		detail.setEzetapPayload(response);
		paymentDao.save(detail);
	}

	private void addPaymentDetail(String cartId, String paymentId, Integer paymentMode, PaytmCreateRequest response,
								  BigDecimal amount) {
		PaymentDetail detail = initiatePayment(cartId, paymentId, paymentMode, amount);
		detail.setPaytmPayload(response);
		paymentDao.save(detail);
	}

	private void addPaymentDetail(String cartId, String paymentId, Integer paymentMode, AGSCreateRequest response,
								  BigDecimal amount) {
		PaymentDetail detail = initiatePayment(cartId, paymentId, paymentMode, amount);
		detail.setAgsPayload(response);
		paymentDao.save(detail);
	}

	private PaymentDetail initiatePayment(String cartId, String paymentId, Integer paymentMode, BigDecimal amount) {
		PaymentDetail detail = new PaymentDetail();
		detail.setCartId(cartId);
		detail.setExternalPaymentId(paymentId);
		detail.setPaymentModeId(paymentMode);
		detail.setAmount(amount);
		detail.setPaymentStatus(PaymentStatus.INITIATED.name());
		return detail;
	}

	private RazorPayPaymentResponse updatePaymentToKettle(RazorPayPaymentResponse request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.UPDATE_RAZOR_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.postWithAuth(endpoint, token, request, RazorPayPaymentResponse.class);
	}

	private EzetapPaymentResponse updateEzetapPaymentToKettle(EzetapPaymentResponse request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.UPDATE_EZETAP_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.postWithAuth(endpoint, token, request, EzetapPaymentResponse.class);
	}

	private CartDetail updateCart(CartCheckoutRequest req, String serviceName) throws WebOrderException, CheckoutFailureException {
		Optional<CartDetail> cartDetail = dao.findById(req.getCartId());
		CartDetail cart = cartDetail.orElse(null);
		updateCustomerIdAndName(cart);
		verifyOrder(cart, false, true);
		verifyPaymentStatus(cart);
		String generatedPaymentId = generateNeoOrderId(serviceName);
		cart.getOrderDetail().setExternalOrderId(generatedPaymentId);
		if(StringUtils.isNotBlank(req.getPosId())){
			cart.setDeviceId(req.getPosId());
		}
		cart = dao.save(cart);
		validateCart(cart);
		return cart;
	}

	private boolean isInterStateTaxApplicable(CartDetail cart){
		//checking interstate tax applicability
		boolean isInterState = false;
		if(cart.getOrderDetail().getSource()=="COD"){
			int unitStateId = neoMetadataCache.getDeliveryUnit(cart.getOrderDetail().getUnitId()).getLocation().getState().getId();
			cart.getOrderDetail().getAddress().getState();
			//customerService.getDeliveryAddress(cart.getOrderDetail().getDeliveryAddress())
			int deliveryStateId = neoMetadataCache.getCityToStateMapping().get(cart.getOrderDetail().getAddress().getCity());
			isInterState = deliveryStateId == unitStateId;
		}
		return isInterState;
	}

	private void cancelExistingPaymentRequest(CartDetail cart) throws URISyntaxException {
		if (cart != null) {
			List<PaymentDetail> details = paymentDao.searchByCartId(cart.getCartId());
			if (details != null && details.size() > 0) {
				for (PaymentDetail detail : details) {
					if (PaymentStatus.INITIATED.name().equals(detail.getPaymentStatus())
							|| PaymentStatus.CREATED.name().equals(detail.getPaymentStatus())) {
						detail.setPaymentStatus(PaymentStatus.AUTO_CANCELLED.name());
						paymentDao.save(detail);
						cancelPaymentRequest(detail.getExternalPaymentId(), "SYSTEM", "Existing Unattended Request",
								PaymentStatus.AUTO_CANCELLED);
					}
				}
			}
		}
	}

	private boolean cancelPaymentRequest(String receiptId, String cancelledBy, String reason, PaymentStatus status)
			throws URISyntaxException {
		PaymentStatusChangeRequest request = new PaymentStatusChangeRequest();
		request.setCancelledBy(cancelledBy);
		request.setCancellationReason(reason);
		request.setReceiptId(receiptId);
		request.setStatus(status.name());
		return cancelPayment(request);
	}

	private void validateCart(CartDetail cart) throws CheckoutFailureException {
		ResponseCode code = getResponseCode(cart);
		if (code != null && code.getCode() < 0) {
			throw new CheckoutFailureException(code);
		}
	}

	private AGSCreateRequest createAGSPayment(Object request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_AGS_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.POST, AGSCreateRequest.class,
				JSONSerializer.toJSON(request), null);

	}

	private AGSPaymentResponse updateAGSPaymentToKettle(AGSPaymentResponse request) throws URISyntaxException {
		String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.UPDATE_AGS_PAYMENT;
		String token = env.getNeoClientToken();
		return WebServiceHelper.postWithAuth(endpoint, token, request, AGSPaymentResponse.class);
	}

	protected void verifyPaymentStatus(CartDetail cart) throws WebOrderException {
		if (cart != null) {
			List<PaymentDetail> details = paymentDao.searchByCartId(cart.getCartId());
			if (details != null && details.size() > 0) {
				for (PaymentDetail detail : details) {
					if (PaymentStatus.SUCCESSFUL.name().equals(detail.getPaymentStatus())) {
						throwAxe(WebErrorCodes.PAYMENT_ALREADY_COMPLETED,
								"There is a successful payment request already associated with the cart");
					}
				}
			}
		}
	}

}
