package com.stpl.tech.neo.core.dao;

import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CartDao extends MongoRepository<CartDetail, String> {

	/**
	 * Order search using the transaction system id i.e. the bill number printed
	 * on customer invoice. aye! thats your kettle's generated order id. we know
	 * its written generateOrderId instead of generatedOrderId, but currently
	 * that's how things are, deal with it.
	 * 
	 * @param id
	 * @return
	 */
	@Query("{'orderDetail.generateOrderId' : ?0}")
	public CartDetail searchByGeneratedOrderId(String id);

	/**
	 * Order search using web ordering system id for an order, which is
	 * generated for payment management,to keep things in sync with transaction
	 * system this should be passed on to kettle as well .
	 * 
	 * @param id
	 * @return
	 */
	@Query("{'orderDetail.externalOrderId' : ?0}")
	public CartDetail searchByExternalOrderId(String id);

	/**
	 * Thing to be noted.
	 * 
	 * When Spring Data creates a new Repository implementation, it analyzes all
	 * the methods defined by the interfaces and tries to automatically generate
	 * queries from the method name. While this has limitations, it is a very
	 * powerful and elegant way of defining new custom access methods with very
	 * little effort. For example, if the managed entity has a name field (and
	 * the Java Bean standard getter and setter for that field), defining the
	 * findByName method in the DAO interface will automatically generate the
	 * correct query
	 * 
	 * <p>
	 * Due to this shit, we can only define method names based on the properties
	 * of the class, i.e findByVariableName, else the class will throw property
	 * not found exception, crazy yes! f# spring data reflection
	 * </p>
	 * 
	 * <p>
	 * method takes web ordering system's customer id which is mongo's id string
	 * with Pageable interface object. Pageable can be used to aggregate the
	 * data into multiple pages.
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@Query("{'customerId' : ?0 , 'cartStatus' :{$eq : 'SETTLED'}}")
	public Page<CartDetail> findByCustomerId(String id, Pageable request);

	/**
	 * Order search using Id generated by MySQL in kettle for the record. this
	 * will be unique for the database can be used when we need kettle order id
	 * to search an order
	 * 
	 * @param orderId
	 * @return
	 */
	@Query("{'orderDetail.orderId' : ?0}")
	public CartDetail findByKettleOrderId(String orderId);

	/**
	 * Get all settled carts by customer id in order to check if there is at least one successful
	 * order placed by customer
	 *
	 * @param id
	 * @return
	 */
	@Query("{'customerId' : ?0 , 'cartStatus' :{$eq : 'SETTLED'}}")
	public List<CartDetail> findSettledCartByCustomerId(String id);

	/**
	 * Get all settled carts where order status is not SETTLED or CANCELLED
	 * this is for cafe and take away orders
	 * @param id
	 * @return
	 */
	@Query("{'cartStatus' : {$eq : 'SETTLED'}, 'orderDetail.status' : { $nin : ['SETTLED','CANCELLED']}, 'orderDetail.source' : {$ne : 'COD'}}")
	public List<CartDetail> findUnSettledOrderCarts();

	/**
	 * Get all settled carts where order status is not DELIVERED or CANCELLED
	 * this is for delivery orders
	 * @param id
	 * @return
	 */
	@Query("{'cartStatus' : {$eq: 'SETTLED'}, 'orderDetail.status' : { $nin: ['DELIVERED','CANCELLED']}, 'orderDetail.source': {$eq: 'COD'}}")
	public List<CartDetail> findUnDeliveredOrderCarts();

}
