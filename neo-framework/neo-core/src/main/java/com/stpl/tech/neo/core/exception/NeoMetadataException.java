package com.stpl.tech.neo.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.OK, reason = "Metadata Collection Failure")
public class NeoMetadataException extends Exception {

	private static final long serialVersionUID = 1L;

	public NeoMetadataException() {
	}

	public NeoMetadataException(String message) {
		super(message);
	}

	public NeoMetadataException(Throwable cause) {
		super(cause);
	}

	public NeoMetadataException(String message, Throwable cause) {
		super(message, cause);
	}

	public NeoMetadataException(String message, Throwable cause, boolean enableSuppression,
			boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
}
