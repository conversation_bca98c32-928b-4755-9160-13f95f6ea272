package com.stpl.tech.neo.core.service;

import java.util.List;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartItem;

public interface CartService {

	public CartDetail createCart(CartDetail cartDetail);

	public boolean addToCart(CartItem item) throws WebOrderException;

	public boolean updateCartItem(CartItem item) throws WebOrderException;

	public boolean removeCartItem(CartItem item) throws WebOrderException;

	public boolean clearCart(String cartId);

	public CartDetail addReOrderItemsToCart(String gOrderId);

	public List<String> getUnsettledOrders();

	public List<String> getUndeliveredOrders();

	public void updateCart(CartDetail cartDetail);
}
