/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.neo.core.util;

import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import com.stpl.tech.neo.core.exception.AuthenticationFailureException;
import com.stpl.tech.util.AppConstants;

import io.jsonwebtoken.impl.Base64UrlCodec;

public class EncriptionHelper {

	public static String encryptUrlCodec(String value) throws AuthenticationFailureException {
		try {
			Key key = generateKey();
			Cipher cipher = Cipher.getInstance(AppConstants.ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, key);
			byte[] encryptedByteValue = cipher.doFinal(value.getBytes("utf-8"));
			String encryptedValue64 = new Base64UrlCodec().encode(encryptedByteValue);
			return encryptedValue64;
		} catch (Exception e) {
			throw new AuthenticationFailureException("Error while encrypting ", e);
		}
	}

	public static String decryptUrlCodec(String value) throws AuthenticationFailureException {
		try {
			Key key = generateKey();
			Cipher cipher = Cipher.getInstance(AppConstants.ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, key);
			byte[] decryptedValue64 = new Base64UrlCodec().decode(value);
			byte[] decryptedByteValue = cipher.doFinal(decryptedValue64);
			return new String(decryptedByteValue, "utf-8");
		} catch (Exception e) {
			throw new AuthenticationFailureException("Error while decrypting ", e);
		}
	}

	private static Key generateKey() throws Exception {
		Key key = new SecretKeySpec(AppConstants.PASSPHRASE_KEY.getBytes(), AppConstants.ALGORITHM);
		return key;
	}

	public static void main(String[] args) {
		try {
			String password = "123456";
			System.out.println("plain pass=" + password);
			String encryptedPassword = EncriptionHelper.encryptUrlCodec(password);
			System.out.println("encrypted pass=" + encryptedPassword);
			String decryptedPassword = EncriptionHelper.decryptUrlCodec(EncriptionHelper.encryptUrlCodec(password));
			System.out.println("decrypted pass=" + decryptedPassword);
		} catch (Exception e) {
			System.out.println("bug" + e.getMessage());
		}
	}
}