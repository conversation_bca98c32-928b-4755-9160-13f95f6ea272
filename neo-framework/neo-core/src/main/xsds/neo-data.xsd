<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="CategoryDetailData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" nillable="false" />
				<xs:element name="code" type="xs:string" />
				<xs:element name="shortCode" type="xs:string" />
				<xs:element name="description" type="xs:string" />
				<xs:element name="subCategories" type="SubCategoryDetailData" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="SubCategoryDetailData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" nillable="false" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" />
			<xs:element name="description" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ProductDetailData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" nillable="false" />
				<xs:element name="code" type="xs:string" />
				<xs:element name="price" type="xs:int" />
				<xs:element name="shortCode" type="xs:string" />
				<xs:element name="description" type="xs:string" />
				<xs:element name="image" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProductDimensionData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" nillable="false" />
				<xs:element name="code" type="xs:string" />
				<xs:element name="shortCode" type="xs:string" />
				<xs:element name="description" type="xs:string" />
				<xs:element name="image" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProductDimensionMappingData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="productId" type="xs:int" />
				<xs:element name="dimensionId" type="xs:int" />
				<xs:element name="price" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="AddonDetailData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" />
				<xs:element name="code" type="xs:string" />
				<xs:element name="description" type="xs:string" />
				<xs:element name="isPaid" type="xs:boolean" />
				<xs:element name="price" type="xs:int" nillable="true" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProductAddonMappingData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="productId" type="xs:int" />
				<xs:element name="addonId" type="xs:int" />
				<xs:element name="description" type="xs:string" />
				<xs:element name="isPaid" type="xs:boolean" />
				<xs:element name="price" type="xs:int" nillable="true" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
