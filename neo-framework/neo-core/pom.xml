<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>neo-framework</artifactId>
		<groupId>com.stpl.tech.neo</groupId>
		<version>5.0.15</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>neo-core</artifactId>
	<packaging>jar</packaging>
	<name>neo-core</name>
	<url>http://maven.apache.org</url>
    <properties>
		<spring-boot.repackage.skip>true</spring-boot.repackage.skip>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.stpl.tech.neo</groupId>
			<artifactId>neo-domain</artifactId>
			<version>5.0.15</version>
		</dependency>
		<dependency>
			<groupId>com.stpl.tech.redis</groupId>
			<artifactId>redis-core</artifactId>
			<version>5.0.15</version>
		</dependency>
		<!-- spring dependencies -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>ch.qos.logback</groupId>-->
<!--			<artifactId>logback-classic</artifactId>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>ch.qos.logback</groupId>-->
<!--			<artifactId>logback-core</artifactId>-->
<!--		</dependency>-->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.6.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-core</artifactId>
            <version>5.0.15</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
