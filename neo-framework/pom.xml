<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.stpl.tech</groupId>
		<artifactId>chaayos</artifactId>
		<version>5.0.15</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
    <groupId>com.stpl.tech.neo</groupId>
    <artifactId>neo-framework</artifactId>
    <packaging>pom</packaging>

    <name>neo-framework</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <modules>
        <module>neo-domain</module>
        <module>neo-core</module>
        <module>neo-admin</module>
        <module>neo-service</module>
    </modules>
</project>
