//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>Java class for ProductPrice complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ProductPrice"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="price" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="recipe" type="{http://www.w3schools.com}RecipeDetail"/&gt;
 *         &lt;element name="currentStock" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductPrice", propOrder = {
    "id",
    "dimension",
    "price",
    "recipe",
    "customize"
})
public class ProductPrice implements Serializable{

    private static final long serialVersionUID = 4265787306189944298L;
    protected int id;
    @XmlElement(required = true)
    protected String dimension;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal price;
    @XmlElement(required = true)
    protected RecipeDetail recipe;
    @XmlElement(required = true)
    protected String recipeId;
    protected Boolean customize;
    @XmlElement(required = true)
    protected Integer buffer;
    @XmlElement(required = true)
    protected Integer threshold;
    protected String profile;
    @XmlElement(required = true)
    protected Boolean isDeliveryOnlyProduct;
    @XmlElement(required = true)
    protected Boolean pickDineInConsumables;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the dimension property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDimension() {
        return dimension;
    }

    /**
     * Sets the value of the dimension property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDimension(String value) {
        this.dimension = value;
    }

    /**
     * Gets the value of the price property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * Sets the value of the price property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrice(BigDecimal value) {
        this.price = value;
    }

    /**
     * Gets the value of the recipe property.
     * 
     * @return
     *     possible object is
     *     {@link RecipeDetail }
     *     
     */
    public RecipeDetail getRecipe() {
        return recipe;
    }

    /**
     * Sets the value of the recipe property.
     * 
     * @param value
     *     allowed object is
     *     {@link RecipeDetail }
     *     
     */
    public void setRecipe(RecipeDetail value) {
        this.recipe = value;
    }

   

	public Integer getBuffer() {
		return buffer;
	}

	public void setBuffer(Integer buffer) {
		this.buffer = buffer;
	}

	public Integer getThreshold() {
		return threshold;
	}

	public void setThreshold(Integer threshold) {
		this.threshold = threshold;
	}

	public Boolean isCustomize() {
		return customize;
	}

	public void setCustomize(Boolean customize) {
		this.customize = customize;
	}

	public String getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(String recipeId) {
		this.recipeId = recipeId;
	}

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public Boolean getIsDeliveryOnlyProduct() {
        return isDeliveryOnlyProduct;
    }

    public void setIsDeliveryOnlyProduct(Boolean deliveryOnlyProduct) {
        this.isDeliveryOnlyProduct = deliveryOnlyProduct;
    }

    public Boolean getPickDineInConsumables() {
        return pickDineInConsumables;
    }

    public void setPickDineInConsumables(Boolean pickDineInConsumables) {
        this.pickDineInConsumables = pickDineInConsumables;
    }
}
