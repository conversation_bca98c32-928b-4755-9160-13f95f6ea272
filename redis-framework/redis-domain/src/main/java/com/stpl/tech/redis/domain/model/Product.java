//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//

package com.stpl.tech.redis.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for Product complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Product"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="strategy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attribute" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="billType" type="{http://www.w3schools.com}BillType"/&gt;
 *         &lt;element name="prices" type="{http://www.w3schools.com}ProductPrice" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Product", propOrder = { "id", "name", "description", "strategy", "attribute", "billType", "prices" ,"customize","taxCode","skuCode","subType","productAliasName"})
@RedisHash("Product")
public class Product implements Serializable {

	private static final long serialVersionUID = -2627944408751884241L;
	@Id
	protected int id;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String description;
	protected int strategy;
	protected Integer webType;
	@XmlElement(required = true)
	protected String attribute;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected BillType billType;
	protected List<ProductPrice> prices;
	protected boolean customize;
	@XmlElement(required = true)
	protected String taxCode;
	@XmlElement(required = true)
	protected String skuCode;
	protected int subType;
	protected List<IndexUrl> marketingImage;
	protected List<IndexUrl> marketingImageWebView;

	protected String productAliasName;

	/**
	 * Gets the value of the id property.
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the description property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Sets the value of the description property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDescription(String value) {
		this.description = value;
	}

	public int getStrategy() {
		return strategy;
	}

	public void setStrategy(int strategy) {
		this.strategy = strategy;
	}

	/**
	 * Gets the value of the subType property.
	 * 
	 */
	public Integer getWebType() {
		return webType;
	}

	/**
	 * Sets the value of the subType property.
	 * 
	 */
	public void setWebType(Integer value) {
		this.webType = value;
	}

	/**
	 * Gets the value of the attribute property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAttribute() {
		return attribute;
	}

	/**
	 * Sets the value of the attribute property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAttribute(String value) {
		this.attribute = value;
	}

	/**
	 * Gets the value of the billType property.
	 *
	 * @return possible object is {@link BillType }
	 *
	 */
	public BillType getBillType() {
		return billType;
	}

	/**
	 * Sets the value of the billType property.
	 *
	 * @param value
	 *            allowed object is {@link BillType }
	 *
	 */
	public void setBillType(BillType value) {
		this.billType = value;
	}

	/**
	 * Gets the value of the prices property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the prices property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getPrices().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link ProductPrice }
	 * 
	 *
	 */
	public List<ProductPrice> getPrices() {
		if (prices == null) {
			prices = new ArrayList<ProductPrice>();
		}
		return this.prices;
	}

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

	public String getTaxCode() {
		return taxCode;
	} 

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	public String getSkuCode() {
		return skuCode;
	}

	public void setSkuCode(String skuCode) {
		this.skuCode = skuCode;
	}

	public int getSubType() {
		return subType;
	}

	public void setSubType(int subType) {
		this.subType = subType;
	}

	public List<IndexUrl> getMarketingImage() {
		if(marketingImage== null){
			marketingImage= new ArrayList<>();
		}
		return this.marketingImage;
	}

	public void setMarketingImage(List<IndexUrl> marketingImage) {
		this.marketingImage = marketingImage;
	}

	public List<IndexUrl> getMarketingImageWebView() {
		if(marketingImageWebView== null){
			marketingImageWebView= new ArrayList<>();
		}
		return this.marketingImageWebView;
	}

	public void setMarketingImageWebView(List<IndexUrl> marketingImageWebView) {
		this.marketingImageWebView = marketingImageWebView;
	}

	public void setProductAliasName(String productAliasName) {
		this.productAliasName = productAliasName;
	}

	public String getProductAliasName() {
		return productAliasName;
	}
}
