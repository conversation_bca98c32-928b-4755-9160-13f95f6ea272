/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.redis.domain.model;

import java.math.BigDecimal;

public class BigDecimalAdapter {

	public static BigDecimal parseBigDecimal(String val){
		BigDecimal decimal = new BigDecimal(val);
		return decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	public static String printBigDecimal(BigDecimal decimal) {
		return decimal.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	public static BigDecimal parseBigDecimalToSix(String val){
		BigDecimal decimal = new BigDecimal(val);
		return decimal.setScale(6, BigDecimal.ROUND_HALF_UP);
	}

	public static String printBigDecimalToSix(BigDecimal decimal) {
		return decimal.setScale(6, BigDecimal.ROUND_HALF_UP).toString();
	}
}
