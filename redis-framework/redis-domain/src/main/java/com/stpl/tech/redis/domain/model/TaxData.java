/**
 * 
 */
package com.stpl.tech.redis.domain.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class TaxData {

	protected String taxCode;

	protected StateTax state;

	protected List<AdditionalTax> others;

	public StateTax getState() {
		return state;
	}

	public void setState(StateTax state) {
		this.state = state;
	}

	public List<AdditionalTax> getOthers() {
		if (others == null) {
			others = new ArrayList<>();
		}
		return others;
	}

	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

}
