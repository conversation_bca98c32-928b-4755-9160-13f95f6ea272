/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.redis.domain.model;

import java.util.ArrayList;
import java.util.List;

public class ProductImageMappingDetail implements java.io.Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 7542735268463811173L;
    private Integer productId;
    private String trendLow;
    private String trendHigh;
    private String specialLow;
    private String specialHigh;
    //	private String gridLow;
//	private List<String> gridLows;
    private IndexUrl gridLow;
    private List<IndexUrl> gridLows;
    private IndexUrl gridLowWebp;
    private List<IndexUrl> gridLowsWebp;
    private String gridHigh;
    private String listLow;
    private String listHigh;
    private String comboLow;
    private String comboHigh;
    private String showcaseVideo;
    private IndexUrl marketingImage;
    private IndexUrl marketingImageWebView;
    private List<IndexUrl> marketingImages;
    private List<IndexUrl> marketingImageWebViews;

    public ProductImageMappingDetail() {
    }

    public ProductImageMappingDetail(Integer productId) {
        this.productId = productId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getTrendLow() {
        return trendLow;
    }

    public void setTrendLow(String trendLow) {
        this.trendLow = trendLow;
    }

    public IndexUrl getGridLowWebp() {
        return gridLowWebp;
    }

    public void setGridLowWebp(IndexUrl gridLowWebp) {
        this.gridLowWebp = gridLowWebp;
    }

    public List<IndexUrl> getGridLowsWebp() {
        if(gridLowsWebp == null){
            gridLowsWebp = new ArrayList<IndexUrl>();
        }
        return gridLowsWebp;
    }

    public void setGridLowsWebp(List<IndexUrl> gridLowsWebp) {
        this.gridLowsWebp = gridLowsWebp;
    }
    
    public String getTrendHigh() {
        return trendHigh;
    }

    public void setTrendHigh(String trendHigh) {
        this.trendHigh = trendHigh;
    }

    public String getSpecialLow() {
        return specialLow;
    }

    public void setSpecialLow(String specialLow) {
        this.specialLow = specialLow;
    }

    public String getSpecialHigh() {
        return specialHigh;
    }

    public void setSpecialHigh(String specialHigh) {
        this.specialHigh = specialHigh;
    }

//	public String getGridLow() {
//		return gridLow;
//	}
//
//	public void setGridLow(String gridLow) {
//		this.gridLow = gridLow;
//	}

    public String getGridHigh() {
        return gridHigh;
    }

    public void setGridHigh(String gridHigh) {
        this.gridHigh = gridHigh;
    }

    public String getListLow() {
        return listLow;
    }

    public void setListLow(String listLow) {
        this.listLow = listLow;
    }

    public String getListHigh() {
        return listHigh;
    }

    public void setListHigh(String listHigh) {
        this.listHigh = listHigh;
    }

    public String getComboLow() {
        return comboLow;
    }

    public void setComboLow(String comboLow) {
        this.comboLow = comboLow;
    }

    public String getComboHigh() {
        return comboHigh;
    }

    public void setComboHigh(String comboHigh) {
        this.comboHigh = comboHigh;
    }

    public String getShowcaseVideo() {
        return showcaseVideo;
    }

    public void setShowcaseVideo(String showcaseVideo) {
        this.showcaseVideo = showcaseVideo;
    }

    public IndexUrl getGridLow() {
        return gridLow;
    }

    public void setGridLow(IndexUrl gridLow) {
        this.gridLow = gridLow;
    }

    public List<IndexUrl> getGridLows() {
        if (gridLows == null) {
            gridLows = new ArrayList<IndexUrl>();
        }
        return gridLows;
    }

    public void setGridLows(List<IndexUrl> gridLows) {
        this.gridLows = gridLows;
    }

    public IndexUrl getMarketingImage() {
        return marketingImage;
    }

    public void setMarketingImage(IndexUrl marketingImage) {
        this.marketingImage = marketingImage;
    }

    public IndexUrl getMarketingImageWebView() {
        return marketingImageWebView;
    }

    public void setMarketingImageWebView(IndexUrl marketingImageWebView) {
        this.marketingImageWebView = marketingImageWebView;
    }

    public List<IndexUrl> getMarketingImages() {
        return marketingImages;
    }

    public void setMarketingImages(List<IndexUrl> marketingImages) {
        this.marketingImages = marketingImages;
    }

    public List<IndexUrl> getMarketingImageWebViews() {
        return marketingImageWebViews;
    }

    public void setMarketingImageWebViews(List<IndexUrl> marketingImageWebViews) {
        this.marketingImageWebViews = marketingImageWebViews;
    }
}
