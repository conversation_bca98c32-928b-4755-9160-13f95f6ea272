//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="products" type="{http://www.w3schools.com}Product" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="taxProfiles" type="{http://www.w3schools.com}TaxProfile" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="operationalHours" type="{http://www.w3schools.com}UnitHours" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "id",
    "name",
    "products",
    "taxProfiles",
    "operationalHours",
    "tableService",
    "tableServiceType",
    "noOfTables"
})
@XmlRootElement(name = "Unit")
@RedisHash("Unit")
public class Unit implements Serializable {

    private static final long serialVersionUID = 9084424039712033288L;

    @Id
    protected Integer id;
    @XmlElement(required = true)
    protected String name;
    protected List<Product> products;
    protected List<TaxData> taxes;
    protected List<TaxProfile> taxProfiles;
    protected List<UnitHours> operationalHours;
    @XmlElement(required = true)
    protected Location location;
    protected String packagingType;
    private BigDecimal packagingValue;

    protected List<Product> subscriptionProducts;
//    protected Map<String, Map<String,List<IndexUrl>>>skuProductImageMap;

    protected boolean tableService;
    protected int tableServiceType;
    @XmlElement(defaultValue = "1")
    protected int noOfTables;

	public Unit() {
	}    

	public Unit(Integer id, String name, List<Product> products, List<TaxProfile> taxProfiles,
			List<UnitHours> operationalHours, List<TaxData> taxes, Location location, String packagingType, BigDecimal packagingValue) {
		super();
		this.id = id;
		this.name = name;
		this.products = products;
		this.taxProfiles = taxProfiles;
		this.operationalHours = operationalHours;
        this.taxes = taxes;
        this.location = location;
        this.packagingType = packagingType;
        this.packagingValue = packagingValue;
	}

	/**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the products property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the products property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getProducts().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Product }
     * 
     * 
     */
    public List<Product> getProducts() {
        if (products == null) {
            products = new ArrayList<Product>();
        }
        return this.products;
    }

    
    public List<TaxData> getTaxes() {
    	if (taxes == null) {
    		taxes = new ArrayList<TaxData>();
        }
		return taxes;
	}

    public List<Product> getSubscriptionProducts() {
        if (subscriptionProducts == null) {
            subscriptionProducts = new ArrayList<>();
        }
        return subscriptionProducts;
    }

    /**
     * Gets the value of the taxProfiles property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the taxProfiles property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTaxProfiles().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TaxProfile }
     * 
     * 
     */
    public List<TaxProfile> getTaxProfiles() {
        if (taxProfiles == null) {
            taxProfiles = new ArrayList<TaxProfile>();
        }
        return this.taxProfiles;
    }

    /**
     * Gets the value of the operationalHours property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the operationalHours property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOperationalHours().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link UnitHours }
     * 
     * 
     */
    public List<UnitHours> getOperationalHours() {
        if (operationalHours == null) {
            operationalHours = new ArrayList<UnitHours>();
        }
        return this.operationalHours;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    public String getPackagingType() {
        return packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    public BigDecimal getPackagingValue() {
        return packagingValue;
    }

    public void setPackagingValue(BigDecimal packagingValue) {
        this.packagingValue = packagingValue;
    }

    public void setTableService(boolean tableService) {
        this.tableService = tableService;
    }

    public void setTableServiceType(int tableServiceType) {
        this.tableServiceType = tableServiceType;
    }

    public void setNoOfTables(int noOfTables) {
        this.noOfTables = noOfTables;
    }

    public boolean isTableService() {
        return tableService;
    }

    public int getTableServiceType() {
        return tableServiceType;
    }

    public int getNoOfTables() {
        return noOfTables;
    }

    @Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Unit other = (Unit) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
    public String toString() {
        return "Unit{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", products=" + products +
            ", taxProfiles=" + taxProfiles +
            ", unitHours=" + operationalHours +
            '}';
    }
}
