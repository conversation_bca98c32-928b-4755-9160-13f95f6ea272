//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.10.01 at 01:01:52 PM IST
//


package com.stpl.tech.redis.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;
import retrofit2.http.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for RecipeDetail complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="RecipeDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="recipeId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ingredient" type="{http://www.w3schools.com}IngredientDetail"/&gt;
 *         &lt;element name="addons" type="{http://www.w3schools.com}IngredientProductDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeDetail", propOrder = {
    "recipeId",
    "dimension",
    "ingredient",
    "recommendations",
    "addons"
})
@RedisHash("Recipe")
public class RecipeDetail implements Serializable {

    private static final long serialVersionUID = -8396561455897629757L;

    @XmlElement(required = true)
    @Id
    protected String recipeId;
    @XmlElement(required = true)
    protected BasicInfo dimension;
    protected IngredientDetail ingredient;
    protected List<IngredientProductDetail> addons;
    protected List<IngredientProductDetail> recommendations;
    protected List<OptionData> options;
    protected int customizationCount;
    protected String profile;


    /**
     * Gets the value of the recipeId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRecipeId() {
        return recipeId;
    }

    /**
     * Sets the value of the recipeId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRecipeId(String value) {
        this.recipeId = value;
    }

    /**
     * Gets the value of the ingredient property.
     *
     * @return
     *     possible object is
     *     {@link IngredientDetail }
     *
     */
    public IngredientDetail getIngredient() {
        return ingredient;
    }

    /**
     * Sets the value of the ingredient property.
     *
     * @param value
     *     allowed object is
     *     {@link IngredientDetail }
     *
     */
    public void setIngredient(IngredientDetail value) {
        this.ingredient = value;
    }

    /**
     * Gets the value of the addons property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the addons property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAddons().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IngredientProductDetail }
     *
     *
     */
    public List<IngredientProductDetail> getAddons() {
        if (addons == null) {
            addons = new ArrayList<IngredientProductDetail>();
        }
        return this.addons;
    }

    public List<IngredientProductDetail> getRecommendations() {
        if (recommendations == null) {
        	recommendations = new ArrayList<IngredientProductDetail>();
        }
        return this.recommendations;
    }

	public int getCustomizationCount() {
		return customizationCount;
	}

	public void setCustomizationCount(int customizationCount) {
		this.customizationCount = customizationCount;
	}

	public BasicInfo getDimension() {
		return dimension;
	}

	public void setDimension(BasicInfo dimension) {
		this.dimension = dimension;
	}

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public void setAddons(List<IngredientProductDetail> addons) {
        this.addons = addons;
    }

    public void setRecommendations(List<IngredientProductDetail> recommendations) {
        this.recommendations = recommendations;
    }

    public List<OptionData> getOptions() {
        return options;
    }

    public void setOptions(List<OptionData> options) {
        this.options = options;
    }
}
