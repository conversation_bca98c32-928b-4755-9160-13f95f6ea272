package com.stpl.tech.redis.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 13-11-2016.
 */
@XmlType(name = "ProductRecipes", propOrder = {
    "productId",
    "recipes"
})
@RedisHash("ProductRecipes")
public class ProductRecipes implements Serializable {

    private static final long serialVersionUID = -3542005504221918294L;

    @XmlElement(required = true)
    @Id
    protected Integer productId;

    private List<RecipeDetail> recipes;

    public ProductRecipes(){}

    public ProductRecipes(Integer productId, List<RecipeDetail> recipes){
        this.productId = productId;
        this.recipes = recipes;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<RecipeDetail> getRecipes() {
        return recipes;
    }

    public void setRecipes(List<RecipeDetail> recipes) {
        this.recipes = recipes;
    }
}
