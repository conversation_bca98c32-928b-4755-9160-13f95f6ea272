//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ProductClassification.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="ProductClassification"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="MENU"/&gt;
 *     &lt;enumeration value="PAID_ADDON"/&gt;
 *     &lt;enumeration value="FREE_ADDON"/&gt;
 *     &lt;enumeration value="VARIANT"/&gt;
 *     &lt;enumeration value="PRODUCT_VARIANT"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "ProductClassification")
@XmlEnum
public enum ProductClassification {

    MENU("MENU"),
    @XmlEnumValue("PAID_ADDON")
    PAID_ADDON("PAID_ADDON"),
    @XmlEnumValue("FREE_ADDON")
    FREE_ADDON("FREE_ADDON"),
    VARIANT("VARIANT"),
    @XmlEnumValue("PRODUCT_VARIANT")
    PRODUCT_VARIANT("PRODUCT_VARIANT"),
    OTHERS("OTHERS"),
    MEALS("MEALS");
    private final String value;

    ProductClassification(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static ProductClassification fromValue(String v) {
        for (ProductClassification c: ProductClassification.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
