//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for TaxType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="TaxType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="NET_PRICE_VAT"/&gt;
 *     &lt;enumeration value="MRP_VAT"/&gt;
 *     &lt;enumeration value="SURCHARGE"/&gt;
 *     &lt;enumeration value="SERVICE_TAX"/&gt;
 *     &lt;enumeration value="SB_CESS"/&gt;
 *     &lt;enumeration value="KK_CESS"/&gt;
 *     &lt;enumeration value="GST"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "TaxType")
@XmlEnum
public enum TaxType {

    @XmlEnumValue("NET_PRICE_VAT")
    NET_PRICE_VAT("NET_PRICE_VAT"),
    @XmlEnumValue("MRP_VAT")
    MRP_VAT("MRP_VAT"),
    SURCHARGE("SURCHARGE"),
    @XmlEnumValue("SERVICE_TAX")
    SERVICE_TAX("SERVICE_TAX"),
    @XmlEnumValue("SB_CESS")
    SB_CESS("SB_CESS"),
    @XmlEnumValue("KK_CESS")
    KK_CESS("KK_CESS"),
    GST("GST");
    private final String value;

    TaxType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static TaxType fromValue(String v) {
        for (TaxType c: TaxType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
