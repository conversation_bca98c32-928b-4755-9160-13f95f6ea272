package com.stpl.tech.redis.domain.model;

import java.util.ArrayList;
import java.util.List;

public class MarketingImages {
    private List<IndexUrl> marketingImageMobView;
    private List<IndexUrl> marketingImageWebView;

    public List<IndexUrl> getMarketingImageMobView() {
        if(marketingImageMobView==null){
         marketingImageMobView = new ArrayList<IndexUrl>();
        }
        return marketingImageMobView;
    }

    public void setMarketingImageMobView(List<IndexUrl> marketingImageMobView) {
        this.marketingImageMobView = marketingImageMobView;
    }

    public List<IndexUrl> getMarketingImageWebView() {
        if(marketingImageWebView==null){
            marketingImageWebView=new ArrayList<>();
        }
        return marketingImageWebView;
    }

    public void setMarketingImageWebView(List<IndexUrl> marketingImageWebView) {
        this.marketingImageWebView = marketingImageWebView;
    }
}
