package com.stpl.tech.redis.domain.model;

import java.util.ArrayList;
import java.util.List;

public class UnitSubscriptionProductsImageMapping {
    private int unitId ;
    private List<Product> subscriptionProducts;
    private List<SubscriptionProductImageMapping> subscriptionProductImageMappingList;

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public List<Product> getSubscriptionProducts() {
        if(subscriptionProducts==null){
            subscriptionProducts= new ArrayList<>();
        }
        return subscriptionProducts;
    }

    public void setSubscriptionProducts(List<Product> subscriptionProducts) {
        this.subscriptionProducts = subscriptionProducts;
    }

    public List<SubscriptionProductImageMapping> getSubscriptionProductImageMappingList() {
        if(subscriptionProductImageMappingList==null){
            subscriptionProductImageMappingList=new ArrayList<>();
        }
        return subscriptionProductImageMappingList;
    }

    public void setSubscriptionProductImageMappingList(List<SubscriptionProductImageMapping> subscriptionProductImageMappingList) {
        this.subscriptionProductImageMappingList = subscriptionProductImageMappingList;
    }
}
