//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.neo.domain.model.redis package.
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.neo.domain.model.redis
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Unit }
     * 
     */
    public Unit createUnit() {
        return new Unit();
    }

    /**
     * Create an instance of {@link Product }
     * 
     */
    public Product createProduct() {
        return new Product();
    }

    /**
     * Create an instance of {@link TaxProfile }
     * 
     */
    public TaxProfile createTaxProfile() {
        return new TaxProfile();
    }

    /**
     * Create an instance of {@link UnitHours }
     * 
     */
    public UnitHours createUnitHours() {
        return new UnitHours();
    }

    /**
     * Create an instance of {@link ProductPrice }
     * 
     */
    public ProductPrice createProductPrice() {
        return new ProductPrice();
    }

    /**
     * Create an instance of {@link RecipeDetail }
     * 
     */
    public RecipeDetail createRecipeDetail() {
        return new RecipeDetail();
    }

    /**
     * Create an instance of {@link IngredientDetail }
     * 
     */
    public IngredientDetail createIngredientDetail() {
        return new IngredientDetail();
    }

    /**
     * Create an instance of {@link IngredientProductDetail }
     * 
     */
    public IngredientProductDetail createIngredientProductDetail() {
        return new IngredientProductDetail();
    }

    /**
     * Create an instance of {@link CompositeProductData }
     * 
     */
    public CompositeProductData createCompositeProductData() {
        return new CompositeProductData();
    }

    /**
     * Create an instance of {@link IngredientProduct }
     * 
     */
    public IngredientProduct createIngredientProduct() {
        return new IngredientProduct();
    }

    /**
     * Create an instance of {@link IngredientVariant }
     * 
     */
    public IngredientVariant createIngredientVariant() {
        return new IngredientVariant();
    }

    /**
     * Create an instance of {@link ProductData }
     * 
     */
    public ProductData createProductData() {
        return new ProductData();
    }

    /**
     * Create an instance of {@link BasicInfo }
     * 
     */
    public BasicInfo createBasicInfo() {
        return new BasicInfo();
    }

    /**
     * Create an instance of {@link CompositeIngredientData }
     * 
     */
    public CompositeIngredientData createCompositeIngredientData() {
        return new CompositeIngredientData();
    }

    /**
     * Create an instance of {@link IngredientVariantDetail }
     * 
     */
    public IngredientVariantDetail createIngredientVariantDetail() {
        return new IngredientVariantDetail();
    }

}
