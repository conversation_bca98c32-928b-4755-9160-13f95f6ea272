//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for IngredientDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="IngredientDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="compositeProduct" type="{http://www.w3schools.com}CompositeProductData"/&gt;
 *         &lt;element name="products" type="{http://www.w3schools.com}IngredientProduct" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="variants" type="{http://www.w3schools.com}IngredientVariant" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="components" type="{http://www.w3schools.com}IngredientProductDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientDetail", propOrder = {
    "compositeProduct",
    "products",
    "variants",
    "components"
})
public class IngredientDetail implements Serializable {

    private static final long serialVersionUID = 3100854599737560007L;
    @XmlElement(required = true)
    protected CompositeProductData compositeProduct;
    protected List<IngredientProduct> products;
    protected List<IngredientVariant> variants;
    protected List<IngredientProductDetail> components;

    /**
     * Gets the value of the compositeProduct property.
     * 
     * @return
     *     possible object is
     *     {@link CompositeProductData }
     *     
     */
    public CompositeProductData getCompositeProduct() {
        return compositeProduct;
    }

    /**
     * Sets the value of the compositeProduct property.
     * 
     * @param value
     *     allowed object is
     *     {@link CompositeProductData }
     *     
     */
    public void setCompositeProduct(CompositeProductData value) {
        this.compositeProduct = value;
    }

    /**
     * Gets the value of the products property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the products property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getProducts().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IngredientProduct }
     * 
     * 
     */
    public List<IngredientProduct> getProducts() {
        if (products == null) {
            products = new ArrayList<IngredientProduct>();
        }
        return this.products;
    }

    /**
     * Gets the value of the variants property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the variants property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getVariants().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IngredientVariant }
     * 
     * 
     */
    public List<IngredientVariant> getVariants() {
        if (variants == null) {
            variants = new ArrayList<IngredientVariant>();
        }
        return this.variants;
    }

    /**
     * Gets the value of the components property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the components property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getComponents().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IngredientProductDetail }
     * 
     * 
     */
    public List<IngredientProductDetail> getComponents() {
        if (components == null) {
            components = new ArrayList<IngredientProductDetail>();
        }
        return this.components;
    }

    public void setProducts(List<IngredientProduct> products) {
        this.products = products;
    }

    public void setVariants(List<IngredientVariant> variants) {
        this.variants = variants;
    }

    public void setComponents(List<IngredientProductDetail> components) {
        this.components = components;
    }
}
