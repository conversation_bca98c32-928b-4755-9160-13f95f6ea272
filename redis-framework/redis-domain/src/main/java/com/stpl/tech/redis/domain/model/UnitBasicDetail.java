//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.17 at 07:23:46 PM IST 
//


package com.stpl.tech.redis.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;


/**
 * <p>Java class for UnitBasicDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitBasicDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="region" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitBasicDetail", propOrder = {
    "id",
    "name",
    "category",
    "subCategory",
    "region",
    "city",
    "status"
})
@RedisHash("UnitBasicDetail")
public class UnitBasicDetail {

	@Id
    protected int id;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String category;
    @XmlElement(required = true)
    protected String subCategory;
    @XmlElement(required = true)
    protected String region;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected boolean live;
    @XmlElement(required = true)
    protected boolean liveInventoryEnabled;
    @XmlElement(required = true)
	private Integer unitManagerId;
    @XmlElement(required = true)
	private Integer cafeManagerId;
    @XmlElement(required = true)
    private String packagingType;
    @XmlElement(required = true)
    private BigDecimal packagingValue;
    @XmlElement(required = true)
    private String cafeAppStatus;
    @XmlElement(required = true)
    private String cafeNeoStatus;
    private String unitZone;
    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the category property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the subCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSubCategory() {
        return subCategory;
    }

    /**
     * Sets the value of the subCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSubCategory(String value) {
        this.subCategory = value;
    }

    /**
     * Gets the value of the region property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRegion() {
        return region;
    }

    /**
     * Sets the value of the region property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRegion(String value) {
        this.region = value;
    }

    /**
     * Gets the value of the region property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the region property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }
    
    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }
    
	public boolean isLive() {
		return live;
	}

	public void setLive(boolean live) {
		this.live = live;
	}

	public boolean isLiveInventoryEnabled() {
		return liveInventoryEnabled;
	}

	public void setLiveInventoryEnabled(boolean liveInventoryEnabled) {
		this.liveInventoryEnabled = liveInventoryEnabled;
	}

	public Integer getUnitManagerId() {
		return unitManagerId;
	}

	public void setUnitManagerId(Integer unitManagerId) {
		this.unitManagerId = unitManagerId;
	}

	public Integer getCafeManagerId() {
		return cafeManagerId;
	}

	public void setCafeManagerId(Integer cafeManagerId) {
		this.cafeManagerId = cafeManagerId;
	}

    public String getPackagingType() {
        return packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    public BigDecimal getPackagingValue() {
        return packagingValue;
    }

    public void setPackagingValue(BigDecimal packagingValue) {
        this.packagingValue = packagingValue;
    }

    public String getCafeAppStatus() {
        return cafeAppStatus;
    }

    public void setCafeAppStatus(String cafeAppStatus) {
        this.cafeAppStatus = cafeAppStatus;
    }

    public String getCafeNeoStatus() {
        return cafeNeoStatus;
    }

    public void setCafeNeoStatus(String cafeNeoStatus) {
        this.cafeNeoStatus = cafeNeoStatus;
    }

    public String getUnitZone() {
        return unitZone;
    }

    public void setUnitZone(String unitZone) {
        this.unitZone = unitZone;
    }
}
