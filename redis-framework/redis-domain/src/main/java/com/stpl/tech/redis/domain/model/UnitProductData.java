/**
 * 
 */
package com.stpl.tech.redis.domain.model;

import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
public class UnitProductData {

	protected Collection<Product> products;
	protected Collection<TaxData> taxes;

	public Collection<TaxData> getTaxes() {
		if (taxes == null) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public Collection<Product> getProducts() {
		if (products == null) {
			products = new ArrayList<>();
		}
		return products;
	}

}
