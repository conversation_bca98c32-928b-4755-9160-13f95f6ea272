//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.redis.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for IngredientVariant complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="IngredientVariant"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="product" type="{http://www.w3schools.com}ProductData"/&gt;
 *         &lt;element name="uom" type="{http://www.w3schools.com}UnitOfMeasure"/&gt;
 *         &lt;element name="captured" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="details" type="{http://www.w3schools.com}IngredientVariantDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientVariant", propOrder = {
    "product",
    "uom",
    "captured",
    "details"
})
public class IngredientVariant implements Serializable {

    private static final long serialVersionUID = 1271423195992860417L;
    @XmlElement(required = true)
    protected ProductData product;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected UnitOfMeasure uom;
    protected boolean captured;
	protected boolean customize;
	protected boolean critical;
    protected List<IngredientVariantDetail> details;

    /**
     * Gets the value of the product property.
     * 
     * @return
     *     possible object is
     *     {@link ProductData }
     *     
     */
    public ProductData getProduct() {
        return product;
    }

    /**
     * Sets the value of the product property.
     * 
     * @param value
     *     allowed object is
     *     {@link ProductData }
     *     
     */
    public void setProduct(ProductData value) {
        this.product = value;
    }

    /**
     * Gets the value of the uom property.
     * 
     * @return
     *     possible object is
     *     {@link UnitOfMeasure }
     *     
     */
    public UnitOfMeasure getUom() {
        return uom;
    }

    /**
     * Sets the value of the uom property.
     * 
     * @param value
     *     allowed object is
     *     {@link UnitOfMeasure }
     *     
     */
    public void setUom(UnitOfMeasure value) {
        this.uom = value;
    }

    /**
     * Gets the value of the captured property.
     * 
     */
    public boolean isCaptured() {
        return captured;
    }

    /**
     * Sets the value of the captured property.
     * 
     */
    public void setCaptured(boolean value) {
        this.captured = value;
    }

    /**
     * Gets the value of the details property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the details property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDetails().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IngredientVariantDetail }
     * 
     * 
     */
    public List<IngredientVariantDetail> getDetails() {
        if (details == null) {
            details = new ArrayList<IngredientVariantDetail>();
        }
        return this.details;
    }

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

	public boolean isCritical() {
		return critical;
	}

	public void setCritical(boolean critical) {
		this.critical = critical;
	}

}
