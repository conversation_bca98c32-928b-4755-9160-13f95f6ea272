package com.stpl.tech.redis.domain.model;

public class UnitDataVO {

	private Unit unit;
	private boolean isOperational;
	private String message;
	private String openingTime;
	private String closingTime;
	private int error;
	private int type;
	private boolean skipDeliveryCharge;
	private boolean skipPackagingCharge;

	public UnitDataVO() {
	}

	public Unit getUnit() {
		return unit;
	}

	public void setUnit(Unit unit) {
		this.unit = unit;
	}

	public boolean isOperational() {
		return isOperational;
	}

	public void setOperational(boolean isOperational) {
		this.isOperational = isOperational;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getOpeningTime() {
		return openingTime;
	}

	public void setOpeningTime(String openingTime) {
		this.openingTime = openingTime;
	}

	public String getClosingTime() {
		return closingTime;
	}

	public void setClosingTime(String closingTime) {
		this.closingTime = closingTime;
	}

	public int getError() {
		return error;
	}

	public void setError(int error) {
		this.error = error;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public boolean isSkipDeliveryCharge() {
		return skipDeliveryCharge;
	}

	public void setSkipDeliveryCharge(boolean skipDeliveryCharge) {
		this.skipDeliveryCharge = skipDeliveryCharge;
	}

	public boolean isSkipPackagingCharge() {
		return skipPackagingCharge;
	}

	public void setSkipPackagingCharge(boolean skipPackagingCharge) {
		this.skipPackagingCharge = skipPackagingCharge;
	}

}
