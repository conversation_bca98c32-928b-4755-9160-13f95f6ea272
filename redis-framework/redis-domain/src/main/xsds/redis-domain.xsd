<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="Unit">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" nillable="false" />
				<xs:element name="products" type="Product" minOccurs="0"
					maxOccurs="unbounded" />
				<xs:element name="taxProfiles" type="TaxProfile"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="operationalHours" type="UnitHours"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="UnitBasicDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="category" type="xs:string" />
			<xs:element name="subCategory" type="xs:string" />
			<xs:element name="region" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="DeliveryUnit">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" nillable="false" />
				<xs:element name="taxProfiles" type="TaxProfile"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="operationalHours" type="UnitHours"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProductUnit">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" nillable="false" />
				<xs:element name="products" type="Product" minOccurs="0"
					maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="Product">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" nillable="false" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="xs:int" />
			<xs:element name="subType" type="xs:int" />
			<xs:element name="attribute" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" />
			<xs:element name="addonProfile" type="xs:string" />
			<xs:element name="supportsVariantLevelOrdering" type="xs:boolean" />
			<xs:element name="prices" type="ProductPrice" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxProfile">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="profileId" type="xs:int" />
			<xs:element name="percentage" type="xs:decimal" />
			<xs:element name="type" type="TaxType" />
			<xs:element name="name" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProductPrice">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="dimension" type="xs:string" nillable="false" />
			<xs:element name="price" type="xs:decimal" />
			<xs:element name="recipe" type="RecipeDetail" />
			<xs:element name="currentStock" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RecipeDetail">
		<xs:sequence>
			<xs:element name="recipeId" type="xs:string" />
			<xs:element name="ingredient" type="IngredientDetail" />
			<xs:element name="addons" type="IngredientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngredientDetail">
		<xs:sequence>
			<xs:element name="compositeProduct" type="CompositeProductData" />
			<xs:element name="products" type="IngredientProduct"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="variants" type="IngredientVariant"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="components" type="IngredientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngredientProductDetail">
		<xs:sequence>
			<xs:element name="product" type="ProductData" />
			<xs:element name="dimension" type="BasicInfo" />
			<xs:element name="uom" type="UnitOfMeasure" />
			<xs:element name="quantity" type="xs:decimal" />
			<xs:element name="defaultSetting" type="xs:boolean"
				default="false" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CompositeProductData">
		<xs:sequence>
			<xs:element name="maxQuantity" type="xs:int" />
			<xs:element name="details" type="CompositeIngredientData"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngredientProduct">
		<xs:sequence>
			<xs:element name="category" type="BasicInfo" />
			<xs:element name="details" type="IngredientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngredientVariant">
		<xs:sequence>
			<xs:element name="product" type="ProductData" />
			<xs:element name="uom" type="UnitOfMeasure" />
			<xs:element name="captured" type="xs:boolean" />
			<xs:element name="details" type="IngredientVariantDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProductData">
		<xs:sequence>
			<xs:element name="productId" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" />
			<xs:element name="type" type="xs:int" />
			<xs:element name="subType" type="xs:int" />
			<xs:element name="variantLevelOrdering" type="xs:boolean" />
			<xs:element name="classification" type="ProductClassification" />
			<xs:element name="isInventoryTracked" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BasicInfo">
		<xs:sequence>
			<xs:element name="infoId" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" />
			<xs:element name="type" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CompositeIngredientData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="menuProducts" type="IngredientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngredientVariantDetail">
		<xs:sequence>
			<xs:element name="productId" type="xs:int" />
			<xs:element name="alias" type="xs:string" />
			<xs:element name="uom" type="UnitOfMeasure" />
			<xs:element name="quantity" type="xs:decimal" />
			<xs:element name="captured" type="xs:boolean" default="true" />
			<xs:element name="defaultSetting" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitHours">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="dayOfTheWeekNumber" type="xs:int" />
			<xs:element name="dayOfTheWeek" type="xs:string" />
			<xs:element name="isOperational" type="xs:boolean" />
			<xs:element name="hasTakeAway" type="xs:boolean" />
			<xs:element name="deliveryOpeningTime" type="xs:time" />
			<xs:element name="deliveryClosingTime" type="xs:time" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IdName">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CategoryData">
		<xs:sequence>
			<xs:element name="webCategories" type="IdName"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:simpleType name="TaxType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NET_PRICE_VAT" />
			<xs:enumeration value="MRP_VAT" />
			<xs:enumeration value="SURCHARGE" />
			<xs:enumeration value="SERVICE_TAX" />
			<xs:enumeration value="SB_CESS" />
			<xs:enumeration value="KK_CESS" />
			<xs:enumeration value="GST" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitOfMeasure" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="KG" />
			<xs:enumeration value="GM" />
			<xs:enumeration value="ML" />
			<xs:enumeration value="L" />
			<xs:enumeration value="PC" />
			<xs:enumeration value="PACKET" />
			<xs:enumeration value="SACHET" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ProductClassification" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MENU" />
			<xs:enumeration value="PAID_ADDON" />
			<xs:enumeration value="FREE_ADDON" />
			<xs:enumeration value="VARIANT" />
			<xs:enumeration value="PRODUCT_VARIANT" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
