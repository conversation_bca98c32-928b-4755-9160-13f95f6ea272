<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<bindings xmlns="http://java.sun.com/xml/ns/jaxb" version="2.0"
	xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<globalBindings underscoreBinding="asCharInWord">
		<javaType name="java.util.Date" xmlType="xs:date"
			parseMethod="com.stpl.tech.redis.domain.model.DateAdapter.parseDate"
			printMethod="com.stpl.tech.redis.domain.model.DateAdapter.printDate" />
		<javaType name="java.sql.Time" xmlType="xs:time"
				  parseMethod="com.stpl.tech.redis.domain.model.DateAdapter.parseTime"
				  printMethod="com.stpl.tech.redis.domain.model.DateAdapter.printTime" />
		<javaType name="java.math.BigDecimal" xmlType="xs:decimal"
			parseMethod="com.stpl.tech.redis.domain.model.BigDecimalAdapter.parseBigDecimal"
			printMethod="com.stpl.tech.redis.domain.model.BigDecimalAdapter.printBigDecimal" />
	</globalBindings>
</bindings>