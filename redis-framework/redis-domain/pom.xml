<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.stpl.tech.redis</groupId>
		<artifactId>redis-framework</artifactId>
		<version>6.2.41</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>redis-domain</artifactId>
	<packaging>jar</packaging>

	<name>redis-domain</name>
	<url>http://maven.apache.org</url>
	<properties>
    	<spring-boot.repackage.skip>true</spring-boot.repackage.skip>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.stpl.tech.util</groupId>
			<artifactId>utility-service</artifactId>
			<version>6.2.41</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.springframework.data</groupId>
		    <artifactId>spring-data-redis</artifactId>
		</dependency>
	</dependencies>

</project>
