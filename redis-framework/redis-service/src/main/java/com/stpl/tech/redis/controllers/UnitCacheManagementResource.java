package com.stpl.tech.redis.controllers;

import com.stpl.tech.redis.core.service.RedisCacheService;
import com.stpl.tech.redis.domain.model.IndexUrl;
import com.stpl.tech.redis.domain.model.SkuCodeProductImageMappingDetail;
import com.stpl.tech.redis.domain.model.Unit;
import com.stpl.tech.redis.domain.model.UnitDataVO;
import com.stpl.tech.redis.domain.model.UnitSubscriptionProductsImageMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import static com.stpl.tech.redis.core.RedisServiceConstants.API_VERSION;
import static com.stpl.tech.redis.core.RedisServiceConstants.SEPARATOR;
import static com.stpl.tech.redis.core.RedisServiceConstants.UNIT_CACHE_ROOT_CONTEXT;

/**
 * Created by Chaayos on 03-10-2016.
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ UNIT_CACHE_ROOT_CONTEXT, method = RequestMethod.GET,  produces = MediaType.APPLICATION_JSON_VALUE) // 'v1/uc'
public class UnitCacheManagementResource {

	private static final Logger LOG = LoggerFactory.getLogger(UnitCacheManagementResource.class);

	@Autowired
	private RedisCacheService redisCacheService;

	@RequestMapping(value = "reload-unit-cache")
	public boolean loadUnitCache() throws IOException, URISyntaxException {
		LOG.info("Request to get all units for redis cache");
		try {
			redisCacheService.removeAllUnits();
			redisCacheService.loadUnitCache();
			return true;
		} catch (Exception e) {
			LOG.error("Error reloading unit cache. ", e.getCause());
			return false;
		}

	}

	@Deprecated
	@RequestMapping(value = "gu")
	public Unit getUnit(@RequestParam int unitId) throws IOException, URISyntaxException {
		LOG.info("Request to get unit from redis cache");
		return redisCacheService.getUnit(unitId);
	}

	@RequestMapping(value = "u")
	public UnitDataVO getUnitData(@RequestParam int unitId) throws IOException, URISyntaxException {
		LOG.info("Request to get unit from redis cache");
		return redisCacheService.getUnitData(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-sku-code-product-images", produces = MediaType.APPLICATION_JSON_VALUE)
	public SkuCodeProductImageMappingDetail getProductImagesForSkuCodeByUnitId(@RequestParam int unitId, @RequestParam String skuCode) throws IOException, URISyntaxException {
		try {
			LOG.info("Getting product images for skucode:{} by unitId:{}", skuCode, unitId);
			return redisCacheService.getProductImagesForSkuCodeByUnitId(unitId, skuCode);
		} catch (Exception e) {
			LOG.error("Unable to get image urls for skucode:{} by unitId:{}", skuCode, unitId,e);
		}
		return null;
	}

	@RequestMapping(value = "refresh-unit-cache")
	public boolean refreshCacheForUnit(@RequestParam int unitId) throws IOException, URISyntaxException {
		LOG.info("Request to get all units for redis cache");
		try {
			redisCacheService.clearCacheForUnit(unitId);
			redisCacheService.getUnit(unitId);
			return true;
		} catch (Exception e) {
			LOG.error("Error reloading cache for unit :{}",unitId, e.getCause());
			return false;
		}
	}

	@RequestMapping(method=RequestMethod.GET,value = "get-single-product-images")
	public IndexUrl getSingleProductImage (@RequestParam int unitId, @RequestParam int productId) throws IOException, URISyntaxException {
		try {
			LOG.info("Getting product images for productId:{} by unitId:{}", productId, unitId);
			return redisCacheService.getSingleProductImage(unitId, productId);
		} catch (Exception e) {
			LOG.error("Unable to get image urls for productId:{} by unitId:{}", productId, unitId,e);
		}
		return null;
	}
	@RequestMapping(method=RequestMethod.GET,value="get-subscription-products-for-unit")
	public List<Integer> getSubscriptionProductsForUnit(@RequestParam int unitId){
		try{
			LOG.info("Getting subscription products for unitId:{}",unitId);
			return redisCacheService.getSubscriptionProducts(unitId);
		}catch(Exception e ){
			LOG.error("Unable to get subscription products for unitId :::::{}", unitId,e);
		}
		return null;
	}

	@RequestMapping(method=RequestMethod.GET,value="get-subscription-products-images-for-unit",produces = MediaType.APPLICATION_JSON_VALUE)
	public UnitSubscriptionProductsImageMapping getSubscriptionProductImagesForUnit(@RequestParam int unitId){
		try{
			LOG.info("Getting subscription products images for unitId:{}",unitId);
			return redisCacheService.getSubscriptionProductImagesForUnit(unitId);
		}catch(Exception e ){
			LOG.error("Unable to get subscription products for unitId :::::{}", unitId,e);
		}
		return null;
	}
}


