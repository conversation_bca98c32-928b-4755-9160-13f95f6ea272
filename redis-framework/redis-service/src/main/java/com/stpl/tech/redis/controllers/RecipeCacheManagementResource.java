package com.stpl.tech.redis.controllers;

import com.stpl.tech.redis.core.service.RedisCacheService;
import com.stpl.tech.redis.domain.model.ProductRecipes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import static com.stpl.tech.redis.core.RedisServiceConstants.*;

/**
 * Created by Chaayos on 03-10-2016.
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + RECIPE_CACHE_ROOT_CONTEXT) // 'v1/recipe-cache'
public class RecipeCacheManagementResource {

	private static final Logger LOG = LoggerFactory.getLogger(RecipeCacheManagementResource.class);

	@Autowired
	private RedisCacheService redisCacheService;

	@RequestMapping(method = RequestMethod.GET, value = "product-recipes", produces = MediaType.APPLICATION_JSON_VALUE)
	public List<ProductRecipes> getRecipe(@RequestParam List<Integer> productIds)
			throws IOException, URISyntaxException {
		LOG.info("Request to get product recipes from redis cache");
		return redisCacheService.getProductRecipes(productIds);
	}

}
