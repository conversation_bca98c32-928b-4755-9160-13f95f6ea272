package com.stpl.tech.redis.config;

import com.stpl.tech.redis.core.config.RedisConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.TimeZone;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Configuration
@Import(value = {RedisConfig.class})
public class RedisServiceConfig {

    static {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
        System.out.println("reading redis service config");
    }

}
