<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <parent>
		<groupId>com.stpl.tech.redis</groupId>
		<artifactId>redis-framework</artifactId>
		<version>6.2.41</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
    <artifactId>redis-core</artifactId>
    <name>redis-core</name>
    <url>http://maven.apache.org</url>
	<properties>
    	<spring-boot.repackage.skip>true</spring-boot.repackage.skip>
	</properties>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.redis</groupId>
            <artifactId>redis-domain</artifactId>
            <version>6.2.41</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!-- <dependency>
		  <groupId>javax.annotation</groupId>
		  <artifactId>javax.annotation-api</artifactId>
		  <version>1.3.2</version>
		</dependency> -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
		    <groupId>org.springframework</groupId>
		    <artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
		    <groupId>jakarta.persistence</groupId>
		    <artifactId>jakarta.persistence-api</artifactId>
		</dependency>
		<dependency>
		    <groupId>jakarta.annotation</groupId>
		    <artifactId>jakarta.annotation-api</artifactId>
		</dependency>
    </dependencies>
</project>
