package com.stpl.tech.redis.core.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import com.stpl.tech.redis.core.service.RedisProperties;
import com.stpl.tech.redis.core.util.CustomRedisSerializer;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Configuration
@EnableRedisRepositories(basePackages = {"com.stpl.tech.redis.core.dao"})
@ComponentScan(basePackages = {"com.stpl.tech.redis.core.service", "com.stpl.tech.redis.core.cache"})
public class RedisConfig {

    @Autowired
    private RedisProperties env;

    public RedisConfig() {
        super();
    }

	/*
	 * @Bean JedisConnectionFactory jedisConnectionFactory() {
	 * JedisConnectionFactory jedisConFactory = new JedisConnectionFactory();
	 * jedisConFactory.setHostName(env.getRedisHost());
	 * jedisConFactory.setPort(env.getRedisPort());
	 * jedisConFactory.setUsePool(true); jedisConFactory.setTimeout(100000);
	 * jedisConFactory.setDatabase(env.getRedisDBIndex());
	 * jedisConFactory.setPassword(env.getRedisPassword());
	 * //System.out.println("initializing redis connection factory redis"); return
	 * jedisConFactory; }
	 * 
	 * @Bean public RedisTemplate<String, Object> redisTemplate() {
	 * RedisTemplate<String, Object> template = new RedisTemplate<>();
	 * template.setConnectionFactory(jedisConnectionFactory());
	 * template.setKeySerializer(new StringRedisSerializer());
	 * template.setHashKeySerializer(new StringRedisSerializer());
	 * template.setValueSerializer(stringRedisSerializer());
	 * template.setHashValueSerializer(stringRedisSerializer());
	 * template.setStringSerializer(stringRedisSerializer()); return template; }
	 */
    @Bean
    public CustomRedisSerializer stringRedisSerializer() {
        return new CustomRedisSerializer();
    }
}
