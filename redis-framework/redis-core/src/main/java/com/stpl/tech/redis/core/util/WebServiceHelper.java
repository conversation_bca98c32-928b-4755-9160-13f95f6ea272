package com.stpl.tech.redis.core.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.util.JSONSerializer;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

public class WebServiceHelper {

	private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);
	public static final String AUTH_INTERNAL = "auth-internal";
	public static final String CONTENT_TYPE = org.apache.http.HttpHeaders.CONTENT_TYPE;
	private static ObjectMapper JSON_CONVERTER = new ObjectMapper();

	public static <T> T postWithAuth(String endPoint, String token, Object body, Class<T> clazz) {
		try {
			RestTemplate restTemplate = new RestTemplate();
			HttpHeaders requestHeaders = new HttpHeaders();
			requestHeaders.set("auth-internal", token);
			requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
			HttpEntity<?> requestEntity;
			if (body != null) {
				requestEntity = new HttpEntity(body, requestHeaders);
			} else {
				requestEntity = new HttpEntity(requestHeaders);
			}
			MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
			jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
			restTemplate.getMessageConverters().add(jackson);
			return restTemplate.postForObject(endPoint, requestEntity, clazz);
		} catch (Exception e) {
			LOG.error("ERROR While Request {}", endPoint, e);
			throw e;
		}
	}

	public static <T> T getRequestWithAuthAndParamAndHeader(String endpoint, String authorizationHeader,Map<String, String> parameters, Class <T> responseClazz)
			throws ClientProtocolException, IOException, URISyntaxException {
		URIBuilder uriBuilder = new URIBuilder(endpoint);
		if (parameters != null && !parameters.keySet().isEmpty()) {
			for (String p : parameters.keySet()) {
				uriBuilder.setParameter(p, parameters.get(p));
			}
		}
		HttpClient client = HttpClientBuilder.create().build();
		HttpGet request = new HttpGet(uriBuilder.build());
		request.setHeader(AUTH_INTERNAL, authorizationHeader);
		request.setHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
		HttpResponse response = client.execute(request);
		LOG.info("url before sending request : {}  " ,request);
		return WebServiceHelper.convertResponse(response, responseClazz, true);
	}

	public static <T> T convertResponse(HttpResponse response, Class<T> responseClazz, boolean usejsonSerializer)
			throws IllegalStateException, IOException {
		if (response != null) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
			StringBuffer result = new StringBuffer();
			String line = "";
			while ((line = reader.readLine()) != null) {
				result.append(line);
			}
			//LOG.info("recorded response :::: {}", result.toString());
			EntityUtils.consume(response.getEntity());
			if (usejsonSerializer) {
				return convertUsingSerializer(result.toString(), responseClazz);

			} else {
				return convert(result.toString(), responseClazz);

			}
		}
		return null;

	}

	public static <T> T convert(String input, Class<T> output) throws IOException {
		return JSON_CONVERTER.readValue(input, output);
	}

	public static <T> T convertUsingSerializer(String input, Class<T> output) throws IOException {
		return JSONSerializer.toJSON(input, output);
	}

	public static <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, String body,
			Map<String, ?> uriVariables) throws URISyntaxException {
		return exchangeWithAuth(endpoint, token, method, clazz, body, uriVariables, MediaType.APPLICATION_JSON_UTF8);
	}

	public static <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, String body,
			Map<String, ?> uriVariables, MediaType type) throws URISyntaxException {
		try {
			RestTemplate restTemplate = new RestTemplate();
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Arrays.asList(type));
			// TODO Check this if something went wrong
			headers.setContentType(type);
			if (token != null) {
				headers.set("auth-internal", token);
			}
			HttpEntity<String> entity = new HttpEntity<String>(body, headers);
			if (uriVariables != null) {
				endpoint += "?";
				for (String key : uriVariables.keySet()) {
					endpoint = endpoint + key + "=" + uriVariables.get(key).toString() + "&";
				}
				endpoint = endpoint.substring(0, endpoint.length() - 1);
			}
			URI uri = new URI(endpoint);
			return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
		} catch (Exception e) {
			LOG.error("ERROR", e);
			throw e;
		}
	}

	public static <E> Collection<E> makeCollection(Iterable<E> iterable) {
		Collection<E> list = new ArrayList<E>();
		for (E item : iterable) {
			list.add(item);
		}
		return list;
	}

	public static <E> List<E> toList(Iterable<E> iterable) {
		List<E> list = new ArrayList<E>();
		for (E item : iterable) {
			list.add(item);
		}
		return list;
	}
}
