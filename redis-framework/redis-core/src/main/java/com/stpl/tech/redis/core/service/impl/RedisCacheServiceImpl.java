package com.stpl.tech.redis.core.service.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.redis.core.dao.CategoryDataDao;
import com.stpl.tech.redis.core.dao.DeliveryUnitDao;
import com.stpl.tech.redis.core.dao.ProductBasicDetailDao;
import com.stpl.tech.redis.core.dao.ProductUnitDao;
import com.stpl.tech.redis.core.dao.RecipeDao;
import com.stpl.tech.redis.core.dao.UnitBasicDetailDao;
import com.stpl.tech.redis.core.dao.UnitBusinessHoursDao;
import com.stpl.tech.redis.core.dao.UnitDao;
import com.stpl.tech.redis.core.service.RedisCacheService;
import com.stpl.tech.redis.core.service.RedisProperties;
import com.stpl.tech.redis.core.util.MasterServiceClientEndpoints;
import com.stpl.tech.redis.core.util.TimeDeserializer;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.CategoryData;
import com.stpl.tech.redis.domain.model.DeliveryUnit;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.redis.domain.model.IndexUrl;
import com.stpl.tech.redis.domain.model.Location;
import com.stpl.tech.redis.domain.model.Product;
import com.stpl.tech.redis.domain.model.ProductBasicDetail;
import com.stpl.tech.redis.domain.model.ProductImageMappingDetailList;
import com.stpl.tech.redis.domain.model.ProductPrice;
import com.stpl.tech.redis.domain.model.ProductRecipeKey;
import com.stpl.tech.redis.domain.model.ProductRecipes;
import com.stpl.tech.redis.domain.model.ProductUnit;
import com.stpl.tech.redis.domain.model.RecipeDetail;
import com.stpl.tech.redis.domain.model.SkuCodeProductImageMappingDetail;
import com.stpl.tech.redis.domain.model.SubscriptionProductImageMapping;
import com.stpl.tech.redis.domain.model.Unit;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.redis.domain.model.UnitBusinessHours;
import com.stpl.tech.redis.domain.model.UnitDataVO;
import com.stpl.tech.redis.domain.model.UnitHours;
import com.stpl.tech.redis.domain.model.UnitProductData;
import com.stpl.tech.redis.domain.model.UnitSubscriptionProductsImageMapping;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URISyntaxException;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class RedisCacheServiceImpl implements RedisCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(RedisCacheServiceImpl.class);

    @Autowired
    private UnitDao unitDao;
    @Autowired
    private UnitBusinessHoursDao unitBusinessHoursDao;
    @Autowired
    private UnitBasicDetailDao unitBasicDetailDao;

    @Autowired
    private DeliveryUnitDao deliveryUnitDao;

    @Autowired
    private ProductUnitDao productUnitDao;

    @Autowired
    private RecipeDao recipeDao;

    @Autowired
    private ProductBasicDetailDao productBasicDetailDao;

    @Autowired
    private RedisProperties environmentProperties;

    @Autowired
    private CategoryDataDao categoryDao;

    private static String ImageType ="GRID_MENU_HIGH";

    @Override
    @PostConstruct
    public void loadUnitCache() throws URISyntaxException {
        if (!AppUtils.isDev(environmentProperties.getEnvType())) {
            LOG.info("Loading Unit Cache");
            // loadAll();
        }
    }

    @Override
    public List<Location> loadLocationCache() throws URISyntaxException {
        return loadAllLocations();
    }

    @Override
    public void loadAll() throws URISyntaxException {
        cleanUp();
        loadAllUnits();
        loadAllLocations();
        loadAllUnitBasicDetail(true);
        loadAllProductRecipes();
        loadWebCategoriesToCache();
        loadAllProductBasicDetail();
    }

    private void cleanUp() {
        unitDao.deleteAll();
        unitBusinessHoursDao.deleteAll();
        unitBasicDetailDao.deleteAll();
        deliveryUnitDao.deleteAll();
        productUnitDao.deleteAll();
        recipeDao.deleteAll();
        categoryDao.deleteAll();
    }

    private void loadAllProductRecipes() throws URISyntaxException {
        Set<String> regions = new HashSet<>();
        for (UnitBasicDetail ubd : unitBasicDetailDao.findAll()) {
            regions.add(ubd.getRegion());
            addDeliveryAndProductUnit(ubd);
        }
        ProductUnit u = null;
        for (String region : regions) {
            LOG.info("finding pricing unit for region: {}", region);
            u = getProductUnit(region);
            for (Product p : u.getProducts()) {
                // get recipe is called to skip calling to kettle
                getRecipe(p.getId());
            }
        }
    }

    @Override
    public void clearUnitCache() throws IOException, URISyntaxException {
        removeAllUnits();
        removeAllUnitBasicDetails();
        recipeDao.deleteAll();
        deliveryUnitDao.deleteAll();
        productUnitDao.deleteAll();
        categoryDao.deleteAll();
    }

    @Override
    public void removeAllUnits() {
        unitDao.deleteAll();
    }

    @Override
    public void removeAllUnitBasicDetails() {
        unitBasicDetailDao.deleteAll();
    }

    @Override
    public Unit getUnit(int id) throws IOException, URISyntaxException {
    	Optional<Unit> unitData = unitDao.findById(id);
        Unit unit = null;
		if (unitData.isEmpty()) {
			unit = loadUnitToCache(id);
		} else {
			unit = unitData.get();
		}
        return unit;
    }

    @Override
    public UnitDataVO getUnitData(int id) throws IOException, URISyntaxException {
        UnitDataVO vo = new UnitDataVO();
        Optional<Unit> unitData = unitDao.findById(id);
        Unit unit = null;
		if (unitData.isEmpty()) {
			unit = loadUnitToCache(id);
		} else {
			unit = unitData.get();
		}
        vo.setUnit(unit);
        UnitBusinessHours ubh = getBusinessHours(id);
        String msg = null;
        int dayOfWeek = AppUtils.getDayOfWeek(AppUtils.getCurrentTimestamp());
        for (UnitHours uhrs : ubh.getOperationalHours()) {
            if (uhrs.getDayOfTheWeek() != null && uhrs.getDayOfTheWeek().trim() != ""
                && uhrs.getDayOfTheWeekNumber() == dayOfWeek) {
                vo.setOpeningTime(uhrs.getTakeAwayOpeningTime());
                vo.setClosingTime(uhrs.getTakeAwayClosingTime());
                try {
                    msg = "Order can placed between " + AppUtils.msgTimeFormat(vo.getOpeningTime()) + " and "
                        + AppUtils.msgTimeFormat(vo.getClosingTime()) + " only.";
                } catch (Exception e) {
                    LOG.error("Error While parsing date for error msg", e);
                    msg = null;
                }
                vo.setMessage(msg);
                vo.setOperational(uhrs.isIsOperational());
            }
        }
        vo.setSkipDeliveryCharge(true);
        return vo;
    }

    @Override
    public List<ProductRecipes> getProductRecipes(List<Integer> productIds) throws URISyntaxException {
        List<ProductRecipes> recipes = new ArrayList<>();
        productIds.forEach(pid -> recipes.add(getRecipe(pid)));
        return recipes;
    }

    @Override
    public ProductRecipes getProductRecipe(int pId) throws URISyntaxException {
        return getRecipe(pId);
    }

    @Override
    public List<ProductRecipes> getAllProductRecipes() {
        Collection<ProductRecipes> list = WebServiceHelper.makeCollection(recipeDao.findAll());
        LOG.info("Loading Product Recipies total: {}", list.size());
        if (!list.isEmpty()) {
            return (List<ProductRecipes>) list;
        }
        return new ArrayList<>();
    }

    private ProductRecipes getRecipe(int productId) {
        Optional<ProductRecipes> recipeDetailsData = recipeDao.findById(productId);
        ProductRecipes recipeDetails = null;
        if (recipeDetailsData.isEmpty()) {
            try {
            	recipeDetails = loadRecipeToCache(productId);
            } catch (URISyntaxException e) {
                LOG.error("Error while getting recipe from Redis Cache", e);
            }
        }else {
        	recipeDetails = recipeDetailsData.get();
        }
        return recipeDetails;
    }

    @Override
    public UnitBasicDetail getUnitbasicDetails(int unitId) {
    	Optional<UnitBasicDetail> unitData = unitBasicDetailDao.findById(unitId);
    	UnitBasicDetail unit = null;
		if (unitData.isEmpty()) {
			unit = loadUnitbasicDetailToCache(unitId);
		} else {
			unit = unitData.get();
		}
        return unit;
    }

    @Override
    public ProductUnit getProductUnit(String region) throws URISyntaxException {
        Optional<ProductUnit> unitData = productUnitDao.findById(region);
        ProductUnit unit = null;
        if (unitData.isEmpty()) {
            // this can be be further cleaned up
            int unitId = getChaiOnDemandUnit(region);
            if (unitId == 0) {
                loadAllUnitBasicDetail(false);
                unitId = getChaiOnDemandUnit(region);
            }
            unit = addToProductUnit(unitId, region);
        }else {
        	unit = unitData.get();
        }
        return unit;
    }

    private int getChaiOnDemandUnit(String region) {
        // we have to iterate as we could not find a clean solution
        for (UnitBasicDetail ubd : unitBasicDetailDao.findAll()) {
            if (ubd.getCategory().equals(AppConstants.COD) && ubd.getRegion().equals(region)) {
                return ubd.getId();
            }
        }
        return 0;
    }

	@Override
	public DeliveryUnit getDeliveryUnit(int id) {
		Optional<DeliveryUnit> unitData = deliveryUnitDao.findById(id);
		DeliveryUnit unit = null;
		if (unitData.isEmpty()) {
			unit = addToDeliveryUnit(id);
		} else {
			unit = unitData.get();
		}
		return unit;
	}

    private List<UnitBasicDetail> loadAllUnitBasicDetail(boolean extendedLoading) throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endPoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.GET_ALL_UNIT_BASIC_DETAILS;
        String token = environmentProperties.getRedisClientToken();
        List<?> list = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET, List.class, null, null);
        List<UnitBasicDetail> ubdList = new ArrayList<>();
        list.forEach(obj -> {
            GsonBuilder gSonBuilder = new GsonBuilder();
            gSonBuilder.registerTypeAdapter(Time.class, new TimeDeserializer());
            Gson gson = gSonBuilder.create();
            String str = gson.toJson(obj, LinkedHashMap.class);
            UnitBasicDetail unit = gson.fromJson(str, UnitBasicDetail.class);
            if(unit.isLive() && AppConstants.ACTIVE.equals(unit.getStatus())) {
                ubdList.add(unit);
            }
        });
        unitBasicDetailDao.saveAll(ubdList);
        LOG.info("Uploaded Unit Basic Details to Redis Cache completed in {} miliseconds",
            System.currentTimeMillis() - startTime);
        if (extendedLoading) {
            ubdList.forEach(this::addDeliveryAndProductUnit);
        }
        return ubdList;
    }

    private List<ProductBasicDetail> loadAllProductBasicDetail() throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endPoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.GET_ALL_PRODUCT_BASIC_DETAILS;
        String token = environmentProperties.getRedisClientToken();
        List<?> products = null;
        try {
            products = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET, List.class, null, null);
            List<ProductBasicDetail> productBasicDetailList = new ArrayList<>();
            products.forEach(obj -> {
                GsonBuilder gSonBuilder = new GsonBuilder();
                Gson gson = gSonBuilder.create();
                String str = gson.toJson(obj, LinkedHashMap.class);
                ProductBasicDetail product = gson.fromJson(str, ProductBasicDetail.class);
                productBasicDetailList.add(product);
            });
            productBasicDetailDao.saveAll(productBasicDetailList);
            LOG.info("Uploaded Product Basic Details to Redis Cache completed in {} miliseconds",
                    System.currentTimeMillis() - startTime);
            return productBasicDetailList;
        }catch (Exception e){
            LOG.error("Exception Product Basic Detail");
        }
        return null;
    }

    @Override
    public Map<Integer, String> getAllProductImages() throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endPoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.GET_ALL_PRODUCT_IMAGES;
        String token = environmentProperties.getRedisClientToken();
        ProductImageMappingDetailList productsImageListData = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET,
            ProductImageMappingDetailList.class, null, null);
        productsImageListData = new Gson().fromJson(new Gson().toJson(productsImageListData), ProductImageMappingDetailList.class);
        Map<Integer, String> productImages = new HashMap<>();
        productsImageListData.getList().forEach(obj -> {
            GsonBuilder gSonBuilder = new GsonBuilder();
            Gson gson = gSonBuilder.create();
            //String str = gson.toJson(obj, LinkedHashMap.class);
            //ProductImageMappingDetail product = gson.fromJson(str, ProductImageMappingDetail.class);
            if(obj.getGridLow() != null) {
                productImages.put(obj.getProductId(), obj.getGridLow().getUrl());
//                productImages.put(obj.getProductId(), obj.getGridHigh()/*.getUrl(*/));
            }
        });
        LOG.info("Uploaded Product Images completed in {} miliseconds",
            System.currentTimeMillis() - startTime);
        return productImages;
    }

    private void addDeliveryAndProductUnit(UnitBasicDetail ubd) {
        if (AppConstants.COD.equals(ubd.getCategory())) {
            addToProductUnit(ubd.getId(), ubd.getRegion());
        } else {
            addToDeliveryUnit(ubd.getId());
        }
    }

    private DeliveryUnit addToDeliveryUnit(int id) {
        long startTime = System.currentTimeMillis();
        Unit unit = null;
        try {
            unit = getUnit(id);
        } catch (Exception e) {
            LOG.error("error getting unit data!");
            return null;
        }
        DeliveryUnit du = new DeliveryUnit();
        du.setId(unit.getId());
        du.setName(unit.getName());
        du.getTaxProfiles().addAll(unit.getTaxProfiles());
        du.getTaxes().addAll(unit.getTaxes());
        du.setLocation(unit.getLocation());
        du.setPackagingType(unit.getPackagingType());
        du.setPackagingValue(unit.getPackagingValue());
        du = deliveryUnitDao.save(du);
        LOG.info("Uploaded Delivery Unit {} to Redis Cache completed in {} miliseconds", du.getName(),
            System.currentTimeMillis() - startTime);
        return du;
    }

    private ProductUnit addToProductUnit(int id, String region) {
        long startTime = System.currentTimeMillis();
        Unit unit = null;
        try {
            unit = getUnit(id);
        } catch (Exception e) {
            LOG.error("error getting unit data!", e);
            return null;
        }
        ProductUnit pu = new ProductUnit();
        pu.setId(unit.getId());
        pu.setName(unit.getName());
        pu.setRegion(region);
        pu.getProducts().addAll(unit.getProducts());
        pu = productUnitDao.save(pu);
        LOG.info("Uploaded Product Unit {} to Redis Cache completed in {} miliseconds", pu.getName(),
            System.currentTimeMillis() - startTime);
        return pu;
    }

    private void loadAllUnits() throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        LOG.info("Loading All Units to Redis Cache");
        String endPoint = environmentProperties.getMasterServiceBasePath() + MasterServiceClientEndpoints.GET_ALL_UNITS;
        String token = environmentProperties.getRedisClientToken();
        List<?> list = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET, List.class, null, null);
        List<Unit> unitList = new ArrayList<>();
        list.forEach(obj -> {
            GsonBuilder gSonBuilder = new GsonBuilder();
            gSonBuilder.registerTypeAdapter(Time.class, new TimeDeserializer());
            Gson gson = gSonBuilder.create();
            String str = gson.toJson(obj, LinkedHashMap.class);
            Unit unit = gson.fromJson(str, Unit.class);
            unitList.add(unit);
        });
        removeProducts(unitList);
        createBusinessHoursData(unitList);
        updateRecipeDetailFromProducts(unitList);
        unitDao.saveAll(unitList);
        LOG.info("Uploaded All Units to Redis Cache completed in {} miliseconds",
            System.currentTimeMillis() - startTime);
    }

    private List<Location> loadAllLocations() throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        LOG.info("Loading All Locations to Redis Cache");
        String endPoint = environmentProperties.getMasterServiceBasePath() + MasterServiceClientEndpoints.GET_ALL_LOCATIONS;
        String token = environmentProperties.getRedisClientToken();
        List<Location> list = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET, List.class, null, null);
        List<Location> locations = new ArrayList<>();
        for (Object obj : list) {
            GsonBuilder gSonBuilder = new GsonBuilder();
            gSonBuilder.registerTypeAdapter(Time.class, new TimeDeserializer());
            Gson gson = gSonBuilder.create();
            String str = gson.toJson(obj, LinkedHashMap.class);
            Location location = gson.fromJson(str, Location.class);
            locations.add(location);
        }
        LOG.info("Uploaded All Locations to Redis Cache completed in {} miliseconds",
            System.currentTimeMillis() - startTime);
        return locations;
    }

    private void removeProducts(List<Unit> unitList) {
        if (unitList != null && !unitList.isEmpty()) {
//            unitList.forEach(u -> addProducts(u));
            for(Unit unit : unitList){
                addProducts(unit);
                getSubscriptionProductImagesBySkuCode(unit);
            }
        }
    }

    private void createBusinessHoursData(List<Unit> unitList) {
        if (unitList != null && !unitList.isEmpty()) {
            unitList.forEach(u -> createBusinessHours(u));
        }
    }

    private void updateRecipeDetailFromProducts(List<Unit> unitList) {
        if (unitList != null && !unitList.isEmpty()) {
            unitList.forEach(u -> updateRecipeDetail(u));
        }
    }

    private void updateRecipeDetail(Unit u) {
        if (u != null) {
            for (Product p : u.getProducts()) {
                p.setStrategy(getStartegy(p).getId());
                for (ProductPrice price : p.getPrices()) {
                    if (price.getRecipe() != null) {
                        price.setRecipeId(price.getRecipe().getRecipeId());
                    }
                    price.setRecipe(null);
                }
            }
        }
    }

    public static enum ProductCustomizationStrategy {
        NO_STRATEGY(0), OPEN_CUSTOMIZATION_MODAL(1), SHOW_CUSTOMIZATION_BELOW_PRODUCT(
            2), OPEN_CUSTOMIZATION_MODAL_WITH_VARIANT(3), SHOW_DIMENSION_BELOW_PRODUCT(4), ADD_DIRECTLY_TO_CART(5);

        private final int id;

        private ProductCustomizationStrategy(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

    }

    private ProductCustomizationStrategy getStartegy(Product p) {
        if (p.getPrices().size() <= 0) {
            return ProductCustomizationStrategy.NO_STRATEGY;
        }
        if (p.getPrices().size() == 1) {
            if (p.isCustomize()) {
                RecipeDetail recipe = p.getPrices().get(0).getRecipe();
                return recipe.getCustomizationCount() == 1
                    ? ProductCustomizationStrategy.SHOW_CUSTOMIZATION_BELOW_PRODUCT
                    : ProductCustomizationStrategy.OPEN_CUSTOMIZATION_MODAL_WITH_VARIANT;
            } else {
                return ProductCustomizationStrategy.ADD_DIRECTLY_TO_CART;
            }
        }

        if (p.getPrices().size() > 1) {
            if (p.isCustomize()) {
                return ProductCustomizationStrategy.OPEN_CUSTOMIZATION_MODAL;
            } else {
                return ProductCustomizationStrategy.SHOW_DIMENSION_BELOW_PRODUCT;
            }
        }
        return ProductCustomizationStrategy.NO_STRATEGY;
    }

    private void addProducts(Unit u) {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.GET_UNIT_PRODUCTS;
        String token = environmentProperties.getRedisClientToken();
        UnitProductData data = WebServiceHelper.postWithAuth(endpoint, token, u.getId(), UnitProductData.class);
        LOG.info("Uploaded Products Of Unit {} to Redis Cache in {} miliseconds", u.getName(),
            System.currentTimeMillis() - startTime);
        List<Product> products = new ArrayList<>();
        List<Product> subscriptionProducts = new ArrayList<>();
        data.getProducts().forEach(product -> {
            if (product.getWebType() != null) {
                if(!validateIsDeliveryOnlyProduct(product)){
                    products.add(product);
                }
            }
            if(product.getSubType()==environmentProperties.getSubscriptionProductSubType()){
                if(!validateIsDeliveryOnlyProduct(product)){
                    subscriptionProducts.add(product);
                }
            }
        });
        u.getProducts().clear();
        u.getProducts().addAll(products);
        u.getTaxes().clear();
        u.getTaxes().addAll(data.getTaxes());
        u.getSubscriptionProducts().clear();
        u.getSubscriptionProducts().addAll(subscriptionProducts);
        LOG.info("After setting products and subscription products:{}",new Gson().toJson(u));
    }

    private boolean validateIsDeliveryOnlyProduct(Product product){
        List<ProductPrice> productPrices = new ArrayList<>();
        if(Objects.nonNull(product.getPrices()) && !product.getPrices().isEmpty()){
             productPrices.addAll(product.getPrices().stream().filter(productPrice -> Objects.nonNull(productPrice.getIsDeliveryOnlyProduct()) && productPrice.getIsDeliveryOnlyProduct()).collect(Collectors.toList()));
             if(!productPrices.isEmpty() && productPrices.size() == product.getPrices().size()){
                 return true;
             }
            // Remove the particular productPrice for which delivery only product is marked true
            if(!productPrices.isEmpty()){
                product.getPrices().stream().forEach(productPrice ->{
                    if(Objects.nonNull(productPrice.getIsDeliveryOnlyProduct()) || !productPrice.getIsDeliveryOnlyProduct()){
                        product.getPrices().add(productPrice);
                    }
                });
            }
        }
        return false;
    }

    private void createBusinessHours(Unit u) {
        UnitBusinessHours businessHours = new UnitBusinessHours(u.getId(), u.getOperationalHours());
        unitBusinessHoursDao.save(businessHours);
        u.getOperationalHours().clear();
    }

    @Override
    public UnitBusinessHours getBusinessHours(int unitId) {
        Optional<UnitBusinessHours> ubsData = unitBusinessHoursDao.findById(unitId);
        UnitBusinessHours ubs = null;
        if (ubsData.isEmpty()) {
            String endpoint = environmentProperties.getMasterServiceBasePath() + MasterServiceClientEndpoints.GET_UNIT;
            String token = environmentProperties.getRedisClientToken();
            Unit unit = WebServiceHelper.postWithAuth(endpoint, token, unitId, Unit.class);
            ubs = new UnitBusinessHours(unit.getId(), unit.getOperationalHours());
            unitBusinessHoursDao.save(ubs);
        }else {
        	ubs = ubsData.get();
        }
        return ubs;
    }

    private UnitBasicDetail loadUnitbasicDetailToCache(int id) {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.GET_UNIT_BASIC_DETAIL;
        String token = environmentProperties.getRedisClientToken();
        UnitBasicDetail ubd = WebServiceHelper.postWithAuth(endpoint, token, id, UnitBasicDetail.class);
        unitBasicDetailDao.save(ubd);
        LOG.info("Uploaded Unit Basic Detail for {} to Redis Cache in {} miliseconds", ubd.getName(),
            System.currentTimeMillis() - startTime);
        return ubd;
    }

    private Unit loadUnitToCache(int id) throws IOException, URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath() + MasterServiceClientEndpoints.GET_UNIT;
        String token = environmentProperties.getRedisClientToken();
        Unit unit = WebServiceHelper.postWithAuth(endpoint, token, id, Unit.class);
        addProducts(unit);
/*        This method gets the product images for the subscription products (i.e set in unit)*/
        getSubscriptionProductImagesBySkuCode(unit);
        // createBusinessHours(unit);
        updateRecipeDetail(unit);
        unitDao.save(unit);
        LOG.info("Uploaded Unit {} to Redis Cache in {} miliseconds", unit.getName(),
            System.currentTimeMillis() - startTime);
        return unit;
    }

    private void getSubscriptionProductImagesBySkuCode(Unit unit) {
//        Getting list of subscription product ids
        LOG.info("Getting list of subscription product ids");
        List<Integer> subscriptionProductIds = new ArrayList<>();
        unit.getSubscriptionProducts().stream().forEach(product -> {
            subscriptionProductIds.add(product.getId());
        });
        LOG.info("Subscription id list size:{} and list is :{}",subscriptionProductIds.size(), new Gson().toJson(subscriptionProductIds));
        try {
            LOG.info("Getting images for subscription Products");
            getImagesForProductIds(subscriptionProductIds,unit);
        } catch (Exception e) {
            LOG.error("Error getting product images :{}",e);
        }
    }

    private ProductRecipes loadRecipeToCache(int productId) throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.PRODUCT_RECIPES;
        String token = environmentProperties.getRedisClientToken();
        Map<String, Object> reqObj = new HashMap<>();
        reqObj.put("productId", productId);
        List data = WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.GET, List.class, null,
            reqObj);
        List<RecipeDetail> recipeDetails = new ArrayList<>();
        data.forEach(recipeObj -> {
            Gson gson = new Gson();
            String str = gson.toJson(recipeObj);
            RecipeDetail recipeDetail = gson.fromJson(str, RecipeDetail.class);
            recipeDetails.add(recipeDetail);
        });
        ProductRecipes productRecipes = new ProductRecipes(productId, recipeDetails);
        recipeDao.save(productRecipes);
        LOG.info("Uploaded Recipes for product {} to Redis Cache in {} miliseconds", productId,
            System.currentTimeMillis() - startTime);
        return productRecipes;
    }

    @Override
    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> loadUnitProductPriceProfile() throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.GET_ALL_UNIT_PRODUCT_PROFILE_MAPPINGS;
        String token = environmentProperties.getRedisClientToken();
        Map<String, Object> reqObj = new HashMap<>();
        Map<?, ?> map = WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.GET, Map.class, null,
            reqObj);
        Map<Integer, Map<Integer, List<ProductRecipeKey>>> productPriceProfileMap = new HashMap<>();
        map.forEach((k, o) -> {
            int unitId = Integer.parseInt(k.toString());
            Gson gson = new Gson();
            String str = gson.toJson(o);
            Map<?, ?> dataMap = gson.fromJson(str, Map.class);
            Map<Integer, List<ProductRecipeKey>> productRecipeKeyMap = new HashMap<>();
            dataMap.forEach((key, val) -> {
                int productId = Integer.parseInt(key.toString());
                String s = gson.toJson(val);
                List<Object> x = gson.fromJson(s, List.class);
                productRecipeKeyMap.put(productId, new ArrayList<>());
                x.forEach(o1 ->
                    productRecipeKeyMap.get(productId).add(gson.fromJson(gson.toJson(o1), ProductRecipeKey.class))
                );
            });
            productPriceProfileMap.put(unitId, productRecipeKeyMap);
        });
        LOG.info("Uploaded product price profiles map in {} miliseconds", System.currentTimeMillis() - startTime);
        return productPriceProfileMap;
    }

    @Override
    public Map<Integer, List<ProductRecipeKey>> loadUnitProductPriceProfileForUnit(int unitId) throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath()
                + MasterServiceClientEndpoints.GET_PRODUCT_PROFILE_MAPPINGS_FOR_UNIT;
        String token = environmentProperties.getRedisClientToken();
        Map<String, Object> reqObj = new HashMap<>();
        reqObj.put("unitId",unitId);
        Map<?, ?> map = WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.GET, Map.class, null,
                reqObj);
        Gson gson = new Gson();
        String str = gson.toJson(map);
        Map<?, ?> dataMap = gson.fromJson(str, Map.class);
        Map<Integer, List<ProductRecipeKey>> productRecipeKeyMap = new HashMap<>();
        dataMap.forEach((key, val) -> {
            int productId = Integer.parseInt(key.toString());
            String s = gson.toJson(val);
            List<Object> x = gson.fromJson(s, List.class);
            productRecipeKeyMap.put(productId, new ArrayList<>());
            x.forEach(o1 ->
                    productRecipeKeyMap.get(productId).add(gson.fromJson(gson.toJson(o1), ProductRecipeKey.class))
            );
        });
        LOG.info("Uploaded product price profiles map for unit in {} miliseconds", System.currentTimeMillis() - startTime);
        return productRecipeKeyMap;
    }

    @Override
    public List<IdName> getWebCategories() {
        Iterable<CategoryData> iterable = categoryDao.findAll();
        List<IdName> webCats = new ArrayList<>();
        Collection<CategoryData> list = WebServiceHelper.makeCollection(iterable);
        if (list.isEmpty()) {
            try {
                webCats = loadWebCategoriesToCache();
            } catch (URISyntaxException e) {
                LOG.error("Error while loading web categories", e);
            }
        } else {
            List<CategoryData> dataList = (List<CategoryData>) list;
            if (dataList != null && !dataList.isEmpty()) {
                webCats = dataList.get(0).getWebCategories();
            }
        }
        return webCats;
    }

    private List<IdName> loadWebCategoriesToCache() throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endpoint = environmentProperties.getMasterServiceBasePath()
            + MasterServiceClientEndpoints.WEB_CATEGORIES;
        String token = environmentProperties.getRedisClientToken();
        List<?> list = WebServiceHelper.exchangeWithAuth(endpoint, token, HttpMethod.GET, List.class, null, null);
        CategoryData data = new CategoryData();
        list.forEach(p -> {
            Gson gson = new Gson();
            String str = gson.toJson(p);
            IdName cat = gson.fromJson(str, IdName.class);
            data.getWebCategories().add(cat);
        });
        categoryDao.save(data);
        LOG.info("Uploaded Web Categories to Redis Cache in {} miliseconds", System.currentTimeMillis() - startTime);
        return data.getWebCategories();
    }

    @Override
    public List<UnitBasicDetail> getAllUnitBasicDetails() throws URISyntaxException {
        return loadAllUnitBasicDetail(false);
    }

    @Override
    public List<ProductBasicDetail> getAllProductBasicDetails() throws URISyntaxException {
        return loadAllProductBasicDetail();
    }

    @Override
    public SkuCodeProductImageMappingDetail getProductImagesForSkuCodeByUnitId(int unitId, String skuCode) {
        Unit unit = null;
        try {
            unit = getUnit(unitId);
        } catch (IOException | URISyntaxException e) {
            LOG.error("Unable to get unit data :{}", e.getMessage());
        }
        Map<String, List<IndexUrl>> marketingImageMap = new HashMap<>();
        SkuCodeProductImageMappingDetail skuCodeProductImageMappingDetail= new SkuCodeProductImageMappingDetail();
        if(Objects.nonNull(unit.getSubscriptionProducts()) && !unit.getSubscriptionProducts().isEmpty()){
            unit.getSubscriptionProducts().forEach(subscriptionProduct -> {
                if (subscriptionProduct.getSkuCode().equalsIgnoreCase(skuCode)) {
                    LOG.info("Setting marketingImage map ");
                    if (Objects.nonNull(subscriptionProduct.getMarketingImage())) {
                        marketingImageMap.put(AppConstants.marketingImage, subscriptionProduct.getMarketingImage());
                    }
                    if (Objects.nonNull(subscriptionProduct.getMarketingImageWebView())) {
                        marketingImageMap.put(AppConstants.marketingImageWebView, subscriptionProduct.getMarketingImageWebView());
                    }
                    skuCodeProductImageMappingDetail.setSkuCodeProduct(subscriptionProduct);
                    skuCodeProductImageMappingDetail.setSkuCodeMarketingImageMap(marketingImageMap);
                }
            });
        }
        LOG.info("SkuProduct Image Map:{}",new Gson().toJson(skuCodeProductImageMappingDetail));
        return skuCodeProductImageMappingDetail;
    }

    @Override
    public void clearCacheForUnit(int unitId) {
        Optional<Unit> unit = unitDao.findById(unitId);
        if(unit.isPresent()){
            removeUnit(unit.get());
            removeUnitBasicDetails(unit.get());
        }
    }

    private void removeUnitBasicDetails(Unit unit) {
        unitBasicDetailDao.deleteById(unit.getId());
    }

    private void removeUnit(Unit unit) {
        unitDao.deleteById(unit.getId());
    }

    private void getImagesForProductIds(List<Integer> list,Unit unit) throws URISyntaxException {
        long startTime = System.currentTimeMillis();
        String endPoint = environmentProperties.getMasterServiceBasePath()
                + MasterServiceClientEndpoints.GET_PRODUCT_IMAGES_FOR_PRODUCTIDS;
        String token = environmentProperties.getRedisClientToken();
        LOG.info("Before calling product images api from master");
        ProductImageMappingDetailList input = new ProductImageMappingDetailList();
        input.setPids(list);
        ProductImageMappingDetailList productsImageListData= null;
        try{
             productsImageListData = WebServiceHelper.postWithAuth(endPoint,token,input,ProductImageMappingDetailList.class);
        }catch(Exception e){
            LOG.error("Exception while getting images for productIds :{} from master:{}",new Gson().toJson(list),e);
        }
        productsImageListData = new Gson().fromJson(new Gson().toJson(productsImageListData), ProductImageMappingDetailList.class);
        Map<Integer, String> productImages = new HashMap<>();
        if(Objects.nonNull(productsImageListData.getList()) && !productsImageListData.getList().isEmpty()){
            LOG.info("Product Images List Data:{}",productsImageListData.getList().size());
            productsImageListData.getList().forEach(obj -> {
                GsonBuilder gSonBuilder = new GsonBuilder();
                Gson gson = gSonBuilder.create();
                Map<String, List<IndexUrl>> marketingImageMap = new HashMap<>();
                unit.getSubscriptionProducts().forEach(subscriptionProduct->{
                    if(subscriptionProduct.getId()==obj.getProductId()){
                        if(Objects.nonNull(obj.getMarketingImages()) && !obj.getMarketingImages().isEmpty()){
                            subscriptionProduct.getMarketingImage().clear();
                            subscriptionProduct.getMarketingImage().addAll(obj.getMarketingImages());
                        }
                        if(Objects.nonNull(obj.getMarketingImageWebViews()) && !obj.getMarketingImageWebViews().isEmpty()){
                            subscriptionProduct.getMarketingImageWebView().clear();
                            subscriptionProduct.getMarketingImageWebView().addAll(obj.getMarketingImageWebViews());
                        }
                    }
                });
            });
        }
        LOG.info("Uploaded Product Images completed in {} miliseconds",
                System.currentTimeMillis() - startTime);
    }

    @Override
    public IndexUrl getSingleProductImage(int unitId, int productId) throws IOException, URISyntaxException {
        Optional<Unit> unitOptional = unitDao.findById(unitId);
        Unit unit = null;
        if (unitOptional.isEmpty()) {
            unit = loadUnitToCache(unitId);
        } else {
            unit =unitOptional.get();
        }
        if (Objects.nonNull(unit.getSubscriptionProducts()) && !unit.getSubscriptionProducts().isEmpty()) {
            for (Product subscriptionProduct : unit.getSubscriptionProducts()) {
                if (subscriptionProduct.getId() == productId) {
                    if (Objects.nonNull(subscriptionProduct.getMarketingImage()) && !subscriptionProduct.getMarketingImage().isEmpty()) {
                        return subscriptionProduct.getMarketingImage().get(0);
                    }
                    if (Objects.nonNull(subscriptionProduct.getMarketingImageWebView()) && !subscriptionProduct.getMarketingImageWebView().isEmpty()) {
                        return subscriptionProduct.getMarketingImageWebView().get(0);
                    }
                }
            }
        }
        LOG.info("Success while getting image for productId:{}",productId);
        return null;
    }

    @Override
    public List<Integer> getSubscriptionProducts(int unitId) throws IOException, URISyntaxException {
        List<Integer> subscriptionProductIds = new ArrayList<>();
        Unit unit = getUnit(unitId);
        if(Objects.nonNull(unit)){
            if(Objects.nonNull(unit.getSubscriptionProducts()) && !unit.getSubscriptionProducts().isEmpty()){
                for(Product subscriptionProduct: unit.getSubscriptionProducts()){
                    subscriptionProductIds.add(subscriptionProduct.getId());
                }
            }
        }
        LOG.info("Active Subscription Product Ids:{}",new Gson().toJson(subscriptionProductIds));
        return subscriptionProductIds ;
    }

    @Override
    public UnitSubscriptionProductsImageMapping getSubscriptionProductImagesForUnit(int unitId) throws IOException, URISyntaxException {
        UnitSubscriptionProductsImageMapping unitSubscriptionProductsImageMapping = new UnitSubscriptionProductsImageMapping();
        unitSubscriptionProductsImageMapping.setSubscriptionProductImageMappingList(new ArrayList<>());
        List<SubscriptionProductImageMapping> subscriptionProductImageMappingList = new ArrayList<>();
        Unit unit = getUnit(unitId);
        if(Objects.nonNull(unit)){
            if(Objects.nonNull(unit.getSubscriptionProducts()) && !unit.getSubscriptionProducts().isEmpty()){
                for(Product subscriptionProduct: unit.getSubscriptionProducts()){
                    SubscriptionProductImageMapping subscriptionProductImageMapping = new SubscriptionProductImageMapping();
                    if(Objects.nonNull(subscriptionProduct.getMarketingImageWebView()) && !subscriptionProduct.getMarketingImageWebView().isEmpty()){
                        subscriptionProductImageMapping.getMarketingImages().getMarketingImageWebView().clear();
                        subscriptionProductImageMapping.getMarketingImages().getMarketingImageWebView().addAll(subscriptionProduct.getMarketingImageWebView());
                    }
                    if(Objects.nonNull(subscriptionProduct.getMarketingImage())&& !subscriptionProduct.getMarketingImage().isEmpty()){
                        subscriptionProductImageMapping.getMarketingImages().getMarketingImageMobView().clear();
                        subscriptionProductImageMapping.getMarketingImages().getMarketingImageMobView().addAll(subscriptionProduct.getMarketingImage());
                    }
                    subscriptionProductImageMapping.setSubscriptionProduct(subscriptionProduct);
                    subscriptionProductImageMappingList.add(subscriptionProductImageMapping);
                }
            }
        }
        unitSubscriptionProductsImageMapping.setSubscriptionProducts(new ArrayList<>());
        unitSubscriptionProductsImageMapping.setSubscriptionProducts(unit.getSubscriptionProducts());
        if(Objects.nonNull(subscriptionProductImageMappingList) && !subscriptionProductImageMappingList.isEmpty()){
            unitSubscriptionProductsImageMapping.getSubscriptionProductImageMappingList().addAll(subscriptionProductImageMappingList);
        }
        LOG.info("Map of unit subscription products and their images", new Gson().toJson(unitSubscriptionProductsImageMapping));
        return unitSubscriptionProductsImageMapping;
    }
}


