package com.stpl.tech.redis.core.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.nio.charset.Charset;
import java.sql.Time;

/**
 * Created by Chaayos on 10-10-2016.
 */
public class CustomRedisSerializer implements RedisSerializer<String> {
    /*@Override
    public byte[] serialize(Object t) throws SerializationException {
        GsonBuilder gSonBuilder = new GsonBuilder();
        gSonBuilder.registerTypeAdapter(Time.class, new TimeDeserializer());
        Gson gson = gSonBuilder.create();
        String str = gson.toJson(t);
        return str.getBytes();
    }*/

    @Override
    public byte[] serialize(String s) throws SerializationException {
        return s == null?null:s.getBytes(Charset.forName("UTF8"));
    }

    @Override
    public String deserialize(byte[] bytes) throws SerializationException {
        String obj = new String(bytes, Charset.forName("UTF8"));
        GsonBuilder gSonBuilder = new GsonBuilder();
        gSonBuilder.registerTypeAdapter(Time.class, new TimeDeserializer());
        Gson gson = gSonBuilder.create();
        return gson.fromJson(obj, String.class);
    }
}
