package com.stpl.tech.redis.core.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.stpl.tech.util.EnvType;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Service
public class RedisProperties {

    @Autowired
    Environment environment;

    public String getRedisHost(){
        return environment.getProperty("redis.host","localhost");
    }

    public int getRedisPort(){
        return Integer.valueOf(environment.getProperty("redis.port","6379")).intValue();
    }

    public EnvType getEnvType(){
        return EnvType.valueOf(environment.getProperty("environment.type","DEV"));
    }

    public String getRedisClientToken(){
        return environment.getProperty("redis.client.token");
    }
    
	public String getMasterServiceBasePath(){
        return environment.getProperty("base.path.master.service");
    }
    public int getSubscriptionProductSubType() {
        return Integer.valueOf(environment.getProperty("subscription.product.type", "3810"));
    }

	public int getRedisDBIndex() {
		return Integer.valueOf(environment.getProperty("redis.db.index","0")).intValue();
	}

	public String getRedisPassword() {
		return environment.getProperty("redis.pass", "R3d15D3V");
	}
	
}
