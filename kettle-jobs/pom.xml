<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.stpl.tech.jobs</groupId>
	<artifactId>kettle-jobs</artifactId>
	<name>kettle-jobs</name>
	<version>4.1.0-SNAPSHOT</version>
	<url>http://maven.apache.org</url>
	<packaging>war</packaging>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>17</java.version>
		<log4j2.version>2.15.0</log4j2.version>
	</properties>
	<build>
		<plugins>
		<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.7.1</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<configuration>
					<checkModificationExcludes>
						<checkModificationExclude>**/pom.xml</checkModificationExclude>
						<checkModificationExclude>release.properties</checkModificationExclude>
					</checkModificationExcludes>
				</configuration>
			</plugin>
		<plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
                <execution>
                    <phase>compile</phase>
                    <goals>
                        <goal>copy-dependencies</goal>
                    </goals>
					<configuration>
                        <outputDirectory>${project.build.directory}/WEB-INF/lib</outputDirectory>
                    </configuration>
                </execution>
            </executions>
        </plugin>
        <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.6</version>
        <executions>
			<execution>
				<id>copy-resources</id>
				<phase>install</phase>
				<goals>
					<goal>copy-resources</goal>
				</goals>
				<configuration>
					<outputDirectory>/data/app/kettle/${env.type}/jobs</outputDirectory>
					<resources>
						<resource>
							<directory>${project.basedir}/src/main/jobs</directory>
							<filtering>true</filtering>
						</resource>
					</resources>
				</configuration>
			</execution>
			<execution>
				<id>copy-resources-1</id>
				<phase>install</phase>
				<goals>
					<goal>copy-resources</goal>
				</goals>
				<configuration>
					<outputDirectory>/data/app/kettle/${env.type}/python_reports/swiggy_report</outputDirectory>
					<resources>
						<resource>
							<directory>${project.basedir}/src/main/reports/python_reports/swiggy_report</directory>
							<filtering>true</filtering>
						</resource>
					</resources>
				</configuration>
			</execution>
      </executions>
      </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
<!--                        <includeEmptyDirs>true</includeEmptyDirs>-->
                        <webResources>
                            <!-- <resource>
                                <directory>src/main/webapp</directory>
                                <targetPath></targetPath>
                                <includes>
                                    <include>**/*</include>
                                </includes>
                            </resource>
//edited below -->
                            <resource>
                               <directory>${project.build.directory}/WEB-INF/lib/</directory>
                               <targetPath>WEB-INF/lib</targetPath>
                               <includes>
                                    <include>**/*</include>
                               </includes>
                            </resource>
                        </webResources>
                        <webXml>WebContent/WEB-INF/web.xml</webXml>
                    </configuration>
				<!-- <configuration>
					<webXml></webXml>
				</configuration> -->
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.apache.maven.plugins
										</groupId>
										<artifactId>
											maven-dependency-plugin
										</artifactId>
										<versionRange>
											[2.8,)
										</versionRange>
										<goals>
											<goal>
												copy-dependencies
											</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<finalName>kettle-jobs</finalName>
	</build>
	<dependencies>
		<!-- https://mvnrepository.com/artifact/com.vladsch.flexmark/flexmark -->
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark</artifactId>
			<version>0.19.6</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.samskivert/jmustache -->
		<dependency>
			<groupId>com.samskivert</groupId>
			<artifactId>jmustache</artifactId>
			<version>1.8</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/log4j/log4j -->
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>
		<dependency>
			<groupId>com.carfey</groupId>
			<artifactId>obsidian</artifactId>
			<version>4.4.1</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/obsidian-4.4.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.stpl.tech.kettle.transaction</groupId>
			<artifactId>kettle-reports</artifactId>
			<version>4.1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.stpl.tech.kettle.etl</groupId>
			<artifactId>kettle-etl</artifactId>
			<version>4.1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-c3p0</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-ehcache</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>
						hibernate-commons-annotations
					</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.security</groupId>
					<artifactId>spring-security-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-beans</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.validation</groupId>
					<artifactId>validation-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.stpl.tech.kettle.log</groupId>
			<artifactId>logging-core</artifactId>
			<version>4.1.0-SNAPSHOT</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.stpl.tech</groupId>-->
<!--			<artifactId>knock-domain</artifactId>-->
<!--			<version>1.0.0-SNAPSHOT</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.stpl.tech.forms</groupId>
			<artifactId>forms-core</artifactId>
			<version>4.1.0-SNAPSHOT</version>
		</dependency>
		 <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>4.3.10.Final</version>
        </dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>4.2.5.RELEASE</version>
		</dependency>
		 <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.8.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.5.4</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.9</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongodb-driver-sync</artifactId>
			<version>4.4.0</version>
		</dependency>
    </dependencies>

</project>
