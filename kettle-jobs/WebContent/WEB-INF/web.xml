<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	id="WebApp_ID" version="3.0">
	<!-- <servlet> <servlet-name>kettle-service</servlet-name> <servlet-class>org.glassfish.jersey.servlet.ServletContainer</servlet-class> 
		Register resources and providers under com.vogella.jersey.first package. 
		<init-param> <param-name>jersey.config.server.provider.packages</param-name> 
		<param-value>com.stpl.tech.kettle.service</param-value> </init-param> <load-on-startup>1</load-on-startup> 
		</servlet> -->
	<!-- Spring child -->

	<!-- Spring root -->
	<context-param>
		<param-name>contextClass</param-name>
		<param-value>
         org.springframework.web.context.support.AnnotationConfigWebApplicationContext
      </param-value>
	</context-param>
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>com.stpl.tech.kettle.jobs.config</param-value>
	</context-param>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<listener>
    	<listener-class>com.carfey.ops.servlet.StartupShutdownListener</listener-class>
  	</listener>
	 <servlet>
        <servlet-name>Rest</servlet-name>
        <servlet-class>com.carfey.ops.servlet.RestServlet</servlet-class>
    </servlet>
	<servlet-mapping>
		<servlet-name>Rest</servlet-name>
		<url-pattern>/rest/*</url-pattern>
	</servlet-mapping>

	<servlet>
        <servlet-name>Resource</servlet-name>
        <servlet-class>com.carfey.suite.servlet.JsonTableDataToResourceServlet</servlet-class>
    </servlet>
	<servlet-mapping>
		<servlet-name>Resource</servlet-name>
		<url-pattern>/ui-rest/resource/*</url-pattern>
	</servlet-mapping>
	
	<servlet>
        <servlet-name>Error</servlet-name>
        <servlet-class>com.carfey.ops.servlet.ErrorServlet</servlet-class>
    </servlet>

	<servlet-mapping>
		<servlet-name>Error</servlet-name>
		<url-pattern>/ui-rest/error/*</url-pattern>
	</servlet-mapping>

    <servlet>
        <servlet-name>DispatcherServlet</servlet-name>
        <servlet-class>com.carfey.suite.servlet.DispatcherServlet</servlet-class>
     </servlet>

	<servlet-mapping>
		<servlet-name>DispatcherServlet</servlet-name>
		<url-pattern>/ui-rest/*</url-pattern>
	</servlet-mapping>

	<servlet>
		<servlet-name>kettle-jobs</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextClass</param-name>
			<param-value>
				org.springframework.web.context.support.AnnotationConfigWebApplicationContext
			</param-value>
		</init-param>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>com.stpl.tech.kettle.jobs.config.WebMVCConfig</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
		<async-supported>true</async-supported>
	</servlet>
	<servlet-mapping>
		<servlet-name>kettle-jobs</servlet-name>
		<url-pattern>/rest-jobs/*</url-pattern>
	</servlet-mapping>
	<filter>
		<filter-name>CorsFilter</filter-name>
		<filter-class>org.apache.catalina.filters.CorsFilter</filter-class>
		<async-supported>true</async-supported>
		<init-param>
			<param-name>cors.allowed.headers</param-name>
			<param-value>Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers,Authorization,auth,Access-Control-Allow-Origin</param-value>
		</init-param>
		<init-param>
	        <param-name>cors.allowed.methods</param-name>
	        <param-value>GET, POST, PUT</param-value>
    	</init-param>
        <init-param>
            <param-name>cors.exposed.headers</param-name>
            <param-value>Access-Control-Allow-Origin,Access-Control-Allow-Credentials</param-value>
        </init-param>
        <init-param>
            <param-name>cors.support.credentials</param-name>
            <param-value>false</param-value>
        </init-param>
        <init-param>
            <param-name>cors.allowed.origins</param-name>
            <param-value>*</param-value>
        </init-param>
	</filter>
	<filter>
     <filter-name>rememberMe</filter-name>
     <filter-class>com.carfey.ops.servlet.RememberMeFilter</filter-class>
   </filter>

   <filter-mapping>
     <filter-name>rememberMe</filter-name>
     <url-pattern>/ui-rest/*</url-pattern>
   </filter-mapping>
	 <filter>
	  <filter-name>GZIPFilter</filter-name>
	  <filter-class>com.stpl.tech.master.core.service.filter.GZIPFilter</filter-class>
	</filter>
	<filter>
		<filter-name>javamelody</filter-name>
		<filter-class>net.bull.javamelody.MonitoringFilter</filter-class>
		<async-supported>true</async-supported>
		<init-param>
	        <param-name>authorized-users</param-name>
	        <param-value>admin:Chaayos123456</param-value>
	    </init-param>
	</filter>
	<filter-mapping>
		<filter-name>javamelody</filter-name>
		<url-pattern>/rest/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
		<dispatcher>ASYNC</dispatcher>
	</filter-mapping>
	<filter-mapping>
		<filter-name>javamelody</filter-name>
		<url-pattern>/monitoring</url-pattern>
	</filter-mapping>
	<listener>
		<listener-class>net.bull.javamelody.SessionListener</listener-class>
	</listener>
	<filter-mapping>
		<filter-name>CorsFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	 <filter-mapping>
	  <filter-name>GZIPFilter</filter-name>
	  <url-pattern>*.js</url-pattern>
	</filter-mapping>
	<filter-mapping>
	  <filter-name>GZIPFilter</filter-name>
	  <url-pattern>*.css</url-pattern>
	</filter-mapping>
	<filter-mapping>
	  <filter-name>GZIPFilter</filter-name>
	  <url-pattern>*.html</url-pattern>
	</filter-mapping>
	<filter-mapping>
	  <filter-name>GZIPFilter</filter-name>
	  <url-pattern>/</url-pattern>
	</filter-mapping>
	<filter-mapping>
	  <filter-name>GZIPFilter</filter-name>
	  <url-pattern>*.eot</url-pattern>
   </filter-mapping>
   <filter-mapping>
	  <filter-name>GZIPFilter</filter-name>
	  <url-pattern>*.ttf</url-pattern>
   </filter-mapping>
   <welcome-file-list>
        <welcome-file>index.html</welcome-file>
   </welcome-file-list>
   <error-page> 
   		<error-code>404</error-code> 
        <location>/error</location> 
	</error-page> 
    <error-page> 
        <exception-type>java.lang.Throwable</exception-type> 
        <location>/error</location> 
    </error-page>  
</web-app> 