CREATE TABLE KETTLE_JOBS_DEV.UNIT_PRODUCT_STOCK_DATA (
  KEY_ID INTEGER NOT NULL AUTO_INCREMENT,
  BUSINESS_DATE DATE,
  CALCULATION_DATE DATE ,
  CAFE_OPENING TIME ,
  CAFE_CLOSING TIME,
  UNIT_ID INTEGER,
  UNIT_NAME varchar(40),
  PRODUCT_ID INTEGER,
  PRODUCT_NAME varchar(255),
  DOWNTIME INTEGER,
  START_TIME TIME,
  CLOSE_TIME TIME,
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB AUTO_INCREMENT=4411 DEFAULT CHARSET=latin1;


CREATE TABLE KETTLE_JOBS_DEV.UNIT_PRODUCT_STOCK_EVENT_AGGREGATE (
  KEY_ID INTEGER NOT NULL AUTO_INCREMENT,
  BUSINESS_DATE DATE ,
  CALCULATION_DATE DATE ,
  CAFE_OPENING TIME ,
  CAFE_CLOSING TIME ,
  UNIT_ID INTEGER,
  PRODUCT_ID INTEGER,
  PRODUCT_NAME varchar(255),
  DOWNTIME INTEGER,
  OPERATION_TIME INTEGER,
  INSTANCE INTEGER,
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB AUTO_INCREMENT=4096 DEFAULT CHARSET=latin1;