#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#

environment.type=PROD

server.base.dir=/data/app/kettle/prod

# jdbc.X
#Transaction Data Source
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=***********************************************************************
jdbc.user=chaayosprodusr
jdbc.pass=ch44Y05Pr0dU53r

#Master Data Source
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.url=******************************************************************************
master.jdbc.user=chaayosprodusr
master.jdbc.pass=ch44Y05Pr0dU53r

#NPS Data Source
datalake.jdbc.url=*********************************************************************************


# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

#JOBS Data Source
jobs.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jobs.jdbc.url=****************************************************************************
jobs.jdbc.user=chaayosprodusr
jobs.jdbc.pass=ch44Y05Pr0dU53r
jobs.file.path=/home/<USER>/application/chaayos/kettle-framework/kettle-etl/main/resource/jobs/tally/

#UnitHealthCheckCacheDelayTime
run.auto.report=true
run.auto.intraday.report=true
run.auto.expense.report=true
run.validate.filter=true
run.aclInterceptor=false
run.intraday.external.reports=true

master.cache.host.details=prod.kettle.chaayos.com
master.cache.host.ports=5701,5702,5703,5704

log4j.rootLogger=INFO, FILE
log4j.appender.FILE=org.apache.log4j.RollingFileAppender
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d [%t] %-5p %c - %m%n
log4j.appender.FILE.File=${catalina.base}/logs/obsidian.log
log4j.appender.FILE.MaxFileSize=50MB
log4j.appender.FILE.MaxBackupIndex=10


com.carfey.obsidian.licence.key=
com.carfey.obsidian.licence.name=


com.carfey.obsidian.db.url=jdbc\:mysql\://kettle-prod.cwad1uvogqkg.eu-west-1.rds.amazonaws.com\:3306/KETTLE_JOBS
com.carfey.obsidian.db.userId=chaayosprodusr
com.carfey.obsidian.db.password=ch44Y05Pr0dU53r
com.carfey.obsidian.db.maxConnections=40
com.carfey.obsidian.db.connectionTimeout=2000
com.carfey.obsidian.db.tablePrefix=
com.carfey.obsidian.db.schema=


com.carfey.suite.security.Authenticator=com.carfey.suite.security.DBAuthenticator

# Email configuration for notifications, if desired
#for straight up open relay, just specify the host using
mail.smtp.host=smtp.gmail.com
mail.smtp.port=465 
#(standard ports are 25, 465 for SSL, 587 for TLS)

#for using TLS and SSL, provide these as necessary
mail.smtp.socketFactory.port=465
mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
mail.smtp.auth=true
mail.smtp.user=<EMAIL>
mail.smtp.password=application@sam

spring.data.mongodb.database=kettle_analytics
spring.data.mongodb.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000

spring.data.mongodb.database=inventory
spring.data.mongodb.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000

inventory.prediction.s3.access.key=********************
inventory.prediction.s3.secret.key=waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY
inventory.prediction.s3.path=inventory.prediction

base.path.kettle.master=http://internal.chaayos.com/master-service
kettle.jobs.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IktFVFRMRUpPQlMiLCJlbnZUeXBlIjoiU1BST0QiLCJwYXJ0bmVySWQiOjE4LCJwYXNzQ29kZSI6IllQTlpBU0Q0VEZFRzdDUiIsImlhdCI6MTY0MDA3NDE3M30.dHGj5k2FJz6ctXIqBEYp9B385iS6__Ec-Vm8Msa0dU0
