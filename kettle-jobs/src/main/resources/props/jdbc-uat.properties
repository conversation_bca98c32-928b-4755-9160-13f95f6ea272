#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#

environment.type=UAT

server.base.dir=/data/app/kettle/uat

# jdbc.X
#Transaction Data Source
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=*********************************************************
jdbc.user=root
jdbc.pass=321in#@!

#Master Data Source
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.url=****************************************************************
master.jdbc.user=root
master.jdbc.pass=321in#@!

#NPS Data Source
datalake.jdbc.url=*******************************************************************

#JOBS Data Source
jobs.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jobs.jdbc.url=**************************************************************
jobs.jdbc.user=root
jobs.jdbc.pass=321in#@!


# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

#JOBS Data Source
jobs.file.path=/home/<USER>/application/chaayos/kettle-framework/kettle-etl/main/resource/jobs/tally/

#UnitHealthCheckCacheDelayTime
run.auto.report=false
run.auto.intraday.report=false
run.auto.expense.report=false
run.validate.filter=false
run.aclInterceptor=false
run.intraday.external.reports=false

master.cache.host.details=localhost
master.cache.host.ports=5701,5702,5703,5704


log4j.rootLogger=INFO, FILE
log4j.appender.FILE=org.apache.log4j.RollingFileAppender
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d [%t] %-5p %c - %m%n
log4j.appender.FILE.File=${catalina.base}/logs/obsidian.log
log4j.appender.FILE.MaxFileSize=50MB
log4j.appender.FILE.MaxBackupIndex=10


com.carfey.obsidian.licence.key=
com.carfey.obsidian.licence.name=


com.carfey.obsidian.db.url=jdbc\:mysql\://localhost\:3306/KETTLE_JOBS_UAT
com.carfey.obsidian.db.userId=root
com.carfey.obsidian.db.password=321in\#@\!
com.carfey.obsidian.db.maxConnections=40
com.carfey.obsidian.db.connectionTimeout=2000
com.carfey.obsidian.db.tablePrefix=
com.carfey.obsidian.db.schema=


com.carfey.suite.security.Authenticator=com.carfey.suite.security.DBAuthenticator

# Email configuration for notifications, if desired
#for straight up open relay, just specify the host using
mail.smtp.host=smtp.gmail.com
mail.smtp.port=465 
#(standard ports are 25, 465 for SSL, 587 for TLS)

#for using TLS and SSL, provide these as necessary
mail.smtp.socketFactory.port=465
mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
mail.smtp.auth=true
mail.smtp.user=<EMAIL>
mail.smtp.password=&<2AEuNA

spring.data.mongodb.database=kettle_analytics
spring.data.mongodb.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000

spring.data.mongodb.database=inventory_dev
spring.data.mongodb.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000