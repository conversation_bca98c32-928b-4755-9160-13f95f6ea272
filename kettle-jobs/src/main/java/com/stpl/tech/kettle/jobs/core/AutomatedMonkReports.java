package com.stpl.tech.kettle.jobs.core;

import com.carfey.jdk.io.StreamDrainer;
import com.carfey.ops.job.Context;
import com.carfey.ops.job.param.Configuration;
import com.carfey.ops.job.param.Description;
import com.carfey.ops.job.param.Parameter;
import com.carfey.ops.job.param.Type;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.loggingservice.service.LoggingService;
import com.stpl.tech.master.core.apps.service.AppsManagementService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.monk.configuration.model.AppBuildData;
import com.stpl.tech.master.monk.configuration.model.AppsVersionMetadata;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-03-2018.
 */
public class AutomatedMonkReports {


    @Component
    @Description("Monk Daily Operations Report")
    public static class MonksDailyOpsReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private LoggingService loggingService;

        @Override
        public void execute(Context context) throws Exception {
            loggingService.sendMonkLogs(AppUtils.getPreviousDate(), AppUtils.getPreviousDate());
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }


    @Component
    @Description("Monk Daily Operations Report Cumulative")
    @Configuration(knownParameters = {
            @Parameter(name = "startDate", required = true, type = Type.STRING),
            @Parameter(name = "endDate", required = true, type = Type.STRING),
            @Parameter(name = "toMails", required = true, type = Type.STRING, allowMultiple = true)
    })
    public static class MonksCumulativeReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private LoggingService loggingService;

        @Override
        public void execute(Context context) throws Exception {
            Date startTime = AppUtils.parseDate(context.getConfig().getString("startDate"));
            Date endTime = AppUtils.parseDate(context.getConfig().getString("endDate"));
            List<String> toMails = context.getConfig().getStringList("toMails");
            loggingService.sendCumulativeMonkLogs(startTime, endTime, toMails);
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }




    @Component
    @Description("Monk Daily Operations Report Unit Wise")
    @Configuration(knownParameters = {
            @Parameter(name = "unitId", required = true, type = Type.INTEGER)
    })
    public static class MonksDailyOpsUnitReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private LoggingService loggingService;

        @Override
        public void execute(Context context) throws Exception {
            Integer unitId = context.getConfig().getInt("unitId");
            Date startTime = AppUtils.getPreviousDate();
            Date endTime = AppUtils.getPreviousDate();
            loggingService.sendMonkLogs(unitId, startTime, endTime);
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }


    @Component
    @Description("Cumulative Monk Wastage Reports")
    public static class ScheduledMonkWastageReport extends AutomatedReportsJobs.AutomatedGenericSqlReport{

        @Autowired
        protected EnvironmentProperties props;

        @Autowired
        protected MasterDataCache masterDataCache;

        @Autowired
        private UnitWiseMonkWastageReport wastageReport;

        @Override
        public void execute(Context context) throws Exception{
            List<UnitBasicDetail> units = masterDataCache.getUnits(UnitCategory.CAFE);
            for(UnitBasicDetail unit : units){
                if(unit.getStatus().equals(UnitStatus.ACTIVE) && unit.isLive()){
                    Map<String, List<Object>> params = new HashMap<>();
                    params.put("unitId", Collections.singletonList(unit.getId()));
                    params.put("startDate", Collections.singletonList(AppUtils.getCurrentBusinessDate()));
                    params.put("endDate", Collections.singletonList(AppUtils.getCurrentBusinessDate()));
                    addToRawValues(context.getConfig().getRawValues(), params);
                    wastageReport.execute(context);
                }
            }
        }

        private void addToRawValues(Map<String, List<Object>> rawValues, Map<String, List<Object>> params){
            for(String key : params.keySet()){
                if(!rawValues.containsKey(key)){
                    rawValues.put(key, params.get(key));
                }else{
                    rawValues.replace(key, params.get(key));
                }
            }
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }


    @Component
    @Description("Unit Wise Monk Wastage Report")
    @Configuration(knownParameters = {@Parameter(name = "fileName",
            defaultValue = "/reports/kettle/UnitWiseMonkWastage.xml" ,required = true)})
    public static class UnitWiseMonkWastageReport extends AutomatedReportsJobs.AutomatedGenericSqlReport {

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private MasterDataCache masterDataCache;

        @Override
        public void execute(Context context) throws Exception {
            Map<String,List<Object>> variables = context.getConfig().getRawValues();
            Integer unitId = Integer.parseInt(variables.get("unitId").get(0).toString());
            String fileName = context.getConfig().getString("fileName");
            Unit unit = masterDataCache.getUnit(unitId);
            List<String> toEmail = null;
            if(variables.containsKey("toEmail")){
                toEmail = variables.get("toEmail").stream().map(Object::toString).collect(Collectors.toList());
            }else{
                EmployeeBasicDetail dam = masterDataCache.getEmployeeBasicDetail(unit.getCafeManager().getId());
                toEmail = Arrays.asList(unit.getUnitEmail(), unit.getManagerEmail(), dam.getEmailId());
            }
            String subject = "Unit Wise Monk Wastage Report for ::" + unit.getName();
            executeReport(fileName, subject, toEmail, variables, false);
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    @Description("Chaayos Apps - Adhoc Build")
    @Configuration(knownParameters = {
            @Parameter(name = "env", required = true, type = Type.STRING),
            @Parameter(name = "major", required = true, type = Type.INTEGER),
            @Parameter(name = "minor", required = true, type = Type.INTEGER),
            @Parameter(name = "patch", required = true, type = Type.INTEGER),
            @Parameter(name = "apkRoot", required = true, type = Type.STRING,
                    defaultValue = "/home/<USER>/builds/android/KettleAndroid/APKS")
    })
    public static class AdHocAppBuildJob extends AbstractAutomatedJobReport {
        private static final com.carfey.jdk.log.Logger LOG = com.carfey.jdk.log.Logger.get();
        private Process mProcess;

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private AppsManagementService appService;

        @Autowired
        private FileArchiveService fileArchiveService;

        @Override
        public void execute(Context context) throws Exception {
            final String ROOT_APK_DIRECTORY = context.getConfig().getString("apkRoot");
            String env = context.getConfig().getString("env");
            Integer major = context.getConfig().getInt("major");
            Integer minor = context.getConfig().getInt("minor");
            Integer patch = context.getConfig().getInt("patch");

            AppsVersionMetadata latestVersion = appService.findLatestVersion();
            Integer version = AppConstants.APP_BUILD_SEED_VERSION;
            if(latestVersion!=null){
                version = latestVersion.getVersion() + 1;
            }

            //Prepare a command args array of strings
            String buildAndroidScript = getBuildCommandPath(new StringBuffer("")).append("buildAndroid.sh").toString();
            List<String> command = Arrays.asList("sh", buildAndroidScript,
                    env, major.toString(), minor.toString(), patch.toString(), version.toString());

            //upload the built APKs to the S3 archiving service if the build is successful
            if (executeBuildScript(command,context)){
                LOG.info("Inside upload the built APKs to the S3 archiving service");
                StringBuffer apkDir = new StringBuffer(ROOT_APK_DIRECTORY);
                if(env.equalsIgnoreCase(EnvType.PROD.name())){
                    apkDir//.append(File.separator).append(EnvType.PROD.name())
                            .append(File.separator).append("RELEASE");
                }else{
                    apkDir//.append(File.separator).append(EnvType.DEV.name())
                            .append(File.separator).append("DEBUG");
                }
                apkDir.append(File.separator).append("V").append(version);
                LOG.info("APK Directory :::::::::::::::::::::::::: " + apkDir.toString());
                List<File> apks = listApksForFolder(new File(apkDir.toString()));
                if(apks!=null && !apks.isEmpty()){
                    List<FileDetail> apksUploaded = new ArrayList<>();
                    //upload APKs iteratively from the root directory
                    for(File apk : apks){
                        String basedir = env + File.separator + "V" + version;
                        FileDetail fileUploaded = fileArchiveService.saveFileToS3("chaayos.apps", basedir, apk, true);
                        apksUploaded.add(fileUploaded);
                    }

                    if(!apksUploaded.isEmpty()){
                        //prepare a new version metadata for new apps uploaded and push to Mongo DB for version control
                        AppsVersionMetadata newVersion = new AppsVersionMetadata(major, minor, patch, version);
                        for(FileDetail fileDetail : apksUploaded){
                            IdCodeName uploadDetails = new IdCodeName(apksUploaded.indexOf(fileDetail), fileDetail.getBucket(), fileDetail.getKey());
                            newVersion.getApps().add(new AppBuildData(fileDetail.getName(), uploadDetails));
                        }
                        appService.uploadNewBuild(newVersion);
                    }
                }
                context.saveJobResult("Apps found", apks.size());
            }
        }


        public List<File> listApksForFolder(final File folder) {
            LOG.info("folder exists ?????? " + folder.exists());
            List<File> apks = new ArrayList<>();
            if(folder != null && folder.listFiles() != null){
                LOG.info("Listing all the files under the folder ::::::::::: "+ folder.getName());
                LOG.info(folder.listFiles());
                for (final File fileEntry : folder.listFiles()) {
                    if (fileEntry.isFile() && fileEntry.getName().contains(".apk")) {
                        apks.add(fileEntry.getAbsoluteFile());
                    }
                }
            }else{
                LOG.info("folder is null ::::::::: " + folder == null);
                LOG.info("list files is null ::::::::: " + folder.listFiles()==null);
            }
            LOG.info("Folder size in build directory :::::::::::: " + apks.size());
            return apks;
        }

        private boolean executeBuildScript(List<String> commandArgs, Context context) throws Exception {
            ProcessBuilder processBuilder = new ProcessBuilder(commandArgs);
            processBuilder.environment().clear();
            processBuilder.directory(new File(getBuildCommandPath(new StringBuffer()).toString()));
            processBuilder.inheritIO();
            this.mProcess = processBuilder.start();
            boolean flag = false;
            try {
                StreamDrainer<String> drainer = StreamDrainer.getTextStreamDrainer(this.mProcess);
                int exitVal = this.mProcess.waitFor();
                LOG.info("Exit Status received from command line ::::: " + exitVal);
                context.saveJobResult("exitStatus", exitVal);
                String output = drainer.getInputStream();
                if (output != null && output.length() != 0) {
                    context.saveJobResult("output", output);
                }
                String error = drainer.getErrorStream();
                if (error != null && error.length() != 0) {
                    context.saveJobResult("error", error);
                }
                if (exitVal != 0) {
                    LOG.error(String.format("Build returned exit code [%s] but expected [%s] on success.", exitVal, 0));
                    //throw new Exception(String.format("File returned exit code [%s] but expected [%s] on success.", exitVal, 0));
                }
                flag = true;
            }catch(Exception e){
                flag = true;
            }

            if (this.mProcess != null){
                this.mProcess.destroy();
            }

            return flag;

        }

        private StringBuffer getBuildCommandPath(StringBuffer command){
            //  /data/app/kettle/prod/scripts
            command.append(File.separator)
                    .append("data").append(File.separator)
                    .append("app").append(File.separator)
                    .append("kettle").append(File.separator)
                    .append("scripts").append(File.separator);
            return command;
        }
        
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

}
