/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.jobs.config;

import java.io.IOException;
import java.util.Properties;
import java.util.TimeZone;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.carfey.ops.job.di.SpringContextAware;
import com.stpl.tech.forms.core.config.FormsConfig;
import com.stpl.tech.kettle.core.config.DataLakeExternalConfig;
import com.stpl.tech.kettle.core.config.TransactionExternalConfig;
import com.stpl.tech.kettle.etl.confg.KettleETLConfig;
import com.stpl.tech.loggingservice.config.KettleLoggingConfig;

@Configuration
@EnableTransactionManagement
@PropertySource({ "classpath:props/jdbc-${env.type}.properties" })
@ComponentScan(basePackages = {
		"com.stpl.tech.kettle.jobs.*",
		"com.stpl.tech.master.core.apps.service",
		"com.stpl.tech.kettle.etl"})
@EnableJpaRepositories(basePackages = {"com.stpl.tech.kettle.jobs.mysql.data.dao.impl"},
		entityManagerFactoryRef = "KettleJobDataSourceEMFactory", transactionManagerRef = "KettleJobDataSourceTM")
@Import(value = {KettleLoggingConfig.class, TransactionExternalConfig.class, FormsConfig.class, SpringContextAware.class,
		KettleETLConfig.class, KettleJobsProperties.class, DataLakeExternalConfig.class, MongoConfig.class})
public class KettleJobsConfig {


	@Autowired
	private Environment env;

	private static final Logger LOG = LoggerFactory.getLogger(KettleJobsConfig.class);
	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
		String propertyFile = "/props/jdbc-" + System.getProperty("env.type") + ".properties";
		Properties props = new Properties();
		try {
			props.load(KettleJobsConfig.class.getResourceAsStream(propertyFile));
			com.carfey.jdk.sys.Configurator.setOverride(props);
		} catch (IOException e) {
			LOG.error("Unable to Load Properties file" + propertyFile);
		}
	}

	final Properties additionalProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
		hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
		hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
		return hibernateProperties;
	}

	@Bean(name = "KettleJobDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean stockEntityManagerFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(stockDataSource());
		em.setPackagesToScan("com.stpl.tech.kettle.jobs.mysql.data.model");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(additionalProperties());
		em.setPersistenceUnitName("KettleJobDataSourcePUName");
		return em;
	}

	@Bean(name = "KettleJobDataSource")
	public DataSource stockDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getProperty("jobs.jdbc.driverClassName"));
		dataSource.setUrl(env.getProperty("jobs.jdbc.url"));
		dataSource.setUsername(env.getProperty("jobs.jdbc.user"));
		dataSource.setPassword(env.getProperty("jobs.jdbc.pass"));
		return dataSource;
	}

	@Bean(name = "KettleJobDataSourceTM")
	public PlatformTransactionManager stockTransactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(stockEntityManagerFactory().getObject());
		return transactionManager;
	}

	@Bean(name = "KettleJobDataSourceET")
	public PersistenceExceptionTranslationPostProcessor scmExceptionTranslation() {
		return new PersistenceExceptionTranslationPostProcessor();
	}
}