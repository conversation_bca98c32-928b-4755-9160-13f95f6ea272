package com.stpl.tech.kettle.jobs.config;

import java.util.Arrays;
import java.util.Collection;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;

@Configuration
public class MongoConfig extends AbstractMongoClientConfiguration {

	@Autowired
	private KettleJobsProperties envr;

	@Override
	protected String getDatabaseName() {
		return envr.getMongoSchema();
	}

	@Override
	public MongoClient mongoClient() {
		ConnectionString connectionString = new ConnectionString(envr.getMongoURI());
		MongoClientSettings mongoClientSettings = MongoClientSettings.builder().applyConnectionString(connectionString)
				.build();
		return MongoClients.create(mongoClientSettings);
	}

	@Override
	public Collection<String> getMappingBasePackages() {
		return Arrays.asList("com.stpl.tech.master.core.apps.dao", "com.stpl.tech.kettle.jobs.mongo.data.dao");
	}
}
