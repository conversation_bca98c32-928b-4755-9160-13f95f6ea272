package com.stpl.tech.kettle.jobs.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.carfey.ops.job.Context;
import com.carfey.ops.job.param.Description;
import com.stpl.tech.forms.core.service.WarningService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.AppConstants;

public class AutomatedWarningActionJob {
	
	@Component
	@Description("Warning AM Action Response")
	public static class AMWarningActionJob extends AbstractAutomatedJobReport {

		@Autowired
		private EnvironmentProperties props;
		@Autowired
		private WarningService warningService;

		@Override
		public void execute(Context context) throws Exception {
			warningService.processWarningActionJobs(AppConstants.AM_RESPONSE);
		}
		
		@Override
		public EnvironmentProperties getProps() {
			return props;
		}
	}
	
	
	@Component
	@Description("Warning DGM Action Response")
	public static class DGMWarningActionJob extends AbstractAutomatedJobReport {

		@Autowired
		private EnvironmentProperties props;
		@Autowired
		private WarningService warningService;

		@Override
		public void execute(Context context) throws Exception {
			warningService.processWarningActionJobs(AppConstants.DGM_RESPONSE);
		}
		
		@Override
		public EnvironmentProperties getProps() {
			return props;
		}
	}
}
