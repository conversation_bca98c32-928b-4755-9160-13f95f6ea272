package com.stpl.tech.kettle.jobs.util;

import com.google.gson.Gson;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Map;

import static com.stpl.tech.master.core.WebServiceHelper.AUTH;

@Component
public class WebServiceHelper {

    private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);

    @Autowired
    private RestTemplate restTemplate;

    private static final int DEFAULT_SOCKET_TIMEOUT = 10;
    private static final int DEFAULT_CONNECTION_TIME_OUT = 100;
    private static HttpClient WEB_SERVICE_CLIENT;


    public <T> T postWithAuth(String endpoint, String token, Object body, Class<T> clazz) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.set("auth-internal", token);
        requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return callApi(endpoint, HttpMethod.POST, clazz, body, null, requestHeaders);
    }

    public static HttpResponse getRequestWithAuthInternal(String endpoint, String authorizationHeader)
            throws ClientProtocolException, IOException {
        HttpGet request = new HttpGet(endpoint);
        request.setHeader("auth-internal", authorizationHeader);
//    StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
//    request.setEntity(entity);
        return executeRequestWithTimeouts(request, DEFAULT_SOCKET_TIMEOUT, DEFAULT_CONNECTION_TIME_OUT);
    }

    public static HttpResponse getRequestWithAuth(String endpoint, String authorizationHeader)
            throws ClientProtocolException, IOException {
        HttpGet request = new HttpGet(endpoint);
        request.setHeader("auth", authorizationHeader);
//    StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
//    request.setEntity(entity);
        return executeRequestWithTimeouts(request, DEFAULT_SOCKET_TIMEOUT, DEFAULT_CONNECTION_TIME_OUT);
    }

    public <T> T callInternalApi(String endpoint, String token, HttpMethod method, Class<T> clazz, Object body,
                                 Map<String, ?> uriVariables) {
        return exchangeWithAuth(endpoint, token, method, clazz, body, uriVariables, MediaType.APPLICATION_JSON_UTF8);
    }

    private String resolvePathVariables(String serviceURL, Map<String, String> pathVariables) {
        if (pathVariables != null && !pathVariables.isEmpty()) {
            for (Map.Entry<String, String> entry : pathVariables.entrySet()) {
                LOG.info("Parameter : " + entry.getKey() + ", Value : " + entry.getValue());
                String key = "{" + entry.getKey() + "}";
                if (serviceURL.toLowerCase().contains(key.toLowerCase())) {
                    serviceURL = serviceURL.replace(key, entry.getValue());
                }
            }
        }
        return serviceURL;
    }

    private <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, Object body,
                                   Map<String, ?> uriVariables, MediaType type) {
        if (type == null) {
            type = MediaType.APPLICATION_JSON_UTF8;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(type));
        headers.setContentType(type);
        headers.set("auth-internal", token);
        return callApi(endpoint, method, clazz, body, uriVariables, headers);
    }

    private <T> T exchange(String endpoint, HttpMethod method, Class<T> clazz, Object body,
                           Map<String, ?> uriVariables, Map<String, ?> headerVariables) {

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headerVariables.keySet().forEach(s -> headers.set(s, headerVariables.get(s).toString()));
        return callApi(endpoint, method, clazz, body, uriVariables, headers);
    }

    private <T> T exchange(String endpoint, HttpMethod method, Class<T> clazz,
                           Map<String, String> pathVariable, Map<String, ?> headerVariables) {

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headerVariables.keySet().forEach(s -> {
            headers.set(s, headerVariables.get(s).toString());
        });
        return callApi(endpoint, method, clazz, pathVariable, headers);
    }

    private <T> T callApi(String endpoint, HttpMethod method, Class<T> clazz, Object body,
                          Map<String, ?> uriVariables, HttpHeaders headers) {
        HttpEntity entity;
        if (body != null) {
            entity = new HttpEntity(body, headers);
        } else {
            entity = new HttpEntity<String>(headers);
        }
        endpoint = setUriVariables(endpoint, uriVariables);
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::");
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        LOG.info("BODY::::::" + new Gson().toJson(body));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    private String setUriVariables(String endpoint, Map<String, ?> uriVariables) {
        if (uriVariables != null) {
            for (Map.Entry<String, ?> entry : uriVariables.entrySet()) {
                LOG.info("Parameter : " + entry.getKey() + ", Value : " + entry.getValue());
                String key = "{" + entry.getKey() + "}";
                if (endpoint.toLowerCase().contains(key.toLowerCase())) {
                	endpoint = endpoint.replace(key, (CharSequence) entry.getValue());
                }
            }
        }
        
        return endpoint;
    }

    private <T> T callApi(String endpoint, HttpMethod method, Class<T> clazz,
                          Map<String, String> pathVariables, HttpHeaders headers) {
        HttpEntity<String> entity;
        entity = new HttpEntity<String>(headers);
        endpoint = resolvePathVariables(endpoint, pathVariables);
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::");
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    public static HttpResponse getRequest(String endpoint, String authorizationHeader)
            throws ClientProtocolException, IOException {
        HttpGet request = new HttpGet(endpoint);
        request.setHeader(AUTH, authorizationHeader);
        return executeRequest(request);
    }

    private static HttpResponse executeRequest(HttpUriRequest requestObject)
            throws ClientProtocolException, IOException {
        return executeRequestWithTimeouts(requestObject, DEFAULT_SOCKET_TIMEOUT, DEFAULT_CONNECTION_TIME_OUT);
    }

    private static HttpResponse executeRequestWithTimeouts(HttpUriRequest requestObject, int socketTimeout,
                                                           int connectionTimeout) throws ClientProtocolException, IOException {
        HttpResponse response = getHttpClientInstance(socketTimeout, connectionTimeout).execute(requestObject);
        // clearing all the resources of the HTTP web service client
        if (response == null || response.getEntity() == null) {
            requestObject.abort();
        }
        return response;
    }

    private static HttpClient getHttpClientInstance(int socketTimeOut, int connectionTimeOut) {
        if (WEB_SERVICE_CLIENT == null) {
            RequestConfig config = RequestConfig.custom().setSocketTimeout(socketTimeOut * 1000)
                    .setConnectTimeout(connectionTimeOut * 1000).setConnectionRequestTimeout(connectionTimeOut * 1000)
                    .build();
            WEB_SERVICE_CLIENT = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
        }
        return WEB_SERVICE_CLIENT;
    }


}
