package com.stpl.tech.kettle.jobs.config;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

@Service
public class KettleJobsProperties {

	@Autowired
	Environment environment;

	public EnvType getEnvType() {
		return EnvType.valueOf(environment.getProperty("environment.type"));
	}

	public String getMongoURI() {
		return environment.getProperty("kettle.mongo.uri");
	}

	public String getMongoSchema() {
		return environment.getProperty("kettle.mongo.schema");
	}
}
