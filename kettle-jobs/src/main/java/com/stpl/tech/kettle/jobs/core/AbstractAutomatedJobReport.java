package com.stpl.tech.kettle.jobs.core;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

import com.carfey.ops.job.SchedulableJob;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.master.core.service.AbstractAutomatedReports;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JaxbUtil;

public abstract class AbstractAutomatedJobReport extends AbstractAutomatedReports implements SchedulableJob {

	@Override
	public EnvType getEnvironmentType() {
		return getProps().getEnvironmentType();
	}

	@Override
	public String getBasePath() {
		return getProps().getBasePath();
	}

	
	public abstract EnvironmentProperties getProps();

	protected void executeReport(String fileName, String subject, List<String> toEmail,
			Map<String, List<Object>> variables, Boolean write) throws ParseException, EmailGenerationException, IOException {
		ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class, getBasePath() + fileName);
		String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
				+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
		executeReports(subject, reportCategories, businessDate, false, toEmail.toArray(new String[toEmail.size()]),
				variables, write);

	}
	
	protected void executeReport(ReportCategories reportCategories, String subject, List<String> toEmail,
			Map<String, List<Object>> variables, Boolean write) throws ParseException, EmailGenerationException, IOException {
		String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
				+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
		executeReports(subject, reportCategories, businessDate, false, toEmail.toArray(new String[toEmail.size()]),
				variables, write);

	}

}
