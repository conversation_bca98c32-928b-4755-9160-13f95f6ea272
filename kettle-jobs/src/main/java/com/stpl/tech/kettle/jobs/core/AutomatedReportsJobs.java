
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.jobs.core;

import com.carfey.jdk.io.StreamDrainer;
import com.carfey.ops.job.Context;
import com.carfey.ops.job.SchedulableJob;
import com.carfey.ops.job.param.Configuration;
import com.carfey.ops.job.param.Description;
import com.carfey.ops.job.param.Parameter;
import com.carfey.ops.job.param.Type;
import com.google.common.base.Joiner;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.kettle.core.FeedbackFrequency;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.datalake.service.CustomerReportService;
import com.stpl.tech.kettle.etl.Converter.ETLDataConverter;
import com.stpl.tech.kettle.etl.core.JobExecutionDefinitionStatus;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.JobStatus;
import com.stpl.tech.kettle.etl.core.data.AggregatedResultDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionErrorDefinitionData;
import com.stpl.tech.kettle.etl.service.JobManagementService;
import com.stpl.tech.kettle.jobs.util.WebServiceHelper;
import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.report.metadata.model.ReportCategory;
import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.kettle.reports.external.impl.FTPTransmission;
import com.stpl.tech.master.core.external.report.service.ReportManagementService;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.notification.GenericNotification;
import com.stpl.tech.master.notification.ReportEmailNotification;
import com.stpl.tech.master.util.QueryExecutorForList;
import com.stpl.tech.master.util.ServiceUtil;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.ExecutionEnvironment;
import com.stpl.tech.util.JaxbUtil;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.ReportDetailData;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpResponse;
import org.pentaho.di.core.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

//import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
//import com.stpl.tech.kettle.etl.service.JobManagementService;

public class AutomatedReportsJobs {
    private static final Logger LOG = LoggerFactory.getLogger(AutomatedReportsJobs.class);

    // @Scheduled(cron = "0 45 5 * * *", zone = "GMT+05:30")
    @Component
    @Description("OHC EOD Daily Report")
    public static class OHCDailyEODReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        @Override
        public void execute(Context context) throws Exception {
            ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                props.getBasePath() + "/reports/OHCSalesReport.xml");
            String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate());
            List<GenericNotification> notification = executeReports(reportCategories, businessDate, true);

            // FTPTransmission ftp = new FTPTransmission("**************", 21,
            // "ftpusr", "Chaayos@1234", null);
            FTPTransmission ftp = new FTPTransmission("**************", 21, "posdsr", "pos[dsr]##123", null);
            ftp.transfer(props.getEnvironmentType(), props.getBasePath() + "/tmp/",
                notification.get(0).getAttachmentData().get(0));
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    @Description("Tally Daily Report")
    public static class TallyDailyReport extends AbstractAutomatedJobReport {

        private String filePath;
        private boolean generated = false;

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private JobManagementService jobManagementService;

        @Autowired
        FileArchiveService fileArchiveService;

        @Override
        public void execute(Context context) throws Exception {
            String businessDate = AppUtils.getDateString(AppUtils.getPreviousBusinessDate(), "dd-MM-yyyy");
            renderVariance(businessDate);
//			renderVariance("01-10-2020");
        }

        public void renderVariance(String businessDate) throws IOException, EmailGenerationException {
            ReportEmailNotification email = new ReportEmailNotification("TALLY REPORT FOR " + businessDate,
                props.getEnvironmentType());
            String[] toEmails = new String[]{"<EMAIL>"};
            String[] fileName = new String[]{"SALES_DATA", "PETTY_CASH", "PETTY_CASH_HAPPAY", "VENDOR_GR_DATA",
                "CASH_SETTLEMENT_DATA"};
            email.setFromEmail("<EMAIL>");
            email.setToEmails(setToEmails(toEmails));
            List<AttachmentData> attachments = new ArrayList<>();

            for (String file : fileName) {
                attachments.add(createAttachments(businessDate, file));
            }
            email.sendRawMail(attachments);
        }

        private AttachmentData createAttachments(String businessDate, String fileName) throws IOException {
            WorkbookContext salesDataWorkBook = WorkbookContextFactory.useXls().createWorkbook();
            List<AggregatedResultDefinitionData> salesData = jobManagementService.getTallyReport(businessDate,
                fileName);
            if (salesData != null) {
                createSaleDataSheet(salesDataWorkBook, salesData);
            } else {
                LOG.info(fileName + " Not Found");
            }
            String writtenPath = AppUtils.write(salesDataWorkBook.toNativeBytes(), props.getBasePath(), "Tally_Report",
                fileName + "_" + businessDate, LOG);
            if (writtenPath != null) {
                this.filePath = writtenPath;
                this.generated = true;
            }
            if (this.generated) {
                File file = new File(this.filePath);
                if (file.exists()) {
                    String mimeType = AppConstants.EXCEL_MIME_TYPE_XLS;
                    AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
                        fileName + "_" + businessDate, mimeType);
                    return reports;
//					saveExcelToS3(file);
                }
            }
            return null;
        }

        private void createSaleDataSheet(WorkbookContext workbookCtx, List<AggregatedResultDefinitionData> result) {
            SheetContext sheetCtx = workbookCtx.createSheet("Sheet1");

            sheetCtx.nextRow().header("Tran No").header("Tran Date").header("GL A/c Name").header("Amount")
                .header("Is Debit").header("Cost Center").header("Ref No").header("Ref Date").header("Narration");

            for (AggregatedResultDefinitionData data : result) {
                if (data.getDebitCredit() == null) {
                    continue;
                }
                if (data.getBusinessCostCentre() == null) {
                    sheetCtx.nextRow().text(data.getTransactionNumber() == null ? "" : data.getTransactionNumber())
                        .date(AppUtils.getDate(data.getBusinessDate(), "dd-MM-yyyy"))
                        .text(data.getAccountCode() == null ? "" : data.getAccountCode())
                        .number(data.getTotalSales() == null ? 0 : data.getTotalSales())
                        .text(data.getDebitCredit() == null ? "" : data.getDebitCredit())
                        .skipCell()
                        .text(data.getReferenceNumber() == null ? "" : data.getReferenceNumber())
                        .date(AppUtils.getDate(data.getBusinessDate(), "dd-MM-yyyy"))
                        .text(data.getNarration() == null ? "" : data.getNarration());
                } else {
                    sheetCtx.nextRow().text(data.getTransactionNumber() == null ? "" : data.getTransactionNumber())
                        .date(AppUtils.getDate(data.getBusinessDate(), "dd-MM-yyyy"))
                        .text(data.getAccountCode() == null ? "" : data.getAccountCode())
                        .number(data.getTotalSales() == null ? 0 : data.getTotalSales())
                        .text(data.getDebitCredit() == null ? "" : data.getDebitCredit())
                        .text(data.getBusinessCostCentre() == null ? "" : data.getBusinessCostCentre())
                        .text(data.getReferenceNumber() == null ? "" : data.getReferenceNumber())
                        .date(AppUtils.getDate(data.getBusinessDate(), "dd-MM-yyyy"))
                        .text(data.getNarration() == null ? "" : data.getNarration());
                }
            }
        }

        public String[] setToEmails(String[] toEmails) {
            return AppUtils.isDev(getEnvironmentType()) ? new String[]{"<EMAIL>"} : toEmails;
        }

        public void saveExcelToS3(File file) {
            try {

                String baseDir = file.getPath();
                String fileName = file.getName();

                fileName = fileName.replaceAll(" ", "_").toLowerCase();

                LOG.info(":::::: Request to upload Tally Report for business date {} ::::::",
                    AppUtils.getPreviousDate());

                FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3CrmBucket(), baseDir, fileName, file,
                    true);
                if (s3File != null) {
                    LOG.info(s3File.getName(), props.getCrmHostUrl() + s3File.getName());
                }
            } catch (Exception e) {
                LOG.error("Encountered error while uploading Tally Report to S3", e);
            }
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    // @Scheduled(cron = "0 10 7 * * *", zone = "GMT+05:30")
    @Description("Daily Feedback Report")
    public static class DailyFeedbackReportJob extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;
        @Autowired
        private FeedbackManagementService feedbackService;

        @Override
        public void execute(Context context) throws Exception {
            runFeedbackReport(FeedbackFrequency.CUMULATIVE, "/reports/CumulativeFeedback.xml");
            runFeedbackReport(FeedbackFrequency.DAILY, "/reports/DailyFeedback.xml");
            if (AppUtils.isTodayMonday()) {
                runFeedbackReport(FeedbackFrequency.WEEKLY, "/reports/WeeklyFeedback.xml");
            }
            if (AppUtils.isTodayTheFirstDayOfMonth()) {
                runFeedbackReport(FeedbackFrequency.MONTHLY, "/reports/MonthlyFeedback.xml");
            }
            if (AppUtils.isTodayTheSecondDayOfMonth()) {
                monthlyCsatScoreReport();
            }
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }

        private void monthlyCsatScoreReport() throws ParseException, EmailGenerationException, IOException {
            if (props.getRunAutoIntradayReports()) {
                ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                    props.getBasePath() + "/reports/MonthlyCSATReport.xml");
                executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
            }
        }

        private void runFeedbackReport(FeedbackFrequency frequency, String reportFile)
            throws ParseException, EmailGenerationException, IOException {
            feedbackService.updateFeedbackData(frequency);
            if (props.getRunAutoReports()) {
                ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                    props.getBasePath() + reportFile);
                executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getPreviousDate()));
            }
        }
    }

    // @Scheduled(fixedRate = 120000)
    // Cron for Monday 6:30 AM
    // @Scheduled(cron = "0 30 6 * * *", zone = "GMT+05:30")
    @Component
    @Description("Paid Employee Meal Notification")
    public static class PaidEmployeeMealReportJob extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        @Override
        public void execute(Context context) throws Exception {
            if (AppUtils.getCurrentDayofMonth() == props.getEmployeeMealMonthlyStartDate()) {
                ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                    props.getBasePath() + "/reports/EmployeeMealReport.xml");
                executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getPreviousDate()));
            }
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    @Description("Settlement Report")
    @Configuration(knownParameters = {@Parameter(name = "jobId", required = true, type = Type.INTEGER),
        @Parameter(name = "jobStepId", required = false, type = Type.INTEGER),
        @Parameter(name = "schemaSuffix", required = false, type = Type.STRING),
        @Parameter(name = "typeOfData", required = false, type = Type.STRING),
        @Parameter(name = "executedBy", required = false, type = Type.INTEGER)})
    public static class SettlementJob implements SchedulableJob {

        @Autowired
        private JobManagementService jobManagementService;

        @Autowired
        private Environment env;

        @Autowired
        private EnvProperties props;

        @Override
        public void execute(Context context) throws Exception {
            Map<String, String> params = new HashMap<>();
            Date previousDate = AppUtils.getPreviousBusinessDate();
            String businessDate = AppUtils.getSQLFormattedDate(previousDate);
            Integer jobId = context.getConfig().getInt("jobId");
            Integer jobStepId = context.getConfig().getInt("jobStepId");
            String schemaSuffix = context.getConfig().getString("schemaSuffix");
            String typeOfData = context.getConfig().getString("typeOfData");
            Integer executedBy = context.getConfig().getInt("executedBy");
            String url = env.getProperty("jobs.jdbc.url");
            String cleanURI = url.substring(5);
            URI uri = URI.create(cleanURI);
            String hostName = uri.getHost();
            jobManagementService.setStatusForRecordsAsInactiveForJobIdAndBusinessDate(jobId, previousDate);
            JobExecutionDefinitionData jobExecutionDefinitionData = jobManagementService
                .createExecutionDefinition(jobId, executedBy, AppUtils.getDate(businessDate, "yyyy-MM-dd"));
            params.put("BUSINESS_DATE", businessDate);
            params.put("JOB_EXECUTION_ID", jobExecutionDefinitionData.getExecutionId().toString());
            String executionTime = AppUtils.dateToString(jobExecutionDefinitionData.getExecutionTime());
            params.put("JOB_EXECUTION_TIME", executionTime);
            params.put("JOB_ID", jobId.toString());
            params.put("JOB_STEP_ID", jobStepId.toString());
            params.put("SCHEMA_SUFFIX", schemaSuffix);
            params.put("TYPE_OF_DATA", typeOfData);
            params.put("HOST_NAME", hostName);
            params.put("USERNAME", env.getProperty("jobs.jdbc.user"));
            params.put("PASSWORD", env.getProperty("jobs.jdbc.pass"));
            try {
                JobResponse<Result> jobResponse = jobManagementService.executeJob(jobExecutionDefinitionData, params);

                jobExecutionDefinitionData = updateJobExecutionStatus(jobExecutionDefinitionData, jobResponse);
            } catch (Exception e) {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FAILED, props.getEnvType());

                JobExecutionErrorDefinitionData errorDefinitionData = ETLDataConverter
                    .convert(jobExecutionDefinitionData, e);
                jobManagementService.addJobExecutionErrorDefinitionData(errorDefinitionData);
                throw e;
            }
        }

        private JobExecutionDefinitionData updateJobExecutionStatus(
            JobExecutionDefinitionData jobExecutionDefinitionData, JobResponse<Result> jobResponse) {
            if (jobResponse.getStatus().name().equals(JobStatus.FAILED.name())) {
                JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData = ETLDataConverter
                    .convert(jobExecutionDefinitionData, jobResponse);
                jobManagementService.addJobExecutionErrorDefinitionData(jobExecutionErrorDefinitionData);
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FAILED, props.getEnvType());
            } else {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FINISHED, props.getEnvType());
            }
            return jobExecutionDefinitionData;
        }
    }


//    @Component
//    @Description("Knock Knock Analytics Jobs")
//    @Configuration(knownParameters = {@Parameter(name = "jobId", required = true, type = Type.INTEGER),
//            @Parameter(name = "schemaSuffix", required = false, type = Type.STRING),
//            @Parameter(name = "executedBy", required = false, type = Type.INTEGER)})
//    public static class KnockKnockAnalyticsJob implements SchedulableJob {
//
//        @Autowired
//        private UnitIntraDaySalesDataService unitIntraDaySalesDataService;
//
//        @Autowired
//        private JobManagementService jobManagementService;
//
//        @Autowired
//        private Environment env;
//
//        @Autowired
//        private EnvProperties props;
//
//        @Autowired
//        private FirebaseNotificationService fireBaseService;
//
//        @Override
//        public void execute(Context context) throws Exception {
//                Map<String, String> params = new HashMap<>();
//                String businessDate = getCustomDate(AppUtils.getCurrentTimestamp());
//                Integer jobId = context.getConfig().getInt("jobId");
//                String schemaSuffix = context.getConfig().getString("schemaSuffix");
//                Integer executedBy = context.getConfig().getInt("executedBy");
//                String url = env.getProperty("jobs.jdbc.url");
//                String cleanURI = url.substring(5);
//                URI uri = URI.create(cleanURI);
//                String hostName = uri.getHost();
//                JobExecutionDefinitionData jobExecutionDefinitionData = jobManagementService
//                        .createExecutionDefinition(jobId, executedBy, AppUtils.getDate(businessDate, "yyyy-MM-dd HH:mm:ss"));
//                params.put("BUSINESS_DATE", businessDate);
//                params.put("SCHEMA_SUFFIX", schemaSuffix);
//                params.put("HOSTNAME", hostName);
//                params.put("USERNAME", env.getProperty("jobs.jdbc.user"));
//                params.put("PASSWORD", env.getProperty("jobs.jdbc.pass"));
//                try {
//                    JobResponse<Result> jobResponse = jobManagementService.executeJob(jobExecutionDefinitionData, params);
//                    List<UnitIntraDaySalesData> unitIntraDaySalesData = unitIntraDaySalesDataService.getUnitIntraDaySaleReport(businessDate);
//                    jobExecutionDefinitionData = updateJobExecutionStatus(jobExecutionDefinitionData, jobResponse);
//                    LOG.info("Job Status {}",jobExecutionDefinitionData.getJobExecutionStatus());
//                } catch (Exception e) {
//                    jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
//                            JobExecutionDefinitionStatus.FAILED, props.getEnvType());
//
//                    JobExecutionErrorDefinitionData errorDefinitionData = ETLDataConverter
//                            .convert(jobExecutionDefinitionData, e);
//                    jobManagementService.addJobExecutionErrorDefinitionData(errorDefinitionData);
//                    throw e;
//                }
//
//        }
//        private String getCustomDate(Date currentTimestamp) {
//            int min =AppUtils.getMinute(currentTimestamp);
//            int quaterToConsider=min/15;
//            return AppUtils.getSQLFormattedDate(currentTimestamp)+ " "+AppUtils.getHour(currentTimestamp) +":"+(quaterToConsider==0?"00" :quaterToConsider*15) +":00";
//        }
//
//        private JobExecutionDefinitionData updateJobExecutionStatus(
//                JobExecutionDefinitionData jobExecutionDefinitionData, JobResponse<Result> jobResponse) {
//            if (jobResponse.getStatus().name().equals(JobStatus.FAILED.name())) {
//                JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData = ETLDataConverter
//                        .convert(jobExecutionDefinitionData, jobResponse);
//                jobManagementService.addJobExecutionErrorDefinitionData(jobExecutionErrorDefinitionData);
//                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
//                        JobExecutionDefinitionStatus.FAILED, props.getEnvType());
//            } else {
//                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
//                        JobExecutionDefinitionStatus.FINISHED, props.getEnvType());
////                fireBaseService.sendNotification(props.getEnvType(),);
//            }
//            return jobExecutionDefinitionData;
//        }
//    }


    @Component
    @Description("Fountain 9- Inventory Data")
    @Configuration(knownParameters = {@Parameter(name = "jobId", required = true, type = Type.INTEGER),
        @Parameter(name = "schemaSuffix", required = false, type = Type.STRING),
        @Parameter(name = "fileName", required = true, type = Type.STRING),
        @Parameter(name = "executedBy", required = false, type = Type.INTEGER)})
    public static class InventoryDataJob implements SchedulableJob {

        @Autowired
        private JobManagementService jobManagementService;

        @Autowired
        private Environment env;

        @Autowired
        private EnvProperties props;

        @Autowired
        private EnvironmentProperties envProps;

        @Autowired
        FileArchiveService fileArchiveService;

        @Override
        public void execute(Context context) throws Exception {
            Map<String, String> params = new HashMap<>();
            Date previousDate = AppUtils.getPreviousBusinessDate();
            String businessDate = AppUtils.getSQLFormattedDate(previousDate);
            Integer jobId = context.getConfig().getInt("jobId");
            String schemaSuffix = context.getConfig().getString("schemaSuffix");
            String fileName = context.getConfig().getString("fileName");
            Integer executedBy = context.getConfig().getInt("executedBy");
            String url = env.getProperty("jobs.jdbc.url");
            String cleanURI = url.substring(5);
            URI uri = URI.create(cleanURI);
            String hostName = uri.getHost();
            JobExecutionDefinitionData jobExecutionDefinitionData = jobManagementService
                .createExecutionDefinition(jobId, executedBy, AppUtils.getDate(businessDate, "yyyy-MM-dd"));
            params.put("BUSINESS_DATE", businessDate);
            params.put("SCHEMA_SUFFIX", schemaSuffix);
            params.put("HOSTNAME", hostName);
            params.put("USERNAME", env.getProperty("jobs.jdbc.user"));
            params.put("PASSWORD", env.getProperty("jobs.jdbc.pass"));
            try {
                JobResponse<Result> jobResponse = jobManagementService.executeJob(jobExecutionDefinitionData, params);
                jobExecutionDefinitionData = updateJobExecutionStatus(jobExecutionDefinitionData, jobResponse, fileName);
            } catch (Exception e) {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FAILED, props.getEnvType());

                JobExecutionErrorDefinitionData errorDefinitionData = ETLDataConverter
                    .convert(jobExecutionDefinitionData, e);
                jobManagementService.addJobExecutionErrorDefinitionData(errorDefinitionData);
                throw e;
            }
        }

        private JobExecutionDefinitionData updateJobExecutionStatus(
            JobExecutionDefinitionData jobExecutionDefinitionData, JobResponse<Result> jobResponse, String fileName) throws IOException {
            if (jobResponse.getStatus().name().equals(JobStatus.FAILED.name())) {
                JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData = ETLDataConverter
                    .convert(jobExecutionDefinitionData, jobResponse);
                jobManagementService.addJobExecutionErrorDefinitionData(jobExecutionErrorDefinitionData);
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FAILED, props.getEnvType());
            } else {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FINISHED, props.getEnvType());
                uploadFile(fileName);
            }
            return jobExecutionDefinitionData;
        }

        public void saveReportToS3(File file, String base) {
            try {
                String baseDir = base;
                String fileName;
                if(file.getName().contains("branch_master")){
                    fileName="branch_master";
                }else if (file.getName().contains("product_master")){
                    fileName="product_master";
                }
                else{
                    fileName=file.getName();
                }
                fileName = fileName.replaceAll(" ", "_").toLowerCase();
                LOG.info(":::::: Request to upload Inventory Data for business date {} ::::::",
                    AppUtils.getPreviousDate());
                FileDetail s3File = fileArchiveService.saveFileToS3(envProps.getS3InventoryBucket(), baseDir, fileName, file,
                    true);
                if (s3File != null) {
                    LOG.info(s3File.getName(), s3File.getName());
                }
            } catch (Exception e) {
                LOG.error("Encountered error while uploading Inventory Report to S3", e);
            }
        }

        private void uploadFile(String fileName) throws IOException {
            String filePath;
            if(fileName.contains("branch_master") || fileName.contains("product_master")){
                filePath = envProps.getBasePath() + "/jobs/inventory/files/" + fileName + ".csv";
            }
            else {
                filePath = envProps.getBasePath() + "/jobs/inventory/files/" + fileName + "_" + AppUtils.getSQLFormattedDate(AppUtils.getPreviousDate()) + ".csv";
            }
            File file = new File(filePath);
            if (file.exists()) {
                saveReportToS3(file, fileName);
            }
        }
    }

    @Component
    @Description("Fountain 9- Inventory Data with Date Range")
    @Configuration(knownParameters = {@Parameter(name = "jobId", required = true, type = Type.INTEGER),
        @Parameter(name = "schemaSuffix", required = false, type = Type.STRING),
        @Parameter(name = "fileName", required = true, type = Type.STRING),
        @Parameter(name = "executedBy", required = false, type = Type.INTEGER),
        @Parameter(name = "startDate", required = true, type = Type.STRING),
        @Parameter(name = "endDate", required = true, type = Type.STRING)})
    public static class InventoryDataDateRangeJob implements SchedulableJob {

        @Autowired
        private JobManagementService jobManagementService;

        @Autowired
        private Environment env;

        @Autowired
        private EnvProperties props;

        @Autowired
        private EnvironmentProperties envProps;

        @Autowired
        FileArchiveService fileArchiveService;

        @Override
        public void execute(Context context) throws Exception {
            Map<String, String> params = new HashMap<>();
            Date previousDate = AppUtils.getPreviousBusinessDate();
            String businessDate = AppUtils.getSQLFormattedDate(previousDate);
            Date startDate = AppUtils.getDate(context.getConfig().getString("startDate"), "yyyy-MM-dd");
            Date endDate = AppUtils.getDate(context.getConfig().getString("endDate"), "yyyy-MM-dd");
            Integer jobId = context.getConfig().getInt("jobId");
            String schemaSuffix = context.getConfig().getString("schemaSuffix");
            String fileName = context.getConfig().getString("fileName");
            Integer executedBy = context.getConfig().getInt("executedBy");
            String url = env.getProperty("jobs.jdbc.url");
            String cleanURI = url.substring(5);
            URI uri = URI.create(cleanURI);
            String hostName = uri.getHost();
            JobExecutionDefinitionData jobExecutionDefinitionData = jobManagementService
                .createExecutionDefinition(jobId, executedBy, AppUtils.getDate(businessDate, "yyyy-MM-dd"));
            params.put("SCHEMA_SUFFIX", schemaSuffix);
            params.put("HOSTNAME", hostName);
            params.put("USERNAME", env.getProperty("jobs.jdbc.user"));
            params.put("PASSWORD", env.getProperty("jobs.jdbc.pass"));
            for (Date date : AppUtils.getDaysBetweenDates(startDate, endDate, true)) {
                try {
                    params.put("BUSINESS_DATE", AppUtils.getSQLFormattedDate(date));
                    JobResponse<Result> jobResponse = jobManagementService.executeJob(jobExecutionDefinitionData, params);
                    jobExecutionDefinitionData = updateJobExecutionStatus(jobExecutionDefinitionData, jobResponse, fileName);
                } catch (Exception e) {
                    jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                        JobExecutionDefinitionStatus.FAILED, props.getEnvType());

                    JobExecutionErrorDefinitionData errorDefinitionData = ETLDataConverter
                        .convert(jobExecutionDefinitionData, e);
                    jobManagementService.addJobExecutionErrorDefinitionData(errorDefinitionData);
                    throw e;
                }
            }
        }


        private JobExecutionDefinitionData updateJobExecutionStatus(
            JobExecutionDefinitionData jobExecutionDefinitionData, JobResponse<Result> jobResponse, String fileName) throws IOException {
            if (jobResponse.getStatus().name().equals(JobStatus.FAILED.name())) {
                JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData = ETLDataConverter
                    .convert(jobExecutionDefinitionData, jobResponse);
                jobManagementService.addJobExecutionErrorDefinitionData(jobExecutionErrorDefinitionData);
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FAILED, props.getEnvType());
            } else {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FINISHED, props.getEnvType());
                uploadFile(fileName);
            }
            return jobExecutionDefinitionData;
        }

        public void saveReportToS3(File file, String base) {
            try {
                String baseDir = base;
                String fileName;
                if(file.getName().contains("branch_master")){
                    fileName="branch_master";
                }else if (file.getName().contains("product_master")){
                    fileName="product_master";
                }
                else{
                    fileName=file.getName();
                }
                fileName = fileName.replaceAll(" ", "_").toLowerCase();
                LOG.info(":::::: Request to upload Inventory Data for business date {} ::::::",
                        AppUtils.getPreviousDate());
                FileDetail s3File = fileArchiveService.saveFileToS3(envProps.getS3InventoryBucket(), baseDir, fileName, file,
                        true);
                if (s3File != null) {
                    LOG.info(s3File.getName(), s3File.getName());
                }
            } catch (Exception e) {
                LOG.error("Encountered error while uploading Inventory Report to S3", e);
            }
        }

        private void uploadFile(String fileName) throws IOException {
            String filePath;
            if(fileName.contains("branch_master") || fileName.contains("product_master")){
                filePath = envProps.getBasePath() + "/jobs/inventory/files/" + fileName + ".csv";
            }
            else {
                filePath = envProps.getBasePath() + "/jobs/inventory/files/" + fileName + "_" + AppUtils.getSQLFormattedDate(AppUtils.getPreviousDate()) + ".csv";
            }
            File file = new File(filePath);
            if (file.exists()) {
                saveReportToS3(file, fileName);
            }
        }
    }

    @Component
    @Description("Settlement Report with Date Range")
    @Configuration(knownParameters = {@Parameter(name = "jobId", required = true, type = Type.INTEGER),
        @Parameter(name = "jobStepId", required = true, type = Type.INTEGER),
        @Parameter(name = "schemaSuffix", required = false, type = Type.STRING),
        @Parameter(name = "typeOfData", required = true, type = Type.STRING),
        @Parameter(name = "executedBy", required = true, type = Type.INTEGER),
        @Parameter(name = "startDate", required = true, type = Type.STRING),
        @Parameter(name = "endDate", required = true, type = Type.STRING)})
    public static class DatedSettlementJob implements SchedulableJob {
        @Autowired
        private JobManagementService jobManagementService;

        @Autowired
        private Environment env;

        @Autowired
        private EnvProperties props;

        @Override
        public void execute(Context context) throws Exception {
            Map<String, String> params = new HashMap<>();
            Date previousDate = AppUtils.getPreviousBusinessDate();
            String businessDate = AppUtils.getSQLFormattedDate(previousDate);
            Date startDate = AppUtils.getDate(context.getConfig().getString("startDate"), "yyyy-MM-dd");
            Date endDate = AppUtils.getDate(context.getConfig().getString("endDate"), "yyyy-MM-dd");
            Integer jobId = context.getConfig().getInt("jobId");
            Integer jobStepId = context.getConfig().getInt("jobStepId");
            String schemaSuffix = context.getConfig().getString("schemaSuffix");
            String typeOfData = context.getConfig().getString("typeOfData");
            Integer executedBy = context.getConfig().getInt("executedBy");

            String url = env.getProperty("jobs.jdbc.url");
            String cleanURI = url.substring(5);
            URI uri = URI.create(cleanURI);
            String hostName = uri.getHost();

            params.put("JOB_ID", jobId.toString());
            params.put("JOB_STEP_ID", jobStepId.toString());
            params.put("SCHEMA_SUFFIX", schemaSuffix);
            params.put("TYPE_OF_DATA", typeOfData);
            params.put("PASSWORD", env.getProperty("jobs.jdbc.pass"));

            params.put("HOST_NAME", hostName);
            params.put("USERNAME", env.getProperty("jobs.jdbc.user"));

            for (Date date : AppUtils.getDaysBetweenDates(startDate, endDate, true)) {
                jobManagementService.setStatusForRecordsAsInactiveForJobIdAndBusinessDate(jobId, date);
                JobExecutionDefinitionData jobExecutionDefinitionData = jobManagementService
                    .createExecutionDefinition(jobId, executedBy, date);
                params.put("BUSINESS_DATE", AppUtils.getSQLFormattedDate(date));
                params.put("JOB_EXECUTION_ID", jobExecutionDefinitionData.getExecutionId().toString());
                String executionTime = AppUtils.dateToString(jobExecutionDefinitionData.getExecutionTime());
                params.put("JOB_EXECUTION_TIME", executionTime);
                try {
                    JobResponse<Result> jobResponse = jobManagementService.executeJob(jobExecutionDefinitionData,
                        params);
                    jobExecutionDefinitionData = updateJobExecutionStatus(jobExecutionDefinitionData, jobResponse);
                } catch (Exception e) {
                    jobExecutionDefinitionData = jobManagementService
                        .updateJobExecutionStatus(jobExecutionDefinitionData, JobExecutionDefinitionStatus.FAILED, props.getEnvType());
                    JobExecutionErrorDefinitionData errorDefinitionData = ETLDataConverter
                        .convert(jobExecutionDefinitionData, e);
                    jobManagementService.addJobExecutionErrorDefinitionData(errorDefinitionData);
                    throw e;
                }

            }
        }

        private JobExecutionDefinitionData updateJobExecutionStatus(
            JobExecutionDefinitionData jobExecutionDefinitionData, JobResponse<Result> jobResponse) {
            if (jobResponse.getStatus().name().equals(JobStatus.FAILED.name())) {
                JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData = ETLDataConverter
                    .convert(jobExecutionDefinitionData, jobResponse);
                jobManagementService.addJobExecutionErrorDefinitionData(jobExecutionErrorDefinitionData);
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FAILED, props.getEnvType());
            } else {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                    JobExecutionDefinitionStatus.FINISHED, props.getEnvType());
            }
            return jobExecutionDefinitionData;
        }
    }

    // @Scheduled(cron = "0 30 4 * * *", zone = "GMT+05:30")
    @Component
    @Description("NPS Calculation")
    public static class NPSCalculationJob implements SchedulableJob {

        private static final Logger LOG = LoggerFactory.getLogger(AutomatedPartnerIntraDayReport.class);
        @Autowired
        private FeedbackManagementService feedbackService;

        @Override
        public void execute(Context context) throws Exception {
            LOG.info("Running NPS Proc at {}", AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp()));
            try {
                feedbackService.runNpsProc();
            } catch (Exception e) {
                LOG.error("Error While running NPS Proc");
            }

        }
    }

    @Component
    @Description("Customer One View Calculation")
    @Configuration(knownParameters = {@Parameter(name = "startDate", required = false, type = Type.STRING),
        @Parameter(name = "endDate", required = false, type = Type.STRING)})
    public static class CustomerOneViewCalculationJob implements SchedulableJob {

        private static final Logger LOG = LoggerFactory.getLogger(AutomatedPartnerIntraDayReport.class);
        @Autowired
        private CustomerReportService customerReportService;

        @Override
        public void execute(Context context) throws Exception {
            LOG.info("Running NPS calculateNpsForBusinessDate Proc at {}",
                AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp()));
            try {
                Date startDate = AppUtils.getDate(context.getConfig().getString("startDate"), "yyyy-MM-dd");
                Date endDate = AppUtils.getDate(context.getConfig().getString("endDate"), "yyyy-MM-dd");
                if (startDate == null || endDate == null) {
                    runProcs(AppUtils.getPreviousBusinessDate());
                } else {
                    Date date = startDate;
                    while (date.compareTo(endDate) < 0) {
                        runProcs(date);
                        date = AppUtils.addDays(date, 1);
                    }
                }
            } catch (Exception e) {
                LOG.error("Error While running NPS Proc");
            }

        }

        private boolean runProcs(Date date) {
            customerReportService.calculateNpsForBusinessDate(date);
            customerReportService.calculateCustomerOneViewFeedbackStats(date);
            customerReportService.calculateFeedbackForBusinessDate(date);
            customerReportService.calculateCustomerOneViewOrderFeedbackStats(date);
            return true;
        }

    }

    
    @Component
    @Description("Unit Wise Intra Day Report")
    @Configuration(knownParameters = {
        @Parameter(name = "toEmail", required = false, allowMultiple = true, type = Type.STRING)})
    public static class AutomatedPartnerIntraDayReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        /*
         * (non-Javadoc)
         *
         * @see com.carfey.ops.job.SchedulableJob#execute(com.carfey.ops.job.Context)
         */
        @Override
        public void execute(Context context) throws Exception {
            String fileName = "/reports/kettle/UnitWiseIntraDayReports.xml";
            List<String> toEmail = context.getConfig().getStringList("toEmail");
            String subject = context.getJobNickname();
            Map<String, List<Object>> variables = context.getConfig().getRawValues();
            executeReport(fileName, subject, toEmail, variables, false);
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }

    }

    @Component
    @Description("Unit Wise Intra Day Report For All")
    @Configuration(knownParameters = {
        @Parameter(name = "toEmail", required = false, allowMultiple = true, type = Type.STRING)})
    public static class AutomatedAllPartnerIntraDayReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;
        private static final Logger LOG = LoggerFactory.getLogger(AutomatedAllPartnerIntraDayReport.class);

        /*
         * (non-Javadoc)
         *
         * @see com.carfey.ops.job.SchedulableJob#execute(com.carfey.ops.job.Context)
         */
        @Override
        public void execute(Context context) throws Exception {
            String fileName = "/reports/kettle/UnitWiseIntraDayReportsForAll.xml";
            Map<String, List<Object>> variables = context.getConfig().getRawValues();
            // variables.put("unitIds", new ArrayList<>());
            List<String> toEmail = context.getConfig().getStringList("toEmail");
            Map<Integer, List<String>> emails = new HashMap<>();
            Map<IdCodeName, List<ReportDetailData>> reports = new HashMap<>();
            for (String key : variables.keySet()) {
                Integer unitId = null;
                try {
                    unitId = Integer.valueOf(key);
                } catch (Exception e) {
                    LOG.info("Not a number " + key);
                }
                if (unitId != null) {
                    emails.put(unitId, new ArrayList<>());
                    variables.get("unitIds").add(unitId);
                    for (Object o : variables.get(key)) {
                        emails.get(unitId).add((String) o);
                    }
                    emails.get(unitId).addAll(toEmail);
                }
            }

            ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                getBasePath() + fileName);
            String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
                + AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());

            ReportCategory category = reportCategories.getCategory().get(0);
            LOG.info("Running automated report: {}", category.getName());
            for (ReportData report : category.getReport()) {
                if (report.getEnvironment() == null || report.getEnvironment().trim().length() == 0) {
                    report.setEnvironment(getEnvironmentType().getExecutionEnvironment().name());
                }
                setParams(report, variables);
                DataSource dataSource = ServiceUtil
                    .getDataSourceBean(ExecutionEnvironment.valueOf(report.getEnvironment()), false);
                QueryExecutorForList executor = new QueryExecutorForList();
                executor.execute(report, dataSource);
                List<List<String>> data = executor.getData();
                List<String> header = data.get(0);
                for (int i = 1; i < data.size(); i++) {
                    List<String> row = data.get(i);
                    Integer unitId = Integer.valueOf(row.get(0));
                    String unitName = row.get(1);
                    IdCodeName unit = new IdCodeName(unitId, unitName, unitName);
                    List<List<String>> unitData = new ArrayList<>();
                    unitData.add(header);
                    unitData.add(row);
                    ReportDetailData r = new ReportDetailData(report.getName(), unitData, true, new HashSet<>());
                    if (!reports.containsKey(unit)) {
                        reports.put(unit, new ArrayList<>());
                    }
                    reports.get(unit).add(r);
                }
            }

            for (IdCodeName unit : reports.keySet()) {
                String subject = unit.getName() + " Intra Day Report";
                GenericNotification mailNotification = createNotification(subject, category, businessDate,
                    emails.get(unit.getId()).toArray(new String[]{}));
                List<ReportDetailData> r = reports.get(unit);
                mailNotification.setReportsData(r);
                sendGenericNotification(mailNotification);
            }
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }

    }

    @Component
    @Description("Unit Wise Intra Day Report")
    @Configuration(knownParameters = {
        @Parameter(name = "toEmail", required = false, allowMultiple = true, type = Type.STRING)})
    public static class AutomatedPartnerIntraDayWithBulkOrderReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        /*
         * (non-Javadoc)
         *
         * @see com.carfey.ops.job.SchedulableJob#execute(com.carfey.ops.job.Context)
         */
        @Override
        public void execute(Context context) throws Exception {
            String fileName = "/reports/kettle/UnitWiseIntraDayWithBulkOrderReports.xml";
            List<String> toEmail = context.getConfig().getStringList("toEmail");
            String subject = context.getJobNickname();
            Map<String, List<Object>> variables = context.getConfig().getRawValues();
            executeReport(fileName, subject, toEmail, variables, false);
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }

    }

    @Component
    @Description("Unit Wise Quarterly NPS Report")
    @Configuration(knownParameters = {
        @Parameter(name = "toEmail", required = true, allowMultiple = true, type = Type.STRING)})
    public static class AutomatedQuarterlyNpsJob extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        /*
         * (non-Javadoc)
         *
         * @see com.carfey.ops.job.SchedulableJob#execute(com.carfey.ops.job.Context)
         */
        @Override
        public void execute(Context context) throws Exception {
            String fileName = "/reports/kettle/AutomatedQuarterlyNpsReport.xml";
            List<String> toEmail = context.getConfig().getStringList("toEmail");
            String subject = context.getJobNickname();
            Map<String, List<Object>> variables = context.getConfig().getRawValues();
            executeReport(fileName, subject, toEmail, variables, false);
        }

        /*
         * (non-Javadoc)
         *
         * @see com.stpl.tech.kettle.jobs.core.AbstractAutomatedJobReport#getProps()
         */
        @Override
        public EnvironmentProperties getProps() {
            return props;
        }

    }

    @Component
    @Description("Generic Python Report")
    @Configuration(knownParameters = {@Parameter(name = "scriptPath", required = true, type = Type.STRING),
        @Parameter(name = "toMails", required = true, allowMultiple = true, type = Type.STRING),
        @Parameter(name = "args", required = false, allowMultiple = true, type = Type.STRING)})
    public static class AutomatedGenericPythonJob extends AbstractAutomatedJobReport {

        private Process mProcess;

        @Autowired
        private EnvironmentProperties props;

        @Override
        public void execute(Context context) throws Exception {

            String scriptPath = context.getConfig().getString("scriptPath");
            List<String> toMails = context.getConfig().getStringList("toMails");
            String sendTo = Joiner.on(",").join(toMails);
            List<String> args = context.getConfig().getStringList("args");
            ProcessBuilder processBuilder = null;
            if (args != null && !args.isEmpty()) {
                String params = args.size() > 1 ? Joiner.on(" ").join(args) : args.get(0);
                processBuilder = new ProcessBuilder("python", scriptPath, params, sendTo);
            } else {
                processBuilder = new ProcessBuilder("python", scriptPath, sendTo);
            }
            processBuilder.environment().clear();
            processBuilder.inheritIO();
            this.mProcess = processBuilder.start();
            try {
                StreamDrainer<String> drainer = StreamDrainer.getTextStreamDrainer(this.mProcess);
                int exitVal = this.mProcess.waitFor();
                System.out.println("Exit Status received from command line ::::: " + exitVal);
                context.saveJobResult("exitStatus", exitVal);
                String output = drainer.getInputStream();
                if (output != null && output.length() != 0) {
                    context.saveJobResult("output", output);
                }
                String error = drainer.getErrorStream();
                if (error != null && error.length() != 0) {
                    context.saveJobResult("error", error);
                }
                if (exitVal != 0) {
                    System.out.println(
                        String.format("Build returned exit code [%s] but expected [%s] on success.", exitVal, 0));
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

            if (this.mProcess != null) {
                this.mProcess.destroy();
            }
        }

        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    @Description("Generic XML Report Jobs")
    @Configuration(knownParameters = {@Parameter(name = "fileName", required = true, type = Type.STRING),
        @Parameter(name = "toEmail", required = false, allowMultiple = true, type = Type.STRING),
        @Parameter(name = "write", required = false, type = Type.BOOLEAN)})
    public static class AutomatedGenericSqlReport extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        /*
         * (non-Javadoc)
         *
         * @see com.carfey.ops.job.SchedulableJob#execute(com.carfey.ops.job.Context)
         */
        @Override
        public void execute(Context context) throws Exception {
            String fileName = context.getConfig().getString("fileName");
            List<String> toEmail = context.getConfig().getStringList("toEmail") == null ? new ArrayList<>()
                : context.getConfig().getStringList("toEmail");
            String subject = context.getConfig().getString("subject");
            Map<String, List<Object>> variables = context.getConfig().getRawValues();
            Boolean write = context.getConfig().getBoolean("write");
            write = write == null ? false : write;
            executeReport(fileName, subject == null || subject.trim().equals("") ? null : subject, toEmail, variables,
                write);
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    @Description("Generic Report Jobs")
    @Configuration(knownParameters = {@Parameter(name = "reportId", required = true, type = Type.INTEGER),
        @Parameter(name = "toEmail", required = false, allowMultiple = true, type = Type.STRING),
        @Parameter(name = "write", required = false, type = Type.BOOLEAN)})
    public static class AutomatedGenericSqlReportById extends AbstractAutomatedJobReport {

        @Autowired
        private EnvironmentProperties props;

        @Autowired
        private ReportManagementService reportService;

        /*
         * (non-Javadoc)
         *
         * @see com.carfey.ops.job.SchedulableJob#execute(com.carfey.ops.job.Context)
         */
        @Override
        public void execute(Context context) throws Exception {
            ReportCategories categories = reportService.getReportCategory(context.getConfig().getInt("reportId"));
            List<String> toEmail = context.getConfig().getStringList("toEmail") == null ? new ArrayList<>()
                : context.getConfig().getStringList("toEmail");
            String subject = context.getConfig().getString("subject");
            Map<String, List<Object>> variables = context.getConfig().getRawValues();
            Boolean write = context.getConfig().getBoolean("write");
            write = write == null ? false : write;
            executeReport(categories, subject == null || subject.trim().equals("") ? null : subject, toEmail, variables,
                write);
        }

        @Override
        public EnvironmentProperties getProps() {
            return props;
        }
    }

    @Component
    @Description("Generic WebService Helper With Auth Internal")
    @Configuration(knownParameters = {
            @Parameter(name = "apiPath", required = true, type = Type.STRING),
            @Parameter(name = "token", required = true, type = Type.STRING)})
    public static class GenericWebServiceHelper implements SchedulableJob {
        @Autowired
        WebServiceHelper webServiceHelper;

        @Autowired
        EnvironmentProperties environmentProperties;

        @Override
        public void execute(Context context) throws Exception {
            try{
                String apiPath = context.getConfig().getString("apiPath");
                String token = context.getConfig().getString("token");
                HttpResponse httpResponse = WebServiceHelper.getRequestWithAuthInternal(apiPath, token);
                if (httpResponse.getEntity() != null && httpResponse.getEntity().getContent() != null) {
                    httpResponse.getEntity().getContent().close();
                }
            }catch (Exception e){
                LOG.error("Exception Caught While Calling API ",e);
            }
        }
    }
}

