package com.stpl.tech.kettle.jobs.core;

import com.carfey.ops.job.Context;
import com.carfey.ops.job.SchedulableJob;
import com.carfey.ops.job.param.Configuration;
import com.carfey.ops.job.param.Description;
import com.carfey.ops.job.param.Parameter;
import com.carfey.ops.job.param.Type;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.kettle.etl.Converter.ETLDataConverter;
import com.stpl.tech.kettle.etl.core.JobExecutionDefinitionStatus;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.JobStatus;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionErrorDefinitionData;
import com.stpl.tech.kettle.etl.service.JobManagementService;
import com.stpl.tech.kettle.jobs.util.WebServiceHelper;
import com.stpl.tech.util.AppUtils;
import org.apache.http.HttpResponse;
import org.pentaho.di.core.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class AutomatedAnalyticsJob {

    private static final Logger LOG = LoggerFactory.getLogger(AutomatedReportsJobs.class);

    @Component
    @Description("Knock Knock Analytics Jobs")
    @Configuration(knownParameters = {@Parameter(name = "jobId", required = true, type = Type.INTEGER),
            @Parameter(name = "schemaSuffix", required = false, type = Type.STRING),
            @Parameter(name = "apiReference", required = false, type = Type.STRING),
            @Parameter(name = "token", required = false, type = Type.STRING),
            @Parameter(name = "executedBy", required = false, type = Type.INTEGER)})
    public static class KnockKnockAnalyticsJob implements SchedulableJob {

        @Autowired
        private JobManagementService jobManagementService;

        @Autowired
        private Environment env;

        @Autowired
        private EnvProperties props;

        @Override
        public void execute(Context context) throws Exception {
            Map<String, String> params = new HashMap<>();
            String businessDate = getCustomDate(AppUtils.getCurrentTimestamp());
            Integer jobId = context.getConfig().getInt("jobId");
            String schemaSuffix = context.getConfig().getString("schemaSuffix");
            Integer executedBy = context.getConfig().getInt("executedBy");
            String url = env.getProperty("jobs.jdbc.url");
            String apiReference =context.getConfig().getString("apiReference") ;
            String token =context.getConfig().getString("token") ;
            String cleanURI = url.substring(5);
            URI uri = URI.create(cleanURI);
            String hostName = uri.getHost();
            JobExecutionDefinitionData jobExecutionDefinitionData = jobManagementService
                    .createExecutionDefinition(jobId, executedBy, AppUtils.getDate(businessDate, "yyyy-MM-dd HH:mm:ss"));
            params.put("BUSINESS_DATE", businessDate);
            params.put("SCHEMA_SUFFIX", schemaSuffix);
            params.put("HOSTNAME", hostName);
            params.put("USERNAME", env.getProperty("jobs.jdbc.user"));
            params.put("PASSWORD", env.getProperty("jobs.jdbc.pass"));
            try {
                JobResponse<Result> jobResponse = jobManagementService.executeJob(jobExecutionDefinitionData, params);
                jobExecutionDefinitionData = updateJobExecutionStatus(jobExecutionDefinitionData, jobResponse);
                LOG.info("Job Status {}",jobExecutionDefinitionData.getJobExecutionStatus());
                callKnockBackend(apiReference,token);
            } catch (Exception e) {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                        JobExecutionDefinitionStatus.FAILED, props.getEnvType());

                JobExecutionErrorDefinitionData errorDefinitionData = ETLDataConverter
                        .convert(jobExecutionDefinitionData, e);
                jobManagementService.addJobExecutionErrorDefinitionData(errorDefinitionData);
                throw e;
            }
        }

        private void callKnockBackend(String apiReference, String token) {
            try{
                LOG.info("Calling Knock Backend :::: {}",AppUtils.getCurrentTimestamp());
                HttpResponse httpResponse = WebServiceHelper.getRequestWithAuth(apiReference, token);
                if (httpResponse.getEntity() != null && httpResponse.getEntity().getContent() != null) {
                    httpResponse.getEntity().getContent().close();
                    LOG.info("Successfully Close Connection with API Request ::: {}",AppUtils.getCurrentTimestamp());
                }
            }catch (Exception e){
                LOG.error("Exception Caught While Calling API ",e);
            }
        }

        private String getCustomDate(Date currentTimestamp) {
            int min =AppUtils.getMinute(currentTimestamp);
            int quaterToConsider=min/15;
            return AppUtils.getSQLFormattedDate(currentTimestamp)+ " "+AppUtils.getHour(currentTimestamp) +":"+(quaterToConsider==0?"00" :quaterToConsider*15) +":00";
        }

        private JobExecutionDefinitionData updateJobExecutionStatus(
                JobExecutionDefinitionData jobExecutionDefinitionData, JobResponse<Result> jobResponse) {
            if (jobResponse.getStatus().name().equals(JobStatus.FAILED.name())) {
                JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData = ETLDataConverter
                        .convert(jobExecutionDefinitionData, jobResponse);
                jobManagementService.addJobExecutionErrorDefinitionData(jobExecutionErrorDefinitionData);
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                        JobExecutionDefinitionStatus.FAILED, props.getEnvType());
            } else {
                jobExecutionDefinitionData = jobManagementService.updateJobExecutionStatus(jobExecutionDefinitionData,
                        JobExecutionDefinitionStatus.FINISHED, props.getEnvType());
//                fireBaseService.sendNotification(props.getEnvType(),);
            }
            return jobExecutionDefinitionData;
        }
    }
}
