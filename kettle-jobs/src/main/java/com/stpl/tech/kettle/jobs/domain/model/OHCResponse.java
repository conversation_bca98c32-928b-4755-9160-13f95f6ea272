package com.stpl.tech.kettle.jobs.domain.model;

import java.util.ArrayList;
import java.util.List;

public class OHCResponse {
	String tid;
	String requested_id;
	List<Object> required_saledts = new ArrayList<>();
	String requested_on;
	String status;
	String flag;

	@Override
	public String toString() {
		return "OHCResponse [tid=" + tid + ", requested_id=" + requested_id + ", required_saledts=" + required_saledts
				+ ", requested_on=" + requested_on + ", status=" + status + ", flag=" + flag + "]";
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getRequested_id() {
		return requested_id;
	}

	public void setRequested_id(String requested_id) {
		this.requested_id = requested_id;
	}

	public List<Object> getRequired_saledts() {
		return required_saledts;
	}

	public void setRequired_saledts(List<Object> required_saledts) {
		this.required_saledts = required_saledts;
	}

	public String getRequested_on() {
		return requested_on;
	}

	public void setRequested_on(String requested_on) {
		this.requested_on = requested_on;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

}
