<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>UNIT_INTRA_DAY_SALES_DATA_INSERT</name>
    <description />
    <extended_description />
    <trans_version />
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>BUSINESS_DATE</name>
        <default_value>2021-01-19 17:00</default_value>
        <description />
      </parameter>
      <parameter>
        <name>HOSTNAME</name>
        <default_value>dump.kettle.chaayos.com</default_value>
        <description />
      </parameter>
      <parameter>
        <name>PASSWORD</name>
        <default_value>321In#@!</default_value>
        <description />
      </parameter>
      <parameter>
        <name>SCHEMA_SUFFIX</name>
        <default_value>_DUMP</default_value>
        <description />
      </parameter>
      <parameter>
        <name>USERNAME</name>
        <default_value>rptusr</default_value>
        <description />
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection />
        <schema />
        <table />
        <size_limit_lines />
        <interval />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject />
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject />
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject />
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject />
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject />
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject />
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection />
        <schema />
        <table />
        <interval />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection />
      <table />
      <field />
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file />
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2022/01/19 17:14:45.248</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/01/19 17:14:45.248</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>KETTLE</name>
    <server>${HOSTNAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername />
    <data_tablespace />
    <index_tablespace />
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>KETTLE_WAREHOUSE</name>
    <server>${HOSTNAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE_WAREHOUSE${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername />
    <data_tablespace />
    <index_tablespace />
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>Table input</from>
      <to>Table output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Table input</name>
    <type>TableInput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <connection>KETTLE</connection>
    <sql>SELECT 
    'ACTIVE' RECORD_STATUS,
    X.RECORD_TIME RECORD_TIME,
    X.BUSINESS_DATE BUSINESS_DATE,
    YEAR(X.BUSINESS_DATE) YEAR_VALUE,
    MONTH(X.BUSINESS_DATE) MONTH_VALUE,
    DAY(X.BUSINESS_DATE) DAY_OF_MONTH,
    HOUR(X.BUSINESS_DATE) HOUR_OF_DAY,
    DAYNAME(X.BUSINESS_DATE) DAY_OF_WEEK,
    DAYOFWEEK(X.BUSINESS_DATE) DAY_OF_WEEK_NUMBER,
    CASE
        WHEN DAYOFWEEK(X.BUSINESS_DATE) IN (2 , 3, 4, 5) THEN 'WEEK_DAY'
        WHEN DAYOFWEEK(X.BUSINESS_DATE) IN (1 , 7) THEN 'WEEK_END'
        WHEN DAYOFWEEK(X.BUSINESS_DATE) IN (6) THEN 'WEEK_FRIDAY'
    END ACTUAL_WEEK_DAY,
    CASE
        WHEN DAYOFWEEK(X.BUSINESS_DATE) IN (2 , 3, 4, 5, 6) THEN 'WEEK_DAY'
        WHEN DAYOFWEEK(X.BUSINESS_DATE) IN (1 , 7) THEN 'WEEK_END'
    END REPORTING_WEEK_DAY,
    WEEKOFYEAR(X.BUSINESS_DATE) WEEK_OF_YEAR,
    COALESCE(TRUNCATE(MINUTE('${BUSINESS_DATE}') / 15,
                0) + 1,
            0) HOUR_SLAB,
    CASE
        WHEN
            TRUNCATE(MINUTE('${BUSINESS_DATE}') / 15,
                0) = 0
        THEN
            'Y'
        ELSE 'N'
    END LAST_FOR_HOUR,
    CASE
        WHEN
            HOUR('${BUSINESS_DATE}') >= 6
                AND HOUR('${BUSINESS_DATE}') &lt;= 11
        THEN
            'BREAKFAST'
        WHEN
            HOUR('${BUSINESS_DATE}') >= 12
                AND HOUR('${BUSINESS_DATE}') &lt;= 14
        THEN
            'LUNCH'
        WHEN
            HOUR('${BUSINESS_DATE}') >= 15
                AND HOUR('${BUSINESS_DATE}') &lt;= 19
        THEN
            'EVENING'
        WHEN
            HOUR('${BUSINESS_DATE}') >= 20
                AND HOUR('${BUSINESS_DATE}') &lt;= 21
        THEN
            'DINNER'
        WHEN
            HOUR('${BUSINESS_DATE}') >= 22
                AND HOUR('${BUSINESS_DATE}') &lt;= 23
        THEN
            'POST_DINNER'
        WHEN
            HOUR('${BUSINESS_DATE}') >= 0
                AND HOUR('${BUSINESS_DATE}') &lt;= 5
        THEN
            'NIGHT'
    END DAY_PART,
    ud.UNIT_ID,
    ud.UNIT_NAME,
    ud.COST_CENTER,
    ud.SHORT_NAME,
    ud.UNIT_CATEGORY,
    ud.UNIT_SUB_CATEGORY,
    ud.UNIT_REGION,
    ad.CITY UNIT_CITY,
    ad.STATE UNIT_STATE,
    UCASE(ed.EMP_ID) AREA_MANAGER_ID,
    UCASE(ed.EMP_NAME) AREA_MANAGER,
    UCASE(ed1.EMP_ID) DEPUTY_AREA_MANAGER_ID,
    UCASE(ed1.EMP_NAME) DEPUTY_AREA_MANAGER,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    COALESCE((A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
            0) DINE_IN_TICKETS,
    COALESCE((A.NET_SALES - A.NET_DELIVERY_SALES), 0) DINE_IN_SALES,
    COALESCE(TRUNCATE((A.NET_SALES + A.WALLET_REDEMPTION - A.NET_DELIVERY_SALES - A.WALLET_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
                0),
            0) DINE_IN_APC,
    COALESCE(TRUNCATE((A.NET_SALES + A.WALLET_REDEMPTION - A.NET_DELIVERY_SALES - A.WALLET_AMOUNT - A.NET_APP_SALES) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS - A.NET_APP_TICKETS),
                0),
            0) CAFE_DINE_IN_APC,
    COALESCE(A.NET_DELIVERY_TICKETS, 0) DELIVERY_TICKETS,
    COALESCE(A.ZOMATO_NET_DELIVERY_TICKETS, 0) ZOMATO_DELIVERY_TICKETS,
    COALESCE(A.SWIGGY_NET_DELIVERY_TICKETS, 0) SWIGGY_DELIVERY_TICKETS,
    COALESCE(A.OTHERS_NET_DELIVERY_TICKETS, 0) OTHERS_DELIVERY_TICKETS,
    COALESCE(A.ZOMATO_CHAAYOS_NET_DELIVERY_TICKETS, 0) ZOMATO_CHAAYOS_DELIVERY_TICKETS,
    COALESCE(A.SWIGGY_CHAAYOS_NET_DELIVERY_TICKETS, 0) SWIGGY_CHAAYOS_DELIVERY_TICKETS,
    COALESCE(A.OTHERS_CHAAYOS_NET_DELIVERY_TICKETS, 0) OTHERS_CHAAYOS_DELIVERY_TICKETS,
    COALESCE(A.ZOMATO_GNT_NET_DELIVERY_TICKETS, 0) ZOMATO_GNT_DELIVERY_TICKETS,
    COALESCE(A.SWIGGY_GNT_NET_DELIVERY_TICKETS, 0) SWIGGY_GNT_DELIVERY_TICKETS,
    COALESCE(A.OTHERS_GNT_NET_DELIVERY_TICKETS, 0) OTHERS_GNT_DELIVERY_TICKETS,
    COALESCE(A.NET_DELIVERY_SALES, 0) DELIVERY_SALES,
    COALESCE(A.ZOMATO_NET_DELIVERY_SALES, 0) ZOMATO_DELIVERY_SALES,
    COALESCE(A.SWIGGY_NET_DELIVERY_SALES, 0) SWIGGY_DELIVERY_SALES,
    COALESCE(A.OTHERS_NET_DELIVERY_SALES, 0) OTHERS_DELIVERY_SALES,
    COALESCE(A.ZOMATO_CHAAYOS_NET_DELIVERY_SALES, 0) ZOMATO_CHAAYOS_DELIVERY_SALES,
    COALESCE(A.SWIGGY_CHAAYOS_NET_DELIVERY_SALES, 0) SWIGGY_CHAAYOS_DELIVERY_SALES,
    COALESCE(A.OTHERS_CHAAYOS_NET_DELIVERY_SALES, 0) OTHERS_CHAAYOS_DELIVERY_SALES,
    COALESCE(A.ZOMATO_GNT_NET_DELIVERY_SALES, 0) ZOMATO_GNT_DELIVERY_SALES,
    COALESCE(A.SWIGGY_GNT_NET_DELIVERY_SALES, 0) SWIGGY_GNT_DELIVERY_SALES,
    COALESCE(A.OTHERS_GNT_NET_DELIVERY_SALES, 0) OTHERS_GNT_DELIVERY_SALES,
    COALESCE(A.NET_DELIVERY_APC, 0) DELIVERY_APC,
    
    COALESCE(A.ZOMATO_NET_DELIVERY_APC, 0) ZOMATO_DELIVERY_APC,
    COALESCE(A.SWIGGY_NET_DELIVERY_APC, 0) SWIGGY_DELIVERY_APC,
    COALESCE(A.OTHERS_NET_DELIVERY_APC, 0) OTHERS_DELIVERY_APC,
    COALESCE(A.ZOMATO_CHAAYOS_NET_DELIVERY_APC, 0) ZOMATO_CHAAYOS_DELIVERY_APC,
    COALESCE(A.SWIGGY_CHAAYOS_NET_DELIVERY_APC, 0) SWIGGY_CHAAYOS_DELIVERY_APC,
    COALESCE(A.OTHERS_CHAAYOS_NET_DELIVERY_APC, 0) OTHERS_CHAAYOS_DELIVERY_APC,
    COALESCE(A.ZOMATO_GNT_NET_DELIVERY_APC, 0) ZOMATO_GNT_DELIVERY_APC,
    COALESCE(A.SWIGGY_GNT_NET_DELIVERY_APC, 0) SWIGGY_GNT_DELIVERY_APC,
    COALESCE(A.OTHERS_GNT_NET_DELIVERY_APC, 0) OTHERS_GNT_DELIVERY_APC,

    COALESCE(A.Chaayos_NET_DELIVERY_TICKETS, 0) CHAAYOS_DELIVERY_TICKETS,
    COALESCE(A.Chaayos_NET_DELIVERY_SALES, 0) CHAAYOS_DELIVERY_SALES,
    COALESCE(A.Chaayos_NET_DELIVERY_APC, 0) CHAAYOS_DELIVERY_APC,
    COALESCE(A.GnT_NET_DELIVERY_TICKETS, 0) GNT_DELIVERY_TICKETS,
    COALESCE(A.GnT_NET_DELIVERY_SALES, 0) GNT_DELIVERY_SALES,
    COALESCE(A.GnT_NET_DELIVERY_APC, 0) GNT_DELIVERY_APC,
    COALESCE(A.NET_APP_TICKETS, 0) APP_TICKETS,
    COALESCE(A.NET_APP_SALES, 0) APP_SALES,
    COALESCE(A.NET_APP_APC, 0) APP_APC,
    COALESCE(TRUNCATE((COALESCE(A.NET_APP_TICKETS, 0) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) * 100),
                0),
            0) APP_PENETRATION,
    CASE
        WHEN C.CAFE_ONLY_CUSTOMERS IS NULL THEN 0.000000
        ELSE C.CAFE_ONLY_CUSTOMERS
    END CAFE_ONLY_CUSTOMERS,
    COALESCE((A.TKTS_WITH_NO_CUSTOMER + C.CAFE_ONLY_CUSTOMERS),
            0) CAFE_ALL_TICKETS,
    COALESCE(TRUNCATE(C.CAFE_ONLY_CUSTOMERS / (A.TKTS_WITH_NO_CUSTOMER + C.CAFE_ONLY_CUSTOMERS) * 100,
                0),
            0) CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    COALESCE(A.WALLET_AMOUNT, 0) WALLET_AMOUNT,
    COALESCE(A.SUBSCRIPTION_TICKETS, 0) SUBSCRIPTION_TICKETS,
    COALESCE(A.WALLET_REDEMPTION, 0) WALLET_REDEMPTION,
    COALESCE(A.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
    COALESCE(A.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    COALESCE((B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)),
            0) LWSD_DINE_IN_TICKETS,
    COALESCE((B.LWSD_NET_SALES - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0)),
            0) LWSD_DINE_IN_SALES,
    COALESCE(TRUNCATE((B.LWSD_NET_SALES + B.LWSD_WALLET_REDEMPTION - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) - COALESCE(B.LWSD_WALLET_AMOUNT, 0)) / (B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)),
                0),
            0) LWSD_DINE_IN_APC,
    COALESCE(TRUNCATE((B.LWSD_NET_SALES + B.LWSD_WALLET_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_WALLET_AMOUNT - B.LWSD_NET_APP_SALES) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS - B.LWSD_NET_APP_TICKETS),
                0),
            0) LWSD_CAFE_DINE_IN_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_DELIVERY_APC,
    COALESCE(B.Chaayos_LWSD_NET_DELIVERY_TICKETS, 0) LWSD_CHAAYOS_DELIVERY_TICKETS,
    COALESCE(B.Chaayos_LWSD_NET_DELIVERY_SALES, 0) LWSD_CHAAYOS_DELIVERY_SALES,
    COALESCE(B.Chaayos_LWSD_NET_DELIVERY_APC, 0) LWSD_CHAAYOS_DELIVERY_APC,
    COALESCE(B.GnT_LWSD_NET_DELIVERY_TICKETS, 0) LWSD_GNT_DELIVERY_TICKETS,
    COALESCE(B.GnT_LWSD_NET_DELIVERY_SALES, 0) LWSD_GNT_DELIVERY_SALES,
    COALESCE(B.GnT_LWSD_NET_DELIVERY_APC, 0) LWSD_GNT_DELIVERY_APC,
    COALESCE(B.LWSD_NET_APP_TICKETS, 0) LWSD_APP_TICKETS,
    COALESCE(B.LWSD_NET_APP_SALES, 0) LWSD_APP_SALES,
    COALESCE(B.LWSD_NET_APP_APC, 0) LWSD_APP_APC,
    COALESCE(TRUNCATE((COALESCE(B.LWSD_NET_APP_TICKETS, 0) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) * 100),
                0),
            0) LWSD_APP_PENETRATION,
    COALESCE(B.LWSD_WALLET_AMOUNT, 0) LWSD_WALLET_AMOUNT,
    COALESCE(B.LWSD_WALLET_REDEMPTION, 0) LWSD_WALLET_REDEMPTION,
    COALESCE(B.LWSD_MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
    COALESCE(B.LWSD_SUBSCRIPTION_TICKETS, 0) LWSD_SUBSCRIPTION_TICKETS,
    COALESCE(B.LWSD_GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY
FROM
    KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud
        INNER JOIN
    KETTLE_MASTER${SCHEMA_SUFFIX}.ADDRESS_INFO ad ON ud.UNIT_ADDR_ID = ad.ADDRESS_ID
        INNER JOIN
    KETTLE_MASTER${SCHEMA_SUFFIX}.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
        INNER JOIN
        (SELECT  (CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END) BUSINESS_DATE, '${BUSINESS_DATE}' RECORD_TIME ) X
        INNER JOIN
    KETTLE_MASTER${SCHEMA_SUFFIX}.EMPLOYEE_DETAIL ed1 ON ud.CAFE_MANAGER = ed1.EMP_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0) -COALESCE(X.SELECT_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.WALLET_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(m.WALLET_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0) - COALESCE(X.SELECT_TICKETS, 0) ), 1), 0) NET_APC,
            m.NET_APP_TICKETS,
            m.NET_APP_SALES,
            COALESCE(m.NET_APP_APC, 0) NET_APP_APC,
            m.NET_DELIVERY_TICKETS,
            m.ZOMATO_NET_DELIVERY_TICKETS,
            m.SWIGGY_NET_DELIVERY_TICKETS,
            m.OTHERS_NET_DELIVERY_TICKETS,
            m.ZOMATO_CHAAYOS_NET_DELIVERY_TICKETS,
            m.SWIGGY_CHAAYOS_NET_DELIVERY_TICKETS,
            m.OTHERS_CHAAYOS_NET_DELIVERY_TICKETS,
            m.ZOMATO_GNT_NET_DELIVERY_TICKETS,
            m.SWIGGY_GNT_NET_DELIVERY_TICKETS,
            m.OTHERS_GNT_NET_DELIVERY_TICKETS,
            m.Chaayos_NET_DELIVERY_TICKETS,
            m.GnT_NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            m.ZOMATO_NET_DELIVERY_SALES,
            m.SWIGGY_NET_DELIVERY_SALES,
            m.OTHERS_NET_DELIVERY_SALES,
            m.ZOMATO_CHAAYOS_NET_DELIVERY_SALES,
            m.SWIGGY_CHAAYOS_NET_DELIVERY_SALES,
            m.OTHERS_CHAAYOS_NET_DELIVERY_SALES,
            m.ZOMATO_GNT_NET_DELIVERY_SALES,
            m.SWIGGY_GNT_NET_DELIVERY_SALES,
            m.OTHERS_GNT_NET_DELIVERY_SALES,
            m.Chaayos_NET_DELIVERY_SALES,
            m.GnT_NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            COALESCE(m.ZOMATO_NET_DELIVERY_APC, 0) ZOMATO_NET_DELIVERY_APC,
            COALESCE(m.SWIGGY_NET_DELIVERY_APC, 0) SWIGGY_NET_DELIVERY_APC,
            COALESCE(m.OTHERS_NET_DELIVERY_APC, 0) OTHERS_NET_DELIVERY_APC,
            COALESCE(m.ZOMATO_CHAAYOS_NET_DELIVERY_APC, 0) ZOMATO_CHAAYOS_NET_DELIVERY_APC,
            COALESCE(m.SWIGGY_CHAAYOS_NET_DELIVERY_APC, 0) SWIGGY_CHAAYOS_NET_DELIVERY_APC,
            COALESCE(m.OTHERS_CHAAYOS_NET_DELIVERY_APC, 0) OTHERS_CHAAYOS_NET_DELIVERY_APC,
            COALESCE(m.ZOMATO_GNT_NET_DELIVERY_APC, 0) ZOMATO_GNT_NET_DELIVERY_APC,
            COALESCE(m.SWIGGY_GNT_NET_DELIVERY_APC, 0) SWIGGY_GNT_NET_DELIVERY_APC,
            COALESCE(m.OTHERS_GNT_NET_DELIVERY_APC, 0) OTHERS_GNT_NET_DELIVERY_APC,
            COALESCE(m.Chaayos_NET_DELIVERY_APC, 0) Chaayos_NET_DELIVERY_APC,
            COALESCE(m.GnT_NET_DELIVERY_APC, 0) GnT_NET_DELIVERY_APC,
            m.TKTS_WITH_NO_CUSTOMER,
            COALESCE(m.WALLET_AMOUNT, 0) WALLET_AMOUNT,
            COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.WALLET_REDEMPTION, 0) WALLET_REDEMPTION,
            COALESCE(m.WALLET_TICKETS, 0) WALLET_TICKETS,
            COALESCE(X.SELECT_TICKETS, 0) SUBSCRIPTION_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT &lt;&gt; '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN 1
                ELSE 0
            END) AS WALLET_TICKETS,
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS WALLET_AMOUNT,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                THEN
                    1
                ELSE 0
            END) AS ZOMATO_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                THEN
                    1
                ELSE 0
            END) AS SWIGGY_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                THEN
                    1
                ELSE 0
            END) AS OTHERS_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    1
                ELSE 0
            END) AS ZOMATO_CHAAYOS_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    1
                ELSE 0
            END) AS SWIGGY_CHAAYOS_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    1
                ELSE 0
            END) AS OTHERS_CHAAYOS_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID = 3
                THEN
                    1
                ELSE 0
            END) AS ZOMATO_GNT_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID = 3
                THEN
                    1
                ELSE 0
            END) AS SWIGGY_GNT_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID = 3
                THEN
                    1
                ELSE 0
            END) AS OTHERS_GNT_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS ZOMATO_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS SWIGGY_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS OTHERS_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS ZOMATO_CHAAYOS_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS SWIGGY_CHAAYOS_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS OTHERS_CHAAYOS_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS ZOMATO_GNT_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS SWIGGY_GNT_NET_DELIVERY_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS OTHERS_GNT_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                THEN
                    1
                ELSE 0
            END), 0) AS ZOMATO_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    1
                ELSE 0
            END), 0) AS ZOMATO_CHAAYOS_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 6
                        AND od.BRAND_ID = 3
                THEN
                    1
                ELSE 0
            END), 0) AS ZOMATO_GNT_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                THEN
                    1
                ELSE 0
            END), 0) AS SWIGGY_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    1
                ELSE 0
            END), 0) AS SWIGGY_CHAAYOS_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID = 3
                        AND od.BRAND_ID = 3
                THEN
                    1
                ELSE 0
            END), 0) AS SWIGGY_GNT_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                THEN
                    1
                ELSE 0
            END), 0) AS OTHERS_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    1
                ELSE 0
            END), 0) AS OTHERS_CHAAYOS_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                        AND od.CHANNEL_PARTNER_ID NOT IN (3 , 6)
                        AND od.BRAND_ID = 3
                THEN
                    1
                ELSE 0
            END), 0) AS OTHERS_GNT_NET_DELIVERY_APC,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID &lt;&gt; 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS Chaayos_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS Chaayos_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID &lt;&gt; 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS Chaayos_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS GnT_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS GnT_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS GnT_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    1
                ELSE 0
            END) AS NET_APP_TICKETS,
            SUM(CASE
                WHEN
                    od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_APP_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    1
                ELSE 0
            END), 0) AS NET_APP_APC,
            (SUM(CASE
                WHEN
                    od.ORDER_SOURCE &lt;&gt; 'COD'
                        AND od.CUSTOMER_ID &lt;= 5
                        AND od.TOTAL_AMOUNT &lt;&gt; '0.00'
                THEN
                    1
                ELSE 0
            END)) AS TKTS_WITH_NO_CUSTOMER
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}'
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}') qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    
    LEFT OUTER JOIN
    (SELECT
        qa.UNIT_ID, COUNT(DISTINCT ORDER_ID) SELECT_TICKETS
    FROM
        (SELECT
        od.UNIT_ID, od.ORDER_ID
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.SUBSCRIPTION_PLAN_EVENT SPE ON SPE.ORDER_ID = od.ORDER_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}') qa
    GROUP BY qa.UNIT_ID) X ON m.UNIT_ID = X.UNIT_ID
    
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1303 , 1453, 1470, 1640, 1641, 1642, 1000001) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_ITEM oi
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}'
            AND oi.PRODUCT_ID IN (730 , 840, 850, 1051, 1057, 1129, 1154, 1157, 1158, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1230, 1237, 1283, 1289, 1291, 1303, 1304, 1305, 1306, 1354, 1355, 1356, 1357, 1362, 1363, 1369, 1370, 1371, 1372, 1373, 1374, 1399, 1450, 1453, 1456, 1457, 1458, 1459, 1470, 1484, 1485, 1557, 1558, 1559, 1560, 1564, 1634, 1644, 1645, 1646, 1000000, 1000001, 1000008, 1000009, 1000010, 1000011, 1000012, 1000013, 1000014, 1000015, 1000016, 1000017, 1000018, 1000019, 1000020, 1000021, 1000022, 1000023, 1000024, 1000030)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.WALLET_REDEMPTION, 2) WALLET_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE${SCHEMA_SUFFIX}.ORDER_SETTLEMENT os
            INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS &lt;&gt; 'CANCELLED'
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
                    ELSE DATE('${BUSINESS_DATE}')
                END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}'
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}'
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) WALLET_REDEMPTION
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_SETTLEMENT os
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), INTERVAL 5 HOUR) AND '${BUSINESS_DATE}'
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0) - COALESCE(X.SELECT_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.WALLET_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(m.WALLET_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0) - COALESCE(X.SELECT_TICKETS, 0) ), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            m.LWSD_NET_APP_TICKETS,
            m.LWSD_NET_APP_SALES,
            COALESCE(m.LWSD_NET_APP_APC, 0) LWSD_NET_APP_APC,
            m.Chaayos_LWSD_NET_DELIVERY_TICKETS,
            m.Chaayos_LWSD_NET_DELIVERY_SALES,
            COALESCE(m.Chaayos_LWSD_NET_DELIVERY_APC, 0) Chaayos_LWSD_NET_DELIVERY_APC,
            m.GnT_LWSD_NET_DELIVERY_TICKETS,
            m.GnT_LWSD_NET_DELIVERY_SALES,
            COALESCE(m.GnT_LWSD_NET_DELIVERY_APC, 0) GnT_LWSD_NET_DELIVERY_APC,
            COALESCE(m.WALLET_AMOUNT, 0) LWSD_WALLET_AMOUNT,
            COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.WALLET_REDEMPTION, 0) LWSD_WALLET_REDEMPTION,
            COALESCE(m.WALLET_TICKETS, 0) LWSD_WALLET_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION,
			COALESCE(X.SELECT_TICKETS, 0) LWSD_SUBSCRIPTION_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT &lt;&gt; '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN 1
                ELSE 0
            END) AS WALLET_TICKETS,
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS WALLET_AMOUNT,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT &lt;&gt; '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID &lt;&gt; 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS Chaayos_LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS Chaayos_LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID &lt;&gt; 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID &lt;&gt; 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS Chaayos_LWSD_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS GnT_LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS GnT_LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.BRAND_ID = 3
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS GnT_LWSD_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_APP_TICKETS,
            SUM(CASE
                WHEN
                    od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_APP_SALES,
            TRUNCATE(SUM(CASE
                WHEN
                    od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT &lt;&gt; '0.00'
                        AND od.BRAND_ID = 1
                        AND od.CHANNEL_PARTNER_ID IN (21 , 14)
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_APP_APC
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    
    
    LEFT OUTER JOIN
    (SELECT
        qa.UNIT_ID, COUNT(DISTINCT ORDER_ID) SELECT_TICKETS
    FROM
        (SELECT
        od.UNIT_ID, od.ORDER_ID
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.SUBSCRIPTION_PLAN_EVENT SPE ON SPE.ORDER_ID = od.ORDER_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_ID) X ON m.UNIT_ID = X.UNIT_ID
    
    
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1303 , 1453, 1470, 1640, 1641, 1642, 1000001) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_ITEM oi
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (730 , 840, 850, 1051, 1057, 1129, 1154, 1157, 1158, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1230, 1237, 1283, 1289, 1291, 1303, 1304, 1305, 1306, 1354, 1355, 1356, 1357, 1362, 1363, 1369, 1370, 1371, 1372, 1373, 1374, 1399, 1450, 1453, 1456, 1457, 1458, 1459, 1470, 1484, 1485, 1557, 1558, 1559, 1560, 1564, 1634, 1644, 1645, 1646, 1000000, 1000001, 1000008, 1000009, 1000010, 1000011, 1000012, 1000013, 1000014, 1000015, 1000016, 1000017, 1000018, 1000019, 1000020, 1000021, 1000022, 1000023, 1000024, 1000030)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.WALLET_REDEMPTION, 2) WALLET_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE${SCHEMA_SUFFIX}.ORDER_SETTLEMENT os
            INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS &lt;&gt; 'CANCELLED'
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
                    ELSE DATE('${BUSINESS_DATE}')
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) WALLET_REDEMPTION
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_SETTLEMENT os
    INNER JOIN KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        DATE_FORMAT(DATE('${BUSINESS_DATE}'), '%d-%m-%Y') BIZ_DATE,
            od.UNIT_ID,
            COUNT(DISTINCT CUSTOMER_ID) CAFE_ONLY_CUSTOMERS
    FROM
        KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND od.ORDER_SOURCE &lt;&gt; 'COD'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR('${BUSINESS_DATE}') &lt;= 5 THEN SUBDATE(DATE('${BUSINESS_DATE}'), 1)
            ELSE DATE('${BUSINESS_DATE}')
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL - 7 DAY)
        
    GROUP BY od.UNIT_ID) C ON A.UNIT_ID = C.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY AREA_MANAGER , ud.UNIT_NAME;</sql>
    <limit>0</limit>
    <lookup />
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema />
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>192</xloc>
      <yloc>96</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Table output</name>
    <type>TableOutput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <connection>KETTLE_WAREHOUSE</connection>
    <schema>KETTLE_WAREHOUSE${SCHEMA_SUFFIX}</schema>
    <table>UNIT_INTRA_DAY_SALES_DATA</table>
    <commit>10000</commit>
    <truncate>N</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>Y</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field />
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field />
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field />
    <fields>
      <field>
        <column_name>RECORD_STATUS</column_name>
        <stream_name>RECORD_STATUS</stream_name>
      </field>
      <field>
        <column_name>RECORD_TIME</column_name>
        <stream_name>RECORD_TIME</stream_name>
      </field>
      <field>
        <column_name>BUSINESS_DATE</column_name>
        <stream_name>BUSINESS_DATE</stream_name>
      </field>
      <field>
        <column_name>YEAR_VALUE</column_name>
        <stream_name>YEAR_VALUE</stream_name>
      </field>
      <field>
        <column_name>MONTH_VALUE</column_name>
        <stream_name>MONTH_VALUE</stream_name>
      </field>
      <field>
        <column_name>DAY_OF_MONTH</column_name>
        <stream_name>DAY_OF_MONTH</stream_name>
      </field>
      <field>
        <column_name>HOUR_OF_DAY</column_name>
        <stream_name>HOUR_OF_DAY</stream_name>
      </field>
      <field>
        <column_name>DAY_OF_WEEK</column_name>
        <stream_name>DAY_OF_WEEK</stream_name>
      </field>
      <field>
        <column_name>DAY_OF_WEEK_NUMBER</column_name>
        <stream_name>DAY_OF_WEEK_NUMBER</stream_name>
      </field>
      <field>
        <column_name>ACTUAL_WEEK_DAY</column_name>
        <stream_name>ACTUAL_WEEK_DAY</stream_name>
      </field>
      <field>
        <column_name>REPORTING_WEEK_DAY</column_name>
        <stream_name>REPORTING_WEEK_DAY</stream_name>
      </field>
      <field>
        <column_name>WEEK_OF_YEAR</column_name>
        <stream_name>WEEK_OF_YEAR</stream_name>
      </field>
      <field>
        <column_name>HOUR_SLAB</column_name>
        <stream_name>HOUR_SLAB</stream_name>
      </field>
      <field>
        <column_name>LAST_FOR_HOUR</column_name>
        <stream_name>LAST_FOR_HOUR</stream_name>
      </field>
      <field>
        <column_name>DAY_PART</column_name>
        <stream_name>DAY_PART</stream_name>
      </field>
      <field>
        <column_name>UNIT_ID</column_name>
        <stream_name>UNIT_ID</stream_name>
      </field>
      <field>
        <column_name>UNIT_NAME</column_name>
        <stream_name>UNIT_NAME</stream_name>
      </field>
      <field>
        <column_name>COST_CENTER</column_name>
        <stream_name>COST_CENTER</stream_name>
      </field>
      <field>
        <column_name>SHORT_NAME</column_name>
        <stream_name>SHORT_NAME</stream_name>
      </field>
      <field>
        <column_name>UNIT_CATEGORY</column_name>
        <stream_name>UNIT_CATEGORY</stream_name>
      </field>
      <field>
        <column_name>UNIT_SUB_CATEGORY</column_name>
        <stream_name>UNIT_SUB_CATEGORY</stream_name>
      </field>
      <field>
        <column_name>UNIT_REGION</column_name>
        <stream_name>UNIT_REGION</stream_name>
      </field>
      <field>
        <column_name>UNIT_CITY</column_name>
        <stream_name>UNIT_CITY</stream_name>
      </field>
      <field>
        <column_name>UNIT_STATE</column_name>
        <stream_name>UNIT_STATE</stream_name>
      </field>
      <field>
        <column_name>AREA_MANAGER_ID</column_name>
        <stream_name>AREA_MANAGER_ID</stream_name>
      </field>
      <field>
        <column_name>AREA_MANAGER</column_name>
        <stream_name>AREA_MANAGER</stream_name>
      </field>
      <field>
        <column_name>DEPUTY_AREA_MANAGER_ID</column_name>
        <stream_name>DEPUTY_AREA_MANAGER_ID</stream_name>
      </field>
      <field>
        <column_name>DEPUTY_AREA_MANAGER</column_name>
        <stream_name>DEPUTY_AREA_MANAGER</stream_name>
      </field>
      <field>
        <column_name>NET_TICKETS</column_name>
        <stream_name>NET_TICKETS</stream_name>
      </field>
      <field>
        <column_name>NET_SALES</column_name>
        <stream_name>NET_SALES</stream_name>
      </field>
      <field>
        <column_name>NET_APC</column_name>
        <stream_name>NET_APC</stream_name>
      </field>
      <field>
        <column_name>DINE_IN_TICKETS</column_name>
        <stream_name>DINE_IN_TICKETS</stream_name>
      </field>
      <field>
        <column_name>DINE_IN_SALES</column_name>
        <stream_name>DINE_IN_SALES</stream_name>
      </field>
      <field>
        <column_name>DINE_IN_APC</column_name>
        <stream_name>DINE_IN_APC</stream_name>
      </field>
      <field>
        <column_name>CAFE_DINE_IN_APC</column_name>
        <stream_name>CAFE_DINE_IN_APC</stream_name>
      </field>
      <field>
        <column_name>DELIVERY_TICKETS</column_name>
        <stream_name>DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_DELIVERY_TICKETS</column_name>
        <stream_name>ZOMATO_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_DELIVERY_TICKETS</column_name>
        <stream_name>SWIGGY_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>OTHERS_DELIVERY_TICKETS</column_name>
        <stream_name>OTHERS_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_CHAAYOS_DELIVERY_TICKETS</column_name>
        <stream_name>ZOMATO_CHAAYOS_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_CHAAYOS_DELIVERY_TICKETS</column_name>
        <stream_name>SWIGGY_CHAAYOS_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>OTHERS_CHAAYOS_DELIVERY_TICKETS</column_name>
        <stream_name>OTHERS_CHAAYOS_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_GNT_DELIVERY_TICKETS</column_name>
        <stream_name>ZOMATO_GNT_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_GNT_DELIVERY_TICKETS</column_name>
        <stream_name>SWIGGY_GNT_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>OTHERS_GNT_DELIVERY_TICKETS</column_name>
        <stream_name>OTHERS_GNT_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>DELIVERY_SALES</column_name>
        <stream_name>DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_DELIVERY_SALES</column_name>
        <stream_name>ZOMATO_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_DELIVERY_SALES</column_name>
        <stream_name>SWIGGY_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>OTHERS_DELIVERY_SALES</column_name>
        <stream_name>OTHERS_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_CHAAYOS_DELIVERY_SALES</column_name>
        <stream_name>ZOMATO_CHAAYOS_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_CHAAYOS_DELIVERY_SALES</column_name>
        <stream_name>SWIGGY_CHAAYOS_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>OTHERS_CHAAYOS_DELIVERY_SALES</column_name>
        <stream_name>OTHERS_CHAAYOS_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_GNT_DELIVERY_SALES</column_name>
        <stream_name>ZOMATO_GNT_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_GNT_DELIVERY_SALES</column_name>
        <stream_name>SWIGGY_GNT_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>OTHERS_GNT_DELIVERY_SALES</column_name>
        <stream_name>OTHERS_GNT_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>DELIVERY_APC</column_name>
        <stream_name>DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_DELIVERY_APC</column_name>
        <stream_name>ZOMATO_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_DELIVERY_APC</column_name>
        <stream_name>SWIGGY_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>OTHERS_DELIVERY_APC</column_name>
        <stream_name>OTHERS_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_CHAAYOS_DELIVERY_APC</column_name>
        <stream_name>ZOMATO_CHAAYOS_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_CHAAYOS_DELIVERY_APC</column_name>
        <stream_name>SWIGGY_CHAAYOS_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>OTHERS_CHAAYOS_DELIVERY_APC</column_name>
        <stream_name>OTHERS_CHAAYOS_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>ZOMATO_GNT_DELIVERY_APC</column_name>
        <stream_name>ZOMATO_GNT_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>SWIGGY_GNT_DELIVERY_APC</column_name>
        <stream_name>SWIGGY_GNT_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>OTHERS_GNT_DELIVERY_APC</column_name>
        <stream_name>OTHERS_GNT_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>CHAAYOS_DELIVERY_TICKETS</column_name>
        <stream_name>CHAAYOS_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>CHAAYOS_DELIVERY_SALES</column_name>
        <stream_name>CHAAYOS_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>CHAAYOS_DELIVERY_APC</column_name>
        <stream_name>CHAAYOS_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>GNT_DELIVERY_TICKETS</column_name>
        <stream_name>GNT_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>GNT_DELIVERY_SALES</column_name>
        <stream_name>GNT_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>GNT_DELIVERY_APC</column_name>
        <stream_name>GNT_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>APP_TICKETS</column_name>
        <stream_name>APP_TICKETS</stream_name>
      </field>
      <field>
        <column_name>APP_SALES</column_name>
        <stream_name>APP_SALES</stream_name>
      </field>
      <field>
        <column_name>APP_APC</column_name>
        <stream_name>APP_APC</stream_name>
      </field>
      <field>
        <column_name>APP_PENETRATION</column_name>
        <stream_name>APP_PENETRATION</stream_name>
      </field>
      <field>
        <column_name>CAFE_ONLY_CUSTOMERS</column_name>
        <stream_name>CAFE_ONLY_CUSTOMERS</stream_name>
      </field>
      <field>
        <column_name>CAFE_ALL_TICKETS</column_name>
        <stream_name>CAFE_ALL_TICKETS</stream_name>
      </field>
      <field>
        <column_name>CUSTOMER_CAPTURE_PERCENT_OF_TKTS</column_name>
        <stream_name>CUSTOMER_CAPTURE_PERCENT_OF_TKTS</stream_name>
      </field>
      <field>
        <column_name>WALLET_AMOUNT</column_name>
        <stream_name>WALLET_AMOUNT</stream_name>
      </field>
      <field>
        <column_name>SUBSCRIPTION_TICKETS</column_name>
        <stream_name>SUBSCRIPTION_TICKETS</stream_name>
      </field>
      <field>
        <column_name>WALLET_REDEMPTION</column_name>
        <stream_name>WALLET_REDEMPTION</stream_name>
      </field>
      <field>
        <column_name>MERCHANDISE_SALES</column_name>
        <stream_name>MERCHANDISE_SALES</stream_name>
      </field>
      <field>
        <column_name>GIFT_BOX_QUANTITY</column_name>
        <stream_name>GIFT_BOX_QUANTITY</stream_name>
      </field>
      <field>
        <column_name>LWSD_NET_TICKETS</column_name>
        <stream_name>LWSD_NET_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_NET_SALES</column_name>
        <stream_name>LWSD_NET_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_NET_APC</column_name>
        <stream_name>LWSD_NET_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_DINE_IN_TICKETS</column_name>
        <stream_name>LWSD_DINE_IN_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_DINE_IN_SALES</column_name>
        <stream_name>LWSD_DINE_IN_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_DINE_IN_APC</column_name>
        <stream_name>LWSD_DINE_IN_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_CAFE_DINE_IN_APC</column_name>
        <stream_name>LWSD_CAFE_DINE_IN_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_DELIVERY_TICKETS</column_name>
        <stream_name>LWSD_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_DELIVERY_SALES</column_name>
        <stream_name>LWSD_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_DELIVERY_APC</column_name>
        <stream_name>LWSD_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_CHAAYOS_DELIVERY_TICKETS</column_name>
        <stream_name>LWSD_CHAAYOS_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_CHAAYOS_DELIVERY_SALES</column_name>
        <stream_name>LWSD_CHAAYOS_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_CHAAYOS_DELIVERY_APC</column_name>
        <stream_name>LWSD_CHAAYOS_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_GNT_DELIVERY_TICKETS</column_name>
        <stream_name>LWSD_GNT_DELIVERY_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_GNT_DELIVERY_SALES</column_name>
        <stream_name>LWSD_GNT_DELIVERY_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_GNT_DELIVERY_APC</column_name>
        <stream_name>LWSD_GNT_DELIVERY_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_APP_TICKETS</column_name>
        <stream_name>LWSD_APP_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_APP_SALES</column_name>
        <stream_name>LWSD_APP_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_APP_APC</column_name>
        <stream_name>LWSD_APP_APC</stream_name>
      </field>
      <field>
        <column_name>LWSD_APP_PENETRATION</column_name>
        <stream_name>LWSD_APP_PENETRATION</stream_name>
      </field>
      <field>
        <column_name>LWSD_WALLET_AMOUNT</column_name>
        <stream_name>LWSD_WALLET_AMOUNT</stream_name>
      </field>
      <field>
        <column_name>LWSD_WALLET_REDEMPTION</column_name>
        <stream_name>LWSD_WALLET_REDEMPTION</stream_name>
      </field>
      <field>
        <column_name>LWSD_MERCHANDISE_SALES</column_name>
        <stream_name>LWSD_MERCHANDISE_SALES</stream_name>
      </field>
      <field>
        <column_name>LWSD_SUBSCRIPTION_TICKETS</column_name>
        <stream_name>LWSD_SUBSCRIPTION_TICKETS</stream_name>
      </field>
      <field>
        <column_name>LWSD_GIFT_BOX_QUANTITY</column_name>
        <stream_name>LWSD_GIFT_BOX_QUANTITY</stream_name>
      </field>
    </fields>
    <cluster_schema />
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>96</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
