<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>sales</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>BUSINESS_DATE</name>
        <default_value>2022-11-09</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>HOSTNAME</name>
        <default_value>dump.kettle.chaayos.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>PASSWORD</name>
        <default_value>321In#@!</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>SCHEMA_SUFFIX</name>
        <default_value>_DUMP</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>USERNAME</name>
        <default_value>rptusr</default_value>
        <description/>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2021/10/18 15:12:57.945</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/10/18 15:12:57.945</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>Input_connection</name>
    <server>${HOSTNAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>Input</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Input</name>
    <type>TableInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>Input_connection</connection>
    <sql>SELECT
    DATE_FORMAT('${BUSINESS_DATE}',"%Y-%m-%d") BUSINESS_DATE,
    a.SKU_ID,
    a.ORDER_SOURCE,
 	a.ORDER_TYPE ORDER_TYPE,
    a.BRAND,
    a.CAFE_ID,
    SUM(a.QUANTITY) QUANTITY,
    SUM(a.REVENUE) REVENUE,
    SUM(a.GMV) GMV,
    a.MRP
FROM(
	SELECT
		od.BUSINESS_DATE BUSINESS_DATE,
		concat(pd.PRODUCT_ID,'_',oi.DIMENSION) SKU_ID,
		CASE WHEN od.ORDER_SOURCE='COD' THEN 'DELIVERY' ELSE 'DINE_IN' END ORDER_SOURCE,
		od.ORDER_TYPE ORDER_TYPE,
		bd.BRAND_NAME BRAND,
		ud.COST_CENTER CAFE_ID,
		SUM(oi.QUANTITY) QUANTITY,
		SUM(oi.AMOUNT_PAID) REVENUE,
		SUM(oi.PRICE * oi.QUANTITY) GMV,
		AVG(oi.PRICE) MRP
	FROM
		KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od,
		KETTLE${SCHEMA_SUFFIX}.ORDER_ITEM oi,
		KETTLE_MASTER${SCHEMA_SUFFIX}.BRAND_DETAIL bd,
		KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud,
		KETTLE_MASTER${SCHEMA_SUFFIX}.PRODUCT_DETAIL pd
	WHERE
        oi.ORDER_ID = od.ORDER_ID
		AND oi.PRODUCT_ID = pd.PRODUCT_ID
		AND od.UNIT_ID = ud.UNIT_ID
		AND od.BUSINESS_DATE = '${BUSINESS_DATE}'
		AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
		AND od.ORDER_TYPE IN ('order','employee-meal','complimentary-order','unsatisfied-customer-order','paid-employee-meal')
    AND od.TAXABLE_AMOUNT &lt;= 3000
		AND od.BUSINESS_DATE IS NOT NULL
		AND IS_GIFT_CARD_ORDER &lt;&gt; 'Y'
		AND oi.TAX_CODE NOT IN( 'GIFT_CARD', 'COMBO')
		AND od.BRAND_ID = bd.BRAND_ID
		AND oi.PRICE > 0
		AND oi.PRODUCT_ID NOT IN (1043,1463,1291,1044)
	group by BUSINESS_DATE, SKU_ID, ORDER_SOURCE, ORDER_TYPE, BRAND, CAFE_ID
	)a
group by a.BUSINESS_DATE, a.SKU_ID, a.ORDER_SOURCE, a.ORDER_TYPE, a.BRAND, a.CAFE_ID;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>208</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Output</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>,</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>N</create_parent_folder>
    <file>
      <name>${Internal.Entry.Current.Directory}/files/sales_${BUSINESS_DATE}</name>
      <is_command>N</is_command>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>N</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format>yyyy-MM-dd</date_time_format>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery>0</splitevery>
    </file>
    <fields>
      <field>
        <name>BUSINESS_DATE</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>SKU_ID</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>ORDER_SOURCE</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>ORDER_TYPE</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>BRAND</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>CAFE_ID</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>QUANTITY</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>REVENUE</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>GMV</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>MRP</name>
        <type>Number</type>
        <format>0.#####</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>208</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
