<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>goods_service</name>
    <description />
    <extended_description />
    <trans_version />
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>BUSINESS_DATE</name>
        <default_value>2021-07-14</default_value>
        <description />
      </parameter>
      <parameter>
        <name>HOST_NAME</name>
        <default_value>dump.kettle.chaayos.com</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_EXECUTION_ID</name>
        <default_value>1022</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_EXECUTION_TIME</name>
        <default_value>2019-03-30 15:00:01</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_ID</name>
        <default_value>3</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_STEP_ID</name>
        <default_value>3</default_value>
        <description />
      </parameter>
      <parameter>
        <name>PASSWORD</name>
        <default_value>321In#@!</default_value>
        <description />
      </parameter>
      <parameter>
        <name>SCHEMA_SUFFIX</name>
        <default_value />
        <description />
      </parameter>
      <parameter>
        <name>TYPE_OF_DATA</name>
        <default_value>TAX_DATA</default_value>
        <description />
      </parameter>
      <parameter>
        <name>USERNAME</name>
        <default_value>rptusr</default_value>
        <description />
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection />
        <schema />
        <table />
        <size_limit_lines />
        <interval />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject />
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject />
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject />
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject />
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject />
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject />
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection />
        <schema />
        <table />
        <interval />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection />
      <table />
      <field />
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file />
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2020/04/04 21:15:50.449</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/04/04 21:15:50.449</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>con</name>
    <server>${HOST_NAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE_MASTER${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername />
    <data_tablespace />
    <index_tablespace />
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>OUTPUT_CONNECTION</name>
    <server>${HOST_NAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE_JOBS${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername />
    <data_tablespace />
    <index_tablespace />
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>Table input</from>
      <to>Table output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Table input</name>
    <type>TableInput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <connection>con</connection>
    <sql>SELECT 
    DATE_FORMAT('${BUSINESS_DATE}', '%d-%m-%Y') BUSINESS_DATE,
    z.UNIT_ID UNIT_ID,
    z.UNIT_NAME UNIT_NAME,
    z.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(PAYMENT_REQUEST_ID, '_', INVOICE_NUMBER) REFERENCE_NUMBER,
    CONCAT('JV/PUR/',
            z.UNIT_ID,
            '/',
            DATE_FORMAT('${BUSINESS_DATE}', '%Y/%m'),
            '/',
            z.PAYMENT_REQUEST_ID) TRANSACTION_NUMBER,
    z.STATE STATE,
    z.STATE_LONG_CODE STATE_CODE,
    'VENDOR GR BOOKING' NARRATION,
    CONCAT('PURCHASE_',
            CATEGORY_CODE,
            '_',
            SUB_CATEGORY_CODE) PARTICULARS,
    SUM(TOTAL_AMOUNT) TOTAL_SALES,
    'YES' IS_DEBIT,
    CONCAT('PURCHASE_',
            CATEGORY_CODE,
            '_',
            SUB_CATEGORY_CODE) ACCOUNT_CODE,
    'VENDOR_GR_DATA' KEY_TYPE,
    PAYMENT_REQUEST_ID KEY_ID,
    '${JOB_ID}' JOB_ID,
    '${JOB_EXECUTION_ID}' JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    '${JOB_STEP_ID}' STEP_ID
FROM
    (SELECT 
        cd.CATEGORY_CODE,
            scd.SUB_CATEGORY_CODE,
            i.TOTAL_PRICE TOTAL_AMOUNT,
            gr.EXTRA_CHARGES,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            ud.COST_CENTER,
            gr.PAYMENT_REQUEST_ID,
            pr.INVOICE_NUMBER,
            st.STATE,
            st.STATE_LONG_CODE 
    FROM
        KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GOODS_RECEIVED_DATA gr
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON gr.DELIVERY_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL st ON ld.STATE_DETAIL_ID = st.STATE_DETAIL_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = gr.PAYMENT_REQUEST_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST_STATUS_LOG prs ON prs.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
        AND prs.TO_STATUS = 'APPROVED'
    WHERE
        prs.UPDATE_TIME BETWEEN DATE_ADD('${BUSINESS_DATE}', INTERVAL 295 MINUTE) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL 1735 MINUTE)
		AND ud.CONSIDERED_FOR_ACCOUNTING='Y' AND v.VENDOR_ID&lt;&gt;1926) z
GROUP BY ACCOUNT_CODE , PAYMENT_REQUEST_ID
UNION ALL
SELECT 
    DATE_FORMAT('${BUSINESS_DATE}', '%d-%m-%Y') BUSINESS_DATE,
    z.UNIT_ID UNIT_ID,
    z.UNIT_NAME UNIT_NAME,
    z.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(PAYMENT_REQUEST_ID, '_', INVOICE_NUMBER) REFERENCE_NUMBER,
    CONCAT('JV/PUR/',
            z.UNIT_ID,
            '/',
            DATE_FORMAT('${BUSINESS_DATE}', '%Y/%m'),
            '/',
            z.PAYMENT_REQUEST_ID) TRANSACTION_NUMBER,
    z.STATE STATE,
    z.STATE_LONG_CODE STATE_CODE,
    'VENDOR GR BOOKING' NARRATION,
    case when CATEGORY_ID = 3 THEN  CONCAT('PURCHASE_',
            CATEGORY_CODE,
            '_',
            SUB_CATEGORY_CODE) ELSE 
    'FREIGHT_INWARD' END PARTICULARS,
    SUM(TOTAL_AMOUNT) TOTAL_SALES,
    'YES' IS_DEBIT,
    case when CATEGORY_ID = 3 THEN  CONCAT('PURCHASE_',
            CATEGORY_CODE,
            '_',
            SUB_CATEGORY_CODE) ELSE 
    'FREIGHT_INWARD' END ACCOUNT_CODE,
    'VENDOR_GR_DATA' KEY_TYPE,
    PAYMENT_REQUEST_ID KEY_ID,
    '${JOB_ID}' JOB_ID,
    '${JOB_EXECUTION_ID}' JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    '${JOB_STEP_ID}' STEP_ID
FROM
    (SELECT 
            MAX(cd.CATEGORY_ID) CATEGORY_ID,
            MAX(cd.CATEGORY_CODE) CATEGORY_CODE,
            MAX(scd.SUB_CATEGORY_CODE) SUB_CATEGORY_CODE,
            MAX(gr.EXTRA_CHARGES) TOTAL_AMOUNT,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            ud.COST_CENTER,
            gr.PAYMENT_REQUEST_ID,
            pr.INVOICE_NUMBER,
            st.STATE,
            st.STATE_LONG_CODE
    FROM
        KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GOODS_RECEIVED_DATA gr
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON gr.DELIVERY_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL st ON ld.STATE_DETAIL_ID = st.STATE_DETAIL_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = gr.PAYMENT_REQUEST_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST_STATUS_LOG prs ON prs.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
        AND prs.TO_STATUS = 'APPROVED'
    WHERE
        prs.UPDATE_TIME BETWEEN DATE_ADD('${BUSINESS_DATE}', INTERVAL 295 MINUTE) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL 1735 MINUTE)
        and gr.EXTRA_CHARGES IS NOT NULL and gr.EXTRA_CHARGES > '0.0' AND ud.CONSIDERED_FOR_ACCOUNTING='Y' AND v.VENDOR_ID&lt;&gt;1926
        group by gr.GOODS_RECEIVED_ID) z
GROUP BY ACCOUNT_CODE , PAYMENT_REQUEST_ID
UNION ALL
SELECT 
    DATE_FORMAT('${BUSINESS_DATE}', '%d-%m-%Y') BUSINESS_DATE,
    z.UNIT_ID UNIT_ID,
    z.UNIT_NAME UNIT_NAME,
    z.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(PAYMENT_REQUEST_ID, '_', INVOICE_NUMBER) REFERENCE_NUMBER,
    CONCAT('JV/PUR/',
            z.UNIT_ID,
            '/',
            DATE_FORMAT('${BUSINESS_DATE}', '%Y/%m'),
            '/',
            z.PAYMENT_REQUEST_ID) TRANSACTION_NUMBER,
    z.STATE STATE,
    z.STATE_LONG_CODE STATE_LONG_CODE,
    'VENDOR GR BOOKING' NARRATION,
    CONCAT(TAX_TYPE, '_',STATE_LONG_CODE,'_INPUT') PARTICULARS,
    SUM(TOTAL_AMOUNT) TOTAL_SALES,
    'YES' IS_DEBIT,
    CONCAT(TAX_TYPE, '_',STATE_LONG_CODE,'_INPUT') ACCOUNT_CODE,
    'VENDOR_GR_DATA' KEY_TYPE,
    PAYMENT_REQUEST_ID KEY_ID,
    '${JOB_ID}' JOB_ID,
    '${JOB_EXECUTION_ID}' JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    '${JOB_STEP_ID}' STEP_ID
FROM
    (SELECT 
            gri.TAX_TYPE,
            gri.TAX_VALUE TOTAL_AMOUNT,
            gr.EXTRA_CHARGES,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            ud.COST_CENTER,
            gr.PAYMENT_REQUEST_ID,
            pr.INVOICE_NUMBER,
            st.STATE,
            st.STATE_LONG_CODE
    FROM
        KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GOODS_RECEIVED_DATA gr
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.ITEM_TAX_DETAIL_DATA gri on i.GOODS_RECEIVED_ITEM_ID = gri.GR_ITEM_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON gr.DELIVERY_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL st ON ld.STATE_DETAIL_ID = st.STATE_DETAIL_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = gr.PAYMENT_REQUEST_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST_STATUS_LOG prs ON prs.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
        AND prs.TO_STATUS = 'APPROVED'
   WHERE
        prs.UPDATE_TIME BETWEEN DATE_ADD('${BUSINESS_DATE}', INTERVAL 295 MINUTE) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL 1735 MINUTE)
        AND gri.TAX_VALUE IS NOT NULL AND gri.TAX_VALUE > '0' AND ud.CONSIDERED_FOR_ACCOUNTING='Y' AND v.VENDOR_ID&lt;&gt;1926) z
GROUP BY ACCOUNT_CODE , PAYMENT_REQUEST_ID
UNION ALL
SELECT 
    DATE_FORMAT('${BUSINESS_DATE}', '%d-%m-%Y') BUSINESS_DATE,
    z.UNIT_ID UNIT_ID,
    z.UNIT_NAME UNIT_NAME,
    z.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(PAYMENT_REQUEST_ID, '_', INVOICE_NUMBER) REFERENCE_NUMBER,
    CONCAT('JV/PUR/',
            z.UNIT_ID,
            '/',
            DATE_FORMAT('${BUSINESS_DATE}', '%Y/%m'),
            '/',
            z.PAYMENT_REQUEST_ID) TRANSACTION_NUMBER,
    z.STATE STATE,
    z.STATE_LONG_CODE STATE_LONG_CODE,
    'VENDOR GR BOOKING' NARRATION,
    CONCAT('PURCHASE_',
            CATEGORY_CODE,
            '_',
            SUB_CATEGORY_CODE) PARTICULARS,
    SUM(TOTAL_AMOUNT) TOTAL_SALES,
    'YES' IS_DEBIT,
    CONCAT('PURCHASE_',
            CATEGORY_CODE,
            '_',
            SUB_CATEGORY_CODE) ACCOUNT_CODE,
    'VENDOR_GR_DATA' KEY_TYPE,
    PAYMENT_REQUEST_ID KEY_ID,
    '${JOB_ID}' JOB_ID,
    '${JOB_EXECUTION_ID}' JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    '${JOB_STEP_ID}' STEP_ID
FROM
    (SELECT 
        
            cd.CATEGORY_CODE,
            scd.SUB_CATEGORY_CODE,
            gri.TAX_TYPE,
            gri.TAX_VALUE TOTAL_AMOUNT,
            gr.EXTRA_CHARGES,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            ud.COST_CENTER,
            gr.PAYMENT_REQUEST_ID,
            pr.INVOICE_NUMBER,
            st.STATE,
            st.STATE_LONG_CODE
    FROM
        KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GOODS_RECEIVED_DATA gr
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.ITEM_TAX_DETAIL_DATA gri on i.GOODS_RECEIVED_ITEM_ID = gri.GR_ITEM_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    AND cd.CATEGORY_ID IN (3) 
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON gr.DELIVERY_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL st ON ld.STATE_DETAIL_ID = st.STATE_DETAIL_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = gr.PAYMENT_REQUEST_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST_STATUS_LOG prs ON prs.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
        AND prs.TO_STATUS = 'APPROVED'
   WHERE
        prs.UPDATE_TIME BETWEEN DATE_ADD('${BUSINESS_DATE}', INTERVAL 295 MINUTE) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL 1735 MINUTE)
        AND gri.TAX_VALUE IS NOT NULL AND gri.TAX_VALUE > '0' AND ud.CONSIDERED_FOR_ACCOUNTING='Y' AND v.VENDOR_ID&lt;&gt;1926) z
GROUP BY ACCOUNT_CODE , PAYMENT_REQUEST_ID
UNION ALL
SELECT 
    DATE_FORMAT('${BUSINESS_DATE}', '%d-%m-%Y') BUSINESS_DATE,
    z.UNIT_ID UNIT_ID,
    z.UNIT_NAME UNIT_NAME,
    z.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(PAYMENT_REQUEST_ID, '_', INVOICE_NUMBER) REFERENCE_NUMBER,
    CONCAT('JV/PUR/',
            z.UNIT_ID,
            '/',
            DATE_FORMAT('${BUSINESS_DATE}', '%Y/%m'),
            '/',
            z.PAYMENT_REQUEST_ID) TRANSACTION_NUMBER,
    z.STATE STATE,
    z.STATE_LONG_CODE STATE_LONG_CODE,
    'VENDOR GR BOOKING' NARRATION,
    'GST_INPUT_FA_COMMON' PARTICULARS,
    SUM(TOTAL_AMOUNT) TOTAL_SALES,
    'NO' IS_DEBIT,
    'GST_INPUT_FA_COMMON' ACCOUNT_CODE,
    'VENDOR_GR_DATA' KEY_TYPE,
    PAYMENT_REQUEST_ID KEY_ID,
    '${JOB_ID}' JOB_ID,
    '${JOB_EXECUTION_ID}' JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    '${JOB_STEP_ID}' STEP_ID
FROM
    (SELECT 
        
            cd.CATEGORY_CODE,
            scd.SUB_CATEGORY_CODE,
            gri.TAX_TYPE,
            gri.TAX_VALUE TOTAL_AMOUNT,
            gr.EXTRA_CHARGES,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            ud.COST_CENTER,
            gr.PAYMENT_REQUEST_ID,
            pr.INVOICE_NUMBER,
            st.STATE,
            st.STATE_LONG_CODE
    FROM
        KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GOODS_RECEIVED_DATA gr
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.ITEM_TAX_DETAIL_DATA gri on i.GOODS_RECEIVED_ITEM_ID = gri.GR_ITEM_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    AND cd.CATEGORY_ID IN (3) 
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON gr.DELIVERY_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL st ON ld.STATE_DETAIL_ID = st.STATE_DETAIL_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = gr.PAYMENT_REQUEST_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST_STATUS_LOG prs ON prs.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
        AND prs.TO_STATUS = 'APPROVED'
   WHERE
        prs.UPDATE_TIME BETWEEN DATE_ADD('${BUSINESS_DATE}', INTERVAL 295 MINUTE) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL 1735 MINUTE)
        AND gri.TAX_VALUE IS NOT NULL AND gri.TAX_VALUE > '0' AND ud.CONSIDERED_FOR_ACCOUNTING='Y' AND v.VENDOR_ID&lt;&gt;1926) z
GROUP BY ACCOUNT_CODE , PAYMENT_REQUEST_ID
UNION ALL
SELECT 
    DATE_FORMAT('${BUSINESS_DATE}', '%d-%m-%Y') BUSINESS_DATE,
    z.UNIT_ID UNIT_ID,
    z.UNIT_NAME UNIT_NAME,
    z.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(PAYMENT_REQUEST_ID, '_', INVOICE_NUMBER) REFERENCE_NUMBER,
    CONCAT('JV/PUR/',
            z.UNIT_ID,
            '/',
            DATE_FORMAT('${BUSINESS_DATE}', '%Y/%m'),
            '/',
            z.PAYMENT_REQUEST_ID) TRANSACTION_NUMBER,
    z.STATE STATE,
    z.STATE_LONG_CODE STATE_CODE,
    'VENDOR GR BOOKING' NARRATION,
    CONCAT('VENDOR_CODE_', VENDOR_ID) PARTICULARS,
    SUM(TOTAL_AMOUNT) TOTAL_SALES,
    'NO' IS_DEBIT,
    CONCAT('VENDOR_CODE_', VENDOR_ID) ACCOUNT_CODE,
    'VENDOR_GR_DATA' KEY_TYPE,
    PAYMENT_REQUEST_ID KEY_ID,
    '${JOB_ID}' JOB_ID,
    '${JOB_EXECUTION_ID}' JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    '${JOB_STEP_ID}' STEP_ID
FROM
    (SELECT 
            gr.VENDOR_ID,
            gr.TOTAL_AMOUNT TOTAL_AMOUNT,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            ud.COST_CENTER,
            gr.PAYMENT_REQUEST_ID,
            pr.INVOICE_NUMBER,
            st.STATE,
            st.STATE_LONG_CODE
    FROM
        KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_GOODS_RECEIVED_DATA gr
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud ON gr.DELIVERY_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    INNER JOIN KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL st ON ld.STATE_DETAIL_ID = st.STATE_DETAIL_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = gr.PAYMENT_REQUEST_ID
    INNER JOIN KETTLE_SCM${SCHEMA_SUFFIX}.PAYMENT_REQUEST_STATUS_LOG prs ON prs.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
        AND prs.TO_STATUS = 'APPROVED'
    WHERE
        prs.UPDATE_TIME BETWEEN DATE_ADD('${BUSINESS_DATE}', INTERVAL 295 MINUTE) AND DATE_ADD('${BUSINESS_DATE}', INTERVAL 1735 MINUTE)
        and gr.TOTAL_AMOUNT IS NOT NULL and gr.TOTAL_AMOUNT > '0.0' 
		AND ud.CONSIDERED_FOR_ACCOUNTING='Y' AND v.VENDOR_ID&lt;&gt;1926) z
GROUP BY ACCOUNT_CODE , PAYMENT_REQUEST_ID</sql>
    <limit>0</limit>
    <lookup />
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema />
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>240</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Table output</name>
    <type>TableOutput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <connection>OUTPUT_CONNECTION</connection>
    <schema>KETTLE_JOBS${SCHEMA_SUFFIX}</schema>
    <table>AGGREGATED_RESULT_DEFINITION</table>
    <commit>1000</commit>
    <truncate>N</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>Y</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field />
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field />
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field />
    <fields>
      <field>
        <column_name>BUSINESS_DATE</column_name>
        <stream_name>BUSINESS_DATE</stream_name>
      </field>
      <field>
        <column_name>UNIT_ID</column_name>
        <stream_name>UNIT_ID</stream_name>
      </field>
      <field>
        <column_name>UNIT_NAME</column_name>
        <stream_name>UNIT_NAME</stream_name>
      </field>
      <field>
        <column_name>BUSINESS_COST_CENTRE</column_name>
        <stream_name>BUSINESS_COST_CENTRE</stream_name>
      </field>
      <field>
        <column_name>REFERENCE_NUMBER</column_name>
        <stream_name>REFERENCE_NUMBER</stream_name>
      </field>
      <field>
        <column_name>TRANSACTION_NUMBER</column_name>
        <stream_name>TRANSACTION_NUMBER</stream_name>
      </field>
      <field>
        <column_name>STATE</column_name>
        <stream_name>STATE</stream_name>
      </field>
      <field>
        <column_name>STATE_CODE</column_name>
        <stream_name>STATE_CODE</stream_name>
      </field>
      <field>
        <column_name>NARRATION</column_name>
        <stream_name>NARRATION</stream_name>
      </field>
      <field>
        <column_name>PARTICULARS</column_name>
        <stream_name>PARTICULARS</stream_name>
      </field>
      <field>
        <column_name>TOTAL_SALES</column_name>
        <stream_name>TOTAL_SALES</stream_name>
      </field>
      <field>
        <column_name>IS_DEBIT</column_name>
        <stream_name>IS_DEBIT</stream_name>
      </field>
      <field>
        <column_name>ACCOUNT_CODE</column_name>
        <stream_name>ACCOUNT_CODE</stream_name>
      </field>
      <field>
        <column_name>KEY_TYPE</column_name>
        <stream_name>KEY_TYPE</stream_name>
      </field>
      <field>
        <column_name>KEY_ID</column_name>
        <stream_name>KEY_ID</stream_name>
      </field>
      <field>
        <column_name>JOB_ID</column_name>
        <stream_name>JOB_ID</stream_name>
      </field>
      <field>
        <column_name>JOB_EXECUTION_ID</column_name>
        <stream_name>JOB_EXECUTION_ID</stream_name>
      </field>
      <field>
        <column_name>JOB_EXECUTION_TIME</column_name>
        <stream_name>JOB_EXECUTION_TIME</stream_name>
      </field>
      <field>
        <column_name>TYPE_OF_DATA</column_name>
        <stream_name>TYPE_OF_DATA</stream_name>
      </field>
      <field>
        <column_name>DATA_STATUS</column_name>
        <stream_name>DATA_STATUS</stream_name>
      </field>
      <field>
        <column_name>STEP_ID</column_name>
        <stream_name>STEP_ID</stream_name>
      </field>
    </fields>
    <cluster_schema />
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
