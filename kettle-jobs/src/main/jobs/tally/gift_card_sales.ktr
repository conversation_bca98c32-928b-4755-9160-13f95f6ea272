<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>gift_card_sales</name>
    <description />
    <extended_description />
    <trans_version />
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>BUSINESS_DATE</name>
        <default_value>2020-10-01</default_value>
        <description />
      </parameter>
      <parameter>
        <name>HOST_NAME</name>
        <default_value>*************</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_EXECUTION_ID</name>
        <default_value>1022</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_EXECUTION_TIME</name>
        <default_value>2019-03-30 15:00:01</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_ID</name>
        <default_value>3</default_value>
        <description />
      </parameter>
      <parameter>
        <name>JOB_STEP_ID</name>
        <default_value>3</default_value>
        <description />
      </parameter>
      <parameter>
        <name>PASSWORD</name>
        <default_value>321in#@!</default_value>
        <description />
      </parameter>
      <parameter>
        <name>SCHEMA_SUFFIX</name>
                              <default_value />
        <description />
      </parameter>
      <parameter>
        <name>TYPE_OF_DATA</name>
        <default_value>TAX_DATA</default_value>
        <description />
      </parameter>
      <parameter>
        <name>USERNAME</name>
        <default_value>root</default_value>
        <description />
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection />
        <schema />
        <table />
        <size_limit_lines />
        <interval />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject />
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject />
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject />
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject />
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject />
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject />
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection />
        <schema />
        <table />
        <interval />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection />
        <schema />
        <table />
        <timeout_days />
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection />
      <table />
      <field />
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file />
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2020/04/04 21:15:50.449</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/04/04 21:15:50.449</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>con</name>
    <server>${HOST_NAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE_MASTER${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername />
    <data_tablespace />
    <index_tablespace />
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>OUTPUT_CONNECTION</name>
    <server>${HOST_NAME}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE_JOBS${SCHEMA_SUFFIX}</database>
    <port>3306</port>
    <username>${USERNAME}</username>
    <password>${PASSWORD}</password>
    <servername />
    <data_tablespace />
    <index_tablespace />
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>Table input</from>
      <to>Table output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Table input</name>
    <type>TableInput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <connection>con</connection>
    <sql>SELECT 
   DATE_FORMAT('${BUSINESS_DATE}',"%d-%m-%Y") BUSINESS_DATE,
    ud.UNIT_ID UNIT_ID,
	ud.UNIT_NAME UNIT_NAME,
	ud.COST_CENTER BUSINESS_COST_CENTRE,
    CONCAT(DATE_FORMAT('${BUSINESS_DATE}', "%Y%m%d"), '#',ud.UNIT_ID ) REFERENCE_NUMBER,
	CONCAT('JV/SALES/',ud.UNIT_ID,'/',DATE_FORMAT('${BUSINESS_DATE}',"%Y/%m/%d")) TRANSACTION_NUMBER,
    sd.STATE STATE,
    sd.STATE_LONG_CODE STATE_CODE,
	'GIFT CARD SALES BOOKING' NARRATION,
    'SALES_GIFT_CARD' PARTICULARS,
    SUM(COALESCE(cc.CARD_INITIAL_AMOUNT, 0) + COALESCE(cc.CARD_INITIAL_OFFER, 0)) TOTAL_SALES,
    'NO' IS_DEBIT,
    'SALES_GIFT_CARD' ACCOUNT_CODE,
    'SALES_DATA' KEY_TYPE,
     cc.CARD_TYPE KEY_ID,
    ${JOB_ID} JOB_ID,
    ${JOB_EXECUTION_ID} JOB_EXECUTION_ID,
    '${JOB_EXECUTION_TIME}' JOB_EXECUTION_TIME,
    '${TYPE_OF_DATA}' TYPE_OF_DATA,
    'ACTIVE' DATA_STATUS,
    ${JOB_STEP_ID} STEP_ID
FROM
    KETTLE_MASTER${SCHEMA_SUFFIX}.UNIT_DETAIL ud
        INNER JOIN
    KETTLE_MASTER${SCHEMA_SUFFIX}.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
        INNER JOIN
    KETTLE_MASTER${SCHEMA_SUFFIX}.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
        INNER JOIN
    KETTLE${SCHEMA_SUFFIX}.ORDER_DETAIL od ON od.UNIT_ID = ud.UNIT_ID
        INNER  JOIN
    KETTLE${SCHEMA_SUFFIX}.CASH_CARD_DETAIL cc ON od.ORDER_ID = cc.PURCHASE_ORDER_ID
        AND cc.CARD_TYPE = 'ECARD'
WHERE
    od.BUSINESS_DATE =  '${BUSINESS_DATE}'
    AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
	AND ud.CONSIDERED_FOR_ACCOUNTING='Y'
GROUP BY ud.UNIT_ID
;
</sql>
    <limit>0</limit>
    <lookup />
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema />
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>240</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Table output</name>
    <type>TableOutput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <connection>OUTPUT_CONNECTION</connection>
    <schema>KETTLE_JOBS${SCHEMA_SUFFIX}</schema>
    <table>AGGREGATED_RESULT_DEFINITION</table>
    <commit>1000</commit>
    <truncate>N</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>Y</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field />
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field />
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field />
    <fields>
      <field>
        <column_name>BUSINESS_DATE</column_name>
        <stream_name>BUSINESS_DATE</stream_name>
      </field>
      <field>
        <column_name>UNIT_ID</column_name>
        <stream_name>UNIT_ID</stream_name>
      </field>
      <field>
        <column_name>UNIT_NAME</column_name>
        <stream_name>UNIT_NAME</stream_name>
      </field>
      <field>
        <column_name>BUSINESS_COST_CENTRE</column_name>
        <stream_name>BUSINESS_COST_CENTRE</stream_name>
      </field>
      <field>
        <column_name>REFERENCE_NUMBER</column_name>
        <stream_name>REFERENCE_NUMBER</stream_name>
      </field>
      <field>
        <column_name>TRANSACTION_NUMBER</column_name>
        <stream_name>TRANSACTION_NUMBER</stream_name>
      </field>
      <field>
        <column_name>STATE</column_name>
        <stream_name>STATE</stream_name>
      </field>
      <field>
        <column_name>STATE_CODE</column_name>
        <stream_name>STATE_CODE</stream_name>
      </field>
      <field>
        <column_name>NARRATION</column_name>
        <stream_name>NARRATION</stream_name>
      </field>
      <field>
        <column_name>PARTICULARS</column_name>
        <stream_name>PARTICULARS</stream_name>
      </field>
      <field>
        <column_name>TOTAL_SALES</column_name>
        <stream_name>TOTAL_SALES</stream_name>
      </field>
      <field>
        <column_name>IS_DEBIT</column_name>
        <stream_name>IS_DEBIT</stream_name>
      </field>
      <field>
        <column_name>ACCOUNT_CODE</column_name>
        <stream_name>ACCOUNT_CODE</stream_name>
      </field>
      <field>
        <column_name>KEY_TYPE</column_name>
        <stream_name>KEY_TYPE</stream_name>
      </field>
      <field>
        <column_name>KEY_ID</column_name>
        <stream_name>KEY_ID</stream_name>
      </field>
      <field>
        <column_name>JOB_ID</column_name>
        <stream_name>JOB_ID</stream_name>
      </field>
      <field>
        <column_name>JOB_EXECUTION_ID</column_name>
        <stream_name>JOB_EXECUTION_ID</stream_name>
      </field>
      <field>
        <column_name>JOB_EXECUTION_TIME</column_name>
        <stream_name>JOB_EXECUTION_TIME</stream_name>
      </field>
      <field>
        <column_name>TYPE_OF_DATA</column_name>
        <stream_name>TYPE_OF_DATA</stream_name>
      </field>
      <field>
        <column_name>DATA_STATUS</column_name>
        <stream_name>DATA_STATUS</stream_name>
      </field>
      <field>
        <column_name>STEP_ID</column_name>
        <stream_name>STEP_ID</stream_name>
      </field>
    </fields>
    <cluster_schema />
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
