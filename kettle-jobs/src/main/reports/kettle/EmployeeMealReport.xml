<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Employee Meal Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Employee Meal Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    *
FROM
    (SELECT 
        a.MEAL_ID,
            od.BUSINESS_DATE,
            emp.EMP_ID,
            emp.EMPLOYEE_CODE,
            emp.EMP_NAME,
            ud.UNIT_NAME,
            od.GENERATED_ORDER_ID,
            od.BILLING_SERVER_TIME,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.EMPLOYEE_MEAL_ALLOWANCE_DATA a
    LEFT JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = a.ORDER_ID
    LEFT JOIN KETTLE_MASTER.EMPLOYEE_DETAIL emp ON emp.EMP_ID = a.EMPLOYEE_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE >= CAST(DATE_FORMAT(DATE_SUB(current_date(), INTERVAL 1 MONTH) ,'%Y-%m-25') as DATE)) a
				     ]]>
					</content>
				</report>

			</reports>
		</category>
	</categories>
</ReportCategories>
