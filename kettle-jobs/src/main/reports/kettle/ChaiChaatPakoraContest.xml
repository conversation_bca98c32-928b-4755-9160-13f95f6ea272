<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="Chai Chaat Pakora Contest" type="Automated"
                          accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>


                                <report id="10" name="REGION LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
                                                SELECT
														T1.REGION,
														REGION_INCIDENCE_TARGET AS CCP_TARGET,
														ROUND(((Paneer_and_<PERSON>chi_Pakoras + Sabudana_Tikki + Veg_Tikki_<PERSON>latter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore)/total_Tickets)*100,0) AS CCP_INCIDENCE,
														Baarish_<PERSON>ale_Pakore,
														B<PERSON><PERSON><PERSON>,
														<PERSON><PERSON>_<PERSON>,
														<PERSON><PERSON>_<PERSON><PERSON>,
														<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>,
														<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,
														<PERSON><PERSON>_<PERSON>r_<PERSON>at,
														<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,
														<PERSON><PERSON><PERSON><PERSON>_Tikki,
														<PERSON><PERSON>_and_<PERSON>chi_Pakoras,
														Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore AS Total_Qty,
														total_Tickets
													FROM
														(SELECT
															A7.BUSINESS_DATE,
																A7.REGION,
																CONCAT(ROUND((FOOD_SOLD_QTY / Total_Tickets) * 100, 0), '%') Food_Incidence,
																Baarish_Wale_Pakore,
																Bhelpuri,
																Bun_Samosa,
																Vada_Pav,
																Kaala_Chana_Chaat,
																Palak_Patta_Crispies,
																Samosa_Matar_Chaat,
																Veg_Tikki_Platter,
																Sabudana_Tikki,
																Paneer_and_Mirchi_Pakoras,
																total_Tickets
														FROM
															(SELECT
															A1.BUSINESS_DATE,
																A1.REGION,
																COALESCE(A1.Total_Tickets, 0) total_Tickets,
																COALESCE(FOOD_SOLD_QTY, 0) FOOD_SOLD_QTY,
																COALESCE(Baarish_Wale_Pakore, 0) Baarish_Wale_Pakore,
																COALESCE(Bhelpuri, 0) Bhelpuri,
																COALESCE(Bun_Samosa, 0) Bun_Samosa,
																COALESCE(Vada_Pav, 0) Vada_Pav,
																COALESCE(Kaala_Chana_Chaat, 0) Kaala_Chana_Chaat,
																COALESCE(Palak_Patta_Crispies, 0) Palak_Patta_Crispies,
																COALESCE(Samosa_Matar_Chaat, 0) Samosa_Matar_Chaat,
																COALESCE(Veg_Tikki_Platter, 0) Veg_Tikki_Platter,
																COALESCE(Sabudana_Tikki, 0) Sabudana_Tikki,
																COALESCE(Paneer_and_Mirchi_Pakoras, 0) Paneer_and_Mirchi_Pakoras
														FROM
															(SELECT
															A.BUSINESS_DATE, REGION, COUNT(A.order_ID) Total_Tickets
														FROM
															(SELECT
															od.ORDER_ID, '2020-09-01' AS BUSINESS_DATE, REGION
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID = ud.UNIT_MANAGER
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.IS_GIFT_CARD_ORDER <> 'Y'
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
														GROUP BY 1 , 2) AS A
														GROUP BY 1 , 2) AS A1
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Baarish_Wale_Pakore
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1109
														GROUP BY 1) A51 ON A51.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Bhelpuri
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1200
														GROUP BY 1) A52 ON A52.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Bun_Samosa
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1113
														GROUP BY 1) A53 ON A53.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Vada_Pav
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 670
														GROUP BY 1) A54 ON A54.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Kaala_Chana_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1064
														GROUP BY 1) A55 ON A55.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Palak_Patta_Crispies
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1065
														GROUP BY 1) A56 ON A56.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Samosa_Matar_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1201
														GROUP BY 1) A57 ON A57.REGION = A1.REGION
														LEFT JOIN (SELECT
															REGION, SUM(oi.QUANTITY) Veg_Tikki_Platter
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1302
														GROUP BY 1) A58 ON A58.REGION = A1.REGION
														LEFT JOIN (SELECT
															L1.REGION, SUM(oi.QUANTITY) Sabudana_Tikki
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1024
														GROUP BY 1) A59 ON A59.REGION = A1.REGION
														LEFT JOIN (SELECT
															REGION, SUM(oi.QUANTITY) Paneer_and_Mirchi_Pakoras
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1402
														GROUP BY 1) A60 ON A60.REGION = A1.REGION
														LEFT JOIN (SELECT
															AA.REGION,
																SUM(CASE
																	WHEN PRODUCT_ID IN (1109 , 1200, 1113, 670, 1064, 1065, 1201, 1302, 1024, 1402) THEN QUANTITY
																	ELSE 0
																END) FOOD_SOLD_QTY,
																SUM(TOTAL_SALES) TOTAL_SALES
														FROM
															(SELECT
															L1.REGION,
																PDB.PRODUCT_ID PRODUCT_ID,
																SUM(oi.QUANTITY) QUANTITY,
																SUM(oi.AMOUNT_PAID) TOTAL_SALES
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
														GROUP BY 1 , 2) AA
														GROUP BY 1) A6 ON A6.REGION = A1.REGION) A7
														GROUP BY 1 , 2 DESC
														ORDER BY REGION) A8
															LEFT JOIN
														(SELECT
															REGION, REGION_INCIDENCE_TARGET
														FROM
															CLM_ANALYTICS.TEMP_UNIT_L1_L2
														GROUP BY 1) T1 ON T1.REGION = A8.REGION;
                                       ]]>
                                        </content>
                                </report>

                                <report id="10" name="L2 LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

													SELECT
														T1.L2_NAME,
														L2_INCIDENCE_TARGET AS L2_CCP_TARGET,
														ROUND(((Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore)/total_Tickets)*100,0) AS CCP_INCIDENCE,
														Baarish_Wale_Pakore,
														Bhelpuri,
														Bun_Samosa,
														Vada_Pav,
														Kaala_Chana_Chaat,
														Palak_Patta_Crispies,
														Samosa_Matar_Chaat,
														Veg_Tikki_Platter,
														Sabudana_Tikki,
														Paneer_and_Mirchi_Pakoras,
														Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore AS Total_Qty,
														total_Tickets
													FROM
														(SELECT
															A7.BUSINESS_DATE,
																A7.L2_NAME,
																CONCAT(ROUND((FOOD_SOLD_QTY / Total_Tickets) * 100, 0), '%') Food_Incidence,
																Baarish_Wale_Pakore,
																Bhelpuri,
																Bun_Samosa,
																Vada_Pav,
																Kaala_Chana_Chaat,
																Palak_Patta_Crispies,
																Samosa_Matar_Chaat,
																Veg_Tikki_Platter,
																Sabudana_Tikki,
																Paneer_and_Mirchi_Pakoras,
																total_Tickets
														FROM
															(SELECT
															A1.BUSINESS_DATE,
																A1.L2_NAME,
																COALESCE(A1.Total_Tickets, 0) total_Tickets,
																COALESCE(FOOD_SOLD_QTY, 0) FOOD_SOLD_QTY,
																COALESCE(Baarish_Wale_Pakore, 0) Baarish_Wale_Pakore,
																COALESCE(Bhelpuri, 0) Bhelpuri,
																COALESCE(Bun_Samosa, 0) Bun_Samosa,
																COALESCE(Vada_Pav, 0) Vada_Pav,
																COALESCE(Kaala_Chana_Chaat, 0) Kaala_Chana_Chaat,
																COALESCE(Palak_Patta_Crispies, 0) Palak_Patta_Crispies,
																COALESCE(Samosa_Matar_Chaat, 0) Samosa_Matar_Chaat,
																COALESCE(Veg_Tikki_Platter, 0) Veg_Tikki_Platter,
																COALESCE(Sabudana_Tikki, 0) Sabudana_Tikki,
																COALESCE(Paneer_and_Mirchi_Pakoras, 0) Paneer_and_Mirchi_Pakoras
														FROM
															(SELECT
															A.BUSINESS_DATE, L2_NAME, COUNT(A.order_ID) Total_Tickets
														FROM
															(SELECT
															od.ORDER_ID, '2020-09-01' AS BUSINESS_DATE, L2_NAME
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID = ud.UNIT_MANAGER
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.IS_GIFT_CARD_ORDER <> 'Y'
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
														GROUP BY 1 , 2) AS A
														GROUP BY 1 , 2) AS A1
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Baarish_Wale_Pakore
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1109
														GROUP BY 1) A51 ON A51.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Bhelpuri
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1200
														GROUP BY 1) A52 ON A52.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Bun_Samosa
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1113
														GROUP BY 1) A53 ON A53.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Vada_Pav
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 670
														GROUP BY 1) A54 ON A54.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Kaala_Chana_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1064
														GROUP BY 1) A55 ON A55.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Palak_Patta_Crispies
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1065
														GROUP BY 1) A56 ON A56.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Samosa_Matar_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1201
														GROUP BY 1) A57 ON A57.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L2_NAME, SUM(oi.QUANTITY) Veg_Tikki_Platter
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1302
														GROUP BY 1) A58 ON A58.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L1.L2_NAME, SUM(oi.QUANTITY) Sabudana_Tikki
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1024
														GROUP BY 1) A59 ON A59.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															L2_NAME, SUM(oi.QUANTITY) Paneer_and_Mirchi_Pakoras
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1402
														GROUP BY 1) A60 ON A60.L2_NAME = A1.L2_NAME
														LEFT JOIN (SELECT
															AA.L2_NAME,
																SUM(CASE
																	WHEN PRODUCT_ID IN (1109 , 1200, 1113, 670, 1064, 1065, 1201, 1302, 1024, 1402) THEN QUANTITY
																	ELSE 0
																END) FOOD_SOLD_QTY,
																SUM(TOTAL_SALES) TOTAL_SALES
														FROM
															(SELECT
															L1.L2_NAME,
																PDB.PRODUCT_ID PRODUCT_ID,
																SUM(oi.QUANTITY) QUANTITY,
																SUM(oi.AMOUNT_PAID) TOTAL_SALES
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
														GROUP BY 1 , 2) AA
														GROUP BY 1) A6 ON A6.L2_NAME = A1.L2_NAME) A7
														GROUP BY 1 , 2 DESC
														ORDER BY L2_NAME) A8
															LEFT JOIN
														(SELECT
															L2_NAME, L2_INCIDENCE_TARGET
														FROM
															CLM_ANALYTICS.TEMP_UNIT_L1_L2
														GROUP BY 1) T1 ON T1.L2_NAME = A8.L2_NAME;

                                                ]]>
                                        </content>
                                </report>

                                <report id="10" name="L1 LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
													SELECT
														T1.L1_NAME,
														L1_INCIDENCE_TARGET AS L1_CCP_TARGET,
														ROUND(((Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore)/total_Tickets)*100,0) AS CCP_INCIDENCE,
														Baarish_Wale_Pakore,
														Bhelpuri,
														Bun_Samosa,
														Vada_Pav,
														Kaala_Chana_Chaat,
														Palak_Patta_Crispies,
														Samosa_Matar_Chaat,
														Veg_Tikki_Platter,
														Sabudana_Tikki,
														Paneer_and_Mirchi_Pakoras,
														Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore AS Total_Qty,
														total_Tickets
													FROM
														(SELECT
															A7.BUSINESS_DATE,
																A7.L1_NAME,
																CONCAT(ROUND((FOOD_SOLD_QTY / Total_Tickets) * 100, 0), '%') Food_Incidence,
																Baarish_Wale_Pakore,
																Bhelpuri,
																Bun_Samosa,
																Vada_Pav,
																Kaala_Chana_Chaat,
																Palak_Patta_Crispies,
																Samosa_Matar_Chaat,
																Veg_Tikki_Platter,
																Sabudana_Tikki,
																Paneer_and_Mirchi_Pakoras,
																total_Tickets
														FROM
															(SELECT
															A1.BUSINESS_DATE,
																A1.L1_NAME,
																COALESCE(A1.Total_Tickets, 0) total_Tickets,
																COALESCE(FOOD_SOLD_QTY, 0) FOOD_SOLD_QTY,
																COALESCE(Baarish_Wale_Pakore, 0) Baarish_Wale_Pakore,
																COALESCE(Bhelpuri, 0) Bhelpuri,
																COALESCE(Bun_Samosa, 0) Bun_Samosa,
																COALESCE(Vada_Pav, 0) Vada_Pav,
																COALESCE(Kaala_Chana_Chaat, 0) Kaala_Chana_Chaat,
																COALESCE(Palak_Patta_Crispies, 0) Palak_Patta_Crispies,
																COALESCE(Samosa_Matar_Chaat, 0) Samosa_Matar_Chaat,
																COALESCE(Veg_Tikki_Platter, 0) Veg_Tikki_Platter,
																COALESCE(Sabudana_Tikki, 0) Sabudana_Tikki,
																COALESCE(Paneer_and_Mirchi_Pakoras, 0) Paneer_and_Mirchi_Pakoras
														FROM
															(SELECT
															A.BUSINESS_DATE, L1_NAME, COUNT(A.order_ID) Total_Tickets
														FROM
															(SELECT
															od.ORDER_ID, '2020-09-01' AS BUSINESS_DATE, L1_NAME
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID = ud.UNIT_MANAGER
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.IS_GIFT_CARD_ORDER <> 'Y'
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
														GROUP BY 1 , 2) AS A
														GROUP BY 1 , 2) AS A1
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Baarish_Wale_Pakore
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1109
														GROUP BY 1) A51 ON A51.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Bhelpuri
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1200
														GROUP BY 1) A52 ON A52.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Bun_Samosa
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1113
														GROUP BY 1) A53 ON A53.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Vada_Pav
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 670
														GROUP BY 1) A54 ON A54.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Kaala_Chana_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1064
														GROUP BY 1) A55 ON A55.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Palak_Patta_Crispies
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1065
														GROUP BY 1) A56 ON A56.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Samosa_Matar_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1201
														GROUP BY 1) A57 ON A57.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1_NAME, SUM(oi.QUANTITY) Veg_Tikki_Platter
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1302
														GROUP BY 1) A58 ON A58.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1.L1_NAME, SUM(oi.QUANTITY) Sabudana_Tikki
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1024
														GROUP BY 1) A59 ON A59.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															L1_NAME, SUM(oi.QUANTITY) Paneer_and_Mirchi_Pakoras
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1402
														GROUP BY 1) A60 ON A60.L1_NAME = A1.L1_NAME
														LEFT JOIN (SELECT
															AA.L1_NAME,
																SUM(CASE
																	WHEN PRODUCT_ID IN (1109 , 1200, 1113, 670, 1064, 1065, 1201, 1302, 1024, 1402) THEN QUANTITY
																	ELSE 0
																END) FOOD_SOLD_QTY,
																SUM(TOTAL_SALES) TOTAL_SALES
														FROM
															(SELECT
															L1.L1_NAME,
																PDB.PRODUCT_ID PRODUCT_ID,
																SUM(oi.QUANTITY) QUANTITY,
																SUM(oi.AMOUNT_PAID) TOTAL_SALES
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
														INNER JOIN CLM_ANALYTICS.TEMP_UNIT_L1_L2 L1 ON L1.UNIT_ID = od.UNIT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
														GROUP BY 1 , 2) AA
														GROUP BY 1) A6 ON A6.L1_NAME = A1.L1_NAME) A7
														GROUP BY 1 , 2 DESC
														ORDER BY L1_NAME) A8
															LEFT JOIN
														(SELECT
															L1_NAME, L1_INCIDENCE_TARGET
														FROM
															CLM_ANALYTICS.TEMP_UNIT_L1_L2
														GROUP BY 1) T1 ON T1.L1_NAME = A8.L1_NAME;


                                             ]]>
                                        </content>
                                </report>





                                <report id="10" name="All Cafes" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

													SELECT
														A8.UNIT_NAME,
														A8.AREA_MANAGER,
														CCP_INCIDENCE_TARGET AS CCP_TARGET,
														ROUND(((Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore)/total_Tickets)*100,0) AS CCP_INCIDENCE,
														Baarish_Wale_Pakore,
														Bhelpuri,
														Bun_Samosa,
														Vada_Pav,
														Kaala_Chana_Chaat,
														Palak_Patta_Crispies,
														Samosa_Matar_Chaat,
														Veg_Tikki_Platter,
														Sabudana_Tikki,
														Paneer_and_Mirchi_Pakoras,
														Paneer_and_Mirchi_Pakoras + Sabudana_Tikki + Veg_Tikki_Platter + Samosa_Matar_Chaat + Kaala_Chana_Chaat + Palak_Patta_Crispies + Vada_Pav + Bhelpuri + Bun_Samosa + Baarish_Wale_Pakore AS Total_Qty,
														total_Tickets
													FROM
														(SELECT
															A7.BUSINESS_DATE,
																A7.UNIT_ID,
																A7.UNIT_NAME,
																A7.AREA_MANAGER,
																CONCAT(ROUND((FOOD_SOLD_QTY / Total_Tickets) * 100, 0), '%') Food_Incidence,
																Baarish_Wale_Pakore,
																Bhelpuri,
																Bun_Samosa,
																Vada_Pav,
																Kaala_Chana_Chaat,
																Palak_Patta_Crispies,
																Samosa_Matar_Chaat,
																Veg_Tikki_Platter,
																Sabudana_Tikki,
																Paneer_and_Mirchi_Pakoras,
																total_Tickets
														FROM
															(SELECT
															A1.BUSINESS_DATE,
																A1.UNIT_ID,
																A1.UNIT_NAME,
																A1.AREA_MANAGER,
																COALESCE(A1.Total_Tickets, 0) total_Tickets,
																COALESCE(FOOD_SOLD_QTY, 0) FOOD_SOLD_QTY,
																COALESCE(Baarish_Wale_Pakore, 0) Baarish_Wale_Pakore,
																COALESCE(Bhelpuri, 0) Bhelpuri,
																COALESCE(Bun_Samosa, 0) Bun_Samosa,
																COALESCE(Vada_Pav, 0) Vada_Pav,
																COALESCE(Kaala_Chana_Chaat, 0) Kaala_Chana_Chaat,
																COALESCE(Palak_Patta_Crispies, 0) Palak_Patta_Crispies,
																COALESCE(Samosa_Matar_Chaat, 0) Samosa_Matar_Chaat,
																COALESCE(Veg_Tikki_Platter, 0) Veg_Tikki_Platter,
																COALESCE(Sabudana_Tikki, 0) Sabudana_Tikki,
																COALESCE(Paneer_and_Mirchi_Pakoras, 0) Paneer_and_Mirchi_Pakoras
														FROM
															(SELECT
															A.BUSINESS_DATE,
																A.UNIT_ID,
																A.UNIT_NAME,
																A.AREA_MANAGER,
																COUNT(A.order_ID) Total_Tickets
														FROM
															(SELECT
															od.ORDER_ID,
																'2020-09-01' AS BUSINESS_DATE,
																ud.UNIT_NAME,
																ED.EMP_NAME AREA_MANAGER,
																ud.UNIT_ID
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID = ud.UNIT_MANAGER
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.IS_GIFT_CARD_ORDER <> 'Y'
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
														GROUP BY 1 , 2 , 3 , 4 , 5) AS A
														GROUP BY 1 , 2 , 3 , 4) AS A1
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Baarish_Wale_Pakore
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1109
														GROUP BY 1) A51 ON A51.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Bhelpuri
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1200
														GROUP BY 1) A52 ON A52.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Bun_Samosa
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1113
														GROUP BY 1) A53 ON A53.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Vada_Pav
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 670
														GROUP BY 1) A54 ON A54.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Kaala_Chana_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1064
														GROUP BY 1) A55 ON A55.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Palak_Patta_Crispies
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1065
														GROUP BY 1) A56 ON A56.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Samosa_Matar_Chaat
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1201
														GROUP BY 1) A57 ON A57.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Veg_Tikki_Platter
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1302
														GROUP BY 1) A58 ON A58.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Sabudana_Tikki
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND pd.PRODUCT_ID = 1024
														GROUP BY 1) A59 ON A59.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															ud.UNIT_ID, SUM(oi.QUANTITY) Paneer_and_Mirchi_Pakoras
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND pd.PRODUCT_ID = 1402
														GROUP BY 1) A60 ON A60.UNIT_ID = A1.UNIT_ID
														LEFT JOIN (SELECT
															AA.UNIT_ID,
																SUM(CASE
																	WHEN PRODUCT_ID IN (1109 , 1200, 1113, 670, 1064, 1065, 1201, 1302, 1024, 1402) THEN QUANTITY
																	ELSE 0
																END) FOOD_SOLD_QTY,
																SUM(TOTAL_SALES) TOTAL_SALES
														FROM
															(SELECT
															od.UNIT_ID,
																PDB.PRODUCT_ID PRODUCT_ID,
																SUM(oi.QUANTITY) QUANTITY,
																SUM(oi.AMOUNT_PAID) TOTAL_SALES
														FROM
															KETTLE.ORDER_DETAIL od
														INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
														INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
														WHERE
															od.ORDER_STATUS <> 'CANCELLED'
																AND ORDER_TYPE = 'ORDER'
																AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
																AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
																AND (oi.IS_COMPLIMENTARY = 'N'
																OR oi.IS_COMPLIMENTARY IS NULL)
																AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                                                                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                                                                    ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
																AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
														GROUP BY 1 , 2) AA
														GROUP BY 1) A6 ON A6.UNIT_ID = A1.UNIT_ID) A7
														GROUP BY 1 , 2 , 3 , 4 DESC
														ORDER BY AREA_MANAGER) A8
															LEFT JOIN
														(SELECT
															UNIT_ID, CCP_INCIDENCE_TARGET
														FROM
															CLM_ANALYTICS.TEMP_UNIT_L1_L2
														GROUP BY 1) T1 ON T1.UNIT_ID = A8.UNIT_ID;

                                                ]]>
                                        </content>
                                </report>
                        </reports>
                </category>
        </categories>
</ReportCategories>