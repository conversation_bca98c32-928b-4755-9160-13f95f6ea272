<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
			<category name="Cafe Order Dump"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="CSV" compress="true">
			<reports>
				<report id="1" name="Cafe Order Dump" executionType="SQL" skipInline="true"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
					
SELECT 
    *
FROM
    (SELECT 
		od.ORDER_ID,
		od.GENERATED_ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            od.TOTAL_AMOUNT,
            od.DISCOUNT_PERCENT,
            od.DISCOUNT_AMOUNT,
            od.DISCOUNT_REASON,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST_UTGST',
            SUM(CASE
                WHEN
                    otd.TAX_CODE != 'SGST/UTGST'
                        && otd.TAX_CODE != 'CGST'
                THEN
                    otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            od.ROUND_OFF_AMOUNT,
            od.SETTLED_AMOUNT,
            od.ORDER_SOURCE,
            od.POINTS_REDEEMED,
            od.SALE_AMOUNT,
            od.PROMOTIONAL_DISCOUNT,
            od.TOTAL_DISCOUNT,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.SAVING_AMOUNT,
            od.CUSTOMER_NAME,
            od.ORDER_TYPE,
            od.BUSINESS_DATE,
            od.MANUAL_BILL_BOOK_NO
    FROM
        KETTLE.ORDER_DETAIL od
        LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
	    AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) t1
        LEFT JOIN
    (SELECT 
		od.BUSINESS_DATE,
        od.ORDER_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Prepaid'
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
	    AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) t2 ON t1.ORDER_ID = t2.ORDER_ID
    LEFT JOIN 
    ( SELECT 
    od.ORDER_ID, SUM(oi.TOTAL_AMOUNT) GIFT_CARD_AMOUNT
FROM
    ORDER_DETAIL od
        INNER JOIN
    ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
WHERE
    od.BUSINESS_DATE BETWEEN :startDate AND :endDate
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
        AND oi.TAX_CODE IN ('GIFT_CARD')
	    AND od.UNIT_ID = :unitId
GROUP BY od.ORDER_ID) t3 ON t1.ORDER_ID = t3.ORDER_ID;

				     ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="STRING" />
						<param name="endDate" displayName="End Date"
							dataType="STRING" />
						<param name="unitId" displayName="unitId"
							dataType="INTEGER" />
					</params>
				</report>
			</reports>
		</category>
</categories>
</ReportCategories>

