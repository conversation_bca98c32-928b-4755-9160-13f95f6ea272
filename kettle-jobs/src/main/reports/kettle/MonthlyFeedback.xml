<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
 <categories>
<category name="Monthly Feedback Rating" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
                        <reports>
				 <report id="1" name="Monthly Feedback Rating" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
					SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    COALESCE(cst.CUMULATIVE_SCORE, 'NA') CUMULATIVE_SCORE,
    COALESCE(cst.DINE_IN_SCORE, 'NA') DINE_IN_SCORE,
    COALESCE(cst.DELIVERY_SCORE, 'NA') DELIVERY_SCORE,
    COALESCE(cst.REQUESTED_FEEDBACK, 'NA') REQUESTED_FEEDBACK,
    COALESCE(cst.RECEIVED_FEEDBACK, 'NA') RECEIVED_FEEDBACK,
    COALESCE(cst.REQUESTED_DINE_IN_FEEDBACK, 'NA') REQUESTED_DINE_IN_FEEDBACK,
    COALESCE(cst.REQUESTED_DELIVERY_FEEDBACK, 'NA') REQUESTED_DELIVERY_FEEDBACK,
    COALESCE(cst.RECEIVED_DINE_IN_FEEDBACK, 'NA') RECEIVED_DINE_IN_FEEDBACK,
    COALESCE(cst.RECEIVED_DELIVERY_FEEDBACK, 'NA') RECEIVED_DELIVERY_FEEDBACK
FROM
    KETTLE.CONSOLIDATED_CSAT_SCORE cst,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    cst.UNIT_ID = ud.UNIT_ID
        AND CSAT_SCORE_TYPE = 'MONTHLY'
        AND IS_CURRENT_SCORE = 'Y'

				     ]]></content>
				</report>
				
			</reports>
</category>
 </categories>
</ReportCategories>
