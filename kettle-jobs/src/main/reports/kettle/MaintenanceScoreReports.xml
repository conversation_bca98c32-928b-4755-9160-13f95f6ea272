<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category schedule="" toEmails="<EMAIL>" fromEmail="<EMAIL>" id="1"
                  accessCode="Automated" type="Automated" name="SCM Report: Maintenance Score">
            <reports>
                <report id="1" name="System Wise" GreturnType="com.amazonaws.util.json.JSONArray" executionType="SQL"
                        attachmentType="EXCEL" environment="DUMP">
                    <content>
                        <![CDATA[
                Select * from (
                SELECT DISTINCT
                    TOTAL_TICKETS,
                    CLOSED,
                    HOLD,
                    <PERSON><PERSON><PERSON>,
                    CLOSED_OVERDUE,
                    OPEN_OVERDUE,
                    HOLD_OVERDUE,
                    CONCAT(ROUND((TOTAL_TICKETS - OPEN_OVERDUE - CLOSED_OVERDUE - HOLD_OVERDUE) / (TOTAL_TICKETS) * 100,
                                    2),
                            '%') AS COMPLIANCE_SCORE,
                    AVERAGE_TAT
                FROM
                    (SELECT
                            COUNT(TicketId) AS TOTAL_TICKETS,
                            COUNT(CASE
                                WHEN StatusName = 'CLOSED' THEN 1
                                ELSE NULL
                            END) AS CLOSED,
                            COUNT(CASE
                                WHEN statusName = 'HOLD' THEN 1
                                ELSE NULL
                            END) AS HOLD,
                            COUNT(CASE
                                WHEN StatusName = 'Assigned' THEN 1
                                ELSE NULL
                            END) AS OPEN,
                            COUNT(CASE
                                WHEN
                                    HOUR(TIMEDIFF(CloserDateTime, CreatedDateTime)) > PRIORITY_TAT
                                        AND StatusName = 'CLOSED'
                                THEN
                                    1
                                ELSE NULL
                            END) AS CLOSED_OVERDUE,
                            COUNT(CASE
                                WHEN
                                    HOUR(TIMEDIFF(concat(current_date(),'05:00:00') , CreatedDateTime)) > PRIORITY_TAT
                                        AND (StatusName = 'Assigned')
                                THEN
                                    1
                                ELSE NULL
                            END) AS Open_OVERDUE,
                            COUNT(CASE
                                WHEN
                                    HOUR(TIMEDIFF(concat(current_date(),'05:00:00'), CreatedDateTime)) > PRIORITY_TAT
                                        AND (StatusName = 'Hold')
                                THEN
                                    1
                                ELSE NULL
                            END) AS Hold_OVERDUE,
                            ROUND(AVG(HOUR(FINAL_TAT)), 2) AS AVERAGE_TAT
                    FROM
                        (SELECT
                        t.TicketId,
                            t.TicketNo,
                            tt.ParentTicketTypeId,
                            tt1.TicketType AS Cat,
                            tt.TicketType AS SubCat,
                            l.LocationName,
                            p.Priority,
                            pt.PRIORITY_TAT,
                            ts.StatusName,
                            UserGroupName,
                            User.UserName AS TicketAssignee,
                            u2.UserName AS TIcketRaisedby,
                            ReportedDateTime,
                            Description,
                            CreatedDateTime,
                            CloserDateTime,
                            TAT,
                            CASE
                                WHEN TAT <> 0 THEN TAT
                                WHEN TAT = 0 THEN TIMEDIFF(CURRENT_TIMESTAMP(), CreatedDateTime)
                            END AS FINAL_TAT
                    FROM
                        ASSET_INFINITY.Tickets t
                    INNER JOIN ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                    INNER JOIN ASSET_INFINITY.`Ticket Type` tt1 ON tt.ParentTicketTypeId = tt1.TickettypeID
                    INNER JOIN ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                    INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                    INNER JOIN ASSET_INFINITY.PRIORITY_TAT pt ON pt.PRIORITY = p.Priority
                    INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                    INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId
                        AND u.UsergroupID = t.UsergroupID
                    INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                    INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                    INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                    WHERE
                        DATE(t.CreatedDateTime) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
                            AND tt.ParentTicketTypeId BETWEEN 2 AND 52 ) C
                   ) B)C;

                        ]]>
                    </content>
                </report>
                <report id="1" name="Region Wise" executionType="SQL" attachmentType="EXCEL" environment="DUMP">
                    <content>
                        <![CDATA[
                        Select * from (Select REGION,
                        TOTAL_TICKETS,
                        CLOSED,
                        HOLD,
                        OPEN,
                        CLOSED_OVERDUE,
                        OPEN_OVERDUE,
                        HOLD_OVERDUE,
                        CONCAT(ROUND((TOTAL_TICKETS-OPEN_OVERDUE-CLOSED_OVERDUE-HOLD_OVERDUE)/(TOTAL_TICKETS)*100,2),'%') AS COMPLIANCE_SCORE,
                        AVERAGE_TAT from (
                        Select A.UNIT_REGION AS REGION ,
                        count(TicketId) AS TOTAL_TICKETS,
                        count(CASE WHEN StatusName = 'CLOSED' THEN 1 else NULL END) AS CLOSED,
                        count(CASE WHEN statusName = 'HOLD'THEN 1 else NULL END) AS HOLD,
                        count(CASE WHEN StatusName = 'Assigned' THEN 1 else NULL END) AS OPEN,
                        count(CASE WHEN hour(timediff(CloserDateTime,CreatedDateTime)) > PRIORITY_TAT AND StatusName = 'CLOSED' THEN 1 else NULL END) AS CLOSED_OVERDUE,

                        count(CASE WHEN hour(timediff(concat(current_date(),'05:00:00'),CreatedDateTime)) > PRIORITY_TAT AND (StatusName = 'Assigned') THEN 1 else NULL END) AS Open_OVERDUE,

                        count(CASE WHEN hour(timediff(concat(current_date(),'05:00:00'),CreatedDateTime)) > PRIORITY_TAT AND (StatusName = 'Hold') THEN 1 else NULL END) AS Hold_OVERDUE,
                        ROUND(AVG(HOUR(FINAL_TAT)),2) AS AVERAGE_TAT
                        from(
                        SELECT t.TicketId,
                        t.TicketNo,
                        tt.ParentTicketTypeId,
                        tt1.TicketType AS Cat,
                        tt.TicketType AS SubCat,
                        l.LocationName,
                        u.UNIT_REGION,
                        p.Priority,
                        pt.PRIORITY_TAT,
                        ts.StatusName,
                        UserGroupName,
                        User.UserName AS TicketAssignee,
                        u2.UserName AS TIcketRaisedby,
                        ReportedDateTime,
                        Description,
                        CreatedDateTime,
                        CloserDateTime,
                        TAT,
                        CASE
                        WHEN TAT <> 0 THEN TAT
                        WHEN TAT = 0 THEN  timediff(current_timestamp(),CreatedDateTime)
                        END AS FINAL_TAT
                        From
                        ASSET_INFINITY.Tickets t
                                INNER JOIN
                            ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                                INNER JOIN
                            ASSET_INFINITY.`Ticket Type` tt1 ON tt.ParentTicketTypeId = tt1.TickettypeID

                            INNER join ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                            INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                            INNER JOIN ASSET_INFINITY.PRIORITY_TAT pt On pt.PRIORITY = p.Priority
                            INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                            INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId AND u.UsergroupID = t.UsergroupID
                            INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                            INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                            INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                            INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_NAME = l.LocationName
                            WHERE t.CreatedDateTime BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
                            AND tt.ParentTicketTypeId BETWEEN 2 AND 52 )A group by UNIT_REGION)B)C;
						]]>
                    </content>
                </report>
                <report id="1" name="Agent Wise" executionType="SQL" attachmentType="EXCEL" environment="DUMP">
                    <content>
                        <![CDATA[
                                       SELECT * FROM
                                    (SELECT DISTINCT
                                AGENT,
                                Priority,
                                TOTAL_TICKETS,
                                CLOSED,
                                HOLD,
                                OPEN,
                                CLOSED_OVERDUE,
                                OPEN_OVERDUE,
                                HOLD_OVERDUE,
                                CONCAT(ROUND((TOTAL_TICKETS - OPEN_OVERDUE - CLOSED_OVERDUE - HOLD_OVERDUE) / (TOTAL_TICKETS) * 100,
                                                2),
                                        '%') AS COMPLIANCE_SCORE,
                                AVERAGE_TAT
                            FROM
                                (SELECT C.TicketAssignee AS AGENT,
                                C.Priority,
                                        COUNT(TicketId) AS TOTAL_TICKETS,
                                        COUNT(CASE
                                            WHEN StatusName = 'CLOSED' THEN 1
                                            ELSE NULL
                                        END) AS CLOSED,
                                        COUNT(CASE
                                            WHEN statusName = 'HOLD' THEN 1
                                            ELSE NULL
                                        END) AS HOLD,
                                        COUNT(CASE
                                            WHEN StatusName = 'Assigned' THEN 1
                                            ELSE NULL
                                        END) AS OPEN,
                                        COUNT(CASE
                                            WHEN
                                                HOUR(TIMEDIFF(CloserDateTime, CreatedDateTime)) > PRIORITY_TAT
                                                    AND StatusName = 'CLOSED'
                                            THEN
                                                1
                                            ELSE NULL
                                        END) AS CLOSED_OVERDUE,
                                        COUNT(CASE
                                            WHEN
                                                HOUR(TIMEDIFF(concat(current_date(),'05:00:00'), CreatedDateTime)) > PRIORITY_TAT
                                                    AND (StatusName = 'Assigned')
                                            THEN
                                                1
                                            ELSE NULL
                                        END) AS Open_OVERDUE,
                                        COUNT(CASE
                                            WHEN
                                                HOUR(TIMEDIFF(concat(current_date(),'05:00:00'), CreatedDateTime)) > PRIORITY_TAT
                                                    AND (StatusName = 'Hold')
                                            THEN
                                                1
                                            ELSE NULL
                                        END) AS Hold_OVERDUE,
                                        ROUND(AVG(HOUR(FINAL_TAT)), 2) AS AVERAGE_TAT
                                FROM
                                    (SELECT
                                    t.TicketId,
                                        t.TicketNo,
                                        tt.ParentTicketTypeId,
                                        tt1.TicketType AS Cat,
                                        tt.TicketType AS SubCat,
                                        l.LocationName,
                                        p.Priority,
                                        pt.PRIORITY_TAT,
                                        ts.StatusName,
                                        UserGroupName,
                                        User.UserName AS TicketAssignee,
                                        u2.UserName AS TIcketRaisedby,
                                        ReportedDateTime,
                                        Description,
                                        CreatedDateTime,
                                        CloserDateTime,
                                        TAT,
                                        CASE
                                            WHEN TAT <> 0 THEN TAT
                                            WHEN TAT = 0 THEN TIMEDIFF(CURRENT_TIMESTAMP(), CreatedDateTime)
                                        END AS FINAL_TAT
                                FROM
                                    ASSET_INFINITY.Tickets t
                                INNER JOIN ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                                INNER JOIN ASSET_INFINITY.`Ticket Type` tt1 ON tt.ParentTicketTypeId = tt1.TickettypeID
                                INNER JOIN ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                                INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                                INNER JOIN ASSET_INFINITY.PRIORITY_TAT pt ON pt.PRIORITY = p.Priority
                                INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                                INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId
                                    AND u.UsergroupID = t.UsergroupID
                                INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                                INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                                INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                                WHERE
                                    DATE(t.CreatedDateTime) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)

                                        AND tt.ParentTicketTypeId BETWEEN 2 AND 52 ) C
                               group by  TicketAssignee,PRIORITY) B
                            ) C
						]]>
                    </content>
                </report>
                <report id="1" name="Priority Wise" executionType="SQL" attachmentType="EXCEL" environment="DUMP">
                    <content>
                        <![CDATA[

                        Select * from (SELECT DISTINCT
                            Priority,
                            TOTAL_TICKETS,
                            CLOSED,
                            HOLD,
                            OPEN,
                            CLOSED_OVERDUE,
                            OPEN_OVERDUE,
                            HOLD_OVERDUE,
                            CONCAT(ROUND((TOTAL_TICKETS - OPEN_OVERDUE - CLOSED_OVERDUE - HOLD_OVERDUE) / (TOTAL_TICKETS) * 100,
                                            2),
                                    '%') AS COMPLIANCE_SCORE,
                            AVERAGE_TAT
                        FROM
                            (SELECT
                            C.Priority,
                                    COUNT(TicketId) AS TOTAL_TICKETS,
                                    COUNT(CASE
                                        WHEN StatusName = 'CLOSED' THEN 1
                                        ELSE NULL
                                    END) AS CLOSED,
                                    COUNT(CASE
                                        WHEN statusName = 'HOLD' THEN 1
                                        ELSE NULL
                                    END) AS HOLD,
                                    COUNT(CASE
                                        WHEN StatusName = 'Assigned' THEN 1
                                        ELSE NULL
                                    END) AS OPEN,
                                    COUNT(CASE
                                        WHEN
                                            HOUR(TIMEDIFF(CloserDateTime, CreatedDateTime)) > PRIORITY_TAT
                                                AND StatusName = 'CLOSED'
                                        THEN
                                            1
                                        ELSE NULL
                                    END) AS CLOSED_OVERDUE,
                                    COUNT(CASE
                                        WHEN
                                            HOUR(TIMEDIFF(concat(current_date(),'05:00:00') , CreatedDateTime)) > PRIORITY_TAT
                                                AND (StatusName = 'Assigned')
                                        THEN
                                            1
                                        ELSE NULL
                                    END) AS Open_OVERDUE,
                                    COUNT(CASE
                                        WHEN
                                            HOUR(TIMEDIFF(concat(current_date(),'05:00:00'), CreatedDateTime)) > PRIORITY_TAT
                                                AND (StatusName = 'Hold')
                                        THEN
                                            1
                                        ELSE NULL
                                    END) AS Hold_OVERDUE,
                                    ROUND(AVG(HOUR(FINAL_TAT)), 2) AS AVERAGE_TAT
                            FROM
                                (SELECT
                                t.TicketId,
                                    t.TicketNo,
                                    tt.ParentTicketTypeId,
                                    tt1.TicketType AS Cat,
                                    tt.TicketType AS SubCat,
                                    l.LocationName,
                                    p.Priority,
                                    pt.PRIORITY_TAT,
                                    ts.StatusName,
                                    UserGroupName,
                                    User.UserName AS TicketAssignee,
                                    u2.UserName AS TIcketRaisedby,
                                    ReportedDateTime,
                                    Description,
                                    CreatedDateTime,
                                    CloserDateTime,
                                    TAT,
                                    CASE
                                        WHEN TAT <> 0 THEN TAT
                                        WHEN TAT = 0 THEN TIMEDIFF(CURRENT_TIMESTAMP(), CreatedDateTime)
                                    END AS FINAL_TAT
                            FROM
                                ASSET_INFINITY.Tickets t
                            INNER JOIN ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                            INNER JOIN ASSET_INFINITY.`Ticket Type` tt1 ON tt.ParentTicketTypeId = tt1.TickettypeID
                            INNER JOIN ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                            INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                            INNER JOIN ASSET_INFINITY.PRIORITY_TAT pt ON pt.PRIORITY = p.Priority
                            INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                            INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId
                                AND u.UsergroupID = t.UsergroupID
                            INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                            INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                            INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                            WHERE
                                DATE(t.CreatedDateTime) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
                                    AND tt.ParentTicketTypeId BETWEEN 2 AND 52 ) C
                           group by  PRIORITY) B)C;
						]]>
                    </content>
                </report>
                <report id="1" name="Cafe Wise" executionType="SQL" attachmentType="EXCEL" environment="DUMP">
                    <content>

                        <![CDATA[

                            Select * from(
                            Select CAFE_NAME,
                            TOTAL_TICKETS,
                            CLOSED,
                            HOLD,
                            OPEN,
                            CLOSED_OVERDUE,
                            OPEN_OVERDUE,
                            HOLD_OVERDUE,
                            CONCAT(ROUND((TOTAL_TICKETS-OPEN_OVERDUE-CLOSED_OVERDUE-HOLD_OVERDUE)/(TOTAL_TICKETS)*100,2),'%') AS COMPLIANCE_SCORE,
                            AVERAGE_TAT from (
                            Select A.LocationName AS CAFE_NAME ,
                            count(TicketId) AS TOTAL_TICKETS,
                            count(CASE WHEN StatusName = 'CLOSED' THEN 1 else NULL END) AS CLOSED,
                            count(CASE WHEN statusName = 'HOLD'THEN 1 else NULL END) AS HOLD,
                            count(CASE WHEN StatusName = 'Assigned' THEN 1 else NULL END) AS OPEN,
                            count(CASE WHEN hour(timediff(CloserDateTime,CreatedDateTime)) > PRIORITY_TAT AND StatusName = 'CLOSED' THEN 1 else NULL END) AS CLOSED_OVERDUE,

                            count(CASE WHEN hour(timediff(concat(current_date(),'05:00:00'),CreatedDateTime)) > PRIORITY_TAT AND (StatusName = 'Assigned') THEN 1 else NULL END) AS Open_OVERDUE,

                            count(CASE WHEN hour(timediff(concat(current_date(),'05:00:00'),CreatedDateTime)) > PRIORITY_TAT AND (StatusName = 'Hold') THEN 1 else NULL END) AS Hold_OVERDUE,
                            ROUND(AVG(HOUR(FINAL_TAT)),2) AS AVERAGE_TAT
                            from(
                            SELECT t.TicketId,
                            t.TicketNo,
                            tt.ParentTicketTypeId,
                            tt1.TicketType AS Cat,
                            tt.TicketType AS SubCat,
                            l.LocationName,
                            p.Priority,
                            pt.PRIORITY_TAT,
                            ts.StatusName,
                            UserGroupName,
                            User.UserName AS TicketAssignee,
                            u2.UserName AS TIcketRaisedby,
                            ReportedDateTime,
                            Description,
                            CreatedDateTime,
                            CloserDateTime,
                            TAT,
                            CASE
                            WHEN TAT <> 0 THEN TAT
                            WHEN TAT = 0 THEN  timediff(current_timestamp(),CreatedDateTime)
                            END AS FINAL_TAT
                            From
                            ASSET_INFINITY.Tickets t
                                    INNER JOIN
                                ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                                    INNER JOIN
                                ASSET_INFINITY.`Ticket Type` tt1 ON tt.ParentTicketTypeId = tt1.TickettypeID

                                INNER join ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                                INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                                INNER JOIN ASSET_INFINITY.PRIORITY_TAT pt On pt.PRIORITY = p.Priority
                                INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                                INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId AND u.UsergroupID = t.UsergroupID
                                INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                                INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                                INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                                WHERE t.CreatedDateTime BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)AND tt.ParentTicketTypeId BETWEEN 2 AND 52 )A group by CAFE_NAME)B) C;
						]]>


                    </content>
                </report>
                <report id="1" name="Agent Priority Wise" executionType="SQL" attachmentType="EXCEL" environment="DUMP">
                    <content>

                        <![CDATA[

                                Select * from (

                                SELECT DISTINCT
                                    AGENT,
                                    Priority,
                                    TOTAL_TICKETS,
                                    CLOSED,
                                    HOLD,
                                    OPEN,
                                    CLOSED_OVERDUE,
                                    OPEN_OVERDUE,
                                    HOLD_OVERDUE,
                                    CONCAT(ROUND((TOTAL_TICKETS - OPEN_OVERDUE - CLOSED_OVERDUE - HOLD_OVERDUE) / (TOTAL_TICKETS) * 100,
                                                    2),
                                            '%') AS COMPLIANCE_SCORE,
                                    AVERAGE_TAT
                                FROM
                                    (SELECT C.TicketAssignee AS AGENT,
                                    C.Priority,
                                            COUNT(TicketId) AS TOTAL_TICKETS,
                                            COUNT(CASE
                                                WHEN StatusName = 'CLOSED' THEN 1
                                                ELSE NULL
                                            END) AS CLOSED,
                                            COUNT(CASE
                                                WHEN statusName = 'HOLD' THEN 1
                                                ELSE NULL
                                            END) AS HOLD,
                                            COUNT(CASE
                                                WHEN StatusName = 'Assigned' THEN 1
                                                ELSE NULL
                                            END) AS OPEN,
                                            COUNT(CASE
                                                WHEN
                                                    HOUR(TIMEDIFF(CloserDateTime, CreatedDateTime)) > PRIORITY_TAT
                                                        AND StatusName = 'CLOSED'
                                                THEN
                                                    1
                                                ELSE NULL
                                            END) AS CLOSED_OVERDUE,
                                            COUNT(CASE
                                                WHEN
                                                    HOUR(TIMEDIFF(concat(current_date(),'05:00:00'), CreatedDateTime)) > PRIORITY_TAT
                                                        AND (StatusName = 'Assigned')
                                                THEN
                                                    1
                                                ELSE NULL
                                            END) AS Open_OVERDUE,
                                            COUNT(CASE
                                                WHEN
                                                    HOUR(TIMEDIFF(concat(current_date(),'05:00:00'), CreatedDateTime)) > PRIORITY_TAT
                                                        AND (StatusName = 'Hold')
                                                THEN
                                                    1
                                                ELSE NULL
                                            END) AS Hold_OVERDUE,
                                            ROUND(AVG(HOUR(FINAL_TAT)), 2) AS AVERAGE_TAT
                                    FROM
                                        (SELECT
                                        t.TicketId,
                                            t.TicketNo,
                                            tt.ParentTicketTypeId,
                                            tt1.TicketType AS Cat,
                                            tt.TicketType AS SubCat,
                                            l.LocationName,
                                            p.Priority,
                                            pt.PRIORITY_TAT,
                                            ts.StatusName,
                                            UserGroupName,
                                            User.UserName AS TicketAssignee,
                                            u2.UserName AS TIcketRaisedby,
                                            ReportedDateTime,
                                            Description,
                                            CreatedDateTime,
                                            CloserDateTime,
                                            TAT,
                                            CASE
                                                WHEN TAT <> 0 THEN TAT
                                                WHEN TAT = 0 THEN TIMEDIFF(CURRENT_TIMESTAMP(), CreatedDateTime)
                                            END AS FINAL_TAT
                                    FROM
                                        ASSET_INFINITY.Tickets t
                                    INNER JOIN ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                                    INNER JOIN ASSET_INFINITY.`Ticket Type` tt1 ON tt.ParentTicketTypeId = tt1.TickettypeID
                                    INNER JOIN ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                                    INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                                    INNER JOIN ASSET_INFINITY.PRIORITY_TAT pt ON pt.PRIORITY = p.Priority
                                    INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                                    INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId
                                        AND u.UsergroupID = t.UsergroupID
                                    INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                                    INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                                    INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                                    WHERE
                                        DATE(t.CreatedDateTime) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)

                                            AND tt.ParentTicketTypeId BETWEEN 2 AND 52 ) C
                                   group by  TicketAssignee,PRIORITY) B)C;
						]]>

                    </content>
                </report>
                <report id="1" name="Dump" executionType="SQL" attachmentType="EXCEL" environment="DUMP">
                    <content>
                        <![CDATA[
                            Select * from (
                            SELECT t.TicketId,
                            t.TicketNo,
                            tt.TicketType,
                            l.LocationName,
                            ts.StatusName,
                            UserGroupName,
                            User.UserName AS TicketAssignee,
                            u2.UserName AS TIcketRaisedby,
                            p.Priority,
                            ReportedDateTime,
                            Description,
                            CreatedDateTime,
                            CloserDateTime,
                            TAT
                            FROM
                                ASSET_INFINITY.Tickets t
                                    INNER JOIN
                                ASSET_INFINITY.`Ticket Type` tt ON tt.TickettypeID = t.TickettypeID
                                INNER join ASSET_INFINITY.Location l ON l.LOCATIONID = t.LOCATIONID
                                INNER JOIN ASSET_INFINITY.Priority p ON p.PriorityId = t.PriorityID
                                INNER JOIN ASSET_INFINITY.TicketStatus ts ON ts.TicketstatusID = t.TicketStatusID
                                INNER JOIN ASSET_INFINITY.UserGroupAssignee u ON u.UserId = t.UserGroupAsigneeId AND u.UsergroupID = t.UsergroupID
                                INNER JOIN ASSET_INFINITY.User ON User.Userid = u.userid
                                INNER JOIN ASSET_INFINITY.UserGroup us ON us.usergroupID = t.userGroupID
                                INNER JOIN ASSET_INFINITY.User u2 ON u2.userid = t.TicketReportedbyID
                                WHERE DATE(t.CreatedDateTime) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)

                                        AND tt.ParentTicketTypeId BETWEEN 2 AND 52)A
                             ;
						]]>
                    </content>
                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>