<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
 <categories>
<!--<category name="Daily Feedback Rating" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
                        <reports>
				 <report id="1" name="Daily Feedback Rating" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
					SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    COALESCE(cst.CUMULATIVE_SCORE, 'NA') CUMULATIVE_SCORE,
    COALESCE(cst.DINE_IN_SCORE, 'NA') DINE_IN_SCORE,
    COALESCE(cst.DELIVERY_SCORE, 'NA') DELIVERY_SCORE,
    COALESCE(cst.REQUESTED_FEEDBACK, 'NA') REQUESTED_FEEDBACK,
    COALESCE(cst.RECEIVED_FEEDBACK, 'NA') RECEIVED_FEEDBACK,
    COALESCE(cst.REQUESTED_DINE_IN_FEEDBACK, 'NA') REQUESTED_DINE_IN_FEEDBACK,
    COALESCE(cst.REQUESTED_DELIVERY_FEEDBACK, 'NA') REQUESTED_DELIVERY_FEEDBACK,
    COALESCE(cst.RECEIVED_DINE_IN_FEEDBACK, 'NA') RECEIVED_DINE_IN_FEEDBACK,
    COALESCE(cst.RECEIVED_DELIVERY_FEEDBACK, 'NA') RECEIVED_DELIVERY_FEEDBACK
FROM
    KETTLE.CONSOLIDATED_CSAT_SCORE cst,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    cst.UNIT_ID = ud.UNIT_ID
        AND CSAT_SCORE_TYPE = 'DAILY'
        AND IS_CURRENT_SCORE = 'Y'

				     ]]></content>
				</report>
				
			</reports>
</category>
<category name="Daily Feedback Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
                        <reports>
                                 <report id="1" name="Daily Feedback Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
                                        SELECT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    COALESCE(ud.COMMUNICATION_CHANNEL, 'mohit') COMMUNICATION_CHANNEL,
    CONCAT('Cumulative ',
            COALESCE(cst.CUMULATIVE_SCORE, 'NA'),
            ', Dine In ',
            COALESCE(cst.DINE_IN_SCORE, 'NA'),
            ', Delivery ',
            COALESCE(cst.DELIVERY_SCORE, 'NA')) OVERALL_SCORE,
    CONCAT('Cumulative ',
            COALESCE(cst1.CUMULATIVE_SCORE, 'NA'),
            '(',
            COALESCE(cst1.RECEIVED_FEEDBACK, '0'),
            ')',
            ', Dine In ',
            COALESCE(cst1.DINE_IN_SCORE, 'NA'),
            '(',
            COALESCE(cst1.RECEIVED_DINE_IN_FEEDBACK, '0'),
            ')',
            ', Delivery ',
            COALESCE(cst1.DELIVERY_SCORE, 'NA'),
            '(',
            COALESCE(cst1.RECEIVED_DELIVERY_FEEDBACK, '0'),
            ')') DAILY_SCORE,
    CONCAT(CASE
                WHEN
                    cst1.CAFE_AMBIENCE_ISSUES IS NOT NULL
                        AND cst1.CAFE_AMBIENCE_ISSUES > 0
                THEN
                    CONCAT('Ambience (',
                            cst1.CAFE_AMBIENCE_ISSUES,
                            '), ')
                ELSE ''
            END,
            CASE
                WHEN
                    cst1.CAFE_BEVERAGE_ISSUES IS NOT NULL
                        AND cst1.CAFE_BEVERAGE_ISSUES > 0
                THEN
                    CONCAT('Beverages(',
                            cst1.CAFE_BEVERAGE_ISSUES,
                            '), ')
                ELSE ''
            END,
            CASE
                WHEN
                    cst1.CAFE_FOOD_ISSUES IS NOT NULL
                        AND cst1.CAFE_FOOD_ISSUES > 0
                THEN
                    CONCAT('Food(', cst1.CAFE_FOOD_ISSUES, '), ')
                ELSE ''
            END,
            CASE
                WHEN
                    cst1.CAFE_SERVICE_ISSUES IS NOT NULL
                        AND cst1.CAFE_SERVICE_ISSUES > 0
                THEN
                    CONCAT('Service(',
                            cst1.CAFE_SERVICE_ISSUES,
                            ')')
                ELSE ''
            END) CAFE_ISSUES,
    CONCAT(CASE
                WHEN
                    cst1.DELIVERY_BEVERAGE_ISSUES IS NOT NULL
                        AND cst1.DELIVERY_BEVERAGE_ISSUES > 0
                THEN
                    CONCAT('Beverages(',
                            cst1.DELIVERY_BEVERAGE_ISSUES,
                            '), ')
                ELSE ''
            END,
            CASE
                WHEN
                    cst1.DELIVERY_FOOD_ISSUES IS NOT NULL
                        AND cst1.DELIVERY_FOOD_ISSUES > 0
                THEN
                    CONCAT('Food(',
                            cst1.DELIVERY_FOOD_ISSUES,
                            '), ')
                ELSE ''
            END,
            CASE
                WHEN
                    cst1.DELIVERY_SERVICE_ISSUES IS NOT NULL
                        AND cst1.DELIVERY_SERVICE_ISSUES > 0
                THEN
                    CONCAT('Service(',
                            cst1.DELIVERY_SERVICE_ISSUES,
                            ')')
                ELSE ''
            END) DELIVERY_ISSUES
FROM
    KETTLE.CONSOLIDATED_CSAT_SCORE cst
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON cst.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    KETTLE.CONSOLIDATED_CSAT_SCORE cst1 ON cst.UNIT_ID = cst1.UNIT_ID
WHERE
    cst.CSAT_SCORE_TYPE = 'CUMULATIVE'
        AND cst.IS_CURRENT_SCORE = 'Y'
        AND cst1.CSAT_SCORE_TYPE = 'DAILY'
        AND cst1.IS_CURRENT_SCORE = 'Y'
        AND ud.UNIT_STATUS = 'ACTIVE';







                                     ]]></content>
                                        <notifications>
                                                <notification>
                                                        <type>SLACK</type>
                                                        <notificationIdIndex>2</notificationIdIndex>
                                                        <notificationType>DIRECT</notificationType>
                                                        <channel>feedback_cafe</channel>
                                                        <indexes>
                                                                <messageIndex>1</messageIndex>
                                                                <messageIndex>3</messageIndex>
                                                                <messageIndex>4</messageIndex>
                                                                <messageIndex>5</messageIndex>
                                                                <messageIndex>6</messageIndex>
                                                        </indexes>
                                                </notification>
                                         </notifications>
                                </report>

                        </reports>
</category>-->
<category name="NPS Ranking Across Cafes"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
                        <reports>
                                 <report id="1" name="Unit Wise NPS Ranking" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
					

SELECT * FROM
(SELECT
    nps.BUSINESS_DATE,
	nps.NPS_CATEGORY,
    nps.UNIT_ID,
    nps.UNIT_NAME,
    ud.UNIT_REGION,
    UCASE(ed.EMP_NAME) AREA_MANAGER,
    nps.RANK_OF_THE_DAY RANK_OF_THE_QUARTER,
    nps.NPS_SCORE,
    nps.POSITIVE_PERCENTAGE,
    nps.NEGATIVE_PERCENTAGE,
    nps.TOTAL_POSITIVE,
    nps.TOTAL_NEGATIVE,
    nps.TOTAL_TICKET
FROM
    KETTLE.DAY_WISE_NPS_SCORE  nps
    LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = nps.UNIT_ID
    LEFT JOIN 
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ed.EMP_ID = ud.UNIT_MANAGER
WHERE
    BUSINESS_DATE = DATE_ADD(CURRENT_DATE, INTERVAL - 1 DAY)
	AND nps.NPS_CATEGORY = 'QTD_OVERALL'
ORDER BY RANK_OF_THE_DAY ) A
					]]></content>
                                </report>

                                 <report id="1" name="Unit Wise New Customer NPS Ranking" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
					

SELECT * FROM
(SELECT
    nps.BUSINESS_DATE,
	nps.NPS_CATEGORY,
    nps.UNIT_ID,
    nps.UNIT_NAME,
    ud.UNIT_REGION,
    UCASE(ed.EMP_NAME) AREA_MANAGER,
    nps.RANK_OF_THE_DAY RANK_OF_THE_QUARTER,
    nps.NPS_SCORE,
    nps.POSITIVE_PERCENTAGE,
    nps.NEGATIVE_PERCENTAGE,
    nps.TOTAL_POSITIVE,
    nps.TOTAL_NEGATIVE,
    nps.TOTAL_TICKET
FROM
    KETTLE.DAY_WISE_NPS_SCORE  nps
    LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = nps.UNIT_ID
    LEFT JOIN 
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ed.EMP_ID = ud.UNIT_MANAGER
WHERE
    BUSINESS_DATE = DATE_ADD(CURRENT_DATE, INTERVAL - 1 DAY)
	AND nps.NPS_CATEGORY = 'QTD_NEW_CUSTOMER'
ORDER BY RANK_OF_THE_DAY ) A

					]]></content>
                                </report>
								
                        </reports>
		</category>
 </categories>
</ReportCategories>
