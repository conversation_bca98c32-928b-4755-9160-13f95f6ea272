<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Maintenance Warehouse Report: Area Warehuse Stock" type="Automated"
                  accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="" schedule="">
			<reports>
				<report id="1" name="Area Warehuse Stock" executionType="SQL"
						GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
							SELECT
								ud.UNIT_ID,
								ud.UNIT_NAME,
								sd.SKU_ID,
								sd.SKU_NAME,
								sd.UNIT_OF_MEASURE,
								SUM(cdd.QUANTITY) QUANTITY
							FROM
								KETTLE_SCM.COST_DETAIL_DATA cdd
									INNER JOIN
								KETTLE_MASTER.UNIT_DETAIL ud ON cdd.UNIT_ID = ud.UNIT_ID
									INNER JOIN
								KETTLE_SCM.SKU_DEFINITION sd ON cdd.KEY_ID = sd.SKU_ID
							WHERE
								ud.UNIT_CATEGORY = 'WAREHOUSE'
									AND ud.UNIT_STATUS = 'ACTIVE'
									AND (sd.SKU_NAME LIKE 'MONK%'
									OR sd.SKU_NAME LIKE 'INDUCTION%')
									AND ud.UNIT_ID IN (26142 , 26147, 26149)
							GROUP BY 1 , 2 , 3 , 4 , 5;
        ]]>
					</content>
				</report>
				<report id="2" name="Area Warehuse Variance Summary" executionType="SQL"
						GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
							SELECT
								ud.UNIT_ID,
								ud.UNIT_NAME,
								ROUND(SUM(ABS(inv.VARIANCE_COST)), 2) VARIANCE_COST_MOD_MTD,
								ROUND(SUM(inv.VARIANCE_COST), 2) VARIANCE_COST_REAL_MTD
							FROM
								KETTLE_SCM.DAY_CLOSE_EVENT dce
									INNER JOIN
								KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
									INNER JOIN
								KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
									INNER JOIN
								KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
							WHERE
								DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE(), 1)
									AND dce.STATUS <> 'CANCELLED'
									AND ud.UNIT_ID IN (26149 , 26147, 26142)
									AND sd.SKU_STATUS = 'ACTIVE'
									AND sd.INVENTORY_LIST_ID <> '1'
							GROUP BY ud.UNIT_ID;
        				]]>
					</content>
				</report>
				<report id="3" name="Transfer Order From Warehouse" executionType="SQL"
						GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
							SELECT
								*
							FROM
								(SELECT
									t.TRANSFER_ORDER_ID,
										t.TRANSFER_ORDER_STATUS,
										ud.UNIT_NAME SENDING_UNIT_NAME,
										ud1.UNIT_NAME RECEIVING_UNIT_NAME,
										DATE(t.GENERATION_TIME) TO_DATE,
										sku.SKU_NAME,
										toi.TRANSFERRED_QUANTITY
								FROM
									KETTLE_SCM.TRANSFER_ORDER t
								INNER JOIN KETTLE_SCM.TRANSFER_ORDER_ITEM toi ON t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
								INNER JOIN KETTLE_SCM.SKU_DEFINITION sku ON sku.SKU_ID = toi.SKU_ID
								INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID
								INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
								INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
								INNER JOIN KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = t.GENERATION_UNIT_ID
								INNER JOIN KETTLE_SCM.UNIT_DETAIL ud1 ON ud1.UNIT_ID = t.GENERATED_FOR_UNIT_ID
								WHERE
									t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
										AND DATE(t.GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE(), 30) AND SUBDATE(CURRENT_DATE(), 1)
										AND GENERATION_UNIT_ID IN (26142 , 26147, 26149)) s;
        				]]>
					</content>
				</report>
				<report id="4" name="Warehouse GR" executionType="SQL"
						GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
							SELECT
								T.*
							FROM
								(SELECT
									g.GOODS_RECEIVED_ID,
										u.UNIT_NAME AS TRANSFERRING_UNIT,
										g.GENERATED_FOR_UNIT_ID AS RECEIVING_UNIT_ID,
										ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
										gi.SKU_NAME,
										ri.REQUESTED_QUANTITY,
										gi.TRANSFERRED_QUANTITY,
										gi.RECEIVED_QUANTITY,
										gi.UNIT_OF_MEASURE,
										sc.SUB_CATEGORY_NAME,
										r.GENERATION_TIME AS REQUESTING_TIME,
										t.GENERATION_TIME AS TRANSFER_TIME,
										g.LAST_UPDATE_TIME AS RECEIVING_TIME
								FROM
									KETTLE_SCM.GOODS_RECEIVED_ITEM gi
								INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
									AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
									AND DATE(g.LAST_UPDATE_TIME) BETWEEN SUBDATE(CURRENT_DATE(), 30) AND SUBDATE(CURRENT_DATE(), 1)
								INNER JOIN KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
								INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
								LEFT JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
								INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
								INNER JOIN KETTLE_MASTER.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
								INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
								INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
								LEFT JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
								INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
								LEFT JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID = ri.VENDOR_ID) T
							WHERE
								T.RECEIVING_UNIT_ID IN (26142 , 26147, 26149);
        				]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="Maintenance Warehouse Report: Area Warehouse Detail" type="Automated"
                        accessCode="Automated" id="5" fromEmail="<EMAIL>"
                        toEmails=""
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="6" name="Area Warehouse Detail" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
							SELECT
								ud.UNIT_NAME,
								dce.BUSINESS_DATE,
								dce.GENERATION_TIME,
								sd.SKU_NAME,
								pd.PRODUCT_NAME,
								cd.CATEGORY_CODE,
								scd.SUB_CATEGORY_CODE,
								sd.UNIT_OF_MEASURE,
								inv.PRICE,
								inv.OPENING,
								COALESCE(inv.TRANSFERRED, 0) AS TRANSFERRED,
								COALESCE(inv.RECEIVED, 0) AS RECEIVED,
								COALESCE(inv.CONSUMPTION, 0) AS CONSUMPTION,
								COALESCE(inv.BOOKED, 0) AS BOOKING,
								COALESCE(inv.WASTED, 0) AS WASTED,
								COALESCE(inv.EXPECTED_CLOSING, 0) AS SYSTEM_CLOSING,
								COALESCE(inv.ACTUAL_CLOSING, 0) AS ACTUAL_CLOSING,
								COALESCE(inv.VARIANCE, 0) AS VARIANCE,
								COALESCE(inv.VARIANCE_COST, 0) AS VARIANCE_COST
							FROM
								KETTLE_SCM.DAY_CLOSE_EVENT dce
									INNER JOIN
								KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
									INNER JOIN
								KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
									INNER JOIN
								KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
									INNER JOIN
								KETTLE_SCM.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
									INNER JOIN
								KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
									INNER JOIN
								KETTLE_SCM.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
							WHERE
								dce.BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1)
									AND dce.STATUS <> 'CANCELLED'
									AND dce.UNIT_ID IN (26149 , 26147, 26142)
							ORDER BY dce.UNIT_ID;
						]]>
					</content>
				</report>
				<report id="7" name="Transfer From Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
							SELECT
								*
							FROM
								(SELECT
									t.TRANSFER_ORDER_ID,
										toi.TRANSFER_ORDER_ITEM_ID,
										t.TRANSFER_ORDER_STATUS,
										t.GENERATION_UNIT_ID SENDING_UNIT_ID,
										ud.UNIT_NAME SENDING_UNIT_NAME,
										ud.COMPANY_ID SENDING_COMPANY,
										t.GENERATED_FOR_UNIT_ID RECEIVING_UNIT_ID,
										ud1.UNIT_NAME RECEIVING_UNIT_NAME,
										ud1.COMPANY_ID RECEIVING_COMPANY,
										DATE(t.GENERATION_TIME) TO_DATE,
										toi.SKU_ID,
										sku.SKU_NAME,
										toi.TRANSFERRED_QUANTITY,
										cd.CATEGORY_NAME,
										scd.SUB_CATEGORY_NAME,
										toi.CALCULATED_AMOUNT AS TAXABLE_AMOUNT,
										toi.TOTAL_TAX AS TOTAL_TAX,
										t.TRANSFER_TYPE
								FROM
									KETTLE_SCM.TRANSFER_ORDER t
								INNER JOIN KETTLE_SCM.TRANSFER_ORDER_ITEM toi ON t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
								INNER JOIN KETTLE_SCM.SKU_DEFINITION sku ON sku.SKU_ID = toi.SKU_ID
								INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID
								INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
								INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
								INNER JOIN KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = t.GENERATION_UNIT_ID
								INNER JOIN KETTLE_SCM.UNIT_DETAIL ud1 ON ud1.UNIT_ID = t.GENERATED_FOR_UNIT_ID
								WHERE
									t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
										AND DATE(t.GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE(), 30) AND SUBDATE(CURRENT_DATE(), 1)
										AND GENERATION_UNIT_ID IN (26142 , 26147, 26149)) s;
						]]>
					</content>
				</report>
				<report id="7" name="Warehouse GR" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
							SELECT
								T.*
							FROM
								(SELECT
									g.GOODS_RECEIVED_ID,
										gi.GOODS_RECEIVED_ITEM_ID,
										gi.TRANSFER_ORDER_ITEM_ID,
										gi.REQUEST_ORDER_ITEM_ID,
										u.UNIT_NAME AS TRANSFERRING_UNIT,
										g.GENERATED_FOR_UNIT_ID,
										ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
										ux.UNIT_REGION,
										p.PRODUCT_ID,
										TRIM(REPLACE(REPLACE(REPLACE(REPLACE(p.PRODUCT_NAME, ' ', ' '), ' ', ' '), '	', ' '), ',', ' ')) PRODUCT_NAME,
										gi.SKU_ID,
										gi.SKU_NAME,
										ri.REQUESTED_QUANTITY,
										gi.TRANSFERRED_QUANTITY,
										gi.RECEIVED_QUANTITY,
										(gi.RECEIVED_QUANTITY * gi.UNIT_PRICE) AS COST,
										gi.UNIT_OF_MEASURE,
										gi.UNIT_PRICE AS UNIT_PRICE,
										c.CATEGORY_NAME,
										sc.SUB_CATEGORY_NAME,
										r.GENERATION_TIME AS REQUESTING_TIME,
										t.GENERATION_TIME AS TRANSFER_TIME,
										g.LAST_UPDATE_TIME AS RECEIVING_TIME,
										vd.ENTITY_NAME
								FROM
									KETTLE_SCM.GOODS_RECEIVED_ITEM gi
								INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
									AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
									AND DATE(g.LAST_UPDATE_TIME) BETWEEN SUBDATE(CURRENT_DATE(), 30) AND SUBDATE(CURRENT_DATE(), 1)
								INNER JOIN KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
								INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
								LEFT JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
								INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
								INNER JOIN KETTLE_MASTER.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
								INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
								INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
								LEFT JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
								INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
								LEFT JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID = ri.VENDOR_ID) T
							WHERE
								T.GENERATED_FOR_UNIT_ID IN (26142 , 26147, 26149);
						]]>
					</content>
				</report>


			</reports>
		</category>
	</categories>
</ReportCategories>
