<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="All Regions" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    A.BUSINESS_DATE,
    'All Regions' AS TOTAL,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
    FROM
    (SELECT 
        m.BUSINESS_DATE,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        od.BUSINESS_DATE,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_APC,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) od
    ) m
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BUSINESS_DATE) n ON m.BUSINESS_DATE = n.BUSINESS_DATE
    LEFT OUTER JOIN (SELECT 
        a.BUSINESS_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BUSINESS_DATE) b ON a.BUSINESS_DATE = b.BUSINESS_DATE) p ON m.BUSINESS_DATE = p.BUSINESS_DATE) A
        LEFT OUTER JOIN
    (SELECT 
        m.BUSINESS_DATE,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) m
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BUSINESS_DATE) n ON m.BUSINESS_DATE = n.BUSINESS_DATE
    LEFT OUTER JOIN (SELECT 
        a.BUSINESS_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BUSINESS_DATE) b ON a.BUSINESS_DATE = b.BUSINESS_DATE) p ON m.BUSINESS_DATE = p.BUSINESS_DATE) B ON A.BUSINESS_DATE = B.BUSINESS_DATE
        ]]>
					</content>

				</report>
				<report id="1" name="Regional" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    A.BUSINESS_DATE,
    A.UNIT_REGION,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    (SELECT 
        m.BUSINESS_DATE,
            m.UNIT_REGION,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        od.BUSINESS_DATE,
            od.UNIT_REGION,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
		FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            ud.UNIT_REGION,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.UNIT_REGION) od
) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_REGION,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_REGION) a
    GROUP BY a.UNIT_REGION) n ON m.UNIT_REGION = n.UNIT_REGION
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_REGION,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.UNIT_REGION) a
    INNER JOIN (SELECT 
        ud.UNIT_REGION, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_REGION) b ON a.UNIT_REGION = b.UNIT_REGION) p ON m.UNIT_REGION = p.UNIT_REGION) A
        LEFT OUTER JOIN
    (SELECT 
        m.BUSINESS_DATE,
            m.UNIT_REGION,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            ud.UNIT_REGION,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_REGION) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_REGION,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_REGION) a
    GROUP BY a.UNIT_REGION) n ON m.UNIT_REGION = n.UNIT_REGION
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_REGION,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_REGION) a
    INNER JOIN (SELECT 
        ud.UNIT_REGION, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_REGION) b ON a.UNIT_REGION = b.UNIT_REGION) p ON m.UNIT_REGION = p.UNIT_REGION) B ON A.BUSINESS_DATE = B.BUSINESS_DATE
        AND A.UNIT_REGION = B.UNIT_REGION
GROUP BY A.UNIT_REGION

        ]]>
					</content>
				</report>
				<report id="1" name="Area Managers" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    A.BUSINESS_DATE,
    UCASE(ed.EMP_NAME) AREA_MANAGER,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    (SELECT 
        m.BUSINESS_DATE,
            m.UNIT_MANAGER,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        od.BUSINESS_DATE,
            od.UNIT_MANAGER,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
		FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.UNIT_MANAGER) od
) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_MANAGER,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_MANAGER) a
    GROUP BY a.UNIT_MANAGER) n ON m.UNIT_MANAGER = n.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.UNIT_MANAGER) a
    INNER JOIN (SELECT 
        ud.UNIT_MANAGER, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_MANAGER) b ON a.UNIT_MANAGER = b.UNIT_MANAGER) p ON m.UNIT_MANAGER = p.UNIT_MANAGER) A
        LEFT OUTER JOIN
    (SELECT 
        m.BUSINESS_DATE,
            m.UNIT_MANAGER,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BUSINESS_DATE,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_MANAGER) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_MANAGER,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_MANAGER) a
    GROUP BY a.UNIT_MANAGER) n ON m.UNIT_MANAGER = n.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_MANAGER) a
    INNER JOIN (SELECT 
        ud.UNIT_MANAGER, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_MANAGER) b ON a.UNIT_MANAGER = b.UNIT_MANAGER) p ON m.UNIT_MANAGER = p.UNIT_MANAGER) B ON A.BUSINESS_DATE = B.BUSINESS_DATE
        AND A.UNIT_MANAGER = B.UNIT_MANAGER
        INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ed
        on A.UNIT_MANAGER = ed.EMP_ID
GROUP BY A.UNIT_MANAGER

        ]]>
					</content>
				</report>
				<report id="2" name="Cafes" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    A.COLD_PENETRATION,
    B.LWSD_COLD_PENETRATION,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

                                                ]]>
					</content>

				</report>
				
				<report id="4" name="New Product Total Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
        AND oi.PRODUCT_ID IN (710,720,730,770,865,1026,1027,1048,1057,1060,1061,1062,1063,1064,1065,1066,1067,1068,1022)
GROUP BY BUSINESS_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION
ORDER BY BUSINESS_DATE , pd.PRODUCT_NAME , oi.DIMENSION
                                                ]]>
					</content>

				</report>
				<report id="5" name="Gift Card Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
                        ud.UNIT_NAME,
            oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES,
                        CONCAT(COALESCE(ud.UNIT_NAME, 'NA'),' - ',case when oi.PRODUCT_ID = 1026 then 'GC:500' else 'GC:1000' end,' - ',COALESCE( SUM(oi.QUANTITY), 'NA'),' - ',COALESCE(SUM(oi.PRICE * oi.QUANTITY), 'NA')) GIFT_CARD_SUMMARY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY ud.UNIT_NAME , oi.PRODUCT_ID

]]>
					</content>
					<!--<notifications>
                                                                                                                                                                                                                                            <notification>
                                                                        <type>SLACK</type>
                                                                        <notificationType>DIRECT</notificationType>
                                                                        <channel>sales_rockers</channel>
                                                                        <indexes>
                                                                                        <messageIndex>4</messageIndex>
                                                                        </indexes>
                                                        </notification>
                                         </notifications>-->
				</report>
				<report id="5" name="Region Wise Gift Card Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
                        ud.UNIT_REGION,
            oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES,
                        CONCAT(COALESCE(ud.UNIT_REGION, 'NA'),' - ',case when oi.PRODUCT_ID = 1026 then 'GC:500' else 'GC:1000' end,' - ',COALESCE( SUM(oi.QUANTITY), 'NA'),' - ',COALESCE(SUM(oi.PRICE * oi.QUANTITY), 'NA')) GIFT_CARD_SUMMARY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY ud.UNIT_REGION , oi.PRODUCT_ID

]]>
					</content>
					<!--<notifications>
                                                                                                                                                                                                                                            <notification>
                                                                        <type>SLACK</type>
                                                                        <notificationType>DIRECT</notificationType>
                                                                        <channel>sales_rockers</channel>
                                                                        <indexes>
                                                                                        <messageIndex>4</messageIndex>
                                                                        </indexes>
                                                        </notification>
                                         </notifications>-->
				</report>
				<report id="5" name="Product Wise Gift Card Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
                        oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES,
                        CONCAT(oi.PRODUCT_NAME,' - ',COALESCE( SUM(oi.QUANTITY), 'NA'),' - ',COALESCE(SUM(oi.PRICE * oi.QUANTITY), 'NA')) GIFT_CARD_SUMMARY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.PRODUCT_NAME
]]>
					</content>
					<!--<notifications>
                                                                                                                                                                                                                                            <notification>
                                                                        <type>SLACK</type>
                                                                        <notificationType>DIRECT</notificationType>
                                                                        <channel>sales_rockers</channel>
                                                                        <indexes>
                                                                                        <messageIndex>3</messageIndex>
                                                                        </indexes>
                                                        </notification>
                                         </notifications>-->
				</report>
				<report id="9" name="Daily Web App Orders" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ORDER_SOURCE,
    SUM(CASE
        WHEN PAYMENT_MODE_ID = 1 THEN 1
        ELSE 0
    END) CASH_ORDERS,
    SUM(CASE
        WHEN PAYMENT_MODE_ID = 12 THEN 1
        ELSE 0
    END) ONLINE_PAYMENT_ORDERS
FROM
    ORDER_DETAIL od,
    ORDER_SETTLEMENT os
WHERE
    od.ORDER_ID = os.ORDER_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND ORDER_SOURCE_ID LIKE 'NEO-%'
        AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
GROUP BY ORDER_SOURCE

]]>
					</content>
				</report>

			</reports>
		</category>
		<category name="GK2 Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26005
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26005
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26005
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Pacific Mall Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26012
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26012
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26012
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Logix City Center Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26016
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26016
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26016
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Chaayos Dhaula Kuan Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26017
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26017
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26017
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Chaayos Indirapuram Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26019
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26019
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26019
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Chaayos Dwarka Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26018
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                 AND od.UNIT_ID = 26018
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
         AND od.UNIT_ID = 26018
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>

				</report>
			</reports>
		</category>
		<!--<category name="Intra Day Dispatch and Delivery Delay Details"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Intra Day Dispatch and Delivery Delay Details" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    od.GENERATED_ORDER_ID GENERATED_ORDER_ID,
    cai.ADDRESS_LINE_1 ADDRESS_LINE_1,
    cai.ADDRESS_LINE_2 ADDRESS_LINE_2,
    cai.LOCALITY LOCALITY,
    od.ORDER_ID ORDER_ID,
    ud.UNIT_NAME UNIT_NAME,
    od.SETTLED_AMOUNT ORDER_VALUE,
    ci.FIRST_NAME CUSTOMER_NAME,
    ci.CONTACT_NUMBER CONTACT_NUMBER,
    oen.ENTRY_TYPE DELAY_TYPE,
    DATE_FORMAT(od.BILLING_SERVER_TIME, '%d %b %Y %T') CREATED_AT,
    CASE
        WHEN ose.UPDATE_TIME IS NULL THEN - 1
        ELSE TIME_TO_SEC(TIMEDIFF(ose.UPDATE_TIME, od.BILLING_SERVER_TIME)) / 60
    END DELIVERY_ELAPSED_TIME
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE.CUSTOMER_ADDRESS_INFO cai ON cai.ADDRESS_ID = od.DELIVERY_ADDRESS
        INNER JOIN
    KETTLE.ORDER_EMAIL_NOTIFICATION oen ON oen.ORDER_ID = od.ORDER_ID
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT JOIN
    KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TRANSITION_STATUS = 'SUCCESS'
        AND (ose.FROM_STATUS = 'SETTLED'
        AND ose.TO_STATUS = 'DELIVERED')
WHERE
    oen.ENTRY_TYPE IN ('DISPATCH_DELAY' , 'DELIVERY_DELAY')
        AND oen.IS_EMAIL_DELIVERED = 'Y'
        AND oen.EXECUTION_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND od.DELIVERY_PARTNER_ID = 8
ORDER BY od.ORDER_ID DESC;

                                     ]]>
					</content>
				</report>

			</reports>
		</category>-->
	</categories>
</ReportCategories>

