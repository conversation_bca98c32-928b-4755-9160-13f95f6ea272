<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Petty cash Reports" type="petty cash"
			accessCode="Finance">
			<reports>
				<report name="MTD Voucher Details Report"
						executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT
								*
							FROM
								(SELECT
									v.VOUCHER_ID,
										v.GENERATED_VOUCHER_ID,
										u.UNIT_NAME,
										u.UNIT_ID,
										v.CURRENT_STATUS,
										v.BUSINESS_DATE AS 'DATE OF EXPENSES',
										vs.ACTION_TIME AS 'DATE OF APPROVAL BY AM',
										vs1.ACTION_TIME AS 'DATE OF APPROVAL BY FINANCE',
										e.EMP_NAME AS 'CREATED BY',
										v.EXPENSE_TYPE AS 'EXPENSES HEAD',
										v.BUDGET_CATEGORY,
										v.EXPENSE_AMOUNT,
										v.LAST_UPDATE_TIME,
										vs2.TO_STATUS AS 'REJECTED BY',
										vs2.ACTION_TIME AS 'REJECTION TIME',
										vs2.ACTION_COMMENT AS 'REJECTION REASON',
										c.CLAIM_ID,
										c.CURRENT_STATUS AS 'CLAIM_STATUS',
										c.HAPPAY_ID,
										c.CREATION_TIME AS 'CLAIM_TIME',
										c.REQUESTED_AMOUNT,
										c.APPROVED_AMOUNT,
										e1.EMP_NAME AS 'CLAIMED BY',
										e2.EMP_NAME AS 'CLAIM APPROVED BY',
										cl.UPDATE_TIME AS 'CLAIM_APPROVE_TIME',
										e3.EMP_NAME AS 'CLAIM REJECTED BY',
										cl1.UPDATE_TIME AS 'CLAIM_REJECT_TIME',
										cl1.COMMENTS AS 'CLAIM REJECT COMMENT'
								FROM
									KETTLE.VOUCHER_DATA v
								INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO
									AND DATE(v.BUSINESS_DATE) >= DATE(CONCAT(YEAR(CURRENT_DATE()), '-', MONTH(CURRENT_DATE()), '-01'))
									AND v.ACCOUNT_TYPE = 'UNIT'
									AND v.EXPENSE_CATEGORY = 'PETTY_CASH'
								LEFT JOIN KETTLE.VOUCHER_STATUS_DATA vs ON vs.VOUCHER_ID = v.VOUCHER_ID
									AND vs.TO_STATUS = 'FINANCE_PENDING'
									AND vs.TRANSITION_STATUS = 'SUCCESS'
								LEFT JOIN KETTLE.VOUCHER_STATUS_DATA vs1 ON vs1.VOUCHER_ID = v.VOUCHER_ID
									AND vs1.TO_STATUS = 'APPROVED'
									AND vs1.TRANSITION_STATUS = 'SUCCESS'
								LEFT JOIN KETTLE.VOUCHER_STATUS_DATA vs2 ON vs2.VOUCHER_ID = v.VOUCHER_ID
									AND vs2.TO_STATUS IN ('PENDING_REJECTION_AM' , 'PENDING_REJECTION_FINANCE')
									AND vs2.TRANSITION_STATUS = 'SUCCESS'
								INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e ON e.EMP_ID = v.ISSUED_BY
								LEFT JOIN KETTLE.CLAIM_DETAIL c ON c.CLAIM_ID = v.CLAIM_ID
								INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e1 ON e1.EMP_ID = c.CREATED_BY
								LEFT JOIN KETTLE.CLAIM_LOG_DETAIL cl ON cl.CLAIM_ID = c.CLAIM_ID
									AND cl.TO_STATUS = 'APPROVED'
								LEFT JOIN KETTLE.CLAIM_LOG_DETAIL cl1 ON cl1.CLAIM_ID = c.CLAIM_ID
									AND cl1.TO_STATUS = 'REJECTED'
								LEFT JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e2 ON e2.EMP_ID = cl.UPDATED_BY
								LEFT JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e3 ON e2.EMP_ID = cl1.UPDATED_BY) T;
							]]>
					</content>
				</report>
				<report name="Vouchers pending from last month or before"
						executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT
								*
							FROM
								(SELECT
									v.VOUCHER_ID,
										v.GENERATED_VOUCHER_ID,
										u.UNIT_NAME,
										u.UNIT_ID,
										v.CURRENT_STATUS,
										v.BUSINESS_DATE AS 'DATE OF EXPENSES',
										vs.ACTION_TIME AS 'DATE OF APPROVAL BY AM',
										vs1.ACTION_TIME AS 'DATE OF APPROVAL BY FINANCE',
										e.EMP_NAME AS 'CREATED BY',
										v.EXPENSE_TYPE AS 'EXPENSES HEAD',
										v.BUDGET_CATEGORY,
										v.EXPENSE_AMOUNT,
										v.LAST_UPDATE_TIME,
										vs2.TO_STATUS AS 'REJECTED BY',
										vs2.ACTION_TIME AS 'REJECTION TIME',
										vs2.ACTION_COMMENT AS 'REJECTION REASON',
										c.CLAIM_ID,
										c.CURRENT_STATUS AS 'CLAIM_STATUS',
										c.HAPPAY_ID,
										c.CREATION_TIME AS 'CLAIM_TIME',
										c.REQUESTED_AMOUNT,
										c.APPROVED_AMOUNT,
										e1.EMP_NAME AS 'CLAIMED BY',
										e2.EMP_NAME AS 'CLAIM APPROVED BY',
										cl.UPDATE_TIME AS 'CLAIM_APPROVE_TIME',
										e3.EMP_NAME AS 'CLAIM REJECTED BY',
										cl1.UPDATE_TIME AS 'CLAIM_REJECT_TIME',
										cl1.COMMENTS AS 'CLAIM REJECT COMMENT'
								FROM
									KETTLE.VOUCHER_DATA v
								INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO
									AND v.CURRENT_STATUS <> 'SETTLED'
									AND DATE(v.BUSINESS_DATE) < DATE(CONCAT(YEAR(CURRENT_DATE()), '-', MONTH(CURRENT_DATE()), '-01'))
									AND v.ACCOUNT_TYPE = 'UNIT'
									AND v.EXPENSE_CATEGORY = 'PETTY_CASH'
								LEFT JOIN KETTLE.VOUCHER_STATUS_DATA vs ON vs.VOUCHER_ID = v.VOUCHER_ID
									AND vs.TO_STATUS = 'FINANCE_PENDING'
									AND vs.TRANSITION_STATUS = 'SUCCESS'
								LEFT JOIN KETTLE.VOUCHER_STATUS_DATA vs1 ON vs1.VOUCHER_ID = v.VOUCHER_ID
									AND vs1.TO_STATUS = 'APPROVED'
									AND vs1.TRANSITION_STATUS = 'SUCCESS'
								LEFT JOIN KETTLE.VOUCHER_STATUS_DATA vs2 ON vs2.VOUCHER_ID = v.VOUCHER_ID
									AND vs2.TO_STATUS IN ('PENDING_REJECTION_AM' , 'PENDING_REJECTION_FINANCE')
									AND vs2.TRANSITION_STATUS = 'SUCCESS'
								INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e ON e.EMP_ID = v.ISSUED_BY
								LEFT JOIN KETTLE.CLAIM_DETAIL c ON c.CLAIM_ID = v.CLAIM_ID
								INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e1 ON e1.EMP_ID = c.CREATED_BY
								LEFT JOIN KETTLE.CLAIM_LOG_DETAIL cl ON cl.CLAIM_ID = c.CLAIM_ID
									AND cl.TO_STATUS = 'APPROVED'
								LEFT JOIN KETTLE.CLAIM_LOG_DETAIL cl1 ON cl1.CLAIM_ID = c.CLAIM_ID
									AND cl1.TO_STATUS = 'REJECTED'
								INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e2 ON e2.EMP_ID = cl.UPDATED_BY
								LEFT JOIN KETTLE_MASTER.EMPLOYEE_DETAIL e3 ON e2.EMP_ID = cl1.UPDATED_BY
								WHERE
									c.CURRENT_STATUS <> 'SETTLED') T;
							]]>
					</content>
				</report>
				<report name="Cafe wise expenses for date range"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT * FROM (SELECT 
								v.VOUCHER_ID,
								u.UNIT_NAME,
								u.UNIT_ID,
								v.BUSINESS_DATE as 'DATE OF EXPENSES',
								vs.ACTION_TIME as 'DATE OF APPROVAL BY AM',
								vs1.ACTION_TIME as 'DATE OF APPROVAL BY FINANCE',
								e.EMP_NAME as 'CREATED BY',
								v.EXPENSE_TYPE as 'EXPENSES HEAD',
								v.BUDGET_CATEGORY,
								v.EXPENSE_AMOUNT,
								v.CURRENT_STATUS,
								vs2.ACTION_COMMENT as 'REJECTION REASON'
							FROM
								KETTLE.VOUCHER_DATA v
									INNER JOIN
								KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO
									AND DATE(v.BUSINESS_DATE) = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
									AND v.ACCOUNT_TYPE = 'UNIT'
									AND v.EXPENSE_CATEGORY = 'PETTY_CASH'
									LEFT JOIN
								KETTLE.VOUCHER_STATUS_DATA vs ON vs.VOUCHER_ID = v.VOUCHER_ID
									AND vs.TO_STATUS = 'FINANCE_PENDING'
									LEFT JOIN
								KETTLE.VOUCHER_STATUS_DATA vs1 ON vs1.VOUCHER_ID = v.VOUCHER_ID
									AND vs1.TO_STATUS = 'APPROVED'
									LEFT JOIN
								KETTLE.VOUCHER_STATUS_DATA vs2 ON vs2.VOUCHER_ID = v.VOUCHER_ID
									AND vs2.TO_STATUS IN ('PENDING_REJECTION_AM' , 'PENDING_REJECTION_FINANCE')
									INNER JOIN
								KETTLE_MASTER.EMPLOYEE_DETAIL e ON e.EMP_ID = v.ISSUED_BY) T;
							]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Cafe wise expenses per header for date range"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT * FROM (SELECT 
								u.UNIT_NAME,
								u.UNIT_ID,
								v.BUSINESS_DATE AS EXPENSE_DATE,
								v.EXPENSE_TYPE as 'EXPENSES HEAD',
								v.BUDGET_CATEGORY,
								SUM(v.EXPENSE_AMOUNT) AS EXPENSE_AMOUNT
							FROM
								KETTLE.VOUCHER_DATA v
									INNER JOIN
								KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO
									AND DATE(v.BUSINESS_DATE) = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
									AND v.ACCOUNT_TYPE = 'UNIT'
									AND v.EXPENSE_CATEGORY = 'PETTY_CASH'
									GROUP BY v.ACCOUNT_NO, v.BUSINESS_DATE, v.BUDGET_CATEGORY) T;
							]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Unsettled claims till date"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT 
								c.CLAIM_ID,
								c.CREATION_TIME,
								u.UNIT_NAME,
								u.UNIT_ID,
								c.HAPPAY_ID,
								c.REQUESTED_AMOUNT,
								c.APPROVED_AMOUNT,
								c.CURRENT_STATUS
							FROM
								KETTLE.CLAIM_DETAIL c
									INNER JOIN
								KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = c.UNIT_ID
									AND c.CURRENT_STATUS <> 'SETTLED'
									AND CLAIM_TYPE = 'HAPPAY';
							]]>
					</content>
				</report>
				<report name="Voucher and Expense Detail for date range"
						executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT * FROM (SELECT
								v.GENERATED_VOUCHER_ID,
								u.UNIT_ID,
								u.UNIT_NAME,
								v.BUSINESS_DATE,
								v.EXPENSE_TYPE,
								v.EXPENSE_DETAIL,
								v.CURRENT_STATUS,
								v.EXPENSE_AMOUNT,
								v.EXPENSE_TYPE AS 'EXPENSES HEAD',
								v.BUDGET_CATEGORY,
								v.CLAIM_ID,
								c.HAPPAY_ID
							FROM
								KETTLE_DUMP.VOUCHER_DATA v
									INNER JOIN
								KETTLE_DUMP.CLAIM_DETAIL c ON c.CLAIM_ID = v.CLAIM_ID
									AND DATE(c.CREATION_TIME) >= :startDate
									AND DATE(c.CREATION_TIME) <= :endDate
									INNER JOIN
								KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO) T;
							]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							   dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date"
							   dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Cafe wise finance pending expenses till date"
						executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT * FROM (SELECT
								v.VOUCHER_ID,
								u.UNIT_NAME,
								u.UNIT_ID,
								v.BUSINESS_DATE as 'DATE OF EXPENSES',
								vs.ACTION_TIME as 'DATE OF APPROVAL BY AM',
								vs1.ACTION_TIME as 'DATE OF APPROVAL BY FINANCE',
								e.EMP_NAME as 'CREATED BY',
								v.EXPENSE_TYPE as 'EXPENSES HEAD',
								v.BUDGET_CATEGORY,
								v.EXPENSE_AMOUNT,
								v.CURRENT_STATUS,
								vs2.ACTION_COMMENT as 'REJECTION REASON'
							FROM
								KETTLE.VOUCHER_DATA v
									INNER JOIN
								KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO
									AND DATE(v.BUSINESS_DATE) <= SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
									AND v.ACCOUNT_TYPE = 'UNIT'
									AND v.EXPENSE_CATEGORY = 'PETTY_CASH'
                                    AND v.CURRENT_STATUS = 'FINANCE_PENDING'
									LEFT JOIN
								KETTLE.VOUCHER_STATUS_DATA vs ON vs.VOUCHER_ID = v.VOUCHER_ID
									AND vs.TO_STATUS = 'FINANCE_PENDING'
									LEFT JOIN
								KETTLE.VOUCHER_STATUS_DATA vs1 ON vs1.VOUCHER_ID = v.VOUCHER_ID
									AND vs1.TO_STATUS = 'APPROVED'
									LEFT JOIN
								KETTLE.VOUCHER_STATUS_DATA vs2 ON vs2.VOUCHER_ID = v.VOUCHER_ID
									AND vs2.TO_STATUS IN ('PENDING_REJECTION_AM' , 'PENDING_REJECTION_FINANCE')
									INNER JOIN
								KETTLE_MASTER.EMPLOYEE_DETAIL e ON e.EMP_ID = v.ISSUED_BY) T
							]]>
					</content>
				</report>
				<report name="Finance approved expenses cafe wise"
						executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							SELECT * FROM (SELECT
								v.VOUCHER_ID,
								u.UNIT_NAME,
								u.UNIT_ID,
								v.BUSINESS_DATE as 'DATE OF EXPENSES',
								vs1.ACTION_TIME as 'DATE OF APPROVAL BY FINANCE',
								e.EMP_NAME as 'CREATED BY',
								v.EXPENSE_TYPE as 'EXPENSES HEAD',
								v.BUDGET_CATEGORY,
								v.EXPENSE_AMOUNT,
								v.CURRENT_STATUS
							FROM
								KETTLE_DUMP.VOUCHER_DATA v
									INNER JOIN
								KETTLE_DUMP.VOUCHER_STATUS_DATA vs1 ON vs1.VOUCHER_ID = v.VOUCHER_ID
									AND vs1.TO_STATUS = 'APPROVED'
                                    AND DATE(vs1.ACTION_TIME) = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
									INNER JOIN
								KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID = v.ACCOUNT_NO
									AND v.ACCOUNT_TYPE = 'UNIT'
									AND v.EXPENSE_CATEGORY = 'PETTY_CASH'
									LEFT JOIN
								KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL e ON e.EMP_ID = v.ISSUED_BY) T;
							]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>