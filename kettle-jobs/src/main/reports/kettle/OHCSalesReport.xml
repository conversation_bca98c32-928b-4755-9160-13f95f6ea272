<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="ChaayosOHCDailySalesReport" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="1" name="All Regions" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    'Chaayos' AS STORE_ID,
    (CASE
        WHEN od.ORDER_STATUS = 'SETTLED' THEN 'Sale'
        ELSE 'Void'
    END) AS BILL_TYPE,
    od.BUSINESS_DATE AS BILL_DATE,
    od.GENERATED_ORDER_ID AS BILL_NO,
    od.SETTLED_AMOUNT AS BILL_AMOUNT,
    od.TAXABLE_AMOUNT AS BASE_SALE_AMT,
    COALESCE(od.DISCOUNT_AMOUNT, 0) AS BILL_DISC_AMT,
    od.TOTAL_TAX AS TOTAL_TAX_AMT,
    TRUNCATE(od.TOTAL_TAX / 2, 2) AS CGST,
    TRUNCATE(od.TOTAL_TAX / 2, 2) AS SGST,
    '0.00' AS IGST,
    '0.00' AS OTHER_TAXES,
    '0.00' AS SERV_CHARGE_AMT,
    od.ROUND_OFF_AMOUNT AS ROUNDING_AMT
FROM
    KETTLE.ORDER_DETAIL od
        LEFT OUTER JOIN
    (SELECT 
        od.ORDER_ID, SUM(oi.QUANTITY * oi.PRICE) GC_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_ITEM oi
    WHERE
        od.BUSINESS_DATE = DATE_ADD(CURRENT_DATE(), INTERVAL - 1 DAY)
            AND od.UNIT_ID = 26036
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.TAX_CODE = 'GIFT_CARD'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_STATUS <> 'CANCELLED'
    GROUP BY od.ORDER_ID) a ON od.ORDER_ID = a.ORDER_ID
WHERE
    od.ORDER_ID IS NOT NULL
        AND od.UNIT_ID = 26036
        AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE = DATE_ADD(CURRENT_DATE(),
        INTERVAL - 1 DAY)
        AND (od.SETTLED_AMOUNT <> 0
        AND (od.SETTLED_AMOUNT - COALESCE(a.GC_AMOUNT, 0)) > 0)
ORDER BY od.ORDER_ID

        ]]>
					</content>

				</report>
				                        </reports>
		</category>
	</categories>
</ReportCategories>
