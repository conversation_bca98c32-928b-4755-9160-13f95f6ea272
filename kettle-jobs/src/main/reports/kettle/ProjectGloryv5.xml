<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="Project Glory 2.0" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>

                                
                                <report id="10" name="CHAAYOS" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
SELECT 
'Overall' AS 'Chaayos',
NEW_CUSTOMER_APC,
EXISTING_CUSTOMER_APC,
OVERALL_CUSTOMER_APC,
CONCAT(NEW_CORE_ITEM_INCIDENCE,"%") NEW_CORE_ITEM_INCIDENCE,
CONCAT(EXISTING_CORE_ITEM_INCIDENCE, "%") EXISTING_CORE_ITEM_INCIDENCE,
CONCAT(OVERALL_CORE_ITEM_INCIDENCE, "%") OVERALL_CORE_ITEM_INCIDENCE,
NEW_CUSTOMER_TICKETS,
(OVERALL_CUSTOMER_TICKETS - NEW_CUSTOMER_TICKETS) EXISTING_CUSTOMER_TICKETS,
OVERALL_CUSTOMER_TICKETS,
CONCAT(ROUND((BEV_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") BOT,
CONCAT(ROUND((FOOD_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") FOT

FROM(
SELECT 
    'Overall' AS Chaayos,
    A1.BIZ_DATE,
    ROUND((DINE_SALE / total_Tickets), 0) NEW_CUSTOMER_APC,
    total_Tickets NEW_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence /QUANTITY) * 100, 0) AS NEW_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1) AS A
   group by 1) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1)AS B
    GROUP BY 1)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE) Z
    
    LEFT OUTER JOIN

(SELECT 
    'Overall' AS Chaayos,
    A1.BIZ_DATE,
    ROUND((DINE_SALE / total_Tickets), 0) EXISTING_CUSTOMER_APC,
    total_Tickets EXISTING_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence /QUANTITY) * 100, 0) AS EXISTING_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME < ADDTIME(UTC_TIMESTAMP, '05:30:00')
            #AND IS_NEW_CUSTOMER <> 'Y'
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1) AS A
   group by 1) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME < ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1)AS B
    GROUP BY 1)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE) Z1 ON Z1.BIZ_DATE = Z.BIZ_DATE
    
    LEFT OUTER JOIN

(SELECT 
    'Overall' AS Overall_Chaayos,
    A1.BIZ_DATE,
    ROUND((DINE_SALE / total_Tickets), 0) OVERALL_CUSTOMER_APC,
    total_Tickets OVERALL_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence /QUANTITY) * 100, 0) AS OVERALL_CORE_ITEM_INCIDENCE,
    QUANTITY
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1) AS A
   group by 1) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1)AS B
    GROUP BY 1)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE) Z2 ON Z2.BIZ_DATE = Z.BIZ_DATE


            LEFT OUTER JOIN
(SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY_TKT,
            SUM(CASE
                WHEN PTYPE IN ('7') THEN 1
                ELSE 0
            END) FOOD_ONLY_TKT
    FROM
        (SELECT 
        od1.ORDER_ID,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od1.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND (oi1.IS_COMPLIMENTARY = 'N'
            OR oi1.IS_COMPLIMENTARY IS NULL)
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1) AS B
    GROUP BY 1) Z3 ON Z3.BIZ_DATE = Z.BIZ_DATE;              
            
                                
                                       ]]>
                                        </content>
                                </report>
                                
                                <report id="10" name="REGIONAL LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[


SELECT 

Z.UNIT_REGION_INFO REGION,
NEW_CUSTOMER_APC,
EXISTING_CUSTOMER_APC,
OVERALL_CUSTOMER_APC,
CONCAT(NEW_CORE_ITEM_INCIDENCE,"%") NEW_CORE_ITEM_INCIDENCE,
CONCAT(EXISTING_CORE_ITEM_INCIDENCE, "%") EXISTING_CORE_ITEM_INCIDENCE,
CONCAT(OVERALL_CORE_ITEM_INCIDENCE, "%") OVERALL_CORE_ITEM_INCIDENCE,
NEW_CUSTOMER_TICKETS,
(OVERALL_CUSTOMER_TICKETS - NEW_CUSTOMER_TICKETS) EXISTING_CUSTOMER_TICKETS,
OVERALL_CUSTOMER_TICKETS,
CONCAT(ROUND((BEV_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") BOT,
CONCAT(ROUND((FOOD_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") FOT


FROM(
SELECT 
    'Overall' AS Chaayos,
    A1.BIZ_DATE,
    A1.UNIT_REGION_INFO,
    ROUND((DINE_SALE / total_Tickets), 0) NEW_CUSTOMER_APC,
    total_Tickets NEW_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence /QUANTITY) * 100, 0) AS NEW_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE WHEN A.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN A.UNIT_REGION IN ('MUMBAI','PUNE') THEN 'WEST'
            WHEN A.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.UNIT_REGION,
        SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_REGION_INFO,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST'
            WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_REGION_INFO = B1.UNIT_REGION_INFO) Z
    
    LEFT OUTER JOIN

(SELECT 
    A1.BIZ_DATE,
    A1.UNIT_REGION_INFO,
    ROUND((DINE_SALE / total_Tickets), 0) EXISTING_CUSTOMER_APC,
    total_Tickets EXISTING_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence /QUANTITY) * 100, 0) AS EXISTING_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE WHEN A.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN A.UNIT_REGION IN ('MUMBAI','PUNE') THEN 'WEST'
            WHEN A.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.UNIT_REGION,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME < ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_REGION_INFO,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST'
            WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME < ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_REGION_INFO = B1.UNIT_REGION_INFO) Z1 ON Z1.BIZ_DATE = Z.BIZ_DATE AND Z1.UNIT_REGION_INFO = Z.UNIT_REGION_INFO
    
    LEFT OUTER JOIN

(SELECT 
    'Overall' AS OVERALL_Chaayos,
    A1.BIZ_DATE,
    A1.UNIT_REGION_INFO,
    ROUND((DINE_SALE / total_Tickets), 0) OVERALL_CUSTOMER_APC,
    total_Tickets OVERALL_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence /QUANTITY) * 100, 0) AS OVERALL_CORE_ITEM_INCIDENCE,
    QUANTITY
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE WHEN A.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN A.UNIT_REGION IN ('MUMBAI','PUNE') THEN 'WEST'
            WHEN A.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            ud.UNIT_REGION,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_REGION_INFO,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            CASE WHEN UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN UNIT_REGION IN ('MUMBAI','PUNE') THEN 'WEST'
            WHEN UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID    
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_REGION_INFO = B1.UNIT_REGION_INFO) Z2 ON Z2.BIZ_DATE = Z.BIZ_DATE AND Z2.UNIT_REGION_INFO = Z.UNIT_REGION_INFO 
    
    
    
    
            LEFT OUTER JOIN
(SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        B.UNIT_REGION_INFO,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY_TKT,
            SUM(CASE
                WHEN PTYPE IN ('7') THEN 1
                ELSE 0
            END) FOOD_ONLY_TKT
    FROM
        (SELECT 
        od1.ORDER_ID,
            CASE WHEN UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN UNIT_REGION IN ('MUMBAI','PUNE') THEN 'WEST'
            WHEN UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od1.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND (oi1.IS_COMPLIMENTARY = 'N'
            OR oi1.IS_COMPLIMENTARY IS NULL)
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS B
    GROUP BY 1, 2) Z3 ON Z3.BIZ_DATE = Z.BIZ_DATE AND Z3.UNIT_REGION_INFO = Z.UNIT_REGION_INFO
    
    GROUP BY 1;


                                                ]]>
                                        </content>
                                </report>

                                <report id="10" name="AREA MANAGER(L2) LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[


SELECT 

ed.EMP_NAME AREA_MANAGER,
NEW_CUSTOMER_APC,
EXISTING_CUSTOMER_APC,
OVERALL_CUSTOMER_APC,
CONCAT(NEW_CORE_ITEM_INCIDENCE,"%") NEW_CORE_ITEM_INCIDENCE,
CONCAT(EXISTING_CORE_ITEM_INCIDENCE, "%") EXISTING_CORE_ITEM_INCIDENCE,
CONCAT(OVERALL_CORE_ITEM_INCIDENCE, "%") OVERALL_CORE_ITEM_INCIDENCE,
NEW_CUSTOMER_TICKETS,
(OVERALL_CUSTOMER_TICKETS - NEW_CUSTOMER_TICKETS) EXISTING_CUSTOMER_TICKETS,
OVERALL_CUSTOMER_TICKETS,
CONCAT(ROUND((BEV_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") BOT,
CONCAT(ROUND((FOOD_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") FOT


FROM(
SELECT 
    'Overall' AS Chaayos,
    A1.BIZ_DATE,
    A1.UNIT_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) NEW_CUSTOMER_APC,
    total_Tickets NEW_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence / QUANTITY) * 100, 0) AS NEW_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            UNIT_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.UNIT_MANAGER,
        SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_MANAGER,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            UNIT_MANAGER,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_MANAGER = B1.UNIT_MANAGER) Z
    
    LEFT OUTER JOIN

(SELECT 
    A1.BIZ_DATE,
    A1.UNIT_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) EXISTING_CUSTOMER_APC,
    total_Tickets EXISTING_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence / QUANTITY) * 100, 0) AS EXISTING_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            UNIT_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.UNIT_MANAGER,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME < ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_MANAGER,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            UNIT_MANAGER,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME < ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_MANAGER = B1.UNIT_MANAGER) Z1 ON Z1.BIZ_DATE = Z.BIZ_DATE AND Z1.UNIT_MANAGER = Z.UNIT_MANAGER
    
    LEFT OUTER JOIN

(SELECT 
    'Overall' AS OVERALL_Chaayos,
    A1.BIZ_DATE,
    A1.UNIT_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) OVERALL_CUSTOMER_APC,
    total_Tickets OVERALL_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence / QUANTITY) * 100, 0) AS OVERALL_CORE_ITEM_INCIDENCE,
    QUANTITY
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            UNIT_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            ud.UNIT_MANAGER,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_MANAGER,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            UNIT_MANAGER,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID    
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_MANAGER = B1.UNIT_MANAGER) Z2 ON Z2.BIZ_DATE = Z.BIZ_DATE AND Z2.UNIT_MANAGER = Z.UNIT_MANAGER
    
    
        LEFT OUTER JOIN
(SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        B.UNIT_MANAGER,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY_TKT,
            SUM(CASE
                WHEN PTYPE IN ('7') THEN 1
                ELSE 0
            END) FOOD_ONLY_TKT
    FROM
        (SELECT 
        od1.ORDER_ID,
            ud1.UNIT_MANAGER,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od1.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND (oi1.IS_COMPLIMENTARY = 'N'
            OR oi1.IS_COMPLIMENTARY IS NULL)
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS B
    GROUP BY 1, 2) Z3 ON Z3.BIZ_DATE = Z.BIZ_DATE AND Z3.UNIT_MANAGER = Z.UNIT_MANAGER
    
    
    
    INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON Z.UNIT_MANAGER = ed.EMP_ID
    GROUP BY 1;


                                             ]]>
                                        </content>
                                </report>





                                <report id="10" name="DEPUTY_AM(L1) LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
        

SELECT 

ed.EMP_NAME DEPUTY_AM_L1,
NEW_CUSTOMER_APC,
EXISTING_CUSTOMER_APC,
OVERALL_CUSTOMER_APC,
CONCAT(NEW_CORE_ITEM_INCIDENCE,"%") NEW_CORE_ITEM_INCIDENCE,
CONCAT(EXISTING_CORE_ITEM_INCIDENCE, "%") EXISTING_CORE_ITEM_INCIDENCE,
CONCAT(OVERALL_CORE_ITEM_INCIDENCE, "%") OVERALL_CORE_ITEM_INCIDENCE,
NEW_CUSTOMER_TICKETS,
(OVERALL_CUSTOMER_TICKETS - NEW_CUSTOMER_TICKETS) EXISTING_CUSTOMER_TICKETS,
OVERALL_CUSTOMER_TICKETS,
CONCAT(ROUND((BEV_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") BOT,
CONCAT(ROUND((FOOD_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") FOT


FROM(
SELECT 
    'Overall' AS Chaayos,
    A1.BIZ_DATE,
    A1.CAFE_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) NEW_CUSTOMER_APC,
    total_Tickets NEW_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence / QUANTITY) * 100, 0) AS NEW_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CAFE_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.CAFE_MANAGER,
        SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.CAFE_MANAGER,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            ud.CAFE_MANAGER,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.CAFE_MANAGER = B1.CAFE_MANAGER) Z
    
    LEFT OUTER JOIN

(SELECT 
    A1.BIZ_DATE,
    A1.CAFE_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) EXISTING_CUSTOMER_APC,
    total_Tickets EXISTING_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence / QUANTITY) * 100, 0) AS EXISTING_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CAFE_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.CAFE_MANAGER,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME <  "2021-02-08 05:00:00"
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.CAFE_MANAGER,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            ud.CAFE_MANAGER,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME <  "2021-02-08 05:00:00"
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.CAFE_MANAGER = B1.CAFE_MANAGER) Z1 ON Z1.BIZ_DATE = Z.BIZ_DATE AND Z1.CAFE_MANAGER = Z.CAFE_MANAGER
    
    LEFT OUTER JOIN

(SELECT 
    'Overall' AS OVERALL_Chaayos,
    A1.BIZ_DATE,
    A1.CAFE_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) OVERALL_CUSTOMER_APC,
    total_Tickets OVERALL_CUSTOMER_TICKETS,
    ROUND((Core_Item_Incidence / QUANTITY) * 100, 0) AS OVERALL_CORE_ITEM_INCIDENCE,
    QUANTITY
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CAFE_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            ud.CAFE_MANAGER,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295, 1000007)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.CAFE_MANAGER,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) Core_Item_Incidence,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            ud.CAFE_MANAGER,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID    
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.CAFE_MANAGER = B1.CAFE_MANAGER) Z2 ON Z2.BIZ_DATE = Z.BIZ_DATE AND Z2.CAFE_MANAGER = Z.CAFE_MANAGER
    
        LEFT OUTER JOIN
(SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        B.CAFE_MANAGER,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY_TKT,
            SUM(CASE
                WHEN PTYPE IN ('7') THEN 1
                ELSE 0
            END) FOOD_ONLY_TKT
    FROM
        (SELECT 
        od1.ORDER_ID,
            ud1.CAFE_MANAGER,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od1.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND (oi1.IS_COMPLIMENTARY = 'N'
            OR oi1.IS_COMPLIMENTARY IS NULL)
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS B
    GROUP BY 1, 2) Z3 ON Z3.BIZ_DATE = Z.BIZ_DATE AND Z3.CAFE_MANAGER = Z.CAFE_MANAGER
    
    INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON Z.CAFE_MANAGER = ed.EMP_ID
    GROUP BY 1;



                                                ]]>
                                        </content>
 
                               </report>
 
 
 
 
                                <report id="10" name="CAFE LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
 
SELECT 

Z.UNIT_NAME UNIT_NAME,
ed.EMP_NAME AREA_MANAGER,
NEW_CUSTOMER_APC,
EXISTING_CUSTOMER_APC,
OVERALL_CUSTOMER_APC,
CONCAT(NEW_CORE_ITEM_INCIDENCE,"%") NEW_CORE_ITEM_INCIDENCE,
CONCAT(EXISTING_CORE_ITEM_INCIDENCE, "%") EXISTING_CORE_ITEM_INCIDENCE,
CONCAT(OVERALL_CORE_ITEM_INCIDENCE, "%") OVERALL_CORE_ITEM_INCIDENCE,
NEW_CUSTOMER_TICKETS,
(OVERALL_CUSTOMER_TICKETS - NEW_CUSTOMER_TICKETS) EXISTING_CUSTOMER_TICKETS,
OVERALL_CUSTOMER_TICKETS,
CONCAT(ROUND((BEV_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") BOT,
CONCAT(ROUND((FOOD_ONLY_TKT/OVERALL_CUSTOMER_TICKETS)*100, 0),"%") FOT

FROM(
SELECT 
    'Overall' AS Chaayos,
    A1.BIZ_DATE,
    A1.UNIT_NAME,
    A1.UNIT_MANAGER,
    ROUND((DINE_SALE / total_Tickets), 0) NEW_CUSTOMER_APC,
    total_Tickets NEW_CUSTOMER_TICKETS,
    ROUND((CORE_ITEM_INCIDENCE /QUANTITY) * 100, 0) AS NEW_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            UNIT_NAME,
            UNIT_MANAGER,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.UNIT_NAME,
        ud.UNIT_MANAGER,
        SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043,26198)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME  BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_NAME,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) CORE_ITEM_INCIDENCE,
            SUM(QUANTITY) QUANTITY 
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            UNIT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043,26198)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME  BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_NAME = B1.UNIT_NAME) Z
    
    LEFT OUTER JOIN

(SELECT 
    A1.BIZ_DATE,
    A1.UNIT_NAME,
    ROUND((DINE_SALE / total_Tickets), 0) EXISTING_CUSTOMER_APC,
    total_Tickets EXISTING_CUSTOMER_TICKETS,
    ROUND((CORE_ITEM_INCIDENCE /QUANTITY) * 100, 0) AS EXISTING_CORE_ITEM_INCIDENCE
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            UNIT_NAME,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
        ud.UNIT_NAME,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043,26198)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND ci.NUMBER_VERIFICATION_TIME < "2021-02-01 05:00:00"
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_NAME,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231
) THEN QUANTITY
                ELSE 0
            END) CORE_ITEM_INCIDENCE,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            UNIT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od 
	INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
	INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043,26198)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND ci.NUMBER_VERIFICATION_TIME < "2021-02-01 05:00:00"
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_NAME = B1.UNIT_NAME) Z1 ON Z1.BIZ_DATE = Z.BIZ_DATE AND Z1.UNIT_NAME = Z.UNIT_NAME
    
    LEFT OUTER JOIN

(SELECT 
    'Overall' AS OVERALL_Chaayos,
    A1.BIZ_DATE,
    A1.UNIT_NAME,
    ROUND((DINE_SALE / total_Tickets), 0) OVERALL_CUSTOMER_APC,
    total_Tickets OVERALL_CUSTOMER_TICKETS,
    ROUND((CORE_ITEM_INCIDENCE /QUANTITY) * 100, 0) AS OVERALL_CORE_ITEM_INCIDENCE,
    QUANTITY
FROM
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            UNIT_NAME,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT 
        od.ORDER_ID,
            ud.UNIT_NAME,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043,26198)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.IS_GIFT_CARD_ORDER <> 'Y'
            #AND (oi.IS_COMPLIMENTARY = 'N' OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1, 2) AS A
   group by 1, 2) AS A1
        LEFT OUTER JOIN
    (SELECT 
            DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            B.UNIT_NAME,
            SUM(CASE
                WHEN PRODUCT_ID IN (10,11,12,14,15,50,130,271,460,620,640,641,660,670,680,690,868,992,1037,1057,1064,1065,1156,1164,1166,1179,1201,1231,1033) THEN QUANTITY
                ELSE 0
            END) CORE_ITEM_INCIDENCE,
            SUM(QUANTITY) QUANTITY
           FROM
        (SELECT 
            oi.PRODUCT_ID PRODUCT_ID,
            UNIT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID    
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043,26198)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY = 'N'
            OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1, 1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295, 1004, 1238, 1239, 1358, 1359, 1626, 1627, 1628, 1377, 1154, 1000007)
    GROUP BY 1, 2)AS B
    GROUP BY 1, 2)AS B1 ON A1.BIZ_DATE = B1.BIZ_DATE AND A1.UNIT_NAME = B1.UNIT_NAME) Z2 ON Z2.BIZ_DATE = Z.BIZ_DATE AND Z2.UNIT_NAME = Z.UNIT_NAME 
    
    
    LEFT OUTER JOIN
(SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        B.UNIT_NAME,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY_TKT,
            SUM(CASE
                WHEN PTYPE IN ('7') THEN 1
                ELSE 0
            END) FOOD_ONLY_TKT
    FROM
        (SELECT 
        od1.ORDER_ID,
            ud1.UNIT_NAME,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od1.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND (oi1.IS_COMPLIMENTARY = 'N'
            OR oi1.IS_COMPLIMENTARY IS NULL)
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS B
    GROUP BY 1, 2) Z3 ON Z3.BIZ_DATE = Z.BIZ_DATE AND Z3.UNIT_NAME = Z.UNIT_NAME 
   
   INNER JOIN
    
 KETTLE_MASTER.EMPLOYEE_DETAIL ed ON Z.UNIT_MANAGER = ed.EMP_ID
    GROUP BY 1 ORDER BY ed.EMP_NAME,UNIT_NAME;
 
                                                ]]>
                                        </content>
 
                               </report>
 
 
 
              </reports>
        </category>
   </categories>
</ReportCategories>


