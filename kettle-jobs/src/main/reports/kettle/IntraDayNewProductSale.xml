<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Intra Day New Product Sale" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails=""
                        schedule="">
			<reports>
				<report id="1" name="Overall: New Product Sale" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
                OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 1)
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
 AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')            
AND oi.PRODUCT_ID IN (select RL_CODE from KETTLE_MASTER.REF_LOOKUP_TYPE rtl, KETTLE_MASTER.REF_LOOKUP rl
where rtl.RTL_ID = rl.RTL_ID
and rtl.RTL_GROUP ='ANALYTICS'
and rtl.RTL_CODE = 'NEW_PRODUCTS'
and rl.RL_STATUS = 'ACTIVE'
)
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION
ORDER BY BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION;


        ]]>
					</content>

				</report>
				<report id="2" name="Area Manager(L2): New Product Total Sales" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
        ed.EMP_NAME AREA_MANAGER_L2,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 4
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
                OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 1)
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
                LEFT JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
 AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')        
AND oi.PRODUCT_ID IN (select RL_CODE from KETTLE_MASTER.REF_LOOKUP_TYPE rtl, KETTLE_MASTER.REF_LOOKUP rl
where rtl.RTL_ID = rl.RTL_ID
and rtl.RTL_GROUP ='ANALYTICS'
and rtl.RTL_CODE = 'NEW_PRODUCTS'
and rl.RL_STATUS = 'ACTIVE'
)
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION, ud.UNIT_MANAGER
ORDER BY ud.UNIT_MANAGER, BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION

        ]]>
					</content>
				</report>
				<report id="3" name="Unit wise:  New Product Sale" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
        ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
                OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 1)
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
 AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')       
AND oi.PRODUCT_ID IN (select RL_CODE from KETTLE_MASTER.REF_LOOKUP_TYPE rtl, KETTLE_MASTER.REF_LOOKUP rl
where rtl.RTL_ID = rl.RTL_ID
and rtl.RTL_GROUP ='ANALYTICS'
and rtl.RTL_CODE = 'NEW_PRODUCTS'
and rl.RL_STATUS = 'ACTIVE')
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION, ud.UNIT_NAME
ORDER BY BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION, ud.UNIT_NAME

        ]]>
					</content>
				</report>
				
			</reports>
		</category>
	</categories>
</ReportCategories>

