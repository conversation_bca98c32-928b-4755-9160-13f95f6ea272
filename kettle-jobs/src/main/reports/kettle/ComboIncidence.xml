<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="COMBO INCIDENCE:" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>

                                
                                <report id="11" name="CAFE LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
            
SELECT 
    A1.UNIT_ID,
    A1.UNIT_NAME,
    DINE_TICKETS,
    QUANTITY COMBO_SOLD,
    CONCAT(ROUND((QUANTITY/DINE_TICKETS)*100,0),"%") COMBO_INCIDENCE
        FROM
    (SELECT 
            UNIT_ID,
            UNIT_NAME,
            COUNT(A.ORDER_ID) DINE_TICKETS
    FROM
        (SELECT 
        od.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_NAME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2 DESC
    ORDER BY UNIT_NAME) AS A1
        LEFT JOIN
    (SELECT 
        od.UNIT_ID,
            #oi.PRODUCT_NAME PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY   FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND pd.PRODUCT_TYPE = 8
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1) A2 ON A2.UNIT_ID = A1.UNIT_ID GROUP BY 1 HAVING A2.QUANTITY>0 ORDER BY COMBO_SOLD DESC;            
            
 
                                                ]]>
                                        </content>
 
                               </report>
 
 
 
              </reports>
        </category>
   </categories>
</ReportCategories>


