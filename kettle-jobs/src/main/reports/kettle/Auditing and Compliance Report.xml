<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
      <categories>
            <category name="Compliance Reports" type="Cafe Operations"
                  accessCode="Marketing">
                  <reports>
                        <report name="Discount Summary Sheet"
                              executionType="SQL" returnType="java.lang.String">
                              <content><![CDATA[
							  
							  
			SELECT 
    od.UNIT_ID,
    ud.UNIT_NAME,
    od.ORDER_SOURCE,
    ud.UNIT_REGION,
    od.GENERATED_ORDER_ID AS ORDER_NO,
    DATE(od.BILLING_SERVER_TIME) AS 'DATE',
    TIME(od.BILLING_SERVER_TIME) AS 'TIME',
    oi.PRODUCT_ID,
    pd.PRODUCT_NAME,
    oi.PRICE,
    oi.QUANTITY,
    oi.IS_COMPLIMENTARY,
    od.EMP_ID,
    COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
    (CASE
        WHEN oi.IS_COMPLIMENTARY = 'Y' THEN 100
        ELSE 0
    END) AS DISCOUNT_PERCENT,
    COALESCE(oi.COMPLIMENTARY_TYPE_ID, 'NA') AS COMPLIMENTARY_ID,
    COALESCE(cc.COMP_CODE, 'NA') AS REASON_FOR_COMPLIMENTARY,
    ci.CUSTOMER_ID,
    ci.CONTACT_NUMBER,
    ci.FIRST_NAME,
    od.TOTAL_AMOUNT,
    od.TAXABLE_AMOUNT,
    od.DISCOUNT_AMOUNT,
    od.SETTLED_AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
        LEFT JOIN
    KETTLE_DUMP.COMPLIMENTARY_CODE cc ON cc.COMP_ID = oi.COMPLIMENTARY_TYPE_ID
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
	   
	   
	   			]]></content>
                              <params>
                                    <param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                                    <param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                              </params>
 
                        </report>
	   
	   
                        <report name="Bill Wise Summary Sheet"
                              executionType="SQL" returnType="java.lang.String">
                              <content><![CDATA[
							  
							  
							  
		SELECT 
    od.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_REGION,
    od.EMP_ID,
    COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
        od.GENERATED_ORDER_ID AS ORDER_NO,
    DATE(od.BILLING_SERVER_TIME) AS 'DATE',
    TIME(od.BILLING_SERVER_TIME) AS 'TIME',
    nt.QUANTITY,
    od.TOTAL_AMOUNT,
    od.DISCOUNT_AMOUNT,
    od.TAXABLE_AMOUNT,
    od.SURCHARGE_TAX_AMOUNT,
    od.SERVICE_TAX_AMOUNT,
    od.NET_PRICE_VAT_AMOUNT,
    od.MRP_VAT_AMOUNT,
    od.SB_CESS_AMOUNT,
    od.SETTLED_AMOUNT,
    od.ORDER_SOURCE,
    pm.MODE_NAME AS SETTLEMENT_TYPE,
    od.CHANNEL_PARTNER_ID,
    cp.PARTNER_DISPLAY_NAME
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
        LEFT JOIN
    (SELECT 
        od.ORDER_ID, SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
				
				
    GROUP BY od.ORDER_ID) nt ON od.ORDER_ID = nt.ORDER_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
       
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
	   
	   ]]></content>
                              <params>
                                    <param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                                    <param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                              </params>
 
                        </report>
						
						
                        <report name="Cancelled Order Sheet"
                              executionType="SQL" returnType="java.lang.String">
                              <content><![CDATA[
							  
							  
							  
		SELECT 
    ud.UNIT_NAME,
    ud.UNIT_REGION,
    od.GENERATED_ORDER_ID AS ORDER_NO,
    DATE(od.BILLING_SERVER_TIME) AS 'DATE',
    TIME(od.BILLING_SERVER_TIME) AS 'TIME',
    od.BILL_CANCELLATION_TIME,
    nt.QUANTITY,
    od.ORDER_SOURCE,
    od.TAXABLE_AMOUNT,
    od.SURCHARGE_TAX_AMOUNT,
    od.SERVICE_TAX_AMOUNT,
    od.NET_PRICE_VAT_AMOUNT,
    od.MRP_VAT_AMOUNT,
    od.SETTLED_AMOUNT,
    od.EMP_ID,
    COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
    od.GENERATED_ORDER_ID,
    COALESCE(od.CANCELATION_REASON, 'NA') AS CANCELATION_REASON,
    COALESCE(od.CANCELLED_BY, 'NA') AS CANCELLED_BY_ID,
    COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_BY,
    COALESCE(od.CANCEL_APPROVED_BY, 'NA') AS CANCEL_APPROVED_BY_ID,
    COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_APPROVED_BY
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed2 ON od.CANCEL_APPROVED_BY = ed2.EMP_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed3 ON od.CANCELLED_BY = ed3.EMP_ID
        LEFT JOIN
    (SELECT 
        od.ORDER_ID, SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS = 'CANCELLED'
		
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
				
				
    GROUP BY od.ORDER_ID) nt ON od.ORDER_ID = nt.ORDER_ID
WHERE
    od.ORDER_STATUS = 'CANCELLED'
	
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
			
			
			   ]]></content>
                              <params>
                                    <param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                                    <param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                              </params>
 
                        </report>
						
						
						
						 <report name="Other Compliance Issue"
                              executionType="SQL" returnType="java.lang.String">
                              <content><![CDATA[
						
						
						SELECT 
    od.ORDER_ID,
    od.GENERATED_ORDER_ID AS ORDER_NO,
    od.BILLING_SERVER_TIME,
    od.ORDER_STATUS,
    od.UNIT_ID,
    ud.UNIT_NAME,
    od.TOTAL_AMOUNT,
    od.TAXABLE_AMOUNT,
    od.SETTLED_AMOUNT,
    os.*,
    od.EMP_ID,
    ed.EMP_NAME
FROM
    KETTLE_DUMP.ORDER_DETAIL od,
    (SELECT 
        ORDER_ID,
            SUM(CASE
                WHEN ROUND_OFF_AMOUNT IS NOT NULL THEN AMOUNT_PAID + ROUND_OFF_AMOUNT
                ELSE AMOUNT_PAID
            END) AMOUNT_PAID,
            COUNT(*) NO_OF_SETTLEMENTS,
            MAX(PAYMENT_MODE_ID)
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT
    GROUP BY ORDER_ID) os,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed
WHERE
    od.ORDER_ID = os.ORDER_ID
        AND od.SETTLED_AMOUNT <> os.AMOUNT_PAID
        AND od.UNIT_ID = ud.UNIT_ID
        AND ed.EMP_ID = od.EMP_ID
		
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)

						
			  ]]></content>
                              <params>
                                    <param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                                    <param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                              </params>
 
                        </report>			
						

						
						
						        </reports>
				  
	  
            </category>
      </categories>
</ReportCategories>