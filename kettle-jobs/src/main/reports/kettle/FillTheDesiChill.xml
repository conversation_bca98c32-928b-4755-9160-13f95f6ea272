<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="FEEL THE DESI CHILL- in every sip:" type="Automated"
                          accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>


                                <report id="10" name="CHAAYOS" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
SELECT
    T1.L3_LEVEL AS INDIA,
    APC_Chaayos_Target AS APC_TARGET,
    ROUND((DINE_SALE / total_Tickets), 0) APC_ACH,
    COLD_Chaayos AS COLD_TARGET,
    ROUND((COLD / total_Tickets) * 100, 0) AS COLD_ACH,
    BOT_Chaayos AS BOT_TARGET,
    ROUND((BOT_ACH / total_Tickets) * 100, 0) AS BOT_ACH,
    FTR_Chaayos AS Regular_to_Full_TARGET,
    ROUND((Full_Item_QTY/ total_Tickets) * 100, 0) AS Regular_to_Full_ACH,
    Merchandise_Chaayos AS MERCHANDISE_TARGET,
    ROUND((MERCHANDISE / DINE_SALE) * 100, 0) AS MERC_OF_DINE_SALE,
    MERCHANDISE MERC_REVENUE
FROM
    (SELECT
        BUSINESS_DATE,
            L3_LEVEL,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT
        od.ORDER_ID,
            CURRENT_DATE AS BUSINESS_DATE,
            SFT.L3_LEVEL,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2 DESC
    ORDER BY L3_LEVEL) AS A1
        LEFT JOIN
    (SELECT
        A2.L3_LEVEL,
            SUM(CASE
                WHEN PRODUCT_ID IN (40, 300, 310, 370, 400, 420, 440, 450, 460, 480, 1060, 1178, 1179, 1232, 1234, 1235, 1540) THEN QUANTITY
	           ELSE 0
            END) COLD,
            SUM(CASE
                WHEN PRODUCT_ID IN (730 , 1057, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1237, 1283, 1289, 1306, 1305, 1304, 1303, 1354, 1355, 1356, 1357, 1369, 1370, 1372, 1374, 1453, 1457, 1458, 1459, 1470, 1456, 1399, 1485, 1154) THEN AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE
    FROM
        (SELECT
        SFT.L3_LEVEL,
            PDB.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) A2
    GROUP BY 1) A3 ON A3.L3_LEVEL = A1.L3_LEVEL
        LEFT JOIN
    (SELECT
        L3_LEVEL,
            BUSINESS_DATE,
            APC_Chaayos_Target,
            COLD_Chaayos,
            FTR_Chaayos,
            BOT_Chaayos,
            Merchandise_Chaayos
    FROM
        CLM_ANALYTICS.STREET_FOOD_TARGET SF_TARGET
    WHERE
        (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
    GROUP BY 1) T1 ON T1.L3_LEVEL = A1.L3_LEVEL
        LEFT JOIN
    (SELECT
        U.L3_LEVEL,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BOT_ACH
    FROM
        (SELECT
        od1.ORDER_ID,
            SFT.L3_LEVEL,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od1.UNIT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
            AND od1.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
    GROUP BY 1 , 2) AS U
    GROUP BY 1) U2 ON U2.L3_LEVEL = A1.L3_LEVEL
         LEFT JOIN
(SELECT
        V.L3_LEVEL,
            SUM(CASE
                WHEN V.dimension = 'Full' THEN V.QTY
                ELSE 0
            END) 'Full_Item_QTY',
            SUM(CASE
                WHEN V.dimension IN ('Full' , 'Regular') THEN V.QTY
                ELSE 0
            END) 'Full_Reg_QTY'
    FROM
        (SELECT
        od.ORDER_ID, L3_LEVEL, oi.dimension, SUM(oi.QUANTITY) QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (10, 11, 12, 14, 15, 50, 60, 130, 140, 150, 170, 272, 300, 310, 420, 450, 460, 480, 1060, 1061, 1205, 1282, 1292, 1293, 1294, 70, 1235, 1375, 1376, 1540)
    GROUP BY 1 , 2) AS V group by 1) V2 ON V2.L3_LEVEL = A1.L3_LEVEL;




                                       ]]>
                                        </content>
                                </report>

                                <report id="10" name="REGIONAL LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT
    T1.REGION,
    APC_REGION_Target AS APC_TARGET,
    ROUND((DINE_SALE / total_Tickets), 0) APC_ACH,
    COLD_REGION AS COLD_TARGET,
    ROUND((COLD / total_Tickets) * 100, 0) AS COLD_ACH,
    FTR_Region AS Regular_to_Full_TARGET,
    ROUND((Full_Item_QTY/ total_Tickets) * 100, 0) AS Regular_to_Full_ACH,
    BOT_REGION AS BOT_TARGET,
    ROUND((BOT_ACH / total_Tickets) * 100, 0) AS BOT_ACH,
    Merchandise_REGION AS MERCHANDISE_TARGET,
    ROUND((MERCHANDISE / DINE_SALE) * 100, 0) AS MERC_OF_DINE_SALE,
    MERCHANDISE MERC_REVENUE
FROM
    (SELECT
        BUSINESS_DATE,
            REGION,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT
        od.ORDER_ID,
            CURRENT_DATE AS BUSINESS_DATE,
            SFT.REGION,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2 DESC
    ORDER BY REGION) AS A1
        LEFT JOIN
    (SELECT
        A2.REGION,
                  SUM(CASE
                WHEN PRODUCT_ID IN (40, 300, 310, 370, 400, 420, 440, 450, 460, 480, 1060, 1178, 1179, 1232, 1234, 1235, 1540) THEN QUANTITY
	           ELSE 0
            END) COLD,
            SUM(CASE
                WHEN PRODUCT_ID IN (730 , 1057, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1237, 1283, 1289, 1306, 1305, 1304, 1303, 1354, 1355, 1356, 1357, 1369, 1370, 1372, 1374, 1453, 1457, 1458, 1459, 1470, 1456, 1399, 1485, 1154) THEN AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE
    FROM
        (SELECT
        SFT.REGION,
            PDB.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) A2
    GROUP BY 1) A3 ON A3.REGION = A1.REGION
        LEFT JOIN
    (SELECT
        REGION,
            BUSINESS_DATE,
            APC_Region_Target,
            COLD_Region,
            FTR_Region,
            BOT_Region,
            Merchandise_Region
    FROM
        CLM_ANALYTICS.STREET_FOOD_TARGET SF_TARGET
    WHERE
        (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
    GROUP BY 1) T1 ON T1.REGION = A1.REGION
        LEFT JOIN
    (SELECT
        U.REGION,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BOT_ACH
    FROM
        (SELECT
        od1.ORDER_ID,
            SFT.REGION,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od1.UNIT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
            AND od1.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
    GROUP BY 1 , 2) AS U
    GROUP BY 1) U2 ON U2.REGION = A1.REGION
         LEFT JOIN
(SELECT
        V.REGION,
            SUM(CASE
                WHEN V.dimension = 'Full' THEN V.QTY
                ELSE 0
            END) 'Full_Item_QTY',
            SUM(CASE
                WHEN V.dimension IN ('Full' , 'Regular') THEN V.QTY
                ELSE 0
            END) 'Full_Reg_QTY'
    FROM
        (SELECT
        od.ORDER_ID, SFT.REGION, oi.dimension, SUM(oi.QUANTITY) QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (10, 11, 12, 14, 15, 50, 60, 130, 140, 150, 170, 272, 300, 310, 420, 450, 460, 480, 1060, 1061, 1205, 1282, 1292, 1293, 1294, 70, 1235, 1375, 1376, 1540)
    GROUP BY 1 , 2) AS V group by 1) V2 ON V2.REGION = A1.REGION;



                                                ]]>
                                        </content>
                                </report>

                                <report id="10" name="AREA MANAGER(L2) LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
SELECT
    T1.L2_LEVEL AS LEAGUE_TEAM,
    APC_AM_Target AS APC_TARGET,
    ROUND((DINE_SALE / total_Tickets), 0) APC_ACH,
    COLD_AM AS COLD_TARGET,
    ROUND((COLD / total_Tickets) * 100, 0) AS COLD_ACH,
    FTR_AM AS Regular_to_Full_TARGET,
    ROUND((Full_Item_QTY/ total_Tickets) * 100, 0) AS Regular_to_Full_ACH,
    BOT_AM AS BOT_TARGET,
    ROUND((BOT_ACH / total_Tickets) * 100, 0) AS BOT_ACH,
    Merchandise_AM AS MERCHANDISE_TARGET,
    ROUND((MERCHANDISE / DINE_SALE) * 100, 0) AS MERC_OF_DINE_SALE,
    MERCHANDISE MERC_REVENUE
FROM
    (SELECT
        BUSINESS_DATE,
            L2_LEVEL,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT
        od.ORDER_ID,
            SFT.L2_LEVEL,
            CURRENT_DATE AS BUSINESS_DATE,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2 DESC
    ORDER BY L2_LEVEL) AS A1
        LEFT JOIN
    (SELECT
        A2.L2_LEVEL,
           SUM(CASE
                WHEN PRODUCT_ID IN (40, 300, 310, 370, 400, 420, 440, 450, 460, 480, 1060, 1178, 1179, 1232, 1234, 1235, 1540) THEN QUANTITY
	           ELSE 0
            END) COLD,
            SUM(CASE
                WHEN PRODUCT_ID IN (730 , 1057, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1237, 1283, 1289, 1306, 1305, 1304, 1303, 1354, 1355, 1356, 1357, 1369, 1370, 1372, 1374, 1453, 1457, 1458, 1459, 1470, 1456, 1399, 1485, 1154) THEN AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE
    FROM
        (SELECT
        SFT.L2_LEVEL,
            PDB.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) A2
    GROUP BY 1) A3 ON A3.L2_LEVEL = A1.L2_LEVEL
        LEFT JOIN
    (SELECT
        L2_LEVEL,
            BUSINESS_DATE,
            APC_AM_Target,
            COLD_AM,
            FTR_AM,
            BOT_AM,
            Merchandise_AM
    FROM
        CLM_ANALYTICS.STREET_FOOD_TARGET SF_TARGET
    WHERE
        (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
    GROUP BY 1) T1 ON T1.L2_LEVEL = A1.L2_LEVEL
        LEFT JOIN
    (SELECT
        U.L2_LEVEL,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BOT_ACH
    FROM
        (SELECT
        od1.ORDER_ID,
            SFT.L2_LEVEL,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od1.UNIT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
            AND od1.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
    GROUP BY 1 , 2) AS U
    GROUP BY 1) U2 ON U2.L2_LEVEL = A1.L2_LEVEL
         LEFT JOIN
(SELECT
        V.L2_LEVEL,
            SUM(CASE
                WHEN V.dimension = 'Full' THEN V.QTY
                ELSE 0
            END) 'Full_Item_QTY',
            SUM(CASE
                WHEN V.dimension IN ('Full' , 'Regular') THEN V.QTY
                ELSE 0
            END) 'Full_Reg_QTY'
    FROM
        (SELECT
        od.ORDER_ID, SFT.L2_LEVEL, oi.dimension, SUM(oi.QUANTITY) QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (10, 11, 12, 14, 15, 50, 60, 130, 140, 150, 170, 272, 300, 310, 420, 450, 460, 480, 1060, 1061, 1205, 1282, 1292, 1293, 1294, 70, 1235, 1375, 1376, 1540)
    GROUP BY 1 , 2) AS V group by 1) V2 ON V2.L2_LEVEL = A1.L2_LEVEL;


                                             ]]>
                                        </content>
                                </report>





                                <report id="10" name="DEPUTY_AM(L1) LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT
    T1.L1_LEVEL AS TEAM_NAME,
    APC_DAM_Target AS APC_TARGET,
    ROUND((DINE_SALE / total_Tickets), 0) APC_ACH,
    COLD_DAM AS COLD_TARGET,
    ROUND((COLD / total_Tickets) * 100, 0) AS COLD_ACH,
    FTR_DAM AS Regular_to_Full_TARGET,
    ROUND((Full_Item_QTY/ total_Tickets) * 100, 0) AS Regular_to_Full_ACH,
    BOT_DAM AS BOT_TARGET,
    ROUND((BOT_ACH / total_Tickets) * 100, 0) AS BOT_ACH,
	Merchandise_DAM AS MERCHANDISE_TARGET,
    ROUND((MERCHANDISE / DINE_SALE) * 100, 0) AS MERC_OF_DINE_SALE,
    MERCHANDISE MERC_REVENUE
FROM
    (SELECT
        BUSINESS_DATE,
            L1_LEVEL,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT
        od.ORDER_ID,
            CURRENT_DATE AS BUSINESS_DATE,
            SFT.L1_LEVEL,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2 DESC
    ORDER BY L1_LEVEL) AS A1
        LEFT JOIN
    (SELECT
        A2.L1_LEVEL,
            SUM(CASE
                WHEN PRODUCT_ID IN (40, 300, 310, 370, 400, 420, 440, 450, 460, 480, 1060, 1178, 1179, 1232, 1234, 1235, 1540) THEN QUANTITY
	           ELSE 0
            END) COLD,
            SUM(CASE
                WHEN PRODUCT_ID IN (730 , 1057, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1237, 1283, 1289, 1306, 1305, 1304, 1303, 1354, 1355, 1356, 1357, 1369, 1370, 1372, 1374, 1453, 1457, 1458, 1459, 1470, 1456, 1399, 1485, 1154) THEN AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE
	FROM
        (SELECT
        SFT.L1_LEVEL,
            PDB.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) A2
    GROUP BY 1) A3 ON A3.L1_LEVEL = A1.L1_LEVEL
        LEFT JOIN
    (SELECT
        L1_LEVEL,
            BUSINESS_DATE,
            APC_DAM_Target,
            COLD_DAM,
			FTR_DAM,
            BOT_DAM,
            Merchandise_DAM
	FROM
        CLM_ANALYTICS.STREET_FOOD_TARGET SF_TARGET
    WHERE
        (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
    GROUP BY 1) T1 ON T1.L1_LEVEL = A1.L1_LEVEL
        LEFT JOIN
    (SELECT
        U.L1_LEVEL,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BOT_ACH
    FROM
        (SELECT
        od1.ORDER_ID,
            SFT.L1_LEVEL,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od1.UNIT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
            AND od1.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
    GROUP BY 1 , 2) AS U
    GROUP BY 1) U2 ON U2.L1_LEVEL = A1.L1_LEVEL
         LEFT JOIN
(SELECT
        V.L1_LEVEL,
            SUM(CASE
                WHEN V.dimension = 'Full' THEN V.QTY
                ELSE 0
            END) 'Full_Item_QTY',
            SUM(CASE
                WHEN V.dimension IN ('Full' , 'Regular') THEN V.QTY
                ELSE 0
            END) 'Full_Reg_QTY'
    FROM
        (SELECT
        od.ORDER_ID, SFT.L1_LEVEL, oi.dimension, SUM(oi.QUANTITY) QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (10, 11, 12, 14, 15, 50, 60, 130, 140, 150, 170, 272, 300, 310, 420, 450, 460, 480, 1060, 1061, 1205, 1282, 1292, 1293, 1294, 70, 1235, 1375, 1376, 1540)
    GROUP BY 1 , 2) AS V group by 1) V2 ON V2.L1_LEVEL = A1.L1_LEVEL;

                                                ]]>
                                        </content>

                                </report>




                                <report id="10" name="CAFE LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

 SELECT
    UNIT_NAME,
    L2_LEVEL AS LEAGUE_TEAM,
    APC_Target AS APC_TARGET,
    ROUND((DINE_SALE / total_Tickets), 0) APC_ACH,
    COLD_Target AS COLD_TARGET,
    ROUND((COLD / total_Tickets) * 100, 0) AS COLD_ACH,
    FTR_Target AS Regular_to_Full_TARGET,
    ROUND((Full_Item_QTY/ total_Tickets) * 100, 0) AS Regular_to_Full_ACH,
    BOT_Target AS BOT_TARGET,
    ROUND((BOT_ACH / total_Tickets) * 100, 0) AS BOT_ACH,
    Merchandise_Target AS MERCHANDISE_TARGET,
    ROUND((MERCHANDISE / DINE_SALE) * 100, 0) AS MERC_OF_DINE_SALE,
    MERCHANDISE MERC_REVENUE
FROM
    (SELECT
        BUSINESS_DATE,
            UNIT_ID,
            UNIT_NAME,
            L2_LEVEL,
            COUNT(A.order_ID) Total_Tickets,
            SUM(A.AMOUNT_PAID) DINE_SALE
    FROM
        (SELECT
        od.ORDER_ID,
            CURRENT_DATE AS BUSINESS_DATE,
            SFT.UNIT_ID,
            ud.UNIT_NAME,
            SFT.L2_LEVEL,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            #AND od.IS_GIFT_CARD_ORDER <> 'Y'
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2 DESC
    ORDER BY L2_LEVEL) AS A1
        LEFT JOIN
    (SELECT
        A2.UNIT_ID,
            SUM(CASE
                WHEN PRODUCT_ID IN (40, 300, 310, 370, 400, 420, 440, 450, 460, 480, 1060, 1178, 1179, 1232, 1234, 1235, 1540) THEN QUANTITY
	           ELSE 0
            END) COLD,
            SUM(CASE
                WHEN PRODUCT_ID IN (730 , 1057, 1164, 1165, 1166, 1168, 1169, 1170, 1171, 1237, 1283, 1289, 1306, 1305, 1304, 1303, 1354, 1355, 1356, 1357, 1369, 1370, 1372, 1374, 1453, 1457, 1458, 1459, 1470, 1456, 1399, 1485, 1154) THEN AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE
		FROM
        (SELECT
        SFT.UNIT_ID,
            PDB.PRODUCT_ID PRODUCT_ID,
            SUM(oi.QUANTITY) QUANTITY,
            COUNT(DISTINCT od.ORDER_ID) Total_Tickets,
            SUM(oi.AMOUNT_PAID) AMOUNT_PAID
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
          #  AND (oi1.IS_COMPLIMENTARY = 'N' OR oi1.IS_COMPLIMENTARY IS NULL)
			AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218, 1291, 1295)
    GROUP BY 1 , 2) A2
    GROUP BY 1) A3 ON A3.UNIT_ID = A1.UNIT_ID
        LEFT JOIN
    (SELECT
        UNIT_ID,
            BUSINESS_DATE,
            APC_Target,
            COLD_Target,
            FTR_TARGET,
            BOT_Target,
            Merchandise_Target
    FROM
        CLM_ANALYTICS.STREET_FOOD_TARGET SF_TARGET
    WHERE
        (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
    GROUP BY 1 , 2) T1 ON T1.UNIT_ID = A1.UNIT_ID
        LEFT JOIN
    (SELECT 
        U.UNIT_ID,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BOT_ACH
    FROM
        (SELECT 
        od1.ORDER_ID,
            SFT.UNIT_ID,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od1.UNIT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
          #  AND (oi1.IS_COMPLIMENTARY = 'N' OR oi1.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
            AND od1.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26043)
    GROUP BY 1 , 2) AS U
    GROUP BY 1) U2 ON U2.UNIT_ID = A1.UNIT_ID
         LEFT JOIN
(SELECT 
        V.UNIT_ID,
            SUM(CASE
                WHEN V.dimension = 'Full' THEN V.QTY
                ELSE 0
            END) 'Full_Item_QTY',
            SUM(CASE
                WHEN V.dimension IN ('Full' , 'Regular') THEN V.QTY
                ELSE 0
            END) 'Full_Reg_QTY'
    FROM
        (SELECT 
        od.ORDER_ID, ud.UNIT_ID, oi.dimension, SUM(oi.QUANTITY) QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN CLM_ANALYTICS.STREET_FOOD_TARGET SFT ON SFT.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND od.UNIT_ID NOT IN (26198, 26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (CASE WHEN WEEKDAY(CURRENT_DATE) IN (6,7) THEN WD_WE = "WE" ELSE WD_WE = "WD" END)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (10, 11, 12, 14, 15, 50, 60, 130, 140, 150, 170, 272, 300, 310, 420, 450, 460, 480, 1060, 1061, 1205, 1282, 1292, 1293, 1294, 70, 1235, 1375, 1376, 1540)
    GROUP BY 1 , 2) AS V group by 1) V2 ON V2.UNIT_ID = A1.UNIT_ID;
            

 
 
                                                ]]>
                                        </content>

                                </report>



                        </reports>
                </category>
        </categories>
</ReportCategories>


