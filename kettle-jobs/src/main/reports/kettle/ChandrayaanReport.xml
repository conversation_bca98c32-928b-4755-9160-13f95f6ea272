<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="Chandrayaan Contest" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>

                                <report id="10" name="Region Wise" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
SELECT 
    A8.UNIT_REGION,
    T1.APC_TARGET,
    A8.APC,
    T1.Beverage_Target,
    A8.Beverage_Incidence,
    Full_Ratio_Target,
    A8.Full_Ratio,
    Food_Target,
    Food_Incidence,
    Meals_Target,
    Meals_Incidence,
    Bakery_Target,
    Bakery_Incidence,
    Cold_Target,
    Cold_Incidence,
    Merch_Target,
    Merch_Revenue
FROM
    (SELECT 
        A7.BUSINESS_DATE,
            A7.UNIT_REGION,
            TRUNCATE((TOTAL_SALES / Total_Tickets), 0) APC,
            CONCAT(TRUNCATE((BEV_ONLY_SALES / Total_Tickets) * 100, 0), '%') Beverage_Incidence,
            CONCAT(TRUNCATE((Full_Item_QTY / Full_Reg_QTY) * 100, 0), '%') Full_Ratio,
            CONCAT(TRUNCATE((FOOD_SOLD_QTY / Total_Tickets) * 100, 0), '%') Food_Incidence,
            CONCAT(TRUNCATE((MEAL_SOLD_QTY / Total_Tickets) * 100, 0), '%') Meals_Incidence,
            CONCAT(TRUNCATE((Bakery_SOLD_QTY / Total_Tickets) * 100, 0), '%') Bakery_Incidence,
            CONCAT(TRUNCATE((COLD_SOLD_QTY / Total_Tickets) * 100, 0), '%') Cold_Incidence,
            CONCAT(TRUNCATE((MERCH_SOLD_AMT / TOTAL_SALES) * 100, 0), '%') Merch_Revenue
    FROM
        (SELECT 
        A1.BUSINESS_DATE,
            A1.UNIT_REGION,
            COALESCE(A1.Total_Tickets, 0) Total_Tickets,
            COALESCE(A2.BEV_ONLY_SALES, 0) BEV_ONLY_SALES,
            COALESCE(FULL_Item_QTY, 0) Full_Item_QTY,
            COALESCE(Full_Reg_QTY, 0) Full_Reg_QTY,
            COALESCE(MEAL_SOLD_QTY, 0) MEAL_SOLD_QTY,
            COALESCE(Bakery_SOLD_QTY, 0) Bakery_SOLD_QTY,
            COALESCE(COLD_SOLD_QTY, 0) COLD_SOLD_QTY,
            COALESCE(FOOD_SOLD_QTY, 0) FOOD_SOLD_QTY,
            COALESCE(MERCH_SOLD_AMT, 0) MERCH_SOLD_AMT,
            COALESCE(TOTAL_SALES, 0) TOTAL_SALES
    FROM
        (SELECT 
        A.BUSINESS_DATE,
            A.UNIT_REGION,
            COUNT(A.order_ID) Total_Tickets
    FROM
        (SELECT 
        od.ORDER_ID,
        (CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN DATE(DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 6 HOUR))
            ELSE DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        END) AS BUSINESS_DATE,
            ud.UNIT_REGION
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AS A
    GROUP BY 1 , 2) AS A1
    LEFT JOIN (SELECT 
        B.UNIT_REGION,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY_SALES
    FROM
        (SELECT 
        od1.ORDER_ID,
            ud1.UNIT_ID,
            ud1.UNIT_REGION,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od1
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od1.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER' AND od1.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd1.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2 , 3) AS B
    GROUP BY 1) A2 ON A1.UNIT_REGION = A2.UNIT_REGION
    LEFT JOIN (SELECT 
        UNIT_REGION,
            SUM(CASE
                WHEN A3.dimension = 'Full' THEN A3.QTY
                ELSE 0
            END) 'Full_Item_QTY',
            SUM(CASE
                WHEN A3.dimension IN ('Full' , 'Regular') THEN A3.QTY
                ELSE 0
            END) 'Full_Reg_QTY'
    FROM
        (SELECT 
        od.ORDER_ID, UNIT_REGION, oi.dimension, SUM(oi.QUANTITY) QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN ('10' , '11', '12', '14', '15', '50', '60', '130', '140', '150', '170', '272', '300', '310', '420', '450', '460', '480', '979', '1060', '1061', '1205', '1282', '1292', '1293', '1294')
    GROUP BY 1 , 2) AS A3
    GROUP BY 1) A4 ON A4.UNIT_REGION = A1.UNIT_REGION
    LEFT JOIN (SELECT 
        UD.UNIT_REGION, SUM(oi.QUANTITY) MEAL_SOLD_QTY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL UD ON UD.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (1028 , 1063, 1103, 1111, 1112, 1151, 1222, 1223)
    GROUP BY 1) A5 ON A5.UNIT_REGION = A1.UNIT_REGION
    LEFT JOIN (SELECT 
        AA.UNIT_REGION,
            SUM(CASE
                WHEN ATTRIBUTE = '5 - Bakery' THEN QUANTITY
                ELSE 0
            END) Bakery_SOLD_QTY,
            SUM(CASE
                WHEN ATTRIBUTE = '2 - Beverages Cold' THEN QUANTITY
                ELSE 0
            END) COLD_SOLD_QTY,
            SUM(CASE
                WHEN ATTRIBUTE IN ('3 - Food Veg' , '4 - Food NonVeg', '6 - Combos') THEN QUANTITY
                ELSE 0
            END) FOOD_SOLD_QTY,
            SUM(CASE
                WHEN ATTRIBUTE = '7 - Merchandise' THEN TOTAL_SALES
                ELSE 0
            END) MERCH_SOLD_AMT,
            SUM(TOTAL_SALES) TOTAL_SALES
    FROM
        (SELECT 
        UD.UNIT_REGION,
            PDB.ATTRIBUTE1 ATTRIBUTE,
            SUM(oi.QUANTITY) QUANTITY,
            ROUND(SUM(oi.AMOUNT_PAID), 0) TOTAL_SALES
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    JOIN KETTLE_MASTER.UNIT_DETAIL UD ON UD.UNIT_ID = od.UNIT_ID
    JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID = UD.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND (oi.IS_COMPLIMENTARY ='N' OR oi.IS_COMPLIMENTARY IS NULL)
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1290, 1218, 1291, 1295)
    GROUP BY 1 , 2) AA
    GROUP BY 1) A6 ON A6.UNIT_REGION = A1.UNIT_REGION) A7
    GROUP BY 1 , 2 , 3 , 4 DESC
    ORDER BY UNIT_REGION) A8
        LEFT JOIN
    (SELECT 
        BUSINESS_DATE,
            UNIT_REGION,
            TRUNCATE((SUM(APC * TICKETS_DINE_IN)) / SUM(TICKETS_DINE_IN), 0) APC_TARGET,
            CONCAT(TRUNCATE((SUM(BEVERAGE) / SUM(TICKETS_DINE_IN)) * 100, 0), '%') Beverage_Target,
            CONCAT(TRUNCATE((SUM(DIMENSION_FULL) / SUM(DIMENSION_REGULAR)) * 100, 0), '%') Full_Ratio_Target,
            CONCAT(TRUNCATE((SUM(FOOD) / SUM(TICKETS_DINE_IN)) * 100, 0), '%') Food_Target,
            CONCAT(TRUNCATE((SUM(MEAL) / SUM(TICKETS_DINE_IN)) * 100, 0), '%') Meals_Target,
            CONCAT(TRUNCATE((SUM(CAKE) / SUM(TICKETS_DINE_IN)) * 100, 0), '%') Bakery_Target,
            CONCAT(TRUNCATE((SUM(COLD) / SUM(TICKETS_DINE_IN)) * 100, 0), '%') Cold_Target,
            CONCAT(TRUNCATE((SUM(MERCHANDISE_SALES) / SUM(NET_SALE)) * 100, 0), '%') Merch_Target
    FROM
        KETTLE.MONTHLY_TARGETS_DATA A
    JOIN KETTLE_MASTER.UNIT_DETAIL UD ON UD.UNIT_ID = A.UNIT_ID
    WHERE
        RECORD_STATUS = 'ACTIVE'
            AND BUSINESS_DATE >= (CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN DATE(DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 6 HOUR))
            ELSE DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        END)
            AND TARGET_TYPE = 'DAILY'
    GROUP BY 1 , 2) T1 ON T1.UNIT_REGION = A8.UNIT_REGION
        AND T1.BUSINESS_DATE = A8.BUSINESS_DATE
		
                                                ]]>
                                        </content>
                                </report>

                                <report id="10" name="Area Manager Wise" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT 
A8.AREA_MANAGER,
T1.APC_TARGET,
A8.APC,
T1.Beverage_Target,
A8.Beverage_Incidence,
Full_Ratio_Target,
A8.Full_Ratio,
Food_Target,
Food_Incidence,
Meals_Target,
Meals_Incidence,
Bakery_Target,
Bakery_Incidence,
Cold_Target,
Cold_Incidence,
Merch_Target,
Merch_Revenue
FROM
 (SELECT 
 A7.BUSINESS_DATE,
 A7.AREA_MANAGER,
TRUNCATE((TOTAL_SALES/Total_Tickets),0)APC,
CONCAT(TRUNCATE((BEV_ONLY_SALES/Total_Tickets)*100,0),'%')Beverage_Incidence,
CONCAT(TRUNCATE((Full_Item_QTY/Full_Reg_QTY)*100,0),'%')Full_Ratio,
CONCAT(TRUNCATE((FOOD_SOLD_QTY/Total_Tickets)*100,0),'%')Food_Incidence,
CONCAT(TRUNCATE((MEAL_SOLD_QTY/Total_Tickets)*100,0),'%')Meals_Incidence,
CONCAT(TRUNCATE((Bakery_SOLD_QTY/Total_Tickets)*100,0),'%')Bakery_Incidence,
CONCAT(TRUNCATE((COLD_SOLD_QTY/Total_Tickets)*100,0),'%')Cold_Incidence,
CONCAT(TRUNCATE((MERCH_SOLD_AMT/TOTAL_SALES)*100,0),'%')Merch_Revenue
from
(SELECT 
A1.BUSINESS_DATE,
A1.AREA_MANAGER,
coalesce(A1.Total_Tickets,0)Total_Tickets,
coalesce(A2.BEV_ONLY_SALES,0)BEV_ONLY_SALES ,
coalesce(FULL_Item_QTY,0)Full_Item_QTY,
coalesce(Full_Reg_QTY,0)Full_Reg_QTY,
coalesce(MEAL_SOLD_QTY,0)MEAL_SOLD_QTY,
coalesce(Bakery_SOLD_QTY,0)Bakery_SOLD_QTY,
coalesce(COLD_SOLD_QTY,0)COLD_SOLD_QTY,
coalesce(FOOD_SOLD_QTY,0)FOOD_SOLD_QTY,
coalesce(MERCH_SOLD_AMT,0)MERCH_SOLD_AMT,
coalesce(TOTAL_SALES,0)TOTAL_SALES
FROM
(SELECT 
A.BUSINESS_DATE,
A.AREA_MANAGER,
Count(A.order_ID)Total_Tickets 
FROM (SELECT od.ORDER_ID,(CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN DATE(DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 6 HOUR))
            ELSE DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        END) AS BUSINESS_DATE,
ED.EMP_NAME AREA_MANAGER
fROM KETTLE.ORDER_DETAIL od
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud on ud.UNIT_ID = od.UNIT_ID
INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=ud.UNIT_MANAGER
WHERE od.ORDER_STATUS <> 'CANCELLED'
AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
AND ORDER_SOURCE in ('CAFE','TAKE_AWAY')
AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
AND pd.PRODUCT_ID NOT IN (1026,1027,1048,1056,1157,1158,1176,1290,1218,1291,1295)
GROUP BY 1,2)as A
GROUP BY 1,2) AS A1 
LEFT JOIN  (SELECT B.AREA_MANAGER, 
SUM(CASE WHEN PTYPE IN ('5','6','6,5','5,6') THEN 1 ELSE 0 END) BEV_ONLY_SALES 
FROM (SELECT od1.ORDER_ID, 
ud1.UNIT_ID,
ED.EMP_NAME AREA_MANAGER,
GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
FROM KETTLE.ORDER_DETAIL od1
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 on ud1.UNIT_ID = od1.UNIT_ID
iNNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=ud1.UNIT_MANAGER
WHERE od1.ORDER_STATUS <> 'CANCELLED'
AND ORDER_TYPE = 'ORDER' AND od1.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
AND ORDER_SOURCE in ('CAFE','TAKE_AWAY')
AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
AND pd1.PRODUCT_ID NOT IN (1026,1027,1048,1056,1157,1158,1176,1290,1218,1291,1295)
GROUP BY 1,2,3)as B
GROUP BY 1)A2 ON A1.AREA_MANAGER = A2.AREA_MANAGER
LEFT JOIN                       
(SELECT AREA_MANAGER,
SUM(CASE WHEN A3.dimension ='Full' then   A3.QTY ELSE 0 END )'Full_Item_QTY',
SUM(CASE WHEN A3.dimension in ('Full','Regular') then A3.QTY ELSE 0 END ) 'Full_Reg_QTY'
FROM (SELECT od.ORDER_ID,	 ED.EMP_NAME AREA_MANAGER,
oi.dimension,sum(oi.QUANTITY)QTY
FROM KETTLE.ORDER_DETAIL od
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud on ud.UNIT_ID = od.UNIT_ID
INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=ud.UNIT_MANAGER
WHERE od.ORDER_STATUS <> 'CANCELLED'
AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
AND ORDER_SOURCE in ('CAFE','TAKE_AWAY')
AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
 AND pd.PRODUCT_ID IN ('10','11','12','14','15','50','60','130','140','150','170','272','300','310','420',
'450','460','480','979','1060','1061','1205','1282','1292','1293','1294')
GROUP BY 1,2)as A3 GROUP BY 1) A4 ON A4.AREA_MANAGER = A1.AREA_MANAGER
LEFT JOIN (SELECT ED.EMP_NAME AREA_MANAGER, SUM(oi.QUANTITY) MEAL_SOLD_QTY
FROM KETTLE.ORDER_DETAIL od
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
INNER JOIN KETTLE_MASTER.UNIT_DETAIL UD ON UD.UNIT_ID=od.UNIT_ID
JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=UD.UNIT_MANAGER
WHERE od.ORDER_STATUS <> 'CANCELLED' AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119) AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
AND pd.PRODUCT_ID IN (1028 , 1063, 1103, 1111, 1112, 1151, 1222, 1223)
GROUP BY 1) A5 ON A5.AREA_MANAGER=A1.AREA_MANAGER
LEFT JOIN (SELECT AA.AREA_MANAGER,
SUM(CASE WHEN ATTRIBUTE='5 - Bakery' THEN QUANTITY ELSE 0 END )Bakery_SOLD_QTY,
SUM(CASE WHEN ATTRIBUTE='2 - Beverages Cold' THEN QUANTITY ELSE 0 END )COLD_SOLD_QTY,
SUM(CASE WHEN ATTRIBUTE IN ('3 - Food Veg','4 - Food NonVeg','6 - Combos') THEN QUANTITY ELSE 0 END )FOOD_SOLD_QTY,
SUM(CASE WHEN ATTRIBUTE='7 - Merchandise' THEN TOTAL_SALES ELSE 0 END )MERCH_SOLD_AMT,
SUM(TOTAL_SALES)TOTAL_SALES
FROM
(SELECT 
    ED.EMP_NAME AREA_MANAGER,
    PDB.ATTRIBUTE1 ATTRIBUTE,
    SUM(oi.QUANTITY) QUANTITY,
    ROUND(SUM(oi.AMOUNT_PAID), 0)TOTAL_SALES
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    JOIN 
    KETTLE_MASTER.UNIT_DETAIL UD ON UD.UNIT_ID=od.UNIT_ID
JOIN 
KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=UD.UNIT_MANAGER
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
        AND ORDER_SOURCE IN ( 'CAFE','TAKE_AWAY')
        AND (oi.IS_COMPLIMENTARY ='N' OR oi.IS_COMPLIMENTARY IS NULL)
	    AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND oi.PRODUCT_ID NOT IN (1026,1027,1048,1056,1157,1158,1176,1290,1218,1291,1295)
GROUP BY 1,2)AA GROUP BY 1)A6 ON A6.AREA_MANAGER= A1.AREA_MANAGER)A7 
group by 1,2,3,4 
DESC ORDER BY AREA_MANAGER)A8
LEFT JOIN 
(SELECT 
BUSINESS_DATE,
ED.EMP_NAME AREA_MANAGER, 
TRUNCATE((SUM(APC*TICKETS_DINE_IN))/SUM(TICKETS_DINE_IN),0) APC_TARGET,
CONCAT(TRUNCATE((SUM(BEVERAGE)/SUM(TICKETS_DINE_IN))*100,0),'%')Beverage_Target,
CONCAT(TRUNCATE((SUM(DIMENSION_FULL)/SUM(DIMENSION_REGULAR))*100,0),'%')Full_Ratio_Target,
CONCAT(TRUNCATE((SUM(FOOD)/SUM(TICKETS_DINE_IN))*100,0),'%')Food_Target,
CONCAT(TRUNCATE((SUM(MEAL)/SUM(TICKETS_DINE_IN))*100,0),'%')Meals_Target,
CONCAT(TRUNCATE((SUM(CAKE)/SUM(TICKETS_DINE_IN))*100,0),'%')Bakery_Target,
CONCAT(TRUNCATE((SUM(COLD)/SUM(TICKETS_DINE_IN))*100,0),'%')Cold_Target,
CONCAT(TRUNCATE((SUM(MERCHANDISE_SALES)/SUM(NET_SALE))*100,0),'%')Merch_Target
FROM KETTLE.MONTHLY_TARGETS_DATA A
JOIN KETTLE_MASTER.UNIT_DETAIL UD ON UD.UNIT_ID=A.UNIT_ID
JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=UD.UNIT_MANAGER
WHERE RECORD_STATUS ='ACTIVE'
AND BUSINESS_DATE >=(CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN DATE(DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 6 HOUR))
            ELSE DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        END)
AND TARGET_TYPE ='DAILY'
GROUP BY 1,2)T1 ON 
T1.AREA_MANAGER=A8.AREA_MANAGER
AND 
T1.BUSINESS_DATE=A8.BUSINESS_DATE                                                ]]>
                                        </content>
                                </report>





                                <report id="10" name="All Cafes" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
SELECT 
A8.UNIT_NAME,
A8.AREA_MANAGER,
TRUNCATE(T1.APC_TARGET,0)APC_TARGET,
A8.APC,
T1.Beverage_Target,
A8.Beverage_Incidence,
Full_Ratio_Target,
A8.Full_Ratio,
Food_Target,
Food_Incidence,
Meals_Target,
Meals_Incidence,
Bakery_Target,
Bakery_Incidence,
Cold_Target,
Cold_Incidence,
Merch_Target,
Merch_Revenue
FROM
 (SELECT 
 A7.BUSINESS_DATE,
 A7.UNIT_ID,
 A7.UNIT_NAME,
 A7.AREA_MANAGER,
TRUNCATE((TOTAL_SALES/Total_Tickets),0)APC,
CONCAT(TRUNCATE((BEV_ONLY_SALES/Total_Tickets)*100,0),'%')Beverage_Incidence,
CONCAT(TRUNCATE((Full_Item_QTY/Full_Reg_QTY)*100,0),'%')Full_Ratio,
CONCAT(TRUNCATE((FOOD_SOLD_QTY/Total_Tickets)*100,0),'%')Food_Incidence,
CONCAT(TRUNCATE((MEAL_SOLD_QTY/Total_Tickets)*100,0),'%')Meals_Incidence,
CONCAT(TRUNCATE((Bakery_SOLD_QTY/Total_Tickets)*100,0),'%')Bakery_Incidence,
CONCAT(TRUNCATE((COLD_SOLD_QTY/Total_Tickets)*100,0),'%')Cold_Incidence,
CONCAT(TRUNCATE((MERCH_SOLD_AMT/TOTAL_SALES)*100,0),'%')Merch_Revenue
from
(SELECT 
A1.BUSINESS_DATE,
A1.UNIT_ID,
A1.UNIT_NAME,
A1.AREA_MANAGER,
coalesce(A1.Total_Tickets,0)Total_Tickets,
coalesce(A2.BEV_ONLY_SALES,0)BEV_ONLY_SALES ,
coalesce(FULL_Item_QTY,0)Full_Item_QTY,
coalesce(Full_Reg_QTY,0)Full_Reg_QTY,
coalesce(MEAL_SOLD_QTY,0)MEAL_SOLD_QTY,
coalesce(Bakery_SOLD_QTY,0)Bakery_SOLD_QTY,
coalesce(COLD_SOLD_QTY,0)COLD_SOLD_QTY,
coalesce(FOOD_SOLD_QTY,0)FOOD_SOLD_QTY,
coalesce(MERCH_SOLD_AMT,0)MERCH_SOLD_AMT,
coalesce(TOTAL_SALES,0)TOTAL_SALES
FROM
(SELECT 
A.BUSINESS_DATE,
A.UNIT_ID, 
A.UNIT_NAME,
A.AREA_MANAGER,
Count(A.order_ID)Total_Tickets 
FROM (SELECT
od.ORDER_ID,(CASE
                WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN DATE(DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 6 HOUR))
                ELSE DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
            END) AS BUSINESS_DATE,
    ud.UNIT_NAME,
ED.EMP_NAME AREA_MANAGER,
ud.UNIT_ID
fROM KETTLE.ORDER_DETAIL od
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud on ud.UNIT_ID = od.UNIT_ID
INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ED ON ED.EMP_ID=ud.UNIT_MANAGER
WHERE od.ORDER_STATUS <> 'CANCELLED'
AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
AND ORDER_SOURCE in ('CAFE','TAKE_AWAY')
AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00') 
AND pd.PRODUCT_ID NOT IN (1026,1027,1048,1056,1157,1158,1176,1290,1218,1291,1295)
GROUP BY 1,2,3,4,5)as A
GROUP BY 1,2,3,4) AS A1 
LEFT JOIN  
(SELECT B.UNIT_ID, 
SUM(CASE WHEN PTYPE IN ('5','6','6,5','5,6') THEN 1 ELSE 0 END) BEV_ONLY_SALES 
FROM (
SELECT od1.ORDER_ID, 
ud1.UNIT_ID,
GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
FROM KETTLE.ORDER_DETAIL od1
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud1 on ud1.UNIT_ID = od1.UNIT_ID
iNNER JOIN KETTLE.ORDER_ITEM oi1 ON od1.ORDER_ID = oi1.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON oi1.PRODUCT_ID = pd1.PRODUCT_ID
WHERE od1.ORDER_STATUS <> 'CANCELLED'
AND ORDER_TYPE = 'ORDER' AND od1.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
AND ORDER_SOURCE in ('CAFE','TAKE_AWAY')
AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
AND pd1.PRODUCT_ID NOT IN (1026,1027,1048,1056,1157,1158,1176,1218,1291,1295)
GROUP BY 1,2)as B
GROUP BY 1)A2 ON A1.UNIT_ID = A2.UNIT_ID
LEFT JOIN                       
(SELECT 
A3.UNIT_ID,
SUM(CASE WHEN A3.dimension ='Full' then   A3.QTY ELSE 0 END )'Full_Item_QTY',
SUM(CASE WHEN A3.dimension in ('Full','Regular') then A3.QTY ELSE 0 END ) 'Full_Reg_QTY'
FROM (SELECT od.ORDER_ID,	 ud.UNIT_ID,
oi.dimension,sum(oi.QUANTITY)QTY
FROM KETTLE.ORDER_DETAIL od
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud on ud.UNIT_ID = od.UNIT_ID
INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
WHERE od.ORDER_STATUS <> 'CANCELLED'
AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
AND ORDER_SOURCE in ('CAFE','TAKE_AWAY')
AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
 AND pd.PRODUCT_ID IN ('10','11','12','14','15','50','60','130','140','150','170','272','300','310','420',
'450','460','480','979','1060','1061','1205','1282','1292','1293','1294')
GROUP BY 1,2)as A3 GROUP BY 1) A4 ON A4.UNIT_ID = A1.UNIT_ID
LEFT JOIN (SELECT ud.UNIT_ID, SUM(oi.QUANTITY) MEAL_SOLD_QTY
FROM KETTLE.ORDER_DETAIL od
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
WHERE od.ORDER_STATUS <> 'CANCELLED' AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119) AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
AND pd.PRODUCT_ID IN (1028 , 1063, 1103, 1111, 1112, 1151, 1222, 1223)
GROUP BY 1) A5 ON A5.UNIT_ID=A1.UNIT_ID
LEFT JOIN (
SELECT AA.UNIT_ID,
SUM(CASE WHEN ATTRIBUTE='5 - Bakery' THEN QUANTITY ELSE 0 END )Bakery_SOLD_QTY,
SUM(CASE WHEN ATTRIBUTE='2 - Beverages Cold' THEN QUANTITY ELSE 0 END )COLD_SOLD_QTY,
SUM(CASE WHEN ATTRIBUTE IN ('3 - Food Veg','4 - Food NonVeg','6 - Combos') THEN QUANTITY ELSE 0 END )FOOD_SOLD_QTY,
SUM(CASE WHEN ATTRIBUTE='7 - Merchandise' THEN TOTAL_SALES ELSE 0 END )MERCH_SOLD_AMT,
SUM(TOTAL_SALES)TOTAL_SALES
FROM
(SELECT 
    od.UNIT_ID,
    PDB.ATTRIBUTE1 ATTRIBUTE,
    SUM(oi.QUANTITY) QUANTITY,
	SUM(oi.AMOUNT_PAID) TOTAL_SALES
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND ORDER_TYPE = 'ORDER' AND od.UNIT_ID NOT IN (26007,26008,26009,26010,26011,26097,26098,26119)
        AND ORDER_SOURCE IN ( 'CAFE','TAKE_AWAY')
        AND (oi.IS_COMPLIMENTARY ='N' OR oi.IS_COMPLIMENTARY IS NULL)
        AND BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND oi.PRODUCT_ID NOT IN (1026,1027,1048,1056,1157,1158,1176,1218,1291,1295)
GROUP BY 1,2)AA GROUP BY 1)A6 ON A6.UNIT_ID= A1.UNIT_ID)A7 
group by 1,2,3,4 
DESC ORDER BY AREA_MANAGER)A8
LEFT JOIN 
(
SELECT BUSINESS_DATE,
UNIT_ID, 
APC APC_TARGET,
CONCAT(TRUNCATE((BEVERAGE/TICKETS_DINE_IN)*100,0),'%')Beverage_Target,
CONCAT(TRUNCATE((DIMENSION_FULL/DIMENSION_REGULAR)*100,0),'%')Full_Ratio_Target,
CONCAT(TRUNCATE((FOOD/TICKETS_DINE_IN)*100,0),'%')Food_Target,
CONCAT(TRUNCATE((MEAL/TICKETS_DINE_IN)*100,0),'%')Meals_Target,
CONCAT(TRUNCATE((CAKE/TICKETS_DINE_IN)*100,0),'%')Bakery_Target,
CONCAT(TRUNCATE((COLD/TICKETS_DINE_IN)*100,0),'%')Cold_Target,
CONCAT(TRUNCATE((MERCHANDISE_SALES/NET_SALE)*100,0),'%')Merch_Target
FROM KETTLE.MONTHLY_TARGETS_DATA 
WHERE RECORD_STATUS ='ACTIVE'
AND TARGET_TYPE ='DAILY'
AND BUSINESS_DATE = (CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN DATE(DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 6 HOUR))
            ELSE DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        END)
GROUP BY 1,2)T1 ON 
T1.UNIT_ID=A8.UNIT_ID 
AND 
T1.BUSINESS_DATE=A8.BUSINESS_DATE

                                                ]]>
                                        </content>
                                </report>
                </reports>
        </category>
</categories>
</ReportCategories>

