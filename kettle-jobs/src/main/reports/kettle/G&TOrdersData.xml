<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="GNT Orders Data: Confidential! " type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>

                                
                                <report id="11" name="CAFE LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
            
SELECT 
    *
FROM
    (SELECT 
        UNIT_NAME,
            UNIT_REGION,
            ed.EMP_NAME L2_LEVEL,
            OD.ORDER_ID,
            OD.BILLING_SERVER_TIME,
            ORDER_STATUS,
            CI.FIRST_NAME,
            CI.CONTACT_NUMBER,
            GROUP_CONCAT(DISTINCT OI.PRODUCT_NAME) PRODUCTS,
            SUM(AMOUNT_PAID) AMOUNT
    FROM
        KETTLE.ORDER_DETAIL OD
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON OD.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM OI ON OD.ORDER_ID = OI.ORDER_ID
    INNER JOIN KETTLE.CUSTOMER_INFO CI ON CI.CUSTOMER_ID = OD.CUSTOMER_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
    WHERE
        OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND OD.BRAND_ID = 3
    GROUP BY 1 , 2 , 3 , 4 , 5 , 6 , 7 , 8
    ORDER BY EMP_NAME) a;
    
    
    
                                                ]]>
                                        </content>
 
                               </report>
 
 
 
              </reports>
        </category>
   </categories>
</ReportCategories>


