<ReportCategories xmlns="http://www.w3schools.com">
<categories>
<!--<category name="Intra Day Dispatch and Delivery Delay Details"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
                        <reports>
                                 <report id="1" name="Intra Day Dispatch and Delivery Delay Details" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
SELECT
    od.GENERATED_ORDER_ID GENERATED_ORDER_ID,
    cai.ADDRESS_LINE_1 ADDRESS_LINE_1,
    cai.ADDRESS_LINE_2 ADDRESS_LINE_2,
    cai.LOCALITY LOCALITY,
    od.ORDER_ID ORDER_ID,
    ud.UNIT_NAME UNIT_NAME,
    od.SETTLED_AMOUNT ORDER_VALUE,
    ci.FIRST_NAME CUSTOMER_NAME,
    ci.CONTACT_NUMBER CONTACT_NUMBER,
    oen.ENTRY_TYPE DELAY_TYPE,
    DATE_FORMAT(od.BILLING_SERVER_TIME, '%d %b %Y %T') CREATED_AT,
    CASE
        WHEN ose.UPDATE_TIME IS NULL THEN - 1
        ELSE TIME_TO_SEC(TIMEDIFF(ose.UPDATE_TIME, od.BILLING_SERVER_TIME)) / 60
    END DELIVERY_ELAPSED_TIME
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE.CUSTOMER_ADDRESS_INFO cai ON cai.ADDRESS_ID = od.DELIVERY_ADDRESS
        INNER JOIN
    KETTLE.ORDER_EMAIL_NOTIFICATION oen ON oen.ORDER_ID = od.ORDER_ID
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT JOIN
    KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TRANSITION_STATUS = 'SUCCESS'
        AND (ose.FROM_STATUS = 'SETTLED'
        AND ose.TO_STATUS = 'DELIVERED')
WHERE
    oen.ENTRY_TYPE IN ('DISPATCH_DELAY' , 'DELIVERY_DELAY')
        AND oen.IS_EMAIL_DELIVERED = 'Y'
        AND oen.EXECUTION_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND od.DELIVERY_PARTNER_ID = 8
GROUP BY od.ORDER_ID ORDER BY od.ORDER_ID DESC

                                     ]]></content>
                                </report>

                        </reports>
</category>-->
</categories>
</ReportCategories>

