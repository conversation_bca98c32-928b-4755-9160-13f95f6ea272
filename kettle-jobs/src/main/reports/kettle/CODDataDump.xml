<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
			<category name="COD Order Dump"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="1" name="COD Order Dump" executionType="SQL" skipInline="true"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
					
SELECT
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    od.*,
    dr.<PERSON><PERSON>_NAME,
    cp.PARTNER_DISPLAY_NAME,
    dp.PARTNER_DISPLAY_NAME,
    ci.*,
    ai.*
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD'
 AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
ORDER BY ud.UNIT_NAME, BUSINESS_DATE

				     ]]>
					</content>
				</report>
			</reports>
		</category>
		<!--<category name="DINE/COD LSM REPORT" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Total" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    TOTAL,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT 
        TOTAL,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT 
        'AllRegions' AS TOTAL,
            ORDER_SOURCE,
            A.ORDER_ID,
            OFFER_CODE,
            PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) A
    GROUP BY 1) Z;
]]>
					</content>
				</report>
				<report id="1" name="Region Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    UNIT_REGION,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT 
        UNIT_REGION,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT 
        UNIT_REGION,
            ORDER_SOURCE,
            A.ORDER_ID,
            OFFER_CODE,
            PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) A
    GROUP BY 1) Z
						]]>
					</content>
				</report>
				<report id="1" name="Manager Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    UCASE(EMP_NAME) AREA_MANAGER,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT 
        EMP_NAME,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT 
        EMP_NAME, ORDER_SOURCE, A.ORDER_ID, OFFER_CODE, PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL C ON C.EMP_ID = B.UNIT_MANAGER
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) A
    GROUP BY 1) Z
       
    
						]]>
					</content>
				</report>
				<report id="1" name="Unit Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[	

SELECT 
    UNIT_NAME,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT 
        UNIT_NAME,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER' , 'MUSKAFREE', 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT 
        UNIT_NAME, ORDER_SOURCE, A.ORDER_ID, OFFER_CODE, PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) A
    GROUP BY 1) Z
    

					]]>
					</content>
				</report>						
			</reports>
		</category>-->
</categories>
</ReportCategories>

