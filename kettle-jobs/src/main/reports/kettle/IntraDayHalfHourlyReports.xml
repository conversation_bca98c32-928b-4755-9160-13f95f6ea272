<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<!--<category name="CHAI CHAKHNA DRIVE" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Total" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM  (SELECT 
    S.TOTAL,
 FEAST_TARGETS AS 'CCB TARGET',
    FEAST_AMOUNT 'CCB ACTUAL QTY',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'CCB ACH%'
FROM
    (SELECT 
        'AllRegions' AS TOTAL, SUM(GIFT_CARD) FEAST_TARGETS
    FROM
        CLM_ANALYTICS.MAR7_TARGETS
    GROUP BY 1) S
        LEFT JOIN
    (SELECT 
        'AllRegions' AS TOTAL,
            SUM(oi.QUANTITY) AS FEAST_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER' 
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR7_TARGETS)
            AND PRODUCT_ID IN (1169,1170,1171)
    GROUP BY 1) T ON T.TOTAL = S.TOTAL) x    
						]]>
					</content>
				</report>
				<report id="1" name="Region Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
 SELECT * FROM  (SELECT 
    S.UNIT_REGION,
 FEAST_TARGETS AS 'CCB TARGET',
    FEAST_AMOUNT 'CCB ACTUAL QTY',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'CCB ACH%'
FROM
    (SELECT 
        UNIT_REGION, SUM(GIFT_CARD) FEAST_TARGETS
    FROM
        CLM_ANALYTICS.MAR7_TARGETS
    GROUP BY 1) S
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            SUM(oi.QUANTITY) AS FEAST_AMOUNT
		
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR7_TARGETS)
            AND PRODUCT_ID IN (1169,1170,1171)
    GROUP BY 1) T ON T.UNIT_REGION = S.UNIT_REGION) x   
						]]>
					</content>
				</report>
				<report id="1" name="Manager Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM  (SELECT 
    S.AREA_MANAGER,
 FEAST_TARGETS AS 'CCB TARGET',
    FEAST_AMOUNT 'CCB ACTUAL QTY',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'CCB ACH%'
FROM
    (SELECT 
        AREA_MANAGER, SUM(GIFT_CARD) FEAST_TARGETS,SUM(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.MAR7_TARGETS
    GROUP BY 1) S
        LEFT JOIN
    (SELECT 
        emp.EMP_NAME,
            SUM(oi.QUANTITY) AS FEAST_AMOUNT
		
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL emp ON ud.UNIT_MANAGER = emp.EMP_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR7_TARGETS)
            AND PRODUCT_ID IN (1169,1170,1171)
    GROUP BY 1) T ON T.EMP_NAME = S.AREA_MANAGER) x
	
						]]>
					</content>
				</report>
				<report id="1" name="Unit Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[	
  SELECT * FROM  (SELECT 
    S.UNIT_NAME,
 FEAST_TARGETS AS 'CCB TARGET',
    FEAST_AMOUNT 'CCB ACTUAL QTY',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'CCB ACH%'

FROM
    (SELECT 
        UNIT_ID,UNIT_NAME, SUM(GIFT_CARD) FEAST_TARGETS,SUM(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.MAR7_TARGETS
    GROUP BY 1,2) S
        LEFT JOIN
    (SELECT 
        od.UNIT_ID,
            SUM(oi.QUANTITY) AS FEAST_AMOUNT
		
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR7_TARGETS)
            AND PRODUCT_ID IN (1169,1170,1171)
    GROUP BY 1) T ON T.UNIT_ID = S.UNIT_ID) x
						]]>
					</content>
				</report>						
			</reports>
		</category>-->
<!--<category name="Gift Card Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Total" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM  (SELECT 
    S.TOTAL,
 FEAST_TARGETS AS 'GIFT_CARD_TARGET',
    FEAST_AMOUNT 'GIFT_CARD_AMOUNT',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'GIFT_CARD_ACH%'
FROM
    (SELECT 
        'AllRegions' AS TOTAL, SUM(GIFT_CARD) FEAST_TARGETS
    FROM
        CLM_ANALYTICS.MAR12_TARGETS
    GROUP BY 1) S
        LEFT JOIN
    (SELECT 
        'AllRegions' AS TOTAL,
            SUM(CASE WHEN PRODUCT_ID IN (1026, 1027, 1048) THEN oi.TOTAL_AMOUNT END) AS FEAST_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR12_TARGETS)
            AND PRODUCT_ID IN (1026, 1027, 1048)
    GROUP BY 1) T ON T.TOTAL = S.TOTAL) x;
    
						]]>
					</content>
				</report>
				<report id="1" name="Region Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
 SELECT * FROM  (SELECT 
    S.UNIT_REGION,
 FEAST_TARGETS AS 'GIFT_CARD_TARGET',
    FEAST_AMOUNT 'GIFT_CARD_AMOUNT',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'GIFT_CARD_ACH%'
FROM
    (SELECT 
        UNIT_REGION, SUM(GIFT_CARD) FEAST_TARGETS
    FROM
        CLM_ANALYTICS.MAR12_TARGETS
    GROUP BY 1) S
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            SUM(CASE WHEN PRODUCT_ID IN (1026, 1027, 1048) THEN oi.TOTAL_AMOUNT END) AS FEAST_AMOUNT
		
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR12_TARGETS)
            AND PRODUCT_ID IN (1026, 1027, 1048)
    GROUP BY 1) T ON T.UNIT_REGION = S.UNIT_REGION) x;
    
						]]>
					</content>
				</report>
				<report id="1" name="Manager Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM  (SELECT 
    S.AREA_MANAGER,
 FEAST_TARGETS AS 'GIFT_CARD_TARGET',
    FEAST_AMOUNT 'GIFT_CARD_AMOUNT',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'GIFT_CARD_ACH%'
FROM
    (SELECT 
        AREA_MANAGER, SUM(GIFT_CARD) FEAST_TARGETS,SUM(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.MAR12_TARGETS
    GROUP BY 1) S
        LEFT JOIN
    (SELECT 
        emp.EMP_NAME,
            SUM(CASE WHEN PRODUCT_ID IN (1026, 1027, 1048) THEN oi.TOTAL_AMOUNT END) AS FEAST_AMOUNT
		
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL emp ON ud.UNIT_MANAGER = emp.EMP_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR12_TARGETS)
            AND PRODUCT_ID IN (1026, 1027, 1048)
    GROUP BY 1) T ON T.EMP_NAME = S.AREA_MANAGER) x
	
						]]>
					</content>
				</report>
				<report id="1" name="Unit Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[	
 SELECT * FROM  (SELECT 
    S.UNIT_NAME,
 FEAST_TARGETS AS 'GIFT_CARD_TARGET',
    FEAST_AMOUNT 'GIFT_CARD_AMOUNT',
    CONCAT(ROUND((FEAST_AMOUNT * 100 / FEAST_TARGETS),
                    0),
            '%') 'GIFT_CARD_ACH%'

FROM
    (SELECT 
        UNIT_ID,UNIT_NAME, SUM(GIFT_CARD) FEAST_TARGETS,SUM(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.MAR12_TARGETS
    GROUP BY 1,2) S
        LEFT JOIN
    (SELECT 
        od.UNIT_ID,
            SUM(CASE WHEN PRODUCT_ID IN (1026, 1027, 1048) THEN oi.TOTAL_AMOUNT END) AS FEAST_AMOUNT
		
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
      od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')     AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER'
			AND od.UNIT_ID IN (SELECT UNIT_ID FROM CLM_ANALYTICS.MAR12_TARGETS)
            AND PRODUCT_ID IN (1026, 1027, 1048)
    GROUP BY 1) T ON T.UNIT_ID = S.UNIT_ID) x
					]]>
					</content>
				</report>						
			</reports>
		</category>-->
	</categories>
</ReportCategories>