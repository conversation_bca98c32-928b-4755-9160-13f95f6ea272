<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="Beat The Heat Intraday Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
                        <reports>
                                <report id="10" name="Area Manager Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT * FROM (

SELECT
    A.AREA_MANAGER,
    CONCAT(ROUND(A.BEV_ONLY_TICKETS * 100, 0), '%') BEV_ONLY_TARGET,
    CONCAT(ROUND(B.BEV_ONLY/B.TOTAL_ORDERS*100,0),'%')BEV_ONLY_ACH,
    CONCAT(ROUND(A.CAKE_PENETRATION * 100, 0), '%') CAKE_TARGET,
    CONCAT(ROUND((B.CAKE_ORDERS / B.TOTAL_ORDERS) * 100,
                    0),
            '%') CAKE_PENETRATION,
    CONCAT(ROUND(A.MERCHANDISE_PENETRATION * 100, 0),
            '%') MERCHANDISE_TARGET,
    CONCAT(ROUND((B.MER_ORDERS / B.TOTAL_ORDERS) * 100, 0),
            '%') MERCHANDISE,
    CONCAT(ROUND(A.MEALS_PENETRATION * 100, 0), '%') MEAL_TARGET,
    CONCAT(ROUND((B.MEAL_ORDERS / B.TOTAL_ORDERS) * 100,
                    0),
            '%') MEALS,
    CONCAT(ROUND(A.COLD_PENETRATION * 100, 0), '%') COLD_TARGET,
    CONCAT(ROUND((B.COLD_ORDERS / B.TOTAL_ORDERS) * 100,
                    0),
            '%') COLD_ONLY,
    CONCAT(ROUND(A.FULL_TO_REGULAR_RATIO * 100, 0),
            '%') FULL_RATIO_TARGET,
    CONCAT(ROUND(B.FULL_RATIO, 0), '%') AS FULL_RATIO
FROM
    (SELECT
        AM_ID,
            AREA_MANAGER,
            AVG(BEV_ONLY_TICKETS) BEV_ONLY_TICKETS,
            AVG(MEALS_PENETRATION) MEALS_PENETRATION,
            AVG(COLD_PENETRATION) COLD_PENETRATION,
            AVG(MERCHANDISE_PENETRATION) MERCHANDISE_PENETRATION,
            AVG(CAKE_PENETRATION) CAKE_PENETRATION,
            AVG(FULL_TO_REGULAR_RATIO) FULL_TO_REGULAR_RATIO
    FROM
        CLM_ANALYTICS.BEAT_THE_HEAT_TARGETS
    GROUP BY 1) AS A
        INNER JOIN
    (SELECT
        A.AM_ID,
            SUM(COALESCE(B.TOTAL_ORDERS, 0)) AS TOTAL_ORDERS,
            SUM(COALESCE(B.BEV_ONLY, 0)) AS BEV_ONLY,
            SUM(COALESCE(B.CAKE_ORDERS, 0)) AS CAKE_ORDERS,
            SUM(COALESCE(B.MER_ORDERS, 0)) AS MER_ORDERS,
            SUM(COALESCE(B.MEAL_ORDERS, 0)) AS MEAL_ORDERS,
            SUM(COALESCE(B.COLD_ORDERS, 0)) AS COLD_ORDERS,
            AVG(COALESCE(B.FULL_RATIO, 0)) AS FULL_RATIO
    FROM
        CLM_ANALYTICS.BEAT_THE_HEAT_TARGETS A
    LEFT JOIN (SELECT
        A.UNIT_ID,
            A.UNIT_NAME,
            TOTAL_ORDERS,
            BEV_ONLY,
            CAKE_ORDERS,
            MER_ORDERS,
            MEAL_ORDERS,
            COLD_ORDERS,
            FULL_RATIO
    FROM
        (SELECT
        od.UNIT_ID,
            UNIT_NAME,
            COUNT(DISTINCT od.ORDER_ID) TOTAL_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218)
    GROUP BY 1 , 2) A
    LEFT OUTER JOIN (SELECT
        UNIT_ID,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BUSINESS_DATE BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218)
    GROUP BY 1 , 2) A
    GROUP BY 1) B ON B.UNIT_ID = A.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) CAKE_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (1036 , 750, 1156, 740, 760, 870, 866, 1045, 1037, 1066, 865, 1182, 1183, 1110, 770, 1038, 1155, 1224, 1051, 1175, 1230, 869, 867)
    GROUP BY 1) C ON B.UNIT_ID = C.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) MER_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (691 , 692, 700, 710, 720, 730, 1057, 1059, 1143, 1144, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1219, 1237)
    GROUP BY 1) D ON B.UNIT_ID = D.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) MEAL_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (1028 , 1063, 1103, 1111, 1112, 1151, 1222, 1223)
    GROUP BY 1) E ON B.UNIT_ID = E.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) COLD_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND PRODUCT_TYPE = 6
    GROUP BY 1) F ON B.UNIT_ID = F.UNIT_ID
    LEFT OUTER JOIN (SELECT
        UNIT_ID,
            ROUND(FULL / (FULL + REGULAR) * 100, 0) AS FULL_RATIO
    FROM
        (SELECT
        od.UNIT_ID,
            SUM(CASE
                WHEN DIMENSION = 'FULL' THEN QUANTITY
            END) AS 'FULL',
            SUM(CASE
                WHEN DIMENSION = 'REGULAR' THEN QUANTITY
            END) AS 'REGULAR'
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND DIMENSION IN ('Regular' , 'Full')
            AND PRODUCT_ID IN (1205 , 14, 15, 140, 150, 10, 12, 50, 272, 11, 60, 130, 170, 420, 480, 300, 450, 460, 310, 1060, 1061)
    GROUP BY 1) G
    GROUP BY 1) H ON B.UNIT_ID = H.UNIT_ID) AS B ON A.UNIT_ID = B.UNIT_ID
    GROUP BY 1) AS B ON A.AM_ID = B.AM_ID
) as X

                                                ]]>
                                        </content>
                                </report>
                                <report id = "11" name="Unit Wise" executionType="EXCEL" returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT * FROM (
SELECT
    A.UNIT_ID,
    A.UNIT_NAME,
    CONCAT(ROUND(A.BEV_ONLY_TICKETS*100, 0),'%') BEV_ONLY_TARGET,
    CONCAT(ROUND(B.BEV_ONLY/B.TOTAL_ORDERS*100,0),'%') BEV_ONLY_ACH,
    CONCAT(ROUND(A.CAKE_PENETRATION*100, 0), '%') CAKE_TARGET,
    CONCAT(ROUND((B.CAKE_ORDERS/B.TOTAL_ORDERS)*100,0),'%') CAKE_PENETRATION,
    CONCAT(ROUND(A.MERCHANDISE_PENETRATION*100, 0), '%') MERCHANDISE_TARGET,
    CONCAT(ROUND((B.MER_ORDERS/B.TOTAL_ORDERS)*100,0),'%') MERCHANDISE,
    CONCAT(ROUND(A.MEALS_PENETRATION*100, 0), '%') MEAL_TARGET,
    CONCAT(ROUND((B.MEAL_ORDERS/B.TOTAL_ORDERS)*100,0),'%') MEALS,
    CONCAT(ROUND(A.COLD_PENETRATION*100, 0), '%') COLD_TARGET,
    CONCAT(ROUND((B.COLD_ORDERS/B.TOTAL_ORDERS)*100,0),'%') COLD_ONLY,
    CONCAT(ROUND(A.FULL_TO_REGULAR_RATIO * 100, 0), '%') FULL_RATIO_TARGET,
    B.FULL_RATIO
FROM
    CLM_ANALYTICS.BEAT_THE_HEAT_TARGETS A
        LEFT JOIN
    (SELECT
        A.UNIT_ID,
            A.UNIT_NAME,
            TOTAL_ORDERS,
            BEV_ONLY,
            CAKE_ORDERS,
            MER_ORDERS,
            MEAL_ORDERS,
            COLD_ORDERS,
            FULL_RATIO
    FROM
        (SELECT
        od.UNIT_ID,
            UNIT_NAME,
            COUNT(DISTINCT od.ORDER_ID) TOTAL_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218)
    GROUP BY 1 , 2) A
    LEFT OUTER JOIN (SELECT
        UNIT_ID,
            SUM(CASE
                WHEN PTYPE IN ('5' , '6', '6,5', '5,6') THEN 1
                ELSE 0
            END) BEV_ONLY
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            GROUP_CONCAT(DISTINCT PRODUCT_TYPE) PTYPE
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND BUSINESS_DATE BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048, 1056, 1157, 1158, 1176, 1218)
    GROUP BY 1 , 2) A
    GROUP BY 1) B ON B.UNIT_ID = A.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) CAKE_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (1036 , 750, 1156, 740, 760, 870, 866, 1045, 1037, 1066, 865, 1182, 1183, 1110, 770, 1038, 1155, 1224, 1051, 1175, 1230, 869, 867)
    GROUP BY 1) C ON B.UNIT_ID = C.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) MER_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (691 , 692, 700, 710, 720, 730, 1057, 1059, 1143, 1144, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1219, 1237)
    GROUP BY 1) D ON B.UNIT_ID = D.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) MEAL_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND pd.PRODUCT_ID IN (1028 , 1063, 1103, 1111, 1112, 1151, 1222, 1223)
    GROUP BY 1) E ON B.UNIT_ID = E.UNIT_ID
    LEFT OUTER JOIN (SELECT
        od.UNIT_ID, COUNT(DISTINCT od.ORDER_ID) COLD_ORDERS
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND PRODUCT_TYPE = 6
    GROUP BY 1) F ON B.UNIT_ID = F.UNIT_ID
    LEFT OUTER JOIN (SELECT
        UNIT_ID,
            CONCAT(ROUND(FULL / (FULL + REGULAR) * 100, 0), '%') AS FULL_RATIO
    FROM
        (SELECT
        od.UNIT_ID,
            SUM(CASE
                WHEN DIMENSION = 'FULL' THEN QUANTITY
            END) AS 'FULL',
            SUM(CASE
                WHEN DIMENSION = 'REGULAR' THEN QUANTITY
            END) AS 'REGULAR'
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE IN ('CAFE' , 'TAKE_AWAY')
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND DIMENSION IN ('Regular' , 'Full')
            AND PRODUCT_ID IN (1205 , 14, 15, 140, 150, 10, 12, 50, 272, 11, 60, 130, 170, 420, 480, 300, 450, 460, 310, 1060, 1061)
    GROUP BY 1) G
    GROUP BY 1) H ON B.UNIT_ID = H.UNIT_ID) AS B ON A.UNIT_ID = B.UNIT_ID

) as X
                                                ]]>
                                        </content>
                                </report>
                </reports>
        </category>
</categories>
</ReportCategories>