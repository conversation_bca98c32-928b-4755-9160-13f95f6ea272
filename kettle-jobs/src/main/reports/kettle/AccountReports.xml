<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
            ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="Pending Pull Transfer Reports" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
                        <reports>
                                <report id="1" name="Pending Pull Transfers" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[

SELECT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    DATE_FORMAT(pd.PULL_DATE, '%Y-%m-%d') PULL_DATE,
    pm.MODE_DESCRIPTION,
    pd.STATUS,
    ed.EMP_NAME,
    pd.PULL_AMOUNT,
    pd.COMMENT

FROM
    KETTLE.PULL_DETAIL pd,
    KETTLE_MASTER.UNIT_DETAIL ud,
    KETTLE_MASTER.EMPLOYEE_DETAIL ed,
    KETTLE_MASTER.PAYMENT_MODE pm
WHERE
    pd.CREATED_BY = ed.EMP_ID
        AND pd.PULL_UNIT = ud.UNIT_ID
        AND pd.STATUS NOT IN ( 'TRANSFERRED', 'CANCELLED')
        AND pd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
        order by ud.UNIT_NAME, pd.PAYMENT_MODE, PULL_DATE desc
                                                ]]></content>

                                </report>
                        </reports>
                </category>
				
				
				<category name="Pending Pull Transfer Reports" type="Automated"
                        accessCode="Automated" id="1" 
                        schedule="">
                        <reports>
                                <report id="1" name="Pending Pull Transfers" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                    
										<notifications>
										   <notification>
												<type>SLACK</type>
												<notificationIdIndex>8</notificationIdIndex>
												<notificationType>DIRECT</notificationType>
												<channel>technology</channel>
												<indexes>
													<messageIndex>0</messageIndex>
													<messageIndex>1</messageIndex>
													<messageIndex>2</messageIndex>
													<messageIndex>3</messageIndex>
													<messageIndex>4</messageIndex>
													<messageIndex>6</messageIndex>
												</indexes>
											</notification>
										</notifications>
										<content><![CDATA[

											SELECT y.* FROM(SELECT 
												x.UNIT,
												x.UNIT_NAME,
												x.PULL_DATE,
												x.DESCRIPTION,
												x.STATUS,
												x.EMPLOYEE_NAME,
												x.PULL_AMOUNT,
												x.COMMENT,
												ed.COMMUNICATION_CHANNEL as SLACK_CHANNEL
											FROM    
												(SELECT
													ud.UNIT_ID as UNIT,
													ud.UNIT_NAME as UNIT_NAME,
													DATE_FORMAT(pd.PULL_DATE, '%Y-%m-%d') PULL_DATE,
													pm.MODE_DESCRIPTION as DESCRIPTION,
													pd.STATUS as STATUS,
													ed.EMP_NAME as EMPLOYEE_NAME,
													pd.PULL_AMOUNT as PULL_AMOUNT,
													pd.COMMENT as COMMENT
												FROM
													KETTLE.PULL_DETAIL pd,
													KETTLE_MASTER.UNIT_DETAIL ud,
													KETTLE_MASTER.EMPLOYEE_DETAIL ed,
													KETTLE_MASTER.PAYMENT_MODE pm
												WHERE
														pd.CREATED_BY = ed.EMP_ID
														AND pd.PULL_UNIT = ud.UNIT_ID
														AND pd.STATUS NOT IN ( 'TRANSFERRED', 'CANCELLED')
														AND pd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
														AND pd.PULL_DATE >= DATE_ADD(DATE_SUB(CURDATE(), INTERVAL 1 DAY), INTERVAL 6 HOUR)
														order by ud.UNIT_NAME, pd.PAYMENT_MODE, PULL_DATE desc) 
											as x 
											inner join KETTLE_MASTER.UNIT_DETAIL ud on x.UNIT = ud.UNIT_ID
											inner join KETTLE_MASTER.EMPLOYEE_DETAIL ed on ud.UNIT_MANAGER=ed.EMP_ID)as y;
																							]]>
										</content>
                                </report>
                        </reports>
                </category>

				
                <category name="Pending Transfer Acknowledgement Reports" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>"
                        schedule="" attachmentType="CSV">
                        <reports>
                                <report id="1" name="Pending Transfer Acknowledgements" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[

SELECT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    pm.MODE_NAME,
    DATE_FORMAT(sd.SETTLEMENT_TIME,'%Y-%m-%d %H:%i:%S') AS SETTLEMENT_TIME,
    sd.SETTLEMENT_SERVICE_PROVIDER AS SETTLEMENT_RECIEPT,
    sd.TOTAL_AMOUNT,
    sd.SETTLEMENT_STATUS
FROM
    KETTLE.SETTLEMENT_DETAIL sd
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON sd.SETTLEMENT_UNIT = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PAYMENT_MODE pm ON sd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
WHERE
    sd.SETTLEMENT_STATUS <> 'SETTLED'
ORDER BY SETTLEMENT_TIME

                                                ]]></content>
                                </report>
                                <report id="2" name="Pending Pull Transfers" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[

SELECT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    DATE_FORMAT(pd.PULL_DATE, '%Y-%m-%d') PULL_DATE,
    pm.MODE_DESCRIPTION,
    pd.STATUS,
    ed.EMP_NAME,
    pd.PULL_AMOUNT,
    pd.COMMENT

FROM
    KETTLE.PULL_DETAIL pd,
    KETTLE_MASTER.UNIT_DETAIL ud,
    KETTLE_MASTER.EMPLOYEE_DETAIL ed,
    KETTLE_MASTER.PAYMENT_MODE pm
WHERE
    pd.CREATED_BY = ed.EMP_ID
        AND pd.PULL_UNIT = ud.UNIT_ID
        AND pd.STATUS NOT IN ( 'TRANSFERRED', 'CANCELLED')
        AND pd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
        order by ud.UNIT_NAME, pd.PAYMENT_MODE, PULL_DATE desc
                                                ]]></content>

                                </report>

		       </reports>
                </category>
        </categories>
</ReportCategories>

