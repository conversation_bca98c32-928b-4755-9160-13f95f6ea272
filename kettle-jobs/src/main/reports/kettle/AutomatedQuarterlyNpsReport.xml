<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="Quarterly NPS Report" type="Automated"
                  accessCode="Automated" id="1" fromEmail="<EMAIL>">
            <reports>
                <report id="1" name="Unit Wise Quarterly NPS report" executionType="SQL"
                        attachmentType="EXCEL" returnType="com.amazonaws.util.json.JSONArray">
                    <content>
                        <![CDATA[
                            SELECT
                                emp1.EMP_NAME AS 'CAFE_HANDLER',
                                ud.UNIT_REGION,
                                emp.EMP_NAME,
                                nps.DAY_WISE_NPS_SCORE_ID,
                                nps.BUSINESS_DATE,
                                nps.UNIT_ID,
                                nps.UNIT_NAME,
                                nps.UNIT_CATEGORY,
                                nps.TOTAL_POSITIVE,
                                nps.TOTAL_NEGATIVE,
                                nps.TOTAL_NEUTRAL,
                                nps.TOTAL_TICKET,
                                nps.POSITIVE_PERCENTAGE,
                                nps.NEGATIVE_PERCENTAGE,
                                nps.NEUTRAL_PERCENTAGE,
                                nps.NPS_SCORE,
                                nps.RANK_OF_THE_DAY,
                                nps.TOTAL_CAFES,
                                nps.TOTAL_CAFE_TICKETS,
                                nps.TOTAL_CAFE_NEGATIVE,
                                nps.TOTAL_CAFE_NEUTRAL,
                                nps.TOTAL_CAFE_POSITIVE,
                                nps.CAFE_POSITIVE_PERCENTAGE,
                                nps.CAFE_NEGATIVE_PERCENTAGE,
                                nps.CAFE_NEUTRAL_PERCENTAGE,
                                nps.TOTAL_COD_TICKETS,
                                nps.TOTAL_COD_NEGATIVE,
                                nps.TOTAL_COD_NEUTRAL,
                                nps.TOTAL_COD_POSITIVE,
                                nps.COD_POSITIVE_PERCENTAGE,
                                nps.COD_NEGATIVE_PERCENTAGE,
                                nps.COD_NEUTRAL_PERCENTAGE,
                                nps.CAFE_NPS_SCORE,
                                nps.COD_NPS_SCORE,
                                nps.NPS_CATEGORY
                            FROM
                                KETTLE_DUMP.DAY_WISE_NPS_SCORE nps,
                                KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
                                KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL emp,
                                KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL emp1
                            WHERE
                                nps.UNIT_ID = ud.UNIT_ID
                                    AND ud.UNIT_MANAGER = emp.EMP_ID
                                    AND emp1.EMP_ID = ud.CAFE_MANAGER
                                    AND nps.BUSINESS_DATE >= :startDate
                                    AND nps.BUSINESS_DATE <= :endDate
                                    AND nps.NPS_CATEGORY = 'DAILY_OVERALL'
                            ORDER BY nps.BUSINESS_DATE , nps.NPS_CATEGORY , nps.UNIT_ID
                        ]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="startDate" dataType="DATE" />
                        <param name="endDate" displayName="endDate" dataType="DATE" />
                    </params>
                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>