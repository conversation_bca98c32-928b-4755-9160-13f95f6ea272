<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
                <category name="COGS Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>,<EMAIL>">
                        <reports>
                                <report id="10" name="Overall System COGS" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

Select X.Last_7_days , Y._7_14_days,Z._14_21_days from (
Select
CONCAT(ROUND((((SUM(mp.TOTAL_COST))/(SUM(PRICE*QUANTITY)))*100),2),'%')AS Last_7_days
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
AND mp.TOTAL_COST IS NOT NULL
   )X , (Select  CONCAT(ROUND(SUM(mp.TOTAL_COST)/SUM(PRICE*QUANTITY)*100,2),'%')AS _7_14_days
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
AND mp.TOTAL_COST IS NOT NULL
   )Y , (Select CONCAT(ROUND(SUM(mp.TOTAL_COST)/SUM(PRICE*QUANTITY)*100,2),'%')AS _14_21_days
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
AND mp.TOTAL_COST IS NOT NULL
   )Z
                                                  ]]>
                                        </content>
                                </report>
                                <report id="10" name="Sub Category Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
Select K.* ,
E.SALES_SHARE_last_7_days,
F.SALES_SHARE_last_7_14_days,
D.SALES_SHARE_last_14_21_days,
E.AVG_COGS_last_7_days,
F.AVG_COGS_last_7_14_days,
D.AVG_COGS_last_14_21_days,
E.COGS_SHARE_last_7_days,
F.COGS_SHARE_last_7_14_days,
D.COGS_SHARE_last_14_21_days
 from 
(Select SUB_CAT from CLM_ANALYTICS.COGS_SUB_CAT) K
LEFT JOIN  
(Select B.*,
    CONCAT(ROUND((SALES_SHARE_last_7_days/100 * AVG_COGS_last_7_days/100)*100,2),'%') AS COGS_SHARE_last_7_days
from
    (Select 
        Y.SUB_CAT,
            CONCAT(ROUND((SUM(Y.SALES) / Z.TOTAL_SALES * 100), 2),'%') AS SALES_SHARE_last_7_days,
            CONCAT(ROUND((SUM(Y.TOTAL_COST) / SUM(Y.SALES) * 100), 2),'%') AS AVG_COGS_last_7_days
    from
        (Select 
        X . *,
            CASE
                WHEN PRICE = 0 THEN 'HIGH'
                WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 30)
                        AND RTL_CODE = 'BAKERY'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
                WHEN
                    COGS_PERC > 20
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 15 AND COGS_PERC <= 20)
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 15
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Food'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
                WHEN
                    COGS_PERC > 15
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 10 AND COGS_PERC <= 15)
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 10
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'LOW'
                WHEN
                    COGS_PERC > 35
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 30
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'LOW'
                WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Others'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Combos'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
            END AS SUB_CAT
    from
        (Select 
        ud.UNIT_ID,
            mp.MENU_PRODUCT_ID,
            mp.PRODUCT_NAME,
            mp.DIMENSION,
            RTL_CODE,
            SUM(TOTAL_COST) AS TOTAL_COST,
            SUM(PRICE * QUANTITY) AS SALES,
            ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
            ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
            mp.PRICE
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
    INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
    WHERE
            DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
            AND TOTAL_COST IS NOT NULL
    GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X) Y, (Select 
        SUM(PRICE * QUANTITY) AS TOTAL_SALES
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
) Z
    GROUP BY SUB_CAT) B)E ON E.SUB_CAT = K.SUB_CAT

LEFT join

(Select B.*,
    CONCAT(ROUND((SALES_SHARE_last_7_14_days/100 * AVG_COGS_last_7_14_days/100)*100,2),'%') AS COGS_SHARE_last_7_14_days
from
    (Select 
        Y.SUB_CAT,
            CONCAT(ROUND((SUM(Y.SALES) / Z.TOTAL_SALES * 100), 2),'%') AS SALES_SHARE_last_7_14_days,
            CONCAT(ROUND((SUM(Y.TOTAL_COST) / SUM(Y.SALES) * 100), 2),'%') AS AVG_COGS_last_7_14_days
    from
        (Select 
        X . *,
            CASE
                WHEN PRICE = 0 THEN 'HIGH'
                WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 30)
                        AND RTL_CODE = 'BAKERY'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
                WHEN
                    COGS_PERC > 20
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 15 AND COGS_PERC <= 20)
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 15
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Food'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
                WHEN
                    COGS_PERC > 15
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 10 AND COGS_PERC <= 15)
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 10
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'LOW'
                WHEN
                    COGS_PERC > 35
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 30
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'LOW'
                WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Others'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Combos'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
            END AS SUB_CAT
    from
        (Select 
        ud.UNIT_ID,
            mp.MENU_PRODUCT_ID,
            mp.PRODUCT_NAME,
            mp.DIMENSION,
            RTL_CODE,
            SUM(TOTAL_COST) AS TOTAL_COST,
            SUM(PRICE * QUANTITY) AS SALES,
            ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
            ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
            mp.PRICE
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
    INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
    WHERE
            DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
            AND TOTAL_COST IS NOT NULL
    GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X) Y, (Select 
        SUM(PRICE * QUANTITY) AS TOTAL_SALES
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
) Z
    GROUP BY SUB_CAT)B)F  ON K.SUB_CAT = F.SUB_CAT

LEFT JOIN

(Select B.*,
    CONCAT(ROUND((SALES_SHARE_last_14_21_days/100 * AVG_COGS_last_14_21_days/100)*100,2),'%') AS COGS_SHARE_last_14_21_days
from
    (Select 
        Y.SUB_CAT,
            CONCAT(ROUND((SUM(Y.SALES) / Z.TOTAL_SALES * 100), 2),'%') AS SALES_SHARE_last_14_21_days,
            CONCAT(ROUND((SUM(Y.TOTAL_COST) / SUM(Y.SALES) * 100), 2),'%') AS AVG_COGS_last_14_21_days
    from
        (Select 
        X . *,
            CASE
                WHEN PRICE = 0 THEN 'HIGH'
                WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 30)
                        AND RTL_CODE = 'BAKERY'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
                WHEN
                    COGS_PERC > 20
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 15 AND COGS_PERC <= 20)
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 15
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Food'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
                WHEN
                    COGS_PERC > 15
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 10 AND COGS_PERC <= 15)
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 10
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'LOW'
                WHEN
                    COGS_PERC > 35
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 30
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'LOW'
                WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Others'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Combos'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
            END AS SUB_CAT
    from
        (Select 
        ud.UNIT_ID,
            mp.MENU_PRODUCT_ID,
            mp.PRODUCT_NAME,
            mp.DIMENSION,
            RTL_CODE,
            SUM(TOTAL_COST) AS TOTAL_COST,
            SUM(PRICE * QUANTITY) AS SALES,
            ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
            ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
            mp.PRICE
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
    INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
    WHERE
            DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
            AND TOTAL_COST IS NOT NULL
    GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X) Y, (Select 
        SUM(PRICE * QUANTITY) AS TOTAL_SALES
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
) Z
    GROUP BY SUB_CAT) B)D ON D.SUB_CAT = K.SUB_CAT;
                                                  ]]>
                                        </content>
                                </report>
                                <report id="10" name="Category - Subcateogry Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
Select K.*,
E.SALES_SHARE_last_7_days,
F.SALES_SHARE_last_7_14_days,
D.SALES_SHARE_last_14_21_days,
E.AVG_COGS_last_7_days,
F.AVG_COGS_last_7_14_days,
D.AVG_COGS_last_14_21_days,
E.COGS_SHARE_last_7_days,
F.COGS_SHARE_last_7_14_days,
D.COGS_SHARE_last_14_21_days
from
    (Select 
        RTL_CODE, SUB_CAT
    from
        KETTLE_MASTER.REF_LOOKUP_TYPE
    INNER JOIN CLM_ANALYTICS.COGS_SUB_CAT
    WHERE
        RTL_GROUP = 'CATEGORY' ORDER BY RTL_CODE , SUB_CAT) K
        LEFT JOIN
    (Select 
        B . *,
            CONCAT(ROUND((SALES_SHARE_last_7_days / 100 * AVG_COGS_last_7_days / 100) * 100, 2), '%') AS COGS_SHARE_last_7_days
    from
        (Select 
        Y.RTL_CODE,
            Y.SUB_CAT,
            CONCAT(ROUND((SUM(Y.SALES) / Z.TOTAL_SALES * 100), 2), '%') AS SALES_SHARE_last_7_days,
            CONCAT(ROUND((SUM(Y.TOTAL_COST) / SUM(Y.SALES) * 100), 2), '%') AS AVG_COGS_last_7_days
    from
        (Select 
        X . *,
            CASE
                WHEN PRICE = 0 THEN 'HIGH'
                WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 30)
                        AND RTL_CODE = 'BAKERY'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
                WHEN
                    COGS_PERC > 20
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 15 AND COGS_PERC <= 20)
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 15
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Food'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
                WHEN
                    COGS_PERC > 15
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 10 AND COGS_PERC <= 15)
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 10
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'LOW'
                WHEN
                    COGS_PERC > 35
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 30
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'LOW'
                WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Others'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Combos'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
            END AS SUB_CAT
    from
        (Select 
        ud.UNIT_ID,
            mp.MENU_PRODUCT_ID,
            mp.PRODUCT_NAME,
            mp.DIMENSION,
            RTL_CODE,
            SUM(TOTAL_COST) AS TOTAL_COST,
            SUM(PRICE * QUANTITY) AS SALES,
            ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
            ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
            mp.PRICE
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
    INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE, 7) AND SUBDATE(CURRENT_DATE, 1)
            AND TOTAL_COST IS NOT NULL
    GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X) Y, (Select 
        SUM(PRICE * QUANTITY) AS TOTAL_SALES
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE, 7) AND SUBDATE(CURRENT_DATE, 1)) Z
    GROUP BY SUB_CAT,RTL_CODE) B)E ON E.SUB_CAT = K.SUB_CAT
        AND E.RTL_CODE = K.RTL_CODE

Left JOIN
(Select B.*,
    CONCAT(ROUND((SALES_SHARE_last_7_14_days/100 * AVG_COGS_last_7_14_days/100)*100,2),'%') AS COGS_SHARE_last_7_14_days
from
    (Select 
Y.RTL_CODE ,
        Y.SUB_CAT,
            CONCAT(ROUND((SUM(Y.SALES) / Z.TOTAL_SALES * 100), 2),'%') AS SALES_SHARE_last_7_14_days,
            CONCAT(ROUND((SUM(Y.TOTAL_COST) / SUM(Y.SALES) * 100), 2),'%') AS AVG_COGS_last_7_14_days
    from
        (Select 
        X . *,
            CASE
                WHEN PRICE = 0 THEN 'HIGH'
                WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 30)
                        AND RTL_CODE = 'BAKERY'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
                WHEN
                    COGS_PERC > 20
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 15 AND COGS_PERC <= 20)
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 15
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Food'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
                WHEN
                    COGS_PERC > 15
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 10 AND COGS_PERC <= 15)
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 10
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'LOW'
                WHEN
                    COGS_PERC > 35
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 30
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'LOW'
                WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Others'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Combos'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
            END AS SUB_CAT
    from
        (Select 
        ud.UNIT_ID,
            mp.MENU_PRODUCT_ID,
            mp.PRODUCT_NAME,
            mp.DIMENSION,
            RTL_CODE,
            SUM(TOTAL_COST) AS TOTAL_COST,
            SUM(PRICE * QUANTITY) AS SALES,
            ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
            ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
            mp.PRICE
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
    INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
    WHERE
            DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
            AND TOTAL_COST IS NOT NULL
    GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X) Y, (Select 
        SUM(PRICE * QUANTITY) AS TOTAL_SALES
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
) Z
    GROUP BY SUB_CAT,RTL_CODE)B)F  ON K.SUB_CAT = F.SUB_CAT AND K.RTL_CODE = F.RTL_CODE

Left JOIN

(Select B.*,
    CONCAT(ROUND((SALES_SHARE_last_14_21_days/100 * AVG_COGS_last_14_21_days/100)*100,2),'%') AS COGS_SHARE_last_14_21_days
from
    (Select 
RTL_CODE ,
        Y.SUB_CAT,
            CONCAT(ROUND((SUM(Y.SALES) / Z.TOTAL_SALES * 100), 2),'%') AS SALES_SHARE_last_14_21_days,
            CONCAT(ROUND((SUM(Y.TOTAL_COST) / SUM(Y.SALES) * 100), 2),'%') AS AVG_COGS_last_14_21_days
    from
        (Select 
        X . *,
            CASE
                WHEN PRICE = 0 THEN 'HIGH'
                WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 30)
                        AND RTL_CODE = 'BAKERY'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
                WHEN
                    COGS_PERC > 20
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 15 AND COGS_PERC <= 20)
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 15
                        AND RTL_CODE = 'ColdBeverages'
                THEN
                    'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Food'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
                WHEN
                    COGS_PERC > 15
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 10 AND COGS_PERC <= 15)
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 10
                        AND RTL_CODE = 'HotBeverages'
                THEN
                    'LOW'
                WHEN
                    COGS_PERC > 35
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'MEDIUM'
                WHEN
                    COGS_PERC < 30
                        AND RTL_CODE = 'Merchandise'
                THEN
                    'LOW'
                WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 30 AND COGS_PERC <= 35)
                        AND RTL_CODE = 'Others'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
                WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
                WHEN
                    (COGS_PERC >= 20 AND COGS_PERC <= 25)
                        AND RTL_CODE = 'Combos'
                THEN
                    'MEDIUM'
                WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
            END AS SUB_CAT
    from
        (Select 
        ud.UNIT_ID,
            mp.MENU_PRODUCT_ID,
            mp.PRODUCT_NAME,
            mp.DIMENSION,
            RTL_CODE,
            SUM(TOTAL_COST) AS TOTAL_COST,
            SUM(PRICE * QUANTITY) AS SALES,
            ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
            ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
            mp.PRICE
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
    INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
    WHERE
            DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
            AND TOTAL_COST IS NOT NULL
    GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X) Y, (Select 
        SUM(PRICE * QUANTITY) AS TOTAL_SALES
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
) Z
    GROUP BY SUB_CAT,RTL_CODE) B)D ON D.SUB_CAT = K.SUB_CAT AND D.RTL_CODE = K.RTL_CODE;
                                                  ]]>
                                        </content>
                                </report>
                                <report id="10" name="Region Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
Select X.UNIT_REGION, X.Last_7_days , Y._7_14_days,Z._14_21_days from (
Select
UNIT_REGION,
CONCAT(ROUND((((SUM(mp.TOTAL_COST))/(SUM(PRICE*QUANTITY)))*100),2),'%')AS Last_7_days
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
AND mp.TOTAL_COST IS NOT NULL
 GROUP BY UNIT_REGION
   )X INNER JOIN (Select UNIT_REGION, CONCAT(ROUND(SUM(mp.TOTAL_COST)/SUM(PRICE*QUANTITY)*100,2),'%')AS _7_14_days
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
AND mp.TOTAL_COST IS NOT NULL
 GROUP BY UNIT_REGION
   )Y ON X.UNIT_REGION = Y.UNIT_REGION INNER JOIN (Select UNIT_REGION , CONCAT(ROUND(SUM(mp.TOTAL_COST)/SUM(PRICE*QUANTITY)*100,2),'%')AS _14_21_days
    from
        KETTLE.MENU_PRODUCT_COST_DETAIL mp
    INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
    WHERE
        DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
AND mp.TOTAL_COST IS NOT NULL
 GROUP BY UNIT_REGION
   )Z ON Z.UNIT_REGION = Y.UNIT_REGION;
                                                  ]]>
                                        </content>
                                </report>
                                
                                                                <report id="10" name="Category Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
                                                Select X.RTL_CODE , X.Last_7_days , Y._7_14_days,Z._14_21_days from

(Select RTL_CODE from KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = 'CATEGORY')K
LEFT JOIN

(Select
           RTL_CODE,	
CONCAT(ROUND((SUM(mp.TOTAL_COST) / (SUM(mp.PRICE*QUANTITY))) * 100, 2), '%') AS LAST_7_days
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
       DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1) AND
mp.TOTAL_COST IS NOT NULL

   GROUP BY RTL_CODE) X ON X.RTL_CODE = K.RTL_CODE
LEFT JOIN
(Select
           RTL_CODE,
CONCAT(ROUND((SUM(mp.TOTAL_COST) / (SUM(mp.PRICE*QUANTITY))) * 100, 2), '%') AS _7_14_days
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
       DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
AND  mp.TOTAL_COST IS NOT NULL
   GROUP BY RTL_CODE)Y ON K.RTL_CODE = Y.RTL_CODE
LEFT JOIN
(Select
           RTL_CODE,
CONCAT(ROUND((SUM(mp.TOTAL_COST) / (SUM(mp.PRICE*QUANTITY))) * 100, 2), '%') AS _14_21_days
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
       DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14) AND  mp.TOTAL_COST IS NOT NULL
   GROUP BY RTL_CODE)Z ON Z.RTL_CODE = K.RTL_CODE;
                                                  ]]>
                                        </content>
                                </report>
                                
								<report id="10" name="Cost Source Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

Select
X.COST_SOURCE,
X.Last_7_days , Y._7_14_days,Z._14_21_days
from

(Select
       COST_SOURCE,
CONCAT(ROUND(((sum(mp.TOTAL_COST)/SUM(mp.PRICE*QUANTITY)))*100,2),'%') AS Last_7_days
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   WHERE
       DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
AND mp.TOTAL_COST IS NOT NULL
   GROUP BY COST_SOURCE )X
LEFT JOIN
(Select
       COST_SOURCE,
CONCAT(ROUND(((sum(mp.TOTAL_COST)/sum(mp.PRICE*QUANTITY)))*100,2),'%') AS _7_14_days
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   WHERE
       DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
AND mp.TOTAL_COST IS NOT NULL
   GROUP BY COST_SOURCE )Y ON X.COST_SOURCE = Y.COST_SOURCE
LEFT JOIN
(Select
       COST_SOURCE,
CONCAT(ROUND(((sum(mp.TOTAL_COST)/sum(mp.PRICE*QUANTITY)))*100,2),'%') AS _14_21_days
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   WHERE
       DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
AND mp.TOTAL_COST IS NOT NULL
   GROUP BY COST_SOURCE )Z ON Z.COST_SOURCE = Y.COST_SOURCE;
                                                  ]]>
                                        </content>
                                </report>
                                
								<report id="10" name="Last-7 Cogs Details" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray"
                                        attachmentType="EXCEL">
                                        <content>
                                                <![CDATA[
                                                 Select
       ud.UNIT_REGION,
           mp.MENU_PRODUCT_ID,
    
mp.PRODUCT_NAME,
           mp.DIMENSION,
           RTL_CODE,
           SUM(TOTAL_COST) AS TOTAL_COST,
           SUM(PRICE * QUANTITY) AS SALES,
           ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
           ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
           mp.PRICE
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
           DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
           AND TOTAL_COST IS NOT NULL
   GROUP BY UNIT_REGION , mp.MENU_PRODUCT_ID , mp.DIMENSION;
    
                                                  ]]>
                                        </content>
                                </report>
                                
                                <report id="10" name="Last-7 Sub Cat wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray"
                                        attachmentType="EXCEL">
                                        <content>
                                                <![CDATA[
                                                Select
       X . *,
           CASE
               WHEN PRICE = 0 THEN 'HIGH'
               WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 30)
                       AND RTL_CODE = 'BAKERY'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
               WHEN
                   COGS_PERC > 20
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 15 AND COGS_PERC <= 20)
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 15
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'LOW'
               WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 25)
                       AND RTL_CODE = 'Food'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
               WHEN
                   COGS_PERC > 15
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 10 AND COGS_PERC <= 15)
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 10
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'LOW'
               WHEN
                   COGS_PERC > 35
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 30 AND COGS_PERC <= 35)
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 30
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'LOW'
               WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 30 AND COGS_PERC <= 35)
                       AND RTL_CODE = 'Others'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
               WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 25)
                       AND RTL_CODE = 'Combos'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
           END AS SUB_CAT
   from
       (Select
       ud.UNIT_ID,
           mp.MENU_PRODUCT_ID,
           mp.PRODUCT_NAME,
           mp.DIMENSION,
           RTL_CODE,
           SUM(TOTAL_COST) AS TOTAL_COST,
           SUM(PRICE * QUANTITY) AS SALES,
           ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
           ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
           mp.PRICE
   from
       KETTLE_DUMP.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE_DUMP.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
       COST_SOURCE = 'CAFE'
           AND DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 7) AND SUBDATE(CURRENT_DATE , 1)
           AND TOTAL_COST IS NOT NULL
   GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X;
                                                
                                                  ]]>
                                        </content>
                                </report>
                                
                                <report id="10" name="7-14 SubCat" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray"
                                        attachmentType="EXCEL">
                                        <content>
                                                <![CDATA[
   Select
       X . *,
           CASE
               WHEN PRICE = 0 THEN 'HIGH'
               WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 30)
                       AND RTL_CODE = 'BAKERY'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
               WHEN
                   COGS_PERC > 20
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 15 AND COGS_PERC <= 20)
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 15
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'LOW'
               WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 25)
                       AND RTL_CODE = 'Food'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
               WHEN
                   COGS_PERC > 15
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 10 AND COGS_PERC <= 15)
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 10
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'LOW'
               WHEN
                   COGS_PERC > 35
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 30 AND COGS_PERC <= 35)
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 30
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'LOW'
               WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 30 AND COGS_PERC <= 35)
                       AND RTL_CODE = 'Others'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
               WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 25)
                       AND RTL_CODE = 'Combos'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
           END AS SUB_CAT
   from
       (Select
       ud.UNIT_ID,
           mp.MENU_PRODUCT_ID,
           mp.PRODUCT_NAME,
           mp.DIMENSION,
           RTL_CODE,
           SUM(TOTAL_COST) AS TOTAL_COST,
           SUM(PRICE * QUANTITY) AS SALES,
           ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
           ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
           mp.PRICE
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
       COST_SOURCE = 'CAFE'
           AND DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 14) AND SUBDATE(CURRENT_DATE , 7)
           AND TOTAL_COST IS NOT NULL
   GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X;

                                                  ]]>
                                        </content>
                                </report>
                                
                                <report id="10" name="14-21 SubCat" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray"
                                        attachmentType="EXCEL">
                                        <content>
                                                <![CDATA[
 Select
       X . *,
           CASE
               WHEN PRICE = 0 THEN 'HIGH'
               WHEN COGS_PERC > 30 AND RTL_CODE = 'BAKERY' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 30)
                       AND RTL_CODE = 'BAKERY'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'BAKERY' THEN 'LOW'
               WHEN
                   COGS_PERC > 20
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 15 AND COGS_PERC <= 20)
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 15
                       AND RTL_CODE = 'ColdBeverages'
               THEN
                   'LOW'
               WHEN COGS_PERC > 25 AND RTL_CODE = 'Food' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 25)
                       AND RTL_CODE = 'Food'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'Food' THEN 'LOW'
               WHEN
                   COGS_PERC > 15
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 10 AND COGS_PERC <= 15)
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 10
                       AND RTL_CODE = 'HotBeverages'
               THEN
                   'LOW'
               WHEN
                   COGS_PERC > 35
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'HIGH'
               WHEN
                   (COGS_PERC >= 30 AND COGS_PERC <= 35)
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'MEDIUM'
               WHEN
                   COGS_PERC < 30
                       AND RTL_CODE = 'Merchandise'
               THEN
                   'LOW'
               WHEN COGS_PERC > 35 AND RTL_CODE = 'Others' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 30 AND COGS_PERC <= 35)
                       AND RTL_CODE = 'Others'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 30 AND RTL_CODE = 'Others' THEN 'LOW'
               WHEN COGS_PERC > 25 AND RTL_CODE = 'Combos' THEN 'HIGH'
               WHEN
                   (COGS_PERC >= 20 AND COGS_PERC <= 25)
                       AND RTL_CODE = 'Combos'
               THEN
                   'MEDIUM'
               WHEN COGS_PERC < 20 AND RTL_CODE = 'Combos' THEN 'LOW'
           END AS SUB_CAT
   from
       (Select
       ud.UNIT_ID,
           mp.MENU_PRODUCT_ID,
           mp.PRODUCT_NAME,
           mp.DIMENSION,
           RTL_CODE,
           SUM(TOTAL_COST) AS TOTAL_COST,
           SUM(PRICE * QUANTITY) AS SALES,
           ROUND(avg(coalesce(TOTAL_COST / QUANTITY)), 2) AS AVG_COGS,
           ROUND((avg(TOTAL_COST / QUANTITY)) / PRICE * 100, 2) AS COGS_PERC,
           mp.PRICE
   from
       KETTLE.MENU_PRODUCT_COST_DETAIL mp
   INNER JOIN KETTLE.UNIT_CLOSURE_DETAILS uc ON mp.CLOSURE_ID = uc.CLOSURE_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = uc.UNIT_ID
   INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL p ON p.PRODUCT_ID = mp.MENU_PRODUCT_ID
   INNER JOIN KETTLE_MASTER.REF_LOOKUP_TYPE r ON r.RTL_ID = p.PRODUCT_TYPE
   WHERE
       COST_SOURCE = 'CAFE'
           AND DATE(GENERATION_TIME) BETWEEN SUBDATE(CURRENT_DATE , 21) AND SUBDATE(CURRENT_DATE , 14)
           AND TOTAL_COST IS NOT NULL
   GROUP BY UNIT_ID , mp.MENU_PRODUCT_ID , mp.DIMENSION) X;


                                                  ]]>
                                        </content>
                                </report>
                </reports>
        </category>
</categories>
</ReportCategories>