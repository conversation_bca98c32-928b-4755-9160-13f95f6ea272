<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
            ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
	<!--	<category name="Daily Sales Report" type="Automated" accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>,<EMAIL>" schedule="">
			<reports>
				<report id="1" name="Daily Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    od.UNIT_ID,
    CAST(CONCAT(SUM(CASE
                    WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                    ELSE 0
                END),
                ' / ',
                COALESCE((CASE
                            WHEN
                                DAYOFWEEK(CASE
                                            WHEN
                                                HOUR(od.BILLING_SERVER_TIME) <= 5
                                            THEN
                                                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                                                        INTERVAL - 6 HOUR))
                                            ELSE DATE(od.BILLING_SERVER_TIME)
                                        END) NOT IN ('1' , '7')
                            THEN
                                td.TKT_WD
                            ELSE td.TKT_WE
                        END),
                        'NA'),
                ' / ',
                COALESCE(TRUNCATE((SUM(CASE
                                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                                ELSE 0
                            END)) * 100 / (CASE
                                WHEN
                                    DAYOFWEEK(CASE
                                                WHEN
                                                    HOUR(od.BILLING_SERVER_TIME) <= 5
                                                THEN
                                                    DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                                                            INTERVAL - 6 HOUR))
                                                ELSE DATE(od.BILLING_SERVER_TIME)
                                            END) NOT IN ('1' , '7')
                                THEN
                                    td.TKT_WD
                                ELSE td.TKT_WE
                            END),
                            1),
                        'NA'),
                '%',
                ' / ',
                nt121.LMSD_TKTS)
        AS CHAR (100)) AS 'TKT / TARGET / %_TARGET / LMSD_TKT',
    CAST(CONCAT(TRUNCATE((SUM(od.TAXABLE_AMOUNT)), 0),
               ' / ',
                COALESCE((CASE
                            WHEN
                                DAYOFWEEK(CASE
                                            WHEN
                                                HOUR(od.BILLING_SERVER_TIME) <= 5
                                            THEN
                                                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                                                        INTERVAL - 6 HOUR))
                                            ELSE DATE(od.BILLING_SERVER_TIME)
                                        END) NOT IN ('1' , '7')
                            THEN
                                td.SALES_WD
                            ELSE td.SALES_WE
                        END),
                        'NA'),
                ' / ',
                COALESCE(TRUNCATE((SUM(od.TAXABLE_AMOUNT)) * 100 / (CASE
                                WHEN
                                    DAYOFWEEK(CASE
                                                WHEN
                                                    HOUR(od.BILLING_SERVER_TIME) <= 5
                                                THEN
                                                    DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                                                            INTERVAL - 6 HOUR))
                                                ELSE DATE(od.BILLING_SERVER_TIME)
                                            END) NOT IN ('1' , '7')
                                THEN
                                    td.SALES_WD
                                ELSE td.SALES_WE
                            END),
                            1),
                        'NA'),
                '%',
                ' / ',
                TRUNCATE(nt121.LMSD_SALES, 0))
        AS CHAR (100)) AS 'SALES / TARGET / %_TARGET / LMSD_SALES',
    CAST(CONCAT(TRUNCATE((SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                        ELSE 0
                    END)),
                    0),
                ' / ',
                COALESCE((CASE
                            WHEN
                                DAYOFWEEK(CASE
                                            WHEN
                                                HOUR(od.BILLING_SERVER_TIME) <= 5
                                            THEN
                                                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                                                        INTERVAL - 6 HOUR))
                                            ELSE DATE(od.BILLING_SERVER_TIME)
                                        END) NOT IN ('1' , '7')
                            THEN
                                td.APC_WD
                            ELSE td.APC_WE
                        END),
                        'NA'),
                ' / ',
                COALESCE(TRUNCATE((SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                                ELSE 0
                            END)) * 100 / (CASE
                                WHEN
                                    DAYOFWEEK(CASE
                                                WHEN
                                                    HOUR(od.BILLING_SERVER_TIME) <= 5
                                                THEN
                                                    DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                                                            INTERVAL - 6 HOUR))
                                                ELSE DATE(od.BILLING_SERVER_TIME)
                                            END) NOT IN ('1' , '7')
                                THEN
                                    td.APC_WD
                                ELSE td.APC_WE
                            END),
                            1),
                        'NA'),
                '%',
                ' / ',
                TRUNCATE(nt121.LMSD_APC, 0))
        AS CHAR (100)) AS 'APC / TARGET / %_TARGET / LMSD_APC',
    CAST(CONCAT(TRUNCATE((SUM(CASE
                        WHEN
                            od.CUSTOMER_ID > 5
                                AND od.TOTAL_AMOUNT <> '0.00'
                        THEN
                            1
                        ELSE 0
                    END)) * 100 / (SUM(CASE
                        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                        ELSE 0
                    END)),
                    1),
                '%',
                ' / ',
                COALESCE(td.CUSTOMER_CAPTURE_RATE, 'NA'),
                '%',
                ' / ',
                COALESCE(TRUNCATE((SUM(CASE
                                WHEN
                                    od.CUSTOMER_ID > 5
                                        AND od.TOTAL_AMOUNT <> '0.00'
                                THEN
                                    1
                                ELSE 0
                            END)) * 100 / (SUM(CASE
                                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                                ELSE 0
                            END)) * 100 / td.CUSTOMER_CAPTURE_RATE,
                            0),
                        'NA'),
                '%')
        AS CHAR (100)) AS CUSTOMER_CAPTURE_RATEvsTARGET,
    COALESCE(CAST(CONCAT(SUM(CASE
                            WHEN od.ORDER_SOURCE = 'COD' THEN 1
                            ELSE 0
                        END),
                        ' / ',
                        COALESCE(td.DELIVERY_TKT, 'NA'),
                        ' / ',
                        COALESCE(TRUNCATE((SUM(CASE
                                        WHEN od.ORDER_SOURCE = 'COD' THEN 1
                                        ELSE 0
                                    END)) * 100 / td.DELIVERY_TKT,
                                    1),
                                'NA'),
                        '%')
                AS CHAR (100)),
            'NA') AS DELIVERY_TKTvsTARGET,
    COALESCE(nt.COMBOSvsTARGET, 'NA') AS COMBOSvsTARGET,
    COALESCE(nt.SAMBOSSAvsTARGET, 'NA') SAMBOSSAvsTARGET,
    CONCAT(TRUNCATE(q.ORDERS * 100 / SUM(CASE
                    WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                    ELSE 0
                END),
                1),
            ' / ',
            COALESCE(td.TICKETS_WITH_FOOD_ITEM, 'NA'),
            ' / ',
            COALESCE(TRUNCATE((q.ORDERS * 100 / SUM(CASE
                            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                            ELSE 0
                        END)) * 100 / td.TICKETS_WITH_FOOD_ITEM,
                        1),
                    'NA')) AS '%OF_TICKETS_WITH_FOOD_ITEMvsTARGETS',
    TRUNCATE(e.CHAI_QUANTITY / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS CHAI_PER_TKT,
    TRUNCATE(e.OTHER_BEVERAGE_QUANTITY / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS OTHER_BEVERAGE_PER_TKT,
    TRUNCATE(e.NASHTA / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS NASHTA_PER_TKT,
    TRUNCATE(e.SANDWICH_WRAP / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS SANDWICH_WRAP_PER_TKT,
    TRUNCATE(e.CAKES_COOKIES / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS CAKES_COOKIES_PER_TKT,
    TRUNCATE(e.PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS PEOPLE_PER_TKT,
    TRUNCATE(e.FIRST_FREE_CHAI / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS FREE_CHAI_PER_TKT,
    TRUNCATE(e.FIRST_CHAI_WITH_SAMBOSSA / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS FREE_CHAI_WITH_SAMBOSSA_PER_TKT
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE.TARGET_DETAIL td ON od.UNIT_ID = td.UNIT_ID
        AND MONTH(CASE
            WHEN
                HOUR(BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(BILLING_SERVER_TIME)
        END) = td.MONTH
        LEFT JOIN
    (SELECT
        od.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LMSD_TKTS,
            SUM(od.TAXABLE_AMOUNT) AS LMSD_SALES,
            SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LMSD_APC
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 8), INTERVAL 5 HOUR) AND DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 7), INTERVAL 5 HOUR)
    GROUP BY od.UNIT_ID) AS nt121 ON od.UNIT_ID = nt121.UNIT_ID
        LEFT JOIN
    (SELECT
        od.UNIT_ID,
            (CASE
                WHEN HOUR(BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            CAST(CONCAT(SUM(CASE
                    WHEN
                        rlt.RTL_ID = 8
                            AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN ('2100' , '2102', '2105', '2106')
                    THEN
                        oi.QUANTITY
                    ELSE 0
                END), ' / ', COALESCE(td.COMBOS, 'NA'), ' / ', COALESCE(TRUNCATE((SUM(CASE
                    WHEN
                        rlt.RTL_ID = 8
                            AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN ('2100' , '2102', '2105', '2106')
                    THEN
                        oi.QUANTITY
                    ELSE 0
                END)) * 100 / (td.COMBOS), 1), 'NA'), '%')
                AS CHAR (100)) AS COMBOSvsTARGET,
            CAST(CONCAT(SUM(CASE
                    WHEN
                        pd.PRODUCT_NAME LIKE '%SAMBOSSA%'
                            AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN ('2100' , '2102', '2105', '2106')
                    THEN
                        oi.QUANTITY
                   ELSE 0
                END), ' / ', COALESCE(td.SAMBOSSA, 'NA'), ' / ', COALESCE(TRUNCATE((SUM(CASE
                    WHEN
                        pd.PRODUCT_NAME LIKE '%SAMBOSSA%'
                            AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN ('2100' , '2102', '2105', '2106')
                    THEN
                        oi.QUANTITY
                    ELSE 0
                END)) * 100 / (td.SAMBOSSA), 1), 'NA'), '%')
                AS CHAR (100)) AS SAMBOSSAvsTARGET
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    LEFT JOIN KETTLE_MASTER.REF_LOOKUP_TYPE rlt ON pd.PRODUCT_TYPE = rlt.RTL_ID
    LEFT JOIN KETTLE.TARGET_DETAIL td ON od.UNIT_ID = td.UNIT_ID
        AND MONTH(CASE
        WHEN HOUR(BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
        ELSE DATE(BILLING_SERVER_TIME)
    END) = td.MONTH
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1), INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 5 HOUR)
    GROUP BY od.UNIT_ID , BUSINESS_DATE) AS nt ON od.UNIT_ID = nt.UNIT_ID
        LEFT JOIN
    (SELECT
        b.UNIT_ID,
            b.BUSINESS_DATE,
            SUM(b.CHAI_QUANTITY) AS CHAI_QUANTITY,
            SUM(b.OTHER_BEVERAGE_QUANTITY) AS OTHER_BEVERAGE_QUANTITY,
            SUM(b.NASHTA) AS NASHTA,
            SUM(b.SANDWICH_WRAP) AS SANDWICH_WRAP,
            SUM(b.CAKES_COOKIES) AS CAKES_COOKIES,
            SUM(b.PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE) AS PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE,
            SUM(b.FIRST_FREE_CHAI) AS FIRST_FREE_CHAI,
            SUM(FIRST_CHAI_WITH_SAMBOSSA) AS FIRST_CHAI_WITH_SAMBOSSA
    FROM
        (SELECT
        a.*,
            GREATEST(a.TOTAL_BEVERAGE_QUANTITY, a.FOOD_QUANTITY) PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE
    FROM
        (SELECT
        od.UNIT_ID,
            (CASE
                WHEN HOUR(BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            oi.ORDER_ID,
            SUM(CASE
                WHEN
                    pd.PRODUCT_SUB_TYPE IN (501 , 502, 503)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) CHAI_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE IN (5 , 6)
                        AND pd.PRODUCT_SUB_TYPE NOT IN (501 , 502, 503)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHER_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_SUB_TYPE = 703
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) AS NASHTA,
            SUM(CASE
                WHEN
                    pd.PRODUCT_SUB_TYPE IN (701 , 702)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) AS SANDWICH_WRAP,
            SUM(CASE
                WHEN
                    pd.PRODUCT_SUB_TYPE IN (1001 , 1002)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) AS CAKES_COOKIES,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE IN (7 , 8)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE IN (5 , 6)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) TOTAL_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_SUB_TYPE IN (501 , 502, 503)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2101 , 2104)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) AS FIRST_FREE_CHAI,
            SUM(CASE
                WHEN
                    pd.PRODUCT_NAME LIKE '%SAMBOSSA%'
                        AND (pd.PRODUCT_SUB_TYPE IN (501 , 502, 503)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2101 , 2104))
                THEN
                    1
                ELSE 0
            END) AS FIRST_CHAI_WITH_SAMBOSSA
    FROM
        KETTLE.ORDER_ITEM oi
    LEFT JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1), INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 5 HOUR)
    GROUP BY od.UNIT_ID , BUSINESS_DATE , oi.ORDER_ID) AS a) AS b
    GROUP BY b.UNIT_ID , b.BUSINESS_DATE) AS e ON e.UNIT_ID = od.UNIT_ID
        AND e.BUSINESS_DATE = CASE
        WHEN
            HOUR(BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(BILLING_SERVER_TIME)
    END
        LEFT JOIN
    (SELECT
        od.UNIT_ID,
            (CASE
                WHEN HOUR(BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            COUNT(DISTINCT oi.ORDER_ID) AS ORDERS
    FROM
        KETTLE.ORDER_ITEM oi
    LEFT JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    LEFT JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        pd.PRODUCT_TYPE IN (7 , 8, 10)
        AND od.TOTAL_AMOUNT > 0            
        AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1), INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 5 HOUR)
    GROUP BY od.UNIT_ID , BUSINESS_DATE) AS q ON q.UNIT_ID = od.UNIT_ID
        AND q.BUSINESS_DATE = (CASE
        WHEN
            HOUR(BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(BILLING_SERVER_TIME)
    END)
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1),
        INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 5 HOUR)
GROUP BY ud.UNIT_NAME , od.UNIT_ID
 
                                                ]]>
					</content>

				</report>
				<report id="2" name="Channel Partner Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    cp.PARTNER_DISPLAY_NAME,
    SUM(CASE
        WHEN
            od.ORDER_STATUS <> 'CANCELLED'
                AND od.TOTAL_AMOUNT <> '0.00'
        THEN
            1
        ELSE 0
    END) AS SETTLED_ORDERS,
    SUM(CASE
        WHEN
            od.ORDER_STATUS = 'CANCELLED'
                AND od.TOTAL_AMOUNT <> '0.00'
        THEN
            1
        ELSE 0
    END) AS CANCELLED_ORDERS
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 2))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1))
GROUP BY ud.UNIT_NAME , cp.PARTNER_DISPLAY_NAME
 
 
 
                                                ]]>
					</content>

				</report>

				<report id="2" name="Delivery Partner Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    COALESCE(dp.PARTNER_DISPLAY_NAME,'None') as DELIVERY_PARTNER,
    SUM(CASE
        WHEN od.ORDER_STATUS <> 'CANCELLED' and od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS SETTLED_ORDERS,
    SUM(CASE
        WHEN od.ORDER_STATUS = 'CANCELLED' and od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS CANCELLED_ORDERS
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 2))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE =SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1))
            and od.ORDER_SOURCE='COD'
GROUP BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
ORDER BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
 
 
 
 
                                                ]]>
					</content>

				</report>
			</reports>
		</category>-->

	<!--	<category name="Daily TAT Reports" type="Automated" accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>,<EMAIL>,<EMAIL>" schedule="">
			<reports>
				<report id="1" name="Unit Wise TAT for Delivery Orders" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    AVG(CASE
        WHEN
            ose.TO_STATUS = 'PROCESSING'
        THEN
            (TIMESTAMPDIFF(MINUTE,
                ose.START_TIME,
                ose.UPDATE_TIME))
        ELSE 0
    END) AS 'AVG_TIME_ACKNOWLEDGE(Min)',
    AVG(CASE
        WHEN
            ose.TO_STATUS = 'READY_TO_DISPATCH'
        THEN
            (TIMESTAMPDIFF(MINUTE,
                ose.START_TIME,
                ose.UPDATE_TIME))
        ELSE 0
    END) AS 'AVG_TIME_RTD(Min)',
    AVG(CASE
        WHEN
            ose.TO_STATUS = 'SETTLED'
        THEN
            (TIMESTAMPDIFF(MINUTE,
                ose.START_TIME,
                ose.UPDATE_TIME))
        ELSE 0
    END) AS 'AVG_TIME_SETTLED(Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'PROCESSING'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 60
        THEN
            1
        ELSE 0
    END) AS 'ACKNOWLEDGE(<1Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'PROCESSING'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 60
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 120
        THEN
            1
        ELSE 0
    END) AS 'ACKNOWLEDGE(1-2Min)',
    SUM(CASE
        WHEN
           ose.TO_STATUS = 'PROCESSING'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 120
        THEN
            1
        ELSE 0
    END) AS 'ACKNOWLEDGE(>2Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'READY_TO_DISPATCH'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 300
        THEN
            1
        ELSE 0
    END) AS 'RTD(<5Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'READY_TO_DISPATCH'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 300
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 600
        THEN
            1
        ELSE 0
    END) AS 'RTD(5-10Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'READY_TO_DISPATCH'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 600
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 900
        THEN
            1
        ELSE 0
    END) AS 'RTD(10-15Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'READY_TO_DISPATCH'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 900
        THEN
            1
        ELSE 0
    END) AS 'RTD(>15Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'SETTLED'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 60
        THEN
            1
        ELSE 0
    END) AS 'SETTLED(<1Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'SETTLED'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 60
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 120
        THEN
            1
        ELSE 0
    END) AS 'SETTLED(1-2Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'SETTLED'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 120
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 300
        THEN
            1
        ELSE 0
    END) AS 'SETTLED(2-5Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'SETTLED'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 300
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) < 600
       THEN
            1
        ELSE 0
    END) AS 'SETTLED(5-10Min)',
    SUM(CASE
        WHEN
            ose.TO_STATUS = 'SETTLED'
                AND TIMESTAMPDIFF(SECOND,
                ose.START_TIME,
                ose.UPDATE_TIME) >= 600
        THEN
            1
        ELSE 0
    END) AS 'SETTLED(>10Min)'
FROM
    KETTLE.ORDER_STATUS_EVENT ose
        INNER JOIN
    KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_SOURCE = 'COD'
        AND od.ORDER_STATUS = 'SETTLED'
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 2))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE =SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1))
GROUP BY ud.UNIT_NAME
ORDER BY ud.UNIT_NAME
 
 
                                                ]]>
					</content>

				</report>
				<report id="2" name="Unit Wise TAT for Delivery Partners" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    dp.PARTNER_DISPLAY_NAME,
    AVG(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'ARRIVED'
        THEN
            (TIMESTAMPDIFF(MINUTE,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP))
        ELSE 0
    END) AS 'AVG_TIME_ARRIVED(Min)',
    AVG(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'PICKEDUP'
                OR dse.DELIVERY_TO_STATUS = 'DEPARTED'
        THEN
            (TIMESTAMPDIFF(MINUTE,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP))
        ELSE 0
    END) AS 'AVG_TIME_PICKEDUP(Min)',
    AVG(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'DELIVERED'
        THEN
            (TIMESTAMPDIFF(MINUTE,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP))
        ELSE 0
    END) AS 'AVG_TIME_DELIVERED(Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'ARRIVED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 120
        THEN
            1
        ELSE 0
    END) AS 'ARRIVED(<2Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'ARRIVED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 120
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 300
        THEN
            1
        ELSE 0
    END) AS 'ARRIVED(2-5Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'ARRIVED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 300
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 600
        THEN
            1
        ELSE 0
    END) AS 'ARRIVED(5-10Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'ARRIVED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 600
        THEN
            1
        ELSE 0
    END) AS 'ARRIVED(>10Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'PICKEDUP'
                OR dse.DELIVERY_TO_STATUS = 'DEPARTED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 120
        THEN
            1
        ELSE 0
    END) AS 'PICKEDUP(<2Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'PICKEDUP'
                OR dse.DELIVERY_TO_STATUS = 'DEPARTED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 120
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 300
        THEN
            1
        ELSE 0
    END) AS 'PICKEDUP(2-5Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'PICKEDUP'
                OR dse.DELIVERY_TO_STATUS = 'DEPARTED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 300
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 600
        THEN
            1
        ELSE 0
    END) AS 'PICKEDUP(5-10Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'PICKEDUP'
                OR dse.DELIVERY_TO_STATUS = 'DEPARTED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 600
        THEN
            1
        ELSE 0
    END) AS 'PICKEDUP(>10Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'DELIVERED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 300
        THEN
            1
        ELSE 0
    END) AS 'DELIVERED(<5Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'DELIVERED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 300
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 600
        THEN
            1
        ELSE 0
    END) AS 'DELIVERED(5-10Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'DELIVERED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 600
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 900
        THEN
            1
        ELSE 0
    END) AS 'DELIVERED(10-15Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'DELIVERED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 900
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) < 1200
        THEN
            1
        ELSE 0
    END) AS 'DELIVERED(15-20Min)',
    SUM(CASE
        WHEN
            dse.DELIVERY_TO_STATUS = 'DELIVERED'
                AND TIMESTAMPDIFF(SECOND,
                dse.STATUS_START_TMSTMP,
                dse.STATUS_UPDATE_TMSTMP) >= 1200
        THEN
            1
        ELSE 0
    END) AS 'DELIVERED(>20Min)'
FROM
    KETTLE.DELIVERY_STATUS_EVENT dse
        LEFT JOIN
    KETTLE.ORDER_DETAIL od ON dse.ORDER_ID = od.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE.DELIVERY_PARTNER dp ON dse.DELIVERY_PARTNER_ID = dp.PARTNER_ID
WHERE
    od.ORDER_STATUS = 'SETTLED'
        AND od.ORDER_ID > (SELECT
            CASE
                    WHEN MAX(LAST_ORDER_ID) IS NULL THEN 0
                    ELSE MAX(LAST_ORDER_ID)
                END
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 2))
        AND od.ORDER_ID <= (SELECT
            CASE
                    WHEN MAX(LAST_ORDER_ID) IS NULL THEN 0
                    ELSE MAX(LAST_ORDER_ID)
                END
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1))
GROUP BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
 
 
                                                ]]>
					</content>

				</report>
			</reports>
		</category>-->
		<category name="Consolidate Settlement Report" type="Automated" accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>" schedule="">
			<reports>
				<report id="7" name="Settlement Data " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[

SELECT 
    COALESCE(COUNT(od.ORDER_ID), 0) TOTAL_TICKETS,
    ud.UNIT_NAME,
    ud.UNIT_ID,
    COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS),
            0) 'Total Sales',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Cash',
    SUM(CASE
        WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Visa/Master Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'AMEX Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Credit',
    SUM(CASE
        WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Gift Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Paytm',
SUM(CASE
        WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'RazorPay',
SUM(CASE
        WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'PayTmOnline',
SUM(CASE
        WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Mobikwik',
SUM(CASE
        WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'FreeCharge',
    SUM(CASE
        WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'DineOut',
SUM(CASE
        WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Prepaid',
SUM(CASE
        WHEN pm.MODE_NAME = 'PhonePe' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'PhonePe',
SUM(CASE
        WHEN pm.MODE_NAME = 'GYFTR' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'GYFTR'
FROM
    (SELECT 
        *
    FROM
        KETTLE.UNIT_CLOSURE_DETAILS
    WHERE
        BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            AND CURRENT_STATUS <> 'CANCELLED') ucd
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ucd.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE.ORDER_DETAIL od ON od.ORDER_ID > ucd.START_ORDER_ID
        AND od.ORDER_ID <= ucd.LAST_ORDER_ID
        AND od.UNIT_ID = ucd.UNIT_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        LEFT JOIN
    KETTLE.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
GROUP BY ud.UNIT_NAME , ud.UNIT_ID;

                                                ]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Credit Bills Report" type="Automated" accessCode="Automated" id="3" fromEmail="<EMAIL>" toEmails="<EMAIL>" schedule="" attachmentType="EXCEL" >
			<reports>
				<report id="7" name="Credit Bills" executionType="SQL"
                returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    cp.PARTNER_CODE,
    COALESCE(dp.PARTNER_DISPLAY_NAME, 'NA') AS PARTNER_DISPLAY_NAME,
    (CASE
        WHEN ci.CUSTOMER_ID <> 1 THEN COALESCE(ci.CONTACT_NUMBER, 'NA')
        ELSE 'NA'
    END) AS CONTACT_NUMBER,
    (CASE
        WHEN ci.CUSTOMER_ID <> 1 THEN COALESCE(ci.FIRST_NAME, 'NA')
        ELSE 'NA'
    END) AS CUSTOMER_NAME,
    od.*,
    os.*,
    pm.*,
    B.*
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
        LEFT JOIN
    KETTLE.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        LEFT JOIN
    KETTLE.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    (SELECT 
        od.ORDER_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID = 1043 THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) AS PACKAGING_CHARGES,
            SUM(CASE
                WHEN oi.PRODUCT_ID = 1044 THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) AS DELIVERY_CHARGES
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1), INTERVAL 5 HOUR) AND DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1), INTERVAL 29 HOUR)
    GROUP BY od.ORDER_ID) B ON od.ORDER_ID = B.ORDER_ID
WHERE
    od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
            1),
        INTERVAL 5 HOUR) AND DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
            1),
        INTERVAL 29 HOUR)
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND pm.PAYMENT_MODE_ID = 6
ORDER BY ud.UNIT_NAME , PARTNER_CODE , PARTNER_DISPLAY_NAME
         
]]>
					</content>

				</report>
			</reports>
		</category>
		<category name="Cafe Login Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Cafe Login Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
 
 
SELECT
    ud.UNIT_ID,
        ud.UNIT_CATEGORY,
    ud.UNIT_NAME,
    MIN(LOGIN_TIME) FIRST_LOGIN_TIME,
    MAX(LOGIN_TIME) LAST_LOGIN_TIME,
    CASE
        WHEN MAX(LOGOUT_TIME) < MAX(LOGIN_TIME) THEN 'Did Not Logout'
        ELSE MAX(LOGOUT_TIME)
    END LAST_LOGOUT_TIME,
    MAX(ucd.CLOSURE_START_TIME) CLOSURE_TIME
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    KETTLE.UNIT_CLOSURE_DETAILS ucd ON ud.UNIT_ID = ucd.UNIT_ID
        AND ucd.BUSINESS_DATE = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
        LEFT OUTER JOIN
    KETTLE_MASTER.EMPLOYEE_SESSION_DETAILS esd ON ud.UNIT_ID = esd.UNIT_ID
        INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ed.EMP_ID = esd.EMPLOYEE_ID
WHERE
    esd.LOGIN_TIME > DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1),
        INTERVAL 5 HOUR)
        AND esd.LOGIN_TIME < DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 5 HOUR)
        AND ud.UNIT_STATUS = 'ACTIVE'
GROUP BY ud.UNIT_CATEGORY, ud.UNIT_ID, ud.UNIT_NAME
ORDER BY ud.UNIT_CATEGORY,FIRST_LOGIN_TIME
]]>
					</content>
				</report>
			</reports>
		</category>
		<!--<category name="Customer Signup Offer Report" type="Automated"
		accessCode="Automated" id="1" fromEmail="<EMAIL>"
		toEmails="<EMAIL>"
		schedule="">
			<reports>
				<report id="1" name="Customer Signup Offer Report" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    KETTLE.LOYALTY_SCORE
WHERE
    AVAILED_SIGNUP_OFFER = 'N'
        AND ORDER_COUNT > 2;
]]>
					</content>
				</report>
			</reports>
		</category>-->
		<category name="Last Day Customer Acquisition Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Customer Acquisition Summary Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    A.UNIT_ID,
    Z.UNIT_NAME,
    coalesce(A.NEW_CAFE_CUSTOMER,0) 'NEW_CAFE_CUSTOMER',
    coalesce(A.OLD_CAFE_CUSTOMER,0) 'OLD_CAFE_CUSTOMER',
    coalesce(B.NEW_COD_CUSTOMER,0) 'NEW_COD_CUSTOMER',
    coalesce(B.OLD_COD_CUSTOMER,0) 'OLD_COD_CUSTOMER'
FROM
    KETTLE_MASTER.UNIT_DETAIL Z
        INNER JOIN
    (SELECT 
        UNIT_ID,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID > (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2)))
                THEN
                    ci.CUSTOMER_ID
            END)) NEW_CAFE_CUSTOMER,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID < (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2)))
                THEN
                    ci.CUSTOMER_ID
            END)) OLD_CAFE_CUSTOMER
    FROM
        KETTLE.CUSTOMER_INFO ci, KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.CUSTOMER_ID = ci.CUSTOMER_ID
            AND od.CUSTOMER_ID > 5
            AND ORDER_SOURCE <> 'COD'
    GROUP BY UNIT_ID) A ON A.UNIT_ID = Z.UNIT_ID
        LEFT JOIN
    (SELECT 
        UNIT_ID,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID > (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2)))
                THEN
                    ci.CUSTOMER_ID
            END)) NEW_COD_CUSTOMER,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID < (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2)))
                THEN
                    ci.CUSTOMER_ID
            END)) OLD_COD_CUSTOMER
    FROM
        KETTLE.CUSTOMER_INFO ci, KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.CUSTOMER_ID = ci.CUSTOMER_ID
            AND od.CUSTOMER_ID > 5
            AND ORDER_SOURCE = 'COD'
    GROUP BY UNIT_ID) B ON B.UNIT_ID = Z.UNIT_ID ) D
				     ]]>
					</content>
				</report>
				<report id="1" name="Customer Acquisition Detail Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
DISTINCT
	ud.UNIT_ID,
    ud.UNIT_NAME,
    (ci.CONTACT_NUMBER) CONTACT_NUMBER,
    ci.IS_NUMBER_VERIFIED,
    case when ci.CUSTOMER_ID > (select max(CUSTOMER_ID)
from KETTLE.ORDER_DETAIL
where ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))) then 'NEW' ELSE 'OLD' END NEW_OR_OLD,
		COUNT(DISTINCT od.ORDER_ID) ORDER_COUNT
FROM
    KETTLE.CUSTOMER_INFO ci,
    KETTLE.ORDER_DETAIL od,
    KETTLE_MASTER.UNIT_DETAIL ud
   
WHERE
    od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
        AND od.CUSTOMER_ID = ci.CUSTOMER_ID
        and
        od.UNIT_ID=ud.UNIT_ID
        and od.CUSTOMER_ID > 5
        group by ud.UNIT_ID, CONTACT_NUMBER
        order by ud.UNIT_ID, CONTACT_NUMBER
        
				     ]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="Customer Repeat Score" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary Of Customer Repeat Score" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
					call KETTLE.UNIT_WISE_CUSTOMER_SCORE;

				     ]]>
					</content>
				</report>
			</reports>
		</category>

		<!--<category name="Delivery Order TAT Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Production Time TAT Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
					SELECT 
    ud.UNIT_CATEGORY,
    ud.UNIT_NAME,
    CONCAT(FLOOR(m.TIME_IN_SEC / 60),
            ' mins ',
            MOD(m.TIME_IN_SEC, 60),
            ' sec') 95_PERCENTIILE
FROM
    (SELECT 
        t.TIME_IN_SEC,
            @unitRank:=CASE
                WHEN @unit <> t.UNIT_ID THEN 0
                ELSE @unitRank + 1
            END AS RANK,
            @unit:=unit_id AS UNIT_ID
    FROM
        (SELECT @unitRank:=- 1) s, (SELECT @unit:=- 1) c, (SELECT 
        @unitRank:=- 1,
            od.ORDER_ID,
            od.UNIT_ID,
            MIN(ose.START_TIME),
            MAX(ose.UPDATE_TIME),
            TIME_TO_SEC(TIMEDIFF(MAX(ose.UPDATE_TIME), MIN(ose.START_TIME))) TIME_IN_SEC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ose.FROM_STATUS IN ('INITIATED' , 'CREATED', 'PROCESSING')
    GROUP BY od.ORDER_ID , od.UNIT_ID
    ORDER BY od.UNIT_ID , TIME_IN_SEC) t) m,
    (SELECT 
        od.UNIT_ID,
            COUNT(DISTINCT od.ORDER_ID) TOTAL_ORDERS,
            CEIL(0.95 * COUNT(DISTINCT od.ORDER_ID)) - 1 N_PERCENTILE
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.ORDER_SOURCE = 'COD'
             AND od.ORDER_STATUS <> 'CANCELLED'
            AND ose.FROM_STATUS IN ('INITIATED' , 'CREATED', 'PROCESSING')
    GROUP BY od.UNIT_ID) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    ud.UNIT_ID = a.UNIT_ID
        AND m.UNIT_ID = a.UNIT_ID
        AND m.RANK = a.N_PERCENTILE
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME;

				     ]]>
					</content>
				</report>

				<report id="1" name="Summary of Dispatch Time TAT Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
					SELECT 
    ud.UNIT_CATEGORY,
    ud.UNIT_NAME,
    CONCAT(FLOOR(m.TIME_IN_SEC / 60),
            ' mins ',
            MOD(m.TIME_IN_SEC, 60),
            ' sec') 95_PERCENTIILE
FROM
    (SELECT 
        t.TIME_IN_SEC,
         @unitRank:=CASE
                WHEN @unit <> t.UNIT_ID THEN 0
                ELSE @unitRank + 1
            END AS RANK,
            @unit:=unit_id AS UNIT_ID
    FROM
        (SELECT @unitRank:=- 1) s, (SELECT @unit:=- 1) c, (SELECT 
        @unitRank:=- 1,
            od.ORDER_ID,
            od.UNIT_ID,
            MIN(ose.START_TIME),
            MAX(ose.UPDATE_TIME),
            TIME_TO_SEC(TIMEDIFF(MAX(ose.UPDATE_TIME), MIN(ose.START_TIME))) TIME_IN_SEC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE,2))
            AND od.ORDER_SOURCE = 'COD'
             AND od.ORDER_STATUS <> 'CANCELLED'
            AND ose.FROM_STATUS IN ('READY_TO_DISPATCH')
    GROUP BY od.ORDER_ID , od.UNIT_ID
    ORDER BY od.UNIT_ID , TIME_IN_SEC) t) m,
    (SELECT 
        od.UNIT_ID,
            COUNT(DISTINCT od.ORDER_ID) TOTAL_ORDERS,
            CEIL(0.95 * COUNT(DISTINCT od.ORDER_ID)) - 1 N_PERCENTILE
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.ORDER_SOURCE = 'COD'
             AND od.ORDER_STATUS <> 'CANCELLED'
            AND ose.FROM_STATUS IN ('READY_TO_DISPATCH')
    GROUP BY od.UNIT_ID) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    ud.UNIT_ID = a.UNIT_ID
        AND m.UNIT_ID = a.UNIT_ID
        AND m.RANK = a.N_PERCENTILE
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

				     ]]>
					</content>
				</report>
			</reports>
		</category>-->
		<!--<category name="Delivery Order with Value higher than 400 TAT Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Summary of Production Time TAT Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
                                        SELECT
    ud.UNIT_CATEGORY,
    ud.UNIT_NAME,
    CONCAT(FLOOR(m.TIME_IN_SEC / 60),
            ' mins ',
            MOD(m.TIME_IN_SEC, 60),
            ' sec') 95_PERCENTIILE
FROM
    (SELECT
        t.TIME_IN_SEC,
            @unitRank:=CASE
                WHEN @unit <> t.UNIT_ID THEN 0
                ELSE @unitRank + 1
            END AS RANK,
            @unit:=unit_id AS UNIT_ID
    FROM
        (SELECT @unitRank:=- 1) s, (SELECT @unit:=- 1) c, (SELECT
        @unitRank:=- 1,
            od.ORDER_ID,
            od.UNIT_ID,
            MIN(ose.START_TIME),
            MAX(ose.UPDATE_TIME),
            TIME_TO_SEC(TIMEDIFF(MAX(ose.UPDATE_TIME), MIN(ose.START_TIME))) TIME_IN_SEC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.ORDER_SOURCE = 'COD'
			AND od.SETTLED_AMOUNT >= 400.00
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ose.FROM_STATUS IN ('INITIATED' , 'CREATED', 'PROCESSING')
    GROUP BY od.ORDER_ID , od.UNIT_ID
    ORDER BY od.UNIT_ID , TIME_IN_SEC) t) m,
    (SELECT
        od.UNIT_ID,
            COUNT(DISTINCT od.ORDER_ID) TOTAL_ORDERS,
            CEIL(0.95 * COUNT(DISTINCT od.ORDER_ID)) - 1 N_PERCENTILE
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.ORDER_SOURCE = 'COD'
             AND od.ORDER_STATUS <> 'CANCELLED'
             AND od.SETTLED_AMOUNT >= 400.00
            AND ose.FROM_STATUS IN ('INITIATED' , 'CREATED', 'PROCESSING')
    GROUP BY od.UNIT_ID) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    ud.UNIT_ID = a.UNIT_ID
        AND m.UNIT_ID = a.UNIT_ID
        AND m.RANK = a.N_PERCENTILE
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

                                     ]]>
					</content>
				</report>

				<report id="1" name="Summary of Dispatch Time TAT Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
                                        
					SELECT
    ud.UNIT_CATEGORY,
    ud.UNIT_NAME,
    CONCAT(FLOOR(m.TIME_IN_SEC / 60),
            ' mins ',
            MOD(m.TIME_IN_SEC, 60),
            ' sec') 95_PERCENTIILE
FROM
    (SELECT
        t.TIME_IN_SEC,
         @unitRank:=CASE
                WHEN @unit <> t.UNIT_ID THEN 0
                ELSE @unitRank + 1
            END AS RANK,
            @unit:=unit_id AS UNIT_ID
    FROM
        (SELECT @unitRank:=- 1) s, (SELECT @unit:=- 1) c, (SELECT
        @unitRank:=- 1,
            od.ORDER_ID,
            od.UNIT_ID,
            MIN(ose.START_TIME),
            MAX(ose.UPDATE_TIME),
            TIME_TO_SEC(TIMEDIFF(MAX(ose.UPDATE_TIME), MIN(ose.START_TIME))) TIME_IN_SEC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE,2))
            AND od.ORDER_SOURCE = 'COD'
             AND od.ORDER_STATUS <> 'CANCELLED'
			AND od.SETTLED_AMOUNT >= 400.00
            AND ose.FROM_STATUS IN ('INITIATED' , 'CREATED', 'PROCESSING','READY_TO_DISPATCH')
    GROUP BY od.ORDER_ID , od.UNIT_ID
    ORDER BY od.UNIT_ID , TIME_IN_SEC) t) m,
    (SELECT
        od.UNIT_ID,
            COUNT(DISTINCT od.ORDER_ID) TOTAL_ORDERS,
            CEIL(0.95 * COUNT(DISTINCT od.ORDER_ID)) - 1 N_PERCENTILE
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
            AND od.ORDER_SOURCE = 'COD'
             AND od.ORDER_STATUS <> 'CANCELLED'
             AND od.SETTLED_AMOUNT >= 400.00
            AND ose.FROM_STATUS IN ('INITIATED' , 'CREATED', 'PROCESSING','READY_TO_DISPATCH')
    GROUP BY od.UNIT_ID) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    ud.UNIT_ID = a.UNIT_ID
        AND m.UNIT_ID = a.UNIT_ID
        AND m.RANK = a.N_PERCENTILE
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

                                     ]]>
					</content>
				</report>
			</reports>
		</category>-->
		<category name="Locality Wise Delivery Order" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="" attachmentType="EXCEL" compress="true">
			<reports>
				<report id="1" name="Summary of Locality Wise Delivery Order" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
					
					SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    cia.LOCALITY,
    SUM(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    COUNT(*) ORDER_COUNT,
    ROUND(SUM(od.TAXABLE_AMOUNT) / COUNT(*),2) APC
FROM
    KETTLE.ORDER_DETAIL od,
    KETTLE_MASTER.UNIT_DETAIL ud,
    KETTLE.CUSTOMER_ADDRESS_INFO cia
WHERE
    od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
            1),
        INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
        INTERVAL 5 HOUR)
        AND od.DELIVERY_ADDRESS = cia.ADDRESS_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_SOURCE = 'COD'
        AND od.UNIT_ID = ud.UNIT_ID
        AND od.TOTAL_AMOUNT > 0.0
GROUP BY ud.UNIT_ID , ud.UNIT_NAME , cia.LOCALITY
ORDER BY ud.UNIT_ID , ud.UNIT_NAME , cia.LOCALITY


				     ]]>
					</content>
				</report>
			</reports>
		</category>
 <category name="Summarized Dispatch and Delivery Delay Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL">
                        <reports>
                        <report id="1" name="Current Month Delivery and Dispatch Email" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                            <![CDATA[

SELECT
        MONTHNAME(CURRENT_DATE) MONTH_NAME,
    a.UNIT_ID,
    a.UNIT_NAME,
    coalesce(b.DISPATCH_DELAY_COUNT, 0) DISPATCH_DELAY_COUNT,
    coalesce(b.DELIVERY_DELAY_COUNT, 0) DELIVERY_DELAY_COUNT,
        coalesce(b.TOTAL_DELAYS, 0) TOTAL_DELAYS,
    coalesce(a.DELIVERY_COUNT, 0) DELIVERY_COUNT,
    TRUNCATE(coalesce(b.DISPATCH_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DISPATCH_DELAY_PERCENTAGE,
        TRUNCATE(coalesce(b.DELIVERY_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DELIVERY_DELAY_PERCENTAGE
FROM
    (SELECT
            ud.UNIT_ID,
            ud.UNIT_NAME,
            COUNT(*) DELIVERY_COUNT
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.ORDER_SOURCE = 'COD'
         AND od.DELIVERY_PARTNER_ID = 8
            AND od.UNIT_ID = ud.UNIT_ID
            AND od.BILLING_SERVER_TIME >= DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -1 MONTH)), INTERVAL 29 HOUR)
    GROUP BY ud.UNIT_ID , UNIT_NAME
    ORDER BY ud.UNIT_NAME ) a
        LEFT OUTER JOIN
    (SELECT
    d.UNIT_ID,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DELIVERY_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DELIVERY_DELAY_COUNT,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DISPATCH_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DISPATCH_DELAY_COUNT,
    SUM(DELAY_COUNT) TOTAL_DELAYS
FROM
    (SELECT
        od.UNIT_ID,
            oen.ENTRY_TYPE,
            COUNT(DISTINCT oen.ORDER_ID) DELAY_COUNT
    FROM
        KETTLE.ORDER_EMAIL_NOTIFICATION oen, KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID = oen.ORDER_ID
            AND od.ORDER_SOURCE = 'COD'
             AND od.DELIVERY_PARTNER_ID = 8
            AND od.BILLING_SERVER_TIME >= DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -1 MONTH)), INTERVAL 29 HOUR)
            AND oen.ENTRY_TYPE IN ('DELIVERY_DELAY' , 'DISPATCH_DELAY')
    GROUP BY od.UNIT_ID , oen.ENTRY_TYPE ) d
GROUP BY d.UNIT_ID ) b ON a.UNIT_ID = b.UNIT_ID
ORDER BY a.UNIT_NAME

 ]]>
                                        </content>
                                </report>
                                <report id="2" name="Last Month Delivery and Dispatch Email" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                            <![CDATA[

SELECT
        MONTHNAME(DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -2 MONTH)), INTERVAL 29 HOUR)) MONTH_NAME,
    a.UNIT_ID,
    a.UNIT_NAME,
    coalesce(b.DISPATCH_DELAY_COUNT, 0) DISPATCH_DELAY_COUNT,
    coalesce(b.DELIVERY_DELAY_COUNT, 0) DELIVERY_DELAY_COUNT,
        coalesce(b.TOTAL_DELAYS, 0) TOTAL_DELAYS,
    coalesce(a.DELIVERY_COUNT, 0) DELIVERY_COUNT,
    TRUNCATE(coalesce(b.DISPATCH_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DISPATCH_DELAY_PERCENTAGE,
        TRUNCATE(coalesce(b.DELIVERY_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DELIVERY_DELAY_PERCENTAGE
FROM
    (SELECT
            ud.UNIT_ID,
            ud.UNIT_NAME,
            COUNT(*) DELIVERY_COUNT
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.ORDER_SOURCE = 'COD'
         AND od.DELIVERY_PARTNER_ID = 8
            AND od.UNIT_ID = ud.UNIT_ID
            AND od.BILLING_SERVER_TIME >= DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -2 MONTH)), INTERVAL 29 HOUR)
            AND od.BILLING_SERVER_TIME < DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -1 MONTH)), INTERVAL 29 HOUR)
    GROUP BY ud.UNIT_ID , UNIT_NAME
    ORDER BY ud.UNIT_NAME ) a
        LEFT OUTER JOIN
    (SELECT
    d.UNIT_ID,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DELIVERY_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DELIVERY_DELAY_COUNT,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DISPATCH_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DISPATCH_DELAY_COUNT,
    SUM(DELAY_COUNT) TOTAL_DELAYS
FROM
    (SELECT
        od.UNIT_ID,
            oen.ENTRY_TYPE,
            COUNT(DISTINCT oen.ORDER_ID) DELAY_COUNT
    FROM
        KETTLE.ORDER_EMAIL_NOTIFICATION oen, KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID = oen.ORDER_ID
            AND od.ORDER_SOURCE = 'COD'
            AND od.DELIVERY_PARTNER_ID = 8
            AND od.BILLING_SERVER_TIME >= DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -2 MONTH)), INTERVAL 29 HOUR)
            AND od.BILLING_SERVER_TIME < DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -1 MONTH)), INTERVAL 29 HOUR)
            AND oen.ENTRY_TYPE IN ('DELIVERY_DELAY' , 'DISPATCH_DELAY')
    GROUP BY od.UNIT_ID , oen.ENTRY_TYPE ) d
GROUP BY d.UNIT_ID ) b ON a.UNIT_ID = b.UNIT_ID
ORDER BY a.UNIT_NAME
 ]]>
                                        </content>
                                </report>
<report id="1" name="Cumulative Delivery and Dispatch Delay Email" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                            <![CDATA[

SELECT
    a.UNIT_ID,
    a.UNIT_NAME,
    coalesce(b.DISPATCH_DELAY_COUNT, 0) DISPATCH_DELAY_COUNT,
    coalesce(b.DELIVERY_DELAY_COUNT, 0) DELIVERY_DELAY_COUNT,
        coalesce(b.TOTAL_DELAYS, 0) TOTAL_DELAYS,
    coalesce(a.DELIVERY_COUNT, 0) DELIVERY_COUNT,
    TRUNCATE(coalesce(b.DISPATCH_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DISPATCH_DELAY_PERCENTAGE,
        TRUNCATE(coalesce(b.DELIVERY_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DELIVERY_DELAY_PERCENTAGE
FROM
    (SELECT
            ud.UNIT_ID,
            ud.UNIT_NAME,
            COUNT(*) DELIVERY_COUNT
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.ORDER_SOURCE = 'COD'
         AND od.DELIVERY_PARTNER_ID = 8
            AND od.UNIT_ID = ud.UNIT_ID
            AND od.BILLING_SERVER_TIME >= '2017-02-01'
    GROUP BY ud.UNIT_ID , UNIT_NAME
    ORDER BY ud.UNIT_NAME ) a
        LEFT OUTER JOIN
    (SELECT
    d.UNIT_ID,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DELIVERY_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DELIVERY_DELAY_COUNT,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DISPATCH_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DISPATCH_DELAY_COUNT,
    SUM(DELAY_COUNT) TOTAL_DELAYS
FROM
    (SELECT
        od.UNIT_ID,
            oen.ENTRY_TYPE,
            COUNT(DISTINCT oen.ORDER_ID) DELAY_COUNT
    FROM
        KETTLE.ORDER_EMAIL_NOTIFICATION oen, KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID = oen.ORDER_ID
            AND od.ORDER_SOURCE = 'COD'
             AND od.DELIVERY_PARTNER_ID = 8
            AND od.BILLING_SERVER_TIME >= '2017-02-01'
            AND oen.ENTRY_TYPE IN ('DELIVERY_DELAY' , 'DISPATCH_DELAY')
    GROUP BY od.UNIT_ID , oen.ENTRY_TYPE ) d
GROUP BY d.UNIT_ID ) b ON a.UNIT_ID = b.UNIT_ID
ORDER BY a.UNIT_NAME


 ]]>
                                        </content>
                                </report>
                                <report id="3" name="Day Wise Delivery and Dispatch Email" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                            <![CDATA[

SELECT
    a.BUSINESS_DATE,
    DAYNAME(a.BUSINESS_DATE) DAY,
    case when DAYOFWEEK(a.BUSINESS_DATE) IN (1,7) then 'WE' else 'WD' end DAY_TYPE,
    a.UNIT_ID,
    a.UNIT_NAME,
    coalesce(b.DISPATCH_DELAY_COUNT, 0) DISPATCH_DELAY_COUNT,
    coalesce(b.DELIVERY_DELAY_COUNT, 0) DELIVERY_DELAY_COUNT,
        coalesce(b.TOTAL_DELAYS, 0) TOTAL_DELAYS,
    coalesce(a.DELIVERY_COUNT, 0) DELIVERY_COUNT,
    TRUNCATE(coalesce(b.DISPATCH_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DISPATCH_DELAY_PERCENTAGE,
        TRUNCATE(coalesce(b.DELIVERY_DELAY_COUNT, 0)/coalesce(a.DELIVERY_COUNT, 0) *100, 2) DELIVERY_DELAY_PERCENTAGE
FROM
    (SELECT
        CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN SUBDATE(DATE(od.BILLING_SERVER_TIME), 1)
                ELSE DATE(od.BILLING_SERVER_TIME)
            END BUSINESS_DATE,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            COUNT(*) DELIVERY_COUNT
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.ORDER_SOURCE = 'COD'
         AND od.DELIVERY_PARTNER_ID = 8
            AND od.UNIT_ID = ud.UNIT_ID
            AND od.BILLING_SERVER_TIME >= DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -1 MONTH)), INTERVAL 29 HOUR)
    GROUP BY BUSINESS_DATE , ud.UNIT_ID , UNIT_NAME
    ORDER BY ud.UNIT_NAME , BUSINESS_DATE) a
        LEFT OUTER JOIN
    (SELECT
    d.UNIT_ID,
    d.BUSINESS_DATE,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DELIVERY_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DELIVERY_DELAY_COUNT,
    SUM(CASE
        WHEN ENTRY_TYPE = 'DISPATCH_DELAY' THEN DELAY_COUNT
        ELSE 0
    END) DISPATCH_DELAY_COUNT,
    SUM(DELAY_COUNT) TOTAL_DELAYS
FROM
    (SELECT
        od.UNIT_ID,
            oen.ENTRY_TYPE,
            CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN SUBDATE(DATE(od.BILLING_SERVER_TIME), 1)
                ELSE DATE(od.BILLING_SERVER_TIME)
            END BUSINESS_DATE,
            COUNT(DISTINCT oen.ORDER_ID) DELAY_COUNT
    FROM
        KETTLE.ORDER_EMAIL_NOTIFICATION oen, KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID = oen.ORDER_ID
            AND od.ORDER_SOURCE = 'COD'
             AND od.DELIVERY_PARTNER_ID = 8
            AND od.BILLING_SERVER_TIME >= DATE_ADD(LAST_DAY(DATE_ADD(CURDATE(), INTERVAL -1 MONTH)), INTERVAL 29 HOUR)
            AND oen.ENTRY_TYPE IN ('DELIVERY_DELAY' , 'DISPATCH_DELAY')
    GROUP BY od.UNIT_ID , oen.ENTRY_TYPE , BUSINESS_DATE) d
GROUP BY d.UNIT_ID , d.BUSINESS_DATE) b ON a.UNIT_ID = b.UNIT_ID
        AND a.BUSINESS_DATE = b.BUSINESS_DATE
ORDER BY a.UNIT_NAME , a.BUSINESS_DATE

 ]]>
                                        </content>
                                </report>
                        </reports>
                </category>
	    	<!-- <category name="MTD Product Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Total" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    B.TOTAL,
    CONCAT(ROUND(22, 0), '%') NON_VEG_TARGET,
    CONCAT(ROUND(28, 0), '%') KULHAD_GUR_CHAI_TARGET,
    CONCAT(ROUND(NON_VEG, 0), '%') NON_VEG_ACTUAL,
    CONCAT(ROUND(KULHAD_GUR_CHAI, 0), '%') KULHAD_GUR_CHAI_ACTUAL
FROM
    (SELECT 
        'AllRegions' AS TOTAL,
            AVG(GIFT_CARD) FEAST_TARGETS,
            AVG(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.NOV11_TARGETS
    GROUP BY 1) B
        LEFT JOIN
    (SELECT 
        TOTAL,
            (SUM(NON_VEG) / SUM(ORDERS)) * 100 NON_VEG,
            (SUM(KULHAD_GUR_CHAI) / SUM(ORDERS)) * 100 KULHAD_GUR_CHAI
    FROM
        (SELECT 
        'AllRegions' AS TOTAL,
            COUNT(DISTINCT A.ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN Z.ATTRIBUTE = 'NON_VEG' THEN A.ORDER_ID
            END)) AS NON_VEG,
            COUNT(DISTINCT (CASE
                WHEN Z.PRODUCT_ID IN (80 , 1033) THEN A.ORDER_ID
            END)) AS KULHAD_GUR_CHAI
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL Z ON Z.PRODUCT_ID = C.PRODUCT_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE <> 'COD'
            AND BILLING_SERVER_TIME >= '2017-11-11 05:00:00'
            AND C.PRODUCT_ID NOT IN (1026 , 1027, 1048)
    GROUP BY 1) C GROUP BY 1) A ON A.TOTAL = B.TOTAL						
]]>
					</content>
				</report>
				<report id="1" name="Region Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    B.UNIT_REGION,
    CONCAT(ROUND(FEAST_TARGETS, 0), '%') NON_VEG_TARGET,
    CONCAT(ROUND(GIFT_BOX_TARGET, 0), '%') KULHAD_GUR_CHAI_TARGET,
    CONCAT(ROUND(NON_VEG, 0), '%') NON_VEG_ACTUAL,
    CONCAT(ROUND(KULHAD_GUR_CHAI, 0), '%') KULHAD_GUR_CHAI_ACTUAL
FROM
    (SELECT 
        UNIT_NAME UNIT_REGION,
            AVG(GIFT_CARD) FEAST_TARGETS,
            AVG(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.NOV11_TARGETS_1
    GROUP BY 1) B
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            (SUM(NON_VEG) / SUM(ORDERS)) * 100 NON_VEG,
            (SUM(KULHAD_GUR_CHAI) / SUM(ORDERS)) * 100 KULHAD_GUR_CHAI
    FROM
        (SELECT 
        UNIT_REGION,
            COUNT(DISTINCT A.ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN Z.ATTRIBUTE = 'NON_VEG' THEN A.ORDER_ID
            END)) AS NON_VEG,
            COUNT(DISTINCT (CASE
                WHEN Z.PRODUCT_ID IN (80 , 1033) THEN A.ORDER_ID
            END)) AS KULHAD_GUR_CHAI
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL Z ON Z.PRODUCT_ID = C.PRODUCT_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE <> 'COD'
            AND BILLING_SERVER_TIME >= '2017-11-11 05:00:00'
            AND C.PRODUCT_ID NOT IN (1026 , 1027, 1048)
    GROUP BY 1) C GROUP BY 1) A ON A.UNIT_REGION = B.UNIT_REGION
						]]>
					</content>
				</report>
				<report id="1" name="Manager Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    B.AREA_MANAGER,
    CONCAT(ROUND(FEAST_TARGETS, 0), '%') NON_VEG_TARGET,
    CONCAT(ROUND(GIFT_BOX_TARGET, 0), '%') KULHAD_GUR_CHAI_TARGET,
    CONCAT(ROUND(NON_VEG, 0), '%') NON_VEG_ACTUAL,
    CONCAT(ROUND(KULHAD_GUR_CHAI, 0), '%') KULHAD_GUR_CHAI_ACTUAL
FROM
    (SELECT 
        AREA_MANAGER,
            AVG(GIFT_CARD) FEAST_TARGETS,
            AVG(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.NOV11_TARGETS_2
    GROUP BY 1) B
        LEFT JOIN
    (SELECT 
        EMP_NAME,
            (SUM(NON_VEG) / SUM(ORDERS)) * 100 NON_VEG,
            (SUM(KULHAD_GUR_CHAI) / SUM(ORDERS)) * 100 KULHAD_GUR_CHAI
    FROM
        (SELECT 
        EMP_NAME,
            COUNT(DISTINCT A.ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN Z.ATTRIBUTE = 'NON_VEG' THEN A.ORDER_ID
            END)) AS NON_VEG,
            COUNT(DISTINCT (CASE
                WHEN Z.PRODUCT_ID IN (80 , 1033) THEN A.ORDER_ID
            END)) AS KULHAD_GUR_CHAI
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL emp ON B.UNIT_MANAGER = emp.EMP_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL Z ON Z.PRODUCT_ID = C.PRODUCT_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE <> 'COD'
            AND BILLING_SERVER_TIME  >= '2017-11-11 05:30:00'
            AND C.PRODUCT_ID NOT IN (1026 , 1027, 1048)
    GROUP BY 1) C GROUP BY 1) A ON A.EMP_NAME = B.AREA_MANAGER
						]]>
					</content>
				</report>
				<report id="1" name="Unit Wise" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[	
SELECT 
    B.UNIT_NAME,
    CONCAT(ROUND(FEAST_TARGETS, 0), '%') NON_VEG_TARGET,
    CONCAT(ROUND(GIFT_BOX_TARGET, 0), '%') KULHAD_GUR_CHAI_TARGET,
    CONCAT(ROUND(NON_VEG, 0), '%') NON_VEG_ACTUAL,
    CONCAT(ROUND(KULHAD_GUR_CHAI, 0), '%') KULHAD_GUR_CHAI_ACTUAL
FROM
    (SELECT 
        UNIT_ID,
            UNIT_NAME,
            AVG(GIFT_CARD) FEAST_TARGETS,
            AVG(GIFT_BOX) GIFT_BOX_TARGET
    FROM
        CLM_ANALYTICS.NOV11_TARGETS
    GROUP BY 1 , 2) B
        LEFT JOIN
    (SELECT 
        UNIT_ID,
            NON_VEG / ORDERS * 100 NON_VEG,
            KULHAD_GUR_CHAI / ORDERS * 100 KULHAD_GUR_CHAI
    FROM
        (SELECT 
        B.UNIT_ID,
            COUNT(DISTINCT A.ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN Z.ATTRIBUTE = 'NON_VEG' THEN A.ORDER_ID
            END)) AS NON_VEG,
            COUNT(DISTINCT (CASE
                WHEN Z.PRODUCT_ID IN (80 , 1033) THEN A.ORDER_ID
            END)) AS KULHAD_GUR_CHAI
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL Z ON Z.PRODUCT_ID = C.PRODUCT_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND ORDER_SOURCE <> 'COD'
            AND BILLING_SERVER_TIME >= '2017-11-11 05:30:00'
            AND C.PRODUCT_ID NOT IN (1026 , 1027, 1048)
    GROUP BY 1) C) A ON A.UNIT_ID = B.UNIT_ID
					]]>
					</content>
				</report>						
			</reports>
		</category> -->

 <category name="Summary of Delivery Report for Missed Call Service" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
                        <reports>
                                <report id="1" name="Summary of Missed Call Service Issue" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT 
    ed.EMP_NAME AREA_MANAGER,
    ud.UNIT_ID,
    ud.UNIT_NAME,
    DELIVERED_VIA_MISS_CALL,
    AVG_DELIVERY_TIME_MISS_CALL,
    DELIVERED_VIA_ASSEMBLY_SCREEN,
    AVG_DELIVERY_TIME_ASSEMBLY_SCREEN,
    CEIL(DELIVERED_VIA_MISS_CALL / (DELIVERED_VIA_MISS_CALL + DELIVERED_VIA_ASSEMBLY_SCREEN) * 100) PERCENTAGE_ADOPTION
FROM
    (SELECT 
        UNIT_ID,
            MAX(CASE
                WHEN
                    REASON_TEXT IS NOT NULL
                        && REASON_TEXT <> ''
                        && REASON_TEXT LIKE '%SDP missed call%'
                THEN
                    NO_OF_ORDERS
                ELSE 0
            END) DELIVERED_VIA_MISS_CALL,
            MAX(CASE
                WHEN REASON_TEXT LIKE '%SDP missed call%' THEN AVG_DELIVERY_TIME_POST_DISPATCH
                ELSE 0
            END) AVG_DELIVERY_TIME_MISS_CALL,
            MAX(CASE
                WHEN
                    REASON_TEXT IS NULL || REASON_TEXT = ''
                        || REASON_TEXT NOT LIKE '%SDP missed call%'
                THEN
                    NO_OF_ORDERS
                ELSE 0
            END) DELIVERED_VIA_ASSEMBLY_SCREEN,
            MAX(CASE
                WHEN
                    REASON_TEXT IS NULL || REASON_TEXT = ''
                        || REASON_TEXT NOT LIKE '%SDP missed call%'
                THEN
                    AVG_DELIVERY_TIME_POST_DISPATCH
                ELSE 0
            END) AVG_DELIVERY_TIME_ASSEMBLY_SCREEN
    FROM
        (SELECT 
        od.UNIT_ID,
            REASON_TEXT,
            COUNT(DISTINCT od.ORDER_ID) NO_OF_ORDERS,
            CEIL(AVG(TIME_TO_SEC(TIMEDIFF(ose.UPDATE_TIME, ose.START_TIME)) / 60)) AVG_DELIVERY_TIME_POST_DISPATCH
    FROM
        KETTLE.ORDER_STATUS_EVENT ose, KETTLE.ORDER_DETAIL od
    WHERE
        ose.TRANSITION_STATUS = 'SUCCESS'
            AND ose.ORDER_ID = od.ORDER_ID
            AND ose.FROM_STATUS = 'SETTLED'
            AND ose.TO_STATUS = 'DELIVERED'
            AND od.DELIVERY_PARTNER_ID = 8
            AND od.BUSINESS_DATE = DATE_ADD(CURRENT_DATE(), INTERVAL - 1 DAY)
    GROUP BY od.UNIT_ID , ose.REASON_TEXT) a
    GROUP BY UNIT_ID) m,
    KETTLE_MASTER.UNIT_DETAIL ud,
    KETTLE_MASTER.EMPLOYEE_DETAIL ed
WHERE
    m.UNIT_ID = ud.UNIT_ID
        AND ud.UNIT_MANAGER = ed.EMP_ID
        AND DELIVERED_VIA_ASSEMBLY_SCREEN > 0
ORDER BY AREA_MANAGER , PERCENTAGE_ADOPTION
 ]]>
                                        </content>
                                </report>
                        </reports>
                </category>
 <category name="Summary of Inventory Update Defaulters List" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
                        <reports>
                                <report id="1" name="Summary of Hourly Inventory Update Defaulters List" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[
SELECT 
    ed.EMP_NAME AREA_MANAGER,
    m.UNIT_ID,
    ud.UNIT_NAME,
    m.STOCK_TAKING_COUNT,
    m.INVENTORY_UPDATE_COUNT,
    CASE
        WHEN
            m.STOCK_TAKING_COUNT > 1
                AND m.INVENTORY_UPDATE_COUNT > 0
        THEN
            '1 - MULTIPLE STOCK INS AND UPDATES'
        WHEN
            m.STOCK_TAKING_COUNT > 1
                AND m.INVENTORY_UPDATE_COUNT = 0
        THEN
            '3 - MULTIPLE STOCK INS'
        WHEN
            m.STOCK_TAKING_COUNT <= 1
                AND m.INVENTORY_UPDATE_COUNT > 0
        THEN
            '2 - MULTIPLE UPDATES'
    END ISSUE_DETAILS
FROM
    (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.EVENT_TYPE = 'STOCK_IN' THEN NO_OF_EVENTS
                ELSE 0
            END) STOCK_TAKING_COUNT,
            SUM(CASE
                WHEN a.EVENT_TYPE = 'UPDATE' THEN NO_OF_EVENTS
                ELSE 0
            END) INVENTORY_UPDATE_COUNT
    FROM
        (SELECT 
        iue.UNIT_ID, iue.EVENT_TYPE, COUNT(*) NO_OF_EVENTS
    FROM
        KETTLE.INVENTORY_UPDATE_EVENT iue
    WHERE
        iue.UPDATE_TIME BETWEEN DATE_ADD(DATE_ADD(DATE(CURRENT_TIMESTAMP), INTERVAL - 1 DAY), INTERVAL 330 MINUTE) AND DATE_ADD(DATE(CURRENT_TIMESTAMP), INTERVAL 330 MINUTE)
            AND iue.EVENT_TYPE IN ('UPDATE' , 'STOCK_IN')
    GROUP BY iue.UNIT_ID , iue.EVENT_TYPE) a
    GROUP BY a.UNIT_ID) m,
    KETTLE_MASTER.UNIT_DETAIL ud,
    KETTLE_MASTER.EMPLOYEE_DETAIL ed
WHERE
    m.UNIT_ID = ud.UNIT_ID
        AND ud.UNIT_MANAGER = ed.EMP_ID
        AND (m.STOCK_TAKING_COUNT > 1
        OR m.INVENTORY_UPDATE_COUNT > 0)
ORDER BY AREA_MANAGER , ISSUE_DETAILS
]]>
                                        </content>
                                </report>
                        </reports>
                </category>
<category name="MTD-DINE/COD LSM REPORT" type="Automated" accessCode="Automated" id="1" 
	fromEmail="<EMAIL>" toEmails="<EMAIL>,<EMAIL>" schedule="">
	<reports>
		<report id="1" name="Total" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
			<content>
				<![CDATA[
SELECT
    TOTAL,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT
        TOTAL,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT
        'AllRegions' AS TOTAL,
            ORDER_SOURCE,
            A.ORDER_ID,
            OFFER_CODE,
            PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME > DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL dayofmonth(ADDTIME(UTC_TIMESTAMP, '05:30:00'))-1 DAY)), INTERVAL 5 HOUR)) A
    GROUP BY 1) Z;
]]>
			</content>
		</report>
		<report id="1" name="Region Wise" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
			<content>
				<![CDATA[
SELECT
    UNIT_REGION,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT
        UNIT_REGION,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT
        UNIT_REGION,
            ORDER_SOURCE,
            A.ORDER_ID,
            OFFER_CODE,
            PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME > DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL dayofmonth(ADDTIME(UTC_TIMESTAMP, '05:30:00'))-1 DAY)), INTERVAL 5 HOUR)) A
    GROUP BY 1) Z
]]>
			</content>
		</report>
		<report id="1" name="Manager Wise" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
			<content>
				<![CDATA[
SELECT
    UCASE(EMP_NAME) AREA_MANAGER,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT
        EMP_NAME,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT
        EMP_NAME, ORDER_SOURCE, A.ORDER_ID, OFFER_CODE, PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL C ON C.EMP_ID = B.UNIT_MANAGER
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME > DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL dayofmonth(ADDTIME(UTC_TIMESTAMP, '05:30:00'))-1 DAY)), INTERVAL 5 HOUR)) A
    GROUP BY 1) Z


]]>
			</content>
		</report>
		<report id="1" name="Unit Wise" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
			<content>
				<![CDATA[

SELECT
    UNIT_NAME,
    ORDERS 'TOTAL TICKETS',
    TOTAL_REDEMPTION 'TOTAL REDEMPTION',
    DINE_ORDERS - GIFT_CARD_TICKETS 'DINE TICKETS',
    DINE_IN_REDEMPTION 'DINE REDEMPTION',
    DINE_IN_REDEMPTION_COUPONS 'DINE REDEMPTION COUPONS',
    DINE_IN_REDEMPTION_FLYER 'DINE REDEMPTION FLYER',
    COD_ORDERS 'COD TICKETS',
    COD_REDEMPTION 'COD REDEMPTION',
    COD_REDEMPTION_COUPONS 'COD REDEMPTION COUPONS',
    COD_REDEMPTION_FLYER 'COD REDEMPTION FLYER'
FROM
    (SELECT
        UNIT_NAME,
            COUNT(DISTINCT ORDER_ID) ORDERS,
            COUNT(DISTINCT (CASE
                WHEN OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50') THEN ORDER_ID
            END)) AS TOTAL_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE <> 'COD' THEN ORDER_ID
            END)) AS DINE_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE <> 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS DINE_IN_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN ORDER_SOURCE = 'COD' THEN ORDER_ID
            END)) AS COD_ORDERS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER' , 'MUSKAFREE',
'ICETFREE', 'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('MUSKAFREE' , 'ICETFREE',
'MUMBAI50')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_COUPONS,
            COUNT(DISTINCT (CASE
                WHEN
                    ORDER_SOURCE = 'COD'
                        AND OFFER_CODE IN ('SUMMER','WINTER')
                THEN
                    ORDER_ID
            END)) AS COD_REDEMPTION_FLYER,
            COUNT(DISTINCT (CASE
                WHEN PRODUCT_ID IN (1026 , 1027, 1048) THEN A.ORDER_ID
            END)) AS GIFT_CARD_TICKETS
    FROM
        (SELECT
        UNIT_NAME, ORDER_SOURCE, A.ORDER_ID, OFFER_CODE, PRODUCT_ID
    FROM
        KETTLE.ORDER_DETAIL A
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM C ON C.ORDER_ID = A.ORDER_ID
    WHERE
        ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE = 'ORDER'
            AND A.UNIT_ID NOT IN (26014 , 26013)
            AND A.BILLING_SERVER_TIME > DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL dayofmonth(ADDTIME(UTC_TIMESTAMP, '05:30:00'))-1 DAY)), INTERVAL 5 HOUR)) A
    GROUP BY 1) Z


]]>
			</content>
		</report>
	</reports>
</category>
		<category name="Daily NPS Dump Report" type="Automated" accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>,<EMAIL>" schedule="">
			<reports>
				<report id="7" name="NPS Dump Unit Wise " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    ond.GENERATED_ORDER_ID,
    ond.NPS_SCORE,
    ond.NPS_QUESTION,
    ond.NPS_RESPONSE,
    GROUP_CONCAT(CONCAT(oi.PRODUCT_NAME, '(', oi.QUANTITY, ')')
        SEPARATOR '|') ORDER_ITEMS,
    od.ORDER_SOURCE,
    od.BILLING_SERVER_TIME BILL_TIME,
    ose.UPDATE_TIME DISPATCH_TIME,
    CASE
        WHEN
            ose.UPDATE_TIME IS NOT NULL
        THEN
            (TIMESTAMPDIFF(MINUTE,
                od.BILLING_SERVER_TIME,
                ose.UPDATE_TIME))
        ELSE NULL
    END DISPATCH_TIME_IN_MINUTES,
    ose1.UPDATE_TIME DELIVERY_TIME,
    CASE
        WHEN
            ose1.UPDATE_TIME IS NOT NULL
        THEN
            (TIMESTAMPDIFF(MINUTE,
                od.BILLING_SERVER_TIME,
                ose1.UPDATE_TIME))
        ELSE NULL
    END DELIVERY_TIME_IN_MINUTES,
    od.SETTLED_AMOUNT,
    cia.LOCALITY,
    cp.PARTNER_DISPLAY_NAME
FROM
    KETTLE.ORDER_NPS_DETAIL ond
        INNER JOIN
    KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ond.ORDER_ID
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    KETTLE.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE.CUSTOMER_ADDRESS_INFO cia ON od.DELIVERY_ADDRESS = cia.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE.ORDER_STATUS_EVENT ose ON ose.ORDER_ID = od.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED'
        LEFT OUTER JOIN
    KETTLE.ORDER_STATUS_EVENT ose1 ON ose1.ORDER_ID = od.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED'
WHERE
    ond.SURVEY_CREATION_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
            1),
        INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
        INTERVAL 5 HOUR)
GROUP BY ond.GENERATED_ORDER_ID , ond.NPS_SCORE , od.ORDER_SOURCE , od.BILLING_SERVER_TIME , od.SETTLED_AMOUNT , cia.LOCALITY;

                                                ]]>
					</content>

				</report>
			</reports>
		</category>
</categories>
</ReportCategories>
