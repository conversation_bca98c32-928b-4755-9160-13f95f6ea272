<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Channel Partner Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails=""
                        schedule="">
			<reports>
				<report id="1" name="Channel Partner Report - Chaayos Tickets" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    'Chaayos' AS total,
    COALESCE(b.Chaayos_Delivery + b.Web_App + b.SWIGGY + b.Zomato + b.DUNZO + b.UBER_EATS + b.AMAZON + b.Others,
            0) Overall,
    COALESCE(c.Chaayos_Delivery + c.Web_App + c.SWIGGY + c.Zomato + c.DUNZO + c.UBER_EATS + c.AMAZON + c.Others,
            0) Overall_LWSD,
    COALESCE(b.Chaayos_Delivery, 0) Cha<PERSON>os_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_lwsd,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_lwsd,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_lwsd,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_lwsd,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_lwsd,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_lwsd,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_lwsd,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_lwsd
FROM
    (SELECT 
        'chaayos' AS total,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b
        LEFT JOIN
    (SELECT 
        'chaayos' AS total,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY 1) c ON b.total = c.total
				     ]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Chaayos APC" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    'Chaayos' as Total,
	COALESCE(b.OVERALL, 0) Overall,
    COALESCE(c.OVERALL, 0) Overall_lwsd,
	COALESCE(b.Chaayos_Delivery, 0) Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_lwsd,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_lwsd,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_lwsd,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_lwsd,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_lwsd,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_lwsd,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_lwsd,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_lwsd
FROM
    (SELECT 
        'Chaayos' as Total,
         ROUND(COALESCE(SUM(TAXABLE_AMOUNT),0)/COUNT(DISTINCT (ORDER_ID)),0) AS OVERALL,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)),0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)),0) AS Web_App,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)),0) AS SWIGGY,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)),0) AS Zomato,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)),0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)),0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)),0) AS AMAZON,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)),0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b 
        LEFT JOIN
    (SELECT 
        'Chaayos' as total,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT),0)/COUNT(DISTINCT (ORDER_ID)),0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)),0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)),0) AS Web_App,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)),0) AS SWIGGY,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)),0) AS Zomato,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)),0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)),0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)),0) AS AMAZON,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)),0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY 1) c ON c.total = b.total GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Region Tickets" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_REGION,
    coalesce(b.Chaayos_Delivery+b.Web_App+b.SWIGGY+b.Zomato+b.DUNZO+b.UBER_EATS+b.AMAZON+b.Others,0) TOTAL,
	coalesce(c.Chaayos_Delivery+c.Web_App+c.SWIGGY+c.Zomato+c.DUNZO+c.UBER_EATS+c.AMAZON+c.Others,0) TOTAL_LWSD,
	COALESCE(b.Chaayos_Delivery, 0)Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_lwsd,
    COALESCE(b.Web_App, 0)Web_App,
    COALESCE(c.Web_App, 0) Web_App_lwsd,
    COALESCE(b.SWIGGY, 0)SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_lwsd,
    COALESCE(b.Zomato, 0)Zomato,
    COALESCE(c.Zomato, 0) Zomato_lwsd,
    COALESCE(b.DUNZO, 0)DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_lwsd,
    COALESCE(b.UBER_EATS, 0)UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_lwsd,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_lwsd,
    COALESCE(b.Others, 0)Others,
    COALESCE(c.Others, 0) Others_lwsd
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_REGION = ud.UNIT_REGION
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY 1) c ON c.UNIT_REGION = ud.UNIT_REGION
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
        GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Region APC" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_REGION,
	COALESCE(b.OVERALL, 0)Overall,
    COALESCE(c.OVERALL, 0) Overall_lwsd,
	COALESCE(b.Chaayos_Delivery, 0)Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_lwsd,
    COALESCE(b.Web_App, 0)Web_App,
    COALESCE(c.Web_App, 0) Web_App_lwsd,
    COALESCE(b.SWIGGY, 0)SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_lwsd,
    COALESCE(b.Zomato, 0)Zomato,
    COALESCE(c.Zomato, 0) Zomato_lwsd,
    COALESCE(b.DUNZO, 0)DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_lwsd,
    COALESCE(b.UBER_EATS, 0)UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_lwsd,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_lwsd,
    COALESCE(b.Others, 0)Others,
    COALESCE(c.Others, 0) Others_lwsd
FROM    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
         ROUND(COALESCE(SUM(TAXABLE_AMOUNT),0)/COUNT(DISTINCT (ORDER_ID)),0) AS OVERALL,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)),0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)),0) AS Web_App,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)),0) AS SWIGGY,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)),0) AS Zomato,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)),0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)),0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)),0) AS AMAZON,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)),0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_REGION = ud.UNIT_REGION
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT),0)/COUNT(DISTINCT (ORDER_ID)),0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)),0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)),0) AS Web_App,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)),0) AS SWIGGY,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)),0) AS Zomato,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)),0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)),0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)),0) AS AMAZON,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)),0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY 1) c  ON c.UNIT_REGION = ud.UNIT_REGION
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
        GROUP BY 1
					]]>
					</content>
				</report>
								<report id="1" name="Channel Partner Report - Unit Tickets" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    coalesce(b.Chaayos_Delivery+b.Web_App+b.SWIGGY+b.Zomato+b.DUNZO+b.UBER_EATS+b.AMAZON+b.Others,0) TOTAL,
	coalesce(c.Chaayos_Delivery+c.Web_App+c.SWIGGY+c.Zomato+c.DUNZO+c.UBER_EATS+c.AMAZON+c.Others,0) TOTAL_LWSD,
	COALESCE(b.Chaayos_Delivery, 0)Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_lwsd,
    COALESCE(b.Web_App, 0)Web_App,
    COALESCE(c.Web_App, 0) Web_App_lwsd,
    COALESCE(b.SWIGGY, 0)SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_lwsd,
    COALESCE(b.Zomato, 0)Zomato,
    COALESCE(c.Zomato, 0) Zomato_lwsd,
    COALESCE(b.DUNZO, 0)DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_lwsd,
    COALESCE(b.UBER_EATS, 0)UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_lwsd,
    COALESCE(b.AMAZON, 0)AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_lwsd,
    COALESCE(b.Others, 0)Others,
    COALESCE(c.Others, 0) Others_lwsd
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_NAME = ud.UNIT_NAME
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY 1) c ON c.UNIT_NAME = ud.UNIT_NAME
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
        GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Unit APC" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
	COALESCE(b.OVERALL, 0)Overall,
    COALESCE(c.OVERALL, 0) Overall_lwsd,
	COALESCE(b.Chaayos_Delivery, 0)Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_lwsd,
    COALESCE(b.Web_App, 0)Web_App,
    COALESCE(c.Web_App, 0) Web_App_lwsd,
    COALESCE(b.SWIGGY, 0)SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_lwsd,
    COALESCE(b.Zomato, 0)Zomato,
    COALESCE(c.Zomato, 0) Zomato_lwsd,
    COALESCE(b.DUNZO, 0)DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_lwsd,
    COALESCE(b.UBER_EATS, 0)UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_lwsd,
    COALESCE(b.AMAZON, 0)AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_lwsd,
    COALESCE(b.Others, 0)Others,
    COALESCE(c.Others, 0) Others_lwsd
FROM    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
         ROUND(COALESCE(SUM(TAXABLE_AMOUNT),0)/COUNT(DISTINCT (ORDER_ID)),0) AS OVERALL,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)),0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)),0) AS Web_App,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)),0) AS SWIGGY,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)),0) AS Zomato,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)),0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)),0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)),0) AS AMAZON,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)),0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_NAME = ud.UNIT_NAME
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT),0)/COUNT(DISTINCT (ORDER_ID)),0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)),0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)),0) AS Web_App,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)),0) AS SWIGGY,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)),0) AS Zomato,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)),0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)),0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)),0) AS AMAZON,
             ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END),0)/COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)),0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY 1) c  ON c.UNIT_NAME = ud.UNIT_NAME
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
        GROUP BY 1
					]]>
					</content>
				</report>

				<report id="1" name="Channel Partner Report - Chaayos Tickets MTD/LMTD" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    'Chaayos' AS total,
    COALESCE(b.Chaayos_Delivery + b.Web_App + b.SWIGGY + b.Zomato + b.DUNZO + b.UBER_EATS + b.AMAZON + b.Others,
            0) Overall,
    COALESCE(c.Chaayos_Delivery + c.Web_App + c.SWIGGY + c.Zomato + c.DUNZO + c.UBER_EATS + c.AMAZON + c.Others,
            0) Overall_LMTD,
    COALESCE(b.Chaayos_Delivery, 0) Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_LMTD,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_LMTD,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_LMTD,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_LMTD,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_LMTD,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_LMTD,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_LMTD,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_LMTD
FROM
    (SELECT 
        'chaayos' AS total,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b
        LEFT JOIN
    (SELECT 
        'chaayos' AS total,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 1 MONTH), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND SUBDATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 1 MONTH)
    GROUP BY 1) c ON b.total = c.total
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Chaayos APC MTD/LMTD" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    'Chaayos' AS Total,
    COALESCE(b.OVERALL, 0) Overall,
    COALESCE(c.OVERALL, 0) Overall_LMTD,
    COALESCE(b.Chaayos_Delivery, 0) Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_LMTD,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_LMTD,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_LMTD,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_LMTD,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_LMTD,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_LMTD,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_LMTD,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_LMTD
FROM
    (SELECT 
        'Chaayos' AS Total,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT), 0) / COUNT(DISTINCT (ORDER_ID)), 0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)), 0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)), 0) AS Web_App,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)), 0) AS SWIGGY,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)), 0) AS Zomato,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)), 0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)), 0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)), 0) AS AMAZON,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)), 0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b
        LEFT JOIN
    (SELECT 
        'Chaayos' AS total,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT), 0) / COUNT(DISTINCT (ORDER_ID)), 0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)), 0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)), 0) AS Web_App,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)), 0) AS SWIGGY,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)), 0) AS Zomato,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)), 0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)), 0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)), 0) AS AMAZON,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)), 0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 1 MONTH), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND SUBDATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 1 MONTH)
    GROUP BY 1) c ON c.total = b.total
GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Region Tickets MTD/LMTD" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_REGION,
    coalesce(b.Chaayos_Delivery+b.Web_App+b.SWIGGY+b.Zomato+b.DUNZO+b.UBER_EATS+b.AMAZON+b.Others,0) TOTAL,
	coalesce(c.Chaayos_Delivery+c.Web_App+c.SWIGGY+c.Zomato+c.DUNZO+c.UBER_EATS+c.AMAZON+c.Others,0) TOTAL_LMTD,
	COALESCE(b.Chaayos_Delivery, 0)Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_LMTD,
    COALESCE(b.Web_App, 0)Web_App,
    COALESCE(c.Web_App, 0) Web_App_LMTD,
    COALESCE(b.SWIGGY, 0)SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_LMTD,
    COALESCE(b.Zomato, 0)Zomato,
    COALESCE(c.Zomato, 0) Zomato_LMTD,
    COALESCE(b.DUNZO, 0)DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_LMTD,
    COALESCE(b.UBER_EATS, 0)UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_LMTD,
    COALESCE(b.AMAZON, 0)AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_LMTD,
    COALESCE(b.Others, 0)Others,
    COALESCE(c.Others, 0) Others_LMTD
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_REGION = ud.UNIT_REGION
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 1 MONTH), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND SUBDATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 1 MONTH)
    GROUP BY 1) c ON c.UNIT_REGION = ud.UNIT_REGION
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
        GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Region APC MTD/LMTD" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_REGION,
    COALESCE(b.OVERALL, 0) Overall,
    COALESCE(c.OVERALL, 0) Overall_LMTD,
    COALESCE(b.Chaayos_Delivery, 0) Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_LMTD,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_LMTD,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_LMTD,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_LMTD,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_LMTD,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_LMTD,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_LMTD,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_LMTD
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT), 0) / COUNT(DISTINCT (ORDER_ID)), 0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)), 0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)), 0) AS Web_App,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)), 0) AS SWIGGY,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)), 0) AS Zomato,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)), 0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)), 0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)), 0) AS AMAZON,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)), 0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
             AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_REGION = ud.UNIT_REGION
        LEFT JOIN
    (SELECT 
        UNIT_REGION,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT), 0) / COUNT(DISTINCT (ORDER_ID)), 0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)), 0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)), 0) AS Web_App,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)), 0) AS SWIGGY,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)), 0) AS Zomato,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)), 0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)), 0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)), 0) AS AMAZON,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)), 0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 1 MONTH), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND SUBDATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 1 MONTH)
    GROUP BY 1) c ON c.UNIT_REGION = ud.UNIT_REGION
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Unit Tickets MTD/LMTD" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    COALESCE(b.Chaayos_Delivery + b.Web_App + b.SWIGGY + b.Zomato + b.DUNZO + b.UBER_EATS + b.AMAZON + b.Others,
            0) TOTAL,
    COALESCE(c.Chaayos_Delivery + c.Web_App + c.SWIGGY + c.Zomato + c.DUNZO + c.UBER_EATS + c.AMAZON + c.Others,
            0) TOTAL_LMTD,
    COALESCE(b.Chaayos_Delivery, 0) Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_LMTD,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_LMTD,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_LMTD,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_LMTD,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_LMTD,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_LMTD,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_LMTD,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_LMTD
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_NAME = ud.UNIT_NAME
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)) AS Chaayos_Delivery,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)) AS Web_App,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)) AS SWIGGY,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)) AS Zomato,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)) AS DUNZO,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)) AS UBER_EATS,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)) AS AMAZON,
            COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 1 MONTH), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND SUBDATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 1 MONTH)
    GROUP BY 1) c ON c.UNIT_NAME = ud.UNIT_NAME
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
GROUP BY 1
					]]>
					</content>
				</report>
				<report id="1" name="Channel Partner Report - Unit APC MTD/LMTD" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    COALESCE(b.OVERALL, 0) Overall,
    COALESCE(c.OVERALL, 0) Overall_LMTD,
    COALESCE(b.Chaayos_Delivery, 0) Chaayos_Delivery,
    COALESCE(c.Chaayos_Delivery, 0) Chaayos_Delivery_LMTD,
    COALESCE(b.Web_App, 0) Web_App,
    COALESCE(c.Web_App, 0) Web_App_LMTD,
    COALESCE(b.SWIGGY, 0) SWIGGY,
    COALESCE(c.SWIGGY, 0) SWIGGY_LMTD,
    COALESCE(b.Zomato, 0) Zomato,
    COALESCE(c.Zomato, 0) Zomato_LMTD,
    COALESCE(b.DUNZO, 0) DUNZO,
    COALESCE(c.DUNZO, 0) DUNZO_LMTD,
    COALESCE(b.UBER_EATS, 0) UBER_EATS,
    COALESCE(c.UBER_EATS, 0) UBER_EATS_LMTD,
    COALESCE(b.AMAZON, 0) AMAZON,
    COALESCE(c.AMAZON, 0) AMAZON_LMTD,
    COALESCE(b.Others, 0) Others,
    COALESCE(c.Others, 0) Others_LMTD
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT), 0) / COUNT(DISTINCT (ORDER_ID)), 0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)), 0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)), 0) AS Web_App,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)), 0) AS SWIGGY,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)), 0) AS Zomato,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)), 0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)), 0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)), 0) AS AMAZON,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)), 0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY 1) b ON b.UNIT_NAME = ud.UNIT_NAME
        LEFT JOIN
    (SELECT 
        UNIT_NAME,
            ROUND(COALESCE(SUM(TAXABLE_AMOUNT), 0) / COUNT(DISTINCT (ORDER_ID)), 0) AS OVERALL,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 2 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 2 THEN ORDER_ID
            END)), 0) AS Chaayos_Delivery,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 14 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 14 THEN ORDER_ID
            END)), 0) AS Web_App,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 6 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 6 THEN ORDER_ID
            END)), 0) AS SWIGGY,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 3 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 3 THEN ORDER_ID
            END)), 0) AS Zomato,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 17 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 17 THEN ORDER_ID
            END)), 0) AS DUNZO,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 15 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 15 THEN ORDER_ID
            END)), 0) AS UBER_EATS,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID = 18 THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID = 18 THEN ORDER_ID
            END)), 0) AS AMAZON,
            ROUND(COALESCE(SUM(CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN TAXABLE_AMOUNT
            END), 0) / COUNT(DISTINCT (CASE
                WHEN PARTNER_ID NOT IN (2 , 14, 6, 3, 17, 15, 18) THEN ORDER_ID
            END)), 0) AS Others
    FROM
        KETTLE.ORDER_DETAIL OD
    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON OD.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL C ON OD.UNIT_ID = C.UNIT_ID
    WHERE
        OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.TOTAL_AMOUNT <> '0.00'
            AND OD.ORDER_SOURCE = 'COD'
            AND OD.ORDER_TYPE = 'order'
            AND OD.ORDER_STATUS <> 'CANCELLED'
            AND OD.BILLING_SERVER_TIME BETWEEN DATE_FORMAT(DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 1 MONTH), INTERVAL 5 HOUR), '%Y-%m-01 05:00:00') AND SUBDATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 1 MONTH)
    GROUP BY 1) c ON c.UNIT_NAME = ud.UNIT_NAME
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
GROUP BY 1
					]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>