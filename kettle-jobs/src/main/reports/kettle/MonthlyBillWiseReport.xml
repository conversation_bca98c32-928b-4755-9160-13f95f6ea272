<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Monthly Bill Wise Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails=""
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="1" name="Monthly Bill Wise Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ORDER_ID AS 'BILL NO',
    BILLING_SERVER_TIME 'DATE',
    TAXABLE_AMOUNT - ZERO_TAX_AMOUNT + DISCOUNT + TOTAL_TAX AS 'GROSS AMOUNT',
    TAXABLE_AMOUNT - ZERO_TAX_AMOUNT + TOTAL_TAX AS 'BILL AMOUNT',
    DISCOUNT AS DISCOUNT,
    TOTAL_TAX AS TAX,
    0 AS 'RETURN AMOUNT',
    0 AS 'RETURN TAX',
    TAXABLE_AMOUNT - ZERO_TAX_AMOUNT AS 'NETSALE'
FROM
    (SELECT 
        od.ORDER_ID,
            od.BILLING_SERVER_TIME,
            COALESCE(od.DISCOUNT_AMOUNT, 0) + COALESCE(od.PROMOTIONAL_DISCOUNT, 0) DISCOUNT,
            od.TOTAL_TAX,
            od.TAXABLE_AMOUNT,
            od.SETTLED_AMOUNT,
            SUM(CASE
                WHEN oi.BILL_TYPE = 'ZERO_TAX' AND oi.PRODUCT_ID != 1291 THEN oi.PRICE * oi.QUANTITY
                ELSE 0
            END) ZERO_TAX_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                UNIT_ID = :unitId
                AND BUSINESS_DATE = SUBDATE(DATE_SUB(DATE_SUB(CURRENT_DATE,INTERVAL 1 MONTH), INTERVAL DAYOFMONTH(CURRENT_DATE)-1 DAY), 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                UNIT_ID = :unitId
                AND BUSINESS_DATE = LAST_DAY(DATE_SUB(CURRENT_DATE,INTERVAL 1 MONTH)))
            AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) a
WHERE
    a.TAXABLE_AMOUNT - a.ZERO_TAX_AMOUNT + a.DISCOUNT > 0;

				     ]]>
					</content>
					<params>
						<param name="unitId" displayName="unitId"
							dataType="INTEGER" />
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
