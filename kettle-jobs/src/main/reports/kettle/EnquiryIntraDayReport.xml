<ReportCategories xmlns="http://www.w3schools.com">
        <categories>
<category name="Call Center Enquiry Data" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
                        <reports>
                                <report id="1" name="Summary of Call Center Enquiry Data" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
SELECT
    distinct
    DATE(oei.ENQUIRY_TIME, '05:30:00') ENQUIRY_DATE,
    HOUR(oei.ENQUIRY_TIME, '05:30:00') ENQUIRY_HOUR,
    ud.UNIT_ID,
    ud.UNIT_NAME,
    pd.PRODUCT_NAME, 
    oei.CUSTOMER_ID,    
    case when oei.ORDER_ID is null then 'ORDER_LOST'
    when oei.ORDER_ID is not null && oei.IS_REPLACEMENT_SERVED = 1 then 'REPLACEMENT_SERVED'
        when oei.ORDER_ID is not null && oei.IS_REPLACEMENT_SERVED = 0 then 'REPLACEMENT_NOT_SERVED'
        end ENQUIRY_STATUS,
    oei.AVAILABLE_QUANTITY,
    case when oei.AVAILABLE_QUANTITY > 0 then oei.ORDERED_QUANTITY + oei.AVAILABLE_QUANTITY else oei.ORDERED_QUANTITY end REQUESTED_QUANTITY,
    upp.PRICE,
    ((case when oei.AVAILABLE_QUANTITY > 0 then oei.ORDERED_QUANTITY + oei.AVAILABLE_QUANTITY else oei.ORDERED_QUANTITY end) * upp.PRICE) REVENUE_EFFECTED
FROM
    KETTLE.ORDER_ENQUIRY_ITEM oei
        LEFT OUTER JOIN
    KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oei.ORDER_ID
        LEFT OUTER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON oei.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON oei.PRODUCT_ID = pd.PRODUCT_ID
        LEFT OUTER JOIN
    KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm ON upm.UNIT_ID = oei.UNIT_ID
        AND upm.PRODUCT_ID = oei.PRODUCT_ID
        LEFT OUTER JOIN
    KETTLE_MASTER.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
    where ENQUIRY_TIME > CURRENT_DATE;
                                
				]]></content>
                                </report>

                        </reports>
                </category>	
</categories>
</ReportCategories>

