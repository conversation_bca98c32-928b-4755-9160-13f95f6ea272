<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="Mark Food Ready:" type="Automated"
                  accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>">
            <reports>


                <report id="10" name="CHAAYOS" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                    <content>
                        <![CDATA[
SELECT
    'Chaayos' Chaay<PERSON>,
    b.PARTNER_DISPLAY_NAME DELIVERY_PARTNER_NAME,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:05:00',1,0
         )) AS MFR_5_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:05:01' AND '00:09:00',1,0
         )) AS MFR_9_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '00:12:00',1,0
         )) AS MFR_12_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:12:01' AND '00:15:00',1,0
         )) AS MFR_15_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:15:01' AND '00:20:00',1,0
         )) AS MFR_20_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:20:01' AND '00:25:00',1,0
         )) AS MFR_25_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:25:01' AND '11:99:99',1,0
         )) AS MFR_MORE_THAN_25_MIN,
        CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:09:00',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Within_9_Min,
         CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '11:99:99',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Greater_than_9_Min
 FROM
    (SELECT
        a.ORDER_ID,
            a.UNIT_NAME,
            a.PARTNER_DISPLAY_NAME,
            a.BRAND_ID,
            MAX(a.CREATED) CREATED,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH
    FROM
        (SELECT
        ose.ORDER_ID,
            ud.UNIT_NAME,
            cp.PARTNER_DISPLAY_NAME,
            od.BRAND_ID,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH
    FROM
        KETTLE.ORDER_STATUS_EVENT ose
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
    INNER JOIN KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND ose.TO_STATUS = 'CREATED' IS NOT NULL
            AND ORDER_SOURCE = 'COD'
            AND ORDER_STATUS <> 'CANCELLED'
            AND BRAND_ID <>3) a
    GROUP BY ORDER_ID , UNIT_NAME) b GROUP BY 1,2;




                                       ]]>
                    </content>
                </report>

                <report id="10" name="REGIONAL LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                    <content>
                        <![CDATA[

SELECT
    b.UNIT_REGION REGION,
    b.PARTNER_DISPLAY_NAME DELIVERY_PARTNER_NAME,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:05:00',1,0
         )) AS MFR_5_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:05:01' AND '00:09:00',1,0
         )) AS MFR_9_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '00:12:00',1,0
         )) AS MFR_12_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:12:01' AND '00:15:00',1,0
         )) AS MFR_15_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:15:01' AND '00:20:00',1,0
         )) AS MFR_20_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:20:01' AND '00:25:00',1,0
         )) AS MFR_25_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:25:01' AND '11:99:99',1,0
         )) AS MFR_MORE_THAN_25_MIN,
        CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:09:00',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Within_9_Min,
         CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '11:99:99',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Greater_than_9_Min
 FROM
    (SELECT
        a.ORDER_ID,
            CASE WHEN a.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN a.UNIT_REGION IN ('MUMBAI') THEN 'WEST'
            WHEN a.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION,
            a.PARTNER_DISPLAY_NAME,
            a.BRAND_ID,
            MAX(a.CREATED) CREATED,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH
    FROM
        (SELECT
        ose.ORDER_ID,
            ud.UNIT_REGION,
            cp.PARTNER_DISPLAY_NAME,
            od.BRAND_ID,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH
    FROM
        KETTLE.ORDER_STATUS_EVENT ose
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
    INNER JOIN KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND ose.TO_STATUS = 'CREATED' IS NOT NULL
            AND ORDER_SOURCE = 'COD'
            AND ORDER_STATUS <> 'CANCELLED'
            AND BRAND_ID <>3) a
    GROUP BY ORDER_ID , UNIT_REGION) b GROUP BY 1,2;


                                                ]]>
                    </content>
                </report>

                <report id="10" name="AREA MANAGER(L2) LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                    <content>
                        <![CDATA[
SELECT
    UCASE(b.EMP_NAME) AREA_MANAGER,
    b.PARTNER_DISPLAY_NAME DELIVERY_PARTNER_NAME,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:05:00',1,0
         )) AS MFR_5_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:05:01' AND '00:09:00',1,0
         )) AS MFR_9_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '00:12:00',1,0
         )) AS MFR_12_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:12:01' AND '00:15:00',1,0
         )) AS MFR_15_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:15:01' AND '00:20:00',1,0
         )) AS MFR_20_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:20:01' AND '00:25:00',1,0
         )) AS MFR_25_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:25:01' AND '11:99:99',1,0
         )) AS MFR_MORE_THAN_25_MIN,
        CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:09:00',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Within_9_Min,
         CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '11:99:99',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Greater_than_9_Min
 FROM
    (SELECT
        a.ORDER_ID,
            a.UNIT_MANAGER,
            a.EMP_NAME,
            a.PARTNER_DISPLAY_NAME,
            a.BRAND_ID,
            MAX(a.CREATED) CREATED,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH
    FROM
        (SELECT
        ose.ORDER_ID,
            ud.UNIT_MANAGER,
            ed.EMP_NAME,
            cp.PARTNER_DISPLAY_NAME,
            od.BRAND_ID,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH
    FROM
        KETTLE.ORDER_STATUS_EVENT ose
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
    INNER JOIN KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
    #AND ud.CAFE_MANAGER = ud.UNIT_MANAGER
    WHERE
        BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND ose.TO_STATUS = 'CREATED' IS NOT NULL
            AND ORDER_SOURCE = 'COD'
            AND ORDER_STATUS <> 'CANCELLED'
            AND BRAND_ID <>3
            ) a
    GROUP BY ORDER_ID , UNIT_MANAGER) b GROUP BY 1,2;

                                             ]]>
                    </content>
                </report>





                <report id="10" name="Ghee And Turmeric" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                    <content>
                        <![CDATA[

SELECT
    b.UNIT_NAME CAFE,
    b.PARTNER_DISPLAY_NAME DELIVERY_PARTNER_NAME,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:05:00',1,0
         )) AS MFR_5_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:05:01' AND '00:09:00',1,0
         )) AS MFR_9_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '00:12:00',1,0
         )) AS MFR_12_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:12:01' AND '00:15:00',1,0
         )) AS MFR_15_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:15:01' AND '00:20:00',1,0
         )) AS MFR_20_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:20:01' AND '00:25:00',1,0
         )) AS MFR_25_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:25:01' AND '11:99:99',1,0
         )) AS MFR_MORE_THAN_25_MIN,
         CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:12:00',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Within_12_Min,
         CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:12:01' AND '11:99:99',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Greater_than_12_Min
 FROM
    (SELECT
        a.ORDER_ID,
            a.UNIT_NAME,
            a.PARTNER_DISPLAY_NAME,
            a.BRAND_ID,
            MAX(a.CREATED) CREATED,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH
    FROM
        (SELECT
        ose.ORDER_ID,
            ud.UNIT_NAME,
            cp.PARTNER_DISPLAY_NAME,
            od.BRAND_ID,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH
    FROM
        KETTLE.ORDER_STATUS_EVENT ose
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
    INNER JOIN KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND ose.TO_STATUS = 'CREATED' IS NOT NULL
            AND ORDER_SOURCE = 'COD'
            AND ORDER_STATUS <> 'CANCELLED'
            AND BRAND_ID =3) a
    GROUP BY ORDER_ID , UNIT_NAME) b GROUP BY 1,2;
                                                ]]>
                    </content>

                </report>




                <report id="10" name="CAFE LEVEL" executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
                    <content>
                        <![CDATA[

 SELECT
    b.UNIT_NAME CAFE,
    b.PARTNER_DISPLAY_NAME DELIVERY_PARTNER_NAME,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:05:00',1,0
         )) AS MFR_5_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:05:01' AND '00:09:00',1,0
         )) AS MFR_9_MIN,
SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '00:12:00',1,0
         )) AS MFR_12_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:12:01' AND '00:15:00',1,0
         )) AS MFR_15_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:15:01' AND '00:20:00',1,0
         )) AS MFR_20_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:20:01' AND '00:25:00',1,0
         )) AS MFR_25_MIN,
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:25:01' AND '11:99:99',1,0
         )) AS MFR_MORE_THAN_25_MIN,
        CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '00:09:00',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Within_9_Min,
         CONCAT(ROUND( SUM(IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:09:01' AND '11:99:99',1,0
         ))/
         SUM( IF
         (
         TIMEDIFF(b.READY_TO_DISPATCH, b.CREATED) BETWEEN '00:00:00' AND '11:99:99',1,0
         ))*100,0),"%") Greater_than_9_Min
 FROM
    (SELECT
        a.ORDER_ID,
            a.UNIT_NAME,
            a.PARTNER_DISPLAY_NAME,
            a.BRAND_ID,
            MAX(a.CREATED) CREATED,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH
    FROM
        (SELECT
        ose.ORDER_ID,
            ud.UNIT_NAME,
            cp.PARTNER_DISPLAY_NAME,
            od.BRAND_ID,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH
    FROM
        KETTLE.ORDER_STATUS_EVENT ose
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
    INNER JOIN KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND ose.TO_STATUS = 'CREATED' IS NOT NULL
            AND ORDER_SOURCE = 'COD'
            AND ORDER_STATUS <> 'CANCELLED'
            AND BRAND_ID <>3) a
    GROUP BY ORDER_ID , UNIT_NAME) b GROUP BY 1,2;


                                                ]]>
                    </content>

                </report>



            </reports>
        </category>
    </categories>
</ReportCategories>


