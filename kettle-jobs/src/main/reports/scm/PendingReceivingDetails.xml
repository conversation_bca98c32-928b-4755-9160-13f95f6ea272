<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Pending Receiving Details (Diary/bakery)" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Pending Receiving Details (Diary/bakery)" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    DATE(ro.FULFILLMENT_DATE) AS FULFILLMENT_DATE,
    ro.GENERATION_TIME,
    ro.GENERATED_BY,
    FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) AS REQUESTED_QUANTITY,
    FLOOR(SUM(roi.TRANSFERRED_QUANTITY)) AS TRANSFERRED_QUANTITY,
    FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
    vd.VENDOR_NAME,
    cd.SUB_CATEGORY_NAME
FROM
    REQUEST_ORDER ro
        INNER JOIN
    REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND DATE(ro.FULFILLMENT_DATE) = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND ro.IS_SPECIAL_ORDER = 'Y'
        INNER JOIN
    UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    VENDOR_DETAIL vd ON roi.VENDOR_ID = vd.VENDOR_ID
        INNER JOIN
    PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
        INNER JOIN
    SUB_CATEGORY_DEFINITION cd ON cd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
WHERE
    pd.Category_ID = '1'
        AND (pd.SUB_CATEGORY_ID = '1' OR pd.SUB_CATEGORY_ID = '3')
        AND RECEIVED_QUANTITY = '0'
	AND ro.REQUEST_ORDER_STATUS NOT IN ( 'CANCELLED', 'SETTLED' )
GROUP BY ud.UNIT_NAME
ORDER BY RECEIVED_QUANTITY;
]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


