<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Receiving Details - Bakery" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Receiving Details - Bakery" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        ud.UNIT_NAME,
            DATE(ro.FULFILLMENT_DATE) FULFILLMENT_DATE,
            ro.GENERATION_TIME,
            ro.GENERATED_BY,
            FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) REQUESTED_ABSOLUTE_QUANTITY,
            FLOOR(SUM(roi.TRANSFERRED_QUANTITY)) TRANSFERRED_QUANTITY,
            FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
            vd.VENDOR_NAME
    FROM
        REQUEST_ORDER ro
    INNER JOIN REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND ro.FULFILLMENT_DATE = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND ro.IS_SPECIAL_ORDER = 'Y'
    INNER JOIN UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
    INNER JOIN UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
    INNER JOIN VENDOR_DETAIL vd ON roi.VENDOR_ID = vd.VENDOR_ID
    INNER JOIN PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
    WHERE
        pd.Category_ID = '1'
        AND pd.SUB_CATEGORY_ID = '1'
	AND ro.REQUEST_ORDER_STATUS <> 'CANCELLED'
    GROUP BY ud.UNIT_NAME
    ORDER BY RECEIVED_QUANTITY) a
        ]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


