import sys
import os
import smtplib
import pymongo
os.environ.setdefault('PATH', '')
import pandas as pd
import datetime
import pytz
from mysql.connector import MySQLConnection, Error
from configparser import ConfigParser
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.utils import COMMASPACE, formatdate
from os.path import basename
from pymongo import ReadPreference
from dateutil.relativedelta import relativedelta

EPOCH = datetime.datetime.utcfromtimestamp(0)
BASEPATH = '/data/app/kettle/dev/python_scripts/stockouts'
TO_MAILS = sys.args[1]

def read_db_config(filename=BASEPATH + '/config.ini', section='mongo'):
    """ Read database configuration file and return a dictionary object
    :param filename: name of the configuration file
    :param section: section of database configuration
    :return: a dictionary of database parameters
    """
    return read_config(filename, section)


def read_email_config(filename=BASEPATH + '/config.ini', section='email'):
    """ Read database configuration file and return a dictionary object
    :param filename: name of the configuration file
    :param section: section of email configuration
    :return: a dictionary of email parameters
    """
    # get section, default to mysql
    return read_config(filename=filename, section=section)


def read_config(filename, section):
    # create parser and read ini configuration file
    parser = ConfigParser()
    parser.read(filename)
    config = {}
    if parser.has_section(section):
        items = parser.items(section)
        for item in items:
            config[item[0]] = item[1]
    else:
        raise Exception('{0} not found in the {1} file'.format(section, filename))

    return config


def unix_time_millis(dt):
    return (dt - EPOCH).total_seconds() * 1000.0


def connect(type="mongo", schema="inventory"):
    if type == "mongo":
        mongo_config = read_db_config(section="mongo")
        client = pymongo.MongoClient(mongo_config["uri"], read_preference=ReadPreference.PRIMARY)
        db = client[schema]
        return db
    elif type == "mysql":
        """ Connect to MySQL database """
        db_config = read_db_config(section="mysql")
        conn = None
        try:
            print('Connecting to MySQL database...')
            conn = MySQLConnection(**db_config)

            if conn.is_connected():
                print('connection established.')
                return conn
            else:
                print('connection failed.')

        except Error as error:
            print(error)


def prep_metadata(connection):
    try:
        cursor = connection.cursor()
        querystr = "SELECT rtl.RTL_CODE, GROUP_CONCAT(pd.PRODUCT_NAME) as PRODUCTS from KETTLE_MASTER_DEV.PRODUCT_DETAIL pd " \
                   "INNER JOIN KETTLE_MASTER_DEV.REF_LOOKUP_TYPE rtl on pd.PRODUCT_TYPE = rtl.RTL_ID " \
                   "WHERE pd.PRODUCT_STATUS='ACTIVE' GROUP BY 1"
        cursor.execute(querystr)
        result_set = list(cursor.fetchall())
        metadata = []
        if len(result_set) > 0:
            for row in result_set:
                products = list(row[1].split(","))
                for p in products:
                    metadata.append([p, row[0]])
        return pd.DataFrame(metadata, columns=["productName", "productCategory"])
    except Error as error:
        print error

    finally:
        if connection is not None:
            connection.close()
            print('Connection closed.')


def process_stockouts():
    mysql_conn = connect("mysql", "KETTLE_MASTER")
    db = connect(type="mongo", schema="inventory")
    mydate = datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
    startTime = datetime.datetime.combine(mydate, datetime.time(hour=8, minute=00))
    endTime = datetime.datetime.combine(mydate, datetime.time(hour=19, minute=00))
    stockOuts = db.unitProductsStockEventData
    #{"status":"STOCK_OUT","eventTimeStamp":{"$gte":{"$date":"2019-06-19T06:00:00.000Z"}}}
    cursor = stockOuts.find({"status": "STOCK_OUT", "eventTimeStamp": {"$gte": startTime, "$lte": endTime}},
                            {"_class": 0, "_id": 0})
    stockOutEvents = []
    for i in cursor:
        stockOutEvents.append(i)

    frame = pd.DataFrame(stockOutEvents)
    product_frame = prep_metadata(mysql_conn)
    merged = pd.merge(frame, product_frame, left_on=['productName'], right_on=['productName'], how="left")
    pivoted = pd.pivot_table(merged, values='eventTimeStamp', index=['unitName'],
                   columns=['productCategory'], aggfunc='count')
    flattened = pd.DataFrame(pivoted.to_records())
    flattened.fillna(0, inplace=True)
    flattened.rename(columns={'unitName': 'Cafe'}, inplace=True)
    pd.set_option('colheader_justify', 'center')
    for col in flattened.columns:
        if col is not "Cafe":
            flattened[col] = flattened[col].apply(lambda x: int(x))
    filename = BASEPATH + "/data/StockOuts.csv"
    merged.to_csv(filename, sep=",", encoding="utf-8", index=False)
    send_to = list(TO_MAILS.split(","))
    send_mail(send_to=send_to, text=flattened.to_html(index=False),
              subject="STOCK_OUTS for TODAY", filename=filename)
    os.remove(filename)


def createExpiryData(objArr, row):
    for expiry in row["inventory"]:
        objArr.append({
            "UNIT_ID": row["unitId"],
            "BUSINESS_DATE": row["businessDate"],
            "CREATION_TIME": row["creationTime"],
            "BUSINESS_DATE_IST": row["businessDate"] + relativedelta(minutes=30, hour=5),
            "CREATION_TIME_IST": row["creationTime"] + relativedelta(minutes=30, hour=5),
            "PRODUCT_ID": expiry["id"],
            "PRODUCT_NAME": expiry["name"],
            "QTY": expiry["qty"],
            "UOM": expiry["u"],
            "EXPIRY_QTY": expiry["exQty"],
            "PRICE": expiry["price"]
        })


def process_expiries():
    db = connect()
    expiries = db.inventorySnapShot
    cursor = expiries.find({"snapShotStatus": "ACTIVE", "creationTime": {}}, {"_class": 0, "_id": 0})
    expiryEvents = []
    for i in cursor:
        createExpiryData(expiryEvents, i)

    frame = pd.DataFrame(expiryEvents)
    frame.to_csv(BASEPATH + "/data/PredictedExpiries.csv", sep=",", index=False)


def send_mail(send_to, subject, text=None, filename=None, server="smtp.gmail.com"):
    email_config = read_email_config()
    msg = MIMEMultipart()
    msg['From'] = email_config['user']
    msg['To'] = COMMASPACE.join(send_to) if type(send_to) is list else send_to
    msg['Date'] = formatdate(localtime=True)
    msg['Subject'] = subject

    if text is not None:
        msg.attach(MIMEText(text, "html"))

    with open(filename, "rb") as fil:
        part = MIMEApplication(fil.read(), Name=basename(filename))
        # After the file is closed
        part['Content-Disposition'] = 'attachment; filename="%s"' % basename(filename)
        msg.attach(part)

    smtp = smtplib.SMTP(server, 587)
    smtp.ehlo()
    smtp.starttls()
    smtp.login(email_config['user'], email_config['password'])
    smtp.sendmail(email_config['user'], send_to, msg.as_string())
    smtp.quit()
    smtp.close()


if __name__ == '__main__':
    process_stockouts()

