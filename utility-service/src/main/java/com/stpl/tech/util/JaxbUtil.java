/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util;

import java.io.File;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JaxbUtil {

	private static final Logger LOG = LoggerFactory.getLogger(JaxbUtil.class);

	public static <T> T jaxbXMLToObject(Class<T> clazz, String fileName) {
		try {
			JAXBContext context = JAXBContext.newInstance(clazz);
			Unmarshaller un = context.createUnmarshaller();
			@SuppressWarnings("unchecked")
			T emp = (T) un.unmarshal(new File(fileName));
			return emp;
		} catch (JAXBException e) {
			LOG.error("Error while converting xml to object", e);
		}
		return null;
	}

	public static <T> T jaxbXMLToObject(Class<T> clazz, File file) {
		try {
			JAXBContext context = JAXBContext.newInstance(clazz);
			Unmarshaller un = context.createUnmarshaller();
			@SuppressWarnings("unchecked")
			T emp = (T) un.unmarshal(file);
			return emp;
		} catch (JAXBException e) {
			LOG.error("Error while converting xml to object", e);
		}
		return null;
	}

	public static <T> void jaxbObjectToXML(Class<T> clazz, T emp, String fileName) {

		try {
			JAXBContext context = JAXBContext.newInstance(clazz);
			Marshaller m = context.createMarshaller();
			// for pretty-print XML in JAXB
			m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

			// Write to System.out for debugging
			// m.marshal(emp, System.out);

			// Write to File
			m.marshal(emp, new File(fileName));
		} catch (JAXBException e) {
			LOG.error("Error while converting object to xml", e);
		}
	}

}
