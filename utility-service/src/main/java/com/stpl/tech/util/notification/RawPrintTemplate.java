/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util.notification;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;

import com.stpl.tech.util.TemplateRenderingException;

public abstract class RawPrintTemplate implements PrintTemplate {

	public String getContent() throws TemplateRenderingException {

		try {
			// File output
			File f = new File(getFilepath());
			if (!f.exists()) {
				f.getParentFile().mkdirs();
			}
			StringBuilder sc = processData();
			if (sc != null) {
				try (PrintWriter out = new PrintWriter(f)) {
					out.println(sc);
				}
			}
			return sc != null ? sc.toString() : null;

		} catch (IOException e) {
			throw new TemplateRenderingException("Error while processing template", e);
		}
	}

	public abstract StringBuilder processData();

	public abstract String getFilepath();

}
