package com.stpl.tech.util.notification.model;

import java.util.Date;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 27-11-2017.
 */
public class SNSNotificationType {

    private String Type;
    private String MessageId;
    private String TopicArn;
    private EmailBounceMessage Message;
    private Date Timestamp;
    private String SignatureVersion;
    private String Signature;
    private String SigningCertURL;
    private String UnsubscribeURL;


    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getMessageId() {
        return MessageId;
    }

    public void setMessageId(String messageId) {
        MessageId = messageId;
    }

    public String getTopicArn() {
        return TopicArn;
    }

    public void setTopicArn(String topicArn) {
        TopicArn = topicArn;
    }

    public EmailBounceMessage getMessage() {
        return Message;
    }

    public void setMessage(EmailBounceMessage message) {
        Message = message;
    }

    public Date getTimestamp() {
        return Timestamp;
    }

    public void setTimestamp(Date timestamp) {
        Timestamp = timestamp;
    }

    public String getSignatureVersion() {
        return SignatureVersion;
    }

    public void setSignatureVersion(String signatureVersion) {
        SignatureVersion = signatureVersion;
    }

    public String getSignature() {
        return Signature;
    }

    public void setSignature(String signature) {
        Signature = signature;
    }

    public String getSigningCertURL() {
        return SigningCertURL;
    }

    public void setSigningCertURL(String signingCertURL) {
        SigningCertURL = signingCertURL;
    }

    public String getUnsubscribeURL() {
        return UnsubscribeURL;
    }

    public void setUnsubscribeURL(String unsubscribeURL) {
        UnsubscribeURL = unsubscribeURL;
    }
}
