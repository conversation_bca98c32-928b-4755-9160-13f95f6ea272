/*
 * Created By <PERSON><PERSON><PERSON>
 */

/*
 * Created By Shanmukh
 */

package com.stpl.tech.util.domain.adapter;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class FlexibleDateDeserializer implements JsonDeserializer<Date> {

    private static final List<SimpleDateFormat> formats = Arrays.asList(
            // Existing (already in your list) — kept for completeness
            new SimpleDateFormat("MMM dd, yyyy HH:mm:ss", Locale.ENGLISH),
            new SimpleDateFormat("MMM dd, yyyy, HH:mm:ss", Locale.ENGLISH),
            new SimpleDateFormat("MMM dd, yyyy, hh:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMM dd, yyyy hh:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMM dd, yyyy h:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMM dd yyyy hh:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMM dd yyyy h:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMM d, yyyy h:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMM d yyyy h:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("MMMM dd, yyyy hh:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("dd MMM yyyy", Locale.ENGLISH),
            new SimpleDateFormat("dd MMM yyyy HH:mm:ss", Locale.ENGLISH),
            new SimpleDateFormat("dd MMM yyyy HH:mm:ss zzz", Locale.ENGLISH),
            new SimpleDateFormat("dd-MM-yyyy"),
            new SimpleDateFormat("dd-MM-yyyy HH:mm:ss"),
            new SimpleDateFormat("dd-MM-yyyy'T'HH:mm:ss"),
            new SimpleDateFormat("dd-MM-yyyy'T'HH:mm:ss.SSS"),
            new SimpleDateFormat("dd-MM-yyyy'T'HH:mm:ss.SSSZ"),
            new SimpleDateFormat("dd-MM-yyyy'T'HH:mm:ssZ"),
            new SimpleDateFormat("dd/MM/yyyy"),
            new SimpleDateFormat("dd/MM/yyyy HH:mm:ss"),
            new SimpleDateFormat("dd/MM/yy HH:mm:ss", Locale.ENGLISH),
            new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH),
            new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH),
            new SimpleDateFormat("EEE MMM dd HH:mm:ss yyyy", Locale.ENGLISH),
            new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz", Locale.ENGLISH),
            new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss Z", Locale.ENGLISH),
            new SimpleDateFormat("G", Locale.ENGLISH), // rare but if ever used
            new SimpleDateFormat("HH:mm:ss", Locale.ENGLISH),
            new SimpleDateFormat("MM-dd-yyyy", Locale.ENGLISH),
            new SimpleDateFormat("MM/dd/yyyy"),
            new SimpleDateFormat("MM/dd/yyyy hh:mm:ss a"),
            new SimpleDateFormat("MM/dd/yyyy HH:mm:ss"),
            new SimpleDateFormat("M/d/yyyy h:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("M/d/yyyy HH:mm:ss", Locale.ENGLISH),
            new SimpleDateFormat("hh:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("h:mm:ss a", Locale.ENGLISH),
            new SimpleDateFormat("hh:mm:ss.SSS a", Locale.ENGLISH),
            new SimpleDateFormat("yyyy.MM.dd G 'at' HH:mm:ss z", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd"),
            new SimpleDateFormat("yyyy-MM-dd HH:mm"),
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.ENGLISH),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.ENGLISH),
            new SimpleDateFormat("yyyy/MM/dd"),
            new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"),
            new SimpleDateFormat("yyyyMMdd"),
            new SimpleDateFormat("yyyyMMddHHmmss"),
            new SimpleDateFormat("yyyyMMdd'T'HHmmss"),
            new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'")

    );

    public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
            throws JsonParseException {
        String dateStr = json.getAsString();
        for (SimpleDateFormat format : formats) {
            try {
                return format.parse(dateStr);
            } catch (ParseException ignored) {
            }
        }
        throw new JsonParseException("Unparseable date: " + dateStr);
    }
}

