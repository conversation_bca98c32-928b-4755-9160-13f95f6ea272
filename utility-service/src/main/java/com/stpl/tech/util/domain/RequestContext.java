package com.stpl.tech.util.domain;


public class RequestContext {

    private static final ThreadLocal<RequestHolder> context = ThreadLocal.withInitial(RequestHolder::new);

    public static Integer getBrandId() {
        return context.get().getBrandId();
    }
    public static Integer getCompanyId() {
        return context.get().getCompanyId();
    }

    public static RequestHolder getContext() {
        return context.get();
    }
    public static void setContext(RequestHolder holders) {
        context.set(holders);
    }

    public static void clearContext() {
        context.remove();
    }

    public static Boolean isContextAvailable() {
        RequestHolder holders = context.get();
        return holders != null && (holders.getBrandId() != null || holders.getCompanyId() != null);
    }
}
