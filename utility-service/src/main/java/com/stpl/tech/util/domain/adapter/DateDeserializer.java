package com.stpl.tech.util.domain.adapter;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.util.Date;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;

public class DateDeserializer implements JsonDeserializer<Date> {

	@Override
	public Date deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext)
			throws JsonParseException {
		try {
			String s = jsonElement.getAsString();
			return com.stpl.tech.util.domain.adapter.DateAdapter.parseDate(s);
		} catch (ParseException e) {
		}
		throw new JsonParseException("Unparseable time: \"" + jsonElement.getAsString());
	}
}
