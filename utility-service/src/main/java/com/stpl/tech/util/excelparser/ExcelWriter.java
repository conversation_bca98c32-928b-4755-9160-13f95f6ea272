package com.stpl.tech.util.excelparser;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class ExcelWriter {

	private Workbook workbook;
	private File file;

	public ExcelWriter() {
		this.workbook = new XSSFWorkbook();
	}

	public ExcelWriter(Workbook workbook) {
		this.workbook = workbook;
	}

	@SuppressWarnings(value = { "rawtypes", "unchecked" })
	public void writeSheet(List<? extends Object> data, Class t) {
		/*
		 * if (data == null || data.isEmpty()) { System.out.println("Empty Data");
		 * return; }
		 */

		// specify the name of the sheet represented by the class
		ExcelSheet sheetAnnotation = (ExcelSheet) t.getAnnotation(ExcelSheet.class);
		Sheet sheet;
		if (sheetAnnotation == null)
			sheet = workbook.createSheet();
		else
			sheet = workbook.createSheet(sheetAnnotation.value());

		// Retrieve all the properties of class which are annotated with
		// ExcelField. Except the property 'extraData' all other properties of
		// the class will be collected here
		List<Field> excelColumns = getAnnonatedColumns(t);
		int rowCount = 0;
		int colCount = 0;

		// Write Headers
		Row headerRow = sheet.createRow(rowCount++);
		colCount = writeHeaders(colCount, excelColumns, "", headerRow, t);

		// Write Data
		if (data != null && !data.isEmpty()) {
			for (Object dataObject : data) {
				Row dataRow = sheet.createRow(rowCount++);
				colCount = 0;
				colCount = writeRowData(colCount, dataRow, excelColumns, dataObject);
			}
		}

		// Auto sizing columns to cleanup the appearance of the spreadsheet.
		for (int i = 0; i < colCount; i++) {
			sheet.autoSizeColumn(i);
		}

	}

	public void writeSheet(List<? extends Object> data, Class t, String sheetName) {
		/*
		 * if (data == null || data.isEmpty()) { System.out.println("Empty Data");
		 * return; }
		 */

		// specify the name of the sheet represented by the class
		ExcelSheet sheetAnnotation = (ExcelSheet) t.getAnnotation(ExcelSheet.class);
		Sheet sheet;
		if (sheetAnnotation == null)
			sheet = workbook.createSheet();
		else
			sheet = workbook.createSheet(sheetName);

		// Retrieve all the properties of class which are annotated with
		// ExcelField. Except the property 'extraData' all other properties of
		// the class will be collected here
		List<Field> excelColumns = getAnnonatedColumns(t);
		int rowCount = 0;
		int colCount = 0;

		// Write Headers
		Row headerRow = sheet.createRow(rowCount++);
		colCount = writeHeaders(colCount, excelColumns, "", headerRow, t);
		CellStyle dataStyle = getDataStyle();
		// Write Data
		if (data != null && !data.isEmpty()) {
			for (Object dataObject : data) {
				Row dataRow = sheet.createRow(rowCount++);
				colCount = 0;
				colCount = writeRowData(colCount, dataRow, excelColumns, dataObject, dataStyle);
			}
		}
		// removed auto-sizing to improve time while bulk writing
	}

	public CellStyle getHeaderStyle() {
		CellStyle headerStyle = workbook.createCellStyle();
		Font font = workbook.createFont();
		font.setFontHeightInPoints((short) 12);
		font.setBold(true);
		font.setColor(IndexedColors.WHITE.getIndex());

		headerStyle.setFont(font);
		headerStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headerStyle.setBorderBottom(BorderStyle.THIN);
		headerStyle.setBorderLeft(BorderStyle.THIN);
		headerStyle.setBorderRight(BorderStyle.THIN);
		headerStyle.setBorderTop(BorderStyle.THIN);

		return headerStyle;
	}

	CellStyle getDataStyle() {
		CellStyle dataStyle = workbook.createCellStyle();
		dataStyle.setBorderBottom(BorderStyle.THIN);
		dataStyle.setBorderLeft(BorderStyle.THIN);
		dataStyle.setBorderRight(BorderStyle.THIN);
		dataStyle.setBorderTop(BorderStyle.THIN);
		return dataStyle;
	}

	public int writeHeaders(int startCol, List<Field> excelColumns, String parent, Row headerRow, Class<?> clazz) {
		CellStyle headerStyle = getHeaderStyle();
		for (Field header : excelColumns) {

			ExcelField cellAnnotation = header.getAnnotation(ExcelField.class);
			String headerLabel = parent;
			if (cellAnnotation.headerName() != null && !AppConstants.BLANK.equals(cellAnnotation.headerName())) {
				headerLabel = parent + cellAnnotation.headerName();
			} else {
				headerLabel = header.getName();
			}
			// for flat structure only
			Cell cell = headerRow.createCell(startCol++);
			cell.setCellValue(capitalize(headerLabel));
			cell.setCellStyle(headerStyle);
		}
		return startCol;
	}

	public String capitalize(String headerLabel) {
		if(headerLabel != null) {
			return headerLabel.replaceAll("(?=.*[a-z])([A-Z])", "_$1").toUpperCase().trim();
		}
		return "NO HEADER";
	}

	int writeRowData(int colCount, Row dataRow, List<Field> excelColumns, Object data) {
		CellStyle dataStyle = getDataStyle();

		for (Field field : excelColumns) {
			try {
					Object o = field.get(data);
					String value = "";
					if (o != null) {
						value = String.valueOf(o);
					}
					Cell cell = dataRow.createCell(colCount++);
					cell.setCellValue(value);
					cell.setCellStyle(dataStyle);
			} catch (IllegalArgumentException e) {
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			}
		}
		return colCount;
	}

	int writeRowData(int colCount, Row dataRow, List<Field> excelColumns, Object data, CellStyle dataStyle) {
		for (Field field : excelColumns) {
			try {
				Object o = field.get(data);
				String value = "";
				if (o != null) {
					value = String.valueOf(o);
				}
				Cell cell = dataRow.createCell(colCount++);
				cell.setCellValue(value);
				cell.setCellStyle(dataStyle);
			} catch (IllegalArgumentException e) {
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			}
		}
		return colCount;
	}

	@SuppressWarnings("rawtypes")
	public
	List<Field> getAnnonatedColumns(Class t) {
		List<Field> excelColumns = new ArrayList<>();
		for (Field field : t.getDeclaredFields()) {
			ExcelField cellAnnotation = field.getAnnotation(ExcelField.class);
			if (cellAnnotation != null) {
				field.setAccessible(true);
				excelColumns.add(field);
			}
		}
		return excelColumns;
	}

	public void WriteToFile(String fileName) throws IOException {
		file = new File(fileName);
		if (!file.exists()) {
			file.createNewFile();
		}
		FileOutputStream fileOutputStream = null;
		try {
			fileOutputStream = new FileOutputStream(file);
			workbook.write(fileOutputStream);
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (fileOutputStream != null) {
				try {
					fileOutputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public Workbook getWorkbook() {
		return workbook;
	}

	public void setWorkbook(Workbook workbook) {
		this.workbook = workbook;
	}

	public File getFile() {
		return file;
	}

	public void setFile(File file) {
		this.file = file;
	}

}
