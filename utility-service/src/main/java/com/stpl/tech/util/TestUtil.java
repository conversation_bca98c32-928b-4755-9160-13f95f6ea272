package com.stpl.tech.util;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class TestUtil {

	private final String USER_AGENT = "Mozilla/5.0";

	public static void main(String[] args) throws Exception {

		TestUtil http = new TestUtil();

		System.out.println("\nTesting 2 - Send Http POST request");

		Date startDate = new SimpleDateFormat("yyyy-MM-dd").parse("2018-05-13'");
		Date endDate = new SimpleDateFormat("yyyy-MM-dd").parse("2018-05-14'");
		String unitId = "26048";

		LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

		List<LocalDate> list =  new ArrayList<>();
		list.add(LocalDate.of(2018, Month.AUGUST, 4));
		list.add(LocalDate.of(2018, Month.AUGUST, 5));
		list.add(LocalDate.of(2018, Month.AUGUST, 29));
		list.add(LocalDate.of(2018, Month.AUGUST, 30));
		
		for(LocalDate date : list) {
			System.out.println(date);
			http.sendPost(date.toString(), unitId);
			TimeUnit.MINUTES.sleep(1);	
		}
		
		/*for (LocalDate date = start; date.isBefore(end); date = date.plusDays(1)) {
			// Do your job here with `date`.
			System.out.println(date);
			http.sendPost(date.toString(), unitId);
			TimeUnit.MINUTES.sleep(1);
		}*/
		
		System.out.println("-------DONE--------");
	}

	// HTTP POST request
	private void sendPost(String date, String unitId) throws Exception {

		String url = "http://prod.kettle.chaayos.com:9797/kettle-service/rest/v1/pos-metadata/unit/generate/external?";

		url = url + "businessDate=" + date;
		url = url + "&unitId=" + unitId;

		HttpClient client = HttpClientBuilder.create().build();

		HttpPost post = new HttpPost(url);

		// add header
		post.setHeader("User-Agent", USER_AGENT);
		post.setHeader("Content-Type", "application/json");
		post.setHeader("auth",
				"eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiMDg4L2RqL3JYUUkwbFhaNHZuWWswbFlPaGlBVWY4aTVhSHQxWDZtV0d1U3l1ZHUzSzFNeXZDQjFEL3lMVjRQNDFxdVQ5cG9qcVhWZlxuRFMrbDhkZGN2dz09IiwidW5pdElkIjowLCJ0ZXJtaW5hbElkIjowLCJ1c2VySWQiOjEyMDQ1OCwiaWF0IjoxNTM1MDA4MTM2LCJpc3N1ZXIiOiJLRVRUTEVfQURNSU4ifQ.IzCDZsrCKgAOt_tJx9dkXPtCQJdKIBBQnCwNAqRyvLU");

		HttpResponse response = client.execute(post);
		System.out.println("\nSending 'POST' request to URL : " + url);
		System.out.println("Post parameters : " + post.getEntity());
		System.out.println("Response Code : " + response.getStatusLine().getStatusCode());

		BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

		StringBuffer result = new StringBuffer();
		String line = "";
		while ((line = rd.readLine()) != null) {
			result.append(line);
		}

		System.out.println(result.toString());

	}
}
