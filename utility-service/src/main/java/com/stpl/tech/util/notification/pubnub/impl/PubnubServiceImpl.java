package com.stpl.tech.util.notification.pubnub.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import com.pubnub.api.PNConfiguration;
import com.pubnub.api.PubNub;
import com.pubnub.api.callbacks.PNCallback;
import com.pubnub.api.models.consumer.PNPublishResult;
import com.pubnub.api.models.consumer.PNStatus;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 16-03-2018.
 */
public class PubnubServiceImpl implements PubnubService {

    private static final Logger LOG = LoggerFactory.getLogger(PubnubServiceImpl.class);
    private Map<String, BlockingQueue<PushNotification>> PUBLISH_QUEUE = new HashMap<>();
    private static final int MAX_RETRIES = 10;

    private PubNub CLIENT = null;
    private String envType;
    private String subscribeKey;
    private String publishKey;


    public PubnubServiceImpl(String envType, String subscribeKey, String publishKey) {
        this.envType = envType;
        this.subscribeKey = subscribeKey;
        this.publishKey = publishKey;
        this.init();
    }


    private void init() {
        LOG.info("Trying to init Pubnub client in post contruct method");
        PNConfiguration pnConfiguration = new PNConfiguration();
        pnConfiguration.setPublishKey(this.publishKey);
        pnConfiguration.setSubscribeKey(this.subscribeKey);
        pnConfiguration.setUuid(envType +"_PUBNUB_SERVICE_CLIENT");
        CLIENT = new PubNub(pnConfiguration);
    }

    private void startPublisher(BlockingQueue<PushNotification> queue) {
        new Thread(() -> {
            while(true) {
                try {
                    PushNotification pubNubMessage = queue.take();
                    publish(pubNubMessage);
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    LOG.error("Error while running publish message of Pubnub", e);
                }
            }
        }).start();
    }

    private <T> void addToPublishQueue(PushNotification<T> notification) {
        String key = notification.getChannelName();
        BlockingQueue<PushNotification> blockingQueue = PUBLISH_QUEUE.get(key);
        if (blockingQueue == null) {
            blockingQueue = new LinkedBlockingQueue<>();
            startPublisher(blockingQueue);
        }
        if(!blockingQueue.contains(notification)){
            blockingQueue.add(notification);
            PUBLISH_QUEUE.put(key, blockingQueue);
        }
    }


    private synchronized void publish(PushNotification<T> notification) {
        CLIENT
            .publish()
            .message(notification.getMessage())
            .channel(notification.getChannelName())
            .async(new PNCallback<PNPublishResult>() {
                @Override
                public void onResponse(PNPublishResult result, PNStatus status) {
                    // handle publish result, status always present, result if successful
                    // status.isError to see if error happened
                    try {
                        if (status.isError() && notification.getRetries() < MAX_RETRIES) {
                            LOG.info("Got Error while publishing, re-adding msg to queue :::: {} {}",
                                    notification.getChannelName(), notification.getMessage());
                            notification.setRetries(notification.getRetries() + 1);
                            addToPublishQueue(notification);
                        }else {
                            LOG.info(":::::::::: Successful in publishing orderId {} to the channel {} ::::::::::",
                                    notification.getMessage(),notification.getChannelName());
                        }
                    } catch (Exception e) {
                        LOG.error("Got error while sending push notification", e);
                    }
                }
            });
    }

    @Override
    public <T> void sendNotification(PushNotification<T> notification) {
        LOG.info("Adding notification to notification queue :: {} message :: {}",
                notification.getChannelName(), JSONSerializer.toJSON(notification.getMessage()));
        addToPublishQueue(notification);
    }

}
