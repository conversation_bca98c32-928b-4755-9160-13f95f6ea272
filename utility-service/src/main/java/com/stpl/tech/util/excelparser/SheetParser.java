package com.stpl.tech.util.excelparser;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.OptionalInt;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.MappedExcelObject;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import com.stpl.tech.util.excelparser.exception.ExcelInvalidCell;
import com.stpl.tech.util.excelparser.exception.ExcelInvalidCellValuesException;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import com.stpl.tech.util.excelparser.helper.HSSFHelper;

public class SheetParser {

	private List<ExcelInvalidCell> excelInvalidCells;

	public SheetParser() {
		excelInvalidCells = new ArrayList<>();
	}

	public <T> List<T> createEntity(Sheet sheet, Class<T> clazz, Consumer<ExcelParsingException> errorHandler) throws  ExcelParsingException {
		List<T> list = new ArrayList<>();
		ExcelObject excelObject = getExcelObject(clazz, errorHandler);
		if (excelObject.start() <= 0 || excelObject.end() < 0) {
			return list;
		}
		int end = getEnd(sheet, clazz, excelObject);

		for (int currentLocation = excelObject.start(); currentLocation <= end; currentLocation++) {
			T object = getNewInstance(sheet, clazz, excelObject.parseType(), currentLocation, true,
					errorHandler);
			List<Field> mappedExcelFields = getMappedExcelObjects(clazz);
			for (Field mappedField : mappedExcelFields) {
				Class<?> fieldType = mappedField.getType();
				Class<?> clazz1 = fieldType.equals(List.class) ? getFieldType(mappedField) : fieldType;
				List<?> fieldValue = createEntity(sheet, clazz1, errorHandler);
				if (fieldType.equals(List.class)) {
					setFieldValue(mappedField, object, fieldValue);
				} else if (!fieldValue.isEmpty()) {
					setFieldValue(mappedField, object, fieldValue.get(0));
				}
			}
			list.add(object);
		}
		return list;
	}

	public <T> List<T> createEntityWithIterator(Sheet sheet, Class<T> clazz,
			Consumer<ExcelParsingException> errorHandler) {
		List<T> list = new ArrayList<T>();

		ExcelObject excelObject = getExcelObject(clazz, errorHandler);
		if (excelObject.start() <= 0 || excelObject.end() < 0) {
			return list;
		}
		int end = getEnd(sheet, clazz, excelObject);
		for (int currentLocation = excelObject.start(); currentLocation <= end; currentLocation++) {
			T object = getNewInstance(sheet.iterator(), sheet.getSheetName(), clazz, excelObject.parseType(),
					currentLocation, excelObject.zeroIfNull(), errorHandler);
			List<Field> mappedExcelFields = getMappedExcelObjects(clazz);
			for (Field mappedField : mappedExcelFields) {
				Class<?> fieldType = mappedField.getType();
				Class<?> clazz1 = fieldType.equals(List.class) ? getFieldType(mappedField) : fieldType;
				List<?> fieldValue = createEntityWithIterator(sheet, clazz1, errorHandler);
				if (fieldType.equals(List.class)) {
					setFieldValue(mappedField, object, fieldValue);
				} else if (!fieldValue.isEmpty()) {
					setFieldValue(mappedField, object, fieldValue.get(0));
				}
			}
			list.add(object);
		}

		return list;
	}

	private <T> int getEnd(Sheet sheet, Class<T> clazz, ExcelObject excelObject) {
		int end = excelObject.end();
		if (end > 0) {
			return end;
		}
		return getRowOrColumnEnd(sheet, clazz);
	}

	/**
	 * @deprecated Pass an error handler lambda instead (see other signature)
	 */
	@Deprecated
	public <T> List<T> createEntity(Sheet sheet, String sheetName, Class<T> clazz) {
		return createEntity(sheet, clazz, error -> {
			throw error;
		});
	}

	public <T> int getRowOrColumnEnd(Sheet sheet, Class<T> clazz) {
		ExcelObject excelObject = getExcelObject(clazz, e -> {
			throw e;
		});
		ParseType parseType = excelObject.parseType();
		if (parseType == ParseType.ROW) {
			return sheet.getLastRowNum();
		}

		Set<Integer> positions = getExcelFieldPositionMap(clazz).keySet();
		OptionalInt maxPosition = positions.stream().mapToInt((x) -> x).max();
		OptionalInt minPosition = positions.stream().mapToInt((x) -> x).min();

		int maxCellNumber = 0;
		for (int i = minPosition.getAsInt(); i < maxPosition.getAsInt(); i++) {
			int cellsNumber = sheet.getRow(i).getLastCellNum();
			if (maxCellNumber < cellsNumber) {
				maxCellNumber = cellsNumber;
			}
		}
		return maxCellNumber;
	}

	private Class<?> getFieldType(Field field) {
		Type type = field.getGenericType();
		if (type instanceof ParameterizedType) {
			ParameterizedType pt = (ParameterizedType) type;
			return (Class<?>) pt.getActualTypeArguments()[0];
		}

		return null;
	}

	private <T> List<Field> getMappedExcelObjects(Class<T> clazz) {
		List<Field> fieldList = new ArrayList<>();
		Field[] fields = clazz.getDeclaredFields();
		for (Field field : fields) {
			MappedExcelObject mappedExcelObject = field.getAnnotation(MappedExcelObject.class);
			if (mappedExcelObject != null) {
				field.setAccessible(true);
				fieldList.add(field);
			}
		}
		return fieldList;
	}

	private <T> ExcelObject getExcelObject(Class<T> clazz, Consumer<ExcelParsingException> errorHandler) {
		ExcelObject excelObject = clazz.getAnnotation(ExcelObject.class);
		if (excelObject == null) {
			errorHandler.accept(new ExcelParsingException(
					"Invalid class configuration - ExcelObject annotation missing - " + clazz.getSimpleName()));
		}
		return excelObject;
	}

	private <T> T getNewInstance(Sheet sheet, Class<T> clazz, ParseType parseType, Integer currentLocation,
			boolean zeroIfNull, Consumer<ExcelParsingException> errorHandler) throws ExcelParsingException {
		T object = getInstance(clazz, errorHandler);
		Map<Integer, Field> excelPositionMap = getExcelFieldPositionMap(clazz);
		for (Integer position : excelPositionMap.keySet()) {
			Field field = excelPositionMap.get(position);
			Object cellValue;
			Object cellValueString;
			if (ParseType.ROW == parseType) {
				cellValue = HSSFHelper.getCellValue(sheet, field.getType(), currentLocation, position, zeroIfNull,
						errorHandler);
				cellValueString = HSSFHelper.getCellValue(sheet, String.class, currentLocation, position, zeroIfNull,
						errorHandler);
			} else {
				cellValue = HSSFHelper.getCellValue(sheet, field.getType(), position, currentLocation, zeroIfNull,
						errorHandler);
				cellValueString = HSSFHelper.getCellValue(sheet, String.class, position, currentLocation, zeroIfNull,
						errorHandler);
			}
			validateAnnotation(field, cellValueString, position, currentLocation);
			setFieldValue(field, object, cellValue);
		}

		return object;
	}

	private <T> T getNewInstance(Iterator<Row> rowIterator, String sheetName, Class<T> clazz, ParseType parseType,
			Integer currentLocation, boolean zeroIfNull, Consumer<ExcelParsingException> errorHandler) {
		T object = getInstance(clazz, errorHandler);
		Map<Integer, Field> excelPositionMap = getSortedExcelFieldPositionMap(clazz);
		Row row = null;
		for (Integer position : excelPositionMap.keySet()) {
			Field field = excelPositionMap.get(position);
			Object cellValue;
			Object cellValueString;

			if (ParseType.ROW == parseType) {
				if (null == row || row.getRowNum() + 1 != currentLocation) {
					row = HSSFHelper.getRow(rowIterator, currentLocation);
				}
				cellValue = HSSFHelper.getCellValue(row, sheetName, field.getType(), currentLocation, position,
						zeroIfNull, errorHandler);
				cellValueString = HSSFHelper.getCellValue(row, sheetName, String.class, currentLocation, position,
						zeroIfNull, errorHandler);
			} else {
				if (null == row || row.getRowNum() + 1 != position) {
					row = HSSFHelper.getRow(rowIterator, position);
				}
				cellValue = HSSFHelper.getCellValue(row, sheetName, field.getType(), position, currentLocation,
						zeroIfNull, errorHandler);
				cellValueString = HSSFHelper.getCellValue(row, sheetName, String.class, position, currentLocation,
						zeroIfNull, errorHandler);
			}
			validateAnnotation(field, cellValueString, position, currentLocation);
			setFieldValue(field, object, cellValue);
		}

		return object;
	}

	private void validateAnnotation(Field field, Object cellValueString, int position, int currentLocation) {
		ExcelField annotation = field.getAnnotation(ExcelField.class);
		if (annotation.validate()) {
			Pattern pattern = Pattern.compile(annotation.regex());
			cellValueString = cellValueString != null ? cellValueString.toString() : "";
			Matcher matcher = pattern.matcher((String) cellValueString);
			if (!matcher.matches()) {
				ExcelInvalidCell excelInvalidCell = new ExcelInvalidCell(position, currentLocation,
						(String) cellValueString);
				excelInvalidCells.add(excelInvalidCell);
				if (annotation.validationType() == ExcelField.ValidationType.HARD) {
					throw new ExcelInvalidCellValuesException("Invalid cell value at [" + currentLocation + ", "
							+ position
							+ "] in the sheet. This exception can be suppressed by setting 'validationType' in @ExcelField to 'ValidationType.SOFT");
				}
			}
		}
	}

	private <T> T getInstance(Class<T> clazz, Consumer<ExcelParsingException> errorHandler) {
		T object;
		try {
			Constructor<T> constructor = clazz.getDeclaredConstructor();
			constructor.setAccessible(true);
			object = constructor.newInstance();
		} catch (Exception e) {
			errorHandler.accept(new ExcelParsingException(
					"Exception occurred while instantiating the class " + clazz.getName(), e));
			return null;
		}
		return object;
	}

	private <T> void setFieldValue(Field field, T object, Object cellValue) {
		try {
			field.set(object, cellValue);
		} catch (IllegalArgumentException | IllegalAccessException e) {
			throw new ExcelParsingException("Exception occurred while setting field value ", e);
		}
	}

	private <T> Map<Integer, Field> getExcelFieldPositionMap(Class<T> clazz) {
		Map<Integer, Field> fieldMap = new HashMap<>();
		return fillMap(clazz, fieldMap);
	}

	private <T> Map<Integer, Field> getSortedExcelFieldPositionMap(Class<T> clazz) {
		Map<Integer, Field> fieldMap = new TreeMap<>();
		return fillMap(clazz, fieldMap);
	}

	private <T> Map<Integer, Field> fillMap(Class<T> clazz, Map<Integer, Field> fieldMap) {
		Field[] fields = clazz.getDeclaredFields();
		int i = 0;
		for (Field field : fields) {
			ExcelField excelField = field.getAnnotation(ExcelField.class);
			if (excelField != null) {
				field.setAccessible(true);
				fieldMap.put(excelField.position() > -1 ? excelField.position() : i, field);
				i++;
			}
		}
		return fieldMap;
	}

}
