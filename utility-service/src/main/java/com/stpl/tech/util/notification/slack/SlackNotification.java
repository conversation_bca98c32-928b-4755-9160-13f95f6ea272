package com.stpl.tech.util.notification.slack;

import com.stpl.tech.util.EnvType;

public enum SlackNotification {

	FEEDBACK_CAFE_LOW_RATING("feedback_cafe","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAhjHthns/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=uYFWshvDjxBvUiAxm-cHimfasqPzStrlbrCmB4uDZHA%3D"),
	FEEDBACK_DELIVERY_LOW_RATING("feedback_delivery","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA0znWeSI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=dtT-WLUEVXJCi_apT1Y9rTh4uypak-pL4oEe21Lj3IA%3D"),
	SYSTEM_LOGIN("system_login","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA_tcMnF8/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=eJTxPTnMKBu66fWGW511Ie9xGRhPWO8NNPXUTzRncXM%3D"),
	SYSTEM_ERRORS("kettle_errors","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAdxGOvHw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=coXZOEUDjF1YZz8i33GoW1XDKwZfeHk4PDKquZiZk9s%3D"),
	DEV_NOTIFICATION("dev_notification","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAj0Ilc3w/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=Riu01yViWZD-ZnQZbABZBgD7PFhYZj2WGFhI8ElyoOA%3D"),
	VENDOR_ORDERING("vendor_ordering","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAoteMOyI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=hh7c7FSyARpwDSwYDxxOMopTTcapC1_zSYCmW0lAzdE%3D"),
	ASSET_ORDERING("asset_ordering","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAmrQKqOQ/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=DnE7C8OBQXfrO6HjyACTCBJJHGEhf_bvJ7Zsq-6jUEA%3D"),
	MONK_SYNC_ERRORS("monk_errors","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA1-nYO_w/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=iKs1OllEpRx25zXrpxGnNcrF4ibV3JwnY3Y0CHWk_ZU%3D"),
	PRODUCT_NOTIFICATIONS("product_notify","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAm70n-EU/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=auo_d5smmo7lnz3l7J-iweTTjxPMWezlg3DdYxgFcZs%3D"),
	VARIANCE_REPORT_FAILURES("variance_failures","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAfRATsIQ/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=K87jbKicf6COGS7JO3MQbcx-oNgyYlRTChUO-CEtStw%3D"),
	STOCK_OUT_EVENT("stockout_event","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA7ibxGoE/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=IwTc4IHCSnW1YxG4yNKbRFKompdqzQ0Pfd8ZYDs0axE%3D"),
	REFUNDS("refunds","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAooxif1w/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=8cuNdXLknT3I49cKVZZ0D78HTKDr1aTJTINnI63TNc0%3D"),
	SUPPLY_CHAIN("sumo_notify","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAGgs1kkM/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=JeAde58JIn2WrTIBT1t6m0BQcWTZNJq42XjAeStSHMc%3D"),
	ACCOUNTS("accounts","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA-RPKJUM/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=RrBUZqIbBoWf1FELzLdBhFhFspQdxMrjEAnRinP4WRc%3D"),
	ORDER_FAILURE_INFO("webapp_orders","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA30Qv1is/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=7Ghv0pgLMQocy_7yUZg2OtkQMKm3CxRuQZUsQqQE4Gk%3D"),
	PAYMENT_REQUEST("payment_request","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA-qyBjZo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=PEjQJdBLshEmbwnuWHLx-XCcXkN1wfp4VtJN3JKJ_RY%3D"),
	DELAY_DELIVERY_CHANNEL("notify","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAJrth8lM/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=m_rrlXKlM-7dxOa-AMyEJB2WUOa_6ugKpOcZMJWjA08%3D"),
	ORDER_DELIVERY_LOG("order_log","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAc741U3M/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=gtJX7AQn9WEEjpOLY5s3-G03Lou_w-gpVHINh3iuDoI%3D"),
	PARTNER_INTEGRATION("partner_int","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA5MCqQyo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=e82ayjhPOIMikozsceXL-Y6qjkywQeIMC_FA2mjV0mA%3D"),
	INVENTORY_UPDATE("inventory_update","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAA3j9CIyo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cCxOQvSm50dKGC_Xg4gvkK2PXex3BX9Bgx_KOkVsrzY%3D"),
	CUSTOMER_CALL_BACK("customer_callback","https://chat.googleapis.com/v1/spaces/AAAAxIN4eUw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=GFhqiuckyX-GKWzSKppK42ghNrB1lhNnNH2sA1lK4oE%3D","https://chat.googleapis.com/v1/spaces/AAAAfqmaibQ/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=g7d5wy7-0HCwSD4ZbQFVateTysstybu_8_a39si85Vc%3D");

	private String channel;
	private String webHookDev;
	private String webHookProd;

	private SlackNotification(String channel,String webHookDev,String webHookProd) {
		this.channel = channel;
		this.webHookDev = webHookDev;
		this.webHookProd = webHookProd;
	}

	public String getChannel(EnvType env) {
		switch (env) {
		case DEV:
			return webHookDev;
		case STAGE:
			return webHookDev;
		case PROD:
			return  webHookProd;
		case SPROD:
			return webHookProd;
		default:
			break;
		}
		return null;
	}
	
	
	public String getChannel(EnvType env, String managerId) {
		switch (env) {
		case DEV:
			return DEV_NOTIFICATION.channel;
		case PROD:
		case SPROD:
			return managerId + "_" + channel;
		default:
			break;
		}
		return null;
	}

}
