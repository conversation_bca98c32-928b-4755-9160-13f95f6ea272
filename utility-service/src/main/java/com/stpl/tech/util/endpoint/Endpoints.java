package com.stpl.tech.util.endpoint;

public class Endpoints {

	/**
	 * KETTLE Service URLs
	 */

	public static final String KETTLE_SERVICE_ENTRY_POINT = "/kettle-service/rest/v1/";
	public static final String GET_LAST_DAY_CLOSE = KETTLE_SERVICE_ENTRY_POINT + "order-management/last-day-close";
	public static final String SYNC_MISSING_ORDER_INVENTORY = KETTLE_SERVICE_ENTRY_POINT
			+ "order-management/sync-order-inventory";
	public static final String GET_MTD_PNL_DETAILS = KETTLE_SERVICE_ENTRY_POINT+ "expense-management/get-mtd-pnl-details";

	/**
	 * MASTER Service URLs
	 */

	public static final String MASTER_SERVICE_ENTRY_POINT = "/master-service/rest/v1/";
	public static final String GET_ALL_UNITS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/unit/all";
	public static final String GET_RECIPE = MASTER_SERVICE_ENTRY_POINT + "redis-cache/recipe";
	public static final String GET_UNIT_PRODUCTS = MASTER_SERVICE_ENTRY_POINT + "unit-metadata/unit-product-data";
	public static final String GET_ALL_UNIT_BASIC_DETAILS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/ubd/all";
	public static final String GET_UNIT_BASIC_DETAIL = MASTER_SERVICE_ENTRY_POINT + "redis-cache/ubd";

	/**
	 * SCM Service URLs
	 */

	public static final String SCM_SERVICE_ENTRY_POINT = "/scm-service/rest/v1/";
	public static final String GET_COST_DETAIL_DATA = SCM_SERVICE_ENTRY_POINT + "scm-data/inventory";
	public static final String SCM_PNL_URL = SCM_SERVICE_ENTRY_POINT + "stock-management/calculate-pnl";
	public static final String SCM_PNL_URL_FINALIZED = SCM_SERVICE_ENTRY_POINT
			+ "stock-management/calculate-pnl/finalized";
	public static final String SCM_PNL_URL_MONTHLY_DAY_CLOSE = SCM_SERVICE_ENTRY_POINT
			+ "stock-management/calculate-pnl/monthly";

	/**
	 * Inventory Service URls
	 */
	
	public static final String INVENTORY_SERVICE_ENTRY_POINT = "/kettle-inventory/";
	public static final String INVENTORY_SERVICE_VERSION = "/rest/v1/";
	public static final String GET_CAFE_INVENTORY = "inventory-data/get-cafe-products";
	public static final String GET_CAFE_INVENTORY_INFO = "inventory-data/cafe-inventory";
	public static final String GET_CAFE_INVENTORY_INFO_PRODUCTS = "inventory-data/cafe-inventory/products";
	
	
	
	/**
	 * CRM service URLs
	 */
	
	public static final String CRM_SERVICE_ENTRY_POINT = "/kettle-crm/rest/v1/";

	public static final String GET_CUSTOMER_BY_FACE_ID = CRM_SERVICE_ENTRY_POINT + "crm/signin-by-faceId";
	public static final String GET_CUSTOMER_BY_CONTACT = CRM_SERVICE_ENTRY_POINT + "crm/customer-by-contact";
	public static final String SKIPPED_FACE_IT_NOTIFICATION = CRM_SERVICE_ENTRY_POINT + "crm/face-it/skipped-face-it-notify";
	public static final String MAP_CUSTOMER_BY_FACE_ID = CRM_SERVICE_ENTRY_POINT + "crm/map-by-faceId";
	public static final String OPT_OUT_CUSTOMER_OF_FACE_IT_BY_ID = CRM_SERVICE_ENTRY_POINT + "crm/face-it/opt-out-customer/customer-id";
	public static final String OPT_OUT_CUSTOMER_OF_FACE_IT_BY_CONTACT = CRM_SERVICE_ENTRY_POINT + "crm/face-it/opt-out-customer/contact-number";


	/**
	 * CRM Service V2 URls
	 */
	public static final String CRM_SERVICE_ENTRY_POINT_V2 = "/crm-service/rest/v2/";

	public static final String GET_CUSTOMER_BY_FACE_ID_V2 = CRM_SERVICE_ENTRY_POINT_V2 + "crm/signin-by-faceId";
	public static final String GET_CUSTOMER_BY_CONTACT_V2 = CRM_SERVICE_ENTRY_POINT_V2 + "crm/customer-by-contact";
	public static final String SKIPPED_FACE_IT_NOTIFICATION_V2 = CRM_SERVICE_ENTRY_POINT_V2 + "crm/face-it/skipped-face-it-notify";
	public static final String MAP_CUSTOMER_BY_FACE_ID_V2 = CRM_SERVICE_ENTRY_POINT_V2 + "crm/map-by-faceId";

	/**
	 * Channel Partner service URLs
	 */
	
	public static final String CHANNELPARTNER_SERVICE_ENTRY_POINT = "/channel-partner/rest/v1/";
	public static final String GET_RIDER_TEMP_STATUS = "zomato/order/rider/rider_details";
	public static final String GET_RIDER_MASK_STATUS = "zomato/order/rider/rider_mask";

    public static final String PARTNER_UNIT_TOGGLE_OFF = "rest/knock/bulk/unit-off-reasons-request";
    public static final String IN_VALIDATE_USER_SESSION = "/rest/v1/users/invalidate/unit/session";
    public static final String GOAL_DATA_CLEAR="rest/kettle-analytics/clear-unit-goal-cache/save-unit-day-wise-goals";

	public static final String SEND_EMAIL = "rest/v1/email/send-email";
}
