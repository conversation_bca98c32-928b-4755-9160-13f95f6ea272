package com.stpl.tech.util.transaction;

public class CashTransactionMetadata {

	private TransactionType transactionType;
	private CashMetadataType cashMetadataType;
	private CashTransactionCategory cashTransactionCategory;
	private CashTransactionCode cashTransactionCode;

	public CashTransactionMetadata() {
		// DEFAULT
	}

	public CashTransactionMetadata(TransactionType transactionType, CashMetadataType cashMetadataType,
			CashTransactionCategory cashTransactionCategory, CashTransactionCode cashTransactionCode) {
		super();
		this.transactionType = transactionType;
		this.cashMetadataType = cashMetadataType;
		this.cashTransactionCode = cashTransactionCode;
		this.cashTransactionCategory = cashTransactionCategory;
	}

	public TransactionType getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(TransactionType transactionType) {
		this.transactionType = transactionType;
	}

	public CashMetadataType getCashMetadataType() {
		return cashMetadataType;
	}

	public void setCashMetadataType(CashMetadataType cashMetadataType) {
		this.cashMetadataType = cashMetadataType;
	}

	public CashTransactionCode getCashTransactionCode() {
		return cashTransactionCode;
	}

	public void setCashTransactionCode(CashTransactionCode cashTransactionCode) {
		this.cashTransactionCode = cashTransactionCode;
	}

	public CashTransactionCategory getCashTransactionCategory() {
		return cashTransactionCategory;
	}

	public void setCashTransactionCategory(CashTransactionCategory cashTransactionCategory) {
		this.cashTransactionCategory = cashTransactionCategory;
	}

}
