/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util.notification;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;

public class MyAWSCredentials implements AWSCredentialsProvider {

    private String getAWSAccessKeyId() {
        return "********************";
    }

    private String getAWSSecretKey() {
        return "oqPiJswD/nuFTlL++KYivNq68D7n+KOYgKxNRpq0";
    }

    @Override
    public AWSCredentials getCredentials() {
        AWSCredentials awsCredentials = new BasicAWSCredentials(getAWSAccessKeyId(), getAWSSecretKey());
        return awsCredentials;
    }

    @Override
    public void refresh() {

    }
}
