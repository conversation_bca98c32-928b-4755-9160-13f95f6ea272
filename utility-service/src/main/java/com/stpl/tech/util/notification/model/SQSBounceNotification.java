package com.stpl.tech.util.notification.model;

import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.Queue;
import javax.jms.Session;

import com.amazon.sqs.javamessaging.ProviderConfiguration;
import com.amazon.sqs.javamessaging.SQSConnection;
import com.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazon.sqs.javamessaging.SQSMessageConsumer;
import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.stpl.tech.util.notification.MyAWSCredentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-11-2017.
 */
public class SQSBounceNotification {

    private static final Logger LOG = LoggerFactory.getLogger(SQSBounceNotification.class);

    private static SQSBounceNotification INSTANCE;
    static {
        try {
            INSTANCE = new SQSBounceNotification();
        } catch (JMSException e) {
            LOG.error("Failed to Initialize SQS ", e);
        }
    }

    private final SQSConnection sqsConnection;

    private SQSBounceNotification() throws JMSException {

        ClientConfiguration configuration = new ClientConfiguration();
        configuration.setMaxErrorRetry(3);
        configuration.setConnectionTimeout(0);
        configuration.setSocketTimeout(0);
        configuration.setProtocol(Protocol.HTTPS);
        /*sqsConnection = SQSConnectionFactory.builder().withClientConfiguration(configuration)
                .withAWSCredentialsProvider(new AWSCredentialsProvider() {

                    private AWSCredentials credentials = new MyAWSCredentials();

                    @Override
                    public void refresh() {}

                    @Override
                    public AWSCredentials getCredentials() {
                        return credentials;
                    }
                }).withRegion(Region.getRegion(Regions.EU_WEST_1))
                .withNumberOfMessagesToPrefetch(10)
                .build()
                .createConnection();*/
        SQSConnectionFactory sqsConnectionFactory = new SQSConnectionFactory(new ProviderConfiguration().withNumberOfMessagesToPrefetch(10),
                AmazonSQSClientBuilder.standard().withClientConfiguration(configuration).withCredentials(new MyAWSCredentials()).withRegion(Regions.EU_WEST_1));
        sqsConnection = sqsConnectionFactory.createConnection();
    }

    public static SQSBounceNotification getInstance() {
        return INSTANCE;
    }

    public Queue getQueue(SQSSession session) throws JMSException {
        String queueName = "ses-bounce-queue";
        return session.createQueue(queueName);
    }

    public SQSSession getSession() throws JMSException {
        return (SQSSession) sqsConnection.createSession(false, Session.AUTO_ACKNOWLEDGE);
    }

    public MessageConsumer getConsumer(SQSSession session) throws JMSException {
        return session.createConsumer(getQueue(session));
    }

    public SQSConnection getSqsConnection() {
        return sqsConnection;
    }

}
