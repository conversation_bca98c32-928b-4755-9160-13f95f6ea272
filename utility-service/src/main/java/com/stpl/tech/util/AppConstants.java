/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util;

import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;

public class AppConstants {
	/**
	 * Authentication
	 */
	public static final String DATE_FORMAT = "yyyy-MM-dd";

	public  static  final  String DATE_MONTH_FORMAT = "dd-MMM-yyyy" ;
	public static final String CHAAYOS_BRAND = "Chaayos";
	public static final String GNT_BRAND = "G&T";
	public static final String DOHFUL_BRAND ="DOHFUL";
	public static final String ALGORITHM = "AES";
	public static final String PASSPHRASE_KEY = "C4@@y05a^3)H-5uN";
	public static final String SUCCESS="SUCCESS";

	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern(DATE_FORMAT);
	/**
	 * Date related
	 */
	public  static final DateTimeFormatter DATE_MONTH_FORMATTER =DateTimeFormat.forPattern(DATE_MONTH_FORMAT);
	public static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormat.forPattern("yyyy-MM");
	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");

	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_TIME_WITH_NO_MILISECOND_FORMATTER = DateTimeFormat
			.forPattern("yyyy-MM-dd HH:mm:ss");

	public static final DateTimeFormatter DATE_TIME_WITH_ONLY_TIME_FORMATTER = DateTimeFormat.forPattern("HH:mm");
	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_TIME_FORMATTER_WITH_NO_CHARACTERS = DateTimeFormat
			.forPattern("yyyyMMddHHmmss");

	/**
	 * Delivery related
	 */
	public static final DateTimeFormatter DATE_TIME_ISO_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mmZ");
	public static final DateTimeFormatter DATE_TIME_ISO_FORMATTER_IN_SEC = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZ");

	public static final String BILL_PRINT_DATE_FORMAT = "dd/MM/yyyy HH:mm";
	public static final String BILL_PRINT_TIME_FORMAT = "HH:mm:ss";
	/**
	 * Ref lookup related
	 */
	public static final String RTL_GROUP_CATEGORY = "CATEGORY";
	public static final String RTL_GROUP_DIMENSION = "DIMENSION";
	public static final String RTL_GROUP_ITEM_PER_TICKET = "ITEM_PER_TICKET";
	public static final String RTL_GROUP_ADDONS = "ADDONS";
	public static final String RTL_GROUP_DISCOUNT = "DISCOUNT";
	public static final String RTL_GROUP_COMPLIMENTARY = "COMPLIMENTARY";
	public static final String RTL_GROUP_WEB_CATEGORY = "WEB_CATEGORY";
	public static final String RTL_GROUP_PR_TYPE = "PR_TYPE";
	public static final String RTL_GROUP_STATION_CATEGORIES = "WORK_STATIONS_STATION_CATEGORY";
	public static final String RTL_GROUP_ADJUSTMENT_COMMENT = "ADJUSTMENT_COMMENT ";

	public static final String RTL_CAFE_DELAY_REASONS = "CAFE_DELAY_REASONS";
	public static final String RTL_RIDER_DELAY_REASONS = "RIDER_DELAY_REASONS";

	public static final String RTL_CODE_DISCOUNT_CODE = "DiscountCode";
	public static final String RTL_CODE_CORE_PRODUCTS = "CORE_PRODUCTS";
	public static final String RTL_CODE_CATEGORY_PRODUCTS = "CATEGORY_PRODUCTS";
	public static final String RTL_CODE_COMPLIMENTARY_CODE = "ComplimentaryCode";

	public static final String RTL_CODE_DEFAULT_DISCOUNT_CODE = "Other";
	public static final String RTL_CODE_DEFAULT_COMPLIMENTARY_CODE = "Any Other";
	public static final String RTL_GROUP_CATEGORY_COMBO_NAME = "Combos";

	public static final String RTL_GROUP_FEEDBACK_DETAIL = "FEEDBACK_DETAIL";

	/**
	 * Constants
	 */
	public static final String YES = "Y";
	public static final String NO = "N";
	public static final String FAIL = "F";
	public static final String ALL = "All";
	public static final int GET_FOR_ALL = -1;

	/**
	 * Defaults
	 */

	public static final String DEFAULT_HAS_PARCEL = NO;
	public static final String DEFAULT_IS_COMPLIMENTARY = NO;
	public static final String DEFAULT_IS_NUMBER_VERIFIED = NO;
	public static final String DEFAULT_IS_EMAIL_VERIFIED = NO;
	public static final String DEFAULT_COUNTRY_CODE = "+91";
	public static final String DEFAULT_ADDRESS_TYPE = "RESEDENTIAL";
	public static final String DEFAULT_HAS_MULTIPLE_SECTION = NO;
	public static final String DEFAULT_AUTO_TRIGGERED = YES;
	public static final String DEFAULT_PASSCODE = "123123";
	public static final String OTHERS = "others";
	public static final int APP_BUILD_SEED_VERSION = 50;
    public static final String ASSEMBLY_ORDER_CHANNEL = "_assemblyorderchannel_";
	public static final String ANDROID_NOTIFICATIONS_CHANNEL = "_androidNotificationsChannel_";
	public static final String CANCELLED = "CANCELLED";
    public static final String SWIGGY = "SWIGGY";
    public static final String ZOMATO = "ZOMATO";
	public static final String BOTH_SWIGGY_ZOMATO = "BOTH";
    public static final String WA_OPT_IN = "OPT_IN";

	public static final String INTRA_STATE = "INTRA_STATE";
	public static final String INTER_STATE = "INTER_STATE";
	public static final String IGST = "IGST";

    public static final String updateStatus ="UPDATED";
	public static final String saveStatus="SAVED";
	public static final String markedInactive ="MARKED_INACTIVE";
	public static final String FAILED = "FAILED";
    public static final String DINE_IN_MILK_SELECTION_VARIANT_NAME ="MILK SELECTION" ;
    public static final String DEFAULT_UNIT_ZONE = "north";
    public static final String TEST_CONTACT_NUMBER = "9818019762";
	public static final String CHARITY_ORDER_NOTIFICATION_NUMBER = "9717779785";
    public static final String ADVANCE_PAYMENT = "ADVANCE_PAYMENT";
    public static final String ACTIVATED = "ACTIVATED";
    public static String addons= "Addons";


    public static final String getValue(String value, String defaultIfNull) {
		return value == null || "".equals(value.trim()) ? defaultIfNull : value;
	}

	public static final String getValue(Boolean value) {

		return value != null && value ? YES : NO;
	}

	public static final boolean getValue(String value) {

		return YES.equals(value);
	}

	public static final String getValue(Boolean value, String defaultIfNull) {
		return Objects.isNull(value) || Boolean.TRUE.equals(value) ? defaultIfNull : NO;
	}

	/**
	 * App Constants
	 */

	public static final int DINE_IN_CHANNEL_PARTNER = 1;
	public static final int DINE_IN_CHANNEL_PARTNER_CODE = 21;

	public static final int WEB_APP_CHANNEL_PARTNER_CODE = 14;
	public static final int NO_DIMENSION_CODE = 1;
	public static final String NO_DIMENSION_STRING = "None";
	public static final String NO_WASTAGE = "NO_WASTAGE";
	public static final int CAFE_VISIT_LOYALTY_POINT = 10;
	public static final int VERIFICATION_LOYALTY_POINT = 10;
	public static final int EMOTION_CONFIDENCE_THRESHOLD = 80;
	public static final int CUTOFF_LOYALTY_POINTS = 300;
	public static final String PARCEL_CODE = "Parcel";
	public static final MathContext MATH_CONTEXT = new MathContext(10);
	public static final String CHAAYOS_RECEIPT = "Chaayos \uD83D\uDC9A You";
	public static final String CHAAYOS_SUBSCRIPTION = "Chaayos Subscription";
	public static final String CHAI_PREPAID = "CHAI_PREPAID";
	public static final String CHAAYOS_DELIVERY = "Delivery Support";
	public static final String DELIVERY = "DELIVERY";
	public static final String TOTAL_SALE = "TOTAL_SALE";
	public static final String DINE_IN = "DINE_IN";
	public static final String PAYMENT_SRC_NEO_SERVICE = "NEO_SERVICE";
	public static final String CHAAYOS_COD = "Chaayos-COD";
	public static final String MARKETING_PARTNER = "Marketing-Partner";
	public static final int CHAAYOS_COMBO_PRODUCT_TYPE = 8;
	public static final Set<Integer> ADMIN_DEPARTMENTS = new HashSet<Integer>(Arrays.asList(102, 105));
	public static final int ADMIN_USER_ID = 100000;
	public static final int EMPLOYEE_MEAL_ID = 2100;
	public static final int SAMPLING_AND_MARKETING = 2106;
	public static final String DOHFUL_RECEIPT = "Dohful \uD83D\uDC99 You";

	public static boolean isDineIn(int channelPartnerId) {
		return channelPartnerId == DINE_IN_CHANNEL_PARTNER || channelPartnerId == DINE_IN_CHANNEL_PARTNER_CODE || channelPartnerId == WEB_APP_CHANNEL_PARTNER_CODE;
	}

	public static final String OFFER_SCOPE_CORPORATE = "CORPORATE";
	public static final String CAFE_BILL_PRINT_TEMPLATE = "template/OrderPrint.html";
	public static final String COD_BILL_PRINT_TEMPLATE = "template/OrderCODPrint.html";

	// Web Socket based configurations
	public static final String WEB_SOCKET_CHANNEL = "/ws-channel/";
	public static final String WEB_SOCKET_CHANNEL_ORDERS = "/orders";
	public static final String ORDER_QUEUE_CHANNEL = "/order-queue";
	public static final String UPDATE_ORDERS = "/updateOrder";

	public static final String EXCEL_MIME_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
	public static final String TEXT_MIME_TYPE = "text/html";
	public static final String CSV_MIME_TYPE = "text/csv";
	public static final String ZIP_MIME_TYPE = "application/zip";
	public static final String PDF_MIME_TYPE = "application/pdf";
	public static final String JSON_MIME_TYPE = "application/json";
	public static final String EXCEL_MIME_TYPE_XLS = "application/xls";
	public static final String JPEG_MIME_TYPE = "image/jpeg";
	public static final String JPG_MIME_TYPE = "image/jpg";
	public static final String PNG_MIME_TYPE = "image/png";

	/**
	 * Unit Status
	 */
	public static final String ACTIVE = "ACTIVE";
	public static final String IN_ACTIVE = "IN_ACTIVE";
	public static final String PENDING_REQUEST = "PENDING";
	public static final String COMPLETED_REQUEST = "COMPLETED";
	public static final String APPROVED_REQUEST = "APPROVED";
	public static final String INTERNAL_SOURCE = "INTERNAL SOURCE";
	public static final String ARCHIVED = "ARCHIVED";
	public static final String APPROVED = "APPROVED";






	public static final String PAYMENT_MODE_CREDIT = "Credit";

	/**
	 * Complimentary Eligibility
	 */
	public static final String ACCOUNTABLE = "ACCOUNTABLE";
	public static final String NOT_ACCOUNTABLE = "NOT_ACCOUNTABLE";

	// public static final int DELIVERY_REQUEST_API_TIMEOUT = 10;

	public static final int DELIVERY_PARTNER_AUTOMATED = -1;

	public static final int DELIVERY_PARTNER_PICKUP = 5;

	public static final int DELIVERY_PARTNER_NONE = 1;

	public static final int DELIVERY_PARTNER_CHAAYOS_DELIVERY = 8;

	public static final int TAKEAWAY_SEED = 100;

	/** Email of the Service Account */
	public static final String SERVICE_ACCOUNT_ACCOUNT_EMAIL = "<EMAIL>";

	/*
	 * public static final String MIMETYPE_GOOGLE_SHEETS =
	 * "application/vnd.google-apps.spreadsheet"; public static final String
	 * PREFIX_EXPENSE_REPORT = "expense"; public static final String
	 * PREFIX_EXPENSE_REPORT_OUTPUT = "expense_output"; public static final String
	 * APP_NAME_EXPENSE_REPORT = "scoreboard";
	 */

	public static final int COMPLEMENTARY_CODE_COMBO = 1;
	public static final int COMPLEMENTARY_CODE_LOYALTY = 2101;

	public static final String DIMENSION_NONE = "None";

	public static final int DEPARTMEMT_CORPORATE_ID = 102;
	public static final int DESIGNATION_ADMIN_ID = 1009;

	public static final int PAYMENT_MODE_CASH = 1;
	public static final int PAYMENT_MODE_GIFT_CARD = 10;
	public static final int PAYMENT_MODE_DINE_IN_CREDIT = 21;
	public static final int PAYMENT_MODE_AMEX = 3;
//	public static final int PAYMENT_MODE_PAYTM_EDC = 35;

	public static final int PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD = 35;
	public static final int PAYMENT_MODE_PAYTM_EDC_AMEX_CARD = 36;
	public static final int PAYMENT_MODE_PAYTM_EDC_UPI = 37;
	public static final int PAYMENT_MODE_PAYPHI_EDC = 38;
//	public static final int PAYMENT_MODE_PAYPHI_EDC_AMEX_CARD = 39;
//	public static final int PAYMENT_MODE_PAYPHI_EDC_UPI = 40;

	public static final int PAYMENT_MODE_PAYTM_DQR_UPI = 39;
	public static final List<Integer> PAYTM_AVAILABLE_PAYMENT_MODES= Arrays.asList(PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD, PAYMENT_MODE_PAYTM_EDC_AMEX_CARD, PAYMENT_MODE_PAYTM_EDC_UPI, PAYMENT_MODE_PAYTM_DQR_UPI);
	public static final int CHAI_ON_DEMAND_UNIT_ID = 11001;
	public static final String CAFE = "CAFE";
	public static final String COD = "COD";
	public static final String TAKE_AWAY = "TAKE_AWAY";
	public static final String NEO = "NEO";
	public static final String HYPHEN = "-";
	public static final int CHANNEL_PARTNER_WEB_APP = 14;
	public static final int ZERO = 0;
	public static final int ONE = 1;

	public static final int SYSTEM_EMPLOYEE_ID = 120056;
	public static final String SYSTEM_EMPLOYEE_NAME = "System";

	public static final String ETKN = "ETKN";

	public static final String KETTLE = "Kettle";
	public static final String KETTLE_SERVICE = "KETTLE_SERVICE";


	public static final Integer[] SET_VALUES = new Integer[] { 1000007,1043, 1044,1027,1026,1048,861 };
	public static final Set<Integer> HIDDEN_PRODUCTS_SET = new HashSet<>(Arrays.asList(SET_VALUES));

	public static final Integer[] ZERO_TAX_PRODUCTS_ID = new Integer[] {1043, 1044 };
	public static final Set<Integer> ZERO_TAX_PRODUCTS_SET = new HashSet<>(Arrays.asList(ZERO_TAX_PRODUCTS_ID));

	public static final String CHARSET = "UTF-8";

	public static final int CATEGORY_OTHERS = 12;

	public static final int CATEGORY_MERCHANDISE = 9;

	public static final String TAX_EXEMPTED = "TAX_EXEMPTED";

	public static final String GIFT_CARD_TAX_CODE = "GIFT_CARD";
	public static final String WALLET_TYPE= "WALLET_TYPE";
	public static final String SUGGEST_WALLET= "SUGGEST_WALLET";
	public static final String GIFT_CARD_WALLET= "GIFT_CARD_WALLET";
	public static final String PRODUCT_ATTRIBUTE_NON_VEG = "NON_VEG";
	public static final String COMBO_TAX_CODE = "COMBO";
	public static final String ZERO_TAX_CODE = "ZERO_TAX";
	public static final String REFUND_VOUCHER = "REFUND_VOUCHER";
	public static final int GST_TAX_ID = 1;
	public static final int COUNTRY_ID_INDIA = 1;

	public static final String ORDER_TYPE_REGULAR = "order";
	public static final String ORDER_TYPE_EMPLOYEE_MEAL = "employee-meal";
	public static final String ORDER_TYPE_PAID_EMPLOYEE_MEAL = "paid-employee-meal";
	public static final String ORDER_TYPE_COMPLIMENTARY = "complimentary-order";
	public static final String ORDER_TYPE_UNSTAISFIED_CUSTOMER = "unsatisfied-customer-order";
	public static final String ORDER_TYPE_PPE_ORDER = "PPE";
	public static final String ORDER_TYPE_TESTING_ORDER = "test-order";
	public static final String ORDER_TYPE_TABLE_ORDER = "table_order";


	public static final String REPORT_FREQUENCY = "FREQUENCY";
	public static final String BLANK = "";
	public static final String SFTP = "SFTP";

	public static final String DIMESION_FULL_STRING = "Full";
	public static final String DIMESION_REGULAR_STRING = "Regular";

	public static final String BUDGET_UNAVAILABLE = "BUDGET_NOT_PROVIDED";

	public static final String ZTZ = "ZTZ";

	public static final String AM_RESPONSE = "amResponse";
	public static final String DGM_RESPONSE = "dgmResponse";

	public static final String RECIPE_PROFILE_P0 = "P0";
	public static final String RECIPE_PROFILE_P1 = "P1";
	public static final String RECIPE_PROFILE_OP1 = "O_P1";
	public static final String RECIPE_PROFILE_P2 = "P2";
	public static final String RECIPE_PROFILE_P3 = "P3";

	public static  final String RECIPE_PROFILE_P4 = "P4";
	public static  final String RECIPE_PROFILE_OP4 = "O_P4";
	public static final String RECIPE_PROFILE_CC1 = "CC1";
	public static final String RECIPE_PROFILE_OP0 = "O_P0";
	public static final String RECIPE_PROFILE_OP2 = "O_P2";
	public static final String RECIPE_PROFILE_OP3 = "O_P3";
	public static final String RECIPE_PROFILE_OCC1 = "O_CC1";
	public static final String RECIPE_PROFILE_DC0 = "DC0";

	public static final String RECIPE_PROFILE_ODC0 = "O_DC0";
	public static final String RECIPE_PROFILE_PP2 = "PP2";
	public static final String RECIPE_PROFILE_OPP2 = "O_PP2";
	public static final String RECIPE_PROFILE_PP3 = "PP3";

	public static final String RECIPE_PROFILE_OPP3 = "O_PP3";
	public static final String RECIPE_PROFILE_PC1 = "PC1";
	public static final String RECIPE_PROFILE_OPC1 = "O_PC1";

	public static final String DEFAULT_RECIPE_PROFILE = RECIPE_PROFILE_P0;
	public static final String ORDER_CANCELLATION = "OrderCancellation";
	public static final String FORWARD_SLASH = "/";
	public static final int CHANNEL_PARTNER_ZOMATO = 3;
	public static final int CHANNEL_PARTNER_SWIGGY = 6;
	public static final int CHANNEL_PARTNER_MAGICPIN = 24;
	public static final String BOOLEAN = "boolean";
	public static final int CHANNEL_PARTNER_DINE_IN_APP = 21;
	public static final String CALL_BACK_QUESTION = "call you back";

	public static final String PERCENTAGE = "PERCENTAGE";
	public static final String FIXED = "FIXED";
	public static final String BATCH_CODE_KEY_TYPE_UNIT = "UNIT";
	public static final String BATCH_CODE_KEY_TYPE_SKU = "SKU";

	//TODO Vivek Move this to metadata
	public static final  String CHAAYOS_CASH_OFFER_CODE = "CHAAYOS_CASH";

	public static final  String DOHFUL_CASH_OFFER_CODE = "DOHFUL_CASH";
	public static final  String CHAAYOS_CASH_BACK_OFFER_CODE = "CASHBACK";
	public static final BigDecimal CHAAYOS_CASH_REDEMPTION_PER_ORDER = new BigDecimal(100);
	public static final int CHAAYOS_CASH_OFFER_REDEMPTION_DAYS = 0;
	public static final int CHAAYOS_CASH_OFFER_MAX_ALLOWED_REDEMPTION = 1;

	public static final  String CHAAYOS_SELECT_SUBSCRIPTION_OFFER_CODE = "CHAAYOS_SELECT";
	public static final  String CHAAYOS_PRO_SUBSCRIPTION_OFFER_CODE = "CHAAYOS_PRO";

	public static final String TRUE_CALLER = "TRUE_CALLER";

	public static final int CHAAYOS_BRAND_ID = 1;
	public static final Integer DOHFUL_BRAND_ID = 6;
	public static final Integer DOHFUL_COMPANY_ID = 1005;
	public static final Integer SUNSHINE_COMPANY_ID = 1000;

	public static final int SWIGGY_INSTAMART_BRAND_ID = 5;

	public static final int DESI_CANTEEN_BRAND_ID = 2;

	public static final int GNT_BRAND_ID = 3;

	public static final int CHAAYOS_DINEIN_PARTNER_ID = 1;

	public static final int CHAAYOS_DELIVERY_PARTNER_ID = 2;

	public static final String ASSET_DEPRECIATION_STRATEGY = "FLAT_LINE";
	public static final int UPDATE_INVENTORY_SUCCESS = 1;
	public static final int UPDATE_INVENTORY_FAIL = -1;
	public static final int UPDATE_INVENTORY_INIT = 0;

	public static final String S3_REPORT_BASE_PATH = "reports";
	public static final String S3_REPORT_VERSION_FOLDER = "TEST_REPORT_VERSION";
	public static final String MAPPING_SOURCE_COUPON = "COUPON";
	public static final String MAPPING_SOURCE_OFFER = "OFFER";
	public static final String CHANNEL_PARTNER_DEFAULT_COUPON = "ZOMATO";

	public static final List<Integer> PROMOTER_RATINGS = Arrays.asList(9,10);

	public static final String REMAINING_DAY="REMAINING_DAY";
	public static final String ORDERING_DAY="ORDERING_DAY";

	public static final int PSEUDO_GIFT_CART_ID = 1;
	public static final int MICRO_WALLET_ID = 2;
	public static final int ADVANCE_PAYMENT_PRODUCT_ID = 3;

	public  static final String ABOUT_TO_STOCK_OUT="1";
	public  static final String STOCK_OUT="0";
	public static final String SIGNUP_OFFER_DELIVERY = "SIGNUP_OFFER_DELIVERY";
    //MAC
	public static final String ACCEPTED = "ACCEPTED";
	public static final String REJECTED = "REJECTED";
	public static final Integer ORDER_CANCELLATION_THRESHOLD=60 ; //second

	public static final String BULK_ADJUSTMENT = "BULK_ADJUSTMENT";

	public static final String COMPANY_SHORT_NAME = "STPL";

	public static final int BAZAAR_PARTNER_ID = 23;
	public static final int BAZAAR_UNIT_ID = 26254;

	public static final String LOYALTY_GIFTING= "GIFTING";

	public static final Integer PROMOTER= 9;
	public static final Integer PASSIVE= 8;
	public static final Integer FEEDBACK_PROMOTER= 4;
	public static final Integer FEEDBACK_PASSIVE= 3;
	//public static final BigDecimal DETRACTOR= new BigDecimal(9);
	public static final List<Integer> DESI_CHAI_ID= Arrays.asList(10,11,12,14,15,50);
	public static final List<Integer> BAARISH_CHAI_ID= Arrays.asList(1282,1292,1293,1294);
	public static final List<String> ACTIVE_EMPLOYEE_APPLICATIONS = Arrays.asList("KETTLE_SERVICE","KETTLE_CRM");
	public static final String DESI_CHAI="Desi Chai";
	public static final String BAARISH_CHAI="Baarish Wali Chai";

	public static final String STATUS_CREATED="CREATED";
	public static final String STATUS_NOTIFIED="NOTIFIED";
	public static final String STATUS_SUCCESSFUL="SUCCESSFUL";

	public static final List<Integer> EXCLUDE_CUSTOMER_IDS= Arrays.asList(1,2,3,4,5,24035,67456,142315,24035,142315,3527255);

	public static final Set<Integer> SEASONAL_PRODUCT_IDS_1 = new HashSet<Integer>( Arrays.asList(1000065,1000080));/*Anda Parantha and Dhokla*/
	public static final Set<Integer> SEASONAL_PRODUCT_IDS_2 = new HashSet<Integer>( Arrays.asList(1000084,1000085));/*Veg puff and chicken puff*/
	public static final Set<Integer> SEASONAL_PRODUCT_IDS_3= new HashSet<Integer>( Arrays.asList(1000066));/*Kesar Chai*/
	public static final int SYSTEM_UNIT_ID = 0;



	public static final Pattern VALID_EMAIL_ADDRESS_REGEX = Pattern.compile("^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,6}$",
			Pattern.CASE_INSENSITIVE);

	public  static final String STOCK_IN_CONSTANT="STOCK_IN";
	public  static final String STOCK_OUT_CONSTANT="STOCK_OUT";
	public static final String INITIATED = "INITIATED";
	public static final String FIXED_ASSETS = "FA_Equipment";
	public static final String FIXED_ASSET_ORDER = "FIXED_ASSET_ORDER";
	public static final String GOODS = "NRE_Consumable";
	public static final String NEXT_BEST_OFFER = "NEXT_BEST_OFFER";
	public static final String LOYAL_TEA_COUPON_CODE = "LOYAL_TEA";
	public static final Integer NEXT_BEST_OFFER_CAMPAIGN_ID = 1;

	public static final String MD5_HASH = "MD5";
	public static final String SHA256_HASH = "SHA-256";

	public static final String FACEBOOK = "FB";
	public static final String GOOGLE = "GOOGLE";
	public static final String CLEVERTAP = "CLV";

	public static final Integer DELIVERY_PRODUCT_ID = 1044;
	public static final Integer PACKAGING_PRODUCT_ID = 1043;

	public static final String marketingImage = "MARKETING_IMAGES_MOB_VIEW";
	public static final String marketingImageWebView = "MARKETING_IMAGES_WEB_VIEW";

	public static final BigDecimal UNIT_PRODUCT_PRICE_THRESHOLD = BigDecimal.valueOf(0.20);

	public static final String SMS = "SMS";
	public static final String PREFERENCE = "PREFERENCE";

	public static final String APP = "APP";

	public static final String F9_ORDERING = "FOUNTAIN9_DATA_SOURCE_ORDERING";

	public static final String INGREDIENT = "INGREDIENT";

	public static final String OVER_ALL = "OVER_ALL";
	public static final String REGION = "REGION";
	public static final String CITY = "CITY";
	public static final int SUBSCRIPTION_PRODUCT_SUB_CATEGORY = 3810;
	public static final String ERROR = "ERROR";

	//Type common with bakery merchendise.
	public static final List<Integer> CLEVERTAP_FOOD_PRODUCT_IDS= Arrays.asList(780, 790, 800, 810, 865, 1003, 1004, 1005, 1006, 1007, 1145, 1146, 1147, 1182,
			1183, 1238, 1239, 1586, 1587, 1588, 1589, 1590);
	public static final List<Integer> CLEVERTAP_FOOD_PRODUCT_TYPE= Arrays.asList(7, 10, 52, 53, 60, 61);
	public static final List<String> IGNORE_UNIT_KEYWORDS= Arrays.asList("odc","zepto","zomato","bazaar","test","zomaro","bots");


	public static final String KNOCK_NOTIFICATION_ENDPOINT ="rest/notification/";

	public static final Integer WS_MANUAL_TASK_ORDER_TIME_DIFF = 1;

	public static final String AGREEMENT_TYPE_VENDOR = "VENDOR";
	public static final String CREATED_VENDOR = "CREATED";
	public static final String EDITED_VENDOR = "EDITED";


	public static final String CONACT_NUMBER = "contactNumber";

	public static final String EMAIL_ID = "emailId";

	public static final String CONACT_NUMBER_DATA = "contactNumberData";

	public static final String EMAIL_ID_DATA = "emailIdData";

	public static final String CUSTOMER_INFO = "CustomerInfo";

	public static final String CUSTOMER_LEAD_SOURCES = "FB_LEADGEN,GOOGLE,CHAAYOS-COD,NEO_SERVICE,FACEBOOK,NEWSPAPER,INSTAGRAM,CLM,WHATSAPP,SHOPIFY,CONTLO_SHOPIFY";

	public static final String CUSTOMER_CONTACT_INFO_MAPPING_OLD = "CustomerContactInfoMappingOld";

	public static final String CUSTOMER_CONTACT_INFO_MAPPING = "CustomerContactInfoMapping";

	public static final String WEB_OFFER_COUPON_REDEMPTION_DETAIL = "WebOfferCouponRedemptionDetail";

	public static final String TRUE_CALLER_PROFILE_DETAIL = "TrueCallerProfileDetail";

	public static final String TRUE_CALLER_PROFILE_DETAIL_SECONDARY = "TrueCallerProfileDetailSecondary";

	public static final String SIGNUP_IMAGE_MAPPING = "SignupImageMapping";

	public static final String LOOKUP_IMAGE_MAPPING = "LookupImageMapping";

	public static final String TRUE_CALLER_REQUEST_DETAIL = "TrueCallerRequestDetail";

	public static final Integer MONK_CALIBRATION_MARGIN_TIME_MS = 1800 * 1000;

	public static final String WHATSAPP = "WHATSAPP";

	public static final String GOOGLE_MY_BUSINESS = "GMB";

	public static final String KETTLE_ORDER_VERSION = "v1";
	public static final String LOYAL_TEA_OFFER_CODE = "LOYALTEA";

	public static final List<String> MENU_TIMING_SERVICES = Arrays.asList("DELIVERY","TAKEAWAY");

	public static final String GET_PRODUCT_PROXY = "PRODUCT_PROXY";

	public static final String GET_LIST_PROXY = "LIST_PROXY";



	public static final String ASSEMBLY_ORDER_DISPATCHED_STATUS_CHANNEL = "_assembly_order_dispatched_channel_";
	public static final String ORDER_RECEIPT_PATH = "/orders/";

	public static final String IS_EXHAUSTED = "Y";
	public static final String IS_REDEEMED = "Y";

	public static final String POS = "POS";
	public static final String CAFE_APP = "CAFE_APP";

	public static final String DEFAULT_TIME_ZONE="Asia/Kolkata";

	public static final List<Integer> SKIP_LOYALTY_PRODUCTS= Arrays.asList(1377,1000379);

	public static final String PROCESSING = "PROCESSING";
	public static final String PROCESSED = "PROCESSED";

	public static final String DECLINE = "DECLINE";
	public static final String DATA_SPREADSHEET = "1dRljf2lvU4xn0ZiO0d0-M7OhKObzXIp40GxZL00iy_k";
	public static final String ZFR_SHEET = "360324067";
	public static final String GV_SHEET = "815740426";
	public static final String ILR_SHEET = "103876386";
	public static final String ORS_SHEET = "1478240031";
	public static final String PROMO_REDEMPTION_SHEET = "1197277308";
	public static final String REJECTION_SHEET = "1530550540";
	public static final String ADJUNCT_SHEET = "1lBRov8yk8g60hmg6MuoYRAJyxCq-mQr3u1o-FZxJOlk";

	public static final String TABLEAU_SITE_ID_ZFR = "c651c2d2-6f2b-4959-a84f-849f6e9ff74a";
	public static final String TABLEAU_WORKBOOK_ID_ZFR = "83d02d05-2818-410a-96ac-ce1ec9c5a18c";

	public static final String TABLEAU_SITE_ID_CR = "c651c2d2-6f2b-4959-a84f-849f6e9ff74a";
	public static final String TABLEAU_WORKBOOK_ID_CR = "5e5612a4-4690-485c-aff5-86db0940901c";
	public static final String TABLEAU_WORKBOOK_ID_CG = "aad80a5d-5140-4b33-af03-01f7e148430a";

	public static final String TABLEAU_SITE_ID_SFR = "";
	public static final String TABLEAU_WORKBOOK_ID_SFR = "";

//	public static final String LABEL_UPDATE_ZFR = "Label_4708785314306790833";
	public static final String LABEL_SWIGGY_ADS_FUNNEL = "Label_3009353752217329812";
	public static final String LABEL_SWIGGY_MTD = "Label_1662093608223545687";
	public static final String LABEL_TEST_LABEL = "Label_3831545467334435631";
	public static final String LABEL_ZOMATO_CR = "Label_260090983796568036";

	public static final String LABEL_WHATSAPP_DATA = "Label_646333358209038592";
	public static final String LABEL_WHATSAPP_DATA_183 = "Label_9033897514276344016";

	public static final String STRING = "STRING";


	public static final String APP_INSTALLED = "INSTALLED";

	public static final String APP_UNINSTALLED = "UNINSTALLED";

	public static final int SCM_MILK_PRODUCT_ID = 100234;

	public static final int SCM_SUGAR_PRODUCT_ID = 100330;

	public static final String SCM_NO_SUGAR_VARIANT_PRODUCT_ALIAS  = "No Sugar";
	public static final String THROUGH_CUSTOMER_ID = "CUSTOMER_ID";
	public static final String THROUGH_ORDER_ID = "ORDER_ID";

	public static final String THROUGH_GENERATED_ORDER_ID = "GENERATED_ORDER_ID";

	public static final List<Integer> BIRTHDAY_MONTH_OFFER_IDS = Arrays.asList(3909,3908);
	public static final String WINBACK = "WINBACK";
	public static final String CALL_CENTER = "CALL_CENTER";
	public static final String RTL_COMPENSATION_DELAY_REASONS = "COMPENSATION_REASONS";
	public static final String PARTNER_MANAGEMENT_ENDPOINT = "v1/partner-management/";
	public static final String GUR = "GUR";
	public static final String HONEY = "HONEY";
	public static final String SUGAR_FREE = "SUGAR FREE";
	public static final String UNSATISFIED_CUSTOMER_ORDER = "unsatisfied-customer-order";
	public static final int CHAI_COMPLETION_STATUS_CODE = 19;
	public static final String CHAI_COMPLETION_S_CODE = "S19";
	public static final int RESPONSE_CHAI_COMPLETE=1;
	public static final String REVALIDATION_SUCCESSFUL = "REVALIDATION_SUCCESSFUL";
	public static final String CUSTOMER = "CUSTOMER";
	public static final String NOT_DELIVERED = "NOT_DELIVERED";
	public static final String REGULAR_TAG = "REGULAR_TAG";
	public static final String NUTRITION_TAG = "NUTRITION_TAG";

	public  static final List<Integer> codPartnerIds = new ArrayList<>(Arrays.asList(3,6,24));

	public static final String SCRATCH_CARD = "SCRATCH_CARD";

	public static final String oatMilkPrefix = "O_";
	public static final String almondMilkPrefix = "A_";

	public  static final Integer DUMMY_ORDER_CANCELLATION_ID = -1;

	public  static final Integer OAT_MILK_SCM_PRODUCT_ID = 104191;

	public static final Integer OAT_MILK_DOHFUL_SCM_PRODUCT_ID = 104371;
	public static final Integer ALMOND_MILK_DOHFUL_SCM_PRODUCT_ID = 104372;
	public  static final String OAT_MILK_SCM_PRODUCT_NAME = "Oats-Milk";
	public  static final String OAT_MILK_DOHFUL_SCM_PRODUCT_NAME = "Oat milk _Dohful";
	public  static final String ALMOND_MILK_DOHFUL_SCM_PRODUCT_NAME = "Almond milk _Dohful";

	public static final Integer DOHFUL_SCM_MILK_PRODUCT_ID = 104349;

	public static final String NOT_APPLICABLE = "NOT_APPLICABLE";

	public static final Integer FRESH_CREAM_GNT_SCM_PRODUCT_ID = 102316;
	public static final String K = "K";
	public static final String NS = "NS";
	public static final String R = "R";
	public static final String TECHNOLOGY_EMAIL = "<EMAIL>";
	public static final String REPORTING_EMAIL = "<EMAIL>";
	public static final String FINANCE_EMAIL = "<EMAIL>";
	public static final String SERVICE_CHARGE = "Optional Service Charge";
	public static final Integer SERVICE_CHARGE_SUBTYPE = 4038;
	public static final String SERVICE_CHARGE_DESCRIPTION = "We levy an optional service charge which goes to the team involved in serving you today. This is completely optional and at your discretion.";
	public static final String CAMPAIGN = "CAMPAIGN";
	public static final List<Integer> CHAAYOS_COMPANY_IDS = new ArrayList<>(Arrays.asList(1000, 1001, 1002, 1003, 1004,1006));

	public static final String TESTING_UNIT = "TESTING_UNIT";
	public static final String PRICE_PROFILE_ID = "PRICE_PROFILE_ID";

	public static final String PRICE_PROFILE_VERSION = "PRICE_PROFILE_VERSION";

	public static final List<Integer> COD_MAPPINGS_FOR_CHECKLIST = new ArrayList<>(Arrays.asList(CHANNEL_PARTNER_ZOMATO, CHANNEL_PARTNER_SWIGGY, CHANNEL_PARTNER_MAGICPIN));
	public static final List<Integer> MENU_MAPPING_FOR_CHECKLIST = new ArrayList<>(Arrays.asList(
			CHAAYOS_DINEIN_PARTNER_ID, CHANNEL_PARTNER_ZOMATO, CHANNEL_PARTNER_SWIGGY, CHANNEL_PARTNER_MAGICPIN, CHANNEL_PARTNER_WEB_APP, CHANNEL_PARTNER_DINE_IN_APP
	));
	public static final List<String> COD_IMAGE_TYPES_FOR_CHECKLIST = new ArrayList<>(Arrays.asList("GRID_MENU_LOW", "GRID_MENU_LOW_WEBP", "GRID_MENU_100X100", "GRID_MENU_400X400"));
	public static final List<String> DINE_IN_IMAGE_TYPES_FOR_CHECKLIST = new ArrayList<>(Collections.singletonList("GRID_MENU_LOW"));

	public static final Integer EMP_MEAL_UNIT_ID = 26045;
	public static final String IN_PROGRESS = "IN_PROGRESS";
	public static final Integer HOT_BEVERAGE_PRODUCT_TYPE = 5;
	public static final String HOT_BEVERAGE_STATION_CATEGORY_NAME = "Hot Beverage";
	public static final Integer HOT_BEVERAGE_STATION_CATEGORY_ID=4009;

	public static final List<String> codMappings = new ArrayList<>(Arrays.asList("productStatus", "unitProductMapping",
			"unitProductPricing", "codProductMappingZomato", "codProductMappingSwiggy", "codProductMappingMagicPin",
			"codProductPriceMappingZomato", "codProductPriceMappingSwiggy", "codProductPriceMappingMagicPin", "menuMappingStatusZomato",
			"menuMappingStatusSwiggy", "menuMappingStatusMagicPin", "recipeLive", "priceProfileMappingZomato", "priceProfileMappingSwiggy",
			"priceProfileMappingMagicPin", "imageMappingStatusCOD", "hotBeverageMonkRecipe", "inventoryTrackValid","inventoryTrackInValidReason"));

	public static final List<String> dineInMappings = new ArrayList<>(Arrays.asList("productStatus", "unitProductMapping",
			"unitProductPricing", "menuMappingStatusChaayosDineIn", "menuMappingStatusDineInApp", "recipeLive",
			"priceProfileMappingDineIn", "imageMappingStatusDineIn", "hotBeverageMonkRecipe", "inventoryTrackValid","inventoryTrackInValidReason"));

	public static final Integer GIFT_SUB_CATEGORY = 904;
	public static final Integer CHAAYOS_MEAL_COMBO_TYPE = 43;

	public static final String CHAI_MONK_DEFAULT_VERSION = "V1";

	public static final String OFFER_TIMING_SEPARATOR = "--";
	public static final Integer NO_OF_CANCEL_ORDER_EMAILS_TO_BE_SENT=5;

	public static final List<Integer> employeeMealEligibleDepartments = new ArrayList<>(Arrays.asList(101,114,126,112,143,129,114,133));


}
