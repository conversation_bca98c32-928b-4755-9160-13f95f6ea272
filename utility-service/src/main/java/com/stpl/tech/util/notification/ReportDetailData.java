/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util.notification;

import java.util.List;
import java.util.Set;

public class ReportDetailData {

	private String name;
	private List<List<String>> content;
	private Set<Integer> skipColumns;
	private boolean writeInline = true;

	public ReportDetailData(String name, List<List<String>> content, boolean writeInline, Set<Integer> skipColumns) {
		super();
		this.name = name;
		this.content = content;
		this.writeInline = writeInline;
		this.skipColumns = skipColumns;
	}

	public ReportDetailData() {
		super();
	}

	public List<List<String>> getContent() {
		return content;
	}

	public void setContent(List<List<String>> content) {
		this.content = content;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isWriteInline() {
		return writeInline;
	}

	public void setWriteInline(boolean writeInline) {
		this.writeInline = writeInline;
	}

	public Set<Integer> getSkipColumns() {
		return skipColumns;
	}

	public void setSkipColumns(Set<Integer> skipColumns) {
		this.skipColumns = skipColumns;
	}
	
	

}
