package com.stpl.tech.util.notification.model;

public class ResponseData<T> {

	private boolean success;
	private String message = "FAILED";
	private T payload;

	public ResponseData() {
		// TODO Auto-generated constructor stub
	}

	public ResponseData(boolean status, String message) {
		this.success = status;
		this.message = message;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public T getPayload() {
		return payload;
	}

	public void setPayload(T payload) {
		this.payload = payload;
	}
}
