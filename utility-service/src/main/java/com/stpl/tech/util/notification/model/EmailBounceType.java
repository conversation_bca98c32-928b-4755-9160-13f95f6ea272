package com.stpl.tech.util.notification.model;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 27-11-2017.
 */
public class EmailBounceType {
    private String bounceType;
    private String bounceSubType;
    private List<BouncedRecipient> bouncedRecipients;
    private String timestamp;
    private String feedbackId;
    private String reportingMTA;

    public String getBounceType() {
        return bounceType;
    }

    public void setBounceType(String bounceType) {
        this.bounceType = bounceType;
    }

    public String getBounceSubType() {
        return bounceSubType;
    }

    public void setBounceSubType(String bounceSubType) {
        this.bounceSubType = bounceSubType;
    }

    public List<BouncedRecipient> getBouncedRecipients() {
        return bouncedRecipients;
    }

    public void setBouncedRecipients(List<BouncedRecipient> bouncedRecipients) {
        this.bouncedRecipients = bouncedRecipients;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(String feedbackId) {
        this.feedbackId = feedbackId;
    }

    public String getReportingMTA() {
        return reportingMTA;
    }

    public void setReportingMTA(String reportingMTA) {
        this.reportingMTA = reportingMTA;
    }
}
