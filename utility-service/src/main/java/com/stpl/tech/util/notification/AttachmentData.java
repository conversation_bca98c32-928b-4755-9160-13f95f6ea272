/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util.notification;

import com.stpl.tech.util.AppConstants;

public class AttachmentData {

	private byte[] attachment;
	private String fileName;
	private String contentType;

	public AttachmentData(){}

	public AttachmentData(String fileName, String contentType) {
		this.contentType = contentType;
		this.fileName = fileName + getFileExtension(contentType);
	}

	public AttachmentData(byte[] attachment, String fileName, String contentType) {
		super();
		this.contentType = contentType;
		this.attachment = attachment;
		this.fileName = fileName + getFileExtension(contentType);
	}

	public byte[] getAttachment() {
		return attachment;
	}

	public void setAttachment(byte[] attachment) {
		this.attachment = attachment;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}

	public String getFileExtension() {
		return getFileExtension(contentType);
	}
	
	private  String getFileExtension(String contentType) {
		if (contentType.equals(AppConstants.EXCEL_MIME_TYPE)) {
			return ".xlsx";
		} else if (contentType.equals(AppConstants.CSV_MIME_TYPE)) {
			return ".csv";
		} else if (contentType.equals(AppConstants.ZIP_MIME_TYPE)) {
			return ".zip";
		} else if (contentType.equals(AppConstants.PDF_MIME_TYPE)) {
			return ".pdf";
		} else if (contentType.equals(AppConstants.JSON_MIME_TYPE)) {
			return ".json";
		} else if(contentType.equals(AppConstants.EXCEL_MIME_TYPE_XLS)){
			return ".xls";
		} else if(contentType.equals(AppConstants.JPEG_MIME_TYPE)){
			return ".jpeg";
		}else if(contentType.equals(AppConstants.JPG_MIME_TYPE)){
			return ".jpg";
		}else if(contentType.equals(AppConstants.PNG_MIME_TYPE)){
			return ".png";
		}
		return ".txt";
	}
}
