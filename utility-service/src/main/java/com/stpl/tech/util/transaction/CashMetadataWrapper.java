package com.stpl.tech.util.transaction;

import java.math.BigDecimal;
import java.util.Date;

public class CashMetadataWrapper {

	private Integer customerId;
	private Integer referentId;
	private String customerRefCode;
	private Integer referralMappingId;
	private Integer cashLogDataId;
	private Integer orderId;
	private BigDecimal transactionAmount;
	private Date transactionTime;
	private String contactnumber;
	private CashTransactionMetadata metadata;

	public CashMetadataWrapper() {
		// TODO Auto-generated constructor stub
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer referrerId) {
		this.customerId = referrerId;
	}

	public Integer getReferentId() {
		return referentId;
	}

	public void setReferentId(Integer referrentId) {
		this.referentId = referrentId;
	}

	public String getCustomerRefCode() {
		return customerRefCode;
	}

	public void setCustomerRefCode(String refCode) {
		this.customerRefCode = refCode;
	}

	public Integer getReferralMappingId() {
		return referralMappingId;
	}

	public void setReferralMappingId(Integer referralMappingId) {
		this.referralMappingId = referralMappingId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public Date getTransactionTime() {
		return transactionTime;
	}

	public void setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	public CashTransactionMetadata getMetadata() {
		return metadata;
	}

	public void setMetadata(CashTransactionMetadata metadata) {
		this.metadata = metadata;
	}

	public Integer getCashLogDataId() {
		return cashLogDataId;
	}

	public void setCashLogDataId(Integer cashLogDataId) {
		this.cashLogDataId = cashLogDataId;
	}

	public String getContactnumber() {
		return contactnumber;
	}

	public void setContactnumber(String contactnumber) {
		this.contactnumber = contactnumber;
	}

}
