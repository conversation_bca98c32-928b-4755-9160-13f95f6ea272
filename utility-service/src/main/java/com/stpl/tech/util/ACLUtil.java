package com.stpl.tech.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by Chaayos on 26-10-2016.
 */
public class ACLUtil {
	
	private static final Logger LOG = LoggerFactory.getLogger(ACLUtil.class);
	private static final ACLUtil INSTANCE = new ACLUtil();
	
	private Map<String, String> uriMap = new HashMap<String, String>();
	
	private ACLUtil() {
		
	}

	public static ACLUtil getInstance() {
		return INSTANCE;
	}
	
	public void clearUriCache(String source) {
		LOG.info("Clearing URI Cache from source  : " + source + " of size : " + uriMap.size());
		this.uriMap = new HashMap<>();
	}
	
	
	public String convertURIToModule(String uri) {
		if (!uriMap.containsKey(uri)) {
			String requestURI = uri.substring(1);
			String application = requestURI.substring(0, requestURI.indexOf('/'));
			int index = requestURI.indexOf("v1/") != -1 ? requestURI.indexOf("v1/") : requestURI.indexOf("v2/");
			String servletURL = requestURI.substring(index + 3);
			servletURL = servletURL.replaceAll("/", ".");
			uriMap.put(uri, application + "." + servletURL);
			LOG.info("ACLUtil:Size" + uriMap.size());
		}
		return uriMap.get(uri);
	}

    public Boolean checkPermission(Map<String, Integer> map, String module, String requestMethod) {
        switch (requestMethod) {
            case "GET":
                return hasPermission(map, module, 1);
            case "PUT":
                return hasPermission(map, module, 2);
            case "POST":
                return hasPermission(map, module, 4);
            case "DELETE":
                return hasPermission(map, module, 8);
            default:
                return false;
        }
    }

    public Boolean hasPermission(Map<String, Integer> map, String module, int permissionId) {
        if (map != null) {
            while (map.size() > 0) {
                if (map.containsKey(module)) {
                    return true;
                } else {
                    if (module.contains(".*")) {
                        module = module.substring(0, module.lastIndexOf('.') - 1);
                    }
                    if (!module.isEmpty() && module.contains(".")) {
                        module = module.substring(0, module.lastIndexOf('.'));
                        module = module.concat(".*");
                    } else {
                        break;
                    }
                }
            }
        }
        return false;
    }

    public Boolean hasPermission(List<String> list, String module) {
        if (list != null) {
            while (list.size() > 0) {
                if (list.contains(module)) {
                    return true;
                } else {
                    if (module.contains(".*")) {
                        module = module.substring(0, module.lastIndexOf('.') - 1);
                    }
                    if (!module.isEmpty() && module.contains(".")) {
                        module = module.substring(0, module.lastIndexOf('.'));
                        module = module.concat(".*");
                    } else {
                        break;
                    }
                }
            }
        }
        return false;
    }
}
