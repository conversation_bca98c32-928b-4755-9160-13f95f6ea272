/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util;

import com.google.gson.*;
import com.stpl.tech.util.domain.adapter.BigDecimalAdapter;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.Date;

public class JSONSerializer {

	private static final GsonBuilder builder = new GsonBuilder();
	private static final Gson gson;
	private static final JsonParser jsonParser = new JsonParser();

	static {
		// Register an adapter to manage the date types as long values
		builder.registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
			public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
					throws JsonParseException {
				Date date;
				try {
					date = new Date(json.getAsJsonPrimitive().getAsLong());
					return date;
				} catch (NumberFormatException e) {
					date = AppUtils.parseDate(json.getAsJsonPrimitive().getAsString());
					return date;
				}
			}
		});

		builder.registerTypeAdapter(BigDecimal.class, new JsonDeserializer<BigDecimal>() {
			@Override
			public BigDecimal deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
					throws JsonParseException {
				return BigDecimalAdapter.parseBigDecimalToSix(json.getAsString());
			}
		});

		builder.setDateFormat("yyyy-MM-dd HH:mm:ss");
		gson = builder.create();
	}

	public static <T> T toJSON(String str, Class<T> clazz) {
		JsonElement element = jsonParser.parse(str);
		return gson.fromJson(element, clazz);
	}

	public static <T> T toJSON(String str, Type type) {
		JsonElement element = JsonParser.parseString(str);
		return gson.fromJson(element, type);
	}

	public static <T> T toJSON(Object obj, Type type) {
		return gson.fromJson(gson.toJson(obj), type);
	}

	public static <T> String toJSON(T object) {
		return gson.toJson(object);
	}

}
