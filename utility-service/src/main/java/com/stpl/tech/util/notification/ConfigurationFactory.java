/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.util.notification;

import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;

public class ConfigurationFactory {

	private static Configuration freemarkerConfig;

	// TODO fix this
	public static Configuration getFreemarkerConfiguration() {
		if (freemarkerConfig == null) {
			freemarkerConfig = new Configuration(Configuration.VERSION_2_3_23);
		}
		freemarkerConfig.setClassForTemplateLoading(ConfigurationFactory.class, "/");
		freemarkerConfig.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
		freemarkerConfig.setDefaultEncoding("UTF-8");
		return freemarkerConfig;

	}
}
