#BLOCK A1
SELECT 
    A.BIZ_DATE,
    'Overall' AS TOTAL,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    
    (A<PERSON>NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.WALLET_REDEMPTION - A.NET_DELIVERY_SALES - A.WALLET_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_WALLET_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_WALLET_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    TRUNCATE((A.NET_SALES + A.WALLET_REDEMPTION - A.NET_DELIVERY_SALES - A.WALLET_AMOUNT - A.NET_APP_SALES) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS - A.NET_APP_TICKETS),
        0) CAFE_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_WALLET_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_WALLET_AMOUNT - B.LWSD_NET_APP_SALES) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS - B.LWSD_NET_APP_TICKETS),
        0) LWSD_CAFE_DINE_IN_APC,
    
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    
    A.Chaayos_NET_DELIVERY_TICKETS,
    B.Chaayos_LWSD_NET_DELIVERY_TICKETS,
    A.Chaayos_NET_DELIVERY_SALES,
    B.Chaayos_LWSD_NET_DELIVERY_SALES,
    A.Chaayos_NET_DELIVERY_APC,
    B.Chaayos_LWSD_NET_DELIVERY_APC,
    
    
    A.GnT_NET_DELIVERY_TICKETS,
    B.GnT_LWSD_NET_DELIVERY_TICKETS,
    A.GnT_NET_DELIVERY_SALES,
    B.GnT_LWSD_NET_DELIVERY_SALES,
    A.GnT_NET_DELIVERY_APC,
    B.GnT_LWSD_NET_DELIVERY_APC,
    
    A.NET_APP_TICKETS,
    B.LWSD_NET_APP_TICKETS,
    A.NET_APP_SALES,
    B.LWSD_NET_APP_SALES,
    A.NET_APP_APC,
    B.LWSD_NET_APP_APC,
	TRUNCATE((COALESCE(A.NET_APP_TICKETS, 0)/(A.NET_TICKETS - A.NET_DELIVERY_TICKETS)*100),0) APP_PENETRATION,
	TRUNCATE((COALESCE(B.LWSD_NET_APP_TICKETS, 0)/(B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS)*100),0) LWSD_APP_PENETRATION,
    
    
    COALESCE(TRUNCATE(C.CAFE_ONLY_CUSTOMERS/
    (A.TKTS_WITH_NO_CUSTOMER + C.CAFE_ONLY_CUSTOMERS)* 100,0),0) CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.WALLET_AMOUNT,
    COALESCE(B.LWSD_WALLET_AMOUNT, 0) LWSD_WALLET_AMOUNT,
    A.WALLET_REDEMPTION,
    COALESCE(B.LWSD_WALLET_REDEMPTION, 0) LWSD_WALLET_REDEMPTION,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    #A.DONATION,
    #B.LWSD_DONATION,
    A.GIFT_BOX_QUANTITY,
    B.LWSD_GIFT_BOX_QUANTITY
    FROM
    (SELECT 
        m.BIZ_DATE,
            (m.NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.WALLET_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(m.WALLET_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.NET_APP_TICKETS,
            m.NET_APP_SALES,
            COALESCE(m.NET_APP_APC, 0) NET_APP_APC,
            m.Chaayos_NET_DELIVERY_TICKETS,
            m.Chaayos_NET_DELIVERY_SALES,
            COALESCE(m.Chaayos_NET_DELIVERY_APC, 0) Chaayos_NET_DELIVERY_APC,
            m.GnT_NET_DELIVERY_TICKETS,
            m.GnT_NET_DELIVERY_SALES,
            COALESCE(m.GnT_NET_DELIVERY_APC, 0) GnT_NET_DELIVERY_APC,
            m.TKTS_WITH_NO_CUSTOMER,
            COALESCE(m.WALLET_AMOUNT, 0) WALLET_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.WALLET_REDEMPTION, 0) WALLET_REDEMPTION,
            COALESCE(m.WALLET_TICKETS, 0) WALLET_TICKETS,
            j.COLD_PENETRATION,
            COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_APC,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.Chaayos_NET_DELIVERY_TICKETS,
            od.Chaayos_NET_DELIVERY_SALES,
            od.Chaayos_NET_DELIVERY_APC,
            od.GnT_NET_DELIVERY_TICKETS,
            od.GnT_NET_DELIVERY_SALES,
            od.GnT_NET_DELIVERY_APC,
            od.NET_APP_TICKETS,
            od.NET_APP_SALES,
            od.NET_APP_APC,
            od.TKTS_WITH_NO_CUSTOMER,
            od.WALLET_TICKETS,
            od.WALLET_AMOUNT
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN 1
                ELSE 0
            END) AS WALLET_TICKETS,
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS WALLET_AMOUNT,
            
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID <> 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS Chaayos_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID <> 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS Chaayos_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID <> 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID <> 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS Chaayos_NET_DELIVERY_APC,
            
            
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS GnT_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID = 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS GnT_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID = 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS GnT_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14)
                THEN
                    1
                ELSE 0
            END) AS NET_APP_TICKETS,
            SUM(CASE
                WHEN od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14) THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_APP_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14) THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14)
                THEN
                    1
                ELSE 0
            END), 0) AS NET_APP_APC,
            (SUM(CASE
                WHEN
                    od.ORDER_SOURCE <> 'COD'
                        AND od.CUSTOMER_ID <= 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) AS TKTS_WITH_NO_CUSTOMER
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) od) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692 , 1143, 1144, 1219, 1303, 1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN(730,840,850,1051,1057,1129,1154,1157,1158,1164,1165,1166,1168,1169,1170,1171,1230,1237,1283,1289,1291,1303,1304,1305,1306,1354,1355,1356,1357,1362,1363,1369,1370,1371,1372,1373,1374,1399,1450,1453,1456,1457,1458,1459,1470,1484,1485,1557,1558,1559,1560,1564,1634,1644,1645,1646,1000000,1000001,1000008,1000009,1000010,1000011,1000012,1000013,1000014,1000015,1000016,1000017,1000018,1000019,1000020,1000021,1000022,1000023,1000024,1000030)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.WALLET_REDEMPTION, 2) WALLET_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) WALLET_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            (m.LWSD_NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.WALLET_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(m.WALLET_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(m.WALLET_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            m.LWSD_NET_APP_TICKETS,
            m.LWSD_NET_APP_SALES,
            COALESCE(m.LWSD_NET_APP_APC, 0) LWSD_NET_APP_APC,
            m.Chaayos_LWSD_NET_DELIVERY_TICKETS,
            m.Chaayos_LWSD_NET_DELIVERY_SALES,
            COALESCE(m.Chaayos_LWSD_NET_DELIVERY_APC, 0) Chaayos_LWSD_NET_DELIVERY_APC,
            
            m.GnT_LWSD_NET_DELIVERY_TICKETS,
            m.GnT_LWSD_NET_DELIVERY_SALES,
            COALESCE(m.GnT_LWSD_NET_DELIVERY_APC, 0) GnT_LWSD_NET_DELIVERY_APC,
            COALESCE(m.WALLET_AMOUNT, 0) LWSD_WALLET_AMOUNT,
            COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.WALLET_REDEMPTION, 0) LWSD_WALLET_REDEMPTION,
            COALESCE(m.WALLET_TICKETS, 0) LWSD_WALLET_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
        
        SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN 1
                ELSE 0
            END) AS WALLET_TICKETS,
            SUM(CASE
                WHEN od.IS_GIFT_CARD_ORDER = 'Y' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS WALLET_AMOUNT,
            
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC,
            
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID <> 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS Chaayos_LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID <> 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS Chaayos_LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID <> 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID <> 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS Chaayos_LWSD_NET_DELIVERY_APC,
            
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS GnT_LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID = 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS GnT_LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' AND od.BRAND_ID = 3 THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 3 
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS GnT_LWSD_NET_DELIVERY_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14)
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_APP_TICKETS,
            SUM(CASE
                WHEN od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14) THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_APP_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14) THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'  AND od.BRAND_ID = 1 AND od.CHANNEL_PARTNER_ID IN (21,14)
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_APP_APC
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) ) m #LWSD
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) ) qa #LWSD
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692 , 1143, 1144, 1219, 1303, 1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) #LWSD
            AND oi.PRODUCT_ID IN(730,840,850,1051,1057,1129,1154,1157,1158,1164,1165,1166,1168,1169,1170,1171,1230,1237,1283,1289,1291,1303,1304,1305,1306,1354,1355,1356,1357,1362,1363,1369,1370,1371,1372,1373,1374,1399,1450,1453,1456,1457,1458,1459,1470,1484,1485,1557,1558,1559,1560,1564,1634,1644,1645,1646,1000000,1000001,1000008,1000009,1000010,1000011,1000012,1000013,1000014,1000015,1000016,1000017,1000018,1000019,1000020,1000021,1000022,1000023,1000024,1000030)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.WALLET_REDEMPTION, 2) WALLET_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) #LWSD
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) ) a #LWSD
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) WALLET_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) #LWSD
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) B ON A.BIZ_DATE = B.BIZ_DATE
    LEFT OUTER JOIN 
     (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            count(distinct CUSTOMER_ID) CAFE_ONLY_CUSTOMERS
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011, 26097, 26098, 26119, 26254)
            AND od.ORDER_SOURCE <> 'COD'
            AND od.ORDER_TYPE = 'order'
			AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ELSE CURRENT_DATE END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY) #LWSD
        ) C ON A.BIZ_DATE= C.BIZ_DATE;