<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_oOJ6sFpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_oOJ6sVpBEeaY1-o0LnAsFA" bindingContexts="_oOJ6s1pBEeaY1-o0LnAsFA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_oOJ6sVpBEeaY1-o0LnAsFA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_okQGglpBEeaY1-o0LnAsFA" label="%trimmedwindow.label.eclipseSDK" x="156" y="156" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_okQGglpBEeaY1-o0LnAsFA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_okQtkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_o-MQgFpBEeaY1-o0LnAsFA">
        <children xsi:type="advanced:Perspective" xmi:id="_o-MQgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.j2ee.J2EEPerspective" selectedElement="_o-MQgVpBEeaY1-o0LnAsFA" label="Java EE" iconURI="platform:/plugin/org.eclipse.jst.j2ee.ui/icons/full/cview16/j2ee_perspective.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.jst.j2ee.J2eeMainActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.wst.server.ui.ServersView</tags>
          <tags>persp.viewSC:org.eclipse.datatools.connectivity.DataSourceExplorerNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.wst.common.snippets.internal.ui.SnippetsView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.AllMarkersView</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.jpa.ui.wizard.newJpaProject</tags>
          <tags>persp.perspSC:org.eclipse.jpt.ui.jpaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.web.ui.webDevPerspective</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.EarProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.project.facet.WebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.project.facet.EjbProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.jca.ui.internal.wizard.ConnectorProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.appclient.AppClientProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.web.ui.internal.wizards.SimpleWebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newJpaProject</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.internal.wizard.AddServletWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddSessionBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddMessageDrivenBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newEntity</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ws.creation.ui.wizard.serverwizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.actionSet:org.eclipse.wst.server.ui.internal.webbrowser.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.actionSet:org.eclipse.wst.ws.explorer.explorer</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_o-MQgVpBEeaY1-o0LnAsFA" selectedElement="_o-MQglpBEeaY1-o0LnAsFA" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_o-MQglpBEeaY1-o0LnAsFA" elementId="topLeft" containerData="2500" selectedElement="_o-MQg1pBEeaY1-o0LnAsFA">
              <children xsi:type="advanced:Placeholder" xmi:id="_o-MQg1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_o8Q94FpBEeaY1-o0LnAsFA"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_o-MQhFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_o8TaIFpBEeaY1-o0LnAsFA"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_o-MQhVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_o8TaIVpBEeaY1-o0LnAsFA"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_o-MQhlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.PackagesView" toBeRendered="false" ref="_o9-OEFpBEeaY1-o0LnAsFA"/>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_o-MQh1pBEeaY1-o0LnAsFA" containerData="7500">
              <children xsi:type="basic:PartSashContainer" xmi:id="_o-MQiFpBEeaY1-o0LnAsFA" containerData="7000" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQiVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editorss" containerData="7000" ref="_o8DigFpBEeaY1-o0LnAsFA"/>
                <children xsi:type="basic:PartStack" xmi:id="_o-MQilpBEeaY1-o0LnAsFA" elementId="topRight" containerData="3000" selectedElement="_o-MQi1pBEeaY1-o0LnAsFA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_o-MQi1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ContentOutline" ref="_o-Gw81pBEeaY1-o0LnAsFA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_o-MQjFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_o-HYAFpBEeaY1-o0LnAsFA"/>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_o-MQjVpBEeaY1-o0LnAsFA" elementId="bottomRight" containerData="3000" selectedElement="_o-MQjlpBEeaY1-o0LnAsFA">
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQjlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.AllMarkersView" ref="_o9-1IFpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQj1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.PropertySheet" ref="_o9_cMFpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui.ServersView" ref="_o-ADQFpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQkVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" ref="_o-BRYFpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQklpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" ref="_o-E7wFpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQk1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ProblemView" toBeRendered="false" ref="_o-GJ4FpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQlFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_o-GJ4VpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQlVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_o-GJ4lpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQllpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_o-Gw8FpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQl1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_o-Gw8VpBEeaY1-o0LnAsFA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_o-MQmFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_o-Gw8lpBEeaY1-o0LnAsFA"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_okQtkVpBEeaY1-o0LnAsFA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <tags>active</tags>
        <children xsi:type="advanced:Placeholder" xmi:id="_okQtklpBEeaY1-o0LnAsFA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_okNDMFpBEeaY1-o0LnAsFA"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_okQtk1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_okQGgFpBEeaY1-o0LnAsFA"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_okQtlFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_okQGgVpBEeaY1-o0LnAsFA"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_okNDMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_okQGgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_pdNu0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_pdNu0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_okQGgVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_o8DigFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_o8DigVpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o8Q94FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_pCp5wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_pCp5wVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o8TaIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o8TaIVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o9-OEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.PackagesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java Browsing</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o9-1IFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.AllMarkersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.allSeverityField&quot; categoryGroup=&quot;org.eclipse.ui.ide.type&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.allMarkersGenerator&quot; partName=&quot;Markers&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.allSeverityField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.allSeverityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_pLYX8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.AllMarkersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_pLYX8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.AllMarkersView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o9_cMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-ADQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-BRYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Data Management</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-E7wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-GJ4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-GJ4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-GJ4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-Gw8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-Gw8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-Gw8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-Gw81pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_pITOwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_pITOwVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_o-HYAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <trimBars xmi:id="_okcTwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_ovS44FpBEeaY1-o0LnAsFA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ovS44VpBEeaY1-o0LnAsFA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovUHAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_oviwgFpBEeaY1-o0LnAsFA" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" enabled="false" command="_oO6vv1pBEeaY1-o0LnAsFA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovUuEFpBEeaY1-o0LnAsFA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ovUuEVpBEeaY1-o0LnAsFA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_o_8kAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_o_TDwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_o_ssYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_o_ZxcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_o_0oMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.ws.explorer.explorer">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovUuElpBEeaY1-o0LnAsFA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ovUuE1pBEeaY1-o0LnAsFA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovVVIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ovlMwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_oO5hmlpBEeaY1-o0LnAsFA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovVVIVpBEeaY1-o0LnAsFA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ovVVIlpBEeaY1-o0LnAsFA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovVVI1pBEeaY1-o0LnAsFA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ovVVJFpBEeaY1-o0LnAsFA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ovV8MFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.workbench.help">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_o4lXwFpBEeaY1-o0LnAsFA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_o4ml4FpBEeaY1-o0LnAsFA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_o4w98FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_o43roFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_o5DR0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_o5UXkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_o5ldUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.trim.vertical1" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_pf098FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_o5mrcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <bindingTables xmi:id="_oOJ6slpBEeaY1-o0LnAsFA" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_oOJ6s1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP3K4VpBEeaY1-o0LnAsFA" keySequence="CTRL+A" command="_oO0CMlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3x8VpBEeaY1-o0LnAsFA" keySequence="CTRL+SPACE" command="_oO6IpFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP4ZA1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+SPACE" command="_oOy0AVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP8DYVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+L" command="_oO7W6lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP8DZFpBEeaY1-o0LnAsFA" keySequence="CTRL+V" command="_oOwXvlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP9RgFpBEeaY1-o0LnAsFA" keySequence="CTRL+1" command="_oOyM8FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQGbcVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+D" command="_oO7981pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQIQpVpBEeaY1-o0LnAsFA" keySequence="SHIFT+INSERT" command="_oOwXvlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs5VpBEeaY1-o0LnAsFA" keySequence="ALT+PAGE_DOWN" command="_oO4TgVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQLT81pBEeaY1-o0LnAsFA" keySequence="CTRL+C" command="_oO13PVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQLT9lpBEeaY1-o0LnAsFA" keySequence="ALT+PAGE_UP" command="_oO1QJ1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7BFpBEeaY1-o0LnAsFA" keySequence="CTRL+Y" command="_oO0pOFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRallpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+F3" command="_oO6vyVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSBo1pBEeaY1-o0LnAsFA" keySequence="CTRL+X" command="_oOza_VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPxVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+I" command="_oOyM01pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPxlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+F1" command="_oOxl1VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQT20FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+F2" command="_oO3sf1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQT21VpBEeaY1-o0LnAsFA" keySequence="CTRL+INSERT" command="_oO13PVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhN1pBEeaY1-o0LnAsFA" keySequence="CTRL+Z" command="_oOza9VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvUlpBEeaY1-o0LnAsFA" keySequence="CTRL+F10" command="_oOw-vFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbyo1pBEeaY1-o0LnAsFA" keySequence="SHIFT+DEL" command="_oOza_VpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPpIcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_oPA2UVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPvPEFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+C" command="_oO13U1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP0HlFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_oOw-z1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP0uolpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+B" command="_oO8lAlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP1VsVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_UP" command="_oO1QJFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP1VtFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_oOxl01pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3x81pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_UP" command="_oO0pHFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCKAlpBEeaY1-o0LnAsFA" keySequence="CTRL+/" command="_oO13U1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQDYIFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+U" command="_oO5hmVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQFNUFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+/" command="_oO0pMFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQJewlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F" command="_oO791lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7B1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+M" command="_oOyz5VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMclpBEeaY1-o0LnAsFA" keySequence="CTRL+T" command="_oO2eRlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQUd4FpBEeaY1-o0LnAsFA" keySequence="CTRL+I" command="_oOzbD1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVE9FpBEeaY1-o0LnAsFA" keySequence="CTRL+O" command="_oO0CM1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6I1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+P" command="_oO4TklpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhOVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+O" command="_oOza8FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvVFpBEeaY1-o0LnAsFA" keySequence="CTRL+F3" command="_oO7-CVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakgFpBEeaY1-o0LnAsFA" keySequence="CTRL+7" command="_oO13U1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakhFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+\" command="_oOw-1VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZtFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_oOzbGFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZt1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_oO0CNVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQh5QlpBEeaY1-o0LnAsFA" keySequence="CTRL+2 L" command="_oOw-x1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQigUlpBEeaY1-o0LnAsFA" keySequence="CTRL+2 F" command="_oO7991pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQjucFpBEeaY1-o0LnAsFA" keySequence="CTRL+2 R" command="_oO5hrFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQjuclpBEeaY1-o0LnAsFA" keySequence="CTRL+2 M" command="_oOzbHVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPwdMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_oPA2fFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPwdMVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+C" command="_oO13U1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCKA1pBEeaY1-o0LnAsFA" keySequence="CTRL+/" command="_oO13U1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakgVpBEeaY1-o0LnAsFA" keySequence="CTRL+7" command="_oO13U1pBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPwdMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_oPAPQFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPxEQFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+C" command="_oO5hn1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oPzggFpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_RIGHT" command="_oO7951pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQIQpFpBEeaY1-o0LnAsFA" keySequence="SHIFT+INSERT" command="_oO0CAFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlY1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+V" command="_oO0CAFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQT21FpBEeaY1-o0LnAsFA" keySequence="CTRL+INSERT" command="_oO5hn1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbLkVpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_UP" command="_oOvwkVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPxEQVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_oPAPQ1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPxEQlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+C" command="_oO2eYVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP0uoFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_oO4TcFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP1VslpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_UP" command="_oO8lG1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP18wlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_oO5hl1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP4ZAVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_UP" command="_oOwXyVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCKBFpBEeaY1-o0LnAsFA" keySequence="CTRL+/" command="_oO2eYVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQDYJFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+U" command="_oOza-lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQFNUVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+/" command="_oO0pI1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQGbcFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+E" command="_oO1QPFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQJexVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F" command="_oOw-ylpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7AlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+T" command="_oO0pJlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQMiEVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+M" command="_oOw-tFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMc1pBEeaY1-o0LnAsFA" keySequence="CTRL+T" command="_oO3FZlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQUd4VpBEeaY1-o0LnAsFA" keySequence="CTRL+I" command="_oO0CHFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVE-VpBEeaY1-o0LnAsFA" keySequence="CTRL+O" command="_oOwXplpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6JVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+P" command="_oO3salpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIQFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+O" command="_oOzbI1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvVVpBEeaY1-o0LnAsFA" keySequence="CTRL+F3" command="_oO13QlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakglpBEeaY1-o0LnAsFA" keySequence="CTRL+7" command="_oO2eYVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakhVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+\" command="_oOxlxVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZtVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_oO792FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdAwFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_oOxl51pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQh5Q1pBEeaY1-o0LnAsFA" keySequence="CTRL+2 L" command="_oO7-ClpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQigU1pBEeaY1-o0LnAsFA" keySequence="CTRL+2 F" command="_oO0CAlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQjucVpBEeaY1-o0LnAsFA" keySequence="CTRL+2 R" command="_oOyz6FpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPxEQ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_oPA2U1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPxrUFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+C" command="_oO791FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP0uoVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_oO3FbFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP1Vs1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_UP" command="_oOxl9lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP18w1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_oO46jFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP18xVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+A" command="_oO8lE1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP4ZAlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_UP" command="_oO4Th1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQFNUlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+/" command="_oO13T1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF0FpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F" command="_oO8k71pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQUd4lpBEeaY1-o0LnAsFA" keySequence="CTRL+I" command="_oO6vwlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsAFpBEeaY1-o0LnAsFA" keySequence="CTRL+O" command="_oO2eVVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6JlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+P" command="_oO2eSFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhNFpBEeaY1-o0LnAsFA" keySequence="F3" command="_oO2eRVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakhlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+\" command="_oO3sflpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakh1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+>" command="_oO6Iv1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZtlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_oOw-sFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdAwVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_oO0pJFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPxrUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.contexts.window" bindingContext="_oOJ6tFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPxrUlpBEeaY1-o0LnAsFA" keySequence="CTRL+B" command="_oOwXxlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oPxrU1pBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_oO2eYlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP0HkFpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_RIGHT" command="_oOzbEVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP18xFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+A" command="_oO4Tf1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP2j0FpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_oO3FZFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP2j0VpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+A" command="_oO4Tj1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP2j0lpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_LEFT" command="_oOw-wFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP2j01pBEeaY1-o0LnAsFA" keySequence="ALT+C" command="_oOy0ElpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP4ZBVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D R" command="_oO0pOVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5AEFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D J" command="_oO5hlFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5AEVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D Q" command="_oO0pHVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5AElpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D A" command="_oO6IvlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5nIFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D E" command="_oO8lA1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5nIVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D X" command="_oO0pLFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5nIlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D O" command="_oO13Q1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP5nI1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D T" command="_oOvwn1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP6OMFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+D P" command="_oO6vulpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP6ONVpBEeaY1-o0LnAsFA" keySequence="SHIFT+F2" command="_oO3FaVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP61QVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+G" command="_oO3FcFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP61QlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Z" command="_oO1QPVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP7cUFpBEeaY1-o0LnAsFA" keySequence="CTRL+F11" command="_oO7W1lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP7cU1pBEeaY1-o0LnAsFA" keySequence="F12" command="_oO6Ip1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP8DZVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+U" command="_oOxl5lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP-foFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+N" command="_oO13UVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP_twFpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+M" command="_oO796lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP_twVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+F7" command="_oO46gFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP_twlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F4" command="_oOza-1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP_tw1pBEeaY1-o0LnAsFA" keySequence="CTRL+," command="_oOwXw1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQAU0FpBEeaY1-o0LnAsFA" keySequence="F11" command="_oO796FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQA74FpBEeaY1-o0LnAsFA" keySequence="CTRL+P" command="_oO6vv1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQBi8FpBEeaY1-o0LnAsFA" keySequence="CTRL+{" command="_oOy0BVpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQBi8VpBEeaY1-o0LnAsFA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_oQBi8lpBEeaY1-o0LnAsFA" keySequence="CTRL+S" command="_oO0CD1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCKAFpBEeaY1-o0LnAsFA" keySequence="DEL" command="_oOxlx1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQDYJVpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+P" command="_oOw-51pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQD_MVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+M" command="_oO79-VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQD_M1pBEeaY1-o0LnAsFA" keySequence="F4" command="_oOwXzVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQEmQlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+T" command="_oO2eSVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQEmRFpBEeaY1-o0LnAsFA" keySequence="ALT+F7" command="_oO2eT1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQEmRVpBEeaY1-o0LnAsFA" keySequence="CTRL+F12" command="_oOxl0VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQFNU1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+V" command="_oO0CGlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQF0YlpBEeaY1-o0LnAsFA" keySequence="CTRL+F4" command="_oO1QIlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHCgFpBEeaY1-o0LnAsFA" keySequence="CTRL+E" command="_oOy0G1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHCgVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+E" command="_oOxl4FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHCglpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+F" command="_oO7WwlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHpkFpBEeaY1-o0LnAsFA" keySequence="CTRL+F" command="_oOw-3lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHpkVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+I" command="_oOw-vlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHplVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+H" command="_oO13MVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQIQoVpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+H" command="_oOw-slpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQI3sFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+H" command="_oOy0FFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQI3sVpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+G" command="_oO3sfFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQI3slpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+G" command="_oO8k7lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF1FpBEeaY1-o0LnAsFA" keySequence="CTRL+G" command="_oOvwk1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs4VpBEeaY1-o0LnAsFA" keySequence="CTRL+H" command="_oO6Io1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQLT9FpBEeaY1-o0LnAsFA" keySequence="CTRL+#" command="_oOw-vVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7AFpBEeaY1-o0LnAsFA" keySequence="CTRL+-" command="_oO7W7FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7AVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+T" command="_oOza_FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7A1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+W" command="_oOza-1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNJIFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+P" command="_oO3sg1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNwMlpBEeaY1-o0LnAsFA" keySequence="CTRL+." command="_oO7-C1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNwM1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+S" command="_oO3saFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQOXQFpBEeaY1-o0LnAsFA" keySequence="CTRL+U" command="_oO0pL1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQOXQlpBEeaY1-o0LnAsFA" keySequence="CTRL+F6" command="_oOxlwFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlYlpBEeaY1-o0LnAsFA" keySequence="CTRL+W" command="_oO1QIlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlZFpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+T" command="_oOw-u1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlZVpBEeaY1-o0LnAsFA" keySequence="F2" command="_oOwXxVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMdFpBEeaY1-o0LnAsFA" keySequence="ALT+F5" command="_oO0CKVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMdVpBEeaY1-o0LnAsFA" keySequence="ALT+V" command="_oO0CN1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQzgFpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+F12" command="_oO79-1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQzgVpBEeaY1-o0LnAsFA" keySequence="ALT+-" command="_oO13UFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQzglpBEeaY1-o0LnAsFA" keySequence="ALT+X" command="_oO1QO1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRakVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+R" command="_oO0pPVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSosVpBEeaY1-o0LnAsFA" keySequence="ALT+CR" command="_oO5hqVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSos1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_oOy0B1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPwVpBEeaY1-o0LnAsFA" keySequence="CTRL+Q" command="_oO6vz1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQT20VpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_oO3sZ1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQUd41pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+J" command="_oOy0GFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQUd51pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+O" command="_oO797lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsA1pBEeaY1-o0LnAsFA" keySequence="F5" command="_oOzbGlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsBFpBEeaY1-o0LnAsFA" keySequence="CTRL+3" command="_oOyz41pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsBVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F7" command="_oO7961pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsBlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F8" command="_oOy0BFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQWTEFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+R" command="_oO8lGFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQWTElpBEeaY1-o0LnAsFA" keySequence="CTRL+F8" command="_oOyM8lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6IFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F9" command="_oO0CJlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6IVpBEeaY1-o0LnAsFA" keySequence="CTRL+BREAK" command="_oOw-zlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhMlpBEeaY1-o0LnAsFA" keySequence="F3" command="_oOyM6lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIQVpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+T" command="_oO13OVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIRFpBEeaY1-o0LnAsFA" keySequence="CTRL+F9" command="_oOxl5FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIRVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+L" command="_oOyM7FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvUFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F6" command="_oO3sclpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvUVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+W" command="_oO7-BlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZWYVpBEeaY1-o0LnAsFA" keySequence="CTRL+M" command="_oO5huVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZWYlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+S" command="_oO1QNlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZ9cFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F12" command="_oOvwrlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZ9cVpBEeaY1-o0LnAsFA" keySequence="SHIFT+F5" command="_oO0pF1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZ9clpBEeaY1-o0LnAsFA" keySequence="CTRL+F7" command="_oO13PlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZ9dFpBEeaY1-o0LnAsFA" keySequence="CTRL+N" command="_oO8k81pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZ9dVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+N" command="_oOza9lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbLk1pBEeaY1-o0LnAsFA" keySequence="CTRL+_" command="_oOy0BVpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQbLlFpBEeaY1-o0LnAsFA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_oQbLlVpBEeaY1-o0LnAsFA" keySequence="CTRL+DEL" command="_oO7W8FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbyoVpBEeaY1-o0LnAsFA" keySequence="CTRL+=" command="_oOzbFFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbyolpBEeaY1-o0LnAsFA" keySequence="SHIFT+DEL" command="_oOyz_lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbypFpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_oO8k-lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbypVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+B" command="_oOxlzlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZsFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+C" command="_oO6IqlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdAwlpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+B" command="_oO4TelpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdAxFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X Q" command="_oOyM0FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdAxVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X J" command="_oO4ThVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdn0FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X T" command="_oO0CL1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdn0VpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X E" command="_oO3se1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdn0lpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q P" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQdn01pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_oQdn1VpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X X" command="_oO13VVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQeO4FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q L" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQeO4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_oQeO4lpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q T" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQeO41pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_oQeO5VpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X P" command="_oO7-AVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQe18FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q C" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQe18VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_oQe18lpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q O" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQe181pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_oQe19FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q B" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQe19VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_oQe19lpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q D" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQe191pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_oQfdAVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q K" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQfdAlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_oQfdA1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q H" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQfdBFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_oQgEEFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q J" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQgrIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_oQgrIlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q Q" command="_oO4TclpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQgrI1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X A" command="_oOwXwlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQhSMFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X M" command="_oO0COFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQhSMVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q Y" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQhSMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_oQhSM1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X R" command="_oO0pNlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQh5QFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q X" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQh5QVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_oQh5RFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q Z" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQh5RVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_oQigUFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q S" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQigUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_oQjHYFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q V" command="_oO4TclpBEeaY1-o0LnAsFA">
      <parameters xmi:id="_oQjHYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_oQjHYlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+X O" command="_oO3FalpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPxrVFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" bindingContext="_oPAPR1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPySYFpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+D" command="_oOy0F1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP2j1FpBEeaY1-o0LnAsFA" keySequence="ALT+C" command="_oOyz7lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP9RgVpBEeaY1-o0LnAsFA" keySequence="ALT+Q" command="_oO6vt1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCKAVpBEeaY1-o0LnAsFA" keySequence="CTRL+/" command="_oOvwqFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQF0YFpBEeaY1-o0LnAsFA" keySequence="ALT+S" command="_oO0pEVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQO-UlpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+R" command="_oO7W1VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQzg1pBEeaY1-o0LnAsFA" keySequence="ALT+X" command="_oO8lBVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6IlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+P" command="_oO5hkVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvVlpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+X" command="_oO0pPFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPySYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.serverViewScope" bindingContext="_oPA2alpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPySYlpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+D" command="_oO4TgFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQD_MFpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+P" command="_oO46hVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQO-U1pBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+R" command="_oO7-DlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIQlpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+S" command="_oOyM41pBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oPySY1pBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_oPA2cVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oPySZFpBEeaY1-o0LnAsFA" keySequence="ALT+D" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3K4FpBEeaY1-o0LnAsFA" keySequence="ALT+C" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3K5FpBEeaY1-o0LnAsFA" keySequence="ALT+B" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQF0YVpBEeaY1-o0LnAsFA" keySequence="ALT+S" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQGbdFpBEeaY1-o0LnAsFA" keySequence="ALT+G" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHChFpBEeaY1-o0LnAsFA" keySequence="ALT+H" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs5lpBEeaY1-o0LnAsFA" keySequence="ALT+F" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQLT9VpBEeaY1-o0LnAsFA" keySequence="ALT+E" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNwMFpBEeaY1-o0LnAsFA" keySequence="ALT+N" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQO-UFpBEeaY1-o0LnAsFA" keySequence="ALT+L" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMcFpBEeaY1-o0LnAsFA" keySequence="ALT+R" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMdlpBEeaY1-o0LnAsFA" keySequence="ALT+V" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsAVpBEeaY1-o0LnAsFA" keySequence="ALT+P" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIQ1pBEeaY1-o0LnAsFA" keySequence="ALT+W" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIR1pBEeaY1-o0LnAsFA" keySequence="ALT+T" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZWYFpBEeaY1-o0LnAsFA" keySequence="ALT+Y" command="_oO13TVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakiFpBEeaY1-o0LnAsFA" keySequence="ALT+A" command="_oO13TVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP0HkVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_oPAPQlpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP0HklpBEeaY1-o0LnAsFA" keySequence="CTRL+ARROW_DOWN" command="_oO8lElpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP0Hk1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_oO0CG1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3K4lpBEeaY1-o0LnAsFA" keySequence="CTRL+ARROW_UP" command="_oOxl0lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3K41pBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+ARROW_UP" command="_oO7-B1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3x8FpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_DOWN" command="_oO4TjFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3x8lpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+DEL" command="_oO6Iq1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP4ZBFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+A" command="_oO2eR1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP7cVFpBEeaY1-o0LnAsFA" keySequence="CTRL+NUMPAD_DIVIDE" command="_oOxl11pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP8DYFpBEeaY1-o0LnAsFA" keySequence="SHIFT+CR" command="_oO7911pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCxFFpBEeaY1-o0LnAsFA" keySequence="INSERT" command="_oO3FU1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQF0Y1pBEeaY1-o0LnAsFA" keySequence="HOME" command="_oO792VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHCg1pBEeaY1-o0LnAsFA" keySequence="SHIFT+HOME" command="_oOzbIVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF0VpBEeaY1-o0LnAsFA" keySequence="SHIFT+END" command="_oO0CGFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF0lpBEeaY1-o0LnAsFA" keySequence="CTRL+HOME" command="_oOwXvVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF1lpBEeaY1-o0LnAsFA" keySequence="CTRL+BS" command="_oOvwklpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF11pBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+J" command="_oOyM6FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs4FpBEeaY1-o0LnAsFA" keySequence="CTRL+END" command="_oO4TkVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs4lpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_oOxl7lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs41pBEeaY1-o0LnAsFA" keySequence="CTRL+D" command="_oOw-01pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKs5FpBEeaY1-o0LnAsFA" keySequence="CTRL+ARROW_RIGHT" command="_oOyM2FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQLT91pBEeaY1-o0LnAsFA" keySequence="CTRL+ARROW_LEFT" command="_oO13OFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQMiElpBEeaY1-o0LnAsFA" keySequence="CTRL+NUMPAD_ADD" command="_oO79-FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQMiE1pBEeaY1-o0LnAsFA" keySequence="ALT+/" command="_oO793VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNJIVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+K" command="_oOxlzVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNJIlpBEeaY1-o0LnAsFA" keySequence="CTRL+L" command="_oO6Iu1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNwMVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+X" command="_oO13RlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQOXQVpBEeaY1-o0LnAsFA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_oO46kFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlZlpBEeaY1-o0LnAsFA" keySequence="F2" command="_oOyz5FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSoslpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+Q" command="_oOyM21pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSotFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_oO3FVVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPwFpBEeaY1-o0LnAsFA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_oO6vx1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPwlpBEeaY1-o0LnAsFA" keySequence="CTRL+J" command="_oOw-w1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPw1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+CR" command="_oO6vyFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQTPxFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+INSERT" command="_oOxl6FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQT20lpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_oO46mFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQT201pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+J" command="_oOxl-FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVE81pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+Y" command="_oO0CCFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQWTEVpBEeaY1-o0LnAsFA" keySequence="END" command="_oO7W0FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQWTE1pBEeaY1-o0LnAsFA" keySequence="CTRL+K" command="_oO4TfFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYvU1pBEeaY1-o0LnAsFA" keySequence="CTRL+F10" command="_oO6vxFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbLklpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_UP" command="_oO8k_lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbyoFpBEeaY1-o0LnAsFA" keySequence="CTRL+DEL" command="_oOy0HVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdAw1pBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+ARROW_DOWN" command="_oO0pElpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP0uo1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_oPA2UFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP1VsFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+B" command="_oO8lAlpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP18wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_oPA2dVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP18wVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_oO0CFVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP3K5VpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_DOWN" command="_oOy0FlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP4ZAFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+ARROW_UP" command="_oO5hsVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCxEFpBEeaY1-o0LnAsFA" keySequence="CTRL+CR" command="_oOy0EFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCxE1pBEeaY1-o0LnAsFA" keySequence="INSERT" command="_oO0CBVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQDYIVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+U" command="_oO46ilpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQEmQFpBEeaY1-o0LnAsFA" keySequence="F4" command="_oOw-uFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHpklpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+I" command="_oO0CDlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQIQo1pBEeaY1-o0LnAsFA" keySequence="SHIFT+INSERT" command="_oOxlwlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRak1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+R" command="_oO0CGVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQbLkFpBEeaY1-o0LnAsFA" keySequence="ALT+ARROW_UP" command="_oO3sh1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZsVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+C" command="_oO1QNFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP6OMVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_oPA2a1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP6OMlpBEeaY1-o0LnAsFA" keySequence="F8" command="_oO2eYFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP94kFpBEeaY1-o0LnAsFA" keySequence="CTRL+F2" command="_oO6IrlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQCxFVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+3" command="_oOwXzlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQO-UVpBEeaY1-o0LnAsFA" keySequence="F6" command="_oO0CH1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSBoFpBEeaY1-o0LnAsFA" keySequence="CTRL+F5" command="_oO7941pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSBpFpBEeaY1-o0LnAsFA" keySequence="CTRL+R" command="_oO13QVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVsAlpBEeaY1-o0LnAsFA" keySequence="F5" command="_oOwX0lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhMFpBEeaY1-o0LnAsFA" keySequence="F7" command="_oO8k4lpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP6OM1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_oPA2XVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP6ONFpBEeaY1-o0LnAsFA" keySequence="SHIFT+F2" command="_oO2eWVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQJewVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F" command="_oO791lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRakFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+R" command="_oOwXzFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhMVpBEeaY1-o0LnAsFA" keySequence="F3" command="_oOvwolpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhOFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+O" command="_oOvwm1pBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP6ONlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_oPA2eVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP61QFpBEeaY1-o0LnAsFA" keySequence="SHIFT+F2" command="_oO6IslpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP61Q1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Z" command="_oO0CKFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oP8qcFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+U" command="_oOyM11pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQD_MlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+M" command="_oO7W0lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQEmQVpBEeaY1-o0LnAsFA" keySequence="F4" command="_oO0pFlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQEmQ1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+T" command="_oOxlzFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQFNVlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+V" command="_oOyM2lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHplFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+I" command="_oO2eVlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQIQoFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+H" command="_oO8lFVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQIQolpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+H" command="_oO5hslpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQJewFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+G" command="_oO2eTFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF1VpBEeaY1-o0LnAsFA" keySequence="CTRL+G" command="_oO4TfVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRalVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+R" command="_oO3sbVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQUd5FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+J" command="_oO3FZVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVE8lpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+O" command="_oOw-1FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQXhM1pBEeaY1-o0LnAsFA" keySequence="F3" command="_oO46hFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQYIRlpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+L" command="_oO0pJVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZWZFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+S" command="_oO3sY1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZs1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+C" command="_oOyz9lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQdn1FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q P" command="_oO3sdlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQeO5FpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q T" command="_oO3FYVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQfdAFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q D" command="_oO4TjVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQgrIVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+Q J" command="_oO7W5VpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP7cUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_oPA2bFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP7cUlpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+." command="_oO5ht1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQKF01pBEeaY1-o0LnAsFA" keySequence="CTRL+G" command="_oO5huFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQzhFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+," command="_oO7WwVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP8DYlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_oPA2fVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP8DY1pBEeaY1-o0LnAsFA" keySequence="CTRL+V" command="_oO13RFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQLT8FpBEeaY1-o0LnAsFA" keySequence="CTRL+C" command="_oOyz8VpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oP8qcVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_oPAPSFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oP8qclpBEeaY1-o0LnAsFA" keySequence="CTRL+1" command="_oO8k6VpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQAU0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" bindingContext="_oPA2flpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQAU0lpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F5" command="_oO46gFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSBoVpBEeaY1-o0LnAsFA" keySequence="CTRL+F5" command="_oO2eT1pBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQAU01pBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.editorContext" bindingContext="_oPA2YFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQAU1FpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F5" command="_oO46gFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQNJI1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F10" command="_oO7-BVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlYFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F11" command="_oO795lpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSBolpBEeaY1-o0LnAsFA" keySequence="CTRL+F5" command="_oO2eT1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQWTFFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F9" command="_oO0CHlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZWZVpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F12" command="_oO8k5lpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQCxEVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_oPAPQVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQCxElpBEeaY1-o0LnAsFA" keySequence="CTRL+CR" command="_oOw-uVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQSosFpBEeaY1-o0LnAsFA" keySequence="ALT+CR" command="_oO13MFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQDYIlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_oPA2dlpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQDYI1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+U" command="_oO46ilpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQHpk1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+I" command="_oO0CDlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQMiEFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+M" command="_oOwXpVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRalFpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+R" command="_oO0CGVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVE9VpBEeaY1-o0LnAsFA" keySequence="CTRL+O" command="_oO7-A1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZWY1pBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+S" command="_oOzbAlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQcZslpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+C" command="_oO1QNFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQFNVFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_oPA2YlpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQFNVVpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+V" command="_oO0CNFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQL7CFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+M" command="_oOyz9VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQRaklpBEeaY1-o0LnAsFA" keySequence="ALT+SHIFT+R" command="_oO79_1pBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQGbclpBEeaY1-o0LnAsFA" elementId="org.eclipse.core.runtime.xml" bindingContext="_oPA2V1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQGbc1pBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+D" command="_oO0pLlpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQW6JFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+P" command="_oO6IqFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQJew1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_oPA2aFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQJexFpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+F" command="_oOw-zVpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQVE-FpBEeaY1-o0LnAsFA" keySequence="CTRL+O" command="_oOw-5FpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQLT8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_oPA2cFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQLT8lpBEeaY1-o0LnAsFA" keySequence="CTRL+C" command="_oOxlwVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQL7BVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_oPAPRlpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQL7BlpBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+N" command="_oO79_FpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQPlYVpBEeaY1-o0LnAsFA" keySequence="CTRL+W" command="_oO3FW1pBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQQMcVpBEeaY1-o0LnAsFA" keySequence="CTRL+T" command="_oOyz6VpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQZ9c1pBEeaY1-o0LnAsFA" keySequence="CTRL+N" command="_oO3seFpBEeaY1-o0LnAsFA"/>
    <bindings xmi:id="_oQakg1pBEeaY1-o0LnAsFA" keySequence="ALT+CTRL+M" command="_oO0CLFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQUd5VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_oPA2d1pBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQUd5lpBEeaY1-o0LnAsFA" keySequence="F1" command="_oOvwoFpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQVE8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_oPA2elpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQVE8VpBEeaY1-o0LnAsFA" keySequence="CTRL+SHIFT+O" command="_oOwXtVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQVE9lpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_oPA2eFpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQVE91pBEeaY1-o0LnAsFA" keySequence="CTRL+O" command="_oOwXtVpBEeaY1-o0LnAsFA"/>
  </bindingTables>
  <bindingTables xmi:id="_oQXhNVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.console" bindingContext="_oPA2ZVpBEeaY1-o0LnAsFA">
    <bindings xmi:id="_oQXhNlpBEeaY1-o0LnAsFA" keySequence="CTRL+Z" command="_oO7-BFpBEeaY1-o0LnAsFA">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_o8F-wVpBEeaY1-o0LnAsFA" bindingContext="_o8F-wFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8F-w1pBEeaY1-o0LnAsFA" bindingContext="_o8F-wlpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Gl0VpBEeaY1-o0LnAsFA" bindingContext="_o8Gl0FpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Gl01pBEeaY1-o0LnAsFA" bindingContext="_o8Gl0lpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Gl1VpBEeaY1-o0LnAsFA" bindingContext="_o8Gl1FpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Gl11pBEeaY1-o0LnAsFA" bindingContext="_o8Gl1lpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8HM4VpBEeaY1-o0LnAsFA" bindingContext="_o8HM4FpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8HM41pBEeaY1-o0LnAsFA" bindingContext="_o8HM4lpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8HM5VpBEeaY1-o0LnAsFA" bindingContext="_o8HM5FpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8HM51pBEeaY1-o0LnAsFA" bindingContext="_o8HM5lpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Hz8VpBEeaY1-o0LnAsFA" bindingContext="_o8Hz8FpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Hz81pBEeaY1-o0LnAsFA" bindingContext="_o8Hz8lpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Hz9VpBEeaY1-o0LnAsFA" bindingContext="_o8Hz9FpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8IbAFpBEeaY1-o0LnAsFA" bindingContext="_o8Hz9lpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8IbAlpBEeaY1-o0LnAsFA" bindingContext="_o8IbAVpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8IbBFpBEeaY1-o0LnAsFA" bindingContext="_o8IbA1pBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8IbBlpBEeaY1-o0LnAsFA" bindingContext="_o8IbBVpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JCEVpBEeaY1-o0LnAsFA" bindingContext="_o8JCEFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JCE1pBEeaY1-o0LnAsFA" bindingContext="_o8JCElpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JCFVpBEeaY1-o0LnAsFA" bindingContext="_o8JCFFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JCF1pBEeaY1-o0LnAsFA" bindingContext="_o8JCFlpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JpIVpBEeaY1-o0LnAsFA" bindingContext="_o8JpIFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JpI1pBEeaY1-o0LnAsFA" bindingContext="_o8JpIlpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JpJVpBEeaY1-o0LnAsFA" bindingContext="_o8JpJFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8JpJ1pBEeaY1-o0LnAsFA" bindingContext="_o8JpJlpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8KQMVpBEeaY1-o0LnAsFA" bindingContext="_o8KQMFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8KQM1pBEeaY1-o0LnAsFA" bindingContext="_o8KQMlpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8KQNVpBEeaY1-o0LnAsFA" bindingContext="_o8KQNFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8K3QVpBEeaY1-o0LnAsFA" bindingContext="_o8K3QFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8K3Q1pBEeaY1-o0LnAsFA" bindingContext="_o8K3QlpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8LeUFpBEeaY1-o0LnAsFA" bindingContext="_o8K3RFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8LeUlpBEeaY1-o0LnAsFA" bindingContext="_o8LeUVpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8LeVFpBEeaY1-o0LnAsFA" bindingContext="_o8LeU1pBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8MFYFpBEeaY1-o0LnAsFA" bindingContext="_o8LeVVpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8MFYlpBEeaY1-o0LnAsFA" bindingContext="_o8MFYVpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8MFZFpBEeaY1-o0LnAsFA" bindingContext="_o8MFY1pBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8MFZlpBEeaY1-o0LnAsFA" bindingContext="_o8MFZVpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8MscVpBEeaY1-o0LnAsFA" bindingContext="_o8MscFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Msc1pBEeaY1-o0LnAsFA" bindingContext="_o8MsclpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8MsdVpBEeaY1-o0LnAsFA" bindingContext="_o8MsdFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8NTgVpBEeaY1-o0LnAsFA" bindingContext="_o8NTgFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8NTg1pBEeaY1-o0LnAsFA" bindingContext="_o8NTglpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8NThVpBEeaY1-o0LnAsFA" bindingContext="_o8NThFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8N6kVpBEeaY1-o0LnAsFA" bindingContext="_o8N6kFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8N6k1pBEeaY1-o0LnAsFA" bindingContext="_o8N6klpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8N6lVpBEeaY1-o0LnAsFA" bindingContext="_o8N6lFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8OhoVpBEeaY1-o0LnAsFA" bindingContext="_o8OhoFpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8Oho1pBEeaY1-o0LnAsFA" bindingContext="_o8OholpBEeaY1-o0LnAsFA"/>
  <bindingTables xmi:id="_o8PIsVpBEeaY1-o0LnAsFA" bindingContext="_o8PIsFpBEeaY1-o0LnAsFA"/>
  <rootContext xmi:id="_oOJ6s1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_oOJ6tFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_oOJ6tVpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_oPAPQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_oPAPQVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_oPAPQlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_oPAPQ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_oPA2eVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_oPAPR1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" name="Editing SQL" description="Editing SQL Context"/>
        <children xmi:id="_oPAPSFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_oPA2UVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_oPA2U1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_oPA2VFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_oPA2VVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_oPA2VlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_oPA2V1pBEeaY1-o0LnAsFA" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_oPA2WFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_oPA2WVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_oPA2WlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_oPA2XFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_oPA2XlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_oPA2YVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_oPA2YlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_oPA2Z1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_oPA2clpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_oPA2dFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_oPA2e1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_oPA2f1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
        </children>
        <children xmi:id="_oPA2XVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_oPA2YFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.editorContext" name="Using Web Page Editor" description="Key binding context when using the web page editor"/>
        <children xmi:id="_oPA2aFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_oPA2c1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_oPA2dlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_oPA2d1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_oPA2eFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_oPA2elpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_oPA2fFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
        <children xmi:id="_oPA2flpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" name="In Faces Config Editor" description="Key binding context when using the Faces Config Editor"/>
      </children>
      <children xmi:id="_oPAPRFpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_oPAPRlpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_oPA2UlpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.schemaobjecteditor.schemaediting" name="Schema Object Editor" description="Schema Object Editor"/>
      <children xmi:id="_oPA2ZVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_oPA2ZlpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_oPA2aVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_oPA2alpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_oPA2a1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_oPA2bFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_oPA2bVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_oPA2blpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_oPA2cFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_oPA2cVpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_oPA2dVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_oPA2fVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_oOJ6tlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_oPAPRVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_oPA2UFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_oPA2W1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_oPA2X1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_oPA2Y1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_oPA2ZFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_oPA2b1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_o8F-wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_o8F-wlpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.actionSet" name="Auto::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet"/>
  <rootContext xmi:id="_o8Gl0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_o8Gl0lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_o8Gl1FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_o8Gl1lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_o8HM4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_o8HM4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_o8HM5FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_o8HM5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_o8Hz8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_o8Hz8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_o8Hz9FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_o8Hz9lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_o8IbAVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_o8IbA1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_o8IbBVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation" name="Auto::org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation"/>
  <rootContext xmi:id="_o8JCEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_o8JCElpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_o8JCFFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_o8JCFlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_o8JpIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_o8JpIlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_o8JpJFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_o8JpJlpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_o8KQMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_o8KQMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_o8KQNFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_o8K3QFpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_o8K3QlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_o8K3RFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_o8LeUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_o8LeU1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_o8LeVVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_o8MFYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_o8MFY1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_o8MFZVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_o8MscFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_o8MsclpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_o8MsdFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_o8NTgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_o8NTglpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.JavaActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_o8NThFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.A_OpenActionSet" name="Auto::org.eclipse.wst.jsdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_o8N6kFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.CodingActionSet" name="Auto::org.eclipse.wst.jsdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_o8N6klpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.SearchActionSet" name="Auto::org.eclipse.wst.jsdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_o8N6lFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_o8OhoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_o8OholpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_o8PIsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <descriptors xmi:id="_oTBzoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_oTGFEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_oTOn8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_oTQdIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.plan.planView" label="Execution Plan" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.plan/icons/sqlplan.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_oTSSUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.result.resultView" label="SQL Results" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.result.ui/icons/sqlresult.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_oTTgcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTUukFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTVVoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTV8sFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTXx4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTYY8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTZAAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTZnEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_oTbcQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_oTcDUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_oTcqYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_oTd4gFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_oTefkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oTftsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_oTg70FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oTiJ8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_oTjYEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_oTl0UFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_oTmbYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_oTpesFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_oTtJEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_oTuXMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_oTu-QFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_oTvlUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oTwMYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_oTxagFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_oTyBkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.ui.jpaStructureView" label="JPA Structure" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-structure.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_oTzPsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.ui.jpaDetailsView" label="JPA Details" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-details.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_oTz2wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsf.ui.component.ComponentTreeView" label="JSF Component Tree" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_oT1E4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsf.ui.tagregistry.TagRegistryView" label="Tag Registry" iconURI="platform:/plugin/org.eclipse.jst.jsf.ui/icons/obj16/library_obj.gif" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_oT2TAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.ws.jaxws.ui.views.AnnotationsView" label="Annotation Properties" iconURI="platform:/plugin/org.eclipse.jst.ws.jaxws.ui/icons/eview16/prop_ps.gif" tooltip="" category="Web Services" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Web Services</tags>
  </descriptors>
  <descriptors xmi:id="_oT26EFpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.gif" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_oT4vQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_oT5WUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_oT6kcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_oT7ykFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_oT8ZoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_oT9nwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.gif" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_oT-14FpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.gif" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_oUAEAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_oUBSIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_oUCgQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_oUDHUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_oUDuYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.apidocs.rcp.views.apidocs" label="API Docs" iconURI="platform:/plugin/org.eclipse.recommenders.apidocs.rcp/icons/view16/apidocs.png" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_oUE8gFpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.models.rcp.views.projectCoordinates" label="Project Coordinates" iconURI="platform:/plugin/org.eclipse.recommenders.coordinates.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_oUGKoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.models.rcp.views.modelRepositories" label="Model Repositories" iconURI="platform:/plugin/org.eclipse.recommenders.models.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_oUH_0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.models.rcp.views.dependencyOverview" label="Dependency Overview" iconURI="platform:/plugin/org.eclipse.recommenders.models.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_oUIm4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oUJ1AFpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oULDIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oULqMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oUM4UFpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oUNfYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oUOGcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_oUOtgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUP7oFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUQisFpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_oURw0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_oUSX4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_oUTmAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_oUUNEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUUNEVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUVbMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_oUWpUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUX3cFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUYegFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUZFkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUZsoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUaTsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUa6wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUbh0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUcv8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUdXAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUd-EFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUfMMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_oUgaUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_oUhBYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" label="Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/class_hi.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_oUiPgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" label="Script Explorer" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/package.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_oUi2kFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_oUjdoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_oUkEsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" tooltip="JavaScript Documentation" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_oUnIAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_oUoWIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_oUpkQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_oUqLUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_oUrZcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_oUrZcVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_oOuicFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOuicVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvJgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOvJgVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_oOvJglpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwkVpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_oOstT1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwklpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwk1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwlFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInTypeHierarchyView" commandName="Show JavaScript Element Type Hierarchy" description="Show a JavaScript element in the Type Hierarchy view" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOvwlVpBEeaY1-o0LnAsFA" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_oOvwllpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwl1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwmFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwmVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwmlpBEeaY1-o0LnAsFA" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwm1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwnFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwnVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.callhierarchy.view" commandName="JavaScript Call Hierarchy" description="Show the Call Hierarchy view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwnlpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.commands.export" commandName="Export Profiles Command" description="Command to export connection profiles" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwn1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwoVpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwolpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwo1pBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.commands.openBrowserDialog" commandName="Open a Web browser" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOvwpFpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_oOvwpVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwplpBEeaY1-o0LnAsFA" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwp1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_oOstWlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwqFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.toggleCommentAction" commandName="Toggle Comment" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwqVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwqlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwq1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwrFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwrVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwrlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOvwr1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXoVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXolpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXo1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXpFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXpVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_oOsGNVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXplpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXp1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.commands.openElementInEditor" commandName="Open JavaScript Element" description="Open a JavaScript element in its editor" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOwXqFpBEeaY1-o0LnAsFA" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_oOwXqVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.TypesView" commandName="JavaScript Types" description="Show the Types view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXqlpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXq1pBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXrFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXrVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXrlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXr1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOwXsVpBEeaY1-o0LnAsFA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_oOwXslpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXs1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXtFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXtVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXtlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXt1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.MembersView" commandName="JavaScript Members" description="Show the Members view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXuFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXuVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXulpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.commands.openBrowser" commandName="Open a Web browser" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOwXu1pBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_oOwXvFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.discoveryWizardCommand" commandName="Discovery Wizard" description="shows the connector discovery wizard" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXvVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXvlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXv1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Referehs Remote Cache" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXwVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXwlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXw1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXxFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXxVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXxlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXx1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXyFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXyVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXylpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXy1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXzFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXzVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXzlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.debug.ui.script.opensource" commandName="Open Source" description="Shows the JavaScript source for the selected script element" category="_oOstQ1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwXz1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwX0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_oOstV1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwX0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwX0lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOwX01pBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.ws.jaxws.ui.configure.handlers" commandName="Configure Handlers" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-sFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-sVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-slpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-s1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-tFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-tVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOw-tlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_oOw-t1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-uFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-uVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.debug.ui.breakpoint.properties" commandName="JavaScript Breakpoint Properties" description="View and edit the properties for a given JavaScript breakpoint" category="_oOstQ1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-ulpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-u1pBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Terminal" category="_oOstRVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-vFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-vVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-vlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-v1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-wVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-wlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-w1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-xFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-xVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-xlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.newEntity" commandName="JPA Entity" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-x1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-yFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-yVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-ylpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-y1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-zFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-zVpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_oOstX1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-zlpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.result.terminate" commandName="Terminate Result" category="_oOstTVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-z1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() functions for the type" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-0lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-01pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-1FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-1VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-1lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method in its hierarchy" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-11pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-2FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-2VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOw-2lpBEeaY1-o0LnAsFA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_oOw-21pBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-3FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-3VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter functions for type's vars" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-3lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-31pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Search" description="Adds all plug-ins in the target platform to java search" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOw-4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_oOw-41pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-5FpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-5VpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_oOsGMFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOw-51pBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlwVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlwlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlw1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.JavaBrowsingPerspective" commandName="JavaScript Browsing" description="Show the JavaScript Browsing perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlxFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlxVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlxlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlx1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlyFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlyVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlylpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxly1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlzFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlzVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlzlpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxlz1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.StashDrop" commandName="Delete Stashed Commit..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl0lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl01pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl1FpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_oOstQFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl1VpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_oOstXlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl1lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl11pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl2FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl2VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl2lpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_oOstYFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl21pBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl3FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl3VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_oOstRFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOxl3lpBEeaY1-o0LnAsFA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_oOxl31pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl41pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl5FpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl5VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl51pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl6FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl6VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl6lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl61pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl7FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl7VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl7lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl71pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl81pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl9FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl9VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.ProjectsView" commandName="JavaScript Projects" description="Show the Projects view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl9lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl91pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl-FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOxl-VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory function to encapsulate invocation of the selected constructor" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM0lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM01pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM1FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM1VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM1lpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM11pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM2FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM2VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM2lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM21pBEeaY1-o0LnAsFA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM3FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM3VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM3lpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM31pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_oOstQlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM41pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_oOstQVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM5FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_oOstTFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOyM5VpBEeaY1-o0LnAsFA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_oOyM5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM51pBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM6FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM6VpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.saveAsTemplateAction" commandName="Save as Template" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM6lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM61pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM7FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM7VpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_oOstU1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM7lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM71pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM81pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM9FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.StashApply" commandName="Apply Stashed Changes" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyM9VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOyz4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_oOyz4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz41pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz5FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz5VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_oOsGNVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz51pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz6FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz6VpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz6lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz61pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz7FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz7VpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.refreshFromDatabaseAction" commandName="Refresh from Database" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz7lpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteAsOneStatementAction" commandName="Execute Selected Text As One Statement" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz71pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.commands.addrepository" commandName="New Repository Profile Command" description="Command to create a new repository profile" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz81pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.JavaPerspective" commandName="JavaScript" description="Show the JavaScript perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz9FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz9VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz9lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.modify.method.parameters" commandName="Change Function Signature" description="Change function signature includes parameter names and parameter order" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz91pBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.upgradeToEclipseLinkMappingFile" commandName="Upgrade to EclipseLink Mapping File" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz-FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_oOstSFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOyz-VpBEeaY1-o0LnAsFA" elementId="url" name="URL"/>
    <parameters xmi:id="_oOyz-lpBEeaY1-o0LnAsFA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_oOyz-1pBEeaY1-o0LnAsFA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_oOyz_FpBEeaY1-o0LnAsFA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_oOyz_VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static var and uses the new static var" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz_lpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.result.removeAllInstances" commandName="Remove All Visible Results" category="_oOstTVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOyz_1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0AFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0AVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0AlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0A1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0BFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0BVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_oOstSFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOy0BlpBEeaY1-o0LnAsFA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_oOy0B1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0CFpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0CVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0ClpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0C1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0DFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0DVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0DlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0D1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0EFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0EVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0ElpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.copycolumn" commandName="Copy" category="_oOstSVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0E1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0FFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0FVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0FlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0F1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.debugAction" commandName="Debug" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0GFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0GVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0GlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0G1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0HFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jaxb.ui.generateJaxbClasses" commandName="JAXB Classes..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0HVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOy0HlpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza81pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza9FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza9VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza9lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza91pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza-FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza-VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza-lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza-1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza_FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza_VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza_lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOza_1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbAVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbAlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_oOsGNVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbA1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbBFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbBVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.generate.xml" commandName="&amp;XML File..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbBlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbB1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbCFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbCVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_oOstRFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOzbClpBEeaY1-o0LnAsFA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_oOzbC1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="Promotes anonymous type to global level and replaces its references" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbDFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbDVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbDlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbD1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbEVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbElpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbE1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbFFpBEeaY1-o0LnAsFA" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_oOstWFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbFVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbFlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbF1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbGFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbGVpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbGlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbG1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.commands.showcategory" commandName="Show Category" description="Show Category" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbHFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbHVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbHlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbH1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbIVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbIlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbI1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbJFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbJVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbJlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeMapAs" commandName="Map As" category="_oOstXFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oOzbJ1pBEeaY1-o0LnAsFA" elementId="specifiedPersistentAttributeMappingKey" name="specified mapping key" optional="false"/>
    <parameters xmi:id="_oOzbKFpBEeaY1-o0LnAsFA" elementId="defaultPersistentAttributeMappingKey" name="default mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_oOzbKVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oOzbKlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_oOstT1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CAVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CAlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CA1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.convertJavaGenerators" commandName="Move Java Generators to XML..." category="_oOstWVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CBFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CBVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CBlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CB1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CCFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CCVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.commands.addprofile" commandName="New Connection Profile Command" description="Command to create a new connection profile" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO0CClpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.ui.ignoreCategory" name="ignoreCategory"/>
    <parameters xmi:id="_oO0CC1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.ui.useSelection" name="useSelection"/>
  </commands>
  <commands xmi:id="_oO0CDFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CDVpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CDlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CD1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CEVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected function" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CElpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CE1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_oOsGMlpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO0CFFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_oO0CFVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CFlpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_oOsGMFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CF1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.generateEntities" commandName="Generate Entities from Tables..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CGFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CGVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CGlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CG1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CHFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CHVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CHlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.vertical" commandName="Vertical Layout" category="_oOsGM1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CH1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.addToPersistenceUnit" commandName="Add to Persistence Unit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CIVpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CIlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_oOstRFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO0CI1pBEeaY1-o0LnAsFA" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_oO0CJFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CJVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CJlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CJ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CKFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CKVpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Project" description="Update Maven Project configuration and dependencies" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CKlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CK1pBEeaY1-o0LnAsFA" elementId="refresh.schema.editor.action.id" commandName="Refresh from Server" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CLFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CLVpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CLlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CL1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.convertJavaQueries" commandName="Move Java Queries to XML..." category="_oOstWVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CMVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CM1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CNFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CNVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CNlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0CN1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.pastecolumn" commandName="Paste" category="_oOstSVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0COFpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pEVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteCurrentAction" commandName="Execute Current Text" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pElpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pE1pBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pFFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pFVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pFlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pF1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pGFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pGVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pGlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pG1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_oOstXVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pHFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pHVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pHlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pH1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pIVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_oOstSFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO0pIlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_oO0pI1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pJFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pJVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pJlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pJ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_oOstTFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO0pKFpBEeaY1-o0LnAsFA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_oO0pKVpBEeaY1-o0LnAsFA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_oO0pKlpBEeaY1-o0LnAsFA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_oO0pK1pBEeaY1-o0LnAsFA" elementId="revert.schema.editor.action.id" commandName="Revert Object" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pLFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pLVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pLlpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven Dependency" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pL1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pMVpBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_oOstR1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.core.synchronizeClasses" commandName="Synchronize Class List" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pM1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pNFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pNVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pNlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pN1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.inner.to.top.level" commandName="Convert Member Type to Top Level" description="Convert member type to top level" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pOFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pOVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pOlpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pO1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pPFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSQLAction" commandName="Execute All" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pPVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pPlpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove &amp;API Problem Filters..." description="Remove API problem filters for this project" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO0pP1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QIFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QIVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QIlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QI1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QJFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QJVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO1QJlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_oO1QJ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QKFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QKVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO1QKlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_oO1QK1pBEeaY1-o0LnAsFA" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QLFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QLVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_oOstUlpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO1QLlpBEeaY1-o0LnAsFA" elementId="title" name="Title"/>
    <parameters xmi:id="_oO1QL1pBEeaY1-o0LnAsFA" elementId="message" name="Message"/>
    <parameters xmi:id="_oO1QMFpBEeaY1-o0LnAsFA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_oO1QMVpBEeaY1-o0LnAsFA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_oO1QMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QM1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QNFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QNVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QNlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QN1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QOFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QOVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QOlpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QO1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.cutcolumn" commandName="Cut" category="_oOstSVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QPFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.debug.ui.evaluate.command" commandName="Evaluate" description="Evaluates the selected text in the JavaScript editor" category="_oOstQ1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QPVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QPlpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QP1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO1QQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13MFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13MVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13MlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13M1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13NFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13NVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13NlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_oOstWlpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO13N1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_oO13OFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13OVpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_oOstRVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13OlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_oOstSFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO13O1pBEeaY1-o0LnAsFA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_oO13PFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13PVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13PlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13P1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13QFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13QVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13QlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13Q1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13RFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13RVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13RlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13R1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13SFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13SVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13SlpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13S1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13TFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13TVpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_oOstT1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13TlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13T1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13UFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13UVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13UlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13U1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13VFpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13VVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO13VlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eQVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eQlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eQ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eRFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eRVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eRlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eR1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eSFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eSVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eSlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eS1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eTFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eTVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jaxb.eclipselink.ui.command.addEclipseLinkJaxbProperty" commandName="Add EclipseLink JAXB property" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eTlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eT1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eUlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eU1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eVFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eVVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eVlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or function" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eV1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eWFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.PackagesView" commandName="JavaScript Folders" description="Show the Folders view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eWVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eWlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eW1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eXFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eXVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eXlpBEeaY1-o0LnAsFA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eX1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO2eYlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FUlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FU1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FVFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FVVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FVlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FV1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FWFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FWVpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO3FWlpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_oO3FW1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FXFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FXVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new function parameter based on the selected expression" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FXlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FX1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" commandName="JavaScript Type Hierarchy" description="Show the Type Hierarchy view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FYlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FY1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FZFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FZVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FZlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FZ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FaFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools &amp;Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FaVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FalpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3Fa1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FbFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FbVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FblpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3Fb1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.convertJavaProjectToJpa" commandName="Convert to JPA Project..." category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.emf.codegen.ecore.ui.Generate" commandName="Generate Code" description="Generate code for the EMF models in the workspace" category="_oOstS1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3FcVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sYlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sY1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sZFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_oOsGMlpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO3sZVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_oO3sZlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sZ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3saFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3saVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3salpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sa1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sbFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sbVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sblpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sb1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3scFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.generateDynamicEntities" commandName="Generate Dynamic Entities from Tables..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3scVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sclpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sc1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sdFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sdVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sdlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" commandName="JavaScript Script Explorer" description="Show the Script Explorer" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sd1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3seFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3seVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3selpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3se1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sfFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sfVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sflpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sf1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_oOstXlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.entityMappingsAddPersistentClass" commandName="Add Class..." category="_oOstXFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sgVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sglpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sg1pBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3shFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3shVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="Rename XSD element" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3shlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO3sh1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TcFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TcVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TclpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_oOsGMVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO4Tc1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_oO4TdFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_oO4TdVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_oO4TdlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4Td1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TeFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO4TeVpBEeaY1-o0LnAsFA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_oO4TelpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4Te1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TfFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TfVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TflpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4Tf1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TgFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_oOstQVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TgVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TglpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4Tg1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_oOsGO1pBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO4ThFpBEeaY1-o0LnAsFA" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_oO4ThVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4ThlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4Th1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TiFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TiVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_oOstTFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO4TilpBEeaY1-o0LnAsFA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_oO4Ti1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TjFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TjVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.SourceView" commandName="JavaScript Declaration" description="Show the Declaration view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TjlpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4Tj1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO4TkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_oO4TkVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO4TklpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46gFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46gVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.override.methods" commandName="Override/Implement Functions" description="Override or implement functions from super types" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46glpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46g1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46hFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46hVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_oOstQVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46hlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46h1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46iFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46iVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46ilpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46i1pBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46jFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46jVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46jlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46j1pBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_oOstRVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46kFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46kVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInPackageView" commandName="Show JavaScript Element in Script Explorer" description="Select JavaScript element in the Script Explorer view" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO46klpBEeaY1-o0LnAsFA" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_oO46k1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="Promotes local element to global level and replaces its references" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46lFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46lVpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_oOsGN1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46llpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46l1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46mFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO46mVpBEeaY1-o0LnAsFA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hkFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hkVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.GotoMatchingTokenAction" commandName="Goto Matching Token" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hklpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API &amp;Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hk1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Functions" description="Add delegate functions for a type's vars" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hlFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hlVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hllpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.convertJavaConverters" commandName="Move Java Converters to XML..." category="_oOstWVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hl1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hmFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hmVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hmlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hm1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.in.package.view" commandName="Show in Script Explorer" description="Show the selected element in the Script Explorer" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hnFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hnVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hnlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hn1pBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_oOstT1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hoVpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5holpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5ho1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hpFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_oOstV1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hpVpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hplpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hp1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hqFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hqVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hqlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hq1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hrFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hrVpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.commands.openPreferences" commandName="Open the preferences dialog" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO5hrlpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_oO5hr1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hsVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hslpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5hs1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5htFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5htVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5htlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5ht1pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5huFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO5huVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IoFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IoVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IolpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Io1pBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IpFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IpVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IplpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Ip1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IqFpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven Plugin" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IqVpBEeaY1-o0LnAsFA" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IqlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Iq1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IrFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IrVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IrlpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Ir1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IsVpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IslpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Is1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6ItFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6ItVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6ItlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.persistentTypeAddVirtualAttribute" commandName="Add Virtual Attribute..." category="_oOstXFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6It1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_oOstVlpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO6IuFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_oO6IuVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_oO6IulpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newDynamicEntity" commandName="EclipseLink Dynamic Entity" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Iu1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IvFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IvVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IvlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Iv1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IwVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.saveToDatabaseAction" commandName="Save to Database" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IwlpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Iw1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IxFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IxVpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IxlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6Ix1pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IyFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6IyVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vsFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXml" commandName="Add Attribute to XML" category="_oOstXFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vsVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vslpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vs1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vtFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vtVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.persistentTypeMapAs" commandName="Map As" category="_oOstXFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO6vtlpBEeaY1-o0LnAsFA" elementId="persistentTypeMappingKey" name="mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_oO6vt1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.DMLDialogSelectionAction" commandName="Edit in SQL Query Builder..." category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vuFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vuVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.dbws.ui.generateDbws" commandName="Generate Database Web Services" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vulpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vu1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vvFpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.attachProfileAction" commandName="Set Connection Information" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vvVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vvlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_oOstV1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vv1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vwVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vwlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vw1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.connectivity.commands.import" commandName="Import Profiles Command" description="Command to import connection profiles" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vxFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vxVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vxlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vx1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vyFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vyVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vylpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vy1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vzFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vzVpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vzlpBEeaY1-o0LnAsFA" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6vz1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6v0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO6v0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7WwFpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7WwVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7WwlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7Ww1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7WxFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7WxVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7WxlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_oOstUlpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO7Wx1pBEeaY1-o0LnAsFA" elementId="title" name="Title"/>
    <parameters xmi:id="_oO7WyFpBEeaY1-o0LnAsFA" elementId="message" name="Message"/>
    <parameters xmi:id="_oO7WyVpBEeaY1-o0LnAsFA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_oO7WylpBEeaY1-o0LnAsFA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_oO7Wy1pBEeaY1-o0LnAsFA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_oO7WzFpBEeaY1-o0LnAsFA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_oO7WzVpBEeaY1-o0LnAsFA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_oO7WzlpBEeaY1-o0LnAsFA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_oO7Wz1pBEeaY1-o0LnAsFA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_oO7W0FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W0VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W0lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.method" commandName="Extract Function" description="Extract a set of statements or an expression into a new function and use the new function" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W01pBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.generateDDL" commandName="Generate Tables from Entities..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W1FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W1VpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.runAction" commandName="Run" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W1lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W11pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W2FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W2VpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W2lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W21pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W3FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W3VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W3lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W31pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_oOstVlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W41pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W5FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W5VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.JavadocView" commandName="Documentation" description="Show the JavaScript Documentation view" category="_oOsGMVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W51pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO7W6FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_oO7W6VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_oO7W6lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W61pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W7FpBEeaY1-o0LnAsFA" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_oOstWFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W7VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO7W7lpBEeaY1-o0LnAsFA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_oO7W71pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7W8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.result.removeInstance" commandName="Remove Result" category="_oOstTVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO790FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO790VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.discovery.ui.discoveryWizardCommand" commandName="Discovery Wizard" description="shows the connector discovery wizard" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO790lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7901pBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXmlAndMap" commandName="Add Attribute to XML and Map..." category="_oOstXFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO791FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO791VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO791lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7911pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO792FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO792VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO792lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7921pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO793FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO793VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO793lpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7931pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO794FpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_oOsGMFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO794VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_oOstTlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO794lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7941pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO795FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO795VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO795lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.design" commandName="Graphical Designer" category="_oOsGM1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7951pBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_oOstT1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO796FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO796VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO796lpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7961pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO797FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Var" description="Create getting and setting functions for the var and use only those to access the var" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO797VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO797lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7971pBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO798FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Var" description="Convert a local variable to a var" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO798VpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO798lpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7981pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO799FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO799VpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_oOstRVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO799lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7991pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79-FpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79-VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79-lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newEclipseLinkMappingFile" commandName="EclipseLink ORM Mapping File" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79-1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79_FpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79_VpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79_lpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO79_1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-AFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-AVpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-AlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-A1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_oOsGO1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-BFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-BVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.horizotal" commandName="Horizontal Layout" category="_oOsGM1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-BlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-B1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-CFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-CVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-ClpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-C1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-DFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-DVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO7-DlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_oOstQVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k4FpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO8k4VpBEeaY1-o0LnAsFA" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_oO8k4lpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k41pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_oOstSFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO8k5FpBEeaY1-o0LnAsFA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_oO8k5VpBEeaY1-o0LnAsFA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_oO8k5lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.source" commandName="Source Code" category="_oOsGM1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k51pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_oOstTFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k6FpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_oOstRFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k6VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k6lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k61pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected function" category="_oOstYlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k7FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k7VpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k7lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k71pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k8FpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k8VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k8lpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k81pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_oOstRFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO8k9FpBEeaY1-o0LnAsFA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_oO8k9VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k9lpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k91pBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k-FpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k-VpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k-lpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_oOsGOVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k-1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k_FpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_oOstVFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k_VpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_oOstUFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k_lpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8k_1pBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO8lAFpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_oO8lAVpBEeaY1-o0LnAsFA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_oO8lAlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lA1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lBFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.makePersistent" commandName="Make Persistent..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lBVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSelectionAction" commandName="Execute Selected Text" category="_oOstVVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lBlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jaxb.ui.command.createPackageInfo" commandName="Create package-info.java" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lB1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_oOstSlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lCFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_oOstW1pBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lCVpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.rcp.commands.extensionDiscovery" commandName="Discover New Extensions" category="_oOstYVpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO8lClpBEeaY1-o0LnAsFA" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_oO8lC1pBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_oOstRlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lDFpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_oOstUVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lDVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.newMappingFile" commandName="JPA ORM Mapping File" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lDlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lD1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lEFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lEVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.xmlFileUpgradeToLatestVersion" commandName="Upgrade JPA Document Version" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lElpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_oOsGOFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lE1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_oOsGMlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lFFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_oOstSFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lFVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lFlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lF1pBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.commands.openscrapbook" commandName="Open SQL Scrapboo&amp;k" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lGFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_oOsGPFpBEeaY1-o0LnAsFA">
    <parameters xmi:id="_oO8lGVpBEeaY1-o0LnAsFA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_oO8lGlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_oOsGOlpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oO8lG1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_oOsGPFpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oihBIFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oihoMFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet/org.eclipse.datatools.sqltools.sqlscrapbook.actions.OpenScrapbookAction" commandName="Open SQL Scrapbook" description="Open scrapbook to edit SQL statements" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oikrgFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oikrgVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oilSkFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oilSkVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oil5oFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oil5oVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oimgsFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oimgsVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oinHwFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiqyIFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oirZMFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oirZMVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oisnUFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oisnUVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Servlet" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oitOYFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="Dynamic Web Project" description="Create a Dynamic Web project" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oitOYVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oit1cFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oit1cVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiucgFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiucgVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oivDkFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oivqoFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oivqoVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiwRsFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiw4wFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oixf0FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenFileWizard" commandName="JavaScript Source File" description="New JavaScript file" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiyG4FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenProjectWizard" commandName="JavaScript Project..." description="New JavaScript Project" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiyG4VpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.refactor.show.refactoring.history" commandName="History..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiyt8FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.SearchActionSet/org.eclipse.wst.jsdt.ui.actions.OpenJavaSearchPage" commandName="JavaScript..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oizVAFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oizVAVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oiz8EFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi0jIFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="Create a new Cascading Style Sheet" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi0jIVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="Create a new JavaScript file" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi1KMFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="Create a new HTML page" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi1KMVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi1xQFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi1xQVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.rullerDoubleClick/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Add Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi2YUFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.exporter.genModelEditorContribution/org.eclipse.emf.exporter.ui.GenModelExportActionDelegate.Editor" commandName="Export Model..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi2YUVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.importer.genModelEditorContribution/org.eclipse.emf.importer.ui.GenModelReloadActionDelegate.Editor" commandName="Reload..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi2_YFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.RemoveMappingActionID" commandName="Remove Mapping" description="Remove the mapping associated with the selected objects." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi2_YVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.TypeMatchMappingActionID" commandName="Match Mapping by Type" description="Create child mappings automatically by type." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi3mcFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.NameMatchMappingActionID" commandName="Match Mapping by Name" description="Create child mappings automatically by name." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi3mcVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateOneSidedMappingActionID" commandName="Create One-sided Mapping" description="Create a new mapping for the selected object." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi4NgFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateMappingActionID" commandName="Create Mapping" description="Create a new mapping between the selected objects." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi40kFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddOuputRootActionID" commandName="Add Output Root..." description="Add new output root." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi40kVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddInputRootActionID" commandName="Add Input Root..." description="Add new input root." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi5boFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi5boVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi6CsFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi6CsVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi6pwFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi7Q0FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi7Q0VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi734FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi734VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi8e8FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi8e8VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi9GAFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi9GAVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi9tEFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi9tEVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi-UIFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi-UIVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi-7MFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi-7MVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi_iQFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_oi_iQVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojAJUFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojAJUVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.debug.ui.togglebreakpoint/org.eclipse.wst.jsdt.debug.ui.RulerToggleBreakpoint" commandName="Toggle Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojAwYFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojAwYVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojBXcFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojBXcVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojBXclpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojB-gFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="Reload Dependencies" description="Reload Dependencies" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojB-gVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojClkFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojClkVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojHeEFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojHeEVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojIsMFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojJTQFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojJ6UFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojKhYFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojLIcFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojLvgFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojLvgVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojMWkFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojMWkVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojMWklpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojM9oFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojNksFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojNksVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojOLwFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojOLwVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojOLwlpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojOLw1pBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojOy0FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojPZ4FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojPZ4VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojPZ4lpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojQA8FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojQA8VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojQoAFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojTEQFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojUSYFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojU5cFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojU5cVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojU5clpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojVggFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojVggVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojVgglpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojWHkFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojWHkVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojWuoFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojWuoVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojXVsFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojXVsVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojXVslpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojX8wFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojX8wVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojYj0FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojYj0VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojZK4FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojZK4VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojZK4lpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojZx8FpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojZx8VpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojaZAFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojaZAVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojaZAlpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojbAEFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojbAEVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojbnIFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojbnIVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojbnIlpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojcOMFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojcOMVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojc1QFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojc1QVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.add.scriptload.breakpoint" commandName="Add Script Load Breakpoint" description="Add Script Load Breakpoint" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojc1QlpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Suspend For All Script Loads" description="Suspends when any script is loaded" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojdcUFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.suspend.on.exceptions" commandName="Suspend On JavaScript Exceptions" description="Suspend on all JavaScript exceptions" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojdcUVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::debugViewActions/org.eclipse.wst.jsdt.debug.ui.show.all.scripts" commandName="Show All Scripts" description="Shows or hides all scripts loaded in the visible targets" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojeDYFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.functions" commandName="Show function variables" description="Show or hide function variables" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojeDYVpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.this" commandName="Show 'this' variable" description="Show or hide the this variable" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojeDYlpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.prototypes" commandName="Show proto variables" description="Show or hide proto variables" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <commands xmi:id="_ojeqcFpBEeaY1-o0LnAsFA" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="Validate WS-I Message Log File" category="_oOstYVpBEeaY1-o0LnAsFA"/>
  <addons xmi:id="_oOJ6t1pBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_oOJ6uFpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_oOJ6uVpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_oOJ6ulpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_oOJ6u1pBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_oOJ6vFpBEeaY1-o0LnAsFA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_oOJ6vVpBEeaY1-o0LnAsFA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_oOJ6vlpBEeaY1-o0LnAsFA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_oOJ6v1pBEeaY1-o0LnAsFA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_oOJ6wFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_oOSdkFpBEeaY1-o0LnAsFA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_oOsGMFpBEeaY1-o0LnAsFA" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_oOsGMVpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_oOsGMlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_oOsGM1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jst.pagedesigner.pagelayout" name="Web Page Editor Layout"/>
  <categories xmi:id="_oOsGNFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_oOsGNVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_oOsGNlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_oOsGN1pBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_oOsGOFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_oOsGOVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_oOsGOlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.category.source" name="Source" description="JavaScript Source Actions"/>
  <categories xmi:id="_oOsGO1pBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_oOsGPFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_oOstQFpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_oOstQVpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_oOstQlpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="%commands.category.name" description="%commands.category.description"/>
  <categories xmi:id="_oOstQ1pBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.debug.ui.category" name="JavaScript Debug" description="Tooling for debugging JavaScript"/>
  <categories xmi:id="_oOstRFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_oOstRVpBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_oOstRlpBEeaY1-o0LnAsFA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_oOstR1pBEeaY1-o0LnAsFA" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_oOstSFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_oOstSVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaedtor.10x" name="ASA 9.x table schema editor"/>
  <categories xmi:id="_oOstSlpBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_oOstS1pBEeaY1-o0LnAsFA" elementId="org.eclipse.emf.codegen.ecore.ui.Commands" name="EMF Code Generation" description="Commands for the EMF code generation tools"/>
  <categories xmi:id="_oOstTFpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_oOstTVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.result.category" name="SQL Results View"/>
  <categories xmi:id="_oOstTlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_oOstT1pBEeaY1-o0LnAsFA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_oOstUFpBEeaY1-o0LnAsFA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_oOstUVpBEeaY1-o0LnAsFA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_oOstUlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_oOstU1pBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_oOstVFpBEeaY1-o0LnAsFA" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_oOstVVpBEeaY1-o0LnAsFA" elementId="org.eclipse.datatools.sqltools.sqleditor.category" name="Database Tools" description="Database Development tools"/>
  <categories xmi:id="_oOstVlpBEeaY1-o0LnAsFA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_oOstV1pBEeaY1-o0LnAsFA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_oOstWFpBEeaY1-o0LnAsFA" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_oOstWVpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.jpaMetadataConversionCommands" name="JPA Metadata Conversion"/>
  <categories xmi:id="_oOstWlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_oOstW1pBEeaY1-o0LnAsFA" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_oOstXFpBEeaY1-o0LnAsFA" elementId="org.eclipse.jpt.jpa.ui.jpaStructureViewCommands" name="JPA Structure View"/>
  <categories xmi:id="_oOstXVpBEeaY1-o0LnAsFA" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_oOstXlpBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_oOstX1pBEeaY1-o0LnAsFA" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_oOstYFpBEeaY1-o0LnAsFA" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_oOstYVpBEeaY1-o0LnAsFA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_oOstYlpBEeaY1-o0LnAsFA" elementId="org.eclipse.wst.jsdt.ui.category.refactoring" name="Refactor - JavaScript" description="JavaScript Refactoring Actions"/>
</application:Application>
