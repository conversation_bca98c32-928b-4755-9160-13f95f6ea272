<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kettle-clm</artifactId>
        <groupId>com.stpl.tech.kettle.clm</groupId>
        <version>4.1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>clm-service</artifactId>
    <name>clm-service</name>
    <url>http://maven.apache.org</url>
    <packaging>war</packaging>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <build>
        <finalName>${project.artifactId}</finalName>
    </build>
<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-war-plugin</artifactId>-->
<!--                <version>2.6</version>-->
<!--                <configuration>-->
<!--                    <webXml>WebContent/WEB-INF/web.xml</webXml>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--        <finalName>clm-service</finalName>-->
<!--    </build>-->
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.clm</groupId>
            <artifactId>clm-core</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
