package com.stpl.tech.clm.service.contoller;

import com.stpl.tech.clm.core.data.vo.TestCampaignVO;
import com.stpl.tech.clm.core.service.*;
import com.stpl.tech.clm.core.util.ClmServiceConstants;
import com.stpl.tech.clm.domain.model.*;
import com.stpl.tech.master.core.service.AbstractResources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping(value = ClmServiceConstants.API_VERSION + ClmServiceConstants.SEPARATOR
        + ClmServiceConstants.CLM_METADATA_ROOT_CONTEXT)
public class ClmMetadataManagementResource extends AbstractResources {

    private final Logger LOG = LoggerFactory.getLogger(ClmMetadataManagementResource.class);

    @Autowired
    private ClmMetadataService metadataService;

    @Autowired
    private CampaignService campaignService;

    @Autowired
    private IntentService intentService;

    @Autowired
    private TestUserService testUserService;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private TemplateAttributeService templateAttributeService;

    @RequestMapping(method = RequestMethod.GET, value = "campaign-types", produces = MediaType.APPLICATION_JSON)
    public CampaignType[] getCampaignTypes() {
        LOG.info("Request to get all Campaign Types");
        return CampaignType.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "campaigns", produces = MediaType.APPLICATION_JSON)
    public List<Campaign> getAllCampaigns() {
        LOG.info("Request to get all Campaigns");
        return metadataService.getAllCampaigns();
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Campaign addCampaigns(@RequestBody Campaign campaign) {
        LOG.info("Request to add Campaign");
        return campaignService.addCampaigns(campaign);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign/sendMessage", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean testCampaigns(@RequestBody TestCampaignVO campaign) throws IOException{
        LOG.info("Request to test Campaign");
        return campaignService.sendMessage(campaign.getServiceProvider(), campaign.getCampaignType(), campaign.getContactNumber(),campaign.getMessage());
    };

    @RequestMapping(method = RequestMethod.POST, value = "campaign/update", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Campaign updateCampaigns(@RequestBody Campaign campaign) {
        LOG.info("Request to update Campaign");
        return campaignService.updateCampaigns(campaign);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign/deactivate", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean deactivateCampaign(@RequestBody int campaignId) {
        LOG.info("Request to deactivate Campaign");
        return campaignService.changeCampaignStatus(campaignId, RecordStatus.IN_ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign/activate", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean activateCampaign(@RequestBody int campaignId) {
        LOG.info("Request to activate Campaign");
        return campaignService.changeCampaignStatus(campaignId, RecordStatus.ACTIVE);
    }

    @RequestMapping(method = RequestMethod.GET, value = "Campaign-events", produces = MediaType.APPLICATION_JSON)
    public List<CampaignEvent> getAllCampaignEvents() {
        LOG.info("Request to get all Campaign Events");
        return metadataService.getAllCampaignEvents();
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-events/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public CampaignEvent addCampaignEvents(@RequestBody CampaignEvent campaignEvent) {
        LOG.info("Request to add Campaign-Events");
        return campaignService.addCampaignEvents(campaignEvent);
    }

    @RequestMapping(method = RequestMethod.GET, value = "campaign-targets", produces = MediaType.APPLICATION_JSON)
    public List<CampaignTarget> getAllCampaignTargets() {
        LOG.info("Request to get all Campaign Targets");
        return metadataService.getAllCampaignTargets();
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-target/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public CampaignTarget addCampaignTargets(@RequestBody CampaignTarget campaignTarget) {
        LOG.info("Request to add Campaign-target");
        return campaignService.addCampaignTargets(campaignTarget);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-target/update", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public CampaignTarget updateCampaignTarget(@RequestBody CampaignTarget campaignTarget) {
        LOG.info("Request to update Campaign-target");
        return campaignService.updateCampaignTarget(campaignTarget);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-target/addAll", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean addCampaignTargets(@RequestBody List<CampaignTarget> campaignTargets) {
        LOG.info("Request to add all Campaign-target");
        return campaignService.addCampaignTargets(campaignTargets);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-target/updateAll", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean updateCampaignTargets(@RequestBody List<CampaignTarget> campaignTargets) {
        LOG.info("Request to add all Campaign-target");
        return campaignService.updateCampaignTargets(campaignTargets);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-target/remove", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean removeCampaignTarget(@RequestBody CampaignTarget campaignTarget) {
        LOG.info("Request to remove Campaign-target");
        return campaignService.removeCampaignTarget(campaignTarget);
    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-target/removeAll", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean removeCampaignTargets(@RequestBody List<CampaignTarget> campaignTargets) {
        LOG.info("Request to remove all Campaign-targets");
        return campaignService.removeCampaignTargets(campaignTargets);
    }

    @RequestMapping(method = RequestMethod.GET, value = "intents", produces = MediaType.APPLICATION_JSON)
    public List<Intent> getAllIntents() {
        LOG.info("Request to add all Intents");
        return metadataService.getAllIntents();
    }

    @RequestMapping(method = RequestMethod.POST, value = "intent/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Intent addIntent(@RequestBody Intent intent) {
        LOG.info("Request to add intent");
        return intentService.addIntent(intent);
    }

    @RequestMapping(method = RequestMethod.POST, value = "intent/update", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Intent updateIntent(@RequestBody Intent intent) {
        LOG.info("Request to update intent");
        return intentService.updateIntent(intent);
    }

    @RequestMapping(method = RequestMethod.GET, value = "delivery-status", produces = MediaType.APPLICATION_JSON)
    public DeliveryStatus[] getDeliveryStatus() {
        LOG.info("Request to get all Delivery Status");
        return DeliveryStatus.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "notification-Type", produces = MediaType.APPLICATION_JSON)
    public NotificationType[] getNotificationType() {
        LOG.info("Request to get all Notification Type");
        return NotificationType.values();
    }

    @RequestMapping(method = RequestMethod.POST, value = "record-Status", produces = MediaType.APPLICATION_JSON)
    public RecordStatus[] getRecordStatus() {
        LOG.info("Request to get all Record Status");
        return RecordStatus.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "service-Provider", produces = MediaType.APPLICATION_JSON)
    public List<ServiceProvider> getAllServiceProviders() {
        LOG.info("Request to get all Service Providers");
        return metadataService.getAllServiceProviders();
    }

    @RequestMapping(method = RequestMethod.GET, value = "templates", produces = MediaType.APPLICATION_JSON)
    public List<Template> getAllTemplates() {
        LOG.info("Request to get all Templates");
        return metadataService.getAllTemplates();
    }

    @RequestMapping(method = RequestMethod.POST, value = "template/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Template addTemplate(@RequestBody Template template) {
        LOG.info("Request to add Template");
        return templateService.addTemplate(template);
    }

    @RequestMapping(method = RequestMethod.POST, value = "template/update", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Template updateTemplate(@RequestBody Template template) {
        LOG.info("Request to update template");
        return templateService.updateTemplate(template);
    }
    @RequestMapping(method = RequestMethod.GET, value = "template-Attributes", produces = MediaType.APPLICATION_JSON)
    public List<TemplateAttribute> getAllTemplateAttributes() {
        LOG.info("Request to get all Template Attributes");
        return metadataService.getAllTemplateAttributes();
    }

    @RequestMapping(method = RequestMethod.GET, value = "template-AttributeTypes", produces = MediaType.APPLICATION_JSON)
    public TemplateAttributeType[] getTemplateAttributeTypes() {
        LOG.info("Request to get all Template Attribute Types");
        return TemplateAttributeType.values();
    }

    @RequestMapping(method = RequestMethod.POST, value = "templateAttribute/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public TemplateAttribute addTemplateAttribute(@RequestBody TemplateAttribute templateAttribute) {
        LOG.info("Request to add Template Attribute");
        return templateAttributeService.addTemplateAttribute(templateAttribute);
    }

    @RequestMapping(method = RequestMethod.POST, value = "templateAttribute/update", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public TemplateAttribute updateTemplateAttribute(@RequestBody TemplateAttribute templateAttribute) {
        LOG.info("Request to update Template Attribute");
        return templateAttributeService.updateTemplateAttribute(templateAttribute);
    }


    @RequestMapping(method = RequestMethod.GET, value = "template-Categories", produces = MediaType.APPLICATION_JSON)
    public TemplateCategory[] getTemplateCategories() {
        LOG.info("Request to get all Template Categories");
        return TemplateCategory.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "template-Types", produces = MediaType.APPLICATION_JSON)
    public TemplateType[] getTemplateTypes() {
        LOG.info("Request to get all Template Types");
        return TemplateType.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "test-Users", produces = MediaType.APPLICATION_JSON)
    public List<TestUser> getAllTestUsers() {
        LOG.info("Request to get all Test Users");
        return metadataService.getAllTestUsers();
    }

    @RequestMapping(method = RequestMethod.POST, value = "test-User/add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public TestUser addTestUser(@RequestBody TestUser testUser) {
        LOG.info("Request to add Test users");
        return testUserService.addTestUser(testUser);
    }

    @RequestMapping(method = RequestMethod.POST, value = "test-User/update", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public TestUser updateTestUser(@RequestBody TestUser testUser) {
        LOG.info("Request to update Test users");
        return testUserService.updateTestUser(testUser);
    }

    @RequestMapping(method = RequestMethod.POST, value = "test-User/remove", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean removeTestUser(@RequestBody TestUser testUser) {
        LOG.info("Request to update Test users");
        return testUserService.removeTestUser(testUser);
    }
}