package com.stpl.tech.clm.service.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.stpl.tech.clm.core.util.ClmServiceConstants;
import com.stpl.tech.master.core.external.interceptor.ACLInterceptor;
import com.stpl.tech.master.core.external.interceptor.SessionAuthInterceptor;

@Configuration
@EnableWebMvc
@PropertySource("classpath:props/jdbc-dev.properties")
@ComponentScan("com.stpl.tech.clm.service.controller")
public class WebMVCConfig extends WebMvcConfigurerAdapter {

	@Autowired
	private ACLInterceptor aclInterceptor;

	@Autowired
	private SessionAuthInterceptor sessionAuthInterceptor;

	@Override
	public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
		configurer.enable();
	}

	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
		converters.add(new MappingJackson2HttpMessageConverter());
		super.configureMessageConverters(converters);
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(sessionAuthInterceptor).addPathPatterns("/**");
		registry.addInterceptor(aclInterceptor).addPathPatterns("/**");
	}

	@Bean(name = "multipartResolver")
	public CommonsMultipartResolver commonsMultipartResolver() {
		CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
		commonsMultipartResolver.setDefaultEncoding(ClmServiceConstants.CHARSET);
		commonsMultipartResolver.setMaxUploadSize(8388608);
		return commonsMultipartResolver;
	}

}