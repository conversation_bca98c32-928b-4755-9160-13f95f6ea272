/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.clm.service.config;

import java.util.TimeZone;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.stpl.tech.clm.core.config.ClmConfig;
import com.stpl.tech.master.core.config.MasterCacheClientConfig;

@Configuration
@EnableScheduling
@Import(value = { ClmConfig.class , MasterCacheClientConfig.class })
public class ClmServiceConfig {
	static {

		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}
}
