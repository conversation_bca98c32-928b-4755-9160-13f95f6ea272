+++++++++++++++++++++++++++++++++++++++++++++++++#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#

environment.type=DEV
mail.receipt.email=<EMAIL>
mail.undelivered.email=<EMAIL>
mail.dummy.customer.id=5
mail.to.email=<EMAIL>
mail.retry.count=2
mail.thread.sleep.time=60000

server.base.dir=/data/app/kettle/dev

clm.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
clm.jdbc.url=************************************************************************************************************
clm.jdbc.user=root
clm.jdbc.pass=321in321

# Mongo
clm.mongo.url=uat.kettle.chaayos.com
clm.mongo.port=27017
clm.mongo.schema=kettle_clm
clm.mongo.user=root
clm.mongo.pass=root

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

run.validate.filter=true
run.aclInterceptor=false

# hazelcast cache
master.cache.host.details=uat.kettle.chaayos.com
master.cache.host.ports=5701,5702,5703

# automated reports
run.auto.report=false