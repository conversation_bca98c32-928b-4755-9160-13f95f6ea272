<div id="mainTemplateDiv" ng-init="init()">
	<div class="row">
		<div class="col-lg-12"><br>
	        <h1 class="page-header">
	        	Bulk SMS Transactional 
	        </h1>
	    </div>
	</div>
		
	<div class="form-group">
		<div class="col-lg-2"><label>Campaign Name</label></div>
			<div class="col-lg-8">
				 <select class="form-control" ng-model="selectedCampaignTypeData"  ng-options="campaignDetail as campaignDetail.name for campaignDetail in campaignData  track by campaignDetail.id" required > </select>   
			</div>
	</div>
		
	<div class="form-group" style="padding-top:40px">
		<div class="col-lg-2"><label>Template Name</label></div>
			<div class="col-lg-8">
					<select class="form-control" ng-model="selectTemplatesNameData"  ng-options="templatesNameData as templatesNameData.name for templatesNameData in viewTemplateData  track by templatesNameData.text")> </select>   
			</div>
	</div>
	<div class="row" style="padding-top:40px">
		<div><button class="btn btn-primary pull-right"  ng-click="parseTemplate()"><i class="fa fa-plus fw"></i> Parse</button></div>
	</div><br>
	
	<div class="row" style="display:none" id="templateStructureDiv">
		<div class="form-group">
				<table class="table table-striped table-bordered">
						 <tbody>
							<tr ng-repeat="templateAttributeResult in templateAttributeValue">
					          <td class="col-lg-2">  
					              <input type="text" placeholder="${{templateAttributeResult}}$" class="form-control" id="campaignName" ng-model="campaignName[$index]" ng-required/>
					           </td>
					          <td class="col-lg-2">
					              <select class="form-control" ng-model="selectTemplatesFileData"  ng-options="templatesFileData as templatesFileData.name for templatesFileData in templateFile  track by templatesFileData.name" ng-change="showTemplateDetails(selectTemplatesFileData,$index,templateAttributeResult)"> </select>   
					          </td>
					          <td class="col-lg-8">
					              	<textarea class="form-control" style="display:none" id="test{{templateAttributeResult}}" rows="2" ng-model="templateDescription[$index]"></textarea>
					          </td>
					       </tr>
					       <tr>
					       		<td class="form-group clearfix" colspan="3"><button class="btn btn-primary pull-right"  ng-click="sbmtTemplates()">Test SMS</button></td>
					       </tr>
				       </tbody>
				</table>
		 </div>
	 </div>
	</div>
 </div>
 
	<div id="smsTestTemplateDiv" style="display:none; padding-top:25px">
	 <h1 class="page-header">Test Template </h1>
	 	<div class="row">
			<div class="col-lg-3">
					 <label>SMS Pattern</label>
					 <textarea class="form-control" rows="6" disabled="disabled" ng-model="selectParseTemplate"></textarea> 
			</div>
			<div class="col-lg-3">
					<label>Service Provider</label>
					<select class="form-control" ng-model="selectedServiceProvider" ng-options="serviceProviderData as serviceProviderData.name for serviceProviderData in serviceProviderList track by selectedServiceProvider.name"></select>
			</div>
			
			<div class="col-lg-6">
				<div class="col-lg-4">
					    <select size="10"  class="form-control" id="myselection" multiple ng-multiple="true" ng-model="selectedColors" ng-options="userTestResult.contactNumber for userTestResult in userTestData"></select>
				</div>
				<div class="col-lg-2">
					<table>
						<tr ng-repeat="selectedColorsResult in selectedColors">
							<td class="col-lg-1" style="scroll:autoflow"><label>{{selectedColorsResult.contactNumber}}</label></td>
						</tr>
					</table>
				 </div>
		</div><br><br>
		
		<div class="row" style="margin-top:180px;margin-bottom:50px">
				<div class="col-lg-6">
					<table class="table table-striped table-bordered">
							 <tbody>
								<tr ng-repeat="templateAttributeResult in templateAttributeValue">
						         <td class="col-lg-1"><label>${{templateAttributeResult}}$</label></td>
						         <td class="col-lg-3"><input type="text"  class="form-control" id="templateAttributeName" ng-model="freeTextData[templateAttributeResult]" ng-required/> </td>
						       </tr>
					       </tbody>
					</table>
				</div>
			<div class="col-lg-6">
					<table class="table table-striped table-bordered">
							<tr>
							    <td class="col-lg-3"> Remaining Balance : 1500   </td>
							    <td class="col-lg-3"> Re charge SMS : <br><a href="https://www.smsacharya.com" target="_blank">SMS Ghup Shup</a> <br> <a href="https://www.smsacharya.com" target="_blank">SMS Acarya  </td>
							</tr>
					</table>
			</div>
		</div>
		<div  align="right">
				<table>
					<tr>
					    <td>&nbsp;<button class="btn btn-primary pull-right"  ng-click="backSMSTemplate()">Back</button>  &nbsp;</td>
					    <td>&nbsp;<button class="btn btn-primary pull-right"  ng-click="previewSmsTemplate()">Preview SMS</button>&nbsp;</td>
			       </tr>
				</table>
		</div>
	 </div><br><br>
	 </div>
 
	 <div class="modal fade" id="smsPreviewModal" tabindex="-1" role="dialog" aria-labelledby="smsPreviewModalLable">
	   <div class="modal-dialog" role="document">
	      <div class="modal-content">
	          <div class="modal-header">
	            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	            <h4 class="modal-title" id="smsPreviewModalLableTitle">SMS Preview Modal</h4>
	          </div>
	          <form name="myForm">
		          <div class="modal-body">
		              <div class="form-group">
		                  <label>Number *</label>
		                <textarea class="form-control"  rows="2" ng-model="testingSmsNumber"></textarea>
		              </div>
		              <div class="form-group">
		                  <label>SMS Format *</label>
		                <textarea class="form-control"  rows="4" ng-model="testingSmsDetails"></textarea>
		               </div>
		              
		               <div class="form-group clearfix">
		                  <button class="btn btn-primary pull-right" ng-click="submitSendSMS()">Send Test SMS </button>
		              </div>
		          </div>
	          </form>
	     </div>
	  </div>
	</div>


			<div id="fileUploadDiv" style="display:none">
				<div class="row"><br>
						<div class="form-group">
							<button class="btn btn-primary pull-right" export-to-csv> CSV format for upload Data </button> 
							<!-- <div align="right" ><a href="img/smsformat.csv" ng-click="downloadExcelFormat()" download >  <b>CSV format for upload Data </b> </a> </div> -->					
						</div>
				</div>
			</div>
		
	
	 <div class="row" id="smsFileUploadMainDiv"  >
			<h4 class="modal-title" >SMS Preview Modal</h4>
			<div class="form-group">
				 <label>File Upload</label>
				 <span  id="dvPhoneNoCust"><input type="file" class="form-control"  file-reader="fileContent" ng-click='injectedObject.invoke()'/></span>
			</div>
	 </div>
	
		<div class="row" id="exportTableDataNone" style="display:none">
					<table width="100%" id="myTable">
				         <th ng-repeat="item in templateAttributeValue">{{item}} </th>
				    </table>
		</div>
	
		
		<div id="textSmsGridDiv" style="display:none">
			<div class="row">
			<div align="right"><b>Total SMS : {{fileContent.length}}</b></div>
				<table class="table table-bordered">
				
	                    <tr style="background-color:#50773e; color:#ffffff; overflow:scroll">
	                       <th>S.No &nbsp;</th>
	                       <th>Check&nbsp;<input type="checkbox" ng-model='allChecked'  ng-true-value="'YES'" ng-false-value="'NO'"  ng-click="checkAll()"></th>
	                      	<th ng-repeat="item in templateAttributeValue">{{item}} </th>
	                      	<th>Length  &nbsp;</th>
	                        <th>Count  &nbsp;</th>
	                      	</tr>
	                <tbody>
	                    <tr ng-repeat='fulSelObjUnits in fileContent'> 
	                        <td>{{$index+1}}</td>
	                        <td> <input type="checkbox"  ng-model='fulSelObjUnits.checkValue'></td>
	                        <td>{{fulSelObjUnits.contact}}</td>
	                        <td>{{fulSelObjUnits.testSms}} <a ng-click="changeUserText(fileContent,fulSelObjUnits.contact)" style="text-align:right;cursor:pointer"><b>&nbsp;Edit</b></a></td>
	                        <td>{{fulSelObjUnits.customerName}}</td>
	                        <td>{{fulSelObjUnits.couponsCode}} </td>
	                         <td>{{fulSelObjUnits.visit}}</td>
	                        <td>{{fulSelObjUnits.testSms.length}} </td>
	                        <td>{{fulSelObjUnits.testSms.length/160 | ceil }}</td></tr>
	                    <tr>Checked SMS :111<td colspan="12"><button class="btn btn-primary pull-right" ng-click="finalSmsListSend()">Send  SMS </button> </td></tr>
	                </tbody>
	 		</table>
	 		
	 		<!-- <my-directive file-Reader=" injectedObject"> </ my-directive> -->
		</div>
	</div>
		


<div class="modal fade" id="smsUpdateModal" tabindex="-1" role="dialog" aria-labelledby="smsUpdateModalLable" style="display:none">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="smsUpdateModalTitle">SMS Update Modal</h4>
          </div>
          <form name="myForm">
	          <div class="modal-body">
	             <div class="form-group">
	                  <label>SMS Format *</label>
	                	<textarea class="form-control"  rows="4" ng-model="updateSmsDetailsData"></textarea>
	               </div>
	               <div class="form-group clearfix">
	                  <button class="btn btn-primary pull-right" ng-click="updateSMS(updateMobileNumber)">Update SMS </button>
	              </div>
	          </div>
          </form>
     </div>
  </div>
</div>



<div id="FinalSmsGridViewDiv" style="display:none">
			<div class="row">
			<div align="right"><b>Total SMS : {{smsListFinalData.length}}</b></div>
				<table class="table table-bordered">
				
	                    <tr style="background-color:#50773e; color:#ffffff; overflow:scroll">
	                       <th>S.No &nbsp;</th>
	                      	<th>Contact Number  &nbsp;</th>
	                        <th>SMS  &nbsp;</th>
	                        <th> Length  &nbsp;</th>
	                        <th> Count  &nbsp;</th>
	                      	</tr>
	                <tbody>
	                
	                    <tr ng-repeat='fulSelObjUnits in smsListFinalData'> 
	                        <td>{{$index+1}}</td>
	                        <td>{{fulSelObjUnits.contact}}</td>
	                        <td>{{fulSelObjUnits.testSms}} </td>
	                        <td>{{fulSelObjUnits.testSms.length}} </td>
	                        <td>{{fulSelObjUnits.testSms.length/160 | ceil }}</td></tr>
	                </tbody>
	 		</table>
	 		<div align="right" class="row">
		 		<div class="col-lg-6"><button class="btn btn-primary btn-lg btn-block" ng-click="backSmsDetails()">&nbsp;Back &nbsp;</button> </div>
		 		<div class="col-lg-6"><button class="btn btn-primary btn-lg btn-block" ng-click="finalSmsListSend()"> Send Message</button> </div>
	 		</div>
	 		<!-- <my-directive file-Reader=" injectedObject"> </ my-directive> -->
		</div>
	</div>



