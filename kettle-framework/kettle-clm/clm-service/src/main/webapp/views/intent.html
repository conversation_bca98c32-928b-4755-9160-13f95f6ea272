<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
        <h1 class="page-header">
        	Intent View
            <button class="btn btn-primary pull-right" data-toggle="modal" id="addIntentIdDiv" ng-click="addIntent()"><i class="fa fa-plus fw"></i> Add Intent</button>
        </h1>
        
    </div>
</div>


<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    <div class="col-lg-offset-6 col-lg-2">
        Results per page:
        <select ng-model="entryLimit" class="form-control">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
            <option value="300">300</option>
            <option value="400">400</option>
        </select>
    </div>
</div>

<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ intentData.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                         <th>ID &nbsp;</th>
                        <th> Name&nbsp;</th>
                        <th>Description&nbsp;</th>
                        <th align="center">Action&nbsp;</th>
                    </thead>
                <tbody>
                
                    <tr ng-repeat="intentsList in intentData | filter:search">
                        <td>{{intentsList.id}}</td>
                        <td>{{intentsList.name}}</td>
                        <td>{{intentsList.description}}</td>
                       <td align="left">
                            <img ng-click="editIntent(intentsList.id)" style="margin-bottom:8px;cursor:pointer" title="Edit Intent" ng-src="img/change.png" height="25px" width="25px">&nbsp;&nbsp;
 						</td> 
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>



<div class="modal fade" id="intentModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">{{action}} Intent</h4>
          </div>
          <div class="modal-body">
              <div class="row">
     <form name="myForm">
          <div class="modal-body">
              <div class="form-group">
                  <label>Name *</label>
                  <input type="text" class="form-control" id="intentName" ng-model="intentName" ng-required/>
              </div>
              <div class="form-group">
                  <label>Description *</label>
                  <input type="text" class="form-control" id="intentDescription"  ng-model="intentDescription" />
              </div>
               <div class="form-group clearfix">
                  <button class="btn btn-primary pull-right" ng-if="action=='add'" ng-click="sbmtIntent()">Add</button>
                  <button class="btn btn-primary pull-right" ng-if="action=='edit'" ng-click="updateIntent(intentID)">Update</button>
              </div>
          </div>
     </form>
</div>
          </div>
     </div>
  </div>
</div>






