   


<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
        <h1 class="page-header">
        	Campaign View
            <button class="btn btn-primary pull-right" data-toggle="modal" id="addCampaignDiv" ng-click="addCampaign()"> Add Campaign</button>
        </h1>
    </div>
</div>
<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
</div>
 
<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ campaignData.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                         <th>ID &nbsp;</th>
                        <th> Name&nbsp;</th>
                        <th>Description&nbsp;</th>
                        <th>Intent Name&nbsp;</th>
                        <th align="center">Action&nbsp;</th>
                    </thead>
                <tbody>
                
                    <tr ng-repeat="campaignList in campaignData | filter:search">
                        <td>{{campaignList.id}}</td>
                        <td>{{campaignList.name}}</td>
                        <td>{{campaignList.description}}</td>
                        <td>{{campaignList.intent.name}}</td>
                       <td align="left">
                            <img ng-click="editCampaign(campaignList.id)" style="margin-bottom:8px;cursor:pointer" title="Edit Campaign" ng-src="img/change.png" height="25px" width="25px">&nbsp;&nbsp;
 						</td> 
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>

<div class="modal fade" id="campaignModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">{{action}} Campaign</h4>
          </div>
          <div class="modal-body">
		<div class="row">
              <div class="col-lg-4">
              <lable>&nbsp;</lable>
                  <input type="text" placeholder="Name" class="form-control" id="campaignName" ng-model="campaignName" ng-required/>
              </div>
              <div class="col-lg-4">
              <lable style="font-size:10px" align="center">Campaign Type</lable>
              	<select class="form-control" ng-model="campaignType"  ng-options="campaignDetails as campaignDetails.name for campaignDetails in campaignTypeLists | orderBy:'name' track by campaignDetails.name" required > </select>   
              </div>
              
              <div class="col-lg-4">
              <lable style="font-size:10px" align="center">Intent</lable>
                <select class="form-control" ng-model="selectedIntentType"  ng-options="intentDetails as intentDetails.name for intentDetails in intentData  track by intentDetails.id" required > </select>   
			</div>
              <div class="col-lg-12" style="bottom-top:20px">&nbsp;
              <textarea class="form-control" rows="1" ng-model="campaignDescription"></textarea></div>
        </div>
        
        <br><br>
            <div class="form-group clearfix">
                  <button class="btn btn-primary pull-right" ng-if="action=='add'" ng-click="sbmtCampaign()">Add</button>
                  <button class="btn btn-primary pull-right" ng-if="action=='edit'" ng-click="updateCampaign(campaignID)">Update</button>
             </div>
          </div>
     
          </div>
     </div>
  </div>







       