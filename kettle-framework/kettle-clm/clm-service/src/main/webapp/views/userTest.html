<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
        <h1 class="page-header">
        	User Test View
            <button class="btn btn-primary pull-right" data-toggle="modal" id="addUserTestIdDiv" ng-click="addUserTest()"><i class="fa fa-plus fw"></i> Add User Test</button>
        </h1>
        
    </div>
</div>


<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
    
</div>

<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ userTestData.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                         <th>ID &nbsp;</th>
                        <th> Name&nbsp;</th>
                        <th>Phone&nbsp;</th>
                        <th>Email&nbsp;</th>
                        <th align="center">Action&nbsp;</th>
                    </thead>
                <tbody>
                
                    <tr ng-repeat="userTestList in userTestData | filter:search">
                        <td>{{userTestList.id}}</td>
                         <td>{{userTestList.name}}</td>
                        <td>{{userTestList.contactNumber}}</td>
                        <td>{{userTestList.emailId}}</td>
                       <td align="left">
                            <img ng-click="editUserTest(userTestList.id)" style="margin-bottom:8px;cursor:pointer" title="Edit Intent" ng-src="img/change.png" height="25px" width="25px">&nbsp;&nbsp;
 						</td> 
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>



<div class="modal fade" id="userTestModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">{{action}} Test User</h4>
          </div>
          <div class="modal-body">
              <div class="row">
     <form name="myForm">
          <div class="modal-body">
              <div class="form-group">
                  <label>Name *</label>
                  <input type="text" class="form-control" id="testUserName" ng-model="testUserName" ng-required/>
              </div>
              
              <div class="form-group">
                  <label>Contact Number *</label>
                  <input type="text" class="form-control" id="testUserContact" ng-model="testUserContact" ng-required/>
              </div>
              
              <div class="form-group">
                  <label>Email *</label>
                  <input type="text" class="form-control" id="testUserEmail" ng-model="testUserEmail" ng-required/>
              </div>
              
               <div class="form-group clearfix">
                  <button class="btn btn-primary pull-right" ng-if="action=='add'" ng-click="sbmtUserTest()">Add</button>
                  <button class="btn btn-primary pull-right" ng-if="action=='edit'" ng-click="updateUserTest(userTestID)">Update</button>
              </div>
          </div>
     </form>
</div>
          </div>
     </div>
  </div>
</div>






