   


<div class="row" ng-init="init()">
	<div class="col-lg-12"><br>
        <h1 class="page-header">
        	Template View
            <button class="btn btn-primary pull-right" data-toggle="modal" id="addCreateTemplateDiv" ng-click="addCreateTemplate()"> Add Template</button>
        </h1>
    </div>
</div>
<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
</div>
 
<div class="row">
	<div class="col-xs-12" ng-if="filteredItems > 0">
    	<p>Filtered {{ viewTemplateData.length }} of {{ totalItems}} total results</p>
        <div class="row">
        	<div class="col-xs-12">
            	<table class="table table-striped table-bordered">
                    <thead>
                         <th>ID &nbsp;</th>
                        <th> Name&nbsp;</th>
                        <th>Category&nbsp;</th>
                        <th>Type&nbsp;</th>
                        <th align="center">Text&nbsp;</th>
                        <th align="center">Action&nbsp;</th>
                    </thead>
                <tbody>
                
                
                    <tr ng-repeat="templateList in viewTemplateData | filter:search">
                        <td>{{templateList.id}}</td>
                        <td>{{templateList.name}}</td>
                        <td>{{templateList.category}}</td>
                        <td>{{templateList.type}}</td>
                         <td>{{templateList.text}}</td>
                       <td align="left">
                            <img ng-click="editCreateTemplate(templateList.id)" style="margin-bottom:8px;cursor:pointer" title="Edit Campaign" ng-src="img/change.png" height="25px" width="25px">&nbsp;&nbsp;
 						</td> 
                    </tr>
                </tbody>
                </table>
            </div>
            <div class="col-lg-10" ng-if="filteredItems == 0">
                <h4>No results found</h4>
            </div>
        </div>
        
    </div>
</div>

<div class="modal fade" id="createTemplateModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">{{action}} Template</h4>
          </div>
          <div class="modal-body">
		<div class="row">
              <div class="col-lg-4">
              <lable style="font-size:12px" align="center">Template Name</lable>
                  <input type="text" placeholder="Name" class="form-control" id="templateName" ng-model="templateName" ng-required/>
              </div>
              <div class="col-lg-4">
              <lable style="font-size:12px" align="center">Category</lable>
              	<select class="form-control" ng-model="templateCategory"  ng-options="templateCateogyrData as templateCateogyrData.name for templateCateogyrData in templateCategoryLists | orderBy:'name' track by templateCateogyrData.name" required > </select>   
              </div>
              
              <div class="col-lg-4">
              	<lable style="font-size:12px" align="center">Type</lable>
                <select class="form-control" ng-model="selectedTemplateType"  ng-options="templateTypeData as templateTypeData.name for templateTypeData in templateTypeLists  track by templateTypeData.name" required > </select>   
			 </div>
              <div class="col-lg-12">&nbsp;
              		<textarea class="form-control" rows="2" placeholder="text" ng-model="templateDescription"></textarea>
              </div><br>
        
            <div class="col-lg-12" style="margin-top:20px">
                  <button class="btn btn-primary pull-right"  ng-click="parseTemplate()">Parse </button>
             </div>
          </div>
          
          
          <div style="display:none; margin-top:20px" id="templateAttributeListDiv">
          
          <div class="form-group">
			<table class="table table-striped table-bordered">
					 <tbody>
					 <!-- <tr><td>{{templateAttributeDataDisplay}}</td></tr> -->
						<tr ng-repeat="templateAttributeResult in templateAttributeDataDisplay">
						{{templateAttributeResult}}
				          <td class="col-lg-4">  
				              <input type="text" disabled="disabled" placeholder="${{templateAttributeResult}}$" class="form-control" id="templateAttributeName" ng-model="templateAttributeName[templateAttributeResult]" ng-required/>
				           </td>
				          <td class="col-lg-3">
				              <select class="form-control" ng-model="selectTemplatesFileData[templateAttributeResult]"  ng-options="templatesFileData as templatesFileData.name for templatesFileData in templateFile  track by templatesFileData.name" ng-change="showTemplateDetails(selectTemplatesFileData[templateAttributeResult],$index,templateAttributeResult)"> </select>   
				          </td>
				          <td class="col-lg-5">
								<!-- {{selectTemplatesFileData[templateAttributeResult].name !='TEXT'}} -->
				              	<textarea class="form-control" data-ng-class="{hidden: selectTemplatesFileData[templateAttributeResult].name !='TEXT'}" id="text{{templateAttributeResult}}" rows="2" ng-model="templateAttributeDescription[templateAttributeResult]"></textarea>
				          </td>
				       </tr>
				       <tr>
				       		<td class="form-group clearfix" colspan="4" ng-if="action=='add'"><button class="btn btn-primary pull-right"  ng-click="sbmtCreateTemplates()">Add Template</button></td>
				       		<td class="form-group clearfix" colspan="4" ng-if="action=='edit'"><button class="btn btn-primary pull-right"  ng-click="updateCreateTemplates(templateID)">Update Template</button></td>
				       </tr>
			       </tbody>
			</table>
	 </div>
          
          </div>
          
          
     
          </div>
     </div>
  </div>
</div>







       