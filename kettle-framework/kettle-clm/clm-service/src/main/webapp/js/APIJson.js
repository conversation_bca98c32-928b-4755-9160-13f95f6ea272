/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular.module('clmapp').factory('APIJson', APIJson);

    function APIJson() {
    	
    	var baseUrl = "http://dev.kettle.chaayos.com:9595/";
    	var scmBaseUrl = "http://dev.kettle.chaayos.com:9595/";
    	
    	 var clmBaseUrl 		= "http://localhost:8080/";
    	 var KETTLE_CLM_CONTEXT = clmBaseUrl+"clm-service/rest/v1/";
    	// var KETTLE_CLM_INTENT 	=  KETTLE_CLM_CONTEXT+"clm-metadata/intent/add";

        var KETTLE_CONTEXT = baseUrl+"kettle-service/rest/v1/";
        var MASTER_CONTEXT = baseUrl+"master-service/rest/v1/";
        var CUSTOMER_CONTEXT = baseUrl+"kettle-crm/rest/v1/";
        var SCM_SERVICE = scmBaseUrl + "scm-service/rest/v1/" ;
        var CHEKLIST_CONTEXT = baseUrl+"kettle-checklist/rest/v1/";
        
        var POS_METADATA_ROOT_CONTEXT = KETTLE_CONTEXT+"pos-metadata";
        var RECIPE_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT+"recipe";
        var USER_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT+"users";
        var UNIT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT+"unit-metadata";
        var CRM_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT+"crm";
        var PRODUCT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT+"product-metadata";
        var REPORT_METADATA_ROOT_CONTEXT = KETTLE_CONTEXT+"report-metadata";
        var CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT+"customer-profile";
        var OFFER_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT+"offer-management";
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT+"user-management";
        var CASH_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT+"cash-management";
        var SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "product-management/";
        var SCM_METADATA_ROOT_CONTEXT = SCM_SERVICE + "scm-metadata/";
        var SCM_UNIT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "unit-management/";
        var CHEKLIST_METADATA_CONTEXT = CHEKLIST_CONTEXT + "checklist-metadata/";
        var CHEKLIST_SERVICE_CONTEXT = CHEKLIST_CONTEXT + "checklist-service/";
        var CHEKLIST_MANAGEMENT_CONTEXT = CHEKLIST_CONTEXT + "checklist-management/";
        var CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT+"customer-offer-management";
        var ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT+"access-control-management/";
        var UNIT_METADATA_MANAGMENT_ROOT_CONTEXT   = KETTLE_CONTEXT + "metadata-management/";

        var service = {};
        
        service.urls = {
        	clmMetaData: {
                intentAdd:KETTLE_CLM_CONTEXT+"clm-metadata/intent/add",
                intentView:KETTLE_CLM_CONTEXT+"clm-metadata/intents",
                intentUpdate:KETTLE_CLM_CONTEXT+"clm-metadata/intent/update",
                campaignAdd:KETTLE_CLM_CONTEXT+"clm-metadata/campaign/add",
                campaignUpdate:KETTLE_CLM_CONTEXT+"clm-metadata/campaign/update",
                campaignView:KETTLE_CLM_CONTEXT+"clm-metadata/campaigns",
                templateAdd:KETTLE_CLM_CONTEXT+"clm-metadata/template/add",
                viewTemplate:KETTLE_CLM_CONTEXT+"clm-metadata/templates",
                addTemplate:KETTLE_CLM_CONTEXT+"clm-metadata/template/add",
                addAttributes:KETTLE_CLM_CONTEXT+"clm-metadata/templateAttribute/add",
                updateAttributes:KETTLE_CLM_CONTEXT+"clm-metadata/templateAttribute/update",
                updateTemplate:KETTLE_CLM_CONTEXT+"clm-metadata/template/update",
                userView:KETTLE_CLM_CONTEXT+"clm-metadata/test-Users",
                userTestAdd:KETTLE_CLM_CONTEXT+"clm-metadata/test-User/add",
                userTestUpdate:KETTLE_CLM_CONTEXT+"clm-metadata/test-User/update",
                
                
                
                sendSMS:KETTLE_CLM_CONTEXT+"clm-metadata/campaign/sendMessage"
                
              ///http://localhost:8080/clm-service/rest/v1/clm-metadata/campaign/sendMessage
               // templateServiceProvider:KETTLE_CLM_CONTEXT+"clm-metadata/service-Provider"
              
            },
            users:{
                login:USER_SERVICES_ROOT_CONTEXT+"/login",
                //adminLogin:USER_SERVICES_ROOT_CONTEXT+"/admin/login",
                changePassCode:USER_SERVICES_ROOT_CONTEXT+"/changePasscode",
                logout:USER_SERVICES_ROOT_CONTEXT+"/logout"
            },
            customer:{
            	signin:CUSTOMER_CONTEXT+CRM_SERVICES_ROOT_CONTEXT+"/signin"
            },
            unitMetaData: {
            	unit:UNIT_METADATA_ROOT_CONTEXT+"/unit-data",
                regions:UNIT_METADATA_ROOT_CONTEXT+"/regions",
                divisions:UNIT_METADATA_ROOT_CONTEXT+"/divisions",
                departments:UNIT_METADATA_ROOT_CONTEXT+"/departments",
                families:UNIT_METADATA_ROOT_CONTEXT+"/families",
                taxProfiles:UNIT_METADATA_ROOT_CONTEXT+"/tax-profiles",
                units:UNIT_METADATA_ROOT_CONTEXT+"/units",
                allUnits:UNIT_METADATA_ROOT_CONTEXT+"/all-units",
                addUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/add",
                updateUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/update",
                activateUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/activate",
                deactivateUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/deactivate",
                listTypes:UNIT_METADATA_ROOT_CONTEXT+"/listTypes",
                scmUnit:SCM_UNIT_MANAGEMENT_ROOT_CONTEXT+"unit",
                subCategories:UNIT_METADATA_ROOT_CONTEXT+"/sub-categories",
                viewUnitDeliveryPartners:UNIT_METADATA_MANAGMENT_ROOT_CONTEXT+"unit/delivery-partner/",
                addUpdateDeliveryPartners:UNIT_METADATA_MANAGMENT_ROOT_CONTEXT+"unit/delivery-partner/update"
            },
            productMetaData: {
                deactivateProductMapping:PRODUCT_METADATA_ROOT_CONTEXT+"/product/mapping/deactivate",
                activateProductMapping:PRODUCT_METADATA_ROOT_CONTEXT+"/product/mapping/activate",
                unitProductPriceUpdate:PRODUCT_METADATA_ROOT_CONTEXT+"/unit-product/price/update",
                productPriceUpdate:PRODUCT_METADATA_ROOT_CONTEXT+"/product/price/update",
                products:PRODUCT_METADATA_ROOT_CONTEXT+"/products",
                productsInfo:PRODUCT_METADATA_ROOT_CONTEXT+"/product-info",
                productPriceMapping:PRODUCT_METADATA_ROOT_CONTEXT+"/product/price/mappings",
                vendor:PRODUCT_METADATA_ROOT_CONTEXT+"/vendor",
                deactivateProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/deactivate",
                activateProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/activate",
                addProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/add",
                updateProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/update",
                classifications:PRODUCT_METADATA_ROOT_CONTEXT+"/classifications"
            },
            reportMetaData: {
                reportEnviroments:REPORT_METADATA_ROOT_CONTEXT+"/report/environments",
                adHocReports:REPORT_METADATA_ROOT_CONTEXT+"/report/adhoc-reports-list",
                reportCategories:REPORT_METADATA_ROOT_CONTEXT+"/report/categories",
                reportExecute:REPORT_METADATA_ROOT_CONTEXT+"/report/execute",
                wowReport:REPORT_METADATA_ROOT_CONTEXT+"/report/expense/execute/wow",
                momReport:REPORT_METADATA_ROOT_CONTEXT+"/report/expense/execute/mom"
            },
            customerProfile: {
                refer:CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT+"/refer"
            },
            recipeManagement:{
            	ingredientType: RECIPE_SERVICES_ROOT_CONTEXT+"/ingredient-type",
            	addRecipe : RECIPE_SERVICES_ROOT_CONTEXT+"/add",
            	updateRecipe : RECIPE_SERVICES_ROOT_CONTEXT+"/update",
            	findRecipe : RECIPE_SERVICES_ROOT_CONTEXT+"/find-recipe",
            	findRecipeByName : RECIPE_SERVICES_ROOT_CONTEXT+"/find-recipe/name",
            	removeRecipe : RECIPE_SERVICES_ROOT_CONTEXT+"/remove",
            	findAll: RECIPE_SERVICES_ROOT_CONTEXT+"/findAll",
               	downloadRecipe: RECIPE_SERVICES_ROOT_CONTEXT+"/get-recipe-dump",
            	downloadAllRecipe: RECIPE_SERVICES_ROOT_CONTEXT+"/get-all-recipe-dump"
            },
            offerManagement: {
                marketingPartner:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner",
                offerCategory:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offer-category",
                paymentModes:OFFER_MANAGEMENT_ROOT_CONTEXT+"/payment-modes",
                marketingPartnerDeactivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner/deactivate",
                marketingPartnerActivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner/activate",
                marketingPartnerAdd:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner/add",
                offerCoupons:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offer/coupons",
                offers:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offers",
                offersAdd:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offers/add",
                offersUpdate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offers/update",
                couponAvailablity:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/availablity",
                couponAdd:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/add",
                couponUpdate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/update",
                couponAuto:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/auto",
                couponMappingActivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/mapping/activate",
                couponMappingDeactivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/mapping/deactivate",
                couponSearch:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/search"
            },
            customerOfferManagement:{
                channelPartners:CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT+"/channel-partners"
            },
            userManagement: {
                managers:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/managers",
                users:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/users",
                user:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user",
                userAdd:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/add",
                userActivate:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/activate",
                userDeactivate:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/deactivate",
                userUnits:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/units",
                userUpdateMapping:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/update-mapping",
                userUpdate:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/update",
                userResetPasscode:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/reset-passcode"
            },
            scmProductManagement: {
                metadata: SCM_METADATA_ROOT_CONTEXT + "metadata",
                productDetail: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product",
                productDetails: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "products",
            },
            cashManagement:{
                pullSettlementsGet:CASH_MANAGEMENT_ROOT_CONTEXT+"/pullSettlements/get",
                pullSettlementsOpenGet:CASH_MANAGEMENT_ROOT_CONTEXT+"/pullSettlements/open/get",
                pullSettlementClose:CASH_MANAGEMENT_ROOT_CONTEXT+"/pullSettlement/close"

            },
            checkList:{
            	addCheckList:CHEKLIST_MANAGEMENT_CONTEXT+"checklist/add",
            	updateCheckList:CHEKLIST_MANAGEMENT_CONTEXT+"checklist/update",
            	activate:CHEKLIST_MANAGEMENT_CONTEXT+"checklist/activate",
            	listTypes:CHEKLIST_METADATA_CONTEXT+"listTypes",
            	categories:CHEKLIST_METADATA_CONTEXT+"categories",
            	frequency:CHEKLIST_METADATA_CONTEXT+"frequency",
            	station:CHEKLIST_METADATA_CONTEXT+"station",
            	allCheckLists:CHEKLIST_MANAGEMENT_CONTEXT+"checklists"
            },
            checklistitem:{
            	activate:CHEKLIST_MANAGEMENT_CONTEXT+"checklistitem/activate",
            	deactivate:CHEKLIST_MANAGEMENT_CONTEXT+"checklistitem/deactivate",
            },
            accessControlManagement: {
                getAccessControls: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "access-controls",
                activateAccessControls: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "activate-access-control",
                deactivateAccessControls: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "deactivate-access-control",
                applications: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "applications",
                preAuthenticatedApi: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "pre-authenticated-api",
                preAuthenticatedApis: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "pre-authenticated-apis",
                deactivatePreAuthenticatedApi: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "deactivate-pre-authenticated-api",
                activatePreAuthenticatedApi: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "activate-pre-authenticated-api"
            }
        };

        return service;
    }


})();